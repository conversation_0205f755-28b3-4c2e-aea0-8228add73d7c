package com.dspread.mdm.service.constants

import android.content.Context
import android.content.pm.PackageManager

class Constants {

    companion object {
        // Timber 配置
        const val TIMBER_LOG_TAG = "SmartMdmServiceApp"

        // WebSocket 配置
        const val WEBSOCKET_PROTOCOL_VERSION = "1"

        // 日志调试开关
        const val DEBUG_LOG_ENABLED_WS = true

        // ==================== 地理围栏全局变量 ====================
        // 地理围栏状态
        @Volatile
        var geofenceStatus = 1 // 默认IN_ZONE

        // 地理围栏配置
        @Volatile
        var geoLatitude = 0.0
        @Volatile
        var geoLongitude = 0.0
        @Volatile
        var geoRadius = 200f

        // 商店信息（用于WiFi和蓝牙辅助定位）
        @Volatile
        var storeId = ""
        @Volatile
        var storeSsid = ""
        @Volatile
        var storeIp = ""

        // 设备状态标志
        @Volatile
        var isUnboxRunning = false
        @Volatile
        var bUnboxResetFromGeo = false

        /**
         * Get current app version name (e.g. "1.0.3")
         */
        fun getVersionName(context: Context): String {
            return try {
                val pInfo = context.packageManager.getPackageInfo(context.packageName, 0)
                pInfo.versionName ?: "Unknown"
            } catch (e: PackageManager.NameNotFoundException) {
                "Unknown"
            }
        }

        /**
         * Get current app version code (for internal tracking)
         */
        fun getVersionCode(context: Context): Int {
            return try {
                val pInfo = context.packageManager.getPackageInfo(context.packageName, 0)
                @Suppress("DEPRECATION")
                pInfo.versionCode
            } catch (e: PackageManager.NameNotFoundException) {
                -1
            }
        }
    }

    /**
     * 四大模块相关常量
     */
    object ModuleConstants {
        // WiFi Profile 相关
        const val WIFI_PROFILE_CACHE_FILE = "wifi_profile_cache.json"
        const val WIFI_DELAY_EXECUTE_TIME = 24 * 60 * 60 * 1000L // 24小时延迟重试
        const val WIFI_CONNECT_TIMEOUT = 15000L // WiFi连接超时时间
        const val WIFI_PROFILE_CHECK_INTERVAL = 5 * 60 * 1000L // 5分钟检查间隔

        // APN 相关
        const val APN_TABLE_URI = "content://telephony/carriers"
        const val MAX_PING_ATTEMPTS = 5
        const val PING_INTERVAL = 2000L // ping间隔时间
        const val APN_NETWORK_TIMEOUT = 30000L // APN网络超时

        // Log Streaming 相关
        const val LOG_FILE_MAX_SIZE = 10 * 1024 * 1024L // 10MB
        const val LOG_TOTAL_MAX_SIZE = 3L * 1024 * 1024 * 1024 // 3GB
        const val LOG_UPLOAD_RETRY_COUNT = 3
        const val LOG_QUEUE_FILE = "log_queue.txt"
        const val LOG_GZ_QUEUE_FILE = "gz_queue.txt"

        // Geofencing 相关
        const val IN_ZONE = 1
        const val OUT_OF_ZONE = 2
        const val LOCK_SCREEN = 3
        const val WIPE_DATA = 4
        const val ROAMING = 5
        const val DEFAULT_GEOFENCE_RADIUS = 100f // 默认围栏半径（米）
        const val GPS_UPDATE_INTERVAL = 60000L // GPS更新间隔（毫秒）
        const val BLUETOOTH_SCAN_INTERVAL = 30000L // 蓝牙扫描间隔
        const val M_GEOFENCE_SET_SERVICE_LAUNCHER = true ////是否使用“设置service为主Launcher的锁屏方案


        // Provisioning 相关
        const val PROVISIONING_DEFAULT_INTERVAL = 12 * 60 * 60L // 12小时（秒）
        const val PROVISIONING_CONFIG_FILE = "provisioning_config.json"
        const val PROVISIONING_FLAGS_FILE = "provisioning_flags.json"
        const val PROVISIONING_RETRY_COUNT = 3
        const val PROVISIONING_RETRY_DELAY = 5000L // 5秒重试延迟

        // 系统资源路径 (使用应用私有目录)
        const val SYSTEM_LOGO_PATH = "/data/data/com.dspread.mdm.service/files/media/logo/logo.bin"
        const val BOOT_ANIMATION_PATH = "/data/data/com.dspread.mdm.service/files/media/anim/bootanimation.zip"

        // 配置存储路径 (优先使用系统目录，失败时回退到外部存储)
        const val PRIMARY_CONFIG_PATH = "/data/pos/config/"  // 主目录
        const val FALLBACK_CONFIG_PATH_BASE = "/sdcard/Android/data/com.dspread.mdm.service/files/"  // 回退目录基础路径
        const val FALLBACK_CONFIG_PATH = "/sdcard/Android/data/com.dspread.mdm.service/files/config/"  // 回退目录

        // 兼容性：保持原有常量，但会动态选择实际路径
        const val CONFIG_STORAGE_PATH = PRIMARY_CONFIG_PATH
    }

    /**
     * 服务相关常量
     */
    object ServiceConstants {
        // SmartMdmBackgroundService 相关
        const val SERVICE_TAG = "[SmartMdmBackgroundService] "
        const val NOTIFICATION_ID = 1001
        const val CHANNEL_ID = "smart_mdm_service_channel"
        const val CHANNEL_NAME = "Smart MDM Service"
        const val CHANNEL_DESCRIPTION = "Smart MDM 后台服务通知"

        // 服务通知相关
        const val NOTIFICATION_TITLE = "Smart MDM Service"
        const val NOTIFICATION_TEXT = "MDM 服务正在运行..."

        // WakeLock 相关
        const val WAKELOCK_INITIAL_TIMEOUT = 60000L // 60秒初始WakeLock超时

        // Provisioning 配置
        const val DEFAULT_PROVISIONING_CONFIG_URL = com.dspread.mdm.service.config.ProvisionConfig.DEFAULT_CONFIG_URL
        const val PROVISIONING_WAIT_TIMEOUT = 30000L // 30秒Provisioning等待超时
        const val PROVISIONING_WAIT_INTERVAL = 500L  // 500毫秒检查间隔
    }

    /**
     * WebSocket消息类型常量
     */
    object WsMessageTypes {
        // 四大模块消息类型
        const val WIFI_PROFILE_CONFIG = "SC007"    // WiFi配置消息（修正）
        const val LOG_STREAM_CONTROL = "SC009"     // 日志流控制消息（修正）
        const val APN_CONFIG = "SC010"             // APN配置消息（修正）
        const val GEOFENCE_CONFIG = "ST007"        // 地理围栏配置消息（修正为ST007）
    }

}
