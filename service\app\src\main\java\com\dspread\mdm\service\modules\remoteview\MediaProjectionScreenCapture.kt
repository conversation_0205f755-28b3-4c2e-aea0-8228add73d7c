package com.dspread.mdm.service.modules.remoteview

import android.content.Context
import android.graphics.Bitmap
import android.graphics.PixelFormat
import android.hardware.display.DisplayManager
import android.hardware.display.VirtualDisplay
import android.media.Image
import android.media.ImageReader
import android.media.projection.MediaProjection
import android.media.projection.MediaProjectionManager
import android.os.Handler
import android.os.Looper
import android.util.DisplayMetrics
import android.view.WindowManager
import com.dspread.mdm.service.utils.log.Logger
import com.dspread.mdm.service.services.MediaProjectionService
import java.io.ByteArrayOutputStream

/**
 * MediaProjection截屏实现
 * ScreenRecorderService实现
 */
class MediaProjectionScreenCapture(private val context: Context) {

    private var mediaProjectionManager: MediaProjectionManager? = null
    private var mediaProjection: MediaProjection? = null
    private var imageReader: ImageReader? = null
    private var virtualDisplay: VirtualDisplay? = null
    private var handler: Handler? = null

    private var width: Int = 0
    private var height: Int = 0
    private var dpi: Int = 0

    // 标志位：权限是否已获取
    private var isPermissionGranted: Boolean = false

    // 缓存上一帧数据，用于静止画面
    private var lastFrameData: ByteArray? = null

    /**
     * 初始化MediaProjection截屏
     */
    fun initialize(): Boolean {
        return try {
            // Logger.remote("初始化MediaProjection截屏") // 减少重复日志

            // 获取屏幕参数
            val windowManager = context.getSystemService(Context.WINDOW_SERVICE) as WindowManager
            val display = windowManager.defaultDisplay
            val displayMetrics = DisplayMetrics()
            display.getRealMetrics(displayMetrics)

            width = displayMetrics.widthPixels
            height = displayMetrics.heightPixels
            dpi = displayMetrics.densityDpi

            // Logger.remote("屏幕参数: ${width}x${height}, DPI: $dpi") // 减少重复日志

            // 获取MediaProjectionManager
            mediaProjectionManager = context.getSystemService(Context.MEDIA_PROJECTION_SERVICE) as? MediaProjectionManager

            if (mediaProjectionManager == null) {
                Logger.remoteE("无法获取MediaProjectionManager")
                return false
            }

            // 创建Handler
            handler = Handler(Looper.getMainLooper())

            // Logger.remote("MediaProjection截屏初始化成功") // 减少重复日志
            true

        } catch (e: Exception) {
            Logger.remoteE("MediaProjection截屏初始化失败", e)
            false
        }
    }

    /**
     * 创建MediaProjection实例
     */
    fun createMediaProjection(): MediaProjection? {
        return try {
            Logger.remote("尝试创建MediaProjection")

            // 尝试使用已有的授权数据
            val result1 = tryCreateMediaProjectionWithExistingData()
            if (result1 != null) return result1

            // 请求用户授权（通过RequestMediaProjectionActivity）
            val result2 = tryRequestMediaProjectionPermission()
            if (result2 != null) return result2

            Logger.remoteW("无法创建MediaProjection")
            null

        } catch (e: Exception) {
            Logger.remoteE("创建MediaProjection异常", e)
            null
        }
    }

    /**
     * 尝试使用已有的授权数据创建MediaProjection
     */
    private fun tryCreateMediaProjectionWithExistingData(): MediaProjection? {
        return try {
            Logger.remote("尝试使用已有授权数据创建MediaProjection")

            val data = RequestMediaProjectionActivity.getMediaProjectionData()
            if (data != null && mediaProjectionManager != null) {
                val mediaProjection = mediaProjectionManager!!.getMediaProjection(android.app.Activity.RESULT_OK, data)
                if (mediaProjection != null) {
                    Logger.remote("使用已有授权数据创建MediaProjection成功")
                    return mediaProjection
                }
            }

            Logger.remoteW("没有可用的授权数据")
            null

        } catch (e: Exception) {
            Logger.remoteW("使用已有授权数据失败: ${e.message}")
            null
        }
    }

    /**
     * 请求MediaProjection权限（必须通过前台服务）
     */
    private fun tryRequestMediaProjectionPermission(): MediaProjection? {
        return try {
            Logger.remote("请求MediaProjection权限（通过前台服务）")

            // 使用CountDownLatch实现同步
            val latch = java.util.concurrent.CountDownLatch(1)
            var result: MediaProjection? = null
            var error: Exception? = null

            RequestMediaProjectionActivity.requestMediaProjection(context) { data ->
                try {
                    if (data != null) {
                        Logger.remote("权限授权成功，启动前台服务")

                        // 启动前台服务（异步，不等待）
                        MediaProjectionService.startService(context, data)
                        Logger.remote("前台服务启动请求已发送")

                        // 设置权限已获取标志
                        isPermissionGranted = true

                        // 返回一个占位符，表示权限已获取
                        result = mediaProjection // 可能为null，但权限已获取
                        Logger.remote("权限获取成功，将在截屏时创建MediaProjection")

                    } else {
                        error = Exception("权限请求失败或被拒绝")
                    }
                } catch (e: Exception) {
                    error = e
                } finally {
                    latch.countDown()
                }
            }

            // 等待权限请求完成（最多等待15秒）
            val completed = latch.await(15, java.util.concurrent.TimeUnit.SECONDS)

            if (!completed) {
                Logger.remoteE("权限请求超时（15秒）")
                return null
            }

            if (error != null) {
                Logger.remoteE("权限请求失败", error)
                return null
            }

            result

        } catch (e: Exception) {
            Logger.remoteE("请求MediaProjection权限异常", e)
            null
        }
    }

    /**
     * 使用MediaProjection进行截屏（优化性能）
     */
    fun captureScreen(): ByteArray? {
        return try {
            // 检查是否需要初始化
            if (mediaProjection == null || virtualDisplay == null) {
                // Logger.remote("初始化MediaProjection和VirtualDisplay") // 减少重复日志

                // 清理旧资源
                cleanup()

                // 优先从Service获取已有的MediaProjection
                mediaProjection = MediaProjectionService.getMediaProjection()
                if (mediaProjection == null) {
                    Logger.remote("Service中无MediaProjection，请求新权限")
                    RequestMediaProjectionActivity.clearMediaProjectionData()
                    mediaProjection = createMediaProjection()
                    if (mediaProjection == null) {
                        Logger.remoteE("MediaProjection创建失败")
                        return null
                    }
                } else {
                    Logger.remote("使用Service中的MediaProjection")
                }

                // 只在初始化时注册回调和创建VirtualDisplay
                // 注册回调（Android 14+要求）
                mediaProjection?.registerCallback(object : MediaProjection.Callback() {
                    override fun onStop() {
                        Logger.remote("MediaProjection已停止")
                        // 简单清理，不递归调用cleanup()
                        virtualDisplay?.release()
                        virtualDisplay = null
                        imageReader?.close()
                        imageReader = null
                        mediaProjection = null
                    }
                }, handler)

                // 创建ImageReader和VirtualDisplay
                imageReader = ImageReader.newInstance(width, height, PixelFormat.RGBA_8888, 2)

                virtualDisplay = mediaProjection?.createVirtualDisplay(
                    "ScreenCapture",
                    width, height, dpi,
                    DisplayManager.VIRTUAL_DISPLAY_FLAG_AUTO_MIRROR,
                    imageReader?.surface,
                    null, handler
                )

                if (virtualDisplay == null) {
                    Logger.remoteE("VirtualDisplay创建失败")
                    return null
                }

                Logger.remote("MediaProjection和VirtualDisplay创建成功")

                // 等待VirtualDisplay准备就绪
                Thread.sleep(200)
            } else {
                // Logger.remote("复用已有的MediaProjection和VirtualDisplay") // 减少重复日志
            }

            // 获取图像（优化缓冲区处理）
            var image: Image? = null
            var retryCount = 0

            // 简化重试逻辑：最多3次尝试，失败就跳过
            while (image == null && retryCount < 3) {
                image = imageReader?.acquireLatestImage()
                if (image == null) {
                    Thread.sleep(30) // 短暂等待
                    retryCount++
                }
            }

            if (image == null) {
                // 静止画面优化：返回上一帧数据
                if (lastFrameData != null) {
                    Logger.remote("画面静止，重用上一帧数据 (${lastFrameData!!.size} bytes)")
                    return lastFrameData
                } else {
                    Logger.remoteW("无法获取图像且无缓存，跳过此帧")
                    return null
                }
            }

            return processImage(image)

        } catch (e: Exception) {
            Logger.remoteE("MediaProjection截屏失败", e)
            return null
        }
    }

    /**
     * 处理图像数据
     */
    private fun processImage(image: Image): ByteArray? {
        return try {
            // 转换为Bitmap
            val bitmap = imageToBitmap(image)
            image.close()

            if (bitmap == null) {
                Logger.remoteE("无法转换为Bitmap")
                return null
            }

            // 转换为字节数组（使用JPEG压缩以减少数据量）
            val outputStream = ByteArrayOutputStream()
            bitmap.compress(Bitmap.CompressFormat.JPEG, 80, outputStream) // 80%质量，平衡大小和质量
            val bytes = outputStream.toByteArray()

            bitmap.recycle()
            outputStream.close()

            // 缓存成功的帧数据
            lastFrameData = bytes

            // Logger.remote("MediaProjection截屏成功，大小: ${bytes.size} bytes") // 减少重复日志
            bytes

        } catch (e: Exception) {
            Logger.remoteE("处理图像失败", e)
            null
        }
    }

    /**
     * 将Image转换为Bitmap
     */
    private fun imageToBitmap(image: Image): Bitmap? {
        return try {
            val planes = image.planes
            val buffer = planes[0].buffer
            val pixelStride = planes[0].pixelStride
            val rowStride = planes[0].rowStride
            val rowPadding = rowStride - pixelStride * width

            val bitmap = Bitmap.createBitmap(
                width + rowPadding / pixelStride,
                height,
                Bitmap.Config.ARGB_8888
            )

            bitmap.copyPixelsFromBuffer(buffer)
            bitmap

        } catch (e: Exception) {
            Logger.remoteE("Image转Bitmap失败", e)
            null
        }
    }

    /**
     * 清理资源（每次截屏后清理以确保稳定性）
     */
    private fun cleanup() {
        try {
            virtualDisplay?.release()
            virtualDisplay = null

            imageReader?.close()
            imageReader = null

            // 保留mediaProjection，下次截屏时复用
        } catch (e: Exception) {
            Logger.remoteE("清理资源失败", e)
        }
    }

    /**
     * 释放所有资源
     */
    fun release() {
        try {
            // 完全清理所有资源
            virtualDisplay?.release()
            virtualDisplay = null

            imageReader?.close()
            imageReader = null

            mediaProjection?.stop()
            mediaProjection = null

            mediaProjectionManager = null

            // 重置权限标志位
            isPermissionGranted = false

            // 清理缓存的帧数据
            lastFrameData = null

            Logger.remote("MediaProjection资源释放完成，权限标志已重置")

        } catch (e: Exception) {
            Logger.remoteE("释放MediaProjection资源失败", e)
        }
    }
}
