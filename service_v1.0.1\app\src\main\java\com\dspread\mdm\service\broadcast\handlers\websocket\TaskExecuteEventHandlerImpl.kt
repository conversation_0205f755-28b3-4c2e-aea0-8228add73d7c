package com.dspread.mdm.service.broadcast.handlers.websocket

import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import com.dspread.mdm.service.services.AppInstallService
import com.dspread.mdm.service.services.AppUninstallService
import com.dspread.mdm.service.network.websocket.WebSocketCenter
import com.dspread.mdm.service.platform.api.network.NetworkApi
import com.dspread.mdm.service.network.websocket.message.WsMessageSender
import com.dspread.mdm.service.network.websocket.task.WsTaskManager
import com.dspread.mdm.service.broadcast.core.BroadcastEventHandler
import com.dspread.mdm.service.utils.log.Logger
import com.dspread.mdm.service.broadcast.core.BroadcastSender
import com.dspread.mdm.service.constants.TaskStateConstants
import com.dspread.mdm.service.config.TimerConfig
import com.dspread.mdm.service.broadcast.core.BroadcastActions


/**
 * 任务执行事件处理器
 */
class TaskExecuteEventHandlerImpl : BroadcastEventHandler {
    
    companion object {
        private const val TAG = "TaskExecuteEventHandler"
    }
    
    override fun getHandlerName(): String = TAG
    
    override fun getSupportedActions(): List<String> {
        return listOf(BroadcastActions.ACTION_WSTASK_EXEC_BC)
    }
    
    override fun handleBroadcast(context: Context, intent: Intent): Boolean {
        val action = intent.action ?: return false

        return try {
            when (action) {
                BroadcastActions.ACTION_WSTASK_EXEC_BC -> {
                    handleTaskExecute(context)
                    true
                }
                else -> false
            }
        } catch (e: Exception) {
            Logger.receiverE("处理广播失败: $action", e)
            false
        }
    }
    
    /**
     * 处理任务执行
     */
    private fun handleTaskExecute(context: Context) {
        try {
            // 安排下一次任务执行（保持定时器持续运行）
            scheduleNextTaskExecution(context)

            // 检查网络状态
            if (!NetworkApi.isNetworkAvailable(context)) {
                return
            }

            // 检查WebSocket连接
            if (!WebSocketCenter.isConnected()) {
                return
            }
            
            // 获取下一个待执行任务
            val todoTask = WsTaskManager.getNextTodoWSTask()

            if (todoTask == null) {
                // 没有任务时不再打印日志，减少冗余信息
                return
            }

            val taskId = todoTask.optString("taskId", "")
            val taskType = todoTask.optString("taskType", "")
            val pkgName = todoTask.optString("pkgName", "")

            // 输出执行任务日志
            Logger.receiver("执行任务: $taskType/$taskId")
            
            // 检查是否是自身更新任务
            if (pkgName == context.packageName && "01" == taskType) {
                Logger.receiver("检测到自身更新任务，检查版本状态")
                handleSelfUpdateTask(context, todoTask)
            } else {
                Logger.receiver("其他类型任务，交由TaskHandler处理")
                // 其他任务交由现有的TaskHandler处理
                handleGeneralTask(context, todoTask)
            }
            
        } catch (e: Exception) {
            Logger.receiverE("处理任务执行失败", e)
        }
    }
    
    /**
     * 处理自身更新任务
     */
    private fun handleSelfUpdateTask(context: Context, task: org.json.JSONObject) {
        try {
            val taskId = task.optString("taskId", "")
            val apkName = task.optString("apkName", "")
            
            Logger.receiver("处理自身更新任务: taskId=$taskId, apkName=$apkName")
            
            // 检查版本是否匹配
            val versionCheckResult = checkSelfUpdateVersion(task)
            
            if (versionCheckResult == 1 || versionCheckResult == 2) {
                // 版本匹配，直接上报安装成功
                Logger.receiver("Local $apkName is Latest")
                
                // 更新任务状态并上报
                WsTaskManager.updateWSTaskState(taskId, "B03") // B03对应INSTALL_SUCCESS
                WsMessageSender.uploadTaskResult(taskId, "B03", null)
                
                // 等待一秒，然后发送下一个任务执行广播
                Thread.sleep(1000)
                Logger.receiver("UPDATE_APK Local Service is latest, send WSTASK_EXEC_BC")
                sendTaskExecuteBroadcast(context)
                
            } else {
                Logger.receiver("自身版本不匹配，任务继续等待执行")
                // 版本不匹配，任务继续等待，交由TaskHandler处理实际的安装逻辑
            }
            
        } catch (e: Exception) {
            Logger.receiverE("处理自身更新任务失败", e)
        }
    }
    
    /**
     * 检查自身更新版本
     */
    private fun checkSelfUpdateVersion(task: org.json.JSONObject): Int {
        return try {
            // 这里应该实现具体的版本比较逻辑
            // 返回值：1=版本相同，2=本地版本更高，0=需要更新
            
            // 暂时返回1，表示版本匹配（更新成功）
            // TODO: 实现具体的版本比较逻辑
            1
            
        } catch (e: Exception) {
            Logger.receiverE("版本检查失败", e)
            0
        }
    }
    
    /**
     * 处理一般任务（非自身更新任务）
     */
    private fun handleGeneralTask(context: Context, task: org.json.JSONObject) {
        try {
            val taskId = task.optString("taskId", "")
            val taskType = task.optString("taskType", "")
            val pkgName = task.optString("packName", "")
            val apkName = task.optString("apkName", "")

            Logger.receiver("处理一般任务: taskId=$taskId, taskType=$taskType, pkgName=$pkgName, apkName=$apkName")

            // 根据任务类型分发处理
            when (taskType) {
                "01" -> {
                    // 应用安装任务 - 先检查是否已安装
                    Logger.receiver("处理应用安装任务: $apkName")

                    // 先检查应用是否已安装
                    if (isAppAlreadyInstalled(context, task)) {
                        Logger.receiver("应用已安装且版本匹配，直接上报成功: $pkgName")
                        WsTaskManager.updateWSTaskState(taskId, TaskStateConstants.INSTALL_SUCCESS)
                        WsMessageSender.uploadTaskResult(taskId, TaskStateConstants.INSTALL_SUCCESS, null)

                        // 发送下一个任务执行广播
                        Thread.sleep(1000)
                        sendTaskExecuteBroadcast(context)
                        return
                    }

                    handleAppInstallTask(context, task)
                }
                "02" -> {
                    // 应用卸载任务
                    Logger.receiver("处理应用卸载任务: $apkName")
                    handleAppUninstallTask(context, task)
                }
                "OSUPDATE" -> {
                    // OS升级任务
                    Logger.receiver("处理OS升级任务")
                    handleOSUpdateTask(context, task)
                }
                else -> {
                    Logger.receiver("未知任务类型: $taskType")
                    // 更新任务状态为失败
                    WsTaskManager.updateWSTaskState(taskId, "FAILED")
                    WsMessageSender.uploadTaskResult(taskId, "FAILED", "未知任务类型: $taskType")
                }
            }

        } catch (e: Exception) {
            Logger.receiverE("处理一般任务失败", e)
        }
    }

    /**
     * 处理应用安装任务
     */
    private fun handleAppInstallTask(context: Context, task: org.json.JSONObject) {
        try {
            val taskId = task.optString("taskId", "")
            val packageName = task.optString("packName", "")
            val apkName = task.optString("apkName", "")
            val versionName = task.optString("versionName", "")
            val versionCode = task.optString("versionCode", "")
            val downloadUrl = task.optString("url", "")
            val apkMd5 = task.optString("apkMd5", "")

            Logger.receiver("开始安装应用: $apkName")
            Logger.receiver("包名: $packageName, 版本: $versionName($versionCode)")
            Logger.receiver("下载地址: $downloadUrl")

            // 更新任务状态为执行中
            WsTaskManager.updateWSTaskState(taskId, TaskStateConstants.INSTALL_ING)
            WsMessageSender.uploadTaskResult(taskId, TaskStateConstants.INSTALL_ING, null)

            // 使用AppInstallService执行安装
            AppInstallService.startInstall(
                context = context,
                taskId = taskId,
                packageName = packageName,
                apkName = apkName,
                downloadUrl = downloadUrl,
                apkMd5 = apkMd5,
                versionName = versionName,
                versionCode = versionCode.toIntOrNull() ?: 0
            )

        } catch (e: Exception) {
            Logger.receiverE("处理应用安装任务失败", e)
            val taskId = task.optString("taskId", "")
            WsTaskManager.updateWSTaskState(taskId, "INSTALL_FAILED")
            WsMessageSender.uploadTaskResult(taskId, "INSTALL_FAILED", e.message)
        }
    }

    /**
     * 处理应用卸载任务
     */
    private fun handleAppUninstallTask(context: Context, task: org.json.JSONObject) {
        try {
            val taskId = task.optString("taskId", "")
            val packageName = task.optString("packName", "")
            val apkName = task.optString("apkName", "")

            Logger.receiver("开始卸载应用: $apkName ($packageName)")

            // 更新任务状态为执行中
            WsTaskManager.updateWSTaskState(taskId, "UNINSTALL_ING")
            WsMessageSender.uploadTaskResult(taskId, "UNINSTALL_ING", null)

            // 使用AppUninstallService执行卸载
            AppUninstallService.startUninstall(
                context = context,
                taskId = taskId,
                packageName = packageName,
                apkName = apkName
            )

        } catch (e: Exception) {
            Logger.receiverE("处理应用卸载任务失败", e)
            val taskId = task.optString("taskId", "")
            WsTaskManager.updateWSTaskState(taskId, "UNINSTALL_FAILED")
            WsMessageSender.uploadTaskResult(taskId, "UNINSTALL_FAILED", e.message)
        }
    }

    /**
     * 处理OS升级任务
     */
    private fun handleOSUpdateTask(context: Context, task: org.json.JSONObject) {
        try {
            val taskId = task.optString("taskId", "")
            Logger.receiver("处理OS升级任务: $taskId")

            // TODO: 实现OS升级逻辑
            Logger.receiver("OS升级功能待实现")

            // 暂时标记为失败
            WsTaskManager.updateWSTaskState(taskId, "FAILED")
            WsMessageSender.uploadTaskResult(taskId, "FAILED", "OS升级功能待实现")

        } catch (e: Exception) {
            Logger.receiverE("处理OS升级任务失败", e)
            val taskId = task.optString("taskId", "")
            WsTaskManager.updateWSTaskState(taskId, "FAILED")
            WsMessageSender.uploadTaskResult(taskId, "FAILED", e.message)
        }
    }

    /**
     * 检查应用是否已安装且版本匹配
     */
    private fun isAppAlreadyInstalled(context: Context, task: org.json.JSONObject): Boolean {
        return try {
            val packageName = task.optString("packName", "")
            val versionName = task.optString("versionName", "")
            val versionCode = task.optString("versionCode", "0").toIntOrNull() ?: 0

            if (packageName.isEmpty()) {
                Logger.receiver("包名为空，无法检查安装状态")
                return false
            }

            val packageManager = context.packageManager
            val packageInfo = packageManager.getPackageInfo(packageName, 0)

            // 检查版本匹配
            val installedVersionName = packageInfo.versionName ?: ""
            val installedVersionCode = packageInfo.versionCode

            Logger.receiver("版本比较: 任务版本=$versionName($versionCode), 已安装版本=$installedVersionName($installedVersionCode)")

            // 版本匹配条件：版本名相同且版本号相同或更高
            val versionMatched = (versionName == installedVersionName && versionCode <= installedVersionCode)

            Logger.receiver("应用安装检查结果: $packageName, 版本匹配=$versionMatched")
            return versionMatched

        } catch (e: PackageManager.NameNotFoundException) {
            Logger.receiver("应用未安装: ${task.optString("packName", "")}")
            false
        } catch (e: Exception) {
            Logger.receiverE("检查应用安装状态失败", e)
            false
        }
    }

    /**
     * 发送任务执行广播
     */
    private fun sendTaskExecuteBroadcast(context: Context) {
        try {
            BroadcastSender.sendBroadcast(context, BroadcastActions.ACTION_WSTASK_EXEC_BC)
            Logger.receiver("发送任务执行广播")
        } catch (e: Exception) {
            Logger.receiverE("发送任务执行广播失败", e)
        }
    }

    /**
     * 安排下一次任务执行（支持调试模式和生产模式）
     */
    fun scheduleNextTaskExecution(context: Context) {
        try {
            val interval = TimerConfig.getTaskExecutionInterval()

            val intent = Intent(BroadcastActions.ACTION_WSTASK_EXEC_BC).apply {
                setPackage(context.packageName)
            }
            val pendingIntent = android.app.PendingIntent.getBroadcast(
                context,
                1001, // 任务执行定时器专用requestCode
                intent,
                android.app.PendingIntent.FLAG_UPDATE_CURRENT or android.app.PendingIntent.FLAG_IMMUTABLE
            )

            val alarmManager = context.getSystemService(Context.ALARM_SERVICE) as android.app.AlarmManager
            val triggerTime = System.currentTimeMillis() + (interval * 1000)

            // 取消之前的定时器
            alarmManager.cancel(pendingIntent)

            // 使用精确的闹钟
            alarmManager.setExact(
                android.app.AlarmManager.RTC_WAKEUP,
                triggerTime,
                pendingIntent
            )

            val taskExecutionInterval = TimerConfig.getTaskExecutionInterval()
            val minutes = taskExecutionInterval / 60
            val timeDesc = if (minutes >= 1) "${minutes}分钟" else "${taskExecutionInterval}秒"
            Logger.success("⏰ 设置任务执行定时器成功，下次执行: ${taskExecutionInterval}秒后 ($timeDesc)")

        } catch (e: Exception) {
            Logger.receiverE("安排下一次任务执行失败", e)
        }
    }
}
