<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/ll_background"
    android:layout_width="match_parent"
    android:layout_height="72dp"
    android:background="@color/white_smoke">

    <LinearLayout
        android:layout_width="72dp"
        android:layout_height="match_parent"
        android:gravity="center"
        android:background="@color/white_smoke">
        <ImageView
            android:id="@+id/iv_icon"
            android:layout_width="62dp"
            android:layout_height="62dp"
            android:background="@drawable/ic_launcher"/>
    </LinearLayout>
    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="1"
        android:padding="5dp"
        android:gravity="center_vertical"
        android:orientation="vertical">
        <TextView
            android:id="@+id/tv_apkname"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:text="English"
            android:textSize="16sp"
            android:textColor="@color/title" />

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal">
            <TextView
                android:id="@+id/tv_versionname_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="3dp"
                android:gravity="center"
                android:text="VersionName: "
                android:textSize="12sp"
                android:textColor="@color/black" />
            <TextView
                android:id="@+id/tv_versionname"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="3dp"
                android:gravity="center"
                android:text="English"
                android:textSize="12sp"
                android:textColor="@color/subtitle" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal">
            <TextView
                android:id="@+id/tv_versioncode_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="3dp"
                android:gravity="center"
                android:text="VersionCode: "
                android:textSize="12sp"
                android:textColor="@color/black" />
            <TextView
                android:id="@+id/tv_versioncode"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="3dp"
                android:gravity="center"
                android:text="English"
                android:textSize="12sp"
                android:textColor="@color/subtitle" />
        </LinearLayout>
    </LinearLayout>
</LinearLayout>
