package com.bbpos.wiseapp.settings.utils;

import android.content.Context;
import android.net.wifi.WifiConfiguration;
import android.net.wifi.WifiManager;
import android.text.TextUtils;

import com.bbpos.wiseapp.logger.BBLog;

import org.json.JSONArray;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;

public class ProxyHandler {

    /**
     * 設置wifi代理信息
     * @param jsonObject
     */
    public static void handlWifiProxy(Context mContext,JSONObject jsonObject){
        if(jsonObject == null) return;
        if (jsonObject.has("data")){

            JSONObject proxyJson = jsonObject.optJSONObject("data").optJSONObject("wifi_proxy");
            BBLog.d(BBLog.TAG, "存在wifi_proxy");
//            BBLog.d(BBLog.TAG, "proxyjson: "+proxyJson.toString());
            if (proxyJson!=null){
                JSONObject hostJson = proxyJson.optJSONObject("host");
                JSONObject staticIpJson = proxyJson.optJSONObject("staticIP");

                WifiManager wifiManager = WifiProxyUtil.getWifiManager(mContext);
                WifiConfiguration config  = WifiProxyUtil.getCurrentWifiConfiguration(wifiManager);
                boolean isNeedSetProxy = false;
                boolean isNeedSetStaticIp = false;
                if (hostJson!=null){
                    BBLog.d(BBLog.TAG, "handlWifiProxy: host: "+getStringFromJson(hostJson,"host")+", port: "+Integer.valueOf(getStringFromJson(hostJson,"port")) + ", bypass: "+ getJsonStr2List(hostJson,"bypass"));
                    isNeedSetProxy = true;
                    WifiProxyUtil.setWifiProxySettingsFor17And(mContext,
                            config,
                            getStringFromJson(hostJson,"host"),
                            Integer.valueOf(getStringFromJson(hostJson,"port")),
                            getJsonStr2List(hostJson,"bypass"),
                            false);
                }
                //    public static boolean setWifiStaticIp(Context context,WifiConfiguration wifiConfig, boolean dhcp, String ip, int prefix, String dns1, String dns2, String gateway){
                if (staticIpJson!=null){
                    BBLog.d(BBLog.TAG, "handlWifiProxy: isdhcp: "+ "1".equals(getStringFromJson(staticIpJson,"isdhcp"))//true:dhcp ,false:staticIP
                            +", ip: "+getStringFromJson(staticIpJson,"ip")
                            + ", prefix: "+ Integer.valueOf(getStringFromJson(staticIpJson,"prefix"))
                            + ", dns1: "+getStringFromJson(staticIpJson,"dns1")
                            + ", dns2: "+getStringFromJson(staticIpJson,"dns2")
                            + ", gateway: "+getStringFromJson(staticIpJson,"gateway")
                    );

                    isNeedSetStaticIp = true;
                    WifiProxyUtil.setWifiStaticIp(mContext,
                            config,
                            "1".equals(getStringFromJson(staticIpJson,"isdhcp")),
                            getStringFromJson(staticIpJson,"ip"),
                            Integer.valueOf(getStringFromJson(staticIpJson,"prefix")),
                            getStringFromJson(staticIpJson,"dns1"),
                            getStringFromJson(staticIpJson,"dns2"),
                            getStringFromJson(staticIpJson,"gateway"),
                            false);
                }

//                if (isNeedSetProxy || isNeedSetStaticIp)
                    WifiProxyUtil.refreshWifiConfig(wifiManager,config);
            }
        }
    }

    private static String getStringFromJson(JSONObject jsonObject, String  key){
        if (jsonObject == null || TextUtils.isEmpty(key)) return "";
        if (jsonObject.has(key))
            return jsonObject.optString(key);
        return "";
    }

    private static List<String> getJsonStr2List(JSONObject jsonObject, String key){
        List<String> bypass = new ArrayList<>();
        try {
            if (jsonObject == null || TextUtils.isEmpty(key)) return bypass;
            if (jsonObject.has("bypass")){
                String bypassStr = jsonObject.optString("bypass");
                BBLog.d(BBLog.TAG, "getJsonStr2List: bypass"+bypassStr);
                JSONArray array = new JSONArray(bypassStr.replace(" ", ""));
                for(int i = 0; i < array.length(); i++) {
                    bypass.add((String) array.get(i));
                }
                return bypass;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return bypass;
    }
}
