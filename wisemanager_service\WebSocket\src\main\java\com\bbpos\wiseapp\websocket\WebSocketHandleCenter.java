package com.bbpos.wiseapp.websocket;

import android.content.Context;
import android.util.Log;

import com.bbpos.BaseWebSocketHandlerCenter;
import com.bbpos.wiseapp.EncrypUtil;

/**
 *  Java-WebSocket
 *
 * 已经绑定了 WebSocketService 服务的 Activity，
 */
public class WebSocket<PERSON>andleCenter extends BaseWebSocketHandlerCenter {
    private WebSocketServiceConnectManager mConnectManager;

    public WebSocketHandleCenter(Context context) {
        super(context);
        mConnectManager = new WebSocketServiceConnectManager(context, this);
        mConnectManager.onCreate();
    }

    @Override
    public void sendText(String text) {
        Log.e(TAG, "WebSocket: WebSocketHandleCenter  数据发送：" + text);
        try {
            if (m_need_rsa) {
                mConnectManager.sendText(EncrypUtil.encode(EncrypUtil.encryptByPublicKey(text.getBytes(), m_server_public_key)));
            } else {
                mConnectManager.sendText(text);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void reconnect() {
        mConnectManager.reconnect();
    }

    @Override
    public void onMessageResponse(Response message) {
        super.onMessageResponse(message);
        String respone = "";
        try {
            if (m_need_rsa) {
                respone = new String(EncrypUtil.decryptByPrivateKey(EncrypUtil.decode(message.getResponseText()), m_private_key));
            } else {
                respone = message.getResponseText();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        Log.e(TAG, "onMessageResponse: " + respone);

        if (mListener!=null) {
            mListener.onMessageRecv(respone);
        }
    }
}
