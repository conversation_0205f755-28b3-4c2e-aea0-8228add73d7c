package com.dspread.mdm.service.broadcast.handlers.service

import android.app.AlarmManager
import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.os.PowerManager
import com.dspread.mdm.service.broadcast.core.BroadcastActions
import com.dspread.mdm.service.broadcast.core.BroadcastEventHandler
import com.dspread.mdm.service.services.ServiceKeepAliveService
import com.dspread.mdm.service.utils.log.Logger
import kotlinx.coroutines.*
import com.dspread.mdm.service.config.TimerConfig

/**
 * 服务守护事件处理器
 * 负责定时保活机制，确保自更新时服务能正常重启
 *
 * 保活策略：
 * 1. 定期启动ServiceKeepAliveService保持进程活跃
 * 2. 使用智能WakeLock管理，避免长期持有
 * 3. 确保PackageUpdateReceiver能正常接收自更新广播
 */
class ServiceGuardEventHandler : BroadcastEventHandler {

    companion object {
        private const val TAG = "ServiceGuardEventHandler"
        
        // 服务守护间隔（120秒）
        private const val SERVICE_GUARD_INTERVAL = 120L // 120秒，单位：秒

        // 防重复执行标志
        @Volatile
        private var isExecuting = false

        // 上次执行时间，防止过于频繁执行
        @Volatile
        private var lastExecuteTime = 0L

        // 最小执行间隔（30秒）
        private const val MIN_EXECUTE_INTERVAL = 30 * 1000L

        // 定时器专用协程作用域
        private val timerScope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    }

    override fun getHandlerName(): String = TAG

    override fun getSupportedActions(): List<String> {
        return listOf(BroadcastActions.ACTION_SERVICE_GUARD_TIMER)
    }

    override fun handleBroadcast(context: Context, intent: Intent): Boolean {
        val action = intent.action ?: return false

        return try {
            when (action) {
                BroadcastActions.ACTION_SERVICE_GUARD_TIMER -> {
                    handleServiceGuard(context)
                    true
                }
                else -> false
            }
        } catch (e: Exception) {
            Logger.receiverE("$TAG 处理广播失败: $action", e)
            false
        }
    }

    /**
     * 处理服务守护
     */
    private fun handleServiceGuard(context: Context) {
        // 将耗时操作移到后台线程执行
        timerScope.launch {
            try {
                // 防重复执行检查
                val currentTime = System.currentTimeMillis()
                if (isExecuting) {
                    Logger.comW("$TAG 上次执行尚未完成，跳过本次执行")
                    return@launch
                }

                if (currentTime - lastExecuteTime < MIN_EXECUTE_INTERVAL) {
                    Logger.comW("$TAG 执行间隔过短，跳过本次执行")
                    return@launch
                }

                isExecuting = true
                lastExecuteTime = currentTime

                Logger.com("$TAG 开始执行保活动作")
                
                // 安排下一次服务守护（保持定时器持续运行）
                scheduleNextServiceGuard(context)
                
                // 执行保活动作
                performKeepAliveActions(context)

            } catch (e: Exception) {
                Logger.comE("$TAG 处理服务守护定时器失败", e)
            } finally {
                isExecuting = false
            }
        }
    }

    /**
     * 启动服务守护定时器
     */
    fun startServiceGuardTimer(context: Context) {
        try {
            val interval = TimerConfig.getServiceGuardInterval()
            Logger.receiver("$TAG 启动服务守护定时器: ${interval}s")

            // 直接安排第一次服务守护检查，不需要立即发送广播
            scheduleNextServiceGuard(context)

        } catch (e: Exception) {
            Logger.receiverE("$TAG 启动服务守护定时器失败", e)
        }
    }

    /**
     * 停止服务守护定时器
     */
    fun stopServiceGuardTimer(context: Context) {
        try {
            val intent = Intent(BroadcastActions.ACTION_SERVICE_GUARD_TIMER).apply {
                setPackage(context.packageName)
            }
            val pendingIntent = PendingIntent.getBroadcast(
                context,
                0,
                intent,
                PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
            )

            val alarmManager = context.getSystemService(Context.ALARM_SERVICE) as AlarmManager
            alarmManager.cancel(pendingIntent)

            Logger.receiver("$TAG 服务守护定时器已停止")

        } catch (e: Exception) {
            Logger.receiverE("$TAG 停止服务守护定时器失败", e)
        }
    }

    /**
     * 安排下一次服务守护
     */
    private fun scheduleNextServiceGuard(context: Context) {
        try {
            val intent = Intent(BroadcastActions.ACTION_SERVICE_GUARD_TIMER).apply {
                setPackage(context.packageName)
            }
            val pendingIntent = PendingIntent.getBroadcast(
                context,
                0,
                intent,
                PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
            )

            val alarmManager = context.getSystemService(Context.ALARM_SERVICE) as AlarmManager
            val interval = TimerConfig.getServiceGuardInterval()
            val triggerTime = System.currentTimeMillis() + (interval * 1000)

            // 取消之前的定时器
            alarmManager.cancel(pendingIntent)

            // 使用精确的闹钟
            alarmManager.setExact(
                AlarmManager.RTC_WAKEUP,
                triggerTime,
                pendingIntent
            )

            val minutes = interval / 60
            val timeDesc = if (minutes >= 1) "${minutes}分钟" else "${interval}秒"
            Logger.success("⏰ 设置服务守护定时器成功，下次执行: ${interval}秒后 ($timeDesc)")

        } catch (e: Exception) {
            Logger.receiverE("$TAG 安排下一次服务守护失败", e)
        }
    }

    /**
     * 执行保活动作
     */
    private fun performKeepAliveActions(context: Context) {
        try {
            // 1. 启动保活服务
            startKeepAliveService(context)

            // 2. 智能WakeLock管理
            manageWakeLock(context)

            Logger.com("$TAG 保活动作执行完成")

        } catch (e: Exception) {
            Logger.comE("$TAG 执行保活动作失败", e)
        }
    }

    /**
     * 启动保活服务
     */
    private fun startKeepAliveService(context: Context) {
        try {
            val intent = Intent(context, ServiceKeepAliveService::class.java)
            context.startService(intent)
            Logger.com("$TAG 启动保活服务")

        } catch (e: Exception) {
            Logger.comE("$TAG 启动保活服务失败", e)
        }
    }

    /**
     * 智能WakeLock管理
     */
    private fun manageWakeLock(context: Context) {
        try {
            val powerManager = context.getSystemService(Context.POWER_SERVICE) as PowerManager
            val wakeLock = powerManager.newWakeLock(
                PowerManager.PARTIAL_WAKE_LOCK,
                "$TAG:ServiceGuardWakeLock"
            )

            // 短暂持有WakeLock，确保保活动作完成
            wakeLock.acquire(5000) // 5秒后自动释放

            // 延迟释放WakeLock
            timerScope.launch {
                delay(3000) // 3秒后释放
                if (wakeLock.isHeld) {
                    wakeLock.release()
                    Logger.com("$TAG WakeLock已释放")
                }
            }

        } catch (e: Exception) {
            Logger.comE("$TAG WakeLock管理失败", e)
        }
    }
}
