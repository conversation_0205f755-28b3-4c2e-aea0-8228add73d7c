package com.dspread.mdm.service.platform.api.screen

import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import com.dspread.mdm.service.modules.remoteview.MediaProjectionScreenCapture
import com.dspread.mdm.service.platform.api.system.ShellApi
import com.dspread.mdm.service.utils.log.Logger
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.*

/**
 * 屏幕截取API
 * 统一管理各种截屏方式
 * 提供平台级的屏幕截取功能
 */
class ScreenCaptureApi private constructor(private val context: Context) {

    companion object {
        private const val DEFAULT_COMPRESSION_QUALITY = 60
        private const val DEFAULT_SCALE_FACTOR = 2 // 1/2缩放

        @Volatile
        private var instance: ScreenCaptureApi? = null

        /**
         * 获取单例实例
         */
        fun getInstance(context: Context): ScreenCaptureApi {
            return instance ?: synchronized(this) {
                instance ?: ScreenCaptureApi(context.applicationContext).also { instance = it }
            }
        }
    }

    // 使用稳定版本的MediaProjectionScreenCapture
    private val mediaProjectionScreenCapture by lazy {
        MediaProjectionScreenCapture(context).apply {
            initialize()
        }
    }

    /**
     * 截屏到文件（同步方法）
     * @param filePath 保存文件路径
     * @return 是否成功
     */
    suspend fun captureScreenToFile(filePath: String): Boolean {
        Logger.remote("开始截屏到文件: $filePath")
        val startTime = System.currentTimeMillis()
        
        return try {
            // 优先使用MediaProjection截屏
            var success = false

            Logger.remote("使用MediaProjection执行截屏到文件")

            // 使用MediaProjection截屏，然后保存到文件
            val screenshotData = mediaProjectionScreenCapture.captureScreen()
            if (screenshotData != null) {
                try {
                    val file = File(filePath)
                    file.parentFile?.mkdirs()

                    FileOutputStream(file).use { fos ->
                        fos.write(screenshotData)
                        fos.flush()
                    }

                    success = file.exists() && file.length() > 0
                    if (success) {
                        Logger.remote("MediaProjection截屏到文件成功: ${file.length()} bytes")
                    }
                } catch (e: Exception) {
                    Logger.remoteE("保存截屏文件失败", e)
                }
            }

            // 如果MediaProjection失败，尝试Shell命令
            if (!success) {
                Logger.remote("MediaProjection失败，尝试Shell命令截屏")
                success = captureScreenWithShellCommand(filePath)
            }

            val duration = System.currentTimeMillis() - startTime
            if (success) {
                Logger.remote("截屏到文件成功，耗时: ${duration}ms")
            } else {
                Logger.remoteE("截屏到文件失败，耗时: ${duration}ms")
            }
            
            success
            
        } catch (e: Exception) {
            Logger.remoteE("截屏到文件异常", e)
            false
        }
    }

    /**
     * 截屏到字节数组（异步方法）
     * @param quality 压缩质量 (0-100)
     * @param scaleFactor 缩放因子 (1=原尺寸, 2=1/2尺寸)
     * @return 截屏数据
     */
    suspend fun captureScreenToBytes(
        quality: Int = DEFAULT_COMPRESSION_QUALITY,
        scaleFactor: Int = DEFAULT_SCALE_FACTOR
    ): ByteArray? = withContext(Dispatchers.IO) {
        Logger.remote("开始截屏到字节数组，质量: $quality, 缩放: 1/$scaleFactor")
        val startTime = System.currentTimeMillis()
        
        try {
            // 优先使用MediaProjection
            var screenshotData = mediaProjectionScreenCapture.captureScreen()

            // 如果失败，尝试Shell命令
            if (screenshotData == null) {
                Logger.remote("MediaProjection失败，尝试Shell命令")
                screenshotData = captureScreenWithShellToBytes()
            }
            
            if (screenshotData != null) {
                // 处理图片压缩和缩放
                val processedData = processScreenshotData(screenshotData, quality, scaleFactor)
                
                val duration = System.currentTimeMillis() - startTime
                Logger.remote("截屏到字节数组成功，原始: ${screenshotData.size} bytes, 处理后: ${processedData?.size ?: 0} bytes, 耗时: ${duration}ms")
                
                processedData
            } else {
                Logger.remoteE("所有截屏方法都失败")
                null
            }
            
        } catch (e: Exception) {
            Logger.remoteE("截屏到字节数组异常", e)
            null
        }
    }

    /**
     * 使用Shell命令截屏到文件
     */
    private suspend fun captureScreenWithShellCommand(filePath: String): Boolean {
        return try {
            val command = "screencap -p $filePath"
            val result = ShellApi.executeCommand(command)

            val file = File(filePath)
            val success = file.exists() && file.length() > 0 && result.isSuccess

            if (success) {
                Logger.remote("Shell命令截屏成功: ${file.length()} bytes")
            } else {
                Logger.remoteE("Shell命令截屏失败: ${result.exceptionOrNull()?.message}")
            }

            success

        } catch (e: Exception) {
            Logger.remoteE("Shell命令截屏异常", e)
            false
        }
    }

    /**
     * 使用Shell命令截屏到字节数组
     * 优先使用高效的流式读取，备用文件方式
     */
    private suspend fun captureScreenWithShellToBytes(): ByteArray? {
        return try {
            // 直接读取命令输出流
            val efficientBytes = captureScreenWithShellStream()
            if (efficientBytes != null) {
                return efficientBytes
            }

            // 方法2：备用方式，通过文件
            val tempFile = File.createTempFile("screenshot", ".png", context.cacheDir)

            if (captureScreenWithShellCommand(tempFile.absolutePath)) {
                val data = tempFile.readBytes()
                tempFile.delete()
                data
            } else {
                null
            }

        } catch (e: Exception) {
            Logger.remoteE("Shell命令截屏到字节数组异常", e)
            null
        }
    }

    /**
     * 直接从screencap命令读取字节流（高效方式）
     */
    private suspend fun captureScreenWithShellStream(): ByteArray? {
        return withContext(Dispatchers.IO) {
            try {
                val process = Runtime.getRuntime().exec("screencap -p")
                val inputStream = process.inputStream
                val bufferedInputStream = java.io.BufferedInputStream(inputStream)

                val buffer = StringBuilder(1024 * 1024)
                val tempBuffer = ByteArray(1024 * 1024)

                var count: Int
                while (bufferedInputStream.read(tempBuffer).also { count = it } > 0) {
                    buffer.append(String(tempBuffer, 0, count, Charsets.ISO_8859_1))
                }

                bufferedInputStream.close()
                val retCode = process.waitFor()
                process.destroy()

                if (retCode == 0) {
                    val result = buffer.toString().toByteArray(Charsets.ISO_8859_1)
                    Logger.remote("Shell流式截屏成功: ${result.size} bytes")
                    result
                } else {
                    Logger.remoteE("Shell流式截屏失败: retCode=$retCode")
                    null
                }

            } catch (e: Exception) {
                Logger.remoteE("Shell流式截屏异常", e)
                null
            }
        }
    }

    /**
     * 处理截屏数据（压缩和缩放）
     */
    private fun processScreenshotData(
        data: ByteArray,
        quality: Int,
        scaleFactor: Int
    ): ByteArray? {
        return try {
            val bitmap = BitmapFactory.decodeByteArray(data, 0, data.size)
            if (bitmap == null) {
                Logger.remoteE("无法解码截屏数据")
                return null
            }
            
            // 缩放处理
            val scaledBitmap = if (scaleFactor > 1) {
                val newWidth = bitmap.width / scaleFactor
                val newHeight = bitmap.height / scaleFactor
                Bitmap.createScaledBitmap(bitmap, newWidth, newHeight, true)
            } else {
                bitmap
            }
            
            // 压缩处理
            val outputStream = ByteArrayOutputStream()
            scaledBitmap.compress(Bitmap.CompressFormat.JPEG, quality, outputStream)
            val result = outputStream.toByteArray()
            
            // 清理资源
            if (scaledBitmap != bitmap) {
                scaledBitmap.recycle()
            }
            bitmap.recycle()
            outputStream.close()
            
            result
            
        } catch (e: Exception) {
            Logger.remoteE("处理截屏数据异常", e)
            null
        }
    }

    /**
     * 释放资源
     */
    fun release() {
        try {
            Logger.remote("释放ScreenCaptureApi资源")
            mediaProjectionScreenCapture.release()
        } catch (e: Exception) {
            Logger.remoteE("释放ScreenCaptureApi资源失败", e)
        }
    }

}
