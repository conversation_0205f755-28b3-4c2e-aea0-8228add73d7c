package com.dspread.mdm.service.utils.zip

import android.os.Build
import android.util.Log
import androidx.annotation.RequiresApi
import com.dspread.mdm.service.utils.log.Logger
import java.io.*
import java.util.zip.*

/**
 * ZIP 压缩解压缩工具类
 */
object ZipJava {
    private const val TAG = "ZipJava"

    /**
     * 压缩字节数组
     */
    @RequiresApi(api = Build.VERSION_CODES.N)
    @JvmStatic
    @Throws(Exception::class)
    fun compress(data: ByteArray?): ByteArray? {
        if (data == null) return null
        
        var byteArrayOutputStream: ByteArrayOutputStream? = null
        var zipOutputStream: ZipOutputStream? = null
        
        return try {
            byteArrayOutputStream = ByteArrayOutputStream()
            zipOutputStream = ZipOutputStream(byteArrayOutputStream)
            zipOutputStream.putNextEntry(ZipEntry("0"))
            zipOutputStream.write(data)
            zipOutputStream.closeEntry()

            byteArrayOutputStream.toByteArray()
            
        } catch (e: IOException) {
            Logger.wsmE("压缩字符串数据出错", e)
            throw Exception("压缩字符串数据出错", e)
        } finally {
            zipOutputStream?.let {
                try {
                    it.close()
                } catch (e: IOException) {
                    Log.d(TAG, "关闭zipOutputStream出错", e)
                }
            }
            byteArrayOutputStream?.let {
                try {
                    it.close()
                } catch (e: IOException) {
                    Log.e(TAG, "关闭byteArrayOutputStream出错", e)
                }
            }
        }
    }

    /**
     * 解压缩字节数组
     */
    @JvmStatic
    @Throws(Exception::class)
    fun decompress(compressed: ByteArray?): ByteArray? {
        if (compressed == null) return null
        
        // ZIP 解压缩开始
        
        var out: ByteArrayOutputStream? = null
        var inputStream: ByteArrayInputStream? = null
        var zipInputStream: ZipInputStream? = null
        
        return try {
            out = ByteArrayOutputStream()
            inputStream = ByteArrayInputStream(compressed)
            zipInputStream = ZipInputStream(inputStream)
            
            zipInputStream.nextEntry

            val buffer = ByteArray(1024)
            var offset: Int

            while (zipInputStream.read(buffer).also { offset = it } != -1) {
                out.write(buffer, 0, offset)
            }
            
            out.toByteArray()
            
        } catch (e: IOException) {
            Logger.wsmE("解压缩字符串数据出错", e)
            throw Exception("解压缩字符串数据出错", e)
        } finally {
            zipInputStream?.let {
                try {
                    it.close()
                } catch (e: IOException) {
                    Log.d(TAG, "关闭ZipInputStream出错", e)
                }
            }
            inputStream?.let {
                try {
                    it.close()
                } catch (e: IOException) {
                    Log.e(TAG, "关闭ByteArrayInputStream出错", e)
                }
            }
            out?.let {
                try {
                    it.close()
                } catch (e: IOException) {
                    Log.d(TAG, "关闭ByteArrayOutputStream出错", e)
                }
            }
        }
    }

    /**
     * 压缩文件或目录到ZIP文件
     */
    @JvmStatic
    @Throws(IOException::class)
    fun zip(src: String, dest: String) {
        var out: ZipOutputStream? = null
        try {
            val outFile = File(dest)
            val fileOrDirectory = File(src)
            out = ZipOutputStream(FileOutputStream(outFile))
            
            if (fileOrDirectory.isFile) {
                zipFileOrDirectory(out, fileOrDirectory, "")
            } else {
                val entries = fileOrDirectory.listFiles()
                entries?.forEach { entry ->
                    zipFileOrDirectory(out, entry, "")
                }
            }
        } catch (ex: IOException) {
            ex.printStackTrace()
        } finally {
            out?.let {
                try {
                    it.close()
                } catch (ex: IOException) {
                    ex.printStackTrace()
                }
            }
        }
    }

    /**
     * 递归压缩文件或目录
     */
    private fun zipFileOrDirectory(out: ZipOutputStream, fileOrDirectory: File, curPath: String) {
        var inputStream: FileInputStream? = null
        try {
            if (!fileOrDirectory.isDirectory) {
                // 压缩文件
                val buffer = ByteArray(4096)
                var bytesRead: Int
                inputStream = FileInputStream(fileOrDirectory)
                val entry = ZipEntry(curPath + fileOrDirectory.name)
                out.putNextEntry(entry)
                
                while (inputStream.read(buffer).also { bytesRead = it } != -1) {
                    out.write(buffer, 0, bytesRead)
                }
                out.closeEntry()
            } else {
                // 压缩目录
                val entries = fileOrDirectory.listFiles()
                entries?.forEach { entry ->
                    zipFileOrDirectory(out, entry, curPath + fileOrDirectory.name + "/")
                }
            }
        } catch (ex: IOException) {
            ex.printStackTrace()
        } finally {
            inputStream?.let {
                try {
                    it.close()
                } catch (ex: IOException) {
                    ex.printStackTrace()
                }
            }
        }
    }
}
