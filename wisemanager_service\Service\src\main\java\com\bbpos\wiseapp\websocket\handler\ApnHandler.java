package com.bbpos.wiseapp.websocket.handler;

import android.content.Context;
import android.os.Build;
import android.telephony.SubscriptionInfo;
import android.text.TextUtils;

import androidx.annotation.RequiresApi;

import com.bbpos.wiseapp.logger.BBLog;
import com.bbpos.wiseapp.service.common.UsualData;
import com.bbpos.wiseapp.settings.utils.APNManager;
import com.bbpos.wiseapp.settings.utils.APNManager.APN;
import com.bbpos.wiseapp.settings.utils.WirelessUtil;
import com.bbpos.wiseapp.tms.utils.ContextUtil;
import com.bbpos.wiseapp.tms.utils.Helpers;
import com.bbpos.wiseapp.tms.utils.ParameterName;
import com.bbpos.wiseapp.utils.SharedPreferencesUtils;
import com.bbpos.wiseapp.utils.ThreadUtils;
import com.bbpos.wiseapp.websocket.WebSocketSender;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

@RequiresApi(api = Build.VERSION_CODES.M)
public class ApnHandler {
    private static final String TAG = BBLog.TAG;
    private static Context mContext;

    public ApnHandler(Context context) {
        mContext = context;
    }

    public void handleMsg(String response) {
//        BBLog.d("ApnHandler handleMsg : " + response);
        try {
            JSONObject responseJson = new JSONObject(response);
            JSONObject responseData = Helpers.getDataFromResponse(responseJson);
            if (responseData == null) {
                BBLog.w(TAG, "responseData == null");
                return;
            }
            if (responseData.has(ParameterName.apnList)) {
                //前置检查
                JSONArray apnList = responseData.getJSONArray(ParameterName.apnList);
//                BBLog.w(TAG, "apnList = " + apnList);
                if (apnList == null || apnList.length() == 0) {
                    String cacheApnData = SharedPreferencesUtils.getSharePreferencesValue(UsualData.SHARED_PREFERENCES_APN_LIST,"");
                    //移除已设置的apn，并恢复默认
                    BBLog.w(TAG, "下发APN数据为空，移除所有已保存的APN配置项");
                    removeCustomerAddApnIfNeed(cacheApnData);
                    SharedPreferencesUtils.clearByKey(UsualData.SHARED_PREFERENCES_APN_LIST);
                    return;
                }

                List<APN> apnDatas= parseDataToApnList(apnList);
                if (apnDatas.size() > 0) {
                    BBLog.w(TAG, "移除所有已保存的APN配置项");
                    removeCustomerAddApn();
                    SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_APN_LIST,apnList.toString());
                    // 根据numeric进行分组，符合当前运营商组的首个节点为优先级最高的apn，需要设置为首选
                    Map<String, List<APN>> numericMap = groupAPNsByNumeric(apnDatas);
                    //不包含当前运营商的apn集合
                    Map<String, List<APN>> numericMapAfterRemove = numericMap;
                    List<SubscriptionInfo> subscriptionInfoList = APNManager.getInstance().getActiveSubscriptionInfoList();
                    if (subscriptionInfoList != null && subscriptionInfoList.size() > 0) {
                        boolean applyApnResult = false;
                        APN firstIndexApn = null;
                        SubscriptionInfo currentSubInfo = null;
                        for (SubscriptionInfo info : subscriptionInfoList) {
                            String numeric = APNManager.getInstance().getSimOperator();
                            if (numericMap.get(numeric) != null) {
                                List<APN> apns = numericMap.get(numeric);
                                if (apns != null && apns.size() > 0) {
                                    BBLog.d(TAG," 保存当前运营商下的APN配置");
                                    for (int i = 0; i < apns.size(); i++) {
                                        if (i == 0) {//添加优先级最高的apn并应用
                                            firstIndexApn = apns.get(0);
                                            BBLog.d(TAG," 当前运营商下的首选APN配置信息： " + firstIndexApn);
                                            currentSubInfo = info;
                                            applyApnResult = isApplyFirstApnFromMDM(applyApnResult, firstIndexApn, info);
                                            BBLog.d(TAG," 保存当前运营商下的首选APN Result = " + applyApnResult);
                                        }else {//其余apn只保存不做进一步处理
                                            BBLog.d(TAG," 保存当前运营商下其他APN.");
                                            APNManager.getInstance().checkAndSaveApn(apns.get(i));
                                        }
                                    }
                                }
                                //移除当前运营商的数据
                                numericMapAfterRemove.remove(numeric);
                            }
                        }

                        //保存剩余数据
                        BBLog.d(TAG," 保存其他运营商的APN配置");
                        APNManager.getInstance().checkAndSaveAllApn(numericMap);

                        BBLog.d(TAG," 当前运营商首选APN配置状态: " + applyApnResult);
                        if (applyApnResult) {
                            APNManager.getInstance().applyApnNetWork(mContext);
                            Thread.sleep(5000);
                            if (Helpers.isMobile(mContext)) {
                                BBLog.d(TAG, "当前为移动数据网络，检测网络状态... ");
                                APN finalFirstIndexApn = firstIndexApn;
                                checkNetworkFineAfterSwitch(finalFirstIndexApn);
                            } else {
                                BBLog.d(TAG, "当前为WIFI环境，仅添加APN... ");
                                applyApnResult = isApplyFirstApnFromMDM(applyApnResult, firstIndexApn, currentSubInfo);
                                BBLog.d(TAG," applyApn Result = " + applyApnResult);
                                //上送状态
                                WebSocketSender.C0904_NetworkStatusUpload();
                            }
                        }
                    } else {
                        BBLog.d(TAG," 终端未插入SIM卡，仅保存APN");
                        APNManager.getInstance().checkAndSaveAllApn(numericMap);
                    }
                }
            }
        } catch (Exception e){
            e.printStackTrace();
        }
    }

    private static void checkNetworkFineAfterSwitch(APN finalFirstIndexApn) {
        if (Helpers.isWifi(ContextUtil.getInstance())) {
            BBLog.d(TAG, " checkNetworkFineAfterSwitch: 终端当前使用WiFi网络，略过 ");
            return;
        }
        ThreadUtils.postOnBackgroundThread(new Runnable() {
            @Override
            public void run() {
                try {
                    boolean pingSuccess = false;
                    int maxAttempts = 5;
                    int attemptCount = 1;
                    while (attemptCount <= maxAttempts && !pingSuccess) {
                        BBLog.d(TAG, " ping attemptCount =" + attemptCount);
                        if (WirelessUtil.ping()) {
                            pingSuccess = true;
                        }
                        attemptCount++;
                        Thread.sleep(2000);
                    }
                    if (pingSuccess) {
                        BBLog.d(TAG, " APN切换成功并且ping成功 ");
                        WebSocketSender.C0904_NetworkStatusUpload();
                    } else {
                        BBLog.d(TAG, " APN切换成功并且ping失败,准备恢复到默认apn ");
                        APNManager.getInstance().restoreDefaultApnBySubId(APNManager.getInstance().getDefaultSubscriptionId());
                         //解除网络绑定
                        APNManager.getInstance().restoreNormalNetwork(mContext);
                        //重新添加所有apn
                        APNManager.getInstance().checkAndSaveAllApn(parseCacheDataToMap());
                        //上送状态
                        BBLog.d(TAG, "ApnHandler 上送APN ");
                        WebSocketSender.C0904_NetworkStatusUpload();
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        });
    }

    private static boolean isApplyFirstApnFromMDM(boolean applyApnResult, APN firstIndexApn, SubscriptionInfo info) {
        try {
            APN findApn = APNManager.getInstance().findApnByApnNameAndNumeric(firstIndexApn.getApn(), firstIndexApn.getNumeric());
            if (findApn != null) {
                boolean result = applyApnResult = APNManager.getInstance().removeApnByOperatorAndId(findApn.getId());
                BBLog.d(TAG," remove apn Result = " + result);
            }
            int apnId = APNManager.getInstance().addApn(firstIndexApn,true);
            BBLog.d(TAG," addApn Result apnId= " + apnId);
            if (apnId != -1) {
                applyApnResult = APNManager.getInstance().applyApnBySubId(info.getSubscriptionId(), apnId);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return applyApnResult;
    }

    private void removeCustomerAddApnIfNeed(String apnList) {
        if (apnList == null || apnList.isEmpty()) return;
        try {
            JSONArray cacheApnData = new JSONArray(apnList);
            if (cacheApnData!=null && cacheApnData.length()>0) {
                for (int i = 0; i < cacheApnData.length(); i++) {
                    JSONObject apnData = (JSONObject)cacheApnData.opt(i);
                    String numericFromData = apnData.optString(ParameterName.MCC)+apnData.optString(ParameterName.MNC);
                    APNManager.getInstance().removeApnByOperatorAndApnName(apnData.optString(ParameterName.APN),numericFromData);
                }

                //解除网络绑定
                APNManager.getInstance().restoreNormalNetwork(mContext);
                APNManager.getInstance().restoreDefaultApnBySubId(APNManager.getInstance().getDefaultSubscriptionId());
                //上送网络信息
                BBLog.d(TAG,"removeCustomerAddApnIfNeed APN配置发生变化，上送C0904 ");
                WebSocketSender.C0904_NetworkStatusUpload();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void removeCustomerAddApn() {
        String cacheData = SharedPreferencesUtils.getSharePreferencesValue(UsualData.SHARED_PREFERENCES_APN_LIST,"");
        if (cacheData == null || cacheData.isEmpty()) return;
        removeCustomerAddApnIfNeed(cacheData);
    }

    public static Map<String,List<APN>> parseCacheDataToMap(){
        try {
            JSONArray cacheData = getApnDataFromCache();
            List<APN> apnDatas= parseDataToApnList(cacheData);
            if (apnDatas.size() > 0) {
                // 根据numeric进行分组，符合当前运营商组的首个节点为优先级最高的apn，需要设置为首选
                return groupAPNsByNumeric(apnDatas);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    private static List<APN> parseDataToApnList(JSONArray jsonArray) {
        List<APN> apnList = new ArrayList<>();
        if (jsonArray == null || jsonArray.length() == 0) return apnList;
        try {
            APN apn;
            JSONObject apnEntry;
            if (jsonArray != null && jsonArray.length() > 0) {
                for (int i = 0; i < jsonArray.length(); i++) {
                    apnEntry = (JSONObject) jsonArray.get(i);

                    apn = new APN();
                    apn.setName(apnEntry.optString(ParameterName.NAME));
                    apn.setApn(apnEntry.optString(ParameterName.APN));
                    apn.setProxy(apnEntry.optString(ParameterName.PROXY));
                    apn.setPort(apnEntry.optString(ParameterName.PORT));
                    apn.setMmsProxy(apnEntry.optString(ParameterName.MMSPROXY));
                    apn.setMmsPort(apnEntry.optString(ParameterName.MMSPORT));
                    apn.setServer(apnEntry.optString(ParameterName.SERVER));
                    apn.setUser(apnEntry.optString(ParameterName.USER));
                    apn.setPassword(apnEntry.optString(ParameterName.PASSWORD));
                    apn.setMmsc(apnEntry.optString(ParameterName.MMSC));
                    apn.setMcc(apnEntry.optString(ParameterName.MCC));
                    apn.setMnc(apnEntry.optString(ParameterName.MNC));
                    apn.setNumeric(apnEntry.optString(ParameterName.MCC)+""+apnEntry.optString(ParameterName.MNC));
                    apn.setAuthType(apnEntry.optInt(ParameterName.AUTH_TYPE));
                    apn.setApnType(apnEntry.optString(ParameterName.APN_TYPE));
                    apn.setProtocol(apnEntry.optString(ParameterName.PROTOCOL));
                    apn.setRoamingProtocol(apnEntry.optString(ParameterName.ROAMING_PROTOCOL));
                    apn.setCurrent(apnEntry.optString(ParameterName.CURRENT,"0"));
                    //处理bearer 和 bearer_bitmask 值
                    int bearerBitmask = APNManager.getInstance().getBearerBitmaskValue(apnEntry.optString(ParameterName.BEARER));
                    int bearerValue = APNManager.getInstance().getBearerValue(bearerBitmask);
//                    BBLog.d(BBLog.TAG," parseDataToApnList bearerBitmask = " + bearerBitmask + ", bearerValue = " + bearerValue);

                    apn.setBearer(bearerValue+"");
                    apn.setBearerBitmask(bearerBitmask+"");

                    apn.setMvnoType(apnEntry.optInt(ParameterName.MVNO_TYPE));
                    apn.setMvnoMatchData(apnEntry.optString(ParameterName.MVNO_MATCH_DATA));
                    apn.setPriorityOrder(apnEntry.optInt(ParameterName.APN_PRIORITY));
                    apn.setProId(apnEntry.optString(ParameterName.APN_PROID));
                    apnList.add(apn);
                }
            }
//            BBLog.d(TAG, " parseDataToApnList = " + apnList);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return apnList;
    }

    /**
     *  分组并排序
     * @param apnList
     * @return
     */
    private static Map<String, List<APN>> groupAPNsByNumeric(List<APN> apnList) {
        Map<String, List<APN>> numericMap = new HashMap<>();

        for (APN apn : apnList) {
            String numeric = apn.getNumeric();
            if (!numericMap.containsKey(numeric)) {
                numericMap.put(numeric, new ArrayList<>());
            }
            numericMap.get(numeric).add(apn);
        }

        for (List<APN> numericAPNs : numericMap.values()) {
            Collections.sort(numericAPNs, new Comparator<APN>() {
                @Override
                public int compare(APN apn1, APN apn2) {
                    return Integer.compare(apn1.getPriorityOrder(), apn2.getPriorityOrder());
                };
            });
        }
//        BBLog.d(TAG, " groupAPNsByNumeric = " + numericMap);
        return numericMap;
    }

    private static JSONArray getApnDataFromCache(){
        String cacheApnData = SharedPreferencesUtils.getSharePreferencesValue(UsualData.SHARED_PREFERENCES_APN_LIST,"");
        if (cacheApnData == null || cacheApnData.length() == 0) return null;
        try {
            JSONArray apnList = new JSONArray(cacheApnData);
            return apnList;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 根据指定参数查找apn是否为平台下发，若是则返回关联的proId
     * @param apn
     * @param mcc
     * @param mnc
     * @return
     */
    public static String getAnpProIdFromCacheIfExits(String apn, String mcc, String mnc) {
        if (TextUtils.isEmpty(apn) || TextUtils.isEmpty(mcc) || TextUtils.isEmpty(mnc)) return "";
        try {
            JSONArray cacheData = getApnDataFromCache();
            if (cacheData != null && cacheData.length() > 0) {
                for (int i=0; i< cacheData.length(); i++) {
                    JSONObject apnObj = cacheData.optJSONObject(i);
                    if (apnObj != null && (apn.equals(apnObj.optString(ParameterName.APN))
                            && mcc.equals(apnObj.optString(ParameterName.MCC))
                            && mnc.equals(apnObj.optString(ParameterName.MNC)))) {
                        return apnObj.optString(ParameterName.APN_PROID);
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "";
    }

    public static void checkIfApnAvailable() {
        BBLog.d(BBLog.TAG,"checkIfApnAvailable --");;
        try {
            JSONArray cacheApns = getApnDataFromCache();
            List<SubscriptionInfo> subList = APNManager.getInstance().getActiveSubscriptionInfoList();
            if (cacheApns != null && cacheApns.length() > 0) {
                for (int i = 0; i < cacheApns.length(); i++) {
                    JSONObject apnData = (JSONObject) cacheApns.opt(i);
                    for (SubscriptionInfo info : subList) {
                        String numeric = APNManager.getInstance().getSimOperator();
                        String numericFromData = apnData.optString(ParameterName.MCC) + apnData.optString(ParameterName.MNC);
                        BBLog.d(BBLog.TAG,"checkIfApnAvailable numeric = " + numeric + ", numericFromData = " + numericFromData);
                        if (numeric.equals(numericFromData)) {
                            APN saveApn = APNManager.getInstance().findApnByApnNameAndNumeric(apnData.optString(ParameterName.APN), numeric);
//                            BBLog.d(BBLog.TAG,"checkIfApnAvailable saveApn = " + saveApn);
                            if (saveApn != null) {
                                APN currentDefaultApn = APNManager.getInstance().getDefaultApnBySubId(info.getSubscriptionId());
//                                BBLog.d(BBLog.TAG,"checkIfApnAvailable currentDefaultApn = " + currentDefaultApn);
                                if (currentDefaultApn != null && saveApn.getId().equals(currentDefaultApn.getId())) {
                                    BBLog.d(BBLog.TAG,"checkIfApnAvailable -->终端正在使用自定义APN");
                                    checkNetworkFineAfterSwitch(currentDefaultApn);
                                    break;
                                }
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
