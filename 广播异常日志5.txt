2025-08-21 21:01:07.226  5840-5840  ead.mdm.service         com.dspread.mdm.service              W  type=1400 audit(0.0:647): avc: denied { write } for name="com.dspread.mdm.service-cVY3TkIBiGyMwPqwSysoJA==" dev="dm-6" ino=13908 scontext=u:r:system_app:s0 tcontext=u:object_r:apk_data_file:s0 tclass=dir permissive=0
2025-08-21 21:01:07.918  5840-5840  NetworkSecurityConfig   com.dspread.mdm.service              D  Using Network Security Config from resource network_security_config debugBuild: true
2025-08-21 21:01:07.922  5840-5840  NetworkSecurityConfig   com.dspread.mdm.service              D  Using Network Security Config from resource network_security_config debugBuild: true
2025-08-21 21:01:08.012  5840-5840  Common                  com.dspread.mdm.service              D  🔧 日志配置：测试环境模式
2025-08-21 21:01:08.018  5840-5840  Common                  com.dspread.mdm.service              D  🔧 [DebugConfig] 调试模式已启用
2025-08-21 21:01:08.020  5840-5840  Common                  com.dspread.mdm.service              D  🔧 SmartMdmServiceApp: 统一配置管理器初始化完成
2025-08-21 21:01:08.024  5840-5840  Common                  com.dspread.mdm.service              D  🔧 SmartMdmServiceApp: 系统目录初始化完成 - /sdcard/Android/data/com.dspread.mdm.service/files/config/
2025-08-21 21:01:08.067  5840-5840  Receiver                com.dspread.mdm.service              D  🔧 ServiceGuardEventHandler 启动服务守护定时器: 120s
2025-08-21 21:01:08.074  5840-5840  Common                  com.dspread.mdm.service              I  ✅ ⏰ 设置服务守护定时器成功，下次执行: 120秒后 (2分钟)
2025-08-21 21:01:08.076  5840-5840  Common                  com.dspread.mdm.service              D  🔧 SmartMdmServiceApp: 服务守护定时器启动成功
2025-08-21 21:01:08.082  5840-5840  Common                  com.dspread.mdm.service              D  🔧 ServiceStartupManager 收到服务启动请求: APPLICATION_STARTUP, 强制启动: false
2025-08-21 21:01:08.088  5840-5840  Common                  com.dspread.mdm.service              D  🔧 ServiceStartupManager 开始执行服务启动: APPLICATION_STARTUP
2025-08-21 21:01:08.091  5840-5840  Common                  com.dspread.mdm.service              D  🔧 ServiceManager: 准备启动后台服务
2025-08-21 21:01:08.094  5840-5840  ContextImpl             com.dspread.mdm.service              W  Calling a method in the system process without a qualified user: android.app.ContextImpl.startService:1669 android.content.ContextWrapper.startService:720 com.dspread.mdm.service.platform.manager.ServiceManager.startBackgroundService:20 com.dspread.mdm.service.platform.manager.ServiceStartupManager.performServiceStart:121 com.dspread.mdm.service.platform.manager.ServiceStartupManager.startService:66 
2025-08-21 21:01:08.098  5840-5840  Common                  com.dspread.mdm.service              D  🔧 ServiceManager: 后台服务启动成功，ComponentName: ComponentInfo{com.dspread.mdm.service/com.dspread.mdm.service.services.SmartMdmBackgroundService}
2025-08-21 21:01:09.127  5840-5840  Common                  com.dspread.mdm.service              D  🔧 ServiceStartupManager 服务启动验证成功: APPLICATION_STARTUP
2025-08-21 21:01:09.130  5840-5840  Common                  com.dspread.mdm.service              D  🔧 ServiceStartupManager 服务启动成功: APPLICATION_STARTUP
2025-08-21 21:01:09.132  5840-5840  Common                  com.dspread.mdm.service              D  🔧 SmartMdmServiceApp: 主服务启动请求已提交
2025-08-21 21:01:09.134  5840-5840  Common                  com.dspread.mdm.service              D  🔧 SmartMdmServiceApp: Application启动完成，包名: com.dspread.mdm.service
2025-08-21 21:01:09.267  5840-5840  SurfaceFactory          com.dspread.mdm.service              I  [static] sSurfaceFactory = com.mediatek.view.impl.SurfaceFactoryImpl@b98077b
2025-08-21 21:01:09.277  5840-5840  ViewRootIm...tActivity] com.dspread.mdm.service              D  hardware acceleration = true , fakeHwAccelerated = false, sRendererDisabled = false, forceHwAccelerated = false, sSystemRendererDisabled = false
2025-08-21 21:01:09.284  5840-5840  InputTransport          com.dspread.mdm.service              I  Create ARC handle: 0x9e1b9270
2025-08-21 21:01:09.286  5840-5840  PhoneWindow             com.dspread.mdm.service              V  DecorView setVisiblity: visibility = 0, Parent = android.view.ViewRootImpl@e15a057, this = DecorView@59f744[TestActivity]
2025-08-21 21:01:09.294  5840-5840  PhoneWindow             com.dspread.mdm.service              V  DecorView setVisiblity: visibility = 4, Parent = android.view.ViewRootImpl@e15a057, this = DecorView@59f744[TestActivity]
2025-08-21 21:01:09.295  5840-5840  Choreographer           com.dspread.mdm.service              I  Skipped 78 frames!  The application may be doing too much work on its main thread.
2025-08-21 21:01:09.330  5840-5840  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 服务启动
2025-08-21 21:01:09.342  5840-5840  setContentIntent        com.dspread.mdm.service              D  packageName = com.dspread.mdm.service
2025-08-21 21:01:09.348   947-975   NotificationService     system_server                        E  enqueueNotificationInternal pkg: com.dspread.mdm.service
2025-08-21 21:01:09.348  5840-5840  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 开始服务初始化
2025-08-21 21:01:09.351  5840-5840  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] Android < 14: 启动Provisioning服务
2025-08-21 21:01:09.355  5840-5840  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 服务创建完成
2025-08-21 21:01:09.355  5840-5871  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 启动ProvisioningService
2025-08-21 21:01:09.359  5840-5840  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] onStartCommand调用
2025-08-21 21:01:09.365  5840-5871  Provisioning            com.dspread.mdm.service              I  ℹ️ ProvisioningManager初始化完成
2025-08-21 21:01:09.368  5840-5840  Platform                com.dspread.mdm.service              D  🔧 WakeLockManager 获取智能WakeLock成功，持续时间: 60000ms
2025-08-21 21:01:09.368  5840-5871  Provisioning            com.dspread.mdm.service              D  🔧 状态标志文件不存在，使用默认值
2025-08-21 21:01:09.373  5840-5840  Platform                com.dspread.mdm.service              D  🔧 WakeLockManager 设置WakeLock续期定时器: 60000ms
2025-08-21 21:01:09.383  5840-5871  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] ProvisioningService启动完成
2025-08-21 21:01:09.384  5840-5873  Provisioning            com.dspread.mdm.service              I  ℹ️ 开始执行Provisioning配置，触发类型: FIRST_BOOT
2025-08-21 21:01:09.387  5840-5840  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 初始化基础组件
2025-08-21 21:01:09.388  5840-5873  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] Provisioning进度: 正在下载配置文件...
2025-08-21 21:01:09.391  5840-5873  Provisioning            com.dspread.mdm.service              I  ℹ️ 开始请求远程配置...
2025-08-21 21:01:09.406  5840-5873  Provisioning            com.dspread.mdm.service              I  ℹ️ 请求配置: https://config.dspreadserv.net/status/config?SN=MDE2MTAwNDAyMDIzMDcwNjAyMjc=&timestamp=1755781269405
2025-08-21 21:01:09.416  5840-5873  System.out              com.dspread.mdm.service              I  [okhttp]:check permission begin!
2025-08-21 21:01:09.417  5840-5873  System                  com.dspread.mdm.service              W  ClassLoader referenced unknown path: system/framework/mediatek-cta.jar
2025-08-21 21:01:09.419  5840-5873  System.out              com.dspread.mdm.service              I  [okhttp] e:java.lang.ClassNotFoundException: com.mediatek.cta.CtaUtils
2025-08-21 21:01:09.430  5840-5840  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 服务初始化完成
2025-08-21 21:01:09.487  5840-5875  Platform                com.dspread.mdm.service              D  🔧 DeviceInfoApi 单例实例已创建
2025-08-21 21:01:09.507  5840-5875  Platform                com.dspread.mdm.service              W  ⚠️ SpVersionApi SP版本信息格式异常，未找到SP_VERSION标记
2025-08-21 21:01:09.520  5840-5875  Platform                com.dspread.mdm.service              D  🔧 SpVersionApi 从Settings获取SP版本: STD 582.1.238.8
2025-08-21 21:01:09.554  5840-5840  WebSocket               com.dspread.mdm.service              I  🔧 C0905 主动式上送: manual_trigger (主动: 1)
2025-08-21 21:01:09.567  5840-5840  WebSocket               com.dspread.mdm.service              I  🔧 首次上送，上送当日数据
2025-08-21 21:01:09.571  5840-5840  WebSocket               com.dspread.mdm.service              I  🔧 没有流量数据需要上送
2025-08-21 21:01:09.573  5840-5840  Common                  com.dspread.mdm.service              D  🔧 刷新时自动上送流量统计
2025-08-21 21:01:09.795  5840-5873  System.out              com.dspread.mdm.service              I  [socket]:check permission begin!
2025-08-21 21:01:09.796  5840-5873  System                  com.dspread.mdm.service              W  ClassLoader referenced unknown path: system/framework/mediatek-cta.jar
2025-08-21 21:01:09.797  5840-5873  System.out              com.dspread.mdm.service              I  [socket] e:java.lang.ClassNotFoundException: com.mediatek.cta.CtaUtils
2025-08-21 21:01:10.899  5840-5873  System.out              com.dspread.mdm.service              I  [OkHttp] sendRequest>>
2025-08-21 21:01:10.900  5840-5873  System.out              com.dspread.mdm.service              I  [OkHttp] sendRequest<<
2025-08-21 21:01:12.154  5840-5873  Provisioning            com.dspread.mdm.service              I  ℹ️ 远程配置获取成功
2025-08-21 21:01:12.157  5840-5873  Provisioning            com.dspread.mdm.service              I  ℹ️ 远程配置请求成功
2025-08-21 21:01:12.159  5840-5873  Provisioning            com.dspread.mdm.service              I  ℹ️ 远程配置解析成功 - CID: 1001
2025-08-21 21:01:12.162  5840-5873  Provisioning            com.dspread.mdm.service              I  ℹ️ 创建配置目录成功: /sdcard/Android/data/com.dspread.mdm.service/files/config
2025-08-21 21:01:12.172  5840-5873  Provisioning            com.dspread.mdm.service              I  ℹ️ API响应已保存到本地: /sdcard/Android/data/com.dspread.mdm.service/files/config/provisioning_config.json, 具体内容为:
                                                                                                    {"mode":"dev","data":{"requestTime":"1755781273013","customization":{"system":{"timezone":"Asia\/Hong_Kong","logo":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/test\/logo.bin","logoMd5":"ba1ee533924eae5c408465e7cddcbda4","bootAnimationMd5":"ba1ee533924eae5c408465e7cddcbda4","gps":{"minUpdateTime":"30","scheduleTime":"60","maxLocateTime":"0","minDistance":"10","valid_distance":"500","care":"1"},"powerSaveMode":{"enable":"1","screenTimeout":"0"},"bootAnimation":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/test\/bootanimation.zip"},"polling":{"heartbeatTime":"300","terminalInfoTime":"900","statusApiUrl":"wss:\/\/api.dspreadserv.net\/status\/websocket\/register","remoteUrl":"wss:\/\/remote.dspreadserv.net\/remoteWSS\/websockify","uploadMode":"1","wssreconn":{"pingInterval":"30","delayPolicy":"1","delaySwitch":"1","delayTime":"60"}}},"client":"default","cid":"1001"},"function":"getProvisioningConfig","description":"SUCCESS","stateCode":"0","version":"v1.0.1.20250801"}
2025-08-21 21:01:12.175  5840-5873  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] Provisioning进度: 正在下载 Logo...
2025-08-21 21:01:12.180  5840-5873  Provisioning            com.dspread.mdm.service              I  ℹ️ 文件不存在，开始下载: Logo
2025-08-21 21:01:12.182  5840-5873  Provisioning            com.dspread.mdm.service              I  ℹ️ 下载: https://siot-bucket01.s3.ap-northeast-1.amazonaws.com/test/logo.bin -> /sdcard/Android/data/com.dspread.mdm.service/files/config/logo.bin
2025-08-21 21:01:12.189  5840-5873  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 尝试下载，第1次
2025-08-21 21:01:12.192  5840-5873  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 开始下载
2025-08-21 21:01:12.194  5840-5873  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader URL: https://siot-bucket01.s3.ap-northeast-1.amazonaws.com/test/logo.bin
2025-08-21 21:01:12.196  5840-5873  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 路径: /sdcard/Android/data/com.dspread.mdm.service/files/config/logo.bin
2025-08-21 21:01:12.199  5840-5873  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 本地文件不存在，创建文件
2025-08-21 21:01:12.203  5840-5873  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader allFileLength=0, 需要从服务器获取文件大小
2025-08-21 21:01:12.207  5840-5873  System.out              com.dspread.mdm.service              I  [okhttp]:check permission begin!
2025-08-21 21:01:12.207  5840-5873  System.out              com.dspread.mdm.service              I  [okhttp]:not MMS!
2025-08-21 21:01:12.207  5840-5873  System.out              com.dspread.mdm.service              I  [okhttp]:not Email!
2025-08-21 21:01:14.900  5840-5888  ProfileInstaller        com.dspread.mdm.service              D  Installing profile for com.dspread.mdm.service
2025-08-21 21:01:17.578  5840-5873  System.out              com.dspread.mdm.service              I  [socket]:check permission begin!
2025-08-21 21:01:18.006  5840-5873  System.out              com.dspread.mdm.service              I  [OkHttp] sendRequest>>
2025-08-21 21:01:18.007  5840-5873  System.out              com.dspread.mdm.service              I  [OkHttp] sendRequest<<
2025-08-21 21:01:18.602  5840-5873  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader HTTP响应码: 200
2025-08-21 21:01:18.606  5840-5873  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 服务器返回Content-Length: 4771451
2025-08-21 21:01:18.608  5840-5873  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 完整下载模式
2025-08-21 21:01:18.610  5840-5873  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 完整下载，使用服务器返回大小: 4771451
2025-08-21 21:01:18.612  5840-5873  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader localFile = true /sdcard/Android/data/com.dspread.mdm.service/files/config/logo.bin, 追加模式: false
2025-08-21 21:02:02.115  5840-5873  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 计算MD5: logo.bin
2025-08-21 21:02:02.209  5840-5873  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 文件: 4771451 MD5: ba1ee533924eae5c408465e7cddcbda4
2025-08-21 21:02:02.211  5840-5873  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader MD5校验正确
2025-08-21 21:02:02.213  5840-5873  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 清理连接资源
2025-08-21 21:02:02.216  5840-5873  Provisioning            com.dspread.mdm.service              I  ℹ️ Logo 下载完成: /sdcard/Android/data/com.dspread.mdm.service/files/config/logo.bin
2025-08-21 21:02:02.218  5840-5873  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] Provisioning进度: 正在下载 BootAnimation...
2025-08-21 21:02:02.221  5840-5873  Provisioning            com.dspread.mdm.service              I  ℹ️ 文件不存在，开始下载: BootAnimation
2025-08-21 21:02:02.224  5840-5873  Provisioning            com.dspread.mdm.service              I  ℹ️ 下载: https://siot-bucket01.s3.ap-northeast-1.amazonaws.com/test/bootanimation.zip -> /sdcard/Android/data/com.dspread.mdm.service/files/config/bootanimation.zip
2025-08-21 21:02:02.226  5840-5873  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 尝试下载，第1次
2025-08-21 21:02:02.229  5840-5873  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 开始下载
2025-08-21 21:02:02.231  5840-5873  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader URL: https://siot-bucket01.s3.ap-northeast-1.amazonaws.com/test/bootanimation.zip
2025-08-21 21:02:02.232  5840-5873  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 路径: /sdcard/Android/data/com.dspread.mdm.service/files/config/bootanimation.zip
2025-08-21 21:02:02.235  5840-5873  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 本地文件不存在，创建文件
2025-08-21 21:02:02.238  5840-5873  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader allFileLength=0, 需要从服务器获取文件大小
2025-08-21 21:02:02.242  5840-5873  System.out              com.dspread.mdm.service              I  [okhttp]:check permission begin!
2025-08-21 21:02:02.242  5840-5873  System.out              com.dspread.mdm.service              I  [okhttp]:not MMS!
2025-08-21 21:02:02.242  5840-5873  System.out              com.dspread.mdm.service              I  [okhttp]:not Email!
2025-08-21 21:02:02.243  5840-5873  System.out              com.dspread.mdm.service              I  [OkHttp] sendRequest>>
2025-08-21 21:02:02.244  5840-5873  System.out              com.dspread.mdm.service              I  [OkHttp] sendRequest<<
2025-08-21 21:02:02.531  5840-5873  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader HTTP响应码: 200
2025-08-21 21:02:02.534  5840-5873  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 服务器返回Content-Length: 4771451
2025-08-21 21:02:02.536  5840-5873  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 完整下载模式
2025-08-21 21:02:02.539  5840-5873  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 完整下载，使用服务器返回大小: 4771451
2025-08-21 21:02:02.541  5840-5873  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader localFile = true /sdcard/Android/data/com.dspread.mdm.service/files/config/bootanimation.zip, 追加模式: false
2025-08-21 21:02:51.990  5840-5873  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 计算MD5: bootanimation.zip
2025-08-21 21:02:52.087  5840-5873  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 文件: 4771451 MD5: ba1ee533924eae5c408465e7cddcbda4
2025-08-21 21:02:52.089  5840-5873  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader MD5校验正确
2025-08-21 21:02:52.092  5840-5873  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 清理连接资源
2025-08-21 21:02:52.094  5840-5873  Provisioning            com.dspread.mdm.service              I  ℹ️ BootAnimation 下载完成: /sdcard/Android/data/com.dspread.mdm.service/files/config/bootanimation.zip
2025-08-21 21:02:52.097  5840-5873  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] Provisioning进度: 正在应用配置...
2025-08-21 21:02:52.099  5840-5873  Provisioning            com.dspread.mdm.service              I  ℹ️ 配置应用完成
2025-08-21 21:02:52.101  5840-5873  Provisioning            com.dspread.mdm.service              I  ℹ️ 远程配置成功，标记首次配置完成
2025-08-21 21:02:52.105  5840-5873  Provisioning            com.dspread.mdm.service              D  🔧 状态标志保存成功: /sdcard/Android/data/com.dspread.mdm.service/files/config/provisioning_flags.json
2025-08-21 21:02:52.107  5840-5873  Provisioning            com.dspread.mdm.service              I  ℹ️ ProvisioningManager: 初始启动标记完成，网络重连重试功能已激活
2025-08-21 21:02:52.110  5840-5873  Provisioning            com.dspread.mdm.service              I  ℹ️ Provisioning配置执行完成
2025-08-21 21:02:52.112  5840-5873  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] Provisioning完成，启动核心组件
2025-08-21 21:02:52.127  5840-5840  WebSocket               com.dspread.mdm.service              I  🔧 开始初始化 WebSocket 组件...
2025-08-21 21:02:52.133  5840-5840  WebSocket               com.dspread.mdm.service              I  🔧 初始化 WebSocket 密钥管理器
2025-08-21 21:02:52.139  5840-5840  WebSocket               com.dspread.mdm.service              I  🔧 生成客户端公私钥对
2025-08-21 21:02:52.247  5840-5840  Common                  com.dspread.mdm.service              I  ✅ WebSocket 密钥管理器初始化完成
2025-08-21 21:02:52.255  5840-5840  WebSocket               com.dspread.mdm.service              I  🔧 开始初始化 WsMessageCenter...
2025-08-21 21:02:52.257  5840-5840  WebSocket               com.dspread.mdm.service              I  🔧 WsMessageCenter 首次初始化
2025-08-21 21:02:52.277  5840-5840  WebSocket               com.dspread.mdm.service              I  🔧 WsTaskManager 初始化完成
2025-08-21 21:02:52.388  5840-5840  WebSocket               com.dspread.mdm.service              I  🔧 WsMessageCenter 初始化完成
2025-08-21 21:02:52.390  5840-5840  WebSocket               com.dspread.mdm.service              I  🔧 开始清理终态任务，当前任务数量: 0
2025-08-21 21:02:52.393  5840-5840  WebSocket               com.dspread.mdm.service              I  🔧 没有需要清理的终态任务
2025-08-21 21:02:52.395  5840-5840  WebSocket               com.dspread.mdm.service              I  🔧 开始重置任务状态，当前任务个数: 0
2025-08-21 21:02:52.397  5840-5840  WebSocket               com.dspread.mdm.service              I  🔧 没有需要重置的任务
2025-08-21 21:02:52.400  5840-5840  Common                  com.dspread.mdm.service              I  ✅ 任务状态恢复完成
2025-08-21 21:02:52.406  5840-5840  WebSocket               com.dspread.mdm.service              I  🔧 使用Provisioning配置的WebSocket URL: wss://api.dspreadserv.net/status/websocket/register
2025-08-21 21:02:52.417  5840-5840  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接管理器初始化完成
2025-08-21 21:02:52.419  5840-5840  Common                  com.dspread.mdm.service              I  ✅ WebSocket 管理器初始化完成
2025-08-21 21:02:52.422  5840-5840  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 管理器初始化完成
2025-08-21 21:02:52.425  5840-5840  Common                  com.dspread.mdm.service              I  ✅ WebSocket 组件初始化完成
2025-08-21 21:02:52.428  5840-5840  WebSocket               com.dspread.mdm.service              I  🔧 启动 WebSocket 连接...
2025-08-21 21:02:52.430  5840-5840  WebSocket               com.dspread.mdm.service              I  🔧 开始连接 WebSocket...
2025-08-21 21:02:52.443  5840-5840  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接到: wss://api.dspreadserv.net/status/websocket/register?serialNo=MDE2MTAwNDAyMDIzMDcwNjAyMjc=&publicKey=TUlHZk1BMEdDU3FHU0liM0RRRUJBUVVBQTRHTkFEQ0JpUUtCZ1FDTHV3NUdEZG5tTExvaXFNWjVJU3NwcUpXZGRVSWw2Z1dlTnpHMTR0MmtJY3VKZEg2QnZJcjQvSm1ud3Myc1puK0lqQkRtTE1JaHlXZ2E4QWt3S2toY2hzVUoydGtlVW5zbUVJRVF1Rm16NUlmVHA4cWNsL0l1K3NjYzZPU3Q5cE1rMUNZWXJTZlo4YmJzc0U3ZmN0UzRWWXd3cTRZQWVnUm5MeGZib2pWdTh3SURBUUFC&query=1&msgVer=3&timestamp=1755781372434&signature=UOYPVYp734PNzsuQsXwgCq1IJOHjaMVGM/JtzbHKluzfSkOO8NoEdrZLRYDKFflpiTFXG63qjvKpPKV0a/dl5/DBN3i60XZE7RyAq+StDhTM0tp+92/rpf0G2BhYVy2JKaxraLLy1tNS+FJhMgp+R7i/paGZNktODLHtsQ+PlZE=
2025-08-21 21:02:52.448  5840-5840  WebSocket               com.dspread.mdm.service              I  🔧 未检测到系统代理设置
2025-08-21 21:02:52.473  5840-5840  WebSocket               com.dspread.mdm.service              I  🔧 ===== WebSocket 库默认配置 =====
2025-08-21 21:02:52.475  5840-5840  WebSocket               com.dspread.mdm.service              I  🔧 默认 PING 间隔: 30000ms (30秒)
2025-08-21 21:02:52.477  5840-5840  WebSocket               com.dspread.mdm.service              I  🔧 默认 PONG 间隔: 0ms (0秒)
2025-08-21 21:02:52.480  5840-5840  WebSocket               com.dspread.mdm.service              I  🔧 帧队列大小: 5
2025-08-21 21:02:52.482  5840-5840  WebSocket               com.dspread.mdm.service              I  🔧 连接超时: 15000ms (15秒)
2025-08-21 21:02:52.484  5840-5840  WebSocket               com.dspread.mdm.service              I  🔧 允许缺失关闭帧: false
2025-08-21 21:02:52.486  5840-5840  WebSocket               com.dspread.mdm.service              I  🔧 ================================
2025-08-21 21:02:52.489  5840-5840  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接请求已发送
2025-08-21 21:02:52.491  5840-5840  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接启动完成
2025-08-21 21:02:52.494  5840-5840  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 开始初始化广播管理器...
2025-08-21 21:02:52.499  5840-5840  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 开始初始化...
2025-08-21 21:02:52.506  5840-5840  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 包更新广播接收器注册完成
2025-08-21 21:02:52.507  5840-5897  System.out              com.dspread.mdm.service              I  [socket]:check permission begin!
2025-08-21 21:02:52.509  5840-5840  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 统一广播接收器注册完成
2025-08-21 21:02:52.511  5840-5840  Common                  com.dspread.mdm.service              I  ✅ BroadcastManager 初始化完成
2025-08-21 21:02:52.529  5840-5840  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 广播管理器初始化完成
2025-08-21 21:02:52.531  5840-5840  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 开始启动所有定时器...
2025-08-21 21:02:52.533  5840-5840  Provisioning            com.dspread.mdm.service              D  🔧 ProvisioningEventHandler 启动Provisioning定时器: 300s (0小时)
2025-08-21 21:02:52.539  5840-5840  Common                  com.dspread.mdm.service              I  ✅ ⏰ 设置初始化定时器成功，下次执行: 300秒后 (0小时)
2025-08-21 21:02:52.541  5840-5840  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] Provisioning定时器启动成功
2025-08-21 21:02:52.545  5840-5840  Common                  com.dspread.mdm.service              I  ✅ ⏰ 设置心跳定时器成功，下次执行: 60秒后 (1分钟)
2025-08-21 21:02:52.547  5840-5840  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 心跳定时器启动成功
2025-08-21 21:02:52.551  5840-5840  Common                  com.dspread.mdm.service              I  ✅ ⏰ 设置任务执行定时器成功，下次执行: 60秒后 (1分钟)
2025-08-21 21:02:52.553  5840-5840  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 任务执行定时器启动成功
2025-08-21 21:02:52.557  5840-5840  Common                  com.dspread.mdm.service              I  ✅ ⏰ 设置终端信息定时器成功，下次执行: 120秒后 (2分钟)
2025-08-21 21:02:52.559  5840-5840  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 终端信息上传定时器启动成功
2025-08-21 21:02:52.561  5840-5840  Common                  com.dspread.mdm.service              D  🔧 TimerConfig ========== 当前定时器配置 (调试模式) ==========
2025-08-21 21:02:52.562  5840-5840  Common                  com.dspread.mdm.service              D  🔧 TimerConfig ①心跳定时器: 60秒
2025-08-21 21:02:52.565  5840-5840  Common                  com.dspread.mdm.service              D  🔧 TimerConfig ②终端信息上传定时器: 120秒
2025-08-21 21:02:52.567  5840-5840  Common                  com.dspread.mdm.service              D  🔧 TimerConfig ③任务执行定时器: 60秒
2025-08-21 21:02:52.570  5840-5840  Common                  com.dspread.mdm.service              D  🔧 TimerConfig ④服务守护定时器: 120秒
2025-08-21 21:02:52.572  5840-5840  Common                  com.dspread.mdm.service              D  🔧 TimerConfig ⑤初始化定时器: 300秒
2025-08-21 21:02:52.574  5840-5840  Common                  com.dspread.mdm.service              D  🔧 TimerConfig ===============================================
2025-08-21 21:02:52.575  5840-5840  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 所有定时器启动完成
2025-08-21 21:02:52.579  5840-5840  Common                  com.dspread.mdm.service              D  🔧 [ModuleManagerRegistry] 模块管理器注册中心初始化完成
2025-08-21 21:02:52.582  5840-5840  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 核心组件初始化完成
2025-08-21 21:02:52.582  5840-5873  Common                  com.dspread.mdm.service              D  🔧 [ModuleManagerRegistry] 启动所有模块
2025-08-21 21:02:52.587  5840-5840  Platform                com.dspread.mdm.service              D  🔧 NetworkApi 单例实例已创建
2025-08-21 21:02:52.590  5840-5840  TetheringManager        com.dspread.mdm.service              I  registerTetheringEventCallback:com.dspread.mdm.service
2025-08-21 21:02:52.592  5840-5873  Common                  com.dspread.mdm.service              D  🔧 Module LogStream Manager status changed to: INITIALIZED
2025-08-21 21:02:52.596  5840-5873  Common                  com.dspread.mdm.service              D  🔧 Module LogStream Manager status changed to: STARTING
2025-08-21 21:02:52.601  5840-5840  Receiver                com.dspread.mdm.service              D  🔧 网络状态变化: 连接=true, 类型=WIFI
2025-08-21 21:02:52.604  5840-5840  Receiver                com.dspread.mdm.service              D  🔧 网络恢复，尝试重新连接WebSocket
2025-08-21 21:02:52.606  5840-5840  Receiver                com.dspread.mdm.service              D  🔧 ServiceManagementEventHandler 处理网络变化事件
2025-08-21 21:02:52.612  5840-5840  Common                  com.dspread.mdm.service              D  🔧 ServiceStartupManager 服务正在运行，无需启动: NETWORK_CHANGE
2025-08-21 21:02:52.615  5840-5840  Receiver                com.dspread.mdm.service              D  🔧 ServiceManagementEventHandler 网络变化后服务检查完成
2025-08-21 21:02:52.618  5840-5840  Provisioning            com.dspread.mdm.service              D  🔧 ProvisioningEventHandler 网络状态变化
2025-08-21 21:02:52.625  5840-5840  Provisioning            com.dspread.mdm.service              I  ℹ️ ProvisioningEventHandler 检测到网络重连，但Provisioning已完成，按正常定时器间隔检查更新
2025-08-21 21:02:52.628  5840-5840  Receiver                com.dspread.mdm.service              D  🔧 电池状态变化: 电量=100%, 温度=26°C, 充电=true
2025-08-21 21:02:52.635  5840-5840  WebSocket               com.dspread.mdm.service              I  🔧 C0902 被动式上送: battery_level_change_5 (被动: 1)
2025-08-21 21:02:52.655  5840-5840  WebSocket               com.dspread.mdm.service              I  🔧 消息已缓存: C0902 (缓存数量: 1)
2025-08-21 21:02:52.657  5840-5840  WebSocket               com.dspread.mdm.service              I  🔧 C0902 电池状态上送成功 (1, trigger=battery_level_change_5)
2025-08-21 21:02:52.678  5840-5873  Common                  com.dspread.mdm.service              D  🔧 Module LogStream Manager status changed to: RUNNING
2025-08-21 21:02:52.680  5840-5873  Common                  com.dspread.mdm.service              D  🔧 [ModuleManagerRegistry] LogStreamManager启动成功
2025-08-21 21:02:52.704  5840-5873  Common                  com.dspread.mdm.service              D  🔧 Module Geofence Manager status changed to: INITIALIZED
2025-08-21 21:02:52.709  5840-5873  Common                  com.dspread.mdm.service              D  🔧 Module Geofence Manager status changed to: STARTING
2025-08-21 21:02:52.728  5840-5873  Platform                com.dspread.mdm.service              D  🔧 用户交互监控已启动（简化版本）
2025-08-21 21:02:52.729  5840-5840  Platform                com.dspread.mdm.service              D  🔧 [WiFiManagerApi] WiFiManagerApi 单例实例已创建
2025-08-21 21:02:52.730  5840-5873  Platform                com.dspread.mdm.service              D  🔧 使用模拟用户交互检测
2025-08-21 21:02:52.732  5840-5873  Platform                com.dspread.mdm.service              D  🔧 用户交互监控已启动
2025-08-21 21:02:52.735  5840-5873  Service                 com.dspread.mdm.service              D  🔧 [GeofenceManager]Constants.geofenceStatus = 1
2025-08-21 21:02:52.737  5840-5873  Service                 com.dspread.mdm.service              D  🔧 [GeofenceManager]-- Constants.storeId =  -- Constants.storeSsid =  -- Constants.storeIp = 
2025-08-21 21:02:52.740  5840-5873  Common                  com.dspread.mdm.service              D  🔧 Module Geofence Manager status changed to: RUNNING
2025-08-21 21:02:52.743  5840-5873  Common                  com.dspread.mdm.service              D  🔧 [ModuleManagerRegistry] GeofenceManager启动成功
2025-08-21 21:02:52.756  5840-5898  System.out              com.dspread.mdm.service              I  [socket]:check permission begin!
2025-08-21 21:02:52.756  5840-5873  RuleBase                com.dspread.mdm.service              D  🔧 RuleBaseManager 开始初始化RuleBaseManager...
2025-08-21 21:02:52.762  5840-5873  RuleBase                com.dspread.mdm.service              D  🔧 RuleBaseStorage RuleBase存储管理器初始化完成
2025-08-21 21:02:52.764  5840-5873  RuleBase                com.dspread.mdm.service              D  🔧 RuleStateMonitor 状态监控器初始化完成
2025-08-21 21:02:52.766  5840-5873  RuleBase                com.dspread.mdm.service              D  🔧 RuleBaseManager RuleBaseManager初始化完成
2025-08-21 21:02:52.767  5840-5873  Common                  com.dspread.mdm.service              D  🔧 [ModuleManagerRegistry] RuleBaseManager启动成功
2025-08-21 21:02:52.769  5840-5873  Common                  com.dspread.mdm.service              D  🔧 [ModuleManagerRegistry] 所有模块启动完成
2025-08-21 21:02:54.867  5840-5907  Common                  com.dspread.mdm.service              I  ✅ WebSocket 物理连接成功，等待服务器 hellMsg
2025-08-21 21:02:54.870  5840-5907  WebSocket               com.dspread.mdm.service              I  🔧 ===== 心跳发送器实际配置 =====
2025-08-21 21:02:54.874  5840-5907  WebSocket               com.dspread.mdm.service              I  🔧 实际 PING 发送间隔: 30000ms (30秒)
2025-08-21 21:02:54.877  5840-5907  WebSocket               com.dspread.mdm.service              I  🔧 PING 定时器名称: PingSender
2025-08-21 21:02:54.880  5840-5907  WebSocket               com.dspread.mdm.service              I  🔧 实际 PONG 发送间隔: 0ms (0秒)
2025-08-21 21:02:54.882  5840-5907  WebSocket               com.dspread.mdm.service              I  🔧 PONG 定时器名称: PongSender
2025-08-21 21:02:54.885  5840-5907  WebSocket               com.dspread.mdm.service              I  🔧 ===============================
2025-08-21 21:02:54.887  5840-5907  WebSocket               com.dspread.mdm.service              I  🔧 启动PING/PONG检测，阈值: 连续3个PING无PONG响应
2025-08-21 21:02:54.910  5840-5907  Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"msg_state":"P","request_time":"1755580536662","data":{"taskList":[{"beginDate":"2024-08-19 05:15:36","taskType":"02","apkName":"Via","endDate":"9999-12-31 23:59:59","appId":"1755580536662","packName":"mark.via","versionName":"6.2.0","taskId":"1755580536662","versionCode":"20250117"}]},"tranCode":"ST001","request_id":"1755580536662ST001","version":"1","serialNo":"01610040202307060227"}
2025-08-21 21:02:54.913  5840-5907  WebSocket               com.dspread.mdm.service              I  🔧 准备发送响应确认 - requestId: 1755580536662ST001, needResponse: true
2025-08-21 21:02:54.919  5840-5907  WebSocket               com.dspread.mdm.service              I  🔧 非重要消息，不缓存: C0000
2025-08-21 21:02:54.925  5840-5907  WebSocket               com.dspread.mdm.service              I  🔧 非重要消息，不缓存: C0000
2025-08-21 21:02:54.926  5840-5907  WebSocket               com.dspread.mdm.service              I  🔧 已发送C0000响应确认: requestId=1755580536662ST001
2025-08-21 21:02:54.929  5840-5907  Task                    com.dspread.mdm.service              D  🔧 处理任务消息: ST001
2025-08-21 21:02:54.931  5840-5907  Task                    com.dspread.mdm.service              I  ℹ️ 更新任务列表: requestId=1755580536662ST001, 任务数量=1
2025-08-21 21:02:54.932  5840-5907  Task                    com.dspread.mdm.service              D  🔧 任务插入到末尾: taskId=1755580536662
2025-08-21 21:02:54.934  5840-5907  Task                    com.dspread.mdm.service              D  🔧 添加/更新任务: taskId=1755580536662
2025-08-21 21:02:54.937  5840-5907  Task                    com.dspread.mdm.service              D  🔧 保存任务列表到存储: 1 个
2025-08-21 21:02:54.939  5840-5907  Task                    com.dspread.mdm.service              D  🔧 开始处理任务列表，共 1 个任务
2025-08-21 21:02:54.941  5840-5907  Task                    com.dspread.mdm.service              D  🔧 处理任务: taskId=1755580536662, type=02, package=mark.via, apk=Via
2025-08-21 21:02:54.943  5840-5907  Task                    com.dspread.mdm.service              D  🔧 处理卸载任务: 1755580536662
2025-08-21 21:02:54.950  5840-5907  Task                    com.dspread.mdm.service              D  🔧 更新任务状态: taskId=1755580536662, state=D02
2025-08-21 21:02:54.953  5840-5907  Task                    com.dspread.mdm.service              D  🔧 保存任务列表到存储: 1 个
2025-08-21 21:02:54.956  5840-5907  Task                    com.dspread.mdm.service              D  🔧 保存任务列表到存储: 1 个
2025-08-21 21:02:54.958  5840-5907  WebSocket               com.dspread.mdm.service              I  🔧 ST001 主动式上送: task_failed (主动: 2)
2025-08-21 21:02:54.966  5840-5907  WebSocket               com.dspread.mdm.service              I  🔧 消息已缓存: C0108 (缓存数量: 2)
2025-08-21 21:02:54.968  5840-5907  WebSocket               com.dspread.mdm.service              I  🔧 任务结果实时上送: taskId=1755580536662, result=D02 (1)
2025-08-21 21:02:54.983  5840-5907  Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"msg_state":"P","request_time":"1755580524415","data":{"taskList":[{"beginDate":"2024-08-19 05:15:24","taskType":"02","apkName":"OpenVPN for Android","endDate":"9999-12-31 23:59:59","appId":"1755580524415","packName":"de.blinkt.openvpn","versionName":"0.5.36a","taskId":"1755580524415","versionCode":"65"}]},"tranCode":"ST001","request_id":"1755580524415ST001","version":"1","serialNo":"01610040202307060227"}
2025-08-21 21:02:54.985  5840-5907  WebSocket               com.dspread.mdm.service              I  🔧 准备发送响应确认 - requestId: 1755580524415ST001, needResponse: true
2025-08-21 21:02:54.990  5840-5907  WebSocket               com.dspread.mdm.service              I  🔧 非重要消息，不缓存: C0000
2025-08-21 21:02:54.995  5840-5907  WebSocket               com.dspread.mdm.service              I  🔧 非重要消息，不缓存: C0000
2025-08-21 21:02:54.996  5840-5907  WebSocket               com.dspread.mdm.service              I  🔧 已发送C0000响应确认: requestId=1755580524415ST001
2025-08-21 21:02:54.998  5840-5907  Task                    com.dspread.mdm.service              D  🔧 处理任务消息: ST001
2025-08-21 21:02:55.000  5840-5907  Task                    com.dspread.mdm.service              I  ℹ️ 更新任务列表: requestId=1755580524415ST001, 任务数量=1
2025-08-21 21:02:55.002  5840-5907  Task                    com.dspread.mdm.service              D  🔧 任务按时间插入到位置: 0, taskId=1755580524415
2025-08-21 21:02:55.004  5840-5907  Task                    com.dspread.mdm.service              D  🔧 添加/更新任务: taskId=1755580524415
2025-08-21 21:02:55.006  5840-5907  Task                    com.dspread.mdm.service              D  🔧 发现已完成任务: taskId=1755580536662, result=D02
2025-08-21 21:02:55.007  5840-5907  Task                    com.dspread.mdm.service              D  🔧 上送任务结果: taskId=1755580536662, result=D02
2025-08-21 21:02:55.009  5840-5907  Task                    com.dspread.mdm.service              D  🔧 清理已完成任务: taskId=1755580536662, result=D02
2025-08-21 21:02:55.010  5840-5907  Task                    com.dspread.mdm.service              D  🔧 清理了 1 个已完成任务
2025-08-21 21:02:55.013  5840-5907  Task                    com.dspread.mdm.service              D  🔧 保存任务列表到存储: 1 个
2025-08-21 21:02:55.014  5840-5907  Task                    com.dspread.mdm.service              D  🔧 开始处理任务列表，共 1 个任务
2025-08-21 21:02:55.016  5840-5907  Task                    com.dspread.mdm.service              D  🔧 处理任务: taskId=1755580524415, type=02, package=de.blinkt.openvpn, apk=OpenVPN for Android
2025-08-21 21:02:55.017  5840-5907  Task                    com.dspread.mdm.service              D  🔧 处理卸载任务: 1755580524415
2025-08-21 21:02:55.020  5840-5907  Task                    com.dspread.mdm.service              D  🔧 更新任务状态: taskId=1755580524415, state=D02
2025-08-21 21:02:55.022  5840-5907  Task                    com.dspread.mdm.service              D  🔧 保存任务列表到存储: 1 个
2025-08-21 21:02:55.025  5840-5907  Task                    com.dspread.mdm.service              D  🔧 保存任务列表到存储: 1 个
2025-08-21 21:02:55.027  5840-5907  WebSocket               com.dspread.mdm.service              I  🔧 ST001 主动式上送: task_failed (主动: 3)
2025-08-21 21:02:55.035  5840-5907  WebSocket               com.dspread.mdm.service              I  🔧 消息已缓存: C0108 (缓存数量: 3)
2025-08-21 21:02:55.036  5840-5907  WebSocket               com.dspread.mdm.service              I  🔧 任务结果实时上送: taskId=1755580524415, result=D02 (1)
2025-08-21 21:02:55.046  5840-5907  Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"hellMsg":"hello 01610040202307060227","tranCode":"S0000","version":"1","rebootTime":"01:16:36","serialNo":"01610040202307060227","deviceStatus":"6"}
2025-08-21 21:02:55.048  5840-5907  Common                  com.dspread.mdm.service              I  ✅ 收到服务器 hellMsg，WebSocket 业务连接成功！
2025-08-21 21:02:55.050  5840-5907  Common                  com.dspread.mdm.service              I  ✅ WebSocket 业务连接成功！
2025-08-21 21:02:55.051  5840-5907  WebSocket               com.dspread.mdm.service              I  🔧 业务连接成功，重连计数已重置为 0
2025-08-21 21:02:55.055  5840-5907  Platform                com.dspread.mdm.service              D  🔧 WakeLockManager 获取WiFi锁成功，防止WiFi休眠
2025-08-21 21:02:55.057  5840-5907  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接成功，检查自身更新任务状态
2025-08-21 21:02:55.060  5840-5907  Receiver                com.dspread.mdm.service              D  🔧 发送广播: com.dspread.mdm.service.CHECK_SELF_UPDATE_STATUS
2025-08-21 21:02:55.061  5840-5907  WebSocket               com.dspread.mdm.service              I  🔧 开始发送缓存消息，数量: 3
2025-08-21 21:02:55.067  5840-5907  WebSocket               com.dspread.mdm.service              I  🔧 C0902 主动式上送: cached_message_resend (主动: 4)
2025-08-21 21:02:55.074  5840-5907  Common                  com.dspread.mdm.service              I  ✅ 缓存消息发送成功: C0902
2025-08-21 21:02:55.576  5840-5907  WebSocket               com.dspread.mdm.service              I  🔧 C0108 主动式上送: cached_message_resend (主动: 5)
2025-08-21 21:02:55.584  5840-5907  Common                  com.dspread.mdm.service              I  ✅ 缓存消息发送成功: C0108
2025-08-21 21:02:56.088  5840-5907  WebSocket               com.dspread.mdm.service              I  🔧 C0108 主动式上送: cached_message_resend (主动: 6)
2025-08-21 21:02:56.096  5840-5907  Common                  com.dspread.mdm.service              I  ✅ 缓存消息发送成功: C0108
2025-08-21 21:02:56.600  5840-5907  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接成功
2025-08-21 21:02:56.603  5840-5907  WebSocket               com.dspread.mdm.service              I  🔧 设备状态: 6
2025-08-21 21:02:56.606  5840-5907  WebSocket               com.dspread.mdm.service              I  🔧 检查首次连接标志: isFirstTimeWebSocketConnected=true
2025-08-21 21:02:56.608  5840-5907  WebSocket               com.dspread.mdm.service              I  🔧 首次WebSocket连接成功，发送初始化信息并执行初始化检查
2025-08-21 21:02:56.610  5840-5907  WebSocket               com.dspread.mdm.service              I  🔧 开始发送初始化信息（带流量控制）
2025-08-21 21:02:56.613  5840-5907  WebSocket               com.dspread.mdm.service              I  🔧 C0901 被动式上送: first_connection (被动: 2)
2025-08-21 21:02:56.618  5840-5907  Platform                com.dspread.mdm.service              D  🔧 重新收集应用信息
2025-08-21 21:02:56.698  5840-5907  AppManager              com.dspread.mdm.service              I  ℹ️ 应用信息获取完成: 总数120(系统119/用户1) 返回1个
2025-08-21 21:02:56.703  5840-5907  Platform                com.dspread.mdm.service              D  🔧 应用信息: 1 个应用
2025-08-21 21:02:56.713  5840-5907  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0901","serialNo":"01610040202307060227","request_time":"1755781376706","request_id":"1755781376706C0901","version":"1","data":{"apkInfo":[{"packName":"com.dspread.mdm.service","apkName":"service","versionCode":22,"versionName":"1.1.03.20250821.DSPREAD.MDM.SERVICE","updateDate":"2025-08-21 21:01:06"}],"sytemInfo":{"androidVersion":"11","buildNumber":"D60_Baseline_debug_20250708_ota_test","aspl":"2021-12-05"},"serviceInfo":[]},"myVersionName":"1.1.03.20250821.DSPREAD.MDM.SERVICE","terminalDate":"20250821210256"}
2025-08-21 21:02:56.715  5840-5907  WebSocket               com.dspread.mdm.service              I  🔧 C0901 应用信息上送成功 (1)
2025-08-21 21:02:57.717  5840-5907  WebSocket               com.dspread.mdm.service              I  🔧 C0902 被动式上送: first_connection (被动: 3)
2025-08-21 21:02:57.735  5840-5907  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0902","serialNo":"01610040202307060227","request_time":"1755781377726","request_id":"1755781377726C0902","version":"1","data":{"batteryLife":100,"batteryHealth":2,"temprature":"26.6","isCharging":"1","isLowBattery":"0","powerOff":"0","outOfBattery":"0"},"myVersionName":"1.1.03.20250821.DSPREAD.MDM.SERVICE","terminalDate":"20250821210257"}
2025-08-21 21:02:57.737  5840-5907  WebSocket               com.dspread.mdm.service              I  🔧 C0902 电池状态上送成功 (1, trigger=first_connection)
2025-08-21 21:02:58.740  5840-5907  WebSocket               com.dspread.mdm.service              I  🔧 C0903 被动式上送: first_connection (被动: 4)
2025-08-21 21:02:58.889  5840-5907  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0903","serialNo":"01610040202307060227","request_time":"1755781378880","request_id":"1755781378880C0903","version":"1","data":{"hardwareInfo":[{"name":"CPU Usage","desc":"cpu:0.50"},{"name":"storage_usage_status","desc":" 5.47GB \/ 32.00GB ","state":"1"},{"name":"total_memory","desc":" 0.54GB \/ 1.86GB ","state":"1"},{"name":"androidVersion","desc":"11","state":"1"},{"name":"network_status","desc":"wifi","state":"1"},{"name":"OSVer","desc":"11","state":"1"}],"locationInfo":{"gps":{"longitude":"-999.0","latitude":"-999.0","satelliteCount":0,"satelliteCountInUsed":0,"satelliteAverSNR":"0.00","distance":"0.0","lockStatus":"1","lockMeter":"200","proId":"","proName":""},"baseSite":{"MCC":"","MNC":"","CID":"-1","LAC":"-1","DBM":"-999","IMSI":"","ICCID":""},"wifi":{"SSID":"fubox_2.4G","IP":"**************","MAC":"52:C3:63:13:8C:EE","SSTH":"-25","BSSID":"7c:10:c9:d6:9f:10"},"address":"无法获取"}},"myVersionName":"1.1.03.20250821.DSPREAD.MDM.SERVICE","terminalDate":"20250821210258"}
2025-08-21 21:02:58.891  5840-5907  WebSocket               com.dspread.mdm.service              I  🔧 C0903 数据信息上送成功 (1)
2025-08-21 21:02:59.894  5840-5907  WebSocket               com.dspread.mdm.service              I  🔧 C0904 被动式上送: first_connection (被动: 5)
2025-08-21 21:03:00.003  5840-5907  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0904","serialNo":"01610040202307060227","request_time":"1755781379988","request_id":"1755781379988C0904","version":"1","data":{"wifiOption":[{"SSID":"fubox_2.4G","SSTH":"-28"},{"SSID":"2206-5G","SSTH":"-32"},{"SSID":"fubox_5G","SSTH":"-40"},{"SSID":"2206","SSTH":"-41"},{"SSID":"2205_5G","SSTH":"-51"},{"SSID":"2205","SSTH":"-53"},{"SSID":"@Ruijie-1816","SSTH":"-56"},{"SSID":"2207","SSTH":"-67"},{"SSID":"DIRECT-C6-Mi All-in-One Inkjet","SSTH":"-70"},{"SSID":"DIRECT-CC-HP Laser 1188w","SSTH":"-71"},{"SSID":"2306","SSTH":"-72"},{"SSID":"2106","SSTH":"-72"},{"SSID":"2103","SSTH":"-77"},{"SSID":"2207-5G","SSTH":"-80"},{"SSID":"诺富特酒店2208","SSTH":"-81"},{"SSID":"dingjie888","SSTH":"-82"},{"SSID":"FJQS","SSTH":"-83"},{"SSID":"ChinaNet-SnSC","SSTH":"-85"},{"SSID":"2405","SSTH":"-86"},{"SSID":"2103_5G","SSTH":"-86"},{"SSID":"1621","SSTH":"-87"},{"SSID":"HUAWEI_B311_福","SSTH":"-92"},{"SSID":"2106-5G","SSTH":"-94"}],"baseSite":{"MCC":"","MNC":"","CID":"-1","LAC":"-1","DBM":"-999","IMSI":"","ICCID":""},"wifi":{"SSID":"fubox_2.4G","IP":"**************","MAC":"52:C3:63:13:8C:EE","SSTH":"-25","BSSID":"7c:10:c9:d6:9f:10"}},"myVersionName":"1.1.03.20250821.DSPREAD.MDM.SERVICE","terminalDate":"20250821210259"}
2025-08-21 21:03:00.006  5840-5907  WebSocket               com.dspread.mdm.service              I  🔧 C0904 网络状态上送成功 (1)
2025-08-21 21:03:00.058  5840-5856  System                  com.dspread.mdm.service              W  A resource failed to call close. 
2025-08-21 21:03:01.009  5840-5907  WebSocket               com.dspread.mdm.service              I  🔧 C0906 被动式上送: first_connection (被动: 6)
2025-08-21 21:03:01.016  5840-5907  Platform                com.dspread.mdm.service              W  ⚠️ SpVersionApi SP版本信息格式异常，未找到SP_VERSION标记
2025-08-21 21:03:01.019  5840-5907  Platform                com.dspread.mdm.service              D  🔧 SpVersionApi 从Settings获取SP版本: STD 582.1.238.8
2025-08-21 21:03:01.035  5840-5907  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0906","serialNo":"01610040202307060227","request_time":"1755781381026","request_id":"1755781381026C0906","version":"1","data":{"firmWareInfo":{"spfw":"STD 582.1.238.8"},"imei_1":"014250620000709","imei_2":"","wifi_mac":"52:c3:63:13:8c:ee","bt_mac":"","bsn":""},"myVersionName":"1.1.03.20250821.DSPREAD.MDM.SERVICE","terminalDate":"20250821210301"}
2025-08-21 21:03:01.038  5840-5907  WebSocket               com.dspread.mdm.service              I  🔧 C0906 设备信息上送成功 (1)
2025-08-21 21:03:01.040  5840-5907  WebSocket               com.dspread.mdm.service              I  🔧 C0109 被动式上送: first_connection (被动: 7)
2025-08-21 21:03:01.048  5840-5907  Platform                com.dspread.mdm.service              D  🔧 重新收集应用信息（强制刷新）
2025-08-21 21:03:01.110  5840-5907  AppManager              com.dspread.mdm.service              I  ℹ️ 应用信息获取完成: 总数120(系统119/用户1) 返回1个
2025-08-21 21:03:01.114  5840-5907  Platform                com.dspread.mdm.service              D  🔧 应用信息: 1 个应用
2025-08-21 21:03:01.212  5840-5907  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0109","serialNo":"01610040202307060227","request_time":"1755781381196","request_id":"1755781381196C0109","version":"1","data":{"apkInfo":[{"packName":"com.dspread.mdm.service","apkName":"service","versionCode":22,"versionName":"1.1.03.20250821.DSPREAD.MDM.SERVICE","updateDate":"2025-08-21 21:01:06"}],"locationInfo":{"gps":{"longitude":"-999.0","latitude":"-999.0","satelliteCount":0,"satelliteCountInUsed":0,"satelliteAverSNR":"0.00","distance":"0.0","lockStatus":"1","lockMeter":"200","proId":"","proName":""},"baseSite":{"MCC":"","MNC":"","CID":"-1","LAC":"-1","DBM":"-999","IMSI":"","ICCID":""},"wifi":{"SSID":"fubox_2.4G","IP":"**************","MAC":"52:C3:63:13:8C:EE","SSTH":"-25","BSSID":"7c:10:c9:d6:9f:10"},"address":"无法获取"},"hardwareInfo":[{"name":"CPU Usage","desc":"cpu:0.50"},{"name":"storage_usage_status","desc":" 5.47GB \/ 32.00GB ","state":"1"},{"name":"total_memory","desc":" 0.54GB \/ 1.86GB ","state":"1"},{"name":"androidVersion","desc":"11","state":"1"},{"name":"network_status","desc":"wifi","state":"1"},{"name":"OSVer","desc":"11","state":"1"}],"wifiOption":[{"SSID":"fubox_2.4G","SSTH":"-28"},{"SSID":"2206-5G","SSTH":"-32"},{"SSID":"fubox_5G","SSTH":"-40"},{"SSID":"2206","SSTH":"-41"},{"SSID":"2205_5G","SSTH":"-51"},{"SSID":"2205","SSTH":"-53"},{"SSID":"@Ruijie-1816","SSTH":"-56"},{"SSID":"2207","SSTH":"-67"},{"SSID":"DIRECT-C6-Mi All-in-One Inkjet","SSTH":"-70"},{"SSID":"DIRECT-CC-HP Laser 1188w","SSTH":"-71"},{"SSID":"2306","SSTH":"-72"},{"SSID":"2106","SSTH":"-72"},{"SSID":"2103","SSTH":"-77"},{"SSID":"2207-5G","SSTH":"-80"},{"SSID":"诺富特酒店2208","SSTH":"-81"},{"SSID":"dingjie888","SSTH":"-82"},{"SSID":"FJQS","SSTH":"-83"},{"SSID":"ChinaNet-SnSC","SSTH":"-85"},{"SSID":"2405","SSTH":"-86"},{"SSID":"2103_5G","SSTH":"-86"},{"SSID":"1621","SSTH":"-87"},{"SSID":"HUAWEI_B311_福","SSTH":"-92"},{"SSID":"2106-5G","SSTH":"-94"}],"sytemInfo":{"androidVersion":"11","buildNumber":"D60_Baseline_debug_20250708_ota_test","aspl":"2021-12-05"}},"myVersionName":"1.1.03.20250821.DSPREAD.MDM.SERVICE","terminalDate":"20250821210301"}
2025-08-21 21:03:01.215  5840-5907  WebSocket               com.dspread.mdm.service              I  🔧 C0109 终端信息上传完成 (1, trigger=first_connection)
2025-08-21 21:03:01.217  5840-5907  Common                  com.dspread.mdm.service              I  ✅ 初始化信息发送完成
2025-08-21 21:03:01.220  5840-5907  WebSocket               com.dspread.mdm.service              I  🔧 检查服务更新结果
2025-08-21 21:03:01.222  5840-5907  Task                    com.dspread.mdm.service              I  ℹ️ 本地任务列表长度: 1
2025-08-21 21:03:01.224  5840-5907  Task                    com.dspread.mdm.service              D  🔧 本地任务列表内容: [{"beginDate":"2024-08-19 05:15:24","taskType":"02","apkName":"OpenVPN for Android","endDate":"9999-12-31 23:59:59","appId":"1755580524415","packName":"de.blinkt.openvpn","versionName":"0.5.36a","taskId":"1755580524415","versionCode":"65","request_id":"1755580524415ST001","request_time":"1755580524415","silent_install":"","taskResult":"D02","lastUpdateTime":1755781375024}]
2025-08-21 21:03:01.226  5840-5907  Task                    com.dspread.mdm.service              I  ℹ️ 当前应用版本: versionCode=22, versionName=1.1.03.20250821.DSPREAD.MDM.SERVICE
2025-08-21 21:03:01.228  5840-5907  Task                    com.dspread.mdm.service              I  ℹ️ 任务列表数量: 1
2025-08-21 21:03:01.230  5840-5907  Task                    com.dspread.mdm.service              D  🔧 检查任务[0]: taskId=1755580524415, taskType=02, pkgName=de.blinkt.openvpn, versionCode=65, versionName=0.5.36a, taskResult=D02
2025-08-21 21:03:01.232  5840-5907  WebSocket               com.dspread.mdm.service              I  🔧 检查OS升级状态...
2025-08-21 21:03:01.237  5840-5907  Common                  com.dspread.mdm.service              D  🔧 OsUpdateStatusChecker WebSocket连接成功，检查OS升级状态
2025-08-21 21:03:01.239  5840-5907  Common                  com.dspread.mdm.service              D  🔧 OsUpdateStatusChecker 没有待检查的OS升级任务
2025-08-21 21:03:01.241  5840-5907  WebSocket               com.dspread.mdm.service              I  🔧 OS升级状态检查已触发
2025-08-21 21:03:01.243  5840-5907  WebSocket               com.dspread.mdm.service              I  🔧 首次连接初始化检查完成
2025-08-21 21:03:01.253  5840-5907  Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"request_time":"1755781377052","org_request_time":"1755781374962","org_request_id":"1755781374962C0108","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755781377052S0000","serialNo":"01610040202307060227"}
2025-08-21 21:03:01.255  5840-5907  WebSocket               com.dspread.mdm.service              I  🔧 收到响应消息: transCode=S0000, orgRequestId=1755781374962C0108, state=0, remark=
2025-08-21 21:03:01.257  5840-5907  WebSocket               com.dspread.mdm.service              I  🔧 处理任务结果响应: state=0
2025-08-21 21:03:01.259  5840-5907  WebSocket               com.dspread.mdm.service              I  🔧 任务结果上传成功
2025-08-21 21:03:01.269  5840-5907  Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"request_time":"1755781377652","org_request_time":"1755781375030","org_request_id":"1755781375030C0108","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755781377652S0000","serialNo":"01610040202307060227"}
2025-08-21 21:03:01.271  5840-5907  WebSocket               com.dspread.mdm.service              I  🔧 收到响应消息: transCode=S0000, orgRequestId=1755781375030C0108, state=0, remark=
2025-08-21 21:03:01.273  5840-5907  WebSocket               com.dspread.mdm.service              I  🔧 处理任务结果响应: state=0
2025-08-21 21:03:01.274  5840-5907  WebSocket               com.dspread.mdm.service              I  🔧 任务结果上传成功
2025-08-21 21:03:01.624  5840-5907  Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"request_time":"1755781382504","org_request_time":"1755781381026","org_request_id":"1755781381026C0906","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755781382504S0000","serialNo":"01610040202307060227"}
2025-08-21 21:03:01.626  5840-5907  WebSocket               com.dspread.mdm.service              I  🔧 收到响应消息: transCode=S0000, orgRequestId=1755781381026C0906, state=0, remark=
2025-08-21 21:03:01.628  5840-5907  WebSocket               com.dspread.mdm.service              I  🔧 处理设备信息响应: state=0
2025-08-21 21:03:01.630  5840-5907  WebSocket               com.dspread.mdm.service              I  🔧 设备信息上传成功
2025-08-21 21:03:02.239  5840-5907  Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"request_time":"1755781383079","org_request_time":"1755781381196","org_request_id":"1755781381196C0109","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755781383079S0000","serialNo":"01610040202307060227"}
2025-08-21 21:03:02.242  5840-5907  WebSocket               com.dspread.mdm.service              I  🔧 收到响应消息: transCode=S0000, orgRequestId=1755781381196C0109, state=0, remark=
2025-08-21 21:03:02.245  5840-5907  WebSocket               com.dspread.mdm.service              I  🔧 终端信息上传成功
2025-08-21 21:03:07.619  5840-5840  Receiver                com.dspread.mdm.service              D  🔧 网络恢复，重新上传网络状态信息（防抖通过）
2025-08-21 21:03:07.626  5840-5840  WebSocket               com.dspread.mdm.service              I  🔧 C0904 被动式上送拒绝: NETWORK_RECOVERY (拒绝: 1)
2025-08-21 21:03:07.629  5840-5840  WebSocket               com.dspread.mdm.service              I  🔧 C0904 网络状态上送被流量控制阻止: 被动事件 'NETWORK_RECOVERY' 在平衡模式 - 重要变化下未启用
2025-08-21 21:03:24.870  5840-5908  WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 1 (第1个，待响应: 1)
2025-08-21 21:03:25.270  5840-5907  WebSocket               com.dspread.mdm.service              I  🔧 收到服务器 PONG 帧: 1 (待响应PING: 0)
2025-08-21 21:03:54.870  5840-5908  WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 2 (第2个，待响应: 1)
2025-08-21 21:03:55.478  5840-5907  WebSocket               com.dspread.mdm.service              I  🔧 收到服务器 PONG 帧: 2 (待响应PING: 0)
2025-08-21 21:04:24.871  5840-5908  WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 3 (第3个，待响应: 1)
2025-08-21 21:04:25.379  5840-5907  WebSocket               com.dspread.mdm.service              I  🔧 收到服务器 PONG 帧: 3 (待响应PING: 0)
2025-08-21 21:04:54.873  5840-5908  WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 4 (第4个，待响应: 1)
2025-08-21 21:04:55.485  5840-5907  WebSocket               com.dspread.mdm.service              I  🔧 收到服务器 PONG 帧: 4 (待响应PING: 0)
