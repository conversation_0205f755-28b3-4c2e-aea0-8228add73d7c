2025-08-18 17:01:50.922  5305-5305  Common                  com.dspread.mdm.service              D  🔧 日志配置：生产环境模式
2025-08-18 17:01:50.924  5305-5305  Common                  com.dspread.mdm.service              D  🔧 [DebugConfig] 定时器调试模式已禁用
2025-08-18 17:01:50.926  5305-5305  Common                  com.dspread.mdm.service              D  🔧 TimerConfig ========== 当前定时器配置 (生产模式) ==========
2025-08-18 17:01:50.930  5305-5305  ead.mdm.service         com.dspread.mdm.service              W  type=1400 audit(0.0:430): avc: denied { create } for name="pos" scontext=u:r:system_app:s0 tcontext=u:object_r:system_data_file:s0 tclass=dir permissive=0
2025-08-18 17:01:50.933  5305-5305  ead.mdm.service         com.dspread.mdm.service              W  type=1400 audit(0.0:431): avc: denied { create } for name="pos" scontext=u:r:system_app:s0 tcontext=u:object_r:system_data_file:s0 tclass=dir permissive=0
2025-08-18 17:01:50.936  5305-5305  Provisioning            com.dspread.mdm.service              W  ⚠️ 主目录 创建失败: /data/pos/config
2025-08-18 17:01:50.938  5305-5305  Provisioning            com.dspread.mdm.service              D  🔧 回退目录 已存在: /sdcard/Android/data/com.dspread.mdm.service/files/config
2025-08-18 17:01:50.939  5305-5305  Provisioning            com.dspread.mdm.service              W  ⚠️ 主目录不可用，使用回退配置目录: /sdcard/Android/data/com.dspread.mdm.service/files/config/
2025-08-18 17:01:50.939  5305-5305  Provisioning            com.dspread.mdm.service              I  ℹ️ 配置路径初始化完成: 外部存储目录
2025-08-18 17:01:50.948  5305-5305  Provisioning            com.dspread.mdm.service              D  🔧 状态标志加载成功: /sdcard/Android/data/com.dspread.mdm.service/files/config/provisioning_flags.json
2025-08-18 17:01:50.959  5305-5305  Common                  com.dspread.mdm.service              D  🔧 TimerConfig 使用Provisioning配置的心跳间隔: 300秒
2025-08-18 17:01:50.960  5305-5305  Common                  com.dspread.mdm.service              D  🔧 TimerConfig ①心跳定时器: 300秒
2025-08-18 17:01:50.963  5305-5305  Common                  com.dspread.mdm.service              D  🔧 TimerConfig 使用Provisioning配置的终端信息上传间隔: 900秒
2025-08-18 17:01:50.964  5305-5305  Common                  com.dspread.mdm.service              D  🔧 TimerConfig ②终端信息上传: 900秒
2025-08-18 17:01:50.965  5305-5305  Common                  com.dspread.mdm.service              D  🔧 TimerConfig ③任务执行: 60秒
2025-08-18 17:01:50.966  5305-5305  Common                  com.dspread.mdm.service              D  🔧 TimerConfig ④服务守护: 120秒
2025-08-18 17:01:50.967  5305-5305  Common                  com.dspread.mdm.service              D  🔧 TimerConfig ⑤Provisioning: 43200秒
2025-08-18 17:01:50.968  5305-5305  Common                  com.dspread.mdm.service              D  🔧 TimerConfig ===============================================
2025-08-18 17:01:50.969  5305-5305  Common                  com.dspread.mdm.service              D  🔧 日志配置：生产环境模式
2025-08-18 17:01:50.969  5305-5305  Common                  com.dspread.mdm.service              D  🔧 [DebugConfig] 调试模式已禁用
2025-08-18 17:01:50.970  5305-5305  Common                  com.dspread.mdm.service              D  🔧 [DebugConfig] ========== 当前日志配置 ==========
2025-08-18 17:01:51.051  5305-5305  Common                  com.dspread.mdm.service              D  🔧 [DebugConfig]             ========== 统一配置状态 (生产模式) ==========
                                                                                                    
                                                                                                                LogStream配置 (生产模式):
                                                                                                    启用状态: true
                                                                                                    单个日志文件: 5MB
                                                                                                    压缩文件总限制: 250MB
                                                                                                    原始日志总限制: 250MB
                                                                                                    Recent日志大小: 128KB
                                                                                                    上传URL: https://smartms-applog.s3.sa-east-1.amazonaws.com/log/
                                                                                                    
                                                                                                                Provisioning配置 (生产模式):
                                                                                                    配置目录: /data/pos/config/
                                                                                                    媒体目录: /data/pos/media/
                                                                                                    Logo目录: /data/pos/media/logo/
                                                                                                    开机动画目录: /data/pos/media/bootanimation/
                                                                                                    
                                                                                                                ========== 日志配置状态 ==========
                                                                                                    全局开关: true
                                                                                                    
                                                                                                    模块开关:
                                                                                                      Common: true
                                                                                                      HTTPS: true
                                                                                                      WebSocket: true
                                                                                                      API: true
                                                                                                      Service: true
                                                                                                      Provisioning: true
                                                                                                      Task: true
                                                                                                      RuleBase: true
                                                                                                      Application: true
                                                                                                      LogStream: false
                                                                                                      RemoteView: false
                                                                                                      Wi-Fi: false
                                                                                                      APN: false
                                                                                                      GeoFence: false
                                                                                                      Receiver: true
                                                                                                    ================================
                                                                                                                =========================================
2025-08-18 17:01:51.051  5305-5305  Common                  com.dspread.mdm.service              D  🔧 [DebugConfig] =====================================
2025-08-18 17:01:51.052  5305-5305  Common                  com.dspread.mdm.service              D  🔧 [DebugConfig] 统一配置管理器初始化完成，当前模式: 生产模式
2025-08-18 17:01:51.054  5305-5305  Common                  com.dspread.mdm.service              D  🔧 [DebugConfig] 定时器调试模式已禁用
2025-08-18 17:01:51.055  5305-5305  Common                  com.dspread.mdm.service              D  🔧 TimerConfig ========== 当前定时器配置 (生产模式) ==========
2025-08-18 17:01:51.060  5305-5305  Common                  com.dspread.mdm.service              D  🔧 TimerConfig 使用Provisioning配置的心跳间隔: 300秒
2025-08-18 17:01:51.061  5305-5305  Common                  com.dspread.mdm.service              D  🔧 TimerConfig ①心跳定时器: 300秒
2025-08-18 17:01:51.065  5305-5305  Common                  com.dspread.mdm.service              D  🔧 TimerConfig 使用Provisioning配置的终端信息上传间隔: 900秒
2025-08-18 17:01:51.066  5305-5305  Common                  com.dspread.mdm.service              D  🔧 TimerConfig ②终端信息上传: 900秒
2025-08-18 17:01:51.067  5305-5305  Common                  com.dspread.mdm.service              D  🔧 TimerConfig ③任务执行: 60秒
2025-08-18 17:01:51.068  5305-5305  Common                  com.dspread.mdm.service              D  🔧 TimerConfig ④服务守护: 120秒
2025-08-18 17:01:51.069  5305-5305  Common                  com.dspread.mdm.service              D  🔧 TimerConfig ⑤Provisioning: 43200秒
2025-08-18 17:01:51.069  5305-5305  Common                  com.dspread.mdm.service              D  🔧 TimerConfig ===============================================
2025-08-18 17:01:51.070  5305-5305  Common                  com.dspread.mdm.service              D  🔧 日志配置：生产环境模式
2025-08-18 17:01:51.071  5305-5305  Common                  com.dspread.mdm.service              D  🔧 [DebugConfig] 调试模式已禁用
2025-08-18 17:01:51.072  5305-5305  Common                  com.dspread.mdm.service              D  🔧 [DebugConfig] ========== 当前日志配置 ==========
2025-08-18 17:01:51.088  5305-5305  Common                  com.dspread.mdm.service              D  🔧 [DebugConfig]             ========== 统一配置状态 (生产模式) ==========
                                                                                                    
                                                                                                                LogStream配置 (生产模式):
                                                                                                    启用状态: true
                                                                                                    单个日志文件: 5MB
                                                                                                    压缩文件总限制: 250MB
                                                                                                    原始日志总限制: 250MB
                                                                                                    Recent日志大小: 128KB
                                                                                                    上传URL: https://smartms-applog.s3.sa-east-1.amazonaws.com/log/
                                                                                                    
                                                                                                                Provisioning配置 (生产模式):
                                                                                                    配置目录: /data/pos/config/
                                                                                                    媒体目录: /data/pos/media/
                                                                                                    Logo目录: /data/pos/media/logo/
                                                                                                    开机动画目录: /data/pos/media/bootanimation/
                                                                                                    
                                                                                                                ========== 日志配置状态 ==========
                                                                                                    全局开关: true
                                                                                                    
                                                                                                    模块开关:
                                                                                                      Common: true
                                                                                                      HTTPS: true
                                                                                                      WebSocket: true
                                                                                                      API: true
                                                                                                      Service: true
                                                                                                      Provisioning: true
                                                                                                      Task: true
                                                                                                      RuleBase: true
                                                                                                      Application: true
                                                                                                      LogStream: false
                                                                                                      RemoteView: false
                                                                                                      Wi-Fi: false
                                                                                                      APN: false
                                                                                                      GeoFence: false
                                                                                                      Receiver: true
                                                                                                    ================================
                                                                                                                =========================================
2025-08-18 17:01:51.089  5305-5305  Common                  com.dspread.mdm.service              D  🔧 [DebugConfig] =====================================
2025-08-18 17:01:51.090  5305-5305  Common                  com.dspread.mdm.service              D  🔧 SmartMdmServiceApp: 统一配置管理器初始化完成
2025-08-18 17:01:51.091  5305-5305  Common                  com.dspread.mdm.service              D  🔧 SmartMdmServiceApp: 开始初始化系统目录...
2025-08-18 17:01:51.087  5305-5305  ead.mdm.service         com.dspread.mdm.service              W  type=1400 audit(0.0:432): avc: denied { create } for name="pos" scontext=u:r:system_app:s0 tcontext=u:object_r:system_data_file:s0 tclass=dir permissive=0
2025-08-18 17:01:51.093  5305-5305  Common                  com.dspread.mdm.service              D  🔧 SmartMdmServiceApp: 配置目录创建 - 路径: /data/pos/config, 结果: false
2025-08-18 17:01:51.094  5305-5305  Common                  com.dspread.mdm.service              D  🔧 SmartMdmServiceApp: Logo目录已存在 - /data/data/com.dspread.mdm.service/files/media/logo
2025-08-18 17:01:51.095  5305-5305  Common                  com.dspread.mdm.service              D  🔧 SmartMdmServiceApp: 开机动画目录已存在 - /data/data/com.dspread.mdm.service/files/media/anim
2025-08-18 17:01:51.096  5305-5305  Common                  com.dspread.mdm.service              D  🔧 SmartMdmServiceApp: 系统目录初始化完成
2025-08-18 17:01:51.100  5305-5305  Platform                com.dspread.mdm.service              D  🔧 NetworkTrafficMonitor 初始化网络流量监控器
2025-08-18 17:01:51.105  5305-5305  Platform                com.dspread.mdm.service              D  🔧 NetworkTrafficMonitor 加载已保存的流量数据: 0B
2025-08-18 17:01:51.164  5305-5305  Platform                com.dspread.mdm.service              D  🔧 NetworkTrafficMonitor 每日上送任务已启动
2025-08-18 17:01:51.165  5305-5305  Platform                com.dspread.mdm.service              D  🔧 NetworkTrafficMonitor 网络流量监控器初始化完成
2025-08-18 17:01:51.169  5305-5305  Receiver                com.dspread.mdm.service              D  🔧 ServiceGuardEventHandler 启动服务守护定时器: 120s
2025-08-18 17:01:51.174  5305-5305  Common                  com.dspread.mdm.service              I  ✅ ⏰ 设置服务守护定时器成功，下次执行: 120秒后 (2分钟)
2025-08-18 17:01:51.174  5305-5305  Common                  com.dspread.mdm.service              D  🔧 SmartMdmServiceApp: 服务守护定时器启动成功
2025-08-18 17:01:51.177  5305-5305  Common                  com.dspread.mdm.service              D  🔧 ServiceStartupManager 收到服务启动请求: APPLICATION_STARTUP, 强制启动: false
2025-08-18 17:01:51.181  5305-5305  Common                  com.dspread.mdm.service              D  🔧 ServiceStartupManager 开始执行服务启动: APPLICATION_STARTUP
2025-08-18 17:01:51.182  5305-5305  Common                  com.dspread.mdm.service              D  🔧 ServiceManager: 准备启动后台服务
2025-08-18 17:01:51.184  5305-5305  ContextImpl             com.dspread.mdm.service              W  Calling a method in the system process without a qualified user: android.app.ContextImpl.startService:1571 android.content.ContextWrapper.startService:669 com.dspread.mdm.service.platform.manager.ServiceManager.startBackgroundService:20 com.dspread.mdm.service.platform.manager.ServiceStartupManager.performServiceStart:121 com.dspread.mdm.service.platform.manager.ServiceStartupManager.startService:66 
2025-08-18 17:01:51.187  5305-5305  Common                  com.dspread.mdm.service              D  🔧 ServiceManager: 后台服务启动成功，ComponentName: ComponentInfo{com.dspread.mdm.service/com.dspread.mdm.service.services.SmartMdmBackgroundService}
2025-08-18 17:01:52.190  5305-5305  Common                  com.dspread.mdm.service              D  🔧 ServiceStartupManager 服务启动验证成功: APPLICATION_STARTUP
2025-08-18 17:01:52.191  5305-5305  Common                  com.dspread.mdm.service              D  🔧 ServiceStartupManager 服务启动成功: APPLICATION_STARTUP
2025-08-18 17:01:52.192  5305-5305  Common                  com.dspread.mdm.service              D  🔧 SmartMdmServiceApp: 主服务启动请求已提交
2025-08-18 17:01:52.193  5305-5305  Common                  com.dspread.mdm.service              D  🔧 SmartMdmServiceApp: Application启动完成，包名: com.dspread.mdm.service
2025-08-18 17:01:52.312  5305-5305  SurfaceFactory          com.dspread.mdm.service              I  [static] sSurfaceFactory = com.mediatek.view.impl.SurfaceFactoryImpl@6a052c5
2025-08-18 17:01:52.323  5305-5305  ViewRootIm...tActivity] com.dspread.mdm.service              D  hardware acceleration = true , fakeHwAccelerated = false, sRendererDisabled = false, forceHwAccelerated = false, sSystemRendererDisabled = false
2025-08-18 17:01:52.330  5305-5305  InputTransport          com.dspread.mdm.service              I  Create ARC handle: 0x9231b770
2025-08-18 17:01:52.331  5305-5305  PhoneWindow             com.dspread.mdm.service              V  DecorView setVisiblity: visibility = 0, Parent = android.view.ViewRootImpl@6b85641, this = DecorView@e0ab7e6[TestActivity]
2025-08-18 17:01:52.333  5305-5305  Choreographer           com.dspread.mdm.service              I  Skipped 88 frames!  The application may be doing too much work on its main thread.
2025-08-18 17:01:52.381  5305-5348  GPUD                    com.dspread.mdm.service              I  @gpudInitialize: successfully initialized with GL, dbg=0 mmdump_dbg=0 mmpath_dbg=0
2025-08-18 17:01:52.387  5305-5348  GED                     com.dspread.mdm.service              E  Failed to get GED Log Buf, err(0)
2025-08-18 17:01:52.407  5305-5348  Surface                 com.dspread.mdm.service              D  Surface::connect(this=0x7ed76000,api=1)
2025-08-18 17:01:52.409  5305-5348  Surface                 com.dspread.mdm.service              D  Surface::setBufferCount(this=0x7ed76000,bufferCount=3)
2025-08-18 17:01:52.409  5305-5348  Surface                 com.dspread.mdm.service              D  Surface::allocateBuffers(this=0x7ed76000)
2025-08-18 17:01:52.420  5305-5348  Gralloc3                com.dspread.mdm.service              W  mapper 3.x is not supported
2025-08-18 17:01:52.423  5305-5348  ion                     com.dspread.mdm.service              E  ioctl c0044901 failed with code -1: Invalid argument
2025-08-18 17:01:52.449  5305-5348  OpenGLRenderer          com.dspread.mdm.service              I  Davey! duration=1582ms; Flags=1, IntendedVsync=21430871411654, Vsync=21432338078350, OldestInputEvent=9223372036854775807, NewestInputEvent=0, HandleInputStart=21432344888230, AnimationStart=21432344969846, PerformTraversalsStart=21432345356000, DrawStart=21432421433546, SyncQueued=21432429180085, SyncStart=21432435651316, IssueDrawCommandsStart=21432435761624, SwapBuffers=21432459300011, FrameCompleted=21432460452627, DequeueBufferDuration=0, QueueBufferDuration=576000, 
2025-08-18 17:01:52.463  5305-5305  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 服务启动
2025-08-18 17:01:52.478   961-984   NotificationService     system_server                        E  enqueueNotificationInternal pkg: com.dspread.mdm.service
2025-08-18 17:01:52.479  5305-5305  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 开始服务初始化
2025-08-18 17:01:52.479  5305-5305  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] Android < 14: 启动Provisioning服务
2025-08-18 17:01:52.482  5305-5305  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 服务创建完成
2025-08-18 17:01:52.483  5305-5353  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 启动ProvisioningService
2025-08-18 17:01:52.484  5305-5305  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] onStartCommand调用
2025-08-18 17:01:52.488  5305-5353  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] ProvisioningService启动完成
2025-08-18 17:01:52.488  5305-5344  Provisioning            com.dspread.mdm.service              I  ℹ️ 开始执行Provisioning配置，触发类型: FIRST_BOOT
2025-08-18 17:01:52.489  5305-5305  Platform                com.dspread.mdm.service              D  🔧 WakeLockManager 获取智能WakeLock成功，持续时间: 60000ms
2025-08-18 17:01:52.492  5305-5344  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] Provisioning进度: 正在下载配置文件...
2025-08-18 17:01:52.493  5305-5344  Provisioning            com.dspread.mdm.service              I  ℹ️ 开始请求远程配置...
2025-08-18 17:01:52.493  5305-5305  Platform                com.dspread.mdm.service              D  🔧 WakeLockManager 设置WakeLock续期定时器: 60000ms
2025-08-18 17:01:52.510  5305-5344  Provisioning            com.dspread.mdm.service              I  ℹ️ 请求配置: https://config.dspreadserv.net/status/config?SN=MDEyNDMwMzAyMDI0MTIwOTAxMDE=&timestamp=1755507712510
2025-08-18 17:01:52.510  5305-5305  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 初始化基础组件
2025-08-18 17:01:52.513  5305-5305  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 开始初始化...
2025-08-18 17:01:52.519  5305-5305  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 包更新广播接收器注册完成
2025-08-18 17:01:52.520  5305-5305  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 统一广播接收器注册完成
2025-08-18 17:01:52.522  5305-5305  Common                  com.dspread.mdm.service              I  ✅ BroadcastManager 初始化完成
2025-08-18 17:01:52.527  5305-5305  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: NetworkEventHandler -> android.net.conn.CONNECTIVITY_CHANGE
2025-08-18 17:01:52.527  5305-5305  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: NetworkEventHandler -> android.net.wifi.STATE_CHANGE
2025-08-18 17:01:52.529  5305-5305  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: BatteryEventHandler -> android.intent.action.BATTERY_CHANGED
2025-08-18 17:01:52.530  5305-5305  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: BatteryEventHandler -> android.intent.action.BATTERY_LOW
2025-08-18 17:01:52.531  5305-5344  NetworkSecurityConfig   com.dspread.mdm.service              D  Using Network Security Config from resource network_security_config debugBuild: true
2025-08-18 17:01:52.532  5305-5305  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: BatteryEventHandler -> android.intent.action.BATTERY_OKAY
2025-08-18 17:01:52.533  5305-5305  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: BatteryEventHandler -> android.intent.action.ACTION_POWER_CONNECTED
2025-08-18 17:01:52.534  5305-5305  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: BatteryEventHandler -> android.intent.action.ACTION_POWER_DISCONNECTED
2025-08-18 17:01:52.536  5305-5305  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: SystemEventHandler -> android.intent.action.BOOT_COMPLETED
2025-08-18 17:01:52.537  5305-5305  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: SystemEventHandler -> android.intent.action.QUICKBOOT_POWERON
2025-08-18 17:01:52.538  5305-5305  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: SystemEventHandler -> android.intent.action.LOCKED_BOOT_COMPLETED
2025-08-18 17:01:52.539  5305-5305  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: SystemEventHandler -> android.intent.action.ACTION_SHUTDOWN
2025-08-18 17:01:52.539  5305-5344  System.out              com.dspread.mdm.service              I  [okhttp]:check permission begin!
2025-08-18 17:01:52.540  5305-5305  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: SystemEventHandler -> android.intent.action.REBOOT
2025-08-18 17:01:52.540  5305-5344  System                  com.dspread.mdm.service              W  ClassLoader referenced unknown path: system/framework/mediatek-cta.jar
2025-08-18 17:01:52.541  5305-5305  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: ScreenEventHandler -> android.intent.action.SCREEN_ON
2025-08-18 17:01:52.543  5305-5305  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: ScreenEventHandler -> android.intent.action.SCREEN_OFF
2025-08-18 17:01:52.543  5305-5344  System.out              com.dspread.mdm.service              I  [okhttp] e:java.lang.ClassNotFoundException: com.mediatek.cta.CtaUtils
2025-08-18 17:01:52.546  5305-5305  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: HeartbeatEventHandler -> com.dspread.mdm.service.POLL_TIMER_START
2025-08-18 17:01:52.552  5305-5305  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: PackageUpdateEventHandler -> android.intent.action.PACKAGE_ADDED
2025-08-18 17:01:52.553  5305-5305  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: PackageUpdateEventHandler -> android.intent.action.PACKAGE_REMOVED
2025-08-18 17:01:52.554  5305-5305  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: PackageUpdateEventHandler -> android.intent.action.PACKAGE_REPLACED
2025-08-18 17:01:52.556  5305-5305  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: PackageUpdateEventHandler -> com.dspread.mdm.service.CHECK_SELF_UPDATE_STATUS
2025-08-18 17:01:52.560  5305-5305  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: TaskExecuteEventHandler -> com.dspread.mdm.service.WSTASK_EXEC_BC
2025-08-18 17:01:52.562  5305-5305  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: TerminalInfoEventHandler -> com.dspread.mdm.service.TER_INFO_UPLOAD_BC
2025-08-18 17:01:52.563  5305-5305  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: ServiceGuardEventHandler -> com.dspread.mdm.service.SERVICE_GUARD_TIMER
2025-08-18 17:01:52.565  5305-5305  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: ServiceManagementEventHandler -> android.intent.action.USER_PRESENT
2025-08-18 17:01:52.567  5305-5305  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: ServiceManagementEventHandler -> android.intent.action.TIME_TICK
2025-08-18 17:01:52.568  5305-5305  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: ServiceManagementEventHandler -> com.dspread.mdm.service.SERVICE_RESTART
2025-08-18 17:01:52.569  5305-5305  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: ServiceManagementEventHandler -> android.net.conn.CONNECTIVITY_CHANGE
2025-08-18 17:01:52.570  5305-5305  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: WakeLockEventHandler -> com.dspread.mdm.service.WAKELOCK_RENEWAL
2025-08-18 17:01:52.571  5305-5305  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: WakeLockEventHandler -> com.dspread.mdm.service.WAKELOCK_ACQUIRE
2025-08-18 17:01:52.574  5305-5305  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: WakeLockEventHandler -> com.dspread.mdm.service.WAKELOCK_RELEASE
2025-08-18 17:01:52.578  5305-5305  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: ProvisioningEventHandler -> com.dspread.mdm.service.PROVISIONING_TIMER
2025-08-18 17:01:52.579  5305-5305  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: ProvisioningEventHandler -> android.net.conn.CONNECTIVITY_CHANGE
2025-08-18 17:01:52.580  5305-5305  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: ProvisioningEventHandler -> android.intent.action.BOOT_COMPLETED
2025-08-18 17:01:52.621  5305-5305  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 服务初始化完成
2025-08-18 17:01:52.622  5305-5346  Platform                com.dspread.mdm.service              D  🔧 DeviceInfoApi 单例实例已创建
2025-08-18 17:01:52.631  5305-5305  Platform                com.dspread.mdm.service              D  🔧 NetworkApi 单例实例已创建
2025-08-18 17:01:52.634  5305-5305  Receiver                com.dspread.mdm.service              D  🔧 网络状态变化: 连接=true, 类型=WIFI
2025-08-18 17:01:52.636  5305-5346  Platform                com.dspread.mdm.service              W  ⚠️ SpVersionApi SP版本信息格式异常，未找到SP_VERSION标记
2025-08-18 17:01:52.637  5305-5346  Platform                com.dspread.mdm.service              D  🔧 SpVersionApi 从Settings获取SP版本: 588.30.241.59
2025-08-18 17:01:52.646  5305-5305  Receiver                com.dspread.mdm.service              D  🔧 网络恢复，尝试重新连接WebSocket
2025-08-18 17:01:52.647  5305-5305  Receiver                com.dspread.mdm.service              D  🔧 ServiceManagementEventHandler 处理网络变化事件
2025-08-18 17:01:52.651  5305-5305  Common                  com.dspread.mdm.service              D  🔧 ServiceStartupManager 服务正在运行，无需启动: NETWORK_CHANGE
2025-08-18 17:01:52.652  5305-5305  Receiver                com.dspread.mdm.service              D  🔧 ServiceManagementEventHandler 网络变化后服务检查完成
2025-08-18 17:01:52.653  5305-5305  Provisioning            com.dspread.mdm.service              D  🔧 ProvisioningEventHandler 网络状态变化
2025-08-18 17:01:52.654  5305-5305  Provisioning            com.dspread.mdm.service              D  🔧 ProvisioningEventHandler 忽略初始启动时的网络连接事件
2025-08-18 17:01:52.657  5305-5305  Receiver                com.dspread.mdm.service              D  🔧 电池状态更新: 电池状态: 电量=54%, 温度=35.2°C, 健康=2, 充电=是, 低电量=否, 耗尽=否
2025-08-18 17:01:52.658  5305-5305  Receiver                com.dspread.mdm.service              D  🔧 电池状态变化: 电量=54%, 温度=35°C, 充电=true
2025-08-18 17:01:52.683  5305-5305  WebSocket               com.dspread.mdm.service              I  🔧 C0902 被动式上送: battery_level_change_5 (被动: 1)
2025-08-18 17:01:52.701  5305-5305  WebSocket               com.dspread.mdm.service              I  🔧 消息已缓存: C0902 (缓存数量: 1)
2025-08-18 17:01:52.702  5305-5305  WebSocket               com.dspread.mdm.service              I  🔧 C0902 电池状态上送成功 (1, trigger=battery_level_change_5)
2025-08-18 17:01:52.749  5305-5305  WebSocket               com.dspread.mdm.service              I  🔧 C0905 主动式上送: manual_trigger (主动: 1)
2025-08-18 17:01:52.754  5305-5305  WebSocket               com.dspread.mdm.service              I  🔧 当日无流量数据，跳过上送
2025-08-18 17:01:52.756  5305-5305  WebSocket               com.dspread.mdm.service              I  🔧 没有流量数据需要上送
2025-08-18 17:01:52.757  5305-5305  Common                  com.dspread.mdm.service              D  🔧 刷新时自动上送流量统计
2025-08-18 17:01:52.795  5305-5344  System.out              com.dspread.mdm.service              I  [socket]:check permission begin!
2025-08-18 17:01:52.796  5305-5344  System                  com.dspread.mdm.service              W  ClassLoader referenced unknown path: system/framework/mediatek-cta.jar
2025-08-18 17:01:52.798  5305-5344  System.out              com.dspread.mdm.service              I  [socket] e:java.lang.ClassNotFoundException: com.mediatek.cta.CtaUtils
2025-08-18 17:01:53.634  5305-5305  Receiver                com.dspread.mdm.service              D  🔧 电池状态更新: 电池状态: 电量=54%, 温度=35.0°C, 健康=2, 充电=是, 低电量=否, 耗尽=否
2025-08-18 17:01:53.753  5305-5344  System.out              com.dspread.mdm.service              I  [OkHttp] sendRequest>>
2025-08-18 17:01:53.753  5305-5344  System.out              com.dspread.mdm.service              I  [OkHttp] sendRequest<<
2025-08-18 17:01:55.521  5305-5344  Provisioning            com.dspread.mdm.service              I  ℹ️ 远程配置获取成功
2025-08-18 17:01:55.522  5305-5344  Provisioning            com.dspread.mdm.service              I  ℹ️ 远程配置请求成功
2025-08-18 17:01:55.524  5305-5344  Provisioning            com.dspread.mdm.service              I  ℹ️ 远程配置解析成功 - CID: 10012001
2025-08-18 17:01:55.528  5305-5344  Provisioning            com.dspread.mdm.service              I  ℹ️ API响应已保存到本地: /sdcard/Android/data/com.dspread.mdm.service/files/config/provisioning_config.json, 具体内容为:
                                                                                                    {"mode":"dev","data":{"requestTime":"1755507720131","customization":{"system":{"timezone":"Asia\/Hong_Kong","logo":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/test\/logo.bin","logoMd5":"ba1ee533924eae5c408465e7cddcbda4","bootAnimationMd5":"ba1ee533924eae5c408465e7cddcbda4","gps":{"minUpdateTime":"30","scheduleTime":"60","maxLocateTime":"0","minDistance":"10","valid_distance":"500","care":"1"},"powerSaveMode":{"enable":"1","screenTimeout":"0"},"bootAnimation":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/test\/bootanimation.zip"},"polling":{"heartbeatTime":"300","terminalInfoTime":"900","statusApiUrl":"wss:\/\/api.dspreadserv.net\/status\/websocket\/register","remoteUrl":"wss:\/\/remote.dspreadserv.net\/remoteWSS\/websockify","uploadMode":"1","wssreconn":{"delayPolicy":"1","delaySwitch":"1","delayTime":"60"}}},"client":"default","cid":"10012001"},"function":"getProvisioningConfig","description":"SUCCESS","stateCode":"0","version":"v1.0.1.20250801"}
2025-08-18 17:01:55.531  5305-5344  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] Provisioning进度: 正在下载 Logo...
2025-08-18 17:01:55.587  5305-5344  Provisioning            com.dspread.mdm.service              I  ℹ️ 文件已存在且MD5匹配，跳过下载: Logo
2025-08-18 17:01:55.588  5305-5344  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] Provisioning进度: 正在下载 BootAnimation...
2025-08-18 17:01:55.641  5305-5344  Provisioning            com.dspread.mdm.service              I  ℹ️ 文件已存在且MD5匹配，跳过下载: BootAnimation
2025-08-18 17:01:55.642  5305-5344  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] Provisioning进度: 正在应用配置...
2025-08-18 17:01:55.643  5305-5344  Provisioning            com.dspread.mdm.service              I  ℹ️ 配置应用完成
2025-08-18 17:01:55.644  5305-5344  Provisioning            com.dspread.mdm.service              I  ℹ️ 远程配置成功，标记首次配置完成
2025-08-18 17:01:55.646  5305-5344  Provisioning            com.dspread.mdm.service              D  🔧 状态标志保存成功: /sdcard/Android/data/com.dspread.mdm.service/files/config/provisioning_flags.json
2025-08-18 17:01:55.647  5305-5344  Provisioning            com.dspread.mdm.service              I  ℹ️ Provisioning配置执行完成
2025-08-18 17:01:55.647  5305-5344  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] Provisioning完成，启动核心组件
2025-08-18 17:01:55.649  5305-5305  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 开始初始化核心组件
2025-08-18 17:01:55.656  5305-5305  WebSocket               com.dspread.mdm.service              I  🔧 开始初始化 WebSocket 组件...
2025-08-18 17:01:55.659  5305-5305  WebSocket               com.dspread.mdm.service              I  🔧 初始化 WebSocket 密钥管理器
2025-08-18 17:01:55.659  5305-5305  WebSocket               com.dspread.mdm.service              I  🔧 生成客户端公私钥对
2025-08-18 17:01:55.824  5305-5305  Common                  com.dspread.mdm.service              I  ✅ WebSocket 密钥管理器初始化完成
2025-08-18 17:01:55.831  5305-5305  WebSocket               com.dspread.mdm.service              I  🔧 开始初始化 WsMessageCenter...
2025-08-18 17:01:55.831  5305-5305  WebSocket               com.dspread.mdm.service              I  🔧 WsMessageCenter 首次初始化
2025-08-18 17:01:55.847  5305-5305  Task                    com.dspread.mdm.service              D  🔧 从存储加载任务: 0 个
2025-08-18 17:01:55.849  5305-5305  WebSocket               com.dspread.mdm.service              I  🔧 WsTaskManager 初始化完成
2025-08-18 17:01:55.951  5305-5305  WebSocket               com.dspread.mdm.service              I  🔧 WsMessageCenter 初始化完成
2025-08-18 17:01:55.952  5305-5305  WebSocket               com.dspread.mdm.service              I  🔧 开始清理终态任务，当前任务数量: 0
2025-08-18 17:01:55.952  5305-5305  WebSocket               com.dspread.mdm.service              I  🔧 没有需要清理的终态任务
2025-08-18 17:01:55.953  5305-5305  WebSocket               com.dspread.mdm.service              I  🔧 开始重置任务状态，当前任务个数: 0
2025-08-18 17:01:55.954  5305-5305  WebSocket               com.dspread.mdm.service              I  🔧 没有需要重置的任务
2025-08-18 17:01:55.954  5305-5305  Common                  com.dspread.mdm.service              I  ✅ 任务状态恢复完成
2025-08-18 17:01:55.960  5305-5305  WebSocket               com.dspread.mdm.service              I  🔧 使用Provisioning配置的WebSocket URL: wss://api.dspreadserv.net/status/websocket/register
2025-08-18 17:01:55.962  5305-5305  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接管理器初始化完成
2025-08-18 17:01:55.963  5305-5305  Common                  com.dspread.mdm.service              I  ✅ WebSocket 管理器初始化完成
2025-08-18 17:01:55.964  5305-5305  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 管理器初始化完成
2025-08-18 17:01:55.966  5305-5305  Common                  com.dspread.mdm.service              I  ✅ WebSocket 组件初始化完成
2025-08-18 17:01:55.967  5305-5305  WebSocket               com.dspread.mdm.service              I  🔧 启动 WebSocket 连接...
2025-08-18 17:01:55.968  5305-5305  WebSocket               com.dspread.mdm.service              I  🔧 开始连接 WebSocket...
2025-08-18 17:01:55.979  5305-5305  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接到: wss://api.dspreadserv.net/status/websocket/register?serialNo=MDEyNDMwMzAyMDI0MTIwOTAxMDE=&publicKey=TUlHZk1BMEdDU3FHU0liM0RRRUJBUVVBQTRHTkFEQ0JpUUtCZ1FDejNQb2hmNnFkZTRvYVY2blJhc21aK1pZWXQxZEFVSExqKzVITkhxUlRnUWRZeGdrRGNaSHcwdjlhYWI4OENpa2FwYmhNZTZabm0yWFJZU1dnV1N0ZnJGQzJNNU1oNHFwaCtCd1BBOElGdkpHeThCOUpzOVpHNkRqa2JDdG0xenNxVEJyMU9qRkdWWDBBSVBiZ1ROVFRSN0pzWW9rMk16cXFVK0NmeVNkN1F3SURBUUFC&query=1&msgVer=3&timestamp=1755507715969&signature=eGOeyvv8cUBYBhGw7Q/HI+GT+MpVkWvuCDpB8b6Lj/FCP47o+hbl0m9rr6i4+GY4W+qi0hKrcRHHJ8ETF7HAPKxcLNPNunKF5mNc/MlLs6xWapK3uAbP502cNMBT3Am6pbN+gS/ARdbOIPmkkIRTzfNCq8vT5RT/lzgXXeOq/lI=
2025-08-18 17:01:55.983  5305-5305  WebSocket               com.dspread.mdm.service              I  🔧 未检测到系统代理设置
2025-08-18 17:01:56.001  5305-5305  WebSocket               com.dspread.mdm.service              I  🔧 ===== WebSocket 库默认配置 =====
2025-08-18 17:01:56.002  5305-5305  WebSocket               com.dspread.mdm.service              I  🔧 默认 PING 间隔: 60000ms (60秒)
2025-08-18 17:01:56.003  5305-5305  WebSocket               com.dspread.mdm.service              I  🔧 默认 PONG 间隔: 0ms (0秒)
2025-08-18 17:01:56.004  5305-5305  WebSocket               com.dspread.mdm.service              I  🔧 帧队列大小: 5
2025-08-18 17:01:56.005  5305-5305  WebSocket               com.dspread.mdm.service              I  🔧 连接超时: 15000ms (15秒)
2025-08-18 17:01:56.006  5305-5305  WebSocket               com.dspread.mdm.service              I  🔧 允许缺失关闭帧: false
2025-08-18 17:01:56.006  5305-5305  WebSocket               com.dspread.mdm.service              I  🔧 ================================
2025-08-18 17:01:56.008  5305-5305  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接请求已发送
2025-08-18 17:01:56.009  5305-5305  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接启动完成
2025-08-18 17:01:56.013  5305-5305  Common                  com.dspread.mdm.service              D  🔧 [ModuleManagerRegistry] 模块管理器注册中心初始化完成
2025-08-18 17:01:56.025  5305-5305  RuleBase                com.dspread.mdm.service              D  🔧 RuleBaseManager 开始初始化RuleBaseManager...
2025-08-18 17:01:56.031  5305-5305  RuleBase                com.dspread.mdm.service              D  🔧 RuleBaseStorage RuleBase存储管理器初始化完成
2025-08-18 17:01:56.032  5305-5305  RuleBase                com.dspread.mdm.service              D  🔧 RuleStateMonitor 状态监控器初始化完成
2025-08-18 17:01:56.033  5305-5305  RuleBase                com.dspread.mdm.service              D  🔧 RuleBaseManager RuleBaseManager初始化完成
2025-08-18 17:01:56.034  5305-5305  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] RuleBaseManager初始化成功
2025-08-18 17:01:56.036  5305-5305  Provisioning            com.dspread.mdm.service              D  🔧 ProvisioningEventHandler 启动Provisioning定时器: 43200s (12小时)
2025-08-18 17:01:56.036  5305-5344  Common                  com.dspread.mdm.service              D  🔧 [ModuleManagerRegistry] 启动所有模块
2025-08-18 17:01:56.040  5305-5305  Common                  com.dspread.mdm.service              I  ✅ ⏰ 设置初始化定时器成功，下次执行: 43200秒后 (12小时)
2025-08-18 17:01:56.040  5305-5305  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] Provisioning定时器启动成功
2025-08-18 17:01:56.041  5305-5305  Common                  com.dspread.mdm.service              D  🔧 TimerConfig ========== 当前定时器配置 (生产模式) ==========
2025-08-18 17:01:56.044  5305-5344  Common                  com.dspread.mdm.service              D  🔧 [ModuleManagerRegistry] LogStreamManager实例创建完成
2025-08-18 17:01:56.045  5305-5305  Common                  com.dspread.mdm.service              D  🔧 TimerConfig 使用Provisioning配置的心跳间隔: 300秒
2025-08-18 17:01:56.045  5305-5344  Common                  com.dspread.mdm.service              D  🔧 Module LogStream Manager status changed to: INITIALIZED
2025-08-18 17:01:56.046  5305-5305  Common                  com.dspread.mdm.service              D  🔧 TimerConfig ①心跳定时器: 300秒
2025-08-18 17:01:56.049  5305-5305  Common                  com.dspread.mdm.service              D  🔧 TimerConfig 使用Provisioning配置的终端信息上传间隔: 900秒
2025-08-18 17:01:56.050  5305-5305  Common                  com.dspread.mdm.service              D  🔧 TimerConfig ②终端信息上传: 900秒
2025-08-18 17:01:56.051  5305-5305  Common                  com.dspread.mdm.service              D  🔧 TimerConfig ③任务执行: 60秒
2025-08-18 17:01:56.051  5305-5344  Common                  com.dspread.mdm.service              D  🔧 [ModuleManagerRegistry] LogStreamManager初始化成功
2025-08-18 17:01:56.052  5305-5305  Common                  com.dspread.mdm.service              D  🔧 TimerConfig ④服务守护: 120秒
2025-08-18 17:01:56.052  5305-5344  Common                  com.dspread.mdm.service              D  🔧 Module LogStream Manager status changed to: STARTING
2025-08-18 17:01:56.053  5305-5305  Common                  com.dspread.mdm.service              D  🔧 TimerConfig ⑤Provisioning: 43200秒
2025-08-18 17:01:56.053  5305-5305  Common                  com.dspread.mdm.service              D  🔧 TimerConfig ===============================================
2025-08-18 17:01:56.054  5305-5305  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 核心组件初始化完成
2025-08-18 17:01:56.097  5305-5344  Common                  com.dspread.mdm.service              D  🔧 Module LogStream Manager status changed to: RUNNING
2025-08-18 17:01:56.098  5305-5344  Common                  com.dspread.mdm.service              D  🔧 [ModuleManagerRegistry] LogStreamManager启动成功
2025-08-18 17:01:56.098  5305-5344  Common                  com.dspread.mdm.service              D  🔧 [ModuleManagerRegistry] LogStreamManager启动成功
2025-08-18 17:01:56.099  5305-5344  Common                  com.dspread.mdm.service              D  🔧 [ModuleManagerRegistry] 开始初始化地理围栏管理器
2025-08-18 17:01:56.121  5305-5344  Common                  com.dspread.mdm.service              D  🔧 Module Geofence Manager status changed to: INITIALIZED
2025-08-18 17:01:56.121  5305-5376  System.out              com.dspread.mdm.service              I  [socket]:check permission begin!
2025-08-18 17:01:56.131  5305-5344  Common                  com.dspread.mdm.service              D  🔧 Module Geofence Manager status changed to: STARTING
2025-08-18 17:01:56.150  5305-5344  Platform                com.dspread.mdm.service              D  🔧 用户交互监控已启动（简化版本）
2025-08-18 17:01:56.151  5305-5305  Platform                com.dspread.mdm.service              D  🔧 [WiFiManagerApi] WiFiManagerApi 单例实例已创建
2025-08-18 17:01:56.151  5305-5344  Platform                com.dspread.mdm.service              D  🔧 使用模拟用户交互检测
2025-08-18 17:01:56.152  5305-5344  Platform                com.dspread.mdm.service              D  🔧 用户交互监控已启动
2025-08-18 17:01:56.153  5305-5344  Service                 com.dspread.mdm.service              D  🔧 [GeofenceManager]Constants.geofenceStatus = 1
2025-08-18 17:01:56.154  5305-5344  Service                 com.dspread.mdm.service              D  🔧 [GeofenceManager]-- Constants.storeId =  -- Constants.storeSsid =  -- Constants.storeIp = 
2025-08-18 17:01:56.155  5305-5344  Common                  com.dspread.mdm.service              D  🔧 Module Geofence Manager status changed to: RUNNING
2025-08-18 17:01:56.157  5305-5344  Common                  com.dspread.mdm.service              D  🔧 [ModuleManagerRegistry] 地理围栏管理器初始化成功
2025-08-18 17:01:56.158  5305-5344  Common                  com.dspread.mdm.service              D  🔧 [ModuleManagerRegistry] GeofenceManager启动成功
2025-08-18 17:01:56.158  5305-5344  Common                  com.dspread.mdm.service              D  🔧 [ModuleManagerRegistry] 所有模块启动完成
2025-08-18 17:01:56.178  5305-5344  Platform                com.dspread.mdm.service              D  🔧 NetworkTrafficMonitor 等待下次上送时间: 2025-08-19 00:00:00
2025-08-18 17:01:56.375  5305-5377  System.out              com.dspread.mdm.service              I  [socket]:check permission begin!
2025-08-18 17:01:56.937  5305-5320  System                  com.dspread.mdm.service              W  A resource failed to call close. 
2025-08-18 17:01:57.674  5305-5379  Common                  com.dspread.mdm.service              I  ✅ WebSocket 物理连接成功，等待服务器 hellMsg
2025-08-18 17:01:57.675  5305-5379  WebSocket               com.dspread.mdm.service              I  🔧 ===== 心跳发送器实际配置 =====
2025-08-18 17:01:57.676  5305-5379  WebSocket               com.dspread.mdm.service              I  🔧 实际 PING 发送间隔: 60000ms (60秒)
2025-08-18 17:01:57.676  5305-5379  WebSocket               com.dspread.mdm.service              I  🔧 PING 定时器名称: PingSender
2025-08-18 17:01:57.677  5305-5379  WebSocket               com.dspread.mdm.service              I  🔧 实际 PONG 发送间隔: 0ms (0秒)
2025-08-18 17:01:57.678  5305-5379  WebSocket               com.dspread.mdm.service              I  🔧 PONG 定时器名称: PongSender
2025-08-18 17:01:57.679  5305-5379  WebSocket               com.dspread.mdm.service              I  🔧 ===============================
2025-08-18 17:01:57.700  5305-5379  Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"hellMsg":"hello 01243030202412090101","tranCode":"S0000","version":"1","rebootTime":"01:19:15","serialNo":"01243030202412090101","deviceStatus":"6"}
2025-08-18 17:01:57.701  5305-5379  Common                  com.dspread.mdm.service              I  ✅ 收到服务器 hellMsg，WebSocket 业务连接成功！
2025-08-18 17:01:57.702  5305-5379  Common                  com.dspread.mdm.service              I  ✅ WebSocket 业务连接成功！
2025-08-18 17:01:57.703  5305-5379  WebSocket               com.dspread.mdm.service              I  🔧 业务连接成功，重连计数已重置为 0
2025-08-18 17:01:57.706  5305-5379  Common                  com.dspread.mdm.service              I  ✅ ⏰ 设置任务执行定时器成功，下次执行: 60秒后 (1分钟)
2025-08-18 17:01:57.707  5305-5379  WebSocket               com.dspread.mdm.service              I  🔧 已启动任务执行定时器
2025-08-18 17:01:57.710  5305-5379  Common                  com.dspread.mdm.service              D  🔧 TimerConfig 使用Provisioning配置的心跳间隔: 300秒
2025-08-18 17:01:57.713  5305-5379  Common                  com.dspread.mdm.service              I  ✅ ⏰ 设置心跳定时器成功，下次执行: 300秒后 (5分钟)
2025-08-18 17:01:57.713  5305-5379  WebSocket               com.dspread.mdm.service              I  🔧 已启动心跳定时器
2025-08-18 17:01:57.716  5305-5379  Common                  com.dspread.mdm.service              D  🔧 TimerConfig 使用Provisioning配置的终端信息上传间隔: 900秒
2025-08-18 17:01:57.719  5305-5379  Common                  com.dspread.mdm.service              I  ✅ ⏰ 设置终端信息定时器成功，下次执行: 900秒后 (15分钟)
2025-08-18 17:01:57.720  5305-5379  WebSocket               com.dspread.mdm.service              I  🔧 已启动终端信息上传定时器
2025-08-18 17:01:57.721  5305-5379  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接成功，检查自身更新任务状态
2025-08-18 17:01:57.723  5305-5379  ContextImpl             com.dspread.mdm.service              W  Calling a method in the system process without a qualified user: android.app.ContextImpl.sendBroadcast:1046 android.content.ContextWrapper.sendBroadcast:448 com.dspread.mdm.service.broadcast.core.BroadcastSender.sendBroadcast:99 com.dspread.mdm.service.broadcast.core.BroadcastSender.sendBroadcast:23 com.dspread.mdm.service.network.websocket.connection.WsConnectionManager.checkSelfUpdateTaskStatus:311 
2025-08-18 17:01:57.724   961-1214  ActivityManager         system_server                        E  Sending non-protected broadcast com.dspread.mdm.service.CHECK_SELF_UPDATE_STATUS from system 5305:com.dspread.mdm.service/1000 pkg com.dspread.mdm.service (Ask Gemini)
                                                                                                    java.lang.Throwable
                                                                                                    	at com.android.server.am.ActivityManagerService.checkBroadcastFromSystem(ActivityManagerService.java:15078)
                                                                                                    	at com.android.server.am.ActivityManagerService.broadcastIntentLocked(ActivityManagerService.java:15625)
                                                                                                    	at com.android.server.am.ActivityManagerService.broadcastIntentLocked(ActivityManagerService.java:15095)
                                                                                                    	at com.android.server.am.ActivityManagerService.broadcastIntent(ActivityManagerService.java:15878)
                                                                                                    	at android.app.IActivityManager$Stub.onTransact(IActivityManager.java:2071)
                                                                                                    	at com.android.server.am.ActivityManagerService.onTransact(ActivityManagerService.java:2886)
                                                                                                    	at android.os.Binder.execTransactInternal(Binder.java:1021)
                                                                                                    	at android.os.Binder.execTransact(Binder.java:994)
2025-08-18 17:01:57.735  5305-5379  Receiver                com.dspread.mdm.service              D  🔧 发送广播: com.dspread.mdm.service.CHECK_SELF_UPDATE_STATUS
2025-08-18 17:01:57.736  5305-5379  WebSocket               com.dspread.mdm.service              I  🔧 开始发送缓存消息，数量: 1
2025-08-18 17:01:57.736  5305-5305  Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 收到自身更新状态检查广播: com.dspread.mdm.service.CHECK_SELF_UPDATE_STATUS
2025-08-18 17:01:57.737  5305-5305  Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 开始执行自身更新任务状态检查
2025-08-18 17:01:57.737  5305-5305  Task                    com.dspread.mdm.service              D  🔧 获取待执行任务数量: 0
2025-08-18 17:01:57.738  5305-5305  Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 待执行任务数量: 0
2025-08-18 17:01:57.738  5305-5379  WebSocket               com.dspread.mdm.service              I  🔧 C0902 主动式上送: cached_message_resend (主动: 2)
2025-08-18 17:01:57.744  5305-5379  Common                  com.dspread.mdm.service              I  ✅ 缓存消息发送成功: C0902
2025-08-18 17:01:57.831  5305-5382  ProfileInstaller        com.dspread.mdm.service              D  Installing profile for com.dspread.mdm.service
2025-08-18 17:01:58.245  5305-5379  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接成功
2025-08-18 17:01:58.246  5305-5379  WebSocket               com.dspread.mdm.service              I  🔧 设备状态: 6
2025-08-18 17:01:58.246  5305-5379  WebSocket               com.dspread.mdm.service              I  🔧 检查首次连接标志: isFirstTimeWebSocketConnected=true
2025-08-18 17:01:58.247  5305-5379  WebSocket               com.dspread.mdm.service              I  🔧 首次WebSocket连接成功，发送初始化信息并执行初始化检查
2025-08-18 17:01:58.248  5305-5379  WebSocket               com.dspread.mdm.service              I  🔧 开始发送初始化信息（带流量控制）
2025-08-18 17:01:58.248  5305-5379  WebSocket               com.dspread.mdm.service              I  🔧 C0901 被动式上送: first_connection (被动: 2)
2025-08-18 17:01:58.251  5305-5379  Platform                com.dspread.mdm.service              D  🔧 重新收集应用信息
2025-08-18 17:01:58.308  5305-5379  AppManager              com.dspread.mdm.service              I  ℹ️ 应用信息获取完成: 总数99(系统95/用户4) 返回4个
2025-08-18 17:01:58.315  5305-5379  Platform                com.dspread.mdm.service              D  🔧 应用信息: 4 个应用
2025-08-18 17:01:58.323  5305-5379  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0901","serialNo":"01243030202412090101","request_time":"1755507718316","request_id":"1755507718316C0901","version":"1","data":{"apkInfo":[{"packName":"de.blinkt.openvpn","apkName":"OpenVPN for Android","versionCode":65,"versionName":"0.5.36a","updateDate":"2025-08-18 16:23:34"},{"packName":"com.android.dreams.basic","apkName":"Basic Daydreams","versionCode":29,"versionName":"10","updateDate":"2009-01-01 08:00:00"},{"packName":"com.dspread.mdm.service","apkName":"service","versionCode":13,"versionName":"1.0.10.20250818.DSPREAD.MDM.SERVICE","updateDate":"2025-08-18 16:50:37"},{"packName":"com.dspread.new","apkName":"Smart-pos-demo","versionCode":3687,"versionName":"3.6.87","updateDate":"2009-01-01 08:00:00"}],"sytemInfo":{"androidVersion":"10","buildNumber":"D20Pro_Baseline_debug_20250708_ota_test","aspl":"2021-07-05"},"serviceInfo":[]},"myVersionName":"1.0.10.20250818.DSPREAD.MDM.SERVICE","terminalDate":"20250818170158"}
2025-08-18 17:01:58.324  5305-5379  WebSocket               com.dspread.mdm.service              I  🔧 C0901 应用信息上送成功 (1)
2025-08-18 17:01:59.325  5305-5379  WebSocket               com.dspread.mdm.service              I  🔧 C0902 被动式上送: first_connection (被动: 3)
2025-08-18 17:01:59.340  5305-5379  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0902","serialNo":"01243030202412090101","request_time":"1755507719332","request_id":"1755507719332C0902","version":"1","data":{"batteryLife":54,"batteryHealth":2,"temprature":"35.0","isCharging":"1","isLowBattery":"0","powerOff":"0","outOfBattery":"0"},"myVersionName":"1.0.10.20250818.DSPREAD.MDM.SERVICE","terminalDate":"20250818170159"}
2025-08-18 17:01:59.341  5305-5379  WebSocket               com.dspread.mdm.service              I  🔧 C0902 电池状态上送成功 (1, trigger=first_connection)
2025-08-18 17:02:00.343  5305-5379  WebSocket               com.dspread.mdm.service              I  🔧 C0903 被动式上送: first_connection (被动: 4)
2025-08-18 17:02:00.368  5305-5379  Platform                com.dspread.mdm.service              D  🔧 内存总容量为： 1.87GB 
2025-08-18 17:02:00.369  5305-5379  Platform                com.dspread.mdm.service              D  🔧 内存使用量为： 0.43GB 
2025-08-18 17:02:00.439  5305-5379  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0903","serialNo":"01243030202412090101","request_time":"1755507720430","request_id":"1755507720430C0903","version":"1","data":{"hardwareInfo":[{"name":"CPU Usage","desc":"cpu:0.50"},{"name":"storage_usage_status","desc":" 4.76GB \/ 8.00GB ","state":"1"},{"name":"total_memory","desc":" 0.43GB \/ 1.87GB ","state":"1"},{"name":"androidVersion","desc":"10","state":"1"},{"name":"network_status","desc":"wifi","state":"1"},{"name":"OSVer","desc":"10","state":"1"}],"locationInfo":{"gps":{"longitude":"-999.0","latitude":"-999.0","satelliteCount":0,"satelliteCountInUsed":0,"satelliteAverSNR":"0.00","distance":"0.0","lockStatus":"1","lockMeter":"200","proId":"","proName":""},"baseSite":{"MCC":"","MNC":"","CID":"-1","LAC":"-1","DBM":"-999","IMSI":"","ICCID":""},"wifi":{"SSID":"fubox_2.4G","IP":"*************","MAC":"D6:C4:A7:CA:EB:20","SSTH":"-31","BSSID":"7c:10:c9:d6:9f:10"},"address":"无法获取"}},"myVersionName":"1.0.10.20250818.DSPREAD.MDM.SERVICE","terminalDate":"20250818170200"}
2025-08-18 17:02:00.439  5305-5379  WebSocket               com.dspread.mdm.service              I  🔧 C0903 数据信息上送成功 (1)
2025-08-18 17:02:01.441  5305-5379  WebSocket               com.dspread.mdm.service              I  🔧 C0904 被动式上送: first_connection (被动: 5)
2025-08-18 17:02:01.514  5305-5379  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0904","serialNo":"01243030202412090101","request_time":"1755507721505","request_id":"1755507721505C0904","version":"1","data":{"wifiOption":[{"SSID":"2206","SSTH":"-27"},{"SSID":"fubox_2.4G","SSTH":"-31"},{"SSID":"2206-5G","SSTH":"-33"},{"SSID":"DIRECT-C6-Mi All-in-One Inkjet","SSTH":"-52"},{"SSID":"2205","SSTH":"-64"},{"SSID":"2106","SSTH":"-64"},{"SSID":"2103","SSTH":"-68"},{"SSID":"2205_5G","SSTH":"-69"},{"SSID":"2207","SSTH":"-69"},{"SSID":"CMCC-2203","SSTH":"-72"},{"SSID":"诺富特酒店2303","SSTH":"-78"},{"SSID":"2103_5G","SSTH":"-83"},{"SSID":"2207-5G","SSTH":"-87"},{"SSID":"ChinaNet-2116","SSTH":"-88"}],"baseSite":{"MCC":"","MNC":"","CID":"-1","LAC":"-1","DBM":"-999","IMSI":"","ICCID":""},"wifi":{"SSID":"fubox_2.4G","IP":"*************","MAC":"D6:C4:A7:CA:EB:20","SSTH":"-31","BSSID":"7c:10:c9:d6:9f:10"}},"myVersionName":"1.0.10.20250818.DSPREAD.MDM.SERVICE","terminalDate":"20250818170201"}
2025-08-18 17:02:01.514  5305-5379  WebSocket               com.dspread.mdm.service              I  🔧 C0904 网络状态上送成功 (1)
2025-08-18 17:02:02.516  5305-5379  WebSocket               com.dspread.mdm.service              I  🔧 C0906 被动式上送: first_connection (被动: 6)
2025-08-18 17:02:02.521  5305-5379  Platform                com.dspread.mdm.service              W  ⚠️ SpVersionApi SP版本信息格式异常，未找到SP_VERSION标记
2025-08-18 17:02:02.522  5305-5379  Platform                com.dspread.mdm.service              D  🔧 SpVersionApi 从Settings获取SP版本: 588.30.241.59
2025-08-18 17:02:02.536  5305-5379  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0906","serialNo":"01243030202412090101","request_time":"1755507722528","request_id":"1755507722528C0906","version":"1","data":{"firmWareInfo":{"spfw":"588.30.241.59"},"imei_1":"869096056734583","imei_2":"","wifi_mac":"d6:c4:a7:ca:eb:20","bt_mac":"","bsn":""},"myVersionName":"1.0.10.20250818.DSPREAD.MDM.SERVICE","terminalDate":"20250818170202"}
2025-08-18 17:02:02.538  5305-5379  WebSocket               com.dspread.mdm.service              I  🔧 C0906 设备信息上送成功 (1)
2025-08-18 17:02:02.539  5305-5379  WebSocket               com.dspread.mdm.service              I  🔧 C0109 被动式上送: first_connection (被动: 7)
2025-08-18 17:02:02.542  5305-5379  Platform                com.dspread.mdm.service              D  🔧 重新收集应用信息（强制刷新）
2025-08-18 17:02:02.578  5305-5379  AppManager              com.dspread.mdm.service              I  ℹ️ 应用信息获取完成: 总数99(系统95/用户4) 返回4个
2025-08-18 17:02:02.587  5305-5379  Platform                com.dspread.mdm.service              D  🔧 应用信息: 4 个应用
2025-08-18 17:02:02.644  5305-5379  Platform                com.dspread.mdm.service              D  🔧 内存总容量为： 1.87GB 
2025-08-18 17:02:02.645  5305-5379  Platform                com.dspread.mdm.service              D  🔧 内存使用量为： 0.43GB 
2025-08-18 17:02:02.668  5305-5379  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0109","serialNo":"01243030202412090101","request_time":"1755507722657","request_id":"1755507722657C0109","version":"1","data":{"apkInfo":[{"packName":"de.blinkt.openvpn","apkName":"OpenVPN for Android","versionCode":65,"versionName":"0.5.36a","updateDate":"2025-08-18 16:23:34"},{"packName":"com.android.dreams.basic","apkName":"Basic Daydreams","versionCode":29,"versionName":"10","updateDate":"2009-01-01 08:00:00"},{"packName":"com.dspread.mdm.service","apkName":"service","versionCode":13,"versionName":"1.0.10.20250818.DSPREAD.MDM.SERVICE","updateDate":"2025-08-18 16:50:37"},{"packName":"com.dspread.new","apkName":"Smart-pos-demo","versionCode":3687,"versionName":"3.6.87","updateDate":"2009-01-01 08:00:00"}],"locationInfo":{"gps":{"longitude":"-999.0","latitude":"-999.0","satelliteCount":0,"satelliteCountInUsed":0,"satelliteAverSNR":"0.00","distance":"0.0","lockStatus":"1","lockMeter":"200","proId":"","proName":""},"baseSite":{"MCC":"","MNC":"","CID":"-1","LAC":"-1","DBM":"-999","IMSI":"","ICCID":""},"wifi":{"SSID":"fubox_2.4G","IP":"*************","MAC":"D6:C4:A7:CA:EB:20","SSTH":"-31","BSSID":"7c:10:c9:d6:9f:10"},"address":"无法获取"},"hardwareInfo":[{"name":"CPU Usage","desc":"cpu:0.50"},{"name":"storage_usage_status","desc":" 4.76GB \/ 8.00GB ","state":"1"},{"name":"total_memory","desc":" 0.43GB \/ 1.87GB ","state":"1"},{"name":"androidVersion","desc":"10","state":"1"},{"name":"network_status","desc":"wifi","state":"1"},{"name":"OSVer","desc":"10","state":"1"}],"wifiOption":[{"SSID":"2206","SSTH":"-27"},{"SSID":"fubox_2.4G","SSTH":"-31"},{"SSID":"2206-5G","SSTH":"-33"},{"SSID":"DIRECT-C6-Mi All-in-One Inkjet","SSTH":"-52"},{"SSID":"2205","SSTH":"-64"},{"SSID":"2106","SSTH":"-64"},{"SSID":"2103","SSTH":"-68"},{"SSID":"2205_5G","SSTH":"-69"},{"SSID":"2207","SSTH":"-69"},{"SSID":"CMCC-2203","SSTH":"-72"},{"SSID":"诺富特酒店2303","SSTH":"-78"},{"SSID":"2103_5G","SSTH":"-83"},{"SSID":"2207-5G","SSTH":"-87"},{"SSID":"ChinaNet-2116","SSTH":"-88"}],"sytemInfo":{"androidVersion":"10","buildNumber":"D20Pro_Baseline_debug_20250708_ota_test","aspl":"2021-07-05"}},"myVersionName":"1.0.10.20250818.DSPREAD.MDM.SERVICE","terminalDate":"20250818170202"}
2025-08-18 17:02:02.669  5305-5379  WebSocket               com.dspread.mdm.service              I  🔧 C0109 终端信息上传完成 (1, trigger=first_connection)
2025-08-18 17:02:02.670  5305-5379  Common                  com.dspread.mdm.service              I  ✅ 初始化信息发送完成
2025-08-18 17:02:02.671  5305-5379  WebSocket               com.dspread.mdm.service              I  🔧 检查服务更新结果
2025-08-18 17:02:02.672  5305-5379  Task                    com.dspread.mdm.service              I  ℹ️ 本地任务列表长度: 0
2025-08-18 17:02:02.673  5305-5379  Task                    com.dspread.mdm.service              D  🔧 本地任务列表内容: []
2025-08-18 17:02:02.675  5305-5379  Task                    com.dspread.mdm.service              I  ℹ️ 当前应用版本: versionCode=13, versionName=1.0.10.20250818.DSPREAD.MDM.SERVICE
2025-08-18 17:02:02.675  5305-5379  Task                    com.dspread.mdm.service              I  ℹ️ 任务列表数量: 0
2025-08-18 17:02:02.676  5305-5379  WebSocket               com.dspread.mdm.service              I  🔧 检查OS升级状态...
2025-08-18 17:02:02.679  5305-5379  Common                  com.dspread.mdm.service              D  🔧 OsUpdateStatusChecker WebSocket连接成功，检查OS升级状态
2025-08-18 17:02:02.680  5305-5379  Common                  com.dspread.mdm.service              D  🔧 OsUpdateStatusChecker 没有待检查的OS升级任务
2025-08-18 17:02:02.681  5305-5379  WebSocket               com.dspread.mdm.service              I  🔧 OS升级状态检查已触发
2025-08-18 17:02:02.681  5305-5379  WebSocket               com.dspread.mdm.service              I  🔧 首次连接初始化检查完成
2025-08-18 17:02:03.019  5305-5379  Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"request_time":"1755507727564","org_request_time":"1755507722528","org_request_id":"1755507722528C0906","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755507727564S0000","serialNo":"01243030202412090101"}
2025-08-18 17:02:03.022  5305-5379  WebSocket               com.dspread.mdm.service              I  🔧 收到响应消息: transCode=S0000, orgRequestId=1755507722528C0906, state=0, remark=
2025-08-18 17:02:03.023  5305-5379  WebSocket               com.dspread.mdm.service              I  🔧 处理设备信息响应: state=0
2025-08-18 17:02:03.024  5305-5379  WebSocket               com.dspread.mdm.service              I  🔧 设备信息上传成功
2025-08-18 17:02:03.622  5305-5379  Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"request_time":"1755507727994","org_request_time":"1755507722657","org_request_id":"1755507722657C0109","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755507727994S0000","serialNo":"01243030202412090101"}
2025-08-18 17:02:03.623  5305-5379  WebSocket               com.dspread.mdm.service              I  🔧 收到响应消息: transCode=S0000, orgRequestId=1755507722657C0109, state=0, remark=
2025-08-18 17:02:03.624  5305-5379  WebSocket               com.dspread.mdm.service              I  🔧 终端信息上传成功
2025-08-18 17:02:03.626  5305-5379  WebSocket               com.dspread.mdm.service              I  🔧 终端信息上传成功标志已设置
2025-08-18 17:02:06.430  5305-5305  Receiver                com.dspread.mdm.service              D  🔧 电池状态更新: 电池状态: 电量=54%, 温度=35.0°C, 健康=2, 充电=是, 低电量=否, 耗尽=否
2025-08-18 17:02:07.649  5305-5305  Receiver                com.dspread.mdm.service              D  🔧 网络恢复，重新上传网络状态信息（防抖通过）
2025-08-18 17:02:07.654  5305-5305  WebSocket               com.dspread.mdm.service              I  🔧 C0904 被动式上送拒绝: NETWORK_RECOVERY (拒绝: 1)
2025-08-18 17:02:07.656  5305-5305  WebSocket               com.dspread.mdm.service              I  🔧 C0904 网络状态上送被流量控制阻止: 被动事件 'NETWORK_RECOVERY' 在平衡模式 - 重要变化下未启用
2025-08-18 17:02:22.493   961-1047  ActivityManager         system_server                        E  Sending non-protected broadcast com.dspread.mdm.service.WAKELOCK_RENEWAL from system uid 1000 pkg com.dspread.mdm.service (Ask Gemini)
                                                                                                    java.lang.Throwable
                                                                                                    	at com.android.server.am.ActivityManagerService.checkBroadcastFromSystem(ActivityManagerService.java:15082)
                                                                                                    	at com.android.server.am.ActivityManagerService.broadcastIntentLocked(ActivityManagerService.java:15719)
                                                                                                    	at com.android.server.am.ActivityManagerService.broadcastIntentInPackage(ActivityManagerService.java:15916)
                                                                                                    	at com.android.server.am.ActivityManagerService$LocalService.broadcastIntentInPackage(ActivityManagerService.java:18556)
                                                                                                    	at com.android.server.am.PendingIntentRecord.sendInner(PendingIntentRecord.java:432)
                                                                                                    	at com.android.server.am.PendingIntentRecord.sendWithResult(PendingIntentRecord.java:276)
                                                                                                    	at com.android.server.am.ActivityManagerService.sendIntentSender(ActivityManagerService.java:5633)
                                                                                                    	at android.app.PendingIntent.sendAndReturnResult(PendingIntent.java:896)
                                                                                                    	at android.app.PendingIntent.send(PendingIntent.java:878)
                                                                                                    	at com.android.server.AlarmManagerService$DeliveryTracker.deliverLocked(AlarmManagerService.java:4760)
                                                                                                    	at com.android.server.AlarmManagerService.deliverAlarmsLocked(AlarmManagerService.java:3899)
                                                                                                    	at com.android.server.AlarmManagerService$AlarmThread.run(AlarmManagerService.java:4160)
2025-08-18 17:02:23.746  5305-5305  Receiver                com.dspread.mdm.service              D  🔧 电池状态更新: 电池状态: 电量=55%, 温度=35.1°C, 健康=2, 充电=是, 低电量=否, 耗尽=否
2025-08-18 17:02:57.675  5305-5380  WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 1
2025-08-18 17:02:57.704   961-1047  ActivityManager         system_server                        E  Sending non-protected broadcast com.dspread.mdm.service.WSTASK_EXEC_BC from system uid 1000 pkg com.dspread.mdm.service (Ask Gemini)
                                                                                                    java.lang.Throwable
                                                                                                    	at com.android.server.am.ActivityManagerService.checkBroadcastFromSystem(ActivityManagerService.java:15082)
                                                                                                    	at com.android.server.am.ActivityManagerService.broadcastIntentLocked(ActivityManagerService.java:15719)
                                                                                                    	at com.android.server.am.ActivityManagerService.broadcastIntentInPackage(ActivityManagerService.java:15916)
                                                                                                    	at com.android.server.am.ActivityManagerService$LocalService.broadcastIntentInPackage(ActivityManagerService.java:18556)
                                                                                                    	at com.android.server.am.PendingIntentRecord.sendInner(PendingIntentRecord.java:432)
                                                                                                    	at com.android.server.am.PendingIntentRecord.sendWithResult(PendingIntentRecord.java:276)
                                                                                                    	at com.android.server.am.ActivityManagerService.sendIntentSender(ActivityManagerService.java:5633)
                                                                                                    	at android.app.PendingIntent.sendAndReturnResult(PendingIntent.java:896)
                                                                                                    	at android.app.PendingIntent.send(PendingIntent.java:878)
                                                                                                    	at com.android.server.AlarmManagerService$DeliveryTracker.deliverLocked(AlarmManagerService.java:4760)
                                                                                                    	at com.android.server.AlarmManagerService.deliverAlarmsLocked(AlarmManagerService.java:3899)
                                                                                                    	at com.android.server.AlarmManagerService$AlarmThread.run(AlarmManagerService.java:4160)
2025-08-18 17:02:57.727  5305-5305  Common                  com.dspread.mdm.service              I  ✅ ⏰ 设置任务执行定时器成功，下次执行: 60秒后 (1分钟)
2025-08-18 17:02:58.085  5305-5379  WebSocket               com.dspread.mdm.service              I  🔧 收到服务器 PONG 帧: 1
2025-08-18 17:03:51.172   961-1047  ActivityManager         system_server                        E  Sending non-protected broadcast com.dspread.mdm.service.SERVICE_GUARD_TIMER from system uid 1000 pkg com.dspread.mdm.service (Ask Gemini)
                                                                                                    java.lang.Throwable
                                                                                                    	at com.android.server.am.ActivityManagerService.checkBroadcastFromSystem(ActivityManagerService.java:15082)
                                                                                                    	at com.android.server.am.ActivityManagerService.broadcastIntentLocked(ActivityManagerService.java:15719)
                                                                                                    	at com.android.server.am.ActivityManagerService.broadcastIntentInPackage(ActivityManagerService.java:15916)
                                                                                                    	at com.android.server.am.ActivityManagerService$LocalService.broadcastIntentInPackage(ActivityManagerService.java:18556)
                                                                                                    	at com.android.server.am.PendingIntentRecord.sendInner(PendingIntentRecord.java:432)
                                                                                                    	at com.android.server.am.PendingIntentRecord.sendWithResult(PendingIntentRecord.java:276)
                                                                                                    	at com.android.server.am.ActivityManagerService.sendIntentSender(ActivityManagerService.java:5633)
                                                                                                    	at android.app.PendingIntent.sendAndReturnResult(PendingIntent.java:896)
                                                                                                    	at android.app.PendingIntent.send(PendingIntent.java:878)
                                                                                                    	at com.android.server.AlarmManagerService$DeliveryTracker.deliverLocked(AlarmManagerService.java:4760)
                                                                                                    	at com.android.server.AlarmManagerService.deliverAlarmsLocked(AlarmManagerService.java:3899)
                                                                                                    	at com.android.server.AlarmManagerService$AlarmThread.run(AlarmManagerService.java:4160)
2025-08-18 17:03:51.192  5305-5344  Common                  com.dspread.mdm.service              D  🔧 ServiceGuardEventHandler 开始执行保活动作
2025-08-18 17:03:51.200  5305-5344  Common                  com.dspread.mdm.service              I  ✅ ⏰ 设置服务守护定时器成功，下次执行: 120秒后 (2分钟)
2025-08-18 17:03:51.202  5305-5344  ContextImpl             com.dspread.mdm.service              W  Calling a method in the system process without a qualified user: android.app.ContextImpl.startService:1571 android.content.ContextWrapper.startService:669 com.dspread.mdm.service.broadcast.handlers.service.ServiceGuardEventHandler.startKeepAliveService:211 com.dspread.mdm.service.broadcast.handlers.service.ServiceGuardEventHandler.performKeepAliveActions:193 com.dspread.mdm.service.broadcast.handlers.service.ServiceGuardEventHandler.access$performKeepAliveActions:24 
2025-08-18 17:03:51.209  5305-5344  Common                  com.dspread.mdm.service              D  🔧 ServiceGuardEventHandler 启动保活服务
2025-08-18 17:03:51.212  5305-5305  Service                 com.dspread.mdm.service              D  🔧 ServiceKeepAliveService 保活服务创建
2025-08-18 17:03:51.215  5305-5305  Service                 com.dspread.mdm.service              D  🔧 ServiceKeepAliveService 保活服务启动
2025-08-18 17:03:51.216  5305-5305  Platform                com.dspread.mdm.service              D  🔧 WakeLockManager WakeLock不存在或未持有: ServiceKeepAlive
2025-08-18 17:03:51.219  5305-5344  Common                  com.dspread.mdm.service              D  🔧 ServiceGuardEventHandler 保活动作执行完成
2025-08-18 17:03:51.219  5305-5305  Platform                com.dspread.mdm.service              D  🔧 WakeLockManager 获取WakeLock成功: ServiceKeepAlive (超时: 30000ms)
2025-08-18 17:03:54.223  5305-5344  Common                  com.dspread.mdm.service              D  🔧 ServiceGuardEventHandler WakeLock已释放
2025-08-18 17:03:57.676  5305-5380  WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 2
2025-08-18 17:03:57.725   961-1047  ActivityManager         system_server                        E  Sending non-protected broadcast com.dspread.mdm.service.WSTASK_EXEC_BC from system uid 1000 pkg com.dspread.mdm.service (Ask Gemini)
                                                                                                    java.lang.Throwable
                                                                                                    	at com.android.server.am.ActivityManagerService.checkBroadcastFromSystem(ActivityManagerService.java:15082)
                                                                                                    	at com.android.server.am.ActivityManagerService.broadcastIntentLocked(ActivityManagerService.java:15719)
                                                                                                    	at com.android.server.am.ActivityManagerService.broadcastIntentInPackage(ActivityManagerService.java:15916)
                                                                                                    	at com.android.server.am.ActivityManagerService$LocalService.broadcastIntentInPackage(ActivityManagerService.java:18556)
                                                                                                    	at com.android.server.am.PendingIntentRecord.sendInner(PendingIntentRecord.java:432)
                                                                                                    	at com.android.server.am.PendingIntentRecord.sendWithResult(PendingIntentRecord.java:276)
                                                                                                    	at com.android.server.am.ActivityManagerService.sendIntentSender(ActivityManagerService.java:5633)
                                                                                                    	at android.app.PendingIntent.sendAndReturnResult(PendingIntent.java:896)
                                                                                                    	at android.app.PendingIntent.send(PendingIntent.java:878)
                                                                                                    	at com.android.server.AlarmManagerService$DeliveryTracker.deliverLocked(AlarmManagerService.java:4760)
                                                                                                    	at com.android.server.AlarmManagerService.deliverAlarmsLocked(AlarmManagerService.java:3899)
                                                                                                    	at com.android.server.AlarmManagerService$AlarmThread.run(AlarmManagerService.java:4160)
2025-08-18 17:03:57.746  5305-5305  Common                  com.dspread.mdm.service              I  ✅ ⏰ 设置任务执行定时器成功，下次执行: 60秒后 (1分钟)
2025-08-18 17:03:58.091  5305-5379  WebSocket               com.dspread.mdm.service              I  🔧 收到服务器 PONG 帧: 2
2025-08-18 17:04:14.560  5305-5305  Receiver                com.dspread.mdm.service              D  🔧 电池状态更新: 电池状态: 电量=55%, 温度=35.0°C, 健康=2, 充电=是, 低电量=否, 耗尽=否
2025-08-18 17:04:21.227  5305-5305  Platform                com.dspread.mdm.service              D  🔧 WakeLockManager WakeLock不存在或未持有: ServiceKeepAlive
2025-08-18 17:04:21.229  5305-5305  Service                 com.dspread.mdm.service              D  🔧 ServiceKeepAliveService 保活任务完成，释放WakeLock
2025-08-18 17:04:37.638  5305-5379  Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"msg_state":"P","request_time":"1755507882067","data":{"param":"","c_type":"CALLC0109"},"expire_time":1755511482,"tranCode":"SC004","request_id":"1755507882067SC004","version":"1","serialNo":"01243030202412090101"}
2025-08-18 17:04:37.641  5305-5379  WebSocket               com.dspread.mdm.service              I  🔧 准备发送响应确认 - requestId: 1755507882067SC004, needResponse: true
2025-08-18 17:04:37.655  5305-5379  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0000","serialNo":"01243030202412090101","request_time":"1755507877644","request_id":"1755507877644C0000","version":"1","org_request_id":"1755507882067SC004","org_request_time":"1755507882067","response_state":"0","myVersionName":"1.0.10.20250818.DSPREAD.MDM.SERVICE","terminalDate":"20250818170437"}
2025-08-18 17:04:37.670  5305-5379  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0000","serialNo":"01243030202412090101","request_time":"1755507877660","request_id":"1755507877660C0000","version":"1","org_request_id":"1755507882067SC004","org_request_time":"1755507882067","response_state":"0","myVersionName":"1.0.10.20250818.DSPREAD.MDM.SERVICE","terminalDate":"20250818170437"}
2025-08-18 17:04:37.671  5305-5379  WebSocket               com.dspread.mdm.service              I  🔧 已发送C0000响应确认: requestId=1755507882067SC004
2025-08-18 17:04:37.674  5305-5379  WebSocket               com.dspread.mdm.service              I  🔧 处理命令: SC004
2025-08-18 17:04:37.675  5305-5379  WebSocket               com.dspread.mdm.service              I  🔧 收到SC004命令，类型: CALLC0109
2025-08-18 17:04:37.677  5305-5379  WebSocket               com.dspread.mdm.service              I  🔧 服务器请求终端信息上传
2025-08-18 17:04:37.678  5305-5379  WebSocket               com.dspread.mdm.service              I  🔧 C0109 主动式上送: terminal_info_timer (主动: 3)
2025-08-18 17:04:37.682  5305-5379  Platform                com.dspread.mdm.service              D  🔧 重新收集应用信息（强制刷新）
2025-08-18 17:04:37.724  5305-5379  AppManager              com.dspread.mdm.service              I  ℹ️ 应用信息获取完成: 总数99(系统95/用户4) 返回4个
2025-08-18 17:04:37.733  5305-5379  Platform                com.dspread.mdm.service              D  🔧 应用信息: 4 个应用
2025-08-18 17:04:37.809  5305-5379  Platform                com.dspread.mdm.service              D  🔧 内存总容量为： 1.87GB 
2025-08-18 17:04:37.810  5305-5379  Platform                com.dspread.mdm.service              D  🔧 内存使用量为： 0.44GB 
2025-08-18 17:04:37.834  5305-5379  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0109","serialNo":"01243030202412090101","request_time":"1755507877819","request_id":"1755507877819C0109","version":"1","data":{"apkInfo":[{"packName":"de.blinkt.openvpn","apkName":"OpenVPN for Android","versionCode":65,"versionName":"0.5.36a","updateDate":"2025-08-18 16:23:34"},{"packName":"com.android.dreams.basic","apkName":"Basic Daydreams","versionCode":29,"versionName":"10","updateDate":"2009-01-01 08:00:00"},{"packName":"com.dspread.mdm.service","apkName":"service","versionCode":13,"versionName":"1.0.10.20250818.DSPREAD.MDM.SERVICE","updateDate":"2025-08-18 16:50:37"},{"packName":"com.dspread.new","apkName":"Smart-pos-demo","versionCode":3687,"versionName":"3.6.87","updateDate":"2009-01-01 08:00:00"}],"locationInfo":{"gps":{"longitude":"-999.0","latitude":"-999.0","satelliteCount":0,"satelliteCountInUsed":0,"satelliteAverSNR":"0.00","distance":"0.0","lockStatus":"1","lockMeter":"200","proId":"","proName":""},"baseSite":{"MCC":"","MNC":"","CID":"-1","LAC":"-1","DBM":"-999","IMSI":"","ICCID":""},"wifi":{"SSID":"fubox_2.4G","IP":"*************","MAC":"D6:C4:A7:CA:EB:20","SSTH":"-34","BSSID":"7c:10:c9:d6:9f:10"},"address":"无法获取"},"hardwareInfo":[{"name":"CPU Usage","desc":"cpu:0.50"},{"name":"storage_usage_status","desc":" 4.76GB \/ 8.00GB ","state":"1"},{"name":"total_memory","desc":" 0.44GB \/ 1.87GB ","state":"1"},{"name":"androidVersion","desc":"10","state":"1"},{"name":"network_status","desc":"wifi","state":"1"},{"name":"OSVer","desc":"10","state":"1"}],"wifiOption":[{"SSID":"2206","SSTH":"-27"},{"SSID":"fubox_2.4G","SSTH":"-33"},{"SSID":"2206-5G","SSTH":"-32"},{"SSID":"DIRECT-C6-Mi All-in-One Inkjet","SSTH":"-49"},{"SSID":"@Ruijie-1816","SSTH":"-53"},{"SSID":"2205","SSTH":"-56"},{"SSID":"2103","SSTH":"-67"},{"SSID":"DIRECT-CC-HP Laser 1188w","SSTH":"-67"},{"SSID":"2205_5G","SSTH":"-68"},{"SSID":"2106","SSTH":"-68"},{"SSID":"@Ruijie-1816_5G","SSTH":"-68"},{"SSID":"2207","SSTH":"-73"},{"SSID":"DIRECT-7D-HP Laser 136w","SSTH":"-75"},{"SSID":"2306","SSTH":"-75"},{"SSID":"2207-5G","SSTH":"-86"},{"SSID":"2103_5G","SSTH":"-89"}],"sytemInfo":{"androidVersion":"10","buildNumber":"D20Pro_Baseline_debug_20250708_ota_test","aspl":"2021-07-05"}},"myVersionName":"1.0.10.20250818.DSPREAD.MDM.SERVICE","terminalDate":"20250818170437"}
2025-08-18 17:04:37.835  5305-5379  WebSocket               com.dspread.mdm.service              I  🔧 C0109 终端信息上传完成 (1, trigger=terminal_info_timer)
2025-08-18 17:04:37.836  5305-5379  WebSocket               com.dspread.mdm.service              I  🔧 同时上传C0901应用信息（含服务信息）
2025-08-18 17:04:37.836  5305-5379  WebSocket               com.dspread.mdm.service              I  🔧 检查默认开启的服务
2025-08-18 17:04:37.837  5305-5379  WebSocket               com.dspread.mdm.service              I  🔧 本地无LogStream服务信息
2025-08-18 17:04:37.838  5305-5379  WebSocket               com.dspread.mdm.service              I  🔧 LogStream服务未启用，跳过保存
2025-08-18 17:04:37.838  5305-5379  WebSocket               com.dspread.mdm.service              I  🔧 默认服务检查完成
2025-08-18 17:04:37.839  5305-5379  WebSocket               com.dspread.mdm.service              I  🔧 C0901 主动式上送: command_trigger (主动: 4)
2025-08-18 17:04:37.842  5305-5379  Platform                com.dspread.mdm.service              D  🔧 使用缓存的应用信息
2025-08-18 17:04:37.851  5305-5379  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0901","serialNo":"01243030202412090101","request_time":"1755507877843","request_id":"1755507877843C0901","version":"1","data":{"apkInfo":[{"packName":"de.blinkt.openvpn","apkName":"OpenVPN for Android","versionCode":65,"versionName":"0.5.36a","updateDate":"2025-08-18 16:23:34"},{"packName":"com.android.dreams.basic","apkName":"Basic Daydreams","versionCode":29,"versionName":"10","updateDate":"2009-01-01 08:00:00"},{"packName":"com.dspread.mdm.service","apkName":"service","versionCode":13,"versionName":"1.0.10.20250818.DSPREAD.MDM.SERVICE","updateDate":"2025-08-18 16:50:37"},{"packName":"com.dspread.new","apkName":"Smart-pos-demo","versionCode":3687,"versionName":"3.6.87","updateDate":"2009-01-01 08:00:00"}],"sytemInfo":{"androidVersion":"10","buildNumber":"D20Pro_Baseline_debug_20250708_ota_test","aspl":"2021-07-05"},"serviceInfo":[]},"myVersionName":"1.0.10.20250818.DSPREAD.MDM.SERVICE","terminalDate":"20250818170437"}
2025-08-18 17:04:37.852  5305-5379  WebSocket               com.dspread.mdm.service              I  🔧 C0901 应用信息上送成功 (1)
2025-08-18 17:04:38.559  5305-5379  Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"request_time":"1755507882850","org_request_time":"1755507877819","org_request_id":"1755507877819C0109","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755507882850S0000","serialNo":"01243030202412090101"}
2025-08-18 17:04:38.561  5305-5379  WebSocket               com.dspread.mdm.service              I  🔧 收到响应消息: transCode=S0000, orgRequestId=1755507877819C0109, state=0, remark=
2025-08-18 17:04:38.563  5305-5379  WebSocket               com.dspread.mdm.service              I  🔧 终端信息上传成功
2025-08-18 17:04:38.566  5305-5379  WebSocket               com.dspread.mdm.service              I  🔧 终端信息上传成功标志已设置
2025-08-18 17:04:45.737  5305-5379  Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"msg_state":"P","request_time":"1755507890196","data":{"param":{"beginDate":"2024-08-18 09:04:50","taskType":"05","period":"1","endDate":"9999-12-31 23:59:59","serviceId":"856c2e6d2c7c4f60bc0fadcf9b9ad650","serviceName":"Log Streaming","command":"C03","url":"https://smartms.s3.sa-east-1.amazonaws.com/icon/5e80041b23fb441695e88253d4542c5b.png","taskId":"1755507890196"},"c_type":"CALL_LOG_STREAM"},"expire_time":1756112690,"tranCode":"SC004","request_id":"1755507890196SC004","version":"1","serialNo":"01243030202412090101"}
2025-08-18 17:04:45.739  5305-5379  WebSocket               com.dspread.mdm.service              I  🔧 准备发送响应确认 - requestId: 1755507890196SC004, needResponse: true
2025-08-18 17:04:45.753  5305-5379  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0000","serialNo":"01243030202412090101","request_time":"1755507885743","request_id":"1755507885743C0000","version":"1","org_request_id":"1755507890196SC004","org_request_time":"1755507890196","response_state":"0","myVersionName":"1.0.10.20250818.DSPREAD.MDM.SERVICE","terminalDate":"20250818170445"}
2025-08-18 17:04:45.767  5305-5379  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0000","serialNo":"01243030202412090101","request_time":"1755507885757","request_id":"1755507885757C0000","version":"1","org_request_id":"1755507890196SC004","org_request_time":"1755507890196","response_state":"0","myVersionName":"1.0.10.20250818.DSPREAD.MDM.SERVICE","terminalDate":"20250818170445"}
2025-08-18 17:04:45.768  5305-5379  WebSocket               com.dspread.mdm.service              I  🔧 已发送C0000响应确认: requestId=1755507890196SC004
2025-08-18 17:04:45.770  5305-5379  WebSocket               com.dspread.mdm.service              I  🔧 处理命令: SC004
2025-08-18 17:04:45.771  5305-5379  WebSocket               com.dspread.mdm.service              I  🔧 收到SC004命令，类型: CALL_LOG_STREAM
2025-08-18 17:04:45.772  5305-5379  WebSocket               com.dspread.mdm.service              I  🔧 服务器请求启动日志流
2025-08-18 17:04:45.919  5305-5374  System.out              com.dspread.mdm.service              I  [okhttp]:check permission begin!
2025-08-18 17:04:45.919  5305-5374  System.out              com.dspread.mdm.service              I  [okhttp]:not MMS!
2025-08-18 17:04:45.919  5305-5374  System.out              com.dspread.mdm.service              I  [okhttp]:not Email!
2025-08-18 17:04:46.020  5305-5374  System.out              com.dspread.mdm.service              I  [socket]:check permission begin!
2025-08-18 17:04:46.301  5305-5374  System.out              com.dspread.mdm.service              I  [OkHttp] sendRequest<<
2025-08-18 17:04:46.569  5305-5344  WebSocket               com.dspread.mdm.service              I  🔧 发送C0901应用信息响应（包含服务信息）
2025-08-18 17:04:46.570  5305-5344  WebSocket               com.dspread.mdm.service              I  🔧 服务信息已添加到管理器: 856c2e6d2c7c4f60bc0fadcf9b9ad650
2025-08-18 17:04:46.571  5305-5344  Platform                com.dspread.mdm.service              D  🔧 使用缓存的应用信息
2025-08-18 17:04:46.573  5305-5344  WebSocket               com.dspread.mdm.service              I  🔧 服务信息已添加到C0901响应中
2025-08-18 17:04:46.590  5305-5344  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0901","serialNo":"01243030202412090101","request_time":"1755507886574","request_id":"1755507886574C0901","version":"1","data":{"apkInfo":[{"packName":"de.blinkt.openvpn","apkName":"OpenVPN for Android","versionCode":65,"versionName":"0.5.36a","updateDate":"2025-08-18 16:23:34"},{"packName":"com.android.dreams.basic","apkName":"Basic Daydreams","versionCode":29,"versionName":"10","updateDate":"2009-01-01 08:00:00"},{"packName":"com.dspread.mdm.service","apkName":"service","versionCode":13,"versionName":"1.0.10.20250818.DSPREAD.MDM.SERVICE","updateDate":"2025-08-18 16:50:37"},{"packName":"com.dspread.new","apkName":"Smart-pos-demo","versionCode":3687,"versionName":"3.6.87","updateDate":"2009-01-01 08:00:00"}],"sytemInfo":{"androidVersion":"10","buildNumber":"D20Pro_Baseline_debug_20250708_ota_test","aspl":"2021-07-05"},"serviceInfo":[{"taskId":"1755507890196","serviceId":"856c2e6d2c7c4f60bc0fadcf9b9ad650","serviceName":"Log Stream","taskType":"05","command":"C03","stateDesc":"IMPLEMENTED","beginDate":"2025-08-18 17:04:46","endDate":"9999-12-31 23:59:59","period":"1","request_id":"","request_time":""}]},"myVersionName":"1.0.10.20250818.DSPREAD.MDM.SERVICE","terminalDate":"20250818170446"}
2025-08-18 17:04:46.591  5305-5344  WebSocket               com.dspread.mdm.service              I  🔧 C0901 应用信息响应（含服务信息）发送成功
2025-08-18 17:04:57.677  5305-5380  WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 3
2025-08-18 17:04:57.744   961-1047  ActivityManager         system_server                        E  Sending non-protected broadcast com.dspread.mdm.service.WSTASK_EXEC_BC from system uid 1000 pkg com.dspread.mdm.service (Ask Gemini)
                                                                                                    java.lang.Throwable
                                                                                                    	at com.android.server.am.ActivityManagerService.checkBroadcastFromSystem(ActivityManagerService.java:15082)
                                                                                                    	at com.android.server.am.ActivityManagerService.broadcastIntentLocked(ActivityManagerService.java:15719)
                                                                                                    	at com.android.server.am.ActivityManagerService.broadcastIntentInPackage(ActivityManagerService.java:15916)
                                                                                                    	at com.android.server.am.ActivityManagerService$LocalService.broadcastIntentInPackage(ActivityManagerService.java:18556)
                                                                                                    	at com.android.server.am.PendingIntentRecord.sendInner(PendingIntentRecord.java:432)
                                                                                                    	at com.android.server.am.PendingIntentRecord.sendWithResult(PendingIntentRecord.java:276)
                                                                                                    	at com.android.server.am.ActivityManagerService.sendIntentSender(ActivityManagerService.java:5633)
                                                                                                    	at android.app.PendingIntent.sendAndReturnResult(PendingIntent.java:896)
                                                                                                    	at android.app.PendingIntent.send(PendingIntent.java:878)
                                                                                                    	at com.android.server.AlarmManagerService$DeliveryTracker.deliverLocked(AlarmManagerService.java:4760)
                                                                                                    	at com.android.server.AlarmManagerService.deliverAlarmsLocked(AlarmManagerService.java:3899)
                                                                                                    	at com.android.server.AlarmManagerService$AlarmThread.run(AlarmManagerService.java:4160)
2025-08-18 17:04:57.764  5305-5305  Common                  com.dspread.mdm.service              I  ✅ ⏰ 设置任务执行定时器成功，下次执行: 60秒后 (1分钟)
2025-08-18 17:04:58.098  5305-5379  WebSocket               com.dspread.mdm.service              I  🔧 收到服务器 PONG 帧: 3
2025-08-18 17:05:11.467  5305-5305  Receiver                com.dspread.mdm.service              D  🔧 电池状态更新: 电池状态: 电量=55%, 温度=35.1°C, 健康=2, 充电=是, 低电量=否, 耗尽=否
2025-08-18 17:05:16.648  5305-5379  Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"msg_state":"P","request_time":"1755507920915","data":{"param":"","c_type":"CALLC0109"},"expire_time":1755511520,"tranCode":"SC004","request_id":"1755507920915SC004","version":"1","serialNo":"01243030202412090101"}
2025-08-18 17:05:16.650  5305-5379  WebSocket               com.dspread.mdm.service              I  🔧 准备发送响应确认 - requestId: 1755507920915SC004, needResponse: true
2025-08-18 17:05:16.665  5305-5379  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0000","serialNo":"01243030202412090101","request_time":"1755507916654","request_id":"1755507916654C0000","version":"1","org_request_id":"1755507920915SC004","org_request_time":"1755507920915","response_state":"0","myVersionName":"1.0.10.20250818.DSPREAD.MDM.SERVICE","terminalDate":"20250818170516"}
2025-08-18 17:05:16.678  5305-5379  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0000","serialNo":"01243030202412090101","request_time":"1755507916669","request_id":"1755507916669C0000","version":"1","org_request_id":"1755507920915SC004","org_request_time":"1755507920915","response_state":"0","myVersionName":"1.0.10.20250818.DSPREAD.MDM.SERVICE","terminalDate":"20250818170516"}
2025-08-18 17:05:16.680  5305-5379  WebSocket               com.dspread.mdm.service              I  🔧 已发送C0000响应确认: requestId=1755507920915SC004
2025-08-18 17:05:16.682  5305-5379  WebSocket               com.dspread.mdm.service              I  🔧 处理命令: SC004
2025-08-18 17:05:16.683  5305-5379  WebSocket               com.dspread.mdm.service              I  🔧 收到SC004命令，类型: CALLC0109
2025-08-18 17:05:16.684  5305-5379  WebSocket               com.dspread.mdm.service              I  🔧 服务器请求终端信息上传
2025-08-18 17:05:16.686  5305-5379  WebSocket               com.dspread.mdm.service              I  🔧 C0109 主动式上送: terminal_info_timer (主动: 5)
2025-08-18 17:05:16.692  5305-5379  Platform                com.dspread.mdm.service              D  🔧 重新收集应用信息（强制刷新）
2025-08-18 17:05:16.740  5305-5379  AppManager              com.dspread.mdm.service              I  ℹ️ 应用信息获取完成: 总数99(系统95/用户4) 返回4个
2025-08-18 17:05:16.751  5305-5379  Platform                com.dspread.mdm.service              D  🔧 应用信息: 4 个应用
2025-08-18 17:05:16.853  5305-5379  Platform                com.dspread.mdm.service              D  🔧 内存总容量为： 1.87GB 
2025-08-18 17:05:16.855  5305-5379  Platform                com.dspread.mdm.service              D  🔧 内存使用量为： 0.44GB 
2025-08-18 17:05:16.885  5305-5379  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0109","serialNo":"01243030202412090101","request_time":"1755507916867","request_id":"1755507916867C0109","version":"1","data":{"apkInfo":[{"packName":"de.blinkt.openvpn","apkName":"OpenVPN for Android","versionCode":65,"versionName":"0.5.36a","updateDate":"2025-08-18 16:23:34"},{"packName":"com.android.dreams.basic","apkName":"Basic Daydreams","versionCode":29,"versionName":"10","updateDate":"2009-01-01 08:00:00"},{"packName":"com.dspread.mdm.service","apkName":"service","versionCode":13,"versionName":"1.0.10.20250818.DSPREAD.MDM.SERVICE","updateDate":"2025-08-18 16:50:37"},{"packName":"com.dspread.new","apkName":"Smart-pos-demo","versionCode":3687,"versionName":"3.6.87","updateDate":"2009-01-01 08:00:00"}],"locationInfo":{"gps":{"longitude":"-999.0","latitude":"-999.0","satelliteCount":0,"satelliteCountInUsed":0,"satelliteAverSNR":"0.00","distance":"0.0","lockStatus":"1","lockMeter":"200","proId":"","proName":""},"baseSite":{"MCC":"","MNC":"","CID":"-1","LAC":"-1","DBM":"-999","IMSI":"","ICCID":""},"wifi":{"SSID":"fubox_2.4G","IP":"*************","MAC":"D6:C4:A7:CA:EB:20","SSTH":"-34","BSSID":"7c:10:c9:d6:9f:10"},"address":"无法获取"},"hardwareInfo":[{"name":"CPU Usage","desc":"cpu:0.50"},{"name":"storage_usage_status","desc":" 4.76GB \/ 8.00GB ","state":"1"},{"name":"total_memory","desc":" 0.44GB \/ 1.87GB ","state":"1"},{"name":"androidVersion","desc":"10","state":"1"},{"name":"network_status","desc":"wifi","state":"1"},{"name":"OSVer","desc":"10","state":"1"}],"wifiOption":[{"SSID":"2206","SSTH":"-27"},{"SSID":"fubox_2.4G","SSTH":"-34"},{"SSID":"2206-5G","SSTH":"-32"},{"SSID":"DIRECT-C6-Mi All-in-One Inkjet","SSTH":"-49"},{"SSID":"@Ruijie-1816","SSTH":"-53"},{"SSID":"2205","SSTH":"-56"},{"SSID":"2103","SSTH":"-67"},{"SSID":"DIRECT-CC-HP Laser 1188w","SSTH":"-67"},{"SSID":"2205_5G","SSTH":"-68"},{"SSID":"2106","SSTH":"-68"},{"SSID":"@Ruijie-1816_5G","SSTH":"-68"},{"SSID":"2207","SSTH":"-73"},{"SSID":"DIRECT-7D-HP Laser 136w","SSTH":"-75"},{"SSID":"2306","SSTH":"-75"},{"SSID":"2207-5G","SSTH":"-86"},{"SSID":"2103_5G","SSTH":"-89"}],"sytemInfo":{"androidVersion":"10","buildNumber":"D20Pro_Baseline_debug_20250708_ota_test","aspl":"2021-07-05"}},"myVersionName":"1.0.10.20250818.DSPREAD.MDM.SERVICE","terminalDate":"20250818170516"}
2025-08-18 17:05:16.886  5305-5379  WebSocket               com.dspread.mdm.service              I  🔧 C0109 终端信息上传完成 (1, trigger=terminal_info_timer)
2025-08-18 17:05:16.887  5305-5379  WebSocket               com.dspread.mdm.service              I  🔧 同时上传C0901应用信息（含服务信息）
2025-08-18 17:05:16.887  5305-5379  WebSocket               com.dspread.mdm.service              I  🔧 检查默认开启的服务
2025-08-18 17:05:16.888  5305-5379  WebSocket               com.dspread.mdm.service              I  🔧 本地无LogStream服务信息
2025-08-18 17:05:16.889  5305-5379  WebSocket               com.dspread.mdm.service              I  🔧 LogStream服务未启用，跳过保存
2025-08-18 17:05:16.889  5305-5379  WebSocket               com.dspread.mdm.service              I  🔧 默认服务检查完成
2025-08-18 17:05:16.890  5305-5379  WebSocket               com.dspread.mdm.service              I  🔧 C0901 主动式上送: command_trigger (主动: 6)
2025-08-18 17:05:16.894  5305-5379  Platform                com.dspread.mdm.service              D  🔧 使用缓存的应用信息
2025-08-18 17:05:16.903  5305-5379  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0901","serialNo":"01243030202412090101","request_time":"1755507916894","request_id":"1755507916894C0901","version":"1","data":{"apkInfo":[{"packName":"de.blinkt.openvpn","apkName":"OpenVPN for Android","versionCode":65,"versionName":"0.5.36a","updateDate":"2025-08-18 16:23:34"},{"packName":"com.android.dreams.basic","apkName":"Basic Daydreams","versionCode":29,"versionName":"10","updateDate":"2009-01-01 08:00:00"},{"packName":"com.dspread.mdm.service","apkName":"service","versionCode":13,"versionName":"1.0.10.20250818.DSPREAD.MDM.SERVICE","updateDate":"2025-08-18 16:50:37"},{"packName":"com.dspread.new","apkName":"Smart-pos-demo","versionCode":3687,"versionName":"3.6.87","updateDate":"2009-01-01 08:00:00"}],"sytemInfo":{"androidVersion":"10","buildNumber":"D20Pro_Baseline_debug_20250708_ota_test","aspl":"2021-07-05"},"serviceInfo":[{"taskId":"1755507890196","serviceId":"856c2e6d2c7c4f60bc0fadcf9b9ad650","serviceName":"Log Stream","taskType":"05","command":"C03","stateDesc":"IMPLEMENTED","beginDate":"2025-08-18 17:04:46","endDate":"9999-12-31 23:59:59","period":"1","request_id":"","request_time":""}]},"myVersionName":"1.0.10.20250818.DSPREAD.MDM.SERVICE","terminalDate":"20250818170516"}
2025-08-18 17:05:16.904  5305-5379  WebSocket               com.dspread.mdm.service              I  🔧 C0901 应用信息上送成功 (1)
2025-08-18 17:05:17.541  5305-5379  Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"request_time":"1755507921902","org_request_time":"1755507916867","org_request_id":"1755507916867C0109","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755507921902S0000","serialNo":"01243030202412090101"}
2025-08-18 17:05:17.543  5305-5379  WebSocket               com.dspread.mdm.service              I  🔧 收到响应消息: transCode=S0000, orgRequestId=1755507916867C0109, state=0, remark=
2025-08-18 17:05:17.544  5305-5379  WebSocket               com.dspread.mdm.service              I  🔧 终端信息上传成功
2025-08-18 17:05:17.546  5305-5379  WebSocket               com.dspread.mdm.service              I  🔧 终端信息上传成功标志已设置
2025-08-18 17:05:18.701  5305-5379  Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"msg_state":"P","request_time":"1755507923211","data":{"param":"","c_type":"CALL_LOG_STREAM"},"expire_time":1756112723,"tranCode":"SC004","request_id":"1755507923211SC004","version":"1","serialNo":"01243030202412090101"}
2025-08-18 17:05:18.703  5305-5379  WebSocket               com.dspread.mdm.service              I  🔧 准备发送响应确认 - requestId: 1755507923211SC004, needResponse: true
2025-08-18 17:05:18.716  5305-5379  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0000","serialNo":"01243030202412090101","request_time":"1755507918707","request_id":"1755507918707C0000","version":"1","org_request_id":"1755507923211SC004","org_request_time":"1755507923211","response_state":"0","myVersionName":"1.0.10.20250818.DSPREAD.MDM.SERVICE","terminalDate":"20250818170518"}
2025-08-18 17:05:18.728  5305-5379  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0000","serialNo":"01243030202412090101","request_time":"1755507918719","request_id":"1755507918719C0000","version":"1","org_request_id":"1755507923211SC004","org_request_time":"1755507923211","response_state":"0","myVersionName":"1.0.10.20250818.DSPREAD.MDM.SERVICE","terminalDate":"20250818170518"}
2025-08-18 17:05:18.729  5305-5379  WebSocket               com.dspread.mdm.service              I  🔧 已发送C0000响应确认: requestId=1755507923211SC004
2025-08-18 17:05:18.731  5305-5379  WebSocket               com.dspread.mdm.service              I  🔧 处理命令: SC004
2025-08-18 17:05:18.732  5305-5379  WebSocket               com.dspread.mdm.service              I  🔧 收到SC004命令，类型: CALL_LOG_STREAM
2025-08-18 17:05:18.733  5305-5379  WebSocket               com.dspread.mdm.service              I  🔧 服务器请求启动日志流
2025-08-18 17:05:51.199   961-1047  ActivityManager         system_server                        E  Sending non-protected broadcast com.dspread.mdm.service.SERVICE_GUARD_TIMER from system uid 1000 pkg com.dspread.mdm.service (Ask Gemini)
                                                                                                    java.lang.Throwable
                                                                                                    	at com.android.server.am.ActivityManagerService.checkBroadcastFromSystem(ActivityManagerService.java:15082)
                                                                                                    	at com.android.server.am.ActivityManagerService.broadcastIntentLocked(ActivityManagerService.java:15719)
                                                                                                    	at com.android.server.am.ActivityManagerService.broadcastIntentInPackage(ActivityManagerService.java:15916)
                                                                                                    	at com.android.server.am.ActivityManagerService$LocalService.broadcastIntentInPackage(ActivityManagerService.java:18556)
                                                                                                    	at com.android.server.am.PendingIntentRecord.sendInner(PendingIntentRecord.java:432)
                                                                                                    	at com.android.server.am.PendingIntentRecord.sendWithResult(PendingIntentRecord.java:276)
                                                                                                    	at com.android.server.am.ActivityManagerService.sendIntentSender(ActivityManagerService.java:5633)
                                                                                                    	at android.app.PendingIntent.sendAndReturnResult(PendingIntent.java:896)
                                                                                                    	at android.app.PendingIntent.send(PendingIntent.java:878)
                                                                                                    	at com.android.server.AlarmManagerService$DeliveryTracker.deliverLocked(AlarmManagerService.java:4760)
                                                                                                    	at com.android.server.AlarmManagerService.deliverAlarmsLocked(AlarmManagerService.java:3899)
                                                                                                    	at com.android.server.AlarmManagerService$AlarmThread.run(AlarmManagerService.java:4160)
2025-08-18 17:05:51.217  5305-5374  Common                  com.dspread.mdm.service              D  🔧 ServiceGuardEventHandler 开始执行保活动作
2025-08-18 17:05:51.224  5305-5374  Common                  com.dspread.mdm.service              I  ✅ ⏰ 设置服务守护定时器成功，下次执行: 120秒后 (2分钟)
2025-08-18 17:05:51.226  5305-5374  ContextImpl             com.dspread.mdm.service              W  Calling a method in the system process without a qualified user: android.app.ContextImpl.startService:1571 android.content.ContextWrapper.startService:669 com.dspread.mdm.service.broadcast.handlers.service.ServiceGuardEventHandler.startKeepAliveService:211 com.dspread.mdm.service.broadcast.handlers.service.ServiceGuardEventHandler.performKeepAliveActions:193 com.dspread.mdm.service.broadcast.handlers.service.ServiceGuardEventHandler.access$performKeepAliveActions:24 
2025-08-18 17:05:51.228  5305-5374  Common                  com.dspread.mdm.service              D  🔧 ServiceGuardEventHandler 启动保活服务
2025-08-18 17:05:51.229  5305-5305  Service                 com.dspread.mdm.service              D  🔧 ServiceKeepAliveService 保活服务启动
2025-08-18 17:05:51.231  5305-5305  Platform                com.dspread.mdm.service              D  🔧 WakeLockManager WakeLock不存在或未持有: ServiceKeepAlive
2025-08-18 17:05:51.234  5305-5305  Platform                com.dspread.mdm.service              D  🔧 WakeLockManager 获取WakeLock成功: ServiceKeepAlive (超时: 30000ms)
2025-08-18 17:05:51.235  5305-5374  Common                  com.dspread.mdm.service              D  🔧 ServiceGuardEventHandler 保活动作执行完成
2025-08-18 17:05:54.240  5305-5374  Common                  com.dspread.mdm.service              D  🔧 ServiceGuardEventHandler WakeLock已释放
2025-08-18 17:05:57.677  5305-5380  WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 4
2025-08-18 17:05:57.762   961-1047  ActivityManager         system_server                        E  Sending non-protected broadcast com.dspread.mdm.service.WSTASK_EXEC_BC from system uid 1000 pkg com.dspread.mdm.service (Ask Gemini)
                                                                                                    java.lang.Throwable
                                                                                                    	at com.android.server.am.ActivityManagerService.checkBroadcastFromSystem(ActivityManagerService.java:15082)
                                                                                                    	at com.android.server.am.ActivityManagerService.broadcastIntentLocked(ActivityManagerService.java:15719)
                                                                                                    	at com.android.server.am.ActivityManagerService.broadcastIntentInPackage(ActivityManagerService.java:15916)
                                                                                                    	at com.android.server.am.ActivityManagerService$LocalService.broadcastIntentInPackage(ActivityManagerService.java:18556)
                                                                                                    	at com.android.server.am.PendingIntentRecord.sendInner(PendingIntentRecord.java:432)
                                                                                                    	at com.android.server.am.PendingIntentRecord.sendWithResult(PendingIntentRecord.java:276)
                                                                                                    	at com.android.server.am.ActivityManagerService.sendIntentSender(ActivityManagerService.java:5633)
                                                                                                    	at android.app.PendingIntent.sendAndReturnResult(PendingIntent.java:896)
                                                                                                    	at android.app.PendingIntent.send(PendingIntent.java:878)
                                                                                                    	at com.android.server.AlarmManagerService$DeliveryTracker.deliverLocked(AlarmManagerService.java:4760)
                                                                                                    	at com.android.server.AlarmManagerService.deliverAlarmsLocked(AlarmManagerService.java:3899)
                                                                                                    	at com.android.server.AlarmManagerService$AlarmThread.run(AlarmManagerService.java:4160)
2025-08-18 17:05:57.785  5305-5305  Common                  com.dspread.mdm.service              I  ✅ ⏰ 设置任务执行定时器成功，下次执行: 60秒后 (1分钟)
2025-08-18 17:05:58.106  5305-5379  WebSocket               com.dspread.mdm.service              I  🔧 收到服务器 PONG 帧: 4
2025-08-18 17:06:07.876  5305-5305  Receiver                com.dspread.mdm.service              D  🔧 电池状态更新: 电池状态: 电量=56%, 温度=35.1°C, 健康=2, 充电=是, 低电量=否, 耗尽=否
2025-08-18 17:06:21.251  5305-5305  Platform                com.dspread.mdm.service              D  🔧 WakeLockManager WakeLock不存在或未持有: ServiceKeepAlive
2025-08-18 17:06:21.252  5305-5305  Service                 com.dspread.mdm.service              D  🔧 ServiceKeepAliveService 保活任务完成，释放WakeLock
2025-08-18 17:06:55.939  5305-5305  Receiver                com.dspread.mdm.service              D  🔧 电池状态更新: 电池状态: 电量=56%, 温度=35.0°C, 健康=2, 充电=是, 低电量=否, 耗尽=否
2025-08-18 17:06:57.679  5305-5380  WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 5
2025-08-18 17:06:57.713   961-1047  ActivityManager         system_server                        E  Sending non-protected broadcast com.dspread.mdm.service.POLL_TIMER_START from system uid 1000 pkg com.dspread.mdm.service (Ask Gemini)
                                                                                                    java.lang.Throwable
                                                                                                    	at com.android.server.am.ActivityManagerService.checkBroadcastFromSystem(ActivityManagerService.java:15082)
                                                                                                    	at com.android.server.am.ActivityManagerService.broadcastIntentLocked(ActivityManagerService.java:15719)
                                                                                                    	at com.android.server.am.ActivityManagerService.broadcastIntentInPackage(ActivityManagerService.java:15916)
                                                                                                    	at com.android.server.am.ActivityManagerService$LocalService.broadcastIntentInPackage(ActivityManagerService.java:18556)
                                                                                                    	at com.android.server.am.PendingIntentRecord.sendInner(PendingIntentRecord.java:432)
                                                                                                    	at com.android.server.am.PendingIntentRecord.sendWithResult(PendingIntentRecord.java:276)
                                                                                                    	at com.android.server.am.ActivityManagerService.sendIntentSender(ActivityManagerService.java:5633)
                                                                                                    	at android.app.PendingIntent.sendAndReturnResult(PendingIntent.java:896)
                                                                                                    	at android.app.PendingIntent.send(PendingIntent.java:878)
                                                                                                    	at com.android.server.AlarmManagerService$DeliveryTracker.deliverLocked(AlarmManagerService.java:4760)
                                                                                                    	at com.android.server.AlarmManagerService.deliverAlarmsLocked(AlarmManagerService.java:3899)
                                                                                                    	at com.android.server.AlarmManagerService$AlarmThread.run(AlarmManagerService.java:4160)
2025-08-18 17:06:57.730  5305-5305  Receiver                com.dspread.mdm.service              D  🔧 收到轮询定时器广播
2025-08-18 17:06:57.732  5305-5305  WebSocket               com.dspread.mdm.service              I  🔧 WebSocketCenter: 发送心跳
2025-08-18 17:06:57.733  5305-5305  Receiver                com.dspread.mdm.service              D  🔧 检查待上传的任务和规则结果
2025-08-18 17:06:57.740  5305-5305  WebSocket               com.dspread.mdm.service              I  🔧 C0201 被动式上送: scheduled_report (被动: 8)
2025-08-18 17:06:57.758  5305-5305  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0201","serialNo":"01243030202412090101","request_time":"1755508017747","request_id":"1755508017747C0201","version":"1","data":{"unboxStatus":"normal","isInUse":"1","websocketConnected":true,"myVersionName":"1.0.10.20250818.DSPREAD.MDM.SERVICE","terminalDate":"20250818170657"},"myVersionName":"1.0.10.20250818.DSPREAD.MDM.SERVICE","terminalDate":"20250818170657"}
2025-08-18 17:06:57.760  5305-5305  WebSocket               com.dspread.mdm.service              I  🔧 C0201 设备状态上送成功: status=normal, inUse=true (1)
2025-08-18 17:06:57.764  5305-5305  Common                  com.dspread.mdm.service              D  🔧 TimerConfig 使用Provisioning配置的心跳间隔: 300秒
2025-08-18 17:06:57.768  5305-5305  Common                  com.dspread.mdm.service              I  ✅ ⏰ 设置心跳定时器成功，下次执行: 300秒后 (5分钟)
2025-08-18 17:06:57.783   961-1047  ActivityManager         system_server                        E  Sending non-protected broadcast com.dspread.mdm.service.WSTASK_EXEC_BC from system uid 1000 pkg com.dspread.mdm.service (Ask Gemini)
                                                                                                    java.lang.Throwable
                                                                                                    	at com.android.server.am.ActivityManagerService.checkBroadcastFromSystem(ActivityManagerService.java:15082)
                                                                                                    	at com.android.server.am.ActivityManagerService.broadcastIntentLocked(ActivityManagerService.java:15719)
                                                                                                    	at com.android.server.am.ActivityManagerService.broadcastIntentInPackage(ActivityManagerService.java:15916)
                                                                                                    	at com.android.server.am.ActivityManagerService$LocalService.broadcastIntentInPackage(ActivityManagerService.java:18556)
                                                                                                    	at com.android.server.am.PendingIntentRecord.sendInner(PendingIntentRecord.java:432)
                                                                                                    	at com.android.server.am.PendingIntentRecord.sendWithResult(PendingIntentRecord.java:276)
                                                                                                    	at com.android.server.am.ActivityManagerService.sendIntentSender(ActivityManagerService.java:5633)
                                                                                                    	at android.app.PendingIntent.sendAndReturnResult(PendingIntent.java:896)
                                                                                                    	at android.app.PendingIntent.send(PendingIntent.java:878)
                                                                                                    	at com.android.server.AlarmManagerService$DeliveryTracker.deliverLocked(AlarmManagerService.java:4760)
                                                                                                    	at com.android.server.AlarmManagerService.deliverAlarmsLocked(AlarmManagerService.java:3899)
                                                                                                    	at com.android.server.AlarmManagerService$AlarmThread.run(AlarmManagerService.java:4160)
2025-08-18 17:06:57.799  5305-5305  Common                  com.dspread.mdm.service              I  ✅ ⏰ 设置任务执行定时器成功，下次执行: 60秒后 (1分钟)
2025-08-18 17:06:58.112  5305-5379  WebSocket               com.dspread.mdm.service              I  🔧 收到服务器 PONG 帧: 5
2025-08-18 17:07:11.467  5305-5305  Receiver                com.dspread.mdm.service              D  🔧 电池状态更新: 电池状态: 电量=56%, 温度=35.1°C, 健康=2, 充电=是, 低电量=否, 耗尽=否
2025-08-18 17:07:51.223   961-1047  ActivityManager         system_server                        E  Sending non-protected broadcast com.dspread.mdm.service.SERVICE_GUARD_TIMER from system uid 1000 pkg com.dspread.mdm.service (Ask Gemini)
                                                                                                    java.lang.Throwable
                                                                                                    	at com.android.server.am.ActivityManagerService.checkBroadcastFromSystem(ActivityManagerService.java:15082)
                                                                                                    	at com.android.server.am.ActivityManagerService.broadcastIntentLocked(ActivityManagerService.java:15719)
                                                                                                    	at com.android.server.am.ActivityManagerService.broadcastIntentInPackage(ActivityManagerService.java:15916)
                                                                                                    	at com.android.server.am.ActivityManagerService$LocalService.broadcastIntentInPackage(ActivityManagerService.java:18556)
                                                                                                    	at com.android.server.am.PendingIntentRecord.sendInner(PendingIntentRecord.java:432)
                                                                                                    	at com.android.server.am.PendingIntentRecord.sendWithResult(PendingIntentRecord.java:276)
                                                                                                    	at com.android.server.am.ActivityManagerService.sendIntentSender(ActivityManagerService.java:5633)
                                                                                                    	at android.app.PendingIntent.sendAndReturnResult(PendingIntent.java:896)
                                                                                                    	at android.app.PendingIntent.send(PendingIntent.java:878)
                                                                                                    	at com.android.server.AlarmManagerService$DeliveryTracker.deliverLocked(AlarmManagerService.java:4760)
                                                                                                    	at com.android.server.AlarmManagerService.deliverAlarmsLocked(AlarmManagerService.java:3899)
                                                                                                    	at com.android.server.AlarmManagerService$AlarmThread.run(AlarmManagerService.java:4160)
2025-08-18 17:07:51.246  5305-5374  Common                  com.dspread.mdm.service              D  🔧 ServiceGuardEventHandler 开始执行保活动作
2025-08-18 17:07:51.253  5305-5374  Common                  com.dspread.mdm.service              I  ✅ ⏰ 设置服务守护定时器成功，下次执行: 120秒后 (2分钟)
2025-08-18 17:07:51.256  5305-5374  ContextImpl             com.dspread.mdm.service              W  Calling a method in the system process without a qualified user: android.app.ContextImpl.startService:1571 android.content.ContextWrapper.startService:669 com.dspread.mdm.service.broadcast.handlers.service.ServiceGuardEventHandler.startKeepAliveService:211 com.dspread.mdm.service.broadcast.handlers.service.ServiceGuardEventHandler.performKeepAliveActions:193 com.dspread.mdm.service.broadcast.handlers.service.ServiceGuardEventHandler.access$performKeepAliveActions:24 
2025-08-18 17:07:51.259  5305-5374  Common                  com.dspread.mdm.service              D  🔧 ServiceGuardEventHandler 启动保活服务
2025-08-18 17:07:51.260  5305-5305  Service                 com.dspread.mdm.service              D  🔧 ServiceKeepAliveService 保活服务启动
2025-08-18 17:07:51.261  5305-5305  Platform                com.dspread.mdm.service              D  🔧 WakeLockManager WakeLock不存在或未持有: ServiceKeepAlive
2025-08-18 17:07:51.264  5305-5374  Common                  com.dspread.mdm.service              D  🔧 ServiceGuardEventHandler 保活动作执行完成
2025-08-18 17:07:51.266  5305-5305  Platform                com.dspread.mdm.service              D  🔧 WakeLockManager 获取WakeLock成功: ServiceKeepAlive (超时: 30000ms)
2025-08-18 17:07:54.269  5305-5374  Common                  com.dspread.mdm.service              D  🔧 ServiceGuardEventHandler WakeLock已释放
2025-08-18 17:07:57.679  5305-5380  WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 6
2025-08-18 17:07:57.799   961-1047  ActivityManager         system_server                        E  Sending non-protected broadcast com.dspread.mdm.service.WSTASK_EXEC_BC from system uid 1000 pkg com.dspread.mdm.service (Ask Gemini)
                                                                                                    java.lang.Throwable
                                                                                                    	at com.android.server.am.ActivityManagerService.checkBroadcastFromSystem(ActivityManagerService.java:15082)
                                                                                                    	at com.android.server.am.ActivityManagerService.broadcastIntentLocked(ActivityManagerService.java:15719)
                                                                                                    	at com.android.server.am.ActivityManagerService.broadcastIntentInPackage(ActivityManagerService.java:15916)
                                                                                                    	at com.android.server.am.ActivityManagerService$LocalService.broadcastIntentInPackage(ActivityManagerService.java:18556)
                                                                                                    	at com.android.server.am.PendingIntentRecord.sendInner(PendingIntentRecord.java:432)
                                                                                                    	at com.android.server.am.PendingIntentRecord.sendWithResult(PendingIntentRecord.java:276)
                                                                                                    	at com.android.server.am.ActivityManagerService.sendIntentSender(ActivityManagerService.java:5633)
                                                                                                    	at android.app.PendingIntent.sendAndReturnResult(PendingIntent.java:896)
                                                                                                    	at android.app.PendingIntent.send(PendingIntent.java:878)
                                                                                                    	at com.android.server.AlarmManagerService$DeliveryTracker.deliverLocked(AlarmManagerService.java:4760)
                                                                                                    	at com.android.server.AlarmManagerService.deliverAlarmsLocked(AlarmManagerService.java:3899)
                                                                                                    	at com.android.server.AlarmManagerService$AlarmThread.run(AlarmManagerService.java:4160)
2025-08-18 17:07:57.821  5305-5305  Common                  com.dspread.mdm.service              I  ✅ ⏰ 设置任务执行定时器成功，下次执行: 60秒后 (1分钟)
2025-08-18 17:07:58.120  5305-5379  WebSocket               com.dspread.mdm.service              I  🔧 收到服务器 PONG 帧: 6
2025-08-18 17:08:00.311  5305-5305  Receiver                com.dspread.mdm.service              D  🔧 电池状态更新: 电池状态: 电量=56%, 温度=35.1°C, 健康=2, 充电=是, 低电量=否, 耗尽=否
2025-08-18 17:08:21.289  5305-5305  Platform                com.dspread.mdm.service              D  🔧 WakeLockManager WakeLock不存在或未持有: ServiceKeepAlive
2025-08-18 17:08:21.291  5305-5305  Service                 com.dspread.mdm.service              D  🔧 ServiceKeepAliveService 保活任务完成，释放WakeLock
2025-08-18 17:08:57.680  5305-5380  WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 7
2025-08-18 17:08:57.819   961-1047  ActivityManager         system_server                        E  Sending non-protected broadcast com.dspread.mdm.service.WSTASK_EXEC_BC from system uid 1000 pkg com.dspread.mdm.service (Ask Gemini)
                                                                                                    java.lang.Throwable
                                                                                                    	at com.android.server.am.ActivityManagerService.checkBroadcastFromSystem(ActivityManagerService.java:15082)
                                                                                                    	at com.android.server.am.ActivityManagerService.broadcastIntentLocked(ActivityManagerService.java:15719)
                                                                                                    	at com.android.server.am.ActivityManagerService.broadcastIntentInPackage(ActivityManagerService.java:15916)
                                                                                                    	at com.android.server.am.ActivityManagerService$LocalService.broadcastIntentInPackage(ActivityManagerService.java:18556)
                                                                                                    	at com.android.server.am.PendingIntentRecord.sendInner(PendingIntentRecord.java:432)
                                                                                                    	at com.android.server.am.PendingIntentRecord.sendWithResult(PendingIntentRecord.java:276)
                                                                                                    	at com.android.server.am.ActivityManagerService.sendIntentSender(ActivityManagerService.java:5633)
                                                                                                    	at android.app.PendingIntent.sendAndReturnResult(PendingIntent.java:896)
                                                                                                    	at android.app.PendingIntent.send(PendingIntent.java:878)
                                                                                                    	at com.android.server.AlarmManagerService$DeliveryTracker.deliverLocked(AlarmManagerService.java:4760)
                                                                                                    	at com.android.server.AlarmManagerService.deliverAlarmsLocked(AlarmManagerService.java:3899)
                                                                                                    	at com.android.server.AlarmManagerService$AlarmThread.run(AlarmManagerService.java:4160)
2025-08-18 17:08:57.842  5305-5305  Common                  com.dspread.mdm.service              I  ✅ ⏰ 设置任务执行定时器成功，下次执行: 60秒后 (1分钟)
2025-08-18 17:08:58.130  5305-5379  WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误 (Ask Gemini)
                                                                                                    com.neovisionaries.ws.client.NoMoreFrameException: No more WebSocket frame from the server.
                                                                                                    	at com.neovisionaries.ws.client.WebSocketInputStream.readFrame(WebSocketInputStream.java:54)
                                                                                                    	at com.neovisionaries.ws.client.ReadingThread.readFrame(ReadingThread.java:338)
                                                                                                    	at com.neovisionaries.ws.client.ReadingThread.main(ReadingThread.java:99)
                                                                                                    	at com.neovisionaries.ws.client.ReadingThread.runMain(ReadingThread.java:64)
                                                                                                    	at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45)
2025-08-18 17:08:58.132  5305-5379  WebSocket               com.dspread.mdm.service              I  🔧 onError: 不触发重连，等待 onDisconnected 处理
2025-08-18 17:08:58.138  5305-5380  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接断开 - 客户端主动断开
2025-08-18 17:08:58.140  5305-5380  WebSocket               com.dspread.mdm.service              I  🔧    服务器关闭帧: code=-1, reason=无原因
2025-08-18 17:08:58.141  5305-5380  WebSocket               com.dspread.mdm.service              I  🔧    客户端关闭帧: code=1002, reason=No more WebSocket frame from the server.
2025-08-18 17:08:58.143  5305-5380  WebSocket               com.dspread.mdm.service              I  🔧    断开发起方: 客户端
2025-08-18 17:08:58.145  5305-5380  WebSocket               com.dspread.mdm.service              I  🔧 reconnect() 被调用，调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onDisconnected:727
2025-08-18 17:08:58.146  5305-5380  WebSocket               com.dspread.mdm.service              I  🔧 开始重连 WebSocket... (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onDisconnected:727)
2025-08-18 17:08:58.151  5305-5380  WebSocket               com.dspread.mdm.service              I  🔧 使用Provisioning配置的重连策略: delayPolicy=1, delaySwitch=1, delayTime=60
2025-08-18 17:08:58.154  5305-5380  WebSocket               com.dspread.mdm.service              I  🔧 当前断开记录数: 1, 延迟开关: 1
2025-08-18 17:08:58.155  5305-5380  WebSocket               com.dspread.mdm.service              I  🔧 重连策略计算完成: 第1次重连，间隔3000ms (3秒)
2025-08-18 17:08:58.156  5305-5380  WebSocket               com.dspread.mdm.service              I  🔧 开始第1次重连，间隔3000ms (3秒) (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onDisconnected:727)
2025-08-18 17:09:01.174  5305-5305  WebSocket               com.dspread.mdm.service              I  🔧 重连 WebSocket: wss://api.dspreadserv.net/status/websocket/register?serialNo=MDEyNDMwMzAyMDI0MTIwOTAxMDE=&publicKey=TUlHZk1BMEdDU3FHU0liM0RRRUJBUVVBQTRHTkFEQ0JpUUtCZ1FDejNQb2hmNnFkZTRvYVY2blJhc21aK1pZWXQxZEFVSExqKzVITkhxUlRnUWRZeGdrRGNaSHcwdjlhYWI4OENpa2FwYmhNZTZabm0yWFJZU1dnV1N0ZnJGQzJNNU1oNHFwaCtCd1BBOElGdkpHeThCOUpzOVpHNkRqa2JDdG0xenNxVEJyMU9qRkdWWDBBSVBiZ1ROVFRSN0pzWW9rMk16cXFVK0NmeVNkN1F3SURBUUFC&query=0&msgVer=3&timestamp=1755508141161&signature=Y8GG1qAZJ4oE29brKg70KABXOzik5DnU/DhFHMdn+26PQ4XI+/KChYbYgZicJlvMG3HzYHrST8nLTAdRfyYdBriX2V7XGw13urOnVLWLvjOVR+Lxf5tkdOQ5AX1blHiK3xrWwqx7v9v7S6B+kzUOHHbS2/pV/rPbD5uIfLyn6Nc=
2025-08-18 17:09:01.175  5305-5305  WebSocket               com.dspread.mdm.service              I  🔧 未检测到系统代理设置
2025-08-18 17:09:01.289  5305-5403  System.out              com.dspread.mdm.service              I  [socket]:check permission begin!
2025-08-18 17:09:01.539  5305-5404  System.out              com.dspread.mdm.service              I  [socket]:check permission begin!
2025-08-18 17:09:02.214  5305-5320  System                  com.dspread.mdm.service              W  A resource failed to call close. 
2025-08-18 17:09:02.945  5305-5405  Common                  com.dspread.mdm.service              I  ✅ WebSocket 物理连接成功，等待服务器 hellMsg
2025-08-18 17:09:02.947  5305-5405  WebSocket               com.dspread.mdm.service              I  🔧 ===== 心跳发送器实际配置 =====
2025-08-18 17:09:02.948  5305-5405  WebSocket               com.dspread.mdm.service              I  🔧 实际 PING 发送间隔: 60000ms (60秒)
2025-08-18 17:09:02.950  5305-5405  WebSocket               com.dspread.mdm.service              I  🔧 PING 定时器名称: PingSender
2025-08-18 17:09:02.951  5305-5405  WebSocket               com.dspread.mdm.service              I  🔧 实际 PONG 发送间隔: 0ms (0秒)
2025-08-18 17:09:02.953  5305-5405  WebSocket               com.dspread.mdm.service              I  🔧 PONG 定时器名称: PongSender
2025-08-18 17:09:02.954  5305-5405  WebSocket               com.dspread.mdm.service              I  🔧 ===============================
2025-08-18 17:09:02.975  5305-5405  Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"hellMsg":"hello 01243030202412090101","tranCode":"S0000","version":"1","rebootTime":"02:45:07","serialNo":"01243030202412090101","deviceStatus":"6"}
2025-08-18 17:09:02.977  5305-5405  Common                  com.dspread.mdm.service              I  ✅ 收到服务器 hellMsg，WebSocket 业务连接成功！
2025-08-18 17:09:02.978  5305-5405  Common                  com.dspread.mdm.service              I  ✅ WebSocket 业务连接成功！
2025-08-18 17:09:02.979  5305-5405  WebSocket               com.dspread.mdm.service              I  🔧 业务连接成功，重连计数已重置为 0
2025-08-18 17:09:02.984  5305-5405  Common                  com.dspread.mdm.service              I  ✅ ⏰ 设置任务执行定时器成功，下次执行: 60秒后 (1分钟)
2025-08-18 17:09:02.985  5305-5405  WebSocket               com.dspread.mdm.service              I  🔧 已启动任务执行定时器
2025-08-18 17:09:02.990  5305-5405  Common                  com.dspread.mdm.service              D  🔧 TimerConfig 使用Provisioning配置的心跳间隔: 300秒
2025-08-18 17:09:02.995  5305-5405  Common                  com.dspread.mdm.service              I  ✅ ⏰ 设置心跳定时器成功，下次执行: 300秒后 (5分钟)
2025-08-18 17:09:02.997  5305-5405  WebSocket               com.dspread.mdm.service              I  🔧 已启动心跳定时器
2025-08-18 17:09:03.002  5305-5405  Common                  com.dspread.mdm.service              D  🔧 TimerConfig 使用Provisioning配置的终端信息上传间隔: 900秒
2025-08-18 17:09:03.008  5305-5405  Common                  com.dspread.mdm.service              I  ✅ ⏰ 设置终端信息定时器成功，下次执行: 900秒后 (15分钟)
2025-08-18 17:09:03.009  5305-5405  WebSocket               com.dspread.mdm.service              I  🔧 已启动终端信息上传定时器
2025-08-18 17:09:03.010  5305-5405  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接成功，检查自身更新任务状态
2025-08-18 17:09:03.012  5305-5405  ContextImpl             com.dspread.mdm.service              W  Calling a method in the system process without a qualified user: android.app.ContextImpl.sendBroadcast:1046 android.content.ContextWrapper.sendBroadcast:448 com.dspread.mdm.service.broadcast.core.BroadcastSender.sendBroadcast:99 com.dspread.mdm.service.broadcast.core.BroadcastSender.sendBroadcast:23 com.dspread.mdm.service.network.websocket.connection.WsConnectionManager.checkSelfUpdateTaskStatus:311 
2025-08-18 17:09:03.015   961-974   ActivityManager         system_server                        E  Sending non-protected broadcast com.dspread.mdm.service.CHECK_SELF_UPDATE_STATUS from system 5305:com.dspread.mdm.service/1000 pkg com.dspread.mdm.service (Ask Gemini)
                                                                                                    java.lang.Throwable
                                                                                                    	at com.android.server.am.ActivityManagerService.checkBroadcastFromSystem(ActivityManagerService.java:15078)
                                                                                                    	at com.android.server.am.ActivityManagerService.broadcastIntentLocked(ActivityManagerService.java:15625)
                                                                                                    	at com.android.server.am.ActivityManagerService.broadcastIntentLocked(ActivityManagerService.java:15095)
                                                                                                    	at com.android.server.am.ActivityManagerService.broadcastIntent(ActivityManagerService.java:15878)
                                                                                                    	at android.app.IActivityManager$Stub.onTransact(IActivityManager.java:2071)
                                                                                                    	at com.android.server.am.ActivityManagerService.onTransact(ActivityManagerService.java:2886)
                                                                                                    	at android.os.Binder.execTransactInternal(Binder.java:1021)
                                                                                                    	at android.os.Binder.execTransact(Binder.java:994)
2025-08-18 17:09:03.031  5305-5405  Receiver                com.dspread.mdm.service              D  🔧 发送广播: com.dspread.mdm.service.CHECK_SELF_UPDATE_STATUS
2025-08-18 17:09:03.033  5305-5305  Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 收到自身更新状态检查广播: com.dspread.mdm.service.CHECK_SELF_UPDATE_STATUS
2025-08-18 17:09:03.033  5305-5405  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接成功
2025-08-18 17:09:03.034  5305-5305  Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 开始执行自身更新任务状态检查
2025-08-18 17:09:03.034  5305-5405  WebSocket               com.dspread.mdm.service              I  🔧 设备状态: 6
2025-08-18 17:09:03.035  5305-5405  WebSocket               com.dspread.mdm.service              I  🔧 检查首次连接标志: isFirstTimeWebSocketConnected=false
2025-08-18 17:09:03.035  5305-5305  Task                    com.dspread.mdm.service              D  🔧 获取待执行任务数量: 0
2025-08-18 17:09:03.036  5305-5305  Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 待执行任务数量: 0
2025-08-18 17:09:03.037  5305-5405  WebSocket               com.dspread.mdm.service              I  🔧 非首次连接，跳过初始化信息发送和初始化检查
2025-08-18 17:09:03.038  5305-5405  WebSocket               com.dspread.mdm.service              I  🔧 执行必要的状态同步检查
2025-08-18 17:09:03.086  5305-5405  WebSocket               com.dspread.mdm.service              I  🔧 检查服务更新结果
2025-08-18 17:09:03.087  5305-5405  Task                    com.dspread.mdm.service              I  ℹ️ 本地任务列表长度: 0
2025-08-18 17:09:03.088  5305-5405  Task                    com.dspread.mdm.service              D  🔧 本地任务列表内容: []
2025-08-18 17:09:03.090  5305-5405  Task                    com.dspread.mdm.service              I  ℹ️ 当前应用版本: versionCode=13, versionName=1.0.10.20250818.DSPREAD.MDM.SERVICE
2025-08-18 17:09:03.091  5305-5405  Task                    com.dspread.mdm.service              I  ℹ️ 任务列表数量: 0
2025-08-18 17:09:03.091  5305-5405  WebSocket               com.dspread.mdm.service              I  🔧 检查OS升级状态...
2025-08-18 17:09:03.092  5305-5405  Common                  com.dspread.mdm.service              D  🔧 OsUpdateStatusChecker WebSocket连接成功，检查OS升级状态
2025-08-18 17:09:03.093  5305-5405  Common                  com.dspread.mdm.service              D  🔧 OsUpdateStatusChecker 没有待检查的OS升级任务
2025-08-18 17:09:03.094  5305-5405  WebSocket               com.dspread.mdm.service              I  🔧 OS升级状态检查已触发
