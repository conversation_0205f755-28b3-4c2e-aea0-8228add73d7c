<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="fill_parent"
    android:layout_height="fill_parent"
    android:orientation="vertical"
    android:background="@color/white">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content" >

        <ImageView
            android:id="@+id/iv_title_bg"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerHorizontal="true"
            android:background="@drawable/title_bg"/>

        <ImageView
            android:id="@+id/iv_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="96dp"
            android:layout_centerHorizontal="true"
            android:background="@drawable/login_title"/>
    </RelativeLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="250dp"
        android:layout_marginTop="-70dp"
        android:orientation="vertical">

        <LinearLayout
            android:id="@+id/linearLayout_camera"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_margin="5dp"
            android:layout_marginLeft="5dp"
            android:layout_marginRight="5dp"
            android:layout_weight="1"
            android:orientation="vertical">


            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:gravity="center"
                android:layout_weight="1">

                <RelativeLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content">
                    <LinearLayout
                        android:id="@+id/btn1"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="10dp"
                        android:layout_marginRight="10dp"
                        android:gravity="center"
                        android:orientation="vertical"
                        android:background="@drawable/ic_action_bg_1">

                        <ImageView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_centerHorizontal="true"
                            android:src="@drawable/ic_action_1"/>

                        <TextView
                            style="@style/MainMenuTextView"
                            android:text="@string/mshowurl" />
                    </LinearLayout>
                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="89dp"
                        android:layout_marginTop="79dp"
                        android:background="@drawable/ic_action_bg_corner"/>
                </RelativeLayout>

                <RelativeLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content">
                    <LinearLayout
                        android:id="@+id/btn2"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="10dp"
                        android:layout_marginRight="10dp"
                        android:gravity="center"
                        android:orientation="vertical"
                        android:background="@drawable/ic_action_bg_2">

                        <ImageView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_centerHorizontal="true"
                            android:src="@drawable/ic_action_2"/>

                        <TextView
                            style="@style/MainMenuTextView"
                            android:text="@string/mshowdeviceinfo" />
                    </LinearLayout>
                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="89dp"
                        android:layout_marginTop="79dp"
                        android:background="@drawable/ic_action_bg_corner"/>
                </RelativeLayout>
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:gravity="center"
                android:layout_weight="1">

                <RelativeLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content">
                    <LinearLayout
                        android:id="@+id/btn3"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="10dp"
                        android:layout_marginRight="10dp"
                        android:gravity="center"
                        android:background="@drawable/ic_action_bg_3"
                        android:orientation="vertical">

                        <ImageView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_centerHorizontal="true"
                            android:src="@drawable/ic_action_3" />

                        <TextView
                            style="@style/MainMenuTextView"
                            android:text="@string/mstartpoll" />
                    </LinearLayout>
                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="89dp"
                        android:layout_marginTop="79dp"
                        android:background="@drawable/ic_action_bg_corner"/>
                </RelativeLayout>

                <RelativeLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content">
                    <LinearLayout
                        android:id="@+id/btn4"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="10dp"
                        android:layout_marginRight="10dp"
                        android:gravity="center"
                        android:background="@drawable/ic_action_bg_4"
                        android:orientation="vertical">

                        <ImageView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_centerHorizontal="true"
                            android:src="@drawable/ic_action_4" />

                        <TextView
                            style="@style/MainMenuTextView"
                            android:text="@string/muploadTermInfo" />
                    </LinearLayout>
                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="89dp"
                        android:layout_marginTop="79dp"
                        android:background="@drawable/ic_action_bg_corner"/>
                </RelativeLayout>

                <RelativeLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:visibility="gone">
                    <LinearLayout
                        android:layout_width="180px"
                        android:layout_height="180px"
                        android:layout_marginLeft="10dp"
                        android:layout_marginRight="10dp"
                        android:gravity="center"
                        android:orientation="vertical">

                        <ImageView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_centerHorizontal="true"
                            android:src="@null" />

                        <TextView
                            style="@style/MainMenuTextView"
                            android:text="" />

                    </LinearLayout>
                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="89dp"
                        android:layout_marginTop="79dp"
                        android:background="@null"/>
                </RelativeLayout>

                <RelativeLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:visibility="gone">

                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="89dp"
                        android:layout_marginTop="79dp"
                        android:background="@null"/>

                    <LinearLayout
                        android:layout_width="180px"
                        android:layout_height="180px"
                        android:layout_marginLeft="10dp"
                        android:layout_marginRight="10dp"
                        android:gravity="center"
                        android:orientation="vertical"
                        android:layout_alignParentTop="true"
                        android:layout_centerHorizontal="true">

                        <ImageView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_centerHorizontal="true"
                            android:src="@null" />

                        <TextView
                            style="@style/MainMenuTextView"
                            android:text="" />

                    </LinearLayout>
                </RelativeLayout>
            </LinearLayout>
        </LinearLayout>
    </LinearLayout>

</LinearLayout>
