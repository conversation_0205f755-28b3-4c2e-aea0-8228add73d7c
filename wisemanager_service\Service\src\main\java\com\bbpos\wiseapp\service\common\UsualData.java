package com.bbpos.wiseapp.service.common;

public class UsualData {
    public final static String LAUNCHER_PACKAGE_NAME = "com.bbpos.wiseapp.launcher";
    public final static String LAUNCHER_711_PACKAGE_NAME = "com.seven11.launcher";
    public final static String LOADER_711_PACKAGE_NAME = "com.bbpos.loader";
    public final static String LAUNCHER_CLASS_NAME = "com.bbpos.wiseapp.service.MainActivity";
    public final static String LAUNCHER_RECEIVER_CLASS_NAME = "com.bbpos.wiseapp.launcher.receiver.Receiver";
    public final static String SERVICE_PACKAGE_NAME = "com.bbpos.wiseapp.service";
    public final static String SERVICE_SETTINGS_PACKAGE_NAME = "com.bbpos.wiseapp.settings.activity.SettingActivity";
    public final static String SETTINGS_WIFI_CLASS_NAME = "com.bbpos.wiseapp.settings.activity.WiFiDialogActivity";
    public final static String KIOSK_APP_NAME = "com.example.kiosk";
    public final static String LAUNCHER_WISESCANNER_NAME = "com.mining.app.zxing.activity.MipcaActivityCapture";
    public final static String SYSTEM_SETTINGS_NAME = "com.android.settings.Settings";
    public final static String OTA_TRIGGER_APP_NAME = "com.example.firmware";
    /** TAG for OTA or SP tool update */
    public final static String SYSTEM_WISEPOS_OTA_TRIGGER_APP_PACKAGENAME = "com.bbpos.wiseapp.wisepos.ota";//Wisepos4G
    public final static String SYSTEM_OTA_TRIGGER_APP_PACKAGENAME = "com.bbpos.wiseapp.ota";//7MD
    public final static String SYSTEM_TMT_OTA_TRIGGER_APP_PACKAGENAME = "com.bbpos.wiseapp.tmt";//P1000 P500
    public final static String SYSTEM_KEY_UPDATE_TRIGGER_APP_PACKAGENAME = "com.bbpos.wiseapp.keyupdate";//P1000 P500  --key update
    public final static String SYSTEM_SP_OTA_TRIGGER_APP_PACKAGENAME = "com.bbpos.wiseapp.tool";//7MD SP

    public final static String ACTION_WISEPOS_OTA_TRIGGER_COMPLETED = "com.bbpos.wiseapp.wisepos.atcion.OTA.COMPLETED";
    public final static String ACTION_OTA_TRIGGER_COMPLETED = "com.bbpos.wiseapp.atcion.OTA.COMPLETED";
    public final static String ACTION_SP_OTA_TRIGGER_COMPLETED = "com.bbpos.wiseapp.atcion.SP.COMPLETED";
    public final static String ACTION_TMT_OTA_TRIGGER_COMPLETED = "com.bbpos.wiseapp.atcion.TMT.COMPLETED";
    public final static String ACTION_KEYUPDATE_TRIGGER_COMPLETED = "com.bbpos.wiseapp.atcion.KEYUPDATE.COMPLETED";
    public final static String ACTION_GDS_STATE_UPLOAD = "com.bbpos.wiseapp.atcion.GDS.STATE.UPLOAD";

    public final static String SYSTEM_WISEPOS_OTA_TRIGGER_TASKID = "system_wisepos_ota_trigger_taskid";//Wisepos4G
    public final static String SYSTEM_OTA_TRIGGER_TASKID = "system_ota_trigger_taskid";//7MD
    public final static String SYSTEM_SP_OTA_TRIGGER_TASKID = "system_sp_ota_trigger_taskid";//7MD SP
    public final static String SYSTEM_TMT_TRIGGER_TASKID = "system_tmt_trigger_taskid";//P1000 P500
    public final static String SYSTEM_KEY_UPDATE_TRIGGER_TASKID = "system_key_trigger_taskid";//P1000 P500

    public static final String TMS_SERVICE_NAME = "com.bbpos.wiseapp.tms.service.InitializeService";

    public final static String CONFIG_PRODUCTION_URL = "https://config.wisemanager.com/v2/WiseAppConfig-Prod";//"https://config-test.wisemanager.com/default/WiseAppConfig-UAT";//"https://config.wisemanager.com/default/WiseAppConfig-Prod";
    public final static String CONFIG_UAT_URL = "https://config.uat.wisemanager.com/default/WiseAppConfig-UAT";
    public final static String CONFIG_DEVELOPMENT_URL = "https://config.dev.wisemanager.com/default/WiseAppConfig-Dev";
    
//	public final static String WISEPOS_PULS_TMT_URL_DEFAULT = "https://api-uat.emms.bbpos.com/tms2/deviceasset/";//tmt remote update uat
	public final static String WISEPOS_PULS_TMT_URL_DEFAULT = "https://api.emms.bbpos.com/tms2/deviceasset/";//tmt remote update pro url

    public final static String SHARED_PREFERENCES_UNREAD_NUM = "unread_num";
    public final static String SHARED_PREFERENCES_APPS = "apps";

    public final static String SHARED_UNBOX_STATUS = "unbox_status";
    public final static String SHARED_PREFERENCES_FIRST_RUN = "first_run";
    public final static String SHARED_PREFERENCES_TOP_ON_LAUNCHER = "top_on_launcher";
    public final static String SHARED_PREFERENCES_LAST_NETWORK_STATE = "last_network_state";
    public final static String SHARED_PREFERENCES_CONFIG_URL = "config_url";
    public final static String SHARED_PREFERENCES_INIT_CONFIG_FLAG = "init_config_flag";
    public final static String SHARED_PREFERENCES_SCAN_CODE_CONTENT = "scan_code_content";
    public final static String SHARED_PREFERENCES_LAST_CLIENT_NAME = "last_client";
    public final static String SHARED_PREFERENCES_LAST_CLIENT_ID = "last_client_id";
    public final static String SHARED_PREFERENCES_PROVISION_DEFAULT_WIFI = "provision_default_wifi";
    //configure
    public final static String SHARED_PREFERENCES_VERSION = "version";
    public final static String SHARED_PREFERENCES_CLIENT_NAME = "client";
    public final static String SHARED_PREFERENCES_CLIENT_ID = "client_id";
    public final static String SHARED_PREFERENCES_CID = "cid";
    public final static String SHARED_PREFERENCES_SOID = "soid";
    public final static String SHARED_PREFERENCES_MODE = "mode";
    public final static String SHARED_PREFERENCES_MODE_IN_CONFIG = "mode_in_config";
    public final static String SHARED_PREFERENCES_MODEL = "model";
    public final static String SHARED_PREFERENCES_PWD_MODE = "pwdMode";
    public final static String SHARED_PREFERENCES_GMS_APPLIST = "gmsAppList";
    public final static String SHARED_PREFERENCES_COLOR_STYLE = "colorStyle";
    public final static String SHARED_PREFERENCES_LOGO_URL = "logoUrl";
    public final static String SHARED_PREFERENCES_LOGO_URL_MD5 = "logoUrlMd5";
    public final static String SHARED_PREFERENCES_LOGINTOKEN = "loginToken";
    public final static String SHARED_PREFERENCES_SYSTEM= "system";
    public final static String SHARED_PREFERENCES_GPS = "gps";
    public final static String SHARED_PREFERENCES_GPS_STORE_LATITUDE = "gps_latitude";
    public final static String SHARED_PREFERENCES_GPS_STORE_LONGITUDE = "gps_longitude";
    public final static String SHARED_PREFERENCES_GPS_STATUS = "gps_status";
    public final static String SHARED_PREFERENCES_ISCAREGPS = "care";//是否要关注gps位置信息
    public final static String SHARED_PREFERENCES_GPS_UPDATE_TIME = "minUpdateTime";//地理位置更新时间
    public final static String SHARED_PREFERENCES_GPS_UPDATE_DISTANCE = "minDistance";//地理位置更新距离
    public final static String SHARED_PREFERENCES_GPS_UPDATE_SCHEDULETIME = "scheduleTime";//当care为0时，用来定时获取gps
    public final static String SHARED_PREFERENCES_GPS_UPDATE_MAXLOCATETIME = "maxLocateTime";//定位超时时间
    public final static String SHARED_PREFERENCES_GPS_GEOFENCE_STATUS = "geo_status";//定位超时时间
    public final static String SHARED_PREFERENCES_GPS_GEOFENCE_LOCK_STATUS = "lock_status";//定位超时时间
    public final static String SHARED_PREFERENCES_GPS_GEOFENCE_LOCK_METERS= "lock_meter";//定位超时时间
    public final static String SHARED_PREFERENCES_GPS_GEOFENCE_LOCK_MINS = "lock_min";//定位超时时间
    public final static String SHARED_PREFERENCES_GPS_GEOFENCE_WARN_COUNTDOWN_SECS = "warn_countdown_sec";//定位超时时间
    public final static String SHARED_PREFERENCES_GPS_GEOFENCE_LOCK_COUNTDOWN_MINS = "lock_countdown_min";//定位超时时间
    public final static String SHARED_PREFERENCES_GPS_GEOFENCE_WIPE_STATUS = "wipe_status";//定位超时时间
    public final static String SHARED_PREFERENCES_GPS_GEOFENCE_WIPE_MINS = "wipe_min";//定位超时时间
    public final static String SHARED_PREFERENCES_GPS_GEOFENCE_OTP_COUNTER = "otp_counter";//定位超时时间
    public final static String SHARED_PREFERENCES_GPS_GEOFENCE_CUR_PROFILE = "current_exe_profile";//定位超时时间
    public final static String SHARED_PREFERENCES_GPS_GEOFENCE_VALID_DISTANCE = "valid_distance";//定位超时时间
    public final static String SHARED_PREFERENCES_PACKAGE_MANAGER = "packageManager";//是否啓用APK驗簽
    public final static String SHARED_PREFERENCES_APK_VERIFY = "apkVerify";//是否啓用APK驗簽
    public final static String SHARED_PREFERENCES_INSTALLER_PACKAGE_NAME = "installerPackageName";//允許安裝APK的應用列表
    public final static String SHARED_PREFERENCES_PAYMENT_PACKAGE_LIST = "paymentPackageList";//允許使用payment SDK的APK的應用列表
    public final static String SHARED_PREFERENCES_APK_BLOCK = "apkBlock";//禁用的應用列表
    public final static String SHARED_PREFERENCES_APK_BLOCK_ENABLE = "enable";//禁用的應用列表開關
    public final static String SHARED_PREFERENCES_APK_BLOCK_LIST = "apkBlockList";//禁用的應用列表開關
    public final static String SHARED_PREFERENCES_DISABLE_PHONE = "disable_phone";//禁用电话功能
    public final static String SHARED_PREFERENCES_DISABLE_MMS = "disable_mms";//禁用短信功能
    public final static String SHARED_PREFERENCES_CURRENT_LAUNCHER = "currentLauncher";//記錄當前設置的主Launcher
	public final static String SHARED_PREFERENCES_STORE_ID = "store_id";//記錄當前設置的主Launcher
    public final static String SHARED_PREFERENCES_STORE_SSID = "store_ssid";//記錄當前設置的主Launcher
    public final static String SHARED_PREFERENCES_STORE_IP = "store_ip";//記錄當前設置的主Launcher    public final static String SHARED_PREFERENCES_DIALOG_SWITCH = "dialogSwitch";
	public final static String SHARED_PREFERENCES_DIALOG_SWITCH = "dialogSwitch";
	
    public final static String SHARED_PREFERENCES_WALLPAPER_URL = "wallpaperUrl";
    public final static String SHARED_PREFERENCES_WALLPAPER_URL_MD5 = "wallpaperUrlMd5";
    public final static String SHARED_PREFERENCES_CLIENT_APP_URL = "clientAppUrl";
    public final static String SHARED_PREFERENCES_CLIENT_APP_URL_MD5 = "clientAppUrlMd5";
    public final static String SHARED_PREFERENCES_APP_URL = "appUrl";
    public final static String SHARED_PREFERENCES_APP_MD5 = "appMd5";
    public final static String SHARED_PREFERENCES_STARTUP_APP_PACKAGE_NAME = "startupAppPackageName";
    public final static String SHARED_PREFERENCES_ACCESS_POLLING_TIME = "accessPollingTime";
    public final static String SHARED_PREFERENCES_TIMEZONE = "timezone";
    public final static String SHARED_PREFERENCES_REQUEST_TIME = "requestTime";
    public final static String SHARED_PREFERENCES_POWER_SAVE_MODE_ENABLE = "enable";
    public final static String SHARED_PREFERENCES_SCREEN_TIMEOUT = "screenTimeout";
    public final static String SHARED_PREFERENCES_CUSTOMER_CA_CERT_SWITCH = "caCertSwitch";
    public final static String SHARED_PREFERENCES_CUSTOMER_CA_CERT_MD5 = "caCertMd5";
    public final static String SHARED_PREFERENCES_CUSTOMER_CA_CERT_URL = "caCertUrl";


    public final static String SHARED_PREFERENCES_SERVER_URL = "serverUrl";
    public final static String SHARED_PREFERENCES_HEAVY_SERVER_URL = "heavyServerUrl";
    public final static String SHARED_PREFERENCES_HEARTBEAT_TIME = "heartbeatTime";
    public final static String SHARED_PREFERENCES_TERMINAL_INFO_TIME = "terminalInfoTime";
    public final static String SHARED_PREFERENCES_WISELOG_TIME = "wiseLogUploadTime";
    public final static String SHARED_PREFERENCES_UPLOAD_MODE = "uploadMode";
    public final static String SHARED_PREFERENCES_WSSRECONN = "wssreconn";
    public final static String SHARED_PREFERENCES_DELAY_SWITCH = "delaySwitch";
    public final static String SHARED_PREFERENCES_DELAY_TIME = "delayTime";
    public final static String SHARED_PREFERENCES_DELAY_POLICY = "delayPolicy";

    public final static String SHARED_PREFERENCES_WM_API_URL = "wmapiUrl";
    public final static String SHARED_PREFERENCES_WEBSOCKET_SERVER_URL = "statusApiUrl";
    public final static String SHARED_PREFERENCES_LAST_WEBSOCKET_SERVER_URL = "lastStatusApiUrl";
    public final static String SHARED_PREFERENCES_WEBSOCKET_REMOTE_VIEW_SERVER_URL = "remoteUrl";
    public final static String SHARED_PREFERENCES_OTA_SERVER_URL = "otaServerUrl";
    public final static String SHARED_PREFERENCES_TMT_SERVER_URL = "tmtServerUrl";
    public final static String SHARED_PREFERENCES_ENABLE_WISELOG = "enableWiseLog";
    public final static String SHARED_PREFERENCES_WPL_HAS_MIGRATE = "wplHasMigrate";//wifi profile旧数据是否已迁移(明文转密文)

    public final static String SHARED_PREFERENCES_OTA_TODO_FLAG = "otaTodo";
//    public final static String SHARED_PREFERENCES_NEED_TMT_TODO_FLAG = "tmtTodo";//P1000 P500
//    public final static String SHARED_PREFERENCES_NEED_KEY_UPDATE_TODO_FLAG = "keyUpdateTodo";//P1000 P500
    public final static String SHARED_PREFERENCES_NEED_WISEPOS5_OTA_FLAG = "isNeedWisePos5Ota";//P1000 P500
    public final static String SHARED_PREFERENCES_NEED_WISEPOSPRO_OTA_FLAG = "isNeedOta";//7MD
    public final static String SHARED_PREFERENCES_NEED_WISEPOS4G_OTA_FLAG = "isNeedWiseposOta";//wisepos4G

    public final static String SHARED_PREFERENCES_SYSTEM_LOGO = "systemLogo";
    public final static String SHARED_PREFERENCES_SYSTEM_LOGO_MD5 = "systemLogoMd5";
    public final static String SHARED_PREFERENCES_BOOT_ANIMATION = "bootAnimation";
    public final static String SHARED_PREFERENCES_BOOT_ANIMATION_MD5 = "bootAnimationMd5";
    public final static String SHARED_PREFERENCES_KIOSK = "kiosk";
    public final static String SHARED_PREFERENCES_LOCATION_METHOD = "location_method";
    public final static String SHARED_PREFERENCES_CONCEAL = "conceal";
    public final static String SHARED_PREFERENCES_FIRST_PAGE_CELL_COUNT = "cell_count";

    public final static String SHARED_PREFERENCES_WIFIPROXY = "wifi_proxy";
    public final static String SHARED_PREFERENCES_BT_MAC = "bt_mac";
    public final static String SHARED_PREFERENCES_BT_ENABLE = "bt_enable";
    public final static String SHARED_PREFERENCES_FALL_COUNT = "fall_count";
    public final static String SHARED_PREFERENCES_ALLOW_BT_LISTENING = "bt_listening";
    public final static String SHARED_PREFERENCES_ALLOW_PAYMENT_DATA = "allow_payment_data_upload";

    public static final int NETWORK_STATUS_INAVAILABLE = 0;    //没有网络连接
    public static final int NETWORK_STATUS_WIFI = 1;             //无线网络
    public static final int NETWORK_STATUS_MOBILE = 2;           //移动网络
    public static final int NETWORK_STATUS_ETHERNET = 3;        //以太网

    public static final String DISABLE_USUAL_KEY = "android.intent.action.DISABLE_KEYCODE";
    public static final String DISABLE_RECENT_APPS = "android.intent.action.DISABLE_RECENT_APPS";

    //BroadcastAction
    public static final String ACTION_SHOW_PROGRESS = "com.bbpos.wiseapp.service.ACTION_SHOW_PROGRESS";
    public static final String ACTION_HIDE_PROGRESS = "com.bbpos.wiseapp.service.ACTION_HIDE_PROGRESS";
    public static final String ACTION_PROGRESS_PERCENT = "com.bbpos.wiseapp.service.ACTION_PROGRESS_PERCENT";
    public static final String ACTION_PROGRESS_MSG = "com.bbpos.wiseapp.service.ACTION_PROGRESS_MSG";
    public static final String ACTION_LAUNCHER_REFRESH = "com.bbpos.wiseapp.service.ACTION_LAUNCHER_REFRESH";
    public static final String ACTION_SERVICE_UPDATED = "com.bbpos.wiseapp.service.ACTION_SERVICE_UPDATED";
    public static final String ACTION_DOWNLOAD_RESOURCE = "com.bbpos.wiseapp.service.ACTION_DOWNLOAD_RESOURCE";
    public static final String ACTION_TOAST_MSG = "com.bbpos.wiseapp.service.ACTION_TOAST_MSG";
    public static final String ACTION_STARTCLIENTAPK_MSG = "com.bbpos.wiseapp.service.ACTION_STARTCLIENTAPK_MSG";
    public static final String ACTION_TMT_DONE = "com.bbpos.wiseapp.service.ACTION_TMT_DONE";
    public static final String ACTION_TODO_OTAUPGRADE = "com.bbpos.wiseapp.service.ACTION_TODO_OTAUPGRADE";
    public static final String ACTION_DIALOG_MSG = "com.bbpos.wiseapp.service.ACTION_DIALOG_MSG";
    public static final String ACTION_CLEAR_DESKTOP_MSG = "com.bbpos.wiseapp.launcher.ACTION_CLEAR_DESKTOP_MSG";
    public static final String ACTION_TODO_WISEPOS_OTA_UPDATE = "com.bbpos.wiseapp.launcher.ACTION_WISEPOS_OTA_UPDATE";
    public static final String ACTION_TODO_OTA_UPDATE = "com.bbpos.wiseapp.launcher.ACTION_OTA_UPDATE";
    public static final String ACTION_TODO_SP_UPDATE = "com.bbpos.wiseapp.launcher.ACTION_SP_UPDATE";
    public static final String ACTION_TODO_TMT_UPDATE = "com.bbpos.wiseapp.launcher.ACTION_TMT_UPDATE";
    public static final String ACTION_TODO_KEY_UPDATE = "com.bbpos.wiseapp.launcher.ACTION_KEY_UPDATE";
    public static final String ACTION_DELETE_APK = "com.bbpos.wiseapp.launcher.ACTION_DELETE_APK";
    public static final String ACTION_LAUNCH_COMPLETE = "com.bbpos.wiseapp.launcher.ACTION_LAUNCH_COMPLETE";
    public static final String ACTION_CLOSE_LOCKSCREEN = "com.bbpos.wiseapp.launcher.ACTION_CLOSE_LOCKSCREEN";
    public static final String ACTION_DATA_WIPED = "com.bbpos.wiseapp.launcher.ACTION_DATA_WIPED";
    public static final String ACTION_WIPE_PARAM_CHANGE = "com.bbpos.wiseapp.launcher.ACTION_WIPE_PARAM_CHANGE";
    public static final String ACTION_ENTER_GEOFENCE = "com.bbpos.wiseapp.launcher.ACTION_ENTER_GEOFENCE";
    public static final String ACTION_OUT_OF_GEOFENCE = "com.bbpos.wiseapp.launcher.ACTION_OUT_OF_GEOFENCE";
    public static final String ACTION_GET_ONETIME_PWD = "com.bbpos.wiseapp.launcher.ACTION_GET_ONETIME_PWD";
    public static final String ACTION_WEBSOCKET_CONNECTION = "com.bbpos.wiseapp.launcher.ACTION_WEBSOCKET_CONNECTION";

    public static final String RULEBASED_EXEC_BC = "com.bbpos.wiseapp.service.RULEBASED_EXEC_BC";
    public static final String WSTASK_UPDATED_BC = "com.bbpos.wiseapp.service.WSTASK_UPDATED_BC";
    public static final String WSTASK_EXEC_BC = "com.bbpos.wiseapp.service.WSTASK_EXEC_BC";

    public static final String LAST_WIFI_RSSI_VALUE = "last_wifi_rssi_value";
    public static final String LAST_WIFI_SSID_VALUE = "last_wifi_ssid_value";

    public static final String LAST_READ_LOG_TIME = "last_read_log_time";
    public static final String LOG_STREAM_SERVICE_APP_EXECUTING = "logstream_serviceapp_executing";
    public static final String LOGGER_UPLOAD_LIST = "logger_upload_list";

    public final static String SHARED_PREFERENCES_OSUPDATE_TASKID = "osupdate_taskid";//記錄當前設置的主Launcher

    public final static String SHARED_PREFERENCES_DEVICE_BID_INFO = "device_bid_info";//設備bid信息
    public final static String SHARED_PREFERENCES_DEVICE_FULL_INFO = "device_full_info";//完整deviceInfo 信息
    public final static String IPTABLES_RULES = "iptables.rules";

    public final static String SHARED_PREFERENCES_NGINX_IP = "nginx_ip";
    public final static String SHARED_PREFERENCES_NGINX_ENABLE = "nginx_enable";

    public final static String SHARED_PREFERENCES_APN_LIST = "apn_list";
}
