2025-08-11 14:36:14.215 22592-22592 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 收到广播: com.dspread.mdm.service.WSTASK_EXEC_BC
2025-08-11 14:36:14.230 22592-22592 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 分发广播事件: com.dspread.mdm.service.WSTASK_EXEC_BC -> 1个处理器
2025-08-11 14:36:14.246 22592-22592 Receiver                com.dspread.mdm.service              D  🔧 没有任务
2025-08-11 14:36:14.251 22592-22592 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 处理器 TaskExecuteEventHandler 处理成功: com.dspread.mdm.service.WSTASK_EXEC_BC
2025-08-11 14:37:15.221 22592-22592 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 收到广播: com.dspread.mdm.service.WSTASK_EXEC_BC
2025-08-11 14:37:15.230 22592-22592 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 分发广播事件: com.dspread.mdm.service.WSTASK_EXEC_BC -> 1个处理器
2025-08-11 14:37:15.249 22592-22592 Receiver                com.dspread.mdm.service              D  🔧 没有任务
2025-08-11 14:37:15.254 22592-22592 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 处理器 TaskExecuteEventHandler 处理成功: com.dspread.mdm.service.WSTASK_EXEC_BC
2025-08-11 14:38:15.987 22592-22592 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 收到广播: com.dspread.mdm.service.WSTASK_EXEC_BC
2025-08-11 14:38:15.993 22592-22592 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 分发广播事件: com.dspread.mdm.service.WSTASK_EXEC_BC -> 1个处理器
2025-08-11 14:38:16.015 22592-22592 Receiver                com.dspread.mdm.service              D  🔧 没有任务
2025-08-11 14:38:16.019 22592-22592 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 处理器 TaskExecuteEventHandler 处理成功: com.dspread.mdm.service.WSTASK_EXEC_BC
2025-08-11 14:39:17.208 22592-22592 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 收到广播: com.dspread.mdm.service.WSTASK_EXEC_BC
2025-08-11 14:39:17.223 22592-22592 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 分发广播事件: com.dspread.mdm.service.WSTASK_EXEC_BC -> 1个处理器
2025-08-11 14:39:17.245 22592-22592 Receiver                com.dspread.mdm.service              D  🔧 没有任务
2025-08-11 14:39:17.251 22592-22592 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 处理器 TaskExecuteEventHandler 处理成功: com.dspread.mdm.service.WSTASK_EXEC_BC
2025-08-11 14:39:48.201 22592-25360 WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 17
2025-08-11 14:40:17.245 22592-22592 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 收到广播: com.dspread.mdm.service.WSTASK_EXEC_BC
2025-08-11 14:40:17.251 22592-22592 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 分发广播事件: com.dspread.mdm.service.WSTASK_EXEC_BC -> 1个处理器
2025-08-11 14:40:17.271 22592-22592 Receiver                com.dspread.mdm.service              D  🔧 没有任务
2025-08-11 14:40:17.277 22592-22592 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 处理器 TaskExecuteEventHandler 处理成功: com.dspread.mdm.service.WSTASK_EXEC_BC
2025-08-11 14:41:18.219 22592-22592 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 收到广播: com.dspread.mdm.service.WSTASK_EXEC_BC
2025-08-11 14:41:18.231 22592-22592 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 分发广播事件: com.dspread.mdm.service.WSTASK_EXEC_BC -> 1个处理器
2025-08-11 14:41:18.246 22592-22592 Receiver                com.dspread.mdm.service              D  🔧 没有任务
2025-08-11 14:41:18.252 22592-22592 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 处理器 TaskExecuteEventHandler 处理成功: com.dspread.mdm.service.WSTASK_EXEC_BC
2025-08-11 14:41:54.189 22592-25359 WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误 (Ask Gemini)
                                                                                                    com.neovisionaries.ws.client.WebSocketException: An I/O error occurred while a frame was being read from the web socket: Connection timed out
                                                                                                    	at com.neovisionaries.ws.client.ReadingThread.readFrame(ReadingThread.java:375)
                                                                                                    	at com.neovisionaries.ws.client.ReadingThread.main(ReadingThread.java:99)
                                                                                                    	at com.neovisionaries.ws.client.ReadingThread.runMain(ReadingThread.java:64)
                                                                                                    	at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45)
                                                                                                    Caused by: java.net.SocketException: Connection timed out
                                                                                                    	at java.net.SocketInputStream.socketRead0(Native Method)
                                                                                                    	at java.net.SocketInputStream.socketRead(SocketInputStream.java:118)
                                                                                                    	at java.net.SocketInputStream.read(SocketInputStream.java:173)
                                                                                                    	at java.net.SocketInputStream.read(SocketInputStream.java:143)
                                                                                                    	at java.io.BufferedInputStream.fill(BufferedInputStream.java:239)
                                                                                                    	at java.io.BufferedInputStream.read1(BufferedInputStream.java:279)
                                                                                                    	at java.io.BufferedInputStream.read(BufferedInputStream.java:338)
                                                                                                    	at java.io.FilterInputStream.read(FilterInputStream.java:133)
                                                                                                    	at com.neovisionaries.ws.client.WebSocketInputStream.readBytes(WebSocketInputStream.java:165)
                                                                                                    	at com.neovisionaries.ws.client.WebSocketInputStream.readFrame(WebSocketInputStream.java:46)
                                                                                                    	at com.neovisionaries.ws.client.ReadingThread.readFrame(ReadingThread.java:338)
                                                                                                    	at com.neovisionaries.ws.client.ReadingThread.main(ReadingThread.java:99) 
                                                                                                    	at com.neovisionaries.ws.client.ReadingThread.runMain(ReadingThread.java:64) 
                                                                                                    	at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45) 
2025-08-11 14:41:54.196 22592-25359 WebSocket               com.dspread.mdm.service              I  🔧 onError: 不触发重连，等待 onDisconnected 处理
2025-08-11 14:41:54.211 22592-25360 WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误 (Ask Gemini)
                                                                                                    com.neovisionaries.ws.client.WebSocketException: Flushing frames to the server failed: Broken pipe
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.doFlush(WritingThread.java:434)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.sendFrames(WritingThread.java:384)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.main(WritingThread.java:108)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.runMain(WritingThread.java:55)
                                                                                                    	at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45)
                                                                                                    Caused by: java.net.SocketException: Broken pipe
                                                                                                    	at java.net.SocketOutputStream.socketWrite0(Native Method)
                                                                                                    	at java.net.SocketOutputStream.socketWrite(SocketOutputStream.java:116)
                                                                                                    	at java.net.SocketOutputStream.write(SocketOutputStream.java:156)
                                                                                                    	at java.io.BufferedOutputStream.flushBuffer(BufferedOutputStream.java:81)
                                                                                                    	at java.io.BufferedOutputStream.flush(BufferedOutputStream.java:142)
                                                                                                    	at java.io.BufferedOutputStream.flush(BufferedOutputStream.java:143)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.flush(WritingThread.java:271)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.doFlush(WritingThread.java:422)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.sendFrames(WritingThread.java:384) 
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.main(WritingThread.java:108) 
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.runMain(WritingThread.java:55) 
                                                                                                    	at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45) 
2025-08-11 14:41:54.218 22592-25360 WebSocket               com.dspread.mdm.service              I  🔧 onError: 不触发重连，等待 onDisconnected 处理
2025-08-11 14:41:54.227 22592-25360 WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误 (Ask Gemini)
                                                                                                    com.neovisionaries.ws.client.WebSocketException: Flushing frames to the server failed: Broken pipe
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.doFlush(WritingThread.java:434)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.sendFrames(WritingThread.java:384)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.main(WritingThread.java:120)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.runMain(WritingThread.java:55)
                                                                                                    	at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45)
                                                                                                    Caused by: java.net.SocketException: Broken pipe
                                                                                                    	at java.net.SocketOutputStream.socketWrite0(Native Method)
                                                                                                    	at java.net.SocketOutputStream.socketWrite(SocketOutputStream.java:116)
                                                                                                    	at java.net.SocketOutputStream.write(SocketOutputStream.java:156)
                                                                                                    	at java.io.BufferedOutputStream.flushBuffer(BufferedOutputStream.java:81)
                                                                                                    	at java.io.BufferedOutputStream.flush(BufferedOutputStream.java:142)
                                                                                                    	at java.io.BufferedOutputStream.flush(BufferedOutputStream.java:143)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.flush(WritingThread.java:271)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.doFlush(WritingThread.java:422)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.sendFrames(WritingThread.java:384) 
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.main(WritingThread.java:120) 
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.runMain(WritingThread.java:55) 
                                                                                                    	at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45) 
2025-08-11 14:41:54.232 22592-25360 WebSocket               com.dspread.mdm.service              I  🔧 onError: 不触发重连，等待 onDisconnected 处理
2025-08-11 14:41:54.239 22592-25360 WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接断开 - 客户端主动断开
2025-08-11 14:41:54.244 22592-25360 WebSocket               com.dspread.mdm.service              I  🔧    服务器关闭帧: code=-1, reason=无原因
2025-08-11 14:41:54.249 22592-25360 WebSocket               com.dspread.mdm.service              I  🔧    客户端关闭帧: code=1008, reason=An I/O error occurred while a frame was being read from the web socket: Connection timed out
2025-08-11 14:41:54.254 22592-25360 WebSocket               com.dspread.mdm.service              I  🔧    断开发起方: 客户端
2025-08-11 14:41:54.258 22592-25360 WebSocket               com.dspread.mdm.service              I  🔧 reconnect() 被调用，调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onDisconnected:705
2025-08-11 14:41:54.262 22592-25360 WebSocket               com.dspread.mdm.service              I  🔧 开始重连 WebSocket... (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onDisconnected:705)
2025-08-11 14:41:54.270 22592-25360 WebSocket               com.dspread.mdm.service              I  🔧 使用Provisioning配置的重连策略: delayPolicy=30, delaySwitch=1, delayTime=900
2025-08-11 14:41:54.274 22592-25360 WebSocket               com.dspread.mdm.service              I  🔧 当前断开记录数: 3, 延迟开关: 1
2025-08-11 14:41:54.278 22592-25360 WebSocket               com.dspread.mdm.service              I  🔧 重连策略计算完成: 第1次重连，间隔3000ms (3秒)
2025-08-11 14:41:54.282 22592-25360 WebSocket               com.dspread.mdm.service              I  🔧 开始第1次重连，间隔3000ms (3秒) (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onDisconnected:705)
2025-08-11 14:42:09.962 22592-22592 WebSocket               com.dspread.mdm.service              I  🔧 重连 WebSocket: ws://35.75.3.206:8080/status/websocket/register?serialNo=MDEzNTQwOTAyMDI1MDMwNTAzOTk=&publicKey=TUlHZk1BMEdDU3FHU0liM0RRRUJBUVVBQTRHTkFEQ0JpUUtCZ1FDK09zN2VEZlBLOUcySnFoS29CME0rMmJha1VETEZhWUlYY3YxbGszWGZyeitjWndmNTJHSlQrL2lkWkFhVjBPRlpmOUwvR1pvc005eVYrYVpja0l1cnMwNDhJeXc1UlpBNHA1Ty9VaDRzMU55bnBRM21jV0gra3lBWXIvL1luOFBYeXdVQmZKYy9XTlBpTHB5NTd0OFhpVkhDYXVhQ2FpSEZZeW9paTZ0OEd3SURBUUFC&query=0&msgVer=3&timestamp=1754894529945&signature=OkRWjspw9l3dggFIrPQiqcGspLBVRrPHYePxhJMba7FXRGGa4YNW7gYEEbal6+Na2Xwwu6ojI96Kd3SrrmLxiBrwB4toM7XNHuNwJXQ89v5BXoPUfNqyAYtzGwoY8yD0/hquLHWOkv2nwC81GUgsUfe3DXpPKbttKba7QnFrw3c=
2025-08-11 14:42:09.971 22592-22592 WebSocket               com.dspread.mdm.service              I  🔧 未检测到系统代理设置
2025-08-11 14:42:09.983 22592-25792 TrafficStats            com.dspread.mdm.service              D  tagSocket(85) with statsTag=0xffffffff, statsUid=-1
2025-08-11 14:42:10.473 22592-25796 Common                  com.dspread.mdm.service              I  ✅ WebSocket 物理连接成功，等待服务器 hellMsg
2025-08-11 14:42:10.479 22592-25796 WebSocket               com.dspread.mdm.service              I  🔧 ===== 心跳发送器实际配置 =====
2025-08-11 14:42:10.486 22592-25796 WebSocket               com.dspread.mdm.service              I  🔧 实际 PING 发送间隔: 60000ms (60秒)
2025-08-11 14:42:10.492 22592-25796 WebSocket               com.dspread.mdm.service              I  🔧 PING 定时器名称: PingSender
2025-08-11 14:42:10.499 22592-25796 WebSocket               com.dspread.mdm.service              I  🔧 实际 PONG 发送间隔: 0ms (0秒)
2025-08-11 14:42:10.505 22592-25796 WebSocket               com.dspread.mdm.service              I  🔧 PONG 定时器名称: PongSender
2025-08-11 14:42:10.509 22592-25796 WebSocket               com.dspread.mdm.service              I  🔧 ===============================
2025-08-11 14:42:10.822 22592-25796 Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"hellMsg":"hello 01354090202503050399","tranCode":"S0000","version":"1","rebootTime":"03:05:02","serialNo":"01354090202503050399","deviceStatus":"6"}
2025-08-11 14:42:10.828 22592-25796 WebSocket               com.dspread.mdm.service              I  🔧 收到业务消息 - tranCode: S0000
2025-08-11 14:42:10.833 22592-25796 Common                  com.dspread.mdm.service              I  ✅ 收到服务器 hellMsg，WebSocket 业务连接成功！
2025-08-11 14:42:10.838 22592-25796 Common                  com.dspread.mdm.service              I  ✅ WebSocket 业务连接成功！
2025-08-11 14:42:10.843 22592-25796 WebSocket               com.dspread.mdm.service              I  🔧 业务连接成功，重连计数已重置为 0
2025-08-11 14:42:10.853 22592-25796 Common                  com.dspread.mdm.service              D  🔧 使用Provisioning配置的心跳间隔: 300秒
2025-08-11 14:42:10.857 22592-25796 Common                  com.dspread.mdm.service              D  🔧 启动轮询定时器，间隔: 300秒
2025-08-11 14:42:10.862 22592-25796 WebSocket               com.dspread.mdm.service              I  🔧 WebSocketCenter: 发送心跳
2025-08-11 14:42:10.866 22592-25796 WebSocket               com.dspread.mdm.service              I  🔧 发送心跳消息
2025-08-11 14:42:10.870 22592-25796 WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 已连接，执行轮询任务
2025-08-11 14:42:10.879 22592-25796 Common                  com.dspread.mdm.service              D  🔧 已安排下一次轮询: 300秒后
2025-08-11 14:42:10.883 22592-25796 WebSocket               com.dspread.mdm.service              I  🔧 已启动心跳轮询定时器
2025-08-11 14:42:10.890 22592-25796 Receiver                com.dspread.mdm.service              D  🔧 TaskExecutionTimer 启动任务执行定时器: 60s
2025-08-11 14:42:10.900 22592-25796 WebSocket               com.dspread.mdm.service              I  🔧 已启动任务执行定时器
2025-08-11 14:42:10.904 22592-25796 WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接成功，检查自身更新任务状态
2025-08-11 14:42:10.908 22592-25796 ContextImpl             com.dspread.mdm.service              W  Calling a method in the system process without a qualified user: android.app.ContextImpl.sendBroadcast:1220 android.content.ContextWrapper.sendBroadcast:504 com.dspread.mdm.service.receiver.manager.BroadcastSender.sendBroadcast:103 com.dspread.mdm.service.receiver.manager.BroadcastSender.sendBroadcast:27 com.dspread.mdm.service.network.websocket.connection.WsConnectionManager.checkSelfUpdateTaskStatus:312 
2025-08-11 14:42:10.911 22592-25796 Receiver                com.dspread.mdm.service              D  🔧 发送广播: com.dspread.mdm.service.CHECK_SELF_UPDATE_STATUS
2025-08-11 14:42:10.916 22592-25796 WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接成功
2025-08-11 14:42:10.920 22592-25796 WebSocket               com.dspread.mdm.service              I  🔧 设备状态: 6
2025-08-11 14:42:10.923 22592-25796 WebSocket               com.dspread.mdm.service              I  🔧 启动 C0109 定时器
2025-08-11 14:42:10.927 22592-25796 ContextImpl             com.dspread.mdm.service              W  Calling a method in the system process without a qualified user: android.app.ContextImpl.sendBroadcast:1220 android.content.ContextWrapper.sendBroadcast:504 com.dspread.mdm.service.receiver.manager.BroadcastSender.sendBroadcast:103 com.dspread.mdm.service.receiver.manager.BroadcastSender.sendBroadcast:27 com.dspread.mdm.service.network.websocket.message.WsMessageCenter.startTerminalInfoUploadTimer:389 
2025-08-11 14:42:10.930 22592-25796 Receiver                com.dspread.mdm.service              D  🔧 发送广播: com.dspread.mdm.service.TER_INFO_UPLOAD_BC
2025-08-11 14:42:10.934 22592-25796 WebSocket               com.dspread.mdm.service              I  🔧 检查首次连接标志: isFirstTimeWebSocketConnected=false
2025-08-11 14:42:10.938 22592-25796 WebSocket               com.dspread.mdm.service              I  🔧 非首次连接，跳过初始化信息发送和初始化检查
2025-08-11 14:42:10.941 22592-25796 WebSocket               com.dspread.mdm.service              I  🔧 执行必要的状态同步检查
2025-08-11 14:42:10.945 22592-25796 WebSocket               com.dspread.mdm.service              I  🔧 检查服务更新结果
2025-08-11 14:42:10.949 22592-25796 Task                    com.dspread.mdm.service              I  ℹ️ 📋 本地任务列表长度: 0
2025-08-11 14:42:10.952 22592-25796 Task                    com.dspread.mdm.service              D  🔧 📋 本地任务列表内容: []
2025-08-11 14:42:10.956 22592-25796 Task                    com.dspread.mdm.service              I  ℹ️ 当前应用版本: versionCode=11, versionName=1.0.08.20250808.DSPREAD.MDM.SERVICE
2025-08-11 14:42:10.960 22592-25796 Task                    com.dspread.mdm.service              I  ℹ️ 📋 任务列表数量: 0
2025-08-11 14:42:10.964 22592-25796 WebSocket               com.dspread.mdm.service              I  🔧 检查OS升级状态...
2025-08-11 14:42:10.967 22592-25796 Common                  com.dspread.mdm.service              D  🔧 OsUpdateStatusChecker WebSocket连接成功，检查OS升级状态
2025-08-11 14:42:10.971 22592-25796 Common                  com.dspread.mdm.service              D  🔧 OsUpdateStatusChecker 没有待检查的OS升级任务
2025-08-11 14:42:10.975 22592-25796 WebSocket               com.dspread.mdm.service              I  🔧 OS升级状态检查已触发
2025-08-11 14:43:10.903 22592-22592 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 收到广播: com.dspread.mdm.service.WSTASK_EXEC_BC
2025-08-11 14:43:10.910 22592-22592 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 分发广播事件: com.dspread.mdm.service.WSTASK_EXEC_BC -> 1个处理器
2025-08-11 14:43:10.928 22592-22592 Receiver                com.dspread.mdm.service              D  🔧 没有任务
2025-08-11 14:43:10.936 22592-22592 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 处理器 TaskExecuteEventHandler 处理成功: com.dspread.mdm.service.WSTASK_EXEC_BC
2025-08-11 14:44:10.927 22592-22592 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 收到广播: com.dspread.mdm.service.WSTASK_EXEC_BC
2025-08-11 14:44:10.934 22592-22592 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 分发广播事件: com.dspread.mdm.service.WSTASK_EXEC_BC -> 1个处理器
2025-08-11 14:44:10.953 22592-22592 Receiver                com.dspread.mdm.service              D  🔧 没有任务
2025-08-11 14:44:10.960 22592-22592 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 处理器 TaskExecuteEventHandler 处理成功: com.dspread.mdm.service.WSTASK_EXEC_BC
2025-08-11 14:45:12.211 22592-22592 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 收到广播: com.dspread.mdm.service.WSTASK_EXEC_BC
2025-08-11 14:45:12.220 22592-22592 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 分发广播事件: com.dspread.mdm.service.WSTASK_EXEC_BC -> 1个处理器
2025-08-11 14:45:12.239 22592-22592 Receiver                com.dspread.mdm.service              D  🔧 没有任务
2025-08-11 14:45:12.244 22592-22592 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 处理器 TaskExecuteEventHandler 处理成功: com.dspread.mdm.service.WSTASK_EXEC_BC
2025-08-11 14:46:13.183 22592-22592 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 收到广播: com.dspread.mdm.service.WSTASK_EXEC_BC
2025-08-11 14:46:13.194 22592-22592 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 分发广播事件: com.dspread.mdm.service.WSTASK_EXEC_BC -> 1个处理器
2025-08-11 14:46:13.218 22592-22592 Receiver                com.dspread.mdm.service              D  🔧 没有任务
2025-08-11 14:46:13.225 22592-22592 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 处理器 TaskExecuteEventHandler 处理成功: com.dspread.mdm.service.WSTASK_EXEC_BC
2025-08-11 14:47:12.180 22592-25797 WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 1
2025-08-11 14:47:12.188 22592-22592 Receiver                com.dspread.mdm.service              D  🔧 🔧 TimerReceiver 收到广播: com.dspread.mdm.service.POLL_TIMER_START
2025-08-11 14:47:12.205 22592-22592 Receiver                com.dspread.mdm.service              D  🔧 🔧 处理轮询定时器启动
2025-08-11 14:47:12.216 22592-22592 Receiver                com.dspread.mdm.service              D  🔧 🔧 WebSocket连接状态: true
2025-08-11 14:47:12.221 22592-22592 Receiver                com.dspread.mdm.service              D  🔧 🔧 发送心跳
2025-08-11 14:47:12.227 22592-22592 WebSocket               com.dspread.mdm.service              I  🔧 WebSocketCenter: 发送心跳
2025-08-11 14:47:12.232 22592-22592 WebSocket               com.dspread.mdm.service              I  🔧 发送心跳消息
2025-08-11 14:47:12.236 22592-22592 Receiver                com.dspread.mdm.service              D  🔧 🔧 开始执行轮询任务
2025-08-11 14:47:12.241 22592-22592 Receiver                com.dspread.mdm.service              D  🔧 🔧 执行定期轮询任务
2025-08-11 14:47:12.245 22592-22592 Receiver                com.dspread.mdm.service              D  🔧 🔧 准备发送任务执行广播 (间隔: 7926795ms)
2025-08-11 14:47:12.249 22592-22592 Receiver                com.dspread.mdm.service              D  🔧 🔧 开始发送任务执行广播: com.dspread.mdm.service.WSTASK_EXEC_BC
2025-08-11 14:47:12.253 22592-22592 ContextImpl             com.dspread.mdm.service              W  Calling a method in the system process without a qualified user: android.app.ContextImpl.sendBroadcast:1220 android.content.ContextWrapper.sendBroadcast:504 com.dspread.mdm.service.receiver.manager.BroadcastSender.sendBroadcast:103 com.dspread.mdm.service.receiver.manager.BroadcastSender.sendBroadcast:27 com.dspread.mdm.service.receiver.manager.handlers.TimerEventHandlerImpl.sendTaskExecuteBroadcast:196 
2025-08-11 14:47:12.257 22592-22592 Receiver                com.dspread.mdm.service              D  🔧 发送广播: com.dspread.mdm.service.WSTASK_EXEC_BC
2025-08-11 14:47:12.260 22592-22592 Receiver                com.dspread.mdm.service              D  🔧 🔧 任务执行广播发送成功: com.dspread.mdm.service.WSTASK_EXEC_BC
2025-08-11 14:47:12.264 22592-22592 Receiver                com.dspread.mdm.service              D  🔧 🔧 发送任务执行广播完成 (间隔: 7926795ms)
2025-08-11 14:47:12.268 22592-22592 Receiver                com.dspread.mdm.service              D  🔧 检查待上传的任务和规则结果
2025-08-11 14:47:12.276 22592-22592 WebSocket               com.dspread.mdm.service              I  🔧 C0201 被动式上送: scheduled_report (被动: 13)
2025-08-11 14:47:12.300 22592-22592 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0201","serialNo":"01354090202503050399","request_time":"1754894832284","request_id":"1754894832284C0201","version":"1","data":{"unboxStatus":"normal","isInUse":"1","websocketConnected":true,"myVersionName":"1.0.08.20250808.DSPREAD.MDM.SERVICE","terminalDate":"20250811144712"},"myVersionName":"1.0.08.20250808.DSPREAD.MDM.SERVICE","terminalDate":"20250811144712"}
2025-08-11 14:47:12.304 22592-22592 WebSocket               com.dspread.mdm.service              I  🔧 C0201 设备状态上送成功: status=normal, inUse=true (1)
2025-08-11 14:47:12.308 22592-22592 Receiver                com.dspread.mdm.service              D  🔧 上报设备状态 (间隔: 7926795ms)
2025-08-11 14:47:12.312 22592-22592 Receiver                com.dspread.mdm.service              D  🔧 🔧 轮询任务执行完成
2025-08-11 14:47:12.318 22592-22592 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 收到广播: com.dspread.mdm.service.WSTASK_EXEC_BC
2025-08-11 14:47:12.322 22592-22592 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 分发广播事件: com.dspread.mdm.service.WSTASK_EXEC_BC -> 1个处理器
2025-08-11 14:47:12.333 22592-22592 Receiver                com.dspread.mdm.service              D  🔧 没有任务
2025-08-11 14:47:12.337 22592-22592 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 处理器 TaskExecuteEventHandler 处理成功: com.dspread.mdm.service.WSTASK_EXEC_BC
2025-08-11 14:47:49.748 22592-22592 Receiver                com.dspread.mdm.service              D  🔧 屏幕点亮
2025-08-11 14:48:12.335 22592-22592 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 收到广播: com.dspread.mdm.service.WSTASK_EXEC_BC
2025-08-11 14:48:12.348 22592-22592 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 分发广播事件: com.dspread.mdm.service.WSTASK_EXEC_BC -> 1个处理器
2025-08-11 14:48:12.368 22592-22592 Receiver                com.dspread.mdm.service              D  🔧 没有任务
2025-08-11 14:48:12.374 22592-22592 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 处理器 TaskExecuteEventHandler 处理成功: com.dspread.mdm.service.WSTASK_EXEC_BC
2025-08-11 14:48:35.437 22592-22592 Receiver                com.dspread.mdm.service              D  🔧 电源已连接
2025-08-11 14:48:35.466 22592-22592 WebSocket               com.dspread.mdm.service              I  🔧 C0202 被动式上送拒绝: device_usage_change (拒绝: 4)
2025-08-11 14:48:35.475 22592-22592 WebSocket               com.dspread.mdm.service              I  🔧 C0202 设备事件上送被流量控制阻止: 被动事件 'device_usage_change' 在平衡模式 - 重要变化下未启用
2025-08-11 14:48:35.486 22592-22592 WebSocket               com.dspread.mdm.service              I  🔧 C0902 主动式上送: charging_state_change (主动: 4)
2025-08-11 14:48:35.520 22592-22592 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0902","serialNo":"01354090202503050399","request_time":"1754894915498","request_id":"1754894915498C0902","version":"1","data":{"batteryLife":84,"batteryHealth":2,"temprature":"28.5","isCharging":"1","isLowBattery":"0","powerOff":"0","outOfBattery":"0"},"myVersionName":"1.0.08.20250808.DSPREAD.MDM.SERVICE","terminalDate":"20250811144835"}
2025-08-11 14:48:35.526 22592-22592 WebSocket               com.dspread.mdm.service              I  🔧 C0902 电池状态上送成功 (1, trigger=charging_state_change)
2025-08-11 14:48:40.673 22592-25797 WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 2
2025-08-11 14:49:12.360 22592-22592 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 收到广播: com.dspread.mdm.service.WSTASK_EXEC_BC
2025-08-11 14:49:12.364 22592-22592 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 分发广播事件: com.dspread.mdm.service.WSTASK_EXEC_BC -> 1个处理器
2025-08-11 14:49:12.374 22592-22592 Receiver                com.dspread.mdm.service              D  🔧 没有任务
2025-08-11 14:49:12.377 22592-22592 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 处理器 TaskExecuteEventHandler 处理成功: com.dspread.mdm.service.WSTASK_EXEC_BC
2025-08-11 14:49:40.674 22592-25797 WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 3
2025-08-11 14:50:12.375 22592-22592 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 收到广播: com.dspread.mdm.service.WSTASK_EXEC_BC
2025-08-11 14:50:12.382 22592-22592 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 分发广播事件: com.dspread.mdm.service.WSTASK_EXEC_BC -> 1个处理器
2025-08-11 14:50:12.400 22592-22592 Receiver                com.dspread.mdm.service              D  🔧 没有任务
2025-08-11 14:50:12.406 22592-22592 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 处理器 TaskExecuteEventHandler 处理成功: com.dspread.mdm.service.WSTASK_EXEC_BC
2025-08-11 14:50:40.675 22592-25797 WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 4
2025-08-11 14:51:12.397 22592-22592 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 收到广播: com.dspread.mdm.service.WSTASK_EXEC_BC
2025-08-11 14:51:12.404 22592-22592 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 分发广播事件: com.dspread.mdm.service.WSTASK_EXEC_BC -> 1个处理器
2025-08-11 14:51:12.422 22592-22592 Receiver                com.dspread.mdm.service              D  🔧 没有任务
2025-08-11 14:51:12.428 22592-22592 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 处理器 TaskExecuteEventHandler 处理成功: com.dspread.mdm.service.WSTASK_EXEC_BC
2025-08-11 14:51:40.676 22592-25797 WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 5
2025-08-11 14:52:12.419 22592-22592 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 收到广播: com.dspread.mdm.service.WSTASK_EXEC_BC
2025-08-11 14:52:12.427 22592-22592 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 分发广播事件: com.dspread.mdm.service.WSTASK_EXEC_BC -> 1个处理器
2025-08-11 14:52:12.444 22592-22592 Receiver                com.dspread.mdm.service              D  🔧 没有任务
2025-08-11 14:52:12.451 22592-22592 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 处理器 TaskExecuteEventHandler 处理成功: com.dspread.mdm.service.WSTASK_EXEC_BC
2025-08-11 14:52:40.676 22592-25797 WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 6
2025-08-11 14:53:12.442 22592-22592 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 收到广播: com.dspread.mdm.service.WSTASK_EXEC_BC
2025-08-11 14:53:12.449 22592-22592 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 分发广播事件: com.dspread.mdm.service.WSTASK_EXEC_BC -> 1个处理器
2025-08-11 14:53:12.465 22592-22592 Receiver                com.dspread.mdm.service              D  🔧 没有任务
2025-08-11 14:53:12.472 22592-22592 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 处理器 TaskExecuteEventHandler 处理成功: com.dspread.mdm.service.WSTASK_EXEC_BC
2025-08-11 14:53:40.677 22592-25797 WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 7
2025-08-11 14:54:12.464 22592-22592 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 收到广播: com.dspread.mdm.service.WSTASK_EXEC_BC
2025-08-11 14:54:12.471 22592-22592 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 分发广播事件: com.dspread.mdm.service.WSTASK_EXEC_BC -> 1个处理器
2025-08-11 14:54:12.488 22592-22592 Receiver                com.dspread.mdm.service              D  🔧 没有任务
2025-08-11 14:54:12.495 22592-22592 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 处理器 TaskExecuteEventHandler 处理成功: com.dspread.mdm.service.WSTASK_EXEC_BC
2025-08-11 14:54:40.677 22592-25797 WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 8
2025-08-11 14:55:12.486 22592-22592 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 收到广播: com.dspread.mdm.service.WSTASK_EXEC_BC
2025-08-11 14:55:12.493 22592-22592 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 分发广播事件: com.dspread.mdm.service.WSTASK_EXEC_BC -> 1个处理器
2025-08-11 14:55:12.510 22592-22592 Receiver                com.dspread.mdm.service              D  🔧 没有任务
2025-08-11 14:55:12.517 22592-22592 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 处理器 TaskExecuteEventHandler 处理成功: com.dspread.mdm.service.WSTASK_EXEC_BC
2025-08-11 14:55:40.678 22592-25797 WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 9
2025-08-11 14:56:12.508 22592-22592 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 收到广播: com.dspread.mdm.service.WSTASK_EXEC_BC
2025-08-11 14:56:12.515 22592-22592 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 分发广播事件: com.dspread.mdm.service.WSTASK_EXEC_BC -> 1个处理器
2025-08-11 14:56:12.533 22592-22592 Receiver                com.dspread.mdm.service              D  🔧 没有任务
2025-08-11 14:56:12.539 22592-22592 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 处理器 TaskExecuteEventHandler 处理成功: com.dspread.mdm.service.WSTASK_EXEC_BC
2025-08-11 14:56:40.679 22592-25797 WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 10
2025-08-11 14:57:12.532 22592-22592 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 收到广播: com.dspread.mdm.service.WSTASK_EXEC_BC
2025-08-11 14:57:12.539 22592-22592 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 分发广播事件: com.dspread.mdm.service.WSTASK_EXEC_BC -> 1个处理器
2025-08-11 14:57:12.557 22592-22592 Receiver                com.dspread.mdm.service              D  🔧 没有任务
2025-08-11 14:57:12.563 22592-22592 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 处理器 TaskExecuteEventHandler 处理成功: com.dspread.mdm.service.WSTASK_EXEC_BC
2025-08-11 14:57:40.680 22592-25797 WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 11
2025-08-11 14:58:12.556 22592-22592 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 收到广播: com.dspread.mdm.service.WSTASK_EXEC_BC
2025-08-11 14:58:12.563 22592-22592 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 分发广播事件: com.dspread.mdm.service.WSTASK_EXEC_BC -> 1个处理器
2025-08-11 14:58:12.580 22592-22592 Receiver                com.dspread.mdm.service              D  🔧 没有任务
2025-08-11 14:58:12.587 22592-22592 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 处理器 TaskExecuteEventHandler 处理成功: com.dspread.mdm.service.WSTASK_EXEC_BC
2025-08-11 14:58:40.681 22592-25797 WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 12
2025-08-11 14:59:12.578 22592-22592 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 收到广播: com.dspread.mdm.service.WSTASK_EXEC_BC
2025-08-11 14:59:12.585 22592-22592 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 分发广播事件: com.dspread.mdm.service.WSTASK_EXEC_BC -> 1个处理器
2025-08-11 14:59:12.603 22592-22592 Receiver                com.dspread.mdm.service              D  🔧 没有任务
2025-08-11 14:59:12.610 22592-22592 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 处理器 TaskExecuteEventHandler 处理成功: com.dspread.mdm.service.WSTASK_EXEC_BC
2025-08-11 14:59:40.683 22592-25797 WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 13
2025-08-11 15:00:12.599 22592-22592 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 收到广播: com.dspread.mdm.service.WSTASK_EXEC_BC
2025-08-11 15:00:12.606 22592-22592 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 分发广播事件: com.dspread.mdm.service.WSTASK_EXEC_BC -> 1个处理器
2025-08-11 15:00:12.623 22592-22592 Receiver                com.dspread.mdm.service              D  🔧 没有任务
2025-08-11 15:00:12.630 22592-22592 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 处理器 TaskExecuteEventHandler 处理成功: com.dspread.mdm.service.WSTASK_EXEC_BC
2025-08-11 15:00:40.684 22592-25797 WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 14
2025-08-11 15:01:12.622 22592-22592 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 收到广播: com.dspread.mdm.service.WSTASK_EXEC_BC
2025-08-11 15:01:12.629 22592-22592 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 分发广播事件: com.dspread.mdm.service.WSTASK_EXEC_BC -> 1个处理器
2025-08-11 15:01:12.646 22592-22592 Receiver                com.dspread.mdm.service              D  🔧 没有任务
2025-08-11 15:01:12.652 22592-22592 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 处理器 TaskExecuteEventHandler 处理成功: com.dspread.mdm.service.WSTASK_EXEC_BC
2025-08-11 15:01:40.685 22592-25797 WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 15
2025-08-11 15:02:12.644 22592-22592 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 收到广播: com.dspread.mdm.service.WSTASK_EXEC_BC
2025-08-11 15:02:12.651 22592-22592 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 分发广播事件: com.dspread.mdm.service.WSTASK_EXEC_BC -> 1个处理器
2025-08-11 15:02:12.668 22592-22592 Receiver                com.dspread.mdm.service              D  🔧 没有任务
2025-08-11 15:02:12.675 22592-22592 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 处理器 TaskExecuteEventHandler 处理成功: com.dspread.mdm.service.WSTASK_EXEC_BC
2025-08-11 15:02:40.686 22592-25797 WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 16
2025-08-11 15:03:12.665 22592-22592 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 收到广播: com.dspread.mdm.service.WSTASK_EXEC_BC
2025-08-11 15:03:12.672 22592-22592 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 分发广播事件: com.dspread.mdm.service.WSTASK_EXEC_BC -> 1个处理器
2025-08-11 15:03:12.689 22592-22592 Receiver                com.dspread.mdm.service              D  🔧 没有任务
2025-08-11 15:03:12.696 22592-22592 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 处理器 TaskExecuteEventHandler 处理成功: com.dspread.mdm.service.WSTASK_EXEC_BC
2025-08-11 15:03:40.687 22592-25797 WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 17
2025-08-11 15:04:12.689 22592-22592 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 收到广播: com.dspread.mdm.service.WSTASK_EXEC_BC
2025-08-11 15:04:12.696 22592-22592 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 分发广播事件: com.dspread.mdm.service.WSTASK_EXEC_BC -> 1个处理器
2025-08-11 15:04:12.714 22592-22592 Receiver                com.dspread.mdm.service              D  🔧 没有任务
2025-08-11 15:04:12.720 22592-22592 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 处理器 TaskExecuteEventHandler 处理成功: com.dspread.mdm.service.WSTASK_EXEC_BC
2025-08-11 15:04:24.200 22592-25796 WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误 (Ask Gemini)
                                                                                                    com.neovisionaries.ws.client.WebSocketException: An I/O error occurred while a frame was being read from the web socket: Connection timed out
                                                                                                    	at com.neovisionaries.ws.client.ReadingThread.readFrame(ReadingThread.java:375)
                                                                                                    	at com.neovisionaries.ws.client.ReadingThread.main(ReadingThread.java:99)
                                                                                                    	at com.neovisionaries.ws.client.ReadingThread.runMain(ReadingThread.java:64)
                                                                                                    	at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45)
                                                                                                    Caused by: java.net.SocketException: Connection timed out
                                                                                                    	at java.net.SocketInputStream.socketRead0(Native Method)
                                                                                                    	at java.net.SocketInputStream.socketRead(SocketInputStream.java:118)
                                                                                                    	at java.net.SocketInputStream.read(SocketInputStream.java:173)
                                                                                                    	at java.net.SocketInputStream.read(SocketInputStream.java:143)
                                                                                                    	at java.io.BufferedInputStream.fill(BufferedInputStream.java:239)
                                                                                                    	at java.io.BufferedInputStream.read1(BufferedInputStream.java:279)
                                                                                                    	at java.io.BufferedInputStream.read(BufferedInputStream.java:338)
                                                                                                    	at java.io.FilterInputStream.read(FilterInputStream.java:133)
                                                                                                    	at com.neovisionaries.ws.client.WebSocketInputStream.readBytes(WebSocketInputStream.java:165)
                                                                                                    	at com.neovisionaries.ws.client.WebSocketInputStream.readFrame(WebSocketInputStream.java:46)
                                                                                                    	at com.neovisionaries.ws.client.ReadingThread.readFrame(ReadingThread.java:338)
                                                                                                    	at com.neovisionaries.ws.client.ReadingThread.main(ReadingThread.java:99) 
                                                                                                    	at com.neovisionaries.ws.client.ReadingThread.runMain(ReadingThread.java:64) 
                                                                                                    	at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45) 
2025-08-11 15:04:24.207 22592-25796 WebSocket               com.dspread.mdm.service              I  🔧 onError: 不触发重连，等待 onDisconnected 处理
2025-08-11 15:04:24.218 22592-25797 WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误 (Ask Gemini)
                                                                                                    com.neovisionaries.ws.client.WebSocketException: Flushing frames to the server failed: Broken pipe
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.doFlush(WritingThread.java:434)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.sendFrames(WritingThread.java:384)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.main(WritingThread.java:108)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.runMain(WritingThread.java:55)
                                                                                                    	at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45)
                                                                                                    Caused by: java.net.SocketException: Broken pipe
                                                                                                    	at java.net.SocketOutputStream.socketWrite0(Native Method)
                                                                                                    	at java.net.SocketOutputStream.socketWrite(SocketOutputStream.java:116)
                                                                                                    	at java.net.SocketOutputStream.write(SocketOutputStream.java:156)
                                                                                                    	at java.io.BufferedOutputStream.flushBuffer(BufferedOutputStream.java:81)
                                                                                                    	at java.io.BufferedOutputStream.flush(BufferedOutputStream.java:142)
                                                                                                    	at java.io.BufferedOutputStream.flush(BufferedOutputStream.java:143)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.flush(WritingThread.java:271)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.doFlush(WritingThread.java:422)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.sendFrames(WritingThread.java:384) 
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.main(WritingThread.java:108) 
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.runMain(WritingThread.java:55) 
                                                                                                    	at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45) 
2025-08-11 15:04:24.225 22592-25797 WebSocket               com.dspread.mdm.service              I  🔧 onError: 不触发重连，等待 onDisconnected 处理
2025-08-11 15:04:24.235 22592-25797 WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误 (Ask Gemini)
                                                                                                    com.neovisionaries.ws.client.WebSocketException: Flushing frames to the server failed: Socket closed
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.doFlush(WritingThread.java:434)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.sendFrames(WritingThread.java:384)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.main(WritingThread.java:120)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.runMain(WritingThread.java:55)
                                                                                                    	at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45)
                                                                                                    Caused by: java.net.SocketException: Socket closed
                                                                                                    	at java.net.SocketOutputStream.socketWrite(SocketOutputStream.java:119)
                                                                                                    	at java.net.SocketOutputStream.write(SocketOutputStream.java:156)
                                                                                                    	at java.io.BufferedOutputStream.flushBuffer(BufferedOutputStream.java:81)
                                                                                                    	at java.io.BufferedOutputStream.flush(BufferedOutputStream.java:142)
                                                                                                    	at java.io.BufferedOutputStream.flush(BufferedOutputStream.java:143)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.flush(WritingThread.java:271)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.doFlush(WritingThread.java:422)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.sendFrames(WritingThread.java:384) 
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.main(WritingThread.java:120) 
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.runMain(WritingThread.java:55) 
                                                                                                    	at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45) 
2025-08-11 15:04:24.243 22592-25797 WebSocket               com.dspread.mdm.service              I  🔧 onError: 不触发重连，等待 onDisconnected 处理
2025-08-11 15:04:24.250 22592-25797 WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接断开 - 客户端主动断开
2025-08-11 15:04:24.257 22592-25797 WebSocket               com.dspread.mdm.service              I  🔧    服务器关闭帧: code=-1, reason=无原因
2025-08-11 15:04:24.263 22592-25797 WebSocket               com.dspread.mdm.service              I  🔧    客户端关闭帧: code=1008, reason=An I/O error occurred while a frame was being read from the web socket: Connection timed out
2025-08-11 15:04:24.269 22592-25797 WebSocket               com.dspread.mdm.service              I  🔧    断开发起方: 客户端
2025-08-11 15:04:24.276 22592-25797 WebSocket               com.dspread.mdm.service              I  🔧 reconnect() 被调用，调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onDisconnected:705
2025-08-11 15:04:24.281 22592-25797 WebSocket               com.dspread.mdm.service              I  🔧 开始重连 WebSocket... (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onDisconnected:705)
2025-08-11 15:04:24.292 22592-25797 WebSocket               com.dspread.mdm.service              I  🔧 使用Provisioning配置的重连策略: delayPolicy=30, delaySwitch=1, delayTime=900
2025-08-11 15:04:24.296 22592-25797 WebSocket               com.dspread.mdm.service              I  🔧 当前断开记录数: 4, 延迟开关: 1
2025-08-11 15:04:24.301 22592-25797 WebSocket               com.dspread.mdm.service              I  🔧 重连策略计算完成: 第1次重连，间隔3000ms (3秒)
2025-08-11 15:04:24.305 22592-25797 WebSocket               com.dspread.mdm.service              I  🔧 开始第1次重连，间隔3000ms (3秒) (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onDisconnected:705)
2025-08-11 15:04:27.330 22592-22592 WebSocket               com.dspread.mdm.service              I  🔧 重连 WebSocket: ws://35.75.3.206:8080/status/websocket/register?serialNo=MDEzNTQwOTAyMDI1MDMwNTAzOTk=&publicKey=TUlHZk1BMEdDU3FHU0liM0RRRUJBUVVBQTRHTkFEQ0JpUUtCZ1FDK09zN2VEZlBLOUcySnFoS29CME0rMmJha1VETEZhWUlYY3YxbGszWGZyeitjWndmNTJHSlQrL2lkWkFhVjBPRlpmOUwvR1pvc005eVYrYVpja0l1cnMwNDhJeXc1UlpBNHA1Ty9VaDRzMU55bnBRM21jV0gra3lBWXIvL1luOFBYeXdVQmZKYy9XTlBpTHB5NTd0OFhpVkhDYXVhQ2FpSEZZeW9paTZ0OEd3SURBUUFC&query=0&msgVer=3&timestamp=1754895867317&signature=iJGIgTgGoJCQt0mO39G+x9vyZjT1O89ItEcrUsq2PzoNXoQOciMzai0r/sU5sjopm1QceWH64ss+ni/2wPTwMte+Eyq4WFYWLasqkTvGxCoBhrTdqh00dz3IDK0jmECs9Mxo7OZZZqNQ1506mFn4HKoCcBNrme0niE4pXaF/Qww=
2025-08-11 15:04:27.338 22592-22592 WebSocket               com.dspread.mdm.service              I  🔧 未检测到系统代理设置
2025-08-11 15:04:27.352 22592-26110 TrafficStats            com.dspread.mdm.service              D  tagSocket(85) with statsTag=0xffffffff, statsUid=-1
2025-08-11 15:04:27.567 22592-26111 Common                  com.dspread.mdm.service              I  ✅ WebSocket 物理连接成功，等待服务器 hellMsg
2025-08-11 15:04:27.573 22592-26111 WebSocket               com.dspread.mdm.service              I  🔧 ===== 心跳发送器实际配置 =====
2025-08-11 15:04:27.578 22592-26111 WebSocket               com.dspread.mdm.service              I  🔧 实际 PING 发送间隔: 60000ms (60秒)
2025-08-11 15:04:27.584 22592-26111 WebSocket               com.dspread.mdm.service              I  🔧 PING 定时器名称: PingSender
2025-08-11 15:04:27.589 22592-26111 WebSocket               com.dspread.mdm.service              I  🔧 实际 PONG 发送间隔: 0ms (0秒)
2025-08-11 15:04:27.594 22592-26111 WebSocket               com.dspread.mdm.service              I  🔧 PONG 定时器名称: PongSender
2025-08-11 15:04:27.598 22592-26111 WebSocket               com.dspread.mdm.service              I  🔧 ===============================
2025-08-11 15:04:27.721 22592-26111 Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"hellMsg":"hello 01354090202503050399","tranCode":"S0000","version":"1","rebootTime":"03:05:02","serialNo":"01354090202503050399","deviceStatus":"6"}
2025-08-11 15:04:27.727 22592-26111 WebSocket               com.dspread.mdm.service              I  🔧 收到业务消息 - tranCode: S0000
2025-08-11 15:04:27.732 22592-26111 Common                  com.dspread.mdm.service              I  ✅ 收到服务器 hellMsg，WebSocket 业务连接成功！
2025-08-11 15:04:27.737 22592-26111 Common                  com.dspread.mdm.service              I  ✅ WebSocket 业务连接成功！
2025-08-11 15:04:27.742 22592-26111 WebSocket               com.dspread.mdm.service              I  🔧 业务连接成功，重连计数已重置为 0
2025-08-11 15:04:27.751 22592-26111 Common                  com.dspread.mdm.service              D  🔧 使用Provisioning配置的心跳间隔: 300秒
2025-08-11 15:04:27.755 22592-26111 Common                  com.dspread.mdm.service              D  🔧 启动轮询定时器，间隔: 300秒
2025-08-11 15:04:27.759 22592-26111 WebSocket               com.dspread.mdm.service              I  🔧 WebSocketCenter: 发送心跳
2025-08-11 15:04:27.762 22592-26111 WebSocket               com.dspread.mdm.service              I  🔧 发送心跳消息
2025-08-11 15:04:27.766 22592-26111 WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 已连接，执行轮询任务
2025-08-11 15:04:27.772 22592-26111 Common                  com.dspread.mdm.service              D  🔧 已安排下一次轮询: 300秒后
2025-08-11 15:04:27.775 22592-26111 WebSocket               com.dspread.mdm.service              I  🔧 已启动心跳轮询定时器
2025-08-11 15:04:27.778 22592-26111 Receiver                com.dspread.mdm.service              D  🔧 TaskExecutionTimer 启动任务执行定时器: 60s
2025-08-11 15:04:27.785 22592-26111 WebSocket               com.dspread.mdm.service              I  🔧 已启动任务执行定时器
2025-08-11 15:04:27.819 22592-26111 WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接成功，检查自身更新任务状态
2025-08-11 15:04:27.822 22592-26111 ContextImpl             com.dspread.mdm.service              W  Calling a method in the system process without a qualified user: android.app.ContextImpl.sendBroadcast:1220 android.content.ContextWrapper.sendBroadcast:504 com.dspread.mdm.service.receiver.manager.BroadcastSender.sendBroadcast:103 com.dspread.mdm.service.receiver.manager.BroadcastSender.sendBroadcast:27 com.dspread.mdm.service.network.websocket.connection.WsConnectionManager.checkSelfUpdateTaskStatus:312 
2025-08-11 15:04:27.825 22592-26111 Receiver                com.dspread.mdm.service              D  🔧 发送广播: com.dspread.mdm.service.CHECK_SELF_UPDATE_STATUS
2025-08-11 15:04:27.828 22592-26111 WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接成功
2025-08-11 15:04:27.832 22592-26111 WebSocket               com.dspread.mdm.service              I  🔧 设备状态: 6
2025-08-11 15:04:27.835 22592-26111 WebSocket               com.dspread.mdm.service              I  🔧 启动 C0109 定时器
2025-08-11 15:04:27.838 22592-26111 ContextImpl             com.dspread.mdm.service              W  Calling a method in the system process without a qualified user: android.app.ContextImpl.sendBroadcast:1220 android.content.ContextWrapper.sendBroadcast:504 com.dspread.mdm.service.receiver.manager.BroadcastSender.sendBroadcast:103 com.dspread.mdm.service.receiver.manager.BroadcastSender.sendBroadcast:27 com.dspread.mdm.service.network.websocket.message.WsMessageCenter.startTerminalInfoUploadTimer:389 
2025-08-11 15:04:27.840 22592-26111 Receiver                com.dspread.mdm.service              D  🔧 发送广播: com.dspread.mdm.service.TER_INFO_UPLOAD_BC
2025-08-11 15:04:27.843 22592-26111 WebSocket               com.dspread.mdm.service              I  🔧 检查首次连接标志: isFirstTimeWebSocketConnected=false
2025-08-11 15:04:27.846 22592-26111 WebSocket               com.dspread.mdm.service              I  🔧 非首次连接，跳过初始化信息发送和初始化检查
2025-08-11 15:04:27.849 22592-26111 WebSocket               com.dspread.mdm.service              I  🔧 执行必要的状态同步检查
2025-08-11 15:04:27.852 22592-26111 WebSocket               com.dspread.mdm.service              I  🔧 检查服务更新结果
2025-08-11 15:04:27.855 22592-26111 Task                    com.dspread.mdm.service              I  ℹ️ 📋 本地任务列表长度: 0
2025-08-11 15:04:27.859 22592-26111 Task                    com.dspread.mdm.service              D  🔧 📋 本地任务列表内容: []
2025-08-11 15:04:27.862 22592-26111 Task                    com.dspread.mdm.service              I  ℹ️ 当前应用版本: versionCode=11, versionName=1.0.08.20250808.DSPREAD.MDM.SERVICE
2025-08-11 15:04:27.865 22592-26111 Task                    com.dspread.mdm.service              I  ℹ️ 📋 任务列表数量: 0
2025-08-11 15:04:27.868 22592-26111 WebSocket               com.dspread.mdm.service              I  🔧 检查OS升级状态...
2025-08-11 15:04:27.871 22592-26111 Common                  com.dspread.mdm.service              D  🔧 OsUpdateStatusChecker WebSocket连接成功，检查OS升级状态
2025-08-11 15:04:27.874 22592-26111 Common                  com.dspread.mdm.service              D  🔧 OsUpdateStatusChecker 没有待检查的OS升级任务
2025-08-11 15:04:27.877 22592-26111 WebSocket               com.dspread.mdm.service              I  🔧 OS升级状态检查已触发
2025-08-11 15:05:27.569 22592-26112 WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 1
2025-08-11 15:05:27.660 22592-26111 WebSocket               com.dspread.mdm.service              I  🔧 收到服务器 PONG 帧: 1
2025-08-11 15:05:27.788 22592-22592 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 收到广播: com.dspread.mdm.service.WSTASK_EXEC_BC
2025-08-11 15:05:27.795 22592-22592 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 分发广播事件: com.dspread.mdm.service.WSTASK_EXEC_BC -> 1个处理器
2025-08-11 15:05:27.812 22592-22592 Receiver                com.dspread.mdm.service              D  🔧 没有任务
2025-08-11 15:05:27.819 22592-22592 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 处理器 TaskExecuteEventHandler 处理成功: com.dspread.mdm.service.WSTASK_EXEC_BC
2025-08-11 15:06:27.570 22592-26112 WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 2
2025-08-11 15:06:27.662 22592-26111 WebSocket               com.dspread.mdm.service              I  🔧 收到服务器 PONG 帧: 2
2025-08-11 15:06:27.810 22592-22592 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 收到广播: com.dspread.mdm.service.WSTASK_EXEC_BC
2025-08-11 15:06:27.819 22592-22592 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 分发广播事件: com.dspread.mdm.service.WSTASK_EXEC_BC -> 1个处理器
2025-08-11 15:06:27.839 22592-22592 Receiver                com.dspread.mdm.service              D  🔧 没有任务
2025-08-11 15:06:27.844 22592-22592 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 处理器 TaskExecuteEventHandler 处理成功: com.dspread.mdm.service.WSTASK_EXEC_BC
2025-08-11 15:07:27.571 22592-26112 WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 3
2025-08-11 15:07:27.668 22592-26111 WebSocket               com.dspread.mdm.service              I  🔧 收到服务器 PONG 帧: 3
2025-08-11 15:07:27.837 22592-22592 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 收到广播: com.dspread.mdm.service.WSTASK_EXEC_BC
2025-08-11 15:07:27.844 22592-22592 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 分发广播事件: com.dspread.mdm.service.WSTASK_EXEC_BC -> 1个处理器
2025-08-11 15:07:27.861 22592-22592 Receiver                com.dspread.mdm.service              D  🔧 没有任务
2025-08-11 15:07:27.868 22592-22592 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 处理器 TaskExecuteEventHandler 处理成功: com.dspread.mdm.service.WSTASK_EXEC_BC
2025-08-11 15:08:27.572 22592-26112 WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 4
2025-08-11 15:08:27.754 22592-26111 WebSocket               com.dspread.mdm.service              I  🔧 收到服务器 PONG 帧: 4
2025-08-11 15:08:27.858 22592-22592 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 收到广播: com.dspread.mdm.service.WSTASK_EXEC_BC
2025-08-11 15:08:27.865 22592-22592 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 分发广播事件: com.dspread.mdm.service.WSTASK_EXEC_BC -> 1个处理器
2025-08-11 15:08:27.882 22592-22592 Receiver                com.dspread.mdm.service              D  🔧 没有任务
2025-08-11 15:08:27.889 22592-22592 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 处理器 TaskExecuteEventHandler 处理成功: com.dspread.mdm.service.WSTASK_EXEC_BC
2025-08-11 15:09:27.573 22592-26112 WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 5
2025-08-11 15:09:27.669 22592-26111 WebSocket               com.dspread.mdm.service              I  🔧 收到服务器 PONG 帧: 5
2025-08-11 15:09:27.775 22592-22592 Receiver                com.dspread.mdm.service              D  🔧 🔧 TimerReceiver 收到广播: com.dspread.mdm.service.POLL_TIMER_START
2025-08-11 15:09:27.781 22592-22592 Receiver                com.dspread.mdm.service              D  🔧 🔧 处理轮询定时器启动
2025-08-11 15:09:27.788 22592-22592 Receiver                com.dspread.mdm.service              D  🔧 🔧 WebSocket连接状态: true
2025-08-11 15:09:27.794 22592-22592 Receiver                com.dspread.mdm.service              D  🔧 🔧 发送心跳
2025-08-11 15:09:27.801 22592-22592 WebSocket               com.dspread.mdm.service              I  🔧 WebSocketCenter: 发送心跳
2025-08-11 15:09:27.808 22592-22592 WebSocket               com.dspread.mdm.service              I  🔧 发送心跳消息
2025-08-11 15:09:27.815 22592-22592 Receiver                com.dspread.mdm.service              D  🔧 🔧 开始执行轮询任务
2025-08-11 15:09:27.821 22592-22592 Receiver                com.dspread.mdm.service              D  🔧 🔧 执行定期轮询任务
2025-08-11 15:09:27.827 22592-22592 Receiver                com.dspread.mdm.service              D  🔧 🔧 准备发送任务执行广播 (间隔: 1335581ms)
2025-08-11 15:09:27.833 22592-22592 Receiver                com.dspread.mdm.service              D  🔧 🔧 开始发送任务执行广播: com.dspread.mdm.service.WSTASK_EXEC_BC
2025-08-11 15:09:27.839 22592-22592 ContextImpl             com.dspread.mdm.service              W  Calling a method in the system process without a qualified user: android.app.ContextImpl.sendBroadcast:1220 android.content.ContextWrapper.sendBroadcast:504 com.dspread.mdm.service.receiver.manager.BroadcastSender.sendBroadcast:103 com.dspread.mdm.service.receiver.manager.BroadcastSender.sendBroadcast:27 com.dspread.mdm.service.receiver.manager.handlers.TimerEventHandlerImpl.sendTaskExecuteBroadcast:196 
2025-08-11 15:09:27.842 22592-22592 Receiver                com.dspread.mdm.service              D  🔧 发送广播: com.dspread.mdm.service.WSTASK_EXEC_BC
2025-08-11 15:09:27.848 22592-22592 Receiver                com.dspread.mdm.service              D  🔧 🔧 任务执行广播发送成功: com.dspread.mdm.service.WSTASK_EXEC_BC
2025-08-11 15:09:27.852 22592-22592 Receiver                com.dspread.mdm.service              D  🔧 🔧 发送任务执行广播完成 (间隔: 1335581ms)
2025-08-11 15:09:27.856 22592-22592 Receiver                com.dspread.mdm.service              D  🔧 检查待上传的任务和规则结果
2025-08-11 15:09:27.866 22592-22592 WebSocket               com.dspread.mdm.service              I  🔧 C0201 被动式上送: scheduled_report (被动: 14)
2025-08-11 15:09:27.888 22592-22592 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0201","serialNo":"01354090202503050399","request_time":"1754896167873","request_id":"1754896167873C0201","version":"1","data":{"unboxStatus":"normal","isInUse":"1","websocketConnected":true,"myVersionName":"1.0.08.20250808.DSPREAD.MDM.SERVICE","terminalDate":"20250811150927"},"myVersionName":"1.0.08.20250808.DSPREAD.MDM.SERVICE","terminalDate":"20250811150927"}
2025-08-11 15:09:27.891 22592-22592 WebSocket               com.dspread.mdm.service              I  🔧 C0201 设备状态上送成功: status=normal, inUse=true (1)
2025-08-11 15:09:27.894 22592-22592 Receiver                com.dspread.mdm.service              D  🔧 上报设备状态 (间隔: 1335581ms)
2025-08-11 15:09:27.897 22592-22592 Receiver                com.dspread.mdm.service              D  🔧 🔧 轮询任务执行完成
2025-08-11 15:09:27.902 22592-22592 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 收到广播: com.dspread.mdm.service.WSTASK_EXEC_BC
2025-08-11 15:09:27.906 22592-22592 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 分发广播事件: com.dspread.mdm.service.WSTASK_EXEC_BC -> 1个处理器
2025-08-11 15:09:27.914 22592-22592 Receiver                com.dspread.mdm.service              D  🔧 没有任务
2025-08-11 15:09:27.917 22592-22592 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 处理器 TaskExecuteEventHandler 处理成功: com.dspread.mdm.service.WSTASK_EXEC_BC
2025-08-11 15:09:27.920 22592-22592 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 收到广播: com.dspread.mdm.service.WSTASK_EXEC_BC
2025-08-11 15:09:27.924 22592-22592 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 分发广播事件: com.dspread.mdm.service.WSTASK_EXEC_BC -> 1个处理器
2025-08-11 15:09:27.932 22592-22592 Receiver                com.dspread.mdm.service              D  🔧 没有任务
2025-08-11 15:09:27.935 22592-22592 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 处理器 TaskExecuteEventHandler 处理成功: com.dspread.mdm.service.WSTASK_EXEC_BC
