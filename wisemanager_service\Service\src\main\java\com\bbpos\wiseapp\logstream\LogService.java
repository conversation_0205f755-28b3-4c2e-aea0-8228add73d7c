package com.bbpos.wiseapp.logstream;

import android.annotation.SuppressLint;
import android.app.AlarmManager;
import android.app.PendingIntent;
import android.app.Service;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.os.Build;
import android.os.Environment;
import android.os.Handler;
import android.os.IBinder;
import android.os.Looper;
import android.text.TextUtils;
import android.util.Log;
import android.widget.Toast;

import com.bbpos.wiseapp.logger.BBLog;
import com.bbpos.wiseapp.logger.LLQueue;
import com.bbpos.wiseapp.security.SecurityOperate;
import com.bbpos.wiseapp.service.common.RequestPermission;
import com.bbpos.wiseapp.service.common.UsualData;
import com.bbpos.wiseapp.system.api.DeviceInfoApi;
import com.bbpos.wiseapp.tms.utils.ContextUtil;
import com.bbpos.wiseapp.tms.utils.IOUtils;
import com.bbpos.wiseapp.utils.FileUtils;
import com.bbpos.wiseapp.utils.HttpUtils;
import com.bbpos.wiseapp.utils.SharedPreferencesUtils;
import com.bbpos.wiseapp.zip.GzipJava;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.RandomAccessFile;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Comparator;
import java.util.Date;
import java.util.List;

public class LogService extends Service {
    //日志
    private static final String TAG = BBLog.TAG;//"WiseApp2.0 LogService";
    public static boolean isLogExecuting = false;
    private static boolean uploadLogGZFile = false;  //是否需要上传log

    private static final int MEMORY_LOG_DIR_MAX_SIZE = 1000 * 1024 * 1024;
    //内存中日志文件最大值，10M
    private static final int MEMORY_LOG_FILE_MAX_SIZE = 50 * 1024 * 1024;
    private static final int RECENT_LOG_MAX_SIZE = 256 * 1024;
    private static final int MEMORY_LOG_GZ_MAX_COUNT = 5;
    //根据recent文件要求的大小设置对应的时间长度，保证logcat缓存及时全部写入文件中
    private static final int MEMORY_LOG_FILE_MONITOR_INTERVAL_256KB = 10 * 1000;
    //内存中的日志文件大小监控时间间隔，1分钟
    private static final int MEMORY_LOG_FILE_MONITOR_INTERVAL = 1 * 60 * 1000;
    //sd卡中日志文件的最多保存天数
    private static final int SDCARD_LOG_FILE_SAVE_DAYS = 3;
    //日志文件在sdcard中的路径【主要这个很重要】
    private static String LOG_PATH_SDCARD_DIR;
    private static String LOG_UPLOAD_PATH_SDCARD_DIR;
    private static String LOG_UPLOAD_PATH_SDCARD_GZ_FILE_RECORD;
    private static String LOG_UPLOAD_PATH_SDCARD_LOG_FILE_RECORD;
    private static String UPLOAD_DIR = "upload";
    //如果当前的日志写在内存中，记录当前的日志文件名称
    private static String CURR_INSTALL_LOG_NAME;

    private static String FIRST_TIME_TEMP_SAVE_LOG_NAME = "temporary.txt";
    //本服务输出的日志文件名称，也可以是txt格式，或者后缀名是.log
    private String logGZQueueFileName = "LogGZUploadQueue.txt";
    private String logQueueFileName = "LogUploadQueue.txt";
    private static String logTemporaryFileName = "temporary.txt";

    @SuppressLint("SimpleDateFormat")
    private SimpleDateFormat myLogSdf = new SimpleDateFormat("yyyyMMdd-HHmmss");
    //日志名称格式
    @SuppressLint("SimpleDateFormat")
    private static SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmm");
    private static SimpleDateFormat sdftime = new SimpleDateFormat("yyyyMMddHHmm");

    private Process process;

    private LogTaskReceiver logTaskReceiver;

    /* 是否正在监测日志文件大小；
     * 如果当前日志记录在SDard中则为false
     * 如果当前日志记录在内存中则为true*/
    private boolean logSizeMoniting = false;
    private boolean firstTime = true;
    private boolean firstTimeSaveLog = true;
    private boolean firstTimeCheckSize = true;

    //日志文件监测action
    private static String MONITOR_LOG_SIZE_ACTION = "MONITOR_LOG_SIZE";
    //切换日志文件action
    private static String SWITCH_LOG_FILE_ACTION = "SWITCH_LOG_FILE_ACTION";
    //切换日志文件action
    private static String START_LOG_COLLECTOR_ACTION = "START_LOG_COLLECTOR_ACTION";
    private boolean uploadRecent = false;

    /**
     * 无需绑定
     * @param intent            intent
     * @return                  IBinder对象
     */
    @Override
    public IBinder onBind(Intent intent) {
        return null;
    }

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        if (intent != null) {
            uploadRecent = intent.getBooleanExtra("uploadRecent", false);
        }
        return super.onStartCommand(intent, flags, startId);
    }

    /**
     * 销毁时调用该方法
     */
    @Override
    public void onDestroy() {
        super.onDestroy();
        recordLogServiceLog("LogService onDestroy");
        isLogExecuting = false;

        try {
            uploadCurrentLog();
        } catch (IOException e) {
            e.printStackTrace();
        }

        try {
            mLogQueue.saveToFile(LOG_UPLOAD_PATH_SDCARD_LOG_FILE_RECORD);
            mLogQueueGz.saveToFile(LOG_UPLOAD_PATH_SDCARD_GZ_FILE_RECORD);
        } catch (IOException e) {
            e.printStackTrace();
        }
        if (process != null) {
            process.destroy();
        }

        //注意需要注销广播
        if(logTaskReceiver!=null){
            unregisterReceiver(logTaskReceiver);
        }
        cancelLogSizeMonitorTask();
    }


    /**
     * 每次开启service时，都会调用一次该方法，用于初始化
     */
    @Override
    public void onCreate() {
        super.onCreate();
        new Handler(Looper.getMainLooper()).post(new Runnable() {
            @Override
            public void run() {
                Toast.makeText(ContextUtil.getInstance(), "Start LogCollector...", Toast.LENGTH_LONG).show();
            }
        });

        try {
            init();
            register();
//        deploySwitchLogFileTask();
            isLogExecuting = true;
            new LogCollectorThread().start();
            new LogUploadThread().start();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void init(){
        //日志文件在sdcard中的路径
        File sdCardPath = null;
        boolean sdCardExist = Environment.getExternalStorageState().equals(Environment.MEDIA_MOUNTED);
        if(sdCardExist) {
            sdCardPath = Environment.getExternalStorageDirectory();//获取跟目录
        }
        if(sdCardPath!=null) {
            LOG_PATH_SDCARD_DIR = sdCardPath.getAbsolutePath() + File.separator + "log";
            LOG_UPLOAD_PATH_SDCARD_DIR = sdCardPath.getAbsolutePath() + File.separator + "log" + File.separator + UPLOAD_DIR;
            LOG_UPLOAD_PATH_SDCARD_GZ_FILE_RECORD = sdCardPath.getAbsolutePath() + File.separator + "log" + File.separator + UPLOAD_DIR + File.separator + logGZQueueFileName;
            LOG_UPLOAD_PATH_SDCARD_LOG_FILE_RECORD = sdCardPath.getAbsolutePath() + File.separator + "log" + File.separator + UPLOAD_DIR + File.separator + logQueueFileName;
            //创建log文件夹
            createLogDir();
            initLogQueue();
            sdf.setLenient(false);
            sdftime.setLenient(false);
            myLogSdf.setLenient(false);
            //当前的日志记录类型
            BBLog.w(TAG, "日志上送功能啓動LogService onCreate");
        }
    }


    private void register(){
        IntentFilter logTaskFilter = new IntentFilter();
        logTaskFilter.addAction(MONITOR_LOG_SIZE_ACTION);
        logTaskFilter.addAction(START_LOG_COLLECTOR_ACTION);
        logTaskFilter.addAction(Intent.ACTION_SHUTDOWN);
//        logTaskFilter.addAction(SWITCH_LOG_FILE_ACTION);
        logTaskReceiver = new LogTaskReceiver();
        registerReceiver(logTaskReceiver, logTaskFilter, RequestPermission.REQUEST_PERMISSION_MY_BROADCAST, null);
    }

    /**
     * 部署日志切换任务，每天凌晨切换日志文件
     */
    private void deploySwitchLogFileTask() {
        Intent intent = new Intent(SWITCH_LOG_FILE_ACTION);
        PendingIntent sender = PendingIntent.getBroadcast(this, 0, intent, 0);
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_MONTH, 1);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);

        // 部署任务
        AlarmManager am = (AlarmManager) getSystemService(ALARM_SERVICE);
        if (am != null) {
            am.setRepeating(AlarmManager.RTC_WAKEUP, calendar.getTimeInMillis(), AlarmManager.INTERVAL_DAY, sender);
        }
        recordLogServiceLog("deployNextTask success,next task time is:"+myLogSdf.format(calendar.getTime()));
    }

    /**
     * 日志收集
     * 1.清除日志缓存
     * 2.杀死应用程序已开启的Logcat进程防止多个进程写入一个日志文件
     * 3.开启日志收集进程
     * 4.处理日志文件，移动 OR 删除
     */
    class LogCollectorThread extends Thread {
        LogCollectorThread(){
            super("LogCollectorThread");
            BBLog.d(TAG, "LogCollectorThread is create");
        }

        @SuppressLint("WakelockTimeout")
        @Override
        public void run() {
            try {
                //唤醒手机
                //每次记录日志之前先清除日志的缓存, 不然会在两个日志文件中记录重复的日志
//                BBLog.w(TAG, "firstTime = " + firstTime);
                if (firstTime) {
                    firstTime = false;
                    BBLog.w(TAG, "首次不清除掉前面的Logcat");
                } else {
                    clearLogCache();
                }
                //运行PS命令得到进程信息
                List<String> orgProcessList = getAllProcess();
                //根据ps命令得到的内容获取PID，User，name等信息
                List<ProcessInfo> processInfoList = getProcessInfoList(orgProcessList);
                //关闭由本程序开启的logcat进程
                killLogcatPro(processInfoList);
                //开始收集日志信息
                createLogCollector();
                //休眠，创建文件，然后处理文件，不然该文件还没创建，会影响文件删除
                Thread.sleep(1000);
                //处理日志文件，这里面主要是处理sd卡和内存卡切换存储日志逻辑
                handleLog();
                //释放
            } catch (Exception e) {
                e.printStackTrace();
                recordLogServiceLog(Log.getStackTraceString(e));
            }
        }
    }

    /**
     * 每次记录日志之前先清除日志的缓存, 不然会在两个日志文件中记录重复的日志
     */
    private void clearLogCache() {
        BBLog.w(TAG, "清除掉前面的Logcat，接着记录");
        Process pro = null;
        List<String> commandList = new ArrayList<>();
        commandList.add("logcat");
        commandList.add("-c");
        try {
            pro = Runtime.getRuntime().exec(commandList.toArray(new String[commandList.size()]));
            StreamConsumer errorGobbler = new StreamConsumer(pro.getErrorStream());
            StreamConsumer outputGobbler = new StreamConsumer(pro.getInputStream());
            errorGobbler.start();
            outputGobbler.start();
            if (pro.waitFor() != 0) {
                BBLog.e(TAG, " clearLogCache proc.waitFor() != 0");
                recordLogServiceLog("clearLogCache clearLogCache proc.waitFor() != 0");
            }
        } catch (Exception e) {
            BBLog.e(TAG, "clearLogCache failed", e);
            recordLogServiceLog("clearLogCache failed");
        } finally {
            try {
                if (pro != null) {
                    pro.destroy();
                }
            } catch (Exception e) {
                BBLog.e(TAG, "clearLogCache failed", e);
                recordLogServiceLog("clearLogCache failed");
            }
        }
    }

    /**
     * 关闭由本程序开启的logcat进程：
     * 根据用户名称杀死进程(如果是本程序进程开启的Logcat收集进程那么两者的USER一致)
     * 如果不关闭会有多个进程读取logcat日志缓存信息写入日志文件
     * @param allPro                        allPro
     */
    private void killLogcatPro(List<ProcessInfo> allPro) {
        if(process != null){
            process.destroy();
        }
        String packName = this.getPackageName();
        //获取本程序的用户名称
        String myUser = getAppUser(packName, allPro);

        for (ProcessInfo processInfo : allPro) {
            if (processInfo.name.toLowerCase().equals("logcat") && processInfo.user.equals(myUser)) {
                android.os.Process.killProcess(Integer.parseInt(processInfo.pid));
                recordLogServiceLog("===== kill another logcat process success,the process info is:"
                      + processInfo);
            }
        }
    }

    /**
     * 获取本程序的用户名称
     * @param packName                              packName
     * @param allProList                            allProList
     * @return                                      程序名称
     */
    private String getAppUser(String packName, List<ProcessInfo> allProList) {
        for (ProcessInfo processInfo : allProList) {
            if (processInfo.name.equals(packName)) {
                return processInfo.user;
            }
        }
        return null;
    }

    /**
     * 根据ps命令得到的内容获取PID，User，name等信息
     * @param orgProcessList                orgProcessList
     * @return                              集合
     */
    private List<ProcessInfo> getProcessInfoList(List<String> orgProcessList) {
        List<ProcessInfo> proInfoList = new ArrayList<>();
        for (int i = 1; i < orgProcessList.size(); i++) {
            String processInfo = orgProcessList.get(i);
            String[] proStr = processInfo.split(" ");
            // USER PID PPID VSIZE RSS WCHAN PC NAME
            // root 1 0 416 300 c00d4b28 0000cd5c S /init
            List<String> orgInfo = new ArrayList<>();
            for (String str : proStr) {
                if (!"".equals(str)) {
                    orgInfo.add(str);
                }
            }
            if (orgInfo.size() == 9) {
                ProcessInfo pInfo = new ProcessInfo();
                pInfo.user = orgInfo.get(0);
                pInfo.pid = orgInfo.get(1);
                pInfo.ppid = orgInfo.get(2);
                pInfo.name = orgInfo.get(8);
                proInfoList.add(pInfo);
            }
        }
        return proInfoList;
    }

    /**
     * 运行PS命令得到进程信息
     * @return
     *          USER PID PPID VSIZE RSS WCHAN PC NAME
     *          root 1 0 416 300 c00d4b28 0000cd5c S /init
     */
    private List<String> getAllProcess() {
        List<String> orgProList = new ArrayList<>();
        Process pro = null;
        try {
//            pro = Runtime.getRuntime().exec("ps");
            pro = SecurityOperate.getInstance().execCommand(ContextUtil.getInstance(),"ps");
            StreamConsumer errorConsumer = new StreamConsumer(pro.getErrorStream());
            StreamConsumer outputConsumer = new StreamConsumer(pro.getInputStream(), orgProList);
            errorConsumer.start();
            outputConsumer.start();
            if (pro.waitFor() != 0) {
                BBLog.e(TAG, "getAllProcess pro.waitFor() != 0");
                recordLogServiceLog("getAllProcess pro.waitFor() != 0");
            }
        } catch (Exception e) {
            BBLog.e(TAG, "getAllProcess failed", e);
            recordLogServiceLog("getAllProcess failed");
        } finally {
            try {
                if (pro != null) {
                    pro.destroy();
                }
            } catch (Exception e) {
                BBLog.e(TAG, "getAllProcess failed", e);
                recordLogServiceLog("getAllProcess failed");
            }
        }
        return orgProList;
    }

    /**
     * 开始收集日志信息
     * 日志包括，f，d，v，time，
     */
    public void createLogCollector() {
        // 日志文件名称
        String logFileName;
        if (firstTimeSaveLog && uploadRecent) {
            firstTimeSaveLog = false;
            createLogDir();
            logFileName = LOG_PATH_SDCARD_DIR + File.separator + logTemporaryFileName;
            FIRST_TIME_TEMP_SAVE_LOG_NAME = logTemporaryFileName;
            File file = new File(logFileName);
            if (file.exists()) {
                file.delete();
            }
            recordLogServiceLog("*** 首次开始记录日志 ***,and log name is:"+logFileName);
        } else {
            logFileName = getLogPath();
            recordLogServiceLog("*** 重新开始记录日志 ***,and log name is:"+logFileName);
        }
        List<String> commandList = new ArrayList<>();
        commandList.add("logcat");
//        commandList.add("-d");
        commandList.add("-f");
        commandList.add(logFileName);
        commandList.add("WA2.0_WiseApp2.0:S");
        commandList.add("chenxinggsm:S");
        commandList.add("GnssLocationProvider:S");
//        commandList.add("| grep -v WA2.0_");
//        commandList.add(">");
//        commandList.add(logFileName);
//        commandList.add("-v");
//        commandList.add("threadtime");

        // 过滤所有的i信息
//        commandList.add("*:I");

        // 过滤所有的错误信息
        //commandList.add("*:E");

        // 过滤指定TAG的信息
        // commandList.add("MyAPP:V");
        // commandList.add("*:S");
        try {
//            process = Runtime.getRuntime().exec(String.format("logcat | grep -v WA2.0_", logFileName));
            process = Runtime.getRuntime().exec(commandList.toArray(new String[0]));
            if (!logFileName.contains(FIRST_TIME_TEMP_SAVE_LOG_NAME)) {
                mLogQueue.enQueue(logFileName); //文件名先進隊列，避免關機時情況，日志文件沒記錄；直接斷電無法解決
                BBLog.i(TAG, "LogService 添加到log記錄日志隊列 當前條數：" + mLogQueue.queueLength());
            }
            mLogQueue.saveToFile(LOG_UPLOAD_PATH_SDCARD_LOG_FILE_RECORD);
            // process.waitFor();
        } catch (Exception e) {
            BBLog.e(TAG, "CollectorThread == >" + e.getMessage(), e);
            recordLogServiceLog("CollectorThread == >" + e.getMessage());
        }
    }

    /**
     * 根据当前的存储位置得到日志的绝对存储路径
     * @return                      路径
     */
    public String getLogPath(){
        createLogDir();
        // 日志文件名称
        String logFileName = sdf.format(new Date()) + ".log";
        CURR_INSTALL_LOG_NAME = logFileName;
        BBLog.d(TAG, "Log stored in SDcard, the path is:"+LOG_PATH_SDCARD_DIR + File.separator + logFileName);
        return LOG_PATH_SDCARD_DIR + File.separator + logFileName;
    }

    /**
     * 处理日志文件
     * 1.如果日志文件存储位置切换到内存中，删除除了正在写的日志文件
     *   并且部署日志大小监控任务，控制日志大小不超过规定值
     * 2.如果日志文件存储位置切换到SDCard中，删除7天之前的日志，移
     *     动所有存储在内存中的日志到SDCard中，并将之前部署的日志大小
     *   监控取消
     */
    public void handleLog(){
        //取消部署日志大小监控任务
        deployLogSizeMonitorTask();
        //删除内存下过期的日志
        try {
            deleteSDCardExpiredLogEx();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 部署日志大小监控任务
     */
    private void deployLogSizeMonitorTask() {
        //如果当前正在监控着，则不需要继续部署
        if(logSizeMoniting){
            return;
        }
        logSizeMoniting = true;
        Intent intent = new Intent(MONITOR_LOG_SIZE_ACTION);
        PendingIntent sender = PendingIntent.getBroadcast(this, 0, intent, 0);
        AlarmManager am = (AlarmManager) getSystemService(ALARM_SERVICE);
        if (am != null) {
            am.cancel(sender);
            if (firstTimeCheckSize) {
//                am.setRepeating(AlarmManager.RTC_WAKEUP, System.currentTimeMillis(), MEMORY_LOG_FILE_MONITOR_INTERVAL_256KB, sender);
                if (Build.VERSION.SDK_INT < Build.VERSION_CODES.KITKAT) {
                    am.set(AlarmManager.RTC_WAKEUP, System.currentTimeMillis()+MEMORY_LOG_FILE_MONITOR_INTERVAL_256KB, sender);
                } else {
                    am.setExact(AlarmManager.RTC_WAKEUP, System.currentTimeMillis()+MEMORY_LOG_FILE_MONITOR_INTERVAL_256KB, sender);
                }
            } else {
//                am.setRepeating(AlarmManager.RTC_WAKEUP, System.currentTimeMillis(), MEMORY_LOG_FILE_MONITOR_INTERVAL, sender);
                if (Build.VERSION.SDK_INT < Build.VERSION_CODES.KITKAT) {
                    am.set(AlarmManager.RTC_WAKEUP, System.currentTimeMillis()+MEMORY_LOG_FILE_MONITOR_INTERVAL, sender);
                } else {
                    am.setExact(AlarmManager.RTC_WAKEUP, System.currentTimeMillis()+MEMORY_LOG_FILE_MONITOR_INTERVAL, sender);
                }
            }
        }
        BBLog.d(TAG, "deployLogSizeMonitorTask() suc !");
        //recordLogServiceLog("deployLogSizeMonitorTask() succ ,start time is " + calendar.getTime().toLocaleString());
    }

    /**
     * 取消部署日志大小监控任务
     */
    private void cancelLogSizeMonitorTask() {
        logSizeMoniting = false;
        AlarmManager am = (AlarmManager) getSystemService(ALARM_SERVICE);
        Intent intent = new Intent(MONITOR_LOG_SIZE_ACTION);
        PendingIntent sender = PendingIntent.getBroadcast(this, 0, intent, 0);
        if (am != null) {
            am.cancel(sender);
        }
        BBLog.d(TAG, "canelLogSizeMonitorTask() suc");
    }

    /**
     * 检查日志文件大小是否超过了规定大小
     * 如果超过了重新开启一个日志收集进程
     */
    private void checkLogSize(){
        //当文件不为空时
        if(CURR_INSTALL_LOG_NAME != null && CURR_INSTALL_LOG_NAME.length()>0){
            //日志文件在内存中的路径+如果当前的日志写在内存中，记录当前的日志文件名称
            String path = LOG_PATH_SDCARD_DIR + File.separator + CURR_INSTALL_LOG_NAME;
            File file = new File(path);
            if(!file.exists()){
                return;
            }
            BBLog.d(TAG, "checkLogSize() ==> The size of the log is too big?");
            //当文件长度>=10M时，重新创建一个新的文件夹
            if(file.length() >= MEMORY_LOG_FILE_MAX_SIZE){
                BBLog.d(TAG, "The log's size is too big!");
                try {
                    File fileLogZip = null;
                    if (file.exists() && path.endsWith(".log")) {
                        // 保存成gz壓縮文件，刪除log原文件；
                        String gzName = path.substring(0, path.lastIndexOf(".")) + "--" + sdftime.format(new Date()) + ".gz";
                        StringBuffer stringBuffer = new StringBuffer(gzName);
                        stringBuffer.insert(gzName.lastIndexOf(File.separator) + 1, UPLOAD_DIR + File.separator);
                        gzName = stringBuffer.toString();
                        fileLogZip = new File(gzName);
                        BBLog.i(TAG, "LogService 日志壓縮成文件 " + gzName);
                        GzipJava.compressGZIP(file, fileLogZip);
                        file.delete();

                        mLogQueue.removeOne(path);  //從隊列中刪除
                        mLogQueue.saveToFile(LOG_UPLOAD_PATH_SDCARD_LOG_FILE_RECORD);
                        mLogQueueGz.enQueue(gzName);    //加入到發送隊列中
                        mLogQueueGz.saveToFile(LOG_UPLOAD_PATH_SDCARD_GZ_FILE_RECORD);  //保存起來

                        checkLogGZCount();
                    }
                } catch (IOException e) {
                    e.printStackTrace();
                }
                new LogCollectorThread().start();
            } else {
                BBLog.d(TAG, "checkLog() ==> No...");
            }
        }
    }

    /**
     * 创建日志目录
     */
    private void createLogDir() {
        //日志文件在内存中的路径的文件夹
        File file;
        boolean mkOk;

        //判断SD卡是否存在,并且是否具有读写权限
        //创建SD卡路径下的文件夹
        if (Environment.getExternalStorageState().equals(Environment.MEDIA_MOUNTED)) {
            file = new File(LOG_PATH_SDCARD_DIR);
            if (!file.isDirectory()) {
                mkOk = file.mkdirs();
                if (!mkOk) {
                    recordLogServiceLog("move file failed,dir is not created succ");
                }
            }

            file = new File(LOG_UPLOAD_PATH_SDCARD_DIR);
            if (!file.isDirectory()) {
                mkOk = file.mkdirs();
                if (!mkOk) {
                    recordLogServiceLog("move file failed,dir is not created succ");
                }
            }
        }
    }

    /**
     * 删除内存下过期的日志
     */
    private void deleteSDCardExpiredLog() {
        Long dirSize = FileUtils.getTotalSizeOfFilesInDir(new File(LOG_PATH_SDCARD_DIR));
        BBLog.i(TAG, "開始清理过期的日志：當前Log文件夾大小" + dirSize);
        if (dirSize < MEMORY_LOG_DIR_MAX_SIZE) {
            return;
        }
        File file = new File(LOG_PATH_SDCARD_DIR);
        if (file.isDirectory()) {
            File[] allFiles = file.listFiles();
            for (File logFile : allFiles) {
                String fileName = logFile.getName();
                if (logFile.isDirectory() || logQueueFileName.equals(fileName) || logGZQueueFileName.equals(fileName)) {
                    continue;
                }
                //去除文件的扩展类型
                BBLog.i(TAG, "已存在的Log日志文件：" + fileName);
                String createDateInfo = getFileNameWithoutExtension(fileName);
                //判断sdcard上的日志文件是否可以删除
                if (canDeleteSDLog(createDateInfo)) {
                    //noinspection ResultOfMethodCallIgnored
                    logFile.delete();
                    BBLog.d(TAG, "刪除過期Log文件成功,the log path is:" + logFile.getAbsolutePath());
                } else if (!mLogQueue.isInQueue(logFile.getAbsolutePath())) {
                    logFile.delete();
                    BBLog.d(TAG, "刪除不在Log記錄隊列中的Log成功,the log path is:" + logFile.getAbsolutePath());
                }
            }
        }

        File fileUpload = new File(LOG_UPLOAD_PATH_SDCARD_DIR);
        if (fileUpload.isDirectory()) {
            File[] allFiles = fileUpload.listFiles();
            for (File logFile : allFiles) {
                String fileName = logFile.getName();
                if (logFile.isDirectory() || logQueueFileName.equals(fileName) || logGZQueueFileName.equals(fileName)) {
                    continue;
                }
                //去除文件的扩展类型
                BBLog.i(TAG, "已存在的Log日志壓縮文件：" + fileName);
                String createDateInfo = getFileNameWithoutExtension(fileName);
                //判断sdcard上的日志文件是否可以删除
                if (canDeleteSDLog(createDateInfo)) {
                    //noinspection ResultOfMethodCallIgnored
                    logFile.delete();
                    BBLog.d(TAG, "刪除過期Log壓縮文件成功,the log path is:" + logFile.getAbsolutePath());
                } else if (!mLogQueueGz.isInQueue(logFile.getAbsolutePath())) {
                    logFile.delete();
                    BBLog.d(TAG, "刪除不在Log上傳隊列中的Log壓縮文件成功,the log path is:" + logFile.getAbsolutePath());
                }
            }
        }
        BBLog.i(TAG, "開始清理过期的日志：結束");
    }

    /**
     * 删除内存下过期的日志
     */
    private void deleteSDCardExpiredLogEx() throws ParseException {
        Long dirSize = FileUtils.getTotalSizeOfFilesInDir(new File(LOG_PATH_SDCARD_DIR));
        BBLog.i(TAG, "開始清理过期的日志：當前Log文件夾大小" + dirSize);
        if (dirSize < MEMORY_LOG_DIR_MAX_SIZE) {
            BBLog.i(TAG, "開始清理过期的日志：不需要，返回");
            return;
        }

        String logFileNameToDelete = "";
        String gzFileNameToDelete = "";
        String lastFileName;
        int count = 10;
        while (dirSize>MEMORY_LOG_DIR_MAX_SIZE &&count>0) {
            logFileNameToDelete = "";
            gzFileNameToDelete = "";
            File file = new File(LOG_PATH_SDCARD_DIR);
            lastFileName = "";
            if (file.isDirectory()) {
                File[] allFiles = file.listFiles();
                for (File logFile : allFiles) {
                    String currFileName = logFile.getName();
                    if (logFile.isDirectory() || CURR_INSTALL_LOG_NAME.equals(currFileName)) {
                        continue;
                    }

                    if (TextUtils.isEmpty(lastFileName)) {
                        lastFileName = currFileName;
                    } else {
                        //去除文件的扩展类型
                        BBLog.i(TAG, "已存在的Log日志文件：" + currFileName);
                        String lastFileBaseName = getFileNameWithoutExtension(lastFileName);
                        String currFileBaseName = getFileNameWithoutExtension(currFileName);
                        if (lastFileBaseName.contains("--")) {
                            lastFileBaseName = lastFileBaseName.split("--")[0];
                        }
                        if (currFileBaseName.contains("--")) {
                            currFileBaseName = currFileBaseName.split("--")[0];
                        }

                        Date lastFileDate;
                        Date currFileDate;
                        try {
                            lastFileDate = sdf.parse(lastFileBaseName);
                        } catch (ParseException e) {
                            e.printStackTrace();
                            lastFileDate = myLogSdf.parse(lastFileBaseName);
                        }
                        try {
                            currFileDate = sdf.parse(currFileBaseName);
                        } catch (ParseException e) {
                            e.printStackTrace();
                            currFileDate = myLogSdf.parse(currFileBaseName);
                        }
                        //判断sdcard上的日志文件是否可以删除
                        if (currFileDate.before(lastFileDate)) {
                            //noinspection ResultOfMethodCallIgnored
                            lastFileName = currFileName;
                        }
                    }
                }
            }
            if (!TextUtils.isEmpty(lastFileName)) {
//                File fileToDelete = new File(LOG_PATH_SDCARD_DIR + File.separator + lastFileName);
//                if (fileToDelete.exists()) {
//                    BBLog.i(TAG, "開始清理过期的日志：删除" + lastFileName);
//                    fileToDelete.delete();
//                }
                logFileNameToDelete = lastFileName;     //記錄log文件中最早的文件；
            }

            File fileUpload = new File(LOG_UPLOAD_PATH_SDCARD_DIR);
            lastFileName = "";
            if (fileUpload.isDirectory()) {
                File[] allFiles = fileUpload.listFiles();
                for (File logFile : allFiles) {
                    String currFileName = logFile.getName();
                    if (logFile.isDirectory() || logQueueFileName.equals(currFileName) || logGZQueueFileName.equals(currFileName)) {
                        continue;
                    }

                    if (TextUtils.isEmpty(lastFileName)) {
                        lastFileName = currFileName;
                    } else {
                        //去除文件的扩展类型
                        BBLog.i(TAG, "已存在的Log日志文件：" + currFileName);
                        String lastFileBaseName = getFileNameWithoutExtension(lastFileName);
                        String currFileBaseName = getFileNameWithoutExtension(currFileName);
                        if (lastFileBaseName.contains("--")) {
                            lastFileBaseName = lastFileBaseName.split("--")[0];
                        }
                        if (currFileBaseName.contains("--")) {
                            currFileBaseName = currFileBaseName.split("--")[0];
                        }

                        Date lastFileDate;
                        Date currFileDate;
                        try {
                            lastFileDate = sdf.parse(lastFileBaseName);
                        } catch (ParseException e) {
                            e.printStackTrace();
                            lastFileDate = myLogSdf.parse(lastFileBaseName);
                        }
                        try {
                            currFileDate = sdf.parse(currFileBaseName);
                        } catch (ParseException e) {
                            e.printStackTrace();
                            currFileDate = myLogSdf.parse(currFileBaseName);
                        }
                        //判断sdcard上的日志文件是否可以删除
                        if (currFileDate.before(lastFileDate)) {
                            //noinspection ResultOfMethodCallIgnored
                            lastFileName = currFileName;
                        }
                    }
                }
            }
            if (!TextUtils.isEmpty(lastFileName)) {
//                File fileToDelete = new File(LOG_UPLOAD_PATH_SDCARD_DIR + File.separator + lastFileName);
//                if (fileToDelete.exists()) {
//                    BBLog.i(TAG, "開始清理过期的日志：删除" + lastFileName);
//                    fileToDelete.delete();
//                }
                gzFileNameToDelete = lastFileName;  //記錄gz文件中最早的文件
            }

            BBLog.i(TAG, "找到文件：" + logFileNameToDelete + "和" + gzFileNameToDelete);
            if (!TextUtils.isEmpty(logFileNameToDelete) && !TextUtils.isEmpty(gzFileNameToDelete)) {
                String logFileBaseName = getFileNameWithoutExtension(logFileNameToDelete);
                String gzFileBaseName = getFileNameWithoutExtension(gzFileNameToDelete);
                BBLog.i(TAG, "文件名："+logFileBaseName+"和"+gzFileBaseName);
                Date logFileDate = sdf.parse(logFileBaseName);
                Date gzFileDate = sdf.parse(gzFileBaseName);
                //判断sdcard上的日志文件是否可以删除
                BBLog.i(TAG, "文件名日期："+logFileDate+"和"+gzFileDate);
                if (logFileDate.before(gzFileDate) || logFileDate.equals(gzFileDate)) {
                    File fileToDelete = new File(LOG_PATH_SDCARD_DIR + File.separator + logFileNameToDelete);
                    if (fileToDelete.exists()) {
                        BBLog.i(TAG, "開始清理过期的日志：删除" + logFileNameToDelete);
                        fileToDelete.delete();
                    } else {
                        BBLog.i(TAG, "開始清理过期的日志："+fileToDelete+"文件不存在");
                    }
                } else {
                    File fileToDelete = new File(LOG_UPLOAD_PATH_SDCARD_DIR + File.separator + gzFileNameToDelete);
                    if (fileToDelete.exists()) {
                        BBLog.i(TAG, "開始清理过期的日志：删除" + gzFileNameToDelete);
                        fileToDelete.delete();
                    } else {
                        BBLog.i(TAG, "開始清理过期的日志："+fileToDelete+"文件不存在");
                    }
                }
            } else if (!TextUtils.isEmpty(logFileNameToDelete)) {
                File fileToDelete = new File(LOG_PATH_SDCARD_DIR + File.separator + logFileNameToDelete);
                if (fileToDelete.exists()) {
                    BBLog.i(TAG, "開始清理过期的日志：删除" + logFileNameToDelete);
                    fileToDelete.delete();
                } else {
                    BBLog.i(TAG, "開始清理过期的日志："+fileToDelete+"文件不存在");
                }
            } else if (!TextUtils.isEmpty(gzFileNameToDelete)) {
                File fileToDelete = new File(LOG_UPLOAD_PATH_SDCARD_DIR + File.separator + gzFileNameToDelete);
                if (fileToDelete.exists()) {
                    BBLog.i(TAG, "開始清理过期的日志：删除" + gzFileNameToDelete);
                    fileToDelete.delete();
                } else {
                    BBLog.i(TAG, "開始清理过期的日志："+fileToDelete+"文件不存在");
                }
            }

            dirSize = FileUtils.getTotalSizeOfFilesInDir(new File(LOG_PATH_SDCARD_DIR));
            BBLog.i(TAG, "開始清理过期的日志：删除后文件夾大小" + dirSize);
            count--;
        }
        BBLog.i(TAG, "開始清理过期的日志：結束");
    }

    private boolean isRegularGZ(File gzFile) {
        try {
            String fileName = gzFile.getName();
            String[] temp = fileName.split("--");
            String beginDate = temp[0];
            String endDate = temp[1];
            sdf.parse(beginDate);
            sdftime.parse(endDate);
            return true;
        } catch (Exception e) {
            e.printStackTrace();
        }

        return false;
    }

    /**
     * 监测gz文件个数，删除多余最早的
     */
    private void checkLogGZCount() {
        try {
            File fileUpload = new File(LOG_UPLOAD_PATH_SDCARD_DIR);
            if (!fileUpload.isDirectory() || fileUpload.listFiles().length <= MEMORY_LOG_GZ_MAX_COUNT+2) {
                BBLog.i(TAG, "開始监测gz文件个数，当前：" + fileUpload.listFiles().length);
                return;
            }

            BBLog.i(TAG, "開始监测gz文件个数，当前：" + fileUpload.listFiles().length);

            String gzFileNameToDelete = "";
            String lastFileName;
            while (fileUpload.listFiles().length > MEMORY_LOG_GZ_MAX_COUNT+2) {
                lastFileName = "";
                if (fileUpload.isDirectory()) {
                    File[] allFiles = fileUpload.listFiles();
                    for (File logFile : allFiles) {
                        String currFileName = logFile.getName();
                        if (logQueueFileName.equals(currFileName) || logGZQueueFileName.equals(currFileName)) {
                            continue;
                        }

                        if (logFile.isDirectory() || !isRegularGZ(logFile)) {
                            logFile.delete();
                            break;
                        }

                        if (TextUtils.isEmpty(lastFileName)) {
                            lastFileName = currFileName;
                        } else {
                            //去除文件的扩展类型
//                            BBLog.i(TAG, "已存在的Log日志文件：" + currFileName);
                            String lastFileBaseName = getFileNameWithoutExtension(lastFileName);
                            String currFileBaseName = getFileNameWithoutExtension(currFileName);
                            if (lastFileBaseName.contains("--")) {
                                lastFileBaseName = lastFileBaseName.split("--")[0];
                            }
                            if (currFileBaseName.contains("--")) {
                                currFileBaseName = currFileBaseName.split("--")[0];
                            }

                            Date lastFileDate;
                            Date currFileDate;
                            try {
                                lastFileDate = sdf.parse(lastFileBaseName);
                            } catch (ParseException e) {
                                e.printStackTrace();
                                lastFileDate = myLogSdf.parse(lastFileBaseName);
                            }
                            try {
                                currFileDate = sdf.parse(currFileBaseName);
                            } catch (ParseException e) {
                                e.printStackTrace();
                                currFileDate = myLogSdf.parse(currFileBaseName);
                            }
                            //判断sdcard上的日志文件是否可以删除
                            if (currFileDate.before(lastFileDate)) {
                                //noinspection ResultOfMethodCallIgnored
                                lastFileName = currFileName;
                            }
                        }
                    }
                }
                if (!TextUtils.isEmpty(lastFileName)) {
                    gzFileNameToDelete = lastFileName;  //記錄gz文件中最早的文件
                }

                BBLog.i(TAG, "找到GZ文件：" + gzFileNameToDelete);
                if (!TextUtils.isEmpty(gzFileNameToDelete)) {
                    String path = LOG_UPLOAD_PATH_SDCARD_DIR + File.separator + gzFileNameToDelete;
                    File fileToDelete = new File(path);
                    if (fileToDelete.exists()) {
                        BBLog.i(TAG, "開始删除多余的gz日志：删除" + gzFileNameToDelete);
                        fileToDelete.delete();
                        if (mLogQueueGz.isInQueue(path)) {
                            mLogQueueGz.removeOne(path);
                            mLogQueueGz.saveToFile(LOG_UPLOAD_PATH_SDCARD_GZ_FILE_RECORD);
                        }
                    } else {
                        BBLog.i(TAG, "開始删除多余的gz日志："+fileToDelete+"文件不存在");
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 判断sdcard上的日志文件是否可以删除
     * @param createDateStr                     createDateStr
     * @return                                  是否可以删除
     */
    public boolean canDeleteSDLog(String createDateStr) {
        boolean canDel ;
        //获取当前时间
        Calendar calendar = Calendar.getInstance();
        //删除7天之前日志
        calendar.add(Calendar.DAY_OF_MONTH, -1 * SDCARD_LOG_FILE_SAVE_DAYS);
        Date expiredDate = calendar.getTime();
        try {
            Date createDate = sdf.parse(createDateStr);
            canDel = createDate.before(expiredDate);
        } catch (ParseException e) {
            BBLog.e(TAG, e.getMessage(), e);
            canDel = false;
        }
        return canDel;
    }

    /**
     * 这个注意是记录错误的日志
     * 记录日志服务的基本信息 防止日志服务有错，在LogCat日志中无法查找
     * 此日志名称为Log.log
     * @param msg                           msg
     */
    private void recordLogServiceLog(String msg) {
        BBLog.e(TAG, msg);
    }

    /**
     * 去除文件的扩展类型（.log）
     * @param fileName                          fileName
     * @return                                  字符串
     */
    private String getFileNameWithoutExtension(String fileName){
        return fileName.substring(0, fileName.indexOf("."));
    }

    class ProcessInfo {
        public String user;
        private String pid;
        private String ppid;
        public String name;

        @Override
        public String toString() {
            return "ProcessInfo{" +
                    "user='" + user + '\'' +
                    ", pid='" + pid + '\'' +
                    ", ppid='" + ppid + '\'' +
                    ", name='" + name + '\'' +
                    '}';
        }
    }


    class StreamConsumer extends Thread {
        InputStream is;
        List<String> list;

        StreamConsumer(InputStream is) {
            this.is = is;
        }

        StreamConsumer(InputStream is, List<String> list) {
            this.is = is;
            this.list = list;
        }

        public void run() {
            try {
                InputStreamReader isr = new InputStreamReader(is);
                BufferedReader br = new BufferedReader(isr);
                String line ;
                while ((line = br.readLine()) != null) {
                    if (list != null) {
                        list.add(line);
                    }
                }
            } catch (IOException ioe) {
                ioe.printStackTrace();
            }
        }
    }

    private void scheduleAlarm() {
        Intent intent = new Intent(MONITOR_LOG_SIZE_ACTION);
        PendingIntent sender = PendingIntent.getBroadcast(this, 0, intent, 0);
        AlarmManager am = (AlarmManager) getSystemService(ALARM_SERVICE);
        if (am != null) {
            am.cancel(sender);
            if (firstTimeCheckSize) {
//                am.setRepeating(AlarmManager.RTC_WAKEUP, System.currentTimeMillis(), MEMORY_LOG_FILE_MONITOR_INTERVAL_256KB, sender);
                if (Build.VERSION.SDK_INT < Build.VERSION_CODES.KITKAT) {
                    am.set(AlarmManager.RTC_WAKEUP, System.currentTimeMillis()+MEMORY_LOG_FILE_MONITOR_INTERVAL_256KB, sender);
                } else {
                    am.setExact(AlarmManager.RTC_WAKEUP, System.currentTimeMillis()+MEMORY_LOG_FILE_MONITOR_INTERVAL_256KB, sender);
                }
            } else {
//                am.setRepeating(AlarmManager.RTC_WAKEUP, System.currentTimeMillis(), MEMORY_LOG_FILE_MONITOR_INTERVAL, sender);
                if (Build.VERSION.SDK_INT < Build.VERSION_CODES.KITKAT) {
                    am.set(AlarmManager.RTC_WAKEUP, System.currentTimeMillis()+MEMORY_LOG_FILE_MONITOR_INTERVAL, sender);
                } else {
                    am.setExact(AlarmManager.RTC_WAKEUP, System.currentTimeMillis()+MEMORY_LOG_FILE_MONITOR_INTERVAL, sender);
                }
            }
        }
    }

    /**
     * 日志任务接收广播
     * 切换日志，监控日志大小
     * <AUTHOR>
     *
     */
    class LogTaskReceiver extends BroadcastReceiver {
        public void onReceive(Context context, Intent intent) {
            String action = intent.getAction();
            //切换日志文件action
            if(SWITCH_LOG_FILE_ACTION.equals(action)){
                new LogCollectorThread().start();
            }else if(MONITOR_LOG_SIZE_ACTION.equals(action)){
                //日志文件监测action
                if (firstTimeCheckSize) {
                    firstTimeCheckSize = false;
                    if (uploadRecent) {
                        uploadRecentLog(true);
                    }
                } else {
                    checkLogSize();
                }
                scheduleAlarm();
            }else if(Intent.ACTION_SHUTDOWN.equals(action)){
                //日志文件监测action
                BBLog.w(TAG, "收到關機廣播");
                powerOffSaveCurrent();
            }else if(START_LOG_COLLECTOR_ACTION.equals(action)){
                new LogCollectorThread().start();
            }
        }
    }


    class FileComparator implements Comparator<File> {
        public int compare(File file1, File file2) {
            if(logGZQueueFileName.equals(file1.getName())){
                return -1;
            }else if(logGZQueueFileName.equals(file2.getName())){
                return 1;
            }

            String createInfo1 = getFileNameWithoutExtension(file1.getName());
            String createInfo2 = getFileNameWithoutExtension(file2.getName());

            try {
                Date create1 = sdf.parse(createInfo1);
                Date create2 = sdf.parse(createInfo2);
                if(create1.before(create2)){
                    return -1;
                }else{
                    return 1;
                }
            } catch (ParseException e) {
                return 0;
            }
        }
    }

    //////////////////////////////////日志上送////////////////////////////////
    private static LLQueue<String> mLogQueue = new LLQueue<>();     //用於LOG file創建時記錄到該隊列中存儲，避免LogService意外挂掉時記錄丟失
    private static LLQueue<String> mLogQueueGz = new LLQueue<>();   //GZ file上傳 發送隊列，存儲要上傳的壓縮文件的列表；

    public static void powerOffSaveCurrent() {
        try {
            BBLog.w(TAG, "終端關機：");
            BBLog.w(TAG, "日志記錄隊列當前條數：" + mLogQueue.queueLength());
            mLogQueue.saveToFile(LOG_UPLOAD_PATH_SDCARD_LOG_FILE_RECORD);
            BBLog.w(TAG, "日志上送隊列當前條數：" + mLogQueueGz.queueLength());
            mLogQueueGz.saveToFile(LOG_UPLOAD_PATH_SDCARD_GZ_FILE_RECORD);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    private void initLogQueue() {
        mLogQueue.clear();
        mLogQueueGz.clear();
        try {
            String filePathLog = LOG_UPLOAD_PATH_SDCARD_LOG_FILE_RECORD;
            String filePathGz = LOG_UPLOAD_PATH_SDCARD_GZ_FILE_RECORD;
            File fileLog = new File(filePathLog);
            File fileGz = new File(filePathGz);
            //先讀GZ記錄的列表
            if (fileGz.exists()) {
                mLogQueueGz.readFromFile(filePathGz);
                BBLog.i(TAG, "日志上送隊列條數：" + mLogQueueGz.queueLength());
            }
            //再讀log記錄的列表，有存在殘餘的，壓縮到GZ記錄列表中，保證殘餘的也能上送
            if (fileLog.exists()) {
                mLogQueue.readFromFile(filePathLog);
                if (mLogQueue.queueLength() > 0) {
                    BBLog.i(TAG, "Log記錄隊列mLogQueue中存在殘留文件 開始壓縮到Gz記錄隊列中");
                    while (mLogQueue.queueLength() > 0) {
                        String uploadFile = (String) mLogQueue.getQueuePeek();
                        File fileTemp = new File(uploadFile);
                        BBLog.i(TAG, "找到log文件 " + fileTemp.length() + "  " + uploadFile);
                        if (fileTemp.exists() && uploadFile.endsWith(".log")) {
                            String gzName = uploadFile.substring(0, uploadFile.lastIndexOf(".")) + "--" + sdftime.format(new Date()) + ".gz";
                            StringBuffer stringBuffer = new StringBuffer(gzName);
                            stringBuffer.insert(gzName.lastIndexOf(File.separator) + 1, UPLOAD_DIR + File.separator);
                            gzName = stringBuffer.toString();
                            File fileLogZip = new File(gzName);
                            BBLog.i(TAG, "壓縮成文件 " + gzName);
                            GzipJava.compressGZIP(fileTemp, fileLogZip);
                            mLogQueueGz.enQueue(gzName);
                            mLogQueueGz.saveToFile(LOG_UPLOAD_PATH_SDCARD_GZ_FILE_RECORD);  //保存起來
                            fileTemp.delete();
                        } else {
                            BBLog.i(TAG, "無效文件");
                        }
                        mLogQueue.deQueue();
                    }
                    BBLog.i(TAG, "Log記錄隊列mLogQueue中存在殘留文件 結束 剩餘：" + mLogQueue.queueLength());
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        BBLog.w(TAG, "日志上送隊列條數更新后：" + mLogQueueGz.queueLength());
    }

    class LogUploadThread extends Thread {
        LogUploadThread(){
            super("LogUploadThread");
            BBLog.d(TAG, "LogUploadThread is create");
        }

        @SuppressLint("WakelockTimeout")
        @Override
        public void run() {
            try {
                while (true) {
                    if (!isLogExecuting) {
                        break;
                    }

                    if (uploadLogGZFile && mLogQueueGz.queueLength() > 0) {
                        String uploadZipFile = (String) mLogQueueGz.getQueueLast();//getQueuePeek();改为getQueueLast，先传最新的
                        File fileLogZip = new File(uploadZipFile);
                        BBLog.i(TAG, "LogService 開始上送日志壓縮文件：" + fileLogZip.length() + "  " + uploadZipFile);
                        if (fileLogZip.exists() && uploadZipFile.endsWith(".gz")) {
                            BBLog.i(TAG, "LogService 上送日志壓縮文件 ");
                            if (fileLogZip.exists() && fileLogZip.length()>0) {
                                if (HttpUtils.urlUploadFile(HttpUtils.S3_LOG_STREAMING_URL, HttpUtils.S3_LOG_STREAMING_BUCKET_NAME, getLogUploadBucket(), fileLogZip)) {
                                    if (fileLogZip != null) {
                                        fileLogZip.delete();
                                    }
                                    mLogQueueGz.removeOne(uploadZipFile);//deQueue();改为getQueueLast，先传最新的
                                    BBLog.w(TAG, "LogService 結束上送日志壓縮文件 成功 剩餘條數：" + mLogQueueGz.queueLength());
                                    mLogQueueGz.saveToFile(LOG_UPLOAD_PATH_SDCARD_GZ_FILE_RECORD);
                                } else {
                                    BBLog.w(TAG, "LogService 結束上送日志壓縮文件 失敗 剩餘條數：" + mLogQueueGz.queueLength());
                                }
                            }
                        } else {
                            BBLog.i(TAG, "LogService 無效文件：");
                            mLogQueueGz.deQueue();
                            mLogQueueGz.saveToFile(LOG_UPLOAD_PATH_SDCARD_GZ_FILE_RECORD);
                        }
//                        sleep(5000);
                    } else {
                        sleep(5000);
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
                recordLogServiceLog(Log.getStackTraceString(e));
            }
        }
    }

    private static String getLogUploadBucket() {
        String mode = SharedPreferencesUtils.getSharePreferencesValue(UsualData.SHARED_PREFERENCES_MODE_IN_CONFIG, "uat");
        String sn = DeviceInfoApi.getIntance().getSerialNumber();
        SimpleDateFormat format = new SimpleDateFormat("yyyyMMdd");
        String date = format.format(new Date(System.currentTimeMillis()));

        return mode + "/" + sn + "/" + date;
    }

    private static String getLogUploadBucketForRecent() {
        String mode = SharedPreferencesUtils.getSharePreferencesValue(UsualData.SHARED_PREFERENCES_MODE_IN_CONFIG, "uat");
        String sn = DeviceInfoApi.getIntance().getSerialNumber();

        return mode + "/" + sn;
    }

    public static void uploadCurrentLog() throws IOException {
        BBLog.i(TAG, "Log 上送未记录達到目標大小的log");
        try {
            if (mLogQueue.queueLength() > 0) {
                BBLog.i(TAG, "Log記錄隊列mLogQueue中存在殘留文件 開始壓縮到Gz記錄隊列中");
                String uploadFile = (String) mLogQueue.getQueueLast();
                File fileTemp = new File(uploadFile);
                BBLog.i(TAG, "找到log文件 " + fileTemp.length() + "  " + uploadFile);
                if (fileTemp.exists() && uploadFile.endsWith(".log")) {
                    String gzName = uploadFile.substring(0, uploadFile.lastIndexOf(".")) + "--" + sdftime.format(new Date()) + ".gz";
                    StringBuffer stringBuffer = new StringBuffer(gzName);
                    stringBuffer.insert(gzName.lastIndexOf(File.separator) + 1, "upload" + File.separator);
                    gzName = stringBuffer.toString();
                    File fileLogZip = new File(gzName);
                    BBLog.i(TAG, "壓縮成文件 " + gzName);
                    GzipJava.compressGZIP(fileTemp, fileLogZip);
                    fileTemp.delete();
                    mLogQueueGz.enQueue(gzName);
                    mLogQueueGz.saveToFile(LOG_UPLOAD_PATH_SDCARD_GZ_FILE_RECORD);  //保存起來
                    mLogQueue.removeOne(uploadFile);
                    mLogQueue.saveToFile(LOG_UPLOAD_PATH_SDCARD_LOG_FILE_RECORD);
                    new Thread(new Runnable() {
                        @Override
                        public void run() {
                            if (HttpUtils.urlUploadFile(HttpUtils.S3_LOG_STREAMING_URL, HttpUtils.S3_LOG_STREAMING_BUCKET_NAME, getLogUploadBucket(), fileLogZip)) {
                                if (fileLogZip != null) {
                                    fileLogZip.delete();
                                }
                                try {
                                    mLogQueueGz.deQueue();
                                    mLogQueueGz.saveToFile(LOG_UPLOAD_PATH_SDCARD_GZ_FILE_RECORD);  //保存起來
                                } catch (IOException e) {
                                    e.printStackTrace();
                                }
                                BBLog.w(TAG, "LogService 結束上送日志壓縮文件 成功 剩餘條數：" + mLogQueueGz.queueLength());
                            } else {
                                BBLog.w(TAG, "LogService 結束上送日志壓縮文件 失敗 剩餘條數：" + mLogQueueGz.queueLength());
                            }
                        }
                    }).start();
                } else {
                    BBLog.i(TAG, "無效文件");
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public synchronized static void uploadRecentLog(boolean isFirstTime) {
        try {
            if((isFirstTime && FIRST_TIME_TEMP_SAVE_LOG_NAME != null && FIRST_TIME_TEMP_SAVE_LOG_NAME.length()>0)
            || (CURR_INSTALL_LOG_NAME != null && CURR_INSTALL_LOG_NAME.length()>0)) {
                //日志文件在内存中的路径+如果当前的日志写在内存中，记录当前的日志文件名称
                BBLog.w(TAG, "##### start LogService uploadRecentLog #####");
                String path;
                File file;
                if (isFirstTime) {
                    Intent intent = new Intent(START_LOG_COLLECTOR_ACTION);
                    ContextUtil.getInstance().getApplicationContext().sendBroadcast(intent, RequestPermission.REQUEST_PERMISSION_MY_BROADCAST);
                    path = LOG_PATH_SDCARD_DIR + File.separator + FIRST_TIME_TEMP_SAVE_LOG_NAME;
                    BBLog.e(TAG, "path = " + path);
                    file = new File(path);
                    if (!file.exists()) {
                        return;
                    }
                } else {
                    path = LOG_PATH_SDCARD_DIR + File.separator + CURR_INSTALL_LOG_NAME;
                    BBLog.e(TAG, "path = " + path);
                    file = new File(path);
                    if (!file.exists()) {
                        return;
                    }
                }

                File recentFile = new File(LOG_UPLOAD_PATH_SDCARD_DIR + File.separator + "recent.log");
                if (recentFile.exists()) {
                    recentFile.delete();
                }
                recentFile.createNewFile();
                FileOutputStream outputStream = new FileOutputStream(recentFile);

                RandomAccessFile accessFile = new RandomAccessFile(path, "r");
                int readlen = 4*1024;
                long currentLogcatRead = accessFile.length();
                long totalRecentSize = 0;
                long recentOffset = 0;
                long counter = 0;
                boolean uploadLog = false;
                if (currentLogcatRead > RECENT_LOG_MAX_SIZE) {
                    totalRecentSize = RECENT_LOG_MAX_SIZE;
                    recentOffset = currentLogcatRead - RECENT_LOG_MAX_SIZE;
                    uploadLog = true;
                } else {
                    totalRecentSize = currentLogcatRead;
                    recentOffset = 0;
                }

                BBLog.w(TAG, "LogService current read logcat size="+currentLogcatRead+"  totalSizeToRead="+totalRecentSize+"  offset="+recentOffset);
                byte[] buffer = new byte[readlen];

                //先上送Recent文件
                try {
                    for (int i=0; i<RECENT_LOG_MAX_SIZE/readlen; i++){
                        accessFile.seek(recentOffset + i*readlen);
                        int len = accessFile.read(buffer);
                        if (len > 0) {
                            outputStream.write(buffer);
                            counter = counter + len;
                            if (totalRecentSize == counter) {
                                break;
                            }
                        } else if (len == -1) {
                            break;
                        }
                    }
                    BBLog.w(TAG, "LogService uploadRecentLog recent.gz文件大小="+recentFile.length());
                    File recentFileLogZip = new File(LOG_UPLOAD_PATH_SDCARD_DIR + File.separator + "recent.gz");
                    GzipJava.compressGZIP(recentFile, recentFileLogZip);
                    BBLog.w(TAG, "LogService uploadRecentLog recent.gz压缩大小="+recentFileLogZip.length());
                    recentFile.delete();
                    new Thread(new Runnable() {
                        @Override
                        public void run() {
                            for (int i=0; i<3; i++) {
                                if (HttpUtils.urlUploadFile(HttpUtils.S3_LOG_STREAMING_URL, HttpUtils.S3_LOG_STREAMING_BUCKET_NAME, getLogUploadBucketForRecent(), recentFileLogZip)) {
                                    if (recentFileLogZip != null) {
                                        recentFileLogZip.delete();
                                    }
                                    BBLog.w(TAG, "LogService recentFile 上送成功");
                                    break;
                                } else {
                                    BBLog.w(TAG, "LogService recentFile 上送失敗");
                                }
                            }
                        }
                    }).start();
                } catch (Exception e){
                    e.printStackTrace();
                } finally {
                    try {
                        if (accessFile != null) {
                            accessFile.close();
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                    IOUtils.flushCloseOutputStream(outputStream);
                }

                //同时将当前的log文件分包上送；
                long totalSize = file.length();
                RandomAccessFile accessFileTemp = null;
                try {
                    accessFileTemp = new RandomAccessFile(path, "r");
                    BBLog.i(TAG, "upload Logcat：accessFile init = " + accessFileTemp.length());
                } catch (Exception e) {
                    e.printStackTrace();
                }
                BBLog.i(TAG, "upload Logcat：read size = " + totalSize);
                try {
                    if (true) {
                        File fileLogZip = null;

                        if (isFirstTime) {
                            //获取当前logFile实际存储数据的大小；
                            long sendCounter = 1;//totalSize % MEMORY_LOG_FILE_MAX_SIZE == 0 ? totalSize / MEMORY_LOG_FILE_MAX_SIZE : totalSize / MEMORY_LOG_FILE_MAX_SIZE + 1;
                            BBLog.i(TAG, "upload Logcat：sendCounter = " + sendCounter);
                            for (int time = 0; time < sendCounter; time++) {
                                String tempPath = LOG_PATH_SDCARD_DIR + File.separator + sdf.format(new Date()) + "_temp.log";
                                File tempFile = new File(tempPath);
                                tempFile.createNewFile();
                                FileOutputStream outputStreamTemp = new FileOutputStream(tempFile);
                                long offsetTemp = 0;
                                long leftSize = totalSize - time * MEMORY_LOG_FILE_MAX_SIZE;
                                long blockSize = totalSize;
//                            if (leftSize > MEMORY_LOG_FILE_MAX_SIZE) {
//                                blockSize = MEMORY_LOG_FILE_MAX_SIZE;
//                            } else {
//                                blockSize = leftSize;
//                            }
                                BBLog.i(TAG, "upload Logcat：blockSize = " + blockSize);
                                try {
                                    while (true) {
                                        int len;
                                        accessFileTemp.seek(time * MEMORY_LOG_FILE_MAX_SIZE + offsetTemp);
                                        if (blockSize - offsetTemp > readlen) {
                                            len = accessFileTemp.read(buffer);
                                        } else {
                                            len = accessFileTemp.read(buffer, 0, (int) (blockSize - offsetTemp));
                                        }
                                        if (len > 0) {
                                            outputStreamTemp.write(buffer, 0, len);
                                            offsetTemp = offsetTemp + len;
                                            if (blockSize == offsetTemp) {
                                                break;
                                            }
                                        } else if (len == -1) {
                                            break;
                                        }
                                    }
                                } catch (Exception e) {
                                    e.printStackTrace();
                                } finally {
                                    IOUtils.flushCloseOutputStream(outputStreamTemp);
                                }

                                // 保存成gz壓縮文件，刪除log原文件；
                                BBLog.i(TAG, "upload Logcat：No." + time + " file size = " + tempFile.length());
                                String gzName = tempPath.substring(0, tempPath.lastIndexOf("_temp")) + "--" + sdftime.format(new Date()) + ".gz";
                                StringBuffer stringBuffer = new StringBuffer(gzName);
                                stringBuffer.insert(gzName.lastIndexOf(File.separator) + 1, UPLOAD_DIR + File.separator);
                                gzName = stringBuffer.toString();
                                fileLogZip = new File(gzName);
                                GzipJava.compressGZIP(tempFile, fileLogZip);
                                BBLog.i(TAG, "upload Logcat：No." + time + " file gzSize = " + fileLogZip.length());
                                tempFile.delete();

//                            mLogQueueGz.enQueue(gzName);    //加入到發送隊列中
//                            mLogQueueGz.saveToFile(LOG_UPLOAD_PATH_SDCARD_GZ_FILE_RECORD);  //保存起來

                                File finalFileLogZip = fileLogZip;
                                String finalGzName = gzName;
                                new Thread(new Runnable() {
                                    @Override
                                    public void run() {
                                        for (int i = 0; i < 3; i++) {
                                            if (HttpUtils.urlUploadFile(HttpUtils.S3_LOG_STREAMING_URL, HttpUtils.S3_LOG_STREAMING_BUCKET_NAME, getLogUploadBucket(), finalFileLogZip)) {
                                                if (finalFileLogZip != null) {
                                                    finalFileLogZip.delete();
                                                }
                                                BBLog.w(TAG, "upload Logcat upload file SUCC");
//                                            try {
//                                                mLogQueueGz.deQueue();
//                                                mLogQueueGz.saveToFile(LOG_UPLOAD_PATH_SDCARD_GZ_FILE_RECORD);
//                                            } catch (IOException e) {
//                                                e.printStackTrace();
//                                            }
                                                return;
                                            } else {
                                                BBLog.w(TAG, "upload Logcat upload file FAIL " + (i + 1) + "time(s)");
                                            }
                                        }
                                        //如果失败的话，再加入队列中寻求再次上送。避免uploadThread中重复上送
                                        try {
                                            mLogQueueGz.enQueue(finalGzName);
                                            mLogQueueGz.saveToFile(LOG_UPLOAD_PATH_SDCARD_GZ_FILE_RECORD);
                                        } catch (IOException e) {
                                            e.printStackTrace();
                                        }
                                    }
                                }).start();
                            }
                        } else {
                            String gzName;
                            if (file.exists() && path.endsWith(".log")) {
                                // 保存成gz壓縮文件，刪除log原文件；
                                gzName = path.substring(0, path.lastIndexOf(".")) + "--" + sdftime.format(new Date()) + ".gz";
                                StringBuffer stringBuffer = new StringBuffer(gzName);
                                stringBuffer.insert(gzName.lastIndexOf(File.separator) + 1, UPLOAD_DIR + File.separator);
                                gzName = stringBuffer.toString();
                                fileLogZip = new File(gzName);
                                BBLog.i(TAG, "LogService 日志壓縮成文件 " + gzName);
                                GzipJava.compressGZIP(file, fileLogZip);
                                file.delete();

                                mLogQueue.removeOne(path);  //從隊列中刪除
                                mLogQueue.saveToFile(LOG_UPLOAD_PATH_SDCARD_LOG_FILE_RECORD);
                                mLogQueueGz.enQueue(gzName);    //加入到發送隊列中
                                mLogQueueGz.saveToFile(LOG_UPLOAD_PATH_SDCARD_GZ_FILE_RECORD);  //保存起來

                                Intent intent = new Intent(START_LOG_COLLECTOR_ACTION);
                                ContextUtil.getInstance().getApplicationContext().sendBroadcast(intent, RequestPermission.REQUEST_PERMISSION_MY_BROADCAST);
                            }
                        }
                    }
                } catch (IOException e) {
                    e.printStackTrace();
                } finally {
                    if (file.getName().contains(logTemporaryFileName)) {
                        file.delete();
                    }
                    try {
                        if (accessFileTemp != null) {
                            accessFileTemp.close();
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void stopLogServiceUpload() {
        BBLog.e(TAG, "STOP LOGSERVICE UPLOAD !!!");
        LogService.uploadLogGZFile = false;
    }

    public static void startLogServiceUpload() {
        BBLog.e(TAG, "START LOGSERVICE UPLOAD !!!");
        LogService.uploadLogGZFile = true;
    }
}