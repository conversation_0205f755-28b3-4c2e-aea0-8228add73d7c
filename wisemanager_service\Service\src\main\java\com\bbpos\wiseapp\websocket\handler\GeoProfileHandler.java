package com.bbpos.wiseapp.websocket.handler;

import android.content.Context;
import android.content.SharedPreferences;
import android.preference.PreferenceManager;
import android.text.TextUtils;

import com.bbpos.wiseapp.logger.BBLog;
import com.bbpos.wiseapp.service.common.UsualData;
import com.bbpos.wiseapp.tms.location.GpsLocationManager;
import com.bbpos.wiseapp.tms.utils.Constants;
import com.bbpos.wiseapp.tms.utils.ContextUtil;
import com.bbpos.wiseapp.tms.utils.DateTimeUtils;
import com.bbpos.wiseapp.tms.utils.Helpers;
import com.bbpos.wiseapp.tms.utils.ParameterName;
import com.bbpos.wiseapp.tms.utils.SPKeys;
import com.bbpos.wiseapp.tms.utils.TaskState;
import com.bbpos.wiseapp.utils.SharedPreferencesUtils;
import com.bbpos.wiseapp.websocket.WebSocketManager;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.text.SimpleDateFormat;

/**
 * 处理ST007 geo profile 协议
 */
public class GeoProfileHandler {
    private WebSocketManager webSocketManager;
    private Context mContext;

    public GeoProfileHandler(Context context, WebSocketManager webSocketManager) {
        this.webSocketManager = webSocketManager;
        mContext = context;
    }

    public void handleMsg(String response) {
        try {
            JSONObject geoInfo = null;
            JSONObject responseJson = new JSONObject(response);
            JSONObject responseData = Helpers.getDataFromResponse(responseJson);
            if (responseData == null) {
                return;
            }
            if (responseData.has(ParameterName.geoInfo)) {
                geoInfo = responseData.getJSONObject(ParameterName.geoInfo);
                if (geoInfo!=null){
                    if (geoInfo.has(ParameterName.action) && ("A".equals(geoInfo.getString(ParameterName.action)) || "M".equals(geoInfo.getString(ParameterName.action)))) {
                        boolean checkModifyDate = false;
                        String curProfile = SharedPreferencesUtils.getSharePreferencesValue(UsualData.SHARED_PREFERENCES_GPS_GEOFENCE_CUR_PROFILE, "");
                        if (!TextUtils.isEmpty(curProfile)) {
                            checkModifyDate = true;
                        }
                        String curModifyDateStr = "";
                        if (checkModifyDate) {
                            curModifyDateStr = new JSONObject(curProfile).getString(ParameterName.modifyDate);
                        }
                        String beginDateStr = geoInfo.getString(ParameterName.beginDate);
                        String endDateStr = geoInfo.getString(ParameterName.endDate);
                        String modifyDateStr = geoInfo.getString(ParameterName.modifyDate);
                        long nowTime = System.currentTimeMillis();
                        try {
                            long curModifyTime = 0L;
                            long modifyTime = 0L;
                            long begTime;
                            long endTime;
                            if (DateTimeUtils.isNumeric(beginDateStr) && DateTimeUtils.isNumeric(endDateStr)) {
                                if (checkModifyDate) {
                                    curModifyTime = new Long(curModifyDateStr).longValue();
                                    modifyTime = new Long(modifyDateStr).longValue();
                                }
                                begTime = new Long(beginDateStr).longValue();
                                endTime = new Long(endDateStr).longValue();
                            } else {
                                SimpleDateFormat sdf = new SimpleDateFormat(Constants.dateFormat);
                                if (checkModifyDate) {
                                    curModifyTime = sdf.parse(curModifyDateStr).getTime();
                                    modifyTime = sdf.parse(modifyDateStr).getTime();
                                }
                                begTime = sdf.parse(beginDateStr).getTime();
                                endTime = sdf.parse(endDateStr).getTime();
                            }

                            if (geoInfo.has(ParameterName.isDefault) && "1".equals(geoInfo.getString(ParameterName.isDefault))) {
                                updateGeoProfileList(geoInfo);
                                if (!TextUtils.isEmpty(curProfile)) {
                                    JSONObject curProfileJson = new JSONObject(curProfile);
                                    //如果当前正在执行default，则里面执行更新；
                                    if (curProfileJson.has(ParameterName.isDefault) && "1".equals(curProfileJson.getString(ParameterName.isDefault))) {
                                        GpsLocationManager.executeGeoProfile(geoInfo);
                                    }
                                }
                            } else {
                                if (begTime < nowTime && endTime > nowTime) {
                                    if (!checkModifyDate || (checkModifyDate && modifyTime>curModifyTime)) {
                                        boolean disableTag = "1".equals(geoInfo.optString(ParameterName.disableTag)) ? true : false;
                                        if (disableTag) {
                                            if (!TextUtils.isEmpty(curProfile)) {
                                                //要disable的profile是当前执行的，直接执行default，否则丢弃
                                                String curProfileID = new JSONObject(curProfile).getString(ParameterName.proId);
                                                if (curProfileID.equals(geoInfo.getString(ParameterName.proId))) {
                                                    GpsLocationManager.executeGeoProfile(getDefaultGeoProfile());
                                                }
                                            }
                                        } else {
                                            GpsLocationManager.executeGeoProfile(geoInfo);
                                        }
                                    } else {
                                        BBLog.d(Constants.TAG, "geoprofile 當前Profile的modifyDate 晚於新推送profile，不執行");
                                    }
                                } else if (nowTime < begTime) {
                                    if (!TextUtils.isEmpty(curProfile)) {
                                        JSONObject curProfileJson = new JSONObject(curProfile);
                                        //如当下发的就是当前执行的非default，且修改当前这条为未生效的时间，则执行default；
                                        if (curProfileJson.getString(ParameterName.proId).equals(geoInfo.getString(ParameterName.proId))) {
                                            GpsLocationManager.executeGeoProfile(getDefaultGeoProfile());
                                        }
                                    }
                                    updateGeoProfileList(geoInfo);
                                } else {
                                    //過期作廢
                                }
                            }
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    } else if (geoInfo.has(ParameterName.action) && "D".equals(geoInfo.getString(ParameterName.action))) {
                        deleteGeoProfileFromList(geoInfo);
                    }
                }
            } else {
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**与本地任务列表进行对比、更新*/
    public static void updateGeoProfileList(JSONObject profileInfo){
        try {
            SharedPreferences sp = PreferenceManager.getDefaultSharedPreferences(ContextUtil.getInstance());
            String localGeoProfileListStr = sp.getString(SPKeys.WEBSOCKET_GEOFENCE_PROFILE_LIST, "");
            BBLog.d(Constants.TAG, "当前本地: localGeoProfileListStr  -->"+localGeoProfileListStr);
            if (!TextUtils.isEmpty(localGeoProfileListStr)) {
                BBLog.d(Constants.TAG, "本地不为空，直接新增保存");
                JSONArray localProfileList = new JSONArray(localGeoProfileListStr);
                if (profileInfo.has(ParameterName.isDefault) && "1".equals(profileInfo.getString(ParameterName.isDefault))) {
                    for (int i = 0; i < localProfileList.length(); i++) {
                        JSONObject profileJsonObj = (JSONObject) localProfileList.get(i);
                        //参数处理
                        if (profileJsonObj.has(ParameterName.isDefault) && "1".equals(profileJsonObj.getString(ParameterName.isDefault))) {
                            BBLog.d(Constants.TAG, "是default geoprofile，直接更新本地的，結束");
                            profileJsonObj.put(ParameterName.lockMeter, profileInfo.getString(ParameterName.lockMeter));
                            profileJsonObj.put(ParameterName.lockMin, profileInfo.getString(ParameterName.lockMin));
                            profileJsonObj.put(ParameterName.wipeMin, profileInfo.getString(ParameterName.wipeMin));
                            profileJsonObj.put(ParameterName.wipeStatus, profileInfo.getString(ParameterName.wipeStatus));
                            profileJsonObj.put(ParameterName.beginDate, profileInfo.getString(ParameterName.beginDate));
                            profileJsonObj.put(ParameterName.endDate, profileInfo.getString(ParameterName.endDate));
                            profileJsonObj.put(ParameterName.modifyDate, profileInfo.getString(ParameterName.modifyDate));
                            profileJsonObj.put(ParameterName.proName,  profileInfo.getString(ParameterName.proName));
                            sp.edit().putString(SPKeys.WEBSOCKET_GEOFENCE_PROFILE_LIST, localProfileList.toString()).commit();
                            return;
                        }
                    }
                    localProfileList.put(profileInfo);
                    BBLog.d(Constants.TAG, "是default geoprofile，本地沒找到default，直接插入");
                    sp.edit().putString(SPKeys.WEBSOCKET_GEOFENCE_PROFILE_LIST, localProfileList.toString()).commit();
                } else {
                    for (int i = 0; i < localProfileList.length(); i++) {
                        JSONObject profileJsonObj = (JSONObject) localProfileList.get(i);
                        //参数处理
                        if (profileJsonObj.getString(ParameterName.proId).equals(profileInfo.getString(ParameterName.proId))) {
                            BBLog.d(Constants.TAG, "是default geoprofile，直接更新本地的，結束");
                            profileJsonObj.put(ParameterName.lockMeter, profileInfo.getString(ParameterName.lockMeter));
                            profileJsonObj.put(ParameterName.lockMin, profileInfo.getString(ParameterName.lockMin));
                            profileJsonObj.put(ParameterName.wipeMin, profileInfo.getString(ParameterName.wipeMin));
                            profileJsonObj.put(ParameterName.wipeStatus, profileInfo.getString(ParameterName.wipeStatus));
                            profileJsonObj.put(ParameterName.beginDate, profileInfo.getString(ParameterName.beginDate));
                            profileJsonObj.put(ParameterName.endDate, profileInfo.getString(ParameterName.endDate));
                            profileJsonObj.put(ParameterName.modifyDate, profileInfo.getString(ParameterName.modifyDate));
                            profileJsonObj.put(ParameterName.proName,  profileInfo.getString(ParameterName.proName));
                            sp.edit().putString(SPKeys.WEBSOCKET_GEOFENCE_PROFILE_LIST, localProfileList.toString()).commit();
                            return;
                        }
                    }
                    BBLog.d(Constants.TAG, "不是default geoprofile，但未到執行時間，直接插入");
                    localProfileList.put(profileInfo);
                    sp.edit().putString(SPKeys.WEBSOCKET_GEOFENCE_PROFILE_LIST, localProfileList.toString()).commit();
                }
            }else {
                BBLog.d(Constants.TAG, "本地为空，直接保存");
                if (profileInfo.has(ParameterName.isDefault) && "1".equals(profileInfo.getString(ParameterName.isDefault))) {
                    //如果本地沒有存在過profile，那麽Default立馬生效
                    BBLog.d(Constants.TAG, "是default geoprofile，并且是本地第一條，直接插入並立馬生效");
                    if (!Constants.B_UNBOX_RUNNING && TextUtils.isEmpty(SharedPreferencesUtils.getSharePreferencesValue(UsualData.SHARED_PREFERENCES_GPS_GEOFENCE_CUR_PROFILE, ""))) {
                        GpsLocationManager.executeGeoProfile(profileInfo);
                    }
                }
                JSONArray newProfileList = new JSONArray();
                newProfileList.put(profileInfo);
                sp.edit().putString(SPKeys.WEBSOCKET_GEOFENCE_PROFILE_LIST, newProfileList.toString()).commit();
            }
        } catch (JSONException e) {
            e.printStackTrace();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**与本地任务列表进行对比、更新*/
    private void deleteGeoProfileFromList(JSONObject profileInfo){
        try {
            SharedPreferences sp = PreferenceManager.getDefaultSharedPreferences(ContextUtil.getInstance());
            String localGeoProfileListStr = sp.getString(SPKeys.WEBSOCKET_GEOFENCE_PROFILE_LIST, "");
            String cur_execute_id = getCurExeProfileId();
            if (!TextUtils.isEmpty(cur_execute_id) && !cur_execute_id.equals(profileInfo.getString(ParameterName.proId))) {
                BBLog.d(Constants.TAG, "当前本地: localGeoProfileListStr  -->"+localGeoProfileListStr);
                if (!TextUtils.isEmpty(localGeoProfileListStr)) {
                    BBLog.d(Constants.TAG, "本地不为空，遍歷刪除");
                    JSONArray localProfileList = new JSONArray(localGeoProfileListStr);
                    int delete_idx = 0;
                    for (int i = 0; i < localProfileList.length(); i++) {
                        JSONObject profileJsonObj = (JSONObject) localProfileList.get(i);
                        if (cur_execute_id.equals(profileJsonObj.getString(ParameterName.proId))) {
                            delete_idx = i;
                            localProfileList.remove(delete_idx);
                            sp.edit().putString(SPKeys.WEBSOCKET_GEOFENCE_PROFILE_LIST, localProfileList.toString()).commit();
                            return;
                        }
                    }
                }
            } else {
                //如果是當前執行的profile，則直接執行default
                BBLog.d(Constants.TAG, "当前本地: localGeoProfileListStr  -->"+localGeoProfileListStr);
                if (!TextUtils.isEmpty(localGeoProfileListStr)) {
                    BBLog.d(Constants.TAG, "本地不为空，遍歷刪除");
                    JSONArray localProfileList = new JSONArray(localGeoProfileListStr);
                    for (int i = 0; i < localProfileList.length(); i++) {
                        JSONObject profileJsonObj = (JSONObject) localProfileList.get(i);
                        if ("1".equals(profileJsonObj.getString(ParameterName.isDefault))) {
                            GpsLocationManager.executeGeoProfile(profileJsonObj);
                            return;
                        }
                    }
                    //如果找不到default，则没有可执行的
                    GpsLocationManager.executeGeoProfile(null);
                }
            }
        } catch (JSONException e) {
            e.printStackTrace();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void executeOnDueGeoProfile() {
        if (Constants.B_UNBOX_RUNNING) {
            BBLog.i(BBLog.TAG, "终端正在做UNBOX，不执行Geo profile");
            return;
        }

        try {
            SharedPreferences sp = PreferenceManager.getDefaultSharedPreferences(ContextUtil.getInstance());
            String localGeoProfileListStr = sp.getString(SPKeys.WEBSOCKET_GEOFENCE_PROFILE_LIST, "");
            JSONObject defaultProfileJson = null;
            BBLog.d(Constants.TAG, "当前本地: localGeoProfileListStr  -->"+localGeoProfileListStr);
            if (!TextUtils.isEmpty(localGeoProfileListStr)) {
                JSONArray localProfileList = new JSONArray(localGeoProfileListStr);
                BBLog.d(Constants.TAG, "本地不为空，遍歷查找到期GeoProfile Size = " + localProfileList.length());
                for (int i = 0; i < localProfileList.length(); i++) {
                    JSONObject profileJsonObj = (JSONObject) localProfileList.get(i);
                    if (!(profileJsonObj.has(ParameterName.isDefault) && "1".equals(profileJsonObj.getString(ParameterName.isDefault)))) {
                        String beginDateStr = profileJsonObj.getString(ParameterName.beginDate);
                        String endDateStr = profileJsonObj.getString(ParameterName.endDate);
                        long nowTime = System.currentTimeMillis();
                        long begTime;
                        long endTime;
                        if (DateTimeUtils.isNumeric(beginDateStr) && DateTimeUtils.isNumeric(endDateStr)) {
                            begTime = new Long(beginDateStr).longValue();
                            endTime = new Long(endDateStr).longValue();
                        } else {
                            SimpleDateFormat sdf = new SimpleDateFormat(Constants.dateFormat);
                            begTime = sdf.parse(beginDateStr).getTime();
                            endTime = sdf.parse(endDateStr).getTime();
                        }

                        if (begTime < nowTime && endTime > nowTime) {
                            GpsLocationManager.executeGeoProfile(profileJsonObj);
                            localProfileList.remove(i);
                            sp.edit().putString(SPKeys.WEBSOCKET_GEOFENCE_PROFILE_LIST, localProfileList.toString()).commit();
                            return;
                        }
                    } else {
                        //先找出default后面用
                        defaultProfileJson = profileJsonObj;
                    }
                }
            }

            BBLog.e(BBLog.TAG, "没有新到期要执行的profile，检查当前是否过期");
            String profile = SharedPreferencesUtils.getSharePreferencesValue(UsualData.SHARED_PREFERENCES_GPS_GEOFENCE_CUR_PROFILE, "");
            if (!TextUtils.isEmpty(profile)) {
                JSONObject profileJson = new JSONObject(profile);
                if (profileJson.has(ParameterName.isDefault) && "1".equals(profileJson.getString(ParameterName.isDefault))) {
                    return;
                }
                String beginDateStr = profileJson.getString(ParameterName.beginDate);
                String endDateStr = profileJson.getString(ParameterName.endDate);
                long nowTime = System.currentTimeMillis();
                long endTime;
                if (DateTimeUtils.isNumeric(beginDateStr) && DateTimeUtils.isNumeric(endDateStr)) {
                    endTime = new Long(endDateStr).longValue();
                } else {
                    SimpleDateFormat sdf = new SimpleDateFormat(Constants.dateFormat);
                    endTime = sdf.parse(endDateStr).getTime();
                }
                if (nowTime > endTime) {
                    BBLog.e(BBLog.TAG, "当前过期，直接執行Default");
                    GpsLocationManager.executeGeoProfile(defaultProfileJson);
                }
            } else {
                //如果当前没有已执行的，执行default
                if (defaultProfileJson != null) {
                    GpsLocationManager.executeGeoProfile(defaultProfileJson);
                }
            }
        } catch (JSONException e) {
            e.printStackTrace();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static JSONObject getDefaultGeoProfile() {
        JSONObject defaultProfileJson = null;
        try {
            SharedPreferences sp = PreferenceManager.getDefaultSharedPreferences(ContextUtil.getInstance());
            String localGeoProfileListStr = sp.getString(SPKeys.WEBSOCKET_GEOFENCE_PROFILE_LIST, "");
            BBLog.d(Constants.TAG, "当前本地: localGeoProfileListStr  -->"+localGeoProfileListStr);
            if (!TextUtils.isEmpty(localGeoProfileListStr)) {
                JSONArray localProfileList = new JSONArray(localGeoProfileListStr);
                BBLog.d(Constants.TAG, "本地不为空，遍歷查找到期GeoProfile Size = " + localProfileList.length());
                for (int i = 0; i < localProfileList.length(); i++) {
                    JSONObject profileJsonObj = (JSONObject) localProfileList.get(i);
                    if (profileJsonObj.has(ParameterName.isDefault) && "1".equals(profileJsonObj.getString(ParameterName.isDefault))) {
                        //先找出default后面用
                        defaultProfileJson = profileJsonObj;
                    }
                }
            }
        } catch (JSONException e) {
            e.printStackTrace();
        } catch (Exception e) {
            e.printStackTrace();
        }

        return defaultProfileJson;
    }

    public static String getCurExeProfileId() {
        String proId = "";
        String profile = SharedPreferencesUtils.getSharePreferencesValue(UsualData.SHARED_PREFERENCES_GPS_GEOFENCE_CUR_PROFILE, "");
        if (TextUtils.isEmpty(profile)) {
            return null;
        }

        try {
            JSONObject profileJson = new JSONObject(profile);
            if (profileJson.has(ParameterName.proId)) {
                proId = profileJson.getString(ParameterName.proId);
            }
        } catch (JSONException e) {
            e.printStackTrace();
        }

        return proId;
    }
}
