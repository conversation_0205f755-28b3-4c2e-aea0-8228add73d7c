package com.dspread.mdm.service.ui.dialog

import android.app.Dialog
import android.content.Context
import android.graphics.Color
import android.graphics.Typeface
import android.os.Build
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.WindowManager
import android.widget.Button
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import com.dspread.mdm.service.R
import com.dspread.mdm.service.utils.log.Logger

/**
 * 地理围栏警告对话框
 */
class GeofenceWarningDialog private constructor(
    private val context: Context,
    private val onDismiss: (() -> Unit)? = null
) {
    
    companion object {
        private const val TAG = "GeofenceWarningDialog"
        
        /**
         * 显示地理围栏警告对话框
         */
        fun show(
            context: Context,
            onDismiss: (() -> Unit)? = null
        ): Dialog? {
            return try {
                val dialog = GeofenceWarningDialog(context, onDismiss)
                dialog.createDialog()
            } catch (e: Exception) {
                Logger.geoE("$TAG 创建对话框失败", e)
                null
            }
        }
    }
    
    /**
     * 创建对话框
     */
    private fun createDialog(): Dialog? {
        return try {
            val dialog = Dialog(context, R.style.GeofenceWarningDialogStyle)

            // 创建对话框布局
            val dialogView = createDialogView(dialog)
            dialog.setContentView(dialogView)

            // 设置对话框属性
            setupDialogProperties(dialog)

            // 显示对话框
            dialog.show()

            Logger.geo("$TAG 地理围栏警告对话框已显示")
            dialog

        } catch (e: Exception) {
            Logger.geoE("$TAG 创建对话框失败", e)
            null
        }
    }
    
    /**
     * 创建对话框视图
     */
    private fun createDialogView(dialog: Dialog): View {
        // 创建主容器
        val mainLayout = LinearLayout(context).apply {
            orientation = LinearLayout.VERTICAL
            layoutParams = LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.MATCH_PARENT,
                LinearLayout.LayoutParams.WRAP_CONTENT
            )
            setBackgroundResource(R.drawable.dialog_background)
        }
        
        // 添加图标区域
        val iconLayout = createIconLayout()
        mainLayout.addView(iconLayout)
        
        // 添加内容区域
        val contentLayout = createContentLayout(dialog)
        mainLayout.addView(contentLayout)
        
        return mainLayout
    }
    
    /**
     * 创建图标区域
     */
    private fun createIconLayout(): LinearLayout {
        return LinearLayout(context).apply {
            layoutParams = LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.MATCH_PARENT,
                LinearLayout.LayoutParams.WRAP_CONTENT
            ).apply {
                topMargin = dpToPx(17)
            }
            gravity = Gravity.CENTER
            setBackgroundColor(Color.WHITE)
            
            // 添加警告图标
            val iconView = ImageView(context).apply {
                layoutParams = LinearLayout.LayoutParams(
                    LinearLayout.LayoutParams.WRAP_CONTENT,
                    LinearLayout.LayoutParams.WRAP_CONTENT
                )
                setImageResource(R.drawable.ic_warning_geofence)
                scaleType = ImageView.ScaleType.CENTER
            }
            addView(iconView)
        }
    }
    
    /**
     * 创建内容区域
     */
    private fun createContentLayout(dialog: Dialog): LinearLayout {
        return LinearLayout(context).apply {
            layoutParams = LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.MATCH_PARENT,
                LinearLayout.LayoutParams.WRAP_CONTENT
            )
            orientation = LinearLayout.VERTICAL
            gravity = Gravity.CENTER
            setBackgroundResource(R.drawable.dialog_content_background)
            
            // 添加标题
            val titleView = createTitleView()
            addView(titleView)
            
            // 添加内容文本
            val contentView = createContentView()
            addView(contentView)
            
            // 添加OK按钮
            val buttonView = createButtonView(dialog)
            addView(buttonView)
        }
    }
    
    /**
     * 创建标题视图
     */
    private fun createTitleView(): TextView {
        return TextView(context).apply {
            layoutParams = LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.WRAP_CONTENT,
                LinearLayout.LayoutParams.WRAP_CONTENT
            ).apply {
                leftMargin = dpToPx(16)
                rightMargin = dpToPx(16)
                topMargin = dpToPx(20)
            }
            text = "OUT OF GEOFENCE"
            gravity = Gravity.CENTER
            textSize = 24f
            setTypeface(null, Typeface.BOLD)
            setTextColor(Color.parseColor("#3a3c46"))
        }
    }
    
    /**
     * 创建内容视图
     */
    private fun createContentView(): TextView {
        return TextView(context).apply {
            layoutParams = LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.WRAP_CONTENT,
                LinearLayout.LayoutParams.WRAP_CONTENT
            ).apply {
                leftMargin = dpToPx(15)
                rightMargin = dpToPx(15)
                topMargin = dpToPx(10)
            }
            text = "Please return to the store, otherwise the device will be locked."
            gravity = Gravity.CENTER
            textSize = 16f
            setTextColor(Color.parseColor("#3a3c46"))
        }
    }
    
    /**
     * 创建按钮视图
     */
    private fun createButtonView(dialog: Dialog): Button {
        return Button(context).apply {
            layoutParams = LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.MATCH_PARENT,
                LinearLayout.LayoutParams.WRAP_CONTENT
            ).apply {
                leftMargin = dpToPx(16)
                rightMargin = dpToPx(16)
                topMargin = dpToPx(18)
                bottomMargin = dpToPx(20)
            }
            text = "OK"
            textSize = 20f
            setTextColor(Color.WHITE)
            setBackgroundResource(R.drawable.button_primary)
            isAllCaps = false
            
            // 设置点击事件
            setOnClickListener {
                Logger.geo("$TAG OK按钮被点击，关闭警告对话框")
                onDismiss?.invoke()
                dialog.dismiss()
            }
        }
    }
    
    /**
     * 设置对话框属性
     */
    private fun setupDialogProperties(dialog: Dialog) {
        dialog.setCanceledOnTouchOutside(false)
        dialog.setCancelable(false)
        
        // 设置为系统级窗口
        dialog.window?.let { window ->
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                window.setType(WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY)
            } else {
                @Suppress("DEPRECATION")
                window.setType(WindowManager.LayoutParams.TYPE_SYSTEM_ALERT)
            }
            
            // 设置窗口属性
            window.attributes = window.attributes.apply {
                width = WindowManager.LayoutParams.WRAP_CONTENT
                height = WindowManager.LayoutParams.WRAP_CONTENT
                gravity = Gravity.CENTER
            }
        }
    }
    
    /**
     * dp转px
     */
    private fun dpToPx(dp: Int): Int {
        val density = context.resources.displayMetrics.density
        return (dp * density + 0.5f).toInt()
    }
}
