package com.dspread.mdm.service.modules.rulebase.engine

import android.content.Context
import com.dspread.mdm.service.SmartMdmServiceApp
import com.dspread.mdm.service.constants.TaskStateConstants
import com.dspread.mdm.service.modules.rulebase.core.RuleStateMachine
import com.dspread.mdm.service.modules.rulebase.core.RuleStateMachine.RuleState
import com.dspread.mdm.service.modules.rulebase.core.RuleStateMachine.StateTransitionEvent
import com.dspread.mdm.service.modules.rulebase.core.RuleStateMachine.RuleStateChangeListener
import com.dspread.mdm.service.modules.rulebase.model.Rule
import com.dspread.mdm.service.modules.rulebase.model.RuleApp
import com.dspread.mdm.service.network.websocket.message.WsMessageSender
import com.dspread.mdm.service.platform.api.app.AppManagerApi
import com.dspread.mdm.service.platform.api.model.SystemOperationResult
import com.dspread.mdm.service.network.https.HttpDownloader
import com.dspread.mdm.service.utils.log.Logger
import org.json.JSONArray
import org.json.JSONObject
import java.io.File
import java.util.concurrent.ConcurrentHashMap

/**
 * 规则执行引擎 - 完整版本
 * 负责协调规则的实际执行过程，包括应用下载、安装、卸载
 */
class RuleExecutionEngine private constructor(private val context: Context) {
    
    companion object {
        private const val TAG = "RuleExecutionEngine"
        
        @Volatile
        private var instance: RuleExecutionEngine? = null
        
        fun getInstance(context: Context): RuleExecutionEngine {
            return instance ?: synchronized(this) {
                instance ?: RuleExecutionEngine(context.applicationContext).also { instance = it }
            }
        }
    }
    
    // 状态机引用
    private val stateMachine = RuleStateMachine.getInstance()
    
    // 执行状态映射
    private val executionStates = ConcurrentHashMap<String, ExecutionState>()
    
    // 规则应用状态跟踪
    private val ruleAppStatusMap = ConcurrentHashMap<String, JSONArray>()
    
    // 卸载回调跟踪
    private val uninstallCallbackMap = ConcurrentHashMap<String, (String, Int, String) -> Unit>()
    
    // 安装回调跟踪
    private val installCallbackMap = ConcurrentHashMap<String, (String, Int, String) -> Unit>()
    
    // 下载进度跟踪
    private val lastReportedProgress = ConcurrentHashMap<String, Int>()
    
    /**
     * 执行状态
     */
    data class ExecutionState(
        val ruleId: String,
        val rule: Rule,
        val appStates: MutableMap<String, AppState> = mutableMapOf(),
        val startTime: Long = System.currentTimeMillis(),
        var endTime: Long? = null,
        var isCompleted: Boolean = false,
        var successCount: Int = 0,
        var failedCount: Int = 0
    )
    
    /**
     * 应用状态
     */
    data class AppState(
        val packageName: String,
        val taskType: String, // "01"=安装, "02"=卸载
        val versionName: String? = null,
        val versionCode: String? = null,
        var status: String = "PENDING",
        var result: String? = null,
        var errorMessage: String? = null
    )
    

    
    /**
     * 执行规则
     */
    fun executeRule(rule: Rule): Boolean {
        return try {
            val ruleId = rule.ruleId
            
            // 检查是否已在执行
            if (executionStates.containsKey(ruleId)) {
                Logger.ruleE("$TAG 规则已存在，不能重复执行: $ruleId")
                return false
            }
            
            // 初始化状态机状态
            if (!stateMachine.initializeRule(ruleId)) {
                Logger.ruleE("$TAG 规则状态初始化失败: $ruleId")
                return false
            }
            
            // 创建执行状态
            val executionState = ExecutionState(ruleId, rule)
            
            // 初始化应用状态
            rule.appList?.forEach { app ->
                executionState.appStates[app.packName] = AppState(
                    packageName = app.packName,
                    taskType = "01", // 安装
                    versionName = app.versionName,
                    versionCode = app.versionCode
                )
            }
            
            rule.deleteAppList?.forEach { app ->
                executionState.appStates[app.packName] = AppState(
                    packageName = app.packName,
                    taskType = "02", // 卸载
                    versionName = app.versionName,
                    versionCode = app.versionCode
                )
            }
            
            // 保存执行状态
            executionStates[ruleId] = executionState
            
            // 初始化规则应用状态跟踪
            initRuleAppStatus(ruleId, rule)
            
            // 更新状态机状态 - 按照正确的流程：TODO -> STARTING -> EXECUTING
            stateMachine.updateRuleState(ruleId, RuleState.STARTING)
            stateMachine.updateRuleState(ruleId, RuleState.EXECUTING)
            
            // 开始实际执行
            startRuleExecution(rule)
            
            Logger.rule("$TAG 规则执行启动成功: $ruleId")
            true
            
        } catch (e: Exception) {
            Logger.ruleE("$TAG 规则执行启动失败: ${rule.ruleId}", e)
            false
        }
    }
    
    /**
     * 开始规则执行
     */
    private fun startRuleExecution(rule: Rule) {
        try {
            Logger.rule("$TAG 开始执行规则: ${rule.ruleId}")
            
            // 1. 先执行卸载应用
            if (rule.deleteAppList.isNotEmpty()) {
                Logger.rule("$TAG 执行卸载应用，数量: ${rule.deleteAppList.size}")
                executeUninstallApps(rule.deleteAppList, rule.ruleId)
            }
            
            // 2. 再执行安装应用
            if (rule.appList.isNotEmpty()) {
                Logger.rule("$TAG 执行安装应用，数量: ${rule.appList.size}")
                executeInstallApps(rule.appList, rule.ruleId)
            }
            
            // 3. 如果没有应用需要处理，直接标记为成功
            if (rule.appList.isEmpty() && rule.deleteAppList.isEmpty()) {
                Logger.rule("$TAG 规则无应用需要处理，标记为成功")
                updateAppStatus(rule.ruleId, "SYSTEM", TaskStateConstants.RULEBASED_SUCCESS, "无应用需要处理")
                checkRuleCompletion(rule.ruleId)
            }
            
        } catch (e: Exception) {
            Logger.ruleE("$TAG 执行规则失败: ${rule.ruleId}", e)
            updateAppStatus(rule.ruleId, "SYSTEM", TaskStateConstants.FAILED, "执行异常: ${e.message}")
            checkRuleCompletion(rule.ruleId)
        }
    }
    
    /**
     * 执行卸载应用
     */
    private fun executeUninstallApps(deleteAppList: List<RuleApp>, ruleId: String) {
        try {
            Logger.rule("$TAG 开始卸载应用，数量: ${deleteAppList.size}")
            
            val appManagerApi = AppManagerApi(context)
            
            for (app in deleteAppList) {
                Logger.rule("$TAG 卸载应用: ${app.packName}")
                uninstallApp(app, ruleId, appManagerApi)
            }
            
        } catch (e: Exception) {
            Logger.ruleE("$TAG 执行卸载应用失败", e)
        }
    }
    
    /**
     * 卸载单个应用
     */
    private fun uninstallApp(app: RuleApp, ruleId: String, appManagerApi: AppManagerApi) {
        try {
            // 1. 检查应用是否存在
            if (!appManagerApi.isApplicationInstalled(app.packName)) {
                Logger.rule("$TAG 应用不存在，视为卸载成功: ${app.packName}")
                updateAppStatus(ruleId, app.packName, TaskStateConstants.UNINSTALL_SUCCESS, "应用不存在，视为卸载成功")
                checkRuleCompletion(ruleId)
                return
            }
            
            // 2. 更新状态为卸载中
            updateAppStatus(ruleId, app.packName, TaskStateConstants.UNINSTALL_ING)
            
            // 3. 注册卸载回调
            Logger.rule("$TAG 注册卸载回调: ${app.packName} (规则ID: $ruleId)")
            uninstallCallbackMap[app.packName] = { pkg: String, returnCode: Int, errorMsg: String ->
                Logger.rule("$TAG 卸载回调被触发: pkg=$pkg, returnCode=$returnCode, error=$errorMsg")
                
                if (returnCode == 1) {
                    Logger.rule("$TAG 应用卸载启动成功: ${app.packName}")
                    // 卸载启动成功，等待系统广播确认
                } else {
                    Logger.ruleE("$TAG 应用卸载失败: ${app.packName}, error=$errorMsg")
                    // 卸载失败，直接更新状态
                    updateAppStatus(ruleId, app.packName, TaskStateConstants.UNINSTALL_FAILED, errorMsg)
                    checkRuleCompletion(ruleId)
                }
                
                uninstallCallbackMap.remove(pkg)
            }
            
            // 4. 执行卸载
            val result = appManagerApi.uninstallAppSilentlyAsync(app.packName)
            if (!result.isSuccess) {
                val errorMsg = when (result) {
                    is SystemOperationResult.Failure -> result.error
                    else -> "启动卸载失败"
                }
                Logger.ruleE("$TAG 启动卸载失败: ${app.packName}, $errorMsg")
                updateAppStatus(ruleId, app.packName, TaskStateConstants.UNINSTALL_FAILED, errorMsg)
                checkRuleCompletion(ruleId)
                uninstallCallbackMap.remove(app.packName)
            }
            
        } catch (e: Exception) {
            Logger.ruleE("$TAG 卸载应用失败: ${app.packName}", e)
            updateAppStatus(ruleId, app.packName, TaskStateConstants.UNINSTALL_FAILED, e.message)
            checkRuleCompletion(ruleId)
        }
    }
    
    /**
     * 执行安装应用
     */
    private fun executeInstallApps(appList: List<RuleApp>, ruleId: String) {
        try {
            Logger.rule("$TAG 开始安装应用，数量: ${appList.size}")
            
            for (app in appList) {
                Logger.rule("$TAG 安装应用: ${app.packName}, URL: ${app.apkUrl}")
                downloadAndInstallApp(app, ruleId)
            }
            
        } catch (e: Exception) {
            Logger.ruleE("$TAG 执行安装应用失败", e)
        }
    }
    
    /**
     * 下载并安装单个应用
     */
    private fun downloadAndInstallApp(app: RuleApp, ruleId: String) {
        val taskId = "rule_${ruleId}_${app.packName}"
        
        try {
            val appManagerApi = AppManagerApi(context)
            
            // 1. 检查应用是否已安装且版本一致
            if (isAppAlreadyInstalled(appManagerApi, app)) {
                Logger.rule("$TAG 应用已安装且版本一致: ${app.packName}")
                updateAppStatus(ruleId, app.packName, TaskStateConstants.INSTALL_SUCCESS)
                checkRuleCompletion(ruleId)
                return
            }
            
            // 2. 更新状态为下载中
            updateAppStatus(ruleId, app.packName, TaskStateConstants.DOWNLOAD_ING)
            
            // 3. 开始下载APK（异步）
            Thread {
                try {
                    val downloadDir = File(context.filesDir, "downloads")
                    if (!downloadDir.exists()) downloadDir.mkdirs()
                    
                    val apkFile = File(downloadDir, "$taskId.apk")
                    
                    // 使用HttpDownloader下载
                    val success = HttpDownloader.fileDownloadByUrlWithRetry(
                        app.apkUrl,
                        apkFile.absolutePath,
                        app.apkSize,
                        app.apkMd5,
                        object : HttpDownloader.FileDownloadCallBack {
                            override fun requestSuccess() {
                                Logger.rule("$TAG APK下载成功: ${apkFile.absolutePath}")
                                updateAppStatus(ruleId, app.packName, TaskStateConstants.DOWNLOAD_SUCCESS)
                                
                                // 开始安装
                                installDownloadedApk(app, ruleId, apkFile.absolutePath, appManagerApi)
                            }
                            
                            override fun requestFail(errorCode: Int, errorStr: String) {
                                Logger.ruleE("$TAG APK下载失败: $errorStr (错误码: $errorCode)")
                                updateAppStatus(ruleId, app.packName, TaskStateConstants.DOWNLOAD_FAILED, errorStr)
                                checkRuleCompletion(ruleId)
                            }
                            
                            override fun onDownloading(curFileSize: Long, fileSize: Long) {
                                // 计算下载进度百分比
                                val progress = if (fileSize > 0) {
                                    (curFileSize * 100 / fileSize).toInt()
                                } else {
                                    0
                                }
                                
                                // 只记录本地日志，不上报进度
                                if (progress % 10 == 0 && progress != lastReportedProgress[app.packName]) {
                                    lastReportedProgress[app.packName] = progress
                                    Logger.rule("$TAG 下载进度: ${app.packName} - $progress%")
                                    
                                    // 更新应用状态，但不上报
                                    val progressMsg = "下载中: $progress%"
                                    updateAppStatus(ruleId, app.packName, TaskStateConstants.DOWNLOAD_ING, progressMsg, false)
                                }
                            }
                        }
                    )
                    
                    if (!success) {
                        Logger.ruleE("$TAG 下载失败，未调用回调")
                        updateAppStatus(ruleId, app.packName, TaskStateConstants.DOWNLOAD_FAILED, "下载失败")
                        checkRuleCompletion(ruleId)
                    }
                    
                } catch (e: Exception) {
                    Logger.ruleE("$TAG 下载过程异常", e)
                    updateAppStatus(ruleId, app.packName, TaskStateConstants.DOWNLOAD_FAILED, e.message)
                    checkRuleCompletion(ruleId)
                }
            }.start()
            
        } catch (e: Exception) {
            Logger.ruleE("$TAG 下载安装应用失败: ${app.packName}", e)
            updateAppStatus(ruleId, app.packName, TaskStateConstants.INSTALL_FAILED, e.message)
            checkRuleCompletion(ruleId)
        }
    }
    
    /**
     * 安装已下载的APK
     */
    private fun installDownloadedApk(app: RuleApp, ruleId: String, apkPath: String, appManagerApi: AppManagerApi) {
        try {
            Logger.rule("$TAG 开始安装APK: $apkPath")
            updateAppStatus(ruleId, app.packName, TaskStateConstants.INSTALL_ING)
            
            // 注册安装回调
            Logger.rule("$TAG 注册安装回调: ${app.packName} (规则ID: $ruleId)")
            installCallbackMap[app.packName] = { pkg: String, returnCode: Int, errorMsg: String ->
                Logger.rule("$TAG 安装回调被触发: pkg=$pkg, returnCode=$returnCode, error=$errorMsg")
                
                if (returnCode == 1) {
                    Logger.rule("$TAG 应用安装启动成功: ${app.packName}")
                    // 安装启动成功，等待系统广播确认
                } else {
                    Logger.ruleE("$TAG 应用安装失败: ${app.packName}, error=$errorMsg")
                    // 安装失败，直接更新状态
                    updateAppStatus(ruleId, app.packName, TaskStateConstants.INSTALL_FAILED, errorMsg, false)
                    checkRuleCompletion(ruleId)
                }
                
                installCallbackMap.remove(pkg)
            }
            
            // 执行安装
            val result = appManagerApi.installApkSilentlyAsync(apkPath)
            if (!result.isSuccess) {
                val errorMsg = when (result) {
                    is SystemOperationResult.Failure -> result.error
                    else -> "启动安装失败"
                }
                Logger.ruleE("$TAG 启动安装失败: ${app.packName}, $errorMsg")
                updateAppStatus(ruleId, app.packName, TaskStateConstants.INSTALL_FAILED, errorMsg, false)
                checkRuleCompletion(ruleId)
                installCallbackMap.remove(app.packName)
            }
            
        } catch (e: Exception) {
            Logger.ruleE("$TAG 安装APK失败: ${app.packName}", e)
            updateAppStatus(ruleId, app.packName, TaskStateConstants.INSTALL_FAILED, e.message, false)
            checkRuleCompletion(ruleId)
        }
    }
    
    /**
     * 初始化规则应用状态跟踪
     */
    private fun initRuleAppStatus(ruleId: String, rule: Rule) {
        try {
            val resultApkListArray = JSONArray()
            
            // 添加安装应用状态
            rule.appList.forEach { app ->
                Logger.rule("$TAG 创建应用状态: packName=${app.packName}, apkName=${app.apkName}")
                
                val appStatus = JSONObject().apply {
                    put("packName", app.packName)
                    put("apkName", app.apkName)
                    put("versionName", app.versionName)
                    put("versionCode", app.versionCode)
                    put("apkUrl", app.apkUrl ?: "")
                    put("apkMd5", app.apkMd5 ?: "")
                    put("apkSize", app.apkSize ?: 0)
                    put("installBy", app.installBy ?: "0")
                    put("isSystemApp", app.isSystemApp ?: false)
                    put("priority", app.priority ?: 0)
                    put("result", TaskStateConstants.EXECUTE_WAITING)  // 按照旧项目使用W01
                    put("taskType", TaskStateConstants.TASK_INSTALL)
                }
                resultApkListArray.put(appStatus)
            }
            
            // 添加卸载应用状态
            rule.deleteAppList.forEach { app ->
                val appStatus = JSONObject().apply {
                    put("packName", app.packName)
                    put("apkName", app.apkName)
                    put("versionName", app.versionName)
                    put("versionCode", app.versionCode)
                    put("apkUrl", app.apkUrl ?: "")
                    put("apkMd5", app.apkMd5 ?: "")
                    put("apkSize", app.apkSize ?: 0)
                    put("installBy", app.installBy ?: "0")
                    put("isSystemApp", app.isSystemApp ?: false)
                    put("priority", app.priority ?: 0)
                    put("result", TaskStateConstants.EXECUTE_WAITING)  // 按照旧项目使用W01
                    put("taskType", TaskStateConstants.TASK_UNINSTALL)
                }
                resultApkListArray.put(appStatus)
            }
            
            ruleAppStatusMap[ruleId] = resultApkListArray
            Logger.rule("$TAG 初始化规则应用状态: $ruleId, 应用数量: ${resultApkListArray.length()}")
            
        } catch (e: Exception) {
            Logger.ruleE("$TAG 初始化规则应用状态失败: $ruleId", e)
        }
    }
    
    /**
     * 更新应用状态
     */
        fun updateAppStatus(ruleId: String, packageName: String, status: String, errorMsg: String? = null, shouldReport: Boolean = true) {
        try {
            val resultApkListArray = ruleAppStatusMap[ruleId] ?: return

            for (i in 0 until resultApkListArray.length()) {
                val appStatus = resultApkListArray.getJSONObject(i)
                if (packageName == appStatus.getString("packName")) {
                    appStatus.put("result", status)

                    // 更新errorMsg字段
                    if (errorMsg != null) {
                        appStatus.put("errorMsg", errorMsg)
                    } else {
                        // 如果状态是成功状态，清除errorMsg
                        when (status) {
                            TaskStateConstants.DOWNLOAD_SUCCESS,
                            TaskStateConstants.INSTALL_SUCCESS,
                            TaskStateConstants.UNINSTALL_SUCCESS -> {
                                if (appStatus.has("errorMsg")) {
                                    appStatus.remove("errorMsg")
                                }
                            }
                        }
                    }
                    break
                }
            }

            // 减少频繁的状态更新日志，只在关键状态变化时记录
            if (status != TaskStateConstants.DOWNLOAD_ING || errorMsg == null || !errorMsg.contains("下载中:")) {
                Logger.rule("$TAG 更新应用状态: $ruleId, $packageName -> $status")
            }

            // 每次应用状态变化都上送C0107
            if (shouldReport) {
                reportRuleExecutingStatus(ruleId, resultApkListArray)
            }

        } catch (e: Exception) {
            Logger.ruleE("$TAG 更新应用状态失败: $ruleId, $packageName", e)
        }
    }
    
    // 移除shouldReportImmediately方法，因为每次状态变化都上送
    
    // 移除separateAppsByStatus方法，因为按照旧项目逻辑不需要分离成功和失败列表
    
    /**
     * 上报规则状态
     */
    fun reportRuleState(ruleId: String, state: String) {
        try {
            val resultApkListArray = ruleAppStatusMap[ruleId]
            
            // 按照旧项目逻辑，上送完整的应用列表
            WsMessageSender.uploadRulebasedResult(
                ruleId = ruleId,
                result = state,
                failedList = resultApkListArray,  // 上送完整列表
                successList = null
            )
            
            Logger.rule("$TAG 上报规则状态: $ruleId -> $state")
        } catch (e: Exception) {
            Logger.ruleE("$TAG 上报规则状态失败: $ruleId -> $state", e)
        }
    }
    
    /**
     * 上报规则执行中状态
     */
    private fun reportRuleExecutingStatus(ruleId: String, resultApkListArray: JSONArray) {
        try {
            // 按照旧项目逻辑，上送完整的应用列表
            WsMessageSender.uploadRulebasedResult(
                ruleId = ruleId,
                result = TaskStateConstants.RULEBASED_EXECUTING,
                failedList = resultApkListArray,  // 上送完整列表
                successList = null
            )
            
            Logger.rule("$TAG 上报规则执行中状态: $ruleId, 应用数量: ${resultApkListArray.length()}")
        } catch (e: Exception) {
            Logger.ruleE("$TAG 上报规则执行中状态失败: $ruleId", e)
        }
    }
    
    /**
     * 检查规则是否完成并上报最终结果
     */
    fun checkRuleCompletion(ruleId: String) {
        try {
            val resultApkListArray = ruleAppStatusMap[ruleId] ?: return
            
            var allCompleted = true
            var anyFailed = false
            var completedCount = 0
            var totalCount = resultApkListArray.length()
            
            Logger.rule("$TAG 开始检查规则完成状态: $ruleId, 总应用数: $totalCount")
            
            // 检查所有应用状态
            for (i in 0 until resultApkListArray.length()) {
                val appStatus = resultApkListArray.getJSONObject(i)
                val result = appStatus.getString("result")
                val packageName = appStatus.optString("packName", "unknown")
                
                Logger.rule("$TAG 检查应用状态: $packageName -> $result")
                
                when (result) {
                    // 进行中的状态
                    TaskStateConstants.EXECUTE_WAITING,
                    TaskStateConstants.DOWNLOAD_ING,
                    TaskStateConstants.INSTALL_ING,
                    TaskStateConstants.UNINSTALL_ING -> {
                        Logger.rule("$TAG 发现未完成状态: $packageName -> $result")
                        allCompleted = false
                        break
                    }
                    // 失败状态
                    TaskStateConstants.DOWNLOAD_FAILED,
                    TaskStateConstants.INSTALL_FAILED,
                    TaskStateConstants.UNINSTALL_FAILED -> {
                        Logger.rule("$TAG 发现失败状态: $packageName -> $result")
                        anyFailed = true
                        completedCount++
                    }
                    // 成功状态
                    TaskStateConstants.INSTALL_SUCCESS,
                    TaskStateConstants.UNINSTALL_SUCCESS,
                    TaskStateConstants.DOWNLOAD_SUCCESS -> {
                        Logger.rule("$TAG 发现成功状态: $packageName -> $result")
                        completedCount++
                    }
                    else -> {
                        Logger.ruleW("$TAG 未知状态: $packageName -> $result，视为未完成")
                        allCompleted = false
                        break
                    }
                }
            }
            
            Logger.rule("$TAG 规则完成检查结果: $ruleId, 全部完成: $allCompleted, 完成数: $completedCount/$totalCount, 有失败: $anyFailed")
            
            if (allCompleted) {
                // 按照旧项目逻辑处理最终状态
                val currentState = stateMachine.getRuleState(ruleId)
                
                if (anyFailed) {
                    // 有失败，上送RULEBASED_EXECUTING
                    Logger.rule("$TAG 规则执行有失败，上送RULEBASED_EXECUTING: $ruleId")
                    reportRuleState(ruleId, TaskStateConstants.RULEBASED_EXECUTING)
                    
                    // 更新状态机状态为PENDING（可重试）
                    stateMachine.updateRuleState(ruleId, RuleState.PENDING)
                } else {
                    // 全部成功，上送RULEBASED_SUCCESS
                    Logger.rule("$TAG 规则执行全部成功，上送RULEBASED_SUCCESS: $ruleId")
                    reportRuleState(ruleId, TaskStateConstants.RULEBASED_SUCCESS)
                    
                    // 更新状态机状态为COMPLETED
                    stateMachine.updateRuleState(ruleId, RuleState.COMPLETED)
                }
                
                // 清理状态跟踪
                ruleAppStatusMap.remove(ruleId)
                lastReportedProgress.clear()
                
                Logger.rule("$TAG 规则执行完成: $ruleId, 有失败: $anyFailed")
            } else {
                Logger.rule("$TAG 规则未完成: $ruleId, 完成数: $completedCount/$totalCount")
            }
            
        } catch (e: Exception) {
            Logger.ruleE("$TAG 检查规则完成状态失败: $ruleId", e)
        }
    }
    
    /**
     * 检查应用是否已安装且版本一致
     */
    private fun isAppAlreadyInstalled(appManagerApi: AppManagerApi, app: RuleApp): Boolean {
        return try {
            Logger.rule("$TAG 检查应用安装状态: ${app.packName}")
            
            val isInstalled = appManagerApi.isApplicationInstalled(app.packName)
            Logger.rule("$TAG 应用是否已安装: $isInstalled")
            
            if (!isInstalled) {
                Logger.rule("$TAG 应用未安装，需要安装")
                false
            } else {
                val installedInfo = appManagerApi.getApplicationInfo(app.packName)
                Logger.rule("$TAG 已安装应用信息: $installedInfo")
                
                installedInfo?.let { info ->
                    Logger.rule("$TAG 版本比较: 已安装=${info.versionName}(${info.versionCode}) vs 规则=${app.versionName}(${app.versionCode})")
                    
                    val versionMatch = info.versionName == app.versionName &&
                                     info.versionCode.toString() == app.versionCode
                    
                    Logger.rule("$TAG 版本匹配结果: $versionMatch")
                    versionMatch
                } ?: run {
                    Logger.ruleW("$TAG 无法获取已安装应用信息，视为需要安装")
                    false
                }
            }
        } catch (e: Exception) {
            Logger.ruleE("$TAG 检查应用安装状态失败: ${app.packName}", e)
            false
        }
    }
    
    /**
     * 处理系统广播通知的应用卸载事件
     */
    fun onAppUninstalled(packageName: String) {
        try {
            Logger.rule("$TAG 收到系统广播，应用已卸载: $packageName")
            
            // 查找并触发对应的卸载回调
            uninstallCallbackMap[packageName]?.let { callback ->
                Logger.rule("$TAG 找到匹配的卸载回调，触发: $packageName")
                callback.invoke(packageName, 1, "")
                
                // 触发回调后，更新状态为卸载成功
                updateAppStateFromBroadcast(packageName, TaskStateConstants.UNINSTALL_SUCCESS)
            } ?: run {
                Logger.rule("$TAG 未找到匹配的卸载回调: $packageName")
                
                // 如果没有找到回调，尝试通过规则ID查找并更新状态
                updateAppStateFromBroadcast(packageName, TaskStateConstants.UNINSTALL_SUCCESS)
            }
            
        } catch (e: Exception) {
            Logger.ruleE("$TAG 处理应用卸载广播失败: $packageName", e)
        }
    }
    
    /**
     * 处理系统广播通知的应用安装事件
     */
    fun onAppInstalled(packageName: String) {
        try {
            Logger.rule("$TAG 收到系统广播，应用已安装: $packageName")
            
            // 查找并触发对应的安装回调
            installCallbackMap[packageName]?.let { callback ->
                Logger.rule("$TAG 找到匹配的安装回调，触发: $packageName")
                callback.invoke(packageName, 1, "")
                
                // 触发回调后，更新状态为安装成功
                updateAppStateFromBroadcast(packageName, TaskStateConstants.INSTALL_SUCCESS)
            } ?: run {
                Logger.rule("$TAG 未找到匹配的安装回调: $packageName")
                
                // 如果没有找到回调，尝试通过规则ID查找并更新状态
                updateAppStateFromBroadcast(packageName, TaskStateConstants.INSTALL_SUCCESS)
            }
            
        } catch (e: Exception) {
            Logger.ruleE("$TAG 处理应用安装广播失败: $packageName", e)
        }
    }
    
    /**
     * 更新应用状态并触发状态转换
     */
    fun updateAppState(ruleId: String, packageName: String, newStatus: String): Boolean {
        return try {
            val executionState = executionStates[ruleId]
            if (executionState == null) {
                Logger.ruleE("$TAG 规则执行状态不存在: $ruleId")
                return false
            }
            
            val appState = executionState.appStates[packageName]
            if (appState == null) {
                Logger.ruleE("$TAG 应用状态不存在: $ruleId, $packageName")
                return false
            }
            
            val oldStatus = appState.status
            appState.status = newStatus
            
            Logger.rule("$TAG 应用状态更新: $ruleId, $packageName, $oldStatus -> $newStatus")
            
            // 如果应用状态是完成状态，立即检查规则完成情况
            if (isAppStateCompleted(newStatus)) {
                Logger.rule("$TAG 应用状态完成，触发规则完成检查: $ruleId, $packageName -> $newStatus")
                checkRuleCompletion(ruleId)
            }
            
            true
            
        } catch (e: Exception) {
            Logger.ruleE("$TAG 更新应用状态失败: $ruleId, $packageName", e)
            false
        }
    }
    
    /**
     * 判断应用状态是否为完成状态
     */
    private fun isAppStateCompleted(status: String): Boolean {
        return when (status) {
            "INSTALL_SUCCESS", "UNINSTALL_SUCCESS", "INSTALL_FAILED", "UNINSTALL_FAILED" -> true
            else -> false
        }
    }
    

    
    /**
     * 停止规则执行
     */
    fun stopRuleExecution(ruleId: String): Boolean {
        return try {
            val executionState = executionStates[ruleId]
            if (executionState != null) {
                executionState.isCompleted = true
                executionState.endTime = System.currentTimeMillis()
                
                // 更新状态机状态
                stateMachine.updateRuleState(ruleId, RuleState.FAILED)
            }
            
            Logger.rule("$TAG 规则执行已停止: $ruleId")
            true
            
        } catch (e: Exception) {
            Logger.ruleE("$TAG 停止规则执行失败: $ruleId", e)
            false
        }
    }
    
    /**
     * 清理规则执行状态
     */
    fun cleanupRuleExecution(ruleId: String): Boolean {
        return try {
            executionStates.remove(ruleId)
            ruleAppStatusMap.remove(ruleId)
            Logger.rule("$TAG 规则执行状态已清理: $ruleId")
            true
        } catch (e: Exception) {
            Logger.ruleE("$TAG 清理规则执行状态失败: $ruleId", e)
            false
        }
    }
    
    /**
     * 清理所有规则执行状态
     */
    fun clearAllRules(): Boolean {
        return try {
            Logger.rule("$TAG 开始清理所有规则执行状态...")
            
            // 清理所有执行状态
            executionStates.clear()
            
            // 清理所有规则应用状态
            ruleAppStatusMap.clear()
            
            // 清理回调映射
            uninstallCallbackMap.clear()
            installCallbackMap.clear()
            
            // 清理下载进度跟踪
            lastReportedProgress.clear()
            
            Logger.rule("$TAG 所有规则执行状态清理完成")
            true
            
        } catch (e: Exception) {
            Logger.ruleE("$TAG 清理所有规则执行状态失败", e)
            false
        }
    }
    
    /**
     * 获取规则执行状态
     */
    fun getExecutionState(ruleId: String): ExecutionState? {
        return executionStates[ruleId]
    }
    
    /**
     * 获取规则执行状态 - 兼容性方法
     */
    fun getRuleExecutionState(ruleId: String): ExecutionState? {
        return executionStates[ruleId]
    }
    
    /**
     * 获取所有执行状态
     */
    fun getAllExecutionStates(): Map<String, ExecutionState> {
        return executionStates.toMap()
    }

    /**
     * 从广播事件更新应用状态
     */
        private fun updateAppStateFromBroadcast(packageName: String, status: String) {
        try {
            // 遍历所有规则，查找包含该应用包名的规则
            for ((ruleId, resultApkListArray) in ruleAppStatusMap) {
                for (i in 0 until resultApkListArray.length()) {
                    val appStatus = resultApkListArray.getJSONObject(i)
                    if (packageName == appStatus.getString("packName")) {
                        val currentResult = appStatus.getString("result")

                        // 检查规则是否已完成
                        val ruleState = stateMachine.getRuleState(ruleId)
                        if (ruleState?.code == "R03" || ruleState?.code == "FAILED") {
                            Logger.rule("$TAG 规则已完成，跳过广播更新: $ruleId, $packageName -> ${ruleState.code}")
                            return
                        }

                        // 只有当状态不是完成状态时才更新
                        if (!isAppStateCompleted(currentResult)) {
                            Logger.rule("$TAG 从广播更新应用状态: $ruleId, $packageName -> $status")

                            // 直接更新应用状态
                            appStatus.put("result", status)

                            // 清除errorMsg字段（如果存在）
                            if (appStatus.has("errorMsg")) {
                                appStatus.remove("errorMsg")
                            }

                            // 上报状态
                            reportRuleExecutingStatus(ruleId, resultApkListArray)

                            // 触发规则完成检查
                            checkRuleCompletion(ruleId)
                        } else {
                            Logger.rule("$TAG 应用状态已完成，跳过广播更新: $ruleId, $packageName -> $currentResult")
                        }
                        return
                    }
                }
            }

            Logger.rule("$TAG 未找到匹配的规则应用: $packageName")

        } catch (e: Exception) {
            Logger.ruleE("$TAG 从广播更新应用状态失败: $packageName", e)
        }
    }
} 