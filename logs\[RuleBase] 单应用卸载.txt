2025-08-14 18:33:20.391 19136-19207 Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"msg_state":"P","request_time":"1755167598918","data":{"ruleList":[{"deleteAppList":[{"apkName":"Via","appId":"353795033bc0410dac6dee5cb8702b06","packName":"mark.via","versionName":"6.6.0","versionCode":"20250713"}],"modifyDate":"2025-08-14 10:33:18","endDate":"9999-12-31 23:59:59","restartAfter":"0","installBy":"0","beginDate":"2024-08-14 10:33:18","appList":[],"serviceList":[],"action":"A","ruleName":"d60_test_296","ruleId":"01f42138486940dd8ba8a136d84e239d","createDate":"2025-08-14 10:33:18","orgRuleId":""}]},"tranCode":"ST005","request_id":"1755167598918ST005","version":"1","serialNo":"01354090202503050399"}
2025-08-14 18:33:20.401 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 准备发送响应确认 - requestId: 1755167598918ST005, needResponse: true
2025-08-14 18:33:20.427 19136-19207 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0000","serialNo":"01354090202503050399","request_time":"1755167600411","request_id":"1755167600411C0000","version":"1","org_request_id":"1755167598918ST005","org_request_time":"1755167598918","response_state":"0","myVersionName":"1.0.08.20250814.DSPREAD.MDM.SERVICE","terminalDate":"20250814183320"}
2025-08-14 18:33:20.449 19136-19207 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0000","serialNo":"01354090202503050399","request_time":"1755167600435","request_id":"1755167600435C0000","version":"1","org_request_id":"1755167598918ST005","org_request_time":"1755167598918","response_state":"0","myVersionName":"1.0.08.20250814.DSPREAD.MDM.SERVICE","terminalDate":"20250814183320"}
2025-08-14 18:33:20.454 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 已发送C0000响应确认: requestId=1755167598918ST005
2025-08-14 18:33:20.462 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleHandler 处理规则消息: ST005
2025-08-14 18:33:20.468 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleHandler 处理规则列表，数量: 1
2025-08-14 18:33:20.473 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleHandler 处理规则: ruleId=01f42138486940dd8ba8a136d84e239d, action=A
2025-08-14 18:33:20.478 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析规则JSON: ruleId=01f42138486940dd8ba8a136d84e239d
2025-08-14 18:33:20.483 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule appList数组: []
2025-08-14 18:33:20.490 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule deleteAppList数组: [{"apkName":"Via","appId":"353795033bc0410dac6dee5cb8702b06","packName":"mark.via","versionName":"6.6.0","versionCode":"20250713"}]
2025-08-14 18:33:20.496 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=mark.via, apkName=Via
2025-08-14 18:33:20.501 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后appList数量: 0
2025-08-14 18:33:20.506 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后deleteAppList数量: 1
2025-08-14 18:33:20.511 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleBaseStorage 处理规则: 01f42138486940dd8ba8a136d84e239d, 操作: A
2025-08-14 18:33:20.517 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 验证规则: ruleId=01f42138486940dd8ba8a136d84e239d, ruleType=app_management, action=A
2025-08-14 18:33:20.522 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule appList数量: 0, deleteAppList数量: 1
2025-08-14 18:33:20.543 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 验证应用管理规则: action=A
2025-08-14 18:33:20.549 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 检测到纯卸载规则，自动调整action: A -> D
2025-08-14 18:33:20.554 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 删除操作验证通过: 允许空应用列表
2025-08-14 18:33:20.563 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析规则JSON: ruleId=937eccc7e4ff4fc0bc9eaf2788685515
2025-08-14 18:33:20.572 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule appList数组: [{"packName":"mark.via","apkName":"Via","versionName":"6.6.0","versionCode":"20250713","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/4d256c4123b346fd817537b491342204.apk","apkMd5":"0d1225260d03e10a8ffc8409369c442a","apkSize":2565110,"installBy":"0","isSystemApp":false,"priority":0}]
2025-08-14 18:33:20.578 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule deleteAppList数组: []
2025-08-14 18:33:20.583 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=mark.via, apkName=Via
2025-08-14 18:33:20.589 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后appList数量: 1
2025-08-14 18:33:20.594 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后deleteAppList数量: 0
2025-08-14 18:33:20.603 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析规则JSON: ruleId=937eccc7e4ff4fc0bc9eaf2788685515
2025-08-14 18:33:20.612 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule appList数组: [{"packName":"mark.via","apkName":"Via","versionName":"6.6.0","versionCode":"20250713","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/4d256c4123b346fd817537b491342204.apk","apkMd5":"0d1225260d03e10a8ffc8409369c442a","apkSize":2565110,"installBy":"0","isSystemApp":false,"priority":0}]
2025-08-14 18:33:20.617 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule deleteAppList数组: []
2025-08-14 18:33:20.623 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=mark.via, apkName=Via
2025-08-14 18:33:20.628 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后appList数量: 1
2025-08-14 18:33:20.633 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后deleteAppList数量: 0
2025-08-14 18:33:20.654 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleBaseStorage 保存 2 个规则到存储
2025-08-14 18:33:20.660 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleBaseStorage Rule 01f42138486940dd8ba8a136d84e239d 添加成功
2025-08-14 18:33:20.665 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleBaseStorage 触发规则执行: 01f42138486940dd8ba8a136d84e239d
2025-08-14 18:33:20.672 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleHandler 规则处理成功: 01f42138486940dd8ba8a136d84e239d
2025-08-14 18:33:20.672 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 RuleBaseStorage 设备空闲，立即执行规则: 01f42138486940dd8ba8a136d84e239d
2025-08-14 18:33:20.677 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 RuleBaseManager 直接执行规则: 01f42138486940dd8ba8a136d84e239d
2025-08-14 18:33:20.678 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleHandler 规则列表处理完成: 处理完成 - 成功: 1, 失败: 0, 忽略: 0
2025-08-14 18:33:20.683 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleHandler 上报规则处理结果: 0 - 处理完成 - 成功: 1, 失败: 0, 忽略: 0
2025-08-14 18:33:20.683 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 RuleBaseManager 处理规则: 01f42138486940dd8ba8a136d84e239d, 操作: A
2025-08-14 18:33:20.689 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 C0107 主动式上送: rulebase_update (主动: 9)
2025-08-14 18:33:20.689 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析规则JSON: ruleId=01f42138486940dd8ba8a136d84e239d
2025-08-14 18:33:20.694 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 Rule appList数组: []
2025-08-14 18:33:20.702 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 Rule deleteAppList数组: [{"packName":"mark.via","apkName":"Via","versionName":"6.6.0","versionCode":"20250713","apkUrl":"","apkMd5":"","apkSize":0,"installBy":"0","isSystemApp":false,"priority":0}]
2025-08-14 18:33:20.708 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=mark.via, apkName=Via
2025-08-14 18:33:20.709 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 添加规则结果: ruleId=batch_rules
2025-08-14 18:33:20.713 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后appList数量: 0
2025-08-14 18:33:20.718 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后deleteAppList数量: 1
2025-08-14 18:33:20.724 19136-19207 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0107","serialNo":"01354090202503050399","request_time":"1755167600703","request_id":"1755167600703C0107","version":"1","data":{"ruleId":"batch_rules","taskResult":"0"},"myVersionName":"1.0.08.20250814.DSPREAD.MDM.SERVICE","terminalDate":"20250814183320","org_request_id":"1755167598918ST005","org_request_time":"1755167598918"}
2025-08-14 18:33:20.724 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 RuleBaseStorage 处理规则: 01f42138486940dd8ba8a136d84e239d, 操作: A
2025-08-14 18:33:20.729 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 规则结果实时上送: ruleId=batch_rules, result=0 (1)
2025-08-14 18:33:20.730 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 Rule 验证规则: ruleId=01f42138486940dd8ba8a136d84e239d, ruleType=app_management, action=A
2025-08-14 18:33:20.735 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 Rule appList数量: 0, deleteAppList数量: 1
2025-08-14 18:33:20.756 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 Rule 验证应用管理规则: action=A
2025-08-14 18:33:20.762 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 Rule 检测到纯卸载规则，自动调整action: A -> D
2025-08-14 18:33:20.767 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 Rule 删除操作验证通过: 允许空应用列表
2025-08-14 18:33:20.779 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析规则JSON: ruleId=937eccc7e4ff4fc0bc9eaf2788685515
2025-08-14 18:33:20.788 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 Rule appList数组: [{"packName":"mark.via","apkName":"Via","versionName":"6.6.0","versionCode":"20250713","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/4d256c4123b346fd817537b491342204.apk","apkMd5":"0d1225260d03e10a8ffc8409369c442a","apkSize":2565110,"installBy":"0","isSystemApp":false,"priority":0}]
2025-08-14 18:33:20.794 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 Rule deleteAppList数组: []
2025-08-14 18:33:20.799 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=mark.via, apkName=Via
2025-08-14 18:33:20.804 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后appList数量: 1
2025-08-14 18:33:20.810 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后deleteAppList数量: 0
2025-08-14 18:33:20.815 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析规则JSON: ruleId=01f42138486940dd8ba8a136d84e239d
2025-08-14 18:33:20.821 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 Rule appList数组: []
2025-08-14 18:33:20.829 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 Rule deleteAppList数组: [{"packName":"mark.via","apkName":"Via","versionName":"6.6.0","versionCode":"20250713","apkUrl":"","apkMd5":"","apkSize":0,"installBy":"0","isSystemApp":false,"priority":0}]
2025-08-14 18:33:20.834 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=mark.via, apkName=Via
2025-08-14 18:33:20.838 19136-19207 Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"request_time":"1755167599849","org_request_time":"1755167600703","org_request_id":"1755167600703C0107","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755167599849S0000","serialNo":"01354090202503050399"}
2025-08-14 18:33:20.840 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后appList数量: 0
2025-08-14 18:33:20.845 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后deleteAppList数量: 1
2025-08-14 18:33:20.846 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 收到响应消息: transCode=S0000, orgRequestId=1755167600703C0107, state=0, remark=
2025-08-14 18:33:20.851 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 RuleBaseStorage Rule 01f42138486940dd8ba8a136d84e239d 已存在，忽略Add操作
2025-08-14 18:33:20.851 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 处理规则结果响应: state=0
2025-08-14 18:33:20.857 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 规则结果上传成功
2025-08-14 18:33:20.857 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 RuleStateMachine 规则初始化成功: 01f42138486940dd8ba8a136d84e239d -> RuleState(code=todo, description=等待执行)
2025-08-14 18:33:20.863 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 RuleStateMachine 规则已存在: 01f42138486940dd8ba8a136d84e239d, 当前状态: RuleState(code=todo, description=等待执行)
2025-08-14 18:33:20.869 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 初始化规则应用状态: 01f42138486940dd8ba8a136d84e239d, 应用数量: 1
2025-08-14 18:33:20.874 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 RuleStateMonitor 收到状态变化: 01f42138486940dd8ba8a136d84e239d, todo -> R01
2025-08-14 18:33:20.880 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 RuleStateMonitor 状态变化已记录: 01f42138486940dd8ba8a136d84e239d, R01
2025-08-14 18:33:20.885 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 RuleStateMachine 状态转换成功: 01f42138486940dd8ba8a136d84e239d, todo -> R01
2025-08-14 18:33:20.891 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 RuleStateMonitor 收到状态变化: 01f42138486940dd8ba8a136d84e239d, R01 -> R02
2025-08-14 18:33:20.896 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 RuleStateMonitor 状态变化已记录: 01f42138486940dd8ba8a136d84e239d, R02
2025-08-14 18:33:20.902 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 RuleStateMachine 状态转换成功: 01f42138486940dd8ba8a136d84e239d, R01 -> R02
2025-08-14 18:33:20.907 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 开始执行规则: 01f42138486940dd8ba8a136d84e239d
2025-08-14 18:33:20.913 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 执行卸载应用，数量: 1
2025-08-14 18:33:20.918 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 开始卸载应用，数量: 1
2025-08-14 18:33:20.924 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 卸载应用: mark.via
2025-08-14 18:33:20.931 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 更新应用状态: 01f42138486940dd8ba8a136d84e239d, mark.via -> D01
2025-08-14 18:33:20.936 19136-19165 WebSocket               com.dspread.mdm.service              I  🔧 C0107 主动式上送: rulebase_update (主动: 10)
2025-08-14 18:33:20.955 19136-19165 WebSocket               com.dspread.mdm.service              I  🔧 添加规则结果: ruleId=01f42138486940dd8ba8a136d84e239d
2025-08-14 18:33:20.975 19136-19165 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0107","serialNo":"01354090202503050399","request_time":"1755167600950","request_id":"1755167600950C0107","version":"1","data":{"ruleId":"01f42138486940dd8ba8a136d84e239d","taskResult":"R02","failedApkList":[{"packName":"mark.via","apkName":"Via","versionName":"6.6.0","versionCode":"20250713","apkUrl":"","apkMd5":"","apkSize":0,"installBy":"0","isSystemApp":false,"priority":0,"result":"D01","taskType":"02"}]},"myVersionName":"1.0.08.20250814.DSPREAD.MDM.SERVICE","terminalDate":"20250814183320"}
2025-08-14 18:33:20.981 19136-19165 WebSocket               com.dspread.mdm.service              I  🔧 规则结果实时上送: ruleId=01f42138486940dd8ba8a136d84e239d, result=R02 (1)
2025-08-14 18:33:20.987 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 上报规则执行中状态: 01f42138486940dd8ba8a136d84e239d, 应用数量: 1
2025-08-14 18:33:20.992 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 注册卸载回调: mark.via (规则ID: 01f42138486940dd8ba8a136d84e239d)
2025-08-14 18:33:20.998 19136-19165 AppManager              com.dspread.mdm.service              I  ℹ️ Uninstalling app via PackageInstaller: mark.via
2025-08-14 18:33:21.004 19136-19165 AppManager              com.dspread.mdm.service              I  ℹ️ Deleting package via PackageInstaller: mark.via
2025-08-14 18:33:21.012 19136-19165 AppManager              com.dspread.mdm.service              I  ℹ️ PackageInstaller uninstall initiated
2025-08-14 18:33:21.018 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 规则执行启动成功: 01f42138486940dd8ba8a136d84e239d
2025-08-14 18:33:21.024 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 RuleBaseManager 规则添加成功: 01f42138486940dd8ba8a136d84e239d
2025-08-14 18:33:21.304 19136-19136 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 收到广播: android.intent.action.PACKAGE_REMOVED
2025-08-14 18:33:21.306 19136-19207 Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"request_time":"1755167600099","org_request_time":"1755167600950","org_request_id":"1755167600950C0107","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755167600099S0000","serialNo":"01354090202503050399"}
2025-08-14 18:33:21.318 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 收到响应消息: transCode=S0000, orgRequestId=1755167600950C0107, state=0, remark=
2025-08-14 18:33:21.327 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 处理规则结果响应: state=0
2025-08-14 18:33:21.336 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 规则结果上传成功
2025-08-14 18:33:21.347 19136-19136 Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 收到包更新广播: android.intent.action.PACKAGE_REMOVED, 包名: mark.via
2025-08-14 18:33:21.354 19136-19136 Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 应用卸载: mark.via
2025-08-14 18:33:21.363 19136-19136 Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 检查WebSocket状态: packageName=mark.via, action=UNINSTALL
2025-08-14 18:33:21.369 19136-19136 Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 通知rulebase处理应用卸载: mark.via
2025-08-14 18:33:21.375 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 收到系统广播，应用已卸载: mark.via
2025-08-14 18:33:21.382 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 找到匹配的卸载回调，触发: mark.via
2025-08-14 18:33:21.389 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 卸载回调被触发: pkg=mark.via, returnCode=1, error=
2025-08-14 18:33:21.397 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 应用卸载启动成功: mark.via
2025-08-14 18:33:21.403 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 从广播更新应用状态: 01f42138486940dd8ba8a136d84e239d, mark.via -> D02
2025-08-14 18:33:21.412 19136-19136 WebSocket               com.dspread.mdm.service              I  🔧 C0107 主动式上送: rulebase_update (主动: 11)
2025-08-14 18:33:21.439 19136-19136 WebSocket               com.dspread.mdm.service              I  🔧 添加规则结果: ruleId=01f42138486940dd8ba8a136d84e239d
2025-08-14 18:33:21.462 19136-19136 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0107","serialNo":"01354090202503050399","request_time":"1755167601431","request_id":"1755167601431C0107","version":"1","data":{"ruleId":"01f42138486940dd8ba8a136d84e239d","taskResult":"R02","failedApkList":[{"packName":"mark.via","apkName":"Via","versionName":"6.6.0","versionCode":"20250713","apkUrl":"","apkMd5":"","apkSize":0,"installBy":"0","isSystemApp":false,"priority":0,"result":"D02","taskType":"02"}]},"myVersionName":"1.0.08.20250814.DSPREAD.MDM.SERVICE","terminalDate":"20250814183321"}
2025-08-14 18:33:21.471 19136-19136 WebSocket               com.dspread.mdm.service              I  🔧 规则结果实时上送: ruleId=01f42138486940dd8ba8a136d84e239d, result=R02 (1)
2025-08-14 18:33:21.477 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 上报规则执行中状态: 01f42138486940dd8ba8a136d84e239d, 应用数量: 1
2025-08-14 18:33:21.484 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 开始检查规则完成状态: 01f42138486940dd8ba8a136d84e239d, 总应用数: 1
2025-08-14 18:33:21.490 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 检查应用状态: mark.via -> D02
2025-08-14 18:33:21.496 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 发现成功状态: mark.via -> D02
2025-08-14 18:33:21.503 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 规则完成检查结果: 01f42138486940dd8ba8a136d84e239d, 全部完成: true, 完成数: 1/1, 有失败: false
2025-08-14 18:33:21.509 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 规则执行全部成功，上送RULEBASED_SUCCESS: 01f42138486940dd8ba8a136d84e239d
2025-08-14 18:33:21.515 19136-19136 WebSocket               com.dspread.mdm.service              I  🔧 C0107 主动式上送: rulebase_update (主动: 12)
2025-08-14 18:33:21.536 19136-19136 WebSocket               com.dspread.mdm.service              I  🔧 添加规则结果: ruleId=01f42138486940dd8ba8a136d84e239d
2025-08-14 18:33:21.558 19136-19136 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0107","serialNo":"01354090202503050399","request_time":"1755167601530","request_id":"1755167601530C0107","version":"1","data":{"ruleId":"01f42138486940dd8ba8a136d84e239d","taskResult":"R03","failedApkList":[{"packName":"mark.via","apkName":"Via","versionName":"6.6.0","versionCode":"20250713","apkUrl":"","apkMd5":"","apkSize":0,"installBy":"0","isSystemApp":false,"priority":0,"result":"D02","taskType":"02"}]},"myVersionName":"1.0.08.20250814.DSPREAD.MDM.SERVICE","terminalDate":"20250814183321"}
2025-08-14 18:33:21.564 19136-19136 WebSocket               com.dspread.mdm.service              I  🔧 规则结果实时上送: ruleId=01f42138486940dd8ba8a136d84e239d, result=R03 (1)
2025-08-14 18:33:21.570 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 上报规则状态: 01f42138486940dd8ba8a136d84e239d -> R03
2025-08-14 18:33:21.573 19136-19207 Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"request_time":"1755167600586","org_request_time":"1755167601431","org_request_id":"1755167601431C0107","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755167600586S0000","serialNo":"01354090202503050399"}
2025-08-14 18:33:21.576 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleStateMonitor 收到状态变化: 01f42138486940dd8ba8a136d84e239d, R02 -> R03
2025-08-14 18:33:21.582 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleStateMonitor 状态变化已记录: 01f42138486940dd8ba8a136d84e239d, R03
2025-08-14 18:33:21.583 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 收到响应消息: transCode=S0000, orgRequestId=1755167601431C0107, state=0, remark=
2025-08-14 18:33:21.589 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 处理规则结果响应: state=0
2025-08-14 18:33:21.590 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleStateMachine 状态转换成功: 01f42138486940dd8ba8a136d84e239d, R02 -> R03
2025-08-14 18:33:21.595 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 规则结果上传成功
2025-08-14 18:33:21.596 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 规则执行完成: 01f42138486940dd8ba8a136d84e239d, 有失败: false
2025-08-14 18:33:21.602 19136-19136 Platform                com.dspread.mdm.service              D  🔧 重新收集应用信息（强制刷新）
2025-08-14 18:33:21.710 19136-19207 Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"request_time":"1755167600688","org_request_time":"1755167601530","org_request_id":"1755167601530C0107","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755167600688S0000","serialNo":"01354090202503050399"}
2025-08-14 18:33:21.714 19136-19136 AppManager              com.dspread.mdm.service              I  ℹ️ 应用信息获取完成: 总数132(系统129/用户3) 返回3个
2025-08-14 18:33:21.724 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 收到响应消息: transCode=S0000, orgRequestId=1755167601530C0107, state=0, remark=
2025-08-14 18:33:21.732 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 处理规则结果响应: state=0
2025-08-14 18:33:21.738 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 规则结果上传成功
2025-08-14 18:33:21.747 19136-19136 Platform                com.dspread.mdm.service              D  🔧 应用信息: 3 个应用
2025-08-14 18:33:21.753 19136-19136 Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 应用信息缓存刷新完成
2025-08-14 18:33:21.761 19136-19136 ContextImpl             com.dspread.mdm.service              W  Calling a method in the system process without a qualified user: android.app.ContextImpl.sendBroadcast:1220 android.content.ContextWrapper.sendBroadcast:504 com.dspread.mdm.service.broadcast.core.BroadcastSender.sendBroadcast:99 com.dspread.mdm.service.broadcast.core.BroadcastSender.sendBroadcast:23 com.dspread.mdm.service.broadcast.handlers.system.PackageUpdateEventHandlerImpl.sendAppRefreshBroadcast:491 
2025-08-14 18:33:21.764 19136-19136 Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 发送应用刷新广播
2025-08-14 18:33:21.774 19136-19136 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 收到广播: android.intent.action.PACKAGE_REMOVED
2025-08-14 18:33:21.782 19136-19136 Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 收到包更新广播: android.intent.action.PACKAGE_REMOVED, 包名: mark.via
2025-08-14 18:33:21.788 19136-19136 Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 应用卸载: mark.via
2025-08-14 18:33:21.795 19136-19136 Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 检查WebSocket状态: packageName=mark.via, action=UNINSTALL
2025-08-14 18:33:21.801 19136-19136 Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 通知rulebase处理应用卸载: mark.via
2025-08-14 18:33:21.808 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 收到系统广播，应用已卸载: mark.via
2025-08-14 18:33:21.815 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 未找到匹配的卸载回调: mark.via
2025-08-14 18:33:21.822 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 未找到匹配的规则应用: mark.via
2025-08-14 18:33:21.828 19136-19136 Platform                com.dspread.mdm.service              D  🔧 重新收集应用信息（强制刷新）
2025-08-14 18:33:21.935 19136-19136 AppManager              com.dspread.mdm.service              I  ℹ️ 应用信息获取完成: 总数132(系统129/用户3) 返回3个
2025-08-14 18:33:21.962 19136-19136 Platform                com.dspread.mdm.service              D  🔧 应用信息: 3 个应用
2025-08-14 18:33:21.969 19136-19136 Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 应用信息缓存刷新完成
2025-08-14 18:33:21.976 19136-19136 ContextImpl             com.dspread.mdm.service              W  Calling a method in the system process without a qualified user: android.app.ContextImpl.sendBroadcast:1220 android.content.ContextWrapper.sendBroadcast:504 com.dspread.mdm.service.broadcast.core.BroadcastSender.sendBroadcast:99 com.dspread.mdm.service.broadcast.core.BroadcastSender.sendBroadcast:23 com.dspread.mdm.service.broadcast.handlers.system.PackageUpdateEventHandlerImpl.sendAppRefreshBroadcast:491 
2025-08-14 18:33:21.978 19136-19136 Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 发送应用刷新广播
2025-08-14 18:33:24.778 19136-19165 Common                  com.dspread.mdm.service              D  🔧 ServiceGuardEventHandler 开始执行保活动作
2025-08-14 18:33:24.790 19136-19165 Common                  com.dspread.mdm.service              I  ✅ ⏰ 设置服务守护定时器成功，下次执行: 120秒后 (2分钟)
2025-08-14 18:33:24.796 19136-19165 ContextImpl             com.dspread.mdm.service              W  Calling a method in the system process without a qualified user: android.app.ContextImpl.startService:1899 android.content.ContextWrapper.startService:825 com.dspread.mdm.service.broadcast.handlers.service.ServiceGuardEventHandler.startKeepAliveService:211 com.dspread.mdm.service.broadcast.handlers.service.ServiceGuardEventHandler.performKeepAliveActions:193 com.dspread.mdm.service.broadcast.handlers.service.ServiceGuardEventHandler.access$performKeepAliveActions:24 
2025-08-14 18:33:24.801 19136-19165 Common                  com.dspread.mdm.service              D  🔧 ServiceGuardEventHandler 启动保活服务
2025-08-14 18:33:24.801 19136-19136 Service                 com.dspread.mdm.service              D  🔧 ServiceKeepAliveService 保活服务启动
2025-08-14 18:33:24.808 19136-19136 Platform                com.dspread.mdm.service              D  🔧 WakeLockManager WakeLock不存在或未持有: ServiceKeepAlive
2025-08-14 18:33:24.813 19136-19165 Common                  com.dspread.mdm.service              D  🔧 ServiceGuardEventHandler 保活动作执行完成
2025-08-14 18:33:24.819 19136-19136 Platform                com.dspread.mdm.service              D  🔧 WakeLockManager 获取WakeLock成功: ServiceKeepAlive (超时: 30000ms)
2025-08-14 18:33:27.819 19136-19165 Common                  com.dspread.mdm.service              D  🔧 ServiceGuardEventHandler WakeLock已释放
