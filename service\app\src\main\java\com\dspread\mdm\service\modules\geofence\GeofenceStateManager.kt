package com.dspread.mdm.service.modules.geofence

import android.content.Context
import android.content.SharedPreferences
import android.preference.PreferenceManager
import com.dspread.mdm.service.constants.Constants
import com.dspread.mdm.service.modules.geofence.location.GpsLocationManager
import com.dspread.mdm.service.utils.log.Logger

/**
 * 地理围栏状态管理器
 * 负责地理围栏相关状态的持久化存储和恢复
 */
object GeofenceStateManager {
    
    private const val TAG = "GeofenceStateManager"
    
    // SharedPreferences键名
    private const val PREF_GEOFENCE_STATUS = "geofence_status"
    private const val PREF_GEOFENCE_ENABLED = "geofence_enabled"
    private const val PREF_GEOFENCE_LATITUDE = "geofence_latitude"
    private const val PREF_GEOFENCE_LONGITUDE = "geofence_longitude"
    private const val PREF_GEOFENCE_RADIUS = "geofence_radius"
    private const val PREF_GEOFENCE_LOCK_MINS = "geofence_lock_mins"
    private const val PREF_GEOFENCE_WIPE_ENABLED = "geofence_wipe_enabled"
    private const val PREF_GEOFENCE_WIPE_MINS = "geofence_wipe_mins"
    private const val PREF_GEOFENCE_NEAR_BEACON = "geofence_near_beacon"
    private const val PREF_GEOFENCE_OUT_OF_FENCE = "geofence_out_of_fence"
    private const val PREF_GEOFENCE_SCAN_MODE = "geofence_scan_mode"
    private const val PREF_GEOFENCE_WARN_COUNTDOWN = "geofence_warn_countdown_secs"
    private const val PREF_GEOFENCE_LOCK_COUNTDOWN = "geofence_lock_countdown_mins"
    private const val PREF_GEOFENCE_CURRENT_PROFILE = "geofence_current_profile"
    private const val PREF_GEOFENCE_LAST_DISTANCE = "geofence_last_distance"
    
    // 商店信息
    private const val PREF_STORE_ID = "store_id"
    private const val PREF_STORE_SSID = "store_ssid"
    private const val PREF_STORE_IP = "store_ip"
    
    /**
     * 保存地理围栏状态
     */
    fun saveGeofenceStatus(context: Context, status: Int) {
        try {
            val prefs = getSharedPreferences(context)
            prefs.edit().putInt(PREF_GEOFENCE_STATUS, status).apply()
            Logger.geo("$TAG 保存地理围栏状态: $status")
        } catch (e: Exception) {
            Logger.geoE("$TAG 保存地理围栏状态失败", e)
        }
    }
    
    /**
     * 获取地理围栏状态
     */
    fun getGeofenceStatus(context: Context): Int {
        return try {
            val prefs = getSharedPreferences(context)
            prefs.getInt(PREF_GEOFENCE_STATUS, GpsLocationManager.IN_ZONE)
        } catch (e: Exception) {
            Logger.geoE("$TAG 获取地理围栏状态失败", e)
            GpsLocationManager.IN_ZONE
        }
    }
    
    /**
     * 保存地理围栏配置（同时保存到两个位置确保兼容性）
     */
    fun saveGeofenceConfig(context: Context) {
        try {
            val prefs = getSharedPreferences(context)
            val editor = prefs.edit()

            editor.putBoolean(PREF_GEOFENCE_ENABLED, GpsLocationManager.geoStatus)
            editor.putFloat(PREF_GEOFENCE_LATITUDE, Constants.geoLatitude.toFloat())
            editor.putFloat(PREF_GEOFENCE_LONGITUDE, Constants.geoLongitude.toFloat())
            editor.putInt(PREF_GEOFENCE_RADIUS, GpsLocationManager.geoLockMeter)
            editor.putInt(PREF_GEOFENCE_LOCK_MINS, GpsLocationManager.geoLockMins)
            editor.putBoolean(PREF_GEOFENCE_WIPE_ENABLED, GpsLocationManager.geoWipeStatus)
            editor.putInt(PREF_GEOFENCE_WIPE_MINS, GpsLocationManager.geoWipeMins)
            editor.putBoolean(PREF_GEOFENCE_NEAR_BEACON, GpsLocationManager.geoIsNearBeacon)
            editor.putBoolean(PREF_GEOFENCE_OUT_OF_FENCE, GpsLocationManager.bGeoOutOfFence)
            editor.putInt(PREF_GEOFENCE_SCAN_MODE, GpsLocationManager.scanMode)
            editor.putFloat(PREF_GEOFENCE_LAST_DISTANCE, GpsLocationManager.lastDistance.toFloat())

            // 保存商店信息
            editor.putString(PREF_STORE_ID, Constants.storeId)
            editor.putString(PREF_STORE_SSID, Constants.storeSsid)
            editor.putString(PREF_STORE_IP, Constants.storeIp)

            editor.apply()

            // 同时保存坐标到gps_config确保兼容性
            val gpsConfigPrefs = context.getSharedPreferences("gps_config", Context.MODE_PRIVATE)
            gpsConfigPrefs.edit().apply {
                putString("store_latitude", Constants.geoLatitude.toString())
                putString("store_longitude", Constants.geoLongitude.toString())
                apply()
            }

            Logger.geo("$TAG 保存地理围栏配置完成: 坐标=(${Constants.geoLatitude}, ${Constants.geoLongitude})")

        } catch (e: Exception) {
            Logger.geoE("$TAG 保存地理围栏配置失败", e)
        }
    }

    /**
     * 立即保存地理围栏启用状态
     */
    fun saveGeofenceEnabled(context: Context, enabled: Boolean) {
        try {
            val prefs = getSharedPreferences(context)
            val editor = prefs.edit()
            editor.putBoolean(PREF_GEOFENCE_ENABLED, enabled)
            editor.apply()

            Logger.geo("$TAG 地理围栏启用状态已保存: $enabled")

        } catch (e: Exception) {
            Logger.geoE("$TAG 保存地理围栏启用状态失败", e)
        }
    }
    
    /**
     * 恢复地理围栏配置（统一坐标读取来源）
     */
    fun restoreGeofenceConfig(context: Context) {
        try {
            val prefs = getSharedPreferences(context)

            GpsLocationManager.geoStatus = prefs.getBoolean(PREF_GEOFENCE_ENABLED, true) // 默认启用地理围栏

            // 优先从gps_config读取店铺坐标，如果没有则从geofence_state读取
            val gpsConfigPrefs = context.getSharedPreferences("gps_config", Context.MODE_PRIVATE)
            val storeLatStr = gpsConfigPrefs.getString("store_latitude", "")
            val storeLngStr = gpsConfigPrefs.getString("store_longitude", "")

            if (!storeLatStr.isNullOrEmpty() && !storeLngStr.isNullOrEmpty()) {
                // 从gps_config读取店铺坐标（优先）
                try {
                    Constants.geoLatitude = storeLatStr.toDouble()
                    Constants.geoLongitude = storeLngStr.toDouble()
                    Logger.geo("$TAG 从gps_config恢复店铺坐标: ($storeLatStr, $storeLngStr)")
                } catch (e: NumberFormatException) {
                    Logger.geoE("$TAG 店铺坐标格式错误，使用默认值", e)
                    Constants.geoLatitude = 0.0
                    Constants.geoLongitude = 0.0
                }
            } else {
                // 从geofence_state读取（备用）
                Constants.geoLatitude = prefs.getFloat(PREF_GEOFENCE_LATITUDE, 0f).toDouble()
                Constants.geoLongitude = prefs.getFloat(PREF_GEOFENCE_LONGITUDE, 0f).toDouble()
                Logger.geo("$TAG 从geofence_state恢复坐标: (${Constants.geoLatitude}, ${Constants.geoLongitude})")
            }

            // 从geofence_config读取参数，与保存位置一致
            val configPrefs = context.getSharedPreferences("geofence_config", Context.MODE_PRIVATE)

            // 读取lockMeter参数
            val lockMeterStr = configPrefs.getString("lockMeter", "")
            if (!lockMeterStr.isNullOrEmpty()) {
                try {
                    GpsLocationManager.geoLockMeter = lockMeterStr.toInt()
                    Logger.geo("$TAG 从geofence_config恢复lockMeter: $lockMeterStr")
                } catch (e: NumberFormatException) {
                    GpsLocationManager.geoLockMeter = prefs.getInt(PREF_GEOFENCE_RADIUS, 200)
                    Logger.geo("$TAG lockMeter格式错误，使用备用值: ${GpsLocationManager.geoLockMeter}")
                }
            } else {
                GpsLocationManager.geoLockMeter = prefs.getInt(PREF_GEOFENCE_RADIUS, 200)
            }

            // 读取lockMin参数
            val lockMinStr = configPrefs.getString("lockMin", "")
            if (!lockMinStr.isNullOrEmpty()) {
                try {
                    GpsLocationManager.geoLockMins = lockMinStr.toInt()
                    Logger.geo("$TAG 从geofence_config恢复lockMin: $lockMinStr")
                } catch (e: NumberFormatException) {
                    GpsLocationManager.geoLockMins = prefs.getInt(PREF_GEOFENCE_LOCK_MINS, 5)
                    Logger.geo("$TAG lockMin格式错误，使用备用值: ${GpsLocationManager.geoLockMins}")
                }
            } else {
                GpsLocationManager.geoLockMins = prefs.getInt(PREF_GEOFENCE_LOCK_MINS, 5)
            }

            // 读取wipeStatus参数
            val wipeStatusStr = configPrefs.getString("wipeStatus", "")
            if (!wipeStatusStr.isNullOrEmpty()) {
                GpsLocationManager.geoWipeStatus = "1" == wipeStatusStr
                Logger.geo("$TAG 从geofence_config恢复wipeStatus: $wipeStatusStr")
            } else {
                GpsLocationManager.geoWipeStatus = prefs.getBoolean(PREF_GEOFENCE_WIPE_ENABLED, false)
            }

            // 读取wipeMin参数
            val wipeMinStr = configPrefs.getString("wipeMin", "")
            if (!wipeMinStr.isNullOrEmpty()) {
                try {
                    GpsLocationManager.geoWipeMins = wipeMinStr.toInt()
                    Logger.geo("$TAG 从geofence_config恢复wipeMin: $wipeMinStr")
                } catch (e: NumberFormatException) {
                    GpsLocationManager.geoWipeMins = prefs.getInt(PREF_GEOFENCE_WIPE_MINS, 5)
                    Logger.geo("$TAG wipeMin格式错误，使用备用值: ${GpsLocationManager.geoWipeMins}")
                }
            } else {
                GpsLocationManager.geoWipeMins = prefs.getInt(PREF_GEOFENCE_WIPE_MINS, 5)
            }
            GpsLocationManager.geoIsNearBeacon = prefs.getBoolean(PREF_GEOFENCE_NEAR_BEACON, false)
            GpsLocationManager.bGeoOutOfFence = prefs.getBoolean(PREF_GEOFENCE_OUT_OF_FENCE, true)
            GpsLocationManager.scanMode = prefs.getInt(PREF_GEOFENCE_SCAN_MODE, 1)
            GpsLocationManager.lastDistance = prefs.getFloat(PREF_GEOFENCE_LAST_DISTANCE, 0f).toDouble()

            // 恢复商店信息
            Constants.storeId = prefs.getString(PREF_STORE_ID, "") ?: ""
            Constants.storeSsid = prefs.getString(PREF_STORE_SSID, "") ?: ""
            Constants.storeIp = prefs.getString(PREF_STORE_IP, "") ?: ""

            // 恢复地理围栏状态
            Constants.geofenceStatus = getGeofenceStatus(context)

            Logger.geo("$TAG 恢复地理围栏配置完成: 启用=${GpsLocationManager.geoStatus}, 状态=${Constants.geofenceStatus}, 坐标=(${Constants.geoLatitude}, ${Constants.geoLongitude})")

        } catch (e: Exception) {
            Logger.geoE("$TAG 恢复地理围栏配置失败", e)
        }
    }
    
    /**
     * 保存警告倒计时
     */
    fun saveWarningCountdown(context: Context, countdown: Int) {
        try {
            val prefs = getSharedPreferences(context)
            prefs.edit().putInt(PREF_GEOFENCE_WARN_COUNTDOWN, countdown).apply()
            Logger.geo("$TAG 保存警告倒计时: ${countdown}秒")
        } catch (e: Exception) {
            Logger.geoE("$TAG 保存警告倒计时失败", e)
        }
    }
    
    /**
     * 获取警告倒计时
     */
    fun getWarningCountdown(context: Context, defaultValue: Int = 0): Int {
        return try {
            val prefs = getSharedPreferences(context)
            prefs.getInt(PREF_GEOFENCE_WARN_COUNTDOWN, defaultValue)
        } catch (e: Exception) {
            Logger.geoE("$TAG 获取警告倒计时失败", e)
            defaultValue
        }
    }
    
    /**
     * 保存锁屏倒计时
     */
    fun saveLockCountdown(context: Context, countdown: Int) {
        try {
            val prefs = getSharedPreferences(context)
            prefs.edit().putInt(PREF_GEOFENCE_LOCK_COUNTDOWN, countdown).apply()
            Logger.geo("$TAG 保存锁屏倒计时: ${countdown}分钟")
        } catch (e: Exception) {
            Logger.geoE("$TAG 保存锁屏倒计时失败", e)
        }
    }
    
    /**
     * 获取锁屏倒计时
     */
    fun getLockCountdown(context: Context, defaultValue: Int = 0): Int {
        return try {
            val prefs = getSharedPreferences(context)
            prefs.getInt(PREF_GEOFENCE_LOCK_COUNTDOWN, defaultValue)
        } catch (e: Exception) {
            Logger.geoE("$TAG 获取锁屏倒计时失败", e)
            defaultValue
        }
    }
    
    /**
     * 保存当前Profile
     */
    fun saveCurrentProfile(context: Context, profile: String) {
        try {
            val prefs = getSharedPreferences(context)
            prefs.edit().putString(PREF_GEOFENCE_CURRENT_PROFILE, profile).apply()
            Logger.geo("$TAG 保存当前Profile")
        } catch (e: Exception) {
            Logger.geoE("$TAG 保存当前Profile失败", e)
        }
    }
    
    /**
     * 获取当前Profile
     */
    fun getCurrentProfile(context: Context): String {
        return try {
            val prefs = getSharedPreferences(context)
            prefs.getString(PREF_GEOFENCE_CURRENT_PROFILE, "") ?: ""
        } catch (e: Exception) {
            Logger.geoE("$TAG 获取当前Profile失败", e)
            ""
        }
    }
    
    /**
     * 清除所有地理围栏状态
     */
    fun clearAllGeofenceState(context: Context) {
        try {
            val prefs = getSharedPreferences(context)
            val editor = prefs.edit()
            
            // 清除所有地理围栏相关的键
            editor.remove(PREF_GEOFENCE_STATUS)
            editor.remove(PREF_GEOFENCE_ENABLED)
            editor.remove(PREF_GEOFENCE_LATITUDE)
            editor.remove(PREF_GEOFENCE_LONGITUDE)
            editor.remove(PREF_GEOFENCE_RADIUS)
            editor.remove(PREF_GEOFENCE_LOCK_MINS)
            editor.remove(PREF_GEOFENCE_WIPE_ENABLED)
            editor.remove(PREF_GEOFENCE_WIPE_MINS)
            editor.remove(PREF_GEOFENCE_NEAR_BEACON)
            editor.remove(PREF_GEOFENCE_OUT_OF_FENCE)
            editor.remove(PREF_GEOFENCE_SCAN_MODE)
            editor.remove(PREF_GEOFENCE_WARN_COUNTDOWN)
            editor.remove(PREF_GEOFENCE_LOCK_COUNTDOWN)
            editor.remove(PREF_GEOFENCE_CURRENT_PROFILE)
            editor.remove(PREF_GEOFENCE_LAST_DISTANCE)
            editor.remove(PREF_STORE_ID)
            editor.remove(PREF_STORE_SSID)
            editor.remove(PREF_STORE_IP)
            
            editor.apply()
            Logger.geo("$TAG 清除所有地理围栏状态完成")
            
        } catch (e: Exception) {
            Logger.geoE("$TAG 清除地理围栏状态失败", e)
        }
    }
    
    /**
     * 获取地理围栏状态摘要
     */
    fun getGeofenceStateSummary(context: Context): String {
        return try {
            val prefs = getSharedPreferences(context)
            val status = prefs.getInt(PREF_GEOFENCE_STATUS, GpsLocationManager.IN_ZONE)
            val enabled = prefs.getBoolean(PREF_GEOFENCE_ENABLED, false)
            val latitude = prefs.getFloat(PREF_GEOFENCE_LATITUDE, 0f)
            val longitude = prefs.getFloat(PREF_GEOFENCE_LONGITUDE, 0f)
            val radius = prefs.getInt(PREF_GEOFENCE_RADIUS, 200)
            
            val statusText = when (status) {
                GpsLocationManager.IN_ZONE -> "围栏内"
                GpsLocationManager.OUT_OF_ZONE -> "围栏外"
                GpsLocationManager.LOCK_SCREEN -> "锁屏"
                GpsLocationManager.WIPE_DATA -> "数据擦除"
                GpsLocationManager.ROAMING -> "漫游"
                else -> "未知"
            }
            
            "状态: $statusText, 启用: $enabled, 中心: ($latitude, $longitude), 半径: ${radius}米"
        } catch (e: Exception) {
            Logger.geoE("$TAG 获取状态摘要失败", e)
            "状态获取失败"
        }
    }
    
    /**
     * 获取SharedPreferences实例
     */
    fun getSharedPreferences(context: Context): SharedPreferences {
        return PreferenceManager.getDefaultSharedPreferences(context)
    }
}
