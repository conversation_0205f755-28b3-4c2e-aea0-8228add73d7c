package com.dspread.mdm.service.platform.api.device

import android.content.Context
import android.net.wifi.WifiManager
import android.os.Build
import android.telephony.cdma.CdmaCellLocation
import android.telephony.gsm.GsmCellLocation
import android.telephony.TelephonyManager
import com.dspread.mdm.service.services.DspreadService
import com.dspread.mdm.service.utils.log.Logger
import com.dspread.mdm.service.platform.api.model.DeviceInfo
import com.dspread.mdm.service.platform.api.model.CellInfo

/**
 * 设备信息API
 * 提供设备硬件信息、系统信息等获取功能
 *
 */
class DeviceInfoApi(private val context: Context) {

    companion object {
        private const val TAG = "DeviceInfoApi"

        @Volatile
        private var INSTANCE: DeviceInfoApi? = null

        /**
         * 获取单例实例
         */
        private fun getInstance(context: Context): DeviceInfoApi {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: DeviceInfoApi(context.applicationContext).also {
                    INSTANCE = it
                    Logger.platform("DeviceInfoApi 单例实例已创建")
                }
            }
        }

        // ==================== 静态方法接口 ====================

        /**
         * 获取设备序列号
         */
        fun getSerialNumber(context: Context): String? {
            return getInstance(context).getSerialNumber()
        }

        /**
         * 获取设备IMEI
         */
        fun getImei(context: Context): String {
            return getInstance(context).getImei()
        }

        /**
         * 获取设备MAC地址
         */
        fun getMacAddress(context: Context): String {
            return getInstance(context).getMacAddress()
        }

        /**
         * 获取设备信息
         */
        fun getDeviceInfo(context: Context): DeviceInfo {
            return getInstance(context).getCompleteDeviceInfo()
        }

        /**
         * 获取基站信息
         */
        fun getCellInfo(context: Context): CellInfo? {
            return getInstance(context).getCellInfo()
        }
    }
    
    private val telephonyManager by lazy {
        context.getSystemService(Context.TELEPHONY_SERVICE) as TelephonyManager
    }
    
    private val wifiManager by lazy {
        context.applicationContext.getSystemService(Context.WIFI_SERVICE) as WifiManager
    }
    
    // 临时移除SystemPropertiesApi依赖
    
    /**
     * 获取完整设备信息
     */
    fun getCompleteDeviceInfo(): DeviceInfo {
        return DeviceInfo(
            serialNumber = getSerialNumber(),
            imei = getImei(),
            model = getDeviceModel(),
            manufacturer = getManufacturer(),
            androidVersion = getAndroidVersion(),
            buildVersion = getBuildVersion(),
            macAddress = getMacAddress(),
            cellInfo = getCellInfo()
        )
    }
    
    /**
     * 获取设备序列号
     */
    fun getSerialNumber(): String {
        // Android 14及以上，优先使用DspreadService
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE) {
            try {
                val serialFromService = DspreadService.getDeviceSerialNumber()
                if (serialFromService.isNotEmpty() && serialFromService != "unknown") {
//                    Logger.platform("$TAG 通过DspreadService获取序列号: $serialFromService")
                    return serialFromService
                }
            } catch (e: Exception) {
                Logger.platformE("$TAG DspreadService获取序列号失败，使用备用方案", e)
            }
        }

        var serialNo: String? = null
        var roSerialNo: String? = null
        var dsnSerialNo: String? = null

        // 1. 尝试官方 API（Build.getSerial()）
        try {
            serialNo = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                Build.getSerial()
            } else {
                Build.SERIAL // Android 7.x 及以下
            }
        } catch (e: Exception) {
            Logger.platformE("$TAG 获取设备序列号失败", e)
        }

        // 2. 备用方案：读取系统属性
        if (serialNo.isNullOrEmpty() || "unknown" == serialNo) {
            try {
                roSerialNo = getSystemProperty("ro.serialno")
                dsnSerialNo = getSystemProperty("sys.dsn")

                // 选择序列号
                serialNo = when {
                    !dsnSerialNo.isNullOrEmpty() && dsnSerialNo != "unknown" -> dsnSerialNo
                    !roSerialNo.isNullOrEmpty() && roSerialNo != "unknown" -> roSerialNo
                    else -> serialNo
                }
            } catch (e: Exception) {
                Logger.platformE("$TAG 读取系统属性失败", e)
            }
        }

        // 3. 最终检查和返回
        return when {
            !serialNo.isNullOrEmpty() && serialNo != "unknown" -> serialNo
            else -> "unknown"
        }
    }

    /**
     * 获取系统属性
     */
    private fun getSystemProperty(key: String): String? {
        return try {
            val systemProperties = Class.forName("android.os.SystemProperties")
            val get = systemProperties.getMethod("get", String::class.java)
            get.invoke(null, key) as? String
        } catch (e: Exception) {
            Logger.platformE("获取系统属性失败: $key", e)
            null
        }
    }
    
    /**
     * 获取IMEI
     */
    fun getImei(): String {
        return try {
            when {
                Build.VERSION.SDK_INT >= Build.VERSION_CODES.O -> {
                    telephonyManager.imei ?: telephonyManager.deviceId ?: ""
                }
                else -> {
                    telephonyManager.deviceId ?: ""
                }
            }
        } catch (e: SecurityException) {
            Logger.platformW("No permission to get IMEI")
            ""
        } catch (e: Exception) {
            Logger.platformE("Failed to get IMEI", e)
            ""
        }
    }
    
    /**
     * 获取设备型号
     */
    fun getDeviceModel(): String {
        return Build.MODEL
    }
    
    /**
     * 获取制造商
     */
    fun getManufacturer(): String {
        return Build.MANUFACTURER
    }
    
    /**
     * 获取Android版本
     */
    fun getAndroidVersion(): String {
        return Build.VERSION.RELEASE
    }
    
    /**
     * 获取构建版本
     */
    fun getBuildVersion(): String {
        return Build.DISPLAY
    }
    
    /**
     * 获取WiFi MAC地址
     */
    fun getMacAddress(): String {
        return try {
            val wifiInfo = wifiManager.connectionInfo
            wifiInfo?.macAddress ?: "unknown"
        } catch (e: Exception) {
            Logger.platformE("Failed to get MAC address", e)
            "unknown"
        }
    }
    
    /**
     * 获取基站信息
     */
    fun getCellInfo(): CellInfo? {
        return try {
            val cellLocation = telephonyManager.cellLocation
            val operator = telephonyManager.networkOperator
            
            val mcc = if (operator.length >= 3) operator.substring(0, 3) else ""
            val mnc = if (operator.length >= 3) operator.substring(3) else ""
            
            when (cellLocation) {
                is GsmCellLocation -> {
                    CellInfo(
                        type = "GSM",
                        lac = cellLocation.lac,
                        cid = cellLocation.cid,
                        mcc = mcc,
                        mnc = mnc,
                        signalStrength = getSignalStrength()
                    )
                }
                is CdmaCellLocation -> {
                    CellInfo(
                        type = "CDMA",
                        lac = cellLocation.networkId,
                        cid = cellLocation.baseStationId,
                        mcc = mcc,
                        mnc = mnc,
                        signalStrength = getSignalStrength()
                    )
                }
                else -> null
            }
        } catch (e: SecurityException) {
            Logger.platformW("No permission to get cell info")
            null
        } catch (e: Exception) {
            Logger.platformE("Failed to get cell info", e)
            null
        }
    }
    
    /**
     * 获取信号强度
     */
    private fun getSignalStrength(): Int {
        return try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
                val signalStrength = telephonyManager.signalStrength
                signalStrength?.level ?: 0
            } else {
                // 对于较旧的Android版本，使用CellInfo获取信号强度
                val cellInfos = telephonyManager.allCellInfo
                if (cellInfos != null && cellInfos.isNotEmpty()) {
                    val registeredCell = cellInfos.find { it.isRegistered }
                    when (registeredCell) {
                        is android.telephony.CellInfoGsm -> {
                            registeredCell.cellSignalStrength.level
                        }
                        is android.telephony.CellInfoLte -> {
                            registeredCell.cellSignalStrength.level
                        }
                        is android.telephony.CellInfoWcdma -> {
                            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR2) {
                                registeredCell.cellSignalStrength.level
                            } else {
                                0
                            }
                        }
                        is android.telephony.CellInfoCdma -> {
                            registeredCell.cellSignalStrength.level
                        }
                        else -> 0
                    }
                } else {
                    0
                }
            }
        } catch (e: Exception) {
            Logger.platformW("Failed to get signal strength: ${e.message}")
            0
        }
    }

    /**
     * 获取系统属性
     */
    fun getSystemProperty(key: String, defaultValue: String = ""): String {
        return try {
            val systemPropertiesClass = Class.forName("android.os.SystemProperties")
            val getMethod = systemPropertiesClass.getMethod("get", String::class.java, String::class.java)
            getMethod.invoke(null, key, defaultValue) as String
        } catch (e: Exception) {
            Logger.platformW("Failed to get system property $key: ${e.message}")
            defaultValue
        }
    }

    /**
     * 设置系统属性
     */
    fun setSystemProperty(key: String, value: String): Boolean {
        return try {
            val systemPropertiesClass = Class.forName("android.os.SystemProperties")
            val setMethod = systemPropertiesClass.getMethod("set", String::class.java, String::class.java)
            setMethod.invoke(null, key, value)
            Logger.platformI("System property set: $key = $value")
            true
        } catch (e: Exception) {
            Logger.platformE("Failed to set system property $key: ${e.message}")
            false
        }
    }
    
    /**
     * 获取设备品牌
     */
    fun getBrand(): String {
        return Build.BRAND
    }
    
    /**
     * 获取设备名称
     */
    fun getDevice(): String {
        return Build.DEVICE
    }
    
    /**
     * 获取硬件信息
     */
    fun getHardware(): String {
        return "unknown" // 临时简化
    }
    
    /**
     * 获取主板信息
     */
    fun getBoard(): String {
        return Build.BOARD
    }
    
    /**
     * 获取CPU架构
     */
    fun getCpuAbi(): String {
        return Build.SUPPORTED_ABIS.firstOrNull() ?: "unknown"
    }
    
    /**
     * 获取所有支持的CPU架构
     */
    fun getSupportedAbis(): Array<String> {
        return Build.SUPPORTED_ABIS
    }
    
    /**
     * 获取构建指纹
     */
    fun getBuildFingerprint(): String {
        return Build.FINGERPRINT
    }
    
    /**
     * 获取构建ID
     */
    fun getBuildId(): String {
        return Build.ID
    }
    
    /**
     * 获取构建类型
     */
    fun getBuildType(): String {
        return Build.TYPE
    }
    
    /**
     * 获取构建标签
     */
    fun getBuildTags(): String {
        return Build.TAGS
    }
    
    /**
     * 获取SDK版本
     */
    fun getSdkVersion(): Int {
        return Build.VERSION.SDK_INT
    }
    
    /**
     * 获取安全补丁级别
     */
    fun getSecurityPatchLevel(): String {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            Build.VERSION.SECURITY_PATCH
        } else {
            "unknown"
        }
    }
    
    /**
     * 是否为调试版本
     */
    fun isDebuggable(): Boolean {
        return false // 临时简化
    }

    /**
     * 是否为安全版本
     */
    fun isSecure(): Boolean {
        return true // 临时简化
    }
    
    /**
     * 获取网络运营商名称
     */
    fun getNetworkOperatorName(): String {
        return try {
            telephonyManager.networkOperatorName ?: "unknown"
        } catch (e: Exception) {
            Logger.platformW("Failed to get network operator name: ${e.message}")
            "unknown"
        }
    }
    
    /**
     * 获取SIM卡运营商名称
     */
    fun getSimOperatorName(): String {
        return try {
            telephonyManager.simOperatorName ?: "unknown"
        } catch (e: Exception) {
            Logger.platformW("Failed to get SIM operator name: ${e.message}")
            "unknown"
        }
    }
    
    /**
     * 获取SIM卡序列号
     */
    fun getSimSerialNumber(): String {
        return try {
            telephonyManager.simSerialNumber ?: "unknown"
        } catch (e: SecurityException) {
            Logger.platformW("No permission to get SIM serial number")
            "unknown"
        } catch (e: Exception) {
            Logger.platformE("Failed to get SIM serial number", e)
            "unknown"
        }
    }
    
    /**
     * 检查API可用性
     */
    fun isAvailable(): Boolean {
        return try {
            telephonyManager != null && wifiManager != null
        } catch (e: Exception) {
            Logger.platformW("DeviceInfoApi not available: ${e.message}")
            false
        }
    }
}
