package com.bbpos.wiseapp.tms.service;

import android.app.IntentService;
import android.content.Intent;
import android.content.SharedPreferences;
import android.graphics.PixelFormat;
import android.os.Handler;
import android.os.Looper;
import android.os.Message;
import android.preference.PreferenceManager;
import android.util.Log;
import android.view.Gravity;
import android.view.View;
import android.view.View.OnClickListener;
import android.view.WindowManager;

import com.bbpos.wiseapp.logger.BBLog;
import com.bbpos.wiseapp.service.common.RequestPermission;
import com.bbpos.wiseapp.tms.utils.BroadcastActions;
import com.bbpos.wiseapp.tms.widget.DesktopLayout;

import java.util.Timer;
import java.util.TimerTask;

public class LockDeskService extends IntentService{

	private WindowManager mWindowManager;
	private WindowManager.LayoutParams mLayout;
	public static DesktopLayout mDesktopLayout;
	/**该终端是否被锁定*/
	public static final String IS_POS_LOCK = "IS_POS_LOCK";
	public static final String REMOTE_COMMAND_TMS_CLOSE = "tmsClose";
	/**TMS操作-锁定pos*/
	public static final String TMS_OPERATE_LOCK = "02";
	/**TMS操作-解除锁定pos*/
	public static final String TMS_OPERATE_UNLOCK = "03"; 
	/**TMS操作-启动 或者轮询监听锁定pos*/
	public static final String TMS_BOOT_COMPLETED_LOCK = "04";
	public LockDeskService() {
		super("LockDeskService");
		// TODO Auto-generated constructor stub
	}

	@Override
	protected void onHandleIntent(Intent intent) {
		// TODO Auto-generated method stub
		//获取操作类型
		final String type = intent.getExtras().getString(REMOTE_COMMAND_TMS_CLOSE);
		if(!type.equals(TMS_BOOT_COMPLETED_LOCK)) {
			Intent intents = new Intent(BroadcastActions.TMS_LOCK_SUCCESS_BROAD);
 			LockDeskService.this.sendBroadcast(intents, RequestPermission.REQUEST_PERMISSION_INTERNET);
		}
		Handler  handler = new Handler(Looper.getMainLooper());                          

	      handler.post(new Runnable() {    
	             @Override    
	        public void run() {   
				 if (type.equals(TMS_OPERATE_LOCK) || type.equals(TMS_BOOT_COMPLETED_LOCK)) {
					SharedPreferences sp = PreferenceManager.getDefaultSharedPreferences(LockDeskService.this);
					sp.edit().putBoolean(IS_POS_LOCK, true).commit();
					createWindowManager();
					createDesktopLayout();
					showDesk();
				 } else {
					createWindowManager();
					SharedPreferences sp = PreferenceManager.getDefaultSharedPreferences(LockDeskService.this);
					sp.edit().putBoolean(IS_POS_LOCK, false).commit();
	//	            		closeDesk();
				 }
	         }
	     }); 
	}
	/**
	 * 创建悬浮窗体
	 */
	private void createDesktopLayout() {
		if (mDesktopLayout == null) {
			mDesktopLayout = new DesktopLayout(this);
			BBLog.i("mWindowManager", "showDesk"+mDesktopLayout.getId());
			mDesktopLayout.setOnClickListener(new OnClickListener() {
				
				@Override
				public void onClick(View arg0) {
					// TODO Auto-generated method stub
					SharedPreferences sp = PreferenceManager.getDefaultSharedPreferences(LockDeskService.this);
					if(!sp.getBoolean(IS_POS_LOCK, true)) {
						closeDesk();
					}
				}
			});
		}
	}
	
	/**
	 * 显示DesktopLayout
	 */
	private void showDesk() {
		BBLog.i("mWindowManager", "showDesk"+mDesktopLayout.getId());
		if(mDesktopLayout.getParent() == null)
		mWindowManager.addView(mDesktopLayout, mLayout);
		
		StartCheckShowViewClose();
		this.stopSelf();
	}
	/**
	 * 关闭DesktopLayout
	 */
	private void closeDesk() {
		if (mDesktopLayout.getParent() != null) {
			mWindowManager.removeView(mDesktopLayout);
		}
	}
	/**
	 * 设置WindowManager
	 */
	private void createWindowManager() {
		// 取得系统窗体
		mWindowManager = (WindowManager) getApplicationContext().getSystemService("window");
		// 窗体的布局样式
		mLayout = new WindowManager.LayoutParams();
		// 设置窗体显示类型——TYPE_SYSTEM_ALERT(系统提示)
		mLayout.type = WindowManager.LayoutParams.TYPE_SYSTEM_ALERT;
		// 设置窗体焦点及触摸：
		// FLAG_NOT_FOCUSABLE(不能获得按键输入焦点)
		mLayout.flags = WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE|WindowManager.LayoutParams.FLAG_FULLSCREEN;
		// 设置显示的模式
		mLayout.format = PixelFormat.RGBA_8888;
		// 设置对齐的方法
		mLayout.gravity = Gravity.CENTER;// | Gravity.LEFT;
		// 设置窗体宽度和高度
		mLayout.width = WindowManager.LayoutParams.MATCH_PARENT;
		mLayout.height = WindowManager.LayoutParams.MATCH_PARENT;
	}
	
	public void StartCheckShowViewClose() {
		task = new TimerTask() {  
		    @Override  
		    public void run() {  
				// TODO Auto-generated method stub
				Message message = new Message();
				message.what = 1;
				handler.sendMessage(message);
		    }
		};
		timer.schedule(task, 60000, 60000);	
	}
	private final Timer timer = new Timer();
	private TimerTask task;  
	Handler handler = new Handler() {  
	    @Override  
	    public void handleMessage(Message msg) {  
	        // TODO Auto-generated method stub  
	        // 要做的事情  
	    	SharedPreferences sp = PreferenceManager.getDefaultSharedPreferences(getApplicationContext());
			if (!sp.getBoolean(IS_POS_LOCK, true)) {
				timer.cancel();
				closeDesk() ;
			}
	    }  
	}; 		
}
