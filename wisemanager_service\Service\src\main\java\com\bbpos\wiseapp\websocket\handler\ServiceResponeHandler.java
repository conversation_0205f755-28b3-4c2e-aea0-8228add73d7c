package com.bbpos.wiseapp.websocket.handler;

import android.content.Context;

import com.bbpos.wiseapp.logger.BBLog;
import com.bbpos.wiseapp.service.common.UsualData;
import com.bbpos.wiseapp.tms.utils.Constants;
import com.bbpos.wiseapp.tms.utils.WebSocketTaskListManager;
import com.bbpos.wiseapp.utils.SharedPreferencesUtils;
import com.bbpos.wiseapp.websocket.WebSocketManager;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.concurrent.ConcurrentHashMap;

/**
 * 注册websocket请求。处理 ws对应的响应报文
 */
public class ServiceResponeHandler {
    private WebSocketManager webSocketManager;
    private Context mContext;
    //注册请求
    public static ConcurrentHashMap registerRuests = new ConcurrentHashMap();

    public ServiceResponeHandler(Context context, WebSocketManager webSocketManager){
        this.webSocketManager = webSocketManager;
        this.mContext = context;
    }

    public void registerAction(String tranCode,String requestId){
        if (registerRuests == null)
            registerRuests = new ConcurrentHashMap();
        registerRuests.put(tranCode,requestId);
    }

    public void dispatch(String orderMsg) {
        String orgTranCode = "";
        String orgReqId = "";
        String orgReqTime = "";
        JSONObject jsonObject = null;
        try {
            jsonObject = new JSONObject(orderMsg);
            //获取原始请求id
            if (jsonObject.has("org_request_id")) {
                orgReqId = jsonObject.getString("org_request_id");
            }
            //获取原始请求时间
            if (jsonObject.has("org_request_time")) {
                orgReqTime = jsonObject.getString("org_request_time");
            }
            // 得到原始请求tranCode，  requestId = reqTime + tranCode
            orgTranCode = orgReqId.replaceFirst(orgReqTime,"");

            switch (orgTranCode){
                case "C0109":
//                    if (registerRuests.containsKey(orgTranCode) && orgReqId.equals(registerRuests.get(orgTranCode).toString())){
                        SharedPreferencesUtils.setSharePreferencesValue(Constants.FLAG_LAST_TER_INFO_UPLOAD_ALIVE,"true");
        				BBLog.d(Constants.TAG, "WebSocket 接收到S0000报文. C0109 任务上报成功，等待下次轮询,原始 requestId： "+orgReqId);
        				Constants.IS_FIRST_TER_INFO_UPLOAD_COMPLETED = true;
        				//取消注册
//        				registerRuests.remove(orgTranCode);
//                    }
                    break;
                case "C0901":
                    BBLog.d(Constants.TAG, "WebSocket 接收到S0000报文. C0901上报成功： "+orgReqId);
                    break;
                case "C0902":
                    BBLog.d(Constants.TAG, "WebSocket 接收到S0000报文. C0902上报成功： "+orgReqId);
                    break;
                case "C0903":
                    BBLog.d(Constants.TAG, "WebSocket 接收到S0000报文. C0903上报成功： "+orgReqId);
                    break;
                case "C0904":
                    BBLog.d(Constants.TAG, "WebSocket 接收到S0000报文. C0904上报成功： "+orgReqId);
                    break;
				case "C0906":
					BBLog.d(Constants.TAG, "WebSocket 接收到S0000报文. C0906上报成功： "+orgReqId);
                    break;
                case "C0107":
                    BBLog.d(Constants.TAG, "WebSocket 接收到S0000报文. C0107上报成功： "+orgReqId);
                    RulebasedListHandler.removeRuleResultJsonObjByOrgReqId(orgReqId);
                    break;
                case "C0108":
                    BBLog.d(Constants.TAG, "WebSocket 接收到S0000报文. C0108上报成功： "+orgReqId);
                    WebSocketTaskListManager.removeWSTaskResultJsonObjByOrgReqId(orgReqId);
                    break;
                default:
                    //取消注册
//                    registerRuests.remove(orgTranCode);
                    break;
            }
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

}
