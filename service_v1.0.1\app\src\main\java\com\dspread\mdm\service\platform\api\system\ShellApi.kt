package com.dspread.mdm.service.platform.api.system

import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.BufferedReader
import java.io.InputStreamReader

/**
 * 统一的Shell命令执行工具类
 * 简化版本：解决功能重叠问题
 */
object ShellApi {
    
    /**
     * 执行Shell命令
     */
    suspend fun executeCommand(command: String): Result<String> {
        return withContext(Dispatchers.IO) {
            try {
                val process = Runtime.getRuntime().exec(command)
                val reader = BufferedReader(InputStreamReader(process.inputStream))
                val output = StringBuilder()
                
                var line: String?
                while (reader.readLine().also { line = it } != null) {
                    output.append(line).append("\n")
                }
                
                val exitCode = process.waitFor()
                reader.close()
                
                if (exitCode == 0) {
                    Result.success(output.toString())
                } else {
                    Result.failure(Exception("Command failed with exit code: $exitCode"))
                }
            } catch (e: Exception) {
                Result.failure(e)
            }
        }
    }
    
    /**
     * 执行Root Shell命令（安全版本）
     */
    suspend fun executeRootCommand(command: String): Result<String> {
        return withContext(Dispatchers.IO) {
            try {
                // 使用参数化执行避免注入
                val process = Runtime.getRuntime().exec(arrayOf("su", "-c", command))
                val reader = BufferedReader(InputStreamReader(process.inputStream))
                val output = StringBuilder()

                var line: String?
                while (reader.readLine().also { line = it } != null) {
                    output.append(line).append("\n")
                }

                val exitCode = process.waitFor()
                reader.close()

                if (exitCode == 0) {
                    Result.success(output.toString())
                } else {
                    Result.failure(Exception("Root command failed with exit code: $exitCode"))
                }
            } catch (e: Exception) {
                Result.failure(e)
            }
        }
    }

    /**
     * 检查Root权限是否可用
     */
    suspend fun isRootAvailable(): Boolean {
        return try {
            val result = executeRootCommand("id")
            result.isSuccess && result.getOrNull()?.contains("uid=0") == true
        } catch (e: Exception) {
            false
        }
    }
}
