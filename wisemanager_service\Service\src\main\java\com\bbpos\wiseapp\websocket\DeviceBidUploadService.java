package com.bbpos.wiseapp.websocket;

import android.content.Intent;
import android.os.Handler;
import android.os.Looper;
import android.provider.Settings;
import android.text.TextUtils;

import androidx.annotation.Nullable;

import com.bbpos.bbdevice.BBDeviceController;
import com.bbpos.wiseapp.logger.BBLog;
import com.bbpos.wiseapp.service.WakeLockService;
import com.bbpos.wiseapp.service.bbdevice.MyBBDeviceControllerListener;
import com.bbpos.wiseapp.service.common.UsualData;
import com.bbpos.wiseapp.system.api.DeviceInfoApi;
import com.bbpos.wiseapp.system.api.SysIntermediateApi;
import com.bbpos.wiseapp.tms.utils.Constants;
import com.bbpos.wiseapp.tms.utils.ParameterName;
import com.bbpos.wiseapp.utils.SharedPreferencesUtils;

import org.json.JSONException;
import org.json.JSONObject;

import java.text.SimpleDateFormat;
import java.util.Hashtable;
import java.util.Iterator;

public class DeviceBidUploadService extends WakeLockService {
	private static final String TAG = "DeviceBid";
	private BBDeviceController bbDeviceController;
	private MyBBDeviceControllerListener listener;

	public DeviceBidUploadService() {
		super("DeviceBidUploadService");
	}

	@Override
	protected void onHandleIntent(@Nullable Intent intent) {
		BBLog.i(TAG, "DeviceBidUploadService DeviceBidUploadService, start...");
		if (DeviceInfoApi.getIntance().isWisePosPro() || DeviceInfoApi.getIntance().isWisePosTouch() || DeviceInfoApi.getIntance().isWisePosTouchPlus()) {
			try {
				//check rom build time
				String deviceInfoData = null;
				long utc = 0l;
				long utc_target = 0l;
				try {
					utc = Long.valueOf(SysIntermediateApi.getIntance().getProp("ro.build.date.utc"));
					utc_target = new SimpleDateFormat("yyyy-MM-dd").parse("2021-04-26").getTime();
				} catch (Exception e) {
					e.printStackTrace();
				}

				if (utc*1000>utc_target) {
					deviceInfoData = Settings.System.getString(getContentResolver(), "BBPOSDeviceInfo");
					BBLog.i(TAG, "DeviceBidUploadService GetDeviceInfo from OS,  deviceInfoData = " + deviceInfoData);
				}
				//rom has keep deviceinfo, use it
				if (!TextUtils.isEmpty(deviceInfoData)) {
					Hashtable<String, String> deviceInfoDataTable = stringToHashTable(deviceInfoData);
					handleAndUploadData(deviceInfoDataTable);
				} else {
					// get from sdk
					BBLog.i(TAG, "DeviceBidUploadService GetDeviceInfo from SDK ..." );

					if (bbDeviceController == null) {
						listener = new MyBBDeviceControllerListener() {
							@Override
							public void onReturnDeviceInfo(Hashtable<String, String> deviceInfoData) {
								BBLog.i(TAG, "DeviceBidUploadService onReturnDeviceInfo " );
								handleAndUploadData(deviceInfoData);
							}
						};
						bbDeviceController = BBDeviceController.getInstance(getApplicationContext(), listener);
						listener.setBbDeviceController(bbDeviceController);
						BBDeviceController.setDebugLogEnabled(true);
					}
					new Handler(Looper.getMainLooper()).postDelayed(new Runnable() {
						@Override
						public void run() {
							BBLog.i(TAG, "======> onReturnDeviceInfo, startSerial...");
							bbDeviceController.startSerial();
						}
					}, 90000);
				}
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
	}

	private void handleAndUploadData(Hashtable<String, String> deviceInfoData) {
		JSONObject infoData = getDeviceInfoParamer(deviceInfoData);
		String fullDeviceInfo = getFullDeviceInfo(deviceInfoData);
		if (infoData != null) {
			SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_DEVICE_BID_INFO, infoData.toString());
			if (listener != null && ((MyBBDeviceControllerListener) listener).isSerialConnected()) {
				bbDeviceController.stopSerial();
			}
			WebSocketSender.C0906_DeviceInfoUpload(infoData);
		}

		if (!TextUtils.isEmpty(fullDeviceInfo)) {
			SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_DEVICE_FULL_INFO, fullDeviceInfo);
			// TODO: 2022/2/8 修改为主动上送模式
			WebSocketSender.C0908_DeviceFullInfoUpload(fullDeviceInfo);
		}
	}

	private String getFullDeviceInfo(Hashtable<String, String> deviceInfoData){
		if (deviceInfoData == null || deviceInfoData.size() == 0) return "";
		JSONObject jsonObject = new JSONObject();
		for (String key : deviceInfoData.keySet()) {
			try {
				jsonObject.put(key,deviceInfoData.get(key));
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
		return jsonObject.toString();
	}

	private JSONObject getDeviceInfoParamer(Hashtable<String, String> deviceInfoData) {
		if (deviceInfoData == null || deviceInfoData.keySet().size() == 0) {
			BBLog.i(BBLog.TAG, "DeviceBidUploadService getDeviceInfoParamer deviceInfoData為空");
			return null;
		}
		JSONObject deviceInfo = new JSONObject();
		try {
			//獲取wc-bid、sp-bid
			if (deviceInfoData.get(ParameterName.bid) != null) {
				String bid = deviceInfoData.get(ParameterName.bid);
				BBLog.i(BBLog.TAG, "onReturnDeviceInfo: bid ============ " + (String) deviceInfoData.get(ParameterName.bid));
				if (!bid.contains(",")) {
					if (bid.startsWith("WSP7")) {
						deviceInfo.put(ParameterName.sp_bid, bid.trim());
						deviceInfo.put(ParameterName.wisecube_bid, "");
					} else if (bid.startsWith("CHB6")) {
						deviceInfo.put(ParameterName.sp_bid, "");
						deviceInfo.put(ParameterName.wisecube_bid, bid.trim());
					}  else if (bid.startsWith("WTH1") || bid.startsWith("WSC6") || bid.startsWith("WSS6")) {
						deviceInfo.put(ParameterName.sp_bid, bid.trim());
						deviceInfo.put(ParameterName.wisecube_bid, "");
					}
				} else {
					String[] bids = bid.split(",");
					if (bids != null && bids.length == 2) {
						String spBid = bids[0];
						String wcBid = bids[1];
						deviceInfo.put(ParameterName.sp_bid, spBid.trim());
						deviceInfo.put(ParameterName.wisecube_bid, wcBid.trim());
					}
				}
			}

			//獲取wc 、sp firmware version
			if (deviceInfoData.get(ParameterName.firmware_version) != null) {
				String fwVersion = deviceInfoData.get(ParameterName.firmware_version);
				BBLog.i(BBLog.TAG, "onReturnDeviceInfo: firmware_version ============ " + (String) deviceInfoData.get(ParameterName.firmware_version));
				if (!fwVersion.contains(",")) {
					if (fwVersion.startsWith("WSP7")) {
						deviceInfo.put(ParameterName.fw, "");
						deviceInfo.put(ParameterName.spfw, fwVersion.trim());
					} else if (fwVersion.startsWith("CHB6")) {
						deviceInfo.put(ParameterName.fw, fwVersion.trim());
						deviceInfo.put(ParameterName.spfw, "");
					} else {
						deviceInfo.put(ParameterName.fw, "");
						deviceInfo.put(ParameterName.spfw, fwVersion.trim());
					}
				} else {
					String[] bids = fwVersion.split(",");
					if (bids != null && bids.length == 2) {
						BBLog.i(BBLog.TAG, "onReturnDeviceInfo: firmware_version length ============ " + 2);
						parserFirmwareInfo(deviceInfo, bids);
					}

					if (bids != null && bids.length == 3) {
						BBLog.i(BBLog.TAG, "onReturnDeviceInfo: firmware_version length ============ " + 3);
						parserFirmwareInfo(deviceInfo, bids);
					}

					if (bids != null && bids.length == 4) {
						BBLog.i(BBLog.TAG, "onReturnDeviceInfo: firmware_version length ============ " + 4);
						parserFirmwareInfo(deviceInfo, bids);
					}
				}

			}

			//獲取config version
			if (deviceInfoData.get(ParameterName.device_setting_version) != null || deviceInfoData.get(ParameterName.terminal_setting_version) != null) {
				if (TextUtils.isEmpty(deviceInfo.optString(ParameterName.conf)) && deviceInfoData.get(ParameterName.device_setting_version) == null) {
					deviceInfo.put(ParameterName.conf, deviceInfoData.get(ParameterName.terminal_setting_version));
				} else if (TextUtils.isEmpty(deviceInfo.optString(ParameterName.conf)) && deviceInfoData.get(ParameterName.terminal_setting_version) == null) {
					deviceInfo.put(ParameterName.conf, deviceInfoData.get(ParameterName.device_setting_version));
				} else {
					//兩個都存在情況下，取其中之一進行賦值
					deviceInfo.put(ParameterName.conf, deviceInfoData.get(ParameterName.device_setting_version));
				}
			} else {
				deviceInfo.put(ParameterName.conf, "");
			}
			BBLog.i(BBLog.TAG, "onReturnDeviceInfo: conf ============ " + (String) deviceInfo.optString(ParameterName.conf));

			if (deviceInfoData.get(ParameterName.battery_percentage) != null){
				deviceInfo.put(ParameterName.wc_battery, deviceInfoData.get(ParameterName.battery_percentage));
			}else {
				deviceInfo.put(ParameterName.wc_battery, "");
			}
			BBLog.i(BBLog.TAG, "onReturnDeviceInfo: conf ============ " + (String) deviceInfo.optString(ParameterName.battery_percentage));
		} catch (Exception e) {
			e.printStackTrace();
		}
		return deviceInfo;
	}

	private void parserFirmwareInfo(JSONObject deviceInfo, String[] bids) throws JSONException {
		if (!TextUtils.isEmpty(deviceInfo.optString(ParameterName.spfw)) && !TextUtils.isEmpty(deviceInfo.optString(ParameterName.fw)))
			return;
		for (String bid : bids) {
			if (!TextUtils.isEmpty(bid)) {
				if (bid.trim().startsWith("WSP7")) {
					deviceInfo.put(ParameterName.spfw, bid.trim());
				} else if (bid.trim().startsWith("CHB6")) {
					deviceInfo.put(ParameterName.fw, bid.trim());
				} else if (bid.trim().startsWith("SRED")){
					if (bid.trim().startsWith("SREDv3")) {
						Constants.MSR_TYPE = Constants.E_MSR_TYPE.MAXIM_MSR;
					} else if (bid.trim().startsWith("SREDv4")) {
						Constants.MSR_TYPE = Constants.E_MSR_TYPE.MEGAHUNT_MSR;
					}
				}
			}
		}
	}

	private Hashtable<String, String> stringToHashTable(String jsonStr) {
		if (TextUtils.isEmpty(jsonStr)) return null;
		Hashtable<String,String> deviceInfo = new Hashtable<>();
		try {
			JSONObject jsonObject = new JSONObject(jsonStr);
			if (jsonObject != null) {
				Iterator iterator = jsonObject.keys();
				while(iterator.hasNext()) {
					String key = (String) iterator.next();
					String value = TextUtils.isEmpty(jsonObject.optString(key)) ? "" : jsonObject.optString(key);
					deviceInfo.put(key,value);
				}
				return deviceInfo;
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return deviceInfo;
	}

}
