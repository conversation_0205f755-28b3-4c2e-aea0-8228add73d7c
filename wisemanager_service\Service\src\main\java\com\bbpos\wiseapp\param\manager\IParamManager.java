package com.bbpos.wiseapp.param.manager;

import android.content.Context;
import java.util.List;

public interface IParamManager {
    List<ParamModel> getParams(Context var1);

    boolean hasParam(Context var1, String var2);

    String getStrParam(Context var1, String var2, String var3);

    int getIntParam(Context var1, String var2, int var3);

    boolean getBooleanParam(Context var1, String var2, boolean var3);

    long getLongParam(Context var1, String var2, long var3);

    void updateParam(Context var1, String var2, Boolean var3);

    void updateParam(Context var1, String var2, String var3) throws Exception;

    void updateParam(Context var1, ParamModel var2);

    void updateParamInPar(Context var1, String var2, String var3, String var4, Boolean var5);

    void updateParamInPar(Context var1, String var2, String var3, String var4, String var5);

    void updateParamInPar(Context var1, String var2, String var3, ParamModel var4);
}
