package com.bbpos.wiseapp.system.api;

import android.bbpos.CustServiceManager;
import android.content.Context;
import android.util.Log;

import com.bbpos.wiseapp.logger.BBLog;
import com.bbpos.wiseapp.tms.service.WSTaskOsUpdateService;

import java.io.File;

public class CustomServiceManager {
    private static CustomServiceManager instance;
    private Context mContext;
    private CustServiceManager mCustServiceManager;
    private boolean bInited = false;

    public static CustomServiceManager getInstance() {
        if (instance == null) {
            instance = new CustomServiceManager();
        }
        return instance;
    }

    public boolean hasInit() {
        return bInited;
    }

    public void init(Context context) {
        mContext = context;
        mCustServiceManager = (CustServiceManager)mContext.getSystemService("custservice");
        bInited = true;
        try {
            if (mCustServiceManager != null && (DeviceInfoApi.getIntance().isWisePosTouch()
                    || DeviceInfoApi.getIntance().isWisePosTouchPlus()
                    || DeviceInfoApi.getIntance().isWisePosGo()
                    || DeviceInfoApi.getIntance().isWisePosLE()
                    || DeviceInfoApi.getIntance().isWisePosLP())) {
                mCustServiceManager.initSystemUpdate();
                mCustServiceManager.bindSystemUpdateCallback(WSTaskOsUpdateService.mSystemUpdateCallback);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        BBLog.i(BBLog.TAG, "CustomServiceManager init...");
    }

    public void replaceBootLogo(String path) {
        if (!bInited) {
            return;
        }

        BBLog.i(BBLog.TAG, "CustomServiceManager setBootLogo " + path);
        if (mCustServiceManager != null) {
            mCustServiceManager.setBootLogo(path);
        }
    }

    public void replaceBootAnimation(String path) {
        if (!bInited) {
            return;
        }

        BBLog.i(BBLog.TAG, "CustomServiceManager setBootAnimation " + path);
        if (mCustServiceManager != null) {
            mCustServiceManager.setBootAnimation(path);
        }
    }

    public void startSystemUpdate(String filePath) {
        if (!bInited) {
            return;
        }

        try {
            File file = new File(filePath);
            UpdateParser.ParsedUpdate parsedUpdate = UpdateParser.parse(file);
            if (parsedUpdate != null) {
                BBLog.e(BBLog.TAG, "UpdateParser." + parsedUpdate);
                // Refer to UpdateEngine.applyPayload() for more usage
                mCustServiceManager.applySystemUpdatePayload(parsedUpdate.mUrl, parsedUpdate.mOffset, parsedUpdate.mSize, parsedUpdate.mProps);
            } else {
                BBLog.e(BBLog.TAG, "UpdateParser failed !!!");
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void setBootupStatusBarVisible(boolean b) {
        if (!bInited) {
            return;
        }
        BBLog.i(BBLog.TAG, "CustomServiceManager setBootupStatusBarVisible " + b);
        if (b) {
            mCustServiceManager.bootupShowStatusBar();
        } else {
            mCustServiceManager.bootupHideStatusBar();
        }
    }

    public boolean getBootupStatusBarVisible() {
        if (!bInited) {
            return true;
        }

        return !mCustServiceManager.isBootupHideStatusBar();
    }

    public void setBootupNavigationBarVisible(boolean b) {
        if (!bInited) {
            return;
        }
        BBLog.i(BBLog.TAG, "CustomServiceManager setBootupNavigationBarVisible " + b);
        if (b) {
            mCustServiceManager.bootupShowNavigationBar();
        } else {
            mCustServiceManager.bootupHideNavigationBar();
        }
    }

    public boolean getBootupNavigationBarVisible() {
        if (!bInited) {
            return true;
        }

        return !mCustServiceManager.isBootupHideNavigationBar();
    }

    public void setDeviceName(String name) {
        if (!bInited) {
            return;
        }

        mCustServiceManager.setDeviceName(name);
    }

    public void startOSUpdate(String updateFile, boolean popup){
        if (!bInited) {
            return;
        }
        BBLog.i(BBLog.TAG, "CustomServiceManager startOSUpdate: " + updateFile);
        mCustServiceManager.startOSUpdate(updateFile, popup);
    }

    /**
     *  設置禁用的程序列表
     * @param appPackageNames
     *        eg: "Launcher_WiseManage\n Service_WiseManage\n"
     */
    public void setHiddenAppList(String appPackageNames) {
        if (!bInited) {
            return;
        }
        BBLog.i(BBLog.TAG, "CustomServiceManager setHiddenAppList: " + appPackageNames);
        mCustServiceManager.saveHiddenAppsList(appPackageNames != null ? appPackageNames : "");
    }

    /**
     * 查詢系統已禁用的程序列表
     * @return
     */
    public String queryHiddenAppList() {
        if (!bInited) {
            return null;
        }
        return mCustServiceManager.getHiddenAppList();
    }
}
