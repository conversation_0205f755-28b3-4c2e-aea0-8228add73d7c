package com.dspread.mdm.service.modules.remoteview.model

import com.dspread.mdm.service.modules.ModuleConfig
import org.json.JSONObject

/**
 * Remote View 模块数据模型
 */

/**
 * Remote View 配置
 * 注意：websocketUrl应该通过RemoteViewWebSocketManager.getRemoteViewWebSocketUrl()动态获取
 */
data class RemoteViewConfig(
    val websocketUrl: String, // 不再设置默认值，由调用方提供
    val captureMode: RemoteViewCaptureMode = RemoteViewCaptureMode.MEDIA_PROJECTION, // 使用MediaProjection方式
    val compressionQuality: Int = 60, // JPEG压缩质量60%
    val captureInterval: Long = 50L, // 50ms间隔，约20FPS
    val reconnectMaxAttempts: Int = 12, // 12次重连
    val reconnectInterval: Long = 5000L, // 5秒间隔
    val connectionTimeout: Long = 5000L, // 5秒超时
    val bucketPath: String? = null, // 服务器分配的bucket路径
    val enableFrameDeduplication: Boolean = false // 禁用帧去重，保证流畅度
) : ModuleConfig() {

    override fun isValid(): Boolean {
        return websocketUrl.isNotEmpty() &&
               compressionQuality in 1..100 &&
               captureInterval > 0 &&
               reconnectMaxAttempts > 0 &&
               reconnectInterval > 0 &&
               connectionTimeout > 0
    }

    override fun toJson(): JSONObject {
        return JSONObject().apply {
            put("websocket_url", websocketUrl)
            put("capture_mode", captureMode.name)
            put("compression_quality", compressionQuality)
            put("capture_interval", captureInterval)
            put("reconnect_max_attempts", reconnectMaxAttempts)
            put("reconnect_interval", reconnectInterval)
            put("connection_timeout", connectionTimeout)
            put("bucket_path", bucketPath)
        }
    }

    companion object {
        fun fromJson(json: JSONObject): RemoteViewConfig {
            return RemoteViewConfig(
                websocketUrl = json.optString("websocket_url", "ws://35.75.3.206:8080/remoteWSS/websockify"),
                captureMode = try {
                    RemoteViewCaptureMode.valueOf(json.optString("capture_mode", RemoteViewCaptureMode.MEDIA_PROJECTION.name))
                } catch (e: Exception) {
                    RemoteViewCaptureMode.MEDIA_PROJECTION
                },
                compressionQuality = json.optInt("compression_quality", 60),
                captureInterval = json.optLong("capture_interval", 50L),
                reconnectMaxAttempts = json.optInt("reconnect_max_attempts", 12),
                reconnectInterval = json.optLong("reconnect_interval", 5000L),
                connectionTimeout = json.optLong("connection_timeout", 5000L),
                bucketPath = json.optString("bucket_path").takeIf { it.isNotEmpty() },
                enableFrameDeduplication = json.optBoolean("enable_frame_deduplication", false) // 默认禁用
            )
        }
    }
}

/**
 * Remote View 截屏方式枚举
 */
enum class RemoteViewCaptureMode {
    // MediaProjection方式（当前使用）
    MEDIA_PROJECTION
}

/**
 * Remote View 状态
 */
enum class RemoteViewStatus {
    STOPPED,        // 已停止
    STARTING,       // 启动中
    RUNNING,        // 运行中
    STOPPING,       // 停止中
    RECONNECTING,   // 重连中
    ERROR           // 错误状态
}

/**
 * 截屏结果
 */
data class ScreenCaptureResult(
    val success: Boolean,
    val data: ByteArray? = null,
    val filePath: String? = null,
    val captureTime: Long = 0,
    val compressTime: Long = 0,
    val dataSize: Int = 0,
    val error: String? = null
) {
    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as ScreenCaptureResult

        if (success != other.success) return false
        if (data != null) {
            if (other.data == null) return false
            if (!data.contentEquals(other.data)) return false
        } else if (other.data != null) return false
        if (filePath != other.filePath) return false
        if (captureTime != other.captureTime) return false
        if (compressTime != other.compressTime) return false
        if (dataSize != other.dataSize) return false
        if (error != other.error) return false

        return true
    }

    override fun hashCode(): Int {
        var result = success.hashCode()
        result = 31 * result + (data?.contentHashCode() ?: 0)
        result = 31 * result + (filePath?.hashCode() ?: 0)
        result = 31 * result + captureTime.hashCode()
        result = 31 * result + compressTime.hashCode()
        result = 31 * result + dataSize.hashCode()
        result = 31 * result + (error?.hashCode() ?: 0)
        return result
    }
}

/**
 * WebSocket连接状态
 */
data class WebSocketConnectionState(
    val isConnected: Boolean = false,
    val reconnectCount: Int = 0,
    val lastConnectTime: Long = 0,
    val lastError: String? = null
)

/**
 * Remote View 性能统计
 */
data class RemoteViewPerformanceStats(
    val totalFrames: Long = 0,
    val successFrames: Long = 0,
    val failedFrames: Long = 0,
    val averageCaptureTime: Long = 0,
    val averageCompressTime: Long = 0,
    val averageTransmitTime: Long = 0,
    val averageDataSize: Int = 0,
    val startTime: Long = System.currentTimeMillis()
) {
    val successRate: Float
        get() = if (totalFrames > 0) successFrames.toFloat() / totalFrames else 0f
        
    val runningTime: Long
        get() = System.currentTimeMillis() - startTime
}

/**
 * Remote View 命令
 */
sealed class RemoteViewCommand {
    object Start : RemoteViewCommand()
    object Stop : RemoteViewCommand()
    object Pause : RemoteViewCommand()
    object Resume : RemoteViewCommand()
    data class UpdateConfig(val config: RemoteViewConfig) : RemoteViewCommand()
    data class SetBucketPath(val bucketPath: String) : RemoteViewCommand()
}

/**
 * Remote View 事件
 */
sealed class RemoteViewEvent {
    object Started : RemoteViewEvent()
    object Stopped : RemoteViewEvent()
    object Paused : RemoteViewEvent()
    object Resumed : RemoteViewEvent()
    data class Error(val message: String, val throwable: Throwable? = null) : RemoteViewEvent()
    data class WebSocketConnected(val url: String) : RemoteViewEvent()
    data class WebSocketDisconnected(val reason: String) : RemoteViewEvent()
    data class FrameCaptured(val stats: RemoteViewPerformanceStats) : RemoteViewEvent()
    data class ConfigUpdated(val config: RemoteViewConfig) : RemoteViewEvent()
}
