package com.dspread.mdm.service.network.https

import com.dspread.mdm.service.platform.monitor.NetworkTrafficMonitor
import com.dspread.mdm.service.platform.api.network.NetworkApi
import com.dspread.mdm.service.utils.log.Logger
import android.content.Context
import java.io.*
import java.net.URL
import java.security.MessageDigest
import java.math.BigInteger
import javax.net.ssl.HttpsURLConnection
import java.util.*

/**
 * HTTP文件下载器
 * 保持原有的稳定性和可靠性
 */
object HttpDownloader {

    private const val TAG = "HttpDownloader"
    private const val DOWNLOAD_RETRY = 5 // S3支持断点续传，可以安全重试
    private const val CONNECTION_TIMEOUT = 30 * 1000 // 连接超时
    private const val SO_TIMEOUT = 60 * 1000 // 读取超时
    private const val BUFFER_SIZE = 4 * 1024 // 缓冲区大小
    private const val STORAGE_OVER_FLOW = "STORAGE_OVER_FLOW" // 存储溢出标识
    private const val MIN_RETRY_DELAY = 2000L // 最小重试延迟(毫秒)
    private const val MAX_RETRY_DELAY = 30000L // 最大重试延迟(毫秒)
    private const val NETWORK_CHECK_TIMEOUT = 5000 // 网络检测超时(毫秒)
    private const val MAX_RETRY_TRAFFIC_RATIO = 1.2 // S3断点续传环境下的流量保护比例
    
    /**
     * 下载回调接口
     */
    interface FileDownloadCallBack {
        fun requestSuccess()
        fun requestFail(errorCode: Int, errorStr: String)
        fun onDownloading(curFileSize: Long, fileSize: Long)
    }
    
    /**
     * 带重试的文件下载
     */
    fun fileDownloadByUrlWithRetry(
        fileUrl: String,
        filePath: String,
        fileLength: Long,
        md5: String,
        callBackWithRetry: FileDownloadCallBack,
        context: Context? = null
    ): Boolean {
        
        var isDownloadSuccess = false
        var lastErrorCode = 0
        var isStorageOverflow = false
        var totalTrafficUsed = 0L // 累计使用流量
        
        // 重试循环，增加递增延迟
        for (i in 0 until DOWNLOAD_RETRY) {
            if (isDownloadSuccess) {
                // 下载成功，退出循环
                try {
                    callBackWithRetry.requestSuccess()
                } catch (e: Exception) {
                    Logger.httpsE("$TAG 回调成功异常", e)
                }
                return true
            } else if (isStorageOverflow) {
                // 内部存储空间不足，退出循环
                try {
                    callBackWithRetry.requestFail(0, STORAGE_OVER_FLOW)
                } catch (e: Exception) {
                    Logger.httpsE("$TAG 回调存储溢出异常", e)
                }
                return false
            }

            // 重试前等待，递增延迟策略
            if (i > 0) {
                val retryDelay = minOf(MIN_RETRY_DELAY * (1 shl (i - 1)), MAX_RETRY_DELAY)
                Logger.https("$TAG 第${i + 1}次重试前等待 ${retryDelay}ms")
                try {
                    Thread.sleep(retryDelay)
                } catch (e: InterruptedException) {
                    Logger.httpsE("$TAG 重试延迟被中断", e)
                    Thread.currentThread().interrupt()
                    return false
                }

                // 重试前检查网络状态
                if (context != null && !NetworkApi.isNetworkAvailable(context)) {
                    Logger.httpsE("$TAG 网络不可用，跳过第${i + 1}次重试")
                    continue
                }

                // 流量保护：检查是否超过流量限制
                if (fileLength > 0 && totalTrafficUsed > fileLength * MAX_RETRY_TRAFFIC_RATIO) {
                    Logger.httpsE("$TAG 流量保护：已使用${totalTrafficUsed}字节，超过限制(${fileLength * MAX_RETRY_TRAFFIC_RATIO}字节)，停止重试")
                    callBackWithRetry.requestFail(-1, "流量保护：重试流量超限")
                    return false
                }
            }

            Logger.https("$TAG 尝试下载，第${i + 1}次")

            // 记录本次重试前的文件大小
            val fileSizeBeforeRetry = File(filePath).length()

            // 调用核心下载方法
            isDownloadSuccess = fileDownloadByUrl(fileUrl, filePath, fileLength, md5, object : FileDownloadCallBack {
                override fun requestSuccess() {
                    // 内部回调，不处理
                }
                
                override fun requestFail(errorCode: Int, errorStr: String) {
                    // 记录错误信息
                    lastErrorCode = errorCode
                    if (STORAGE_OVER_FLOW == errorStr) {
                        isStorageOverflow = true
                    }
                    Logger.httpsE("$TAG 下载失败: $errorCode $errorStr")

                    // 统计本次重试消耗的流量
                    val fileSizeAfterRetry = File(filePath).length()
                    val trafficUsedThisRetry = maxOf(0, fileSizeAfterRetry - fileSizeBeforeRetry)
                    totalTrafficUsed += trafficUsedThisRetry
                    Logger.https("$TAG 本次重试流量: ${trafficUsedThisRetry}字节, 累计流量: ${totalTrafficUsed}字节")
                }
                
                override fun onDownloading(curFileSize: Long, fileSize: Long) {
                    // 透传进度回调
                    callBackWithRetry.onDownloading(curFileSize, fileSize)
                }
            })
        }
        
        // 所有重试都失败
        if (!isDownloadSuccess) {
            Logger.httpsE("$TAG 下载失败，错误码: $lastErrorCode")
            callBackWithRetry.requestFail(lastErrorCode, "file download failed.")
        } else {
            try {
                callBackWithRetry.requestSuccess()
            } catch (e: Exception) {
                Logger.httpsE("$TAG 回调成功异常", e)
            }
        }
        
        return isDownloadSuccess
    }
    
    /**
     * 核心文件下载方法
     */
    private fun fileDownloadByUrl(
        fileUrl: String,
        filePath: String,
        allFileLength: Long,
        md5: String,
        callBack: FileDownloadCallBack?
    ): Boolean {
        
        Logger.https("$TAG 开始下载")
        Logger.https("$TAG URL: $fileUrl")
        Logger.https("$TAG 路径: $filePath")
        
        val cleanUrl = fileUrl.replace(" ", "%20")
        
        var urlConnection: HttpsURLConnection? = null
        var inputStream: InputStream? = null
        var outputStream: FileOutputStream? = null
        var isDownLoadSucc = false
        
        try {
            // 保存文件到本地
            val localFile = File(filePath)
            
            // 路径安全检查
            try {
                val canonicalPath = localFile.canonicalPath
                // 这里可以添加路径安全检查
            } catch (e: IOException) {
                Logger.httpsE("$TAG 路径检查异常", e)
            }
            
            if (!localFile.exists()) {
                Logger.https("$TAG 本地文件不存在，创建文件")
                if (!localFile.parentFile?.exists()!!) {
                    localFile.parentFile?.mkdirs()
                }
                localFile.createNewFile()
            }
            
            val curFileLength = localFile.length()
            
            // 判断文件是否已下载完成
            when {
                allFileLength > 0 && curFileLength > allFileLength -> {
                    callBack?.requestFail(0, "curFileLen:$curFileLength > serverFileSize:$allFileLength. download file failed")
                    localFile.delete() // 文件大小大于总文件，删除文件
                    return false
                }
                allFileLength > 0 && curFileLength == allFileLength -> {
                    // 文件下载成功，MD5校验
                    val localFileMd5 = getMd5ByFile(localFile)
                    if (md5.equals(localFileMd5, ignoreCase = true)) {
                        val cal = Calendar.getInstance()
                        localFile.setLastModified(cal.timeInMillis)
            
                        return true
                    } else {
                        localFile.delete()
                        callBack?.requestFail(0, "file md5 check failed. 3")
                        return false
                    }
                }
                allFileLength > 0 && isStorageOverflow(allFileLength) -> {
                    // 内存预留值检测
                    callBack?.requestFail(0, STORAGE_OVER_FLOW)
                    return false
                }
            }

            // 如果allFileLength为0，说明调用方没有提供文件大小，需要从服务器获取
            Logger.https("$TAG allFileLength=$allFileLength, 需要从服务器获取文件大小")
            
            // 建立连接
            val url = URL(cleanUrl)
            urlConnection = url.openConnection() as HttpsURLConnection
            
            // SSL配置
            // 这里可以根据需要添加SSL配置
            
            // 设置超时时间
            urlConnection.connectTimeout = CONNECTION_TIMEOUT
            urlConnection.readTimeout = SO_TIMEOUT
            urlConnection.requestMethod = "GET"
            
            // 设置请求头
            urlConnection.setRequestProperty("User-Agent", "Mozilla/4.0 (compatible; MSIE 7.0; Windows NT 5.1)")
            urlConnection.setRequestProperty("Connection", "Keep-Alive")

            // 断点续传支持：如果本地文件已存在且大小合理，设置Range头
            var isUsingRange = false
            if (curFileLength > 0 && allFileLength > 0 && curFileLength < allFileLength) {
                urlConnection.setRequestProperty("Range", "bytes=$curFileLength-")
                isUsingRange = true
                Logger.https("$TAG 设置断点续传: Range=bytes=$curFileLength-")
            }

            // 只有在文件已存在且有内容时才使用断点续传
            if (curFileLength > 0) {
                urlConnection.setRequestProperty("Range", "bytes=$curFileLength-")
                Logger.https("$TAG 使用断点续传，从字节 $curFileLength 开始")
            }

            val returnCode = urlConnection.responseCode
            Logger.https("$TAG HTTP响应码: $returnCode")

            if (returnCode != HttpsURLConnection.HTTP_PARTIAL && returnCode != HttpsURLConnection.HTTP_OK) {
                val downFailInfo = "http error,response code:$returnCode"
                localFile.delete()
                Logger.httpsE("$TAG $downFailInfo")
                callBack?.requestFail(returnCode, "server response fail")
                return false
            }

            val serverContentLength = urlConnection.contentLength.toLong()
            Logger.https("$TAG 服务器返回Content-Length: $serverContentLength")

            // S3断点续传状态检查和验证
            val isResumeSuccessful = if (curFileLength > 0) {
                when (returnCode) {
                    HttpsURLConnection.HTTP_PARTIAL -> {
                        Logger.https("$TAG ✅ S3断点续传成功: 206 Partial Content")
                        true
                    }
                    HttpsURLConnection.HTTP_OK -> {
                        Logger.httpsW("$TAG ⚠️ S3返回200而非206，服务器不支持Range或重新下载完整文件")
                        // 检查Content-Length是否等于完整文件大小
                        if (allFileLength > 0 && serverContentLength == allFileLength) {
                            Logger.httpsW("$TAG ⚠️ 服务器重新发送完整文件，删除本地部分文件重新下载")
                            localFile.delete()
                            false
                        } else {
                            Logger.httpsW("$TAG ⚠️ 无法确定是否断点续传，继续尝试")
                            false
                        }
                    }
                    else -> false
                }
            } else {
                Logger.https("$TAG 完整下载模式")
                true
            }

            // 如果断点续传失败，重新计算文件大小
            val adjustedCurFileLength = if (!isResumeSuccessful && curFileLength > 0) {
                Logger.https("$TAG 断点续传失败，重新计算文件大小")
                if (localFile.exists()) localFile.length() else 0L
            } else {
                curFileLength
            }

            // 计算实际的总文件大小
            val actualFileLength = when {
                allFileLength > 0 -> {
                    // 调用方提供了文件大小，直接使用
                    Logger.https("$TAG 使用调用方提供的文件大小: $allFileLength")
                    allFileLength
                }
                curFileLength > 0 && serverContentLength > 0 -> {
                    // 断点续传情况：总大小 = 已下载 + 服务器返回的剩余大小
                    val totalSize = curFileLength + serverContentLength
                    Logger.https("$TAG 断点续传计算总大小: $curFileLength + $serverContentLength = $totalSize")
                    totalSize
                }
                serverContentLength > 0 -> {
                    // 完整下载：使用服务器返回的大小
                    Logger.https("$TAG 完整下载，使用服务器返回大小: $serverContentLength")
                    serverContentLength
                }
                else -> {
                    Logger.https("$TAG 服务器未返回Content-Length，将流式下载")
                    -1L // 表示未知大小，流式下载
                }
            }

            // 获取服务器的输入流，获取数据
            inputStream = BufferedInputStream(urlConnection.inputStream)

            // 根据是否断点续传选择文件写入模式
            val useAppendMode = isResumeSuccessful && adjustedCurFileLength > 0
            Logger.https("$TAG localFile = ${localFile.exists()} ${localFile.path}, 追加模式: $useAppendMode")
            outputStream = FileOutputStream(localFile, useAppendMode)
            
            // 读取buffer
            val buffer = ByteArray(BUFFER_SIZE)
            var len: Int
            var currentFileLength = adjustedCurFileLength
            var lastReportedProgress = -1 // 上次上报的进度百分比

            // 读取服务器数据
            while (inputStream.read(buffer).also { len = it } != -1) {
                // 保存文件到本地
                outputStream.write(buffer, 0, len)
                // 同步到文件系统
                outputStream.fd.sync()

                // 统计HTTP下载流量
                NetworkTrafficMonitor.recordHttpDownload(len.toLong())

                // 更新当前文件长度
                currentFileLength += len
                val progressFileLength = if (actualFileLength > 0) actualFileLength else currentFileLength

                // 控制进度回调频率，避免过于频繁的回调
                if (actualFileLength > 0) {
                    val currentProgress = ((currentFileLength * 100) / actualFileLength).toInt()
                    // 只在进度变化且为5的倍数时回调，避免每次buffer读取都回调
                    if (currentProgress != lastReportedProgress && (currentProgress % 5 == 0 || currentProgress == 100)) {
                        lastReportedProgress = currentProgress
                        callBack?.onDownloading(currentFileLength, progressFileLength)
                    }
                } else {
                    // 对于未知大小的文件，每64KB回调一次（16次buffer读取）
                    if ((currentFileLength / (64 * 1024)) != ((currentFileLength - len) / (64 * 1024))) {
                        callBack?.onDownloading(currentFileLength, progressFileLength)
                    }
                }
            }
            outputStream.flush()


            // 文件下载成功，进行校验
            val finalFileLength = if (actualFileLength > 0) actualFileLength else currentFileLength
            if (!isFileDownloadSuccessed(filePath, finalFileLength, md5)) {
                localFile.delete() // 下载文件错误，删除文件
                callBack?.requestFail(0, "download file failed")
                return false
            }
            Logger.https("$TAG MD5校验正确")
            isDownLoadSucc = true
            
        } catch (e: Exception) {
            isDownLoadSucc = false
            val downFailInfo = "download throw Exception e = $e"
            Logger.httpsE("$TAG $downFailInfo", e)
            
        } finally {
            Logger.https("$TAG 清理连接资源")
            try {
                inputStream?.close()
                outputStream?.close()
                urlConnection?.disconnect()
            } catch (e: Exception) {
                Logger.httpsE("$TAG 清理资源异常", e)
            }
        }
        
        return isDownLoadSucc
    }
    
    /**
     * 检查存储空间是否溢出
     */
    private fun isStorageOverflow(fileLength: Long): Boolean {
        // 这里可以实现具体的存储空间检查逻辑
        // 暂时返回false
        return false
    }
    
    /**
     * 检查文件下载是否成功
     */
    private fun isFileDownloadSuccessed(fileName: String, serverFileSize: Long, serverFileMd5: String): Boolean {
        val file = File(fileName)
        if (!file.exists()) {
            Logger.httpsE("$TAG 文件不存在: $fileName")
            return false
        }
        if (file.length() != serverFileSize) {
            Logger.httpsE("$TAG 文件大小校验失败: ${file.length()} != $serverFileSize")
            return false
        }
        val curFileMd5 = getMd5ByFile(file)
        if (!curFileMd5.equals(serverFileMd5, ignoreCase = true)) {
            Logger.httpsE("$TAG MD5校验失败: $curFileMd5 != $serverFileMd5")
            return false
        }
        return true
    }
    
    /**
     * 获取文件MD5
     */
    private fun getMd5ByFile(file: File): String {
        var value = ""
        var inputStream: FileInputStream? = null
        
        try {
            Logger.https("$TAG 计算MD5: ${file.name}")
            inputStream = FileInputStream(file)
            val md5 = MessageDigest.getInstance("MD5")
            val buffer = ByteArray(8 * 1024)
            var length: Int
            
            while (inputStream.read(buffer).also { length = it } != -1) {
                md5.update(buffer, 0, length)
            }
            
            val bi = BigInteger(1, md5.digest())
            value = bi.toString(16) // 16进制
            
            // 补齐32位
            while (value.length < 32) {
                value = "0$value"
            }
            
        } catch (e: Exception) {
            Logger.httpsE("$TAG 计算MD5异常", e)
        } finally {
            try {
                inputStream?.close()
            } catch (e: Exception) {
                Logger.httpsE("$TAG 关闭文件流异常", e)
            }
        }
        
        Logger.https("$TAG 文件: ${file.length()} MD5: $value")
        return value
    }


}
