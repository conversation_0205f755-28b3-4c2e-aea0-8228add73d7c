package com.dspread.mdm.service.platform.api.network

import android.annotation.SuppressLint
import android.content.Context
import android.net.wifi.WifiConfiguration
import android.net.wifi.WifiManager
import android.os.Build
import com.dspread.mdm.service.platform.api.model.WiFiConnectionInfo
import com.dspread.mdm.service.platform.api.model.WiFiScanResult
import com.dspread.mdm.service.utils.log.Logger
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

/**
 * WiFi系统API封装类
 * 提供统一的WiFi管理接口，封装Android系统API调用
 *
 * 使用单例模式，避免重复实例化和资源浪费
 */
class WiFiManagerApi(private val context: Context) {

    companion object {
        private const val TAG = "[WiFiManagerApi]"

        @Volatile
        private var INSTANCE: WiFiManagerApi? = null

        /**
         * 获取单例实例
         */
        private fun getInstance(context: Context): WiFiManagerApi {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: WiFiManagerApi(context.applicationContext).also {
                    INSTANCE = it
                    Logger.platform("$TAG WiFiManagerApi 单例实例已创建")
                }
            }
        }

        // ==================== 静态方法接口 ====================

        /**
         * 获取当前连接的WiFi SSID
         */
        fun getCurConnectSSID(context: Context): String? {
            return getInstance(context).getCurConnectSSID()
        }

        /**
         * 获取WiFi IP地址
         */
        fun getWifiIP(context: Context): String? {
            return getInstance(context).getWifiIP()
        }

        /**
         * 检查WiFi是否启用
         */
        fun isWifiEnabled(context: Context): Boolean {
            return try {
                getInstance(context).wifiManager.isWifiEnabled
            } catch (e: Exception) {
                false
            }
        }

        /**
         * 启用WiFi
         */
        fun enableWifi(context: Context): Boolean {
            return try {
                getInstance(context).wifiManager.isWifiEnabled = true
                true
            } catch (e: Exception) {
                false
            }
        }

        /**
         * 禁用WiFi
         */
        fun disableWifi(context: Context): Boolean {
            return try {
                getInstance(context).wifiManager.isWifiEnabled = false
                true
            } catch (e: Exception) {
                false
            }
        }
    }
    
    private val wifiManager = context.applicationContext.getSystemService(Context.WIFI_SERVICE) as WifiManager
    
    // ==================== 网络配置管理 ====================
    
    suspend fun addNetwork(config: WifiConfiguration): Result<Int> = withContext(Dispatchers.IO) {
        try {
            val networkId = wifiManager.addNetwork(config)
            if (networkId != -1) {
                Logger.platform("$TAG 网络添加成功: ${config.SSID}, networkId=$networkId")
                Result.success(networkId)
            } else {
                Logger.platformE("$TAG 网络添加失败: ${config.SSID}")
                Result.failure(Exception("Failed to add network: ${config.SSID}"))
            }
        } catch (e: Exception) {
            Logger.platformE("$TAG 网络添加异常: ${config.SSID}", e)
            Result.failure(e)
        }
    }
    
    suspend fun updateNetwork(config: WifiConfiguration): Result<Int> = withContext(Dispatchers.IO) {
        try {
            val result = wifiManager.updateNetwork(config)
            if (result != -1) {
                Logger.platform("$TAG 网络更新成功: ${config.SSID}, networkId=${config.networkId}")
                Result.success(config.networkId)
            } else {
                Logger.platformE("$TAG 网络更新失败: ${config.SSID}, networkId=${config.networkId}")
                Result.failure(Exception("Failed to update network: ${config.SSID}"))
            }
        } catch (e: Exception) {
            Logger.platformE("$TAG 网络更新异常: ${config.SSID}", e)
            Result.failure(e)
        }
    }
    
    suspend fun removeNetwork(networkId: Int): Result<Boolean> = withContext(Dispatchers.IO) {
        try {
            val removed = wifiManager.removeNetwork(networkId)
            if (removed) {
                Logger.platform("$TAG 网络移除成功: networkId=$networkId")
            } else {
                Logger.platformE("$TAG 网络移除失败: networkId=$networkId")
            }
            Result.success(removed)
        } catch (e: Exception) {
            Logger.platformE("$TAG 网络移除异常: networkId=$networkId", e)
            Result.failure(e)
        }
    }
    
    suspend fun enableNetwork(networkId: Int, disableOthers: Boolean = true): Result<Boolean> = withContext(Dispatchers.IO) {
        try {
            val enabled = wifiManager.enableNetwork(networkId, disableOthers)
            if (enabled) {
                Logger.platform("$TAG 网络启用成功: networkId=$networkId, disableOthers=$disableOthers")
            } else {
                Logger.platformE("$TAG 网络启用失败: networkId=$networkId")
            }
            Result.success(enabled)
        } catch (e: Exception) {
            Logger.platformE("$TAG 网络启用异常: networkId=$networkId", e)
            Result.failure(e)
        }
    }

    suspend fun disableNetwork(networkId: Int): Result<Boolean> = withContext(Dispatchers.IO) {
        try {
            val disabled = wifiManager.disableNetwork(networkId)
            if (disabled) {
                Logger.platform("$TAG 网络禁用成功: networkId=$networkId")
            } else {
                Logger.platformE("$TAG 网络禁用失败: networkId=$networkId")
            }
            Result.success(disabled)
        } catch (e: Exception) {
            Logger.platformE("$TAG 网络禁用异常: networkId=$networkId", e)
            Result.failure(e)
        }
    }

    suspend fun disconnect(): Result<Boolean> = withContext(Dispatchers.IO) {
        try {
            val disconnected = wifiManager.disconnect()
            if (disconnected) {
                Logger.platform("$TAG WiFi断开连接成功")
            } else {
                Logger.platformE("$TAG WiFi断开连接失败")
            }
            Result.success(disconnected)
        } catch (e: Exception) {
            Logger.platformE("$TAG WiFi断开连接异常", e)
            Result.failure(e)
        }
    }
    
    // ==================== 网络状态查询 ====================
    
    @SuppressLint("MissingPermission")
    suspend fun getConfiguredNetworks(): Result<List<WifiConfiguration>> = withContext(Dispatchers.IO) {
        try {
            val networks = wifiManager.configuredNetworks ?: emptyList()
            Logger.platform("$TAG 获取已配置网络: ${networks.size} 个")
            Result.success(networks)
        } catch (e: Exception) {
            Logger.platformE("$TAG 获取已配置网络异常", e)
            Result.failure(e)
        }
    }
    
    suspend fun getCurrentWifiInfo(): Result<WiFiConnectionInfo?> = withContext(Dispatchers.IO) {
        try {
            val wifiInfo = wifiManager.connectionInfo
            val connectionInfo = wifiInfo?.let {
                WiFiConnectionInfo(
                    ssid = it.ssid?.replace("\"", ""),
                    networkId = it.networkId,
                    rssi = it.rssi,
                    linkSpeed = it.linkSpeed,
                    frequency = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) it.frequency else 0
                )
            }
            Result.success(connectionInfo)
        } catch (e: Exception) {
            Logger.platformE("$TAG 获取当前WiFi信息异常", e)
            Result.failure(e)
        }
    }
    
    suspend fun isWifiEnabled(): Result<Boolean> = withContext(Dispatchers.IO) {
        try {
            val enabled = wifiManager.isWifiEnabled
            Result.success(enabled)
        } catch (e: Exception) {
            Logger.platformE("$TAG 检查WiFi状态异常", e)
            Result.failure(e)
        }
    }
    
    suspend fun isWifiConnected(): Result<Boolean> = withContext(Dispatchers.IO) {
        try {
            val wifiInfo = wifiManager.connectionInfo
            val connected = wifiInfo != null && wifiInfo.networkId != -1
            Result.success(connected)
        } catch (e: Exception) {
            Logger.platformE("$TAG 检查WiFi连接状态异常", e)
            Result.failure(e)
        }
    }
    
    // ==================== 网络扫描 ====================
    
    suspend fun startScan(): Result<Boolean> = withContext(Dispatchers.IO) {
        try {
            if (!wifiManager.isWifiEnabled) {
                Logger.platformW("$TAG WiFi未启用，无法扫描")
                return@withContext Result.failure(Exception("WiFi not enabled"))
            }
            
            val scanStarted = wifiManager.startScan()
            if (scanStarted) {
                Logger.platform("$TAG WiFi扫描启动成功")
            } else {
                Logger.platformW("$TAG WiFi扫描启动失败")
            }
            Result.success(scanStarted)
        } catch (e: Exception) {
            Logger.platformE("$TAG WiFi扫描启动异常", e)
            Result.failure(e)
        }
    }
    
    @SuppressLint("MissingPermission")
    suspend fun getScanResults(): Result<List<WiFiScanResult>> = withContext(Dispatchers.IO) {
        try {
            val scanResults = wifiManager.scanResults ?: emptyList()
            val results = scanResults.map { result ->
                WiFiScanResult(
                    ssid = result.SSID,
                    bssid = result.BSSID,
                    level = result.level,
                    frequency = result.frequency,
                    capabilities = result.capabilities
                )
            }
            Logger.platform("$TAG 获取扫描结果: ${results.size} 个")
            Result.success(results)
        } catch (e: Exception) {
            Logger.platformE("$TAG 获取扫描结果异常", e)
            Result.failure(e)
        }
    }
    
    // ==================== 系统控制 ====================
    
    @SuppressLint("MissingPermission")
    suspend fun setWifiEnabled(enabled: Boolean): Result<Boolean> = withContext(Dispatchers.IO) {
        try {
            @Suppress("DEPRECATION")
            wifiManager.isWifiEnabled = enabled
            Logger.platform("$TAG WiFi状态设置: $enabled")
            Result.success(true)
        } catch (e: Exception) {
            Logger.platformE("$TAG 设置WiFi状态异常", e)
            Result.failure(e)
        }
    }
    
    suspend fun reconnect(): Result<Boolean> = withContext(Dispatchers.IO) {
        try {
            val result = wifiManager.reconnect()
            if (result) {
                Logger.platform("$TAG WiFi重连成功")
            } else {
                Logger.platformE("$TAG WiFi重连失败")
            }
            Result.success(result)
        } catch (e: Exception) {
            Logger.platformE("$TAG WiFi重连异常", e)
            Result.failure(e)
        }
    }
    
    @SuppressLint("MissingPermission")
    suspend fun saveConfiguration(): Result<Boolean> = withContext(Dispatchers.IO) {
        try {
            val result = wifiManager.saveConfiguration()
            if (result) {
                Logger.platform("$TAG WiFi配置保存成功")
            } else {
                Logger.platformE("$TAG WiFi配置保存失败")
            }
            Result.success(result)
        } catch (e: Exception) {
            Logger.platformE("$TAG WiFi配置保存异常", e)
            Result.failure(e)
        }
    }
    
    // ==================== 便捷方法 ====================
    
    suspend fun findNetworkBySSID(ssid: String): Result<WifiConfiguration?> = withContext(Dispatchers.IO) {
        try {
            val networks = getConfiguredNetworks().getOrNull() ?: emptyList()
            val targetSsid = "\"$ssid\""
            val network = networks.find { it.SSID == targetSsid }
            Result.success(network)
        } catch (e: Exception) {
            Logger.platformE("$TAG 查找网络异常: $ssid", e)
            Result.failure(e)
        }
    }
    
    suspend fun getSignalStrength(): Result<Int> = withContext(Dispatchers.IO) {
        try {
            val wifiInfo = wifiManager.connectionInfo
            val signalLevel = WifiManager.calculateSignalLevel(wifiInfo.rssi, 5)
            Result.success(signalLevel)
        } catch (e: Exception) {
            Logger.platformE("$TAG 获取信号强度异常", e)
            Result.failure(e)
        }
    }

    /**
     * 获取当前连接的WiFi IP地址
     */
    fun getWifiIP(): String {
        return try {
            val wifiInfo = wifiManager.connectionInfo
            val ipAddress = wifiInfo.ipAddress
            String.format("%d.%d.%d.%d",
                ipAddress and 0xff,
                ipAddress shr 8 and 0xff,
                ipAddress shr 16 and 0xff,
                ipAddress shr 24 and 0xff)
        } catch (e: Exception) {
            Logger.platformE("$TAG 获取WiFi IP地址异常", e)
            ""
        }
    }

    /**
     * 获取当前连接的WiFi SSID
     */
    fun getCurConnectSSID(): String {
        return try {
            val wifiInfo = wifiManager.connectionInfo
            wifiInfo.ssid?.replace("\"", "") ?: ""
        } catch (e: Exception) {
            Logger.platformE("$TAG 获取当前SSID异常", e)
            ""
        }
    }
}
