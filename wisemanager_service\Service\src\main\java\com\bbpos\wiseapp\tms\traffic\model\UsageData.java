package com.bbpos.wiseapp.tms.traffic.model;

import android.database.Cursor;

public class UsageData extends BaseDataModel{
	public static final String DATA_DATE = "dataDate"; // pk
	public static final String PKG_NAME = "pkgName"; // pk
	public static final String VERSION_CODE = "versionCode";//pk 
	public static final String VERSION_NAME = "versionName";
	/** 使用次数 */
	public static final String LAUNCK_COUNT = "launchCount";
	/** 使用时长 */
	public static final String TOTAL_TIME_IN_FOREGROUND = "totalTimeInForeground";
	
	public String dataDate;
	public String pkgName;
	public int versionCode;
	public String versionName;
	public int launchCount;
	public long totalTimeInForeground;

	public UsageData(Cursor cursor) {
		this.pkgName = getCursorString(cursor,PKG_NAME);
		this.dataDate = getCursorString(cursor,DATA_DATE);
		this.versionCode = getCursorInt(cursor, VERSION_CODE);
		this.versionName = getCursorString(cursor, VERSION_NAME);
		this.launchCount = getCursorInt(cursor, LAUNCK_COUNT);
		this.totalTimeInForeground = getCursorLong(cursor, TOTAL_TIME_IN_FOREGROUND);
	}

	public UsageData(String dataDate, String pkgName, int versionCode,String versionName,
			int launchCount, long totalTimeInForeground) {
		super();
		this.dataDate = dataDate;
		this.pkgName = pkgName;
		this.versionCode = versionCode;
		this.versionName = versionName;
		this.launchCount = launchCount;
		this.totalTimeInForeground = totalTimeInForeground;
	}
}
