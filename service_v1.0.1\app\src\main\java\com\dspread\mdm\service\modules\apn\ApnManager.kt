package com.dspread.mdm.service.modules.apn

import android.annotation.SuppressLint
import android.content.ContentValues
import android.content.Context
import android.net.ConnectivityManager
import android.net.Network
import android.net.NetworkCapabilities
import android.net.NetworkRequest
import android.net.Uri
import android.os.Build
import android.telephony.SubscriptionInfo
import android.telephony.SubscriptionManager
import androidx.annotation.RequiresApi
import com.dspread.mdm.service.modules.BaseModuleManager
import com.dspread.mdm.service.modules.apn.model.ApnConfig
import com.dspread.mdm.service.modules.apn.model.ApnOperationResult
import com.dspread.mdm.service.modules.apn.model.ApnStatistics
import com.dspread.mdm.service.modules.apn.model.ApnValidationResult
import com.dspread.mdm.service.modules.apn.model.CarrierInfo
import com.dspread.mdm.service.modules.apn.model.DataUsage
import com.dspread.mdm.service.modules.apn.model.NetworkStatus
import com.dspread.mdm.service.utils.log.Logger
import kotlinx.coroutines.*
import org.json.JSONArray
import java.util.concurrent.atomic.AtomicReference

/**
 * APN管理器
 * 负责APN配置、运营商检测、网络监控的整体协调和管理
 */
@RequiresApi(api = Build.VERSION_CODES.N)
class ApnManager(
    private val context: Context
) : BaseModuleManager() {
    
    companion object {
        private const val TAG = "[ApnManager]"
        private const val DEFAULT_VALIDATION_INTERVAL = 300000L // 5分钟
    }
    
    // 核心组件
    private val carrierDetector = CarrierDetector(context)
    private val apnConfigManager = ApnConfigManager(context)
    private val networkStatusMonitor by lazy { NetworkStatusMonitor(context) }
    
    // 状态管理
    private val currentCarriers = AtomicReference<List<CarrierInfo>>(emptyList())
    private val currentApnConfigs = AtomicReference<List<ApnConfig>>(emptyList())
    private val currentNetworkStatus = AtomicReference<NetworkStatus>()
    
    // 任务管理
    private var validationJob: Job? = null

    // 统计信息
    private val statistics = ApnStatistics()
    
    override fun getModuleName(): String = "APN Manager"
    
    override suspend fun onInitialize(): Result<Unit> {
        return try {
            Logger.apnI("$TAG 初始化APN管理器")

            // 检查组件可用性
            if (!carrierDetector.isAvailable()) {
                Logger.apnW("$TAG 运营商检测器不可用")
            }

            if (!apnConfigManager.isAvailable()) {
                Logger.apnW("$TAG APN配置管理器不可用")
            }

            if (!networkStatusMonitor.isAvailable()) {
                Logger.apnW("$TAG 网络状态监控器不可用")
            }

            // 初始化运营商信息
            refreshCarrierInfo()

            // 初始化APN配置
            refreshApnConfigs()

            Logger.apnI("$TAG APN管理器初始化完成")
            Result.success(Unit)
        } catch (e: Exception) {
            Logger.apnE("$TAG APN管理器初始化失败", e)
            Result.failure(e)
        }
    }
    
    override suspend fun onStart(): Result<Unit> {
        return try {
            Logger.apnI("$TAG 启动APN管理服务")

            // 启动网络状态监控
            startNetworkMonitoring()

            // 启动定期验证任务
            startValidationTask()

            Logger.apnI("$TAG APN管理服务启动完成")
            Result.success(Unit)
        } catch (e: Exception) {
            Logger.apnE("$TAG APN管理服务启动失败", e)
            Result.failure(e)
        }
    }

    override suspend fun onStop(): Result<Unit> {
        return try {
            Logger.apnI("$TAG 停止APN管理服务")

            // 停止网络状态监控
            stopNetworkMonitoring()

            // 停止验证任务
            stopValidationTask()

            Logger.apnI("$TAG APN管理服务停止完成")
            Result.success(Unit)
        } catch (e: Exception) {
            Logger.apnE("$TAG APN管理服务停止失败", e)
            Result.failure(e)
        }
    }
    
    /**
     * 更新APN配置列表
     */
    suspend fun updateApnConfigs(configs: List<ApnConfig>): Result<Unit> {
        return try {
            Logger.apnI("$TAG 更新APN配置列表，数量: ${configs.size}")
            
            val results = mutableListOf<ApnOperationResult>()
            
            configs.forEach { config ->
                try {
                    val result = apnConfigManager.addApnConfig(config)
                    if (result.isSuccess) {
                        results.add(result.getOrNull()!!)
                        statistics.recordConfiguration()
                    }
                } catch (e: Exception) {
                    Logger.apnE("$TAG 添加APN配置失败: ${config.name}", e)
                }
            }
            
            // 刷新当前配置
            refreshApnConfigs()
            
            Logger.apnI("$TAG APN配置更新完成，成功: ${results.count { it.success }}, 失败: ${results.count { !it.success }}")
            Result.success(Unit)
            
        } catch (e: Exception) {
            Logger.apnE("$TAG 更新APN配置失败", e)
            Result.failure(e)
        }
    }
    
    /**
     * 设置默认APN
     */
    suspend fun setDefaultApn(apnId: Long, simSlot: Int): Result<ApnOperationResult> {
        return try {
            Logger.apnI("$TAG 设置默认APN: ID=$apnId, SIM槽位=$simSlot")
            
            val result = apnConfigManager.setDefaultApn(apnId, simSlot)
            
            if (result.isSuccess) {
                statistics.recordConfiguration()
                refreshApnConfigs()
            }
            
            result
        } catch (e: Exception) {
            Logger.apnE("$TAG 设置默认APN失败", e)
            Result.failure(e)
        }
    }
    
    /**
     * 启用APN
     */
    suspend fun enableApn(apnId: Long): Result<ApnOperationResult> = withContext(Dispatchers.IO) {
        try {
            Logger.apnI("$TAG 启用APN: ID=$apnId")

            if (apnId <= 0) {
                Logger.apnW("$TAG APN ID无效: $apnId")
                return@withContext Result.success(ApnOperationResult(
                    success = false,
                    apnId = apnId,
                    message = "APN ID无效",
                    errorCode = -1
                ))
            }

            // 修改APN配置的carrier_enabled字段为1（启用）
            val uri = Uri.parse("content://telephony/carriers/$apnId")
            val values = ContentValues().apply {
                put("carrier_enabled", 1)
            }

            val affectedRows = context.contentResolver.update(uri, values, null, null)

            if (affectedRows > 0) {
                Logger.apnI("$TAG APN启用成功: ID=$apnId, 影响行数=$affectedRows")
                val result = ApnOperationResult(
                    success = true,
                    apnId = apnId,
                    message = "APN启用成功",
                    affectedRows = affectedRows
                )
                Result.success(result)
            } else {
                Logger.apnW("$TAG APN启用失败: ID=$apnId, 未找到对应的APN记录")
                val result = ApnOperationResult(
                    success = false,
                    apnId = apnId,
                    message = "未找到对应的APN记录",
                    errorCode = -2
                )
                Result.success(result)
            }
        } catch (e: Exception) {
            Logger.apnE("$TAG 启用APN失败: ID=$apnId", e)
            Result.failure(e)
        }
    }
    
    /**
     * 禁用APN
     */
    suspend fun disableApn(apnId: Long): Result<ApnOperationResult> = withContext(Dispatchers.IO) {
        try {
            Logger.apnI("$TAG 禁用APN: ID=$apnId")

            if (apnId <= 0) {
                Logger.apnW("$TAG APN ID无效: $apnId")
                return@withContext Result.success(ApnOperationResult(
                    success = false,
                    apnId = apnId,
                    message = "APN ID无效",
                    errorCode = -1
                ))
            }

            // 修改APN配置的carrier_enabled字段为0（禁用）
            val uri = Uri.parse("content://telephony/carriers/$apnId")
            val values = ContentValues().apply {
                put("carrier_enabled", 0)
            }

            val affectedRows = context.contentResolver.update(uri, values, null, null)

            if (affectedRows > 0) {
                Logger.apnI("$TAG APN禁用成功: ID=$apnId, 影响行数=$affectedRows")
                val result = ApnOperationResult(
                    success = true,
                    apnId = apnId,
                    message = "APN禁用成功",
                    affectedRows = affectedRows
                )
                Result.success(result)
            } else {
                Logger.apnW("$TAG APN禁用失败: ID=$apnId, 未找到对应的APN记录")
                val result = ApnOperationResult(
                    success = false,
                    apnId = apnId,
                    message = "未找到对应的APN记录",
                    errorCode = -2
                )
                Result.success(result)
            }
        } catch (e: Exception) {
            Logger.apnE("$TAG 禁用APN失败: ID=$apnId", e)
            Result.failure(e)
        }
    }
    
    /**
     * 删除APN
     */
    suspend fun deleteApn(apnId: Long): Result<ApnOperationResult> {
        return try {
            Logger.apnI("$TAG 删除APN: ID=$apnId")
            
            val result = apnConfigManager.deleteApnConfig(apnId)
            
            if (result.isSuccess) {
                refreshApnConfigs()
            }
            
            result
        } catch (e: Exception) {
            Logger.apnE("$TAG 删除APN失败", e)
            Result.failure(e)
        }
    }
    
    /**
     * 重置APN配置
     */
    suspend fun resetApns(simSlot: Int): Result<ApnOperationResult> {
        return try {
            Logger.apnI("$TAG 重置APN配置: SIM槽位=$simSlot")
            
            val result = apnConfigManager.resetApnConfigs(simSlot)
            
            if (result.isSuccess) {
                refreshApnConfigs()
            }
            
            result
        } catch (e: Exception) {
            Logger.apnE("$TAG 重置APN配置失败", e)
            Result.failure(e)
        }
    }
    
    /**
     * 验证APN连接
     */
    suspend fun validateApn(apnId: Long, testUrl: String = "", timeout: Int = 30000): Result<ApnValidationResult> {
        return try {
            Logger.apnI("$TAG 验证APN连接: ID=$apnId")
            
            // 查找对应的APN配置
            val apnConfig = currentApnConfigs.get().find {
                it.id == apnId
            }
            
            if (apnConfig == null) {
                return Result.failure(IllegalArgumentException("APN not found: $apnId"))
            }
            
            val result = apnConfigManager.validateApnConnection(apnConfig, testUrl, timeout)
            
            if (result.isSuccess) {
                val validationResult = result.getOrNull()!!
                if (validationResult.isValid) {
                    statistics.recordConnectionSuccess()
                } else {
                    statistics.recordConnectionFailure()
                }
            }
            
            result
        } catch (e: Exception) {
            Logger.apnE("$TAG 验证APN连接失败", e)
            statistics.recordConnectionFailure()
            Result.failure(e)
        }
    }
    
    /**
     * 测试网络连接
     */
    suspend fun testConnectivity(testUrl: String = ""): Result<ApnValidationResult> {
        return try {
            Logger.apnI("$TAG 测试网络连接")
            
            // 使用当前默认APN进行测试
            val currentApns = getCurrentApns()
            val defaultApn = currentApns.firstOrNull()
            
            if (defaultApn == null) {
                return Result.failure(IllegalStateException("No default APN found"))
            }
            
            validateApn(defaultApn.id, testUrl)
        } catch (e: Exception) {
            Logger.apnE("$TAG 测试网络连接失败", e)
            Result.failure(e)
        }
    }
    
    /**
     * 刷新运营商信息
     */
    suspend fun refreshCarrierInfo(): Result<List<CarrierInfo>> {
        return try {
            Logger.apnI("$TAG 刷新运营商信息")
            
            val carriers = carrierDetector.detectAllCarriers()
            currentCarriers.set(carriers)
            
            carriers.forEach { carrier ->
                if (carrier.simSlot != currentCarriers.get().find { it.simSlot == carrier.simSlot }?.simSlot) {
                    statistics.recordCarrierSwitch()
                }
                
                if (carrier.isRoaming) {
                    statistics.recordRoamingEvent()
                }
            }
            
            Logger.apnI("$TAG 运营商信息刷新完成，发现 ${carriers.size} 个运营商")
            Result.success(carriers)
        } catch (e: Exception) {
            Logger.apnE("$TAG 刷新运营商信息失败", e)
            Result.failure(e)
        }
    }
    
    /**
     * 刷新APN配置
     */
    private suspend fun refreshApnConfigs() {
        try {
            val allApns = apnConfigManager.getAllApnConfigs()
            if (allApns.isSuccess) {
                currentApnConfigs.set(allApns.getOrNull() ?: emptyList())
                statistics.activeConfigurations = currentApnConfigs.get().count { it.isActive }.toLong()
            }
        } catch (e: Exception) {
            Logger.apnE("$TAG 刷新APN配置失败", e)
        }
    }
    
    /**
     * 启动网络状态监控
     */
    private fun startNetworkMonitoring() {
        try {
            networkStatusMonitor.startMonitoring { status ->
                currentNetworkStatus.set(status)
                
                // 记录统计信息
                if (status.isConnected) {
                    statistics.recordConnectionSuccess()
                } else {
                    statistics.recordConnectionFailure()
                }
            }
            
            Logger.apnI("$TAG 网络状态监控已启动")
        } catch (e: Exception) {
            Logger.apnE("$TAG 启动网络状态监控失败", e)
        }
    }
    
    /**
     * 停止网络状态监控
     */
    private fun stopNetworkMonitoring() {
        try {
            networkStatusMonitor.stopMonitoring()
            Logger.apnI("$TAG 网络状态监控已停止")
        } catch (e: Exception) {
            Logger.apnE("$TAG 停止网络状态监控失败", e)
        }
    }
    
    /**
     * 启动验证任务
     */
    private fun startValidationTask() {
        validationJob = GlobalScope.launch {
            try {
                while (isActive) {
                    // 定期验证当前APN连接
                    val currentApns = getCurrentApns()
                    currentApns.forEach { apn ->
                        try {
                            validateApn(apn.id)
                        } catch (e: Exception) {
                            Logger.apnE("$TAG 定期验证APN失败: ${apn.name}", e)
                        }
                    }
                    
                    delay(DEFAULT_VALIDATION_INTERVAL)
                }
            } catch (e: CancellationException) {
                Logger.apnI("$TAG 验证任务已取消")
            } catch (e: Exception) {
                Logger.apnE("$TAG 验证任务异常", e)
            }
        }
    }
    
    /**
     * 停止验证任务
     */
    private fun stopValidationTask() {
        validationJob?.cancel()
        validationJob = null
    }
    
    // Getter方法
    fun getCarrierInfo(): List<CarrierInfo> = currentCarriers.get()
    fun getAllApns(): List<ApnConfig> = currentApnConfigs.get()
    fun getCurrentApns(): List<ApnConfig> = currentApnConfigs.get().filter { it.isDefault || it.isActive }
    fun getNetworkStatus(): NetworkStatus = currentNetworkStatus.get() ?: networkStatusMonitor.getCurrentNetworkStatus()
    fun getStatistics(): ApnStatistics = statistics
    fun getDataUsage(): DataUsage = carrierDetector.getCurrentDataUsage()


    /**
     * 获取当前运营商numeric
     */
    suspend fun getCurrentOperatorNumeric(): String {
        return try {
            carrierDetector.getCurrentOperatorNumeric()
        } catch (e: Exception) {
            Logger.apnE("$TAG 获取当前运营商numeric失败", e)
            ""
        }
    }


    /**
     * 计算Bearer Bitmask值
     * 从JSON数组字符串中解析bearer值并计算bitmask
     */
    fun getBearerBitmaskValue(bearers: String?): Int {
        Logger.apnI("$TAG getBearerBitmaskValue array = $bearers")
        var bearerBitmask = 0
        try {
            if (bearers.isNullOrEmpty()) {
                return bearerBitmask
            }
            val array = JSONArray(bearers)
            for (i in 0 until array.length()) {
                val bearer = array.optString(i)
                if (bearer.toInt() == 0) {
                    bearerBitmask = 0
                    break
                } else {
                    bearerBitmask = bearerBitmask or getBitmaskForTech(bearer.toInt())
                }
            }
        } catch (e: Exception) {
            Logger.apnE("$TAG getBearerBitmaskValue失败", e)
        }
        return bearerBitmask
    }

    /**
     * 根据bearerBitmask获取bearer值
     */
    fun getBearerValue(bearerBitmask: Int): Int {
        val mBearerInitialVal = 0
        return if (bearerBitmask == 0 || mBearerInitialVal == 0) {
            0
        } else if (bitmaskHasTech(bearerBitmask, mBearerInitialVal)) {
            mBearerInitialVal
        } else {
            0
        }
    }

    /**
     * 获取指定技术的bitmask
     */
    private fun getBitmaskForTech(radioTech: Int): Int {
        return if (radioTech >= 1) {
            1 shl (radioTech - 1)
        } else {
            0
        }
    }

    /**
     * 检查bitmask是否包含指定技术
     */
    fun bitmaskHasTech(bearerBitmask: Int, radioTech: Int): Boolean {
        return if (bearerBitmask == 0) {
            true
        } else if (radioTech >= 1) {
            (bearerBitmask and (1 shl (radioTech - 1))) != 0
        } else {
            false
        }
    }


    /**
     * 根据APN名称和numeric查找APN
     */
    suspend fun findApnByApnNameAndNumeric(apnName: String, numeric: String): ApnConfig? {
        return try {
            apnConfigManager.findApnByApnNameAndNumeric(apnName, numeric)
        } catch (e: Exception) {
            Logger.apnE("$TAG 查找APN失败", e)
            null
        }
    }

    /**
     * 根据ID移除APN
     */
    suspend fun removeApnById(apnId: Long): Result<Boolean> {
        return try {
            Logger.apnI("$TAG 根据ID移除APN: $apnId")
            val result = apnConfigManager.deleteApnConfig(apnId)
            Result.success(result.isSuccess)
        } catch (e: Exception) {
            Logger.apnE("$TAG 根据ID移除APN失败", e)
            Result.failure(e)
        }
    }

    /**
     * 根据运营商和ID移除APN
     */
    suspend fun removeApnByOperatorAndId(apnId: String): Result<Boolean> {
        return try {
            Logger.apnI("$TAG 根据运营商和ID移除APN: $apnId")

            val uri = Uri.parse("content://telephony/carriers")
            val selection = "_id = ?"
            val selectionArgs = arrayOf(apnId)

            val deletedRows = context.contentResolver.delete(uri, selection, selectionArgs)
            val success = deletedRows > 0

            Logger.apnI("$TAG 移除APN结果: $success, 删除了 $deletedRows 行")
            Result.success(success)
        } catch (e: Exception) {
            Logger.apnE("$TAG 根据运营商和ID移除APN失败", e)
            Result.failure(e)
        }
    }

    /**
     * 根据运营商和APN名称移除APN
     */
    suspend fun removeApnByOperatorAndApnName(apnName: String, numeric: String): Result<Boolean> {
        return try {
            Logger.apnI("$TAG 根据运营商和APN名称移除APN: $apnName, $numeric")
            apnConfigManager.removeApnByOperatorAndApnName(apnName, numeric)
        } catch (e: Exception) {
            Logger.apnE("$TAG 根据运营商和APN名称移除APN失败", e)
            Result.failure(e)
        }
    }

    /**
     * 添加APN配置
     */
    suspend fun addApnConfig(apnConfig: ApnConfig, setAsDefault: Boolean = false): Result<ApnOperationResult> {
        return try {
            Logger.apnI("$TAG 添加APN配置: ${apnConfig.apn}, 设为默认: $setAsDefault")

            val result = apnConfigManager.addApnConfig(apnConfig)
            if (result.isSuccess && setAsDefault) {
                val apnId = result.getOrNull()?.apnId ?: -1
                if (apnId != -1L) {
                   setDefaultApnBySlot(apnId.toString(), apnConfig.simSlot)
                }
            }

            result
        } catch (e: Exception) {
            Logger.apnE("$TAG 添加APN配置失败", e)
            Result.failure(e)
        }
    }

    /**
     * 保存APN配置
     */
    suspend fun saveApnConfig(apnConfig: ApnConfig): Result<ApnOperationResult> {
        return try {
            Logger.apnI("$TAG 保存APN配置: ${apnConfig.apn}")
            apnConfigManager.addApnConfig(apnConfig)
        } catch (e: Exception) {
            Logger.apnE("$TAG 保存APN配置失败", e)
            Result.failure(e)
        }
    }

    /**
     * 检查并保存APN
     * 这是核心方法：先查找 -> 删除旧的 -> 添加新的
     */
    suspend fun checkAndSaveApn(apn: ApnConfig): Result<Int> {
        return try {
            Logger.apnI("$TAG 💾 保存APN: ${apn.name} (${apn.apn}) - 运营商: ${apn.numeric}")

            // 1. 先查找是否存在相同的APN
            Logger.apn("$TAG   检查是否存在同名APN...")
            val findApn = findApnByApnNameAndNumeric(apn.apn, apn.numeric)

            if (findApn != null) {
                // 2. 如果存在，先删除旧的
                Logger.apnI("$TAG   🗑️ 发现同名APN(ID:${findApn.id})，先删除...")
                val removeResult = removeApnById(findApn.id)
                if (removeResult.isSuccess) {
                    Logger.apnI("$TAG   旧APN删除成功")
                } else {
                    Logger.apnE("$TAG   旧APN删除失败")
                }
            } else {
                Logger.apn("$TAG   未发现同名APN，直接添加")
            }

            // 3. 添加新的APN
            Logger.apn("$TAG   ➕ 添加新APN配置...")
            val addResult = addApnWithForce(apn, true)
            val apnId = addResult.getOrNull() ?: -1
            if (apnId != -1) {
                Logger.apnI("$TAG   APN保存成功，ID: $apnId")
            } else {
                Logger.apnE("$TAG   APN保存失败")
            }

            addResult
        } catch (e: Exception) {
            Logger.apnE("$TAG APN保存异常", e)
            Result.failure(e)
        }
    }

    /**
     * 强制添加APN
     */
    suspend fun addApnWithForce(apn: ApnConfig, force: Boolean): Result<Int> {
        return try {
            if (apn.apn.isBlank() && apn.numeric.isBlank()) {
                Logger.apnW("$TAG 跳过添加APN，APN和numeric不能为空")
                return Result.success(-1)
            }

            val uri = Uri.parse("content://telephony/carriers")

            if (!force && (apn.name.length < 1 || apn.apn.length < 1 ||
                          apn.mcc.length != 3 || (apn.mnc.length and 0xFFFE) != 2)) {
                return Result.success(-1)
            }

            val values = apnConfigManager.createContentValues(apn)
            val insertUri = context.contentResolver.insert(uri, values)

            val apnId = if (insertUri != null) {
                insertUri.lastPathSegment?.toInt() ?: -1
            } else {
                -1
            }

            Logger.apnI("$TAG 添加APN结果: apnId=$apnId")
            Result.success(apnId)
        } catch (e: Exception) {
            Logger.apnE("$TAG 强制添加APN失败", e)
            Result.failure(e)
        }
    }

    /**
     * 更新APN
     */
    suspend fun updateApn(apn: ApnConfig): Result<Boolean> {
        return try {
            if (apn.id <= 0) {
                Logger.apnW("$TAG APN ID无效: ${apn.id}")
                return Result.success(false)
            }

            val findApn = findApnByApnNameAndNumeric(apn.apn, apn.numeric)
            if (findApn != null) {
                return updateApnWithForce(apn, true)
            }

            Result.success(false)
        } catch (e: Exception) {
            Logger.apnE("$TAG 更新APN失败", e)
            Result.failure(e)
        }
    }

    /**
     * 强制更新APN
     */
    suspend fun updateApnWithForce(apn: ApnConfig, force: Boolean): Result<Boolean> {
        return try {
            val uri = Uri.parse("content://telephony/carriers")

            if (!force && (apn.name.length < 1 || apn.mcc.length != 3 ||
                          (apn.mnc.length and 0xFFFE) != 2 ||
                          (apn.type.isNullOrEmpty() || !apn.type.contains("ia")) && apn.apn.length < 1)) {
                Logger.apnW("$TAG force = false, 跳过更新")
                return Result.success(false)
            }

            if (force && apn.name.length < 1 && apn.apn.length < 1) {
                val deletedRows = context.contentResolver.delete(uri, null, null)
                Logger.apnI("$TAG 删除行数: $deletedRows")
                return Result.success(false)
            }

            val values = apnConfigManager.createContentValues(apn)
            val count = context.contentResolver.update(uri, values, null, null)
            val success = count > 0

            Logger.apnI("$TAG 更新APN结果: $success, 更新了 $count 行")
            Result.success(success)
        } catch (e: Exception) {
            Logger.apnE("$TAG 强制更新APN失败", e)
            Result.failure(e)
        }
    }

    /**
     * 应用APN网络配置
     */
    suspend fun applyApnNetwork(): Result<Unit> {
        return try {
            Logger.apnI("$TAG 应用APN网络配置")

            val builder = NetworkRequest.Builder()
            builder.addCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET)
            builder.addTransportType(NetworkCapabilities.TRANSPORT_CELLULAR)
            val request = builder.build()

            val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
            connectivityManager.requestNetwork(request, object : ConnectivityManager.NetworkCallback() {
                override fun onAvailable(network: Network) {
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                        connectivityManager.bindProcessToNetwork(network)
                    } else {
                        @Suppress("DEPRECATION")
                        ConnectivityManager.setProcessDefaultNetwork(network)
                    }
                    Logger.apnI("$TAG APN网络绑定成功")
                }
            })

            Result.success(Unit)
        } catch (e: Exception) {
            Logger.apnE("$TAG 应用APN网络配置失败", e)
            Result.failure(e)
        }
    }

    /**
     * 恢复正常网络
     */
    suspend fun restoreNormalNetwork(): Result<Unit> {
        return try {
            Logger.apnI("$TAG 恢复正常网络")

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
                connectivityManager.bindProcessToNetwork(null)
            } else {
                @Suppress("DEPRECATION")
                ConnectivityManager.setProcessDefaultNetwork(null)
            }

            Logger.apnI("$TAG 正常网络恢复成功")
            Result.success(Unit)
        } catch (e: Exception) {
            Logger.apnE("$TAG 恢复正常网络失败", e)
            Result.failure(e)
        }
    }

    /**
     * 恢复默认APN
     */
    suspend fun restoreDefaultApn(subId: Int = -1): Result<Unit> {
        return try {
            Logger.apnI("$TAG 恢复默认APN: subId=$subId")

            if (subId < 0) {
                Logger.apnW("$TAG subId < 0, 跳过恢复默认APN")
                return Result.success(Unit)
            }

            val uri = getDefaultApnUri(subId)
            val count = context.contentResolver.delete(uri, null, null)

            Logger.apnI("$TAG 恢复默认APN result = ${count > 0}, subId = $subId")
            Result.success(Unit)
        } catch (e: Exception) {
            Logger.apnE("$TAG 恢复默认APN失败", e)
            Result.failure(e)
        }
    }

    /**
     * 检查是否正在使用移动数据网络
     */
    suspend fun isMobileDataActive(): Boolean {
        return try {
            networkStatusMonitor.isMobileDataActive()
        } catch (e: Exception) {
            Logger.apnE("$TAG 检查移动数据状态失败", e)
            false
        }
    }

    /**
     * 测试网络连通性
     */
    suspend fun testNetworkConnectivity(host: String = "*******", timeout: Int = 5000): Boolean {
        return try {
            Logger.apnI("$TAG 测试网络连通性: $host")
            networkStatusMonitor.testConnectivity(host, timeout)
        } catch (e: Exception) {
            Logger.apnE("$TAG 网络连通性测试失败", e)
            false
        }
    }

    // ==================== 辅助方法 ====================

    /**
     * 设置默认APN
     */
    suspend fun setDefaultApnBySlot(apnId: String, slotIndex: Int): Result<Unit> {
        return try {
            Logger.apnI("$TAG 设置默认APN: apnId=$apnId, slotIndex=$slotIndex")

            val resolver = context.contentResolver
            val values = ContentValues()
            values.put("apn_id", apnId)

            val uri = Uri.parse("content://telephony/carriers/preferapn")
                .buildUpon()
                .appendQueryParameter("slot", slotIndex.toString())
                .build()

            val count = resolver.update(uri, values, null, null)
            Logger.apnI("$TAG 设置默认APN结果: 更新了 $count 行")

            Result.success(Unit)
        } catch (e: Exception) {
            Logger.apnE("$TAG 设置默认APN失败", e)
            Result.failure(e)
        }
    }

    /**
     * 应用APN到指定SIM卡
     */
    suspend fun applyApnBySubId(subId: Int, apnId: Int): Result<Boolean> {
        return try {
            Logger.apnI("$TAG 应用APN到SIM卡: subId=$subId, apnId=$apnId")

            val values = ContentValues()
            values.put("sub_id", subId)
            values.put("apn_id", apnId)

            val uri = Uri.parse("content://telephony/carriers/preferapn")
            val count = context.contentResolver.update(uri, values, null, null)
            val success = count > 0

            Logger.apnI("$TAG 应用APN结果: $success, 更新了 $count 行")
            Result.success(success)
        } catch (e: Exception) {
            Logger.apnE("$TAG 应用APN到SIM卡失败", e)
            Result.failure(e)
        }
    }

    /**
     * 获取指定SIM卡的默认APN
     */
    suspend fun getDefaultApnBySubId(subId: Int): ApnConfig? {
        return try {
            Logger.apnI("$TAG 获取默认APN: subId=$subId")

            val uri = getPreferApnUri(subId)
            val cursor = context.contentResolver.query(uri, null, null, null, null)

            cursor?.use {
                if (it.moveToFirst()) {
                    return apnConfigManager.parseApnConfigFromCursor(it)
                }
            }

            null
        } catch (e: Exception) {
            Logger.apnE("$TAG 获取默认APN失败", e)
            null
        }
    }

    /**
     * 获取首选APN的URI
     */
    private fun getPreferApnUri(subId: Int): Uri {
        return Uri.parse("content://telephony/carriers/preferapn/subId/$subId")
    }

    /**
     * 获取默认APN的URI
     */
    private fun getDefaultApnUri(subId: Int): Uri {
        return Uri.parse("content://telephony/carriers/preferapn/subId/$subId")
    }

    /**
     * 获取活跃的SIM卡信息列表
     */
    @SuppressLint("MissingPermission")
    suspend fun getActiveSubscriptionInfoList(): List<SubscriptionInfo>? {
        return withContext(Dispatchers.IO) {
            try {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP_MR1) {
                    val subscriptionManager = SubscriptionManager.from(context)
                    val activeSubscriptions = subscriptionManager.activeSubscriptionInfoList

                    if (activeSubscriptions != null && activeSubscriptions.isNotEmpty()) {
                        Logger.apnI("$TAG 获取到 ${activeSubscriptions.size} 个活跃SIM卡")
                        activeSubscriptions.forEach { subscription ->
                            val carrierId = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                                subscription.carrierId
                            } else {
                                -1 // API 29以下使用默认值
                            }
                            Logger.apn("$TAG SIM卡信息: slotIndex=${subscription.simSlotIndex}, " +
                                    "carrierId=$carrierId, " +
                                    "displayName=${subscription.displayName}")
                        }
                        activeSubscriptions
                    } else {
                        Logger.apnW("$TAG 没有找到活跃的SIM卡")
                        null
                    }
                } else {
                    // Android 5.1以下版本的兼容处理
                    val currentNumeric = getCurrentOperatorNumeric()
                    if (currentNumeric.isNotEmpty()) {
                        Logger.apnI("$TAG 低版本Android，使用当前运营商信息: $currentNumeric")
                        // 返回null，因为低版本无法获取SubscriptionInfo
                        null
                    } else {
                        Logger.apnW("$TAG 低版本Android，无法获取运营商信息")
                        null
                    }
                }
            } catch (e: Exception) {
                Logger.apnE("$TAG 获取活跃SIM卡信息失败", e)
                null
            }
        }
    }

    /**
     * 检查并保存所有APN
     */
    suspend fun checkAndSaveAllApn(numericMap: Map<String, List<ApnConfig>>): Result<Unit> {
        return try {
            Logger.apnI("$TAG 检查并保存所有APN配置")

            if (numericMap.isNotEmpty()) {
                for ((keyForNumeric, apnListForNumeric) in numericMap) {
                    Logger.apn("$TAG checkAndSaveAllApn Key: $keyForNumeric")
                    for (apn in apnListForNumeric) {
                        // 调用核心的checkAndSaveApn方法
                        checkAndSaveApn(apn)
                    }
                }
            }

            Result.success(Unit)
        } catch (e: Exception) {
            Logger.apnE("$TAG 检查并保存所有APN失败", e)
            Result.failure(e)
        }
    }

    /**
     * 获取默认的subscription ID
     */
    fun getDefaultSubscriptionId(): Int {
        return try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP_MR1) {
                SubscriptionManager.getDefaultSubscriptionId()
            } else {
                // API 21以下，返回默认值
                -1
            }
        } catch (e: Exception) {
            Logger.apnE("$TAG 获取默认subscription ID失败", e)
            -1
        }
    }
}
