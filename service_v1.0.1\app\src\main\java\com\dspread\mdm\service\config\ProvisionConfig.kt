package com.dspread.mdm.service.config

import android.content.Context
import com.dspread.mdm.service.modules.provisioning.model.*
import java.io.File

/**
 * Provisioning配置管理
 * 支持获取默认配置和配置下载目录
 */
object ProvisionConfig {

    // 默认配置服务器URL
//    const val DEFAULT_CONFIG_URL = "http://35.75.3.206:8080/status/config"

    // 生产环境服务器URL
    const val DEFAULT_CONFIG_URL = "https://config.dspreadserv.net/status/config"

    private var isDebugMode = false

    /**
     * 设置调试模式
     */
    fun setDebugMode(enabled: Boolean) {
        isDebugMode = enabled
    }
    
    /**
     * 获取默认配置
     */
    fun getDefaultConfig(): ProvisioningConfig {
        return ProvisioningConfig(
            cid = "10001",
            requestTime = "1755508964344",
            polling = PollingConfig(
                statusApiUrl = "wss://api.dspreadserv.net/status/websocket/register",
                remoteUrl = "wss://remote.dspreadserv.net/remoteWSS/websockify",
                heartbeatTime = "300",
                terminalInfoTime = "900",
                uploadMode = "1",
                wssReconnConfig = WssReconnConfig(
                    delayPolicy = "1",
                    delaySwitch = "1",
                    delayTime = "60"
                )
            ),
            system = SystemConfig(
                logo = "https://siot-bucket01.s3.ap-northeast-1.amazonaws.com/test/logo.bin",
                logoMd5 = "ba1ee533924eae5c408465e7cddcbda4",
                bootAnimation = "https://siot-bucket01.s3.ap-northeast-1.amazonaws.com/test/bootanimation.zip",
                bootAnimationMd5 = "ba1ee533924eae5c408465e7cddcbda4",
                timezone = "Asia/Hong_Kong",
                gpsConfig = GpsConfig(
                    minUpdateTime = "30",
                    scheduleTime = "60",
                    maxLocateTime = "0",
                    minDistance = "10",
                    validDistance = "500",
                    care = "1"
                ),
                powerSaveMode = PowerSaveMode(
                    enable = "1",
                    screenTimeout = "0"
                )
            )
        )
    }
    
    /**
     * 获取配置下载目录
     * debug模式下使用/sdcard/应用目录，生产模式使用/data/pos/
     */
    fun getConfigDownloadPath(context: Context): String {
        return if (isDebugMode) {
            // Debug模式：使用外部存储应用目录
            "/sdcard/Android/data/${context.packageName}/files/config/"
        } else {
            // 生产模式：使用系统目录
            "/data/pos/config/"
        }
    }
    
    /**
     * 获取媒体文件下载目录
     */
    fun getMediaDownloadPath(context: Context): String {
        return if (isDebugMode) {
            // Debug模式：使用外部存储应用目录
            "/sdcard/Android/data/${context.packageName}/files/media/"
        } else {
            // 生产模式：使用系统目录
            "/data/pos/media/"
        }
    }
    
    /**
     * 获取Logo下载目录
     */
    fun getLogoDownloadPath(context: Context): String {
        return getMediaDownloadPath(context) + "logo/"
    }
    
    /**
     * 获取开机动画下载目录
     */
    fun getBootAnimationDownloadPath(context: Context): String {
        return getMediaDownloadPath(context) + "bootanimation/"
    }
    
    /**
     * 确保目录存在
     */
    fun ensureDirectoryExists(path: String): Boolean {
        return try {
            val dir = File(path)
            if (!dir.exists()) {
                dir.mkdirs()
            } else {
                true
            }
        } catch (e: Exception) {
            false
        }
    }
    
    /**
     * 获取配置描述
     */
    fun getConfigDescription(context: Context): String {
        val mode = if (isDebugMode) "调试模式" else "生产模式"
        return """
            Provisioning配置 ($mode):
            配置目录: ${getConfigDownloadPath(context)}
            媒体目录: ${getMediaDownloadPath(context)}
            Logo目录: ${getLogoDownloadPath(context)}
            开机动画目录: ${getBootAnimationDownloadPath(context)}
        """.trimIndent()
    }
    
    /**
     * 初始化所有必要目录
     */
    fun initializeDirectories(context: Context): Boolean {
        val directories = listOf(
            getConfigDownloadPath(context),
            getMediaDownloadPath(context),
            getLogoDownloadPath(context),
            getBootAnimationDownloadPath(context)
        )
        
        var allSuccess = true
        directories.forEach { path ->
            if (!ensureDirectoryExists(path)) {
                allSuccess = false
            }
        }
        
        return allSuccess
    }
    
    /**
     * 获取目录状态信息
     */
    fun getDirectoryStatus(context: Context): Map<String, Boolean> {
        return mapOf(
            "config" to File(getConfigDownloadPath(context)).exists(),
            "media" to File(getMediaDownloadPath(context)).exists(),
            "logo" to File(getLogoDownloadPath(context)).exists(),
            "bootAnimation" to File(getBootAnimationDownloadPath(context)).exists()
        )
    }
}
