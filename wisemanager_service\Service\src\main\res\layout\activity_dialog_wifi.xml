<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context="com.bbpos.wiseapp.settings.activity.WiFiDialogActivity">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginTop="62dp"
        android:layout_marginBottom="58dp"
        android:layout_marginLeft="20dp"
        android:layout_marginRight="20dp"
        android:background="@drawable/bg_shape"
        android:orientation="vertical">
        <TextView
            android:id="@+id/tv_title"
            android:layout_width="wrap_content"
            android:layout_height="53dp"
            android:layout_marginLeft="24dp"
            android:gravity="center"
            android:text="@string/select_wifi"
            android:textSize="18sp"
            android:textColor="@color/title"/>
        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:background="@color/white">
            <TextView
                android:id="@+id/tv_prompt"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:gravity="center"
                android:text=""
                android:textSize="14sp"
                android:textColor="@color/subtitle"/>
            <LinearLayout
                android:id="@+id/ll_listwifi"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical"
                android:background="@color/white">
                <ListView
                    android:id="@+id/list_wifi"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:listSelector="@drawable/item_selector"
                    android:dividerHeight="1dp"
                    android:divider="@color/divider">
                </ListView>
            </LinearLayout>
        </FrameLayout>
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="68dp"
            android:gravity="center_vertical|right"
            android:orientation="horizontal">
            <TextView
                android:id="@+id/tv_skip"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:paddingTop="@dimen/dp_10"
                android:paddingBottom="@dimen/dp_10"
                android:paddingLeft="@dimen/dp_5"
                android:paddingRight="@dimen/dp_5"
                android:layout_marginRight="30dp"
                android:gravity="center"
                android:text="@string/skip"
                android:textColor="@color/theme_green"
                android:textSize="16sp"/>
        </LinearLayout>
    </LinearLayout>
</LinearLayout>