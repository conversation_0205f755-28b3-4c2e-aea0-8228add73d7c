✅ WebSocket 物理连接成功，等待服务器 hellMsg
2025-08-18 18:00:32.599  6288-6575  WebSocket               com.dspread.mdm.service              I  🔧 ===== 心跳发送器实际配置 =====
2025-08-18 18:00:32.607  6288-6575  WebSocket               com.dspread.mdm.service              I  🔧 实际 PING 发送间隔: 60000ms (60秒)
2025-08-18 18:00:32.613  6288-6575  WebSocket               com.dspread.mdm.service              I  🔧 PING 定时器名称: PingSender
2025-08-18 18:00:32.619  6288-6575  WebSocket               com.dspread.mdm.service              I  🔧 实际 PONG 发送间隔: 0ms (0秒)
2025-08-18 18:00:32.625  6288-6575  WebSocket               com.dspread.mdm.service              I  🔧 PONG 定时器名称: PongSender
2025-08-18 18:00:32.631  6288-6575  WebSocket               com.dspread.mdm.service              I  🔧 ===============================
2025-08-18 18:00:32.920  6288-6575  Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"hellMsg":"hello 01354090202503050399","tranCode":"S0000","version":"1","rebootTime":"03:01:27","serialNo":"01354090202503050399","deviceStatus":"6"}
2025-08-18 18:00:32.928  6288-6575  Common                  com.dspread.mdm.service              I  ✅ 收到服务器 hellMsg，WebSocket 业务连接成功！
2025-08-18 18:00:32.934  6288-6575  Common                  com.dspread.mdm.service              I  ✅ WebSocket 业务连接成功！
2025-08-18 18:00:32.940  6288-6575  WebSocket               com.dspread.mdm.service              I  🔧 业务连接成功，重连计数已重置为 0
2025-08-18 18:00:32.952  6288-6575  Common                  com.dspread.mdm.service              I  ✅ ⏰ 设置任务执行定时器成功，下次执行: 60秒后 (1分钟)
2025-08-18 18:00:32.958  6288-6575  WebSocket               com.dspread.mdm.service              I  🔧 已启动任务执行定时器
2025-08-18 18:00:32.974  6288-6575  Common                  com.dspread.mdm.service              D  🔧 TimerConfig 使用Provisioning配置的心跳间隔: 300秒
2025-08-18 18:00:32.984  6288-6575  Common                  com.dspread.mdm.service              I  ✅ ⏰ 设置心跳定时器成功，下次执行: 300秒后 (5分钟)
2025-08-18 18:00:32.989  6288-6575  WebSocket               com.dspread.mdm.service              I  🔧 已启动心跳定时器
2025-08-18 18:00:33.004  6288-6575  Common                  com.dspread.mdm.service              D  🔧 TimerConfig 使用Provisioning配置的终端信息上传间隔: 900秒
2025-08-18 18:00:33.015  6288-6575  Common                  com.dspread.mdm.service              I  ✅ ⏰ 设置终端信息定时器成功，下次执行: 900秒后 (15分钟)
2025-08-18 18:00:33.020  6288-6575  WebSocket               com.dspread.mdm.service              I  🔧 已启动终端信息上传定时器
2025-08-18 18:00:33.026  6288-6575  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接成功，检查自身更新任务状态
2025-08-18 18:00:33.032  6288-6575  ContextImpl             com.dspread.mdm.service              W  Calling a method in the system process without a qualified user: android.app.ContextImpl.sendBroadcast:1220 android.content.ContextWrapper.sendBroadcast:504 com.dspread.mdm.service.broadcast.core.BroadcastSender.sendBroadcast:99 com.dspread.mdm.service.broadcast.core.BroadcastSender.sendBroadcast:23 com.dspread.mdm.service.network.websocket.connection.WsConnectionManager.checkSelfUpdateTaskStatus:311 
2025-08-18 18:00:33.036  6288-6575  Receiver                com.dspread.mdm.service              D  🔧 发送广播: com.dspread.mdm.service.CHECK_SELF_UPDATE_STATUS
2025-08-18 18:00:33.037  6288-6288  Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 收到自身更新状态检查广播: com.dspread.mdm.service.CHECK_SELF_UPDATE_STATUS
2025-08-18 18:00:33.041  6288-6288  Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 开始执行自身更新任务状态检查
2025-08-18 18:00:33.042  6288-6575  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接成功
2025-08-18 18:00:33.047  6288-6575  WebSocket               com.dspread.mdm.service              I  🔧 设备状态: 6
2025-08-18 18:00:33.050  6288-6288  Task                    com.dspread.mdm.service              D  🔧 获取待执行任务数量: 0
2025-08-18 18:00:33.052  6288-6575  WebSocket               com.dspread.mdm.service              I  🔧 检查首次连接标志: isFirstTimeWebSocketConnected=false
2025-08-18 18:00:33.056  6288-6288  Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 待执行任务数量: 0
2025-08-18 18:00:33.058  6288-6575  WebSocket               com.dspread.mdm.service              I  🔧 非首次连接，跳过初始化信息发送和初始化检查
2025-08-18 18:00:33.063  6288-6575  WebSocket               com.dspread.mdm.service              I  🔧 执行必要的状态同步检查
2025-08-18 18:00:33.068  6288-6575  WebSocket               com.dspread.mdm.service              I  🔧 检查服务更新结果
2025-08-18 18:00:33.076  6288-6575  Task                    com.dspread.mdm.service              I  ℹ️ 本地任务列表长度: 1
2025-08-18 18:00:33.083  6288-6575  Task                    com.dspread.mdm.service              D  🔧 本地任务列表内容: [{"apkMd5":"4b8b6abb2323d19794a5a3202d38623d","apkName":"Via","endDate":"9999-12-31 23:59:59","installBy":"0","versionName":"6.2.0","versionCode":"20250117","url":"https:\/\/smartms.s3.sa-east-1.amazonaws.com\/app\/0065dbfe61ed43448fe63749c18aed6f.apk","beginDate":"2024-08-18 09:50:08","taskType":"01","appId":"9f23ca5c54e9477bbf6d6acbdf884957","apkSize":"1937911","appIconUrl":"https:\/\/smartms.s3.sa-east-1.amazonaws.com\/icon\/2a3e2056c8fa4f16bebf50985fc085f7ic_launcher.png","packName":"mark.via","taskId":"be1661198a8343fda509dac89f6055ee&9f23ca5c54e9477bbf6d6acbdf884957","request_id":"1755510608376ST001","request_time":"1755510608376","silent_install":"","taskResult":"B03","lastUpdateTime":1755510621724}]
2025-08-18 18:00:33.089  6288-6575  Task                    com.dspread.mdm.service              I  ℹ️ 当前应用版本: versionCode=13, versionName=1.0.10.20250818.DSPREAD.MDM.SERVICE
2025-08-18 18:00:33.094  6288-6575  Task                    com.dspread.mdm.service              I  ℹ️ 任务列表数量: 1
2025-08-18 18:00:33.100  6288-6575  Task                    com.dspread.mdm.service              D  🔧 检查任务[0]: taskId=be1661198a8343fda509dac89f6055ee&9f23ca5c54e9477bbf6d6acbdf884957, taskType=01, pkgName=mark.via, versionCode=20250117, versionName=6.2.0, taskResult=B03
2025-08-18 18:00:33.105  6288-6575  WebSocket               com.dspread.mdm.service              I  🔧 检查OS升级状态...
2025-08-18 18:00:33.111  6288-6575  Common                  com.dspread.mdm.service              D  🔧 OsUpdateStatusChecker WebSocket连接成功，检查OS升级状态
2025-08-18 18:00:33.117  6288-6575  Common                  com.dspread.mdm.service              D  🔧 OsUpdateStatusChecker 没有待检查的OS升级任务
2025-08-18 18:00:33.123  6288-6575  WebSocket               com.dspread.mdm.service              I  🔧 OS升级状态检查已触发
2025-08-18 18:00:39.232  6288-6288  GeoFence                com.dspread.mdm.service              D  🔧 [GeofenceReceiver] 收到广播: com.dspread.mdm.service.ACTION_SYSTEM_TIMER
2025-08-18 18:00:39.239  6288-6288  GeoFence                com.dspread.mdm.service              D  🔧 [GeofenceReceiver] 系统定时器触发
2025-08-18 18:00:39.249  6288-6316  GeoFence                com.dspread.mdm.service              D  🔧 [GeofenceReceiver] 执行系统维护任务
2025-08-18 18:00:39.267  6288-6316  GeoFence                com.dspread.mdm.service              D  🔧 [GeofenceReceiver] 设置定时器成功: com.dspread.mdm.service.ACTION_SYSTEM_TIMER, 间隔: 1分钟
2025-08-18 18:00:39.462  6288-6288  GeoFence                com.dspread.mdm.service              D  🔧 [GeofenceReceiver] 收到广播: com.dspread.mdm.service.ACTION_GEOFENCE_CHECK
2025-08-18 18:00:39.471  6288-6288  GeoFence                com.dspread.mdm.service              D  🔧 [GeofenceReceiver] 地理围栏检查定时器触发
2025-08-18 18:00:39.482  6288-6316  GeoFence                com.dspread.mdm.service              D  🔧 [GeofenceReceiver] 检查GeoFence到期执行情况 GEO_STATUS=false M_GEOFENCE_STATUS=1
2025-08-18 18:00:39.492  6288-6316  GeoFence                com.dspread.mdm.service              D  🔧 [GeofenceHandler] 检查并执行到期的地理围栏Profile
2025-08-18 18:00:39.502  6288-6316  GeoFence                com.dspread.mdm.service              D  🔧 [GeofenceHandler] 没有待执行的Profile
2025-08-18 18:00:39.519  6288-6316  GeoFence                com.dspread.mdm.service              D  🔧 [GeofenceReceiver] 设置定时器成功: com.dspread.mdm.service.ACTION_GEOFENCE_CHECK, 间隔: 1分钟
2025-08-18 18:00:39.680  6288-6316  LogStream               com.dspread.mdm.service              I  🔧 [LogStreamManager] 检查日志队列进行压缩，队列长度: 0
2025-08-18 18:00:39.690  6288-6316  LogStream               com.dspread.mdm.service              I  🔧 [LogCollector] 检查存储空间并清理旧文件
2025-08-18 18:00:39.701  6288-6316  LogStream               com.dspread.mdm.service              I  🔧 [LogCollector] 当前压缩文件总大小: 0B / 250MB
2025-08-18 18:00:39.713  6288-6316  LogStream               com.dspread.mdm.service              I  🔧 [LogCollector] 当前原始日志总大小: 958KB / 250MB
2025-08-18 18:00:47.265  6288-6288  Platform                com.dspread.mdm.service              D  🔧 WakeLockManager WakeLock不存在或未持有: ServiceKeepAlive
2025-08-18 18:00:47.274  6288-6288  Service                 com.dspread.mdm.service              D  🔧 ServiceKeepAliveService 保活任务完成，释放WakeLock
2025-08-18 18:00:50.089  6288-6288  Receiver                com.dspread.mdm.service              D  🔧 电池状态更新: 电池状态: 电量=85%, 温度=30.6°C, 健康=2, 充电=是, 低电量=否, 耗尽=否
2025-08-18 18:00:50.100  6288-6288  GeoFence                com.dspread.mdm.service              D  🔧 [GeofenceReceiver] 收到广播: android.intent.action.BATTERY_CHANGED
2025-08-18 18:01:09.727  6288-6316  LogStream               com.dspread.mdm.service              I  🔧 [LogStreamManager] 检查日志队列进行压缩，队列长度: 0
2025-08-18 18:01:09.737  6288-6316  LogStream               com.dspread.mdm.service              I  🔧 [LogCollector] 检查存储空间并清理旧文件
2025-08-18 18:01:09.748  6288-6316  LogStream               com.dspread.mdm.service              I  🔧 [LogCollector] 当前压缩文件总大小: 0B / 250MB
2025-08-18 18:01:09.760  6288-6316  LogStream               com.dspread.mdm.service              I  🔧 [LogCollector] 当前原始日志总大小: 973KB / 250MB
2025-08-18 18:01:23.403  6288-6575  Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"msg_state":"P","request_time":"1755511282311","data":{"param":"","c_type":"CALLC0109"},"expire_time":1755514882,"tranCode":"SC004","request_id":"1755511282311SC004","version":"1","serialNo":"01354090202503050399"}
2025-08-18 18:01:23.410  6288-6575  WebSocket               com.dspread.mdm.service              I  🔧 准备发送响应确认 - requestId: 1755511282311SC004, needResponse: true
2025-08-18 18:01:23.436  6288-6575  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0000","serialNo":"01354090202503050399","request_time":"1755511283420","request_id":"1755511283420C0000","version":"1","org_request_id":"1755511282311SC004","org_request_time":"1755511282311","response_state":"0","myVersionName":"1.0.10.20250818.DSPREAD.MDM.SERVICE","terminalDate":"20250818180123"}
2025-08-18 18:01:23.457  6288-6575  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0000","serialNo":"01354090202503050399","request_time":"1755511283442","request_id":"1755511283442C0000","version":"1","org_request_id":"1755511282311SC004","org_request_time":"1755511282311","response_state":"0","myVersionName":"1.0.10.20250818.DSPREAD.MDM.SERVICE","terminalDate":"20250818180123"}
2025-08-18 18:01:23.463  6288-6575  WebSocket               com.dspread.mdm.service              I  🔧 已发送C0000响应确认: requestId=1755511282311SC004
2025-08-18 18:01:23.469  6288-6575  WebSocket               com.dspread.mdm.service              I  🔧 处理命令: SC004
2025-08-18 18:01:23.474  6288-6575  WebSocket               com.dspread.mdm.service              I  🔧 收到SC004命令，类型: CALLC0109
2025-08-18 18:01:23.479  6288-6575  WebSocket               com.dspread.mdm.service              I  🔧 服务器请求终端信息上传
2025-08-18 18:01:23.484  6288-6575  WebSocket               com.dspread.mdm.service              I  🔧 C0109 主动式上送: terminal_info_timer (主动: 17)
2025-08-18 18:01:23.499  6288-6575  Platform                com.dspread.mdm.service              D  🔧 重新收集应用信息（强制刷新）
2025-08-18 18:01:23.592  6288-6575  AppManager              com.dspread.mdm.service              I  ℹ️ 应用信息获取完成: 总数131(系统129/用户2) 返回2个
2025-08-18 18:01:23.599  6288-6299  System                  com.dspread.mdm.service              W  A resource failed to call close. 
2025-08-18 18:01:23.602  6288-6299  System                  com.dspread.mdm.service              W  A resource failed to call close. 
2025-08-18 18:01:23.605  6288-6299  System                  com.dspread.mdm.service              W  A resource failed to call close. 
2025-08-18 18:01:23.609  6288-6575  Platform                com.dspread.mdm.service              D  🔧 应用信息: 2 个应用
2025-08-18 18:01:23.618  6288-6575  GeoFence                com.dspread.mdm.service              D  🔧 位置提供者状态: Network=false, GPS=true
2025-08-18 18:01:23.634  6288-6575  GeoFence                com.dspread.mdm.service              D  🔧 gps 提供者返回空位置
2025-08-18 18:01:23.641  6288-6575  GeoFence                com.dspread.mdm.service              D  🔧 GPSLocation 当前Profile为空，信息设为空
2025-08-18 18:01:23.650  6288-6575  GeoFence                com.dspread.mdm.service              W  ⚠️ [CellLocationManager] 网络运营商信息无效: 
2025-08-18 18:01:23.715  6288-6575  Platform                com.dspread.mdm.service              D  🔧 内存总容量为： 1.86GB 
2025-08-18 18:01:23.720  6288-6575  Platform                com.dspread.mdm.service              D  🔧 内存使用量为： 0.71GB 
2025-08-18 18:01:23.751  6288-6575  GeoFence                com.dspread.mdm.service              D  🔧 WiFi扫描结果数量: 31
2025-08-18 18:01:23.834  6288-6575  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0109","serialNo":"01354090202503050399","request_time":"1755511283758","request_id":"1755511283758C0109","version":"1","data":{"apkInfo":[{"packName":"com.dspread.mdm.service","apkName":"service","versionCode":13,"versionName":"1.0.10.20250818.DSPREAD.MDM.SERVICE","updateDate":"2025-08-18 17:42:14"},{"packName":"mark.via","apkName":"Via","versionCode":20250117,"versionName":"6.2.0","updateDate":"2025-08-18 17:50:20"}],"locationInfo":{"gps":{"longitude":"-999.0","latitude":"-999.0","satelliteCount":0,"satelliteCountInUsed":0,"satelliteAverSNR":"0.00","distance":"0.0","lockStatus":"1","lockMeter":"200","proId":"","proName":""},"baseSite":{"MCC":"","MNC":"","CID":"-1","LAC":"-1","DBM":"-999","IMSI":"","ICCID":""},"wifi":{"SSID":"fubox_2.4G","IP":"**************","MAC":"BE:87:C2:42:DF:75","SSTH":"-28","BSSID":"7c:10:c9:d6:9f:10"},"address":"无法获取"},"hardwareInfo":[{"name":"CPU Usage","desc":"cpu:0.50"},{"name":"storage_usage_status","desc":" 4.49GB \/ 32.00GB ","state":"1"},{"name":"total_memory","desc":" 0.71GB \/ 1.86GB ","state":"1"},{"name":"androidVersion","desc":"14","state":"1"},{"name":"network_status","desc":"wifi","state":"1"},{"name":"OSVer","desc":"14","state":"1"}],"wifiOption":[{"SSID":"2306","SSTH":"-71"},{"SSID":"2205","SSTH":"-47"},{"SSID":"2205_5G","SSTH":"-59"},{"SSID":"2103","SSTH":"-61"},{"SSID":"DIRECT-C6-Mi All-in-One Inkjet","SSTH":"-60"},{"SSID":"ChinaNet-SnSC","SSTH":"-84"},{"SSID":"2207-5G","SSTH":"-79"},{"SSID":"2106-5G","SSTH":"-89"},{"SSID":"2103_5G","SSTH":"-76"},{"SSID":"@Ruijie-1816_5G","SSTH":"-70"},{"SSID":"CMCC-2203","SSTH":"-65"},{"SSID":"2405","SSTH":"-86"},{"SSID":"诺富特酒店2208","SSTH":"-79"},{"SSID":"1715C","SSTH":"-85"},{"SSID":"2201","SSTH":"-76"},{"SSID":"fubox_2.4G","SSTH":"-31"},{"SSID":"2106","SSTH":"-71"},{"SSID":"DIRECT-CC-HP Laser 1188w","SSTH":"-67"},{"SSID":"2206","SSTH":"-24"},{"SSID":"2206-5G","SSTH":"-33"},{"SSID":"ChinaNet-ce2Z","SSTH":"-81"},{"SSID":"诺富特酒店2303","SSTH":"-90"},{"SSID":"@Ruijie-1816","SSTH":"-47"}],"sytemInfo":{"androidVersion":"14","buildNumber":"BASE_D30M-MU_GEN_AP_V1.0.8.1_202507032000_ota_test","aspl":"2025-03-05"}},"myVersionName":"1.0.10.20250818.DSPREAD.MDM.SERVICE","terminalDate":"20250818180123"}
2025-08-18 18:01:23.839  6288-6575  WebSocket               com.dspread.mdm.service              I  🔧 C0109 终端信息上传完成 (1, trigger=terminal_info_timer)
2025-08-18 18:01:23.844  6288-6575  WebSocket               com.dspread.mdm.service              I  🔧 同时上传C0901应用信息（含服务信息）
2025-08-18 18:01:23.849  6288-6575  WebSocket               com.dspread.mdm.service              I  🔧 检查默认开启的服务
2025-08-18 18:01:23.854  6288-6575  WebSocket               com.dspread.mdm.service              I  🔧 本地无LogStream服务信息
2025-08-18 18:01:23.859  6288-6575  WebSocket               com.dspread.mdm.service              I  🔧 LogStream服务未启用，跳过保存
2025-08-18 18:01:23.864  6288-6575  WebSocket               com.dspread.mdm.service              I  🔧 默认服务检查完成
2025-08-18 18:01:23.869  6288-6575  WebSocket               com.dspread.mdm.service              I  🔧 C0901 主动式上送: command_trigger (主动: 18)
2025-08-18 18:01:23.883  6288-6575  Platform                com.dspread.mdm.service              D  🔧 使用缓存的应用信息
2025-08-18 18:01:23.918  6288-6575  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0901","serialNo":"01354090202503050399","request_time":"1755511283888","request_id":"1755511283888C0901","version":"1","data":{"apkInfo":[{"packName":"com.dspread.mdm.service","apkName":"service","versionCode":13,"versionName":"1.0.10.20250818.DSPREAD.MDM.SERVICE","updateDate":"2025-08-18 17:42:14"},{"packName":"mark.via","apkName":"Via","versionCode":20250117,"versionName":"6.2.0","updateDate":"2025-08-18 17:50:20"}],"sytemInfo":{"androidVersion":"14","buildNumber":"BASE_D30M-MU_GEN_AP_V1.0.8.1_202507032000_ota_test","aspl":"2025-03-05"},"serviceInfo":[{"taskId":"1755510190201","serviceId":"856c2e6d2c7c4f60bc0fadcf9b9ad650","serviceName":"Log Stream","taskType":"05","command":"C03","stateDesc":"IMPLEMENTED","beginDate":"2025-08-18 17:43:14","endDate":"9999-12-31 23:59:59","period":"1","request_id":"","request_time":""}]},"myVersionName":"1.0.10.20250818.DSPREAD.MDM.SERVICE","terminalDate":"20250818180123"}
2025-08-18 18:01:23.923  6288-6575  WebSocket               com.dspread.mdm.service              I  🔧 C0901 应用信息上送成功 (1)
2025-08-18 18:01:24.422  6288-6575  Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"request_time":"1755511283292","org_request_time":"1755511283758","org_request_id":"1755511283758C0109","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755511283292S0000","serialNo":"01354090202503050399"}
2025-08-18 18:01:24.430  6288-6575  WebSocket               com.dspread.mdm.service              I  🔧 收到响应消息: transCode=S0000, orgRequestId=1755511283758C0109, state=0, remark=
2025-08-18 18:01:24.435  6288-6575  WebSocket               com.dspread.mdm.service              I  🔧 终端信息上传成功
2025-08-18 18:01:24.441  6288-6575  WebSocket               com.dspread.mdm.service              I  🔧 终端信息上传成功标志已设置
2025-08-18 18:01:29.994  6288-6288  Receiver                com.dspread.mdm.service              D  🔧 电源已断开
2025-08-18 18:01:30.017  6288-6288  WebSocket               com.dspread.mdm.service              I  🔧 C0902 主动式上送: charging_state_change (主动: 19)
2025-08-18 18:01:30.086  6288-6288  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0902","serialNo":"01354090202503050399","request_time":"1755511290058","request_id":"1755511290058C0902","version":"1","data":{"batteryLife":85,"batteryHealth":2,"temprature":"30.7","isCharging":"0","isLowBattery":"0","powerOff":"0","outOfBattery":"0"},"myVersionName":"1.0.10.20250818.DSPREAD.MDM.SERVICE","terminalDate":"20250818180130"}
2025-08-18 18:01:30.106  6288-6288  WebSocket               com.dspread.mdm.service              I  🔧 C0902 电池状态上送成功 (1, trigger=charging_state_change)
2025-08-18 18:01:30.116  6288-6288  GeoFence                com.dspread.mdm.service              D  🔧 [GeofenceReceiver] 收到广播: android.intent.action.ACTION_POWER_DISCONNECTED
2025-08-18 18:01:30.128  6288-6288  GeoFence                com.dspread.mdm.service              D  🔧 [GeofenceReceiver] 电源已断开
2025-08-18 18:01:30.149  6288-6288  Receiver                com.dspread.mdm.service              D  🔧 电池状态更新: 电池状态: 电量=85%, 温度=30.7°C, 健康=2, 充电=否, 低电量=否, 耗尽=否
2025-08-18 18:01:30.160  6288-6288  GeoFence                com.dspread.mdm.service              D  🔧 [GeofenceReceiver] 收到广播: android.intent.action.BATTERY_CHANGED
2025-08-18 18:01:30.167  6288-6288  Receiver                com.dspread.mdm.service              D  🔧 屏幕点亮
2025-08-18 18:01:30.174  6288-6288  WebSocket               com.dspread.mdm.service              W  ⚠️ 未知触发条件: screen_on，默认按被动式处理
2025-08-18 18:01:30.190  6288-6288  WebSocket               com.dspread.mdm.service              I  🔧 C0201 被动式上送拒绝: screen_on (拒绝: 3)
2025-08-18 18:01:30.197  6288-6288  WebSocket               com.dspread.mdm.service              I  🔧 C0201 设备状态上送被流量控制阻止: 被动事件 'screen_on' 在平衡模式 - 重要变化下未启用
2025-08-18 18:01:30.203  6288-6288  Receiver                com.dspread.mdm.service              D  🔧 屏幕点亮处理完成
2025-08-18 18:01:30.230  6288-6288  GeoFence                com.dspread.mdm.service              D  🔧 [GeofenceReceiver] 收到广播: android.intent.action.SCREEN_ON
2025-08-18 18:01:30.236  6288-6288  GeoFence                com.dspread.mdm.service              D  🔧 [GeofenceReceiver] 屏幕已开启
2025-08-18 18:01:30.308  6288-6288  Receiver                com.dspread.mdm.service              D  🔧 电池状态更新: 电池状态: 电量=85%, 温度=30.5°C, 健康=2, 充电=否, 低电量=否, 耗尽=否
2025-08-18 18:01:30.315  6288-6288  GeoFence                com.dspread.mdm.service              D  🔧 [GeofenceReceiver] 收到广播: android.intent.action.BATTERY_CHANGED
2025-08-18 18:01:30.626  6288-6288  Receiver                com.dspread.mdm.service              D  🔧 电池状态更新: 电池状态: 电量=85%, 温度=30.5°C, 健康=2, 充电=否, 低电量=否, 耗尽=否
2025-08-18 18:01:30.635  6288-6288  GeoFence                com.dspread.mdm.service              D  🔧 [GeofenceReceiver] 收到广播: android.intent.action.BATTERY_CHANGED
2025-08-18 18:01:32.062  6288-6288  Receiver                com.dspread.mdm.service              D  🔧 电源已连接
2025-08-18 18:01:32.069  6288-6288  WebSocket               com.dspread.mdm.service              I  🔧 C0902 主动式上送: charging_state_change (主动: 20)
2025-08-18 18:01:32.120  6288-6288  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0902","serialNo":"01354090202503050399","request_time":"1755511292096","request_id":"1755511292096C0902","version":"1","data":{"batteryLife":85,"batteryHealth":2,"temprature":"30.6","isCharging":"1","isLowBattery":"0","powerOff":"0","outOfBattery":"0"},"myVersionName":"1.0.10.20250818.DSPREAD.MDM.SERVICE","terminalDate":"20250818180132"}
2025-08-18 18:01:32.125  6288-6288  WebSocket               com.dspread.mdm.service              I  🔧 C0902 电池状态上送成功 (1, trigger=charging_state_change)
2025-08-18 18:01:32.131  6288-6288  GeoFence                com.dspread.mdm.service              D  🔧 [GeofenceReceiver] 收到广播: android.intent.action.ACTION_POWER_CONNECTED
2025-08-18 18:01:32.136  6288-6288  GeoFence                com.dspread.mdm.service              D  🔧 [GeofenceReceiver] 电源已连接
2025-08-18 18:01:32.143  6288-6288  Receiver                com.dspread.mdm.service              D  🔧 电池状态更新: 电池状态: 电量=85%, 温度=30.6°C, 健康=2, 充电=是, 低电量=否, 耗尽=否
2025-08-18 18:01:32.149  6288-6288  GeoFence                com.dspread.mdm.service              D  🔧 [GeofenceReceiver] 收到广播: android.intent.action.BATTERY_CHANGED
2025-08-18 18:01:32.402  6288-6288  Receiver                com.dspread.mdm.service              D  🔧 电池状态更新: 电池状态: 电量=85%, 温度=30.4°C, 健康=2, 充电=是, 低电量=否, 耗尽=否
2025-08-18 18:01:32.409  6288-6288  GeoFence                com.dspread.mdm.service              D  🔧 [GeofenceReceiver] 收到广播: android.intent.action.BATTERY_CHANGED
2025-08-18 18:01:32.594  6288-6576  WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 1
2025-08-18 18:01:32.722  6288-6288  Receiver                com.dspread.mdm.service              D  🔧 电池状态更新: 电池状态: 电量=85%, 温度=30.5°C, 健康=2, 充电=是, 低电量=否, 耗尽=否
2025-08-18 18:01:32.729  6288-6288  GeoFence                com.dspread.mdm.service              D  🔧 [GeofenceReceiver] 收到广播: android.intent.action.BATTERY_CHANGED
2025-08-18 18:01:32.958  6288-6288  Common                  com.dspread.mdm.service              I  ✅ ⏰ 设置任务执行定时器成功，下次执行: 60秒后 (1分钟)
2025-08-18 18:01:32.995  6288-6575  WebSocket               com.dspread.mdm.service              I  🔧 收到服务器 PONG 帧: 1
2025-08-18 18:01:37.554  6288-6575  Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"msg_state":"P","request_time":"1755511296552","data":{"param":"","c_type":"CALL_LOG_STREAM"},"expire_time":1756116096,"tranCode":"SC004","request_id":"1755511296552SC004","version":"1","serialNo":"01354090202503050399"}
2025-08-18 18:01:37.565  6288-6575  WebSocket               com.dspread.mdm.service              I  🔧 准备发送响应确认 - requestId: 1755511296552SC004, needResponse: true
2025-08-18 18:01:37.599  6288-6575  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0000","serialNo":"01354090202503050399","request_time":"1755511297572","request_id":"1755511297572C0000","version":"1","org_request_id":"1755511296552SC004","org_request_time":"1755511296552","response_state":"0","myVersionName":"1.0.10.20250818.DSPREAD.MDM.SERVICE","terminalDate":"20250818180137"}
2025-08-18 18:01:37.636  6288-6575  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0000","serialNo":"01354090202503050399","request_time":"1755511297609","request_id":"1755511297609C0000","version":"1","org_request_id":"1755511296552SC004","org_request_time":"1755511296552","response_state":"0","myVersionName":"1.0.10.20250818.DSPREAD.MDM.SERVICE","terminalDate":"20250818180137"}
2025-08-18 18:01:37.644  6288-6575  WebSocket               com.dspread.mdm.service              I  🔧 已发送C0000响应确认: requestId=1755511296552SC004
2025-08-18 18:01:37.654  6288-6575  WebSocket               com.dspread.mdm.service              I  🔧 处理命令: SC004
2025-08-18 18:01:37.662  6288-6575  WebSocket               com.dspread.mdm.service              I  🔧 收到SC004命令，类型: CALL_LOG_STREAM
2025-08-18 18:01:37.670  6288-6575  WebSocket               com.dspread.mdm.service              I  🔧 服务器请求启动日志流
2025-08-18 18:01:37.678  6288-6575  LogStream               com.dspread.mdm.service              I  🔧 [LogStreamWebSocketHandler] 处理CALL_LOG_STREAM请求
2025-08-18 18:01:37.686  6288-6575  LogStream               com.dspread.mdm.service              I  🔧 [LogStreamWebSocketHandler] 日志流参数: 
2025-08-18 18:01:37.694  6288-6575  LogStream               com.dspread.mdm.service              W  ⚠️ [LogStreamWebSocketHandler] param参数为空，跳过日志流处理
2025-08-18 18:01:39.274  6288-6288  GeoFence                com.dspread.mdm.service              D  🔧 [GeofenceReceiver] 收到广播: com.dspread.mdm.service.ACTION_SYSTEM_TIMER
2025-08-18 18:01:39.284  6288-6288  GeoFence                com.dspread.mdm.service              D  🔧 [GeofenceReceiver] 系统定时器触发
2025-08-18 18:01:39.298  6288-6316  GeoFence                com.dspread.mdm.service              D  🔧 [GeofenceReceiver] 执行系统维护任务
2025-08-18 18:01:39.314  6288-6316  GeoFence                com.dspread.mdm.service              D  🔧 [GeofenceReceiver] 设置定时器成功: com.dspread.mdm.service.ACTION_SYSTEM_TIMER, 间隔: 1分钟
2025-08-18 18:01:39.524  6288-6288  GeoFence                com.dspread.mdm.service              D  🔧 [GeofenceReceiver] 收到广播: com.dspread.mdm.service.ACTION_GEOFENCE_CHECK
2025-08-18 18:01:39.534  6288-6288  GeoFence                com.dspread.mdm.service              D  🔧 [GeofenceReceiver] 地理围栏检查定时器触发
2025-08-18 18:01:39.547  6288-6316  GeoFence                com.dspread.mdm.service              D  🔧 [GeofenceReceiver] 检查GeoFence到期执行情况 GEO_STATUS=false M_GEOFENCE_STATUS=1
2025-08-18 18:01:39.557  6288-6316  GeoFence                com.dspread.mdm.service              D  🔧 [GeofenceHandler] 检查并执行到期的地理围栏Profile
2025-08-18 18:01:39.568  6288-6316  GeoFence                com.dspread.mdm.service              D  🔧 [GeofenceHandler] 没有待执行的Profile
2025-08-18 18:01:39.584  6288-6316  GeoFence                com.dspread.mdm.service              D  🔧 [GeofenceReceiver] 设置定时器成功: com.dspread.mdm.service.ACTION_GEOFENCE_CHECK, 间隔: 1分钟
2025-08-18 18:01:39.774  6288-6316  LogStream               com.dspread.mdm.service              I  🔧 [LogStreamManager] 检查日志队列进行压缩，队列长度: 0
2025-08-18 18:01:39.784  6288-6316  LogStream               com.dspread.mdm.service              I  🔧 [LogCollector] 检查存储空间并清理旧文件
2025-08-18 18:01:39.795  6288-6316  LogStream               com.dspread.mdm.service              I  🔧 [LogCollector] 当前压缩文件总大小: 0B / 250MB
2025-08-18 18:01:39.808  6288-6316  LogStream               com.dspread.mdm.service              I  🔧 [LogCollector] 当前原始日志总大小: 1MB / 250MB
2025-08-18 18:01:42.464  6288-6575  Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"msg_state":"P","request_time":"1755511301320","data":{"param":{"beginDate":"2024-08-18 10:01:41","taskType":"06","endDate":"9999-12-31 23:59:59","serviceId":"856c2e6d2c7c4f60bc0fadcf9b9ad650","serviceName":"Log Streaming","command":"C03","url":"https://smartms.s3.sa-east-1.amazonaws.com/icon/5e80041b23fb441695e88253d4542c5b.png","taskId":"1755511301320"},"c_type":"CLOSE_LOG_STREAM"},"expire_time":1756116101,"tranCode":"SC004","request_id":"1755511301320SC004","version":"1","serialNo":"01354090202503050399"}
2025-08-18 18:01:42.471  6288-6575  WebSocket               com.dspread.mdm.service              I  🔧 准备发送响应确认 - requestId: 1755511301320SC004, needResponse: true
2025-08-18 18:01:42.490  6288-6575  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0000","serialNo":"01354090202503050399","request_time":"1755511302475","request_id":"1755511302475C0000","version":"1","org_request_id":"1755511301320SC004","org_request_time":"1755511301320","response_state":"0","myVersionName":"1.0.10.20250818.DSPREAD.MDM.SERVICE","terminalDate":"20250818180142"}
2025-08-18 18:01:42.510  6288-6575  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0000","serialNo":"01354090202503050399","request_time":"1755511302496","request_id":"1755511302496C0000","version":"1","org_request_id":"1755511301320SC004","org_request_time":"1755511301320","response_state":"0","myVersionName":"1.0.10.20250818.DSPREAD.MDM.SERVICE","terminalDate":"20250818180142"}
2025-08-18 18:01:42.515  6288-6575  WebSocket               com.dspread.mdm.service              I  🔧 已发送C0000响应确认: requestId=1755511301320SC004
2025-08-18 18:01:42.522  6288-6575  WebSocket               com.dspread.mdm.service              I  🔧 处理命令: SC004
2025-08-18 18:01:42.527  6288-6575  WebSocket               com.dspread.mdm.service              I  🔧 收到SC004命令，类型: CLOSE_LOG_STREAM
2025-08-18 18:01:42.534  6288-6575  WebSocket               com.dspread.mdm.service              I  🔧 收到CLOSE_LOG_STREAM命令
2025-08-18 18:01:42.539  6288-6575  WebSocket               com.dspread.mdm.service              I  🔧 服务器请求关闭日志流，立即执行
2025-08-18 18:01:42.546  6288-6575  Common                  com.dspread.mdm.service              D  🔧 ModuleStopManager: 立即停止Log Stream
2025-08-18 18:01:42.553  6288-6575  LogStream               com.dspread.mdm.service              I  🔧 [LogStreamWebSocketHandler] 处理CLOSE_LOG_STREAM请求
2025-08-18 18:01:42.559  6288-6288  Common                  com.dspread.mdm.service              E  ❌ ModuleStopManager: 停止Log Stream异常 (Ask Gemini)
                                                                                                    java.lang.NoSuchMethodException: com.dspread.mdm.service.modules.logstream.LogStreamManager.stopStreaming []
                                                                                                    	at java.lang.Class.getMethod(Class.java:2937)
                                                                                                    	at java.lang.Class.getMethod(Class.java:2449)
                                                                                                    	at com.dspread.mdm.service.modules.ModuleStopManager$stopLogStreamImmediately$1.invokeSuspend(ModuleStopManager.kt:103)
                                                                                                    	at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
                                                                                                    	at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:108)
                                                                                                    	at android.os.Handler.handleCallback(Handler.java:958)
                                                                                                    	at android.os.Handler.dispatchMessage(Handler.java:99)
                                                                                                    	at android.os.Looper.loopOnce(Looper.java:205)
                                                                                                    	at android.os.Looper.loop(Looper.java:294)
                                                                                                    	at android.app.ActivityThread.main(ActivityThread.java:8225)
                                                                                                    	at java.lang.reflect.Method.invoke(Native Method)
                                                                                                    	at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:573)
                                                                                                    	at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:1058)
2025-08-18 18:01:42.564  6288-6575  LogStream               com.dspread.mdm.service              I  🔧 [LogStreamWebSocketHandler] 关闭日志流参数: {"beginDate":"2024-08-18 10:01:41","taskType":"06","endDate":"9999-12-31 23:59:59","serviceId":"856c2e6d2c7c4f60bc0fadcf9b9ad650","serviceName":"Log Streaming","command":"C03","url":"https:\/\/smartms.s3.sa-east-1.amazonaws.com\/icon\/5e80041b23fb441695e88253d4542c5b.png","taskId":"1755511301320"}
2025-08-18 18:01:42.572  6288-6575  LogStream               com.dspread.mdm.service              I  🔧 [LogStreamWebSocketHandler] 关闭日志流服务 - taskType: 06, serviceId: 856c2e6d2c7c4f60bc0fadcf9b9ad650
2025-08-18 18:01:42.578  6288-6575  LogStream               com.dspread.mdm.service              I  🔧 [LogStreamWebSocketHandler] CLOSE_LOG_STREAM taskType=06，执行服务重置
2025-08-18 18:01:42.589  6288-6288  LogStream               com.dspread.mdm.service              I  🔧 [LogStreamWebSocketHandler] 收到CLOSE_LOG_STREAM命令，立即停止日志流
2025-08-18 18:01:42.595  6288-6288  LogStream               com.dspread.mdm.service              I  🔧 [LogStreamManager] 日志流已经停止，无需重复操作
2025-08-18 18:01:42.601  6288-6288  LogStream               com.dspread.mdm.service              I  🔧 [LogStreamWebSocketHandler] 日志流服务停止成功
2025-08-18 18:01:42.609  6288-6288  Receiver                com.dspread.mdm.service              D  🔧 屏幕关闭
2025-08-18 18:01:42.616  6288-6288  WebSocket               com.dspread.mdm.service              W  ⚠️ 未知触发条件: screen_off，默认按被动式处理
2025-08-18 18:01:42.633  6288-6288  WebSocket               com.dspread.mdm.service              I  🔧 C0201 被动式上送拒绝: screen_off (拒绝: 4)
2025-08-18 18:01:42.640  6288-6288  WebSocket               com.dspread.mdm.service              I  🔧 C0201 设备状态上送被流量控制阻止: 被动事件 'screen_off' 在平衡模式 - 重要变化下未启用
2025-08-18 18:01:42.647  6288-6288  Receiver                com.dspread.mdm.service              D  🔧 屏幕关闭处理完成
2025-08-18 18:01:42.656  6288-6288  GeoFence                com.dspread.mdm.service              D  🔧 [GeofenceReceiver] 收到广播: android.intent.action.SCREEN_OFF
2025-08-18 18:01:42.662  6288-6288  GeoFence                com.dspread.mdm.service              D  🔧 [GeofenceReceiver] 屏幕已关闭
2025-08-18 18:01:43.448  6288-6316  Platform                com.dspread.mdm.service              D  🔧 NetworkTrafficMonitor 流量统计保存完成
2025-08-18 18:01:43.486  6288-6575  Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"msg_state":"P","request_time":"1755511302374","data":{"param":{"beginDate":"2024-08-18 10:01:42","taskType":"05","period":"1","endDate":"9999-12-31 23:59:59","serviceId":"856c2e6d2c7c4f60bc0fadcf9b9ad650","serviceName":"Log Streaming","command":"C03","url":"https://smartms.s3.sa-east-1.amazonaws.com/icon/5e80041b23fb441695e88253d4542c5b.png","taskId":"1755511302374"},"c_type":"CALL_LOG_STREAM"},"expire_time":1756116102,"tranCode":"SC004","request_id":"1755511302374SC004","version":"1","serialNo":"01354090202503050399"}
2025-08-18 18:01:43.494  6288-6575  WebSocket               com.dspread.mdm.service              I  🔧 准备发送响应确认 - requestId: 1755511302374SC004, needResponse: true
2025-08-18 18:01:43.515  6288-6575  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0000","serialNo":"01354090202503050399","request_time":"1755511303499","request_id":"1755511303499C0000","version":"1","org_request_id":"1755511302374SC004","org_request_time":"1755511302374","response_state":"0","myVersionName":"1.0.10.20250818.DSPREAD.MDM.SERVICE","terminalDate":"20250818180143"}
2025-08-18 18:01:43.537  6288-6575  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0000","serialNo":"01354090202503050399","request_time":"1755511303522","request_id":"1755511303522C0000","version":"1","org_request_id":"1755511302374SC004","org_request_time":"1755511302374","response_state":"0","myVersionName":"1.0.10.20250818.DSPREAD.MDM.SERVICE","terminalDate":"20250818180143"}
2025-08-18 18:01:43.542  6288-6575  WebSocket               com.dspread.mdm.service              I  🔧 已发送C0000响应确认: requestId=1755511302374SC004
2025-08-18 18:01:43.550  6288-6575  WebSocket               com.dspread.mdm.service              I  🔧 处理命令: SC004
2025-08-18 18:01:43.556  6288-6575  WebSocket               com.dspread.mdm.service              I  🔧 收到SC004命令，类型: CALL_LOG_STREAM
2025-08-18 18:01:43.561  6288-6575  WebSocket               com.dspread.mdm.service              I  🔧 服务器请求启动日志流
2025-08-18 18:01:43.567  6288-6575  LogStream               com.dspread.mdm.service              I  🔧 [LogStreamWebSocketHandler] 处理CALL_LOG_STREAM请求
2025-08-18 18:01:43.576  6288-6575  LogStream               com.dspread.mdm.service              I  🔧 [LogStreamWebSocketHandler] 日志流参数: {"beginDate":"2024-08-18 10:01:42","taskType":"05","period":"1","endDate":"9999-12-31 23:59:59","serviceId":"856c2e6d2c7c4f60bc0fadcf9b9ad650","serviceName":"Log Streaming","command":"C03","url":"https:\/\/smartms.s3.sa-east-1.amazonaws.com\/icon\/5e80041b23fb441695e88253d4542c5b.png","taskId":"1755511302374"}
2025-08-18 18:01:43.583  6288-6575  LogStream               com.dspread.mdm.service              I  🔧 [LogStreamWebSocketHandler] 解析参数 - taskType: 05, serviceId: 856c2e6d2c7c4f60bc0fadcf9b9ad650
2025-08-18 18:01:43.589  6288-6575  LogStream               com.dspread.mdm.service              I  🔧 [LogStreamWebSocketHandler] 时间范围 - 开始: 2024-08-18 10:01:42, 结束: 9999-12-31 23:59:59
2025-08-18 18:01:43.595  6288-6575  LogStream               com.dspread.mdm.service              I  🔧 [LogStreamWebSocketHandler] 上传URL: https://smartms.s3.sa-east-1.amazonaws.com/icon/5e80041b23fb441695e88253d4542c5b.png
2025-08-18 18:01:43.601  6288-6316  LogStream               com.dspread.mdm.service              I  🔧 [LogStreamWebSocketHandler] ========== 开始处理日志流上传 ==========
2025-08-18 18:01:43.607  6288-6316  LogStream               com.dspread.mdm.service              I  🔧 [LogStreamWebSocketHandler] 原始参数 - beginDate: 2024-08-18 10:01:42, endDate: 9999-12-31 23:59:59, period: 1
2025-08-18 18:01:43.616  6288-6316  LogStream               com.dspread.mdm.service              I  🔧 [LogStreamWebSocketHandler] 处理时间范围 - 当前时间: 2025-08-18 18:01:43
2025-08-18 18:01:43.622  6288-6316  LogStream               com.dspread.mdm.service              I  🔧 [LogStreamWebSocketHandler] 检测到不明确时间范围，使用period计算实际时间
2025-08-18 18:01:43.628  6288-6316  LogStream               com.dspread.mdm.service              I  🔧 [LogStreamWebSocketHandler] Period解析结果: 1小时
2025-08-18 18:01:43.634  6288-6316  LogStream               com.dspread.mdm.service              I  🔧 [LogStreamWebSocketHandler] 计算后时间范围 - 开始: 2025-08-18 18:01:43, 结束: 2025-08-18 19:01:43
2025-08-18 18:01:43.639  6288-6316  LogStream               com.dspread.mdm.service              I  🔧 [LogStreamWebSocketHandler] 处理后时间范围 - 开始: 2025-08-18 18:01:43, 结束: 2025-08-18 19:01:43
2025-08-18 18:01:43.655  6288-6316  LogStream               com.dspread.mdm.service              I  🔧 [LogStreamWebSocketHandler] 时间判断 - 当前: 1755511303645, 开始: 1755511303000, 结束: 1755514903000
2025-08-18 18:01:43.660  6288-6316  LogStream               com.dspread.mdm.service              I  🔧 [LogStreamWebSocketHandler] 时间范围检查结果: true
2025-08-18 18:01:43.665  6288-6316  LogStream               com.dspread.mdm.service              I  🔧 [LogStreamWebSocketHandler] 时间范围判断结果 - 是否需要上传: true
2025-08-18 18:01:43.670  6288-6316  LogStream               com.dspread.mdm.service              I  🔧 [LogStreamWebSocketHandler] 步骤1: 上传Recent日志
2025-08-18 18:01:43.676  6288-6316  LogStream               com.dspread.mdm.service              I  🔧 [LogStreamWebSocketHandler] 开始上传Recent日志
2025-08-18 18:01:43.693  6288-6316  LogStream               com.dspread.mdm.service              I  🔧 [LogCollector] 找到最新日志文件: mdm_log_20250818_174238.log
2025-08-18 18:01:43.699  6288-6316  LogStream               com.dspread.mdm.service              I  🔧 [LogStreamWebSocketHandler] 当前日志文件: mdm_log_20250818_174238.log, 大小: 1181606
2025-08-18 18:01:43.705  6288-6316  LogStream               com.dspread.mdm.service              I  🔧 [LogStreamWebSocketHandler] 生成Recent日志 - 原文件大小: 1181743B, Recent限制: 262144B
2025-08-18 18:01:43.710  6288-6316  LogStream               com.dspread.mdm.service              I  🔧 [LogStreamWebSocketHandler] Recent文件路径: /storage/emulated/0/Android/data/com.dspread.mdm.service/files/logs/upload/recent.log
2025-08-18 18:01:43.722  6288-6316  LogStream               com.dspread.mdm.service              I  🔧 [LogStreamWebSocketHandler] 截取最后256KB完成，实际读取: 262144B
2025-08-18 18:01:43.728  6288-6316  LogStream               com.dspread.mdm.service              I  🔧 [LogStreamWebSocketHandler] Recent日志文件生成成功: recent.log, 大小: 262144B
2025-08-18 18:01:43.734  6288-6316  LogStream               com.dspread.mdm.service              I  🔧 [LogStreamWebSocketHandler] Recent日志文件生成成功: recent.log, 大小: 262144
2025-08-18 18:01:43.741  6288-6316  LogStream               com.dspread.mdm.service              I  🔧 [LogStreamWebSocketHandler] 开始压缩Recent日志: recent.log -> recent.gz
2025-08-18 18:01:43.747  6288-6316  LogStream               com.dspread.mdm.service              I  🔧 [LogStreamWebSocketHandler] 压缩目标路径: /storage/emulated/0/Android/data/com.dspread.mdm.service/files/logs/upload/recent.gz
2025-08-18 18:01:43.753  6288-6316  LogStream               com.dspread.mdm.service              I  🔧 [LogProcessor] 开始压缩文件: recent.log
2025-08-18 18:01:43.791  6288-6316  LogStream               com.dspread.mdm.service              I  🔧 [LogProcessor] 压缩完成: recent.log -> recent.gz
2025-08-18 18:01:43.798  6288-6316  LogStream               com.dspread.mdm.service              I  🔧 [LogProcessor] 压缩比: 15.56% (262144B -> 40790B)
2025-08-18 18:01:43.821  6288-6360  LogStream               com.dspread.mdm.service              I  🔧 [LogStreamWebSocketHandler] Recent日志压缩成功: /storage/emulated/0/Android/data/com.dspread.mdm.service/files/logs/upload/recent.gz
2025-08-18 18:01:43.827  6288-6360  LogStream               com.dspread.mdm.service              I  🔧 [LogStreamWebSocketHandler] Recent日志压缩完成: recent.gz, 大小: 40790
2025-08-18 18:01:43.832  6288-6360  LogStream               com.dspread.mdm.service              I  🔧 [LogStreamWebSocketHandler] 开始上传Recent文件到S3
2025-08-18 18:01:43.839  6288-6360  LogStream               com.dspread.mdm.service              I  🔧 [S3LogUploader] 开始上传Recent文件: recent.gz
2025-08-18 18:01:43.845  6288-6360  LogStream               com.dspread.mdm.service              I  🔧 [S3LogUploader] Recent文件S3路径: dev/01354090202503050399/recent.gz
2025-08-18 18:01:43.860  6288-6360  LogStream               com.dspread.mdm.service              I  🔧 [S3LogUploader] 签名字符串: PUT
                                                                                                    wZpNfIeq6HX0+kHV79lcGA==
                                                                                                    application/octet-stream
                                                                                                    Mon, 18 Aug 2025 10:01:43 GMT
                                                                                                    /smartms-applog/dev/01354090202503050399/recent.gz
2025-08-18 18:01:43.867  6288-6360  LogStream               com.dspread.mdm.service              I  🔧 [S3LogUploader] S3上传签名生成完成
2025-08-18 18:01:43.873  6288-6360  LogStream               com.dspread.mdm.service              I  🔧 [S3LogUploader] 上传到URL: https://smartms-applog.s3.sa-east-1.amazonaws.com/log/dev/01354090202503050399/recent.gz
2025-08-18 18:01:44.267  6288-6360  TrafficStats            com.dspread.mdm.service              D  tagSocket(117) with statsTag=0xffffffff, statsUid=-1
2025-08-18 18:01:46.721  6288-6360  LogStream               com.dspread.mdm.service              I  🔧 [S3LogUploader] 上传响应码: 403, 上传字节数: 40790
2025-08-18 18:01:46.735  6288-6360  LogStream               com.dspread.mdm.service              E  ❌ [S3LogUploader] HTTP上传失败，错误信息: <?xml version="1.0" encoding="UTF-8"?>
                                                                                                    <Error><Code>SignatureDoesNotMatch</Code><Message>The request signature we calculated does not match the signature you provided. Check your key and signing method.</Message><AWSAccessKeyId>AKIA25LIDIZ7ISJODMHO</AWSAccessKeyId><StringToSign>PUT
                                                                                                    wZpNfIeq6HX0+kHV79lcGA==
                                                                                                    application/octet-stream
                                                                                                    Mon, 18 Aug 2025 10:01:43 GMT
                                                                                                    /smartms-applog/log/dev/01354090202503050399/recent.gz</StringToSign><SignatureProvided>BpGG4MyrPj3KWyn1kT7JVhEBvls=</SignatureProvided><StringToSignBytes>50 55 54 0a 77 5a 70 4e 66 49 65 71 36 48 58 30 2b 6b 48 56 37 39 6c 63 47 41 3d 3d 0a 61 70 70 6c 69 63 61 74 69 6f 6e 2f 6f 63 74 65 74 2d 73 74 72 65 61 6d 0a 4d 6f 6e 2c 20 31 38 20 41 75 67 20 32 30 32 35 20 31 30 3a 30 31 3a 34 33 20 47 4d 54 0a 2f 73 6d 61 72 74 6d 73 2d 61 70 70 6c 6f 67 2f 6c 6f 67 2f 64 65 76 2f 30 31 33 35 34 30 39 30 32 30 32 35 30 33 30 35 30 33 39 39 2f 72 65 63 65 6e 74 2e 67 7a</StringToSignBytes><RequestId>QZTK8S2ZFVQAH3AQ</RequestId><HostId>5X/cTQcUzISCW9YTv78VklWf/I7Wb5F6PmE+9ihrPHYeZaF3EL3lhGTNu5HmJR20wgq7hDDZVgBpnEK+XR77MA==</HostId></Error>
2025-08-18 18:01:46.740  6288-6360  LogStream               com.dspread.mdm.service              E  ❌ [S3LogUploader] Recent文件上传失败
2025-08-18 18:01:46.747  6288-6387  LogStream               com.dspread.mdm.service              E  ❌ [LogStreamWebSocketHandler] Recent文件S3上传失败
2025-08-18 18:01:46.757  6288-6387  LogStream               com.dspread.mdm.service              E  ❌ [LogStreamWebSocketHandler] Recent日志上传失败
2025-08-18 18:01:46.764  6288-6387  LogStream               com.dspread.mdm.service              I  🔧 [LogStreamWebSocketHandler] 步骤2: 启动压缩包日志上传
2025-08-18 18:01:46.772  6288-6387  LogStream               com.dspread.mdm.service              I  🔧 [LogStreamWebSocketHandler] 开始启动压缩包日志上传
2025-08-18 18:01:46.780  6288-6387  LogStream               com.dspread.mdm.service              I  🔧 [LogStreamWebSocketHandler] 开始处理压缩日志上传
2025-08-18 18:01:46.781  6288-6360  LogStream               com.dspread.mdm.service              I  🔧 [LogStreamManager] 检查日志队列进行压缩，队列长度: 0
2025-08-18 18:01:46.798  6288-6387  LogStream               com.dspread.mdm.service              I  🔧 [LogStreamWebSocketHandler] 找到 0 个压缩文件待上传
2025-08-18 18:01:46.804  6288-6387  LogStream               com.dspread.mdm.service              I  🔧 [LogStreamWebSocketHandler] 压缩日志上传处理完成
2025-08-18 18:01:46.810  6288-6387  LogStream               com.dspread.mdm.service              I  🔧 [LogStreamWebSocketHandler] 压缩包日志上传启动完成
2025-08-18 18:01:46.816  6288-6387  LogStream               com.dspread.mdm.service              I  🔧 [LogStreamWebSocketHandler] 步骤3: 发送C0901应用信息响应
2025-08-18 18:01:46.823  6288-6387  LogStream               com.dspread.mdm.service              I  🔧 [LogStreamWebSocketHandler] 开始发送C0901日志流响应
2025-08-18 18:01:46.834  6288-6387  LogStream               com.dspread.mdm.service              I  🔧 [LogStreamWebSocketHandler] 日志流服务信息构建完成
2025-08-18 18:01:46.843  6288-6387  LogStream               com.dspread.mdm.service              I  🔧 [LogStreamWebSocketHandler] 构建服务信息完成: {"taskId":"1755511302374","serviceId":"856c2e6d2c7c4f60bc0fadcf9b9ad650","serviceName":"Log Stream","taskType":"05","command":"C03","stateDesc":"IMPLEMENTED","beginDate":"2025-08-18 18:01:46","endDate":"9999-12-31 23:59:59","period":"1","request_id":"","request_time":""}
2025-08-18 18:01:46.848  6288-6387  WebSocket               com.dspread.mdm.service              I  🔧 发送C0901应用信息响应（包含服务信息）
2025-08-18 18:01:46.854  6288-6387  WebSocket               com.dspread.mdm.service              I  🔧 服务信息已添加到管理器: 856c2e6d2c7c4f60bc0fadcf9b9ad650
2025-08-18 18:01:46.860  6288-6387  Platform                com.dspread.mdm.service              D  🔧 使用缓存的应用信息
2025-08-18 18:01:46.866  6288-6387  WebSocket               com.dspread.mdm.service              I  🔧 服务信息已添加到C0901响应中
2025-08-18 18:01:46.902  6288-6387  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0901","serialNo":"01354090202503050399","request_time":"1755511306871","request_id":"1755511306871C0901","version":"1","data":{"apkInfo":[{"packName":"com.dspread.mdm.service","apkName":"service","versionCode":13,"versionName":"1.0.10.20250818.DSPREAD.MDM.SERVICE","updateDate":"2025-08-18 17:42:14"},{"packName":"mark.via","apkName":"Via","versionCode":20250117,"versionName":"6.2.0","updateDate":"2025-08-18 17:50:20"}],"sytemInfo":{"androidVersion":"14","buildNumber":"BASE_D30M-MU_GEN_AP_V1.0.8.1_202507032000_ota_test","aspl":"2025-03-05"},"serviceInfo":[{"taskId":"1755511302374","serviceId":"856c2e6d2c7c4f60bc0fadcf9b9ad650","serviceName":"Log Stream","taskType":"05","command":"C03","stateDesc":"IMPLEMENTED","beginDate":"2025-08-18 18:01:46","endDate":"9999-12-31 23:59:59","period":"1","request_id":"","request_time":""}]},"myVersionName":"1.0.10.20250818.DSPREAD.MDM.SERVICE","terminalDate":"20250818180146"}
2025-08-18 18:01:46.907  6288-6387  WebSocket               com.dspread.mdm.service              I  🔧 C0901 应用信息响应（含服务信息）发送成功
2025-08-18 18:01:46.913  6288-6387  LogStream               com.dspread.mdm.service              I  🔧 [LogStreamWebSocketHandler] C0901日志流响应发送完成
2025-08-18 18:01:46.918  6288-6387  LogStream               com.dspread.mdm.service              I  🔧 [LogStreamWebSocketHandler] ========== 日志流上传处理完成 ==========
2025-08-18 18:02:09.821  6288-6387  LogStream               com.dspread.mdm.service              I  🔧 [LogStreamManager] 检查日志队列进行压缩，队列长度: 0
2025-08-18 18:02:09.826  6288-6387  LogStream               com.dspread.mdm.service              I  🔧 [LogCollector] 检查存储空间并清理旧文件
2025-08-18 18:02:09.832  6288-6387  LogStream               com.dspread.mdm.service              I  🔧 [LogCollector] 当前压缩文件总大小: 0B / 250MB
2025-08-18 18:02:09.839  6288-6387  LogStream               com.dspread.mdm.service              I  🔧 [LogCollector] 当前原始日志总大小: 1MB / 250MB
2025-08-18 18:02:17.221  6288-6387  Common                  com.dspread.mdm.service              D  🔧 ServiceGuardEventHandler 开始执行保活动作
2025-08-18 18:02:17.232  6288-6387  Common                  com.dspread.mdm.service              I  ✅ ⏰ 设置服务守护定时器成功，下次执行: 120秒后 (2分钟)
2025-08-18 18:02:17.241  6288-6387  ContextImpl             com.dspread.mdm.service              W  Calling a method in the system process without a qualified user: android.app.ContextImpl.startService:1899 android.content.ContextWrapper.startService:825 com.dspread.mdm.service.broadcast.handlers.service.ServiceGuardEventHandler.startKeepAliveService:211 com.dspread.mdm.service.broadcast.handlers.service.ServiceGuardEventHandler.performKeepAliveActions:193 com.dspread.mdm.service.broadcast.handlers.service.ServiceGuardEventHandler.access$performKeepAliveActions:24 
2025-08-18 18:02:17.247  6288-6288  Service                 com.dspread.mdm.service              D  🔧 ServiceKeepAliveService 保活服务启动
2025-08-18 18:02:17.247  6288-6387  Common                  com.dspread.mdm.service              D  🔧 ServiceGuardEventHandler 启动保活服务
2025-08-18 18:02:17.256  6288-6288  Platform                com.dspread.mdm.service              D  🔧 WakeLockManager WakeLock不存在或未持有: ServiceKeepAlive
2025-08-18 18:02:17.262  6288-6387  Common                  com.dspread.mdm.service              D  🔧 ServiceGuardEventHandler 保活动作执行完成
2025-08-18 18:02:17.268  6288-6288  Platform                com.dspread.mdm.service              D  🔧 WakeLockManager 获取WakeLock成功: ServiceKeepAlive (超时: 30000ms)
2025-08-18 18:02:17.666  6288-6288  Receiver                com.dspread.mdm.service              D  🔧 电池状态更新: 电池状态: 电量=85%, 温度=30.6°C, 健康=2, 充电=是, 低电量=否, 耗尽=否
2025-08-18 18:02:17.677  6288-6288  GeoFence                com.dspread.mdm.service              D  🔧 [GeofenceReceiver] 收到广播: android.intent.action.BATTERY_CHANGED
2025-08-18 18:02:20.268  6288-6387  Common                  com.dspread.mdm.service              D  🔧 ServiceGuardEventHandler WakeLock已释放