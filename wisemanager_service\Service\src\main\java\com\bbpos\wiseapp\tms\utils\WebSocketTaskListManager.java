package com.bbpos.wiseapp.tms.utils;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import android.content.pm.PackageManager;
import android.os.Build;
import android.preference.PreferenceManager;
import android.text.TextUtils;

import com.bbpos.wiseapp.logger.BBLog;
import com.bbpos.wiseapp.param.manager.ParamModel;
import com.bbpos.wiseapp.service.common.UsualData;
import com.bbpos.wiseapp.service.contentprovider.db.ParamDbOperation;
import com.bbpos.wiseapp.system.api.DeviceInfoApi;
import com.bbpos.wiseapp.system.api.SysIntermediateApi;
import com.bbpos.wiseapp.tms.service.WSTaskApkExpireUninstallService;
import com.bbpos.wiseapp.utils.SharedPreferencesUtils;
import com.bbpos.wiseapp.websocket.WebSocketCenter;
import com.bbpos.wiseapp.websocket.handler.InAppVarHandler;
import com.bbpos.wiseapp.websocket.handler.RulebasedHandler;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Set;

@SuppressLint({ "UseSparseArrays", "UseValueOf", "SimpleDateFormat", "NewApi"})
/**
 * 本地任务列表管理
 * 固化任务数据至shareprefrence中
 * */
public class WebSocketTaskListManager {
	/**任务执行时间map,用于发出timeup广播*/
	public static final HashMap<Long,Long> wstaskExeTimeMap = new HashMap<Long,Long>();
	/**任务过期时间map,用于发出timeup广播*/
	public static final HashMap<Long,Long> wstaskExpTimeMap = new HashMap<Long,Long>();
	public static final SharedPreferences sp = PreferenceManager.getDefaultSharedPreferences(ContextUtil.getInstance());
	
	/**与本地任务列表进行对比、更新*/
	public static void updateWSTaskList(String requestId, String requestTime, JSONArray taskList, String silentInstall){
		try {
			if (taskList == null || taskList.length() == 0) {
				BBLog.e(Constants.TAG, "taskList is empty.updateWSTaskList failed");
				return;
			}
			String localTaskListStr = sp.getString(SPKeys.WEBSOCKET_TASK_LIST, "");
			BBLog.d(Constants.TAG, "当前本地: localTaskListStr  -->"+localTaskListStr);
			BBLog.d(Constants.TAG, "新下发的: taskList  -->"+taskList.toString());
			if (!Helpers.isStrNoEmpty(localTaskListStr)) {
				BBLog.d(Constants.TAG, "本地为空，直接新增保存");
				for (int i = 0; i < taskList.length(); i++) {
					JSONObject taskJsonObj = (JSONObject) taskList.get(i);
					taskJsonObj.put(ParameterName.request_id, requestId);
					taskJsonObj.put(ParameterName.request_time, requestTime);
					if (!TextUtils.isEmpty(silentInstall)) {
						taskJsonObj.put(ParameterName.silent_install, silentInstall);
					}

					//参数处理
					if (taskJsonObj.has(ParameterName.inAppVal)) {
						List<ParamModel> params = new ArrayList<ParamModel>();
						JSONArray inAppVal = null;
						inAppVal = taskJsonObj.getJSONArray(ParameterName.inAppVal);
						for (int index = 0; index < inAppVal.length(); index++) {
							JSONObject inAppValJson = inAppVal.getJSONObject(index);
							String idx = inAppValJson.optString(ParameterName.idx);
							String inKey = inAppValJson.optString(ParameterName.inKey);
							String inValue = inAppValJson.optString(ParameterName.inValue);
							String dataSrc = inAppValJson.optString(ParameterName.dataSrc);
							ParamModel paramModel = new ParamModel(idx, taskJsonObj.getString(ParameterName.packName), inKey, inValue, dataSrc);
							params.add(paramModel);
						}

						if (ParamDbOperation.updateParams(ContextUtil.getInstance(), taskJsonObj.getString(ParameterName.packName), params)) {
							InAppVarHandler.sendBroadcastToNotify(taskJsonObj.getString(ParameterName.packName));
						}
					}
				}
				sp.edit().putString(SPKeys.WEBSOCKET_TASK_LIST, taskList.toString()).commit();
			} else {
				HashMap<String, JSONObject> taskJsonMap = new HashMap<String, JSONObject>();
				for (int i = 0; i < taskList.length(); i++) {
					JSONObject taskJsonObj = (JSONObject) taskList.get(i);
					taskJsonObj.put(ParameterName.request_id, requestId);
					taskJsonObj.put(ParameterName.request_time, requestTime);
					BBLog.d(Constants.TAG, "新下发的，逐条: taskJsonObj  -->"+taskJsonObj.toString());

//					if (!isWSAppPlusTaskExist(taskJsonObj)) {
						taskJsonMap.put(taskJsonObj.getString(ParameterName.taskId), taskJsonObj);
//					}

					//参数处理
					if (taskJsonObj.has(ParameterName.inAppVal)) {
						List<ParamModel> params = new ArrayList<ParamModel>();
						JSONArray inAppVal = null;
						inAppVal = taskJsonObj.getJSONArray(ParameterName.inAppVal);
						for (int index = 0; index < inAppVal.length(); index++) {
							JSONObject inAppValJson = inAppVal.getJSONObject(index);
							String idx = inAppValJson.optString(ParameterName.idx);
							String inKey = inAppValJson.optString(ParameterName.inKey);
							String inValue = inAppValJson.optString(ParameterName.inValue);
							String dataSrc = inAppValJson.optString(ParameterName.dataSrc);
							ParamModel paramModel = new ParamModel(idx, taskJsonObj.getString(ParameterName.packName), inKey, inValue, dataSrc);
							params.add(paramModel);
						}

						if (ParamDbOperation.updateParams(ContextUtil.getInstance(), taskJsonObj.getString(ParameterName.packName), params)) {
							InAppVarHandler.sendBroadcastToNotify(taskJsonObj.getString(ParameterName.packName));
						}
					}
				}
				
				JSONArray localTaskList = new JSONArray(localTaskListStr);
				List<Integer> toDeleteIndexs = new ArrayList<Integer>();
				//使用新的taskjson对本地任务进行更新
				for (int i = 0; i < localTaskList.length(); i++) {
					JSONObject localTaskJsonObj = (JSONObject) localTaskList.get(i);
					String taskId = localTaskJsonObj.getString(ParameterName.taskId);

					if(taskJsonMap.get(taskId) != null){
						if (isWSTaskCompleted(localTaskJsonObj)) {
							BBLog.w(BBLog.TAG, "updateWSTaskList 找到相同但已完成的任务，更新：" + localTaskJsonObj);
							Helpers.updateWSTaskJsonObj(localTaskJsonObj, taskJsonMap.get(taskId));
						}
						taskJsonMap.remove(taskId);
					}
					/*if (TaskType.UPDATE_APK.equals(localTaskJsonObj.get(ParameterName.taskType)) && isWSTaskDownloadFailed(localTaskJsonObj)) {
						//如果是APP+推送的，并且是下載失敗的情況，重新做
						localTaskJsonObj.put(ParameterName.taskResult, TaskState.TODO);
					} else */if ((localTaskJsonObj.has(ParameterName.delete) && Constants.TASK_DELETE_FLG.equals(localTaskJsonObj.get(ParameterName.delete)))
						|| (!TaskType.UPDATE_APK.equals(localTaskJsonObj.get(ParameterName.taskType)) && isWSTaskCompleted(localTaskJsonObj))
						|| (TaskType.UPDATE_APK.equals(localTaskJsonObj.get(ParameterName.taskType)) && "9999-12-31 23:59:59".equals(localTaskJsonObj.get(ParameterName.endDate)) && isWSTaskCompleted(localTaskJsonObj))) {
						BBLog.w(BBLog.TAG, "updateWSTaskList 刪除失效：" + localTaskJsonObj);
						toDeleteIndexs.add(new Integer(i));
					} else {
						String beginDateStr = localTaskJsonObj.getString(ParameterName.beginDate);
						String endDateStr = localTaskJsonObj.getString(ParameterName.endDate);
						long nowTime = System.currentTimeMillis();
						try {
							long begTime;
							long endTime;
							if (DateTimeUtils.isNumeric(beginDateStr) && DateTimeUtils.isNumeric(endDateStr)) {
								begTime = new Long(beginDateStr).longValue();
								endTime = new Long(endDateStr).longValue();
							} else {
								SimpleDateFormat sdf = new SimpleDateFormat(Constants.dateFormat);
								begTime = sdf.parse(beginDateStr).getTime();
								endTime = sdf.parse(endDateStr).getTime();
							}

							if ((endTime + 90000) < nowTime) {
								//已过时 刪除
								BBLog.w(BBLog.TAG, "updateWSTaskList 刪除已過時：" + localTaskJsonObj);
								toDeleteIndexs.add(new Integer(i));
							}
						} catch (Exception e) {
							e.printStackTrace();
						}
					}
				}
				
				//删除标志为delete的任务
				if (Build.VERSION.SDK_INT>=Build.VERSION_CODES.KITKAT) {
					for (int i = toDeleteIndexs.size()-1; i >= 0; i--) {
						localTaskList.remove(toDeleteIndexs.get(i));
					}
				} else{
					for (int i = toDeleteIndexs.size() - 1; i >= 0; i--) {
						localTaskList = Helpers.remove(localTaskList, toDeleteIndexs.get(i));
					}
				}
				//加入新任务
				Set<String> newTaskIdSet = taskJsonMap.keySet();
				Iterator<String> newTaskIdIt = newTaskIdSet.iterator();
				while (newTaskIdIt.hasNext()) {
					String taskIdTmp = newTaskIdIt.next();
					JSONObject taskJsonObj = taskJsonMap.get(taskIdTmp);
					BBLog.d(Constants.TAG, "加入新任务: newTaskIdIt  -->"+taskJsonObj.toString());
					localTaskList.put(taskJsonObj);
				}
				//更新至sp
				sp.edit().putString(SPKeys.WEBSOCKET_TASK_LIST, localTaskList.toString()).commit();
				BBLog.d(Constants.TAG, "保存结果列表: localTaskList  -->"+localTaskList.toString());
			}
		} catch (JSONException e) {
			e.printStackTrace();
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	/**获取所有任务的执行时间，将可立即执行的任务时间用零代表*/
	public static HashMap<Long,Long> updateTaskExeTime(){
		try {
			wstaskExeTimeMap.clear();

			String localTaskListStr = sp.getString(SPKeys.WEBSOCKET_TASK_LIST, "");
			if (!Helpers.isStrNoEmpty(localTaskListStr)) {
				return wstaskExeTimeMap;
			} else {
				JSONArray localTaskList = new JSONArray(localTaskListStr);
				long nowTime = System.currentTimeMillis();
				BBLog.w(BBLog.TAG, "updateTaskExeTime localTaskList.length = " + localTaskList.length());
				for (int i = 0; i < localTaskList.length(); i++) {
					JSONObject localTaskJsonObj = (JSONObject) localTaskList.get(i);
					if (isWSTaskCompleted(localTaskJsonObj) || isWSTaskWaiting(localTaskJsonObj)) {
						continue;
					}
					BBLog.w(BBLog.TAG, "找到待执行任务 localTaskJsonObj = " + localTaskJsonObj);
					String beginDateStr = localTaskJsonObj.getString(ParameterName.beginDate);
					String endDateStr = localTaskJsonObj.getString(ParameterName.endDate);

					try {
						long begTime;
						long endTime;
						if (DateTimeUtils.isNumeric(beginDateStr) && DateTimeUtils.isNumeric(endDateStr)) {
							begTime = new Long(beginDateStr).longValue();
							endTime = new Long(endDateStr).longValue();
						} else {
							BBLog.w(BBLog.TAG, "beginDateStr=" + beginDateStr + "  endDateStr=" + endDateStr);
							SimpleDateFormat sdf = new SimpleDateFormat(Constants.dateFormat);
							begTime = sdf.parse(beginDateStr).getTime();
							endTime = sdf.parse(endDateStr).getTime();
						}

						BBLog.w(BBLog.TAG, "nowTime=" + nowTime + "  begTime=" + begTime + "  endTime=" + endTime);

						if (nowTime < begTime) {
							//即刻执行
							wstaskExeTimeMap.put(begTime, new Long(0));
						} else if (begTime < nowTime && nowTime < endTime) {
							wstaskExeTimeMap.put(new Long(0), new Long(0));
						} else if (endTime < nowTime) {
							//已过时 不执行
						}
					} catch (NumberFormatException e) {
						e.printStackTrace();
						removeWSTaskJsonObjById(SPKeys.WEBSOCKET_TASK_LIST, localTaskJsonObj.getString(ParameterName.taskId));
					}
				}
			}
		} catch (JSONException e) {
			e.printStackTrace();
		} catch (Exception e) {
			e.printStackTrace();
		}
		return wstaskExeTimeMap;
	}

	/**获取已到执行时间的待执行任务*/
	public static JSONObject getNextTodoWSTask() {
		try {
			String localTaskListStr = sp.getString(SPKeys.WEBSOCKET_TASK_LIST, "");
			if (!Helpers.isStrNoEmpty(localTaskListStr)) {
				return null;
			} else {
				long nowTime = System.currentTimeMillis();
				JSONArray localTaskList = new JSONArray(localTaskListStr);
				for (int i = 0; i < localTaskList.length(); i++) {
					JSONObject localTaskJsonObj = (JSONObject) localTaskList.get(i);
					if(isWSTaskTodo(localTaskJsonObj)){
						//状态为空或todo
						//任务执行时间判断
						String beginDateStr = localTaskJsonObj.getString(ParameterName.beginDate);
						String endDateStr = localTaskJsonObj.getString(ParameterName.endDate);

						long begTime;
						long endTime;
						if (DateTimeUtils.isNumeric(beginDateStr) && DateTimeUtils.isNumeric(endDateStr)) {
							begTime = new Long(beginDateStr).longValue();
							endTime = new Long(endDateStr).longValue();
						} else {
							SimpleDateFormat sdf = new SimpleDateFormat(Constants.dateFormat);
							begTime = sdf.parse(beginDateStr).getTime();
							endTime = sdf.parse(endDateStr).getTime();
						}

						if (nowTime < begTime || nowTime > endTime){
							BBLog.v(Constants.TAG, localTaskJsonObj.getString(ParameterName.taskId)+" not arrive time."+beginDateStr+"-"+endDateStr);
							continue;
						}
						
						return localTaskJsonObj;
					}
				}
			}
		} catch (JSONException e) {
			e.printStackTrace();
		} catch (Exception e) {
			e.printStackTrace();
		}
		return null;
	}
	
	public static void updateWSTaskState(String taskId, String taskState) {
		try {
			String localTaskListStr = sp.getString(SPKeys.WEBSOCKET_TASK_LIST, "");
			if (!Helpers.isStrNoEmpty(localTaskListStr))
				return;
			JSONArray localTaskList = new JSONArray(localTaskListStr);
			for (int i = 0; i < localTaskList.length(); i++) {
				JSONObject localTaskJsonObj = (JSONObject) localTaskList.get(i);
				String taskIdTmp = localTaskJsonObj.getString(ParameterName.taskId);
				if (taskId.equals(taskIdTmp)) {
					BBLog.e(Constants.TAG, "updateWSTaskState " + taskState);
					localTaskJsonObj.put(ParameterName.taskResult, taskState);
					break;
				}
			}
			sp.edit().putString(SPKeys.WEBSOCKET_TASK_LIST, localTaskList.toString()).commit();
		} catch (JSONException e) {
			e.printStackTrace();
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	public static void setWSTaskToDelete(String taskId) {
		try {
			String localTaskListStr = sp.getString(SPKeys.WEBSOCKET_TASK_LIST, "");
			if (!Helpers.isStrNoEmpty(localTaskListStr))
				return;
			JSONArray localTaskList = new JSONArray(localTaskListStr);
			for (int i = 0; i < localTaskList.length(); i++) {
				JSONObject localTaskJsonObj = (JSONObject) localTaskList.get(i);
				String taskIdTmp = localTaskJsonObj.getString(ParameterName.taskId);
				if (taskId.equals(taskIdTmp)) {
					localTaskJsonObj.put(ParameterName.delete, Constants.TASK_DELETE_FLG);
				}
			}
			sp.edit().putString(SPKeys.WEBSOCKET_TASK_LIST, localTaskList.toString()).commit();
		} catch (JSONException e) {
			e.printStackTrace();
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	
	/**根据任务号返回任务json*/
	public static JSONObject getWSTaskJsonObjById(String taskId) {
		try {
			String localTaskListStr = sp.getString(SPKeys.WEBSOCKET_TASK_LIST, "");
			if (!Helpers.isStrNoEmpty(localTaskListStr))
				return null;
			JSONArray localTaskList = new JSONArray(localTaskListStr);
			for (int i = 0; i < localTaskList.length(); i++) {
				JSONObject localTaskJsonObj = (JSONObject) localTaskList.get(i);
				String taskIdTmp = localTaskJsonObj.getString(ParameterName.taskId);
				if (taskId.equals(taskIdTmp)) {
					return localTaskJsonObj;
				}
			}
		} catch (JSONException e) {
			e.printStackTrace();
		} catch (Exception e) {
			e.printStackTrace();
		}
		return null;
	}

	public static void removeWSTaskJsonObjById(String taskList, String taskId) {
		try {
			String localTaskListStr = sp.getString(taskList, "");
			if (!Helpers.isStrNoEmpty(localTaskListStr))
				return;
			BBLog.i(Constants.TAG, "removeWSTaskJsonObjById: " + taskId);
			JSONArray localTaskList = new JSONArray(localTaskListStr);
			for (int i = 0; i < localTaskList.length(); i++) {
				JSONObject localTaskJsonObj = (JSONObject) localTaskList.get(i);
				String taskIdTmp = localTaskJsonObj.getString(ParameterName.taskId);
				if (taskId.equals(taskIdTmp)) {
					if(Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
						localTaskList.remove(i);
					} else {
						localTaskList = Helpers.remove(localTaskList, i);
					}
					sp.edit().putString(taskList, localTaskList.toString()).commit();
					return;
				}
			}
		} catch (JSONException e) {
			e.printStackTrace();
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	private static boolean isWSTaskSuccessed(JSONObject taskJsonObj) {
		try {
			if(!taskJsonObj.has(ParameterName.taskResult)) {
				return false;
			}
			String taskResult = taskJsonObj.getString(ParameterName.taskResult);
			if (!Helpers.isStrNoEmpty(taskResult)) {
				return false;
			} else if (taskResult.equals(TaskState.SUCCESSED) || taskResult.equals(TaskState.INSTALL_SUCCESS)
					|| taskResult.equals(TaskState.UNINSTALL_SUCCESS) || taskResult.equals(TaskState.UPDATE_SUCCESS)) {
				return true;
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return false;
	}

	private static boolean isWSTaskDownloadFailed(JSONObject taskJsonObj) {
		try {
			if(!taskJsonObj.has(ParameterName.taskResult)) {
				return false;
			}
			String taskResult = taskJsonObj.getString(ParameterName.taskResult);
			if (!Helpers.isStrNoEmpty(taskResult)) {
				return false;
			} else if (taskResult.equals(TaskState.DOWNLOAD_FAILED)) {
				return true;
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return false;
	}
	
	private static boolean isWSTaskFailed(JSONObject taskJsonObj) {
		try {
			if(!taskJsonObj.has(ParameterName.taskResult)) {
				return false;
			}
			String taskResult = taskJsonObj.getString(ParameterName.taskResult);
			if (!Helpers.isStrNoEmpty(taskResult)) {
				return false;
			} else if (taskResult.equals(TaskState.FAILED) || taskResult.equals(TaskState.DOWNLOAD_FAILED)
					|| taskResult.equals(TaskState.INSTALL_FAILED) || taskResult.equals(TaskState.RELY_FAILED)
					|| taskResult.equals(TaskState.SERVER_FAILED) || taskResult.equals(TaskState.UNINSTALL_FAILED)
					|| taskResult.equals(TaskState.UPDATE_FAILED)) {
				return true;
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return false;
	}

	private static boolean isWSTaskFailedForResetTask(JSONObject taskJsonObj) {
		try {
			if(!taskJsonObj.has(ParameterName.taskResult)) {
				return false;
			}
			String taskResult = taskJsonObj.getString(ParameterName.taskResult);
			if (!Helpers.isStrNoEmpty(taskResult)) {
				return false;
			} else if (taskResult.equals(TaskState.FAILED) //|| taskResult.equals(TaskState.DOWNLOAD_FAILED)
					|| taskResult.equals(TaskState.INSTALL_FAILED) || taskResult.equals(TaskState.RELY_FAILED)
					|| taskResult.equals(TaskState.SERVER_FAILED) || taskResult.equals(TaskState.UNINSTALL_FAILED)
					|| taskResult.equals(TaskState.UPDATE_FAILED)) {
				return true;
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return false;
	}
	
	public static boolean isWSTaskCompleted(JSONObject taskJsonObj){
		if(isWSTaskFailed(taskJsonObj) || isWSTaskSuccessed(taskJsonObj)) {
			return true;
		} else {
			return false;
		}
	}

	public static boolean isWSTaskWaiting(JSONObject taskJsonObj){
		try {
			if(!taskJsonObj.has(ParameterName.taskResult)) {
				return false;
			}
			String taskResult = taskJsonObj.getString(ParameterName.taskResult);
			if (!Helpers.isStrNoEmpty(taskResult)) {
				return false;
			} else if (taskResult.equals(TaskState.WATING)) {
				return true;
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return false;
	}

	private static boolean isWSTaskTodo(JSONObject localTaskJsonObj) throws JSONException {
		if (!localTaskJsonObj.has(ParameterName.taskResult)
				|| !Helpers.isStrNoEmpty(localTaskJsonObj.getString(ParameterName.taskResult))
				|| TaskState.TODO.equals(localTaskJsonObj.getString(ParameterName.taskResult))
				|| (TaskType.OSUPDATE.equals(localTaskJsonObj.getString(ParameterName.taskType))&&TaskState.DOWNLOAD_FAILED.equals(localTaskJsonObj.getString(ParameterName.taskResult)))) {
			return true;
		} else {
			return false;
		}
	}

	/**将未完成的任务 状态置为todo*/
	public static void resetDoingWSTaskState() {
		try {
			String localTaskListStr = sp.getString(SPKeys.WEBSOCKET_TASK_LIST, "");
			if(localTaskListStr.equals("")) {
				return;
			}
			JSONArray localWSTaskList = new JSONArray(localTaskListStr);
			BBLog.i(Constants.TAG, "当前任务个数：" + localWSTaskList.length());
			String taskId = SharedPreferencesUtils.getSharePreferencesValue(UsualData.SHARED_PREFERENCES_OSUPDATE_TASKID, "");
			for (int i = 0; i < localWSTaskList.length(); i++) {
				JSONObject localTaskJsonObj = (JSONObject) localWSTaskList.get(i);
				BBLog.i(Constants.TAG, "localTaskJsonObj: " + localTaskJsonObj);
				if (isWSTaskFailed(localTaskJsonObj) || isWSTaskSuccessed(localTaskJsonObj)
				|| (TaskType.UPDATE_APK.equals(localTaskJsonObj.getString(ParameterName.taskType))&&UsualData.SERVICE_PACKAGE_NAME.equals(localTaskJsonObj.getString(ParameterName.packName))&&TaskState.INSTALL_ING.equals(localTaskJsonObj.getString(ParameterName.taskResult)))){// || (TaskType.OSUPDATE.equals(localTaskJsonObj.getString(ParameterName.taskType))&&localTaskJsonObj.has(ParameterName.taskResult)&& TaskState.DOWNLOAD_SUCCESS.equals(localTaskJsonObj.getString(ParameterName.taskResult)))) {
					continue;
				} else if (!TextUtils.isEmpty(taskId)
						&& taskId.equals(localTaskJsonObj.getString(ParameterName.taskId))
						&& TaskState.UPDATE_ING.equals(localTaskJsonObj.getString(ParameterName.taskResult))) {
					continue;
				} else {
					BBLog.i(Constants.TAG, "resetDoingTaskState: " + localTaskJsonObj.toString());
					updateWSTaskState(localTaskJsonObj.getString(ParameterName.taskId), TaskState.TODO);
				}
			}
		} catch (JSONException e) {
			e.printStackTrace();
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	public static boolean isWSAppPlusTaskExist(JSONObject jsonObject) {
		String appId = "";
		try {
			if (!TaskType.UPDATE_APK.equals(jsonObject.getString(ParameterName.taskType))) {
				return false;
			} else {
				appId = jsonObject.getString(ParameterName.appId);
				String localTaskListStr = sp.getString(SPKeys.WEBSOCKET_TASK_LIST, "");
				if (!Helpers.isStrNoEmpty(localTaskListStr))
					return false;
				JSONArray localTaskList = new JSONArray(localTaskListStr);
				for (int i = 0; i < localTaskList.length(); i++) {
					JSONObject localTaskJsonObj = (JSONObject) localTaskList.get(i);
					if(TaskType.UPDATE_APK.equals(jsonObject.getString(ParameterName.taskType)) && localTaskJsonObj.has(ParameterName.appId)) {
						if (appId.equals(localTaskJsonObj.getString(ParameterName.appId))
						&& localTaskJsonObj.getString(ParameterName.apkName).equals(jsonObject.getString(ParameterName.apkName))
						&& localTaskJsonObj.getString(ParameterName.versionName).equals(jsonObject.getString(ParameterName.versionName))
						&& localTaskJsonObj.getString(ParameterName.versionCode).equals(jsonObject.getString(ParameterName.versionCode))
						&& !isWSTaskCompleted(localTaskJsonObj)) {
							return true;
						}
					}
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return false;
	}

	public static boolean isAppFromAppPlus(String packageName, String versionName, String versionCode) {
		try {
			String localTaskListStr = sp.getString(SPKeys.WEBSOCKET_TASK_LIST, "");
			if (!Helpers.isStrNoEmpty(localTaskListStr))
				return false;
			JSONArray localTaskList = new JSONArray(localTaskListStr);
			for (int i = 0; i < localTaskList.length(); i++) {
				JSONObject localTaskJsonObj = (JSONObject) localTaskList.get(i);
				if(TaskType.UPDATE_APK.equals(localTaskJsonObj.getString(ParameterName.taskType))) {
					if (localTaskJsonObj.getString(ParameterName.pkgName).equals(packageName)
							&& localTaskJsonObj.getString(ParameterName.versionName).equals(versionName)
							&& localTaskJsonObj.getString(ParameterName.versionCode).equals(versionCode)
							&& isWSTaskSuccessed(localTaskJsonObj)) {
						BBLog.w(BBLog.TAG, packageName + "属于APP+推送的APP，不删除");
						return true;
					}
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return false;
	}

	public static void checkExpireAppPlusTask(Context context) {
		try {
			String localTaskListStr = sp.getString(SPKeys.WEBSOCKET_TASK_LIST, "");
			if (!Helpers.isStrNoEmpty(localTaskListStr))
				return;
			JSONArray localTaskList = new JSONArray(localTaskListStr);
			for (int i = 0; i < localTaskList.length(); i++) {
				JSONObject localTaskJsonObj = (JSONObject) localTaskList.get(i);
				if(TaskType.UPDATE_APK.equals(localTaskJsonObj.getString(ParameterName.taskType))) {
					String beginDateStr = localTaskJsonObj.getString(ParameterName.beginDate);
					String endDateStr = localTaskJsonObj.getString(ParameterName.endDate);
					long nowTime = System.currentTimeMillis();
					try {
						long endTime;
						if (DateTimeUtils.isNumeric(beginDateStr) && DateTimeUtils.isNumeric(endDateStr)) {
							endTime = new Long(endDateStr).longValue();
						} else {
							SimpleDateFormat sdf = new SimpleDateFormat(Constants.dateFormat);
							endTime = sdf.parse(endDateStr).getTime();
						}

						if (endTime < nowTime) {
							//已过时 刪除
							Intent serviceIntent = new Intent(context, WSTaskApkExpireUninstallService.class);
							serviceIntent.putExtra(ParameterName.taskId, localTaskJsonObj.getString(ParameterName.taskId));
							serviceIntent.putExtra(ParameterName.packName, localTaskJsonObj.getString(ParameterName.packName));
							serviceIntent.putExtra(ParameterName.apkName, localTaskJsonObj.getString(ParameterName.apkName));
							context.startService(serviceIntent);
						}
					} catch (Exception e) {
						e.printStackTrace();
					}
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	public static void checkExpireServiceAppTask(Context context) {
		try {
			String localTaskListStr = sp.getString(SPKeys.WEBSOCKET_SERVICE_LIST, "");
			if (!Helpers.isStrNoEmpty(localTaskListStr))
				return;
			JSONArray serviceList = new JSONArray(localTaskListStr);
			for (int i = 0; i < serviceList.length(); i++) {
				JSONObject localServiceJsonObj = (JSONObject) serviceList.get(i);
				String beginDateStr = localServiceJsonObj.getString(ParameterName.beginDate);
				String endDateStr = localServiceJsonObj.getString(ParameterName.endDate);
				long nowTime = System.currentTimeMillis();
				try {
					long endTime;
					if (DateTimeUtils.isNumeric(beginDateStr) && DateTimeUtils.isNumeric(endDateStr)) {
						endTime = new Long(endDateStr).longValue();
					} else {
						SimpleDateFormat sdf = new SimpleDateFormat(Constants.dateFormat);
						endTime = sdf.parse(endDateStr).getTime();
					}

					if (endTime < nowTime) {
						//已过时 刪除
						for (int index = 0; index<RulebasedHandler.service_code.length; index++) {
							if (RulebasedHandler.service_code[index].equals(localServiceJsonObj.getString(ParameterName.command))) {
								RulebasedHandler.executeServiceFallback(localServiceJsonObj);
								break;
							}
						}
					}
				} catch (Exception e) {
					e.printStackTrace();
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	public static void checkServiceUpdateResult() {
		try {
			String localTaskListStr = sp.getString(SPKeys.WEBSOCKET_TASK_LIST, "");
			if (!Helpers.isStrNoEmpty(localTaskListStr)){
				return;
			}
			int versionCode = ContextUtil.getInstance().getPackageManager().getPackageInfo(ContextUtil.getInstance().getPackageName(), 0).versionCode;
			String versionName = ContextUtil.getInstance().getPackageManager().getPackageInfo(ContextUtil.getInstance().getPackageName(), 0).versionName;
			JSONArray localTaskList = new JSONArray(localTaskListStr);
			for (int i = 0; i < localTaskList.length(); i++) {
				JSONObject localTaskJsonObj = (JSONObject) localTaskList.get(i);
				if(TaskType.UPDATE_APK.equals(localTaskJsonObj.getString(ParameterName.taskType))
			 	&& UsualData.SERVICE_PACKAGE_NAME.equals(localTaskJsonObj.getString(ParameterName.packName))
				&& String.valueOf(versionCode).equals(localTaskJsonObj.getString(ParameterName.versionCode))
				&& versionName.equals(localTaskJsonObj.getString(ParameterName.versionName))) {
					BBLog.i(BBLog.TAG, "checkServiceUpdateResult: " + localTaskJsonObj);
					String beginDateStr = localTaskJsonObj.getString(ParameterName.beginDate);
					String endDateStr = localTaskJsonObj.getString(ParameterName.endDate);
					long nowTime = System.currentTimeMillis();
					try {
						long beginTime;
						if (DateTimeUtils.isNumeric(beginDateStr) && DateTimeUtils.isNumeric(endDateStr)) {
							beginTime = new Long(beginDateStr).longValue();
						} else {
							SimpleDateFormat sdf = new SimpleDateFormat(Constants.dateFormat);
							beginTime = sdf.parse(beginDateStr).getTime();
						}

						BBLog.w(BBLog.TAG, "nowTime=" + nowTime + "  beginTime=" + beginTime + "  " + localTaskJsonObj.getString(ParameterName.taskResult));

						if (beginTime < nowTime && TaskState.INSTALL_ING.equals(localTaskJsonObj.getString(ParameterName.taskResult))) {
							//已过时 刪除
							Helpers.updateWSTaskStateAndUpload(ContextUtil.getInstance(), localTaskJsonObj.getString(ParameterName.taskId), TaskState.UPDATE_SUCCESS, null);
						}
					} catch (Exception e) {
						e.printStackTrace();
					}
					break;
				}
			}
		} catch (JSONException e) {
			e.printStackTrace();
		} catch (PackageManager.NameNotFoundException e) {
			e.printStackTrace();
		} catch (Exception e){
			e.printStackTrace();
		}
	}

	public static void checkOSUpdateResult() {
		try {
			String taskId = SharedPreferencesUtils.getSharePreferencesValue(UsualData.SHARED_PREFERENCES_OSUPDATE_TASKID, "");
			if (TextUtils.isEmpty(taskId)) {
				return;
			}

			SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_OSUPDATE_TASKID, "");

			String localTaskListStr = sp.getString(SPKeys.WEBSOCKET_TASK_LIST, "");
			if (!Helpers.isStrNoEmpty(localTaskListStr)){
				return;
			}
			JSONArray localTaskList = new JSONArray(localTaskListStr);
			for (int i = 0; i < localTaskList.length(); i++) {
				JSONObject localTaskJsonObj = (JSONObject) localTaskList.get(i);
				if(taskId.equals(localTaskJsonObj.getString(ParameterName.taskId))
				&& TaskState.UPDATE_ING.equals(localTaskJsonObj.getString(ParameterName.taskResult))) {
					String targetVer = localTaskJsonObj.getString(ParameterName.displayVer);
					if (targetVer.equals(DeviceInfoApi.getIntance().getCustomVersion())) {
						Helpers.updateWSTaskStateAndUpload(ContextUtil.getInstance(), taskId, TaskState.UPDATE_SUCCESS, "");
					} else {
						Helpers.updateWSTaskStateAndUpload(ContextUtil.getInstance(), taskId, TaskState.UPDATE_FAILED, "");
					}
//					break;
				}
			}
		} catch (JSONException e) {
			e.printStackTrace();
		} catch (Exception e){
			e.printStackTrace();
		}
	}

	/////////////////APP+ 執行結果處理/////////////////////
	public static void addWSTaskResultJsonObj(String taskId, JSONObject jsonObject) {
		try {
			String wsTaskListStr = sp.getString(SPKeys.WEBSOCKET_TASK_EXECUTE_RESULT_LIST, "");
			if (!Helpers.isStrNoEmpty(wsTaskListStr)) {
				JSONArray jsonArray = new JSONArray();
				jsonArray.put(jsonObject);
				BBLog.i(BBLog.TAG, "addWSTaskResultJsonObj: " + jsonArray);
				sp.edit().putString(SPKeys.WEBSOCKET_TASK_EXECUTE_RESULT_LIST, jsonArray.toString()).commit();
				return;
			}

			JSONArray wsTaskList = new JSONArray(wsTaskListStr);
			for (int i = 0; i < wsTaskList.length(); i++) {
				JSONObject localWSTaskJsonObj = (JSONObject) wsTaskList.get(i);
				JSONObject data = localWSTaskJsonObj.optJSONObject(ParameterName.data);
				String taskIdTmp = data.optString(ParameterName.taskId);
				if (taskId.equals(taskIdTmp)) {
					wsTaskList.put(i, jsonObject);
					BBLog.i(BBLog.TAG, "addWSTaskResultJsonObj: " + localWSTaskJsonObj);
					sp.edit().putString(SPKeys.WEBSOCKET_TASK_EXECUTE_RESULT_LIST, wsTaskList.toString()).commit();
					return;
				}
			}

			BBLog.i(BBLog.TAG, "addWSTaskResultJsonObj: " + jsonObject);
			wsTaskList.put(jsonObject);
			sp.edit().putString(SPKeys.WEBSOCKET_TASK_EXECUTE_RESULT_LIST, wsTaskList.toString()).commit();
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	public static void removeWSTaskResultJsonObjByOrgReqId(String orgReqId) {
		try {
			String localWSTaskListStr = sp.getString(SPKeys.WEBSOCKET_TASK_EXECUTE_RESULT_LIST, "");
			if (!Helpers.isStrNoEmpty(localWSTaskListStr))
				return;
			BBLog.i(BBLog.TAG, "removeWSTaskResultJsonObjByOrgReqId: " + orgReqId);
			JSONArray localWSTaskList = new JSONArray(localWSTaskListStr);
			for (int i=localWSTaskList.length()-1; i>=0; i--) {
				JSONObject localWSTaskJsonObj = (JSONObject) localWSTaskList.get(i);
				if (localWSTaskJsonObj.length() == 0 || !localWSTaskJsonObj.has(ParameterName.request_id)) {
					BBLog.i(BBLog.TAG, "delete empty msg: " + i);
					if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
						localWSTaskList.remove(i);
					} else {
						localWSTaskList = Helpers.remove(localWSTaskList, i);
					}
				}
			}

			BBLog.i(BBLog.TAG, "after check empty, localWSTaskList.length(): " + localWSTaskList.length());
			for (int i = 0; i < localWSTaskList.length(); i++) {
				JSONObject localWSTaskJsonObj = (JSONObject) localWSTaskList.get(i);
				try {
					String reqIdTmp = localWSTaskJsonObj.getString(ParameterName.request_id);
					if (orgReqId.equals(reqIdTmp)) {
						if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
							localWSTaskList.remove(i);
						} else {
							localWSTaskList = Helpers.remove(localWSTaskList, i);
						}
						sp.edit().putString(SPKeys.WEBSOCKET_TASK_EXECUTE_RESULT_LIST, localWSTaskList.toString()).commit();
						return;
					}
				} catch (Exception e) {
					localWSTaskList.remove(i);
					sp.edit().putString(SPKeys.WEBSOCKET_TASK_EXECUTE_RESULT_LIST, localWSTaskList.toString()).commit();
					return;
				}
			}
		} catch (JSONException e) {
			e.printStackTrace();
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	public static void clearWSTaskResultCache() {
		sp.edit().remove(SPKeys.WEBSOCKET_TASK_EXECUTE_RESULT_LIST).commit();
	}

	public static void uploadWSTaskResult() {
		try {
			String wsTaskListStr = sp.getString(SPKeys.WEBSOCKET_TASK_EXECUTE_RESULT_LIST, "");
			if (Helpers.isStrNoEmpty(wsTaskListStr)) {
				JSONArray wsTaskList = new JSONArray(wsTaskListStr);
				for (int i = 0; i < wsTaskList.length(); i++) {
					JSONObject localWSTaskJsonObj = (JSONObject) wsTaskList.get(i);
					WebSocketCenter.sendMessage(localWSTaskJsonObj, true);
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
}
