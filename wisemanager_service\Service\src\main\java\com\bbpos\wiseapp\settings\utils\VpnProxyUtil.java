package com.bbpos.wiseapp.settings.utils;

import android.content.Context;
import android.net.ConnectivityManager;
import android.os.Build;
import android.util.Log;

import com.bbpos.wiseapp.logger.BBLog;

import java.lang.reflect.Constructor;
import java.lang.reflect.Field;
import java.lang.reflect.Method;

/**
 * Created by eric.lin on 2019/4/18.
 *
 * 使用示例：
 *          connect：
 *               //查询检查是否已经存在VPN
 *                 Object vpnProfile = VpnProxyUtil.getVpnProfile();
 *                 if (vpnProfile == null) {
 *                     vpnProfile = VpnProxyUtil.createVpnProfile("vpn1", "203.174.52.54", "eric.lin", "test123","test1234");
 *                 } else {
 *                     VpnProxyUtil.setParams(vpnProfile, "vpn1", "203.174.52.54", "eric.lin", "test123","test1234");
 *                 }
 *                 final Object finalVpnProfile = vpnProfile;
 *                 new Thread(){
 *                     @Override
 *                     public void run() {
 *                         //连接
 *                         VpnProxyUtil.connect(VpnTestActivity.this, finalVpnProfile);
 *                     }
 *                 }.start();
 *
 *          disConnect：
 *               new Thread(){
 *                     @Override
 *                     public void run() {
 *                         //断开连接
 *                         VpnProxyUtil.disconnect(VpnTestActivity.this);
 *                     }
 *                 }.start();
 *
 */

public class VpnProxyUtil {
    private static final String TAG = "VpnProxyUtil";
    private static Class vpnProfileClz;
    private static Class credentialsClz;
    private static Class keyStoreClz;
    private static Class iConManagerClz;
    private static Object iConManagerObj;

    /**
     * 使用其他方法前先调用该方法
     * 初始化vpn相关的类
     * @param context
     */
    public static void init(Context context){
        try {
            vpnProfileClz = Class.forName("com.android.internal.net.VpnProfile");
            keyStoreClz = Class.forName("android.security.KeyStore");

            ConnectivityManager cm = (ConnectivityManager) context.getSystemService(Context.CONNECTIVITY_SERVICE);
            Field fieldIConManager = null;

            fieldIConManager = cm.getClass().getDeclaredField("mService");
            fieldIConManager.setAccessible(true);
            iConManagerObj = fieldIConManager.get(cm);
            iConManagerClz = Class.forName(iConManagerObj.getClass().getName());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * @param name     vpn连接名，自定义
     * @param server   服务器地址
     * @param username 用户名
     * @param password 用户密码
     * @return 返回一个com.android.internal.net.VpnProfile的实例
     */
    public static Object createVpnProfile(String name, String server, String username, String password,String ipsecSecret) {
        Object vpnProfileObj = null;
        try {
            //生成vpn的key
            long millis = System.currentTimeMillis();
            String vpnKey = Long.toHexString(millis);
            //获得构造函数
            Constructor constructor = vpnProfileClz.getConstructor(String.class);
            vpnProfileObj = constructor.newInstance(vpnKey);
            //设置参数
            setParams(vpnProfileObj,name,server,username,password,ipsecSecret);
            //插入vpn数据
            insertVpn(vpnProfileObj,vpnKey);
            BBLog.d(BBLog.TAG, "createVpnProfile: success");
        } catch (Exception e) {
            BBLog.d(BBLog.TAG, "createVpnProfile: error: "+e.getMessage());
            e.printStackTrace();
        }
        return vpnProfileObj;
    }

    /**
     * @param name     vpn连接名，自定义
     * @param server   服务器地址
     * @param username 用户名
     * @param password 用户密码
     * @return 返回一个com.android.internal.net.VpnProfile的实例
     */
    public static Object setParams(Object vpnProfileObj,String name, String server, String username, String password,String ipsecSecret) {
        try {
            Field field_username = vpnProfileClz.getDeclaredField("username");
            Field field_type = vpnProfileClz.getDeclaredField("type");
            Field field_password = vpnProfileClz.getDeclaredField("password");
            Field field_server = vpnProfileClz.getDeclaredField("server");
            Field field_name = vpnProfileClz.getDeclaredField("name");
            Field field_ipsecSecret     = vpnProfileClz.getDeclaredField("ipsecSecret");
            Field field_saveLogin     = vpnProfileClz.getDeclaredField("saveLogin");

            //设置参数
            field_type.set(vpnProfileObj,1);//1: TYPE_L2TP_IPSEC_PSK
            field_name.set(vpnProfileObj, name);
            field_server.set(vpnProfileObj, server);
            field_username.set(vpnProfileObj, username);
            field_password.set(vpnProfileObj, password);
            field_ipsecSecret.set(vpnProfileObj, ipsecSecret);
            field_saveLogin.set(vpnProfileObj, true);

            BBLog.d(BBLog.TAG, "setParams: success");
        } catch (Exception e) {
            e.printStackTrace();
            BBLog.d(BBLog.TAG, "setParams: error: "+e.getMessage());
        }
        return vpnProfileObj;
    }

    /**
     * 连接vpn
     * @param context
     * @param profile com.android.internal.net.VpnProfile的实例
     * @return true:连接成功，false:连接失败
     */
    public static boolean connect(Context context, Object profile) {
        boolean isConnected = true;
        try {
            BBLog.d(BBLog.TAG, "connect: ");
            Method metStartLegacyVpn = iConManagerClz.getDeclaredMethod("startLegacyVpn", vpnProfileClz);
            metStartLegacyVpn.setAccessible(true);
            //解锁KeyStore
            unlock(context);
            //开启vpn连接
            metStartLegacyVpn.invoke(iConManagerObj, profile);

            BBLog.d(BBLog.TAG, "connect: success");
        } catch (Exception e) {
            isConnected = false;
            e.printStackTrace();
            BBLog.d(BBLog.TAG, "connect: error: "+e.getMessage());
        }
        return isConnected;
    }

    /**
     * 断开vpn连接
     * @param context
     * @return true:已断开，false:断开失败
     */
    public static boolean disconnect(Context context) {
        boolean disconnected = true;
        try {
            Method metPrepare = iConManagerClz.getDeclaredMethod("prepareVpn", String.class, String.class);
            //断开连接
            metPrepare.invoke(iConManagerObj, "[Legacy VPN]", "[Legacy VPN]");
        } catch (Exception e) {
            disconnected = false;
            e.printStackTrace();
        }
        return disconnected;
    }

    /**
     * @return 返回一个已存在的vpn实例
     */
    public static Object getVpnProfile() {
        Method keyStore_saw = null;
        try {
            Object keyStoreObj = getKeyStoreInstance();
            if (Build.VERSION.SDK_INT<23) {
                keyStore_saw = keyStoreClz.getMethod("saw", String.class);

            }else {
                keyStore_saw = keyStoreClz.getMethod("list", String.class);
            }

            keyStore_saw.setAccessible(true);
            //查找数据库
            String[] keys = (String[]) keyStore_saw.invoke(keyStoreObj, "VPN_");
            //如果之前没有创建过vpn，则返回null
            if (keys == null || keys.length == 0) {
                return null;
            }

            for (String s : keys) {
                BBLog.i("key:", s);
            }

            Method vpnProfile_decode = vpnProfileClz.getDeclaredMethod("decode", String.class, byte[].class);
            vpnProfile_decode.setAccessible(true);

            Method keyStore_get = keyStoreClz.getDeclaredMethod("get", String.class);
            keyStore_get.setAccessible(true);
            //获得第一个vpn
            Object byteArrayValue = keyStore_get.invoke(keyStoreObj,"VPN_"+keys[0]);
            //反序列化返回VpnProfile实例
            Object vpnProfileObj = vpnProfile_decode.invoke(null, keys[0], byteArrayValue);

            return vpnProfileObj;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }
    private static void insertVpn(Object profieObj,String key)throws Exception{
        Method keyStore_put = keyStoreClz.getDeclaredMethod("put", String.class, byte[].class, int.class, int.class);
        Object keyStoreObj = getKeyStoreInstance();
        Class vpnProfileClz = Class.forName("com.android.internal.net.VpnProfile");
        Method vpnProfile_encode = vpnProfileClz.getDeclaredMethod("encode");
        byte[] bytes = (byte[]) vpnProfile_encode.invoke(profieObj);
        keyStore_put.invoke(keyStoreObj,"VPN_"+key,bytes,-1,1);

        BBLog.d(BBLog.TAG, "insertVpn: success");
    }

    private static Object getKeyStoreInstance() throws Exception {
        Method keyStore_getInstance = keyStoreClz.getMethod("getInstance");
        keyStore_getInstance.setAccessible(true);
        Object keyStoreObj = keyStore_getInstance.invoke(null);
        return keyStoreObj;
    }
    private static void unlock(Context mContext) throws Exception {
        credentialsClz = Class.forName("android.security.Credentials");

        Method credentials_getInstance = credentialsClz.getDeclaredMethod("getInstance");
        Object credentialsObj = credentials_getInstance.invoke(null);

        Method credentials_unlock = credentialsClz.getDeclaredMethod("unlock",Context.class);
        credentials_unlock.invoke(credentialsObj,mContext);
    }

}
