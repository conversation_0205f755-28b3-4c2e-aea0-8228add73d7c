<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AndroidLayouts">
    <shared>
      <config />
    </shared>
  </component>
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="NONE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="0690af8c-d5d0-435c-a01e-cd653286d4a8" name="Changes" comment="">
      <change beforePath="$PROJECT_DIR$/app/build.gradle" beforeDir="false" afterPath="$PROJECT_DIR$/app/build.gradle" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/src/main/AndroidManifest.xml" beforeDir="false" afterPath="$PROJECT_DIR$/app/src/main/AndroidManifest.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/src/main/java/com/dspread/mdm/service/SmartMdmServiceApp.kt" beforeDir="false" afterPath="$PROJECT_DIR$/app/src/main/java/com/dspread/mdm/service/SmartMdmServiceApp.kt" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/src/main/java/com/dspread/mdm/service/broadcast/core/BroadcastManager.kt" beforeDir="false" afterPath="$PROJECT_DIR$/app/src/main/java/com/dspread/mdm/service/broadcast/core/BroadcastManager.kt" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/src/main/java/com/dspread/mdm/service/broadcast/core/BroadcastSender.kt" beforeDir="false" afterPath="$PROJECT_DIR$/app/src/main/java/com/dspread/mdm/service/broadcast/core/BroadcastSender.kt" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/src/main/java/com/dspread/mdm/service/broadcast/handlers/service/ServiceGuardEventHandler.kt" beforeDir="false" afterPath="$PROJECT_DIR$/app/src/main/java/com/dspread/mdm/service/broadcast/handlers/service/ServiceGuardEventHandler.kt" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/src/main/java/com/dspread/mdm/service/broadcast/handlers/system/PackageUpdateEventHandlerImpl.kt" beforeDir="false" afterPath="$PROJECT_DIR$/app/src/main/java/com/dspread/mdm/service/broadcast/handlers/system/PackageUpdateEventHandlerImpl.kt" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/src/main/java/com/dspread/mdm/service/broadcast/handlers/system/ProvisioningEventHandler.kt" beforeDir="false" afterPath="$PROJECT_DIR$/app/src/main/java/com/dspread/mdm/service/broadcast/handlers/system/ProvisioningEventHandler.kt" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/src/main/java/com/dspread/mdm/service/broadcast/handlers/websocket/HeartbeatEventHandlerImpl.kt" beforeDir="false" afterPath="$PROJECT_DIR$/app/src/main/java/com/dspread/mdm/service/broadcast/handlers/websocket/HeartbeatEventHandlerImpl.kt" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/src/main/java/com/dspread/mdm/service/broadcast/handlers/websocket/TaskExecuteEventHandlerImpl.kt" beforeDir="false" afterPath="$PROJECT_DIR$/app/src/main/java/com/dspread/mdm/service/broadcast/handlers/websocket/TaskExecuteEventHandlerImpl.kt" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/src/main/java/com/dspread/mdm/service/broadcast/handlers/websocket/TerminalInfoEventHandlerImpl.kt" beforeDir="false" afterPath="$PROJECT_DIR$/app/src/main/java/com/dspread/mdm/service/broadcast/handlers/websocket/TerminalInfoEventHandlerImpl.kt" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/src/main/java/com/dspread/mdm/service/broadcast/receivers/GeofenceReceiver.kt" beforeDir="false" afterPath="$PROJECT_DIR$/app/src/main/java/com/dspread/mdm/service/broadcast/receivers/GeofenceReceiver.kt" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/src/main/java/com/dspread/mdm/service/config/DebugConfig.kt" beforeDir="false" afterPath="$PROJECT_DIR$/app/src/main/java/com/dspread/mdm/service/config/DebugConfig.kt" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/src/main/java/com/dspread/mdm/service/config/ProvisionConfig.kt" beforeDir="false" afterPath="$PROJECT_DIR$/app/src/main/java/com/dspread/mdm/service/config/ProvisionConfig.kt" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/src/main/java/com/dspread/mdm/service/modules/ModuleManagerRegistry.kt" beforeDir="false" afterPath="$PROJECT_DIR$/app/src/main/java/com/dspread/mdm/service/modules/ModuleManagerRegistry.kt" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/src/main/java/com/dspread/mdm/service/modules/geofence/location/GpsLocationManager.kt" beforeDir="false" afterPath="$PROJECT_DIR$/app/src/main/java/com/dspread/mdm/service/modules/geofence/location/GpsLocationManager.kt" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/src/main/java/com/dspread/mdm/service/modules/provisioning/ProvisioningManager.kt" beforeDir="false" afterPath="$PROJECT_DIR$/app/src/main/java/com/dspread/mdm/service/modules/provisioning/ProvisioningManager.kt" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/src/main/java/com/dspread/mdm/service/modules/provisioning/ProvisioningTrigger.kt" beforeDir="false" afterPath="$PROJECT_DIR$/app/src/main/java/com/dspread/mdm/service/modules/provisioning/ProvisioningTrigger.kt" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/src/main/java/com/dspread/mdm/service/network/websocket/connection/WsConnectionManager.kt" beforeDir="false" afterPath="$PROJECT_DIR$/app/src/main/java/com/dspread/mdm/service/network/websocket/connection/WsConnectionManager.kt" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/src/main/java/com/dspread/mdm/service/platform/manager/ServiceGuardManager.kt" beforeDir="false" afterPath="$PROJECT_DIR$/app/src/main/java/com/dspread/mdm/service/platform/manager/ServiceGuardManager.kt" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/src/main/java/com/dspread/mdm/service/platform/manager/WakeLockManager.kt" beforeDir="false" afterPath="$PROJECT_DIR$/app/src/main/java/com/dspread/mdm/service/platform/manager/WakeLockManager.kt" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/src/main/java/com/dspread/mdm/service/services/SmartMdmBackgroundService.kt" beforeDir="false" afterPath="$PROJECT_DIR$/app/src/main/java/com/dspread/mdm/service/services/SmartMdmBackgroundService.kt" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ClangdSettings">
    <option name="formatViaClangd" value="false" />
  </component>
  <component name="ExecutionTargetManager" SELECTED_TARGET="device_and_snapshot_combo_box_target[DeviceId(pluginId=PhysicalDevice, isTemplate=false, identifier=serial=01354090202503050399)]" />
  <component name="ExternalProjectsData">
    <projectState path="$PROJECT_DIR$">
      <ProjectState />
    </projectState>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 3
}</component>
  <component name="ProjectId" id="30uA4izRJ1RcKShJZbDZ0JgRoQv" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "Android App.app.executor": "Run",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.cidr.known.project.marker": "true",
    "RunOnceActivity.readMode.enableVisualFormatting": "true",
    "cf.first.check.clang-format": "false",
    "cidr.known.project.marker": "true",
    "dart.analysis.tool.window.visible": "false",
    "git-widget-placeholder": "dspread",
    "kotlin-language-version-configured": "true",
    "last_opened_file_path": "E:/SourceCode/service",
    "show.migrate.to.gradle.popup": "false"
  },
  "keyToStringList": {
    "kotlin-gradle-user-dirs": [
      "C:\\Users\\<USER>\\.gradle"
    ]
  }
}]]></component>
  <component name="RecentsManager">
    <key name="MoveFile.RECENT_KEYS">
      <recent name="E:\SourceCode\Dspread\service\app\src\main\java\com\dspread\mdm\service\broadcast\handlers\websocket" />
      <recent name="E:\SourceCode\Dspread\service\app\src\main\java\com\dspread\mdm\service\broadcast\handlers\websocekt" />
      <recent name="E:\SourceCode\Dspread\service\app\src\main\java\com\dspread\mdm\service\broadcast\core" />
    </key>
    <key name="MoveKotlinTopLevelDeclarationsDialog.RECENTS_KEY">
      <recent name="com.dspread.mdm.service.broadcast.receivers" />
      <recent name="com.dspread.mdm.service.broadcast.handlers.service" />
      <recent name="com.dspread.mdm.service.broadcast.handlers" />
      <recent name="com.dspread.mdm.service.broadcast.handlers.websocekt" />
      <recent name="com.dspread.mdm.service.broadcast.handlers.system" />
    </key>
  </component>
  <component name="RunManager">
    <configuration name="app" type="AndroidRunConfigurationType" factoryName="Android App" activateToolWindowBeforeRun="false">
      <module name="service.app.main" />
      <option name="DEPLOY" value="true" />
      <option name="DEPLOY_APK_FROM_BUNDLE" value="false" />
      <option name="DEPLOY_AS_INSTANT" value="false" />
      <option name="ARTIFACT_NAME" value="" />
      <option name="PM_INSTALL_OPTIONS" value="" />
      <option name="ALL_USERS" value="false" />
      <option name="ALWAYS_INSTALL_WITH_PM" value="false" />
      <option name="CLEAR_APP_STORAGE" value="false" />
      <option name="DYNAMIC_FEATURES_DISABLED_LIST" value="" />
      <option name="ACTIVITY_EXTRA_FLAGS" value="" />
      <option name="MODE" value="default_activity" />
      <option name="CLEAR_LOGCAT" value="false" />
      <option name="SHOW_LOGCAT_AUTOMATICALLY" value="false" />
      <option name="TARGET_SELECTION_MODE" value="DEVICE_AND_SNAPSHOT_COMBO_BOX" />
      <option name="SELECTED_CLOUD_MATRIX_CONFIGURATION_ID" value="-1" />
      <option name="SELECTED_CLOUD_MATRIX_PROJECT_ID" value="" />
      <option name="DEBUGGER_TYPE" value="Auto" />
      <Auto>
        <option name="USE_JAVA_AWARE_DEBUGGER" value="false" />
        <option name="SHOW_STATIC_VARS" value="true" />
        <option name="WORKING_DIR" value="" />
        <option name="TARGET_LOGGING_CHANNELS" value="lldb process:gdb-remote packets" />
        <option name="SHOW_OPTIMIZED_WARNING" value="true" />
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Auto>
      <Hybrid>
        <option name="USE_JAVA_AWARE_DEBUGGER" value="false" />
        <option name="SHOW_STATIC_VARS" value="true" />
        <option name="WORKING_DIR" value="" />
        <option name="TARGET_LOGGING_CHANNELS" value="lldb process:gdb-remote packets" />
        <option name="SHOW_OPTIMIZED_WARNING" value="true" />
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Hybrid>
      <Java>
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Java>
      <Native>
        <option name="USE_JAVA_AWARE_DEBUGGER" value="false" />
        <option name="SHOW_STATIC_VARS" value="true" />
        <option name="WORKING_DIR" value="" />
        <option name="TARGET_LOGGING_CHANNELS" value="lldb process:gdb-remote packets" />
        <option name="SHOW_OPTIMIZED_WARNING" value="true" />
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Native>
      <Profilers>
        <option name="ADVANCED_PROFILING_ENABLED" value="false" />
        <option name="STARTUP_PROFILING_ENABLED" value="false" />
        <option name="STARTUP_CPU_PROFILING_ENABLED" value="false" />
        <option name="STARTUP_CPU_PROFILING_CONFIGURATION_NAME" value="Java/Kotlin Method Sample (legacy)" />
        <option name="STARTUP_NATIVE_MEMORY_PROFILING_ENABLED" value="false" />
        <option name="NATIVE_MEMORY_SAMPLE_RATE_BYTES" value="2048" />
      </Profilers>
      <option name="DEEP_LINK" value="" />
      <option name="ACTIVITY_CLASS" value="" />
      <option name="SEARCH_ACTIVITY_IN_GLOBAL_SCOPE" value="false" />
      <option name="SKIP_ACTIVITY_VALIDATION" value="false" />
      <method v="2">
        <option name="Android.Gradle.BeforeRunTask" enabled="true" />
      </method>
    </configuration>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="0690af8c-d5d0-435c-a01e-cd653286d4a8" name="Changes" comment="" />
      <created>1754466894438</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1754466894438</updated>
    </task>
    <servers />
  </component>
</project>