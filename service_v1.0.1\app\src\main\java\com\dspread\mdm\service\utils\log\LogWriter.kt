package com.dspread.mdm.service.utils.log

/**
 * 日志写入接口
 */
interface LogWriter {
    /**
     * 写入日志
     */
    fun writeLog(level: Int, tag: String, message: String, throwable: Throwable? = null)
    
    /**
     * 刷新缓存
     */
    fun flush()
    
    /**
     * 关闭写入器
     */
    fun close()
}

/**
 * 控制台日志写入器
 */
class ConsoleLogWriter : LogWriter {
    
    override fun writeLog(level: Int, tag: String, message: String, throwable: Throwable?) {
        val levelName = LogLevel.getLevelShortName(level)
        val logMessage = "[$levelName] $tag: $message"
        
        when (level) {
            LogLevel.VERBOSE -> android.util.Log.v(tag, message, throwable)
            LogLevel.DEBUG -> android.util.Log.d(tag, message, throwable)
            LogLevel.INFO -> android.util.Log.i(tag, message, throwable)
            LogLevel.WARN -> android.util.Log.w(tag, message, throwable)
            LogLevel.ERROR -> android.util.Log.e(tag, message, throwable)
        }
    }
    
    override fun flush() {
        // 控制台输出不需要刷新
    }
    
    override fun close() {
        // 控制台输出不需要关闭
    }
}

/**
 * 文件日志写入器
 */
class FileLogWriter(
    private val logDirectory: java.io.File,
    private val maxFileSize: Long = 10 * 1024 * 1024, // 10MB
    private val maxBufferSize: Int = 1000
) : LogWriter {
    
    companion object {
        private const val LOG_FILE_PREFIX = "mdm_log"
        private const val LOG_FILE_EXTENSION = ".log"
    }
    
    private val dateFormat = java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS", java.util.Locale.getDefault())
    private val fileNameFormat = java.text.SimpleDateFormat("yyyyMMdd_HHmmss", java.util.Locale.getDefault())
    
    private var currentLogFile: java.io.File? = null
    private var currentFileWriter: java.io.FileWriter? = null
    private val logBuffer = java.util.concurrent.ConcurrentLinkedQueue<String>()
    
    init {
        // 确保日志目录存在
        if (!logDirectory.exists()) {
            logDirectory.mkdirs()
        }
        createNewLogFile()
    }
    
    override fun writeLog(level: Int, tag: String, message: String, throwable: Throwable?) {
        try {
            val timestamp = dateFormat.format(java.util.Date())
            val levelName = LogLevel.getLevelShortName(level)
            val logLine = "$timestamp [$levelName] $tag: $message"
            
            // 添加到缓冲区
            if (logBuffer.size < maxBufferSize) {
                logBuffer.offer(logLine)
                
                // 如果有异常，也添加到缓冲区
                throwable?.let { t ->
                    logBuffer.offer("Exception: ${t.message}")
                    t.stackTrace.forEach { stackElement ->
                        logBuffer.offer("    at $stackElement")
                    }
                }
                
                // 检查是否需要刷新
                if (logBuffer.size >= maxBufferSize / 2) {
                    flush()
                }
            }
        } catch (e: Exception) {
            // 避免日志写入异常影响主流程
            e.printStackTrace()
        }
    }
    
    override fun flush() {
        try {
            val writer = currentFileWriter ?: return
            
            while (logBuffer.isNotEmpty()) {
                val logLine = logBuffer.poll() ?: break
                writer.write(logLine)
                writer.write("\n")
            }
            
            writer.flush()
            
            // 检查文件大小
            checkFileSize()
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }
    
    override fun close() {
        try {
            flush()
            currentFileWriter?.close()
            currentFileWriter = null
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }
    
    /**
     * 创建新的日志文件
     */
    private fun createNewLogFile() {
        try {
            // 关闭当前文件
            currentFileWriter?.close()
            
            // 创建新文件
            val timestamp = fileNameFormat.format(java.util.Date())
            currentLogFile = java.io.File(logDirectory, "${LOG_FILE_PREFIX}_$timestamp$LOG_FILE_EXTENSION")
            currentFileWriter = java.io.FileWriter(currentLogFile, true)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }
    
    /**
     * 检查文件大小，必要时创建新文件
     */
    private fun checkFileSize() {
        try {
            val file = currentLogFile ?: return
            
            if (file.length() > maxFileSize) {
                createNewLogFile()
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }
    
    /**
     * 获取当前日志文件列表
     */
    fun getLogFiles(): List<java.io.File> {
        return try {
            logDirectory.listFiles()?.filter { 
                it.isFile && it.name.startsWith(LOG_FILE_PREFIX) && it.name.endsWith(LOG_FILE_EXTENSION)
            }?.sortedByDescending { it.lastModified() } ?: emptyList()
        } catch (e: Exception) {
            e.printStackTrace()
            emptyList()
        }
    }
}
