package com.bbpos.wiseapp.websocket.handler;

import android.content.Context;
import android.content.Intent;
import android.text.TextUtils;

import com.bbpos.wiseapp.logger.BBLog;
import com.bbpos.wiseapp.network.wificonfig.WiFiProfileManager;
import com.bbpos.wiseapp.service.common.UsualData;
import com.bbpos.wiseapp.tms.utils.Constants;
import com.bbpos.wiseapp.tms.utils.ContextUtil;
import com.bbpos.wiseapp.tms.utils.Helpers;
import com.bbpos.wiseapp.tms.utils.ParameterName;
import com.bbpos.wiseapp.tms.utils.WebSocketServiceListManager;
import com.bbpos.wiseapp.utils.SharedPreferencesUtils;
import com.bbpos.wiseapp.websocket.WebSocketManager;
import com.bbpos.wiseapp.websocket.WifiProfileProcessService;

import org.json.JSONArray;
import org.json.JSONObject;

import static com.bbpos.wiseapp.websocket.WifiProfileProcessService.saveAllWifiProfileToWifiConfig;

/**
 * 处理ST006 wifi profile 协议
 */
public class WifiProfileHandler {
    private static final String TAG = BBLog.TAG;
    private WebSocketManager webSocketManager;
    private static JSONArray currentCacheWifiProfile;
    private static Context mContext;

    public WifiProfileHandler(Context context, WebSocketManager webSocketManager) {
        this.webSocketManager = webSocketManager;
        mContext = context;
        WiFiProfileManager.getInstance(mContext).startScan();
    }

    public void handleMsg(String response) {
        try {
            JSONArray wifiList = null;
            JSONObject responseJson = new JSONObject(response);
            JSONObject responseData = Helpers.getDataFromResponse(responseJson);
            if (responseData == null) {
                return;
            }
//            BBLog.e(TAG, "WifiProfileHandler [handleMsg]: list= "+response);
            if (responseData.has(ParameterName.wifiList)) {
                wifiList = responseData.getJSONArray(ParameterName.wifiList);
                if (wifiList==null || wifiList.length()==0) {
                    return;
                }
                //todo MDM 会下发终端离线期间未收到的所有profile 信息，可能导致终端不断进行wifi切换。此处暂时过滤这种场景。
                if (wifiList!=null && currentCacheWifiProfile!=null && wifiList.toString().equals(currentCacheWifiProfile.toString())){
                    BBLog.e(TAG, "WifiProfileHandler [handleMsg]: 当前下发的wifi profile同本地数据一致。" );
                    currentCacheWifiProfile = wifiList;
                    return;
                }
                WebSocketServiceListManager.updateWifiProfileList(wifiList);
                // 保持所有list中wifi
                saveAllWifiProfileToWifiConfig(wifiList);
                String status = SharedPreferencesUtils.getSharePreferencesValue(UsualData.SHARED_UNBOX_STATUS, "");
                BBLog.i(BBLog.TAG, "终端状态值=" + status);
                if (/*!"5".equals(status) &&*/ !"6".equals(status)) {
                    return;
                }
                if (wifiList!=null && wifiList.length() > 0){
                    //保存数据到内存中，用于同SharedPreferences中缓存的数据做比较
                    currentCacheWifiProfile = wifiList;
                    if (Constants.isOTARunning){
                        BBLog.e(TAG, "WifiProfileHandler [handleMsg]: 终端正在执行OTA ，暂时不执行wifi切换");
                    }else if (WifiProfileProcessService.isRunning.get()){
                        BBLog.e(TAG, "WifiProfileHandler WifiProfileProcessService is running");
                    }else {
                        Intent intent = new Intent(ContextUtil.getInstance().getApplicationContext(), WifiProfileProcessService.class);
                        WifiProfileProcessService.currentCacheWifiProfile = currentCacheWifiProfile;
                        ContextUtil.getInstance().getApplicationContext().startService(intent);
                    }
                }else {
//                    BBLog.e(TAG, "WifiProfileHandler [handleMsg]: wifiList = "+ wifiList );
                }
            } else {
                //todo 同Joe确认。当下发的wifi profile 列表为空时，可能是mdm删除了所有wifi 配置，此时不做任何处理---尽量让终端保持在线可控状态
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * todo 测试数据
     * @return
     */
    public static JSONObject initTestData() {
        JSONObject response = null;
        try {
//            JSONArray array = new JSONArray();
//            JSONObject object = new JSONObject();
//            object.put("beginDate", "1970-01-01 00:00:00");
//            object.put("isDefault", "1");
//            object.put("connectHidden", "0");
//            object.put("endDate", "2019-08-30 00:00:00");
//            object.put("securityType","WPA/WPA2 PSK");
//            object.put("proxyType","None");
//            object.put("ssid","7mdTest02");
//            object.put("password","BBpos2019");
//            object.put("order","8");
//            array.put(object);

//            object = new JSONObject();
//            object.put("beginDate", "2019-08-23 15:50:00");
//            object.put("isDefault", "0");
//            object.put("connectHidden", "0");
//            object.put("endDate", "2019-08-23 16:10:00");
//            object.put("securityType","WPA2-PSK");
//            object.put("proxyType","None");
//            object.put("ssid","Xiaomi_33D2_5G");
//            object.put("password", "peaty34.123");
//            object.put("order","4");
//            object.put("proxyType", "Manual");
//            object.put("proxyIp", "*************");
//            object.put("proxyPort", "8123");
//            object.put("byPassProxyFor", "");
//            object.put("proxyType", "PAC");
//            object.put("pacUrl", "https://download.wisemanager.com/config/bbpos/proxy-4.pac");
//            array.put(object);

//            object = new JSONObject();
//            object.put("beginDate","2019-08-23 16:00:00");
//            object.put("isDefault","0");
//            object.put("connectHidden","0");
//            object.put("endDate","2019-08-23 16:30:00");
//            object.put("securityType","None");
//            object.put("proxyType","None");
//            object.put("ssid","7mdTest04");
//            object.put("order","2");
////            object.put("proxyType", "PAC");
////            object.put("proxyIp", "*************");
////            object.put("proxyPort", "8123");
////            object.put("byPassProxyFor", "");
////            object.put("pacUrl", "http://**************:8080/download/proxy.pac");
//            array.put(object);

//            object = new JSONObject();
//            object.put("beginDate","2019-08-23 16:00:00");
//            object.put("isDefault","0");
//            object.put("connectHidden","0");
//            object.put("endDate","2019-08-23 16:29:00");
//            object.put("securityType","None");
//            object.put("proxyType","None");
//            object.put("ssid","7mdTest01");
//            object.put("order","7");
////            object.put("proxyType", "PAC");
////            object.put("proxyIp", "*************");
////            object.put("proxyPort", "8123");
////            object.put("byPassProxyFor", "");
////            object.put("pacUrl", "http://**************:8080/download/proxy.pac");
//            array.put(object);

//            object = new JSONObject();
//            object.put("beginDate", "2019-08-23 16:32:00");
//            object.put("isDefault", "1");
//            object.put("connectHidden", "0");
//            object.put("endDate", "2019-08-23 23:34:00");
//            object.put("securityType","WPA2-PSK");
//            object.put("proxyType","None");
//            object.put("ssid","BBPos_HOME");
//            object.put("password","FZBBpos2019");
//            object.put("order","5");
//            object.put("proxyType", "Manual");
//            object.put("proxyIp", "fp01.dev.bbpos.com");
//            object.put("proxyPort", "8123");
//            object.put("byPassProxyFor", "");
//            object.put("proxyType", "PAC");
//            object.put("pacUrl", "http://**************:8080/download/proxy-5.pac");
//            array.put(object);

//            object = new JSONObject();
//            object.put("beginDate", "2019-08-23 16:37:00");
//            object.put("isDefault", "0");
//            object.put("connectHidden", "0");
//            object.put("endDate", "2019-08-23 16:40:00");
//            object.put("securityType", "WPA2-PSK");
//            object.put("proxyType", "None");
//            object.put("ssid", "BBPos_HOME_5G");
//            object.put("password", "FZBBpos2019");
//            object.put("order", "3");
////            object.put("proxyType", "Manual");
////            object.put("proxyIp", "*************");
////            object.put("proxyPort", "8123");
////            object.put("byPassProxyFor", "");
//            array.put(object);

//            object = new JSONObject();
//            object.put("beginDate", "2019-08-21 22:45:00");
//            object.put("isDefault", "0");
//            object.put("connectHidden", "0");
//            object.put("endDate", "2019-08-21 22:50:00");
//            object.put("securityType","WPA2-PSK");
//            object.put("proxyType","None");
//            object.put("ssid","Xiaomi_33D2");
//            object.put("password", "peaty34.123");
//            object.put("order", "2");
//            array.put(object);
//
//            object = new JSONObject();
//            object.put("beginDate", "1970-01-01 00:00:00");
//            object.put("isDefault", "1");
//            object.put("connectHidden", "0");
//            object.put("endDate", "2019-08-30 00:00:00");
//            object.put("securityType","WPA2-PSK");
//            object.put("proxyType","None");
//            object.put("ssid","BBPOS4115");
//            object.put("password","BBpos2019");
//            object.put("order","6");
//            array.put(object);

//            JSONObject wifiListobj = new JSONObject();
//            wifiListobj.put("wifiList", array);
//
//            response = new JSONObject();
//            response.put("requestTime", "1565755352084");
//            response.put("data", wifiListobj);
//            response.put("requestId", "1565755352084ST006");
//            response.put("msgState", "P");
//            response.put("tranCode", "ST006");
//            response.put("msgVersion", "1");
//            response.put("serialNo", "7MDUS0100000168");
//            BBLog.d(TAG, "initTestData: response= " + response.toString());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return response;
    }

    /**
     * 定时触发
     */
    public static void scheduleWifProfileTask(Context context) {
        String status = SharedPreferencesUtils.getSharePreferencesValue(UsualData.SHARED_UNBOX_STATUS, "");
        BBLog.i(BBLog.TAG, "终端状态值=" + status);
        if (/*!"5".equals(status) &&*/ !"6".equals(status)) {
            //unbox 未完成，先执行provisioning default wifi
//            checkProvisiongDefaultWifProfile();
            return;
        }

        if (Helpers.isMobile(context)) {
            BBLog.d(TAG, "WifiProfileHandler [scheduleWifProfileTask]: 终端正在使用移动数据网络...");
            return;
        }

        if (currentCacheWifiProfile == null) {
            currentCacheWifiProfile = WebSocketServiceListManager.getWifiProfileListFromLocal();
        }
        BBLog.d(TAG, "WifiProfileHandler [scheduleWifProfileTask]: 触发定时器   >>>>>>");
        if (currentCacheWifiProfile!=null && currentCacheWifiProfile.length() > 0){
            if (Constants.isOTARunning){
                BBLog.e(TAG, "WifiProfileHandler [scheduleWifProfileTask]: 终端正在执行OTA ，暂时不执行wifi切换");
            }else if (WifiProfileProcessService.isRunning.get()){
                BBLog.e(TAG, "WifiProfileHandler WifiProfileProcessService is running");
            }else {
                Intent intent = new Intent(ContextUtil.getInstance().getApplicationContext(), WifiProfileProcessService.class);
                WifiProfileProcessService.currentCacheWifiProfile = currentCacheWifiProfile;
                ContextUtil.getInstance().getApplicationContext().startService(intent);
            }
        }else {
//            BBLog.e(TAG, "WifiProfileHandler [scheduleWifProfileTask]: wifiList = "+ currentCacheWifiProfile );
        }
    }

    /**
     * 检查是否存在provisioning default wifi 并判断是否需要连接；
     * unbox完成后，不再执行该操作
     */
    @Deprecated
    public static void checkProvisiongDefaultWifProfile() {
        BBLog.d(TAG, "checkProvisiongDefaultWifProfile =============> ");
        JSONArray cacheWifiProfile = WebSocketServiceListManager.getProvisionDefaultWifiProfileListFromLocal();
        try {
            if (cacheWifiProfile !=null && cacheWifiProfile.length()>0){
                JSONObject wifi = cacheWifiProfile.getJSONObject(0);
                //终端处在wifi range内并且当前终端没有网络连接
                if (WifiProfileProcessService.checkIsWifiInRange(wifi) && !WiFiProfileManager.getInstance(mContext).isWifiConnected()){
                    //连接wifi
                    BBLog.e(TAG, "WifiProfileProcessService [checkProvisiongDefaultWifProfile]: wifi ["+wifi.getString(ParameterName.ssid)+"] 正在连接 provisioning default Wifi");
                    WifiProfileProcessService.handlerWifiConnectBySecurityType(-1, wifi);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        BBLog.d(TAG, "checkProvisiongDefaultWifProfile <=============");
    }

    /**
     *  MDM 下发指令，终端切换到对应wifi
     * @param context
     * @param response
     */
    public  void changeWifiByServiceCmd(Context context, String response){
        if (currentCacheWifiProfile == null) {
            currentCacheWifiProfile = WebSocketServiceListManager.getWifiProfileListFromLocal();
        }
//        if (wifiConnectListener == null) {
//            initOnWifiConnectListener(context);
//        }
        BBLog.d(TAG, "WifiProfileHandler [changeWifiByServiceCmd]: 接收到切换wifi指令 :"+response);
        // TODO: 8/23/2019 协议待定
    }

    public static boolean checkIsWifiProfileSSID(String ssid){
        try {
            if (TextUtils.isEmpty(ssid)) {
                return false;
            }

//			SharedPreferences sp = PreferenceManager.getDefaultSharedPreferences(ContextUtil.getInstance());
//			String wifiProfileJson = sp.getString(SPKeys.WEBSOCKET_WIFL_PROFILE_LIST, "");
//			if (TextUtils.isEmpty(wifiProfileJson)) {
//				return false;
//			}
//			JSONArray cacheData = new JSONArray(wifiProfileJson);

			JSONArray cacheData = WebSocketServiceListManager.getWifiProfileListFromLocal();
			if (cacheData == null) return false;

            if (cacheData == null || cacheData.length() == 0) {
                return false;
            }
            for (int i = 0; i < cacheData.length(); i++){
                if (ssid.equals(cacheData.getJSONObject(i).getString(ParameterName.ssid))){
                    return true;
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }
}
