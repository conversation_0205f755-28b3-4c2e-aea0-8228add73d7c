package com.bbpos.wiseapp.utils;

import android.app.KeyguardManager;
import android.app.KeyguardManager.KeyguardLock;
import android.content.Context;
import android.os.Handler;
import android.os.PowerManager;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

public class WakeLockUtil {

    @Nullable
    public static PowerManager.WakeLock acquireWakeLock(@NonNull Context context, long timeout) {
        PowerManager.WakeLock wakeLock = null;
        try {
            PowerManager pm = (PowerManager) context.getSystemService(Context.POWER_SERVICE);
            if (pm == null)
                return null;
            wakeLock = pm.newWakeLock(PowerManager.ACQUIRE_CAUSES_WAKEUP | PowerManager.SCREEN_BRIGHT_WAKE_LOCK,context.getClass().getName());
            wakeLock.acquire(timeout);

        } catch (Exception e) {
            e.printStackTrace();
        }
        return wakeLock;
    }

    public static void release(@Nullable PowerManager.WakeLock wakeLock) {
        try {
            if (wakeLock != null && wakeLock.isHeld()) {
                wakeLock.release();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void releaseDelay(@Nullable PowerManager.WakeLock wakeLock, long delay) {
        try {
            if (wakeLock != null && wakeLock.isHeld()) {
                new Handler().postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        wakeLock.release();
                    }
                },delay);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     *
     * @param context
     * @return
     *          true为打开，false为关闭
     */
    public static boolean isScreenOn(@Nullable Context context){
        try {
            PowerManager powerManager = (PowerManager) context.getSystemService(Context.POWER_SERVICE);
            return powerManager.isScreenOn();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

    /**
     * 屏幕锁有五种设置，分别是没有设置屏幕锁，滑动解锁，图案解锁，PIN码解锁，密码解锁
     * @param context
     * @return
     *         false: 没有设置屏幕锁 | 已解锁
     *         true: 设置了屏幕锁但未解锁
     */
    public static boolean isDeviceLock(@Nullable Context context){
        try {
            KeyguardManager mKeyguardManager = (KeyguardManager) context.getSystemService(Context.KEYGUARD_SERVICE);
            return mKeyguardManager.inKeyguardRestrictedInputMode();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }
}
