<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/bg_shape">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="17dp"
        android:gravity="center"
        android:background="@color/white">
        <ImageView
            android:id="@+id/iv_image"
            android:layout_width="70dp"
            android:layout_height="70dp"
            android:scaleType="center"
            android:background="@drawable/success"
            android:layout_gravity="center"
            android:contentDescription="TODO" />
    </LinearLayout>
    <LinearLayout
        android:layout_height="wrap_content"
        android:layout_width="match_parent"
        android:gravity="center"
        android:orientation="vertical"
        android:background="@drawable/shape_corner_down">
        <TextView
            android:id="@+id/tv_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="16dp"
            android:layout_marginRight="16dp"
            android:text="OS Upgrade Completed"
            android:gravity="center"
            android:textStyle="bold"
            android:textSize="24sp"
            android:textColor="#3a3c46"/>
        <TextView
            android:id="@+id/tv_content"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="15dp"
            android:layout_marginRight="15dp"
            android:layout_marginTop="@dimen/dp_15"
            android:gravity="center"
            tools:text="Current Version: XXXX"
            android:textSize="18sp"
            android:textColor="#3a3c46"/>
        <Button
            android:id="@+id/btn_ok"
            android:layout_width="120dp"
            android:layout_height="@dimen/dp_40"
            android:padding="@dimen/dp_5"
            android:layout_marginTop="18dp"
            android:layout_marginLeft="20dp"
            android:layout_marginRight="20dp"
            android:layout_marginBottom="18dp"
            android:gravity="center"
            android:text="OK"
            android:textAllCaps="false"
            android:textSize="18sp"
            android:textColor="@color/white"
            android:background="@drawable/btn_bord_green" />
    </LinearLayout>
</LinearLayout>