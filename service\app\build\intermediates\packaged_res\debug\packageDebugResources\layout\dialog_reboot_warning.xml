<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#80000000"
    android:gravity="center"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="360dp"
        android:layout_height="wrap_content"
        android:background="@drawable/dialog_reboot_background"
        android:elevation="8dp"
        android:orientation="vertical"
        android:padding="0dp">

        <!-- 顶部图标区域 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/dialog_reboot_header_bg"
            android:gravity="center"
            android:orientation="vertical"
            android:padding="24dp">

            <ImageView
                android:layout_width="64dp"
                android:layout_height="64dp"
                android:src="@drawable/ic_reboot_warning"
                app:tint="#FF0000" />

            <TextView
                android:id="@+id/tv_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="12dp"
                android:text="系统维护提醒"
                android:textColor="#FFFFFF"
                android:textSize="20sp"
                android:textStyle="bold" />

        </LinearLayout>

        <!-- 内容区域 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="24dp">

            <!-- 消息内容 -->
            <TextView
                android:id="@+id/tv_message"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:lineSpacingExtra="4dp"
                android:text="系统将在指定时间重启以完成维护任务"
                android:textColor="#333333"
                android:textSize="16sp" />

            <!-- 倒计时显示 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="20dp"
                android:background="@drawable/countdown_background"
                android:gravity="center"
                android:orientation="horizontal"
                android:padding="16dp">

                <ImageView
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:src="@drawable/ic_timer"
                    app:tint="#FF5722" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="8dp"
                    android:text="剩余时间："
                    android:textColor="#666666"
                    android:textSize="14sp" />

                <TextView
                    android:id="@+id/tv_countdown"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="4dp"
                    android:text="05:00"
                    android:textColor="#FF5722"
                    android:textSize="18sp"
                    android:textStyle="bold" />

            </LinearLayout>

            <!-- 提示信息 -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:gravity="center"
                android:text="您可以选择延迟重启或立即重启"
                android:textColor="#999999"
                android:textSize="12sp" />

        </LinearLayout>

        <!-- 分割线 -->
        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="#E0E0E0" />

        <!-- 按钮区域 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <!-- 延迟重启按钮 -->
            <TextView
                android:id="@+id/btn_delay"
                android:layout_width="0dp"
                android:layout_height="56dp"
                android:layout_weight="1"
                android:background="@drawable/button_delay_selector"
                android:gravity="center"
                android:text="延迟重启"
                android:textColor="#666666"
                android:textSize="16sp" />

            <!-- 垂直分割线 -->
            <View
                android:layout_width="1dp"
                android:layout_height="match_parent"
                android:background="#E0E0E0" />

            <!-- 立即重启按钮 -->
            <TextView
                android:id="@+id/btn_reboot_now"
                android:layout_width="0dp"
                android:layout_height="56dp"
                android:layout_weight="1"
                android:background="@drawable/button_reboot_selector"
                android:gravity="center"
                android:text="立即重启"
                android:textColor="#FF5722"
                android:textSize="16sp"
                android:textStyle="bold" />

        </LinearLayout>

    </LinearLayout>

</LinearLayout>
