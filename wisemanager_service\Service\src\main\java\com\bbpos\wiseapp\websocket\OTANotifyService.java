package com.bbpos.wiseapp.websocket;

import android.app.IntentService;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.graphics.Color;
import android.graphics.PixelFormat;
import android.os.Build;
import android.os.Handler;
import android.os.Message;
import android.text.TextUtils;
import android.util.Log;
import android.view.Gravity;
import android.view.WindowManager;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.bbpos.wiseapp.logger.BBLog;
import com.bbpos.wiseapp.service.R;
import com.bbpos.wiseapp.service.common.UsualData;
import com.bbpos.wiseapp.system.api.DeviceInfoApi;
import com.bbpos.wiseapp.tms.adapter.SystemManagerAdapter;
import com.bbpos.wiseapp.tms.utils.Constants;
import com.bbpos.wiseapp.tms.utils.ContextUtil;
import com.bbpos.wiseapp.tms.utils.ParameterName;
import com.bbpos.wiseapp.utils.ActivityUtils;
import com.bbpos.wiseapp.utils.SharedPreferencesUtils;

import java.util.Timer;
import java.util.TimerTask;

public class OTANotifyService extends IntentService {
    private static final int MSG_UPDATE_DESKTOP = 1014;
    public static final int mForceAutoCloseDialogRemainSec = 60;
    private static int tempTime = mForceAutoCloseDialogRemainSec;
    private WindowManager mWindowManager;
    private WindowManager.LayoutParams mLayout;
    private TextView mDesktopLayout;
    private Timer timerDesk = new Timer();
    private TimerTask task;
    private int otaType;
    private String packageName;

    float mTouchStartX;
    float mTouchStartY;
    // 声明屏幕的宽高
    float x, y;
    int top;

    int width = 0;
    int height = 0;
    private long startTime;

    private final Handler mHandler = new Handler(ContextUtil.getInstance().getMainLooper()) {
        @Override
        public void handleMessage(Message msg) {
            super.handleMessage(msg);
            switch (msg.what) {
                case MSG_UPDATE_DESKTOP:
                    if (tempTime >= 0) {
                        BBLog.i(Constants.TAG, "OTANotifyService： OTA upgrade will be start after " +tempTime + " secs ");
                        mDesktopLayout.setText("  OTA upgrade will be start after " + tempTime + " secs  ");
                    } else if (tempTime < 0) {
                        if (timerDesk != null) {
                            timerDesk.cancel();
                        }
                        closeDesk();
                        if (otaType!=-1){
                            if (otaType == Constants.TYPE_WISECUBE_FW_UPGRADE){
                                BBLog.i(Constants.TAG, "OTANotifyService： begin to reboot");
                                SystemManagerAdapter.reboot(getApplicationContext());
                            }else if (otaType == Constants.TYPE_WISEPOS_TMT_UPGRADE){
                                BBLog.i(Constants.TAG, "OTANotifyService： begin to reboot");
                                SystemManagerAdapter.reboot(getApplicationContext());
                            }else if (otaType == Constants.TYPE_SP_FW_UPGRADE){
//                                Constants.isOTARunning = true;
//                                ActivityUtils.startApp(getApplicationContext(),packageName);
								// TODO: 2020/3/12 據Joe Ho建議，SP upgrade前重啟終端
								SystemManagerAdapter.reboot(getApplicationContext());
                            }else if (otaType == Constants.TYPE_TMT_UPGRADE){
                                SystemManagerAdapter.reboot(getApplicationContext());
//								ActivityUtils.startApp(getApplicationContext(),packageName);
                            }else if (otaType == Constants.TYPE_KEY_UPGRADE){
								SystemManagerAdapter.reboot(getApplicationContext());
//                                ActivityUtils.startApp(getApplicationContext(),packageName);
                            }
                        }
                        OTANotifyService.this.stopSelf();
                    }
                    break;
                default:
                    break;
            }
        }
    };

    private void startActivityByPakgeName() {
        try{
            if (!TextUtils.isEmpty(packageName)) {
                Intent intent = getPackageManager().getLaunchIntentForPackage(packageName);
                intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                intent.putExtra(ParameterName.TMT_URL, SharedPreferencesUtils.getSharePreferencesValue(UsualData.SHARED_PREFERENCES_TMT_SERVER_URL, UsualData.WISEPOS_PULS_TMT_URL_DEFAULT));
                startActivity(intent);
                BBLog.i(Constants.TAG, "启动该应用");
            }  else {
                BBLog.i(Constants.TAG, "包名为空或者该应用未安装");
            }
        } catch(Exception e) {
            e.printStackTrace();
            BBLog.i(Constants.TAG, "没有安装该应用");
        }
    }


    public OTANotifyService() {
        super("OTANotifyService");
    }

    @Override
    protected void onHandleIntent( Intent intent) {
    	if (DeviceInfoApi.getIntance().isWisePosPro() && Constants.B_UNBOX_RUNNING) {
			BBLog.i(BBLog.TAG, "OTANotifyService 终端正在做UNBOX，返回. unboxf_running = "+ Constants.B_UNBOX_RUNNING);
			return;
		}

        BBLog.i(Constants.TAG, "OTANotifyService  onStartCommand flag_need_ota=="+intent.getBooleanExtra(ParameterName.flag_need_ota,false));
        if (intent.getBooleanExtra(ParameterName.flag_need_ota,false)) {
        	synchronized (OTANotifyService.class) {
				if (Constants.isOTARunning) {
					BBLog.e(Constants.TAG, "OTANotifyService: 當前正在執行OTA/SP Upgrade,本次升級任務退出");
					return;
				}
				Constants.isOTARunning = true;
			}

			tempTime = mForceAutoCloseDialogRemainSec;
            //獲取升級類型
            otaType = intent.getIntExtra(ParameterName.type_for_ota,-1);
            packageName = intent.getStringExtra(ParameterName.packName);
            BBLog.e("WiseApp2.0", "OTANotifyService： onHandleIntent: packname=="+packageName );
            createWindowManager();
            createDesktopLayout();
            mHandler.postDelayed(new Runnable() {
                @Override
                public void run() {
                    showDesk();
                }
            },2000);
        }
    }

    /**
     * 创建悬浮窗体
     */
    private void createDesktopLayout() {
        if (mDesktopLayout == null) {
            mDesktopLayout = new TextView(this);
            mDesktopLayout.setText("  OTA upgrade will be start after " + tempTime + " secs  ");
            mDesktopLayout.setGravity(Gravity.CENTER);
            LinearLayout.LayoutParams layoutParams = new LinearLayout.LayoutParams(LinearLayout.LayoutParams.WRAP_CONTENT, LinearLayout.LayoutParams.MATCH_PARENT);
            layoutParams.gravity = Gravity.CENTER_HORIZONTAL|Gravity.TOP;
            layoutParams.setMargins(10,5,10,5);
            mDesktopLayout.setTextColor(Color.BLACK);
            mDesktopLayout.setBackground(getDrawable(R.drawable.bg_selector));
        }
    }

    /**
     * 设置WindowManager
     */
    private void createWindowManager() {
        // 取得系统窗体
        mWindowManager = (WindowManager) getApplicationContext().getSystemService(Context.WINDOW_SERVICE);
        // 窗体的布局样式
        mLayout = new WindowManager.LayoutParams();
        // 设置窗体显示类型――TYPE_SYSTEM_ALERT(系统提示)
        if (Build.VERSION.SDK_INT>=26) {//8.0新特性
            mLayout.type = WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY;
        }else {
            mLayout.type = WindowManager.LayoutParams.TYPE_SYSTEM_ALERT;
        }
        // 设置窗体焦点及触摸：
        // FLAG_NOT_FOCUSABLE(不能获得按键输入焦点)
        mLayout.flags = WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE | WindowManager.LayoutParams.FLAG_FULLSCREEN;
        // 设置显示的模式
        mLayout.format = PixelFormat.RGBA_8888;
        // 设置对齐的方法
        mLayout.gravity = Gravity.TOP;// | Gravity.LEFT;
//        mLayout.y = getPosition(getApplicationContext(), "poY", (int) 80);
//        mLayout.x = getPosition(getApplicationContext(), "poX", (int) 0);
        // 设置窗体宽度和高度
        mLayout.width = WindowManager.LayoutParams.WRAP_CONTENT;
        mLayout.height = WindowManager.LayoutParams.WRAP_CONTENT;
//        mLayout.horizontalMargin = 10;
//        mLayout.verticalMargin= 5;

        width = mWindowManager.getDefaultDisplay().getWidth();
        height = mWindowManager.getDefaultDisplay().getHeight();
    }

    private void closeDesk() {
        if (mDesktopLayout.getParent() != null) {
            mWindowManager.removeView(mDesktopLayout);
            mDesktopLayout = null;
        }

        if (timerDesk != null) {
            timerDesk.cancel();
            timerDesk = null;
        }

        if (task != null) {
            task.cancel();
            task = null;
        }
        stopSelf();
    }

    public void StartCheckShowViewClose() {
        if (task == null) {
            task = new TimerTask() {
                @Override
                public void run() {
                    tempTime--;
                    mHandler.sendEmptyMessage(MSG_UPDATE_DESKTOP);
                }
            };
        }
        timerDesk.schedule(task, 1000, 1000);
    }

    /**
     * 显示DesktopLayout
     */
    private void showDesk() {
        BBLog.i(Constants.TAG, "OTANotifyService： mWindowManager showDesk" + mDesktopLayout.getId());
        if (mDesktopLayout.getParent() == null)
            mWindowManager.addView(mDesktopLayout, mLayout);
        StartCheckShowViewClose();
    }

    private String getTimeStr(){
        int hour = mForceAutoCloseDialogRemainSec/3600;
        int min = (mForceAutoCloseDialogRemainSec-hour*3600)/60;
        int sec = mForceAutoCloseDialogRemainSec%60;
        return (hour>0?hour+":":"") + (min>0?min+":":"") +sec;
    }
}
