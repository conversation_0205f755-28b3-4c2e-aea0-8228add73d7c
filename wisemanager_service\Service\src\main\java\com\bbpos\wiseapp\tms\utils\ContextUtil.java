package com.bbpos.wiseapp.tms.utils;

import android.Manifest;
import android.app.AlarmManager;
import android.app.Application;
import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.PendingIntent;
import android.app.ProgressDialog;
import android.bbpos.SystemUpdateConstants;
import android.bluetooth.BluetoothAdapter;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.SharedPreferences;
import android.content.pm.ApplicationInfo;
import android.content.pm.PackageManager;
import android.content.pm.ResolveInfo;
import android.content.res.Configuration;
import android.content.res.Resources;
import android.database.ContentObserver;
import android.graphics.Typeface;
import android.location.LocationManager;
import android.net.ConnectivityManager;
import android.net.NetworkRequest;
import android.net.wifi.WifiManager;
import android.os.Build;
import android.os.Environment;
import android.os.Handler;
import android.os.Looper;
import android.os.Message;
import android.preference.PreferenceManager;
import android.provider.Settings;
import androidx.core.app.ActivityCompat;
import androidx.core.app.NotificationCompat;
import android.text.TextUtils;
import android.util.DisplayMetrics;
import android.view.WindowManager;

import com.bbpos.wiseapp.logger.BBLog;
import com.bbpos.wiseapp.logger.MyDiskLogStrategy;
import com.bbpos.wiseapp.logstream.LogService;
import com.bbpos.wiseapp.network.NetworkStateCallbackImpl;
import com.bbpos.wiseapp.network.NetworkStateListerner;
import com.bbpos.wiseapp.network.NetworkStateReceiver;
import com.bbpos.wiseapp.network.WifiContivityManager;
import com.bbpos.wiseapp.provisioning.ProvisionService;
import com.bbpos.wiseapp.provisioning.ProvisionTimer;
import com.bbpos.wiseapp.remoteviewer.ViewService;
import com.bbpos.wiseapp.security.SecurityOperate;
import com.bbpos.wiseapp.service.ListeningService;
import com.bbpos.wiseapp.service.R;
import com.bbpos.wiseapp.service.common.RequestPermission;
import com.bbpos.wiseapp.service.common.UsualData;
import com.bbpos.wiseapp.service.receiver.FallReciver;
import com.bbpos.wiseapp.service.receiver.SecurityTamperReceiver;
import com.bbpos.wiseapp.service.receiver.WifiControlReceiver;
import com.bbpos.wiseapp.settings.utils.APNManager;
import com.bbpos.wiseapp.settings.utils.ServiceGuardHandler;
import com.bbpos.wiseapp.settings.utils.WifiProxyUtil;
import com.bbpos.wiseapp.settings.utils.WirelessUtil;
import com.bbpos.wiseapp.settings.utils.tlv.StringUtils;
import com.bbpos.wiseapp.system.api.CustomServiceManager;
import com.bbpos.wiseapp.system.api.DeviceInfoApi;
import com.bbpos.wiseapp.system.api.ServiceApi;
import com.bbpos.wiseapp.system.api.SysIntermediateApi;
import com.bbpos.wiseapp.system.api.SystemApi;
import com.bbpos.wiseapp.tms.location.GpsLocationManager;
import com.bbpos.wiseapp.tms.receiver.BatteryStatusReceiver;
import com.bbpos.wiseapp.tms.receiver.NetworkStatusReceiver;
import com.bbpos.wiseapp.tms.receiver.SystemReceiver;
import com.bbpos.wiseapp.tms.receiver.TmsUpdateReceiver;
import com.bbpos.wiseapp.tms.timer.HardwareDetectTimer;
import com.bbpos.wiseapp.utils.Base64Utils;
import com.bbpos.wiseapp.utils.FileUtils;
import com.bbpos.wiseapp.utils.PaymentModeUtils;
import com.bbpos.wiseapp.utils.RSAUtils;
import com.bbpos.wiseapp.utils.SharedPreferencesUtils;
import com.bbpos.wiseapp.websocket.DeviceBidUploadService;
import com.bbpos.wiseapp.websocket.WebSocketCenter;
import com.bbpos.wiseapp.websocket.WebSocketManager;
import com.bbpos.wiseapp.websocket.handler.RulebasedListHandler;
import com.bbpos.wiseapp.websocket.nv.NvWebSocketHandlerCenter;
import com.bbpos.wiseapp.websocket.nv.WsManager;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.Field;
import java.text.SimpleDateFormat;
import java.util.List;
import java.util.Map;

import static com.bbpos.wiseapp.utils.FileUtils.getWifiConfigFilePath;
import static com.bbpos.wiseapp.utils.FileUtils.writeWifiConfigToFile;

public class ContextUtil extends Application {
    private static ContextUtil instance;
    public static ContextUtil getInstance() {
        return instance;
    }

    private static Intent intentService = null;
    private static Intent intentViewService = null;
    private static NotificationCompat.Builder builder;

    public static Typeface tf_trasandina_w03_bold;

    private int mNetworkConnectCounter = 0;

    private SystemReceiver mSystemReceiver = null;
    private TmsUpdateReceiver mTmsUpdateReceiver = null;
    private BatteryStatusReceiver mBatteryStatusReceiver = null;
    private NetworkStatusReceiver mNetworkStatusReceiver = null;
    private NetworkStateReceiver mNetworkStateReceiver = null;
    private ConnectivityManager.NetworkCallback mNetworkCallback;
    private ConnectivityManager mConnectivityManager;
    private BroadcastReceiver mTamperReceiver = null;
    private FallReciver mFallReciver = null;
    private WifiControlReceiver mWifiControlReceiver = null;
    private static ProgressDialog mProgressDialog;
    private static Handler mHandler = new Handler(Looper.getMainLooper()) {
        @Override
        public void handleMessage(Message msg) {
            BBLog.e(BBLog.TAG, "WSTaskOsUpdateService mHandler " + msg.what);
            if (msg.what == 1001) {
                try {
                    if (mProgressDialog == null) {
                        mProgressDialog = new ProgressDialog(ContextUtil.getInstance());
                        mProgressDialog.setProgressStyle(ProgressDialog.STYLE_HORIZONTAL);
                        mProgressDialog.setTitle("Android system update");
                        if (msg.arg1 == SystemUpdateConstants.UpdateStatusConstants.DOWNLOADING) {
                            mProgressDialog.setMessage("Preparing to update...\r\nDownloading");
                        } else if (msg.arg1 == SystemUpdateConstants.UpdateStatusConstants.VERIFYING) {
                            mProgressDialog.setMessage("Preparing to update...\r\nVerifying");
                        } else if (msg.arg1 == SystemUpdateConstants.UpdateStatusConstants.FINALIZING) {
                            mProgressDialog.setMessage("Preparing to update...\r\nFinalizing");
                        }
                        mProgressDialog.setCancelable(false);
                        mProgressDialog.setCanceledOnTouchOutside(false);
                        mProgressDialog.getWindow().setType((WindowManager.LayoutParams.TYPE_SYSTEM_ALERT));
                    }
                    if (!mProgressDialog.isShowing()) {
                        mProgressDialog.show();
                    }
                    float percent = (float) msg.obj;
                    BBLog.e(BBLog.TAG, "WSTaskOsUpdateService setProgress " + (int)(percent*100));
                    mProgressDialog.setProgress((int)(percent*100));
                } catch (Exception e) {
                    e.printStackTrace();
                }
            } else if (msg.what == 1002) {
                try {
                    new Handler(Looper.getMainLooper()).post(new Runnable() {
                        @Override
                        public void run() {
                            mProgressDialog.dismiss();
                        }
                    });
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
    };

    private final ContentObserver mGpsMonitor = new ContentObserver(null) {
        @Override
        public void onChange(boolean selfChange) {
            super.onChange(selfChange);

            boolean enabled = GpsLocationManager.getLocationManager()
                    .isProviderEnabled(LocationManager.GPS_PROVIDER);
            BBLog.e(BBLog.TAG, "onChange:GpsLocationManager gps enabled  = " + enabled );
            if (!enabled) {
                BBLog.e(BBLog.TAG, "onChange: GpsLocationManager release gps resource  ");
                GpsLocationManager.unregistLocationChangeListener();
            } else {
                if (GpsLocationManager.isCareGPS) {
                    GpsLocationManager.registLocationChangeListener();
                }
            }
        }
    };
    @Override
    public void onCreate() {
        super.onCreate();
        //初始化日志
        initLog();
        //初始化ContextUtil.instance和通用context
        initContext();
        //初始化变量
        initVariable();
        //初始化设备接口
        initEquipment();
        //初始化websocket
        initWebsocket();
        //初始化配置
        initConfigure();
        //初始化其他
        initOthers();

        if (Constants.M_GEOFENCE_SET_SERVICE_LAUNCHER) {
            if (Constants.M_GEOFENCE_STATUS == GpsLocationManager.LOCK_SCREEN) {    //如果是WIPE_DATA，则代表Service已经设置成主Launcher了，无需主动调用锁屏
                GpsLocationManager.showOutOfGeofenceLockScreen();
            } else if (Constants.M_GEOFENCE_STATUS == GpsLocationManager.OUT_OF_ZONE) {
                GpsLocationManager.showOutOfGeofenceWarning(GpsLocationManager.WARNING_STATUS_OUT_OF_FENCE_AT_REBOOT);
            }
        }else {
            if (Constants.M_GEOFENCE_STATUS == GpsLocationManager.LOCK_SCREEN || Constants.M_GEOFENCE_STATUS == GpsLocationManager.WIPE_DATA) {
                GpsLocationManager.showOutOfGeofenceLockScreen();
            } else if (Constants.M_GEOFENCE_STATUS == GpsLocationManager.OUT_OF_ZONE) {
                GpsLocationManager.showOutOfGeofenceWarning(GpsLocationManager.WARNING_STATUS_OUT_OF_FENCE_AT_REBOOT);
            }
        }

        tf_trasandina_w03_bold = Typeface.createFromAsset(getAssets(), "fonts/Trasandina-W03-Bold.ttf");
    }

    /***
     * 初始化Log配置
     */
    private void initLog() {
        if (DeviceInfoApi.getIntance().isWisePos4G() && !Build.DISPLAY.contains("WiseManager")) {
            BBLog.setLogOutput(true, false);    //默认值是不输出到控制台log,但是需要记录日志文件
        } else {
            BBLog.setLogOutput(true, true);    //默认值是不输出到控制台log,但是需要记录日志文件
        }
        BBLog.i(BBLog.TAG, "======> ContextUtil, initLog...");
    }

    private void initContext() {
        BBLog.i(BBLog.TAG, "======> ContextUtil, initContext...");
        instance = this;

        ServiceApi.getIntance().init(ContextUtil.getInstance());
        APNManager.init(ContextUtil.getInstance());
        WifiContivityManager.getInstance().init(ContextUtil.getInstance());

        long utc = 0l;
        long utc_target = 0l;
        try {
            utc = Long.valueOf(SysIntermediateApi.getIntance().getProp("ro.build.date.utc"));
            utc_target = new SimpleDateFormat("yyyy-MM-dd").parse("2021-03-01").getTime();
            BBLog.i(BBLog.TAG, "==> ro.build.date.utc=" + utc + "   utc_target=" + utc_target);
        } catch (Exception e) {
            e.printStackTrace();
        }
        //判断ROM的编译时间晚于某个时间的，就确定使用bbpos_android.jar，可执行初始化
		if (utc*1000>utc_target
		&& (DeviceInfoApi.getIntance().isWisePosTouch() 
		 || DeviceInfoApi.getIntance().isWisePosTouchPlus()
		 || DeviceInfoApi.getIntance().isWisePos5()
		 || DeviceInfoApi.getIntance().isWisePos5Plus()
		 || DeviceInfoApi.getIntance().isWisePosGo())
         || DeviceInfoApi.getIntance().isWisePosLE()
         || DeviceInfoApi.getIntance().isWisePosLP()) {
        	CustomServiceManager.getInstance().init(getApplicationContext());
		}

        SharedPreferencesUtils.init(ContextUtil.getInstance());

        ServiceGuardHandler.getInstance(ContextUtil.getInstance());
    }

    private void initConfigure() {
        BBLog.i(BBLog.TAG, "======> ContextUtil, initConfigure...");
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            mNetworkCallback = new NetworkStateCallbackImpl();
            NetworkRequest.Builder builder = new NetworkRequest.Builder();
            NetworkRequest request = builder.build();
            mConnectivityManager = (ConnectivityManager) ContextUtil.getInstance().getSystemService(Context.CONNECTIVITY_SERVICE);
            if (mConnectivityManager != null) {
                mConnectivityManager.registerNetworkCallback(request, mNetworkCallback);
            }
        } else {
            //Android 24以下版本，由于支持ConnectivityManager.CONNECTIVITY_ACTION，所以分散到各个Timer中
            if (mNetworkStateReceiver == null) {
                mNetworkStateReceiver = new NetworkStateReceiver();
                ContextUtil.getInstance().registerReceiver(mNetworkStateReceiver, new IntentFilter(ConnectivityManager.CONNECTIVITY_ACTION));
            }
        }
        NetworkStateListerner.setNetworkStateListener(mINetworkStateListener);

        if (mTmsUpdateReceiver == null) {
            mTmsUpdateReceiver = new TmsUpdateReceiver();
            IntentFilter intentFilter = new IntentFilter(Intent.ACTION_PACKAGE_ADDED);
            intentFilter.addAction(Intent.ACTION_PACKAGE_REMOVED);
            intentFilter.addAction(Intent.ACTION_PACKAGE_REPLACED);
            intentFilter.addDataScheme("package");
            ContextUtil.getInstance().registerReceiver(mTmsUpdateReceiver, intentFilter, RequestPermission.REQUEST_PERMISSION_MY_BROADCAST, null);
        }

        if (mBatteryStatusReceiver == null) {
            mBatteryStatusReceiver = new BatteryStatusReceiver();
            IntentFilter intentFilter = new IntentFilter();
            intentFilter.addAction(Intent.ACTION_BATTERY_CHANGED);
            intentFilter.addAction(Intent.ACTION_POWER_CONNECTED);
            intentFilter.addAction(Intent.ACTION_POWER_DISCONNECTED);
//            intentFilter.addAction(Intent.ACTION_BATTERY_OKAY);
//            intentFilter.addAction(Intent.ACTION_BATTERY_LOW);
            ContextUtil.getInstance().registerReceiver(mBatteryStatusReceiver, intentFilter, RequestPermission.REQUEST_PERMISSION_MY_BROADCAST, null);
        }

        if (mNetworkStatusReceiver == null) {
            mNetworkStatusReceiver = new NetworkStatusReceiver(ContextUtil.getInstance());
            IntentFilter intentFilter = new IntentFilter();
            intentFilter.addAction(WifiManager.RSSI_CHANGED_ACTION);
            intentFilter.addAction(WifiManager.NETWORK_STATE_CHANGED_ACTION);
            ContextUtil.getInstance().registerReceiver(mNetworkStatusReceiver, intentFilter, RequestPermission.REQUEST_PERMISSION_MY_BROADCAST, null);
        }

        if (mSystemReceiver == null) {
            mSystemReceiver = new SystemReceiver();
            IntentFilter intentFilter = new IntentFilter();
            intentFilter.addAction("prepare_to_factory_reset");
            intentFilter.addAction(Intent.ACTION_SHUTDOWN);
            intentFilter.addAction(Intent.ACTION_TIME_TICK);
            intentFilter.addAction(BluetoothAdapter.ACTION_STATE_CHANGED);
            intentFilter.addAction(BroadcastActions.ACTION_EXPIRE_REBOOT);
            intentFilter.addAction(BroadcastActions.ACTION_GEOFENCING_DETECTED_WARNING);
            intentFilter.addAction(BroadcastActions.ACTION_GEOFENCING_DETECTED_LOCK);
            intentFilter.addAction(BroadcastActions.ACTION_GEOFENCING_RESET_LOCKSCREEN_TIMER);
            intentFilter.addAction(UsualData.ACTION_DELETE_APK);
            intentFilter.addAction(UsualData.ACTION_LAUNCH_COMPLETE);
            intentFilter.addAction(BroadcastActions.LOGCAT_STREAM_TIME);
            ContextUtil.getInstance().registerReceiver(mSystemReceiver, intentFilter, RequestPermission.REQUEST_PERMISSION_MY_BROADCAST, null);
        }

        //注册 安全芯片受 攻击广播
        if (mTamperReceiver == null) {
            mTamperReceiver = new SecurityTamperReceiver();
            IntentFilter intentFilter = new IntentFilter();
            intentFilter.addAction(SecurityTamperReceiver.ACTION_TAMPER);
            ContextUtil.getInstance().registerReceiver(mTamperReceiver, intentFilter, RequestPermission.REQUEST_PERMISSION_MY_BROADCAST, null);
        }

        //注册设备跌落自由落体检测广播
        if (mFallReciver == null) {
            mFallReciver = new FallReciver();
            IntentFilter intentFilter = new IntentFilter();
            intentFilter.addAction(FallReciver.ACTION_FALL_FREE);
            ContextUtil.getInstance().registerReceiver(mFallReciver, intentFilter, RequestPermission.REQUEST_PERMISSION_MY_BROADCAST, null);
        }

        if (mWifiControlReceiver == null) {
            mWifiControlReceiver = new WifiControlReceiver();
            IntentFilter intentFilter = new IntentFilter();
            intentFilter.addAction(BroadcastActions.OPEN_WIFI);
            intentFilter.addAction(BroadcastActions.CLOSE_WIFI);
            intentFilter.addAction(BroadcastActions.GET_WIFI_STATE);
            intentFilter.addAction(WifiManager.WIFI_STATE_CHANGED_ACTION);
            ContextUtil.getInstance().registerReceiver(mWifiControlReceiver, intentFilter, RequestPermission.REQUEST_PERMISSION_MY_BROADCAST, null);
        }
    }

    private void initVariable() {
        if ("true".equals(SharedPreferencesUtils.getSharePreferencesValue(UsualData.SHARED_PREFERENCES_ENABLE_WISELOG, "false"))) {
            MyDiskLogStrategy.enableSaveLoggerFile(true);
        } else {
            MyDiskLogStrategy.enableSaveLoggerFile(false);
        }

        BBLog.i(BBLog.TAG, "======> ContextUtil, initVariable...");
//        WebSocketManager.url = SharedPreferencesUtils.getSharePreferencesValue(UsualData.SHARED_PREFERENCES_WEBSOCKET_SERVER_URL, "");
        WebSocketManager.url = "ws://35.75.3.206:8080/status/websocket/register";
        BBLog.e(BBLog.TAG, "WebSocket URL = " + WebSocketManager.url);

        Constants.allowUploadPaymentDataPkgs = FileUtils.getAllowPaymentPks();
        BBLog.w(BBLog.TAG, "payment app 許可包名：" + Constants.allowUploadPaymentDataPkgs);

        Constants.start_up_model = SharedPreferencesUtils.getSharePreferencesValue(UsualData.SHARED_PREFERENCES_MODE_IN_CONFIG, "uat");
        BBLog.w(BBLog.TAG, "start up model：" + Constants.start_up_model);

        String kioskMode = SharedPreferencesUtils.getSharePreferencesValue(UsualData.SHARED_PREFERENCES_KIOSK, "false");
        SysIntermediateApi.getIntance().updateSystemProperty(kioskMode);

        Constants.m_gms_applist = SharedPreferencesUtils.getSharePreferencesValue(UsualData.SHARED_PREFERENCES_GMS_APPLIST, "");
        BBLog.w(BBLog.TAG, "GMS套件APP：" + Constants.m_gms_applist);
        Constants.UPLOAD_MODE = SharedPreferencesUtils.getSharePreferencesValue(UsualData.SHARED_PREFERENCES_UPLOAD_MODE, "1");
        Constants.LAST_CLIENT_APK_MD5 = SharedPreferencesUtils.getSharePreferencesValue(UsualData.SHARED_PREFERENCES_CLIENT_APP_URL_MD5, "");
        BBLog.w(BBLog.TAG, "上送模式：" + Constants.UPLOAD_MODE);

        WebSocketTaskListManager.clearWSTaskResultCache();
        RulebasedListHandler.clearRuleResultCache();

		try {
			BBLog.e(BBLog.TAG, "开始迁移 WPL 数据 ----> ");
			SharedPreferences sp = PreferenceManager.getDefaultSharedPreferences(ContextUtil.getInstance());
			//保存mdm下发的wifiprofile list数据
			String wifiProfileJson = sp.getString(SPKeys.WEBSOCKET_WIFL_PROFILE_LIST,"");
			//保存上次连接成功的wifiprofile 数据
			String cacheLastConnectedWPData = SharedPreferencesUtils.getSharePreferencesValue(SPKeys.LAST_CONNECTED_WIFL_PROFILE,"");
			byte[] encode = null;
			String wplData = "";

			BBLog.e(BBLog.TAG, "1、迁移 WPL 数据 " );
			if (!TextUtils.isEmpty(wifiProfileJson)) {
                /*
				encode = DESUtils.encryptCbc(wifiProfileJson.getBytes(),
						SecurityOperate.getInstance().getDeviceDK(ContextUtil.getInstance()).getBytes(),
                        SecurityOperate.getInstance().getDeviceDIV(ContextUtil.getInstance()).getBytes(),
						DESUtils.Padding.PKCS5_PADDING);
				wplData = StringUtils.Hex2String(encode);
                */
                wplData = AESUtil.encryptToHex(wifiProfileJson);
				BBLog.e(BBLog.TAG, "initVariable wplData step 1 : " + wplData);
				if (!TextUtils.isEmpty(getWifiConfigFilePath())) {
					if (!TextUtils.isEmpty(wplData)) {
						boolean result = writeWifiConfigToFile(wplData);
						if (result) {
                            sp.edit().remove(SPKeys.WEBSOCKET_WIFL_PROFILE_LIST).commit();
                        }
					}
				} else {
					sp.edit().putString(SPKeys.WEBSOCKET_WIFL_PROFILE_LIST, wplData).commit();
				}
			}

			BBLog.e(BBLog.TAG, "2、迁移 last connect WP 数据  " );
			if (!TextUtils.isEmpty(cacheLastConnectedWPData)) {
                /*
				encode = DESUtils.encryptCbc(cacheLastConnectedWPData.getBytes(),
						SecurityOperate.getInstance().getDeviceDK(ContextUtil.getInstance()).getBytes(),
                        SecurityOperate.getInstance().getDeviceDIV(ContextUtil.getInstance()).getBytes(),
						DESUtils.Padding.PKCS5_PADDING);
				wplData = StringUtils.Hex2String(encode);
                */
                wplData = AESUtil.encryptToHex(cacheLastConnectedWPData);
				BBLog.e(BBLog.TAG, "initVariable wplData step 2 :  " + wplData);
				if (!TextUtils.isEmpty(wplData)) {
					SharedPreferencesUtils.clearByKey(SPKeys.LAST_CONNECTED_WIFL_PROFILE);
					SharedPreferencesUtils.setSharePreferencesValue(SPKeys.LAST_CONNECTED_ENCRYPT_WIFL_PROFILE, wplData);
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		}


        try {
            Constants.U_MY_VERSION = getPackageManager().getPackageInfo(getPackageName(), 0).versionName;
            BBLog.w(BBLog.TAG, "当前版本：" + Constants.U_MY_VERSION);
        } catch (PackageManager.NameNotFoundException e) {
            e.printStackTrace();
        } catch (Exception e) {
            e.printStackTrace();
        }

        try {
            String paymentLists = SharedPreferencesUtils.getSharePreferencesValue(UsualData.SHARED_PREFERENCES_PAYMENT_PACKAGE_LIST, "");
            if (TextUtils.isEmpty(paymentLists)) {
                if (TextUtils.isEmpty(PaymentModeUtils.getEnablePaymentAppPackageList(ContextUtil.getInstance()))) {
                    paymentLists = "com.bbpos.bbdevice.testapp, " +
                            "com.bbpos.bbdevice.wsp7xexample, com.bbpos.bbdevice.wsp7xexample2, com.bbpos.bbdevice.wsp7xexample3, com.bbpos.bbdevice.wsp7xexample4, com.bbpos.bbdevice.wsp7xexample5, " +
                            "com.bbpos.bbdevice.wiseposexample, com.bbpos.bbdevice.wiseposexample2, com.bbpos.bbdevice.wiseposexample3, com.bbpos.bbdevice.wiseposexample4, com.bbpos.bbdevice.wiseposexample5, " +
                            "com.bbpos.wisepay.app";
                    //設置默認可以使用startEmv的應用程序
                    PaymentModeUtils.setEnablePaymentAppPackageList(ContextUtil.getInstance(), paymentLists);
                    SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_PAYMENT_PACKAGE_LIST, Constants.PRE_LOCAL + paymentLists);
                }
            } else if (paymentLists.contains(Constants.PRE_PROVISION)) {
                paymentLists = paymentLists.replace(Constants.PRE_PROVISION,"");
                String romConfigPaymentList = PaymentModeUtils.getEnablePaymentAppPackageList(ContextUtil.getInstance());//已设置的包名数据
                if (!paymentLists.equals(romConfigPaymentList)) {
                    PaymentModeUtils.setEnablePaymentAppPackageList(ContextUtil.getInstance(), paymentLists);
                    SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_PAYMENT_PACKAGE_LIST, Constants.PRE_PROVISION + paymentLists);
                }
            } else if (paymentLists.contains(Constants.PRE_LOCAL)) {
                paymentLists = paymentLists.replace(Constants.PRE_LOCAL,"");
                String romConfigPaymentList = PaymentModeUtils.getEnablePaymentAppPackageList(ContextUtil.getInstance());//已设置的包名数据
                if (!paymentLists.equals(romConfigPaymentList)) {
                    PaymentModeUtils.setEnablePaymentAppPackageList(ContextUtil.getInstance(), paymentLists);
                    SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_PAYMENT_PACKAGE_LIST, Constants.PRE_LOCAL + paymentLists);
                }
            }
            BBLog.w(BBLog.TAG, "支付app使能列表: " + PaymentModeUtils.getEnablePaymentAppPackageList(ContextUtil.getInstance()));
        } catch (Exception e) {
            e.printStackTrace();
        }

        if (!TextUtils.isEmpty(DeviceInfoApi.getIntance().getOSUpdateLowBatLevel())) {
            try {
                int level = Integer.valueOf(DeviceInfoApi.getIntance().getOSUpdateLowBatLevel());
                Constants.BAT_LOW_FOR_OSUPDATE = level;
            } catch (NumberFormatException e) {
                e.printStackTrace();
            }
        }
        if (!TextUtils.isEmpty(DeviceInfoApi.getIntance().getOSUpdateLowBatLevelCharging())) {
            try {
                int level = Integer.valueOf(DeviceInfoApi.getIntance().getOSUpdateLowBatLevelCharging());
                Constants.BAT_LOW_FOR_OSUPDATE_CHARGING = level;
            } catch (NumberFormatException e) {
                e.printStackTrace();
            }
        }
        if (!TextUtils.isEmpty(DeviceInfoApi.getIntance().getSystemLowBatLevel())) {
            try {
                int level = Integer.valueOf(DeviceInfoApi.getIntance().getSystemLowBatLevel());
                Constants.BAT_LOW_FOR_SYYTEM = level;
            } catch (NumberFormatException e) {
                e.printStackTrace();
            }
        }
		
        Constants.M_GEOFENCE_STATUS = Integer.valueOf(SharedPreferencesUtils.getSharePreferencesValue(UsualData.SHARED_PREFERENCES_GPS_STATUS, GpsLocationManager.IN_ZONE +""));
        BBLog.e(BBLog.TAG, "Constants.M_GEOFENCE_STATUS = " + Constants.M_GEOFENCE_STATUS);
		Constants.STORE_ID = SharedPreferencesUtils.getSharePreferencesValue(UsualData.SHARED_PREFERENCES_STORE_ID, "");
        Constants.STORE_SSID = SharedPreferencesUtils.getSharePreferencesValue(UsualData.SHARED_PREFERENCES_STORE_SSID, "");
        Constants.STORE_IP = SharedPreferencesUtils.getSharePreferencesValue(UsualData.SHARED_PREFERENCES_STORE_IP, "");
        BBLog.w(BBLog.TAG, "-- Constants.STORE_ID = " + Constants.STORE_ID + "-- Constants.STORE_SSID = " + Constants.STORE_SSID + "-- Constants.STORE_IP = " + Constants.STORE_IP);
        try {
            if (StringUtils.isNumeric(SharedPreferencesUtils.getSharePreferencesValue(UsualData.SHARED_PREFERENCES_GPS_GEOFENCE_VALID_DISTANCE, "500"))) {
                GpsLocationManager.validErrorDistance = Long.parseLong(SharedPreferencesUtils.getSharePreferencesValue(UsualData.SHARED_PREFERENCES_GPS_GEOFENCE_VALID_DISTANCE, "500"));
            } else {
                GpsLocationManager.validErrorDistance = 500;
            }
        } catch (Exception e) {
            GpsLocationManager.validErrorDistance = 500;
            e.printStackTrace();
        }

        try {
            String profile = SharedPreferencesUtils.getSharePreferencesValue(UsualData.SHARED_PREFERENCES_GPS_GEOFENCE_CUR_PROFILE, "");
            if (!TextUtils.isEmpty(profile)) {
                JSONObject profileJson = new JSONObject(profile);
                GpsLocationManager.initGeoProfile(profileJson);
            } else {
                GpsLocationManager.initGeoProfile(null);
            }
        } catch (JSONException e) {
            e.printStackTrace();
        }

        Constants.EGINX_PROXY_ENABLE = SharedPreferencesUtils.getSharePreferencesValue(UsualData.SHARED_PREFERENCES_NGINX_ENABLE, false);
        Constants.EGINX_PROXY_IP = SharedPreferencesUtils.getSharePreferencesValue(UsualData.SHARED_PREFERENCES_NGINX_IP, "");

        Constants.S3_RESOURCE_URL = SharedPreferencesUtils.getSharePreferencesValue(UsualData.SHARED_PREFERENCES_WM_API_URL, "");
    }

    private void initEquipment() {
        BBLog.i(BBLog.TAG, "======> ContextUtil, initEquipment...");
		try {
			if (DeviceInfoApi.getIntance().isWisePosPro() || DeviceInfoApi.getIntance().isWisePosTouch() || DeviceInfoApi.getIntance().isWisePosTouchPlus()) { //暫時先獲取7MD bid
				String bid = SharedPreferencesUtils.getSharePreferencesValue(UsualData.SHARED_PREFERENCES_DEVICE_BID_INFO,"");
				boolean isOtaRunning = SharedPreferencesUtils.getSharePreferencesValue(UsualData.SHARED_PREFERENCES_NEED_WISEPOSPRO_OTA_FLAG, false);
				BBLog.e(BBLog.TAG, "initEquipment: -----------------------------------bid = "+bid + ", isOtaRunning = "+isOtaRunning);
				// TODO: 2020/7/28   修改为每次reboot后，都获取一次设备信息(wc battery需要每天reboot后上送一次)
				if (/*TextUtils.isEmpty(bid) && */!Boolean.valueOf(isOtaRunning)) {
					Intent targetIntent = new Intent(ContextUtil.getInstance(), DeviceBidUploadService.class);
					targetIntent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
					ContextUtil.getInstance().startService(targetIntent);
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		}

		try {
            //打開wifi
            WifiManager mWifiManager = (WifiManager) getApplicationContext().getSystemService(WIFI_SERVICE);
            if (!mWifiManager.isWifiEnabled()) {
                mWifiManager.setWifiEnabled(true);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        try {
            //注册BLE Scanner，扫描BEACON;
            if (GpsLocationManager.isBTBeaconScanNeed()) {
                GpsLocationManager.registerBLEScanner();
                Helpers.sendBroad(getApplicationContext(), BroadcastActions.BLE_SCAN_TIMER_START_BC);
            }
            Constants.BT_MAC = SharedPreferencesUtils.getSharePreferencesValue(UsualData.SHARED_PREFERENCES_BT_MAC, "");
            Constants.BT_ENABLE = SharedPreferencesUtils.getSharePreferencesValue(UsualData.SHARED_PREFERENCES_BT_ENABLE, false);
            BBLog.e(BBLog.TAG, "init BT Mac = " + Constants.BT_MAC);
            //先获取allowToUseBT，是否允许使用BT,如果允许：则Constants.BT_STATUS按当前系统的蓝牙开关状态来赋值常开或者常关
            //如果不允许，则Constants.BT_STATUS直接赋值常关；
            BluetoothAdapter mBluetoothAdapter = BluetoothAdapter.getDefaultAdapter();
            if (TextUtils.isEmpty(Constants.BT_MAC)) {
                //mac为空，需要打開藍牙来取值，在广播那边获取取值，并判断是否要关闭
                if (!mBluetoothAdapter.isEnabled()) {
                    mBluetoothAdapter.enable();
                } else {
                    Constants.BT_MAC = WirelessUtil.getBluetoothMacAddress();
                    BBLog.e(BBLog.TAG, "BT Mac = " + Constants.BT_MAC);
                    SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_BT_MAC, Constants.BT_MAC);
                    if (Constants.BT_ENABLE) {
                        Constants.BT_STATUS = true;
                    } else {
                        mBluetoothAdapter.disable();
                    }
                }
            } else {
                //mac不为空，不需要开启取值的情况下，如果不允许使用且蓝牙开着，则主动关闭
                if (!Constants.BT_ENABLE && mBluetoothAdapter.isEnabled()) {
                    mBluetoothAdapter.disable();
                }
            }
            Constants.IMEI_NO = WirelessUtil.getIMEI(ContextUtil.getInstance());
            BBLog.e(BBLog.TAG, "IMEI_NO = " + Constants.IMEI_NO);
        } catch (Exception e) {
            e.printStackTrace();
        }

        try {
            //监听gps定位开关状态
            getContentResolver().registerContentObserver(
                    Settings.Secure.getUriFor(Settings.System.LOCATION_PROVIDERS_ALLOWED), false, mGpsMonitor);
            //启动定位
            if (ActivityCompat.checkSelfPermission(ContextUtil.getInstance(), Manifest.permission.ACCESS_FINE_LOCATION) == PackageManager.PERMISSION_GRANTED
                    && ActivityCompat.checkSelfPermission(ContextUtil.getInstance(), Manifest.permission.ACCESS_COARSE_LOCATION) == PackageManager.PERMISSION_GRANTED) {
                // TODO: Consider calling
                GpsLocationManager.registLocationChangeListener();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        try {
            //啓動AccessibilityService
            if (DeviceInfoApi.getIntance().isWisePosPro()) {
                ListeningService.updateAccessibility(ContextUtil.getInstance());
                if (!ListeningService.isAccessibilitySettingsOn(ContextUtil.getInstance(), ListeningService.class.getCanonicalName(), ListeningService.class.getSimpleName())) {
                    Intent intent = new Intent(Settings.ACTION_ACCESSIBILITY_SETTINGS);
                    intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                    startActivity(intent);
                } else {
                    Intent intent = new Intent(ContextUtil.getInstance(), ListeningService.class);
                    startService(intent);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void initWebsocket() {
        BBLog.i(BBLog.TAG, "======> ContextUtil, initWebsocket...");
        try {
            //註冊Websokcet處理單元
            WebSocketCenter.init(ContextUtil.getInstance(), new NvWebSocketHandlerCenter(ContextUtil.getInstance()));
        } catch (Exception e) {
            e.printStackTrace();
        }

        try {
            //keystore初始化
//            KeyStoreManager manager = new KeyStoreManager(ContextUtil.getInstance(),"wiseapp");
            if (WebSocketManager.m_need_rsa) {
                //加密密钥准备
//                String privatekeyIndex = SharedPreferencesUtils.getSharePreferencesValue(SPKeys.PRIVATE_KEY_INDEX, "");
//                String publickeyIndex = SharedPreferencesUtils.getSharePreferencesValue(SPKeys.PUBLIC_KEY_INDEX, "");
//                if (!TextUtils.isEmpty(privatekeyIndex) && !TextUtils.isEmpty(publickeyIndex)) {
//                    WebSocketManager.m_private_key = manager.decryptData(privatekeyIndex);
//                    WebSocketManager.m_public_key = manager.decryptData(publickeyIndex);
//                    BBLog.e(BBLog.TAG, "已存在公私钥 publickey=" + WebSocketManager.m_public_key + "   privatekey=" + WebSocketManager.m_private_key);
//                }

                if (true) {//TextUtils.isEmpty(WebSocketManager.m_private_key) || TextUtils.isEmpty(WebSocketManager.m_public_key)) {
                    Map<String, Object> map = RSAUtils.genKeyPair();
                    WebSocketManager.m_public_key = RSAUtils.getPublicKey(map);
                    WebSocketManager.m_private_key = RSAUtils.getPrivateKey(map);
                    BBLog.e(BBLog.TAG, "生成公私钥");
//                    String privatekey_index = manager.encryptData(WebSocketManager.m_private_key);
//                    String publickey_index = manager.encryptData(WebSocketManager.m_public_key);
//                    SharedPreferencesUtils.setSharePreferencesValue(SPKeys.PRIVATE_KEY_INDEX, privatekey_index);
//                    SharedPreferencesUtils.setSharePreferencesValue(SPKeys.PUBLIC_KEY_INDEX, publickey_index);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        //复制签名证书到目录下
        try {
            String filePath = null;
            File file = null;
            if (new File("/bbpos").exists()) {
                filePath = "/bbpos" + File.separator + Constants.CERT_FILE_PATH;
            }

            if (TextUtils.isEmpty(filePath)) {
                filePath = Environment.getExternalStorageDirectory().getPath()+"/Share"+File.separator+ContextUtil.getInstance().getPackageName()+File.separator+"mdm.cer";
            }
            BBLog.w(BBLog.TAG, "验签文件路徑：" + filePath);
            file = new File(filePath);
            if (file.exists()) {
                file.delete();
            }
            initCertificateFile(filePath);
        } catch (Exception e) {
            BBLog.e(BBLog.TAG, "APK 验签文件创建失败");
            e.printStackTrace();
        }

        //任務恢復
        WebSocketTaskListManager.resetDoingWSTaskState();
        RulebasedListHandler.cleanAppList();
    }

    private void initOthers() {
        BBLog.i(BBLog.TAG, "======> ContextUtil, initOthers...");
        String clientAppPackageName = SharedPreferencesUtils.getSharePreferencesValue(UsualData.SHARED_PREFERENCES_STARTUP_APP_PACKAGE_NAME, "");
        if (!TextUtils.isEmpty(clientAppPackageName) && !UsualData.LOADER_711_PACKAGE_NAME.equals(clientAppPackageName)) {
            if (!(SharedPreferencesUtils.getSharePreferencesValue(UsualData.SHARED_PREFERENCES_NEED_WISEPOS5_OTA_FLAG, false)
                    || SharedPreferencesUtils.getSharePreferencesValue(UsualData.SHARED_PREFERENCES_NEED_WISEPOSPRO_OTA_FLAG, false)
                    || SharedPreferencesUtils.getSharePreferencesValue(UsualData.SHARED_PREFERENCES_NEED_WISEPOS4G_OTA_FLAG, false))) {
                ProvisionService.startClientApp();
            }
        }

        if (DeviceInfoApi.getIntance().isWisePos5() || DeviceInfoApi.getIntance().isWisePos5Plus()) {
            String curLauncher = SharedPreferencesUtils.getSharePreferencesValue(UsualData.SHARED_PREFERENCES_CURRENT_LAUNCHER, "");
            if (TextUtils.isEmpty(curLauncher)) {
                setLauncherApp(getApplicationContext(), UsualData.LAUNCHER_PACKAGE_NAME);
            } else {
                setLauncherApp(getApplicationContext(), curLauncher);
            }

            try {
                String apkVerify = SharedPreferencesUtils.getSharePreferencesValue(UsualData.SHARED_PREFERENCES_APK_VERIFY , "");
                if ("true".equals(apkVerify) || "false".equals(apkVerify)) {
                    if ("true".equals(apkVerify)) {
                        BBLog.e(BBLog.TAG, "— 開啓APK驗簽流程 —");
                    } else if ("false".equals(apkVerify)) {
                        BBLog.e(BBLog.TAG, "— 關閉APK驗簽流程 —");
                    }
                    SysIntermediateApi.getIntance().setProp("persist.bbpos.enable_apk_verify", apkVerify); //Disable 驗簽
                }
            } catch (Exception e) {
                e.printStackTrace();
            }

            try {
                String package_list = "";
                String installerJson = SharedPreferencesUtils.getSharePreferencesValue(UsualData.SHARED_PREFERENCES_INSTALLER_PACKAGE_NAME, "");
                if (!TextUtils.isEmpty(installerJson)) {
                    JSONArray packageList = new JSONArray(installerJson);
                    if (packageList.length() > 0) {
                        for (int i = 0; i < packageList.length(); i++) {
                            if (i == 0) {
                                package_list += packageList.getJSONObject(i).optString("package_name");
                            } else {
                                package_list = package_list + "|" + packageList.getJSONObject(i).optString("package_name");
                            }
                        }
                        SystemApi.setAllowInstallApkPackeName(getApplicationContext(), package_list);
                    }
                } else {
                    SystemApi.setAllowInstallApkPackeName(getApplicationContext(), "com.bbpos.wiseapp.service|com.cyanogenmod.filemanager|com.mswipe.mswip_appst_a_in");
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        try {
            //獲取資源
            getResources();
        } catch (Exception e) {
            e.printStackTrace();
        }

        WindowManager m =(WindowManager) getSystemService(Context.WINDOW_SERVICE);
        DisplayMetrics dm = new DisplayMetrics();// 获取屏幕宽、高用
        m.getDefaultDisplay().getMetrics(dm);
        BBLog.w(BBLog.TAG, "本機型號" + DeviceInfoApi.getIntance().getWisePOSModel());
        BBLog.w(BBLog.TAG, "當前設備：" + Build.MODEL);
        BBLog.w(BBLog.TAG, "分辨率為：" + dm.widthPixels + " x " + dm.heightPixels);
        BBLog.w(BBLog.TAG, "像素密度：" + dm.densityDpi + " dpi");
        m.getDefaultDisplay().getRealMetrics(dm);
        BBLog.w(BBLog.TAG, "實際分辨率為：" + dm.widthPixels + " x " + dm.heightPixels);

        if (DeviceInfoApi.getIntance().isWisePosPro()) {
//            SysIntermediateApi.getIntance().setProp("persist.logd.size", "1048576"); //Enable adb
            try {
//                Runtime.getRuntime().exec("logcat -G 50M");
                SecurityOperate.getInstance().execCommand(ContextUtil.getInstance(),"logcat -G 50M");
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
//        startLogServiceSchedule();
//        Helpers.sendBroad(getApplicationContext(), BroadcastActions.WISE_LOG_UPLOAD_BC);
    }

    public static void startWebsocketService(Context contextUtil, String publickKey) {
        try {
            WebSocketManager.m_server_public_key = SecurityOperate.getInstance().getServerSPK(contextUtil);
            BBLog.e(BBLog.TAG, "服务器公钥：" + WebSocketManager.m_server_public_key);
            if (!TextUtils.isEmpty(WebSocketManager.url)) {
//                WebSocketSetting.setConnectUrl(WebSocketManager.url + "?serialNo=" + Base64Utils.encode(RSAUtils.encryptByPublicKey(DeviceInfoApi.getIntance().getSerialNumber().getBytes(), WebSocketManager.m_server_public_key))
//                        +"&publicKey="+publickKey);//必选
//                WebSocketSetting.setReconnectWithNetworkChanged(true);
//                contextUtil.startService(new Intent(contextUtil, WebSocketService.class));
//                WebSocketCenter.init(contextUtil,new WebSocketHandleCenter(ContextUtil.getInstance()));
                BBLog.e("WiseApp2.0", WebSocketManager.url);
                if (Constants.M_WEBSOCKET_MSGVER == 1) {
                    BBLog.e("WiseApp2.0", "Constants.M_WEBSOCKET_MSGVER == 1 ");
                    WsManager.getInstance().init(
                            new WsManager.Builder()
                                    .Context(ContextUtil.getInstance())
                                    .SocketListener(new NvWebSocketHandlerCenter(ContextUtil.getInstance()))
//                                    .Proxy(getProxy())
                                    .SrviceUrl(WebSocketManager.url + "?serialNo=" + Base64Utils.encode(RSAUtils.encryptByPublicKey(DeviceInfoApi.getIntance().getSerialNumber().getBytes(), WebSocketManager.m_server_public_key)) + "&publicKey=" + publickKey + "&query=1" + "&msgVer=" + Constants.M_WEBSOCKET_MSGVER)
                                    .SrviceUrlForReconn(WebSocketManager.url + "?serialNo=" + Base64Utils.encode(RSAUtils.encryptByPublicKey(DeviceInfoApi.getIntance().getSerialNumber().getBytes(), WebSocketManager.m_server_public_key)) + "&publicKey=" + publickKey + "&query=0" + "&msgVer=" + Constants.M_WEBSOCKET_MSGVER)
                                    .ConneccTimeout(15000)
                    );
                } else if (Constants.M_WEBSOCKET_MSGVER == 3) {
                    String connect_url = "serialNo=" + Base64Utils.encode(DeviceInfoApi.getIntance().getSerialNumber().getBytes()) + "&publicKey=" + Base64Utils.encode(publickKey.getBytes()) + "&query=1" + "&msgVer=" + Constants.M_WEBSOCKET_MSGVER + "&timestamp=" + System.currentTimeMillis();
                    String reconnect_url = "serialNo=" + Base64Utils.encode(DeviceInfoApi.getIntance().getSerialNumber().getBytes()) + "&publicKey=" + Base64Utils.encode(publickKey.getBytes()) + "&query=0" + "&msgVer=" + Constants.M_WEBSOCKET_MSGVER + "&timestamp=" + System.currentTimeMillis();
                    WsManager.getInstance().init(
                            new WsManager.Builder()
                                    .Context(ContextUtil.getInstance())
                                    .SocketListener(new NvWebSocketHandlerCenter(ContextUtil.getInstance()))
//                                    .Proxy(getProxy())
                                    .SrviceUrl(WebSocketManager.url + "?" + connect_url + "&signature=" + RSAUtils.sign(connect_url.getBytes(), WebSocketManager.m_private_key))
                                    .SrviceUrlForReconn(WebSocketManager.url + "?" + reconnect_url + "&signature=" + RSAUtils.sign(reconnect_url.getBytes(), WebSocketManager.m_private_key))
                                    .ConneccTimeout(15000)
                    );
                }
//                WebSocketCenter.init(ContextUtil.getInstance(),new NvWebSocketHandlerCenter(ContextUtil.getInstance()));
            } else {
                BBLog.e(BBLog.TAG, "WebSocketManager.url 为空，无法连接WebSocket");
            }
        } catch (Exception e) {
            BBLog.e(BBLog.TAG, "startWebsocketService: " + e.getMessage());
            e.printStackTrace();
        }
    }

    public static void startWebsocketService(Context contextUtil) {
//        WebSocketManager.url = SharedPreferencesUtils.getSharePreferencesValue(UsualData.SHARED_PREFERENCES_WEBSOCKET_SERVER_URL, "");
        WebSocketManager.url = "ws://35.75.3.206:8080/status/websocket/register";
        BBLog.e(BBLog.TAG, "开始连接WebSocket url = " + WebSocketManager.url);
        if (!TextUtils.isEmpty(WebSocketManager.url)) {
//            WebSocketSetting.setConnectUrl(WebSocketManager.url + "?serialNo=" + DeviceInfoApi.getIntance().getSerialNumber());//必选
//            WebSocketSetting.setReconnectWithNetworkChanged(true);
//            contextUtil.startService(new Intent(contextUtil, WebSocketService.class));
//            WebSocketCenter.init(contextUtil,new WebSocketHandleCenter(ContextUtil.getInstance()));
            WsManager.getInstance().init(
                    new WsManager.Builder()
                    .Context(ContextUtil.getInstance())
                    .SocketListener(new NvWebSocketHandlerCenter(ContextUtil.getInstance()))
                    .Proxy(getProxy())
                    .SrviceUrl(WebSocketManager.url + "?serialNo=" + DeviceInfoApi.getIntance().getSerialNumber())
                    .ConneccTimeout(15000)
                );
//            WebSocketCenter.init(ContextUtil.getInstance(),new NvWebSocketHandlerCenter(ContextUtil.getInstance()));
        }
    }

    public static String getProxy() {
        String proxyAddress = System.getProperty("http.proxyHost");
        String portStr = System.getProperty("http.proxyPort");
        String PROXY = "http://"+proxyAddress+":"+portStr;
        BBLog.v("WiseApp2.0", "nv websocket proxy: "+PROXY);
        if (TextUtils.isEmpty(proxyAddress) || TextUtils.isEmpty(portStr)) {
            return null;
        }else {
            return PROXY;
        }
    }


    @Override
    public void onConfigurationChanged(Configuration newConfig) {
        if (newConfig.fontScale != 1)//非默认值
            getResources();
        super.onConfigurationChanged(newConfig);
    }

    @Override
    public Resources getResources() {
        Resources res = super.getResources();
        if (res.getConfiguration().fontScale != 1) {//非默认值
            Configuration newConfig = new Configuration();
            newConfig.setToDefaults();//设置默认
            res.updateConfiguration(newConfig, res.getDisplayMetrics());
        }

        return res;
    }

    public static void setLauncherApp(Context context, String packageName) {
        final Intent mainIntent = new Intent(Intent.ACTION_MAIN, null);
        mainIntent.addCategory(Intent.CATEGORY_HOME);
        final PackageManager packageManager = context.getPackageManager();
        List<ResolveInfo> apps = packageManager.queryIntentActivities(mainIntent, 0);
        if (apps == null) {
            return;
        } else {
            for (ResolveInfo info : apps) {
                if (packageName.equals(info.activityInfo.packageName)) {
                    BBLog.e(BBLog.TAG, "设置" + packageName + "为 Launcher");
                    Intent intent = new Intent("setCustionLauncherPackageName");
                    BBLog.e(BBLog.TAG, "setLauncherApp: " + info.activityInfo.packageName + "|" + info.activityInfo.name);
                    intent.putExtra("packageName", info.activityInfo.packageName + "|" + info.activityInfo.name);//packageName|name
                    getInstance().sendBroadcast(intent, RequestPermission.REQUEST_PERMISSION_MY_BROADCAST);
                    SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_CURRENT_LAUNCHER, packageName);
                    break;
                }
            }
        }
    }

    private void initCertificateFile(String filePath) throws IOException {
        File certFile = new File(filePath);
        File cerFileDir = certFile.getParentFile();
        BBLog.e(BBLog.TAG, "cerFileDir: " + cerFileDir.getAbsolutePath());
        if (!cerFileDir.exists()) {
            cerFileDir.mkdirs();
            certFile.createNewFile();
        }
        FileOutputStream fos = null;
        InputStream is = getResources().openRawResource(R.raw.mdm);
        try {
            byte[] data = new byte[2048];
            int nbread = 0;
            fos = new FileOutputStream(certFile);
            while ((nbread = is.read(data)) > -1) {
                fos.write(data, 0, nbread);
            }
        } catch (Exception ex) {
            certFile.delete();
            ex.printStackTrace();
        } finally {
            IOUtils.flushCloseOutputStream(fos);
        }
    }

    NetworkStateListerner.INetworkStateListener mINetworkStateListener = new NetworkStateListerner.INetworkStateListener() {
        @Override
        public void getNetworkState(int state) {
            BBLog.e(BBLog.TAG, "getNetworkState, state = " + state);
            int lastNetworkState = Integer.valueOf(SharedPreferencesUtils.getSharePreferencesValue(UsualData.SHARED_PREFERENCES_LAST_NETWORK_STATE, String.valueOf(UsualData.NETWORK_STATUS_INAVAILABLE)));
            if (state == UsualData.NETWORK_STATUS_INAVAILABLE) {
                BBLog.e(BBLog.TAG, "网络未连接");
            } else {
                mNetworkConnectCounter++;
                BBLog.d(BBLog.TAG, "mNetworkConnectCounter: " + mNetworkConnectCounter + "  Constants.M_GEOFENCE_STATUS=" + Constants.M_GEOFENCE_STATUS);

                if (mNetworkConnectCounter == 1) {
                    GpsLocationManager.registLocationChangeListener();
                    if (WebSocketManager.m_need_rsa) {
                        startWebsocketService(ContextUtil.getInstance(), WebSocketManager.m_public_key);
                    } else {
                        startWebsocketService(ContextUtil.getInstance());
                    }
                }

                //啓動WebSocket
                if (mNetworkConnectCounter == 1) {
                    BBLog.e(BBLog.TAG, "网络第1次连接");

                    if (!WirelessUtil.ping()) {
                        if (WifiProxyUtil.isWifiProxy(getApplicationContext())) {
//                            WifiProxyUtil.resetConfigProxy(getApplicationContext());
//                            BBLog.e(BBLog.TAG, "网络第1次连接，清除wifi proxy");
                            BBLog.e(BBLog.TAG, "网络第1次连接，不清除代理");
                        } else {
                            WifiProxyUtil.setConfigProxy(getApplicationContext());
                            BBLog.e(BBLog.TAG, "网络第1次连接，设置wifi proxy");
                        }
                    } else {
                        doNetworkConnectedCallback();
                    }
                } else if (mNetworkConnectCounter == 2) {
                    BBLog.e(BBLog.TAG, "网络第2次连接");
                    if (!WirelessUtil.ping()) {
                        if (WifiProxyUtil.isWifiProxy(getApplicationContext())) {
//                            WifiProxyUtil.resetConfigProxy(getApplicationContext());
//                            BBLog.e(BBLog.TAG, "网络第2次连接，清除wifi proxy");
                            BBLog.e(BBLog.TAG, "网络第2次连接，不清除代理");
                        } else {
                            WifiProxyUtil.setConfigProxy(getApplicationContext());
                            BBLog.e(BBLog.TAG, "网络第2次连接，设置wifi proxy");
                        }
                    } else {
                        doNetworkConnectedCallback();
                    }
                } else if (mNetworkConnectCounter == 3) {
                    BBLog.e(BBLog.TAG, "网络第3次连接");
                    if (!WirelessUtil.ping()) {
                        if (WifiProxyUtil.isWifiProxy(getApplicationContext())) {
//                            WifiProxyUtil.resetConfigProxy(getApplicationContext());
//                            BBLog.e(BBLog.TAG, "网络第3次连接，清除wifi proxy");
                            BBLog.e(BBLog.TAG, "网络第3次连接，不清除代理");
                        } else {
                            WifiProxyUtil.setConfigProxy(getApplicationContext());
                            BBLog.e(BBLog.TAG, "网络第3次连接，设置wifi proxy");
                        }
                    } else {
                        doNetworkConnectedCallback();
                    }
                } else {
                    if (lastNetworkState == UsualData.NETWORK_STATUS_INAVAILABLE) {
                        BBLog.e(BBLog.TAG, "网络断开已恢复连接");
                        doNetworkConnectedCallback();
                    }
                }
            }
            SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_LAST_NETWORK_STATE, String.valueOf(state));
        }
    };

    private void doNetworkConnectedCallback() {
        BBLog.e(BBLog.TAG, "网络已连接");
//        gotoProvisioning();
        if (!DeviceInfoApi.getIntance().isWisePosGo()) {
            HardwareDetectTimer.startHardwareDetectTimer(ContextUtil.getInstance());
        }
//        WiseLogUploadTimer.startWiseLogTimer(ContextUtil.getInstance());
//        Helpers.sendBroad(getApplicationContext(), BroadcastActions.WISE_LOG_UPLOAD_BC);
        //上送终端信息
//        Helpers.sendBroad(getApplicationContext(), BroadcastActions.TER_INFO_UPLOAD_BC);
        Helpers.sendBroad(getApplicationContext(), BroadcastActions.POLL_TIMER_START_BC);
        BBLog.w(BBLog.TAG, "mINetworkStateListener 網絡重連 發送 WSTASK_EXEC_BC");
//        Helpers.sendBroad(getApplicationContext(), UsualData.WSTASK_EXEC_BC);
    }

    private void gotoProvisioning() {
        BBLog.w(BBLog.TAG, "Constants.IS_PROVISIONING_FROM_SCAN_QRCODE = " + Constants.IS_PROVISIONING_FROM_SCAN_QRCODE);
        BBLog.w(BBLog.TAG, "Constants.IS_FIRST_PROVISIONING_COMPLETED = " + Constants.IS_FIRST_PROVISIONING_COMPLETED);
        if (Constants.IS_PROVISIONING_FROM_SCAN_QRCODE) {
            Constants.IS_PROVISIONING_FROM_SCAN_QRCODE = false;
            ProvisionTimer.startProvisionTimer(getApplicationContext(), "1");
        } else {
            if (!Constants.IS_FIRST_PROVISIONING_COMPLETED) {
//                if (DeviceInfoApi.getIntance().isWisePosPro()) {
//                    if (new File(FileUtils.getWiseAppConfigPath() + FileUtils.FILE_SEPARATOR + FileUtils.CONFIG_NAME + FileUtils.FILE_FORMAT).exists()) {
//                        ProvisionTimer.startProvisionTimer(getApplicationContext(), "0");
//                    }
//                } else {
                    ProvisionTimer.startProvisionTimer(getApplicationContext(), "0");
//                }
            } else {
                BBLog.w(BBLog.TAG, "首次Provisioning已完成");
            }
        }
    }

    public static void showStatusBarIcon(Context context) {
        String channelID = "0";
        String channelName = "WiseManager";
        NotificationManager nm = (NotificationManager) context.getSystemService(Context.NOTIFICATION_SERVICE);
        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.O) {
            NotificationChannel channel = new NotificationChannel(channelID, channelName, NotificationManager.IMPORTANCE_DEFAULT);
            channel.enableLights(false);
            channel.enableVibration(false);
            channel.setVibrationPattern(new long[]{0});
            channel.setSound(null, null);
            nm.createNotificationChannel(channel);
        }
        if (builder == null) {
            builder = new NotificationCompat.Builder(context, channelID);
//            builder.setContentText("Websocket not connect");
            builder.setSmallIcon(R.drawable.notification_logo);
            builder.setAutoCancel(false);
            builder.setVibrate(new long[]{0});
            builder.setSound(null);
            builder.setOngoing(true);
            builder.setWhen(System.currentTimeMillis());
            //创建通知时指定channelID
            builder.setChannelId(channelID);
        }
        Notification notification = builder.build();
        notification.flags |= NotificationCompat.FLAG_ONGOING_EVENT | NotificationCompat.FLAG_NO_CLEAR;
        nm.notify(Constants.NOTIFICATION_ID_ICON, notification);
    }

    public static void hideStatusBarIcon(Context context) {
        NotificationManager nm = (NotificationManager) context.getSystemService(Context.NOTIFICATION_SERVICE);
        nm.cancel(Constants.NOTIFICATION_ID_ICON);
    }

    public static void changeNotificationMsg(Context context, String msg) {
        NotificationManager nm = (NotificationManager) context.getSystemService(Context.NOTIFICATION_SERVICE);
        builder.setContentText(msg);
        nm.notify(Constants.NOTIFICATION_ID_ICON, builder.build());
    }

    private void startLogServiceSchedule() {
        try {
            startLogService(false);

            String service_app_str = SharedPreferencesUtils.getSharePreferencesValue(UsualData.LOG_STREAM_SERVICE_APP_EXECUTING, "");
            BBLog.e(BBLog.TAG, "检测是否有需要继续进行的 LOG_STREAMING 任务：" + service_app_str);
            if (TextUtils.isEmpty(service_app_str)) {
                return;
            }
            JSONObject service_app = new JSONObject(service_app_str);
            if (service_app != null && RuleStatus.IMPLEMENTED.equals(service_app.optString(ParameterName.stateDesc))) {
                String beginDateStr = service_app.getString(ParameterName.beginDate);
                String endDateStr = service_app.getString(ParameterName.endDate);
                long nowTime = System.currentTimeMillis();
                try {
                    long beginTime;
                    long endTime;
                    if (DateTimeUtils.isNumeric(beginDateStr) && DateTimeUtils.isNumeric(endDateStr)) {
                        beginTime = new Long(beginDateStr).longValue();
                        endTime = new Long(endDateStr).longValue();
                    } else {
                        SimpleDateFormat sdf = new SimpleDateFormat(Constants.dateFormat);
                        beginTime = sdf.parse(beginDateStr).getTime();
                        endTime = sdf.parse(endDateStr).getTime();
                    }

                    if ("9999-12-31 23:59:59".equals(endDateStr)) {
                        if (beginTime < nowTime && nowTime < (beginTime+24*3*60*60*1000)) {
                            //在周期内，启动日志；
                            startLogService(false);
                            BBLog.i(BBLog.TAG, "启动LogService定时器");
                            Intent intentTmp = new Intent(BroadcastActions.LOGCAT_STREAM_TIME);
//                            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
//                                intentTmp.setComponent(new ComponentName(getPackageName(), SystemReceiver.class.getName()));
//                            }
                            PendingIntent pi = PendingIntent.getBroadcast(ContextUtil.getInstance(), 0, intentTmp, PendingIntent.FLAG_UPDATE_CURRENT);
                            AlarmManager am = (AlarmManager) getSystemService(Context.ALARM_SERVICE);
                            am.cancel(pi);
                            long timeOnMillis = beginTime+24*3*60*60*1000;

                            BBLog.i(BBLog.TAG, "LogService定时器 截至時間 " + timeOnMillis);
                            if (Build.VERSION.SDK_INT < Build.VERSION_CODES.KITKAT) {
                                am.set(AlarmManager.RTC_WAKEUP, timeOnMillis, pi);
                            } else {
                                am.setExact(AlarmManager.RTC_WAKEUP, timeOnMillis, pi);
                            }
                        }
                    } else {
                        if (beginTime < nowTime && nowTime < endTime) {
                            //在周期内，启动日志；
//                            startLogService(false);
                            LogService.startLogServiceUpload();
                        }
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void startLogServiceScheduleJson(JSONObject service_app) {
        try {
            if (service_app != null) {
                String beginDateStr = service_app.getString(ParameterName.beginDate);
                String endDateStr = service_app.getString(ParameterName.endDate);
                long nowTime = System.currentTimeMillis();
                try {
                    long beginTime;
                    long endTime;
                    if (DateTimeUtils.isNumeric(beginDateStr) && DateTimeUtils.isNumeric(endDateStr)) {
                        beginTime = new Long(beginDateStr).longValue();
                        endTime = new Long(endDateStr).longValue();
                    } else {
                        SimpleDateFormat sdf = new SimpleDateFormat(Constants.dateFormat);
                        beginTime = sdf.parse(beginDateStr).getTime();
                        endTime = sdf.parse(endDateStr).getTime();
                    }
                    BBLog.i(BBLog.TAG, "startLogServiceScheduleJson beginDateStr=" + beginDateStr);
                    BBLog.i(BBLog.TAG, "startLogServiceScheduleJson endDateStr=" + endDateStr);
                    BBLog.i(BBLog.TAG, "startLogServiceScheduleJson beginTime=" + beginTime);
                    BBLog.i(BBLog.TAG, "startLogServiceScheduleJson endTime=" + endTime);
                    if ("9999-12-31 23:59:59".equals(endDateStr)) {
                        if (beginTime < nowTime && nowTime < (beginTime+24*3*60*60*1000)) {
                            //在周期内，启动日志；
                            startLogService(false);
                            BBLog.i(BBLog.TAG, "启动LogService定时器");
                            Intent intentTmp = new Intent(BroadcastActions.LOGCAT_STREAM_TIME);
//                            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
//                                intentTmp.setComponent(new ComponentName(ContextUtil.getInstance().getPackageName(), SystemReceiver.class.getName()));
//                            }
                            PendingIntent pi = PendingIntent.getBroadcast(ContextUtil.getInstance(), 0, intentTmp, PendingIntent.FLAG_UPDATE_CURRENT);
                            AlarmManager am = (AlarmManager) ContextUtil.getInstance().getSystemService(Context.ALARM_SERVICE);
                            am.cancel(pi);
                            long timeOnMillis = beginTime+24*3*60*60*1000;

                            BBLog.i(BBLog.TAG, "LogService定时器 截至時間 " + timeOnMillis);
                            if (Build.VERSION.SDK_INT < Build.VERSION_CODES.KITKAT) {
                                am.set(AlarmManager.RTC_WAKEUP, timeOnMillis, pi);
                            } else {
                                am.setExact(AlarmManager.RTC_WAKEUP, timeOnMillis, pi);
                            }
                        }
                    } else {
                        if (beginTime < nowTime && nowTime < endTime) {
                            //在周期内，启动日志；
                            LogService.startLogServiceUpload();
                        }
                    }
                    service_app.put(ParameterName.stateDesc, RuleStatus.IMPLEMENTED);
                    SharedPreferencesUtils.setSharePreferencesValue(UsualData.LOG_STREAM_SERVICE_APP_EXECUTING, service_app.toString());
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void startLogService(boolean uploadRecent) {
        BBLog.e(BBLog.TAG, "startLogService 启动日志上送 isLogExecuting = " + LogService.isLogExecuting);
        if (!LogService.isLogExecuting) {
            intentService = new Intent(ContextUtil.getInstance(), LogService.class);
            intentService.putExtra("uploadRecent", uploadRecent);
            ContextUtil.getInstance().startService(intentService);
        }
    }

    public static void stopLogService() {
        BBLog.e(BBLog.TAG, "startLogService 停止日志上送 isLogExecuting = " + LogService.isLogExecuting);
        if (LogService.isLogExecuting == true) {
            ContextUtil.getInstance().stopService(intentService);
            intentService = null;
            LogService.isLogExecuting = false;
        }
    }

    public static void startViewService(String bucket_path) {
        if (!ViewService.isViewExecuting) {
            intentViewService = new Intent(ContextUtil.getInstance(), ViewService.class);
            intentViewService.putExtra("bucket_path", bucket_path);
            ContextUtil.getInstance().startService(intentViewService);
        }
    }

    public static void stopViewService() {
        if (ViewService.isViewExecuting == true) {
            ContextUtil.getInstance().stopService(intentViewService);
            intentViewService = null;
            ViewService.isViewExecuting = false;
        }
    }

    @Override
    public void onTerminate() {
        super.onTerminate();
        getContentResolver()
                .registerContentObserver(
                        Settings.Secure
                                .getUriFor(Settings.System.LOCATION_PROVIDERS_ALLOWED),
                        false, mGpsMonitor);
    }

    public static void saveUploadLog(String absolutePath) {
        try {
            String uploadListStr = SharedPreferencesUtils.getSharePreferencesValue(UsualData.LOGGER_UPLOAD_LIST, "");
            if (TextUtils.isEmpty(uploadListStr)) {
                JSONArray tempArray = new JSONArray();
                JSONObject tempJson = new JSONObject();
                tempJson.put("filePath", absolutePath);
                tempArray.put(tempJson);
                SharedPreferencesUtils.setSharePreferencesValue(UsualData.LOGGER_UPLOAD_LIST, tempArray.toString());
            } else {
                JSONArray tempArray = new JSONArray(uploadListStr);
                JSONObject tempJson = new JSONObject();
                tempJson.put("filePath", absolutePath);
                tempArray.put(tempJson);
                SharedPreferencesUtils.setSharePreferencesValue(UsualData.LOGGER_UPLOAD_LIST, tempArray.toString());
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void terminateWiseService() {
//        ContextUtil.getInstance().unregisterReceiver(mSystemReceiver);
//        ContextUtil.getInstance().unregisterReceiver(mTmsUpdateReceiver);
//        ContextUtil.getInstance().unregisterReceiver(mBatteryStatusReceiver);
//        ContextUtil.getInstance().unregisterReceiver(mNetworkStatusReceiver);
//        ContextUtil.getInstance().unregisterReceiver(mNetworkStateReceiver);
//        ContextUtil.getInstance().unregisterReceiver(mTamperReceiver);
//
//        WebSocketCenter.disconnect();

        System.exit(0);
    }
	
	    // 获取状态栏高度
    public static int getStatusHeight() {
        Class<?> c = null;
        Object obj = null;
        Field field = null;
        int x = 0, barHeight = 0;
        try {
            c = Class.forName("com.android.internal.R$dimen");
            obj = c.newInstance();
            field = c.getField("status_bar_height");
            x = Integer.parseInt(field.get(obj).toString());
            barHeight = getInstance().getResources().getDimensionPixelSize(x);
        } catch (Exception e1) {
            e1.printStackTrace();
        }
        return barHeight;
    }

    /**
     * 判断当前应用是否是debug状态
     */
    public static boolean isApkInDebug() {
        try {
            ApplicationInfo info = instance.getApplicationInfo();
            return (info.flags & ApplicationInfo.FLAG_DEBUGGABLE) != 0;
        } catch (Exception e) {
            return false;
        }
    }

    public static void sendProgressDialog(int status, float percent) {
        Message msg = new Message();
        msg.what = 1001;
        msg.arg1 = status;
        msg.obj = percent;
        mHandler.sendMessage(msg);
    }

    public static void closeProgressDialog() {
        Message msg = new Message();
        msg.what = 1002;
        mHandler.sendMessage(msg);
    }

    public static void makeToast(String msg) {
        Helpers.makeToast(getInstance(), msg);
    }
}