package com.bbpos.wiseapp.service;

import android.app.Service;
import android.content.pm.IPackageDeleteObserver;
import android.content.pm.IPackageInstallObserver;
import android.net.Uri;
import android.os.Build;
import android.os.RemoteException;
import android.text.TextUtils;
import android.util.Log;

import com.bbpos.wiseapp.logger.BBLog;
import com.bbpos.wiseapp.sdk.app.IAppDeleteObserver;
import com.bbpos.wiseapp.sdk.app.IAppInstallObserver;
import com.bbpos.wiseapp.sdk.app.IAppManager;
import com.bbpos.wiseapp.sdk.app.IAppUploadTransRecord;
import com.bbpos.wiseapp.sdk.app.UsageStats;

import com.bbpos.wiseapp.logger.BBLog;
import com.bbpos.wiseapp.service.appdata.DataCollect;
import com.bbpos.wiseapp.service.appdata.db.TransRecordDBHelper;
import com.bbpos.wiseapp.service.appdata.model.TransRecordData;
import com.bbpos.wiseapp.system.api.Helper;
import com.bbpos.wiseapp.tms.utils.Constants;
import com.bbpos.wiseapp.tms.utils.ContextUtil;
import com.bbpos.wiseapp.tms.utils.Helpers;
import com.bbpos.wiseapp.utils.FileUtils;
import com.bbpos.wiseapp.websocket.WebSocketSender;

import java.io.File;
import java.util.HashMap;
import java.util.List;

/**
 * 应用管理相关接口
 * <AUTHOR>
 * */
public class AppManagerService extends IAppManager.Stub{
	private Service service;
	public AppManagerService(Service service){
		this.service = service;
	}

	@SuppressWarnings("deprecation")
	@Override
	public void installApp(String apkPath, IAppInstallObserver observer, String installerPackageName) throws RemoteException {
		BBLog.i(CloudService.V2_TAG, "install file " + apkPath);
		try {
			Uri uri = Uri.parse("file://" + apkPath);
			// 覆盖安装标志
			int installFlags = 0x00000002;
			if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
				Helper.installPackage(service, apkPath, observer, installerPackageName);
			} else {
                PackageInstallObserver observerTmp = new PackageInstallObserver(apkPath,observer);
				Helper.installPackage(service, uri, observerTmp, installFlags, null);
			}
		} catch (Exception e) {
			BBLog.e(CloudService.V2_TAG, "install fail", e);
			if (!Build.MODEL.equals("7MD")) {
				observer.onInstallFinished("unknown pkgname.", 99, "install fail"+e.getMessage());
			}
		}

		/*
		//参数1 上下文, 参数2 在AndroidManifest中的android:authorities值, 参数3  共享的文件
		Intent install = new Intent(Intent.ACTION_VIEW);
		install.setDataAndType(Uri.parse("file://" + apkPath), "application/vnd.android.package-archive");
		install.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
		service.startActivity(install);
		*/
	}

	@Override
	public void uninstallApp(String packageName, IAppDeleteObserver observer) throws RemoteException {
		PackageDeleteObserver delObserver = new PackageDeleteObserver(observer);
		try {
			if (Build.MODEL.equals("7MD")) {
				Helper.deletePackage(service, packageName, observer);
			} else {
				Helper.deletePackage(service, packageName, delObserver, 0);
			}
		} catch (Exception e) {
			observer.onDeleteFinished(packageName, 99,"uninstall failed"+e.getMessage());
			BBLog.e(CloudService.V2_TAG, "uninstall failed", e);
		}
	}
	
	/** pm的安装回调，安装结束时调用，处理安装结果*/
	private static class PackageInstallObserver extends IPackageInstallObserver.Stub {
		private String apkPath;
		private IAppInstallObserver observer;
		PackageInstallObserver(String apkPath, IAppInstallObserver observer) {
			this.apkPath = apkPath;
			this.observer= observer;
		}

		public void packageInstalled(String packageName, int returnCode) throws RemoteException {
			BBLog.i(CloudService.V2_TAG, "packageInstalled " + packageName + " " + returnCode);

			/** 记录安装结果*/
			/**安装结果 key:appName value:returnCode*/
			HashMap<String,String> resultMap = new HashMap<String,String>();
			resultMap.put("appName", packageName);
			resultMap.put("returnCode", returnCode+"");
			File apkFile = new File(apkPath);
			//安装成功后删除apk文件
			if(apkFile.exists() && returnCode==1)
				apkFile.delete();
			observer.onInstallFinished(packageName,returnCode,"");
		}
	}

	/** 卸载结果 */
	private static class PackageDeleteObserver extends  IPackageDeleteObserver.Stub {
		private IAppDeleteObserver observer;
		public PackageDeleteObserver(IAppDeleteObserver observer){
			this.observer = observer;
		}

		@Override
		public void packageDeleted(String packageName, int returnCode) throws RemoteException {
			BBLog.i(CloudService.V2_TAG, "packageDeleted " + packageName + " returnCode " + returnCode);
			/** 记录卸载结果*/
			StringBuilder result = new StringBuilder();
			result.append(packageName);
			result.append(",");
			result.append(String.valueOf(returnCode));
			observer.onDeleteFinished(packageName, returnCode,"");
		}
	}

	@Override
	public List<UsageStats> getUsageStats(String yyyyMMdd) throws RemoteException {
		// TODO Auto-generated method stub
		DataCollect dataCollect = new DataCollect(this.service);
		return dataCollect.getUsageStatsByDate(yyyyMMdd);
	}

	@Override
	public String getWisePayConfig() throws RemoteException {
		return null;
	}

	@Override
	public void uploadTransRecord(String paymentData, IAppUploadTransRecord iAppUploadTransRecord) throws RemoteException {

	}

	@Override
	public boolean isPermissionGranted() throws RemoteException {
		return false;
	}
}
