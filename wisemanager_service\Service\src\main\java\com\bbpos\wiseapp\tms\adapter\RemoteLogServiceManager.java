package com.bbpos.wiseapp.tms.adapter;

import android.os.Binder;
import android.os.IBinder;
import android.os.IInterface;
import android.os.Parcel;
import android.os.RemoteException;

/**
 * Created by Administrator on 2017-5-5.
 */

public abstract interface RemoteLogServiceManager extends IInterface {
    public abstract int setLogEnable(boolean paramBoolean) throws RemoteException;
    public abstract String getLogDir() throws RemoteException;
    public abstract int setLogSaveTime(int paramInt) throws RemoteException;
    public abstract int setBakupTime(int paramInt) throws RemoteException;
    public abstract int bakupNow() throws RemoteException;
    public abstract int clear() throws RemoteException;

    public static abstract class Stub extends Binder implements RemoteLogServiceManager {
        private static final String DESCRIPTOR = "com.android.remotelogservice.RemoteLogServiceManager";
        static final int TRANSACTION_setLogEnable = 1;
        static final int TRANSACTION_getLogDir = 2;
        static final int TRANSACTION_setLogSaveTime = 3;
        static final int TRANSACTION_setBakupTime = 4;
        static final int TRANSACTION_bakupNow = 5;
        static final int TRANSACTION_clear = 6;

        public Stub() {
            attachInterface(this, "com.android.remotelogservice.RemoteLogServiceManager");
        }

        public static RemoteLogServiceManager asInterface(IBinder obj) {
            if (obj == null) {
                return null;
            }
            IInterface iin = obj.queryLocalInterface("com.android.remotelogservice.RemoteLogServiceManager");
            if ((iin != null) && ((iin instanceof RemoteLogServiceManager))) {
                return (RemoteLogServiceManager)iin;
            }
            return new Proxy(obj);
        }

        public IBinder asBinder() {
            return this;
        }

        public boolean onTransact(int code, Parcel data, Parcel reply, int flags) throws RemoteException {
            int result;
            int temp;
            switch (code) {
            case 1598968902:
                reply.writeString("com.android.remotelogservice.RemoteLogServiceManager");
                return true;
            case 1:
                data.enforceInterface("com.android.remotelogservice.RemoteLogServiceManager");

                boolean _arg0 = data.readInt() != 0;
                result = setLogEnable(_arg0);
                reply.writeNoException();
                reply.writeInt(result);
                return true;
            case 2:
                data.enforceInterface("com.android.remotelogservice.RemoteLogServiceManager");
                String logDir = getLogDir();
                reply.writeNoException();
                reply.writeString(logDir);
                return true;
            case 3:
                data.enforceInterface("com.android.remotelogservice.RemoteLogServiceManager");

                temp = data.readInt();
                result = setLogSaveTime(temp);
                reply.writeNoException();
                reply.writeInt(result);
                return true;
            case 4:
                data.enforceInterface("com.android.remotelogservice.RemoteLogServiceManager");

                temp = data.readInt();
                result = setBakupTime(temp);
                reply.writeNoException();
                reply.writeInt(result);
                return true;
            case 5:
                data.enforceInterface("com.android.remotelogservice.RemoteLogServiceManager");
                result = bakupNow();
                reply.writeNoException();
                reply.writeInt(result);
                return true;
            case 6:
                data.enforceInterface("com.android.remotelogservice.RemoteLogServiceManager");
                result = clear();
                reply.writeNoException();
                reply.writeInt(result);
                return true;
            }

            return super.onTransact(code, data, reply, flags);
        }
        private static class Proxy implements RemoteLogServiceManager {
            private IBinder mRemote;

            Proxy(IBinder remote) {
                this.mRemote = remote;
            }

            public IBinder asBinder() {
                return this.mRemote;
            }

            public String getInterfaceDescriptor() {
                return "com.android.remotelogservice.RemoteLogServiceManager";
            }
            public int setLogEnable(boolean enable) throws RemoteException { Parcel _data = Parcel.obtain();
                Parcel _reply = Parcel.obtain();
                int result;
                try {
                    _data.writeInterfaceToken("com.android.remotelogservice.RemoteLogServiceManager");
                    _data.writeInt(enable ? 1 : 0);
                    this.mRemote.transact(1, _data, _reply, 0);
                    _reply.readException();
                    result = _reply.readInt();
                }
                finally {
                    _reply.recycle();
                    _data.recycle();
                }
                return result; }
            public String getLogDir() throws RemoteException {
                Parcel _data = Parcel.obtain();
                Parcel _reply = Parcel.obtain();
                String result;
                try {
                    _data.writeInterfaceToken("com.android.remotelogservice.RemoteLogServiceManager");
                    this.mRemote.transact(2, _data, _reply, 0);
                    _reply.readException();
                    result = _reply.readString();
                }
                finally {
                    _reply.recycle();
                    _data.recycle();
                }
                return result;
            }
            public int setLogSaveTime(int day) throws RemoteException { Parcel _data = Parcel.obtain();
                Parcel _reply = Parcel.obtain();
                int result;
                try {
                    _data.writeInterfaceToken("com.android.remotelogservice.RemoteLogServiceManager");
                    _data.writeInt(day);
                    this.mRemote.transact(3, _data, _reply, 0);
                    _reply.readException();
                    result = _reply.readInt();
                }
                finally {
                    _reply.recycle();
                    _data.recycle();
                }
                return result; }
            public int setBakupTime(int hour) throws RemoteException {
                Parcel _data = Parcel.obtain();
                Parcel _reply = Parcel.obtain();
                int result;
                try {
                    _data.writeInterfaceToken("com.android.remotelogservice.RemoteLogServiceManager");
                    _data.writeInt(hour);
                    this.mRemote.transact(4, _data, _reply, 0);
                    _reply.readException();
                    result = _reply.readInt();
                }
                finally {
                    _reply.recycle();
                    _data.recycle();
                }
                return result;
            }
            public int bakupNow() throws RemoteException { Parcel _data = Parcel.obtain();
                Parcel _reply = Parcel.obtain();
                int result;
                try {
                    _data.writeInterfaceToken("com.android.remotelogservice.RemoteLogServiceManager");
                    this.mRemote.transact(5, _data, _reply, 0);
                    _reply.readException();
                    result = _reply.readInt();
                }
                finally {
                    _reply.recycle();
                    _data.recycle();
                }
                return result; }
            public int clear() throws RemoteException {
                Parcel _data = Parcel.obtain();
                Parcel _reply = Parcel.obtain();
                int result;
                try {
                    _data.writeInterfaceToken("com.android.remotelogservice.RemoteLogServiceManager");
                    this.mRemote.transact(6, _data, _reply, 0);
                    _reply.readException();
                    result = _reply.readInt();
                }
                finally {
                    _reply.recycle();
                    _data.recycle();
                }
                return result;
            }
        }
    }
}
