package com.bbpos.wiseapp.settings.utils;

import android.bluetooth.BluetoothAdapter;
import android.content.Context;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import android.net.wifi.WifiConfiguration;
import android.net.wifi.WifiInfo;
import android.net.wifi.WifiManager;
import android.os.Build;
import android.os.Handler;
import android.provider.Settings;
import android.telephony.CellInfo;
import android.telephony.CellInfoCdma;
import android.telephony.CellInfoGsm;
import android.telephony.CellInfoLte;
import android.telephony.CellInfoWcdma;
import android.telephony.CellSignalStrengthCdma;
import android.telephony.CellSignalStrengthGsm;
import android.telephony.CellSignalStrengthLte;
import android.telephony.CellSignalStrengthWcdma;
import android.telephony.TelephonyManager;
import android.text.TextUtils;
import android.util.Log;

import com.bbpos.wiseapp.logger.BBLog;
import com.bbpos.wiseapp.network.wificonfig.WiFiProfileManager;
import com.bbpos.wiseapp.security.SecurityOperate;
import com.bbpos.wiseapp.tms.utils.ContextUtil;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.LineNumberReader;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.net.HttpURLConnection;
import java.net.InetSocketAddress;
import java.net.MalformedURLException;
import java.net.NetworkInterface;
import java.net.Proxy;
import java.net.URL;
import java.net.URLConnection;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Locale;
import java.util.concurrent.CountDownLatch;

import static android.content.Context.WIFI_SERVICE;

/**
 * Created by Ian on 2018/12/28.
 */
public class WirelessUtil {
    /* security type */
    public static final int SECURITY_NONE = 0;
    public static final int SECURITY_WEP = 1;
    public static final int SECURITY_PSK = 2;
    public static final int SECURITY_WPA_PSK = 3;
    public static final int SECURITY_WPA2_PSK = 4;
    public static final int SECURITY_EAP = 5;
    public static final int SECURITY_WAPI_PSK = 6;
    public static final int SECURITY_WAPI_CERT = 7;

    public static final int NETWORK_UNKNOW = 1; // 2G
    public static final int NETWORK_2G = 2; // 2G
    public static final int NETWORK_3G = 3; // 3G
    public static final int NETWORK_4G = 4; // 4G

    public static final int WIFI_SIGNAL_LEVEL_MAX = 5;
    public static final int WIFILOCK_SIGNAL_LEVEL_MAX = 4;
    public static final String AIRPLANE_MODE_RADIOS = "airplane_mode_radios";
    public static final String AIRPLANE_MODE_TOGGLEABLE_RADIOS = "airplane_mode_toggleable_radios";
    public static boolean isRadioAllowed(Context context, String type) {
        if (!isAirplaneModeOn(context)) {
            return true;
        }
        String toggleable = Settings.Global.getString(context.getContentResolver(), AIRPLANE_MODE_TOGGLEABLE_RADIOS);
        return toggleable != null && toggleable.contains(type);
    }

    public static boolean isAirplaneModeOn(Context context) {
        return Settings.Global.getInt(context.getContentResolver(),
                Settings.Global.AIRPLANE_MODE_ON, 0) != 0;
    }

    /* 获取当前连接WIFI的SSID     */
    public static String getCurConnectSSID(Context context) {
        ConnectivityManager cm = (ConnectivityManager) context.getSystemService(Context.CONNECTIVITY_SERVICE);
        WifiManager wm = (WifiManager) context.getApplicationContext().getSystemService(WIFI_SERVICE);
        NetworkInfo wifiNetworkInfo = cm.getNetworkInfo(ConnectivityManager.TYPE_WIFI);
        if (wifiNetworkInfo.isConnected()) {
            if (wm != null) {
                WifiInfo winfo = wm.getConnectionInfo();
                if (winfo != null) {
                    String s = winfo.getSSID();
                    if (s.length() > 2 && s.charAt(0) == '"' && s.charAt(s.length() - 1) == '"') {
                        BBLog.i(BBLog.TAG, "getCurConnectSSID = " + s.substring(1, s.length() - 1));
                        return s.substring(1, s.length() - 1);
                    }
                }
            }
        }
        return "";
    }

    public static int getWifiLevel(int rssi, int level_max) {
        if (rssi == Integer.MAX_VALUE) {
            return -1;
        }
        return WifiManager.calculateSignalLevel(rssi, level_max);
    }

    public static WifiConfiguration connectWifi(Context context, String ssid, String password, int accessPointSecurity) {
        BBLog.w(BBLog.TAG, "connectWifi");
        WifiManager mWifiManager = (WifiManager) context.getApplicationContext().getSystemService(WIFI_SERVICE);

        if (!mWifiManager.isWifiEnabled()) {
            mWifiManager.setWifiEnabled(true);
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }

        WifiConfiguration config = new WifiConfiguration();
        config.allowedAuthAlgorithms.clear();
        config.allowedGroupCiphers.clear();
        config.allowedKeyManagement.clear();
        config.allowedPairwiseCiphers.clear();
        config.allowedProtocols.clear();
        config.SSID = "\"" + ssid + "\"";
        config.hiddenSSID = true;

        //如果当前连接的wifi被保存了密码，清除wifi保存信息
        for (WifiConfiguration existingConfig : mWifiManager.getConfiguredNetworks()) {
            if (existingConfig.SSID.equals("\"" + ssid + "\"")) {
                mWifiManager.removeNetwork(existingConfig.networkId);
                mWifiManager.saveConfiguration();
            }
        }

        switch (accessPointSecurity) {
            case SECURITY_WEP:
                if (password.length() != 0) {
                    int length = password.length();
                    // get selected WEP key index
                    int keyIndex = 0; // selected password index, 0~3
//                    if (mWEPKeyIndex != null
//                            && mWEPKeyIndex.getSelectedItemPosition() != AdapterView.INVALID_POSITION) {
//                        keyIndex = mWEPKeyIndex.getSelectedItemPosition();
//                    }
                    // WEP-40, WEP-104, and 256-bit WEP (WEP-232?)
                    if ((length == 10 || length == 26 || length == 32)
                            && password.matches("[0-9A-Fa-f]*")) {
                        //hex password
                        config.wepKeys[keyIndex] = password;
                    } else {
                        //ASCII password
                        config.wepKeys[keyIndex] = '"' + password + '"';
                    }
                    // set wep index to configuration
                    config.wepTxKeyIndex = keyIndex;
                }
                config.hiddenSSID = true;
                config.allowedAuthAlgorithms.set(WifiConfiguration.AuthAlgorithm.SHARED);
                config.allowedGroupCiphers.set(WifiConfiguration.GroupCipher.CCMP);
                config.allowedGroupCiphers.set(WifiConfiguration.GroupCipher.TKIP);
                config.allowedGroupCiphers.set(WifiConfiguration.GroupCipher.WEP40);
                config.allowedGroupCiphers.set(WifiConfiguration.GroupCipher.WEP104);
                config.allowedKeyManagement.set(WifiConfiguration.KeyMgmt.NONE);
                break;
            case SECURITY_WPA_PSK:
            case SECURITY_WPA2_PSK:
                if (password.length() != 0) {
                    if (password.matches("[0-9A-Fa-f]{64}")) {
                        config.preSharedKey = password;
                    } else {
                        config.preSharedKey = '"' + password + '"';
                    }
                }
                config.hiddenSSID = true;
                config.allowedAuthAlgorithms.set(WifiConfiguration.AuthAlgorithm.OPEN);
                config.allowedGroupCiphers.set(WifiConfiguration.GroupCipher.TKIP);
                config.allowedKeyManagement.set(WifiConfiguration.KeyMgmt.WPA_PSK);
                config.allowedPairwiseCiphers.set(WifiConfiguration.PairwiseCipher.TKIP);
                // config.allowedProtocols.set(WifiConfiguration.Protocol.WPA);
                config.allowedGroupCiphers.set(WifiConfiguration.GroupCipher.CCMP);
                config.allowedPairwiseCiphers.set(WifiConfiguration.PairwiseCipher.CCMP);
                config.status = WifiConfiguration.Status.ENABLED;
                break;
            case SECURITY_EAP:
//                if ("AKA".equals((String) eapMethodSpinner.getSelectedItem())
//                        || "SIM".equals((String) eapMethodSpinner.getSelectedItem())) {
//                    eapSimAkaConfig(config, eapMethodSpinner);
//                    BBLog.d(TAG, "eap-sim/aka, config.toString(): " + config.toString());
//                }
                break;
            // add WAPI_PSK & WAPI_CERT
            case SECURITY_WAPI_PSK:
//                config.allowedKeyManagement.set(WifiConfiguration.KeyMgmt.WAPI_PSK);
//                config.allowedProtocols.set(WifiConfiguration.Protocol.WAPI);
//                config.allowedPairwiseCiphers.set(WifiConfiguration.PairwiseCipher.SMS4);
//                config.allowedGroupCiphers.set(WifiConfiguration.GroupCipher.SMS4);
//                if (password.length() != 0) {
////                    BBLog.v(Constants.TAG, "getConfig(), mHex=" + mHex);
////                    if (mHex) { /* Hexadecimal */
////                        config.preSharedKey = password;
////                    } else { /* ASCII */
//                        config.preSharedKey = '"' + password + '"';
////                    }
//                }
                break;
            case SECURITY_WAPI_CERT:
//                config.allowedKeyManagement.set(WifiConfiguration.KeyMgmt.WAPI_CERT);
//                config.allowedProtocols.set(WifiConfiguration.Protocol.WAPI);
//                config.allowedPairwiseCiphers.set(WifiConfiguration.PairwiseCipher.SMS4);
//                config.allowedGroupCiphers.set(WifiConfiguration.GroupCipher.SMS4);
//                config.enterpriseConfig.setCaCertificateWapiAlias((mWapiAsCert.getSelectedItemPosition() == 0) ? ""
//                        : (String) mWapiAsCert.getSelectedItem());
//                config.enterpriseConfig.setClientCertificateWapiAlias((mWapiClientCert.getSelectedItemPosition() == 0) ? ""
//                        : (String) mWapiClientCert.getSelectedItem());
                break;
            default:
                config.allowedKeyManagement.set(WifiConfiguration.KeyMgmt.NONE);
                break;
        }
        int netId = mWifiManager.addNetwork(config);
        mWifiManager.enableNetwork(netId, true);
        return config;
    }

    /**
     * 获取手机信号强度，需添加权限 android.permission.ACCESS_COARSE_LOCATION <br>
     * API要求不低于17 <br>
     *
     * @return 当前手机主卡信号强度,单位 dBm（-1是默认值，表示获取失败）
     */
    public static int getMobileDbm(Context context)
    {
        int dbm = -1;
        TelephonyManager tm = (TelephonyManager)context.getSystemService(Context.TELEPHONY_SERVICE);
        List<CellInfo> cellInfoList;
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR1)
        {
            cellInfoList = tm.getAllCellInfo();
            if (null != cellInfoList)
            {
                for (CellInfo cellInfo : cellInfoList)
                {
                    if (cellInfo instanceof CellInfoGsm)
                    {
                        CellSignalStrengthGsm cellSignalStrengthGsm = ((CellInfoGsm)cellInfo).getCellSignalStrength();
                        dbm = cellSignalStrengthGsm.getDbm();
                    }
                    else if (cellInfo instanceof CellInfoCdma)
                    {
                        CellSignalStrengthCdma cellSignalStrengthCdma =
                                ((CellInfoCdma)cellInfo).getCellSignalStrength();
                        dbm = cellSignalStrengthCdma.getDbm();
                    }
                    else if (cellInfo instanceof CellInfoWcdma)
                    {
                        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR2)
                        {
                            CellSignalStrengthWcdma cellSignalStrengthWcdma =
                                    ((CellInfoWcdma)cellInfo).getCellSignalStrength();
                            dbm = cellSignalStrengthWcdma.getDbm();
                        }
                    }
                    else if (cellInfo instanceof CellInfoLte)
                    {
                        CellSignalStrengthLte cellSignalStrengthLte = ((CellInfoLte)cellInfo).getCellSignalStrength();
                        dbm = cellSignalStrengthLte.getDbm();
                    }
                }
            }
        }
        return dbm;
    }

    public static int getMobileNetworkState(Context context) {
        // 若不是WIFI，则去判断是2G、3G、4G网
        TelephonyManager telephonyManager = (TelephonyManager) context.getSystemService(Context.TELEPHONY_SERVICE);
        int networkType = telephonyManager.getNetworkType();
        switch (networkType) {
    /*
       GPRS : 2G(2.5) General Packet Radia Service 114kbps
       EDGE : 2G(2.75G) Enhanced Data Rate for GSM Evolution 384kbps
       UMTS : 3G WCDMA 联通3G Universal Mobile Telecommunication System 完整的3G移动通信技术标准
       CDMA : 2G 电信 Code Division Multiple Access 码分多址
       EVDO_0 : 3G (EVDO 全程 CDMA2000 1xEV-DO) Evolution - Data Only (Data Optimized) 153.6kps - 2.4mbps 属于3G
       EVDO_A : 3G 1.8mbps - 3.1mbps 属于3G过渡，3.5G
       1xRTT : 2G CDMA2000 1xRTT (RTT - 无线电传输技术) 144kbps 2G的过渡,
       HSDPA : 3.5G 高速下行分组接入 3.5G WCDMA High Speed Downlink Packet Access 14.4mbps
       HSUPA : 3.5G High Speed Uplink Packet Access 高速上行链路分组接入 1.4 - 5.8 mbps
       HSPA : 3G (分HSDPA,HSUPA) High Speed Packet Access
       IDEN : 2G Integrated Dispatch Enhanced Networks 集成数字增强型网络 （属于2G，来自维基百科）
       EVDO_B : 3G EV-DO Rev.B 14.7Mbps 下行 3.5G
       LTE : 4G Long Term Evolution FDD-LTE 和 TDD-LTE , 3G过渡，升级版 LTE Advanced 才是4G
       EHRPD : 3G CDMA2000向LTE 4G的中间产物 Evolved High Rate Packet Data HRPD的升级
       HSPAP : 3G HSPAP 比 HSDPA 快些
       */
            // 2G网络
            case TelephonyManager.NETWORK_TYPE_GPRS:
            case TelephonyManager.NETWORK_TYPE_CDMA:
            case TelephonyManager.NETWORK_TYPE_EDGE:
            case TelephonyManager.NETWORK_TYPE_1xRTT:
            case TelephonyManager.NETWORK_TYPE_IDEN:
                return NETWORK_2G;
            // 3G网络
            case TelephonyManager.NETWORK_TYPE_EVDO_A:
            case TelephonyManager.NETWORK_TYPE_UMTS:
            case TelephonyManager.NETWORK_TYPE_EVDO_0:
            case TelephonyManager.NETWORK_TYPE_HSDPA:
            case TelephonyManager.NETWORK_TYPE_HSUPA:
            case TelephonyManager.NETWORK_TYPE_HSPA:
            case TelephonyManager.NETWORK_TYPE_EVDO_B:
            case TelephonyManager.NETWORK_TYPE_EHRPD:
            case TelephonyManager.NETWORK_TYPE_HSPAP:
                return NETWORK_3G;
            // 4G网络
            case TelephonyManager.NETWORK_TYPE_LTE:
                return NETWORK_4G;
            default:
                return NETWORK_UNKNOW;
        }
    }

    // MAC
    /**
     * Android 6.0 之前（不包括6.0）获取mac地址
     * 必须的权限 <uses-permission android:name="android.permission.ACCESS_WIFI_STATE"></uses-permission>
     * @param context * @return
     */
    public static String getMacDefault(Context context) {
        String mac = "";
        if (context == null) {
            return mac;
        }
        WifiManager wifi = (WifiManager)context.getSystemService(Context.WIFI_SERVICE);
        WifiInfo info = null;
        try {
            info = wifi.getConnectionInfo();
        } catch (Exception e) {
            e.printStackTrace();
        }

        if (info == null) {
            return null;
        }
        mac = info.getMacAddress();
        if (!TextUtils.isEmpty(mac)) {
            mac = mac.toUpperCase(Locale.ENGLISH);
        }
        return mac;
    }

    /**
     * Android 6.0-Android 7.0 获取mac地址
     */
    public static String getMacAddress() {
        String macSerial = null;
        String str = "";

        try {
//            Process pp = Runtime.getRuntime().exec("cat/sys/class/net/wlan0/address");
            Process pp = SecurityOperate.getInstance().execCommand(ContextUtil.getInstance(),"cat/sys/class/net/wlan0/address");
            InputStreamReader ir = new InputStreamReader(pp.getInputStream());
            LineNumberReader input = new LineNumberReader(ir);

            while (null != str) {
                str = input.readLine();
                if (str != null) {
                    macSerial = str.trim();//去空格
                    break;
                }
            }
        } catch (IOException ex) {
            // 赋予默认值
            ex.printStackTrace();
        }

        return macSerial;
    }

    /**
     * Android 7.0之后获取Mac地址
     * 遍历循环所有的网络接口，找到接口是 wlan0
     * 必须的权限 <uses-permission android:name="android.permission.INTERNET"></uses-permission>
     * @return
     */
    public static String getMacFromHardware() {
        try {
            List<NetworkInterface> interfaces = Collections.list(NetworkInterface.getNetworkInterfaces());
            for (NetworkInterface intf : interfaces) {
                if (!intf.getName().equalsIgnoreCase("wlan0")) {
                    continue;
                }
                byte[] mac = intf.getHardwareAddress();
                if (mac==null) return "";
                StringBuilder buf = new StringBuilder();
                for (int idx=0; idx<mac.length; idx++)
                    buf.append(String.format("%02X:", mac[idx]));
                if (buf.length()>0) buf.deleteCharAt(buf.length()-1);
                return buf.toString();
            }
        } catch (Exception ex) {

        }

        return "";
    }

    /**
     * 获取mac地址（适配所有Android版本）
     * @return
     */
    public static String getMac(Context context) {
        String mac = "";
        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.M) {
            mac = getMacDefault(context);
        } else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M && Build.VERSION.SDK_INT < Build.VERSION_CODES.N) {
            mac = getMacAddress();
        } else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            mac = getMacFromHardware();
        }
        return mac;
    }

    /**
     * 获取手机IMEI
     *
     * @param context
     * @return
     */
    public static final String getIMEI(Context context) {
        try {
            //实例化TelephonyManager对象
            TelephonyManager telephonyManager = (TelephonyManager) context.getSystemService(Context.TELEPHONY_SERVICE);
            //获取IMEI号
            Method method = telephonyManager.getClass().getMethod("getDeviceId", int.class);
            //获取IMEI号
            String imei = (String) method.invoke(telephonyManager, 1);
            if (imei == null) {
                imei = "";
            }
            return imei;
        } catch (Exception e) {
            e.printStackTrace();
            return "";
        }
    }

    /**
     * 获取手机IMSI
     */
    public static String getIMSI(Context context){
        try {
            TelephonyManager telephonyManager=(TelephonyManager)context.getSystemService(Context.TELEPHONY_SERVICE);
            //获取IMSI号
            String imsi=telephonyManager.getSubscriberId();
            if(null==imsi){
                imsi="";
            }
            return imsi;
        } catch (Exception e) {
            e.printStackTrace();
            return "";
        }
    }

    /**
     * 获取SIM卡iccid
     */
    public static String getICCID(Context context){
        try {
            TelephonyManager telephonyManager=(TelephonyManager)context.getSystemService(Context.TELEPHONY_SERVICE);
            //获取IMSI号
            String iccid=telephonyManager.getSimSerialNumber();
            if(null==iccid){
                iccid="";
            }
            return iccid;
        } catch (Exception e) {
            e.printStackTrace();
            return "";
        }
    }

    /**
     * 在子线程里开启该方法，可检测当前网络是否能打开网页
     * true是可以上网，false是不能上网
     *
     */
    public static boolean isOnline(){
        URL url;
        String str_url = null;
        try {
            str_url = "https://aws.amazon.com";
            url = new URL(str_url);
            InputStream stream = url.openStream();
            BBLog.w(BBLog.TAG, "可以连接到网络 " + str_url);
            return true;
        } catch (MalformedURLException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        }
        BBLog.w(BBLog.TAG, "无法连接到网络 " + str_url);
        return false;
    }

    /*
     * 判断是否有外网连接（普通方法不能判断外网的网络是否连接，比如连接上局域网）
     */
    static boolean result = true;
    public static boolean ping() {
        BBLog.e(BBLog.TAG, "ping test begin-----------" );
        /*
		HttpURLConnection urlConn = null;
        try {
            URL myUrl = new URL("https://config.wisemanager.com");
            //判断是否存在代理
            String proxyAddress = System.getProperty("http.proxyHost");
            int proxyPort = Integer.parseInt(( System.getProperty("http.proxyPort") != null ? System.getProperty("http.proxyPort") : "-1"));
            if ( (!TextUtils.isEmpty(proxyAddress)) && (proxyPort != -1)){
                InetSocketAddress addr = new InetSocketAddress(proxyAddress,proxyPort);
                Proxy proxy = new Proxy(Proxy.Type.HTTP, addr);
                urlConn = (HttpURLConnection)myUrl.openConnection(proxy);
                BBLog.e(BBLog.TAG, "ping test begin-----------设置http 代理" );
            }else {
                urlConn = (HttpURLConnection) myUrl.openConnection();
                BBLog.e(BBLog.TAG, "ping test begin-----------不设置http 代理" );
            }
            urlConn.setConnectTimeout(15*1000);
            urlConn.setReadTimeout(15*1000);
            urlConn.setInstanceFollowRedirects(true);
            urlConn.connect();
            int responseCode = urlConn.getResponseCode();
            BBLog.v(BBLog.TAG, "response: "+ responseCode);
            if(responseCode>199 && responseCode<400)
                result = true;
        } catch (Exception e) {
            e.printStackTrace();
            result = false;
        }finally {
			try {
				if (urlConn!=null){
					urlConn.disconnect();
					urlConn = null;
				}
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
         */
        BBLog.e(BBLog.TAG, "ping test end -----------   result = "+result );
        return result;
    }

    public static final boolean ping_ip(String ip) {
        String result = null;
        long startTime = System.currentTimeMillis();
        try {
//            Process p = Runtime.getRuntime().exec("ping -c 4 -w 10 " + ip);// ping网址3次
            Process p = SecurityOperate.getInstance().execCommand(ContextUtil.getInstance(),"ping -c 4 -w 10 " + ip);// ping网址3次
            // 读取ping的内容，可以不加
            InputStream input = p.getInputStream();
            BufferedReader in = new BufferedReader(new InputStreamReader(input));
            StringBuffer stringBuffer = new StringBuffer();
            String content = "";
            while ((content = in.readLine()) != null) {
                stringBuffer.append(content);
            }
            WifiInfo wifiInfo = WiFiProfileManager.getInstance(ContextUtil.getInstance()).getConnectionInfo();
            BBLog.e(BBLog.TAG,"wifi ["+ ((wifiInfo == null)? "null" : wifiInfo.getSSID())+"] ------ping----- result content : " + stringBuffer.toString());
            // ping的状态
            int status = p.waitFor();
            if (status == 0) {
                result = "success";
                return true;
            } else {
                result = "failed";
            }
        } catch (Exception e) {
            result = "Exception";
        } finally {
            BBLog.e(BBLog.TAG,"----result--- result = " + result + ",ping 本地ping 耗时 ："+(System.currentTimeMillis()-startTime));
        }
        return false;
    }

    public static boolean checkURL(String url){
        boolean value=false;
        try {
            HttpURLConnection conn=(HttpURLConnection)new URL(url).openConnection();
            int code=conn.getResponseCode();
            BBLog.e(BBLog.TAG,"----checkURL ResponseCode = " + code);
            if(code!=200){
                value=false;
            }else{
                value=true;
            }
        } catch (MalformedURLException e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        } catch (IOException e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }
        return value;
    }

    //获取蓝牙地址
    public static String getBluetoothMacAddress() {
        BluetoothAdapter bluetoothAdapter = BluetoothAdapter.getDefaultAdapter();
        String bluetoothMacAddress = "";
        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.M){
            try {
                Field mServiceField = bluetoothAdapter.getClass().getDeclaredField("mService");
                mServiceField.setAccessible(true);

                Object btManagerService = mServiceField.get(bluetoothAdapter);
                if (btManagerService != null) {
                    bluetoothMacAddress = (String) btManagerService.getClass().getMethod("getAddress").invoke(btManagerService);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        } else {
            bluetoothMacAddress = bluetoothAdapter.getAddress();
        }

        return bluetoothMacAddress;
    }

    public static String getWifiIP(Context context) {
        WifiManager wifiManager =(WifiManager)context.getSystemService(Context.WIFI_SERVICE);
        //判断wifi是否开启
        if (!wifiManager.isWifiEnabled()) {
            wifiManager.setWifiEnabled(true);
        }
        WifiInfo wifiInfo = wifiManager.getConnectionInfo();
        if (wifiInfo == null) {
            return null;
        }

        return int2ip(wifiInfo.getIpAddress());
    }

    /**
     * 将ip的整数形式转换成ip形式
     *
     * @param ipInt
     * @return
     */
    public static String int2ip(int ipInt) {
        StringBuilder sb = new StringBuilder();
        sb.append(ipInt & 0xFF).append(".");
        sb.append((ipInt >> 8) & 0xFF).append(".");
        sb.append((ipInt >> 16) & 0xFF).append(".");
        sb.append((ipInt >> 24) & 0xFF);
        return sb.toString();
    }

    public static String int2Gateway(int ipInt) {
        StringBuilder sb = new StringBuilder();
        sb.append(ipInt & 0xFF).append(".");
        sb.append((ipInt >> 8) & 0xFF).append(".");
        sb.append((ipInt >> 16) & 0xFF).append(".");
        sb.append("1");
        return sb.toString();
    }

    public static String getRouterMac(String gateway) {
        if (TextUtils.isEmpty(gateway)) {
            return "";
        }

        List arpContent = readFile("/proc/net/arp");
        if (arpContent != null && arpContent.size() > 1) {
            for (int i = 1; i < arpContent.size(); i++) {
                List arrayList = new ArrayList();
                String[] split = ((String) arpContent.get(i)).split(" ");
                int j = 0;
                while (j < split.length) {
                    if (split[j] != null && split[j].length() > 0) {
                        arrayList.add(split[j]);
                    }
                    j++;
                }
                if (arrayList.size() > 4 && ((String) arrayList.get(0)).equalsIgnoreCase(gateway)) {
                    return ((String) arrayList.get(3)).toLowerCase();
                }
            }
        }

        return "";
    }

    private static List<String> readFile(String filePath) {
        File file = new File(filePath);
        if (!file.exists()) {
            return null;
        }

        List<String> arrayList = new ArrayList();
        BufferedReader bufferedReader = null;
        try {
            bufferedReader = new BufferedReader(new FileReader(file));
            while (true) {
                String readLine = bufferedReader.readLine();
                if (readLine == null) {
                    break;
                }
                arrayList.add(readLine);
            }
            bufferedReader.close();
            if (bufferedReader != null) {
                bufferedReader.close();
            }
        } catch (IOException e) {
                e.printStackTrace();
                if (bufferedReader != null) {
                    try {
                        bufferedReader.close();
                    } catch (IOException e2) {

                    }
                }
        }

        return arrayList;
    }

    /**
     * 打开热点
     *
     * @param context      上下文
     * @param SSID         名称
     * @param password     密码
     * @param securityType 加密类型
     * @param band         WIFI_FREQUENCY_BAND_5GHZ  WIFI_FREQUENCY_BAND_2GHZ
     * @return 开启状态
     */
    public static final int WIFI_AP_2_4G_CHANNEL = 9;//2.4G热点信道
    public static final int WIFI_AP_5G_CHANNEL = 149;//5G热点信道

    public static boolean getApEnable(Context context) {
        WifiManager wifiManager = (WifiManager) context.getApplicationContext().getSystemService(Context.WIFI_SERVICE);
        if (wifiManager == null) {
            return false;
        }
        try {
            Method method = wifiManager.getClass().getDeclaredMethod("isWifiApEnabled");
            method.setAccessible(true);
            return (Boolean) method.invoke(wifiManager);
        } catch (NoSuchMethodException | InvocationTargetException | IllegalAccessException e) {
            e.printStackTrace();
        }
        return false;
    }

    public static void setApEnable(Context context, boolean enable) {
        try {
            WifiManager wifiManager = (WifiManager) context.getApplicationContext().getSystemService(Context.WIFI_SERVICE);
            if (wifiManager == null) {
                return;
            }
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                // 如果是Android 8.0系统
                if (enable) {
                    // 打开热点
                    wifiManager.startLocalOnlyHotspot(new WifiManager.LocalOnlyHotspotCallback() {

                        @Override
                        public void onStarted(WifiManager.LocalOnlyHotspotReservation reservation) {
                            super.onStarted(reservation);
                            BBLog.d(BBLog.TAG, "onStarted: ");
                        }

                        @Override
                        public void onStopped() {
                            super.onStopped();
                            BBLog.d(BBLog.TAG, "onStopped: ");
                        }

                        @Override
                        public void onFailed(int reason) {
                            super.onFailed(reason);
                            BBLog.d(BBLog.TAG, "onFailed: ");
                        }
                    }, new Handler());
                } else {
                    // 关闭热点
                    Method method = wifiManager.getClass().getDeclaredMethod("stopSoftAp");
                    method.invoke(wifiManager);
                }
            } else {
                Method method = wifiManager.getClass().getMethod("setWifiApEnabled", WifiConfiguration.class, boolean.class);
                method.invoke(wifiManager, null, enable);
            }
        } catch (NoSuchMethodException | IllegalAccessException | InvocationTargetException e) {
            e.printStackTrace();
        }

    }

    public static boolean configAp(Context context, String ssid, String password, int band) {
        if (TextUtils.isEmpty(ssid)) {
            return false;
        }

        WifiManager wifiManager = (WifiManager) context.getSystemService(Context.WIFI_SERVICE);
        if (wifiManager != null && wifiManager.isWifiEnabled()) {
            wifiManager.setWifiEnabled(false);
        }

        WifiConfiguration wifiCfg = new WifiConfiguration();
        wifiCfg.SSID = ssid;
        wifiCfg.preSharedKey = password;
        wifiCfg.hiddenSSID = false;
        wifiCfg.allowedAuthAlgorithms.set(WifiConfiguration.AuthAlgorithm.OPEN);//开放系统认证
        wifiCfg.allowedGroupCiphers.set(WifiConfiguration.GroupCipher.TKIP);
        wifiCfg.allowedKeyManagement.set(4);
        wifiCfg.allowedPairwiseCiphers.set(WifiConfiguration.PairwiseCipher.TKIP);
        wifiCfg.allowedGroupCiphers.set(WifiConfiguration.GroupCipher.CCMP);
        wifiCfg.allowedPairwiseCiphers.set(WifiConfiguration.PairwiseCipher.CCMP);
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            wifiCfg.allowedProtocols.set(WifiConfiguration.Protocol.RSN);
        }
        wifiCfg.status = WifiConfiguration.Status.ENABLED;

        if (getApEnable(context)) {
            setApEnable(context, false);
        }

        Method method = null;
        Field field = null;
        try {
            method = wifiManager.getClass().getMethod("isDualBandSupported");
            //判断是否支持5G
            boolean isDualBandSupported = (boolean) method.invoke(wifiManager);
            BBLog.i(BBLog.TAG, "=====isDualBandSupported = " + isDualBandSupported + "====");
            if (isDualBandSupported && band == 1) {
                field = wifiCfg.getClass().getField("apBand");
                field.setInt(wifiCfg, 1);
                field = wifiCfg.getClass().getField("apChannel");
                field.setInt(wifiCfg, WIFI_AP_5G_CHANNEL);
            } else {
                field = wifiCfg.getClass().getField("apBand");
                field.setInt(wifiCfg, 0);
                field = wifiCfg.getClass().getField("apChannel");
                field.setInt(wifiCfg, WIFI_AP_2_4G_CHANNEL);
            }
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                BBLog.i(BBLog.TAG, "高于Android 8，启动热点");
                Method configMethod = wifiManager.getClass().getMethod("setWifiApConfiguration", WifiConfiguration.class);
                boolean isConfigured = (Boolean) configMethod.invoke(wifiManager, wifiCfg);
                method = wifiManager.getClass().getMethod("startSoftAp", WifiConfiguration.class);
                //返回热点打开状态
                return (Boolean) method.invoke(wifiManager, wifiCfg);
            } else {
                BBLog.i(BBLog.TAG, "低于Android 8，启动热点");
                method = wifiManager.getClass().getMethod("setWifiApEnabled", WifiConfiguration.class, boolean.class);
                method.invoke(wifiManager, wifiCfg, true);
                return true;
            }
        } catch (NoSuchMethodException | NoSuchFieldException | InvocationTargetException | IllegalAccessException e) {
            e.printStackTrace();
            BBLog.i(BBLog.TAG, "=====Exception==" + e.getMessage());
        }
        return false;
    }
}
