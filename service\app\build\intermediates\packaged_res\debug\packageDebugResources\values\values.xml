<?xml version="1.0" encoding="utf-8"?>
<resources>
    <color name="black">#FF000000</color>
    <color name="gray">#FF808080</color>
    <color name="purple_200">#FFBB86FC</color>
    <color name="purple_500">#FF6200EE</color>
    <color name="purple_700">#FF3700B3</color>
    <color name="red">#FFFF0000</color>
    <color name="slate_grey">#FF708090</color>
    <color name="teal_200">#FF03DAC5</color>
    <color name="teal_700">#FF018786</color>
    <color name="white">#FFFFFFFF</color>
    <dimen name="dp_15">15dp</dimen>
    <dimen name="dp_20">20dp</dimen>
    <dimen name="dp_40">40dp</dimen>
    <dimen name="dp_5">5dp</dimen>
    <string name="accessibility_service_description">用于监控设备使用状态，检测用户交互事件以判断设备是否在使用中</string>
    <string name="app_name">service</string>
    <style name="GeofenceDialogAnimation">
        <item name="android:windowEnterAnimation">@anim/dialog_enter</item>
        <item name="android:windowExitAnimation">@anim/dialog_exit</item>
    </style>
    <style name="GeofenceWarningDialogStyle" parent="android:Theme.Dialog">
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:backgroundDimEnabled">true</item>
        <item name="android:backgroundDimAmount">0.6</item>
        <item name="android:windowAnimationStyle">@style/GeofenceDialogAnimation</item>
    </style>
    <style name="Theme.MyTheme" parent="Theme.MaterialComponents.DayNight.DarkActionBar">
        
        <item name="colorPrimary">@color/purple_500</item>
        <item name="colorPrimaryVariant">@color/purple_700</item>
        <item name="colorOnPrimary">@color/white</item>
        
        <item name="colorSecondary">@color/teal_200</item>
        <item name="colorSecondaryVariant">@color/teal_700</item>
        <item name="colorOnSecondary">@color/black</item>
        
        <item name="android:statusBarColor">?attr/colorPrimaryVariant</item>
        
    </style>
    <style name="no_statusbar_activity">
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowFullscreen">true</item>
        <item name="android:windowNoTitle">true</item>
    </style>
    <declare-styleable name="PasswordEditText">
        
        <attr format="integer" name="passwordNumber"/>
        
        <attr format="dimension" name="passwordRadius"/>
        
        <attr format="color" name="passwordColor"/>
        
        <attr format="color" name="divisionLineColor"/>
        
        <attr format="dimension" name="divisionLineSize"/>
        
        <attr format="color" name="bgColor"/>
        
        <attr format="dimension" name="bgSize"/>
        
        <attr format="dimension" name="bgCorner"/>
        
        <attr format="boolean" name="showPassword"/>
    </declare-styleable>
</resources>