package com.bbpos.wiseapp.websocket.handler;

import android.app.AlertDialog;
import android.app.Dialog;
import android.app.ProgressDialog;
import android.bluetooth.BluetoothAdapter;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.content.SharedPreferences;
import android.content.res.AssetFileDescriptor;
import android.media.AudioManager;
import android.media.MediaPlayer;
import android.os.Build;
import android.os.Handler;
import android.os.Looper;
import android.preference.PreferenceManager;
import android.text.TextUtils;
import android.view.View;
import android.view.WindowManager;
import android.widget.TextView;

import com.bbpos.wiseapp.logger.BBLog;
import com.bbpos.wiseapp.logger.MyDiskLogStrategy;
import com.bbpos.wiseapp.logstream.LogService;
import com.bbpos.wiseapp.provisioning.ProvisionTimer;
import com.bbpos.wiseapp.service.R;
import com.bbpos.wiseapp.service.common.RequestPermission;
import com.bbpos.wiseapp.service.common.UsualData;
import com.bbpos.wiseapp.settings.activity.BluetoothEnabler;
import com.bbpos.wiseapp.settings.utils.WirelessUtil;
import com.bbpos.wiseapp.system.api.CustomServiceManager;
import com.bbpos.wiseapp.system.api.DeviceInfoApi;
import com.bbpos.wiseapp.system.api.SysIntermediateApi;
import com.bbpos.wiseapp.system.api.SystemApi;
import com.bbpos.wiseapp.tms.adapter.SystemManagerAdapter;
import com.bbpos.wiseapp.tms.location.GpsLocationManager;
import com.bbpos.wiseapp.tms.utils.AESUtil;
import com.bbpos.wiseapp.tms.utils.BroadcastActions;
import com.bbpos.wiseapp.tms.utils.Constants;
import com.bbpos.wiseapp.tms.utils.ContextUtil;
import com.bbpos.wiseapp.tms.utils.Helpers;
import com.bbpos.wiseapp.tms.utils.ParameterName;
import com.bbpos.wiseapp.tms.utils.RuleStatus;
import com.bbpos.wiseapp.tms.utils.SPKeys;
import com.bbpos.wiseapp.tms.utils.WebSocketServiceListManager;
import com.bbpos.wiseapp.tms.widget.TimerDialog;
import com.bbpos.wiseapp.utils.FileUtils;
import com.bbpos.wiseapp.utils.PaymentModeUtils;
import com.bbpos.wiseapp.utils.SharedPreferencesUtils;
import com.bbpos.wiseapp.websocket.DeviceBidUploadService;
import com.bbpos.wiseapp.websocket.WebSocketManager;
import com.bbpos.wiseapp.websocket.WebSocketSender;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;

public class CommandHandler {
    private Context mContext;
    private WebSocketManager webSocketManager;
    private Dialog mDialog;

    public static String unboxreset_request_id = "";
    public static String unboxreset_request_time = "";

    public CommandHandler(Context context, WebSocketManager manager) {
        mContext = context;
        webSocketManager = manager;
    }

    public void dispatch(String orderMsg) {
        String tranCode = "";
        JSONObject jsonObject = null;
        try {
            jsonObject = new JSONObject(orderMsg);
            if (jsonObject.has("tranCode")) {
                tranCode = jsonObject.getString("tranCode");
            }
            switch (tranCode) {
                case "SC001":
                    SystemManagerAdapter.reboot(mContext);
                    break;
                case "SC002":
                    if (jsonObject.has(ParameterName.data)) {
                        JSONObject dataObject = jsonObject.getJSONObject(ParameterName.data);
                        String msg = dataObject.getString(ParameterName.notice_msg);
                        new Handler(Looper.getMainLooper()).post(new Runnable() {
                            @Override
                            public void run() {
                                AlertDialog dialog;
                                AlertDialog.Builder builder = new AlertDialog.Builder(mContext, ProgressDialog.THEME_HOLO_LIGHT);
                                builder.setTitle("prompt");
                                builder.setCancelable(false);
                                builder.setMessage(msg);
                                builder.setPositiveButton("confirm", new DialogInterface.OnClickListener() {
                                    public void onClick(DialogInterface dialog, int which) {
                                        dialog.dismiss();
                                    }
                                });
                                dialog = builder.create();
                                dialog.getWindow().setType(WindowManager.LayoutParams.TYPE_SYSTEM_ALERT);//这句话是关键
                                dialog.show();
                            }
                        });
                    }
                    break;
                case "SC003":
                    if (jsonObject.has(ParameterName.data)) {
                        JSONObject dataObject = jsonObject.getJSONObject(ParameterName.data);
                        if (jsonObject.has(ParameterName.wifi_info)) {
                            JSONObject wifi_info = dataObject.getJSONObject("wifi_info");
                            WirelessUtil.connectWifi(mContext, wifi_info.getString(ParameterName.SSID), wifi_info.getString(ParameterName.wifi_pwd), WirelessUtil.SECURITY_WPA2_PSK);
                        }
                    }
                    break;
                case "SC004":
                    if (jsonObject.has(ParameterName.data)) {
                        JSONObject dataObject = jsonObject.getJSONObject(ParameterName.data);
                        if (dataObject.has(ParameterName.c_type)) {
                            String type = dataObject.getString(ParameterName.c_type);
                            if ("RESETSTORE".equals(type)) {
                                unboxreset_request_id = jsonObject.getString(ParameterName.request_id);
                                unboxreset_request_time = jsonObject.getString(ParameterName.request_time);
                                new Handler(Looper.getMainLooper()).post(new Runnable() {
                                    @Override
                                    public void run() {
                                        TimerDialog timerDialog = new TimerDialog(mContext, 30);
                                        timerDialog.setButton(mContext.getString(R.string.reset), mContext.getString(R.string.cancel), new TimerDialog.OnDialogClick() {
                                            @Override
                                            public void onClickPositive() {
                                                Constants.B_UNBOX_RESET_FROM_GEO = false;
                                                Intent intent_1 = new Intent(BroadcastActions.UNBIND_LAUNCHER);
                                                BBLog.w(BBLog.TAG, "sendBroadcast: UNBIND_LAUNCHER");
                                                mContext.sendBroadcast(intent_1, RequestPermission.REQUEST_PERMISSION_BBPOS);
                                            }

                                            @Override
                                            public void onClickNegative() {

                                            }
                                        });
                                        timerDialog.showDialog();
                                    }
                                });
                            } else if ("CALLC0109".equals(type)) {
                                if (!"0".equals(Constants.UPLOAD_MODE)) {
                                    WebSocketSender.C0901_AppInfoUpload();
                                    WebSocketSender.C0902_BatteryStatusUpload(Constants.BAT_LEVEL, Constants.BAT_HEALTH, Constants.BAT_TEMP, Constants.IS_BATTERY_CHARGING, Constants.IS_BATTERY_LOW, false, Constants.IS_OUT_OF_BATTERY);
                                }
                                Helpers.sendBroad(mContext, BroadcastActions.TER_INFO_UPLOAD_BC);
 							} else if ("CALLC0906".equals(type)) {
                            	//清除標誌位
                            	SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_DEVICE_BID_INFO, "");
                            	SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_DEVICE_FULL_INFO, "");
                            	BBLog.e(BBLog.TAG, "CALLC0906: 清除本地bid數據+DeviceInfo信息");
                            } else if ("CALLPROVISION".equals(type)) {
                                ProvisionTimer.startProvisionTimer(mContext, "0");
                            } else if ("CALLHB".equals(type)) {
                                WebSocketSender.heartBeat();
                            } else if ("CALL_SET_DF_LCH".equals(type)) {
                                if (dataObject.has(ParameterName.param)) {
                                    String package_name = dataObject.getString(ParameterName.param);
                                    if (!TextUtils.isEmpty(package_name)) {
                                        ContextUtil.setLauncherApp(mContext, package_name);
                                    }
                                }
                            } else if ("CALL_LOG_STREAM".equals(type)) {
                                if (dataObject.has(ParameterName.param)) {
                                    String service_app = dataObject.optString(ParameterName.param);
                                    try {
                                        if (LogService.isLogExecuting) {
                                            LogService.uploadRecentLog(false);
                                            LogService.startLogServiceUpload();
                                        } else {
                                            ContextUtil.startLogService(true);
                                            LogService.startLogServiceUpload();
                                        }
                                        if (!TextUtils.isEmpty(service_app)) {
                                            JSONObject serviceJson = new JSONObject(service_app);
                                            if ("05".equals(serviceJson.optString(ParameterName.taskType))
                                                    && "C03".equals(serviceJson.optString(ParameterName.command))) {
                                                if ("1970-01-01 00:00:00".equals(serviceJson.optString(ParameterName.beginDate))
                                                        && "9999-12-31 23:59:59".equals(serviceJson.optString(ParameterName.endDate))) {
                                                    BBLog.w(BBLog.TAG, "遇到LogStreaming的Schedule 起始結束時間不明確: " + serviceJson.toString());
                                                    long nowtime = System.currentTimeMillis();
                                                    serviceJson.put(ParameterName.request_id, "");
                                                    serviceJson.put(ParameterName.request_time, "");
                                                    serviceJson.put(ParameterName.beginDate, Helpers.getTransDateStr(nowtime));
                                                    if (serviceJson.has(ParameterName.period)) {
                                                        try {
                                                            int hour = Integer.parseInt(serviceJson.optString(ParameterName.period));
                                                            serviceJson.put(ParameterName.endDate, Helpers.getTransDateStr(nowtime + hour * 60 * 60 * 1000));
                                                        } catch (Exception e) {
                                                            e.printStackTrace();
                                                            serviceJson.put(ParameterName.endDate, Helpers.getTransDateStr(nowtime + 3 * 24 * 60 * 60 * 1000));
                                                        }
                                                    } else {
                                                        serviceJson.put(ParameterName.endDate, Helpers.getTransDateStr(nowtime + 3 * 24 * 60 * 60 * 1000));
                                                    }
                                                    serviceJson.put(ParameterName.stateDesc, RuleStatus.IMPLEMENTED);
                                                } else if ("9999-12-31 23:59:59".equals(serviceJson.optString(ParameterName.endDate))) {
                                                    BBLog.w(BBLog.TAG, "遇到LogStreaming的Schedule 結束時間不明確: " + serviceJson.toString());
                                                    long nowtime = System.currentTimeMillis();
                                                    serviceJson.put(ParameterName.request_id, "");
                                                    serviceJson.put(ParameterName.request_time, "");
                                                    if (serviceJson.has(ParameterName.period)) {
                                                        try {
                                                            int hour = Integer.parseInt(serviceJson.optString(ParameterName.period));
                                                            serviceJson.put(ParameterName.endDate, Helpers.getTransDateStr(nowtime + hour * 60 * 60 * 1000));
                                                        } catch (Exception e) {
                                                            e.printStackTrace();
                                                            serviceJson.put(ParameterName.endDate, Helpers.getTransDateStr(new SimpleDateFormat(Constants.dateFormat).parse(serviceJson.optString(ParameterName.beginDate)).getTime() + 3 * 24 * 60 * 60 * 1000));
                                                        }
                                                    } else {
                                                        serviceJson.put(ParameterName.endDate, Helpers.getTransDateStr(new SimpleDateFormat(Constants.dateFormat).parse(serviceJson.optString(ParameterName.beginDate)).getTime() + 3 * 24 * 60 * 60 * 1000));
                                                    }
                                                    serviceJson.put(ParameterName.stateDesc, RuleStatus.IMPLEMENTED);
                                                }

                                                BBLog.w(BBLog.TAG, "修改起始結束時間: " + serviceJson);
                                                String localServiceAppListStr = PreferenceManager.getDefaultSharedPreferences(mContext).getString(SPKeys.WEBSOCKET_SERVICE_LIST, "");
                                                if (TextUtils.isEmpty(localServiceAppListStr)) {
                                                    JSONArray serviceList = new JSONArray();
                                                    serviceList.put(serviceJson);
                                                    PreferenceManager.getDefaultSharedPreferences(mContext).edit().putString(SPKeys.WEBSOCKET_SERVICE_LIST, serviceList.toString()).commit();
                                                } else {
                                                    JSONArray serviceList = new JSONArray(localServiceAppListStr);
                                                    serviceList.put(serviceJson);
                                                    PreferenceManager.getDefaultSharedPreferences(mContext).edit().putString(SPKeys.WEBSOCKET_SERVICE_LIST, serviceList.toString()).commit();
                                                }
                                                SharedPreferencesUtils.setSharePreferencesValue(UsualData.LOG_STREAM_SERVICE_APP_EXECUTING, serviceJson.toString());
                                                WebSocketSender.C0901_AppInfoUpload();
                                            }
                                        }
                                    } catch (JSONException e) {
                                        e.printStackTrace();
                                    }
                                } else {
                                    if (LogService.isLogExecuting) {
                                        LogService.uploadRecentLog(false);
                                        LogService.startLogServiceUpload();
                                    } else {
                                        ContextUtil.startLogService(true);
                                        LogService.startLogServiceUpload();
                                    }
                                }
                            } else if ("CLOSE_LOG_STREAM".equals(type)) {
                                if (dataObject.has(ParameterName.param)) {
                                    String service_app = dataObject.optString(ParameterName.param);
                                    try {
//                                        ContextUtil.stopLogService();
//                                        LogService.stopLogServiceUpload();
                                        JSONObject serviceJson = new JSONObject(service_app);
                                        if ("06".equals(serviceJson.getString(ParameterName.taskType))) {
                                            BBLog.e(BBLog.TAG, "CLOSE_LOG_STREAM 此 Service App 执行卸载动作");
                                            RulebasedHandler.executeServiceReset(serviceJson.getString(ParameterName.command));
                                            String toDeleteServiceID = serviceJson.getString(ParameterName.serviceId);
                                            String localServiceAppListStr = PreferenceManager.getDefaultSharedPreferences(mContext).getString(SPKeys.WEBSOCKET_SERVICE_LIST, "");
                                            if (!TextUtils.isEmpty(localServiceAppListStr)) {
                                                List<Integer> toDeleteIndexs = new ArrayList<Integer>();
                                                JSONArray localServiceList = new JSONArray(localServiceAppListStr);
                                                for (int i = 0; i < localServiceList.length(); i++) {
                                                    JSONObject localServiceJsonObj = (JSONObject) localServiceList.get(i);
                                                    if ("05".equals(localServiceJsonObj.getString(ParameterName.taskType))
                                                            && RuleStatus.IMPLEMENTED.equals(localServiceJsonObj.getString(ParameterName.stateDesc))
                                                            && toDeleteServiceID.equals(localServiceJsonObj.getString(ParameterName.serviceId))) {
                                                        toDeleteIndexs.add(i);
                                                        if ("C03".equals(localServiceJsonObj.getString(ParameterName.command))) {
                                                            WebSocketServiceListManager.removeExecutingLogStreangServiceAppByServiceId(toDeleteServiceID);
                                                        }
                                                        break;
                                                    }
                                                }
                                                //删除标志为delete的任务
                                                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
                                                    for (int i = toDeleteIndexs.size() - 1; i >= 0; i--) {
                                                        localServiceList.remove(toDeleteIndexs.get(i));
                                                    }
                                                } else {
                                                    for (int i = toDeleteIndexs.size() - 1; i >= 0; i--) {
                                                        localServiceList = Helpers.remove(localServiceList, toDeleteIndexs.get(i));
                                                    }
                                                }
                                                PreferenceManager.getDefaultSharedPreferences(mContext).edit().putString(SPKeys.WEBSOCKET_SERVICE_LIST, localServiceList.toString()).commit();
                                            }
                                        }
                                        WebSocketSender.C0901_AppInfoUpload();
                                    } catch (Exception e) {
                                        e.printStackTrace();
                                    }
                                } else {
//                                    ContextUtil.stopLogService();
                                    LogService.stopLogServiceUpload();
                                }
                            } else if ("CALL_REMOTE_CONTROL".equals(type)) {
                                String bucketPath = dataObject.getString(ParameterName.param);
                                ContextUtil.startViewService(bucketPath);
                            } else if ("CLOSE_REMOTE_CONTROL".equals(type)) {
                                ContextUtil.stopViewService();
                            } else if ("CALL_AE".equals(type)) {
                                if (dataObject.has(ParameterName.param)) {
                                    String enable = dataObject.getString(ParameterName.param);
                                    if ("1".equals(enable)) {
                                        SysIntermediateApi.getIntance().setProp("persist.security.bbpos_debug", "1"); //Enable adb
                                    } else {
                                        SysIntermediateApi.getIntance().setProp("persist.security.bbpos_debug", "0"); //Disanable adb
                                    }
                                }
                            } else if ("CALL_RST".equals(type)) {
                                SystemApi.factoryReset(mContext);
                            } else if ("FIND_YOUR_POS".equals(type)) {
                                MediaPlayer mediaPlayer = null;
                                try {
                                    mediaPlayer = new MediaPlayer();
                                    mediaPlayer.setAudioStreamType(AudioManager.STREAM_MUSIC);
                                    mediaPlayer.setLooping(true);
                                    AssetFileDescriptor file = mContext.getResources().openRawResourceFd(R.raw.beep);
                                    mediaPlayer.setDataSource(file.getFileDescriptor(), file.getStartOffset(), file.getLength());
                                    file.close();
                                    mediaPlayer.prepare();
                                    mediaPlayer.start();
                                } catch (Exception e) {
                                    e.printStackTrace();
                                }
                                MediaPlayer finalMediaPlayer = mediaPlayer;
                                new Handler(Looper.getMainLooper()).post(new Runnable() {
                                    @Override
                                    public void run() {
                                        if (mDialog != null) {
                                            mDialog.dismiss();
                                            mDialog = null;
                                        }
                                        mDialog = new Dialog(mContext, R.style.dialog_style_ex);
                                        mDialog.setContentView(R.layout.dialog_confirm);
                                        TextView tv_title = (TextView) mDialog.findViewById(R.id.tv_title);
                                        tv_title.setText(mContext.getString(R.string.find_my_device));
                                        TextView tv_content = (TextView) mDialog.findViewById(R.id.tv_content);
                                        tv_content.setText(mContext.getString(R.string.device_alert));
                                        mDialog.setCanceledOnTouchOutside(false);
                                        TextView tv_cancel = (TextView) mDialog.findViewById(R.id.tv_cancel);
                                        tv_cancel.setVisibility(View.GONE);
                                        TextView tv_install = (TextView) mDialog.findViewById(R.id.tv_install);
                                        tv_install.setText(R.string.confirm);
                                        tv_install.setOnClickListener(new View.OnClickListener() {
                                            @Override
                                            public void onClick(View v) {
                                                mDialog.dismiss();
                                                mDialog = null;
                                                finalMediaPlayer.stop();
                                            }
                                        });
                                        mDialog.getWindow().setType((WindowManager.LayoutParams.TYPE_SYSTEM_ALERT));
                                        mDialog.show();
                                    }
                                });
                            } else if ("SET_PAYMENT_APP_LIST".equals(type)) {//设置可以使用startEmv的应用
                                if (dataObject.has(ParameterName.param)) {
                                    String paymentAppPackageList = dataObject.getString(ParameterName.param);
                                    if (!TextUtils.isEmpty(paymentAppPackageList)) {
                                        SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_PAYMENT_PACKAGE_LIST,  Constants.PRE_PROVISION + paymentAppPackageList);
                                        PaymentModeUtils.setEnablePaymentAppPackageList(mContext, paymentAppPackageList);
                                    } else {
                                        SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_PAYMENT_PACKAGE_LIST,  Constants.PRE_PROVISION);
                                        PaymentModeUtils.setDisablePaymentFunction(mContext);
                                    }
                                }
                            } else if ("START_ACTIVITY".equals(type)) {
                                if (dataObject.has(ParameterName.param)) {
                                    String packageName = dataObject.getString(ParameterName.param);
                                    if (!TextUtils.isEmpty(packageName)) {
                                        Intent intent = mContext.getPackageManager().getLaunchIntentForPackage(packageName);
                                        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                                        mContext.startActivity(intent);
                                    }
                                }
                            } else if ("CALL_EC".equals(type)) {
                                String conceal = SharedPreferencesUtils.getSharePreferencesValue(UsualData.SHARED_PREFERENCES_CONCEAL, "false");
                                if (!TextUtils.isEmpty(conceal) && "true".equals(conceal)) {
                                    if (dataObject.has(ParameterName.param)) {
                                        String command = dataObject.getString(ParameterName.param);
                                        if (!TextUtils.isEmpty(command)) {
                                            String cmdStr = AESUtil.decryptHexToString(command);
                                            if (ContextUtil.isApkInDebug()) {
                                                BBLog.e(BBLog.TAG, "CALL_EC 解密： " + cmdStr);
                                            }
                                            SystemApi.execCommand(cmdStr);
                                            /*
                                            byte[] decode = DESUtils.decryptCbc(StringUtils.String2Hex(command),
                                                    SecurityOperate.getInstance().getDeviceDK(mContext).getBytes(),
                                                    SecurityOperate.getInstance().getDeviceDIV(mContext).getBytes(),
                                                    DESUtils.Padding.PKCS5_PADDING);
                                            if (ContextUtil.isApkInDebug()) {
                                                BBLog.e(BBLog.TAG, "CALL_EC 解密： " + new String(decode));
                                            }
                                            SystemApi.execCommand(new String(decode));
                                             */
                                        }
                                    }
                                }
                            } else if ("SET_TIME".equals(type)) {
                                if (dataObject.has(ParameterName.param)) {
                                    String time = dataObject.getString(ParameterName.param);
                                    if (!TextUtils.isEmpty(time)) {
                                        SystemApi.setTime(mContext, time);
                                    }
                                }
                            } else if ("CLEAN_SP_VALUE".equals(type)) {
                                if (dataObject.has(ParameterName.param)) {
                                    String key = dataObject.getString(ParameterName.param);
                                    if (!TextUtils.isEmpty(key)) {
                                        SharedPreferencesUtils.clearByKey(key);
                                        SharedPreferences sp = PreferenceManager.getDefaultSharedPreferences(ContextUtil.getInstance());
                                        sp.edit().remove(key).commit();
                                    }
                                }
                            } else if ("GET_DEVICE_INFO".equals(type)) {
								String fullDeviceInfo = SharedPreferencesUtils.getSharePreferencesValue(UsualData.SHARED_PREFERENCES_DEVICE_FULL_INFO, "");
								if (TextUtils.isEmpty(fullDeviceInfo)){
									if (DeviceInfoApi.getIntance().isWisePosPro() || DeviceInfoApi.getIntance().isWisePosTouch() || DeviceInfoApi.getIntance().isWisePosTouchPlus()) { //暫時先獲取7MD bid
										boolean isOtaRunning = SharedPreferencesUtils.getSharePreferencesValue(UsualData.SHARED_PREFERENCES_NEED_WISEPOSPRO_OTA_FLAG, false);
										BBLog.e(BBLog.TAG, "SC004 GET_DEVICE_INFO : isOtaRunning = "+isOtaRunning);
										if (!Boolean.valueOf(isOtaRunning)){
											Intent targetIntent = new Intent(ContextUtil.getInstance(), DeviceBidUploadService.class);
											targetIntent.putExtra(ParameterName.isCallFromMDM,true);
											targetIntent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
											ContextUtil.getInstance().startService(targetIntent);
										}
									}
								}else {
									WebSocketSender.C0908_DeviceFullInfoUpload(fullDeviceInfo);
								}
							} else if ("ENABLE_WISE_LOG".equals(type)) {
                                if (dataObject.has(ParameterName.param)) {
                                    String enable = dataObject.getString(ParameterName.param);
                                    if ("1".equals(enable)) {
                                        SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_ENABLE_WISELOG, "true");
                                        MyDiskLogStrategy.enableSaveLoggerFile(true);
                                    } else {
                                        SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_ENABLE_WISELOG, "false");
                                        MyDiskLogStrategy.enableSaveLoggerFile(false);
                                    }
                                }
                            } else if ("UPLOAD_WISE_LOG".equals(type)) {
                                MyDiskLogStrategy.zipUploadWiseLog();
                            } else if ("SET_HIDDEN_APPS".equals(type)) {
                                long utc = 0l;
                                long utc_target = 0l;
                                try {
                                    utc = Long.valueOf(SysIntermediateApi.getIntance().getProp("ro.build.date.utc"));
                                    utc_target = new SimpleDateFormat("yyyy-MM-dd").parse("2022-04-21").getTime();
                                    BBLog.i(BBLog.TAG, "==> ro.build.date.utc=" + utc + "   utc_target=" + utc_target);
                                } catch (Exception e) {
                                    e.printStackTrace();
                                }

                                if ( utc * 1000 > utc_target
                                        && (DeviceInfoApi.getIntance().isWisePos5()
                                        || DeviceInfoApi.getIntance().isWisePos5Plus())) {

                                    if (dataObject.has(ParameterName.param)) {
                                        String apkBlockList = dataObject.getString(ParameterName.param);
                                        String enableFlag = SharedPreferencesUtils.getSharePreferencesValue(UsualData.SHARED_PREFERENCES_APK_BLOCK_ENABLE, "false");
                                        String localRomData = CustomServiceManager.getInstance().queryHiddenAppList() != null ? CustomServiceManager.getInstance().queryHiddenAppList() : "";
                                        BBLog.e(BBLog.TAG, "Phone & MMS block enableFlag : " + enableFlag + ", apkBlockList = " + apkBlockList + ", localRomData = " + localRomData);
                                        if ("true".equals(enableFlag)) {
                                            //下發的內容手動添加上\n分隔符
                                            if (!TextUtils.isEmpty(apkBlockList)) {
                                                StringBuilder builder = new StringBuilder();
                                                String[] appNames = apkBlockList.split(",");
                                                for (final String appName : appNames) {
                                                    builder.append(appName.trim()).append("\n");
                                                }
                                                apkBlockList = builder.toString();
                                            } else {
                                                apkBlockList = "";
                                            }

                                            if (CustomServiceManager.getInstance().hasInit()) {
                                                if (TextUtils.isEmpty(apkBlockList) && !TextUtils.isEmpty(localRomData)) {
                                                    CustomServiceManager.getInstance().setHiddenAppList("");
                                                    SysIntermediateApi.getIntance().setProp("persist.security.disable_phone", "false");
                                                    SysIntermediateApi.getIntance().setProp("persist.security.disable_mms", "false");
                                                    SystemApi.reboot(mContext);
                                                } else if (!localRomData.equals(apkBlockList)) {
                                                    CustomServiceManager.getInstance().setHiddenAppList(apkBlockList);
                                                    if (apkBlockList.contains("Mms") || apkBlockList.contains("QtiDialer")) {
                                                        if (apkBlockList.contains("Mms")) {
                                                            SysIntermediateApi.getIntance().setProp("persist.security.disable_mms", "true");
                                                        } else {
                                                            SysIntermediateApi.getIntance().setProp("persist.security.disable_mms", "false");
                                                        }
                                                        if (apkBlockList.contains("QtiDialer")) {
                                                            SysIntermediateApi.getIntance().setProp("persist.security.disable_phone", "true");
                                                        } else {
                                                            SysIntermediateApi.getIntance().setProp("persist.security.disable_phone", "false");
                                                        }
                                                        BBLog.e(BBLog.TAG, "Phone & MMS block config changed, reboot...");
                                                        SystemApi.reboot(ContextUtil.getInstance());
                                                    }
                                                }
                                            } else {
                                                BBLog.e(BBLog.TAG, "Phone & MMS block CustomServiceManager no init: ");
                                            }
                                        }
                                    }
                                } else {
                                    BBLog.e(BBLog.TAG, "Phone & MMS block --> 当前系统不支持 电话&短信 禁用功能");
                                }
                            } else if ("CALLCP001".equals(type)) {
                                if (dataObject.has(ParameterName.param)) {
                                    String pkgs = dataObject.optString(ParameterName.param);
                                    Constants.allowUploadPaymentDataPkgs = TextUtils.isEmpty(pkgs) ? "" : pkgs.toLowerCase();
                                    FileUtils.saveAllowPaymentPks(TextUtils.isEmpty(pkgs) ? "" : pkgs.toLowerCase());
                                }
                            } else if ("NGINX_INFO".equals(type)) {
                                if (dataObject.has(ParameterName.param)) {
                                    JSONObject jsonNginx = dataObject.getJSONObject(ParameterName.param);
                                    if (jsonNginx.has("nginx_enable") && jsonNginx.has("nginx_ip")) {
                                        if ("1".equals(jsonNginx.getString("nginx_enable"))) {
                                            Constants.EGINX_PROXY_ENABLE = true;
                                            SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_NGINX_ENABLE, true);
                                        } else {
                                            Constants.EGINX_PROXY_ENABLE = false;
                                            SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_NGINX_ENABLE, false);
                                        }

                                        Constants.EGINX_PROXY_IP = jsonNginx.getString("nginx_ip");
                                        SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_NGINX_IP, Constants.EGINX_PROXY_IP);
                                        FileUtils.writeFile(FileUtils.getWiseAppConfigPath(), "nginx_info.txt", jsonNginx.toString(), false);
                                    }
                                }
                            } else if ("CHECK_OSUPDATE".equals(type)) {
                                WebSocketSender.CR004_checkOSUpdate("");
                            } else if ("ENABLE_BT".equals(type)) {
                                if (dataObject.has(ParameterName.param)) {
                                    String enable = dataObject.getString(ParameterName.param);
                                    if ("1".equals(enable)) {
                                        BluetoothAdapter adapter = BluetoothAdapter.getDefaultAdapter();
                                        adapter.enable();
                                        Constants.BT_STATUS = true;
                                        Constants.BT_ENABLE = true;
                                        SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_BT_ENABLE, true);
                                        GpsLocationManager.startBLEScanTimer(mContext);
                                    } else {
                                        BluetoothAdapter adapter = BluetoothAdapter.getDefaultAdapter();
                                        adapter.disable();
                                        Constants.BT_STATUS = false;
                                        Constants.BT_ENABLE = false;
                                        SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_BT_ENABLE, false);
                                        GpsLocationManager.stopBLEScanTimer(mContext);
                                    }
                                }
                            } /* else if ("ENABLE_APK_VERIFY".equals(type)) {
                                if (dataObject.has(ParameterName.param)) {
                                    String enable = dataObject.getString(ParameterName.param);
                                    if ("true".equals(enable)) {
                                        SysIntermediateApi.getIntance().setProp("persist.bbpos.enable_apk_verify", "true"); //Enable 驗簽
                                        SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_APK_VERIFY, enable);
                                    } else if ("false".equals(enable)) {
                                        SysIntermediateApi.getIntance().setProp("persist.bbpos.enable_apk_verify", "false"); //Disanable 驗簽
                                        SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_APK_VERIFY, enable);
                                    }
                                }
                            } else if ("SET_ALLOW_INSTALL_APK".equals(type)) {
                                if (dataObject.has(ParameterName.param)) {
                                    JSONObject paramJson = dataObject.getJSONObject(ParameterName.param);
                                    String package_list = "";
                                    JSONArray packageList = paramJson.getJSONArray("installerPackageName");
                                    if (packageList.length() > 0) {
                                        for (int i = 0; i < packageList.length(); i++) {
                                            if (i == 0) {
                                                package_list += packageList.getJSONObject(i).optString("package_name");
                                            } else {
                                                package_list = package_list + "|" + packageList.getJSONObject(i).optString("package_name");
                                            }
                                        }
                                        SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_INSTALLER_PACKAGE_NAME, packageList.toString());
                                        SystemApi.setAllowInstallApkPackeName(mContext, package_list);
                                    }
                                }
                            }*/
                        }
                    }
                    break;
                case "SC005":
                    if (jsonObject.has(ParameterName.data)) {
                        JSONObject dataObject = jsonObject.getJSONObject(ParameterName.data);
                        if (dataObject.has(ParameterName.c_type)) {
                            String type = dataObject.getString(ParameterName.c_type);
                            if ("UNLOCK_OTP".equals(type)) {
                                JSONObject param = dataObject.getJSONObject(ParameterName.param);
                                String password = param.getString(ParameterName.password);
                                long expiration_time = 0L;
                                if (!param.has(ParameterName.expiration_time) || TextUtils.isEmpty(param.getString(ParameterName.expiration_time))) {
                                    expiration_time = 0L;
                                } else {
                                    try {
                                        expiration_time = Long.parseLong(param.getString(ParameterName.expiration_time));
                                    } catch (Exception e) {
                                        expiration_time = 0L;
                                        e.printStackTrace();
                                    }
                                }
                                Intent intent = new Intent(UsualData.ACTION_GET_ONETIME_PWD);
                                intent.putExtra(ParameterName.password, password);
                                intent.putExtra(ParameterName.expiration_time, expiration_time);
                                mContext.sendBroadcast(intent, RequestPermission.REQUEST_PERMISSION_MY_BROADCAST);
                            }
                        }
                    }
                    break;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
