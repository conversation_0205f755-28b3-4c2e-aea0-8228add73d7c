package com.bbpos.wiseapp.remoteviewer;

import android.annotation.SuppressLint;
import android.app.Service;
import android.content.Intent;
import android.os.IBinder;
import android.text.TextUtils;
import android.util.Base64;
import android.util.Log;

import com.bbpos.wiseapp.logger.BBLog;
import com.bbpos.wiseapp.service.common.UsualData;
import com.bbpos.wiseapp.system.api.DeviceInfoApi;
import com.bbpos.wiseapp.system.api.SystemApi;
import com.bbpos.wiseapp.tms.utils.ContextUtil;
import com.bbpos.wiseapp.utils.HttpUtils;
import com.bbpos.wiseapp.utils.SharedPreferencesUtils;
import com.neovisionaries.ws.client.WebSocket;
import com.neovisionaries.ws.client.WebSocketAdapter;
import com.neovisionaries.ws.client.WebSocketException;
import com.neovisionaries.ws.client.WebSocketExtension;
import com.neovisionaries.ws.client.WebSocketFactory;
import com.neovisionaries.ws.client.WebSocketFrame;
import com.neovisionaries.ws.client.WebSocketState;

import java.text.SimpleDateFormat;
import java.util.Date;

public class ViewService extends Service {
    private static final String TAG = BBLog.TAG;//"WiseApp2.0 RemoteView";

    private static final String DEFAULT_PICTURE_FILE_PATH = "/sdcard/TEMP.png";

    public static boolean isViewExecuting = false;
    private static boolean isInternalTest = false;

    private Thread mPictureUploadThread = null;
    
    private String  bucket_path;
    private String  pictureName;
    private int     pictureCount;
    private Long    currentTime;

    private static final String VIEW_WEBSOCKET_DEFAULT_URL = "wss://wiseapp-us.dev.wisemanager.com/remote/websockify";
    private WebSocket webSocket;
    private String viewWebsocketURL;
    private Boolean mWebSocketIsConnect = false;
    private static int mReconnectCount = 12; //重試次數為12*5 = 60s

    //v1: upload data go throught http, to S3
    private static final String VERSION_V1_SCREENCAP_FILE = "V1_SF";
    private static final String VERSION_V1_SCREENCAP_STREAM = "V1_SS";
    private static final String VERSION_V1_SCREENCAP_STREAM_MULTI_THREAD = "V1_SSMT";
    //v2: upload data go throught websocket
    private static final String VERSION_V2_SCREENCAP_FILE = "V2_SF";
    private static final String VERSION_V2_SCREENCAP_STREAM = "V2_SS";

    private static final String viewVersion = VERSION_V2_SCREENCAP_FILE;

    /**
     * 无需绑定
     * @param intent    intent
     * @return          IBinder对象
     */
    @Override
    public IBinder onBind(Intent intent) {
        return null;
    }

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        viewWebsocketURL = SharedPreferencesUtils.getSharePreferencesValue(UsualData.SHARED_PREFERENCES_WEBSOCKET_REMOTE_VIEW_SERVER_URL, VIEW_WEBSOCKET_DEFAULT_URL);
        if (intent != null) {
            bucket_path = intent.getStringExtra("bucket_path");
            BBLog.e(TAG, "RemoteView上送bucket路徑 " + bucket_path);
        }
        pictureCount = 0;

        isViewExecuting = true;
        if (mPictureUploadThread == null) {
            mPictureUploadThread = new PictureCollectorThread();
            mPictureUploadThread.start();
        }
        return START_NOT_STICKY;
    }

    /**
     * 销毁时调用该方法
     */
    @Override
    public void onDestroy() {
        super.onDestroy();
        recordViewServiceLog("RemoteView onDestroy");

        mWebSocketIsConnect = false;
        isViewExecuting = false;
        webSocket = null;
        if (mPictureUploadThread != null) {
            mPictureUploadThread.interrupt();
            mPictureUploadThread = null;
        }

        mReconnectCount = 12;
    }

    /**
     * 每次开启service时，都会调用一次该方法，用于初始化
     */
    @Override
    public void onCreate() {
        super.onCreate();
/*
        new Handler(Looper.getMainLooper()).post(new Runnable() {
            @Override
            public void run() {
                Toast.makeText(ContextUtil.getInstance(), "Start Picture Collector...", Toast.LENGTH_LONG).show();
            }
        });
*/
        BBLog.d(TAG, "Start Picture Collector...");
    }

    /**
     * 图片收集
     * 1.开启图片收集进程
     */
    class PictureCollectorThread extends Thread {
        PictureCollectorThread() {
            super("PictureCollectorThread");
            BBLog.d(TAG, "PictureCollectorThread is create");
        }

        @SuppressLint("WakelockTimeout")
        @Override
        public void run() {
            try {
                while(isViewExecuting) {
                    if (!TextUtils.isEmpty(viewVersion) && viewVersion.contains("V1")) {
                        if (VERSION_V1_SCREENCAP_STREAM.equals(viewVersion)) {
                            currentTime = System.currentTimeMillis();
                            pictureName = getPictureUploadBucket() + pictureCount + ".png";
                            if (HttpUtils.urlUploadPicture(HttpUtils.S3_VIEW_STREAMING_URL, HttpUtils.S3_VIEW_STREAMING_BUCKET_NAME, pictureName)) {
                                BBLog.e(TAG, "RemoteView pictureName：" + pictureName + "   耗时 " + (System.currentTimeMillis() - currentTime));
                                pictureCount++;
                            } else {
                                BBLog.e(TAG, "RemoteView pictureName upload failure. Picture Name: " + pictureName + "   耗时 " + (System.currentTimeMillis() - currentTime));
                            }
                        } else if (VERSION_V1_SCREENCAP_STREAM_MULTI_THREAD.equals(viewVersion)) {
                            Thread.sleep(500);
                            new uploadThread().start();
                        }
                    } else if (!TextUtils.isEmpty(viewVersion) && viewVersion.contains("V2")) {
                        if (mWebSocketIsConnect) {
                            Thread.sleep(50);
                            byte[] scByteArray = null;
                            long screencapTime = 0;
                            long compressTime = 0;
                            long cur = System.currentTimeMillis();
                            if (VERSION_V2_SCREENCAP_FILE.equals(viewVersion)) {
                                SystemApi.screenShotByShell(DEFAULT_PICTURE_FILE_PATH);
                                screencapTime = System.currentTimeMillis() - cur;
                                cur = System.currentTimeMillis();
                                scByteArray = SystemApi.compressPicture(DEFAULT_PICTURE_FILE_PATH, 60);
                                compressTime = System.currentTimeMillis() - cur;
                            }
                            if (VERSION_V2_SCREENCAP_STREAM.equals(viewVersion)) {
                                scByteArray = HttpUtils.getScreencapBytes();
                                screencapTime = System.currentTimeMillis() - cur;
                            }
                            if (scByteArray.length > 0) {
                                getWebSocket().sendText(Base64.encodeToString(scByteArray, Base64.DEFAULT));
                            }
                            BBLog.v(BBLog.TAG, "RemoteView " +
                                    "截屏耗时：" + screencapTime + " ms，" +
                                    "壓縮耗時：" + compressTime + " ms，" +
                                    "數據大小：" + scByteArray.length + " byte，" +
                                    "縂共耗時：" + (screencapTime + compressTime) + " ms");
                        } else {
                            if (mReconnectCount > 0) {
                                BBLog.d(TAG, "RemoteView websocket is not connectted, reconnect count: " + mReconnectCount);
                                getWebSocket().connect();
                                Thread.sleep(5000);
                                mReconnectCount--;
                            }
                        }
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
                recordViewServiceLog(Log.getStackTraceString(e));
            }
        }
    }

    class uploadThread extends Thread{
        @Override
        public void run() {
            currentTime = System.currentTimeMillis();
            pictureName = genPictureName();
            if (HttpUtils.urlUploadPicture(HttpUtils.S3_VIEW_STREAMING_URL, HttpUtils.S3_VIEW_STREAMING_BUCKET_NAME, pictureName)) {
                BBLog.e(TAG, "RemoteView pictureName：" + pictureName + "   耗时 " + (System.currentTimeMillis() - currentTime));
            } else {
                BBLog.e(TAG, "RemoteView pictureName upload failure. Picture Name: " + pictureName + "   耗时 " + (System.currentTimeMillis() - currentTime));
            }
        }

        public synchronized String genPictureName() {
            String pictureFileCompressName = getPictureUploadBucket() + (pictureCount) + ".png";
            pictureCount ++;
            BBLog.e(TAG, "RemoteView pictureCount：" + pictureCount);
            return pictureFileCompressName;
        }
    }

    private String getPictureUploadBucket() {
        if (isInternalTest) {
            String mode = SharedPreferencesUtils.getSharePreferencesValue(UsualData.SHARED_PREFERENCES_MODE_IN_CONFIG, "uat");
            String sn = DeviceInfoApi.getIntance().getSerialNumber();
            SimpleDateFormat format = new SimpleDateFormat("yyyyMMdd");
            String date = format.format(new Date(System.currentTimeMillis()));

            return mode + "/" + sn + "/" + date;
        }

        return bucket_path;
    }

    /**
     * 这个注意是记录错误的日志
     * 记录日志服务的基本信息 防止日志服务有错，在LogCat日志中无法查找
     * @param msg msg
     */
    private void recordViewServiceLog(String msg) {
        BBLog.e(TAG, msg);
    }

    private WebSocket getWebSocket() {
        try {
            if (webSocket == null) {
                webSocket = new WebSocketFactory()
                        .getProxySettings()
                        .setServer(ContextUtil.getProxy())
                        .getWebSocketFactory()
                        .setConnectionTimeout(5000)
                        .createSocket(viewWebsocketURL + "?serialNo=" + DeviceInfoApi.getIntance().getSerialNumber())
                        .addListener(new WebSocketAdapter() {
                            public void onTextMessage(WebSocket websocket, String message) {
                                BBLog.i(TAG, message);
                            }

                            public void onDisconnected(WebSocket websocket,
                                                       WebSocketFrame serverCloseFrame,
                                                       WebSocketFrame clientCloseFrame,
                                                       boolean closedByServer) throws Exception {
                                BBLog.i(TAG, "On Disconnect called.");
                                mWebSocketIsConnect = false;
                                isViewExecuting = false;
                                webSocket = null;
                                if (mPictureUploadThread != null) {
                                    mPictureUploadThread.interrupt();
                                    mPictureUploadThread = null;
                                }
                            }

                            public void onCloseFrame(WebSocket websocket, WebSocketFrame frame) throws Exception {
                                BBLog.i(TAG, "Received close frame.");
                                BBLog.i(TAG, frame.getPayloadText());
                            }

                            public void onError(WebSocket websocket, WebSocketException cause) throws Exception {
                                BBLog.i(TAG, "An exception happened during the websocket operation");
                                BBLog.i(TAG, String.format("On Frame error %s", cause.getCause()));
                            }

                            public void onStateChanged(WebSocket websocket, WebSocketState newState) throws Exception {
                                BBLog.i(TAG, String.format("New state %s", newState.toString()));
                                String state = newState.toString();
                                if ("OPEN".equals(state)) {
                                    mWebSocketIsConnect = true;
                                }
                            }

                            public void onFrameError(WebSocket websocket, WebSocketException cause, WebSocketFrame frame) throws Exception {
                                BBLog.i(TAG, String.format("On Frame error %s", cause.getCause()));
                            }
                        })
                        .addExtension(WebSocketExtension.PERMESSAGE_DEFLATE);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        return webSocket;
    }
}