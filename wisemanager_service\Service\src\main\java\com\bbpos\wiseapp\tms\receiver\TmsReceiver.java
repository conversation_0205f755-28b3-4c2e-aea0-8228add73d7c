package com.bbpos.wiseapp.tms.receiver;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;

import com.bbpos.wiseapp.logger.BBLog;
import com.bbpos.wiseapp.tms.location.GpsLocationManager;
import com.bbpos.wiseapp.tms.utils.BroadcastActions;

public class TmsReceiver extends BroadcastReceiver {
	@Override
	public void onReceive(Context context, Intent intent) {
		String action = intent.getAction();
		BBLog.v(BBLog.TAG, "TmsReceiver receiver Broadcast " + action);
		try {
			if (BroadcastActions.GPS_SCHEDULE_TIME.equals(action)){
				GpsLocationManager.registLocationChangeListener();
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
}
