package com.dspread.mdm.service.platform.manager

import android.annotation.SuppressLint
import android.app.AlarmManager
import android.content.Context
import android.net.wifi.WifiManager
import android.os.Build
import android.os.PowerManager
import com.dspread.mdm.service.utils.log.Logger
import com.dspread.mdm.service.broadcast.core.BroadcastActions
import com.dspread.mdm.service.broadcast.core.BroadcastSender

/**
 * WakeLock管理器
 * 为关键任务提供CPU唤醒锁
 */
object WakeLockManager {

    private const val TAG = "WakeLockManager"
    private val wakeLocks = mutableMapOf<String, PowerManager.WakeLock>()

    // 持续WakeLock，防止系统深度休眠
    private var persistentWakeLock: PowerManager.WakeLock? = null
    private const val PERSISTENT_WAKELOCK_TAG = "SmartMdmService:PersistentWakeLock"

    // WiFi锁，防止WiFi休眠 - 简化设计
    @Volatile
    private var wifiLock: WifiManager.WifiLock? = null
    private const val WIFI_LOCK_TAG = "SmartMdmService:WiFiLock"
    
    /**
     * 获取WakeLock
     */
    fun acquireWakeLock(context: Context, tag: String, timeout: Long = 0): Boolean {
        return try {
            // 如果已经有相同tag的WakeLock，先释放
            releaseWakeLock(tag)
            
            val powerManager = context.getSystemService(Context.POWER_SERVICE) as PowerManager
            val wakeLock = powerManager.newWakeLock(
                PowerManager.PARTIAL_WAKE_LOCK,
                "MDMService:$tag"
            )
            
            if (timeout > 0) {
                wakeLock.acquire(timeout)
                Logger.platform("$TAG 获取WakeLock成功: $tag (超时: ${timeout}ms)")
            } else {
                wakeLock.acquire()
                Logger.platform("$TAG 获取WakeLock成功: $tag (无超时)")
            }
            
            wakeLocks[tag] = wakeLock
            true
            
        } catch (e: Exception) {
            Logger.platformE("$TAG 获取WakeLock失败: $tag", e)
            false
        }
    }
    
    /**
     * 释放WakeLock
     */
    fun releaseWakeLock(tag: String): Boolean {
        return try {
            val wakeLock = wakeLocks.remove(tag)
            if (wakeLock != null && wakeLock.isHeld) {
                wakeLock.release()
                Logger.platform("$TAG 释放WakeLock成功: $tag")
                true
            } else {
                Logger.platform("$TAG WakeLock不存在或未持有: $tag")
                false
            }
        } catch (e: Exception) {
            Logger.platformE("$TAG 释放WakeLock失败: $tag", e)
            false
        }
    }
    
    /**
     * 延迟释放WakeLock
     */
    fun releaseWakeLockDelayed(tag: String, delayMs: Long) {
        try {
            android.os.Handler(android.os.Looper.getMainLooper()).postDelayed({
                releaseWakeLock(tag)
            }, delayMs)
            Logger.platform("$TAG 设置延迟释放WakeLock: $tag (延迟: ${delayMs}ms)")
        } catch (e: Exception) {
            Logger.platformE("$TAG 设置延迟释放WakeLock失败: $tag", e)
        }
    }
    
    /**
     * 释放所有WakeLock
     */
    fun releaseAllWakeLocks() {
        val tags = wakeLocks.keys.toList()
        tags.forEach { tag ->
            releaseWakeLock(tag)
        }
        Logger.platform("$TAG 释放所有WakeLock完成")
    }
    
    /**
     * 获取当前WakeLock状态
     */
    fun getWakeLockStatus(): String {
        return buildString {
            append("WakeLock状态: ")
            if (wakeLocks.isEmpty()) {
                append("无活跃WakeLock")
            } else {
                wakeLocks.forEach { (tag, wakeLock) ->
                    append("$tag=${if (wakeLock.isHeld) "持有" else "未持有"}, ")
                }
            }
        }
    }
    
    /**
     * 检查屏幕状态
     */
    fun isScreenOn(context: Context): Boolean {
        return try {
            val powerManager = context.getSystemService(Context.POWER_SERVICE) as PowerManager
            powerManager.isInteractive // 使用现代API替代已废弃的isScreenOn
        } catch (e: Exception) {
            Logger.platformE("$TAG 检查屏幕状态失败", e)
            false
        }
    }

    /**
     * 获取智能WakeLock（基于Android官方最佳实践）
     * 使用超时机制避免长期持有，通过AlarmManager定期续期
     */
    fun acquireSmartWakeLock(context: Context, durationMs: Long = 60000): Boolean {
        return try {
            if (persistentWakeLock?.isHeld == true) {
                // 延长时间并重新设置续期定时器
                Logger.platform("$TAG 智能WakeLock已经持有，延长时间并重新设置续期")
                persistentWakeLock?.acquire(durationMs)
                // 重新设置续期定时器，确保在WakeLock过期前续期
                scheduleWakeLockRenewal(context, durationMs)
                return true
            }

            val powerManager = context.getSystemService(Context.POWER_SERVICE) as PowerManager
            persistentWakeLock = powerManager.newWakeLock(
                PowerManager.PARTIAL_WAKE_LOCK,
                PERSISTENT_WAKELOCK_TAG
            )

            // 使用超时机制，避免长期持有（Android官方推荐）
            persistentWakeLock?.acquire(durationMs)
            Logger.platform("$TAG 获取智能WakeLock成功，持续时间: ${durationMs}ms")

            // 设置续期AlarmManager
            scheduleWakeLockRenewal(context, durationMs)

            true

        } catch (e: Exception) {
            Logger.platformE("$TAG 获取智能WakeLock失败", e)
            false
        }
    }

    /**
     * 释放持续WakeLock
     */
    fun releasePersistentWakeLock(context: Context): Boolean {
        return try {
            // 取消续期定时器
            cancelWakeLockRenewal(context)

            persistentWakeLock?.let { wakeLock ->
                if (wakeLock.isHeld) {
                    wakeLock.release()
                    Logger.platform("$TAG 释放智能WakeLock成功")
                }
            }
            persistentWakeLock = null
            true

        } catch (e: Exception) {
            Logger.platformE("$TAG 释放智能WakeLock失败", e)
            false
        }
    }

    /**
     * 检查持续WakeLock状态
     */
    fun isPersistentWakeLockHeld(): Boolean {
        return try {
            persistentWakeLock?.isHeld == true
        } catch (e: Exception) {
            // 如果检查失败，假设没有持有
            false
        }
    }

    /**
     * 获取持久WakeLock状态信息（用于调试）
     */
    fun getPersistentWakeLockStatus(): String {
        return try {
            val isHeld = persistentWakeLock?.isHeld ?: false
            val tag = persistentWakeLock?.toString() ?: "null"
            "持久WakeLock状态: held=$isHeld, tag=$tag"
        } catch (e: Exception) {
            "持久WakeLock状态检查失败: ${e.message}"
        }
    }

    /**
     * 检查特定标签的WakeLock是否正在运行
     */
    fun isWakeLockHeld(tag: String): Boolean {
        return wakeLocks[tag]?.isHeld == true
    }

    /**
     * 检查是否有Remote View相关的WakeLock在运行
     */
    fun hasRemoteViewWakeLock(): Boolean {
        return isWakeLockHeld("RemoteView")
    }

    /**
     * 设置WakeLock续期机制（基于AlarmManager）
     * 参考Android官方文档：使用AlarmManager在Doze模式下唤醒设备
     */
    @SuppressLint("ScheduleExactAlarm")
    private fun scheduleWakeLockRenewal(context: Context, intervalMs: Long) {
        try {
            val alarmManager = context.getSystemService(Context.ALARM_SERVICE) as android.app.AlarmManager

            val pendingIntent = BroadcastSender.createDynamicPendingIntent(
                context,
                BroadcastActions.ACTION_WAKELOCK_RENEWAL,
                0
            )

            // 先取消之前的定时器，避免重复设置
            alarmManager.cancel(pendingIntent)

            // 正确的续期间隔计算
            // 续期时间应该在WakeLock过期前，而不是过期后
            val renewalInterval = maxOf(intervalMs - 30000L, 30000L) // 提前30秒续期，最少30秒间隔
            val triggerTime = System.currentTimeMillis() + renewalInterval

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                alarmManager.setExactAndAllowWhileIdle(
                    AlarmManager.RTC_WAKEUP,
                    triggerTime,
                    pendingIntent
                )
            } else {
                alarmManager.setExact(
                    AlarmManager.RTC_WAKEUP,
                    triggerTime,
                    pendingIntent
                )
            }

            Logger.platform("$TAG 设置WakeLock续期定时器: ${intervalMs}ms")

        } catch (e: Exception) {
            Logger.platformE("$TAG 设置WakeLock续期失败", e)
        }
    }

    /**
     * 取消WakeLock续期机制
     */
    private fun cancelWakeLockRenewal(context: Context) {
        try {
            val alarmManager = context.getSystemService(Context.ALARM_SERVICE) as android.app.AlarmManager

            val pendingIntent = BroadcastSender.createDynamicPendingIntent(
                context,
                BroadcastActions.ACTION_WAKELOCK_RENEWAL,
                0
            )

            alarmManager.cancel(pendingIntent)
            Logger.platform("$TAG 取消WakeLock续期定时器")

        } catch (e: Exception) {
            Logger.platformE("$TAG 取消WakeLock续期失败", e)
        }
    }

    /**
     * 获取WiFi锁，防止WiFi休眠导致WebSocket断开
     * 简化版本：只使用标准模式，避免复杂的模式切换
     */
    fun acquireWifiLock(context: Context): Boolean {
        return try {
            // 如果已经持有有效的锁，直接返回成功
            if (wifiLock?.isHeld == true) {
                Logger.platform("$TAG WiFi锁已经持有")
                return true
            }

            // 清理旧锁（防御性编程）
            releaseWifiLockInternal()

            // 创建新的WiFi锁（使用标准模式，平衡性能和耗电）
            val wifiManager = context.applicationContext.getSystemService(Context.WIFI_SERVICE) as WifiManager
            wifiLock = wifiManager.createWifiLock(WifiManager.WIFI_MODE_FULL, WIFI_LOCK_TAG)
            wifiLock?.acquire()

            Logger.platform("$TAG 获取WiFi锁成功，防止WiFi休眠")
            true

        } catch (e: Exception) {
            Logger.platformE("$TAG 获取WiFi锁失败", e)
            // 异常时清理可能已创建的锁对象
            wifiLock = null
            false
        }
    }

    /**
     * 释放WiFi锁
     */
    fun releaseWifiLock(): Boolean {
        return releaseWifiLockInternal()
    }

    /**
     * 内部释放WiFi锁方法，避免递归调用
     */
    private fun releaseWifiLockInternal(): Boolean {
        return try {
            wifiLock?.let { lock ->
                if (lock.isHeld) {
                    lock.release()
                    Logger.platform("$TAG 释放WiFi锁成功")
                } else {
                    Logger.platform("$TAG WiFi锁未持有，无需释放")
                }
            }
            wifiLock = null
            true

        } catch (e: Exception) {
            Logger.platformE("$TAG 释放WiFi锁失败", e)
            // 即使释放失败，也要清空引用，避免内存泄漏
            wifiLock = null
            false
        }
    }

    /**
     * 检查WiFi锁状态（用于调试）
     */
    fun getWifiLockStatus(): String {
        return try {
            val isHeld = wifiLock?.isHeld ?: false
            val tag = wifiLock?.toString() ?: "null"
            "WiFi锁状态: held=$isHeld, tag=$tag"
        } catch (e: Exception) {
            "WiFi锁状态检查失败: ${e.message}"
        }
    }
}
