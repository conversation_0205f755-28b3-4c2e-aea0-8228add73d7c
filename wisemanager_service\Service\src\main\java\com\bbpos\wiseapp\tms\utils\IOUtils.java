package com.bbpos.wiseapp.tms.utils;

import android.util.Log;

import com.bbpos.wiseapp.logger.BBLog;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;

public class IOUtils {
	private static final String TAG = IOUtils.class.getName();
	
	public static void closeInputStream(InputStream is) {
		if (is != null) {
			try {
				is.close();
			} catch (IOException e) {
				e.printStackTrace();
				BBLog.e(BBLog.TAG, "error in closeInputStream", e);
			}
		}
	}
	
	public static void flushCloseOutputStream(OutputStream fos) {
		if (fos != null) {
			try {
				fos.flush();
				fos.close();
			} catch (IOException e) {
				e.printStackTrace();
				BBLog.e(BBLog.TAG, "error in flushCloseOutputStream", e);
			}
		}
	}
}
