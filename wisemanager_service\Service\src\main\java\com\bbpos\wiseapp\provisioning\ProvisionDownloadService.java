package com.bbpos.wiseapp.provisioning;

import android.content.Context;
import android.content.Intent;
import android.os.IBinder;
import android.text.TextUtils;

import com.bbpos.wiseapp.logger.BBLog;
import com.bbpos.wiseapp.service.WakeLockService;
import com.bbpos.wiseapp.service.common.UsualData;
import com.bbpos.wiseapp.tms.adapter.SystemManagerAdapter;
import com.bbpos.wiseapp.tms.utils.Constants;
import com.bbpos.wiseapp.tms.utils.ParameterName;
import com.bbpos.wiseapp.tms.utils.RulebasedAppListManager;
import com.bbpos.wiseapp.utils.FileUtils;
import com.bbpos.wiseapp.utils.HttpUtils;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.File;

public class ProvisionDownloadService extends WakeLockService {
    private Context mContext = null;
    public static boolean isBusyNow = false;

    public ProvisionDownloadService() {
        super("ProvisionDownloadService");
    }

    @Override
    protected void onHandleIntent(Intent intent) {
        isBusyNow = true;
        mContext = getApplicationContext();
        if (Constants.m_gms_applist==null || Constants.m_gms_applist.length()<=0) {
            return;
        }
        try {
            JSONArray jsonArray = new JSONArray(Constants.m_gms_applist);
            for (int idx=0; idx<jsonArray.length(); idx++) {
                JSONObject app = jsonArray.getJSONObject(idx);
                String appUrl = app.getString(UsualData.SHARED_PREFERENCES_APP_URL);
                String appMd5 = app.getString(UsualData.SHARED_PREFERENCES_APP_MD5);
                String apkName = app.getString(ParameterName.apkName);
                String packName = app.getString(ParameterName.packName);
                String versionName = app.getString(ParameterName.versionName);
                String versionCode = app.getString(ParameterName.versionCode);
                String fileKey = app.optString(ParameterName.fileKey);

                int ret = RulebasedAppListManager.isApkInstalled(packName, versionName, versionCode);
                if (ret==1 || ret==2) {
                    continue;
                }

                if (!TextUtils.isEmpty(fileKey)) {
                    String response = "";
                    for (int i=0; i<3; i++) {
                        response = com.bbpos.wiseapp.tms.network.HttpUtils.postGetFileUrl(Constants.S3_RESOURCE_URL, com.bbpos.wiseapp.tms.network.HttpUtils.getPostTokenRequestContent(fileKey));
                        if (!TextUtils.isEmpty(response)) {
                            break;
                        }
                    }

                    if (TextUtils.isEmpty(response)) {
                        continue;
                    } else {
                        appUrl = response;
                    }
                }
                final String apkPath = FileUtils.getWiseAppConfigPath() + FileUtils.FILE_SEPARATOR + apkName;
                int trycount = 3;
                boolean download_suc = false;
                BBLog.i(ProvisionService.TAG, "download app " + idx + ":" + packName);
                while (trycount-- > 0 && !download_suc) {
                    download_suc = HttpUtils.url2file(appUrl, apkPath, appMd5, new HttpUtils.FileDownloadCallBack() {
                        @Override
                        public void onDownloading(long curFileSize, long fileSize) {
                        }

                        @Override
                        public void requestSuccess(JSONObject responseJson) throws Exception {
                        }

                        @Override
                        public void requestFail(int errorCode, String errorStr) {
                        }
                    });
                }
                if (download_suc) {
                    final File apkFile = new File(apkPath + ".apk");
                    final File apkFileOrg = new File(apkPath + Constants.APK_ORIGINAL_SUFFIX + ".apk");
                    SystemManagerAdapter.installApk(ProvisionDownloadService.this, apkPath, new SystemManagerAdapter.ApkInstallCompleted() {
                        @Override
                        public void onInstallFinished(String pkgName, int returnCode) {
                            if (returnCode == 1) {
                                BBLog.i(ProvisionService.TAG, "GMS app: " + packName + "installed succ");
                                if (apkFile.exists()) {// 安装成功后删除apk文件
                                    apkFile.delete();
                                } else if (apkFileOrg.exists()) {// 安装成功后删除apk文件
                                    apkFileOrg.delete();
                                }
                            } else {
                                BBLog.i(ProvisionService.TAG, "GMS app: " + packName + "installed failed");
                            }
                        }
                    });
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public IBinder onBind(Intent intent) {
        return null;
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        isBusyNow = false;
    }

    public static boolean isGMSAppNeedDownload() {
        BBLog.w(BBLog.TAG, "GMS apk list：" + Constants.m_gms_applist);
        if (Constants.m_gms_applist==null || Constants.m_gms_applist.length()<=0 || Constants.m_need_gms_download==false) {
            return false;
        }
        try {
            JSONArray jsonArray = new JSONArray(Constants.m_gms_applist);
            for (int idx=0; idx<jsonArray.length(); idx++) {
                JSONObject app = jsonArray.getJSONObject(idx);
                String packName = app.getString(ParameterName.packName);
                String versionName = app.getString(ParameterName.versionName);
                String versionCode = app.getString(ParameterName.versionCode);
                int ret = RulebasedAppListManager.isApkInstalled(packName, versionName, versionCode);
                if (ret==1 || ret==2) {
                    continue;
                } else {
                    return true;
                }
            }
            Constants.m_need_gms_download = false;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

    public static boolean isGMSApp(String package_name) {
        if (Constants.m_gms_applist==null || Constants.m_gms_applist.length()<=0) {
            return false;
        }
        try {
            JSONArray jsonArray = new JSONArray(Constants.m_gms_applist);
            for (int idx = 0; idx < jsonArray.length(); idx++) {
                JSONObject app = jsonArray.getJSONObject(idx);
                String packName = app.getString(ParameterName.packName);
                if (package_name.equals(packName)) {
                    BBLog.w(ProvisionService.TAG, package_name + "is a GMS apk, not to delete");
                    return true;
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        return false;
    }
}
