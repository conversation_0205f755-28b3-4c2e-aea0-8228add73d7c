package com.bbpos.wiseapp.utils;

import android.content.Context;
import android.provider.Settings;

public class PaymentModeUtils {

    /**
     *  设置可以使用startEmv的应用程序
     * @param context
     * @param applist  字符串格式，使用英文逗号进行分割 com.bbpos.bbdevice.testapp,com.bbpos.bbdevice.wsp7xexample,
     *                 为""时，代表禁用startEmv
     */
    public static void setEnablePaymentAppPackageList(Context context, String applist) {
        Settings.System.putString(context.getContentResolver(), "EnablePaymentAppPackageList", applist);
    }

    /**
     * 關閉 startEmv 功能
     * @param context
     */
    public static void setDisablePaymentFunction(Context context) {
        Settings.System.putString(context.getContentResolver(), "EnablePaymentAppPackageList", "");
    }

    /**
     *  获取可以使用startEmv的应用程序
     * @param context
     * @return
     */
    public static String getEnablePaymentAppPackageList(Context context) {
        return Settings.System.getString(context.getContentResolver(), "EnablePaymentAppPackageList");
    }
}