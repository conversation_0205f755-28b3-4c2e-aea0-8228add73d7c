package com.dspread.mdm.service.broadcast.core

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.net.ConnectivityManager
import android.os.Build
import com.dspread.mdm.service.utils.log.Logger
import java.util.concurrent.ConcurrentHashMap

/**
 * 统一广播管理器
 * 负责统一管理所有广播的注册、注销和事件分发
 */
object BroadcastManager {
    
    private const val TAG = "BroadcastManager"
    
    // 事件处理器映射
    private val eventHandlers = ConcurrentHashMap<String, MutableList<BroadcastEventHandler>>()
    
    // 广播接收器
    private var unifiedReceiver: UnifiedBroadcastReceiver? = null
    
    // 初始化状态
    private var isInitialized = false
    
    /**
     * 初始化广播管理器
     */
    fun initialize(context: Context) {
        if (isInitialized) {
            Logger.receiver("$TAG 已初始化")
            return
        }
        
        try {
            Logger.receiver("$TAG 开始初始化...")
            
            // 创建统一广播接收器
            unifiedReceiver = UnifiedBroadcastReceiver()
            
            // 注册广播接收器
            registerUnifiedReceiver(context)
            
            isInitialized = true
            Logger.success("$TAG 初始化完成")
            
        } catch (e: Exception) {
            Logger.receiverE("$TAG 初始化失败", e)
        }
    }
    
    /**
     * 注册事件处理器
     */
    fun registerEventHandler(handler: BroadcastEventHandler) {
        try {
            val supportedActions = handler.getSupportedActions()
            
            for (action in supportedActions) {
                val handlers = eventHandlers.getOrPut(action) { mutableListOf() }
                if (!handlers.contains(handler)) {
                    handlers.add(handler)
                    Logger.receiver("$TAG 注册事件处理器: ${handler.getHandlerName()} -> $action")
                }
            }
            
        } catch (e: Exception) {
            Logger.receiverE("$TAG 注册事件处理器失败: ${handler.getHandlerName()}", e)
        }
    }
    
    /**
     * 注销事件处理器
     */
    fun unregisterEventHandler(handler: BroadcastEventHandler) {
        try {
            val supportedActions = handler.getSupportedActions()
            
            for (action in supportedActions) {
                eventHandlers[action]?.remove(handler)
                Logger.receiver("$TAG 注销事件处理器: ${handler.getHandlerName()} -> $action")
            }
            
            // 清理处理器资源
            handler.cleanup()
            
        } catch (e: Exception) {
            Logger.receiverE("$TAG 注销事件处理器失败: ${handler.getHandlerName()}", e)
        }
    }
    
    /**
     * 分发广播事件
     */
    internal fun dispatchBroadcast(context: Context, intent: Intent) {
        val action = intent.action ?: return
        
        try {
            val handlers = eventHandlers[action]
            if (handlers.isNullOrEmpty()) {
                Logger.receiver("$TAG 没有找到处理器: $action")
                return
            }
            
            // 不再记录分发日志，减少冗余信息

            for (handler in handlers) {
                try {
                    val success = handler.handleBroadcast(context, intent)
                    // 只记录处理失败的情况
                    if (!success) {
                        Logger.receiverE("$TAG 处理器 ${handler.getHandlerName()} 处理失败: $action")
                    }
                } catch (e: Exception) {
                    Logger.receiverE("$TAG 处理器 ${handler.getHandlerName()} 处理异常: $action", e)
                }
            }
            
        } catch (e: Exception) {
            Logger.receiverE("$TAG 分发广播事件失败: $action", e)
        }
    }
    
    /**
     * 清理资源
     */
    fun cleanup(context: Context) {
        try {
            Logger.receiver("$TAG 开始清理资源...")
            
            // 注销广播接收器
            unifiedReceiver?.let {
                context.unregisterReceiver(it)
                unifiedReceiver = null
            }
            
            // 清理所有事件处理器
            eventHandlers.values.forEach { handlers ->
                handlers.forEach { handler ->
                    try {
                        handler.cleanup()
                    } catch (e: Exception) {
                        Logger.receiverE("$TAG 清理处理器失败: ${handler.getHandlerName()}", e)
                    }
                }
            }
            eventHandlers.clear()
            
            isInitialized = false
            Logger.success("$TAG 资源清理完成")
            
        } catch (e: Exception) {
            Logger.receiverE("$TAG 清理资源失败", e)
        }
    }
    
    /**
     * 注册统一广播接收器
     */
    private fun registerUnifiedReceiver(context: Context) {
        val filter = IntentFilter().apply {
            // 系统广播
            addAction(BroadcastActions.ACTION_BOOT_COMPLETED)
            addAction(BroadcastActions.ACTION_QUICKBOOT_POWERON)
            addAction(Intent.ACTION_LOCKED_BOOT_COMPLETED)
            addAction(BroadcastActions.ACTION_NETWORK_CHANGED)
            addAction(Intent.ACTION_BOOT_COMPLETED)
            addAction(ConnectivityManager.CONNECTIVITY_ACTION)
            addAction(BroadcastActions.ACTION_SCREEN_ON)
            addAction(BroadcastActions.ACTION_SCREEN_OFF)
            
            // 电池相关
            addAction(Intent.ACTION_BATTERY_CHANGED)
            addAction(Intent.ACTION_BATTERY_LOW)
            addAction(Intent.ACTION_BATTERY_OKAY)
            addAction(Intent.ACTION_POWER_CONNECTED)
            addAction(Intent.ACTION_POWER_DISCONNECTED)
            
            // 系统事件
            addAction(Intent.ACTION_SHUTDOWN)
            addAction(Intent.ACTION_REBOOT)
            addAction(Intent.ACTION_USER_PRESENT)
            addAction(Intent.ACTION_TIME_TICK)

            // 服务管理相关
            addAction(BroadcastActions.ACTION_SERVICE_RESTART)

            // WakeLock相关
            addAction(BroadcastActions.ACTION_WAKELOCK_RENEWAL)
            addAction(BroadcastActions.ACTION_WAKELOCK_ACQUIRE)
            addAction(BroadcastActions.ACTION_WAKELOCK_RELEASE)
            
            // WebSocket相关
            addAction(BroadcastActions.ACTION_WEBSOCKET_CONNECT)
            addAction(BroadcastActions.ACTION_WEBSOCKET_DISCONNECT)
            addAction(BroadcastActions.ACTION_WEBSOCKET_RECONNECT)
            addAction(BroadcastActions.ACTION_WEBSOCKET_SEND_HEARTBEAT)
            
            // 定时器相关
            addAction(BroadcastActions.ACTION_POLL_TIMER_START)
            addAction(BroadcastActions.TER_INFO_UPLOAD_BC)
            addAction(BroadcastActions.ACTION_SERVICE_GUARD_TIMER)
            addAction(BroadcastActions.ACTION_PROVISIONING_TIMER)

            // 任务执行相关
            addAction(BroadcastActions.ACTION_WSTASK_EXEC_BC)
            addAction(BroadcastActions.CHECK_SELF_UPDATE_STATUS)
        }

        // Android 14 (API 34) 及以上版本需要明确指定 RECEIVER_EXPORTED 或 RECEIVER_NOT_EXPORTED
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE) {
            context.registerReceiver(unifiedReceiver, filter, Context.RECEIVER_NOT_EXPORTED)
        } else {
            context.registerReceiver(unifiedReceiver, filter)
        }

        // 单独注册包更新广播（需要特殊的data scheme）
        registerPackageUpdateReceiver(context)

        Logger.receiver("$TAG 统一广播接收器注册完成")
    }

    /**
     * 注册包更新广播接收器
     */
    private fun registerPackageUpdateReceiver(context: Context) {
        try {
            val packageFilter = IntentFilter().apply {
                addAction(Intent.ACTION_PACKAGE_ADDED)
                addAction(Intent.ACTION_PACKAGE_REMOVED)
                addAction(Intent.ACTION_PACKAGE_REPLACED)
                addDataScheme("package")
            }

            // Android 14 (API 34) 及以上版本需要明确指定 RECEIVER_EXPORTED 或 RECEIVER_NOT_EXPORTED
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE) {
                context.registerReceiver(unifiedReceiver, packageFilter, Context.RECEIVER_NOT_EXPORTED)
            } else {
                context.registerReceiver(unifiedReceiver, packageFilter)
            }
            Logger.receiver("$TAG 包更新广播接收器注册完成")

        } catch (e: Exception) {
            Logger.receiverE("$TAG 注册包更新广播接收器失败", e)
        }
    }
    
    /**
     * 统一广播接收器
     */
    private class UnifiedBroadcastReceiver : BroadcastReceiver() {
        override fun onReceive(context: Context, intent: Intent) {
            val action = intent.action ?: return

            // 精简日志：只记录关键广播
            val isKeyBroadcast = action in listOf(
                Intent.ACTION_PACKAGE_ADDED,
                Intent.ACTION_PACKAGE_REMOVED,
                Intent.ACTION_PACKAGE_REPLACED,
                BroadcastActions.ACTION_PROVISIONING_TIMER  // 只记录Provisioning定时器
            )

            if (isKeyBroadcast) {
                Logger.receiver("$TAG 收到广播: $action")
            }

            // 分发给事件处理器
            dispatchBroadcast(context, intent)
        }
    }
}
