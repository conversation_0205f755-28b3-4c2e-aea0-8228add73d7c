package com.bbpos.wiseapp.service.appdata.model;

import android.database.Cursor;
import android.util.Log;

import com.bbpos.wiseapp.logger.BBLog;

public class BaseDataModel {
	protected static final String TAG = BaseDataModel.class.getName();
	private int getcolumnIndex(Cursor cursor,String columnName){
		int index = cursor.getColumnIndex(columnName);
		if(index == -1)
			BBLog.e(BBLog.TAG, "columnName:"+columnName+" not exist");
		return index;
	}
	
	public String getCursorString(Cursor cursor,String columnName){
		return cursor.getString(getcolumnIndex(cursor, columnName));
	}
	
	public int getCursorInt(Cursor cursor,String columnName){
		return cursor.getInt(getcolumnIndex(cursor, columnName));
	}
	
	public long getCursorLong(Cursor cursor,String columnName){
		return cursor.getLong(getcolumnIndex(cursor, columnName));
	}

}
