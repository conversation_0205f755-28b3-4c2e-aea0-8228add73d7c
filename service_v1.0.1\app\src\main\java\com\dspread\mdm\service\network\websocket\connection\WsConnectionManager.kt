package com.dspread.mdm.service.network.websocket.connection

import android.content.Context
import android.os.Handler
import android.os.Looper
import android.os.Message
import com.dspread.mdm.service.SmartMdmServiceApp
import com.neovisionaries.ws.client.*
import com.dspread.mdm.service.network.websocket.constant.WebSocketStatus
import com.dspread.mdm.service.network.websocket.WebSocketCenter
import com.dspread.mdm.service.platform.api.device.DeviceInfoApi
import com.dspread.mdm.service.utils.log.Logger
import com.dspread.mdm.service.modules.provisioning.ProvisioningManager
import java.util.*
import java.util.concurrent.atomic.AtomicBoolean
import java.util.concurrent.atomic.AtomicReference

import android.content.Intent
import com.dspread.mdm.service.broadcast.core.BroadcastSender
import com.dspread.mdm.service.broadcast.handlers.websocket.HeartbeatEventHandlerImpl
import com.dspread.mdm.service.broadcast.handlers.websocket.TaskExecuteEventHandlerImpl
import com.dspread.mdm.service.broadcast.handlers.websocket.TerminalInfoEventHandlerImpl
import com.dspread.mdm.service.broadcast.core.BroadcastActions
import com.dspread.mdm.service.network.websocket.message.WsMessageSender
import com.dspread.mdm.service.network.websocket.task.WsTaskManager

/**
 * WebSocket 连接管理器（内部实现）
 * 基于 nv-websocket-client 库实现 WebSocket 连接管理
 * 
 * 功能特性：
 * - 智能重连机制
 * - 心跳保活
 * - 连接状态管理
 * - 消息发送/接收
 * 
 */
object WsConnectionManager {

    // WebSocket 配置常量（设置）
    private const val FRAME_QUEUE_SIZE = 5
    private const val CONNECT_TIMEOUT = 15000
    private const val PING_INTERVAL = 60000L         // 心跳间隔 60秒
    private const val WHAT_RECONNECT = 1
    
    // 重连策略常量
    private const val MIN_RECONNECT_INTERVAL = 3000L  // 最小重连间隔 3秒
    private const val MAX_RECONNECT_INTERVAL = 15000L // 最大重连间隔 15秒
    private const val TIME_POLICY = 10                // 时间策略：10分钟内断开10次则延长重连间隔
    private const val DELAY_TIME_DEFAULT = 900000L    // 默认延迟时间：15分钟
    
    // WebSocket 实例和状态
    private val wsRef = AtomicReference<WebSocket?>()
    private var status = WebSocketStatus.DISCONNECTED
    private var mHandStop = false  // 手动停止标志（命名）
    private var isWebSocketConnected = false  // 业务层连接状态（收到 hellMsg 才为 true）
    private val isReconnecting = AtomicBoolean(false)  // 防重复重连标志
    
    // 连接参数
    private var context: Context? = null
    private var serviceUrl: String? = null
    private var serviceUrlForReconn: String? = null
    
    // 重连管理
    private var reconnectCount = 0
    private var reconnectTime = MIN_RECONNECT_INTERVAL
    private val disconnectRecords = LinkedList<Long>() // 断开记录队列

    // Handler 用于重连
    private val reconnectHandler = object : Handler(Looper.getMainLooper()) {
        override fun handleMessage(msg: Message) {
            when (msg.what) {
                WHAT_RECONNECT -> {
                    performReconnect()
                }
            }
        }
    }

    /**
     * 初始化 WebSocket 管理器
     */
    fun init(context: Context, url: String, listener: Any?) {
        // 使用 ApplicationContext 避免内存泄漏
        this.context = context.applicationContext
        this.serviceUrl = url
        this.serviceUrlForReconn = url // 重连使用相同URL
        // listener 参数保留兼容性，但不使用

        Logger.wsm("WebSocket 连接管理器初始化完成")
    }

    /**
     * 连接 WebSocket
     */
    fun connect() {
        if (mHandStop) {
            Logger.wsm("手动停止状态，跳过连接")
            return
        }

        try {
            Logger.wsm("开始连接 WebSocket...")

            // 检查是否已有连接存在
            val existingWebSocket = wsRef.get()
            if (existingWebSocket != null && existingWebSocket.isOpen) {
                Logger.wsm("已存在活跃连接，先断开")
                existingWebSocket.disconnect()
            }

            // 构建连接参数
            val connectParams = buildConnectUrl(false)
            val fullUrl = "$serviceUrl?$connectParams"

            Logger.wsm("WebSocket 连接到: $fullUrl")

            // 创建 WebSocket 连接
            val factory = WebSocketFactory()

            // 设置代理
            val proxy = getProxy()
            if (!proxy.isNullOrEmpty()) {
                Logger.wsm("设置 WebSocket 代理: $proxy")
                factory.proxySettings.setServer(proxy)
            }

            val webSocket = factory
                .setConnectionTimeout(CONNECT_TIMEOUT)
                .createSocket(fullUrl)
                .setFrameQueueSize(FRAME_QUEUE_SIZE)
                .setMissingCloseFrameAllowed(false)
                .setPingInterval(PING_INTERVAL)  // 设置60秒心跳间隔
                .addListener(createWebSocketListener())

            wsRef.set(webSocket)
            status = WebSocketStatus.CONNECTING

            // 打印库的默认配置
            printWebSocketDefaults(webSocket)

            // 异步连接
            webSocket.connectAsynchronously()
            Logger.wsm("WebSocket 连接请求已发送")

        } catch (e: Exception) {
            Logger.wsmE("WebSocket 连接失败", e)
            status = WebSocketStatus.CONNECT_FAIL
            // 连接失败时，会在 onError 中触发重连
        }
    }

    /**
     * 重连 WebSocket
     */
    fun reconnect() {
        // 打印调用栈，找出是谁在调用重连
        val stackTrace = Thread.currentThread().stackTrace
        val caller = if (stackTrace.size > 3) {
            "${stackTrace[3].className}.${stackTrace[3].methodName}:${stackTrace[3].lineNumber}"
        } else {
            "未知调用者"
        }
        Logger.wsm("reconnect() 被调用，调用者: $caller")

        if (mHandStop) {
            Logger.wsm("手动停止状态，跳过重连")
            return
        }

        // 防止重复重连
        if (!isReconnecting.compareAndSet(false, true)) {
            Logger.wsm("正在重连中，跳过重复重连请求 (调用者: $caller)")
            return
        }

        Logger.wsm("开始重连 WebSocket... (调用者: $caller)")

        val webSocket = wsRef.get()
        if (webSocket != null && status != WebSocketStatus.CONNECTING) {
            // 断开当前连接
            if (webSocket.isOpen) {
                webSocket.disconnect()
            }

            reconnectCount++
            status = WebSocketStatus.CONNECTING

            // 计算重连延迟（智能策略）
            context?.let { calculateReconnectDelay(it) }

            Logger.wsm("开始第${reconnectCount}次重连，间隔${reconnectTime}ms (${reconnectTime/1000}秒) (调用者: $caller)")

            // 延迟重连
            reconnectHandler.sendEmptyMessageDelayed(WHAT_RECONNECT, reconnectTime)
        } else {
            // 重置重连状态
            isReconnecting.set(false)
            Logger.wsm("重连条件不满足: webSocket=$webSocket, status=$status (调用者: $caller)")
        }
    }

    /**
     * 发送消息
     */
    fun sendMessage(message: String) {
        try {
            val webSocket = wsRef.get()
            if (webSocket != null && webSocket.isOpen) {
                webSocket.sendText(message)
//                Logger.wsm("消息发送成功")
            } else {
//                Logger.wsmE("WebSocket 未连接，消息发送失败")
            }
        } catch (e: Exception) {
            Logger.wsmE("发送消息异常", e)
        }
    }

    /**
     * 断开连接
     */
    fun disconnect() {
        val webSocket = wsRef.get()
        if (webSocket != null) {
            webSocket.disconnect()  // 断开连接
            wsRef.set(null)         // 清空引用
            mHandStop = true        // 设置手动停止标志
        }
        cancelReconnect()
        status = WebSocketStatus.DISCONNECTED
        Logger.wsm("WebSocket 已手动断开")
    }

    /**
     * 释放资源
     */
    fun release() {
        disconnect()
        context = null
        Logger.wsm("WebSocket 连接管理器已释放")
    }

    /**
     * 获取连接状态
     */
    fun getStatus(): WebSocketStatus = status

    /**
     * 是否已连接（物理连接 + 收到 hellMsg）
     */
    fun isConnected(): Boolean {
        val webSocket = wsRef.get()
        return webSocket != null && webSocket.isOpen && isWebSocketConnected
    }

    /**
     * 设置业务连接状态（收到 hellMsg 时调用）
     */
    fun setWebSocketConnected(connected: Boolean) {
        isWebSocketConnected = connected
        if (connected) {
            status = WebSocketStatus.CONNECT_SUCCESS
            Logger.success("WebSocket 业务连接成功！")

            // 收到 hellMsg 时才算真正连接成功，此时重置重连计数
            cancelReconnect()
            Logger.wsm("业务连接成功，重连计数已重置为 0")

            // 连接成功后启动定时器
            try {
                val context = SmartMdmServiceApp.instance

                // 启动定时器（不发送立即广播，避免系统警告）
                startTimersOnConnection(context)

                // 检查自身更新任务状态
                checkSelfUpdateTaskStatus(context)

                // 网络恢复后重发缓存的任务结果
                WsTaskManager.uploadWSTaskResult()

                // 重发缓存的重要消息（如流量统计等）
                WsMessageSender.sendPendingMessages()

            } catch (e: Exception) {
                Logger.wsmE("启动定时器失败", e)
            }
        } else {
            Logger.wsm("WebSocket 业务连接断开")

            // 连接断开时停止任务执行定时器
            try {
                val context = SmartMdmServiceApp.instance
                stopTaskExecutionTimer(context)
                Logger.wsm("已停止任务执行定时器")
            } catch (e: Exception) {
                Logger.wsmE("停止任务执行定时器失败", e)
            }
        }
    }

    /**
     * 检查自身更新任务状态（WebSocket连接成功后调用）
     */
    private fun checkSelfUpdateTaskStatus(context: Context) {
        try {
            Logger.wsm("WebSocket 连接成功，检查自身更新任务状态")

            // 发送广播通知PackageUpdateEventHandler检查状态
            BroadcastSender.sendBroadcast(context, BroadcastActions.CHECK_SELF_UPDATE_STATUS)

        } catch (e: Exception) {
            Logger.wsmE("检查自身更新任务状态失败", e)
        }
    }

    /**
     * 停止任务执行定时器
     */
    private fun stopTaskExecutionTimer(context: Context) {
        try {
            val intent = Intent(BroadcastActions.ACTION_WSTASK_EXEC_BC).apply {
                setPackage(context.packageName)
            }
            val pendingIntent = android.app.PendingIntent.getBroadcast(
                context,
                1001, // 任务执行定时器专用requestCode
                intent,
                android.app.PendingIntent.FLAG_UPDATE_CURRENT or android.app.PendingIntent.FLAG_IMMUTABLE
            )

            val alarmManager = context.getSystemService(Context.ALARM_SERVICE) as android.app.AlarmManager
            alarmManager.cancel(pendingIntent)

        } catch (e: Exception) {
            Logger.wsmE("停止任务执行定时器失败", e)
        }
    }

    // ==================== 私有方法 ====================

    /**
     * 打印 WebSocket 库的默认配置
     */
    private fun printWebSocketDefaults(webSocket: WebSocket) {
        try {
            Logger.wsm("===== WebSocket 库默认配置 =====")

            // 获取 PING 间隔
            val pingInterval = webSocket.pingInterval
            Logger.wsm("默认 PING 间隔: ${pingInterval}ms (${pingInterval/1000}秒)")

            // 获取 PONG 间隔
            val pongInterval = webSocket.pongInterval
            Logger.wsm("默认 PONG 间隔: ${pongInterval}ms (${pongInterval/1000}秒)")

            // 获取帧队列大小
            val frameQueueSize = webSocket.frameQueueSize
            Logger.wsm("帧队列大小: $frameQueueSize")

            // 获取连接超时（通过反射）
            try {
                val socketConnectorField = webSocket.javaClass.getDeclaredField("mSocketConnector")
                socketConnectorField.isAccessible = true
                val socketConnector = socketConnectorField.get(webSocket)

                val timeoutField = socketConnector.javaClass.getDeclaredField("mConnectionTimeout")
                timeoutField.isAccessible = true
                val connectionTimeout = timeoutField.get(socketConnector) as Int
                Logger.wsm("连接超时: ${connectionTimeout}ms (${connectionTimeout/1000}秒)")
            } catch (e: Exception) {
                Logger.wsm("连接超时: 无法获取 (${e.message})")
            }

            // 获取是否允许缺失关闭帧
            val isMissingCloseFrameAllowed = webSocket.isMissingCloseFrameAllowed
            Logger.wsm("允许缺失关闭帧: $isMissingCloseFrameAllowed")

            Logger.wsm("================================")

        } catch (e: Exception) {
            Logger.wsmE("打印 WebSocket 默认配置失败", e)
        }
    }

    /**
     * 打印心跳发送器的实际配置
     */
    private fun printHeartbeatSenderConfig(webSocket: WebSocket) {
        try {
            Logger.wsm("===== 心跳发送器实际配置 =====")

            // 通过反射获取 PingSender 和 PongSender
            try {
                val pingSenderField = webSocket.javaClass.getDeclaredField("mPingSender")
                pingSenderField.isAccessible = true
                val pingSender = pingSenderField.get(webSocket)

                val intervalMethod = pingSender.javaClass.getMethod("getInterval")
                val pingInterval = intervalMethod.invoke(pingSender) as Long
                Logger.wsm("实际 PING 发送间隔: ${pingInterval}ms (${pingInterval/1000}秒)")

                val timerNameMethod = pingSender.javaClass.getMethod("getTimerName")
                val pingTimerName = timerNameMethod.invoke(pingSender) as String?
                Logger.wsm("PING 定时器名称: $pingTimerName")

            } catch (e: Exception) {
                Logger.wsm("PING 发送器配置: 无法获取 (${e.message})")
            }

            try {
                val pongSenderField = webSocket.javaClass.getDeclaredField("mPongSender")
                pongSenderField.isAccessible = true
                val pongSender = pongSenderField.get(webSocket)

                val intervalMethod = pongSender.javaClass.getMethod("getInterval")
                val pongInterval = intervalMethod.invoke(pongSender) as Long
                Logger.wsm("实际 PONG 发送间隔: ${pongInterval}ms (${pongInterval/1000}秒)")

                val timerNameMethod = pongSender.javaClass.getMethod("getTimerName")
                val pongTimerName = timerNameMethod.invoke(pongSender) as String?
                Logger.wsm("PONG 定时器名称: $pongTimerName")

            } catch (e: Exception) {
                Logger.wsm("PONG 发送器配置: 无法获取 (${e.message})")
            }

            Logger.wsm("===============================")

        } catch (e: Exception) {
            Logger.wsmE("打印心跳发送器配置失败", e)
        }
    }

    /**
     * 执行重连
     */
    private fun performReconnect() {
        try {
            // 构建重连参数
            val reconnectParams = buildConnectUrl(true)
            val fullUrl = "$serviceUrlForReconn?$reconnectParams"

            Logger.wsm("重连 WebSocket: $fullUrl")

            // 创建新的 WebSocket 连接
            val factory = WebSocketFactory()

            // 设置代理
            val proxy = getProxy()
            if (!proxy.isNullOrEmpty()) {
                Logger.wsm("重连时设置 WebSocket 代理: $proxy")
                factory.proxySettings.setServer(proxy)
            }

            val webSocket = factory
                .setConnectionTimeout(CONNECT_TIMEOUT)
                .createSocket(fullUrl)
                .setFrameQueueSize(FRAME_QUEUE_SIZE)
                .setMissingCloseFrameAllowed(false)
                .setPingInterval(PING_INTERVAL)  // 设置120秒心跳间隔
                .addListener(createWebSocketListener())

            wsRef.set(webSocket)
            webSocket.connectAsynchronously()

        } catch (e: Exception) {
            Logger.wsmE("重连失败", e)
            // 重置重连状态
            isReconnecting.set(false)
            // 重连失败时，会在 onError 中再次触发重连
        }
    }



    /**
     * 取消重连
     */
    private fun cancelReconnect() {
        reconnectCount = 0
        reconnectHandler.removeMessages(WHAT_RECONNECT)
        isReconnecting.set(false)  // 重置重连状态
    }

    /**
     * 记录断开时间
     */
    private fun recordDisconnectTime() {
        val currentTime = System.currentTimeMillis()
        disconnectRecords.offer(currentTime)
        
        // 保持最多10条记录
        if (disconnectRecords.size > 10) {
            disconnectRecords.poll()
        }
    }

    /**
     * 计算重连延迟
     */
    private fun calculateReconnectDelay(context: Context) {
        // 从Provisioning配置获取重连策略参数
        val reconnectConfig = getReconnectConfig(context)

        // 默认使用最小间隔
        reconnectTime = MIN_RECONNECT_INTERVAL

        // 频繁断开保护机制（根据配置决定是否开启）
        val delaySwitch = reconnectConfig.delaySwitch
        Logger.wsm("当前断开记录数: ${disconnectRecords.size}, 延迟开关: $delaySwitch")

        // 只有在延迟开关开启时才进行频繁断开检测
        if (delaySwitch == "1" && disconnectRecords.size >= 10) {
            val firstDisconnect = disconnectRecords.first
            val lastDisconnect = disconnectRecords.last
            val timeDiff = lastDisconnect - firstDisconnect
            val timeDiffMinutes = timeDiff / (60 * 1000)

            // 使用配置的延迟策略和延迟时间
            val delayPolicy = reconnectConfig.delayPolicy.toLongOrNull() ?: TIME_POLICY.toLong()
            val delayTime = reconnectConfig.delayTime.toLongOrNull() ?: (DELAY_TIME_DEFAULT / 1000)

            Logger.wsm("断开记录 最早一次断开: ${java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(java.util.Date(firstDisconnect))}")
            Logger.wsm("断开记录 最后一次断开: ${java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(java.util.Date(lastDisconnect))}")
            Logger.wsm("时间差: ${timeDiffMinutes}分钟 (配置阈值: ${delayPolicy}分钟)")

            // 如果在指定时间内断开10次，则延长重连间隔
            if (timeDiff < delayPolicy * 60 * 1000) {
                reconnectTime = delayTime * 1000  // 转换为毫秒
                reconnectCount = 0
                Logger.wsm("检测到频繁断开！${delayPolicy}分钟内断开${disconnectRecords.size}次，延长重连间隔至 ${reconnectTime / 1000} 秒 (${reconnectTime / 60000} 分钟)")

                // 清空断开记录，避免一直延长
                disconnectRecords.clear()
            } else {
                Logger.wsm("断开间隔正常，使用标准重连策略")
                calculateNormalReconnectTime()
            }
        } else {
            if (delaySwitch != "1") {
                Logger.wsm("重连延迟开关已关闭，使用标准重连策略")
            }
            calculateNormalReconnectTime()
        }

        Logger.wsm("重连策略计算完成: 第${reconnectCount}次重连，间隔${reconnectTime}ms (${reconnectTime/1000}秒)")
    }

    /**
     * 获取重连配置（从Provisioning配置中读取wssreconn参数）
     */
    private fun getReconnectConfig(context: Context): ReconnectConfig {
        return try {
            val provisioningManager = ProvisioningManager.getInstance(context)
            val config = provisioningManager.getCurrentConfig()

            if (config != null) {
                val wssReconn = config.polling.wssReconnConfig
                Logger.wsm("使用Provisioning配置的重连策略: delayPolicy=${wssReconn.delayPolicy}, delaySwitch=${wssReconn.delaySwitch}, delayTime=${wssReconn.delayTime}")
                return ReconnectConfig(
                    delayPolicy = wssReconn.delayPolicy,
                    delaySwitch = wssReconn.delaySwitch,
                    delayTime = wssReconn.delayTime
                )
            }

            // 使用默认配置
            Logger.wsm("使用默认重连策略")
            ReconnectConfig()

        } catch (e: Exception) {
            Logger.wsmE("获取重连配置失败，使用默认值", e)
            ReconnectConfig()
        }
    }

    /**
     * 重连配置数据类
     */
    private data class ReconnectConfig(
        val delayPolicy: String = TIME_POLICY.toString(),  // 默认10分钟
        val delaySwitch: String = "1",                     // 默认开启
        val delayTime: String = (DELAY_TIME_DEFAULT / 1000).toString()  // 默认15分钟（转换为秒）
    )

    /**
     * 计算正常重连时间
     */
    private fun calculateNormalReconnectTime() {
        if (reconnectCount > 3) {
            val temp = MIN_RECONNECT_INTERVAL * (reconnectCount - 2)
            reconnectTime = if (temp > MAX_RECONNECT_INTERVAL) MAX_RECONNECT_INTERVAL else temp
        }

        // 防止重连次数溢出
        if (reconnectCount >= Int.MAX_VALUE) {
            reconnectCount = 0
        }
    }

    /**
     * 构建连接 URL 参数
     */
    private fun buildConnectUrl(isReconnect: Boolean): String {
        // 逻辑获取设备序列号
        val serialNo = DeviceInfoApi.getSerialNumber(context!!) ?: "unknown"
        val publicKey = WsKeyManager.getClientPublicKeyString()
        val query = if (isReconnect) "0" else "1"
        val msgVer = "3"
        val timestamp = System.currentTimeMillis()

        // Base64 编码（不加密）
        val encodedSerialNo = android.util.Base64.encodeToString(serialNo.toByteArray(), android.util.Base64.NO_WRAP)
        val encodedPublicKey = android.util.Base64.encodeToString(publicKey.toByteArray(), android.util.Base64.NO_WRAP)

        // 构建连接参数（完全格式）
        val connectParams = "serialNo=$encodedSerialNo&publicKey=$encodedPublicKey&query=$query&msgVer=$msgVer&timestamp=$timestamp"

        // 添加签名
        val signature = WsKeyManager.signData(connectParams)
        val finalParams = "$connectParams&signature=$signature"

        return finalParams
    }

    /**
     * 获取代理设置
     */
    private fun getProxy(): String? {
        val proxyAddress = System.getProperty("http.proxyHost")
        val portStr = System.getProperty("http.proxyPort")

        return if (proxyAddress.isNullOrEmpty() || portStr.isNullOrEmpty()) {
            Logger.wsm("未检测到系统代理设置")
            null
        } else {
            val proxy = "http://$proxyAddress:$portStr"
            Logger.wsm("检测到系统代理: $proxy")
            proxy
        }
    }

    /**
     * 创建 WebSocket 监听器
     */
    private fun createWebSocketListener(): WebSocketAdapter {
        return object : WebSocketAdapter() {
            override fun onConnected(websocket: WebSocket, headers: Map<String, List<String>>) {
                Logger.success("WebSocket 物理连接成功，等待服务器 hellMsg")

                // 打印心跳发送器的实际配置
                printHeartbeatSenderConfig(websocket)

                // 物理连接成功后，状态仍为 CONNECTING，等待 hellMsg
                status = WebSocketStatus.CONNECTING

                // 重置重连状态（物理连接成功）
                isReconnecting.set(false)

                // 注意：逻辑，物理连接成功时不重置重连计数，要等收到 hellMsg 才重置
                // 注意：逻辑，连接成功时不重置 mHandStop
            }

            override fun onConnectError(websocket: WebSocket, exception: WebSocketException) {
                Logger.wsmE("WebSocket 连接错误", exception)
                status = WebSocketStatus.CONNECT_FAIL

                // 重置重连状态（连接失败）
                isReconnecting.set(false)

                // 连接错误时自动重连
                if (!mHandStop) {
                    reconnect()
                }
            }

            override fun onTextMessage(websocket: WebSocket, text: String) {
                // 处理心跳响应
                if (text.trim() == "2") {
                    Logger.wsm("收到服务器心跳响应: 2")
                    return
                }

                // 调试：检查是否包含hellMsg
                if (text.contains("hello", ignoreCase = true) || text.contains("hellMsg", ignoreCase = true)) {
                    Logger.wsm("*** 检测到可能的hellMsg消息 *** : ${text.take(100)}")
                }

//                Logger.wsm("收到WebSocket消息: $text")

                // 通过 WsMessageProcessor 处理消息（解压缩/解密等预处理）
                try {
                    // 获取全局的 WsMessageProcessor 实例
                    WebSocketCenter.getMessageProcessor()?.onMessageResponse(text)
                } catch (e: Exception) {
                    Logger.wsmE("处理WebSocket消息失败", e)
                }
            }

            override fun onDisconnected(websocket: WebSocket, serverCloseFrame: WebSocketFrame?, clientCloseFrame: WebSocketFrame?, closedByServer: Boolean) {
                // 详细的断开日志
                val disconnectReason = if (closedByServer) "服务器主动断开" else "客户端主动断开"
                val serverCloseCode = serverCloseFrame?.closeCode ?: -1
                val serverCloseReason = serverCloseFrame?.closeReason ?: "无原因"
                val clientCloseCode = clientCloseFrame?.closeCode ?: -1
                val clientCloseReason = clientCloseFrame?.closeReason ?: "无原因"

                Logger.wsm("WebSocket 连接断开 - $disconnectReason")
                Logger.wsm("   服务器关闭帧: code=$serverCloseCode, reason=$serverCloseReason")
                Logger.wsm("   客户端关闭帧: code=$clientCloseCode, reason=$clientCloseReason")
                Logger.wsm("   断开发起方: ${if (closedByServer) "服务器" else "客户端"}")

                // 记录断开时间
                recordDisconnectTime()

                // 重置业务连接状态
                isWebSocketConnected = false
                status = WebSocketStatus.CONNECT_FAIL  // 设置为 CONNECT_FAIL

                // 重置重连状态（连接断开）
                isReconnecting.set(false)

                // 自动重连
                if (!mHandStop) {
                    reconnect()  // 直接重连，不延迟
                }
            }

            override fun onPingFrame(websocket: WebSocket?, frame: WebSocketFrame?) {
                super.onPingFrame(websocket, frame)
                val payload = frame?.payload?.let {
                    if (it.isNotEmpty()) String(it, Charsets.UTF_8) else "空载荷"
                } ?: "无载荷"
                Logger.wsm("收到服务器 PING 帧: $payload")
            }

            override fun onPongFrame(websocket: WebSocket?, frame: WebSocketFrame?) {
                super.onPongFrame(websocket, frame)
                val payload = frame?.payload?.let {
                    if (it.isNotEmpty()) String(it, Charsets.UTF_8) else "空载荷"
                } ?: "无载荷"
                Logger.wsm("收到服务器 PONG 帧: $payload")
            }

            override fun onSendingFrame(websocket: WebSocket?, frame: WebSocketFrame?) {
                super.onSendingFrame(websocket, frame)
                when (frame?.opcode) {
                    WebSocketOpcode.PING -> {
                        val payload = frame.payload?.let {
                            if (it.isNotEmpty()) String(it, Charsets.UTF_8) else "空载荷"
                        } ?: "无载荷"
                        Logger.wsm("发送客户端 PING 帧: $payload")
                    }
                    WebSocketOpcode.PONG -> {
                        val payload = frame.payload?.let {
                            if (it.isNotEmpty()) String(it, Charsets.UTF_8) else "空载荷"
                        } ?: "无载荷"
                        Logger.wsm("发送客户端 PONG 帧: $payload")
                    }
                }
            }

            override fun onError(websocket: WebSocket, cause: WebSocketException) {
                Logger.wsmE("WebSocket 连接错误", cause)
                status = WebSocketStatus.CONNECT_FAIL

                // 注意：不在 onError 中重连，避免与 onDisconnected 重复重连
                // 重连逻辑统一在 onDisconnected 中处理
                Logger.wsm("onError: 不触发重连，等待 onDisconnected 处理")
            }
        }
    }

    /**
     * 启动连接成功后的定时器
     * 直接调用Handler方法，避免发送广播导致的系统警告
     */
    private fun startTimersOnConnection(context: Context) {
        try {
            // 启动任务执行定时器
            val taskHandler = TaskExecuteEventHandlerImpl()
            taskHandler.scheduleNextTaskExecution(context)
            Logger.wsm("已启动任务执行定时器")

            // 启动心跳定时器
            val heartbeatHandler = HeartbeatEventHandlerImpl()
            heartbeatHandler.scheduleNextHeartbeat(context)
            Logger.wsm("已启动心跳定时器")

            // 启动终端信息上传定时器
            val terminalInfoHandler = TerminalInfoEventHandlerImpl()
            terminalInfoHandler.scheduleNextUpload(context)
            Logger.wsm("已启动终端信息上传定时器")

        } catch (e: Exception) {
            Logger.wsmE("启动连接定时器失败", e)
        }
    }
}
