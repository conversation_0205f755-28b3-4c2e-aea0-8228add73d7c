package com.dspread.mdm.service.modules.rulebase.monitor

import android.content.Context
import com.dspread.mdm.service.constants.TaskStateConstants
import com.dspread.mdm.service.modules.rulebase.core.RuleStateMachine
import com.dspread.mdm.service.modules.rulebase.core.RuleStateMachine.RuleState
import com.dspread.mdm.service.modules.rulebase.core.RuleStateMachine.StateTransitionEvent
import com.dspread.mdm.service.modules.rulebase.core.RuleStateMachine.RuleStateChangeListener
import com.dspread.mdm.service.modules.rulebase.engine.RuleExecutionEngine
import com.dspread.mdm.service.utils.log.Logger
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.atomic.AtomicBoolean
import java.util.concurrent.atomic.AtomicLong

/**
 * 规则状态监控器 - 简化版本
 * 负责监控规则状态变化并上报到服务器
 */
class RuleStateMonitor private constructor(private val context: Context) : RuleStateChangeListener {
    
    companion object {
        private const val TAG = "RuleStateMonitor"
        
        @Volatile
        private var instance: RuleStateMonitor? = null
        
        fun getInstance(context: Context): RuleStateMonitor {
            return instance ?: synchronized(this) {
                instance ?: RuleStateMonitor(context.applicationContext).also { instance = it }
            }
        }
    }
    
    // 状态机引用
    private val stateMachine = RuleStateMachine.getInstance()
    
    // 执行引擎引用
    private val executionEngine = RuleExecutionEngine.getInstance(context)
    
    // 初始化状态
    private val isInitialized = AtomicBoolean(false)
    
    // 监控统计
    private val monitorStats = MonitorStats()
    
    // 状态报告队列
    private val reportQueue = ConcurrentHashMap<String, StateReport>()
    
    /**
     * 状态报告
     */
    data class StateReport(
        val ruleId: String,
        val state: RuleState,
        val timestamp: Long = System.currentTimeMillis(),
        val metadata: Map<String, Any> = emptyMap()
    )
    
    /**
     * 监控统计
     */
    data class MonitorStats(
        var totalRules: Int = 0,
        var activeRules: Int = 0,
        var totalReports: Long = 0,
        var queueSize: Int = 0
    ) {
        fun incrementReports() {
            totalReports++
        }
        
        fun updateQueueSize(size: Int) {
            queueSize = size
        }
    }
    
    /**
     * 初始化监控器
     */
    fun initialize() {
        if (isInitialized.compareAndSet(false, true)) {
            try {
                // 注册状态变化监听器
                stateMachine.addStateChangeListener(this)
                
                Logger.rule("$TAG 状态监控器初始化完成")
                
            } catch (e: Exception) {
                Logger.ruleE("$TAG 状态监控器初始化失败", e)
                isInitialized.set(false)
                throw e
            }
        }
    }
    
    /**
     * 状态变化监听器实现
     */
    override fun onStateChanged(event: StateTransitionEvent) {
        try {
            Logger.rule("$TAG 收到状态变化: ${event.ruleId}, ${event.fromState.code} -> ${event.toState.code}")
            
            // 创建状态报告
            val report = StateReport(
                ruleId = event.ruleId,
                state = event.toState,
                timestamp = event.timestamp
            )
            
            // 添加到报告队列（仅用于记录，不主动上报）
            reportQueue[event.ruleId] = report
            
            // 更新统计
            monitorStats.incrementReports()
            monitorStats.updateQueueSize(reportQueue.size)
            
            // 不再主动上报，让执行引擎负责上报
            Logger.rule("$TAG 状态变化已记录: ${event.ruleId}, ${event.toState.code}")
            
        } catch (e: Exception) {
            Logger.ruleE("$TAG 处理状态变化失败: ${event.ruleId}", e)
        }
    }
    
    override fun onStateTransitionFailed(ruleId: String, fromState: RuleState, toState: RuleState, reason: String) {
        Logger.ruleE("$TAG 状态转换失败: $ruleId, ${fromState.code} -> ${toState.code}, 原因: $reason")
    }
    
    /**
     * 获取监控统计信息
     */
    fun getMonitorStats(): MonitorStats {
        return monitorStats.copy()
    }
    
    /**
     * 强制上报所有状态
     */
    fun forceReportAll() {
        try {
            Logger.rule("$TAG 强制上报所有状态...")
            
            reportQueue.values.forEach { report ->
                // 这里不再主动上报，而是让执行引擎负责
                reportStateImmediately(report)
            }
            
            reportQueue.clear()
            monitorStats.updateQueueSize(0)
            
            Logger.rule("$TAG 强制上报完成")
            
        } catch (e: Exception) {
            Logger.ruleE("$TAG 强制上报失败", e)
        }
    }
    
    /**
     * 清理监控器
     */
    fun cleanup() {
        try {
            Logger.rule("$TAG 监控器清理完成")
            
            // 移除状态变化监听器
            stateMachine.removeStateChangeListener(this)
            
            // 清理报告队列
            reportQueue.clear()
            
        } catch (e: Exception) {
            Logger.ruleE("$TAG 监控器清理失败", e)
        }
    }
    
    // ==================== 私有方法 ====================
    
    /**
     * 判断是否为重要状态
     */
    private fun isImportantState(state: RuleState): Boolean {
        return when (state.code) {
            // 完成状态
            TaskStateConstants.RULEBASED_SUCCESS,
            TaskStateConstants.FAILED,
            TaskStateConstants.SERVER_FAILED,
            TaskStateConstants.RULEBASED_EXPIRED -> true
            
            // 执行状态也需要立即上报
            TaskStateConstants.RULEBASED_EXECUTING -> true
            
            // 其他状态延迟上报
            else -> false
        }
    }
    
    /**
     * 立即上报状态
     */
    private fun reportStateImmediately(report: StateReport) {
        try {
            Logger.rule("$TAG 立即上报状态: ${report.ruleId}, ${report.state.code}")
            
            // 通过执行引擎上报状态
            executionEngine.reportRuleState(report.ruleId, report.state.code)
            
            // 从队列中移除已上报的报告
            reportQueue.remove(report.ruleId)
            monitorStats.updateQueueSize(reportQueue.size)
            
        } catch (e: Exception) {
            Logger.ruleE("$TAG 状态上报失败: ${report.ruleId}", e)
        }
    }
    
    /**
     * 安排延迟上报
     */
    private fun scheduleDelayedReport(report: StateReport) {
        try {
            Logger.rule("$TAG 安排延迟上报: ${report.ruleId}, ${report.state.code}")
            
            // 这里可以添加延迟上报的逻辑
            // 比如使用Handler延迟执行
            
        } catch (e: Exception) {
            Logger.ruleE("$TAG 安排延迟上报失败: ${report.ruleId}", e)
        }
    }
} 