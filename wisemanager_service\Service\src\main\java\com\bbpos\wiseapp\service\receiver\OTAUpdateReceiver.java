package com.bbpos.wiseapp.service.receiver;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.os.Handler;
import android.text.TextUtils;

import com.bbpos.wiseapp.logger.BBLog;
import com.bbpos.wiseapp.provisioning.ProvisionService;
import com.bbpos.wiseapp.service.common.UsualData;
import com.bbpos.wiseapp.system.api.DeviceInfoApi;
import com.bbpos.wiseapp.system.api.SystemApi;
import com.bbpos.wiseapp.tms.adapter.SystemManagerAdapter;
import com.bbpos.wiseapp.tms.utils.Constants;
import com.bbpos.wiseapp.tms.utils.ContextUtil;
import com.bbpos.wiseapp.tms.utils.ParameterName;
import com.bbpos.wiseapp.utils.ActivityUtils;
import com.bbpos.wiseapp.utils.FileUtils;
import com.bbpos.wiseapp.utils.SharedPreferencesUtils;
import com.bbpos.wiseapp.websocket.OTANotifyService;
import com.bbpos.wiseapp.websocket.WebSocketSender;

import org.json.JSONObject;

import java.util.List;
import java.util.Locale;

/**
 * 处理 ota 、sp 、tmt 升级业务
 */
public class OTAUpdateReceiver extends BroadcastReceiver {
    private Context mContext;

    @Override
    public void onReceive(Context context, Intent intent) {
        this.mContext = context;
        String action = intent.getAction();
        BBLog.d(BBLog.TAG, "OTAUpdateReceiver Broadcast action:" + action);
        if(Intent.ACTION_BOOT_COMPLETED.equals(action)) {
            //判断是否需要执行ota升级
            SharedPreferencesUtils.init(ContextUtil.getInstance());
            if (DeviceInfoApi.getIntance().isWisePosPro()) {
				if (Constants.B_UNBOX_RUNNING) {
					BBLog.i(BBLog.TAG, "OTAUpdateReceiver 终端正在做UNBOX，返回. unboxf_running = "+ Constants.B_UNBOX_RUNNING);
					return;
				}

				boolean todoOTAUpdate = SharedPreferencesUtils.getSharePreferencesValue(UsualData.SHARED_PREFERENCES_NEED_WISEPOSPRO_OTA_FLAG, false);
                BBLog.d(BBLog.TAG, "OTAUpdateReceiver: todoOTAUpdate--->" + todoOTAUpdate);
                if (todoOTAUpdate) {
					//判斷是否需要同時進行wisecube、sp ota
					boolean isWcOtaExist = SharedPreferencesUtils.getSharePreferencesValue(Constants.FLAG_WISECUBE_FW_UPGRADE_EXIST, false);
					boolean isSpOtaExist = SharedPreferencesUtils.getSharePreferencesValue(Constants.FLAG_SP_UPGRADE_EXIST, false);
					BBLog.v(Constants.TAG, " OTAUpdateReceiver isWcOtaExist = "+isWcOtaExist + ", isSpOtaExist = "+ isSpOtaExist);
					if (isWcOtaExist) {//wc ota 優先級高於sp ota
						//检查ota工具是否已安装
						if (isInstallApp(context, UsualData.SYSTEM_OTA_TRIGGER_APP_PACKAGENAME)) {
							if (Constants.isOTARunning) {
								BBLog.e(Constants.TAG, "OTAUpdateReceiver: 當前正在執行OTA/SP Upgrade,本次升級任務丟棄");
								return;
							}
							BBLog.v(Constants.TAG, " OTAUpdateReceiver going to process ota upgrade. delay 30 secs");
							//提前设置update标识位
							Constants.isOTARunning = true;
							//此處延時5s執行，因為終端重啟時，可能需要加載客戶Launcher，待客戶Launcher加載完畢，再啟動
							new Handler().postDelayed(new Runnable() {
								@Override
								public void run() {
									Intent otaIntent = mContext.getPackageManager().getLaunchIntentForPackage(UsualData.SYSTEM_OTA_TRIGGER_APP_PACKAGENAME);
									otaIntent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
									mContext.startActivity(otaIntent);
									if (!isSpOtaExist) {
										SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_NEED_WISEPOSPRO_OTA_FLAG, false);
									}
									SharedPreferencesUtils.setSharePreferencesValue(Constants.FLAG_WISECUBE_FW_UPGRADE_EXIST, false);
								}
							}, 10 * 1000);
						} else {
							BBLog.e(Constants.TAG, "OTAUpdateReceiver: 檢查到 WiseCube_OTA 未安裝成功,本次ota升級失敗");
							Constants.isOTARunning = false;
							SharedPreferencesUtils.setSharePreferencesValue(Constants.FLAG_WISECUBE_FW_UPGRADE_EXIST, false);
							if (isSpOtaExist){ //再判斷是否需要進行sp更新
								BBLog.e(Constants.TAG, "OTAUpdateReceiver: 检测到需要进行sp 升级");
								if (isInstallApp(context, UsualData.SYSTEM_SP_OTA_TRIGGER_APP_PACKAGENAME)) {
									if (Constants.isOTARunning) {
										BBLog.e(Constants.TAG, "OTAUpdateReceiver: 當前正在執行OTA/SP Upgrade,本次升級任務丟棄");
										return;
									}
									BBLog.v(Constants.TAG, " OTAUpdateReceiver going to process ota upgrade. delay 30 secs");
									//提前设置update标识位
									Constants.isOTARunning = true;
									//此處延時5s執行，因為終端重啟時，可能需要加載客戶Launcher，待客戶Launcher加載完畢，再啟動
									new Handler().postDelayed(new Runnable() {
										@Override
										public void run() {
											Intent otaIntent = mContext.getPackageManager().getLaunchIntentForPackage(UsualData.SYSTEM_SP_OTA_TRIGGER_APP_PACKAGENAME);
											otaIntent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
											mContext.startActivity(otaIntent);
											if (!isWcOtaExist) {
												SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_NEED_WISEPOSPRO_OTA_FLAG, false);
											}
											SharedPreferencesUtils.setSharePreferencesValue(Constants.FLAG_SP_UPGRADE_EXIST, false);
										}
									}, 10 * 1000);
								}
							}
						}
					}else {
						if (isSpOtaExist){
							if (isWcOtaExist){
								if (isInstallApp(context, UsualData.SYSTEM_OTA_TRIGGER_APP_PACKAGENAME)) {
									if (Constants.isOTARunning) {
										BBLog.e(Constants.TAG, "OTAUpdateReceiver: 當前正在執行OTA/SP Upgrade,本次升級任務丟棄");
										return;
									}
									BBLog.v(Constants.TAG, " OTAUpdateReceiver going to process ota upgrade. delay 30 secs");
									//提前设置update标识位
									Constants.isOTARunning = true;
									//此處延時5s執行，因為終端重啟時，可能需要加載客戶Launcher，待客戶Launcher加載完畢，再啟動
									new Handler().postDelayed(new Runnable() {
										@Override
										public void run() {
											Intent otaIntent = mContext.getPackageManager().getLaunchIntentForPackage(UsualData.SYSTEM_OTA_TRIGGER_APP_PACKAGENAME);
											otaIntent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
											mContext.startActivity(otaIntent);
											if (!isSpOtaExist) {
												SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_NEED_WISEPOSPRO_OTA_FLAG, false);
											}
											SharedPreferencesUtils.setSharePreferencesValue(Constants.FLAG_WISECUBE_FW_UPGRADE_EXIST, false);
										}
									}, 10 * 1000);
								}else {
									BBLog.e(Constants.TAG, "OTAUpdateReceiver: 檢查到 WiseCube_OTA 未安裝成功,本次ota升級失敗");
									Constants.isOTARunning = false;
									SharedPreferencesUtils.setSharePreferencesValue(Constants.FLAG_WISECUBE_FW_UPGRADE_EXIST, false);
									if (isSpOtaExist){ //再判斷是否需要進行sp更新
										BBLog.e(Constants.TAG, "OTAUpdateReceiver: 检测到需要进行sp 升级");
										if (isInstallApp(context, UsualData.SYSTEM_SP_OTA_TRIGGER_APP_PACKAGENAME)) {
											if (Constants.isOTARunning) {
												BBLog.e(Constants.TAG, "OTAUpdateReceiver: 當前正在執行OTA/SP Upgrade,本次升級任務丟棄");
												return;
											}
											BBLog.v(Constants.TAG, " OTAUpdateReceiver going to process ota upgrade. delay 30 secs");
											//提前设置update标识位
											Constants.isOTARunning = true;
											//此處延時5s執行，因為終端重啟時，可能需要加載客戶Launcher，待客戶Launcher加載完畢，再啟動
											new Handler().postDelayed(new Runnable() {
												@Override
												public void run() {
													Intent otaIntent = mContext.getPackageManager().getLaunchIntentForPackage(UsualData.SYSTEM_SP_OTA_TRIGGER_APP_PACKAGENAME);
													otaIntent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
													mContext.startActivity(otaIntent);
													if (!isWcOtaExist) {
														SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_NEED_WISEPOSPRO_OTA_FLAG, false);
													}
													SharedPreferencesUtils.setSharePreferencesValue(Constants.FLAG_SP_UPGRADE_EXIST, false);
												}
											}, 10 * 1000);
										}
									}
								}
							}else {
								if (isInstallApp(context, UsualData.SYSTEM_SP_OTA_TRIGGER_APP_PACKAGENAME)) {
									if (Constants.isOTARunning) {
										BBLog.e(Constants.TAG, "OTAUpdateReceiver: 當前正在執行OTA/SP Upgrade,本次升級任務丟棄");
										return;
									}
									BBLog.v(Constants.TAG, " OTAUpdateReceiver going to process ota upgrade. delay 30 secs");
									//提前设置update标识位
									Constants.isOTARunning = true;
									//此處延時5s執行，因為終端重啟時，可能需要加載客戶Launcher，待客戶Launcher加載完畢，再啟動
									new Handler().postDelayed(new Runnable() {
										@Override
										public void run() {
											Intent otaIntent = mContext.getPackageManager().getLaunchIntentForPackage(UsualData.SYSTEM_SP_OTA_TRIGGER_APP_PACKAGENAME);
											otaIntent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
											mContext.startActivity(otaIntent);
											if (!isWcOtaExist) {
												SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_NEED_WISEPOSPRO_OTA_FLAG, false);
											}
											SharedPreferencesUtils.setSharePreferencesValue(Constants.FLAG_SP_UPGRADE_EXIST, false);
										}
									}, 10 * 1000);
								}else {
									BBLog.e(Constants.TAG, "OTAUpdateReceiver: 檢查到 SP_Update 未安裝成功,本次ota升級失敗");
								}
							}
						}
					}
				}
            }else if (DeviceInfoApi.getIntance().isWisePos4G()) {
                boolean todoOTAUpdate = SharedPreferencesUtils.getSharePreferencesValue(UsualData.SHARED_PREFERENCES_NEED_WISEPOS4G_OTA_FLAG, false);
                BBLog.d(BBLog.TAG, "OTAUpdateReceiver: todoOTAUpdate--->" + todoOTAUpdate);
                if (todoOTAUpdate) {
                    //检查ota工具是否已安装
                    if (isInstallApp(context, UsualData.SYSTEM_WISEPOS_OTA_TRIGGER_APP_PACKAGENAME)) {
                        if (Constants.isOTARunning){
                            BBLog.e(Constants.TAG, "OTAUpdateReceiver: 當前正在執行OTA/SP Upgrade,本次升級任務丟棄");
                            return;
                        }
                        BBLog.v(Constants.TAG, " OTAUpdateReceiver going to process ota upgrade. delay 30 secs");
                        Constants.isOTARunning = true;
                        //此處延時5s執行，因為終端重啟時，可能需要加載客戶Launcher，待客戶Launcher加載完畢，再啟動
                        new Handler().postDelayed(new Runnable() {
                            @Override
                            public void run() {
                                Intent otaIntent = mContext.getPackageManager().getLaunchIntentForPackage(UsualData.SYSTEM_WISEPOS_OTA_TRIGGER_APP_PACKAGENAME);
                                otaIntent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                                mContext.startActivity(otaIntent);
                                SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_NEED_WISEPOS4G_OTA_FLAG, false);
                            }
                        }, 10 * 1000);
                    } else {
                        BBLog.e(Constants.TAG, "OTAUpdateReceiver: 檢查到 ota 工具未安裝成功,本次ota升級失敗");
                    }
                }
            }else if (DeviceInfoApi.getIntance().isWisePos5Plus() || DeviceInfoApi.getIntance().isWisePos5() || DeviceInfoApi.getIntance().isWisePosTouch() || DeviceInfoApi.getIntance().isWisePosTouchPlus()){
				//mark: 同Banny、Derek討論，TMT、Keyupdate可以不需要進行reboot。

                boolean wisePos5OtaFlag = SharedPreferencesUtils.getSharePreferencesValue(UsualData.SHARED_PREFERENCES_NEED_WISEPOS5_OTA_FLAG, false);
                BBLog.d(BBLog.TAG, "OTAUpdateReceiver: wisePos5OtaFlag--->" + wisePos5OtaFlag);
                if (wisePos5OtaFlag){
					boolean isTmtOtaExist = SharedPreferencesUtils.getSharePreferencesValue(Constants.FLAG_TMT_UPGRADE_EXIST, false);
					boolean isKeyOtaExist = SharedPreferencesUtils.getSharePreferencesValue(Constants.FLAG_KEY_UPGRADE_EXIST, false);
					BBLog.v(Constants.TAG, " OTAUpdateReceiver wisePos5OtaFlag = "+isTmtOtaExist + ", isKeyOtaExist = "+ isKeyOtaExist);
					if (isTmtOtaExist) {
						//检查tmt工具是否已安装
						if (isInstallApp(context, UsualData.SYSTEM_TMT_OTA_TRIGGER_APP_PACKAGENAME)) {
							if (Constants.isOTARunning) {
								BBLog.e(Constants.TAG, "OTAUpdateReceiver: 當前正在執行 TMT/KEY Upgrade,本次升級任務丟棄");
								return;
							}
							BBLog.v(Constants.TAG, " OTAUpdateReceiver going to process tmt upgrade. delay 10 secs");
							Constants.isOTARunning = true;

							new Handler().postDelayed(new Runnable() {
								@Override
								public void run() {
									Intent intent = mContext.getPackageManager().getLaunchIntentForPackage(UsualData.SYSTEM_TMT_OTA_TRIGGER_APP_PACKAGENAME);
									intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
									intent.putExtra(ParameterName.TMT_URL, SharedPreferencesUtils.getSharePreferencesValue(UsualData.SHARED_PREFERENCES_TMT_SERVER_URL, UsualData.WISEPOS_PULS_TMT_URL_DEFAULT));
									mContext.startActivity(intent);
									if (!isKeyOtaExist)
										SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_NEED_WISEPOS5_OTA_FLAG, false);
									SharedPreferencesUtils.setSharePreferencesValue(Constants.FLAG_TMT_UPGRADE_EXIST, false);
								}
							}, 10 * 1000);
						} else {
							BBLog.e(Constants.TAG, "OTAUpdateReceiver: 檢查到 TMT 工具未安裝成功,本次 TMT 升級失敗");
							Constants.isOTARunning = false;
							SharedPreferencesUtils.setSharePreferencesValue(Constants.FLAG_TMT_UPGRADE_EXIST, false);
							if (isKeyOtaExist){ //再判斷是否需要進行 key profile更新
								BBLog.e(Constants.TAG, "OTAUpdateReceiver: 检测到需要进行 KEY 升级");
								if (isInstallApp(context, UsualData.SYSTEM_KEY_UPDATE_TRIGGER_APP_PACKAGENAME)) {
									if (Constants.isOTARunning) {
										BBLog.e(Constants.TAG, "OTAUpdateReceiver: 當前正在執行 TMT/KEY Upgrade,本次升級任務丟棄");
										return;
									}
									BBLog.v(Constants.TAG, " OTAUpdateReceiver going to process ota upgrade. delay 10 secs");
									//提前设置update标识位
									Constants.isOTARunning = true;
									new Handler().postDelayed(new Runnable() {
										@Override
										public void run() {
											Intent otaIntent = mContext.getPackageManager().getLaunchIntentForPackage(UsualData.SYSTEM_KEY_UPDATE_TRIGGER_APP_PACKAGENAME);
											otaIntent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
											mContext.startActivity(otaIntent);

											if (!SharedPreferencesUtils.getSharePreferencesValue(Constants.FLAG_TMT_UPGRADE_EXIST, false)) {
												SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_NEED_WISEPOS5_OTA_FLAG, false);
											}
											SharedPreferencesUtils.setSharePreferencesValue(Constants.FLAG_KEY_UPGRADE_EXIST, false);
										}
									}, 10 * 1000);
								}
							}
						}
					}else if (isKeyOtaExist) {
						//检查 KEY PROFILE 工具是否已安装
						if (isInstallApp(context, UsualData.SYSTEM_KEY_UPDATE_TRIGGER_APP_PACKAGENAME)) {
							if (Constants.isOTARunning) {
								BBLog.e(Constants.TAG, "OTAUpdateReceiver: 當前正在執行 TMT/KEY Upgrade,本次升級任務丟棄");
								return;
							}
							BBLog.v(Constants.TAG, " OTAUpdateReceiver going to process key upgrade. delay 10 secs");
							Constants.isOTARunning = true;

							new Handler().postDelayed(new Runnable() {
								@Override
								public void run() {
									Intent intent = mContext.getPackageManager().getLaunchIntentForPackage(UsualData.SYSTEM_KEY_UPDATE_TRIGGER_APP_PACKAGENAME);
									intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
									mContext.startActivity(intent);

									if (!SharedPreferencesUtils.getSharePreferencesValue(Constants.FLAG_TMT_UPGRADE_EXIST, false))
										SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_NEED_WISEPOS5_OTA_FLAG, false);
									SharedPreferencesUtils.setSharePreferencesValue(Constants.FLAG_KEY_UPGRADE_EXIST, false);
								}
							}, 10 * 1000);
						}else {
							BBLog.e(Constants.TAG, "OTAUpdateReceiver: 檢查到 KeyProfile 工具未安裝成功,本次 KeyProfile 升級失敗");
							Constants.isOTARunning = false;
							SharedPreferencesUtils.setSharePreferencesValue(Constants.FLAG_KEY_UPGRADE_EXIST, false);
							if (isTmtOtaExist) {
								if (isInstallApp(context, UsualData.SYSTEM_TMT_OTA_TRIGGER_APP_PACKAGENAME)) {
									if (Constants.isOTARunning) {
										BBLog.e(Constants.TAG, "OTAUpdateReceiver: 當前正在執行 TMT/KEY Upgrade,本次升級任務丟棄");
										return;
									}
									BBLog.v(Constants.TAG, " OTAUpdateReceiver going to process tmt upgrade. delay 10 secs");
									Constants.isOTARunning = true;

									new Handler().postDelayed(new Runnable() {
										@Override
										public void run() {
											Intent intent = mContext.getPackageManager().getLaunchIntentForPackage(UsualData.SYSTEM_TMT_OTA_TRIGGER_APP_PACKAGENAME);
											intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
											intent.putExtra(ParameterName.TMT_URL, SharedPreferencesUtils.getSharePreferencesValue(UsualData.SHARED_PREFERENCES_TMT_SERVER_URL, UsualData.WISEPOS_PULS_TMT_URL_DEFAULT));
											mContext.startActivity(intent);

											if (!SharedPreferencesUtils.getSharePreferencesValue(Constants.FLAG_KEY_UPGRADE_EXIST, false))
												SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_NEED_WISEPOS5_OTA_FLAG, false);
											SharedPreferencesUtils.setSharePreferencesValue(Constants.FLAG_TMT_UPGRADE_EXIST, false);
										}
									}, 10 * 1000);
								}
							}
						}
					}
                }
            }
        }else  if (action.equals(UsualData.ACTION_TODO_OTA_UPDATE)
                || action.equals(UsualData.ACTION_TODO_SP_UPDATE )
                || action.equals(UsualData.ACTION_TODO_TMT_UPDATE)
                || action.equals(UsualData.ACTION_TODO_KEY_UPDATE)
                || action.equals(UsualData.ACTION_TODO_WISEPOS_OTA_UPDATE)) {
            BBLog.i(Constants.TAG, "OTAUpdateReceiver onReceive ACTION_TODO_OTAUPGRADE: " + action);
            String pakename = "";
            Intent targetIntent = new Intent(context, OTANotifyService.class);
            if (action.equals(UsualData.ACTION_TODO_OTA_UPDATE)) {
                if (DeviceInfoApi.getIntance().isWisePosPro()) {  //7md wisecube ota
                    SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_NEED_WISEPOSPRO_OTA_FLAG, true);
                    SharedPreferencesUtils.setSharePreferencesValue(Constants.FLAG_WISECUBE_FW_UPGRADE_EXIST, true);
                    pakename = intent.getStringExtra(ParameterName.packName);
                    targetIntent.putExtra(ParameterName.type_for_ota, Constants.TYPE_WISECUBE_FW_UPGRADE);
                }
            }else if (action.equals(UsualData.ACTION_TODO_WISEPOS_OTA_UPDATE)){
                if (DeviceInfoApi.getIntance().isWisePos4G()) { //wisepos 4G tmt
                    SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_NEED_WISEPOS4G_OTA_FLAG, true);
                    pakename = intent.getStringExtra(ParameterName.packName);
                    targetIntent.putExtra(ParameterName.type_for_ota, Constants.TYPE_WISEPOS_TMT_UPGRADE);
                }
            }else if (action.equals(UsualData.ACTION_TODO_SP_UPDATE)){
                if (DeviceInfoApi.getIntance().isWisePosPro()) { //7md sp update
					SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_NEED_WISEPOSPRO_OTA_FLAG, true);
					SharedPreferencesUtils.setSharePreferencesValue(Constants.FLAG_SP_UPGRADE_EXIST, true);
                    pakename = intent.getStringExtra(ParameterName.packName);
                    targetIntent.putExtra(ParameterName.type_for_ota, Constants.TYPE_SP_FW_UPGRADE);
                }
            }else if (action.equals(UsualData.ACTION_TODO_TMT_UPDATE)){ //p1000/p500 tmt
                if (DeviceInfoApi.getIntance().isWisePos5Plus() || DeviceInfoApi.getIntance().isWisePos5() || DeviceInfoApi.getIntance().isWisePosTouch() || DeviceInfoApi.getIntance().isWisePosTouchPlus()) {
                    pakename = intent.getStringExtra(ParameterName.packName);
                    SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_NEED_WISEPOS5_OTA_FLAG, true);
                    SharedPreferencesUtils.setSharePreferencesValue(Constants.FLAG_TMT_UPGRADE_EXIST, true);
                    targetIntent.putExtra(ParameterName.type_for_ota, Constants.TYPE_TMT_UPGRADE);
                }
            }else if (action.equals(UsualData.ACTION_TODO_KEY_UPDATE)){ //p1000/p500 keyUpdate
                if (DeviceInfoApi.getIntance().isWisePos5Plus() || DeviceInfoApi.getIntance().isWisePos5() || DeviceInfoApi.getIntance().isWisePosTouch() || DeviceInfoApi.getIntance().isWisePosTouchPlus()) {
                    pakename = intent.getStringExtra(ParameterName.packName);
                    SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_NEED_WISEPOS5_OTA_FLAG, true);
					SharedPreferencesUtils.setSharePreferencesValue(Constants.FLAG_KEY_UPGRADE_EXIST, true);
                    targetIntent.putExtra(ParameterName.type_for_ota, Constants.TYPE_KEY_UPGRADE);
                }
            }

            targetIntent.putExtra(ParameterName.flag_need_ota,true);
            if (!TextUtils.isEmpty(pakename))//sp 升級時，才會賦值pakename
                targetIntent.putExtra(ParameterName.packName,pakename);

            BBLog.e("WiseApp2.0", "OTAUpdateReceiver: packagename"+pakename );
            context.startService(targetIntent);
        }else if (UsualData.ACTION_OTA_TRIGGER_COMPLETED.equals(action)){//由 wisecube ota tool 发出
			//清除舊數據，待下次終端啟動後再重新獲取一次
			SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_DEVICE_BID_INFO,"");
			SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_DEVICE_FULL_INFO,"");
            //ota 升级成功
			if (!SharedPreferencesUtils.getSharePreferencesValue(Constants.FLAG_SP_UPGRADE_EXIST, false)) {
				SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_NEED_WISEPOSPRO_OTA_FLAG, false);
			}
			SharedPreferencesUtils.setSharePreferencesValue(Constants.FLAG_WISECUBE_FW_UPGRADE_EXIST,false);
			//清除缓存任务
			Constants.isOTARunning = false;
			//重启
			SystemManagerAdapter.reboot(context);
        }else if (UsualData.ACTION_WISEPOS_OTA_TRIGGER_COMPLETED.equals(action)){//由 wisepos4G ota 发出
            BBLog.e(BBLog.TAG, "OTAUpdateReceiver: wisepos ota 执行结束" );
            //ota 升级成功
            SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_NEED_WISEPOS4G_OTA_FLAG, false);
            //清除缓存任务
            Constants.isOTARunning = false;
        }else if (UsualData.ACTION_STARTCLIENTAPK_MSG.equals(action)){//由 ota tool 发出
            BBLog.d(BBLog.TAG, "OTAUpdateReceiver Broadcast ACTION_STARTCLIENTAPK_MSG:" + action);
            startClientApp(context);
        }else if (UsualData.ACTION_SP_OTA_TRIGGER_COMPLETED.equals(action)){//由 sp update tool 发出
            BBLog.d(BBLog.TAG, "OTAUpdateReceiver Broadcast ACTION_SP_OTA_TRIGGER_COMPLETED:" + action);
            SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_NEED_WISEPOSPRO_OTA_FLAG, false);
			SharedPreferencesUtils.setSharePreferencesValue(Constants.FLAG_SP_UPGRADE_EXIST,false);
			//清除舊數據，待下次終端啟動後再重新獲取一次
			SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_DEVICE_BID_INFO,"");
			SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_DEVICE_FULL_INFO,"");
            //更新任务状态
            Constants.isOTARunning = false;
            //重启设备
//            SystemManagerAdapter.reboot(context);
        }else if (UsualData.ACTION_TMT_OTA_TRIGGER_COMPLETED.equals(action)){//由P1000 P500_tmt工具發出
            BBLog.d(BBLog.TAG, "OTAUpdateReceiver Broadcast ACTION_SP_OTA_TRIGGER_COMPLETED:" + action);
            //更新任务状态
            Constants.isOTARunning = false;
            SharedPreferencesUtils.setSharePreferencesValue(Constants.FLAG_TMT_UPGRADE_EXIST, false);
            if (!SharedPreferencesUtils.getSharePreferencesValue(Constants.FLAG_KEY_UPGRADE_EXIST, false)) {
				SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_NEED_WISEPOS5_OTA_FLAG, false);
				//启动客户应用
				new Handler().postDelayed(new Runnable() {
					@Override
					public void run() {
						ProvisionService.startClientApp();
					}
				}, 5000);
			}else {
				SystemManagerAdapter.reboot(context);

//				//進入key update 升級
//				new Handler().postDelayed(new Runnable() {
//					@Override
//					public void run() {
//						Intent targetIntent = new Intent(context, OTANotifyService.class);
//						targetIntent.putExtra(ParameterName.type_for_ota, Constants.TYPE_KEY_UPGRADE);
//						targetIntent.putExtra(ParameterName.flag_need_ota,true);
//						targetIntent.putExtra(ParameterName.packName,UsualData.SYSTEM_KEY_UPDATE_TRIGGER_APP_PACKAGENAME);
//						intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
//						context.startService(targetIntent);
//
////						Constants.isOTARunning = true;
////						Intent intent = mContext.getPackageManager().getLaunchIntentForPackage(UsualData.SYSTEM_KEY_UPDATE_TRIGGER_APP_PACKAGENAME);
////						intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
////						mContext.startActivity(intent);
//						if (!SharedPreferencesUtils.getSharePreferencesValue(Constants.FLAG_KEY_UPGRADE_EXIST, false))
//							SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_NEED_WISEPOS5_OTA_FLAG, false);
//					}
//				},10*1000);
			}
            //重启设备
//            SystemManagerAdapter.reboot(context);
        }else if (UsualData.ACTION_KEYUPDATE_TRIGGER_COMPLETED.equals(action)){//由P1000 P500 KEY PROFILE 工具發出
            BBLog.d(BBLog.TAG, "OTAUpdateReceiver Broadcast ACTION_KEYUPDATE_TRIGGER_COMPLETED:" + action);
            //更新任务状态
            Constants.isOTARunning = false;
			SharedPreferencesUtils.setSharePreferencesValue(Constants.FLAG_KEY_UPGRADE_EXIST, false);
			//判斷是否還需要進行 tmt操作。
			if (!SharedPreferencesUtils.getSharePreferencesValue(Constants.FLAG_TMT_UPGRADE_EXIST, false)) {
				SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_NEED_WISEPOS5_OTA_FLAG, false);
				//启动客户应用
				new Handler().postDelayed(new Runnable() {
					@Override
					public void run() {
						ProvisionService.startClientApp();
					}
				}, 5000);
			}else {
				SystemApi.reboot(context);

//				//進入tmt 升級
//				new Handler().postDelayed(new Runnable() {
//					@Override
//					public void run() {
//						Intent targetIntent = new Intent(context, OTANotifyService.class);
//						targetIntent.putExtra(ParameterName.type_for_ota, Constants.TYPE_TMT_UPGRADE);
//						targetIntent.putExtra(ParameterName.flag_need_ota,true);
//						targetIntent.putExtra(ParameterName.packName,UsualData.SYSTEM_TMT_OTA_TRIGGER_APP_PACKAGENAME);
//						intent.putExtra(ParameterName.TMT_URL, SharedPreferencesUtils.getSharePreferencesValue(UsualData.SHARED_PREFERENCES_TMT_SERVER_URL, UsualData.WISEPOS_PULS_TMT_URL_DEFAULT));
//						intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
//						context.startService(targetIntent);
//
////						Constants.isOTARunning = true;
////						Intent intent = mContext.getPackageManager().getLaunchIntentForPackage(UsualData.SYSTEM_TMT_OTA_TRIGGER_APP_PACKAGENAME);
////						intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
////						intent.putExtra(ParameterName.TMT_URL, SharedPreferencesUtils.getSharePreferencesValue(UsualData.SHARED_PREFERENCES_TMT_SERVER_URL, UsualData.WISEPOS_PULS_TMT_URL_DEFAULT));
////						mContext.startActivity(intent);
//						if (!SharedPreferencesUtils.getSharePreferencesValue(Constants.FLAG_KEY_UPGRADE_EXIST, false))
//							SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_NEED_WISEPOS5_OTA_FLAG, false);
//					}
//				},10*1000);
			}
        }else if (UsualData.ACTION_GDS_STATE_UPLOAD.equals(action)){
			try {
				if (!TextUtils.isEmpty(intent.getStringExtra("gdsUpdateState"))) {
					String result = intent.getStringExtra("gdsUpdateState");
					JSONObject gdsResult = new JSONObject(result);
					if (gdsResult.has("mode") && Constants.start_up_model.equals(gdsResult.optString("mode"))) {
						BBLog.d(BBLog.TAG,"终端当前mode同gds os result数据保存的数据一致,开始上送C0203报文");
						WebSocketSender.C0203_GdsOsUpgradeStatusUpload(gdsResult);
					}else {
						BBLog.d(BBLog.TAG,"终端当前mode同gds os result保存的数据不一致,延迟上送C0203报文");
						FileUtils.saveOSUpgradeStatus(gdsResult);
					}
				}
			} catch (Exception e) {
				e.printStackTrace();
			}
        }
    }


    //根据包名判断应用是否已经安装
    public  boolean isInstallApp(Context context,String packageName) {
        if (TextUtils.isEmpty(packageName))return false;
        final PackageManager packageManager = context.getPackageManager();// 获取packagemanager
        List<PackageInfo> pinfo = packageManager.getInstalledPackages(0);// 获取所有已安装程序的包信息
        if (pinfo != null) {
            for (int i = 0; i < pinfo.size(); i++) {
                String pn = pinfo.get(i).packageName.toLowerCase(Locale.ENGLISH);
                if (pn.equals(packageName)) {
                    return true;
                }
            }
        }
        return false;
    }

    public void startClientApp(Context context) {
        try{
            SharedPreferencesUtils.init(context);
            String clientAppPackageName = SharedPreferencesUtils.getSharePreferencesValue(UsualData.SHARED_PREFERENCES_STARTUP_APP_PACKAGE_NAME, "");
            BBLog.i(Constants.TAG, "OTAUpdateReceiver clientAppPackageName = " + clientAppPackageName);
            if (!TextUtils.isEmpty(clientAppPackageName) && ActivityUtils.isApplicationAvilible(context, clientAppPackageName)) {
                if (!ActivityUtils.isApplictionOnTop(context, clientAppPackageName)) {
                    Intent intent = context.getPackageManager().getLaunchIntentForPackage(clientAppPackageName);
                    context.startActivity(intent);
                    BBLog.i(Constants.TAG, "启动该应用");
                } else {
                    BBLog.i(Constants.TAG, "该应用已启动");
                }
            }  else {
                BBLog.i(Constants.TAG, "包名为空或者该应用未安装");
            }
        } catch(Exception e) {
            BBLog.i(Constants.TAG, "没有安装该应用");
        }
    }

}
