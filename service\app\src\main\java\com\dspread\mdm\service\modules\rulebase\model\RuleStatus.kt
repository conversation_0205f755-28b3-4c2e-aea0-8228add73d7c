package com.dspread.mdm.service.modules.rulebase.model

/**
 * 规则状态枚举
 */
enum class RuleStatus(val value: String, val description: String) {
    READY("READY", "准备执行"),
    EXECUTING("EXECUTING", "执行中"),
    IMPLEMENTED("IMPLEMENTED", "执行成功"),
    COMPLETED("COMPLETED", "已完成"),
    READY_TO_FALLBACK("READY_TO_FALLBACK", "准备回退"),
    EXPIRED("EXPIRED", "已过期"),
    FAILED("FAILED", "执行失败"),
    CANCELLED("CANCELLED", "已取消");
    
    companion object {
        /**
         * 从字符串值创建状态枚举
         */
        fun fromValue(value: String): RuleStatus {
            return values().find { it.value == value } ?: READY
        }
    }
    
    /**
     * 检查状态转换是否合法
     */
    fun canTransitionTo(newStatus: RuleStatus): Boolean {
        return when (this) {
            READY -> newStatus in listOf(EXECUTING, EXPIRED, CANCELLED)
            EXECUTING -> newStatus in listOf(IMPLEMENTED, FAILED, CANCELLED)
            IMPLEMENTED -> newStatus in listOf(COMPLETED, READY_TO_FALLBACK)
            COMPLETED -> false // 终态，不能再转换
            READY_TO_FALLBACK -> newStatus in listOf(EXPIRED, READY)
            EXPIRED -> newStatus in listOf(READY) // 可以重新激活
            FAILED -> newStatus in listOf(READY, CANCELLED) // 可以重试或取消
            CANCELLED -> false // 终态，不能再转换
        }
    }
    
    /**
     * 检查是否为终态
     */
    fun isTerminalState(): Boolean {
        return this in listOf(COMPLETED, CANCELLED)
    }
    
    /**
     * 检查是否为可执行状态
     */
    fun isExecutableState(): Boolean {
        return this == READY
    }
    
    /**
     * 检查是否为活跃状态（非终态且非过期）
     */
    fun isActiveState(): Boolean {
        return this in listOf(READY, EXECUTING, IMPLEMENTED, READY_TO_FALLBACK)
    }
}
