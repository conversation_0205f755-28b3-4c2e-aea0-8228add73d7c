package com.dspread.mdm.service.modules.geofence.location

import android.content.Context
import android.os.Build
import android.telephony.TelephonyManager
import android.telephony.cdma.CdmaCellLocation
import android.telephony.gsm.GsmCellLocation
import com.dspread.mdm.service.platform.api.network.NetworkApi
import com.dspread.mdm.service.utils.log.Logger
import org.json.JSONObject

/**
 * 基站位置管理器
 * 用于获取基站信息和信号强度
 */
class CellLocationManager(private val context: Context) {

    companion object {
        private const val TAG = "[CellLocationManager]"
    }

    private val telephonyManager: TelephonyManager by lazy {
        context.getSystemService(Context.TELEPHONY_SERVICE) as TelephonyManager
    }
    
    /**
     * 基站信息数据类
     */
    data class CellInfo(
        var mcc: String = "",
        var mnc: String = "",
        var lac: Int = 0,
        var cid: Int = 0,
        var dbm: Int = -1
    )
    
    /**
     * 获取基站信息
     * 优化：添加网络状态检查和优雅降级处理
     */
    fun getCellInfo(): CellInfo {
        val cell = CellInfo()

        try {
            // 首先检查网络状态（复用现有工具类）
            if (!NetworkApi.isNetworkAvailable(context)) {
                Logger.geoW("$TAG 网络未就绪，返回默认基站信息")
                return getDefaultCellInfo()
            }

            // 获取网络运营商信息
            val operator = telephonyManager.networkOperator
            if (operator.isNullOrEmpty() || operator.length < 5) {
                Logger.geoW("$TAG 网络运营商信息无效: $operator")
                return getDefaultCellInfo()
            }

            cell.mcc = operator.substring(0, 3)
            cell.mnc = operator.substring(3)

            // 获取网络类型
            val phoneType = telephonyManager.phoneType

            when (phoneType) {
                TelephonyManager.PHONE_TYPE_CDMA -> {
                    val location = telephonyManager.cellLocation as? CdmaCellLocation
                    if (location == null) {
                        Logger.geoW("$TAG CDMA基站信息不可用，使用默认值")
                        return getDefaultCellInfo()
                    }

                    cell.lac = location.networkId
                    cell.cid = location.baseStationId
                }

                TelephonyManager.PHONE_TYPE_GSM -> {
                    val location = telephonyManager.cellLocation as? GsmCellLocation
                    if (location == null) {
                        Logger.geoW("$TAG GSM基站信息不可用，使用默认值")
                        return getDefaultCellInfo()
                    }

                    cell.lac = location.lac
                    cell.cid = location.cid
                }

                else -> {
                    Logger.geoW("$TAG 未知的手机类型: $phoneType，使用默认值")
                    return getDefaultCellInfo()
                }
            }
            
            // 获取信号强度
            cell.dbm = getMobileDbm()

        } catch (e: SecurityException) {
            Logger.geoE("$TAG 获取基站信息失败：缺少权限", e)
            return getDefaultCellInfo()
        } catch (e: Exception) {
            Logger.geoE("$TAG 获取基站信息失败，使用默认值", e)
            return getDefaultCellInfo()
        }

        return cell
    }


    /**
     * 获取默认基站信息（网络不可用时使用）
     */
    private fun getDefaultCellInfo(): CellInfo {
        return CellInfo().apply {
            mcc = ""  // 默认移动国家代码
            mnc = ""   // 默认移动网络代码
            lac = -1     // 默认位置区域代码
            cid = -1     // 默认基站ID
            dbm = -999   // 默认信号强度
        }
    }
    
    /**
     * 获取基站信息的JSON格式
     */
    fun getLocationJsonCell(): JSONObject? {
        return try {
            // 先检查权限
            if (!hasRequiredPermissions()) {
                Logger.geoW("缺少基站信息权限，使用网络运营商信息")  // 🌍 使用基站专用日志
                return getBasicCellInfo()
            }

            val cell = getCellInfo()

            JSONObject().apply {
                put("MCC", cell.mcc)
                put("MNC", cell.mnc)
                put("CID", cell.cid.toString())
                put("LAC", cell.lac.toString())
                put("DBM", cell.dbm.toString())
                put("IMSI", getIMSI())
                put("ICCID", getICCID())
            }

        } catch (e: SecurityException) {
            Logger.geoW("基站信息权限被拒绝，使用基础信息")  // 🌍 使用基站专用日志
            getBasicCellInfo()
        } catch (e: Exception) {
            Logger.geoE("获取基站JSON信息失败", e)  // 🌍 使用基站专用日志
            getBasicCellInfo()
        }
    }

    /**
     * 检查是否有必要的权限
     */
    private fun hasRequiredPermissions(): Boolean {
        val hasCoarseLocation = context.checkSelfPermission(android.Manifest.permission.ACCESS_COARSE_LOCATION) ==
                android.content.pm.PackageManager.PERMISSION_GRANTED
        val hasFineLocation = context.checkSelfPermission(android.Manifest.permission.ACCESS_FINE_LOCATION) ==
                android.content.pm.PackageManager.PERMISSION_GRANTED
        val hasPhoneState = context.checkSelfPermission(android.Manifest.permission.READ_PHONE_STATE) ==
                android.content.pm.PackageManager.PERMISSION_GRANTED

        return (hasCoarseLocation || hasFineLocation) && hasPhoneState
    }

    /**
     * 获取基础基站信息（不需要位置权限）
     */
    private fun getBasicCellInfo(): JSONObject {
        return try {
            val operator = telephonyManager.networkOperator
            val mcc = if (!operator.isNullOrEmpty() && operator.length >= 5) {
                operator.substring(0, 3)
            } else {
                "310"
            }
            val mnc = if (!operator.isNullOrEmpty() && operator.length >= 5) {
                operator.substring(3)
            } else {
                "260"
            }

            JSONObject().apply {
                put("MCC", mcc)
                put("MNC", mnc)
                put("CID", "47108")  // 默认值
                put("LAC", "8514")   // 默认值
                put("DBM", "-1")     // 默认值
                put("IMSI", getIMSI())
                put("ICCID", getICCID())
            }
        } catch (e: Exception) {
            Logger.geoE("获取基础基站信息失败", e)
            JSONObject().apply {
                put("MCC", "310")
                put("MNC", "260")
                put("CID", "47108")
                put("LAC", "8514")
                put("DBM", "-1")
                put("IMSI", "310260000000000")
                put("ICCID", "89860318640220133897")
            }
        }
    }
    
    /**
     * 获取4G网络信号强度
     */
    fun getSimSignalStrength(): Int {
        return try {
            val networkType = telephonyManager.dataNetworkType
            
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                if (networkType == TelephonyManager.NETWORK_TYPE_LTE) {
                    getLTESignalStrength()
                } else {
                    -1
                }
            } else {
                -1
            }
        } catch (e: Exception) {
            Logger.geoE("获取信号强度失败", e)
            -1
        }
    }
    
    /**
     * 获取LTE信号强度
     */
    private fun getLTESignalStrength(): Int {
        return try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
                val signalStrength = telephonyManager.signalStrength
                if (signalStrength != null) {
                    val gsmSignalStrength = signalStrength.gsmSignalStrength
                    val asu = if (gsmSignalStrength == 99) -1 else gsmSignalStrength
                    val dbm = -113 + (2 * asu)
                    Logger.geo("LTE信号强度: $dbm dBm")
                    dbm
                } else {
                    -1
                }
            } else {
                -1
            }
        } catch (e: Exception) {
            Logger.geoE("获取LTE信号强度失败", e)
            -1
        }
    }
    
    /**
     * 获取移动网络信号强度
     */
    private fun getMobileDbm(): Int {
        return try {
            // 简化实现，返回默认值
            // 实际实现需要根据具体需求调用相应的API
            -1
        } catch (e: Exception) {
            Logger.geoE("获取移动网络信号强度失败", e)
            -1
        }
    }
    
    /**
     * 获取IMSI
     */
    fun getIMSI(): String {
        return try {
            telephonyManager.subscriberId ?: ""
        } catch (e: SecurityException) {
            Logger.geoE("获取IMSI失败：缺少权限", e)
            ""
        } catch (e: Exception) {
            Logger.geoE("获取IMSI失败", e)
            ""
        }
    }
    
    /**
     * 获取ICCID
     */
    fun getICCID(): String {
        return try {
            telephonyManager.simSerialNumber ?: ""
        } catch (e: SecurityException) {
            Logger.geoE("获取ICCID失败：缺少权限", e)
            ""
        } catch (e: Exception) {
            Logger.geoE("获取ICCID失败", e)
            ""
        }
    }
}
