---------------------------- PROCESS STARTED (2301) for package com.dspread.mdm.service ----------------------------
--------- beginning of main
2025-08-21 16:29:43.465  2301-2301  Receiver                com.dspread.mdm.service              D  🔧 电源已断开
2025-08-21 16:29:43.471  2301-2301  WebSocket               com.dspread.mdm.service              I  🔧 C0902 主动式上送: charging_state_change (主动: 150)
2025-08-21 16:29:43.497  2301-2301  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0902","serialNo":"01610040202307060227","request_time":"1755764983483","request_id":"1755764983483C0902","version":"1","data":{"batteryLife":80,"batteryHealth":2,"temprature":"27.2","isCharging":"0","isLowBattery":"0","powerOff":"0","outOfBattery":"0"},"myVersionName":"1.1.03.20250821.DSPREAD.MDM.SERVICE","terminalDate":"20250821162943"}
2025-08-21 16:29:43.499  2301-2301  WebSocket               com.dspread.mdm.service              I  🔧 C0902 电池状态上送成功 (1, trigger=charging_state_change)
--------- beginning of system
2025-08-21 16:29:48.270  2301-2827  WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 5 (第5个，待响应: 1)
2025-08-21 16:29:48.373  2301-2826  WebSocket               com.dspread.mdm.service              I  🔧 收到服务器 PONG 帧: 5 (待响应PING: 0)
2025-08-21 16:30:13.915  2301-2301  Receiver                com.dspread.mdm.service              D  🔧 屏幕关闭
2025-08-21 16:30:13.916  2301-2301  WebSocket               com.dspread.mdm.service              W  ⚠️ 未知触发条件: screen_off，默认按被动式处理
2025-08-21 16:30:13.920  2301-2301  WebSocket               com.dspread.mdm.service              I  🔧 C0201 被动式上送拒绝: screen_off (拒绝: 19)
2025-08-21 16:30:13.921  2301-2301  WebSocket               com.dspread.mdm.service              I  🔧 C0201 设备状态上送被流量控制阻止: 被动事件 'screen_off' 在平衡模式 - 重要变化下未启用
2025-08-21 16:30:13.925  2301-2301  Receiver                com.dspread.mdm.service              D  🔧 屏幕关闭处理完成
2025-08-21 16:30:18.277  2301-2827  WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 6 (第6个，待响应: 1)
2025-08-21 16:30:18.619  2301-2826  WebSocket               com.dspread.mdm.service              I  🔧 收到服务器 PONG 帧: 6 (待响应PING: 0)
2025-08-21 16:31:26.720  2301-2301  Receiver                com.dspread.mdm.service              D  🔧 电源已连接
2025-08-21 16:31:26.721  2301-2301  WebSocket               com.dspread.mdm.service              I  🔧 C0902 主动式上送: charging_state_change (主动: 151)
2025-08-21 16:31:26.814  2301-2301  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0902","serialNo":"01610040202307060227","request_time":"1755765086787","request_id":"1755765086787C0902","version":"1","data":{"batteryLife":80,"batteryHealth":2,"temprature":"26.9","isCharging":"1","isLowBattery":"0","powerOff":"0","outOfBattery":"0"},"myVersionName":"1.1.03.20250821.DSPREAD.MDM.SERVICE","terminalDate":"20250821163126"}
2025-08-21 16:31:26.815  2301-2301  WebSocket               com.dspread.mdm.service              I  🔧 C0902 电池状态上送成功 (1, trigger=charging_state_change)
2025-08-21 16:31:26.845  2301-2301  Receiver                com.dspread.mdm.service              D  🔧 ServiceManagementEventHandler 处理用户解锁事件
2025-08-21 16:31:26.850  2301-2301  Common                  com.dspread.mdm.service              D  🔧 ServiceStartupManager 服务正在运行，无需启动: USER_PRESENT
2025-08-21 16:31:26.851  2301-2301  Receiver                com.dspread.mdm.service              D  🔧 ServiceManagementEventHandler 用户解锁后服务检查完成
2025-08-21 16:31:27.056  2301-2301  Receiver                com.dspread.mdm.service              D  🔧 屏幕点亮
2025-08-21 16:31:27.057  2301-2301  WebSocket               com.dspread.mdm.service              W  ⚠️ 未知触发条件: screen_on，默认按被动式处理
2025-08-21 16:31:27.060  2301-2301  WebSocket               com.dspread.mdm.service              I  🔧 C0201 被动式上送拒绝: screen_on (拒绝: 20)
2025-08-21 16:31:27.061  2301-2301  WebSocket               com.dspread.mdm.service              I  🔧 C0201 设备状态上送被流量控制阻止: 被动事件 'screen_on' 在平衡模式 - 重要变化下未启用
2025-08-21 16:31:27.061  2301-2301  Receiver                com.dspread.mdm.service              D  🔧 屏幕点亮处理完成
2025-08-21 16:31:51.128  2301-2827  WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 7 (第7个，待响应: 1)
2025-08-21 16:31:51.234  2301-2826  WebSocket               com.dspread.mdm.service              I  🔧 收到服务器 PONG 帧: 7 (待响应PING: 0)
2025-08-21 16:31:57.116  2301-2301  Receiver                com.dspread.mdm.service              D  🔧 屏幕关闭
2025-08-21 16:31:57.118  2301-2301  WebSocket               com.dspread.mdm.service              W  ⚠️ 未知触发条件: screen_off，默认按被动式处理
2025-08-21 16:31:57.123  2301-2301  WebSocket               com.dspread.mdm.service              I  🔧 C0201 被动式上送拒绝: screen_off (拒绝: 21)
2025-08-21 16:31:57.125  2301-2301  WebSocket               com.dspread.mdm.service              I  🔧 C0201 设备状态上送被流量控制阻止: 被动事件 'screen_off' 在平衡模式 - 重要变化下未启用
2025-08-21 16:31:57.127  2301-2301  Receiver                com.dspread.mdm.service              D  🔧 屏幕关闭处理完成
2025-08-21 16:32:21.129  2301-2827  WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 8 (第8个，待响应: 1)
2025-08-21 16:32:21.502  2301-2826  WebSocket               com.dspread.mdm.service              I  🔧 收到服务器 PONG 帧: 8 (待响应PING: 0)
2025-08-21 16:32:51.130  2301-2827  WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 9 (第9个，待响应: 1)
2025-08-21 16:32:51.505  2301-2826  WebSocket               com.dspread.mdm.service              I  🔧 收到服务器 PONG 帧: 9 (待响应PING: 0)
2025-08-21 16:33:21.130  2301-2827  WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 10 (第10个，待响应: 1)
2025-08-21 16:33:21.509  2301-2826  WebSocket               com.dspread.mdm.service              I  🔧 收到服务器 PONG 帧: 10 (待响应PING: 0)
2025-08-21 16:33:51.131  2301-2827  WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 11 (第11个，待响应: 1)
2025-08-21 16:33:51.511  2301-2826  WebSocket               com.dspread.mdm.service              I  🔧 收到服务器 PONG 帧: 11 (待响应PING: 0)
2025-08-21 16:34:21.133  2301-2827  WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 12 (第12个，待响应: 1)
2025-08-21 16:34:21.514  2301-2826  WebSocket               com.dspread.mdm.service              I  🔧 收到服务器 PONG 帧: 12 (待响应PING: 0)
2025-08-21 16:34:51.134  2301-2827  WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 13 (第13个，待响应: 1)
2025-08-21 16:34:51.416  2301-2826  WebSocket               com.dspread.mdm.service              I  🔧 收到服务器 PONG 帧: 13 (待响应PING: 0)
2025-08-21 16:35:21.135  2301-2827  WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 14 (第14个，待响应: 1)
2025-08-21 16:35:21.317  2301-2826  WebSocket               com.dspread.mdm.service              I  🔧 收到服务器 PONG 帧: 14 (待响应PING: 0)
2025-08-21 16:35:51.136  2301-2827  WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 15 (第15个，待响应: 1)
2025-08-21 16:35:51.423  2301-2826  WebSocket               com.dspread.mdm.service              I  🔧 收到服务器 PONG 帧: 15 (待响应PING: 0)
2025-08-21 16:36:21.137  2301-2827  WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 16 (第16个，待响应: 1)
2025-08-21 16:36:21.426  2301-2826  WebSocket               com.dspread.mdm.service              I  🔧 收到服务器 PONG 帧: 16 (待响应PING: 0)
2025-08-21 16:36:51.137  2301-2827  WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 17 (第17个，待响应: 1)
2025-08-21 16:36:51.532  2301-2826  WebSocket               com.dspread.mdm.service              I  🔧 收到服务器 PONG 帧: 17 (待响应PING: 0)
