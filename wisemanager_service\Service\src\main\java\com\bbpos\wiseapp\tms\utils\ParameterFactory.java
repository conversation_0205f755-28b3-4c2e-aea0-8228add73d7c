package com.bbpos.wiseapp.tms.utils;

import android.annotation.SuppressLint;
import android.app.usage.StorageStats;
import android.app.usage.StorageStatsManager;
import android.content.Context;
import android.content.SharedPreferences;
import android.content.pm.ApplicationInfo;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.os.Build;
import android.os.Process;
import android.os.UserHandle;
import android.preference.PreferenceManager;

import com.bbpos.wiseapp.logger.BBLog;
import com.bbpos.wiseapp.param.manager.ParamModel;
import com.bbpos.wiseapp.sdk.app.UsageStats;
import com.bbpos.wiseapp.service.common.UsualData;
import com.bbpos.wiseapp.service.contentprovider.db.ParamDbOperation;
import com.bbpos.wiseapp.system.api.DeviceInfoApi;
import com.bbpos.wiseapp.system.api.SysIntermediateApi;
import com.bbpos.wiseapp.tms.listener.device.HardwareInfo;
import com.bbpos.wiseapp.tms.listener.device.MidwareInterface;
import com.bbpos.wiseapp.tms.location.CellLocationManager;
import com.bbpos.wiseapp.tms.location.GpsLocationManager;
import com.bbpos.wiseapp.tms.location.WifiLocationManager;
import com.bbpos.wiseapp.tms.traffic.DataCollectService;
import com.bbpos.wiseapp.tms.traffic.DataProvideService;
import com.bbpos.wiseapp.utils.ActivityUtils;
import com.bbpos.wiseapp.utils.SystemUtils;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.Iterator;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.UUID;

@SuppressLint("SimpleDateFormat")
public class ParameterFactory {
	private static JSONObject posInfo = new JSONObject();
	private static String posInfoForUrl = "";

	public static JSONObject createPosInfo() {
		try {
			//判断终端基础信息为空、重新获取
			if(!posInfo.has(ParameterName.facNo) || !Helpers.isStrNoEmpty(posInfo.getString(ParameterName.facNo))){
				MidwareInterface.init(ContextUtil.getInstance());
				posInfo.put(ParameterName.facNo, MidwareInterface.manu);
				posInfo.put(ParameterName.modelNo, MidwareInterface.model);
				posInfo.put(ParameterName.serialNo, MidwareInterface.serial);
				if (DeviceInfoApi.getIntance().isWisePos5() || DeviceInfoApi.getIntance().isWisePos5Plus()) {
					posInfo.put(ParameterName.displayVer, Build.DISPLAY);
				} else {
					posInfo.put(ParameterName.displayVer, SysIntermediateApi.getIntance().getProp("ro.build.custom.version"));
				}
			}
			posInfo.put(ParameterName.verifyCode, null);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return posInfo;
	}

	@SuppressWarnings("deprecation")
	public static String createPosInfoForUrl() {
		if(!Helpers.isStrNoEmpty(posInfoForUrl) || !Helpers.isStrNoEmpty(MidwareInterface.manu)){
			posInfoForUrl = ParameterName.facNo + "=" + URLEncoder.encode(MidwareInterface.manu) + "&";
			posInfoForUrl += ParameterName.modelNo + "=" + URLEncoder.encode(MidwareInterface.model) + "&";
			posInfoForUrl += ParameterName.serialNo + "=" + URLEncoder.encode(MidwareInterface.serial);
		}
		return posInfoForUrl;
	}

	public static JSONObject createParUpdateParam(String taskId) {
		JSONObject data = new JSONObject();
		try {
			data.put(ParameterName.taskId, taskId);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return data;
	}

	@SuppressWarnings("rawtypes")
	public static JSONArray createApkInfoInTer(List<UsageStats> UsageList) {
		JSONArray apkInfoArray = new JSONArray();
		try {
			PackageManager pckMan = ContextUtil.getInstance().getPackageManager();
			StorageStatsManager storageStatsManager = null;
			if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
				storageStatsManager = (StorageStatsManager) ContextUtil.getInstance().getSystemService(Context.STORAGE_STATS_SERVICE);
			}
			UUID uuid = ActivityUtils.getStorageVolumeUUID(ContextUtil.getInstance());
			List installedPackages = pckMan.getInstalledPackages(0);
			Iterator localIterator = installedPackages.iterator();
			while (localIterator.hasNext()) {
				PackageInfo packageInfo = (PackageInfo) localIterator.next();
				if (((packageInfo.applicationInfo.flags & ApplicationInfo.FLAG_SYSTEM) != 0)
						|| ((packageInfo.applicationInfo.flags & ApplicationInfo.FLAG_UPDATED_SYSTEM_APP) != 0)) {
					if(!(UsualData.LAUNCHER_PACKAGE_NAME.equals(packageInfo.packageName) || UsualData.SERVICE_PACKAGE_NAME.equals(packageInfo.packageName))) {
						continue;
					}
				}
				JSONObject apkInfo = new JSONObject();
				ApplicationInfo mApplicationInfo = packageInfo.applicationInfo;
				apkInfo.put(ParameterName.pkgName, packageInfo.packageName);
				apkInfo.put(ParameterName.apkName, mApplicationInfo.loadLabel(pckMan));
				apkInfo.put(ParameterName.versionCode, packageInfo.versionCode);
				apkInfo.put(ParameterName.versionName, packageInfo.versionName);
				Date updateTime = new Date(packageInfo.lastUpdateTime);
				SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
				apkInfo.put(ParameterName.updateDate, sdf.format(updateTime));
//				apkInfo.put(ParameterName.updateDate, DateTimeUtils.getCurrentTimeMills());

				Date today = new Date();
				SimpleDateFormat dataCollectSdf = new SimpleDateFormat(DataCollectService.DATA_COLLECT_SDF);
				DataProvideService dataProvider = new DataProvideService();
				JSONObject dataCollectObj = dataProvider.doTrafficDataCollect(dataCollectSdf.format(today),
						packageInfo.packageName);
				String todayStr = dataCollectSdf.format(today);
//				if(dataCollectObj.has(packageInfo.packageName)){
//					long nowUseKb = dataCollectObj.getLong(packageInfo.packageName)/1024;
//					apkInfo.put(ParameterName.nowUseKb, nowUseKb);
//				} else {
//					apkInfo.put(ParameterName.nowUseKb, 0);
//				}
////								BBLog.e(BBLog.TAG, dataCollectSdf.parse(todayStr).getTime()+"     "+dataCollectSdf.parse(todayStr)+"    "+getApkUsedTimes(packageInfo.packageName, dataCollectSdf.parse(todayStr).getTime()));
//				apkInfo.put(ParameterName.nowUseTimes,getApkUsedTimes(packageInfo.packageName, dataCollectSdf.format(today),UsageList));

				// 未送数据补送 超过规定时间的不补
				if (UsageList != null) {
					JSONArray sumInfoArray = new JSONArray();
					SharedPreferences sp = PreferenceManager.getDefaultSharedPreferences(ContextUtil.getInstance());
					String lastTrafficUploadDate = sp.getString(SPKeys.LAST_TER_INFO_UPLOAD_DATE, "2000/01/01");
					Calendar ca = Calendar.getInstance();
					ca.setTime(today);
					int uploadDays = 0;
					while (ca.getTimeInMillis() >= sdf.parse(lastTrafficUploadDate).getTime()
							&& uploadDays <= Constants.TRAFFIC_UPLOAD_DAY) {
						JSONObject sumInfoJson = new JSONObject();
						String dateStr = dataCollectSdf.format(ca.getTime());
						dataCollectObj = dataProvider.doTrafficDataCollect(dateStr, packageInfo.packageName);
//					sumInfoJson.put(ParameterName.sumDate, sdf.format(ca.getTime()));
						sumInfoJson.put(ParameterName.sumDate, ca.getTime());
						if (dataCollectObj.has(packageInfo.packageName)) {
							long useKb = dataCollectObj.getLong(packageInfo.packageName) / 1024;
							sumInfoJson.put(ParameterName.useKb, useKb);
						} else {
							sumInfoJson.put(ParameterName.useKb, 0);
						}
						sumInfoJson.put(ParameterName.useTimes, getApkUsedTimes(packageInfo.packageName, dateStr, UsageList));
						sumInfoArray.put(sumInfoJson);
						ca.add(Calendar.DATE, -1);
						uploadDays++;
					}
					apkInfo.put(ParameterName.sumInfo, sumInfoArray);
				}

				JSONArray inAppValArray = new JSONArray();
				List<ParamModel> paramModels = ParamDbOperation.getAppParams(packageInfo.packageName);
				if (paramModels!=null && paramModels.size() > 0) {
					for (ParamModel model : paramModels) {
						JSONObject inAppValJSON = new JSONObject();
						inAppValJSON.put(ParameterName.idx, model.getIdx());
						inAppValJSON.put(ParameterName.inKey, model.getKey());
						inAppValJSON.put(ParameterName.inValue, model.getValue());
						inAppValJSON.put(ParameterName.dataSrc, model.getDataSrc());
						inAppValArray.put(inAppValJSON);
					}
					apkInfo.put(ParameterName.inAppVal, inAppValArray);
				}

				JSONObject appPerformanceJSON = new JSONObject();
				//获取每个应用程序的初始安装磁盘占用大小、缓存数据大小、data数据大小及总磁盘空间占用大小
				try {
					UserHandle userHandle = Process.myUserHandle();
					if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
						StorageStats storageStats = storageStatsManager.queryStatsForPackage(uuid, packageInfo.packageName, userHandle);
						appPerformanceJSON.put(ParameterName.appInitSize,storageStats.getAppBytes()+"");
						appPerformanceJSON.put(ParameterName.appCacheSize,storageStats.getCacheBytes()+"");
						appPerformanceJSON.put(ParameterName.appDataSize,(storageStats.getDataBytes()-storageStats.getCacheBytes())+"");
						appPerformanceJSON.put(ParameterName.appTotalSize,(storageStats.getAppBytes() + storageStats.getCacheBytes() + storageStats.getDataBytes())+"");
					} else {
						long appDataSizes[] = ActivityUtils.handlePackageSizeInfo(ContextUtil.getInstance(), packageInfo.packageName);
						appPerformanceJSON.put(ParameterName.appInitSize,appDataSizes[0]+"");
						appPerformanceJSON.put(ParameterName.appCacheSize,appDataSizes[1]+"");
						appPerformanceJSON.put(ParameterName.appDataSize,appDataSizes[2]+"");
						appPerformanceJSON.put(ParameterName.appTotalSize,appDataSizes[3]+"");
					}
				} catch (Exception e) {
					e.printStackTrace();
				}

				//获取每个应用即时内存使用总大小
				int uid = ContextUtil.getInstance().getPackageManager().getApplicationInfo(packageInfo.packageName, PackageManager.GET_META_DATA).uid;
				appPerformanceJSON.put(ParameterName.appRamUsageSize,ActivityUtils.getRunningAppProcessInfo(uid)+"");

				apkInfo.put(ParameterName.appPerformanceVal, appPerformanceJSON);
				BBLog.d(BBLog.TAG,"packageName: "+packageInfo.packageName+" ,appPerformanceVal = " + appPerformanceJSON);

				apkInfoArray.put(apkInfo);
			}
		} catch (JSONException e) {
			e.printStackTrace();
		} catch (Exception e) {
			e.printStackTrace();
		}

		return apkInfoArray;

	}
	/**获取位置信息*/ //整合标准版gps模块 TODO
	public static JSONObject creatLocationInfoInTer(Context context) {
		JSONObject locationJSONObj = new JSONObject();
		try {
				/**位置信息上送，基站*/
			 CellLocationManager cellManager = new CellLocationManager(context);

			//获取
//			GpsLocationManager.unregistLocationChangeListener();
//			GpsLocationManager.registLocationChangeListener();
//			Thread.sleep(3000);

			BBLog.d(BBLog.TAG, "creatLocationInfoInTer start ====");
//	 		BBLog.d(BBLog.TAG, "gpsManager:"+ GpsLocationManager.getGpsLocation());
	 		BBLog.d(BBLog.TAG, "cellManager:"+cellManager.getLocationJsonCell());
	 		BBLog.d(BBLog.TAG, "wifiManager:"+WifiLocationManager.getInstance(context).getLocationJsonWifiFirst());

			locationJSONObj.put("gps", GpsLocationManager.getGpsLocation());
			locationJSONObj.put("baseSite",cellManager.getLocationJsonCell());
			locationJSONObj.put("wifi",WifiLocationManager.getInstance(context).getLocationJsonWifiFirst());
			locationJSONObj.put("address","无法获取");

			BBLog.d(BBLog.TAG, "locationJSONObj:"+locationJSONObj.toString());
			BBLog.d(BBLog.TAG, "creatLocationInfoInTer end ====");
		} catch (Exception e) {
			BBLog.e(BBLog.TAG, "error in creatLocationInfoInTer.",e);
		}
		return locationJSONObj;
	}

	/**获取终端硬件信息
	 * 暂定
	 * */
	public static JSONArray createTerHardwareInfo(){
		JSONArray data = new JSONArray();
		data.put(HardwareInfo.getCpuInfo());
		data.put(HardwareInfo.getROMInfo());
		data.put(HardwareInfo.getTotalMem(ContextUtil.getInstance()));
		data.put(HardwareInfo.getAndroidVer());
		data.put(HardwareInfo.getCurrentNetType(ContextUtil.getInstance()));
		data.put(HardwareInfo.getOSVer());
		data.put(HardwareInfo.getFwVer());
//		data.put(HardwareInfo.getCurrentCPUUsage());
		if (HardwareInfo.deviceStatus != null) {
			data.put(HardwareInfo.getPrinterStatus());
		}
		BBLog.v(BBLog.TAG, "hardwareInfo : "+data.toString());
		return data;
	}

	public static JSONArray createTerHardwareInfo(String cpu_usage, String rom_usage, String mem_usage){
		JSONArray data = new JSONArray();
		data.put(HardwareInfo.getCpuInfo(cpu_usage));
		data.put(HardwareInfo.getROMInfo(rom_usage));
		data.put(HardwareInfo.getTotalMem(ContextUtil.getInstance(), mem_usage));
		data.put(HardwareInfo.getAndroidVer());
		data.put(HardwareInfo.getCurrentNetType(ContextUtil.getInstance()));
		data.put(HardwareInfo.getOSVer());
		data.put(HardwareInfo.getFwVer());
//		data.put(HardwareInfo.getCurrentCPUUsage());
		if (HardwareInfo.deviceStatus != null) {
			data.put(HardwareInfo.getPrinterStatus());
		}
		BBLog.v(BBLog.TAG, "hardwareInfo : "+data.toString());
		return data;
	}

	/**获取電池信息
	 * 暂定
	 * */
	public static JSONObject createTerBatteryInfo(int intLevel, int temperature, int intHealth, boolean isCharging, boolean isLowBattery, boolean powerOff, boolean outOfBattery){
		JSONObject data = new JSONObject();
		try {
			data.put(ParameterName.batLevel, intLevel+"");
			data.put(ParameterName.batHealth, intHealth+"");
            float temp = temperature;
            data.put(ParameterName.temprature, String.format(Locale.US, "%.1f", temp/10.0));
            if (isCharging) {
                data.put(ParameterName.isCharging, "1");
            } else {
                data.put(ParameterName.isCharging, "0");
            }
            if (isLowBattery) {
                data.put(ParameterName.isLowBattery, "1");
            } else {
                data.put(ParameterName.isLowBattery, "0");
            }
            if (powerOff) {
                data.put(ParameterName.powerOff, "1");
            } else {
                data.put(ParameterName.powerOff, "0");
            }
            if (outOfBattery) {
                data.put(ParameterName.outOfBattery, "1");
            } else {
                data.put(ParameterName.outOfBattery, "0");
            }
			BBLog.v(BBLog.TAG, "batteryInfo : "+data.toString());
		} catch (Exception e) {
			e.printStackTrace();
		}
		return data;
	}


	public static JSONObject createTerInfoUploadParam(Context context, List<UsageStats> UsageList, int intLevel, int temperature, int intHealth, boolean isCharging, boolean isLowBattery, boolean powerOff, boolean outOfBattery){
		JSONObject data = new JSONObject();
		try {
			/**位置信息上送，wifi*/
			WifiLocationManager.getInstance(context).scanWifi();
			Thread.sleep(3000); 	//wifi scan 3秒过程
			if ("0".equals(Constants.UPLOAD_MODE)) {
				data.put(ParameterName.apkInfoInTer, ParameterFactory.createApkInfoInTer(UsageList));
				data.put(ParameterName.locationInfoInTer, creatLocationInfoInTer(context));
				data.put(ParameterName.hardwareInfoTer, createTerHardwareInfo());
				data.put(ParameterName.batteryInfoTer, createTerBatteryInfo(intLevel, temperature, intHealth, isCharging, isLowBattery, powerOff, outOfBattery));
				data.put(ParameterName.wifiOption, createWifiOption(context));
				data.put(ParameterName.cardDeviceUsage, createCardDeviceUsage(context));
				data.put(ParameterName.sytemInfo, createSystemInfo());
				data.put(ParameterName.serviceInfo, WebSocketServiceListManager.uploadServiceAppList());
			} else {
				if (DeviceInfoApi.getIntance().isWisePosPro()) {
					data.put(ParameterName.apkInfoInTer, ParameterFactory.createApkInfoInTer(UsageList));
					data.put(ParameterName.locationInfoInTer, creatLocationInfoInTer(context));
					data.put(ParameterName.hardwareInfoTer, createTerHardwareInfo());
					data.put(ParameterName.cardDeviceUsage, createCardDeviceUsage(context));
					data.put(ParameterName.sytemInfo, createSystemInfo());
				} else {
					data.put(ParameterName.apkInfoInTer, ParameterFactory.createApkInfoInTer(UsageList));
					data.put(ParameterName.locationInfoInTer, creatLocationInfoInTer(context));
					data.put(ParameterName.hardwareInfoTer, createTerHardwareInfo());
//			data.put(ParameterName.batteryInfoTer, createTerBatteryInfo(intLevel, intScale, isCharging, usbCharge, acCharge,intHealth));
					data.put(ParameterName.wifiOption, createWifiOption(context));
					data.put(ParameterName.cardDeviceUsage, createCardDeviceUsage(context));
//			data.put(ParameterName.temprature, temperature+"");
					data.put(ParameterName.sytemInfo, createSystemInfo());
					data.put(ParameterName.serviceInfo, WebSocketServiceListManager.uploadServiceAppList());
				}
			}
		} catch (JSONException e) {
			e.printStackTrace();
		} catch (Exception e) {
			e.printStackTrace();
		}
		return data;
	}


	/** 获取应用的使用次数*/
	private static String getApkUsedTimes(String packageName,String dateStart,List<UsageStats> UsageList)
	{
//		String date = ""+dateStart;
//		BBLog.e(BBLog.TAG,"getApkUsedTimes" + packageName+ "----"+date);
		for(int i = 0; i < UsageList.size() ; i++ ) {
			UsageStats usageStats = UsageList.get(i);
			if(usageStats.getCollectDate().equals(dateStart)&&usageStats.getPackageName().equals(packageName)) {
				i = UsageList.size();
				return usageStats.getLaunchCount();
			}
		}
//		String dateStartStr = String.valueOf(dateStart);
//		int usedTimes = 0;
//		String usedTimesStr = "0";
//		Cursor cursor = null;
//		Uri uri = Uri.parse("content://com.ums.tss.mastercontrol.AppDataProvider/app_click");
//		ContentResolver contentResolver = ContextUtil.getInstance().getContentResolver();
//		cursor = contentResolver.query(uri, null, "package=? and time=?", new String[]{packageName,dateStartStr}, null);
//		if(  cursor != null && cursor.getCount() >0)
//		{
//			//获取应用的使用次数
//			while(cursor.moveToNext())
//			{
//				usedTimes = cursor.getInt(cursor.getColumnIndex("count"));
//			}
//			usedTimesStr =String.valueOf(usedTimes);
//		}
//		if(cursor != null)
//		{
//			cursor.close();
//		}
		return "0";
	}

	public static JSONArray createWifiOption(Context context){
		try {
			return WifiLocationManager.getInstance(context).getLocationJsonWifi();
		} catch (Exception e){
			e.printStackTrace();
		}
		return null;
	}

	public static JSONArray createCardDeviceUsage(Context context){
		JSONArray jsonArray = new JSONArray();
		Map<String,Integer> datas = UsageDataManager.getInstance(context).getUsageLogData();
		BBLog.d(BBLog.TAG, "datas: "+datas.toString());
		try {
			JSONObject obj = new JSONObject();
			obj.put("useCase","Swipe");
			obj.put("useTimes",getIntegerValue(datas.get(UsageDataManager.TAG_DF8718)) + getIntegerValue(datas.get(UsageDataManager.TAG_DF8719)));
			obj.put("successTimes",getIntegerValue(datas.get(UsageDataManager.TAG_DF8718)));
			obj.put("failTimes",getIntegerValue(datas.get(UsageDataManager.TAG_DF8719)));
			jsonArray.put(obj);

			obj = new JSONObject();
			obj.put("useCase","Insert");
			obj.put("useTimes",getIntegerValue(datas.get(UsageDataManager.TAG_DF871A)) + getIntegerValue(datas.get(UsageDataManager.TAG_DF871B)));
			obj.put("successTimes",getIntegerValue(datas.get(UsageDataManager.TAG_DF871A)));
			obj.put("failTimes",getIntegerValue(datas.get(UsageDataManager.TAG_DF871B)));
			jsonArray.put(obj);

			obj = new JSONObject();
			obj.put("useCase","Tap");
			obj.put("useTimes",getIntegerValue(datas.get(UsageDataManager.TAG_DF871C)) + getIntegerValue(datas.get(UsageDataManager.TAG_DF871D)));
			obj.put("successTimes",getIntegerValue(datas.get(UsageDataManager.TAG_DF871C)));
			obj.put("failTimes",getIntegerValue(datas.get(UsageDataManager.TAG_DF871D)));
			jsonArray.put(obj);

			obj = new JSONObject();
			obj.put("useCase","WC-Temper");
			obj.put("useTimes",getIntegerValue(datas.get(UsageDataManager.TAG_DF854C)));
			obj.put("successTimes",0);
			obj.put("failTimes",0);
			jsonArray.put(obj);

			obj = new JSONObject();
			obj.put("useCase","SP-Temper");
			obj.put("useTimes",getIntegerValue(datas.get(UsageDataManager.TAG_DF8551)));
			obj.put("successTimes",0);
			obj.put("failTimes",0);
			jsonArray.put(obj);

			BBLog.d(BBLog.TAG, "createCardDeviceUsage: "+jsonArray.toString());
		} catch (Exception e) {
			e.printStackTrace();
			BBLog.d(BBLog.TAG, "datas error: ");
		}

		return jsonArray;
	}

	public static JSONObject createSystemInfo() {
		JSONObject obj = new JSONObject();
		String style = "";
		if (DeviceInfoApi.getIntance().isWisePosPro()) {
			style = SystemUtils.get7MDModel(ContextUtil.getInstance());
		}
		try {
			obj = new JSONObject();
			obj.put("androidVersion",Build.VERSION.RELEASE);
			obj.put("buildNumber",DeviceInfoApi.getIntance().getCustomVersion());

			if (DeviceInfoApi.getIntance().isWisePosPro()) {
				if ("WSP72".equals(style)) {
					obj.put("appearance", "Hand Strap");
				} else if ("WSP73".equals(style)) {
					obj.put("appearance", "Pistol Grip");
				}
			}

			obj.put("aspl",SysIntermediateApi.getIntance().getDeviceSecretPatchLevel());
		} catch (Exception e) {
			e.printStackTrace();
		}
		return obj;
	}

	private static int getIntegerValue(Integer integer){
		return integer == null ? 0 : integer.intValue();
	}
}
