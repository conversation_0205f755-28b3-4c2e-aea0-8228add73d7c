package com.bbpos.wiseapp.tms.service;

import android.content.Intent;
import android.os.Build;
import android.os.Environment;
import android.os.IBinder;
import android.text.TextUtils;

import androidx.annotation.RequiresApi;

import com.bbpos.wiseapp.logger.BBLog;
import com.bbpos.wiseapp.service.WakeLockService;
import com.bbpos.wiseapp.service.common.UsualData;
import com.bbpos.wiseapp.tms.network.HttpUtils;
import com.bbpos.wiseapp.tms.utils.Constants;
import com.bbpos.wiseapp.tms.utils.FileUtils;
import com.bbpos.wiseapp.tms.utils.ParameterName;
import com.bbpos.wiseapp.tms.utils.RuleStatus;
import com.bbpos.wiseapp.tms.utils.TaskState;
import com.bbpos.wiseapp.tms.utils.WebSocketServiceListManager;
import com.bbpos.wiseapp.websocket.WebSocketSender;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.nio.file.attribute.PosixFilePermission;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

public class DownloadService extends WakeLockService {
    private static final String TAG = "DownloadService";
    boolean download_totally = true;
    public DownloadService() {
        super("DownloadService");
    }

    @Override
    protected void onHandleIntent(Intent intent) {
        try {
            String service_app = intent.getStringExtra("service_app");
            BBLog.e(TAG, "DownloadService start. service_app = " + service_app);
            JSONObject service_app_json = new JSONObject(service_app);
            String service_id = service_app_json.getString(ParameterName.serviceId);
            String command = service_app_json.getString(ParameterName.command);
            JSONArray param = new JSONArray(service_app_json.getString(ParameterName.param));

            List<File> fileAll = new ArrayList<>();
            for (int idx=0; idx<param.length(); idx++) {
                JSONObject downloadFile = param.getJSONObject(idx);
                String fileName = downloadFile.getString(ParameterName.fileName);
                String fileUrl = downloadFile.getString(ParameterName.fileUrl);
                String fileMd5 = downloadFile.getString(ParameterName.fileMd5Ex);
                String fileKey = downloadFile.optString(ParameterName.fileKey);
                String fileHash = downloadFile.has(ParameterName.fileHash)?downloadFile.getString(ParameterName.fileHash):"";
                int fileSize = Integer.valueOf(downloadFile.getString(ParameterName.fileSizeEx));

                String path = "";
                if ("C05".equals(command)) {
                    path = "/bbpos/rom";
                    fileName = UsualData.IPTABLES_RULES;
                } else if ("C06".equals(command)) {
                    path = "/bbpos/ca";
                } else if ("C07".equals(command)) {
                    path = "/bbpos/rom/tls";
                }

                String filePath = "";
                if (new File(path).exists()) {
                    if ("C07".equals(command)) {
                        filePath = path + File.separator + fileHash + ".0";
                    } else {
                        filePath = path + File.separator + fileName;
                    }
                } else {
                    if (!new File(path).mkdirs()) {
                        BBLog.e(BBLog.TAG, filePath + "文件存储路径不存在");
                        WebSocketServiceListManager.updateWSServiceAppState(service_app_json.getString(ParameterName.taskId), RuleStatus.IMPLEMENTED);
                        WebSocketSender.C0108_uploadWSServiceAppResult(service_app_json, TaskState.FAILED, "");
                        return;
                    } else {
                        if ("C07".equals(command)) {
                            filePath = path + File.separator + fileHash + ".0";
                        } else {
                            filePath = path + File.separator + fileName;
                        }
                    }
                }

                File file = new File(filePath);
                fileAll.add(file);
				try {
                    if (!file.getCanonicalPath().startsWith(Environment.getExternalStorageDirectory().getCanonicalPath())) {
                        BBLog.e("DownloadService", "Path Traversal");
                    }
                } catch (IOException e) {
                    e.printStackTrace();
                }
                if (file.exists()) {
                    if (FileUtils.isFileMd5Correct(filePath, fileMd5)) {
                        BBLog.e(BBLog.TAG, filePath + "文件已存在");
                        if ("C05".equals(command)) {    // C05 iptable下载不存在多个任务，直接结束
                            WebSocketServiceListManager.updateWSServiceAppState(service_app_json.getString(ParameterName.taskId), RuleStatus.IMPLEMENTED);
                            WebSocketSender.C0108_uploadWSServiceAppResult(service_app_json, TaskState.INSTALL_SUCCESS, "");
                            WebSocketServiceListManager.removeWSServiceJsonObjByTaskId(service_app_json.getString(ParameterName.taskId));
                            return;
                        }
                        continue;
                    } else {
                        file.delete();
                        file.createNewFile();
                    }
                }

                //上送下载中报文
                if (!TextUtils.isEmpty(fileKey)) {
                    String response = "";
                    for (int i=0; i<3; i++) {
                        response = HttpUtils.postGetFileUrl(Constants.S3_RESOURCE_URL, HttpUtils.getPostTokenRequestContent(fileKey));
                        if (!TextUtils.isEmpty(response)) {
                            break;
                        }
                    }
                    if (TextUtils.isEmpty(response)) {
                        download_totally = false;
                        continue;
                    } else {
                        fileUrl = response;
                    }
                }
                HttpUtils.fileDownloadByUrlWithRetry(fileUrl, filePath, fileSize, fileMd5, new HttpUtils.FileDownloadCallBack() {
                    @Override
                    public void requestSuccess(JSONObject responseJson) throws Exception {
                        // TODO Auto-generated method stub
                    }

                    @Override
                    public void requestFail(int errorCode, String errorStr) {
                        // TODO Auto-generated method stub
                        download_totally = false;
                        BBLog.e(Constants.TAG, "error in download apk file" + errorStr);
                    }

                    @Override
                    public void onDownloading(long curFileSize, long fileSize) {
                        //下载进度反馈 TODO
                        BBLog.w(BBLog.TAG, "onDownloading " + (int) ((curFileSize * 100) / fileSize) + "%");
                    }
                });
            }

            if (download_totally) {
                //如果是C05，则删除之前已经执行成功的service app记录，只保留一条
                if ("C05".equals(command)) {
                    WebSocketServiceListManager.removeWSServiceJsonImplementC05();
                } else if ("C07".equals(command)) {
                    try {
                        File[] files = new File[0];
                        File file = new File("/bbpos/rom/tls");
                        if (file.exists() && file.isDirectory()) {
                            files = file.listFiles();
                        }
                        for(File fileTemp : files){
                            if (!fileAll.contains(fileTemp)) {
                                fileTemp.delete();
                            } else {
                                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                                    BBLog.e(BBLog.TAG, "Add permission to certification: " + fileTemp.getAbsolutePath());
                                    setCAFilePermission(fileTemp.getAbsolutePath());
                                }
                            }
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
                WebSocketServiceListManager.updateWSServiceAppState(service_app_json.getString(ParameterName.taskId), RuleStatus.IMPLEMENTED);
                WebSocketSender.C0108_uploadWSServiceAppResult(service_app_json, TaskState.INSTALL_SUCCESS, "");
                WebSocketSender.C0901_AppInfoUpload();
            } else {
                try {
                    WebSocketServiceListManager.updateWSServiceAppState(service_app_json.getString(ParameterName.taskId), RuleStatus.READY);
                } catch (JSONException e) {
                    e.printStackTrace();
                }
                WebSocketSender.C0108_uploadWSServiceAppResult(service_app_json, TaskState.DOWNLOAD_FAILED, null);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public IBinder onBind(Intent intent) {
        return null;
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
    }

    @RequiresApi(api = Build.VERSION_CODES.O)
    private void setCAFilePermission(String filePathStr) {
        Set<PosixFilePermission> perms = new HashSet<>();

        perms.add(PosixFilePermission.OWNER_READ);
        perms.add(PosixFilePermission.OWNER_WRITE);
        perms.add(PosixFilePermission.GROUP_READ);
        perms.add(PosixFilePermission.OTHERS_READ);

        try {
            Files.setPosixFilePermissions(Paths.get(filePathStr), perms);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }
}
