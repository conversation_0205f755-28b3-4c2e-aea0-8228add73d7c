package com.bbpos.wiseapp.websocket;

import android.app.Dialog;
import android.app.Notification;
import android.app.NotificationManager;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import android.content.pm.ApplicationInfo;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.os.Build;
import android.os.Handler;
import android.os.Looper;
import android.preference.PreferenceManager;
import android.text.TextUtils;
import android.view.View;
import android.view.WindowManager;
import android.widget.ImageView;
import android.widget.TextView;

import com.bbpos.wiseapp.logger.BBLog;
import com.bbpos.wiseapp.provisioning.ProvisionDownloadService;
import com.bbpos.wiseapp.service.ListeningService;
import com.bbpos.wiseapp.service.R;
import com.bbpos.wiseapp.service.WakeLockService;
import com.bbpos.wiseapp.service.common.RequestPermission;
import com.bbpos.wiseapp.service.common.UsualData;
import com.bbpos.wiseapp.system.api.DeviceInfoApi;
import com.bbpos.wiseapp.system.api.SystemApi;
import com.bbpos.wiseapp.tms.adapter.SystemManagerAdapter;
import com.bbpos.wiseapp.tms.network.HttpUtils;
import com.bbpos.wiseapp.tms.utils.BroadcastActions;
import com.bbpos.wiseapp.tms.utils.Constants;
import com.bbpos.wiseapp.tms.utils.ContextUtil;
import com.bbpos.wiseapp.tms.utils.DateTimeUtils;
import com.bbpos.wiseapp.tms.utils.Helpers;
import com.bbpos.wiseapp.tms.utils.ParameterName;
import com.bbpos.wiseapp.tms.utils.RuleStatus;
import com.bbpos.wiseapp.tms.utils.RulebasedAppListManager;
import com.bbpos.wiseapp.tms.model.AppInfo;
import com.bbpos.wiseapp.tms.utils.SPKeys;
import com.bbpos.wiseapp.tms.utils.TaskState;
import com.bbpos.wiseapp.tms.utils.WebSocketServiceListManager;
import com.bbpos.wiseapp.tms.utils.WebSocketTaskListManager;
import com.bbpos.wiseapp.tms.widget.MRulebasedDialog;
import com.bbpos.wiseapp.utils.ActivityUtils;
import com.bbpos.wiseapp.utils.SharedPreferencesUtils;
import com.bbpos.wiseapp.websocket.handler.RulebasedListHandler;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Set;

public class RulebasedService extends WakeLockService {
    public static final String TAG = "RuleTask";
    private Handler mHandler = new Handler(Looper.getMainLooper());
    public static boolean isRuleExecuting = false;
    public static final SharedPreferences sp = PreferenceManager.getDefaultSharedPreferences(ContextUtil.getInstance());
    public static final HashMap<Long, String> ruleExecuteTimeMap = new HashMap<Long, String>();
    public static final HashMap<Long, String> ruleExpireTimeMap = new HashMap<Long, String>();
    private static Dialog lowBatdialog;
    private int total = 0;
    private int count = 0;
    private String m_ruleExecuteId;
    private int m_ruleExecuteCount = 0;
    private AppInfo m_appInfo;
    public static JSONArray resultApkListArray = new JSONArray();
    private NotificationManager manager = null;
    public static Long mCurTaskExeTime = Long.valueOf(-1);
    public static boolean anyFailed = false;
    private int m_totalInstallCount = 0;
    public static final HashMap<String, String> appsToDownload = new HashMap<String, String>();
    public static final HashMap<String, String> appsToDelete = new HashMap<String, String>();
    public static int try_count = 0;
    public static boolean restart_flag = false;
    SimpleDateFormat sdf = new SimpleDateFormat(Constants.dateFormat);

    public RulebasedService() {
        super("RulebasedService");
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        if (manager != null) {
            manager.cancel(2);
        }

        isRuleExecuting = false;
    }

    public static boolean isRuleNeedToDo() {
        List<Integer> toDeleteIndexs = new ArrayList<Integer>();
        JSONArray localRuleList = new JSONArray();
        try {
            Constants.rule_query_count++;
            String localAppListStr = sp.getString(SPKeys.RULEBASED_LIST_APP_LIST, "");
            String localRuleListStr = sp.getString(SPKeys.WEBSOCKET_RULEBASED_LIST, "");
            if (Constants.rule_query_count%5==0) {
                BBLog.i(BBLog.TAG, "本地当前记录的各条RULE APPList = " + localAppListStr);
                BBLog.i(BBLog.TAG, "本地当前存在的 Rule = " + localRuleListStr);
                Constants.rule_query_count = 0;
            }
            if (!Helpers.isStrNoEmpty(localRuleListStr)) {
                return false;
            }
            ruleExecuteTimeMap.clear();
            ruleExpireTimeMap.clear();
            localRuleList = new JSONArray(localRuleListStr);
            BBLog.i(TAG, "WEBSOCKET_RULEBASED_LIST length = " + localRuleList.length());
            for (int i = 0; i < localRuleList.length(); i++) {
                JSONObject localRulebasedJsonObj = (JSONObject) localRuleList.get(i);
                try {
                    if (RuleStatus.READY.equals(localRulebasedJsonObj.getString(ParameterName.ruleStatus))
                            || RuleStatus.IMPLEMENTED.equals(localRulebasedJsonObj.getString(ParameterName.ruleStatus))
                            || RuleStatus.READY_TO_FALLBACK.equals(localRulebasedJsonObj.getString(ParameterName.ruleStatus))) {
                        String beginDateStr = localRulebasedJsonObj.getString(ParameterName.beginDate);
                        String endDateStr = localRulebasedJsonObj.getString(ParameterName.endDate);
                        String modifyDateStr = "";
                        if (localRulebasedJsonObj.has(ParameterName.modifyDate)) {
                            modifyDateStr = localRulebasedJsonObj.getString(ParameterName.modifyDate);
                        }
                        long nowTime = System.currentTimeMillis();
                        long modifyTime = 0;
                        long beginTime;
                        long endTime;
                        if (DateTimeUtils.isNumeric(beginDateStr) && DateTimeUtils.isNumeric(endDateStr) && DateTimeUtils.isNumeric(modifyDateStr)) {
                            if (!TextUtils.isEmpty(modifyDateStr)) {
                                modifyTime = new Long(modifyDateStr).longValue();
                            }
                            beginTime = new Long(beginDateStr).longValue();
                            endTime = new Long(endDateStr).longValue();
                        } else {
                            SimpleDateFormat sdf = new SimpleDateFormat(Constants.dateFormat);
                            if (!TextUtils.isEmpty(modifyDateStr)) {
                                modifyTime = sdf.parse(modifyDateStr).getTime();
                            }
                            beginTime = sdf.parse(beginDateStr).getTime();
                            endTime = sdf.parse(endDateStr).getTime();
                        }

                        if (beginTime < nowTime && nowTime < endTime) {
                            //到时执行
                            if (RuleStatus.READY.equals(localRulebasedJsonObj.getString(ParameterName.ruleStatus))) {
                                if (!TextUtils.isEmpty(modifyDateStr)) {
                                    ruleExecuteTimeMap.put(modifyTime, localRulebasedJsonObj.toString());
                                } else {
                                    ruleExecuteTimeMap.put(beginTime, localRulebasedJsonObj.toString());
                                }
                            } else if (RuleStatus.READY_TO_FALLBACK.equals(localRulebasedJsonObj.getString(ParameterName.ruleStatus))) {
                                if (!TextUtils.isEmpty(modifyDateStr)) {
                                    ruleExpireTimeMap.put(modifyTime, localRulebasedJsonObj.toString());
                                } else {
                                    ruleExpireTimeMap.put(beginTime, localRulebasedJsonObj.toString());
                                }
                            }
                        } else if (endTime < nowTime) {
                            //过期执行
                            if (RuleStatus.READY_TO_FALLBACK.equals(localRulebasedJsonObj.getString(ParameterName.ruleStatus))) {
                                if (!TextUtils.isEmpty(modifyDateStr)) {
                                    ruleExpireTimeMap.put(modifyTime, localRulebasedJsonObj.toString());
                                } else {
                                    ruleExpireTimeMap.put(beginTime, localRulebasedJsonObj.toString());
                                }
                            } else if (RuleStatus.IMPLEMENTED.equals(localRulebasedJsonObj.getString(ParameterName.ruleStatus))) {
                                localRulebasedJsonObj.put(ParameterName.ruleStatus, RuleStatus.READY_TO_FALLBACK);
                                if (!TextUtils.isEmpty(modifyDateStr)) {
                                    ruleExpireTimeMap.put(modifyTime, localRulebasedJsonObj.toString());
                                } else {
                                    ruleExpireTimeMap.put(beginTime, localRulebasedJsonObj.toString());
                                }
                            } else {
                                toDeleteIndexs.add(new Integer(i));
                                localRulebasedJsonObj.put(ParameterName.ruleStatus, RuleStatus.COMPLETED);
                                WebSocketSender.C0107_uploadRulebasedResult(localRulebasedJsonObj.getString(ParameterName.ruleId), TaskState.RULEBASED_EXPIRED, null);
                            }
                        }
                    } else if (RuleStatus.COMPLETED.equals(localRulebasedJsonObj.getString(ParameterName.ruleStatus))) {
                        toDeleteIndexs.add(new Integer(i));
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        //删除标志为delete的任务
        if (Build.VERSION.SDK_INT>=Build.VERSION_CODES.KITKAT) {
            for (int i = toDeleteIndexs.size()-1; i >= 0; i--) {
                localRuleList.remove(toDeleteIndexs.get(i));
            }
        } else{
            for (int i = toDeleteIndexs.size() - 1; i >= 0; i--) {
                localRuleList = Helpers.remove(localRuleList, toDeleteIndexs.get(i));
            }
        }
        if (ruleExpireTimeMap.size() > 0 || ruleExecuteTimeMap.size() > 0) {
            sp.edit().putString(SPKeys.WEBSOCKET_RULEBASED_LIST, localRuleList.toString()).commit();
            return true;
        }
        sp.edit().putString(SPKeys.WEBSOCKET_RULEBASED_LIST, localRuleList.toString()).commit();
        return false;
    }

    @Override
    protected void onHandleIntent(final Intent intent) {
        isRuleExecuting = true;
        mCurTaskExeTime = Long.valueOf(-1);
        m_ruleExecuteCount = ruleExecuteTimeMap.size();
        try {
            int totalcount = m_ruleExecuteCount + ruleExpireTimeMap.size();
            BBLog.e(TAG, "ruleExpireTimeMap size = " + ruleExpireTimeMap.size());
            BBLog.e(TAG, "ruleExecuteTimeMap size = " + ruleExecuteTimeMap.size());
            BBLog.e(TAG, "################## Start EXECUTING Rulebased：total count " + totalcount + " ###################");
            //先计算多条rule时候的重复情况；
            appsToDownload.clear();
            appsToDelete.clear();
            if (m_ruleExecuteCount > 1) {
                Set<Long> taskExeTimeSet = ruleExecuteTimeMap.keySet();
                Iterator<Long> taskExeTimeIterator = taskExeTimeSet.iterator();
                Long modifyTimeLast = Long.MIN_VALUE;
                while (taskExeTimeIterator.hasNext()) {
                    Long timeTmp = taskExeTimeIterator.next();
                    BBLog.e(TAG, "found a rule，modifyDate is " + Helpers.getTransDateStr(timeTmp));
                    JSONObject ruleJsonObject = new JSONObject(ruleExecuteTimeMap.get(timeTmp));
                    if (ruleJsonObject.has(ParameterName.modifyDate)) {
                        String ruleId = ruleJsonObject.getString(ParameterName.ruleId);
                        String modifyDate = ruleJsonObject.getString(ParameterName.modifyDate);
                        Long modifyTime = sdf.parse(modifyDate).getTime();
                        if (ruleJsonObject.has(ParameterName.appList)) {
                            JSONArray appList = ruleJsonObject.getJSONArray(ParameterName.appList);
                            for (int idx = 0; idx < appList.length(); idx++) {
                                JSONObject appJson = appList.getJSONObject(idx);
                                String packName = appJson.getString(ParameterName.packName);
                                if (appsToDownload.containsKey(packName)) {
                                    JSONObject appJsonLast = new JSONObject(appsToDownload.get(packName));
                                    try {
                                        modifyTimeLast = sdf.parse(appJsonLast.optString(ParameterName.modifyDate)).getTime();
                                    } catch (ParseException e) {
                                        e.printStackTrace();
                                        modifyTimeLast = 0L;
                                    }
                                    BBLog.e(BBLog.TAG, "找到重复APP - " + packName);
                                    BBLog.e(BBLog.TAG, "此APP之前记录的modifyDate - " + Helpers.getTransDateStr(modifyTimeLast));
                                    BBLog.e(BBLog.TAG, "新APP现在存在的ModifyDate - " + Helpers.getTransDateStr(modifyTime));
                                    if (modifyTime > modifyTimeLast) {
                                        BBLog.e(BBLog.TAG, "替换新的APP");
                                        appJson.put(ParameterName.ruleId, ruleId);
                                        appJson.put(ParameterName.modifyDate, modifyDate);
                                        appsToDownload.put(packName, appJson.toString());
                                    }
                                } else {
                                    appJson.put(ParameterName.ruleId, ruleId);
                                    appJson.put(ParameterName.modifyDate, modifyDate);
                                    appsToDownload.put(packName, appJson.toString());
                                }
                            }
                        }

                        if (ruleJsonObject.has(ParameterName.deleteAppList)) {
                            //过滤出要删除的，相同APP不同版本可以重复；全部相同的取时间最新的
                            JSONArray appDeleteListTmp = ruleJsonObject.getJSONArray(ParameterName.deleteAppList);
                            for (int j = 0; j < appDeleteListTmp.length(); j++) {
                                JSONObject appDeleteJson = appDeleteListTmp.getJSONObject(j);
                                String versionName = appDeleteJson.getString(ParameterName.versionName);
                                String versionCode = appDeleteJson.getString(ParameterName.versionCode);
                                String packName = appDeleteJson.getString(ParameterName.packName);
//                                if (appsToDelete.containsKey(packName + "-" + versionCode)) {
                                if (appsToDelete.containsKey(packName)) {
//                                    JSONObject appDeleteJsonLast = new JSONObject(appsToDelete.get(packName + "-" + versionCode));
                                    JSONObject appDeleteJsonLast = new JSONObject(appsToDelete.get(packName));
                                    String versionNameTmp = appDeleteJsonLast.getString(ParameterName.versionName);
                                    String versionCodeTmp = appDeleteJsonLast.getString(ParameterName.versionCode);
                                    SimpleDateFormat sdf = new SimpleDateFormat(Constants.dateFormat);
                                    try {
                                        modifyTimeLast = sdf.parse(appDeleteJsonLast.optString(ParameterName.modifyDate)).getTime();
                                    } catch (ParseException e) {
                                        e.printStackTrace();
                                        modifyTimeLast = 0L;
                                    }
                                    if (modifyTime > modifyTimeLast) {
                                        appDeleteJson.put(ParameterName.ruleId, ruleId);
                                        appDeleteJson.put(ParameterName.modifyDate, modifyDate);
//                                        appsToDelete.put(packName + "-" + versionCodeTmp, appDeleteJson.toString());
                                        appsToDelete.put(packName, appDeleteJson.toString());
                                    }
                                } else {
                                    appDeleteJson.put(ParameterName.ruleId, ruleId);
                                    appDeleteJson.put(ParameterName.modifyDate, modifyDate);
//                                    appsToDelete.put(packName + "-" + versionCode, appDeleteJson.toString());
                                    appsToDelete.put(packName, appDeleteJson.toString());
                                }
                            }
                        }
                    }
                }

                for (Iterator it = appsToDownload.values().iterator(); it.hasNext(); ) {
                    JSONObject taskJsonDownload = new JSONObject((String )it.next());
                    String packName = taskJsonDownload.getString(ParameterName.packName);
                    String versionName = taskJsonDownload.getString(ParameterName.versionName);
                    String versionCode = taskJsonDownload.getString(ParameterName.versionCode);
//                    if (appsToDelete.containsKey(packName+"-"+versionCode)) {
                    if (appsToDelete.containsKey(packName)) {
//                        JSONObject taskJsonDelete = new JSONObject(appsToDelete.get(packName+"-"+versionCode));
                        JSONObject taskJsonDelete = new JSONObject(appsToDelete.get(packName));
                        if (taskJsonDownload.has(ParameterName.modifyDate) && taskJsonDelete.has(ParameterName.modifyDate)) {
                            String modifyDateStr = taskJsonDownload.getString(ParameterName.modifyDate);
                            String modifyDateStrDelete = taskJsonDelete.getString(ParameterName.modifyDate);
                            long modifyTimeDownload = 0L;
                            long modifyTimeDelete = 0L;
                            SimpleDateFormat sdf = new SimpleDateFormat(Constants.dateFormat);
                            if (DateTimeUtils.isNumeric(modifyDateStr) && DateTimeUtils.isNumeric(modifyDateStrDelete)) {
                                modifyTimeDownload = new Long(modifyDateStr).longValue();
                                modifyTimeDelete = new Long(modifyDateStrDelete).longValue();
                            } else {
                                modifyTimeDownload = sdf.parse(modifyDateStr).getTime();
                                modifyTimeDelete = sdf.parse(modifyDateStrDelete).getTime();
                            }
                            if (modifyTimeDownload > modifyTimeDelete) {
                                //如果要求刪除的 modify 更新，那麽直接不安裝；
//                                appsToDelete.remove(packName+"-"+versionCode);
                                appsToDelete.remove(packName);
                            } else {
                                it.remove();
                            }
                        }
                    }
                }
                BBLog.e(BBLog.TAG, "提取删除APP列表：" + appsToDelete.toString());
                BBLog.e(BBLog.TAG, "提取目標APP列表：" + appsToDownload.toString());
            }

            //开始实行
            while (ruleExpireTimeMap.size() > 0 || ruleExecuteTimeMap.size() > 0) {
                BBLog.e(TAG, "ruleExpireTimeMap size = " + ruleExpireTimeMap.size());
                BBLog.e(TAG, "ruleExecuteTimeMap size = " + ruleExecuteTimeMap.size());
                if (ruleExpireTimeMap.size() > 0) {
                    Set<Long> taskExeTimeSet = ruleExpireTimeMap.keySet();
                    Iterator<Long> taskExeTimeIterator = taskExeTimeSet.iterator();
                    Long taskExeTime = Long.MIN_VALUE;
                    while (taskExeTimeIterator.hasNext()) {
                        Long timeTmp = taskExeTimeIterator.next();
                        if (timeTmp > taskExeTime) {
                            taskExeTime = timeTmp;
                        }
                    }

                    String ruleId = "";
                    JSONArray appList = null;
                    JSONArray serviceList = null;
                    JSONObject responseData = new JSONObject(ruleExpireTimeMap.get(taskExeTime));
                    ruleId = responseData.getString(ParameterName.ruleId);
                    m_ruleExecuteId = ruleId;
                    mCurTaskExeTime = taskExeTime;
                    WebSocketSender.C0107_uploadRulebasedResult(m_ruleExecuteId, TaskState.RULEBASED_STARTING, null);
                    BBLog.e(BBLog.TAG, "开始执行过期删除的Rule = " + responseData.toString());
                    //  此处service app的到期执行动作，放到ACTION_TIME_TICK的检查中去做，避免重复执行
                    if (responseData.has(ParameterName.serviceList) && responseData.getJSONArray(ParameterName.serviceList).length()>0) {
                        serviceList = responseData.getJSONArray(ParameterName.serviceList);
                        //加入到存储列表中
                        for (int j=0;j<serviceList.length();j++) {
                            JSONObject serviceJson = serviceList.getJSONObject(j);
                            serviceJson.put(ParameterName.taskId, ruleId+serviceJson.getString(ParameterName.serviceId));
                        }
                        BBLog.e(BBLog.TAG, "更新設置要刪除的service app為 READY_TO_FALLBACK 狀態 = " + serviceList.toString());
                        WebSocketServiceListManager.updateServiceList("", "", serviceList, responseData.optString(ParameterName.beginDate), responseData.optString(ParameterName.endDate), RuleStatus.READY_TO_FALLBACK);
                    }
                    if (responseData.has(ParameterName.deleteApps) && "1".equals(responseData.getString(ParameterName.deleteApps)) && responseData.has(ParameterName.appList) && responseData.getJSONArray(ParameterName.appList).length()>0) {
                        appList = responseData.getJSONArray(ParameterName.appList);
                        count = 0;
                        total = appList.length();

                        //开始逐个删除
                        for (int index = 1; index <= appList.length(); index++) {
                            JSONObject taskJson = appList.getJSONObject(index - 1);

                            final String packName = taskJson.getString(ParameterName.packName);
                            final String versionName = taskJson.getString(ParameterName.versionName);
                            final String versionCode = taskJson.getString(ParameterName.versionCode);
                            final String apkName = taskJson.getString(ParameterName.apkName);

                            int nRet = RulebasedAppListManager.isApkInstalled(taskJson);
                            if (nRet == 1) {
                                //如果是APP+推送的APP，優先級高於Rulebased，不刪除
                                if (WebSocketTaskListManager.isAppFromAppPlus(packName, versionName, versionCode)) {
                                    continue;
                                }

                                String ret = RulebasedListHandler.isRulebasedListApp(packName, versionName, versionCode);
                                if ("true".equals(ret) && !ret.startsWith("true_downgrade")) {
                                    continue;
                                }

                                if (ret.startsWith("true_downgrade")) {
                                    String ruleIdTmp = ret.substring("true_downgrade".length());
                                    JSONObject ruleTodo = RulebasedListHandler.getWSRuleJsonObjById(ruleIdTmp);
                                    BBLog.i(BBLog.TAG, "执行过期删除后需要重新做的Rule = " + ruleTodo);
                                    if (ruleTodo != null) {
                                        try {
                                            RulebasedListHandler.updateRuleState(ruleIdTmp, RuleStatus.READY);
                                            String beginDateStrTmp = ruleTodo.getString(ParameterName.beginDate);
                                            Long beginTimeTmp;
                                            if (DateTimeUtils.isNumeric(beginDateStrTmp)) {
                                                beginTimeTmp = new Long(beginDateStrTmp).longValue();
                                            } else {
                                                SimpleDateFormat sdf = new SimpleDateFormat(Constants.dateFormat);
                                                beginTimeTmp = sdf.parse(beginDateStrTmp).getTime();
                                            }
                                            ruleExecuteTimeMap.put(beginTimeTmp, ruleTodo.toString());
                                        } catch (JSONException e) {
                                            e.printStackTrace();
                                        }
                                    }
                                }
                                SystemManagerAdapter.unInstallApk(ContextUtil.getInstance(), packName, new SystemManagerAdapter.ApkUnInstallCompleted() {
                                    @Override
                                    public void onDeleteFinished(int returnCode) {
                                        BBLog.i(Constants.TAG, "packageDeleted " + packName + " returnCode " + returnCode);
                                        if (returnCode == 1) {
                                        } else {
                                        }
                                    }
                                });
                            }
                        }
                    }
                    RulebasedListHandler.updateRuleState(responseData.getString(ParameterName.ruleId), RuleStatus.COMPLETED);
                    WebSocketSender.C0107_uploadRulebasedResult(responseData.getString(ParameterName.ruleId), TaskState.RULEBASED_EXPIRED, null);
                    RulebasedListHandler.removeRuleJsonObjById(responseData.getString(ParameterName.ruleId));
                    RulebasedListHandler.removeAppList(ruleId);
                    ruleExpireTimeMap.remove(taskExeTime);
                } else if (ruleExecuteTimeMap.size() > 0) {
                    Set<Long> taskExeTimeSet = ruleExecuteTimeMap.keySet();
                    Iterator<Long> taskExeTimeIterator = taskExeTimeSet.iterator();
                    Long taskExeTime = Long.MIN_VALUE;
                    while (taskExeTimeIterator.hasNext()) {
                        Long timeTmp = taskExeTimeIterator.next();
                        if (timeTmp > taskExeTime) {
                            taskExeTime = timeTmp;
                        }
                    }

                    if (taskExeTime == mCurTaskExeTime) {
                        BBLog.w(BBLog.TAG, "--- 等待上一條Rule的執行 ---");
                        Thread.sleep(3000);
                        continue;
                    }

                    String ruleId = "";
                    JSONArray appList = null;
                    JSONArray appDeleteList = null;
                    JSONArray serviceList = null;
                    JSONObject responseData = new JSONObject(ruleExecuteTimeMap.get(taskExeTime));
                    BBLog.e(BBLog.TAG, "开始执行需要安装的Rule = " + responseData.toString());
                    BBLog.e(BBLog.TAG, "Rule的ModifyDate = " + Helpers.getTransDateStr(taskExeTime));
                    ruleId = responseData.getString(ParameterName.ruleId);
                    m_ruleExecuteId = ruleId;
                    mCurTaskExeTime = taskExeTime;
                    boolean allowDowngrade = responseData.has(ParameterName.allowDowngrade) ? responseData.getBoolean(ParameterName.allowDowngrade) : true;
                    boolean forceInstall = responseData.has(ParameterName.forceInstall) ? responseData.getBoolean(ParameterName.forceInstall) : false;
                    WebSocketSender.C0107_uploadRulebasedResult(m_ruleExecuteId, TaskState.RULEBASED_STARTING, null);
                    if (responseData.has(ParameterName.serviceList) && responseData.getJSONArray(ParameterName.serviceList).length()>0) {
                        serviceList = responseData.getJSONArray(ParameterName.serviceList);
                        //加入到存储列表中
                        for (int j=0;j<serviceList.length();j++) {
                            JSONObject serviceJson = serviceList.getJSONObject(j);
                            serviceJson.put(ParameterName.taskId, ruleId+serviceJson.getString(ParameterName.serviceId));
                        }
                        WebSocketServiceListManager.updateServiceList("", "", serviceList, responseData.optString(ParameterName.beginDate), responseData.optString(ParameterName.endDate), RuleStatus.READY);

                        RulebasedListHandler.updateRuleState(ruleId, RuleStatus.IMPLEMENTED);
                        WebSocketSender.C0107_uploadRulebasedResult(responseData.getString(ParameterName.ruleId), TaskState.RULEBASED_SUCCESS, null);
                        ruleExecuteTimeMap.remove(taskExeTime);
                    }
                    if ((responseData.has(ParameterName.appList) && responseData.getJSONArray(ParameterName.appList).length()>0) || (responseData.has(ParameterName.deleteAppList) && responseData.getJSONArray(ParameterName.deleteAppList).length()>0)) {
                        anyFailed = false;
                        MRulebasedDialog.mRulebasedDeletelist.clear();
                        MRulebasedDialog.mRulebasedlist.clear();
                        createResultAppList(responseData.optJSONArray(ParameterName.appList), responseData.optJSONArray(ParameterName.deleteAppList));
                        if (responseData.has(ParameterName.deleteAppList) && responseData.getJSONArray(ParameterName.deleteAppList).length()>0) {
                            appDeleteList = responseData.getJSONArray(ParameterName.deleteAppList);
                            for (int index = 1; index <= appDeleteList.length(); index++) {
                                try {
                                    JSONObject taskJson = appDeleteList.getJSONObject(index - 1);

                                    final String packName = taskJson.getString(ParameterName.packName);
                                    final String versionName = taskJson.getString(ParameterName.versionName);
                                    final String versionCode = taskJson.getString(ParameterName.versionCode);
                                    final String apkName = taskJson.getString(ParameterName.apkName);
                                    final String appId = taskJson.getString(ParameterName.appId);

//                                    if (m_ruleExecuteCount > 1 && appsToDelete != null && appsToDelete.size()>0 && !appsToDelete.containsKey(packName+"-"+versionCode)) {
                                    if (m_ruleExecuteCount > 1 && appsToDelete != null && !appsToDelete.containsKey(packName)) {
                                        updateResultAppStatus(packName, TaskState.UNINSTALL_SUCCESS, null);
                                        BBLog.w(BBLog.TAG, "Rule的deleteAppList中要求刪除的" + apkName + "-" + versionCode + " 不在最終的删除列表中，跳过");
                                        continue;
                                    }

                                    AppInfo appInfo = new AppInfo();
                                    appInfo.setApk_name(apkName);
                                    appInfo.setPackage_name(packName);
                                    appInfo.setVersion_name(versionName);
                                    appInfo.setVersion_code(versionCode);
                                    MRulebasedDialog.mRulebasedDeletelist.add(appInfo);
                                } catch (Exception e) {
                                    e.printStackTrace();
                                }
                            }
                        }
                        if (responseData.has(ParameterName.appList) && responseData.getJSONArray(ParameterName.appList).length()>0) {
                            appList = responseData.getJSONArray(ParameterName.appList);
                            count = 0;
                            total = appList.length();
                            BBLog.e(BBLog.TAG, "需要安装app个数 " + appList.length());

                            String channelID = "2";
                            String channelName = "channel_2";
                            manager = SystemApi.createCustomnNotificationManager(RulebasedService.this, channelID, channelName);
                            Notification notification = SystemApi.createCustomnNotification(RulebasedService.this, channelID, android.R.drawable.stat_sys_download, getString(R.string.app_download), getString(R.string.downloading));
                            manager.notify(2, notification);

                            //开始逐个下载
                            for (int index = 1; index <= appList.length(); index++) {
                                JSONObject taskJson = appList.getJSONObject(index - 1);

                                final String packName = taskJson.getString(ParameterName.packName);
                                final String versionName = taskJson.getString(ParameterName.versionName);
                                final String versionCode = taskJson.getString(ParameterName.versionCode);
                                String iconUrl = "";
                                if (taskJson.has(ParameterName.appIconUrlEx)) {
                                    iconUrl = taskJson.getString(ParameterName.appIconUrlEx);
                                }
                                final String finalIconUrl = iconUrl;
                                final String apkName = taskJson.getString(ParameterName.apkName);
                                final String appId = taskJson.getString(ParameterName.appId);
                                final String apkMd5 = taskJson.getString(ParameterName.apkMd5);
                                final String fileKey = taskJson.optString(ParameterName.fileKey);
                                final long totalfileSize = Long.parseLong(taskJson.getString(ParameterName.apkSize));
                                String fileUrl = taskJson.getString(ParameterName.url);
                                final String filePath = Constants.APK_FILE_PATH + appId;

                                count++;
                                if (m_ruleExecuteCount > 1 && appsToDownload != null) {// && appsToDownload.size() > 0) {
                                    if (appsToDownload.containsKey(packName)) {
                                        JSONObject appJson = new JSONObject(appsToDownload.get(packName));
                                        if (!versionName.equals(appJson.getString(ParameterName.versionName))
                                                || !versionCode.equals(appJson.getString(ParameterName.versionCode))) {
                                            BBLog.w(BBLog.TAG, appJson.optString(ParameterName.apkName) + " 存在多条rule中的多版本情况，目标为安装" + appJson.getString(ParameterName.versionName) + "-" + appJson.getString(ParameterName.versionCode) + "版本，此版本忽略掉");
                                            updateResultAppStatus(packName, TaskState.INSTALL_OVERRIDE, null);
                                            WebSocketSender.C0107_uploadRulebasedResult(responseData.getString(ParameterName.ruleId), TaskState.RULEBASED_EXECUTING, resultApkListArray);
                                            continue;
                                        } else {
                                            if (!appJson.optString(ParameterName.ruleId).equals(ruleId)) {
                                                BBLog.w(BBLog.TAG, appJson.optString(ParameterName.apkName) + " 存在多条rule中的多版本情况，目标为安装" + appJson.getString(ParameterName.versionName) + "-" + appJson.getString(ParameterName.versionCode) + "版本，此版本忽略掉");
                                                updateResultAppStatus(packName, TaskState.INSTALL_OVERRIDE, null);
                                                WebSocketSender.C0107_uploadRulebasedResult(responseData.getString(ParameterName.ruleId), TaskState.RULEBASED_EXECUTING, resultApkListArray);
                                                continue;
                                            }
                                        }
//                                    } else if (appsToDelete.containsKey(packName+"-"+versionCode)) {
                                    } else if (appsToDelete.containsKey(packName)) {
                                        BBLog.w(BBLog.TAG, "Rule的appList中要求安裝的" + apkName + "-" + versionCode + " 在最終的删除列表中，跳过下載安裝");
                                        updateResultAppStatus(packName, TaskState.INSTALL_SUCCESS, null);
                                        WebSocketSender.C0107_uploadRulebasedResult(responseData.getString(ParameterName.ruleId), TaskState.RULEBASED_EXECUTING, resultApkListArray);
                                        continue;
                                    }
                                }

                                if (taskJson.has(ParameterName.appStatus) && taskJson.getString(ParameterName.appStatus).equals(TaskState.INSTALL_FAILED)) {
                                    updateResultAppStatus(packName, TaskState.INSTALL_FAILED, null);
                                    WebSocketSender.C0107_uploadRulebasedResult(responseData.getString(ParameterName.ruleId), TaskState.RULEBASED_EXECUTING, resultApkListArray);
                                    BBLog.w(Constants.TAG, "WebSocket rule 推送的 ["+taskJson.getString(ParameterName.packName)+"] 已存在,不處理，跳過此任務");
                                    continue;
                                }

                                int ret = RulebasedAppListManager.isApkInstalled(taskJson);
                                if (ret == 1 || ret == 2) {
                                    //判断是否是ota apk
                                   //2020年5月6日 Andy：針對7-11 客戶，若推送的ota app，在本機上已經安裝過同版本的情況下，跳過任務，不做處理
                                   if (UsualData.SYSTEM_OTA_TRIGGER_APP_PACKAGENAME.equals(taskJson.getString(ParameterName.packName))
												|| UsualData.SYSTEM_SP_OTA_TRIGGER_APP_PACKAGENAME.equals(taskJson.getString(ParameterName.packName))){
									   updateResultAppStatus(packName, TaskState.INSTALL_SUCCESS, null);
									   WebSocketSender.C0107_uploadRulebasedResult(responseData.getString(ParameterName.ruleId), TaskState.RULEBASED_EXECUTING, resultApkListArray);
									   BBLog.w(Constants.TAG, "WebSocket rule 推送的 ["+taskJson.getString(ParameterName.packName)+"] 已存在,不處理，跳過此任務");
									   continue;
									}

									if (
										//  UsualData.SYSTEM_WISEPOS_OTA_TRIGGER_APP_PACKAGENAME.equals(taskJson.getString(ParameterName.packName))
//												|| UsualData.SYSTEM_SP_OTA_TRIGGER_APP_PACKAGENAME.equals(taskJson.getString(ParameterName.packName))
//												||
											UsualData.SYSTEM_WISEPOS_OTA_TRIGGER_APP_PACKAGENAME.equals(taskJson.getString(ParameterName.packName))
                                            || UsualData.SYSTEM_TMT_OTA_TRIGGER_APP_PACKAGENAME.equals(taskJson.getString(ParameterName.packName))
                                            || UsualData.SYSTEM_KEY_UPDATE_TRIGGER_APP_PACKAGENAME.equals(taskJson.getString(ParameterName.packName))) {
//                                    Helpers.updateWSTaskStateAndUpload(ContextUtil.getInstance(), taskJson.getString(ParameterName.taskId), TaskState.SUCCESSED);
                                        prepareToUpdate(ContextUtil.getInstance(), taskJson);
                                    }

                                    if (ret == 1 || (ret==2 && UsualData.LAUNCHER_711_PACKAGE_NAME.equals(packName))) {
                                        updateResultAppStatus(packName, TaskState.INSTALL_SUCCESS, null);
                                        WebSocketSender.C0107_uploadRulebasedResult(responseData.getString(ParameterName.ruleId), TaskState.RULEBASED_EXECUTING, resultApkListArray);
										continue;
                                    }

                                    if (ret == 2 && !allowDowngrade) {
                                        updateResultAppStatus(packName, TaskState.UPDATE_DOWNGRADE_FORBIDDEN, null);
                                        WebSocketSender.C0107_uploadRulebasedResult(responseData.getString(ParameterName.ruleId), TaskState.RULEBASED_EXECUTING, resultApkListArray);
                                        continue;
                                    }
                                }

                                BBLog.w(TAG, "start download Rulebased APP: " + apkName);
                                if (!TextUtils.isEmpty(fileKey)) {
                                    String response_str = "";
                                    for (int i=0; i<3; i++) {
                                        response_str = HttpUtils.postGetFileUrl(Constants.S3_RESOURCE_URL, HttpUtils.getPostTokenRequestContent(fileKey));
                                        if (!TextUtils.isEmpty(response_str)) {
                                            break;
                                        }
                                    }
                                    if (TextUtils.isEmpty(response_str)) {
                                        anyFailed = true;
                                        updateResultAppStatus(packName, TaskState.DOWNLOAD_FAILED, "APK file downloaded failed");
                                        continue;
                                    } else {
                                        fileUrl = response_str;
                                    }
                                }
                                updateResultAppStatus(packName, TaskState.DOWNLOAD_ING, null);
                                WebSocketSender.C0107_uploadRulebasedResult(responseData.getString(ParameterName.ruleId), TaskState.RULEBASED_EXECUTING, resultApkListArray);
                                HttpUtils.fileDownloadByUrlWithRetry(fileUrl, filePath, totalfileSize, apkMd5, new HttpUtils.FileDownloadCallBack() {
                                    @Override
                                    public void requestSuccess(JSONObject responseJson) throws Exception {
                                        // TODO Auto-generated method stub
                                        //上送安装中报文
                                        String newApkPath = null;
                                        // 目前仅适用增量包处理安装
                                        newApkPath = Constants.APK_FILE_PATH + appId;
                                        BBLog.e(TAG, apkName + " download completed");

                                        AppInfo appInfo = new AppInfo();
                                        appInfo.setApk_name(apkName);
                                        appInfo.setPackage_name(packName);
                                        appInfo.setVersion_name(versionName);
                                        appInfo.setVersion_code(versionCode);
                                        appInfo.setIcon_url(finalIconUrl);
                                        appInfo.setInstall_path(newApkPath);
                                        MRulebasedDialog.mRulebasedlist.add(appInfo);

                                        updateResultAppStatus(packName, TaskState.DOWNLOAD_SUCCESS, null);
                                    }

                                    @Override
                                    public void requestFail(int errorCode, String errorStr) {
                                        // TODO Auto-generated method stub
                                        BBLog.e(TAG, "error in download apk file" + errorStr);
                                        BBLog.w(TAG, "count=" + count + "  total=" + total);

                                        anyFailed = true;
                                        updateResultAppStatus(packName, TaskState.DOWNLOAD_FAILED, "APK file downloaded failed");
                                    }

                                    @Override
                                    public void onDownloading(long curFileSize, long fileSize) {
                                        //下载进度反馈 TODO
                                        BBLog.w(BBLog.TAG, "onDownloading " + (int) ((curFileSize * 100) / fileSize) + "%");
                                    }
                                });
                            }
                            manager.cancel(2);
                        }
                        WebSocketSender.C0107_uploadRulebasedResult(responseData.getString(ParameterName.ruleId), TaskState.RULEBASED_EXECUTING, resultApkListArray);
                        if (responseData.has(ParameterName.restartAfter)) {
                            if ("1".equals(responseData.getString(ParameterName.restartAfter))) {
                                restart_flag = true;
                            } else {
                                restart_flag = false;
                            }
                        } else {
                            restart_flag = false;
                        }
                        if ((ListeningService.isDeviceIdle(true)) || forceInstall) {
                            BBLog.w(BBLog.TAG, "終端處於空閑狀態，直接安裝 Rule APK");

                            //下载成功的改成等待安装的状态
                            updateResultAppListToWaitingInstallation();
                            WebSocketSender.C0107_uploadRulebasedResult(responseData.getString(ParameterName.ruleId), TaskState.RULEBASED_EXECUTING, resultApkListArray);

                            //通知完成
                            MRulebasedDialog.mRuleId = ruleId;
                            RulebasedListHandler.updateAppList(ruleId, appList);
                            MRulebasedDialog.installRulebasedApk();
                        } else {
                            //下载成功的改成等待安装的状态
                            updateResultAppListToWaitingInstallation();
                            WebSocketSender.C0107_uploadRulebasedResult(responseData.getString(ParameterName.ruleId), TaskState.RULEBASED_EXECUTING, resultApkListArray);

                            //通知完成
                            RulebasedListHandler.updateAppList(ruleId, appList);
                            Intent it = new Intent(BroadcastActions.RULEBASED_DOWNLOAD_COMPLETED);
                            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                                it.setComponent(new ComponentName(RulebasedService.this.getPackageName(), WebSocketReceiver.class.getName()));
                            }
                            if (responseData.has(ParameterName.delayTime)) {
                                it.putExtra(ParameterName.delayTime, responseData.getString(ParameterName.delayTime));
                            }
                            if (responseData.has(ParameterName.delayCount)) {
                                it.putExtra(ParameterName.delayCount, responseData.getString(ParameterName.delayCount));
                                try {
                                    try_count = Integer.valueOf(responseData.getString(ParameterName.delayCount));
                                    if (try_count < 0) {
                                        try_count = 0;
                                    }
                                } catch (Exception e) {
                                    e.printStackTrace();
                                    try_count = 0;
                                }
                            } else {
                                try_count = Integer.MAX_VALUE;
                            }
                            BBLog.w(BBLog.TAG, "延时安装时时间" + responseData.optString(ParameterName.delayTime) + "分钟  再次弹窗次数 = " + try_count);
                            it.putExtra(ParameterName.ruleId, responseData.getString(ParameterName.ruleId));
                            it.setFlags(Intent.FLAG_INCLUDE_STOPPED_PACKAGES);
                            sendBroadcast(it, RequestPermission.REQUEST_PERMISSION_MY_BROADCAST);
                        }
                    }
                }
            }
        } catch (Exception e1) {
            e1.printStackTrace();
        }
    }

    public void prepareToUpdate(Context context,JSONObject todoWSTaskJsonObj){
        if (todoWSTaskJsonObj == null) return;

        try {
            if (DeviceInfoApi.getIntance().isWisePosPro()) {
                if (todoWSTaskJsonObj.has(ParameterName.pkgName) && UsualData.SYSTEM_OTA_TRIGGER_APP_PACKAGENAME.equals(todoWSTaskJsonObj.getString(ParameterName.packName))) {//ota 升级
                    BBLog.d(BBLog.TAG, "prepareToUpdate [任務類型] ----> OTA update");
                    SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_NEED_WISEPOSPRO_OTA_FLAG, true);
                    if (Constants.IS_BATTERY_CHARGING == false && Constants.IS_BATTERY_LOW == true) {
                        showBatteryLowDialogWhileUpdate(context, WebSocketReceiver.UpdateTaskTypeEnum.OTAUPDATE, todoWSTaskJsonObj);
                    } else {
                        ActivityUtils.startOTAService(todoWSTaskJsonObj,Constants.TYPE_WISECUBE_FW_UPGRADE);
                    }
                } else if (todoWSTaskJsonObj.has(ParameterName.pkgName) && UsualData.SYSTEM_SP_OTA_TRIGGER_APP_PACKAGENAME.equals(todoWSTaskJsonObj.getString(ParameterName.packName))) {//sp 升级
                    BBLog.d(BBLog.TAG, "prepareToUpdate [任務類型] ----> sp update");
					SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_NEED_WISEPOSPRO_OTA_FLAG, true);
                    if (Constants.IS_BATTERY_CHARGING == false && Constants.IS_BATTERY_LOW == true) {
                        showBatteryLowDialogWhileUpdate(context, WebSocketReceiver.UpdateTaskTypeEnum.SPUPDATE, todoWSTaskJsonObj);
                    } else {
                        ActivityUtils.startOTAService(todoWSTaskJsonObj,Constants.TYPE_SP_FW_UPGRADE);
                    }
                }
            } else if (DeviceInfoApi.getIntance().isWisePos5Plus() || DeviceInfoApi.getIntance().isWisePos5() || DeviceInfoApi.getIntance().isWisePosTouch() || DeviceInfoApi.getIntance().isWisePosTouchPlus()) {//P1000、P500
                if (todoWSTaskJsonObj.has(ParameterName.pkgName) && UsualData.SYSTEM_TMT_OTA_TRIGGER_APP_PACKAGENAME.equals(todoWSTaskJsonObj.getString(ParameterName.packName))) {//tmt 升级
                    BBLog.d(BBLog.TAG, "prepareToUpdate [任務類型] ----> P1000/P500 update");
                    SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_NEED_WISEPOS5_OTA_FLAG, true);
                    if (Constants.IS_BATTERY_CHARGING == false && Constants.IS_BATTERY_LOW == true) {
                        showBatteryLowDialogWhileUpdate(context, WebSocketReceiver.UpdateTaskTypeEnum.TMT, todoWSTaskJsonObj);
                    } else {
                        ActivityUtils.startOTAService(todoWSTaskJsonObj,Constants.TYPE_TMT_UPGRADE);
                    }
                } else if (todoWSTaskJsonObj.has(ParameterName.pkgName) && UsualData.SYSTEM_KEY_UPDATE_TRIGGER_APP_PACKAGENAME.equals(todoWSTaskJsonObj.getString(ParameterName.packName))) {//key 升级
                    BBLog.d(BBLog.TAG, "prepareToUpdate [任務類型] ----> P1000/P500 update");
                    SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_NEED_WISEPOS5_OTA_FLAG, true);
                    if (Constants.IS_BATTERY_CHARGING == false && Constants.IS_BATTERY_LOW == true) {
                        showBatteryLowDialogWhileUpdate(context, WebSocketReceiver.UpdateTaskTypeEnum.KEYUPDATE, todoWSTaskJsonObj);
                    } else {
                        ActivityUtils.startOTAService(todoWSTaskJsonObj,Constants.TYPE_KEY_UPGRADE);
                    }
                }
            }else if (DeviceInfoApi.getIntance().isWisePos4G()) {//Wisepos4G
                if (todoWSTaskJsonObj.has(ParameterName.pkgName) && UsualData.SYSTEM_WISEPOS_OTA_TRIGGER_APP_PACKAGENAME.equals(todoWSTaskJsonObj.getString(ParameterName.packName))) {//wisepos4G ota 升级
                    BBLog.d(BBLog.TAG, "prepareToUpdate [任務類型] ----> WisePos4G update");
                    SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_NEED_WISEPOS4G_OTA_FLAG, true);
                    if (Constants.IS_BATTERY_CHARGING == false && Constants.IS_BATTERY_LOW == true) {
                        showBatteryLowDialogWhileUpdate(context, WebSocketReceiver.UpdateTaskTypeEnum.WISEPOSOTA, todoWSTaskJsonObj);
                    } else {
                        ActivityUtils.startOTAService(todoWSTaskJsonObj,Constants.TYPE_WISEPOS_TMT_UPGRADE);
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    private void showBatteryLowDialogWhileUpdate(Context context, WebSocketReceiver.UpdateTaskTypeEnum type, JSONObject todoWSTaskJsonObj) {
        mHandler.post(new Runnable() {
            @Override
            public void run() {
                if (lowBatdialog != null) {
                    lowBatdialog.dismiss();
                    lowBatdialog = null;
                }
                lowBatdialog = new Dialog(context, R.style.dialog_style_ex);
                lowBatdialog.setContentView(R.layout.dialog_confirm);
                ImageView imageView = (ImageView) lowBatdialog.findViewById(R.id.iv_image);
                imageView.setBackground(context.getDrawable(R.drawable.low_bat));
                TextView tv_title = (TextView) lowBatdialog.findViewById(R.id.tv_title);
                tv_title.setText(context.getString(R.string.low_battery));
                TextView tv_content = (TextView) lowBatdialog.findViewById(R.id.tv_content);
                tv_content.setText(context.getString(R.string.install_tip_dialog_message_low_power));
                lowBatdialog.setCanceledOnTouchOutside(false);
                TextView tv_cancel = (TextView) lowBatdialog.findViewById(R.id.tv_cancel);
                tv_cancel.setVisibility(View.GONE);
                TextView tv_install = (TextView) lowBatdialog.findViewById(R.id.tv_install);
                tv_install.setText(R.string.update);
                tv_install.setOnClickListener(
                        new View.OnClickListener() {
                            @Override
                            public void onClick(View v) {
                                lowBatdialog.dismiss();
                                if (Constants.IS_BATTERY_CHARGING==true || Constants.IS_BATTERY_LOW==false) {
                                    try {
                                        if (DeviceInfoApi.getIntance().isWisePosPro() ){
                                            if (type == WebSocketReceiver.UpdateTaskTypeEnum.OTAUPDATE) {
                                                ActivityUtils.startOTAService(todoWSTaskJsonObj,Constants.TYPE_WISECUBE_FW_UPGRADE);

                                            }else if (type == WebSocketReceiver.UpdateTaskTypeEnum.SPUPDATE){
                                                ActivityUtils.startOTAService(todoWSTaskJsonObj,Constants.TYPE_SP_FW_UPGRADE);
                                            }
                                        }else if (DeviceInfoApi.getIntance().isWisePos5Plus() || DeviceInfoApi.getIntance().isWisePos5() || DeviceInfoApi.getIntance().isWisePosTouch() || DeviceInfoApi.getIntance().isWisePosTouchPlus()){// P1000、P500
                                            if (type == WebSocketReceiver.UpdateTaskTypeEnum.TMT){
                                                ActivityUtils.startOTAService(todoWSTaskJsonObj,Constants.TYPE_TMT_UPGRADE);

                                            }else  if (type == WebSocketReceiver.UpdateTaskTypeEnum.KEYUPDATE){
                                                ActivityUtils.startOTAService(todoWSTaskJsonObj,Constants.TYPE_KEY_UPGRADE);
                                            }
                                        }else if (DeviceInfoApi.getIntance().isWisePos4G()){// WisePos 4G
                                            if (type == WebSocketReceiver.UpdateTaskTypeEnum.WISEPOSOTA){
                                                ActivityUtils.startOTAService(todoWSTaskJsonObj,Constants.TYPE_WISEPOS_TMT_UPGRADE);
                                            }
                                        }
                                    } catch (Exception e) {
                                        e.printStackTrace();
                                    }
                                }
                            }
                        });
                lowBatdialog.getWindow().setType((WindowManager.LayoutParams.TYPE_SYSTEM_ALERT));
                lowBatdialog.show();
            }
        });
    }

    private void createResultAppList(JSONArray jsonArrayInsatall, JSONArray jsonArrayDelete) {
        BBLog.d(BBLog.TAG, "createResultAppList: ");
        try {
            resultApkListArray = new JSONArray("[]");
            if (jsonArrayInsatall!=null) {
                for (int index = 0; index < jsonArrayInsatall.length(); index++) {
                    JSONObject taskJson = jsonArrayInsatall.getJSONObject(index);

                    BBLog.d(BBLog.TAG, "createResultAppList: " + taskJson.getString(ParameterName.packName));
                    JSONObject appTask = new JSONObject();
                    appTask.put(ParameterName.packName, taskJson.getString(ParameterName.packName));
                    appTask.put(ParameterName.apkName, taskJson.getString(ParameterName.apkName));
                    appTask.put(ParameterName.versionCode, taskJson.getString(ParameterName.versionCode));
                    appTask.put(ParameterName.versionName, taskJson.getString(ParameterName.versionName));
                    if (taskJson.has(ParameterName.appIconUrlEx)) {
                        appTask.put(ParameterName.appIconUrlEx, taskJson.getString(ParameterName.appIconUrlEx));
                    }
                    appTask.put(ParameterName.result, TaskState.EXECUTE_WATING);
                    resultApkListArray.put(appTask);
                }
            }
            if (jsonArrayDelete!=null) {
                for (int index = 0; index < jsonArrayDelete.length(); index++) {
                    JSONObject taskJson = jsonArrayDelete.getJSONObject(index);

                    BBLog.d(BBLog.TAG, "createResultAppList: " + taskJson.getString(ParameterName.packName));
                    JSONObject appTask = new JSONObject();
                    appTask.put(ParameterName.packName, taskJson.getString(ParameterName.packName));
                    appTask.put(ParameterName.apkName, taskJson.getString(ParameterName.apkName));
                    appTask.put(ParameterName.versionCode, taskJson.getString(ParameterName.versionCode));
                    appTask.put(ParameterName.versionName, taskJson.getString(ParameterName.versionName));
                    if (taskJson.has(ParameterName.appIconUrlEx)) {
                        appTask.put(ParameterName.appIconUrlEx, taskJson.getString(ParameterName.appIconUrlEx));
                    }
                    appTask.put(ParameterName.result, TaskState.EXECUTE_WATING);
                    resultApkListArray.put(appTask);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void updateResultAppListToWaitingInstallation() {
        try {
            BBLog.w(BBLog.TAG, "updateResultAppListToWaitingInstallation");
            for (int index=0; index<resultApkListArray.length(); index++) {
                JSONObject appTask = resultApkListArray.getJSONObject(index);

                if (appTask!=null && TaskState.DOWNLOAD_SUCCESS.equals(appTask.getString(ParameterName.result))) {
                    appTask.put(ParameterName.result, TaskState.INSTALL_WAITING);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void updateResultAppStatus(String packageName, String result, String errMsg) {
        try {
            BBLog.w(BBLog.TAG, "updateResultAppStatus： " + packageName + "   resultApkListArray.length() = " + resultApkListArray.length());
            for (int index=0; index<resultApkListArray.length(); index++) {
                JSONObject appTask = resultApkListArray.getJSONObject(index);

                if (appTask!=null && packageName.equals(appTask.getString(ParameterName.packName))) {
                    appTask.put(ParameterName.result, result);
                    if (!TextUtils.isEmpty(errMsg)) {
                        appTask.put(ParameterName.errorMsg, errMsg);
                    }
                    break;
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
	
	public static void deleteApkOutOfRulebased() {
        //检查卸载
        BBLog.e(BBLog.TAG, "开始删除Rule以外的App");
        PackageManager packageManager = ContextUtil.getInstance().getPackageManager();
        List<PackageInfo> packageInfos = packageManager.getInstalledPackages(0);
        for(int i =0;i < packageInfos.size();i++) {
            final PackageInfo packageInfo = packageInfos.get(i);
            //过滤掉系统app
            if ((ApplicationInfo.FLAG_SYSTEM & packageInfo.applicationInfo.flags) != 0
                    || UsualData.SERVICE_PACKAGE_NAME.equals(packageInfo.packageName)
                    || UsualData.LAUNCHER_PACKAGE_NAME.equals(packageInfo.packageName)
                    || UsualData.LAUNCHER_711_PACKAGE_NAME.equals(packageInfo.packageName)
                    || UsualData.LOADER_711_PACKAGE_NAME.equals(packageInfo.packageName)
                    || SharedPreferencesUtils.getSharePreferencesValue(UsualData.SHARED_PREFERENCES_STARTUP_APP_PACKAGE_NAME, "").equals(packageInfo.packageName)) {
                continue;
            }

            BBLog.e(BBLog.TAG, "开始检查：" + packageInfo.packageName);
            //如果GMS套件APK
            if (ProvisionDownloadService.isGMSApp(packageInfo.packageName)) {
                continue;
            }

            //如果是APP+推送的APP，優先級高於Rulebased，不刪除
            if (WebSocketTaskListManager.isAppFromAppPlus(packageInfo.packageName, packageInfo.versionName, String.valueOf(packageInfo.versionCode))) {
                continue;
            }
            if ("false".equals(RulebasedListHandler.isRulebasedListApp(packageInfo.packageName, packageInfo.versionName, String.valueOf(packageInfo.versionCode)))) {
                BBLog.e(BBLog.TAG, "准备删除" + packageInfo.packageName);
                SystemManagerAdapter.unInstallApk(ContextUtil.getInstance(), packageInfo.packageName, new SystemManagerAdapter.ApkUnInstallCompleted() {
                    @Override
                    public void onDeleteFinished(int returnCode) {
                        BBLog.i(Constants.TAG, "packageDeleted " + packageInfo.packageName + " returnCode " + returnCode);
                        if (returnCode == 1) {
                        } else {
                        }
                    }
                });
            }
        }
    }

    public static void deleteApkInRulebased(String ruleId) {
        //检查卸载
        BBLog.e(BBLog.TAG, "开始删除Rule要求删除的App");
        for(AppInfo appInfo : MRulebasedDialog.mRulebasedDeletelist) {
            //过滤掉系统app
            if (UsualData.SERVICE_PACKAGE_NAME.equals(appInfo.getPackage_name())
                    || UsualData.LAUNCHER_PACKAGE_NAME.equals(appInfo.getPackage_name())
                    || SharedPreferencesUtils.getSharePreferencesValue(UsualData.SHARED_PREFERENCES_STARTUP_APP_PACKAGE_NAME, "").equals(appInfo.getPackage_name())) {
                continue;
            }

            BBLog.e(BBLog.TAG, "开始检查：" + appInfo.getPackage_name());
            //如果GMS套件APK
            if (ProvisionDownloadService.isGMSApp(appInfo.getPackage_name())) {
                continue;
            }

//            //如果是APP+推送的APP，優先級高於Rulebased，不刪除
//            if (WebSocketTaskListManager.isAppFromAppPlus(appInfo.getPackage_name(), appInfo.getVersion_name(), appInfo.getVersion_code())) {
//                continue;
//            }
            BBLog.e(BBLog.TAG, "准备删除" + appInfo.getPackage_name());
            try {
                BBLog.e(BBLog.TAG, "判断" + "packName=" + appInfo.getPackage_name() + " versionCode=" + appInfo.getVersion_code() + " versionName=" + appInfo.getVersion_name() + "是否安装");
                PackageInfo info = ActivityUtils.getPackageInfo(ContextUtil.getInstance().getApplicationContext(), appInfo.getPackage_name());
                if (info == null) {
                    BBLog.e(BBLog.TAG, appInfo.getPackage_name() + "未安装");
                    updateResultAppStatus(appInfo.getPackage_name(), TaskState.UNINSTALL_SUCCESS, null);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }

            SystemManagerAdapter.unInstallApk(ContextUtil.getInstance(), appInfo.getPackage_name(), new SystemManagerAdapter.ApkUnInstallCompleted() {
                @Override
                public void onDeleteFinished(int returnCode) {
                    BBLog.i(Constants.TAG, "packageDeleted " + appInfo.getPackage_name() + " returnCode " + returnCode);
                    if (returnCode == 1) {
                        updateResultAppStatus(appInfo.getPackage_name(), TaskState.UNINSTALL_SUCCESS, null);
                        WebSocketSender.C0107_uploadRulebasedResult(ruleId, TaskState.RULEBASED_SUCCESS, RulebasedService.resultApkListArray);
                    } else {
                    }
                }
            });
        }
    }

    public static void onRuleExecuteCompleted(String ruleId) {
        //删除非Rulebased的APP
        RulebasedService.deleteApkInRulebased(ruleId);
//		RulebasedService.deleteApkOutOfRulebased();

        BBLog.e(BBLog.TAG, "RulebasedService.anyFailed = " + RulebasedService.anyFailed);
        if (RulebasedService.anyFailed) {
            RulebasedListHandler.updateRuleState(ruleId, RuleStatus.READY);
            WebSocketSender.C0107_uploadRulebasedResult(ruleId, TaskState.RULEBASED_EXECUTING, RulebasedService.resultApkListArray);
        } else {
            BBLog.e(BBLog.TAG, "RulebasedService.mRuleId = " + ruleId);
            RulebasedListHandler.updateRuleState(ruleId, RuleStatus.IMPLEMENTED);
            WebSocketSender.C0107_uploadRulebasedResult(ruleId, TaskState.RULEBASED_SUCCESS, RulebasedService.resultApkListArray);
        }
        if (RulebasedService.restart_flag) {
            SystemManagerAdapter.reboot(ContextUtil.getInstance());
        } else {
            RulebasedService.ruleExecuteTimeMap.remove(RulebasedService.mCurTaskExeTime);
        }

        //查找删除被编辑的rule
        try {
            JSONObject localRulebasedJsonObj = RulebasedListHandler.getWSRuleJsonObjById(ruleId);
            if (localRulebasedJsonObj!=null && localRulebasedJsonObj.has(ParameterName.orgRuleId)) {
                String orgRuleId = localRulebasedJsonObj.getString(ParameterName.orgRuleId);
                if (!TextUtils.isEmpty(orgRuleId)) {
                    JSONObject rulebasedOrg = RulebasedListHandler.getWSRuleJsonObjById(orgRuleId);
                    if (rulebasedOrg != null) {
                        if (RuleStatus.IMPLEMENTED.equals(rulebasedOrg.getString(ParameterName.ruleStatus))
                                || RuleStatus.COMPLETED.equals(rulebasedOrg.getString(ParameterName.ruleStatus))) {
                            //删除那些已完成的被编辑的原始rule记录
                            RulebasedListHandler.removeRuleJsonObjById(orgRuleId);
                            RulebasedListHandler.removeAppList(orgRuleId);
                        }
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static boolean isRuleInExecuting(JSONObject localRulebasedJsonObj) {
        try {
            if (localRulebasedJsonObj != null) {
                String beginDateStr = localRulebasedJsonObj.getString(ParameterName.beginDate);
                String endDateStr = localRulebasedJsonObj.getString(ParameterName.endDate);
                String modifyDateStr = "";
                if (localRulebasedJsonObj.has(ParameterName.modifyDate)) {
                    modifyDateStr = localRulebasedJsonObj.getString(ParameterName.modifyDate);
                }
                long modifyTime = 0;
                long beginTime;
                long endTime;
                if (DateTimeUtils.isNumeric(beginDateStr) && DateTimeUtils.isNumeric(endDateStr) && DateTimeUtils.isNumeric(modifyDateStr)) {
                    if (!TextUtils.isEmpty(modifyDateStr)) {
                        modifyTime = new Long(modifyDateStr).longValue();
                    }
                    beginTime = new Long(beginDateStr).longValue();
                    endTime = new Long(endDateStr).longValue();
                } else {
                    SimpleDateFormat sdf = new SimpleDateFormat(Constants.dateFormat);
                    if (!TextUtils.isEmpty(modifyDateStr)) {
                        modifyTime = sdf.parse(modifyDateStr).getTime();
                    }
                    beginTime = sdf.parse(beginDateStr).getTime();
                    endTime = sdf.parse(endDateStr).getTime();
                }

                if (!TextUtils.isEmpty(modifyDateStr)) {
                    if (ruleExecuteTimeMap.containsKey(modifyTime) || ruleExpireTimeMap.containsKey(modifyTime)) {
                        return true;
                    }
                } else {
                    if (ruleExecuteTimeMap.containsKey(beginTime) || ruleExpireTimeMap.containsKey(beginTime)) {
                        return true;
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }
}
