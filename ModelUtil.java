package com.dspread.components.common.device;

import android.os.Build;
import android.os.SystemProperties;
import android.text.TextUtils;

public class ModelUtil {

    public static String getModel() {
        return Build.MODEL;
    }

    public static String getProjectName() {
        return SystemProperties.get("ro.pos.project.name", "");
    }

    public static String getDspreadName() {
        return SystemProperties.get("ro.dspread.model", "");
    }

    public static boolean isD20() {
        if ("D20".equals(getModel()) || "FP9810".equals(getModel())) {
            return true;
        }
        if (!TextUtils.isEmpty(getDspreadName()) && "D20".equals(getDspreadName())) {
            return true;
        }
        return false;
    }

    public static boolean isD60() {
        if (TextUtils.isEmpty(getProjectName())) {
            if ("D60".equals(getModel()) || "FP9900".equals(getModel())) {
                return true;
            }
        }
        if (!TextUtils.isEmpty(getDspreadName()) && "D60".equals(getDspreadName())) {
            return true;
        }
        return false;
    }

    public static boolean isD70() {
        if (TextUtils.isEmpty(getProjectName())) {
            if ("D70".equals(getModel())) {
                return true;
            }
        }
        if (!TextUtils.isEmpty(getDspreadName()) && "D70".equals(getDspreadName())) {
            return true;
        }
        return false;
    }

    public static boolean isD30() {
        if (TextUtils.isEmpty(getProjectName())) {
            if ("D30".equals(getModel())) {
                return true;
            }
        }
        if (!TextUtils.isEmpty(getDspreadName()) && "D30".equals(getDspreadName())) {
            return true;
        }
        return false;
    }

    public static boolean isD30M() {
        if ("D30M".equals(getModel())){
            return true;
        }
        if ("D30M-MU".equals(getProjectName())){
            return true;
        }
        return false;
    }

    public static boolean isD35() {
        if ("D35".equals(getModel())){
            return true;
        }
        if ("D35-MU".equals(getProjectName())){
            return true;
        }
        return false;
    }

    public static boolean isD80() {
        if ("D80".equals(getModel())) {
            return true;
        }
        if ("D80-MU".equals(getProjectName())){
            return true;
        }
        return false;
    }

    public static boolean isD80K() {
        if ("D80K".equals(getModel())){
            return true;
        }
        if ("D80K-MU".equals(getProjectName())){
            return true;
        }
        return false;
    }

    public static boolean isD50() {
        if ("D50".equals(getModel())){
            return true;
        }
        if ("D50-MU".equals(getProjectName())){
            return true;
        }
        return false;
    }
}