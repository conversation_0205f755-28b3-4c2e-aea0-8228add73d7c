package com.dspread.mdm.service.constants

/**
 * 任务状态常量
 * 统一管理所有任务相关的状态码
 * 避免在多个地方重复定义状态码
 */
object TaskStateConstants {
    
    // ==================== 任务类型常量 ====================
    
    /** 安装/更新任务 (UPDATE_APK) */
    const val TASK_INSTALL = "01"
    
    /** 卸载任务 (UNINSTALL_APK) */
    const val TASK_UNINSTALL = "02"

    /** SP下载任务 (SP_DOWNLOAD) */
    const val TASK_SP_DOWNLOAD = "03"
    
    /** OS更新任务 (OSUPDATE) */
    const val TASK_OS_UPDATE = "04"

    /** 参数更新任务 (UPDATE_PARAM) */
    const val TASK_UPDATE_PARAM = "05"

    // ==================== 基础任务状态 ====================
    
    /** 待执行 */
    const val TODO = "todo"
    
    /** 等待中（加入到IntentService队列中，等待执行） */
    const val WAITING = "wating"
    
    /** 执行等待 */
    const val EXECUTE_WAITING = "W01"
    
    // ==================== 下载相关状态 ====================
    
    /** 下载中 */
    const val DOWNLOAD_ING = "A01"
    
    /** 下载成功 */
    const val DOWNLOAD_SUCCESS = "A03"
    
    /** 下载失败 */
    const val DOWNLOAD_FAILED = "A04"
    
    // ==================== 安装相关状态 ====================
    
    /** 安装等待 */
    const val INSTALL_WAITING = "B01"
    
    /** 安装中 */
    const val INSTALL_ING = "B02"
    
    /** 安装成功 */
    const val INSTALL_SUCCESS = "B03"
    
    /** 安装失败 */
    const val INSTALL_FAILED = "B04"
    
    /** 安装覆盖 */
    const val INSTALL_OVERRIDE = "B05"
    
    // ==================== 更新相关状态 ====================
    
    /** 更新中 */
    const val UPDATE_ING = "C02"
    
    /** 更新成功 */
    const val UPDATE_SUCCESS = "C03"
    
    /** 更新失败 */
    const val UPDATE_FAILED = "C04"

    /** 更新降级禁止 */
    const val UPDATE_DOWNGRADE_FORBIDDEN = "C05"
    
    // ==================== 卸载相关状态 ====================
    
    /** 卸载中 */
    const val UNINSTALL_ING = "D01"
    
    /** 卸载成功 */
    const val UNINSTALL_SUCCESS = "D02"
    
    /** 卸载失败 */
    const val UNINSTALL_FAILED = "D03"
    
    /** 卸载过期 */
    const val UNINSTALL_EXPIRE = "D04"
    
    // ==================== 通用结果状态 ====================
    
    /** 任务取消 */
    const val TASK_CANCEL = "E01"
    
    /** 依赖失败 */
    const val RELY_FAILED = "E02"
    
    /** 电量不足 */
    const val LOW_BAT = "E03"
    
    /** 服务器失败 */
    const val SERVER_FAILED = "E04"
    
    /** 失败 */
    const val FAILED = "E05"
    
    /** 成功 */
    const val SUCCESSED = "F01"
    
    // ==================== 规则相关状态 ====================
    
    /** 规则开始执行 */
    const val RULEBASED_STARTING = "R01"
    
    /** 规则执行中 */
    const val RULEBASED_EXECUTING = "R02"
    
    /** 规则执行成功 */
    const val RULEBASED_SUCCESS = "R03"
    
    /** 规则已过期 */
    const val RULEBASED_EXPIRED = "R04"
    
    // ==================== 特殊状态 ====================
    
    /** 延迟（已加入定时器的任务，时间更改） */
    const val DELAY = "delay"
    
    /** 删除（已加入定时器的任务，被取消） */
    const val DELETE = "delete"
    
    /** 无网络（任务执行时间已到，由于无网络挂起） */
    const val NO_NET = "no_net"
    
    // ==================== 新格式兼容状态（用于某些新接口） ====================
    
    /** 成功（新格式） */
    const val RESULT_SUCCESS = "0"
    
    /** 失败（新格式） */
    const val RESULT_FAILED = "1"
    
    /** 部分成功（新格式） */
    const val RESULT_PARTIAL = "2"
    
    // ==================== 辅助方法 ====================
    
    /**
     * 判断是否为完成状态（成功或失败）
     */
    fun isCompletedState(state: String): Boolean {
        return when (state) {
            INSTALL_SUCCESS, INSTALL_FAILED,
            UNINSTALL_SUCCESS, UNINSTALL_FAILED,
            UPDATE_SUCCESS, UPDATE_FAILED, UPDATE_DOWNGRADE_FORBIDDEN,
            DOWNLOAD_SUCCESS, DOWNLOAD_FAILED,
            RULEBASED_SUCCESS, RULEBASED_EXPIRED,
            SUCCESSED, FAILED,
            RESULT_SUCCESS, RESULT_FAILED -> true
            else -> false
        }
    }
    
    /**
     * 判断是否为成功状态
     */
    fun isSuccessState(state: String): Boolean {
        return when (state) {
            INSTALL_SUCCESS, UNINSTALL_SUCCESS, UPDATE_SUCCESS,
            DOWNLOAD_SUCCESS, RULEBASED_SUCCESS,
            SUCCESSED, RESULT_SUCCESS -> true
            else -> false
        }
    }
    
    /**
     * 判断是否为失败状态
     */
    fun isFailureState(state: String): Boolean {
        return when (state) {
            INSTALL_FAILED, UNINSTALL_FAILED, UPDATE_FAILED, UPDATE_DOWNGRADE_FORBIDDEN,
            DOWNLOAD_FAILED, RULEBASED_EXPIRED,
            FAILED, RESULT_FAILED,
            TASK_CANCEL, RELY_FAILED, LOW_BAT, SERVER_FAILED -> true
            else -> false
        }
    }
    
    /**
     * 判断是否为进行中状态
     */
    fun isInProgressState(state: String): Boolean {
        return when (state) {
            DOWNLOAD_ING, INSTALL_ING, UPDATE_ING, UNINSTALL_ING,
            RULEBASED_STARTING, RULEBASED_EXECUTING,
            EXECUTE_WAITING, INSTALL_WAITING -> true
            else -> false
        }
    }
}
