2025-08-14 18:33:54.835 19136-19136 Platform                com.dspread.mdm.service              D  🔧 WakeLockManager WakeLock不存在或未持有: ServiceKeepAlive
2025-08-14 18:33:54.849 19136-19136 Service                 com.dspread.mdm.service              D  🔧 ServiceKeepAliveService 保活任务完成，释放WakeLock
2025-08-14 18:33:58.248 19136-19136 Receiver                com.dspread.mdm.service              D  🔧 电池状态更新: 电池状态: 电量=100%, 温度=29.6°C, 健康=2, 充电=是, 低电量=否, 耗尽=否
2025-08-14 18:34:18.087 19136-19207 Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"msg_state":"P","request_time":"1755167656512","data":{"ruleList":[{"deleteAppList":[{"apkName":"cl960","appId":"f32ac615171249418e3fc4b5bc2f4773","packName":"com.chileaf.cl960.sample","versionName":"1.1.0","versionCode":"1"},{"apkName":"逍遥助手","appId":"225a0a30994e45089dad1a270f8d85a1","packName":"plus.H52FFB9A5","versionName":"1.0","versionCode":"100"}],"modifyDate":"2025-08-14 10:34:16","endDate":"9999-12-31 23:59:59","restartAfter":"0","installBy":"0","beginDate":"2024-08-14 10:34:16","appList":[],"serviceList":[],"action":"A","ruleName":"d60_test_297","ruleId":"e195e52efa414316818a8d4b21fa4e9c","createDate":"2025-08-14 10:34:16","orgRuleId":""}]},"tranCode":"ST005","request_id":"1755167656512ST005","version":"1","serialNo":"01354090202503050399"}
2025-08-14 18:34:18.097 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 准备发送响应确认 - requestId: 1755167656512ST005, needResponse: true
2025-08-14 18:34:18.130 19136-19207 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0000","serialNo":"01354090202503050399","request_time":"1755167658109","request_id":"1755167658109C0000","version":"1","org_request_id":"1755167656512ST005","org_request_time":"1755167656512","response_state":"0","myVersionName":"1.0.08.20250814.DSPREAD.MDM.SERVICE","terminalDate":"20250814183418"}
2025-08-14 18:34:18.157 19136-19207 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0000","serialNo":"01354090202503050399","request_time":"1755167658140","request_id":"1755167658140C0000","version":"1","org_request_id":"1755167656512ST005","org_request_time":"1755167656512","response_state":"0","myVersionName":"1.0.08.20250814.DSPREAD.MDM.SERVICE","terminalDate":"20250814183418"}
2025-08-14 18:34:18.163 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 已发送C0000响应确认: requestId=1755167656512ST005
2025-08-14 18:34:18.172 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleHandler 处理规则消息: ST005
2025-08-14 18:34:18.178 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleHandler 处理规则列表，数量: 1
2025-08-14 18:34:18.184 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleHandler 处理规则: ruleId=e195e52efa414316818a8d4b21fa4e9c, action=A
2025-08-14 18:34:18.190 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析规则JSON: ruleId=e195e52efa414316818a8d4b21fa4e9c
2025-08-14 18:34:18.195 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule appList数组: []
2025-08-14 18:34:18.205 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule deleteAppList数组: [{"apkName":"cl960","appId":"f32ac615171249418e3fc4b5bc2f4773","packName":"com.chileaf.cl960.sample","versionName":"1.1.0","versionCode":"1"},{"apkName":"逍遥助手","appId":"225a0a30994e45089dad1a270f8d85a1","packName":"plus.H52FFB9A5","versionName":"1.0","versionCode":"100"}]
2025-08-14 18:34:18.211 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=com.chileaf.cl960.sample, apkName=cl960
2025-08-14 18:34:18.216 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=plus.H52FFB9A5, apkName=逍遥助手
2025-08-14 18:34:18.222 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后appList数量: 0
2025-08-14 18:34:18.228 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后deleteAppList数量: 2
2025-08-14 18:34:18.234 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleBaseStorage 处理规则: e195e52efa414316818a8d4b21fa4e9c, 操作: A
2025-08-14 18:34:18.240 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 验证规则: ruleId=e195e52efa414316818a8d4b21fa4e9c, ruleType=app_management, action=A
2025-08-14 18:34:18.245 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule appList数量: 0, deleteAppList数量: 2
2025-08-14 18:34:18.267 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 验证应用管理规则: action=A
2025-08-14 18:34:18.273 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 检测到纯卸载规则，自动调整action: A -> D
2025-08-14 18:34:18.279 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 删除操作验证通过: 允许空应用列表
2025-08-14 18:34:18.291 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析规则JSON: ruleId=937eccc7e4ff4fc0bc9eaf2788685515
2025-08-14 18:34:18.301 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule appList数组: [{"packName":"mark.via","apkName":"Via","versionName":"6.6.0","versionCode":"20250713","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/4d256c4123b346fd817537b491342204.apk","apkMd5":"0d1225260d03e10a8ffc8409369c442a","apkSize":2565110,"installBy":"0","isSystemApp":false,"priority":0}]
2025-08-14 18:34:18.306 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule deleteAppList数组: []
2025-08-14 18:34:18.312 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=mark.via, apkName=Via
2025-08-14 18:34:18.318 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后appList数量: 1
2025-08-14 18:34:18.324 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后deleteAppList数量: 0
2025-08-14 18:34:18.330 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析规则JSON: ruleId=01f42138486940dd8ba8a136d84e239d
2025-08-14 18:34:18.336 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule appList数组: []
2025-08-14 18:34:18.344 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule deleteAppList数组: [{"packName":"mark.via","apkName":"Via","versionName":"6.6.0","versionCode":"20250713","apkUrl":"","apkMd5":"","apkSize":0,"installBy":"0","isSystemApp":false,"priority":0}]
2025-08-14 18:34:18.350 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=mark.via, apkName=Via
2025-08-14 18:34:18.355 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后appList数量: 0
2025-08-14 18:34:18.361 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后deleteAppList数量: 1
2025-08-14 18:34:18.374 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析规则JSON: ruleId=937eccc7e4ff4fc0bc9eaf2788685515
2025-08-14 18:34:18.383 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule appList数组: [{"packName":"mark.via","apkName":"Via","versionName":"6.6.0","versionCode":"20250713","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/4d256c4123b346fd817537b491342204.apk","apkMd5":"0d1225260d03e10a8ffc8409369c442a","apkSize":2565110,"installBy":"0","isSystemApp":false,"priority":0}]
2025-08-14 18:34:18.389 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule deleteAppList数组: []
2025-08-14 18:34:18.395 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=mark.via, apkName=Via
2025-08-14 18:34:18.401 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后appList数量: 1
2025-08-14 18:34:18.407 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后deleteAppList数量: 0
2025-08-14 18:34:18.413 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析规则JSON: ruleId=01f42138486940dd8ba8a136d84e239d
2025-08-14 18:34:18.419 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule appList数组: []
2025-08-14 18:34:18.427 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule deleteAppList数组: [{"packName":"mark.via","apkName":"Via","versionName":"6.6.0","versionCode":"20250713","apkUrl":"","apkMd5":"","apkSize":0,"installBy":"0","isSystemApp":false,"priority":0}]
2025-08-14 18:34:18.433 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=mark.via, apkName=Via
2025-08-14 18:34:18.439 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后appList数量: 0
2025-08-14 18:34:18.444 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后deleteAppList数量: 1
2025-08-14 18:34:18.477 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleBaseStorage 保存 3 个规则到存储
2025-08-14 18:34:18.483 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleBaseStorage Rule e195e52efa414316818a8d4b21fa4e9c 添加成功
2025-08-14 18:34:18.488 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleBaseStorage 触发规则执行: e195e52efa414316818a8d4b21fa4e9c
2025-08-14 18:34:18.495 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleHandler 规则处理成功: e195e52efa414316818a8d4b21fa4e9c
2025-08-14 18:34:18.496 19136-19166 RuleBase                com.dspread.mdm.service              D  🔧 RuleBaseStorage 设备空闲，立即执行规则: e195e52efa414316818a8d4b21fa4e9c
2025-08-14 18:34:18.502 19136-19166 RuleBase                com.dspread.mdm.service              D  🔧 RuleBaseManager 直接执行规则: e195e52efa414316818a8d4b21fa4e9c
2025-08-14 18:34:18.502 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleHandler 规则列表处理完成: 处理完成 - 成功: 1, 失败: 0, 忽略: 0
2025-08-14 18:34:18.508 19136-19166 RuleBase                com.dspread.mdm.service              D  🔧 RuleBaseManager 处理规则: e195e52efa414316818a8d4b21fa4e9c, 操作: A
2025-08-14 18:34:18.508 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleHandler 上报规则处理结果: 0 - 处理完成 - 成功: 1, 失败: 0, 忽略: 0
2025-08-14 18:34:18.515 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 C0107 主动式上送: rulebase_update (主动: 13)
2025-08-14 18:34:18.515 19136-19166 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析规则JSON: ruleId=e195e52efa414316818a8d4b21fa4e9c
2025-08-14 18:34:18.521 19136-19166 RuleBase                com.dspread.mdm.service              D  🔧 Rule appList数组: []
2025-08-14 18:34:18.532 19136-19166 RuleBase                com.dspread.mdm.service              D  🔧 Rule deleteAppList数组: [{"packName":"com.chileaf.cl960.sample","apkName":"cl960","versionName":"1.1.0","versionCode":"1","apkUrl":"","apkMd5":"","apkSize":0,"installBy":"0","isSystemApp":false,"priority":0},{"packName":"plus.H52FFB9A5","apkName":"逍遥助手","versionName":"1.0","versionCode":"100","apkUrl":"","apkMd5":"","apkSize":0,"installBy":"0","isSystemApp":false,"priority":0}]
2025-08-14 18:34:18.535 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 添加规则结果: ruleId=batch_rules
2025-08-14 18:34:18.538 19136-19166 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=com.chileaf.cl960.sample, apkName=cl960
2025-08-14 18:34:18.544 19136-19166 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=plus.H52FFB9A5, apkName=逍遥助手
2025-08-14 18:34:18.551 19136-19166 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后appList数量: 0
2025-08-14 18:34:18.551 19136-19207 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0107","serialNo":"01354090202503050399","request_time":"1755167658530","request_id":"1755167658530C0107","version":"1","data":{"ruleId":"batch_rules","taskResult":"0"},"myVersionName":"1.0.08.20250814.DSPREAD.MDM.SERVICE","terminalDate":"20250814183418","org_request_id":"1755167656512ST005","org_request_time":"1755167656512"}
2025-08-14 18:34:18.557 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 规则结果实时上送: ruleId=batch_rules, result=0 (1)
2025-08-14 18:34:18.557 19136-19166 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后deleteAppList数量: 2
2025-08-14 18:34:18.564 19136-19166 RuleBase                com.dspread.mdm.service              D  🔧 RuleBaseStorage 处理规则: e195e52efa414316818a8d4b21fa4e9c, 操作: A
2025-08-14 18:34:18.570 19136-19166 RuleBase                com.dspread.mdm.service              D  🔧 Rule 验证规则: ruleId=e195e52efa414316818a8d4b21fa4e9c, ruleType=app_management, action=A
2025-08-14 18:34:18.575 19136-19166 RuleBase                com.dspread.mdm.service              D  🔧 Rule appList数量: 0, deleteAppList数量: 2
2025-08-14 18:34:18.598 19136-19166 RuleBase                com.dspread.mdm.service              D  🔧 Rule 验证应用管理规则: action=A
2025-08-14 18:34:18.604 19136-19166 RuleBase                com.dspread.mdm.service              D  🔧 Rule 检测到纯卸载规则，自动调整action: A -> D
2025-08-14 18:34:18.609 19136-19166 RuleBase                com.dspread.mdm.service              D  🔧 Rule 删除操作验证通过: 允许空应用列表
2025-08-14 18:34:18.626 19136-19166 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析规则JSON: ruleId=937eccc7e4ff4fc0bc9eaf2788685515
2025-08-14 18:34:18.636 19136-19166 RuleBase                com.dspread.mdm.service              D  🔧 Rule appList数组: [{"packName":"mark.via","apkName":"Via","versionName":"6.6.0","versionCode":"20250713","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/4d256c4123b346fd817537b491342204.apk","apkMd5":"0d1225260d03e10a8ffc8409369c442a","apkSize":2565110,"installBy":"0","isSystemApp":false,"priority":0}]
2025-08-14 18:34:18.642 19136-19166 RuleBase                com.dspread.mdm.service              D  🔧 Rule deleteAppList数组: []
2025-08-14 18:34:18.647 19136-19166 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=mark.via, apkName=Via
2025-08-14 18:34:18.653 19136-19166 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后appList数量: 1
2025-08-14 18:34:18.659 19136-19166 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后deleteAppList数量: 0
2025-08-14 18:34:18.666 19136-19166 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析规则JSON: ruleId=01f42138486940dd8ba8a136d84e239d
2025-08-14 18:34:18.672 19136-19166 RuleBase                com.dspread.mdm.service              D  🔧 Rule appList数组: []
2025-08-14 18:34:18.680 19136-19166 RuleBase                com.dspread.mdm.service              D  🔧 Rule deleteAppList数组: [{"packName":"mark.via","apkName":"Via","versionName":"6.6.0","versionCode":"20250713","apkUrl":"","apkMd5":"","apkSize":0,"installBy":"0","isSystemApp":false,"priority":0}]
2025-08-14 18:34:18.686 19136-19166 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=mark.via, apkName=Via
2025-08-14 18:34:18.690 19136-19207 Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"request_time":"1755167657690","org_request_time":"1755167658530","org_request_id":"1755167658530C0107","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755167657690S0000","serialNo":"01354090202503050399"}
2025-08-14 18:34:18.692 19136-19166 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后appList数量: 0
2025-08-14 18:34:18.698 19136-19166 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后deleteAppList数量: 1
2025-08-14 18:34:18.699 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 收到响应消息: transCode=S0000, orgRequestId=1755167658530C0107, state=0, remark=
2025-08-14 18:34:18.704 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 处理规则结果响应: state=0
2025-08-14 18:34:18.705 19136-19166 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析规则JSON: ruleId=e195e52efa414316818a8d4b21fa4e9c
2025-08-14 18:34:18.711 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 规则结果上传成功
2025-08-14 18:34:18.711 19136-19166 RuleBase                com.dspread.mdm.service              D  🔧 Rule appList数组: []
2025-08-14 18:34:18.723 19136-19166 RuleBase                com.dspread.mdm.service              D  🔧 Rule deleteAppList数组: [{"packName":"com.chileaf.cl960.sample","apkName":"cl960","versionName":"1.1.0","versionCode":"1","apkUrl":"","apkMd5":"","apkSize":0,"installBy":"0","isSystemApp":false,"priority":0},{"packName":"plus.H52FFB9A5","apkName":"逍遥助手","versionName":"1.0","versionCode":"100","apkUrl":"","apkMd5":"","apkSize":0,"installBy":"0","isSystemApp":false,"priority":0}]
2025-08-14 18:34:18.729 19136-19166 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=com.chileaf.cl960.sample, apkName=cl960
2025-08-14 18:34:18.735 19136-19166 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=plus.H52FFB9A5, apkName=逍遥助手
2025-08-14 18:34:18.741 19136-19166 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后appList数量: 0
2025-08-14 18:34:18.747 19136-19166 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后deleteAppList数量: 2
2025-08-14 18:34:18.753 19136-19166 RuleBase                com.dspread.mdm.service              D  🔧 RuleBaseStorage Rule e195e52efa414316818a8d4b21fa4e9c 已存在，忽略Add操作
2025-08-14 18:34:18.759 19136-19166 RuleBase                com.dspread.mdm.service              D  🔧 RuleStateMachine 规则初始化成功: e195e52efa414316818a8d4b21fa4e9c -> RuleState(code=todo, description=等待执行)
2025-08-14 18:34:18.765 19136-19166 RuleBase                com.dspread.mdm.service              D  🔧 RuleStateMachine 规则已存在: e195e52efa414316818a8d4b21fa4e9c, 当前状态: RuleState(code=todo, description=等待执行)
2025-08-14 18:34:18.772 19136-19166 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 初始化规则应用状态: e195e52efa414316818a8d4b21fa4e9c, 应用数量: 2
2025-08-14 18:34:18.778 19136-19166 RuleBase                com.dspread.mdm.service              D  🔧 RuleStateMonitor 收到状态变化: e195e52efa414316818a8d4b21fa4e9c, todo -> R01
2025-08-14 18:34:18.784 19136-19166 RuleBase                com.dspread.mdm.service              D  🔧 RuleStateMonitor 状态变化已记录: e195e52efa414316818a8d4b21fa4e9c, R01
2025-08-14 18:34:18.791 19136-19166 RuleBase                com.dspread.mdm.service              D  🔧 RuleStateMachine 状态转换成功: e195e52efa414316818a8d4b21fa4e9c, todo -> R01
2025-08-14 18:34:18.797 19136-19166 RuleBase                com.dspread.mdm.service              D  🔧 RuleStateMonitor 收到状态变化: e195e52efa414316818a8d4b21fa4e9c, R01 -> R02
2025-08-14 18:34:18.803 19136-19166 RuleBase                com.dspread.mdm.service              D  🔧 RuleStateMonitor 状态变化已记录: e195e52efa414316818a8d4b21fa4e9c, R02
2025-08-14 18:34:18.809 19136-19166 RuleBase                com.dspread.mdm.service              D  🔧 RuleStateMachine 状态转换成功: e195e52efa414316818a8d4b21fa4e9c, R01 -> R02
2025-08-14 18:34:18.815 19136-19166 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 开始执行规则: e195e52efa414316818a8d4b21fa4e9c
2025-08-14 18:34:18.821 19136-19166 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 执行卸载应用，数量: 2
2025-08-14 18:34:18.827 19136-19166 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 开始卸载应用，数量: 2
2025-08-14 18:34:18.833 19136-19166 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 卸载应用: com.chileaf.cl960.sample
2025-08-14 18:34:18.840 19136-19166 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 更新应用状态: e195e52efa414316818a8d4b21fa4e9c, com.chileaf.cl960.sample -> D01
2025-08-14 18:34:18.847 19136-19166 WebSocket               com.dspread.mdm.service              I  🔧 C0107 主动式上送: rulebase_update (主动: 14)
2025-08-14 18:34:18.866 19136-19166 WebSocket               com.dspread.mdm.service              I  🔧 添加规则结果: ruleId=e195e52efa414316818a8d4b21fa4e9c
2025-08-14 18:34:18.895 19136-19166 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0107","serialNo":"01354090202503050399","request_time":"1755167658861","request_id":"1755167658861C0107","version":"1","data":{"ruleId":"e195e52efa414316818a8d4b21fa4e9c","taskResult":"R02","failedApkList":[{"packName":"com.chileaf.cl960.sample","apkName":"cl960","versionName":"1.1.0","versionCode":"1","apkUrl":"","apkMd5":"","apkSize":0,"installBy":"0","isSystemApp":false,"priority":0,"result":"D01","taskType":"02"},{"packName":"plus.H52FFB9A5","apkName":"逍遥助手","versionName":"1.0","versionCode":"100","apkUrl":"","apkMd5":"","apkSize":0,"installBy":"0","isSystemApp":false,"priority":0,"result":"W01","taskType":"02"}]},"myVersionName":"1.0.08.20250814.DSPREAD.MDM.SERVICE","terminalDate":"20250814183418"}
2025-08-14 18:34:18.901 19136-19166 WebSocket               com.dspread.mdm.service              I  🔧 规则结果实时上送: ruleId=e195e52efa414316818a8d4b21fa4e9c, result=R02 (1)
2025-08-14 18:34:18.907 19136-19166 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 上报规则执行中状态: e195e52efa414316818a8d4b21fa4e9c, 应用数量: 2
2025-08-14 18:34:18.913 19136-19166 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 注册卸载回调: com.chileaf.cl960.sample (规则ID: e195e52efa414316818a8d4b21fa4e9c)
2025-08-14 18:34:18.919 19136-19166 AppManager              com.dspread.mdm.service              I  ℹ️ Uninstalling app via PackageInstaller: com.chileaf.cl960.sample
2025-08-14 18:34:18.926 19136-19166 AppManager              com.dspread.mdm.service              I  ℹ️ Deleting package via PackageInstaller: com.chileaf.cl960.sample
2025-08-14 18:34:18.935 19136-19166 AppManager              com.dspread.mdm.service              I  ℹ️ PackageInstaller uninstall initiated
2025-08-14 18:34:18.941 19136-19166 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 卸载应用: plus.H52FFB9A5
2025-08-14 18:34:19.017 19136-19166 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 更新应用状态: e195e52efa414316818a8d4b21fa4e9c, plus.H52FFB9A5 -> D01
2025-08-14 18:34:19.023 19136-19166 WebSocket               com.dspread.mdm.service              I  🔧 C0107 主动式上送: rulebase_update (主动: 15)
2025-08-14 18:34:19.101 19136-19166 WebSocket               com.dspread.mdm.service              I  🔧 添加规则结果: ruleId=e195e52efa414316818a8d4b21fa4e9c
2025-08-14 18:34:19.137 19136-19166 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0107","serialNo":"01354090202503050399","request_time":"1755167659039","request_id":"1755167659039C0107","version":"1","data":{"ruleId":"e195e52efa414316818a8d4b21fa4e9c","taskResult":"R02","failedApkList":[{"packName":"com.chileaf.cl960.sample","apkName":"cl960","versionName":"1.1.0","versionCode":"1","apkUrl":"","apkMd5":"","apkSize":0,"installBy":"0","isSystemApp":false,"priority":0,"result":"D01","taskType":"02"},{"packName":"plus.H52FFB9A5","apkName":"逍遥助手","versionName":"1.0","versionCode":"100","apkUrl":"","apkMd5":"","apkSize":0,"installBy":"0","isSystemApp":false,"priority":0,"result":"D01","taskType":"02"}]},"myVersionName":"1.0.08.20250814.DSPREAD.MDM.SERVICE","terminalDate":"20250814183419"}
2025-08-14 18:34:19.145 19136-19166 WebSocket               com.dspread.mdm.service              I  🔧 规则结果实时上送: ruleId=e195e52efa414316818a8d4b21fa4e9c, result=R02 (1)
2025-08-14 18:34:19.154 19136-19166 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 上报规则执行中状态: e195e52efa414316818a8d4b21fa4e9c, 应用数量: 2
2025-08-14 18:34:19.162 19136-19166 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 注册卸载回调: plus.H52FFB9A5 (规则ID: e195e52efa414316818a8d4b21fa4e9c)
2025-08-14 18:34:19.170 19136-19166 AppManager              com.dspread.mdm.service              I  ℹ️ Uninstalling app via PackageInstaller: plus.H52FFB9A5
2025-08-14 18:34:19.184 19136-19166 AppManager              com.dspread.mdm.service              I  ℹ️ Deleting package via PackageInstaller: plus.H52FFB9A5
2025-08-14 18:34:19.197 19136-19166 AppManager              com.dspread.mdm.service              I  ℹ️ PackageInstaller uninstall initiated
2025-08-14 18:34:19.205 19136-19166 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 规则执行启动成功: e195e52efa414316818a8d4b21fa4e9c
2025-08-14 18:34:19.213 19136-19166 RuleBase                com.dspread.mdm.service              D  🔧 RuleBaseManager 规则添加成功: e195e52efa414316818a8d4b21fa4e9c
2025-08-14 18:34:19.219 19136-19136 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 收到广播: android.intent.action.PACKAGE_REMOVED
2025-08-14 18:34:19.228 19136-19136 Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 收到包更新广播: android.intent.action.PACKAGE_REMOVED, 包名: com.chileaf.cl960.sample
2025-08-14 18:34:19.234 19136-19136 Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 应用卸载: com.chileaf.cl960.sample
2025-08-14 18:34:19.241 19136-19136 Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 检查WebSocket状态: packageName=com.chileaf.cl960.sample, action=UNINSTALL
2025-08-14 18:34:19.248 19136-19136 Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 通知rulebase处理应用卸载: com.chileaf.cl960.sample
2025-08-14 18:34:19.254 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 收到系统广播，应用已卸载: com.chileaf.cl960.sample
2025-08-14 18:34:19.261 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 找到匹配的卸载回调，触发: com.chileaf.cl960.sample
2025-08-14 18:34:19.268 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 卸载回调被触发: pkg=com.chileaf.cl960.sample, returnCode=1, error=
2025-08-14 18:34:19.274 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 应用卸载启动成功: com.chileaf.cl960.sample
2025-08-14 18:34:19.281 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 从广播更新应用状态: e195e52efa414316818a8d4b21fa4e9c, com.chileaf.cl960.sample -> D02
2025-08-14 18:34:19.286 19136-19207 Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"request_time":"1755167658015","org_request_time":"1755167658861","org_request_id":"1755167658861C0107","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755167658015S0000","serialNo":"01354090202503050399"}
2025-08-14 18:34:19.288 19136-19136 WebSocket               com.dspread.mdm.service              I  🔧 C0107 主动式上送: rulebase_update (主动: 16)
2025-08-14 18:34:19.297 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 收到响应消息: transCode=S0000, orgRequestId=1755167658861C0107, state=0, remark=
2025-08-14 18:34:19.306 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 处理规则结果响应: state=0
2025-08-14 18:34:19.312 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 规则结果上传成功
2025-08-14 18:34:19.335 19136-19136 WebSocket               com.dspread.mdm.service              I  🔧 添加规则结果: ruleId=e195e52efa414316818a8d4b21fa4e9c
2025-08-14 18:34:19.365 19136-19136 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0107","serialNo":"01354090202503050399","request_time":"1755167659303","request_id":"1755167659303C0107","version":"1","data":{"ruleId":"e195e52efa414316818a8d4b21fa4e9c","taskResult":"R02","failedApkList":[{"packName":"com.chileaf.cl960.sample","apkName":"cl960","versionName":"1.1.0","versionCode":"1","apkUrl":"","apkMd5":"","apkSize":0,"installBy":"0","isSystemApp":false,"priority":0,"result":"D02","taskType":"02"},{"packName":"plus.H52FFB9A5","apkName":"逍遥助手","versionName":"1.0","versionCode":"100","apkUrl":"","apkMd5":"","apkSize":0,"installBy":"0","isSystemApp":false,"priority":0,"result":"D01","taskType":"02"}]},"myVersionName":"1.0.08.20250814.DSPREAD.MDM.SERVICE","terminalDate":"20250814183419"}
2025-08-14 18:34:19.373 19136-19136 WebSocket               com.dspread.mdm.service              I  🔧 规则结果实时上送: ruleId=e195e52efa414316818a8d4b21fa4e9c, result=R02 (1)
2025-08-14 18:34:19.380 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 上报规则执行中状态: e195e52efa414316818a8d4b21fa4e9c, 应用数量: 2
2025-08-14 18:34:19.387 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 开始检查规则完成状态: e195e52efa414316818a8d4b21fa4e9c, 总应用数: 2
2025-08-14 18:34:19.394 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 检查应用状态: com.chileaf.cl960.sample -> D02
2025-08-14 18:34:19.401 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 发现成功状态: com.chileaf.cl960.sample -> D02
2025-08-14 18:34:19.408 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 检查应用状态: plus.H52FFB9A5 -> D01
2025-08-14 18:34:19.415 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 发现未完成状态: plus.H52FFB9A5 -> D01
2025-08-14 18:34:19.423 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 规则完成检查结果: e195e52efa414316818a8d4b21fa4e9c, 全部完成: false, 完成数: 1/2, 有失败: false
2025-08-14 18:34:19.431 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 规则未完成: e195e52efa414316818a8d4b21fa4e9c, 完成数: 1/2
2025-08-14 18:34:19.438 19136-19136 Platform                com.dspread.mdm.service              D  🔧 重新收集应用信息（强制刷新）
2025-08-14 18:34:19.503 19136-19207 Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"request_time":"1755167658484","org_request_time":"1755167659303","org_request_id":"1755167659303C0107","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755167658484S0000","serialNo":"01354090202503050399"}
2025-08-14 18:34:19.515 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 收到响应消息: transCode=S0000, orgRequestId=1755167659303C0107, state=0, remark=
2025-08-14 18:34:19.522 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 处理规则结果响应: state=0
2025-08-14 18:34:19.529 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 规则结果上传成功
2025-08-14 18:34:19.545 19136-19136 AppManager              com.dspread.mdm.service              I  ℹ️ 应用信息获取完成: 总数130(系统129/用户1) 返回1个
2025-08-14 18:34:19.559 19136-19136 Platform                com.dspread.mdm.service              D  🔧 应用信息: 1 个应用
2025-08-14 18:34:19.567 19136-19136 Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 应用信息缓存刷新完成
2025-08-14 18:34:19.574 19136-19136 ContextImpl             com.dspread.mdm.service              W  Calling a method in the system process without a qualified user: android.app.ContextImpl.sendBroadcast:1220 android.content.ContextWrapper.sendBroadcast:504 com.dspread.mdm.service.broadcast.core.BroadcastSender.sendBroadcast:99 com.dspread.mdm.service.broadcast.core.BroadcastSender.sendBroadcast:23 com.dspread.mdm.service.broadcast.handlers.system.PackageUpdateEventHandlerImpl.sendAppRefreshBroadcast:491 
2025-08-14 18:34:19.577 19136-19136 Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 发送应用刷新广播
2025-08-14 18:34:19.584 19136-19136 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 收到广播: android.intent.action.PACKAGE_REMOVED
2025-08-14 18:34:19.592 19136-19136 Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 收到包更新广播: android.intent.action.PACKAGE_REMOVED, 包名: plus.H52FFB9A5
2025-08-14 18:34:19.599 19136-19136 Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 应用卸载: plus.H52FFB9A5
2025-08-14 18:34:19.606 19136-19136 Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 检查WebSocket状态: packageName=plus.H52FFB9A5, action=UNINSTALL
2025-08-14 18:34:19.613 19136-19136 Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 通知rulebase处理应用卸载: plus.H52FFB9A5
2025-08-14 18:34:19.619 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 收到系统广播，应用已卸载: plus.H52FFB9A5
2025-08-14 18:34:19.626 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 找到匹配的卸载回调，触发: plus.H52FFB9A5
2025-08-14 18:34:19.633 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 卸载回调被触发: pkg=plus.H52FFB9A5, returnCode=1, error=
2025-08-14 18:34:19.640 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 应用卸载启动成功: plus.H52FFB9A5
2025-08-14 18:34:19.647 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 从广播更新应用状态: e195e52efa414316818a8d4b21fa4e9c, plus.H52FFB9A5 -> D02
2025-08-14 18:34:19.654 19136-19136 WebSocket               com.dspread.mdm.service              I  🔧 C0107 主动式上送: rulebase_update (主动: 17)
2025-08-14 18:34:19.677 19136-19136 WebSocket               com.dspread.mdm.service              I  🔧 添加规则结果: ruleId=e195e52efa414316818a8d4b21fa4e9c
2025-08-14 18:34:19.708 19136-19136 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0107","serialNo":"01354090202503050399","request_time":"1755167659670","request_id":"1755167659670C0107","version":"1","data":{"ruleId":"e195e52efa414316818a8d4b21fa4e9c","taskResult":"R02","failedApkList":[{"packName":"com.chileaf.cl960.sample","apkName":"cl960","versionName":"1.1.0","versionCode":"1","apkUrl":"","apkMd5":"","apkSize":0,"installBy":"0","isSystemApp":false,"priority":0,"result":"D02","taskType":"02"},{"packName":"plus.H52FFB9A5","apkName":"逍遥助手","versionName":"1.0","versionCode":"100","apkUrl":"","apkMd5":"","apkSize":0,"installBy":"0","isSystemApp":false,"priority":0,"result":"D02","taskType":"02"}]},"myVersionName":"1.0.08.20250814.DSPREAD.MDM.SERVICE","terminalDate":"20250814183419"}
2025-08-14 18:34:19.714 19136-19136 WebSocket               com.dspread.mdm.service              I  🔧 规则结果实时上送: ruleId=e195e52efa414316818a8d4b21fa4e9c, result=R02 (1)
2025-08-14 18:34:19.721 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 上报规则执行中状态: e195e52efa414316818a8d4b21fa4e9c, 应用数量: 2
2025-08-14 18:34:19.728 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 开始检查规则完成状态: e195e52efa414316818a8d4b21fa4e9c, 总应用数: 2
2025-08-14 18:34:19.735 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 检查应用状态: com.chileaf.cl960.sample -> D02
2025-08-14 18:34:19.742 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 发现成功状态: com.chileaf.cl960.sample -> D02
2025-08-14 18:34:19.749 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 检查应用状态: plus.H52FFB9A5 -> D02
2025-08-14 18:34:19.755 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 发现成功状态: plus.H52FFB9A5 -> D02
2025-08-14 18:34:19.763 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 规则完成检查结果: e195e52efa414316818a8d4b21fa4e9c, 全部完成: true, 完成数: 2/2, 有失败: false
2025-08-14 18:34:19.770 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 规则执行全部成功，上送RULEBASED_SUCCESS: e195e52efa414316818a8d4b21fa4e9c
2025-08-14 18:34:19.777 19136-19136 WebSocket               com.dspread.mdm.service              I  🔧 C0107 主动式上送: rulebase_update (主动: 18)
2025-08-14 18:34:19.799 19136-19136 WebSocket               com.dspread.mdm.service              I  🔧 添加规则结果: ruleId=e195e52efa414316818a8d4b21fa4e9c
2025-08-14 18:34:19.831 19136-19136 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0107","serialNo":"01354090202503050399","request_time":"1755167659793","request_id":"1755167659793C0107","version":"1","data":{"ruleId":"e195e52efa414316818a8d4b21fa4e9c","taskResult":"R03","failedApkList":[{"packName":"com.chileaf.cl960.sample","apkName":"cl960","versionName":"1.1.0","versionCode":"1","apkUrl":"","apkMd5":"","apkSize":0,"installBy":"0","isSystemApp":false,"priority":0,"result":"D02","taskType":"02"},{"packName":"plus.H52FFB9A5","apkName":"逍遥助手","versionName":"1.0","versionCode":"100","apkUrl":"","apkMd5":"","apkSize":0,"installBy":"0","isSystemApp":false,"priority":0,"result":"D02","taskType":"02"}]},"myVersionName":"1.0.08.20250814.DSPREAD.MDM.SERVICE","terminalDate":"20250814183419"}
2025-08-14 18:34:19.839 19136-19136 WebSocket               com.dspread.mdm.service              I  🔧 规则结果实时上送: ruleId=e195e52efa414316818a8d4b21fa4e9c, result=R03 (1)
2025-08-14 18:34:19.846 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 上报规则状态: e195e52efa414316818a8d4b21fa4e9c -> R03
2025-08-14 18:34:19.853 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleStateMonitor 收到状态变化: e195e52efa414316818a8d4b21fa4e9c, R02 -> R03
2025-08-14 18:34:19.861 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleStateMonitor 状态变化已记录: e195e52efa414316818a8d4b21fa4e9c, R03
2025-08-14 18:34:19.868 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleStateMachine 状态转换成功: e195e52efa414316818a8d4b21fa4e9c, R02 -> R03
2025-08-14 18:34:19.875 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 规则执行完成: e195e52efa414316818a8d4b21fa4e9c, 有失败: false
2025-08-14 18:34:19.882 19136-19136 Platform                com.dspread.mdm.service              D  🔧 重新收集应用信息（强制刷新）
2025-08-14 18:34:19.976 19136-19207 Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"request_time":"1755167658831","org_request_time":"1755167659670","org_request_id":"1755167659670C0107","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755167658831S0000","serialNo":"01354090202503050399"}
2025-08-14 18:34:19.986 19136-19136 AppManager              com.dspread.mdm.service              I  ℹ️ 应用信息获取完成: 总数130(系统129/用户1) 返回1个
2025-08-14 18:34:19.990 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 收到响应消息: transCode=S0000, orgRequestId=1755167659670C0107, state=0, remark=
2025-08-14 18:34:19.997 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 处理规则结果响应: state=0
2025-08-14 18:34:19.999 19136-19136 Platform                com.dspread.mdm.service              D  🔧 应用信息: 1 个应用
2025-08-14 18:34:20.004 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 规则结果上传成功
2025-08-14 18:34:20.006 19136-19136 Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 应用信息缓存刷新完成
2025-08-14 18:34:20.013 19136-19136 ContextImpl             com.dspread.mdm.service              W  Calling a method in the system process without a qualified user: android.app.ContextImpl.sendBroadcast:1220 android.content.ContextWrapper.sendBroadcast:504 com.dspread.mdm.service.broadcast.core.BroadcastSender.sendBroadcast:99 com.dspread.mdm.service.broadcast.core.BroadcastSender.sendBroadcast:23 com.dspread.mdm.service.broadcast.handlers.system.PackageUpdateEventHandlerImpl.sendAppRefreshBroadcast:491 
2025-08-14 18:34:20.015 19136-19136 Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 发送应用刷新广播
2025-08-14 18:34:20.367 19136-19207 Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"request_time":"1755167659061","org_request_time":"1755167659793","org_request_id":"1755167659793C0107","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755167659061S0000","serialNo":"01354090202503050399"}
2025-08-14 18:34:20.378 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 收到响应消息: transCode=S0000, orgRequestId=1755167659793C0107, state=0, remark=
2025-08-14 18:34:20.384 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 处理规则结果响应: state=0
2025-08-14 18:34:20.391 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 规则结果上传成功
