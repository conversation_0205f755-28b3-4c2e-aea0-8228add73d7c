package com.bbpos.wiseapp.tms.listener.device;

import android.app.usage.StorageStatsManager;
import android.content.Context;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import android.os.Build;
import android.os.Environment;
import android.os.StatFs;
import android.os.storage.StorageManager;
import android.os.storage.StorageVolume;
import androidx.annotation.RequiresApi;
import android.telephony.TelephonyManager;
import android.util.Log;

import com.bbpos.wiseapp.logger.BBLog;
import com.bbpos.wiseapp.service.R;
import com.bbpos.wiseapp.system.api.SysIntermediateApi;
import com.bbpos.wiseapp.tms.utils.ContextUtil;
import com.bbpos.wiseapp.tms.utils.Constants;
import com.bbpos.wiseapp.tms.utils.Helpers;
import com.bbpos.wiseapp.tms.utils.ParameterName;
import com.bbpos.wiseapp.utils.SystemUtils;

import org.json.JSONException;
import org.json.JSONObject;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.io.IOException;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.List;
import java.util.Locale;
import java.util.UUID;

import com.bbpos.wiseapp.sdk.device.DeviceStatus;

public class HardwareInfo {
	private static final String TAG = Constants.TAG;
	public static long usedBytes = 0;
	public static long totalBytes = 0;

	/**获取已使用内部存储空间占总空间的信息*/
	@SuppressWarnings("deprecation")
	public static JSONObject getCpuInfo()
	{
		JSONObject data = new JSONObject();
		String rate = SystemUtils.getCPURateDesc_All();
		try {
			data.put(ParameterName.typeNameHWInfo, null);
			data.put(ParameterName.nameHWInfo, ContextUtil.getInstance().getResources().getString(R.string.cpu_usage));
			data.put(ParameterName.descHWInfo, rate + "");
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		return data;
	}

	public static JSONObject getCpuInfo(String rate)
	{
		JSONObject data = new JSONObject();
		try {
			data.put(ParameterName.typeNameHWInfo, null);
			data.put(ParameterName.nameHWInfo, ContextUtil.getInstance().getResources().getString(R.string.cpu_usage));
			data.put(ParameterName.descHWInfo, rate + "");
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		return data;
	}

	/**获取已使用内部存储空间占总空间的信息*/
	@SuppressWarnings("deprecation")
	public static JSONObject getROMInfo()
	{
		JSONObject data = new JSONObject();
		// read ROM
		queryWithStorageManager(ContextUtil.getInstance());
		String usedMemory = "";
		String totalMemory = "";
		if (Build.VERSION.SDK_INT < Build.VERSION_CODES.M) {//小于6.0
			usedMemory = getUnit(usedBytes, 1024);
			totalMemory = getUnit(totalBytes, 1024);
		} else {
			usedMemory = getUnit(usedBytes, 1000);
			totalMemory = getUnit(totalBytes, 1000);
		}
	    try {
	    	data.put(ParameterName.typeNameHWInfo, null);
	    	data.put(ParameterName.nameHWInfo, ContextUtil.getInstance().getResources().getString(R.string.storage_usage_status));
			data.put(ParameterName.descHWInfo, usedMemory + "/"+totalMemory);
			if(isROMOverflow(0))
				data.put(ParameterName.stateHWInfo, "0");
			else
				data.put(ParameterName.stateHWInfo, "1");
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		return data;
	}

	public static JSONObject getROMInfo(String rom_usage)
	{
		JSONObject data = new JSONObject();
		try {
			data.put(ParameterName.typeNameHWInfo, null);
			data.put(ParameterName.nameHWInfo, ContextUtil.getInstance().getResources().getString(R.string.storage_usage_status));
			data.put(ParameterName.descHWInfo, rom_usage);
			if(isROMOverflow(0))
				data.put(ParameterName.stateHWInfo, "0");
			else
				data.put(ParameterName.stateHWInfo, "1");
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		return data;
	}

	/**判断剩余内部存储空间是否达到阈值*/
	@SuppressWarnings("deprecation")
	public static boolean isROMOverflow(long fileSize)
	{
		StatFs fs = new StatFs(Environment.getDataDirectory().getPath());
	    long freeBytes = (long)fs.getFreeBlocks() * (long)fs.getBlockSize();
	    if(freeBytes - fileSize <(Constants.STORAGE_REMAIN_MIN*1024*1024)){
	    	return true;
	    }
	    return false;
	}
	/**获取内存总容量*/
	public static JSONObject getTotalMem(Context context){
		/**获取当前系统总内存*/
		JSONObject data = new JSONObject();
		String str1 = "/proc/meminfo";
		String str2;
		String[] arrayOfString;
		long totalMemory = 0;
		long freeMemory = 0;
		long availableMemory = 0;
		long buffers = 0;
		long cached = 0;

		BufferedReader localBufferedReader = null;
		try {
			FileReader localFileReader = new FileReader(str1);
			localBufferedReader = new BufferedReader(localFileReader, 8192);
			/**读取meminfo第一行，当前系统总内存大小 */
			str2 = localBufferedReader.readLine();
			arrayOfString = str2.split("\\s+");
			totalMemory = Integer.valueOf(arrayOfString[1]).intValue();
			str2 = localBufferedReader.readLine();
			arrayOfString = str2.split("\\s+");
			freeMemory = Integer.valueOf(arrayOfString[1]).intValue();
			str2 = localBufferedReader.readLine();
			arrayOfString = str2.split("\\s+");
			availableMemory = Integer.valueOf(arrayOfString[1]).intValue();
			str2 = localBufferedReader.readLine();
			arrayOfString = str2.split("\\s+");
			buffers = Integer.valueOf(arrayOfString[1]).intValue();
			str2 = localBufferedReader.readLine();
			arrayOfString = str2.split("\\s+");
			cached = Integer.valueOf(arrayOfString[1]).intValue();
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			try {
				if (localBufferedReader != null) {
					localBufferedReader.close();
					localBufferedReader=null;
				}
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
		/**将获取当前系统总内存规格化为MB*/
		String totalMemStr = String.format(Locale.US, " %.2f%s ", totalMemory / (1024*1024.00), "GB");
		String usedMemStr = String.format(Locale.US, " %.2f%s ", (totalMemory-freeMemory-buffers-cached) / (1024*1024.00), "GB");
		BBLog.d(BBLog.TAG,"内存总容量为：" + totalMemStr);
		BBLog.d(BBLog.TAG,"内存使用量为：" + usedMemStr);
		try {
			data.put(ParameterName.typeNameHWInfo, null);
			data.put(ParameterName.nameHWInfo, ContextUtil.getInstance().getResources().getString(R.string.total_memory));
			data.put(ParameterName.descHWInfo, usedMemStr + "/"+totalMemStr);
			if(totalMemory<=0)
				data.put(ParameterName.stateHWInfo, "0");
			else
				data.put(ParameterName.stateHWInfo, "1");
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		BBLog.d(BBLog.TAG, "getTotalMem: "+data.toString());
		return data;
	}

	public static JSONObject getTotalMem(Context context, String mem_usage){
		/**获取当前系统总内存*/
		JSONObject data = new JSONObject();
		try {
			data.put(ParameterName.typeNameHWInfo, null);
			data.put(ParameterName.nameHWInfo, ContextUtil.getInstance().getResources().getString(R.string.total_memory));
			data.put(ParameterName.descHWInfo, mem_usage);
			data.put(ParameterName.stateHWInfo, "1");
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		BBLog.d(BBLog.TAG, "getTotalMem: "+data.toString());
		return data;
	}

	/** 获取当前使用网络类型*/
	@SuppressWarnings("static-access")
	public static JSONObject getCurrentNetType(Context context){
		JSONObject data = new JSONObject();
		String type = "";
		ConnectivityManager cm = (ConnectivityManager) context.getSystemService(context.CONNECTIVITY_SERVICE);
		NetworkInfo info = cm.getActiveNetworkInfo();
		if(info == null){
			type = "null";
		}else if(info.getType() == ConnectivityManager.TYPE_WIFI){
			type = "wifi ";
		}else if(info.getType() == ConnectivityManager.TYPE_MOBILE){
			int mobileType = info.getSubtype();
			if (mobileType == TelephonyManager.NETWORK_TYPE_CDMA || mobileType == TelephonyManager.NETWORK_TYPE_GPRS
					|| mobileType == TelephonyManager.NETWORK_TYPE_EDGE) {
				type = "2G ";
			}else if(mobileType == TelephonyManager.NETWORK_TYPE_UMTS || mobileType == TelephonyManager.NETWORK_TYPE_HSDPA
					|| mobileType == TelephonyManager.NETWORK_TYPE_EVDO_A || mobileType == TelephonyManager.NETWORK_TYPE_EVDO_0
					|| mobileType == TelephonyManager.NETWORK_TYPE_EVDO_B){
				type = "3G ";
			}else if(mobileType == TelephonyManager.NETWORK_TYPE_LTE){
				type = "4G ";
			}
		}
		try {
			data.put(ParameterName.typeNameHWInfo, null);
			data.put(ParameterName.nameHWInfo, ContextUtil.getInstance().getResources().getString(R.string.network_status));
			data.put(ParameterName.descHWInfo, type);
			data.put(ParameterName.stateHWInfo,"1");
		} catch (JSONException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		BBLog.d(BBLog.TAG, "使用的网络类型为：" + type);
		return data;
	}
	public static JSONObject getAndroidVer(){
		JSONObject data = new JSONObject();
		try {
			data.put(ParameterName.typeNameHWInfo, null);
			data.put(ParameterName.nameHWInfo,ParameterName.androidVersionNo);
			data.put(ParameterName.descHWInfo, Build.VERSION.RELEASE);
			data.put(ParameterName.stateHWInfo, "1");
		} catch (JSONException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		return data;
	}

	public static JSONObject getOSVer(){
		JSONObject data = new JSONObject();
		if(!Helpers.isStrNoEmpty(MidwareInterface.osVer))
			MidwareInterface.init(ContextUtil.getInstance());
		try {
			data.put(ParameterName.typeNameHWInfo, null);
			data.put(ParameterName.nameHWInfo,ParameterName.OSVerNo);
			data.put(ParameterName.descHWInfo, MidwareInterface.osVer);
			if(Helpers.isStrNoEmpty(MidwareInterface.osVer))
				data.put(ParameterName.stateHWInfo, "1");
			else
				data.put(ParameterName.stateHWInfo, "0");
		} catch (JSONException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		return data;
	}

	/**
	 * 获取由AP + SP 组成的firmware version
	 * @return
	 */
	public static JSONObject getFwVer(){
		JSONObject data = new JSONObject();
		try {
			data.put(ParameterName.typeNameHWInfo, null);
			data.put(ParameterName.nameHWInfo,ParameterName.firmwareVersionNo);
			data.put(ParameterName.descHWInfo, SysIntermediateApi.getIntance().getProp("persist.bbpos.firmware_version"));
			data.put(ParameterName.stateHWInfo, "1");
		} catch (JSONException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		return data;
	}

	/**
	 * 打印机状态
	 * @return
	 */
	public static DeviceStatus deviceStatus = null;
	public static JSONObject getPrinterStatus(){
		JSONObject data = new JSONObject();

		try {
			data.put(ParameterName.typeNameHWInfo, null);
			data.put(ParameterName.nameHWInfo,"Printer");
			data.put(ParameterName.descHWInfo,deviceStatus.getStatusMsg());

			if(deviceStatus.getStatusCode().equals("00"))
				data.put(ParameterName.stateHWInfo, "1");
			else
				data.put(ParameterName.stateHWInfo, "0");
		} catch (JSONException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		return data;
	}

	public static void queryWithStorageManager(Context context) {
		//5.0 查外置存储
		StorageManager storageManager = (StorageManager) context.getSystemService(Context.STORAGE_SERVICE);
		float unit = 1024, unit2 = 1000;
		int version = Build.VERSION.SDK_INT;
		if (version < Build.VERSION_CODES.M) {//小于6.0
			try {
				Method getVolumeList = StorageManager.class.getDeclaredMethod("getVolumeList");
				StorageVolume[] volumeList = (StorageVolume[]) getVolumeList.invoke(storageManager);
				long totalSize = 0, usedSize = 0;
				if (volumeList != null) {
					Method getPathFile = null;
					for (StorageVolume volume : volumeList) {
						if (getPathFile == null) {
							getPathFile = volume.getClass().getDeclaredMethod("getPathFile");
						}
						File file = (File) getPathFile.invoke(volume);
						totalSize += file.getTotalSpace();
						usedSize += file.getUsableSpace();
					}
				}
				totalBytes = totalSize;
				usedBytes = totalSize - usedSize;
				BBLog.d(TAG, "totalSize = " + getUnit(totalSize, unit) + " ,availableSize = " + getUnit(usedSize, unit));
			} catch (NoSuchMethodException e) {
				e.printStackTrace();
			} catch (IllegalAccessException e) {
				e.printStackTrace();
			} catch (InvocationTargetException e) {
				e.printStackTrace();
			}
		} else {
			try {
				Method getVolumes = StorageManager.class.getDeclaredMethod("getVolumes");//6.0
				List<Object> getVolumeInfo = (List<Object>) getVolumes.invoke(storageManager);
				long total = 0L, used = 0L, systemSize = 0L;
				for (Object obj : getVolumeInfo) {

					Field getType = obj.getClass().getField("type");
					int type = getType.getInt(obj);
					if (type == 1) {//TYPE_PRIVATE

						long totalSize = 0L;

						//获取内置内存总大小
						if (version >= Build.VERSION_CODES.O) {//8.0
							Method getFsUuid = obj.getClass().getDeclaredMethod("getFsUuid");
							String fsUuid = (String) getFsUuid.invoke(obj);
							totalSize = getTotalSize(context, fsUuid);//8.0 以后使用
						} else if (version >= Build.VERSION_CODES.N_MR1) {//7.1.1
							Method getPrimaryStorageSize = StorageManager.class.getMethod("getPrimaryStorageSize");//5.0 6.0 7.0没有
							totalSize = (long) getPrimaryStorageSize.invoke(storageManager);
						}

						Method isMountedReadable = obj.getClass().getDeclaredMethod("isMountedReadable");
						boolean readable = (boolean) isMountedReadable.invoke(obj);
						if (readable) {
							Method file = obj.getClass().getDeclaredMethod("getPath");
							File f = (File) file.invoke(obj);

							if (totalSize == 0) {
								totalSize = f.getTotalSpace();
							}
							systemSize = totalSize - f.getTotalSpace();
							used += totalSize - f.getFreeSpace();
							total += totalSize;
						}
					} else if (type == 0) {//TYPE_PUBLIC
						//外置存储
//						Method isMountedReadable = obj.getClass().getDeclaredMethod("isMountedReadable");
//						boolean readable = (boolean) isMountedReadable.invoke(obj);
//						if (readable) {
//							Method file = obj.getClass().getDeclaredMethod("getPath");
//							File f = (File) file.invoke(obj);
//							used += f.getTotalSpace() - f.getFreeSpace();
//							total += f.getTotalSpace();
//						}
					} else if (type == 2) {//TYPE_EMULATED

					}
				}

				totalBytes = total;
				usedBytes = used;
				BBLog.d(TAG, "总内存 total = " + getUnit(total, unit2) + "已用 used(with system) = " + getUnit(used, 1000)
						+ "可用 available = " + getUnit(total - used, unit2) + "系统大小：" + getUnit(systemSize, unit2));
			} catch (SecurityException e) {
				BBLog.e(TAG, "缺少权限：permission.PACKAGE_USAGE_STATS");
			} catch (Exception e) {
				e.printStackTrace();
				queryWithStatFs();
			}
		}
	}

	private static void queryWithStatFs() {
		StatFs statFs = new StatFs(Environment.getExternalStorageDirectory().getPath());

		//存储块
		long blockCount = statFs.getBlockCount();
		//块大小
		long blockSize = statFs.getBlockSize();
		//可用块数量
		long availableCount = statFs.getAvailableBlocks();
		//剩余块数量，注：这个包含保留块（including reserved blocks）即应用无法使用的空间
		long freeBlocks = statFs.getFreeBlocks();

		//level 18
//        long totalSize = statFs.getTotalBytes();
//        long availableSize = statFs.getAvailableBytes();

		BBLog.d(TAG, "=========");
		BBLog.d(TAG, "total = " + getUnit(blockSize * blockCount, 1000));
		BBLog.d(TAG, "available = " + getUnit(blockSize * availableCount, 1000));
		BBLog.d(TAG, "free = " + getUnit(blockSize * freeBlocks, 1000));
		totalBytes = blockSize * blockCount;
		usedBytes = blockSize * availableCount;
	}

	private static String[] units = {"B", "KB", "MB", "GB", "TB"};

	/**
	 * 进制转换
	 */
	public static String getUnit(float size, float base) {
		int index = 0;
		while (index < 3) {
			size = size / base;
			index++;
		}
		return String.format(Locale.US, " %.2f%s ", size, units[index]);
	}

	/**
	 * API 26 android O
	 * 获取总共容量大小，包括系统大小
	 */
	@RequiresApi(Build.VERSION_CODES.O)
	private static long getTotalSize(Context context, String fsUuid) {
		try {
			UUID id;
			if (fsUuid == null) {
				id = StorageManager.UUID_DEFAULT;
			} else {
				id = UUID.fromString(fsUuid);
			}
			StorageStatsManager stats = context.getSystemService(StorageStatsManager.class);
			return stats.getTotalBytes(id);
		} catch (NoSuchFieldError | NoClassDefFoundError | NullPointerException | IOException e) {
			e.printStackTrace();
			return -1;
		}
	}
}
