package com.bbpos.wiseapp.tms.utils;

public class ServerReturnCode {
	/**
	 *     返回码
    01; //成功
    00; //异常
    02; //错误需重新轮询获取信息

    40: //上传错误,需要从头开始上传文件

    50; //需重新轮询获取列表,原因是任务被取消/修改至更晚的时间/修改了依赖关系
    51; //无此任务,需要放弃此任务
    52; //后台经过验证,发现此任务无法执行,此时系统会响应具体的msg信息,终端得到msg返回任务状态的时候再返msg给系统
    53; //无应用 应用不可见
    54; //已是最新版
    55; //任务已过期
    
    61; //未付费
    62; //没有购买期限
    63; //无试用信息
    64;//终端应用已激活无试用
    65;//终端用户未登陆

    90; //无此终端
    91; //参数为空
    92; //无此交易类型
    93; //缺少参数
    94; //终端状态异常
    95; //终端未绑定商户
    96; //数据异常
    97: //参数错误
	 */
	public static final String SUCCESS = "01";
	public static final String TASKNOEXIST = "51";
	public static final String NOEXISTAPK = "53";
	public static final String NOWISLAST = "54";
	public static final String FILENOTEXIST = "56";
	/**终端不存在*/
	public static final String NO_TER_IN_SERVER = "90";
	/**后台接收数据格式错误返回码*/
	public static final String DATA_FAULT = "96";
}
