package com.bbpos.wiseapp.websocket.nv;

import android.content.Context;

import com.bbpos.BaseWebSocketHandlerCenter;
import com.bbpos.wiseapp.EncrypUtil;
import com.bbpos.wiseapp.ZipJava;
import com.bbpos.wiseapp.logger.BBLog;
import com.bbpos.wiseapp.tms.utils.Constants;
import com.bbpos.wiseapp.utils.RSAUtils;
import com.bbpos.wiseapp.websocket.Response;
import com.bbpos.wiseapp.websocket.WebSocketManager;

/**
 * NV-WebSocket-Client
 */
public class NvWebSocketHandlerCenter extends BaseWebSocketHandlerCenter {

    public NvWebSocketHandlerCenter(Context context) {
        super(context);
    }

    @Override
    public void sendText(String text) {
       WsManager.getInstance().sendMsg(text);
    }

    @Override
    public void disconnect() {
        WsManager.getInstance().disconnect();
    }

    @Override
    public void reconnect() {
       WsManager.getInstance().reconnect();
    }

    @Override
    public void onMessageResponse(Response message) {
        super.onMessageResponse(message);
        String respone = "";
        try {
            if (Constants.M_WEBSOCKET_MSGVER==3) {
                /*
                if (m_need_rsa) {
                    String[] list = message.getResponseText().split("&signature=");
                    if (list.length == 2) {
                        if (RSAUtils.verify(list[0].getBytes(), WebSocketManager.m_server_public_key, list[1])) {
                            respone = new String(EncrypUtil.decryptByPrivateKey(ZipJava.decompress(list[0].getBytes("ISO-8859-1")), m_private_key));
                        } else {
                            BBLog.i(BBLog.TAG, "[WebSocket] ====== 报文验签错误");
                        }
                    }
                } else {
                    respone = new String(ZipJava.decompress(message.getResponseText().getBytes("ISO-8859-1")));
                }
                 */
                String[] list = message.getResponseText().split("&signature=");
                respone = new String(EncrypUtil.decryptByPrivateKey(ZipJava.decompress(list[0].getBytes("ISO-8859-1")), m_private_key));
            } else if (Constants.M_WEBSOCKET_MSGVER == 2) {
                if (m_need_rsa) {
                    respone = new String(EncrypUtil.decryptByPrivateKey(ZipJava.decompress(message.getResponseText().getBytes("ISO-8859-1")), m_private_key));
                } else {
                    respone = new String(ZipJava.decompress(message.getResponseText().getBytes("ISO-8859-1")));
                }
            } else {
                if (m_need_rsa) {
                    respone = new String(EncrypUtil.decryptByPrivateKey(EncrypUtil.decode(message.getResponseText()), m_private_key));
                } else {
                    respone = message.getResponseText();
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        if (!respone.contains("\"tranCode\":\"ST006\"")) {
            BBLog.e(TAG, "onMessageResponse: " + respone);
        }

        if (mListener!=null) {
            mListener.onMessageRecv(respone);
        }
    }
}
