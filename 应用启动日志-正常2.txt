--------- beginning of system
--------- beginning of crash
--------- beginning of main
2025-08-20 15:19:57.544  2865-2865  nativeloader            com.dspread.mdm.service              D  Configuring clns-4 for other apk /data/app/~~TcsnnCMpwBlnJRUxZ78ysw==/com.dspread.mdm.service-C82msJq11GBldvSPqEbgvA==/base.apk. target_sdk_version=34, uses_libraries=, library_path=/data/app/~~TcsnnCMpwBlnJRUxZ78ysw==/com.dspread.mdm.service-C82msJq11GBldvSPqEbgvA==/lib/arm:/data/app/~~TcsnnCMpwBlnJRUxZ78ysw==/com.dspread.mdm.service-C82msJq11GBldvSPqEbgvA==/base.apk!/lib/armeabi-v7a, permitted_path=/data:/mnt/expand:/data/user/0/com.dspread.mdm.service
2025-08-20 15:19:57.573  2865-2865  GraphicsEnvironment     com.dspread.mdm.service              V  Currently set values for:
2025-08-20 15:19:57.573  2865-2865  GraphicsEnvironment     com.dspread.mdm.service              V    angle_gl_driver_selection_pkgs=[]
2025-08-20 15:19:57.573  2865-2865  GraphicsEnvironment     com.dspread.mdm.service              V    angle_gl_driver_selection_values=[]
2025-08-20 15:19:57.575  2865-2865  GraphicsEnvironment     com.dspread.mdm.service              V  ANGLE GameManagerService for com.dspread.mdm.service: false
2025-08-20 15:19:57.575  2865-2865  GraphicsEnvironment     com.dspread.mdm.service              V  com.dspread.mdm.service is not listed in per-application setting
2025-08-20 15:19:57.575  2865-2865  GraphicsEnvironment     com.dspread.mdm.service              V  Neither updatable production driver nor prerelease driver is supported.
2025-08-20 15:19:57.604  2865-2865  Compatibil...geReporter com.dspread.mdm.service              D  Compat change id reported: 183155436; UID 1000; state: ENABLED
2025-08-20 15:19:57.640  2865-2865  MbrainDebugManagerImpl  com.dspread.mdm.service              D  getService failed
2025-08-20 15:19:57.757  2865-2865  Common                  com.dspread.mdm.service              D  🔧 日志配置：测试环境模式
2025-08-20 15:19:57.774  2865-2865  Common                  com.dspread.mdm.service              D  🔧 [DebugConfig] 调试模式已启用
2025-08-20 15:19:57.781  2865-2865  Common                  com.dspread.mdm.service              D  🔧 SmartMdmServiceApp: 统一配置管理器初始化完成
2025-08-20 15:19:57.788  2865-2865  Common                  com.dspread.mdm.service              D  🔧 SmartMdmServiceApp: 开始初始化系统目录...
2025-08-20 15:19:57.795  2865-2865  Common                  com.dspread.mdm.service              D  🔧 SmartMdmServiceApp: 配置目录已存在 - /data/pos/config
2025-08-20 15:19:57.803  2865-2865  Common                  com.dspread.mdm.service              D  🔧 SmartMdmServiceApp: Logo目录已存在 - /data/data/com.dspread.mdm.service/files/media/logo
2025-08-20 15:19:57.811  2865-2865  Common                  com.dspread.mdm.service              D  🔧 SmartMdmServiceApp: 开机动画目录已存在 - /data/data/com.dspread.mdm.service/files/media/anim
2025-08-20 15:19:57.818  2865-2865  Common                  com.dspread.mdm.service              D  🔧 SmartMdmServiceApp: 系统目录初始化完成
2025-08-20 15:19:57.903  2865-2865  Receiver                com.dspread.mdm.service              D  🔧 ServiceGuardEventHandler 启动服务守护定时器: 120s
2025-08-20 15:19:57.912  2865-2865  Compatibil...geReporter com.dspread.mdm.service              D  Compat change id reported: 160794467; UID 1000; state: ENABLED
2025-08-20 15:19:57.920  2865-2865  Common                  com.dspread.mdm.service              I  ✅ ⏰ 设置服务守护定时器成功，下次执行: 120秒后 (2分钟)
2025-08-20 15:19:57.927  2865-2865  Common                  com.dspread.mdm.service              D  🔧 SmartMdmServiceApp: 服务守护定时器启动成功
2025-08-20 15:19:57.939  2865-2865  Common                  com.dspread.mdm.service              D  🔧 ServiceStartupManager 收到服务启动请求: APPLICATION_STARTUP, 强制启动: false
2025-08-20 15:19:57.952  2865-2865  Common                  com.dspread.mdm.service              D  🔧 ServiceStartupManager 开始执行服务启动: APPLICATION_STARTUP
2025-08-20 15:19:57.960  2865-2865  Common                  com.dspread.mdm.service              D  🔧 ServiceManager: 准备启动后台服务
2025-08-20 15:19:57.967  2865-2865  ContextImpl             com.dspread.mdm.service              W  Calling a method in the system process without a qualified user: android.app.ContextImpl.startService:1899 android.content.ContextWrapper.startService:825 com.dspread.mdm.service.platform.manager.ServiceManager.startBackgroundService:20 com.dspread.mdm.service.platform.manager.ServiceStartupManager.performServiceStart:121 com.dspread.mdm.service.platform.manager.ServiceStartupManager.startService:66 
2025-08-20 15:19:57.972  2865-2865  Common                  com.dspread.mdm.service              D  🔧 ServiceManager: 后台服务启动成功，ComponentName: ComponentInfo{com.dspread.mdm.service/com.dspread.mdm.service.services.SmartMdmBackgroundService}
2025-08-20 15:19:58.987  2865-2865  Common                  com.dspread.mdm.service              D  🔧 ServiceStartupManager 服务启动验证成功: APPLICATION_STARTUP
2025-08-20 15:19:58.995  2865-2865  Common                  com.dspread.mdm.service              D  🔧 ServiceStartupManager 服务启动成功: APPLICATION_STARTUP
2025-08-20 15:19:59.003  2865-2865  Common                  com.dspread.mdm.service              D  🔧 SmartMdmServiceApp: 主服务启动请求已提交
2025-08-20 15:19:59.010  2865-2865  Common                  com.dspread.mdm.service              D  🔧 SmartMdmServiceApp: Application启动完成，包名: com.dspread.mdm.service
2025-08-20 15:19:59.034  2865-2865  M-ProMotion             com.dspread.mdm.service              I  M-ProMotion is disabled
2025-08-20 15:19:59.127  2865-2865  Choreographer           com.dspread.mdm.service              I  Skipped 88 frames!  The application may be doing too much work on its main thread.
2025-08-20 15:19:59.149  2865-2865  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 服务启动
2025-08-20 15:19:59.202  2865-2865  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 开始服务初始化
2025-08-20 15:19:59.209  2865-2865  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] Android 14+: 初始化DSPREAD服务
2025-08-20 15:19:59.217  2865-2965  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 后台线程：初始化DSPREAD服务
2025-08-20 15:19:59.217  2865-2865  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 服务创建完成
2025-08-20 15:19:59.227  2865-2865  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] onStartCommand调用
2025-08-20 15:19:59.229  2865-2965  Platform                com.dspread.mdm.service              D  🔧 DspreadService 开始初始化DSPREAD服务
2025-08-20 15:19:59.236  2865-2965  ContextImpl             com.dspread.mdm.service              W  Calling a method in the system process without a qualified user: android.app.ContextImpl.bindService:1988 android.content.ContextWrapper.bindService:861 com.dspread.mdm.service.services.DspreadService.bindSysService:111 com.dspread.mdm.service.services.DspreadService.initialize:63 com.dspread.mdm.service.services.SmartMdmBackgroundService.initializeDspreadServiceAsync$lambda$1:227 
2025-08-20 15:19:59.239  2865-2865  Platform                com.dspread.mdm.service              D  🔧 WakeLockManager 获取智能WakeLock成功，持续时间: 60000ms
2025-08-20 15:19:59.242  2865-2965  Platform                com.dspread.mdm.service              D  🔧 DspreadService 系统服务绑定请求已发送
2025-08-20 15:19:59.249  2865-2865  Platform                com.dspread.mdm.service              D  🔧 WakeLockManager 设置WakeLock续期定时器: 60000ms
2025-08-20 15:19:59.256  2865-2865  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 初始化基础组件
2025-08-20 15:19:59.264  2865-2865  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 开始初始化...
2025-08-20 15:19:59.277  2865-2865  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 包更新广播接收器注册完成
2025-08-20 15:19:59.283  2865-2865  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 统一广播接收器注册完成
2025-08-20 15:19:59.289  2865-2865  Common                  com.dspread.mdm.service              I  ✅ BroadcastManager 初始化完成
2025-08-20 15:19:59.332  2865-2865  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 服务初始化完成
2025-08-20 15:19:59.341  2865-2865  Platform                com.dspread.mdm.service              D  🔧 DspreadService 系统服务连接成功: ComponentInfo{com.dspread.sdkservice/com.dspread.sdkservice.SDKSysService}
2025-08-20 15:19:59.350  2865-2865  Platform                com.dspread.mdm.service              D  🔧 DspreadService 系统服务绑定完成
2025-08-20 15:19:59.362  2865-2965  ContextImpl             com.dspread.mdm.service              W  Calling a method in the system process without a qualified user: android.app.ContextImpl.bindService:1988 android.content.ContextWrapper.bindService:861 com.dspread.mdm.service.services.DspreadService.bindDevService:166 com.dspread.mdm.service.services.DspreadService.initialize:66 com.dspread.mdm.service.services.SmartMdmBackgroundService.initializeDspreadServiceAsync$lambda$1:227 
2025-08-20 15:19:59.365  2865-2865  Receiver                com.dspread.mdm.service              D  🔧 电池状态变化: 电量=73%, 温度=25°C, 充电=true
2025-08-20 15:19:59.369  2865-2965  Platform                com.dspread.mdm.service              D  🔧 DspreadService 设备服务绑定请求已发送
2025-08-20 15:19:59.506  2865-2865  Provisioning            com.dspread.mdm.service              D  🔧 主目录 已存在: /data/pos/config
2025-08-20 15:19:59.512  2865-2865  Provisioning            com.dspread.mdm.service              I  ℹ️ 使用主配置目录: /data/pos/config/
2025-08-20 15:19:59.519  2865-2865  Provisioning            com.dspread.mdm.service              I  ℹ️ 配置路径初始化完成: 系统目录
2025-08-20 15:19:59.536  2865-2865  Provisioning            com.dspread.mdm.service              D  🔧 状态标志加载成功: /data/pos/config/provisioning_flags.json
2025-08-20 15:19:59.560  2865-2865  WebSocket               com.dspread.mdm.service              I  🔧 C0902 被动式上送: battery_level_change_5 (被动: 1)
2025-08-20 15:19:59.581  2865-2865  Platform                com.dspread.mdm.service              D  🔧 DeviceInfoApi 单例实例已创建
2025-08-20 15:19:59.588  2865-2865  Platform                com.dspread.mdm.service              E  ❌ DspreadService 设备服务不可用，无法获取序列号
2025-08-20 15:19:59.616  2865-2865  WebSocket               com.dspread.mdm.service              I  🔧 消息已缓存: C0902 (缓存数量: 1)
2025-08-20 15:19:59.622  2865-2865  WebSocket               com.dspread.mdm.service              I  🔧 C0902 电池状态上送成功 (1, trigger=battery_level_change_5)
2025-08-20 15:19:59.633  2865-2865  Platform                com.dspread.mdm.service              D  🔧 NetworkApi 单例实例已创建
2025-08-20 15:19:59.647  2865-2865  Receiver                com.dspread.mdm.service              D  🔧 网络状态变化: 连接=true, 类型=WIFI
2025-08-20 15:19:59.656  2865-2865  Receiver                com.dspread.mdm.service              D  🔧 网络恢复，尝试重新连接WebSocket
2025-08-20 15:19:59.663  2865-2865  Receiver                com.dspread.mdm.service              D  🔧 ServiceManagementEventHandler 处理网络变化事件
2025-08-20 15:19:59.674  2865-2865  Common                  com.dspread.mdm.service              D  🔧 ServiceStartupManager 服务正在运行，无需启动: NETWORK_CHANGE
2025-08-20 15:19:59.681  2865-2865  Receiver                com.dspread.mdm.service              D  🔧 ServiceManagementEventHandler 网络变化后服务检查完成
2025-08-20 15:19:59.687  2865-2865  Provisioning            com.dspread.mdm.service              D  🔧 ProvisioningEventHandler 网络状态变化
2025-08-20 15:19:59.694  2865-2865  Provisioning            com.dspread.mdm.service              D  🔧 ProvisioningEventHandler 忽略初始启动时的网络连接事件
2025-08-20 15:19:59.701  2865-2865  Platform                com.dspread.mdm.service              D  🔧 DspreadService 设备服务连接成功: ComponentInfo{com.dspread.sdkservice/com.dspread.sdkservice.SDKDevService}
2025-08-20 15:19:59.709  2865-2865  Platform                com.dspread.mdm.service              D  🔧 DspreadService 设备服务绑定完成
2025-08-20 15:20:00.717  2865-2965  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 等待DSPREAD服务就绪 (1/10): true
2025-08-20 15:20:00.730  2865-2965  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] DSPREAD服务就绪，启动Provisioning
2025-08-20 15:20:00.743  2865-2965  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 验证DSPREAD服务功能
2025-08-20 15:20:00.770  2865-2965  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 设备序列号验证: 成功(01354090202503050399)
2025-08-20 15:20:00.782  2865-2965  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 服务状态 - 系统服务:✅ 设备服务:✅
2025-08-20 15:20:00.791  2865-2971  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 启动ProvisioningService
2025-08-20 15:20:00.811  2865-2971  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] ProvisioningService启动完成
2025-08-20 15:20:00.812  2865-2959  Provisioning            com.dspread.mdm.service              I  ℹ️ 开始执行Provisioning配置，触发类型: FIRST_BOOT
2025-08-20 15:20:00.823  2865-2959  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] Provisioning进度: 正在下载配置文件...
2025-08-20 15:20:00.835  2865-2959  Provisioning            com.dspread.mdm.service              I  ℹ️ 开始请求远程配置...
2025-08-20 15:20:00.860  2865-2959  Provisioning            com.dspread.mdm.service              I  ℹ️ 请求配置: https://config.dspreadserv.net/status/config?SN=MDEzNTQwOTAyMDI1MDMwNTAzOTk=&timestamp=1755674400858
2025-08-20 15:20:00.993  2865-2959  TrafficStats            com.dspread.mdm.service              D  tagSocket(86) with statsTag=0xffffffff, statsUid=-1
2025-08-20 15:20:02.875  2865-2959  Provisioning            com.dspread.mdm.service              I  ℹ️ 远程配置获取成功
2025-08-20 15:20:02.883  2865-2959  Provisioning            com.dspread.mdm.service              I  ℹ️ 远程配置请求成功
2025-08-20 15:20:02.885  2865-2865  Receiver                com.dspread.mdm.service              D  🔧 屏幕关闭
2025-08-20 15:20:02.893  2865-2959  Provisioning            com.dspread.mdm.service              I  ℹ️ 远程配置解析成功 - CID: 1001
2025-08-20 15:20:02.907  2865-2865  WebSocket               com.dspread.mdm.service              W  ⚠️ 未知触发条件: screen_off，默认按被动式处理
2025-08-20 15:20:02.925  2865-2865  WebSocket               com.dspread.mdm.service              I  🔧 C0201 被动式上送拒绝: screen_off (拒绝: 1)
2025-08-20 15:20:02.928  2865-2959  Provisioning            com.dspread.mdm.service              I  ℹ️ API响应已保存到本地: /data/pos/config/provisioning_config.json, 具体内容为:
                                                                                                    {"mode":"dev","data":{"requestTime":"1755674402623","customization":{"system":{"timezone":"Asia\/Hong_Kong","logo":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/test\/logo.bin","logoMd5":"ba1ee533924eae5c408465e7cddcbda4","bootAnimationMd5":"ba1ee533924eae5c408465e7cddcbda4","gps":{"minUpdateTime":"30","scheduleTime":"60","maxLocateTime":"0","minDistance":"10","valid_distance":"500","care":"1"},"powerSaveMode":{"enable":"1","screenTimeout":"0"},"bootAnimation":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/test\/bootanimation.zip"},"polling":{"heartbeatTime":"300","terminalInfoTime":"900","statusApiUrl":"wss:\/\/api.dspreadserv.net\/status\/websocket\/register","remoteUrl":"wss:\/\/remote.dspreadserv.net\/remoteWSS\/websockify","uploadMode":"1","wssreconn":{"pingInterval":"30","delayPolicy":"1","delaySwitch":"1","delayTime":"60"}}},"client":"default","cid":"1001"},"function":"getProvisioningConfig","description":"SUCCESS","stateCode":"0","version":"v1.0.1.20250801"}
2025-08-20 15:20:02.935  2865-2865  WebSocket               com.dspread.mdm.service              I  🔧 C0201 设备状态上送被流量控制阻止: 被动事件 'screen_off' 在平衡模式 - 重要变化下未启用
2025-08-20 15:20:02.935  2865-2959  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] Provisioning进度: 正在下载 Logo...
2025-08-20 15:20:02.941  2865-2865  Receiver                com.dspread.mdm.service              D  🔧 屏幕关闭处理完成
2025-08-20 15:20:03.053  2865-2959  Provisioning            com.dspread.mdm.service              I  ℹ️ 文件已存在且MD5匹配，跳过下载: Logo
2025-08-20 15:20:03.059  2865-2959  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] Provisioning进度: 正在下载 BootAnimation...
2025-08-20 15:20:03.170  2865-2959  Provisioning            com.dspread.mdm.service              I  ℹ️ 文件已存在且MD5匹配，跳过下载: BootAnimation
2025-08-20 15:20:03.177  2865-2959  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] Provisioning进度: 正在应用配置...
2025-08-20 15:20:03.183  2865-2959  Provisioning            com.dspread.mdm.service              I  ℹ️ 配置应用完成
2025-08-20 15:20:03.190  2865-2959  Provisioning            com.dspread.mdm.service              I  ℹ️ 远程配置成功，标记首次配置完成
2025-08-20 15:20:03.200  2865-2959  Provisioning            com.dspread.mdm.service              D  🔧 状态标志保存成功: /data/pos/config/provisioning_flags.json
2025-08-20 15:20:03.207  2865-2959  Provisioning            com.dspread.mdm.service              I  ℹ️ Provisioning配置执行完成
2025-08-20 15:20:03.213  2865-2959  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] Provisioning完成，启动核心组件
2025-08-20 15:20:03.238  2865-2865  WebSocket               com.dspread.mdm.service              I  🔧 开始初始化 WebSocket 组件...
2025-08-20 15:20:03.246  2865-2865  WebSocket               com.dspread.mdm.service              I  🔧 初始化 WebSocket 密钥管理器
2025-08-20 15:20:03.252  2865-2865  WebSocket               com.dspread.mdm.service              I  🔧 生成客户端公私钥对
2025-08-20 15:20:03.345  2865-2865  Common                  com.dspread.mdm.service              I  ✅ WebSocket 密钥管理器初始化完成
2025-08-20 15:20:03.356  2865-2865  WebSocket               com.dspread.mdm.service              I  🔧 开始初始化 WsMessageCenter...
2025-08-20 15:20:03.362  2865-2865  WebSocket               com.dspread.mdm.service              I  🔧 WsMessageCenter 首次初始化
2025-08-20 15:20:03.384  2865-2865  WebSocket               com.dspread.mdm.service              I  🔧 WsTaskManager 初始化完成
2025-08-20 15:20:03.502  2865-2865  WebSocket               com.dspread.mdm.service              I  🔧 WsMessageCenter 初始化完成
2025-08-20 15:20:03.508  2865-2865  WebSocket               com.dspread.mdm.service              I  🔧 开始清理终态任务，当前任务数量: 0
2025-08-20 15:20:03.514  2865-2865  WebSocket               com.dspread.mdm.service              I  🔧 没有需要清理的终态任务
2025-08-20 15:20:03.520  2865-2865  WebSocket               com.dspread.mdm.service              I  🔧 开始重置任务状态，当前任务个数: 0
2025-08-20 15:20:03.526  2865-2865  WebSocket               com.dspread.mdm.service              I  🔧 没有需要重置的任务
2025-08-20 15:20:03.532  2865-2865  Common                  com.dspread.mdm.service              I  ✅ 任务状态恢复完成
2025-08-20 15:20:03.549  2865-2865  WebSocket               com.dspread.mdm.service              I  🔧 使用Provisioning配置的WebSocket URL: wss://api.dspreadserv.net/status/websocket/register
2025-08-20 15:20:03.556  2865-2865  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接管理器初始化完成
2025-08-20 15:20:03.562  2865-2865  Common                  com.dspread.mdm.service              I  ✅ WebSocket 管理器初始化完成
2025-08-20 15:20:03.568  2865-2865  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 管理器初始化完成
2025-08-20 15:20:03.575  2865-2865  Common                  com.dspread.mdm.service              I  ✅ WebSocket 组件初始化完成
2025-08-20 15:20:03.581  2865-2865  WebSocket               com.dspread.mdm.service              I  🔧 启动 WebSocket 连接...
2025-08-20 15:20:03.587  2865-2865  WebSocket               com.dspread.mdm.service              I  🔧 开始连接 WebSocket...
2025-08-20 15:20:03.614  2865-2865  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接到: wss://api.dspreadserv.net/status/websocket/register?serialNo=MDEzNTQwOTAyMDI1MDMwNTAzOTk=&publicKey=TUlHZk1BMEdDU3FHU0liM0RRRUJBUVVBQTRHTkFEQ0JpUUtCZ1FDMHNQRkFFbEQzcGhLLzc0bHZHcE5Mbi90dThKaDkvZ3hlcEdITlErSGZ3TFRwenl0SkNFclZNdHNYWUU5RElHeFJqdVR2ejZLNHQzSmxPV1g3S2UzZEJ4ei90empubnhuY1lGOG53S2ZZaWR0TFhLNXhPM1BVbUJ1TFZWRHplMXBmQ1FsUnNvSktHTzdJWjN5amw0QWFWMW1lQnQ0TEV3OGcvcXRmWjBvYU5RSURBUUFC&query=1&msgVer=3&timestamp=1755674403598&signature=FZH1nmHopaH29yeOipXG5mLddkcOMEMhyhuh+on3x42out7SgAIRwqWsZiuWz+76iOF+zGYYvFELzlZJFRn4+7OhPAu1o76ZvgEak19rRIujOgvIWJRkk87rTk0GVTjaBtVO1U6V93HIkctNykSHtw+x5JMP0mD6fA1YKcP7Gpk=
2025-08-20 15:20:03.624  2865-2865  WebSocket               com.dspread.mdm.service              I  🔧 未检测到系统代理设置
2025-08-20 15:20:03.668  2865-2865  WebSocket               com.dspread.mdm.service              I  🔧 ===== WebSocket 库默认配置 =====
2025-08-20 15:20:03.674  2865-2865  WebSocket               com.dspread.mdm.service              I  🔧 默认 PING 间隔: 30000ms (30秒)
2025-08-20 15:20:03.680  2865-2865  WebSocket               com.dspread.mdm.service              I  🔧 默认 PONG 间隔: 0ms (0秒)
2025-08-20 15:20:03.685  2865-2865  WebSocket               com.dspread.mdm.service              I  🔧 帧队列大小: 5
2025-08-20 15:20:03.691  2865-2865  WebSocket               com.dspread.mdm.service              I  🔧 连接超时: 15000ms (15秒)
2025-08-20 15:20:03.696  2865-2865  WebSocket               com.dspread.mdm.service              I  🔧 允许缺失关闭帧: false
2025-08-20 15:20:03.703  2865-2865  WebSocket               com.dspread.mdm.service              I  🔧 ================================
2025-08-20 15:20:03.711  2865-2865  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接请求已发送
2025-08-20 15:20:03.718  2865-2865  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接启动完成
2025-08-20 15:20:03.725  2865-2865  Common                  com.dspread.mdm.service              D  🔧 [ModuleManagerRegistry] 模块管理器注册中心初始化完成
2025-08-20 15:20:03.733  2865-2865  Provisioning            com.dspread.mdm.service              D  🔧 ProvisioningEventHandler 启动Provisioning定时器: 300s (0小时)
2025-08-20 15:20:03.733  2865-2959  Common                  com.dspread.mdm.service              D  🔧 [ModuleManagerRegistry] 启动所有模块
2025-08-20 15:20:03.744  2865-2865  Common                  com.dspread.mdm.service              I  ✅ ⏰ 设置初始化定时器成功，下次执行: 300秒后 (0小时)
2025-08-20 15:20:03.749  2865-2959  Common                  com.dspread.mdm.service              D  🔧 Module LogStream Manager status changed to: INITIALIZED
2025-08-20 15:20:03.755  2865-2865  Common                  com.dspread.mdm.service              I  ✅ ⏰ 设置心跳定时器成功，下次执行: 60秒后 (1分钟)
2025-08-20 15:20:03.760  2865-2959  Common                  com.dspread.mdm.service              D  🔧 Module LogStream Manager status changed to: STARTING
2025-08-20 15:20:03.767  2865-2865  Common                  com.dspread.mdm.service              I  ✅ ⏰ 设置任务执行定时器成功，下次执行: 60秒后 (1分钟)
2025-08-20 15:20:03.779  2865-2865  Common                  com.dspread.mdm.service              I  ✅ ⏰ 设置终端信息定时器成功，下次执行: 120秒后 (2分钟)
2025-08-20 15:20:03.786  2865-2865  Common                  com.dspread.mdm.service              D  🔧 TimerConfig ========== 当前定时器配置 (调试模式) ==========
2025-08-20 15:20:03.792  2865-2865  Common                  com.dspread.mdm.service              D  🔧 TimerConfig ①心跳定时器: 60秒
2025-08-20 15:20:03.798  2865-2865  Common                  com.dspread.mdm.service              D  🔧 TimerConfig ②终端信息上传: 120秒
2025-08-20 15:20:03.803  2865-2865  Common                  com.dspread.mdm.service              D  🔧 TimerConfig ③任务执行: 60秒
2025-08-20 15:20:03.808  2865-3004  TrafficStats            com.dspread.mdm.service              D  tagSocket(91) with statsTag=0xffffffff, statsUid=-1
2025-08-20 15:20:03.810  2865-2865  Common                  com.dspread.mdm.service              D  🔧 TimerConfig ④服务守护: 120秒
2025-08-20 15:20:03.816  2865-2865  Common                  com.dspread.mdm.service              D  🔧 TimerConfig ⑤Provisioning: 300秒
2025-08-20 15:20:03.823  2865-2865  Common                  com.dspread.mdm.service              D  🔧 TimerConfig ===============================================
2025-08-20 15:20:03.830  2865-2865  Common                  com.dspread.mdm.service              I  ✅ [SmartMdmBackgroundService] 所有定时器启动完成
2025-08-20 15:20:03.836  2865-2865  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 核心组件初始化完成
2025-08-20 15:20:03.897  2865-2959  Common                  com.dspread.mdm.service              D  🔧 Module LogStream Manager status changed to: RUNNING
2025-08-20 15:20:03.904  2865-2959  Common                  com.dspread.mdm.service              D  🔧 [ModuleManagerRegistry] LogStreamManager启动成功
2025-08-20 15:20:03.932  2865-2959  Compatibil...geReporter com.dspread.mdm.service              D  Compat change id reported: 263076149; UID 1000; state: ENABLED
2025-08-20 15:20:03.946  2865-2959  Common                  com.dspread.mdm.service              D  🔧 Module Geofence Manager status changed to: INITIALIZED
2025-08-20 15:20:03.954  2865-2959  Common                  com.dspread.mdm.service              D  🔧 Module Geofence Manager status changed to: STARTING
2025-08-20 15:20:03.974  2865-2959  Platform                com.dspread.mdm.service              D  🔧 用户交互监控已启动（简化版本）
2025-08-20 15:20:03.980  2865-2959  Platform                com.dspread.mdm.service              D  🔧 使用模拟用户交互检测
2025-08-20 15:20:03.981  2865-2865  Platform                com.dspread.mdm.service              D  🔧 [WiFiManagerApi] WiFiManagerApi 单例实例已创建
2025-08-20 15:20:03.987  2865-2959  Platform                com.dspread.mdm.service              D  🔧 用户交互监控已启动
2025-08-20 15:20:03.993  2865-2959  Service                 com.dspread.mdm.service              D  🔧 [GeofenceManager]Constants.geofenceStatus = 1
2025-08-20 15:20:04.000  2865-2959  Service                 com.dspread.mdm.service              D  🔧 [GeofenceManager]-- Constants.storeId =  -- Constants.storeSsid =  -- Constants.storeIp = 
2025-08-20 15:20:04.008  2865-2959  Common                  com.dspread.mdm.service              D  🔧 Module Geofence Manager status changed to: RUNNING
2025-08-20 15:20:04.015  2865-2959  Common                  com.dspread.mdm.service              D  🔧 [ModuleManagerRegistry] GeofenceManager启动成功
2025-08-20 15:20:04.034  2865-2959  RuleBase                com.dspread.mdm.service              D  🔧 RuleBaseManager 开始初始化RuleBaseManager...
2025-08-20 15:20:04.046  2865-2959  RuleBase                com.dspread.mdm.service              D  🔧 RuleBaseStorage RuleBase存储管理器初始化完成
2025-08-20 15:20:04.053  2865-2959  RuleBase                com.dspread.mdm.service              D  🔧 RuleStateMonitor 状态监控器初始化完成
2025-08-20 15:20:04.059  2865-3005  TrafficStats            com.dspread.mdm.service              D  tagSocket(100) with statsTag=0xffffffff, statsUid=-1
2025-08-20 15:20:04.060  2865-2959  RuleBase                com.dspread.mdm.service              D  🔧 RuleBaseManager RuleBaseManager初始化完成
2025-08-20 15:20:04.066  2865-2959  Common                  com.dspread.mdm.service              D  🔧 [ModuleManagerRegistry] RuleBaseManager启动成功
2025-08-20 15:20:04.072  2865-2959  Common                  com.dspread.mdm.service              D  🔧 [ModuleManagerRegistry] 所有模块启动完成
2025-08-20 15:20:04.878  2865-3028  ProfileInstaller        com.dspread.mdm.service              D  Installing profile for com.dspread.mdm.service
2025-08-20 15:20:06.576  2865-3029  Common                  com.dspread.mdm.service              I  ✅ WebSocket 物理连接成功，等待服务器 hellMsg
2025-08-20 15:20:06.585  2865-3029  WebSocket               com.dspread.mdm.service              I  🔧 ===== 心跳发送器实际配置 =====
2025-08-20 15:20:06.594  2865-3029  WebSocket               com.dspread.mdm.service              I  🔧 实际 PING 发送间隔: 30000ms (30秒)
2025-08-20 15:20:06.602  2865-3029  WebSocket               com.dspread.mdm.service              I  🔧 PING 定时器名称: PingSender
2025-08-20 15:20:06.611  2865-3029  WebSocket               com.dspread.mdm.service              I  🔧 实际 PONG 发送间隔: 0ms (0秒)
2025-08-20 15:20:06.617  2865-3029  WebSocket               com.dspread.mdm.service              I  🔧 PONG 定时器名称: PongSender
2025-08-20 15:20:06.623  2865-3029  WebSocket               com.dspread.mdm.service              I  🔧 ===============================
2025-08-20 15:20:06.630  2865-3029  WebSocket               com.dspread.mdm.service              I  🔧 启动PING/PONG检测，阈值: 连续3个PING无PONG响应
2025-08-20 15:20:06.672  2865-3029  Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"hellMsg":"hello 01354090202503050399","tranCode":"S0000","version":"1","rebootTime":"02:57:26","serialNo":"01354090202503050399","deviceStatus":"6"}
2025-08-20 15:20:06.680  2865-3029  Common                  com.dspread.mdm.service              I  ✅ 收到服务器 hellMsg，WebSocket 业务连接成功！
2025-08-20 15:20:06.686  2865-3029  Common                  com.dspread.mdm.service              I  ✅ WebSocket 业务连接成功！
2025-08-20 15:20:06.692  2865-3029  WebSocket               com.dspread.mdm.service              I  🔧 业务连接成功，重连计数已重置为 0
2025-08-20 15:20:06.699  2865-3029  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接成功，检查自身更新任务状态
2025-08-20 15:20:06.708  2865-3029  Receiver                com.dspread.mdm.service              D  🔧 发送广播: com.dspread.mdm.service.CHECK_SELF_UPDATE_STATUS
2025-08-20 15:20:06.714  2865-3029  WebSocket               com.dspread.mdm.service              I  🔧 开始发送缓存消息，数量: 1
2025-08-20 15:20:06.724  2865-3029  WebSocket               com.dspread.mdm.service              I  🔧 C0902 主动式上送: cached_message_resend (主动: 1)
2025-08-20 15:20:06.761  2865-3029  Common                  com.dspread.mdm.service              I  ✅ 缓存消息发送成功: C0902
2025-08-20 15:20:07.274  2865-3029  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接成功
2025-08-20 15:20:07.280  2865-3029  WebSocket               com.dspread.mdm.service              I  🔧 设备状态: 6
2025-08-20 15:20:07.290  2865-3029  WebSocket               com.dspread.mdm.service              I  🔧 检查首次连接标志: isFirstTimeWebSocketConnected=true
2025-08-20 15:20:07.300  2865-3029  WebSocket               com.dspread.mdm.service              I  🔧 首次WebSocket连接成功，发送初始化信息并执行初始化检查
2025-08-20 15:20:07.309  2865-3029  WebSocket               com.dspread.mdm.service              I  🔧 开始发送初始化信息（带流量控制）
2025-08-20 15:20:07.318  2865-3029  WebSocket               com.dspread.mdm.service              I  🔧 C0901 被动式上送: first_connection (被动: 2)
2025-08-20 15:20:07.327  2865-2885  System                  com.dspread.mdm.service              W  A resource failed to call close. 
2025-08-20 15:20:07.339  2865-3029  Platform                com.dspread.mdm.service              D  🔧 重新收集应用信息
2025-08-20 15:20:07.451  2865-3029  AppManager              com.dspread.mdm.service              I  ℹ️ 应用信息获取完成: 总数131(系统129/用户2) 返回2个
2025-08-20 15:20:07.472  2865-3029  Platform                com.dspread.mdm.service              D  🔧 应用信息: 2 个应用
2025-08-20 15:20:07.509  2865-3029  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0901","serialNo":"01354090202503050399","request_time":"1755674407479","request_id":"1755674407479C0901","version":"1","data":{"apkInfo":[{"packName":"com.dspread.mdm.service","apkName":"service","versionCode":22,"versionName":"1.1.03.20250820.DSPREAD.MDM.SERVICE","updateDate":"2025-08-20 15:17:57"},{"packName":"mark.via","apkName":"Via","versionCode":20250117,"versionName":"6.2.0","updateDate":"2025-08-18 17:50:20"}],"sytemInfo":{"androidVersion":"14","buildNumber":"BASE_D30M-MU_GEN_AP_V1.0.8.1_202507032000_ota_test","aspl":"2025-03-05"},"serviceInfo":[]},"myVersionName":"1.1.03.20250820.DSPREAD.MDM.SERVICE","terminalDate":"20250820152007"}
2025-08-20 15:20:07.517  2865-3029  WebSocket               com.dspread.mdm.service              I  🔧 C0901 应用信息上送成功 (1)
2025-08-20 15:20:08.525  2865-3029  WebSocket               com.dspread.mdm.service              I  🔧 C0902 被动式上送: first_connection (被动: 3)
2025-08-20 15:20:08.572  2865-3029  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0902","serialNo":"01354090202503050399","request_time":"1755674408547","request_id":"1755674408547C0902","version":"1","data":{"batteryLife":73,"batteryHealth":2,"temprature":"25.6","isCharging":"1","isLowBattery":"0","powerOff":"0","outOfBattery":"0"},"myVersionName":"1.1.03.20250820.DSPREAD.MDM.SERVICE","terminalDate":"20250820152008"}
2025-08-20 15:20:08.580  2865-3029  WebSocket               com.dspread.mdm.service              I  🔧 C0902 电池状态上送成功 (1, trigger=first_connection)
2025-08-20 15:20:09.588  2865-3029  WebSocket               com.dspread.mdm.service              I  🔧 C0903 被动式上送: first_connection (被动: 4)
2025-08-20 15:20:09.784  2865-3029  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0903","serialNo":"01354090202503050399","request_time":"1755674409741","request_id":"1755674409741C0903","version":"1","data":{"hardwareInfo":[{"name":"CPU Usage","desc":"cpu:0.50"},{"name":"storage_usage_status","desc":" 4.56GB \/ 32.00GB ","state":"1"},{"name":"total_memory","desc":" 0.72GB \/ 1.86GB ","state":"1"},{"name":"androidVersion","desc":"14","state":"1"},{"name":"network_status","desc":"wifi","state":"1"},{"name":"OSVer","desc":"14","state":"1"}],"locationInfo":{"gps":{"longitude":"-999.0","latitude":"-999.0","satelliteCount":0,"satelliteCountInUsed":0,"satelliteAverSNR":"0.00","distance":"0.0","lockStatus":"1","lockMeter":"200","proId":"","proName":""},"baseSite":{"MCC":"","MNC":"","CID":"-1","LAC":"-1","DBM":"-999","IMSI":"","ICCID":""},"wifi":{"SSID":"fubox_2.4G","IP":"**************","MAC":"BE:87:C2:42:DF:75","SSTH":"-33","BSSID":"7c:10:c9:d6:9f:10"},"address":"无法获取"}},"myVersionName":"1.1.03.20250820.DSPREAD.MDM.SERVICE","terminalDate":"20250820152009"}
2025-08-20 15:20:09.791  2865-3029  WebSocket               com.dspread.mdm.service              I  🔧 C0903 数据信息上送成功 (1)
2025-08-20 15:20:10.799  2865-3029  WebSocket               com.dspread.mdm.service              I  🔧 C0904 被动式上送: first_connection (被动: 5)
2025-08-20 15:20:10.928  2865-3029  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0904","serialNo":"01354090202503050399","request_time":"1755674410879","request_id":"1755674410879C0904","version":"1","data":{"wifiOption":[{"SSID":"fubox_5G","SSTH":"-38"},{"SSID":"2205","SSTH":"-50"},{"SSID":"2205_5G","SSTH":"-59"},{"SSID":"fubox_2.4G","SSTH":"-33"},{"SSID":"2106","SSTH":"-64"},{"SSID":"DIRECT-CC-HP Laser 1188w","SSTH":"-65"},{"SSID":"ZTE_BDA477","SSTH":"-87"},{"SSID":"2206","SSTH":"-25"},{"SSID":"2206-5G","SSTH":"-29"},{"SSID":"DIRECT-C6-Mi All-in-One Inkjet","SSTH":"-48"},{"SSID":"2207-5G","SSTH":"-80"},{"SSID":"2207","SSTH":"-57"},{"SSID":"ChinaNet-65AA9E","SSTH":"-78"},{"SSID":"诺富特酒店2303","SSTH":"-79"},{"SSID":"2103_5G","SSTH":"-79"},{"SSID":"@Ruijie-1816_5G","SSTH":"-68"},{"SSID":"CMCC-2203","SSTH":"-72"},{"SSID":"2103","SSTH":"-83"},{"SSID":"@Ruijie-1816","SSTH":"-55"},{"SSID":"ZTE_96F1CF","SSTH":"-87"}],"baseSite":{"MCC":"","MNC":"","CID":"-1","LAC":"-1","DBM":"-999","IMSI":"","ICCID":""},"wifi":{"SSID":"fubox_2.4G","IP":"**************","MAC":"BE:87:C2:42:DF:75","SSTH":"-33","BSSID":"7c:10:c9:d6:9f:10"}},"myVersionName":"1.1.03.20250820.DSPREAD.MDM.SERVICE","terminalDate":"20250820152010"}
2025-08-20 15:20:10.935  2865-3029  WebSocket               com.dspread.mdm.service              I  🔧 C0904 网络状态上送成功 (1)
2025-08-20 15:20:11.942  2865-3029  WebSocket               com.dspread.mdm.service              I  🔧 C0906 被动式上送: first_connection (被动: 6)
2025-08-20 15:20:11.976  2865-3029  Platform                com.dspread.mdm.service              D  🔧 SpVersionApi 解析SP版本: V1.0.5
2025-08-20 15:20:11.984  2865-3029  Platform                com.dspread.mdm.service              D  🔧 DspreadService 首次获取SP版本成功: V1.0.5 (原始: *SP_VERSION:V1.0.5#*COMMIT:2d7cc90#*DATETIME:20250...)
2025-08-20 15:20:11.991  2865-3029  Platform                com.dspread.mdm.service              D  🔧 SpVersionApi 通过DspreadService获取SP版本: V1.0.5
2025-08-20 15:20:12.030  2865-3029  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0906","serialNo":"01354090202503050399","request_time":"1755674412010","request_id":"1755674412010C0906","version":"1","data":{"firmWareInfo":{"spfw":"V1.0.5"},"imei_1":"864177060143618","imei_2":"","wifi_mac":"be:87:c2:42:df:75","bt_mac":"","bsn":""},"myVersionName":"1.1.03.20250820.DSPREAD.MDM.SERVICE","terminalDate":"20250820152012"}
2025-08-20 15:20:12.037  2865-3029  WebSocket               com.dspread.mdm.service              I  🔧 C0906 设备信息上送成功 (1)
2025-08-20 15:20:12.043  2865-3029  WebSocket               com.dspread.mdm.service              I  🔧 C0109 被动式上送: first_connection (被动: 7)
2025-08-20 15:20:12.060  2865-3029  Platform                com.dspread.mdm.service              D  🔧 重新收集应用信息（强制刷新）
2025-08-20 15:20:12.151  2865-3029  AppManager              com.dspread.mdm.service              I  ℹ️ 应用信息获取完成: 总数131(系统129/用户2) 返回2个
2025-08-20 15:20:12.167  2865-3029  Platform                com.dspread.mdm.service              D  🔧 应用信息: 2 个应用
2025-08-20 15:20:12.343  2865-3029  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0109","serialNo":"01354090202503050399","request_time":"1755674412270","request_id":"1755674412270C0109","version":"1","data":{"apkInfo":[{"packName":"com.dspread.mdm.service","apkName":"service","versionCode":22,"versionName":"1.1.03.20250820.DSPREAD.MDM.SERVICE","updateDate":"2025-08-20 15:17:57"},{"packName":"mark.via","apkName":"Via","versionCode":20250117,"versionName":"6.2.0","updateDate":"2025-08-18 17:50:20"}],"locationInfo":{"gps":{"longitude":"-999.0","latitude":"-999.0","satelliteCount":0,"satelliteCountInUsed":0,"satelliteAverSNR":"0.00","distance":"0.0","lockStatus":"1","lockMeter":"200","proId":"","proName":""},"baseSite":{"MCC":"","MNC":"","CID":"-1","LAC":"-1","DBM":"-999","IMSI":"","ICCID":""},"wifi":{"SSID":"fubox_2.4G","IP":"**************","MAC":"BE:87:C2:42:DF:75","SSTH":"-33","BSSID":"7c:10:c9:d6:9f:10"},"address":"无法获取"},"hardwareInfo":[{"name":"CPU Usage","desc":"cpu:0.50"},{"name":"storage_usage_status","desc":" 4.56GB \/ 32.00GB ","state":"1"},{"name":"total_memory","desc":" 0.72GB \/ 1.86GB ","state":"1"},{"name":"androidVersion","desc":"14","state":"1"},{"name":"network_status","desc":"wifi","state":"1"},{"name":"OSVer","desc":"14","state":"1"}],"wifiOption":[{"SSID":"fubox_5G","SSTH":"-38"},{"SSID":"2205","SSTH":"-50"},{"SSID":"2205_5G","SSTH":"-59"},{"SSID":"fubox_2.4G","SSTH":"-33"},{"SSID":"2106","SSTH":"-64"},{"SSID":"DIRECT-CC-HP Laser 1188w","SSTH":"-65"},{"SSID":"ZTE_BDA477","SSTH":"-87"},{"SSID":"2206","SSTH":"-25"},{"SSID":"2206-5G","SSTH":"-29"},{"SSID":"DIRECT-C6-Mi All-in-One Inkjet","SSTH":"-48"},{"SSID":"2207-5G","SSTH":"-80"},{"SSID":"2207","SSTH":"-57"},{"SSID":"ChinaNet-65AA9E","SSTH":"-78"},{"SSID":"诺富特酒店2303","SSTH":"-79"},{"SSID":"2103_5G","SSTH":"-79"},{"SSID":"@Ruijie-1816_5G","SSTH":"-68"},{"SSID":"CMCC-2203","SSTH":"-72"},{"SSID":"2103","SSTH":"-83"},{"SSID":"@Ruijie-1816","SSTH":"-55"},{"SSID":"ZTE_96F1CF","SSTH":"-87"}],"sytemInfo":{"androidVersion":"14","buildNumber":"BASE_D30M-MU_GEN_AP_V1.0.8.1_202507032000_ota_test","aspl":"2025-03-05"}},"myVersionName":"1.1.03.20250820.DSPREAD.MDM.SERVICE","terminalDate":"20250820152012"}
2025-08-20 15:20:12.351  2865-3029  WebSocket               com.dspread.mdm.service              I  🔧 C0109 终端信息上传完成 (1, trigger=first_connection)
2025-08-20 15:20:12.357  2865-3029  Common                  com.dspread.mdm.service              I  ✅ 初始化信息发送完成
2025-08-20 15:20:12.364  2865-3029  WebSocket               com.dspread.mdm.service              I  🔧 检查服务更新结果
2025-08-20 15:20:12.371  2865-3029  Task                    com.dspread.mdm.service              W  ⚠️ 任务列表为空，跳过服务更新结果检查
2025-08-20 15:20:12.377  2865-3029  WebSocket               com.dspread.mdm.service              I  🔧 检查OS升级状态...
2025-08-20 15:20:12.386  2865-3029  Common                  com.dspread.mdm.service              D  🔧 OsUpdateStatusChecker WebSocket连接成功，检查OS升级状态
2025-08-20 15:20:12.393  2865-3029  Common                  com.dspread.mdm.service              D  🔧 OsUpdateStatusChecker 没有待检查的OS升级任务
2025-08-20 15:20:12.399  2865-3029  WebSocket               com.dspread.mdm.service              I  🔧 OS升级状态检查已触发
2025-08-20 15:20:12.406  2865-3029  WebSocket               com.dspread.mdm.service              I  🔧 首次连接初始化检查完成
2025-08-20 15:20:12.448  2865-3029  Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"request_time":"1755674412204","org_request_time":"1755674412010","org_request_id":"1755674412010C0906","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755674412204S0000","serialNo":"01354090202503050399"}
2025-08-20 15:20:12.458  2865-3029  WebSocket               com.dspread.mdm.service              I  🔧 收到响应消息: transCode=S0000, orgRequestId=1755674412010C0906, state=0, remark=
2025-08-20 15:20:12.464  2865-3029  WebSocket               com.dspread.mdm.service              I  🔧 处理设备信息响应: state=0
2025-08-20 15:20:12.471  2865-3029  WebSocket               com.dspread.mdm.service              I  🔧 设备信息上传成功
2025-08-20 15:20:12.841  2865-3029  Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"request_time":"1755674412574","org_request_time":"1755674412270","org_request_id":"1755674412270C0109","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755674412574S0000","serialNo":"01354090202503050399"}
2025-08-20 15:20:12.851  2865-3029  WebSocket               com.dspread.mdm.service              I  🔧 收到响应消息: transCode=S0000, orgRequestId=1755674412270C0109, state=0, remark=
2025-08-20 15:20:12.858  2865-3029  WebSocket               com.dspread.mdm.service              I  🔧 终端信息上传成功
2025-08-20 15:20:14.673  2865-2865  Receiver                com.dspread.mdm.service              D  🔧 网络恢复，重新上传网络状态信息（防抖通过）
2025-08-20 15:20:14.701  2865-2865  WebSocket               com.dspread.mdm.service              I  🔧 C0904 被动式上送拒绝: NETWORK_RECOVERY (拒绝: 2)
2025-08-20 15:20:14.712  2865-2865  WebSocket               com.dspread.mdm.service              I  🔧 C0904 网络状态上送被流量控制阻止: 被动事件 'NETWORK_RECOVERY' 在平衡模式 - 重要变化下未启用
2025-08-20 15:20:36.580  2865-3030  WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 1 (第1个，待响应: 1)
2025-08-20 15:20:36.638  2865-2865  WebSocket               com.dspread.mdm.service              W  ⚠️ 当前有1个PING等待PONG响应
2025-08-20 15:20:37.082  2865-3029  WebSocket               com.dspread.mdm.service              I  🔧 收到服务器 PONG 帧: 1 (待响应PING: 0)
2025-08-20 15:20:54.122  2865-3029  WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误 (Ask Gemini)
                                                                                                    com.neovisionaries.ws.client.WebSocketException: An I/O error occurred while a frame was being read from the web socket: Software caused connection abort
                                                                                                    	at com.neovisionaries.ws.client.ReadingThread.readFrame(ReadingThread.java:375)
                                                                                                    	at com.neovisionaries.ws.client.ReadingThread.main(ReadingThread.java:99)
                                                                                                    	at com.neovisionaries.ws.client.ReadingThread.runMain(ReadingThread.java:64)
                                                                                                    	at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45)
                                                                                                    Caused by: java.net.SocketException: Software caused connection abort
                                                                                                    	at java.net.SocketInputStream.socketRead0(Native Method)
                                                                                                    	at java.net.SocketInputStream.socketRead(SocketInputStream.java:118)
                                                                                                    	at java.net.SocketInputStream.read(SocketInputStream.java:173)
                                                                                                    	at java.net.SocketInputStream.read(SocketInputStream.java:143)
                                                                                                    	at com.android.org.conscrypt.ConscryptEngineSocket$SSLInputStream.readFromSocket(ConscryptEngineSocket.java:983)
                                                                                                    	at com.android.org.conscrypt.ConscryptEngineSocket$SSLInputStream.processDataFromSocket(ConscryptEngineSocket.java:947)
                                                                                                    	at com.android.org.conscrypt.ConscryptEngineSocket$SSLInputStream.readUntilDataAvailable(ConscryptEngineSocket.java:862)
                                                                                                    	at com.android.org.conscrypt.ConscryptEngineSocket$SSLInputStream.read(ConscryptEngineSocket.java:835)
                                                                                                    	at java.io.BufferedInputStream.fill(BufferedInputStream.java:239)
                                                                                                    	at java.io.BufferedInputStream.read1(BufferedInputStream.java:279)
                                                                                                    	at java.io.BufferedInputStream.read(BufferedInputStream.java:338)
                                                                                                    	at java.io.FilterInputStream.read(FilterInputStream.java:133)
                                                                                                    	at com.neovisionaries.ws.client.WebSocketInputStream.readBytes(WebSocketInputStream.java:165)
                                                                                                    	at com.neovisionaries.ws.client.WebSocketInputStream.readFrame(WebSocketInputStream.java:46)
                                                                                                    	at com.neovisionaries.ws.client.ReadingThread.readFrame(ReadingThread.java:338)
                                                                                                    	at com.neovisionaries.ws.client.ReadingThread.main(ReadingThread.java:99) 
                                                                                                    	at com.neovisionaries.ws.client.ReadingThread.runMain(ReadingThread.java:64) 
                                                                                                    	at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45) 
2025-08-20 15:20:54.131  2865-3029  WebSocket               com.dspread.mdm.service              I  🔧 onError: 不触发重连，等待 onDisconnected 处理
2025-08-20 15:20:54.179  2865-2865  Receiver                com.dspread.mdm.service              D  🔧 网络状态变化: 连接=false, 类型=NONE
2025-08-20 15:20:54.203  2865-3030  WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误 (Ask Gemini)
                                                                                                    com.neovisionaries.ws.client.WebSocketException: Flushing frames to the server failed: Broken pipe
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.doFlush(WritingThread.java:434)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.sendFrames(WritingThread.java:384)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.main(WritingThread.java:108)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.runMain(WritingThread.java:55)
                                                                                                    	at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45)
                                                                                                    Caused by: java.net.SocketException: Broken pipe
                                                                                                    	at java.net.SocketOutputStream.socketWrite0(Native Method)
                                                                                                    	at java.net.SocketOutputStream.socketWrite(SocketOutputStream.java:116)
                                                                                                    	at java.net.SocketOutputStream.write(SocketOutputStream.java:156)
                                                                                                    	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.writeToSocket(ConscryptEngineSocket.java:762)
                                                                                                    	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.writeInternal(ConscryptEngineSocket.java:736)
                                                                                                    	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.write(ConscryptEngineSocket.java:699)
                                                                                                    	at java.io.BufferedOutputStream.flushBuffer(BufferedOutputStream.java:81)
                                                                                                    	at java.io.BufferedOutputStream.flush(BufferedOutputStream.java:142)
                                                                                                    	at java.io.BufferedOutputStream.flush(BufferedOutputStream.java:143)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.flush(WritingThread.java:271)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.doFlush(WritingThread.java:422)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.sendFrames(WritingThread.java:384) 
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.main(WritingThread.java:108) 
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.runMain(WritingThread.java:55) 
                                                                                                    	at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45) 
2025-08-20 15:20:54.212  2865-3030  WebSocket               com.dspread.mdm.service              I  🔧 onError: 不触发重连，等待 onDisconnected 处理
2025-08-20 15:20:54.231  2865-2865  Receiver                com.dspread.mdm.service              D  🔧 网络不可用，WebSocket连接可能受影响
2025-08-20 15:20:54.246  2865-2865  Receiver                com.dspread.mdm.service              D  🔧 ServiceManagementEventHandler 处理网络变化事件
2025-08-20 15:20:54.262  2865-2865  Common                  com.dspread.mdm.service              D  🔧 ServiceStartupManager 服务正在运行，无需启动: NETWORK_CHANGE
2025-08-20 15:20:54.268  2865-3030  WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误 (Ask Gemini)
                                                                                                    com.neovisionaries.ws.client.WebSocketException: Flushing frames to the server failed: Broken pipe
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.doFlush(WritingThread.java:434)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.sendFrames(WritingThread.java:384)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.main(WritingThread.java:120)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.runMain(WritingThread.java:55)
                                                                                                    	at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45)
                                                                                                    Caused by: java.net.SocketException: Broken pipe
                                                                                                    	at java.net.SocketOutputStream.socketWrite0(Native Method)
                                                                                                    	at java.net.SocketOutputStream.socketWrite(SocketOutputStream.java:116)
                                                                                                    	at java.net.SocketOutputStream.write(SocketOutputStream.java:156)
                                                                                                    	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.writeToSocket(ConscryptEngineSocket.java:762)
                                                                                                    	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.writeInternal(ConscryptEngineSocket.java:736)
                                                                                                    	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.write(ConscryptEngineSocket.java:699)
                                                                                                    	at java.io.BufferedOutputStream.flushBuffer(BufferedOutputStream.java:81)
                                                                                                    	at java.io.BufferedOutputStream.flush(BufferedOutputStream.java:142)
                                                                                                    	at java.io.BufferedOutputStream.flush(BufferedOutputStream.java:143)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.flush(WritingThread.java:271)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.doFlush(WritingThread.java:422)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.sendFrames(WritingThread.java:384) 
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.main(WritingThread.java:120) 
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.runMain(WritingThread.java:55) 
                                                                                                    	at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45) 
2025-08-20 15:20:54.278  2865-3030  WebSocket               com.dspread.mdm.service              I  🔧 onError: 不触发重连，等待 onDisconnected 处理
2025-08-20 15:20:54.289  2865-2865  Receiver                com.dspread.mdm.service              D  🔧 ServiceManagementEventHandler 网络变化后服务检查完成
2025-08-20 15:20:54.295  2865-3030  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接断开 - 客户端主动断开
2025-08-20 15:20:54.303  2865-3030  WebSocket               com.dspread.mdm.service              I  🔧    服务器关闭帧: code=-1, reason=无原因
2025-08-20 15:20:54.307  2865-2865  Provisioning            com.dspread.mdm.service              D  🔧 ProvisioningEventHandler 网络状态变化
2025-08-20 15:20:54.314  2865-3030  WebSocket               com.dspread.mdm.service              I  🔧    客户端关闭帧: code=1008, reason=An I/O error occurred while a frame was being read from the web socket: Software caused connection abort
2025-08-20 15:20:54.319  2865-2865  Provisioning            com.dspread.mdm.service              D  🔧 ProvisioningEventHandler 忽略初始启动时的网络连接事件
2025-08-20 15:20:54.321  2865-3030  WebSocket               com.dspread.mdm.service              I  🔧    断开发起方: 客户端
2025-08-20 15:20:54.328  2865-3030  WebSocket               com.dspread.mdm.service              I  🔧 停止PING/PONG检测
2025-08-20 15:20:54.336  2865-3030  WebSocket               com.dspread.mdm.service              I  🔧 reconnect() 被调用，调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onDisconnected:710
2025-08-20 15:20:54.344  2865-3030  WebSocket               com.dspread.mdm.service              I  🔧 开始重连 WebSocket... (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onDisconnected:710)
2025-08-20 15:20:54.363  2865-3030  WebSocket               com.dspread.mdm.service              I  🔧 使用Provisioning配置的重连策略: delayPolicy=1, delaySwitch=1, delayTime=60
2025-08-20 15:20:54.372  2865-3030  WebSocket               com.dspread.mdm.service              I  🔧 当前断开记录数: 1, 延迟开关: 1
2025-08-20 15:20:54.379  2865-3030  WebSocket               com.dspread.mdm.service              I  🔧 重连策略计算完成: 第1次重连，间隔3000ms (3秒)
2025-08-20 15:20:54.387  2865-3030  WebSocket               com.dspread.mdm.service              I  🔧 开始第1次重连，间隔3000ms (3秒) (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onDisconnected:710)
2025-08-20 15:20:54.953  2865-2865  Receiver                com.dspread.mdm.service              D  🔧 网络状态变化: 连接=true, 类型=WIFI
2025-08-20 15:20:54.968  2865-2865  Receiver                com.dspread.mdm.service              D  🔧 网络恢复，尝试重新连接WebSocket
2025-08-20 15:20:54.980  2865-2865  Receiver                com.dspread.mdm.service              D  🔧 ServiceManagementEventHandler 处理网络变化事件
2025-08-20 15:20:54.992  2865-2865  Common                  com.dspread.mdm.service              D  🔧 ServiceStartupManager 服务正在运行，无需启动: NETWORK_CHANGE
2025-08-20 15:20:54.999  2865-2865  Receiver                com.dspread.mdm.service              D  🔧 ServiceManagementEventHandler 网络变化后服务检查完成
2025-08-20 15:20:55.006  2865-2865  Provisioning            com.dspread.mdm.service              D  🔧 ProvisioningEventHandler 网络状态变化
2025-08-20 15:20:55.013  2865-2865  Provisioning            com.dspread.mdm.service              D  🔧 ProvisioningEventHandler 忽略初始启动时的网络连接事件
2025-08-20 15:20:57.428  2865-2865  WebSocket               com.dspread.mdm.service              I  🔧 重连 WebSocket: wss://api.dspreadserv.net/status/websocket/register?serialNo=MDEzNTQwOTAyMDI1MDMwNTAzOTk=&publicKey=TUlHZk1BMEdDU3FHU0liM0RRRUJBUVVBQTRHTkFEQ0JpUUtCZ1FDMHNQRkFFbEQzcGhLLzc0bHZHcE5Mbi90dThKaDkvZ3hlcEdITlErSGZ3TFRwenl0SkNFclZNdHNYWUU5RElHeFJqdVR2ejZLNHQzSmxPV1g3S2UzZEJ4ei90empubnhuY1lGOG53S2ZZaWR0TFhLNXhPM1BVbUJ1TFZWRHplMXBmQ1FsUnNvSktHTzdJWjN5amw0QWFWMW1lQnQ0TEV3OGcvcXRmWjBvYU5RSURBUUFC&query=0&msgVer=3&timestamp=1755674457402&signature=hmGN0VeBSB3sI41MQ8FdiSb364QTrSfqJrENDUbwYdGzwSX8Pp/cLI9L/kPm90k55EnPMJ5bLBTefOpt3JdHbCE0/71mae+5RasF1+/yLb+R/v7hzEK1ISIRWa7WgeM5z/O8c/+3zD7uixbh33TgRLqD0tNIU8dTzaM0MmlzZO0=
2025-08-20 15:20:57.439  2865-2865  WebSocket               com.dspread.mdm.service              I  🔧 未检测到系统代理设置
2025-08-20 15:20:57.501  2865-3065  TrafficStats            com.dspread.mdm.service              D  tagSocket(90) with statsTag=0xffffffff, statsUid=-1
2025-08-20 15:20:57.754  2865-3066  TrafficStats            com.dspread.mdm.service              D  tagSocket(99) with statsTag=0xffffffff, statsUid=-1
2025-08-20 15:20:59.167  2865-3073  Common                  com.dspread.mdm.service              I  ✅ WebSocket 物理连接成功，等待服务器 hellMsg
2025-08-20 15:20:59.176  2865-3073  WebSocket               com.dspread.mdm.service              I  🔧 ===== 心跳发送器实际配置 =====
2025-08-20 15:20:59.186  2865-3073  WebSocket               com.dspread.mdm.service              I  🔧 实际 PING 发送间隔: 30000ms (30秒)
2025-08-20 15:20:59.195  2865-3073  WebSocket               com.dspread.mdm.service              I  🔧 PING 定时器名称: PingSender
2025-08-20 15:20:59.203  2865-3073  WebSocket               com.dspread.mdm.service              I  🔧 实际 PONG 发送间隔: 0ms (0秒)
2025-08-20 15:20:59.210  2865-3073  WebSocket               com.dspread.mdm.service              I  🔧 PONG 定时器名称: PongSender
2025-08-20 15:20:59.216  2865-3073  WebSocket               com.dspread.mdm.service              I  🔧 ===============================
2025-08-20 15:20:59.223  2865-3073  WebSocket               com.dspread.mdm.service              I  🔧 启动PING/PONG检测，阈值: 连续3个PING无PONG响应
2025-08-20 15:20:59.266  2865-3073  Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"hellMsg":"hello 01354090202503050399","tranCode":"S0000","version":"1","rebootTime":"01:10:38","serialNo":"01354090202503050399","deviceStatus":"6"}
2025-08-20 15:20:59.272  2865-2885  System                  com.dspread.mdm.service              W  A resource failed to call close. 
2025-08-20 15:20:59.274  2865-3073  Common                  com.dspread.mdm.service              I  ✅ 收到服务器 hellMsg，WebSocket 业务连接成功！
2025-08-20 15:20:59.281  2865-3073  Common                  com.dspread.mdm.service              I  ✅ WebSocket 业务连接成功！
2025-08-20 15:20:59.288  2865-3073  WebSocket               com.dspread.mdm.service              I  🔧 业务连接成功，重连计数已重置为 0
2025-08-20 15:20:59.295  2865-3073  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接成功，检查自身更新任务状态
2025-08-20 15:20:59.303  2865-3073  Receiver                com.dspread.mdm.service              D  🔧 发送广播: com.dspread.mdm.service.CHECK_SELF_UPDATE_STATUS
2025-08-20 15:20:59.311  2865-3073  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接成功
2025-08-20 15:20:59.319  2865-3073  WebSocket               com.dspread.mdm.service              I  🔧 设备状态: 6
2025-08-20 15:20:59.325  2865-3073  WebSocket               com.dspread.mdm.service              I  🔧 检查首次连接标志: isFirstTimeWebSocketConnected=false
2025-08-20 15:20:59.332  2865-3073  WebSocket               com.dspread.mdm.service              I  🔧 非首次连接，跳过初始化信息发送和初始化检查
2025-08-20 15:20:59.338  2865-3073  WebSocket               com.dspread.mdm.service              I  🔧 执行必要的状态同步检查
2025-08-20 15:20:59.345  2865-3073  WebSocket               com.dspread.mdm.service              I  🔧 检查服务更新结果
2025-08-20 15:20:59.351  2865-3073  Task                    com.dspread.mdm.service              W  ⚠️ 任务列表为空，跳过服务更新结果检查
2025-08-20 15:20:59.359  2865-3073  WebSocket               com.dspread.mdm.service              I  🔧 检查OS升级状态...
2025-08-20 15:20:59.365  2865-3073  Common                  com.dspread.mdm.service              D  🔧 OsUpdateStatusChecker WebSocket连接成功，检查OS升级状态
2025-08-20 15:20:59.373  2865-3073  Common                  com.dspread.mdm.service              D  🔧 OsUpdateStatusChecker 没有待检查的OS升级任务
2025-08-20 15:20:59.381  2865-3073  WebSocket               com.dspread.mdm.service              I  🔧 OS升级状态检查已触发
2025-08-20 15:21:09.983  2865-2865  Receiver                com.dspread.mdm.service              D  🔧 网络恢复上送被防抖阻止，剩余时间: 4689ms
2025-08-20 15:21:29.169  2865-3074  WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 1 (第1个，待响应: 1)
2025-08-20 15:21:29.238  2865-2865  WebSocket               com.dspread.mdm.service              W  ⚠️ 当前有1个PING等待PONG响应
2025-08-20 15:21:29.818  2865-3073  WebSocket               com.dspread.mdm.service              I  🔧 收到服务器 PONG 帧: 1 (待响应PING: 0)
2025-08-20 15:21:59.170  2865-3074  WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 2 (第2个，待响应: 1)
2025-08-20 15:21:59.282  2865-2865  WebSocket               com.dspread.mdm.service              W  ⚠️ 当前有1个PING等待PONG响应
2025-08-20 15:21:59.528  2865-3073  WebSocket               com.dspread.mdm.service              I  🔧 收到服务器 PONG 帧: 2 (待响应PING: 0)
2025-08-20 15:22:29.171  2865-3074  WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 3 (第3个，待响应: 1)
2025-08-20 15:22:29.322  2865-2865  WebSocket               com.dspread.mdm.service              W  ⚠️ 当前有1个PING等待PONG响应
2025-08-20 15:22:29.825  2865-3073  WebSocket               com.dspread.mdm.service              I  🔧 收到服务器 PONG 帧: 3 (待响应PING: 0)
2025-08-20 15:22:59.172  2865-3074  WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 4 (第4个，待响应: 1)
2025-08-20 15:22:59.365  2865-2865  WebSocket               com.dspread.mdm.service              W  ⚠️ 当前有1个PING等待PONG响应
2025-08-20 15:22:59.531  2865-3073  WebSocket               com.dspread.mdm.service              I  🔧 收到服务器 PONG 帧: 4 (待响应PING: 0)
2025-08-20 15:23:29.173  2865-3074  WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 5 (第5个，待响应: 1)
2025-08-20 15:23:29.410  2865-2865  WebSocket               com.dspread.mdm.service              W  ⚠️ 当前有1个PING等待PONG响应
2025-08-20 15:23:30.140  2865-3073  WebSocket               com.dspread.mdm.service              I  🔧 收到服务器 PONG 帧: 5 (待响应PING: 0)
2025-08-20 15:23:59.174  2865-3074  WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 6 (第6个，待响应: 1)
2025-08-20 15:23:59.425  2865-2865  WebSocket               com.dspread.mdm.service              W  ⚠️ 当前有1个PING等待PONG响应
2025-08-20 15:23:59.631  2865-3073  WebSocket               com.dspread.mdm.service              I  🔧 收到服务器 PONG 帧: 6 (待响应PING: 0)
2025-08-20 15:24:29.174  2865-3074  WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 7 (第7个，待响应: 1)
2025-08-20 15:24:29.468  2865-2865  WebSocket               com.dspread.mdm.service              W  ⚠️ 当前有1个PING等待PONG响应
2025-08-20 15:24:29.840  2865-3073  WebSocket               com.dspread.mdm.service              I  🔧 收到服务器 PONG 帧: 7 (待响应PING: 0)
2025-08-20 15:24:59.175  2865-3074  WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 8 (第8个，待响应: 1)
2025-08-20 15:24:59.483  2865-2865  WebSocket               com.dspread.mdm.service              W  ⚠️ 当前有1个PING等待PONG响应
2025-08-20 15:24:59.638  2865-3073  WebSocket               com.dspread.mdm.service              I  🔧 收到服务器 PONG 帧: 8 (待响应PING: 0)
2025-08-20 15:25:29.176  2865-3074  WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 9 (第9个，待响应: 1)
2025-08-20 15:25:29.518  2865-2865  WebSocket               com.dspread.mdm.service              W  ⚠️ 当前有1个PING等待PONG响应
2025-08-20 15:25:29.847  2865-3073  WebSocket               com.dspread.mdm.service              I  🔧 收到服务器 PONG 帧: 9 (待响应PING: 0)
2025-08-20 15:25:59.177  2865-3074  WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 10 (第10个，待响应: 1)
2025-08-20 15:25:59.548  2865-3073  WebSocket               com.dspread.mdm.service              I  🔧 收到服务器 PONG 帧: 10 (待响应PING: 0)
2025-08-20 15:26:29.178  2865-3074  WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 11 (第11个，待响应: 1)
2025-08-20 15:26:29.588  2865-2865  WebSocket               com.dspread.mdm.service              W  ⚠️ 当前有1个PING等待PONG响应
2025-08-20 15:26:29.853  2865-3073  WebSocket               com.dspread.mdm.service              I  🔧 收到服务器 PONG 帧: 11 (待响应PING: 0)
2025-08-20 15:26:59.179  2865-3074  WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 12 (第12个，待响应: 1)
2025-08-20 15:26:59.551  2865-3073  WebSocket               com.dspread.mdm.service              I  🔧 收到服务器 PONG 帧: 12 (待响应PING: 0)
2025-08-20 15:27:29.179  2865-3074  WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 13 (第13个，待响应: 1)
2025-08-20 15:27:29.631  2865-2865  WebSocket               com.dspread.mdm.service              W  ⚠️ 当前有1个PING等待PONG响应
2025-08-20 15:27:29.860  2865-3073  WebSocket               com.dspread.mdm.service              I  🔧 收到服务器 PONG 帧: 13 (待响应PING: 0)
2025-08-20 15:27:59.180  2865-3074  WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 14 (第14个，待响应: 1)
2025-08-20 15:27:59.646  2865-2865  WebSocket               com.dspread.mdm.service              W  ⚠️ 当前有1个PING等待PONG响应
2025-08-20 15:28:01.092  2865-3073  WebSocket               com.dspread.mdm.service              I  🔧 收到服务器 PONG 帧: 14 (待响应PING: 0)
2025-08-20 15:28:29.180  2865-3074  WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 15 (第15个，待响应: 1)
2025-08-20 15:28:29.688  2865-2865  WebSocket               com.dspread.mdm.service              W  ⚠️ 当前有1个PING等待PONG响应
2025-08-20 15:28:29.867  2865-3073  WebSocket               com.dspread.mdm.service              I  🔧 收到服务器 PONG 帧: 15 (待响应PING: 0)
2025-08-20 15:28:59.181  2865-3074  WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 16 (第16个，待响应: 1)
2025-08-20 15:28:59.563  2865-3073  WebSocket               com.dspread.mdm.service              I  🔧 收到服务器 PONG 帧: 16 (待响应PING: 0)
2025-08-20 15:29:29.182  2865-3074  WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 17 (第17个，待响应: 1)
2025-08-20 15:29:29.734  2865-2865  WebSocket               com.dspread.mdm.service              W  ⚠️ 当前有1个PING等待PONG响应
2025-08-20 15:29:29.873  2865-3073  WebSocket               com.dspread.mdm.service              I  🔧 收到服务器 PONG 帧: 17 (待响应PING: 0)
2025-08-20 15:29:59.182  2865-3074  WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 18 (第18个，待响应: 1)
2025-08-20 15:29:59.570  2865-3073  WebSocket               com.dspread.mdm.service              I  🔧 收到服务器 PONG 帧: 18 (待响应PING: 0)
2025-08-20 15:30:29.183  2865-3074  WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 19 (第19个，待响应: 1)
2025-08-20 15:30:29.573  2865-3073  WebSocket               com.dspread.mdm.service              I  🔧 收到服务器 PONG 帧: 19 (待响应PING: 0)
2025-08-20 15:30:59.184  2865-3074  WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 20 (第20个，待响应: 1)
2025-08-20 15:30:59.781  2865-3073  WebSocket               com.dspread.mdm.service              I  🔧 收到服务器 PONG 帧: 20 (待响应PING: 0)
2025-08-20 15:31:29.185  2865-3074  WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 21 (第21个，待响应: 1)
2025-08-20 15:31:29.580  2865-3073  WebSocket               com.dspread.mdm.service              I  🔧 收到服务器 PONG 帧: 21 (待响应PING: 0)
2025-08-20 15:31:59.185  2865-3074  WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 22 (第22个，待响应: 1)
2025-08-20 15:31:59.788  2865-3073  WebSocket               com.dspread.mdm.service              I  🔧 收到服务器 PONG 帧: 22 (待响应PING: 0)
2025-08-20 15:32:29.186  2865-3074  WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 23 (第23个，待响应: 1)
2025-08-20 15:32:29.585  2865-3073  WebSocket               com.dspread.mdm.service              I  🔧 收到服务器 PONG 帧: 23 (待响应PING: 0)
2025-08-20 15:32:59.189  2865-3074  WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 24 (第24个，待响应: 1)
2025-08-20 15:32:59.691  2865-3073  WebSocket               com.dspread.mdm.service              I  🔧 收到服务器 PONG 帧: 24 (待响应PING: 0)
2025-08-20 15:33:29.189  2865-3074  WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 25 (第25个，待响应: 1)
2025-08-20 15:33:29.593  2865-3073  WebSocket               com.dspread.mdm.service              I  🔧 收到服务器 PONG 帧: 25 (待响应PING: 0)
2025-08-20 15:33:59.190  2865-3074  WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 26 (第26个，待响应: 1)
2025-08-20 15:33:59.597  2865-3073  WebSocket               com.dspread.mdm.service              I  🔧 收到服务器 PONG 帧: 26 (待响应PING: 0)
2025-08-20 15:34:29.191  2865-3074  WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 27 (第27个，待响应: 1)
2025-08-20 15:34:29.806  2865-3073  WebSocket               com.dspread.mdm.service              I  🔧 收到服务器 PONG 帧: 27 (待响应PING: 0)
2025-08-20 15:34:59.192  2865-3074  WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 28 (第28个，待响应: 1)
2025-08-20 15:34:59.602  2865-3073  WebSocket               com.dspread.mdm.service              I  🔧 收到服务器 PONG 帧: 28 (待响应PING: 0)
2025-08-20 15:35:29.192  2865-3074  WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 29 (第29个，待响应: 1)
2025-08-20 15:35:29.607  2865-3073  WebSocket               com.dspread.mdm.service              I  🔧 收到服务器 PONG 帧: 29 (待响应PING: 0)
2025-08-20 15:35:59.193  2865-3074  WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 30 (第30个，待响应: 1)
2025-08-20 15:35:59.610  2865-3073  WebSocket               com.dspread.mdm.service              I  🔧 收到服务器 PONG 帧: 30 (待响应PING: 0)
2025-08-20 15:36:29.194  2865-3074  WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 31 (第31个，待响应: 1)
2025-08-20 15:36:29.612  2865-3073  WebSocket               com.dspread.mdm.service              I  🔧 收到服务器 PONG 帧: 31 (待响应PING: 0)
2025-08-20 15:36:59.194  2865-3074  WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 32 (第32个，待响应: 1)
2025-08-20 15:36:59.719  2865-3073  WebSocket               com.dspread.mdm.service              I  🔧 收到服务器 PONG 帧: 32 (待响应PING: 0)
2025-08-20 15:37:29.195  2865-3074  WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 33 (第33个，待响应: 1)
2025-08-20 15:37:29.722  2865-3073  WebSocket               com.dspread.mdm.service              I  🔧 收到服务器 PONG 帧: 33 (待响应PING: 0)
2025-08-20 15:37:59.196  2865-3074  WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 34 (第34个，待响应: 1)
2025-08-20 15:37:59.559  2865-3073  WebSocket               com.dspread.mdm.service              I  🔧 收到服务器 PONG 帧: 34 (待响应PING: 0)
2025-08-20 15:38:04.733  2865-2865  Receiver                com.dspread.mdm.service              D  🔧 屏幕点亮
2025-08-20 15:38:04.775  2865-2865  WebSocket               com.dspread.mdm.service              W  ⚠️ 未知触发条件: screen_on，默认按被动式处理
2025-08-20 15:38:04.817  2865-2865  WebSocket               com.dspread.mdm.service              I  🔧 C0201 被动式上送拒绝: screen_on (拒绝: 3)
2025-08-20 15:38:04.825  2865-2865  WebSocket               com.dspread.mdm.service              I  🔧 C0201 设备状态上送被流量控制阻止: 被动事件 'screen_on' 在平衡模式 - 重要变化下未启用
2025-08-20 15:38:04.833  2865-2865  Receiver                com.dspread.mdm.service              D  🔧 屏幕点亮处理完成
2025-08-20 15:38:11.895  2865-3073  WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误 (Ask Gemini)
                                                                                                    com.neovisionaries.ws.client.WebSocketException: An I/O error occurred while a frame was being read from the web socket: Software caused connection abort
                                                                                                    	at com.neovisionaries.ws.client.ReadingThread.readFrame(ReadingThread.java:375)
                                                                                                    	at com.neovisionaries.ws.client.ReadingThread.main(ReadingThread.java:99)
                                                                                                    	at com.neovisionaries.ws.client.ReadingThread.runMain(ReadingThread.java:64)
                                                                                                    	at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45)
                                                                                                    Caused by: java.net.SocketException: Software caused connection abort
                                                                                                    	at java.net.SocketInputStream.socketRead0(Native Method)
                                                                                                    	at java.net.SocketInputStream.socketRead(SocketInputStream.java:118)
                                                                                                    	at java.net.SocketInputStream.read(SocketInputStream.java:173)
                                                                                                    	at java.net.SocketInputStream.read(SocketInputStream.java:143)
                                                                                                    	at com.android.org.conscrypt.ConscryptEngineSocket$SSLInputStream.readFromSocket(ConscryptEngineSocket.java:983)
                                                                                                    	at com.android.org.conscrypt.ConscryptEngineSocket$SSLInputStream.processDataFromSocket(ConscryptEngineSocket.java:947)
                                                                                                    	at com.android.org.conscrypt.ConscryptEngineSocket$SSLInputStream.readUntilDataAvailable(ConscryptEngineSocket.java:862)
                                                                                                    	at com.android.org.conscrypt.ConscryptEngineSocket$SSLInputStream.read(ConscryptEngineSocket.java:835)
                                                                                                    	at java.io.BufferedInputStream.fill(BufferedInputStream.java:239)
                                                                                                    	at java.io.BufferedInputStream.read1(BufferedInputStream.java:279)
                                                                                                    	at java.io.BufferedInputStream.read(BufferedInputStream.java:338)
                                                                                                    	at java.io.FilterInputStream.read(FilterInputStream.java:133)
                                                                                                    	at com.neovisionaries.ws.client.WebSocketInputStream.readBytes(WebSocketInputStream.java:165)
                                                                                                    	at com.neovisionaries.ws.client.WebSocketInputStream.readFrame(WebSocketInputStream.java:46)
                                                                                                    	at com.neovisionaries.ws.client.ReadingThread.readFrame(ReadingThread.java:338)
                                                                                                    	at com.neovisionaries.ws.client.ReadingThread.main(ReadingThread.java:99) 
                                                                                                    	at com.neovisionaries.ws.client.ReadingThread.runMain(ReadingThread.java:64) 
                                                                                                    	at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45) 
2025-08-20 15:38:11.933  2865-3073  WebSocket               com.dspread.mdm.service              I  🔧 onError: 不触发重连，等待 onDisconnected 处理
2025-08-20 15:38:11.965  2865-2865  Receiver                com.dspread.mdm.service              D  🔧 网络状态变化: 连接=false, 类型=NONE
2025-08-20 15:38:11.997  2865-2865  Receiver                com.dspread.mdm.service              D  🔧 网络不可用，WebSocket连接可能受影响
2025-08-20 15:38:12.002  2865-3074  WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误 (Ask Gemini)
                                                                                                    com.neovisionaries.ws.client.WebSocketException: Flushing frames to the server failed: Broken pipe
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.doFlush(WritingThread.java:434)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.sendFrames(WritingThread.java:384)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.main(WritingThread.java:108)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.runMain(WritingThread.java:55)
                                                                                                    	at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45)
                                                                                                    Caused by: java.net.SocketException: Broken pipe
                                                                                                    	at java.net.SocketOutputStream.socketWrite0(Native Method)
                                                                                                    	at java.net.SocketOutputStream.socketWrite(SocketOutputStream.java:116)
                                                                                                    	at java.net.SocketOutputStream.write(SocketOutputStream.java:156)
                                                                                                    	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.writeToSocket(ConscryptEngineSocket.java:762)
                                                                                                    	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.writeInternal(ConscryptEngineSocket.java:736)
                                                                                                    	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.write(ConscryptEngineSocket.java:699)
                                                                                                    	at java.io.BufferedOutputStream.flushBuffer(BufferedOutputStream.java:81)
                                                                                                    	at java.io.BufferedOutputStream.flush(BufferedOutputStream.java:142)
                                                                                                    	at java.io.BufferedOutputStream.flush(BufferedOutputStream.java:143)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.flush(WritingThread.java:271)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.doFlush(WritingThread.java:422)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.sendFrames(WritingThread.java:384) 
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.main(WritingThread.java:108) 
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.runMain(WritingThread.java:55) 
                                                                                                    	at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45) 
2025-08-20 15:38:12.008  2865-2865  Receiver                com.dspread.mdm.service              D  🔧 ServiceManagementEventHandler 处理网络变化事件
2025-08-20 15:38:12.017  2865-3074  WebSocket               com.dspread.mdm.service              I  🔧 onError: 不触发重连，等待 onDisconnected 处理
2025-08-20 15:38:12.021  2865-2865  Common                  com.dspread.mdm.service              D  🔧 ServiceStartupManager 服务正在运行，无需启动: NETWORK_CHANGE
2025-08-20 15:38:12.028  2865-2865  Receiver                com.dspread.mdm.service              D  🔧 ServiceManagementEventHandler 网络变化后服务检查完成
2025-08-20 15:38:12.034  2865-3074  WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误 (Ask Gemini)
                                                                                                    com.neovisionaries.ws.client.WebSocketException: Flushing frames to the server failed: Broken pipe
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.doFlush(WritingThread.java:434)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.sendFrames(WritingThread.java:384)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.main(WritingThread.java:120)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.runMain(WritingThread.java:55)
                                                                                                    	at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45)
                                                                                                    Caused by: java.net.SocketException: Broken pipe
                                                                                                    	at java.net.SocketOutputStream.socketWrite0(Native Method)
                                                                                                    	at java.net.SocketOutputStream.socketWrite(SocketOutputStream.java:116)
                                                                                                    	at java.net.SocketOutputStream.write(SocketOutputStream.java:156)
                                                                                                    	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.writeToSocket(ConscryptEngineSocket.java:762)
                                                                                                    	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.writeInternal(ConscryptEngineSocket.java:736)
                                                                                                    	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.write(ConscryptEngineSocket.java:699)
                                                                                                    	at java.io.BufferedOutputStream.flushBuffer(BufferedOutputStream.java:81)
                                                                                                    	at java.io.BufferedOutputStream.flush(BufferedOutputStream.java:142)
                                                                                                    	at java.io.BufferedOutputStream.flush(BufferedOutputStream.java:143)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.flush(WritingThread.java:271)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.doFlush(WritingThread.java:422)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.sendFrames(WritingThread.java:384) 
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.main(WritingThread.java:120) 
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.runMain(WritingThread.java:55) 
                                                                                                    	at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45) 
2025-08-20 15:38:12.038  2865-2865  Provisioning            com.dspread.mdm.service              D  🔧 ProvisioningEventHandler 网络状态变化
2025-08-20 15:38:12.045  2865-2865  Provisioning            com.dspread.mdm.service              D  🔧 ProvisioningEventHandler 忽略初始启动时的网络连接事件
2025-08-20 15:38:12.047  2865-3074  WebSocket               com.dspread.mdm.service              I  🔧 onError: 不触发重连，等待 onDisconnected 处理
2025-08-20 15:38:12.058  2865-3074  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接断开 - 客户端主动断开
2025-08-20 15:38:12.066  2865-3074  WebSocket               com.dspread.mdm.service              I  🔧    服务器关闭帧: code=-1, reason=无原因
2025-08-20 15:38:12.074  2865-3074  WebSocket               com.dspread.mdm.service              I  🔧    客户端关闭帧: code=1008, reason=An I/O error occurred while a frame was being read from the web socket: Software caused connection abort
2025-08-20 15:38:12.081  2865-3074  WebSocket               com.dspread.mdm.service              I  🔧    断开发起方: 客户端
2025-08-20 15:38:12.088  2865-3074  WebSocket               com.dspread.mdm.service              I  🔧 停止PING/PONG检测
2025-08-20 15:38:12.095  2865-3074  WebSocket               com.dspread.mdm.service              I  🔧 reconnect() 被调用，调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onDisconnected:710
2025-08-20 15:38:12.103  2865-3074  WebSocket               com.dspread.mdm.service              I  🔧 开始重连 WebSocket... (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onDisconnected:710)
2025-08-20 15:38:12.120  2865-3074  WebSocket               com.dspread.mdm.service              I  🔧 使用Provisioning配置的重连策略: delayPolicy=1, delaySwitch=1, delayTime=60
2025-08-20 15:38:12.126  2865-3074  WebSocket               com.dspread.mdm.service              I  🔧 当前断开记录数: 2, 延迟开关: 1
2025-08-20 15:38:12.133  2865-3074  WebSocket               com.dspread.mdm.service              I  🔧 重连策略计算完成: 第1次重连，间隔3000ms (3秒)
2025-08-20 15:38:12.140  2865-3074  WebSocket               com.dspread.mdm.service              I  🔧 开始第1次重连，间隔3000ms (3秒) (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onDisconnected:710)
2025-08-20 15:38:15.185  2865-2865  WebSocket               com.dspread.mdm.service              I  🔧 重连 WebSocket: wss://api.dspreadserv.net/status/websocket/register?serialNo=MDEzNTQwOTAyMDI1MDMwNTAzOTk=&publicKey=TUlHZk1BMEdDU3FHU0liM0RRRUJBUVVBQTRHTkFEQ0JpUUtCZ1FDMHNQRkFFbEQzcGhLLzc0bHZHcE5Mbi90dThKaDkvZ3hlcEdITlErSGZ3TFRwenl0SkNFclZNdHNYWUU5RElHeFJqdVR2ejZLNHQzSmxPV1g3S2UzZEJ4ei90empubnhuY1lGOG53S2ZZaWR0TFhLNXhPM1BVbUJ1TFZWRHplMXBmQ1FsUnNvSktHTzdJWjN5amw0QWFWMW1lQnQ0TEV3OGcvcXRmWjBvYU5RSURBUUFC&query=0&msgVer=3&timestamp=1755675495156&signature=B5bMuKWUVR20ssQFJf30TfJ0RPztcXN0RP3fynp8RXqc80AwE71ak37UfN5yLOArxUFjpZrT/U2NokDMcWjHsfSn1I+kdSDK2zai0dbRwD0KB73YBYT1tm9rEqIDG5oKBDK6WojlwaaS/fvL1M68xUKpbW7CcfslqwQE4K+O5s4=
2025-08-20 15:38:15.198  2865-2865  WebSocket               com.dspread.mdm.service              I  🔧 未检测到系统代理设置
2025-08-20 15:38:15.251  2865-3181  WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误
2025-08-20 15:38:15.259  2865-3181  WebSocket               com.dspread.mdm.service              I  🔧 onError: 不触发重连，等待 onDisconnected 处理
2025-08-20 15:38:15.266  2865-3181  WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误
2025-08-20 15:38:15.274  2865-3181  WebSocket               com.dspread.mdm.service              I  🔧 reconnect() 被调用，调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onConnectError:655
2025-08-20 15:38:15.280  2865-3181  WebSocket               com.dspread.mdm.service              I  🔧 开始重连 WebSocket... (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onConnectError:655)
2025-08-20 15:38:15.296  2865-3181  WebSocket               com.dspread.mdm.service              I  🔧 使用Provisioning配置的重连策略: delayPolicy=1, delaySwitch=1, delayTime=60
2025-08-20 15:38:15.303  2865-3181  WebSocket               com.dspread.mdm.service              I  🔧 当前断开记录数: 2, 延迟开关: 1
2025-08-20 15:38:15.309  2865-3181  WebSocket               com.dspread.mdm.service              I  🔧 重连策略计算完成: 第2次重连，间隔3000ms (3秒)
2025-08-20 15:38:15.316  2865-3181  WebSocket               com.dspread.mdm.service              I  🔧 开始第2次重连，间隔3000ms (3秒) (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onConnectError:655)
2025-08-20 15:38:18.340  2865-2865  WebSocket               com.dspread.mdm.service              I  🔧 重连 WebSocket: wss://api.dspreadserv.net/status/websocket/register?serialNo=MDEzNTQwOTAyMDI1MDMwNTAzOTk=&publicKey=TUlHZk1BMEdDU3FHU0liM0RRRUJBUVVBQTRHTkFEQ0JpUUtCZ1FDMHNQRkFFbEQzcGhLLzc0bHZHcE5Mbi90dThKaDkvZ3hlcEdITlErSGZ3TFRwenl0SkNFclZNdHNYWUU5RElHeFJqdVR2ejZLNHQzSmxPV1g3S2UzZEJ4ei90empubnhuY1lGOG53S2ZZaWR0TFhLNXhPM1BVbUJ1TFZWRHplMXBmQ1FsUnNvSktHTzdJWjN5amw0QWFWMW1lQnQ0TEV3OGcvcXRmWjBvYU5RSURBUUFC&query=0&msgVer=3&timestamp=1755675498324&signature=r8xFpLCYkf0vyZU9xv1zqbtTx/9DhxZt1GB53ForRtjrTXhO+1/XPdvuZ+5PKpQLMzGaD8mklZ3tze5HLv4siMCojB/5eGYDthvbaEziK9yPQAEKqnBjMyK1o+eZNxFRcxvZuh/TLjKdEDJ2NYdg4tLM5UKuY941ATr/7CYYGsw=
2025-08-20 15:38:18.348  2865-2865  WebSocket               com.dspread.mdm.service              I  🔧 未检测到系统代理设置
2025-08-20 15:38:18.389  2865-3183  WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误
2025-08-20 15:38:18.401  2865-3183  WebSocket               com.dspread.mdm.service              I  🔧 onError: 不触发重连，等待 onDisconnected 处理
2025-08-20 15:38:18.412  2865-3183  WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误
2025-08-20 15:38:18.424  2865-3183  WebSocket               com.dspread.mdm.service              I  🔧 reconnect() 被调用，调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onConnectError:655
2025-08-20 15:38:18.431  2865-3183  WebSocket               com.dspread.mdm.service              I  🔧 开始重连 WebSocket... (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onConnectError:655)
2025-08-20 15:38:18.447  2865-3183  WebSocket               com.dspread.mdm.service              I  🔧 使用Provisioning配置的重连策略: delayPolicy=1, delaySwitch=1, delayTime=60
2025-08-20 15:38:18.456  2865-3183  WebSocket               com.dspread.mdm.service              I  🔧 当前断开记录数: 2, 延迟开关: 1
2025-08-20 15:38:18.464  2865-3183  WebSocket               com.dspread.mdm.service              I  🔧 重连策略计算完成: 第3次重连，间隔3000ms (3秒)
2025-08-20 15:38:18.471  2865-2883  ead.mdm.service         com.dspread.mdm.service              W  Reducing the number of considered missed Gc histogram windows from 104 to 100
2025-08-20 15:38:18.474  2865-3183  WebSocket               com.dspread.mdm.service              I  🔧 开始第3次重连，间隔3000ms (3秒) (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onConnectError:655)
2025-08-20 15:38:21.502  2865-2865  WebSocket               com.dspread.mdm.service              I  🔧 重连 WebSocket: wss://api.dspreadserv.net/status/websocket/register?serialNo=MDEzNTQwOTAyMDI1MDMwNTAzOTk=&publicKey=TUlHZk1BMEdDU3FHU0liM0RRRUJBUVVBQTRHTkFEQ0JpUUtCZ1FDMHNQRkFFbEQzcGhLLzc0bHZHcE5Mbi90dThKaDkvZ3hlcEdITlErSGZ3TFRwenl0SkNFclZNdHNYWUU5RElHeFJqdVR2ejZLNHQzSmxPV1g3S2UzZEJ4ei90empubnhuY1lGOG53S2ZZaWR0TFhLNXhPM1BVbUJ1TFZWRHplMXBmQ1FsUnNvSktHTzdJWjN5amw0QWFWMW1lQnQ0TEV3OGcvcXRmWjBvYU5RSURBUUFC&query=0&msgVer=3&timestamp=1755675501483&signature=OoyBybdvZgfaEnAEqGaJrHaXUaijqeKCjIsRXaSSqAJHfMZerAsCBEvDu3Ls+U03DWaj7kNMHQstPTTk1RevoAVlyGpXTHUuzIcMdSRazjn623urbauszkIeHRL9Q9nEyx/W48mxTftY6ONErj7/SCuBHkkTkWsHVBLl06SdRPM=
2025-08-20 15:38:21.514  2865-2865  WebSocket               com.dspread.mdm.service              I  🔧 未检测到系统代理设置
2025-08-20 15:38:21.563  2865-3185  WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误
2025-08-20 15:38:21.572  2865-3185  WebSocket               com.dspread.mdm.service              I  🔧 onError: 不触发重连，等待 onDisconnected 处理
2025-08-20 15:38:21.578  2865-3185  WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误
2025-08-20 15:38:21.686  2865-3185  WebSocket               com.dspread.mdm.service              I  🔧 reconnect() 被调用，调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onConnectError:655
2025-08-20 15:38:21.691  2865-3185  WebSocket               com.dspread.mdm.service              I  🔧 开始重连 WebSocket... (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onConnectError:655)
2025-08-20 15:38:21.705  2865-3185  WebSocket               com.dspread.mdm.service              I  🔧 使用Provisioning配置的重连策略: delayPolicy=1, delaySwitch=1, delayTime=60
2025-08-20 15:38:21.710  2865-3185  WebSocket               com.dspread.mdm.service              I  🔧 当前断开记录数: 2, 延迟开关: 1
2025-08-20 15:38:21.715  2865-3185  WebSocket               com.dspread.mdm.service              I  🔧 重连策略计算完成: 第4次重连，间隔6000ms (6秒)
2025-08-20 15:38:21.720  2865-3185  WebSocket               com.dspread.mdm.service              I  🔧 开始第4次重连，间隔6000ms (6秒) (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onConnectError:655)
2025-08-20 15:38:27.744  2865-2865  WebSocket               com.dspread.mdm.service              I  🔧 重连 WebSocket: wss://api.dspreadserv.net/status/websocket/register?serialNo=MDEzNTQwOTAyMDI1MDMwNTAzOTk=&publicKey=TUlHZk1BMEdDU3FHU0liM0RRRUJBUVVBQTRHTkFEQ0JpUUtCZ1FDMHNQRkFFbEQzcGhLLzc0bHZHcE5Mbi90dThKaDkvZ3hlcEdITlErSGZ3TFRwenl0SkNFclZNdHNYWUU5RElHeFJqdVR2ejZLNHQzSmxPV1g3S2UzZEJ4ei90empubnhuY1lGOG53S2ZZaWR0TFhLNXhPM1BVbUJ1TFZWRHplMXBmQ1FsUnNvSktHTzdJWjN5amw0QWFWMW1lQnQ0TEV3OGcvcXRmWjBvYU5RSURBUUFC&query=0&msgVer=3&timestamp=1755675507728&signature=aJ1jP7yQpNheLZEnzuxVoFA6/RFrUFsV9zU7l4oIe83rwekItss9HczoCFQmJyomK0yOSscc4MF61gslYy6xSZX0/mSCzxM+CQNdEmzFHLHmSvZyj/r0W3+u+wZsjSEgIG67oHmHogXZcL5A37T60MVENr0prH8StOs4u/uG8zg=
2025-08-20 15:38:27.751  2865-2865  WebSocket               com.dspread.mdm.service              I  🔧 未检测到系统代理设置
2025-08-20 15:38:27.789  2865-3187  WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误
2025-08-20 15:38:27.796  2865-3187  WebSocket               com.dspread.mdm.service              I  🔧 onError: 不触发重连，等待 onDisconnected 处理
2025-08-20 15:38:27.801  2865-3187  WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误
2025-08-20 15:38:27.807  2865-3187  WebSocket               com.dspread.mdm.service              I  🔧 reconnect() 被调用，调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onConnectError:655
2025-08-20 15:38:27.812  2865-3187  WebSocket               com.dspread.mdm.service              I  🔧 开始重连 WebSocket... (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onConnectError:655)
2025-08-20 15:38:27.827  2865-3187  WebSocket               com.dspread.mdm.service              I  🔧 使用Provisioning配置的重连策略: delayPolicy=1, delaySwitch=1, delayTime=60
2025-08-20 15:38:27.832  2865-3187  WebSocket               com.dspread.mdm.service              I  🔧 当前断开记录数: 2, 延迟开关: 1
2025-08-20 15:38:27.837  2865-3187  WebSocket               com.dspread.mdm.service              I  🔧 重连策略计算完成: 第5次重连，间隔9000ms (9秒)
2025-08-20 15:38:27.842  2865-3187  WebSocket               com.dspread.mdm.service              I  🔧 开始第5次重连，间隔9000ms (9秒) (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onConnectError:655)
2025-08-20 15:38:36.873  2865-2865  WebSocket               com.dspread.mdm.service              I  🔧 重连 WebSocket: wss://api.dspreadserv.net/status/websocket/register?serialNo=MDEzNTQwOTAyMDI1MDMwNTAzOTk=&publicKey=TUlHZk1BMEdDU3FHU0liM0RRRUJBUVVBQTRHTkFEQ0JpUUtCZ1FDMHNQRkFFbEQzcGhLLzc0bHZHcE5Mbi90dThKaDkvZ3hlcEdITlErSGZ3TFRwenl0SkNFclZNdHNYWUU5RElHeFJqdVR2ejZLNHQzSmxPV1g3S2UzZEJ4ei90empubnhuY1lGOG53S2ZZaWR0TFhLNXhPM1BVbUJ1TFZWRHplMXBmQ1FsUnNvSktHTzdJWjN5amw0QWFWMW1lQnQ0TEV3OGcvcXRmWjBvYU5RSURBUUFC&query=0&msgVer=3&timestamp=1755675516854&signature=HK6meUVA+D/GiDxDTBP8BowSTGi47Bgg+2DDVrLJwoOSndrBzMuH/yDapL2H2RdEgacfO3SUxWaVCsVUPfCXCQbc1dTDtJr06hYbOdHsS8jqKUXuppPFGY+ioPc8/ITpe3Up8rm+/9OsB6hqT2/xGrk/J/DrXiVdTTfv/n1Wx3U=
2025-08-20 15:38:36.881  2865-2865  WebSocket               com.dspread.mdm.service              I  🔧 未检测到系统代理设置
2025-08-20 15:38:36.925  2865-3189  WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误
2025-08-20 15:38:36.932  2865-3189  WebSocket               com.dspread.mdm.service              I  🔧 onError: 不触发重连，等待 onDisconnected 处理
2025-08-20 15:38:36.939  2865-3189  WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误
2025-08-20 15:38:36.948  2865-3189  WebSocket               com.dspread.mdm.service              I  🔧 reconnect() 被调用，调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onConnectError:655
2025-08-20 15:38:36.954  2865-3189  WebSocket               com.dspread.mdm.service              I  🔧 开始重连 WebSocket... (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onConnectError:655)
2025-08-20 15:38:36.969  2865-3189  WebSocket               com.dspread.mdm.service              I  🔧 使用Provisioning配置的重连策略: delayPolicy=1, delaySwitch=1, delayTime=60
2025-08-20 15:38:36.974  2865-3189  WebSocket               com.dspread.mdm.service              I  🔧 当前断开记录数: 2, 延迟开关: 1
2025-08-20 15:38:36.979  2865-3189  WebSocket               com.dspread.mdm.service              I  🔧 重连策略计算完成: 第6次重连，间隔12000ms (12秒)
2025-08-20 15:38:36.985  2865-3189  WebSocket               com.dspread.mdm.service              I  🔧 开始第6次重连，间隔12000ms (12秒) (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onConnectError:655)
2025-08-20 15:38:49.034  2865-2865  WebSocket               com.dspread.mdm.service              I  🔧 重连 WebSocket: wss://api.dspreadserv.net/status/websocket/register?serialNo=MDEzNTQwOTAyMDI1MDMwNTAzOTk=&publicKey=TUlHZk1BMEdDU3FHU0liM0RRRUJBUVVBQTRHTkFEQ0JpUUtCZ1FDMHNQRkFFbEQzcGhLLzc0bHZHcE5Mbi90dThKaDkvZ3hlcEdITlErSGZ3TFRwenl0SkNFclZNdHNYWUU5RElHeFJqdVR2ejZLNHQzSmxPV1g3S2UzZEJ4ei90empubnhuY1lGOG53S2ZZaWR0TFhLNXhPM1BVbUJ1TFZWRHplMXBmQ1FsUnNvSktHTzdJWjN5amw0QWFWMW1lQnQ0TEV3OGcvcXRmWjBvYU5RSURBUUFC&query=0&msgVer=3&timestamp=1755675529006&signature=KjMheOGuPg451ucSofyPdXij1nZXSWld/W7WguAUVtGYHhqjh2btQkQ/tEuEjnKblkCwjw+uwhjp02tC9bjl/CBXPvuAVB4TqIyLPnh+KBWrKqZWdQrOVZT7v3CK0j9noG0VnfsoV84aI/44WPg4W5jb4IhjUibwxZgsYK/e/lk=
2025-08-20 15:38:49.045  2865-2865  WebSocket               com.dspread.mdm.service              I  🔧 未检测到系统代理设置
2025-08-20 15:38:49.095  2865-3191  WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误
2025-08-20 15:38:49.106  2865-3191  WebSocket               com.dspread.mdm.service              I  🔧 onError: 不触发重连，等待 onDisconnected 处理
2025-08-20 15:38:49.111  2865-3191  WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误
2025-08-20 15:38:49.118  2865-3191  WebSocket               com.dspread.mdm.service              I  🔧 reconnect() 被调用，调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onConnectError:655
2025-08-20 15:38:49.124  2865-3191  WebSocket               com.dspread.mdm.service              I  🔧 开始重连 WebSocket... (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onConnectError:655)
2025-08-20 15:38:49.138  2865-3191  WebSocket               com.dspread.mdm.service              I  🔧 使用Provisioning配置的重连策略: delayPolicy=1, delaySwitch=1, delayTime=60
2025-08-20 15:38:49.144  2865-3191  WebSocket               com.dspread.mdm.service              I  🔧 当前断开记录数: 2, 延迟开关: 1
2025-08-20 15:38:49.149  2865-3191  WebSocket               com.dspread.mdm.service              I  🔧 重连策略计算完成: 第7次重连，间隔15000ms (15秒)
2025-08-20 15:38:49.154  2865-3191  WebSocket               com.dspread.mdm.service              I  🔧 开始第7次重连，间隔15000ms (15秒) (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onConnectError:655)
2025-08-20 15:39:04.190  2865-2865  WebSocket               com.dspread.mdm.service              I  🔧 重连 WebSocket: wss://api.dspreadserv.net/status/websocket/register?serialNo=MDEzNTQwOTAyMDI1MDMwNTAzOTk=&publicKey=TUlHZk1BMEdDU3FHU0liM0RRRUJBUVVBQTRHTkFEQ0JpUUtCZ1FDMHNQRkFFbEQzcGhLLzc0bHZHcE5Mbi90dThKaDkvZ3hlcEdITlErSGZ3TFRwenl0SkNFclZNdHNYWUU5RElHeFJqdVR2ejZLNHQzSmxPV1g3S2UzZEJ4ei90empubnhuY1lGOG53S2ZZaWR0TFhLNXhPM1BVbUJ1TFZWRHplMXBmQ1FsUnNvSktHTzdJWjN5amw0QWFWMW1lQnQ0TEV3OGcvcXRmWjBvYU5RSURBUUFC&query=0&msgVer=3&timestamp=1755675544162&signature=Ed8kS1EubboOtP4/PllsYn+CZ2AV6CjvqWedYWe1itfLXOafjHM0uU1HIUkPIBTyn8y/YIE03NeqvyPVpwlhApL6V/KTXEolZZ3K1vG6k5RQUy2Jy12KSEuB4GW2E0IQBIfJ+VQUTQKDRdONvXdx+YhrCo/kWDxL5ex1cLuYLNA=
2025-08-20 15:39:04.202  2865-2865  WebSocket               com.dspread.mdm.service              I  🔧 未检测到系统代理设置
2025-08-20 15:39:04.254  2865-3195  WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误
2025-08-20 15:39:04.267  2865-3195  WebSocket               com.dspread.mdm.service              I  🔧 onError: 不触发重连，等待 onDisconnected 处理
2025-08-20 15:39:04.276  2865-3195  WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误
2025-08-20 15:39:04.287  2865-3195  WebSocket               com.dspread.mdm.service              I  🔧 reconnect() 被调用，调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onConnectError:655
2025-08-20 15:39:04.295  2865-3195  WebSocket               com.dspread.mdm.service              I  🔧 开始重连 WebSocket... (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onConnectError:655)
2025-08-20 15:39:04.313  2865-3195  WebSocket               com.dspread.mdm.service              I  🔧 使用Provisioning配置的重连策略: delayPolicy=1, delaySwitch=1, delayTime=60
2025-08-20 15:39:04.319  2865-3195  WebSocket               com.dspread.mdm.service              I  🔧 当前断开记录数: 2, 延迟开关: 1
2025-08-20 15:39:04.324  2865-3195  WebSocket               com.dspread.mdm.service              I  🔧 重连策略计算完成: 第8次重连，间隔15000ms (15秒)
2025-08-20 15:39:04.330  2865-3195  WebSocket               com.dspread.mdm.service              I  🔧 开始第8次重连，间隔15000ms (15秒) (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onConnectError:655)
2025-08-20 15:39:19.386  2865-2865  WebSocket               com.dspread.mdm.service              I  🔧 重连 WebSocket: wss://api.dspreadserv.net/status/websocket/register?serialNo=MDEzNTQwOTAyMDI1MDMwNTAzOTk=&publicKey=TUlHZk1BMEdDU3FHU0liM0RRRUJBUVVBQTRHTkFEQ0JpUUtCZ1FDMHNQRkFFbEQzcGhLLzc0bHZHcE5Mbi90dThKaDkvZ3hlcEdITlErSGZ3TFRwenl0SkNFclZNdHNYWUU5RElHeFJqdVR2ejZLNHQzSmxPV1g3S2UzZEJ4ei90empubnhuY1lGOG53S2ZZaWR0TFhLNXhPM1BVbUJ1TFZWRHplMXBmQ1FsUnNvSktHTzdJWjN5amw0QWFWMW1lQnQ0TEV3OGcvcXRmWjBvYU5RSURBUUFC&query=0&msgVer=3&timestamp=1755675559357&signature=d9isrVn0PSPlvJH92cbCop4B/iA07JOebB9kNcw4jSrgywGo/cGaTEhIj+hHeCJBbJ1g+A3Amn6sY26M/ARucU+8508xdm0Lpcg0HJucv6z/R8a6MX+WEi0rmRoz5nIErkhzb/VLezp+BPn2yLoCikQyfU928G+yvIIZg+Rfnxw=
2025-08-20 15:39:19.397  2865-2865  WebSocket               com.dspread.mdm.service              I  🔧 未检测到系统代理设置
2025-08-20 15:39:19.447  2865-3197  WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误
2025-08-20 15:39:19.456  2865-3197  WebSocket               com.dspread.mdm.service              I  🔧 onError: 不触发重连，等待 onDisconnected 处理
2025-08-20 15:39:19.463  2865-3197  WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误
2025-08-20 15:39:19.471  2865-3197  WebSocket               com.dspread.mdm.service              I  🔧 reconnect() 被调用，调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onConnectError:655
2025-08-20 15:39:19.478  2865-3197  WebSocket               com.dspread.mdm.service              I  🔧 开始重连 WebSocket... (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onConnectError:655)
2025-08-20 15:39:19.493  2865-3197  WebSocket               com.dspread.mdm.service              I  🔧 使用Provisioning配置的重连策略: delayPolicy=1, delaySwitch=1, delayTime=60
2025-08-20 15:39:19.499  2865-3197  WebSocket               com.dspread.mdm.service              I  🔧 当前断开记录数: 2, 延迟开关: 1
2025-08-20 15:39:19.504  2865-3197  WebSocket               com.dspread.mdm.service              I  🔧 重连策略计算完成: 第9次重连，间隔15000ms (15秒)
2025-08-20 15:39:19.510  2865-3197  WebSocket               com.dspread.mdm.service              I  🔧 开始第9次重连，间隔15000ms (15秒) (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onConnectError:655)
2025-08-20 15:39:34.546  2865-2865  WebSocket               com.dspread.mdm.service              I  🔧 重连 WebSocket: wss://api.dspreadserv.net/status/websocket/register?serialNo=MDEzNTQwOTAyMDI1MDMwNTAzOTk=&publicKey=TUlHZk1BMEdDU3FHU0liM0RRRUJBUVVBQTRHTkFEQ0JpUUtCZ1FDMHNQRkFFbEQzcGhLLzc0bHZHcE5Mbi90dThKaDkvZ3hlcEdITlErSGZ3TFRwenl0SkNFclZNdHNYWUU5RElHeFJqdVR2ejZLNHQzSmxPV1g3S2UzZEJ4ei90empubnhuY1lGOG53S2ZZaWR0TFhLNXhPM1BVbUJ1TFZWRHplMXBmQ1FsUnNvSktHTzdJWjN5amw0QWFWMW1lQnQ0TEV3OGcvcXRmWjBvYU5RSURBUUFC&query=0&msgVer=3&timestamp=1755675574520&signature=K0gRCUOimdzY7H50f6pxN2BmpYqpT1fIH+pjson/3Wa5O67uZM0MM/OGHbvPWs+bqHEbZoUwphbLr/ql4caTDckgeYZJERJon3XEnuT5ohhnJaR4aAbn1oZt+ehmUEVxZ1CVqZRyzSC4Ystg2z/llM6SFQiP3T08Arkf2IunmJQ=
2025-08-20 15:39:34.558  2865-2865  WebSocket               com.dspread.mdm.service              I  🔧 未检测到系统代理设置
2025-08-20 15:39:34.609  2865-3199  WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误
2025-08-20 15:39:34.616  2865-3199  WebSocket               com.dspread.mdm.service              I  🔧 onError: 不触发重连，等待 onDisconnected 处理
2025-08-20 15:39:34.622  2865-3199  WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误
2025-08-20 15:39:34.629  2865-3199  WebSocket               com.dspread.mdm.service              I  🔧 reconnect() 被调用，调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onConnectError:655
2025-08-20 15:39:34.634  2865-3199  WebSocket               com.dspread.mdm.service              I  🔧 开始重连 WebSocket... (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onConnectError:655)
2025-08-20 15:39:34.649  2865-3199  WebSocket               com.dspread.mdm.service              I  🔧 使用Provisioning配置的重连策略: delayPolicy=1, delaySwitch=1, delayTime=60
2025-08-20 15:39:34.654  2865-3199  WebSocket               com.dspread.mdm.service              I  🔧 当前断开记录数: 2, 延迟开关: 1
2025-08-20 15:39:34.660  2865-3199  WebSocket               com.dspread.mdm.service              I  🔧 重连策略计算完成: 第10次重连，间隔15000ms (15秒)
2025-08-20 15:39:34.666  2865-3199  WebSocket               com.dspread.mdm.service              I  🔧 开始第10次重连，间隔15000ms (15秒) (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onConnectError:655)
2025-08-20 15:39:49.714  2865-2865  WebSocket               com.dspread.mdm.service              I  🔧 重连 WebSocket: wss://api.dspreadserv.net/status/websocket/register?serialNo=MDEzNTQwOTAyMDI1MDMwNTAzOTk=&publicKey=TUlHZk1BMEdDU3FHU0liM0RRRUJBUVVBQTRHTkFEQ0JpUUtCZ1FDMHNQRkFFbEQzcGhLLzc0bHZHcE5Mbi90dThKaDkvZ3hlcEdITlErSGZ3TFRwenl0SkNFclZNdHNYWUU5RElHeFJqdVR2ejZLNHQzSmxPV1g3S2UzZEJ4ei90empubnhuY1lGOG53S2ZZaWR0TFhLNXhPM1BVbUJ1TFZWRHplMXBmQ1FsUnNvSktHTzdJWjN5amw0QWFWMW1lQnQ0TEV3OGcvcXRmWjBvYU5RSURBUUFC&query=0&msgVer=3&timestamp=1755675589685&signature=m3exx+8AT270Hk3Q9+gilCDcKtbTo7UCPCH6yfwhVI3HNcEbQplpboujkzi7LK17UWP+7zy397p4qbv4yl0JGTFsuAO+hQDFc7tR4WJU7oCQXLZdDf9e4cQR/l1RdcdYet6xItvieOpJlQZNX8iD2ayPd12M59SmwMu3mjpbP/g=
2025-08-20 15:39:49.723  2865-2865  WebSocket               com.dspread.mdm.service              I  🔧 未检测到系统代理设置
2025-08-20 15:39:49.769  2865-3201  WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误
2025-08-20 15:39:49.776  2865-3201  WebSocket               com.dspread.mdm.service              I  🔧 onError: 不触发重连，等待 onDisconnected 处理
2025-08-20 15:39:49.782  2865-3201  WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误
2025-08-20 15:39:49.789  2865-3201  WebSocket               com.dspread.mdm.service              I  🔧 reconnect() 被调用，调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onConnectError:655
2025-08-20 15:39:49.794  2865-3201  WebSocket               com.dspread.mdm.service              I  🔧 开始重连 WebSocket... (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onConnectError:655)
2025-08-20 15:39:49.809  2865-3201  WebSocket               com.dspread.mdm.service              I  🔧 使用Provisioning配置的重连策略: delayPolicy=1, delaySwitch=1, delayTime=60
2025-08-20 15:39:49.815  2865-3201  WebSocket               com.dspread.mdm.service              I  🔧 当前断开记录数: 2, 延迟开关: 1
2025-08-20 15:39:49.820  2865-3201  WebSocket               com.dspread.mdm.service              I  🔧 重连策略计算完成: 第11次重连，间隔15000ms (15秒)
2025-08-20 15:39:49.826  2865-3201  WebSocket               com.dspread.mdm.service              I  🔧 开始第11次重连，间隔15000ms (15秒) (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onConnectError:655)
2025-08-20 15:39:57.401  2865-2865  Receiver                com.dspread.mdm.service              D  🔧 网络状态变化: 连接=true, 类型=WIFI
2025-08-20 15:39:57.409  2865-2865  Receiver                com.dspread.mdm.service              D  🔧 网络恢复，尝试重新连接WebSocket
2025-08-20 15:39:57.418  2865-2865  Receiver                com.dspread.mdm.service              D  🔧 ServiceManagementEventHandler 处理网络变化事件
2025-08-20 15:39:57.437  2865-2865  Common                  com.dspread.mdm.service              D  🔧 ServiceStartupManager 服务正在运行，无需启动: NETWORK_CHANGE
2025-08-20 15:39:57.448  2865-2865  Receiver                com.dspread.mdm.service              D  🔧 ServiceManagementEventHandler 网络变化后服务检查完成
2025-08-20 15:39:57.458  2865-2865  Provisioning            com.dspread.mdm.service              D  🔧 ProvisioningEventHandler 网络状态变化
2025-08-20 15:39:57.469  2865-2865  Provisioning            com.dspread.mdm.service              D  🔧 ProvisioningEventHandler 忽略初始启动时的网络连接事件
2025-08-20 15:40:04.862  2865-2865  WebSocket               com.dspread.mdm.service              I  🔧 重连 WebSocket: wss://api.dspreadserv.net/status/websocket/register?serialNo=MDEzNTQwOTAyMDI1MDMwNTAzOTk=&publicKey=TUlHZk1BMEdDU3FHU0liM0RRRUJBUVVBQTRHTkFEQ0JpUUtCZ1FDMHNQRkFFbEQzcGhLLzc0bHZHcE5Mbi90dThKaDkvZ3hlcEdITlErSGZ3TFRwenl0SkNFclZNdHNYWUU5RElHeFJqdVR2ejZLNHQzSmxPV1g3S2UzZEJ4ei90empubnhuY1lGOG53S2ZZaWR0TFhLNXhPM1BVbUJ1TFZWRHplMXBmQ1FsUnNvSktHTzdJWjN5amw0QWFWMW1lQnQ0TEV3OGcvcXRmWjBvYU5RSURBUUFC&query=0&msgVer=3&timestamp=1755675604832&signature=qgXkRgJzugLUDSNu6q+CYWvSW4OYzhzlfyZCbTe4slSQIK3DtrkZM/RNpQvFQ0my2QFsdlAcORDxGRrMh5H3KSXdiKG3ChifkCOnUc7AME2oXjV3GgZ0lxD6W/LwRQbXyNsVFtD9B4V310xgib64Q6q7CgvG29IfJHR7re79ifo=
2025-08-20 15:40:04.875  2865-2865  WebSocket               com.dspread.mdm.service              I  🔧 未检测到系统代理设置
2025-08-20 15:40:04.962  2865-3249  TrafficStats            com.dspread.mdm.service              D  tagSocket(86) with statsTag=0xffffffff, statsUid=-1
2025-08-20 15:40:05.214  2865-3250  TrafficStats            com.dspread.mdm.service              D  tagSocket(89) with statsTag=0xffffffff, statsUid=-1
2025-08-20 15:40:06.531  2865-3251  Common                  com.dspread.mdm.service              I  ✅ WebSocket 物理连接成功，等待服务器 hellMsg
2025-08-20 15:40:06.540  2865-3251  WebSocket               com.dspread.mdm.service              I  🔧 ===== 心跳发送器实际配置 =====
2025-08-20 15:40:06.548  2865-3251  WebSocket               com.dspread.mdm.service              I  🔧 实际 PING 发送间隔: 30000ms (30秒)
2025-08-20 15:40:06.555  2865-3251  WebSocket               com.dspread.mdm.service              I  🔧 PING 定时器名称: PingSender
2025-08-20 15:40:06.562  2865-3251  WebSocket               com.dspread.mdm.service              I  🔧 实际 PONG 发送间隔: 0ms (0秒)
2025-08-20 15:40:06.568  2865-3251  WebSocket               com.dspread.mdm.service              I  🔧 PONG 定时器名称: PongSender
2025-08-20 15:40:06.574  2865-3251  WebSocket               com.dspread.mdm.service              I  🔧 ===============================
2025-08-20 15:40:06.580  2865-3251  WebSocket               com.dspread.mdm.service              I  🔧 启动PING/PONG检测，阈值: 连续3个PING无PONG响应
2025-08-20 15:40:06.730  2865-3251  Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"hellMsg":"hello 01354090202503050399","tranCode":"S0000","version":"1","rebootTime":"02:10:33","serialNo":"01354090202503050399","deviceStatus":"6"}
2025-08-20 15:40:06.737  2865-3251  Common                  com.dspread.mdm.service              I  ✅ 收到服务器 hellMsg，WebSocket 业务连接成功！
2025-08-20 15:40:06.743  2865-3251  Common                  com.dspread.mdm.service              I  ✅ WebSocket 业务连接成功！
2025-08-20 15:40:06.750  2865-3251  WebSocket               com.dspread.mdm.service              I  🔧 业务连接成功，重连计数已重置为 0
2025-08-20 15:40:06.756  2865-3251  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接成功，检查自身更新任务状态
2025-08-20 15:40:06.764  2865-3251  Receiver                com.dspread.mdm.service              D  🔧 发送广播: com.dspread.mdm.service.CHECK_SELF_UPDATE_STATUS
2025-08-20 15:40:06.770  2865-3251  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接成功
2025-08-20 15:40:06.776  2865-3251  WebSocket               com.dspread.mdm.service              I  🔧 设备状态: 6
2025-08-20 15:40:06.782  2865-3251  WebSocket               com.dspread.mdm.service              I  🔧 检查首次连接标志: isFirstTimeWebSocketConnected=false
2025-08-20 15:40:06.787  2865-3251  WebSocket               com.dspread.mdm.service              I  🔧 非首次连接，跳过初始化信息发送和初始化检查
2025-08-20 15:40:06.793  2865-3251  WebSocket               com.dspread.mdm.service              I  🔧 执行必要的状态同步检查
2025-08-20 15:40:06.798  2865-3251  WebSocket               com.dspread.mdm.service              I  🔧 检查服务更新结果
2025-08-20 15:40:06.804  2865-3251  Task                    com.dspread.mdm.service              W  ⚠️ 任务列表为空，跳过服务更新结果检查
2025-08-20 15:40:06.809  2865-3251  WebSocket               com.dspread.mdm.service              I  🔧 检查OS升级状态...
2025-08-20 15:40:06.815  2865-3251  Common                  com.dspread.mdm.service              D  🔧 OsUpdateStatusChecker WebSocket连接成功，检查OS升级状态
2025-08-20 15:40:06.821  2865-3251  Common                  com.dspread.mdm.service              D  🔧 OsUpdateStatusChecker 没有待检查的OS升级任务
2025-08-20 15:40:06.826  2865-3251  WebSocket               com.dspread.mdm.service              I  🔧 OS升级状态检查已触发
2025-08-20 15:40:12.427  2865-2865  Receiver                com.dspread.mdm.service              D  🔧 网络恢复，重新上传网络状态信息（防抖通过）
2025-08-20 15:40:12.447  2865-2865  WebSocket               com.dspread.mdm.service              I  🔧 C0904 被动式上送拒绝: NETWORK_RECOVERY (拒绝: 4)
2025-08-20 15:40:12.455  2865-2865  WebSocket               com.dspread.mdm.service              I  🔧 C0904 网络状态上送被流量控制阻止: 被动事件 'NETWORK_RECOVERY' 在平衡模式 - 重要变化下未启用
2025-08-20 15:40:36.534  2865-3252  WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 1 (第1个，待响应: 1)
2025-08-20 15:40:36.587  2865-2865  WebSocket               com.dspread.mdm.service              W  ⚠️ 当前有1个PING等待PONG响应
2025-08-20 15:40:36.908  2865-3251  WebSocket               com.dspread.mdm.service              I  🔧 收到服务器 PONG 帧: 1 (待响应PING: 0)
2025-08-20 15:41:06.535  2865-3252  WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 2 (第2个，待响应: 1)
2025-08-20 15:41:06.604  2865-2865  WebSocket               com.dspread.mdm.service              W  ⚠️ 当前有1个PING等待PONG响应
2025-08-20 15:41:06.889  2865-3251  WebSocket               com.dspread.mdm.service              I  🔧 收到服务器 PONG 帧: 2 (待响应PING: 0)
2025-08-20 15:41:21.761  2865-2885  System                  com.dspread.mdm.service              W  A resource failed to call close. 
2025-08-20 15:41:22.057  2865-2865  Receiver                com.dspread.mdm.service              D  🔧 屏幕关闭
2025-08-20 15:41:22.064  2865-2865  WebSocket               com.dspread.mdm.service              W  ⚠️ 未知触发条件: screen_off，默认按被动式处理
2025-08-20 15:41:22.089  2865-2865  WebSocket               com.dspread.mdm.service              I  🔧 C0201 被动式上送拒绝: screen_off (拒绝: 5)
2025-08-20 15:41:22.100  2865-2865  WebSocket               com.dspread.mdm.service              I  🔧 C0201 设备状态上送被流量控制阻止: 被动事件 'screen_off' 在平衡模式 - 重要变化下未启用
2025-08-20 15:41:22.108  2865-2865  Receiver                com.dspread.mdm.service              D  🔧 屏幕关闭处理完成
2025-08-20 15:41:36.536  2865-3252  WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 3 (第3个，待响应: 1)
2025-08-20 15:41:36.623  2865-2865  WebSocket               com.dspread.mdm.service              W  ⚠️ 当前有1个PING等待PONG响应
2025-08-20 15:41:36.917  2865-3251  WebSocket               com.dspread.mdm.service              I  🔧 收到服务器 PONG 帧: 3 (待响应PING: 0)
