package com.dspread.mdm.service.network.websocket.message.handler

import android.app.AlertDialog
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.os.Handler
import android.os.Looper
import android.view.WindowManager
import com.dspread.mdm.service.services.AppInstallService
import com.dspread.mdm.service.services.AppUninstallService
import com.dspread.mdm.service.network.websocket.message.WsMessageSender
import com.dspread.mdm.service.network.websocket.constant.WsTransactionCodes
import com.dspread.mdm.service.network.websocket.message.strategy.UploadTriggers
import com.dspread.mdm.service.network.websocket.WebSocketCenter
import com.dspread.mdm.service.broadcast.core.BroadcastActions
import com.dspread.mdm.service.utils.log.Logger
import com.dspread.mdm.service.modules.ModuleManagerRegistry
import com.dspread.mdm.service.modules.logstream.LogStreamWebSocketHandler
import com.dspread.mdm.service.modules.remoteview.RemoteViewHandler
import com.dspread.mdm.service.modules.apn.ApnHandler
import com.dspread.mdm.service.modules.remoteview.RemoteViewManager
import com.dspread.mdm.service.modules.remoteview.RemoteViewWebSocketManager
import com.dspread.mdm.service.modules.remoteview.model.RemoteViewConfig
import com.dspread.mdm.service.modules.remoteview.model.RemoteViewCaptureMode
import com.dspread.mdm.service.modules.remoteview.model.RemoteViewEvent
import com.dspread.mdm.service.modules.remoteview.model.RemoteViewStatus
import com.dspread.mdm.service.modules.logstream.LogStreamManager
import com.dspread.mdm.service.modules.wifi.WifiProfileHandler
import com.dspread.mdm.service.platform.api.system.SystemControlApi
import com.dspread.mdm.service.broadcast.core.BroadcastSender
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.launch
import kotlinx.coroutines.delay
import org.json.JSONObject

/**
 * 命令处理器
 * 处理 SC 系列命令
 */
class CommandHandler(context: Context) : BaseMessageHandler(context) {

    companion object {
        private const val TAG = "CommandHandler"
    }

    // 使用SupervisorJob的协程作用域，避免使用GlobalScope
    private val handlerScope = CoroutineScope(Dispatchers.IO + SupervisorJob())

    // 日志流WebSocket处理器
    private val logStreamHandler = LogStreamWebSocketHandler(context)

    // Remote View处理器
    private val remoteViewHandler = RemoteViewHandler(context)

    override fun handleMessage(message: String) {
        val jsonObject = parseMessage(message) ?: return
        val messageInfo = getMessageInfo(jsonObject)
        
        Logger.wsm("处理命令: ${messageInfo.tranCode}")
        
        when (messageInfo.tranCode) {
            WsTransactionCodes.CMD_REBOOT -> handleRebootCommand(jsonObject)
            WsTransactionCodes.CMD_NOTICE -> handleNoticeCommand(jsonObject)
            WsTransactionCodes.CMD_WIFI_CONFIG -> handleWifiConfigCommand(jsonObject)
            WsTransactionCodes.CMD_APN_CONFIG -> handleApnConfigCommand(jsonObject)
            WsTransactionCodes.CMD_HEARTBEAT -> handleHeartbeatCommand(jsonObject, message)
            WsTransactionCodes.CMD_SOUND_PLAY -> handleSoundPlayCommand(jsonObject)
            WsTransactionCodes.CMD_LOG_UPLOAD -> handleLogUploadCommand(jsonObject)
            WsTransactionCodes.CMD_SCREENSHOT -> handleScreenshotCommand(jsonObject)
            WsTransactionCodes.CMD_LOCATION_UPLOAD -> handleLocationUploadCommand(jsonObject)
            WsTransactionCodes.CMD_SEND_GEO_OTP -> handleOtpCommand(jsonObject)
            WsTransactionCodes.CMD_PROVISION_TIMER -> handleProvisionTimerCommand(jsonObject)
            WsTransactionCodes.CMD_WIFI_SCAN -> handleWifiScanCommand(jsonObject)
            WsTransactionCodes.CMD_MOBILE_DATA -> handleMobileDataCommand(jsonObject)
            WsTransactionCodes.CMD_SYSTEM_UPDATE -> handleSystemUpdateCommand(jsonObject)
            WsTransactionCodes.CMD_APP_INSTALL -> handleAppInstallCommand(jsonObject)
            WsTransactionCodes.CMD_APP_UNINSTALL -> handleAppUninstallCommand(jsonObject)
            WsTransactionCodes.CMD_CLEAR_DATA -> handleClearDataCommand(jsonObject)
            WsTransactionCodes.CMD_FORCE_STOP -> handleForceStopCommand(jsonObject)
            else -> {
                Logger.wsmE("未知的命令类型: ${messageInfo.tranCode}")
            }
        }
    }

    /**
     * 处理重启命令
     */
    private fun handleRebootCommand(jsonObject: JSONObject) {
        Logger.wsm("执行重启命令")
        try {
            val data = getDataFromMessage(jsonObject)
            val reason = data?.optString("reason", "Remote reboot command")

            Logger.wsm("收到重启命令，原因: $reason")

            // 发送确认响应
            sendResponse(jsonObject, "1", "重启命令已接收，正在执行重启")

            // 延迟执行重启，确保响应能够发送成功
            Thread {
                try {
                    Thread.sleep(2000) // 等待2秒确保响应发送完成

                    // 使用SystemControlApi执行重启
                    val systemControlApi = SystemControlApi(context)
                    val result = systemControlApi.reboot(reason)

                    if (result.isSuccess) {
                        val successResult = result as com.dspread.mdm.service.platform.api.model.SystemOperationResult.Success
                        Logger.wsm("重启命令执行成功: ${successResult.message}")
                    } else {
                        val failureResult = result as com.dspread.mdm.service.platform.api.model.SystemOperationResult.Failure
                        Logger.wsmE("重启命令执行失败: ${failureResult.error}")
                    }

                } catch (e: Exception) {
                    Logger.wsmE("延迟执行重启失败", e)
                }
            }.start()

        } catch (e: Exception) {
            Logger.wsmE("处理重启命令失败", e)
            sendResponse(jsonObject, "0", "重启命令处理失败: ${e.message}")
        }
    }

    /**
     * 处理通知消息命令
     */
    private fun handleNoticeCommand(jsonObject: JSONObject) {
        val data = getDataFromMessage(jsonObject) ?: return
        
        try {
            val noticeMsg = data.getString("notice_msg")
            Logger.wsm("显示通知消息: $noticeMsg")
            
            Handler(Looper.getMainLooper()).post {
                showNoticeDialog(noticeMsg)
            }
        } catch (e: Exception) {
            Logger.wsmE("处理通知消息失败", e)
        }
    }

    /**
     * 显示通知对话框
     */
    private fun showNoticeDialog(message: String) {
        try {
            val builder = AlertDialog.Builder(context)
            builder.setTitle("提示")
                .setCancelable(false)
                .setMessage(message)
                .setPositiveButton("确认") { dialog, _ ->
                    dialog.dismiss()
                }
            
            val dialog = builder.create()
            dialog.window?.setType(WindowManager.LayoutParams.TYPE_SYSTEM_ALERT)
            dialog.show()
            
            Logger.wsm("通知对话框已显示")
        } catch (e: Exception) {
            Logger.wsmE("显示通知对话框失败", e)
        }
    }

    /**
     * 处理WiFi配置命令 (ST006)
     * WiFi Profile配置协议，直接调用WifiProfileHandler处理
     */
    private fun handleWifiConfigCommand(jsonObject: JSONObject) {
        try {
            Logger.wsm("开始处理WiFi配置命令 (ST006)")

            // 创建WifiProfileHandler实例并调用handleMessage
            val wifiProfileHandler = WifiProfileHandler(context)

            // 在协程中调用suspend函数
            handlerScope.launch {
                try {
                    val result = wifiProfileHandler.handleMessage(jsonObject.toString())
                    if (result.isSuccess) {
                        Logger.wsm("WiFi配置命令处理成功")
                    } else {
                        Logger.wsmE("WiFi配置命令处理失败: ${result.exceptionOrNull()?.message}")
                    }
                } catch (e: Exception) {
                    Logger.wsmE("WiFi配置命令处理异常", e)
                }
            }

            Logger.wsm("WiFi配置命令已提交执行")
        } catch (e: Exception) {
            Logger.wsmE("处理WiFi配置命令失败", e)
        }
    }

    private fun handleApnConfigCommand(jsonObject: JSONObject) {
        try {
            Logger.wsm("开始处理APN配置命令 (ST009)")

            // 创建ApnHandler实例并调用handleMessage
            val apnHandler = ApnHandler(context)

            // 在协程中调用suspend函数
            handlerScope.launch {
                try {
                    val result = apnHandler.handleMessage(jsonObject.toString())
                    if (result.isSuccess) {
                        Logger.wsm("APN配置命令处理成功")
                    } else {
                        Logger.wsmE("APN配置命令处理失败: ${result.exceptionOrNull()?.message}")
                    }
                } catch (e: Exception) {
                    Logger.wsmE("APN配置命令处理异常", e)
                }
            }

            Logger.wsm("APN配置命令已提交执行")
        } catch (e: Exception) {
            Logger.wsmE("处理APN配置命令失败", e)
        }
    }

    /**
     * 处理心跳命令
     */
    private fun handleHeartbeatCommand(jsonObject: JSONObject, originalMessage: String = "") {
        val data = getDataFromMessage(jsonObject) ?: return

        try {
            val cType = data.optString("c_type")
            Logger.wsm("收到${WsTransactionCodes.CMD_HEARTBEAT}命令，类型: $cType")

            when (cType) {
                "CALLHB" -> {
                    Logger.wsm("收到服务器心跳调用，发送心跳响应")
                    // 收到服务器心跳调用时，发送心跳响应
                    WebSocketCenter.heartBeat()
                }
                "CALLC0109" -> {
                    Logger.wsm("服务器请求终端信息上传")
                    // C0109是完整的终端信息上传
                    WsMessageSender.uploadTerminalInfo()

                    // 业务需要：同时上传应用信息（含服务信息）
                    Logger.wsm("同时上传C0901应用信息（含服务信息）")
                    // 检查并保存默认开启的服务（如logstream）
                    checkAndSaveDefaultServices()

                    // 发送C0901应用信息上传消息（会自动从ServiceInfoManager获取存储的服务信息）
                    WsMessageSender.uploadAppInfo(UploadTriggers.COMMAND_TRIGGER, true)
                }
                "CALLC0901" -> {
                    Logger.wsm("服务器请求应用信息上传")
                    WsMessageSender.uploadAppInfo()
                }
                "CALLC0902" -> {
                    Logger.wsm("服务器请求电池状态上传")
                    WsMessageSender.uploadBatteryStatus()
                }
                "CALLC0903" -> {
                    Logger.wsm("服务器请求数据信息上传")
                    WsMessageSender.uploadDataInfo()
                }
                "CALLC0904" -> {
                    Logger.wsm("服务器请求网络状态上传")
                    WsMessageSender.uploadNetworkStatus()
                }
                "CALLC0908" -> {
                    Logger.wsm("服务器请求完整设备信息上传")
                }
                "CALLCR004" -> {
                    Logger.wsm("服务器请求OS更新检查")
                    WsMessageSender.checkOSUpdate()
                }
                "CALL_ALL_INFO" -> {
                    Logger.wsm("服务器请求所有设备信息")
                    WsMessageSender.uploadTerminalInfo()
                    WsMessageSender.uploadAppInfo()
                    WsMessageSender.uploadBatteryStatus()
                    WsMessageSender.uploadDataInfo()
                    WsMessageSender.uploadNetworkStatus()
                    WsMessageSender.checkOSUpdate()
                }
                "INSTALL_APK" -> {
                    Logger.wsm("服务器请求安装应用")
                    handleAppInstall(data)
                }
                "UNINSTALL_APK" -> {
                    Logger.wsm("服务器请求卸载应用")
                    handleAppUninstall(data)
                }
                "CALL_LOG_STREAM" -> {
                    Logger.wsm("服务器请求启动日志流")
                    logStreamHandler.handleCallLogStream(data)
                }
                "CLOSE_LOG_STREAM" -> {
                    Logger.wsm("收到CLOSE_LOG_STREAM命令")
                    Logger.wsm("服务器请求关闭日志流，立即执行")
                    // 直接调用LogStreamHandler处理，避免重复逻辑
                    logStreamHandler.handleCloseLogStream(data)
                }
                "CALL_REMOTE_CONTROL" -> {
                    Logger.wsm("服务器请求启动远程控制")
                    // 直接调用RemoteViewHandler的启动逻辑，避免重复创建管理器
                    val bucketPath = data?.optString("param") ?: ""
                    Logger.wsm("启动Remote View服务，bucket路径: $bucketPath")
                    GlobalScope.launch {
                        startRemoteViewDirectly(bucketPath)
                    }
                }
                "CLOSE_REMOTE_CONTROL" -> {
                    Logger.wsm("收到SC004命令，类型: CLOSE_REMOTE_CONTROL")
                    Logger.wsm("服务器请求关闭远程控制")
                    // 直接调用原有的停止方法，确保正确停止
                    // Logger.wsm("停止Remote View服务") // 减少重复日志
                    stopRemoteViewService()
                }
                "REMOTE_VIEW_CONFIG" -> {
                    Logger.wsm("服务器请求更新远程控制配置")
                    // 配置更新可以通过RemoteViewHandler处理
                    GlobalScope.launch {
                        remoteViewHandler.handleMessage(jsonObject.toString())
                    }
                }
                "START_ACTIVITY" -> {
                    Logger.wsm("服务器请求启动应用")
                    handleStartActivity(data)
                }
                else -> {
                    Logger.wsmW("未知的${WsTransactionCodes.CMD_HEARTBEAT}请求类型: $cType")
                }
            }
        } catch (e: Exception) {
            Logger.wsmE("处理心跳命令失败", e)
        }
    }

    /**
     * 处理重置命令
     */
    private fun handleUnboxResetCommand(jsonObject: JSONObject) {
        val messageInfo = getMessageInfo(jsonObject)
        Logger.wsm("执行重置命令")
        
        try {
            // 保存请求信息用于后续响应
            // unboxResetRequestId = messageInfo.requestId
            // unboxResetRequestTime = messageInfo.requestTime
            
            // 这里需要实现重置逻辑
            // 例如：清除数据、恢复出厂设置等
            
            Logger.wsm("重置命令已执行")
        } catch (e: Exception) {
            Logger.wsmE("执行重置命令失败", e)
        }
    }

    /**
     * 处理声音播放命令
     */
    private fun handleSoundPlayCommand(jsonObject: JSONObject) {
        val data = getDataFromMessage(jsonObject) ?: return
        
        try {
            val soundType = data.optString("sound_type")
            val duration = data.optInt("duration", 5000)
            
            Logger.wsm("播放声音: type=$soundType, duration=${duration}ms")
            
            // 这里需要实现声音播放逻辑
            // MediaPlayer 或其他音频播放方式
            
            Logger.wsm("声音播放命令已执行")
        } catch (e: Exception) {
            Logger.wsmE("处理声音播放失败", e)
        }
    }

    /**
     * 处理日志上传命令
     */
    private fun handleLogUploadCommand(jsonObject: JSONObject) {
        val data = getDataFromMessage(jsonObject) ?: return
        
        try {
            val logType = data.optString("log_type", "all")
            val startTime = data.optString("start_time")
            val endTime = data.optString("end_time")
            
            Logger.wsm("上传日志: type=$logType, start=$startTime, end=$endTime")
            
            // 这里需要实现日志收集和上传逻辑
            
            Logger.wsm("日志上传命令已执行")
        } catch (e: Exception) {
            Logger.wsmE("处理日志上传失败", e)
        }
    }

    /**
     * 处理截屏命令
     */
    private fun handleScreenshotCommand(jsonObject: JSONObject) {
        Logger.wsm("执行截屏命令")
        
        try {
            // 这里需要实现截屏逻辑
            // 可能需要系统权限
            
            Logger.wsm("截屏命令已执行")
        } catch (e: Exception) {
            Logger.wsmE("执行截屏命令失败", e)
        }
    }

    /**
     * 处理位置上传命令
     */
    private fun handleLocationUploadCommand(jsonObject: JSONObject) {
        Logger.wsm("执行位置上传命令")
        
        try {
            // 这里需要实现位置获取和上传逻辑
            // GPS、WiFi、基站定位等
            
            Logger.wsm("位置上传命令已执行")
        } catch (e: Exception) {
            Logger.wsmE("执行位置上传命令失败", e)
        }
    }

    /**
     * 处理OTP密码命令
     */
    private fun handleOtpCommand(jsonObject: JSONObject) {
        try {
            Logger.wsm("处理OTP密码命令 (SC005)")

            val data = getDataFromMessage(jsonObject)
            if (data == null) {
                Logger.wsmE("OTP命令缺少data字段")
                return
            }

            val cType = data.optString("c_type")
            if (cType == "UNLOCK_OTP") {
                val param = data.optJSONObject("param")
                if (param != null) {
                    val password = param.optString("password")
                    val expirationTimeStr = param.optString("expiration_time")

                    var expirationTime = 0L
                    if (expirationTimeStr.isNotEmpty()) {
                        try {
                            expirationTime = expirationTimeStr.toLong()
                        } catch (e: Exception) {
                            Logger.wsmE("解析过期时间失败: $expirationTimeStr", e)
                            expirationTime = 0L
                        }
                    }

                    Logger.wsm("收到OTP密码: password=$password, expirationTime=$expirationTime")

                    // 发送广播给LockScreenActivity
                    BroadcastSender.sendBroadcast(
                        context,
                        BroadcastActions.ACTION_GET_ONETIME_PWD,
                        "password" to password,
                        "expiration_time" to expirationTime
                    )

                    Logger.wsm("OTP密码广播已发送")
                } else {
                    Logger.wsmE("OTP命令缺少param字段")
                }
            } else {
                Logger.wsmW("未知的OTP命令类型: $cType")
            }

        } catch (e: Exception) {
            Logger.wsmE("处理OTP密码命令失败", e)
        }
    }

    /**
     * 处理定时器设置命令
     */
    private fun handleProvisionTimerCommand(jsonObject: JSONObject) {
        val data = getDataFromMessage(jsonObject) ?: return
        
        try {
            val timerType = data.optString("timer_type")
            val interval = data.optLong("interval", 0)
            
            Logger.wsm("设置定时器: type=$timerType, interval=${interval}ms")
            
            // 这里需要实现定时器设置逻辑
            
            Logger.wsm("定时器设置命令已执行")
        } catch (e: Exception) {
            Logger.wsmE("处理定时器设置失败", e)
        }
    }

    /**
     * 处理WiFi扫描命令
     */
    private fun handleWifiScanCommand(jsonObject: JSONObject) {
        Logger.wsm("执行WiFi扫描命令")

        try {
            // 这里需要实现WiFi扫描逻辑

            Logger.wsm("WiFi扫描命令已执行")
        } catch (e: Exception) {
            Logger.wsmE("执行WiFi扫描命令失败", e)
        }
    }

    /**
     * 检查并保存默认开启的服务
     */
    private fun checkAndSaveDefaultServices() {
        try {
            Logger.wsm("检查默认开启的服务")

            // 检查LogStream服务是否默认开启
            checkAndSaveLogStreamService()

            // 检查4G切换服务是否默认开启
            // checkAndSave4GService() // TODO: 如果有4G切换服务的话

            Logger.wsm("默认服务检查完成")
        } catch (e: Exception) {
            Logger.wsmE("检查默认服务失败", e)
        }
    }

    /**
     * 检查并保存LogStream服务信息
     */
    private fun checkAndSaveLogStreamService() {
        try {
            val serviceId = "logstream_service"

            // 先检查现有的LogStream服务是否过期
            checkAndCleanExpiredLogStreamService(serviceId)

            // 检查LogStream服务是否启用
            val logStreamManager = ModuleManagerRegistry.getLogStreamManager()
            if (logStreamManager?.isStreamingActive() == true) {
                Logger.wsm("检测到LogStream服务已启用，保存服务信息")

                // 构建LogStream服务信息
                val serviceInfo = buildLogStreamServiceInfo()

                // 保存到ServiceInfoManager
                com.dspread.mdm.service.network.websocket.message.ServiceInfoManager.addOrUpdateService(serviceId, serviceInfo)

                Logger.wsm("LogStream服务信息已保存: $serviceInfo")
            } else {
                Logger.wsm("LogStream服务未启用，跳过保存")
            }
        } catch (e: Exception) {
            Logger.wsmE("检查LogStream服务失败", e)
        }
    }

    /**
     * 检查并清理过期的LogStream服务
     */
    private fun checkAndCleanExpiredLogStreamService(serviceId: String) {
        try {
            val serviceInfoManager = com.dspread.mdm.service.network.websocket.message.ServiceInfoManager
            val existingService = serviceInfoManager.getService(serviceId)

            if (existingService != null) {
                Logger.wsm("检查LogStream服务是否过期: $serviceId")

                // 获取服务的开始时间和period
                val beginDate = existingService.optString("beginDate", "")
                val period = existingService.optInt("period", 1) // 默认1小时

                if (beginDate.isNotEmpty()) {
                    val dateFormat = java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss", java.util.Locale.getDefault())
                    val startTime = dateFormat.parse(beginDate)?.time ?: 0L
                    val currentTime = System.currentTimeMillis()
                    val periodMillis = period * 60 * 60 * 1000L // 转换为毫秒

                    if (currentTime - startTime > periodMillis) {
                        Logger.wsm("LogStream服务已过期，自动清理: 开始时间=$beginDate, 期限=${period}小时")
                        serviceInfoManager.removeService(serviceId)
                        Logger.wsm("LogStream服务已从本地清除")
                    } else {
                        val remainingHours = (periodMillis - (currentTime - startTime)) / (60 * 60 * 1000L)
                        Logger.wsm("LogStream服务未过期，剩余时间: ${remainingHours}小时")
                    }
                } else {
                    Logger.wsmW("LogStream服务缺少开始时间，无法判断是否过期")
                }
            } else {
                Logger.wsm("本地无LogStream服务信息")
            }
        } catch (e: Exception) {
            Logger.wsmE("检查LogStream服务过期状态失败", e)
        }
    }

    /**
     * 构建LogStream服务信息
     */
    private fun buildLogStreamServiceInfo(): org.json.JSONObject {
        return try {
            val currentTime = System.currentTimeMillis()
            val dateFormat = java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss", java.util.Locale.getDefault())

            org.json.JSONObject().apply {
                put("taskId", "default_logstream")
                put("serviceId", "logstream_service")
                put("serviceName", "Log Stream")
                put("taskType", "05")
                put("command", "C03")
                put("stateDesc", "IMPLEMENTED")  // 表示服务已实施
                put("beginDate", dateFormat.format(java.util.Date(currentTime)))
                put("endDate", "9999-12-31 23:59:59")  // 长期有效
                put("period", "1")
                put("request_id", "")
                put("request_time", "")
            }
        } catch (e: Exception) {
            Logger.wsmE("构建LogStream服务信息失败", e)
            org.json.JSONObject()
        }
    }



    /**
     * 处理移动数据控制命令
     */
    private fun handleMobileDataCommand(jsonObject: JSONObject) {
        val data = getDataFromMessage(jsonObject) ?: return

        try {
            val enable = data.optBoolean("enable", true)
            Logger.wsm("${if (enable) "启用" else "禁用"}移动数据")

            // 这里需要实现移动数据控制逻辑
            // SystemControlApi.setMobileDataEnabled(context, enable)

            Logger.wsm("移动数据控制命令已执行")
        } catch (e: Exception) {
            Logger.wsmE("执行移动数据控制命令失败", e)
        }
    }

    /**
     * 处理系统更新命令
     */
    private fun handleSystemUpdateCommand(jsonObject: JSONObject) {
        Logger.wsm("执行系统更新命令")

        try {
            // 这里需要实现系统更新逻辑

            Logger.wsm("系统更新命令已执行")
        } catch (e: Exception) {
            Logger.wsmE("执行系统更新命令失败", e)
        }
    }

    /**
     * 处理应用安装命令
     */
    private fun handleAppInstallCommand(jsonObject: JSONObject) {
        val data = getDataFromMessage(jsonObject) ?: return

        try {
            val apkPath = data.optString("apk_path")
            Logger.wsm("安装应用: $apkPath")

            // 这里需要实现应用安装逻辑
            // AppManagerApi.installApkSilently(context, apkPath)

            Logger.wsm("应用安装命令已执行")
        } catch (e: Exception) {
            Logger.wsmE("执行应用安装命令失败", e)
        }
    }

    /**
     * 处理应用卸载命令
     */
    private fun handleAppUninstallCommand(jsonObject: JSONObject) {
        val data = getDataFromMessage(jsonObject) ?: return

        try {
            val packageName = data.optString("package_name")
            Logger.wsm("卸载应用: $packageName")

            // 这里需要实现应用卸载逻辑
            // AppManagerApi.uninstallAppSilently(context, packageName)

            Logger.wsm("应用卸载命令已执行")
        } catch (e: Exception) {
            Logger.wsmE("执行应用卸载命令失败", e)
        }
    }

    /**
     * 处理清除数据命令
     */
    private fun handleClearDataCommand(jsonObject: JSONObject) {
        val data = getDataFromMessage(jsonObject) ?: return

        try {
            val packageName = data.optString("package_name")
            Logger.wsm("清除应用数据: $packageName")

            // 这里需要实现清除数据逻辑
            // AppManagerApi.clearApplicationData(context, packageName)

            Logger.wsm("清除数据命令已执行")
        } catch (e: Exception) {
            Logger.wsmE("执行清除数据命令失败", e)
        }
    }

    /**
     * 处理强制停止应用命令
     */
    private fun handleForceStopCommand(jsonObject: JSONObject) {
        val data = getDataFromMessage(jsonObject) ?: return

        try {
            val packageName = data.optString("package_name")
            Logger.wsm("强制停止应用: $packageName")

            // 这里需要实现强制停止逻辑
            // AppManagerApi.forceStopApplication(context, packageName)

            Logger.wsm("强制停止应用命令已执行")
        } catch (e: Exception) {
            Logger.wsmE("执行强制停止应用命令失败", e)
        }
    }

    /**
     * 处理应用安装请求
     */
    private fun handleAppInstall(dataObject: JSONObject) {
        try {
            val taskId = dataObject.getString("taskId")
            val packageName = dataObject.getString("packageName")
            val apkName = dataObject.getString("apkName")
            val downloadUrl = dataObject.getString("downloadUrl")
            val apkMd5 = dataObject.getString("apkMd5")
            val versionName = dataObject.getString("versionName")
            val versionCode = dataObject.getLong("versionCode")

            Logger.wsm("开始安装应用: $apkName ($packageName)")

            // 启动安装服务
            AppInstallService.startInstall(
                context = context,
                taskId = taskId,
                packageName = packageName,
                apkName = apkName,
                downloadUrl = downloadUrl,
                apkMd5 = apkMd5,
                versionName = versionName,
                versionCode = versionCode.toInt()
            )

        } catch (e: Exception) {
            Logger.wsmE("处理应用安装请求失败", e)
        }
    }

    /**
     * 处理应用卸载请求
     */
    private fun handleAppUninstall(dataObject: JSONObject) {
        try {
            val taskId = dataObject.getString("taskId")
            val packageName = dataObject.getString("packageName")
            val apkName = dataObject.getString("apkName")

            Logger.wsm("开始卸载应用: $apkName ($packageName)")

            // 启动卸载服务
            AppUninstallService.startUninstall(
                context = context,
                taskId = taskId,
                packageName = packageName,
                apkName = apkName
            )

        } catch (e: Exception) {
            Logger.wsmE("处理应用卸载请求失败", e)
        }
    }

    // 日志流处理方法已移动到LogStreamWebSocketHandler

    /**
     * 处理启动日志流请求（已废弃，使用LogStreamWebSocketHandler）
     */
    @Deprecated("使用LogStreamWebSocketHandler.handleCallLogStream")
    private fun handleCallLogStream(dataObject: JSONObject) {
        try {
            Logger.wsm("处理CALL_LOG_STREAM请求")

            if (dataObject.has("param")) {
                val paramString = dataObject.optString("param")
                Logger.wsm("日志流参数: $paramString")

                try {
                    val paramJson = JSONObject(paramString)
                    val taskType = paramJson.optString("taskType", "")
                    val serviceId = paramJson.optString("serviceId", "")
                    val serviceName = paramJson.optString("serviceName", "")
                    val url = paramJson.optString("url", "")

                    Logger.wsm("启动日志流服务 - taskType: $taskType, serviceId: $serviceId")

                    // 启动日志流服务
                    startLogStreamService(paramJson)

                } catch (e: Exception) {
                    Logger.wsmE("解析日志流参数失败", e)
                }
            } else {
                Logger.wsmW("CALL_LOG_STREAM请求缺少param参数")
            }

        } catch (e: Exception) {
            Logger.wsmE("处理CALL_LOG_STREAM请求失败", e)
        }
    }

    /**
     * 处理关闭日志流请求
     */
    private fun handleCloseLogStream(dataObject: JSONObject) {
        try {
            Logger.wsm("处理CLOSE_LOG_STREAM请求")

            if (dataObject.has("param")) {
                val paramString = dataObject.optString("param")
                Logger.wsm("关闭日志流参数: $paramString")

                try {
                    val paramJson = JSONObject(paramString)
                    val taskType = paramJson.optString("taskType", "")
                    val serviceId = paramJson.optString("serviceId", "")
                    val command = paramJson.optString("command", "")

                    Logger.wsm("关闭日志流服务 - taskType: $taskType, serviceId: $serviceId")

                    // 根据taskType处理不同的关闭逻辑
                    if ("06" == taskType) {
                        Logger.wsm("CLOSE_LOG_STREAM taskType=06，执行服务重置")
                        // TODO: 实现服务重置逻辑
                        // RulebasedHandler.executeServiceReset(command)

                        // 删除服务ID相关的配置
                        // TODO: 实现删除服务配置逻辑
                    }

                    // 停止日志流服务
                    stopLogStreamService(paramJson)

                } catch (e: Exception) {
                    Logger.wsmE("解析关闭日志流参数失败", e)
                }
            } else {
                Logger.wsmW("CLOSE_LOG_STREAM请求缺少param参数")
                // 直接停止日志流服务
                stopLogStreamService(null)
            }

        } catch (e: Exception) {
            Logger.wsmE("处理CLOSE_LOG_STREAM请求失败", e)
        }
    }

    /**
     * 启动日志流服务
     */
    private fun startLogStreamService(paramJson: JSONObject?) {
        try {
            Logger.wsm("启动日志流服务")
            val logStreamManager = getLogStreamManager()

            logStreamManager?.let { manager ->
                GlobalScope.launch {
                    try {
                        // 检查是否已经在运行
                        if (manager.isStreamingActive()) {
                            Logger.wsm("日志流服务已在运行，先停止再重新启动")
                            manager.stopStreaming()
                        }

                        // 配置日志流参数
                        paramJson?.let { configureLogStream(manager, it) }

                        // 启动日志流
                        val result = manager.startStreaming()
                        if (result.isSuccess) {
                            Logger.wsm("日志流服务启动成功")
                        } else {
                            Logger.wsmE("日志流服务启动失败", result.exceptionOrNull())
                        }
                    } catch (e: Exception) {
                        Logger.wsmE("启动日志流服务异常", e)
                    }
                }
            } ?: Logger.wsmE("无法获取LogStreamManager实例")

        } catch (e: Exception) {
            Logger.wsmE("启动日志流服务失败", e)
        }
    }

    /**
     * 停止日志流服务
     * 立即响应关闭命令，确保任何时候都以收到指令为主
     */
    private fun stopLogStreamService(paramJson: JSONObject?) {
        try {
            Logger.wsm("停止日志流服务")
            val logStreamManager = getLogStreamManager()

            logStreamManager?.let {
                GlobalScope.launch(Dispatchers.Main.immediate) {
                    try {
                        val result = it.stopStreaming()
                        if (result.isSuccess) {
                            Logger.wsm("日志流服务停止成功")
                        } else {
                            Logger.wsmE("日志流服务停止失败", result.exceptionOrNull())
                        }
                    } catch (e: Exception) {
                        Logger.wsmE("停止日志流服务异常", e)
                    }
                }
            } ?: Logger.wsmE("无法获取LogStreamManager实例")

        } catch (e: Exception) {
            Logger.wsmE("停止日志流服务失败", e)
        }
    }

    /**
     * 获取LogStreamManager实例
     */
    private fun getLogStreamManager(): LogStreamManager? {
        return ModuleManagerRegistry.getLogStreamManager()
    }

    /**
     * 配置日志流参数
     */
    private fun configureLogStream(logStreamManager: LogStreamManager, paramJson: JSONObject) {
        try {
            // TODO: 根据参数配置日志流
            // 例如：设置上传URL、收集间隔等
            val url = paramJson.optString("url", "")
            val period = paramJson.optString("period", "1")
            val beginDate = paramJson.optString("beginDate", "")
            val endDate = paramJson.optString("endDate", "")

            Logger.wsm("配置日志流 - URL: $url, Period: $period")

            // 创建配置对象并更新
            // val config = LogStreamConfig(...)
            // logStreamManager.updateConfig(config)

        } catch (e: Exception) {
            Logger.wsmE("配置日志流参数失败", e)
        }
    }



    /**
     * 直接启动Remote View服务，避免消息传递问题
     */
    private suspend fun startRemoteViewDirectly(bucketPath: String) {
        try {
            Logger.wsm("直接启动Remote View服务")

            // 检查当前状态，避免重复启动
            val currentManager = remoteViewHandler.getCurrentManager()
            if (currentManager != null) {
                val currentStatus = currentManager.remoteViewStatus.value
                if (currentStatus == RemoteViewStatus.RUNNING || currentStatus == RemoteViewStatus.STARTING) {
                    Logger.remote("Remote View服务已在运行中，跳过重复启动")
                    return
                }

                // 如果状态不是STOPPED，先停止再重新启动
                if (currentStatus != RemoteViewStatus.STOPPED) {
                    Logger.remote("Remote View状态异常($currentStatus)，先停止再启动")
                    currentManager.stop()
                    delay(200) // 等待停止完成
                    currentManager.release()
                    remoteViewHandler.setCurrentManager(null)
                }
            }

            // 创建Remote View配置 - 使用动态获取的WebSocket URL
            val remoteWebSocketUrl = RemoteViewWebSocketManager.getRemoteViewWebSocketUrl(context)
            val config = RemoteViewConfig(
                websocketUrl = remoteWebSocketUrl,
                captureMode = RemoteViewCaptureMode.MEDIA_PROJECTION,
                bucketPath = bucketPath.takeIf { it.isNotEmpty() }
            )

            // 创建新的管理器
            val manager = RemoteViewManager(context, config).apply {
                // 设置事件监听
                onEvent = { event ->
                    when (event) {
                        is RemoteViewEvent.Started -> {
                            Logger.remote("Remote View服务启动成功")
                        }
                        is RemoteViewEvent.Stopped -> {
                            Logger.remote("Remote View服务已停止")
                        }
                        is RemoteViewEvent.Error -> {
                            Logger.remoteE("Remote View服务错误: ${event.message}", event.throwable)
                        }
                        else -> {
                            Logger.remote("Remote View事件: $event")
                        }
                    }
                }
            }

            // 初始化管理器
            val initResult = manager.initialize()
            if (initResult.isSuccess) {
                // 启动服务
                val startResult = manager.start()
                if (startResult.isSuccess) {
                    Logger.remote("Remote View服务启动成功")
                    // 保存管理器实例供停止时使用
                    remoteViewHandler.setCurrentManager(manager)
                } else {
                    Logger.remoteE("Remote View服务启动失败")
                    manager.release()
                }
            } else {
                Logger.remoteE("Remote View管理器初始化失败")
                manager.release()
            }

        } catch (e: Exception) {
            Logger.remoteE("直接启动Remote View服务异常", e)
        }
    }

    /**
     * 停止Remote View服务
     * 立即响应关闭命令，确保任何时候都以收到指令为主
     */
    private fun stopRemoteViewService() {
        try {
            // Logger.wsm("停止Remote View服务") // 减少重复日志

            // 使用立即执行的协程，确保快速响应
            GlobalScope.launch(Dispatchers.Main.immediate) {
                val manager = remoteViewHandler.getCurrentManager()
                if (manager != null) {
                    Logger.wsm("找到运行中的Remote View管理器，立即停止")

                    try {
                        // 使用BaseModuleManager提供的便捷方法
                        val stopResult = manager.stop()
                        if (stopResult.isSuccess) {
                            manager.release()
                            remoteViewHandler.setCurrentManager(null)
                            Logger.wsm("Remote View服务停止成功")
                        } else {
                            Logger.wsmE("Remote View服务停止失败")
                        }
                    } catch (e: Exception) {
                        Logger.wsmE("停止Remote View服务异常", e)
                        // 异常情况下也要清理管理器
                        try {
                            manager.release()
                            remoteViewHandler.setCurrentManager(null)
                        } catch (releaseException: Exception) {
                            Logger.wsmE("释放Remote View管理器异常", releaseException)
                        }
                    }
                } else {
                    Logger.wsmW("Remote View服务未运行，无需停止")
                }
            }

        } catch (e: Exception) {
            Logger.wsmE("停止Remote View服务异常", e)
        }
    }

    /**
     * 处理启动应用命令
     */
    private fun handleStartActivity(data: JSONObject?) {
        try {
            val packageName = data?.optString("packageName") ?: ""
            if (packageName.isBlank()) {
                Logger.wsmW("启动应用命令缺少packageName参数")
                return
            }

            Logger.wsm("启动应用: $packageName")

            // 检查应用是否已安装
            val packageManager = context.packageManager
            try {
                packageManager.getPackageInfo(packageName, 0)
                Logger.wsm("应用已安装，尝试启动: $packageName")

                // 获取应用的启动Intent
                val launchIntent = packageManager.getLaunchIntentForPackage(packageName)
                if (launchIntent != null) {
                    launchIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                    context.startActivity(launchIntent)
                    Logger.wsm("应用启动成功: $packageName")
                } else {
                    Logger.wsmW("无法获取应用启动Intent: $packageName")
                }

            } catch (e: PackageManager.NameNotFoundException) {
                Logger.wsmW("应用未安装: $packageName")
            }

        } catch (e: Exception) {
            Logger.wsmE("启动应用失败", e)
        }
    }
}
