package com.dspread.mdm.service.platform.collector

import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.os.BatteryManager
import android.os.Build
import android.net.wifi.WifiManager
import android.net.ConnectivityManager
import android.net.NetworkCapabilities
import android.os.StatFs
import android.os.Environment
import android.os.storage.StorageManager
// StorageStatsManager 需要 API 26+，使用反射调用
import android.app.ActivityManager
import android.net.wifi.WifiInfo
import com.dspread.mdm.service.platform.api.device.DeviceInfoApi
import com.dspread.mdm.service.platform.api.app.AppManagerApi
import com.dspread.mdm.service.modules.geofence.location.CellLocationManager
import com.dspread.mdm.service.modules.geofence.location.WifiLocationManager
import com.dspread.mdm.service.modules.geofence.location.GpsLocationManager
import com.dspread.mdm.service.utils.log.Logger
import org.json.JSONArray
import org.json.JSONObject
import java.text.SimpleDateFormat
import java.util.*

/**
 * 设备数据收集器
 * 负责收集各种设备信息用于WebSocket上报
 *
 * 优化特性：
 * - 缓存机制：避免频繁收集相同数据
 * - 异步收集：避免阻塞主线程
 * - 错误处理：收集失败时提供默认值
 * - 单例模式：避免重复实例化和资源浪费
 */
class DeviceDataCollector(private val context: Context) {

    private val deviceInfoApi by lazy { DeviceInfoApi(context) }
    private val appManagerApi by lazy { AppManagerApi(context) }

    // 缓存机制
    private var cachedAppInfo: JSONObject? = null
    private var cachedHardwareInfo: JSONArray? = null
    private var cachedWifiOptions: JSONArray? = null
    private var lastAppInfoUpdate = 0L
    private var lastHardwareInfoUpdate = 0L
    private var lastWifiOptionsUpdate = 0L

    companion object {
        private const val TAG = "DeviceDataCollector"

        // 缓存有效期（毫秒）
        private const val APP_INFO_CACHE_DURATION = 5 * 60 * 1000L // 5分钟
        private const val HARDWARE_INFO_CACHE_DURATION = 10 * 60 * 1000L // 10分钟
        private const val WIFI_OPTIONS_CACHE_DURATION = 2 * 60 * 1000L // 2分钟

        @Volatile
        private var INSTANCE: DeviceDataCollector? = null

        /**
         * 获取单例实例
         */
        private fun getInstance(context: Context): DeviceDataCollector {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: DeviceDataCollector(context.applicationContext).also {
                    INSTANCE = it
                    Logger.platform("DeviceDataCollector 单例实例已创建")
                }
            }
        }

        // ==================== 静态方法接口 ====================

        /**
         * 收集应用信息
         */
        fun collectAppInfo(context: Context, forceRefresh: Boolean = false): JSONObject {
            return getInstance(context).collectAppInfo(forceRefresh)
        }

        /**
         * 收集位置信息
         */
        fun collectLocationInfo(context: Context): JSONObject {
            return getInstance(context).collectLocationInfo()
        }

        /**
         * 清理缓存
         */
        fun clearCache(context: Context) {
            getInstance(context).clearCache()
        }

        /**
         * 清理所有缓存（静态方法）
         */
        fun clearAllCache() {
            INSTANCE?.clearCache()
        }

        // 其他方法暂时保持实例调用方式，避免参数不匹配问题
    }
    
    /**
     * 收集应用信息 (C0901)
     * 使用缓存机制提高性能
     */
    fun collectAppInfo(forceRefresh: Boolean = false): JSONObject {
        // 检查缓存是否有效
        val currentTime = System.currentTimeMillis()
        if (!forceRefresh && cachedAppInfo != null && (currentTime - lastAppInfoUpdate) < APP_INFO_CACHE_DURATION) {
            Logger.platform("使用缓存的应用信息")
            return cachedAppInfo!!
        }

        Logger.platform("重新收集应用信息${if (forceRefresh) "（强制刷新）" else ""}")

        return try {
            val apkInfoArray = JSONArray()
            val systemInfoObject = JSONObject()

            // 获取应用信息并实时验证
            val apps = appManagerApi.getInstalledApplications(includeSystemApps = true)

            apps.filter { app ->
                // 过滤逻辑：非系统应用 + 特殊系统应用
                (!app.isSystemApp)
            }.filter { app ->
                // 二次验证：确保应用真的存在（防止卸载过程中的缓存问题）
                val reallyExists = appManagerApi.isApplicationInstalled(app.packageName)
                if (!reallyExists) {
                    Logger.com("应用已被卸载，跳过: ${app.packageName}")
                }
                reallyExists
            }.forEach { app ->
                apkInfoArray.put(JSONObject().apply {
                    put("packName", app.packageName)
                    put("apkName", app.appName)
                    put("versionCode", app.versionCode)
                    put("versionName", app.versionName)
                    put("updateDate", SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault())
                        .format(Date(app.lastUpdateTime)))
                })
            }

            Logger.platform("应用信息: ${apkInfoArray.length()} 个应用")

            // 系统信息
            systemInfoObject.apply {
                put("androidVersion", Build.VERSION.RELEASE)
                put("buildNumber", Build.DISPLAY)
                put("aspl", Build.VERSION.SECURITY_PATCH)
            }

            val result = JSONObject().apply {
                put("apkInfo", apkInfoArray)
                put("sytemInfo", systemInfoObject)
            }

            // 更新缓存
            cachedAppInfo = result
            lastAppInfoUpdate = System.currentTimeMillis()

            result

        } catch (e: Exception) {
            Logger.platformE("收集应用信息失败", e)
            // 返回缓存数据或默认数据
            cachedAppInfo ?: createDefaultAppInfo()
        }
    }
    
    /**
     * 收集电池状态信息 (C0902)
     */
    fun collectBatteryStatus(): JSONObject {
        return try {
            // 使用广播方式获取电池信息
            val batteryIntent = context.registerReceiver(null, IntentFilter(Intent.ACTION_BATTERY_CHANGED))

            if (batteryIntent != null) {
                val level = batteryIntent.getIntExtra(BatteryManager.EXTRA_LEVEL, 100)
                val scale = batteryIntent.getIntExtra(BatteryManager.EXTRA_SCALE, 100)
                val health = batteryIntent.getIntExtra(BatteryManager.EXTRA_HEALTH, BatteryManager.BATTERY_HEALTH_GOOD)
                val temperature = batteryIntent.getIntExtra(BatteryManager.EXTRA_TEMPERATURE, 250) // 默认25.0度
                val status = batteryIntent.getIntExtra(BatteryManager.EXTRA_STATUS, -1)
                val plugged = batteryIntent.getIntExtra(BatteryManager.EXTRA_PLUGGED, 0)

                // 计算电池百分比
                val batteryLevel = (level * 100) / scale

                // 判断充电状态
                val isCharging = (status == BatteryManager.BATTERY_STATUS_CHARGING) ||
                               (status == BatteryManager.BATTERY_STATUS_FULL) ||
                               (plugged != 0)

                val isLowBattery = batteryLevel <= 15
                val outOfBattery = batteryLevel <= 1

                JSONObject().apply {
                    put("batteryLife", batteryLevel)
                    put("batteryHealth", health)
                    put("temprature", String.format("%.1f", temperature / 10.0)) // 温度需要除以10
                    put("isCharging", if (isCharging) "1" else "0")
                    put("isLowBattery", if (isLowBattery) "1" else "0")
                    put("powerOff", "0")
                    put("outOfBattery", if (outOfBattery) "1" else "0")
                }
            } else {
                // 如果无法获取电池信息，使用默认值
                getDefaultBatteryInfo()
            }

        } catch (e: Exception) {
            Logger.platformE("收集电池状态失败", e)
            getDefaultBatteryInfo()
        }
    }

    private fun getDefaultBatteryInfo(): JSONObject {
        return JSONObject().apply {
            put("batteryLife", 100)
            put("batteryHealth", 2) // BATTERY_HEALTH_GOOD
            put("temprature", "25.0")
            put("isCharging", "0")
            put("isLowBattery", "0")
            put("powerOff", "0")
            put("outOfBattery", "0")
        }
    }
    
    /**
     * 收集硬件信息
     */
    fun collectHardwareInfo(): JSONArray {
        return try {
            val hardwareArray = JSONArray()

            // CPU使用率 (简化实现)
            hardwareArray.put(JSONObject().apply {
                put("name", "CPU Usage")
                put("desc", "cpu:0.50") // 简化实现，实际应该读取/proc/stat
            })

            // 存储使用状态
            val storageInfo = getStorageInfo()
            hardwareArray.put(JSONObject().apply {
                put("name", "storage_usage_status")
                put("desc", storageInfo.first)
                put("state", "1")
            })

            // 内存信息
            val memoryInfo = getMemoryInfo()
            hardwareArray.put(JSONObject().apply {
                put("name", "total_memory")
                put("desc", memoryInfo.first)
                put("state", if (memoryInfo.second > 0) "1" else "0")
            })
            
            // Android版本
            hardwareArray.put(JSONObject().apply {
                put("name", "androidVersion")
                put("desc", Build.VERSION.RELEASE)
                put("state", "1")
            })
            
            // 网络状态
            val networkStatus = getNetworkStatus()
            hardwareArray.put(JSONObject().apply {
                put("name", "network_status")
                put("desc", networkStatus)
                put("state", "1")
            })
            
            // OS版本
            hardwareArray.put(JSONObject().apply {
                put("name", "OSVer")
                put("desc", Build.VERSION.RELEASE)
                put("state", "1")
            })
            
            hardwareArray
            
        } catch (e: Exception) {
            Logger.platformE("收集硬件信息失败", e)
            JSONArray()
        }
    }
    
    /**
     * 收集位置信息 - 使用新的位置管理器
     */
    fun collectLocationInfo(): JSONObject {
        return try {
            val locationInfo = JSONObject()

            // GPS信息 - 使用GpsLocationManager
            val gpsInfo = GpsLocationManager.getGpsLocation(context)
            locationInfo.put("gps", gpsInfo)

            // 基站信息 - 使用CellLocationManager
            val baseSiteInfo = collectBaseSiteInfo()
            locationInfo.put("baseSite", baseSiteInfo)

            // WiFi信息 - 使用WifiLocationManager
            val wifiInfo = collectWifiInfo()
            locationInfo.put("wifi", wifiInfo)
            
            // 地址信息
            locationInfo.put("address", "无法获取")
            
            locationInfo
            
        } catch (e: Exception) {
            Logger.platformE("收集位置信息失败", e)
            JSONObject()
        }
    }
    
    /**
     * 收集GPS信息 - 专门用于地理围栏上报
     */
    fun collectGpsInfo(): JSONObject {
        return try {
            // 直接使用GpsLocationManager获取GPS信息
            GpsLocationManager.getGpsLocation(context)
        } catch (e: Exception) {
            Logger.platformE("收集GPS信息失败", e)
            // 返回默认GPS信息
            JSONObject().apply {
                put("longitude", "-999")
                put("latitude", "-999")
            }
        }
    }
    
    /**
     * 收集基站信息 - 使用CellLocationManager
     */
    fun collectBaseSiteInfo(): JSONObject {
        return try {
            val cellLocationManager = CellLocationManager(context)
            val cellJson = cellLocationManager.getLocationJsonCell()

            if (cellJson != null) {
                cellJson
            } else {
                // 如果获取失败，使用默认值
                getDefaultBaseSiteInfo()
            }
        } catch (e: Exception) {
            Logger.platformW("收集基站信息失败（可能是网络未就绪），使用默认值: ${e.message}")
            getDefaultBaseSiteInfo()
        }
    }

    /**
     * 获取默认基站信息
     */
    private fun getDefaultBaseSiteInfo(): JSONObject {
        return JSONObject().apply {
            put("MCC", "310")
            put("MNC", "260")
            put("CID", "47108")
            put("LAC", "8514")
            put("DBM", "-1")
            put("IMSI", "310260000000000")
            put("ICCID", "89860318640220133897")
        }
    }
    
    /**
     * 获取存储信息 -  HardwareInfo.java
     */
    private fun getStorageInfo(): Pair<String, Long> {
        return try {
            val storageManager = context.getSystemService(Context.STORAGE_SERVICE) as StorageManager
            val version = Build.VERSION.SDK_INT
            var totalBytes = 0L
            var usedBytes = 0L

            if (version >= Build.VERSION_CODES.M) { // Android 6.0+
                try {
                    val getVolumes = StorageManager::class.java.getDeclaredMethod("getVolumes")
                    val volumeInfoList = getVolumes.invoke(storageManager) as List<*>

                    for (volumeInfo in volumeInfoList) {
                        val getType = volumeInfo!!::class.java.getField("type")
                        val type = getType.getInt(volumeInfo)

                        if (type == 1) { // TYPE_PRIVATE
                            var totalSize = 0L

                            // 获取内置存储总大小
                            if (version >= Build.VERSION_CODES.O) { // Android 8.0+
                                val getFsUuid = volumeInfo::class.java.getDeclaredMethod("getFsUuid")
                                val fsUuid = getFsUuid.invoke(volumeInfo) as String?
                                totalSize = getTotalStorageSize(fsUuid)
                            } else if (version >= Build.VERSION_CODES.N_MR1) { // Android 7.1.1+
                                val getPrimaryStorageSize = StorageManager::class.java.getMethod("getPrimaryStorageSize")
                                totalSize = getPrimaryStorageSize.invoke(storageManager) as Long
                            }

                            if (totalSize > 0) {
                                totalBytes = totalSize
                                // 计算已使用空间
                                val stat = StatFs(Environment.getDataDirectory().path)
                                val availableBytes = stat.availableBlocksLong * stat.blockSizeLong
                                usedBytes = totalSize - availableBytes
                                break
                            }
                        }
                    }
                } catch (e: Exception) {
                    Logger.platformE("获取存储信息失败，使用备用方法", e)
                    return getStorageInfoFallback()
                }
            } else {
                return getStorageInfoFallback()
            }

            // 格式化存储信息
            val unit = if (version < Build.VERSION_CODES.M) 1024 else 1000
            val usedMemory = getStorageUnit(usedBytes, unit)
            val totalMemory = getStorageUnit(totalBytes, unit)

            Pair("$usedMemory/$totalMemory", totalBytes)

        } catch (e: Exception) {
            Logger.platformE("获取存储信息失败", e)
            getStorageInfoFallback()
        }
    }

    /**
     * 备用存储信息获取方法
     */
    private fun getStorageInfoFallback(): Pair<String, Long> {
        return try {
            Logger.platformW("🚨 使用备用存储信息获取方法")
            val stat = StatFs(Environment.getDataDirectory().path)
            val totalBytes = stat.blockCountLong * stat.blockSizeLong
            val availableBytes = stat.availableBlocksLong * stat.blockSizeLong
            val usedBytes = totalBytes - availableBytes

            val usedGB = String.format(" %.2fGB ", usedBytes / (1024.0 * 1024.0 * 1024.0))
            val totalGB = String.format(" %.2fGB ", totalBytes / (1024.0 * 1024.0 * 1024.0))

            Pair("$usedGB/$totalGB", totalBytes)
        } catch (e: Exception) {
            Logger.platformE("备用存储信息获取失败", e)
            Pair("0.00GB / 0.00GB", 0L)
        }
    }

    /**
     * 获取总存储大小 - 使用 StorageStatsManager (API 26+) 和 StatFs 备用方案
     */
    private fun getTotalStorageSize(fsUuid: String? = null): Long {
        return try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                // Android 8.0+ 使用 StorageStatsManager (通过反射调用)
                val storageStatsManager = context.getSystemService(Context.STORAGE_STATS_SERVICE)

                val id = if (fsUuid == null) {
                    StorageManager.UUID_DEFAULT
                } else {
                    java.util.UUID.fromString(fsUuid)
                }

                // 使用反射调用 getTotalBytes
                val getTotalBytesMethod = storageStatsManager::class.java.getMethod("getTotalBytes", java.util.UUID::class.java)
                getTotalBytesMethod.invoke(storageStatsManager, id) as Long
            } else {
                // Android 7.1及以下使用 StatFs
                val statFs = StatFs(Environment.getDataDirectory().path)
                statFs.totalBytes
            }
        } catch (e: Exception) {
            Logger.platformE("获取总存储大小失败，使用 StatFs 备用方案", e)
            try {
                val statFs = StatFs(Environment.getDataDirectory().path)
                statFs.totalBytes
            } catch (e2: Exception) {
                Logger.platformE("StatFs 备用方案也失败", e2)
                0L
            }
        }
    }

    /**
     * 格式化存储大小为GB
     */
    private fun formatStorageSize(bytes: Long): String {
        return String.format("%.2fGB", bytes / (1024.0 * 1024.0 * 1024.0))
    }

    /**
     * 格式化存储单位
     */
    private fun getStorageUnit(bytes: Long, unit: Int): String {
        val gb = bytes / (unit * unit * unit.toDouble())
        return String.format(" %.2fGB ", gb)
    }

    /**
     * 收集WiFi信息 - 兼容性处理
     */
    fun collectWifiInfo(): JSONObject {
        return try {
            // 检查WiFi权限
            if (context.checkSelfPermission(android.Manifest.permission.ACCESS_WIFI_STATE) !=
                android.content.pm.PackageManager.PERMISSION_GRANTED) {
                return getDefaultWifiInfo()
            }

            // 检查WiFi连接状态
            val currentWifiInfo = getCurrentWifiInfo()
            if (currentWifiInfo == null) {
                return getDefaultWifiInfo()
            }

            val ssid = getWifiSSIDFromInfo(currentWifiInfo)
            val macAddress = getWifiMacAddress(currentWifiInfo)
            val ip = intToIp(currentWifiInfo.ipAddress)

            JSONObject().apply {
                put("SSID", ssid)
                put("IP", ip)
                put("MAC", macAddress)
                put("SSTH", currentWifiInfo.rssi.toString())
                put("BSSID", currentWifiInfo.bssid ?: "")
            }
        } catch (e: SecurityException) {
            getDefaultWifiInfo()
        } catch (e: Exception) {
            Logger.wsmE("收集WiFi信息失败", e)
            getDefaultWifiInfo()
        }
    }

    /**
     * 获取内存信息 - 读取 /proc/meminfo
     */
    private fun getMemoryInfo(): Pair<String, Long> {
        return try {
            val memInfoFile = "/proc/meminfo"
            var totalMemory = 0L
            var freeMemory = 0L
            var buffers = 0L
            var cached = 0L

            val reader = java.io.BufferedReader(java.io.FileReader(memInfoFile))
            var line: String?

            while (reader.readLine().also { line = it } != null) {
                val parts = line!!.split("\\s+".toRegex())
                if (parts.size >= 2) {
                    val value = parts[1].toLongOrNull() ?: 0L
                    when {
                        line!!.startsWith("MemTotal:") -> totalMemory = value * 1024 // kB to bytes
                        line!!.startsWith("MemFree:") -> freeMemory = value * 1024
                        line!!.startsWith("Buffers:") -> buffers = value * 1024
                        line!!.startsWith("Cached:") -> cached = value * 1024
                    }
                }
            }
            reader.close()

            // 计算已使用内存
            val usedMemory = totalMemory - freeMemory - buffers - cached

            // 格式化为GB
            val totalMemStr = String.format(" %.2fGB ", totalMemory / (1024.0 * 1024.0 * 1024.0))
            val usedMemStr = String.format(" %.2fGB ", usedMemory / (1024.0 * 1024.0 * 1024.0))

//            Logger.platform("内存总容量为：$totalMemStr")
//            Logger.platform("内存使用量为：$usedMemStr")

            Pair("$usedMemStr/$totalMemStr", totalMemory)

        } catch (e: Exception) {
            Logger.platformE("读取内存信息失败，使用备用方法", e)
            getMemoryInfoFallback()
        }
    }

    /**
     * 备用内存信息获取方法
     */
    private fun getMemoryInfoFallback(): Pair<String, Long> {
        return try {
            Logger.platformW("🚨 使用备用内存信息获取方法")
            val activityManager = context.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
            val memInfo = ActivityManager.MemoryInfo()
            activityManager.getMemoryInfo(memInfo)

            val totalMemory = memInfo.totalMem
            val availableMemory = memInfo.availMem
            val usedMemory = totalMemory - availableMemory

            val totalMemStr = String.format(" %.2fGB ", totalMemory / (1024.0 * 1024.0 * 1024.0))
            val usedMemStr = String.format(" %.2fGB ", usedMemory / (1024.0 * 1024.0 * 1024.0))

            Logger.platform("💾 备用方法内存信息: $usedMemStr/$totalMemStr")
            Pair("FALLBACK_$usedMemStr/$totalMemStr", totalMemory)  // 添加标识符
        } catch (e: Exception) {
            Logger.platformE("备用内存信息获取失败", e)
            Pair("ERROR_0.00GB / 0.00GB", 0L)
        }
    }

    /**
     * 获取当前WiFi信息
     * 只有在WiFi真正连接时才返回WifiInfo，否则返回null
     */
    private fun getCurrentWifiInfo(): WifiInfo? {
        return try {
            val connectivityManager = context.applicationContext.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
            val wifiManager = context.applicationContext.getSystemService(Context.WIFI_SERVICE) as WifiManager

            @Suppress("DEPRECATION")
            val wifiNetworkInfo = connectivityManager.getNetworkInfo(ConnectivityManager.TYPE_WIFI)

            if (wifiNetworkInfo?.isConnected == true) {
                wifiManager.connectionInfo
            } else {
                null
            }
        } catch (e: Exception) {
            Logger.wsmE("获取当前WiFi信息失败", e)
            null
        }
    }

    /**
     * 从WifiInfo获取SSID - 兼容性处理
     */
    private fun getWifiSSIDFromInfo(wifiInfo: WifiInfo): String {
        return try {
            if (Build.VERSION.SDK_INT < Build.VERSION_CODES.KITKAT) {
                wifiInfo.ssid ?: "AndroidWifi"
            } else {
                val cleaned = wifiInfo.ssid?.replace("\"", "")
                if (cleaned.isNullOrEmpty() || cleaned == "<unknown ssid>") {
                    "AndroidWifi"
                } else {
                    cleaned
                }
            }
        } catch (e: Exception) {
            Logger.wsmE("从WifiInfo获取SSID失败", e)
            "AndroidWifi"
        }
    }

    /**
     * 获取WiFi SSID - 兼容性处理
     */
    private fun getWifiSSID(): String {
        return try {
            val apiLevel = Build.VERSION.SDK_INT
            Logger.platform("getWifiSSID: API: $apiLevel")

            when {
                apiLevel <= Build.VERSION_CODES.O || apiLevel >= 28 -> {
                    // Android 8.0及以下，或Android 9.0及以上
                    val wifiManager = context.applicationContext.getSystemService(Context.WIFI_SERVICE) as WifiManager
                    val wifiInfo = wifiManager.connectionInfo

                    if (apiLevel < Build.VERSION_CODES.KITKAT) {
                        Logger.wsmI("getWifiSSID: API < KITKAT, 原始SSID: '${wifiInfo.ssid}'")
                        val result = wifiInfo.ssid ?: "FALLBACK_AndroidWifi"
                        Logger.wsmI("getWifiSSID: 最终结果: '$result'")
                        result
                    } else {
                        Logger.wsmI("getWifiSSID: API >= KITKAT, 原始SSID: '${wifiInfo.ssid}'")
                        val cleaned = wifiInfo.ssid?.replace("\"", "")
                        val result = if (cleaned.isNullOrEmpty() || cleaned == "<unknown ssid>") {
                            "FALLBACK_AndroidWifi"
                        } else {
                            cleaned
                        }
                        Logger.wsmI("getWifiSSID: 清理后: '$cleaned', 最终结果: '$result'")
                        result
                    }
                }
                apiLevel == Build.VERSION_CODES.O_MR1 -> {
                    // Android 8.1
                    val connectivityManager = context.applicationContext.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
                    val networkInfo = connectivityManager.activeNetworkInfo

                    if (networkInfo?.isConnected == true && networkInfo.extraInfo != null) {
                        Logger.platform("getWifiSSID: getExtraInfo: ${networkInfo.extraInfo}")
                        networkInfo.extraInfo.replace("\"", "")
                    } else {
                        "AndroidWifi"
                    }
                }
                else -> "AndroidWifi"
            }
        } catch (e: Exception) {
            Logger.platformE("获取WiFi SSID失败", e)
            "AndroidWifi"
        }
    }

    /**
     * 获取WiFi MAC地址 - 处理不同Android版本
     */
    private fun getWifiMacAddress(wifiInfo: WifiInfo): String {
        return try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                // Android 7.0+ 需要通过网络接口获取
                val hardwareMac = getMacFromHardware()
                if (hardwareMac == "02:15:b2:00:00:00" || hardwareMac.contains("00:00:00")) {
                    "02:15:b2:00:00:00"
                } else {
                    hardwareMac
                }
            } else {
                // Android 7.0以下可以直接获取
                if (wifiInfo.macAddress.isNullOrEmpty() || wifiInfo.macAddress.contains("00:00:00")) {
                    "02:15:b2:00:00:00"
                } else {
                    wifiInfo.macAddress
                }
            }
        } catch (e: Exception) {
            Logger.wsmE("获取WiFi MAC地址失败", e)
            "02:15:b2:00:00:00"
        }
    }

    /**
     * 从硬件接口获取MAC地址 - 
     */
    private fun getMacFromHardware(): String {
        return try {
            val interfaces = java.util.Collections.list(java.net.NetworkInterface.getNetworkInterfaces())
            for (intf in interfaces) {
                if (!intf.name.equals("wlan0", ignoreCase = true)) {
                    continue
                }
                val mac = intf.hardwareAddress
                if (mac == null) return "02:15:b2:00:00:00"

                val buf = StringBuilder()
                for (idx in mac.indices) {
                    buf.append(String.format("%02X:", mac[idx]))
                }
                if (buf.isNotEmpty()) {
                    buf.deleteCharAt(buf.length - 1)
                }
                return buf.toString()
            }
            "02:15:b2:00:00:00"
        } catch (e: Exception) {
            Logger.platformE("从硬件获取MAC地址失败", e)
            "02:15:b2:00:00:00"
        }
    }

    private fun getDefaultWifiInfo(): JSONObject {
        return JSONObject().apply {
            put("SSID", "AndroidWifi")
            put("IP", "*********")
            put("MAC", "02:15:b2:00:00:00")
            put("SSTH", "-50")
            put("BSSID", "")
        }
    }
    
    /**
     * 收集WiFi选项列表 - 使用WifiLocationManager
     */
    fun collectWifiOptions(): JSONArray {
        return try {
            // 检查WiFi权限
            if (context.checkSelfPermission(android.Manifest.permission.ACCESS_WIFI_STATE) !=
                android.content.pm.PackageManager.PERMISSION_GRANTED) {
                return getDefaultWifiOptions()
            }

            val wifiLocationManager = WifiLocationManager.getInstance(context)

            // 尝试获取WiFi扫描结果
            val wifiJsonArray = wifiLocationManager.getLocationJsonWifi()
            if (wifiJsonArray != null && wifiJsonArray.length() > 0) {
                return wifiJsonArray
            }

            // 如果扫描结果为空，尝试获取当前连接的WiFi
            val currentWifiJson = wifiLocationManager.getLocationJsonWifiFirst()
            if (currentWifiJson != null) {
                val wifiArray = JSONArray()
                val wifiOption = JSONObject().apply {
                    put("SSID", currentWifiJson.optString("SSID", "AndroidWifi"))
                    put("SSTH", currentWifiJson.optString("SSTH", "-50"))
                }
                wifiArray.put(wifiOption)
                return wifiArray
            }

            // 都失败了，使用默认值
            getDefaultWifiOptions()

        } catch (e: SecurityException) {
            getDefaultWifiOptions()
        } catch (e: Exception) {
            Logger.platformE("收集WiFi选项失败", e)
            getDefaultWifiOptions()
        }
    }

    private fun getDefaultWifiOptions(): JSONArray {
        return JSONArray().apply {
            put(JSONObject().apply {
                put("SSID", "AndroidWifi")
                put("SSTH", "-50")
            })
        }
    }
    
    /**
     * 获取网络状态
     */
    private fun getNetworkStatus(): String {
        return try {
            val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
            val network = connectivityManager.activeNetwork
            val capabilities = connectivityManager.getNetworkCapabilities(network)
            
            when {
                capabilities?.hasTransport(NetworkCapabilities.TRANSPORT_WIFI) == true -> "wifi"
                capabilities?.hasTransport(NetworkCapabilities.TRANSPORT_CELLULAR) == true -> "mobile"
                else -> "none"
            }
        } catch (e: Exception) {
            "wifi"
        }
    }
    
    /**
     * 将IP地址从int转换为字符串
     */
    private fun intToIp(ip: Int): String {
        return String.format("%d.%d.%d.%d",
            ip and 0xff,
            ip shr 8 and 0xff,
            ip shr 16 and 0xff,
            ip shr 24 and 0xff)
    }

    // ==================== 缓存管理和默认数据 ====================

    /**
     * 清理所有缓存
     */
    fun clearCache() {
        cachedAppInfo = null
        cachedHardwareInfo = null
        cachedWifiOptions = null
        lastAppInfoUpdate = 0L
        lastHardwareInfoUpdate = 0L
        lastWifiOptionsUpdate = 0L
        Logger.platform("设备数据缓存已清理")
    }

    /**
     * 清理应用信息缓存（在应用安装/卸载后调用）
     */
    fun clearAppInfoCache() {
        cachedAppInfo = null
        lastAppInfoUpdate = 0L
        Logger.platform("应用信息缓存已清理")
    }

    /**
     * 应用状态变化通知（安装/卸载后调用）
     * 立即刷新缓存并返回最新数据
     */
    fun onAppStateChanged(action: String, packageName: String): JSONObject {
        Logger.platform("应用状态变化: $action - $packageName")
        return collectAppInfo(forceRefresh = true)
    }

    /**
     * 创建默认应用信息
     */
    private fun createDefaultAppInfo(): JSONObject {
        return JSONObject().apply {
            put("apkInfo", JSONArray().apply {
                put(JSONObject().apply {
                    put("packName", "com.dspread.mdm.service")
                    put("apkName", "Smart MDM Service")
                    put("versionCode", 1)
                    put("versionName", "1.0")
                    put("updateDate", SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault())
                        .format(Date()))
                })
            })
            put("sytemInfo", JSONObject().apply {
                put("androidVersion", Build.VERSION.RELEASE)
                put("buildNumber", Build.DISPLAY)
                put("aspl", Build.VERSION.SECURITY_PATCH)
            })
        }
    }

    /**
     * 获取缓存状态信息
     */
    fun getCacheStatus(): String {
        val currentTime = System.currentTimeMillis()
        return buildString {
            append("缓存状态: ")
            append("应用信息=${if (cachedAppInfo != null && (currentTime - lastAppInfoUpdate) < APP_INFO_CACHE_DURATION) "有效" else "无效"}, ")
            append("硬件信息=${if (cachedHardwareInfo != null && (currentTime - lastHardwareInfoUpdate) < HARDWARE_INFO_CACHE_DURATION) "有效" else "无效"}, ")
            append("WiFi选项=${if (cachedWifiOptions != null && (currentTime - lastWifiOptionsUpdate) < WIFI_OPTIONS_CACHE_DURATION) "有效" else "无效"}")
        }
    }
}
