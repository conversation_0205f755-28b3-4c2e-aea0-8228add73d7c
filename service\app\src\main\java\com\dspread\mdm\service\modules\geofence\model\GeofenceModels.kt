package com.dspread.mdm.service.modules.geofence.model

import android.location.Location
import com.dspread.mdm.service.constants.Constants.ModuleConstants
import org.json.JSONObject
import kotlin.math.*

/**
 * 地理围栏配置数据类
 */
data class GeofenceConfig(
    val latitude: Double,                    // 围栏中心纬度
    val longitude: Double,                   // 围栏中心经度
    val radius: Float,                       // 围栏半径（米）
    val lockMinutes: Int,                    // 锁定倒计时（分钟）
    val wipeData: Boolean,                   // 是否执行数据擦除
    val wipeMins: Int,                       // 数据擦除倒计时（分钟）
    val roamingTag: String,                  // 漫游标签
    val storeId: String = "",                // 商店ID（用于蓝牙信标）
    val storeSsid: String = "",              // 商店WiFi SSID
    val storeIp: String = "",                // 商店WiFi IP地址
    val beaconEnabled: Boolean = true,       // 是否启用蓝牙信标检测
    val wifiAssistEnabled: Boolean = true,   // 是否启用WiFi辅助定位
    val gpsEnabled: Boolean = true           // 是否启用GPS定位
) {
    
    /**
     * 检查配置是否有效
     */
    fun isValid(): Boolean {
        return latitude in -90.0..90.0 && 
               longitude in -180.0..180.0 && 
               radius > 0 &&
               lockMinutes > 0
    }
    
    /**
     * 是否处于漫游状态
     */
    fun isRoaming(): Boolean {
        return roamingTag == "1"
    }
    
    /**
     * 转换为JSON对象
     */
    fun toJson(): JSONObject {
        return JSONObject().apply {
            put("latitude", latitude)
            put("longitude", longitude)
            put("lockMeter", radius)
            put("lockMin", lockMinutes)
            put("wipeData", if (wipeData) "1" else "0")
            put("wipeMins", wipeMins)
            put("roamingTag", roamingTag)
            put("storeId", storeId)
            put("storeSsid", storeSsid)
            put("storeIp", storeIp)
        }
    }
    
    companion object {
        /**
         * 从JSON对象创建配置
         */
        fun fromJson(json: JSONObject): GeofenceConfig? {
            return try {
                GeofenceConfig(
                    latitude = json.getDouble("latitude"),
                    longitude = json.getDouble("longitude"),
                    radius = json.optString("lockMeter", "100").toFloat(),
                    lockMinutes = json.optInt("lockMin", 5),
                    wipeData = json.optString("wipeData", "0") == "1",
                    wipeMins = json.optInt("wipeMins", 30),
                    roamingTag = json.optString("roamingTag", "0"),
                    storeId = json.optString("storeId", ""),
                    storeSsid = json.optString("storeSsid", ""),
                    storeIp = json.optString("storeIp", "")
                )
            } catch (e: Exception) {
                null
            }
        }
    }
}

/**
 * 地理围栏状态枚举
 */
enum class GeofenceStatus(val value: Int, val description: String) {
    IN_ZONE(ModuleConstants.IN_ZONE, "在围栏内"),
    OUT_OF_ZONE(ModuleConstants.OUT_OF_ZONE, "在围栏外"),
    LOCK_SCREEN(ModuleConstants.LOCK_SCREEN, "锁定屏幕"),
    WIPE_DATA(ModuleConstants.WIPE_DATA, "擦除数据"),
    ROAMING(ModuleConstants.ROAMING, "漫游状态");
    
    companion object {
        fun fromValue(value: Int): GeofenceStatus {
            return GeofenceStatus.entries.find { it.value == value } ?: OUT_OF_ZONE
        }
    }
}

/**
 * 安全措施类型枚举
 */
enum class SecurityActionType(val description: String) {
    SHOW_WARNING("显示警告"),
    LOCK_SCREEN("锁定屏幕"),
    WIPE_DATA("擦除数据"),
    REBOOT("重启设备"),
    DISABLE_FEATURES("禁用功能");
}

/**
 * 安全措施数据类
 */
data class SecurityAction(
    val type: SecurityActionType,
    val delaySeconds: Int = 0,              // 延迟执行时间（秒）
    val message: String = "",               // 警告消息
    val persistent: Boolean = false,        // 是否持久化
    val parameters: Map<String, Any> = emptyMap() // 额外参数
)

/**
 * 位置信息数据类
 */
data class LocationInfo(
    val latitude: Double,
    val longitude: Double,
    val accuracy: Float,
    val timestamp: Long,
    val provider: String,                   // GPS, NETWORK, PASSIVE
    val altitude: Double = 0.0,
    val bearing: Float = 0f,
    val speed: Float = 0f
) {
    
    /**
     * 转换为Android Location对象
     */
    fun toLocation(): Location {
        return Location(provider).apply {
            this.latitude = <EMAIL>
            this.longitude = <EMAIL>
            this.accuracy = <EMAIL>
            this.time = <EMAIL>
            this.altitude = <EMAIL>
            this.bearing = <EMAIL>
            this.speed = <EMAIL>
        }
    }
    
    /**
     * 计算与目标位置的距离（米）
     */
    fun distanceTo(target: LocationInfo): Double {
        return GeofenceUtils.calculateDistance(
            latitude,
            longitude,
            target.latitude,
            target.longitude
        )
    }

    /**
     * 计算与地理围栏中心的距离
     */
    fun distanceToGeofence(config: GeofenceConfig): Double {
        return GeofenceUtils.calculateDistance(
            latitude,
            longitude,
            config.latitude,
            config.longitude
        )
    }
    
    companion object {
        /**
         * 从Android Location对象创建
         */
        fun fromLocation(location: Location): LocationInfo {
            return LocationInfo(
                latitude = location.latitude,
                longitude = location.longitude,
                accuracy = location.accuracy,
                timestamp = location.time,
                provider = location.provider ?: "unknown",
                altitude = location.altitude,
                bearing = location.bearing,
                speed = location.speed
            )
        }
    }
}

/**
 * 蓝牙信标信息数据类
 */
data class BeaconInfo(
    val uuid: String,                       // 信标UUID
    val major: Int,                         // Major值
    val minor: Int,                         // Minor值
    val rssi: Int,                          // 信号强度
    val distance: Double,                   // 估算距离
    val timestamp: Long,                    // 扫描时间戳
    val manufacturerData: ByteArray? = null // 制造商数据
) {
    
    /**
     * 检查是否匹配指定的Store ID
     */
    fun matchesStoreId(storeId: String): Boolean {
        return manufacturerData?.let { data ->
            val dataString = String(data)
            dataString.contains("Store:$storeId")
        } == true
    }
    
    /**
     * 信号强度是否足够强（距离足够近）
     */
    fun isSignalStrong(): Boolean {
        return rssi > -70 && distance < 10.0 // 10米内且信号强度好
    }
    
    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false
        
        other as BeaconInfo
        
        if (uuid != other.uuid) return false
        if (major != other.major) return false
        if (minor != other.minor) return false
        
        return true
    }
    
    override fun hashCode(): Int {
        var result = uuid.hashCode()
        result = 31 * result + major
        result = 31 * result + minor
        return result
    }
}

/**
 * WiFi辅助定位信息数据类
 */
data class WifiAssistInfo(
    val ssid: String,                       // WiFi SSID
    val bssid: String,                      // WiFi BSSID (MAC地址)
    val ipAddress: String,                  // IP地址
    val rssi: Int,                          // 信号强度
    val frequency: Int,                     // 频率
    val timestamp: Long                     // 扫描时间戳
) {
    
    /**
     * 检查是否匹配指定的商店WiFi
     */
    fun matchesStoreWifi(storeSsid: String, storeIp: String): Boolean {
        return ssid == storeSsid && ipAddress == storeIp
    }
    
    /**
     * 信号强度是否足够强
     */
    fun isSignalStrong(): Boolean {
        return rssi > -60 // 信号强度阈值
    }
}

/**
 * 地理围栏事件数据类
 */
data class GeofenceEvent(
    val eventType: GeofenceEventType,
    val timestamp: Long,
    val location: LocationInfo?,
    val previousStatus: GeofenceStatus,
    val currentStatus: GeofenceStatus,
    val distance: Double,
    val config: GeofenceConfig,
    val triggerSource: GeofenceTriggerSource
)

/**
 * 地理围栏事件类型枚举
 */
enum class GeofenceEventType {
    ENTER,          // 进入围栏
    EXIT,           // 离开围栏
    DWELL,          // 在围栏内停留
    STATUS_CHANGE   // 状态变化
}

/**
 * 地理围栏触发源枚举
 */
enum class GeofenceTriggerSource {
    GPS,            // GPS定位
    WIFI,           // WiFi辅助定位
    BLUETOOTH,      // 蓝牙信标
    NETWORK,        // 网络定位
    MANUAL          // 手动触发
}

/**
 * 地理围栏工具类
 */
object GeofenceUtils {
    
    /**
     * 使用Haversine公式计算两点间距离（米）
     */
    fun calculateDistance(lat1: Double, lon1: Double, lat2: Double, lon2: Double): Double {
        val earthRadius = 6371000.0 // 地球半径（米）
        
        val dLat = Math.toRadians(lat2 - lat1)
        val dLon = Math.toRadians(lon2 - lon1)
        
        val a = sin(dLat / 2).pow(2) + 
                cos(Math.toRadians(lat1)) * cos(Math.toRadians(lat2)) * 
                sin(dLon / 2).pow(2)
        
        val c = 2 * atan2(sqrt(a), sqrt(1 - a))
        
        return earthRadius * c
    }
    
    /**
     * 检查位置是否在地理围栏内
     */
    fun isLocationInGeofence(location: LocationInfo, config: GeofenceConfig): Boolean {
        val distance = location.distanceToGeofence(config)
        return distance <= config.radius
    }
    
    /**
     * 根据GPS精度调整围栏半径
     */
    fun adjustRadiusForAccuracy(originalRadius: Float, gpsAccuracy: Float): Float {
        // 如果GPS精度较差，适当扩大围栏半径以避免误判
        return if (gpsAccuracy > 50) {
            originalRadius + gpsAccuracy * 0.5f
        } else {
            originalRadius
        }
    }
    
    /**
     * 计算两个位置之间的方位角
     */
    fun calculateBearing(from: LocationInfo, to: LocationInfo): Double {
        val lat1 = Math.toRadians(from.latitude)
        val lat2 = Math.toRadians(to.latitude)
        val deltaLon = Math.toRadians(to.longitude - from.longitude)

        val y = sin(deltaLon) * cos(lat2)
        val x = cos(lat1) * sin(lat2) - sin(lat1) * cos(lat2) * cos(deltaLon)

        val bearing = Math.toDegrees(atan2(y, x))
        return (bearing + 360) % 360
    }
}

/**
 * 地理围栏统计信息类
 */
data class GeofenceStatistics(
    var totalLocationUpdates: Long = 0,
    var gpsLocationUpdates: Long = 0,
    var networkLocationUpdates: Long = 0,
    var bluetoothDetections: Long = 0,
    var wifiDetections: Long = 0,
    var geofenceEnterEvents: Long = 0,
    var geofenceExitEvents: Long = 0,
    var securityActionsExecuted: Long = 0,
    var lastLocationUpdate: Long = 0,
    var lastGeofenceEvent: Long = 0,
    var averageGpsAccuracy: Float = 0f,
    var batteryOptimizationEnabled: Boolean = false
) {

    private var totalGpsAccuracy: Float = 0f

    /**
     * 记录位置更新
     */
    fun recordLocationUpdate() {
        totalLocationUpdates++
        lastLocationUpdate = System.currentTimeMillis()
    }

    /**
     * 记录GPS更新
     */
    fun recordGpsUpdate(accuracy: Float = 0f) {
        gpsLocationUpdates++
        if (accuracy > 0) {
            totalGpsAccuracy += accuracy
            averageGpsAccuracy = totalGpsAccuracy / gpsLocationUpdates
        }
    }

    /**
     * 记录网络定位更新
     */
    fun recordNetworkUpdate() {
        networkLocationUpdates++
    }

    /**
     * 记录蓝牙检测
     */
    fun recordBluetoothDetection() {
        bluetoothDetections++
    }

    /**
     * 记录WiFi检测
     */
    fun recordWifiDetection() {
        wifiDetections++
    }

    /**
     * 记录进入围栏事件
     */
    fun recordGeofenceEnter() {
        geofenceEnterEvents++
        lastGeofenceEvent = System.currentTimeMillis()
    }

    /**
     * 记录离开围栏事件
     */
    fun recordGeofenceExit() {
        geofenceExitEvents++
        lastGeofenceEvent = System.currentTimeMillis()
    }

    /**
     * 记录安全措施执行
     */
    fun recordSecurityAction() {
        securityActionsExecuted++
    }

    /**
     * 重置统计信息
     */
    fun reset() {
        totalLocationUpdates = 0
        gpsLocationUpdates = 0
        networkLocationUpdates = 0
        bluetoothDetections = 0
        wifiDetections = 0
        geofenceEnterEvents = 0
        geofenceExitEvents = 0
        securityActionsExecuted = 0
        lastLocationUpdate = 0
        lastGeofenceEvent = 0
        totalGpsAccuracy = 0f
        averageGpsAccuracy = 0f
    }
}
