package com.dspread.mdm.service.network.websocket

import android.content.Context
import com.dspread.mdm.service.broadcast.core.BroadcastActions
import com.dspread.mdm.service.broadcast.core.BroadcastSender
import com.dspread.mdm.service.network.websocket.message.WsMessageCenter
import com.dspread.mdm.service.network.websocket.message.WsMessageSender
import com.dspread.mdm.service.network.websocket.task.WsTaskManager
import com.dspread.mdm.service.network.websocket.connection.WsKeyManager
import com.dspread.mdm.service.network.websocket.message.processor.WsMessageProcessor

import com.dspread.mdm.service.network.websocket.connection.WsManager
import com.dspread.mdm.service.utils.log.Logger
import com.dspread.mdm.service.modules.provisioning.ProvisioningManager
import com.dspread.mdm.service.platform.monitor.UserInteractionMonitor
import org.json.JSONObject

/**
 * WebSocket 业务中心
 * WebSocketCenter.java，保持接口一致性
 *
 * 功能模块：
 * 1. 初始化管理 - init(), cleanup(), reinitialize()
 * 2. 连接管理 - connect(), disconnect(), reconnect()
 * 3. 消息发送 - sendMessage(), heartBeat(), C0000_wsRespone()
 * 4. 状态查询 - isConnected(), isInitialized()
 * 5. 监控管理 - startDeviceMonitoring(), stopDeviceMonitoring()
 * 6. 事件处理 - handleDeviceInfoRequest(), setMessageListener()
 */
object WebSocketCenter {

    // 消息监听器接口
    interface OnMessageListener {
        fun onMessageRecv(message: String)
    }

    private var messageListener: OnMessageListener? = null
    private var messageProcessor: WsMessageProcessor? = null
    private var userInteractionMonitor: UserInteractionMonitor? = null
    private var isInitialized = false

    /**
     * 初始化 WebSocket 中心
     * 合并了原WebSocketInitializer的功能
     */
    fun init(context: Context) {
        if (isInitialized) {
            Logger.wsm("WebSocketCenter 已初始化")
            return
        }

        try {
            Logger.wsm("开始初始化 WebSocket 组件...")

            // 1. 初始化密钥管理器
            initializeKeyManager(context)

            // 2. 初始化消息中心
            WsMessageCenter.init(context)

            // 3. 恢复任务状态
            resetTaskStates(context)

            // 4. 初始化消息预处理器
            messageProcessor = WsMessageProcessor(context).apply {
                setMessageListener { message ->
                    // 先通知外部监听器（如果有的话）
                    messageListener?.onMessageRecv(message)
                    // 然后传递给消息中心处理
                    WsMessageCenter.handleMessage(message)
                }
            }

            // 5. 初始化 WebSocket 管理器（重要：必须在连接前初始化）
            initializeWebSocketManager(context)

            // 6. 初始化用户交互监控器
            userInteractionMonitor = UserInteractionMonitor(context)

            isInitialized = true
            Logger.success("WebSocket 组件初始化完成")

        } catch (e: Exception) {
            Logger.wsmE("WebSocketCenter 初始化失败", e)
        }
    }

    /**
     * 清理资源
     */
    fun cleanup() {
        try {
            Logger.wsm("清理 WebSocket 资源...")

            // 释放资源
            release()

            isInitialized = false
            Logger.success("WebSocket 资源清理完成")

        } catch (e: Exception) {
            Logger.wsmE("WebSocket 资源清理失败", e)
        }
    }

    /**
     * 重新初始化（先清理再初始化）
     */
    fun reinitialize(context: Context) {
        try {
            Logger.wsm("重新初始化 WebSocket 组件...")
            cleanup()
            init(context)
        } catch (e: Exception) {
            Logger.wsmE("重新初始化 WebSocket 组件失败", e)
        }
    }

    /**
     * 启动 WebSocket 连接
     */
    fun connect() {
        try {
            if (!isInitialized) {
                Logger.wsmE("WebSocket 未初始化，无法连接")
                return
            }

            Logger.wsm("启动 WebSocket 连接...")
            WsManager.getInstance().connect()

            // 启动连接保活机制
//            startConnectionKeepAlive()

            Logger.wsm("WebSocket 连接启动完成")

        } catch (e: Exception) {
            Logger.wsmE("启动 WebSocket 连接失败", e)
        }
    }

    /**
     * 断开 WebSocket 连接
     */
    fun disconnect() {
        try {
            Logger.wsm("断开 WebSocket 连接...")
            WsManager.getInstance().disconnect()
            Logger.wsm("WebSocket 连接断开完成")

        } catch (e: Exception) {
            Logger.wsmE("断开 WebSocket 连接失败", e)
        }
    }

    /**
     * 重新连接 WebSocket
     */
    fun reconnect() {
        try {
            Logger.wsm("重新连接 WebSocket...")
            WsManager.getInstance().reconnect()
            Logger.wsm("WebSocket 重连启动完成")

        } catch (e: Exception) {
            Logger.wsmE("重新连接 WebSocket 失败", e)
        }
    }

    /**
     * 检查 WebSocket 连接状态
     */
    fun isConnected(): Boolean {
        return try {
            WsManager.getInstance().isConnected()
        } catch (e: Exception) {
            Logger.wsmE("检查连接状态失败", e)
            false
        }
    }

    /**
     * 释放资源
     */
    fun release() {
        try {
            Logger.wsm("释放 WebSocket 资源...")

            // 停止设备监控
//            stopDeviceMonitoring()

            // 释放消息预处理器
            messageProcessor?.release()
            messageProcessor = null

            // 释放 WebSocket 管理器
            WsManager.getInstance().release()

            Logger.wsm("WebSocket 资源释放完成")

        } catch (e: Exception) {
            Logger.wsmE("释放 WebSocket 资源失败", e)
        }
    }

    /**
     * 初始化 WebSocket 管理器
     */
    private fun initializeWebSocketManager(context: Context) {
        try {
            // 获取 WebSocket URL（从配置或默认值）
            val websocketUrl = getWebSocketUrl(context)

            if (websocketUrl.isNotEmpty()) {
                // 构建 WebSocket 管理器
                val builder = WsManager.Builder()
                    .context(context)
                    .serviceUrl(websocketUrl)
                    .serviceUrlForReconn(websocketUrl)
                    .connectTimeout(15000)

                // 初始化 WebSocket 管理器
                WsManager.getInstance().init(builder)
                Logger.wsm("WebSocket 管理器初始化完成")

            } else {
                Logger.wsmE("WebSocket URL 为空，无法初始化管理器")
            }

        } catch (e: Exception) {
            Logger.wsmE("初始化 WebSocket 管理器失败", e)
        }
    }

    /**
     * 获取 WebSocket URL
     * 只从Provisioning配置获取，ProvisioningConfig会提供默认配置
     */
    private fun getWebSocketUrl(context: Context): String {
        return try {
            val provisioningManager = ProvisioningManager.getInstance(context)
            val provisioningConfig = try {
                provisioningManager.getCurrentConfig()
            } catch (e: Exception) {
                Logger.wsmE("Provisioning配置解析失败", e)
                null
            }

            if (provisioningConfig != null) {
                val websocketUrl = provisioningConfig.polling.statusApiUrl
                Logger.wsm("使用Provisioning配置的WebSocket URL: $websocketUrl")
                return websocketUrl
            } else {
                Logger.wsmE("无法获取Provisioning配置，WebSocket初始化失败")
                return ""
            }
        } catch (e: Exception) {
            Logger.wsmE("获取 WebSocket URL 失败", e)
            return ""
        }
    }



    /**
     * 发送心跳
     * 主要给PollTimer使用
     */
    fun heartBeat() {
        Logger.wsm("WebSocketCenter: 发送心跳")
        WsMessageSender.sendHeartbeat()
    }

    /**
     * 处理设备信息请求
     */
    fun handleDeviceInfoRequest(message: JSONObject) {
        try {
            // 设备信息请求现在由CommandHandler中的SC004处理
            // 这个方法保留是为了兼容性，实际处理在CommandHandler.handleHeartbeatCommand()中
            Logger.wsm("收到设备信息请求，将由CommandHandler处理")
        } catch (e: Exception) {
            Logger.wsmE("处理设备信息请求失败", e)
        }
    }

    /**
     * 启动设备状态监控（C0201用户交互监控）
     */
    fun startDeviceMonitoring() {
        try {
            userInteractionMonitor?.startMonitoring()

            // 启动服务生命周期检查
            com.dspread.mdm.service.network.websocket.message.ServiceLifecycleManager.startLifecycleCheck()

            Logger.wsm("用户交互监控已启动")
        } catch (e: Exception) {
            Logger.wsmE("启动用户交互监控失败", e)
        }
    }

    /**
     * 停止设备状态监控（C0201用户交互监控）
     */
    fun stopDeviceMonitoring() {
        try {
            userInteractionMonitor?.stopMonitoring()
            Logger.wsm("用户交互监控已停止")
        } catch (e: Exception) {
            Logger.wsmE("停止用户交互监控失败", e)
        }
    }

    /**
     * 初始化密钥管理器
     */
    private fun initializeKeyManager(context: Context) {
        try {
            WsKeyManager.initialize(context)
        } catch (e: Exception) {
            Logger.wsmE("密钥管理器初始化失败", e)
        }
    }

    /**
     * 恢复任务状态（先清理终态任务，再将正在执行的任务重置为待执行）
     */
    private fun resetTaskStates(context: Context) {
        try {
            // 1. 先清理终态任务（已完成的任务不需要保留）
            WsTaskManager.cleanupTerminalTasks()

            // 2. 再重置执行中的任务状态
            WsTaskManager.resetDoingWSTaskState()
        } catch (e: Exception) {
            Logger.wsmE("任务状态恢复失败", e)
        }
    }

    // ==================== 消息监听管理 ====================

    /**
     * 设置消息监听器
     */
    fun setMessageListener(listener: OnMessageListener?) {
        this.messageListener = listener
        Logger.wsm("设置WebSocket消息监听器: ${if (listener != null) "已设置" else "已清除"}")
    }

    /**
     * 获取当前消息监听器
     */
    fun getMessageListener(): OnMessageListener? {
        return messageListener
    }

    /**
     * 获取消息预处理器（供 WsConnectionManager 调用）
     */
    fun getMessageProcessor(): WsMessageProcessor? {
        return messageProcessor
    }

    // ==================== 消息处理 ====================

    /**
     * 启动终端信息上传定时器
     */
    private fun startTerminalInfoUploadTimer(context: Context) {
        try {
            BroadcastSender.sendBroadcast(context, BroadcastActions.TER_INFO_UPLOAD_BC)
            Logger.wsm("启动终端信息上传定时器")
        } catch (e: Exception) {
            Logger.wsmE("启动终端信息上传定时器失败", e)
        }
    }

    /**
     * 启动连接保活机制
     * 防止系统深度休眠导致WebSocket连接断开
     */
    private fun startConnectionKeepAlive() {
        try {
            // 启动心跳保活
            startHeartbeatKeepAlive()

            // 启动连接状态监控
            startConnectionMonitor()

            Logger.wsm("WebSocket连接保活机制启动")

        } catch (e: Exception) {
            Logger.wsmE("启动连接保活机制失败", e)
        }
    }

    /**
     * 启动心跳保活
     * 注意：心跳发送由PollTimer负责（5分钟间隔），这里只做连接监控
     */
    private fun startHeartbeatKeepAlive() {
        // 每60秒检查一次连接状态，不发送心跳（心跳由PollTimer负责）
        val connectionCheckRunnable = object : Runnable {
            override fun run() {
                try {
                    if (!isConnected()) {
                        Logger.wsm("💔 连接已断开，尝试重连")
                        reconnect()
                    }

                    // 60秒后再次检查
                    android.os.Handler(android.os.Looper.getMainLooper()).postDelayed(this, 60000)

                } catch (e: Exception) {
                    Logger.wsmE("连接检查失败", e)
                }
            }
        }

        // 启动连接检查
        android.os.Handler(android.os.Looper.getMainLooper()).postDelayed(connectionCheckRunnable, 60000)
    }

    /**
     * 启动连接状态监控
     */
    private fun startConnectionMonitor() {
        // 每60秒检查一次连接状态
        val monitorRunnable = object : Runnable {
            override fun run() {
                try {
                    if (!isConnected()) {
                        Logger.wsm("检测到连接断开，尝试重连")
                        reconnect()
                    } else {
                        // Logger.wsm("连接状态正常") // 注释掉频繁的状态检查日志
                    }

                    // 60秒后再次检查
                    android.os.Handler(android.os.Looper.getMainLooper()).postDelayed(this, 60000)

                } catch (e: Exception) {
                    Logger.wsmE("连接状态监控失败", e)
                }
            }
        }

        // 启动监控
        android.os.Handler(android.os.Looper.getMainLooper()).postDelayed(monitorRunnable, 60000)
    }

}
