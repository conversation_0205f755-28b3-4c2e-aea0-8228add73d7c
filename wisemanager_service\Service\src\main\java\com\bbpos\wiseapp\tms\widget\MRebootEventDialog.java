package com.bbpos.wiseapp.tms.widget;

import android.app.Dialog;
import android.content.Context;
import android.content.DialogInterface;
import android.content.SharedPreferences;
import android.graphics.Color;
import android.graphics.PixelFormat;
import android.graphics.Rect;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.Message;
import android.preference.PreferenceManager;
import android.util.DisplayMetrics;
import android.util.Log;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.TextView;

import com.bbpos.wiseapp.logger.BBLog;
import com.bbpos.wiseapp.service.R;
import com.bbpos.wiseapp.tms.adapter.SystemManagerAdapter;
import com.bbpos.wiseapp.tms.model.AppInfo;
import com.bbpos.wiseapp.tms.utils.BroadcastActions;
import com.bbpos.wiseapp.tms.utils.Constants;
import com.bbpos.wiseapp.tms.utils.ContextUtil;
import com.bbpos.wiseapp.tms.utils.Helpers;

import java.util.List;
import java.util.Timer;
import java.util.TimerTask;

public class MRebootEventDialog extends Dialog{
	private static final int MSG_UPDATE_DESKTOP = Integer.MAX_VALUE - 1;
	private Context context;
	private String mTxtTitle = "";
	private String mTxtMsg = "";
	private TextView mTitle;
	private TextView mMsg;
	private TextView mTimeout;
	private TextView mBtnDelay;
	private TextView mBtnInstall;
	private View.OnClickListener delayListener;
	private View.OnClickListener installListener;
	public static MRebootEventDialog mDialog;

	private boolean isCreated = false;
	private Timer mTimer = null;
	private Handler mHandler = new Handler() {
		@Override
		public void handleMessage(Message msg) {
			super.handleMessage(msg);
			if (msg.what == MSG_UPDATE_DESKTOP) {
				if (mForceAutoCloseDialogRemainSec >= 0) {
					mDesktopLayout.setText("Terminal will reboot in " + getTimeStr() + " secs");
				} else if (mForceAutoCloseDialogRemainSec < 0) {
					SystemManagerAdapter.reboot(ContextUtil.getInstance());
					if (timerDesk != null) {
						timerDesk.cancel();
					}
					closeDesk();
				}
			} else if (msg.what >= 0) {
				String str_hour = "";
				String str_min = "";
				String str_sec = "";
				int hour = msg.what/3600;
				int min = (msg.what-hour*3600)/60;
				int sec = msg.what%60;
				if (hour>0 && hour<10) {
					str_hour = "0"+hour+"";
				} else if (hour>=10) {
					str_hour = hour+"";
				}
				if (min>0 && min<10) {
					str_min = "0"+min+"";
				} else if (min>=10) {
					str_min = min+"";
				}
				if (sec>=0 && sec<10) {
					str_sec = "0"+sec+"";
				} else if (sec>=10) {
					str_sec = sec+"";
				}
				mTimeout.setText((hour>0?str_hour+":":"") + (min>0?str_min+":":"") +str_sec);
			} else {
				mBtnInstall.callOnClick();
				mTimer.cancel();
			}
		}
	};


	public static MRebootEventDialog showRebootEventDialog(Context context, String title, String msg, final View.OnClickListener onDelayClickListener, final View.OnClickListener onInstallClickListener){
		BBLog.w(BBLog.TAG, "MRebootEventDialog");
		if (mDialog != null) {
			mDialog.dismiss();
			mDialog = null;
		}
		mDialog = new MRebootEventDialog(context) ;
		mDialog.setTitle(title);
		mDialog.setMessage(msg);
		mDialog.setCanceledOnTouchOutside(false);
		mDialog.setDelayButton(new View.OnClickListener() {
			@Override
			public void onClick(View v) {
				if (mDialog.mTimer != null) {
					mDialog.mTimer.cancel();
				}
				mDialog.dismiss();
				if (onDelayClickListener != null) {
					onDelayClickListener.onClick(v);
				}
			}
		});
		mDialog.setInstallButton(new View.OnClickListener() {
			@Override
			public void onClick(View v) {
				if (mDialog.mTimer != null) {
					mDialog.mTimer.cancel();
				}
				mDialog.dismiss();
				if (onInstallClickListener != null) {
					onInstallClickListener.onClick(v);
				}
			}
		});
		mDialog.setOnKeyListener(new DialogInterface.OnKeyListener() {
			@Override
			public boolean onKey(DialogInterface dialog, int keyCode, KeyEvent event) {
				if(keyCode == KeyEvent.KEYCODE_BACK)
				{

				}
				return false;
			}
		});

		Window dialogWindow = mDialog.getWindow();
		WindowManager.LayoutParams lp = dialogWindow.getAttributes();
		/*
		 * lp.x与lp.y表示相对于原始位置的偏移.
		 * 当参数值包含Gravity.LEFT时,对话框出现在左边,所以lp.x就表示相对左边的偏移,负值忽略.
		 * 当参数值包含Gravity.RIGHT时,对话框出现在右边,所以lp.x就表示相对右边的偏移,负值忽略.
		 * 当参数值包含Gravity.TOP时,对话框出现在上边,所以lp.y就表示相对上边的偏移,负值忽略.
		 * 当参数值包含Gravity.BOTTOM时,对话框出现在下边,所以lp.y就表示相对下边的偏移,负值忽略.
		 * 当参数值包含Gravity.CENTER_HORIZONTAL时
		 * ,对话框水平居中,所以lp.x就表示在水平居中的位置移动lp.x像素,正值向右移动,负值向左移动.
		 * 当参数值包含Gravity.CENTER_VERTICAL时
		 * ,对话框垂直居中,所以lp.y就表示在垂直居中的位置移动lp.y像素,正值向右移动,负值向左移动.
		 * gravity的默认值为Gravity.CENTER,即Gravity.CENTER_HORIZONTAL |
		 * Gravity.CENTER_VERTICAL.
		 *
		 * 本来setGravity的参数值为Gravity.LEFT | Gravity.TOP时对话框应出现在程序的左上角,但在
		 * 我手机上测试时发现距左边与上边都有一小段距离,而且垂直坐标把程序标题栏也计算在内了,
		 * Gravity.LEFT, Gravity.TOP, Gravity.BOTTOM与Gravity.RIGHT都是如此,据边界有一小段距离
		 */

		WindowManager m = (WindowManager)context.getSystemService(Context.WINDOW_SERVICE);
		DisplayMetrics dm = new DisplayMetrics();; // 获取屏幕宽、高用
		m.getDefaultDisplay().getMetrics(dm);
		WindowManager.LayoutParams p = dialogWindow.getAttributes(); // 获取对话框当前的参数值

		lp.width = (int) (dm.widthPixels); // 宽度
		lp.height = (int) (dm.heightPixels); // 宽度

		dialogWindow.setAttributes(lp);
		dialogWindow.setType(WindowManager.LayoutParams.TYPE_SYSTEM_ALERT);//这句话是关键
		mDialog.show();
		return mDialog;
	}

	TimerTask timerTask = new TimerTask() {
		int second = 300;
		@Override
		public void run() {
			Message msg = new Message();
			msg.what = second;
			mHandler.sendMessage(msg);
			second--;
		}
	};

	@Override
	protected void onCreate(Bundle savedInstanceState) {
		super.onCreate(savedInstanceState);
		init();

		if (mTimer == null) {
			mTimer = new Timer();
			mTimer.schedule(timerTask, 0, 1000);
		}
	}

	public MRebootEventDialog(Context context){
		super(context, R.style.dialog_style_ex);
		this.context = context;
	}

	public MRebootEventDialog(Context context, List<AppInfo> list) {
		super(context);
		this.context = context;
	}

	private void init(){
		LayoutInflater inflater = LayoutInflater.from(context);
		View view = inflater.inflate(R.layout.dialog_osupgrade,null);
		setContentView(view);
		isCreated = true;
		findControls(view);
	}

	private void findControls(View view){
		mTitle = (TextView) view.findViewById(R.id.tv_title);
		mTitle.setText(mTxtTitle);
		mMsg = (TextView) view.findViewById(R.id.tv_msg);
		mMsg.setText(mTxtMsg);
		mTimeout = (TextView)view.findViewById(R.id.tv_timeout);
		mBtnDelay = (TextView)view.findViewById(R.id.tv_delay);
		mBtnDelay.setText(R.string.reboot_after_5min);
		mBtnDelay.setOnClickListener(delayListener);
		mBtnInstall = (TextView)view.findViewById(R.id.tv_install);
		mBtnInstall.setText(R.string.reboot_now);
		mBtnInstall.setOnClickListener(installListener);
	}

	/**
	 * @Description  设置确定键内容（右键）
	 * @param listener
	 * @return void
	 */
	public void setDelayButton(final View.OnClickListener listener){
		delayListener = listener;
		if(isCreated){
			mBtnDelay.setOnClickListener(delayListener);
		}
	}

	/**
	 * @Description  设置取消键内容（左键）
	 * @param listener
	 * @return void
	 */
	public void setInstallButton(final View.OnClickListener listener){
		installListener = listener;
		if(isCreated){
			mBtnInstall.setOnClickListener(installListener);
		}
	}

	public void setTitle(String title){
		if (title != null) {
			mTxtTitle = title;
		}
	}

	public void setMessage(String msg){
		if (msg != null) {
			mTxtMsg = msg;
		}
	}

	////////////////////////////////////////////////////////////
	private WindowManager mWindowManager;
	private WindowManager.LayoutParams mLayout;
	private View mView;
	private TextView mDesktopLayout;
	public static int mForceAutoCloseDialogRemainSec = 60;
	private TimerTask mForceAutoCloseDialogTimerTask;
	private final Timer timerDesk = new Timer();
	private TimerTask task;
	/**
	 * 创建悬浮窗体
	 */
	public void createDesktopLayout() {
		if (mView == null) {
			LayoutInflater layoutInflater = LayoutInflater.from(ContextUtil.getInstance());

			mView = layoutInflater.inflate(R.layout.view_floatwin, null);
			mDesktopLayout = mView.findViewById(R.id.tv_msg);
			mDesktopLayout.setText("Terminal will reboot in " + getTimeStr() + " secs");
			mDesktopLayout.setGravity(Gravity.CENTER);
			mDesktopLayout.setTextColor(Color.BLACK);

			mDesktopLayout.setBackgroundColor(ContextUtil.getInstance().getResources().getColor(R.color.white));
			BBLog.i(Constants.TAG, "mWindowManager  showDesk" + mView.getId());
			mDesktopLayout.setOnTouchListener(new View.OnTouchListener() {
				@Override
				public boolean onTouch(View v, MotionEvent event) {
					switch (event.getAction()) {
						case MotionEvent.ACTION_DOWN:
							mTouchStartX = event.getRawX();
							mTouchStartY = event.getRawY() - top; // 25是系统状态栏的高度
							x = mLayout.x;
							y = mLayout.y;
							startTime = System.currentTimeMillis();
							break;
						case MotionEvent.ACTION_MOVE:
							// 手指移动时，对比按下时的横坐标，计算出移动的距离，来调整menu的leftMargin值，从而显示和隐藏menu
							// 更新浮动窗口位置参数
							int delX = (int) (event.getRawX() - mTouchStartX);
							int delY = (int) (event.getRawY() - mTouchStartY);
							mLayout.x = (int) (x + delX);
							mLayout.y = (int) (y + delY);
							mWindowManager.updateViewLayout(mView, mLayout);
							// menulayout.setLayoutParams(menuParams);
							break;
						case MotionEvent.ACTION_UP:
//							int moveX = (int) (event.getX() - mTouchStartX);
//							int moveY = (int) (event.getY() - mTouchStartY);
//							long end = System.currentTimeMillis() - startTime;
//							if (moveX < (width / 20) && moveY < (height / 20) && end < 160) {
//								if (timerDesk != null) {
//									timerDesk.cancel();
//								}
//								closeDesk();
//								Helpers.sendBroad(context, BroadcastActions.ACTION_EXPIRE_REBOOT);
//							}
							break;
					}

					return true;
				}
			});

			mView.measure(View.MeasureSpec.makeMeasureSpec(0,
					View.MeasureSpec.UNSPECIFIED), View.MeasureSpec
					.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED));
		}
	}

	/**
	 * 设置WindowManager
	 */
	public void createWindowManager() {
		// 取得系统窗体
		mWindowManager = (WindowManager) ContextUtil.getInstance().getSystemService(Context.WINDOW_SERVICE);
		// 窗体的布局样式
		mLayout = new WindowManager.LayoutParams();
		// 设置窗体显示类型――TYPE_SYSTEM_ALERT(系统提示)
		if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
			mLayout.type = WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY;
		} else {
			mLayout.type = WindowManager.LayoutParams.TYPE_PHONE;
		}
		// 设置窗体焦点及触摸：
		// FLAG_NOT_FOCUSABLE(不能获得按键输入焦点)
		mLayout.flags = WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE | WindowManager.LayoutParams.FLAG_FULLSCREEN;
		// 设置显示的模式
		mLayout.format = PixelFormat.RGBA_8888;
		// 设置对齐的方法
		mLayout.gravity = Gravity.TOP | Gravity.CENTER_HORIZONTAL;
		mLayout.x = 0;
		mLayout.y = 0 + 25;
		// 设置窗体宽度和高度
		mLayout.width = WindowManager.LayoutParams.WRAP_CONTENT;
		mLayout.height = WindowManager.LayoutParams.WRAP_CONTENT;

		width = mWindowManager.getDefaultDisplay().getWidth();
		height = mWindowManager.getDefaultDisplay().getHeight();
	}

	private void closeDesk() {
		if (mView.getParent() != null) {
			mWindowManager.removeView(mView);
			mView = null;
		}

		if (timerDesk != null) {
			timerDesk.cancel();
		}
	}

	public void StartCheckShowViewClose() {
		if (task == null) {
			task = new TimerTask() {
				@Override
				public void run() {
					mForceAutoCloseDialogRemainSec--;
					BBLog.v(Constants.TAG, "StartCheckShowViewClose mForceAutoCloseDialogRemainSec:" + mForceAutoCloseDialogRemainSec);
					mHandler.sendEmptyMessage(MSG_UPDATE_DESKTOP);
				}
			};
		}
		timerDesk.schedule(task, 1000, 1000);
	}

	/**
	 * 显示DesktopLayout
	 */
	public void showDesk(int timeout) {
		mForceAutoCloseDialogRemainSec = timeout;
		BBLog.i(Constants.TAG, "mWindowManager showDesk" + mView.getId());
		if (mView.getParent() == null)
			mWindowManager.addView(mView, mLayout);
		StartCheckShowViewClose();
	}

	/******************************** 移动小窗 ******************************************/
	/**
	 * 记录手指移动时的横坐标。
	 */
	float mTouchStartX;
	float mTouchStartY;
	// 声明屏幕的宽高
	float x, y;
	int top;

	int width = 0;
	int height = 0;
	private long startTime;

	@Override
	public void onWindowFocusChanged(boolean hasFocus) {
		super.onWindowFocusChanged(hasFocus);
		Rect rect = new Rect();
		// /取得整个视图部分,注意，如果你要设置标题样式，这个必须出现在标题样式之后，否则会出错
		getWindow().getDecorView().getWindowVisibleDisplayFrame(rect);
		top = rect.top;// 状态栏的高度，所以rect.height,rect.width分别是系统的高度的宽度
//		BBLog.i(Constants.TAG, "top = " + top);
	}

	/** 写入初始位置 */
	private void writePosition(Context context, int poX, int poY) {
		SharedPreferences sp = PreferenceManager.getDefaultSharedPreferences(context);
		sp.edit().putInt("poX", poX).putInt("poY", poY).commit();
	}

	public int getPosition(Context context, String key, int defaultValue) {
		SharedPreferences sp = PreferenceManager.getDefaultSharedPreferences(context);
		return sp.getInt(key, defaultValue);
	}


	private String getTimeStr(){
		int hour = mForceAutoCloseDialogRemainSec/3600;
		int min = (mForceAutoCloseDialogRemainSec-hour*3600)/60;
		int sec = mForceAutoCloseDialogRemainSec%60;
		return (hour>0?hour+":":"") + (min>0?min+":":"") +sec;
	}
}
