package com.dspread.mdm.service.modules.geofence

import android.annotation.SuppressLint
import android.content.Context
import android.net.wifi.WifiManager
import android.bluetooth.BluetoothAdapter
import android.preference.PreferenceManager
import com.dspread.mdm.service.SmartMdmServiceApp
import com.dspread.mdm.service.constants.Constants
import com.dspread.mdm.service.modules.geofence.location.GpsLocationManager
import com.dspread.mdm.service.modules.BaseModuleManager
import com.dspread.mdm.service.modules.ModuleStatus
import com.dspread.mdm.service.modules.geofence.model.BeaconInfo
import com.dspread.mdm.service.modules.geofence.model.GeofenceConfig
import com.dspread.mdm.service.modules.geofence.model.GeofenceStatistics
import com.dspread.mdm.service.modules.geofence.model.GeofenceStatus
import com.dspread.mdm.service.modules.geofence.model.GeofenceUtils
import com.dspread.mdm.service.modules.geofence.model.LocationInfo
import com.dspread.mdm.service.platform.monitor.UserInteractionMonitor
import com.dspread.mdm.service.broadcast.receivers.GeofenceReceiver
import com.dspread.mdm.service.utils.log.Logger
import com.dspread.mdm.service.utils.storage.PreferencesManager
import kotlinx.coroutines.*
import org.json.JSONObject
import java.lang.ref.WeakReference
import java.util.concurrent.atomic.AtomicBoolean
import java.util.concurrent.atomic.AtomicReference

/**
 * 地理围栏管理器
 * 负责地理围栏的监控、状态管理和安全措施执行
 */
class GeofenceManager(
    private val context: Context
) : BaseModuleManager() {
    
    companion object {
        private const val TAG = "[GeofenceManager]"
        private const val BLUETOOTH_SCAN_INTERVAL = 60000L // 60秒

        @Volatile
        private var instanceRef: WeakReference<GeofenceManager>? = null

        /**
         * 获取GeofenceManager实例（向后兼容）
         * 使用WeakReference避免内存泄漏
         */
        @Synchronized
        fun getInstance(context: Context): GeofenceManager {
            // 使用ApplicationContext避免Activity Context泄漏
            val appContext = context.applicationContext

            // 检查现有实例是否仍然有效
            instanceRef?.get()?.let { existingInstance ->
                return existingInstance
            }

            // 创建新实例并用WeakReference包装
            val newInstance = GeofenceManager(appContext)
            instanceRef = WeakReference(newInstance)
            return newInstance
        }

        /**
         * 向后兼容：initialize方法
         */
        fun initialize(context: Context): Boolean {
            return try {
                val manager = getInstance(context)
                runBlocking {
                    val initResult = manager.initialize()
                    val startResult = manager.start()
                    initResult.isSuccess && startResult.isSuccess
                }
            } catch (e: Exception) {
                Logger.geoE("$TAG 兼容性初始化失败", e)
                false
            }
        }

        /**
         * 向后兼容：deinitialize方法
         */
        fun deinitialize(context: Context) {
            try {
                instanceRef?.get()?.let { manager ->
                    runBlocking {
                        manager.stop()
                    }
                }
                // 清理引用
                instanceRef = null
            } catch (e: Exception) {
                Logger.geoE("$TAG 兼容性反初始化失败", e)
            }
        }

        /**
         * 向后兼容：isInitialized方法
         */
        fun isInitialized(): Boolean {
            return instanceRef?.get()?.getStatus() == ModuleStatus.RUNNING
        }

        /**
         * 向后兼容：getStatusSummary方法
         */
        fun getStatusSummary(context: Context): String {
            return getInstance(context).getStatusSummary()
        }

        /**
         * 向后兼容：restart方法
         */
        fun restart(context: Context): Boolean {
            return try {
                val manager = getInstance(context)
                runBlocking {
                    manager.restart().isSuccess
                }
            } catch (e: Exception) {
                Logger.geoE("$TAG 兼容性重启失败", e)
                false
            }
        }

        /**
         * 向后兼容：executeGeofenceProfile方法
         */
        fun executeGeofenceProfile(context: Context, geoInfo: JSONObject): Boolean {
            return getInstance(context).executeGeofenceProfile(geoInfo)
        }
    }
    
    // 地理围栏相关组件
    private val securityActionHandler = SecurityActionHandler(context)
    private val bluetoothScanner = BluetoothBeaconScanner(context)
    
    // 状态管理
    private val currentConfig = AtomicReference<GeofenceConfig?>(null)
    private val currentStatus = AtomicReference(GeofenceStatus.OUT_OF_ZONE)
    private val currentLocation = AtomicReference<LocationInfo?>(null)
    private val isMonitoring = AtomicBoolean(false)
    private val isEmergencyMode = AtomicBoolean(false)

    private var geofenceReceiver: GeofenceReceiver? = null
    @SuppressLint("StaticFieldLeak")
    private var userInteractionMonitor: UserInteractionMonitor? = null
    
    // 单一协程作用域 + 明确Job管理
    // 使用SupervisorJob确保子协程失败不影响其他协程
    private val managerScope = CoroutineScope(Dispatchers.IO + SupervisorJob())

    // 明确的Job引用，便于精确控制各个功能模块
    private var monitoringJob: Job? = null
    private var bluetoothScanJob: Job? = null
    private var securityActionJob: Job? = null
    
    // 统计信息
    private val statistics = GeofenceStatistics()
    
    override fun getModuleName(): String = "Geofence Manager"
    
    override suspend fun onInitialize(): Result<Unit> {
        return try {
            Logger.geo("$TAG 开始初始化地理围栏管理器")

            // 1. 初始化蓝牙扫描器
            bluetoothScanner.initialize()

            // 2. 初始化安全措施处理器
            securityActionHandler.initialize()

            Logger.geo("$TAG 地理围栏管理器初始化完成")
            Result.success(Unit)
        } catch (e: Exception) {
            Logger.geoE("$TAG 地理围栏管理器初始化失败", e)
            Result.failure(e)
        }
    }
    
    override suspend fun onStart(): Result<Unit> {
        return try {
            Logger.geo("$TAG 启动地理围栏服务")

            // 1. 注册广播接收器
            registerBroadcastReceivers()

            // 2. 启动定时任务
            startTimerTasks()

            // 3. 初始化GPS位置监听
            initializeLocationMonitoring()

            // 4. 启动用户交互监控
            startUserInteractionMonitoring()

            // 5、初始化地理围栏状态
            initializeGeofenceProfile()

            Logger.geo("$TAG 地理围栏服务启动完成")
            Result.success(Unit)
        } catch (e: Exception) {
            Logger.geoE("$TAG 地理围栏服务启动失败", e)
            Result.failure(e)
        }
    }

    private fun initializeGeofenceProfile() {
        // 初始化地理围栏状态 - 使用与restoreGeofenceConfig一致的key
        val geofencePrefs = PreferenceManager.getDefaultSharedPreferences(context)
        Constants.geofenceStatus = geofencePrefs.getInt("geofence_status", GpsLocationManager.IN_ZONE)
        Logger.serve("${TAG}Constants.geofenceStatus = ${Constants.geofenceStatus}")

        // 初始化商店信息 - 使用与restoreGeofenceConfig一致的key
        Constants.storeId = geofencePrefs.getString("store_id", "") ?: ""
        Constants.storeSsid = geofencePrefs.getString("store_ssid", "") ?: ""
        Constants.storeIp = geofencePrefs.getString("store_ip", "") ?: ""
        Logger.serve("${TAG}-- Constants.storeId = ${Constants.storeId} -- Constants.storeSsid = ${Constants.storeSsid} -- Constants.storeIp = ${Constants.storeIp}")

        // 初始化GPS有效误差距离 - 使用PreferencesManager存储
        try {
            val validDistanceStr = PreferencesManager.getString("valid_distance", "500")
            if (validDistanceStr.all { it.isDigit() }) {
                GpsLocationManager.validErrorDistance = validDistanceStr.toFloatOrNull() ?: 500f
            } else {
                GpsLocationManager.validErrorDistance = 500f
            }
        } catch (e: Exception) {
            GpsLocationManager.validErrorDistance = 500f
            Logger.serveE("${TAG}初始化GPS有效误差距离失败", e)
        }

        // 初始化地理围栏Profile - 使用与restoreGeofenceConfig一致的key
        try {
            val profile = geofencePrefs.getString("geofence_current_profile", "") ?: ""
            if (profile.isNotEmpty()) {
                val profileJson = JSONObject(profile)
                GpsLocationManager.initGeoProfile(SmartMdmServiceApp.instance, profileJson)
            } else {
                GpsLocationManager.initGeoProfile(SmartMdmServiceApp.instance, null)
            }
        } catch (e: Exception) {
            Logger.serveE("${TAG}初始化地理围栏Profile失败", e)
        }
    }

    override suspend fun onStop(): Result<Unit> {
        return try {
            Logger.geo("$TAG 开始停止地理围栏服务")

            // 1. 停止定时任务
            stopTimerTasks()

            // 2. 注销广播接收器
            unregisterBroadcastReceivers()

            // 3. 停止位置监听
            stopLocationMonitoring()

            // 4. 停止用户交互监控
            stopUserInteractionMonitoring()

            // 5. 保存当前状态
            saveCurrentState()

            // 6. 停止所有监控
            stopAllMonitoring()

            // 7. 取消协程作用域（模块完全停止时）
            managerScope.cancel()

            Logger.geo("$TAG 地理围栏服务停止完成")
            Result.success(Unit)
        } catch (e: Exception) {
            Logger.geoE("$TAG 地理围栏服务停止失败", e)
            Result.failure(e)
        }
    }
    
    /**
     * 更新地理围栏配置
     */
    fun updateGeofenceConfig(config: GeofenceConfig): Result<Unit> {
        return try {
            Logger.geo("$TAG 更新地理围栏配置")
            
            // 检查配置有效性
            if (!config.isValid()) {
                return Result.failure(IllegalArgumentException("Invalid geofence config"))
            }
            
            // 检查是否为漫游状态
            if (config.isRoaming()) {
                Logger.geo("$TAG 设备处于漫游状态，暂停地理围栏功能")
                currentStatus.set(GeofenceStatus.ROAMING)
                stopAllMonitoring()
                currentConfig.set(config)
                return Result.success(Unit)
            }
            
            // 保存配置
            val oldConfig = currentConfig.getAndSet(config)
            
            // 如果配置发生变化，重新启动监控
            if (oldConfig != config) {
                Logger.geo("$TAG 地理围栏配置已更改，重新启动监控")
                
                if (isMonitoring.get()) {
                    stopAllMonitoring()
                    startLocationMonitoring()
                }
                
                // 重置状态
                resetStatus()
            }
            
            Logger.geo("$TAG 地理围栏配置更新完成")
            Result.success(Unit)
            
        } catch (e: Exception) {
            Logger.geoE("$TAG 地理围栏配置更新失败", e)
            Result.failure(e)
        }
    }
    
    /**
     * 开始监控
     */
    fun startMonitoring(): Result<Unit> {
        return try {
            if (isEmergencyMode.get()) {
                Logger.geoW("$TAG 紧急模式下无法启动监控")
                return Result.failure(IllegalStateException("Emergency mode is active"))
            }
            
            val config = currentConfig.get()
            if (config == null) {
                Logger.geoW("$TAG 未配置地理围栏，无法启动监控")
                return Result.failure(IllegalStateException("No geofence config"))
            }
            
            if (config.isRoaming()) {
                Logger.geo("$TAG 漫游状态下不启动监控")
                return Result.success(Unit)
            }
            
            Logger.geo("$TAG 开始地理围栏监控")
            startLocationMonitoring()
            
            Result.success(Unit)
        } catch (e: Exception) {
            Logger.geoE("$TAG 启动地理围栏监控失败", e)
            Result.failure(e)
        }
    }
    
    /**
     * 停止监控
     */
    fun stopMonitoring(): Result<Unit> {
        return try {
            Logger.geo("$TAG 停止地理围栏监控")
            stopAllMonitoring()
            Result.success(Unit)
        } catch (e: Exception) {
            Logger.geoE("$TAG 停止地理围栏监控失败", e)
            Result.failure(e)
        }
    }
    
    /**
     * 启动位置监控
     */
    private fun startLocationMonitoring() {
        if (isMonitoring.compareAndSet(false, true)) {
            Logger.geo("$TAG 启动位置监控")
            
            monitoringJob = managerScope.launch {
                try {
                    // 启动GPS位置更新
                    startGpsMonitoring()
                    
                    // 启动WiFi辅助定位
                    startWifiMonitoring()
                    
                    // 启动蓝牙信标扫描
                    startBluetoothMonitoring()
                    
                } catch (e: Exception) {
                    Logger.geoE("$TAG 位置监控启动失败", e)
                }
            }
        }
    }
    
    /**
     * 启动GPS监控
     */
    private fun startGpsMonitoring() {
        try {
            // gpsLocationManager.startLocationUpdates { location: Location ->
            //     GlobalScope.launch {
            //         val locationInfo = LocationInfo.fromLocation(location)
            //         processLocationUpdate(locationInfo)
            //         statistics.recordGpsUpdate()
            //     }
            // }
            Logger.geo("$TAG GPS监控暂时禁用")
            Logger.geo("$TAG GPS监控已启动")
        } catch (e: Exception) {
            Logger.geoE("$TAG GPS监控启动失败", e)
        }
    }
    
    /**
     * 启动WiFi监控
     */
    private fun startWifiMonitoring() {
        try {
            val config = currentConfig.get() ?: return
            
            if (config.wifiAssistEnabled && config.storeSsid.isNotEmpty()) {
                // 定期检查WiFi连接状态
                managerScope.launch {
                    while (isMonitoring.get()) {
                        try {
                            checkWifiAssistLocation()
                            delay(30000) // 30秒检查一次
                        } catch (e: Exception) {
                            Logger.geoE("$TAG WiFi辅助定位检查失败", e)
                        }
                    }
                }
                Logger.geo("$TAG WiFi辅助定位已启动")
            }
        } catch (e: Exception) {
            Logger.geoE("$TAG WiFi监控启动失败", e)
        }
    }
    
    /**
     * 启动蓝牙监控
     */
    private fun startBluetoothMonitoring() {
        try {
            val config = currentConfig.get() ?: return
            
            if (config.beaconEnabled && config.storeId.isNotEmpty()) {
                bluetoothScanJob = managerScope.launch {
                    while (isMonitoring.get()) {
                        try {
                            bluetoothScanner.startScan(config.storeId) { beacons ->
                                // 在同一作用域中处理蓝牙信标回调
                                managerScope.launch { processBluetoothBeacons(beacons) }
                            }
                            delay(BLUETOOTH_SCAN_INTERVAL)
                        } catch (e: Exception) {
                            Logger.geoE("$TAG 蓝牙扫描失败", e)
                        }
                    }
                }
                Logger.geo("$TAG 蓝牙信标监控已启动")
            }
        } catch (e: Exception) {
            Logger.geoE("$TAG 蓝牙监控启动失败", e)
        }
    }
    
    /**
     * 处理位置更新
     */
    fun processLocationUpdate(location: LocationInfo) {
        try {
            currentLocation.set(location)
            statistics.recordLocationUpdate()
            
            val config = currentConfig.get() ?: return
            
            // 计算与围栏中心的距离
            val distance = location.distanceToGeofence(config)
            
            // 根据GPS精度调整围栏半径
            val adjustedRadius =
                GeofenceUtils.adjustRadiusForAccuracy(config.radius, location.accuracy)
            
            Logger.geo("$TAG 位置更新: 距离围栏中心 ${distance.toInt()}米 (调整半径: ${adjustedRadius.toInt()}米)")
            
            // 判断是否在围栏内
            val isInGeofence = distance <= adjustedRadius
            val currentStat = currentStatus.get()
            
            if (isInGeofence) {
                // 在围栏内
                if (currentStat == GeofenceStatus.OUT_OF_ZONE ||
                    currentStat == GeofenceStatus.LOCK_SCREEN ||
                    currentStat == GeofenceStatus.WIPE_DATA
                ) {
                    
                    handleEnterGeofence(location, config)
                }
            } else {
                // 在围栏外
                if (currentStat == GeofenceStatus.IN_ZONE) {
                    handleExitGeofence(location, config)
                }
            }
            
        } catch (e: Exception) {
            Logger.geoE("$TAG 处理位置更新失败", e)
        }
    }
    
    /**
     * 处理进入围栏
     */
    private fun handleEnterGeofence(location : LocationInfo, config: GeofenceConfig) {
        try {
            Logger.geo("$TAG 进入地理围栏")
            statistics.recordGeofenceEnter()
            
            // 取消任何正在执行的安全措施
            securityActionJob?.cancel()
            
        } catch (e: Exception) {
            Logger.geoE("$TAG 处理进入围栏失败", e)
        }
    }
    
    /**
     * 处理离开围栏
     */
    private fun handleExitGeofence(location: LocationInfo, config: GeofenceConfig) {
        try {
            Logger.geo("$TAG 离开地理围栏")
            statistics.recordGeofenceExit()

            // 启动安全措施倒计时
            startSecurityActionCountdown(config)
            
        } catch (e: Exception) {
            Logger.geoE("$TAG 处理离开围栏失败", e)
        }
    }
    
    /**
     * 启动安全措施倒计时
     */
    private fun startSecurityActionCountdown(config: GeofenceConfig) {
        securityActionJob?.cancel()
        
        securityActionJob = managerScope.launch {
            try {
                Logger.geo("$TAG 启动安全措施倒计时: ${config.lockMinutes}分钟")
                
                // 显示警告
                securityActionHandler.showWarning("设备已离开安全区域，将在${config.lockMinutes}分钟后锁定")
                
                // 等待锁定时间
                delay(config.lockMinutes * 60 * 1000L)
                
                // 检查是否仍在围栏外
                if (currentStatus.get() == GeofenceStatus.OUT_OF_ZONE) {
                    executeLockScreen()
                    
                    // 如果配置了数据擦除
                    if (config.wipeData) {
                        Logger.geo("$TAG 启动数据擦除倒计时: ${config.wipeMins}分钟")
                        delay(config.wipeMins * 60 * 1000L)
                        
                        // 再次检查状态
                        if (currentStatus.get() == GeofenceStatus.LOCK_SCREEN) {
                            executeDataWipe()
                        }
                    }
                }
                
            } catch (_: CancellationException) {
                Logger.geo("$TAG 安全措施倒计时被取消")
            } catch (e: Exception) {
                Logger.geoE("$TAG 安全措施倒计时执行失败", e)
            }
        }
    }
    
    /**
     * 执行锁屏
     */
    suspend fun executeLockScreen(): Result<Unit> {
        return try {
            Logger.geo("$TAG 执行锁屏操作")
            
            currentStatus.set(GeofenceStatus.LOCK_SCREEN)
            val result = securityActionHandler.lockScreen()
            
            if (result.isSuccess) {
                statistics.recordSecurityAction()
                Logger.geo("$TAG 锁屏操作执行成功")
            } else {
                Logger.geoE("$TAG 锁屏操作执行失败")
            }
            
            result
        } catch (e: Exception) {
            Logger.geoE("$TAG 锁屏操作异常", e)
            Result.failure(e)
        }
    }
    
    /**
     * 执行数据擦除
     */
    suspend fun executeDataWipe(): Result<Unit> {
        return try {
            Logger.geo("$TAG 执行数据擦除操作")
            
            currentStatus.set(GeofenceStatus.WIPE_DATA)
            val result = securityActionHandler.wipeData()
            
            if (result.isSuccess) {
                statistics.recordSecurityAction()
                Logger.geo("$TAG 数据擦除操作执行成功")
            } else {
                Logger.geoE("$TAG 数据擦除操作执行失败")
            }
            
            result
        } catch (e: Exception) {
            Logger.geoE("$TAG 数据擦除操作异常", e)
            Result.failure(e)
        }
    }
    
    /**
     * 执行重启
     */
    suspend fun executeReboot(): Result<Unit> {
        return try {
            Logger.geo("$TAG 执行重启操作")
            
            val result = securityActionHandler.reboot()
            
            if (result.isSuccess) {
                statistics.recordSecurityAction()
                Logger.geo("$TAG 重启操作执行成功")
            } else {
                Logger.geoE("$TAG 重启操作执行失败")
            }
            
            result
        } catch (e: Exception) {
            Logger.geoE("$TAG 重启操作异常", e)
            Result.failure(e)
        }
    }
    
    // 其他方法将在下一个文件中继续实现...
    
    /**
     * 停止所有监控
     */
    private fun stopAllMonitoring() {
        isMonitoring.set(false)

        // 精确取消特定任务
        monitoringJob?.cancel()
        bluetoothScanJob?.cancel()
        securityActionJob?.cancel()

        // 只需要管理一个作用域（完全停止时才取消）
        // managerScope.cancel() // 只在模块完全销毁时调用

        // gpsLocationManager.stopLocationUpdates() // 暂时注释
        bluetoothScanner.stopScan()

        Logger.geo("$TAG 所有监控已停止")
    }
    
    /**
     * 重置状态
     */
    fun resetStatus() {
        currentStatus.set(GeofenceStatus.OUT_OF_ZONE)
        securityActionJob?.cancel()
        Logger.geo("$TAG 地理围栏状态已重置")
    }
    
    // Getter方法
    fun getCurrentStatus(): GeofenceStatus = currentStatus.get()
    fun getCurrentLocation(): LocationInfo? = currentLocation.get()
    fun getCurrentConfig(): GeofenceConfig? = currentConfig.get()
    fun isGpsEnabled(): Boolean = true // 暂时返回true
    fun isBluetoothEnabled(): Boolean = BluetoothAdapter.getDefaultAdapter()?.isEnabled ?: false
    fun isWifiEnabled(): Boolean = (context.applicationContext.getSystemService(Context.WIFI_SERVICE) as WifiManager).isWifiEnabled
    fun getStatistics(): GeofenceStatistics = statistics
    
    // 紧急模式
    fun enableEmergencyMode() {
        isEmergencyMode.set(true)
        stopAllMonitoring()
    }
    
    fun disableEmergencyMode() {
        isEmergencyMode.set(false)
        if (currentConfig.get() != null) {
            startLocationMonitoring()
        }
    }
    
    // 待实现的方法
    private fun checkWifiAssistLocation() {
        // TODO: 实现WiFi辅助定位检查
    }
    
    private fun processBluetoothBeacons(beacons: List<BeaconInfo>) {
        // TODO: 处理蓝牙信标检测结果
    }


    /**
     * 恢复地理围栏状态
     */
    private fun restoreGeofenceState() {
        try {
            Logger.geo("$TAG 恢复地理围栏状态")

            // 恢复配置和状态
            GeofenceStateManager.restoreGeofenceConfig(context)

            // 如果地理围栏已启用，执行当前Profile
            if (GpsLocationManager.geoStatus) {
                val currentProfile = GeofenceStateManager.getCurrentProfile(context)
                if (currentProfile.isNotEmpty()) {
                    try {
                        val profileJson = JSONObject(currentProfile)
                        // 使用executeGeoProfileForInitNonNull处理非空Profile
                        GpsLocationManager.executeGeoProfileForInitNonNull(context, profileJson)
                        Logger.geo("$TAG 恢复当前Profile执行")
                    } catch (e: Exception) {
                        Logger.geoE("$TAG 恢复Profile执行失败", e)
                    }
                }
            }

            Logger.geo("$TAG 地理围栏状态恢复完成: 启用=${GpsLocationManager.geoStatus}, 状态=${Constants.geofenceStatus}")

        } catch (e: Exception) {
            Logger.geoE("$TAG 恢复地理围栏状态失败", e)
        }
    }

    /**
     * 注册广播接收器
     */
    private fun registerBroadcastReceivers() {
        try {
            Logger.geo("$TAG 注册广播接收器")

            // 注册统一的GeofenceReceiver（合并了CloudReceiver和SystemReceiver的功能）
            geofenceReceiver = GeofenceReceiver()
            val geofenceFilter = GeofenceReceiver.createIntentFilter()
            context.registerReceiver(geofenceReceiver, geofenceFilter)
            Logger.geo("$TAG GeofenceReceiver已注册（包含原CloudReceiver和SystemReceiver功能）")

        } catch (e: Exception) {
            Logger.geoE("$TAG 注册广播接收器失败", e)
        }
    }

    /**
     * 注销广播接收器
     */
    private fun unregisterBroadcastReceivers() {
        try {
            Logger.geo("$TAG 注销广播接收器")

            geofenceReceiver?.let {
                context.unregisterReceiver(it)
                geofenceReceiver = null
                Logger.geo("$TAG GeofenceReceiver已注销")
            }

        } catch (e: Exception) {
            Logger.geoE("$TAG 注销广播接收器失败", e)
        }
    }

    /**
     * 启动定时任务
     */
    private fun startTimerTasks() {
        try {
            Logger.geo("$TAG 启动定时任务")

            // 启动系统定时器（现在由GeofenceReceiver管理）
            GeofenceReceiver.startSystemTimer(context)

            Logger.geo("$TAG 定时任务启动完成")

        } catch (e: Exception) {
            Logger.geoE("$TAG 启动定时任务失败", e)
        }
    }

    /**
     * 停止定时任务
     */
    private fun stopTimerTasks() {
        try {
            Logger.geo("$TAG 停止定时任务")

            // 停止系统定时器（现在由GeofenceReceiver管理）
            GeofenceReceiver.stopSystemTimer(context)

            Logger.geo("$TAG 定时任务停止完成")

        } catch (e: Exception) {
            Logger.geoE("$TAG 停止定时任务失败", e)
        }
    }

    /**
     * 初始化位置监听
     */
    private fun initializeLocationMonitoring() {
        try {
            Logger.geo("$TAG 初始化位置监听")

            // 如果地理围栏已启用，启动位置监听
            if (GpsLocationManager.geoStatus) {
                GpsLocationManager.registerLocationChangeListener(context)
                Logger.geo("$TAG GPS位置监听已启动")
            } else {
                Logger.geo("$TAG 地理围栏未启用，跳过位置监听")
            }

        } catch (e: Exception) {
            Logger.geoE("$TAG 初始化位置监听失败", e)
        }
    }

    /**
     * 停止位置监听
     */
    private fun stopLocationMonitoring() {
        try {
            Logger.geo("$TAG 停止位置监听")

            GpsLocationManager.unregisterLocationChangeListener()

            Logger.geo("$TAG 位置监听已停止")

        } catch (e: Exception) {
            Logger.geoE("$TAG 停止位置监听失败", e)
        }
    }

    /**
     * 启动用户交互监控
     */
    private fun startUserInteractionMonitoring() {
        try {
            Logger.geo("$TAG 启动用户交互监控")

            userInteractionMonitor = UserInteractionMonitor(context)
            userInteractionMonitor?.startMonitoring()

            Logger.geo("$TAG 用户交互监控已启动")

        } catch (e: Exception) {
            Logger.geoE("$TAG 启动用户交互监控失败", e)
        }
    }

    /**
     * 停止用户交互监控
     */
    private fun stopUserInteractionMonitoring() {
        try {
            Logger.geo("$TAG 停止用户交互监控")

            userInteractionMonitor?.stopMonitoring()
            userInteractionMonitor = null

            Logger.geo("$TAG 用户交互监控已停止")

        } catch (e: Exception) {
            Logger.geoE("$TAG 停止用户交互监控失败", e)
        }
    }

    /**
     * 保存当前状态
     */
    private fun saveCurrentState() {
        try {
            Logger.geo("$TAG 保存当前状态")

            // 保存地理围栏配置
            GeofenceStateManager.saveGeofenceConfig(context)

            Logger.geo("$TAG 当前状态保存完成")

        } catch (e: Exception) {
            Logger.geoE("$TAG 保存当前状态失败", e)
        }
    }

    /**
     * 获取地理围栏状态摘要
     */
    fun getStatusSummary(): String {
        return try {
            if (getStatus() != ModuleStatus.RUNNING) {
                "地理围栏模块未运行"
            } else {
                val stateSummary = GeofenceStateManager.getGeofenceStateSummary(context)
                "地理围栏模块已运行\n$stateSummary"
            }
        } catch (e: Exception) {
            Logger.geoE("$TAG 获取状态摘要失败", e)
            "状态获取失败"
        }
    }

    /**
     * 重新启动地理围栏模块
     */
    suspend fun restart(): Result<Unit> {
        return try {
            Logger.geo("$TAG 重新启动地理围栏模块")

            // 先停止
            stop()

            // 等待一段时间
            delay(1000)

            // 重新启动
            start()

        } catch (e: Exception) {
            Logger.geoE("$TAG 重新启动地理围栏模块失败", e)
            Result.failure(e)
        }
    }

    /**
     * 执行地理围栏Profile
     */
    fun executeGeofenceProfile(geoInfo: JSONObject): Boolean {
        return try {
            if (getStatus() != ModuleStatus.RUNNING) {
                Logger.geoW("$TAG 地理围栏模块未运行，无法执行Profile")
                return false
            }

            Logger.geo("$TAG 执行地理围栏Profile")

            // 处理Profile消息
            val success = GeofenceHandler(context).handleGeoProfileMessage(geoInfo)

            if (success) {
                // 保存当前状态
                saveCurrentState()
                Logger.geo("$TAG 地理围栏Profile执行成功")
            } else {
                Logger.geoW("$TAG 地理围栏Profile执行失败")
            }

            success

        } catch (e: Exception) {
            Logger.geoE("$TAG 执行地理围栏Profile失败", e)
            false
        }
    }
}
