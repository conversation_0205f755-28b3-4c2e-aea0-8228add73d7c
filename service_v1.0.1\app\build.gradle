plugins {
    alias(libs.plugins.android.application)
    alias(libs.plugins.kotlin.android)
}

static def versionTag() {
    return new Date().format("yyyyMMdd", TimeZone.getTimeZone("Asia/Shanghai"))
}

android {
    signingConfigs {
        emulator {
            keyAlias "travel"
            storeFile file("../platform_emulator.jks")
            storePassword "travel2025"
            keyPassword "travel2025"
        }
        d30 {
            keyAlias "platform"
            storeFile file("../platform_d30.jks")
            storePassword "android"
            keyPassword "android"
        }
        p3000 {
            keyAlias "android"
            storeFile file("../platform_p3000.jks")
            storePassword "20250715"
            keyPassword "20250715"
        }
    }

    namespace 'com.dspread.mdm.service'
    compileSdk 34

    defaultConfig {
        applicationId "com.dspread.mdm.service"
        minSdk 21
        targetSdk 34
        versionCode 20
        versionName "1.1.01." + versionTag() + ".DSPREAD.MDM.SERVICE"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }

    buildTypes {
        release {
            minifyEnabled true
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
            signingConfig signingConfigs.d30
        }
        debug {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
            signingConfig signingConfigs.d30
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
    }
    kotlinOptions {
        jvmTarget = '17'
    }

    // 启用系统API访问
    useLibrary 'org.apache.http.legacy'

    // 编译选项 - 允许访问隐藏API
    lintOptions {
        checkReleaseBuilds false
        abortOnError false
    }
    applicationVariants.all { variant ->
        variant.outputs.all {
            def suffix = ''
            if (variant.buildType.name == 'release') {
                suffix = ''
            } else if (variant.buildType.name == 'debug') {
                suffix = '_debug'
            }
            outputFileName = "service_v${variant.versionName}${suffix}.apk"
        }
    }
}

dependencies {
    implementation libs.androidx.core.ktx
    implementation libs.androidx.appcompat
    implementation libs.material
    implementation libs.nv.websocket.client

    // 添加缺失的依赖
    implementation libs.gson
    implementation libs.kotlinx.coroutines.android
    implementation libs.kotlinx.coroutines.core

    // OkHttp for WebSocket
    implementation libs.okhttp

    // 系统jar包依赖
    implementation files('libs/dspread-sdkdevservice-aidl-1.0.2.jar')
    implementation files('libs/dspread-sdksysservice-aidl-1.0.1.jar')

    testImplementation libs.junit
    androidTestImplementation libs.androidx.junit
    androidTestImplementation libs.androidx.espresso.core
}