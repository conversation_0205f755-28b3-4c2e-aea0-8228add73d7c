package com.bbpos.wiseapp.service.contentprovider.db;

import android.content.Context;
import android.database.sqlite.SQLiteDatabase;
import android.database.sqlite.SQLiteOpenHelper;

public class CommonDataOpenHelper extends SQLiteOpenHelper{
	private static int VERSION=1;
	private static String GREATE_SQL="create table "+ DbConstants.DB_TABLE+
			"(_id INTEGER PRIMARY KEY," +
			"param_key varchar(32)," +  // 暂server_url  -生态使用url
			"param_value varchar(128))" ;

	public CommonDataOpenHelper(Context context) {
		super(context, DbConstants.DB_NAME, null, VERSION);
		// TODO Auto-generated constructor stub
	}

	@Override
	public void onCreate(SQLiteDatabase db) {
		// TODO Auto-generated method stub
		db.execSQL(GREATE_SQL);	
	}

	@Override
	public void onUpgrade(SQLiteDatabase arg0, int arg1, int arg2) {
		// TODO Auto-generated method stub
		
	}




}
