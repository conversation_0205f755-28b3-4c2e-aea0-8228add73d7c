package com.dspread.mdm.service.modules.logstream

import com.dspread.mdm.service.utils.log.Logger
import java.io.File
import java.io.FileReader
import java.io.FileWriter
import java.io.IOException
import java.util.concurrent.ConcurrentLinkedQueue

/**
 * 日志队列管理器
 */
object LogQueue {
    
    private const val TAG = "[LogQueue]"
    
    // 日志文件队列
    private val logFileQueue = ConcurrentLinkedQueue<String>()
    
    // 压缩文件队列
    private val gzFileQueue = ConcurrentLinkedQueue<String>()
    
    // 队列文件名
    private const val LOG_QUEUE_FILE = "log_queue.txt"
    private const val GZ_QUEUE_FILE = "gz_queue.txt"
    
    /**
     * 添加日志文件到队列
     */
    fun enqueue(logFilePath: String) {
        try {
            logFileQueue.offer(logFilePath)
            Logger.logStream("$TAG 添加日志文件到队列: $logFilePath")
            Logger.logStream("$TAG 当前日志队列长度: ${logFileQueue.size}")
        } catch (e: Exception) {
            Logger.logStreamE("$TAG 添加日志文件到队列失败", e)
        }
    }
    
    /**
     * 从队列中移除日志文件
     */
    fun dequeue(): String? {
        return try {
            val filePath = logFileQueue.poll()
            if (filePath != null) {
                Logger.logStream("$TAG 从队列中移除日志文件: $filePath")
                Logger.logStream("$TAG 当前日志队列长度: ${logFileQueue.size}")
            }
            filePath
        } catch (e: Exception) {
            Logger.logStreamE("$TAG 从队列中移除日志文件失败", e)
            null
        }
    }
    
    /**
     * 移除指定的日志文件
     */
    fun removeLogFile(logFilePath: String): Boolean {
        return try {
            val result = logFileQueue.remove(logFilePath)
            if (result) {
                Logger.logStream("$TAG 从队列中移除指定日志文件: $logFilePath")
                Logger.logStream("$TAG 当前日志队列长度: ${logFileQueue.size}")
            }
            result
        } catch (e: Exception) {
            Logger.logStreamE("$TAG 移除指定日志文件失败", e)
            false
        }
    }
    
    /**
     * 获取队列中的最后一个文件
     */
    fun getLastLogFile(): String? {
        return try {
            logFileQueue.lastOrNull()
        } catch (e: Exception) {
            Logger.logStreamE("$TAG 获取最后一个日志文件失败", e)
            null
        }
    }
    
    /**
     * 获取日志队列长度
     */
    fun getLogQueueLength(): Int {
        return logFileQueue.size
    }
    
    /**
     * 添加压缩文件到队列
     */
    fun enqueueGz(gzFilePath: String) {
        try {
            gzFileQueue.offer(gzFilePath)
            Logger.logStream("$TAG 添加压缩文件到队列: $gzFilePath")
            Logger.logStream("$TAG 当前压缩队列长度: ${gzFileQueue.size}")
        } catch (e: Exception) {
            Logger.logStreamE("$TAG 添加压缩文件到队列失败", e)
        }
    }
    
    /**
     * 从压缩队列中移除文件
     */
    fun dequeueGz(): String? {
        return try {
            val filePath = gzFileQueue.poll()
            if (filePath != null) {
                Logger.logStream("$TAG 从压缩队列中移除文件: $filePath")
                Logger.logStream("$TAG 当前压缩队列长度: ${gzFileQueue.size}")
            }
            filePath
        } catch (e: Exception) {
            Logger.logStreamE("$TAG 从压缩队列中移除文件失败", e)
            null
        }
    }
    
    /**
     * 获取压缩队列长度
     */
    fun getGzQueueLength(): Int {
        return gzFileQueue.size
    }
    
    /**
     * 保存日志队列到文件
     */
    fun saveLogQueueToFile(queueFile: File) {
        try {
            FileWriter(queueFile).use { writer ->
                for (filePath in logFileQueue) {
                    writer.write(filePath)
                    writer.write("\n")
                }
            }
            Logger.logStream("$TAG 日志队列已保存到文件: ${queueFile.name}")
        } catch (e: IOException) {
            Logger.logStreamE("$TAG 保存日志队列到文件失败", e)
        }
    }
    
    /**
     * 保存压缩队列到文件
     */
    fun saveGzQueueToFile(queueFile: File) {
        try {
            FileWriter(queueFile).use { writer ->
                for (filePath in gzFileQueue) {
                    writer.write(filePath)
                    writer.write("\n")
                }
            }
            Logger.logStream("$TAG 压缩队列已保存到文件: ${queueFile.name}")
        } catch (e: IOException) {
            Logger.logStreamE("$TAG 保存压缩队列到文件失败", e)
        }
    }
    
    /**
     * 从文件加载日志队列
     */
    fun loadLogQueueFromFile(queueFile: File) {
        if (!queueFile.exists()) {
            Logger.logStream("$TAG 日志队列文件不存在: ${queueFile.name}")
            return
        }
        
        try {
            FileReader(queueFile).use { reader ->
                reader.readLines().forEach { line ->
                    val filePath = line.trim()
                    if (filePath.isNotEmpty()) {
                        logFileQueue.offer(filePath)
                    }
                }
            }
            Logger.logStream("$TAG 从文件加载日志队列完成，队列长度: ${logFileQueue.size}")
        } catch (e: IOException) {
            Logger.logStreamE("$TAG 从文件加载日志队列失败", e)
        }
    }
    
    /**
     * 从文件加载压缩队列
     */
    fun loadGzQueueFromFile(queueFile: File) {
        if (!queueFile.exists()) {
            Logger.logStream("$TAG 压缩队列文件不存在: ${queueFile.name}")
            return
        }
        
        try {
            FileReader(queueFile).use { reader ->
                reader.readLines().forEach { line ->
                    val filePath = line.trim()
                    if (filePath.isNotEmpty()) {
                        gzFileQueue.offer(filePath)
                    }
                }
            }
            Logger.logStream("$TAG 从文件加载压缩队列完成，队列长度: ${gzFileQueue.size}")
        } catch (e: IOException) {
            Logger.logStreamE("$TAG 从文件加载压缩队列失败", e)
        }
    }
    
    /**
     * 清空所有队列
     */
    fun clearAll() {
        logFileQueue.clear()
        gzFileQueue.clear()
        Logger.logStream("$TAG 所有队列已清空")
    }
    
    /**
     * 获取所有日志文件路径
     */
    fun getAllLogFiles(): List<String> {
        return logFileQueue.toList()
    }
    
    /**
     * 获取所有压缩文件路径
     */
    fun getAllGzFiles(): List<String> {
        return gzFileQueue.toList()
    }

    /**
     * 获取日志队列实例
     */
    fun getLogQueue(): ConcurrentLinkedQueue<String> {
        return logFileQueue
    }

    /**
     * 获取压缩队列实例
     */
    fun getGzQueue(): ConcurrentLinkedQueue<String> {
        return gzFileQueue
    }

    /**
     * 保存日志队列到文件（无参数版本）
     */
    fun saveLogQueueToFile() {
        // 使用默认的日志目录
        val logDir = File("/sdcard/log")
        if (!logDir.exists()) {
            logDir.mkdirs()
        }
        val queueFile = File(logDir, LOG_QUEUE_FILE)
        saveLogQueueToFile(queueFile)
    }

    /**
     * 保存压缩队列到文件（无参数版本）
     */
    fun saveGzQueueToFile() {
        // 使用默认的日志目录
        val logDir = File("/sdcard/log")
        if (!logDir.exists()) {
            logDir.mkdirs()
        }
        val queueFile = File(logDir, GZ_QUEUE_FILE)
        saveGzQueueToFile(queueFile)
    }
}
