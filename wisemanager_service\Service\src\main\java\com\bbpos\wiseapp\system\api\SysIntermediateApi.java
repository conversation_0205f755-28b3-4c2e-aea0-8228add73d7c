package com.bbpos.wiseapp.system.api;

import android.content.Context;
import android.content.Intent;
import android.provider.Settings;
import android.util.Log;
import android.view.KeyEvent;

import com.bbpos.wiseapp.logger.BBLog;
import com.bbpos.wiseapp.service.common.RequestPermission;
import com.bbpos.wiseapp.service.common.UsualData;
import com.bbpos.wiseapp.tms.location.GpsLocationManager;
import com.bbpos.wiseapp.tms.utils.Constants;
import com.bbpos.wiseapp.tms.utils.ContextUtil;
import com.bbpos.wiseapp.utils.SharedPreferencesUtils;

import java.lang.reflect.Method;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;

public class SysIntermediateApi {
    private final static String TAG = "SysIntermediateApi";

    private Context mContext = null;
    private static SysIntermediateApi mSysIntermediateApi = null;

    public void init(Context context) {
        if (mContext == null) {
            mContext = context;
        }
    }

    private Context getContext() {
        return mContext;
    }

    public static SysIntermediateApi getIntance() {
        if (mSysIntermediateApi == null) {
            mSysIntermediateApi = new SysIntermediateApi();
            mSysIntermediateApi.init(ContextUtil.getInstance());
        }

        return mSysIntermediateApi;
    }

    public void updateSystemProperty(String kioskMode) {
        if ("false".equals(kioskMode)) {
            if (Constants.M_GEOFENCE_STATUS==GpsLocationManager.LOCK_SCREEN
             || Constants.M_GEOFENCE_STATUS==GpsLocationManager.WIPE_DATA
             || "true".equals(SharedPreferencesUtils.getSharePreferencesValue(UsualData.SHARED_PREFERENCES_KIOSK, "false"))) {
                return;
            }
        }
        if (DeviceInfoApi.getIntance().isWisePos5() || DeviceInfoApi.getIntance().isWisePos5Plus()
         || DeviceInfoApi.getIntance().isWisePosTouch() || DeviceInfoApi.getIntance().isWisePosTouchPlus()
         || DeviceInfoApi.getIntance().isWisePosLE() || DeviceInfoApi.getIntance().isWisePosLP()) {
            long utc = 0l;
            long utc_target = 0l;
            try {
                utc = Long.valueOf(SysIntermediateApi.getIntance().getProp("ro.build.date.utc"));
                utc_target = new SimpleDateFormat("yyyy-MM-dd").parse("2021-04-26").getTime();
                BBLog.i(BBLog.TAG, "==> ro.build.date.utc=" + utc + "   utc_target=" + utc_target);
            } catch (Exception e) {
                e.printStackTrace();
            }
            //判断ROM的编译时间晚于某个时间的，就确定使用bbpos_android.jar，可执行初始化
            if (utc*1000>utc_target) {
                if ("false".equals(kioskMode)) {
                    if (!DeviceInfoApi.getIntance().isShopifyDevice()) {
                        CustomServiceManager.getInstance().setBootupStatusBarVisible(true);
                    }
                    CustomServiceManager.getInstance().setBootupNavigationBarVisible(true);
                } else {
                    CustomServiceManager.getInstance().setBootupStatusBarVisible(false);
                    CustomServiceManager.getInstance().setBootupNavigationBarVisible(false);
                }
            } else {
                if ("false".equals(kioskMode)) {
//            setStatusBarKeyValid(true);
                    setStatusBarHide(false);
                } else {
//            setStatusBarKeyValid(false);
                    setStatusBarHide(true);
                }
            }
        } else {
            if ("false".equals(kioskMode)) {
//            setStatusBarKeyValid(true);
                setStatusBarHide(false);
            } else {
//            setStatusBarKeyValid(false);
                setStatusBarHide(true);
            }
        }

        setPowerKeyValid(true);
    }

    public void setStatusBarKeyValid(boolean enable) {
        BBLog.w(TAG, "setStatusBarKeyValid = " + enable);
        if (enable) {
            setKeyValue(KeyEvent.KEYCODE_MENU, 0);
            setKeyValue(KeyEvent.KEYCODE_HOME, 0);
            setKeyValue(KeyEvent.KEYCODE_BACK, 0);
        } else {
            setKeyValue(KeyEvent.KEYCODE_MENU, 1);
            setKeyValue(KeyEvent.KEYCODE_HOME, 1);
            setKeyValue(KeyEvent.KEYCODE_BACK, 0);
        }
    }

    public void setStatusBarHide(boolean enable) {
        BBLog.w(TAG, "setStatusBarHide = " + enable);
        if (enable) {
            Intent intent = new Intent("bbpos_hide_statusbar_navigation");
            getContext().sendBroadcast(intent, RequestPermission.REQUEST_PERMISSION_MY_BROADCAST);
        } else {
            Intent intent = new Intent("bbpos_show_statusbar_navigation");
            getContext().sendBroadcast(intent, RequestPermission.REQUEST_PERMISSION_MY_BROADCAST);
        }
    }

    public void setPowerKeyValid(boolean enable) {
        if (!DeviceInfoApi.getIntance().isWisePos4G()) {
            return;
        }

        BBLog.w(TAG, "setPowerKeyValid = " + enable);
        if (enable) {
            String buildVersion = getProp("ro.build.custom.version");
            BBLog.i(TAG, "ro.build.custom.version = " + buildVersion);
            buildVersion = buildVersion.substring(buildVersion.lastIndexOf("_")+1);
            BBLog.i(TAG, "buildVersion = " + buildVersion);
            String[] verlist = buildVersion.split("\\.");
            if (Integer.valueOf(verlist[0])==0 && Integer.valueOf(verlist[1]) == 21) {
                BBLog.i(TAG, "21 version");
                if (verlist[2].equals("2018120410")) {
                    Settings.System.putString(getContext().getContentResolver(), "can_get_into_sleep", "1");
                }
            } else if (Integer.valueOf(verlist[0])==0 && Integer.valueOf(verlist[1])<=27) {
                BBLog.i(TAG, "before 27 version");
                Settings.System.putString(getContext().getContentResolver(), "can_get_into_sleep", "1");
            } else {
                //新版本ROM使用该方式
                Intent statusbarIntent = new Intent("com.bbpos.powerkey_enable");
                statusbarIntent.putExtra("powerkey_enable", "1");
                getContext().sendBroadcast(statusbarIntent, RequestPermission.REQUEST_PERMISSION_MY_BROADCAST);
            }
        } else {
            String buildVersion = getProp("ro.build.custom.version");
            BBLog.i(TAG, "ro.build.custom.version = " + buildVersion);
            buildVersion = buildVersion.substring(buildVersion.lastIndexOf("_")+1);
            BBLog.i(TAG, "buildVersion = " + buildVersion);
            String[] verlist = buildVersion.split("\\.");
            if (Integer.valueOf(verlist[0])==0 && Integer.valueOf(verlist[1]) == 21) {
                BBLog.i(TAG, "21 version");
                if (verlist[2].equals("2018120410")) {
                    Settings.System.putString(getContext().getContentResolver(), "can_get_into_sleep", "0");
                }
            } else if (Integer.valueOf(verlist[0])==0 && Integer.valueOf(verlist[1])<=27) {
                BBLog.i(TAG, "before 27 version");
                Settings.System.putString(getContext().getContentResolver(), "can_get_into_sleep", "0");
            } else {
                //用于新版本ROM（注意）
                Intent statusbarIntent = new Intent("com.bbpos.powerkey_enable");
                statusbarIntent.putExtra("powerkey_enable", "0");
                getContext().sendBroadcast(statusbarIntent, RequestPermission.REQUEST_PERMISSION_MY_BROADCAST);
            }
        }
    }

	/**
	 * 获取终端安全补丁版本号
	 * @return
	 */
	public String getDeviceSecretPatchLevel(){
		String patchLevel = "";
		try {
			patchLevel = getProp("ro.build.version.security_patch");
			SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd", Locale.ENGLISH);
			Date date = dateFormat.parse(patchLevel);
			dateFormat = new SimpleDateFormat("d-MMM-yyyy", Locale.ENGLISH);
			patchLevel = dateFormat.format(date);
			BBLog.w(TAG, "getDeviceSecretPatchLevel = " + patchLevel );
		} catch (Exception e) {
			e.printStackTrace();
		}
		return patchLevel;
	}

    public String getProp(String propName) {
        Class<?> classType = null;
        String buildVersion = null;
        try {
            classType = Class.forName("android.os.SystemProperties");
            Method getMethod = classType.getDeclaredMethod("get", new Class<?>[]{String.class});
            buildVersion = (String) getMethod.invoke(classType, new Object[]{propName});
        } catch (Exception e) {
            e.printStackTrace();
        }

        return buildVersion;
    }

    public void setProp(String key, String value) {
        try {
            Class<?> c = Class.forName("android.os.SystemProperties");
            Method set = c.getMethod("set", String.class, String.class);
            set.invoke(c, key, value );
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void setKeyValue(int keycode, int state) {
        if (keycode != KeyEvent.KEYCODE_MENU) {
            Intent intent = new Intent(UsualData.DISABLE_USUAL_KEY);
            intent.putExtra("keycode", keycode);
            intent.putExtra("state", state);
            getContext().sendBroadcast(intent, RequestPermission.REQUEST_PERMISSION_MY_BROADCAST);
        } else {
            Intent intentMenu = new Intent(UsualData.DISABLE_RECENT_APPS);
            if (state == 1) {
                intentMenu.putExtra("disable_recent", true);
            } else {
                intentMenu.putExtra("disable_recent", false);
            }
            getContext().sendBroadcast(intentMenu, RequestPermission.REQUEST_PERMISSION_MY_BROADCAST);
        }
    }
}
