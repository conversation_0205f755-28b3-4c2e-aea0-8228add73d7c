[{"merged": "com.dspread.mdm.service.app-debug-31:/mipmap-mdpi_ic_launcher.webp.flat", "source": "com.dspread.mdm.service.app-main-33:/mipmap-mdpi/ic_launcher.webp"}, {"merged": "com.dspread.mdm.service.app-debug-31:/mipmap-mdpi_ic_launcher_round.webp.flat", "source": "com.dspread.mdm.service.app-main-33:/mipmap-mdpi/ic_launcher_round.webp"}, {"merged": "com.dspread.mdm.service.app-debug-31:/drawable_credit_card.xml.flat", "source": "com.dspread.mdm.service.app-main-33:/drawable/credit_card.xml"}, {"merged": "com.dspread.mdm.service.app-debug-31:/mipmap-xhdpi_ic_launcher_round.webp.flat", "source": "com.dspread.mdm.service.app-main-33:/mipmap-xhdpi/ic_launcher_round.webp"}, {"merged": "com.dspread.mdm.service.app-debug-31:/drawable_btn_bord_green.xml.flat", "source": "com.dspread.mdm.service.app-main-33:/drawable/btn_bord_green.xml"}, {"merged": "com.dspread.mdm.service.app-debug-31:/layout_view_reboot_float_window.xml.flat", "source": "com.dspread.mdm.service.app-main-33:/layout/view_reboot_float_window.xml"}, {"merged": "com.dspread.mdm.service.app-debug-31:/drawable_ic_lock.xml.flat", "source": "com.dspread.mdm.service.app-main-33:/drawable/ic_lock.xml"}, {"merged": "com.dspread.mdm.service.app-debug-31:/mipmap-xxxhdpi_ic_launcher.webp.flat", "source": "com.dspread.mdm.service.app-main-33:/mipmap-xxxhdpi/ic_launcher.webp"}, {"merged": "com.dspread.mdm.service.app-debug-31:/drawable_ic_reboot_warning.xml.flat", "source": "com.dspread.mdm.service.app-main-33:/drawable/ic_reboot_warning.xml"}, {"merged": "com.dspread.mdm.service.app-debug-31:/mipmap-xxxhdpi_ic_launcher_round.webp.flat", "source": "com.dspread.mdm.service.app-main-33:/mipmap-xxxhdpi/ic_launcher_round.webp"}, {"merged": "com.dspread.mdm.service.app-debug-31:/drawable_float_window_background.xml.flat", "source": "com.dspread.mdm.service.app-main-33:/drawable/float_window_background.xml"}, {"merged": "com.dspread.mdm.service.app-debug-31:/drawable_bg_shape.xml.flat", "source": "com.dspread.mdm.service.app-main-33:/drawable/bg_shape.xml"}, {"merged": "com.dspread.mdm.service.app-debug-31:/drawable_button_secondary.xml.flat", "source": "com.dspread.mdm.service.app-main-33:/drawable/button_secondary.xml"}, {"merged": "com.dspread.mdm.service.app-debug-31:/drawable-xxxhdpi_ic_launcher_foreground.png.flat", "source": "com.dspread.mdm.service.app-pngs-27:/drawable-xxxhdpi/ic_launcher_foreground.png"}, {"merged": "com.dspread.mdm.service.app-debug-31:/xml_network_security_config.xml.flat", "source": "com.dspread.mdm.service.app-main-33:/xml/network_security_config.xml"}, {"merged": "com.dspread.mdm.service.app-debug-31:/mipmap-xxhdpi_ic_launcher.webp.flat", "source": "com.dspread.mdm.service.app-main-33:/mipmap-xxhdpi/ic_launcher.webp"}, {"merged": "com.dspread.mdm.service.app-debug-31:/drawable_shape_corner_down.xml.flat", "source": "com.dspread.mdm.service.app-main-33:/drawable/shape_corner_down.xml"}, {"merged": "com.dspread.mdm.service.app-debug-31:/drawable-xxhdpi_ic_launcher_foreground.png.flat", "source": "com.dspread.mdm.service.app-pngs-27:/drawable-xxhdpi/ic_launcher_foreground.png"}, {"merged": "com.dspread.mdm.service.app-debug-31:/drawable_button_primary.xml.flat", "source": "com.dspread.mdm.service.app-main-33:/drawable/button_primary.xml"}, {"merged": "com.dspread.mdm.service.app-debug-31:/anim_dialog_exit.xml.flat", "source": "com.dspread.mdm.service.app-main-33:/anim/dialog_exit.xml"}, {"merged": "com.dspread.mdm.service.app-debug-31:/drawable_ic_notification.png.flat", "source": "com.dspread.mdm.service.app-main-33:/drawable/ic_notification.png"}, {"merged": "com.dspread.mdm.service.app-debug-31:/xml_backup_rules.xml.flat", "source": "com.dspread.mdm.service.app-main-33:/xml/backup_rules.xml"}, {"merged": "com.dspread.mdm.service.app-debug-31:/drawable_dialog_content_background.xml.flat", "source": "com.dspread.mdm.service.app-main-33:/drawable/dialog_content_background.xml"}, {"merged": "com.dspread.mdm.service.app-debug-31:/drawable_button_reboot_selector.xml.flat", "source": "com.dspread.mdm.service.app-main-33:/drawable/button_reboot_selector.xml"}, {"merged": "com.dspread.mdm.service.app-debug-31:/xml_data_extraction_rules.xml.flat", "source": "com.dspread.mdm.service.app-main-33:/xml/data_extraction_rules.xml"}, {"merged": "com.dspread.mdm.service.app-debug-31:/drawable_ic_warning_geofence.xml.flat", "source": "com.dspread.mdm.service.app-main-33:/drawable/ic_warning_geofence.xml"}, {"merged": "com.dspread.mdm.service.app-debug-31:/drawable_dialog_reboot_background.xml.flat", "source": "com.dspread.mdm.service.app-main-33:/drawable/dialog_reboot_background.xml"}, {"merged": "com.dspread.mdm.service.app-debug-31:/drawable-xhdpi_ic_launcher_foreground.png.flat", "source": "com.dspread.mdm.service.app-pngs-27:/drawable-xhdpi/ic_launcher_foreground.png"}, {"merged": "com.dspread.mdm.service.app-debug-31:/mipmap-anydpi-v26_ic_launcher.xml.flat", "source": "com.dspread.mdm.service.app-main-33:/mipmap-anydpi-v26/ic_launcher.xml"}, {"merged": "com.dspread.mdm.service.app-debug-31:/drawable_button_background.xml.flat", "source": "com.dspread.mdm.service.app-main-33:/drawable/button_background.xml"}, {"merged": "com.dspread.mdm.service.app-debug-31:/drawable_ic_launcher_background.xml.flat", "source": "com.dspread.mdm.service.app-main-33:/drawable/ic_launcher_background.xml"}, {"merged": "com.dspread.mdm.service.app-debug-31:/drawable-ldpi_ic_launcher_foreground.png.flat", "source": "com.dspread.mdm.service.app-pngs-27:/drawable-ldpi/ic_launcher_foreground.png"}, {"merged": "com.dspread.mdm.service.app-debug-31:/drawable_dialog_reboot_header_bg.xml.flat", "source": "com.dspread.mdm.service.app-main-33:/drawable/dialog_reboot_header_bg.xml"}, {"merged": "com.dspread.mdm.service.app-debug-31:/mipmap-hdpi_ic_launcher.webp.flat", "source": "com.dspread.mdm.service.app-main-33:/mipmap-hdpi/ic_launcher.webp"}, {"merged": "com.dspread.mdm.service.app-debug-31:/mipmap-hdpi_ic_launcher_round.webp.flat", "source": "com.dspread.mdm.service.app-main-33:/mipmap-hdpi/ic_launcher_round.webp"}, {"merged": "com.dspread.mdm.service.app-debug-31:/drawable_btn_bord_gray.xml.flat", "source": "com.dspread.mdm.service.app-main-33:/drawable/btn_bord_gray.xml"}, {"merged": "com.dspread.mdm.service.app-debug-31:/layout_activity_remote_view_test.xml.flat", "source": "com.dspread.mdm.service.app-main-33:/layout/activity_remote_view_test.xml"}, {"merged": "com.dspread.mdm.service.app-debug-31:/mipmap-anydpi-v26_ic_launcher_round.xml.flat", "source": "com.dspread.mdm.service.app-main-33:/mipmap-anydpi-v26/ic_launcher_round.xml"}, {"merged": "com.dspread.mdm.service.app-debug-31:/mipmap-xxhdpi_ic_launcher_round.webp.flat", "source": "com.dspread.mdm.service.app-main-33:/mipmap-xxhdpi/ic_launcher_round.webp"}, {"merged": "com.dspread.mdm.service.app-debug-31:/mipmap-xhdpi_ic_launcher.webp.flat", "source": "com.dspread.mdm.service.app-main-33:/mipmap-xhdpi/ic_launcher.webp"}, {"merged": "com.dspread.mdm.service.app-debug-31:/xml_accessibility_service_config.xml.flat", "source": "com.dspread.mdm.service.app-main-33:/xml/accessibility_service_config.xml"}, {"merged": "com.dspread.mdm.service.app-debug-31:/drawable_geo_warning.xml.flat", "source": "com.dspread.mdm.service.app-main-33:/drawable/geo_warning.xml"}, {"merged": "com.dspread.mdm.service.app-debug-31:/drawable-mdpi_ic_launcher_foreground.png.flat", "source": "com.dspread.mdm.service.app-pngs-27:/drawable-mdpi/ic_launcher_foreground.png"}, {"merged": "com.dspread.mdm.service.app-debug-31:/drawable_dialog_background.xml.flat", "source": "com.dspread.mdm.service.app-main-33:/drawable/dialog_background.xml"}, {"merged": "com.dspread.mdm.service.app-debug-31:/layout_activity_lock_screen.xml.flat", "source": "com.dspread.mdm.service.app-main-33:/layout/activity_lock_screen.xml"}, {"merged": "com.dspread.mdm.service.app-debug-31:/drawable_ic_timer.xml.flat", "source": "com.dspread.mdm.service.app-main-33:/drawable/ic_timer.xml"}, {"merged": "com.dspread.mdm.service.app-debug-31:/drawable_button_background_red.xml.flat", "source": "com.dspread.mdm.service.app-main-33:/drawable/button_background_red.xml"}, {"merged": "com.dspread.mdm.service.app-debug-31:/anim_dialog_enter.xml.flat", "source": "com.dspread.mdm.service.app-main-33:/anim/dialog_enter.xml"}, {"merged": "com.dspread.mdm.service.app-debug-31:/drawable-hdpi_ic_launcher_foreground.png.flat", "source": "com.dspread.mdm.service.app-pngs-27:/drawable-hdpi/ic_launcher_foreground.png"}, {"merged": "com.dspread.mdm.service.app-debug-31:/drawable_countdown_background.xml.flat", "source": "com.dspread.mdm.service.app-main-33:/drawable/countdown_background.xml"}, {"merged": "com.dspread.mdm.service.app-debug-31:/layout_dialog_os_upgrade.xml.flat", "source": "com.dspread.mdm.service.app-main-33:/layout/dialog_os_upgrade.xml"}, {"merged": "com.dspread.mdm.service.app-debug-31:/drawable_button_delay_selector.xml.flat", "source": "com.dspread.mdm.service.app-main-33:/drawable/button_delay_selector.xml"}, {"merged": "com.dspread.mdm.service.app-debug-31:/layout_dialog_reboot_warning.xml.flat", "source": "com.dspread.mdm.service.app-main-33:/layout/dialog_reboot_warning.xml"}, {"merged": "com.dspread.mdm.service.app-debug-31:/drawable_edit_text_background.xml.flat", "source": "com.dspread.mdm.service.app-main-33:/drawable/edit_text_background.xml"}, {"merged": "com.dspread.mdm.service.app-debug-31:/drawable-anydpi-v24_ic_launcher_foreground.xml.flat", "source": "com.dspread.mdm.service.app-pngs-27:/drawable-anydpi-v24/ic_launcher_foreground.xml"}, {"merged": "com.dspread.mdm.service.app-debug-31:/drawable_bg_countdown_warning.xml.flat", "source": "com.dspread.mdm.service.app-main-33:/drawable/bg_countdown_warning.xml"}]