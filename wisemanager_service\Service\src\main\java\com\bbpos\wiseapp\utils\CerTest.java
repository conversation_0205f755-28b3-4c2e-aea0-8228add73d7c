package com.bbpos.wiseapp.utils;

import com.bbpos.wiseapp.security.SecurityOperate;
import com.bbpos.wiseapp.tms.utils.ContextUtil;

public class CerTest {

    public static void main(String[] args) throws InterruptedException {
             CerTest test=new CerTest();
             //生成 keystore 文件
             test.getKeyStore();
             Thread.sleep(2000);
             //生成 *.cer 证书文件
             test.export();
    }

    public void execCommand(String[] arstringCommand) {  
        for (int i = 0; i < arstringCommand.length; i++) {  
            System.out.print(arstringCommand[i] + " ");  
        }  
        try {  
            Runtime.getRuntime().exec(arstringCommand);  
        } catch (Exception e) {
            System.out.println(e.getMessage());  
        }  
    }  

    public void execCommand(String arstringCommand) {  
        try {  
//            Runtime.getRuntime().exec(arstringCommand);
            SecurityOperate.getInstance().execCommand(ContextUtil.getInstance(),arstringCommand);
        } catch (Exception e) {
            System.out.println(e.getMessage());  
        }  
    }  
  
    /** 
     * 生成 *.keystore 
     */  
    public void getKeyStore() {  
        String[] arstringCommand = new String[] {  
  
//        "cmd ", "/k",  
//                "start", // cmd Shell命令  
//  
//                "keytool",  
//                "-genkey", // -genkey表示生成密钥  
//                "-validity", // -validity指定证书有效�?(单位：天)，这里是365�?  
//                "365",  
//                "-keysize",//     指定密钥长度  
//                "1024",  
//                "-alias", // -alias指定别名，这里是everygold 
//                "mdm",  
//                "-keyalg", // -keyalg 指定密钥的算�? (�? RSA DSA（如果不指定默认采用DSA�?)  
//                "RSA",  
//                "-keystore", // -keystore指定存储位置，这里是d:/leslie.keystore  
//                "d:/bbposmdm.keystore",  
//                "-dname",// CN=(名字与姓�?), OU=(组织单位名称), O=(组织名称), L=(城市或区域名�?),  
//                            // ST=(州或省份名称), C=(单位的两字母国家代码)"  
//                "CN=(roterlin), OU=(mdm), O=(bbpos), L=(Fuzhou), ST=(Fujian), C=(CN)",  
//                "-storepass", // 指定密钥库的密码(获取keystore信息�?�?的密�?)  
//                "123456",   
//                "-keypass",// 指定别名条目的密�?(私钥的密�?)  
//                "123456",   
//                "-v"// -v 显示密钥库中的证书详细信�?  
                
                
        		"cmd ", "/k",  
                "start", // cmd Shell命令  
  
                "keytool",  
                "-genkey", // -genkey表示生成密钥  
                "-validity", // -validity指定证书有效�?(单位：天)，这里是365�?  
                "1",  
                "-keysize",//     指定密钥长度  
                "1024",  
                "-alias", // -alias指定别名，这里是everygold 
                "wisemanager",  
                "-keyalg", // -keyalg 指定密钥的算�? (�? RSA DSA（如果不指定默认采用DSA�?)  
                "RSA",  
                "-keystore", // -keystore指定存储位置，这里是d:/leslie.keystore  
                "d:/test/bbposmdm.keystore",  
                "-dname",// CN=(名字与姓�?), OU=(组织单位名称), O=(组织名称), L=(城市或区域名�?),  
                            // ST=(州或省份名称), C=(单位的两字母国家代码)"  
                "CN=(queenieshi), OU=(huanhui), O=(bbpos), L=(Shenzhen), ST=(Guangdong), C=(US)",  
                "-storepass", // 指定密钥库的密码(获取keystore信息�?�?的密�?)  
                "a543210",   
                "-keypass",// 指定别名条目的密�?(私钥的密�?)  
                "a543210",   
                "-v"// -v 显示密钥库中的证书详细信�?  
        };  
        execCommand(arstringCommand);  
    }  
  
    /** 
     * 导出证书文件 
     */  
    public void export() {  
        String[] arstringCommand = new String[] {
//                "cmd ", "/k",  
//                "start", // cmd Shell命令  
//                "keytool",  
//                "-export", // - export指定为导出操�?   
//                "-keystore", // -keystore指定keystore文件，这里是d:/leslie.keystore  
//                "d:/bbposmdm.keystore",  
//                "-alias", // -alias指定别名，这里是ss  
//                "mdm",  
//                "-file",//-file指向导出路径  
//                "d:/bbposmdm.cer",  
//                "-storepass",// 指定密钥库的密码  
//                "123456"  
  
        "cmd ", "/k",  
                "start", // cmd Shell命令  
                "keytool",  
                "-export", // - export指定为导出操�?   
                "-keystore", // -keystore指定keystore文件，这里是d:/leslie.keystore  
                "d:/test/bbposmdm.keystore",  
                "-alias", // -alias指定别名，这里是ss  
                "wisemanager",  
                "-file",//-file指向导出路径  
                "d:/test/bbposmdm.cer",  
                "-storepass",// 指定密钥库的密码  
                "a543210"   
        };  
        execCommand(arstringCommand);  
    }
}
