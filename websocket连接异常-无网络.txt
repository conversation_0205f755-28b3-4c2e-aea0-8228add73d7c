--------- beginning of main
2025-08-21 15:44:34.699  3806-3806  Common                  com.dspread.mdm.service              D  🔧 TimerConfig ④服务守护定时器: 120秒
2025-08-21 15:44:34.702  3806-3806  Common                  com.dspread.mdm.service              D  🔧 TimerConfig ⑤初始化定时器: 43200秒
2025-08-21 15:44:34.706  3806-3806  Common                  com.dspread.mdm.service              D  🔧 TimerConfig ===============================================
2025-08-21 15:44:34.711  3806-3806  Common                  com.dspread.mdm.service              I  ✅ [SmartMdmBackgroundService] 所有定时器启动完成
2025-08-21 15:44:34.715  3806-3806  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 核心组件初始化完成
2025-08-21 15:44:34.750  3806-3873  Common                  com.dspread.mdm.service              D  🔧 Module LogStream Manager status changed to: RUNNING
2025-08-21 15:44:34.753  3806-3873  Common                  com.dspread.mdm.service              D  🔧 [ModuleManagerRegistry] LogStreamManager启动成功
2025-08-21 15:44:34.786  3806-3873  Common                  com.dspread.mdm.service              D  🔧 Module Geofence Manager status changed to: INITIALIZED
2025-08-21 15:44:34.790  3806-3873  Common                  com.dspread.mdm.service              D  🔧 Module Geofence Manager status changed to: STARTING
2025-08-21 15:44:34.819  3806-3806  Platform                com.dspread.mdm.service              D  🔧 [WiFiManagerApi] WiFiManagerApi 单例实例已创建
2025-08-21 15:44:34.820  3806-3873  Platform                com.dspread.mdm.service              D  🔧 用户交互监控已启动（简化版本）
2025-08-21 15:44:34.822  3806-3873  Platform                com.dspread.mdm.service              D  🔧 使用模拟用户交互检测
2025-08-21 15:44:34.825  3806-3873  Platform                com.dspread.mdm.service              D  🔧 用户交互监控已启动
2025-08-21 15:44:34.828  3806-3873  Service                 com.dspread.mdm.service              D  🔧 [GeofenceManager]Constants.geofenceStatus = 1
2025-08-21 15:44:34.830  3806-3873  Service                 com.dspread.mdm.service              D  🔧 [GeofenceManager]-- Constants.storeId =  -- Constants.storeSsid =  -- Constants.storeIp = 
2025-08-21 15:44:34.834  3806-3873  Common                  com.dspread.mdm.service              D  🔧 Module Geofence Manager status changed to: RUNNING
2025-08-21 15:44:34.837  3806-3873  Common                  com.dspread.mdm.service              D  🔧 [ModuleManagerRegistry] GeofenceManager启动成功
2025-08-21 15:44:34.852  3806-3873  RuleBase                com.dspread.mdm.service              D  🔧 RuleBaseManager 开始初始化RuleBaseManager...
2025-08-21 15:44:34.860  3806-3873  RuleBase                com.dspread.mdm.service              D  🔧 RuleBaseStorage RuleBase存储管理器初始化完成
2025-08-21 15:44:34.862  3806-3873  RuleBase                com.dspread.mdm.service              D  🔧 RuleStateMonitor 状态监控器初始化完成
2025-08-21 15:44:34.865  3806-3873  RuleBase                com.dspread.mdm.service              D  🔧 RuleBaseManager RuleBaseManager初始化完成
2025-08-21 15:44:34.868  3806-3873  Common                  com.dspread.mdm.service              D  🔧 [ModuleManagerRegistry] RuleBaseManager启动成功
2025-08-21 15:44:34.870  3806-3873  Common                  com.dspread.mdm.service              D  🔧 [ModuleManagerRegistry] 所有模块启动完成
2025-08-21 15:44:34.967  3806-3939  System.out              com.dspread.mdm.service              I  [com.mediatek.cta.CtaAdapter]:check permission begin!
2025-08-21 15:44:34.968  3806-3939  TrafficStats            com.dspread.mdm.service              D  tagSocket(107) with statsTag=0xffffffff, statsUid=-1
2025-08-21 15:44:35.217  3806-3940  System.out              com.dspread.mdm.service              I  [com.mediatek.cta.CtaAdapter]:check permission begin!
2025-08-21 15:44:35.218  3806-3940  TrafficStats            com.dspread.mdm.service              D  tagSocket(114) with statsTag=0xffffffff, statsUid=-1
2025-08-21 15:44:37.120  3806-3941  Common                  com.dspread.mdm.service              I  ✅ WebSocket 物理连接成功，等待服务器 hellMsg
2025-08-21 15:44:37.123  3806-3941  WebSocket               com.dspread.mdm.service              I  🔧 ===== 心跳发送器实际配置 =====
2025-08-21 15:44:37.127  3806-3941  WebSocket               com.dspread.mdm.service              I  🔧 实际 PING 发送间隔: 30000ms (30秒)
2025-08-21 15:44:37.130  3806-3941  WebSocket               com.dspread.mdm.service              I  🔧 PING 定时器名称: PingSender
2025-08-21 15:44:37.133  3806-3941  WebSocket               com.dspread.mdm.service              I  🔧 实际 PONG 发送间隔: 0ms (0秒)
2025-08-21 15:44:37.136  3806-3941  WebSocket               com.dspread.mdm.service              I  🔧 PONG 定时器名称: PongSender
2025-08-21 15:44:37.138  3806-3941  WebSocket               com.dspread.mdm.service              I  🔧 ===============================
2025-08-21 15:44:37.141  3806-3941  WebSocket               com.dspread.mdm.service              I  🔧 启动PING/PONG检测，阈值: 连续3个PING无PONG响应
2025-08-21 15:44:37.152  3806-3942  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接断开 - 服务器主动断开
2025-08-21 15:44:37.156  3806-3942  WebSocket               com.dspread.mdm.service              I  🔧    服务器关闭帧: code=1000, reason=无原因
2025-08-21 15:44:37.159  3806-3942  WebSocket               com.dspread.mdm.service              I  🔧    客户端关闭帧: code=1000, reason=无原因
2025-08-21 15:44:37.162  3806-3942  WebSocket               com.dspread.mdm.service              I  🔧    断开发起方: 服务器
2025-08-21 15:44:37.165  3806-3942  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 业务连接断开
2025-08-21 15:44:37.168  3806-3942  WebSocket               com.dspread.mdm.service              I  🔧 停止PING/PONG检测
2025-08-21 15:44:37.172  3806-3942  WebSocket               com.dspread.mdm.service              I  🔧 reconnect() 被调用，调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onDisconnected:746
2025-08-21 15:44:37.175  3806-3942  WebSocket               com.dspread.mdm.service              I  🔧 开始重连 WebSocket... (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onDisconnected:746)
2025-08-21 15:44:37.180  3806-3942  Provisioning            com.dspread.mdm.service              W  ⚠️ 本地配置格式不正确，期望完整的API响应格式
2025-08-21 15:44:37.184  3806-3942  WebSocket               com.dspread.mdm.service              I  🔧 使用Provisioning配置的重连策略: delayPolicy=1, delaySwitch=1, delayTime=60
2025-08-21 15:44:37.187  3806-3942  WebSocket               com.dspread.mdm.service              I  🔧 当前断开记录数: 1, 延迟开关: 1
2025-08-21 15:44:37.190  3806-3942  WebSocket               com.dspread.mdm.service              I  🔧 重连策略计算完成: 第1次重连，间隔3000ms (3秒)
2025-08-21 15:44:37.193  3806-3942  WebSocket               com.dspread.mdm.service              I  🔧 开始第1次重连，间隔3000ms (3秒) (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onDisconnected:746)
2025-08-21 15:44:40.209  3806-3806  WebSocket               com.dspread.mdm.service              I  🔧 重连 WebSocket: wss://api.dspreadserv.net/status/websocket/register?serialNo=MDAwMDE1MDRQMzAwMDExNTYwNw==&publicKey=TUlHZk1BMEdDU3FHU0liM0RRRUJBUVVBQTRHTkFEQ0JpUUtCZ1FDd3ExK1UxdWhLbnZNY2ZPN3JCZDE3UUUvOWtiNkhzb2hwOU1HYVdGb0RNUlMwRmIzZ1pHeDFHSk9aemd4MzNqZjlVblgrV3FmZndIazBMVVozTzJZVjdpY3pjRkw1bGxoajZ1VFJNS3lZek1Uc1dKR1lIUjZrRVkraCtmd3Z5WUZrVlRVZnRvcWNwVHZOKzlYSDFmM3pMQms0UkNSSEx4SWxOUkd1eHZYYTF3SURBUUFC&query=0&msgVer=3&timestamp=1755762280198&signature=dXWygQ1PtiKlFKpG4SYHarpYSq7BX8PlVoiNxnMjnN5bgtbA00rQQ0jPDfv1G1+aNDb9EKa9dct1l8JqfRmjT6D6zqyHTJTR5GWzkqFXfaHbacofIXKAiNlTZY4D/oWanCUn5LpW+cZEGKs6I4FmRFsP+CLw69NzLC+cl53nSVE=
2025-08-21 15:44:40.213  3806-3806  WebSocket               com.dspread.mdm.service              I  🔧 未检测到系统代理设置
2025-08-21 15:44:40.224  3806-3806  Provisioning            com.dspread.mdm.service              W  ⚠️ 本地配置格式不正确，期望完整的API响应格式
2025-08-21 15:44:40.237  3806-3947  System.out              com.dspread.mdm.service              I  [com.mediatek.cta.CtaAdapter]:check permission begin!
2025-08-21 15:44:40.238  3806-3947  TrafficStats            com.dspread.mdm.service              D  tagSocket(107) with statsTag=0xffffffff, statsUid=-1
2025-08-21 15:44:40.488  3806-3948  System.out              com.dspread.mdm.service              I  [com.mediatek.cta.CtaAdapter]:check permission begin!
2025-08-21 15:44:40.489  3806-3948  TrafficStats            com.dspread.mdm.service              D  tagSocket(119) with statsTag=0xffffffff, statsUid=-1
2025-08-21 15:44:42.031  3806-3949  Common                  com.dspread.mdm.service              I  ✅ WebSocket 物理连接成功，等待服务器 hellMsg
2025-08-21 15:44:42.035  3806-3949  WebSocket               com.dspread.mdm.service              I  🔧 ===== 心跳发送器实际配置 =====
2025-08-21 15:44:42.038  3806-3949  WebSocket               com.dspread.mdm.service              I  🔧 实际 PING 发送间隔: 30000ms (30秒)
2025-08-21 15:44:42.042  3806-3949  WebSocket               com.dspread.mdm.service              I  🔧 PING 定时器名称: PingSender
2025-08-21 15:44:42.046  3806-3949  WebSocket               com.dspread.mdm.service              I  🔧 实际 PONG 发送间隔: 0ms (0秒)
2025-08-21 15:44:42.049  3806-3949  WebSocket               com.dspread.mdm.service              I  🔧 PONG 定时器名称: PongSender
2025-08-21 15:44:42.052  3806-3949  WebSocket               com.dspread.mdm.service              I  🔧 ===============================
2025-08-21 15:44:42.055  3806-3949  WebSocket               com.dspread.mdm.service              I  🔧 启动PING/PONG检测，阈值: 连续3个PING无PONG响应
2025-08-21 15:44:42.068  3806-3950  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接断开 - 服务器主动断开
2025-08-21 15:44:42.072  3806-3950  WebSocket               com.dspread.mdm.service              I  🔧    服务器关闭帧: code=1000, reason=无原因
2025-08-21 15:44:42.075  3806-3950  WebSocket               com.dspread.mdm.service              I  🔧    客户端关闭帧: code=1000, reason=无原因
2025-08-21 15:44:42.080  3806-3950  WebSocket               com.dspread.mdm.service              I  🔧    断开发起方: 服务器
2025-08-21 15:44:42.084  3806-3950  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 业务连接断开
2025-08-21 15:44:42.089  3806-3950  WebSocket               com.dspread.mdm.service              I  🔧 停止PING/PONG检测
2025-08-21 15:44:42.093  3806-3950  WebSocket               com.dspread.mdm.service              I  🔧 reconnect() 被调用，调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onDisconnected:746
2025-08-21 15:44:42.097  3806-3950  WebSocket               com.dspread.mdm.service              I  🔧 开始重连 WebSocket... (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onDisconnected:746)
2025-08-21 15:44:42.105  3806-3950  Provisioning            com.dspread.mdm.service              W  ⚠️ 本地配置格式不正确，期望完整的API响应格式
2025-08-21 15:44:42.112  3806-3950  WebSocket               com.dspread.mdm.service              I  🔧 使用Provisioning配置的重连策略: delayPolicy=1, delaySwitch=1, delayTime=60
2025-08-21 15:44:42.116  3806-3950  WebSocket               com.dspread.mdm.service              I  🔧 当前断开记录数: 2, 延迟开关: 1
2025-08-21 15:44:42.120  3806-3950  WebSocket               com.dspread.mdm.service              I  🔧 重连策略计算完成: 第2次重连，间隔3000ms (3秒)
2025-08-21 15:44:42.124  3806-3950  WebSocket               com.dspread.mdm.service              I  🔧 开始第2次重连，间隔3000ms (3秒) (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onDisconnected:746)
2025-08-21 15:44:42.134  3806-3824  System                  com.dspread.mdm.service              W  A resource failed to call close. 
2025-08-21 15:44:42.137  3806-3824  System                  com.dspread.mdm.service              W  A resource failed to call close. 
--------- beginning of system
2025-08-21 15:44:45.143  3806-3806  WebSocket               com.dspread.mdm.service              I  🔧 重连 WebSocket: wss://api.dspreadserv.net/status/websocket/register?serialNo=MDAwMDE1MDRQMzAwMDExNTYwNw==&publicKey=TUlHZk1BMEdDU3FHU0liM0RRRUJBUVVBQTRHTkFEQ0JpUUtCZ1FDd3ExK1UxdWhLbnZNY2ZPN3JCZDE3UUUvOWtiNkhzb2hwOU1HYVdGb0RNUlMwRmIzZ1pHeDFHSk9aemd4MzNqZjlVblgrV3FmZndIazBMVVozTzJZVjdpY3pjRkw1bGxoajZ1VFJNS3lZek1Uc1dKR1lIUjZrRVkraCtmd3Z5WUZrVlRVZnRvcWNwVHZOKzlYSDFmM3pMQms0UkNSSEx4SWxOUkd1eHZYYTF3SURBUUFC&query=0&msgVer=3&timestamp=1755762285132&signature=Kges1M2ahob68hk0sK3RLwFyoEW1jDI59ehW9aPA3skLn5Se5iIBuovlqxPR6Z/p+1wS8pJnXOTZw96Jh34TyzycCyx1/wzr2hbZeGabc4/hFjt1tAFi+Mxd4r4bhT7iFm3UfLGE0vs5Np2dhiRm8380fwao5mQPvpgvqoJE4rw=
2025-08-21 15:44:45.147  3806-3806  WebSocket               com.dspread.mdm.service              I  🔧 未检测到系统代理设置
2025-08-21 15:44:45.158  3806-3806  Provisioning            com.dspread.mdm.service              W  ⚠️ 本地配置格式不正确，期望完整的API响应格式
2025-08-21 15:44:45.173  3806-3955  System.out              com.dspread.mdm.service              I  [com.mediatek.cta.CtaAdapter]:check permission begin!
2025-08-21 15:44:45.173  3806-3955  TrafficStats            com.dspread.mdm.service              D  tagSocket(107) with statsTag=0xffffffff, statsUid=-1
2025-08-21 15:44:45.423  3806-3956  System.out              com.dspread.mdm.service              I  [com.mediatek.cta.CtaAdapter]:check permission begin!
2025-08-21 15:44:45.424  3806-3956  TrafficStats            com.dspread.mdm.service              D  tagSocket(114) with statsTag=0xffffffff, statsUid=-1
2025-08-21 15:44:47.355  3806-3957  Common                  com.dspread.mdm.service              I  ✅ WebSocket 物理连接成功，等待服务器 hellMsg
2025-08-21 15:44:47.359  3806-3957  WebSocket               com.dspread.mdm.service              I  🔧 ===== 心跳发送器实际配置 =====
2025-08-21 15:44:47.362  3806-3957  WebSocket               com.dspread.mdm.service              I  🔧 实际 PING 发送间隔: 30000ms (30秒)
2025-08-21 15:44:47.365  3806-3957  WebSocket               com.dspread.mdm.service              I  🔧 PING 定时器名称: PingSender
2025-08-21 15:44:47.368  3806-3957  WebSocket               com.dspread.mdm.service              I  🔧 实际 PONG 发送间隔: 0ms (0秒)
2025-08-21 15:44:47.371  3806-3957  WebSocket               com.dspread.mdm.service              I  🔧 PONG 定时器名称: PongSender
2025-08-21 15:44:47.374  3806-3957  WebSocket               com.dspread.mdm.service              I  🔧 ===============================
2025-08-21 15:44:47.376  3806-3957  WebSocket               com.dspread.mdm.service              I  🔧 启动PING/PONG检测，阈值: 连续3个PING无PONG响应
2025-08-21 15:44:47.384  3806-3958  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接断开 - 服务器主动断开
2025-08-21 15:44:47.387  3806-3958  WebSocket               com.dspread.mdm.service              I  🔧    服务器关闭帧: code=1000, reason=无原因
2025-08-21 15:44:47.390  3806-3958  WebSocket               com.dspread.mdm.service              I  🔧    客户端关闭帧: code=1000, reason=无原因
2025-08-21 15:44:47.393  3806-3958  WebSocket               com.dspread.mdm.service              I  🔧    断开发起方: 服务器
2025-08-21 15:44:47.396  3806-3958  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 业务连接断开
2025-08-21 15:44:47.399  3806-3958  WebSocket               com.dspread.mdm.service              I  🔧 停止PING/PONG检测
2025-08-21 15:44:47.402  3806-3958  WebSocket               com.dspread.mdm.service              I  🔧 reconnect() 被调用，调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onDisconnected:746
2025-08-21 15:44:47.405  3806-3958  WebSocket               com.dspread.mdm.service              I  🔧 开始重连 WebSocket... (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onDisconnected:746)
2025-08-21 15:44:47.411  3806-3958  Provisioning            com.dspread.mdm.service              W  ⚠️ 本地配置格式不正确，期望完整的API响应格式
2025-08-21 15:44:47.414  3806-3958  WebSocket               com.dspread.mdm.service              I  🔧 使用Provisioning配置的重连策略: delayPolicy=1, delaySwitch=1, delayTime=60
2025-08-21 15:44:47.417  3806-3958  WebSocket               com.dspread.mdm.service              I  🔧 当前断开记录数: 3, 延迟开关: 1
2025-08-21 15:44:47.420  3806-3958  WebSocket               com.dspread.mdm.service              I  🔧 重连策略计算完成: 第3次重连，间隔3000ms (3秒)
2025-08-21 15:44:47.423  3806-3958  WebSocket               com.dspread.mdm.service              I  🔧 开始第3次重连，间隔3000ms (3秒) (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onDisconnected:746)
2025-08-21 15:44:50.439  3806-3806  WebSocket               com.dspread.mdm.service              I  🔧 重连 WebSocket: wss://api.dspreadserv.net/status/websocket/register?serialNo=MDAwMDE1MDRQMzAwMDExNTYwNw==&publicKey=TUlHZk1BMEdDU3FHU0liM0RRRUJBUVVBQTRHTkFEQ0JpUUtCZ1FDd3ExK1UxdWhLbnZNY2ZPN3JCZDE3UUUvOWtiNkhzb2hwOU1HYVdGb0RNUlMwRmIzZ1pHeDFHSk9aemd4MzNqZjlVblgrV3FmZndIazBMVVozTzJZVjdpY3pjRkw1bGxoajZ1VFJNS3lZek1Uc1dKR1lIUjZrRVkraCtmd3Z5WUZrVlRVZnRvcWNwVHZOKzlYSDFmM3pMQms0UkNSSEx4SWxOUkd1eHZYYTF3SURBUUFC&query=0&msgVer=3&timestamp=1755762290428&signature=Ohju4tMGHfwu7q+96JJlY296F+pE+D4EUExWyTQ/C3fsK9tpkoAmdNPvXf3Up831Z3nWF7vNsE4RyQKrG3uEqtKc0uSb0OtIlPZ6/wLet4UVD2mButBRligFlzNzFlAk/nnGKBVYjLOlGX55+fBOK16Rwp78KLU6nmp22Kln/5Y=
2025-08-21 15:44:50.443  3806-3806  WebSocket               com.dspread.mdm.service              I  🔧 未检测到系统代理设置
2025-08-21 15:44:50.455  3806-3806  Provisioning            com.dspread.mdm.service              W  ⚠️ 本地配置格式不正确，期望完整的API响应格式
2025-08-21 15:44:50.469  3806-3963  System.out              com.dspread.mdm.service              I  [com.mediatek.cta.CtaAdapter]:check permission begin!
2025-08-21 15:44:50.470  3806-3963  TrafficStats            com.dspread.mdm.service              D  tagSocket(114) with statsTag=0xffffffff, statsUid=-1
2025-08-21 15:44:50.720  3806-3964  System.out              com.dspread.mdm.service              I  [com.mediatek.cta.CtaAdapter]:check permission begin!
2025-08-21 15:44:50.721  3806-3964  TrafficStats            com.dspread.mdm.service              D  tagSocket(118) with statsTag=0xffffffff, statsUid=-1
2025-08-21 15:44:52.884  3806-3966  Common                  com.dspread.mdm.service              I  ✅ WebSocket 物理连接成功，等待服务器 hellMsg
2025-08-21 15:44:52.888  3806-3966  WebSocket               com.dspread.mdm.service              I  🔧 ===== 心跳发送器实际配置 =====
2025-08-21 15:44:52.891  3806-3966  WebSocket               com.dspread.mdm.service              I  🔧 实际 PING 发送间隔: 30000ms (30秒)
2025-08-21 15:44:52.894  3806-3966  WebSocket               com.dspread.mdm.service              I  🔧 PING 定时器名称: PingSender
2025-08-21 15:44:52.897  3806-3966  WebSocket               com.dspread.mdm.service              I  🔧 实际 PONG 发送间隔: 0ms (0秒)
2025-08-21 15:44:52.900  3806-3966  WebSocket               com.dspread.mdm.service              I  🔧 PONG 定时器名称: PongSender
2025-08-21 15:44:52.903  3806-3966  WebSocket               com.dspread.mdm.service              I  🔧 ===============================
2025-08-21 15:44:52.905  3806-3966  WebSocket               com.dspread.mdm.service              I  🔧 启动PING/PONG检测，阈值: 连续3个PING无PONG响应
2025-08-21 15:44:52.912  3806-3966  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接断开 - 服务器主动断开
2025-08-21 15:44:52.915  3806-3966  WebSocket               com.dspread.mdm.service              I  🔧    服务器关闭帧: code=1000, reason=无原因
2025-08-21 15:44:52.918  3806-3966  WebSocket               com.dspread.mdm.service              I  🔧    客户端关闭帧: code=1000, reason=无原因
2025-08-21 15:44:52.920  3806-3966  WebSocket               com.dspread.mdm.service              I  🔧    断开发起方: 服务器
2025-08-21 15:44:52.923  3806-3966  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 业务连接断开
2025-08-21 15:44:52.926  3806-3966  WebSocket               com.dspread.mdm.service              I  🔧 停止PING/PONG检测
2025-08-21 15:44:52.929  3806-3966  WebSocket               com.dspread.mdm.service              I  🔧 reconnect() 被调用，调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onDisconnected:746
2025-08-21 15:44:52.931  3806-3966  WebSocket               com.dspread.mdm.service              I  🔧 开始重连 WebSocket... (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onDisconnected:746)
2025-08-21 15:44:52.936  3806-3966  Provisioning            com.dspread.mdm.service              W  ⚠️ 本地配置格式不正确，期望完整的API响应格式
2025-08-21 15:44:52.939  3806-3966  WebSocket               com.dspread.mdm.service              I  🔧 使用Provisioning配置的重连策略: delayPolicy=1, delaySwitch=1, delayTime=60
2025-08-21 15:44:52.942  3806-3966  WebSocket               com.dspread.mdm.service              I  🔧 当前断开记录数: 4, 延迟开关: 1
2025-08-21 15:44:52.944  3806-3966  WebSocket               com.dspread.mdm.service              I  🔧 重连策略计算完成: 第4次重连，间隔6000ms (6秒)
2025-08-21 15:44:52.947  3806-3966  WebSocket               com.dspread.mdm.service              I  🔧 开始第4次重连，间隔6000ms (6秒) (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onDisconnected:746)
2025-08-21 15:44:58.963  3806-3806  WebSocket               com.dspread.mdm.service              I  🔧 重连 WebSocket: wss://api.dspreadserv.net/status/websocket/register?serialNo=MDAwMDE1MDRQMzAwMDExNTYwNw==&publicKey=TUlHZk1BMEdDU3FHU0liM0RRRUJBUVVBQTRHTkFEQ0JpUUtCZ1FDd3ExK1UxdWhLbnZNY2ZPN3JCZDE3UUUvOWtiNkhzb2hwOU1HYVdGb0RNUlMwRmIzZ1pHeDFHSk9aemd4MzNqZjlVblgrV3FmZndIazBMVVozTzJZVjdpY3pjRkw1bGxoajZ1VFJNS3lZek1Uc1dKR1lIUjZrRVkraCtmd3Z5WUZrVlRVZnRvcWNwVHZOKzlYSDFmM3pMQms0UkNSSEx4SWxOUkd1eHZYYTF3SURBUUFC&query=0&msgVer=3&timestamp=1755762298952&signature=OHntDdzlPy59uWtbwhbDI9V+ZC2cgA8x4mclIKfhASM5McEsXtRPBQ4fx6O24/xu5c8vjdXM+LHk1fagoveOJtikgIAZZlYUCUZZ8CmrZBDbqRP3kRf8WzRBiXHNBJ2LfWJ6g4gIm5/EXvkVg4hpgBAnYfet2TfIfkFfJm2+Q4g=
2025-08-21 15:44:58.967  3806-3806  WebSocket               com.dspread.mdm.service              I  🔧 未检测到系统代理设置
2025-08-21 15:44:58.978  3806-3806  Provisioning            com.dspread.mdm.service              W  ⚠️ 本地配置格式不正确，期望完整的API响应格式
2025-08-21 15:44:58.992  3806-3971  System.out              com.dspread.mdm.service              I  [com.mediatek.cta.CtaAdapter]:check permission begin!
2025-08-21 15:44:58.993  3806-3971  TrafficStats            com.dspread.mdm.service              D  tagSocket(114) with statsTag=0xffffffff, statsUid=-1
2025-08-21 15:44:59.243  3806-3972  System.out              com.dspread.mdm.service              I  [com.mediatek.cta.CtaAdapter]:check permission begin!
2025-08-21 15:44:59.244  3806-3972  TrafficStats            com.dspread.mdm.service              D  tagSocket(123) with statsTag=0xffffffff, statsUid=-1
2025-08-21 15:45:01.384  3806-3973  Common                  com.dspread.mdm.service              I  ✅ WebSocket 物理连接成功，等待服务器 hellMsg
2025-08-21 15:45:01.387  3806-3973  WebSocket               com.dspread.mdm.service              I  🔧 ===== 心跳发送器实际配置 =====
2025-08-21 15:45:01.390  3806-3973  WebSocket               com.dspread.mdm.service              I  🔧 实际 PING 发送间隔: 30000ms (30秒)
2025-08-21 15:45:01.393  3806-3973  WebSocket               com.dspread.mdm.service              I  🔧 PING 定时器名称: PingSender
2025-08-21 15:45:01.396  3806-3973  WebSocket               com.dspread.mdm.service              I  🔧 实际 PONG 发送间隔: 0ms (0秒)
2025-08-21 15:45:01.399  3806-3973  WebSocket               com.dspread.mdm.service              I  🔧 PONG 定时器名称: PongSender
2025-08-21 15:45:01.402  3806-3973  WebSocket               com.dspread.mdm.service              I  🔧 ===============================
2025-08-21 15:45:01.404  3806-3973  WebSocket               com.dspread.mdm.service              I  🔧 启动PING/PONG检测，阈值: 连续3个PING无PONG响应
2025-08-21 15:45:01.411  3806-3974  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接断开 - 服务器主动断开
2025-08-21 15:45:01.414  3806-3974  WebSocket               com.dspread.mdm.service              I  🔧    服务器关闭帧: code=1000, reason=无原因
2025-08-21 15:45:01.417  3806-3974  WebSocket               com.dspread.mdm.service              I  🔧    客户端关闭帧: code=1000, reason=无原因
2025-08-21 15:45:01.420  3806-3974  WebSocket               com.dspread.mdm.service              I  🔧    断开发起方: 服务器
2025-08-21 15:45:01.423  3806-3974  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 业务连接断开
2025-08-21 15:45:01.425  3806-3974  WebSocket               com.dspread.mdm.service              I  🔧 停止PING/PONG检测
2025-08-21 15:45:01.428  3806-3974  WebSocket               com.dspread.mdm.service              I  🔧 reconnect() 被调用，调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onDisconnected:746
2025-08-21 15:45:01.431  3806-3974  WebSocket               com.dspread.mdm.service              I  🔧 开始重连 WebSocket... (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onDisconnected:746)
2025-08-21 15:45:01.436  3806-3974  Provisioning            com.dspread.mdm.service              W  ⚠️ 本地配置格式不正确，期望完整的API响应格式
2025-08-21 15:45:01.439  3806-3974  WebSocket               com.dspread.mdm.service              I  🔧 使用Provisioning配置的重连策略: delayPolicy=1, delaySwitch=1, delayTime=60
2025-08-21 15:45:01.442  3806-3974  WebSocket               com.dspread.mdm.service              I  🔧 当前断开记录数: 5, 延迟开关: 1
2025-08-21 15:45:01.444  3806-3974  WebSocket               com.dspread.mdm.service              I  🔧 重连策略计算完成: 第5次重连，间隔9000ms (9秒)
2025-08-21 15:45:01.447  3806-3974  WebSocket               com.dspread.mdm.service              I  🔧 开始第5次重连，间隔9000ms (9秒) (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onDisconnected:746)
2025-08-21 15:45:10.472  3806-3806  WebSocket               com.dspread.mdm.service              I  🔧 重连 WebSocket: wss://api.dspreadserv.net/status/websocket/register?serialNo=MDAwMDE1MDRQMzAwMDExNTYwNw==&publicKey=TUlHZk1BMEdDU3FHU0liM0RRRUJBUVVBQTRHTkFEQ0JpUUtCZ1FDd3ExK1UxdWhLbnZNY2ZPN3JCZDE3UUUvOWtiNkhzb2hwOU1HYVdGb0RNUlMwRmIzZ1pHeDFHSk9aemd4MzNqZjlVblgrV3FmZndIazBMVVozTzJZVjdpY3pjRkw1bGxoajZ1VFJNS3lZek1Uc1dKR1lIUjZrRVkraCtmd3Z5WUZrVlRVZnRvcWNwVHZOKzlYSDFmM3pMQms0UkNSSEx4SWxOUkd1eHZYYTF3SURBUUFC&query=0&msgVer=3&timestamp=1755762310461&signature=gtGo1vYyN0DquocHbpVmby01pa/8FB9zd3Pfy8KnODX2NNo4baNm0U+JWfSzE3oHyHBPVw6Zgv2gF7xTNDgK296EbWtWzCusSuPv3Y0j26SuphRSIpNlYDOI79wMjJU1jDx+Cas9GOJvGDuFq3mwuUgZ3Ty1b/WMFTDdwtSrFFY=
2025-08-21 15:45:10.476  3806-3806  WebSocket               com.dspread.mdm.service              I  🔧 未检测到系统代理设置
2025-08-21 15:45:10.488  3806-3806  Provisioning            com.dspread.mdm.service              W  ⚠️ 本地配置格式不正确，期望完整的API响应格式
2025-08-21 15:45:10.502  3806-3979  System.out              com.dspread.mdm.service              I  [com.mediatek.cta.CtaAdapter]:check permission begin!
2025-08-21 15:45:10.503  3806-3979  TrafficStats            com.dspread.mdm.service              D  tagSocket(123) with statsTag=0xffffffff, statsUid=-1
2025-08-21 15:45:10.752  3806-3980  System.out              com.dspread.mdm.service              I  [com.mediatek.cta.CtaAdapter]:check permission begin!
2025-08-21 15:45:10.753  3806-3980  TrafficStats            com.dspread.mdm.service              D  tagSocket(128) with statsTag=0xffffffff, statsUid=-1
2025-08-21 15:45:12.853  3806-3981  Common                  com.dspread.mdm.service              I  ✅ WebSocket 物理连接成功，等待服务器 hellMsg
2025-08-21 15:45:12.856  3806-3981  WebSocket               com.dspread.mdm.service              I  🔧 ===== 心跳发送器实际配置 =====
2025-08-21 15:45:12.860  3806-3981  WebSocket               com.dspread.mdm.service              I  🔧 实际 PING 发送间隔: 30000ms (30秒)
2025-08-21 15:45:12.862  3806-3981  WebSocket               com.dspread.mdm.service              I  🔧 PING 定时器名称: PingSender
2025-08-21 15:45:12.865  3806-3981  WebSocket               com.dspread.mdm.service              I  🔧 实际 PONG 发送间隔: 0ms (0秒)
2025-08-21 15:45:12.868  3806-3981  WebSocket               com.dspread.mdm.service              I  🔧 PONG 定时器名称: PongSender
2025-08-21 15:45:12.871  3806-3981  WebSocket               com.dspread.mdm.service              I  🔧 ===============================
2025-08-21 15:45:12.874  3806-3981  WebSocket               com.dspread.mdm.service              I  🔧 启动PING/PONG检测，阈值: 连续3个PING无PONG响应
2025-08-21 15:45:12.882  3806-3982  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接断开 - 服务器主动断开
2025-08-21 15:45:12.885  3806-3982  WebSocket               com.dspread.mdm.service              I  🔧    服务器关闭帧: code=1000, reason=无原因
2025-08-21 15:45:12.888  3806-3982  WebSocket               com.dspread.mdm.service              I  🔧    客户端关闭帧: code=1000, reason=无原因
2025-08-21 15:45:12.891  3806-3982  WebSocket               com.dspread.mdm.service              I  🔧    断开发起方: 服务器
2025-08-21 15:45:12.894  3806-3982  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 业务连接断开
2025-08-21 15:45:12.897  3806-3982  WebSocket               com.dspread.mdm.service              I  🔧 停止PING/PONG检测
2025-08-21 15:45:12.900  3806-3982  WebSocket               com.dspread.mdm.service              I  🔧 reconnect() 被调用，调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onDisconnected:746
2025-08-21 15:45:12.903  3806-3982  WebSocket               com.dspread.mdm.service              I  🔧 开始重连 WebSocket... (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onDisconnected:746)
2025-08-21 15:45:12.908  3806-3982  Provisioning            com.dspread.mdm.service              W  ⚠️ 本地配置格式不正确，期望完整的API响应格式
2025-08-21 15:45:12.913  3806-3982  WebSocket               com.dspread.mdm.service              I  🔧 使用Provisioning配置的重连策略: delayPolicy=1, delaySwitch=1, delayTime=60
2025-08-21 15:45:12.916  3806-3982  WebSocket               com.dspread.mdm.service              I  🔧 当前断开记录数: 6, 延迟开关: 1
2025-08-21 15:45:12.920  3806-3982  WebSocket               com.dspread.mdm.service              I  🔧 重连策略计算完成: 第6次重连，间隔12000ms (12秒)
2025-08-21 15:45:12.923  3806-3982  WebSocket               com.dspread.mdm.service              I  🔧 开始第6次重连，间隔12000ms (12秒) (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onDisconnected:746)
2025-08-21 15:45:12.952  3806-3824  System                  com.dspread.mdm.service              W  A resource failed to call close. 
2025-08-21 15:45:12.956  3806-3824  System                  com.dspread.mdm.service              W  A resource failed to call close. 
2025-08-21 15:45:12.960  3806-3824  System                  com.dspread.mdm.service              W  A resource failed to call close. 
2025-08-21 15:45:12.963  3806-3824  System                  com.dspread.mdm.service              W  A resource failed to call close. 
2025-08-21 15:45:24.941  3806-3806  WebSocket               com.dspread.mdm.service              I  🔧 重连 WebSocket: wss://api.dspreadserv.net/status/websocket/register?serialNo=MDAwMDE1MDRQMzAwMDExNTYwNw==&publicKey=TUlHZk1BMEdDU3FHU0liM0RRRUJBUVVBQTRHTkFEQ0JpUUtCZ1FDd3ExK1UxdWhLbnZNY2ZPN3JCZDE3UUUvOWtiNkhzb2hwOU1HYVdGb0RNUlMwRmIzZ1pHeDFHSk9aemd4MzNqZjlVblgrV3FmZndIazBMVVozTzJZVjdpY3pjRkw1bGxoajZ1VFJNS3lZek1Uc1dKR1lIUjZrRVkraCtmd3Z5WUZrVlRVZnRvcWNwVHZOKzlYSDFmM3pMQms0UkNSSEx4SWxOUkd1eHZYYTF3SURBUUFC&query=0&msgVer=3&timestamp=1755762324930&signature=HtGI1A+E9hmf7nsGMRihGF80bh9kfl8xS4Dgk5h1XsrdkGRmecAw4yznYc3+QezMkRGjtmWhDodVG73clsekpnNf1u7qUHtyX40go5t2VLr4sU10azlLgPa34zjl0fPZ+OmxSsA4bhXepFuyaV26B14fEiU8P+k9Zks6xsG6ZXU=
2025-08-21 15:45:24.945  3806-3806  WebSocket               com.dspread.mdm.service              I  🔧 未检测到系统代理设置
2025-08-21 15:45:24.956  3806-3806  Provisioning            com.dspread.mdm.service              W  ⚠️ 本地配置格式不正确，期望完整的API响应格式
2025-08-21 15:45:24.970  3806-3987  System.out              com.dspread.mdm.service              I  [com.mediatek.cta.CtaAdapter]:check permission begin!
2025-08-21 15:45:24.971  3806-3987  TrafficStats            com.dspread.mdm.service              D  tagSocket(107) with statsTag=0xffffffff, statsUid=-1
2025-08-21 15:45:25.220  3806-3988  System.out              com.dspread.mdm.service              I  [com.mediatek.cta.CtaAdapter]:check permission begin!
2025-08-21 15:45:25.221  3806-3988  TrafficStats            com.dspread.mdm.service              D  tagSocket(114) with statsTag=0xffffffff, statsUid=-1
2025-08-21 15:45:25.224  3806-3988  ead.mdm.service         com.dspread.mdm.service              E  netdebug -00000001.-560070587 counter:9 ret:-1 connectErrno:115 latencyMs:0 addr:::ffff:52.67.235.191 port:443 processname:ead.mdm.service
                                                                                                    Umask:	00
2025-08-21 15:45:26.730  3806-3991  Common                  com.dspread.mdm.service              I  ✅ WebSocket 物理连接成功，等待服务器 hellMsg
2025-08-21 15:45:26.734  3806-3991  WebSocket               com.dspread.mdm.service              I  🔧 ===== 心跳发送器实际配置 =====
2025-08-21 15:45:26.738  3806-3991  WebSocket               com.dspread.mdm.service              I  🔧 实际 PING 发送间隔: 30000ms (30秒)
2025-08-21 15:45:26.741  3806-3991  WebSocket               com.dspread.mdm.service              I  🔧 PING 定时器名称: PingSender
2025-08-21 15:45:26.744  3806-3991  WebSocket               com.dspread.mdm.service              I  🔧 实际 PONG 发送间隔: 0ms (0秒)
2025-08-21 15:45:26.747  3806-3991  WebSocket               com.dspread.mdm.service              I  🔧 PONG 定时器名称: PongSender
2025-08-21 15:45:26.750  3806-3991  WebSocket               com.dspread.mdm.service              I  🔧 ===============================
2025-08-21 15:45:26.753  3806-3991  WebSocket               com.dspread.mdm.service              I  🔧 启动PING/PONG检测，阈值: 连续3个PING无PONG响应
2025-08-21 15:45:26.761  3806-3992  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接断开 - 服务器主动断开
2025-08-21 15:45:26.765  3806-3992  WebSocket               com.dspread.mdm.service              I  🔧    服务器关闭帧: code=1000, reason=无原因
2025-08-21 15:45:26.768  3806-3992  WebSocket               com.dspread.mdm.service              I  🔧    客户端关闭帧: code=1000, reason=无原因
2025-08-21 15:45:26.772  3806-3992  WebSocket               com.dspread.mdm.service              I  🔧    断开发起方: 服务器
2025-08-21 15:45:26.775  3806-3992  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 业务连接断开
2025-08-21 15:45:26.779  3806-3992  WebSocket               com.dspread.mdm.service              I  🔧 停止PING/PONG检测
2025-08-21 15:45:26.783  3806-3992  WebSocket               com.dspread.mdm.service              I  🔧 reconnect() 被调用，调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onDisconnected:746
2025-08-21 15:45:26.786  3806-3992  WebSocket               com.dspread.mdm.service              I  🔧 开始重连 WebSocket... (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onDisconnected:746)
2025-08-21 15:45:26.792  3806-3992  Provisioning            com.dspread.mdm.service              W  ⚠️ 本地配置格式不正确，期望完整的API响应格式
2025-08-21 15:45:26.796  3806-3992  WebSocket               com.dspread.mdm.service              I  🔧 使用Provisioning配置的重连策略: delayPolicy=1, delaySwitch=1, delayTime=60
2025-08-21 15:45:26.799  3806-3992  WebSocket               com.dspread.mdm.service              I  🔧 当前断开记录数: 7, 延迟开关: 1
2025-08-21 15:45:26.801  3806-3992  WebSocket               com.dspread.mdm.service              I  🔧 重连策略计算完成: 第7次重连，间隔15000ms (15秒)
2025-08-21 15:45:26.804  3806-3992  WebSocket               com.dspread.mdm.service              I  🔧 开始第7次重连，间隔15000ms (15秒) (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onDisconnected:746)
2025-08-21 15:45:41.832  3806-3806  WebSocket               com.dspread.mdm.service              I  🔧 重连 WebSocket: wss://api.dspreadserv.net/status/websocket/register?serialNo=MDAwMDE1MDRQMzAwMDExNTYwNw==&publicKey=TUlHZk1BMEdDU3FHU0liM0RRRUJBUVVBQTRHTkFEQ0JpUUtCZ1FDd3ExK1UxdWhLbnZNY2ZPN3JCZDE3UUUvOWtiNkhzb2hwOU1HYVdGb0RNUlMwRmIzZ1pHeDFHSk9aemd4MzNqZjlVblgrV3FmZndIazBMVVozTzJZVjdpY3pjRkw1bGxoajZ1VFJNS3lZek1Uc1dKR1lIUjZrRVkraCtmd3Z5WUZrVlRVZnRvcWNwVHZOKzlYSDFmM3pMQms0UkNSSEx4SWxOUkd1eHZYYTF3SURBUUFC&query=0&msgVer=3&timestamp=1755762341821&signature=kddwYF96gqeL2sr7xe+E0MrViq/2SIAfT46gseDxajGX0x9FIBeEY1mBSrHuJjLgmXIXhgKi+O7kAyMQehdJC0zC4FhctZ4sIFR2FFevOpqKupqzXpbzriT4SxiVqqkbLJ7QaBxeKanqZIrvmaGljmEfgI2/BQWP71nOpuowkqQ=
2025-08-21 15:45:41.836  3806-3806  WebSocket               com.dspread.mdm.service              I  🔧 未检测到系统代理设置
2025-08-21 15:45:41.847  3806-3806  Provisioning            com.dspread.mdm.service              W  ⚠️ 本地配置格式不正确，期望完整的API响应格式
2025-08-21 15:45:42.037  3806-3997  System.out              com.dspread.mdm.service              I  [com.mediatek.cta.CtaAdapter]:check permission begin!
2025-08-21 15:45:42.038  3806-3997  TrafficStats            com.dspread.mdm.service              D  tagSocket(107) with statsTag=0xffffffff, statsUid=-1
2025-08-21 15:45:42.288  3806-3998  System.out              com.dspread.mdm.service              I  [com.mediatek.cta.CtaAdapter]:check permission begin!
2025-08-21 15:45:42.289  3806-3998  TrafficStats            com.dspread.mdm.service              D  tagSocket(118) with statsTag=0xffffffff, statsUid=-1
2025-08-21 15:45:43.881  3806-3999  Common                  com.dspread.mdm.service              I  ✅ WebSocket 物理连接成功，等待服务器 hellMsg
2025-08-21 15:45:43.884  3806-3999  WebSocket               com.dspread.mdm.service              I  🔧 ===== 心跳发送器实际配置 =====
2025-08-21 15:45:43.887  3806-3999  WebSocket               com.dspread.mdm.service              I  🔧 实际 PING 发送间隔: 30000ms (30秒)
2025-08-21 15:45:43.890  3806-3999  WebSocket               com.dspread.mdm.service              I  🔧 PING 定时器名称: PingSender
2025-08-21 15:45:43.892  3806-3999  WebSocket               com.dspread.mdm.service              I  🔧 实际 PONG 发送间隔: 0ms (0秒)
2025-08-21 15:45:43.895  3806-3999  WebSocket               com.dspread.mdm.service              I  🔧 PONG 定时器名称: PongSender
2025-08-21 15:45:43.898  3806-3999  WebSocket               com.dspread.mdm.service              I  🔧 ===============================
2025-08-21 15:45:43.901  3806-3999  WebSocket               com.dspread.mdm.service              I  🔧 启动PING/PONG检测，阈值: 连续3个PING无PONG响应
2025-08-21 15:45:43.908  3806-4000  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接断开 - 服务器主动断开
2025-08-21 15:45:43.912  3806-4000  WebSocket               com.dspread.mdm.service              I  🔧    服务器关闭帧: code=1000, reason=无原因
2025-08-21 15:45:43.915  3806-4000  WebSocket               com.dspread.mdm.service              I  🔧    客户端关闭帧: code=1000, reason=无原因
2025-08-21 15:45:43.919  3806-4000  WebSocket               com.dspread.mdm.service              I  🔧    断开发起方: 服务器
2025-08-21 15:45:43.922  3806-4000  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 业务连接断开
2025-08-21 15:45:43.925  3806-4000  WebSocket               com.dspread.mdm.service              I  🔧 停止PING/PONG检测
2025-08-21 15:45:43.929  3806-4000  WebSocket               com.dspread.mdm.service              I  🔧 reconnect() 被调用，调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onDisconnected:746
2025-08-21 15:45:43.933  3806-4000  WebSocket               com.dspread.mdm.service              I  🔧 开始重连 WebSocket... (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onDisconnected:746)
2025-08-21 15:45:43.939  3806-4000  Provisioning            com.dspread.mdm.service              W  ⚠️ 本地配置格式不正确，期望完整的API响应格式
2025-08-21 15:45:43.943  3806-4000  WebSocket               com.dspread.mdm.service              I  🔧 使用Provisioning配置的重连策略: delayPolicy=1, delaySwitch=1, delayTime=60
2025-08-21 15:45:43.946  3806-4000  WebSocket               com.dspread.mdm.service              I  🔧 当前断开记录数: 8, 延迟开关: 1
2025-08-21 15:45:43.949  3806-4000  WebSocket               com.dspread.mdm.service              I  🔧 重连策略计算完成: 第8次重连，间隔15000ms (15秒)
2025-08-21 15:45:43.953  3806-4000  WebSocket               com.dspread.mdm.service              I  🔧 开始第8次重连，间隔15000ms (15秒) (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onDisconnected:746)
2025-08-21 15:45:58.984  3806-3806  WebSocket               com.dspread.mdm.service              I  🔧 重连 WebSocket: wss://api.dspreadserv.net/status/websocket/register?serialNo=MDAwMDE1MDRQMzAwMDExNTYwNw==&publicKey=TUlHZk1BMEdDU3FHU0liM0RRRUJBUVVBQTRHTkFEQ0JpUUtCZ1FDd3ExK1UxdWhLbnZNY2ZPN3JCZDE3UUUvOWtiNkhzb2hwOU1HYVdGb0RNUlMwRmIzZ1pHeDFHSk9aemd4MzNqZjlVblgrV3FmZndIazBMVVozTzJZVjdpY3pjRkw1bGxoajZ1VFJNS3lZek1Uc1dKR1lIUjZrRVkraCtmd3Z5WUZrVlRVZnRvcWNwVHZOKzlYSDFmM3pMQms0UkNSSEx4SWxOUkd1eHZYYTF3SURBUUFC&query=0&msgVer=3&timestamp=1755762358974&signature=GFNG1mFB2/Hx59VHYtXgxNImOaXVm+tWu43r3IAjdKXuCiYojDcp+q2z8P/8ZHediRvUFu5WePn2k9+CmlFx6NJZJSoxA9HPHjfu83XQhSIvTNthH42tSesq2Hml/6TCXun2QHDIpVNrP5A/FfvrNz+w7U7tzuPv0Ox5JNOWBPI=
2025-08-21 15:45:58.989  3806-3806  WebSocket               com.dspread.mdm.service              I  🔧 未检测到系统代理设置
2025-08-21 15:45:59.000  3806-3806  Provisioning            com.dspread.mdm.service              W  ⚠️ 本地配置格式不正确，期望完整的API响应格式
2025-08-21 15:45:59.015  3806-4012  System.out              com.dspread.mdm.service              I  [com.mediatek.cta.CtaAdapter]:check permission begin!
2025-08-21 15:45:59.016  3806-4012  TrafficStats            com.dspread.mdm.service              D  tagSocket(118) with statsTag=0xffffffff, statsUid=-1
2025-08-21 15:45:59.266  3806-4013  System.out              com.dspread.mdm.service              I  [com.mediatek.cta.CtaAdapter]:check permission begin!
2025-08-21 15:45:59.266  3806-4013  TrafficStats            com.dspread.mdm.service              D  tagSocket(123) with statsTag=0xffffffff, statsUid=-1
2025-08-21 15:46:00.878  3806-4014  Common                  com.dspread.mdm.service              I  ✅ WebSocket 物理连接成功，等待服务器 hellMsg
2025-08-21 15:46:00.881  3806-4014  WebSocket               com.dspread.mdm.service              I  🔧 ===== 心跳发送器实际配置 =====
2025-08-21 15:46:00.884  3806-4014  WebSocket               com.dspread.mdm.service              I  🔧 实际 PING 发送间隔: 30000ms (30秒)
2025-08-21 15:46:00.887  3806-4014  WebSocket               com.dspread.mdm.service              I  🔧 PING 定时器名称: PingSender
2025-08-21 15:46:00.890  3806-4014  WebSocket               com.dspread.mdm.service              I  🔧 实际 PONG 发送间隔: 0ms (0秒)
2025-08-21 15:46:00.893  3806-4014  WebSocket               com.dspread.mdm.service              I  🔧 PONG 定时器名称: PongSender
2025-08-21 15:46:00.896  3806-4014  WebSocket               com.dspread.mdm.service              I  🔧 ===============================
2025-08-21 15:46:00.899  3806-4014  WebSocket               com.dspread.mdm.service              I  🔧 启动PING/PONG检测，阈值: 连续3个PING无PONG响应
2025-08-21 15:46:00.907  3806-4015  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接断开 - 服务器主动断开
2025-08-21 15:46:00.911  3806-4015  WebSocket               com.dspread.mdm.service              I  🔧    服务器关闭帧: code=1000, reason=无原因
2025-08-21 15:46:00.915  3806-4015  WebSocket               com.dspread.mdm.service              I  🔧    客户端关闭帧: code=1000, reason=无原因
2025-08-21 15:46:00.918  3806-4015  WebSocket               com.dspread.mdm.service              I  🔧    断开发起方: 服务器
2025-08-21 15:46:00.922  3806-4015  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 业务连接断开
2025-08-21 15:46:00.925  3806-4015  WebSocket               com.dspread.mdm.service              I  🔧 停止PING/PONG检测
2025-08-21 15:46:00.929  3806-4015  WebSocket               com.dspread.mdm.service              I  🔧 reconnect() 被调用，调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onDisconnected:746
2025-08-21 15:46:00.933  3806-4015  WebSocket               com.dspread.mdm.service              I  🔧 开始重连 WebSocket... (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onDisconnected:746)
2025-08-21 15:46:00.940  3806-4015  Provisioning            com.dspread.mdm.service              W  ⚠️ 本地配置格式不正确，期望完整的API响应格式
2025-08-21 15:46:00.943  3806-4015  WebSocket               com.dspread.mdm.service              I  🔧 使用Provisioning配置的重连策略: delayPolicy=1, delaySwitch=1, delayTime=60
2025-08-21 15:46:00.947  3806-4015  WebSocket               com.dspread.mdm.service              I  🔧 当前断开记录数: 9, 延迟开关: 1
2025-08-21 15:46:00.950  3806-4015  WebSocket               com.dspread.mdm.service              I  🔧 重连策略计算完成: 第9次重连，间隔15000ms (15秒)
2025-08-21 15:46:00.954  3806-4015  WebSocket               com.dspread.mdm.service              I  🔧 开始第9次重连，间隔15000ms (15秒) (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onDisconnected:746)
2025-08-21 15:46:15.973  3806-3806  WebSocket               com.dspread.mdm.service              I  🔧 重连 WebSocket: wss://api.dspreadserv.net/status/websocket/register?serialNo=MDAwMDE1MDRQMzAwMDExNTYwNw==&publicKey=TUlHZk1BMEdDU3FHU0liM0RRRUJBUVVBQTRHTkFEQ0JpUUtCZ1FDd3ExK1UxdWhLbnZNY2ZPN3JCZDE3UUUvOWtiNkhzb2hwOU1HYVdGb0RNUlMwRmIzZ1pHeDFHSk9aemd4MzNqZjlVblgrV3FmZndIazBMVVozTzJZVjdpY3pjRkw1bGxoajZ1VFJNS3lZek1Uc1dKR1lIUjZrRVkraCtmd3Z5WUZrVlRVZnRvcWNwVHZOKzlYSDFmM3pMQms0UkNSSEx4SWxOUkd1eHZYYTF3SURBUUFC&query=0&msgVer=3&timestamp=1755762375962&signature=W4dD03kiH2nxdJEdgpSxtgQs9d+MRMXBVrXWTemcVsh80ZwYk7kY8Gkz07FdAKAtwmy7bd4z3pu8PI1V1Nfxoa+bzrq4xTFHX/wJAqJi7IAkHKu1CIJKYgPDoRNbvgohxLwi37T840zKx7PPzyyzjX5/sCFkH0uKWSM+fuEpwKA=
2025-08-21 15:46:15.977  3806-3806  WebSocket               com.dspread.mdm.service              I  🔧 未检测到系统代理设置
2025-08-21 15:46:15.989  3806-3806  Provisioning            com.dspread.mdm.service              W  ⚠️ 本地配置格式不正确，期望完整的API响应格式
2025-08-21 15:46:16.003  3806-4020  System.out              com.dspread.mdm.service              I  [com.mediatek.cta.CtaAdapter]:check permission begin!
2025-08-21 15:46:16.005  3806-4020  TrafficStats            com.dspread.mdm.service              D  tagSocket(123) with statsTag=0xffffffff, statsUid=-1
2025-08-21 15:46:16.254  3806-4021  System.out              com.dspread.mdm.service              I  [com.mediatek.cta.CtaAdapter]:check permission begin!
2025-08-21 15:46:16.255  3806-4021  TrafficStats            com.dspread.mdm.service              D  tagSocket(128) with statsTag=0xffffffff, statsUid=-1
2025-08-21 15:46:17.876  3806-4023  Common                  com.dspread.mdm.service              I  ✅ WebSocket 物理连接成功，等待服务器 hellMsg
2025-08-21 15:46:17.880  3806-4023  WebSocket               com.dspread.mdm.service              I  🔧 ===== 心跳发送器实际配置 =====
2025-08-21 15:46:17.884  3806-4023  WebSocket               com.dspread.mdm.service              I  🔧 实际 PING 发送间隔: 30000ms (30秒)
2025-08-21 15:46:17.888  3806-4023  WebSocket               com.dspread.mdm.service              I  🔧 PING 定时器名称: PingSender
2025-08-21 15:46:17.892  3806-4023  WebSocket               com.dspread.mdm.service              I  🔧 实际 PONG 发送间隔: 0ms (0秒)
2025-08-21 15:46:17.895  3806-4023  WebSocket               com.dspread.mdm.service              I  🔧 PONG 定时器名称: PongSender
2025-08-21 15:46:17.899  3806-4023  WebSocket               com.dspread.mdm.service              I  🔧 ===============================
2025-08-21 15:46:17.902  3806-4023  WebSocket               com.dspread.mdm.service              I  🔧 启动PING/PONG检测，阈值: 连续3个PING无PONG响应
2025-08-21 15:46:17.909  3806-4023  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接断开 - 服务器主动断开
2025-08-21 15:46:17.913  3806-4023  WebSocket               com.dspread.mdm.service              I  🔧    服务器关闭帧: code=1000, reason=无原因
2025-08-21 15:46:17.917  3806-4023  WebSocket               com.dspread.mdm.service              I  🔧    客户端关闭帧: code=1000, reason=无原因
2025-08-21 15:46:17.920  3806-4023  WebSocket               com.dspread.mdm.service              I  🔧    断开发起方: 服务器
2025-08-21 15:46:17.923  3806-4023  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 业务连接断开
2025-08-21 15:46:17.926  3806-4023  WebSocket               com.dspread.mdm.service              I  🔧 停止PING/PONG检测
2025-08-21 15:46:17.930  3806-4023  WebSocket               com.dspread.mdm.service              I  🔧 reconnect() 被调用，调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onDisconnected:746
2025-08-21 15:46:17.933  3806-4023  WebSocket               com.dspread.mdm.service              I  🔧 开始重连 WebSocket... (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onDisconnected:746)
2025-08-21 15:46:17.938  3806-4023  Provisioning            com.dspread.mdm.service              W  ⚠️ 本地配置格式不正确，期望完整的API响应格式
2025-08-21 15:46:17.942  3806-4023  WebSocket               com.dspread.mdm.service              I  🔧 使用Provisioning配置的重连策略: delayPolicy=1, delaySwitch=1, delayTime=60
2025-08-21 15:46:17.945  3806-4023  WebSocket               com.dspread.mdm.service              I  🔧 当前断开记录数: 10, 延迟开关: 1
2025-08-21 15:46:17.958  3806-4023  WebSocket               com.dspread.mdm.service              I  🔧 断开记录 最早一次断开: 2025-08-21 15:44:37
2025-08-21 15:46:17.965  3806-4023  WebSocket               com.dspread.mdm.service              I  🔧 断开记录 最后一次断开: 2025-08-21 15:46:17
2025-08-21 15:46:17.968  3806-4023  WebSocket               com.dspread.mdm.service              I  🔧 时间差: 1分钟 (配置阈值: 1分钟)
2025-08-21 15:46:17.972  3806-4023  WebSocket               com.dspread.mdm.service              I  🔧 断开间隔正常，使用标准重连策略
2025-08-21 15:46:17.976  3806-4023  WebSocket               com.dspread.mdm.service              I  🔧 重连策略计算完成: 第10次重连，间隔15000ms (15秒)
2025-08-21 15:46:17.979  3806-4023  WebSocket               com.dspread.mdm.service              I  🔧 开始第10次重连，间隔15000ms (15秒) (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onDisconnected:746)
2025-08-21 15:46:18.018  3806-3824  System                  com.dspread.mdm.service              W  A resource failed to call close. 
2025-08-21 15:46:18.021  3806-3824  System                  com.dspread.mdm.service              W  A resource failed to call close. 
2025-08-21 15:46:18.024  3806-3824  System                  com.dspread.mdm.service              W  A resource failed to call close. 
2025-08-21 15:46:18.026  3806-3824  System                  com.dspread.mdm.service              W  A resource failed to call close. 
2025-08-21 15:46:33.005  3806-3806  WebSocket               com.dspread.mdm.service              I  🔧 重连 WebSocket: wss://api.dspreadserv.net/status/websocket/register?serialNo=MDAwMDE1MDRQMzAwMDExNTYwNw==&publicKey=TUlHZk1BMEdDU3FHU0liM0RRRUJBUVVBQTRHTkFEQ0JpUUtCZ1FDd3ExK1UxdWhLbnZNY2ZPN3JCZDE3UUUvOWtiNkhzb2hwOU1HYVdGb0RNUlMwRmIzZ1pHeDFHSk9aemd4MzNqZjlVblgrV3FmZndIazBMVVozTzJZVjdpY3pjRkw1bGxoajZ1VFJNS3lZek1Uc1dKR1lIUjZrRVkraCtmd3Z5WUZrVlRVZnRvcWNwVHZOKzlYSDFmM3pMQms0UkNSSEx4SWxOUkd1eHZYYTF3SURBUUFC&query=0&msgVer=3&timestamp=1755762392993&signature=lB2z+DYHhJPyRDtnYKXVoHc4UQ9rtl66pYmPxD8I38oEtkcnyHPlxq+dK/JWY3MV1+8PDmDZ3SSBtZZWp1SEbk218KHOirz+kZP6wLpMisQDAgxoTII/SIl3PC0U1Nt2+rih4FqTFy2dz5IQ2rSLcFu/dtvX9MpOfKxPLOCoQpY=
2025-08-21 15:46:33.010  3806-3806  WebSocket               com.dspread.mdm.service              I  🔧 未检测到系统代理设置
2025-08-21 15:46:33.021  3806-3806  Provisioning            com.dspread.mdm.service              W  ⚠️ 本地配置格式不正确，期望完整的API响应格式
2025-08-21 15:46:33.037  3806-4030  System.out              com.dspread.mdm.service              I  [com.mediatek.cta.CtaAdapter]:check permission begin!
2025-08-21 15:46:33.039  3806-4030  TrafficStats            com.dspread.mdm.service              D  tagSocket(107) with statsTag=0xffffffff, statsUid=-1
2025-08-21 15:46:33.288  3806-4031  System.out              com.dspread.mdm.service              I  [com.mediatek.cta.CtaAdapter]:check permission begin!
2025-08-21 15:46:33.289  3806-4031  TrafficStats            com.dspread.mdm.service              D  tagSocket(114) with statsTag=0xffffffff, statsUid=-1
2025-08-21 15:46:35.388  3806-4032  Common                  com.dspread.mdm.service              I  ✅ WebSocket 物理连接成功，等待服务器 hellMsg
2025-08-21 15:46:35.392  3806-4032  WebSocket               com.dspread.mdm.service              I  🔧 ===== 心跳发送器实际配置 =====
2025-08-21 15:46:35.395  3806-4032  WebSocket               com.dspread.mdm.service              I  🔧 实际 PING 发送间隔: 30000ms (30秒)
2025-08-21 15:46:35.398  3806-4032  WebSocket               com.dspread.mdm.service              I  🔧 PING 定时器名称: PingSender
2025-08-21 15:46:35.401  3806-4032  WebSocket               com.dspread.mdm.service              I  🔧 实际 PONG 发送间隔: 0ms (0秒)
2025-08-21 15:46:35.404  3806-4032  WebSocket               com.dspread.mdm.service              I  🔧 PONG 定时器名称: PongSender
2025-08-21 15:46:35.407  3806-4032  WebSocket               com.dspread.mdm.service              I  🔧 ===============================
2025-08-21 15:46:35.410  3806-4032  WebSocket               com.dspread.mdm.service              I  🔧 启动PING/PONG检测，阈值: 连续3个PING无PONG响应
2025-08-21 15:46:35.417  3806-4033  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接断开 - 服务器主动断开
2025-08-21 15:46:35.421  3806-4033  WebSocket               com.dspread.mdm.service              I  🔧    服务器关闭帧: code=1000, reason=无原因
2025-08-21 15:46:35.424  3806-4033  WebSocket               com.dspread.mdm.service              I  🔧    客户端关闭帧: code=1000, reason=无原因
2025-08-21 15:46:35.427  3806-4033  WebSocket               com.dspread.mdm.service              I  🔧    断开发起方: 服务器
2025-08-21 15:46:35.430  3806-4033  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 业务连接断开
2025-08-21 15:46:35.433  3806-4033  WebSocket               com.dspread.mdm.service              I  🔧 停止PING/PONG检测
2025-08-21 15:46:35.436  3806-4033  WebSocket               com.dspread.mdm.service              I  🔧 reconnect() 被调用，调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onDisconnected:746
2025-08-21 15:46:35.439  3806-4033  WebSocket               com.dspread.mdm.service              I  🔧 开始重连 WebSocket... (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onDisconnected:746)
2025-08-21 15:46:35.444  3806-4033  Provisioning            com.dspread.mdm.service              W  ⚠️ 本地配置格式不正确，期望完整的API响应格式
2025-08-21 15:46:35.447  3806-4033  WebSocket               com.dspread.mdm.service              I  🔧 使用Provisioning配置的重连策略: delayPolicy=1, delaySwitch=1, delayTime=60
2025-08-21 15:46:35.450  3806-4033  WebSocket               com.dspread.mdm.service              I  🔧 当前断开记录数: 10, 延迟开关: 1
2025-08-21 15:46:35.455  3806-4033  WebSocket               com.dspread.mdm.service              I  🔧 断开记录 最早一次断开: 2025-08-21 15:44:42
2025-08-21 15:46:35.460  3806-4033  WebSocket               com.dspread.mdm.service              I  🔧 断开记录 最后一次断开: 2025-08-21 15:46:35
2025-08-21 15:46:35.462  3806-4033  WebSocket               com.dspread.mdm.service              I  🔧 时间差: 1分钟 (配置阈值: 1分钟)
2025-08-21 15:46:35.465  3806-4033  WebSocket               com.dspread.mdm.service              I  🔧 断开间隔正常，使用标准重连策略
2025-08-21 15:46:35.468  3806-4033  WebSocket               com.dspread.mdm.service              I  🔧 重连策略计算完成: 第11次重连，间隔15000ms (15秒)
2025-08-21 15:46:35.470  3806-4033  WebSocket               com.dspread.mdm.service              I  🔧 开始第11次重连，间隔15000ms (15秒) (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onDisconnected:746)
2025-08-21 15:46:43.281  3806-3806  Receiver                com.dspread.mdm.service              D  🔧 电池状态变化: 电量=17%, 温度=29°C, 充电=true
2025-08-21 15:46:43.289  3806-3806  Provisioning            com.dspread.mdm.service              W  ⚠️ 本地配置格式不正确，期望完整的API响应格式
2025-08-21 15:46:43.293  3806-3806  WebSocket               com.dspread.mdm.service              I  🔧 C0902 被动式上送: battery_level_change_5 (被动: 2)
2025-08-21 15:46:43.315  3806-3806  WebSocket               com.dspread.mdm.service              I  🔧 消息已缓存: C0902 (缓存数量: 3)
2025-08-21 15:46:43.318  3806-3806  WebSocket               com.dspread.mdm.service              I  🔧 C0902 电池状态上送成功 (1, trigger=battery_level_change_5)
2025-08-21 15:46:50.493  3806-3806  WebSocket               com.dspread.mdm.service              I  🔧 重连 WebSocket: wss://api.dspreadserv.net/status/websocket/register?serialNo=MDAwMDE1MDRQMzAwMDExNTYwNw==&publicKey=TUlHZk1BMEdDU3FHU0liM0RRRUJBUVVBQTRHTkFEQ0JpUUtCZ1FDd3ExK1UxdWhLbnZNY2ZPN3JCZDE3UUUvOWtiNkhzb2hwOU1HYVdGb0RNUlMwRmIzZ1pHeDFHSk9aemd4MzNqZjlVblgrV3FmZndIazBMVVozTzJZVjdpY3pjRkw1bGxoajZ1VFJNS3lZek1Uc1dKR1lIUjZrRVkraCtmd3Z5WUZrVlRVZnRvcWNwVHZOKzlYSDFmM3pMQms0UkNSSEx4SWxOUkd1eHZYYTF3SURBUUFC&query=0&msgVer=3&timestamp=1755762410482&signature=czuRVmV6rejmnFlkp7yD1XgacDLEsb2+Wbdrr/I0DW14SPEnGIlJYI44aOvNAkUxTxQqkZYODJbUnj1JZmiP7Fgo+w5o5u4SJW4foT1xcfuyTeDHpdzYAdjC6GpjbclOpkv4DjDuWbwPfzL2x6t0ynJuhLlOoFmvJeYujrPSVj8=
2025-08-21 15:46:50.497  3806-3806  WebSocket               com.dspread.mdm.service              I  🔧 未检测到系统代理设置
2025-08-21 15:46:50.508  3806-3806  Provisioning            com.dspread.mdm.service              W  ⚠️ 本地配置格式不正确，期望完整的API响应格式
2025-08-21 15:46:50.851  3806-4038  System.out              com.dspread.mdm.service              I  [com.mediatek.cta.CtaAdapter]:check permission begin!
2025-08-21 15:46:50.852  3806-4038  TrafficStats            com.dspread.mdm.service              D  tagSocket(107) with statsTag=0xffffffff, statsUid=-1
2025-08-21 15:46:51.101  3806-4039  System.out              com.dspread.mdm.service              I  [com.mediatek.cta.CtaAdapter]:check permission begin!
2025-08-21 15:46:51.102  3806-4039  TrafficStats            com.dspread.mdm.service              D  tagSocket(118) with statsTag=0xffffffff, statsUid=-1
2025-08-21 15:46:52.693  3806-4040  Common                  com.dspread.mdm.service              I  ✅ WebSocket 物理连接成功，等待服务器 hellMsg
2025-08-21 15:46:52.696  3806-4040  WebSocket               com.dspread.mdm.service              I  🔧 ===== 心跳发送器实际配置 =====
2025-08-21 15:46:52.699  3806-4040  WebSocket               com.dspread.mdm.service              I  🔧 实际 PING 发送间隔: 30000ms (30秒)
2025-08-21 15:46:52.702  3806-4040  WebSocket               com.dspread.mdm.service              I  🔧 PING 定时器名称: PingSender
2025-08-21 15:46:52.705  3806-4040  WebSocket               com.dspread.mdm.service              I  🔧 实际 PONG 发送间隔: 0ms (0秒)
2025-08-21 15:46:52.708  3806-4040  WebSocket               com.dspread.mdm.service              I  🔧 PONG 定时器名称: PongSender
2025-08-21 15:46:52.710  3806-4040  WebSocket               com.dspread.mdm.service              I  🔧 ===============================
2025-08-21 15:46:52.713  3806-4040  WebSocket               com.dspread.mdm.service              I  🔧 启动PING/PONG检测，阈值: 连续3个PING无PONG响应
2025-08-21 15:46:52.720  3806-4041  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接断开 - 服务器主动断开
2025-08-21 15:46:52.723  3806-4041  WebSocket               com.dspread.mdm.service              I  🔧    服务器关闭帧: code=1000, reason=无原因
2025-08-21 15:46:52.726  3806-4041  WebSocket               com.dspread.mdm.service              I  🔧    客户端关闭帧: code=1000, reason=无原因
2025-08-21 15:46:52.729  3806-4041  WebSocket               com.dspread.mdm.service              I  🔧    断开发起方: 服务器
2025-08-21 15:46:52.732  3806-4041  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 业务连接断开
2025-08-21 15:46:52.735  3806-4041  WebSocket               com.dspread.mdm.service              I  🔧 停止PING/PONG检测
2025-08-21 15:46:52.739  3806-4041  WebSocket               com.dspread.mdm.service              I  🔧 reconnect() 被调用，调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onDisconnected:746
2025-08-21 15:46:52.792  3806-4041  WebSocket               com.dspread.mdm.service              I  🔧 开始重连 WebSocket... (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onDisconnected:746)
2025-08-21 15:46:52.797  3806-4041  Provisioning            com.dspread.mdm.service              W  ⚠️ 本地配置格式不正确，期望完整的API响应格式
2025-08-21 15:46:52.800  3806-4041  WebSocket               com.dspread.mdm.service              I  🔧 使用Provisioning配置的重连策略: delayPolicy=1, delaySwitch=1, delayTime=60
2025-08-21 15:46:52.803  3806-4041  WebSocket               com.dspread.mdm.service              I  🔧 当前断开记录数: 10, 延迟开关: 1
2025-08-21 15:46:52.808  3806-4041  WebSocket               com.dspread.mdm.service              I  🔧 断开记录 最早一次断开: 2025-08-21 15:44:47
2025-08-21 15:46:52.812  3806-4041  WebSocket               com.dspread.mdm.service              I  🔧 断开记录 最后一次断开: 2025-08-21 15:46:52
2025-08-21 15:46:52.815  3806-4041  WebSocket               com.dspread.mdm.service              I  🔧 时间差: 2分钟 (配置阈值: 1分钟)
2025-08-21 15:46:52.817  3806-4041  WebSocket               com.dspread.mdm.service              I  🔧 断开间隔正常，使用标准重连策略
2025-08-21 15:46:52.819  3806-4041  WebSocket               com.dspread.mdm.service              I  🔧 重连策略计算完成: 第12次重连，间隔15000ms (15秒)
2025-08-21 15:46:52.822  3806-4041  WebSocket               com.dspread.mdm.service              I  🔧 开始第12次重连，间隔15000ms (15秒) (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onDisconnected:746)
2025-08-21 15:47:07.853  3806-3806  WebSocket               com.dspread.mdm.service              I  🔧 重连 WebSocket: wss://api.dspreadserv.net/status/websocket/register?serialNo=MDAwMDE1MDRQMzAwMDExNTYwNw==&publicKey=TUlHZk1BMEdDU3FHU0liM0RRRUJBUVVBQTRHTkFEQ0JpUUtCZ1FDd3ExK1UxdWhLbnZNY2ZPN3JCZDE3UUUvOWtiNkhzb2hwOU1HYVdGb0RNUlMwRmIzZ1pHeDFHSk9aemd4MzNqZjlVblgrV3FmZndIazBMVVozTzJZVjdpY3pjRkw1bGxoajZ1VFJNS3lZek1Uc1dKR1lIUjZrRVkraCtmd3Z5WUZrVlRVZnRvcWNwVHZOKzlYSDFmM3pMQms0UkNSSEx4SWxOUkd1eHZYYTF3SURBUUFC&query=0&msgVer=3&timestamp=1755762427842&signature=QlJpzH7MHOn58ZTGLuEjdJj/LQQQ+9AGSzQzkjkQ4u+0gnEhnTaqidqgj0x9er5SoofCd/2NE/gqgeqPVh0EIZiKzG5sgh9HC1kks2Jf063lHtcNkkU5nFBeK1HrFFkAiQ31jtFcWdr9eJs/SKZReqBVng0VnyyftjInMAZ432o=
2025-08-21 15:47:07.856  3806-3806  WebSocket               com.dspread.mdm.service              I  🔧 未检测到系统代理设置
2025-08-21 15:47:07.868  3806-3806  Provisioning            com.dspread.mdm.service              W  ⚠️ 本地配置格式不正确，期望完整的API响应格式
2025-08-21 15:47:07.881  3806-4046  System.out              com.dspread.mdm.service              I  [com.mediatek.cta.CtaAdapter]:check permission begin!
2025-08-21 15:47:07.882  3806-4046  TrafficStats            com.dspread.mdm.service              D  tagSocket(107) with statsTag=0xffffffff, statsUid=-1
2025-08-21 15:47:08.132  3806-4047  System.out              com.dspread.mdm.service              I  [com.mediatek.cta.CtaAdapter]:check permission begin!
2025-08-21 15:47:08.133  3806-4047  TrafficStats            com.dspread.mdm.service              D  tagSocket(123) with statsTag=0xffffffff, statsUid=-1
2025-08-21 15:47:09.692  3806-4048  Common                  com.dspread.mdm.service              I  ✅ WebSocket 物理连接成功，等待服务器 hellMsg
2025-08-21 15:47:09.695  3806-4048  WebSocket               com.dspread.mdm.service              I  🔧 ===== 心跳发送器实际配置 =====
2025-08-21 15:47:09.698  3806-4048  WebSocket               com.dspread.mdm.service              I  🔧 实际 PING 发送间隔: 30000ms (30秒)
2025-08-21 15:47:09.701  3806-4048  WebSocket               com.dspread.mdm.service              I  🔧 PING 定时器名称: PingSender
2025-08-21 15:47:09.704  3806-4048  WebSocket               com.dspread.mdm.service              I  🔧 实际 PONG 发送间隔: 0ms (0秒)
2025-08-21 15:47:09.707  3806-4048  WebSocket               com.dspread.mdm.service              I  🔧 PONG 定时器名称: PongSender
2025-08-21 15:47:09.710  3806-4048  WebSocket               com.dspread.mdm.service              I  🔧 ===============================
2025-08-21 15:47:09.712  3806-4048  WebSocket               com.dspread.mdm.service              I  🔧 启动PING/PONG检测，阈值: 连续3个PING无PONG响应
2025-08-21 15:47:09.720  3806-4049  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接断开 - 服务器主动断开
2025-08-21 15:47:09.723  3806-4049  WebSocket               com.dspread.mdm.service              I  🔧    服务器关闭帧: code=1000, reason=无原因
2025-08-21 15:47:09.726  3806-4049  WebSocket               com.dspread.mdm.service              I  🔧    客户端关闭帧: code=1000, reason=无原因
2025-08-21 15:47:09.730  3806-4049  WebSocket               com.dspread.mdm.service              I  🔧    断开发起方: 服务器
2025-08-21 15:47:09.733  3806-4049  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 业务连接断开
2025-08-21 15:47:09.736  3806-4049  WebSocket               com.dspread.mdm.service              I  🔧 停止PING/PONG检测
2025-08-21 15:47:09.740  3806-4049  WebSocket               com.dspread.mdm.service              I  🔧 reconnect() 被调用，调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onDisconnected:746
2025-08-21 15:47:09.743  3806-4049  WebSocket               com.dspread.mdm.service              I  🔧 开始重连 WebSocket... (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onDisconnected:746)
2025-08-21 15:47:09.750  3806-4049  Provisioning            com.dspread.mdm.service              W  ⚠️ 本地配置格式不正确，期望完整的API响应格式
2025-08-21 15:47:09.753  3806-4049  WebSocket               com.dspread.mdm.service              I  🔧 使用Provisioning配置的重连策略: delayPolicy=1, delaySwitch=1, delayTime=60
2025-08-21 15:47:09.756  3806-4049  WebSocket               com.dspread.mdm.service              I  🔧 当前断开记录数: 10, 延迟开关: 1
2025-08-21 15:47:09.763  3806-4049  WebSocket               com.dspread.mdm.service              I  🔧 断开记录 最早一次断开: 2025-08-21 15:44:52
2025-08-21 15:47:09.768  3806-4049  WebSocket               com.dspread.mdm.service              I  🔧 断开记录 最后一次断开: 2025-08-21 15:47:09
2025-08-21 15:47:09.771  3806-4049  WebSocket               com.dspread.mdm.service              I  🔧 时间差: 2分钟 (配置阈值: 1分钟)
2025-08-21 15:47:09.774  3806-4049  WebSocket               com.dspread.mdm.service              I  🔧 断开间隔正常，使用标准重连策略
2025-08-21 15:47:09.776  3806-4049  WebSocket               com.dspread.mdm.service              I  🔧 重连策略计算完成: 第13次重连，间隔15000ms (15秒)
2025-08-21 15:47:09.779  3806-4049  WebSocket               com.dspread.mdm.service              I  🔧 开始第13次重连，间隔15000ms (15秒) (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onDisconnected:746)
2025-08-21 15:47:24.803  3806-3806  WebSocket               com.dspread.mdm.service              I  🔧 重连 WebSocket: wss://api.dspreadserv.net/status/websocket/register?serialNo=MDAwMDE1MDRQMzAwMDExNTYwNw==&publicKey=TUlHZk1BMEdDU3FHU0liM0RRRUJBUVVBQTRHTkFEQ0JpUUtCZ1FDd3ExK1UxdWhLbnZNY2ZPN3JCZDE3UUUvOWtiNkhzb2hwOU1HYVdGb0RNUlMwRmIzZ1pHeDFHSk9aemd4MzNqZjlVblgrV3FmZndIazBMVVozTzJZVjdpY3pjRkw1bGxoajZ1VFJNS3lZek1Uc1dKR1lIUjZrRVkraCtmd3Z5WUZrVlRVZnRvcWNwVHZOKzlYSDFmM3pMQms0UkNSSEx4SWxOUkd1eHZYYTF3SURBUUFC&query=0&msgVer=3&timestamp=1755762444792&signature=S5pDADpWQeygaPCWrZOJ/xjBpN4tNLlg2nM5NOvXb7nlqEVQXmpm5YxlxJ495EH6T/8iqUbiVaPVuadNc7VEQM3/LFJXZP+QnphH+tpmFha40o5J7SMLGAmaqsdFGmUNI28CepNOnTh7D42oXonePnycXKKocvss6QuptKJbMKE=
2025-08-21 15:47:24.806  3806-3806  WebSocket               com.dspread.mdm.service              I  🔧 未检测到系统代理设置
2025-08-21 15:47:24.817  3806-3806  Provisioning            com.dspread.mdm.service              W  ⚠️ 本地配置格式不正确，期望完整的API响应格式
2025-08-21 15:47:24.830  3806-4054  System.out              com.dspread.mdm.service              I  [com.mediatek.cta.CtaAdapter]:check permission begin!
2025-08-21 15:47:24.831  3806-4054  TrafficStats            com.dspread.mdm.service              D  tagSocket(107) with statsTag=0xffffffff, statsUid=-1
2025-08-21 15:47:25.081  3806-4055  System.out              com.dspread.mdm.service              I  [com.mediatek.cta.CtaAdapter]:check permission begin!
2025-08-21 15:47:25.082  3806-4055  TrafficStats            com.dspread.mdm.service              D  tagSocket(128) with statsTag=0xffffffff, statsUid=-1
2025-08-21 15:47:25.085  3806-4055  ead.mdm.service         com.dspread.mdm.service              E  netdebug -00000001.-958120945 counter:9 ret:-1 connectErrno:115 latencyMs:0 addr:::ffff:54.233.249.113 port:443 processname:ead.mdm.service
                                                                                                    Umask:	00
2025-08-21 15:47:26.997  3806-4058  Common                  com.dspread.mdm.service              I  ✅ WebSocket 物理连接成功，等待服务器 hellMsg
2025-08-21 15:47:27.001  3806-4058  WebSocket               com.dspread.mdm.service              I  🔧 ===== 心跳发送器实际配置 =====
2025-08-21 15:47:27.004  3806-4058  WebSocket               com.dspread.mdm.service              I  🔧 实际 PING 发送间隔: 30000ms (30秒)
2025-08-21 15:47:27.006  3806-4058  WebSocket               com.dspread.mdm.service              I  🔧 PING 定时器名称: PingSender
2025-08-21 15:47:27.009  3806-4058  WebSocket               com.dspread.mdm.service              I  🔧 实际 PONG 发送间隔: 0ms (0秒)
2025-08-21 15:47:27.011  3806-4058  WebSocket               com.dspread.mdm.service              I  🔧 PONG 定时器名称: PongSender
2025-08-21 15:47:27.014  3806-4058  WebSocket               com.dspread.mdm.service              I  🔧 ===============================
2025-08-21 15:47:27.016  3806-4058  WebSocket               com.dspread.mdm.service              I  🔧 启动PING/PONG检测，阈值: 连续3个PING无PONG响应
2025-08-21 15:47:27.023  3806-4059  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接断开 - 服务器主动断开
2025-08-21 15:47:27.026  3806-4059  WebSocket               com.dspread.mdm.service              I  🔧    服务器关闭帧: code=1000, reason=无原因
2025-08-21 15:47:27.029  3806-4059  WebSocket               com.dspread.mdm.service              I  🔧    客户端关闭帧: code=1000, reason=无原因
2025-08-21 15:47:27.031  3806-4059  WebSocket               com.dspread.mdm.service              I  🔧    断开发起方: 服务器
2025-08-21 15:47:27.034  3806-4059  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 业务连接断开
2025-08-21 15:47:27.036  3806-4059  WebSocket               com.dspread.mdm.service              I  🔧 停止PING/PONG检测
2025-08-21 15:47:27.039  3806-4059  WebSocket               com.dspread.mdm.service              I  🔧 reconnect() 被调用，调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onDisconnected:746
2025-08-21 15:47:27.042  3806-4059  WebSocket               com.dspread.mdm.service              I  🔧 开始重连 WebSocket... (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onDisconnected:746)
2025-08-21 15:47:27.047  3806-4059  Provisioning            com.dspread.mdm.service              W  ⚠️ 本地配置格式不正确，期望完整的API响应格式
2025-08-21 15:47:27.051  3806-4059  WebSocket               com.dspread.mdm.service              I  🔧 使用Provisioning配置的重连策略: delayPolicy=1, delaySwitch=1, delayTime=60
2025-08-21 15:47:27.053  3806-4059  WebSocket               com.dspread.mdm.service              I  🔧 当前断开记录数: 10, 延迟开关: 1
2025-08-21 15:47:27.058  3806-4059  WebSocket               com.dspread.mdm.service              I  🔧 断开记录 最早一次断开: 2025-08-21 15:45:01
2025-08-21 15:47:27.063  3806-4059  WebSocket               com.dspread.mdm.service              I  🔧 断开记录 最后一次断开: 2025-08-21 15:47:27
2025-08-21 15:47:27.065  3806-4059  WebSocket               com.dspread.mdm.service              I  🔧 时间差: 2分钟 (配置阈值: 1分钟)
2025-08-21 15:47:27.068  3806-4059  WebSocket               com.dspread.mdm.service              I  🔧 断开间隔正常，使用标准重连策略
2025-08-21 15:47:27.070  3806-4059  WebSocket               com.dspread.mdm.service              I  🔧 重连策略计算完成: 第14次重连，间隔15000ms (15秒)
2025-08-21 15:47:27.073  3806-4059  WebSocket               com.dspread.mdm.service              I  🔧 开始第14次重连，间隔15000ms (15秒) (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onDisconnected:746)
2025-08-21 15:47:42.103  3806-3806  WebSocket               com.dspread.mdm.service              I  🔧 重连 WebSocket: wss://api.dspreadserv.net/status/websocket/register?serialNo=MDAwMDE1MDRQMzAwMDExNTYwNw==&publicKey=TUlHZk1BMEdDU3FHU0liM0RRRUJBUVVBQTRHTkFEQ0JpUUtCZ1FDd3ExK1UxdWhLbnZNY2ZPN3JCZDE3UUUvOWtiNkhzb2hwOU1HYVdGb0RNUlMwRmIzZ1pHeDFHSk9aemd4MzNqZjlVblgrV3FmZndIazBMVVozTzJZVjdpY3pjRkw1bGxoajZ1VFJNS3lZek1Uc1dKR1lIUjZrRVkraCtmd3Z5WUZrVlRVZnRvcWNwVHZOKzlYSDFmM3pMQms0UkNSSEx4SWxOUkd1eHZYYTF3SURBUUFC&query=0&msgVer=3&timestamp=1755762462093&signature=clRPuKUjOkSxfjYCjh+ScuZnIUkz1pm2Y6G+I4ZEuTjst79XUrEHw0AsJD9bVQkCmjePt+CLOtHJj1mvhUOfeqk/68KIz0oYqNmMGhr0UPw6si1Vf5JLO5nrevdDMGGuRMgcnBOZui5M63bopeqIKmnSC8DeRxTjryWK0slqN5U=
2025-08-21 15:47:42.107  3806-3806  WebSocket               com.dspread.mdm.service              I  🔧 未检测到系统代理设置
2025-08-21 15:47:42.117  3806-3806  Provisioning            com.dspread.mdm.service              W  ⚠️ 本地配置格式不正确，期望完整的API响应格式
2025-08-21 15:47:42.133  3806-4064  System.out              com.dspread.mdm.service              I  [com.mediatek.cta.CtaAdapter]:check permission begin!
2025-08-21 15:47:42.134  3806-4064  TrafficStats            com.dspread.mdm.service              D  tagSocket(107) with statsTag=0xffffffff, statsUid=-1
2025-08-21 15:47:42.383  3806-4065  System.out              com.dspread.mdm.service              I  [com.mediatek.cta.CtaAdapter]:check permission begin!
2025-08-21 15:47:42.384  3806-4065  TrafficStats            com.dspread.mdm.service              D  tagSocket(132) with statsTag=0xffffffff, statsUid=-1
2025-08-21 15:47:42.809  3806-3824  System                  com.dspread.mdm.service              W  A resource failed to call close. 
2025-08-21 15:47:42.812  3806-3824  System                  com.dspread.mdm.service              W  A resource failed to call close. 
2025-08-21 15:47:42.816  3806-3824  System                  com.dspread.mdm.service              W  A resource failed to call close. 
2025-08-21 15:47:42.819  3806-3824  System                  com.dspread.mdm.service              W  A resource failed to call close. 
2025-08-21 15:47:42.823  3806-3824  System                  com.dspread.mdm.service              W  A resource failed to call close. 
2025-08-21 15:47:44.098  3806-4066  Common                  com.dspread.mdm.service              I  ✅ WebSocket 物理连接成功，等待服务器 hellMsg
2025-08-21 15:47:44.101  3806-4066  WebSocket               com.dspread.mdm.service              I  🔧 ===== 心跳发送器实际配置 =====
2025-08-21 15:47:44.104  3806-4066  WebSocket               com.dspread.mdm.service              I  🔧 实际 PING 发送间隔: 30000ms (30秒)
2025-08-21 15:47:44.107  3806-4066  WebSocket               com.dspread.mdm.service              I  🔧 PING 定时器名称: PingSender
2025-08-21 15:47:44.110  3806-4066  WebSocket               com.dspread.mdm.service              I  🔧 实际 PONG 发送间隔: 0ms (0秒)
2025-08-21 15:47:44.112  3806-4066  WebSocket               com.dspread.mdm.service              I  🔧 PONG 定时器名称: PongSender
2025-08-21 15:47:44.115  3806-4066  WebSocket               com.dspread.mdm.service              I  🔧 ===============================
2025-08-21 15:47:44.117  3806-4066  WebSocket               com.dspread.mdm.service              I  🔧 启动PING/PONG检测，阈值: 连续3个PING无PONG响应
2025-08-21 15:47:44.124  3806-4067  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接断开 - 服务器主动断开
2025-08-21 15:47:44.127  3806-4067  WebSocket               com.dspread.mdm.service              I  🔧    服务器关闭帧: code=1000, reason=无原因
2025-08-21 15:47:44.130  3806-4067  WebSocket               com.dspread.mdm.service              I  🔧    客户端关闭帧: code=1000, reason=无原因
2025-08-21 15:47:44.132  3806-4067  WebSocket               com.dspread.mdm.service              I  🔧    断开发起方: 服务器
2025-08-21 15:47:44.135  3806-4067  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 业务连接断开
2025-08-21 15:47:44.138  3806-4067  WebSocket               com.dspread.mdm.service              I  🔧 停止PING/PONG检测
2025-08-21 15:47:44.141  3806-4067  WebSocket               com.dspread.mdm.service              I  🔧 reconnect() 被调用，调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onDisconnected:746
2025-08-21 15:47:44.143  3806-4067  WebSocket               com.dspread.mdm.service              I  🔧 开始重连 WebSocket... (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onDisconnected:746)
2025-08-21 15:47:44.148  3806-4067  Provisioning            com.dspread.mdm.service              W  ⚠️ 本地配置格式不正确，期望完整的API响应格式
2025-08-21 15:47:44.150  3806-4067  WebSocket               com.dspread.mdm.service              I  🔧 使用Provisioning配置的重连策略: delayPolicy=1, delaySwitch=1, delayTime=60
2025-08-21 15:47:44.152  3806-4067  WebSocket               com.dspread.mdm.service              I  🔧 当前断开记录数: 10, 延迟开关: 1
2025-08-21 15:47:44.156  3806-4067  WebSocket               com.dspread.mdm.service              I  🔧 断开记录 最早一次断开: 2025-08-21 15:45:12
2025-08-21 15:47:44.160  3806-4067  WebSocket               com.dspread.mdm.service              I  🔧 断开记录 最后一次断开: 2025-08-21 15:47:44
2025-08-21 15:47:44.162  3806-4067  WebSocket               com.dspread.mdm.service              I  🔧 时间差: 2分钟 (配置阈值: 1分钟)
2025-08-21 15:47:44.164  3806-4067  WebSocket               com.dspread.mdm.service              I  🔧 断开间隔正常，使用标准重连策略
2025-08-21 15:47:44.166  3806-4067  WebSocket               com.dspread.mdm.service              I  🔧 重连策略计算完成: 第15次重连，间隔15000ms (15秒)
2025-08-21 15:47:44.168  3806-4067  WebSocket               com.dspread.mdm.service              I  🔧 开始第15次重连，间隔15000ms (15秒) (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onDisconnected:746)
2025-08-21 15:47:59.192  3806-3806  WebSocket               com.dspread.mdm.service              I  🔧 重连 WebSocket: wss://api.dspreadserv.net/status/websocket/register?serialNo=MDAwMDE1MDRQMzAwMDExNTYwNw==&publicKey=TUlHZk1BMEdDU3FHU0liM0RRRUJBUVVBQTRHTkFEQ0JpUUtCZ1FDd3ExK1UxdWhLbnZNY2ZPN3JCZDE3UUUvOWtiNkhzb2hwOU1HYVdGb0RNUlMwRmIzZ1pHeDFHSk9aemd4MzNqZjlVblgrV3FmZndIazBMVVozTzJZVjdpY3pjRkw1bGxoajZ1VFJNS3lZek1Uc1dKR1lIUjZrRVkraCtmd3Z5WUZrVlRVZnRvcWNwVHZOKzlYSDFmM3pMQms0UkNSSEx4SWxOUkd1eHZYYTF3SURBUUFC&query=0&msgVer=3&timestamp=1755762479182&signature=SYUmVaiyamLn/oaFs0U5vXkrPIw70FvzcxQjam8zYe7QN/OwKkuyY2J91GHCXoSDVQY/ohYO+0BusI+E90N6Z8xWBX7ZJnxBEiV3T310sYxCj4UhNq37+C3c7sA9za9LQ0JOgcyIp5vzWAjQnMRn5xrZRSyvM1SKJgCzQXseKZY=
2025-08-21 15:47:59.196  3806-3806  WebSocket               com.dspread.mdm.service              I  🔧 未检测到系统代理设置
2025-08-21 15:47:59.207  3806-3806  Provisioning            com.dspread.mdm.service              W  ⚠️ 本地配置格式不正确，期望完整的API响应格式
2025-08-21 15:47:59.561  3806-4073  System.out              com.dspread.mdm.service              I  [com.mediatek.cta.CtaAdapter]:check permission begin!
2025-08-21 15:47:59.562  3806-4073  TrafficStats            com.dspread.mdm.service              D  tagSocket(107) with statsTag=0xffffffff, statsUid=-1
2025-08-21 15:47:59.811  3806-4074  System.out              com.dspread.mdm.service              I  [com.mediatek.cta.CtaAdapter]:check permission begin!
2025-08-21 15:47:59.812  3806-4074  TrafficStats            com.dspread.mdm.service              D  tagSocket(114) with statsTag=0xffffffff, statsUid=-1
2025-08-21 15:48:01.828  3806-4077  Common                  com.dspread.mdm.service              I  ✅ WebSocket 物理连接成功，等待服务器 hellMsg
2025-08-21 15:48:01.831  3806-4077  WebSocket               com.dspread.mdm.service              I  🔧 ===== 心跳发送器实际配置 =====
2025-08-21 15:48:01.834  3806-4077  WebSocket               com.dspread.mdm.service              I  🔧 实际 PING 发送间隔: 30000ms (30秒)
2025-08-21 15:48:01.836  3806-4077  WebSocket               com.dspread.mdm.service              I  🔧 PING 定时器名称: PingSender
2025-08-21 15:48:01.839  3806-4077  WebSocket               com.dspread.mdm.service              I  🔧 实际 PONG 发送间隔: 0ms (0秒)
2025-08-21 15:48:01.842  3806-4077  WebSocket               com.dspread.mdm.service              I  🔧 PONG 定时器名称: PongSender
2025-08-21 15:48:01.844  3806-4077  WebSocket               com.dspread.mdm.service              I  🔧 ===============================
2025-08-21 15:48:01.847  3806-4077  WebSocket               com.dspread.mdm.service              I  🔧 启动PING/PONG检测，阈值: 连续3个PING无PONG响应
2025-08-21 15:48:01.854  3806-4078  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接断开 - 服务器主动断开
2025-08-21 15:48:01.857  3806-4078  WebSocket               com.dspread.mdm.service              I  🔧    服务器关闭帧: code=1000, reason=无原因
2025-08-21 15:48:01.861  3806-4078  WebSocket               com.dspread.mdm.service              I  🔧    客户端关闭帧: code=1000, reason=无原因
2025-08-21 15:48:01.864  3806-4078  WebSocket               com.dspread.mdm.service              I  🔧    断开发起方: 服务器
2025-08-21 15:48:01.867  3806-4078  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 业务连接断开
2025-08-21 15:48:01.870  3806-4078  WebSocket               com.dspread.mdm.service              I  🔧 停止PING/PONG检测
2025-08-21 15:48:01.874  3806-4078  WebSocket               com.dspread.mdm.service              I  🔧 reconnect() 被调用，调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onDisconnected:746
2025-08-21 15:48:01.877  3806-4078  WebSocket               com.dspread.mdm.service              I  🔧 开始重连 WebSocket... (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onDisconnected:746)
2025-08-21 15:48:01.883  3806-4078  Provisioning            com.dspread.mdm.service              W  ⚠️ 本地配置格式不正确，期望完整的API响应格式
2025-08-21 15:48:01.887  3806-4078  WebSocket               com.dspread.mdm.service              I  🔧 使用Provisioning配置的重连策略: delayPolicy=1, delaySwitch=1, delayTime=60
2025-08-21 15:48:01.890  3806-4078  WebSocket               com.dspread.mdm.service              I  🔧 当前断开记录数: 10, 延迟开关: 1
2025-08-21 15:48:01.896  3806-4078  WebSocket               com.dspread.mdm.service              I  🔧 断开记录 最早一次断开: 2025-08-21 15:45:26
2025-08-21 15:48:01.901  3806-4078  WebSocket               com.dspread.mdm.service              I  🔧 断开记录 最后一次断开: 2025-08-21 15:48:01
2025-08-21 15:48:01.904  3806-4078  WebSocket               com.dspread.mdm.service              I  🔧 时间差: 2分钟 (配置阈值: 1分钟)
2025-08-21 15:48:01.907  3806-4078  WebSocket               com.dspread.mdm.service              I  🔧 断开间隔正常，使用标准重连策略
2025-08-21 15:48:01.910  3806-4078  WebSocket               com.dspread.mdm.service              I  🔧 重连策略计算完成: 第16次重连，间隔15000ms (15秒)
2025-08-21 15:48:01.912  3806-4078  WebSocket               com.dspread.mdm.service              I  🔧 开始第16次重连，间隔15000ms (15秒) (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onDisconnected:746)
