2025-08-21 17:53:25.976   947-998   PackageManager          system_server                        E  Adding duplicate shared id: 1000 name=com.dspread.mdm.service
2025-08-21 17:53:26.736  4367-4367  ead.mdm.service         com.dspread.mdm.service              W  type=1400 audit(0.0:502): avc: denied { write } for name="com.dspread.mdm.service-iR8fxUm2qcT7jjxi1Y6HSQ==" dev="dm-6" ino=15228 scontext=u:r:system_app:s0 tcontext=u:object_r:apk_data_file:s0 tclass=dir permissive=0
2025-08-21 17:53:27.428  4367-4367  NetworkSecurityConfig   com.dspread.mdm.service              D  Using Network Security Config from resource network_security_config debugBuild: true
2025-08-21 17:53:27.433  4367-4367  NetworkSecurityConfig   com.dspread.mdm.service              D  Using Network Security Config from resource network_security_config debugBuild: true
2025-08-21 17:53:27.522  4367-4367  Common                  com.dspread.mdm.service              D  🔧 日志配置：测试环境模式
2025-08-21 17:53:27.528  4367-4367  Common                  com.dspread.mdm.service              D  🔧 [DebugConfig] 调试模式已启用
2025-08-21 17:53:27.530  4367-4367  Common                  com.dspread.mdm.service              D  🔧 SmartMdmServiceApp: 统一配置管理器初始化完成
2025-08-21 17:53:27.534  4367-4367  Common                  com.dspread.mdm.service              D  🔧 SmartMdmServiceApp: 系统目录初始化完成 - /sdcard/Android/data/com.dspread.mdm.service/files/config/
2025-08-21 17:53:27.536  4367-4367  Common                  com.dspread.mdm.service              D  🔧 SmartMdmServiceApp: 开始初始化广播管理器...
2025-08-21 17:53:27.540  4367-4367  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 开始初始化...
2025-08-21 17:53:27.546  4367-4367  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 包更新广播接收器注册完成
2025-08-21 17:53:27.548  4367-4367  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 统一广播接收器注册完成
2025-08-21 17:53:27.551  4367-4367  Common                  com.dspread.mdm.service              I  ✅ BroadcastManager 初始化完成
2025-08-21 17:53:27.579  4367-4367  Common                  com.dspread.mdm.service              I  ✅ SmartMdmServiceApp: 广播管理器初始化完成
2025-08-21 17:53:27.582  4367-4367  Common                  com.dspread.mdm.service              D  🔧 SmartMdmServiceApp: 开始启动所有定时器...
2025-08-21 17:53:27.584  4367-4367  Provisioning            com.dspread.mdm.service              D  🔧 ProvisioningEventHandler 启动Provisioning定时器: 300s (0小时)
2025-08-21 17:53:27.591  4367-4367  Common                  com.dspread.mdm.service              I  ✅ ⏰ 设置初始化定时器成功，下次执行: 300秒后 (0小时)
2025-08-21 17:53:27.593  4367-4367  Common                  com.dspread.mdm.service              D  🔧 SmartMdmServiceApp: 初始化定时器启动成功
2025-08-21 17:53:27.597  4367-4367  Common                  com.dspread.mdm.service              I  ✅ ⏰ 设置心跳定时器成功，下次执行: 60秒后 (1分钟)
2025-08-21 17:53:27.599  4367-4367  Common                  com.dspread.mdm.service              D  🔧 SmartMdmServiceApp: 心跳定时器启动成功
2025-08-21 17:53:27.604  4367-4367  Common                  com.dspread.mdm.service              I  ✅ ⏰ 设置任务执行定时器成功，下次执行: 60秒后 (1分钟)
2025-08-21 17:53:27.606  4367-4367  Common                  com.dspread.mdm.service              D  🔧 SmartMdmServiceApp: 任务执行定时器启动成功
2025-08-21 17:53:27.610  4367-4367  Common                  com.dspread.mdm.service              I  ✅ ⏰ 设置终端信息定时器成功，下次执行: 120秒后 (2分钟)
2025-08-21 17:53:27.612  4367-4367  Common                  com.dspread.mdm.service              D  🔧 SmartMdmServiceApp: 终端信息上传定时器启动成功
2025-08-21 17:53:27.614  4367-4367  Common                  com.dspread.mdm.service              D  🔧 TimerConfig ========== 当前定时器配置 (调试模式) ==========
2025-08-21 17:53:27.616  4367-4367  Common                  com.dspread.mdm.service              D  🔧 TimerConfig ①心跳定时器: 60秒
2025-08-21 17:53:27.618  4367-4367  Common                  com.dspread.mdm.service              D  🔧 TimerConfig ②终端信息上传定时器: 120秒
2025-08-21 17:53:27.621  4367-4367  Common                  com.dspread.mdm.service              D  🔧 TimerConfig ③任务执行定时器: 60秒
2025-08-21 17:53:27.622  4367-4367  Common                  com.dspread.mdm.service              D  🔧 TimerConfig ④服务守护定时器: 120秒
2025-08-21 17:53:27.625  4367-4367  Common                  com.dspread.mdm.service              D  🔧 TimerConfig ⑤初始化定时器: 300秒
2025-08-21 17:53:27.627  4367-4367  Common                  com.dspread.mdm.service              D  🔧 TimerConfig ===============================================
2025-08-21 17:53:27.629  4367-4367  Common                  com.dspread.mdm.service              I  ✅ SmartMdmServiceApp: 所有定时器启动完成
2025-08-21 17:53:27.674  4367-4367  Receiver                com.dspread.mdm.service              D  🔧 ServiceGuardEventHandler 启动服务守护定时器: 120s
2025-08-21 17:53:27.679  4367-4367  Common                  com.dspread.mdm.service              I  ✅ ⏰ 设置服务守护定时器成功，下次执行: 120秒后 (2分钟)
2025-08-21 17:53:27.681  4367-4367  Common                  com.dspread.mdm.service              D  🔧 SmartMdmServiceApp: 服务守护定时器启动成功
2025-08-21 17:53:27.687  4367-4367  Common                  com.dspread.mdm.service              D  🔧 ServiceStartupManager 收到服务启动请求: APPLICATION_STARTUP, 强制启动: false
2025-08-21 17:53:27.694  4367-4367  Common                  com.dspread.mdm.service              D  🔧 ServiceStartupManager 开始执行服务启动: APPLICATION_STARTUP
2025-08-21 17:53:27.696  4367-4367  Common                  com.dspread.mdm.service              D  🔧 ServiceManager: 准备启动后台服务
2025-08-21 17:53:27.698  4367-4367  ContextImpl             com.dspread.mdm.service              W  Calling a method in the system process without a qualified user: android.app.ContextImpl.startService:1669 android.content.ContextWrapper.startService:720 com.dspread.mdm.service.platform.manager.ServiceManager.startBackgroundService:20 com.dspread.mdm.service.platform.manager.ServiceStartupManager.performServiceStart:121 com.dspread.mdm.service.platform.manager.ServiceStartupManager.startService:66 
2025-08-21 17:53:27.702  4367-4367  Common                  com.dspread.mdm.service              D  🔧 ServiceManager: 后台服务启动成功，ComponentName: ComponentInfo{com.dspread.mdm.service/com.dspread.mdm.service.services.SmartMdmBackgroundService}
2025-08-21 17:53:28.748  4367-4367  Common                  com.dspread.mdm.service              D  🔧 ServiceStartupManager 服务启动验证成功: APPLICATION_STARTUP
2025-08-21 17:53:28.750  4367-4367  Common                  com.dspread.mdm.service              D  🔧 ServiceStartupManager 服务启动成功: APPLICATION_STARTUP
2025-08-21 17:53:28.752  4367-4367  Common                  com.dspread.mdm.service              D  🔧 SmartMdmServiceApp: 主服务启动请求已提交
2025-08-21 17:53:28.755  4367-4367  Common                  com.dspread.mdm.service              D  🔧 SmartMdmServiceApp: Application启动完成，包名: com.dspread.mdm.service
2025-08-21 17:53:28.884  4367-4367  SurfaceFactory          com.dspread.mdm.service              I  [static] sSurfaceFactory = com.mediatek.view.impl.SurfaceFactoryImpl@b8d25f1
2025-08-21 17:53:28.894  4367-4367  ViewRootIm...tActivity] com.dspread.mdm.service              D  hardware acceleration = true , fakeHwAccelerated = false, sRendererDisabled = false, forceHwAccelerated = false, sSystemRendererDisabled = false
2025-08-21 17:53:28.901  4367-4367  InputTransport          com.dspread.mdm.service              I  Create ARC handle: 0x9e21c200
2025-08-21 17:53:28.903  4367-4367  PhoneWindow             com.dspread.mdm.service              V  DecorView setVisiblity: visibility = 0, Parent = android.view.ViewRootImpl@23d302d, this = DecorView@8896962[TestActivity]
2025-08-21 17:53:28.910  4367-4367  PhoneWindow             com.dspread.mdm.service              V  DecorView setVisiblity: visibility = 4, Parent = android.view.ViewRootImpl@23d302d, this = DecorView@8896962[TestActivity]
2025-08-21 17:53:28.911  4367-4367  Choreographer           com.dspread.mdm.service              I  Skipped 84 frames!  The application may be doing too much work on its main thread.
2025-08-21 17:53:28.942  4367-4367  Platform                com.dspread.mdm.service              D  🔧 NetworkApi 单例实例已创建
2025-08-21 17:53:28.946  4367-4367  TetheringManager        com.dspread.mdm.service              I  registerTetheringEventCallback:com.dspread.mdm.service
2025-08-21 17:53:28.958  4367-4367  Receiver                com.dspread.mdm.service              D  🔧 网络状态变化: 连接=true, 类型=WIFI
2025-08-21 17:53:28.967  4367-4367  Receiver                com.dspread.mdm.service              D  🔧 网络恢复，尝试重新连接WebSocket
2025-08-21 17:53:28.970  4367-4367  Provisioning            com.dspread.mdm.service              D  🔧 ProvisioningEventHandler 网络状态变化
2025-08-21 17:53:28.977  4367-4367  Provisioning            com.dspread.mdm.service              I  ℹ️ ProvisioningManager初始化完成
2025-08-21 17:53:28.980  4367-4367  Provisioning            com.dspread.mdm.service              D  🔧 状态标志文件不存在，使用默认值
2025-08-21 17:53:28.982  4367-4367  Provisioning            com.dspread.mdm.service              D  🔧 ProvisioningEventHandler 忽略初始启动时的网络连接事件
2025-08-21 17:53:28.986  4367-4367  Receiver                com.dspread.mdm.service              D  🔧 电池状态变化: 电量=91%, 温度=27°C, 充电=true
2025-08-21 17:53:29.087  4367-4367  WebSocket               com.dspread.mdm.service              I  🔧 C0902 被动式上送: battery_level_change_5 (被动: 1)
2025-08-21 17:53:29.101  4367-4367  Platform                com.dspread.mdm.service              D  🔧 DeviceInfoApi 单例实例已创建
2025-08-21 17:53:29.117  4367-4367  WebSocket               com.dspread.mdm.service              I  🔧 消息已缓存: C0902 (缓存数量: 1)
2025-08-21 17:53:29.120  4367-4367  WebSocket               com.dspread.mdm.service              I  🔧 C0902 电池状态上送成功 (1, trigger=battery_level_change_5)
2025-08-21 17:53:29.127  4367-4367  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 服务启动
2025-08-21 17:53:29.138  4367-4367  setContentIntent        com.dspread.mdm.service              D  packageName = com.dspread.mdm.service
2025-08-21 17:53:29.144  4367-4367  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 开始服务初始化
2025-08-21 17:53:29.146   947-975   NotificationService     system_server                        E  enqueueNotificationInternal pkg: com.dspread.mdm.service
2025-08-21 17:53:29.146  4367-4367  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] Android < 14: 启动Provisioning服务
2025-08-21 17:53:29.150  4367-4367  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 服务创建完成
2025-08-21 17:53:29.150  4367-4399  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 启动ProvisioningService
2025-08-21 17:53:29.154  4367-4367  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] onStartCommand调用
2025-08-21 17:53:29.161  4367-4367  Platform                com.dspread.mdm.service              D  🔧 WakeLockManager 获取智能WakeLock成功，持续时间: 60000ms
2025-08-21 17:53:29.166  4367-4399  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] ProvisioningService启动完成
2025-08-21 17:53:29.166  4367-4367  Platform                com.dspread.mdm.service              D  🔧 WakeLockManager 设置WakeLock续期定时器: 60000ms
2025-08-21 17:53:29.168  4367-4400  Provisioning            com.dspread.mdm.service              I  ℹ️ 开始执行Provisioning配置，触发类型: FIRST_BOOT
2025-08-21 17:53:29.172  4367-4400  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] Provisioning进度: 正在下载配置文件...
2025-08-21 17:53:29.174  4367-4400  Provisioning            com.dspread.mdm.service              I  ℹ️ 开始请求远程配置...
2025-08-21 17:53:29.178  4367-4367  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 初始化基础组件
2025-08-21 17:53:29.184  4367-4400  Provisioning            com.dspread.mdm.service              I  ℹ️ 请求配置: https://config.dspreadserv.net/status/config?SN=MDE2MTAwNDAyMDIzMDcwNjAyMjc=&timestamp=1755770009184
2025-08-21 17:53:29.194  4367-4400  System.out              com.dspread.mdm.service              I  [okhttp]:check permission begin!
2025-08-21 17:53:29.195  4367-4400  System                  com.dspread.mdm.service              W  ClassLoader referenced unknown path: system/framework/mediatek-cta.jar
2025-08-21 17:53:29.196  4367-4400  System.out              com.dspread.mdm.service              I  [okhttp] e:java.lang.ClassNotFoundException: com.mediatek.cta.CtaUtils
2025-08-21 17:53:29.202  4367-4402  Platform                com.dspread.mdm.service              W  ⚠️ SpVersionApi SP版本信息格式异常，未找到SP_VERSION标记
2025-08-21 17:53:29.211  4367-4367  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 服务初始化完成
2025-08-21 17:53:29.214  4367-4402  Platform                com.dspread.mdm.service              D  🔧 SpVersionApi 从Settings获取SP版本: STD 582.1.238.8
2025-08-21 17:53:29.230  4367-4367  WebSocket               com.dspread.mdm.service              I  🔧 C0905 主动式上送: manual_trigger (主动: 1)
2025-08-21 17:53:29.239  4367-4367  WebSocket               com.dspread.mdm.service              I  🔧 首次上送，上送当日数据
2025-08-21 17:53:29.242  4367-4367  WebSocket               com.dspread.mdm.service              I  🔧 没有流量数据需要上送
2025-08-21 17:53:29.244  4367-4367  Common                  com.dspread.mdm.service              D  🔧 刷新时自动上送流量统计
2025-08-21 17:53:29.523  4367-4400  System.out              com.dspread.mdm.service              I  [socket]:check permission begin!
2025-08-21 17:53:29.524  4367-4400  System                  com.dspread.mdm.service              W  ClassLoader referenced unknown path: system/framework/mediatek-cta.jar
2025-08-21 17:53:29.525  4367-4400  System.out              com.dspread.mdm.service              I  [socket] e:java.lang.ClassNotFoundException: com.mediatek.cta.CtaUtils
2025-08-21 17:53:30.625  4367-4400  System.out              com.dspread.mdm.service              I  [OkHttp] sendRequest>>
2025-08-21 17:53:30.626  4367-4400  System.out              com.dspread.mdm.service              I  [OkHttp] sendRequest<<
2025-08-21 17:53:31.882  4367-4400  Provisioning            com.dspread.mdm.service              I  ℹ️ 远程配置获取成功
2025-08-21 17:53:31.885  4367-4400  Provisioning            com.dspread.mdm.service              I  ℹ️ 远程配置请求成功
2025-08-21 17:53:31.888  4367-4400  Provisioning            com.dspread.mdm.service              I  ℹ️ 远程配置解析成功 - CID: 1001
2025-08-21 17:53:31.890  4367-4400  Provisioning            com.dspread.mdm.service              I  ℹ️ 创建配置目录成功: /sdcard/Android/data/com.dspread.mdm.service/files/config
2025-08-21 17:53:31.900  4367-4400  Provisioning            com.dspread.mdm.service              I  ℹ️ API响应已保存到本地: /sdcard/Android/data/com.dspread.mdm.service/files/config/provisioning_config.json, 具体内容为:
                                                                                                    {"mode":"dev","data":{"requestTime":"1755770012744","customization":{"system":{"timezone":"Asia\/Hong_Kong","logo":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/test\/logo.bin","logoMd5":"ba1ee533924eae5c408465e7cddcbda4","bootAnimationMd5":"ba1ee533924eae5c408465e7cddcbda4","gps":{"minUpdateTime":"30","scheduleTime":"60","maxLocateTime":"0","minDistance":"10","valid_distance":"500","care":"1"},"powerSaveMode":{"enable":"1","screenTimeout":"0"},"bootAnimation":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/test\/bootanimation.zip"},"polling":{"heartbeatTime":"300","terminalInfoTime":"900","statusApiUrl":"wss:\/\/api.dspreadserv.net\/status\/websocket\/register","remoteUrl":"wss:\/\/remote.dspreadserv.net\/remoteWSS\/websockify","uploadMode":"1","wssreconn":{"pingInterval":"30","delayPolicy":"1","delaySwitch":"1","delayTime":"60"}}},"client":"default","cid":"1001"},"function":"getProvisioningConfig","description":"SUCCESS","stateCode":"0","version":"v1.0.1.20250801"}
2025-08-21 17:53:31.903  4367-4400  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] Provisioning进度: 正在下载 Logo...
2025-08-21 17:53:31.907  4367-4400  Provisioning            com.dspread.mdm.service              I  ℹ️ 文件不存在，开始下载: Logo
2025-08-21 17:53:31.909  4367-4400  Provisioning            com.dspread.mdm.service              I  ℹ️ 下载: https://siot-bucket01.s3.ap-northeast-1.amazonaws.com/test/logo.bin -> /sdcard/Android/data/com.dspread.mdm.service/files/config/logo.bin
2025-08-21 17:53:31.917  4367-4400  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 尝试下载，第1次
2025-08-21 17:53:31.920  4367-4400  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 开始下载
2025-08-21 17:53:31.922  4367-4400  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader URL: https://siot-bucket01.s3.ap-northeast-1.amazonaws.com/test/logo.bin
2025-08-21 17:53:31.924  4367-4400  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 路径: /sdcard/Android/data/com.dspread.mdm.service/files/config/logo.bin
2025-08-21 17:53:31.926  4367-4400  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 本地文件不存在，创建文件
2025-08-21 17:53:31.929  4367-4400  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader allFileLength=0, 需要从服务器获取文件大小
2025-08-21 17:53:31.934  4367-4400  System.out              com.dspread.mdm.service              I  [okhttp]:check permission begin!
2025-08-21 17:53:31.934  4367-4400  System.out              com.dspread.mdm.service              I  [okhttp]:not MMS!
2025-08-21 17:53:31.934  4367-4400  System.out              com.dspread.mdm.service              I  [okhttp]:not Email!
2025-08-21 17:53:32.287  4367-4400  System.out              com.dspread.mdm.service              I  [socket]:check permission begin!
2025-08-21 17:53:32.764  4367-4400  System.out              com.dspread.mdm.service              I  [OkHttp] sendRequest>>
2025-08-21 17:53:32.764  4367-4400  System.out              com.dspread.mdm.service              I  [OkHttp] sendRequest<<
2025-08-21 17:53:33.414  4367-4400  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader HTTP响应码: 200
2025-08-21 17:53:33.417  4367-4400  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 服务器返回Content-Length: 4771451
2025-08-21 17:53:33.419  4367-4400  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 完整下载模式
2025-08-21 17:53:33.421  4367-4400  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 完整下载，使用服务器返回大小: 4771451
2025-08-21 17:53:33.424  4367-4400  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader localFile = true /sdcard/Android/data/com.dspread.mdm.service/files/config/logo.bin, 追加模式: false
2025-08-21 17:53:34.723  4367-4413  ProfileInstaller        com.dspread.mdm.service              D  Installing profile for com.dspread.mdm.service
2025-08-21 17:53:40.075  4367-4400  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 计算MD5: logo.bin
2025-08-21 17:53:40.156  4367-4400  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 文件: 4771451 MD5: ba1ee533924eae5c408465e7cddcbda4
2025-08-21 17:53:40.159  4367-4400  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader MD5校验正确
2025-08-21 17:53:40.161  4367-4400  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 清理连接资源
2025-08-21 17:53:40.163  4367-4400  Provisioning            com.dspread.mdm.service              I  ℹ️ Logo 下载完成: /sdcard/Android/data/com.dspread.mdm.service/files/config/logo.bin
2025-08-21 17:53:40.166  4367-4400  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] Provisioning进度: 正在下载 BootAnimation...
2025-08-21 17:53:40.169  4367-4400  Provisioning            com.dspread.mdm.service              I  ℹ️ 文件不存在，开始下载: BootAnimation
2025-08-21 17:53:40.171  4367-4400  Provisioning            com.dspread.mdm.service              I  ℹ️ 下载: https://siot-bucket01.s3.ap-northeast-1.amazonaws.com/test/bootanimation.zip -> /sdcard/Android/data/com.dspread.mdm.service/files/config/bootanimation.zip
2025-08-21 17:53:40.173  4367-4400  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 尝试下载，第1次
2025-08-21 17:53:40.175  4367-4400  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 开始下载
2025-08-21 17:53:40.177  4367-4400  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader URL: https://siot-bucket01.s3.ap-northeast-1.amazonaws.com/test/bootanimation.zip
2025-08-21 17:53:40.179  4367-4400  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 路径: /sdcard/Android/data/com.dspread.mdm.service/files/config/bootanimation.zip
2025-08-21 17:53:40.181  4367-4400  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 本地文件不存在，创建文件
2025-08-21 17:53:40.184  4367-4400  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader allFileLength=0, 需要从服务器获取文件大小
2025-08-21 17:53:40.189  4367-4400  System.out              com.dspread.mdm.service              I  [okhttp]:check permission begin!
2025-08-21 17:53:40.189  4367-4400  System.out              com.dspread.mdm.service              I  [okhttp]:not MMS!
2025-08-21 17:53:40.189  4367-4400  System.out              com.dspread.mdm.service              I  [okhttp]:not Email!
2025-08-21 17:53:40.190  4367-4400  System.out              com.dspread.mdm.service              I  [OkHttp] sendRequest>>
2025-08-21 17:53:40.190  4367-4400  System.out              com.dspread.mdm.service              I  [OkHttp] sendRequest<<
2025-08-21 17:53:40.480  4367-4400  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader HTTP响应码: 200
2025-08-21 17:53:40.483  4367-4400  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 服务器返回Content-Length: 4771451
2025-08-21 17:53:40.486  4367-4400  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 完整下载模式
2025-08-21 17:53:40.488  4367-4400  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 完整下载，使用服务器返回大小: 4771451
2025-08-21 17:53:40.490  4367-4400  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader localFile = true /sdcard/Android/data/com.dspread.mdm.service/files/config/bootanimation.zip, 追加模式: false
2025-08-21 17:53:46.106  4367-4400  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 计算MD5: bootanimation.zip
2025-08-21 17:53:46.172  4367-4400  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 文件: 4771451 MD5: ba1ee533924eae5c408465e7cddcbda4
2025-08-21 17:53:46.174  4367-4400  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader MD5校验正确
2025-08-21 17:53:46.176  4367-4400  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 清理连接资源
2025-08-21 17:53:46.178  4367-4400  Provisioning            com.dspread.mdm.service              I  ℹ️ BootAnimation 下载完成: /sdcard/Android/data/com.dspread.mdm.service/files/config/bootanimation.zip
2025-08-21 17:53:46.180  4367-4400  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] Provisioning进度: 正在应用配置...
2025-08-21 17:53:46.182  4367-4400  Provisioning            com.dspread.mdm.service              I  ℹ️ 配置应用完成
2025-08-21 17:53:46.184  4367-4400  Provisioning            com.dspread.mdm.service              I  ℹ️ 远程配置成功，标记首次配置完成
2025-08-21 17:53:46.188  4367-4400  Provisioning            com.dspread.mdm.service              D  🔧 状态标志保存成功: /sdcard/Android/data/com.dspread.mdm.service/files/config/provisioning_flags.json
2025-08-21 17:53:46.190  4367-4400  Provisioning            com.dspread.mdm.service              I  ℹ️ ProvisioningManager: 初始启动标记完成，网络重连重试功能已激活
2025-08-21 17:53:46.193  4367-4400  Provisioning            com.dspread.mdm.service              I  ℹ️ Provisioning配置执行完成
2025-08-21 17:53:46.195  4367-4400  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] Provisioning完成，启动核心组件
2025-08-21 17:53:46.206  4367-4367  WebSocket               com.dspread.mdm.service              I  🔧 开始初始化 WebSocket 组件...
2025-08-21 17:53:46.212  4367-4367  WebSocket               com.dspread.mdm.service              I  🔧 初始化 WebSocket 密钥管理器
2025-08-21 17:53:46.216  4367-4367  WebSocket               com.dspread.mdm.service              I  🔧 生成客户端公私钥对
2025-08-21 17:53:46.358  4367-4367  Common                  com.dspread.mdm.service              I  ✅ WebSocket 密钥管理器初始化完成
2025-08-21 17:53:46.366  4367-4367  WebSocket               com.dspread.mdm.service              I  🔧 开始初始化 WsMessageCenter...
2025-08-21 17:53:46.368  4367-4367  WebSocket               com.dspread.mdm.service              I  🔧 WsMessageCenter 首次初始化
2025-08-21 17:53:46.385  4367-4367  WebSocket               com.dspread.mdm.service              I  🔧 WsTaskManager 初始化完成
2025-08-21 17:53:46.486  4367-4367  WebSocket               com.dspread.mdm.service              I  🔧 WsMessageCenter 初始化完成
2025-08-21 17:53:46.488  4367-4367  WebSocket               com.dspread.mdm.service              I  🔧 开始清理终态任务，当前任务数量: 0
2025-08-21 17:53:46.490  4367-4367  WebSocket               com.dspread.mdm.service              I  🔧 没有需要清理的终态任务
2025-08-21 17:53:46.493  4367-4367  WebSocket               com.dspread.mdm.service              I  🔧 开始重置任务状态，当前任务个数: 0
2025-08-21 17:53:46.496  4367-4367  WebSocket               com.dspread.mdm.service              I  🔧 没有需要重置的任务
2025-08-21 17:53:46.498  4367-4367  Common                  com.dspread.mdm.service              I  ✅ 任务状态恢复完成
2025-08-21 17:53:46.507  4367-4367  WebSocket               com.dspread.mdm.service              I  🔧 使用Provisioning配置的WebSocket URL: wss://api.dspreadserv.net/status/websocket/register
2025-08-21 17:53:46.509  4367-4367  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接管理器初始化完成
2025-08-21 17:53:46.512  4367-4367  Common                  com.dspread.mdm.service              I  ✅ WebSocket 管理器初始化完成
2025-08-21 17:53:46.515  4367-4367  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 管理器初始化完成
2025-08-21 17:53:46.518  4367-4367  Common                  com.dspread.mdm.service              I  ✅ WebSocket 组件初始化完成
2025-08-21 17:53:46.520  4367-4367  WebSocket               com.dspread.mdm.service              I  🔧 启动 WebSocket 连接...
2025-08-21 17:53:46.522  4367-4367  WebSocket               com.dspread.mdm.service              I  🔧 开始连接 WebSocket...
2025-08-21 17:53:46.535  4367-4367  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接到: wss://api.dspreadserv.net/status/websocket/register?serialNo=MDE2MTAwNDAyMDIzMDcwNjAyMjc=&publicKey=TUlHZk1BMEdDU3FHU0liM0RRRUJBUVVBQTRHTkFEQ0JpUUtCZ1FDdjNvQ0dKM0ZxaSswSjZxaGlYeDZDcC82TmhwakdiZ05kTm11a25ITmgxa0pHR0l2NnMyVWpjNjJhcHFadmRZQTYvN1dHa0UzRzRpV3ZpaGZMNVFSWUoybzlESUJFcTh5aENGQXZGREpjK2NUNFcyN3FJV1U5YjZZRUt5MkhtL3B5S2ZHdUVBdWdQSTU0TXJueHdZTTZ5UXJsUHMzbnpNRWNjOUczWWhSbnZRSURBUUFC&query=1&msgVer=3&timestamp=1755770026526&signature=I4B7gk80Gyx5l6uSnt1m6fvbmFDZKgwzMVxqTc5O/4JBbNhieWZMMH8P3ZZppOc3RyrYte3cxmEgBHv4MkCSkp4KjdOkWez2qllT4b+voMM7rRiQuZCFRLCONMs838acmtQWSwxO9uIWgFHcj3i7nbDaY3Aui07K3g/VQkgO+oc=
2025-08-21 17:53:46.540  4367-4367  WebSocket               com.dspread.mdm.service              I  🔧 未检测到系统代理设置
2025-08-21 17:53:46.561  4367-4367  WebSocket               com.dspread.mdm.service              I  🔧 ===== WebSocket 库默认配置 =====
2025-08-21 17:53:46.563  4367-4367  WebSocket               com.dspread.mdm.service              I  🔧 默认 PING 间隔: 30000ms (30秒)
2025-08-21 17:53:46.566  4367-4367  WebSocket               com.dspread.mdm.service              I  🔧 默认 PONG 间隔: 0ms (0秒)
2025-08-21 17:53:46.568  4367-4367  WebSocket               com.dspread.mdm.service              I  🔧 帧队列大小: 5
2025-08-21 17:53:46.571  4367-4367  WebSocket               com.dspread.mdm.service              I  🔧 连接超时: 15000ms (15秒)
2025-08-21 17:53:46.573  4367-4367  WebSocket               com.dspread.mdm.service              I  🔧 允许缺失关闭帧: false
2025-08-21 17:53:46.575  4367-4367  WebSocket               com.dspread.mdm.service              I  🔧 ================================
2025-08-21 17:53:46.578  4367-4367  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接请求已发送
2025-08-21 17:53:46.581  4367-4367  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接启动完成
2025-08-21 17:53:46.584  4367-4367  Common                  com.dspread.mdm.service              D  🔧 [ModuleManagerRegistry] 模块管理器注册中心初始化完成
2025-08-21 17:53:46.587  4367-4367  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 核心组件初始化完成
2025-08-21 17:53:46.588  4367-4400  Common                  com.dspread.mdm.service              D  🔧 [ModuleManagerRegistry] 启动所有模块
2025-08-21 17:53:46.594  4367-4424  System.out              com.dspread.mdm.service              I  [socket]:check permission begin!
2025-08-21 17:53:46.596  4367-4400  Common                  com.dspread.mdm.service              D  🔧 Module LogStream Manager status changed to: INITIALIZED
2025-08-21 17:53:46.601  4367-4400  Common                  com.dspread.mdm.service              D  🔧 Module LogStream Manager status changed to: STARTING
2025-08-21 17:53:46.676  4367-4400  Common                  com.dspread.mdm.service              D  🔧 Module LogStream Manager status changed to: RUNNING
2025-08-21 17:53:46.679  4367-4400  Common                  com.dspread.mdm.service              D  🔧 [ModuleManagerRegistry] LogStreamManager启动成功
2025-08-21 17:53:46.706  4367-4400  Common                  com.dspread.mdm.service              D  🔧 Module Geofence Manager status changed to: INITIALIZED
2025-08-21 17:53:46.710  4367-4400  Common                  com.dspread.mdm.service              D  🔧 Module Geofence Manager status changed to: STARTING
2025-08-21 17:53:46.731  4367-4400  Platform                com.dspread.mdm.service              D  🔧 用户交互监控已启动（简化版本）
2025-08-21 17:53:46.731  4367-4367  Platform                com.dspread.mdm.service              D  🔧 [WiFiManagerApi] WiFiManagerApi 单例实例已创建
2025-08-21 17:53:46.733  4367-4400  Platform                com.dspread.mdm.service              D  🔧 使用模拟用户交互检测
2025-08-21 17:53:46.735  4367-4400  Platform                com.dspread.mdm.service              D  🔧 用户交互监控已启动
2025-08-21 17:53:46.737  4367-4400  Service                 com.dspread.mdm.service              D  🔧 [GeofenceManager]Constants.geofenceStatus = 1
2025-08-21 17:53:46.739  4367-4400  Service                 com.dspread.mdm.service              D  🔧 [GeofenceManager]-- Constants.storeId =  -- Constants.storeSsid =  -- Constants.storeIp = 
2025-08-21 17:53:46.742  4367-4400  Common                  com.dspread.mdm.service              D  🔧 Module Geofence Manager status changed to: RUNNING
2025-08-21 17:53:46.744  4367-4400  Common                  com.dspread.mdm.service              D  🔧 [ModuleManagerRegistry] GeofenceManager启动成功
2025-08-21 17:53:46.757  4367-4400  RuleBase                com.dspread.mdm.service              D  🔧 RuleBaseManager 开始初始化RuleBaseManager...
2025-08-21 17:53:46.763  4367-4400  RuleBase                com.dspread.mdm.service              D  🔧 RuleBaseStorage RuleBase存储管理器初始化完成
2025-08-21 17:53:46.765  4367-4400  RuleBase                com.dspread.mdm.service              D  🔧 RuleStateMonitor 状态监控器初始化完成
2025-08-21 17:53:46.767  4367-4400  RuleBase                com.dspread.mdm.service              D  🔧 RuleBaseManager RuleBaseManager初始化完成
2025-08-21 17:53:46.768  4367-4400  Common                  com.dspread.mdm.service              D  🔧 [ModuleManagerRegistry] RuleBaseManager启动成功
2025-08-21 17:53:46.770  4367-4400  Common                  com.dspread.mdm.service              D  🔧 [ModuleManagerRegistry] 所有模块启动完成
2025-08-21 17:53:46.844  4367-4425  System.out              com.dspread.mdm.service              I  [socket]:check permission begin!
2025-08-21 17:53:48.988  4367-4435  Common                  com.dspread.mdm.service              I  ✅ WebSocket 物理连接成功，等待服务器 hellMsg
2025-08-21 17:53:48.991  4367-4435  WebSocket               com.dspread.mdm.service              I  🔧 ===== 心跳发送器实际配置 =====
2025-08-21 17:53:48.994  4367-4435  WebSocket               com.dspread.mdm.service              I  🔧 实际 PING 发送间隔: 30000ms (30秒)
2025-08-21 17:53:48.996  4367-4435  WebSocket               com.dspread.mdm.service              I  🔧 PING 定时器名称: PingSender
2025-08-21 17:53:48.999  4367-4435  WebSocket               com.dspread.mdm.service              I  🔧 实际 PONG 发送间隔: 0ms (0秒)
2025-08-21 17:53:49.001  4367-4435  WebSocket               com.dspread.mdm.service              I  🔧 PONG 定时器名称: PongSender
2025-08-21 17:53:49.003  4367-4435  WebSocket               com.dspread.mdm.service              I  🔧 ===============================
2025-08-21 17:53:49.006  4367-4435  WebSocket               com.dspread.mdm.service              I  🔧 启动PING/PONG检测，阈值: 连续3个PING无PONG响应
2025-08-21 17:53:49.030  4367-4435  Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"msg_state":"P","request_time":"1755580536662","data":{"taskList":[{"beginDate":"2024-08-19 05:15:36","taskType":"02","apkName":"Via","endDate":"9999-12-31 23:59:59","appId":"1755580536662","packName":"mark.via","versionName":"6.2.0","taskId":"1755580536662","versionCode":"20250117"}]},"tranCode":"ST001","request_id":"1755580536662ST001","version":"1","serialNo":"01610040202307060227"}
2025-08-21 17:53:49.033  4367-4435  WebSocket               com.dspread.mdm.service              I  🔧 准备发送响应确认 - requestId: 1755580536662ST001, needResponse: true
2025-08-21 17:53:49.041  4367-4435  WebSocket               com.dspread.mdm.service              I  🔧 非重要消息，不缓存: C0000
2025-08-21 17:53:49.046  4367-4435  WebSocket               com.dspread.mdm.service              I  🔧 非重要消息，不缓存: C0000
2025-08-21 17:53:49.049  4367-4435  WebSocket               com.dspread.mdm.service              I  🔧 已发送C0000响应确认: requestId=1755580536662ST001
2025-08-21 17:53:49.052  4367-4435  Task                    com.dspread.mdm.service              D  🔧 处理任务消息: ST001
2025-08-21 17:53:49.054  4367-4435  Task                    com.dspread.mdm.service              I  ℹ️ 更新任务列表: requestId=1755580536662ST001, 任务数量=1
2025-08-21 17:53:49.057  4367-4435  Task                    com.dspread.mdm.service              D  🔧 任务插入到末尾: taskId=1755580536662
2025-08-21 17:53:49.058  4367-4435  Task                    com.dspread.mdm.service              D  🔧 添加/更新任务: taskId=1755580536662
2025-08-21 17:53:49.062  4367-4435  Task                    com.dspread.mdm.service              D  🔧 保存任务列表到存储: 1 个
2025-08-21 17:53:49.065  4367-4435  Task                    com.dspread.mdm.service              D  🔧 开始处理任务列表，共 1 个任务
2025-08-21 17:53:49.067  4367-4435  Task                    com.dspread.mdm.service              D  🔧 处理任务: taskId=1755580536662, type=02, package=mark.via, apk=Via
2025-08-21 17:53:49.069  4367-4435  Task                    com.dspread.mdm.service              D  🔧 处理卸载任务: 1755580536662
2025-08-21 17:53:49.076  4367-4435  Task                    com.dspread.mdm.service              D  🔧 更新任务状态: taskId=1755580536662, state=D02
2025-08-21 17:53:49.079  4367-4435  Task                    com.dspread.mdm.service              D  🔧 保存任务列表到存储: 1 个
2025-08-21 17:53:49.082  4367-4435  Task                    com.dspread.mdm.service              D  🔧 保存任务列表到存储: 1 个
2025-08-21 17:53:49.084  4367-4435  WebSocket               com.dspread.mdm.service              I  🔧 ST001 主动式上送: task_failed (主动: 2)
2025-08-21 17:53:49.093  4367-4435  WebSocket               com.dspread.mdm.service              I  🔧 消息已缓存: C0108 (缓存数量: 2)
2025-08-21 17:53:49.096  4367-4435  WebSocket               com.dspread.mdm.service              I  🔧 任务结果实时上送: taskId=1755580536662, result=D02 (1)
2025-08-21 17:53:49.110  4367-4435  Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"msg_state":"P","request_time":"1755580524415","data":{"taskList":[{"beginDate":"2024-08-19 05:15:24","taskType":"02","apkName":"OpenVPN for Android","endDate":"9999-12-31 23:59:59","appId":"1755580524415","packName":"de.blinkt.openvpn","versionName":"0.5.36a","taskId":"1755580524415","versionCode":"65"}]},"tranCode":"ST001","request_id":"1755580524415ST001","version":"1","serialNo":"01610040202307060227"}
2025-08-21 17:53:49.112  4367-4435  WebSocket               com.dspread.mdm.service              I  🔧 准备发送响应确认 - requestId: 1755580524415ST001, needResponse: true
2025-08-21 17:53:49.118  4367-4435  WebSocket               com.dspread.mdm.service              I  🔧 非重要消息，不缓存: C0000
2025-08-21 17:53:49.123  4367-4435  WebSocket               com.dspread.mdm.service              I  🔧 非重要消息，不缓存: C0000
2025-08-21 17:53:49.124  4367-4435  WebSocket               com.dspread.mdm.service              I  🔧 已发送C0000响应确认: requestId=1755580524415ST001
2025-08-21 17:53:49.126  4367-4435  Task                    com.dspread.mdm.service              D  🔧 处理任务消息: ST001
2025-08-21 17:53:49.128  4367-4435  Task                    com.dspread.mdm.service              I  ℹ️ 更新任务列表: requestId=1755580524415ST001, 任务数量=1
2025-08-21 17:53:49.131  4367-4435  Task                    com.dspread.mdm.service              D  🔧 任务按时间插入到位置: 0, taskId=1755580524415
2025-08-21 17:53:49.132  4367-4435  Task                    com.dspread.mdm.service              D  🔧 添加/更新任务: taskId=1755580524415
2025-08-21 17:53:49.135  4367-4435  Task                    com.dspread.mdm.service              D  🔧 发现已完成任务: taskId=1755580536662, result=D02
2025-08-21 17:53:49.136  4367-4435  Task                    com.dspread.mdm.service              D  🔧 上送任务结果: taskId=1755580536662, result=D02
2025-08-21 17:53:49.138  4367-4435  Task                    com.dspread.mdm.service              D  🔧 清理已完成任务: taskId=1755580536662, result=D02
2025-08-21 17:53:49.140  4367-4435  Task                    com.dspread.mdm.service              D  🔧 清理了 1 个已完成任务
2025-08-21 17:53:49.142  4367-4435  Task                    com.dspread.mdm.service              D  🔧 保存任务列表到存储: 1 个
2025-08-21 17:53:49.144  4367-4435  Task                    com.dspread.mdm.service              D  🔧 开始处理任务列表，共 1 个任务
2025-08-21 17:53:49.146  4367-4435  Task                    com.dspread.mdm.service              D  🔧 处理任务: taskId=1755580524415, type=02, package=de.blinkt.openvpn, apk=OpenVPN for Android
2025-08-21 17:53:49.148  4367-4435  Task                    com.dspread.mdm.service              D  🔧 处理卸载任务: 1755580524415
2025-08-21 17:53:49.150  4367-4435  Task                    com.dspread.mdm.service              D  🔧 更新任务状态: taskId=1755580524415, state=D02
2025-08-21 17:53:49.153  4367-4435  Task                    com.dspread.mdm.service              D  🔧 保存任务列表到存储: 1 个
2025-08-21 17:53:49.155  4367-4435  Task                    com.dspread.mdm.service              D  🔧 保存任务列表到存储: 1 个
2025-08-21 17:53:49.157  4367-4435  WebSocket               com.dspread.mdm.service              I  🔧 ST001 主动式上送: task_failed (主动: 3)
2025-08-21 17:53:49.166  4367-4435  WebSocket               com.dspread.mdm.service              I  🔧 消息已缓存: C0108 (缓存数量: 3)
2025-08-21 17:53:49.168  4367-4435  WebSocket               com.dspread.mdm.service              I  🔧 任务结果实时上送: taskId=1755580524415, result=D02 (1)
2025-08-21 17:53:49.179  4367-4435  Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"hellMsg":"hello 01610040202307060227","tranCode":"S0000","version":"1","rebootTime":"01:55:12","serialNo":"01610040202307060227","deviceStatus":"6"}
2025-08-21 17:53:49.181  4367-4435  Common                  com.dspread.mdm.service              I  ✅ 收到服务器 hellMsg，WebSocket 业务连接成功！
2025-08-21 17:53:49.182  4367-4435  Common                  com.dspread.mdm.service              I  ✅ WebSocket 业务连接成功！
2025-08-21 17:53:49.184  4367-4435  WebSocket               com.dspread.mdm.service              I  🔧 业务连接成功，重连计数已重置为 0
2025-08-21 17:53:49.188  4367-4435  Platform                com.dspread.mdm.service              D  🔧 WakeLockManager 获取WiFi锁成功，防止WiFi休眠
2025-08-21 17:53:49.190  4367-4435  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接成功，检查自身更新任务状态
2025-08-21 17:53:49.193  4367-4435  Receiver                com.dspread.mdm.service              D  🔧 发送广播: com.dspread.mdm.service.CHECK_SELF_UPDATE_STATUS
2025-08-21 17:53:49.195  4367-4435  WebSocket               com.dspread.mdm.service              I  🔧 开始发送缓存消息，数量: 3
2025-08-21 17:53:49.201  4367-4435  WebSocket               com.dspread.mdm.service              I  🔧 C0902 主动式上送: cached_message_resend (主动: 4)
2025-08-21 17:53:49.208  4367-4435  Common                  com.dspread.mdm.service              I  ✅ 缓存消息发送成功: C0902
2025-08-21 17:53:49.710  4367-4435  WebSocket               com.dspread.mdm.service              I  🔧 C0108 主动式上送: cached_message_resend (主动: 5)
2025-08-21 17:53:49.717  4367-4435  Common                  com.dspread.mdm.service              I  ✅ 缓存消息发送成功: C0108
2025-08-21 17:53:50.220  4367-4435  WebSocket               com.dspread.mdm.service              I  🔧 C0108 主动式上送: cached_message_resend (主动: 6)
2025-08-21 17:53:50.228  4367-4435  Common                  com.dspread.mdm.service              I  ✅ 缓存消息发送成功: C0108
2025-08-21 17:53:50.732  4367-4435  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接成功
2025-08-21 17:53:50.734  4367-4435  WebSocket               com.dspread.mdm.service              I  🔧 设备状态: 6
2025-08-21 17:53:50.736  4367-4435  WebSocket               com.dspread.mdm.service              I  🔧 检查首次连接标志: isFirstTimeWebSocketConnected=true
2025-08-21 17:53:50.739  4367-4435  WebSocket               com.dspread.mdm.service              I  🔧 首次WebSocket连接成功，发送初始化信息并执行初始化检查
2025-08-21 17:53:50.741  4367-4435  WebSocket               com.dspread.mdm.service              I  🔧 开始发送初始化信息（带流量控制）
2025-08-21 17:53:50.744  4367-4435  WebSocket               com.dspread.mdm.service              I  🔧 C0901 被动式上送: first_connection (被动: 2)
2025-08-21 17:53:50.749  4367-4435  Platform                com.dspread.mdm.service              D  🔧 重新收集应用信息
2025-08-21 17:53:50.826  4367-4435  AppManager              com.dspread.mdm.service              I  ℹ️ 应用信息获取完成: 总数120(系统119/用户1) 返回1个
2025-08-21 17:53:50.831  4367-4435  Platform                com.dspread.mdm.service              D  🔧 应用信息: 1 个应用
2025-08-21 17:53:50.842  4367-4435  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0901","serialNo":"01610040202307060227","request_time":"1755770030833","request_id":"1755770030833C0901","version":"1","data":{"apkInfo":[{"packName":"com.dspread.mdm.service","apkName":"service","versionCode":22,"versionName":"1.1.03.20250821.DSPREAD.MDM.SERVICE","updateDate":"2025-08-21 17:53:25"}],"sytemInfo":{"androidVersion":"11","buildNumber":"D60_Baseline_debug_20250708_ota_test","aspl":"2021-12-05"},"serviceInfo":[]},"myVersionName":"1.1.03.20250821.DSPREAD.MDM.SERVICE","terminalDate":"20250821175350"}
2025-08-21 17:53:50.844  4367-4435  WebSocket               com.dspread.mdm.service              I  🔧 C0901 应用信息上送成功 (1)
2025-08-21 17:53:51.847  4367-4435  WebSocket               com.dspread.mdm.service              I  🔧 C0902 被动式上送: first_connection (被动: 3)
2025-08-21 17:53:51.861  4367-4435  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0902","serialNo":"01610040202307060227","request_time":"1755770031854","request_id":"1755770031854C0902","version":"1","data":{"batteryLife":91,"batteryHealth":2,"temprature":"27.8","isCharging":"1","isLowBattery":"0","powerOff":"0","outOfBattery":"0"},"myVersionName":"1.1.03.20250821.DSPREAD.MDM.SERVICE","terminalDate":"20250821175351"}
2025-08-21 17:53:51.863  4367-4435  WebSocket               com.dspread.mdm.service              I  🔧 C0902 电池状态上送成功 (1, trigger=first_connection)
2025-08-21 17:53:52.867  4367-4435  WebSocket               com.dspread.mdm.service              I  🔧 C0903 被动式上送: first_connection (被动: 4)
2025-08-21 17:53:53.012  4367-4435  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0903","serialNo":"01610040202307060227","request_time":"1755770033002","request_id":"1755770033002C0903","version":"1","data":{"hardwareInfo":[{"name":"CPU Usage","desc":"cpu:0.50"},{"name":"storage_usage_status","desc":" 5.42GB \/ 32.00GB ","state":"1"},{"name":"total_memory","desc":" 0.53GB \/ 1.86GB ","state":"1"},{"name":"androidVersion","desc":"11","state":"1"},{"name":"network_status","desc":"wifi","state":"1"},{"name":"OSVer","desc":"11","state":"1"}],"locationInfo":{"gps":{"longitude":"-999.0","latitude":"-999.0","satelliteCount":0,"satelliteCountInUsed":0,"satelliteAverSNR":"0.00","distance":"0.0","lockStatus":"1","lockMeter":"200","proId":"","proName":""},"baseSite":{"MCC":"","MNC":"","CID":"-1","LAC":"-1","DBM":"-999","IMSI":"","ICCID":""},"wifi":{"SSID":"fubox_2.4G","IP":"**************","MAC":"52:C3:63:13:8C:EE","SSTH":"-25","BSSID":"7c:10:c9:d6:9f:10"},"address":"无法获取"}},"myVersionName":"1.1.03.20250821.DSPREAD.MDM.SERVICE","terminalDate":"20250821175353"}
2025-08-21 17:53:53.015  4367-4435  WebSocket               com.dspread.mdm.service              I  🔧 C0903 数据信息上送成功 (1)
2025-08-21 17:53:54.018  4367-4435  WebSocket               com.dspread.mdm.service              I  🔧 C0904 被动式上送: first_connection (被动: 5)
2025-08-21 17:53:54.117  4367-4435  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0904","serialNo":"01610040202307060227","request_time":"1755770034106","request_id":"1755770034106C0904","version":"1","data":{"wifiOption":[{"SSID":"fubox_2.4G","SSTH":"-28"},{"SSID":"2206-5G","SSTH":"-32"},{"SSID":"fubox_5G","SSTH":"-40"},{"SSID":"2206","SSTH":"-41"},{"SSID":"2205_5G","SSTH":"-51"},{"SSID":"2205","SSTH":"-53"},{"SSID":"@Ruijie-1816","SSTH":"-56"},{"SSID":"2207","SSTH":"-67"},{"SSID":"DIRECT-C6-Mi All-in-One Inkjet","SSTH":"-70"},{"SSID":"DIRECT-CC-HP Laser 1188w","SSTH":"-71"},{"SSID":"2306","SSTH":"-72"},{"SSID":"2106","SSTH":"-72"},{"SSID":"2103","SSTH":"-77"},{"SSID":"2207-5G","SSTH":"-80"},{"SSID":"诺富特酒店2208","SSTH":"-81"},{"SSID":"dingjie888","SSTH":"-82"},{"SSID":"FJQS","SSTH":"-83"},{"SSID":"ChinaNet-SnSC","SSTH":"-85"},{"SSID":"2405","SSTH":"-86"},{"SSID":"2103_5G","SSTH":"-86"},{"SSID":"1621","SSTH":"-87"},{"SSID":"HUAWEI_B311_福","SSTH":"-92"},{"SSID":"2106-5G","SSTH":"-94"}],"baseSite":{"MCC":"","MNC":"","CID":"-1","LAC":"-1","DBM":"-999","IMSI":"","ICCID":""},"wifi":{"SSID":"fubox_2.4G","IP":"**************","MAC":"52:C3:63:13:8C:EE","SSTH":"-25","BSSID":"7c:10:c9:d6:9f:10"}},"myVersionName":"1.1.03.20250821.DSPREAD.MDM.SERVICE","terminalDate":"20250821175354"}
2025-08-21 17:53:54.119  4367-4435  WebSocket               com.dspread.mdm.service              I  🔧 C0904 网络状态上送成功 (1)
2025-08-21 17:53:55.122  4367-4435  WebSocket               com.dspread.mdm.service              I  🔧 C0906 被动式上送: first_connection (被动: 6)
2025-08-21 17:53:55.128  4367-4435  Platform                com.dspread.mdm.service              W  ⚠️ SpVersionApi SP版本信息格式异常，未找到SP_VERSION标记
2025-08-21 17:53:55.131  4367-4435  Platform                com.dspread.mdm.service              D  🔧 SpVersionApi 从Settings获取SP版本: STD 582.1.238.8
2025-08-21 17:53:55.145  4367-4435  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0906","serialNo":"01610040202307060227","request_time":"1755770035136","request_id":"1755770035136C0906","version":"1","data":{"firmWareInfo":{"spfw":"STD 582.1.238.8"},"imei_1":"014250620000709","imei_2":"","wifi_mac":"52:c3:63:13:8c:ee","bt_mac":"","bsn":""},"myVersionName":"1.1.03.20250821.DSPREAD.MDM.SERVICE","terminalDate":"20250821175355"}
2025-08-21 17:53:55.147  4367-4435  WebSocket               com.dspread.mdm.service              I  🔧 C0906 设备信息上送成功 (1)
2025-08-21 17:53:55.150  4367-4435  WebSocket               com.dspread.mdm.service              I  🔧 C0109 被动式上送: first_connection (被动: 7)
2025-08-21 17:53:55.158  4367-4435  Platform                com.dspread.mdm.service              D  🔧 重新收集应用信息（强制刷新）
2025-08-21 17:53:55.227  4367-4435  AppManager              com.dspread.mdm.service              I  ℹ️ 应用信息获取完成: 总数120(系统119/用户1) 返回1个
2025-08-21 17:53:55.234  4367-4435  Platform                com.dspread.mdm.service              D  🔧 应用信息: 1 个应用
2025-08-21 17:53:55.259  4367-4383  System                  com.dspread.mdm.service              W  A resource failed to call close. 
2025-08-21 17:53:55.344  4367-4435  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0109","serialNo":"01610040202307060227","request_time":"1755770035328","request_id":"1755770035328C0109","version":"1","data":{"apkInfo":[{"packName":"com.dspread.mdm.service","apkName":"service","versionCode":22,"versionName":"1.1.03.20250821.DSPREAD.MDM.SERVICE","updateDate":"2025-08-21 17:53:25"}],"locationInfo":{"gps":{"longitude":"-999.0","latitude":"-999.0","satelliteCount":0,"satelliteCountInUsed":0,"satelliteAverSNR":"0.00","distance":"0.0","lockStatus":"1","lockMeter":"200","proId":"","proName":""},"baseSite":{"MCC":"","MNC":"","CID":"-1","LAC":"-1","DBM":"-999","IMSI":"","ICCID":""},"wifi":{"SSID":"fubox_2.4G","IP":"**************","MAC":"52:C3:63:13:8C:EE","SSTH":"-25","BSSID":"7c:10:c9:d6:9f:10"},"address":"无法获取"},"hardwareInfo":[{"name":"CPU Usage","desc":"cpu:0.50"},{"name":"storage_usage_status","desc":" 5.42GB \/ 32.00GB ","state":"1"},{"name":"total_memory","desc":" 0.53GB \/ 1.86GB ","state":"1"},{"name":"androidVersion","desc":"11","state":"1"},{"name":"network_status","desc":"wifi","state":"1"},{"name":"OSVer","desc":"11","state":"1"}],"wifiOption":[{"SSID":"fubox_2.4G","SSTH":"-28"},{"SSID":"2206-5G","SSTH":"-32"},{"SSID":"fubox_5G","SSTH":"-40"},{"SSID":"2206","SSTH":"-41"},{"SSID":"2205_5G","SSTH":"-51"},{"SSID":"2205","SSTH":"-53"},{"SSID":"@Ruijie-1816","SSTH":"-56"},{"SSID":"2207","SSTH":"-67"},{"SSID":"DIRECT-C6-Mi All-in-One Inkjet","SSTH":"-70"},{"SSID":"DIRECT-CC-HP Laser 1188w","SSTH":"-71"},{"SSID":"2306","SSTH":"-72"},{"SSID":"2106","SSTH":"-72"},{"SSID":"2103","SSTH":"-77"},{"SSID":"2207-5G","SSTH":"-80"},{"SSID":"诺富特酒店2208","SSTH":"-81"},{"SSID":"dingjie888","SSTH":"-82"},{"SSID":"FJQS","SSTH":"-83"},{"SSID":"ChinaNet-SnSC","SSTH":"-85"},{"SSID":"2405","SSTH":"-86"},{"SSID":"2103_5G","SSTH":"-86"},{"SSID":"1621","SSTH":"-87"},{"SSID":"HUAWEI_B311_福","SSTH":"-92"},{"SSID":"2106-5G","SSTH":"-94"}],"sytemInfo":{"androidVersion":"11","buildNumber":"D60_Baseline_debug_20250708_ota_test","aspl":"2021-12-05"}},"myVersionName":"1.1.03.20250821.DSPREAD.MDM.SERVICE","terminalDate":"20250821175355"}
2025-08-21 17:53:55.346  4367-4435  WebSocket               com.dspread.mdm.service              I  🔧 C0109 终端信息上传完成 (1, trigger=first_connection)
2025-08-21 17:53:55.348  4367-4435  Common                  com.dspread.mdm.service              I  ✅ 初始化信息发送完成
2025-08-21 17:53:55.351  4367-4435  WebSocket               com.dspread.mdm.service              I  🔧 检查服务更新结果
2025-08-21 17:53:55.353  4367-4435  Task                    com.dspread.mdm.service              I  ℹ️ 本地任务列表长度: 1
2025-08-21 17:53:55.356  4367-4435  Task                    com.dspread.mdm.service              D  🔧 本地任务列表内容: [{"beginDate":"2024-08-19 05:15:24","taskType":"02","apkName":"OpenVPN for Android","endDate":"9999-12-31 23:59:59","appId":"1755580524415","packName":"de.blinkt.openvpn","versionName":"0.5.36a","taskId":"1755580524415","versionCode":"65","request_id":"1755580524415ST001","request_time":"1755580524415","silent_install":"","taskResult":"D02","lastUpdateTime":1755770029154}]
2025-08-21 17:53:55.358  4367-4435  Task                    com.dspread.mdm.service              I  ℹ️ 当前应用版本: versionCode=22, versionName=1.1.03.20250821.DSPREAD.MDM.SERVICE
2025-08-21 17:53:55.360  4367-4435  Task                    com.dspread.mdm.service              I  ℹ️ 任务列表数量: 1
2025-08-21 17:53:55.362  4367-4435  Task                    com.dspread.mdm.service              D  🔧 检查任务[0]: taskId=1755580524415, taskType=02, pkgName=de.blinkt.openvpn, versionCode=65, versionName=0.5.36a, taskResult=D02
2025-08-21 17:53:55.364  4367-4435  WebSocket               com.dspread.mdm.service              I  🔧 检查OS升级状态...
2025-08-21 17:53:55.369  4367-4435  Common                  com.dspread.mdm.service              D  🔧 OsUpdateStatusChecker WebSocket连接成功，检查OS升级状态
2025-08-21 17:53:55.371  4367-4435  Common                  com.dspread.mdm.service              D  🔧 OsUpdateStatusChecker 没有待检查的OS升级任务
2025-08-21 17:53:55.373  4367-4435  WebSocket               com.dspread.mdm.service              I  🔧 OS升级状态检查已触发
2025-08-21 17:53:55.375  4367-4435  WebSocket               com.dspread.mdm.service              I  🔧 首次连接初始化检查完成
2025-08-21 17:53:55.386  4367-4435  Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"request_time":"1755770031183","org_request_time":"1755770029088","org_request_id":"1755770029088C0108","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755770031183S0000","serialNo":"01610040202307060227"}
2025-08-21 17:53:55.389  4367-4435  WebSocket               com.dspread.mdm.service              I  🔧 收到响应消息: transCode=S0000, orgRequestId=1755770029088C0108, state=0, remark=
2025-08-21 17:53:55.390  4367-4435  WebSocket               com.dspread.mdm.service              I  🔧 处理任务结果响应: state=0
2025-08-21 17:53:55.392  4367-4435  WebSocket               com.dspread.mdm.service              I  🔧 任务结果上传成功
2025-08-21 17:53:55.402  4367-4435  Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"request_time":"1755770031776","org_request_time":"1755770029161","org_request_id":"1755770029161C0108","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755770031776S0000","serialNo":"01610040202307060227"}
2025-08-21 17:53:55.404  4367-4435  WebSocket               com.dspread.mdm.service              I  🔧 收到响应消息: transCode=S0000, orgRequestId=1755770029161C0108, state=0, remark=
2025-08-21 17:53:55.406  4367-4435  WebSocket               com.dspread.mdm.service              I  🔧 处理任务结果响应: state=0
2025-08-21 17:53:55.407  4367-4435  WebSocket               com.dspread.mdm.service              I  🔧 任务结果上传成功
2025-08-21 17:53:55.641  4367-4435  Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"request_time":"1755770036614","org_request_time":"1755770035136","org_request_id":"1755770035136C0906","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755770036614S0000","serialNo":"01610040202307060227"}
2025-08-21 17:53:55.644  4367-4435  WebSocket               com.dspread.mdm.service              I  🔧 收到响应消息: transCode=S0000, orgRequestId=1755770035136C0906, state=0, remark=
2025-08-21 17:53:55.646  4367-4435  WebSocket               com.dspread.mdm.service              I  🔧 处理设备信息响应: state=0
2025-08-21 17:53:55.647  4367-4435  WebSocket               com.dspread.mdm.service              I  🔧 设备信息上传成功
2025-08-21 17:53:56.059  4367-4435  Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"request_time":"1755770037098","org_request_time":"1755770035328","org_request_id":"1755770035328C0109","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755770037098S0000","serialNo":"01610040202307060227"}
2025-08-21 17:53:56.062  4367-4435  WebSocket               com.dspread.mdm.service              I  🔧 收到响应消息: transCode=S0000, orgRequestId=1755770035328C0109, state=0, remark=
2025-08-21 17:53:56.064  4367-4435  WebSocket               com.dspread.mdm.service              I  🔧 终端信息上传成功
2025-08-21 17:54:05.497  4367-4367  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 没有找到处理器: android.intent.action.TIME_TICK
2025-08-21 17:54:18.991  4367-4436  WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 1 (第1个，待响应: 1)
2025-08-21 17:54:19.391  4367-4435  WebSocket               com.dspread.mdm.service              I  🔧 收到服务器 PONG 帧: 1 (待响应PING: 0)
2025-08-21 17:54:48.992  4367-4436  WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 2 (第2个，待响应: 1)
2025-08-21 17:54:49.598  4367-4435  WebSocket               com.dspread.mdm.service              I  🔧 收到服务器 PONG 帧: 2 (待响应PING: 0)
2025-08-21 17:55:05.553  4367-4367  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 没有找到处理器: android.intent.action.TIME_TICK
2025-08-21 17:55:18.993  4367-4436  WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 3 (第3个，待响应: 1)
2025-08-21 17:55:19.397  4367-4435  WebSocket               com.dspread.mdm.service              I  🔧 收到服务器 PONG 帧: 3 (待响应PING: 0)
2025-08-21 17:55:48.994  4367-4436  WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 4 (第4个，待响应: 1)
2025-08-21 17:55:49.606  4367-4435  WebSocket               com.dspread.mdm.service              I  🔧 收到服务器 PONG 帧: 4 (待响应PING: 0)
2025-08-21 17:56:05.608  4367-4367  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 没有找到处理器: android.intent.action.TIME_TICK
2025-08-21 17:56:18.995  4367-4436  WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 5 (第5个，待响应: 1)
2025-08-21 17:56:19.507  4367-4435  WebSocket               com.dspread.mdm.service              I  🔧 收到服务器 PONG 帧: 5 (待响应PING: 0)
2025-08-21 17:56:48.996  4367-4436  WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 6 (第6个，待响应: 1)
2025-08-21 17:56:49.408  4367-4435  WebSocket               com.dspread.mdm.service              I  🔧 收到服务器 PONG 帧: 6 (待响应PING: 0)
2025-08-21 17:57:05.675  4367-4367  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 没有找到处理器: android.intent.action.TIME_TICK
2025-08-21 17:57:18.998  4367-4436  WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 7 (第7个，待响应: 1)
2025-08-21 17:57:19.513  4367-4435  WebSocket               com.dspread.mdm.service              I  🔧 收到服务器 PONG 帧: 7 (待响应PING: 0)
2025-08-21 17:57:48.999  4367-4436  WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 8 (第8个，待响应: 1)
2025-08-21 17:57:49.516  4367-4435  WebSocket               com.dspread.mdm.service              I  🔧 收到服务器 PONG 帧: 8 (待响应PING: 0)
2025-08-21 17:58:06.063  4367-4367  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 没有找到处理器: android.intent.action.TIME_TICK
2025-08-21 17:58:19.000  4367-4436  WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 9 (第9个，待响应: 1)
2025-08-21 17:58:19.623  4367-4435  WebSocket               com.dspread.mdm.service              I  🔧 收到服务器 PONG 帧: 9 (待响应PING: 0)
