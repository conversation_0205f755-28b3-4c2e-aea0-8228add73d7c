package com.dspread.mdm.service.ui.view

import android.annotation.SuppressLint
import android.content.Context
import android.content.res.TypedArray
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.graphics.Rect
import android.graphics.RectF
import android.util.AttributeSet
import android.util.TypedValue
import android.widget.EditText
import com.dspread.mdm.service.R

@SuppressLint("AppCompatCustomView")
class PasswordEditText : EditText {

    constructor(context: Context) : this(context, null)

    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs) {
        initPaint()
        initAttributeSet(context, attrs)
        // 不显示光标
        isCursorVisible = false
    }

    // 画笔
    private var mPaint: Paint = Paint()
    
    // 一个密码所占的宽度
    private var mPasswordItemWidth: Int = 0
    
    // 密码的个数默认为6位数
    private var mPasswordNumber: Int = 6
    
    // 是否显示密码
    private var mShowPassword: Boolean = false
    
    // 背景边框颜色
    private var mBgColor: Int = Color.parseColor("#979797")
    
    // 背景边框大小
    private var mBgSize: Int = 1
    
    // 背景边框圆角大小
    private var mBgCorner: Int = 0
    
    // 分割线的颜色
    private var mDivisionLineColor: Int = mBgColor
    
    // 分割线的大小
    private var mDivisionLineSize: Int = 1
    
    // 密码圆点的颜色
    private var mPasswordColor: Int = Color.parseColor("#d8d8d8")
    
    // 密码圆点的半径大小
    private var mPasswordRadius: Int = 8
    
    // 文本颜色
    private var mTextColor: Int = Color.parseColor("#383838")
    
    // 文本大小
    private var mTextSize: Int = 80

    // 设置当前密码是否已满的接口回调
    private var mListener: PasswordFullListener? = null

    fun setOnPasswordFullListener(listener: PasswordFullListener?) {
        this.mListener = listener
    }

    /**
     * 密码已经全部填满
     */
    interface PasswordFullListener {
        fun passwordFull(password: String, bfull: Boolean)
    }



    /**
     * 初始化属性
     */
    private fun initAttributeSet(context: Context, attrs: AttributeSet?) {
        if (attrs != null) {
            val array: TypedArray = context.obtainStyledAttributes(attrs, R.styleable.PasswordEditText)
            // 获取大小
            mDivisionLineSize = array.getDimension(R.styleable.PasswordEditText_divisionLineSize, dip2px(mDivisionLineSize).toFloat()).toInt()
            mPasswordRadius = array.getDimension(R.styleable.PasswordEditText_passwordRadius, dip2px(mPasswordRadius).toFloat()).toInt()
            mBgSize = array.getDimension(R.styleable.PasswordEditText_bgSize, dip2px(mBgSize).toFloat()).toInt()
            mBgCorner = array.getDimension(R.styleable.PasswordEditText_bgCorner, 0f).toInt()
            // 获取颜色
            mBgColor = array.getColor(R.styleable.PasswordEditText_bgColor, mBgColor)
            mDivisionLineColor = array.getColor(R.styleable.PasswordEditText_divisionLineColor, mDivisionLineColor)
            mPasswordColor = array.getColor(R.styleable.PasswordEditText_passwordColor, mPasswordColor)
            mShowPassword = array.getBoolean(R.styleable.PasswordEditText_showPassword, mShowPassword)
            array.recycle()
        }
    }

    /**
     * 初始化画笔
     */
    private fun initPaint() {
        mPaint = Paint()
        mPaint.isAntiAlias = true
        mPaint.isDither = true
    }

    /**
     * dip 转 px
     */
    private fun dip2px(dip: Int): Int {
        return TypedValue.applyDimension(
            TypedValue.COMPLEX_UNIT_DIP,
            dip.toFloat(), resources.displayMetrics
        ).toInt()
    }

    override fun onDraw(canvas: Canvas) {
        val passwordWidth = width - (mPasswordNumber - 1) * mDivisionLineSize
        mPasswordItemWidth = passwordWidth / mPasswordNumber
        // 绘制背景
        drawBg(canvas)
        // 绘制分割线
        drawDivisionLine(canvas)
        // 绘制密码
        drawHidePassword(canvas)

        // 当前密码是不是满了
        mListener?.let { listener ->
            val password = text.toString().trim()
            if (password.length < mPasswordNumber) {
                listener.passwordFull(password, false)
            } else {
                listener.passwordFull(password, true)
            }
        }
    }

    /**
     * 绘制背景
     */
    private fun drawBg(canvas: Canvas) {
        mPaint.color = mBgColor
        // 设置画笔为空心
        mPaint.style = Paint.Style.STROKE
        mPaint.strokeWidth = mBgSize.toFloat()
        val rectF = RectF(
            mBgSize.toFloat(),
            mBgSize.toFloat(),
            (width - mBgSize).toFloat(),
            (height - mBgSize).toFloat()
        )
        // 如果没有设置圆角，就画矩形
        if (mBgCorner == 0) {
            canvas.drawRect(rectF, mPaint)
        } else {
            // 如果有设置圆角就画圆矩形
            canvas.drawRoundRect(rectF, mBgCorner.toFloat(), mBgCorner.toFloat(), mPaint)
        }
    }

    /**
     * 绘制分割线
     */
    private fun drawDivisionLine(canvas: Canvas) {
        mPaint.strokeWidth = mDivisionLineSize.toFloat()
        mPaint.color = mDivisionLineColor
        for (i in 0 until mPasswordNumber - 1) {
            val startX = ((i + 1) * mDivisionLineSize + (i + 1) * mPasswordItemWidth + mBgSize).toFloat()
            canvas.drawLine(startX, mBgSize.toFloat(), startX, (height - mBgSize).toFloat(), mPaint)
        }
    }

    /**
     * 绘制隐藏的密码
     */
    private fun drawHidePassword(canvas: Canvas) {
        val passwordLength = text.length
        if (!mShowPassword) {
            mPaint.color = mPasswordColor
            // 设置画笔为实心
            mPaint.style = Paint.Style.FILL
            for (i in 0 until passwordLength) {
                val cx = (i * mDivisionLineSize + i * mPasswordItemWidth + mPasswordItemWidth / 2 + mBgSize).toFloat()
                canvas.drawCircle(cx, (height / 2).toFloat(), mPasswordRadius.toFloat(), mPaint)
            }
        } else {
            mPaint.color = mTextColor
            mPaint.textSize = mTextSize.toFloat()
            // 设置画笔为实心
            mPaint.style = Paint.Style.FILL
            for (i in 0 until passwordLength) {
                val textChar = text[i].toString()
                val rect = Rect()
                mPaint.getTextBounds(textChar, 0, textChar.length, rect)
                val cx = (i * mDivisionLineSize + i * mPasswordItemWidth + mPasswordItemWidth / 2 + mBgSize - rect.width() / 2).toFloat()
                canvas.drawText(textChar, cx, (height / 2 + rect.height() / 2).toFloat(), mPaint)
            }
        }
    }
}
