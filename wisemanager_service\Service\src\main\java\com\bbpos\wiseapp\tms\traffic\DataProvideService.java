package com.bbpos.wiseapp.tms.traffic;

import android.app.Service;
import android.content.Intent;
import android.os.Binder;
import android.os.IBinder;
import android.os.RemoteException;
import android.util.Log;

import com.bbpos.wiseapp.logger.BBLog;
import com.bbpos.wiseapp.tms.utils.ContextUtil;
import com.bbpos.wiseapp.tms.traffic.model.TrafficData;
import com.bbpos.wiseapp.tms.traffic.utils.DataCollectDBHelper;
import com.bbpos.wiseapp.tms.utils.ParameterName;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.List;

/**
 * 提供数据收集服务收集出的数据
 * <AUTHOR>
 *
 */
public class DataProvideService extends Service{
	private static final String TAG = DataProvideService.class.getName();
	private ServiceBinder serviceBinder = new ServiceBinder(); 
	
	@Override
	public IBinder onBind(Intent arg0) {
		return serviceBinder;
	}

	public class ServiceBinder extends Binder{
		public DataProvideService getService(){
            return DataProvideService.this;
        }
	}

	/**返回该日期下应用流量使用情况
	 * @return JsonObj key:pkgName value:traffic*/
	public JSONObject doTrafficDataCollect(String dateStr) throws RemoteException {
		DataCollectDBHelper dbHelper = new DataCollectDBHelper(ContextUtil.getInstance());
		List<TrafficData> trafficList = dbHelper.getTrafficDataList(dateStr);
		try {
			JSONObject jsonObj = new JSONObject();
			for (TrafficData trafficData : trafficList) {
				BBLog.v(BBLog.TAG, trafficData.pkgName+":"+trafficData.rxBytes+","+trafficData.txBytes+"-"+trafficData.netType);
				String pkStr = trafficData.pkgName;
				if (jsonObj.has(pkStr)) {
					long trafficBytes = jsonObj.getLong(pkStr);
					jsonObj.put(pkStr, trafficData.rxBytes + trafficData.txBytes + trafficBytes);
				} else {
					jsonObj.put(pkStr, trafficData.rxBytes + trafficData.txBytes);
				}
			}
			
			return jsonObj;
		} catch (Exception e) {
			e.printStackTrace();
		}
		return null;
	}
	
	/**返回该日期下应用流量使用情况
	 * @return JsonObj key:pkgName value:traffic*/
	public JSONObject doTrafficDataCollect(String dateStr,String pkgName) throws RemoteException {
		DataCollectDBHelper dbHelper = new DataCollectDBHelper(ContextUtil.getInstance());
		List<TrafficData> trafficList = dbHelper.getTrafficDataList(dateStr,pkgName);
		try {
			JSONObject jsonObj = new JSONObject();
			for (TrafficData trafficData : trafficList) {
				BBLog.v(BBLog.TAG, trafficData.pkgName+":"+trafficData.rxBytes+","+trafficData.txBytes+"-"+trafficData.netType);
				String pkStr = trafficData.pkgName;
				if (jsonObj.has(pkStr)) {
					long trafficBytes = jsonObj.getLong(pkStr);
					jsonObj.put(pkStr, trafficData.rxBytes + trafficData.txBytes + trafficBytes);
				} else {
					jsonObj.put(pkStr, trafficData.rxBytes + trafficData.txBytes);
				}
			}
			
			return jsonObj;
		} catch (Exception e) {
			e.printStackTrace();
		}
		return null;
	}
	
	/**流量:区分mobile与wifi
	 * @return 流量数据<br/>key值为ParameterName.appPackageName ParameterName.trafficNetType ParameterName.trafficBytes*/
	public JSONArray doWMTrafficDataCollect(String dateStr) throws RemoteException {
		JSONArray trafficDataArray = new JSONArray();
		DataCollectDBHelper dbHelper = new DataCollectDBHelper(ContextUtil.getInstance());
		List<TrafficData> trafficList = dbHelper.getTrafficDataList(dateStr);
		try {
			for (TrafficData trafficData : trafficList) {
				BBLog.v(BBLog.TAG, trafficData.pkgName+trafficData.rxBytes+","+trafficData.txBytes);
				long trafficBytesTmp = trafficData.rxBytes + trafficData.txBytes;
				JSONObject trafficDataJson = new JSONObject();
				trafficDataJson.put(ParameterName.appPackageName, trafficData.pkgName);
				trafficDataJson.put(ParameterName.appVersionName, trafficData.versionName);
				trafficDataJson.put(ParameterName.trafficNetType, trafficData.netType);
				trafficDataJson.put(ParameterName.trafficBytes, trafficBytesTmp);
				trafficDataArray.put(trafficDataJson);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return trafficDataArray;
	}
}
