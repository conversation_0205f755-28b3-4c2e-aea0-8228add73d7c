package com.dspread.mdm.service.config

import android.content.Context
import android.content.SharedPreferences
import com.dspread.mdm.service.utils.log.Logger

/**
 * 统一配置管理器
 * 作为所有配置的统一入口，管理debug模式和各个子配置
 */
object DebugConfig {

    private const val TAG = "[DebugConfig]"
    private const val PREF_NAME = "debug_config"
    private const val KEY_DEBUG_MODE = "debug_mode_enabled"
    private const val KEY_TIMER_DEBUG_MODE = "timer_debug_mode_enabled"

    private var sharedPreferences: SharedPreferences? = null
    private var context: Context? = null
    
    /**
     * 初始化统一配置管理器
     */
    fun init(appContext: Context) {
        context = appContext.applicationContext
        sharedPreferences = context?.getSharedPreferences(PREF_NAME, Context.MODE_PRIVATE)

        // 初始化所有子配置
        initializeAllConfigs()

        // 默认设置为生产模式
        setDebugMode(enabled = false)

        Logger.com("$TAG 统一配置管理器初始化完成，当前模式: ${if (isDebugMode()) "调试模式" else "生产模式"}")
    }
    
    /**
     * 是否启用调试模式
     */
    fun isDebugMode(): Boolean {
        return sharedPreferences?.getBoolean(KEY_DEBUG_MODE, false) ?: false
    }
    
    /**
     * 设置调试模式
     */
    fun setDebugMode(enabled: Boolean) {
        sharedPreferences?.edit()?.putBoolean(KEY_DEBUG_MODE, enabled)?.apply()

        // 同步更新所有子配置
        updateAllConfigs(enabled)

        Logger.com("$TAG 调试模式${if (enabled) "已启用" else "已禁用"}")
        logCurrentConfig()
    }
    
    /**
     * 切换调试模式
     */
    fun toggleDebugMode(): Boolean {
        val newMode = !isDebugMode()
        setDebugMode(newMode)
        return newMode
    }
    
    // ========== 统一配置管理方法 ==========

    /**
     * 设置LogStream配置
     */
    fun setLogStreamEnabled(enabled: Boolean) {
        LogStreamConfig.setEnabled(enabled)
    }

    /**
     * 设置Provisioning配置目录
     */
    fun setProvisioningDebugMode(enabled: Boolean) {
        ProvisionConfig.setDebugMode(enabled)
    }

    /**
     * 设置日志配置模式
     */
    fun setLogConfigMode(debugMode: Boolean) {
        if (debugMode) {
            LogConfig.setDebugMode()
        } else {
            LogConfig.setProductionMode()
        }
    }

    /**
     * 设置定时器调试模式
     */
    fun setTimerDebugMode(enabled: Boolean) {
        sharedPreferences?.edit()?.putBoolean(KEY_TIMER_DEBUG_MODE, enabled)?.apply()
        Logger.com("$TAG 定时器调试模式${if (enabled) "已启用" else "已禁用"}")

        // 打印当前定时器配置
        context?.let { TimerConfig.printCurrentConfig(it) }
    }

    /**
     * 是否启用定时器调试模式
     */
    fun isTimerDebugMode(): Boolean {
        return sharedPreferences?.getBoolean(KEY_TIMER_DEBUG_MODE, false) ?: false
    }

    /**
     * 切换定时器调试模式
     */
    fun toggleTimerDebugMode(): Boolean {
        val newMode = !isTimerDebugMode()
        setTimerDebugMode(newMode)
        return newMode
    }

    /**
     * 获取LogStream配置
     */
    fun getLogStreamConfig() = LogStreamConfig

    /**
     * 获取Provision配置
     */
    fun getProvisionConfig() = ProvisionConfig

    /**
     * 获取Log配置
     */
    fun getLogConfig() = LogConfig

    /**
     * 获取Timer配置
     */
    fun getTimerConfig() = TimerConfig
    
    /**
     * 获取统一配置描述
     */
    fun getConfigDescription(): String {
        val mode = if (isDebugMode()) "调试模式" else "生产模式"
        return """
            ========== 统一配置状态 ($mode) ==========

            ${LogStreamConfig.getConfigDescription()}

            ${context?.let { ProvisionConfig.getConfigDescription(it) } ?: "Provision配置未初始化"}

            ${LogConfig.getCurrentStatus()}
            =========================================
        """.trimIndent()
    }
    
    /**
     * 记录当前配置
     */
    private fun logCurrentConfig() {
        Logger.com("$TAG ========== 当前日志配置 ==========")
        Logger.com("$TAG ${getConfigDescription()}")
        Logger.com("$TAG =====================================")
    }
    
    /**
     * 格式化文件大小
     */
    private fun formatSize(bytes: Long): String {
        return when {
            bytes >= 1024 * 1024 * 1024 -> "${bytes / (1024 * 1024 * 1024)}GB"
            bytes >= 1024 * 1024 -> "${bytes / (1024 * 1024)}MB"
            bytes >= 1024 -> "${bytes / 1024}KB"
            else -> "${bytes}B"
        }
    }
    
    /**
     * 初始化所有子配置
     */
    private fun initializeAllConfigs() {
        context?.let { ctx ->
            LogStreamConfig.init(ctx, isDebugMode())
            ProvisionConfig.setDebugMode(isDebugMode())

            if (isDebugMode()) {
                LogConfig.setDebugMode()
            } else {
                LogConfig.setProductionMode()
            }
        }
    }

    /**
     * 更新所有子配置
     */
    private fun updateAllConfigs(debugMode: Boolean) {
        context?.let { ctx ->
            LogStreamConfig.setDebugMode(debugMode)
            ProvisionConfig.setDebugMode(debugMode)

            setTimerDebugMode(debugMode)

            if (debugMode) {
                LogConfig.setDebugMode()
            } else {
                LogConfig.setProductionMode()
            }
        }
    }

    /**
     * 重置为默认配置
     */
    fun resetToDefault() {
        setDebugMode(false)
        LogStreamConfig.resetToDefault()
        Logger.com("$TAG 已重置为默认配置（生产模式）")
    }

    /**
     * 获取调试信息
     */
    fun getDebugInfo(): Map<String, Any> {
        return mapOf(
            "debugMode" to isDebugMode(),
            "timerDebugMode" to isTimerDebugMode(),
            "logStreamEnabled" to LogStreamConfig.isEnabled(),
            "provisionConfigPath" to (context?.let { ProvisionConfig.getConfigDownloadPath(it) } ?: "未初始化"),
            "logConfigStatus" to LogConfig.getCurrentStatus(),
            "configDescription" to getConfigDescription()
        )
    }

}
