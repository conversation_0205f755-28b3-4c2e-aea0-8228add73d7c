package com.dspread.mdm.service.modules.apn

import android.annotation.SuppressLint
import android.content.Context
import android.net.ConnectivityManager
import android.net.Network
import android.net.NetworkCapabilities
import android.net.NetworkRequest
import android.os.Build
import android.os.Handler
import android.os.Looper
import android.telephony.*
import com.dspread.mdm.service.modules.apn.model.ApnUtils
import com.dspread.mdm.service.modules.apn.model.DataUsage
import com.dspread.mdm.service.modules.apn.model.NetworkStatus
import com.dspread.mdm.service.modules.apn.model.NetworkType
import com.dspread.mdm.service.utils.log.Logger
import kotlinx.coroutines.*
import java.net.InetAddress
import java.util.concurrent.atomic.AtomicBoolean
import java.util.concurrent.atomic.AtomicReference

/**
 * 网络状态监控器
 * 监控移动网络连接状态、信号质量和数据传输
 */
class NetworkStatusMonitor(
    private val context: Context
) {
    
    companion object {
        private const val TAG = "[NetworkStatusMonitor]"
        private const val PING_TIMEOUT = 5000 // 5秒ping超时
        private const val SPEED_TEST_DURATION = 10000L // 10秒速度测试
        private const val MONITOR_INTERVAL = 30000L // 30秒监控间隔
    }
    
    private val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
    private val telephonyManager = context.getSystemService(Context.TELEPHONY_SERVICE) as TelephonyManager

    private val isMonitoring = AtomicBoolean(false)
    private val currentNetworkStatus = AtomicReference<NetworkStatus>()
    private var monitoringJob: Job? = null

    // 网络状态回调
    private var statusCallback: ((NetworkStatus) -> Unit)? = null

    // 主线程Handler，用于PhoneStateListener
    private val mainHandler = Handler(Looper.getMainLooper())
    
    // 网络连接回调
    private val networkCallback = object : ConnectivityManager.NetworkCallback() {
        override fun onAvailable(network: Network) {
            Logger.apn("$TAG 网络连接可用: $network")
            updateNetworkStatus()
        }
        
        override fun onLost(network: Network) {
            Logger.apn("$TAG 网络连接丢失: $network")
            updateNetworkStatus()
        }
        
        override fun onCapabilitiesChanged(network: Network, networkCapabilities: NetworkCapabilities) {
            Logger.apn("$TAG 网络能力变化: $network")
            updateNetworkStatus()
        }
    }
    
    // 信号强度监听器 - 延迟初始化，确保在主线程中创建
    private var signalStrengthListener: PhoneStateListener? = null

    /**
     * 创建信号强度监听器（必须在主线程中调用）
     */
    private fun createSignalStrengthListener(): PhoneStateListener {
        return object : PhoneStateListener() {
            override fun onSignalStrengthsChanged(signalStrength: SignalStrength) {
                Logger.apn("$TAG 信号强度变化")
                updateNetworkStatus()
            }

            override fun onDataConnectionStateChanged(state: Int, networkType: Int) {
                Logger.apn("$TAG 数据连接状态变化: state=$state, networkType=$networkType")
                updateNetworkStatus()
            }

            override fun onServiceStateChanged(serviceState: ServiceState) {
                Logger.apn("$TAG 服务状态变化: ${serviceState.state}")
                updateNetworkStatus()
            }
        }
    }
    
    /**
     * 开始监控
     */
    fun startMonitoring(callback: ((NetworkStatus) -> Unit)? = null) {
        if (isMonitoring.compareAndSet(false, true)) {
            Logger.apn("$TAG 开始网络状态监控")
            
            statusCallback = callback
            
            try {
                // 注册网络状态监听
                registerNetworkCallback()
                
                // 注册信号强度监听
                registerSignalStrengthListener()
                
                // 启动定期监控任务
                startMonitoringTask()
                
                // 立即更新一次状态
                updateNetworkStatus()
                
                Logger.apn("$TAG 网络状态监控启动成功")
            } catch (e: Exception) {
                Logger.apnE("$TAG 启动网络状态监控失败", e)
                isMonitoring.set(false)
            }
        }
    }
    
    /**
     * 停止监控
     */
    fun stopMonitoring() {
        if (isMonitoring.compareAndSet(true, false)) {
            Logger.apn("$TAG 停止网络状态监控")
            
            try {
                // 取消网络状态监听
                unregisterNetworkCallback()
                
                // 取消信号强度监听
                unregisterSignalStrengthListener()
                
                // 停止监控任务
                monitoringJob?.cancel()
                monitoringJob = null
                
                statusCallback = null
                
                Logger.apn("$TAG 网络状态监控已停止")
            } catch (e: Exception) {
                Logger.apnE("$TAG 停止网络状态监控失败", e)
            }
        }
    }
    
    /**
     * 获取当前网络状态
     */
    fun getCurrentNetworkStatus(): NetworkStatus {
        return currentNetworkStatus.get() ?: detectNetworkStatus()
    }
    
    /**
     * 检测网络状态
     */
    @SuppressLint("MissingPermission")
    private fun detectNetworkStatus(): NetworkStatus {
        return try {
            val activeNetwork = connectivityManager.activeNetwork
            val networkCapabilities = activeNetwork?.let { connectivityManager.getNetworkCapabilities(it) }
            
            val isConnected = activeNetwork != null && networkCapabilities != null
            val networkType = determineNetworkType(networkCapabilities)
            val signalStrength = getSignalStrength()
            val dataUsage = getCurrentDataUsage()
            
            // 测试网络延迟
            // val latency = if (isConnected) measureLatency() else -1L // 暂时注释
            val latency = if (isConnected) 50L else -1L // 临时返回固定值
            
            NetworkStatus(
                isConnected = isConnected,
                networkType = networkType,
                signalStrength = signalStrength,
                dataUsage = dataUsage,
                latency = latency,
                timestamp = System.currentTimeMillis()
            )
        } catch (e: Exception) {
            Logger.apnE("$TAG 检测网络状态失败", e)
            NetworkStatus(
                isConnected = false,
                networkType = NetworkType.NONE,
                signalStrength = -1,
                dataUsage = DataUsage(0, 0, 0, 0, System.currentTimeMillis(), System.currentTimeMillis())
            )
        }
    }
    
    /**
     * 确定网络类型
     */
    private fun determineNetworkType(capabilities: NetworkCapabilities?): NetworkType {
        return when {
            capabilities == null -> NetworkType.NONE
            
            capabilities.hasTransport(NetworkCapabilities.TRANSPORT_WIFI) -> NetworkType.WIFI
            
            capabilities.hasTransport(NetworkCapabilities.TRANSPORT_CELLULAR) -> {
                // 根据电话管理器确定移动网络类型
                NetworkType.fromTelephonyType(telephonyManager.networkType)
            }
            
            capabilities.hasTransport(NetworkCapabilities.TRANSPORT_ETHERNET) -> NetworkType.ETHERNET
            
            capabilities.hasTransport(NetworkCapabilities.TRANSPORT_BLUETOOTH) -> NetworkType.BLUETOOTH
            
            capabilities.hasTransport(NetworkCapabilities.TRANSPORT_VPN) -> NetworkType.VPN
            
            else -> NetworkType.OTHER
        }
    }
    
    /**
     * 获取信号强度
     */
    @SuppressLint("MissingPermission")
    private fun getSignalStrength(): Int {
        return try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
                val signalStrength = telephonyManager.signalStrength
                signalStrength?.level ?: -1
            } else {
                // 对于较旧的Android版本，使用CellInfo获取信号强度
                val cellInfos = telephonyManager.allCellInfo
                if (cellInfos != null && cellInfos.isNotEmpty()) {
                    val registeredCell = cellInfos.find { it.isRegistered }
                    when (registeredCell) {
                        is android.telephony.CellInfoGsm -> {
                            registeredCell.cellSignalStrength.level
                        }
                        is android.telephony.CellInfoLte -> {
                            registeredCell.cellSignalStrength.level
                        }
                        is android.telephony.CellInfoWcdma -> {
                            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR2) {
                                registeredCell.cellSignalStrength.level
                            } else {
                                -1
                            }
                        }
                        is android.telephony.CellInfoCdma -> {
                            registeredCell.cellSignalStrength.level
                        }
                        else -> -1
                    }
                } else {
                    Logger.apnW("$TAG 无法获取CellInfo，返回默认信号强度")
                    -1
                }
            }
        } catch (e: Exception) {
            Logger.apnE("$TAG 获取信号强度失败", e)
            -1
        }
    }
    
    /**
     * 获取当前数据使用情况
     */
    private fun getCurrentDataUsage(): DataUsage {
        return try {
            val carrierDetector = CarrierDetector(context)
            carrierDetector.getCurrentDataUsage()
        } catch (e: Exception) {
            Logger.apnE("$TAG 获取数据使用情况失败", e)
            DataUsage(0, 0, 0, 0, System.currentTimeMillis(), System.currentTimeMillis())
        }
    }
    
    /**
     * 测量网络延迟
     */
    private suspend fun measureLatency(): Long {
        return withContext(Dispatchers.IO) {
            try {
                val startTime = System.currentTimeMillis()
                
                // Ping Google DNS
                val address = InetAddress.getByName("*******")
                val reachable = address.isReachable(PING_TIMEOUT)
                
                val endTime = System.currentTimeMillis()
                
                if (reachable) {
                    endTime - startTime
                } else {
                    -1L
                }
            } catch (e: Exception) {
                Logger.apnE("$TAG 测量网络延迟失败", e)
                -1L
            }
        }
    }
    
    /**
     * 测试网络速度
     */
    suspend fun testNetworkSpeed(): Pair<Long, Long> {
        return withContext(Dispatchers.IO) {
            try {
                Logger.apn("$TAG 开始网络速度测试")
                
                // 这里可以实现更复杂的速度测试逻辑
                // 例如下载/上传测试文件
                
                val downloadSpeed = measureDownloadSpeed()
                val uploadSpeed = measureUploadSpeed()
                
                Logger.apn("$TAG 网络速度测试完成: 下载=${ApnUtils.formatSpeed(downloadSpeed)}, 上传=${
                    ApnUtils.formatSpeed(
                        uploadSpeed
                    )
                }")
                
                Pair(downloadSpeed, uploadSpeed)
            } catch (e: Exception) {
                Logger.apnE("$TAG 网络速度测试失败", e)
                Pair(-1L, -1L)
            }
        }
    }
    
    /**
     * 测量下载速度
     */
    private suspend fun measureDownloadSpeed(): Long {
        return try {
            // 简化的下载速度测试
            // 实际实现中可以下载测试文件
            delay(1000) // 模拟测试时间
            1024 * 1024 // 返回模拟的1Mbps
        } catch (e: Exception) {
            Logger.apnE("$TAG 测量下载速度失败", e)
            -1L
        }
    }
    
    /**
     * 测量上传速度
     */
    private suspend fun measureUploadSpeed(): Long {
        return try {
            // 简化的上传速度测试
            // 实际实现中可以上传测试数据
            delay(1000) // 模拟测试时间
            512 * 1024 // 返回模拟的512Kbps
        } catch (e: Exception) {
            Logger.apnE("$TAG 测量上传速度失败", e)
            -1L
        }
    }
    
    /**
     * 注册网络状态回调
     */
    private fun registerNetworkCallback() {
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                connectivityManager.registerDefaultNetworkCallback(networkCallback)
            } else {
                val request = NetworkRequest.Builder()
                    .addCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET)
                    .build()
                connectivityManager.registerNetworkCallback(request, networkCallback)
            }
            Logger.apn("$TAG 网络状态回调注册成功")
        } catch (e: Exception) {
            Logger.apnE("$TAG 注册网络状态回调失败", e)
        }
    }
    
    /**
     * 取消网络状态回调
     */
    private fun unregisterNetworkCallback() {
        try {
            connectivityManager.unregisterNetworkCallback(networkCallback)
            Logger.apn("$TAG 网络状态回调取消成功")
        } catch (e: Exception) {
            Logger.apnE("$TAG 取消网络状态回调失败", e)
        }
    }
    
    /**
     * 注册信号强度监听器
     */
    @SuppressLint("MissingPermission")
    private fun registerSignalStrengthListener() {
        try {
            // 确保在主线程中创建PhoneStateListener
            if (Looper.myLooper() == Looper.getMainLooper()) {
                // 当前在主线程，直接创建
                if (signalStrengthListener == null) {
                    signalStrengthListener = createSignalStrengthListener()
                }
                telephonyManager.listen(signalStrengthListener,
                    PhoneStateListener.LISTEN_SIGNAL_STRENGTHS or
                    PhoneStateListener.LISTEN_DATA_CONNECTION_STATE or
                    PhoneStateListener.LISTEN_SERVICE_STATE
                )
                Logger.apn("$TAG 信号强度监听器注册成功")
            } else {
                // 不在主线程，切换到主线程执行
                mainHandler.post {
                    registerSignalStrengthListener()
                }
            }
        } catch (e: Exception) {
            Logger.apnE("$TAG 注册信号强度监听器失败", e)
        }
    }
    
    /**
     * 取消信号强度监听器
     */
    private fun unregisterSignalStrengthListener() {
        try {
            signalStrengthListener?.let { listener ->
                if (Looper.myLooper() == Looper.getMainLooper()) {
                    // 当前在主线程，直接取消
                    telephonyManager.listen(listener, PhoneStateListener.LISTEN_NONE)
                    signalStrengthListener = null
                    Logger.apn("$TAG 信号强度监听器取消成功")
                } else {
                    // 不在主线程，切换到主线程执行
                    mainHandler.post {
                        unregisterSignalStrengthListener()
                    }
                }
            }
        } catch (e: Exception) {
            Logger.apnE("$TAG 取消信号强度监听器失败", e)
        }
    }
    
    /**
     * 启动监控任务
     */
    private fun startMonitoringTask() {
        monitoringJob = GlobalScope.launch {
            try {
                while (isMonitoring.get()) {
                    updateNetworkStatus()
                    delay(MONITOR_INTERVAL)
                }
            } catch (e: CancellationException) {
                Logger.apn("$TAG 监控任务已取消")
            } catch (e: Exception) {
                Logger.apnE("$TAG 监控任务异常", e)
            }
        }
    }
    
    /**
     * 更新网络状态
     */
    private fun updateNetworkStatus() {
        try {
            val status = detectNetworkStatus()
            currentNetworkStatus.set(status)
            
            // 通知状态变化
            statusCallback?.invoke(status)
            
            Logger.apn("$TAG 网络状态更新: 连接=${status.isConnected}, 类型=${status.networkType.description}, 信号=${status.signalStrength}")
        } catch (e: Exception) {
            Logger.apnE("$TAG 更新网络状态失败", e)
        }
    }
    
    /**
     * 检查网络监控器是否可用
     */
    fun isAvailable(): Boolean {
        return try {
            connectivityManager != null && telephonyManager != null
        } catch (e: Exception) {
            Logger.apnE("$TAG 网络监控器不可用", e)
            false
        }
    }
    
    /**
     * 检查是否正在监控
     */
    fun isMonitoring(): Boolean = isMonitoring.get()
    
    /**
     * 获取网络详细信息
     */
    @SuppressLint("MissingPermission")
    fun getNetworkDetails(): Map<String, Any> {
        return try {
            val details = mutableMapOf<String, Any>()
            
            // 基本网络信息
            val activeNetwork = connectivityManager.activeNetwork
            val networkCapabilities = activeNetwork?.let { connectivityManager.getNetworkCapabilities(it) }
            
            details["hasActiveNetwork"] = activeNetwork != null
            details["hasInternet"] = networkCapabilities?.hasCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET) ?: false
            details["hasValidated"] = networkCapabilities?.hasCapability(NetworkCapabilities.NET_CAPABILITY_VALIDATED) ?: false
            
            // 移动网络信息
            details["networkOperator"] = telephonyManager.networkOperator ?: ""
            details["networkOperatorName"] = telephonyManager.networkOperatorName ?: ""
            details["networkType"] = telephonyManager.networkType
            details["dataState"] = telephonyManager.dataState
            details["isRoaming"] = telephonyManager.isNetworkRoaming
            
            // SIM卡信息
            details["simOperator"] = telephonyManager.simOperator ?: ""
            details["simOperatorName"] = telephonyManager.simOperatorName ?: ""
            details["simState"] = telephonyManager.simState
            details["simCountryIso"] = telephonyManager.simCountryIso ?: ""
            
            details
        } catch (e: Exception) {
            Logger.apnE("$TAG 获取网络详细信息失败", e)
            emptyMap()
        }
    }


    /**
     * 检查是否正在使用移动数据网络
     */
    fun isMobileDataActive(): Boolean {
        return try {
            val activeNetwork = connectivityManager.activeNetwork
            val networkCapabilities = activeNetwork?.let { connectivityManager.getNetworkCapabilities(it) }

            networkCapabilities?.hasTransport(NetworkCapabilities.TRANSPORT_CELLULAR) ?: false
        } catch (e: Exception) {
            Logger.apnE("$TAG 检查移动数据状态失败", e)
            false
        }
    }

    /**
     * 测试网络连通性
     */
    suspend fun testConnectivity(host: String = "*******", timeout: Int = 5000): Boolean {
        return withContext(Dispatchers.IO) {
            try {
                Logger.apn("$TAG 测试网络连通性: $host, 超时: ${timeout}ms")

                val address = InetAddress.getByName(host)
                val reachable = address.isReachable(timeout)

                Logger.apn("$TAG 网络连通性测试结果: $reachable")
                reachable

            } catch (e: Exception) {
                Logger.apnE("$TAG 网络连通性测试失败", e)
                false
            }
        }
    }
}
