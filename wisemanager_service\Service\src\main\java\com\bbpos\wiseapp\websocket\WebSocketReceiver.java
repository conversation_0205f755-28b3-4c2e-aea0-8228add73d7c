package com.bbpos.wiseapp.websocket;

import android.app.AlarmManager;
import android.app.Dialog;
import android.app.PendingIntent;
import android.content.BroadcastReceiver;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import android.net.ConnectivityManager;
import android.os.Build;
import android.os.Handler;
import android.os.Looper;
import android.preference.PreferenceManager;
import android.text.TextUtils;
import android.view.View;
import android.view.WindowManager;
import android.widget.ImageView;
import android.widget.TextView;
import android.widget.Toast;

import com.bbpos.wiseapp.logger.BBLog;
import com.bbpos.wiseapp.network.wificonfig.WiFiProfileManager;
import com.bbpos.wiseapp.service.R;
import com.bbpos.wiseapp.service.common.RequestPermission;
import com.bbpos.wiseapp.service.common.UsualData;
import com.bbpos.wiseapp.settings.utils.WirelessUtil;
import com.bbpos.wiseapp.system.api.DeviceInfoApi;
import com.bbpos.wiseapp.tms.adapter.SystemManagerAdapter;
import com.bbpos.wiseapp.tms.location.GpsLocationManager;
import com.bbpos.wiseapp.tms.service.WSTaskApkUninstallService;
import com.bbpos.wiseapp.tms.service.WSTaskApkUpdateService;
import com.bbpos.wiseapp.tms.service.WSTaskOsUpdateService;
import com.bbpos.wiseapp.tms.service.WSTaskResetUnboxService;
import com.bbpos.wiseapp.tms.service.WSTaskUpdateParamService;
import com.bbpos.wiseapp.tms.utils.BroadcastActions;
import com.bbpos.wiseapp.tms.utils.Constants;
import com.bbpos.wiseapp.tms.utils.ContextUtil;
import com.bbpos.wiseapp.tms.utils.Helpers;
import com.bbpos.wiseapp.tms.utils.ParameterName;
import com.bbpos.wiseapp.tms.utils.RulebasedAppListManager;
import com.bbpos.wiseapp.tms.utils.SPKeys;
import com.bbpos.wiseapp.tms.utils.TaskState;
import com.bbpos.wiseapp.tms.utils.TaskType;
import com.bbpos.wiseapp.tms.utils.WebSocketTaskListManager;
import com.bbpos.wiseapp.tms.widget.MAppPlusDialog;
import com.bbpos.wiseapp.tms.widget.MRulebasedDialog;
import com.bbpos.wiseapp.utils.ActivityUtils;
import com.bbpos.wiseapp.utils.FileUtils;
import com.bbpos.wiseapp.utils.SharedPreferencesUtils;
import com.bbpos.wiseapp.websocket.handler.CommandHandler;

import org.json.JSONObject;

import java.io.File;
import java.util.Iterator;
import java.util.Set;
import java.util.Timer;
import java.util.TimerTask;

public class WebSocketReceiver extends BroadcastReceiver {
    private static final String APPPLUS_TAG = "AppPlusWSTask";
    private Handler mHandler = new Handler(Looper.getMainLooper());
    private Timer mTimer = null;
    private static int timerCount = 0;
    private static Dialog lowBatdialog;
    public static String mStoreId = "";
    public static String mIPAddress = "";

    enum UpdateTaskTypeEnum {
        OTAUPDATE, //7MD WC update
        SPUPDATE,  //7MD SP update
        TMT,       //P1000 P500 TMT update
        KEYUPDATE, //P1000 P500 KEY update
        WISEPOSOTA //WisePos 4G OTA update
    }

    public static void setTimerCount(int count) {
        timerCount = count;
    }

    @Override
    public void onReceive(final Context context, final Intent intent) {
        BBLog.e(BBLog.TAG, "WebSocketReceiver " + intent.getAction());
        if (UsualData.WSTASK_UPDATED_BC.equals(intent.getAction())) {
            //为使同时间的任务串行执行，发出处理广播
            WebSocketTaskListManager.updateTaskExeTime();
            Set<Long> taskExeTimeSet = WebSocketTaskListManager.wstaskExeTimeMap.keySet();
            Iterator<Long> taskExeTimeIterator = taskExeTimeSet.iterator();
            while(taskExeTimeIterator.hasNext()) {
                Long taskExeTime = taskExeTimeIterator.next();
                sendWSTaskTimeupBCOnMillis(context, taskExeTime);
            }
        } else if (UsualData.RULEBASED_EXEC_BC.equals(intent.getAction())) {
            try {
                BBLog.e(Constants.TAG, "RulebasedHandler onReceive RULEBASED_EXEC_BC");

                final String response = intent.getStringExtra("response");
                if (Constants.IS_BATTERY_CHARGING==false && Constants.IS_BATTERY_LOW==true) {
                    mHandler.post(new Runnable() {
                        @Override
                        public void run() {
                            final Dialog dialog = new Dialog(context, R.style.dialog_style_ex);
                            dialog.setContentView(R.layout.dialog_confirm);
                            ImageView imageView = (ImageView) dialog.findViewById(R.id.iv_image);
                            imageView.setBackground(context.getDrawable(R.drawable.low_bat));
                            TextView tv_title = (TextView) dialog.findViewById(R.id.tv_title);
                            tv_title.setText(context.getString(R.string.low_battery));
                            TextView tv_content = (TextView) dialog.findViewById(R.id.tv_content);
                            tv_content.setText(context.getString(R.string.install_apk_tip_dialog_message_low_power));
                            dialog.setCanceledOnTouchOutside(false);
                            TextView tv_cancel = (TextView) dialog.findViewById(R.id.tv_cancel);
                            tv_cancel.setVisibility(View.GONE);
                            TextView tv_install = (TextView) dialog.findViewById(R.id.tv_install);
                            tv_install.setText(R.string.confirm);
                            tv_install.setOnClickListener(new View.OnClickListener() {
                                @Override
                                public void onClick(View v) {
                                    dialog.dismiss();
                                    if (Constants.IS_BATTERY_CHARGING==true || Constants.IS_BATTERY_LOW==false) {
                                        //具体传输数据
                                        Intent serviceIntent = new Intent(ContextUtil.getInstance().getApplicationContext(), RulebasedService.class);
                                        serviceIntent.putExtra("response", response);
                                        ContextUtil.getInstance().getApplicationContext().startService(serviceIntent);
                                    }
                                }
                            });
                            dialog.getWindow().setType((WindowManager.LayoutParams.TYPE_SYSTEM_ALERT));
                            dialog.show();
                        }
                    });
                } else {
                    //具体传输数据
                    Intent serviceIntent = new Intent(ContextUtil.getInstance().getApplicationContext(), RulebasedService.class);
                    serviceIntent.putExtra("response", intent.getStringExtra("response"));
                    ContextUtil.getInstance().getApplicationContext().startService(serviceIntent);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        } else if (UsualData.WSTASK_EXEC_BC.equals(intent.getAction())) {
            try {
                //接收到任务时间到广播后的处理
                if (Helpers.isOnline(context)) {
                    final JSONObject todoWSTaskJsonObj = WebSocketTaskListManager.getNextTodoWSTask();
                    BBLog.i(APPPLUS_TAG, "NEXT WSTask：" + todoWSTaskJsonObj);
                    if (todoWSTaskJsonObj == null) {
                        BBLog.i(Constants.TAG, "no WSTask need todo.");
                    } else {
                        String wstaskType = null;
                        wstaskType = todoWSTaskJsonObj.getString(ParameterName.taskType);
                        //如果接下来的第一个任务是安装或者os升级则弹倒计时提示
                        BBLog.i(APPPLUS_TAG, "taskType=" + wstaskType);
                        //需要弹窗的任务
                        if ((TaskType.UPDATE_APK.equals(wstaskType) || TaskType.UNINSTALL_APK.equals(wstaskType) || TaskType.OSUPDATE.equals(wstaskType) || TaskType.UPDATE_PARAM.equals(wstaskType))) {
                            //空闲时候弹窗
                            if (TaskType.OSUPDATE.equals(wstaskType)) {
                                //如果升级目标版本和本地版本一样，则应答升级成功
                                SharedPreferences sp = PreferenceManager.getDefaultSharedPreferences(ContextUtil.getInstance());
                                String installOTATaskID = sp.getString(SPKeys.OTA_INSTALL_TASK_ID, "");
                                BBLog.i(APPPLUS_TAG, "installOTATaskID" + installOTATaskID);
                                if (DeviceInfoApi.getIntance().getCustomVersion().equals(todoWSTaskJsonObj.getString(ParameterName.displayVer))) {
//                                    WebSocketTaskListManager.updateWSTaskState(todoWSTaskJsonObj.getString(ParameterName.taskId), TaskState.UPDATE_SUCCESS);
                                    Helpers.updateWSTaskStateAndUpload(context, todoWSTaskJsonObj.getString(ParameterName.taskId), TaskState.UPDATE_SUCCESS, null);
                                    Thread.sleep(1000);
                                    BBLog.w(APPPLUS_TAG, "OSUPDATE Target version is same as local! send WSTASK_EXEC_BC");
                                    Helpers.sendBroad(context, UsualData.WSTASK_EXEC_BC);
                                    return;
                                }

                                if (Constants.IS_BATTERY_LOW_FOR_OSUPDATE) {
                                    Helpers.updateWSTaskStateAndUpload(context, todoWSTaskJsonObj.getString(ParameterName.taskId), TaskState.LOW_BAT, null);
                                    mHandler.post(new Runnable() {
                                        @Override
                                        public void run() {
                                            final Dialog dialog = new Dialog(context, R.style.dialog_style_ex);
                                            dialog.setContentView(R.layout.dialog_confirm);
                                            ImageView imageView = (ImageView) dialog.findViewById(R.id.iv_image);
                                            imageView.setBackground(context.getDrawable(R.drawable.low_bat));
                                            TextView tv_title = (TextView) dialog.findViewById(R.id.tv_title);
                                            tv_title.setText(context.getString(R.string.low_battery));
                                            TextView tv_content = (TextView) dialog.findViewById(R.id.tv_content);
                                            tv_content.setText(context.getString(R.string.install_apk_tip_dialog_message_low_power));
                                            dialog.setCanceledOnTouchOutside(false);
                                            TextView tv_cancel = (TextView) dialog.findViewById(R.id.tv_cancel);
                                            tv_cancel.setVisibility(View.GONE);
                                            TextView tv_install = (TextView) dialog.findViewById(R.id.tv_install);
                                            tv_install.setText(R.string.confirm);
                                            tv_install.setOnClickListener(
                                                new View.OnClickListener() {
                                                    @Override
                                                    public void onClick(View v) {
                                                        dialog.dismiss();
                                                        if (!Constants.IS_BATTERY_LOW_FOR_OSUPDATE) {
                                                        //记住文件的MD5
                                                        Intent serviceIntent = new Intent(context, WSTaskOsUpdateService.class);
                                                        //具体传输数据
                                                        try {
                                                            serviceIntent.putExtra(ParameterName.taskId, todoWSTaskJsonObj.getString(ParameterName.taskId));
                                                            serviceIntent.putExtra(ParameterName.url, todoWSTaskJsonObj.getString(ParameterName.url));
                                                            serviceIntent.putExtra(ParameterName.fileName, todoWSTaskJsonObj.getString(ParameterName.fileName));
                                                            serviceIntent.putExtra(ParameterName.fileSizeEx, todoWSTaskJsonObj.getString(ParameterName.fileSizeEx));
                                                            serviceIntent.putExtra(ParameterName.fileMd5Ex, todoWSTaskJsonObj.getString(ParameterName.fileMd5Ex));
                                                            serviceIntent.putExtra(ParameterName.displayVer, todoWSTaskJsonObj.getString(ParameterName.displayVer));
                                                            serviceIntent.putExtra(ParameterName.fileKey, todoWSTaskJsonObj.optString(ParameterName.fileKey));
                                                            context.startService(serviceIntent);
                                                        } catch (Exception e) {
                                                            e.printStackTrace();
                                                        }
                                                    }
                                                }
                                            });
                                            dialog.getWindow().setType((WindowManager.LayoutParams.TYPE_SYSTEM_ALERT));
                                            dialog.show();
                                        }
                                    });
                                } else {
                                    //记住文件的MD5
                                    Intent serviceIntent = new Intent(context, WSTaskOsUpdateService.class);
                                    //具体传输数据
                                    serviceIntent.putExtra(ParameterName.taskId, todoWSTaskJsonObj.getString(ParameterName.taskId));
                                    serviceIntent.putExtra(ParameterName.url, todoWSTaskJsonObj.getString(ParameterName.url));
                                    serviceIntent.putExtra(ParameterName.fileName, todoWSTaskJsonObj.getString(ParameterName.fileName));
                                    serviceIntent.putExtra(ParameterName.fileSizeEx, todoWSTaskJsonObj.getString(ParameterName.fileSizeEx));
                                    serviceIntent.putExtra(ParameterName.fileMd5Ex, todoWSTaskJsonObj.getString(ParameterName.fileMd5Ex));
                                    serviceIntent.putExtra(ParameterName.displayVer, todoWSTaskJsonObj.getString(ParameterName.displayVer));
                                    serviceIntent.putExtra(ParameterName.fileKey, todoWSTaskJsonObj.optString(ParameterName.fileKey));
                                    context.startService(serviceIntent);
                                }
                            } else if (TaskType.UPDATE_APK.equals(wstaskType)) {
                                int ret = RulebasedAppListManager.isApkInstalled(todoWSTaskJsonObj);
                                if (todoWSTaskJsonObj.has(ParameterName.pkgName) && (UsualData.SERVICE_PACKAGE_NAME.equals(todoWSTaskJsonObj.getString(ParameterName.pkgName))
                                || UsualData.LAUNCHER_PACKAGE_NAME.equals(todoWSTaskJsonObj.getString(ParameterName.pkgName)))) {
                                    if (ret==1 || ret==2) {
                                        BBLog.i(APPPLUS_TAG, "Local " + todoWSTaskJsonObj.getString(ParameterName.apkName) + "is Latest");
                                        Helpers.updateWSTaskStateAndUpload(context, todoWSTaskJsonObj.getString(ParameterName.taskId), TaskState.INSTALL_SUCCESS, null);
                                        Thread.sleep(1000);
                                        BBLog.w(APPPLUS_TAG, "UPDATE_APK Local WiseApp is latest, send WSTASK_EXEC_BC");
                                        Helpers.sendBroad(context, UsualData.WSTASK_EXEC_BC);
                                        return;
                                    }
                                } else if (ret == 1) {
                                    BBLog.i(APPPLUS_TAG, "WebSocket the pushed APK already exists");
                                    //判断是否是特殊的APK （ota_update、sp_update、tmt）
									if ( UsualData.SYSTEM_SP_OTA_TRIGGER_APP_PACKAGENAME.equals(todoWSTaskJsonObj.getString(ParameterName.packName))
											|| UsualData.SYSTEM_OTA_TRIGGER_APP_PACKAGENAME.equals(todoWSTaskJsonObj.getString(ParameterName.packName))) {
										Helpers.updateWSTaskStateAndUpload(context, todoWSTaskJsonObj.getString(ParameterName.taskId), TaskState.INSTALL_SUCCESS, null);
										BBLog.w(APPPLUS_TAG, "WebSocket app+ the pushed ["+todoWSTaskJsonObj.getString(ParameterName.packName)+"] already exists, Ignore the task");
										return;
									}
                                    if (
                                    	UsualData.SYSTEM_WISEPOS_OTA_TRIGGER_APP_PACKAGENAME.equals(todoWSTaskJsonObj.getString(ParameterName.packName))
//                                            || UsualData.SYSTEM_SP_OTA_TRIGGER_APP_PACKAGENAME.equals(todoWSTaskJsonObj.getString(ParameterName.packName))
//                                            || UsualData.SYSTEM_OTA_TRIGGER_APP_PACKAGENAME.equals(todoWSTaskJsonObj.getString(ParameterName.packName))
                                            || UsualData.SYSTEM_TMT_OTA_TRIGGER_APP_PACKAGENAME.equals(todoWSTaskJsonObj.getString(ParameterName.packName))
                                            || UsualData.SYSTEM_KEY_UPDATE_TRIGGER_APP_PACKAGENAME.equals(todoWSTaskJsonObj.getString(ParameterName.packName))) {
										Helpers.updateWSTaskStateAndUpload(context, todoWSTaskJsonObj.getString(ParameterName.taskId), TaskState.INSTALL_SUCCESS, null);
                                        prepareToUpdate(context,todoWSTaskJsonObj);
                                    }else {
										Helpers.updateWSTaskStateAndUpload(context, todoWSTaskJsonObj.getString(ParameterName.taskId), TaskState.INSTALL_SUCCESS, null);
										BBLog.w(APPPLUS_TAG, "UPDATE_APK WebSocket the pushed APK already exists, send WSTASK_EXEC_BC");
//										WebSocketSender.C0901_AppInfoUpload();
										Helpers.sendBroad(context, UsualData.WSTASK_EXEC_BC);
                                    }
                                    return;
                                }

                                if (Constants.IS_BATTERY_CHARGING==false && Constants.IS_BATTERY_LOW==true) {
                                    Helpers.updateWSTaskStateAndUpload(context, todoWSTaskJsonObj.getString(ParameterName.taskId), TaskState.LOW_BAT, null);
                                    if (lowBatdialog != null) {
                                        lowBatdialog.dismiss();
                                        lowBatdialog = null;
                                    }
                                    lowBatdialog = new Dialog(context, R.style.dialog_style_ex);
                                    lowBatdialog.setContentView(R.layout.dialog_confirm);
                                    ImageView imageView = (ImageView) lowBatdialog.findViewById(R.id.iv_image);
                                    imageView.setBackground(context.getDrawable(R.drawable.low_bat));
                                    TextView tv_title = (TextView) lowBatdialog.findViewById(R.id.tv_title);
                                    tv_title.setText(context.getString(R.string.low_battery));
                                    TextView tv_content = (TextView) lowBatdialog.findViewById(R.id.tv_content);
                                    tv_content.setText(context.getString(R.string.install_apk_tip_dialog_message_low_power));
                                    lowBatdialog.setCanceledOnTouchOutside(false);
                                    TextView tv_cancel = (TextView) lowBatdialog.findViewById(R.id.tv_cancel);
                                    tv_cancel.setVisibility(View.GONE);
                                    TextView tv_install = (TextView) lowBatdialog.findViewById(R.id.tv_install);
                                    tv_install.setText(R.string.confirm);
                                    tv_install.setOnClickListener(
                                        new View.OnClickListener() {
                                            @Override
                                            public void onClick(View v) {
                                                lowBatdialog.dismiss();
                                                if (Constants.IS_BATTERY_CHARGING==true || Constants.IS_BATTERY_LOW==false) {
                                                    Intent serviceIntent = new Intent(context, WSTaskApkUpdateService.class);
                                                    //具体传输数据
                                                    try {
                                                        serviceIntent.putExtra(ParameterName.taskJson, todoWSTaskJsonObj.toString());
                                                        serviceIntent.putExtra(ParameterName.taskId, todoWSTaskJsonObj.getString(ParameterName.taskId));
                                                        serviceIntent.putExtra(ParameterName.packName, todoWSTaskJsonObj.getString(ParameterName.packName));
                                                        serviceIntent.putExtra(ParameterName.versionName, todoWSTaskJsonObj.getString(ParameterName.versionName));
                                                        serviceIntent.putExtra(ParameterName.versionCode, todoWSTaskJsonObj.getString(ParameterName.versionCode));
                                                        serviceIntent.putExtra(ParameterName.apkName, todoWSTaskJsonObj.getString(ParameterName.apkName));
                                                        serviceIntent.putExtra(ParameterName.url, todoWSTaskJsonObj.getString(ParameterName.url));
                                                        serviceIntent.putExtra(ParameterName.appId, todoWSTaskJsonObj.getString(ParameterName.appId));
                                                        serviceIntent.putExtra(ParameterName.apkSize, todoWSTaskJsonObj.getString(ParameterName.apkSize));
                                                        serviceIntent.putExtra(ParameterName.apkMd5, todoWSTaskJsonObj.getString(ParameterName.apkMd5));
                                                        serviceIntent.putExtra(ParameterName.appIconUrlEx, todoWSTaskJsonObj.getString(ParameterName.appIconUrlEx));
                                                        serviceIntent.putExtra(ParameterName.fileKey, todoWSTaskJsonObj.optString(ParameterName.fileKey));
                                                        serviceIntent.putExtra(ParameterName.allowDowngrade, !todoWSTaskJsonObj.has(ParameterName.allowDowngrade) || todoWSTaskJsonObj.getBoolean(ParameterName.allowDowngrade));
                                                        serviceIntent.putExtra(ParameterName.forceInstall, todoWSTaskJsonObj.has(ParameterName.forceInstall) && todoWSTaskJsonObj.getBoolean(ParameterName.forceInstall));
                                                        context.startService(serviceIntent);
                                                        WebSocketTaskListManager.updateWSTaskState(todoWSTaskJsonObj.getString(ParameterName.taskId), TaskState.WATING);
                                                    } catch (Exception e) {
                                                        e.printStackTrace();
                                                    }
                                                }
                                            }
                                        });
                                    lowBatdialog.getWindow().setType((WindowManager.LayoutParams.TYPE_SYSTEM_ALERT));
                                    lowBatdialog.show();
                                } else {
                                    Intent serviceIntent = new Intent(context, WSTaskApkUpdateService.class);
                                    //具体传输数据
                                    serviceIntent.putExtra(ParameterName.taskJson, todoWSTaskJsonObj.toString());
                                    serviceIntent.putExtra(ParameterName.taskId, todoWSTaskJsonObj.getString(ParameterName.taskId));
                                    serviceIntent.putExtra(ParameterName.packName, todoWSTaskJsonObj.getString(ParameterName.packName));
                                    serviceIntent.putExtra(ParameterName.versionName, todoWSTaskJsonObj.getString(ParameterName.versionName));
                                    serviceIntent.putExtra(ParameterName.versionCode, todoWSTaskJsonObj.getString(ParameterName.versionCode));
                                    serviceIntent.putExtra(ParameterName.apkName, todoWSTaskJsonObj.getString(ParameterName.apkName));
                                    serviceIntent.putExtra(ParameterName.url, todoWSTaskJsonObj.getString(ParameterName.url));
                                    serviceIntent.putExtra(ParameterName.appId, todoWSTaskJsonObj.getString(ParameterName.appId));
                                    serviceIntent.putExtra(ParameterName.apkSize, todoWSTaskJsonObj.getString(ParameterName.apkSize));
                                    serviceIntent.putExtra(ParameterName.apkMd5, todoWSTaskJsonObj.getString(ParameterName.apkMd5));
                                    serviceIntent.putExtra(ParameterName.appIconUrlEx, todoWSTaskJsonObj.getString(ParameterName.appIconUrlEx));
                                    serviceIntent.putExtra(ParameterName.fileKey, todoWSTaskJsonObj.optString(ParameterName.fileKey));
                                    serviceIntent.putExtra(ParameterName.allowDowngrade, !todoWSTaskJsonObj.has(ParameterName.allowDowngrade) || todoWSTaskJsonObj.getBoolean(ParameterName.allowDowngrade));
                                    serviceIntent.putExtra(ParameterName.forceInstall, todoWSTaskJsonObj.has(ParameterName.forceInstall) && todoWSTaskJsonObj.getBoolean(ParameterName.forceInstall));
                                    context.startService(serviceIntent);
                                    WebSocketTaskListManager.updateWSTaskState(todoWSTaskJsonObj.getString(ParameterName.taskId), TaskState.WATING);
                                }
                            } else if (TaskType.UNINSTALL_APK.equals(wstaskType)) {
                                Intent serviceIntent = new Intent(context, WSTaskApkUninstallService.class);
                                serviceIntent.putExtra(ParameterName.taskId, todoWSTaskJsonObj.getString(ParameterName.taskId));
                                serviceIntent.putExtra(ParameterName.packName, todoWSTaskJsonObj.getString(ParameterName.packName));
                                serviceIntent.putExtra(ParameterName.apkName, todoWSTaskJsonObj.getString(ParameterName.apkName));
                                context.startService(serviceIntent);
                            } else if (TaskType.UPDATE_PARAM.equals(wstaskType)) {
                                Intent serviceIntent = new Intent(context, WSTaskUpdateParamService.class);
                                serviceIntent.putExtra(ParameterName.taskId, todoWSTaskJsonObj.getString(ParameterName.taskId));
                                serviceIntent.putExtra(ParameterName.packName, todoWSTaskJsonObj.getString(ParameterName.packName));
                                serviceIntent.putExtra(ParameterName.apkName, todoWSTaskJsonObj.getString(ParameterName.apkName));
                                serviceIntent.putExtra(ParameterName.templateId, todoWSTaskJsonObj.getString(ParameterName.templateId));
                                serviceIntent.putExtra(ParameterName.templateName, todoWSTaskJsonObj.getString(ParameterName.templateName));
                                serviceIntent.putExtra(ParameterName.templateVersion, todoWSTaskJsonObj.getString(ParameterName.templateVersion));
                                serviceIntent.putExtra(ParameterName.url, todoWSTaskJsonObj.getString(ParameterName.url));
                                serviceIntent.putExtra(ParameterName.fileName, todoWSTaskJsonObj.getString(ParameterName.fileName));
                                serviceIntent.putExtra(ParameterName.fileSizeEx, todoWSTaskJsonObj.getString(ParameterName.fileSizeEx));
                                serviceIntent.putExtra(ParameterName.fileMd5Ex, todoWSTaskJsonObj.getString(ParameterName.fileMd5Ex));
                                context.startService(serviceIntent);
                            }
                        }
                    }
                } else {
                    BBLog.i(APPPLUS_TAG, "network is not online.");
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        } else if (BroadcastActions.WIFI_FOUND.equals(intent.getAction())) {
            if (!WebSocketSender.bRequestCR001StoreId) {
                Constants.B_UNBOX_RUNNING = true;
                if (Constants.M_GEOFENCE_SET_SERVICE_LAUNCHER) {
                    GpsLocationManager.shutDownGeoFenceDetect();
                } else {
                    if (Constants.M_GEOFENCE_STATUS < GpsLocationManager.LOCK_SCREEN || Constants.M_GEOFENCE_STATUS == GpsLocationManager.ROAMING) {
                        Constants.B_UNBOX_RUNNING_LOCK = true;
                        GpsLocationManager.shutDownGeoFenceDetect();
                    }
                }
                Constants.STORE_ID = "";
                WebSocketSender.bRequestCR001StoreId = true;;
                WebSocketSender.bRequestCR001PairedStoreId = true; //需要请求StoreId
                WebSocketSender.bRequestCR002StoreIdConfirm = false;;
                WebSocketSender.bRequestCR003AppDownload = false;
                WebSocketSender.bResponeSR003AppDownload = false;
                if (mTimer == null) {
                    mTimer = new Timer();
                    timerCount = 0;
                    mTimer.schedule(new TimerTask() {
                        @Override
                        public void run() {
                            timerCount++;
                            if (WebSocketSender.bRequestCR001StoreId) {
                                if (timerCount == 120 || !WebSocketSender.bRequestCR001StoreId) {    //5分钟定位超时时间
                                    if (WebSocketSender.bRequestCR001StoreId && timerCount == 120) {
                                        Intent intent = new Intent(BroadcastActions.STORE_NOT_FOUND);
                                        BBLog.w(InitialProcessService.TAG, "sendBroadcast STORE_NOT_FOUND  没找到Store ID");
                                        ContextUtil.getInstance().sendBroadcast(intent, RequestPermission.REQUEST_PERMISSION_INTERNET);
                                        WebSocketSender.waiting_storeId = false;
                                    }
                                    WebSocketSender.bRequestCR001StoreId = false;
//                                    BBLog.w(BBLog.TAG, "销毁发送定时器 waiting_storeId=" + WebSocketSender.waiting_storeId + "  bRequestCR001StoreId=" + WebSocketSender.bRequestCR001StoreId);
//                                    mTimer.cancel();
//                                    mTimer = null;
                                    return;
                                }
//                            if (timerCount == 2) {
//                                GpsLocationManager.setGpsSuccess();
//                            }
                                if (timerCount == 1 || timerCount % 30 == 0) {
                                    mHandler.post(new Runnable() {
                                        @Override
                                        public void run() {
                                            Toast.makeText(context, context.getString(R.string.location_prompt), Toast.LENGTH_LONG).show();
                                        }
                                    });
                                }
                                WebSocketSender.CR001_requestStoreId();
                            } else if (WebSocketSender.bRequestCR002StoreIdConfirm) {
                                if (timerCount % 30 == 0) {
                                    if (!TextUtils.isEmpty(Constants.STORE_ID)) {
                                        WebSocketSender.CR002_responeStoreId(WebSocketReceiver.mStoreId, WirelessUtil.getWifiIP(ContextUtil.getInstance()), "1");
                                    } else {
                                        WebSocketSender.CR002_responeStoreId(WebSocketReceiver.mStoreId, WirelessUtil.getWifiIP(ContextUtil.getInstance()), "0");
                                    }
                                }
                            } else if (WebSocketSender.bRequestCR003AppDownload) {
                                if (timerCount % 30 == 0) {
                                    WebSocketSender.CR003_requestPreInstallAppList();
                                }
                            } else if (WebSocketSender.bResponeSR003AppDownload) {
                                BBLog.w(InitialProcessService.TAG, "Cancel timer, waiting_storeId=" + WebSocketSender.waiting_storeId + "  bRequestCR001StoreId=" + WebSocketSender.bRequestCR001StoreId);
                                mTimer.cancel();
                                mTimer = null;
                            }
                        }
                    }, 0, 1000);
                }
            }
        } else if (BroadcastActions.LOGIN_SUCCESS.equals(intent.getAction())) {
            setLoginSuccessFlag();
            WebSocketSender.C0201_DeviceStatusUpload("3", Constants.IS_IN_USE);
            SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_UNBOX_STATUS, "3");
        } else if (BroadcastActions.LOGIN_FAILED.equals(intent.getAction())) {
            WebSocketSender.C0201_DeviceStatusUpload("4", Constants.IS_IN_USE);
            SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_UNBOX_STATUS, "4");
        } else if (BroadcastActions.INIT_READY.equals(intent.getAction())) {
            WebSocketSender.CR003_requestPreInstallAppList();
            WebSocketReceiver.setTimerCount(1);
            WebSocketSender.bRequestCR003AppDownload = true;
/*
            if (mTimer == null) {
                mTimer = new Timer();
                timerCount = 0;
                mTimer.schedule(new TimerTask() {
                    @Override
                    public void run() {
                        timerCount++;
                        if (timerCount == 60) {    //5分钟定位超时时间
                            Intent intent = new Intent(BroadcastActions.BEGIN_DOWNLOAD);
                            BBLog.w(BBLog.TAG, "sendBroadcast: BEGIN_DOWNLOAD");
                            intent.putExtra("app_count", "0");
                            context.sendBroadcast(intent, RequestPermission.REQUEST_PERMISSION_INTERNET);
                            BBLog.w(BBLog.TAG, "销毁发送定时器 waiting_storeId=" + WebSocketSender.waiting_storeId);
                            mTimer.cancel();
                            mTimer = null;
                            return;
                        }
                        WebSocketSender.CR001_requestStoreId();
                    }
                }, 0, 1000);
            }
*/
        } else if (BroadcastActions.STORE_SELECTED.equals(intent.getAction())) {
            WebSocketSender.bRequestCR001StoreId = false;    //用户已选择或者手输StoreId
            String storeId = intent.getStringExtra(ParameterName.storeId);
            mStoreId = storeId;
            mIPAddress = WirelessUtil.getWifiIP(ContextUtil.getInstance());
            BBLog.w(InitialProcessService.TAG, "STORE_SELECTED: " + storeId);
            WebSocketSender.CR002_responeStoreId(storeId, WirelessUtil.getWifiIP(ContextUtil.getInstance()), "0");
            WebSocketReceiver.setTimerCount(1);
            WebSocketSender.bRequestCR002StoreIdConfirm = true;
        } else if (BroadcastActions.RULEBASED_DOWNLOAD_COMPLETED.equals(intent.getAction())) {
            String ruleId = intent.getStringExtra(ParameterName.ruleId);
            long delayTime = 600000;
            if (intent.hasExtra(ParameterName.delayTime)) {
                try {
                    BBLog.e(InitialProcessService.TAG, "showRulebasedAppList delayTime = " + intent.getStringExtra(ParameterName.delayTime));
                    delayTime = Long.valueOf(intent.getStringExtra(ParameterName.delayTime)) * 60000;
                } catch (Exception e) {
                    e.printStackTrace();
                    delayTime = 600000;
                }
            }
            long finalDelayTime = delayTime;
            BBLog.e(InitialProcessService.TAG, "showRulebasedAppList finalDelayTime = " + finalDelayTime);
            MRulebasedDialog.showRulebasedAppList(ContextUtil.getInstance().getApplicationContext(), ruleId
                , new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        mHandler.postDelayed(new Runnable() {
                            @Override
                            public void run() {
                                //再次通知
                                if (RulebasedService.try_count > 0) {
                                    Intent it = new Intent(BroadcastActions.RULEBASED_DOWNLOAD_COMPLETED);
                                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                                        it.setComponent(new ComponentName(context.getPackageName(), WebSocketReceiver.class.getName()));
                                    }
                                    it.putExtra(ParameterName.ruleId, ruleId);
                                    it.putExtra(ParameterName.delayTime, finalDelayTime/60000+"");
                                    it.setFlags(Intent.FLAG_INCLUDE_STOPPED_PACKAGES);
                                    context.sendBroadcast(it, RequestPermission.REQUEST_PERMISSION_MY_BROADCAST);
                                    RulebasedService.try_count--;
                                } else {
                                    try {
                                        MRulebasedDialog.installRulebasedApk();
                                    } catch (InterruptedException e) {
                                        e.printStackTrace();
                                    }
                                }
                            }
                        }, finalDelayTime);
                    }
                }
                , new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        try {
                            MRulebasedDialog.installRulebasedApk();
                        } catch (InterruptedException e) {
                            e.printStackTrace();
                        }
                    }
                }
            );
        } else if (BroadcastActions.APP_PLUS_DOWNLOAD_COMPLETED.equals(intent.getAction())) {
            MAppPlusDialog.showAppPlusList(ContextUtil.getInstance().getApplicationContext()
                    , new View.OnClickListener() {
                        @Override
                        public void onClick(View v) {
                            mHandler.postDelayed(new Runnable() {
                                @Override
                                public void run() {
                                    //再次通知
                                    Helpers.sendBroad(context, BroadcastActions.APP_PLUS_DOWNLOAD_COMPLETED);
                                }
                            }, 1200000);
                        }
                    }
                    , new View.OnClickListener() {
                        @Override
                        public void onClick(View v) {
                            try {
                                MAppPlusDialog.installAppPlusApk(context);
                            } catch (Exception e) {
                                e.printStackTrace();
                            }
                        }
                    }
            );
        } else if (BroadcastActions.RESTART_DEVICE.equals(intent.getAction())) {
            SystemManagerAdapter.reboot(context);
        } else if (BroadcastActions.UNBIND_COMPLETE.equals(intent.getAction())) {
            //增加unbind时候的unlock
            if (Constants.M_GEOFENCE_STATUS>=GpsLocationManager.LOCK_SCREEN || Constants.M_GEOFENCE_STATUS>=GpsLocationManager.WIPE_DATA) {
                if (!Constants.B_UNBOX_RESET_FROM_GEO) {
                    GpsLocationManager.closeOutOfGeofenceLockScreen();
                }
            }

            Intent intent_tmp = new Intent(context, WSTaskResetUnboxService.class);
            intent_tmp.putExtra(ParameterName.request_id, CommandHandler.unboxreset_request_id);
            intent_tmp.putExtra(ParameterName.request_time, CommandHandler.unboxreset_request_time);
            context.startService(intent_tmp);
        } else if (BroadcastActions.DELETE_TEST_WIFI.equals(intent.getAction())) {
            String ssid = "test";
            String pwd = "testtest";
            WiFiProfileManager.getInstance(ContextUtil.getInstance()).delWifiBySSID(ssid);
        } else if (BroadcastActions.SET_DEFAULT_LAUNCHER.equals(intent.getAction())) {
            if (intent.hasExtra("package_name")) {
                String package_name = intent.getStringExtra("package_name");
                ContextUtil.setLauncherApp(ContextUtil.getInstance(), package_name);
            } else {
                ContextUtil.setLauncherApp(ContextUtil.getInstance(), UsualData.LOADER_711_PACKAGE_NAME);
            }
        } else if (ConnectivityManager.CONNECTIVITY_ACTION.equals(intent.getAction())) {
            if (Helpers.isOnline(context)) {
                BBLog.w(BBLog.TAG, "CONNECTIVITY_ACTION 網絡重連 發送 WSTASK_EXEC_BC");
                Helpers.sendBroad(context, UsualData.WSTASK_EXEC_BC);
            }
        }
    }

    private void prepareToUpdate(Context context,JSONObject todoWSTaskJsonObj) {
        if (todoWSTaskJsonObj == null) return;

        try {
            if (DeviceInfoApi.getIntance().isWisePosPro()) {
                if (todoWSTaskJsonObj.has(ParameterName.pkgName) && UsualData.SYSTEM_OTA_TRIGGER_APP_PACKAGENAME.equals(todoWSTaskJsonObj.getString(ParameterName.packName))) {//ota 升级
                    BBLog.d(BBLog.TAG, "prepareToUpdate [任務類型] ----> OTA update");
                    SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_NEED_WISEPOSPRO_OTA_FLAG, true);
                    if (Constants.IS_BATTERY_CHARGING == false && Constants.IS_BATTERY_LOW == true) {
                        showBatteryLowDialogWhileUpdate(context, UpdateTaskTypeEnum.OTAUPDATE, todoWSTaskJsonObj);
                    } else {
                        //保持任务的taskid。在升级完毕后，清除缓存数据并上报任务状态
                        SharedPreferencesUtils.setSharePreferencesValue(UsualData.SYSTEM_OTA_TRIGGER_TASKID, todoWSTaskJsonObj.getString(ParameterName.taskId));
                        ActivityUtils.startOTAService(todoWSTaskJsonObj,Constants.TYPE_WISECUBE_FW_UPGRADE);
                    }
                } else if (todoWSTaskJsonObj.has(ParameterName.pkgName) && UsualData.SYSTEM_SP_OTA_TRIGGER_APP_PACKAGENAME.equals(todoWSTaskJsonObj.getString(ParameterName.packName))) {//sp 升级
                    BBLog.d(BBLog.TAG, "prepareToUpdate [任務類型] ----> sp update");
					SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_NEED_WISEPOSPRO_OTA_FLAG, true);
                    if (Constants.IS_BATTERY_CHARGING == false && Constants.IS_BATTERY_LOW == true) {
                        showBatteryLowDialogWhileUpdate(context, UpdateTaskTypeEnum.SPUPDATE, todoWSTaskJsonObj);
                    } else {
                        //保持任务的taskid。在升级完毕后，清除缓存数据并上报任务状态
                        SharedPreferencesUtils.setSharePreferencesValue(UsualData.SYSTEM_SP_OTA_TRIGGER_TASKID, todoWSTaskJsonObj.getString(ParameterName.taskId));
                        ActivityUtils.startOTAService(todoWSTaskJsonObj,Constants.TYPE_SP_FW_UPGRADE);
                    }
                }
            } else if (DeviceInfoApi.getIntance().isWisePos5Plus() || DeviceInfoApi.getIntance().isWisePos5() || DeviceInfoApi.getIntance().isWisePosTouch() || DeviceInfoApi.getIntance().isWisePosTouchPlus()) {//P1000、P500
                if (todoWSTaskJsonObj.has(ParameterName.pkgName) && UsualData.SYSTEM_TMT_OTA_TRIGGER_APP_PACKAGENAME.equals(todoWSTaskJsonObj.getString(ParameterName.packName))) {//tmt 升级
                    BBLog.d(BBLog.TAG, "prepareToUpdate [任務類型] ----> P1000/P500 update");
                    SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_NEED_WISEPOS5_OTA_FLAG, true);
                    if (Constants.IS_BATTERY_CHARGING == false && Constants.IS_BATTERY_LOW == true) {
                        showBatteryLowDialogWhileUpdate(context, UpdateTaskTypeEnum.TMT, todoWSTaskJsonObj);
                    } else {
                        //保持任务的taskid。在升级完毕后，清除缓存数据并上报任务状态
                        SharedPreferencesUtils.setSharePreferencesValue(UsualData.SYSTEM_TMT_TRIGGER_TASKID, todoWSTaskJsonObj.getString(ParameterName.taskId));
                        ActivityUtils.startOTAService(todoWSTaskJsonObj,Constants.TYPE_TMT_UPGRADE);
                    }
                }else if (todoWSTaskJsonObj.has(ParameterName.pkgName) && UsualData.SYSTEM_KEY_UPDATE_TRIGGER_APP_PACKAGENAME.equals(todoWSTaskJsonObj.getString(ParameterName.packName))) {//key 升级
                    BBLog.d(BBLog.TAG, "prepareToUpdate [任務類型] ----> P1000/P500 update");
                    SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_NEED_WISEPOS5_OTA_FLAG, true);
                    if (Constants.IS_BATTERY_CHARGING == false && Constants.IS_BATTERY_LOW == true) {
                        showBatteryLowDialogWhileUpdate(context, UpdateTaskTypeEnum.KEYUPDATE, todoWSTaskJsonObj);
                    } else {
                        //保持任务的taskid。在升级完毕后，清除缓存数据并上报任务状态
                        SharedPreferencesUtils.setSharePreferencesValue(UsualData.SYSTEM_KEY_UPDATE_TRIGGER_TASKID, todoWSTaskJsonObj.getString(ParameterName.taskId));
                        ActivityUtils.startOTAService(todoWSTaskJsonObj,Constants.TYPE_KEY_UPGRADE);
                    }
                }
            }else if (DeviceInfoApi.getIntance().isWisePos4G()) {//Wisepos4G
                if (todoWSTaskJsonObj.has(ParameterName.pkgName) && UsualData.SYSTEM_WISEPOS_OTA_TRIGGER_APP_PACKAGENAME.equals(todoWSTaskJsonObj.getString(ParameterName.packName))) {//wisepos4G ota 升级
                    BBLog.d(BBLog.TAG, "prepareToUpdate [任務類型] ----> WisePos4G update");
                    SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_NEED_WISEPOS4G_OTA_FLAG, true);
                    if (Constants.IS_BATTERY_CHARGING == false && Constants.IS_BATTERY_LOW == true) {
                        showBatteryLowDialogWhileUpdate(context, UpdateTaskTypeEnum.WISEPOSOTA, todoWSTaskJsonObj);
                    } else {
                        //保持任务的taskid。在升级完毕后，清除缓存数据并上报任务状态
                        SharedPreferencesUtils.setSharePreferencesValue(UsualData.SYSTEM_WISEPOS_OTA_TRIGGER_TASKID, todoWSTaskJsonObj.getString(ParameterName.taskId));
                        ActivityUtils.startOTAService(todoWSTaskJsonObj,Constants.TYPE_WISEPOS_TMT_UPGRADE);
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void showBatteryLowDialogWhileUpdate(Context context, UpdateTaskTypeEnum type,JSONObject todoWSTaskJsonObj) {
        mHandler.post(new Runnable() {
            @Override
            public void run() {
                if (lowBatdialog != null) {
                    lowBatdialog.dismiss();
                    lowBatdialog = null;
                }
                lowBatdialog = new Dialog(context, R.style.dialog_style_ex);
                lowBatdialog.setContentView(R.layout.dialog_confirm);
                ImageView imageView = (ImageView) lowBatdialog.findViewById(R.id.iv_image);
                imageView.setBackground(context.getDrawable(R.drawable.low_bat));
                TextView tv_title = (TextView) lowBatdialog.findViewById(R.id.tv_title);
                tv_title.setText(context.getString(R.string.low_battery));
                TextView tv_content = (TextView) lowBatdialog.findViewById(R.id.tv_content);
                tv_content.setText(context.getString(R.string.install_tip_dialog_message_low_power));
                lowBatdialog.setCanceledOnTouchOutside(false);
                TextView tv_cancel = (TextView) lowBatdialog.findViewById(R.id.tv_cancel);
                tv_cancel.setVisibility(View.GONE);
                TextView tv_install = (TextView) lowBatdialog.findViewById(R.id.tv_install);
                tv_install.setText(R.string.update);
                tv_install.setOnClickListener(
                    new View.OnClickListener() {
                        @Override
                        public void onClick(View v) {
                            lowBatdialog.dismiss();
                            if (Constants.IS_BATTERY_CHARGING==true || Constants.IS_BATTERY_LOW==false) {
                                try {
                                    if (DeviceInfoApi.getIntance().isWisePosPro()) {
                                        if (type == UpdateTaskTypeEnum.OTAUPDATE) {
                                            //保持任务的taskid。在升级完毕后，清除缓存数据并上报任务状态
                                            SharedPreferencesUtils.setSharePreferencesValue(UsualData.SYSTEM_OTA_TRIGGER_TASKID, todoWSTaskJsonObj.getString(ParameterName.taskId));
                                            ActivityUtils.startOTAService(todoWSTaskJsonObj,Constants.TYPE_WISECUBE_FW_UPGRADE);
                                        }else if (type == UpdateTaskTypeEnum.SPUPDATE) {
                                            //保持任务的taskid。在升级完毕后，清除缓存数据并上报任务状态
                                            SharedPreferencesUtils.setSharePreferencesValue(UsualData.SYSTEM_SP_OTA_TRIGGER_TASKID, todoWSTaskJsonObj.getString(ParameterName.taskId));
                                            ActivityUtils.startOTAService(todoWSTaskJsonObj,Constants.TYPE_SP_FW_UPGRADE);
                                        }
                                    }else if (DeviceInfoApi.getIntance().isWisePos5Plus() || DeviceInfoApi.getIntance().isWisePos5() || DeviceInfoApi.getIntance().isWisePosTouch() || DeviceInfoApi.getIntance().isWisePosTouchPlus()) {// P1000、P500
                                        if (type == UpdateTaskTypeEnum.TMT) {
                                            SharedPreferencesUtils.setSharePreferencesValue(UsualData.SYSTEM_TMT_TRIGGER_TASKID, todoWSTaskJsonObj.getString(ParameterName.taskId));
                                            ActivityUtils.startOTAService(todoWSTaskJsonObj,Constants.TYPE_TMT_UPGRADE);
                                        }else if (type == UpdateTaskTypeEnum.KEYUPDATE) {
                                            SharedPreferencesUtils.setSharePreferencesValue(UsualData.SYSTEM_KEY_UPDATE_TRIGGER_TASKID, todoWSTaskJsonObj.getString(ParameterName.taskId));
                                            ActivityUtils.startOTAService(todoWSTaskJsonObj,Constants.TYPE_KEY_UPGRADE);
                                        }
                                    }else if (DeviceInfoApi.getIntance().isWisePos4G()) {// WisePos 4G
                                        if (type == UpdateTaskTypeEnum.WISEPOSOTA) {
                                            SharedPreferencesUtils.setSharePreferencesValue(UsualData.SYSTEM_WISEPOS_OTA_TRIGGER_TASKID, todoWSTaskJsonObj.getString(ParameterName.taskId));
                                            ActivityUtils.startOTAService(todoWSTaskJsonObj,Constants.TYPE_WISEPOS_TMT_UPGRADE);
                                        }
                                    }
                                } catch (Exception e) {
                                    e.printStackTrace();
                                }
                            }
                        }
                    });
                lowBatdialog.getWindow().setType((WindowManager.LayoutParams.TYPE_SYSTEM_ALERT));
                lowBatdialog.show();
            }
        });
    }

    /**设置定时器，发送任务时间已到广播*/
    public void sendWSTaskTimeupBCOnMillis(Context context,long timeOnMillis) {
        Intent taskExeBCIntent = new Intent(UsualData.WSTASK_EXEC_BC);
        taskExeBCIntent.setComponent(new ComponentName(context.getPackageName(), WebSocketReceiver.class.getName()));
        if (timeOnMillis == 0) {
            BBLog.i(BBLog.TAG, "sendWSTaskTimeupBCOnMillis right now.");
            context.sendBroadcast(taskExeBCIntent, RequestPermission.REQUEST_PERMISSION_MY_BROADCAST);
            return;
        }

        BBLog.i(BBLog.TAG, "sendWSTaskTimeupBCOnMillis PendingIntent " + timeOnMillis);
        PendingIntent pi = PendingIntent.getBroadcast(context, (int) (timeOnMillis/1000), taskExeBCIntent, PendingIntent.FLAG_UPDATE_CURRENT);
        AlarmManager am = (AlarmManager) context.getSystemService(Context.ALARM_SERVICE);
        am.cancel(pi);// 取消旧有定时，是用timeOnMillis作为区分

        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.KITKAT) {
            am.set(AlarmManager.RTC_WAKEUP, timeOnMillis, pi);
        }else{
            am.setExact(AlarmManager.RTC_WAKEUP, timeOnMillis, pi);
        }
    }

    public static void setLoginSuccessFlag() {
        try {
            String filePath = null;
            File file = null;
            if (new File("/bbpos").exists()) {
                filePath = "/bbpos" + File.separator + Constants.LOGIN_SUCCESSFUL;
            }

            if (TextUtils.isEmpty(filePath)) {
                filePath = FileUtils.getWiseAppConfigPath() + File.separator + Constants.LOGIN_SUCCESSFUL;;
            }
            BBLog.w(BBLog.TAG, "Flag文件路徑：" + filePath);
            file = new File(filePath);
            if (!file.exists()) {
                file.createNewFile();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static boolean getLoginSuccessFlag() {
        String filePath = null;
        File file = null;
        if (new File("/bbpos").exists()) {
            filePath = "/bbpos" + File.separator + Constants.LOGIN_SUCCESSFUL;
        }

        if (TextUtils.isEmpty(filePath)) {
            filePath = FileUtils.getWiseAppConfigPath() + File.separator + Constants.LOGIN_SUCCESSFUL;;
        }
        BBLog.w(BBLog.TAG, "Flag文件路徑：" + filePath);
        file = new File(filePath);

        return file.exists();
    }

    public static void setSetLauncherFlag() {
        try {
            String filePath = null;
            File file = null;
            if (new File("/bbpos").exists()) {
                filePath = "/bbpos" + File.separator + Constants.SET_LAUNCHER;
            }

            if (TextUtils.isEmpty(filePath)) {
                filePath = FileUtils.getWiseAppConfigPath() + File.separator + Constants.SET_LAUNCHER;;
            }
            BBLog.w(BBLog.TAG, "Flag文件路徑：" + filePath);
            file = new File(filePath);
            if (!file.exists()) {
                file.createNewFile();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static boolean getSetLauncherFlag() {
        String filePath = null;
        File file = null;
        if (new File("/bbpos").exists()) {
            filePath = "/bbpos" + File.separator + Constants.SET_LAUNCHER;
        }

        if (TextUtils.isEmpty(filePath)) {
            filePath = FileUtils.getWiseAppConfigPath() + File.separator + Constants.SET_LAUNCHER;;
        }
        BBLog.w(BBLog.TAG, "Flag文件路徑：" + filePath);
        file = new File(filePath);

        return file.exists();
    }
}
