2025-08-14 18:36:28.438 19136-19207 Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"msg_state":"P","request_time":"1755167787028","data":{"ruleList":[{"deleteAppList":[{"apkName":"Via","appId":"353795033bc0410dac6dee5cb8702b06","packName":"mark.via","versionName":"6.6.0","versionCode":"20250713"}],"modifyDate":"2025-08-14 10:36:26","endDate":"9999-12-31 23:59:59","restartAfter":"0","installBy":"0","beginDate":"2024-08-14 10:36:26","appList":[{"apkMd5":"ba1ee533924eae5c408465e7cddcbda4","apkName":"逍遥助手","appId":"225a0a30994e45089dad1a270f8d85a1","apkSize":"4771451","appIconUrl":"https://siot-bucket01.s3.ap-northeast-1.amazonaws.com/icon/6dc157c7ff1649448fb7084544d2e8f5icon.png","packName":"plus.H52FFB9A5","versionName":"1.0","versionCode":"100","url":"https://siot-bucket01.s3.ap-northeast-1.amazonaws.com/app/203831945bb84d42b21decf1bf936815.apk"}],"serviceList":[],"action":"A","ruleName":"d60_test_299","ruleId":"83d4a14ed05348efa59fbc626e5d8b19","createDate":"2025-08-14 10:36:26","orgRuleId":""}]},"tranCode":"ST005","request_id":"1755167787028ST005","version":"1","serialNo":"01354090202503050399"}
2025-08-14 18:36:28.448 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 准备发送响应确认 - requestId: 1755167787028ST005, needResponse: true
2025-08-14 18:36:28.473 19136-19207 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0000","serialNo":"01354090202503050399","request_time":"1755167788457","request_id":"1755167788457C0000","version":"1","org_request_id":"1755167787028ST005","org_request_time":"1755167787028","response_state":"0","myVersionName":"1.0.08.20250814.DSPREAD.MDM.SERVICE","terminalDate":"20250814183628"}
2025-08-14 18:36:28.496 19136-19207 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0000","serialNo":"01354090202503050399","request_time":"1755167788482","request_id":"1755167788482C0000","version":"1","org_request_id":"1755167787028ST005","org_request_time":"1755167787028","response_state":"0","myVersionName":"1.0.08.20250814.DSPREAD.MDM.SERVICE","terminalDate":"20250814183628"}
2025-08-14 18:36:28.500 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 已发送C0000响应确认: requestId=1755167787028ST005
2025-08-14 18:36:28.509 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleHandler 处理规则消息: ST005
2025-08-14 18:36:28.514 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleHandler 处理规则列表，数量: 1
2025-08-14 18:36:28.518 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleHandler 处理规则: ruleId=83d4a14ed05348efa59fbc626e5d8b19, action=A
2025-08-14 18:36:28.523 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析规则JSON: ruleId=83d4a14ed05348efa59fbc626e5d8b19
2025-08-14 18:36:28.534 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule appList数组: [{"apkMd5":"ba1ee533924eae5c408465e7cddcbda4","apkName":"逍遥助手","appId":"225a0a30994e45089dad1a270f8d85a1","apkSize":"4771451","appIconUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/icon\/6dc157c7ff1649448fb7084544d2e8f5icon.png","packName":"plus.H52FFB9A5","versionName":"1.0","versionCode":"100","url":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/203831945bb84d42b21decf1bf936815.apk"}]
2025-08-14 18:36:28.540 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule deleteAppList数组: [{"apkName":"Via","appId":"353795033bc0410dac6dee5cb8702b06","packName":"mark.via","versionName":"6.6.0","versionCode":"20250713"}]
2025-08-14 18:36:28.545 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=plus.H52FFB9A5, apkName=逍遥助手
2025-08-14 18:36:28.550 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=mark.via, apkName=Via
2025-08-14 18:36:28.555 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后appList数量: 1
2025-08-14 18:36:28.560 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后deleteAppList数量: 1
2025-08-14 18:36:28.565 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleBaseStorage 处理规则: 83d4a14ed05348efa59fbc626e5d8b19, 操作: A
2025-08-14 18:36:28.569 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 验证规则: ruleId=83d4a14ed05348efa59fbc626e5d8b19, ruleType=app_management, action=A
2025-08-14 18:36:28.574 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule appList数量: 1, deleteAppList数量: 1
2025-08-14 18:36:28.595 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 验证应用管理规则: action=A
2025-08-14 18:36:28.600 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 验证应用列表: appList=1, deleteAppList=1
2025-08-14 18:36:28.606 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 应用列表验证通过
2025-08-14 18:36:28.627 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析规则JSON: ruleId=937eccc7e4ff4fc0bc9eaf2788685515
2025-08-14 18:36:28.635 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule appList数组: [{"packName":"mark.via","apkName":"Via","versionName":"6.6.0","versionCode":"20250713","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/4d256c4123b346fd817537b491342204.apk","apkMd5":"0d1225260d03e10a8ffc8409369c442a","apkSize":2565110,"installBy":"0","isSystemApp":false,"priority":0}]
2025-08-14 18:36:28.640 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule deleteAppList数组: []
2025-08-14 18:36:28.645 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=mark.via, apkName=Via
2025-08-14 18:36:28.649 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后appList数量: 1
2025-08-14 18:36:28.654 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后deleteAppList数量: 0
2025-08-14 18:36:28.659 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析规则JSON: ruleId=01f42138486940dd8ba8a136d84e239d
2025-08-14 18:36:28.664 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule appList数组: []
2025-08-14 18:36:28.671 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule deleteAppList数组: [{"packName":"mark.via","apkName":"Via","versionName":"6.6.0","versionCode":"20250713","apkUrl":"","apkMd5":"","apkSize":0,"installBy":"0","isSystemApp":false,"priority":0}]
2025-08-14 18:36:28.676 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=mark.via, apkName=Via
2025-08-14 18:36:28.681 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后appList数量: 0
2025-08-14 18:36:28.685 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后deleteAppList数量: 1
2025-08-14 18:36:28.690 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析规则JSON: ruleId=e195e52efa414316818a8d4b21fa4e9c
2025-08-14 18:36:28.695 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule appList数组: []
2025-08-14 18:36:28.705 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule deleteAppList数组: [{"packName":"com.chileaf.cl960.sample","apkName":"cl960","versionName":"1.1.0","versionCode":"1","apkUrl":"","apkMd5":"","apkSize":0,"installBy":"0","isSystemApp":false,"priority":0},{"packName":"plus.H52FFB9A5","apkName":"逍遥助手","versionName":"1.0","versionCode":"100","apkUrl":"","apkMd5":"","apkSize":0,"installBy":"0","isSystemApp":false,"priority":0}]
2025-08-14 18:36:28.710 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=com.chileaf.cl960.sample, apkName=cl960
2025-08-14 18:36:28.715 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=plus.H52FFB9A5, apkName=逍遥助手
2025-08-14 18:36:28.720 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后appList数量: 0
2025-08-14 18:36:28.724 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后deleteAppList数量: 2
2025-08-14 18:36:28.730 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析规则JSON: ruleId=6eb6f82614c74c3bb1f5ca0e32ef787e
2025-08-14 18:36:28.741 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule appList数组: [{"packName":"mark.via","apkName":"Via","versionName":"6.6.0","versionCode":"20250713","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/4d256c4123b346fd817537b491342204.apk","apkMd5":"0d1225260d03e10a8ffc8409369c442a","apkSize":2565110,"installBy":"0","isSystemApp":false,"priority":0},{"packName":"com.chileaf.cl960.sample","apkName":"cl960","versionName":"1.1.0","versionCode":"1","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/42e16b87eaa849bcaaf0f5fadcac6aab.apk","apkMd5":"54cc3a371939423d4288d63e1ac88019","apkSize":6155416,"installBy":"0","isSystemApp":false,"priority":0}]
2025-08-14 18:36:28.746 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule deleteAppList数组: []
2025-08-14 18:36:28.751 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=mark.via, apkName=Via
2025-08-14 18:36:28.756 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=com.chileaf.cl960.sample, apkName=cl960
2025-08-14 18:36:28.761 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后appList数量: 2
2025-08-14 18:36:28.765 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后deleteAppList数量: 0
2025-08-14 18:36:28.786 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析规则JSON: ruleId=937eccc7e4ff4fc0bc9eaf2788685515
2025-08-14 18:36:28.795 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule appList数组: [{"packName":"mark.via","apkName":"Via","versionName":"6.6.0","versionCode":"20250713","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/4d256c4123b346fd817537b491342204.apk","apkMd5":"0d1225260d03e10a8ffc8409369c442a","apkSize":2565110,"installBy":"0","isSystemApp":false,"priority":0}]
2025-08-14 18:36:28.799 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule deleteAppList数组: []
2025-08-14 18:36:28.804 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=mark.via, apkName=Via
2025-08-14 18:36:28.809 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后appList数量: 1
2025-08-14 18:36:28.814 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后deleteAppList数量: 0
2025-08-14 18:36:28.819 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析规则JSON: ruleId=01f42138486940dd8ba8a136d84e239d
2025-08-14 18:36:28.824 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule appList数组: []
2025-08-14 18:36:28.831 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule deleteAppList数组: [{"packName":"mark.via","apkName":"Via","versionName":"6.6.0","versionCode":"20250713","apkUrl":"","apkMd5":"","apkSize":0,"installBy":"0","isSystemApp":false,"priority":0}]
2025-08-14 18:36:28.836 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=mark.via, apkName=Via
2025-08-14 18:36:28.841 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后appList数量: 0
2025-08-14 18:36:28.845 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后deleteAppList数量: 1
2025-08-14 18:36:28.850 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析规则JSON: ruleId=e195e52efa414316818a8d4b21fa4e9c
2025-08-14 18:36:28.855 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule appList数组: []
2025-08-14 18:36:28.865 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule deleteAppList数组: [{"packName":"com.chileaf.cl960.sample","apkName":"cl960","versionName":"1.1.0","versionCode":"1","apkUrl":"","apkMd5":"","apkSize":0,"installBy":"0","isSystemApp":false,"priority":0},{"packName":"plus.H52FFB9A5","apkName":"逍遥助手","versionName":"1.0","versionCode":"100","apkUrl":"","apkMd5":"","apkSize":0,"installBy":"0","isSystemApp":false,"priority":0}]
2025-08-14 18:36:28.870 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=com.chileaf.cl960.sample, apkName=cl960
2025-08-14 18:36:28.875 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=plus.H52FFB9A5, apkName=逍遥助手
2025-08-14 18:36:28.880 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后appList数量: 0
2025-08-14 18:36:28.885 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后deleteAppList数量: 2
2025-08-14 18:36:28.890 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析规则JSON: ruleId=6eb6f82614c74c3bb1f5ca0e32ef787e
2025-08-14 18:36:28.901 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule appList数组: [{"packName":"mark.via","apkName":"Via","versionName":"6.6.0","versionCode":"20250713","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/4d256c4123b346fd817537b491342204.apk","apkMd5":"0d1225260d03e10a8ffc8409369c442a","apkSize":2565110,"installBy":"0","isSystemApp":false,"priority":0},{"packName":"com.chileaf.cl960.sample","apkName":"cl960","versionName":"1.1.0","versionCode":"1","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/42e16b87eaa849bcaaf0f5fadcac6aab.apk","apkMd5":"54cc3a371939423d4288d63e1ac88019","apkSize":6155416,"installBy":"0","isSystemApp":false,"priority":0}]
2025-08-14 18:36:28.906 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule deleteAppList数组: []
2025-08-14 18:36:28.911 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=mark.via, apkName=Via
2025-08-14 18:36:28.916 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=com.chileaf.cl960.sample, apkName=cl960
2025-08-14 18:36:28.921 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后appList数量: 2
2025-08-14 18:36:28.925 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后deleteAppList数量: 0
2025-08-14 18:36:28.982 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleBaseStorage 保存 5 个规则到存储
2025-08-14 18:36:28.987 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleBaseStorage Rule 83d4a14ed05348efa59fbc626e5d8b19 添加成功
2025-08-14 18:36:28.991 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleBaseStorage 触发规则执行: 83d4a14ed05348efa59fbc626e5d8b19
2025-08-14 18:36:28.997 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleHandler 规则处理成功: 83d4a14ed05348efa59fbc626e5d8b19
2025-08-14 18:36:28.997 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 RuleBaseStorage 设备空闲，立即执行规则: 83d4a14ed05348efa59fbc626e5d8b19
2025-08-14 18:36:29.002 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleHandler 规则列表处理完成: 处理完成 - 成功: 1, 失败: 0, 忽略: 0
2025-08-14 18:36:29.002 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 RuleBaseManager 直接执行规则: 83d4a14ed05348efa59fbc626e5d8b19
2025-08-14 18:36:29.007 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleHandler 上报规则处理结果: 0 - 处理完成 - 成功: 1, 失败: 0, 忽略: 0
2025-08-14 18:36:29.008 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 RuleBaseManager 处理规则: 83d4a14ed05348efa59fbc626e5d8b19, 操作: A
2025-08-14 18:36:29.012 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 C0107 主动式上送: rulebase_update (主动: 29)
2025-08-14 18:36:29.013 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析规则JSON: ruleId=83d4a14ed05348efa59fbc626e5d8b19
2025-08-14 18:36:29.022 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 Rule appList数组: [{"packName":"plus.H52FFB9A5","apkName":"逍遥助手","versionName":"1.0","versionCode":"100","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/203831945bb84d42b21decf1bf936815.apk","apkMd5":"ba1ee533924eae5c408465e7cddcbda4","apkSize":4771451,"installBy":"0","isSystemApp":false,"priority":0}]
2025-08-14 18:36:29.029 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 Rule deleteAppList数组: [{"packName":"mark.via","apkName":"Via","versionName":"6.6.0","versionCode":"20250713","apkUrl":"","apkMd5":"","apkSize":0,"installBy":"0","isSystemApp":false,"priority":0}]
2025-08-14 18:36:29.031 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 添加规则结果: ruleId=batch_rules
2025-08-14 18:36:29.034 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=plus.H52FFB9A5, apkName=逍遥助手
2025-08-14 18:36:29.038 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=mark.via, apkName=Via
2025-08-14 18:36:29.043 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后appList数量: 1
2025-08-14 18:36:29.046 19136-19207 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0107","serialNo":"01354090202503050399","request_time":"1755167789026","request_id":"1755167789026C0107","version":"1","data":{"ruleId":"batch_rules","taskResult":"0"},"myVersionName":"1.0.08.20250814.DSPREAD.MDM.SERVICE","terminalDate":"20250814183629","org_request_id":"1755167787028ST005","org_request_time":"1755167787028"}
2025-08-14 18:36:29.048 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后deleteAppList数量: 1
2025-08-14 18:36:29.051 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 规则结果实时上送: ruleId=batch_rules, result=0 (1)
2025-08-14 18:36:29.053 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 RuleBaseStorage 处理规则: 83d4a14ed05348efa59fbc626e5d8b19, 操作: A
2025-08-14 18:36:29.057 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 Rule 验证规则: ruleId=83d4a14ed05348efa59fbc626e5d8b19, ruleType=app_management, action=A
2025-08-14 18:36:29.062 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 Rule appList数量: 1, deleteAppList数量: 1
2025-08-14 18:36:29.083 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 Rule 验证应用管理规则: action=A
2025-08-14 18:36:29.088 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 Rule 验证应用列表: appList=1, deleteAppList=1
2025-08-14 18:36:29.093 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 Rule 应用列表验证通过
2025-08-14 18:36:29.119 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析规则JSON: ruleId=937eccc7e4ff4fc0bc9eaf2788685515
2025-08-14 18:36:29.127 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 Rule appList数组: [{"packName":"mark.via","apkName":"Via","versionName":"6.6.0","versionCode":"20250713","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/4d256c4123b346fd817537b491342204.apk","apkMd5":"0d1225260d03e10a8ffc8409369c442a","apkSize":2565110,"installBy":"0","isSystemApp":false,"priority":0}]
2025-08-14 18:36:29.132 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 Rule deleteAppList数组: []
2025-08-14 18:36:29.137 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=mark.via, apkName=Via
2025-08-14 18:36:29.142 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后appList数量: 1
2025-08-14 18:36:29.146 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后deleteAppList数量: 0
2025-08-14 18:36:29.151 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析规则JSON: ruleId=01f42138486940dd8ba8a136d84e239d
2025-08-14 18:36:29.156 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 Rule appList数组: []
2025-08-14 18:36:29.163 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 Rule deleteAppList数组: [{"packName":"mark.via","apkName":"Via","versionName":"6.6.0","versionCode":"20250713","apkUrl":"","apkMd5":"","apkSize":0,"installBy":"0","isSystemApp":false,"priority":0}]
2025-08-14 18:36:29.168 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=mark.via, apkName=Via
2025-08-14 18:36:29.173 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后appList数量: 0
2025-08-14 18:36:29.177 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后deleteAppList数量: 1
2025-08-14 18:36:29.182 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析规则JSON: ruleId=e195e52efa414316818a8d4b21fa4e9c
2025-08-14 18:36:29.187 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 Rule appList数组: []
2025-08-14 18:36:29.197 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 Rule deleteAppList数组: [{"packName":"com.chileaf.cl960.sample","apkName":"cl960","versionName":"1.1.0","versionCode":"1","apkUrl":"","apkMd5":"","apkSize":0,"installBy":"0","isSystemApp":false,"priority":0},{"packName":"plus.H52FFB9A5","apkName":"逍遥助手","versionName":"1.0","versionCode":"100","apkUrl":"","apkMd5":"","apkSize":0,"installBy":"0","isSystemApp":false,"priority":0}]
2025-08-14 18:36:29.202 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=com.chileaf.cl960.sample, apkName=cl960
2025-08-14 18:36:29.207 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=plus.H52FFB9A5, apkName=逍遥助手
2025-08-14 18:36:29.212 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后appList数量: 0
2025-08-14 18:36:29.216 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后deleteAppList数量: 2
2025-08-14 18:36:29.221 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析规则JSON: ruleId=6eb6f82614c74c3bb1f5ca0e32ef787e
2025-08-14 18:36:29.233 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 Rule appList数组: [{"packName":"mark.via","apkName":"Via","versionName":"6.6.0","versionCode":"20250713","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/4d256c4123b346fd817537b491342204.apk","apkMd5":"0d1225260d03e10a8ffc8409369c442a","apkSize":2565110,"installBy":"0","isSystemApp":false,"priority":0},{"packName":"com.chileaf.cl960.sample","apkName":"cl960","versionName":"1.1.0","versionCode":"1","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/42e16b87eaa849bcaaf0f5fadcac6aab.apk","apkMd5":"54cc3a371939423d4288d63e1ac88019","apkSize":6155416,"installBy":"0","isSystemApp":false,"priority":0}]
2025-08-14 18:36:29.238 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 Rule deleteAppList数组: []
2025-08-14 18:36:29.243 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=mark.via, apkName=Via
2025-08-14 18:36:29.247 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=com.chileaf.cl960.sample, apkName=cl960
2025-08-14 18:36:29.252 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后appList数量: 2
2025-08-14 18:36:29.257 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后deleteAppList数量: 0
2025-08-14 18:36:29.262 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析规则JSON: ruleId=83d4a14ed05348efa59fbc626e5d8b19
2025-08-14 18:36:29.271 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 Rule appList数组: [{"packName":"plus.H52FFB9A5","apkName":"逍遥助手","versionName":"1.0","versionCode":"100","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/203831945bb84d42b21decf1bf936815.apk","apkMd5":"ba1ee533924eae5c408465e7cddcbda4","apkSize":4771451,"installBy":"0","isSystemApp":false,"priority":0}]
2025-08-14 18:36:29.278 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 Rule deleteAppList数组: [{"packName":"mark.via","apkName":"Via","versionName":"6.6.0","versionCode":"20250713","apkUrl":"","apkMd5":"","apkSize":0,"installBy":"0","isSystemApp":false,"priority":0}]
2025-08-14 18:36:29.283 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=plus.H52FFB9A5, apkName=逍遥助手
2025-08-14 18:36:29.288 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=mark.via, apkName=Via
2025-08-14 18:36:29.293 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后appList数量: 1
2025-08-14 18:36:29.297 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后deleteAppList数量: 1
2025-08-14 18:36:29.302 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 RuleBaseStorage Rule 83d4a14ed05348efa59fbc626e5d8b19 已存在，忽略Add操作
2025-08-14 18:36:29.307 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 RuleStateMachine 规则初始化成功: 83d4a14ed05348efa59fbc626e5d8b19 -> RuleState(code=todo, description=等待执行)
2025-08-14 18:36:29.312 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 RuleStateMachine 规则已存在: 83d4a14ed05348efa59fbc626e5d8b19, 当前状态: RuleState(code=todo, description=等待执行)
2025-08-14 18:36:29.317 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 创建应用状态: packName=plus.H52FFB9A5, apkName=逍遥助手
2025-08-14 18:36:29.323 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 初始化规则应用状态: 83d4a14ed05348efa59fbc626e5d8b19, 应用数量: 2
2025-08-14 18:36:29.328 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 RuleStateMonitor 收到状态变化: 83d4a14ed05348efa59fbc626e5d8b19, todo -> R01
2025-08-14 18:36:29.332 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 RuleStateMonitor 状态变化已记录: 83d4a14ed05348efa59fbc626e5d8b19, R01
2025-08-14 18:36:29.337 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 RuleStateMachine 状态转换成功: 83d4a14ed05348efa59fbc626e5d8b19, todo -> R01
2025-08-14 18:36:29.342 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 RuleStateMonitor 收到状态变化: 83d4a14ed05348efa59fbc626e5d8b19, R01 -> R02
2025-08-14 18:36:29.347 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 RuleStateMonitor 状态变化已记录: 83d4a14ed05348efa59fbc626e5d8b19, R02
2025-08-14 18:36:29.352 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 RuleStateMachine 状态转换成功: 83d4a14ed05348efa59fbc626e5d8b19, R01 -> R02
2025-08-14 18:36:29.356 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 开始执行规则: 83d4a14ed05348efa59fbc626e5d8b19
2025-08-14 18:36:29.361 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 执行卸载应用，数量: 1
2025-08-14 18:36:29.366 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 开始卸载应用，数量: 1
2025-08-14 18:36:29.370 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 卸载应用: mark.via
2025-08-14 18:36:29.377 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 更新应用状态: 83d4a14ed05348efa59fbc626e5d8b19, mark.via -> D01
2025-08-14 18:36:29.382 19136-19165 WebSocket               com.dspread.mdm.service              I  🔧 C0107 主动式上送: rulebase_update (主动: 30)
2025-08-14 18:36:29.392 19136-19207 Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"request_time":"1755167788181","org_request_time":"1755167789026","org_request_id":"1755167789026C0107","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755167788181S0000","serialNo":"01354090202503050399"}
2025-08-14 18:36:29.399 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 收到响应消息: transCode=S0000, orgRequestId=1755167789026C0107, state=0, remark=
2025-08-14 18:36:29.401 19136-19165 WebSocket               com.dspread.mdm.service              I  🔧 添加规则结果: ruleId=83d4a14ed05348efa59fbc626e5d8b19
2025-08-14 18:36:29.404 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 处理规则结果响应: state=0
2025-08-14 18:36:29.409 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 规则结果上传成功
2025-08-14 18:36:29.431 19136-19165 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0107","serialNo":"01354090202503050399","request_time":"1755167789396","request_id":"1755167789396C0107","version":"1","data":{"ruleId":"83d4a14ed05348efa59fbc626e5d8b19","taskResult":"R02","failedApkList":[{"packName":"plus.H52FFB9A5","apkName":"逍遥助手","versionName":"1.0","versionCode":"100","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/203831945bb84d42b21decf1bf936815.apk","apkMd5":"ba1ee533924eae5c408465e7cddcbda4","apkSize":4771451,"installBy":"0","isSystemApp":false,"priority":0,"result":"W01","taskType":"01"},{"packName":"mark.via","apkName":"Via","versionName":"6.6.0","versionCode":"20250713","apkUrl":"","apkMd5":"","apkSize":0,"installBy":"0","isSystemApp":false,"priority":0,"result":"D01","taskType":"02"}]},"myVersionName":"1.0.08.20250814.DSPREAD.MDM.SERVICE","terminalDate":"20250814183629"}
2025-08-14 18:36:29.436 19136-19165 WebSocket               com.dspread.mdm.service              I  🔧 规则结果实时上送: ruleId=83d4a14ed05348efa59fbc626e5d8b19, result=R02 (1)
2025-08-14 18:36:29.441 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 上报规则执行中状态: 83d4a14ed05348efa59fbc626e5d8b19, 应用数量: 2
2025-08-14 18:36:29.446 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 注册卸载回调: mark.via (规则ID: 83d4a14ed05348efa59fbc626e5d8b19)
2025-08-14 18:36:29.451 19136-19165 AppManager              com.dspread.mdm.service              I  ℹ️ Uninstalling app via PackageInstaller: mark.via
2025-08-14 18:36:29.455 19136-19165 AppManager              com.dspread.mdm.service              I  ℹ️ Deleting package via PackageInstaller: mark.via
2025-08-14 18:36:29.463 19136-19165 AppManager              com.dspread.mdm.service              I  ℹ️ PackageInstaller uninstall initiated
2025-08-14 18:36:29.468 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 执行安装应用，数量: 1
2025-08-14 18:36:29.473 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 开始安装应用，数量: 1
2025-08-14 18:36:29.479 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 安装应用: plus.H52FFB9A5, URL: https://siot-bucket01.s3.ap-northeast-1.amazonaws.com/app/203831945bb84d42b21decf1bf936815.apk
2025-08-14 18:36:29.484 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 检查应用安装状态: plus.H52FFB9A5
2025-08-14 18:36:29.542 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 应用是否已安装: false
2025-08-14 18:36:29.547 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 应用未安装，需要安装
2025-08-14 18:36:29.552 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 更新应用状态: 83d4a14ed05348efa59fbc626e5d8b19, plus.H52FFB9A5 -> A01
2025-08-14 18:36:29.557 19136-19165 WebSocket               com.dspread.mdm.service              I  🔧 C0107 主动式上送: rulebase_update (主动: 31)
2025-08-14 18:36:29.596 19136-19207 Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"request_time":"1755167788549","org_request_time":"1755167789396","org_request_id":"1755167789396C0107","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755167788549S0000","serialNo":"01354090202503050399"}
2025-08-14 18:36:29.605 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 收到响应消息: transCode=S0000, orgRequestId=1755167789396C0107, state=0, remark=
2025-08-14 18:36:29.612 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 处理规则结果响应: state=0
2025-08-14 18:36:29.618 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 规则结果上传成功
2025-08-14 18:36:29.622 19136-19165 WebSocket               com.dspread.mdm.service              I  🔧 添加规则结果: ruleId=83d4a14ed05348efa59fbc626e5d8b19
2025-08-14 18:36:29.673 19136-19165 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0107","serialNo":"01354090202503050399","request_time":"1************","request_id":"1************C0107","version":"1","data":{"ruleId":"83d4a14ed05348efa59fbc626e5d8b19","taskResult":"R02","failedApkList":[{"packName":"plus.H52FFB9A5","apkName":"逍遥助手","versionName":"1.0","versionCode":"100","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/203831945bb84d42b21decf1bf936815.apk","apkMd5":"ba1ee533924eae5c408465e7cddcbda4","apkSize":4771451,"installBy":"0","isSystemApp":false,"priority":0,"result":"A01","taskType":"01"},{"packName":"mark.via","apkName":"Via","versionName":"6.6.0","versionCode":"20250713","apkUrl":"","apkMd5":"","apkSize":0,"installBy":"0","isSystemApp":false,"priority":0,"result":"D01","taskType":"02"}]},"myVersionName":"1.0.08.20250814.DSPREAD.MDM.SERVICE","terminalDate":"20250814183629"}
2025-08-14 18:36:29.678 19136-19165 WebSocket               com.dspread.mdm.service              I  🔧 规则结果实时上送: ruleId=83d4a14ed05348efa59fbc626e5d8b19, result=R02 (1)
2025-08-14 18:36:29.683 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 上报规则执行中状态: 83d4a14ed05348efa59fbc626e5d8b19, 应用数量: 2
2025-08-14 18:36:29.689 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 规则执行启动成功: 83d4a14ed05348efa59fbc626e5d8b19
2025-08-14 18:36:29.690 19136-19425 HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 尝试下载，第1次
2025-08-14 18:36:29.694 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 RuleBaseManager 规则添加成功: 83d4a14ed05348efa59fbc626e5d8b19
2025-08-14 18:36:29.710 19136-19425 HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 开始下载
2025-08-14 18:36:29.715 19136-19425 HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader URL: https://siot-bucket01.s3.ap-northeast-1.amazonaws.com/app/203831945bb84d42b21decf1bf936815.apk
2025-08-14 18:36:29.720 19136-19425 HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 路径: /data/user/0/com.dspread.mdm.service/files/downloads/rule_83d4a14ed05348efa59fbc626e5d8b19_plus.H52FFB9A5.apk
2025-08-14 18:36:29.727 19136-19425 HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 本地文件不存在，创建文件
2025-08-14 18:36:29.737 19136-19136 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 收到广播: android.intent.action.PACKAGE_REMOVED
2025-08-14 18:36:29.760 19136-19425 HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader allFileLength=4771451, 需要从服务器获取文件大小
2025-08-14 18:36:29.771 19136-19136 Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 收到包更新广播: android.intent.action.PACKAGE_REMOVED, 包名: mark.via
2025-08-14 18:36:29.780 19136-19136 Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 应用卸载: mark.via
2025-08-14 18:36:29.789 19136-19136 Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 检查WebSocket状态: packageName=mark.via, action=UNINSTALL
2025-08-14 18:36:29.796 19136-19136 Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 通知rulebase处理应用卸载: mark.via
2025-08-14 18:36:29.802 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 收到系统广播，应用已卸载: mark.via
2025-08-14 18:36:29.812 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 找到匹配的卸载回调，触发: mark.via
2025-08-14 18:36:29.818 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 卸载回调被触发: pkg=mark.via, returnCode=1, error=
2025-08-14 18:36:29.823 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 应用卸载启动成功: mark.via
2025-08-14 18:36:29.830 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 从广播更新应用状态: 83d4a14ed05348efa59fbc626e5d8b19, mark.via -> D02
2025-08-14 18:36:29.836 19136-19136 WebSocket               com.dspread.mdm.service              I  🔧 C0107 主动式上送: rulebase_update (主动: 32)
2025-08-14 18:36:29.867 19136-19136 WebSocket               com.dspread.mdm.service              I  🔧 添加规则结果: ruleId=83d4a14ed05348efa59fbc626e5d8b19
2025-08-14 18:36:29.885 19136-19207 Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"request_time":"1755167788790","org_request_time":"1************","org_request_id":"1************C0107","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755167788790S0000","serialNo":"01354090202503050399"}
2025-08-14 18:36:29.896 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 收到响应消息: transCode=S0000, orgRequestId=1************C0107, state=0, remark=
2025-08-14 18:36:29.902 19136-19425 TrafficStats            com.dspread.mdm.service              D  tagSocket(98) with statsTag=0xffffffff, statsUid=-1
2025-08-14 18:36:29.903 19136-19136 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0107","serialNo":"01354090202503050399","request_time":"1755167789852","request_id":"1755167789852C0107","version":"1","data":{"ruleId":"83d4a14ed05348efa59fbc626e5d8b19","taskResult":"R02","failedApkList":[{"packName":"plus.H52FFB9A5","apkName":"逍遥助手","versionName":"1.0","versionCode":"100","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/203831945bb84d42b21decf1bf936815.apk","apkMd5":"ba1ee533924eae5c408465e7cddcbda4","apkSize":4771451,"installBy":"0","isSystemApp":false,"priority":0,"result":"A01","taskType":"01"},{"packName":"mark.via","apkName":"Via","versionName":"6.6.0","versionCode":"20250713","apkUrl":"","apkMd5":"","apkSize":0,"installBy":"0","isSystemApp":false,"priority":0,"result":"D02","taskType":"02"}]},"myVersionName":"1.0.08.20250814.DSPREAD.MDM.SERVICE","terminalDate":"20250814183629"}
2025-08-14 18:36:29.907 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 处理规则结果响应: state=0
2025-08-14 18:36:29.909 19136-19136 WebSocket               com.dspread.mdm.service              I  🔧 规则结果实时上送: ruleId=83d4a14ed05348efa59fbc626e5d8b19, result=R02 (1)
2025-08-14 18:36:29.914 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 规则结果上传成功
2025-08-14 18:36:29.915 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 上报规则执行中状态: 83d4a14ed05348efa59fbc626e5d8b19, 应用数量: 2
2025-08-14 18:36:29.921 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 开始检查规则完成状态: 83d4a14ed05348efa59fbc626e5d8b19, 总应用数: 2
2025-08-14 18:36:30.030 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 检查应用状态: plus.H52FFB9A5 -> A01
2025-08-14 18:36:30.036 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 发现未完成状态: plus.H52FFB9A5 -> A01
2025-08-14 18:36:30.041 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 规则完成检查结果: 83d4a14ed05348efa59fbc626e5d8b19, 全部完成: false, 完成数: 0/2, 有失败: false
2025-08-14 18:36:30.046 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 规则未完成: 83d4a14ed05348efa59fbc626e5d8b19, 完成数: 0/2
2025-08-14 18:36:30.051 19136-19136 Platform                com.dspread.mdm.service              D  🔧 重新收集应用信息（强制刷新）
2025-08-14 18:36:30.156 19136-19136 AppManager              com.dspread.mdm.service              I  ℹ️ 应用信息获取完成: 总数131(系统129/用户2) 返回2个
2025-08-14 18:36:30.177 19136-19136 Platform                com.dspread.mdm.service              D  🔧 应用信息: 2 个应用
2025-08-14 18:36:30.182 19136-19136 Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 应用信息缓存刷新完成
2025-08-14 18:36:30.188 19136-19136 ContextImpl             com.dspread.mdm.service              W  Calling a method in the system process without a qualified user: android.app.ContextImpl.sendBroadcast:1220 android.content.ContextWrapper.sendBroadcast:504 com.dspread.mdm.service.broadcast.core.BroadcastSender.sendBroadcast:99 com.dspread.mdm.service.broadcast.core.BroadcastSender.sendBroadcast:23 com.dspread.mdm.service.broadcast.handlers.system.PackageUpdateEventHandlerImpl.sendAppRefreshBroadcast:491 
2025-08-14 18:36:30.192 19136-19136 Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 发送应用刷新广播
2025-08-14 18:36:30.198 19136-19136 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 收到广播: android.intent.action.PACKAGE_REMOVED
2025-08-14 18:36:30.204 19136-19136 Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 收到包更新广播: android.intent.action.PACKAGE_REMOVED, 包名: mark.via
2025-08-14 18:36:30.210 19136-19136 Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 应用卸载: mark.via
2025-08-14 18:36:30.215 19136-19136 Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 检查WebSocket状态: packageName=mark.via, action=UNINSTALL
2025-08-14 18:36:30.221 19136-19136 Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 通知rulebase处理应用卸载: mark.via
2025-08-14 18:36:30.226 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 收到系统广播，应用已卸载: mark.via
2025-08-14 18:36:30.231 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 未找到匹配的卸载回调: mark.via
2025-08-14 18:36:30.237 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 从广播更新应用状态: 83d4a14ed05348efa59fbc626e5d8b19, mark.via -> D02
2025-08-14 18:36:30.242 19136-19136 WebSocket               com.dspread.mdm.service              I  🔧 C0107 主动式上送: rulebase_update (主动: 33)
2025-08-14 18:36:30.263 19136-19136 WebSocket               com.dspread.mdm.service              I  🔧 添加规则结果: ruleId=83d4a14ed05348efa59fbc626e5d8b19
2025-08-14 18:36:30.296 19136-19207 Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"request_time":"1755167789020","org_request_time":"1755167789852","org_request_id":"1755167789852C0107","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755167789020S0000","serialNo":"01354090202503050399"}
2025-08-14 18:36:30.296 19136-19136 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0107","serialNo":"01354090202503050399","request_time":"1755167790257","request_id":"1755167790257C0107","version":"1","data":{"ruleId":"83d4a14ed05348efa59fbc626e5d8b19","taskResult":"R02","failedApkList":[{"packName":"plus.H52FFB9A5","apkName":"逍遥助手","versionName":"1.0","versionCode":"100","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/203831945bb84d42b21decf1bf936815.apk","apkMd5":"ba1ee533924eae5c408465e7cddcbda4","apkSize":4771451,"installBy":"0","isSystemApp":false,"priority":0,"result":"A01","taskType":"01"},{"packName":"mark.via","apkName":"Via","versionName":"6.6.0","versionCode":"20250713","apkUrl":"","apkMd5":"","apkSize":0,"installBy":"0","isSystemApp":false,"priority":0,"result":"D02","taskType":"02"}]},"myVersionName":"1.0.08.20250814.DSPREAD.MDM.SERVICE","terminalDate":"20250814183630"}
2025-08-14 18:36:30.304 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 收到响应消息: transCode=S0000, orgRequestId=1755167789852C0107, state=0, remark=
2025-08-14 18:36:30.305 19136-19136 WebSocket               com.dspread.mdm.service              I  🔧 规则结果实时上送: ruleId=83d4a14ed05348efa59fbc626e5d8b19, result=R02 (1)
2025-08-14 18:36:30.310 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 处理规则结果响应: state=0
2025-08-14 18:36:30.311 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 上报规则执行中状态: 83d4a14ed05348efa59fbc626e5d8b19, 应用数量: 2
2025-08-14 18:36:30.315 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 规则结果上传成功
2025-08-14 18:36:30.316 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 开始检查规则完成状态: 83d4a14ed05348efa59fbc626e5d8b19, 总应用数: 2
2025-08-14 18:36:30.321 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 检查应用状态: plus.H52FFB9A5 -> A01
2025-08-14 18:36:30.326 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 发现未完成状态: plus.H52FFB9A5 -> A01
2025-08-14 18:36:30.332 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 规则完成检查结果: 83d4a14ed05348efa59fbc626e5d8b19, 全部完成: false, 完成数: 0/2, 有失败: false
2025-08-14 18:36:30.337 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 规则未完成: 83d4a14ed05348efa59fbc626e5d8b19, 完成数: 0/2
2025-08-14 18:36:30.342 19136-19136 Platform                com.dspread.mdm.service              D  🔧 重新收集应用信息（强制刷新）
2025-08-14 18:36:30.421 19136-19207 Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"request_time":"1755167789416","org_request_time":"1755167790257","org_request_id":"1755167790257C0107","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755167789416S0000","serialNo":"01354090202503050399"}
2025-08-14 18:36:30.429 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 收到响应消息: transCode=S0000, orgRequestId=1755167790257C0107, state=0, remark=
2025-08-14 18:36:30.434 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 处理规则结果响应: state=0
2025-08-14 18:36:30.438 19136-19136 AppManager              com.dspread.mdm.service              I  ℹ️ 应用信息获取完成: 总数131(系统129/用户2) 返回2个
2025-08-14 18:36:30.439 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 规则结果上传成功
2025-08-14 18:36:30.455 19136-19136 Platform                com.dspread.mdm.service              D  🔧 应用信息: 2 个应用
2025-08-14 18:36:30.461 19136-19136 Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 应用信息缓存刷新完成
2025-08-14 18:36:30.467 19136-19136 ContextImpl             com.dspread.mdm.service              W  Calling a method in the system process without a qualified user: android.app.ContextImpl.sendBroadcast:1220 android.content.ContextWrapper.sendBroadcast:504 com.dspread.mdm.service.broadcast.core.BroadcastSender.sendBroadcast:99 com.dspread.mdm.service.broadcast.core.BroadcastSender.sendBroadcast:23 com.dspread.mdm.service.broadcast.handlers.system.PackageUpdateEventHandlerImpl.sendAppRefreshBroadcast:491 
2025-08-14 18:36:30.469 19136-19136 Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 发送应用刷新广播
2025-08-14 18:36:30.478 19136-19425 HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader HTTP响应码: 200
2025-08-14 18:36:30.485 19136-19425 HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 服务器返回Content-Length: 4771451
2025-08-14 18:36:30.491 19136-19425 HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader localFile = true /data/user/0/com.dspread.mdm.service/files/downloads/rule_83d4a14ed05348efa59fbc626e5d8b19_plus.H52FFB9A5.apk
2025-08-14 18:36:30.500 19136-19425 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 下载进度: plus.H52FFB9A5 - 0%
2025-08-14 18:36:31.278 19136-19425 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 下载进度: plus.H52FFB9A5 - 10%
2025-08-14 18:36:31.984 19136-19425 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 下载进度: plus.H52FFB9A5 - 20%
2025-08-14 18:36:32.456 19136-19425 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 下载进度: plus.H52FFB9A5 - 30%
2025-08-14 18:36:33.053 19136-19425 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 下载进度: plus.H52FFB9A5 - 40%
2025-08-14 18:36:33.616 19136-19425 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 下载进度: plus.H52FFB9A5 - 50%
2025-08-14 18:36:34.209 19136-19425 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 下载进度: plus.H52FFB9A5 - 60%
2025-08-14 18:36:34.715 19136-19425 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 下载进度: plus.H52FFB9A5 - 70%
2025-08-14 18:36:35.276 19136-19425 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 下载进度: plus.H52FFB9A5 - 80%
2025-08-14 18:36:35.837 19136-19425 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 下载进度: plus.H52FFB9A5 - 90%
2025-08-14 18:36:36.397 19136-19425 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 下载进度: plus.H52FFB9A5 - 100%
2025-08-14 18:36:36.406 19136-19425 HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 计算MD5: rule_83d4a14ed05348efa59fbc626e5d8b19_plus.H52FFB9A5.apk
2025-08-14 18:36:36.486 19136-19425 HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 文件: 4771451 MD5: ba1ee533924eae5c408465e7cddcbda4
2025-08-14 18:36:36.491 19136-19425 HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader MD5校验正确
2025-08-14 18:36:36.495 19136-19425 HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 清理连接资源
2025-08-14 18:36:36.500 19136-19425 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine APK下载成功: /data/user/0/com.dspread.mdm.service/files/downloads/rule_83d4a14ed05348efa59fbc626e5d8b19_plus.H52FFB9A5.apk
2025-08-14 18:36:36.505 19136-19425 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 更新应用状态: 83d4a14ed05348efa59fbc626e5d8b19, plus.H52FFB9A5 -> A03
2025-08-14 18:36:36.510 19136-19425 WebSocket               com.dspread.mdm.service              I  🔧 C0107 主动式上送: rulebase_update (主动: 34)
2025-08-14 18:36:36.530 19136-19425 WebSocket               com.dspread.mdm.service              I  🔧 添加规则结果: ruleId=83d4a14ed05348efa59fbc626e5d8b19
2025-08-14 18:36:36.561 19136-19425 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0107","serialNo":"01354090202503050399","request_time":"1755167796523","request_id":"1755167796523C0107","version":"1","data":{"ruleId":"83d4a14ed05348efa59fbc626e5d8b19","taskResult":"R02","failedApkList":[{"packName":"plus.H52FFB9A5","apkName":"逍遥助手","versionName":"1.0","versionCode":"100","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/203831945bb84d42b21decf1bf936815.apk","apkMd5":"ba1ee533924eae5c408465e7cddcbda4","apkSize":4771451,"installBy":"0","isSystemApp":false,"priority":0,"result":"A03","taskType":"01"},{"packName":"mark.via","apkName":"Via","versionName":"6.6.0","versionCode":"20250713","apkUrl":"","apkMd5":"","apkSize":0,"installBy":"0","isSystemApp":false,"priority":0,"result":"D02","taskType":"02"}]},"myVersionName":"1.0.08.20250814.DSPREAD.MDM.SERVICE","terminalDate":"20250814183636"}
2025-08-14 18:36:36.566 19136-19425 WebSocket               com.dspread.mdm.service              I  🔧 规则结果实时上送: ruleId=83d4a14ed05348efa59fbc626e5d8b19, result=R02 (1)
2025-08-14 18:36:36.570 19136-19425 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 上报规则执行中状态: 83d4a14ed05348efa59fbc626e5d8b19, 应用数量: 2
2025-08-14 18:36:36.575 19136-19425 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 开始安装APK: /data/user/0/com.dspread.mdm.service/files/downloads/rule_83d4a14ed05348efa59fbc626e5d8b19_plus.H52FFB9A5.apk
2025-08-14 18:36:36.580 19136-19425 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 更新应用状态: 83d4a14ed05348efa59fbc626e5d8b19, plus.H52FFB9A5 -> B02
2025-08-14 18:36:36.585 19136-19425 WebSocket               com.dspread.mdm.service              I  🔧 C0107 主动式上送: rulebase_update (主动: 35)
2025-08-14 18:36:36.603 19136-19425 WebSocket               com.dspread.mdm.service              I  🔧 添加规则结果: ruleId=83d4a14ed05348efa59fbc626e5d8b19
2025-08-14 18:36:36.634 19136-19425 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0107","serialNo":"01354090202503050399","request_time":"1755167796598","request_id":"1755167796598C0107","version":"1","data":{"ruleId":"83d4a14ed05348efa59fbc626e5d8b19","taskResult":"R02","failedApkList":[{"packName":"plus.H52FFB9A5","apkName":"逍遥助手","versionName":"1.0","versionCode":"100","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/203831945bb84d42b21decf1bf936815.apk","apkMd5":"ba1ee533924eae5c408465e7cddcbda4","apkSize":4771451,"installBy":"0","isSystemApp":false,"priority":0,"result":"B02","taskType":"01"},{"packName":"mark.via","apkName":"Via","versionName":"6.6.0","versionCode":"20250713","apkUrl":"","apkMd5":"","apkSize":0,"installBy":"0","isSystemApp":false,"priority":0,"result":"D02","taskType":"02"}]},"myVersionName":"1.0.08.20250814.DSPREAD.MDM.SERVICE","terminalDate":"20250814183636"}
2025-08-14 18:36:36.639 19136-19425 WebSocket               com.dspread.mdm.service              I  🔧 规则结果实时上送: ruleId=83d4a14ed05348efa59fbc626e5d8b19, result=R02 (1)
2025-08-14 18:36:36.644 19136-19425 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 上报规则执行中状态: 83d4a14ed05348efa59fbc626e5d8b19, 应用数量: 2
2025-08-14 18:36:36.648 19136-19425 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 注册安装回调: plus.H52FFB9A5 (规则ID: 83d4a14ed05348efa59fbc626e5d8b19)
2025-08-14 18:36:36.664 19136-19425 PackageParser           com.dspread.mdm.service              W  Unknown element under <manifest>: queries at /data/user/0/com.dspread.mdm.service/files/downloads/rule_83d4a14ed05348efa59fbc626e5d8b19_plus.H52FFB9A5.apk Binary XML file line #14
2025-08-14 18:36:36.678 19136-19425 AppManager              com.dspread.mdm.service              I  ℹ️ 准备安装: plus.H52FFB9A5
2025-08-14 18:36:36.694 19136-19425 PackageParser           com.dspread.mdm.service              W  Unknown element under <manifest>: queries at /data/user/0/com.dspread.mdm.service/files/downloads/rule_83d4a14ed05348efa59fbc626e5d8b19_plus.H52FFB9A5.apk Binary XML file line #14
2025-08-14 18:36:36.709 19136-19425 AppManager              com.dspread.mdm.service              I  ℹ️ APK验证通过: plus.H52FFB9A5 v1.0(100) 4659KB
2025-08-14 18:36:36.717 19136-19425 AppManager              com.dspread.mdm.service              I  ℹ️ 创建Session: sessionId=1701995935
2025-08-14 18:36:36.763 19136-19207 Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"request_time":"1755167795679","org_request_time":"1755167796523","org_request_id":"1755167796523C0107","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755167795679S0000","serialNo":"01354090202503050399"}
2025-08-14 18:36:36.771 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 收到响应消息: transCode=S0000, orgRequestId=1755167796523C0107, state=0, remark=
2025-08-14 18:36:36.776 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 处理规则结果响应: state=0
2025-08-14 18:36:36.781 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 规则结果上传成功
2025-08-14 18:36:36.920 19136-19425 AppManager              com.dspread.mdm.service              I  ℹ️ PackageInstaller installation committed: sessionId=1701995935
2025-08-14 18:36:36.980 19136-19136 AppManager              com.dspread.mdm.service              I  ℹ️ 安装进度: 90%
2025-08-14 18:36:37.377 19136-19207 Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"request_time":"1755167795877","org_request_time":"1755167796598","org_request_id":"1755167796598C0107","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755167795877S0000","serialNo":"01354090202503050399"}
2025-08-14 18:36:37.386 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 收到响应消息: transCode=S0000, orgRequestId=1755167796598C0107, state=0, remark=
2025-08-14 18:36:37.391 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 处理规则结果响应: state=0
2025-08-14 18:36:37.396 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 规则结果上传成功
2025-08-14 18:36:38.378 19136-19136 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 收到广播: android.intent.action.PACKAGE_ADDED
2025-08-14 18:36:38.384 19136-19136 Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 收到包更新广播: android.intent.action.PACKAGE_ADDED, 包名: plus.H52FFB9A5
2025-08-14 18:36:38.395 19136-19136 Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 应用安装: plus.H52FFB9A5
2025-08-14 18:36:38.400 19136-19136 Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 检查WebSocket状态: packageName=plus.H52FFB9A5, action=INSTALL
2025-08-14 18:36:38.406 19136-19136 Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 通知rulebase处理应用安装: plus.H52FFB9A5
2025-08-14 18:36:38.415 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 收到系统广播，应用已安装: plus.H52FFB9A5
2025-08-14 18:36:38.421 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 找到匹配的安装回调，触发: plus.H52FFB9A5
2025-08-14 18:36:38.433 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 安装回调被触发: pkg=plus.H52FFB9A5, returnCode=1, error=
2025-08-14 18:36:38.439 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 应用安装启动成功: plus.H52FFB9A5
2025-08-14 18:36:38.446 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 从广播更新应用状态: 83d4a14ed05348efa59fbc626e5d8b19, plus.H52FFB9A5 -> B03
2025-08-14 18:36:38.453 19136-19136 WebSocket               com.dspread.mdm.service              I  🔧 C0107 主动式上送: rulebase_update (主动: 36)
2025-08-14 18:36:38.482 19136-19136 WebSocket               com.dspread.mdm.service              I  🔧 添加规则结果: ruleId=83d4a14ed05348efa59fbc626e5d8b19
2025-08-14 18:36:38.519 19136-19136 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0107","serialNo":"01354090202503050399","request_time":"1755167798474","request_id":"1755167798474C0107","version":"1","data":{"ruleId":"83d4a14ed05348efa59fbc626e5d8b19","taskResult":"R02","failedApkList":[{"packName":"plus.H52FFB9A5","apkName":"逍遥助手","versionName":"1.0","versionCode":"100","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/203831945bb84d42b21decf1bf936815.apk","apkMd5":"ba1ee533924eae5c408465e7cddcbda4","apkSize":4771451,"installBy":"0","isSystemApp":false,"priority":0,"result":"B03","taskType":"01"},{"packName":"mark.via","apkName":"Via","versionName":"6.6.0","versionCode":"20250713","apkUrl":"","apkMd5":"","apkSize":0,"installBy":"0","isSystemApp":false,"priority":0,"result":"D02","taskType":"02"}]},"myVersionName":"1.0.08.20250814.DSPREAD.MDM.SERVICE","terminalDate":"20250814183638"}
2025-08-14 18:36:38.526 19136-19136 WebSocket               com.dspread.mdm.service              I  🔧 规则结果实时上送: ruleId=83d4a14ed05348efa59fbc626e5d8b19, result=R02 (1)
2025-08-14 18:36:38.534 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 上报规则执行中状态: 83d4a14ed05348efa59fbc626e5d8b19, 应用数量: 2
2025-08-14 18:36:38.541 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 开始检查规则完成状态: 83d4a14ed05348efa59fbc626e5d8b19, 总应用数: 2
2025-08-14 18:36:38.547 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 检查应用状态: plus.H52FFB9A5 -> B03
2025-08-14 18:36:38.552 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 发现成功状态: plus.H52FFB9A5 -> B03
2025-08-14 18:36:38.558 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 检查应用状态: mark.via -> D02
2025-08-14 18:36:38.563 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 发现成功状态: mark.via -> D02
2025-08-14 18:36:38.569 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 规则完成检查结果: 83d4a14ed05348efa59fbc626e5d8b19, 全部完成: true, 完成数: 2/2, 有失败: false
2025-08-14 18:36:38.575 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 规则执行全部成功，上送RULEBASED_SUCCESS: 83d4a14ed05348efa59fbc626e5d8b19
2025-08-14 18:36:38.580 19136-19136 WebSocket               com.dspread.mdm.service              I  🔧 C0107 主动式上送: rulebase_update (主动: 37)
2025-08-14 18:36:38.615 19136-19136 WebSocket               com.dspread.mdm.service              I  🔧 添加规则结果: ruleId=83d4a14ed05348efa59fbc626e5d8b19
2025-08-14 18:36:38.653 19136-19136 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0107","serialNo":"01354090202503050399","request_time":"1755167798607","request_id":"1755167798607C0107","version":"1","data":{"ruleId":"83d4a14ed05348efa59fbc626e5d8b19","taskResult":"R03","failedApkList":[{"packName":"plus.H52FFB9A5","apkName":"逍遥助手","versionName":"1.0","versionCode":"100","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/203831945bb84d42b21decf1bf936815.apk","apkMd5":"ba1ee533924eae5c408465e7cddcbda4","apkSize":4771451,"installBy":"0","isSystemApp":false,"priority":0,"result":"B03","taskType":"01"},{"packName":"mark.via","apkName":"Via","versionName":"6.6.0","versionCode":"20250713","apkUrl":"","apkMd5":"","apkSize":0,"installBy":"0","isSystemApp":false,"priority":0,"result":"D02","taskType":"02"}]},"myVersionName":"1.0.08.20250814.DSPREAD.MDM.SERVICE","terminalDate":"20250814183638"}
2025-08-14 18:36:38.658 19136-19136 WebSocket               com.dspread.mdm.service              I  🔧 规则结果实时上送: ruleId=83d4a14ed05348efa59fbc626e5d8b19, result=R03 (1)
2025-08-14 18:36:38.663 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 上报规则状态: 83d4a14ed05348efa59fbc626e5d8b19 -> R03
2025-08-14 18:36:38.669 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleStateMonitor 收到状态变化: 83d4a14ed05348efa59fbc626e5d8b19, R02 -> R03
2025-08-14 18:36:38.674 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleStateMonitor 状态变化已记录: 83d4a14ed05348efa59fbc626e5d8b19, R03
2025-08-14 18:36:38.679 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleStateMachine 状态转换成功: 83d4a14ed05348efa59fbc626e5d8b19, R02 -> R03
2025-08-14 18:36:38.684 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 规则执行完成: 83d4a14ed05348efa59fbc626e5d8b19, 有失败: false
2025-08-14 18:36:38.690 19136-19136 Platform                com.dspread.mdm.service              D  🔧 重新收集应用信息（强制刷新）
2025-08-14 18:36:38.715 19136-19207 Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"request_time":"1755167797661","org_request_time":"1755167798474","org_request_id":"1755167798474C0107","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755167797661S0000","serialNo":"01354090202503050399"}
2025-08-14 18:36:38.736 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 收到响应消息: transCode=S0000, orgRequestId=1755167798474C0107, state=0, remark=
2025-08-14 18:36:38.744 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 处理规则结果响应: state=0
2025-08-14 18:36:38.751 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 规则结果上传成功
2025-08-14 18:36:38.801 19136-19136 AppManager              com.dspread.mdm.service              I  ℹ️ 应用信息获取完成: 总数132(系统129/用户3) 返回3个
2025-08-14 18:36:38.823 19136-19136 Platform                com.dspread.mdm.service              D  🔧 应用信息: 3 个应用
2025-08-14 18:36:38.828 19136-19136 Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 应用信息缓存刷新完成
2025-08-14 18:36:38.831 19136-19207 Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"request_time":"1755167797768","org_request_time":"1755167798607","org_request_id":"1755167798607C0107","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755167797768S0000","serialNo":"01354090202503050399"}
2025-08-14 18:36:38.833 19136-19136 ContextImpl             com.dspread.mdm.service              W  Calling a method in the system process without a qualified user: android.app.ContextImpl.sendBroadcast:1220 android.content.ContextWrapper.sendBroadcast:504 com.dspread.mdm.service.broadcast.core.BroadcastSender.sendBroadcast:99 com.dspread.mdm.service.broadcast.core.BroadcastSender.sendBroadcast:23 com.dspread.mdm.service.broadcast.handlers.system.PackageUpdateEventHandlerImpl.sendAppRefreshBroadcast:491 
2025-08-14 18:36:38.835 19136-19136 Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 发送应用刷新广播
2025-08-14 18:36:38.839 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 收到响应消息: transCode=S0000, orgRequestId=1755167798607C0107, state=0, remark=
2025-08-14 18:36:38.843 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 处理规则结果响应: state=0
2025-08-14 18:36:38.848 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 规则结果上传成功
2025-08-14 18:36:42.102 19136-19208 WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 7
2025-08-14 18:36:42.198 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 收到服务器 PONG 帧: 7
2025-08-14 18:36:42.311 19136-19136 Common                  com.dspread.mdm.service              I  ✅ ⏰ 设置任务执行定时器成功，下次执行: 60秒后 (1分钟)
