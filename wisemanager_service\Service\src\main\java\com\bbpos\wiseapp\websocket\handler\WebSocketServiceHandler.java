package com.bbpos.wiseapp.websocket.handler;

import android.content.Context;
import android.util.Log;

import com.bbpos.wiseapp.service.common.UsualData;
import com.bbpos.wiseapp.tms.utils.Constants;
import com.bbpos.wiseapp.tms.utils.ContextUtil;
import com.bbpos.wiseapp.tms.utils.Helpers;
import com.bbpos.wiseapp.tms.utils.ParameterName;
import com.bbpos.wiseapp.tms.utils.RuleStatus;
import com.bbpos.wiseapp.tms.utils.WebSocketServiceListManager;
import com.bbpos.wiseapp.tms.utils.WebSocketTaskListManager;
import com.bbpos.wiseapp.websocket.RulebasedService;
import com.bbpos.wiseapp.websocket.WebSocketManager;
import com.bbpos.wiseapp.websocket.WebSocketSender;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

public class WebSocketServiceHandler {
    private WebSocketManager webSocketManager;
    private Context mContext;

    public WebSocketServiceHandler(Context context, WebSocketManager manager) {
        mContext = context;
        webSocketManager = manager;
    }

    public void handleMsg(String response) {
        String tranCode = "";
        JSONObject jsonObject = null;
        try {
            //判断是否有任务列表
            JSONArray serviceList = null;
            JSONObject responseJson = new JSONObject(response);
            JSONObject responseData = Helpers.getDataFromResponse(responseJson);
            if (responseData == null) {
                return;
            }
            if (responseData.has(ParameterName.serviceList)) {
                serviceList = responseData.getJSONArray(ParameterName.serviceList);
                JSONObject serviceTemp = serviceList.getJSONObject(0);

                String requestId = responseJson.optString(ParameterName.request_id);
                String requestTime = responseJson.optString(ParameterName.request_time);

                WebSocketServiceListManager.updateServiceList(requestId, requestTime, serviceList, serviceTemp.optString(ParameterName.beginDate), serviceTemp.optString(ParameterName.endDate), RuleStatus.READY);
//                for (int i=0; i<serviceList.length(); i++) {
//                    JSONObject serviceJson = serviceList.getJSONObject(i);
//                    RulebasedHandler.executeService(serviceJson);
//                }
            }
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }
}
