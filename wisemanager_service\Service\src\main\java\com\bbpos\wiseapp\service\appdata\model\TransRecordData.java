package com.bbpos.wiseapp.service.appdata.model;

import android.database.Cursor;

/**
 * payment 交易信息
 */
public class TransRecordData extends BaseDataModel{
	public static final String ID = "id";
	public static final String TRAN_APP_PKG = "packageName"; // 交易app包名
	public static final String PAYMENT_DATA_MD5 = "paymentDataMD5"; // 交易数据MD5值
	public static final String PAYMENT_DATA = "paymentData"; // 交易数据

	public String packageName;
	public String paymentDataMD5;
	public String paymentData;;

	public TransRecordData(Cursor cursor) {
		this.packageName = getCursorString(cursor,TRAN_APP_PKG);
		this.paymentDataMD5 = getCursorString(cursor,PAYMENT_DATA_MD5);
		this.paymentData = getCursorString(cursor, PAYMENT_DATA);
	}

	public TransRecordData(String packageName, String paymentDataMD5, String paymentData) {
		super();
		this.packageName = packageName;
		this.paymentDataMD5 = paymentDataMD5;
		this.paymentData = paymentData;
	}
}
