package com.dspread.mdm.service.platform.api

import android.content.Context
import android.os.Environment
import android.os.StatFs
import com.dspread.mdm.service.utils.log.Logger
import java.io.BufferedReader
import java.io.FileReader
import java.util.*
import java.util.regex.Pattern

/**
 * 系统API
 * 提供CPU使用率检测、存储信息获取等系统级功能
 *
 * 使用单例模式，避免重复实例化和资源浪费
 */
class SystemApi(private val context: Context) {

    companion object {
        private const val TAG = "SystemApi"

        @Volatile
        private var INSTANCE: SystemApi? = null

        /**
         * 获取单例实例
         */
        private fun getInstance(context: Context): SystemApi {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: SystemApi(context.applicationContext).also {
                    INSTANCE = it
                    Logger.platform("SystemApi 单例实例已创建")
                }
            }
        }

        // ==================== 静态方法接口 ====================

        /**
         * 获取CPU使用率
         */
        fun getCPUUsageRate(context: Context): String {
            return getInstance(context).getCPUUsageRate()
        }

        /**
         * 获取内存使用率
         */
        fun getMemoryUsageRate(context: Context): String {
            return getInstance(context).getMemoryUsageRate()
        }

        /**
         * 获取存储使用率
         */
        fun getStorageUsageRate(context: Context): String {
            return getInstance(context).getStorageUsageRate()
        }
    }

    /**
     * 获取CPU使用率
     * getCPURateDesc_All()
     */
    fun getCPUUsageRate(): String {
        val path = "/proc/stat" // 系统CPU信息文件
        val totalJiffies = LongArray(2)
        val totalIdle = LongArray(2)
        var firstCPUNum = 0 // 防止两次读取文件获知的CPU数量不同，导致不能计算
        
        val pattern = Pattern.compile(" [0-9]+")
        
        try {
            for (i in 0..1) {
                totalJiffies[i] = 0
                totalIdle[i] = 0
                var bufferedReader: BufferedReader? = null
                
                try {
                    val fileReader = FileReader(path)
                    bufferedReader = BufferedReader(fileReader, 8192)
                    
                    var currentCPUNum = 0
                    var str: String?
                    
                    while (bufferedReader.readLine().also { str = it } != null && 
                           (i == 0 || currentCPUNum < firstCPUNum)) {
                        if (str?.lowercase()?.startsWith("cpu") == true) {
                            currentCPUNum++
                            var index = 0
                            val matcher = pattern.matcher(str)
                            
                            while (matcher.find()) {
                                try {
                                    val num = str!!.substring(matcher.start() + 1, matcher.end()).toLong()
                                    totalJiffies[i] += num
                                    if (index == 3) { // idle time
                                        totalIdle[i] += num
                                    }
                                    index++
                                } catch (e: NumberFormatException) {
                                    Logger.platformE("解析CPU数据失败", e)
                                }
                            }
                        }
                        
                        if (i == 0) {
                            firstCPUNum = currentCPUNum
                            try {
                                Thread.sleep(360) // 等待一段时间再次读取
                            } catch (e: InterruptedException) {
                                Thread.currentThread().interrupt()
                            }
                        }
                    }
                } finally {
                    bufferedReader?.close()
                }
            }
            
            // 计算CPU使用率
            val rate = if (totalJiffies[1] - totalJiffies[0] == 0L) {
                0.0
            } else {
                100.0 * (1.0 - (totalIdle[1] - totalIdle[0]).toDouble() / (totalJiffies[1] - totalJiffies[0]).toDouble())
            }
            
            return String.format(Locale.getDefault(), "%.1f%%", rate)
            
        } catch (e: Exception) {
            Logger.platformE("获取CPU使用率失败", e)
            return "0.0%"
        }
    }

    /**
     * 获取内存使用率
     */
    fun getMemoryUsageRate(): String {
        return try {
            val runtime = Runtime.getRuntime()
            val totalMemory = runtime.totalMemory()
            val freeMemory = runtime.freeMemory()
            val usedMemory = totalMemory - freeMemory
            val usageRate = (usedMemory.toDouble() / totalMemory * 100)
            
            String.format(Locale.getDefault(), "%.1f%%", usageRate)
        } catch (e: Exception) {
            Logger.platformE("获取内存使用率失败", e)
            "0.0%"
        }
    }

    /**
     * 获取存储空间使用率
     */
    fun getStorageUsageRate(): String {
        return try {
            val stat = StatFs(Environment.getDataDirectory().path)
            val totalBytes = stat.totalBytes
            val availableBytes = stat.availableBytes
            val usedBytes = totalBytes - availableBytes
            val usageRate = (usedBytes.toDouble() / totalBytes * 100)
            
            String.format(Locale.getDefault(), "%.1f%%", usageRate)
        } catch (e: Exception) {
            Logger.platformE("获取存储使用率失败", e)
            "0.0%"
        }
    }

    /**
     * 获取系统负载信息
     */
    fun getSystemLoadInfo(): Map<String, String> {
        return try {
            mapOf(
                "cpuUsage" to getCPUUsageRate(),
                "memoryUsage" to getMemoryUsageRate(),
                "storageUsage" to getStorageUsageRate(),
                "timestamp" to System.currentTimeMillis().toString()
            )
        } catch (e: Exception) {
            Logger.platformE("获取系统负载信息失败", e)
            mapOf(
                "error" to (e.message ?: "Unknown error"),
                "timestamp" to System.currentTimeMillis().toString()
            )
        }
    }

    /**
     * 获取系统运行时间
     */
    fun getSystemUptime(): Long {
        return try {
            val uptimeFile = "/proc/uptime"
            val reader = BufferedReader(FileReader(uptimeFile))
            val line = reader.readLine()
            reader.close()
            
            val uptimeSeconds = line.split(" ")[0].toDouble()
            (uptimeSeconds * 1000).toLong() // 转换为毫秒
        } catch (e: Exception) {
            Logger.platformE("获取系统运行时间失败", e)
            0L
        }
    }

    /**
     * 获取系统详细信息
     */
    fun getSystemDetails(): Map<String, Any> {
        return try {
            val runtime = Runtime.getRuntime()
            mapOf(
                "cpuUsage" to getCPUUsageRate(),
                "memoryUsage" to getMemoryUsageRate(),
                "storageUsage" to getStorageUsageRate(),
                "uptime" to getSystemUptime(),
                "totalMemory" to runtime.totalMemory(),
                "freeMemory" to runtime.freeMemory(),
                "maxMemory" to runtime.maxMemory(),
                "availableProcessors" to runtime.availableProcessors(),
                "javaVersion" to System.getProperty("java.version"),
                "osName" to System.getProperty("os.name"),
                "osVersion" to System.getProperty("os.version"),
                "timestamp" to System.currentTimeMillis()
            )
        } catch (e: Exception) {
            Logger.platformE("获取系统详细信息失败", e)
            mapOf(
                "error" to (e.message ?: "Unknown error"),
                "timestamp" to System.currentTimeMillis()
            )
        }
    }

    /**
     * 检查系统资源是否充足
     */
    fun isSystemResourceSufficient(): Boolean {
        return try {
            val cpuUsage = getCPUUsageRate().replace("%", "").toDoubleOrNull() ?: 100.0
            val memoryUsage = getMemoryUsageRate().replace("%", "").toDoubleOrNull() ?: 100.0
            val storageUsage = getStorageUsageRate().replace("%", "").toDoubleOrNull() ?: 100.0
            
            // 如果CPU、内存、存储使用率都低于80%，认为资源充足
            cpuUsage < 80.0 && memoryUsage < 80.0 && storageUsage < 80.0
        } catch (e: Exception) {
            Logger.platformE("检查系统资源状态失败", e)
            false
        }
    }

    /**
     * 获取系统资源警告信息
     */
    fun getSystemResourceWarnings(): List<String> {
        val warnings = mutableListOf<String>()
        
        try {
            val cpuUsage = getCPUUsageRate().replace("%", "").toDoubleOrNull() ?: 0.0
            val memoryUsage = getMemoryUsageRate().replace("%", "").toDoubleOrNull() ?: 0.0
            val storageUsage = getStorageUsageRate().replace("%", "").toDoubleOrNull() ?: 0.0
            
            if (cpuUsage > 90.0) {
                warnings.add("CPU使用率过高: $cpuUsage%")
            }
            if (memoryUsage > 90.0) {
                warnings.add("内存使用率过高: $memoryUsage%")
            }
            if (storageUsage > 90.0) {
                warnings.add("存储空间使用率过高: $storageUsage%")
            }
            
        } catch (e: Exception) {
            Logger.platformE("获取系统资源警告失败", e)
            warnings.add("无法获取系统资源状态")
        }
        
        return warnings
    }
}
