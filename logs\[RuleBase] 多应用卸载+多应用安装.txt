2025-08-14 18:37:38.500 19136-19207 Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"msg_state":"P","request_time":"1755167857144","data":{"ruleList":[{"deleteAppList":[{"apkName":"cl960","appId":"f32ac615171249418e3fc4b5bc2f4773","packName":"com.chileaf.cl960.sample","versionName":"1.1.0","versionCode":"1"},{"apkName":"逍遥助手","appId":"225a0a30994e45089dad1a270f8d85a1","packName":"plus.H52FFB9A5","versionName":"1.0","versionCode":"100"}],"modifyDate":"2025-08-14 10:37:37","endDate":"9999-12-31 23:59:59","restartAfter":"0","installBy":"0","beginDate":"2024-08-14 10:37:37","appList":[{"apkMd5":"0d1225260d03e10a8ffc8409369c442a","apkName":"Via","appId":"353795033bc0410dac6dee5cb8702b06","apkSize":"2565110","appIconUrl":"https://siot-bucket01.s3.ap-northeast-1.amazonaws.com/icon/88cceb7e7cae4b8792791b5eb9d04c47ic_launcher.png","packName":"mark.via","versionName":"6.6.0","versionCode":"20250713","url":"https://siot-bucket01.s3.ap-northeast-1.amazonaws.com/app/4d256c4123b346fd817537b491342204.apk"},{"apkMd5":"accb937c84dcd0d346da7f8fee7bf0ae","apkName":"懒猫趣播","appId":"8a6d45b6779244d59837eb1d83f882c9","apkSize":"13140733","appIconUrl":"https://siot-bucket01.s3.ap-northeast-1.amazonaws.com/icon/c7cc24f7b04744acb72d7dc17a8074edic_launcher.png","packName":"com.lingjingnet.lazycat","versionName":"1.0.1","versionCode":"10","url":"https://siot-bucket01.s3.ap-northeast-1.amazonaws.com/app/f03e70448bf9461abf1804e9da65b36f.apk"}],"serviceList":[],"action":"A","ruleName":"d60_test_2991","ruleId":"168fee4bca5148a0be863080acad09ee","createDate":"2025-08-14 10:37:37","orgRuleId":""}]},"tranCode":"ST005","request_id":"1755167857144ST005","version":"1","serialNo":"01354090202503050399"}
2025-08-14 18:37:38.511 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 准备发送响应确认 - requestId: 1755167857144ST005, needResponse: true
2025-08-14 18:37:38.536 19136-19207 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0000","serialNo":"01354090202503050399","request_time":"1755167858519","request_id":"1755167858519C0000","version":"1","org_request_id":"1755167857144ST005","org_request_time":"1755167857144","response_state":"0","myVersionName":"1.0.08.20250814.DSPREAD.MDM.SERVICE","terminalDate":"20250814183738"}
2025-08-14 18:37:38.559 19136-19207 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0000","serialNo":"01354090202503050399","request_time":"1755167858545","request_id":"1755167858545C0000","version":"1","org_request_id":"1755167857144ST005","org_request_time":"1755167857144","response_state":"0","myVersionName":"1.0.08.20250814.DSPREAD.MDM.SERVICE","terminalDate":"20250814183738"}
2025-08-14 18:37:38.563 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 已发送C0000响应确认: requestId=1755167857144ST005
2025-08-14 18:37:38.573 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleHandler 处理规则消息: ST005
2025-08-14 18:37:38.577 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleHandler 处理规则列表，数量: 1
2025-08-14 18:37:38.582 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleHandler 处理规则: ruleId=168fee4bca5148a0be863080acad09ee, action=A
2025-08-14 18:37:38.587 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析规则JSON: ruleId=168fee4bca5148a0be863080acad09ee
2025-08-14 18:37:38.602 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule appList数组: [{"apkMd5":"0d1225260d03e10a8ffc8409369c442a","apkName":"Via","appId":"353795033bc0410dac6dee5cb8702b06","apkSize":"2565110","appIconUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/icon\/88cceb7e7cae4b8792791b5eb9d04c47ic_launcher.png","packName":"mark.via","versionName":"6.6.0","versionCode":"20250713","url":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/4d256c4123b346fd817537b491342204.apk"},{"apkMd5":"accb937c84dcd0d346da7f8fee7bf0ae","apkName":"懒猫趣播","appId":"8a6d45b6779244d59837eb1d83f882c9","apkSize":"13140733","appIconUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/icon\/c7cc24f7b04744acb72d7dc17a8074edic_launcher.png","packName":"com.lingjingnet.lazycat","versionName":"1.0.1","versionCode":"10","url":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/f03e70448bf9461abf1804e9da65b36f.apk"}]
2025-08-14 18:37:38.611 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule deleteAppList数组: [{"apkName":"cl960","appId":"f32ac615171249418e3fc4b5bc2f4773","packName":"com.chileaf.cl960.sample","versionName":"1.1.0","versionCode":"1"},{"apkName":"逍遥助手","appId":"225a0a30994e45089dad1a270f8d85a1","packName":"plus.H52FFB9A5","versionName":"1.0","versionCode":"100"}]
2025-08-14 18:37:38.615 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=mark.via, apkName=Via
2025-08-14 18:37:38.620 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=com.lingjingnet.lazycat, apkName=懒猫趣播
2025-08-14 18:37:38.625 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=com.chileaf.cl960.sample, apkName=cl960
2025-08-14 18:37:38.629 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=plus.H52FFB9A5, apkName=逍遥助手
2025-08-14 18:37:38.634 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后appList数量: 2
2025-08-14 18:37:38.638 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后deleteAppList数量: 2
2025-08-14 18:37:38.643 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleBaseStorage 处理规则: 168fee4bca5148a0be863080acad09ee, 操作: A
2025-08-14 18:37:38.648 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 验证规则: ruleId=168fee4bca5148a0be863080acad09ee, ruleType=app_management, action=A
2025-08-14 18:37:38.652 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule appList数量: 2, deleteAppList数量: 2
2025-08-14 18:37:38.673 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 验证应用管理规则: action=A
2025-08-14 18:37:38.677 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 验证应用列表: appList=2, deleteAppList=2
2025-08-14 18:37:38.684 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 应用列表验证通过
2025-08-14 18:37:38.707 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析规则JSON: ruleId=937eccc7e4ff4fc0bc9eaf2788685515
2025-08-14 18:37:38.716 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule appList数组: [{"packName":"mark.via","apkName":"Via","versionName":"6.6.0","versionCode":"20250713","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/4d256c4123b346fd817537b491342204.apk","apkMd5":"0d1225260d03e10a8ffc8409369c442a","apkSize":2565110,"installBy":"0","isSystemApp":false,"priority":0}]
2025-08-14 18:37:38.720 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule deleteAppList数组: []
2025-08-14 18:37:38.725 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=mark.via, apkName=Via
2025-08-14 18:37:38.729 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后appList数量: 1
2025-08-14 18:37:38.734 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后deleteAppList数量: 0
2025-08-14 18:37:38.739 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析规则JSON: ruleId=01f42138486940dd8ba8a136d84e239d
2025-08-14 18:37:38.743 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule appList数组: []
2025-08-14 18:37:38.750 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule deleteAppList数组: [{"packName":"mark.via","apkName":"Via","versionName":"6.6.0","versionCode":"20250713","apkUrl":"","apkMd5":"","apkSize":0,"installBy":"0","isSystemApp":false,"priority":0}]
2025-08-14 18:37:38.755 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=mark.via, apkName=Via
2025-08-14 18:37:38.759 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后appList数量: 0
2025-08-14 18:37:38.764 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后deleteAppList数量: 1
2025-08-14 18:37:38.769 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析规则JSON: ruleId=e195e52efa414316818a8d4b21fa4e9c
2025-08-14 18:37:38.773 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule appList数组: []
2025-08-14 18:37:38.783 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule deleteAppList数组: [{"packName":"com.chileaf.cl960.sample","apkName":"cl960","versionName":"1.1.0","versionCode":"1","apkUrl":"","apkMd5":"","apkSize":0,"installBy":"0","isSystemApp":false,"priority":0},{"packName":"plus.H52FFB9A5","apkName":"逍遥助手","versionName":"1.0","versionCode":"100","apkUrl":"","apkMd5":"","apkSize":0,"installBy":"0","isSystemApp":false,"priority":0}]
2025-08-14 18:37:38.788 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=com.chileaf.cl960.sample, apkName=cl960
2025-08-14 18:37:38.793 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=plus.H52FFB9A5, apkName=逍遥助手
2025-08-14 18:37:38.797 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后appList数量: 0
2025-08-14 18:37:38.802 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后deleteAppList数量: 2
2025-08-14 18:37:38.806 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析规则JSON: ruleId=6eb6f82614c74c3bb1f5ca0e32ef787e
2025-08-14 18:37:38.818 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule appList数组: [{"packName":"mark.via","apkName":"Via","versionName":"6.6.0","versionCode":"20250713","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/4d256c4123b346fd817537b491342204.apk","apkMd5":"0d1225260d03e10a8ffc8409369c442a","apkSize":2565110,"installBy":"0","isSystemApp":false,"priority":0},{"packName":"com.chileaf.cl960.sample","apkName":"cl960","versionName":"1.1.0","versionCode":"1","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/42e16b87eaa849bcaaf0f5fadcac6aab.apk","apkMd5":"54cc3a371939423d4288d63e1ac88019","apkSize":6155416,"installBy":"0","isSystemApp":false,"priority":0}]
2025-08-14 18:37:38.823 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule deleteAppList数组: []
2025-08-14 18:37:38.827 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=mark.via, apkName=Via
2025-08-14 18:37:38.832 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=com.chileaf.cl960.sample, apkName=cl960
2025-08-14 18:37:38.836 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后appList数量: 2
2025-08-14 18:37:38.841 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后deleteAppList数量: 0
2025-08-14 18:37:38.846 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析规则JSON: ruleId=83d4a14ed05348efa59fbc626e5d8b19
2025-08-14 18:37:38.854 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule appList数组: [{"packName":"plus.H52FFB9A5","apkName":"逍遥助手","versionName":"1.0","versionCode":"100","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/203831945bb84d42b21decf1bf936815.apk","apkMd5":"ba1ee533924eae5c408465e7cddcbda4","apkSize":4771451,"installBy":"0","isSystemApp":false,"priority":0}]
2025-08-14 18:37:38.861 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule deleteAppList数组: [{"packName":"mark.via","apkName":"Via","versionName":"6.6.0","versionCode":"20250713","apkUrl":"","apkMd5":"","apkSize":0,"installBy":"0","isSystemApp":false,"priority":0}]
2025-08-14 18:37:38.866 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=plus.H52FFB9A5, apkName=逍遥助手
2025-08-14 18:37:38.871 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=mark.via, apkName=Via
2025-08-14 18:37:38.875 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后appList数量: 1
2025-08-14 18:37:38.880 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后deleteAppList数量: 1
2025-08-14 18:37:38.903 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析规则JSON: ruleId=937eccc7e4ff4fc0bc9eaf2788685515
2025-08-14 18:37:38.912 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule appList数组: [{"packName":"mark.via","apkName":"Via","versionName":"6.6.0","versionCode":"20250713","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/4d256c4123b346fd817537b491342204.apk","apkMd5":"0d1225260d03e10a8ffc8409369c442a","apkSize":2565110,"installBy":"0","isSystemApp":false,"priority":0}]
2025-08-14 18:37:38.916 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule deleteAppList数组: []
2025-08-14 18:37:38.921 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=mark.via, apkName=Via
2025-08-14 18:37:38.925 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后appList数量: 1
2025-08-14 18:37:38.930 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后deleteAppList数量: 0
2025-08-14 18:37:38.935 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析规则JSON: ruleId=01f42138486940dd8ba8a136d84e239d
2025-08-14 18:37:38.939 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule appList数组: []
2025-08-14 18:37:38.946 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule deleteAppList数组: [{"packName":"mark.via","apkName":"Via","versionName":"6.6.0","versionCode":"20250713","apkUrl":"","apkMd5":"","apkSize":0,"installBy":"0","isSystemApp":false,"priority":0}]
2025-08-14 18:37:38.951 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=mark.via, apkName=Via
2025-08-14 18:37:38.955 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后appList数量: 0
2025-08-14 18:37:38.960 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后deleteAppList数量: 1
2025-08-14 18:37:38.965 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析规则JSON: ruleId=e195e52efa414316818a8d4b21fa4e9c
2025-08-14 18:37:38.969 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule appList数组: []
2025-08-14 18:37:38.979 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule deleteAppList数组: [{"packName":"com.chileaf.cl960.sample","apkName":"cl960","versionName":"1.1.0","versionCode":"1","apkUrl":"","apkMd5":"","apkSize":0,"installBy":"0","isSystemApp":false,"priority":0},{"packName":"plus.H52FFB9A5","apkName":"逍遥助手","versionName":"1.0","versionCode":"100","apkUrl":"","apkMd5":"","apkSize":0,"installBy":"0","isSystemApp":false,"priority":0}]
2025-08-14 18:37:38.984 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=com.chileaf.cl960.sample, apkName=cl960
2025-08-14 18:37:38.989 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=plus.H52FFB9A5, apkName=逍遥助手
2025-08-14 18:37:38.993 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后appList数量: 0
2025-08-14 18:37:38.998 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后deleteAppList数量: 2
2025-08-14 18:37:39.002 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析规则JSON: ruleId=6eb6f82614c74c3bb1f5ca0e32ef787e
2025-08-14 18:37:39.014 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule appList数组: [{"packName":"mark.via","apkName":"Via","versionName":"6.6.0","versionCode":"20250713","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/4d256c4123b346fd817537b491342204.apk","apkMd5":"0d1225260d03e10a8ffc8409369c442a","apkSize":2565110,"installBy":"0","isSystemApp":false,"priority":0},{"packName":"com.chileaf.cl960.sample","apkName":"cl960","versionName":"1.1.0","versionCode":"1","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/42e16b87eaa849bcaaf0f5fadcac6aab.apk","apkMd5":"54cc3a371939423d4288d63e1ac88019","apkSize":6155416,"installBy":"0","isSystemApp":false,"priority":0}]
2025-08-14 18:37:39.019 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule deleteAppList数组: []
2025-08-14 18:37:39.023 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=mark.via, apkName=Via
2025-08-14 18:37:39.028 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=com.chileaf.cl960.sample, apkName=cl960
2025-08-14 18:37:39.032 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后appList数量: 2
2025-08-14 18:37:39.037 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后deleteAppList数量: 0
2025-08-14 18:37:39.042 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析规则JSON: ruleId=83d4a14ed05348efa59fbc626e5d8b19
2025-08-14 18:37:39.051 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule appList数组: [{"packName":"plus.H52FFB9A5","apkName":"逍遥助手","versionName":"1.0","versionCode":"100","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/203831945bb84d42b21decf1bf936815.apk","apkMd5":"ba1ee533924eae5c408465e7cddcbda4","apkSize":4771451,"installBy":"0","isSystemApp":false,"priority":0}]
2025-08-14 18:37:39.057 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule deleteAppList数组: [{"packName":"mark.via","apkName":"Via","versionName":"6.6.0","versionCode":"20250713","apkUrl":"","apkMd5":"","apkSize":0,"installBy":"0","isSystemApp":false,"priority":0}]
2025-08-14 18:37:39.062 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=plus.H52FFB9A5, apkName=逍遥助手
2025-08-14 18:37:39.067 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=mark.via, apkName=Via
2025-08-14 18:37:39.071 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后appList数量: 1
2025-08-14 18:37:39.076 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后deleteAppList数量: 1
2025-08-14 18:37:39.151 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleBaseStorage 保存 6 个规则到存储
2025-08-14 18:37:39.155 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleBaseStorage Rule 168fee4bca5148a0be863080acad09ee 添加成功
2025-08-14 18:37:39.160 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleBaseStorage 触发规则执行: 168fee4bca5148a0be863080acad09ee
2025-08-14 18:37:39.165 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleHandler 规则处理成功: 168fee4bca5148a0be863080acad09ee
2025-08-14 18:37:39.166 19136-19166 RuleBase                com.dspread.mdm.service              D  🔧 RuleBaseStorage 设备空闲，立即执行规则: 168fee4bca5148a0be863080acad09ee
2025-08-14 18:37:39.170 19136-19166 RuleBase                com.dspread.mdm.service              D  🔧 RuleBaseManager 直接执行规则: 168fee4bca5148a0be863080acad09ee
2025-08-14 18:37:39.170 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleHandler 规则列表处理完成: 处理完成 - 成功: 1, 失败: 0, 忽略: 0
2025-08-14 18:37:39.176 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleHandler 上报规则处理结果: 0 - 处理完成 - 成功: 1, 失败: 0, 忽略: 0
2025-08-14 18:37:39.177 19136-19166 RuleBase                com.dspread.mdm.service              D  🔧 RuleBaseManager 处理规则: 168fee4bca5148a0be863080acad09ee, 操作: A
2025-08-14 18:37:39.181 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 C0107 主动式上送: rulebase_update (主动: 38)
2025-08-14 18:37:39.183 19136-19166 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析规则JSON: ruleId=168fee4bca5148a0be863080acad09ee
2025-08-14 18:37:39.196 19136-19166 RuleBase                com.dspread.mdm.service              D  🔧 Rule appList数组: [{"packName":"mark.via","apkName":"Via","versionName":"6.6.0","versionCode":"20250713","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/4d256c4123b346fd817537b491342204.apk","apkMd5":"0d1225260d03e10a8ffc8409369c442a","apkSize":2565110,"installBy":"0","isSystemApp":false,"priority":0},{"packName":"com.lingjingnet.lazycat","apkName":"懒猫趣播","versionName":"1.0.1","versionCode":"10","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/f03e70448bf9461abf1804e9da65b36f.apk","apkMd5":"accb937c84dcd0d346da7f8fee7bf0ae","apkSize":13140733,"installBy":"0","isSystemApp":false,"priority":0}]
2025-08-14 18:37:39.199 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 添加规则结果: ruleId=batch_rules
2025-08-14 18:37:39.206 19136-19166 RuleBase                com.dspread.mdm.service              D  🔧 Rule deleteAppList数组: [{"packName":"com.chileaf.cl960.sample","apkName":"cl960","versionName":"1.1.0","versionCode":"1","apkUrl":"","apkMd5":"","apkSize":0,"installBy":"0","isSystemApp":false,"priority":0},{"packName":"plus.H52FFB9A5","apkName":"逍遥助手","versionName":"1.0","versionCode":"100","apkUrl":"","apkMd5":"","apkSize":0,"installBy":"0","isSystemApp":false,"priority":0}]
2025-08-14 18:37:39.210 19136-19166 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=mark.via, apkName=Via
2025-08-14 18:37:39.214 19136-19207 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0107","serialNo":"01354090202503050399","request_time":"1755167859194","request_id":"1755167859194C0107","version":"1","data":{"ruleId":"batch_rules","taskResult":"0"},"myVersionName":"1.0.08.20250814.DSPREAD.MDM.SERVICE","terminalDate":"20250814183739","org_request_id":"1755167857144ST005","org_request_time":"1755167857144"}
2025-08-14 18:37:39.215 19136-19166 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=com.lingjingnet.lazycat, apkName=懒猫趣播
2025-08-14 18:37:39.219 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 规则结果实时上送: ruleId=batch_rules, result=0 (1)
2025-08-14 18:37:39.219 19136-19166 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=com.chileaf.cl960.sample, apkName=cl960
2025-08-14 18:37:39.224 19136-19166 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=plus.H52FFB9A5, apkName=逍遥助手
2025-08-14 18:37:39.228 19136-19166 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后appList数量: 2
2025-08-14 18:37:39.233 19136-19166 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后deleteAppList数量: 2
2025-08-14 18:37:39.238 19136-19166 RuleBase                com.dspread.mdm.service              D  🔧 RuleBaseStorage 处理规则: 168fee4bca5148a0be863080acad09ee, 操作: A
2025-08-14 18:37:39.242 19136-19166 RuleBase                com.dspread.mdm.service              D  🔧 Rule 验证规则: ruleId=168fee4bca5148a0be863080acad09ee, ruleType=app_management, action=A
2025-08-14 18:37:39.247 19136-19166 RuleBase                com.dspread.mdm.service              D  🔧 Rule appList数量: 2, deleteAppList数量: 2
2025-08-14 18:37:39.267 19136-19166 RuleBase                com.dspread.mdm.service              D  🔧 Rule 验证应用管理规则: action=A
2025-08-14 18:37:39.272 19136-19166 RuleBase                com.dspread.mdm.service              D  🔧 Rule 验证应用列表: appList=2, deleteAppList=2
2025-08-14 18:37:39.279 19136-19166 RuleBase                com.dspread.mdm.service              D  🔧 Rule 应用列表验证通过
2025-08-14 18:37:39.309 19136-19166 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析规则JSON: ruleId=937eccc7e4ff4fc0bc9eaf2788685515
2025-08-14 18:37:39.317 19136-19166 RuleBase                com.dspread.mdm.service              D  🔧 Rule appList数组: [{"packName":"mark.via","apkName":"Via","versionName":"6.6.0","versionCode":"20250713","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/4d256c4123b346fd817537b491342204.apk","apkMd5":"0d1225260d03e10a8ffc8409369c442a","apkSize":2565110,"installBy":"0","isSystemApp":false,"priority":0}]
2025-08-14 18:37:39.322 19136-19166 RuleBase                com.dspread.mdm.service              D  🔧 Rule deleteAppList数组: []
2025-08-14 18:37:39.326 19136-19166 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=mark.via, apkName=Via
2025-08-14 18:37:39.331 19136-19166 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后appList数量: 1
2025-08-14 18:37:39.335 19136-19166 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后deleteAppList数量: 0
2025-08-14 18:37:39.340 19136-19166 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析规则JSON: ruleId=01f42138486940dd8ba8a136d84e239d
2025-08-14 18:37:39.342 19136-19207 Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"request_time":"1755167858343","org_request_time":"1755167859194","org_request_id":"1755167859194C0107","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755167858343S0000","serialNo":"01354090202503050399"}
2025-08-14 18:37:39.345 19136-19166 RuleBase                com.dspread.mdm.service              D  🔧 Rule appList数组: []
2025-08-14 18:37:39.349 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 收到响应消息: transCode=S0000, orgRequestId=1755167859194C0107, state=0, remark=
2025-08-14 18:37:39.352 19136-19166 RuleBase                com.dspread.mdm.service              D  🔧 Rule deleteAppList数组: [{"packName":"mark.via","apkName":"Via","versionName":"6.6.0","versionCode":"20250713","apkUrl":"","apkMd5":"","apkSize":0,"installBy":"0","isSystemApp":false,"priority":0}]
2025-08-14 18:37:39.354 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 处理规则结果响应: state=0
2025-08-14 18:37:39.356 19136-19166 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=mark.via, apkName=Via
2025-08-14 18:37:39.359 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 规则结果上传成功
2025-08-14 18:37:39.361 19136-19166 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后appList数量: 0
2025-08-14 18:37:39.365 19136-19166 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后deleteAppList数量: 1
2025-08-14 18:37:39.370 19136-19166 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析规则JSON: ruleId=e195e52efa414316818a8d4b21fa4e9c
2025-08-14 18:37:39.375 19136-19166 RuleBase                com.dspread.mdm.service              D  🔧 Rule appList数组: []
2025-08-14 18:37:39.385 19136-19166 RuleBase                com.dspread.mdm.service              D  🔧 Rule deleteAppList数组: [{"packName":"com.chileaf.cl960.sample","apkName":"cl960","versionName":"1.1.0","versionCode":"1","apkUrl":"","apkMd5":"","apkSize":0,"installBy":"0","isSystemApp":false,"priority":0},{"packName":"plus.H52FFB9A5","apkName":"逍遥助手","versionName":"1.0","versionCode":"100","apkUrl":"","apkMd5":"","apkSize":0,"installBy":"0","isSystemApp":false,"priority":0}]
2025-08-14 18:37:39.389 19136-19166 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=com.chileaf.cl960.sample, apkName=cl960
2025-08-14 18:37:39.394 19136-19166 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=plus.H52FFB9A5, apkName=逍遥助手
2025-08-14 18:37:39.399 19136-19166 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后appList数量: 0
2025-08-14 18:37:39.403 19136-19166 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后deleteAppList数量: 2
2025-08-14 18:37:39.408 19136-19166 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析规则JSON: ruleId=6eb6f82614c74c3bb1f5ca0e32ef787e
2025-08-14 18:37:39.420 19136-19166 RuleBase                com.dspread.mdm.service              D  🔧 Rule appList数组: [{"packName":"mark.via","apkName":"Via","versionName":"6.6.0","versionCode":"20250713","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/4d256c4123b346fd817537b491342204.apk","apkMd5":"0d1225260d03e10a8ffc8409369c442a","apkSize":2565110,"installBy":"0","isSystemApp":false,"priority":0},{"packName":"com.chileaf.cl960.sample","apkName":"cl960","versionName":"1.1.0","versionCode":"1","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/42e16b87eaa849bcaaf0f5fadcac6aab.apk","apkMd5":"54cc3a371939423d4288d63e1ac88019","apkSize":6155416,"installBy":"0","isSystemApp":false,"priority":0}]
2025-08-14 18:37:39.424 19136-19166 RuleBase                com.dspread.mdm.service              D  🔧 Rule deleteAppList数组: []
2025-08-14 18:37:39.429 19136-19166 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=mark.via, apkName=Via
2025-08-14 18:37:39.434 19136-19166 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=com.chileaf.cl960.sample, apkName=cl960
2025-08-14 18:37:39.438 19136-19166 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后appList数量: 2
2025-08-14 18:37:39.443 19136-19166 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后deleteAppList数量: 0
2025-08-14 18:37:39.448 19136-19166 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析规则JSON: ruleId=83d4a14ed05348efa59fbc626e5d8b19
2025-08-14 18:37:39.456 19136-19166 RuleBase                com.dspread.mdm.service              D  🔧 Rule appList数组: [{"packName":"plus.H52FFB9A5","apkName":"逍遥助手","versionName":"1.0","versionCode":"100","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/203831945bb84d42b21decf1bf936815.apk","apkMd5":"ba1ee533924eae5c408465e7cddcbda4","apkSize":4771451,"installBy":"0","isSystemApp":false,"priority":0}]
2025-08-14 18:37:39.463 19136-19166 RuleBase                com.dspread.mdm.service              D  🔧 Rule deleteAppList数组: [{"packName":"mark.via","apkName":"Via","versionName":"6.6.0","versionCode":"20250713","apkUrl":"","apkMd5":"","apkSize":0,"installBy":"0","isSystemApp":false,"priority":0}]
2025-08-14 18:37:39.468 19136-19166 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=plus.H52FFB9A5, apkName=逍遥助手
2025-08-14 18:37:39.473 19136-19166 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=mark.via, apkName=Via
2025-08-14 18:37:39.477 19136-19166 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后appList数量: 1
2025-08-14 18:37:39.482 19136-19166 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后deleteAppList数量: 1
2025-08-14 18:37:39.487 19136-19166 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析规则JSON: ruleId=168fee4bca5148a0be863080acad09ee
2025-08-14 18:37:39.500 19136-19166 RuleBase                com.dspread.mdm.service              D  🔧 Rule appList数组: [{"packName":"mark.via","apkName":"Via","versionName":"6.6.0","versionCode":"20250713","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/4d256c4123b346fd817537b491342204.apk","apkMd5":"0d1225260d03e10a8ffc8409369c442a","apkSize":2565110,"installBy":"0","isSystemApp":false,"priority":0},{"packName":"com.lingjingnet.lazycat","apkName":"懒猫趣播","versionName":"1.0.1","versionCode":"10","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/f03e70448bf9461abf1804e9da65b36f.apk","apkMd5":"accb937c84dcd0d346da7f8fee7bf0ae","apkSize":13140733,"installBy":"0","isSystemApp":false,"priority":0}]
2025-08-14 18:37:39.510 19136-19166 RuleBase                com.dspread.mdm.service              D  🔧 Rule deleteAppList数组: [{"packName":"com.chileaf.cl960.sample","apkName":"cl960","versionName":"1.1.0","versionCode":"1","apkUrl":"","apkMd5":"","apkSize":0,"installBy":"0","isSystemApp":false,"priority":0},{"packName":"plus.H52FFB9A5","apkName":"逍遥助手","versionName":"1.0","versionCode":"100","apkUrl":"","apkMd5":"","apkSize":0,"installBy":"0","isSystemApp":false,"priority":0}]
2025-08-14 18:37:39.514 19136-19166 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=mark.via, apkName=Via
2025-08-14 18:37:39.519 19136-19166 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=com.lingjingnet.lazycat, apkName=懒猫趣播
2025-08-14 18:37:39.524 19136-19166 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=com.chileaf.cl960.sample, apkName=cl960
2025-08-14 18:37:39.528 19136-19166 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=plus.H52FFB9A5, apkName=逍遥助手
2025-08-14 18:37:39.533 19136-19166 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后appList数量: 2
2025-08-14 18:37:39.538 19136-19166 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后deleteAppList数量: 2
2025-08-14 18:37:39.542 19136-19166 RuleBase                com.dspread.mdm.service              D  🔧 RuleBaseStorage Rule 168fee4bca5148a0be863080acad09ee 已存在，忽略Add操作
2025-08-14 18:37:39.547 19136-19166 RuleBase                com.dspread.mdm.service              D  🔧 RuleStateMachine 规则初始化成功: 168fee4bca5148a0be863080acad09ee -> RuleState(code=todo, description=等待执行)
2025-08-14 18:37:39.552 19136-19166 RuleBase                com.dspread.mdm.service              D  🔧 RuleStateMachine 规则已存在: 168fee4bca5148a0be863080acad09ee, 当前状态: RuleState(code=todo, description=等待执行)
2025-08-14 18:37:39.557 19136-19166 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 创建应用状态: packName=mark.via, apkName=Via
2025-08-14 18:37:39.562 19136-19166 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 创建应用状态: packName=com.lingjingnet.lazycat, apkName=懒猫趣播
2025-08-14 18:37:39.567 19136-19166 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 初始化规则应用状态: 168fee4bca5148a0be863080acad09ee, 应用数量: 4
2025-08-14 18:37:39.572 19136-19166 RuleBase                com.dspread.mdm.service              D  🔧 RuleStateMonitor 收到状态变化: 168fee4bca5148a0be863080acad09ee, todo -> R01
2025-08-14 18:37:39.576 19136-19166 RuleBase                com.dspread.mdm.service              D  🔧 RuleStateMonitor 状态变化已记录: 168fee4bca5148a0be863080acad09ee, R01
2025-08-14 18:37:39.581 19136-19166 RuleBase                com.dspread.mdm.service              D  🔧 RuleStateMachine 状态转换成功: 168fee4bca5148a0be863080acad09ee, todo -> R01
2025-08-14 18:37:39.585 19136-19166 RuleBase                com.dspread.mdm.service              D  🔧 RuleStateMonitor 收到状态变化: 168fee4bca5148a0be863080acad09ee, R01 -> R02
2025-08-14 18:37:39.590 19136-19166 RuleBase                com.dspread.mdm.service              D  🔧 RuleStateMonitor 状态变化已记录: 168fee4bca5148a0be863080acad09ee, R02
2025-08-14 18:37:39.594 19136-19166 RuleBase                com.dspread.mdm.service              D  🔧 RuleStateMachine 状态转换成功: 168fee4bca5148a0be863080acad09ee, R01 -> R02
2025-08-14 18:37:39.599 19136-19166 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 开始执行规则: 168fee4bca5148a0be863080acad09ee
2025-08-14 18:37:39.603 19136-19166 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 执行卸载应用，数量: 2
2025-08-14 18:37:39.608 19136-19166 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 开始卸载应用，数量: 2
2025-08-14 18:37:39.613 19136-19166 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 卸载应用: com.chileaf.cl960.sample
2025-08-14 18:37:39.619 19136-19166 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 更新应用状态: 168fee4bca5148a0be863080acad09ee, com.chileaf.cl960.sample -> D01
2025-08-14 18:37:39.623 19136-19166 WebSocket               com.dspread.mdm.service              I  🔧 C0107 主动式上送: rulebase_update (主动: 39)
2025-08-14 18:37:39.641 19136-19166 WebSocket               com.dspread.mdm.service              I  🔧 添加规则结果: ruleId=168fee4bca5148a0be863080acad09ee
2025-08-14 18:37:39.686 19136-19166 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0107","serialNo":"01354090202503050399","request_time":"1755167859636","request_id":"1755167859636C0107","version":"1","data":{"ruleId":"168fee4bca5148a0be863080acad09ee","taskResult":"R02","failedApkList":[{"packName":"mark.via","apkName":"Via","versionName":"6.6.0","versionCode":"20250713","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/4d256c4123b346fd817537b491342204.apk","apkMd5":"0d1225260d03e10a8ffc8409369c442a","apkSize":2565110,"installBy":"0","isSystemApp":false,"priority":0,"result":"W01","taskType":"01"},{"packName":"com.lingjingnet.lazycat","apkName":"懒猫趣播","versionName":"1.0.1","versionCode":"10","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/f03e70448bf9461abf1804e9da65b36f.apk","apkMd5":"accb937c84dcd0d346da7f8fee7bf0ae","apkSize":13140733,"installBy":"0","isSystemApp":false,"priority":0,"result":"W01","taskType":"01"},{"packName":"com.chileaf.cl960.sample","apkName":"cl960","versionName":"1.1.0","versionCode":"1","apkUrl":"","apkMd5":"","apkSize":0,"installBy":"0","isSystemApp":false,"priority":0,"result":"D01","taskType":"02"},{"packName":"plus.H52FFB9A5","apkName":"逍遥助手","versionName":"1.0","versionCode":"100","apkUrl":"","apkMd5":"","apkSize":0,"installBy":"0","isSystemApp":false,"priority":0,"result":"W01","taskType":"02"}]},"myVersionName":"1.0.08.20250814.DSPREAD.MDM.SERVICE","terminalDate":"20250814183739"}
2025-08-14 18:37:39.691 19136-19166 WebSocket               com.dspread.mdm.service              I  🔧 规则结果实时上送: ruleId=168fee4bca5148a0be863080acad09ee, result=R02 (1)
2025-08-14 18:37:39.696 19136-19166 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 上报规则执行中状态: 168fee4bca5148a0be863080acad09ee, 应用数量: 4
2025-08-14 18:37:39.700 19136-19166 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 注册卸载回调: com.chileaf.cl960.sample (规则ID: 168fee4bca5148a0be863080acad09ee)
2025-08-14 18:37:39.705 19136-19166 AppManager              com.dspread.mdm.service              I  ℹ️ Uninstalling app via PackageInstaller: com.chileaf.cl960.sample
2025-08-14 18:37:39.709 19136-19166 AppManager              com.dspread.mdm.service              I  ℹ️ Deleting package via PackageInstaller: com.chileaf.cl960.sample
2025-08-14 18:37:39.717 19136-19166 AppManager              com.dspread.mdm.service              I  ℹ️ PackageInstaller uninstall initiated
2025-08-14 18:37:39.721 19136-19166 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 卸载应用: plus.H52FFB9A5
2025-08-14 18:37:39.729 19136-19166 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 更新应用状态: 168fee4bca5148a0be863080acad09ee, plus.H52FFB9A5 -> D01
2025-08-14 18:37:39.734 19136-19166 WebSocket               com.dspread.mdm.service              I  🔧 C0107 主动式上送: rulebase_update (主动: 40)
2025-08-14 18:37:39.798 19136-19166 WebSocket               com.dspread.mdm.service              I  🔧 添加规则结果: ruleId=168fee4bca5148a0be863080acad09ee
2025-08-14 18:37:39.842 19136-19207 Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"request_time":"1755167858803","org_request_time":"1755167859636","org_request_id":"1755167859636C0107","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755167858803S0000","serialNo":"01354090202503050399"}
2025-08-14 18:37:39.846 19136-19166 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0107","serialNo":"01354090202503050399","request_time":"1755167859747","request_id":"1755167859747C0107","version":"1","data":{"ruleId":"168fee4bca5148a0be863080acad09ee","taskResult":"R02","failedApkList":[{"packName":"mark.via","apkName":"Via","versionName":"6.6.0","versionCode":"20250713","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/4d256c4123b346fd817537b491342204.apk","apkMd5":"0d1225260d03e10a8ffc8409369c442a","apkSize":2565110,"installBy":"0","isSystemApp":false,"priority":0,"result":"W01","taskType":"01"},{"packName":"com.lingjingnet.lazycat","apkName":"懒猫趣播","versionName":"1.0.1","versionCode":"10","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/f03e70448bf9461abf1804e9da65b36f.apk","apkMd5":"accb937c84dcd0d346da7f8fee7bf0ae","apkSize":13140733,"installBy":"0","isSystemApp":false,"priority":0,"result":"W01","taskType":"01"},{"packName":"com.chileaf.cl960.sample","apkName":"cl960","versionName":"1.1.0","versionCode":"1","apkUrl":"","apkMd5":"","apkSize":0,"installBy":"0","isSystemApp":false,"priority":0,"result":"D01","taskType":"02"},{"packName":"plus.H52FFB9A5","apkName":"逍遥助手","versionName":"1.0","versionCode":"100","apkUrl":"","apkMd5":"","apkSize":0,"installBy":"0","isSystemApp":false,"priority":0,"result":"D01","taskType":"02"}]},"myVersionName":"1.0.08.20250814.DSPREAD.MDM.SERVICE","terminalDate":"20250814183739"}
2025-08-14 18:37:39.850 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 收到响应消息: transCode=S0000, orgRequestId=1755167859636C0107, state=0, remark=
2025-08-14 18:37:39.851 19136-19166 WebSocket               com.dspread.mdm.service              I  🔧 规则结果实时上送: ruleId=168fee4bca5148a0be863080acad09ee, result=R02 (1)
2025-08-14 18:37:39.855 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 处理规则结果响应: state=0
2025-08-14 18:37:39.856 19136-19166 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 上报规则执行中状态: 168fee4bca5148a0be863080acad09ee, 应用数量: 4
2025-08-14 18:37:39.859 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 规则结果上传成功
2025-08-14 18:37:39.860 19136-19166 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 注册卸载回调: plus.H52FFB9A5 (规则ID: 168fee4bca5148a0be863080acad09ee)
2025-08-14 18:37:39.864 19136-19166 AppManager              com.dspread.mdm.service              I  ℹ️ Uninstalling app via PackageInstaller: plus.H52FFB9A5
2025-08-14 18:37:39.869 19136-19166 AppManager              com.dspread.mdm.service              I  ℹ️ Deleting package via PackageInstaller: plus.H52FFB9A5
2025-08-14 18:37:39.944 19136-19166 AppManager              com.dspread.mdm.service              I  ℹ️ PackageInstaller uninstall initiated
2025-08-14 18:37:39.949 19136-19166 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 执行安装应用，数量: 2
2025-08-14 18:37:39.954 19136-19166 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 开始安装应用，数量: 2
2025-08-14 18:37:39.962 19136-19166 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 安装应用: mark.via, URL: https://siot-bucket01.s3.ap-northeast-1.amazonaws.com/app/4d256c4123b346fd817537b491342204.apk
2025-08-14 18:37:39.967 19136-19166 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 检查应用安装状态: mark.via
2025-08-14 18:37:40.030 19136-19166 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 应用是否已安装: false
2025-08-14 18:37:40.035 19136-19166 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 应用未安装，需要安装
2025-08-14 18:37:40.040 19136-19166 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 更新应用状态: 168fee4bca5148a0be863080acad09ee, mark.via -> A01
2025-08-14 18:37:40.045 19136-19166 WebSocket               com.dspread.mdm.service              I  🔧 C0107 主动式上送: rulebase_update (主动: 41)
2025-08-14 18:37:40.117 19136-19166 WebSocket               com.dspread.mdm.service              I  🔧 添加规则结果: ruleId=168fee4bca5148a0be863080acad09ee
2025-08-14 18:37:40.140 19136-19136 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 收到广播: android.intent.action.PACKAGE_REMOVED
2025-08-14 18:37:40.151 19136-19136 Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 收到包更新广播: android.intent.action.PACKAGE_REMOVED, 包名: com.chileaf.cl960.sample
2025-08-14 18:37:40.169 19136-19136 Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 应用卸载: com.chileaf.cl960.sample
2025-08-14 18:37:40.180 19136-19136 Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 检查WebSocket状态: packageName=com.chileaf.cl960.sample, action=UNINSTALL
2025-08-14 18:37:40.189 19136-19136 Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 通知rulebase处理应用卸载: com.chileaf.cl960.sample
2025-08-14 18:37:40.195 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 收到系统广播，应用已卸载: com.chileaf.cl960.sample
2025-08-14 18:37:40.205 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 找到匹配的卸载回调，触发: com.chileaf.cl960.sample
2025-08-14 18:37:40.210 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 卸载回调被触发: pkg=com.chileaf.cl960.sample, returnCode=1, error=
2025-08-14 18:37:40.211 19136-19166 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0107","serialNo":"01354090202503050399","request_time":"1755167860058","request_id":"1755167860058C0107","version":"1","data":{"ruleId":"168fee4bca5148a0be863080acad09ee","taskResult":"R02","failedApkList":[{"packName":"mark.via","apkName":"Via","versionName":"6.6.0","versionCode":"20250713","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/4d256c4123b346fd817537b491342204.apk","apkMd5":"0d1225260d03e10a8ffc8409369c442a","apkSize":2565110,"installBy":"0","isSystemApp":false,"priority":0,"result":"A01","taskType":"01"},{"packName":"com.lingjingnet.lazycat","apkName":"懒猫趣播","versionName":"1.0.1","versionCode":"10","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/f03e70448bf9461abf1804e9da65b36f.apk","apkMd5":"accb937c84dcd0d346da7f8fee7bf0ae","apkSize":13140733,"installBy":"0","isSystemApp":false,"priority":0,"result":"W01","taskType":"01"},{"packName":"com.chileaf.cl960.sample","apkName":"cl960","versionName":"1.1.0","versionCode":"1","apkUrl":"","apkMd5":"","apkSize":0,"installBy":"0","isSystemApp":false,"priority":0,"result":"D01","taskType":"02"},{"packName":"plus.H52FFB9A5","apkName":"逍遥助手","versionName":"1.0","versionCode":"100","apkUrl":"","apkMd5":"","apkSize":0,"installBy":"0","isSystemApp":false,"priority":0,"result":"D01","taskType":"02"}]},"myVersionName":"1.0.08.20250814.DSPREAD.MDM.SERVICE","terminalDate":"20250814183740"}
2025-08-14 18:37:40.216 19136-19166 WebSocket               com.dspread.mdm.service              I  🔧 规则结果实时上送: ruleId=168fee4bca5148a0be863080acad09ee, result=R02 (1)
2025-08-14 18:37:40.216 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 应用卸载启动成功: com.chileaf.cl960.sample
2025-08-14 18:37:40.221 19136-19166 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 上报规则执行中状态: 168fee4bca5148a0be863080acad09ee, 应用数量: 4
2025-08-14 18:37:40.221 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 从广播更新应用状态: 168fee4bca5148a0be863080acad09ee, com.chileaf.cl960.sample -> D02
2025-08-14 18:37:40.226 19136-19136 WebSocket               com.dspread.mdm.service              I  🔧 C0107 主动式上送: rulebase_update (主动: 42)
2025-08-14 18:37:40.227 19136-19166 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 安装应用: com.lingjingnet.lazycat, URL: https://siot-bucket01.s3.ap-northeast-1.amazonaws.com/app/f03e70448bf9461abf1804e9da65b36f.apk
2025-08-14 18:37:40.234 19136-19166 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 检查应用安装状态: com.lingjingnet.lazycat
2025-08-14 18:37:40.248 19136-19136 WebSocket               com.dspread.mdm.service              I  🔧 添加规则结果: ruleId=168fee4bca5148a0be863080acad09ee
2025-08-14 18:37:40.259 19136-19166 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 应用是否已安装: false
2025-08-14 18:37:40.264 19136-19166 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 应用未安装，需要安装
2025-08-14 18:37:40.269 19136-19166 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 更新应用状态: 168fee4bca5148a0be863080acad09ee, com.lingjingnet.lazycat -> A01
2025-08-14 18:37:40.274 19136-19166 WebSocket               com.dspread.mdm.service              I  🔧 C0107 主动式上送: rulebase_update (主动: 43)
2025-08-14 18:37:40.284 19136-19469 HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 尝试下载，第1次
2025-08-14 18:37:40.293 19136-19166 WebSocket               com.dspread.mdm.service              I  🔧 添加规则结果: ruleId=168fee4bca5148a0be863080acad09ee
2025-08-14 18:37:40.294 19136-19469 HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 开始下载
2025-08-14 18:37:40.296 19136-19136 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0107","serialNo":"01354090202503050399","request_time":"1755167860240","request_id":"1755167860240C0107","version":"1","data":{"ruleId":"168fee4bca5148a0be863080acad09ee","taskResult":"R02","failedApkList":[{"packName":"mark.via","apkName":"Via","versionName":"6.6.0","versionCode":"20250713","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/4d256c4123b346fd817537b491342204.apk","apkMd5":"0d1225260d03e10a8ffc8409369c442a","apkSize":2565110,"installBy":"0","isSystemApp":false,"priority":0,"result":"A01","taskType":"01"},{"packName":"com.lingjingnet.lazycat","apkName":"懒猫趣播","versionName":"1.0.1","versionCode":"10","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/f03e70448bf9461abf1804e9da65b36f.apk","apkMd5":"accb937c84dcd0d346da7f8fee7bf0ae","apkSize":13140733,"installBy":"0","isSystemApp":false,"priority":0,"result":"A01","taskType":"01"},{"packName":"com.chileaf.cl960.sample","apkName":"cl960","versionName":"1.1.0","versionCode":"1","apkUrl":"","apkMd5":"","apkSize":0,"installBy":"0","isSystemApp":false,"priority":0,"result":"D02","taskType":"02"},{"packName":"plus.H52FFB9A5","apkName":"逍遥助手","versionName":"1.0","versionCode":"100","apkUrl":"","apkMd5":"","apkSize":0,"installBy":"0","isSystemApp":false,"priority":0,"result":"D01","taskType":"02"}]},"myVersionName":"1.0.08.20250814.DSPREAD.MDM.SERVICE","terminalDate":"20250814183740"}
2025-08-14 18:37:40.305 19136-19136 WebSocket               com.dspread.mdm.service              I  🔧 规则结果实时上送: ruleId=168fee4bca5148a0be863080acad09ee, result=R02 (1)
2025-08-14 18:37:40.310 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 上报规则执行中状态: 168fee4bca5148a0be863080acad09ee, 应用数量: 4
2025-08-14 18:37:40.314 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 开始检查规则完成状态: 168fee4bca5148a0be863080acad09ee, 总应用数: 4
2025-08-14 18:37:40.322 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 检查应用状态: mark.via -> A01
2025-08-14 18:37:40.326 19136-19469 HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader URL: https://siot-bucket01.s3.ap-northeast-1.amazonaws.com/app/4d256c4123b346fd817537b491342204.apk
2025-08-14 18:37:40.331 19136-19207 Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"request_time":"1755167858954","org_request_time":"1755167859747","org_request_id":"1755167859747C0107","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755167858954S0000","serialNo":"01354090202503050399"}
2025-08-14 18:37:40.337 19136-19469 HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 路径: /data/user/0/com.dspread.mdm.service/files/downloads/rule_168fee4bca5148a0be863080acad09ee_mark.via.apk
2025-08-14 18:37:40.337 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 发现未完成状态: mark.via -> A01
2025-08-14 18:37:40.342 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 规则完成检查结果: 168fee4bca5148a0be863080acad09ee, 全部完成: false, 完成数: 0/4, 有失败: false
2025-08-14 18:37:40.344 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 收到响应消息: transCode=S0000, orgRequestId=1755167859747C0107, state=0, remark=
2025-08-14 18:37:40.345 19136-19166 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0107","serialNo":"01354090202503050399","request_time":"1755167860288","request_id":"1755167860288C0107","version":"1","data":{"ruleId":"168fee4bca5148a0be863080acad09ee","taskResult":"R02","failedApkList":[{"packName":"mark.via","apkName":"Via","versionName":"6.6.0","versionCode":"20250713","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/4d256c4123b346fd817537b491342204.apk","apkMd5":"0d1225260d03e10a8ffc8409369c442a","apkSize":2565110,"installBy":"0","isSystemApp":false,"priority":0,"result":"A01","taskType":"01"},{"packName":"com.lingjingnet.lazycat","apkName":"懒猫趣播","versionName":"1.0.1","versionCode":"10","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/f03e70448bf9461abf1804e9da65b36f.apk","apkMd5":"accb937c84dcd0d346da7f8fee7bf0ae","apkSize":13140733,"installBy":"0","isSystemApp":false,"priority":0,"result":"A01","taskType":"01"},{"packName":"com.chileaf.cl960.sample","apkName":"cl960","versionName":"1.1.0","versionCode":"1","apkUrl":"","apkMd5":"","apkSize":0,"installBy":"0","isSystemApp":false,"priority":0,"result":"D02","taskType":"02"},{"packName":"plus.H52FFB9A5","apkName":"逍遥助手","versionName":"1.0","versionCode":"100","apkUrl":"","apkMd5":"","apkSize":0,"installBy":"0","isSystemApp":false,"priority":0,"result":"D01","taskType":"02"}]},"myVersionName":"1.0.08.20250814.DSPREAD.MDM.SERVICE","terminalDate":"20250814183740"}
2025-08-14 18:37:40.346 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 规则未完成: 168fee4bca5148a0be863080acad09ee, 完成数: 0/4
2025-08-14 18:37:40.349 19136-19166 WebSocket               com.dspread.mdm.service              I  🔧 规则结果实时上送: ruleId=168fee4bca5148a0be863080acad09ee, result=R02 (1)
2025-08-14 18:37:40.353 19136-19469 HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 本地文件不存在，创建文件
2025-08-14 18:37:40.354 19136-19166 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 上报规则执行中状态: 168fee4bca5148a0be863080acad09ee, 应用数量: 4
2025-08-14 18:37:40.358 19136-19469 HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader allFileLength=2565110, 需要从服务器获取文件大小
2025-08-14 18:37:40.361 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 处理规则结果响应: state=0
2025-08-14 18:37:40.361 19136-19166 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 规则执行启动成功: 168fee4bca5148a0be863080acad09ee
2025-08-14 18:37:40.363 19136-19476 HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 尝试下载，第1次
2025-08-14 18:37:40.365 19136-19136 Platform                com.dspread.mdm.service              D  🔧 重新收集应用信息（强制刷新）
2025-08-14 18:37:40.366 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 规则结果上传成功
2025-08-14 18:37:40.366 19136-19166 RuleBase                com.dspread.mdm.service              D  🔧 RuleBaseManager 规则添加成功: 168fee4bca5148a0be863080acad09ee
2025-08-14 18:37:40.372 19136-19476 HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 开始下载
2025-08-14 18:37:40.383 19136-19476 HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader URL: https://siot-bucket01.s3.ap-northeast-1.amazonaws.com/app/f03e70448bf9461abf1804e9da65b36f.apk
2025-08-14 18:37:40.389 19136-19476 HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 路径: /data/user/0/com.dspread.mdm.service/files/downloads/rule_168fee4bca5148a0be863080acad09ee_com.lingjingnet.lazycat.apk
2025-08-14 18:37:40.402 19136-19476 HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 本地文件不存在，创建文件
2025-08-14 18:37:40.421 19136-19476 HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader allFileLength=13140733, 需要从服务器获取文件大小
2025-08-14 18:37:40.427 19136-19207 Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"request_time":"1755167859346","org_request_time":"1755167860058","org_request_id":"1755167860058C0107","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755167859346S0000","serialNo":"01354090202503050399"}
2025-08-14 18:37:40.435 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 收到响应消息: transCode=S0000, orgRequestId=1755167860058C0107, state=0, remark=
2025-08-14 18:37:40.444 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 处理规则结果响应: state=0
2025-08-14 18:37:40.448 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 规则结果上传成功
2025-08-14 18:37:40.464 19136-19136 AppManager              com.dspread.mdm.service              I  ℹ️ 应用信息获取完成: 总数130(系统129/用户1) 返回1个
2025-08-14 18:37:40.475 19136-19136 Platform                com.dspread.mdm.service              D  🔧 应用信息: 1 个应用
2025-08-14 18:37:40.477 19136-19207 Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"request_time":"1755167859428","org_request_time":"1755167860240","org_request_id":"1755167860240C0107","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755167859428S0000","serialNo":"01354090202503050399"}
2025-08-14 18:37:40.480 19136-19136 Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 应用信息缓存刷新完成
2025-08-14 18:37:40.485 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 收到响应消息: transCode=S0000, orgRequestId=1755167860240C0107, state=0, remark=
2025-08-14 18:37:40.486 19136-19136 ContextImpl             com.dspread.mdm.service              W  Calling a method in the system process without a qualified user: android.app.ContextImpl.sendBroadcast:1220 android.content.ContextWrapper.sendBroadcast:504 com.dspread.mdm.service.broadcast.core.BroadcastSender.sendBroadcast:99 com.dspread.mdm.service.broadcast.core.BroadcastSender.sendBroadcast:23 com.dspread.mdm.service.broadcast.handlers.system.PackageUpdateEventHandlerImpl.sendAppRefreshBroadcast:491 
2025-08-14 18:37:40.489 19136-19136 Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 发送应用刷新广播
2025-08-14 18:37:40.490 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 处理规则结果响应: state=0
2025-08-14 18:37:40.494 19136-19136 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 收到广播: android.intent.action.PACKAGE_REMOVED
2025-08-14 18:37:40.494 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 规则结果上传成功
2025-08-14 18:37:40.500 19136-19136 Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 收到包更新广播: android.intent.action.PACKAGE_REMOVED, 包名: com.chileaf.cl960.sample
2025-08-14 18:37:40.505 19136-19136 Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 应用卸载: com.chileaf.cl960.sample
2025-08-14 18:37:40.510 19136-19136 Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 检查WebSocket状态: packageName=com.chileaf.cl960.sample, action=UNINSTALL
2025-08-14 18:37:40.515 19136-19136 Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 通知rulebase处理应用卸载: com.chileaf.cl960.sample
2025-08-14 18:37:40.520 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 收到系统广播，应用已卸载: com.chileaf.cl960.sample
2025-08-14 18:37:40.523 19136-19469 TrafficStats            com.dspread.mdm.service              D  tagSocket(98) with statsTag=0xffffffff, statsUid=-1
2025-08-14 18:37:40.525 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 未找到匹配的卸载回调: com.chileaf.cl960.sample
2025-08-14 18:37:40.528 19136-19476 TrafficStats            com.dspread.mdm.service              D  tagSocket(102) with statsTag=0xffffffff, statsUid=-1
2025-08-14 18:37:40.531 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 从广播更新应用状态: 168fee4bca5148a0be863080acad09ee, com.chileaf.cl960.sample -> D02
2025-08-14 18:37:40.536 19136-19136 WebSocket               com.dspread.mdm.service              I  🔧 C0107 主动式上送: rulebase_update (主动: 44)
2025-08-14 18:37:40.555 19136-19136 WebSocket               com.dspread.mdm.service              I  🔧 添加规则结果: ruleId=168fee4bca5148a0be863080acad09ee
2025-08-14 18:37:40.602 19136-19136 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0107","serialNo":"01354090202503050399","request_time":"1755167860550","request_id":"1755167860550C0107","version":"1","data":{"ruleId":"168fee4bca5148a0be863080acad09ee","taskResult":"R02","failedApkList":[{"packName":"mark.via","apkName":"Via","versionName":"6.6.0","versionCode":"20250713","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/4d256c4123b346fd817537b491342204.apk","apkMd5":"0d1225260d03e10a8ffc8409369c442a","apkSize":2565110,"installBy":"0","isSystemApp":false,"priority":0,"result":"A01","taskType":"01"},{"packName":"com.lingjingnet.lazycat","apkName":"懒猫趣播","versionName":"1.0.1","versionCode":"10","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/f03e70448bf9461abf1804e9da65b36f.apk","apkMd5":"accb937c84dcd0d346da7f8fee7bf0ae","apkSize":13140733,"installBy":"0","isSystemApp":false,"priority":0,"result":"A01","taskType":"01"},{"packName":"com.chileaf.cl960.sample","apkName":"cl960","versionName":"1.1.0","versionCode":"1","apkUrl":"","apkMd5":"","apkSize":0,"installBy":"0","isSystemApp":false,"priority":0,"result":"D02","taskType":"02"},{"packName":"plus.H52FFB9A5","apkName":"逍遥助手","versionName":"1.0","versionCode":"100","apkUrl":"","apkMd5":"","apkSize":0,"installBy":"0","isSystemApp":false,"priority":0,"result":"D01","taskType":"02"}]},"myVersionName":"1.0.08.20250814.DSPREAD.MDM.SERVICE","terminalDate":"20250814183740"}
2025-08-14 18:37:40.603 19136-19207 Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"request_time":"1755167859535","org_request_time":"1755167860288","org_request_id":"1755167860288C0107","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755167859535S0000","serialNo":"01354090202503050399"}
2025-08-14 18:37:40.607 19136-19136 WebSocket               com.dspread.mdm.service              I  🔧 规则结果实时上送: ruleId=168fee4bca5148a0be863080acad09ee, result=R02 (1)
2025-08-14 18:37:40.610 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 收到响应消息: transCode=S0000, orgRequestId=1755167860288C0107, state=0, remark=
2025-08-14 18:37:40.612 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 上报规则执行中状态: 168fee4bca5148a0be863080acad09ee, 应用数量: 4
2025-08-14 18:37:40.615 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 处理规则结果响应: state=0
2025-08-14 18:37:40.617 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 开始检查规则完成状态: 168fee4bca5148a0be863080acad09ee, 总应用数: 4
2025-08-14 18:37:40.620 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 规则结果上传成功
2025-08-14 18:37:40.622 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 检查应用状态: mark.via -> A01
2025-08-14 18:37:40.627 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 发现未完成状态: mark.via -> A01
2025-08-14 18:37:40.632 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 规则完成检查结果: 168fee4bca5148a0be863080acad09ee, 全部完成: false, 完成数: 0/4, 有失败: false
2025-08-14 18:37:40.637 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 规则未完成: 168fee4bca5148a0be863080acad09ee, 完成数: 0/4
2025-08-14 18:37:40.642 19136-19136 Platform                com.dspread.mdm.service              D  🔧 重新收集应用信息（强制刷新）
2025-08-14 18:37:40.736 19136-19207 Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"request_time":"1755167859711","org_request_time":"1755167860550","org_request_id":"1755167860550C0107","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755167859711S0000","serialNo":"01354090202503050399"}
2025-08-14 18:37:40.748 19136-19136 AppManager              com.dspread.mdm.service              I  ℹ️ 应用信息获取完成: 总数130(系统129/用户1) 返回1个
2025-08-14 18:37:40.767 19136-19136 Platform                com.dspread.mdm.service              D  🔧 应用信息: 1 个应用
2025-08-14 18:37:40.777 19136-19136 Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 应用信息缓存刷新完成
2025-08-14 18:37:40.783 19136-19136 ContextImpl             com.dspread.mdm.service              W  Calling a method in the system process without a qualified user: android.app.ContextImpl.sendBroadcast:1220 android.content.ContextWrapper.sendBroadcast:504 com.dspread.mdm.service.broadcast.core.BroadcastSender.sendBroadcast:99 com.dspread.mdm.service.broadcast.core.BroadcastSender.sendBroadcast:23 com.dspread.mdm.service.broadcast.handlers.system.PackageUpdateEventHandlerImpl.sendAppRefreshBroadcast:491 
2025-08-14 18:37:40.784 19136-19136 Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 发送应用刷新广播
2025-08-14 18:37:40.793 19136-19136 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 收到广播: android.intent.action.PACKAGE_REMOVED
2025-08-14 18:37:40.801 19136-19136 Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 收到包更新广播: android.intent.action.PACKAGE_REMOVED, 包名: plus.H52FFB9A5
2025-08-14 18:37:40.812 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 收到响应消息: transCode=S0000, orgRequestId=1755167860550C0107, state=0, remark=
2025-08-14 18:37:40.818 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 处理规则结果响应: state=0
2025-08-14 18:37:40.838 19136-19136 Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 应用卸载: plus.H52FFB9A5
2025-08-14 18:37:40.845 19136-19136 Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 检查WebSocket状态: packageName=plus.H52FFB9A5, action=UNINSTALL
2025-08-14 18:37:40.852 19136-19136 Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 通知rulebase处理应用卸载: plus.H52FFB9A5
2025-08-14 18:37:40.861 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 规则结果上传成功
2025-08-14 18:37:40.877 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 收到系统广播，应用已卸载: plus.H52FFB9A5
2025-08-14 18:37:40.883 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 找到匹配的卸载回调，触发: plus.H52FFB9A5
2025-08-14 18:37:40.889 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 卸载回调被触发: pkg=plus.H52FFB9A5, returnCode=1, error=
2025-08-14 18:37:40.904 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 应用卸载启动成功: plus.H52FFB9A5
2025-08-14 18:37:40.916 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 从广播更新应用状态: 168fee4bca5148a0be863080acad09ee, plus.H52FFB9A5 -> D02
2025-08-14 18:37:40.926 19136-19136 WebSocket               com.dspread.mdm.service              I  🔧 C0107 主动式上送: rulebase_update (主动: 45)
2025-08-14 18:37:40.948 19136-19136 WebSocket               com.dspread.mdm.service              I  🔧 添加规则结果: ruleId=168fee4bca5148a0be863080acad09ee
2025-08-14 18:37:40.999 19136-19136 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0107","serialNo":"01354090202503050399","request_time":"1755167860941","request_id":"1755167860941C0107","version":"1","data":{"ruleId":"168fee4bca5148a0be863080acad09ee","taskResult":"R02","failedApkList":[{"packName":"mark.via","apkName":"Via","versionName":"6.6.0","versionCode":"20250713","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/4d256c4123b346fd817537b491342204.apk","apkMd5":"0d1225260d03e10a8ffc8409369c442a","apkSize":2565110,"installBy":"0","isSystemApp":false,"priority":0,"result":"A01","taskType":"01"},{"packName":"com.lingjingnet.lazycat","apkName":"懒猫趣播","versionName":"1.0.1","versionCode":"10","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/f03e70448bf9461abf1804e9da65b36f.apk","apkMd5":"accb937c84dcd0d346da7f8fee7bf0ae","apkSize":13140733,"installBy":"0","isSystemApp":false,"priority":0,"result":"A01","taskType":"01"},{"packName":"com.chileaf.cl960.sample","apkName":"cl960","versionName":"1.1.0","versionCode":"1","apkUrl":"","apkMd5":"","apkSize":0,"installBy":"0","isSystemApp":false,"priority":0,"result":"D02","taskType":"02"},{"packName":"plus.H52FFB9A5","apkName":"逍遥助手","versionName":"1.0","versionCode":"100","apkUrl":"","apkMd5":"","apkSize":0,"installBy":"0","isSystemApp":false,"priority":0,"result":"D02","taskType":"02"}]},"myVersionName":"1.0.08.20250814.DSPREAD.MDM.SERVICE","terminalDate":"20250814183740"}
2025-08-14 18:37:41.006 19136-19136 WebSocket               com.dspread.mdm.service              I  🔧 规则结果实时上送: ruleId=168fee4bca5148a0be863080acad09ee, result=R02 (1)
2025-08-14 18:37:41.014 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 上报规则执行中状态: 168fee4bca5148a0be863080acad09ee, 应用数量: 4
2025-08-14 18:37:41.020 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 开始检查规则完成状态: 168fee4bca5148a0be863080acad09ee, 总应用数: 4
2025-08-14 18:37:41.025 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 检查应用状态: mark.via -> A01
2025-08-14 18:37:41.031 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 发现未完成状态: mark.via -> A01
2025-08-14 18:37:41.036 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 规则完成检查结果: 168fee4bca5148a0be863080acad09ee, 全部完成: false, 完成数: 0/4, 有失败: false
2025-08-14 18:37:41.041 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 规则未完成: 168fee4bca5148a0be863080acad09ee, 完成数: 0/4
2025-08-14 18:37:41.046 19136-19136 Platform                com.dspread.mdm.service              D  🔧 重新收集应用信息（强制刷新）
2025-08-14 18:37:41.145 19136-19136 AppManager              com.dspread.mdm.service              I  ℹ️ 应用信息获取完成: 总数130(系统129/用户1) 返回1个
2025-08-14 18:37:41.157 19136-19136 Platform                com.dspread.mdm.service              D  🔧 应用信息: 1 个应用
2025-08-14 18:37:41.162 19136-19136 Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 应用信息缓存刷新完成
2025-08-14 18:37:41.168 19136-19136 ContextImpl             com.dspread.mdm.service              W  Calling a method in the system process without a qualified user: android.app.ContextImpl.sendBroadcast:1220 android.content.ContextWrapper.sendBroadcast:504 com.dspread.mdm.service.broadcast.core.BroadcastSender.sendBroadcast:99 com.dspread.mdm.service.broadcast.core.BroadcastSender.sendBroadcast:23 com.dspread.mdm.service.broadcast.handlers.system.PackageUpdateEventHandlerImpl.sendAppRefreshBroadcast:491 
2025-08-14 18:37:41.170 19136-19136 Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 发送应用刷新广播
2025-08-14 18:37:41.175 19136-19136 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 收到广播: android.intent.action.PACKAGE_REMOVED
2025-08-14 18:37:41.181 19136-19136 Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 收到包更新广播: android.intent.action.PACKAGE_REMOVED, 包名: plus.H52FFB9A5
2025-08-14 18:37:41.187 19136-19136 Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 应用卸载: plus.H52FFB9A5
2025-08-14 18:37:41.192 19136-19136 Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 检查WebSocket状态: packageName=plus.H52FFB9A5, action=UNINSTALL
2025-08-14 18:37:41.197 19136-19136 Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 通知rulebase处理应用卸载: plus.H52FFB9A5
2025-08-14 18:37:41.203 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 收到系统广播，应用已卸载: plus.H52FFB9A5
2025-08-14 18:37:41.208 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 未找到匹配的卸载回调: plus.H52FFB9A5
2025-08-14 18:37:41.214 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 从广播更新应用状态: 168fee4bca5148a0be863080acad09ee, plus.H52FFB9A5 -> D02
2025-08-14 18:37:41.221 19136-19136 WebSocket               com.dspread.mdm.service              I  🔧 C0107 主动式上送: rulebase_update (主动: 46)
2025-08-14 18:37:41.254 19136-19136 WebSocket               com.dspread.mdm.service              I  🔧 添加规则结果: ruleId=168fee4bca5148a0be863080acad09ee
2025-08-14 18:37:41.256 19136-19469 HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader HTTP响应码: 200
2025-08-14 18:37:41.264 19136-19469 HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 服务器返回Content-Length: 2565110
2025-08-14 18:37:41.272 19136-19469 HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader localFile = true /data/user/0/com.dspread.mdm.service/files/downloads/rule_168fee4bca5148a0be863080acad09ee_mark.via.apk
2025-08-14 18:37:41.290 19136-19469 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 下载进度: mark.via - 0%
2025-08-14 18:37:41.314 19136-19476 HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader HTTP响应码: 200
2025-08-14 18:37:41.317 19136-19136 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0107","serialNo":"01354090202503050399","request_time":"1755167861245","request_id":"1755167861245C0107","version":"1","data":{"ruleId":"168fee4bca5148a0be863080acad09ee","taskResult":"R02","failedApkList":[{"packName":"mark.via","apkName":"Via","versionName":"6.6.0","versionCode":"20250713","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/4d256c4123b346fd817537b491342204.apk","apkMd5":"0d1225260d03e10a8ffc8409369c442a","apkSize":2565110,"installBy":"0","isSystemApp":false,"priority":0,"result":"A01","taskType":"01"},{"packName":"com.lingjingnet.lazycat","apkName":"懒猫趣播","versionName":"1.0.1","versionCode":"10","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/f03e70448bf9461abf1804e9da65b36f.apk","apkMd5":"accb937c84dcd0d346da7f8fee7bf0ae","apkSize":13140733,"installBy":"0","isSystemApp":false,"priority":0,"result":"A01","taskType":"01"},{"packName":"com.chileaf.cl960.sample","apkName":"cl960","versionName":"1.1.0","versionCode":"1","apkUrl":"","apkMd5":"","apkSize":0,"installBy":"0","isSystemApp":false,"priority":0,"result":"D02","taskType":"02"},{"packName":"plus.H52FFB9A5","apkName":"逍遥助手","versionName":"1.0","versionCode":"100","apkUrl":"","apkMd5":"","apkSize":0,"installBy":"0","isSystemApp":false,"priority":0,"result":"D02","taskType":"02"}]},"myVersionName":"1.0.08.20250814.DSPREAD.MDM.SERVICE","terminalDate":"20250814183741"}
2025-08-14 18:37:41.323 19136-19476 HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 服务器返回Content-Length: 13140733
2025-08-14 18:37:41.327 19136-19136 WebSocket               com.dspread.mdm.service              I  🔧 规则结果实时上送: ruleId=168fee4bca5148a0be863080acad09ee, result=R02 (1)
2025-08-14 18:37:41.332 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 上报规则执行中状态: 168fee4bca5148a0be863080acad09ee, 应用数量: 4
2025-08-14 18:37:41.333 19136-19207 Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"request_time":"1755167860106","org_request_time":"1755167860941","org_request_id":"1755167860941C0107","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755167860106S0000","serialNo":"01354090202503050399"}
2025-08-14 18:37:41.338 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 开始检查规则完成状态: 168fee4bca5148a0be863080acad09ee, 总应用数: 4
2025-08-14 18:37:41.338 19136-19476 HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader localFile = true /data/user/0/com.dspread.mdm.service/files/downloads/rule_168fee4bca5148a0be863080acad09ee_com.lingjingnet.lazycat.apk
2025-08-14 18:37:41.341 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 收到响应消息: transCode=S0000, orgRequestId=1755167860941C0107, state=0, remark=
2025-08-14 18:37:41.348 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 处理规则结果响应: state=0
2025-08-14 18:37:41.352 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 规则结果上传成功
2025-08-14 18:37:41.354 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 检查应用状态: mark.via -> A01
2025-08-14 18:37:41.365 19136-19476 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 下载进度: com.lingjingnet.lazycat - 0%
2025-08-14 18:37:41.441 19136-19207 Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"request_time":"1755167860418","org_request_time":"1755167861245","org_request_id":"1755167861245C0107","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755167860418S0000","serialNo":"01354090202503050399"}
2025-08-14 18:37:41.449 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 收到响应消息: transCode=S0000, orgRequestId=1755167861245C0107, state=0, remark=
2025-08-14 18:37:41.454 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 处理规则结果响应: state=0
2025-08-14 18:37:41.459 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 规则结果上传成功
2025-08-14 18:37:41.476 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 发现未完成状态: mark.via -> A01
2025-08-14 18:37:41.481 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 规则完成检查结果: 168fee4bca5148a0be863080acad09ee, 全部完成: false, 完成数: 0/4, 有失败: false
2025-08-14 18:37:41.486 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 规则未完成: 168fee4bca5148a0be863080acad09ee, 完成数: 0/4
2025-08-14 18:37:41.491 19136-19136 Platform                com.dspread.mdm.service              D  🔧 重新收集应用信息（强制刷新）
2025-08-14 18:37:41.581 19136-19469 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 下载进度: mark.via - 10%
2025-08-14 18:37:41.588 19136-19136 AppManager              com.dspread.mdm.service              I  ℹ️ 应用信息获取完成: 总数130(系统129/用户1) 返回1个
2025-08-14 18:37:41.600 19136-19136 Platform                com.dspread.mdm.service              D  🔧 应用信息: 1 个应用
2025-08-14 18:37:41.605 19136-19136 Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 应用信息缓存刷新完成
2025-08-14 18:37:41.610 19136-19136 ContextImpl             com.dspread.mdm.service              W  Calling a method in the system process without a qualified user: android.app.ContextImpl.sendBroadcast:1220 android.content.ContextWrapper.sendBroadcast:504 com.dspread.mdm.service.broadcast.core.BroadcastSender.sendBroadcast:99 com.dspread.mdm.service.broadcast.core.BroadcastSender.sendBroadcast:23 com.dspread.mdm.service.broadcast.handlers.system.PackageUpdateEventHandlerImpl.sendAppRefreshBroadcast:491 
2025-08-14 18:37:41.612 19136-19136 Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 发送应用刷新广播
2025-08-14 18:37:42.103 19136-19208 WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 8
2025-08-14 18:37:42.158 19136-19469 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 下载进度: mark.via - 20%
2025-08-14 18:37:42.264 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 收到服务器 PONG 帧: 8
2025-08-14 18:37:42.315 19136-19136 Common                  com.dspread.mdm.service              I  ✅ ⏰ 设置任务执行定时器成功，下次执行: 60秒后 (1分钟)
2025-08-14 18:37:42.412 19136-19469 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 下载进度: mark.via - 30%
2025-08-14 18:37:42.539 19136-19469 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 下载进度: mark.via - 40%
2025-08-14 18:37:42.905 19136-19469 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 下载进度: mark.via - 50%
2025-08-14 18:37:43.339 19136-19469 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 下载进度: mark.via - 60%
2025-08-14 18:37:43.703 19136-19469 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 下载进度: mark.via - 70%
2025-08-14 18:37:44.158 19136-19469 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 下载进度: mark.via - 80%
2025-08-14 18:37:44.648 19136-19469 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 下载进度: mark.via - 90%
2025-08-14 18:37:45.040 19136-19469 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 下载进度: mark.via - 100%
2025-08-14 18:37:45.046 19136-19469 HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 计算MD5: rule_168fee4bca5148a0be863080acad09ee_mark.via.apk
2025-08-14 18:37:45.085 19136-19469 HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 文件: 2565110 MD5: 0d1225260d03e10a8ffc8409369c442a
2025-08-14 18:37:45.090 19136-19469 HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader MD5校验正确
2025-08-14 18:37:45.094 19136-19469 HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 清理连接资源
2025-08-14 18:37:45.099 19136-19469 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine APK下载成功: /data/user/0/com.dspread.mdm.service/files/downloads/rule_168fee4bca5148a0be863080acad09ee_mark.via.apk
2025-08-14 18:37:45.104 19136-19469 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 更新应用状态: 168fee4bca5148a0be863080acad09ee, mark.via -> A03
2025-08-14 18:37:45.109 19136-19469 WebSocket               com.dspread.mdm.service              I  🔧 C0107 主动式上送: rulebase_update (主动: 47)
2025-08-14 18:37:45.130 19136-19469 WebSocket               com.dspread.mdm.service              I  🔧 添加规则结果: ruleId=168fee4bca5148a0be863080acad09ee
2025-08-14 18:37:45.176 19136-19469 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0107","serialNo":"01354090202503050399","request_time":"1755167865122","request_id":"1755167865122C0107","version":"1","data":{"ruleId":"168fee4bca5148a0be863080acad09ee","taskResult":"R02","failedApkList":[{"packName":"mark.via","apkName":"Via","versionName":"6.6.0","versionCode":"20250713","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/4d256c4123b346fd817537b491342204.apk","apkMd5":"0d1225260d03e10a8ffc8409369c442a","apkSize":2565110,"installBy":"0","isSystemApp":false,"priority":0,"result":"A03","taskType":"01"},{"packName":"com.lingjingnet.lazycat","apkName":"懒猫趣播","versionName":"1.0.1","versionCode":"10","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/f03e70448bf9461abf1804e9da65b36f.apk","apkMd5":"accb937c84dcd0d346da7f8fee7bf0ae","apkSize":13140733,"installBy":"0","isSystemApp":false,"priority":0,"result":"A01","taskType":"01","errorMsg":"下载中: 0%"},{"packName":"com.chileaf.cl960.sample","apkName":"cl960","versionName":"1.1.0","versionCode":"1","apkUrl":"","apkMd5":"","apkSize":0,"installBy":"0","isSystemApp":false,"priority":0,"result":"D02","taskType":"02"},{"packName":"plus.H52FFB9A5","apkName":"逍遥助手","versionName":"1.0","versionCode":"100","apkUrl":"","apkMd5":"","apkSize":0,"installBy":"0","isSystemApp":false,"priority":0,"result":"D02","taskType":"02"}]},"myVersionName":"1.0.08.20250814.DSPREAD.MDM.SERVICE","terminalDate":"20250814183745"}
2025-08-14 18:37:45.180 19136-19469 WebSocket               com.dspread.mdm.service              I  🔧 规则结果实时上送: ruleId=168fee4bca5148a0be863080acad09ee, result=R02 (1)
2025-08-14 18:37:45.185 19136-19469 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 上报规则执行中状态: 168fee4bca5148a0be863080acad09ee, 应用数量: 4
2025-08-14 18:37:45.190 19136-19469 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 开始安装APK: /data/user/0/com.dspread.mdm.service/files/downloads/rule_168fee4bca5148a0be863080acad09ee_mark.via.apk
2025-08-14 18:37:45.195 19136-19469 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 更新应用状态: 168fee4bca5148a0be863080acad09ee, mark.via -> B02
2025-08-14 18:37:45.200 19136-19469 WebSocket               com.dspread.mdm.service              I  🔧 C0107 主动式上送: rulebase_update (主动: 48)
2025-08-14 18:37:45.218 19136-19469 WebSocket               com.dspread.mdm.service              I  🔧 添加规则结果: ruleId=168fee4bca5148a0be863080acad09ee
2025-08-14 18:37:45.264 19136-19469 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0107","serialNo":"01354090202503050399","request_time":"1755167865213","request_id":"1755167865213C0107","version":"1","data":{"ruleId":"168fee4bca5148a0be863080acad09ee","taskResult":"R02","failedApkList":[{"packName":"mark.via","apkName":"Via","versionName":"6.6.0","versionCode":"20250713","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/4d256c4123b346fd817537b491342204.apk","apkMd5":"0d1225260d03e10a8ffc8409369c442a","apkSize":2565110,"installBy":"0","isSystemApp":false,"priority":0,"result":"B02","taskType":"01"},{"packName":"com.lingjingnet.lazycat","apkName":"懒猫趣播","versionName":"1.0.1","versionCode":"10","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/f03e70448bf9461abf1804e9da65b36f.apk","apkMd5":"accb937c84dcd0d346da7f8fee7bf0ae","apkSize":13140733,"installBy":"0","isSystemApp":false,"priority":0,"result":"A01","taskType":"01","errorMsg":"下载中: 0%"},{"packName":"com.chileaf.cl960.sample","apkName":"cl960","versionName":"1.1.0","versionCode":"1","apkUrl":"","apkMd5":"","apkSize":0,"installBy":"0","isSystemApp":false,"priority":0,"result":"D02","taskType":"02"},{"packName":"plus.H52FFB9A5","apkName":"逍遥助手","versionName":"1.0","versionCode":"100","apkUrl":"","apkMd5":"","apkSize":0,"installBy":"0","isSystemApp":false,"priority":0,"result":"D02","taskType":"02"}]},"myVersionName":"1.0.08.20250814.DSPREAD.MDM.SERVICE","terminalDate":"20250814183745"}
2025-08-14 18:37:45.269 19136-19469 WebSocket               com.dspread.mdm.service              I  🔧 规则结果实时上送: ruleId=168fee4bca5148a0be863080acad09ee, result=R02 (1)
2025-08-14 18:37:45.273 19136-19469 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 上报规则执行中状态: 168fee4bca5148a0be863080acad09ee, 应用数量: 4
2025-08-14 18:37:45.278 19136-19469 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 注册安装回调: mark.via (规则ID: 168fee4bca5148a0be863080acad09ee)
2025-08-14 18:37:45.300 19136-19469 PackageParser           com.dspread.mdm.service              W  Unknown element under <manifest>: queries at /data/user/0/com.dspread.mdm.service/files/downloads/rule_168fee4bca5148a0be863080acad09ee_mark.via.apk Binary XML file line #65
2025-08-14 18:37:45.304 19136-19207 Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"request_time":"1755167864286","org_request_time":"1755167865122","org_request_id":"1755167865122C0107","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755167864286S0000","serialNo":"01354090202503050399"}
2025-08-14 18:37:45.312 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 收到响应消息: transCode=S0000, orgRequestId=1755167865122C0107, state=0, remark=
2025-08-14 18:37:45.316 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 处理规则结果响应: state=0
2025-08-14 18:37:45.321 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 规则结果上传成功
2025-08-14 18:37:45.324 19136-19469 AppManager              com.dspread.mdm.service              I  ℹ️ 准备安装: mark.via
2025-08-14 18:37:45.342 19136-19469 PackageParser           com.dspread.mdm.service              W  Unknown element under <manifest>: queries at /data/user/0/com.dspread.mdm.service/files/downloads/rule_168fee4bca5148a0be863080acad09ee_mark.via.apk Binary XML file line #65
2025-08-14 18:37:45.365 19136-19469 AppManager              com.dspread.mdm.service              I  ℹ️ APK验证通过: mark.via v6.6.0(20250713) 2504KB
2025-08-14 18:37:45.374 19136-19469 AppManager              com.dspread.mdm.service              I  ℹ️ 创建Session: sessionId=1460107200
2025-08-14 18:37:45.432 19136-19207 Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"request_time":"1755167864416","org_request_time":"1755167865213","org_request_id":"1755167865213C0107","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755167864416S0000","serialNo":"01354090202503050399"}
2025-08-14 18:37:45.439 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 收到响应消息: transCode=S0000, orgRequestId=1755167865213C0107, state=0, remark=
2025-08-14 18:37:45.444 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 处理规则结果响应: state=0
2025-08-14 18:37:45.449 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 规则结果上传成功
2025-08-14 18:37:45.507 19136-19469 AppManager              com.dspread.mdm.service              I  ℹ️ PackageInstaller installation committed: sessionId=1460107200
2025-08-14 18:37:45.523  1235-1305  PackageInstallerSession system_server                        E  com.dspread.mdm.service drops manifest attribute android:installLocation in base.apk for mark.via
2025-08-14 18:37:45.528 19136-19136 AppManager              com.dspread.mdm.service              I  ℹ️ 安装进度: 90%
2025-08-14 18:37:45.996 19136-19476 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 下载进度: com.lingjingnet.lazycat - 10%
2025-08-14 18:37:46.869 19136-19136 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 收到广播: android.intent.action.PACKAGE_ADDED
2025-08-14 18:37:46.875 19136-19136 Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 收到包更新广播: android.intent.action.PACKAGE_ADDED, 包名: mark.via
2025-08-14 18:37:46.906 19136-19136 Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 应用安装: mark.via
2025-08-14 18:37:46.922 19136-19136 Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 检查WebSocket状态: packageName=mark.via, action=INSTALL
2025-08-14 18:37:46.939 19136-19136 Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 通知rulebase处理应用安装: mark.via
2025-08-14 18:37:46.945 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 收到系统广播，应用已安装: mark.via
2025-08-14 18:37:46.957 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 找到匹配的安装回调，触发: mark.via
2025-08-14 18:37:46.967 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 安装回调被触发: pkg=mark.via, returnCode=1, error=
2025-08-14 18:37:46.973 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 应用安装启动成功: mark.via
2025-08-14 18:37:46.980 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 从广播更新应用状态: 168fee4bca5148a0be863080acad09ee, mark.via -> B03
2025-08-14 18:37:46.988 19136-19136 WebSocket               com.dspread.mdm.service              I  🔧 C0107 主动式上送: rulebase_update (主动: 49)
2025-08-14 18:37:47.016 19136-19136 WebSocket               com.dspread.mdm.service              I  🔧 添加规则结果: ruleId=168fee4bca5148a0be863080acad09ee
2025-08-14 18:37:47.072 19136-19136 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0107","serialNo":"01354090202503050399","request_time":"1755167867007","request_id":"1755167867007C0107","version":"1","data":{"ruleId":"168fee4bca5148a0be863080acad09ee","taskResult":"R02","failedApkList":[{"packName":"mark.via","apkName":"Via","versionName":"6.6.0","versionCode":"20250713","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/4d256c4123b346fd817537b491342204.apk","apkMd5":"0d1225260d03e10a8ffc8409369c442a","apkSize":2565110,"installBy":"0","isSystemApp":false,"priority":0,"result":"B03","taskType":"01"},{"packName":"com.lingjingnet.lazycat","apkName":"懒猫趣播","versionName":"1.0.1","versionCode":"10","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/f03e70448bf9461abf1804e9da65b36f.apk","apkMd5":"accb937c84dcd0d346da7f8fee7bf0ae","apkSize":13140733,"installBy":"0","isSystemApp":false,"priority":0,"result":"A01","taskType":"01","errorMsg":"下载中: 10%"},{"packName":"com.chileaf.cl960.sample","apkName":"cl960","versionName":"1.1.0","versionCode":"1","apkUrl":"","apkMd5":"","apkSize":0,"installBy":"0","isSystemApp":false,"priority":0,"result":"D02","taskType":"02"},{"packName":"plus.H52FFB9A5","apkName":"逍遥助手","versionName":"1.0","versionCode":"100","apkUrl":"","apkMd5":"","apkSize":0,"installBy":"0","isSystemApp":false,"priority":0,"result":"D02","taskType":"02"}]},"myVersionName":"1.0.08.20250814.DSPREAD.MDM.SERVICE","terminalDate":"20250814183747"}
2025-08-14 18:37:47.077 19136-19136 WebSocket               com.dspread.mdm.service              I  🔧 规则结果实时上送: ruleId=168fee4bca5148a0be863080acad09ee, result=R02 (1)
2025-08-14 18:37:47.083 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 上报规则执行中状态: 168fee4bca5148a0be863080acad09ee, 应用数量: 4
2025-08-14 18:37:47.089 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 开始检查规则完成状态: 168fee4bca5148a0be863080acad09ee, 总应用数: 4
2025-08-14 18:37:47.094 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 检查应用状态: mark.via -> B03
2025-08-14 18:37:47.099 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 发现成功状态: mark.via -> B03
2025-08-14 18:37:47.105 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 检查应用状态: com.lingjingnet.lazycat -> A01
2025-08-14 18:37:47.111 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 发现未完成状态: com.lingjingnet.lazycat -> A01
2025-08-14 18:37:47.116 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 规则完成检查结果: 168fee4bca5148a0be863080acad09ee, 全部完成: false, 完成数: 1/4, 有失败: false
2025-08-14 18:37:47.126 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 规则未完成: 168fee4bca5148a0be863080acad09ee, 完成数: 1/4
2025-08-14 18:37:47.131 19136-19136 Platform                com.dspread.mdm.service              D  🔧 重新收集应用信息（强制刷新）
2025-08-14 18:37:47.249 19136-19136 AppManager              com.dspread.mdm.service              I  ℹ️ 应用信息获取完成: 总数131(系统129/用户2) 返回2个
2025-08-14 18:37:47.274 19136-19136 Platform                com.dspread.mdm.service              D  🔧 应用信息: 2 个应用
2025-08-14 18:37:47.284 19136-19136 Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 应用信息缓存刷新完成
2025-08-14 18:37:47.290 19136-19136 ContextImpl             com.dspread.mdm.service              W  Calling a method in the system process without a qualified user: android.app.ContextImpl.sendBroadcast:1220 android.content.ContextWrapper.sendBroadcast:504 com.dspread.mdm.service.broadcast.core.BroadcastSender.sendBroadcast:99 com.dspread.mdm.service.broadcast.core.BroadcastSender.sendBroadcast:23 com.dspread.mdm.service.broadcast.handlers.system.PackageUpdateEventHandlerImpl.sendAppRefreshBroadcast:491 
2025-08-14 18:37:47.292 19136-19136 Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 发送应用刷新广播
2025-08-14 18:37:47.314 19136-19207 Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"request_time":"1755167866178","org_request_time":"1755167867007","org_request_id":"1755167867007C0107","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755167866178S0000","serialNo":"01354090202503050399"}
2025-08-14 18:37:47.324 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 收到响应消息: transCode=S0000, orgRequestId=1755167867007C0107, state=0, remark=
2025-08-14 18:37:47.329 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 处理规则结果响应: state=0
2025-08-14 18:37:47.334 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 规则结果上传成功
2025-08-14 18:37:48.294 19136-19476 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 下载进度: com.lingjingnet.lazycat - 20%
2025-08-14 18:37:50.203 19136-19476 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 下载进度: com.lingjingnet.lazycat - 30%
2025-08-14 18:37:51.695 19136-19476 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 下载进度: com.lingjingnet.lazycat - 40%
2025-08-14 18:37:53.197 19136-19476 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 下载进度: com.lingjingnet.lazycat - 50%
2025-08-14 18:37:54.850 19136-19476 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 下载进度: com.lingjingnet.lazycat - 60%
2025-08-14 18:37:54.881 19136-19136 Platform                com.dspread.mdm.service              D  🔧 WakeLockManager WakeLock不存在或未持有: ServiceKeepAlive
2025-08-14 18:37:54.887 19136-19136 Service                 com.dspread.mdm.service              D  🔧 ServiceKeepAliveService 保活任务完成，释放WakeLock
2025-08-14 18:37:56.386 19136-19476 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 下载进度: com.lingjingnet.lazycat - 70%
2025-08-14 18:37:58.069 19136-19476 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 下载进度: com.lingjingnet.lazycat - 80%
2025-08-14 18:37:58.239 19136-19136 Receiver                com.dspread.mdm.service              D  🔧 电池状态更新: 电池状态: 电量=100%, 温度=29.7°C, 健康=2, 充电=是, 低电量=否, 耗尽=否
2025-08-14 18:37:59.595 19136-19476 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 下载进度: com.lingjingnet.lazycat - 90%
2025-08-14 18:38:01.074 19136-19476 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 下载进度: com.lingjingnet.lazycat - 100%
2025-08-14 18:38:01.081 19136-19476 HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 计算MD5: rule_168fee4bca5148a0be863080acad09ee_com.lingjingnet.lazycat.apk
2025-08-14 18:38:01.249 19136-19476 HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 文件: 13140733 MD5: accb937c84dcd0d346da7f8fee7bf0ae
2025-08-14 18:38:01.254 19136-19476 HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader MD5校验正确
2025-08-14 18:38:01.258 19136-19476 HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 清理连接资源
2025-08-14 18:38:01.263 19136-19476 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine APK下载成功: /data/user/0/com.dspread.mdm.service/files/downloads/rule_168fee4bca5148a0be863080acad09ee_com.lingjingnet.lazycat.apk
2025-08-14 18:38:01.268 19136-19476 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 更新应用状态: 168fee4bca5148a0be863080acad09ee, com.lingjingnet.lazycat -> A03
2025-08-14 18:38:01.272 19136-19476 WebSocket               com.dspread.mdm.service              I  🔧 C0107 主动式上送: rulebase_update (主动: 50)
2025-08-14 18:38:01.292 19136-19476 WebSocket               com.dspread.mdm.service              I  🔧 添加规则结果: ruleId=168fee4bca5148a0be863080acad09ee
2025-08-14 18:38:01.337 19136-19476 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0107","serialNo":"01354090202503050399","request_time":"1755167881285","request_id":"1755167881285C0107","version":"1","data":{"ruleId":"168fee4bca5148a0be863080acad09ee","taskResult":"R02","failedApkList":[{"packName":"mark.via","apkName":"Via","versionName":"6.6.0","versionCode":"20250713","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/4d256c4123b346fd817537b491342204.apk","apkMd5":"0d1225260d03e10a8ffc8409369c442a","apkSize":2565110,"installBy":"0","isSystemApp":false,"priority":0,"result":"B03","taskType":"01"},{"packName":"com.lingjingnet.lazycat","apkName":"懒猫趣播","versionName":"1.0.1","versionCode":"10","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/f03e70448bf9461abf1804e9da65b36f.apk","apkMd5":"accb937c84dcd0d346da7f8fee7bf0ae","apkSize":13140733,"installBy":"0","isSystemApp":false,"priority":0,"result":"A03","taskType":"01"},{"packName":"com.chileaf.cl960.sample","apkName":"cl960","versionName":"1.1.0","versionCode":"1","apkUrl":"","apkMd5":"","apkSize":0,"installBy":"0","isSystemApp":false,"priority":0,"result":"D02","taskType":"02"},{"packName":"plus.H52FFB9A5","apkName":"逍遥助手","versionName":"1.0","versionCode":"100","apkUrl":"","apkMd5":"","apkSize":0,"installBy":"0","isSystemApp":false,"priority":0,"result":"D02","taskType":"02"}]},"myVersionName":"1.0.08.20250814.DSPREAD.MDM.SERVICE","terminalDate":"20250814183801"}
2025-08-14 18:38:01.342 19136-19476 WebSocket               com.dspread.mdm.service              I  🔧 规则结果实时上送: ruleId=168fee4bca5148a0be863080acad09ee, result=R02 (1)
2025-08-14 18:38:01.346 19136-19476 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 上报规则执行中状态: 168fee4bca5148a0be863080acad09ee, 应用数量: 4
2025-08-14 18:38:01.351 19136-19476 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 开始安装APK: /data/user/0/com.dspread.mdm.service/files/downloads/rule_168fee4bca5148a0be863080acad09ee_com.lingjingnet.lazycat.apk
2025-08-14 18:38:01.356 19136-19476 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 更新应用状态: 168fee4bca5148a0be863080acad09ee, com.lingjingnet.lazycat -> B02
2025-08-14 18:38:01.360 19136-19476 WebSocket               com.dspread.mdm.service              I  🔧 C0107 主动式上送: rulebase_update (主动: 51)
2025-08-14 18:38:01.378 19136-19476 WebSocket               com.dspread.mdm.service              I  🔧 添加规则结果: ruleId=168fee4bca5148a0be863080acad09ee
2025-08-14 18:38:01.423 19136-19476 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0107","serialNo":"01354090202503050399","request_time":"1755167881373","request_id":"1755167881373C0107","version":"1","data":{"ruleId":"168fee4bca5148a0be863080acad09ee","taskResult":"R02","failedApkList":[{"packName":"mark.via","apkName":"Via","versionName":"6.6.0","versionCode":"20250713","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/4d256c4123b346fd817537b491342204.apk","apkMd5":"0d1225260d03e10a8ffc8409369c442a","apkSize":2565110,"installBy":"0","isSystemApp":false,"priority":0,"result":"B03","taskType":"01"},{"packName":"com.lingjingnet.lazycat","apkName":"懒猫趣播","versionName":"1.0.1","versionCode":"10","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/f03e70448bf9461abf1804e9da65b36f.apk","apkMd5":"accb937c84dcd0d346da7f8fee7bf0ae","apkSize":13140733,"installBy":"0","isSystemApp":false,"priority":0,"result":"B02","taskType":"01"},{"packName":"com.chileaf.cl960.sample","apkName":"cl960","versionName":"1.1.0","versionCode":"1","apkUrl":"","apkMd5":"","apkSize":0,"installBy":"0","isSystemApp":false,"priority":0,"result":"D02","taskType":"02"},{"packName":"plus.H52FFB9A5","apkName":"逍遥助手","versionName":"1.0","versionCode":"100","apkUrl":"","apkMd5":"","apkSize":0,"installBy":"0","isSystemApp":false,"priority":0,"result":"D02","taskType":"02"}]},"myVersionName":"1.0.08.20250814.DSPREAD.MDM.SERVICE","terminalDate":"20250814183801"}
2025-08-14 18:38:01.428 19136-19476 WebSocket               com.dspread.mdm.service              I  🔧 规则结果实时上送: ruleId=168fee4bca5148a0be863080acad09ee, result=R02 (1)
2025-08-14 18:38:01.433 19136-19476 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 上报规则执行中状态: 168fee4bca5148a0be863080acad09ee, 应用数量: 4
2025-08-14 18:38:01.437 19136-19476 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 注册安装回调: com.lingjingnet.lazycat (规则ID: 168fee4bca5148a0be863080acad09ee)
2025-08-14 18:38:01.461 19136-19476 PackageParser           com.dspread.mdm.service              W  Unknown element under <manifest>: queries at /data/user/0/com.dspread.mdm.service/files/downloads/rule_168fee4bca5148a0be863080acad09ee_com.lingjingnet.lazycat.apk Binary XML file line #38
2025-08-14 18:38:01.485 19136-19476 AppManager              com.dspread.mdm.service              I  ℹ️ 准备安装: com.lingjingnet.lazycat
2025-08-14 18:38:01.511 19136-19476 PackageParser           com.dspread.mdm.service              W  Unknown element under <manifest>: queries at /data/user/0/com.dspread.mdm.service/files/downloads/rule_168fee4bca5148a0be863080acad09ee_com.lingjingnet.lazycat.apk Binary XML file line #38
2025-08-14 18:38:01.527 19136-19207 Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"request_time":"1755167880457","org_request_time":"1755167881285","org_request_id":"1755167881285C0107","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755167880457S0000","serialNo":"01354090202503050399"}
2025-08-14 18:38:01.534 19136-19476 AppManager              com.dspread.mdm.service              I  ℹ️ APK验证通过: com.lingjingnet.lazycat v1.0.1(10) 12832KB
2025-08-14 18:38:01.534 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 收到响应消息: transCode=S0000, orgRequestId=1755167881285C0107, state=0, remark=
2025-08-14 18:38:01.539 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 处理规则结果响应: state=0
2025-08-14 18:38:01.541 19136-19476 AppManager              com.dspread.mdm.service              I  ℹ️ 创建Session: sessionId=1266375904
2025-08-14 18:38:01.544 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 规则结果上传成功
2025-08-14 18:38:01.573 19136-19207 Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"request_time":"1755167880542","org_request_time":"1755167881373","org_request_id":"1755167881373C0107","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755167880542S0000","serialNo":"01354090202503050399"}
2025-08-14 18:38:01.581 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 收到响应消息: transCode=S0000, orgRequestId=1755167881373C0107, state=0, remark=
2025-08-14 18:38:01.585 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 处理规则结果响应: state=0
2025-08-14 18:38:01.590 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 规则结果上传成功
2025-08-14 18:38:02.107 19136-19476 AppManager              com.dspread.mdm.service              I  ℹ️ PackageInstaller installation committed: sessionId=1266375904
2025-08-14 18:38:02.147 19136-19136 AppManager              com.dspread.mdm.service              I  ℹ️ 安装进度: 90%
2025-08-14 18:38:02.419 19136-19136 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 收到广播: android.intent.action.PACKAGE_ADDED
2025-08-14 18:38:02.434 19136-19136 Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 收到包更新广播: android.intent.action.PACKAGE_ADDED, 包名: com.lingjingnet.lazycat
2025-08-14 18:38:02.453 19136-19136 Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 应用安装: com.lingjingnet.lazycat
2025-08-14 18:38:02.461 19136-19136 Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 检查WebSocket状态: packageName=com.lingjingnet.lazycat, action=INSTALL
2025-08-14 18:38:02.467 19136-19136 Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 通知rulebase处理应用安装: com.lingjingnet.lazycat
2025-08-14 18:38:02.472 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 收到系统广播，应用已安装: com.lingjingnet.lazycat
2025-08-14 18:38:02.477 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 找到匹配的安装回调，触发: com.lingjingnet.lazycat
2025-08-14 18:38:02.482 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 安装回调被触发: pkg=com.lingjingnet.lazycat, returnCode=1, error=
2025-08-14 18:38:02.487 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 应用安装启动成功: com.lingjingnet.lazycat
2025-08-14 18:38:02.492 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 从广播更新应用状态: 168fee4bca5148a0be863080acad09ee, com.lingjingnet.lazycat -> B03
2025-08-14 18:38:02.497 19136-19136 WebSocket               com.dspread.mdm.service              I  🔧 C0107 主动式上送: rulebase_update (主动: 52)
2025-08-14 18:38:02.518 19136-19136 WebSocket               com.dspread.mdm.service              I  🔧 添加规则结果: ruleId=168fee4bca5148a0be863080acad09ee
2025-08-14 18:38:02.566 19136-19136 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0107","serialNo":"01354090202503050399","request_time":"1755167882511","request_id":"1755167882511C0107","version":"1","data":{"ruleId":"168fee4bca5148a0be863080acad09ee","taskResult":"R02","failedApkList":[{"packName":"mark.via","apkName":"Via","versionName":"6.6.0","versionCode":"20250713","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/4d256c4123b346fd817537b491342204.apk","apkMd5":"0d1225260d03e10a8ffc8409369c442a","apkSize":2565110,"installBy":"0","isSystemApp":false,"priority":0,"result":"B03","taskType":"01"},{"packName":"com.lingjingnet.lazycat","apkName":"懒猫趣播","versionName":"1.0.1","versionCode":"10","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/f03e70448bf9461abf1804e9da65b36f.apk","apkMd5":"accb937c84dcd0d346da7f8fee7bf0ae","apkSize":13140733,"installBy":"0","isSystemApp":false,"priority":0,"result":"B03","taskType":"01"},{"packName":"com.chileaf.cl960.sample","apkName":"cl960","versionName":"1.1.0","versionCode":"1","apkUrl":"","apkMd5":"","apkSize":0,"installBy":"0","isSystemApp":false,"priority":0,"result":"D02","taskType":"02"},{"packName":"plus.H52FFB9A5","apkName":"逍遥助手","versionName":"1.0","versionCode":"100","apkUrl":"","apkMd5":"","apkSize":0,"installBy":"0","isSystemApp":false,"priority":0,"result":"D02","taskType":"02"}]},"myVersionName":"1.0.08.20250814.DSPREAD.MDM.SERVICE","terminalDate":"20250814183802"}
2025-08-14 18:38:02.571 19136-19136 WebSocket               com.dspread.mdm.service              I  🔧 规则结果实时上送: ruleId=168fee4bca5148a0be863080acad09ee, result=R02 (1)
2025-08-14 18:38:02.576 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 上报规则执行中状态: 168fee4bca5148a0be863080acad09ee, 应用数量: 4
2025-08-14 18:38:02.581 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 开始检查规则完成状态: 168fee4bca5148a0be863080acad09ee, 总应用数: 4
2025-08-14 18:38:02.586 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 检查应用状态: mark.via -> B03
2025-08-14 18:38:02.591 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 发现成功状态: mark.via -> B03
2025-08-14 18:38:02.595 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 检查应用状态: com.lingjingnet.lazycat -> B03
2025-08-14 18:38:02.600 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 发现成功状态: com.lingjingnet.lazycat -> B03
2025-08-14 18:38:02.605 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 检查应用状态: com.chileaf.cl960.sample -> D02
2025-08-14 18:38:02.610 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 发现成功状态: com.chileaf.cl960.sample -> D02
2025-08-14 18:38:02.615 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 检查应用状态: plus.H52FFB9A5 -> D02
2025-08-14 18:38:02.620 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 发现成功状态: plus.H52FFB9A5 -> D02
2025-08-14 18:38:02.625 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 规则完成检查结果: 168fee4bca5148a0be863080acad09ee, 全部完成: true, 完成数: 4/4, 有失败: false
2025-08-14 18:38:02.630 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 规则执行全部成功，上送RULEBASED_SUCCESS: 168fee4bca5148a0be863080acad09ee
2025-08-14 18:38:02.635 19136-19136 WebSocket               com.dspread.mdm.service              I  🔧 C0107 主动式上送: rulebase_update (主动: 53)
2025-08-14 18:38:02.654 19136-19136 WebSocket               com.dspread.mdm.service              I  🔧 添加规则结果: ruleId=168fee4bca5148a0be863080acad09ee
2025-08-14 18:38:02.701 19136-19136 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0107","serialNo":"01354090202503050399","request_time":"1755167882648","request_id":"1755167882648C0107","version":"1","data":{"ruleId":"168fee4bca5148a0be863080acad09ee","taskResult":"R03","failedApkList":[{"packName":"mark.via","apkName":"Via","versionName":"6.6.0","versionCode":"20250713","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/4d256c4123b346fd817537b491342204.apk","apkMd5":"0d1225260d03e10a8ffc8409369c442a","apkSize":2565110,"installBy":"0","isSystemApp":false,"priority":0,"result":"B03","taskType":"01"},{"packName":"com.lingjingnet.lazycat","apkName":"懒猫趣播","versionName":"1.0.1","versionCode":"10","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/f03e70448bf9461abf1804e9da65b36f.apk","apkMd5":"accb937c84dcd0d346da7f8fee7bf0ae","apkSize":13140733,"installBy":"0","isSystemApp":false,"priority":0,"result":"B03","taskType":"01"},{"packName":"com.chileaf.cl960.sample","apkName":"cl960","versionName":"1.1.0","versionCode":"1","apkUrl":"","apkMd5":"","apkSize":0,"installBy":"0","isSystemApp":false,"priority":0,"result":"D02","taskType":"02"},{"packName":"plus.H52FFB9A5","apkName":"逍遥助手","versionName":"1.0","versionCode":"100","apkUrl":"","apkMd5":"","apkSize":0,"installBy":"0","isSystemApp":false,"priority":0,"result":"D02","taskType":"02"}]},"myVersionName":"1.0.08.20250814.DSPREAD.MDM.SERVICE","terminalDate":"20250814183802"}
2025-08-14 18:38:02.706 19136-19136 WebSocket               com.dspread.mdm.service              I  🔧 规则结果实时上送: ruleId=168fee4bca5148a0be863080acad09ee, result=R03 (1)
2025-08-14 18:38:02.711 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 上报规则状态: 168fee4bca5148a0be863080acad09ee -> R03
2025-08-14 18:38:02.716 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleStateMonitor 收到状态变化: 168fee4bca5148a0be863080acad09ee, R02 -> R03
2025-08-14 18:38:02.722 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleStateMonitor 状态变化已记录: 168fee4bca5148a0be863080acad09ee, R03
2025-08-14 18:38:02.727 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleStateMachine 状态转换成功: 168fee4bca5148a0be863080acad09ee, R02 -> R03
2025-08-14 18:38:02.729 19136-19207 Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"request_time":"1755167881681","org_request_time":"1755167882511","org_request_id":"1755167882511C0107","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755167881681S0000","serialNo":"01354090202503050399"}
2025-08-14 18:38:02.732 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 规则执行完成: 168fee4bca5148a0be863080acad09ee, 有失败: false
2025-08-14 18:38:02.736 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 收到响应消息: transCode=S0000, orgRequestId=1755167882511C0107, state=0, remark=
2025-08-14 18:38:02.737 19136-19136 Platform                com.dspread.mdm.service              D  🔧 重新收集应用信息（强制刷新）
2025-08-14 18:38:02.741 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 处理规则结果响应: state=0
2025-08-14 18:38:02.746 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 规则结果上传成功
2025-08-14 18:38:02.856 19136-19136 AppManager              com.dspread.mdm.service              I  ℹ️ 应用信息获取完成: 总数132(系统129/用户3) 返回3个
2025-08-14 18:38:02.861 19136-19207 Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"request_time":"1755167881830","org_request_time":"1755167882648","org_request_id":"1755167882648C0107","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755167881830S0000","serialNo":"01354090202503050399"}
2025-08-14 18:38:02.879 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 收到响应消息: transCode=S0000, orgRequestId=1755167882648C0107, state=0, remark=
2025-08-14 18:38:02.882 19136-19136 Platform                com.dspread.mdm.service              D  🔧 应用信息: 3 个应用
2025-08-14 18:38:02.886 19136-19136 Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 应用信息缓存刷新完成
2025-08-14 18:38:02.891 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 处理规则结果响应: state=0
2025-08-14 18:38:02.893 19136-19136 ContextImpl             com.dspread.mdm.service              W  Calling a method in the system process without a qualified user: android.app.ContextImpl.sendBroadcast:1220 android.content.ContextWrapper.sendBroadcast:504 com.dspread.mdm.service.broadcast.core.BroadcastSender.sendBroadcast:99 com.dspread.mdm.service.broadcast.core.BroadcastSender.sendBroadcast:23 com.dspread.mdm.service.broadcast.handlers.system.PackageUpdateEventHandlerImpl.sendAppRefreshBroadcast:491 
2025-08-14 18:38:02.895 19136-19136 Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 发送应用刷新广播
2025-08-14 18:38:02.913 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 规则结果上传成功
