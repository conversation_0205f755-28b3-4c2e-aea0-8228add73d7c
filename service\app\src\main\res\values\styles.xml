<?xml version="1.0" encoding="utf-8"?>
<resources>
    <style name="no_statusbar_activity">
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowFullscreen">true</item>
        <item name="android:windowNoTitle">true</item>
    </style>

    <!-- 地理围栏警告对话框样式 -->
    <style name="GeofenceWarningDialogStyle" parent="android:Theme.Dialog">
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:backgroundDimEnabled">true</item>
        <item name="android:backgroundDimAmount">0.6</item>
        <item name="android:windowAnimationStyle">@style/GeofenceDialogAnimation</item>
    </style>

    <!-- 对话框动画样式 -->
    <style name="GeofenceDialogAnimation">
        <item name="android:windowEnterAnimation">@anim/dialog_enter</item>
        <item name="android:windowExitAnimation">@anim/dialog_exit</item>
    </style>
</resources>