package com.bbpos.wiseapp.tms.utils;

import android.content.Context;
import android.content.SharedPreferences;
import android.os.AsyncTask;
import android.preference.PreferenceManager;
import android.text.TextUtils;
import android.util.Log;

import com.bbpos.wiseapp.logger.BBLog;
import com.bbpos.wiseapp.settings.utils.tlv.TLV;
import com.bbpos.wiseapp.settings.utils.tlv.TLVParser;
import com.bbpos.wiseapp.websocket.Constant;
import com.google.gson.Gson;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileReader;
import java.io.IOException;
import java.io.RandomAccessFile;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.bbpos.wiseapp.tms.utils.Constants.USAGE_LOG_FILE_PATH;


//    Filename: /sdcard/Android/Data/System/usages.log
//    Content: Date + Time + App package name + “:” + TLV
//    Tags:
//    Tag DF871A (ICC SUCC) = ATR can be read  insert
//    Tag DF871B (ICC FAIL) = if not
//    Tag DF871C (NFC SUCC) = ATR can be read  tap
//    Tag DF871D (NFC FAIL) = if not
//    Tag DF8718 (MSR SUCC) = LRC correct      swipe
//    Tag DF8719 (MSR FAIL) = if not
//    Tag DF8645 (WC Tamper Reason)

/**
 * 获取usage log 数据
 */
public class UsageDataManager {
    /***  tag  type  **/
    public static final String TAG_DF871A = "DF871A";//ICC SUCC
    public static final String TAG_DF871B = "DF871B";//ICC FAIL
    public static final String TAG_DF871C = "DF871C";//NFC SUCC
    public static final String TAG_DF871D = "DF871D";//NFC FAIL
    public static final String TAG_DF8718 = "DF8718";//MSR SUCC
    public static final String TAG_DF8719 = "DF8719";//MSR FAIL
    public static final String TAG_DF8645 = "DF8645";//WC Tamper Reason,弃用
    public static final String TAG_DF8551 = "DF8551";//SP internal Tamper
    public static final String TAG_DF854C = "DF854C";// WC internal Tamper

    public static SharedPreferences sp ;

    private long lastUpdateTime = -1;
    private AsyncTask  task;
    private static Context mContext;
    private boolean isReturnUsageData= false;

    private static final UsageDataManager instance = new UsageDataManager();
    private UsageDataManager(){}

    public static UsageDataManager getInstance(Context context){
        if (mContext == null)
            mContext = context;
        sp = PreferenceManager.getDefaultSharedPreferences(context);
        return instance;
    }

    private File getUsageLogFile(){
        return new File(USAGE_LOG_FILE_PATH);
    }

    private boolean checkIsFileExist(Context context){
        return getUsageLogFile().exists();
    }

    private long getUsageLogFileModifyTime(Context context){
        long lastUpdateTime = 0;
        if (checkIsFileExist(context)) {
            lastUpdateTime = getUsageLogFile().lastModified();
        }
        return lastUpdateTime;
    }

    /**
     * 1.取log数据，取完后删除log文件
     * 2、取sp中缓存的上次读取数据
     * 3、数据进行整合
     * 4、返回最新数据，并缓存
     *
     */
    public Map<String,Integer> getUsageLogData(){
        Map<String,Integer> result = new HashMap<>();
        Map<String,Integer> usageDataMapFromLog = new HashMap<>();
        Map<String,Integer> usageDataMapFromCache = new HashMap<>();
        boolean isExist = checkIsFileExist(mContext);
        if (isExist){
            usageDataMapFromLog = getUsageDataFromLogFile();
        }else {
            usageDataMapFromCache = getUsageDataFromCache();
        }

        Gson gson = new Gson();
        BBLog.d(Constants.TAG, "usageDataMapFromLog: "+gson.toJson(usageDataMapFromLog));
        BBLog.d(Constants.TAG, "usageDataMapFromCache: "+gson.toJson(usageDataMapFromCache));

        if (usageDataMapFromCache!=null && usageDataMapFromCache.size()>0
                && usageDataMapFromLog!=null && usageDataMapFromLog.size()>0) {
            //针对每项的数值进行比较，取最大的数
            for (Map.Entry<String, Integer> entry1 : usageDataMapFromCache.entrySet()) {
                Integer m1value = entry1.getValue() == null ? 0 : entry1.getValue();
                Integer m2value = usageDataMapFromLog.get(entry1.getKey()) == null ? 0 : usageDataMapFromLog.get(entry1.getKey());

                BBLog.d(Constants.TAG, String.format("m1value: %d  ---  m2value: %d", m1value,m2value));

                if (m1value >= m2value) {
                    result.put(entry1.getKey(), m1value);
                } else {
                    result.put(entry1.getKey(), m2value);
                }
            }

        }else {
            if (usageDataMapFromLog!=null && (usageDataMapFromCache == null ||usageDataMapFromCache.size() == 0)){
                result = usageDataMapFromLog;
            }else if (usageDataMapFromCache!=null && (usageDataMapFromLog == null || usageDataMapFromLog.size() == 0)){
                result = usageDataMapFromCache;
            }
        }

        //保持最新的数据
        if (result.size() != 0) {
            BBLog.d(Constants.TAG, "sp save data : "+result.size());
            sp.edit().putString(SPKeys.KEY_USAGE_LOG, gson.toJson(result)).commit();
        }
        BBLog.d(Constants.TAG, "sp save data : "+gson.toJson(result));
        return result;
    }


    private Map<String,Integer> getUsageDataFromLogFile() {
        Map<String,Integer> usageDataMap = new HashMap<>();
//        long lastModify = getUsageLogFileModifyTime(mContext);
//        if (lastUpdateTime!=lastModify){
//            lastUpdateTime = lastModify;
            File file = getUsageLogFile();
            //获取文本最后一行内容
            String lineTemp = readLastLine(file,"utf-8");
            if (lineTemp != null){
                //2019-06-12 11:09:46.924 com.bbpos.bbdevice.wsp7xexample : DF871A040000003EDF871B0400000019DF871C0400000003DF871D0400000000
                lineTemp = lineTemp.substring(lineTemp.lastIndexOf(":")+1,lineTemp.length()-1).trim();//截取有效信息
                List<TLV> parseResult = TLVParser.parse(lineTemp);
                if (parseResult != null && parseResult.size() > 0) {
                    for (TLV tlv : parseResult){
                        if (tlv.tag.toUpperCase().startsWith(TAG_DF871A)){
                            usageDataMap.put(TAG_DF871A, TLVParser.getTLVValue(tlv));
                        }else if (tlv.tag.toUpperCase().startsWith(TAG_DF871B)){
                            usageDataMap.put(TAG_DF871B, TLVParser.getTLVValue(tlv));
                        }else if (tlv.tag.toUpperCase().startsWith(TAG_DF871C)){
                            usageDataMap.put(TAG_DF871C, TLVParser.getTLVValue(tlv));
                        }else if (tlv.tag.toUpperCase().startsWith(TAG_DF871D)){
                            usageDataMap.put(TAG_DF871D, TLVParser.getTLVValue(tlv));
                        }else if (tlv.tag.toUpperCase().startsWith(TAG_DF8718)){
                            usageDataMap.put(TAG_DF8718, TLVParser.getTLVValue(tlv));
                        }else if (tlv.tag.toUpperCase().startsWith(TAG_DF8719)){
                            usageDataMap.put(TAG_DF8719, TLVParser.getTLVValue(tlv));
                        }else if (tlv.tag.toUpperCase().startsWith(TAG_DF8645)){
                            usageDataMap.put(TAG_DF8645, TLVParser.getTLVValue(tlv));
                        }else if (tlv.tag.toUpperCase().startsWith(TAG_DF854C)){
                            usageDataMap.put(TAG_DF854C,TLVParser.getTLVValue(tlv));
                        }else if (tlv.tag.toUpperCase().startsWith(TAG_DF8551)){
                            usageDataMap.put(TAG_DF8551,TLVParser.getTLVValue(tlv));
                        }
                    }
                }
            }
//            BufferedReader bufferedReader = null;
//            String lineTemp = null;
//            try {
//                bufferedReader = new BufferedReader(new FileReader(file));
//                while ((lineTemp = bufferedReader.readLine())!=null){
//                    lineTemp = lineTemp.replace("Tag ","").trim();
//                    //2019-06-12 11:09:46.924 com.bbpos.bbdevice.wsp7xexample : DF871A040000003EDF871B0400000019DF871C0400000003DF871D0400000000
//                    lineTemp = lineTemp.substring(lineTemp.lastIndexOf(":"),lineTemp.length()-1).trim();
//                    if (lineTemp.startsWith("DF871A")){
//                        hanlderTLV(lineTemp, usageDataMap, "DF871A");
//                    }else if (lineTemp.startsWith("DF871B")){
//                        hanlderTLV(lineTemp, usageDataMap, "DF871B");
//                    }else if (lineTemp.startsWith("DF871C")){
//                        hanlderTLV(lineTemp, usageDataMap, "DF871C");
//                    }else if (lineTemp.startsWith("DF871D")){
//                        hanlderTLV(lineTemp, usageDataMap, "DF871D");
//                    }else if (lineTemp.startsWith("DF8718")){
//                        hanlderTLV(lineTemp, usageDataMap, "DF8718");
//                    }else if (lineTemp.startsWith("DF8719")){
//                        hanlderTLV(lineTemp, usageDataMap, "DF8719");
//                    }
//                }
                //当文件存在超过7天时，删除文件，避免后期文件体积增大导致读取超时；已读取数据可以保存下来
                if (getUsageLogFile()!=null && FileUtils.justTime(FileUtils.getFileCreateDate(getUsageLogFile()),7)) {
                    getUsageLogFile().delete();
                }
//            } catch (IOException e) {
//                e.printStackTrace();
//            }
//        }
        return usageDataMap;
    }

    /**
     * 读取上次 记录
     * @return
     */
    private Map<String,Integer> getUsageDataFromCache() {
        Map<String,Integer> usageDataMap = new HashMap<>();
        String cachedata = sp.getString(SPKeys.KEY_USAGE_LOG,"");
        BBLog.d("cached", "getUsageDataFromCache: "+cachedata);
        Gson gson = new Gson();
        if (!TextUtils.isEmpty(cachedata)){
            usageDataMap = (HashMap<String,Integer>)  gson.fromJson(cachedata, Map.class);
        }
        return usageDataMap;
    }

    private void hanlderTLV(String lineTemp, Map<String, Integer> item, String tlvTag) {
        List<TLV> parseResult = TLVParser.parse(lineTemp);
        if (parseResult != null && parseResult.size() > 0) {
            item.put(tlvTag, TLVParser.getTLVValue(parseResult.get(0)));
        }
    }

    /**
     * 读取大文件最后一行文本数据
     * @param file
     * @param charset
     * @return
     * @throws IOException
     */
    public static String readLastLine(File file, String charset) {
        if (!file.exists() || file.isDirectory() || !file.canRead()) {
            return null;
        }
        RandomAccessFile raf = null;
        try {
            raf = new RandomAccessFile(file, "r");
            long len = raf.length();
            if (len == 0L) {
                return null;
            } else {
                long pos = len - 1;
                while (pos > 0) {
                    pos--;
                    raf.seek(pos);
                    if (raf.readByte() == '\n') {
                        break;
                    }
                }
                if (pos == 0) {
                    raf.seek(0);
                }
                byte[] bytes = new byte[(int) (len - pos)];
                raf.read(bytes);
                if (charset == null) {
                    return new String(bytes);
                } else {
                    return new String(bytes, charset);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (raf != null) {
                try {
                    raf.close();
                } catch (Exception e2) {
                }
            }
        }
        return null;
    }
}
