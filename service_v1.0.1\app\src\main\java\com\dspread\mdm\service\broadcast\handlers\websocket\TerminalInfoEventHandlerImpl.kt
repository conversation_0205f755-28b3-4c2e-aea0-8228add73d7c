package com.dspread.mdm.service.broadcast.handlers.websocket

import android.app.AlarmManager
import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import com.dspread.mdm.service.network.websocket.WebSocketCenter
import com.dspread.mdm.service.network.websocket.message.WsMessageSender
import com.dspread.mdm.service.broadcast.core.BroadcastActions
import com.dspread.mdm.service.broadcast.core.BroadcastEventHandler
import com.dspread.mdm.service.utils.log.Logger
import com.dspread.mdm.service.utils.storage.PreferencesManager
import com.dspread.mdm.service.config.TimerConfig

/**
 * 终端信息上传事件处理器
 * 统一处理终端信息上传相关的广播和定时器管理
 */
class TerminalInfoEventHandlerImpl : BroadcastEventHandler {
    
    companion object {
        private const val TAG = "TerminalInfoEventHandler"
        
        // 默认终端信息上传间隔（24小时）
        private const val DEFAULT_TER_INFO_UPLOAD_INTERVAL = 24 * 60 * 60L // 24小时，单位：秒
        
        // SharedPreferences键
        private const val TER_INFO_UPLOAD_INTERVAL_KEY = "ter_info_upload_interval"
        private const val FLAG_LAST_TER_INFO_UPLOAD_ALIVE = "flag_last_ter_info_upload_alive"
    }
    
    override fun getHandlerName(): String = TAG
    
    override fun getSupportedActions(): List<String> {
        return listOf(BroadcastActions.TER_INFO_UPLOAD_BC)
    }
    
    override fun handleBroadcast(context: Context, intent: Intent): Boolean {
        val action = intent.action ?: return false
        
        return try {
            when (action) {
                BroadcastActions.TER_INFO_UPLOAD_BC -> {
                    handleTerminalInfoUpload(context)
                    true
                }
                else -> false
            }
        } catch (e: Exception) {
            Logger.receiverE("$TAG 处理广播失败: $action", e)
            false
        }
    }
    
    /**
     * 处理终端信息上传
     */
    private fun handleTerminalInfoUpload(context: Context) {
        try {
            // 安排下一次上传（保持定时器持续运行）
            scheduleNextUpload(context)
            
            // 执行终端信息上传
            performTerminalInfoUpload(context)
            
            // 更新状态标志
            PreferencesManager.putString(FLAG_LAST_TER_INFO_UPLOAD_ALIVE, "false")
            
        } catch (e: Exception) {
            Logger.receiverE("$TAG 处理终端信息上传失败", e)
        }
    }
    
    /**
     * 启动终端信息上传定时器
     */
    fun startTerminalInfoUploadTimer(context: Context) {
        try {
            val interval = getTerInfoUploadInterval(context)
            Logger.receiver("$TAG 启动终端信息上传定时器: ${interval}s")
            
            // 直接安排第一次终端信息上传，不需要立即发送广播
            scheduleNextUpload(context)
            
        } catch (e: Exception) {
            Logger.receiverE("$TAG 启动终端信息上传定时器失败", e)
        }
    }
    
    /**
     * 停止终端信息上传定时器
     */
    fun stopTerminalInfoUploadTimer(context: Context) {
        try {
            val intent = Intent(BroadcastActions.TER_INFO_UPLOAD_BC).apply {
                setPackage(context.packageName)
            }
            val pendingIntent = PendingIntent.getBroadcast(
                context,
                0,
                intent,
                PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
            )
            
            val alarmManager = context.getSystemService(Context.ALARM_SERVICE) as AlarmManager
            alarmManager.cancel(pendingIntent)
            
            Logger.receiver("$TAG 终端信息上传定时器已停止")
            
        } catch (e: Exception) {
            Logger.receiverE("$TAG 停止终端信息上传定时器失败", e)
        }
    }
    
    /**
     * 安排下一次终端信息上传
     */
    fun scheduleNextUpload(context: Context) {
        try {
            val interval = getTerInfoUploadInterval(context)
            
            val intent = Intent(BroadcastActions.TER_INFO_UPLOAD_BC).apply {
                setPackage(context.packageName)
            }
            val pendingIntent = PendingIntent.getBroadcast(
                context,
                0,
                intent,
                PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
            )
            
            val alarmManager = context.getSystemService(Context.ALARM_SERVICE) as AlarmManager
            val triggerTime = System.currentTimeMillis() + (interval * 1000)
            
            // 取消之前的定时器
            alarmManager.cancel(pendingIntent)
            
            // 使用精确的闹钟
            alarmManager.setExact(
                AlarmManager.RTC_WAKEUP,
                triggerTime,
                pendingIntent
            )
            
            Logger.success("⏰ 设置终端信息定时器成功，下次执行: ${interval}秒后 (${interval/60}分钟)")
            
        } catch (e: Exception) {
            Logger.receiverE("$TAG 安排下一次终端信息上传失败", e)
        }
    }
    
    /**
     * 获取终端信息上传间隔（支持调试模式和生产模式）
     */
    private fun getTerInfoUploadInterval(context: Context): Long {
        return TimerConfig.getTerminalInfoInterval(context)
    }
    
    /**
     * 执行终端信息上传
     */
    private fun performTerminalInfoUpload(context: Context) {
        try {
            // 只有在WebSocket连接成功时才执行上传
            if (WebSocketCenter.isConnected()) {
                // 发送C0109终端信息
                WsMessageSender.uploadTerminalInfo()
                Logger.receiver("$TAG 终端信息上传完成")
            } else {
                Logger.receiver("$TAG WebSocket未连接，跳过终端信息上传")
            }
            
        } catch (e: Exception) {
            Logger.receiverE("$TAG 执行终端信息上传失败", e)
        }
    }
}
