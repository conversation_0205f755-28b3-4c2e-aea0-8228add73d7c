package com.bbpos.wiseapp.tms.model;

public class AppInfo {
    private String apk_name;
    private String package_name;
    private String version_name;
    private String version_code;
    private String icon_url;
    private String install_path;
    private String error_msg;
    private CrashInfo crash_info;
    private String crash_count;

    public String getApk_name() {
        return apk_name;
    }

    public void setApk_name(String apk_name) {
        this.apk_name = apk_name;
    }

    public String getVersion_name() {
        return version_name;
    }

    public void setVersion_name(String version_name) {
        this.version_name = version_name;
    }

    public String getVersion_code() {
        return version_code;
    }

    public void setVersion_code(String version_code) {
        this.version_code = version_code;
    }

    public String getIcon_url() {
        return icon_url;
    }

    public void setIcon_url(String icon_url) {
        this.icon_url = icon_url;
    }

    public String getInstall_path() {
        return install_path;
    }

    public void setInstall_path(String install_path) {
        this.install_path = install_path;
    }

    public String getPackage_name() {
        return package_name;
    }

    public void setPackage_name(String package_name) {
        this.package_name = package_name;
    }

    public CrashInfo getCrash_info() {
        return crash_info;
    }

    public void setCrash_info(CrashInfo crash_info) {
        this.crash_info = crash_info;
    }

    public String getCrash_Count() {
        return crash_count;
    }

    public void setCrash_Count(String crash_count) {
        this.crash_count = crash_count;
    }

    public String toString() {
        StringBuffer sb = new StringBuffer();
        sb.append("Crash App Info:");
        sb.append("[apkName] " + getApk_name());
        sb.append(", ");
        sb.append("[packageName] " + getPackage_name());
        sb.append(", ");
        sb.append("[versionName] " + getVersion_name());
        sb.append(", ");
        sb.append("[versionCode] " + getVersion_code());
        sb.append(", ");
        sb.append("[crashCount] " + getCrash_Count());
        sb.append(", ");
        sb.append("[crashTime] " + getCrash_info().getDate());
        sb.append(", ");
        sb.append("[crashInfo] " + getCrash_info().getInfo());
        return sb.toString();
    }

    public String getError_msg() {
        return error_msg;
    }

    public void setError_msg(String error_msg) {
        this.error_msg = error_msg;
    }
}
