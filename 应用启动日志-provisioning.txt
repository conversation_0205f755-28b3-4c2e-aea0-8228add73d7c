2025-08-20 18:24:22.693  3782-3782  Receiver                com.dspread.mdm.service              D  🔧 屏幕点亮
2025-08-20 18:24:22.736  3782-3782  WebSocket               com.dspread.mdm.service              W  ⚠️ 未知触发条件: screen_on，默认按被动式处理
2025-08-20 18:24:22.761  3782-3782  WebSocket               com.dspread.mdm.service              I  🔧 C0201 被动式上送拒绝: screen_on (拒绝: 3)
2025-08-20 18:24:22.768  3782-3782  WebSocket               com.dspread.mdm.service              I  🔧 C0201 设备状态上送被流量控制阻止: 被动事件 'screen_on' 在平衡模式 - 重要变化下未启用
2025-08-20 18:24:22.775  3782-3782  Receiver                com.dspread.mdm.service              D  🔧 屏幕点亮处理完成
2025-08-20 18:24:24.391  3782-3782  getAnimDeal             com.dspread.mdm.service              I  mode:3 packageName:com.dspread.mdm.service result: false
2025-08-20 18:24:24.430   646-1923  BufferQueueDebug        surfaceflinger                       E  [105bf8f ActivityRecordInputSink com.dspread.mdm.service/.ui.activity.TestActivity#265](this:0xa5782c40,id:-1,api:0,p:-1,c:-1) id info cannot be read from '105bf8f ActivityRecordInputSink com.dspread.mdm.service/.ui.activity.TestActivity#265'
2025-08-20 18:24:24.448   646-1594  BufferQueueDebug        surfaceflinger                       E  [com.dspread.mdm.service/com.dspread.mdm.service.ui.activity.TestActivity#266](this:0xa56f0c40,id:-1,api:0,p:-1,c:-1) id info cannot be read from 'com.dspread.mdm.service/com.dspread.mdm.service.ui.activity.TestActivity#266'
2025-08-20 18:24:24.468  3782-3782  BufferQueueConsumer     com.dspread.mdm.service              D  [](id:ec600000001,api:0,p:-1,c:3782) connect: controlledByApp=false
2025-08-20 18:24:24.475  3782-3809  OpenGLRenderer          com.dspread.mdm.service              E  Unable to match the desired swap behavior.
2025-08-20 18:24:24.511  3782-3809  BLASTBufferQueue        com.dspread.mdm.service              D  [VRI[TestActivity]#1](f:0,a:1) acquireNextBufferLocked size=480x854 mFrameNumber=1 applyTransaction=true mTimestamp=1933707577576(auto) mPendingTransactions.size=0 graphicBufferId=16243566313487 transform=3
2025-08-20 18:24:25.877  3782-3982  WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 5 (第5个，待响应: 1)
2025-08-20 18:24:26.330  3782-3981  WebSocket               com.dspread.mdm.service              I  🔧 收到服务器 PONG 帧: 5 (待响应PING: 0)
---------------------------- PROCESS ENDED (3782) for package com.dspread.mdm.service ----------------------------
2025-08-20 18:24:39.389   646-1322  BufferQueueDebug        surfaceflinger                       E  [ActivityRecord{4a7e2c5 u0 com.dspread.mdm.service/.ui.activity.TestActivity#297](this:0xa594ac40,id:-1,api:0,p:-1,c:-1) id info cannot be read from 'ActivityRecord{4a7e2c5 u0 com.dspread.mdm.service/.ui.activity.TestActivity#297'
2025-08-20 18:24:39.411   646-1322  BufferQueueDebug        surfaceflinger                       E  [5a8e641 Splash Screen com.dspread.mdm.service#298](this:0xa5947c40,id:-1,api:0,p:-1,c:-1) id info cannot be read from '5a8e641 Splash Screen com.dspread.mdm.service#298'
2025-08-20 18:24:39.427   646-938   BufferQueueDebug        surfaceflinger                       E  [Splash Screen com.dspread.mdm.service#299](this:0xa58d3c40,id:-1,api:0,p:-1,c:-1) id info cannot be read from 'Splash Screen com.dspread.mdm.service#299'
2025-08-20 18:24:39.460   646-1322  BufferQueueDebug        surfaceflinger                       E  [63bf53c ActivityRecordInputSink com.dspread.mdm.service/.ui.activity.TestActivity#303](this:0xa5836c40,id:-1,api:0,p:-1,c:-1) id info cannot be read from '63bf53c ActivityRecordInputSink com.dspread.mdm.service/.ui.activity.TestActivity#303'
---------------------------- PROCESS STARTED (4108) for package com.dspread.mdm.service ----------------------------
2025-08-20 18:24:39.631  4108-4108  ziparchive              com.dspread.mdm.service              W  Unable to open '/data/app/~~lPSZvnYutkV0TnUHftC7ow==/com.dspread.mdm.service-9r9Vj4Bjdjwlf9CRn71heg==/base.dm': No such file or directory
2025-08-20 18:24:40.052  4108-4108  nativeloader            com.dspread.mdm.service              D  Configuring clns-4 for other apk /data/app/~~lPSZvnYutkV0TnUHftC7ow==/com.dspread.mdm.service-9r9Vj4Bjdjwlf9CRn71heg==/base.apk. target_sdk_version=34, uses_libraries=, library_path=/data/app/~~lPSZvnYutkV0TnUHftC7ow==/com.dspread.mdm.service-9r9Vj4Bjdjwlf9CRn71heg==/lib/arm, permitted_path=/data:/mnt/expand:/data/user/0/com.dspread.mdm.service
2025-08-20 18:24:40.078  4108-4108  GraphicsEnvironment     com.dspread.mdm.service              V  Currently set values for:
2025-08-20 18:24:40.078  4108-4108  GraphicsEnvironment     com.dspread.mdm.service              V    angle_gl_driver_selection_pkgs=[]
2025-08-20 18:24:40.078  4108-4108  GraphicsEnvironment     com.dspread.mdm.service              V    angle_gl_driver_selection_values=[]
2025-08-20 18:24:40.080  4108-4108  GraphicsEnvironment     com.dspread.mdm.service              V  ANGLE GameManagerService for com.dspread.mdm.service: false
2025-08-20 18:24:40.080  4108-4108  GraphicsEnvironment     com.dspread.mdm.service              V  com.dspread.mdm.service is not listed in per-application setting
2025-08-20 18:24:40.080  4108-4108  GraphicsEnvironment     com.dspread.mdm.service              V  Neither updatable production driver nor prerelease driver is supported.
2025-08-20 18:24:40.107  4108-4108  Compatibil...geReporter com.dspread.mdm.service              D  Compat change id reported: 183155436; UID 1000; state: ENABLED
2025-08-20 18:24:40.154  4108-4108  MbrainDebugManagerImpl  com.dspread.mdm.service              D  getService failed
2025-08-20 18:24:40.210  4108-4108  Common                  com.dspread.mdm.service              D  🔧 日志配置：生产环境模式
2025-08-20 18:24:40.221  4108-4108  Common                  com.dspread.mdm.service              D  🔧 [DebugConfig] 调试模式已禁用
2025-08-20 18:24:40.227  4108-4108  Common                  com.dspread.mdm.service              D  🔧 SmartMdmServiceApp: 统一配置管理器初始化完成
2025-08-20 18:24:40.235  4108-4108  Common                  com.dspread.mdm.service              D  🔧 SmartMdmServiceApp: 系统目录初始化完成 - /data/pos/config/
2025-08-20 18:24:40.283  4108-4108  Receiver                com.dspread.mdm.service              D  🔧 ServiceGuardEventHandler 启动服务守护定时器: 120s
2025-08-20 18:24:40.291  4108-4108  Compatibil...geReporter com.dspread.mdm.service              D  Compat change id reported: 160794467; UID 1000; state: ENABLED
2025-08-20 18:24:40.297  4108-4108  Common                  com.dspread.mdm.service              I  ✅ ⏰ 设置服务守护定时器成功，下次执行: 120秒后 (2分钟)
2025-08-20 18:24:40.303  4108-4108  Common                  com.dspread.mdm.service              D  🔧 SmartMdmServiceApp: 服务守护定时器启动成功
2025-08-20 18:24:40.313  4108-4108  Common                  com.dspread.mdm.service              D  🔧 ServiceStartupManager 收到服务启动请求: APPLICATION_STARTUP, 强制启动: false
2025-08-20 18:24:40.324  4108-4108  Common                  com.dspread.mdm.service              D  🔧 ServiceStartupManager 开始执行服务启动: APPLICATION_STARTUP
2025-08-20 18:24:40.330  4108-4108  Common                  com.dspread.mdm.service              D  🔧 ServiceManager: 准备启动后台服务
2025-08-20 18:24:40.336  4108-4108  ContextImpl             com.dspread.mdm.service              W  Calling a method in the system process without a qualified user: android.app.ContextImpl.startService:1899 android.content.ContextWrapper.startService:825 com.dspread.mdm.service.platform.manager.ServiceManager.startBackgroundService:20 com.dspread.mdm.service.platform.manager.ServiceStartupManager.performServiceStart:121 com.dspread.mdm.service.platform.manager.ServiceStartupManager.startService:66 
2025-08-20 18:24:40.341  4108-4108  Common                  com.dspread.mdm.service              D  🔧 ServiceManager: 后台服务启动成功，ComponentName: ComponentInfo{com.dspread.mdm.service/com.dspread.mdm.service.services.SmartMdmBackgroundService}
2025-08-20 18:24:41.351  4108-4108  Common                  com.dspread.mdm.service              D  🔧 ServiceStartupManager 服务启动验证成功: APPLICATION_STARTUP
2025-08-20 18:24:41.357  4108-4108  Common                  com.dspread.mdm.service              D  🔧 ServiceStartupManager 服务启动成功: APPLICATION_STARTUP
2025-08-20 18:24:41.362  4108-4108  Common                  com.dspread.mdm.service              D  🔧 SmartMdmServiceApp: 主服务启动请求已提交
2025-08-20 18:24:41.368  4108-4108  Common                  com.dspread.mdm.service              D  🔧 SmartMdmServiceApp: Application启动完成，包名: com.dspread.mdm.service
2025-08-20 18:24:41.382  4108-4108  M-ProMotion             com.dspread.mdm.service              I  M-ProMotion is disabled
2025-08-20 18:24:41.450  4108-4108  Compatibil...geReporter com.dspread.mdm.service              D  Compat change id reported: 210923482; UID 1000; state: ENABLED
2025-08-20 18:24:41.485  4108-4108  Compatibil...geReporter com.dspread.mdm.service              D  Compat change id reported: 171228096; UID 1000; state: ENABLED
2025-08-20 18:24:41.542  4108-4108  getAnimDeal             com.dspread.mdm.service              I  mode:3 packageName:com.dspread.mdm.service result: false
2025-08-20 18:24:41.550  4108-4134  PowerHalWrapper         com.dspread.mdm.service              I  PowerHalWrapper.getInstance 
2025-08-20 18:24:41.550  4108-4108  SurfaceFactory          com.dspread.mdm.service              I  [static] sSurfaceFactory = com.mediatek.view.impl.SurfaceFactoryImpl@728e83d
2025-08-20 18:24:41.556  4108-4108  Compatibil...geReporter com.dspread.mdm.service              D  Compat change id reported: 237531167; UID 1000; state: DISABLED
2025-08-20 18:24:41.561  4108-4108  OpenGLRenderer          com.dspread.mdm.service              W  Unknown dataspace 0
2025-08-20 18:24:41.566  4108-4108  VRI[TestActivity]       com.dspread.mdm.service              D  hardware acceleration = true, forceHwAccelerated = false
2025-08-20 18:24:41.571   646-939   BufferQueueDebug        surfaceflinger                       E  [be513a0 com.dspread.mdm.service/com.dspread.mdm.service.ui.activity.TestActivity#305](this:0xa58d7c40,id:-1,api:0,p:-1,c:-1) id info cannot be read from 'be513a0 com.dspread.mdm.service/com.dspread.mdm.service.ui.activity.TestActivity#305'
2025-08-20 18:24:41.579  4108-4108  InputTransport          com.dspread.mdm.service              D  Create ARC handle: 0xb1938670
2025-08-20 18:24:41.579  4108-4108  InputEventReceiver      com.dspread.mdm.service              D  Input log is disabled in InputEventReceiver.
2025-08-20 18:24:41.579  4108-4108  InputTransport          com.dspread.mdm.service              D  Input log is disabled in InputChannel.
2025-08-20 18:24:41.583  4108-4108  Choreographer           com.dspread.mdm.service              I  Skipped 85 frames!  The application may be doing too much work on its main thread.
2025-08-20 18:24:41.628   646-939   BufferQueueDebug        surfaceflinger                       E  [com.dspread.mdm.service/com.dspread.mdm.service.ui.activity.TestActivity#306](this:0xa58d5c40,id:-1,api:0,p:-1,c:-1) id info cannot be read from 'com.dspread.mdm.service/com.dspread.mdm.service.ui.activity.TestActivity#306'
2025-08-20 18:24:41.644  4108-4108  BufferQueueConsumer     com.dspread.mdm.service              D  [](id:100c00000000,api:0,p:-1,c:4108) connect: controlledByApp=false
2025-08-20 18:24:41.679  4108-4135  OpenGLRenderer          com.dspread.mdm.service              E  Unable to match the desired swap behavior.
2025-08-20 18:24:41.686  4108-4139  ion                     com.dspread.mdm.service              E  ioctl c0044901 failed with code -1: Invalid argument
2025-08-20 18:24:41.796  4108-4135  BLASTBufferQueue        com.dspread.mdm.service              D  [VRI[TestActivity]#0](f:0,a:1) acquireNextBufferLocked size=480x854 mFrameNumber=1 applyTransaction=true mTimestamp=1950993366192(auto) mPendingTransactions.size=0 graphicBufferId=17643725651973 transform=3
2025-08-20 18:24:41.802   646-1322  BufferQueueDebug        surfaceflinger                       E  [Surface(name=be513a0 com.dspread.mdm.service/com.dspread.mdm.service.ui.activity.TestActivity)/@0x126b7f6 - animation-leash of starting_reveal#309](this:0xa5883c40,id:-1,api:0,p:-1,c:-1) id info cannot be read from 'Surface(name=be513a0 com.dspread.mdm.service/com.dspread.mdm.service.ui.activity.TestActivity)/@0x126b7f6 - animation-leash of starting_reveal#309'
2025-08-20 18:24:41.813  4108-4108  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 服务启动
2025-08-20 18:24:41.816  4108-4145  OpenGLRenderer          com.dspread.mdm.service              I  Davey! duration=1630ms; Flags=1, FrameTimelineVsyncId=59296, IntendedVsync=1949362178349, Vsync=1950781518379, InputEventId=0, HandleInputStart=1950782979807, AnimationStart=1950783003423, PerformTraversalsStart=1950783594884, DrawStart=1950878504731, FrameDeadline=1949387678349, FrameInterval=1950781658577, FrameStartTime=16698118, SyncQueued=1950892578269, SyncStart=1950894954192, IssueDrawCommandsStart=1950895414884, SwapBuffers=1950992074500, FrameCompleted=1950995456346, DequeueBufferDuration=0, QueueBufferDuration=1160076, GpuCompleted=1950995456346, SwapBuffersCompleted=1950994957654, DisplayPresentTime=0, CommandSubmissionCompleted=1950992074500, 
2025-08-20 18:24:41.848  4108-4108  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 开始服务初始化
2025-08-20 18:24:41.855  4108-4108  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] Android 14+: 初始化DSPREAD服务
2025-08-20 18:24:41.863  4108-4108  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 服务创建完成
2025-08-20 18:24:41.864  4108-4146  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 后台线程：初始化DSPREAD服务
2025-08-20 18:24:41.872  4108-4108  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] onStartCommand调用
2025-08-20 18:24:41.874  4108-4146  Platform                com.dspread.mdm.service              D  🔧 DspreadService 开始初始化DSPREAD服务
2025-08-20 18:24:41.885  4108-4146  ContextImpl             com.dspread.mdm.service              W  Calling a method in the system process without a qualified user: android.app.ContextImpl.bindService:1988 android.content.ContextWrapper.bindService:861 com.dspread.mdm.service.services.DspreadService.bindSysService:111 com.dspread.mdm.service.services.DspreadService.initialize:63 com.dspread.mdm.service.services.SmartMdmBackgroundService.initializeDspreadServiceAsync$lambda$1:227 
2025-08-20 18:24:41.885  4108-4108  Platform                com.dspread.mdm.service              D  🔧 WakeLockManager 获取智能WakeLock成功，持续时间: 60000ms
2025-08-20 18:24:41.899  4108-4146  Platform                com.dspread.mdm.service              D  🔧 DspreadService 系统服务绑定请求已发送
2025-08-20 18:24:41.899  4108-4108  Platform                com.dspread.mdm.service              D  🔧 WakeLockManager 设置WakeLock续期定时器: 60000ms
2025-08-20 18:24:41.943  4108-4108  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 初始化基础组件
2025-08-20 18:24:41.952  4108-4108  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 开始初始化...
2025-08-20 18:24:41.972  4108-4108  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 包更新广播接收器注册完成
2025-08-20 18:24:41.979  4108-4108  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 统一广播接收器注册完成
2025-08-20 18:24:41.985  4108-4108  Common                  com.dspread.mdm.service              I  ✅ BroadcastManager 初始化完成
2025-08-20 18:24:42.046  4108-4147  Platform                com.dspread.mdm.service              D  🔧 DeviceInfoApi 单例实例已创建
2025-08-20 18:24:42.055  4108-4108  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 服务初始化完成
2025-08-20 18:24:42.077  4108-4108  Platform                com.dspread.mdm.service              D  🔧 DspreadService 系统服务连接成功: ComponentInfo{com.dspread.sdkservice/com.dspread.sdkservice.SDKSysService}
2025-08-20 18:24:42.085  4108-4108  Platform                com.dspread.mdm.service              D  🔧 DspreadService 系统服务绑定完成
2025-08-20 18:24:42.095  4108-4146  ContextImpl             com.dspread.mdm.service              W  Calling a method in the system process without a qualified user: android.app.ContextImpl.bindService:1988 android.content.ContextWrapper.bindService:861 com.dspread.mdm.service.services.DspreadService.bindDevService:166 com.dspread.mdm.service.services.DspreadService.initialize:66 com.dspread.mdm.service.services.SmartMdmBackgroundService.initializeDspreadServiceAsync$lambda$1:227 
2025-08-20 18:24:42.103  4108-4108  Receiver                com.dspread.mdm.service              D  🔧 电池状态变化: 电量=100%, 温度=28°C, 充电=true
2025-08-20 18:24:42.112  4108-4146  Platform                com.dspread.mdm.service              D  🔧 DspreadService 设备服务绑定请求已发送
2025-08-20 18:24:42.137  4108-4108  Provisioning            com.dspread.mdm.service              I  ℹ️ ProvisioningManager初始化完成
2025-08-20 18:24:42.154  4108-4108  Provisioning            com.dspread.mdm.service              D  🔧 状态标志加载成功: /data/pos/config/provisioning_flags.json
2025-08-20 18:24:42.177  4108-4108  WebSocket               com.dspread.mdm.service              I  🔧 C0902 被动式上送: battery_level_change_5 (被动: 1)
2025-08-20 18:24:42.196  4108-4108  Platform                com.dspread.mdm.service              E  ❌ DspreadService 设备服务不可用，无法获取序列号
2025-08-20 18:24:42.210   646-939   BufferQueueDebug        surfaceflinger                       E  [Surface(name=5a8e641 Splash Screen com.dspread.mdm.service)/@0xee2e200 - animation-leash of window_animation#311](this:0xa5726c40,id:-1,api:0,p:-1,c:-1) id info cannot be read from 'Surface(name=5a8e641 Splash Screen com.dspread.mdm.service)/@0xee2e200 - animation-leash of window_animation#311'
2025-08-20 18:24:42.224  4108-4108  WebSocket               com.dspread.mdm.service              I  🔧 消息已缓存: C0902 (缓存数量: 1)
2025-08-20 18:24:42.231  4108-4108  WebSocket               com.dspread.mdm.service              I  🔧 C0902 电池状态上送成功 (1, trigger=battery_level_change_5)
2025-08-20 18:24:42.241  4108-4108  Platform                com.dspread.mdm.service              D  🔧 NetworkApi 单例实例已创建
2025-08-20 18:24:42.249  4108-4108  Receiver                com.dspread.mdm.service              D  🔧 网络状态变化: 连接=false, 类型=NONE
2025-08-20 18:24:42.255  4108-4108  Receiver                com.dspread.mdm.service              D  🔧 网络不可用，WebSocket连接可能受影响
2025-08-20 18:24:42.261  4108-4108  Receiver                com.dspread.mdm.service              D  🔧 ServiceManagementEventHandler 处理网络变化事件
2025-08-20 18:24:42.272  4108-4108  Common                  com.dspread.mdm.service              D  🔧 ServiceStartupManager 服务正在运行，无需启动: NETWORK_CHANGE
2025-08-20 18:24:42.278  4108-4108  Receiver                com.dspread.mdm.service              D  🔧 ServiceManagementEventHandler 网络变化后服务检查完成
2025-08-20 18:24:42.285  4108-4108  Provisioning            com.dspread.mdm.service              D  🔧 ProvisioningEventHandler 网络状态变化
2025-08-20 18:24:42.291  4108-4108  Provisioning            com.dspread.mdm.service              D  🔧 ProvisioningEventHandler 忽略初始启动时的网络连接事件
2025-08-20 18:24:42.391  4108-4108  WebSocket               com.dspread.mdm.service              I  🔧 C0905 主动式上送: manual_trigger (主动: 1)
2025-08-20 18:24:42.423  4108-4108  WebSocket               com.dspread.mdm.service              I  🔧 首次上送，上送当日数据
2025-08-20 18:24:42.430  4108-4108  WebSocket               com.dspread.mdm.service              I  🔧 没有流量数据需要上送
2025-08-20 18:24:42.435  4108-4108  Common                  com.dspread.mdm.service              D  🔧 刷新时自动上送流量统计
2025-08-20 18:24:42.464  4108-4108  Platform                com.dspread.mdm.service              D  🔧 DspreadService 设备服务连接成功: ComponentInfo{com.dspread.sdkservice/com.dspread.sdkservice.SDKDevService}
2025-08-20 18:24:42.471  4108-4108  Platform                com.dspread.mdm.service              D  🔧 DspreadService 设备服务绑定完成
2025-08-20 18:24:42.499  4108-4147  Platform                com.dspread.mdm.service              D  🔧 SpVersionApi 解析SP版本: V1.0.5
2025-08-20 18:24:42.516  4108-4147  Platform                com.dspread.mdm.service              D  🔧 DspreadService 首次获取SP版本成功: V1.0.5 (原始: *SP_VERSION:V1.0.5#*COMMIT:2d7cc90#*DATETIME:20250...)
2025-08-20 18:24:42.586  4108-4108  Common                  com.dspread.mdm.service              D  🔧 TestActivity UI已更新: SP=V1.0.5, SN=01354090202503050399
2025-08-20 18:24:43.478  4108-4146  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 等待DSPREAD服务就绪 (1/10): true
2025-08-20 18:24:43.484  4108-4146  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] DSPREAD服务就绪，启动Provisioning
2025-08-20 18:24:43.489  4108-4146  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 验证DSPREAD服务功能
2025-08-20 18:24:43.495  4108-4146  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 设备序列号验证: 成功(01354090202503050399)
2025-08-20 18:24:43.501  4108-4146  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 服务状态 - 系统服务:✅ 设备服务:✅
2025-08-20 18:24:43.507  4108-4146  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 启动ProvisioningService
2025-08-20 18:24:43.519  4108-4146  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] ProvisioningService启动完成
2025-08-20 18:24:43.519  4108-4147  Provisioning            com.dspread.mdm.service              I  ℹ️ 开始执行Provisioning配置，触发类型: FIRST_BOOT
2025-08-20 18:24:43.526  4108-4147  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] Provisioning进度: 正在下载配置文件...
2025-08-20 18:24:43.534  4108-4147  Provisioning            com.dspread.mdm.service              I  ℹ️ 开始请求远程配置...
2025-08-20 18:24:43.545  4108-4147  Provisioning            com.dspread.mdm.service              I  ℹ️ 请求配置: https://config.dspreadserv.net/status/config?SN=MDEzNTQwOTAyMDI1MDMwNTAzOTk=&timestamp=1755685483543
2025-08-20 18:24:43.571  4108-4147  Provisioning            com.dspread.mdm.service              E  ❌ 配置API请求失败
2025-08-20 18:24:43.584  4108-4147  Provisioning            com.dspread.mdm.service              W  ⚠️ 远程配置请求失败: Unable to resolve host "config.dspreadserv.net": No address associated with hostname
2025-08-20 18:24:43.599  4108-4147  Provisioning            com.dspread.mdm.service              I  ℹ️ 📂 使用本地保存的配置 - CID: 1001
2025-08-20 18:24:43.606  4108-4147  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] Provisioning进度: 正在下载 Logo...
2025-08-20 18:24:43.681  4108-4147  Provisioning            com.dspread.mdm.service              I  ℹ️ 文件已存在且MD5匹配，跳过下载: Logo
2025-08-20 18:24:43.687  4108-4147  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] Provisioning进度: 正在下载 BootAnimation...
2025-08-20 18:24:43.759  4108-4147  Provisioning            com.dspread.mdm.service              I  ℹ️ 文件已存在且MD5匹配，跳过下载: BootAnimation
2025-08-20 18:24:43.766  4108-4147  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] Provisioning进度: 正在应用配置...
2025-08-20 18:24:43.772  4108-4147  Provisioning            com.dspread.mdm.service              I  ℹ️ 配置应用完成
2025-08-20 18:24:43.777  4108-4147  Provisioning            com.dspread.mdm.service              W  ⚠️ 远程配置失败，首次配置状态保持未完成，下次启动将重试
2025-08-20 18:24:43.785  4108-4147  Provisioning            com.dspread.mdm.service              D  🔧 状态标志保存成功: /data/pos/config/provisioning_flags.json
2025-08-20 18:24:43.791  4108-4147  Provisioning            com.dspread.mdm.service              I  ℹ️ ProvisioningManager: 初始启动标记完成，网络重连重试功能已激活
2025-08-20 18:24:43.796  4108-4147  Provisioning            com.dspread.mdm.service              I  ℹ️ Provisioning配置执行完成
2025-08-20 18:24:43.802  4108-4147  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] Provisioning完成，启动核心组件
2025-08-20 18:24:43.821  4108-4108  WebSocket               com.dspread.mdm.service              I  🔧 开始初始化 WebSocket 组件...
2025-08-20 18:24:43.828  4108-4108  WebSocket               com.dspread.mdm.service              I  🔧 初始化 WebSocket 密钥管理器
2025-08-20 18:24:43.834  4108-4108  WebSocket               com.dspread.mdm.service              I  🔧 生成客户端公私钥对
2025-08-20 18:24:43.929  4108-4108  Common                  com.dspread.mdm.service              I  ✅ WebSocket 密钥管理器初始化完成
2025-08-20 18:24:43.939  4108-4108  WebSocket               com.dspread.mdm.service              I  🔧 开始初始化 WsMessageCenter...
2025-08-20 18:24:43.944  4108-4108  WebSocket               com.dspread.mdm.service              I  🔧 WsMessageCenter 首次初始化
2025-08-20 18:24:43.964  4108-4108  WebSocket               com.dspread.mdm.service              I  🔧 WsTaskManager 初始化完成
2025-08-20 18:24:44.066  4108-4108  WebSocket               com.dspread.mdm.service              I  🔧 WsMessageCenter 初始化完成
2025-08-20 18:24:44.072  4108-4108  WebSocket               com.dspread.mdm.service              I  🔧 开始清理终态任务，当前任务数量: 0
2025-08-20 18:24:44.077  4108-4108  WebSocket               com.dspread.mdm.service              I  🔧 没有需要清理的终态任务
2025-08-20 18:24:44.083  4108-4108  WebSocket               com.dspread.mdm.service              I  🔧 开始重置任务状态，当前任务个数: 0
2025-08-20 18:24:44.088  4108-4108  WebSocket               com.dspread.mdm.service              I  🔧 没有需要重置的任务
2025-08-20 18:24:44.094  4108-4108  Common                  com.dspread.mdm.service              I  ✅ 任务状态恢复完成
2025-08-20 18:24:44.110  4108-4108  WebSocket               com.dspread.mdm.service              I  🔧 使用Provisioning配置的WebSocket URL: wss://api.dspreadserv.net/status/websocket/register
2025-08-20 18:24:44.116  4108-4108  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接管理器初始化完成
2025-08-20 18:24:44.122  4108-4108  Common                  com.dspread.mdm.service              I  ✅ WebSocket 管理器初始化完成
2025-08-20 18:24:44.127  4108-4108  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 管理器初始化完成
2025-08-20 18:24:44.134  4108-4108  Common                  com.dspread.mdm.service              I  ✅ WebSocket 组件初始化完成
2025-08-20 18:24:44.139  4108-4108  WebSocket               com.dspread.mdm.service              I  🔧 启动 WebSocket 连接...
2025-08-20 18:24:44.144  4108-4108  WebSocket               com.dspread.mdm.service              I  🔧 开始连接 WebSocket...
2025-08-20 18:24:44.165  4108-4108  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接到: wss://api.dspreadserv.net/status/websocket/register?serialNo=MDEzNTQwOTAyMDI1MDMwNTAzOTk=&publicKey=TUlHZk1BMEdDU3FHU0liM0RRRUJBUVVBQTRHTkFEQ0JpUUtCZ1FEbVlURlNoN0dndHl2b2t5L2tkNnpoS1h3VXliNHNEWVdiTGdjME9YYWtWNm9XTisrTDdaV0sxR1F5eDVDK2htUDJ3ZzFQWXQvek9za2ZNdGV2MGQ3aThIQ2ptd2gwdGtQUUYzaHp3ZkY1NVEwNEp6SldEdXdFVXQ2VjRWUkpBWm41WDgvWFZQc1p0NVUzL20waTZEMmJjUDVia21RbDJIYjc2ZjdtdGwzWVNRSURBUUFC&query=1&msgVer=3&timestamp=1755685484149&signature=StSfgiGIqCOU2zs5/joBUZEXIlvUk1hsBnKr6zRabZ/8OcDGbKgBN3zJ9t8JBpwciVSfZAdZjdQNzf+bNuk06Q4STkfQfHHDjY2XNABgmb91vBQ7UClyAMsjjTFPZR1NW+rP0AoHz013cmhKurukYL6IRYZWPrVQKDe6CRLnI8Y=
2025-08-20 18:24:44.173  4108-4108  WebSocket               com.dspread.mdm.service              I  🔧 未检测到系统代理设置
2025-08-20 18:24:44.219  4108-4108  WebSocket               com.dspread.mdm.service              I  🔧 ===== WebSocket 库默认配置 =====
2025-08-20 18:24:44.225  4108-4108  WebSocket               com.dspread.mdm.service              I  🔧 默认 PING 间隔: 30000ms (30秒)
2025-08-20 18:24:44.230  4108-4108  WebSocket               com.dspread.mdm.service              I  🔧 默认 PONG 间隔: 0ms (0秒)
2025-08-20 18:24:44.236  4108-4108  WebSocket               com.dspread.mdm.service              I  🔧 帧队列大小: 5
2025-08-20 18:24:44.241  4108-4108  WebSocket               com.dspread.mdm.service              I  🔧 连接超时: 15000ms (15秒)
2025-08-20 18:24:44.247  4108-4108  WebSocket               com.dspread.mdm.service              I  🔧 允许缺失关闭帧: false
2025-08-20 18:24:44.252  4108-4108  WebSocket               com.dspread.mdm.service              I  🔧 ================================
2025-08-20 18:24:44.258  4108-4108  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接请求已发送
2025-08-20 18:24:44.264  4108-4108  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接启动完成
2025-08-20 18:24:44.264  4108-4163  WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误
2025-08-20 18:24:44.270  4108-4108  Common                  com.dspread.mdm.service              D  🔧 [ModuleManagerRegistry] 模块管理器注册中心初始化完成
2025-08-20 18:24:44.271  4108-4163  WebSocket               com.dspread.mdm.service              I  🔧 onError: 不触发重连，等待 onDisconnected 处理
2025-08-20 18:24:44.276  4108-4163  WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误
2025-08-20 18:24:44.277  4108-4108  Provisioning            com.dspread.mdm.service              D  🔧 ProvisioningEventHandler 启动Provisioning定时器: 43200s (12小时)
2025-08-20 18:24:44.277  4108-4147  Common                  com.dspread.mdm.service              D  🔧 [ModuleManagerRegistry] 启动所有模块
2025-08-20 18:24:44.283  4108-4163  WebSocket               com.dspread.mdm.service              I  🔧 reconnect() 被调用，调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onConnectError:672
2025-08-20 18:24:44.287  4108-4108  Common                  com.dspread.mdm.service              I  ✅ ⏰ 设置初始化定时器成功，下次执行: 43200秒后 (12小时)
2025-08-20 18:24:44.290  4108-4163  WebSocket               com.dspread.mdm.service              I  🔧 开始重连 WebSocket... (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onConnectError:672)
2025-08-20 18:24:44.291  4108-4147  Common                  com.dspread.mdm.service              D  🔧 Module LogStream Manager status changed to: INITIALIZED
2025-08-20 18:24:44.298  4108-4147  Common                  com.dspread.mdm.service              D  🔧 Module LogStream Manager status changed to: STARTING
2025-08-20 18:24:44.305  4108-4163  WebSocket               com.dspread.mdm.service              I  🔧 使用Provisioning配置的重连策略: delayPolicy=1, delaySwitch=1, delayTime=60
2025-08-20 18:24:44.306  4108-4108  Common                  com.dspread.mdm.service              I  ✅ ⏰ 设置心跳定时器成功，下次执行: 300秒后 (5分钟)
2025-08-20 18:24:44.311  4108-4163  WebSocket               com.dspread.mdm.service              I  🔧 当前断开记录数: 0, 延迟开关: 1
2025-08-20 18:24:44.316  4108-4108  Common                  com.dspread.mdm.service              I  ✅ ⏰ 设置任务执行定时器成功，下次执行: 60秒后 (1分钟)
2025-08-20 18:24:44.317  4108-4163  WebSocket               com.dspread.mdm.service              I  🔧 重连策略计算完成: 第1次重连，间隔3000ms (3秒)
2025-08-20 18:24:44.323  4108-4163  WebSocket               com.dspread.mdm.service              I  🔧 开始第1次重连，间隔3000ms (3秒) (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onConnectError:672)
2025-08-20 18:24:44.335  4108-4108  Common                  com.dspread.mdm.service              I  ✅ ⏰ 设置终端信息定时器成功，下次执行: 900秒后 (15分钟)
2025-08-20 18:24:44.360  4108-4108  Common                  com.dspread.mdm.service              D  🔧 TimerConfig ========== 当前定时器配置 (生产模式) ==========
2025-08-20 18:24:44.366  4108-4108  Common                  com.dspread.mdm.service              D  🔧 TimerConfig ①心跳定时器: 300秒
2025-08-20 18:24:44.372  4108-4108  Common                  com.dspread.mdm.service              D  🔧 TimerConfig ②终端信息上传定时器: 900秒
2025-08-20 18:24:44.378  4108-4108  Common                  com.dspread.mdm.service              D  🔧 TimerConfig ③任务执行定时器: 60秒
2025-08-20 18:24:44.384  4108-4108  Common                  com.dspread.mdm.service              D  🔧 TimerConfig ④服务守护定时器: 120秒
2025-08-20 18:24:44.390  4108-4147  Common                  com.dspread.mdm.service              D  🔧 Module LogStream Manager status changed to: RUNNING
2025-08-20 18:24:44.390  4108-4108  Common                  com.dspread.mdm.service              D  🔧 TimerConfig ⑤初始化定时器: 43200秒
2025-08-20 18:24:44.396  4108-4108  Common                  com.dspread.mdm.service              D  🔧 TimerConfig ===============================================
2025-08-20 18:24:44.400  4108-4147  Common                  com.dspread.mdm.service              D  🔧 [ModuleManagerRegistry] LogStreamManager启动成功
2025-08-20 18:24:44.402  4108-4108  Common                  com.dspread.mdm.service              I  ✅ [SmartMdmBackgroundService] 所有定时器启动完成
2025-08-20 18:24:44.408  4108-4108  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 核心组件初始化完成
2025-08-20 18:24:44.424  4108-4147  Compatibil...geReporter com.dspread.mdm.service              D  Compat change id reported: 263076149; UID 1000; state: ENABLED
2025-08-20 18:24:44.435  4108-4147  Common                  com.dspread.mdm.service              D  🔧 Module Geofence Manager status changed to: INITIALIZED
2025-08-20 18:24:44.442  4108-4147  Common                  com.dspread.mdm.service              D  🔧 Module Geofence Manager status changed to: STARTING
2025-08-20 18:24:44.466  4108-4147  Platform                com.dspread.mdm.service              D  🔧 用户交互监控已启动（简化版本）
2025-08-20 18:24:44.472  4108-4147  Platform                com.dspread.mdm.service              D  🔧 使用模拟用户交互检测
2025-08-20 18:24:44.477  4108-4147  Platform                com.dspread.mdm.service              D  🔧 用户交互监控已启动
2025-08-20 18:24:44.484  4108-4147  Service                 com.dspread.mdm.service              D  🔧 [GeofenceManager]Constants.geofenceStatus = 1
2025-08-20 18:24:44.491  4108-4147  Service                 com.dspread.mdm.service              D  🔧 [GeofenceManager]-- Constants.storeId =  -- Constants.storeSsid =  -- Constants.storeIp = 
2025-08-20 18:24:44.499  4108-4147  Common                  com.dspread.mdm.service              D  🔧 Module Geofence Manager status changed to: RUNNING
2025-08-20 18:24:44.507  4108-4147  Common                  com.dspread.mdm.service              D  🔧 [ModuleManagerRegistry] GeofenceManager启动成功
2025-08-20 18:24:44.525  4108-4147  RuleBase                com.dspread.mdm.service              D  🔧 RuleBaseManager 开始初始化RuleBaseManager...
2025-08-20 18:24:44.536  4108-4147  RuleBase                com.dspread.mdm.service              D  🔧 RuleBaseStorage RuleBase存储管理器初始化完成
2025-08-20 18:24:44.542  4108-4147  RuleBase                com.dspread.mdm.service              D  🔧 RuleStateMonitor 状态监控器初始化完成
2025-08-20 18:24:44.548  4108-4147  RuleBase                com.dspread.mdm.service              D  🔧 RuleBaseManager RuleBaseManager初始化完成
2025-08-20 18:24:44.554  4108-4147  Common                  com.dspread.mdm.service              D  🔧 [ModuleManagerRegistry] RuleBaseManager启动成功
2025-08-20 18:24:44.561  4108-4147  Common                  com.dspread.mdm.service              D  🔧 [ModuleManagerRegistry] 所有模块启动完成
2025-08-20 18:24:46.745  4108-4175  ProfileInstaller        com.dspread.mdm.service              D  Installing profile for com.dspread.mdm.service
2025-08-20 18:24:47.346  4108-4108  WebSocket               com.dspread.mdm.service              I  🔧 重连 WebSocket: wss://api.dspreadserv.net/status/websocket/register?serialNo=MDEzNTQwOTAyMDI1MDMwNTAzOTk=&publicKey=TUlHZk1BMEdDU3FHU0liM0RRRUJBUVVBQTRHTkFEQ0JpUUtCZ1FEbVlURlNoN0dndHl2b2t5L2tkNnpoS1h3VXliNHNEWVdiTGdjME9YYWtWNm9XTisrTDdaV0sxR1F5eDVDK2htUDJ3ZzFQWXQvek9za2ZNdGV2MGQ3aThIQ2ptd2gwdGtQUUYzaHp3ZkY1NVEwNEp6SldEdXdFVXQ2VjRWUkpBWm41WDgvWFZQc1p0NVUzL20waTZEMmJjUDVia21RbDJIYjc2ZjdtdGwzWVNRSURBUUFC&query=0&msgVer=3&timestamp=1755685487328&signature=NleF9AuqTA7UxIXufhw9xS/YZpypSeLUKilUyLBkN5ss9ygQO1KGeDHsLJ/wY3gnYtQt/MCaaR3qfzBDfcT82ZVQKmbrb52iHzvFZ6XrpcgtUqiSYi90A2clQllbvW9aa0Qq/NHDdtWzAJpc6mAinyIZEMGfFMh5ilFYsV//Pk8=
2025-08-20 18:24:47.354  4108-4108  WebSocket               com.dspread.mdm.service              I  🔧 未检测到系统代理设置
2025-08-20 18:24:47.398  4108-4176  WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误
2025-08-20 18:24:47.405  4108-4176  WebSocket               com.dspread.mdm.service              I  🔧 onError: 不触发重连，等待 onDisconnected 处理
2025-08-20 18:24:47.411  4108-4176  WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误
2025-08-20 18:24:47.418  4108-4176  WebSocket               com.dspread.mdm.service              I  🔧 reconnect() 被调用，调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onConnectError:672
2025-08-20 18:24:47.424  4108-4176  WebSocket               com.dspread.mdm.service              I  🔧 开始重连 WebSocket... (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onConnectError:672)
2025-08-20 18:24:47.439  4108-4176  WebSocket               com.dspread.mdm.service              I  🔧 使用Provisioning配置的重连策略: delayPolicy=1, delaySwitch=1, delayTime=60
2025-08-20 18:24:47.445  4108-4176  WebSocket               com.dspread.mdm.service              I  🔧 当前断开记录数: 0, 延迟开关: 1
2025-08-20 18:24:47.451  4108-4176  WebSocket               com.dspread.mdm.service              I  🔧 重连策略计算完成: 第2次重连，间隔3000ms (3秒)
2025-08-20 18:24:47.457  4108-4176  WebSocket               com.dspread.mdm.service              I  🔧 开始第2次重连，间隔3000ms (3秒) (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onConnectError:672)
2025-08-20 18:24:50.489  4108-4108  WebSocket               com.dspread.mdm.service              I  🔧 重连 WebSocket: wss://api.dspreadserv.net/status/websocket/register?serialNo=MDEzNTQwOTAyMDI1MDMwNTAzOTk=&publicKey=TUlHZk1BMEdDU3FHU0liM0RRRUJBUVVBQTRHTkFEQ0JpUUtCZ1FEbVlURlNoN0dndHl2b2t5L2tkNnpoS1h3VXliNHNEWVdiTGdjME9YYWtWNm9XTisrTDdaV0sxR1F5eDVDK2htUDJ3ZzFQWXQvek9za2ZNdGV2MGQ3aThIQ2ptd2gwdGtQUUYzaHp3ZkY1NVEwNEp6SldEdXdFVXQ2VjRWUkpBWm41WDgvWFZQc1p0NVUzL20waTZEMmJjUDVia21RbDJIYjc2ZjdtdGwzWVNRSURBUUFC&query=0&msgVer=3&timestamp=1755685490466&signature=W3lgHUTHEQjC84t3TP66G41Omp3bYBudT7YlgRuGfqTfBBAVAN09Daj4/M/ICdTBQVzGEeGARPv0ccDQGFGfVSQtRwSP2yfwJOJVEdCHIuOtc1+C2qIsZ/sCRIGxsfn1v442nVr0cwb2sgAzfNu0D28MKQVwfZZIJN9v62YrhGQ=
2025-08-20 18:24:50.502  4108-4108  WebSocket               com.dspread.mdm.service              I  🔧 未检测到系统代理设置
2025-08-20 18:24:50.554  4108-4179  WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误
2025-08-20 18:24:50.563  4108-4179  WebSocket               com.dspread.mdm.service              I  🔧 onError: 不触发重连，等待 onDisconnected 处理
2025-08-20 18:24:50.570  4108-4179  WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误
2025-08-20 18:24:50.579  4108-4179  WebSocket               com.dspread.mdm.service              I  🔧 reconnect() 被调用，调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onConnectError:672
2025-08-20 18:24:50.585  4108-4179  WebSocket               com.dspread.mdm.service              I  🔧 开始重连 WebSocket... (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onConnectError:672)
2025-08-20 18:24:50.600  4108-4179  WebSocket               com.dspread.mdm.service              I  🔧 使用Provisioning配置的重连策略: delayPolicy=1, delaySwitch=1, delayTime=60
2025-08-20 18:24:50.607  4108-4179  WebSocket               com.dspread.mdm.service              I  🔧 当前断开记录数: 0, 延迟开关: 1
2025-08-20 18:24:50.613  4108-4179  WebSocket               com.dspread.mdm.service              I  🔧 重连策略计算完成: 第3次重连，间隔3000ms (3秒)
2025-08-20 18:24:50.619  4108-4179  WebSocket               com.dspread.mdm.service              I  🔧 开始第3次重连，间隔3000ms (3秒) (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onConnectError:672)
2025-08-20 18:24:53.644  4108-4108  WebSocket               com.dspread.mdm.service              I  🔧 重连 WebSocket: wss://api.dspreadserv.net/status/websocket/register?serialNo=MDEzNTQwOTAyMDI1MDMwNTAzOTk=&publicKey=TUlHZk1BMEdDU3FHU0liM0RRRUJBUVVBQTRHTkFEQ0JpUUtCZ1FEbVlURlNoN0dndHl2b2t5L2tkNnpoS1h3VXliNHNEWVdiTGdjME9YYWtWNm9XTisrTDdaV0sxR1F5eDVDK2htUDJ3ZzFQWXQvek9za2ZNdGV2MGQ3aThIQ2ptd2gwdGtQUUYzaHp3ZkY1NVEwNEp6SldEdXdFVXQ2VjRWUkpBWm41WDgvWFZQc1p0NVUzL20waTZEMmJjUDVia21RbDJIYjc2ZjdtdGwzWVNRSURBUUFC&query=0&msgVer=3&timestamp=1755685493625&signature=U5FKe+g1xHg4/oP9yfOmE6d5r5vt6PtlqK2aV/aSsB8wsQy/Kz/SThQ/6427te+wASSTpNKzAonjmpyyycao+HeSSKbKQUaY7J/WBmA1g6U6EQRtXE/5CbqOr+AxvZKITBFM5KtNFaSyNKIgweTbfqUgo7LVqRIloIHKni3tpfQ=
2025-08-20 18:24:53.653  4108-4108  WebSocket               com.dspread.mdm.service              I  🔧 未检测到系统代理设置
2025-08-20 18:24:53.700  4108-4181  WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误
2025-08-20 18:24:53.707  4108-4181  WebSocket               com.dspread.mdm.service              I  🔧 onError: 不触发重连，等待 onDisconnected 处理
2025-08-20 18:24:53.713  4108-4181  WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误
2025-08-20 18:24:53.720  4108-4181  WebSocket               com.dspread.mdm.service              I  🔧 reconnect() 被调用，调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onConnectError:672
2025-08-20 18:24:53.726  4108-4181  WebSocket               com.dspread.mdm.service              I  🔧 开始重连 WebSocket... (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onConnectError:672)
2025-08-20 18:24:53.742  4108-4181  WebSocket               com.dspread.mdm.service              I  🔧 使用Provisioning配置的重连策略: delayPolicy=1, delaySwitch=1, delayTime=60
2025-08-20 18:24:53.747  4108-4181  WebSocket               com.dspread.mdm.service              I  🔧 当前断开记录数: 0, 延迟开关: 1
2025-08-20 18:24:53.754  4108-4181  WebSocket               com.dspread.mdm.service              I  🔧 重连策略计算完成: 第4次重连，间隔6000ms (6秒)
2025-08-20 18:24:53.760  4108-4181  WebSocket               com.dspread.mdm.service              I  🔧 开始第4次重连，间隔6000ms (6秒) (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onConnectError:672)
2025-08-20 18:24:59.785  4108-4108  WebSocket               com.dspread.mdm.service              I  🔧 重连 WebSocket: wss://api.dspreadserv.net/status/websocket/register?serialNo=MDEzNTQwOTAyMDI1MDMwNTAzOTk=&publicKey=TUlHZk1BMEdDU3FHU0liM0RRRUJBUVVBQTRHTkFEQ0JpUUtCZ1FEbVlURlNoN0dndHl2b2t5L2tkNnpoS1h3VXliNHNEWVdiTGdjME9YYWtWNm9XTisrTDdaV0sxR1F5eDVDK2htUDJ3ZzFQWXQvek9za2ZNdGV2MGQ3aThIQ2ptd2gwdGtQUUYzaHp3ZkY1NVEwNEp6SldEdXdFVXQ2VjRWUkpBWm41WDgvWFZQc1p0NVUzL20waTZEMmJjUDVia21RbDJIYjc2ZjdtdGwzWVNRSURBUUFC&query=0&msgVer=3&timestamp=1755685499766&signature=sZ1PQUSeNIu1KjpGrFN7T21a5wVFHDt9WqQ6l/fYBh1OAsq8IjMhCA0rl2It0OuytUVi0JEOwKCUDNAjNqy6q7Ui2bhlFErHf7tbTaxv47JlGYuZcbjezk5cHa20JdcjMoF1/gr6ngIEvlIBjy2YJu/r+Kd4qjGxMCW4QUMj9TQ=
2025-08-20 18:24:59.792  4108-4108  WebSocket               com.dspread.mdm.service              I  🔧 未检测到系统代理设置
2025-08-20 18:24:59.835  4108-4200  WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误
2025-08-20 18:24:59.844  4108-4200  WebSocket               com.dspread.mdm.service              I  🔧 onError: 不触发重连，等待 onDisconnected 处理
2025-08-20 18:24:59.855  4108-4200  WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误
2025-08-20 18:24:59.866  4108-4200  WebSocket               com.dspread.mdm.service              I  🔧 reconnect() 被调用，调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onConnectError:672
2025-08-20 18:24:59.873  4108-4200  WebSocket               com.dspread.mdm.service              I  🔧 开始重连 WebSocket... (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onConnectError:672)
2025-08-20 18:24:59.892  4108-4200  WebSocket               com.dspread.mdm.service              I  🔧 使用Provisioning配置的重连策略: delayPolicy=1, delaySwitch=1, delayTime=60
2025-08-20 18:24:59.900  4108-4200  WebSocket               com.dspread.mdm.service              I  🔧 当前断开记录数: 0, 延迟开关: 1
2025-08-20 18:24:59.907  4108-4200  WebSocket               com.dspread.mdm.service              I  🔧 重连策略计算完成: 第5次重连，间隔9000ms (9秒)
2025-08-20 18:24:59.914  4108-4200  WebSocket               com.dspread.mdm.service              I  🔧 开始第5次重连，间隔9000ms (9秒) (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onConnectError:672)
2025-08-20 18:25:00.353  4108-4108  Platform                com.dspread.mdm.service              D  🔧 [WiFiManagerApi] WiFiManagerApi 单例实例已创建
2025-08-20 18:25:00.450  4108-4108  Receiver                com.dspread.mdm.service              D  🔧 网络状态变化: 连接=true, 类型=WIFI
2025-08-20 18:25:00.458  4108-4108  Receiver                com.dspread.mdm.service              D  🔧 网络恢复，尝试重新连接WebSocket
2025-08-20 18:25:00.467  4108-4108  Receiver                com.dspread.mdm.service              D  🔧 ServiceManagementEventHandler 处理网络变化事件
2025-08-20 18:25:00.478  4108-4108  Common                  com.dspread.mdm.service              D  🔧 ServiceStartupManager 服务正在运行，无需启动: NETWORK_CHANGE
2025-08-20 18:25:00.487  4108-4108  Receiver                com.dspread.mdm.service              D  🔧 ServiceManagementEventHandler 网络变化后服务检查完成
2025-08-20 18:25:00.497  4108-4108  Provisioning            com.dspread.mdm.service              D  🔧 ProvisioningEventHandler 网络状态变化
2025-08-20 18:25:00.508  4108-4108  Provisioning            com.dspread.mdm.service              I  ℹ️ ProvisioningEventHandler 检测到网络重连，检查配置更新
2025-08-20 18:25:00.517  4108-4108  Provisioning            com.dspread.mdm.service              D  🔧 ProvisioningEventHandler 开始执行Provisioning，触发类型: NETWORK_CONNECTED，配置URL: https://config.dspreadserv.net/status/config
2025-08-20 18:25:00.529  4108-4147  Provisioning            com.dspread.mdm.service              I  ℹ️ 开始执行Provisioning配置，触发类型: NETWORK_CONNECTED
2025-08-20 18:25:00.536  4108-4147  Provisioning            com.dspread.mdm.service              D  🔧 ProvisioningEventHandler Provisioning进度 (NETWORK_CONNECTED): 正在下载配置文件...
2025-08-20 18:25:00.543  4108-4147  Provisioning            com.dspread.mdm.service              I  ℹ️ 开始请求远程配置...
2025-08-20 18:25:00.552  4108-4147  Provisioning            com.dspread.mdm.service              I  ℹ️ 请求配置: https://config.dspreadserv.net/status/config?SN=MDEzNTQwOTAyMDI1MDMwNTAzOTk=&timestamp=1755685500551
2025-08-20 18:25:05.583  4108-4147  TrafficStats            com.dspread.mdm.service              D  tagSocket(116) with statsTag=0xffffffff, statsUid=-1
2025-08-20 18:25:07.242  4108-4147  Provisioning            com.dspread.mdm.service              I  ℹ️ 远程配置获取成功
2025-08-20 18:25:07.249  4108-4147  Provisioning            com.dspread.mdm.service              I  ℹ️ 远程配置请求成功
2025-08-20 18:25:07.256  4108-4147  Provisioning            com.dspread.mdm.service              I  ℹ️ 远程配置解析成功 - CID: 1001
2025-08-20 18:25:07.286  4108-4147  Provisioning            com.dspread.mdm.service              I  ℹ️ API响应已保存到本地: /data/pos/config/provisioning_config.json, 具体内容为:
                                                                                                    {"mode":"dev","data":{"requestTime":"1755685507788","customization":{"system":{"timezone":"Asia\/Hong_Kong","logo":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/test\/logo.bin","logoMd5":"ba1ee533924eae5c408465e7cddcbda4","bootAnimationMd5":"ba1ee533924eae5c408465e7cddcbda4","gps":{"minUpdateTime":"30","scheduleTime":"60","maxLocateTime":"0","minDistance":"10","valid_distance":"500","care":"1"},"powerSaveMode":{"enable":"1","screenTimeout":"0"},"bootAnimation":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/test\/bootanimation.zip"},"polling":{"heartbeatTime":"300","terminalInfoTime":"900","statusApiUrl":"wss:\/\/api.dspreadserv.net\/status\/websocket\/register","remoteUrl":"wss:\/\/remote.dspreadserv.net\/remoteWSS\/websockify","uploadMode":"1","wssreconn":{"pingInterval":"30","delayPolicy":"1","delaySwitch":"1","delayTime":"60"}}},"client":"default","cid":"1001"},"function":"getProvisioningConfig","description":"SUCCESS","stateCode":"0","version":"v1.0.1.20250801"}
2025-08-20 18:25:07.293  4108-4147  Provisioning            com.dspread.mdm.service              D  🔧 ProvisioningEventHandler Provisioning进度 (NETWORK_CONNECTED): 正在下载 Logo...
2025-08-20 18:25:07.367  4108-4147  Provisioning            com.dspread.mdm.service              I  ℹ️ 文件已存在且MD5匹配，跳过下载: Logo
2025-08-20 18:25:07.373  4108-4147  Provisioning            com.dspread.mdm.service              D  🔧 ProvisioningEventHandler Provisioning进度 (NETWORK_CONNECTED): 正在下载 BootAnimation...
2025-08-20 18:25:07.446  4108-4147  Provisioning            com.dspread.mdm.service              I  ℹ️ 文件已存在且MD5匹配，跳过下载: BootAnimation
2025-08-20 18:25:07.452  4108-4147  Provisioning            com.dspread.mdm.service              D  🔧 ProvisioningEventHandler Provisioning进度 (NETWORK_CONNECTED): 正在应用配置...
2025-08-20 18:25:07.458  4108-4147  Provisioning            com.dspread.mdm.service              I  ℹ️ 配置应用完成
2025-08-20 18:25:07.464  4108-4147  Provisioning            com.dspread.mdm.service              I  ℹ️ 远程配置成功，标记首次配置完成
2025-08-20 18:25:07.473  4108-4147  Provisioning            com.dspread.mdm.service              D  🔧 状态标志保存成功: /data/pos/config/provisioning_flags.json
2025-08-20 18:25:07.479  4108-4147  Provisioning            com.dspread.mdm.service              I  ℹ️ Provisioning配置执行完成
2025-08-20 18:25:07.485  4108-4147  Provisioning            com.dspread.mdm.service              I  ℹ️ ProvisioningEventHandler Provisioning完成 (NETWORK_CONNECTED): 配置执行完成
2025-08-20 18:25:08.944  4108-4108  WebSocket               com.dspread.mdm.service              I  🔧 重连 WebSocket: wss://api.dspreadserv.net/status/websocket/register?serialNo=MDEzNTQwOTAyMDI1MDMwNTAzOTk=&publicKey=TUlHZk1BMEdDU3FHU0liM0RRRUJBUVVBQTRHTkFEQ0JpUUtCZ1FEbVlURlNoN0dndHl2b2t5L2tkNnpoS1h3VXliNHNEWVdiTGdjME9YYWtWNm9XTisrTDdaV0sxR1F5eDVDK2htUDJ3ZzFQWXQvek9za2ZNdGV2MGQ3aThIQ2ptd2gwdGtQUUYzaHp3ZkY1NVEwNEp6SldEdXdFVXQ2VjRWUkpBWm41WDgvWFZQc1p0NVUzL20waTZEMmJjUDVia21RbDJIYjc2ZjdtdGwzWVNRSURBUUFC&query=0&msgVer=3&timestamp=1755685508924&signature=JOSz6Pzng3YDsX8njQiU5WrwTdHxs/VI2iqnn4uYAeEsLToXTxElHx6qLD/ReIrreTt9OkDfum/GP9j/e8+xuhJvs5RWgEoCzYSHNryRlaenDnyAfWwpmBFDrw0oUcQXs/0+R7ZEhY8r5LgfDzm7mZ1fZxxvp41WgMwxm9/llTo=
2025-08-20 18:25:08.953  4108-4108  WebSocket               com.dspread.mdm.service              I  🔧 未检测到系统代理设置
2025-08-20 18:25:09.012  4108-4235  TrafficStats            com.dspread.mdm.service              D  tagSocket(165) with statsTag=0xffffffff, statsUid=-1
2025-08-20 18:25:09.263  4108-4236  TrafficStats            com.dspread.mdm.service              D  tagSocket(171) with statsTag=0xffffffff, statsUid=-1
2025-08-20 18:25:10.741  4108-4237  Common                  com.dspread.mdm.service              I  ✅ WebSocket 物理连接成功，等待服务器 hellMsg
2025-08-20 18:25:10.750  4108-4237  WebSocket               com.dspread.mdm.service              I  🔧 ===== 心跳发送器实际配置 =====
2025-08-20 18:25:10.761  4108-4237  WebSocket               com.dspread.mdm.service              I  🔧 实际 PING 发送间隔: 30000ms (30秒)
2025-08-20 18:25:10.772  4108-4237  WebSocket               com.dspread.mdm.service              I  🔧 PING 定时器名称: PingSender
2025-08-20 18:25:10.781  4108-4237  WebSocket               com.dspread.mdm.service              I  🔧 实际 PONG 发送间隔: 0ms (0秒)
2025-08-20 18:25:10.789  4108-4237  WebSocket               com.dspread.mdm.service              I  🔧 PONG 定时器名称: PongSender
2025-08-20 18:25:10.797  4108-4237  WebSocket               com.dspread.mdm.service              I  🔧 ===============================
2025-08-20 18:25:10.804  4108-4237  WebSocket               com.dspread.mdm.service              I  🔧 启动PING/PONG检测，阈值: 连续3个PING无PONG响应
2025-08-20 18:25:10.845  4108-4237  Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"hellMsg":"hello 01354090202503050399","tranCode":"S0000","version":"1","rebootTime":"03:42:19","serialNo":"01354090202503050399","deviceStatus":"6"}
2025-08-20 18:25:10.852  4108-4237  Common                  com.dspread.mdm.service              I  ✅ 收到服务器 hellMsg，WebSocket 业务连接成功！
2025-08-20 18:25:10.858  4108-4237  Common                  com.dspread.mdm.service              I  ✅ WebSocket 业务连接成功！
2025-08-20 18:25:10.864  4108-4237  WebSocket               com.dspread.mdm.service              I  🔧 业务连接成功，重连计数已重置为 0
2025-08-20 18:25:10.870  4108-4237  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接成功，检查自身更新任务状态
2025-08-20 18:25:10.878  4108-4237  Receiver                com.dspread.mdm.service              D  🔧 发送广播: com.dspread.mdm.service.CHECK_SELF_UPDATE_STATUS
2025-08-20 18:25:10.885  4108-4237  WebSocket               com.dspread.mdm.service              I  🔧 开始发送缓存消息，数量: 1
2025-08-20 18:25:10.894  4108-4237  WebSocket               com.dspread.mdm.service              I  🔧 C0902 主动式上送: cached_message_resend (主动: 2)
2025-08-20 18:25:10.932  4108-4237  Common                  com.dspread.mdm.service              I  ✅ 缓存消息发送成功: C0902
2025-08-20 18:25:11.440  4108-4237  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接成功
2025-08-20 18:25:11.447  4108-4237  WebSocket               com.dspread.mdm.service              I  🔧 设备状态: 6
2025-08-20 18:25:11.455  4108-4237  WebSocket               com.dspread.mdm.service              I  🔧 检查首次连接标志: isFirstTimeWebSocketConnected=true
2025-08-20 18:25:11.462  4108-4237  WebSocket               com.dspread.mdm.service              I  🔧 首次WebSocket连接成功，发送初始化信息并执行初始化检查
2025-08-20 18:25:11.471  4108-4237  WebSocket               com.dspread.mdm.service              I  🔧 开始发送初始化信息（带流量控制）
2025-08-20 18:25:11.479  4108-4237  WebSocket               com.dspread.mdm.service              I  🔧 C0901 被动式上送: first_connection (被动: 2)
2025-08-20 18:25:11.498  4108-4237  Platform                com.dspread.mdm.service              D  🔧 重新收集应用信息
2025-08-20 18:25:11.608  4108-4237  AppManager              com.dspread.mdm.service              I  ℹ️ 应用信息获取完成: 总数131(系统129/用户2) 返回2个
2025-08-20 18:25:11.631  4108-4237  Platform                com.dspread.mdm.service              D  🔧 应用信息: 2 个应用
2025-08-20 18:25:11.663  4108-4237  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0901","serialNo":"01354090202503050399","request_time":"1755685511638","request_id":"1755685511638C0901","version":"1","data":{"apkInfo":[{"packName":"com.dspread.mdm.service","apkName":"service","versionCode":22,"versionName":"1.1.03.20250820.DSPREAD.MDM.SERVICE","updateDate":"2025-08-20 18:24:38"},{"packName":"mark.via","apkName":"Via","versionCode":20250117,"versionName":"6.2.0","updateDate":"2025-08-18 17:50:20"}],"sytemInfo":{"androidVersion":"14","buildNumber":"BASE_D30M-MU_GEN_AP_V1.0.8.1_202507032000_ota_test","aspl":"2025-03-05"},"serviceInfo":[]},"myVersionName":"1.1.03.20250820.DSPREAD.MDM.SERVICE","terminalDate":"20250820182511"}
2025-08-20 18:25:11.670  4108-4237  WebSocket               com.dspread.mdm.service              I  🔧 C0901 应用信息上送成功 (1)
2025-08-20 18:25:12.679  4108-4237  WebSocket               com.dspread.mdm.service              I  🔧 C0902 被动式上送: first_connection (被动: 3)
2025-08-20 18:25:12.723  4108-4237  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0902","serialNo":"01354090202503050399","request_time":"1755685512700","request_id":"1755685512700C0902","version":"1","data":{"batteryLife":100,"batteryHealth":2,"temprature":"28.5","isCharging":"1","isLowBattery":"0","powerOff":"0","outOfBattery":"0"},"myVersionName":"1.1.03.20250820.DSPREAD.MDM.SERVICE","terminalDate":"20250820182512"}
2025-08-20 18:25:12.730  4108-4237  WebSocket               com.dspread.mdm.service              I  🔧 C0902 电池状态上送成功 (1, trigger=first_connection)
2025-08-20 18:25:13.738  4108-4237  WebSocket               com.dspread.mdm.service              I  🔧 C0903 被动式上送: first_connection (被动: 4)
2025-08-20 18:25:13.795  4108-4119  System                  com.dspread.mdm.service              W  A resource failed to call close. 
2025-08-20 18:25:13.906  4108-4237  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0903","serialNo":"01354090202503050399","request_time":"1755685513870","request_id":"1755685513870C0903","version":"1","data":{"hardwareInfo":[{"name":"CPU Usage","desc":"cpu:0.50"},{"name":"storage_usage_status","desc":" 4.57GB \/ 32.00GB ","state":"1"},{"name":"total_memory","desc":" 0.69GB \/ 1.86GB ","state":"1"},{"name":"androidVersion","desc":"14","state":"1"},{"name":"network_status","desc":"wifi","state":"1"},{"name":"OSVer","desc":"14","state":"1"}],"locationInfo":{"gps":{"longitude":"-999.0","latitude":"-999.0","satelliteCount":0,"satelliteCountInUsed":0,"satelliteAverSNR":"0.00","distance":"0.0","lockStatus":"1","lockMeter":"200","proId":"","proName":""},"baseSite":{"MCC":"","MNC":"","CID":"-1","LAC":"-1","DBM":"-999","IMSI":"","ICCID":""},"wifi":{"SSID":"fubox_2.4G","IP":"**************","MAC":"BE:87:C2:42:DF:75","SSTH":"-34","BSSID":"7c:10:c9:d6:9f:10"},"address":"无法获取"}},"myVersionName":"1.1.03.20250820.DSPREAD.MDM.SERVICE","terminalDate":"20250820182513"}
2025-08-20 18:25:13.914  4108-4237  WebSocket               com.dspread.mdm.service              I  🔧 C0903 数据信息上送成功 (1)
2025-08-20 18:25:14.922  4108-4237  WebSocket               com.dspread.mdm.service              I  🔧 C0904 被动式上送: first_connection (被动: 5)
2025-08-20 18:25:15.023  4108-4237  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0904","serialNo":"01354090202503050399","request_time":"1755685514989","request_id":"1755685514989C0904","version":"1","data":{"wifiOption":[{"SSID":"2306","SSTH":"-71"},{"SSID":"2205","SSTH":"-51"},{"SSID":"2205_5G","SSTH":"-56"},{"SSID":"DIRECT-7D-HP Laser 136w","SSTH":"-68"},{"SSID":"fubox_2.4G","SSTH":"-35"},{"SSID":"DIRECT-CC-HP Laser 1188w","SSTH":"-71"},{"SSID":"2206","SSTH":"-26"},{"SSID":"2103","SSTH":"-56"},{"SSID":"DIRECT-1D-HP Laser 1188w","SSTH":"-85"},{"SSID":"2206-5G","SSTH":"-35"},{"SSID":"DIRECT-C6-Mi All-in-One Inkjet","SSTH":"-51"},{"SSID":"2207-5G","SSTH":"-78"},{"SSID":"2207","SSTH":"-59"},{"SSID":"@Ruijie-1816_5G","SSTH":"-68"},{"SSID":"@Ruijie-1816","SSTH":"-55"}],"baseSite":{"MCC":"","MNC":"","CID":"-1","LAC":"-1","DBM":"-999","IMSI":"","ICCID":""},"wifi":{"SSID":"fubox_2.4G","IP":"**************","MAC":"BE:87:C2:42:DF:75","SSTH":"-34","BSSID":"7c:10:c9:d6:9f:10"}},"myVersionName":"1.1.03.20250820.DSPREAD.MDM.SERVICE","terminalDate":"20250820182514"}
2025-08-20 18:25:15.029  4108-4237  WebSocket               com.dspread.mdm.service              I  🔧 C0904 网络状态上送成功 (1)
2025-08-20 18:25:15.474  4108-4108  Receiver                com.dspread.mdm.service              D  🔧 网络恢复，重新上传网络状态信息（防抖通过）
2025-08-20 18:25:15.493  4108-4108  WebSocket               com.dspread.mdm.service              I  🔧 C0904 被动式上送拒绝: NETWORK_RECOVERY (拒绝: 1)
2025-08-20 18:25:15.501  4108-4108  WebSocket               com.dspread.mdm.service              I  🔧 C0904 网络状态上送被流量控制阻止: 被动事件 'NETWORK_RECOVERY' 在平衡模式 - 重要变化下未启用
2025-08-20 18:25:16.037  4108-4237  WebSocket               com.dspread.mdm.service              I  🔧 C0906 被动式上送: first_connection (被动: 6)
2025-08-20 18:25:16.052  4108-4237  Platform                com.dspread.mdm.service              D  🔧 DspreadService 使用缓存的SP版本: V1.0.5
2025-08-20 18:25:16.059  4108-4237  Platform                com.dspread.mdm.service              D  🔧 SpVersionApi 通过DspreadService获取SP版本: V1.0.5
2025-08-20 18:25:16.088  4108-4237  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0906","serialNo":"01354090202503050399","request_time":"1755685516070","request_id":"1755685516070C0906","version":"1","data":{"firmWareInfo":{"spfw":"V1.0.5"},"imei_1":"864177060143618","imei_2":"","wifi_mac":"be:87:c2:42:df:75","bt_mac":"","bsn":""},"myVersionName":"1.1.03.20250820.DSPREAD.MDM.SERVICE","terminalDate":"20250820182516"}
2025-08-20 18:25:16.095  4108-4237  WebSocket               com.dspread.mdm.service              I  🔧 C0906 设备信息上送成功 (1)
2025-08-20 18:25:16.102  4108-4237  WebSocket               com.dspread.mdm.service              I  🔧 C0109 被动式上送: first_connection (被动: 7)
2025-08-20 18:25:16.118  4108-4237  Platform                com.dspread.mdm.service              D  🔧 重新收集应用信息（强制刷新）
2025-08-20 18:25:16.206  4108-4237  AppManager              com.dspread.mdm.service              I  ℹ️ 应用信息获取完成: 总数131(系统129/用户2) 返回2个
2025-08-20 18:25:16.221  4108-4237  Platform                com.dspread.mdm.service              D  🔧 应用信息: 2 个应用
2025-08-20 18:25:16.376  4108-4237  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0109","serialNo":"01354090202503050399","request_time":"1755685516311","request_id":"1755685516311C0109","version":"1","data":{"apkInfo":[{"packName":"com.dspread.mdm.service","apkName":"service","versionCode":22,"versionName":"1.1.03.20250820.DSPREAD.MDM.SERVICE","updateDate":"2025-08-20 18:24:38"},{"packName":"mark.via","apkName":"Via","versionCode":20250117,"versionName":"6.2.0","updateDate":"2025-08-18 17:50:20"}],"locationInfo":{"gps":{"longitude":"-999.0","latitude":"-999.0","satelliteCount":0,"satelliteCountInUsed":0,"satelliteAverSNR":"0.00","distance":"0.0","lockStatus":"1","lockMeter":"200","proId":"","proName":""},"baseSite":{"MCC":"","MNC":"","CID":"-1","LAC":"-1","DBM":"-999","IMSI":"","ICCID":""},"wifi":{"SSID":"fubox_2.4G","IP":"**************","MAC":"BE:87:C2:42:DF:75","SSTH":"-34","BSSID":"7c:10:c9:d6:9f:10"},"address":"无法获取"},"hardwareInfo":[{"name":"CPU Usage","desc":"cpu:0.50"},{"name":"storage_usage_status","desc":" 4.57GB \/ 32.00GB ","state":"1"},{"name":"total_memory","desc":" 0.69GB \/ 1.86GB ","state":"1"},{"name":"androidVersion","desc":"14","state":"1"},{"name":"network_status","desc":"wifi","state":"1"},{"name":"OSVer","desc":"14","state":"1"}],"wifiOption":[{"SSID":"2306","SSTH":"-71"},{"SSID":"2205","SSTH":"-51"},{"SSID":"2205_5G","SSTH":"-56"},{"SSID":"DIRECT-7D-HP Laser 136w","SSTH":"-68"},{"SSID":"fubox_2.4G","SSTH":"-35"},{"SSID":"DIRECT-CC-HP Laser 1188w","SSTH":"-71"},{"SSID":"2206","SSTH":"-26"},{"SSID":"2103","SSTH":"-56"},{"SSID":"DIRECT-1D-HP Laser 1188w","SSTH":"-85"},{"SSID":"2206-5G","SSTH":"-35"},{"SSID":"DIRECT-C6-Mi All-in-One Inkjet","SSTH":"-51"},{"SSID":"2207-5G","SSTH":"-78"},{"SSID":"2207","SSTH":"-59"},{"SSID":"@Ruijie-1816_5G","SSTH":"-68"},{"SSID":"@Ruijie-1816","SSTH":"-55"}],"sytemInfo":{"androidVersion":"14","buildNumber":"BASE_D30M-MU_GEN_AP_V1.0.8.1_202507032000_ota_test","aspl":"2025-03-05"}},"myVersionName":"1.1.03.20250820.DSPREAD.MDM.SERVICE","terminalDate":"20250820182516"}
2025-08-20 18:25:16.383  4108-4237  WebSocket               com.dspread.mdm.service              I  🔧 C0109 终端信息上传完成 (1, trigger=first_connection)
2025-08-20 18:25:16.389  4108-4237  Common                  com.dspread.mdm.service              I  ✅ 初始化信息发送完成
2025-08-20 18:25:16.396  4108-4237  WebSocket               com.dspread.mdm.service              I  🔧 检查服务更新结果
2025-08-20 18:25:16.402  4108-4237  Task                    com.dspread.mdm.service              W  ⚠️ 任务列表为空，跳过服务更新结果检查
2025-08-20 18:25:16.408  4108-4237  WebSocket               com.dspread.mdm.service              I  🔧 检查OS升级状态...
2025-08-20 18:25:16.416  4108-4237  Common                  com.dspread.mdm.service              D  🔧 OsUpdateStatusChecker WebSocket连接成功，检查OS升级状态
2025-08-20 18:25:16.423  4108-4237  Common                  com.dspread.mdm.service              D  🔧 OsUpdateStatusChecker 没有待检查的OS升级任务
2025-08-20 18:25:16.429  4108-4237  WebSocket               com.dspread.mdm.service              I  🔧 OS升级状态检查已触发
2025-08-20 18:25:16.435  4108-4237  WebSocket               com.dspread.mdm.service              I  🔧 首次连接初始化检查完成
2025-08-20 18:25:16.485  4108-4237  Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"request_time":"1755685516991","org_request_time":"1755685516070","org_request_id":"1755685516070C0906","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755685516991S0000","serialNo":"01354090202503050399"}
2025-08-20 18:25:16.494  4108-4237  WebSocket               com.dspread.mdm.service              I  🔧 收到响应消息: transCode=S0000, orgRequestId=1755685516070C0906, state=0, remark=
2025-08-20 18:25:16.501  4108-4237  WebSocket               com.dspread.mdm.service              I  🔧 处理设备信息响应: state=0
2025-08-20 18:25:16.507  4108-4237  WebSocket               com.dspread.mdm.service              I  🔧 设备信息上传成功
2025-08-20 18:25:16.857  4108-4237  Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"request_time":"1755685517332","org_request_time":"1755685516311","org_request_id":"1755685516311C0109","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755685517332S0000","serialNo":"01354090202503050399"}
2025-08-20 18:25:16.866  4108-4237  WebSocket               com.dspread.mdm.service              I  🔧 收到响应消息: transCode=S0000, orgRequestId=1755685516311C0109, state=0, remark=
2025-08-20 18:25:16.872  4108-4237  WebSocket               com.dspread.mdm.service              I  🔧 终端信息上传成功
2025-08-20 18:25:17.607  4108-4237  WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误 (Ask Gemini)
                                                                                                    com.neovisionaries.ws.client.WebSocketException: An I/O error occurred while a frame was being read from the web socket: Software caused connection abort
                                                                                                    	at com.neovisionaries.ws.client.ReadingThread.readFrame(ReadingThread.java:375)
                                                                                                    	at com.neovisionaries.ws.client.ReadingThread.main(ReadingThread.java:99)
                                                                                                    	at com.neovisionaries.ws.client.ReadingThread.runMain(ReadingThread.java:64)
                                                                                                    	at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45)
                                                                                                    Caused by: java.net.SocketException: Software caused connection abort
                                                                                                    	at java.net.SocketInputStream.socketRead0(Native Method)
                                                                                                    	at java.net.SocketInputStream.socketRead(SocketInputStream.java:118)
                                                                                                    	at java.net.SocketInputStream.read(SocketInputStream.java:173)
                                                                                                    	at java.net.SocketInputStream.read(SocketInputStream.java:143)
                                                                                                    	at com.android.org.conscrypt.ConscryptEngineSocket$SSLInputStream.readFromSocket(ConscryptEngineSocket.java:983)
                                                                                                    	at com.android.org.conscrypt.ConscryptEngineSocket$SSLInputStream.processDataFromSocket(ConscryptEngineSocket.java:947)
                                                                                                    	at com.android.org.conscrypt.ConscryptEngineSocket$SSLInputStream.readUntilDataAvailable(ConscryptEngineSocket.java:862)
                                                                                                    	at com.android.org.conscrypt.ConscryptEngineSocket$SSLInputStream.read(ConscryptEngineSocket.java:835)
                                                                                                    	at java.io.BufferedInputStream.fill(BufferedInputStream.java:239)
                                                                                                    	at java.io.BufferedInputStream.read1(BufferedInputStream.java:279)
                                                                                                    	at java.io.BufferedInputStream.read(BufferedInputStream.java:338)
                                                                                                    	at java.io.FilterInputStream.read(FilterInputStream.java:133)
                                                                                                    	at com.neovisionaries.ws.client.WebSocketInputStream.readBytes(WebSocketInputStream.java:165)
                                                                                                    	at com.neovisionaries.ws.client.WebSocketInputStream.readFrame(WebSocketInputStream.java:46)
                                                                                                    	at com.neovisionaries.ws.client.ReadingThread.readFrame(ReadingThread.java:338)
                                                                                                    	at com.neovisionaries.ws.client.ReadingThread.main(ReadingThread.java:99) 
                                                                                                    	at com.neovisionaries.ws.client.ReadingThread.runMain(ReadingThread.java:64) 
                                                                                                    	at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45) 
2025-08-20 18:25:17.620  4108-4237  WebSocket               com.dspread.mdm.service              I  🔧 onError: 不触发重连，等待 onDisconnected 处理
2025-08-20 18:25:17.658  4108-4108  Receiver                com.dspread.mdm.service              D  🔧 网络状态变化: 连接=false, 类型=NONE
2025-08-20 18:25:17.667  4108-4108  Receiver                com.dspread.mdm.service              D  🔧 网络不可用，WebSocket连接可能受影响
2025-08-20 18:25:17.675  4108-4108  Receiver                com.dspread.mdm.service              D  🔧 ServiceManagementEventHandler 处理网络变化事件
2025-08-20 18:25:17.687  4108-4238  WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误 (Ask Gemini)
                                                                                                    com.neovisionaries.ws.client.WebSocketException: Flushing frames to the server failed: Broken pipe
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.doFlush(WritingThread.java:434)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.sendFrames(WritingThread.java:384)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.main(WritingThread.java:108)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.runMain(WritingThread.java:55)
                                                                                                    	at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45)
                                                                                                    Caused by: java.net.SocketException: Broken pipe
                                                                                                    	at java.net.SocketOutputStream.socketWrite0(Native Method)
                                                                                                    	at java.net.SocketOutputStream.socketWrite(SocketOutputStream.java:116)
                                                                                                    	at java.net.SocketOutputStream.write(SocketOutputStream.java:156)
                                                                                                    	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.writeToSocket(ConscryptEngineSocket.java:762)
                                                                                                    	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.writeInternal(ConscryptEngineSocket.java:736)
                                                                                                    	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.write(ConscryptEngineSocket.java:699)
                                                                                                    	at java.io.BufferedOutputStream.flushBuffer(BufferedOutputStream.java:81)
                                                                                                    	at java.io.BufferedOutputStream.flush(BufferedOutputStream.java:142)
                                                                                                    	at java.io.BufferedOutputStream.flush(BufferedOutputStream.java:143)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.flush(WritingThread.java:271)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.doFlush(WritingThread.java:422)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.sendFrames(WritingThread.java:384) 
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.main(WritingThread.java:108) 
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.runMain(WritingThread.java:55) 
                                                                                                    	at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45) 
2025-08-20 18:25:17.702  4108-4108  Common                  com.dspread.mdm.service              D  🔧 ServiceStartupManager 服务正在运行，无需启动: NETWORK_CHANGE
2025-08-20 18:25:17.715  4108-4108  Receiver                com.dspread.mdm.service              D  🔧 ServiceManagementEventHandler 网络变化后服务检查完成
2025-08-20 18:25:17.725  4108-4108  Provisioning            com.dspread.mdm.service              D  🔧 ProvisioningEventHandler 网络状态变化
2025-08-20 18:25:17.738  4108-4108  Provisioning            com.dspread.mdm.service              D  🔧 ProvisioningEventHandler 网络不可用，跳过处理
2025-08-20 18:25:17.757  4108-4238  WebSocket               com.dspread.mdm.service              I  🔧 onError: 不触发重连，等待 onDisconnected 处理
2025-08-20 18:25:17.777  4108-4238  WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误 (Ask Gemini)
                                                                                                    com.neovisionaries.ws.client.WebSocketException: Flushing frames to the server failed: Broken pipe
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.doFlush(WritingThread.java:434)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.sendFrames(WritingThread.java:384)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.main(WritingThread.java:120)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.runMain(WritingThread.java:55)
                                                                                                    	at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45)
                                                                                                    Caused by: java.net.SocketException: Broken pipe
                                                                                                    	at java.net.SocketOutputStream.socketWrite0(Native Method)
                                                                                                    	at java.net.SocketOutputStream.socketWrite(SocketOutputStream.java:116)
                                                                                                    	at java.net.SocketOutputStream.write(SocketOutputStream.java:156)
                                                                                                    	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.writeToSocket(ConscryptEngineSocket.java:762)
                                                                                                    	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.writeInternal(ConscryptEngineSocket.java:736)
                                                                                                    	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.write(ConscryptEngineSocket.java:699)
                                                                                                    	at java.io.BufferedOutputStream.flushBuffer(BufferedOutputStream.java:81)
                                                                                                    	at java.io.BufferedOutputStream.flush(BufferedOutputStream.java:142)
                                                                                                    	at java.io.BufferedOutputStream.flush(BufferedOutputStream.java:143)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.flush(WritingThread.java:271)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.doFlush(WritingThread.java:422)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.sendFrames(WritingThread.java:384) 
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.main(WritingThread.java:120) 
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.runMain(WritingThread.java:55) 
                                                                                                    	at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45) 
2025-08-20 18:25:17.787  4108-4238  WebSocket               com.dspread.mdm.service              I  🔧 onError: 不触发重连，等待 onDisconnected 处理
2025-08-20 18:25:17.798  4108-4238  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接断开 - 客户端主动断开
2025-08-20 18:25:17.806  4108-4238  WebSocket               com.dspread.mdm.service              I  🔧    服务器关闭帧: code=-1, reason=无原因
2025-08-20 18:25:17.815  4108-4238  WebSocket               com.dspread.mdm.service              I  🔧    客户端关闭帧: code=1008, reason=An I/O error occurred while a frame was being read from the web socket: Software caused connection abort
2025-08-20 18:25:17.823  4108-4238  WebSocket               com.dspread.mdm.service              I  🔧    断开发起方: 客户端
2025-08-20 18:25:17.832  4108-4238  WebSocket               com.dspread.mdm.service              I  🔧 停止PING/PONG检测
2025-08-20 18:25:17.840  4108-4238  WebSocket               com.dspread.mdm.service              I  🔧 reconnect() 被调用，调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onDisconnected:727
2025-08-20 18:25:17.847  4108-4238  WebSocket               com.dspread.mdm.service              I  🔧 开始重连 WebSocket... (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onDisconnected:727)
2025-08-20 18:25:17.867  4108-4238  WebSocket               com.dspread.mdm.service              I  🔧 使用Provisioning配置的重连策略: delayPolicy=1, delaySwitch=1, delayTime=60
2025-08-20 18:25:17.875  4108-4238  WebSocket               com.dspread.mdm.service              I  🔧 当前断开记录数: 1, 延迟开关: 1
2025-08-20 18:25:17.882  4108-4238  WebSocket               com.dspread.mdm.service              I  🔧 重连策略计算完成: 第1次重连，间隔3000ms (3秒)
2025-08-20 18:25:17.891  4108-4238  WebSocket               com.dspread.mdm.service              I  🔧 开始第1次重连，间隔3000ms (3秒) (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onDisconnected:727)
2025-08-20 18:25:20.914  4108-4108  WebSocket               com.dspread.mdm.service              I  🔧 重连 WebSocket: wss://api.dspreadserv.net/status/websocket/register?serialNo=MDEzNTQwOTAyMDI1MDMwNTAzOTk=&publicKey=TUlHZk1BMEdDU3FHU0liM0RRRUJBUVVBQTRHTkFEQ0JpUUtCZ1FEbVlURlNoN0dndHl2b2t5L2tkNnpoS1h3VXliNHNEWVdiTGdjME9YYWtWNm9XTisrTDdaV0sxR1F5eDVDK2htUDJ3ZzFQWXQvek9za2ZNdGV2MGQ3aThIQ2ptd2gwdGtQUUYzaHp3ZkY1NVEwNEp6SldEdXdFVXQ2VjRWUkpBWm41WDgvWFZQc1p0NVUzL20waTZEMmJjUDVia21RbDJIYjc2ZjdtdGwzWVNRSURBUUFC&query=0&msgVer=3&timestamp=1755685520900&signature=Cddvv47V1D9cvCa4x8x+vHDHRvCJorU4XGnP4RRFNAYwGk+W9bgytbJzQRO5ixW6eXd4tuqv8mF1qrzfej8WgFHO6z574n6hJAHsBT0EChmngBpsEUuhotY28czvoDUR5dhw8A8psTaaSus+TJ5/xIuicRKl95SGk7R1NHlpJh8=
2025-08-20 18:25:20.923  4108-4108  WebSocket               com.dspread.mdm.service              I  🔧 未检测到系统代理设置
2025-08-20 18:25:20.962  4108-4256  WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误
2025-08-20 18:25:20.971  4108-4256  WebSocket               com.dspread.mdm.service              I  🔧 onError: 不触发重连，等待 onDisconnected 处理
2025-08-20 18:25:20.981  4108-4256  WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误
2025-08-20 18:25:20.992  4108-4256  WebSocket               com.dspread.mdm.service              I  🔧 reconnect() 被调用，调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onConnectError:672
2025-08-20 18:25:20.999  4108-4256  WebSocket               com.dspread.mdm.service              I  🔧 开始重连 WebSocket... (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onConnectError:672)
2025-08-20 18:25:21.019  4108-4256  WebSocket               com.dspread.mdm.service              I  🔧 使用Provisioning配置的重连策略: delayPolicy=1, delaySwitch=1, delayTime=60
2025-08-20 18:25:21.026  4108-4256  WebSocket               com.dspread.mdm.service              I  🔧 当前断开记录数: 1, 延迟开关: 1
2025-08-20 18:25:21.034  4108-4256  WebSocket               com.dspread.mdm.service              I  🔧 重连策略计算完成: 第2次重连，间隔3000ms (3秒)
2025-08-20 18:25:21.044  4108-4256  WebSocket               com.dspread.mdm.service              I  🔧 开始第2次重连，间隔3000ms (3秒) (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onConnectError:672)
2025-08-20 18:25:21.473  4108-4108  Receiver                com.dspread.mdm.service              D  🔧 网络状态变化: 连接=true, 类型=WIFI
2025-08-20 18:25:21.486  4108-4108  Receiver                com.dspread.mdm.service              D  🔧 网络恢复，尝试重新连接WebSocket
2025-08-20 18:25:21.495  4108-4108  Receiver                com.dspread.mdm.service              D  🔧 ServiceManagementEventHandler 处理网络变化事件
2025-08-20 18:25:21.510  4108-4108  Common                  com.dspread.mdm.service              D  🔧 ServiceStartupManager 服务正在运行，无需启动: NETWORK_CHANGE
2025-08-20 18:25:21.530  4108-4108  Receiver                com.dspread.mdm.service              D  🔧 ServiceManagementEventHandler 网络变化后服务检查完成
2025-08-20 18:25:21.538  4108-4108  Provisioning            com.dspread.mdm.service              D  🔧 ProvisioningEventHandler 网络状态变化
2025-08-20 18:25:21.548  4108-4108  Provisioning            com.dspread.mdm.service              I  ℹ️ ProvisioningEventHandler 检测到网络重连，检查配置更新
2025-08-20 18:25:21.557  4108-4108  Provisioning            com.dspread.mdm.service              D  🔧 ProvisioningEventHandler 开始执行Provisioning，触发类型: NETWORK_CONNECTED，配置URL: https://config.dspreadserv.net/status/config
2025-08-20 18:25:21.566  4108-4147  Provisioning            com.dspread.mdm.service              I  ℹ️ 开始执行Provisioning配置，触发类型: NETWORK_CONNECTED
2025-08-20 18:25:21.576  4108-4147  Provisioning            com.dspread.mdm.service              D  🔧 ProvisioningEventHandler Provisioning进度 (NETWORK_CONNECTED): 正在下载配置文件...
2025-08-20 18:25:21.584  4108-4147  Provisioning            com.dspread.mdm.service              I  ℹ️ 开始请求远程配置...
2025-08-20 18:25:21.595  4108-4147  Provisioning            com.dspread.mdm.service              I  ℹ️ 请求配置: https://config.dspreadserv.net/status/config?SN=MDEzNTQwOTAyMDI1MDMwNTAzOTk=&timestamp=1755685521594
2025-08-20 18:25:21.623  4108-4147  TrafficStats            com.dspread.mdm.service              D  tagSocket(163) with statsTag=0xffffffff, statsUid=-1
2025-08-20 18:25:24.070  4108-4108  WebSocket               com.dspread.mdm.service              I  🔧 重连 WebSocket: wss://api.dspreadserv.net/status/websocket/register?serialNo=MDEzNTQwOTAyMDI1MDMwNTAzOTk=&publicKey=TUlHZk1BMEdDU3FHU0liM0RRRUJBUVVBQTRHTkFEQ0JpUUtCZ1FEbVlURlNoN0dndHl2b2t5L2tkNnpoS1h3VXliNHNEWVdiTGdjME9YYWtWNm9XTisrTDdaV0sxR1F5eDVDK2htUDJ3ZzFQWXQvek9za2ZNdGV2MGQ3aThIQ2ptd2gwdGtQUUYzaHp3ZkY1NVEwNEp6SldEdXdFVXQ2VjRWUkpBWm41WDgvWFZQc1p0NVUzL20waTZEMmJjUDVia21RbDJIYjc2ZjdtdGwzWVNRSURBUUFC&query=0&msgVer=3&timestamp=1755685524055&signature=Bw0g67z2m4DjOLMtrcqwD8g7vXGiRI04cZZHuNwVOdrn5bHuT9NFr/ZC+9llxuwNuPPkgioV2jOP80vvvwM/6PjmaTVZghmdCaucul+6m/ORrYF8ID1w6/raqkYrSR97UCHkGKuLIPCh4S3co9FV4/TkFYuDOrb0R4EWQjVZtQM=
2025-08-20 18:25:24.078  4108-4108  WebSocket               com.dspread.mdm.service              I  🔧 未检测到系统代理设置
2025-08-20 18:25:24.119  4108-4284  TrafficStats            com.dspread.mdm.service              D  tagSocket(171) with statsTag=0xffffffff, statsUid=-1
2025-08-20 18:25:24.369  4108-4285  TrafficStats            com.dspread.mdm.service              D  tagSocket(174) with statsTag=0xffffffff, statsUid=-1
2025-08-20 18:25:25.176  4108-4147  Provisioning            com.dspread.mdm.service              I  ℹ️ 远程配置获取成功
2025-08-20 18:25:25.184  4108-4147  Provisioning            com.dspread.mdm.service              I  ℹ️ 远程配置请求成功
2025-08-20 18:25:25.192  4108-4147  Provisioning            com.dspread.mdm.service              I  ℹ️ 远程配置解析成功 - CID: 1001
2025-08-20 18:25:25.225  4108-4147  Provisioning            com.dspread.mdm.service              I  ℹ️ API响应已保存到本地: /data/pos/config/provisioning_config.json, 具体内容为:
                                                                                                    {"mode":"dev","data":{"requestTime":"1755685525721","customization":{"system":{"timezone":"Asia\/Hong_Kong","logo":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/test\/logo.bin","logoMd5":"ba1ee533924eae5c408465e7cddcbda4","bootAnimationMd5":"ba1ee533924eae5c408465e7cddcbda4","gps":{"minUpdateTime":"30","scheduleTime":"60","maxLocateTime":"0","minDistance":"10","valid_distance":"500","care":"1"},"powerSaveMode":{"enable":"1","screenTimeout":"0"},"bootAnimation":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/test\/bootanimation.zip"},"polling":{"heartbeatTime":"300","terminalInfoTime":"900","statusApiUrl":"wss:\/\/api.dspreadserv.net\/status\/websocket\/register","remoteUrl":"wss:\/\/remote.dspreadserv.net\/remoteWSS\/websockify","uploadMode":"1","wssreconn":{"pingInterval":"30","delayPolicy":"1","delaySwitch":"1","delayTime":"60"}}},"client":"default","cid":"1001"},"function":"getProvisioningConfig","description":"SUCCESS","stateCode":"0","version":"v1.0.1.20250801"}
2025-08-20 18:25:25.233  4108-4147  Provisioning            com.dspread.mdm.service              D  🔧 ProvisioningEventHandler Provisioning进度 (NETWORK_CONNECTED): 正在下载 Logo...
2025-08-20 18:25:25.408  4108-4147  Provisioning            com.dspread.mdm.service              I  ℹ️ 文件已存在且MD5匹配，跳过下载: Logo
2025-08-20 18:25:25.414  4108-4147  Provisioning            com.dspread.mdm.service              D  🔧 ProvisioningEventHandler Provisioning进度 (NETWORK_CONNECTED): 正在下载 BootAnimation...
2025-08-20 18:25:25.487  4108-4147  Provisioning            com.dspread.mdm.service              I  ℹ️ 文件已存在且MD5匹配，跳过下载: BootAnimation
2025-08-20 18:25:25.493  4108-4147  Provisioning            com.dspread.mdm.service              D  🔧 ProvisioningEventHandler Provisioning进度 (NETWORK_CONNECTED): 正在应用配置...
2025-08-20 18:25:25.499  4108-4147  Provisioning            com.dspread.mdm.service              I  ℹ️ 配置应用完成
2025-08-20 18:25:25.504  4108-4147  Provisioning            com.dspread.mdm.service              I  ℹ️ 远程配置成功，标记首次配置完成
2025-08-20 18:25:25.512  4108-4147  Provisioning            com.dspread.mdm.service              D  🔧 状态标志保存成功: /data/pos/config/provisioning_flags.json
2025-08-20 18:25:25.518  4108-4147  Provisioning            com.dspread.mdm.service              I  ℹ️ Provisioning配置执行完成
2025-08-20 18:25:25.524  4108-4147  Provisioning            com.dspread.mdm.service              I  ℹ️ ProvisioningEventHandler Provisioning完成 (NETWORK_CONNECTED): 配置执行完成
2025-08-20 18:25:25.727  4108-4290  Common                  com.dspread.mdm.service              I  ✅ WebSocket 物理连接成功，等待服务器 hellMsg
2025-08-20 18:25:25.734  4108-4290  WebSocket               com.dspread.mdm.service              I  🔧 ===== 心跳发送器实际配置 =====
2025-08-20 18:25:25.741  4108-4290  WebSocket               com.dspread.mdm.service              I  🔧 实际 PING 发送间隔: 30000ms (30秒)
2025-08-20 18:25:25.747  4108-4290  WebSocket               com.dspread.mdm.service              I  🔧 PING 定时器名称: PingSender
2025-08-20 18:25:25.754  4108-4290  WebSocket               com.dspread.mdm.service              I  🔧 实际 PONG 发送间隔: 0ms (0秒)
2025-08-20 18:25:25.760  4108-4290  WebSocket               com.dspread.mdm.service              I  🔧 PONG 定时器名称: PongSender
2025-08-20 18:25:25.765  4108-4290  WebSocket               com.dspread.mdm.service              I  🔧 ===============================
2025-08-20 18:25:25.771  4108-4290  WebSocket               com.dspread.mdm.service              I  🔧 启动PING/PONG检测，阈值: 连续3个PING无PONG响应
2025-08-20 18:25:25.805  4108-4290  Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"hellMsg":"hello 01354090202503050399","tranCode":"S0000","version":"1","rebootTime":"01:31:12","serialNo":"01354090202503050399","deviceStatus":"6"}
2025-08-20 18:25:25.811  4108-4290  Common                  com.dspread.mdm.service              I  ✅ 收到服务器 hellMsg，WebSocket 业务连接成功！
2025-08-20 18:25:25.817  4108-4290  Common                  com.dspread.mdm.service              I  ✅ WebSocket 业务连接成功！
2025-08-20 18:25:25.823  4108-4290  WebSocket               com.dspread.mdm.service              I  🔧 业务连接成功，重连计数已重置为 0
2025-08-20 18:25:25.828  4108-4290  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接成功，检查自身更新任务状态
2025-08-20 18:25:25.836  4108-4290  Receiver                com.dspread.mdm.service              D  🔧 发送广播: com.dspread.mdm.service.CHECK_SELF_UPDATE_STATUS
2025-08-20 18:25:25.843  4108-4290  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接成功
2025-08-20 18:25:25.848  4108-4290  WebSocket               com.dspread.mdm.service              I  🔧 设备状态: 6
2025-08-20 18:25:25.854  4108-4290  WebSocket               com.dspread.mdm.service              I  🔧 检查首次连接标志: isFirstTimeWebSocketConnected=false
2025-08-20 18:25:25.860  4108-4290  WebSocket               com.dspread.mdm.service              I  🔧 非首次连接，跳过初始化信息发送和初始化检查
2025-08-20 18:25:25.865  4108-4290  WebSocket               com.dspread.mdm.service              I  🔧 执行必要的状态同步检查
2025-08-20 18:25:25.871  4108-4290  WebSocket               com.dspread.mdm.service              I  🔧 检查服务更新结果
2025-08-20 18:25:25.876  4108-4290  Task                    com.dspread.mdm.service              W  ⚠️ 任务列表为空，跳过服务更新结果检查
2025-08-20 18:25:25.882  4108-4290  WebSocket               com.dspread.mdm.service              I  🔧 检查OS升级状态...
2025-08-20 18:25:25.887  4108-4290  Common                  com.dspread.mdm.service              D  🔧 OsUpdateStatusChecker WebSocket连接成功，检查OS升级状态
2025-08-20 18:25:25.893  4108-4290  Common                  com.dspread.mdm.service              D  🔧 OsUpdateStatusChecker 没有待检查的OS升级任务
2025-08-20 18:25:25.899  4108-4290  WebSocket               com.dspread.mdm.service              I  🔧 OS升级状态检查已触发
