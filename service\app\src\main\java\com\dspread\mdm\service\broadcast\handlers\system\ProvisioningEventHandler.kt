package com.dspread.mdm.service.broadcast.handlers.system

import android.annotation.SuppressLint
import android.app.AlarmManager
import android.content.Context
import android.content.Intent
import android.net.ConnectivityManager
import android.os.Build
import com.dspread.mdm.service.broadcast.core.BroadcastActions
import com.dspread.mdm.service.broadcast.core.BroadcastEventHandler
import com.dspread.mdm.service.modules.provisioning.ProvisioningManager
import com.dspread.mdm.service.modules.provisioning.model.ProvisioningTrigger as ProvisioningTriggerType
import com.dspread.mdm.service.platform.api.network.NetworkApi
import com.dspread.mdm.service.utils.log.Logger
import com.dspread.mdm.service.utils.storage.PreferencesManager
import com.dspread.mdm.service.config.TimerConfig
import com.dspread.mdm.service.broadcast.core.BroadcastSender
import com.dspread.mdm.service.config.ProvisionConfig

/**
 * Provisioning事件处理器
 * 统一处理Provisioning定时器相关的广播和定时器管理
 */
class ProvisioningEventHandler : BroadcastEventHandler {
    
    companion object {
        private const val TAG = "ProvisioningEventHandler"

        private const val PROVISIONING_INTERVAL_KEY = "provisioning_interval"
        private const val PROVISIONING_CONFIG_URL_KEY = "provisioning_config_url"
    }

    override fun getHandlerName(): String = TAG
    
    override fun getSupportedActions(): List<String> {
        return listOf(
            BroadcastActions.ACTION_PROVISIONING_TIMER,
            ConnectivityManager.CONNECTIVITY_ACTION,
            Intent.ACTION_BOOT_COMPLETED
        )
    }
    
    override fun handleBroadcast(context: Context, intent: Intent): Boolean {
        val action = intent.action ?: return false
        
        return try {
            when (action) {
                BroadcastActions.ACTION_PROVISIONING_TIMER -> {
                    handleProvisioningTimer(context)
                    true
                }
                ConnectivityManager.CONNECTIVITY_ACTION -> {
                    handleNetworkChange(context)
                    true
                }
                Intent.ACTION_BOOT_COMPLETED -> {
                    handleBootCompleted(context)
                    true
                }
                else -> false
            }
        } catch (e: Exception) {
            Logger.receiverE("$TAG 处理广播失败: $action", e)
            false
        }
    }
    
    /**
     * 处理Provisioning定时器
     */
    private fun handleProvisioningTimer(context: Context) {
        try {
            // 安排下一次Provisioning（保持定时器持续运行）
            scheduleNextProvisioning(context)
            
            // 执行Provisioning
            performProvisioning(context)
            
        } catch (e: Exception) {
            Logger.provE("$TAG 处理Provisioning定时器失败", e)
        }
    }
    
    /**
     * 启动Provisioning定时器
     */
    fun startProvisioningTimer(context: Context) {
        try {
            val interval = getProvisioningInterval(context)
            Logger.prov("$TAG 启动Provisioning定时器: ${interval}s (${interval/3600}小时)")
            
            // 直接安排第一次Provisioning检查，不需要立即发送广播
            scheduleNextProvisioning(context)
            
        } catch (e: Exception) {
            Logger.provE("$TAG 启动Provisioning定时器失败", e)
        }
    }
    
    /**
     * 停止Provisioning定时器
     */
    fun stopProvisioningTimer(context: Context) {
        try {
            val pendingIntent = BroadcastSender.createDynamicPendingIntent(
                context,
                BroadcastActions.ACTION_PROVISIONING_TIMER,
                0
            )
            
            val alarmManager = context.getSystemService(Context.ALARM_SERVICE) as AlarmManager
            alarmManager.cancel(pendingIntent)
            
            Logger.prov("$TAG Provisioning定时器已停止")
            
        } catch (e: Exception) {
            Logger.provE("$TAG 停止Provisioning定时器失败", e)
        }
    }
    
    /**
     * 安排下一次Provisioning
     */
    @SuppressLint("ScheduleExactAlarm")
    private fun scheduleNextProvisioning(context: Context) {
        try {
            val interval = getProvisioningInterval(context)
            
            val pendingIntent = BroadcastSender.createDynamicPendingIntent(
                context,
                BroadcastActions.ACTION_PROVISIONING_TIMER,
                0
            )
            
            val alarmManager = context.getSystemService(Context.ALARM_SERVICE) as AlarmManager
            val triggerTime = System.currentTimeMillis() + (interval * 1000)
            
            // 取消之前的定时器
            alarmManager.cancel(pendingIntent)
            
            // 使用精确的闹钟
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                alarmManager.setExactAndAllowWhileIdle(
                    AlarmManager.RTC_WAKEUP,
                    triggerTime,
                    pendingIntent
                )
            } else {
                alarmManager.setExact(
                    AlarmManager.RTC_WAKEUP,
                    triggerTime,
                    pendingIntent
                )
            }
            
            Logger.success("⏰ 设置初始化定时器成功，下次执行: ${interval}秒后 (${interval/3600}小时)")
            
        } catch (e: Exception) {
            Logger.provE("$TAG 安排下一次Provisioning失败", e)
        }
    }
    
    /**
     * 获取Provisioning间隔（支持调试模式和生产模式）
     */
    private fun getProvisioningInterval(context: Context): Long {
        return TimerConfig.getProvisioningInterval()
    }
    
    /**
     * 执行定时Provisioning
     */
    private fun performProvisioning(context: Context) {
        val configUrl = getConfigUrl()
        performProvisioning(context, configUrl, ProvisioningTriggerType.TIMER)
    }
    
    /**
     * 获取配置URL
     */
    private fun getConfigUrl(): String {
        return try {
            val configUrl = PreferencesManager.getString(PROVISIONING_CONFIG_URL_KEY, ProvisionConfig.DEFAULT_CONFIG_URL)
            configUrl.ifEmpty {
                ProvisionConfig.DEFAULT_CONFIG_URL
            }
        } catch (e: Exception) {
            Logger.provE("获取配置URL失败，使用默认值", e)
            ProvisionConfig.DEFAULT_CONFIG_URL
        }
    }

    /**
     * 处理网络状态变化
     */
    private fun handleNetworkChange(context: Context) {
        try {
            Logger.prov("$TAG 网络状态变化")

            // 忽略初始启动时的网络连接事件
            val provisioningManager = ProvisioningManager.getInstance(context)
            if (provisioningManager.isInitialStartup()) {
                Logger.prov("$TAG 忽略初始启动时的网络连接事件")
                return
            }

            if (!NetworkApi.isNetworkAvailable(context)) {
                Logger.prov("$TAG 网络不可用，跳过处理")
                return
            }

            val flags = provisioningManager.getFlags()

            if (!flags.isFirstProvisioningCompleted) {
                // 首次配置未完成：立即重试
                Logger.provI("$TAG 检测到网络重连且Provisioning未成功，开始重试配置")
                val configUrl = getConfigUrl()
                performProvisioning(context, configUrl, ProvisioningTriggerType.NETWORK_CONNECTED)
            } else {
                // 首次配置已完成：不在网络重连时重试，按正常定时器间隔检查更新
                Logger.provI("$TAG 检测到网络重连，但Provisioning已完成，按正常定时器间隔检查更新")
            }

        } catch (e: Exception) {
            Logger.provE("$TAG 处理网络状态变化失败", e)
        }
    }

    /**
     * 处理系统启动完成
     */
    private fun handleBootCompleted(context: Context) {
        try {
            Logger.prov("$TAG 系统启动完成")

            val provisioningManager = ProvisioningManager.getInstance(context)
            val flags = provisioningManager.getFlags()

            if (!flags.isFirstProvisioningCompleted && NetworkApi.isNetworkAvailable(context)) {
                Logger.provI("$TAG 系统启动后执行首次配置")

                val configUrl = getConfigUrl()
                performProvisioning(context, configUrl, ProvisioningTriggerType.FIRST_BOOT)
            } else {
                Logger.prov("$TAG Provisioning已完成或网络不可用，跳过首次配置")
            }

        } catch (e: Exception) {
            Logger.provE("$TAG 处理系统启动完成失败", e)
        }
    }

    /**
     * 手动触发Provisioning
     */
    fun triggerManually(context: Context) {
        try {
            Logger.prov("$TAG 手动触发Provisioning")

            val configUrl = getConfigUrl()
            performProvisioning(context, configUrl, ProvisioningTriggerType.MANUAL)

        } catch (e: Exception) {
            Logger.provE("$TAG 手动触发Provisioning失败", e)
        }
    }

    /**
     * 执行Provisioning的通用方法
     */
    private fun performProvisioning(context: Context, configUrl: String, triggerType: ProvisioningTriggerType) {
        try {
            Logger.prov("$TAG 开始执行Provisioning，触发类型: $triggerType，配置URL: $configUrl")

            val provisioningManager = ProvisioningManager.getInstance(context)

            provisioningManager.executeProvisioning(
                configUrl = configUrl,
                trigger = triggerType,
                callback = object : ProvisioningManager.ProvisioningCallback {
                    override fun onProgress(result: com.dspread.mdm.service.modules.provisioning.model.ProvisioningResult) {
                        Logger.prov("$TAG Provisioning进度 ($triggerType): ${result.message}")
                    }

                    override fun onCompleted(result: com.dspread.mdm.service.modules.provisioning.model.ProvisioningResult) {
                        Logger.provI("$TAG Provisioning完成 ($triggerType): ${result.message}")

                        // 只有定时器触发才需要安排下次执行
                        if (triggerType == ProvisioningTriggerType.TIMER) {
                            scheduleNextProvisioning(context)
                        }
                    }

                    override fun onError(result: com.dspread.mdm.service.modules.provisioning.model.ProvisioningResult) {
                        Logger.provE("$TAG Provisioning失败 ($triggerType): ${result.message}")

                        // 只有定时器触发才需要安排下次执行（即使失败也要继续）
                        if (triggerType == ProvisioningTriggerType.TIMER) {
                            scheduleNextProvisioning(context)
                        }
                    }
                }
            )

        } catch (e: Exception) {
            Logger.provE("$TAG 执行Provisioning失败", e)
        }
    }
}
