package com.bbpos.wiseapp.settings.wifi;

import android.content.Context;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import android.net.wifi.ScanResult;
import android.net.wifi.WifiConfiguration;
import android.net.wifi.WifiManager;

import com.bbpos.wiseapp.service.R;

/**
 * Created by <PERSON> on 2018/12/29.
 */

public class WifiController {
    /* security type */
    public static final int SECURITY_WEP = 1;
    public static final int SECURITY_PSK = 2;
    public static final int SECURITY_WPA_PSK = 3;
    public static final int SECURITY_WPA2_PSK = 4;
    public static final int SECURITY_EAP = 5;
    public static final int SECURITY_WAPI_PSK = 6;
    public static final int SECURITY_WAPI_CERT = 7;

    private WifiManager mWifiManager = null;
    private ConnectivityManager mConnectivityManager = null;
    private int netId;

    public int getCurNetID() {
        return netId;
    }

    public WifiController(ConnectivityManager connectivityManager, WifiManager wifiManager) {
        mConnectivityManager = connectivityManager;
        mWifiManager = wifiManager;
    }

    /**
     * 判断指定的wifi是否保存
     *
     * @param SSID
     * @return
     */
    public WifiConfiguration isWifiSave(String SSID) {
        if (mWifiManager != null) {
            for (WifiConfiguration existingConfig : mWifiManager.getConfiguredNetworks()) {
                if (existingConfig.SSID.equals("\"" + SSID + "\"")) {
                    return existingConfig;
                }
            }
        }
        return null;
    }

    public void connect(WifiConfiguration config) {
        netId = mWifiManager.addNetwork(config);
        mWifiManager.enableNetwork(netId, true);
    }

    //判断当前是否已经连接
    public boolean isGivenWifiConnect(String SSID) {
        return isWifiConnected() && getCurentWifiSSID().equals(SSID);
    }

    //得到当前连接的WiFi  SSID
    public String getCurentWifiSSID() {
        String ssid = "";
        ssid = mWifiManager.getConnectionInfo().getSSID();
        if (ssid.substring(0, 1).equals("\"")
                && ssid.substring(ssid.length() - 1).equals("\"")) {
            ssid = ssid.substring(1, ssid.length() - 1);
        }
        return ssid;
    }
    /**
     * 是否处于wifi连接的状态
     *
     */
    public boolean isWifiConnected() {
        NetworkInfo wifiNetworkInfo = mConnectivityManager.getNetworkInfo(ConnectivityManager.TYPE_WIFI);
        if (wifiNetworkInfo.isConnected()) {
            return true;
        }
        return false;
    }

    public void disConnectWifi(){
        mWifiManager.disconnect();
    }

    //断开指定ID的网络
    public void disConnectionWifi() {
        mWifiManager.disableNetwork(netId);
        mWifiManager.disconnect();
    }

    public WifiConfiguration creatWifiConfiguration(ScanResult currentWIFI, String password) {
        WifiConfiguration config = new WifiConfiguration();
        config.allowedAuthAlgorithms.clear();
        config.allowedGroupCiphers.clear();
        config.allowedKeyManagement.clear();
        config.allowedPairwiseCiphers.clear();
        config.allowedProtocols.clear();
        config.SSID = "\"" + currentWIFI.SSID + "\"";
        //如果当前连接的wifi被保存了密码，清除wifi保存信息
        WifiConfiguration tempConfig = isWifiSave(currentWIFI.SSID);
        if (tempConfig != null) {
            mWifiManager.removeNetwork(tempConfig.networkId);
            mWifiManager.saveConfiguration();
        }

        int accessPointSecurity = getSecurity(currentWIFI);
        switch (accessPointSecurity) {
            case SECURITY_WEP:
                if (password.length() != 0) {
                    int length = password.length();
                    // get selected WEP key index
                    int keyIndex = 0; // selected password index, 0~3
//                    if (mWEPKeyIndex != null
//                            && mWEPKeyIndex.getSelectedItemPosition() != AdapterView.INVALID_POSITION) {
//                        keyIndex = mWEPKeyIndex.getSelectedItemPosition();
//                    }
                    // WEP-40, WEP-104, and 256-bit WEP (WEP-232?)
                    if ((length == 10 || length == 26 || length == 32)
                            && password.matches("[0-9A-Fa-f]*")) {
                        //hex password
                        config.wepKeys[keyIndex] = password;
                    } else {
                        //ASCII password
                        config.wepKeys[keyIndex] = '"' + password + '"';
                    }
                    // set wep index to configuration
                    config.wepTxKeyIndex = keyIndex;
                }
                config.hiddenSSID = true;
                config.allowedAuthAlgorithms.set(WifiConfiguration.AuthAlgorithm.SHARED);
                config.allowedGroupCiphers.set(WifiConfiguration.GroupCipher.CCMP);
                config.allowedGroupCiphers.set(WifiConfiguration.GroupCipher.TKIP);
                config.allowedGroupCiphers.set(WifiConfiguration.GroupCipher.WEP40);
                config.allowedGroupCiphers.set(WifiConfiguration.GroupCipher.WEP104);
                config.allowedKeyManagement.set(WifiConfiguration.KeyMgmt.NONE);
                break;
            case SECURITY_WPA_PSK:
            case SECURITY_WPA2_PSK:
                if (password.length() != 0) {
                    if (password.matches("[0-9A-Fa-f]{64}")) {
                        config.preSharedKey = password;
                    } else {
                        config.preSharedKey = '"' + password + '"';
                    }
                }
                config.hiddenSSID = true;
                config.allowedAuthAlgorithms.set(WifiConfiguration.AuthAlgorithm.OPEN);
                config.allowedGroupCiphers.set(WifiConfiguration.GroupCipher.TKIP);
                config.allowedKeyManagement.set(WifiConfiguration.KeyMgmt.WPA_PSK);
                config.allowedPairwiseCiphers.set(WifiConfiguration.PairwiseCipher.TKIP);
                // config.allowedProtocols.set(WifiConfiguration.Protocol.WPA);
                config.allowedGroupCiphers.set(WifiConfiguration.GroupCipher.CCMP);
                config.allowedPairwiseCiphers.set(WifiConfiguration.PairwiseCipher.CCMP);
                config.status = WifiConfiguration.Status.ENABLED;
                break;
            case SECURITY_EAP:
//                if ("AKA".equals((String) eapMethodSpinner.getSelectedItem())
//                        || "SIM".equals((String) eapMethodSpinner.getSelectedItem())) {
//                    eapSimAkaConfig(config, eapMethodSpinner);
//                    BBLog.d(BBLog.TAG, "eap-sim/aka, config.toString(): " + config.toString());
//                }
                break;
            // add WAPI_PSK & WAPI_CERT
            case SECURITY_WAPI_PSK:
//                config.allowedKeyManagement.set(WifiConfiguration.KeyMgmt.WAPI_PSK);
//                config.allowedProtocols.set(WifiConfiguration.Protocol.WAPI);
//                config.allowedPairwiseCiphers.set(WifiConfiguration.PairwiseCipher.SMS4);
//                config.allowedGroupCiphers.set(WifiConfiguration.GroupCipher.SMS4);
//                if (password.length() != 0) {
////                    BBLog.v(BBLog.TAG, "getConfig(), mHex=" + mHex);
////                    if (mHex) { /* Hexadecimal */
////                        config.preSharedKey = password;
////                    } else { /* ASCII */
//                        config.preSharedKey = '"' + password + '"';
////                    }
//                }
                break;
            case SECURITY_WAPI_CERT:
//                config.allowedKeyManagement.set(WifiConfiguration.KeyMgmt.WAPI_CERT);
//                config.allowedProtocols.set(WifiConfiguration.Protocol.WAPI);
//                config.allowedPairwiseCiphers.set(WifiConfiguration.PairwiseCipher.SMS4);
//                config.allowedGroupCiphers.set(WifiConfiguration.GroupCipher.SMS4);
//                config.enterpriseConfig.setCaCertificateWapiAlias((mWapiAsCert.getSelectedItemPosition() == 0) ? ""
//                        : (String) mWapiAsCert.getSelectedItem());
//                config.enterpriseConfig.setClientCertificateWapiAlias((mWapiClientCert.getSelectedItemPosition() == 0) ? ""
//                        : (String) mWapiClientCert.getSelectedItem());
                break;
            default:
                config.allowedKeyManagement.set(WifiConfiguration.KeyMgmt.NONE);
                break;
        }
        return config;
    }

    /**
     * add other security, like as wapi, wep
     * @param config
     * @return
     */
    public static int getSecurity(WifiConfiguration config) {
        /* support wapi psk/cert */
//        if (config.allowedKeyManagement.get(WifiConfiguration.KeyMgmt.WAPI_PSK)) {
//            return SECURITY_WAPI_PSK;
//        }
//
//        if (config.allowedKeyManagement.get(WifiConfiguration.KeyMgmt.WAPI_CERT)) {
//            return SECURITY_WAPI_CERT;
//        }

        if (config.wepTxKeyIndex >= 0 && config.wepTxKeyIndex < config.wepKeys.length
                && config.wepKeys[config.wepTxKeyIndex] != null) {
            return SECURITY_WEP;
        }
        return -1;
    }

    public static int getSecurity(ScanResult result) {
        if (result.capabilities.contains("WAPI-PSK")) {
            return SECURITY_WAPI_PSK;
        } else if (result.capabilities.contains("WAPI-CERT")) {
            return SECURITY_WAPI_CERT;
        } else if (result.capabilities.contains("WPA-PSK")) {
            return SECURITY_WPA_PSK;
        } else if (result.capabilities.contains("WPA2-PSK")) {
            return SECURITY_WPA2_PSK;
        }
        return -1;
    }

    public String getSecurityString(int security, Context context) {

        switch(security) {
            case SECURITY_WAPI_PSK:
                /*return WAPI_PSK string */
                return context.getString(R.string.wifi_security_wapi_psk);
            case SECURITY_WAPI_CERT:
                /* return WAPI_CERT string */
                return context.getString(R.string.wifi_security_wapi_certificate);
            default:
        }
        return null;
    }
}
