package com.bbpos.wiseapp.tms.receiver;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import android.net.wifi.WifiManager;
import android.os.Handler;
import android.os.Looper;
import android.os.Message;
import android.telephony.PhoneStateListener;
import android.telephony.SignalStrength;
import android.telephony.TelephonyManager;
import android.util.Log;

import com.bbpos.wiseapp.logger.BBLog;
import com.bbpos.wiseapp.service.common.RequestPermission;
import com.bbpos.wiseapp.service.common.UsualData;
import com.bbpos.wiseapp.settings.utils.WirelessUtil;
import com.bbpos.wiseapp.tms.location.GpsLocationManager;
import com.bbpos.wiseapp.tms.utils.Constants;
import com.bbpos.wiseapp.tms.utils.ContextUtil;
import com.bbpos.wiseapp.tms.utils.Helpers;
import com.bbpos.wiseapp.utils.SharedPreferencesUtils;
import com.bbpos.wiseapp.websocket.WebSocketSender;

import static android.content.Context.WIFI_SERVICE;
import static com.bbpos.wiseapp.settings.utils.WirelessUtil.getCurConnectSSID;

public class NetworkStatusReceiver extends BroadcastReceiver {
    private static final String TAG = "NetworkStatus";
    private static Object lock = new Object();
    TelephonyManager mTelephonyManager = null;
    MyPhoneStateListener mListener = null;
    Handler mHandler = new Handler(Looper.getMainLooper()) {
        @Override
        public void handleMessage(Message msg) {
            super.handleMessage(msg);
            if (msg.what == 0) {
                new Thread(new Runnable() {
                    @Override
                    public void run() {
                        if (!"0".equals(Constants.UPLOAD_MODE)) {
                            WebSocketSender.C0904_NetworkStatusUpload();
                        }
                    }
                }).start();
            }
        }
    };

    public NetworkStatusReceiver(Context context) {
        mTelephonyManager = (TelephonyManager) context.getSystemService(Context.TELEPHONY_SERVICE);
        //开始监听
        mListener = new MyPhoneStateListener();
        //监听信号强度
        mTelephonyManager.listen(mListener, PhoneStateListener.LISTEN_SIGNAL_STRENGTHS);
    }

    @Override
    public void onReceive(Context context, Intent intent) {
        String action = intent.getAction();
        if (action.equals(WifiManager.RSSI_CHANGED_ACTION) || action.equals(WifiManager.NETWORK_STATE_CHANGED_ACTION)) {
//            BBLog.d(TAG, "NetworkStatusReceiver onReceive: wifi action: "+action );
            if (action.equals(WifiManager.RSSI_CHANGED_ACTION)) {
                if (Constants.M_GEOFENCE_STATUS == GpsLocationManager.OUT_OF_ZONE) {
                    if (GpsLocationManager.conditionEnterGeoFence()) {
                        BBLog.w(TAG, "WIFI IP = STORE_IP，judge as back to store，remove OUT_OF_FENCE status");
                        GpsLocationManager.closeOutOfGeofenceWarning();
                        GpsLocationManager.setCurrentGPSStatus(GpsLocationManager.IN_ZONE);
                        WebSocketSender.C0903_DataInfoUploadForGeo(System.currentTimeMillis());
                    }
                } else if (Constants.M_GEOFENCE_STATUS==GpsLocationManager.LOCK_SCREEN || Constants.M_GEOFENCE_STATUS==GpsLocationManager.WIPE_DATA) {
                    if (GpsLocationManager.conditionEnterGeoFence()) {
                        Intent intent_geo = new Intent(UsualData.ACTION_ENTER_GEOFENCE);
                        ContextUtil.getInstance().sendBroadcast(intent_geo, RequestPermission.REQUEST_PERMISSION_MY_BROADCAST);
                    }
                }
            }
            if (!isNetConnected(context)) {
				return;
			}
            synchronized (lock) {
                boolean isStrengThChange = false;
                boolean isSSIDChange = false;
                int curRssiValue = intent.getIntExtra(WifiManager.EXTRA_NEW_RSSI, 0);
                int curStrength = WifiManager.calculateSignalLevel(curRssiValue, 4);//value -1~0
                int valuePerLevel = 200/10;
                BBLog.i(BBLog.TAG, "C0904 curStrength="+curStrength+"  WIFI_SIGNAL_LEVEL = " + Constants.WIFI_SIGNAL_LEVEL);
                BBLog.i(BBLog.TAG, "C0904 平均值="+valuePerLevel+"  信号变化绝对值 = " + Math.abs(curRssiValue-Constants.WIFI_SIGNAL_VALUE));
                if (Constants.WIFI_SIGNAL_LEVEL!=curStrength && Math.abs(curRssiValue-Constants.WIFI_SIGNAL_VALUE)>=valuePerLevel){
                    BBLog.d(BBLog.TAG, "NetworkStatusReceiver WIFI信号强度发生变化" );
                    isStrengThChange = true;
					Constants.WIFI_SIGNAL_LEVEL = curStrength;
                    Constants.WIFI_SIGNAL_VALUE = curRssiValue;
                }

                String curSSID = getCurConnectSSID(context);
                if ("unKnow".equals(Constants.WIFI_SSID)) {
                    if ("".equals(curSSID)) {
                        BBLog.d(BBLog.TAG, "NetworkStatusReceiver onReceive:\"unKnow\"  getCurConnectSSID == “” ");
                        Constants.WIFI_SSID = "hidden_wifi";
                        isSSIDChange = true;
                    } else {
                        BBLog.d(BBLog.TAG, "NetworkStatusReceiver onReceive:\"unKnow\"  getCurConnectSSID not null : ");
						Constants.WIFI_SSID = curSSID;
                        isSSIDChange = false;
                    }
                } else if ("hideen_wifi".equals(Constants.WIFI_SSID)) {
                    BBLog.d(BBLog.TAG, "NetworkStatusReceiver onReceive: hidden_wifi ");
                    if ("".equals(curSSID)) {
						Constants.WIFI_SSID = "hiddent_wifi";
                        isSSIDChange = false;
                    } else {
						Constants.WIFI_SSID = curSSID;
                        isSSIDChange = true;
                    }
                } else {
                    if (!Constants.WIFI_SSID .equals(curSSID)) {
                        BBLog.d(BBLog.TAG, "NetworkStatusReceiver WIFI 名称发生变化");
						Constants.WIFI_SSID  = curSSID;
                        isSSIDChange = true;
                    }
                }

                if (isSSIDChange || isStrengThChange){
                    // TODO: 7/4/2019 上报wifi更变
					mHandler.sendEmptyMessage(0);
                }
            }
        }
    }

    private class MyPhoneStateListener extends PhoneStateListener {
        //获取信号强度
        @Override
        public void onSignalStrengthsChanged(SignalStrength signalStrength) {
            super.onSignalStrengthsChanged(signalStrength);
            //获取网络信号强度
            //获取0-4的5种信号级别，越大信号越好,但是api23开始才能用
            int level = 0;
            if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.M) {
                level = signalStrength.getLevel();
            }

            //获取网络类型
            int networkType = WirelessUtil.getMobileNetworkState(ContextUtil.getInstance());

            if (Constants.MOBILE_TYPE != networkType || (Constants.MOBILE_TYPE==networkType && Constants.MOBILE_SIGNAL_LEVEL!=level)) {
                BBLog.d(BBLog.TAG, "NetworkStatusReceiver 运营商网络 networkType=" + networkType + "  level=" + level);
//                BBLog.d(BBLog.TAG, "NetworkStatusReceiver 运营商网络 发生变化");
                Constants.MOBILE_SIGNAL_LEVEL = level;
                Constants.MOBILE_TYPE = networkType;
                mHandler.sendEmptyMessage(0);
            }
        }
    }

    public boolean isNetConnected(Context context) {
        ConnectivityManager cm = (ConnectivityManager) context.getSystemService(Context.CONNECTIVITY_SERVICE);
        WifiManager wm = (WifiManager) context.getApplicationContext().getSystemService(WIFI_SERVICE);
        NetworkInfo wifiNetworkInfo = cm.getNetworkInfo(ConnectivityManager.TYPE_WIFI);
        return wifiNetworkInfo.isConnected();
    }
}
