package com.bbpos.wiseapp.tms.utils;

public class ParameterName {
    public static final String myVersionName = "myVersionName";
    public static final String terminalDate = "terminalDate";

    public static final String appPackageName = "appPackageName";
	public static final String trafficNetType = "trafficNetType";
	public static final String trafficBytes = "trafficBytes";
	public static final String trafficDate = "trafficDate";
	public static final String trafficData = "trafficData";
    /**remote log service*/
    public static final String logList = "logList";
    public static final String logFile = "logFile";
    public static final String logSize = "logSize";
    public static final String isLog = "isLog";
    public static final String logResultState = "resultCode";
	// 详细数据
	public static final String appName = "appName";
	public static final String appVersion = "appVersion";
	public static final String appVersionName = "appVersionName";
	public static final String wifiTraffic = "wifiTraffic";
	public static final String mobileNetTraffic = "mobileNetTraffic";

	public static final String TYPE = "tranCode";
	public static final String JSON = "jsonParam";
	public static final String DATA = "data";
	public static final String VERSION = "version";
	public static final String DATA_TYPE = "dataType";
	public static final String APP_KEY = "appKey";
	public static final String request_id = "request_id";
	public static final String request_time = "request_time";
	/**终端信息*/
    public static final String POS = "pos";
	
	public static final String downloadFileName = "downloadFileName";
	public static final String downloadFileInfo = "downloadFileInfo";
	/**内部存储空间是否达到阈值*/
	public static final String isStorageOverflow = "isStorageOverflow";
	
	/**厂商编号*/
    public static final String facNo = "facName";
    /**终端型号*/
    public static final String modelNo = "modelName";
    /**终端序列号*/
    public static final String serialNo = "sn";
    public static final String serialNo_ = "serialNo";
    /**终端os版本*/
    public static final String OSVerNo = "OSVer";
    /**终端固件版本*/
    public static final String displayVer = "displayVer";
    /**终端Android版本号*/
    public static final String androidVersionNo = "androidVersion";
    /**由AP + SP组成的firmware version*/
    public static final String firmwareVersionNo = "firmwareVersion";
    /**终端tss登录信息/用户ID*/
    public static final String verifyCode = "verifyCode";
    public static final String returnCode = "state";
    public static final String stateDesc = "stateDesc";

    public static final String appList = "appList";
    public static final String serviceList = "serviceList";
    public static final String deleteApps = "deleteApps";
    public static final String deleteAppList = "deleteAppList";

    public static final String wifiList = "wifiList";

    //轮询返回-任务列表
    public static final String taskList = "taskList";
    public static final String taskJson = "taskJson";
    public static final String posTimestamp = "posTimestamp";
    /**任务分配ID*/
    public static final String taskId = "taskId";
    public static final String relativeId = "relativeId";
    /**任务分配时间戳*/
    public static final String taskTimestamp = "taskTimestamp";
    /**任务类型*/
    public static final String taskType = "taskType";
    /**任务执行结果-任务状态*/
    public static final String taskResult = "taskResult";
    /**执行时间*/
    public static final String beginDate = "beginDate";
    /**失效时间*/
    public static final String endDate = "endDate";
    /**更新时间*/
    public static final String modifyDate = "modifyDate";
    /**周期/时段**/
    public static final String period = "period";
    /**是否此任务是需要删除的 01删除/null 不删除*/
    public static final String delete = "delete";
    /**依赖的任务分配ID*/
    public static final String relyTaskId = "relyTaskId";
    /**应用程序的实际包名称*/
    public static final String appId = "appId";
    public static final String pkgName = "packName";
    public static final String apkName = "apkName";
    public static final String otaName = "otaName";
    public static final String otaId = "otaId";
    public static final String templateId = "templateId";
    public static final String templateName = "templateName";
    public static final String templateVersion = "templateVersion";

    /**更新包类型,01差分包,02全量包,03差分包*/
    public static final String diffPack = "diffPack";
    /**应用程序版本号*/
    public static final String versionCode = "versionCode";
    public static final String versionName = "versionName";
    /**当前应用MD5值,用作查找差分包*/
    public static final String baseApkMd5 = "baseApkMd5";
    public static final String upVersionCode = "upVersionCode";
    public static final String upVersionName = "upVersionName";
    public static final String posSignFlag = "posSignaType";
    public static final String sysInfoTer = "sysInfo";
    public static final String sytemInfo = "sytemInfo";
    public static final String serviceInfo = "serviceInfo";
    public static final String hardwareInfoTer = "hardwareInfo";
    public static final String apkInfoInTer = "apkInfo";
    public static final String locationInfoInTer = "locationInfo";
    public static final String batteryInfoTer = "batteryInfo";
    public static final String wifiOption = "wifiOption";
    public static final String wifiContivity = "wifiContivity";
    public static final String mobileContivity = "mobileContivity";
    public static final String baseSite = "baseSite";
    public static final String wifi = "wifi";
    public static final String cardDeviceUsage = "cardDeviceUsage";
    public static final String temprature = "temprature";
    /**应用更新日期*/
    public static final String updateDate = "updateDate";
    /**当前天总流量*/
    public static final String nowUseKb = "nowUseKb";
    /**当天使用次数*/
    public static final String nowUseTimes = "nowUseTimes";
    /**统计数据*/
    public static final String sumInfo = "sumInfo";
    /**统计日期*/
    public static final String sumDate = "sumDate";
    /**使用流量*/
    public static final String useKb = "useKb";
    /**使用次数*/
    public static final String useTimes = "useTimes";
    /**参数键*/
    public static final String key = "key";
    /**参数值*/
    public static final String value = "value";
    /**硬件类型名称 当为null时,为一级节点,否则为当前信息为二级节点 */
    public static final String typeNameHWInfo = "typeName";
    /**硬件名称，如打印机状态，安全检测模块*/
    public static final String nameHWInfo = "name";
    /**硬件信息描述，描述可能会有多条情况*/
    public static final String descHWInfo = "desc";
    /**硬件信息状态 1 正常 0 异常（如缺纸，报攻击）*/
    public static final String stateHWInfo = "state";
    public static final String errorMsg = "msg";
    public static final String result = "result";
    /**起始下载字节*/
    public static final String starPoint = "startPoint";
	/**实际终端备份日期,格式YYYY-MM-DD-hh-mm-dd*/
	public static final String backupDate = "backupDate";
	public static final String fileSize = "size";
	public static final String fileMd5 = "md5";
    public static final String otaMd5 = "otaMd5";
    /**下载url*/
    public static final String url = "url";
    public static final String fileUrl = "fileUrl";
    /**文件下载名称*/
    public static final String fileName = "fileName";
    /**文件下载完整路径*/
    public static final String filePath= "filePath";
	//tms定义参数 key值 TODO
    public static final String HTTP_URL_KEY = "00000001";//服务器连接url
    public static final String HTTP_PORT_KEY = "00000002";//服务器端口
    public static final String TER_INFO_UPLOAD_INTERVAL_KEY = "00000003";//终端信息上送时间间隔 单位：秒
	public static final String POLL_INTERVAL_KEY = "00000004";//轮询时间间隔 单位：秒
    public static final String HTTP_METHOD_TRANS_KEY = "00000005";//服务器连接url_交易
	public static final String HTTP_METHOD_UPLOAD_KEY = "00000006";//服务器连接url_日志上送
    public static final String HTTP_HEAVY_URL_KEY = "00000007";//服务器连接url
	public static final String STORAGE_REMAIN_MIN_KEY = "TMS_STORAGE_REMAIN_MIN"; // 终端剩余内部存储空间最小值，剩余空间小于该值则不进行与下载相关的任务

	//用户登陆
	public static final String accout = "accout";
	public static final String password = "password";

    /**现有程序包*/
    public static final String ProgFree = "ProgFree";
    public static final String DataFree = "DataFree";
    public static final String MemoryFree = "MemoryFree";
    public static final String PkgNum = "PkgNum";
    public static final String ParamNum = "ParamNum";
    public static final String ExistAppInfo = "ExistAppInfo";
    public static final String ConfigVer = "ConfigVer";
    public static final String EssentialNum = "EssentialNum";
    public static final String ConfigModule = "ConfigModule";
    /**ExistAppInfo*/
    public static final String FileName = "FileName";
    public static final String FileSize = "FileSize";
    public static final String ProgramSize = "ProgramSize";
    public static final String DataFileSize = "DataFileSize";
    public static final String MemoryFileSize = "MemoryFileSize";
    public static final String ExtendLen = "ExtendLen";
    public static final String FileType = "FileType";
    public static final String DisplayName = "DisplayName";
    public static final String Version = "Version";
    public static final String AreaName = "AreaName";
    public static final String ProcFlag = "ProcFlag";
    public static final String BakFlag = "BakFlag";
    public static final String Priority = "Priority";
    public static final String DefaultApp = "DefaultApp";
    /**ModuleName*/
    public static final String ModuleName = "ModuleName";
    /**----------------白名单时间戳及tms参数-----------------**/
    /**轮询间隔,单位：秒**/
    public static final String pollIntervalKey = "pollIntervalKey";
    /**信息上送时间间隔,单位：秒**/
    public static final String uploadIntervalKey = "uploadIntervalKey";
    /**TMS登录密码 md5**/
    public static final String loginPwdMd5 = "loginPwdMd5";
    public static final String HTTP_URL = "http_url";
    public static final String HTTP_HEAVY_URL = "http_heavy_url";

    //電池信息
    public static final String batLevel = "batteryLife";
    public static final String batScale = "batScale";
    public static final String isCharging = "isCharging";
    public static final String isLowBattery = "isLowBattery";
    public static final String unboxStatus = "unboxStatus";
    public static final String powerOff = "powerOff";
    public static final String outOfBattery = "outOfBattery";
    public static final String isInUse = "isInUse";
    public static final String usbCharge = "usbCharge";
    public static final String acCharge = "acCharge";
    public static final String batHealth = "batteryHealth";
	public static final String packName = "packName";
    public static final String appStatus = "appStatus";
    public static final String apkMd5 = "apkMd5";
    public static final String apkSize = "apkSize";

    public static final String apkInfoList = "apkInfoList";

    /**应用图标url*/
    public static final String appIconUrlEx = "appIconUrl";

    public static final String tranCode = "tranCode";
    public static final String data = "data";
    public static final String c_type = "c_type";
    public static final String param = "param";
    public static final String resultCode = "resultCode";
    public static final String resultMsg = "resultMsg";
    public static final String org_request_time = "org_request_time";
    public static final String org_request_id = "org_request_id";
    public static final String response_state = "response_state";
    public static final String response_remark = "response_remark";
    public static final String version = "version";
    public static final String buildNumber = "buildNumber";
    public static final String notice_msg = "notice_msg";
    public static final String wifi_info = "wifi_info";
    public static final String SSID = "SSID";
    public static final String wifi_pwd = "wifi_pwd";
    public static final String rebootTime = "rebootTime";
    public static final String deviceStatus = "deviceStatus";
    public static final String storeGPS = "storeGPS";

    public static final String gps = "gps";
    public static final String pairedBy = "pairedBy";
    public static final String longitude = "longitude";
    public static final String latitude = "latitude";
    public static final String distance = "distance";
    public static final String lockStatus = "lockStatus";
    public static final String geoList = "geoList";
    public static final String geoInfo = "geoInfo";
    public static final String proId = "proId";
    public static final String proName = "proName";
    public static final String lockMeter = "lockMeter";
    public static final String lockMin = "lockMin";
    public static final String wipeMin = "wipeMin";
    public static final String wipeStatus = "wipeStatus";
    public static final String disableTag = "disableTag";
    public static final String roamingTag = "roamingTag";

    public static final String storeList = "storeList";
    public static final String storeId = "storeId";
    public static final String storeNetAddr = "storeNetAddr";
    public static final String networkAddress = "networkAddress";
    public static final String confirmed = "confirmed";
    public static final String confirmMsg = "confirmMsg";

    public static final String fileSizeEx = "fileSize";
    public static final String fileMd5Ex = "fileMd5";
    public static final String fileKey = "fileKey";

    public static final String eventType = "eventType";
    public static final String eventTime = "eventTime";
    public static final String eventDesc = "eventDesc";
    public static final String formatDate = "formatDate";

    //service app
    public static final String command = "command";
    public static final String serviceName = "serviceName";

    //payment package list
    public static final String paymentList = "paymentList";

    //wifi profile
    public static final String ssid = "ssid";
    public static final String pwd = "password";
    public static final String securityType = "securityType";
    public static final String proxyType = "proxyType";
    public static final String proxyIp = "proxyIp";
    public static final String proxyPort = "proxyPort";
    public static final String byPassProxyFor = "byPassProxyFor";
    public static final String pacUrl = "pacUrl";
    public static final String order = "order";
    public static final String connectHidden = "connectHidden";
    public static final String isDefault = "isDefault";
    public static final String wifi_profile = "wifi_profile";
    public static final String wifi_lost_time = "wifi_lost_time";//wifi 连不上或ping不通时，记录时间，在未来24小时内不在尝试连接该wifi（重启后恢复）
    public static final String wifi_flag_cache = "wifi_flag_cache";//是否为上一次连接的ssid 标识

    //ota
    public static final String flag_need_ota = "flag_need_ota";
	public static final String type_for_ota = "type_for_ota";
    public static final String serviceId = "serviceId";
	public static final String TMT_URL = "tmt_url";

    public static final String idx = "idx";
    public static final String inAppVal = "inAppVal";
    public static final String inKey = "inKey";
    public static final String inValue = "inValue";
    public static final String dataSrc = "dataSrc";
    public static final String appPerformanceVal = "appPerformance";
    public static final String appInitSize = "appInitSize";
    public static final String appCacheSize = "appCacheSize";
    public static final String appDataSize = "appDataSize";
    public static final String appTotalSize = "appTotalSize";
    public static final String appRamUsageSize = "appRamUsageSize";
    public static final String ruleList = "ruleList";
    public static final String action = "action";
    public static final String ruleId = "ruleId";
    public static final String ruleStatus = "ruleStatus";
    public static final String ruleName = "ruleName";
    public static final String allowDowngrade = "allowDowngrade";
    public static final String delayTime = "delayTime";
    public static final String delayCount = "delayCount";
    public static final String restartAfter = "restartAfter";
    public static final String forceInstall = "forceInstall";
    public static final String installBy = "installBy";
    public static final String orgRuleId = "orgRuleId";
    public static final String msg = "msg";

    public static final String crashTime = "crashTime";
    public static final String crashCount = "crashCount";
    public static final String crashInfo = "crashInfo";

    public static final String silent_install = "silent_install";
    public static final String failedApkList = "failedApkList";
	public static final String expiration_time = "expiration_time";
    //device bid info
	public static final String firmware_info = "firmWareInfo";
	public static final String device_info = "deviceInfo";

	public static final String bid = "bID";
    public static final String uid = "uid";
    public static final String firmware_version = "firmwareVersion";
	public static final String terminal_setting_version = "terminalSettingVersion";
	public static final String device_setting_version = "deviceSettingVersion";
	public static final String battery_percentage = "batteryPercentage";

    public static final String imei_1 = "imei_1";
    public static final String imei_2 = "imei_2";
    public static final String wifi_mac = "wifi_mac";
    public static final String bt_mac = "bt_mac";
    public static final String bsn = "pcba_bsn";

	public static final String wisecube_bid = "wcbid";
	public static final String sp_bid = "spbid";
	public static final String spfw = "spfw";
	public static final String fw = "fw";
	public static final String conf = "conf";
	public static final String wc_battery = "wc_battery";

	public static final String isCallFromMDM = "isCallFromMDM";

    public static final String packageName = "packageName";
    public static final String paymentData = "paymentData";
    public static final String paymentDataMD5 = "paymentDataMD5";

    public static final String fileHash = "fileHash";

    public static final String apnList = "apnList";
    public static final String NAME = "name";
    public static final String APN = "apn";
    public static final String PROXY = "proxy";
    public static final String PORT = "port";
    public static final String MMSPROXY = "mms_proxy";
    public static final String MMSPORT = "mms_port";
    public static final String SERVER = "server";
    public static final String USER = "username";
    public static final String PASSWORD = "password";
    public static final String MMSC = "mmsc";
    public static final String MCC = "mcc";
    public static final String MNC = "mnc";
    public static final String NUMERIC = "numeric";//
    public static final String AUTH_TYPE = "auth_type";
    public static final String APN_TYPE = "type";
    public static final String PROTOCOL = "protocol";
    public static final String ROAMING_PROTOCOL = "roaming_protocol";
    public static final String CURRENT = "current";//
    public static final String CARRIER_ENABLED = "carrier_enabled";//
    public static final String BEARER = "bearer";//
    public static final String BEARER_BITMASK = "bearer_bitmask";//
    public static final String MVNO_TYPE = "mvno_type";
    public static final String MVNO_MATCH_DATA = "mvno_value";//mvno_match_data
    public static final String APN_PRIORITY = "order";// apn顺序，也即优先级
    public static final String APN_PROID = "proId";// apn profile id

}
