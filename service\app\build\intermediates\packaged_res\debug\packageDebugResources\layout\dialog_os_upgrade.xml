<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#80000000"
    android:gravity="center"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="400dp"
        android:layout_height="wrap_content"
        android:background="@drawable/dialog_background"
        android:orientation="vertical"
        android:padding="24dp">

        <!-- 标题 -->
        <TextView
            android:id="@+id/tv_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:text="系统升级提示"
            android:textColor="#333333"
            android:textSize="18sp"
            android:textStyle="bold" />

        <!-- 消息内容 -->
        <TextView
            android:id="@+id/tv_message"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:gravity="center"
            android:lineSpacingExtra="4dp"
            android:text="检测到新的系统版本，是否立即升级？"
            android:textColor="#666666"
            android:textSize="14sp" />

        <!-- 倒计时显示 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="20dp"
            android:gravity="center"
            android:orientation="horizontal">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="自动升级倒计时: "
                android:textColor="#999999"
                android:textSize="12sp" />

            <TextView
                android:id="@+id/tv_timeout"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="05:00"
                android:textColor="#FF6B35"
                android:textSize="14sp"
                android:textStyle="bold" />

        </LinearLayout>

        <!-- 按钮区域 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="24dp"
            android:orientation="horizontal">

            <!-- 延迟按钮 -->
            <TextView
                android:id="@+id/btn_delay"
                android:layout_width="0dp"
                android:layout_height="44dp"
                android:layout_marginEnd="8dp"
                android:layout_weight="1"
                android:background="@drawable/button_secondary"
                android:gravity="center"
                android:text="稍后升级"
                android:textColor="#666666"
                android:textSize="14sp" />

            <!-- 立即升级按钮 -->
            <TextView
                android:id="@+id/btn_install"
                android:layout_width="0dp"
                android:layout_height="44dp"
                android:layout_marginStart="8dp"
                android:layout_weight="1"
                android:background="@drawable/button_primary"
                android:gravity="center"
                android:text="立即升级"
                android:textColor="#FFFFFF"
                android:textSize="14sp" />

        </LinearLayout>

    </LinearLayout>

</LinearLayout>
