package com.dspread.mdm.service.services

import android.app.IntentService
import android.content.Context
import android.content.Intent
import com.dspread.mdm.service.constants.Constants
import com.dspread.mdm.service.modules.provisioning.ProvisioningManager
import com.dspread.mdm.service.modules.provisioning.ProvisioningTrigger
import com.dspread.mdm.service.modules.provisioning.model.ProvisioningTrigger as ProvisioningTriggerType
import com.dspread.mdm.service.modules.provisioning.model.ProvisioningResult
import com.dspread.mdm.service.platform.manager.WakeLockManager
import com.dspread.mdm.service.utils.log.Logger

/**
 * Provisioning服务
 * 继承IntentService，在后台线程中异步处理Provisioning任务
 *
 * 注意：IntentService已废弃，但在前台服务中使用仍然安全有效
 * 未来可考虑升级为JobIntentService或WorkManager
 */
@Suppress("DEPRECATION")
class ProvisioningService : IntentService("ProvisioningService") {

    companion object {
        private const val TAG = "[ProvisioningService] "
        
        private const val ACTION_EXECUTE_PROVISIONING = "com.dspread.mdm.service.EXECUTE_PROVISIONING"
        private const val ACTION_INITIALIZE_DEFAULT = "com.dspread.mdm.service.INITIALIZE_DEFAULT"
        
        private const val EXTRA_CONFIG_URL = "config_url"
        private const val EXTRA_TRIGGER_TYPE = "trigger_type"
        
        /**
         * 启动Provisioning执行
         */
        fun startProvisioning(context: Context, configUrl: String, triggerType: ProvisioningTriggerType) {
            val intent = Intent(context, ProvisioningService::class.java).apply {
                action = ACTION_EXECUTE_PROVISIONING
                putExtra(EXTRA_CONFIG_URL, configUrl)
                putExtra(EXTRA_TRIGGER_TYPE, triggerType.name)
            }
            context.startService(intent)
        }
        
        /**
         * 初始化默认配置
         */
        fun initializeDefaultConfig(context: Context) {
            val intent = Intent(context, ProvisioningService::class.java).apply {
                action = ACTION_INITIALIZE_DEFAULT
            }
            context.startService(intent)
        }
    }

    override fun onCreate() {
        super.onCreate()
        Logger.provI("服务创建")
        
        // 获取WakeLock，防止服务被杀死
        WakeLockManager.acquireSmartWakeLock(this, Constants.ServiceConstants.WAKELOCK_INITIAL_TIMEOUT)
    }

    override fun onHandleIntent(intent: Intent?) {
        if (intent == null) {
            Logger.provE("Intent为空")
            return
        }

        try {
            when (intent.action) {
                ACTION_EXECUTE_PROVISIONING -> {
                    handleExecuteProvisioning(intent)
                }
                ACTION_INITIALIZE_DEFAULT -> {
                    handleInitializeDefault()
                }
                else -> {
                    Logger.provE("未知的Action: ${intent.action}")
                }
            }
        } catch (e: Exception) {
            Logger.provE("处理Intent失败", e)
        }
    }

    /**
     * 处理Provisioning执行
     */
    private fun handleExecuteProvisioning(intent: Intent) {
        val configUrl = intent.getStringExtra(EXTRA_CONFIG_URL) ?: Constants.ServiceConstants.DEFAULT_PROVISIONING_CONFIG_URL
        val triggerTypeName = intent.getStringExtra(EXTRA_TRIGGER_TYPE) ?: ProvisioningTriggerType.MANUAL.name
        val triggerType = try {
            ProvisioningTriggerType.valueOf(triggerTypeName)
        } catch (e: Exception) {
            ProvisioningTriggerType.MANUAL
        }

        Logger.provI("开始执行Provisioning: $configUrl, 触发类型: $triggerType")

        val provisioningManager = ProvisioningManager.getInstance(this)
        
        // 在后台线程中异步执行Provisioning
        provisioningManager.executeProvisioning(
            configUrl = configUrl,
            trigger = triggerType,
            callback = object : ProvisioningManager.ProvisioningCallback {
                override fun onProgress(result: ProvisioningResult) {
                    Logger.prov("Provisioning进度: ${result.message}")
                }

                override fun onCompleted(result: ProvisioningResult) {
                    Logger.provI("Provisioning完成: ${result.message}")

                    // 如果是首次启动完成，标记初始启动完成
                    if (triggerType == ProvisioningTriggerType.FIRST_BOOT) {
                        try {
                            val provisioningTrigger = ProvisioningTrigger.getInstance(this@ProvisioningService)
                            provisioningTrigger.markInitialStartupCompleted()
                        } catch (e: Exception) {
                            Logger.provE("标记初始启动完成失败", e)
                        }
                    }
                }

                override fun onError(result: ProvisioningResult) {
                    Logger.provE("Provisioning失败: ${result.message}")
                }
            }
        )
    }

    /**
     * 处理默认配置初始化
     */
    private fun handleInitializeDefault() {
        Logger.prov("初始化默认配置")

        val provisioningManager = ProvisioningManager.getInstance(this)
        val success = provisioningManager.initializeDefaultConfig()

        if (success) {
            Logger.provI("默认配置初始化成功")
        } else {
            Logger.provE("默认配置初始化失败")
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        Logger.provI("服务销毁")
        
        // 释放WakeLock
        WakeLockManager.releaseWakeLock(TAG)
    }
}
