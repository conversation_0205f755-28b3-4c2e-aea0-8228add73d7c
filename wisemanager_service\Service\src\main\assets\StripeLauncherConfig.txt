{"data": {"customization": {"apkBlacklist": [{"package_name": "com.bbpos.wiseapp.service", "class_name": "com.bbpos.wiseapp.settings.activity.SettingActivity", "status": "invisible"}, {"package_name": "com.bbpos.wiseapp.service", "class_name": "com.mining.app.zxing.activity.MipcaActivityCapture", "status": "invisible"}, {"package_name": "com.android.settings", "class_name": "com.android.settings.Settings", "status": "invisible"}, {"package_name": "com.bbpos.wiseapp.launcher", "class_name": "com.bbpos.wiseapp.launcher.ResetActivity", "status": "invisible"}, {"package_name": "com.stripe.updater", "class_name": "*", "status": "invisible"}, {"package_name": "com.bbpos.tmt.gds", "class_name": "*", "status": "invisible"}, {"package_name": "com.example.firmware", "class_name": "*", "status": "invisible"}, {"package_name": "org.codeaurora.gallery", "class_name": "*", "status": "invisible"}, {"package_name": "com.bbpos.bbdevice001.ui13", "class_name": "*", "status": "invisible"}, {"package_name": "com.bbpos.wiseposproductiontestdemo_1_0_18", "class_name": "*", "status": "invisible"}, {"package_name": "com.bbpos.wiseposproductiontestdemo_1_0_19", "class_name": "*", "status": "invisible"}, {"package_name": "com.bbpos.bbdevice.wiseposexample", "class_name": "*", "status": "invisible"}, {"package_name": "com.android.calendar", "class_name": "*", "status": "invisible"}, {"package_name": "com.android.deskclock", "class_name": "*", "status": "invisible"}, {"package_name": "com.android.calculator2", "class_name": "*", "status": "invisible"}, {"package_name": "com.android.quicksearchbox", "class_name": "*", "status": "invisible"}, {"package_name": "com.android.browser", "class_name": "*", "status": "invisible"}, {"package_name": "com.android.email", "class_name": "*", "status": "invisible"}, {"package_name": "com.android.mms", "class_name": "*", "status": "invisible"}, {"package_name": "com.android.music", "class_name": "*", "status": "invisible"}, {"package_name": "com.android.documentsui", "class_name": "*", "status": "invisible"}, {"package_name": "com.android.fmradio", "class_name": "*", "status": "invisible"}, {"package_name": "com.android.stk", "class_name": "*", "status": "invisible"}, {"package_name": "com.mediatek.camera", "class_name": "*", "status": "invisible"}, {"package_name": "com.mediatek.filemanager", "class_name": "*", "status": "invisible"}, {"package_name": "org.fdroid.fdroid", "class_name": "*", "status": "invisible"}, {"package_name": "com.farproc.wifi.analyzer", "class_name": "*", "status": "invisible"}, {"package_name": "com.nextdoordeveloper.miperf.miperf", "class_name": "*", "status": "invisible"}, {"package_name": "me.scan.android.client", "class_name": "*", "status": "invisible"}, {"package_name": "com.k_phone.mmitest", "class_name": "*", "status": "invisible"}, {"package_name": "com.android.inputmethod.latin", "class_name": "*", "status": "invisible"}, {"package_name": "com.bbpos.wiseapp", "class_name": "*", "status": "invisible"}, {"package_name": "com.google.android.gms", "class_name": "*", "status": "invisible"}, {"package_name": "com.android.vending", "class_name": "*", "status": "visible"}, {"package_name": "com.cyanogenmod.filemanager", "class_name": "*", "status": "invisible"}, {"package_name": "org.codeaurora.snapcam", "class_name": "*", "status": "invisible"}, {"package_name": "com.android.soundrecorder", "class_name": "*", "status": "invisible"}, {"package_name": "cn.digirun.update.smart", "class_name": "*", "status": "invisible"}, {"package_name": "com.kfree.softdecoding", "class_name": "*", "status": "invisible"}, {"package_name": "com.kphone.snwriter", "class_name": "*", "status": "invisible"}, {"package_name": "com.bbpos.wisepos.tool", "class_name": "*", "status": "invisible"}, {"package_name": "com.bbpos.bbdevice.wsp7xexample", "class_name": "*", "status": "invisible"}, {"package_name": "com.bbpos.barcodescanner2", "class_name": "*", "status": "invisible"}, {"package_name": "com.photo.android.camera", "class_name": "com.android.camera.CameraLauncher", "status": "invisible"}, {"package_name": "com.UCMobile.intl", "class_name": "com.UCMobile.main.UCMobile", "status": "invisible"}, {"package_name": "com.bbpos.wiseapp.tmt", "class_name": "*", "status": "invisible"}]}, "client": "stripe", "cid": "SZZZ"}, "function": "getConfigurationInformation", "stateCode": "0", "stateDescription": "Get configure infomation success!", "version": "v1.0.6.20200824"}