package com.bbpos.wiseapp.tms.utils;

public class BroadcastActions {
	public static final String BOOT_COMPLETED_BC="com.bbpos.wiseapp.service.BOOT_COMPLETED";
	/**启动轮询广播*/
	public static final String POLL_TIMER_START_BC="com.bbpos.wiseapp.tms.POLL_TIMER_START_BC";
	/**终端信息上送广播*/
	public static final String TER_INFO_UPLOAD_BC="com.bbpos.wiseapp.tms.TER_INFO_UPLOAD_BC";
	/**终端信息上送广播*/
	public static final String HARDWARE_DETECT_UPLOAD_BC="com.bbpos.wiseapp.tms.HARDWARE_DETECT_UPLOAD_BC";
	/**终端信息上送广播*/
	public static final String WISE_LOG_UPLOAD_BC="com.bbpos.wiseapp.tms.WISE_LOG_UPLOAD_BC";
	/**启动初始化广播*/
	public static final String PROVISION_TIMER_START_BC="com.bbpos.wiseapp.tms.PROVISION_TIMER_START_BC";
	/**启动初始化广播*/
	public static final String GPS_SCHEDULE_TIME="com.bbpos.wiseapp.tms.GPS_SCHEDULE_TIME";
	/**启动初始化广播*/
	public static final String LOGCAT_STREAM_TIME="com.bbpos.wiseapp.tms.LOGCAT_STREAM_TIME";

	/**TMS参数更新完成--*/
	public static final String PARAM_UPDATE_COMPLETE_BC = "com.bbpos.wiseapp.tms.PARAM_UPDATE_COMPLETE_BC";

	/**BLE SCAN定时器--*/
	public static final String BLE_SCAN_TIMER_START_BC = "com.bbpos.wiseapp.tms.BLE_SCAN_TIMER_START_BC";

	/**--------------待定--------**/
	/**锁屏接收成功广播*/
	public static final String TMS_LOCK_SUCCESS_BROAD = "com.bbpos.wiseapp.tms.BC.TMS_LOCK_SUCCESS_BROAD";

	/**通知应用市场更新剩余存储空间最小值更新广播*/
	public static final String STORAGE_REMAIN_CHANGE_BC = "com.ums.tms.STORAGE_REMAIN_CHANGE_BC";

	/**不可卸载应用列表广播---仅发送不接收*/
	public static final String UNINSTALL_APK_LIST_BC="com.ums.tms.UNINSTALL_APK_LIST_BC";

	//---------------------------------------
	public static final String WIFI_FOUND = "com.bbpos.intent.WIFI_FOUND";
	public static final String STORE_FOUND = "com.bbpos.intent.STORE_FOUND";
	public static final String LOGIN_SUCCESS = "com.bbpos.intent.LOGIN_SUCCESS";
	public static final String LOGIN_FAILED = "com.bbpos.intent.LOGIN_FAILED";
	public static final String INIT_READY = "com.bbpos.intent.INIT_READY";
	public static final String BEGIN_DOWNLOAD = "com.bbpos.intent.BEGIN_DOWNLOAD";
	public static final String APP_DOWNLOADED = "com.bbpos.intent.APP_DOWNLOADED";
	public static final String DOWNLOAD_FINISHED = "com.bbpos.intent.DOWNLOAD_FINISHED";
	public static final String STORE_NOT_FOUND = "com.bbpos.intent.STORE_NOT_FOUND";
	public static final String APP_DOWNLOAD_FAIL = "com.bbpos.intent.APP_DOWNLOAD_FAIL";
	public static final String STORE_SELECTED = "com.bbpos.intent.STORE_SELECTED";
	public static final String STORE_LOOKUP = "com.bbpos.intent.STORE_LOOKUP";
	public static final String RESTART_DEVICE = "com.bbpos.intent.RESTART_DEVICE";
	public static final String UNBIND_LAUNCHER = "com.bbpos.intent.UNBIND_LAUNCHER";
	public static final String UNBIND_COMPLETE = "com.bbpos.intent.UNBIND_COMPLETE";
	public static final String DELETE_TEST_WIFI = "com.bbpos.intent.DELETE_TEST_WIFI";
	public static final String SET_DEFAULT_LAUNCHER = "com.bbpos.intent.SET_DEFAULT_LAUNCHER";
	public static final String APP_REFRESH = "com.bbpos.intent.APP_REFRESH";
	public static final String TEMP_WARNING = "com.bbpos.intent.TEMP_WARNING";
	public static final String SCAN_CODE = "com.bbpos.wiseapp.wisescan.SCAN_CODE";
	public static final String RULEBASED_DOWNLOAD_COMPLETED = "com.bbpos.wiseapp.service.RULEBASED_DOWNLOAD_COMPLETED";
	public static final String APP_PLUS_DOWNLOAD_COMPLETED = "com.bbpos.wiseapp.service.APP_PLUS_DOWNLOAD_COMPLETED";
	public static final String ACTION_EXPIRE_REBOOT = "com.bbpos.wiseapp.service.ACTION_EXPIRE_REBOOT";
	public static final String ACTION_GEOFENCING_DETECTED_WARNING = "com.bbpos.wiseapp.service.ACTION_GEOFENCING_DETECTED_WARNING";
	public static final String ACTION_GEOFENCING_DETECTED_WARNING_CLOSE = "com.bbpos.wiseapp.service.ACTION_GEOFENCING_DETECTED_WARNING_CLOSE";
	public static final String ACTION_GEOFENCING_DETECTED_LOCK = "com.bbpos.wiseapp.service.ACTION_GEOFENCING_DETECTED_LOCK";
	public static final String ACTION_GEOFENCING_RESET_LOCKSCREEN_TIMER = "com.bbpos.wiseapp.service.ACTION_GEOFENCING_RESET_LOCKSCREEN_TIMER";

	//FOR 711 Launcher
	public static final String CLOSE_WIFI = "com.bbpos.intent.CLOSE_WIFI";
	public static final String OPEN_WIFI = "com.bbpos.intent.OPEN_WIFI";
	public static final String GET_WIFI_STATE = "com.bbpos.intent.GET_WIFI_STATE";
	public static final String WIFI_STATE = "com.bbpos.intent.WIFI_STATE";
}
