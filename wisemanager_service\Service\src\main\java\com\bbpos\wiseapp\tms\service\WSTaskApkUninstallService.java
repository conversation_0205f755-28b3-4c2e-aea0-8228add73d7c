package com.bbpos.wiseapp.tms.service;

import android.content.Intent;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.util.Log;

import com.bbpos.wiseapp.logger.BBLog;
import com.bbpos.wiseapp.service.WakeLockService;
import com.bbpos.wiseapp.service.common.UsualData;
import com.bbpos.wiseapp.system.api.SysIntermediateApi;
import com.bbpos.wiseapp.tms.adapter.SystemManagerAdapter;
import com.bbpos.wiseapp.tms.utils.Constants;
import com.bbpos.wiseapp.tms.utils.ContextUtil;
import com.bbpos.wiseapp.tms.utils.Helpers;
import com.bbpos.wiseapp.tms.utils.ParameterName;
import com.bbpos.wiseapp.tms.utils.TaskState;
import com.bbpos.wiseapp.tms.utils.WebSocketTaskListManager;
import com.bbpos.wiseapp.utils.ActivityUtils;

import java.util.Iterator;
import java.util.List;

public class WSTaskApkUninstallService extends WakeLockService {
	private static final String TAG = "AppPlusUninstall";
	public WSTaskApkUninstallService() {
		super("WSTaskApkUninstallService");
	}

	@Override
	public void onDestroy() {
		super.onDestroy();
		BBLog.w(BBLog.TAG, "WSTaskApkUninstallService 結束 發送 WSTASK_EXEC_BC");
		Helpers.sendBroad(WSTaskApkUninstallService.this, UsualData.WSTASK_EXEC_BC);
	}
	@Override
	protected void onHandleIntent(Intent intent) {
		String pkgName = intent.getExtras().getString(ParameterName.pkgName);
		final String taskId = intent.getExtras().getString(ParameterName.taskId);
		WebSocketTaskListManager.updateWSTaskState(taskId, TaskState.UNINSTALL_ING);
		uninstall(taskId,pkgName);
	}

	private void uninstall(final String taskId, final String pkgName) {
		if (!isAppInstalled(pkgName)){
			BBLog.i(TAG, pkgName+" do not exist.");
			Helpers.updateWSTaskStateAndUpload(getApplicationContext(), taskId, TaskState.UNINSTALL_SUCCESS, null);
			return;
		}
		
		SystemManagerAdapter.unInstallApk(getApplicationContext(), pkgName, new SystemManagerAdapter.ApkUnInstallCompleted() {
			@Override
			public void onDeleteFinished(int returnCode) {
				BBLog.i(TAG, "packageDeleted " + pkgName + " returnCode " + returnCode);
	            if (returnCode == 1) {
					Helpers.updateWSTaskStateAndUpload(getApplicationContext(), taskId, TaskState.UNINSTALL_SUCCESS, null);
				} else {
					Helpers.updateWSTaskStateAndUpload(getApplicationContext(), taskId, TaskState.UNINSTALL_FAILED, null);
				}
			}
		});
	}

    /**
     * 判断应用是否安装 
     * @param pkgName 包名
     * */
    @SuppressWarnings("rawtypes")
	private boolean isAppInstalled(String pkgName){
    	PackageManager pm = this.getPackageManager();
		List installedPackages = pm.getInstalledPackages(0);
		Iterator localIterator = installedPackages.iterator();
		while (localIterator.hasNext()) {
			PackageInfo packageInfo = (PackageInfo) localIterator.next();
			if (packageInfo.applicationInfo.packageName.equals(pkgName)) {
				return true;
			}
		}
		return false;
    }
}
