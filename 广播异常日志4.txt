2025-08-21 18:21:22.218   947-998   PackageManager          system_server                        E  Adding duplicate shared id: 1000 name=com.dspread.mdm.service
2025-08-21 18:21:23.026  4750-4750  ead.mdm.service         com.dspread.mdm.service              W  type=1400 audit(0.0:540): avc: denied { write } for name="com.dspread.mdm.service-cFDFJive_9u81Iw8mdEKHQ==" dev="dm-6" ino=15594 scontext=u:r:system_app:s0 tcontext=u:object_r:apk_data_file:s0 tclass=dir permissive=0
2025-08-21 18:21:23.713  4750-4750  NetworkSecurityConfig   com.dspread.mdm.service              D  Using Network Security Config from resource network_security_config debugBuild: true
2025-08-21 18:21:23.717  4750-4750  NetworkSecurityConfig   com.dspread.mdm.service              D  Using Network Security Config from resource network_security_config debugBuild: true
2025-08-21 18:21:23.807  4750-4750  Common                  com.dspread.mdm.service              D  🔧 日志配置：测试环境模式
2025-08-21 18:21:23.813  4750-4750  Common                  com.dspread.mdm.service              D  🔧 [DebugConfig] 调试模式已启用
2025-08-21 18:21:23.815  4750-4750  Common                  com.dspread.mdm.service              D  🔧 SmartMdmServiceApp: 统一配置管理器初始化完成
2025-08-21 18:21:23.819  4750-4750  Common                  com.dspread.mdm.service              D  🔧 SmartMdmServiceApp: 系统目录初始化完成 - /sdcard/Android/data/com.dspread.mdm.service/files/config/
2025-08-21 18:21:23.862  4750-4750  Receiver                com.dspread.mdm.service              D  🔧 ServiceGuardEventHandler 启动服务守护定时器: 120s
2025-08-21 18:21:23.869  4750-4750  Common                  com.dspread.mdm.service              I  ✅ ⏰ 设置服务守护定时器成功，下次执行: 120秒后 (2分钟)
2025-08-21 18:21:23.871  4750-4750  Common                  com.dspread.mdm.service              D  🔧 SmartMdmServiceApp: 服务守护定时器启动成功
2025-08-21 18:21:23.877  4750-4750  Common                  com.dspread.mdm.service              D  🔧 ServiceStartupManager 收到服务启动请求: APPLICATION_STARTUP, 强制启动: false
2025-08-21 18:21:23.883  4750-4750  Common                  com.dspread.mdm.service              D  🔧 ServiceStartupManager 开始执行服务启动: APPLICATION_STARTUP
2025-08-21 18:21:23.886  4750-4750  Common                  com.dspread.mdm.service              D  🔧 ServiceManager: 准备启动后台服务
2025-08-21 18:21:23.889  4750-4750  ContextImpl             com.dspread.mdm.service              W  Calling a method in the system process without a qualified user: android.app.ContextImpl.startService:1669 android.content.ContextWrapper.startService:720 com.dspread.mdm.service.platform.manager.ServiceManager.startBackgroundService:20 com.dspread.mdm.service.platform.manager.ServiceStartupManager.performServiceStart:121 com.dspread.mdm.service.platform.manager.ServiceStartupManager.startService:66 
2025-08-21 18:21:23.893  4750-4750  Common                  com.dspread.mdm.service              D  🔧 ServiceManager: 后台服务启动成功，ComponentName: ComponentInfo{com.dspread.mdm.service/com.dspread.mdm.service.services.SmartMdmBackgroundService}
2025-08-21 18:21:24.907  4750-4750  Common                  com.dspread.mdm.service              D  🔧 ServiceStartupManager 服务启动验证成功: APPLICATION_STARTUP
2025-08-21 18:21:24.910  4750-4750  Common                  com.dspread.mdm.service              D  🔧 ServiceStartupManager 服务启动成功: APPLICATION_STARTUP
2025-08-21 18:21:24.912  4750-4750  Common                  com.dspread.mdm.service              D  🔧 SmartMdmServiceApp: 主服务启动请求已提交
2025-08-21 18:21:24.914  4750-4750  Common                  com.dspread.mdm.service              D  🔧 SmartMdmServiceApp: Application启动完成，包名: com.dspread.mdm.service
2025-08-21 18:21:25.046  4750-4750  SurfaceFactory          com.dspread.mdm.service              I  [static] sSurfaceFactory = com.mediatek.view.impl.SurfaceFactoryImpl@b98077b
2025-08-21 18:21:25.056  4750-4750  ViewRootIm...tActivity] com.dspread.mdm.service              D  hardware acceleration = true , fakeHwAccelerated = false, sRendererDisabled = false, forceHwAccelerated = false, sSystemRendererDisabled = false
2025-08-21 18:21:25.064  4750-4750  InputTransport          com.dspread.mdm.service              I  Create ARC handle: 0x9e0ce130
2025-08-21 18:21:25.065  4750-4750  PhoneWindow             com.dspread.mdm.service              V  DecorView setVisiblity: visibility = 0, Parent = android.view.ViewRootImpl@e15a057, this = DecorView@59f744[TestActivity]
2025-08-21 18:21:25.072  4750-4750  PhoneWindow             com.dspread.mdm.service              V  DecorView setVisiblity: visibility = 4, Parent = android.view.ViewRootImpl@e15a057, this = DecorView@59f744[TestActivity]
2025-08-21 18:21:25.073  4750-4750  Choreographer           com.dspread.mdm.service              I  Skipped 77 frames!  The application may be doing too much work on its main thread.
2025-08-21 18:21:25.109  4750-4750  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 服务启动
2025-08-21 18:21:25.121  4750-4750  setContentIntent        com.dspread.mdm.service              D  packageName = com.dspread.mdm.service
2025-08-21 18:21:25.127  4750-4750  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 开始服务初始化
2025-08-21 18:21:25.127   947-975   NotificationService     system_server                        E  enqueueNotificationInternal pkg: com.dspread.mdm.service
2025-08-21 18:21:25.130  4750-4750  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] Android < 14: 启动Provisioning服务
2025-08-21 18:21:25.134  4750-4750  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 服务创建完成
2025-08-21 18:21:25.134  4750-4781  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 启动ProvisioningService
2025-08-21 18:21:25.138  4750-4750  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] onStartCommand调用
2025-08-21 18:21:25.144  4750-4781  Provisioning            com.dspread.mdm.service              I  ℹ️ ProvisioningManager初始化完成
2025-08-21 18:21:25.147  4750-4750  Platform                com.dspread.mdm.service              D  🔧 WakeLockManager 获取智能WakeLock成功，持续时间: 60000ms
2025-08-21 18:21:25.147  4750-4781  Provisioning            com.dspread.mdm.service              D  🔧 状态标志文件不存在，使用默认值
2025-08-21 18:21:25.152  4750-4750  Platform                com.dspread.mdm.service              D  🔧 WakeLockManager 设置WakeLock续期定时器: 60000ms
2025-08-21 18:21:25.163  4750-4781  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] ProvisioningService启动完成
2025-08-21 18:21:25.164  4750-4782  Provisioning            com.dspread.mdm.service              I  ℹ️ 开始执行Provisioning配置，触发类型: FIRST_BOOT
2025-08-21 18:21:25.166  4750-4782  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] Provisioning进度: 正在下载配置文件...
2025-08-21 18:21:25.167  4750-4750  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 初始化基础组件
2025-08-21 18:21:25.168  4750-4782  Provisioning            com.dspread.mdm.service              I  ℹ️ 开始请求远程配置...
2025-08-21 18:21:25.181  4750-4782  Provisioning            com.dspread.mdm.service              I  ℹ️ 请求配置: https://config.dspreadserv.net/status/config?SN=MDE2MTAwNDAyMDIzMDcwNjAyMjc=&timestamp=1755771685180
2025-08-21 18:21:25.191  4750-4782  System.out              com.dspread.mdm.service              I  [okhttp]:check permission begin!
2025-08-21 18:21:25.192  4750-4782  System                  com.dspread.mdm.service              W  ClassLoader referenced unknown path: system/framework/mediatek-cta.jar
2025-08-21 18:21:25.194  4750-4782  System.out              com.dspread.mdm.service              I  [okhttp] e:java.lang.ClassNotFoundException: com.mediatek.cta.CtaUtils
2025-08-21 18:21:25.208  4750-4750  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 服务初始化完成
2025-08-21 18:21:25.266  4750-4784  Platform                com.dspread.mdm.service              D  🔧 DeviceInfoApi 单例实例已创建
2025-08-21 18:21:25.287  4750-4784  Platform                com.dspread.mdm.service              W  ⚠️ SpVersionApi SP版本信息格式异常，未找到SP_VERSION标记
2025-08-21 18:21:25.299  4750-4784  Platform                com.dspread.mdm.service              D  🔧 SpVersionApi 从Settings获取SP版本: STD 582.1.238.8
2025-08-21 18:21:25.334  4750-4750  WebSocket               com.dspread.mdm.service              I  🔧 C0905 主动式上送: manual_trigger (主动: 1)
2025-08-21 18:21:25.347  4750-4750  WebSocket               com.dspread.mdm.service              I  🔧 首次上送，上送当日数据
2025-08-21 18:21:25.350  4750-4750  WebSocket               com.dspread.mdm.service              I  🔧 没有流量数据需要上送
2025-08-21 18:21:25.352  4750-4750  Common                  com.dspread.mdm.service              D  🔧 刷新时自动上送流量统计
2025-08-21 18:21:25.615  4750-4782  System.out              com.dspread.mdm.service              I  [socket]:check permission begin!
2025-08-21 18:21:25.616  4750-4782  System                  com.dspread.mdm.service              W  ClassLoader referenced unknown path: system/framework/mediatek-cta.jar
2025-08-21 18:21:25.617  4750-4782  System.out              com.dspread.mdm.service              I  [socket] e:java.lang.ClassNotFoundException: com.mediatek.cta.CtaUtils
2025-08-21 18:21:26.719  4750-4782  System.out              com.dspread.mdm.service              I  [OkHttp] sendRequest>>
2025-08-21 18:21:26.720  4750-4782  System.out              com.dspread.mdm.service              I  [OkHttp] sendRequest<<
2025-08-21 18:21:27.872  4750-4782  Provisioning            com.dspread.mdm.service              I  ℹ️ 远程配置获取成功
2025-08-21 18:21:27.875  4750-4782  Provisioning            com.dspread.mdm.service              I  ℹ️ 远程配置请求成功
2025-08-21 18:21:27.877  4750-4782  Provisioning            com.dspread.mdm.service              I  ℹ️ 远程配置解析成功 - CID: 1001
2025-08-21 18:21:27.880  4750-4782  Provisioning            com.dspread.mdm.service              I  ℹ️ 创建配置目录成功: /sdcard/Android/data/com.dspread.mdm.service/files/config
2025-08-21 18:21:27.890  4750-4782  Provisioning            com.dspread.mdm.service              I  ℹ️ API响应已保存到本地: /sdcard/Android/data/com.dspread.mdm.service/files/config/provisioning_config.json, 具体内容为:
                                                                                                    {"mode":"dev","data":{"requestTime":"1755771688833","customization":{"system":{"timezone":"Asia\/Hong_Kong","logo":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/test\/logo.bin","logoMd5":"ba1ee533924eae5c408465e7cddcbda4","bootAnimationMd5":"ba1ee533924eae5c408465e7cddcbda4","gps":{"minUpdateTime":"30","scheduleTime":"60","maxLocateTime":"0","minDistance":"10","valid_distance":"500","care":"1"},"powerSaveMode":{"enable":"1","screenTimeout":"0"},"bootAnimation":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/test\/bootanimation.zip"},"polling":{"heartbeatTime":"300","terminalInfoTime":"900","statusApiUrl":"wss:\/\/api.dspreadserv.net\/status\/websocket\/register","remoteUrl":"wss:\/\/remote.dspreadserv.net\/remoteWSS\/websockify","uploadMode":"1","wssreconn":{"pingInterval":"30","delayPolicy":"1","delaySwitch":"1","delayTime":"60"}}},"client":"default","cid":"1001"},"function":"getProvisioningConfig","description":"SUCCESS","stateCode":"0","version":"v1.0.1.20250801"}
2025-08-21 18:21:27.893  4750-4782  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] Provisioning进度: 正在下载 Logo...
2025-08-21 18:21:27.897  4750-4782  Provisioning            com.dspread.mdm.service              I  ℹ️ 文件不存在，开始下载: Logo
2025-08-21 18:21:27.899  4750-4782  Provisioning            com.dspread.mdm.service              I  ℹ️ 下载: https://siot-bucket01.s3.ap-northeast-1.amazonaws.com/test/logo.bin -> /sdcard/Android/data/com.dspread.mdm.service/files/config/logo.bin
2025-08-21 18:21:27.907  4750-4782  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 尝试下载，第1次
2025-08-21 18:21:27.910  4750-4782  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 开始下载
2025-08-21 18:21:27.912  4750-4782  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader URL: https://siot-bucket01.s3.ap-northeast-1.amazonaws.com/test/logo.bin
2025-08-21 18:21:27.914  4750-4782  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 路径: /sdcard/Android/data/com.dspread.mdm.service/files/config/logo.bin
2025-08-21 18:21:27.916  4750-4782  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 本地文件不存在，创建文件
2025-08-21 18:21:27.919  4750-4782  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader allFileLength=0, 需要从服务器获取文件大小
2025-08-21 18:21:27.924  4750-4782  System.out              com.dspread.mdm.service              I  [okhttp]:check permission begin!
2025-08-21 18:21:27.924  4750-4782  System.out              com.dspread.mdm.service              I  [okhttp]:not MMS!
2025-08-21 18:21:27.924  4750-4782  System.out              com.dspread.mdm.service              I  [okhttp]:not Email!
2025-08-21 18:21:28.277  4750-4782  System.out              com.dspread.mdm.service              I  [socket]:check permission begin!
2025-08-21 18:21:28.757  4750-4782  System.out              com.dspread.mdm.service              I  [OkHttp] sendRequest>>
2025-08-21 18:21:28.758  4750-4782  System.out              com.dspread.mdm.service              I  [OkHttp] sendRequest<<
2025-08-21 18:21:29.404  4750-4782  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader HTTP响应码: 200
2025-08-21 18:21:29.406  4750-4782  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 服务器返回Content-Length: 4771451
2025-08-21 18:21:29.409  4750-4782  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 完整下载模式
2025-08-21 18:21:29.411  4750-4782  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 完整下载，使用服务器返回大小: 4771451
2025-08-21 18:21:29.413  4750-4782  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader localFile = true /sdcard/Android/data/com.dspread.mdm.service/files/config/logo.bin, 追加模式: false
2025-08-21 18:21:31.017  4750-4798  ProfileInstaller        com.dspread.mdm.service              D  Installing profile for com.dspread.mdm.service
2025-08-21 18:21:36.620  4750-4782  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 计算MD5: logo.bin
2025-08-21 18:21:36.693  4750-4782  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 文件: 4771451 MD5: ba1ee533924eae5c408465e7cddcbda4
2025-08-21 18:21:36.695  4750-4782  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader MD5校验正确
2025-08-21 18:21:36.697  4750-4782  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 清理连接资源
2025-08-21 18:21:36.699  4750-4782  Provisioning            com.dspread.mdm.service              I  ℹ️ Logo 下载完成: /sdcard/Android/data/com.dspread.mdm.service/files/config/logo.bin
2025-08-21 18:21:36.702  4750-4782  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] Provisioning进度: 正在下载 BootAnimation...
2025-08-21 18:21:36.705  4750-4782  Provisioning            com.dspread.mdm.service              I  ℹ️ 文件不存在，开始下载: BootAnimation
2025-08-21 18:21:36.707  4750-4782  Provisioning            com.dspread.mdm.service              I  ℹ️ 下载: https://siot-bucket01.s3.ap-northeast-1.amazonaws.com/test/bootanimation.zip -> /sdcard/Android/data/com.dspread.mdm.service/files/config/bootanimation.zip
2025-08-21 18:21:36.709  4750-4782  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 尝试下载，第1次
2025-08-21 18:21:36.712  4750-4782  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 开始下载
2025-08-21 18:21:36.713  4750-4782  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader URL: https://siot-bucket01.s3.ap-northeast-1.amazonaws.com/test/bootanimation.zip
2025-08-21 18:21:36.716  4750-4782  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 路径: /sdcard/Android/data/com.dspread.mdm.service/files/config/bootanimation.zip
2025-08-21 18:21:36.718  4750-4782  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 本地文件不存在，创建文件
2025-08-21 18:21:36.721  4750-4782  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader allFileLength=0, 需要从服务器获取文件大小
2025-08-21 18:21:36.725  4750-4782  System.out              com.dspread.mdm.service              I  [okhttp]:check permission begin!
2025-08-21 18:21:36.725  4750-4782  System.out              com.dspread.mdm.service              I  [okhttp]:not MMS!
2025-08-21 18:21:36.725  4750-4782  System.out              com.dspread.mdm.service              I  [okhttp]:not Email!
2025-08-21 18:21:36.726  4750-4782  System.out              com.dspread.mdm.service              I  [OkHttp] sendRequest>>
2025-08-21 18:21:36.726  4750-4782  System.out              com.dspread.mdm.service              I  [OkHttp] sendRequest<<
2025-08-21 18:21:36.984  4750-4782  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader HTTP响应码: 200
2025-08-21 18:21:36.988  4750-4782  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 服务器返回Content-Length: 4771451
2025-08-21 18:21:36.990  4750-4782  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 完整下载模式
2025-08-21 18:21:36.992  4750-4782  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 完整下载，使用服务器返回大小: 4771451
2025-08-21 18:21:36.994  4750-4782  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader localFile = true /sdcard/Android/data/com.dspread.mdm.service/files/config/bootanimation.zip, 追加模式: false
2025-08-21 18:21:43.209  4750-4782  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 计算MD5: bootanimation.zip
2025-08-21 18:21:43.281  4750-4782  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 文件: 4771451 MD5: ba1ee533924eae5c408465e7cddcbda4
2025-08-21 18:21:43.283  4750-4782  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader MD5校验正确
2025-08-21 18:21:43.285  4750-4782  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 清理连接资源
2025-08-21 18:21:43.287  4750-4782  Provisioning            com.dspread.mdm.service              I  ℹ️ BootAnimation 下载完成: /sdcard/Android/data/com.dspread.mdm.service/files/config/bootanimation.zip
2025-08-21 18:21:43.289  4750-4782  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] Provisioning进度: 正在应用配置...
2025-08-21 18:21:43.291  4750-4782  Provisioning            com.dspread.mdm.service              I  ℹ️ 配置应用完成
2025-08-21 18:21:43.293  4750-4782  Provisioning            com.dspread.mdm.service              I  ℹ️ 远程配置成功，标记首次配置完成
2025-08-21 18:21:43.297  4750-4782  Provisioning            com.dspread.mdm.service              D  🔧 状态标志保存成功: /sdcard/Android/data/com.dspread.mdm.service/files/config/provisioning_flags.json
2025-08-21 18:21:43.299  4750-4782  Provisioning            com.dspread.mdm.service              I  ℹ️ ProvisioningManager: 初始启动标记完成，网络重连重试功能已激活
2025-08-21 18:21:43.301  4750-4782  Provisioning            com.dspread.mdm.service              I  ℹ️ Provisioning配置执行完成
2025-08-21 18:21:43.303  4750-4782  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] Provisioning完成，启动核心组件
2025-08-21 18:21:43.318  4750-4750  WebSocket               com.dspread.mdm.service              I  🔧 开始初始化 WebSocket 组件...
2025-08-21 18:21:43.324  4750-4750  WebSocket               com.dspread.mdm.service              I  🔧 初始化 WebSocket 密钥管理器
2025-08-21 18:21:43.327  4750-4750  WebSocket               com.dspread.mdm.service              I  🔧 生成客户端公私钥对
2025-08-21 18:21:43.487  4750-4750  Common                  com.dspread.mdm.service              I  ✅ WebSocket 密钥管理器初始化完成
2025-08-21 18:21:43.495  4750-4750  WebSocket               com.dspread.mdm.service              I  🔧 开始初始化 WsMessageCenter...
2025-08-21 18:21:43.497  4750-4750  WebSocket               com.dspread.mdm.service              I  🔧 WsMessageCenter 首次初始化
2025-08-21 18:21:43.514  4750-4750  WebSocket               com.dspread.mdm.service              I  🔧 WsTaskManager 初始化完成
2025-08-21 18:21:43.616  4750-4750  WebSocket               com.dspread.mdm.service              I  🔧 WsMessageCenter 初始化完成
2025-08-21 18:21:43.618  4750-4750  WebSocket               com.dspread.mdm.service              I  🔧 开始清理终态任务，当前任务数量: 0
2025-08-21 18:21:43.620  4750-4750  WebSocket               com.dspread.mdm.service              I  🔧 没有需要清理的终态任务
2025-08-21 18:21:43.623  4750-4750  WebSocket               com.dspread.mdm.service              I  🔧 开始重置任务状态，当前任务个数: 0
2025-08-21 18:21:43.625  4750-4750  WebSocket               com.dspread.mdm.service              I  🔧 没有需要重置的任务
2025-08-21 18:21:43.628  4750-4750  Common                  com.dspread.mdm.service              I  ✅ 任务状态恢复完成
2025-08-21 18:21:43.634  4750-4750  WebSocket               com.dspread.mdm.service              I  🔧 使用Provisioning配置的WebSocket URL: wss://api.dspreadserv.net/status/websocket/register
2025-08-21 18:21:43.644  4750-4750  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接管理器初始化完成
2025-08-21 18:21:43.646  4750-4750  Common                  com.dspread.mdm.service              I  ✅ WebSocket 管理器初始化完成
2025-08-21 18:21:43.649  4750-4750  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 管理器初始化完成
2025-08-21 18:21:43.652  4750-4750  Common                  com.dspread.mdm.service              I  ✅ WebSocket 组件初始化完成
2025-08-21 18:21:43.655  4750-4750  WebSocket               com.dspread.mdm.service              I  🔧 启动 WebSocket 连接...
2025-08-21 18:21:43.657  4750-4750  WebSocket               com.dspread.mdm.service              I  🔧 开始连接 WebSocket...
2025-08-21 18:21:43.670  4750-4750  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接到: wss://api.dspreadserv.net/status/websocket/register?serialNo=MDE2MTAwNDAyMDIzMDcwNjAyMjc=&publicKey=TUlHZk1BMEdDU3FHU0liM0RRRUJBUVVBQTRHTkFEQ0JpUUtCZ1FDZkZYOHM2NmFBS1c3eXA4TkhJYnlXZk1rZjZuRk9udERFY1RYMEpmdGUxVEVVOU85bi9odnJ5UWRPUE0zQit4T3dtcHYyYU96Qkl2cHVRNTZraDhHUnU0YUxyY0RseFZhOGhQYS9qSHhlN01XUW4xM2pVazQrT2xGcjRqL1JURit4QXFzQlZXdmxIYU1MSG1ZSVZRUXdUSktJRW9WQkRuSXRKV0dsTExCSHZRSURBUUFC&query=1&msgVer=3&timestamp=1755771703661&signature=I8Raw/OMmZTcwwpLAzcD33XVTsgAlTPlZ3RlpS+rDXjwC5xZdefjLZbALuC0ipkjASi/4kmsjCtQ/BJJ5Clpd5KhXejoKKnFy1SJM75zc4ULeiBBad07PlGEVBQL2skcONJ1ygqBZVpuh7oIVY0C2YzjEzJ8KOoNhq+k/FUBB50=
2025-08-21 18:21:43.676  4750-4750  WebSocket               com.dspread.mdm.service              I  🔧 未检测到系统代理设置
2025-08-21 18:21:43.700  4750-4750  WebSocket               com.dspread.mdm.service              I  🔧 ===== WebSocket 库默认配置 =====
2025-08-21 18:21:43.702  4750-4750  WebSocket               com.dspread.mdm.service              I  🔧 默认 PING 间隔: 30000ms (30秒)
2025-08-21 18:21:43.705  4750-4750  WebSocket               com.dspread.mdm.service              I  🔧 默认 PONG 间隔: 0ms (0秒)
2025-08-21 18:21:43.707  4750-4750  WebSocket               com.dspread.mdm.service              I  🔧 帧队列大小: 5
2025-08-21 18:21:43.709  4750-4750  WebSocket               com.dspread.mdm.service              I  🔧 连接超时: 15000ms (15秒)
2025-08-21 18:21:43.712  4750-4750  WebSocket               com.dspread.mdm.service              I  🔧 允许缺失关闭帧: false
2025-08-21 18:21:43.713  4750-4750  WebSocket               com.dspread.mdm.service              I  🔧 ================================
2025-08-21 18:21:43.717  4750-4750  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接请求已发送
2025-08-21 18:21:43.719  4750-4750  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接启动完成
2025-08-21 18:21:43.722  4750-4750  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 开始初始化广播管理器...
2025-08-21 18:21:43.725  4750-4750  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 开始初始化...
2025-08-21 18:21:43.733  4750-4750  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 包更新广播接收器注册完成
2025-08-21 18:21:43.735  4750-4750  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 统一广播接收器注册完成
2025-08-21 18:21:43.737  4750-4750  Common                  com.dspread.mdm.service              I  ✅ BroadcastManager 初始化完成
2025-08-21 18:21:43.740  4750-4805  System.out              com.dspread.mdm.service              I  [socket]:check permission begin!
2025-08-21 18:21:43.756  4750-4750  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 广播管理器初始化完成
2025-08-21 18:21:43.758  4750-4750  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 开始启动所有定时器...
2025-08-21 18:21:43.760  4750-4750  Provisioning            com.dspread.mdm.service              D  🔧 ProvisioningEventHandler 启动Provisioning定时器: 300s (0小时)
2025-08-21 18:21:43.766  4750-4750  Common                  com.dspread.mdm.service              I  ✅ ⏰ 设置初始化定时器成功，下次执行: 300秒后 (0小时)
2025-08-21 18:21:43.767  4750-4750  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] Provisioning定时器启动成功
2025-08-21 18:21:43.771  4750-4750  Common                  com.dspread.mdm.service              I  ✅ ⏰ 设置心跳定时器成功，下次执行: 60秒后 (1分钟)
2025-08-21 18:21:43.773  4750-4750  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 心跳定时器启动成功
2025-08-21 18:21:43.778  4750-4750  Common                  com.dspread.mdm.service              I  ✅ ⏰ 设置任务执行定时器成功，下次执行: 60秒后 (1分钟)
2025-08-21 18:21:43.780  4750-4750  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 任务执行定时器启动成功
2025-08-21 18:21:43.784  4750-4750  Common                  com.dspread.mdm.service              I  ✅ ⏰ 设置终端信息定时器成功，下次执行: 120秒后 (2分钟)
2025-08-21 18:21:43.786  4750-4750  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 终端信息上传定时器启动成功
2025-08-21 18:21:43.788  4750-4750  Common                  com.dspread.mdm.service              D  🔧 TimerConfig ========== 当前定时器配置 (调试模式) ==========
2025-08-21 18:21:43.790  4750-4750  Common                  com.dspread.mdm.service              D  🔧 TimerConfig ①心跳定时器: 60秒
2025-08-21 18:21:43.792  4750-4750  Common                  com.dspread.mdm.service              D  🔧 TimerConfig ②终端信息上传定时器: 120秒
2025-08-21 18:21:43.796  4750-4750  Common                  com.dspread.mdm.service              D  🔧 TimerConfig ③任务执行定时器: 60秒
2025-08-21 18:21:43.798  4750-4750  Common                  com.dspread.mdm.service              D  🔧 TimerConfig ④服务守护定时器: 120秒
2025-08-21 18:21:43.800  4750-4750  Common                  com.dspread.mdm.service              D  🔧 TimerConfig ⑤初始化定时器: 300秒
2025-08-21 18:21:43.802  4750-4750  Common                  com.dspread.mdm.service              D  🔧 TimerConfig ===============================================
2025-08-21 18:21:43.804  4750-4750  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 所有定时器启动完成
2025-08-21 18:21:43.807  4750-4750  Common                  com.dspread.mdm.service              D  🔧 [ModuleManagerRegistry] 模块管理器注册中心初始化完成
2025-08-21 18:21:43.810  4750-4750  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 核心组件初始化完成
2025-08-21 18:21:43.810  4750-4782  Common                  com.dspread.mdm.service              D  🔧 [ModuleManagerRegistry] 启动所有模块
2025-08-21 18:21:43.814  4750-4750  Platform                com.dspread.mdm.service              D  🔧 NetworkApi 单例实例已创建
2025-08-21 18:21:43.819  4750-4750  TetheringManager        com.dspread.mdm.service              I  registerTetheringEventCallback:com.dspread.mdm.service
2025-08-21 18:21:43.820  4750-4782  Common                  com.dspread.mdm.service              D  🔧 Module LogStream Manager status changed to: INITIALIZED
2025-08-21 18:21:43.824  4750-4782  Common                  com.dspread.mdm.service              D  🔧 Module LogStream Manager status changed to: STARTING
2025-08-21 18:21:43.830  4750-4750  Receiver                com.dspread.mdm.service              D  🔧 网络状态变化: 连接=true, 类型=WIFI
2025-08-21 18:21:43.832  4750-4750  Receiver                com.dspread.mdm.service              D  🔧 网络恢复，尝试重新连接WebSocket
2025-08-21 18:21:43.834  4750-4750  Receiver                com.dspread.mdm.service              D  🔧 ServiceManagementEventHandler 处理网络变化事件
2025-08-21 18:21:43.840  4750-4750  Common                  com.dspread.mdm.service              D  🔧 ServiceStartupManager 服务正在运行，无需启动: NETWORK_CHANGE
2025-08-21 18:21:43.843  4750-4750  Receiver                com.dspread.mdm.service              D  🔧 ServiceManagementEventHandler 网络变化后服务检查完成
2025-08-21 18:21:43.845  4750-4750  Provisioning            com.dspread.mdm.service              D  🔧 ProvisioningEventHandler 网络状态变化
2025-08-21 18:21:43.853  4750-4750  Provisioning            com.dspread.mdm.service              I  ℹ️ ProvisioningEventHandler 检测到网络重连，但Provisioning已完成，按正常定时器间隔检查更新
2025-08-21 18:21:43.856  4750-4750  Receiver                com.dspread.mdm.service              D  🔧 电池状态变化: 电量=93%, 温度=27°C, 充电=true
2025-08-21 18:21:43.863  4750-4750  WebSocket               com.dspread.mdm.service              I  🔧 C0902 被动式上送: battery_level_change_5 (被动: 1)
2025-08-21 18:21:43.884  4750-4750  WebSocket               com.dspread.mdm.service              I  🔧 消息已缓存: C0902 (缓存数量: 1)
2025-08-21 18:21:43.886  4750-4750  WebSocket               com.dspread.mdm.service              I  🔧 C0902 电池状态上送成功 (1, trigger=battery_level_change_5)
2025-08-21 18:21:43.909  4750-4782  Common                  com.dspread.mdm.service              D  🔧 Module LogStream Manager status changed to: RUNNING
2025-08-21 18:21:43.911  4750-4782  Common                  com.dspread.mdm.service              D  🔧 [ModuleManagerRegistry] LogStreamManager启动成功
2025-08-21 18:21:43.935  4750-4782  Common                  com.dspread.mdm.service              D  🔧 Module Geofence Manager status changed to: INITIALIZED
2025-08-21 18:21:43.940  4750-4782  Common                  com.dspread.mdm.service              D  🔧 Module Geofence Manager status changed to: STARTING
2025-08-21 18:21:43.960  4750-4782  Platform                com.dspread.mdm.service              D  🔧 用户交互监控已启动（简化版本）
2025-08-21 18:21:43.961  4750-4750  Platform                com.dspread.mdm.service              D  🔧 [WiFiManagerApi] WiFiManagerApi 单例实例已创建
2025-08-21 18:21:43.962  4750-4782  Platform                com.dspread.mdm.service              D  🔧 使用模拟用户交互检测
2025-08-21 18:21:43.964  4750-4782  Platform                com.dspread.mdm.service              D  🔧 用户交互监控已启动
2025-08-21 18:21:43.967  4750-4782  Service                 com.dspread.mdm.service              D  🔧 [GeofenceManager]Constants.geofenceStatus = 1
2025-08-21 18:21:43.969  4750-4782  Service                 com.dspread.mdm.service              D  🔧 [GeofenceManager]-- Constants.storeId =  -- Constants.storeSsid =  -- Constants.storeIp = 
2025-08-21 18:21:43.971  4750-4782  Common                  com.dspread.mdm.service              D  🔧 Module Geofence Manager status changed to: RUNNING
2025-08-21 18:21:43.974  4750-4782  Common                  com.dspread.mdm.service              D  🔧 [ModuleManagerRegistry] GeofenceManager启动成功
2025-08-21 18:21:43.987  4750-4782  RuleBase                com.dspread.mdm.service              D  🔧 RuleBaseManager 开始初始化RuleBaseManager...
2025-08-21 18:21:43.990  4750-4806  System.out              com.dspread.mdm.service              I  [socket]:check permission begin!
2025-08-21 18:21:43.993  4750-4782  RuleBase                com.dspread.mdm.service              D  🔧 RuleBaseStorage RuleBase存储管理器初始化完成
2025-08-21 18:21:43.995  4750-4782  RuleBase                com.dspread.mdm.service              D  🔧 RuleStateMonitor 状态监控器初始化完成
2025-08-21 18:21:43.997  4750-4782  RuleBase                com.dspread.mdm.service              D  🔧 RuleBaseManager RuleBaseManager初始化完成
2025-08-21 18:21:43.999  4750-4782  Common                  com.dspread.mdm.service              D  🔧 [ModuleManagerRegistry] RuleBaseManager启动成功
2025-08-21 18:21:44.000  4750-4782  Common                  com.dspread.mdm.service              D  🔧 [ModuleManagerRegistry] 所有模块启动完成
2025-08-21 18:21:46.103  4750-4816  Common                  com.dspread.mdm.service              I  ✅ WebSocket 物理连接成功，等待服务器 hellMsg
2025-08-21 18:21:46.106  4750-4816  WebSocket               com.dspread.mdm.service              I  🔧 ===== 心跳发送器实际配置 =====
2025-08-21 18:21:46.108  4750-4816  WebSocket               com.dspread.mdm.service              I  🔧 实际 PING 发送间隔: 30000ms (30秒)
2025-08-21 18:21:46.111  4750-4816  WebSocket               com.dspread.mdm.service              I  🔧 PING 定时器名称: PingSender
2025-08-21 18:21:46.113  4750-4816  WebSocket               com.dspread.mdm.service              I  🔧 实际 PONG 发送间隔: 0ms (0秒)
2025-08-21 18:21:46.116  4750-4816  WebSocket               com.dspread.mdm.service              I  🔧 PONG 定时器名称: PongSender
2025-08-21 18:21:46.118  4750-4816  WebSocket               com.dspread.mdm.service              I  🔧 ===============================
2025-08-21 18:21:46.121  4750-4816  WebSocket               com.dspread.mdm.service              I  🔧 启动PING/PONG检测，阈值: 连续3个PING无PONG响应
2025-08-21 18:21:46.149  4750-4816  Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"msg_state":"P","request_time":"1755580536662","data":{"taskList":[{"beginDate":"2024-08-19 05:15:36","taskType":"02","apkName":"Via","endDate":"9999-12-31 23:59:59","appId":"1755580536662","packName":"mark.via","versionName":"6.2.0","taskId":"1755580536662","versionCode":"20250117"}]},"tranCode":"ST001","request_id":"1755580536662ST001","version":"1","serialNo":"01610040202307060227"}
2025-08-21 18:21:46.152  4750-4816  WebSocket               com.dspread.mdm.service              I  🔧 准备发送响应确认 - requestId: 1755580536662ST001, needResponse: true
2025-08-21 18:21:46.158  4750-4816  WebSocket               com.dspread.mdm.service              I  🔧 非重要消息，不缓存: C0000
2025-08-21 18:21:46.165  4750-4816  WebSocket               com.dspread.mdm.service              I  🔧 非重要消息，不缓存: C0000
2025-08-21 18:21:46.167  4750-4816  WebSocket               com.dspread.mdm.service              I  🔧 已发送C0000响应确认: requestId=1755580536662ST001
2025-08-21 18:21:46.170  4750-4816  Task                    com.dspread.mdm.service              D  🔧 处理任务消息: ST001
2025-08-21 18:21:46.172  4750-4816  Task                    com.dspread.mdm.service              I  ℹ️ 更新任务列表: requestId=1755580536662ST001, 任务数量=1
2025-08-21 18:21:46.174  4750-4816  Task                    com.dspread.mdm.service              D  🔧 任务插入到末尾: taskId=1755580536662
2025-08-21 18:21:46.175  4750-4816  Task                    com.dspread.mdm.service              D  🔧 添加/更新任务: taskId=1755580536662
2025-08-21 18:21:46.179  4750-4816  Task                    com.dspread.mdm.service              D  🔧 保存任务列表到存储: 1 个
2025-08-21 18:21:46.181  4750-4816  Task                    com.dspread.mdm.service              D  🔧 开始处理任务列表，共 1 个任务
2025-08-21 18:21:46.183  4750-4816  Task                    com.dspread.mdm.service              D  🔧 处理任务: taskId=1755580536662, type=02, package=mark.via, apk=Via
2025-08-21 18:21:46.185  4750-4816  Task                    com.dspread.mdm.service              D  🔧 处理卸载任务: 1755580536662
2025-08-21 18:21:46.192  4750-4816  Task                    com.dspread.mdm.service              D  🔧 更新任务状态: taskId=1755580536662, state=D02
2025-08-21 18:21:46.195  4750-4816  Task                    com.dspread.mdm.service              D  🔧 保存任务列表到存储: 1 个
2025-08-21 18:21:46.197  4750-4816  Task                    com.dspread.mdm.service              D  🔧 保存任务列表到存储: 1 个
2025-08-21 18:21:46.199  4750-4816  WebSocket               com.dspread.mdm.service              I  🔧 ST001 主动式上送: task_failed (主动: 2)
2025-08-21 18:21:46.207  4750-4816  WebSocket               com.dspread.mdm.service              I  🔧 消息已缓存: C0108 (缓存数量: 2)
2025-08-21 18:21:46.209  4750-4816  WebSocket               com.dspread.mdm.service              I  🔧 任务结果实时上送: taskId=1755580536662, result=D02 (1)
2025-08-21 18:21:46.222  4750-4816  Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"msg_state":"P","request_time":"1755580524415","data":{"taskList":[{"beginDate":"2024-08-19 05:15:24","taskType":"02","apkName":"OpenVPN for Android","endDate":"9999-12-31 23:59:59","appId":"1755580524415","packName":"de.blinkt.openvpn","versionName":"0.5.36a","taskId":"1755580524415","versionCode":"65"}]},"tranCode":"ST001","request_id":"1755580524415ST001","version":"1","serialNo":"01610040202307060227"}
2025-08-21 18:21:46.225  4750-4816  WebSocket               com.dspread.mdm.service              I  🔧 准备发送响应确认 - requestId: 1755580524415ST001, needResponse: true
2025-08-21 18:21:46.230  4750-4816  WebSocket               com.dspread.mdm.service              I  🔧 非重要消息，不缓存: C0000
2025-08-21 18:21:46.234  4750-4816  WebSocket               com.dspread.mdm.service              I  🔧 非重要消息，不缓存: C0000
2025-08-21 18:21:46.236  4750-4816  WebSocket               com.dspread.mdm.service              I  🔧 已发送C0000响应确认: requestId=1755580524415ST001
2025-08-21 18:21:46.238  4750-4816  Task                    com.dspread.mdm.service              D  🔧 处理任务消息: ST001
2025-08-21 18:21:46.239  4750-4816  Task                    com.dspread.mdm.service              I  ℹ️ 更新任务列表: requestId=1755580524415ST001, 任务数量=1
2025-08-21 18:21:46.242  4750-4816  Task                    com.dspread.mdm.service              D  🔧 任务按时间插入到位置: 0, taskId=1755580524415
2025-08-21 18:21:46.243  4750-4816  Task                    com.dspread.mdm.service              D  🔧 添加/更新任务: taskId=1755580524415
2025-08-21 18:21:46.245  4750-4816  Task                    com.dspread.mdm.service              D  🔧 发现已完成任务: taskId=1755580536662, result=D02
2025-08-21 18:21:46.247  4750-4816  Task                    com.dspread.mdm.service              D  🔧 上送任务结果: taskId=1755580536662, result=D02
2025-08-21 18:21:46.248  4750-4816  Task                    com.dspread.mdm.service              D  🔧 清理已完成任务: taskId=1755580536662, result=D02
2025-08-21 18:21:46.250  4750-4816  Task                    com.dspread.mdm.service              D  🔧 清理了 1 个已完成任务
2025-08-21 18:21:46.252  4750-4816  Task                    com.dspread.mdm.service              D  🔧 保存任务列表到存储: 1 个
2025-08-21 18:21:46.254  4750-4816  Task                    com.dspread.mdm.service              D  🔧 开始处理任务列表，共 1 个任务
2025-08-21 18:21:46.256  4750-4816  Task                    com.dspread.mdm.service              D  🔧 处理任务: taskId=1755580524415, type=02, package=de.blinkt.openvpn, apk=OpenVPN for Android
2025-08-21 18:21:46.257  4750-4816  Task                    com.dspread.mdm.service              D  🔧 处理卸载任务: 1755580524415
2025-08-21 18:21:46.260  4750-4816  Task                    com.dspread.mdm.service              D  🔧 更新任务状态: taskId=1755580524415, state=D02
2025-08-21 18:21:46.262  4750-4816  Task                    com.dspread.mdm.service              D  🔧 保存任务列表到存储: 1 个
2025-08-21 18:21:46.265  4750-4816  Task                    com.dspread.mdm.service              D  🔧 保存任务列表到存储: 1 个
2025-08-21 18:21:46.266  4750-4816  WebSocket               com.dspread.mdm.service              I  🔧 ST001 主动式上送: task_failed (主动: 3)
2025-08-21 18:21:46.274  4750-4816  WebSocket               com.dspread.mdm.service              I  🔧 消息已缓存: C0108 (缓存数量: 3)
2025-08-21 18:21:46.276  4750-4816  WebSocket               com.dspread.mdm.service              I  🔧 任务结果实时上送: taskId=1755580524415, result=D02 (1)
2025-08-21 18:21:46.287  4750-4816  Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"hellMsg":"hello 01610040202307060227","tranCode":"S0000","version":"1","rebootTime":"02:53:49","serialNo":"01610040202307060227","deviceStatus":"6"}
2025-08-21 18:21:46.289  4750-4816  Common                  com.dspread.mdm.service              I  ✅ 收到服务器 hellMsg，WebSocket 业务连接成功！
2025-08-21 18:21:46.290  4750-4816  Common                  com.dspread.mdm.service              I  ✅ WebSocket 业务连接成功！
2025-08-21 18:21:46.292  4750-4816  WebSocket               com.dspread.mdm.service              I  🔧 业务连接成功，重连计数已重置为 0
2025-08-21 18:21:46.295  4750-4816  Platform                com.dspread.mdm.service              D  🔧 WakeLockManager 获取WiFi锁成功，防止WiFi休眠
2025-08-21 18:21:46.297  4750-4816  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接成功，检查自身更新任务状态
2025-08-21 18:21:46.300  4750-4816  Receiver                com.dspread.mdm.service              D  🔧 发送广播: com.dspread.mdm.service.CHECK_SELF_UPDATE_STATUS
2025-08-21 18:21:46.302  4750-4816  WebSocket               com.dspread.mdm.service              I  🔧 开始发送缓存消息，数量: 3
2025-08-21 18:21:46.308  4750-4816  WebSocket               com.dspread.mdm.service              I  🔧 C0902 主动式上送: cached_message_resend (主动: 4)
2025-08-21 18:21:46.314  4750-4816  Common                  com.dspread.mdm.service              I  ✅ 缓存消息发送成功: C0902
2025-08-21 18:21:46.816  4750-4816  WebSocket               com.dspread.mdm.service              I  🔧 C0108 主动式上送: cached_message_resend (主动: 5)
2025-08-21 18:21:46.823  4750-4816  Common                  com.dspread.mdm.service              I  ✅ 缓存消息发送成功: C0108
2025-08-21 18:21:47.326  4750-4816  WebSocket               com.dspread.mdm.service              I  🔧 C0108 主动式上送: cached_message_resend (主动: 6)
2025-08-21 18:21:47.333  4750-4816  Common                  com.dspread.mdm.service              I  ✅ 缓存消息发送成功: C0108
2025-08-21 18:21:47.836  4750-4816  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接成功
2025-08-21 18:21:47.839  4750-4816  WebSocket               com.dspread.mdm.service              I  🔧 设备状态: 6
2025-08-21 18:21:47.841  4750-4816  WebSocket               com.dspread.mdm.service              I  🔧 检查首次连接标志: isFirstTimeWebSocketConnected=true
2025-08-21 18:21:47.844  4750-4816  WebSocket               com.dspread.mdm.service              I  🔧 首次WebSocket连接成功，发送初始化信息并执行初始化检查
2025-08-21 18:21:47.846  4750-4816  WebSocket               com.dspread.mdm.service              I  🔧 开始发送初始化信息（带流量控制）
2025-08-21 18:21:47.848  4750-4816  WebSocket               com.dspread.mdm.service              I  🔧 C0901 被动式上送: first_connection (被动: 2)
2025-08-21 18:21:47.853  4750-4816  Platform                com.dspread.mdm.service              D  🔧 重新收集应用信息
2025-08-21 18:21:47.929  4750-4816  AppManager              com.dspread.mdm.service              I  ℹ️ 应用信息获取完成: 总数120(系统119/用户1) 返回1个
2025-08-21 18:21:47.934  4750-4816  Platform                com.dspread.mdm.service              D  🔧 应用信息: 1 个应用
2025-08-21 18:21:47.943  4750-4816  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0901","serialNo":"01610040202307060227","request_time":"1755771707937","request_id":"1755771707937C0901","version":"1","data":{"apkInfo":[{"packName":"com.dspread.mdm.service","apkName":"service","versionCode":22,"versionName":"1.1.03.20250821.DSPREAD.MDM.SERVICE","updateDate":"2025-08-21 18:21:22"}],"sytemInfo":{"androidVersion":"11","buildNumber":"D60_Baseline_debug_20250708_ota_test","aspl":"2021-12-05"},"serviceInfo":[]},"myVersionName":"1.1.03.20250821.DSPREAD.MDM.SERVICE","terminalDate":"20250821182147"}
2025-08-21 18:21:47.945  4750-4816  WebSocket               com.dspread.mdm.service              I  🔧 C0901 应用信息上送成功 (1)
2025-08-21 18:21:48.947  4750-4816  WebSocket               com.dspread.mdm.service              I  🔧 C0902 被动式上送: first_connection (被动: 3)
2025-08-21 18:21:48.963  4750-4816  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0902","serialNo":"01610040202307060227","request_time":"1755771708955","request_id":"1755771708955C0902","version":"1","data":{"batteryLife":93,"batteryHealth":2,"temprature":"27.7","isCharging":"1","isLowBattery":"0","powerOff":"0","outOfBattery":"0"},"myVersionName":"1.1.03.20250821.DSPREAD.MDM.SERVICE","terminalDate":"20250821182148"}
2025-08-21 18:21:48.965  4750-4816  WebSocket               com.dspread.mdm.service              I  🔧 C0902 电池状态上送成功 (1, trigger=first_connection)
2025-08-21 18:21:49.968  4750-4816  WebSocket               com.dspread.mdm.service              I  🔧 C0903 被动式上送: first_connection (被动: 4)
2025-08-21 18:21:50.106  4750-4816  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0903","serialNo":"01610040202307060227","request_time":"1755771710096","request_id":"1755771710096C0903","version":"1","data":{"hardwareInfo":[{"name":"CPU Usage","desc":"cpu:0.50"},{"name":"storage_usage_status","desc":" 5.43GB \/ 32.00GB ","state":"1"},{"name":"total_memory","desc":" 0.54GB \/ 1.86GB ","state":"1"},{"name":"androidVersion","desc":"11","state":"1"},{"name":"network_status","desc":"wifi","state":"1"},{"name":"OSVer","desc":"11","state":"1"}],"locationInfo":{"gps":{"longitude":"-999.0","latitude":"-999.0","satelliteCount":0,"satelliteCountInUsed":0,"satelliteAverSNR":"0.00","distance":"0.0","lockStatus":"1","lockMeter":"200","proId":"","proName":""},"baseSite":{"MCC":"","MNC":"","CID":"-1","LAC":"-1","DBM":"-999","IMSI":"","ICCID":""},"wifi":{"SSID":"fubox_2.4G","IP":"**************","MAC":"52:C3:63:13:8C:EE","SSTH":"-25","BSSID":"7c:10:c9:d6:9f:10"},"address":"无法获取"}},"myVersionName":"1.1.03.20250821.DSPREAD.MDM.SERVICE","terminalDate":"20250821182150"}
2025-08-21 18:21:50.108  4750-4816  WebSocket               com.dspread.mdm.service              I  🔧 C0903 数据信息上送成功 (1)
2025-08-21 18:21:51.111  4750-4816  WebSocket               com.dspread.mdm.service              I  🔧 C0904 被动式上送: first_connection (被动: 5)
2025-08-21 18:21:51.206  4750-4816  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0904","serialNo":"01610040202307060227","request_time":"1755771711193","request_id":"1755771711193C0904","version":"1","data":{"wifiOption":[{"SSID":"fubox_2.4G","SSTH":"-28"},{"SSID":"2206-5G","SSTH":"-32"},{"SSID":"fubox_5G","SSTH":"-40"},{"SSID":"2206","SSTH":"-41"},{"SSID":"2205_5G","SSTH":"-51"},{"SSID":"2205","SSTH":"-53"},{"SSID":"@Ruijie-1816","SSTH":"-56"},{"SSID":"2207","SSTH":"-67"},{"SSID":"DIRECT-C6-Mi All-in-One Inkjet","SSTH":"-70"},{"SSID":"DIRECT-CC-HP Laser 1188w","SSTH":"-71"},{"SSID":"2306","SSTH":"-72"},{"SSID":"2106","SSTH":"-72"},{"SSID":"2103","SSTH":"-77"},{"SSID":"2207-5G","SSTH":"-80"},{"SSID":"诺富特酒店2208","SSTH":"-81"},{"SSID":"dingjie888","SSTH":"-82"},{"SSID":"FJQS","SSTH":"-83"},{"SSID":"ChinaNet-SnSC","SSTH":"-85"},{"SSID":"2405","SSTH":"-86"},{"SSID":"2103_5G","SSTH":"-86"},{"SSID":"1621","SSTH":"-87"},{"SSID":"HUAWEI_B311_福","SSTH":"-92"},{"SSID":"2106-5G","SSTH":"-94"}],"baseSite":{"MCC":"","MNC":"","CID":"-1","LAC":"-1","DBM":"-999","IMSI":"","ICCID":""},"wifi":{"SSID":"fubox_2.4G","IP":"**************","MAC":"52:C3:63:13:8C:EE","SSTH":"-25","BSSID":"7c:10:c9:d6:9f:10"}},"myVersionName":"1.1.03.20250821.DSPREAD.MDM.SERVICE","terminalDate":"20250821182151"}
2025-08-21 18:21:51.208  4750-4816  WebSocket               com.dspread.mdm.service              I  🔧 C0904 网络状态上送成功 (1)
2025-08-21 18:21:52.211  4750-4816  WebSocket               com.dspread.mdm.service              I  🔧 C0906 被动式上送: first_connection (被动: 6)
2025-08-21 18:21:52.218  4750-4816  Platform                com.dspread.mdm.service              W  ⚠️ SpVersionApi SP版本信息格式异常，未找到SP_VERSION标记
2025-08-21 18:21:52.222  4750-4816  Platform                com.dspread.mdm.service              D  🔧 SpVersionApi 从Settings获取SP版本: STD 582.1.238.8
2025-08-21 18:21:52.241  4750-4816  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0906","serialNo":"01610040202307060227","request_time":"1755771712229","request_id":"1755771712229C0906","version":"1","data":{"firmWareInfo":{"spfw":"STD 582.1.238.8"},"imei_1":"014250620000709","imei_2":"","wifi_mac":"52:c3:63:13:8c:ee","bt_mac":"","bsn":""},"myVersionName":"1.1.03.20250821.DSPREAD.MDM.SERVICE","terminalDate":"20250821182152"}
2025-08-21 18:21:52.246  4750-4816  WebSocket               com.dspread.mdm.service              I  🔧 C0906 设备信息上送成功 (1)
2025-08-21 18:21:52.253  4750-4816  WebSocket               com.dspread.mdm.service              I  🔧 C0109 被动式上送: first_connection (被动: 7)
2025-08-21 18:21:52.262  4750-4816  Platform                com.dspread.mdm.service              D  🔧 重新收集应用信息（强制刷新）
2025-08-21 18:21:52.296  4750-4766  System                  com.dspread.mdm.service              W  A resource failed to call close. 
2025-08-21 18:21:52.327  4750-4816  AppManager              com.dspread.mdm.service              I  ℹ️ 应用信息获取完成: 总数120(系统119/用户1) 返回1个
2025-08-21 18:21:52.331  4750-4816  Platform                com.dspread.mdm.service              D  🔧 应用信息: 1 个应用
2025-08-21 18:21:52.430  4750-4816  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0109","serialNo":"01610040202307060227","request_time":"1755771712418","request_id":"1755771712418C0109","version":"1","data":{"apkInfo":[{"packName":"com.dspread.mdm.service","apkName":"service","versionCode":22,"versionName":"1.1.03.20250821.DSPREAD.MDM.SERVICE","updateDate":"2025-08-21 18:21:22"}],"locationInfo":{"gps":{"longitude":"-999.0","latitude":"-999.0","satelliteCount":0,"satelliteCountInUsed":0,"satelliteAverSNR":"0.00","distance":"0.0","lockStatus":"1","lockMeter":"200","proId":"","proName":""},"baseSite":{"MCC":"","MNC":"","CID":"-1","LAC":"-1","DBM":"-999","IMSI":"","ICCID":""},"wifi":{"SSID":"fubox_2.4G","IP":"**************","MAC":"52:C3:63:13:8C:EE","SSTH":"-25","BSSID":"7c:10:c9:d6:9f:10"},"address":"无法获取"},"hardwareInfo":[{"name":"CPU Usage","desc":"cpu:0.50"},{"name":"storage_usage_status","desc":" 5.43GB \/ 32.00GB ","state":"1"},{"name":"total_memory","desc":" 0.54GB \/ 1.86GB ","state":"1"},{"name":"androidVersion","desc":"11","state":"1"},{"name":"network_status","desc":"wifi","state":"1"},{"name":"OSVer","desc":"11","state":"1"}],"wifiOption":[{"SSID":"fubox_2.4G","SSTH":"-28"},{"SSID":"2206-5G","SSTH":"-32"},{"SSID":"fubox_5G","SSTH":"-40"},{"SSID":"2206","SSTH":"-41"},{"SSID":"2205_5G","SSTH":"-51"},{"SSID":"2205","SSTH":"-53"},{"SSID":"@Ruijie-1816","SSTH":"-56"},{"SSID":"2207","SSTH":"-67"},{"SSID":"DIRECT-C6-Mi All-in-One Inkjet","SSTH":"-70"},{"SSID":"DIRECT-CC-HP Laser 1188w","SSTH":"-71"},{"SSID":"2306","SSTH":"-72"},{"SSID":"2106","SSTH":"-72"},{"SSID":"2103","SSTH":"-77"},{"SSID":"2207-5G","SSTH":"-80"},{"SSID":"诺富特酒店2208","SSTH":"-81"},{"SSID":"dingjie888","SSTH":"-82"},{"SSID":"FJQS","SSTH":"-83"},{"SSID":"ChinaNet-SnSC","SSTH":"-85"},{"SSID":"2405","SSTH":"-86"},{"SSID":"2103_5G","SSTH":"-86"},{"SSID":"1621","SSTH":"-87"},{"SSID":"HUAWEI_B311_福","SSTH":"-92"},{"SSID":"2106-5G","SSTH":"-94"}],"sytemInfo":{"androidVersion":"11","buildNumber":"D60_Baseline_debug_20250708_ota_test","aspl":"2021-12-05"}},"myVersionName":"1.1.03.20250821.DSPREAD.MDM.SERVICE","terminalDate":"20250821182152"}
2025-08-21 18:21:52.432  4750-4816  WebSocket               com.dspread.mdm.service              I  🔧 C0109 终端信息上传完成 (1, trigger=first_connection)
2025-08-21 18:21:52.433  4750-4816  Common                  com.dspread.mdm.service              I  ✅ 初始化信息发送完成
2025-08-21 18:21:52.436  4750-4816  WebSocket               com.dspread.mdm.service              I  🔧 检查服务更新结果
2025-08-21 18:21:52.438  4750-4816  Task                    com.dspread.mdm.service              I  ℹ️ 本地任务列表长度: 1
2025-08-21 18:21:52.439  4750-4816  Task                    com.dspread.mdm.service              D  🔧 本地任务列表内容: [{"beginDate":"2024-08-19 05:15:24","taskType":"02","apkName":"OpenVPN for Android","endDate":"9999-12-31 23:59:59","appId":"1755580524415","packName":"de.blinkt.openvpn","versionName":"0.5.36a","taskId":"1755580524415","versionCode":"65","request_id":"1755580524415ST001","request_time":"1755580524415","silent_install":"","taskResult":"D02","lastUpdateTime":1755771706263}]
2025-08-21 18:21:52.441  4750-4816  Task                    com.dspread.mdm.service              I  ℹ️ 当前应用版本: versionCode=22, versionName=1.1.03.20250821.DSPREAD.MDM.SERVICE
2025-08-21 18:21:52.442  4750-4816  Task                    com.dspread.mdm.service              I  ℹ️ 任务列表数量: 1
2025-08-21 18:21:52.444  4750-4816  Task                    com.dspread.mdm.service              D  🔧 检查任务[0]: taskId=1755580524415, taskType=02, pkgName=de.blinkt.openvpn, versionCode=65, versionName=0.5.36a, taskResult=D02
2025-08-21 18:21:52.446  4750-4816  WebSocket               com.dspread.mdm.service              I  🔧 检查OS升级状态...
2025-08-21 18:21:52.450  4750-4816  Common                  com.dspread.mdm.service              D  🔧 OsUpdateStatusChecker WebSocket连接成功，检查OS升级状态
2025-08-21 18:21:52.452  4750-4816  Common                  com.dspread.mdm.service              D  🔧 OsUpdateStatusChecker 没有待检查的OS升级任务
2025-08-21 18:21:52.453  4750-4816  WebSocket               com.dspread.mdm.service              I  🔧 OS升级状态检查已触发
2025-08-21 18:21:52.455  4750-4816  WebSocket               com.dspread.mdm.service              I  🔧 首次连接初始化检查完成
2025-08-21 18:21:52.467  4750-4816  Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"request_time":"1755771708287","org_request_time":"1755771706203","org_request_id":"1755771706203C0108","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755771708287S0000","serialNo":"01610040202307060227"}
2025-08-21 18:21:52.469  4750-4816  WebSocket               com.dspread.mdm.service              I  🔧 收到响应消息: transCode=S0000, orgRequestId=1755771706203C0108, state=0, remark=
2025-08-21 18:21:52.471  4750-4816  WebSocket               com.dspread.mdm.service              I  🔧 处理任务结果响应: state=0
2025-08-21 18:21:52.472  4750-4816  WebSocket               com.dspread.mdm.service              I  🔧 任务结果上传成功
2025-08-21 18:21:52.482  4750-4816  Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"request_time":"1755771708887","org_request_time":"1755771706270","org_request_id":"1755771706270C0108","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755771708887S0000","serialNo":"01610040202307060227"}
2025-08-21 18:21:52.485  4750-4816  WebSocket               com.dspread.mdm.service              I  🔧 收到响应消息: transCode=S0000, orgRequestId=1755771706270C0108, state=0, remark=
2025-08-21 18:21:52.486  4750-4816  WebSocket               com.dspread.mdm.service              I  🔧 处理任务结果响应: state=0
2025-08-21 18:21:52.488  4750-4816  WebSocket               com.dspread.mdm.service              I  🔧 任务结果上传成功
2025-08-21 18:21:52.759  4750-4816  Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"request_time":"1755771713710","org_request_time":"1755771712229","org_request_id":"1755771712229C0906","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755771713710S0000","serialNo":"01610040202307060227"}
2025-08-21 18:21:52.761  4750-4816  WebSocket               com.dspread.mdm.service              I  🔧 收到响应消息: transCode=S0000, orgRequestId=1755771712229C0906, state=0, remark=
2025-08-21 18:21:52.764  4750-4816  WebSocket               com.dspread.mdm.service              I  🔧 处理设备信息响应: state=0
2025-08-21 18:21:52.766  4750-4816  WebSocket               com.dspread.mdm.service              I  🔧 设备信息上传成功
2025-08-21 18:21:54.397  4750-4816  Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"request_time":"1755771715343","org_request_time":"1755771712418","org_request_id":"1755771712418C0109","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755771715343S0000","serialNo":"01610040202307060227"}
2025-08-21 18:21:54.399  4750-4816  WebSocket               com.dspread.mdm.service              I  🔧 收到响应消息: transCode=S0000, orgRequestId=1755771712418C0109, state=0, remark=
2025-08-21 18:21:54.401  4750-4816  WebSocket               com.dspread.mdm.service              I  🔧 终端信息上传成功
2025-08-21 18:21:58.846  4750-4750  Receiver                com.dspread.mdm.service              D  🔧 网络恢复，重新上传网络状态信息（防抖通过）
2025-08-21 18:21:58.853  4750-4750  WebSocket               com.dspread.mdm.service              I  🔧 C0904 被动式上送拒绝: NETWORK_RECOVERY (拒绝: 1)
2025-08-21 18:21:58.855  4750-4750  WebSocket               com.dspread.mdm.service              I  🔧 C0904 网络状态上送被流量控制阻止: 被动事件 'NETWORK_RECOVERY' 在平衡模式 - 重要变化下未启用
2025-08-21 18:22:16.106  4750-4817  WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 1 (第1个，待响应: 1)
2025-08-21 18:22:16.507  4750-4816  WebSocket               com.dspread.mdm.service              I  🔧 收到服务器 PONG 帧: 1 (待响应PING: 0)
2025-08-21 18:22:46.107  4750-4817  WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 2 (第2个，待响应: 1)
2025-08-21 18:22:46.715  4750-4816  WebSocket               com.dspread.mdm.service              I  🔧 收到服务器 PONG 帧: 2 (待响应PING: 0)
2025-08-21 18:23:16.108  4750-4817  WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 3 (第3个，待响应: 1)
2025-08-21 18:23:16.514  4750-4816  WebSocket               com.dspread.mdm.service              I  🔧 收到服务器 PONG 帧: 3 (待响应PING: 0)
2025-08-21 18:23:46.109  4750-4817  WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 4 (第4个，待响应: 1)
2025-08-21 18:23:46.722  4750-4816  WebSocket               com.dspread.mdm.service              I  🔧 收到服务器 PONG 帧: 4 (待响应PING: 0)
