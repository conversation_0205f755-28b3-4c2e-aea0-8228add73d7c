2025-08-21 18:03:44.567   947-998   PackageManager          system_server                        E  Adding duplicate shared id: 1000 name=com.dspread.mdm.service
2025-08-21 18:03:45.356  4562-4562  ead.mdm.service         com.dspread.mdm.service              W  type=1400 audit(0.0:521): avc: denied { write } for name="com.dspread.mdm.service-DkUVUdVMrOqb7juLWMNc3Q==" dev="dm-6" ino=15354 scontext=u:r:system_app:s0 tcontext=u:object_r:apk_data_file:s0 tclass=dir permissive=0
2025-08-21 18:03:46.052  4562-4562  NetworkSecurityConfig   com.dspread.mdm.service              D  Using Network Security Config from resource network_security_config debugBuild: true
2025-08-21 18:03:46.056  4562-4562  NetworkSecurityConfig   com.dspread.mdm.service              D  Using Network Security Config from resource network_security_config debugBuild: true
2025-08-21 18:03:46.145  4562-4562  Common                  com.dspread.mdm.service              D  🔧 日志配置：生产环境模式
2025-08-21 18:03:46.149  4562-4562  Common                  com.dspread.mdm.service              D  🔧 [DebugConfig] 定时器调试模式已禁用
2025-08-21 18:03:46.152  4562-4562  Common                  com.dspread.mdm.service              D  🔧 TimerConfig ========== 当前定时器配置 (生产模式) ==========
2025-08-21 18:03:46.162  4562-4562  Provisioning            com.dspread.mdm.service              D  🔧 主目录 已存在: /data/pos/config
2025-08-21 18:03:46.165  4562-4562  Provisioning            com.dspread.mdm.service              I  ℹ️ 使用主配置目录: /data/pos/config/
2025-08-21 18:03:46.167  4562-4562  Provisioning            com.dspread.mdm.service              I  ℹ️ 配置路径初始化完成: 系统目录
2025-08-21 18:03:46.176  4562-4562  Provisioning            com.dspread.mdm.service              D  🔧 状态标志加载成功: /data/pos/config/provisioning_flags.json
2025-08-21 18:03:46.188  4562-4562  Common                  com.dspread.mdm.service              D  🔧 TimerConfig 使用Provisioning配置的心跳间隔: 300秒
2025-08-21 18:03:46.191  4562-4562  Common                  com.dspread.mdm.service              D  🔧 TimerConfig ①心跳定时器: 300秒
2025-08-21 18:03:46.196  4562-4562  Common                  com.dspread.mdm.service              D  🔧 TimerConfig 使用Provisioning配置的终端信息上传间隔: 900秒
2025-08-21 18:03:46.198  4562-4562  Common                  com.dspread.mdm.service              D  🔧 TimerConfig ②终端信息上传: 900秒
2025-08-21 18:03:46.201  4562-4562  Common                  com.dspread.mdm.service              D  🔧 TimerConfig ③任务执行: 60秒
2025-08-21 18:03:46.203  4562-4562  Common                  com.dspread.mdm.service              D  🔧 TimerConfig ④服务守护: 120秒
2025-08-21 18:03:46.206  4562-4562  Common                  com.dspread.mdm.service              D  🔧 TimerConfig ⑤Provisioning: 43200秒
2025-08-21 18:03:46.208  4562-4562  Common                  com.dspread.mdm.service              D  🔧 TimerConfig ===============================================
2025-08-21 18:03:46.210  4562-4562  Common                  com.dspread.mdm.service              D  🔧 日志配置：生产环境模式
2025-08-21 18:03:46.212  4562-4562  Common                  com.dspread.mdm.service              D  🔧 [DebugConfig] 调试模式已禁用
2025-08-21 18:03:46.214  4562-4562  Common                  com.dspread.mdm.service              D  🔧 [DebugConfig] ========== 当前日志配置 ==========
2025-08-21 18:03:46.311  4562-4562  Common                  com.dspread.mdm.service              D  🔧 [DebugConfig]             ========== 统一配置状态 (生产模式) ==========
                                                                                                    
                                                                                                                LogStream配置 (生产模式):
                                                                                                    启用状态: true
                                                                                                    单个日志文件: 5MB
                                                                                                    压缩文件总限制: 250MB
                                                                                                    原始日志总限制: 250MB
                                                                                                    Recent日志大小: 128KB
                                                                                                    上传URL: https://smartms-applog.s3.sa-east-1.amazonaws.com/log/
                                                                                                    
                                                                                                                Provisioning配置 (生产模式):
                                                                                                    配置目录: /data/pos/config/
                                                                                                    媒体目录: /data/pos/media/
                                                                                                    Logo目录: /data/pos/media/logo/
                                                                                                    开机动画目录: /data/pos/media/bootanimation/
                                                                                                    
                                                                                                                ========== 日志配置状态 ==========
                                                                                                    全局开关: true
                                                                                                    
                                                                                                    模块开关:
                                                                                                      Common: true
                                                                                                      HTTPS: true
                                                                                                      WebSocket: true
                                                                                                      API: true
                                                                                                      Service: true
                                                                                                      Provisioning: true
                                                                                                      Task: true
                                                                                                      RuleBase: true
                                                                                                      Application: true
                                                                                                      LogStream: false
                                                                                                      RemoteView: false
                                                                                                      Wi-Fi: false
                                                                                                      APN: false
                                                                                                      GeoFence: false
                                                                                                      Receiver: true
                                                                                                    ================================
                                                                                                                =========================================
2025-08-21 18:03:46.313  4562-4562  Common                  com.dspread.mdm.service              D  🔧 [DebugConfig] =====================================
2025-08-21 18:03:46.315  4562-4562  Common                  com.dspread.mdm.service              D  🔧 [DebugConfig] 统一配置管理器初始化完成，当前模式: 生产模式
2025-08-21 18:03:46.318  4562-4562  Common                  com.dspread.mdm.service              D  🔧 [DebugConfig] 定时器调试模式已启用
2025-08-21 18:03:46.321  4562-4562  Common                  com.dspread.mdm.service              D  🔧 TimerConfig ========== 当前定时器配置 (调试模式) ==========
2025-08-21 18:03:46.323  4562-4562  Common                  com.dspread.mdm.service              D  🔧 TimerConfig ①心跳定时器: 60秒
2025-08-21 18:03:46.326  4562-4562  Common                  com.dspread.mdm.service              D  🔧 TimerConfig ②终端信息上传: 120秒
2025-08-21 18:03:46.328  4562-4562  Common                  com.dspread.mdm.service              D  🔧 TimerConfig ③任务执行: 60秒
2025-08-21 18:03:46.330  4562-4562  Common                  com.dspread.mdm.service              D  🔧 TimerConfig ④服务守护: 120秒
2025-08-21 18:03:46.333  4562-4562  Common                  com.dspread.mdm.service              D  🔧 TimerConfig ⑤Provisioning: 300秒
2025-08-21 18:03:46.335  4562-4562  Common                  com.dspread.mdm.service              D  🔧 TimerConfig ===============================================
2025-08-21 18:03:46.337  4562-4562  Common                  com.dspread.mdm.service              D  🔧 日志配置：测试环境模式
2025-08-21 18:03:46.339  4562-4562  Common                  com.dspread.mdm.service              D  🔧 [DebugConfig] 调试模式已启用
2025-08-21 18:03:46.341  4562-4562  Common                  com.dspread.mdm.service              D  🔧 [DebugConfig] ========== 当前日志配置 ==========
2025-08-21 18:03:46.361  4562-4562  Common                  com.dspread.mdm.service              D  🔧 [DebugConfig]             ========== 统一配置状态 (调试模式) ==========
                                                                                                    
                                                                                                                LogStream配置 (调试模式):
                                                                                                    启用状态: true
                                                                                                    单个日志文件: 100KB
                                                                                                    压缩文件总限制: 10MB
                                                                                                    原始日志总限制: 10MB
                                                                                                    Recent日志大小: 1KB
                                                                                                    上传URL: https://smartms-applog.s3.sa-east-1.amazonaws.com/log/
                                                                                                    
                                                                                                                Provisioning配置 (调试模式):
                                                                                                    配置目录: /sdcard/Android/data/com.dspread.mdm.service/files/config/
                                                                                                    媒体目录: /sdcard/Android/data/com.dspread.mdm.service/files/media/
                                                                                                    Logo目录: /sdcard/Android/data/com.dspread.mdm.service/files/media/logo/
                                                                                                    开机动画目录: /sdcard/Android/data/com.dspread.mdm.service/files/media/bootanimation/
                                                                                                    
                                                                                                                ========== 日志配置状态 ==========
                                                                                                    全局开关: true
                                                                                                    
                                                                                                    模块开关:
                                                                                                      Common: true
                                                                                                      HTTPS: true
                                                                                                      WebSocket: true
                                                                                                      API: true
                                                                                                      Service: true
                                                                                                      Provisioning: true
                                                                                                      Task: true
                                                                                                      RuleBase: true
                                                                                                      Application: true
                                                                                                      LogStream: false
                                                                                                      RemoteView: false
                                                                                                      Wi-Fi: false
                                                                                                      APN: false
                                                                                                      GeoFence: false
                                                                                                      Receiver: true
                                                                                                    ================================
                                                                                                                =========================================
2025-08-21 18:03:46.363  4562-4562  Common                  com.dspread.mdm.service              D  🔧 [DebugConfig] =====================================
2025-08-21 18:03:46.365  4562-4562  Common                  com.dspread.mdm.service              D  🔧 SmartMdmServiceApp: 统一配置管理器初始化完成
2025-08-21 18:03:46.367  4562-4562  Common                  com.dspread.mdm.service              D  🔧 SmartMdmServiceApp: 开始初始化系统目录...
2025-08-21 18:03:46.369  4562-4562  Common                  com.dspread.mdm.service              D  🔧 SmartMdmServiceApp: 配置目录已存在 - /data/pos/config
2025-08-21 18:03:46.373  4562-4562  Common                  com.dspread.mdm.service              D  🔧 SmartMdmServiceApp: Logo目录创建 - 路径: /data/data/com.dspread.mdm.service/files/media/logo, 结果: true
2025-08-21 18:03:46.376  4562-4562  Common                  com.dspread.mdm.service              D  🔧 SmartMdmServiceApp: 开机动画目录创建 - 路径: /data/data/com.dspread.mdm.service/files/media/anim, 结果: true
2025-08-21 18:03:46.379  4562-4562  Common                  com.dspread.mdm.service              D  🔧 SmartMdmServiceApp: 系统目录初始化完成
2025-08-21 18:03:46.384  4562-4562  Platform                com.dspread.mdm.service              D  🔧 NetworkTrafficMonitor 初始化网络流量监控器
2025-08-21 18:03:46.388  4562-4562  Platform                com.dspread.mdm.service              D  🔧 NetworkTrafficMonitor 新的一天，重置流量统计
2025-08-21 18:03:46.390  4562-4562  Platform                com.dspread.mdm.service              D  🔧 NetworkTrafficMonitor 流量统计已重置
2025-08-21 18:03:46.393  4562-4562  Platform                com.dspread.mdm.service              D  🔧 NetworkTrafficMonitor 检测到日期变化:  -> 2025-08-21
2025-08-21 18:03:46.395  4562-4562  Platform                com.dspread.mdm.service              D  🔧 NetworkTrafficMonitor 流量统计已重置
2025-08-21 18:03:46.456  4562-4562  Platform                com.dspread.mdm.service              D  🔧 NetworkTrafficMonitor 每日上送任务已启动
2025-08-21 18:03:46.458  4562-4562  Platform                com.dspread.mdm.service              D  🔧 NetworkTrafficMonitor 网络流量监控器初始化完成
2025-08-21 18:03:46.464  4562-4562  Receiver                com.dspread.mdm.service              D  🔧 ServiceGuardEventHandler 启动服务守护定时器: 120s
2025-08-21 18:03:46.470  4562-4562  Common                  com.dspread.mdm.service              I  ✅ ⏰ 设置服务守护定时器成功，下次执行: 120秒后 (2分钟)
2025-08-21 18:03:46.472  4562-4562  Common                  com.dspread.mdm.service              D  🔧 SmartMdmServiceApp: 服务守护定时器启动成功
2025-08-21 18:03:46.477  4562-4562  Common                  com.dspread.mdm.service              D  🔧 ServiceStartupManager 收到服务启动请求: APPLICATION_STARTUP, 强制启动: false
2025-08-21 18:03:46.483  4562-4562  Common                  com.dspread.mdm.service              D  🔧 ServiceStartupManager 开始执行服务启动: APPLICATION_STARTUP
2025-08-21 18:03:46.485  4562-4562  Common                  com.dspread.mdm.service              D  🔧 ServiceManager: 准备启动后台服务
2025-08-21 18:03:46.488  4562-4562  ContextImpl             com.dspread.mdm.service              W  Calling a method in the system process without a qualified user: android.app.ContextImpl.startService:1669 android.content.ContextWrapper.startService:720 com.dspread.mdm.service.platform.manager.ServiceManager.startBackgroundService:20 com.dspread.mdm.service.platform.manager.ServiceStartupManager.performServiceStart:121 com.dspread.mdm.service.platform.manager.ServiceStartupManager.startService:66 
2025-08-21 18:03:46.491  4562-4562  Common                  com.dspread.mdm.service              D  🔧 ServiceManager: 后台服务启动成功，ComponentName: ComponentInfo{com.dspread.mdm.service/com.dspread.mdm.service.services.SmartMdmBackgroundService}
2025-08-21 18:03:47.522  4562-4562  Common                  com.dspread.mdm.service              D  🔧 ServiceStartupManager 服务启动验证成功: APPLICATION_STARTUP
2025-08-21 18:03:47.524  4562-4562  Common                  com.dspread.mdm.service              D  🔧 ServiceStartupManager 服务启动成功: APPLICATION_STARTUP
2025-08-21 18:03:47.526  4562-4562  Common                  com.dspread.mdm.service              D  🔧 SmartMdmServiceApp: 主服务启动请求已提交
2025-08-21 18:03:47.528  4562-4562  Common                  com.dspread.mdm.service              D  🔧 SmartMdmServiceApp: Application启动完成，包名: com.dspread.mdm.service
2025-08-21 18:03:47.646  4562-4562  SurfaceFactory          com.dspread.mdm.service              I  [static] sSurfaceFactory = com.mediatek.view.impl.SurfaceFactoryImpl@8896962
2025-08-21 18:03:47.656  4562-4562  ViewRootIm...tActivity] com.dspread.mdm.service              D  hardware acceleration = true , fakeHwAccelerated = false, sRendererDisabled = false, forceHwAccelerated = false, sSystemRendererDisabled = false
2025-08-21 18:03:47.664  4562-4562  InputTransport          com.dspread.mdm.service              I  Create ARC handle: 0xa4e93290
2025-08-21 18:03:47.666  4562-4562  PhoneWindow             com.dspread.mdm.service              V  DecorView setVisiblity: visibility = 0, Parent = android.view.ViewRootImpl@718b9ae, this = DecorView@8ae434f[TestActivity]
2025-08-21 18:03:47.672  4562-4562  PhoneWindow             com.dspread.mdm.service              V  DecorView setVisiblity: visibility = 4, Parent = android.view.ViewRootImpl@718b9ae, this = DecorView@8ae434f[TestActivity]
2025-08-21 18:03:47.674  4562-4562  Choreographer           com.dspread.mdm.service              I  Skipped 93 frames!  The application may be doing too much work on its main thread.
2025-08-21 18:03:47.709  4562-4562  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 服务启动
2025-08-21 18:03:47.721  4562-4562  setContentIntent        com.dspread.mdm.service              D  packageName = com.dspread.mdm.service
2025-08-21 18:03:47.727  4562-4562  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 开始服务初始化
2025-08-21 18:03:47.729   947-975   NotificationService     system_server                        E  enqueueNotificationInternal pkg: com.dspread.mdm.service
2025-08-21 18:03:47.730  4562-4562  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] Android < 14: 启动Provisioning服务
2025-08-21 18:03:47.733  4562-4562  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 服务创建完成
2025-08-21 18:03:47.734  4562-4599  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 启动ProvisioningService
2025-08-21 18:03:47.737  4562-4562  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] onStartCommand调用
2025-08-21 18:03:47.739  4562-4599  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] ProvisioningService启动完成
2025-08-21 18:03:47.739  4562-4592  Provisioning            com.dspread.mdm.service              I  ℹ️ 开始执行Provisioning配置，触发类型: FIRST_BOOT
2025-08-21 18:03:47.742  4562-4592  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] Provisioning进度: 正在下载配置文件...
2025-08-21 18:03:47.744  4562-4592  Provisioning            com.dspread.mdm.service              I  ℹ️ 开始请求远程配置...
2025-08-21 18:03:47.744  4562-4562  Platform                com.dspread.mdm.service              D  🔧 WakeLockManager 获取智能WakeLock成功，持续时间: 60000ms
2025-08-21 18:03:47.750  4562-4562  Platform                com.dspread.mdm.service              D  🔧 WakeLockManager 设置WakeLock续期定时器: 60000ms
2025-08-21 18:03:47.759  4562-4592  Provisioning            com.dspread.mdm.service              I  ℹ️ 请求配置: https://config.dspreadserv.net/status/config?SN=MDE2MTAwNDAyMDIzMDcwNjAyMjc=&timestamp=1755770627758
2025-08-21 18:03:47.761  4562-4562  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 初始化基础组件
2025-08-21 18:03:47.764  4562-4562  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 开始初始化...
2025-08-21 18:03:47.769  4562-4592  System.out              com.dspread.mdm.service              I  [okhttp]:check permission begin!
2025-08-21 18:03:47.770  4562-4592  System                  com.dspread.mdm.service              W  ClassLoader referenced unknown path: system/framework/mediatek-cta.jar
2025-08-21 18:03:47.772  4562-4592  System.out              com.dspread.mdm.service              I  [okhttp] e:java.lang.ClassNotFoundException: com.mediatek.cta.CtaUtils
2025-08-21 18:03:47.773  4562-4562  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 包更新广播接收器注册完成
2025-08-21 18:03:47.777  4562-4562  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 统一广播接收器注册完成
2025-08-21 18:03:47.779  4562-4562  Common                  com.dspread.mdm.service              I  ✅ BroadcastManager 初始化完成
2025-08-21 18:03:47.785  4562-4562  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: NetworkEventHandler -> android.net.conn.CONNECTIVITY_CHANGE
2025-08-21 18:03:47.787  4562-4562  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: NetworkEventHandler -> android.net.wifi.STATE_CHANGE
2025-08-21 18:03:47.791  4562-4562  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: BatteryEventHandler -> android.intent.action.BATTERY_CHANGED
2025-08-21 18:03:47.793  4562-4562  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: BatteryEventHandler -> android.intent.action.BATTERY_LOW
2025-08-21 18:03:47.796  4562-4562  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: BatteryEventHandler -> android.intent.action.BATTERY_OKAY
2025-08-21 18:03:47.798  4562-4562  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: BatteryEventHandler -> android.intent.action.ACTION_POWER_CONNECTED
2025-08-21 18:03:47.801  4562-4562  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: BatteryEventHandler -> android.intent.action.ACTION_POWER_DISCONNECTED
2025-08-21 18:03:47.805  4562-4562  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: SystemEventHandler -> android.intent.action.BOOT_COMPLETED
2025-08-21 18:03:47.807  4562-4562  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: SystemEventHandler -> android.intent.action.QUICKBOOT_POWERON
2025-08-21 18:03:47.810  4562-4562  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: SystemEventHandler -> android.intent.action.LOCKED_BOOT_COMPLETED
2025-08-21 18:03:47.812  4562-4562  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: SystemEventHandler -> android.intent.action.ACTION_SHUTDOWN
2025-08-21 18:03:47.814  4562-4562  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: SystemEventHandler -> android.intent.action.REBOOT
2025-08-21 18:03:47.818  4562-4562  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: ScreenEventHandler -> android.intent.action.SCREEN_ON
2025-08-21 18:03:47.820  4562-4562  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: ScreenEventHandler -> android.intent.action.SCREEN_OFF
2025-08-21 18:03:47.825  4562-4562  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: HeartbeatEventHandler -> com.dspread.mdm.service.POLL_TIMER_START
2025-08-21 18:03:47.831  4562-4562  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: PackageUpdateEventHandler -> android.intent.action.PACKAGE_ADDED
2025-08-21 18:03:47.833  4562-4562  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: PackageUpdateEventHandler -> android.intent.action.PACKAGE_REMOVED
2025-08-21 18:03:47.835  4562-4562  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: PackageUpdateEventHandler -> android.intent.action.PACKAGE_REPLACED
2025-08-21 18:03:47.838  4562-4562  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: PackageUpdateEventHandler -> com.dspread.mdm.service.CHECK_SELF_UPDATE_STATUS
2025-08-21 18:03:47.844  4562-4562  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: TaskExecuteEventHandler -> com.dspread.mdm.service.WSTASK_EXEC_BC
2025-08-21 18:03:47.848  4562-4562  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: TerminalInfoEventHandler -> com.dspread.mdm.service.TER_INFO_UPLOAD_BC
2025-08-21 18:03:47.850  4562-4562  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: ServiceGuardEventHandler -> com.dspread.mdm.service.SERVICE_GUARD_TIMER
2025-08-21 18:03:47.854  4562-4562  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: ServiceManagementEventHandler -> android.intent.action.USER_PRESENT
2025-08-21 18:03:47.856  4562-4562  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: ServiceManagementEventHandler -> android.intent.action.TIME_TICK
2025-08-21 18:03:47.859  4562-4562  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: ServiceManagementEventHandler -> com.dspread.mdm.service.SERVICE_RESTART
2025-08-21 18:03:47.861  4562-4562  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: ServiceManagementEventHandler -> android.net.conn.CONNECTIVITY_CHANGE
2025-08-21 18:03:47.864  4562-4562  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: WakeLockEventHandler -> com.dspread.mdm.service.WAKELOCK_RENEWAL
2025-08-21 18:03:47.866  4562-4562  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: WakeLockEventHandler -> com.dspread.mdm.service.WAKELOCK_ACQUIRE
2025-08-21 18:03:47.869  4562-4562  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: WakeLockEventHandler -> com.dspread.mdm.service.WAKELOCK_RELEASE
2025-08-21 18:03:47.871  4562-4594  Platform                com.dspread.mdm.service              D  🔧 DeviceInfoApi 单例实例已创建
2025-08-21 18:03:47.874  4562-4562  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: ProvisioningEventHandler -> com.dspread.mdm.service.PROVISIONING_TIMER
2025-08-21 18:03:47.876  4562-4562  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: ProvisioningEventHandler -> android.net.conn.CONNECTIVITY_CHANGE
2025-08-21 18:03:47.879  4562-4562  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: ProvisioningEventHandler -> android.intent.action.BOOT_COMPLETED
2025-08-21 18:03:47.891  4562-4594  Platform                com.dspread.mdm.service              W  ⚠️ SpVersionApi SP版本信息格式异常，未找到SP_VERSION标记
2025-08-21 18:03:47.893  4562-4594  Platform                com.dspread.mdm.service              D  🔧 SpVersionApi 从Settings获取SP版本: STD 582.1.238.8
2025-08-21 18:03:47.920  4562-4562  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 服务初始化完成
2025-08-21 18:03:47.925  4562-4562  Platform                com.dspread.mdm.service              D  🔧 NetworkApi 单例实例已创建
2025-08-21 18:03:47.929  4562-4562  TetheringManager        com.dspread.mdm.service              I  registerTetheringEventCallback:com.dspread.mdm.service
2025-08-21 18:03:47.941  4562-4562  Receiver                com.dspread.mdm.service              D  🔧 网络状态变化: 连接=true, 类型=WIFI
2025-08-21 18:03:47.951  4562-4562  Receiver                com.dspread.mdm.service              D  🔧 网络恢复，尝试重新连接WebSocket
2025-08-21 18:03:47.954  4562-4562  Receiver                com.dspread.mdm.service              D  🔧 ServiceManagementEventHandler 处理网络变化事件
2025-08-21 18:03:47.959  4562-4562  Common                  com.dspread.mdm.service              D  🔧 ServiceStartupManager 服务正在运行，无需启动: NETWORK_CHANGE
2025-08-21 18:03:47.962  4562-4562  Receiver                com.dspread.mdm.service              D  🔧 ServiceManagementEventHandler 网络变化后服务检查完成
2025-08-21 18:03:47.964  4562-4562  Provisioning            com.dspread.mdm.service              D  🔧 ProvisioningEventHandler 网络状态变化
2025-08-21 18:03:47.966  4562-4562  Provisioning            com.dspread.mdm.service              D  🔧 ProvisioningEventHandler 忽略初始启动时的网络连接事件
2025-08-21 18:03:47.969  4562-4562  Receiver                com.dspread.mdm.service              D  🔧 电池状态更新: 电池状态: 电量=92%, 温度=27.7°C, 健康=2, 充电=是, 低电量=否, 耗尽=否
2025-08-21 18:03:47.971  4562-4562  Receiver                com.dspread.mdm.service              D  🔧 电池状态变化: 电量=92%, 温度=27°C, 充电=true
2025-08-21 18:03:47.994  4562-4562  WebSocket               com.dspread.mdm.service              I  🔧 C0902 被动式上送: battery_level_change_5 (被动: 1)
2025-08-21 18:03:48.013  4562-4562  WebSocket               com.dspread.mdm.service              I  🔧 消息已缓存: C0902 (缓存数量: 1)
2025-08-21 18:03:48.015  4562-4562  WebSocket               com.dspread.mdm.service              I  🔧 C0902 电池状态上送成功 (1, trigger=battery_level_change_5)
2025-08-21 18:03:48.018  4562-4562  WebSocket               com.dspread.mdm.service              I  🔧 C0905 主动式上送: manual_trigger (主动: 1)
2025-08-21 18:03:48.024  4562-4562  WebSocket               com.dspread.mdm.service              I  🔧 首次上送，上送当日数据
2025-08-21 18:03:48.027  4562-4562  WebSocket               com.dspread.mdm.service              I  🔧 没有流量数据需要上送
2025-08-21 18:03:48.029  4562-4562  Common                  com.dspread.mdm.service              D  🔧 刷新时自动上送流量统计
2025-08-21 18:03:48.124  4562-4592  System.out              com.dspread.mdm.service              I  [socket]:check permission begin!
2025-08-21 18:03:48.125  4562-4592  System                  com.dspread.mdm.service              W  ClassLoader referenced unknown path: system/framework/mediatek-cta.jar
2025-08-21 18:03:48.126  4562-4592  System.out              com.dspread.mdm.service              I  [socket] e:java.lang.ClassNotFoundException: com.mediatek.cta.CtaUtils
2025-08-21 18:03:49.225  4562-4592  System.out              com.dspread.mdm.service              I  [OkHttp] sendRequest>>
2025-08-21 18:03:49.226  4562-4592  System.out              com.dspread.mdm.service              I  [OkHttp] sendRequest<<
2025-08-21 18:03:50.480  4562-4592  Provisioning            com.dspread.mdm.service              I  ℹ️ 远程配置获取成功
2025-08-21 18:03:50.482  4562-4592  Provisioning            com.dspread.mdm.service              I  ℹ️ 远程配置请求成功
2025-08-21 18:03:50.484  4562-4592  Provisioning            com.dspread.mdm.service              I  ℹ️ 远程配置解析成功 - CID: 1001
2025-08-21 18:03:50.490  4562-4592  Provisioning            com.dspread.mdm.service              I  ℹ️ API响应已保存到本地: /data/pos/config/provisioning_config.json, 具体内容为:
                                                                                                    {"mode":"dev","data":{"requestTime":"1755770631343","customization":{"system":{"timezone":"Asia\/Hong_Kong","logo":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/test\/logo.bin","logoMd5":"ba1ee533924eae5c408465e7cddcbda4","bootAnimationMd5":"ba1ee533924eae5c408465e7cddcbda4","gps":{"minUpdateTime":"30","scheduleTime":"60","maxLocateTime":"0","minDistance":"10","valid_distance":"500","care":"1"},"powerSaveMode":{"enable":"1","screenTimeout":"0"},"bootAnimation":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/test\/bootanimation.zip"},"polling":{"heartbeatTime":"300","terminalInfoTime":"900","statusApiUrl":"wss:\/\/api.dspreadserv.net\/status\/websocket\/register","remoteUrl":"wss:\/\/remote.dspreadserv.net\/remoteWSS\/websockify","uploadMode":"1","wssreconn":{"pingInterval":"30","delayPolicy":"1","delaySwitch":"1","delayTime":"60"}}},"client":"default","cid":"1001"},"function":"getProvisioningConfig","description":"SUCCESS","stateCode":"0","version":"v1.0.1.20250801"}
2025-08-21 18:03:50.493  4562-4592  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] Provisioning进度: 正在下载 Logo...
2025-08-21 18:03:50.498  4562-4592  Provisioning            com.dspread.mdm.service              I  ℹ️ 文件不存在，开始下载: Logo
2025-08-21 18:03:50.500  4562-4592  Provisioning            com.dspread.mdm.service              I  ℹ️ 下载: https://siot-bucket01.s3.ap-northeast-1.amazonaws.com/test/logo.bin -> /data/data/com.dspread.mdm.service/files/media/logo/logo.bin
2025-08-21 18:03:50.508  4562-4592  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 尝试下载，第1次
2025-08-21 18:03:50.511  4562-4592  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 开始下载
2025-08-21 18:03:50.513  4562-4592  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader URL: https://siot-bucket01.s3.ap-northeast-1.amazonaws.com/test/logo.bin
2025-08-21 18:03:50.515  4562-4592  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 路径: /data/data/com.dspread.mdm.service/files/media/logo/logo.bin
2025-08-21 18:03:50.517  4562-4592  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 本地文件不存在，创建文件
2025-08-21 18:03:50.519  4562-4592  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader allFileLength=0, 需要从服务器获取文件大小
2025-08-21 18:03:50.524  4562-4592  System.out              com.dspread.mdm.service              I  [okhttp]:check permission begin!
2025-08-21 18:03:50.524  4562-4592  System.out              com.dspread.mdm.service              I  [okhttp]:not MMS!
2025-08-21 18:03:50.524  4562-4592  System.out              com.dspread.mdm.service              I  [okhttp]:not Email!
2025-08-21 18:03:50.889  4562-4592  System.out              com.dspread.mdm.service              I  [socket]:check permission begin!
2025-08-21 18:03:51.368  4562-4592  System.out              com.dspread.mdm.service              I  [OkHttp] sendRequest>>
2025-08-21 18:03:51.368  4562-4592  System.out              com.dspread.mdm.service              I  [OkHttp] sendRequest<<
2025-08-21 18:03:51.508  4562-4594  Platform                com.dspread.mdm.service              D  🔧 NetworkTrafficMonitor 等待下次上送时间: 2025-08-22 00:00:00
2025-08-21 18:03:52.016  4562-4592  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader HTTP响应码: 200
2025-08-21 18:03:52.018  4562-4592  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 服务器返回Content-Length: 4771451
2025-08-21 18:03:52.020  4562-4592  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 完整下载模式
2025-08-21 18:03:52.023  4562-4592  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 完整下载，使用服务器返回大小: 4771451
2025-08-21 18:03:52.025  4562-4592  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader localFile = true /data/data/com.dspread.mdm.service/files/media/logo/logo.bin, 追加模式: false
2025-08-21 18:03:53.477  4562-4609  ProfileInstaller        com.dspread.mdm.service              D  Installing profile for com.dspread.mdm.service
2025-08-21 18:03:59.119  4562-4562  Receiver                com.dspread.mdm.service              D  🔧 电池状态更新: 电池状态: 电量=92%, 温度=27.8°C, 健康=2, 充电=是, 低电量=否, 耗尽=否
2025-08-21 18:03:59.163  4562-4592  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 计算MD5: logo.bin
2025-08-21 18:03:59.230  4562-4592  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 文件: 4771451 MD5: ba1ee533924eae5c408465e7cddcbda4
2025-08-21 18:03:59.232  4562-4592  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader MD5校验正确
2025-08-21 18:03:59.234  4562-4592  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 清理连接资源
2025-08-21 18:03:59.236  4562-4592  Provisioning            com.dspread.mdm.service              I  ℹ️ Logo 下载完成: /data/data/com.dspread.mdm.service/files/media/logo/logo.bin
2025-08-21 18:03:59.238  4562-4592  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] Provisioning进度: 正在下载 BootAnimation...
2025-08-21 18:03:59.241  4562-4592  Provisioning            com.dspread.mdm.service              I  ℹ️ 文件不存在，开始下载: BootAnimation
2025-08-21 18:03:59.243  4562-4592  Provisioning            com.dspread.mdm.service              I  ℹ️ 下载: https://siot-bucket01.s3.ap-northeast-1.amazonaws.com/test/bootanimation.zip -> /data/data/com.dspread.mdm.service/files/media/anim/bootanimation.zip
2025-08-21 18:03:59.244  4562-4592  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 尝试下载，第1次
2025-08-21 18:03:59.246  4562-4592  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 开始下载
2025-08-21 18:03:59.248  4562-4592  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader URL: https://siot-bucket01.s3.ap-northeast-1.amazonaws.com/test/bootanimation.zip
2025-08-21 18:03:59.250  4562-4592  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 路径: /data/data/com.dspread.mdm.service/files/media/anim/bootanimation.zip
2025-08-21 18:03:59.252  4562-4592  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 本地文件不存在，创建文件
2025-08-21 18:03:59.254  4562-4592  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader allFileLength=0, 需要从服务器获取文件大小
2025-08-21 18:03:59.259  4562-4592  System.out              com.dspread.mdm.service              I  [okhttp]:check permission begin!
2025-08-21 18:03:59.259  4562-4592  System.out              com.dspread.mdm.service              I  [okhttp]:not MMS!
2025-08-21 18:03:59.259  4562-4592  System.out              com.dspread.mdm.service              I  [okhttp]:not Email!
2025-08-21 18:03:59.259  4562-4592  System.out              com.dspread.mdm.service              I  [OkHttp] sendRequest>>
2025-08-21 18:03:59.260  4562-4592  System.out              com.dspread.mdm.service              I  [OkHttp] sendRequest<<
2025-08-21 18:03:59.593  4562-4592  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader HTTP响应码: 200
2025-08-21 18:03:59.598  4562-4592  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 服务器返回Content-Length: 4771451
2025-08-21 18:03:59.600  4562-4592  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 完整下载模式
2025-08-21 18:03:59.602  4562-4592  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 完整下载，使用服务器返回大小: 4771451
2025-08-21 18:03:59.603  4562-4592  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader localFile = true /data/data/com.dspread.mdm.service/files/media/anim/bootanimation.zip, 追加模式: false
2025-08-21 18:04:06.076  4562-4592  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 计算MD5: bootanimation.zip
2025-08-21 18:04:06.152  4562-4592  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 文件: 4771451 MD5: ba1ee533924eae5c408465e7cddcbda4
2025-08-21 18:04:06.155  4562-4592  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader MD5校验正确
2025-08-21 18:04:06.156  4562-4592  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 清理连接资源
2025-08-21 18:04:06.158  4562-4592  Provisioning            com.dspread.mdm.service              I  ℹ️ BootAnimation 下载完成: /data/data/com.dspread.mdm.service/files/media/anim/bootanimation.zip
2025-08-21 18:04:06.161  4562-4592  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] Provisioning进度: 正在应用配置...
2025-08-21 18:04:06.163  4562-4592  Provisioning            com.dspread.mdm.service              I  ℹ️ 配置应用完成
2025-08-21 18:04:06.165  4562-4592  Provisioning            com.dspread.mdm.service              I  ℹ️ 远程配置成功，标记首次配置完成
2025-08-21 18:04:06.167  4562-4592  Provisioning            com.dspread.mdm.service              D  🔧 状态标志保存成功: /data/pos/config/provisioning_flags.json
2025-08-21 18:04:06.169  4562-4592  Provisioning            com.dspread.mdm.service              I  ℹ️ Provisioning配置执行完成
2025-08-21 18:04:06.171  4562-4592  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] Provisioning完成，启动核心组件
2025-08-21 18:04:06.173  4562-4562  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 开始初始化核心组件
2025-08-21 18:04:06.184  4562-4562  WebSocket               com.dspread.mdm.service              I  🔧 开始初始化 WebSocket 组件...
2025-08-21 18:04:06.188  4562-4562  WebSocket               com.dspread.mdm.service              I  🔧 初始化 WebSocket 密钥管理器
2025-08-21 18:04:06.190  4562-4562  WebSocket               com.dspread.mdm.service              I  🔧 生成客户端公私钥对
2025-08-21 18:04:06.311  4562-4562  Common                  com.dspread.mdm.service              I  ✅ WebSocket 密钥管理器初始化完成
2025-08-21 18:04:06.318  4562-4562  WebSocket               com.dspread.mdm.service              I  🔧 开始初始化 WsMessageCenter...
2025-08-21 18:04:06.319  4562-4562  WebSocket               com.dspread.mdm.service              I  🔧 WsMessageCenter 首次初始化
2025-08-21 18:04:06.335  4562-4562  WebSocket               com.dspread.mdm.service              I  🔧 WsTaskManager 初始化完成
2025-08-21 18:04:06.438  4562-4562  WebSocket               com.dspread.mdm.service              I  🔧 WsMessageCenter 初始化完成
2025-08-21 18:04:06.440  4562-4562  WebSocket               com.dspread.mdm.service              I  🔧 开始清理终态任务，当前任务数量: 0
2025-08-21 18:04:06.442  4562-4562  WebSocket               com.dspread.mdm.service              I  🔧 没有需要清理的终态任务
2025-08-21 18:04:06.446  4562-4562  WebSocket               com.dspread.mdm.service              I  🔧 开始重置任务状态，当前任务个数: 0
2025-08-21 18:04:06.448  4562-4562  WebSocket               com.dspread.mdm.service              I  🔧 没有需要重置的任务
2025-08-21 18:04:06.450  4562-4562  Common                  com.dspread.mdm.service              I  ✅ 任务状态恢复完成
2025-08-21 18:04:06.458  4562-4562  WebSocket               com.dspread.mdm.service              I  🔧 使用Provisioning配置的WebSocket URL: wss://api.dspreadserv.net/status/websocket/register
2025-08-21 18:04:06.461  4562-4562  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接管理器初始化完成
2025-08-21 18:04:06.464  4562-4562  Common                  com.dspread.mdm.service              I  ✅ WebSocket 管理器初始化完成
2025-08-21 18:04:06.466  4562-4562  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 管理器初始化完成
2025-08-21 18:04:06.469  4562-4562  Common                  com.dspread.mdm.service              I  ✅ WebSocket 组件初始化完成
2025-08-21 18:04:06.471  4562-4562  WebSocket               com.dspread.mdm.service              I  🔧 启动 WebSocket 连接...
2025-08-21 18:04:06.473  4562-4562  WebSocket               com.dspread.mdm.service              I  🔧 开始连接 WebSocket...
2025-08-21 18:04:06.485  4562-4562  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接到: wss://api.dspreadserv.net/status/websocket/register?serialNo=MDE2MTAwNDAyMDIzMDcwNjAyMjc=&publicKey=TUlHZk1BMEdDU3FHU0liM0RRRUJBUVVBQTRHTkFEQ0JpUUtCZ1FDNDBKNExpenYzRytvT3UzeUZmRW1rWE4rV21YR0hQRE5jK1RXbkNkTUdoT2JoaVBDN1Jvc0JmR21WNTViT3hVOGhLWmxvODdVV25JYlgrQkFKZWFkNEgvdWNkYzRzTSt0bHJaYnF3b2hsL1k2VVJwbjh2MWtDYVJKanhPdVV0Q0hvbTFyelUzL0Mxdk9WZjN2TXNDYWtFNlBMc2ZQVnYybUp5UVFZLzNIdllRSURBUUFC&query=1&msgVer=3&timestamp=1755770646476&signature=GfCOAiQgtc2dGiq6/9Fqbl7HU73wJIbuykNFamUDn9dUTomay9i0C6pAKVZU9Qx3/82ehWbU31vDG4vtfRCLTTmu4dFP5BPwA5u+g7bnMBtxLN4XA74f/58bH6iXpzKbFtrIURZiWW/J6cv2ZvDSZz1PL4b1pyiJZzsvVhc+6xQ=
2025-08-21 18:04:06.489  4562-4562  WebSocket               com.dspread.mdm.service              I  🔧 未检测到系统代理设置
2025-08-21 18:04:06.508  4562-4562  WebSocket               com.dspread.mdm.service              I  🔧 ===== WebSocket 库默认配置 =====
2025-08-21 18:04:06.511  4562-4562  WebSocket               com.dspread.mdm.service              I  🔧 默认 PING 间隔: 60000ms (60秒)
2025-08-21 18:04:06.513  4562-4562  WebSocket               com.dspread.mdm.service              I  🔧 默认 PONG 间隔: 0ms (0秒)
2025-08-21 18:04:06.515  4562-4562  WebSocket               com.dspread.mdm.service              I  🔧 帧队列大小: 5
2025-08-21 18:04:06.517  4562-4562  WebSocket               com.dspread.mdm.service              I  🔧 连接超时: 15000ms (15秒)
2025-08-21 18:04:06.518  4562-4562  WebSocket               com.dspread.mdm.service              I  🔧 允许缺失关闭帧: false
2025-08-21 18:04:06.520  4562-4562  WebSocket               com.dspread.mdm.service              I  🔧 ================================
2025-08-21 18:04:06.524  4562-4562  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接请求已发送
2025-08-21 18:04:06.526  4562-4562  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接启动完成
2025-08-21 18:04:06.532  4562-4562  Common                  com.dspread.mdm.service              D  🔧 [ModuleManagerRegistry] 模块管理器注册中心初始化完成
2025-08-21 18:04:06.541  4562-4617  System.out              com.dspread.mdm.service              I  [socket]:check permission begin!
2025-08-21 18:04:06.547  4562-4562  RuleBase                com.dspread.mdm.service              D  🔧 RuleBaseManager 开始初始化RuleBaseManager...
2025-08-21 18:04:06.553  4562-4562  RuleBase                com.dspread.mdm.service              D  🔧 RuleBaseStorage RuleBase存储管理器初始化完成
2025-08-21 18:04:06.555  4562-4562  RuleBase                com.dspread.mdm.service              D  🔧 RuleStateMonitor 状态监控器初始化完成
2025-08-21 18:04:06.557  4562-4562  RuleBase                com.dspread.mdm.service              D  🔧 RuleBaseManager RuleBaseManager初始化完成
2025-08-21 18:04:06.558  4562-4562  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] RuleBaseManager初始化成功
2025-08-21 18:04:06.561  4562-4562  Provisioning            com.dspread.mdm.service              D  🔧 ProvisioningEventHandler 启动Provisioning定时器: 300s (0小时)
2025-08-21 18:04:06.561  4562-4592  Common                  com.dspread.mdm.service              D  🔧 [ModuleManagerRegistry] 启动所有模块
2025-08-21 18:04:06.566  4562-4562  Common                  com.dspread.mdm.service              I  ✅ ⏰ 设置初始化定时器成功，下次执行: 300秒后 (0小时)
2025-08-21 18:04:06.568  4562-4562  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] Provisioning定时器启动成功
2025-08-21 18:04:06.571  4562-4562  Common                  com.dspread.mdm.service              D  🔧 TimerConfig ========== 当前定时器配置 (调试模式) ==========
2025-08-21 18:04:06.571  4562-4592  Common                  com.dspread.mdm.service              D  🔧 [ModuleManagerRegistry] LogStreamManager实例创建完成
2025-08-21 18:04:06.573  4562-4562  Common                  com.dspread.mdm.service              D  🔧 TimerConfig ①心跳定时器: 60秒
2025-08-21 18:04:06.574  4562-4592  Common                  com.dspread.mdm.service              D  🔧 Module LogStream Manager status changed to: INITIALIZED
2025-08-21 18:04:06.575  4562-4562  Common                  com.dspread.mdm.service              D  🔧 TimerConfig ②终端信息上传: 120秒
2025-08-21 18:04:06.577  4562-4562  Common                  com.dspread.mdm.service              D  🔧 TimerConfig ③任务执行: 60秒
2025-08-21 18:04:06.580  4562-4562  Common                  com.dspread.mdm.service              D  🔧 TimerConfig ④服务守护: 120秒
2025-08-21 18:04:06.580  4562-4592  Common                  com.dspread.mdm.service              D  🔧 [ModuleManagerRegistry] LogStreamManager初始化成功
2025-08-21 18:04:06.582  4562-4562  Common                  com.dspread.mdm.service              D  🔧 TimerConfig ⑤Provisioning: 300秒
2025-08-21 18:04:06.582  4562-4592  Common                  com.dspread.mdm.service              D  🔧 Module LogStream Manager status changed to: STARTING
2025-08-21 18:04:06.584  4562-4562  Common                  com.dspread.mdm.service              D  🔧 TimerConfig ===============================================
2025-08-21 18:04:06.586  4562-4562  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 核心组件初始化完成
2025-08-21 18:04:06.640  4562-4592  Common                  com.dspread.mdm.service              D  🔧 Module LogStream Manager status changed to: RUNNING
2025-08-21 18:04:06.642  4562-4592  Common                  com.dspread.mdm.service              D  🔧 [ModuleManagerRegistry] LogStreamManager启动成功
2025-08-21 18:04:06.644  4562-4592  Common                  com.dspread.mdm.service              D  🔧 [ModuleManagerRegistry] LogStreamManager启动成功
2025-08-21 18:04:06.646  4562-4592  Common                  com.dspread.mdm.service              D  🔧 [ModuleManagerRegistry] 开始初始化地理围栏管理器
2025-08-21 18:04:06.675  4562-4592  Common                  com.dspread.mdm.service              D  🔧 Module Geofence Manager status changed to: INITIALIZED
2025-08-21 18:04:06.678  4562-4592  Common                  com.dspread.mdm.service              D  🔧 Module Geofence Manager status changed to: STARTING
2025-08-21 18:04:06.698  4562-4592  Platform                com.dspread.mdm.service              D  🔧 用户交互监控已启动（简化版本）
2025-08-21 18:04:06.700  4562-4592  Platform                com.dspread.mdm.service              D  🔧 使用模拟用户交互检测
2025-08-21 18:04:06.701  4562-4562  Platform                com.dspread.mdm.service              D  🔧 [WiFiManagerApi] WiFiManagerApi 单例实例已创建
2025-08-21 18:04:06.701  4562-4592  Platform                com.dspread.mdm.service              D  🔧 用户交互监控已启动
2025-08-21 18:04:06.703  4562-4592  Service                 com.dspread.mdm.service              D  🔧 [GeofenceManager]Constants.geofenceStatus = 1
2025-08-21 18:04:06.705  4562-4592  Service                 com.dspread.mdm.service              D  🔧 [GeofenceManager]-- Constants.storeId =  -- Constants.storeSsid =  -- Constants.storeIp = 
2025-08-21 18:04:06.707  4562-4592  Common                  com.dspread.mdm.service              D  🔧 Module Geofence Manager status changed to: RUNNING
2025-08-21 18:04:06.709  4562-4592  Common                  com.dspread.mdm.service              D  🔧 [ModuleManagerRegistry] 地理围栏管理器初始化成功
2025-08-21 18:04:06.711  4562-4592  Common                  com.dspread.mdm.service              D  🔧 [ModuleManagerRegistry] GeofenceManager启动成功
2025-08-21 18:04:06.712  4562-4592  Common                  com.dspread.mdm.service              D  🔧 [ModuleManagerRegistry] 所有模块启动完成
2025-08-21 18:04:06.791  4562-4618  System.out              com.dspread.mdm.service              I  [socket]:check permission begin!
2025-08-21 18:04:08.921  4562-4627  Common                  com.dspread.mdm.service              I  ✅ WebSocket 物理连接成功，等待服务器 hellMsg
2025-08-21 18:04:08.924  4562-4627  WebSocket               com.dspread.mdm.service              I  🔧 ===== 心跳发送器实际配置 =====
2025-08-21 18:04:08.927  4562-4627  WebSocket               com.dspread.mdm.service              I  🔧 实际 PING 发送间隔: 60000ms (60秒)
2025-08-21 18:04:08.930  4562-4627  WebSocket               com.dspread.mdm.service              I  🔧 PING 定时器名称: PingSender
2025-08-21 18:04:08.933  4562-4627  WebSocket               com.dspread.mdm.service              I  🔧 实际 PONG 发送间隔: 0ms (0秒)
2025-08-21 18:04:08.936  4562-4627  WebSocket               com.dspread.mdm.service              I  🔧 PONG 定时器名称: PongSender
2025-08-21 18:04:08.938  4562-4627  WebSocket               com.dspread.mdm.service              I  🔧 ===============================
2025-08-21 18:04:08.973  4562-4627  Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"hellMsg":"hello 01610040202307060227","tranCode":"S0000","version":"1","rebootTime":"01:24:29","serialNo":"01610040202307060227","deviceStatus":"6"}
2025-08-21 18:04:08.976  4562-4627  Common                  com.dspread.mdm.service              I  ✅ 收到服务器 hellMsg，WebSocket 业务连接成功！
2025-08-21 18:04:08.979  4562-4627  Common                  com.dspread.mdm.service              I  ✅ WebSocket 业务连接成功！
2025-08-21 18:04:08.981  4562-4627  WebSocket               com.dspread.mdm.service              I  🔧 业务连接成功，重连计数已重置为 0
2025-08-21 18:04:08.987  4562-4627  Common                  com.dspread.mdm.service              I  ✅ ⏰ 设置任务执行定时器成功，下次执行: 60秒后 (1分钟)
2025-08-21 18:04:08.989  4562-4627  WebSocket               com.dspread.mdm.service              I  🔧 已启动任务执行定时器
2025-08-21 18:04:08.994  4562-4627  Common                  com.dspread.mdm.service              I  ✅ ⏰ 设置心跳定时器成功，下次执行: 60秒后 (1分钟)
2025-08-21 18:04:08.996  4562-4627  WebSocket               com.dspread.mdm.service              I  🔧 已启动心跳定时器
2025-08-21 18:04:09.001  4562-4627  Common                  com.dspread.mdm.service              I  ✅ ⏰ 设置终端信息定时器成功，下次执行: 120秒后 (2分钟)
2025-08-21 18:04:09.003  4562-4627  WebSocket               com.dspread.mdm.service              I  🔧 已启动终端信息上传定时器
2025-08-21 18:04:09.005  4562-4627  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接成功，检查自身更新任务状态
2025-08-21 18:04:09.008  4562-4627  ContextImpl             com.dspread.mdm.service              W  Calling a method in the system process without a qualified user: android.app.ContextImpl.sendBroadcast:1111 android.content.ContextWrapper.sendBroadcast:468 com.dspread.mdm.service.broadcast.core.BroadcastSender.sendBroadcast:99 com.dspread.mdm.service.broadcast.core.BroadcastSender.sendBroadcast:23 com.dspread.mdm.service.network.websocket.connection.WsConnectionManager.checkSelfUpdateTaskStatus:311 
2025-08-21 18:04:09.012   947-3862  ActivityManager         system_server                        E  Sending non-protected broadcast com.dspread.mdm.service.CHECK_SELF_UPDATE_STATUS from system 4562:com.dspread.mdm.service/1000 pkg com.dspread.mdm.service (Ask Gemini)
                                                                                                    java.lang.Throwable
                                                                                                    	at com.android.server.am.ActivityManagerService.checkBroadcastFromSystem(ActivityManagerService.java:16188)
                                                                                                    	at com.android.server.am.ActivityManagerService.broadcastIntentLocked(ActivityManagerService.java:16790)
                                                                                                    	at com.android.server.am.ActivityManagerService.broadcastIntentLocked(ActivityManagerService.java:16205)
                                                                                                    	at com.android.server.am.ActivityManagerService.broadcastIntentWithFeature(ActivityManagerService.java:17052)
                                                                                                    	at android.app.IActivityManager$Stub.onTransact(IActivityManager.java:2386)
                                                                                                    	at com.android.server.am.ActivityManagerService.onTransact(ActivityManagerService.java:3021)
                                                                                                    	at android.os.Binder.execTransactInternal(Binder.java:1154)
                                                                                                    	at android.os.Binder.execTransact(Binder.java:1123)
2025-08-21 18:04:09.013  4562-4627  Receiver                com.dspread.mdm.service              D  🔧 发送广播: com.dspread.mdm.service.CHECK_SELF_UPDATE_STATUS
2025-08-21 18:04:09.016  4562-4627  WebSocket               com.dspread.mdm.service              I  🔧 开始发送缓存消息，数量: 1
2025-08-21 18:04:09.019  4562-4627  WebSocket               com.dspread.mdm.service              I  🔧 C0902 主动式上送: cached_message_resend (主动: 2)
2025-08-21 18:04:09.021  4562-4562  Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 收到自身更新状态检查广播: com.dspread.mdm.service.CHECK_SELF_UPDATE_STATUS
2025-08-21 18:04:09.022  4562-4562  Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 开始执行自身更新任务状态检查
2025-08-21 18:04:09.024  4562-4562  Task                    com.dspread.mdm.service              D  🔧 获取待执行任务数量: 0
2025-08-21 18:04:09.026  4562-4562  Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 待执行任务数量: 0
2025-08-21 18:04:09.027  4562-4627  Common                  com.dspread.mdm.service              I  ✅ 缓存消息发送成功: C0902
2025-08-21 18:04:09.530  4562-4627  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接成功
2025-08-21 18:04:09.532  4562-4627  WebSocket               com.dspread.mdm.service              I  🔧 设备状态: 6
2025-08-21 18:04:09.534  4562-4627  WebSocket               com.dspread.mdm.service              I  🔧 检查首次连接标志: isFirstTimeWebSocketConnected=true
2025-08-21 18:04:09.536  4562-4627  WebSocket               com.dspread.mdm.service              I  🔧 首次WebSocket连接成功，发送初始化信息并执行初始化检查
2025-08-21 18:04:09.538  4562-4627  WebSocket               com.dspread.mdm.service              I  🔧 开始发送初始化信息（带流量控制）
2025-08-21 18:04:09.540  4562-4627  WebSocket               com.dspread.mdm.service              I  🔧 C0901 被动式上送: first_connection (被动: 2)
2025-08-21 18:04:09.544  4562-4627  Platform                com.dspread.mdm.service              D  🔧 重新收集应用信息
2025-08-21 18:04:09.617  4562-4627  AppManager              com.dspread.mdm.service              I  ℹ️ 应用信息获取完成: 总数120(系统119/用户1) 返回1个
2025-08-21 18:04:09.622  4562-4627  Platform                com.dspread.mdm.service              D  🔧 应用信息: 1 个应用
2025-08-21 18:04:09.632  4562-4627  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0901","serialNo":"01610040202307060227","request_time":"1755770649624","request_id":"1755770649624C0901","version":"1","data":{"apkInfo":[{"packName":"com.dspread.mdm.service","apkName":"service","versionCode":20,"versionName":"1.1.01.20250821.DSPREAD.MDM.SERVICE","updateDate":"2025-08-21 18:03:44"}],"sytemInfo":{"androidVersion":"11","buildNumber":"D60_Baseline_debug_20250708_ota_test","aspl":"2021-12-05"},"serviceInfo":[]},"myVersionName":"1.1.01.20250821.DSPREAD.MDM.SERVICE","terminalDate":"20250821180409"}
2025-08-21 18:04:09.634  4562-4627  WebSocket               com.dspread.mdm.service              I  🔧 C0901 应用信息上送成功 (1)
2025-08-21 18:04:10.636  4562-4627  WebSocket               com.dspread.mdm.service              I  🔧 C0902 被动式上送: first_connection (被动: 3)
2025-08-21 18:04:10.650  4562-4627  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0902","serialNo":"01610040202307060227","request_time":"1755770650643","request_id":"1755770650643C0902","version":"1","data":{"batteryLife":92,"batteryHealth":2,"temprature":"27.8","isCharging":"1","isLowBattery":"0","powerOff":"0","outOfBattery":"0"},"myVersionName":"1.1.01.20250821.DSPREAD.MDM.SERVICE","terminalDate":"20250821180410"}
2025-08-21 18:04:10.652  4562-4627  WebSocket               com.dspread.mdm.service              I  🔧 C0902 电池状态上送成功 (1, trigger=first_connection)
2025-08-21 18:04:11.655  4562-4627  WebSocket               com.dspread.mdm.service              I  🔧 C0903 被动式上送: first_connection (被动: 4)
2025-08-21 18:04:11.687  4562-4627  Platform                com.dspread.mdm.service              D  🔧 内存总容量为： 1.86GB 
2025-08-21 18:04:11.690  4562-4627  Platform                com.dspread.mdm.service              D  🔧 内存使用量为： 0.54GB 
2025-08-21 18:04:11.804  4562-4627  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0903","serialNo":"01610040202307060227","request_time":"1755770651790","request_id":"1755770651790C0903","version":"1","data":{"hardwareInfo":[{"name":"CPU Usage","desc":"cpu:0.50"},{"name":"storage_usage_status","desc":" 5.42GB \/ 32.00GB ","state":"1"},{"name":"total_memory","desc":" 0.54GB \/ 1.86GB ","state":"1"},{"name":"androidVersion","desc":"11","state":"1"},{"name":"network_status","desc":"wifi","state":"1"},{"name":"OSVer","desc":"11","state":"1"}],"locationInfo":{"gps":{"longitude":"-999.0","latitude":"-999.0","satelliteCount":0,"satelliteCountInUsed":0,"satelliteAverSNR":"0.00","distance":"0.0","lockStatus":"1","lockMeter":"200","proId":"","proName":""},"baseSite":{"MCC":"","MNC":"","CID":"-1","LAC":"-1","DBM":"-999","IMSI":"","ICCID":""},"wifi":{"SSID":"fubox_2.4G","IP":"**************","MAC":"52:C3:63:13:8C:EE","SSTH":"-25","BSSID":"7c:10:c9:d6:9f:10"},"address":"无法获取"}},"myVersionName":"1.1.01.20250821.DSPREAD.MDM.SERVICE","terminalDate":"20250821180411"}
2025-08-21 18:04:11.810  4562-4627  WebSocket               com.dspread.mdm.service              I  🔧 C0903 数据信息上送成功 (1)
2025-08-21 18:04:11.856  4562-4578  System                  com.dspread.mdm.service              W  A resource failed to call close. 
2025-08-21 18:04:12.817  4562-4627  WebSocket               com.dspread.mdm.service              I  🔧 C0904 被动式上送: first_connection (被动: 5)
2025-08-21 18:04:12.939  4562-4627  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0904","serialNo":"01610040202307060227","request_time":"1755770652928","request_id":"1755770652928C0904","version":"1","data":{"wifiOption":[{"SSID":"fubox_2.4G","SSTH":"-28"},{"SSID":"2206-5G","SSTH":"-32"},{"SSID":"fubox_5G","SSTH":"-40"},{"SSID":"2206","SSTH":"-41"},{"SSID":"2205_5G","SSTH":"-51"},{"SSID":"2205","SSTH":"-53"},{"SSID":"@Ruijie-1816","SSTH":"-56"},{"SSID":"2207","SSTH":"-67"},{"SSID":"DIRECT-C6-Mi All-in-One Inkjet","SSTH":"-70"},{"SSID":"DIRECT-CC-HP Laser 1188w","SSTH":"-71"},{"SSID":"2306","SSTH":"-72"},{"SSID":"2106","SSTH":"-72"},{"SSID":"2103","SSTH":"-77"},{"SSID":"2207-5G","SSTH":"-80"},{"SSID":"诺富特酒店2208","SSTH":"-81"},{"SSID":"dingjie888","SSTH":"-82"},{"SSID":"FJQS","SSTH":"-83"},{"SSID":"ChinaNet-SnSC","SSTH":"-85"},{"SSID":"2405","SSTH":"-86"},{"SSID":"2103_5G","SSTH":"-86"},{"SSID":"1621","SSTH":"-87"},{"SSID":"HUAWEI_B311_福","SSTH":"-92"},{"SSID":"2106-5G","SSTH":"-94"}],"baseSite":{"MCC":"","MNC":"","CID":"-1","LAC":"-1","DBM":"-999","IMSI":"","ICCID":""},"wifi":{"SSID":"fubox_2.4G","IP":"**************","MAC":"52:C3:63:13:8C:EE","SSTH":"-25","BSSID":"7c:10:c9:d6:9f:10"}},"myVersionName":"1.1.01.20250821.DSPREAD.MDM.SERVICE","terminalDate":"20250821180412"}
2025-08-21 18:04:12.941  4562-4627  WebSocket               com.dspread.mdm.service              I  🔧 C0904 网络状态上送成功 (1)
2025-08-21 18:04:13.944  4562-4627  WebSocket               com.dspread.mdm.service              I  🔧 C0906 被动式上送: first_connection (被动: 6)
2025-08-21 18:04:13.950  4562-4627  Platform                com.dspread.mdm.service              W  ⚠️ SpVersionApi SP版本信息格式异常，未找到SP_VERSION标记
2025-08-21 18:04:13.952  4562-4627  Platform                com.dspread.mdm.service              D  🔧 SpVersionApi 从Settings获取SP版本: STD 582.1.238.8
2025-08-21 18:04:13.968  4562-4627  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0906","serialNo":"01610040202307060227","request_time":"1755770653958","request_id":"1755770653958C0906","version":"1","data":{"firmWareInfo":{"spfw":"STD 582.1.238.8"},"imei_1":"014250620000709","imei_2":"","wifi_mac":"52:c3:63:13:8c:ee","bt_mac":"","bsn":""},"myVersionName":"1.1.01.20250821.DSPREAD.MDM.SERVICE","terminalDate":"20250821180413"}
2025-08-21 18:04:13.971  4562-4627  WebSocket               com.dspread.mdm.service              I  🔧 C0906 设备信息上送成功 (1)
2025-08-21 18:04:13.974  4562-4627  WebSocket               com.dspread.mdm.service              I  🔧 C0109 被动式上送: first_connection (被动: 7)
2025-08-21 18:04:13.981  4562-4627  Platform                com.dspread.mdm.service              D  🔧 重新收集应用信息（强制刷新）
2025-08-21 18:04:14.043  4562-4627  AppManager              com.dspread.mdm.service              I  ℹ️ 应用信息获取完成: 总数120(系统119/用户1) 返回1个
2025-08-21 18:04:14.047  4562-4627  Platform                com.dspread.mdm.service              D  🔧 应用信息: 1 个应用
2025-08-21 18:04:14.116  4562-4627  Platform                com.dspread.mdm.service              D  🔧 内存总容量为： 1.86GB 
2025-08-21 18:04:14.118  4562-4627  Platform                com.dspread.mdm.service              D  🔧 内存使用量为： 0.53GB 
2025-08-21 18:04:14.158  4562-4627  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0109","serialNo":"01610040202307060227","request_time":"1755770654142","request_id":"1755770654142C0109","version":"1","data":{"apkInfo":[{"packName":"com.dspread.mdm.service","apkName":"service","versionCode":20,"versionName":"1.1.01.20250821.DSPREAD.MDM.SERVICE","updateDate":"2025-08-21 18:03:44"}],"locationInfo":{"gps":{"longitude":"-999.0","latitude":"-999.0","satelliteCount":0,"satelliteCountInUsed":0,"satelliteAverSNR":"0.00","distance":"0.0","lockStatus":"1","lockMeter":"200","proId":"","proName":""},"baseSite":{"MCC":"","MNC":"","CID":"-1","LAC":"-1","DBM":"-999","IMSI":"","ICCID":""},"wifi":{"SSID":"fubox_2.4G","IP":"**************","MAC":"52:C3:63:13:8C:EE","SSTH":"-25","BSSID":"7c:10:c9:d6:9f:10"},"address":"无法获取"},"hardwareInfo":[{"name":"CPU Usage","desc":"cpu:0.50"},{"name":"storage_usage_status","desc":" 5.42GB \/ 32.00GB ","state":"1"},{"name":"total_memory","desc":" 0.53GB \/ 1.86GB ","state":"1"},{"name":"androidVersion","desc":"11","state":"1"},{"name":"network_status","desc":"wifi","state":"1"},{"name":"OSVer","desc":"11","state":"1"}],"wifiOption":[{"SSID":"fubox_2.4G","SSTH":"-28"},{"SSID":"2206-5G","SSTH":"-32"},{"SSID":"fubox_5G","SSTH":"-40"},{"SSID":"2206","SSTH":"-41"},{"SSID":"2205_5G","SSTH":"-51"},{"SSID":"2205","SSTH":"-53"},{"SSID":"@Ruijie-1816","SSTH":"-56"},{"SSID":"2207","SSTH":"-67"},{"SSID":"DIRECT-C6-Mi All-in-One Inkjet","SSTH":"-70"},{"SSID":"DIRECT-CC-HP Laser 1188w","SSTH":"-71"},{"SSID":"2306","SSTH":"-72"},{"SSID":"2106","SSTH":"-72"},{"SSID":"2103","SSTH":"-77"},{"SSID":"2207-5G","SSTH":"-80"},{"SSID":"诺富特酒店2208","SSTH":"-81"},{"SSID":"dingjie888","SSTH":"-82"},{"SSID":"FJQS","SSTH":"-83"},{"SSID":"ChinaNet-SnSC","SSTH":"-85"},{"SSID":"2405","SSTH":"-86"},{"SSID":"2103_5G","SSTH":"-86"},{"SSID":"1621","SSTH":"-87"},{"SSID":"HUAWEI_B311_福","SSTH":"-92"},{"SSID":"2106-5G","SSTH":"-94"}],"sytemInfo":{"androidVersion":"11","buildNumber":"D60_Baseline_debug_20250708_ota_test","aspl":"2021-12-05"}},"myVersionName":"1.1.01.20250821.DSPREAD.MDM.SERVICE","terminalDate":"20250821180414"}
2025-08-21 18:04:14.160  4562-4627  WebSocket               com.dspread.mdm.service              I  🔧 C0109 终端信息上传完成 (1, trigger=first_connection)
2025-08-21 18:04:14.162  4562-4627  Common                  com.dspread.mdm.service              I  ✅ 初始化信息发送完成
2025-08-21 18:04:14.165  4562-4627  WebSocket               com.dspread.mdm.service              I  🔧 检查服务更新结果
2025-08-21 18:04:14.166  4562-4627  Task                    com.dspread.mdm.service              W  ⚠️ 任务列表为空，跳过服务更新结果检查
2025-08-21 18:04:14.168  4562-4627  WebSocket               com.dspread.mdm.service              I  🔧 检查OS升级状态...
2025-08-21 18:04:14.173  4562-4627  Common                  com.dspread.mdm.service              D  🔧 OsUpdateStatusChecker WebSocket连接成功，检查OS升级状态
2025-08-21 18:04:14.175  4562-4627  Common                  com.dspread.mdm.service              D  🔧 OsUpdateStatusChecker 没有待检查的OS升级任务
2025-08-21 18:04:14.177  4562-4627  WebSocket               com.dspread.mdm.service              I  🔧 OS升级状态检查已触发
2025-08-21 18:04:14.178  4562-4627  WebSocket               com.dspread.mdm.service              I  🔧 首次连接初始化检查完成
2025-08-21 18:04:14.193  4562-4627  Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"msg_state":"P","request_time":"1755580536662","data":{"taskList":[{"beginDate":"2024-08-19 05:15:36","taskType":"02","apkName":"Via","endDate":"9999-12-31 23:59:59","appId":"1755580536662","packName":"mark.via","versionName":"6.2.0","taskId":"1755580536662","versionCode":"20250117"}]},"tranCode":"ST001","request_id":"1755580536662ST001","version":"1","serialNo":"01610040202307060227"}
2025-08-21 18:04:14.195  4562-4627  WebSocket               com.dspread.mdm.service              I  🔧 准备发送响应确认 - requestId: 1755580536662ST001, needResponse: true
2025-08-21 18:04:14.202  4562-4627  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0000","serialNo":"01610040202307060227","request_time":"1755770654198","request_id":"1755770654198C0000","version":"1","org_request_id":"1755580536662ST001","org_request_time":"1755580536662","response_state":"0","myVersionName":"1.1.01.20250821.DSPREAD.MDM.SERVICE","terminalDate":"20250821180414"}
2025-08-21 18:04:14.209  4562-4627  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0000","serialNo":"01610040202307060227","request_time":"1755770654206","request_id":"1755770654206C0000","version":"1","org_request_id":"1755580536662ST001","org_request_time":"1755580536662","response_state":"0","myVersionName":"1.1.01.20250821.DSPREAD.MDM.SERVICE","terminalDate":"20250821180414"}
2025-08-21 18:04:14.211  4562-4627  WebSocket               com.dspread.mdm.service              I  🔧 已发送C0000响应确认: requestId=1755580536662ST001
2025-08-21 18:04:14.213  4562-4627  Task                    com.dspread.mdm.service              D  🔧 处理任务消息: ST001
2025-08-21 18:04:14.215  4562-4627  Task                    com.dspread.mdm.service              I  ℹ️ 更新任务列表: requestId=1755580536662ST001, 任务数量=1
2025-08-21 18:04:14.217  4562-4627  Task                    com.dspread.mdm.service              D  🔧 任务插入到末尾: taskId=1755580536662
2025-08-21 18:04:14.218  4562-4627  Task                    com.dspread.mdm.service              D  🔧 添加/更新任务: taskId=1755580536662
2025-08-21 18:04:14.221  4562-4627  Task                    com.dspread.mdm.service              D  🔧 保存任务列表到存储: 1 个
2025-08-21 18:04:14.223  4562-4627  Task                    com.dspread.mdm.service              D  🔧 开始处理任务列表，共 1 个任务
2025-08-21 18:04:14.225  4562-4627  Task                    com.dspread.mdm.service              D  🔧 处理任务: taskId=1755580536662, type=02, package=mark.via, apk=Via
2025-08-21 18:04:14.226  4562-4627  Task                    com.dspread.mdm.service              D  🔧 处理卸载任务: 1755580536662
2025-08-21 18:04:14.229  4562-4627  Task                    com.dspread.mdm.service              D  🔧 更新任务状态: taskId=1755580536662, state=D02
2025-08-21 18:04:14.231  4562-4627  Task                    com.dspread.mdm.service              D  🔧 保存任务列表到存储: 1 个
2025-08-21 18:04:14.234  4562-4627  Task                    com.dspread.mdm.service              D  🔧 保存任务列表到存储: 1 个
2025-08-21 18:04:14.235  4562-4627  WebSocket               com.dspread.mdm.service              I  🔧 ST001 主动式上送: task_failed (主动: 3)
2025-08-21 18:04:14.248  4562-4627  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0108","serialNo":"01610040202307060227","request_time":"1755770654241","request_id":"1755770654241C0108","version":"1","data":{"taskId":"1755580536662","taskResult":"D02","appId":"1755580536662"},"myVersionName":"1.1.01.20250821.DSPREAD.MDM.SERVICE","terminalDate":"20250821180414","org_request_id":"1755580536662ST001","org_request_time":"1755580536662"}
2025-08-21 18:04:14.250  4562-4627  WebSocket               com.dspread.mdm.service              I  🔧 任务结果实时上送: taskId=1755580536662, result=D02 (1)
2025-08-21 18:04:14.263  4562-4627  Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"msg_state":"P","request_time":"1755580524415","data":{"taskList":[{"beginDate":"2024-08-19 05:15:24","taskType":"02","apkName":"OpenVPN for Android","endDate":"9999-12-31 23:59:59","appId":"1755580524415","packName":"de.blinkt.openvpn","versionName":"0.5.36a","taskId":"1755580524415","versionCode":"65"}]},"tranCode":"ST001","request_id":"1755580524415ST001","version":"1","serialNo":"01610040202307060227"}
2025-08-21 18:04:14.265  4562-4627  WebSocket               com.dspread.mdm.service              I  🔧 准备发送响应确认 - requestId: 1755580524415ST001, needResponse: true
2025-08-21 18:04:14.271  4562-4627  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0000","serialNo":"01610040202307060227","request_time":"1755770654267","request_id":"1755770654267C0000","version":"1","org_request_id":"1755580524415ST001","org_request_time":"1755580524415","response_state":"0","myVersionName":"1.1.01.20250821.DSPREAD.MDM.SERVICE","terminalDate":"20250821180414"}
2025-08-21 18:04:14.279  4562-4627  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0000","serialNo":"01610040202307060227","request_time":"1755770654276","request_id":"1755770654276C0000","version":"1","org_request_id":"1755580524415ST001","org_request_time":"1755580524415","response_state":"0","myVersionName":"1.1.01.20250821.DSPREAD.MDM.SERVICE","terminalDate":"20250821180414"}
2025-08-21 18:04:14.281  4562-4627  WebSocket               com.dspread.mdm.service              I  🔧 已发送C0000响应确认: requestId=1755580524415ST001
2025-08-21 18:04:14.283  4562-4627  Task                    com.dspread.mdm.service              D  🔧 处理任务消息: ST001
2025-08-21 18:04:14.284  4562-4627  Task                    com.dspread.mdm.service              I  ℹ️ 更新任务列表: requestId=1755580524415ST001, 任务数量=1
2025-08-21 18:04:14.286  4562-4627  Task                    com.dspread.mdm.service              D  🔧 任务按时间插入到位置: 0, taskId=1755580524415
2025-08-21 18:04:14.288  4562-4627  Task                    com.dspread.mdm.service              D  🔧 添加/更新任务: taskId=1755580524415
2025-08-21 18:04:14.290  4562-4627  Task                    com.dspread.mdm.service              D  🔧 发现已完成任务: taskId=1755580536662, result=D02
2025-08-21 18:04:14.291  4562-4627  Task                    com.dspread.mdm.service              D  🔧 上送任务结果: taskId=1755580536662, result=D02
2025-08-21 18:04:14.293  4562-4627  Task                    com.dspread.mdm.service              D  🔧 清理已完成任务: taskId=1755580536662, result=D02
2025-08-21 18:04:14.294  4562-4627  Task                    com.dspread.mdm.service              D  🔧 清理了 1 个已完成任务
2025-08-21 18:04:14.296  4562-4627  Task                    com.dspread.mdm.service              D  🔧 保存任务列表到存储: 1 个
2025-08-21 18:04:14.298  4562-4627  Task                    com.dspread.mdm.service              D  🔧 开始处理任务列表，共 1 个任务
2025-08-21 18:04:14.300  4562-4627  Task                    com.dspread.mdm.service              D  🔧 处理任务: taskId=1755580524415, type=02, package=de.blinkt.openvpn, apk=OpenVPN for Android
2025-08-21 18:04:14.301  4562-4627  Task                    com.dspread.mdm.service              D  🔧 处理卸载任务: 1755580524415
2025-08-21 18:04:14.304  4562-4627  Task                    com.dspread.mdm.service              D  🔧 更新任务状态: taskId=1755580524415, state=D02
2025-08-21 18:04:14.306  4562-4627  Task                    com.dspread.mdm.service              D  🔧 保存任务列表到存储: 1 个
2025-08-21 18:04:14.309  4562-4627  Task                    com.dspread.mdm.service              D  🔧 保存任务列表到存储: 1 个
2025-08-21 18:04:14.310  4562-4627  WebSocket               com.dspread.mdm.service              I  🔧 ST001 主动式上送: task_failed (主动: 4)
2025-08-21 18:04:14.319  4562-4627  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0108","serialNo":"01610040202307060227","request_time":"1755770654313","request_id":"1755770654313C0108","version":"1","data":{"taskId":"1755580524415","taskResult":"D02","appId":"1755580524415"},"myVersionName":"1.1.01.20250821.DSPREAD.MDM.SERVICE","terminalDate":"20250821180414","org_request_id":"1755580524415ST001","org_request_time":"1755580524415"}
2025-08-21 18:04:14.320  4562-4627  WebSocket               com.dspread.mdm.service              I  🔧 任务结果实时上送: taskId=1755580524415, result=D02 (1)
2025-08-21 18:04:14.375  4562-4627  Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"request_time":"1755770655437","org_request_time":"1755770653958","org_request_id":"1755770653958C0906","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755770655437S0000","serialNo":"01610040202307060227"}
2025-08-21 18:04:14.377  4562-4627  WebSocket               com.dspread.mdm.service              I  🔧 收到响应消息: transCode=S0000, orgRequestId=1755770653958C0906, state=0, remark=
2025-08-21 18:04:14.379  4562-4627  WebSocket               com.dspread.mdm.service              I  🔧 处理设备信息响应: state=0
2025-08-21 18:04:14.381  4562-4627  WebSocket               com.dspread.mdm.service              I  🔧 设备信息上传成功
2025-08-21 18:04:14.761  4562-4627  Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"request_time":"1755770655718","org_request_time":"1755770654142","org_request_id":"1755770654142C0109","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755770655718S0000","serialNo":"01610040202307060227"}
2025-08-21 18:04:14.764  4562-4627  WebSocket               com.dspread.mdm.service              I  🔧 收到响应消息: transCode=S0000, orgRequestId=1755770654142C0109, state=0, remark=
2025-08-21 18:04:14.766  4562-4627  WebSocket               com.dspread.mdm.service              I  🔧 终端信息上传成功
2025-08-21 18:04:14.767  4562-4627  WebSocket               com.dspread.mdm.service              I  🔧 终端信息上传成功标志已设置
2025-08-21 18:04:14.777  4562-4627  Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"request_time":"1755770655800","org_request_time":"1755770654241","org_request_id":"1755770654241C0108","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755770655800S0000","serialNo":"01610040202307060227"}
2025-08-21 18:04:14.779  4562-4627  WebSocket               com.dspread.mdm.service              I  🔧 收到响应消息: transCode=S0000, orgRequestId=1755770654241C0108, state=0, remark=
2025-08-21 18:04:14.781  4562-4627  WebSocket               com.dspread.mdm.service              I  🔧 处理任务结果响应: state=0
2025-08-21 18:04:14.782  4562-4627  WebSocket               com.dspread.mdm.service              I  🔧 任务结果上传成功
2025-08-21 18:04:14.792  4562-4627  Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"request_time":"1755770655821","org_request_time":"1755770654313","org_request_id":"1755770654313C0108","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755770655821S0000","serialNo":"01610040202307060227"}
2025-08-21 18:04:14.794  4562-4627  WebSocket               com.dspread.mdm.service              I  🔧 收到响应消息: transCode=S0000, orgRequestId=1755770654313C0108, state=0, remark=
2025-08-21 18:04:14.796  4562-4627  WebSocket               com.dspread.mdm.service              I  🔧 处理任务结果响应: state=0
2025-08-21 18:04:14.798  4562-4627  WebSocket               com.dspread.mdm.service              I  🔧 任务结果上传成功
2025-08-21 18:04:17.750   947-1023  ActivityManager         system_server                        E  Sending non-protected broadcast com.dspread.mdm.service.WAKELOCK_RENEWAL from system uid 1000 pkg com.dspread.mdm.service (Ask Gemini)
                                                                                                    java.lang.Throwable
                                                                                                    	at com.android.server.am.ActivityManagerService.checkBroadcastFromSystem(ActivityManagerService.java:16192)
                                                                                                    	at com.android.server.am.ActivityManagerService.broadcastIntentLocked(ActivityManagerService.java:16884)
                                                                                                    	at com.android.server.am.ActivityManagerService.broadcastIntentInPackage(ActivityManagerService.java:17086)
                                                                                                    	at com.android.server.am.ActivityManagerService$LocalService.broadcastIntentInPackage(ActivityManagerService.java:19752)
                                                                                                    	at com.android.server.am.PendingIntentRecord.sendInner(PendingIntentRecord.java:442)
                                                                                                    	at com.android.server.am.PendingIntentRecord.sendWithResult(PendingIntentRecord.java:285)
                                                                                                    	at com.android.server.am.ActivityManagerService.sendIntentSender(ActivityManagerService.java:5911)
                                                                                                    	at android.app.PendingIntent.sendAndReturnResult(PendingIntent.java:913)
                                                                                                    	at android.app.PendingIntent.send(PendingIntent.java:884)
                                                                                                    	at com.android.server.AlarmManagerService$DeliveryTracker.deliverLocked(AlarmManagerService.java:4796)
                                                                                                    	at com.android.server.AlarmManagerService.deliverAlarmsLocked(AlarmManagerService.java:3926)
                                                                                                    	at com.android.server.AlarmManagerService$AlarmThread.run(AlarmManagerService.java:4175)
2025-08-21 18:04:26.738  4562-4562  Receiver                com.dspread.mdm.service              D  🔧 电池状态更新: 电池状态: 电量=92%, 温度=27.7°C, 健康=2, 充电=是, 低电量=否, 耗尽=否
2025-08-21 18:05:08.924  4562-4628  WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 1
2025-08-21 18:05:08.986   947-1023  ActivityManager         system_server                        E  Sending non-protected broadcast com.dspread.mdm.service.WSTASK_EXEC_BC from system uid 1000 pkg com.dspread.mdm.service (Ask Gemini)
                                                                                                    java.lang.Throwable
                                                                                                    	at com.android.server.am.ActivityManagerService.checkBroadcastFromSystem(ActivityManagerService.java:16192)
                                                                                                    	at com.android.server.am.ActivityManagerService.broadcastIntentLocked(ActivityManagerService.java:16884)
                                                                                                    	at com.android.server.am.ActivityManagerService.broadcastIntentInPackage(ActivityManagerService.java:17086)
                                                                                                    	at com.android.server.am.ActivityManagerService$LocalService.broadcastIntentInPackage(ActivityManagerService.java:19752)
                                                                                                    	at com.android.server.am.PendingIntentRecord.sendInner(PendingIntentRecord.java:442)
                                                                                                    	at com.android.server.am.PendingIntentRecord.sendWithResult(PendingIntentRecord.java:285)
                                                                                                    	at com.android.server.am.ActivityManagerService.sendIntentSender(ActivityManagerService.java:5911)
                                                                                                    	at android.app.PendingIntent.sendAndReturnResult(PendingIntent.java:913)
                                                                                                    	at android.app.PendingIntent.send(PendingIntent.java:884)
                                                                                                    	at com.android.server.AlarmManagerService$DeliveryTracker.deliverLocked(AlarmManagerService.java:4796)
                                                                                                    	at com.android.server.AlarmManagerService.deliverAlarmsLocked(AlarmManagerService.java:3926)
                                                                                                    	at com.android.server.AlarmManagerService$AlarmThread.run(AlarmManagerService.java:4175)
2025-08-21 18:05:08.993   947-1023  ActivityManager         system_server                        E  Sending non-protected broadcast com.dspread.mdm.service.POLL_TIMER_START from system uid 1000 pkg com.dspread.mdm.service (Ask Gemini)
                                                                                                    java.lang.Throwable
                                                                                                    	at com.android.server.am.ActivityManagerService.checkBroadcastFromSystem(ActivityManagerService.java:16192)
                                                                                                    	at com.android.server.am.ActivityManagerService.broadcastIntentLocked(ActivityManagerService.java:16884)
                                                                                                    	at com.android.server.am.ActivityManagerService.broadcastIntentInPackage(ActivityManagerService.java:17086)
                                                                                                    	at com.android.server.am.ActivityManagerService$LocalService.broadcastIntentInPackage(ActivityManagerService.java:19752)
                                                                                                    	at com.android.server.am.PendingIntentRecord.sendInner(PendingIntentRecord.java:442)
                                                                                                    	at com.android.server.am.PendingIntentRecord.sendWithResult(PendingIntentRecord.java:285)
                                                                                                    	at com.android.server.am.ActivityManagerService.sendIntentSender(ActivityManagerService.java:5911)
                                                                                                    	at android.app.PendingIntent.sendAndReturnResult(PendingIntent.java:913)
                                                                                                    	at android.app.PendingIntent.send(PendingIntent.java:884)
                                                                                                    	at com.android.server.AlarmManagerService$DeliveryTracker.deliverLocked(AlarmManagerService.java:4796)
                                                                                                    	at com.android.server.AlarmManagerService.deliverAlarmsLocked(AlarmManagerService.java:3926)
                                                                                                    	at com.android.server.AlarmManagerService$AlarmThread.run(AlarmManagerService.java:4175)
2025-08-21 18:05:09.005  4562-4562  Common                  com.dspread.mdm.service              I  ✅ ⏰ 设置任务执行定时器成功，下次执行: 60秒后 (1分钟)
2025-08-21 18:05:09.022  4562-4562  Receiver                com.dspread.mdm.service              D  🔧 收到轮询定时器广播
2025-08-21 18:05:09.026  4562-4562  WebSocket               com.dspread.mdm.service              I  🔧 WebSocketCenter: 发送心跳
2025-08-21 18:05:09.029  4562-4562  Receiver                com.dspread.mdm.service              D  🔧 检查待上传的任务和规则结果
2025-08-21 18:05:09.039  4562-4562  WebSocket               com.dspread.mdm.service              I  🔧 C0201 被动式上送: scheduled_report (被动: 8)
2025-08-21 18:05:09.057  4562-4562  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0201","serialNo":"01610040202307060227","request_time":"1755770709045","request_id":"1755770709045C0201","version":"1","data":{"unboxStatus":"normal","isInUse":"1","websocketConnected":true,"myVersionName":"1.1.01.20250821.DSPREAD.MDM.SERVICE","terminalDate":"20250821180509"},"myVersionName":"1.1.01.20250821.DSPREAD.MDM.SERVICE","terminalDate":"20250821180509"}
2025-08-21 18:05:09.061  4562-4562  WebSocket               com.dspread.mdm.service              I  🔧 C0201 设备状态上送成功: status=normal, inUse=true (1)
2025-08-21 18:05:09.068  4562-4562  Common                  com.dspread.mdm.service              I  ✅ ⏰ 设置心跳定时器成功，下次执行: 60秒后 (1分钟)
2025-08-21 18:05:09.327  4562-4627  WebSocket               com.dspread.mdm.service              I  🔧 收到服务器 PONG 帧: 1
2025-08-21 18:05:26.737  4562-4562  Receiver                com.dspread.mdm.service              D  🔧 电池状态更新: 电池状态: 电量=92%, 温度=27.7°C, 健康=2, 充电=是, 低电量=否, 耗尽=否
2025-08-21 18:05:46.471   947-1023  ActivityManager         system_server                        E  Sending non-protected broadcast com.dspread.mdm.service.SERVICE_GUARD_TIMER from system uid 1000 pkg com.dspread.mdm.service (Ask Gemini)
                                                                                                    java.lang.Throwable
                                                                                                    	at com.android.server.am.ActivityManagerService.checkBroadcastFromSystem(ActivityManagerService.java:16192)
                                                                                                    	at com.android.server.am.ActivityManagerService.broadcastIntentLocked(ActivityManagerService.java:16884)
                                                                                                    	at com.android.server.am.ActivityManagerService.broadcastIntentInPackage(ActivityManagerService.java:17086)
                                                                                                    	at com.android.server.am.ActivityManagerService$LocalService.broadcastIntentInPackage(ActivityManagerService.java:19752)
                                                                                                    	at com.android.server.am.PendingIntentRecord.sendInner(PendingIntentRecord.java:442)
                                                                                                    	at com.android.server.am.PendingIntentRecord.sendWithResult(PendingIntentRecord.java:285)
                                                                                                    	at com.android.server.am.ActivityManagerService.sendIntentSender(ActivityManagerService.java:5911)
                                                                                                    	at android.app.PendingIntent.sendAndReturnResult(PendingIntent.java:913)
                                                                                                    	at android.app.PendingIntent.send(PendingIntent.java:884)
                                                                                                    	at com.android.server.AlarmManagerService$DeliveryTracker.deliverLocked(AlarmManagerService.java:4796)
                                                                                                    	at com.android.server.AlarmManagerService.deliverAlarmsLocked(AlarmManagerService.java:3926)
                                                                                                    	at com.android.server.AlarmManagerService$AlarmThread.run(AlarmManagerService.java:4175)
2025-08-21 18:05:46.489  4562-4592  Common                  com.dspread.mdm.service              D  🔧 ServiceGuardEventHandler 开始执行保活动作
2025-08-21 18:05:46.502  4562-4592  Common                  com.dspread.mdm.service              I  ✅ ⏰ 设置服务守护定时器成功，下次执行: 120秒后 (2分钟)
2025-08-21 18:05:46.506  4562-4592  ContextImpl             com.dspread.mdm.service              W  Calling a method in the system process without a qualified user: android.app.ContextImpl.startService:1669 android.content.ContextWrapper.startService:720 com.dspread.mdm.service.broadcast.handlers.service.ServiceGuardEventHandler.startKeepAliveService:211 com.dspread.mdm.service.broadcast.handlers.service.ServiceGuardEventHandler.performKeepAliveActions:193 com.dspread.mdm.service.broadcast.handlers.service.ServiceGuardEventHandler.access$performKeepAliveActions:24 
2025-08-21 18:05:46.513  4562-4592  Common                  com.dspread.mdm.service              D  🔧 ServiceGuardEventHandler 启动保活服务
2025-08-21 18:05:46.518  4562-4562  Service                 com.dspread.mdm.service              D  🔧 ServiceKeepAliveService 保活服务创建
2025-08-21 18:05:46.523  4562-4562  Service                 com.dspread.mdm.service              D  🔧 ServiceKeepAliveService 保活服务启动
2025-08-21 18:05:46.526  4562-4592  Common                  com.dspread.mdm.service              D  🔧 ServiceGuardEventHandler 保活动作执行完成
2025-08-21 18:05:46.528  4562-4562  Platform                com.dspread.mdm.service              D  🔧 WakeLockManager WakeLock不存在或未持有: ServiceKeepAlive
2025-08-21 18:05:46.543  4562-4562  Platform                com.dspread.mdm.service              D  🔧 WakeLockManager 获取WakeLock成功: ServiceKeepAlive (超时: 30000ms)
2025-08-21 18:05:49.531  4562-4592  Common                  com.dspread.mdm.service              D  🔧 ServiceGuardEventHandler WakeLock已释放
2025-08-21 18:05:59.137  4562-4562  Receiver                com.dspread.mdm.service              D  🔧 电池状态更新: 电池状态: 电量=92%, 温度=27.8°C, 健康=2, 充电=是, 低电量=否, 耗尽=否
2025-08-21 18:06:08.925  4562-4628  WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 2
2025-08-21 18:06:09.000   947-1023  ActivityManager         system_server                        E  Sending non-protected broadcast com.dspread.mdm.service.TER_INFO_UPLOAD_BC from system uid 1000 pkg com.dspread.mdm.service (Ask Gemini)
                                                                                                    java.lang.Throwable
                                                                                                    	at com.android.server.am.ActivityManagerService.checkBroadcastFromSystem(ActivityManagerService.java:16192)
                                                                                                    	at com.android.server.am.ActivityManagerService.broadcastIntentLocked(ActivityManagerService.java:16884)
                                                                                                    	at com.android.server.am.ActivityManagerService.broadcastIntentInPackage(ActivityManagerService.java:17086)
                                                                                                    	at com.android.server.am.ActivityManagerService$LocalService.broadcastIntentInPackage(ActivityManagerService.java:19752)
                                                                                                    	at com.android.server.am.PendingIntentRecord.sendInner(PendingIntentRecord.java:442)
                                                                                                    	at com.android.server.am.PendingIntentRecord.sendWithResult(PendingIntentRecord.java:285)
                                                                                                    	at com.android.server.am.ActivityManagerService.sendIntentSender(ActivityManagerService.java:5911)
                                                                                                    	at android.app.PendingIntent.sendAndReturnResult(PendingIntent.java:913)
                                                                                                    	at android.app.PendingIntent.send(PendingIntent.java:884)
                                                                                                    	at com.android.server.AlarmManagerService$DeliveryTracker.deliverLocked(AlarmManagerService.java:4796)
                                                                                                    	at com.android.server.AlarmManagerService.deliverAlarmsLocked(AlarmManagerService.java:3926)
                                                                                                    	at com.android.server.AlarmManagerService$AlarmThread.run(AlarmManagerService.java:4175)
2025-08-21 18:06:09.004   947-1023  ActivityManager         system_server                        E  Sending non-protected broadcast com.dspread.mdm.service.WSTASK_EXEC_BC from system uid 1000 pkg com.dspread.mdm.service (Ask Gemini)
                                                                                                    java.lang.Throwable
                                                                                                    	at com.android.server.am.ActivityManagerService.checkBroadcastFromSystem(ActivityManagerService.java:16192)
                                                                                                    	at com.android.server.am.ActivityManagerService.broadcastIntentLocked(ActivityManagerService.java:16884)
                                                                                                    	at com.android.server.am.ActivityManagerService.broadcastIntentInPackage(ActivityManagerService.java:17086)
                                                                                                    	at com.android.server.am.ActivityManagerService$LocalService.broadcastIntentInPackage(ActivityManagerService.java:19752)
                                                                                                    	at com.android.server.am.PendingIntentRecord.sendInner(PendingIntentRecord.java:442)
                                                                                                    	at com.android.server.am.PendingIntentRecord.sendWithResult(PendingIntentRecord.java:285)
                                                                                                    	at com.android.server.am.ActivityManagerService.sendIntentSender(ActivityManagerService.java:5911)
                                                                                                    	at android.app.PendingIntent.sendAndReturnResult(PendingIntent.java:913)
                                                                                                    	at android.app.PendingIntent.send(PendingIntent.java:884)
                                                                                                    	at com.android.server.AlarmManagerService$DeliveryTracker.deliverLocked(AlarmManagerService.java:4796)
                                                                                                    	at com.android.server.AlarmManagerService.deliverAlarmsLocked(AlarmManagerService.java:3926)
                                                                                                    	at com.android.server.AlarmManagerService$AlarmThread.run(AlarmManagerService.java:4175)
2025-08-21 18:06:09.018  4562-4562  Common                  com.dspread.mdm.service              I  ✅ ⏰ 设置终端信息定时器成功，下次执行: 120秒后 (2分钟)
2025-08-21 18:06:09.022  4562-4562  WebSocket               com.dspread.mdm.service              I  🔧 C0109 主动式上送: terminal_info_timer (主动: 5)
2025-08-21 18:06:09.026  4562-4562  Platform                com.dspread.mdm.service              D  🔧 重新收集应用信息（强制刷新）
2025-08-21 18:06:09.067   947-1023  ActivityManager         system_server                        E  Sending non-protected broadcast com.dspread.mdm.service.POLL_TIMER_START from system uid 1000 pkg com.dspread.mdm.service (Ask Gemini)
                                                                                                    java.lang.Throwable
                                                                                                    	at com.android.server.am.ActivityManagerService.checkBroadcastFromSystem(ActivityManagerService.java:16192)
                                                                                                    	at com.android.server.am.ActivityManagerService.broadcastIntentLocked(ActivityManagerService.java:16884)
                                                                                                    	at com.android.server.am.ActivityManagerService.broadcastIntentInPackage(ActivityManagerService.java:17086)
                                                                                                    	at com.android.server.am.ActivityManagerService$LocalService.broadcastIntentInPackage(ActivityManagerService.java:19752)
                                                                                                    	at com.android.server.am.PendingIntentRecord.sendInner(PendingIntentRecord.java:442)
                                                                                                    	at com.android.server.am.PendingIntentRecord.sendWithResult(PendingIntentRecord.java:285)
                                                                                                    	at com.android.server.am.ActivityManagerService.sendIntentSender(ActivityManagerService.java:5911)
                                                                                                    	at android.app.PendingIntent.sendAndReturnResult(PendingIntent.java:913)
                                                                                                    	at android.app.PendingIntent.send(PendingIntent.java:884)
                                                                                                    	at com.android.server.AlarmManagerService$DeliveryTracker.deliverLocked(AlarmManagerService.java:4796)
                                                                                                    	at com.android.server.AlarmManagerService.deliverAlarmsLocked(AlarmManagerService.java:3926)
                                                                                                    	at com.android.server.AlarmManagerService$AlarmThread.run(AlarmManagerService.java:4175)
2025-08-21 18:06:09.072  4562-4562  AppManager              com.dspread.mdm.service              I  ℹ️ 应用信息获取完成: 总数120(系统119/用户1) 返回1个
2025-08-21 18:06:09.076  4562-4562  Platform                com.dspread.mdm.service              D  🔧 应用信息: 1 个应用
2025-08-21 18:06:09.157  4562-4562  Platform                com.dspread.mdm.service              D  🔧 内存总容量为： 1.86GB 
2025-08-21 18:06:09.159  4562-4562  Platform                com.dspread.mdm.service              D  🔧 内存使用量为： 0.54GB 
2025-08-21 18:06:09.191  4562-4562  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0109","serialNo":"01610040202307060227","request_time":"1755770769175","request_id":"1755770769175C0109","version":"1","data":{"apkInfo":[{"packName":"com.dspread.mdm.service","apkName":"service","versionCode":20,"versionName":"1.1.01.20250821.DSPREAD.MDM.SERVICE","updateDate":"2025-08-21 18:03:44"}],"locationInfo":{"gps":{"longitude":"-999.0","latitude":"-999.0","satelliteCount":0,"satelliteCountInUsed":0,"satelliteAverSNR":"0.00","distance":"0.0","lockStatus":"1","lockMeter":"200","proId":"","proName":""},"baseSite":{"MCC":"","MNC":"","CID":"-1","LAC":"-1","DBM":"-999","IMSI":"","ICCID":""},"wifi":{"SSID":"fubox_2.4G","IP":"**************","MAC":"52:C3:63:13:8C:EE","SSTH":"-25","BSSID":"7c:10:c9:d6:9f:10"},"address":"无法获取"},"hardwareInfo":[{"name":"CPU Usage","desc":"cpu:0.50"},{"name":"storage_usage_status","desc":" 5.42GB \/ 32.00GB ","state":"1"},{"name":"total_memory","desc":" 0.54GB \/ 1.86GB ","state":"1"},{"name":"androidVersion","desc":"11","state":"1"},{"name":"network_status","desc":"wifi","state":"1"},{"name":"OSVer","desc":"11","state":"1"}],"wifiOption":[{"SSID":"fubox_2.4G","SSTH":"-28"},{"SSID":"2206-5G","SSTH":"-32"},{"SSID":"fubox_5G","SSTH":"-40"},{"SSID":"2206","SSTH":"-41"},{"SSID":"2205_5G","SSTH":"-51"},{"SSID":"2205","SSTH":"-53"},{"SSID":"@Ruijie-1816","SSTH":"-56"},{"SSID":"2207","SSTH":"-67"},{"SSID":"DIRECT-C6-Mi All-in-One Inkjet","SSTH":"-70"},{"SSID":"DIRECT-CC-HP Laser 1188w","SSTH":"-71"},{"SSID":"2306","SSTH":"-72"},{"SSID":"2106","SSTH":"-72"},{"SSID":"2103","SSTH":"-77"},{"SSID":"2207-5G","SSTH":"-80"},{"SSID":"诺富特酒店2208","SSTH":"-81"},{"SSID":"dingjie888","SSTH":"-82"},{"SSID":"FJQS","SSTH":"-83"},{"SSID":"ChinaNet-SnSC","SSTH":"-85"},{"SSID":"2405","SSTH":"-86"},{"SSID":"2103_5G","SSTH":"-86"},{"SSID":"1621","SSTH":"-87"},{"SSID":"HUAWEI_B311_福","SSTH":"-92"},{"SSID":"2106-5G","SSTH":"-94"}],"sytemInfo":{"androidVersion":"11","buildNumber":"D60_Baseline_debug_20250708_ota_test","aspl":"2021-12-05"}},"myVersionName":"1.1.01.20250821.DSPREAD.MDM.SERVICE","terminalDate":"20250821180609"}
2025-08-21 18:06:09.193  4562-4562  WebSocket               com.dspread.mdm.service              I  🔧 C0109 终端信息上传完成 (1, trigger=terminal_info_timer)
2025-08-21 18:06:09.196  4562-4562  Receiver                com.dspread.mdm.service              D  🔧 TerminalInfoEventHandler 终端信息上传完成
2025-08-21 18:06:09.203  4562-4562  Common                  com.dspread.mdm.service              I  ✅ ⏰ 设置任务执行定时器成功，下次执行: 60秒后 (1分钟)
2025-08-21 18:06:09.210  4562-4562  Receiver                com.dspread.mdm.service              D  🔧 收到轮询定时器广播
2025-08-21 18:06:09.212  4562-4562  WebSocket               com.dspread.mdm.service              I  🔧 WebSocketCenter: 发送心跳
2025-08-21 18:06:09.214  4562-4562  Receiver                com.dspread.mdm.service              D  🔧 检查待上传的任务和规则结果
2025-08-21 18:06:09.219  4562-4562  WebSocket               com.dspread.mdm.service              I  🔧 C0201 被动式上送: scheduled_report (被动: 9)
2025-08-21 18:06:09.228  4562-4562  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0201","serialNo":"01610040202307060227","request_time":"1755770769222","request_id":"1755770769222C0201","version":"1","data":{"unboxStatus":"normal","isInUse":"1","websocketConnected":true,"myVersionName":"1.1.01.20250821.DSPREAD.MDM.SERVICE","terminalDate":"20250821180609"},"myVersionName":"1.1.01.20250821.DSPREAD.MDM.SERVICE","terminalDate":"20250821180609"}
2025-08-21 18:06:09.230  4562-4562  WebSocket               com.dspread.mdm.service              I  🔧 C0201 设备状态上送成功: status=normal, inUse=true (1)
2025-08-21 18:06:09.233  4562-4562  Common                  com.dspread.mdm.service              I  ✅ ⏰ 设置心跳定时器成功，下次执行: 60秒后 (1分钟)
2025-08-21 18:06:09.846  4562-4627  WebSocket               com.dspread.mdm.service              I  🔧 收到服务器 PONG 帧: 2
2025-08-21 18:06:10.577  4562-4627  Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"request_time":"1755770771313","org_request_time":"1755770769175","org_request_id":"1755770769175C0109","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755770771313S0000","serialNo":"01610040202307060227"}
2025-08-21 18:06:10.580  4562-4627  WebSocket               com.dspread.mdm.service              I  🔧 收到响应消息: transCode=S0000, orgRequestId=1755770769175C0109, state=0, remark=
2025-08-21 18:06:10.583  4562-4627  WebSocket               com.dspread.mdm.service              I  🔧 终端信息上传成功
2025-08-21 18:06:10.586  4562-4627  WebSocket               com.dspread.mdm.service              I  🔧 终端信息上传成功标志已设置
2025-08-21 18:06:16.553  4562-4562  Platform                com.dspread.mdm.service              D  🔧 WakeLockManager WakeLock不存在或未持有: ServiceKeepAlive
2025-08-21 18:06:16.556  4562-4562  Service                 com.dspread.mdm.service              D  🔧 ServiceKeepAliveService 保活任务完成，释放WakeLock
2025-08-21 18:06:26.737  4562-4562  Receiver                com.dspread.mdm.service              D  🔧 电池状态更新: 电池状态: 电量=92%, 温度=27.7°C, 健康=2, 充电=是, 低电量=否, 耗尽=否
2025-08-21 18:06:59.144  4562-4562  Receiver                com.dspread.mdm.service              D  🔧 电池状态更新: 电池状态: 电量=92%, 温度=27.8°C, 健康=2, 充电=是, 低电量=否, 耗尽=否
2025-08-21 18:07:08.927  4562-4628  WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 3
2025-08-21 18:07:09.208   947-1023  ActivityManager         system_server                        E  Sending non-protected broadcast com.dspread.mdm.service.WSTASK_EXEC_BC from system uid 1000 pkg com.dspread.mdm.service (Ask Gemini)
                                                                                                    java.lang.Throwable
                                                                                                    	at com.android.server.am.ActivityManagerService.checkBroadcastFromSystem(ActivityManagerService.java:16192)
                                                                                                    	at com.android.server.am.ActivityManagerService.broadcastIntentLocked(ActivityManagerService.java:16884)
                                                                                                    	at com.android.server.am.ActivityManagerService.broadcastIntentInPackage(ActivityManagerService.java:17086)
                                                                                                    	at com.android.server.am.ActivityManagerService$LocalService.broadcastIntentInPackage(ActivityManagerService.java:19752)
                                                                                                    	at com.android.server.am.PendingIntentRecord.sendInner(PendingIntentRecord.java:442)
                                                                                                    	at com.android.server.am.PendingIntentRecord.sendWithResult(PendingIntentRecord.java:285)
                                                                                                    	at com.android.server.am.ActivityManagerService.sendIntentSender(ActivityManagerService.java:5911)
                                                                                                    	at android.app.PendingIntent.sendAndReturnResult(PendingIntent.java:913)
                                                                                                    	at android.app.PendingIntent.send(PendingIntent.java:884)
                                                                                                    	at com.android.server.AlarmManagerService$DeliveryTracker.deliverLocked(AlarmManagerService.java:4796)
                                                                                                    	at com.android.server.AlarmManagerService.deliverAlarmsLocked(AlarmManagerService.java:3926)
                                                                                                    	at com.android.server.AlarmManagerService$AlarmThread.run(AlarmManagerService.java:4175)
2025-08-21 18:07:09.236   947-1023  ActivityManager         system_server                        E  Sending non-protected broadcast com.dspread.mdm.service.POLL_TIMER_START from system uid 1000 pkg com.dspread.mdm.service (Ask Gemini)
                                                                                                    java.lang.Throwable
                                                                                                    	at com.android.server.am.ActivityManagerService.checkBroadcastFromSystem(ActivityManagerService.java:16192)
                                                                                                    	at com.android.server.am.ActivityManagerService.broadcastIntentLocked(ActivityManagerService.java:16884)
                                                                                                    	at com.android.server.am.ActivityManagerService.broadcastIntentInPackage(ActivityManagerService.java:17086)
                                                                                                    	at com.android.server.am.ActivityManagerService$LocalService.broadcastIntentInPackage(ActivityManagerService.java:19752)
                                                                                                    	at com.android.server.am.PendingIntentRecord.sendInner(PendingIntentRecord.java:442)
                                                                                                    	at com.android.server.am.PendingIntentRecord.sendWithResult(PendingIntentRecord.java:285)
                                                                                                    	at com.android.server.am.ActivityManagerService.sendIntentSender(ActivityManagerService.java:5911)
                                                                                                    	at android.app.PendingIntent.sendAndReturnResult(PendingIntent.java:913)
                                                                                                    	at android.app.PendingIntent.send(PendingIntent.java:884)
                                                                                                    	at com.android.server.AlarmManagerService$DeliveryTracker.deliverLocked(AlarmManagerService.java:4796)
                                                                                                    	at com.android.server.AlarmManagerService.deliverAlarmsLocked(AlarmManagerService.java:3926)
                                                                                                    	at com.android.server.AlarmManagerService$AlarmThread.run(AlarmManagerService.java:4175)
2025-08-21 18:07:09.244  4562-4562  Common                  com.dspread.mdm.service              I  ✅ ⏰ 设置任务执行定时器成功，下次执行: 60秒后 (1分钟)
2025-08-21 18:07:09.265  4562-4562  Receiver                com.dspread.mdm.service              D  🔧 收到轮询定时器广播
2025-08-21 18:07:09.268  4562-4562  WebSocket               com.dspread.mdm.service              I  🔧 WebSocketCenter: 发送心跳
2025-08-21 18:07:09.272  4562-4562  Receiver                com.dspread.mdm.service              D  🔧 检查待上传的任务和规则结果
2025-08-21 18:07:09.282  4562-4562  WebSocket               com.dspread.mdm.service              I  🔧 C0201 被动式上送: scheduled_report (被动: 10)
2025-08-21 18:07:09.303  4562-4562  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0201","serialNo":"01610040202307060227","request_time":"1755770829291","request_id":"1755770829291C0201","version":"1","data":{"unboxStatus":"normal","isInUse":"1","websocketConnected":true,"myVersionName":"1.1.01.20250821.DSPREAD.MDM.SERVICE","terminalDate":"20250821180709"},"myVersionName":"1.1.01.20250821.DSPREAD.MDM.SERVICE","terminalDate":"20250821180709"}
2025-08-21 18:07:09.306  4562-4562  WebSocket               com.dspread.mdm.service              I  🔧 C0201 设备状态上送成功: status=normal, inUse=true (1)
2025-08-21 18:07:09.314  4562-4562  Common                  com.dspread.mdm.service              I  ✅ ⏰ 设置心跳定时器成功，下次执行: 60秒后 (1分钟)
2025-08-21 18:07:09.443  4562-4627  WebSocket               com.dspread.mdm.service              I  🔧 收到服务器 PONG 帧: 3
2025-08-21 18:07:26.742  4562-4562  Receiver                com.dspread.mdm.service              D  🔧 电池状态更新: 电池状态: 电量=92%, 温度=27.7°C, 健康=2, 充电=是, 低电量=否, 耗尽=否
