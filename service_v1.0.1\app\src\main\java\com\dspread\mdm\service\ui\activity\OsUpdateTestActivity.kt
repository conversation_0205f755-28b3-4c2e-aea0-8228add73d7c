package com.dspread.mdm.service.ui.activity

import android.app.AlertDialog
import android.content.Intent
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.provider.DocumentsContract
import android.provider.OpenableColumns
import android.widget.Button
import android.widget.LinearLayout
import android.widget.ProgressBar
import android.widget.ScrollView
import android.widget.TextView
import android.widget.Toast
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.app.AppCompatActivity
import com.dspread.mdm.service.utils.log.Logger
import com.dspread.mdm.service.platform.api.device.DeviceInfoApi
import com.dspread.mdm.service.platform.api.system.SystemUpdateApi
import com.dspread.mdm.service.platform.api.system.SystemControlApi

import kotlinx.coroutines.*
import androidx.lifecycle.lifecycleScope
import java.io.File

/**
 * OS升级测试Activity
 * 专门用于测试系统升级相关功能
 */
class OsUpdateTestActivity : AppCompatActivity() {

    private lateinit var scrollView: ScrollView
    private lateinit var mainLayout: LinearLayout
    private lateinit var progressBar: ProgressBar
    private lateinit var progressText: TextView
    private lateinit var titleText: TextView
    private lateinit var filePathText: TextView
    private lateinit var versionInfoText: TextView

    private lateinit var systemUpdateApi: SystemUpdateApi
    private lateinit var systemControlApi: SystemControlApi

    // 升级状态管理
    private var isUpgrading = false
    private val buttonList = mutableListOf<Button>()

    // 升级监听器
    private val updateListener = object : SystemUpdateApi.UpdateStatusListener {
        override fun onStatusUpdate(status: String, progress: Int) {
            runOnUiThread {
                // 根据状态消息判断升级阶段（参考TaskHandler，增强A14 FINALIZING阶段）
                val phase = when {
                    status.contains("download", ignoreCase = true) ||
                    status.contains("下载", ignoreCase = true) -> "下载阶段"

                    status.contains("verify", ignoreCase = true) ||
                    status.contains("校验", ignoreCase = true) -> "校验阶段"

                    status.contains("install", ignoreCase = true) ||
                    status.contains("安装", ignoreCase = true) -> "安装阶段"

                    status.contains("apply", ignoreCase = true) ||
                    status.contains("应用", ignoreCase = true) -> "应用阶段"

                    status.contains("finalize", ignoreCase = true) ||
                    status.contains("finalizing", ignoreCase = true) -> {
                        // A14 FINALIZING阶段的详细说明
                        when {
                            progress < 100 -> "完成阶段"
                            progress >= 100 -> "后处理阶段"
                            else -> "完成阶段"
                        }
                    }

                    status.contains("完成", ignoreCase = true) -> "完成阶段"

                    else -> "升级中"
                }

                // 特殊处理FINALIZING 100%阶段
                if (status.contains("finalizing", ignoreCase = true) && progress >= 100) {
                    val detailedMessage = "A/B分区切换准备中，请耐心等待3-5分钟"
                    showProgress(progress, detailedMessage, "后处理阶段")
                    log("升级进度更新: 后处理阶段 - $detailedMessage ($progress%)")
                    log("注意：FINALIZING 100%后需要等待A/B分区切换完成")
                } else {
                    showProgress(progress, status, phase)
                    log("升级进度更新: $phase - $status ($progress%)")
                }
            }
        }

        override fun onUpdateCompleted(success: Boolean, message: String) {
            runOnUiThread {
                setUpgradeState(false)
                if (success) {
                    showProgress(100, "升级完成", "完成")
                    log("升级完成: $message")

                    val completionMessage = if (Build.VERSION.SDK_INT >= 34) {
                        "A/B分区升级完成！\n\n设备将在下次重启时切换到新系统。\n建议现在重启设备以应用更新。"
                    } else {
                        "系统升级完成！\n\n设备将自动重启以应用更新。"
                    }

                    AlertDialog.Builder(this@OsUpdateTestActivity)
                        .setTitle("升级完成")
                        .setMessage(completionMessage)
                        .setPositiveButton("立即重启") { _, _ ->
                            // 重启设备
                            try {
                                Runtime.getRuntime().exec("su -c reboot")
                            } catch (e: Exception) {
                                log("重启失败: ${e.message}")
                                Toast.makeText(this@OsUpdateTestActivity, "请手动重启设备", Toast.LENGTH_LONG).show()
                            }
                        }
                        .setNegativeButton("稍后重启", null)
                        .show()
                } else {
                    showProgress(0, "升级失败", "错误")
                    log("升级失败: $message")
                    Toast.makeText(this@OsUpdateTestActivity, "升级失败: $message", Toast.LENGTH_LONG).show()
                }
            }
        }
    }

    // 文件选择器
    private var selectedUpdateFilePath: String? = null
    private val filePickerLauncher = registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result ->
        if (result.resultCode == RESULT_OK) {
            result.data?.data?.let { uri ->
                handleSelectedFile(uri)
            }
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // 初始化API
        systemUpdateApi = SystemUpdateApi(this)
        systemControlApi = SystemControlApi(this)

        setupUI()
        addOsUpdateButtons()
        log("🚀 OS升级功能测试启动")
        log("设备序列号: ${DeviceInfoApi(this).getSerialNumber()}")
        log("OsUpdateTestActivity 初始化完成")
    }

    override fun onDestroy() {
        super.onDestroy()
    }

    /**
     * 处理用户选择的文件
     */
    private fun handleSelectedFile(uri: Uri) {
        try {
            // 获取文件路径
            val filePath = getFilePathFromUri(uri)
            if (filePath != null && File(filePath).exists()) {
                selectedUpdateFilePath = filePath
                val fileName = File(filePath).name
                val fileSize = File(filePath).length() / (1024 * 1024)

                // 更新界面显示
                filePathText.text = "已选择: $fileName (${fileSize}MB)"
                filePathText.setTextColor(0xFF4CAF50.toInt()) // 绿色表示已选择

                log("已选择升级包: $filePath")
                Toast.makeText(this, "已选择升级包: $fileName", Toast.LENGTH_SHORT).show()
            } else {
                log("无法获取文件路径或文件不存在")
                Toast.makeText(this, "无法访问选择的文件", Toast.LENGTH_SHORT).show()
            }
        } catch (e: Exception) {
            log("处理选择文件失败: ${e.message}")
            Toast.makeText(this, "处理文件失败: ${e.message}", Toast.LENGTH_SHORT).show()
        }
    }

    /**
     * 从Uri获取文件路径
     */
    private fun getFilePathFromUri(uri: Uri): String? {
        return try {
            when (uri.scheme) {
                "file" -> {
                    // 直接文件路径
                    uri.path
                }
                "content" -> {
                    // 尝试获取真实路径
                    val cursor = contentResolver.query(uri, null, null, null, null)
                    cursor?.use {
                        if (it.moveToFirst()) {
                            val columnIndex = it.getColumnIndex("_data")
                            if (columnIndex >= 0) {
                                val realPath = it.getString(columnIndex)
                                if (!realPath.isNullOrEmpty() && File(realPath).exists()) {
                                    return realPath
                                }
                            }
                        }
                    }

                    // 如果无法获取真实路径，复制文件到临时位置（保持原始文件名）
                    log("无法获取直接路径，复制文件到临时位置")
                    copyFileFromUri(uri)
                }
                else -> {
                    log("不支持的Uri scheme: ${uri.scheme}")
                    null
                }
            }
        } catch (e: Exception) {
            log("获取文件路径失败: ${e.message}")
            null
        }
    }

    /**
     * 从Uri复制文件到临时位置，保持原始文件名
     */
    private fun copyFileFromUri(uri: Uri): String? {
        return try {
            // 尝试获取原始文件名
            var originalFileName = "update.zip"

            // 从Uri获取文件名
            contentResolver.query(uri, null, null, null, null)?.use { cursor ->
                if (cursor.moveToFirst()) {
                    val nameIndex = cursor.getColumnIndex(android.provider.OpenableColumns.DISPLAY_NAME)
                    if (nameIndex >= 0) {
                        val fileName = cursor.getString(nameIndex)
                        if (!fileName.isNullOrEmpty()) {
                            originalFileName = fileName
                        }
                    }
                }
            }

            // 确保文件名以.zip结尾
            if (!originalFileName.lowercase().endsWith(".zip")) {
                originalFileName = "${originalFileName.substringBeforeLast('.')}.zip"
            }

            val inputStream = contentResolver.openInputStream(uri)
            val tempFile = File(cacheDir, originalFileName)

            inputStream?.use { input ->
                tempFile.outputStream().use { output ->
                    input.copyTo(output)
                }
            }

            log("文件复制成功: ${originalFileName}")
            tempFile.absolutePath
        } catch (e: Exception) {
            log("复制文件失败: ${e.message}")
            null
        }
    }

    /**
     * 选择升级文件
     */
    private fun selectUpdateFile() {
        try {
            val intent = Intent(Intent.ACTION_GET_CONTENT).apply {
                type = "application/zip"
                addCategory(Intent.CATEGORY_OPENABLE)
                putExtra(Intent.EXTRA_MIME_TYPES, arrayOf("application/zip", "application/x-zip-compressed"))
            }

            filePickerLauncher.launch(Intent.createChooser(intent, "选择OTA升级包"))
        } catch (e: Exception) {
            log("启动文件选择器失败: ${e.message}")
            Toast.makeText(this, "无法打开文件选择器", Toast.LENGTH_SHORT).show()
        }
    }
    
    private fun setupUI() {
        // 创建主内容布局
        mainLayout = LinearLayout(this).apply {
            orientation = LinearLayout.VERTICAL
            setPadding(16, 16, 16, 16)
        }

        // 创建标题
        titleText = TextView(this).apply {
            text = "OS升级功能测试"
            textSize = 18f
            setPadding(0, 0, 0, 8)
            setTextColor(0xFF2196F3.toInt())
        }
        mainLayout.addView(titleText)

        // 创建文件路径显示
        filePathText = TextView(this).apply {
            text = "未选择升级包"
            textSize = 12f
            setPadding(0, 0, 0, 8)
            setTextColor(0xFF666666.toInt())
            visibility = android.view.View.VISIBLE
        }
        mainLayout.addView(filePathText)

        // 创建版本信息显示
        versionInfoText = TextView(this).apply {
            text = ""
            textSize = 11f
            setPadding(0, 0, 0, 16)
            setTextColor(0xFF888888.toInt())
            visibility = android.view.View.GONE
        }
        mainLayout.addView(versionInfoText)

        // 创建进度条
        progressBar = ProgressBar(this, null, android.R.attr.progressBarStyleHorizontal).apply {
            layoutParams = LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.MATCH_PARENT,
                LinearLayout.LayoutParams.WRAP_CONTENT
            ).apply {
                setMargins(0, 0, 0, 8)
            }
            max = 100
            progress = 0
            visibility = android.view.View.GONE
            // 设置进度条样式
            progressDrawable?.setColorFilter(0xFF2196F3.toInt(), android.graphics.PorterDuff.Mode.SRC_IN)
        }
        mainLayout.addView(progressBar)

        // 创建进度文本
        progressText = TextView(this).apply {
            text = ""
            textSize = 14f
            setTextColor(0xFF2196F3.toInt())
            layoutParams = LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.MATCH_PARENT,
                LinearLayout.LayoutParams.WRAP_CONTENT
            ).apply {
                setMargins(0, 0, 0, 16)
            }
            visibility = android.view.View.GONE
            // 设置文本居中
            gravity = android.view.Gravity.CENTER
        }
        mainLayout.addView(progressText)

        // 移除屏幕日志显示区域，只保留系统日志输出

        // 创建整体滚动视图
        scrollView = ScrollView(this).apply {
            addView(mainLayout)
        }

        setContentView(scrollView)
    }
    
    private fun addOsUpdateButtons() {
        log("添加OS升级测试按钮...")

        // 核心功能按钮
        addButton("获取系统信息") { showSystemInfoDialog() }
        addButton("选择升级包") { selectUpdateFile() }
        addButton("执行OTA升级") { performOtaUpgrade() }
        addButton("升级机制说明") { showUpgradeMechanismInfo() }

        log("OS升级测试按钮添加完成")
    }

    private fun addButton(text: String, onClick: () -> Unit) {
        val button = Button(this).apply {
            this.text = text
            textSize = 14f
            setPadding(16, 12, 16, 12)
            layoutParams = LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.MATCH_PARENT,
                LinearLayout.LayoutParams.WRAP_CONTENT
            ).apply {
                setMargins(0, 8, 0, 0)
            }
            setOnClickListener {
                // 检查是否正在升级
                if (isUpgrading && text != "返回主页") {
                    Toast.makeText(this@OsUpdateTestActivity, "正在升级中，请等待升级完成", Toast.LENGTH_SHORT).show()
                    return@setOnClickListener
                }

                log("=== $text ===")
                try {
                    onClick()
                } catch (e: Exception) {
                    log("操作失败: ${e.message}")
                    Logger.comE("OS升级测试失败: $text", e)
                    Toast.makeText(this@OsUpdateTestActivity, "操作失败: ${e.message}", Toast.LENGTH_LONG).show()
                }
            }
        }
        buttonList.add(button)
        mainLayout.addView(button)
    }

    // ==================== OS升级测试方法 ====================

    /**
     * 显示系统信息对话框
     */
    private fun showSystemInfoDialog() {
        log("获取系统信息...")

        lifecycleScope.launch {
            try {
                val systemInfo = withContext(Dispatchers.IO) {
                    systemUpdateApi.getCurrentSystemInfo()
                }

                val infoText = buildString {
                    appendLine("设备型号: ${Build.MODEL}")
                    appendLine("Android版本: ${systemInfo.currentVersion}")
                    appendLine("API级别: ${systemInfo.sdkVersion}")
                    appendLine("构建版本: ${systemInfo.buildNumber}")
                    appendLine("制造商: ${Build.MANUFACTURER}")
                    appendLine("品牌: ${Build.BRAND}")
                    appendLine("硬件: ${Build.HARDWARE}")
                    appendLine("主板: ${Build.BOARD}")
                    appendLine("设备: ${Build.DEVICE}")
                    appendLine("产品: ${Build.PRODUCT}")
                    appendLine("安全补丁: ${systemInfo.securityPatch}")
                    appendLine("构建指纹: ${systemInfo.buildFingerprint}")
                    appendLine("构建ID: ${systemInfo.buildId}")
                    appendLine("构建类型: ${systemInfo.buildType}")
                    appendLine("增量版本: ${systemInfo.incrementalVersion}")
                }

                AlertDialog.Builder(this@OsUpdateTestActivity)
                    .setTitle("系统信息")
                    .setMessage(infoText)
                    .setPositiveButton("确定", null)
                    .show()

                log("系统信息获取成功")

            } catch (e: Exception) {
                log("获取系统信息失败: ${e.message}")
                Toast.makeText(this@OsUpdateTestActivity, "获取系统信息失败: ${e.message}", Toast.LENGTH_LONG).show()
            }
        }
    }



    /**
     * 执行OTA升级（包含实时状态监控）
     */
    private fun performOtaUpgrade() {
        if (isUpgrading) {
            Toast.makeText(this, "升级已在进行中", Toast.LENGTH_SHORT).show()
            return
        }

        log("开始执行OTA升级...")
        log("注意：这将开始系统升级过程")

        // 设置升级状态
        setUpgradeState(true)

        lifecycleScope.launch {
            try {
                // 智能查找OTA升级包
                var updateFile: File? = null
                var updateFilePath = ""

                // 1. 优先使用用户选择的文件
                if (!selectedUpdateFilePath.isNullOrEmpty()) {
                    val selectedFile = File(selectedUpdateFilePath!!)
                    if (selectedFile.exists()) {
                        updateFile = selectedFile
                        updateFilePath = selectedUpdateFilePath!!
                        log("使用用户选择的升级包: $updateFilePath")
                    }
                }

                // 2. 如果没有选择文件，检查常见路径
                if (updateFile == null) {
                    val commonPaths = listOf(
                        "/sdcard/update.zip",
                        "/sdcard/system_update.zip",
                        "/sdcard/ota_update.zip",
                        "/sdcard/Downloads/update.zip",
                        "/sdcard/Downloads/system_update.zip"
                    )

                    for (path in commonPaths) {
                        val file = File(path)
                        if (file.exists()) {
                            updateFile = file
                            updateFilePath = path
                            break
                        }
                    }
                }

                // 3. 如果常见路径没找到，扫描/sdcard目录下的所有ZIP文件
                if (updateFile == null) {
                    updateFile = findOtaPackageInDirectory("/sdcard")
                    updateFilePath = updateFile?.absolutePath ?: ""
                }

                runOnUiThread {
                    if (updateFile == null) {
                        val message = "未找到升级文件，请选择以下方式之一：\n\n" +
                                "方式1: 点击\"选择升级包\"按钮手动选择文件\n\n" +
                                "方式2: 将OTA包(.zip文件)放置到以下位置：\n" +
                                "• /sdcard/update.zip\n" +
                                "• /sdcard/system_update.zip\n" +
                                "• /sdcard/Downloads/update.zip\n" +
                                "• 或/sdcard目录下任意ZIP文件\n\n" +
                                "推荐使用方式1，更加灵活方便"

                        AlertDialog.Builder(this@OsUpdateTestActivity)
                            .setTitle("升级包未找到")
                            .setMessage(message as CharSequence)
                            .setPositiveButton("选择文件") { dialog, which ->
                                setUpgradeState(false) // 重置升级状态
                                selectUpdateFile() // 直接打开文件选择器
                            }
                            .setNegativeButton("取消") { dialog, which ->
                                setUpgradeState(false) // 重置升级状态
                            }
                            .show()

                        log("未找到升级文件")
                        setUpgradeState(false) // 重置升级状态
                        return@runOnUiThread
                    }

                    log("找到升级文件: $updateFilePath")
                    log("文件名: ${updateFile!!.name}")
                    log("文件大小: ${updateFile.length() / (1024 * 1024)}MB")

                    // 检查Android版本和升级策略
                    val upgradeStrategy = if (Build.VERSION.SDK_INT >= 34) {
                        "A/B分区无缝升级 (UpdateEngine)"
                    } else {
                        "传统升级 (RecoverySystem)"
                    }
                    log("升级策略: $upgradeStrategy")

                    // 参考TaskHandler，添加升级前检查
                    showProgress(5, "检查系统版本", "准备阶段")

                    // 检查当前版本（参考TaskHandler的getCurrentSystemVersion）
                    val currentVersion = Build.DISPLAY ?: ""
                    log("当前系统版本: $currentVersion")

                    // Android 14+特殊检查（参考TaskHandler）
                    if (Build.VERSION.SDK_INT >= 34) {
                        log("Android 14+设备，执行升级前检查...")
                        log("注意：Android 14+对升级包时间戳有严格要求")
                        showProgress(8, "Android 14+升级检查", "准备阶段")
                    }

                    showProgress(10, "验证升级包", "准备阶段")

                    // 验证升级包（参考TaskHandler的verifyOSUpdateFile）
                    if (!verifyUpdateFile(updateFile)) {
                        log("升级包验证失败")
                        showProgress(0, "升级包验证失败", "错误")
                        setUpgradeState(false)
                        Toast.makeText(this@OsUpdateTestActivity, "升级包验证失败，请检查文件完整性", Toast.LENGTH_LONG).show()
                        return@runOnUiThread
                    }

                    showProgress(15, "升级包验证通过", "准备阶段")

                    // 版本比较检查（使用SystemUpdateApi）
                    showProgress(18, "检查版本兼容性", "准备阶段")
                    val versionCheckResult = systemUpdateApi.checkVersionCompatibility(updateFile!!)

                    // 在页面显示版本信息
                    updateVersionDisplay(versionCheckResult)

                    // 处理版本检查结果
                    handleVersionCheckResult(versionCheckResult, updateFile)
                }

            } catch (e: Exception) {
                runOnUiThread {
                    log("OTA升级失败: ${e.message}")
                    setUpgradeState(false)
                    showProgress(0, "升级异常", "错误")
                    Toast.makeText(this@OsUpdateTestActivity, "OTA升级失败: ${e.message}", Toast.LENGTH_LONG).show()
                }
            }
        }
    }

    // ==================== 辅助方法 ====================

    /**
     * 显示升级进度
     * @param progress 进度百分比 (0-100)
     * @param message 状态消息
     * @param phase 升级阶段 (可选)
     */
    private fun showProgress(progress: Int, message: String, phase: String? = null) {
        progressBar.visibility = android.view.View.VISIBLE
        progressText.visibility = android.view.View.VISIBLE
        progressBar.progress = progress

        // 构建显示文本
        val displayText = if (phase != null) {
            "$phase: $message ($progress%)"
        } else {
            "$message ($progress%)"
        }

        progressText.text = displayText

        // 根据进度设置颜色
        val color = when {
            progress >= 100 -> 0xFF4CAF50.toInt() // 绿色 - 完成
            progress >= 50 -> 0xFF2196F3.toInt()  // 蓝色 - 进行中
            progress > 0 -> 0xFFFF9800.toInt()    // 橙色 - 开始
            else -> 0xFFF44336.toInt()            // 红色 - 错误
        }
        progressText.setTextColor(color)

        log("升级进度: $displayText")
    }

    private fun hideProgress() {
        progressBar.visibility = android.view.View.GONE
        progressText.visibility = android.view.View.GONE
    }

    /**
     * 设置升级状态
     */
    private fun setUpgradeState(upgrading: Boolean) {
        isUpgrading = upgrading

        // 禁用/启用按钮（除了返回按钮）
        buttonList.forEach { button ->
            if (button.text != "返回主页") {
                button.isEnabled = !upgrading
                button.alpha = if (upgrading) 0.5f else 1.0f
            }
        }
    }



    /**
     * 显示升级机制说明
     */
    private fun showUpgradeMechanismInfo() {
        val mechanismInfo = if (Build.VERSION.SDK_INT >= 34) {
            buildString {
                appendLine("Android 14+ A/B分区无缝升级机制")
                appendLine("=".repeat(35))
                appendLine()
                appendLine("升级阶段说明：")
                appendLine("1. 准备阶段 - 初始化升级环境")
                appendLine("2. 下载阶段 - 下载升级包到非活动分区")
                appendLine("3. 校验阶段 - 验证升级包完整性")
                appendLine("4. 安装阶段 - 安装新系统到非活动分区")
                appendLine("5. 应用阶段 - 应用系统配置")
                appendLine("6. 完成阶段 - 标记升级完成")
                appendLine()
                appendLine("特点：")
                appendLine("• 升级过程中设备可正常使用")
                appendLine("• 升级失败可自动回滚")
                appendLine("• 重启后切换到新系统分区")
                appendLine("• 支持后台静默升级")
            }
        } else {
            buildString {
                appendLine("Android <14 传统升级机制")
                appendLine("=".repeat(30))
                appendLine()
                appendLine("升级阶段说明：")
                appendLine("1. 准备阶段 - 下载升级包")
                appendLine("2. 校验阶段 - 验证升级包")
                appendLine("3. 重启阶段 - 重启到Recovery模式")
                appendLine("4. 安装阶段 - 在Recovery中安装")
                appendLine("5. 完成阶段 - 重启到新系统")
                appendLine()
                appendLine("特点：")
                appendLine("• 升级过程中设备不可使用")
                appendLine("• 需要重启到Recovery模式")
                appendLine("• 升级失败需要手动恢复")
            }
        }

        AlertDialog.Builder(this)
            .setTitle("升级机制说明")
            .setMessage(mechanismInfo)
            .setPositiveButton("确定", null)
            .show()
    }

    private fun log(message: String) {
        // 只输出到系统日志，不显示在屏幕上
        Logger.com("OsUpdateTest: $message")
    }

    /**
     * 在指定目录下查找OTA升级包
     * 查找所有ZIP文件，优先选择包含"update"、"ota"、"system"关键词的文件
     */
    private fun findOtaPackageInDirectory(directoryPath: String): File? {
        return try {
            val directory = File(directoryPath)
            if (!directory.exists() || !directory.isDirectory) {
                return null
            }

            // 获取所有ZIP文件
            val zipFiles = directory.listFiles { file ->
                file.isFile && file.name.lowercase().endsWith(".zip")
            }?.toList() ?: emptyList()

            if (zipFiles.isEmpty()) {
                return null
            }

            // 优先选择包含关键词的文件
            val keywords = listOf("update", "ota", "system", "upgrade")
            val priorityFile = zipFiles.find { file ->
                val fileName = file.name.lowercase()
                keywords.any { keyword -> fileName.contains(keyword) }
            }

            val selectedFile = priorityFile ?: zipFiles.first()
            log("在${directoryPath}目录找到ZIP文件: ${selectedFile.name}")

            selectedFile

        } catch (e: Exception) {
            log("扫描目录失败: ${e.message}")
            null
        }
    }

    /**
     * 验证升级包（参考TaskHandler的verifyOSUpdateFile）
     */
    private fun verifyUpdateFile(file: File): Boolean {
        return try {
            if (!file.exists()) {
                log("升级包文件不存在")
                return false
            }

            if (file.length() == 0L) {
                log("升级包文件大小为0")
                return false
            }

            // 检查文件扩展名
            if (!file.name.lowercase().endsWith(".zip")) {
                log("升级包文件格式不正确，必须是ZIP文件")
                return false
            }

            // 简单的ZIP文件头检查
            file.inputStream().use { input ->
                val header = ByteArray(4)
                val bytesRead = input.read(header)
                if (bytesRead < 4) {
                    log("升级包文件头读取失败")
                    return false
                }

                // ZIP文件头标识: 50 4B 03 04
                if (header[0] != 0x50.toByte() || header[1] != 0x4B.toByte() ||
                    header[2] != 0x03.toByte() || header[3] != 0x04.toByte()) {
                    log("升级包不是有效的ZIP文件")
                    return false
                }
            }

            log("升级包验证通过: ${file.name}, 大小: ${file.length() / (1024 * 1024)}MB")
            true

        } catch (e: Exception) {
            log("升级包验证异常: ${e.message}")
            false
        }
    }



    /**
     * 更新版本信息显示
     */
    private fun updateVersionDisplay(versionResult: SystemUpdateApi.VersionCheckResult) {
        runOnUiThread {
            val statusText = when (versionResult.status) {
                SystemUpdateApi.VersionCheckStatus.SAME_VERSION -> "版本相同"
                SystemUpdateApi.VersionCheckStatus.OLDER_VERSION -> "降级操作"
                SystemUpdateApi.VersionCheckStatus.NEWER_VERSION -> "正常升级"
                SystemUpdateApi.VersionCheckStatus.UNKNOWN -> "版本未知"
            }

            val statusColor = when (versionResult.status) {
                SystemUpdateApi.VersionCheckStatus.SAME_VERSION -> 0xFFFF9800.toInt() // 橙色
                SystemUpdateApi.VersionCheckStatus.OLDER_VERSION -> 0xFFF44336.toInt() // 红色
                SystemUpdateApi.VersionCheckStatus.NEWER_VERSION -> 0xFF4CAF50.toInt() // 绿色
                SystemUpdateApi.VersionCheckStatus.UNKNOWN -> 0xFF888888.toInt() // 灰色
            }

            // 格式化版本显示
            val currentVersionDisplay = formatVersionForDisplay(versionResult.currentVersion)
            val updateVersionDisplay = formatVersionForDisplay(versionResult.updateVersion)

            val versionInfo = buildString {
                appendLine("当前版本: $currentVersionDisplay")
                appendLine("升级包版本: $updateVersionDisplay")
                append("状态: $statusText")
            }

            versionInfoText.text = versionInfo
            versionInfoText.setTextColor(statusColor)
            versionInfoText.visibility = android.view.View.VISIBLE

            log("版本信息显示: $statusText")
        }
    }

    /**
     * 版本显示格式化（保持完整信息）
     */
    private fun formatVersionForDisplay(fullVersion: String): String {
        // 直接返回完整版本，不做任何截断或格式化
        return fullVersion
    }

    /**
     * 处理版本检查结果
     */
    private fun handleVersionCheckResult(versionCheckResult: SystemUpdateApi.VersionCheckResult, updateFile: File?) {
        when (versionCheckResult.status) {
            SystemUpdateApi.VersionCheckStatus.SAME_VERSION -> {
                log("同版本升级被阻止：A/B分区机制不支持相同版本重复安装")
                showProgress(0, "同版本升级不支持", "阻止")

                // 格式化版本显示
                val currentVersionDisplay = formatVersionForDisplay(versionCheckResult.currentVersion)
                val updateVersionDisplay = formatVersionForDisplay(versionCheckResult.updateVersion)

                // 直接阻止同版本升级，显示技术说明
                AlertDialog.Builder(this@OsUpdateTestActivity)
                    .setTitle("升级阻止")
                    .setMessage("检测到升级包版本与当前系统版本相同：\n\n" +
                            "当前版本: $currentVersionDisplay\n" +
                            "升级包版本: $updateVersionDisplay\n\n" +
                            "A/B分区升级机制不支持相同版本的重复安装，强制升级可能导致系统分区哈希验证失败。\n\n" +
                            "如需重装系统，请联系技术支持获取专用重装工具。")
                    .setPositiveButton("确定") { _, _ ->
                        setUpgradeState(false)
                    }
                    .setCancelable(false)
                    .show()
            }
            SystemUpdateApi.VersionCheckStatus.OLDER_VERSION -> {
                log("警告：升级包版本低于当前系统版本")
                showProgress(19, "等待用户确认", "准备阶段")

                // 格式化版本显示
                val currentVersionDisplay = formatVersionForDisplay(versionCheckResult.currentVersion)
                val updateVersionDisplay = formatVersionForDisplay(versionCheckResult.updateVersion)

                // 显示确认对话框并等待用户选择
                showVersionConfirmDialogAsync(
                    title = "降级操作警告",
                    message = "检测到升级包版本低于当前系统版本：\n\n" +
                            "当前版本: $currentVersionDisplay\n" +
                            "升级包版本: $updateVersionDisplay\n\n" +
                            "这是降级操作，可能导致系统不稳定。\n是否继续？",
                    onConfirm = {
                        log("用户确认继续降级操作")
                        continueUpgradeProcess(updateFile!!, "test_upgrade_${System.currentTimeMillis()}")
                    },
                    onCancel = {
                        log("用户取消了降级操作")
                        showProgress(0, "用户取消降级", "取消")
                        setUpgradeState(false)
                    }
                )
            }
            SystemUpdateApi.VersionCheckStatus.NEWER_VERSION -> {
                log("升级包版本高于当前系统版本，正常升级")
                showProgress(20, "版本检查完成", "准备阶段")
                val taskId = "test_upgrade_${System.currentTimeMillis()}"
                continueUpgradeProcess(updateFile!!, taskId)
            }
            SystemUpdateApi.VersionCheckStatus.UNKNOWN -> {
                log("无法确定版本关系，继续升级")
                showProgress(20, "版本检查完成", "准备阶段")
                val taskId = "test_upgrade_${System.currentTimeMillis()}"
                continueUpgradeProcess(updateFile!!, taskId)
            }
        }
    }

    /**
     * 显示异步版本确认对话框
     */
    private fun showVersionConfirmDialogAsync(
        title: String,
        message: String,
        onConfirm: () -> Unit,
        onCancel: () -> Unit
    ) {
        try {
            AlertDialog.Builder(this)
                .setTitle(title)
                .setMessage(message)
                .setPositiveButton("继续升级") { _, _ ->
                    log("用户选择继续升级")
                    onConfirm()
                }
                .setNegativeButton("取消") { _, _ ->
                    log("用户选择取消升级")
                    onCancel()
                }
                .setCancelable(false)
                .show()
        } catch (e: Exception) {
            log("显示版本确认对话框失败: ${e.message}")
            onCancel()
        }
    }

    /**
     * 继续升级流程
     */
    private fun continueUpgradeProcess(updateFile: File, taskId: String) {
        lifecycleScope.launch {
            try {
                val updateFilePath = updateFile.absolutePath

                showProgress(25, "启动升级引擎", "初始化")
                log("开始执行OS升级，taskId: $taskId")

                val result = systemUpdateApi.installOtaUpdate(updateFilePath, taskId, updateListener)

                runOnUiThread {
                    if (result.isSuccess) {
                        log("升级启动成功，taskId: $taskId")
                        showProgress(30, "升级引擎已启动", "初始化")

                        val startMessage = if (Build.VERSION.SDK_INT >= 34) {
                            "A/B分区升级已开始！\n\n升级过程中设备可正常使用，升级完成后重启生效。"
                        } else {
                            "系统升级已开始！\n\n升级过程中请勿关闭设备。"
                        }

                        Toast.makeText(this@OsUpdateTestActivity, startMessage, Toast.LENGTH_LONG).show()

                    } else {
                        val errorMessage = if (result.isFailure) {
                            result.toString()
                        } else {
                            "未知错误"
                        }
                        log("升级启动失败: $errorMessage")
                        showProgress(0, "升级启动失败", "错误")
                        setUpgradeState(false)

                        Toast.makeText(this@OsUpdateTestActivity,
                            "升级启动失败: $errorMessage",
                            Toast.LENGTH_LONG).show()
                    }
                }

            } catch (e: Exception) {
                log("升级过程异常: ${e.message}")
                runOnUiThread {
                    showProgress(0, "升级异常", "错误")
                    setUpgradeState(false)
                    Toast.makeText(this@OsUpdateTestActivity, "升级异常: ${e.message}", Toast.LENGTH_LONG).show()
                }
            }
        }
    }
}
