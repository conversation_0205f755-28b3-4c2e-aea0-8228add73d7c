package com.bbpos.wiseapp.tms.adapter;

import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.ServiceConnection;
import android.content.SharedPreferences;
import android.os.IBinder;
import android.os.RemoteException;
import android.preference.PreferenceManager;

import com.bbpos.wiseapp.logger.BBLog;
import com.bbpos.wiseapp.system.api.Helper;
import com.bbpos.wiseapp.tms.utils.ContextUtil;
import com.bbpos.wiseapp.tms.utils.Constants;
import com.bbpos.wiseapp.tms.utils.Helpers;
import com.bbpos.wiseapp.tms.utils.SPKeys;
import com.bbpos.wiseapp.utils.CertificateTest;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.bbpos.wiseapp.sdk.app.UsageStats;

public class SystemManagerAdapter {
	private static ISystemManger cloudSystemManager;

	public static boolean isServiceBinded(){
		if(cloudSystemManager == null)
			return false;
		return cloudSystemManager.isServiceBinded();
	}

	public static void bindBBposService(Context context, final BindServiceSuccess callBack){
		callBack.onBindSuccess();
	}

	public static void bindService(Context context,final BindServiceSuccess callBack){
		if (!isServiceBinded()) {

			ServiceConnection mConnection = new ServiceConnection() {
				public void onServiceConnected(ComponentName className, IBinder service) {
					cloudSystemManager.initCloudManager(service);
					callBack.onBindSuccess();
				}

				public void onServiceDisconnected(ComponentName className) {
					BBLog.w("onServiceDisconnected", "bindService---.");
					cloudSystemManager.unBindCloudManager();
				}
			};
			String action = Constants.SERVICE_SYSTEM_MANAGER;
			String pkgName = Helpers.getPkgNameByServiceName(action);
			cloudSystemManager = new SystemManagerV2Adapter();
			
			Intent serviceIntent = new Intent(action);
			serviceIntent.setPackage(pkgName);
			ContextUtil.getInstance().bindService(serviceIntent, mConnection, Context.BIND_AUTO_CREATE);
		} else {
			callBack.onBindSuccess();
		}
	}

	public static void bindServiceInit(Context context,final BindServiceSuccess callBack){
			ServiceConnection mConnection = new ServiceConnection() {
				public void onServiceConnected(ComponentName className, IBinder service) {
					cloudSystemManager.initCloudManager(service);
					callBack.onBindSuccess();
				}

				public void onServiceDisconnected(ComponentName className) {
					BBLog.w("onServiceDisconnected", "bindServiceInit.----");
					cloudSystemManager.unBindCloudManager();
				}
			};
			String action = Constants.SERVICE_SYSTEM_MANAGER;
			String pkgName = Helpers.getPkgNameByServiceName(action);
			cloudSystemManager = new SystemManagerV2Adapter();
			
			Intent serviceIntent = new Intent(action);
			serviceIntent.setPackage(pkgName);
			ContextUtil.getInstance().bindService(serviceIntent, mConnection, Context.BIND_AUTO_CREATE);
	}
	
	public static void initTerParam(final Context context) {
		bindService(context, new BindServiceSuccess() {
			@Override
			public void onBindSuccess() {
				cloudSystemManager.initTerParam(context);
			}
		});
	}

	public static void reboot(final Context context) {
		BBLog.w(BBLog.TAG, "重啓設備");
		bindService(context, new BindServiceSuccess() {
			@Override
			public void onBindSuccess() {
				cloudSystemManager.reboot(context);
			}
		});
	}

	/**
	 * 批量顺序安装apk
	 * @param context
	 * @param apkPathArray
	 * @param callback
	 */
	public static void intallApks(final Context context, final String[] apkPathArray,
			final AllApkInstallCompleted callback) {
		startApkIntall(context, apkPathArray, new ISystemManger.ApkIndex(0), new HashMap<String,Integer>(), callback);
	}

	public static void installApk(final Context context,String appPath,final ApkInstallCompleted observer){
		try {
			if (!CertificateTest.verfySignFile(appPath)) {
				BBLog.e(Helper.TAG, "verfySignFile Failed!");
				observer.onInstallFinished("" ,99);
				return;
			} else {
				appPath = appPath + Constants.APK_ORIGINAL_SUFFIX;
			}

			final String finalAppPath = appPath;
			cloudSystemManager.installApk(context, finalAppPath,observer);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	public static void updateSystem(final Context context,final String osFilePath,final int updateType){
		bindService(context, new BindServiceSuccess() {
			@Override
			public void onBindSuccess() {
				if (osFilePath != null) {
					BBLog.e("updateSystem", "osFilePath:" + osFilePath);
				}
				cloudSystemManager.updateSystem(context,osFilePath,updateType);
			}
		});
	}
	
	/***
	 * 批量卸载apk
	 * @param context
	 * @param apkPkgs
	 * @param callback
	 */
	public static void unInstallApks(final Context context,final String[] apkPkgs,final AllApkUnInstallCompleted callback){
		startApkUnintall(context, apkPkgs, new ISystemManger.ApkIndex(0), new HashMap<String,Integer>(), callback);
	}

	public static void unInstallApk(final Context context,final String apkPkgs,final ApkUnInstallCompleted observer){
		bindService(context, new BindServiceSuccess() {
			@Override
			public void onBindSuccess() {
				cloudSystemManager.unInstallApk(context,apkPkgs,observer);
			}
		});
	}
	
	public static void backupByPkgName(final List<String> pkgList,final BackupCompleted listener){
		bindService(null, new BindServiceSuccess() {
			@Override
			public void onBindSuccess() {
				cloudSystemManager.backupByPkgName(pkgList,listener);
			}
		});
	}
	
	public static void restore(final String path, final RecoveryCompleted listener) {
		bindService(null, new BindServiceSuccess() {
			@Override
			public void onBindSuccess() {
				cloudSystemManager.restore(path, listener);
			}
		});
	}
	
	public static void getDeviceStatus(final Context context,final int type,final IDeviceStatusCellectObserver observer){
		bindService(context, new BindServiceSuccess() {
			@Override
			public void onBindSuccess() {
				observer.DeviceStatusCallback(cloudSystemManager.getDeviceStatus(type));
			}
		});
	}
	public static void getUsageStats(final Context context,final String yyyyMMdd,final IUsageStatsObserver observer)
	{
		bindService(context, new BindServiceSuccess() {
			@Override
			public void onBindSuccess() {
				observer.UsageStatsListCallback(cloudSystemManager.getUsageStats(yyyyMMdd));
			}
		});	
	}
	
	public static void getUsageStatsWeek(final Context context,final List<String> dateNeedUoploadList,final IUsageStatsObserver observer)
	{
		final  List<String> dateNeedUoploadListtmp = dateNeedUoploadList;
		bindService(context, new BindServiceSuccess() {
			@Override
			public void onBindSuccess() {
				List<UsageStats> usageList = new ArrayList<UsageStats>();
				for (String yyyymmddtmp :  dateNeedUoploadListtmp) {
					List<UsageStats> usageListtmp =  cloudSystemManager.getUsageStats(yyyymmddtmp);
					if (usageListtmp != null)
						for (UsageStats usage : usageListtmp) {
							usageList.add(new UsageStats(
									usage.getPackageName(),
									usage.getLaunchCount(),
									usage.getTotalTime(),
									usage.getCollectDate()));
						}
					}
				observer.UsageStatsListCallback(usageList);
			}
		});	
	}	
	
	
	/**
	 * 轮询调用安装函数
	 * 
	 * @throws RemoteException
	 */
	@SuppressWarnings("rawtypes")
	private static void startApkIntall(final Context context, final String[] apkPathArray, final ISystemManger.ApkIndex currApkIndex, final Map resultMap,
									   final AllApkInstallCompleted callback){
		ApkInstallCompleted observer = new ApkInstallCompleted() {
			@SuppressWarnings("unchecked")
			@Override
			public void onInstallFinished(String pkgName, int returnCode){
				resultMap.put(pkgName, returnCode);

				// 安装成功后 记录更新包名
				SharedPreferences sp = PreferenceManager.getDefaultSharedPreferences(context);
				if(returnCode == 1){
					String intalledApkPaths = sp.getString(SPKeys.INSTALL_APK_PTAHS, "");
					if ("".equals(intalledApkPaths))
						intalledApkPaths = apkPathArray[currApkIndex.getIndex()];
					else
						intalledApkPaths = intalledApkPaths + SPKeys.INSTALL_APK_PTAHS_SPLIT
								+ apkPathArray[currApkIndex.getIndex()];
					sp.edit().putString(SPKeys.INSTALL_APK_PTAHS, intalledApkPaths).commit();
				}
				/** 判断是否所有安装完成 */ // TODO删除缓存文件
				currApkIndex.add();
				if (currApkIndex.getIndex() < apkPathArray.length)
					startApkIntall(context, apkPathArray, currApkIndex,resultMap, callback);
				else if (currApkIndex.getIndex() == apkPathArray.length && callback != null) {
					// 清空保存的确认包信息与 安装结果
					sp.edit().remove(SPKeys.CONFIRM_PACKAGE).remove(SPKeys.INSTALL_APK_PTAHS).commit();
					callback.installCompleted(resultMap);
				}
			}
		};
		installApk(context,Helpers.addApkSuffix(apkPathArray[currApkIndex.getIndex()]), observer);
	}
	
	/**
	 * 轮询调用卸载函数
	 * 
	 * @throws RemoteException
	 */
	private static void startApkUnintall(final Context context, final String[] apkPkgs, final ISystemManger.ApkIndex currApkIndex,
			final HashMap<String, Integer> resultMap, final AllApkUnInstallCompleted callback) {
		ApkUnInstallCompleted observer = new ApkUnInstallCompleted() {
			@Override
			public void onDeleteFinished(int returnCode) {
				resultMap.put(apkPkgs[currApkIndex.getIndex()], returnCode);	
				/** 判断是否所有卸载完成 */
				currApkIndex.add();
				if (currApkIndex.getIndex() < apkPkgs.length) {
					startApkUnintall(context, apkPkgs, currApkIndex, resultMap, callback);
				} else if (currApkIndex.getIndex() == apkPkgs.length && callback != null) {
					// 清空保存的确认包信息与 安装结果
					callback.uninstallCompleted(resultMap);
				}
			}
		};
		unInstallApk(context, apkPkgs[currApkIndex.getIndex()], observer);
	}

	
	public interface BindServiceSuccess{
		public void onBindSuccess();
	}
	
	public interface BackupCompleted{
		public void onResult(int code, String fileName);
	}
	
	public interface RecoveryCompleted{
		public void onResult(int code);
	}
	
	public interface ApkInstallCompleted{
		public void onInstallFinished(String pkgName, int returnCode);
	}
	
	public interface ApkUnInstallCompleted{
		public void onDeleteFinished(int returnCode);
	}
	
	public interface AllApkInstallCompleted {
		/**
		 * 所有应用安装完成
		 * 
		 * @param resultMap
		 *            key值为包名，value为返回码
		 */
		@SuppressWarnings("rawtypes")
		public void installCompleted(Map resultMapArray);
	}
	
	public interface AllApkUnInstallCompleted {
		/**
		 * 所有应用安装完成
		 * 
		 * @param resultMap
		 *            key值为包名，value为返回码
		 */
		@SuppressWarnings("rawtypes")
		public void uninstallCompleted(Map resultMapArray);
	}
}
