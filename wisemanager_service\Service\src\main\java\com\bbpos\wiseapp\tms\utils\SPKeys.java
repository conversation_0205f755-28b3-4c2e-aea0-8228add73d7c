package com.bbpos.wiseapp.tms.utils;

public class SPKeys {
	/**应用task任务列表*/
	public static final String WEBSOCKET_TASK_LIST = "WEBSOCKET_TASK_LIST";
	/**应用task任务列表*/
	public static final String WEBSOCKET_TASK_EXECUTE_RESULT_LIST = "WEBSOCKET_TASK_EXECUTE_RESULT_LIST";
	/**应用任务列表*/
	public static final String WEBSOCKET_RULEBASED_LIST = "WEBSOCKET_RULEBASED_LIST";
	/**应用task任务列表*/
	public static final String WEBSOCKET_SERVICE_LIST = "WEBSOCKET_SERVICE_LIST";
	/**应用wif profile任务列表*/
	public static final String WEBSOCKET_WIFL_PROFILE_LIST = "WEBSOCKET_WIFI_RPOFILE_LIST";
	/**应用wif profile任务列表*/
	public static final String WEBSOCKET_GEOFENCE_PROFILE_LIST = "WEBSOCKET_GEOFENCE_PROFILE_LIST";
	/**上一次连接的wif profile信息*/
	public static final String LAST_CONNECTED_WIFL_PROFILE = "LAST_WIFI_RPOFILE";
	/**上一次连接的wif profile 加密 信息*/
	public static final String LAST_CONNECTED_ENCRYPT_WIFL_PROFILE = "LAST_ENCRYPT_WIFI_RPOFILE";
	/**ST002的Rulebased处理应用列表*/
	public static final String RULEBASED_APP_LIST = "RULEBASED_APP_LIST";
	/**ST005的Rulebased应用列表记录*/
	public static final String RULEBASED_LIST_APP_LIST = "RULEBASED_LIST_APP_LIST";
	/**ST005的Rulebased上送結果列表记录*/
	public static final String RULEBASED_EXECUTE_RESULT_LIST = "RULEBASED_EXECUTE_RESULT_LIST";
	/**OTA固件MD5*/
	public static final String OTA_FILE_NAME = "OTA_FILE_NAME";
	/**OTA固件MD5*/
	public static final String OTA_FILE_MD5 = "OTA_FILE_MD5";
	/**OTA安装的任务ID**/
	public static final String OTA_INSTALL_TASK_ID = "OTA_INSTALL_TASK_ID";
	/**信息上送最后日期*/
	public static final String LAST_TER_INFO_UPLOAD_DATE = "LAST_TER_INFO_UPLOAD_DATE";
	/**apk安装结果保存*/
	public static final String INSTALL_APK_PTAHS = "intastalledApkPaths";
	public static final String INSTALL_APK_PTAHS_SPLIT = "-";
	
	/**确认包保存*/
	public static final String CONFIRM_PACKAGE = "confirmPackage";

	/**确认包保存*/
	public static final String PRIVATE_KEY_INDEX = "privateKeyIndex";
	public static final String PUBLIC_KEY_INDEX = "publicKeyIndex";


	public static final String KEY_USAGE_LOG = "key_usage_log";
}
