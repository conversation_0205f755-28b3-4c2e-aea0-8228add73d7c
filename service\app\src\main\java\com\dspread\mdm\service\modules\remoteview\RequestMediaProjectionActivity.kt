package com.dspread.mdm.service.modules.remoteview

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.media.projection.MediaProjectionManager
import android.os.Bundle
import com.dspread.mdm.service.utils.log.Logger

/**
 * 请求MediaProjection权限的Activity
 */
class RequestMediaProjectionActivity : Activity() {

    companion object {
        private const val REQUEST_CODE_MEDIA_PROJECTION = 1000
        
        // 静态变量保存结果
        @Volatile
        private var mediaProjectionData: Intent? = null
        
        @Volatile
        private var isRequestPending = false
        
        /**
         * 启动MediaProjection权限请求
         */
        fun requestMediaProjection(context: Context, callback: (Intent?) -> Unit) {
            Logger.remote("启动MediaProjection权限请求")
            
            if (isRequestPending) {
                Logger.remoteW("MediaProjection请求正在进行中")
                callback(null)
                return
            }
            
            // 保存回调
            requestCallback = callback
            
            // 启动Activity
            val intent = Intent(context, RequestMediaProjectionActivity::class.java)
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            context.startActivity(intent)
        }
        
        /**
         * 获取已保存的MediaProjection数据
         */
        fun getMediaProjectionData(): Intent? {
            return mediaProjectionData
        }
        
        /**
         * 清除MediaProjection数据
         */
        fun clearMediaProjectionData() {
            mediaProjectionData = null
        }
        
        // 回调函数
        private var requestCallback: ((Intent?) -> Unit)? = null
    }

    private var mediaProjectionManager: MediaProjectionManager? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        Logger.remote("RequestMediaProjectionActivity onCreate")
        
        try {
            // 获取MediaProjectionManager
            mediaProjectionManager = getSystemService(Context.MEDIA_PROJECTION_SERVICE) as? MediaProjectionManager
            
            if (mediaProjectionManager == null) {
                Logger.remoteE("无法获取MediaProjectionManager")
                finishWithResult(null)
                return
            }
            
            // 检查是否已经有权限
            if (mediaProjectionData != null) {
                Logger.remote("已有MediaProjection权限")
                finishWithResult(mediaProjectionData)
                return
            }
            
            // 请求权限
            requestMediaProjectionPermission()
            
        } catch (e: Exception) {
            Logger.remoteE("RequestMediaProjectionActivity onCreate异常", e)
            finishWithResult(null)
        }
    }

    /**
     * 请求MediaProjection权限
     */
    private fun requestMediaProjectionPermission() {
        try {
            Logger.remote("请求MediaProjection权限")
            isRequestPending = true
            
            val intent = mediaProjectionManager?.createScreenCaptureIntent()
            if (intent != null) {
                startActivityForResult(intent, REQUEST_CODE_MEDIA_PROJECTION)
            } else {
                Logger.remoteE("无法创建ScreenCaptureIntent")
                finishWithResult(null)
            }
            
        } catch (e: Exception) {
            Logger.remoteE("请求MediaProjection权限异常", e)
            finishWithResult(null)
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        
        Logger.remote("onActivityResult: requestCode=$requestCode, resultCode=$resultCode")
        
        when (requestCode) {
            REQUEST_CODE_MEDIA_PROJECTION -> {
                if (resultCode == Activity.RESULT_OK && data != null) {
                    Logger.remote("MediaProjection权限授权成功")
                    mediaProjectionData = data
                    finishWithResult(data)
                } else {
                    Logger.remoteW("MediaProjection权限授权失败或被拒绝")
                    finishWithResult(null)
                }
            }
            else -> {
                Logger.remoteW("未知的requestCode: $requestCode")
                finishWithResult(null)
            }
        }
    }

    /**
     * 完成并返回结果
     */
    private fun finishWithResult(data: Intent?) {
        try {
            isRequestPending = false
            
            // 调用回调
            requestCallback?.invoke(data)
            requestCallback = null
            
            // 关闭Activity
            finish()
            
        } catch (e: Exception) {
            Logger.remoteE("finishWithResult异常", e)
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        Logger.remote("RequestMediaProjectionActivity onDestroy")
        
        // 确保清理状态
        if (isRequestPending) {
            isRequestPending = false
            requestCallback?.invoke(null)
            requestCallback = null
        }
    }
}
