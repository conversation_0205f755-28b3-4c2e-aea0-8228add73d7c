<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:gravity="center"
    android:id="@+id/root"
    android:background="@drawable/wallpaper">

    <LinearLayout
        android:id="@+id/ll_progress"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:gravity="center"
        android:orientation="vertical"
        android:visibility="visible">
        <ImageView
            android:id="@+id/iv_progress"
            android:layout_width="80dp"
            android:layout_height="80dp"
            android:padding="10dp" />
        <TextView
            android:id="@+id/tv_count"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:padding="10dp"
            android:gravity="center"
            android:text="Upgrading"
            android:textColor="@color/white"
            android:textSize="25sp"/>


        <Button
            android:id="@+id/btn_retry"
            android:layout_width="wrap_content"
            android:layout_height="50dp"
            android:paddingLeft="10dp"
            android:paddingRight="10dp"
            android:visibility="gone"
            android:background="@drawable/cursor_shape"
            android:text="@string/tmt_update_failed"/>
    </LinearLayout>


</LinearLayout>