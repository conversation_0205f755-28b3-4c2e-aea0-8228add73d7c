package com.bbpos.wiseapp.settings.utils;

import static android.net.NetworkRequest.Builder;
import static android.telephony.SubscriptionManager.INVALID_SUBSCRIPTION_ID;

import android.annotation.SuppressLint;
import android.annotation.TargetApi;
import android.content.ContentResolver;
import android.content.ContentUris;
import android.content.ContentValues;
import android.content.Context;
import android.database.Cursor;
import android.net.ConnectivityManager;
import android.net.Network;
import android.net.NetworkCapabilities;
import android.net.NetworkRequest;
import android.net.Uri;
import android.os.Build;
import android.provider.Telephony;
import android.telephony.SubscriptionInfo;
import android.telephony.SubscriptionManager;
import android.telephony.TelephonyManager;
import android.text.TextUtils;

import androidx.annotation.NonNull;

import com.bbpos.wiseapp.logger.BBLog;

import org.json.JSONArray;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@TargetApi(Build.VERSION_CODES.N)
public class APNManager {
    private static final String TAG = BBLog.TAG;
    private static final boolean DEBUG = true;
    public static final Uri APN_TABLE_URI = Uri.parse("content://telephony/carriers");
    public static final String RESTORE_CARRIERS_URI = "content://telephony/carriers/restore";
    public static final String PREFERRED_APN_URI = "content://telephony/carriers/preferapn";
    private static final Uri DEFAULTAPN_URI = Uri.parse(RESTORE_CARRIERS_URI);
    private static final Uri PREFERAPN_URI = Uri.parse(PREFERRED_APN_URI);

    private static final String[] PROJECTION = new String[]{
            Telephony.Carriers._ID, // 0 唯一識別符
            Telephony.Carriers.NAME,//運營商的名稱
            Telephony.Carriers.APN,//訪問點名稱
            Telephony.Carriers.PROXY,//代理地址
            Telephony.Carriers.PORT,//端口號
            Telephony.Carriers.MMSPROXY,//MMS代理地址
            Telephony.Carriers.MMSPORT, //10 MMS端口號
            Telephony.Carriers.SERVER,//服務器地址
            Telephony.Carriers.USER, // 5 用戶名
            Telephony.Carriers.PASSWORD,//密碼
            Telephony.Carriers.MMSC,//MMS服務中心地址
            Telephony.Carriers.MCC,//移動國家代碼
            Telephony.Carriers.MNC,//移動網路代碼
            Telephony.Carriers.NUMERIC,//運營商的數字代碼  MCC+MNC
            Telephony.Carriers.AUTH_TYPE, //身份驗證類型
            Telephony.Carriers.TYPE,//連接類型
            Telephony.Carriers.PROTOCOL,//15 連接協議
            Telephony.Carriers.ROAMING_PROTOCOL, //漫遊時的連接協議
            Telephony.Carriers.CURRENT, //是否為當前選中的設置
            Telephony.Carriers.CARRIER_ENABLED,//是否啟用此運營商設置
            Telephony.Carriers.BEARER, //網絡連接類型
            Telephony.Carriers.MVNO_TYPE,//虛擬網絡運營商類型
            Telephony.Carriers.MVNO_MATCH_DATA,//20 虛擬網絡運營商匹配數據
            "bearer_bitmask",//21 网络连接类型掩码
//            Telephony.Carriers.SUBSCRIPTION_ID,// 訂閱ID
    };

    private static final String APN_ID = "_id";
    private static final int ID_INDEX = 0;
    private static final int NAME_INDEX = 1;
    private static final int APN_INDEX = 2;
    private static final int PROXY_INDEX = 3;
    private static final int PROXY_PORT_INDEX = 4;
    private static final int MMS_PROXY_INDEX = 5;
    private static final int MMS_PORT_INDEX = 6;
    private static final int SERVER_INDEX = 7;
    private static final int USER_INDEX = 8;
    private static final int PASSWORD_INDEX = 9;
    private static final int MMSC_INDEX = 10;
    private static final int MCC_INDEX = 11;
    private static final int MNC_INDEX = 12;
    private static final int NUMERIC_INDEX = 13;
    private static final int AUTH_TYPE_INDEX = 14;
    private static final int TYPE_INDEX = 15;
    private static final int PROTOCOL_INDEX = 16;
    private static final int ROAMING_PROTOCOL_INDEX = 17;
    private static final int CURRENT_INDEX = 18;
    private static final int CARRIER_ENABLED_INDEX = 19;
    private static final int BEARER_INDEX = 20;
    private static final int MVNO_TYPE_INDEX = 21;
    private static final int MVNO_MATCH_DATA_INDEX = 22;
    private static final int BEARER_BITMASK_INDEX = 23;
    private static Context mContext;
    protected TelephonyManager telephonyManager;
    private final SubscriptionManager subscriptionManager;

    private static class SingletonHolder {
        private static final APNManager INSTANCE = new APNManager();
    }

    public static APNManager getInstance() {
        return APNManager.SingletonHolder.INSTANCE;
    }

    public static void init(Context context) {
        mContext = context;
    }

    public APNManager() {
        telephonyManager = (TelephonyManager) mContext.getSystemService(Context.TELEPHONY_SERVICE);
        subscriptionManager = (SubscriptionManager) mContext.getSystemService(Context.TELEPHONY_SUBSCRIPTION_SERVICE);
    }

    // 获取sim卡槽个数
    public int getSimSlotCount() {
        if (telephonyManager != null) {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                return telephonyManager.getPhoneCount();
            }
        }
        return 0;
    }

    @SuppressLint("MissingPermission")
    public List<SubscriptionInfo> getActiveSubscriptionInfoList() {
        List<SubscriptionInfo> subscriptionInfoList = subscriptionManager.getActiveSubscriptionInfoList();
        if (subscriptionInfoList != null) {
            return subscriptionInfoList;
        }
        return null;
    }

    // 判断指定卡槽上是否存在sim
    @SuppressLint("MissingPermission")
    public boolean isSimCardExist(int slotIndex) {
        List<SubscriptionInfo> subscriptionInfoList = subscriptionManager.getActiveSubscriptionInfoList();
        if (subscriptionInfoList != null) {
            for (SubscriptionInfo info : subscriptionInfoList) {
                if (info.getSimSlotIndex() == slotIndex) {
                    return true;
                }
            }
        }
        return false;
    }

    @SuppressLint("MissingPermission")
    public int getSubscriptionIdFromSlotIndex(int slotIndex) {
        int subId = INVALID_SUBSCRIPTION_ID;
        List<SubscriptionInfo> subscriptionInfoList = subscriptionManager.getActiveSubscriptionInfoList();
        if (subscriptionInfoList != null) {
            for (SubscriptionInfo info : subscriptionInfoList) {
                if (info.getSimSlotIndex() == slotIndex) {
                    return info.getSubscriptionId();
                }
            }
        }
        return subId;
    }

    // 获取当前默认使用的sim subscriptionID
    public int getDefaultSubscriptionId() {
        SubscriptionManager subscriptionManager = SubscriptionManager.from(mContext);
        if (subscriptionManager != null) {
            return subscriptionManager.getDefaultSubscriptionId();
        }
        return -1;
    }

    // 通过卡槽获取sim卡对应的所有apn信息列表
    public List<APN> getApnListBySlot(int slotIndex) {
        List<APN> apnList = new ArrayList<>();
        APN apnInfo = null;
        String where = getWhere(false);

        Uri contentUri = Uri.parse("content://telephony/carriers");
        Cursor cursor = null;

        ContentResolver mResolver = mContext.getContentResolver();
        try{
            cursor = mResolver.query(
                    contentUri,//Telephony.Carriers.CONTENT_URI,
                    PROJECTION,
                    where,
                    null,
                    Telephony.Carriers.DEFAULT_SORT_ORDER);
            debug("cursor = " + cursor + " : " + cursor.getCount());

            if(null != cursor){
                while(cursor.moveToNext()){
//                    String type = cursor.getString(TYPE_INDEX );//"type"
//                    String mvnoType = cursor.getString(MVNO_TYPE_INDEX);
//                    String mvnoMatchData = cursor.getString(MVNO_MATCH_DATA_INDEX);
//
//                    if(shouldSkipApn(type)) {
//                        //debug("will move next");
//                        cursor.moveToNext();
//                        continue;
//                    }
//                    if (!TextUtils.isEmpty(mvnoType) && !TextUtils.isEmpty(mvnoMatchData)) {
//                        //mvnoList -- 虚拟运营商 --只要是虚拟运营商都不会显示？
//                        debug("虚拟运营商 --只要是虚拟运营商都不会显示？ ");
//                    } else {
                        //mnoList -- MVNO(Mobile Virtaul Network Operator)虚拟网络运营商，没有自己的实体网络，通过租用MNO(Mobile Network Operator)的网络来提供网络服务。
                        apnInfo = new APN(cursor.getString(ID_INDEX), cursor.getString(NAME_INDEX), cursor.getString(APN_INDEX),
                                cursor.getString(PROXY_INDEX), cursor.getString(PROXY_PORT_INDEX), cursor.getString(USER_INDEX),
                                cursor.getString(PASSWORD_INDEX), cursor.getString(SERVER_INDEX), cursor.getString(MMSC_INDEX),
                                cursor.getString(MMS_PROXY_INDEX), cursor.getString(MMS_PORT_INDEX), cursor.getString(MCC_INDEX),
                                cursor.getString(MNC_INDEX), cursor.getInt(AUTH_TYPE_INDEX), cursor.getString(TYPE_INDEX),
                                cursor.getString(PROTOCOL_INDEX), cursor.getString(ROAMING_PROTOCOL_INDEX), cursor.getInt(CARRIER_ENABLED_INDEX),
                                cursor.getString(BEARER_INDEX), cursor.getString(BEARER_BITMASK_INDEX), cursor.getInt(MVNO_TYPE_INDEX), cursor.getString(MVNO_MATCH_DATA_INDEX),
                                cursor.getString(NUMERIC_INDEX), cursor.getString(CURRENT_INDEX));
//                        debug("apnInfo = " + apnInfo.toString());
                        apnList.add(apnInfo);
                    }
//                }
            }
//            debug("apnList = " + apnList);
        } catch(Exception e){
            e.printStackTrace();
        } finally{
            if(cursor!=null) {
                cursor.close();
            }
        }
        return apnList;
    }

    // 获取当前sim卡使用的默认apn信息
    public APN getCurrentApn(int slotIndex) {
        int subId = getSubscriptionIdFromSlotIndex(slotIndex);
        debug("enter getCurrentApn() subId = " + subId);
        return getDefaultApnBySubId(subId);
    }

    public APN getCurrentApn() {
        APN currentApn = null;
        ContentResolver contentResolver = mContext.getContentResolver();
        Cursor cursor = null;
        try {
            // 查询当前正在使用的APN
            cursor = contentResolver.query(Uri.withAppendedPath(Telephony.Carriers.CONTENT_URI, "current"), PROJECTION, null, null, null);
            if (cursor != null && cursor.moveToFirst()) {
                currentApn = new APN();
                currentApn.setName(cursor.getString(NAME_INDEX));
                currentApn.setApn(cursor.getString(APN_INDEX));
                currentApn.setMcc(cursor.getString(MCC_INDEX));
                currentApn.setMnc(cursor.getString(MNC_INDEX));
            }
//            debug(" current apn ------------->" + currentApn);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }
        return currentApn;
    }


    // 获取指定sim卡使用的默认apn信息
    public APN getDefaultApnBySubId(int subId){
        debug("enter getApnBySubId() subId = " + subId);

        APN myApn = null;
        Cursor cursor = null;
        try{
            ContentResolver mResolver = mContext.getContentResolver();
            cursor = mResolver.query(getPreferApnUri(subId), PROJECTION,
                    null, null, Telephony.Carriers.DEFAULT_SORT_ORDER);
            debug("cursor = " + cursor + " : " + cursor.getCount());

            if (cursor != null && cursor.getCount() > 0) {
                cursor.moveToFirst();
                myApn = new APN(cursor.getString(ID_INDEX), cursor.getString(NAME_INDEX), cursor.getString(APN_INDEX),
                        cursor.getString(PROXY_INDEX), cursor.getString(PROXY_PORT_INDEX), cursor.getString(USER_INDEX),
                        cursor.getString(PASSWORD_INDEX), cursor.getString(SERVER_INDEX), cursor.getString(MMSC_INDEX),
                        cursor.getString(MMS_PROXY_INDEX), cursor.getString(MMS_PORT_INDEX), cursor.getString(MCC_INDEX),
                        cursor.getString(MNC_INDEX), cursor.getInt(AUTH_TYPE_INDEX), cursor.getString(TYPE_INDEX),
                        cursor.getString(PROTOCOL_INDEX), cursor.getString(ROAMING_PROTOCOL_INDEX), cursor.getInt(CARRIER_ENABLED_INDEX),
                        cursor.getString(BEARER_INDEX),cursor.getString(BEARER_BITMASK_INDEX), cursor.getInt(MVNO_TYPE_INDEX), cursor.getString(MVNO_MATCH_DATA_INDEX),
                        cursor.getString(NUMERIC_INDEX), cursor.getString(CURRENT_INDEX));;
            }
//            debug("myApn = " + myApn);
        } catch(Exception e){
            e.printStackTrace();
        } finally{
            if(cursor!=null)
                cursor.close();
        }
        return myApn;
    }

    // 根据apn id查找对应的apn信息，并应用到当前sim卡上
    public boolean applyApnBySubId(int subId, int apnId) {
        try {
            ContentValues values = new ContentValues();
            values.put("sub_id", subId);
            values.put("apn_id", apnId);

            ContentResolver contentResolver = mContext.getContentResolver();
            int count = contentResolver.update(PREFERAPN_URI, values, null, null);
            debug("applyApnById apnId-->" + apnId +", subId--->" + subId +", result: count = " + count);
            return count > 0;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

    public boolean setSelectKey(int apnId) {
        try {
            ContentValues values = new ContentValues();
            values.put("apn_id", apnId);

            ContentResolver contentResolver = mContext.getContentResolver();
            int count = contentResolver.update(PREFERAPN_URI, values, null, null);
            return count > 0;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

    // 向当前sim对应的运营商追加一个apn信息并返回apn id
    public int addApn(int slotIndex, String apnName, String apnNumeric) {
        try {
            Uri contentUri = Uri.parse("content://telephony/carriers");
            ContentValues values = new ContentValues();
            values.put("name", apnName);
            values.put("numeric", apnNumeric);
            values.put("sim_id", slotIndex);

            ContentResolver contentResolver = mContext.getContentResolver();
            Uri insertedUri = contentResolver.insert(contentUri, values);
            if (insertedUri != null) {
                return Integer.parseInt(insertedUri.getLastPathSegment());
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return -1;
    }

    // 恢复至当前sim默认设置的apn
    @SuppressLint("MissingPermission")
    public boolean restoreDefaultApn(int slotIndex) {
        try {
            SubscriptionInfo subInfo = null;
            for (int i = 0; i < getSimSlotCount(); ++i) {
                subInfo = subscriptionManager.getActiveSubscriptionInfoForSimSlotIndex(i);
                debug("restoreDefaultApn - " + i + " : " + subInfo);
                if (subInfo != null && slotIndex == subInfo.getSimSlotIndex()) {
                    int subId = subInfo.getSubscriptionId();
                    int count = mContext.getContentResolver().delete(getDefaultApnUri(subId), null, null);
                    debug("restoreDefaultApn result = " + (count > 0) + ", subId = " + subId);
                    return true;
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

    @SuppressLint("MissingPermission")
    public boolean restoreDefaultApnBySubId(int subId) {
        if (subId < 0) return false;
        try {
            int count = mContext.getContentResolver().delete(getDefaultApnUri(subId), null, null);
            debug("恢复默认APN result = " + (count > 0) + ", subId = " + subId);
            return count > 0;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

    public List<APN> findDefaultApns(){
        List<APN> apnList = new ArrayList<>();
        try {
            APN apnInfo;
            Cursor cursor = mContext.getContentResolver().query(DEFAULTAPN_URI, null, null,null, Telephony.Carriers.DEFAULT_SORT_ORDER) ;
            if(null != cursor){
                while(cursor.moveToNext()){
    //                String type = cursor.getString(TYPE_INDEX );//"type"
    //                String mvnoType = cursor.getString(MVNO_TYPE_INDEX);
    //                String mvnoMatchData = cursor.getString(MVNO_MATCH_DATA_INDEX);
    //
    //                    if(shouldSkipApn(type)) {
    //                        //debug("will move next");
    //                        cursor.moveToNext();
    //                        continue;
    //                    }
    //                    if (!TextUtils.isEmpty(mvnoType) && !TextUtils.isEmpty(mvnoMatchData)) {
    //                        //mvnoList -- 虚拟运营商 --只要是虚拟运营商都不会显示？
    //                        debug("虚拟运营商 --只要是虚拟运营商都不会显示？ ");
    //                    } else {
                    //mnoList -- MVNO(Mobile Virtaul Network Operator)虚拟网络运营商，没有自己的实体网络，通过租用MNO(Mobile Network Operator)的网络来提供网络服务。
                    apnInfo = new APN(cursor.getString(ID_INDEX), cursor.getString(NAME_INDEX), cursor.getString(APN_INDEX),
                            cursor.getString(PROXY_INDEX), cursor.getString(PROXY_PORT_INDEX), cursor.getString(USER_INDEX),
                            cursor.getString(PASSWORD_INDEX), cursor.getString(SERVER_INDEX), cursor.getString(MMSC_INDEX),
                            cursor.getString(MMS_PROXY_INDEX), cursor.getString(MMS_PORT_INDEX), cursor.getString(MCC_INDEX),
                            cursor.getString(MNC_INDEX), cursor.getInt(AUTH_TYPE_INDEX), cursor.getString(TYPE_INDEX),
                            cursor.getString(PROTOCOL_INDEX), cursor.getString(ROAMING_PROTOCOL_INDEX), cursor.getInt(CARRIER_ENABLED_INDEX),
                            cursor.getString(BEARER_INDEX),cursor.getString(BEARER_BITMASK_INDEX), cursor.getInt(MVNO_TYPE_INDEX), cursor.getString(MVNO_MATCH_DATA_INDEX),
                            cursor.getString(NUMERIC_INDEX), cursor.getString(CURRENT_INDEX));;
//                    debug("findDefaultApns apnInfo = " + apnInfo.toString());
                    apnList.add(apnInfo);
                }
    //                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        debug("apnList = " + apnList);
        return apnList;
    }

    // 通过apn name及numeric查找对应apn信息
    public APN findApnByApnNameAndNumeric(String apn, String numeric) {
        if ((apn == null || apn.trim().length() == 0 )
                && (numeric == null || numeric.trim().length() == 0 )
                /*&& (mnc == null || mnc.trim().length() == 0 )*/){
            debug("isApnExit skip check, apn and numeric must not be null");
            return null;
        }

//        String where = "apn=\"" + apn + "\"" + " AND mcc=\"" + mcc + "\"" + " AND mnc=\"" + mnc + "\""
        String where = "apn=\"" + apn + "\"" + " AND numeric=\"" + numeric + "\""
                + " AND NOT (type='ia' AND (apn=\"\" OR apn IS NULL)) AND user_visible!=0"
                + " AND NOT (type='emergency')"
                + " AND NOT (type='ims')";

        ContentResolver contentResolver = mContext.getContentResolver();
        Cursor cursor = contentResolver.query(APN_TABLE_URI, PROJECTION, where, null, Telephony.Carriers.DEFAULT_SORT_ORDER);

        APN apnInfo = parseApnInfoFromCursor(cursor);
        return apnInfo;
    }

    public int findApnByApnAndNumeric(String apn, String numeric){
        if ((apn == null || apn.trim().length() == 0 )
                && (numeric == null || numeric.trim().length() == 0 )){
            debug("isApnExit skip check, apn and numeric must not be null");
            return -1;
        }

        int apnId = -1;
        ContentResolver contentResolver = mContext.getContentResolver();
        Cursor cursor = null;
        try {
            cursor = contentResolver.query(APN_TABLE_URI, null, "apn=? and numeric=? ", new String[]{apn,numeric}, null);
            if (cursor != null && cursor.moveToFirst()) {
                apnId = cursor.getInt(cursor.getColumnIndex("_id"));
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }
        return apnId;
    }

    // 更新或新增apn信息
    public boolean updateApn(APN apn) {
        if (apn == null) return false;
//        APN findApn = findApnByApnNameAndNumeric(apn.getApn(),apn.getMcc(),apn.getMnc());
        APN findApn = findApnByApnNameAndNumeric(apn.getApn(),apn.getNumeric());
        if (findApn != null) {
            return updateApn(apn,true);
        }
        return false;
    }

    //添加apn
    public int addApn(APN newApn, boolean force) {
        if (newApn == null ||
                ((newApn.getApn() == null || newApn.getApn().trim().length() == 0 )
                        && (newApn.getNumeric() == null || newApn.getNumeric().trim().length() == 0 ))){
            debug("skip update apn, apn and numeric must not be null");
            return -1;
        }
        return addApn(mContext, force, /*mSlotId,*/ /*newApn.getId(),*/
                newApn.getName(), newApn.getApn(), newApn.getProxy(),
                newApn.getPort(), newApn.getUser(), newApn.getPassword(),
                newApn.getServer(), newApn.getMmsc(), newApn.getMmsProxy(),
                newApn.getMmsPort(), newApn.getMcc(), newApn.getMnc(),
                newApn.getAuthType(), newApn.getApnType(), newApn.getProtocol(),
                newApn.getRoamingProtocol(), newApn.getCarrierEnabled(),
                newApn.getBearer(),newApn.getBearerBitmask(), newApn.getMvnoType(),newApn.getMvnoMatchData());
    }

    //更新apn
    public int addApn(Context context, boolean force, /*int slotId,*/
                           /*String apnkey,*/ String name, String apn, String proxy,
                           String port, String user, String password, String server,
                           String mmsc, String mmsproxy, String mmsport, String mcc,
                           String mnc, int authTypeVal, String strType, String protocol,
                           String roamingProtocol, int carrierEnabled, String bearerVal,String bearerBitmask,
                           int mvnoType, String mvnoValue) {

        try {
            Uri uri = Uri.parse("content://telephony/carriers");
            if (!force
                    && (name.length() < 1 || apn.length() < 1 || mcc.length() != 3 || (mnc
                    .length() & 0xFFFE) != 2)) {
                return -1;
            }

            ContentValues values = getContentValues(name, apn, proxy, port,
                    user, password, server, mmsc,
                    mmsproxy, mmsport, mcc, mnc,
                    authTypeVal, strType, protocol, roamingProtocol,
                    carrierEnabled, bearerVal, bearerBitmask, mvnoType,mvnoValue);

            Uri insertUri = context.getContentResolver().insert(uri,values);
            if (insertUri != null) {
//                debug("insertUri = " + insertUri);
                return Integer.parseInt(insertUri.getLastPathSegment());
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return -1;
    }

    public void checkAndSaveApn(APN apn) {
        try {
            APN findApn = APNManager.getInstance().findApnByApnNameAndNumeric(apn.getApn(), apn.getNumeric());
            if (findApn != null) {
                boolean result = APNManager.getInstance().removeApnByOperatorAndId(findApn.getId());
                debug("saveApnFromMDM remove apn Result = " + result);
            }
            int apnId = APNManager.getInstance().addApn(apn,true);
            debug("saveApnFromMDM addApn apnId= " + apnId);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void checkAndSaveAllApn(Map<String, List<APN>> numericMap) {
//        debug("checkAndSaveAllApn -->" + numericMap);
        try {
            if (numericMap != null && numericMap.size() > 0) {
                for (Map.Entry<String, List<APN>> entry : numericMap.entrySet()) {
                    String keyForNumeric = entry.getKey();
                    List<APN> apnListForNumeric = entry.getValue();
                    debug("checkAndSaveAllApn Key: " + keyForNumeric);
                    for (APN apn : apnListForNumeric) {
                        APNManager.getInstance().checkAndSaveApn(apn);
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @NonNull
    public ContentValues getContentValues(String name, String apn, String proxy, String port,
                                          String user, String password, String server, String mmsc,
                                          String mmsproxy, String mmsport, String mcc, String mnc,
                                          int authTypeVal, String strType, String protocol,
                                          String roamingProtocol, int carrierEnabled, String bearerVal, String bearerBitmask,
                                          int mvnoType, String mvnoValue) {
        ContentValues values = new ContentValues();

        values.put(Telephony.Carriers.NAME, name);
        values.put(Telephony.Carriers.APN, apn);
        values.put(Telephony.Carriers.PROXY, checkNotSet(proxy));
        values.put(Telephony.Carriers.PORT, checkNotSet(port));
        values.put(Telephony.Carriers.USER, checkNotSet(user));
        values.put(Telephony.Carriers.PASSWORD, checkNotSet(password));
        values.put(Telephony.Carriers.SERVER, checkNotSet(server));
        values.put(Telephony.Carriers.MMSC, checkNotSet(mmsc));
        values.put(Telephony.Carriers.MMSPROXY, checkNotSet(mmsproxy));
        values.put(Telephony.Carriers.MMSPORT, checkNotSet(mmsport));

        values.put(Telephony.Carriers.AUTH_TYPE, authTypeVal);
        values.put(Telephony.Carriers.TYPE, checkNotSet(strType));
        values.put(Telephony.Carriers.PROTOCOL, checkNotSet(protocol));
        values.put(Telephony.Carriers.ROAMING_PROTOCOL, checkNotSet(roamingProtocol));
        values.put(Telephony.Carriers.CARRIER_ENABLED, carrierEnabled);

        values.put(Telephony.Carriers.MVNO_TYPE,mvnoType);
        values.put(Telephony.Carriers.MVNO_MATCH_DATA,mvnoValue);

        values.put(Telephony.Carriers.MCC, mcc);
        values.put(Telephony.Carriers.MNC, mnc);
        values.put(Telephony.Carriers.NUMERIC, mcc + mnc);

        //以下代码代表启用这个apn
        String numeric = mcc + mnc;
        debug("numeric = " + numeric);

        String curMnc = null;
        String curMcc = null;
        // MCC is first 3 chars and then in 2 - 3 chars of MNC
        if (numeric != null && numeric.length() > 4) {
            // Country code
            curMcc = numeric.substring(0, 3);
            // Network code
            curMnc = numeric.substring(3);
            debug("curMcc = " + curMcc + ", curMnc = " + curMnc);
        }
        if (curMnc != null && curMcc != null) {
            if (curMnc.equals(mnc) && curMcc.equals(mcc)) {
                //将CURRENT字段设置为1表示将此APN设置设置为当前活动的APN。在Android中，当有多个APN设置可用时，系统将根据优先级和其他条件选择当前活动的APN。通过将CURRENT字段设置为1，你可以将特定的APN设置标记为当前活动的APN。
                //这意味着当条件匹配时，这个APN设置将被标记为当前活动的APN，系统将尝试使用该APN进行数据连接。
                //将APN设置的CURRENT字段设置为1并不保证立即生效。系统会自动选择适用于当前网络连接的默认APN，而不是由应用程序直接控制
                values.put(Telephony.Carriers.CURRENT, 1);
            }
        }

        //指定与APN相关的数据网络类型。GSM/CDMA/LTE/ANY
        if (bearerVal != null && !bearerVal.isEmpty()) {
            values.put(Telephony.Carriers.BEARER, bearerVal);
        }
        if (bearerBitmask != null && !bearerBitmask.isEmpty()) {
            values.put("bearer_bitmask", bearerBitmask);
        }

        values.put("edited", 1);

//        debug("values = " + values);
        return values;
    }

    //添加apn
    public boolean updateApn(@NonNull APN newApn, boolean force) {
        debug("addApn... force = " + force);

        try {
            Uri uri = Uri.parse("content://telephony/carriers");
            int pos = Integer.parseInt(newApn.id);
            uri = ContentUris.withAppendedId(uri, pos);
            debug("apnkey = " + newApn.id + ", pos = " + pos + ", mUri = " + uri + ", uri = " + uri);

            if (newApn == null ||
                    ((newApn.getApn() == null || newApn.getApn().trim().length() == 0 )
                            && (newApn.getNumeric() == null || newApn.getNumeric().trim().length() == 0 ))){
                debug("skip update apn, apn and numeric must not be null");
                return false;
            }


            String name = checkNotSet(newApn.getName());
            String apn = checkNotSet(newApn.getApn());
            String mcc = checkNotSet(newApn.getMcc());
            String mnc = checkNotSet(newApn.getMnc());
            String apnType = newApn.getApnType();
            debug("name = " + name + ", apn = " + apn);
            debug("mcc = " + mcc + ", mnc = " + mnc);
            debug("apnType = " + apnType);

            if (!force && (name.length() < 1 || mcc.length() != 3 || ((mnc.length() & 0xFFFE) != 2)
                    || (apnType == null || !apnType.contains("ia")) && apn.length() < 1)) {
                debug("force = false, return");
                return false;
            }

            if (force && name.length() < 1 && apn.length() < 1 && uri != null) {
                int deletedRows = mContext.getContentResolver().delete(uri, null, null);
                debug("Deleted rows: " + deletedRows);
                return false;
            }

            ContentValues values = getContentValues(name, apn, newApn.getProxy(), newApn.getPort(),
                    newApn.getUser(), newApn.getPassword(), newApn.getServer(),
                    newApn.getMmsc(), newApn.getMmsProxy(), newApn.getMmsPort(),
                    mcc, mnc, newApn.getAuthType(),
                    apnType, newApn.getRoamingProtocol(), newApn.getRoamingProtocol(),
                    newApn.getCarrierEnabled(), newApn.getBearer(), newApn.getBearerBitmask(),
                    newApn.getMvnoType(),newApn.getMvnoMatchData());

            debug("values = " + values);
            int count = mContext.getContentResolver().update(APN_TABLE_URI, values,null,null);
            debug("update count: " + count);
            return count > 0;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

    //设置为指定卡槽上sim的默认apn
    public void setDefaultApn(String apnID, int slotIndex) {
        try {
            ContentResolver resolver = mContext.getContentResolver();
            ContentValues values = new ContentValues();
            values.put(APN_ID, apnID);
            Uri uri = PREFERAPN_URI.buildUpon()
                    .appendQueryParameter("slot", String.valueOf(slotIndex))
                    .build();

            resolver.update(uri, values, null, null);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Deprecated
    public APN getDefaultAPN() {
        APN apn = null;
        try {
            Cursor cursor = mContext.getContentResolver().query(PREFERAPN_URI,
                    null, null, null, null);
            if (cursor != null && cursor.getCount() > 0) {
                cursor.moveToFirst();
                apn = new APN(cursor.getString(ID_INDEX), cursor.getString(NAME_INDEX), cursor.getString(APN_INDEX),
                        cursor.getString(PROXY_INDEX), cursor.getString(PROXY_PORT_INDEX), cursor.getString(USER_INDEX),
                        cursor.getString(PASSWORD_INDEX), cursor.getString(SERVER_INDEX), cursor.getString(MMSC_INDEX),
                        cursor.getString(MMS_PROXY_INDEX), cursor.getString(MMS_PORT_INDEX), cursor.getString(MCC_INDEX),
                        cursor.getString(MNC_INDEX), cursor.getInt(AUTH_TYPE_INDEX), cursor.getString(TYPE_INDEX),
                        cursor.getString(PROTOCOL_INDEX), cursor.getString(ROAMING_PROTOCOL_INDEX), cursor.getInt(CARRIER_ENABLED_INDEX),
                        cursor.getString(BEARER_INDEX),cursor.getString(BEARER_BITMASK_INDEX), cursor.getInt(MVNO_TYPE_INDEX), cursor.getString(MVNO_MATCH_DATA_INDEX),
                        cursor.getString(NUMERIC_INDEX), cursor.getString(CURRENT_INDEX));;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return apn;
    }

    // 根据apn id移除当前运营商下对应的apn信息
    public boolean removeApnByOperatorAndId(String apnId) {
        try {
            Uri contentUri = Uri.parse("content://telephony/carriers");

            int pos = Integer.parseInt(apnId);
            Uri uri = ContentUris.withAppendedId(contentUri, pos);
            debug("apnKey-->" + apnId +", uri--->" + uri);
            int count = 0;
            if (uri != null) {
                count = mContext.getContentResolver().delete(uri, null, null);
            }
            return count > 0 ? true : false;
        } catch (NumberFormatException e) {
            e.printStackTrace();
        }
        return false;
    }

    // 移除指定运营商下的apn信息
    public boolean removeApnByOperatorAndId(String apnId,String numeric) {
        try {
            Uri contentUri = Uri.parse("content://telephony/carriers");

            int pos = Integer.parseInt(apnId);
            Uri uri = ContentUris.withAppendedId(contentUri, pos);
            debug("apnKey-->" + apnId +", uri--->" + uri);

            String where = "numeric=?";
            String[] selectionArgs = {numeric};
            int count = 0;
            if (uri != null) {
                count = mContext.getContentResolver().delete(contentUri, where, selectionArgs);
            }
            return count > 0 ? true : false;
        } catch (NumberFormatException e) {
            e.printStackTrace();
        }
        return false;
    }

    /**
     * 移除指定运营商下的apn信息
     * @param apn apn 名称，非接入点name值
     * @param numeric
     * @return
     */
    public boolean removeApnByOperatorAndApnName(String apn,String numeric) {
        try {
            Uri contentUri = Uri.parse("content://telephony/carriers");

            String where = "numeric=? and apn=? ";
            String[] selectionArgs = {numeric,apn};
            int count = mContext.getContentResolver().delete(contentUri, where, selectionArgs);
            return count > 0 ? true : false;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

    /**
     * 将通信数据绑定到当前网络
     * @param context
     */
    public void applyApnNetWork(Context context) {
        Builder builder = new Builder();
        builder.addCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET);
        builder.addTransportType(NetworkCapabilities.TRANSPORT_CELLULAR);
        NetworkRequest request = builder.build();
        ConnectivityManager connectivityManager = (ConnectivityManager) context.getSystemService(Context.CONNECTIVITY_SERVICE);
        connectivityManager.requestNetwork(request, new ConnectivityManager.NetworkCallback() {
            @Override
            public void onAvailable(Network network) {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                    connectivityManager.bindProcessToNetwork(network);
                } else {
                    ConnectivityManager.setProcessDefaultNetwork(network);
                }
            }
        });
    }

    /**
     * 将数据通信恢复到普通模式
     * @param context
     */
    public static void restoreNormalNetwork(Context context){
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            ConnectivityManager connectivityManager = (ConnectivityManager) context.getSystemService(Context.CONNECTIVITY_SERVICE);
            connectivityManager.bindProcessToNetwork(null);
        } else {
            ConnectivityManager.setProcessDefaultNetwork(null);
        }
    }

    //从cursor中解析apn信息
    private APN parseApnInfoFromCursor(Cursor cursor) {
        APN apnInfo = null;
        try {
            if (cursor != null && cursor.moveToFirst()) {
//                String type = cursor.getString(TYPE_INDEX );
//                String mvnoType = cursor.getString(MVNO_TYPE_INDEX);
//                String mvnoMatchData = cursor.getString(MVNO_MATCH_DATA_INDEX);

//                if(shouldSkipApn(type)) {
//                    debug("will move next");
//                    cursor.moveToNext();
//                }
//                if ((!TextUtils.isEmpty(mvnoType) && Integer.parseInt(mvnoType) != 0)&& !TextUtils.isEmpty(mvnoMatchData)) {
//                    //mvnoList -- 虚拟运营商 --只要是虚拟运营商都不会显示？
//                    debug("虚拟运营商 --只要是虚拟运营商都不会显示？ ");
//                } else {
                    //mnoList -- MVNO(Mobile Virtaul Network Operator)虚拟网络运营商，没有自己的实体网络，通过租用MNO(Mobile Network Operator)的网络来提供网络服务。
                    apnInfo = new APN(cursor.getString(ID_INDEX), cursor.getString(NAME_INDEX), cursor.getString(APN_INDEX),
                            cursor.getString(PROXY_INDEX), cursor.getString(PROXY_PORT_INDEX), cursor.getString(USER_INDEX),
                            cursor.getString(PASSWORD_INDEX), cursor.getString(SERVER_INDEX), cursor.getString(MMSC_INDEX),
                            cursor.getString(MMS_PROXY_INDEX), cursor.getString(MMS_PORT_INDEX), cursor.getString(MCC_INDEX),
                            cursor.getString(MNC_INDEX), cursor.getInt(AUTH_TYPE_INDEX), cursor.getString(TYPE_INDEX),
                            cursor.getString(PROTOCOL_INDEX), cursor.getString(ROAMING_PROTOCOL_INDEX), cursor.getInt(CARRIER_ENABLED_INDEX),
                            cursor.getString(BEARER_INDEX),cursor.getString(BEARER_BITMASK_INDEX), cursor.getInt(MVNO_TYPE_INDEX), cursor.getString(MVNO_MATCH_DATA_INDEX),
                            cursor.getString(NUMERIC_INDEX), cursor.getString(CURRENT_INDEX));
//                    debug("apnInfo = " + apnInfo.toString());
                }
//            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }
        return apnInfo;
    }

    private String getWhere(boolean fetchAll){
        debug("enter getWhere()");
        String mccmnc = "";
        if (!fetchAll) {
            mccmnc = getSimOperator();
            debug("mccmnc = " + mccmnc);
        }

        String where = "numeric=\"" + mccmnc + "\"";
        where += " AND NOT (type='ia' AND (apn=\"\" OR apn IS NULL)) AND user_visible!=0";
        debug("where: " + where);
        return where;
    }

    //获取指定卡槽上sim对应的运营商编码
    public String getSimOperator() {
        return telephonyManager.getSimOperator();
    }

    private boolean shouldSkipApn(String type) {
        boolean isSkipApn = "cmmail".equals(type);
        debug("isSkipApn = " + isSkipApn);
        return isSkipApn;
        //return "cmmail".equals(type);
    }

    private Uri getPreferApnUri(int subId) {
        Uri preferredUri = Uri.withAppendedPath(PREFERAPN_URI, "/subId/" + subId);
//        debug("getPreferredApnUri: " + preferredUri);
        return preferredUri;
    }

    private String checkNotSet(String value) {
        return value == null ?  "" : value;
    }

    private Uri getDefaultApnUri(int subId) {
        Uri defaultUri = Uri.withAppendedPath(DEFAULTAPN_URI, "subId/" + subId);
//        debug("default apn uri : " + defaultUri);
        return defaultUri;
    }

    private static int getBitmaskForTech(int radioTech) {
        if (radioTech >= 1) {
            return (1 << (radioTech - 1));
        }
        return 0;
    }

    public static boolean bitmaskHasTech(int bearerBitmask, int radioTech) {
        if (bearerBitmask == 0) {
            return true;
        } else if (radioTech >= 1) {
            return ((bearerBitmask & (1 << (radioTech - 1))) != 0);
        }
        return false;
    }

    public int getBearerBitmaskValue(String bearers) {
        debug("getBearerBitmaskValue array = " + bearers);
        int bearerBitmask = 0;
        try {
            if (TextUtils.isEmpty(bearers)) {
                return bearerBitmask;
            }
            JSONArray array = new JSONArray(bearers);
            for (int i = 0; i < array.length(); i++) {
                String bearer = array.optString(i);
                if (Integer.parseInt(bearer) == 0) {
                    bearerBitmask = 0;
                    break;
                } else {
                    bearerBitmask |= getBitmaskForTech(Integer.parseInt(bearer));
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return bearerBitmask;
    }

    public int getBearerValue(int bearerBitmask) {
        int bearerVal,mBearerInitialVal = 0;
        if (bearerBitmask == 0 || mBearerInitialVal == 0) {
            bearerVal = 0;
        } else if (bitmaskHasTech(bearerBitmask, mBearerInitialVal)) {
            bearerVal = mBearerInitialVal;
        } else {
            bearerVal = 0;
        }
        return bearerVal;
    }

    private void debug(String str) {
        if (DEBUG) {
            BBLog.d(TAG, str);
        }
    }

    public static class APN{
        private String id ;
        private String name;
        private String apn;
        private String proxy ;
        private String port;
        private String mmsProxy ;
        private String mmsPort ;
        private String server;
        private String user;
        private String password ;
        private String mmsc ;
        private String mcc ;
        private String mnc;
        private String numeric ;
        private int authType = -1 ;
        private String apnType;
        private String protocol;
        private String roamingProtocol ;
        private String current ;
        private int carrierEnabled = 1;
        private String bearer ;
        private String bearerBitmask;
        private int mvnoType = 0;
        private String mvnoMatchData ;
        private int priorityOrder = -1;
        private String proId; // 关联的anp profile id
        public APN(){}

        public APN(String id, String name, String apn, String proxy, String port,
                   String user, String password, String server, String mmsc, String mmsProxy,
                   String mmsPort, String mcc, String mnc, int authType, String apnType,
                   String protocol, String roamingProtocol, int carrierEnabled, String bearer,String bearerBitmask,
                   int mvnoType, String mvnoMatchData, String numeric, String current) {
            this.id = id;
            this.name = name;
            this.apn = apn;
            this.proxy = proxy;
            this.port = port;
            this.user = user;
            this.password = password;
            this.server = server;
            this.mmsc = mmsc;
            this.mmsProxy = mmsProxy;
            this.mmsPort = mmsPort;
            this.mcc = mcc;
            this.mnc = mnc;
            this.authType = authType;
            this.apnType = apnType;
            this.protocol = protocol;
            this.roamingProtocol = roamingProtocol;
            this.carrierEnabled = carrierEnabled;
            this.bearer = bearer;
            this.bearerBitmask = bearerBitmask;
            this.mvnoType = mvnoType;
            this.mvnoMatchData = mvnoMatchData;
            this.numeric = numeric;
            this.current = TextUtils.isEmpty(current) ? "0": current;
        }

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getApn() {
            return apn;
        }

        public void setApn(String apn) {
            this.apn = apn;
        }

        public String getProxy() {
            return proxy;
        }

        public void setProxy(String proxy) {
            this.proxy = proxy;
        }

        public String getPort() {
            return port;
        }

        public void setPort(String port) {
            this.port = port;
        }

        public String getMmsProxy() {
            return mmsProxy;
        }

        public void setMmsProxy(String mmsProxy) {
            this.mmsProxy = mmsProxy;
        }

        public String getMmsPort() {
            return mmsPort;
        }

        public void setMmsPort(String mmsPort) {
            this.mmsPort = mmsPort;
        }

        public String getServer() {
            return server;
        }

        public void setServer(String server) {
            this.server = server;
        }

        public String getUser() {
            return user;
        }

        public void setUser(String user) {
            this.user = user;
        }

        public String getPassword() {
            return password;
        }

        public void setPassword(String password) {
            this.password = password;
        }

        public String getMmsc() {
            return mmsc;
        }

        public void setMmsc(String mmsc) {
            this.mmsc = mmsc;
        }

        public String getMcc() {
            return mcc;
        }

        public void setMcc(String mcc) {
            this.mcc = mcc;
        }

        public String getMnc() {
            return mnc;
        }

        public void setMnc(String mnc) {
            this.mnc = mnc;
        }

        public String getNumeric() {
            return numeric;
        }

        public void setNumeric(String numeric) {
            this.numeric = numeric;
        }

        public int getAuthType() {
            return authType;
        }

        public void setAuthType(int authType) {
            this.authType = authType;
        }

        public String getApnType() {
            return apnType;
        }

        public void setApnType(String apnType) {
            this.apnType = apnType;
        }

        public String getProtocol() {
            return protocol;
        }

        public void setProtocol(String protocol) {
            this.protocol = protocol;
        }

        public String getRoamingProtocol() {
            return roamingProtocol;
        }

        public void setRoamingProtocol(String roamingProtocol) {
            this.roamingProtocol = roamingProtocol;
        }

        public String getCurrent() {
            return current;
        }

        public void setCurrent(String current) {
            this.current = current;
        }

        public int getCarrierEnabled() {
            return carrierEnabled;
        }

        public void setCarrierEnabled(int carrierEnabled) {
            this.carrierEnabled = carrierEnabled;
        }

        public String getBearer() {
            return bearer;
        }

        public void setBearer(String bearer) {
            this.bearer = bearer;
        }

        public String getBearerBitmask() {
            return bearerBitmask;
        }

        public void setBearerBitmask(String bearerBitmask) {
            this.bearerBitmask = bearerBitmask;
        }

        public int getMvnoType() {
            return mvnoType;
        }

        public void setMvnoType(int mvnoType) {
            this.mvnoType = mvnoType;
        }

        public String getMvnoMatchData() {
            return mvnoMatchData;
        }

        public void setMvnoMatchData(String mvnoMatchData) {
            this.mvnoMatchData = mvnoMatchData;
        }

        public int getPriorityOrder() {
            return priorityOrder;
        }

        public void setPriorityOrder(int priorityOrder) {
            this.priorityOrder = priorityOrder;
        }

        public String getProId() {
            return proId;
        }

        public void setProId(String proId) {
            this.proId = proId;
        }

        @Override
        public String toString() {
            return "APN{" +
                    "id='" + id + '\'' +
                    ", name='" + name + '\'' +
                    ", apn='" + apn + '\'' +
                    ", proxy='" + proxy + '\'' +
                    ", port='" + port + '\'' +
                    ", user='" + user + '\'' +
                    ", password='" + password + '\'' +
                    ", server='" + server + '\'' +
                    ", mmsc='" + mmsc + '\'' +
                    ", mmsProxy='" + mmsProxy + '\'' +
                    ", mmsPort='" + mmsPort + '\'' +
                    ", mcc='" + mcc + '\'' +
                    ", mnc='" + mnc + '\'' +
                    ", authType=" + authType +
                    ", apnType='" + apnType + '\'' +
                    ", protocol='" + protocol + '\'' +
                    ", roamingProtocol='" + roamingProtocol + '\'' +
                    ", carrierEnabled=" + carrierEnabled +
                    ", bearer='" + bearer + '\'' +
                    ", bearerBitmask='" + bearerBitmask + '\'' +
                    ", mvnoType=" + mvnoType +
                    ", mvnoMatchData='" + mvnoMatchData + '\'' +
                    ", numeric='" + numeric + '\'' +
                    ", current='" + current + '\'' +
                    ", priorityOrder='" + priorityOrder + '\'' +
                    ", proId='" + proId + '\'' +
                    '}';
        }
    }
}


