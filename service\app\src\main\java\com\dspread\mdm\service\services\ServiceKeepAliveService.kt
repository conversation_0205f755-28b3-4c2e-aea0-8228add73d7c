package com.dspread.mdm.service.services

import android.app.Service
import android.content.Intent
import android.os.IBinder
import com.dspread.mdm.service.platform.manager.WakeLockManager
import com.dspread.mdm.service.utils.log.Logger

/**
 * 服务保活服务
 * 专门用于保持进程活跃，确保自更新时广播接收器能正常工作
 * 替代原来的HardwareDetectUploadService的保活功能
 */
class ServiceKeepAliveService : Service() {
    
    companion object {
        private const val TAG = "ServiceKeepAliveService"
        private const val KEEP_ALIVE_WAKELOCK_TAG = "ServiceKeepAlive"
    }
    
    override fun onCreate() {
        super.onCreate()
        Logger.serve("$TAG 保活服务创建")
    }
    
    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        Logger.serve("$TAG 保活服务启动")
        
        // 获取短期WakeLock，保持进程活跃
        WakeLockManager.acquireWakeLock(this, KEEP_ALIVE_WAKELOCK_TAG, 30000) // 30秒
        
        // 30秒后释放WakeLock
        android.os.Handler(android.os.Looper.getMainLooper()).postDelayed({
            WakeLockManager.releaseWakeLock(KEEP_ALIVE_WAKELOCK_TAG)
            Logger.serve("$TAG 保活任务完成，释放WakeLock")
        }, 30000)
        
        return START_NOT_STICKY // 不需要系统重启，主要是短期保活
    }
    
    override fun onDestroy() {
        super.onDestroy()
        Logger.serve("$TAG 保活服务销毁")
        
        // 确保释放WakeLock
        WakeLockManager.releaseWakeLock(KEEP_ALIVE_WAKELOCK_TAG)
    }
    
    override fun onBind(intent: Intent?): IBinder? = null
}
