package com.dspread.mdm.service.network.websocket.constant

/**
 * WebSocket 事务代码常量
 */
object WsTransactionCodes {
    
    // 响应确认
    const val WS_RESPONSE = "C0000"                    // WebSocket 响应确认
    
    // 任务和规则结果上传
    const val TASK_RESULT_UPLOAD = "C0108"             // 任务结果上送
    const val RULEBASED_RESULT_UPLOAD = "C0107"        // 规则结果上送
    
    // 设备状态上传
    const val DEVICE_STATUS_UPLOAD = "C0201"           // 终端使用状态上送
    const val DEVICE_EVENT_UPLOAD = "C0202"            // 终端上送事件信息
    
    // 应用和系统信息上传
    const val APPS_INFO_UPLOAD = "C0901"               // 应用信息上传
    const val BATTERY_STATUS_UPLOAD = "C0902"          // 电池状态事件信息
    const val DATA_INFO_UPLOAD = "C0903"               // 数据信息上传
    const val NETWORK_STATUS_UPLOAD = "C0904"          // 网络状态上传
    const val NETWORK_TRAFFIC_UPLOAD = "C0905"         // 网络流量统计上传
    const val DEVICE_INFO_UPLOAD = "C0906"             // 设备固件信息上传
    
    // 终端信息上传
    const val TERMINAL_INFO_UPLOAD = "C0109"           // 终端信息上送
    
    const val CHECK_OS_UPDATE = "CR004"                // OS 更新检查

    // 服务器命令 (SC系列)
    const val CMD_REBOOT = "SC001"                      // 重启命令
    const val CMD_NOTICE = "SC002"                      // 通知消息
//    const val CMD_WIFI_CONFIG = "SC003"                 // WiFi配置
    const val CMD_HEARTBEAT = "SC004"                   // 心跳
    const val CMD_SEND_GEO_OTP = "SC005"                 // 推送信息给终端(OTP)
    const val CMD_SOUND_PLAY = "SC006"                  // 播放声音
    const val CMD_LOG_UPLOAD = "SC007"                  // 日志上传
    const val CMD_SCREENSHOT = "SC008"                  // 截屏
    const val CMD_LOCATION_UPLOAD = "SC009"             // 位置上传
    const val CMD_PROVISION_TIMER = "SC010"             // 定时器设置
    const val CMD_WIFI_SCAN = "SC011"                   // WiFi扫描
    const val CMD_MOBILE_DATA = "SC012"                 // 移动数据控制
    const val CMD_SYSTEM_UPDATE = "SC013"               // 系统更新
    const val CMD_APP_INSTALL = "SC014"                 // 应用安装
    const val CMD_APP_UNINSTALL = "SC015"               // 应用卸载
    const val CMD_CLEAR_DATA = "SC016"                  // 清除数据
    const val CMD_FORCE_STOP = "SC017"                  // 强制停止应用

    const val CMD_WIFI_CONFIG = "ST006"                  // WiFi配置
    const val CMD_GEOFENCE_CONFIG = "ST007"              // 地理围栏配置
    const val CMD_APN_CONFIG = "ST009"                   // APN配置管理
}
