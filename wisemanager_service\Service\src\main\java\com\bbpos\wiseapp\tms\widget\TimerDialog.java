package com.bbpos.wiseapp.tms.widget;

import android.app.AlertDialog;
import android.app.Dialog;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.os.CountDownTimer;
import android.view.View;
import android.view.WindowManager;
import android.widget.TextView;

import com.bbpos.wiseapp.logger.BBLog;
import com.bbpos.wiseapp.service.R;
import com.bbpos.wiseapp.tms.utils.BroadcastActions;

import org.json.JSONObject;

public class TimerDialog {
    private Context mContext;
    private Dialog mDialog;
    private CountDownTimer mCountDownTimer;
    private int mCount;
    private OnDialogClick mOnDialogClick;
    TextView tv_install;
    TextView tv_cancel;
    String timerString;

    public interface OnDialogClick {
        public void onClickPositive();
        public void onClickNegative();
    }

    public TimerDialog(Context context, int count) {
        mContext = context;
        mCount = count;
        mDialog = new Dialog(mContext, R.style.dialog_style_ex);
        mDialog.setContentView(R.layout.dialog_confirm);
        TextView tv_title = (TextView) mDialog.findViewById(R.id.tv_title);
        tv_title.setText(mContext.getString(R.string.unbox_reset));
        TextView tv_content = (TextView) mDialog.findViewById(R.id.tv_content);
        tv_content.setText(mContext.getString(R.string.unbox_tip_dialog_message_reset));
        mDialog.setCanceledOnTouchOutside(false);
        tv_cancel = (TextView) mDialog.findViewById(R.id.tv_cancel);
        tv_install = (TextView) mDialog.findViewById(R.id.tv_install);
        mDialog.getWindow().setType((WindowManager.LayoutParams.TYPE_SYSTEM_ALERT));
    }

    public void setButton(String posiText, String negaText, OnDialogClick onDialogClick) {
        timerString = posiText;
        tv_install.setText(posiText);
        tv_install.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                mDialog.dismiss();
                mDialog = null;
                mCountDownTimer.cancel();
                mCountDownTimer = null;
                onDialogClick.onClickPositive();
            }
        });
        tv_cancel.setText(negaText);
        tv_cancel.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                mDialog.dismiss();
                mDialog = null;
                mCountDownTimer.cancel();
                mCountDownTimer = null;
                onDialogClick.onClickNegative();
            }
        });
        mOnDialogClick = onDialogClick;
    }

    public void showDialog() {
        if (mDialog != null) {
            if (mDialog.isShowing()) {
                mDialog.dismiss();
            }
            mDialog.show();
            if (mCountDownTimer != null) {
                mCountDownTimer.cancel();
                mCountDownTimer = null;
            }
            mCountDownTimer = new CountDownTimer(mCount * 1000,1000) {
                @Override
                public void onTick(long millisUntilFinished) {
                    tv_install.setText(timerString + "(" + millisUntilFinished/1000+" sec)");
                }

                @Override
                public void onFinish() {
                    if (mDialog != null) {
                        mDialog.dismiss();
                    }
                    mCountDownTimer.cancel();
                    mCountDownTimer = null;
                    mOnDialogClick.onClickPositive();
                }
            };
            mCountDownTimer.start();
        }
    }
}
