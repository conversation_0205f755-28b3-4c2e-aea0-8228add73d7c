package com.dspread.mdm.service.modules.remoteview

import android.content.Context
import android.util.Base64
import com.dspread.mdm.service.modules.remoteview.model.RemoteViewConfig
import com.dspread.mdm.service.modules.remoteview.model.WebSocketConnectionState
import com.dspread.mdm.service.platform.api.device.DeviceInfoApi
import com.dspread.mdm.service.utils.log.Logger
import com.dspread.mdm.service.modules.provisioning.ProvisioningManager
import kotlinx.coroutines.*
import okhttp3.*
import java.util.concurrent.TimeUnit
import javax.net.ssl.*
import java.security.cert.X509Certificate

/**
 * Remote View WebSocket 连接管理器
 * 负责WebSocket连接的建立、维护和数据传输
 */
class RemoteViewWebSocketManager(
    private val context: Context,
    private val config: RemoteViewConfig
) {

    private var webSocket: WebSocket? = null
    private var okHttpClient: OkHttpClient? = null
    private var connectionState = WebSocketConnectionState()
    private var reconnectJob: Job? = null
    private var isManualDisconnect = false
    
    // 事件回调
    var onConnectionStateChanged: ((WebSocketConnectionState) -> Unit)? = null
    var onMessageReceived: ((String) -> Unit)? = null
    var onError: ((String, Throwable?) -> Unit)? = null

    /**
     * 初始化WebSocket客户端
     */
    private fun initializeClient() {
        if (okHttpClient == null) {
            okHttpClient = createUnsafeOkHttpClient()
        }
    }

    /**
     * 创建不验证SSL的OkHttpClient（用于测试环境）
     */
    private fun createUnsafeOkHttpClient(): OkHttpClient {
        return try {
            // 创建信任所有证书的TrustManager
            val trustAllCerts = arrayOf<TrustManager>(object : X509TrustManager {
                override fun checkClientTrusted(chain: Array<X509Certificate>, authType: String) {}
                override fun checkServerTrusted(chain: Array<X509Certificate>, authType: String) {}
                override fun getAcceptedIssuers(): Array<X509Certificate> = arrayOf()
            })

            // 安装信任所有证书的TrustManager
            val sslContext = SSLContext.getInstance("SSL")
            sslContext.init(null, trustAllCerts, java.security.SecureRandom())
            val sslSocketFactory = sslContext.socketFactory

            OkHttpClient.Builder()
                .connectTimeout(config.connectionTimeout, TimeUnit.MILLISECONDS)
                .readTimeout(30, TimeUnit.SECONDS)
                .writeTimeout(30, TimeUnit.SECONDS)
                .pingInterval(30, TimeUnit.SECONDS)
                .sslSocketFactory(sslSocketFactory, trustAllCerts[0] as X509TrustManager)
                .hostnameVerifier { _, _ -> true } // 不验证主机名
                .build()
        } catch (e: Exception) {
            Logger.remoteE("创建不安全的OkHttpClient失败，使用默认客户端", e)
            // 如果创建失败，使用默认客户端
            OkHttpClient.Builder()
                .connectTimeout(config.connectionTimeout, TimeUnit.MILLISECONDS)
                .readTimeout(30, TimeUnit.SECONDS)
                .writeTimeout(30, TimeUnit.SECONDS)
                .pingInterval(30, TimeUnit.SECONDS)
                .build()
        }
    }

    /**
     * 连接WebSocket
     */
    suspend fun connect(): Boolean = withContext(Dispatchers.IO) {
        try {
            if (isConnected()) {
                Logger.remote("WebSocket已连接，跳过重复连接")
                return@withContext true
            }

            initializeClient()
            isManualDisconnect = false
            
            val deviceInfo = DeviceInfoApi(context)
            val serialNumber = deviceInfo.getSerialNumber()
            val wsUrl = "${config.websocketUrl}?serialNo=$serialNumber"
            
            Logger.remote("开始连接WebSocket: $wsUrl")
            
            val request = Request.Builder()
                .url(wsUrl)
                .build()

            webSocket = okHttpClient?.newWebSocket(request, createWebSocketListener())

            // 智能等待连接结果：每500ms检查一次，最多等待connectionTimeout时间
            val startTime = System.currentTimeMillis()
            var connected = false

            while (System.currentTimeMillis() - startTime < config.connectionTimeout) {
                delay(500) // 每500ms检查一次
                connected = isConnected()
                if (connected) {
                    Logger.remote("WebSocket连接成功，耗时: ${System.currentTimeMillis() - startTime}ms")
                    updateConnectionState(isConnected = true, reconnectCount = 0)
                    break
                }
            }

            if (!connected) {
                Logger.remoteE("WebSocket连接超时，等待时间: ${config.connectionTimeout}ms")
            }

            connected
        } catch (e: Exception) {
            Logger.remoteE("WebSocket连接异常", e)
            onError?.invoke("连接失败: ${e.message}", e)
            false
        }
    }

    /**
     * 断开WebSocket连接
     */
    fun disconnect() {
        // Logger.remote("断开WebSocket连接") // 减少重复日志
        isManualDisconnect = true
        reconnectJob?.cancel()
        
        webSocket?.close(1000, "Manual disconnect")
        webSocket = null
        
        updateConnectionState(isConnected = false, reconnectCount = 0)
    }

    /**
     * 发送文本消息
     */
    fun sendText(message: String): Boolean {
        return try {
            val success = webSocket?.send(message) ?: false
            if (!success) {
                Logger.remoteW("发送消息失败，WebSocket可能未连接")
            }
            success
        } catch (e: Exception) {
            Logger.remoteE("发送消息异常", e)
            false
        }
    }

    /**
     * 发送二进制数据（Base64编码）
     */
    fun sendBinaryData(data: ByteArray): Boolean {
        return try {
            val base64Data = Base64.encodeToString(data, Base64.DEFAULT)
            sendText(base64Data)
        } catch (e: Exception) {
            Logger.remoteE("发送二进制数据异常", e)
            false
        }
    }

    /**
     * 检查连接状态
     */
    fun isConnected(): Boolean {
        // 增强连接状态检查：除了检查内部状态，还要检查WebSocket对象的有效性
        val webSocketValid = webSocket != null
        val stateConnected = connectionState.isConnected

        // 如果WebSocket对象为null但状态显示连接，更新状态
        if (!webSocketValid && stateConnected) {
            Logger.remoteW("WebSocket对象无效，更新连接状态为断开")
            updateConnectionState(isConnected = false, lastError = "WebSocket对象无效")
            return false
        }

        // 检查连接是否超时（超过2分钟没有活动认为连接失效）
        val now = System.currentTimeMillis()
        val lastConnectTime = connectionState.lastConnectTime
        val connectionAge = now - lastConnectTime

        if (stateConnected && connectionAge > 120_000) { // 2分钟
            Logger.remoteW("连接超时，上次连接时间: ${connectionAge}ms 前")
            updateConnectionState(isConnected = false, lastError = "连接超时")
            return false
        }

        return stateConnected && webSocketValid
    }

    /**
     * 获取连接状态
     */
    fun getConnectionState(): WebSocketConnectionState {
        return connectionState
    }

    /**
     * 创建WebSocket监听器
     */
    private fun createWebSocketListener(): WebSocketListener {
        return object : WebSocketListener() {
            override fun onOpen(webSocket: WebSocket, response: Response) {
                Logger.remote("WebSocket连接已打开")
                updateConnectionState(
                    isConnected = true,
                    reconnectCount = 0,
                    lastConnectTime = System.currentTimeMillis()
                )
            }

            override fun onMessage(webSocket: WebSocket, text: String) {
                Logger.remote("收到WebSocket消息: $text")
                onMessageReceived?.invoke(text)
            }

            override fun onClosing(webSocket: WebSocket, code: Int, reason: String) {
                Logger.remote("WebSocket正在关闭: code=$code, reason=$reason")
            }

            override fun onClosed(webSocket: WebSocket, code: Int, reason: String) {
                Logger.remote("WebSocket已关闭: code=$code, reason=$reason")
                updateConnectionState(isConnected = false, lastError = reason)
                
                // 如果不是手动断开，尝试重连
                if (!isManualDisconnect) {
                    startReconnect()
                }
            }

            override fun onFailure(webSocket: WebSocket, t: Throwable, response: Response?) {
                Logger.remoteE("WebSocket连接失败", t)
                val errorMsg = "连接失败: ${t.message}"
                updateConnectionState(isConnected = false, lastError = errorMsg)
                onError?.invoke(errorMsg, t)
                
                // 如果不是手动断开，尝试重连
                if (!isManualDisconnect) {
                    startReconnect()
                }
            }
        }
    }

    /**
     * 开始重连
     */
    private fun startReconnect() {
        if (connectionState.reconnectCount >= config.reconnectMaxAttempts) {
            Logger.remoteE("重连次数已达上限，停止重连")
            onError?.invoke("重连失败，已达最大重试次数", null)
            return
        }

        reconnectJob?.cancel()
        reconnectJob = CoroutineScope(Dispatchers.IO).launch {
            delay(config.reconnectInterval)
            
            if (!isManualDisconnect) {
                Logger.remote("尝试重连WebSocket，第${connectionState.reconnectCount + 1}次")
                updateConnectionState(reconnectCount = connectionState.reconnectCount + 1)
                
                val success = connect()
                if (!success) {
                    // 连接失败，继续重连
                    startReconnect()
                }
            }
        }
    }

    /**
     * 更新连接状态
     */
    private fun updateConnectionState(
        isConnected: Boolean = connectionState.isConnected,
        reconnectCount: Int = connectionState.reconnectCount,
        lastConnectTime: Long = connectionState.lastConnectTime,
        lastError: String? = connectionState.lastError
    ) {
        connectionState = WebSocketConnectionState(
            isConnected = isConnected,
            reconnectCount = reconnectCount,
            lastConnectTime = lastConnectTime,
            lastError = lastError
        )
        
        onConnectionStateChanged?.invoke(connectionState)
    }

    /**
     * 释放资源
     */
    fun release() {
        // Logger.remote("释放WebSocket资源") // 减少重复日志
        disconnect()
        reconnectJob?.cancel()

        okHttpClient?.dispatcher?.executorService?.shutdown()
        okHttpClient = null
    }

    companion object {
        /**
         * 获取RemoteView WebSocket URL
         * 只从Provisioning配置获取，ProvisioningConfig会提供默认配置
         */
        fun getRemoteViewWebSocketUrl(context: Context): String {
            return try {
                val provisioningManager = ProvisioningManager.getInstance(context)
                val provisioningConfig = try {
                    provisioningManager.getCurrentConfig()
                } catch (e: Exception) {
                    Logger.remote("Provisioning配置解析失败: ${e.message}")
                    null
                }

                if (provisioningConfig != null) {
                    val remoteUrl = provisioningConfig.polling.remoteUrl
                    Logger.remote("使用Provisioning配置的RemoteView URL: $remoteUrl")
                    return remoteUrl
                } else {
                    Logger.remote("无法获取Provisioning配置，RemoteView初始化失败")
                    return ""
                }
            } catch (e: Exception) {
                Logger.remote("获取RemoteView WebSocket URL失败: ${e.message}")
                return ""
            }
        }
    }
}
