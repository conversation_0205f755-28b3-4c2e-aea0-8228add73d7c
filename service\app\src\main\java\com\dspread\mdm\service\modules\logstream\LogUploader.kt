package com.dspread.mdm.service.modules.logstream

import android.content.Context
import com.dspread.mdm.service.modules.logstream.model.LogStreamStatistics
import com.dspread.mdm.service.modules.logstream.model.UploadStatus
import com.dspread.mdm.service.modules.logstream.model.UploadTask
import com.dspread.mdm.service.utils.log.Logger
import kotlinx.coroutines.*
import org.json.JSONObject
import java.io.*
import java.net.HttpURLConnection
import java.net.URL
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.atomic.AtomicBoolean
import java.util.concurrent.atomic.AtomicLong
import java.io.PrintWriter
import java.io.OutputStreamWriter

/**
 * 日志上传器
 * 基于现有HttpsClient实现大文件分片上传和断点续传
 */
class LogUploader(
    private val context: Context
) {
    
    companion object {
        private const val TAG = "[LogUploader]"
        private const val UPLOAD_TIMEOUT = 60000L // 60秒超时
        private const val MAX_RETRY_ATTEMPTS = 3
        private const val RETRY_DELAY = 5000L // 5秒重试延迟
        private const val PROGRESS_UPDATE_INTERVAL = 1000L // 1秒进度更新间隔
    }
    
    // 创建HTTPS客户端扩展
    private val httpsClient = HttpsClientExtension()

    // S3上传器
    private val s3Uploader = S3LogUploader(context)

    // 上传任务管理
    private val activeTasks = ConcurrentHashMap<String, UploadTaskContext>()
    private val uploadStatistics = LogStreamStatistics()
    
    /**
     * 上传单个文件
     */
    suspend fun uploadFile(
        filePath: String,
        uploadUrl: String,
        headers: Map<String, String> = emptyMap(),
        onProgress: ((Float) -> Unit)? = null
    ): Result<UploadResponse> {
        return try {
            val file = File(filePath)
            if (!file.exists()) {
                return Result.failure(FileNotFoundException("文件不存在: $filePath"))
            }
            
            Logger.logStream("$TAG 开始上传文件: ${file.name}, 大小: ${file.length()}B")
            
            val taskId = generateTaskId()
            val task = UploadTask(
                id = taskId,
                filePath = filePath,
                uploadUrl = uploadUrl,
                status = UploadStatus.PREPARING,
                createdTime = System.currentTimeMillis(),
                fileSize = file.length(),
                chunkSize = 1024 * 1024, // 1MB分片
                totalChunks = ((file.length() + 1024 * 1024 - 1) / (1024 * 1024)).toInt(),
                headers = headers
            )
            
            val context = UploadTaskContext(
                task = task,
                isActive = AtomicBoolean(true),
                uploadedBytes = AtomicLong(0),
                onProgress = onProgress
            )
            
            activeTasks[taskId] = context
            
            try {
                val result = if (file.length() <= task.chunkSize) {
                    // 小文件直接上传
                    uploadSmallFile(context)
                } else {
                    // 大文件分片上传
                    uploadLargeFile(context)
                }
                
                if (result.isSuccess) {
                    uploadStatistics.recordUploadCompleted(file.length())
                    Logger.logStream("$TAG 文件上传成功: ${file.name}")
                } else {
                    uploadStatistics.recordUploadFailed()
                    Logger.logStreamE("$TAG 文件上传失败: ${file.name}")
                }
                
                result
            } finally {
                activeTasks.remove(taskId)
            }
            
        } catch (e: Exception) {
            Logger.logStreamE("$TAG 上传文件异常: $filePath", e)
            uploadStatistics.recordUploadFailed()
            Result.failure(e)
        }
    }
    
    /**
     * 上传小文件
     */
    private suspend fun uploadSmallFile(context: UploadTaskContext): Result<UploadResponse> {
        return withContext(Dispatchers.IO) {
            try {
                val task = context.task
                val file = File(task.filePath)
                
                Logger.logStream("$TAG 上传小文件: ${file.name}")
                
                // 更新状态
                updateTaskStatus(context, UploadStatus.UPLOADING)
                
                val response = httpsClient.uploadFile(
                    url = task.uploadUrl,
                    file = file,
                    headers = task.headers,
                    onProgress = { progress ->
                        context.uploadedBytes.set((task.fileSize * progress / 100).toLong())
                        context.onProgress?.invoke(progress)
                    }
                )
                
                if (response.isSuccess) {
                    updateTaskStatus(context, UploadStatus.COMPLETED)
                    context.uploadedBytes.set(task.fileSize)
                    context.onProgress?.invoke(100f)
                    
                    val uploadResponse = UploadResponse(
                        success = true,
                        taskId = task.id,
                        uploadedBytes = task.fileSize,
                        totalBytes = task.fileSize,
                        responseData = response.getOrNull() ?: ""
                    )
                    
                    Result.success(uploadResponse)
                } else {
                    updateTaskStatus(context, UploadStatus.FAILED)
                    Result.failure(Exception("上传失败: ${response.exceptionOrNull()?.message}"))
                }
                
            } catch (e: Exception) {
                updateTaskStatus(context, UploadStatus.FAILED)
                Logger.logStreamE("$TAG 小文件上传异常", e)
                Result.failure(e)
            }
        }
    }
    
    /**
     * 上传大文件（分片上传）
     */
    private suspend fun uploadLargeFile(context: UploadTaskContext): Result<UploadResponse> {
        return withContext(Dispatchers.IO) {
            try {
                val task = context.task
                val file = File(task.filePath)
                
                Logger.logStream("$TAG 上传大文件: ${file.name}, 分片数: ${task.totalChunks}")
                
                // 更新状态
                updateTaskStatus(context, UploadStatus.UPLOADING)
                
                // 初始化分片上传
                val initResult = initializeChunkedUpload(context)
                if (initResult.isFailure) {
                    return@withContext Result.failure(initResult.exceptionOrNull()!!)
                }
                
                val uploadId = initResult.getOrNull()
                
                // 上传分片
                val uploadedChunks = mutableListOf<ChunkUploadResult>()
                
                FileInputStream(file).use { fis ->
                    for (chunkIndex in 0 until task.totalChunks) {
                        if (!context.isActive.get()) {
                            Logger.logStream("$TAG 上传任务已取消: ${task.id}")
                            return@withContext Result.failure(Exception("上传任务已取消"))
                        }
                        
                        val chunkResult = uploadChunk(context, fis, chunkIndex, uploadId)
                        if (chunkResult.isSuccess) {
                            uploadedChunks.add(chunkResult.getOrNull()!!)
                            
                            // 更新进度
                            val uploadedBytes = (chunkIndex + 1) * task.chunkSize
                            context.uploadedBytes.set(minOf(uploadedBytes, task.fileSize))
                            val progress = (context.uploadedBytes.get().toFloat() / task.fileSize.toFloat()) * 100f
                            context.onProgress?.invoke(progress)
                            
                        } else {
                            // 重试机制
                            var retryCount = 0
                            var retrySuccess = false
                            
                            while (retryCount < MAX_RETRY_ATTEMPTS && !retrySuccess) {
                                Logger.logStreamW("$TAG 分片上传失败，重试 ${retryCount + 1}/$MAX_RETRY_ATTEMPTS")
                                delay(RETRY_DELAY)
                                
                                val retryResult = uploadChunk(context, fis, chunkIndex, uploadId)
                                if (retryResult.isSuccess) {
                                    uploadedChunks.add(retryResult.getOrNull()!!)
                                    retrySuccess = true
                                } else {
                                    retryCount++
                                }
                            }
                            
                            if (!retrySuccess) {
                                updateTaskStatus(context, UploadStatus.FAILED)
                                return@withContext Result.failure(Exception("分片上传失败: chunk $chunkIndex"))
                            }
                        }
                    }
                }
                
                // 完成分片上传
                val completeResult = completeChunkedUpload(context, uploadId, uploadedChunks)
                if (completeResult.isSuccess) {
                    updateTaskStatus(context, UploadStatus.COMPLETED)
                    context.onProgress?.invoke(100f)
                    
                    val uploadResponse = UploadResponse(
                        success = true,
                        taskId = task.id,
                        uploadedBytes = task.fileSize,
                        totalBytes = task.fileSize,
                        responseData = completeResult.getOrNull() ?: ""
                    )
                    
                    Result.success(uploadResponse)
                } else {
                    updateTaskStatus(context, UploadStatus.FAILED)
                    Result.failure(completeResult.exceptionOrNull()!!)
                }
                
            } catch (e: Exception) {
                updateTaskStatus(context, UploadStatus.FAILED)
                Logger.logStreamE("$TAG 大文件上传异常", e)
                Result.failure(e)
            }
        }
    }
    
    /**
     * 初始化分片上传
     */
    private suspend fun initializeChunkedUpload(context: UploadTaskContext): Result<String> {
        return try {
            val task = context.task
            val initUrl = "${task.uploadUrl}/init"
            
            val requestData = JSONObject().apply {
                put("fileName", File(task.filePath).name)
                put("fileSize", task.fileSize)
                put("chunkSize", task.chunkSize)
                put("totalChunks", task.totalChunks)
            }
            
            val response = httpsClient.post(
                url = initUrl,
                data = requestData.toString(),
                headers = task.headers + ("Content-Type" to "application/json")
            )
            
            if (response.isSuccess) {
                val responseData = response.getOrNull() ?: ""
                val responseJson = JSONObject(responseData)
                val uploadId = responseJson.getString("uploadId")
                
                Logger.logStream("$TAG 分片上传初始化成功: uploadId=$uploadId")
                Result.success(uploadId)
            } else {
                Result.failure(Exception("初始化分片上传失败"))
            }
            
        } catch (e: Exception) {
            Logger.logStreamE("$TAG 初始化分片上传异常", e)
            Result.failure(e)
        }
    }
    
    /**
     * 上传单个分片
     */
    private suspend fun uploadChunk(
        context: UploadTaskContext,
        fis: FileInputStream,
        chunkIndex: Int,
        uploadId: String?
    ): Result<ChunkUploadResult> {
        return try {
            val task = context.task
            val chunkUrl = "${task.uploadUrl}/chunk"
            
            val buffer = ByteArray(task.chunkSize.toInt())
            val bytesRead = fis.read(buffer)
            
            if (bytesRead <= 0) {
                return Result.failure(Exception("读取分片数据失败"))
            }
            
            val chunkData = if (bytesRead < buffer.size) {
                buffer.copyOf(bytesRead)
            } else {
                buffer
            }
            
            val headers = task.headers.toMutableMap().apply {
                put("Content-Type", "application/octet-stream")
                put("X-Chunk-Index", chunkIndex.toString())
                put("X-Chunk-Size", bytesRead.toString())
                uploadId?.let { put("X-Upload-Id", it) }
            }
            
            val response = httpsClient.postBinary(
                url = chunkUrl,
                data = chunkData,
                headers = headers
            )
            
            if (response.isSuccess) {
                val result = ChunkUploadResult(
                    chunkIndex = chunkIndex,
                    size = bytesRead.toLong(),
                    etag = response.getOrNull() ?: "",
                    success = true
                )
                
                Logger.logStream("$TAG 分片上传成功: chunk $chunkIndex, size: $bytesRead")
                Result.success(result)
            } else {
                Logger.logStreamE("$TAG 分片上传失败: chunk $chunkIndex")
                Result.failure(Exception("分片上传失败"))
            }
            
        } catch (e: Exception) {
            Logger.logStreamE("$TAG 上传分片异常: chunk $chunkIndex", e)
            Result.failure(e)
        }
    }
    
    /**
     * 完成分片上传
     */
    private suspend fun completeChunkedUpload(
        context: UploadTaskContext,
        uploadId: String?,
        chunks: List<ChunkUploadResult>
    ): Result<String> {
        return try {
            val task = context.task
            val completeUrl = "${task.uploadUrl}/complete"
            
            val requestData = JSONObject().apply {
                uploadId?.let { put("uploadId", it) }
                put("totalChunks", chunks.size)
                
                val chunksArray = org.json.JSONArray()
                chunks.forEach { chunk ->
                    chunksArray.put(JSONObject().apply {
                        put("index", chunk.chunkIndex)
                        put("size", chunk.size)
                        put("etag", chunk.etag)
                    })
                }
                put("chunks", chunksArray)
            }
            
            val response = httpsClient.post(
                url = completeUrl,
                data = requestData.toString(),
                headers = task.headers + ("Content-Type" to "application/json")
            )
            
            if (response.isSuccess) {
                Logger.logStream("$TAG 分片上传完成: uploadId=$uploadId")
                Result.success(response.getOrNull() ?: "")
            } else {
                Result.failure(Exception("完成分片上传失败"))
            }
            
        } catch (e: Exception) {
            Logger.logStreamE("$TAG 完成分片上传异常", e)
            Result.failure(e)
        }
    }
    
    /**
     * 暂停上传任务
     */
    fun pauseUpload(taskId: String): Result<Unit> {
        return try {
            val context = activeTasks[taskId]
            if (context != null) {
                context.isActive.set(false)
                updateTaskStatus(context, UploadStatus.PAUSED)
                Logger.logStream("$TAG 上传任务已暂停: $taskId")
                Result.success(Unit)
            } else {
                Result.failure(Exception("上传任务不存在: $taskId"))
            }
        } catch (e: Exception) {
            Logger.logStreamE("$TAG 暂停上传任务失败: $taskId", e)
            Result.failure(e)
        }
    }
    
    /**
     * 取消上传任务
     */
    fun cancelUpload(taskId: String): Result<Unit> {
        return try {
            val context = activeTasks[taskId]
            if (context != null) {
                context.isActive.set(false)
                updateTaskStatus(context, UploadStatus.CANCELLED)
                activeTasks.remove(taskId)
                Logger.logStream("$TAG 上传任务已取消: $taskId")
                Result.success(Unit)
            } else {
                Result.failure(Exception("上传任务不存在: $taskId"))
            }
        } catch (e: Exception) {
            Logger.logStreamE("$TAG 取消上传任务失败: $taskId", e)
            Result.failure(e)
        }
    }
    
    /**
     * 获取上传进度
     */
    fun getUploadProgress(taskId: String): Float {
        val context = activeTasks[taskId]
        return if (context != null) {
            val task = context.task
            if (task.fileSize > 0) {
                (context.uploadedBytes.get().toFloat() / task.fileSize.toFloat()) * 100f
            } else {
                0f
            }
        } else {
            0f
        }
    }
    
    /**
     * 获取活跃的上传任务
     */
    fun getActiveTasks(): List<UploadTask> {
        return activeTasks.values.map { it.task }
    }
    
    /**
     * 获取上传统计信息
     */
    fun getStatistics(): LogStreamStatistics {
        return uploadStatistics
    }
    
    /**
     * 更新任务状态
     */
    private fun updateTaskStatus(context: UploadTaskContext, status: UploadStatus) {
        context.task = context.task.copy(status = status)
    }
    
    /**
     * 生成任务ID
     */
    private fun generateTaskId(): String {
        return "upload_${System.currentTimeMillis()}_${(Math.random() * 1000).toInt()}"
    }

    /**
     * 使用S3上传文件
     */
    suspend fun uploadFileToS3(filePath: String): Result<UploadResponse> {
        return try {
            Logger.logStream("$TAG 开始S3上传: $filePath")

            val file = File(filePath)
            if (!file.exists()) {
                return Result.failure(FileNotFoundException("文件不存在: $filePath"))
            }

            val success = s3Uploader.uploadFile(file)

            if (success) {
                Logger.logStream("$TAG S3上传成功: ${file.name}")

                // 创建成功响应
                val response = UploadResponse(
                    success = true,
                    taskId = "s3_upload_${System.currentTimeMillis()}",
                    uploadedBytes = file.length(),
                    totalBytes = file.length(),
                    responseData = "S3上传成功"
                )

                Result.success(response)
            } else {
                Logger.logStreamE("$TAG S3上传失败: ${file.name}")
                Result.failure(Exception("S3上传失败"))
            }

        } catch (e: Exception) {
            Logger.logStreamE("$TAG S3上传异常: $filePath", e)
            Result.failure(e)
        }
    }

    /**
     * 批量S3上传文件
     */
    suspend fun uploadFilesToS3(filePaths: List<String>): Map<String, Result<UploadResponse>> {
        val results = mutableMapOf<String, Result<UploadResponse>>()

        Logger.logStream("$TAG 开始批量S3上传 ${filePaths.size} 个文件")

        for (filePath in filePaths) {
            val result = uploadFileToS3(filePath)
            results[filePath] = result
        }

        val successCount = results.values.count { it.isSuccess }
        Logger.logStream("$TAG 批量S3上传完成: 成功 $successCount/${filePaths.size}")

        return results
    }

    /**
     * S3上传并删除文件
     */
    suspend fun uploadAndDeleteFileS3(filePath: String): Result<UploadResponse> {
        val result = uploadFileToS3(filePath)

        if (result.isSuccess) {
            try {
                val file = File(filePath)
                if (file.delete()) {
                    Logger.logStream("$TAG S3上传后删除文件成功: ${file.name}")
                } else {
                    Logger.logStreamW("$TAG S3上传后删除文件失败: ${file.name}")
                }
            } catch (e: Exception) {
                Logger.logStreamE("$TAG 删除文件异常: $filePath", e)
            }
        }

        return result
    }
}

/**
 * 上传任务上下文
 */
data class UploadTaskContext(
    var task: UploadTask,
    val isActive: AtomicBoolean,
    val uploadedBytes: AtomicLong,
    val onProgress: ((Float) -> Unit)?
)

/**
 * 分片上传结果
 */
data class ChunkUploadResult(
    val chunkIndex: Int,
    val size: Long,
    val etag: String,
    val success: Boolean
)

/**
 * 上传响应
 */
data class UploadResponse(
    val success: Boolean,
    val taskId: String,
    val uploadedBytes: Long,
    val totalBytes: Long,
    val responseData: String,
    val error: String? = null
)

/**
 * HTTPS客户端扩展类
 * 基于现有HttpUtils实现文件上传功能
 */
class HttpsClientExtension {

    companion object {
        private const val TAG = "[HttpsClientExtension]"
    }

    /**
     * 上传文件
     */
    suspend fun uploadFile(
        url: String,
        file: File,
        headers: Map<String, String> = emptyMap(),
        onProgress: ((Float) -> Unit)? = null
    ): Result<String> {
        return withContext(Dispatchers.IO) {
            try {
                Logger.logStream("$TAG 开始上传文件: ${file.name} 到 $url")

                val connection = createConnection(url, headers)

                // 设置multipart/form-data
                val boundary = "----WebKitFormBoundary" + System.currentTimeMillis()
                connection.setRequestProperty("Content-Type", "multipart/form-data; boundary=$boundary")
                connection.setRequestMethod("POST")
                connection.setDoOutput(true)

                val outputStream = connection.getOutputStream()
                val writer = PrintWriter(OutputStreamWriter(outputStream, "UTF-8"), true)

                // 写入文件部分
                writer.append("--$boundary").append("\r\n")
                writer.append("Content-Disposition: form-data; name=\"file\"; filename=\"${file.name}\"").append("\r\n")
                writer.append("Content-Type: application/octet-stream").append("\r\n")
                writer.append("\r\n")
                writer.flush()

                // 写入文件内容
                FileInputStream(file).use { fis ->
                    val buffer = ByteArray(8192)
                    var bytesRead: Int
                    var totalBytes = 0L
                    val fileSize = file.length()

                    while (fis.read(buffer).also { bytesRead = it } != -1) {
                        outputStream.write(buffer, 0, bytesRead)
                        totalBytes += bytesRead

                        // 更新进度
                        onProgress?.invoke((totalBytes.toFloat() / fileSize.toFloat()) * 100f)
                    }
                }

                writer.append("\r\n")
                writer.append("--$boundary--").append("\r\n")
                writer.flush()

                // 读取响应
                val responseCode = connection.getResponseCode()
                val response = if (responseCode == HttpURLConnection.HTTP_OK) {
                    connection.getInputStream().bufferedReader().use { it.readText() }
                } else {
                    connection.getErrorStream()?.bufferedReader()?.use { it.readText() } ?: ""
                }

                connection.disconnect()

                if (responseCode == HttpURLConnection.HTTP_OK) {
                    Logger.logStream("$TAG 文件上传成功: ${file.name}")
                    Result.success(response)
                } else {
                    Logger.logStreamE("$TAG 文件上传失败: ${file.name}, 响应码: $responseCode")
                    Result.failure(Exception("HTTP $responseCode: $response"))
                }

            } catch (e: Exception) {
                Logger.logStreamE("$TAG 文件上传异常: ${file.name}", e)
                Result.failure(e)
            }
        }
    }

    /**
     * POST请求
     */
    suspend fun post(
        url: String,
        data: String,
        headers: Map<String, String> = emptyMap()
    ): Result<String> {
        return withContext(Dispatchers.IO) {
            try {
                val connection = createConnection(url, headers)
                connection.setRequestMethod("POST")
                connection.setDoOutput(true)

                if (!headers.containsKey("Content-Type")) {
                    connection.setRequestProperty("Content-Type", "application/json; charset=UTF-8")
                }

                // 写入数据
                connection.getOutputStream().use { os ->
                    os.write(data.toByteArray(Charsets.UTF_8))
                }

                // 读取响应
                val responseCode = connection.getResponseCode()
                val response = if (responseCode == HttpURLConnection.HTTP_OK) {
                    connection.getInputStream().bufferedReader().use { it.readText() }
                } else {
                    connection.getErrorStream()?.bufferedReader()?.use { it.readText() } ?: ""
                }

                connection.disconnect()

                if (responseCode == HttpURLConnection.HTTP_OK) {
                    Result.success(response)
                } else {
                    Result.failure(Exception("HTTP $responseCode: $response"))
                }

            } catch (e: Exception) {
                Logger.logStreamE("$TAG POST请求异常: $url", e)
                Result.failure(e)
            }
        }
    }

    /**
     * POST二进制数据
     */
    suspend fun postBinary(
        url: String,
        data: ByteArray,
        headers: Map<String, String> = emptyMap()
    ): Result<String> {
        return withContext(Dispatchers.IO) {
            try {
                val connection = createConnection(url, headers)
                connection.setRequestMethod("POST")
                connection.setDoOutput(true)

                if (!headers.containsKey("Content-Type")) {
                    connection.setRequestProperty("Content-Type", "application/octet-stream")
                }

                // 写入二进制数据
                connection.getOutputStream().use { os ->
                    os.write(data)
                }

                // 读取响应
                val responseCode = connection.getResponseCode()
                val response = if (responseCode == HttpURLConnection.HTTP_OK) {
                    connection.getInputStream().bufferedReader().use { it.readText() }
                } else {
                    connection.getErrorStream()?.bufferedReader()?.use { it.readText() } ?: ""
                }

                connection.disconnect()

                if (responseCode == HttpURLConnection.HTTP_OK) {
                    Result.success(response)
                } else {
                    Result.failure(Exception("HTTP $responseCode: $response"))
                }

            } catch (e: Exception) {
                Logger.logStreamE("$TAG POST二进制数据异常: $url", e)
                Result.failure(e)
            }
        }
    }

    /**
     * 创建HTTPS连接
     */
    private fun createConnection(url: String, headers: Map<String, String>): HttpURLConnection {
        val connection = URL(url).openConnection() as HttpURLConnection

        // 设置超时
        connection.setConnectTimeout(30000) // 30秒
        connection.setReadTimeout(60000) // 60秒

        // 设置请求头
        headers.forEach { (key, value) ->
            connection.setRequestProperty(key, value)
        }

        return connection
    }
}
