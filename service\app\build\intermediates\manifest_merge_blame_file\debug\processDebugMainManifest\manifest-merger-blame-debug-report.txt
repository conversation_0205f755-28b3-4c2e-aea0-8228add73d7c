1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.dspread.mdm.service"
4    android:sharedUserId="android.uid.system"
5    android:versionCode="22"
6    android:versionName="1.1.03.20250822.DSPREAD.MDM.SERVICE" >
7
8    <uses-sdk
9        android:minSdkVersion="21"
10        android:targetSdkVersion="34" />
11
12    <!-- ==================== 存储/文件管理权限 ==================== -->
13    <uses-permission android:name="android.permission.MOUNT_UNMOUNT_FILESYSTEMS" />
13-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:7:5-84
13-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:7:22-81
14    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" />
14-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:8:5-82
14-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:8:22-79
15    <uses-permission android:name="android.permission.SCHEDULE_EXACT_ALARM" />
15-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:9:5-79
15-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:9:22-76
16    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
16-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:10:5-81
16-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:10:22-78
17    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
17-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:11:5-80
17-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:11:22-77
18    <uses-permission android:name="android.permission.WRITE_MEDIA_STORAGE" />
18-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:12:5-78
18-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:12:22-75
19    <uses-permission android:name="android.permission.MANAGE_DOCUMENTS" />
19-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:13:5-75
19-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:13:22-72
20
21    <!-- ==================== 网络/通信权限 ==================== -->
22    <uses-permission android:name="android.permission.INTERNET" />
22-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:16:5-67
22-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:16:22-64
23    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
23-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:17:5-79
23-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:17:22-76
24    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
24-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:18:5-76
24-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:18:22-73
25    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />
25-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:19:5-76
25-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:19:22-73
26    <uses-permission android:name="android.permission.CHANGE_NETWORK_STATE" />
26-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:20:5-79
26-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:20:22-76
27    <uses-permission android:name="android.permission.CHANGE_WIFI_MULTICAST_STATE" />
27-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:21:5-86
27-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:21:22-83
28
29    <!-- ==================== 系统基础权限 ==================== -->
30    <!-- WakeLock权限，防止系统深度休眠 -->
31    <uses-permission android:name="android.permission.WAKE_LOCK" />
31-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:25:5-68
31-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:25:22-65
32    <!-- 开机启动权限 -->
33    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
33-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:27:5-81
33-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:27:22-78
34    <!-- 前台服务权限 -->
35    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
35-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:29:5-77
35-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:29:22-74
36    <!-- 查询运行服务权限 -->
37    <uses-permission android:name="android.permission.GET_TASKS" />
37-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:31:5-68
37-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:31:22-65
38    <uses-permission android:name="android.permission.DOWNLOAD_WITHOUT_NOTIFICATION" />
38-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:32:5-88
38-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:32:22-85
39
40    <!-- ==================== 系统更新权限 ==================== -->
41    <uses-permission android:name="android.permission.RECOVERY" />
41-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:35:5-67
41-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:35:22-64
42    <uses-permission android:name="android.permission.ACCESS_CACHE_FILESYSTEM" />
42-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:36:5-82
42-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:36:22-79
43    <uses-permission android:name="android.permission.REBOOT" />
43-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:37:5-65
43-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:37:22-62
44    <uses-permission android:name="android.permission.UPDATE_DEVICE_STATS" />
44-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:38:5-78
44-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:38:22-75
45    <uses-permission android:name="android.permission.WRITE_SECURE_SETTINGS" />
45-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:39:5-80
45-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:39:22-77
46
47    <!-- UpdateEngine专用权限 -->
48    <uses-permission android:name="android.permission.BIND_UPDATE_ENGINE" />
48-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:42:5-77
48-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:42:22-74
49    <uses-permission android:name="android.permission.ACCESS_UPDATE_ENGINE" />
49-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:43:5-79
49-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:43:22-76
50    <uses-permission android:name="android.permission.UPDATE_ENGINE" />
50-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:44:5-72
50-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:44:22-69
51
52    <!-- Android 14+ 额外权限 -->
53    <uses-permission android:name="android.permission.MANAGE_ROLLBACKS" />
53-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:47:5-75
53-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:47:22-72
54    <uses-permission android:name="android.permission.TEST_MANAGE_ROLLBACKS" />
54-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:48:5-80
54-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:48:22-77
55    <uses-permission android:name="android.permission.RECOVERY_REFRESH" />
55-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:49:5-75
55-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:49:22-72
56
57    <!-- ==================== 蓝牙/NFC权限 ==================== -->
58    <uses-permission android:name="android.permission.BLUETOOTH" />
58-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:52:5-68
58-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:52:22-65
59    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />
59-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:53:5-74
59-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:53:22-71
60    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />
60-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:54:5-76
60-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:54:22-73
61    <uses-permission android:name="android.permission.BLUETOOTH_SCAN" />
61-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:55:5-73
61-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:55:22-70
62    <uses-permission android:name="android.permission.BLUETOOTH_ADVERTISE" />
62-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:56:5-78
62-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:56:22-75
63    <uses-permission android:name="android.permission.BLUETOOTH_PRIVILEGED" />
63-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:57:5-79
63-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:57:22-76
64    <uses-permission android:name="android.permission.NFC" />
64-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:58:5-62
64-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:58:22-59
65
66    <!-- ==================== 位置/基站/传感器权限 ==================== -->
67    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
67-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:61:5-81
67-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:61:22-78
68    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
68-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:62:5-79
68-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:62:22-76
69    <uses-permission android:name="android.permission.ACCESS_LOCATION_EXTRA_COMMANDS" />
69-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:63:5-89
69-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:63:22-86
70    <uses-permission android:name="android.permission.ACCESS_CELL_LOCATION" />
70-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:64:5-79
70-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:64:22-76
71    <uses-permission android:name="android.permission.READ_CELL_BROADCASTS" />
71-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:65:5-79
71-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:65:22-76
72    <uses-permission android:name="android.permission.ACTIVITY_RECOGNITION" />
72-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:66:5-79
72-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:66:22-76
73    <uses-permission android:name="android.permission.BODY_SENSORS" />
73-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:67:5-71
73-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:67:22-68
74
75    <!-- ==================== 电话/设备信息权限 ==================== -->
76    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
76-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:70:5-75
76-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:70:22-72
77    <uses-permission android:name="android.permission.READ_PRIVILEGED_PHONE_STATE" />
77-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:71:5-86
77-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:71:22-83
78    <uses-permission android:name="android.permission.CALL_PHONE" />
78-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:72:5-69
78-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:72:22-66
79    <uses-permission android:name="android.permission.ANSWER_PHONE_CALLS" />
79-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:73:5-77
79-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:73:22-74
80    <uses-permission android:name="android.permission.PROCESS_OUTGOING_CALLS" />
80-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:74:5-81
80-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:74:22-78
81    <uses-permission android:name="android.permission.USE_SIP" />
81-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:75:5-66
81-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:75:22-63
82    <uses-permission android:name="android.permission.MODIFY_PHONE_STATE" />
82-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:76:5-77
82-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:76:22-74
83
84    <!-- ==================== 系统/电源/管理权限 ==================== -->
85    <uses-permission android:name="android.permission.SHUTDOWN" />
85-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:79:5-67
85-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:79:22-64
86    <uses-permission android:name="android.permission.DEVICE_POWER" />
86-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:80:5-71
86-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:80:22-68
87    <uses-permission android:name="android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS" />
87-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:81:5-95
87-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:81:22-92
88    <uses-permission android:name="android.permission.BATTERY_STATS" />
88-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:82:5-72
88-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:82:22-69
89    <uses-permission android:name="android.permission.SET_TIME" />
89-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:83:5-67
89-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:83:22-64
90    <uses-permission android:name="android.permission.SET_TIME_ZONE" />
90-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:84:5-72
90-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:84:22-69
91    <uses-permission android:name="android.permission.WRITE_SETTINGS" />
91-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:85:5-73
91-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:85:22-70
92    <uses-permission android:name="android.permission.CHANGE_COMPONENT_ENABLED_STATE" />
92-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:86:5-89
92-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:86:22-86
93    <uses-permission android:name="android.permission.KILL_BACKGROUND_PROCESSES" />
93-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:87:5-84
93-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:87:22-81
94    <uses-permission android:name="android.permission.GET_PACKAGE_SIZE" />
94-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:88:5-75
94-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:88:22-72
95    <uses-permission android:name="android.permission.INTERACT_ACROSS_USERS" />
95-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:89:5-80
95-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:89:22-77
96    <uses-permission android:name="android.permission.INTERACT_ACROSS_USERS_FULL" />
96-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:90:5-85
96-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:90:22-82
97    <uses-permission android:name="android.permission.BROADCAST_STICKY" />
97-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:91:5-75
97-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:91:22-72
98    <uses-permission android:name="android.permission.REORDER_TASKS" />
98-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:92:5-72
98-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:92:22-69
99    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
99-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:93:5-78
99-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:93:22-75
100    <uses-permission android:name="android.permission.EXPAND_STATUS_BAR" />
100-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:94:5-76
100-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:94:22-73
101    <uses-permission android:name="android.permission.STATUS_BAR" />
101-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:95:5-69
101-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:95:22-66
102    <uses-permission android:name="android.permission.STATUS_BAR_SERVICE" />
102-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:96:5-77
102-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:96:22-74
103    <uses-permission android:name="android.permission.MANAGE_USERS" />
103-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:97:5-71
103-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:97:22-68
104    <uses-permission android:name="android.permission.PACKAGE_USAGE_STATS" />
104-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:98:5-78
104-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:98:22-75
105    <uses-permission android:name="android.permission.DUMP" />
105-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:99:5-63
105-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:99:22-60
106    <uses-permission android:name="android.permission.READ_LOGS" />
106-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:100:5-68
106-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:100:22-65
107    <uses-permission android:name="android.permission.SET_DEBUG_APP" />
107-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:101:5-72
107-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:101:22-69
108    <uses-permission android:name="android.permission.PERSISTENT_ACTIVITY" />
108-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:102:5-78
108-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:102:22-75
109    <uses-permission android:name="android.permission.CLEAR_APP_USER_DATA" />
109-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:103:5-78
109-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:103:22-75
110    <uses-permission android:name="android.permission.MANAGE_APP_OPS_MODES" />
110-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:104:5-79
110-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:104:22-76
111    <uses-permission android:name="android.permission.BIND_CARRIER_SERVICE" />
111-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:105:5-79
111-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:105:22-76
112    <uses-permission android:name="android.permission.WRITE_APN_SETTINGS" />
112-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:106:5-77
112-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:106:22-74
113
114    <!-- ==================== 应用/包管理权限 ==================== -->
115    <uses-permission android:name="android.permission.INSTALL_PACKAGES" />
115-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:109:5-75
115-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:109:22-72
116    <uses-permission android:name="android.permission.DELETE_PACKAGES" />
116-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:110:5-74
116-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:110:22-71
117    <uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES" />
117-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:111:5-83
117-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:111:22-80
118    <uses-permission android:name="android.permission.FORCE_STOP_PACKAGES" />
118-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:112:5-78
118-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:112:22-75
119    <uses-permission android:name="android.permission.REQUEST_DELETE_PACKAGES" />
119-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:113:5-82
119-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:113:22-79
120    <uses-permission android:name="android.permission.QUERY_ALL_PACKAGES" />
120-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:114:5-77
120-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:114:22-74
121
122    <!-- ==================== 通知/前台服务权限 ==================== -->
123    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
123-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:117:5-77
123-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:117:22-74
124    <uses-permission android:name="android.permission.ACCESS_NOTIFICATION_POLICY" />
124-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:118:5-85
124-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:118:22-82
125
126    <!-- ==================== 音频/摄像头/多媒体权限 ==================== -->
127    <uses-permission android:name="android.permission.RECORD_AUDIO" />
127-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:121:5-71
127-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:121:22-68
128    <uses-permission android:name="android.permission.CAMERA" />
128-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:122:5-65
128-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:122:22-62
129    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
129-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:123:5-80
129-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:123:22-77
130    <uses-permission android:name="android.permission.VIBRATE" />
130-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:124:5-66
130-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:124:22-63
131
132    <!-- 系统级多媒体捕获权限 -->
133    <uses-permission android:name="android.permission.CAPTURE_AUDIO_OUTPUT" />
133-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:127:5-79
133-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:127:22-76
134    <uses-permission android:name="android.permission.CAPTURE_VIDEO_OUTPUT" />
134-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:128:5-79
134-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:128:22-76
135    <uses-permission android:name="android.permission.CAPTURE_SECURE_VIDEO_OUTPUT" />
135-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:129:5-86
135-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:129:22-83
136
137    <!-- MediaProjection权限 -->
138    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_MEDIA_PROJECTION" />
138-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:132:5-94
138-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:132:22-91
139
140    <!-- ==================== 辅助功能/无障碍权限 ==================== -->
141    <uses-permission android:name="android.permission.BIND_ACCESSIBILITY_SERVICE" />
141-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:135:5-85
141-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:135:22-82
142
143    <!-- ==================== 账户/联系人/日历/SMS/通话/同步相关权限 ==================== -->
144    <uses-permission android:name="android.permission.GET_ACCOUNTS" />
144-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:138:5-71
144-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:138:22-68
145    <uses-permission android:name="android.permission.READ_CALENDAR" />
145-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:139:5-72
145-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:139:22-69
146    <uses-permission android:name="android.permission.READ_CALL_LOG" />
146-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:140:5-72
146-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:140:22-69
147    <uses-permission android:name="android.permission.READ_CONTACTS" />
147-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:141:5-72
147-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:141:22-69
148    <uses-permission android:name="android.permission.READ_SMS" />
148-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:142:5-67
148-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:142:22-64
149    <uses-permission android:name="android.permission.READ_SYNC_SETTINGS" />
149-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:143:5-77
149-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:143:22-74
150    <uses-permission android:name="android.permission.READ_SYNC_STATS" />
150-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:144:5-74
150-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:144:22-71
151    <uses-permission android:name="android.permission.RECEIVE_MMS" />
151-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:145:5-70
151-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:145:22-67
152    <uses-permission android:name="android.permission.RECEIVE_SMS" />
152-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:146:5-70
152-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:146:22-67
153    <uses-permission android:name="android.permission.RECEIVE_WAP_PUSH" />
153-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:147:5-75
153-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:147:22-72
154    <uses-permission android:name="android.permission.SEND_SMS" />
154-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:148:5-67
154-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:148:22-64
155
156    <!-- ==================== 桌面快捷方式/壁纸相关权限 ==================== -->
157    <uses-permission android:name="android.permission.UNINSTALL_SHORTCUT" />
157-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:151:5-77
157-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:151:22-74
158    <uses-permission android:name="android.permission.INSTALL_SHORTCUT" />
158-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:152:5-75
158-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:152:22-72
159    <uses-permission android:name="android.permission.SET_WALLPAPER" />
159-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:153:5-72
159-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:153:22-69
160    <uses-permission android:name="android.permission.SET_WALLPAPER_HINTS" />
160-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:154:5-78
160-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:154:22-75
161
162    <!-- ==================== 自定义权限定义 ==================== -->
163    <uses-permission android:name="android.permission.DSPREAD" />
163-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:157:5-66
163-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:157:22-63
164    <uses-permission android:name="com.dspread.mdm.service.permissions.MY_BROADCAST" />
164-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:158:5-88
164-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:158:22-85
165
166    <!-- Android 10+ 广播权限 -->
167    <uses-permission android:name="android.permission.BROADCAST_CLOSE_SYSTEM_DIALOGS" />
167-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:161:5-89
167-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:161:22-86
168    <uses-permission android:name="android.permission.BROADCAST_PACKAGE_ADDED" />
168-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:162:5-82
168-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:162:22-79
169    <uses-permission android:name="android.permission.BROADCAST_PACKAGE_CHANGED" />
169-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:163:5-84
169-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:163:22-81
170    <uses-permission android:name="android.permission.BROADCAST_PACKAGE_INSTALL" />
170-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:164:5-84
170-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:164:22-81
171    <uses-permission android:name="android.permission.BROADCAST_PACKAGE_REPLACED" />
171-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:165:5-85
171-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:165:22-82
172    <uses-permission android:name="android.permission.BROADCAST_PACKAGE_REMOVED" />
172-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:166:5-84
172-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:166:22-81
173    <uses-permission android:name="android.permission.BROADCAST_PACKAGE_RESTARTED" />
173-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:167:5-86
173-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:167:22-83
174    <uses-permission android:name="android.permission.BROADCAST_PACKAGE_FIRST_LAUNCH" />
174-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:168:5-89
174-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:168:22-86
175    <uses-permission android:name="android.permission.BROADCAST_PACKAGE_NEEDS_VERIFICATION" />
175-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:169:5-95
175-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:169:22-92
176    <uses-permission android:name="android.permission.BROADCAST_PACKAGE_VERIFIED" />
176-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:170:5-85
176-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:170:22-82
177
178    <permission
178-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:172:5-174:47
179        android:name="com.dspread.mdm.service.permissions.MY_BROADCAST"
179-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:173:9-72
180        android:protectionLevel="signature" />
180-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:174:9-44
181    <permission
181-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\77d65a215b30b35b66736631fc83d763\transformed\core-1.13.0\AndroidManifest.xml:22:5-24:47
182        android:name="com.dspread.mdm.service.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
182-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\77d65a215b30b35b66736631fc83d763\transformed\core-1.13.0\AndroidManifest.xml:23:9-81
183        android:protectionLevel="signature" />
183-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\77d65a215b30b35b66736631fc83d763\transformed\core-1.13.0\AndroidManifest.xml:24:9-44
184
185    <uses-permission android:name="com.dspread.mdm.service.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" /> <!-- ==================== 应用程序配置 ==================== -->
185-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\77d65a215b30b35b66736631fc83d763\transformed\core-1.13.0\AndroidManifest.xml:26:5-97
185-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\77d65a215b30b35b66736631fc83d763\transformed\core-1.13.0\AndroidManifest.xml:26:22-94
186    <application
186-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:177:5-312:19
187        android:name="com.dspread.mdm.service.SmartMdmServiceApp"
187-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:178:9-43
188        android:allowBackup="true"
188-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:179:9-35
189        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
189-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\77d65a215b30b35b66736631fc83d763\transformed\core-1.13.0\AndroidManifest.xml:28:18-86
190        android:dataExtractionRules="@xml/data_extraction_rules"
190-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:180:9-65
191        android:debuggable="true"
192        android:extractNativeLibs="true"
193        android:fullBackupContent="@xml/backup_rules"
193-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:181:9-54
194        android:icon="@mipmap/ic_launcher"
194-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:182:9-43
195        android:label="@string/app_name"
195-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:183:9-41
196        android:networkSecurityConfig="@xml/network_security_config"
196-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:189:9-69
197        android:requestLegacyExternalStorage="true"
197-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:187:9-52
198        android:roundIcon="@mipmap/ic_launcher_round"
198-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:184:9-54
199        android:supportsRtl="true"
199-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:185:9-35
200        android:theme="@style/Theme.MyTheme"
200-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:186:9-45
201        android:usesCleartextTraffic="true" >
201-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:188:9-44
202
203        <!-- ==================== Activity组件 ==================== -->
204
205
206        <!-- 测试Activity -->
207        <activity
207-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:195:9-203:20
208            android:name="com.dspread.mdm.service.ui.activity.TestActivity"
208-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:196:13-53
209            android:exported="true"
209-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:197:13-36
210            android:label="System Info" >
210-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:198:13-40
211            <intent-filter>
211-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:199:13-202:29
212                <action android:name="android.intent.action.MAIN" />
212-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:200:17-69
212-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:200:25-66
213
214                <category android:name="android.intent.category.LAUNCHER" />
214-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:201:17-77
214-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:201:27-74
215            </intent-filter>
216        </activity>
217
218        <!-- OS升级测试Activity -->
219        <activity
219-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:206:9-210:52
220            android:name="com.dspread.mdm.service.ui.activity.OsUpdateTestActivity"
220-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:207:13-61
221            android:exported="false"
221-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:208:13-37
222            android:label="OS升级测试"
222-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:209:13-35
223            android:theme="@style/Theme.MyTheme" />
223-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:210:13-49
224
225        <!-- MediaProjection权限请求Activity -->
226        <activity
226-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:213:9-218:49
227            android:name="com.dspread.mdm.service.modules.remoteview.RequestMediaProjectionActivity"
227-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:214:13-78
228            android:excludeFromRecents="true"
228-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:218:13-46
229            android:exported="false"
229-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:215:13-37
230            android:launchMode="singleTop"
230-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:217:13-43
231            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
231-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:216:13-72
232
233        <!-- 锁屏Activity -->
234        <activity
234-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:221:9-234:20
235            android:name="com.dspread.mdm.service.ui.activity.LockScreenActivity"
235-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:222:13-59
236            android:enabled="true"
236-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:224:13-35
237            android:excludeFromRecents="true"
237-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:227:13-46
238            android:exported="true"
238-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:225:13-36
239            android:launchMode="singleTask"
239-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:228:13-44
240            android:screenOrientation="portrait"
240-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:226:13-49
241            android:theme="@style/no_statusbar_activity" >
241-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:223:13-57
242            <intent-filter android:priority="-100" >
242-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:229:13-233:29
242-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:229:28-51
243                <action android:name="android.intent.action.MAIN" />
243-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:200:17-69
243-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:200:25-66
244
245                <category android:name="android.intent.category.HOME" />
245-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:231:17-73
245-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:231:27-70
246                <category android:name="android.intent.category.DEFAULT" />
246-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:232:17-76
246-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:232:27-73
247            </intent-filter>
248        </activity>
249
250        <!-- ==================== Service组件 ==================== -->
251
252
253        <!-- 主后台服务 -->
254        <service
254-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:240:9-249:19
255            android:name="com.dspread.mdm.service.services.SmartMdmBackgroundService"
255-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:241:13-63
256            android:enabled="true"
256-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:242:13-35
257            android:exported="true"
257-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:243:13-36
258            android:foregroundServiceType="dataSync" >
258-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:244:13-53
259            <intent-filter>
259-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:245:13-248:29
260                <action android:name="com.dspread.mdm.service.START_SERVICE" />
260-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:246:17-80
260-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:246:25-77
261
262                <category android:name="android.intent.category.DEFAULT" />
262-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:232:17-76
262-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:232:27-73
263            </intent-filter>
264        </service>
265
266        <!-- Provisioning服务 - 异步处理配置任务 -->
267        <service
267-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:252:9-255:40
268            android:name="com.dspread.mdm.service.services.ProvisioningService"
268-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:253:13-57
269            android:enabled="true"
269-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:254:13-35
270            android:exported="false" />
270-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:255:13-37
271
272        <!-- MediaProjection前台服务 -->
273        <service
273-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:258:9-262:63
274            android:name="com.dspread.mdm.service.services.MediaProjectionService"
274-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:259:13-60
275            android:enabled="true"
275-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:260:13-35
276            android:exported="false"
276-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:261:13-37
277            android:foregroundServiceType="mediaProjection" />
277-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:262:13-60
278
279        <!-- 服务保活服务 -->
280        <service
280-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:265:9-268:40
281            android:name="com.dspread.mdm.service.services.ServiceKeepAliveService"
281-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:266:13-61
282            android:enabled="true"
282-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:267:13-35
283            android:exported="false" />
283-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:268:13-37
284
285        <!-- 应用安装服务 -->
286        <service
286-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:271:9-274:40
287            android:name="com.dspread.mdm.service.services.AppInstallService"
287-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:272:13-55
288            android:enabled="true"
288-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:273:13-35
289            android:exported="false" />
289-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:274:13-37
290
291        <!-- 应用卸载服务 -->
292        <service
292-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:277:9-280:40
293            android:name="com.dspread.mdm.service.services.AppUninstallService"
293-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:278:13-57
294            android:enabled="true"
294-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:279:13-35
295            android:exported="false" />
295-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:280:13-37
296
297        <!-- 用户交互监控辅助功能服务 -->
298        <service
298-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:283:9-293:19
299            android:name="com.dspread.mdm.service.platform.monitor.UserInteractionAccessibilityService"
299-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:284:13-81
300            android:exported="false"
300-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:286:13-37
301            android:permission="android.permission.BIND_ACCESSIBILITY_SERVICE" >
301-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:285:13-79
302            <intent-filter>
302-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:287:13-289:29
303                <action android:name="android.accessibilityservice.AccessibilityService" />
303-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:288:17-92
303-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:288:25-89
304            </intent-filter>
305
306            <meta-data
306-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:290:13-292:72
307                android:name="android.accessibilityservice"
307-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:291:17-60
308                android:resource="@xml/accessibility_service_config" />
308-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:292:17-69
309        </service>
310
311        <!-- ==================== BroadcastReceiver组件 ==================== -->
312
313
314        <!-- Geofence操作广播接收器 -->
315        <receiver
315-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:298:9-310:20
316            android:name="com.dspread.mdm.service.broadcast.receivers.GeofenceReceiver"
316-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:299:13-65
317            android:enabled="true"
317-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:300:13-35
318            android:exported="false" >
318-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:301:13-37
319            <intent-filter>
319-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:302:13-309:29
320                <action android:name="android.intent.action.SCREEN_ON" />
320-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:303:17-74
320-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:303:25-71
321                <action android:name="android.intent.action.SCREEN_OFF" />
321-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:304:17-75
321-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:304:25-72
322                <action android:name="android.intent.action.USER_PRESENT" />
322-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:305:17-77
322-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:305:25-74
323                <action android:name="android.intent.action.BOOT_COMPLETED" />
323-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:306:17-79
323-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:306:25-76
324                <action android:name="android.net.wifi.RSSI_CHANGED" />
324-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:307:17-72
324-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:307:25-69
325                <action android:name="android.net.wifi.NETWORK_STATE_CHANGED" />
325-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:308:17-81
325-->E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:308:25-78
326            </intent-filter>
327        </receiver>
328
329        <provider
329-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\cbaa961bfd044a072a7d4e5814fece02\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
330            android:name="androidx.startup.InitializationProvider"
330-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\cbaa961bfd044a072a7d4e5814fece02\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
331            android:authorities="com.dspread.mdm.service.androidx-startup"
331-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\cbaa961bfd044a072a7d4e5814fece02\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
332            android:exported="false" >
332-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\cbaa961bfd044a072a7d4e5814fece02\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
333            <meta-data
333-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\cbaa961bfd044a072a7d4e5814fece02\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
334                android:name="androidx.emoji2.text.EmojiCompatInitializer"
334-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\cbaa961bfd044a072a7d4e5814fece02\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
335                android:value="androidx.startup" />
335-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\cbaa961bfd044a072a7d4e5814fece02\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
336            <meta-data
336-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\2324ba83f0e58d631ef534998e5b824f\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
337                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
337-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\2324ba83f0e58d631ef534998e5b824f\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
338                android:value="androidx.startup" />
338-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\2324ba83f0e58d631ef534998e5b824f\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
339            <meta-data
339-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\3a86084b347b0870efb55c81983d610b\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
340                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
340-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\3a86084b347b0870efb55c81983d610b\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
341                android:value="androidx.startup" />
341-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\3a86084b347b0870efb55c81983d610b\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
342        </provider>
343
344        <receiver
344-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\3a86084b347b0870efb55c81983d610b\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
345            android:name="androidx.profileinstaller.ProfileInstallReceiver"
345-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\3a86084b347b0870efb55c81983d610b\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
346            android:directBootAware="false"
346-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\3a86084b347b0870efb55c81983d610b\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
347            android:enabled="true"
347-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\3a86084b347b0870efb55c81983d610b\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
348            android:exported="true"
348-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\3a86084b347b0870efb55c81983d610b\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
349            android:permission="android.permission.DUMP" >
349-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\3a86084b347b0870efb55c81983d610b\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
350            <intent-filter>
350-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\3a86084b347b0870efb55c81983d610b\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
351                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
351-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\3a86084b347b0870efb55c81983d610b\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
351-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\3a86084b347b0870efb55c81983d610b\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
352            </intent-filter>
353            <intent-filter>
353-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\3a86084b347b0870efb55c81983d610b\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
354                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
354-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\3a86084b347b0870efb55c81983d610b\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
354-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\3a86084b347b0870efb55c81983d610b\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
355            </intent-filter>
356            <intent-filter>
356-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\3a86084b347b0870efb55c81983d610b\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
357                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
357-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\3a86084b347b0870efb55c81983d610b\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
357-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\3a86084b347b0870efb55c81983d610b\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
358            </intent-filter>
359            <intent-filter>
359-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\3a86084b347b0870efb55c81983d610b\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
360                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
360-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\3a86084b347b0870efb55c81983d610b\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
360-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\3a86084b347b0870efb55c81983d610b\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
361            </intent-filter>
362        </receiver>
363    </application>
364
365</manifest>
