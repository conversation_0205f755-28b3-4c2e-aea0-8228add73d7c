package com.dspread.mdm.service.config

import android.annotation.SuppressLint
import android.content.Context
import android.content.SharedPreferences
import com.dspread.mdm.service.utils.log.Logger

/**
 * 统一配置管理器
 * 作为所有配置的统一入口，管理debug模式和各个子配置
 */
@SuppressLint("StaticFieldLeak")
object DebugConfig {

    private const val TAG = "[DebugConfig]"
    private const val PREF_NAME = "debug_config"
    private const val KEY_DEBUG_MODE = "debug_mode_enabled"

    private var sharedPreferences: SharedPreferences? = null
    private var context: Context? = null
    
    /**
     * 初始化统一配置管理器
     */
    fun init(appContext: Context) {
        context = appContext.applicationContext
        sharedPreferences = context?.getSharedPreferences(PREF_NAME, Context.MODE_PRIVATE)
        Logger.com("$TAG 统一配置管理器初始化完成")
    }
    
    /**
     * 设置调试模式
     */
    fun setDebugMode(debugMode: Boolean) {
        sharedPreferences?.edit()?.putBoolean(KEY_DEBUG_MODE, debugMode)?.apply()

        // 同步更新所有子配置
        context?.let { ctx ->
            LogConfig.setDebugMode(debugMode)
            ProvisionConfig.setDebugMode(debugMode)
            TimerConfig.setDebugMode(debugMode)
            LogStreamConfig.setDebugMode(debugMode)
        }

        Logger.com("$TAG 调试模式${if (debugMode) "已启用" else "已禁用"}")
    }

    /**
     * 获取当前调试模式状态
     */
    fun isDebugMode(): Boolean {
        return sharedPreferences?.getBoolean(KEY_DEBUG_MODE, false) ?: false
    }

}
