package com.dspread.mdm.service.services

import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.content.ServiceConnection
import android.os.Build
import android.os.IBinder
import com.dspread.mdm.service.utils.log.Logger
import com.dspread.sdkdevservice.aidl.deviceService.SDKDeviceService
import com.dspread.sdkdevservice.aidl.deviceInfo.SDKDeviceInfo
import com.dspread.sdksysservice.aidl.systemService.SDKSystemService
import com.dspread.sdksysservice.aidl.systemManager.SDKSystemManager
import java.util.concurrent.CountDownLatch
import java.util.concurrent.TimeUnit

/**
 * DSPREAD服务管理类
 * 负责管理SDKSysService和SDKDevService的绑定和生命周期
 * 仅在Android 14及以上版本使用
 */
object DspreadService {
    private const val TAG = "DspreadService"
    
    // 服务常量
    private const val SYS_SERVICE_ACTION = "com.dspread.SDKSysService"
    private const val DEV_SERVICE_ACTION = "com.dspread.SDKDevService"
    private const val SERVICE_PACKAGE = "com.dspread.sdkservice"
    
    // 服务实例
    private var sysService: SDKSystemService? = null
    private var deviceService: SDKDeviceService? = null
    
    // 绑定状态
    private var isSysServiceBound = false
    private var isDevServiceBound = false

    // 缓存数据
    private var cachedSpVersion: String? = null
    private var cachedSerialNumber: String? = null
    private var lastCacheTime = 0L
    private var isFirstBoot = true // 首次启动标识
    private const val CACHE_DURATION = 30 * 1000L // 30秒缓存
    
    // 服务连接器
    private var sysServiceConnection: ServiceConnection? = null
    private var devServiceConnection: ServiceConnection? = null
    
    /**
     * 初始化DSPREAD服务
     * 仅在Android 14及以上版本执行
     */
    fun initialize(context: Context) {
        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.UPSIDE_DOWN_CAKE) {
            Logger.platform("$TAG Android版本 < 14，跳过DSPREAD服务初始化")
            return
        }
        
        Logger.platform("$TAG 开始初始化DSPREAD服务")
        
        // 绑定系统服务
        bindSysService(context)

        // 绑定设备服务
        bindDevService(context)
    }
    
    /**
     * 绑定系统服务 (SDKSysService)
     */
    private fun bindSysService(context: Context) {
        if (isSysServiceBound) {
            Logger.platform("$TAG 系统服务已绑定")
            return
        }
        
        val latch = CountDownLatch(1)
        
        sysServiceConnection = object : ServiceConnection {
            override fun onServiceConnected(name: ComponentName?, service: IBinder?) {
                Logger.platform("$TAG 系统服务连接成功: $name")
                try {
                    // 直接使用AIDL接口
                    sysService = SDKSystemService.Stub.asInterface(service)

                    isSysServiceBound = true
                    Logger.platform("$TAG 系统服务绑定完成")
                } catch (e: Exception) {
                    Logger.platformE("$TAG 系统服务绑定失败", e)
                    sysService = null
                    isSysServiceBound = false
                } finally {
                    latch.countDown()
                }
            }
            
            override fun onServiceDisconnected(name: ComponentName?) {
                Logger.platform("$TAG 系统服务连接断开: $name")
                sysService = null
                isSysServiceBound = false
            }
        }
        
        try {
            val intent = Intent().apply {
                action = SYS_SERVICE_ACTION
                setPackage(SERVICE_PACKAGE)
            }
            
            val bound = context.bindService(intent, sysServiceConnection!!, Context.BIND_AUTO_CREATE)
            if (bound) {
                Logger.platform("$TAG 系统服务绑定请求已发送")
                // 等待连接完成，最多3秒
                latch.await(3, TimeUnit.SECONDS)
            } else {
                Logger.platformE("$TAG 系统服务绑定请求失败")
            }
        } catch (e: Exception) {
            Logger.platformE("$TAG 系统服务绑定异常", e)
        }
    }
    
    /**
     * 绑定设备服务 (SDKDeviceService)
     */
    private fun bindDevService(context: Context) {
        if (isDevServiceBound) {
            Logger.platform("$TAG 设备服务已绑定")
            return
        }
        
        val latch = CountDownLatch(1)
        
        devServiceConnection = object : ServiceConnection {
            override fun onServiceConnected(name: ComponentName?, service: IBinder?) {
                Logger.platform("$TAG 设备服务连接成功: $name")
                try {
                    // 直接使用AIDL接口
                    deviceService = SDKDeviceService.Stub.asInterface(service)

                    isDevServiceBound = true
                    Logger.platform("$TAG 设备服务绑定完成")
                } catch (e: Exception) {
                    Logger.platformE("$TAG 设备服务绑定失败", e)
                    deviceService = null
                    isDevServiceBound = false
                } finally {
                    latch.countDown()
                }
            }
            
            override fun onServiceDisconnected(name: ComponentName?) {
                Logger.platform("$TAG 设备服务连接断开: $name")
                deviceService = null
                isDevServiceBound = false
            }
        }
        
        try {
            val intent = Intent().apply {
                action = DEV_SERVICE_ACTION
                setPackage(SERVICE_PACKAGE)
            }
            
            val bound = context.bindService(intent, devServiceConnection!!, Context.BIND_AUTO_CREATE)
            if (bound) {
                Logger.platform("$TAG 设备服务绑定请求已发送")
                // 等待连接完成，最多3秒
                latch.await(3, TimeUnit.SECONDS)
            } else {
                Logger.platformE("$TAG 设备服务绑定请求失败")
            }
        } catch (e: Exception) {
            Logger.platformE("$TAG 设备服务绑定异常", e)
        }
    }
    
    /**
     * 获取系统服务实例
     */
    fun getSysService(): SDKSystemService? {
        return if (isSysServiceBound) sysService else null
    }

    /**
     * 获取设备服务实例
     */
    fun getDeviceService(): SDKDeviceService? {
        return if (isDevServiceBound) deviceService else null
    }
    
    /**
     * 检查系统服务是否可用
     */
    fun isSysServiceAvailable(): Boolean {
        return isSysServiceBound && sysService != null
    }
    
    /**
     * 检查设备服务是否可用
     */
    fun isDeviceServiceAvailable(): Boolean {
        return isDevServiceBound && deviceService != null
    }

    /**
     * 解析SP版本信息，只提取版本号
     * 输入示例: *SP_VERSION:V1.0.5#*COMMIT:2d7cc90#*DATETIME:20250702#...
     * 输出: V1.0.5
     */
    private fun parseSpVersion(rawSpInfo: String?): String {
        if (rawSpInfo.isNullOrEmpty()) {
            return ""
        }

        try {
            // 查找 SP_VERSION: 标记
            val spVersionPrefix = "*SP_VERSION:"
            val startIndex = rawSpInfo.indexOf(spVersionPrefix)
            if (startIndex == -1) {
                Logger.platformW("$TAG SP版本信息格式异常，未找到SP_VERSION标记")
                return rawSpInfo.take(20) // 返回前20个字符作为备用
            }

            // 提取版本号部分
            val versionStart = startIndex + spVersionPrefix.length
            val endIndex = rawSpInfo.indexOf("#", versionStart)
            if (endIndex == -1) {
                Logger.platformW("$TAG SP版本信息格式异常，未找到结束标记")
                return rawSpInfo.substring(versionStart).take(20)
            }

            val version = rawSpInfo.substring(versionStart, endIndex)
            Logger.platform("$TAG 解析SP版本: $version")
            return version

        } catch (e: Exception) {
            Logger.platformE("$TAG 解析SP版本失败", e)
            return rawSpInfo.take(20) // 返回前20个字符作为备用
        }
    }
    
    /**
     * 释放服务连接
     */
    fun release(context: Context) {
        Logger.platform("$TAG 释放DSPREAD服务连接")
        
        try {
            sysServiceConnection?.let { 
                context.unbindService(it)
                sysServiceConnection = null
            }
            devServiceConnection?.let { 
                context.unbindService(it)
                devServiceConnection = null
            }
        } catch (e: Exception) {
            Logger.platformE("$TAG 释放服务连接失败", e)
        }
        
        sysService = null
        deviceService = null
        isSysServiceBound = false
        isDevServiceBound = false
    }
    
    /**
     * 重新连接服务
     */
    fun reconnect(context: Context) {
        Logger.platform("$TAG 重新连接DSPREAD服务")
        release(context)
        initialize(context)
    }

    /**
     * 获取SP版本信息（带缓存）
     */
    fun getSpVersion(): String {
        return try {
            // 检查缓存（首次启动时跳过缓存）
            val currentTime = System.currentTimeMillis()
            if (!isFirstBoot && cachedSpVersion != null && (currentTime - lastCacheTime) < CACHE_DURATION) {
                Logger.platform("$TAG 使用缓存的SP版本: $cachedSpVersion")
                return cachedSpVersion!!
            }

            if (!isSysServiceAvailable()) {
                Logger.platformE("$TAG 系统服务不可用，无法获取SP版本")
                return cachedSpVersion ?: ""
            }

            // 通过systemManager获取SP版本
            val systemManagerBinder = sysService!!.systemManager
            if (systemManagerBinder != null) {
                val systemManagerInterface = SDKSystemManager.Stub.asInterface(systemManagerBinder)
                val rawResult = systemManagerInterface.spInfo

                // 解析SP版本信息，只提取版本号
                val parsedVersion = parseSpVersion(rawResult)

                // 更新缓存
                cachedSpVersion = parsedVersion
                lastCacheTime = currentTime

                if (isFirstBoot) {
                    isFirstBoot = false
                    Logger.platform("$TAG 首次获取SP版本成功: $parsedVersion (原始: ${rawResult?.take(50)}...)")
                } else {
                    Logger.platform("$TAG 获取SP版本成功并缓存: $parsedVersion")
                }
                cachedSpVersion!!
            } else {
                Logger.platformE("$TAG systemManager为null")
                cachedSpVersion ?: ""
            }

        } catch (e: Exception) {
            Logger.platformE("$TAG 获取SP版本失败", e)
            cachedSpVersion ?: ""
        }
    }

    /**
     * 获取设备序列号（带缓存，首次启动强制获取）
     */
    fun getDeviceSerialNumber(): String {
        return try {
            // 检查缓存（首次启动时跳过缓存）
            val currentTime = System.currentTimeMillis()
            if (!isFirstBoot && cachedSerialNumber != null && (currentTime - lastCacheTime) < CACHE_DURATION) {
//                Logger.platform("$TAG 使用缓存的设备序列号: $cachedSerialNumber")
                return cachedSerialNumber!!
            }

            if (!isDeviceServiceAvailable()) {
                Logger.platformE("$TAG 设备服务不可用，无法获取序列号")
                return cachedSerialNumber ?: ""
            }

            // 获取设备序列号
            val deviceInfoBinder = deviceService!!.deviceInfo
            if (deviceInfoBinder != null) {
                val deviceInfoInterface = SDKDeviceInfo.Stub.asInterface(deviceInfoBinder)
                val result = deviceInfoInterface.sn

                // 更新缓存
                cachedSerialNumber = result ?: ""
                lastCacheTime = currentTime

//                Logger.platform("$TAG 获取SN成功并缓存: $result")
                cachedSerialNumber!!
            } else {
                Logger.platformE("$TAG deviceInfo为null")
                cachedSerialNumber ?: ""
            }

        } catch (e: Exception) {
            Logger.platformE("$TAG 获取设备序列号失败", e)
            cachedSerialNumber ?: ""
        }
    }
}
