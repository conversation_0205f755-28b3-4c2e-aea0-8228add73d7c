package com.dspread.mdm.service.modules.rulebase.model

import com.dspread.mdm.service.utils.log.Logger
import org.json.JSONArray
import org.json.JSONObject
import java.text.SimpleDateFormat
import java.util.*

/**
 * 规则数据模型
 */
data class Rule(
    // 基础字段
    val ruleId: String,                    // 规则ID (必填)
    val action: String,                    // 操作类型: Add/Modify/Delete
    val ruleType: String,                  // 规则类型
    val ruleStatus: RuleStatus,            // 规则状态
    
    // 时间字段
    val beginDate: String,                 // 开始时间 "yyyy-MM-dd HH:mm:ss"
    val endDate: String,                   // 结束时间 "yyyy-MM-dd HH:mm:ss"
    val modifyTime: Long,                  // 修改时间戳 (用于优先级排序)
    
    // 应用列表
    val appList: List<RuleApp>,            // 要安装的应用列表
    val deleteAppList: List<RuleApp>,      // 要卸载的应用列表
    
    // 执行条件
    val networkRequired: Boolean = true,    // 是否需要网络
    val wifiOnly: Boolean = false,         // 是否仅WiFi环境
    val deviceIdleRequired: Boolean = true, // 是否需要设备空闲
    
    // 元数据
    val createTime: Long = System.currentTimeMillis(),
    val lastExecuteTime: Long = 0L,
    val executeCount: Int = 0,
    val maxRetryCount: Int = 3
) {
    
    companion object {
        private const val TAG = "Rule"
        private val dateFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault())
        
        /**
         * 从JSON创建Rule对象
         */
        fun fromJson(json: JSONObject): Rule? {
            return try {
                val appListArray = json.optJSONArray("appList")
                val deleteAppListArray = json.optJSONArray("deleteAppList")

                Logger.rule("$TAG 解析规则JSON: ruleId=${json.optString("ruleId")}")
                Logger.rule("$TAG appList数组: $appListArray")
                Logger.rule("$TAG deleteAppList数组: $deleteAppListArray")

                val appList = RuleApp.parseAppList(appListArray)
                val deleteAppList = RuleApp.parseAppList(deleteAppListArray)

                Logger.rule("$TAG 解析后appList数量: ${appList.size}")
                Logger.rule("$TAG 解析后deleteAppList数量: ${deleteAppList.size}")

                Rule(
                    ruleId = json.getString("ruleId"),
                    action = json.getString("action"),
                    ruleType = json.optString("ruleType", "app_management"),
                    ruleStatus = RuleStatus.fromValue(json.optString("ruleStatus", "READY")),
                    beginDate = json.getString("beginDate"),
                    endDate = json.getString("endDate"),
                    modifyTime = json.optLong("modifyTime", System.currentTimeMillis()),
                    appList = appList,
                    deleteAppList = deleteAppList,
                    networkRequired = json.optBoolean("networkRequired", true),
                    wifiOnly = json.optBoolean("wifiOnly", false),
                    deviceIdleRequired = json.optBoolean("deviceIdleRequired", true),
                    createTime = json.optLong("createTime", System.currentTimeMillis()),
                    lastExecuteTime = json.optLong("lastExecuteTime", 0L),
                    executeCount = json.optInt("executeCount", 0),
                    maxRetryCount = json.optInt("maxRetryCount", 3)
                )
            } catch (e: Exception) {
                Logger.ruleE("$TAG 解析Rule JSON失败", e)
                null
            }
        }
        
        /**
         * 解析日期时间字符串为时间戳
         */
        fun parseDateTime(dateTimeStr: String): Long {
            return try {
                // 先尝试解析为时间戳
                if (dateTimeStr.matches(Regex("\\d+"))) {
                    dateTimeStr.toLong()
                } else {
                    // 解析为日期格式
                    dateFormat.parse(dateTimeStr)?.time ?: System.currentTimeMillis()
                }
            } catch (e: Exception) {
                Logger.ruleE("$TAG 解析日期时间失败: $dateTimeStr", e)
                System.currentTimeMillis()
            }
        }
        
        /**
         * 验证日期格式是否有效
         */
        fun isValidDateFormat(dateStr: String): Boolean {
            return try {
                if (dateStr.matches(Regex("\\d+"))) {
                    // 时间戳格式
                    dateStr.toLong()
                    true
                } else {
                    // 日期字符串格式
                    dateFormat.parse(dateStr)
                    true
                }
            } catch (e: Exception) {
                false
            }
        }
    }
    
    /**
     * 验证规则数据完整性
     */
    fun validate(): ValidationResult {
        Logger.rule("$TAG 验证规则: ruleId=$ruleId, ruleType=$ruleType, action=$action")
        Logger.rule("$TAG appList数量: ${appList.size}, deleteAppList数量: ${deleteAppList.size}")

        return when {
            // 基础字段验证
            ruleId.isBlank() -> ValidationResult.error("ruleId不能为空")
            action !in listOf("Add", "Modify", "Delete", "A", "M", "D") -> ValidationResult.error("action值无效: $action")
            beginDate.isBlank() -> ValidationResult.error("beginDate不能为空")
            endDate.isBlank() -> ValidationResult.error("endDate不能为空")
            !isValidDateFormat(beginDate) -> ValidationResult.error("beginDate格式无效: $beginDate")
            !isValidDateFormat(endDate) -> ValidationResult.error("endDate格式无效: $endDate")
            parseDateTime(beginDate) >= parseDateTime(endDate) -> ValidationResult.error("开始时间不能晚于结束时间")
            
            // 应用管理规则验证
            ruleType == "app_management" -> validateAppManagementRule()
            
            // Service规则验证
            ruleType == "service_management" -> validateServiceRule()
            
            // 其他规则类型
            else -> ValidationResult.error("不支持的规则类型: $ruleType")
        }
    }
    
    /**
     * 验证应用管理规则
     */
    private fun validateAppManagementRule(): ValidationResult {
        Logger.rule("$TAG 验证应用管理规则: action=$action")
        
        // 自动调整action：如果appList为空且deleteAppList不为空，则视为卸载操作
        val adjustedAction = if (appList.isEmpty() && deleteAppList.isNotEmpty()) {
            Logger.rule("$TAG 检测到纯卸载规则，自动调整action: $action -> D")
            "D"
        } else {
            action
        }
        
        return when (adjustedAction.uppercase()) {
            "A", "ADD" -> {
                if (appList.isEmpty() && deleteAppList.isEmpty()) {
                    Logger.rule("$TAG 验证失败: 添加操作但appList和deleteAppList都为空")
                    ValidationResult.error("添加操作必须包含至少一个应用列表")
                } else {
                    validateAppLists()
                }
            }
            "M", "MODIFY" -> {
                if (appList.isEmpty() && deleteAppList.isEmpty()) {
                    Logger.rule("$TAG 验证失败: 修改操作但appList和deleteAppList都为空")
                    ValidationResult.error("修改操作必须包含至少一个应用列表")
                } else {
                    validateAppLists()
                }
            }
            "D", "DELETE" -> {
                Logger.rule("$TAG 删除操作验证通过: 允许空应用列表")
                ValidationResult.success()
            }
            else -> {
                Logger.rule("$TAG 验证失败: 无效的action值: $action")
                ValidationResult.error("无效的操作类型: $action")
            }
        }
    }
    
    /**
     * 验证Service规则
     */
    private fun validateServiceRule(): ValidationResult {
        Logger.rule("$TAG 验证Service规则: action=$action")
        
        // Service规则暂时不需要特殊验证，允许空应用列表
        return ValidationResult.success()
    }
    
    /**
     * 验证应用列表
     */
    private fun validateAppLists(): ValidationResult {
        Logger.rule("$TAG 验证应用列表: appList=${appList.size}, deleteAppList=${deleteAppList.size}")
        
        // 验证安装应用列表中的每个应用（需要下载字段）
        appList.forEach { app ->
            val appValidation = app.validate(requireDownload = true)
            if (!appValidation.isSuccess) {
                Logger.rule("$TAG 安装应用验证失败: ${app.packName} - ${appValidation.message}")
                return ValidationResult.error("安装应用验证失败: ${app.packName} - ${appValidation.message}")
            }
        }

        // 验证卸载应用列表中的每个应用（不需要下载字段）
        deleteAppList.forEach { app ->
            val appValidation = app.validate(requireDownload = false)
            if (!appValidation.isSuccess) {
                Logger.rule("$TAG 卸载应用验证失败: ${app.packName} - ${appValidation.message}")
                return ValidationResult.error("卸载应用验证失败: ${app.packName} - ${appValidation.message}")
            }
        }

        Logger.rule("$TAG 应用列表验证通过")
        return ValidationResult.success()
    }
    
    /**
     * 检查规则是否在有效时间范围内
     */
    fun isInTimeRange(currentTime: Long = System.currentTimeMillis()): Boolean {
        val beginTime = parseDateTime(beginDate)
        val endTime = parseDateTime(endDate)
        return currentTime in beginTime..endTime
    }
    
    /**
     * 检查规则是否可以执行
     */
    fun canExecute(currentTime: Long = System.currentTimeMillis()): Boolean {
        return ruleStatus.isExecutableState() && 
               isInTimeRange(currentTime) && 
               executeCount < maxRetryCount
    }
    
    /**
     * 获取规则优先级（基于修改时间，越新优先级越高）
     */
    fun getPriority(): Long = modifyTime
    
    /**
     * 创建规则的副本并更新状态
     */
    fun withStatus(newStatus: RuleStatus): Rule {
        return copy(
            ruleStatus = newStatus,
            lastExecuteTime = if (newStatus == RuleStatus.EXECUTING) System.currentTimeMillis() else lastExecuteTime,
            executeCount = if (newStatus == RuleStatus.EXECUTING) executeCount + 1 else executeCount
        )
    }
    
    /**
     * 转换为JSON存储格式
     */
    fun toJson(): JSONObject {
        return JSONObject().apply {
            put("ruleId", ruleId)
            put("action", action)
            put("ruleType", ruleType)
            put("ruleStatus", ruleStatus.value)
            put("beginDate", beginDate)
            put("endDate", endDate)
            put("modifyTime", modifyTime)
            put("appList", JSONArray().apply {
                appList.forEach { app -> put(app.toJson()) }
            })
            put("deleteAppList", JSONArray().apply {
                deleteAppList.forEach { app -> put(app.toJson()) }
            })
            put("networkRequired", networkRequired)
            put("wifiOnly", wifiOnly)
            put("deviceIdleRequired", deviceIdleRequired)
            put("createTime", createTime)
            put("lastExecuteTime", lastExecuteTime)
            put("executeCount", executeCount)
            put("maxRetryCount", maxRetryCount)
        }
    }
    
    /**
     * 获取规则的简要描述
     */
    fun getDescription(): String {
        return "Rule[id=$ruleId, status=${ruleStatus.description}, apps=${appList.size}, " +
               "timeRange=$beginDate~$endDate]"
    }
    
    /**
     * 检查是否与另一个规则冲突
     */
    fun conflictsWith(other: Rule): Boolean {
        // 如果时间范围重叠且包含相同的应用，则认为冲突
        if (!isTimeRangeOverlap(other)) return false
        
        return appList.any { thisApp ->
            other.appList.any { otherApp ->
                thisApp.packName == otherApp.packName
            }
        }
    }
    
    /**
     * 检查时间范围是否重叠
     */
    private fun isTimeRangeOverlap(other: Rule): Boolean {
        val thisBegin = parseDateTime(beginDate)
        val thisEnd = parseDateTime(endDate)
        val otherBegin = parseDateTime(other.beginDate)
        val otherEnd = parseDateTime(other.endDate)
        
        return thisBegin < otherEnd && otherBegin < thisEnd
    }
}
