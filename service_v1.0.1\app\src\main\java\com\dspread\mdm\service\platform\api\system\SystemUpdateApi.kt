﻿package com.dspread.mdm.service.platform.api.system

import android.content.Context
import android.os.Build
import com.dspread.mdm.service.utils.log.Logger
import com.dspread.mdm.service.platform.api.model.SystemOperationResult
import com.dspread.mdm.service.platform.api.model.UpdateInfo
import com.dspread.mdm.service.platform.api.upgrade.UpgradeStrategyFactory
import com.dspread.mdm.service.platform.api.upgrade.UpdateEngineUpgradeStrategy
import com.dspread.mdm.service.platform.manager.UpdateEngineManager
import java.io.File
import java.util.zip.ZipFile
import java.io.BufferedReader
import java.io.InputStreamReader

/**
 * 系统升级API
 * 提供Android系统OTA升级相关功能
 * 使用策略模式支持不同的升级方式
 *
 * 升级策略：
 * - UpdateEngine: Android 14+ 或支持A/B分区的设备
 * - RecoverySystem: 传统升级方式，适用于不支持A/B分区的设备
 *
 * 硬件平台支持：
 * - d20 a10 (MTK8766) - RecoverySystem.installPackage
 * - d60 a11 (MTK8766) - RecoverySystem.installPackage
 * - d70 a11 (MTK8766) - RecoverySystem.installPackage
 * - d30 a14 (MTK8766) - UpdateEngine.applyPayload
 */
class SystemUpdateApi(private val context: Context) {

    companion object {
        private const val TAG = "SystemUpdateApi"
        private const val UPDATE_TEMP_DIR = "/sdcard/Android/data/com.dspread.mdm.service/files/updates"
    }

    private val upgradeStrategyFactory = UpgradeStrategyFactory(context)

    init {
        Logger.platformI("$TAG SystemUpdateApi initialized for Android ${Build.VERSION.SDK_INT}")

        // 创建更新临时目录
        createUpdateTempDirectory()

        // 打印策略支持状态
        val strategiesStatus = upgradeStrategyFactory.getAllStrategiesStatus()
        Logger.platformI("$TAG 升级策略支持状态: $strategiesStatus")
    }
    
    /**
     * 更新状态监听器（兼容接口）
     */
    interface UpdateStatusListener {
        fun onStatusUpdate(status: String, progress: Int)
        fun onUpdateCompleted(success: Boolean, message: String)
    }

    /**
     * 安装OTA更新包（主要方法）
     * 使用策略模式自动选择最佳升级方式
     *
     * @param updateFilePath 更新包文件路径
     * @param taskId 任务ID，用于状态跟踪
     * @param listener 可选的更新状态监听器
     */
    fun installOtaUpdate(
        updateFilePath: String,
        taskId: String? = null,
        listener: UpdateStatusListener? = null
    ): SystemOperationResult {
        return try {
            Logger.platformI("$TAG 开始安装OTA更新: $updateFilePath")

            val updateFile = File(updateFilePath)
            if (!updateFile.exists()) {
                return SystemOperationResult.failure("Update file not found: $updateFilePath")
            }

            // 升级前系统检查
            val preCheckResult = performPreUpgradeChecks(updateFile)
            if (!preCheckResult.isSuccess) {
                return preCheckResult
            }

            // 转换监听器接口
            val strategyListener = listener?.let { originalListener ->
                object : UpdateEngineUpgradeStrategy.UpdateStatusListener {
                    override fun onStatusUpdate(status: String, progress: Int) {
                        originalListener.onStatusUpdate(status, progress)
                    }

                    override fun onUpdateCompleted(success: Boolean, message: String) {
                        originalListener.onUpdateCompleted(success, message)
                    }
                }
            }

            // 使用策略工厂执行升级
            val result = upgradeStrategyFactory.performUpgrade(updateFile, taskId, strategyListener)

            Logger.platformI("$TAG 升级执行结果: ${if (result.isSuccess) "成功" else "失败"}")
            result

        } catch (e: Exception) {
            Logger.platformE("$TAG 安装OTA更新失败", e)
            SystemOperationResult.failure("OTA installation failed: ${e.message}")
        }
    }

    /**
     * 安装OTA更新包（兼容方法，支持File参数）
     */
    fun installOTAUpdate(
        updateFile: File,
        taskId: String? = null,
        listener: UpdateStatusListener? = null
    ): SystemOperationResult {
        return installOtaUpdate(updateFile.absolutePath, taskId, listener)
    }


    /**
     * 获取升级策略信息
     */
    fun getUpgradeStrategyInfo(updateFilePath: String): SystemOperationResult {
        return try {
            val updateFile = File(updateFilePath)
            if (!updateFile.exists()) {
                return SystemOperationResult.failure("Update file not found: $updateFilePath")
            }

            val strategyInfo = upgradeStrategyFactory.getRecommendedStrategy(updateFile)
            val strategiesStatus = upgradeStrategyFactory.getAllStrategiesStatus()

            val info = mapOf(
                "recommended_strategy" to strategyInfo.name,
                "strategy_type" to strategyInfo.type.name,
                "strategy_supported" to strategyInfo.supported,
                "strategy_reason" to strategyInfo.reason,
                "strategy_description" to strategyInfo.description,
                "all_strategies_status" to strategiesStatus
            )

            Logger.platformI("$TAG 升级策略信息: $info")
            SystemOperationResult.success("Upgrade strategy info retrieved: $info")

        } catch (e: Exception) {
            Logger.platformE("$TAG 获取升级策略信息失败", e)
            SystemOperationResult.failure("Failed to get upgrade strategy info: ${e.message}")
        }
    }

    /**
     * 升级前系统检查
     */
    private fun performPreUpgradeChecks(updateFile: File): SystemOperationResult {
        return try {
            Logger.platformI("$TAG 执行升级前系统检查")

            // 1. Android 14+ 特殊处理：重置UpdateEngine状态
            if (Build.VERSION.SDK_INT >= 34) {
                Logger.platformI("$TAG Android 14+设备，检查并重置UpdateEngine状态")
                val resetResult = resetUpdateEngineIfNeeded()
                if (!resetResult.isSuccess) {
                    Logger.platformW("$TAG UpdateEngine状态重置失败，但继续尝试升级")
                }
            }

            // 2. 检查存储空间
            val requiredSpace = updateFile.length() * 2 // 需要双倍空间
            val availableSpace = getAvailableSpace()
            if (availableSpace < requiredSpace) {
                return SystemOperationResult.failure("存储空间不足: 需要${formatBytes(requiredSpace)}, 可用${formatBytes(availableSpace)}")
            }

            // 3. 检查电池电量
            val batteryLevel = getBatteryLevel()
            if (batteryLevel < 30) {
                return SystemOperationResult.failure("电池电量不足30%: 当前${batteryLevel}%")
            }

            Logger.platformI("$TAG 升级前检查通过")
            SystemOperationResult.success("Pre-upgrade checks passed")

        } catch (e: Exception) {
            Logger.platformE("$TAG 升级前检查失败", e)
            SystemOperationResult.failure("Pre-upgrade check failed: ${e.message}")
        }
    }

    /**
     * 重置UpdateEngine状态（Android 14+专用）
     * 解决"Previous update has not been completed"问题
     */
    private fun resetUpdateEngineIfNeeded(): SystemOperationResult {
        return try {
            Logger.platformI("$TAG 尝试重置UpdateEngine状态")

            // 使用UpdateEngineManager重置状态
            val updateEngineManager = UpdateEngineManager(context)
            val resetResult = updateEngineManager.resetUpdateEngineStatus()

            if (resetResult) {
                Logger.platformI("$TAG UpdateEngine状态重置成功")
                // 等待一下让状态生效
                Thread.sleep(1000)
                SystemOperationResult.success("UpdateEngine status reset successfully")
            } else {
                Logger.platformW("$TAG UpdateEngine状态重置失败，可能不需要重置")
                SystemOperationResult.success("UpdateEngine reset not needed or failed")
            }

        } catch (e: Exception) {
            Logger.platformE("$TAG 重置UpdateEngine状态异常", e)
            SystemOperationResult.failure("UpdateEngine reset failed: ${e.message}")
        }
    }

    /**
     * 重启系统
     */
    fun rebootSystem(): SystemOperationResult {
        return try {
            Logger.platformI("$TAG 开始重启系统")
            Runtime.getRuntime().exec("reboot")
            SystemOperationResult.success("System reboot initiated")
        } catch (e: Exception) {
            Logger.platformE("$TAG 系统重启失败", e)
            SystemOperationResult.failure("System reboot failed: ${e.message}")
        }
    }
    /**
     * 清理更新文件
     */
    fun cleanupUpdateFiles(): SystemOperationResult {
        return try {
            Logger.platformI("$TAG 清理更新文件")
            upgradeStrategyFactory.cleanup()
            SystemOperationResult.success("Update files cleaned")
        } catch (e: Exception) {
            Logger.platformE("$TAG 清理更新文件失败", e)
            SystemOperationResult.failure("Cleanup failed: ${e.message}")
        }
    }
    /**
     * 创建更新临时目录
     */
    private fun createUpdateTempDirectory() {
        try {
            val tempDir = File(UPDATE_TEMP_DIR)
            if (!tempDir.exists()) {
                tempDir.mkdirs()
                Logger.platformI("$TAG 创建更新临时目录: $UPDATE_TEMP_DIR")
            }
        } catch (e: Exception) {
            Logger.platformW("$TAG 创建临时目录失败: ${e.message}")
        }
    }

    /**
     * 获取可用存储空间
     */
    private fun getAvailableSpace(): Long {
        return try {
            val stat = android.os.StatFs("/data")
            stat.availableBytes
        } catch (e: Exception) {
            0L
        }
    }

    /**
     * 获取电池电量
     */
    private fun getBatteryLevel(): Int {
        return try {
            val batteryManager = context.getSystemService(Context.BATTERY_SERVICE) as android.os.BatteryManager
            batteryManager.getIntProperty(android.os.BatteryManager.BATTERY_PROPERTY_CAPACITY)
        } catch (e: Exception) {
            100 // 默认假设电量充足
        }
    }

    /**
     * 格式化字节数
     */
    private fun formatBytes(bytes: Long): String {
        return when {
            bytes >= 1024 * 1024 * 1024 -> "%.2f GB".format(bytes / (1024.0 * 1024.0 * 1024.0))
            bytes >= 1024 * 1024 -> "%.2f MB".format(bytes / (1024.0 * 1024.0))
            bytes >= 1024 -> "%.2f KB".format(bytes / 1024.0)
            else -> "$bytes bytes"
        }
    }

    /**
     * 释放资源
     */
    fun release() {
        try {
            upgradeStrategyFactory.cleanup()
            Logger.platformI("$TAG 资源释放完成")
        } catch (e: Exception) {
            Logger.platformW("$TAG 资源释放失败: ${e.message}")
        }
    }

    /**
     * 获取当前系统版本信息
     */
    fun getCurrentSystemInfo(): UpdateInfo {
        return try {
            UpdateInfo(
                currentVersion = Build.VERSION.RELEASE,
                buildNumber = Build.DISPLAY,
                securityPatch = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                    Build.VERSION.SECURITY_PATCH
                } else {
                    "unknown"
                },
                buildFingerprint = Build.FINGERPRINT,
                buildId = Build.ID,
                buildType = Build.TYPE,
                buildTags = Build.TAGS,
                sdkVersion = Build.VERSION.SDK_INT,
                incrementalVersion = Build.VERSION.INCREMENTAL
            )
        } catch (e: Exception) {
            Logger.platformE("$TAG 获取系统信息失败", e)
            UpdateInfo(
                currentVersion = "unknown",
                buildNumber = "unknown",
                securityPatch = "unknown",
                buildFingerprint = "unknown",
                buildId = "unknown",
                buildType = "unknown",
                buildTags = "unknown",
                sdkVersion = 0,
                incrementalVersion = "unknown"
            )
        }
    }

    /**
     * 更新策略枚举
     */
    enum class UpdatePolicy {
        AUTOMATIC,  // 自动更新
        MANUAL,     // 手动更新
        DISABLED    // 禁用更新
    }

    /**
     * 版本检查状态
     */
    enum class VersionCheckStatus {
        SAME_VERSION,    // 相同版本
        OLDER_VERSION,   // 升级包版本更旧
        NEWER_VERSION,   // 升级包版本更新
        UNKNOWN          // 无法确定
    }

    /**
     * 版本检查结果
     */
    data class VersionCheckResult(
        val status: VersionCheckStatus,
        val currentVersion: String,
        val updateVersion: String,
        val message: String
    )

    /**
     * 检查升级包版本兼容性
     * @param updateFile 升级包文件
     * @return 版本检查结果
     */
    fun checkVersionCompatibility(updateFile: File): VersionCheckResult {
        return try {
            val currentVersion = getCurrentSystemVersion()
            val updateVersion = extractVersionFromUpdatePackage(updateFile)

            if (updateVersion.isEmpty()) {
                Logger.platformW("$TAG 无法从升级包中提取版本信息")
                return VersionCheckResult(
                    VersionCheckStatus.UNKNOWN,
                    currentVersion,
                    "unknown",
                    "无法获取升级包版本信息"
                )
            }

            Logger.platformI("$TAG 版本比较: 当前=$currentVersion, 升级包=$updateVersion")

            val comparison = compareVersions(currentVersion, updateVersion)
            val status = when {
                comparison == 0 -> VersionCheckStatus.SAME_VERSION
                comparison > 0 -> VersionCheckStatus.OLDER_VERSION
                else -> VersionCheckStatus.NEWER_VERSION
            }

            VersionCheckResult(status, currentVersion, updateVersion, "版本检查完成")

        } catch (e: Exception) {
            Logger.platformE("$TAG 版本检查异常", e)
            val currentVersion = getCurrentSystemVersion()
            VersionCheckResult(
                VersionCheckStatus.UNKNOWN,
                currentVersion,
                "error",
                "版本检查失败: ${e.message}"
            )
        }
    }

    /**
     * 获取当前系统版本
     */
    private fun getCurrentSystemVersion(): String {
        return try {
            // 根据Android版本使用不同的版本获取方式
            when {
                Build.VERSION.SDK_INT >= 34 -> {
                    // Android 14+: 可能需要特殊处理A/B分区版本
                    Build.DISPLAY ?: Build.VERSION.INCREMENTAL ?: ""
                }
                else -> {
                    // Android < 14: 使用传统方式
                    Build.DISPLAY ?: Build.VERSION.INCREMENTAL ?: ""
                }
            }
        } catch (e: Exception) {
            Logger.platformE("$TAG 获取当前系统版本失败", e)
            ""
        }
    }

    /**
     * 从升级包中提取版本信息（支持A14+和A14以下两种格式）
     */
    private fun extractVersionFromUpdatePackage(updateFile: File): String {
        return try {
            ZipFile(updateFile).use { zipFile ->
                Logger.platformI("$TAG 开始分析升级包: ${updateFile.name}")

                // 方法1: Android 14+ A/B升级包格式
                val version14Plus = extractVersionFromA14Format(zipFile)
                if (version14Plus.isNotEmpty()) {
                    Logger.platformI("$TAG A14+格式提取版本: $version14Plus")
                    return version14Plus
                }

                // 方法2: Android <14 传统升级包格式
                val versionLegacy = extractVersionFromLegacyFormat(zipFile)
                if (versionLegacy.isNotEmpty()) {
                    Logger.platformI("$TAG 传统格式提取版本: $versionLegacy")
                    return versionLegacy
                }

                // 方法3: 从文件名提取版本信息（备用方案）
                val fileName = updateFile.name
                val versionPattern = Regex("([\\d]+\\.[\\d]+\\.[\\d]+)")
                val match = versionPattern.find(fileName)
                if (match != null) {
                    Logger.platformI("$TAG 从文件名提取版本: ${match.value}")
                    return match.value
                }

                Logger.platformW("$TAG 无法从升级包中提取版本信息")
                ""
            }
        } catch (e: Exception) {
            Logger.platformE("$TAG 提取升级包版本失败", e)
            ""
        }
    }

    /**
     * 比较两个版本号
     * @return 0=相等, >0=version1更新, <0=version1更旧
     */
    private fun compareVersions(version1: String, version2: String): Int {
        return try {
            Logger.platformI("$TAG 详细版本比较: '$version1' vs '$version2'")

            // 如果版本字符串完全相同，直接返回相等
            if (version1 == version2) {
                Logger.platformI("$TAG 版本字符串完全相同")
                return 0
            }

            // 提取版本号进行比较
            val v1Clean = cleanVersionString(version1)
            val v2Clean = cleanVersionString(version2)

            Logger.platformI("$TAG 清理后版本: '$v1Clean' vs '$v2Clean'")

            if (v1Clean == v2Clean) {
                Logger.platformI("$TAG 清理后版本相同")
                return 0
            }

            // 特殊处理：如果两个版本都包含相同的核心版本号，认为相同
            val v1Core = extractCoreVersion(v1Clean)
            val v2Core = extractCoreVersion(v2Clean)
            Logger.platformI("$TAG 核心版本: '$v1Core' vs '$v2Core'")

            if (v1Core == v2Core && v1Core.isNotEmpty()) {
                Logger.platformI("$TAG 核心版本相同")
                return 0
            }

            // 数字版本比较
            val v1Numbers = extractVersionNumbers(v1Clean)
            val v2Numbers = extractVersionNumbers(v2Clean)

            Logger.platformI("$TAG 版本数字: $v1Numbers vs $v2Numbers")

            // 逐段比较
            val maxLength = maxOf(v1Numbers.size, v2Numbers.size)
            for (i in 0 until maxLength) {
                val v1Part = v1Numbers.getOrElse(i) { 0 }
                val v2Part = v2Numbers.getOrElse(i) { 0 }

                when {
                    v1Part > v2Part -> {
                        Logger.platformI("$TAG 版本1更新: $v1Part > $v2Part")
                        return 1
                    }
                    v1Part < v2Part -> {
                        Logger.platformI("$TAG 版本1更旧: $v1Part < $v2Part")
                        return -1
                    }
                }
            }

            Logger.platformI("$TAG 版本相等")
            0 // 相等
        } catch (e: Exception) {
            Logger.platformE("$TAG 版本比较异常", e)
            0 // 异常时认为相等
        }
    }

    /**
     * 清理版本字符串，提取核心版本信息
     */
    private fun cleanVersionString(version: String): String {
        return try {
            Logger.platformI("$TAG 清理版本字符串: '$version'")

            // 优先匹配完整的版本格式: V1.0.8.1_202507032000 或 1.0.8.1_202507032000
            val fullVersionMatch = Regex("V?([\\d]+\\.[\\d]+\\.[\\d]+(?:\\.[\\d]+)?(?:_[\\d]+)?)").find(version)
            if (fullVersionMatch != null) {
                val result = fullVersionMatch.groupValues[1]
                Logger.platformI("$TAG 提取完整版本: '$result'")
                return result
            }

            // 匹配简单的三段版本号: 1.0.8
            val simpleVersionMatch = Regex("([\\d]+\\.[\\d]+\\.[\\d]+)").find(version)
            if (simpleVersionMatch != null) {
                val result = simpleVersionMatch.value
                Logger.platformI("$TAG 提取简单版本: '$result'")
                return result
            }

            // 如果都没匹配到，返回原字符串
            Logger.platformW("$TAG 无法清理版本字符串，返回原值: '$version'")
            version
        } catch (e: Exception) {
            Logger.platformE("$TAG 清理版本字符串异常", e)
            version
        }
    }

    /**
     * 提取版本号中的数字部分
     */
    private fun extractVersionNumbers(version: String): List<Int> {
        return try {
            // 提取所有数字序列
            val numbers = Regex("\\d+").findAll(version)
                .map { it.value.toIntOrNull() ?: 0 }
                .toList()

            if (numbers.isEmpty()) {
                listOf(0)
            } else {
                numbers
            }
        } catch (e: Exception) {
            listOf(0)
        }
    }

    /**
     * 从Android 14+ A/B升级包格式提取版本信息
     */
    private fun extractVersionFromA14Format(zipFile: ZipFile): String {
        return try {
            // 检查A/B升级包特征文件
            val payloadEntry = zipFile.getEntry("payload.bin")
            val payloadPropsEntry = zipFile.getEntry("payload_properties.txt")

            if (payloadEntry != null || payloadPropsEntry != null) {
                Logger.platformI("$TAG 检测到A/B升级包格式")

                // 从META-INF/com/android/metadata提取版本
                val metadataEntry = zipFile.getEntry("META-INF/com/android/metadata")
                if (metadataEntry != null) {
                    zipFile.getInputStream(metadataEntry).use { inputStream ->
                        BufferedReader(InputStreamReader(inputStream)).use { reader ->
                            reader.lineSequence().forEach { line ->
                                when {
                                    line.startsWith("post-build-incremental=") -> {
                                        // 优先使用post-build-incremental，这是最准确的版本号
                                        val incremental = line.substringAfter("=")
                                        if (incremental.isNotEmpty()) {
                                            Logger.platformI("$TAG 从post-build-incremental提取版本: $incremental")
                                            return incremental
                                        }
                                    }
                                    line.startsWith("pre-build-incremental=") -> {
                                        // 备用：pre-build-incremental
                                        val incremental = line.substringAfter("=")
                                        if (incremental.isNotEmpty()) {
                                            Logger.platformI("$TAG 从pre-build-incremental提取版本: $incremental")
                                            return incremental
                                        }
                                    }
                                    line.startsWith("post-build=") || line.startsWith("pre-build=") -> {
                                        // 从完整build信息中提取版本
                                        val buildInfo = line.substringAfter("=")
                                        // 尝试提取V1.0.8.1_202507032000格式的版本
                                        val versionMatch = Regex("V([\\d\\.]+_[\\d]+)").find(buildInfo)
                                        if (versionMatch != null) {
                                            Logger.platformI("$TAG 从build信息提取版本: ${versionMatch.groupValues[1]}")
                                            return versionMatch.groupValues[1]
                                        }
                                        // 备用：简单数字版本
                                        val simpleMatch = Regex("([\\d]+\\.[\\d]+\\.[\\d]+)").find(buildInfo)
                                        if (simpleMatch != null) {
                                            Logger.platformI("$TAG 从build信息提取简单版本: ${simpleMatch.value}")
                                            return simpleMatch.value
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
            ""
        } catch (e: Exception) {
            Logger.platformE("$TAG A14+格式版本提取失败", e)
            ""
        }
    }

    /**
     * 从Android <14 传统升级包格式提取版本信息
     */
    private fun extractVersionFromLegacyFormat(zipFile: ZipFile): String {
        return try {
            // 检查传统升级包特征文件
            val systemBuildProp = zipFile.getEntry("system/build.prop")
            val metaInfEntry = zipFile.getEntry("META-INF/com/android/metadata")

            if (systemBuildProp != null) {
                Logger.platformI("$TAG 检测到传统升级包格式")

                // 从system/build.prop提取版本
                zipFile.getInputStream(systemBuildProp).use { inputStream ->
                    BufferedReader(InputStreamReader(inputStream)).use { reader ->
                        reader.lineSequence().forEach { line ->
                            when {
                                line.startsWith("ro.build.display.id=") -> {
                                    val displayId = line.substringAfter("=")
                                    val versionMatch = Regex("([\\d]+\\.[\\d]+\\.[\\d]+)").find(displayId)
                                    if (versionMatch != null) {
                                        return versionMatch.value
                                    }
                                }
                                line.startsWith("ro.build.version.incremental=") -> {
                                    val incremental = line.substringAfter("=")
                                    val versionMatch = Regex("([\\d]+\\.[\\d]+\\.[\\d]+)").find(incremental)
                                    if (versionMatch != null) {
                                        return versionMatch.value
                                    }
                                }
                            }
                        }
                    }
                }
            } else if (metaInfEntry != null) {
                // 如果没有system/build.prop，尝试从metadata提取
                zipFile.getInputStream(metaInfEntry).use { inputStream ->
                    BufferedReader(InputStreamReader(inputStream)).use { reader ->
                        reader.lineSequence().forEach { line ->
                            if (line.startsWith("post-build=") || line.startsWith("pre-build=")) {
                                val buildInfo = line.substringAfter("=")
                                val versionMatch = Regex("([\\d]+\\.[\\d]+\\.[\\d]+)").find(buildInfo)
                                if (versionMatch != null) {
                                    return versionMatch.value
                                }
                            }
                        }
                    }
                }
            }
            ""
        } catch (e: Exception) {
            Logger.platformE("$TAG 传统格式版本提取失败", e)
            ""
        }
    }

    /**
     * 提取核心版本号（去掉时间戳等后缀）
     */
    private fun extractCoreVersion(version: String): String {
        return try {
            // 提取主版本号部分，去掉时间戳
            // 例如: "1.0.8.1_202507032000" -> "1.0.8.1"
            val coreMatch = Regex("([\\d]+\\.[\\d]+\\.[\\d]+(?:\\.[\\d]+)?)").find(version)
            if (coreMatch != null) {
                return coreMatch.value
            }
            version
        } catch (e: Exception) {
            version
        }
    }
}
