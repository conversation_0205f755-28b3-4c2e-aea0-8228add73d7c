package com.dspread.mdm.service.network.websocket.message.strategy

/**
 * 常用触发条件常量
 * 定义各种消息上送的触发条件
 */
object UploadTriggers {
    // 平台指令相关
    const val PLATFORM_REQUEST = "platform_request"               // 平台请求
    const val COMMAND_TRIGGER = "command_trigger"                 // 指令触发
    const val QUERY_REQUEST = "query_request"                     // 查询请求
    const val API_CALL = "api_call"                              // API调用
    const val REMOTE_COMMAND = "remote_command"                   // 远程指令

    // 电池相关
    const val BATTERY_LOW_CRITICAL = "battery_low_critical"        // 电量严重不足（<10%）
    const val CHARGING_STATE_CHANGE = "charging_state_change"      // 充电状态变化
    const val TEMPERATURE_ABNORMAL = "temperature_abnormal"        // 温度异常（<0°C或>50°C）
    const val BATTERY_LEVEL_CHANGE_5 = "battery_level_change_5"    // 电量变化5%以上
    const val TEMPERATURE_CHANGE_5 = "temperature_change_5"        // 温度变化5度以上
    
    // 网络相关
    const val NETWORK_CONNECT = "network_connect"                  // 网络连接
    const val NETWORK_DISCONNECT = "network_disconnect"            // 网络断开
    const val WIFI_SWITCH = "wifi_switch"                         // WiFi切换
    const val SIGNAL_STRENGTH_CHANGE = "signal_strength_change"    // 信号强度变化
    const val IP_CHANGE = "ip_change"                             // IP地址变化
    
    // 应用相关
    const val APP_INSTALL = "app_install"                         // 应用安装
    const val APP_UNINSTALL = "app_uninstall"                     // 应用卸载
    const val APP_UPDATE = "app_update"                           // 应用更新
    const val SERVICE_CHANGE = "service_change"                   // 服务变化
    const val PERMISSION_CHANGE = "permission_change"             // 权限变化
    
    // 位置相关
    const val LOCATION_CHANGE = "location_change"                 // 位置变化
    const val LOCATION_GEOFENCE_CHANGE = "location_geofence_change" // 地理围栏变化
    const val HARDWARE_CHANGE = "hardware_change"                 // 硬件状态变化
    
    // 系统相关
    const val CRASH = "crash"                                     // 崩溃
    const val SECURITY_EVENT = "security_event"                   // 安全事件
    const val SYSTEM_ERROR = "system_error"                       // 系统错误
    const val WARNING_EVENT = "warning_event"                     // 警告事件
    const val FIRST_CONNECTION = "first_connection"               // 首次连接
    const val MANUAL_TRIGGER = "manual_trigger"                   // 手动触发
    const val SIGNIFICANT_CHANGE = "significant_change"           // 重要变化
    const val DEVICE_USAGE_CHANGE = "device_usage_change"         // 设备使用状态变化

    // 任务相关
    const val TASK_START = "task_start"                           // 任务开始
    const val TASK_COMPLETE = "task_complete"                     // 任务完成
    const val TASK_FAILED = "task_failed"                         // 任务失败
    const val RULEBASE_UPDATE = "rulebase_update"                 // 规则引擎更新

    // 定时器相关
    const val TERMINAL_INFO_TIMER = "terminal_info_timer"         // 终端信息定时器
    const val DAILY_TRAFFIC_REPORT = "daily_traffic_report"       // 每日流量统计上报
}
