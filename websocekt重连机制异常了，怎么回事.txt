--------- beginning of system
--------- beginning of main
2025-08-18 16:14:05.850   961-1047  ActivityManager         pid-961                              E  Sending non-protected broadcast com.dspread.mdm.service.WSTASK_EXEC_BC from system uid 1000 pkg com.dspread.mdm.service (Ask Gemini)
                                                                                                    java.lang.Throwable
                                                                                                    	at com.android.server.am.ActivityManagerService.checkBroadcastFromSystem(ActivityManagerService.java:15082)
                                                                                                    	at com.android.server.am.ActivityManagerService.broadcastIntentLocked(ActivityManagerService.java:15719)
                                                                                                    	at com.android.server.am.ActivityManagerService.broadcastIntentInPackage(ActivityManagerService.java:15916)
                                                                                                    	at com.android.server.am.ActivityManagerService$LocalService.broadcastIntentInPackage(ActivityManagerService.java:18556)
                                                                                                    	at com.android.server.am.PendingIntentRecord.sendInner(PendingIntentRecord.java:432)
                                                                                                    	at com.android.server.am.PendingIntentRecord.sendWithResult(PendingIntentRecord.java:276)
                                                                                                    	at com.android.server.am.ActivityManagerService.sendIntentSender(ActivityManagerService.java:5633)
                                                                                                    	at android.app.PendingIntent.sendAndReturnResult(PendingIntent.java:896)
                                                                                                    	at android.app.PendingIntent.send(PendingIntent.java:878)
                                                                                                    	at com.android.server.AlarmManagerService$DeliveryTracker.deliverLocked(AlarmManagerService.java:4760)
                                                                                                    	at com.android.server.AlarmManagerService.deliverAlarmsLocked(AlarmManagerService.java:3899)
                                                                                                    	at com.android.server.AlarmManagerService$AlarmThread.run(AlarmManagerService.java:4160)
2025-08-18 16:15:05.869   961-1047  ActivityManager         pid-961                              E  Sending non-protected broadcast com.dspread.mdm.service.WSTASK_EXEC_BC from system uid 1000 pkg com.dspread.mdm.service (Ask Gemini)
                                                                                                    java.lang.Throwable
                                                                                                    	at com.android.server.am.ActivityManagerService.checkBroadcastFromSystem(ActivityManagerService.java:15082)
                                                                                                    	at com.android.server.am.ActivityManagerService.broadcastIntentLocked(ActivityManagerService.java:15719)
                                                                                                    	at com.android.server.am.ActivityManagerService.broadcastIntentInPackage(ActivityManagerService.java:15916)
                                                                                                    	at com.android.server.am.ActivityManagerService$LocalService.broadcastIntentInPackage(ActivityManagerService.java:18556)
                                                                                                    	at com.android.server.am.PendingIntentRecord.sendInner(PendingIntentRecord.java:432)
                                                                                                    	at com.android.server.am.PendingIntentRecord.sendWithResult(PendingIntentRecord.java:276)
                                                                                                    	at com.android.server.am.ActivityManagerService.sendIntentSender(ActivityManagerService.java:5633)
                                                                                                    	at android.app.PendingIntent.sendAndReturnResult(PendingIntent.java:896)
                                                                                                    	at android.app.PendingIntent.send(PendingIntent.java:878)
                                                                                                    	at com.android.server.AlarmManagerService$DeliveryTracker.deliverLocked(AlarmManagerService.java:4760)
                                                                                                    	at com.android.server.AlarmManagerService.deliverAlarmsLocked(AlarmManagerService.java:3899)
                                                                                                    	at com.android.server.AlarmManagerService$AlarmThread.run(AlarmManagerService.java:4160)
2025-08-18 16:15:24.223   961-1047  ActivityManager         system_server                        E  Sending non-protected broadcast com.dspread.mdm.service.SERVICE_GUARD_TIMER from system uid 1000 pkg com.dspread.mdm.service (Ask Gemini)
                                                                                                    java.lang.Throwable
                                                                                                    	at com.android.server.am.ActivityManagerService.checkBroadcastFromSystem(ActivityManagerService.java:15082)
                                                                                                    	at com.android.server.am.ActivityManagerService.broadcastIntentLocked(ActivityManagerService.java:15719)
                                                                                                    	at com.android.server.am.ActivityManagerService.broadcastIntentInPackage(ActivityManagerService.java:15916)
                                                                                                    	at com.android.server.am.ActivityManagerService$LocalService.broadcastIntentInPackage(ActivityManagerService.java:18556)
                                                                                                    	at com.android.server.am.PendingIntentRecord.sendInner(PendingIntentRecord.java:432)
                                                                                                    	at com.android.server.am.PendingIntentRecord.sendWithResult(PendingIntentRecord.java:276)
                                                                                                    	at com.android.server.am.ActivityManagerService.sendIntentSender(ActivityManagerService.java:5633)
                                                                                                    	at android.app.PendingIntent.sendAndReturnResult(PendingIntent.java:896)
                                                                                                    	at android.app.PendingIntent.send(PendingIntent.java:878)
                                                                                                    	at com.android.server.AlarmManagerService$DeliveryTracker.deliverLocked(AlarmManagerService.java:4760)
                                                                                                    	at com.android.server.AlarmManagerService.deliverAlarmsLocked(AlarmManagerService.java:3899)
                                                                                                    	at com.android.server.AlarmManagerService$AlarmThread.run(AlarmManagerService.java:4160)
2025-08-18 16:15:24.242  4087-4125  Common                  com.dspread.mdm.service              D  🔧 ServiceGuardEventHandler 开始执行保活动作
2025-08-18 16:15:24.247  4087-4125  Common                  com.dspread.mdm.service              I  ✅ ⏰ 设置服务守护定时器成功，下次执行: 120秒后 (2分钟)
2025-08-18 16:15:24.250  4087-4125  ContextImpl             com.dspread.mdm.service              W  Calling a method in the system process without a qualified user: android.app.ContextImpl.startService:1571 android.content.ContextWrapper.startService:669 com.dspread.mdm.service.broadcast.handlers.service.ServiceGuardEventHandler.startKeepAliveService:211 com.dspread.mdm.service.broadcast.handlers.service.ServiceGuardEventHandler.performKeepAliveActions:193 com.dspread.mdm.service.broadcast.handlers.service.ServiceGuardEventHandler.access$performKeepAliveActions:24 
2025-08-18 16:15:24.253  4087-4087  Service                 com.dspread.mdm.service              D  🔧 ServiceKeepAliveService 保活服务启动
2025-08-18 16:15:24.253  4087-4125  Common                  com.dspread.mdm.service              D  🔧 ServiceGuardEventHandler 启动保活服务
2025-08-18 16:15:24.254  4087-4087  Platform                com.dspread.mdm.service              D  🔧 WakeLockManager WakeLock不存在或未持有: ServiceKeepAlive
2025-08-18 16:15:24.258  4087-4087  Platform                com.dspread.mdm.service              D  🔧 WakeLockManager 获取WakeLock成功: ServiceKeepAlive (超时: 30000ms)
2025-08-18 16:15:24.261  4087-4125  Common                  com.dspread.mdm.service              D  🔧 ServiceGuardEventHandler 保活动作执行完成
2025-08-18 16:15:27.265  4087-4125  Common                  com.dspread.mdm.service              D  🔧 ServiceGuardEventHandler WakeLock已释放
2025-08-18 16:15:33.385  4087-4087  ActivityThread          com.dspread.mdm.service              W  handleWindowVisibility: no activity for token android.os.BinderProxy@bf2e84f
2025-08-18 16:15:33.490  4087-4087  PhoneWindow             com.dspread.mdm.service              V  DecorView setVisiblity: visibility = 4, Parent = null, this = DecorView@7d9d60d[]
2025-08-18 16:15:33.495  4087-4087  ViewRootIm...tActivity] com.dspread.mdm.service              D  hardware acceleration = true , fakeHwAccelerated = false, sRendererDisabled = false, forceHwAccelerated = false, sSystemRendererDisabled = false
2025-08-18 16:15:33.501  4087-4087  InputTransport          com.dspread.mdm.service              I  Create ARC handle: 0x7eeb8360
2025-08-18 16:15:33.502  4087-4087  PhoneWindow             com.dspread.mdm.service              V  DecorView setVisiblity: visibility = 0, Parent = android.view.ViewRootImpl@1930b10, this = DecorView@7d9d60d[TestActivity]
2025-08-18 16:15:33.515  4087-4125  Platform                com.dspread.mdm.service              W  ⚠️ SpVersionApi SP版本信息格式异常，未找到SP_VERSION标记
2025-08-18 16:15:33.517  4087-4125  Platform                com.dspread.mdm.service              D  🔧 SpVersionApi 从Settings获取SP版本: 588.30.241.59
2025-08-18 16:15:33.539  4087-4130  Surface                 com.dspread.mdm.service              D  Surface::connect(this=0x7f0e3000,api=1)
2025-08-18 16:15:33.542  4087-4130  Surface                 com.dspread.mdm.service              D  Surface::setBufferCount(this=0x7f0e3000,bufferCount=3)
2025-08-18 16:15:33.542  4087-4130  Surface                 com.dspread.mdm.service              D  Surface::allocateBuffers(this=0x7f0e3000)
2025-08-18 16:15:33.599  4087-4087  WebSocket               com.dspread.mdm.service              I  🔧 C0905 主动式上送: manual_trigger (主动: 11)
2025-08-18 16:15:33.604  4087-4087  WebSocket               com.dspread.mdm.service              I  🔧 首次上送，上送当日数据
2025-08-18 16:15:33.617  4087-4087  WebSocket               com.dspread.mdm.service              I  🔧 消息已缓存: C0905 (缓存数量: 6)
2025-08-18 16:15:33.619  4087-4087  WebSocket               com.dspread.mdm.service              I  🔧 ✅ C0905 网络流量统计上送成功 (1) - 总流量: 9.1MB - 触发: manual_trigger - 数据条数: 1
2025-08-18 16:15:33.620  4087-4087  Common                  com.dspread.mdm.service              D  🔧 刷新时自动上送流量统计
2025-08-18 16:15:53.321  4087-4087  Receiver                com.dspread.mdm.service              D  🔧 电池状态更新: 电池状态: 电量=41%, 温度=36.0°C, 健康=2, 充电=是, 低电量=否, 耗尽=否
2025-08-18 16:15:53.350  4087-4087  Receiver                com.dspread.mdm.service              D  🔧 电池状态更新: 电池状态: 电量=41%, 温度=36.1°C, 健康=2, 充电=是, 低电量=否, 耗尽=否
2025-08-18 16:15:54.261  4087-4087  Platform                com.dspread.mdm.service              D  🔧 WakeLockManager WakeLock不存在或未持有: ServiceKeepAlive
2025-08-18 16:15:54.262  4087-4087  Service                 com.dspread.mdm.service              D  🔧 ServiceKeepAliveService 保活任务完成，释放WakeLock
2025-08-18 16:16:05.889   961-1047  ActivityManager         system_server                        E  Sending non-protected broadcast com.dspread.mdm.service.WSTASK_EXEC_BC from system uid 1000 pkg com.dspread.mdm.service (Ask Gemini)
                                                                                                    java.lang.Throwable
                                                                                                    	at com.android.server.am.ActivityManagerService.checkBroadcastFromSystem(ActivityManagerService.java:15082)
                                                                                                    	at com.android.server.am.ActivityManagerService.broadcastIntentLocked(ActivityManagerService.java:15719)
                                                                                                    	at com.android.server.am.ActivityManagerService.broadcastIntentInPackage(ActivityManagerService.java:15916)
                                                                                                    	at com.android.server.am.ActivityManagerService$LocalService.broadcastIntentInPackage(ActivityManagerService.java:18556)
                                                                                                    	at com.android.server.am.PendingIntentRecord.sendInner(PendingIntentRecord.java:432)
                                                                                                    	at com.android.server.am.PendingIntentRecord.sendWithResult(PendingIntentRecord.java:276)
                                                                                                    	at com.android.server.am.ActivityManagerService.sendIntentSender(ActivityManagerService.java:5633)
                                                                                                    	at android.app.PendingIntent.sendAndReturnResult(PendingIntent.java:896)
                                                                                                    	at android.app.PendingIntent.send(PendingIntent.java:878)
                                                                                                    	at com.android.server.AlarmManagerService$DeliveryTracker.deliverLocked(AlarmManagerService.java:4760)
                                                                                                    	at com.android.server.AlarmManagerService.deliverAlarmsLocked(AlarmManagerService.java:3899)
                                                                                                    	at com.android.server.AlarmManagerService$AlarmThread.run(AlarmManagerService.java:4160)
2025-08-18 16:16:05.910  4087-4087  Common                  com.dspread.mdm.service              I  ✅ ⏰ 设置任务执行定时器成功，下次执行: 60秒后 (1分钟)
2025-08-18 16:16:11.466  4087-4087  Receiver                com.dspread.mdm.service              D  🔧 电池状态更新: 电池状态: 电量=41%, 温度=35.8°C, 健康=2, 充电=是, 低电量=否, 耗尽=否
2025-08-18 16:16:52.055  4087-4087  Receiver                com.dspread.mdm.service              D  🔧 电池状态更新: 电池状态: 电量=42%, 温度=35.6°C, 健康=2, 充电=是, 低电量=否, 耗尽=否
2025-08-18 16:17:05.757   961-1047  ActivityManager         system_server                        E  Sending non-protected broadcast com.dspread.mdm.service.POLL_TIMER_START from system uid 1000 pkg com.dspread.mdm.service (Ask Gemini)
                                                                                                    java.lang.Throwable
                                                                                                    	at com.android.server.am.ActivityManagerService.checkBroadcastFromSystem(ActivityManagerService.java:15082)
                                                                                                    	at com.android.server.am.ActivityManagerService.broadcastIntentLocked(ActivityManagerService.java:15719)
                                                                                                    	at com.android.server.am.ActivityManagerService.broadcastIntentInPackage(ActivityManagerService.java:15916)
                                                                                                    	at com.android.server.am.ActivityManagerService$LocalService.broadcastIntentInPackage(ActivityManagerService.java:18556)
                                                                                                    	at com.android.server.am.PendingIntentRecord.sendInner(PendingIntentRecord.java:432)
                                                                                                    	at com.android.server.am.PendingIntentRecord.sendWithResult(PendingIntentRecord.java:276)
                                                                                                    	at com.android.server.am.ActivityManagerService.sendIntentSender(ActivityManagerService.java:5633)
                                                                                                    	at android.app.PendingIntent.sendAndReturnResult(PendingIntent.java:896)
                                                                                                    	at android.app.PendingIntent.send(PendingIntent.java:878)
                                                                                                    	at com.android.server.AlarmManagerService$DeliveryTracker.deliverLocked(AlarmManagerService.java:4760)
                                                                                                    	at com.android.server.AlarmManagerService.deliverAlarmsLocked(AlarmManagerService.java:3899)
                                                                                                    	at com.android.server.AlarmManagerService$AlarmThread.run(AlarmManagerService.java:4160)
2025-08-18 16:17:05.774  4087-4087  Receiver                com.dspread.mdm.service              D  🔧 收到轮询定时器广播
2025-08-18 16:17:05.774  4087-4087  Receiver                com.dspread.mdm.service              D  🔧 WebSocket未连接，跳过轮询任务
2025-08-18 16:17:05.779  4087-4087  Common                  com.dspread.mdm.service              D  🔧 TimerConfig 使用Provisioning配置的心跳间隔: 300秒
2025-08-18 16:17:05.783  4087-4087  Common                  com.dspread.mdm.service              I  ✅ ⏰ 设置心跳定时器成功，下次执行: 300秒后 (5分钟)
2025-08-18 16:17:05.910   961-1047  ActivityManager         system_server                        E  Sending non-protected broadcast com.dspread.mdm.service.WSTASK_EXEC_BC from system uid 1000 pkg com.dspread.mdm.service (Ask Gemini)
                                                                                                    java.lang.Throwable
                                                                                                    	at com.android.server.am.ActivityManagerService.checkBroadcastFromSystem(ActivityManagerService.java:15082)
                                                                                                    	at com.android.server.am.ActivityManagerService.broadcastIntentLocked(ActivityManagerService.java:15719)
                                                                                                    	at com.android.server.am.ActivityManagerService.broadcastIntentInPackage(ActivityManagerService.java:15916)
                                                                                                    	at com.android.server.am.ActivityManagerService$LocalService.broadcastIntentInPackage(ActivityManagerService.java:18556)
                                                                                                    	at com.android.server.am.PendingIntentRecord.sendInner(PendingIntentRecord.java:432)
                                                                                                    	at com.android.server.am.PendingIntentRecord.sendWithResult(PendingIntentRecord.java:276)
                                                                                                    	at com.android.server.am.ActivityManagerService.sendIntentSender(ActivityManagerService.java:5633)
                                                                                                    	at android.app.PendingIntent.sendAndReturnResult(PendingIntent.java:896)
                                                                                                    	at android.app.PendingIntent.send(PendingIntent.java:878)
                                                                                                    	at com.android.server.AlarmManagerService$DeliveryTracker.deliverLocked(AlarmManagerService.java:4760)
                                                                                                    	at com.android.server.AlarmManagerService.deliverAlarmsLocked(AlarmManagerService.java:3899)
                                                                                                    	at com.android.server.AlarmManagerService$AlarmThread.run(AlarmManagerService.java:4160)
2025-08-18 16:17:05.926  4087-4087  Common                  com.dspread.mdm.service              I  ✅ ⏰ 设置任务执行定时器成功，下次执行: 60秒后 (1分钟)
2025-08-18 16:17:11.468  4087-4087  Receiver                com.dspread.mdm.service              D  🔧 电池状态更新: 电池状态: 电量=42%, 温度=35.4°C, 健康=2, 充电=是, 低电量=否, 耗尽=否
2025-08-18 16:17:24.247   961-1047  ActivityManager         system_server                        E  Sending non-protected broadcast com.dspread.mdm.service.SERVICE_GUARD_TIMER from system uid 1000 pkg com.dspread.mdm.service (Ask Gemini)
                                                                                                    java.lang.Throwable
                                                                                                    	at com.android.server.am.ActivityManagerService.checkBroadcastFromSystem(ActivityManagerService.java:15082)
                                                                                                    	at com.android.server.am.ActivityManagerService.broadcastIntentLocked(ActivityManagerService.java:15719)
                                                                                                    	at com.android.server.am.ActivityManagerService.broadcastIntentInPackage(ActivityManagerService.java:15916)
                                                                                                    	at com.android.server.am.ActivityManagerService$LocalService.broadcastIntentInPackage(ActivityManagerService.java:18556)
                                                                                                    	at com.android.server.am.PendingIntentRecord.sendInner(PendingIntentRecord.java:432)
                                                                                                    	at com.android.server.am.PendingIntentRecord.sendWithResult(PendingIntentRecord.java:276)
                                                                                                    	at com.android.server.am.ActivityManagerService.sendIntentSender(ActivityManagerService.java:5633)
                                                                                                    	at android.app.PendingIntent.sendAndReturnResult(PendingIntent.java:896)
                                                                                                    	at android.app.PendingIntent.send(PendingIntent.java:878)
                                                                                                    	at com.android.server.AlarmManagerService$DeliveryTracker.deliverLocked(AlarmManagerService.java:4760)
                                                                                                    	at com.android.server.AlarmManagerService.deliverAlarmsLocked(AlarmManagerService.java:3899)
                                                                                                    	at com.android.server.AlarmManagerService$AlarmThread.run(AlarmManagerService.java:4160)
2025-08-18 16:17:24.265  4087-4125  Common                  com.dspread.mdm.service              D  🔧 ServiceGuardEventHandler 开始执行保活动作
2025-08-18 16:17:24.271  4087-4125  Common                  com.dspread.mdm.service              I  ✅ ⏰ 设置服务守护定时器成功，下次执行: 120秒后 (2分钟)
2025-08-18 16:17:24.272  4087-4125  ContextImpl             com.dspread.mdm.service              W  Calling a method in the system process without a qualified user: android.app.ContextImpl.startService:1571 android.content.ContextWrapper.startService:669 com.dspread.mdm.service.broadcast.handlers.service.ServiceGuardEventHandler.startKeepAliveService:211 com.dspread.mdm.service.broadcast.handlers.service.ServiceGuardEventHandler.performKeepAliveActions:193 com.dspread.mdm.service.broadcast.handlers.service.ServiceGuardEventHandler.access$performKeepAliveActions:24 
2025-08-18 16:17:24.276  4087-4125  Common                  com.dspread.mdm.service              D  🔧 ServiceGuardEventHandler 启动保活服务
2025-08-18 16:17:24.277  4087-4087  Service                 com.dspread.mdm.service              D  🔧 ServiceKeepAliveService 保活服务启动
2025-08-18 16:17:24.277  4087-4087  Platform                com.dspread.mdm.service              D  🔧 WakeLockManager WakeLock不存在或未持有: ServiceKeepAlive
2025-08-18 16:17:24.282  4087-4125  Common                  com.dspread.mdm.service              D  🔧 ServiceGuardEventHandler 保活动作执行完成
2025-08-18 16:17:24.282  4087-4087  Platform                com.dspread.mdm.service              D  🔧 WakeLockManager 获取WakeLock成功: ServiceKeepAlive (超时: 30000ms)
2025-08-18 16:17:27.286  4087-4125  Common                  com.dspread.mdm.service              D  🔧 ServiceGuardEventHandler WakeLock已释放
2025-08-18 16:17:31.832  4087-4087  Receiver                com.dspread.mdm.service              D  🔧 电池状态更新: 电池状态: 电量=42%, 温度=35.4°C, 健康=2, 充电=是, 低电量=否, 耗尽=否
2025-08-18 16:17:47.251  4087-4087  Receiver                com.dspread.mdm.service              D  🔧 网络状态变化: 连接=false, 类型=NONE
2025-08-18 16:17:47.252  4087-4087  Receiver                com.dspread.mdm.service              D  🔧 网络不可用，WebSocket连接可能受影响
2025-08-18 16:17:47.252  4087-4087  Receiver                com.dspread.mdm.service              D  🔧 ServiceManagementEventHandler 处理网络变化事件
2025-08-18 16:17:47.254  4087-4087  Common                  com.dspread.mdm.service              D  🔧 ServiceStartupManager 服务正在运行，无需启动: NETWORK_CHANGE
2025-08-18 16:17:47.254  4087-4087  Receiver                com.dspread.mdm.service              D  🔧 ServiceManagementEventHandler 网络变化后服务检查完成
2025-08-18 16:17:47.258  4087-4087  Provisioning            com.dspread.mdm.service              D  🔧 ProvisioningEventHandler 网络状态变化
2025-08-18 16:17:47.271  4087-4087  Provisioning            com.dspread.mdm.service              D  🔧 ProvisioningEventHandler 忽略初始启动时的网络连接事件
2025-08-18 16:17:51.421  4087-4087  Receiver                com.dspread.mdm.service              D  🔧 网络状态变化: 连接=true, 类型=WIFI
2025-08-18 16:17:51.422  4087-4087  Receiver                com.dspread.mdm.service              D  🔧 网络恢复，尝试重新连接WebSocket
2025-08-18 16:17:51.422  4087-4087  Receiver                com.dspread.mdm.service              D  🔧 ServiceManagementEventHandler 处理网络变化事件
2025-08-18 16:17:51.424  4087-4087  Common                  com.dspread.mdm.service              D  🔧 ServiceStartupManager 服务正在运行，无需启动: NETWORK_CHANGE
2025-08-18 16:17:51.425  4087-4087  Receiver                com.dspread.mdm.service              D  🔧 ServiceManagementEventHandler 网络变化后服务检查完成
2025-08-18 16:17:51.425  4087-4087  Provisioning            com.dspread.mdm.service              D  🔧 ProvisioningEventHandler 网络状态变化
2025-08-18 16:17:51.425  4087-4087  Provisioning            com.dspread.mdm.service              D  🔧 ProvisioningEventHandler 忽略初始启动时的网络连接事件
2025-08-18 16:17:54.285  4087-4087  Platform                com.dspread.mdm.service              D  🔧 WakeLockManager WakeLock不存在或未持有: ServiceKeepAlive
2025-08-18 16:17:54.286  4087-4087  Service                 com.dspread.mdm.service              D  🔧 ServiceKeepAliveService 保活任务完成，释放WakeLock
2025-08-18 16:18:05.925   961-1047  ActivityManager         system_server                        E  Sending non-protected broadcast com.dspread.mdm.service.WSTASK_EXEC_BC from system uid 1000 pkg com.dspread.mdm.service (Ask Gemini)
                                                                                                    java.lang.Throwable
                                                                                                    	at com.android.server.am.ActivityManagerService.checkBroadcastFromSystem(ActivityManagerService.java:15082)
                                                                                                    	at com.android.server.am.ActivityManagerService.broadcastIntentLocked(ActivityManagerService.java:15719)
                                                                                                    	at com.android.server.am.ActivityManagerService.broadcastIntentInPackage(ActivityManagerService.java:15916)
                                                                                                    	at com.android.server.am.ActivityManagerService$LocalService.broadcastIntentInPackage(ActivityManagerService.java:18556)
                                                                                                    	at com.android.server.am.PendingIntentRecord.sendInner(PendingIntentRecord.java:432)
                                                                                                    	at com.android.server.am.PendingIntentRecord.sendWithResult(PendingIntentRecord.java:276)
                                                                                                    	at com.android.server.am.ActivityManagerService.sendIntentSender(ActivityManagerService.java:5633)
                                                                                                    	at android.app.PendingIntent.sendAndReturnResult(PendingIntent.java:896)
                                                                                                    	at android.app.PendingIntent.send(PendingIntent.java:878)
                                                                                                    	at com.android.server.AlarmManagerService$DeliveryTracker.deliverLocked(AlarmManagerService.java:4760)
                                                                                                    	at com.android.server.AlarmManagerService.deliverAlarmsLocked(AlarmManagerService.java:3899)
                                                                                                    	at com.android.server.AlarmManagerService$AlarmThread.run(AlarmManagerService.java:4160)
2025-08-18 16:18:05.948  4087-4087  Common                  com.dspread.mdm.service              I  ✅ ⏰ 设置任务执行定时器成功，下次执行: 60秒后 (1分钟)
