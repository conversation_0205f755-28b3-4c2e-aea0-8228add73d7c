<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context="com.bbpos.wiseapp.settings.activity.BluetoothActivity">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/background"
        android:orientation="vertical">

        <include layout="@layout/top_bar"/>

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="56dp"
            android:background="@color/status_bar">
            <TextView
                android:id="@+id/tv_status"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_alignParentLeft="true"
                android:layout_marginLeft="72dp"
                android:text="On"
                android:textSize="16sp"
                android:textColor="@color/category"/>
            <com.bbpos.wiseapp.settings.widget.ToggleSwitch
                android:id="@+id/tb_switch"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_alignParentRight="true"
                android:layout_marginRight="16dp"
                android:textOff=""
                android:textOn=""/>
        </RelativeLayout>
        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:padding="24dp"
            android:background="@color/white">
            <TextView
                android:id="@+id/tv_prompt"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:gravity="center"
                android:text="正在關閉WiFi"
                android:textSize="14sp"
                android:textColor="@color/subtitle"/>
            <LinearLayout
                android:id="@+id/ll_bluetooth"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical">
                <LinearLayout
                    android:id="@+id/ll_listbonded"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:background="@color/white">
                    <TextView
                        android:id="@+id/tv_btbonded"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:gravity="center"
                        android:text="@string/bluetooth_preference_paired_dialog_title"
                        android:textSize="14sp"
                        android:textColor="@color/theme_green"/>
                    <ListView
                        android:id="@+id/list_btbonded"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:listSelector="@drawable/item_selector"
                        android:dividerHeight="1dp"
                        android:divider="@color/divider">
                    </ListView>
                </LinearLayout>
                <FrameLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@color/white">
                    <TextView
                        android:id="@+id/tv_prompt_available"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:gravity="center"
                        android:text="正在關閉WiFi"
                        android:textSize="14sp"
                        android:textColor="@color/subtitle"/>
                    <LinearLayout
                        android:id="@+id/ll_listavailable"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:background="@color/white">
                        <TextView
                            android:id="@+id/tv_btavailable"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:gravity="center"
                            android:text="@string/bluetooth_preference_found_devices"
                            android:textSize="14sp"
                            android:textColor="@color/theme_green"/>
                        <ListView
                            android:id="@+id/list_btavailable"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:listSelector="@drawable/item_selector"
                            android:dividerHeight="1dp"
                            android:divider="@color/divider">
                        </ListView>
                    </LinearLayout>
                </FrameLayout>
                <TextView
                    android:id="@+id/tv_visible_prompt"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textSize="14sp"
                    android:textColor="@color/subtitle"/>
            </LinearLayout>
        </FrameLayout>
    </LinearLayout>
</LinearLayout>