package com.bbpos.wiseapp.websocket;

import android.app.Dialog;
import android.content.Context;
import android.content.Intent;
import android.os.Build;
import android.os.Handler;
import android.os.Looper;
import android.text.TextPaint;
import android.text.TextUtils;
import android.view.View;
import android.view.WindowManager;
import android.widget.ImageView;
import android.widget.TextView;

import com.bbpos.BaseWebSocketHandlerCenter;
import com.bbpos.wiseapp.ZipJava;
import com.bbpos.wiseapp.logger.BBLog;
import com.bbpos.wiseapp.service.R;
import com.bbpos.wiseapp.service.appdata.db.TransRecordDBHelper;
import com.bbpos.wiseapp.service.common.RequestPermission;
import com.bbpos.wiseapp.service.common.UsualData;
import com.bbpos.wiseapp.settings.utils.APNManager;
import com.bbpos.wiseapp.settings.utils.WirelessUtil;
import com.bbpos.wiseapp.system.api.DeviceInfoApi;
import com.bbpos.wiseapp.system.api.SystemApi;
import com.bbpos.wiseapp.tms.location.GpsLocationManager;
import com.bbpos.wiseapp.tms.service.WSTaskUpdateParamService;
import com.bbpos.wiseapp.tms.utils.BroadcastActions;
import com.bbpos.wiseapp.tms.utils.Constants;
import com.bbpos.wiseapp.tms.utils.ContextUtil;
import com.bbpos.wiseapp.tms.utils.DateTimeUtils;
import com.bbpos.wiseapp.tms.utils.Helpers;
import com.bbpos.wiseapp.tms.utils.ParameterName;
import com.bbpos.wiseapp.tms.utils.WebSocketTaskListManager;
import com.bbpos.wiseapp.utils.Base64Utils;
import com.bbpos.wiseapp.utils.FileUtils;
import com.bbpos.wiseapp.utils.RSAUtils;
import com.bbpos.wiseapp.utils.SharedPreferencesUtils;
import com.bbpos.wiseapp.websocket.handler.ApnHandler;
import com.bbpos.wiseapp.websocket.handler.GeoProfileHandler;
import com.bbpos.wiseapp.websocket.handler.InAppVarHandler;
import com.bbpos.wiseapp.websocket.handler.RulebasedHandler;
import com.bbpos.wiseapp.websocket.handler.RulebasedListHandler;
import com.bbpos.wiseapp.websocket.handler.WebSocketServiceHandler;
import com.bbpos.wiseapp.websocket.handler.WebSocketTaskHandler;
import com.bbpos.wiseapp.websocket.handler.CommandHandler;
import com.bbpos.wiseapp.websocket.handler.ServiceResponeHandler;
import com.bbpos.wiseapp.websocket.handler.WifiProfileHandler;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.File;

public class WebSocketCenter {
    public static final String TAG = "WebSocketCenter";
    private static Context mContext;
    private static WebSocketManager webSocketManager;
    private static OnMessageListener onMessageListener = new OnMessageListener();
    private static CommandHandler mCommandHandler;
    private static ServiceResponeHandler mServiceResponeHandler;
	private static RulebasedHandler mRulebasedHandler;
    private static RulebasedListHandler mRulebasedListHandler;
    private static WebSocketTaskHandler mWebSocketTaskHandler;
    private static WebSocketServiceHandler mWebSocketServiceHandler;
    private static InAppVarHandler mInAppVarHandler;
    private static WifiProfileHandler mWifiProfileHandler;
    private static ApnHandler mApnHandler;
    private static GeoProfileHandler mGeoProfileHandler;
    public static boolean isWebSocketConnected = false;

    private WebSocketCenter(Context context) {
    }

    public static void init(Context context, BaseWebSocketHandlerCenter handlerCenter) {
        mContext = context;
        webSocketManager = WebSocketManager.getInstance(context, onMessageListener,handlerCenter);
//        webSocketManager = WebSocketManager.getInstance(context, onMessageListener);
        mCommandHandler = new CommandHandler(context, webSocketManager);
        mServiceResponeHandler = new ServiceResponeHandler(context, webSocketManager);
		mRulebasedHandler = new RulebasedHandler(context, webSocketManager);
        mRulebasedListHandler = new RulebasedListHandler(context, webSocketManager);
        mWebSocketTaskHandler = new WebSocketTaskHandler(context, webSocketManager);
        mWebSocketServiceHandler = new WebSocketServiceHandler(context, webSocketManager);
        mInAppVarHandler = new InAppVarHandler(context, webSocketManager);
        mWifiProfileHandler = new WifiProfileHandler(context,webSocketManager);
        mGeoProfileHandler = new GeoProfileHandler(context,webSocketManager);
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            mApnHandler = new ApnHandler(context);
        }
    }

    public static void sendMessage(String reqMsg, boolean needEncrypt){
        if (isWebSocketConnected) {
            sendMessage(reqMsg, "", "", false);
        }
    }

    public static void sendMessage(JSONObject reqMsg, boolean needEncrypt){
        if (reqMsg==null || !isWebSocketConnected) return;
        String reqId = reqMsg.optString("request_id");
        String tranCode = reqMsg.optString("tranCode");
        sendMessage(reqMsg.toString(),tranCode,reqId,false);
    }

    /**
     * @param reqMsg
     * @param tranCode
     * @param reqId
     */
    public static void sendMessage(String reqMsg,String tranCode,String reqId,boolean needEncrypt){
        if (webSocketManager != null) {
            //需要关注S0000响应报文,在ServiceReplayHandler中注册
//            if (!TextUtils.isEmpty(tranCode) && !TextUtils.isEmpty(reqId)) {
//                mServiceResponeHandler.registerAction(tranCode, reqId);
//            }
            BBLog.e(WebSocketSender.TAG, "WebSocket: sendMessage：" + reqMsg);
            try {
                if (needEncrypt) {

                    if (Constants.M_WEBSOCKET_MSGVER==2 || Constants.M_WEBSOCKET_MSGVER==3) {
                        if (BaseWebSocketHandlerCenter.m_need_rsa) {
                            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                                webSocketManager.sendText(new String(ZipJava.compress(RSAUtils.encryptByPublicKey(reqMsg.getBytes(), BaseWebSocketHandlerCenter.m_server_public_key)), "ISO-8859-1"));
                            }
                        } else {
                            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                                webSocketManager.sendText(new String(ZipJava.compress(reqMsg.getBytes()), "ISO-8859-1"));
                            }
                        }
                    } else {
                        if (BaseWebSocketHandlerCenter.m_need_rsa) {
                            webSocketManager.sendText(Base64Utils.encode(RSAUtils.encryptByPublicKey(reqMsg.getBytes(), BaseWebSocketHandlerCenter.m_server_public_key)));
                        } else {
                            webSocketManager.sendText(reqMsg);
                        }
                    }

                } else {
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                        webSocketManager.sendText(new String(ZipJava.compress(reqMsg.getBytes()), "ISO-8859-1"));
//                        webSocketManager.sendText(reqMsg);
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    public static void disconnect(){
        if (webSocketManager != null) {
            try {
                webSocketManager.disconnect();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    public static class OnMessageListener implements WebSocketManager.onMessageRecvListener {
        private boolean commandWhetherExecute = true;

        private void setCommandWhetherExecute(boolean command_whether_execute) {
            commandWhetherExecute = command_whether_execute;
        }

        private boolean getCommandWhetherExecute() {
            return commandWhetherExecute;
        }

        private void analysisCommandWhetherExecute(String tranCode, JSONObject jsonObject) {
            //默认都为true，仅针对ST001/ST003/ST005里含有"installBy"字段且其值为"1"且当前网络为移动网络时才置为false。
            setCommandWhetherExecute(true);
            try {
                if (!("ST001".equals(tranCode) || "ST003".equals(tranCode) || "ST005".equals(tranCode))) {
                    return;
                }
                String listParameterName = null;
                if ("ST001".equals(tranCode)) {
                    listParameterName = ParameterName.taskList;
                } else if ("ST003".equals(tranCode)) {
                    listParameterName = ParameterName.serviceList;
                } else if ("ST005".equals(tranCode)) {
                    listParameterName = ParameterName.ruleList;
                }
                if (jsonObject.has(ParameterName.DATA)) {
                    JSONObject dataJsonObject = jsonObject.getJSONObject(ParameterName.DATA);
                    if (dataJsonObject.has(listParameterName)) {
                        JSONArray listJSONArray = dataJsonObject.getJSONArray(listParameterName);
                        String installBy = null;
                        for (int i = 0; i < listJSONArray.length(); i++) {
                            JSONObject listJsonObject = (JSONObject) listJSONArray.get(i);
                            if (listJsonObject.has(ParameterName.installBy)) {
                                installBy = listJsonObject.getString(ParameterName.installBy);
                            if ("1".equals(installBy) && Helpers.isMobile(mContext)) {
                                    setCommandWhetherExecute(false);
                                    BBLog.w(BBLog.TAG, "检测到指令设置了install by wifi only，但当前网络又属于移动网络，不执行指令" + tranCode + "。");
                                    String dialogSwitch = SharedPreferencesUtils.getSharePreferencesValue(UsualData.SHARED_PREFERENCES_DIALOG_SWITCH, "");
                                    if (!TextUtils.isEmpty(dialogSwitch) && "1".equals(dialogSwitch) && !Constants.IF_DOWNLOAD_WIFI_ONLY_AFTER_DIALOG) {
                                        BBLog.w(BBLog.TAG, "检测dialogSwitch值为1，且目前无提示框，可以显示下载提示框。");
                                        showPromptMessageDialog(mContext);
                                    }
                                }
                            }
                        }
                    }
                }
            } catch (JSONException e) {
                e.printStackTrace();
            }
            BBLog.w(BBLog.TAG, "Analysis Command Whether Execute结束...");
        }

        private void showPromptMessageDialog(Context context) {
            new Handler(Looper.getMainLooper()).post(new Runnable() {
                @Override
                public void run() {
                    final Dialog dialog = new Dialog(context, R.style.dialog_style_ex);
                    dialog.setContentView(R.layout.dialog_download_wifi_only);
                    ImageView imageView = (ImageView) dialog.findViewById(R.id.iv_image);
                    imageView.setBackground(context.getDrawable(R.drawable.download));
                    TextView tv_title = (TextView) dialog.findViewById(R.id.tv_title);
                    tv_title.setText(context.getString(R.string.download_using_wifi_only));
                    TextPaint tp = tv_title.getPaint();
                    tp.setFakeBoldText(true);
                    TextView tv_content = (TextView) dialog.findViewById(R.id.tv_content);
                    tv_content.setText(context.getString(R.string.download_prompt));
                    dialog.setCanceledOnTouchOutside(false);
                    TextView tv_cancel = (TextView) dialog.findViewById(R.id.tv_cancel);
                    tv_cancel.setVisibility(View.GONE);
                    TextView tv_install = (TextView) dialog.findViewById(R.id.tv_install);
                    tv_install.setText(R.string.confirm);
                    tv_install.setOnClickListener(new View.OnClickListener() {
                        @Override
                        public void onClick(View v) {
                            dialog.dismiss();
                            Constants.IF_DOWNLOAD_WIFI_ONLY_AFTER_DIALOG = false;
                        }
                    });
                    dialog.getWindow().setType((WindowManager.LayoutParams.TYPE_SYSTEM_ALERT));
                    dialog.show();
                    Constants.IF_DOWNLOAD_WIFI_ONLY_AFTER_DIALOG = true;
                }
            });
        }

        @Override
        public void onMessageRecv(String response) {
            try {
                JSONObject jsonObject = new JSONObject(response);
                if (jsonObject.has("tranCode")) {
                    String tranCode = jsonObject.getString("tranCode");
                    analysisCommandWhetherExecute(tranCode, jsonObject);
                    if (!tranCode.startsWith("S0000") && getCommandWhetherExecute() &&
                            !(tranCode.startsWith("SC004")&&("CALLHB".equals(jsonObject.getJSONObject(ParameterName.DATA).getString(ParameterName.c_type))))) {
                        WebSocketSender.C0000_wsRespone(jsonObject.getString(ParameterName.request_id), jsonObject.getString(ParameterName.request_time), "0", null);
                    } else {
                        if (jsonObject.has("hellMsg")) {
                            BBLog.i(TAG, "WebSocket CONNECTED !");
                            isWebSocketConnected = true;
                            ContextUtil.showStatusBarIcon(ContextUtil.getInstance());
                            if (jsonObject.has(ParameterName.deviceStatus)) {
                                String device_status = jsonObject.getString(ParameterName.deviceStatus);
                                SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_UNBOX_STATUS, device_status);
                            }
                            if (jsonObject.has(ParameterName.storeGPS)) {
                                JSONObject storeGps = jsonObject.getJSONObject(ParameterName.storeGPS);
                                String store_id = storeGps.optString(ParameterName.storeId);
                                String storeNetAddr = storeGps.optString(ParameterName.storeNetAddr);
                                Constants.STORE_IP = storeNetAddr;
                                String latitude = storeGps.optString(ParameterName.latitude);
                                String longitide =  storeGps.optString(ParameterName.longitude);
                                SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_STORE_ID, store_id);
                                SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_STORE_IP, storeNetAddr);
                                SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_GPS_STORE_LATITUDE, latitude);
                                SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_GPS_STORE_LONGITUDE, longitide);
                            }
                            if (DeviceInfoApi.getIntance().isWisePosPro() && jsonObject.has(ParameterName.rebootTime)) {
                                String rebootTime = jsonObject.getString(ParameterName.rebootTime);
                                if (DateTimeUtils.isRebootTimeVaild(rebootTime)) {
                                    DateTimeUtils.setRebootAlarmEveryday(ContextUtil.getInstance(), rebootTime);
                                }
                            }
                            if (DeviceInfoApi.getIntance().isWisePosPro()) {
                                BBLog.i(TAG, "DEVICE_UNBOX_STATUS：" + SharedPreferencesUtils.getSharePreferencesValue(UsualData.SHARED_UNBOX_STATUS, ""));
                                if ("5".equals(SharedPreferencesUtils.getSharePreferencesValue(UsualData.SHARED_UNBOX_STATUS, ""))
                                        || "6".equals(SharedPreferencesUtils.getSharePreferencesValue(UsualData.SHARED_UNBOX_STATUS, ""))) {
                                    if ("5".equals(SharedPreferencesUtils.getSharePreferencesValue(UsualData.SHARED_UNBOX_STATUS, ""))) {
                                        FileUtils.delFolder(Constants.APK_FILE_PATH_UNBOX);     //刪除UNBOX APK安裝包路徑
                                    }
                                    WebSocketSender.C0201_DeviceStatusUpload("6", Constants.IS_IN_USE);
                                    SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_UNBOX_STATUS, "6");
                                }
                            }

                            if (Constants.M_GEOFENCE_STATUS== GpsLocationManager.LOCK_SCREEN || Constants.M_GEOFENCE_STATUS==GpsLocationManager.WIPE_DATA) {
                                if (!GpsLocationManager.B_GEO_OUT_OF_FENCE) {
                                    Intent intent_geo = new Intent(UsualData.ACTION_ENTER_GEOFENCE);
                                    ContextUtil.getInstance().sendBroadcast(intent_geo, RequestPermission.REQUEST_PERMISSION_MY_BROADCAST);
                                }
                            }

                            if (Constants.IS_FIRST_TIME_WEBSOCKET_CONNECTED) {
                                Constants.IS_FIRST_TIME_WEBSOCKET_CONNECTED = false;
                                WebSocketTaskListManager.checkServiceUpdateResult();
                                WebSocketTaskListManager.checkOSUpdateResult();
                            }
                            if (!Constants.IS_FIRST_TER_INFO_UPLOAD_COMPLETED) {
                                Helpers.sendBroad(mContext, BroadcastActions.TER_INFO_UPLOAD_BC);
                                Constants.IS_FIRST_TER_INFO_UPLOAD_COMPLETED = true;
                            }
                            if (!Constants.IS_FIRST_C090X_UPLOAD_COMPLETED) {
                                WebSocketSender.C0901_AppInfoUpload();
                                WebSocketSender.C0902_BatteryStatusUpload(Constants.BAT_LEVEL, Constants.BAT_HEALTH, Constants.BAT_TEMP, Constants.IS_BATTERY_CHARGING, Constants.IS_BATTERY_LOW, false, Constants.IS_OUT_OF_BATTERY);
                                WebSocketSender.C0903_DataInfoUpload();
                                WebSocketSender.C0904_NetworkStatusUpload();
                                //上送设备bbpos id，针对7MD是从WiseCube上获取的  2020年5月6日新增 fw、config、spfw
                                String bid = SharedPreferencesUtils.getSharePreferencesValue(UsualData.SHARED_PREFERENCES_DEVICE_BID_INFO, "");
                                String fullDeviceInfo = SharedPreferencesUtils.getSharePreferencesValue(UsualData.SHARED_PREFERENCES_DEVICE_FULL_INFO,"");
//                                if (!TextUtils.isEmpty(bid)) {
//									BBLog.e(BBLog.TAG, "WebSocketCenter: cache bid = "+ bid);
									try {
                                        if (!TextUtils.isEmpty(bid)) {
                                            JSONObject infoData = new JSONObject(bid);
                                            WebSocketSender.C0906_DeviceInfoUpload(infoData);
                                        }
                                        if (!TextUtils.isEmpty(fullDeviceInfo)) {
                                            WebSocketSender.C0908_DeviceFullInfoUpload(fullDeviceInfo);
                                        }
									} catch (Exception e) {
										//舊版本只保存spbid、wcbid,則再重新獲取一次
										if (DeviceInfoApi.getIntance().isWisePosPro() || DeviceInfoApi.getIntance().isWisePosTouch() || DeviceInfoApi.getIntance().isWisePosTouchPlus()) { //暫時先獲取7MD bid
											BBLog.e(BBLog.TAG, "WebSocketCenter: 存在舊版本bid數據，重新再獲取一次 ");
											Intent targetIntent = new Intent(ContextUtil.getInstance(), DeviceBidUploadService.class);
											targetIntent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
											ContextUtil.getInstance().startService(targetIntent);
										}
									}
//                                }
                                Constants.IS_FIRST_C090X_UPLOAD_COMPLETED = true;
                            } else {
                                if (Constants.M_GEOFENCE_STATUS>=GpsLocationManager.LOCK_SCREEN && Constants.M_GEOFENCE_STATUS<=GpsLocationManager.WIPE_DATA) {
                                    new Handler(Looper.getMainLooper()).postDelayed(new Runnable() {
                                        @Override
                                        public void run() {
                                            WebSocketSender.C0903_DataInfoUploadForGeo(System.currentTimeMillis());
                                        }
                                    }, 60000);
                                }
                                if (Constants.IS_C0904_UPLOAD_NEEDED) {
                                    Constants.IS_C0904_UPLOAD_NEEDED = false;
                                    WebSocketSender.C0904_NetworkStatusUpload();
                                }
                            }

                            //上送gds-tmt os 升级状态
                            try {
                                String  tempString = FileUtils.readFile(Constants.OTA_UPGRADE_STATUS_PATH);
                                if (!TextUtils.isEmpty(tempString)){
                                    JSONObject result = new JSONObject(tempString);
                                    if (result!=null && Constants.start_up_model.equals(result.optString("mode"))){
                                        result.put("buildNumber",DeviceInfoApi.getIntance().getCustomVersion());
                                        if (!result.has("romResult")){
                                            result.put("romResult",DeviceInfoApi.getIntance().getCustomVersion().equals(result.optString("targetVer")) ? "1" : "3");
                                        }
                                        result.remove("targetVer");
                                        WebSocketSender.C0203_GdsOsUpgradeStatusUpload(result);
                                        new File(Constants.OTA_UPGRADE_STATUS_PATH).delete();
                                    }
                                }
                            } catch (Exception e) {
                                e.printStackTrace();
                            }

                            Helpers.sendBroad(ContextUtil.getInstance(), UsualData.WSTASK_EXEC_BC);
                        }
                    }//com.android.settings/com.android.settings.SubSettings
                    if (tranCode.startsWith("SC")) {
                        mCommandHandler.dispatch(response);
                    } else if (tranCode.startsWith("ST001")) {
                        if (getCommandWhetherExecute()) {
                            mWebSocketTaskHandler.handleMsg(response);
                        }
                    } else if (tranCode.startsWith("ST002")) {
                        if ("5".equals(SharedPreferencesUtils.getSharePreferencesValue(UsualData.SHARED_UNBOX_STATUS, ""))
                         || "6".equals(SharedPreferencesUtils.getSharePreferencesValue(UsualData.SHARED_UNBOX_STATUS, ""))) {
                            mRulebasedHandler.handleMsg(response);
                        }
                    } else if (tranCode.startsWith("ST003")) {
                        if (getCommandWhetherExecute()) {
                            mWebSocketServiceHandler.handleMsg(response);
                        }
                    } else if (tranCode.startsWith("ST004")) {
                        mInAppVarHandler.handleMsg(response);
                    } else if (tranCode.startsWith("ST005")) {
//                        if ("5".equals(SharedPreferencesUtils.getSharePreferencesValue(UsualData.SHARED_UNBOX_STATUS, ""))
//                         || "6".equals(SharedPreferencesUtils.getSharePreferencesValue(UsualData.SHARED_UNBOX_STATUS, ""))) {
                        if (getCommandWhetherExecute()) {
                            mRulebasedListHandler.handleMsg(response);
                        }
//                        }
                    } else if (tranCode.startsWith("ST006")) {
                        mWifiProfileHandler.handleMsg(response);
                    } else if (tranCode.startsWith("ST007")) {
                        mGeoProfileHandler.handleMsg(response);
                    } else if (tranCode.startsWith("ST009")) {
                        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                            mApnHandler.handleMsg(response);
                        }
                    } else if (tranCode.startsWith("S0000")){
                        mServiceResponeHandler.dispatch(response);
                    } else if (tranCode.startsWith("SR003")) {
                        if (!WebSocketSender.bRequestCR003AppDownload) {
                            return;
                        }

                        WebSocketSender.bResponeSR003AppDownload = true;
                        WebSocketSender.bRequestCR003AppDownload = false;
                        Intent intent = new Intent(ContextUtil.getInstance().getApplicationContext(), InitialProcessService.class);
                        intent.putExtra("response", response);
                        ContextUtil.getInstance().getApplicationContext().startService(intent);
                    } else if (tranCode.startsWith("SR001")) {
                        try {
                            JSONArray storeList = null;
                            JSONObject responseJson = new JSONObject(response);
                            JSONObject responseData = Helpers.getDataFromResponse(responseJson);
                            if (responseData == null) {
                                BBLog.w(InitialProcessService.TAG, "responseData == null");
                                return;
                            }
                            if (responseData.has(ParameterName.storeList)) {
                                String pairedBy = responseData.optString(ParameterName.pairedBy);
                                storeList = responseData.getJSONArray(ParameterName.storeList);
                                if (storeList.length() > 0) {
                                    JSONObject store = storeList.getJSONObject(0);
                                    if ("1".equals(pairedBy)) {
                                        if (WebSocketSender.waiting_storeId) {
                                            Constants.STORE_ID = store.getString(ParameterName.storeId);
                                            WebSocketReceiver.mStoreId = Constants.STORE_ID;
                                            WebSocketReceiver.mIPAddress = WirelessUtil.getWifiIP(ContextUtil.getInstance());
                                            BBLog.w(InitialProcessService.TAG, "recv PrePair StoreId: go to CR002 check IP");
                                            WebSocketSender.CR002_responeStoreId(WebSocketReceiver.mStoreId, WirelessUtil.getWifiIP(ContextUtil.getInstance()), pairedBy);
                                            WebSocketReceiver.setTimerCount(1);
                                            WebSocketSender.bRequestCR002StoreIdConfirm = true;
                                            WebSocketSender.bRequestCR001StoreId = false;
                                            WebSocketSender.waiting_storeId = false;
                                        }
                                    } else {
                                        if (WebSocketSender.waiting_storeId) {
                                            Intent intent = new Intent(BroadcastActions.STORE_FOUND);
                                            intent.putExtra("storeList", storeList.toString());
                                            BBLog.w(InitialProcessService.TAG, "sendBroadcast: STORE_FOUND");
                                            mContext.sendBroadcast(intent, RequestPermission.REQUEST_PERMISSION_INTERNET);
                                            WebSocketSender.waiting_storeId = false;
                                        }
                                    }
                                } else {
                                    if (!"1".equals(pairedBy)) {
                                        if (WebSocketSender.waiting_storeId) {
                                            Intent intent = new Intent(BroadcastActions.STORE_NOT_FOUND);
                                            BBLog.w(InitialProcessService.TAG, "sendBroadcast STORE_NOT_FOUND  no Store ID found");
                                            mContext.sendBroadcast(intent, RequestPermission.REQUEST_PERMISSION_INTERNET);
                                            WebSocketSender.waiting_storeId = false;
                                        }
                                    }
                                }
                            } else {
                                String pairedBy = responseData.optString(ParameterName.pairedBy);
                                if (!"1".equals(pairedBy)) {
                                    if (WebSocketSender.waiting_storeId) {
                                        Intent intent = new Intent(BroadcastActions.STORE_NOT_FOUND);
                                        BBLog.w(InitialProcessService.TAG, "sendBroadcast STORE_NOT_FOUND  no Store ID found");
                                        mContext.sendBroadcast(intent, RequestPermission.REQUEST_PERMISSION_INTERNET);
                                        WebSocketSender.waiting_storeId = false;
                                    }
                                }
                            }
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    } else if (tranCode.startsWith("SR002")) {
                        try {
                            if (!WebSocketSender.bRequestCR002StoreIdConfirm) {
                                return;
                            }

//                            WebSocketReceiver.setTimerCount(1);
//                            WebSocketSender.bRequestCR003AppDownload = true;
                            WebSocketSender.bRequestCR002StoreIdConfirm = false;

                            JSONObject responseJson = new JSONObject(response);
                            JSONObject responseData = Helpers.getDataFromResponse(responseJson);
                            if (responseData == null) {
                                return;
                            }
                            String confirmMsg = "";
                            String storeId = responseData.getString(ParameterName.storeId);
                            String storeNetAddr = responseData.optString(ParameterName.storeNetAddr);
                            String confirmed = responseData.getString(ParameterName.confirmed);
                            if (responseData.has(ParameterName.confirmMsg)) {
                                confirmMsg = responseData.getString(ParameterName.confirmMsg);
                            }
                            //设置时区
                            if (responseData.has("androidTimeZone")){
                                if (!Helpers.isMobile(mContext)) {
                                    BBLog.w(InitialProcessService.TAG, "androidTimeZone: androidTimeZone" + responseData.optString("androidTimeZone"));
                                    SystemApi.setTimeZone(mContext, responseData.optString("androidTimeZone"));
                                }
                            }

                            if (responseData.has("storeLat") && responseData.has("storeLng")) {
                                SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_GPS_STORE_LATITUDE, responseData.getString("storeLat"));
                                SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_GPS_STORE_LONGITUDE, responseData.getString("storeLng"));
                            }

                            Intent intent = new Intent(BroadcastActions.STORE_LOOKUP);
                            if ("valid".equals(confirmed)) {
                                if (!TextUtils.isEmpty(Constants.STORE_ID)) {
                                    BBLog.w(InitialProcessService.TAG, "Constants.STORE_ID = " + Constants.STORE_ID);
                                    intent.putExtra("storeId", Constants.STORE_ID);
                                }
                                intent.putExtra("status", "pass");
                                mContext.sendBroadcast(intent, RequestPermission.REQUEST_PERMISSION_INTERNET);

                                SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_STORE_ID, storeId);
                                String ssid = WirelessUtil.getCurConnectSSID(ContextUtil.getInstance());
                                if (!TextUtils.isEmpty(ssid)) {
                                    Constants.STORE_SSID = ssid;
                                    SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_STORE_SSID, Constants.STORE_SSID);
                                }
                                if (!TextUtils.isEmpty(storeNetAddr)) {
                                    Constants.STORE_IP = storeNetAddr;
                                    SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_STORE_IP, Constants.STORE_IP);
                                }
                            }else if ("invalid".equals(confirmed)){
                                if (!TextUtils.isEmpty(Constants.STORE_ID)) {
                                    Intent intent_tmp = new Intent(BroadcastActions.STORE_NOT_FOUND);
                                    BBLog.w(InitialProcessService.TAG, "sendBroadcast STORE_NOT_FOUND  no Store ID found");
                                    mContext.sendBroadcast(intent_tmp, RequestPermission.REQUEST_PERMISSION_INTERNET);
                                }

                                intent.putExtra("status", "fail");
                                if ("101".equals(confirmMsg)) {
                                    intent.putExtra("code", confirmMsg);
                                    intent.putExtra("reason", WebSocketReceiver.mStoreId);
                                } else if ("102".equals(confirmMsg)) {
                                    intent.putExtra("code", confirmMsg);
                                    intent.putExtra("reason", WebSocketReceiver.mIPAddress);
                                }
                                BBLog.w(InitialProcessService.TAG, "sendBroadcast: STORE_LOOKUP");
                                mContext.sendBroadcast(intent, RequestPermission.REQUEST_PERMISSION_INTERNET);
                            }
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    } else if (tranCode.startsWith("SP001")) {
                        try {
                            JSONObject responseJson = new JSONObject(response);
                            JSONObject responseData = Helpers.getDataFromResponse(responseJson);
                            if (responseData == null) {
                                return;
                            }
                            TransRecordDBHelper.getInstance(mContext).deleteRecordData(responseData.optString(ParameterName.packageName),responseData.optString(ParameterName.paymentDataMD5));
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        @Override
        public void onDisconnected() {
            if (isWebSocketConnected) {
                BBLog.e(TAG, "WebSocket DISCONNECTED !");
                /*
                new Handler(Looper.getMainLooper()).post(new Runnable() {
                    @Override
                    public void run() {
                        Toast.makeText(ContextUtil.getInstance(), "DISCONNECTED !", Toast.LENGTH_LONG).show();
                    }
                });
                */
            }
            isWebSocketConnected = false;
            ContextUtil.hideStatusBarIcon(ContextUtil.getInstance());
        }
    }
}
