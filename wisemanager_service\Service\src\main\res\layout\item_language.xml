<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/ll_background"
    android:layout_width="match_parent"
    android:layout_height="72dp">

    <RelativeLayout
        android:id="@+id/rl_item"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <TextView
            android:id="@+id/tv_item"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentLeft="true"
            android:layout_centerVertical="true"
            android:layout_marginLeft="24dp"
            android:gravity="center"
            android:text="English"
            android:textSize="16sp"
            android:textColor="@color/title" />

        <ImageView
            android:id="@+id/iv_check"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:layout_marginRight="22dp"
            android:background="@drawable/check"/>
    </RelativeLayout>
</LinearLayout>
