package com.bbpos.wiseapp.tms.traffic;

import android.annotation.SuppressLint;
import android.app.IntentService;
import android.app.usage.UsageStats;
import android.app.usage.UsageStatsManager;
import android.content.Context;
import android.content.Intent;
import android.content.pm.ApplicationInfo;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import android.net.TrafficStats;
import android.util.Log;

import com.bbpos.wiseapp.logger.BBLog;
import com.bbpos.wiseapp.tms.traffic.model.TrafficData;
import com.bbpos.wiseapp.tms.traffic.utils.DataCollectDBHelper;
import com.bbpos.wiseapp.tms.utils.Constants;

import java.lang.reflect.Field;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 数据收集服务
 * 统计项 -流量 -使用时长
 * 触发时机 -间隔定时进行 -网络类型改变事件触发 -0点触发(需要特殊处理) -关机事件触发
 * */
@SuppressLint("SimpleDateFormat")
public class DataCollectService extends IntentService{
	private static final String TAG = DataCollectService.class.getName();
	public static final String EXE_TYPE = "exeType";
	/**标识0点触发执行，需要特殊处理，将流量数据归为前一日*/
	public static final String EXE_TYPE_CLEAR = "clear";
	public static final String EXE_TYPE_NORMAL = "normal";
	
	private DataCollectDBHelper dbHelper;
	public static final String DATA_COLLECT_SDF = "yyyyMMdd";
	private SimpleDateFormat sdf = new  SimpleDateFormat(DATA_COLLECT_SDF);
	/**触发类型*/
	private String exeType;

	public DataCollectService() {
		super(DataCollectService.class.getSimpleName());
		dbHelper = new DataCollectDBHelper(this);
	}

	@Override
	protected void onHandleIntent(Intent intent) {
		exeType = intent.getExtras().getString(EXE_TYPE);
		BBLog.v(BBLog.TAG, "start data collect,exeType:"+exeType);
//		usageStatCollect();   //使用情况统计
//		trafficDataCollect(); //使用流量统计
	}
	
	@SuppressLint({ "InlinedApi", "SimpleDateFormat", "NewApi" })
	private void usageStatCollect(){
		try {
			BBLog.v(BBLog.TAG, "usageStatCollect start");
			//使用次数与使用时长统计
			Date endDate = new Date();
			SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
			Date beginDate = sdf.parse(sdf.format(endDate));//从当天0点开始统计
			UsageStatsManager usm = (UsageStatsManager)getSystemService(Context.USAGE_STATS_SERVICE);
			List<UsageStats> queryUsageStats = usm.queryUsageStats(UsageStatsManager.INTERVAL_DAILY, beginDate.getTime(), endDate.getTime());
			Map<String,PackageInfo> pkgMaps = getPackageMap();

			for(UsageStats usageStat : queryUsageStats){
				String pkgName = usageStat.getPackageName();
				int launchCount = 0;
				long mTotalTimeInForeground = usageStat.getTotalTimeInForeground();

				Class c = usageStat.getClass();
				try {
					//mLaunchCount 我理解的它就是是app启动次数，这是被@hide修饰的成员变量。只能用反射得到它的值。
					// 但是有个现象是，启动次不准，从桌面点进去记数2，从后台进入记数1，时间间隔为一天，总的统计次数也不准确
					Field field = c.getDeclaredField("mLaunchCount");
					field.setAccessible(true);
					launchCount = field.getInt(usageStat);
				} catch (Exception e) {
					e.printStackTrace();
				}

				PackageInfo pkgInfo = pkgMaps.get(pkgName);
				int versionCode = pkgInfo.versionCode;
				String versionName = pkgInfo.versionName;
				dbHelper.updateUsageStat(sdf.format(new Date()),pkgName,versionCode,versionName,
						launchCount,mTotalTimeInForeground);
			}
			BBLog.v(BBLog.TAG, "usageStatCollect end");
		} catch (ParseException e) {
			e.printStackTrace();
		}
	}
	
	@SuppressLint("SimpleDateFormat")
	private void trafficDataCollect() {
		BBLog.v(BBLog.TAG, "trafficDataCollect start");
		// 获取当前网络类型与LAST_NET_TYPE比较
		ConnectivityManager cmanager = (ConnectivityManager) getSystemService(Context.CONNECTIVITY_SERVICE);
		NetworkInfo netInfo = cmanager.getActiveNetworkInfo();
		if(netInfo == null)
			return;
		String curNetType = netInfo.getTypeName();
		if (Constants.LAST_NET_TYPE.equals(""))
			Constants.LAST_NET_TYPE = curNetType;
		BBLog.v(BBLog.TAG, "last net type:"+ Constants.LAST_NET_TYPE);

		List<PackageInfo> packageInfos = getRequestInternetPkgs();
		int systemUid = 1000;
		for (PackageInfo packageInfo : packageInfos) {
			ApplicationInfo appInfo = packageInfo.applicationInfo;
			// 计算出到上一次统计使用的流量 now = current-last_time 
			// 系統uid特殊处理
			int apkUid = appInfo.uid;
			if(systemUid == apkUid)
				continue;
			long curRxBytes = TrafficStats.getUidRxBytes(apkUid);
			long curTxBytes = TrafficStats.getUidTxBytes(apkUid);

			if (curRxBytes == 0 && curTxBytes == 0)// 流量为0的信息不需要累计
			{
//				BBLog.v(BBLog.TAG, "traffic =0.pass:"+packageInfo.packageName);
				continue;
			}
//			BBLog.v(BBLog.TAG,"apkUid:"+apkUid+" "+packageInfo.packageName+" curRxBytes:"+curRxBytes+" curTxBytes:"+curTxBytes);
			long lastRxBytes = 0;
			long lastTxBytes = 0;
			TrafficData dataModel = dbHelper.getLasTimeTraffic(packageInfo.packageName, packageInfo.versionCode);
			if(dataModel != null){
				lastRxBytes = dataModel.rxBytes;
				lastTxBytes = dataModel.txBytes;
			}

			long nowRxBytes = curRxBytes - lastRxBytes;
			long nowTxBytes = curTxBytes - lastTxBytes;

			if (nowRxBytes <= 0 && nowTxBytes <= 0)// 流量为0的信息不需要累计
				continue;

			//0点统计时 统计出的流量归为前一天所有
			Date nowDate = new Date();
			if(EXE_TYPE_CLEAR.equals(exeType)){
				Calendar c = Calendar.getInstance();
				c.setTime(nowDate);
				c.add(Calendar.DATE, -1);
				nowDate = c.getTime();
			}
			
			dbHelper.appendTrafficData(sdf.format(nowDate), Constants.LAST_NET_TYPE, appInfo.packageName, appInfo.name,
					packageInfo.versionCode, packageInfo.versionName, nowRxBytes, nowTxBytes);
			dbHelper.recordLastTimeTrafficData(appInfo.packageName,packageInfo.versionCode,
					curRxBytes, curTxBytes);
		}
		Constants.LAST_NET_TYPE = curNetType;
		BBLog.v(BBLog.TAG, "current net type:"+ Constants.LAST_NET_TYPE);
	}
	
	private List<PackageInfo> getRequestInternetPkgs(){
		PackageManager pm = DataCollectService.this.getPackageManager();
		List<PackageInfo> allPackageInfos = pm.getInstalledPackages(0);
		List<PackageInfo> hasInternetPkgs = new ArrayList<PackageInfo>();
		for (PackageInfo packageInfo : allPackageInfos) {
			// 统计有使用网络权限app的流量信息
			if (PackageManager.PERMISSION_GRANTED == pm.checkPermission("android.permission.INTERNET",
					packageInfo.packageName)) {
				hasInternetPkgs.add(packageInfo);
//				BBLog.v(BBLog.TAG, "hasInternetPkg:" + packageInfo.packageName);
			}
		}
		return hasInternetPkgs;
	}
	
	@SuppressWarnings("unused")
	private Map<String,PackageInfo> getPackageMap(){
		PackageManager pm = DataCollectService.this.getPackageManager();
		List<PackageInfo> allPackageInfos = pm.getInstalledPackages(0);
		Map<String,PackageInfo> pkgsMap = new HashMap<String,PackageInfo>();
		for (PackageInfo packageInfo : allPackageInfos) {
			pkgsMap.put(packageInfo.packageName, packageInfo);
		}
		return pkgsMap;
	}
}
