package com.dspread.mdm.service.modules.logstream

import android.content.Context
import com.dspread.mdm.service.utils.log.Logger
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import org.json.JSONObject
import java.io.File
import java.text.SimpleDateFormat
import java.util.*


/**
 * 日志流WebSocket处理器
 * 处理平台下发的日志流相关命令
 */
class LogStreamWebSocketHandler(private val context: Context) {
    
    companion object {
        private const val TAG = "[LogStreamWebSocketHandler]"
    }
    
    private val logStreamManager = LogStreamManager(context)
    private val dateFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault())
    
    /**
     * 处理CALL_LOG_STREAM命令
     * 根据时间范围上传对应的日志包
     */
    fun handleCallLogStream(dataObject: JSONObject) {
        try {
            Logger.logStream("$TAG 处理CALL_LOG_STREAM请求")
            
            if (dataObject.has("param")) {
                val paramString = dataObject.optString("param")
                Logger.logStream("$TAG 日志流参数: $paramString")

                // 检查参数是否为空
                if (paramString.isNullOrEmpty()) {
                    Logger.logStreamW("$TAG param参数为空，跳过日志流处理")
                    return
                }

                try {
                    val paramJson = JSONObject(paramString)
                    val taskType = paramJson.optString("taskType", "")
                    val serviceId = paramJson.optString("serviceId", "")
                    val serviceName = paramJson.optString("serviceName", "")
                    val beginDate = paramJson.optString("beginDate", "")
                    val endDate = paramJson.optString("endDate", "")
                    val period = paramJson.optString("period", "1")
                    val url = paramJson.optString("url", "")
                    val taskId = paramJson.optString("taskId", "")
                    
                    Logger.logStream("$TAG 解析参数 - taskType: $taskType, serviceId: $serviceId")
                    Logger.logStream("$TAG 时间范围 - 开始: $beginDate, 结束: $endDate")
                    Logger.logStream("$TAG 上传URL: $url")
                    
                    // 异步处理日志上传
                    GlobalScope.launch {
                        handleLogStreamUpload(
                            beginDate = beginDate,
                            endDate = endDate,
                            period = period,
                            url = url,
                            taskId = taskId,
                            serviceId = serviceId
                        )
                    }
                    
                } catch (e: Exception) {
                    Logger.logStreamE("$TAG 解析日志流参数失败", e)
                }
            } else {
                Logger.logStreamW("$TAG CALL_LOG_STREAM请求缺少param参数")
            }
            
        } catch (e: Exception) {
            Logger.logStreamE("$TAG 处理CALL_LOG_STREAM请求失败", e)
        }
    }
    
    /**
     * 处理CLOSE_LOG_STREAM命令
     */
    fun handleCloseLogStream(dataObject: JSONObject) {
        try {
            Logger.logStream("$TAG 处理CLOSE_LOG_STREAM请求")
            
            if (dataObject.has("param")) {
                val paramString = dataObject.optString("param")
                Logger.logStream("$TAG 关闭日志流参数: $paramString")
                
                try {
                    val paramJson = JSONObject(paramString)
                    val taskType = paramJson.optString("taskType", "")
                    val serviceId = paramJson.optString("serviceId", "")
                    val command = paramJson.optString("command", "")
                    
                    Logger.logStream("$TAG 关闭日志流服务 - taskType: $taskType, serviceId: $serviceId")
                    
                    // 根据taskType处理不同的关闭逻辑
                    if ("06" == taskType) {
                        Logger.logStream("$TAG CLOSE_LOG_STREAM taskType=06，执行服务重置")
                        // TODO: 实现服务重置逻辑
                    }
                    
                    // 立即停止日志流服务，任何时候都以收到指令为主
                    GlobalScope.launch(Dispatchers.Main.immediate) {
                        try {
                            Logger.logStream("$TAG 收到CLOSE_LOG_STREAM命令，立即停止日志流")
                            val result = logStreamManager.stopStreaming()
                            if (result.isSuccess) {
                                Logger.logStream("$TAG 日志流服务停止成功")
                            } else {
                                Logger.logStreamE("$TAG 日志流服务停止失败", result.exceptionOrNull())
                            }
                        } catch (e: Exception) {
                            Logger.logStreamE("$TAG 停止日志流服务异常", e)
                        }
                    }
                    
                } catch (e: Exception) {
                    Logger.logStreamE("$TAG 解析关闭日志流参数失败", e)
                }
            } else {
                Logger.logStreamW("$TAG CLOSE_LOG_STREAM请求缺少param参数")
                // 直接立即停止日志流服务
                GlobalScope.launch(Dispatchers.Main.immediate) {
                    Logger.logStream("$TAG 无参数CLOSE_LOG_STREAM，立即停止日志流")
                    logStreamManager.stopStreaming()
                }
            }
            
        } catch (e: Exception) {
            Logger.logStreamE("$TAG 处理CLOSE_LOG_STREAM请求失败", e)
        }
    }
    
    /**
     * 处理日志流请求
     */
    private suspend fun processLogStreamRequest(
        beginDate: String,
        endDate: String,
        period: String,
        url: String,
        taskId: String,
        serviceId: String
    ) {
        try {
            Logger.logStream("$TAG 开始处理日志流请求")
            
            // 解析时间范围
            val beginTime = parseDateTime(beginDate)
            val endTime = parseDateTime(endDate)
            
            Logger.logStream("$TAG 解析时间范围 - 开始: ${beginTime?.time}, 结束: ${endTime?.time}")
            
            // 查找符合时间范围的日志文件
            val logFiles = findLogFilesInTimeRange(beginTime, endTime)
            Logger.logStream("$TAG 找到 ${logFiles.size} 个符合时间范围的日志文件")
            
            if (logFiles.isEmpty()) {
                Logger.logStreamW("$TAG 没有找到符合时间范围的日志文件")
                return
            }
            
            // 压缩并上传日志文件
            for (logFile in logFiles) {
                try {
                    Logger.logStream("$TAG 处理日志文件: ${logFile.name}")
                    
                    // 检查是否已经是压缩文件
                    val fileToUpload = if (logFile.name.endsWith(".gz")) {
                        logFile
                    } else {
                        // 压缩文件
                        compressLogFile(logFile)
                    }
                    
                    if (fileToUpload != null && fileToUpload.exists()) {
                        // 上传文件
                        val uploadResult = uploadLogFile(fileToUpload, url)
                        if (uploadResult) {
                            Logger.logStream("$TAG 日志文件上传成功: ${fileToUpload.name}")
                        } else {
                            Logger.logStreamE("$TAG 日志文件上传失败: ${fileToUpload.name}")
                        }
                    }
                    
                } catch (e: Exception) {
                    Logger.logStreamE("$TAG 处理日志文件失败: ${logFile.name}", e)
                }
            }
            
            Logger.logStream("$TAG 日志流请求处理完成")
            
        } catch (e: Exception) {
            Logger.logStreamE("$TAG 处理日志流请求失败", e)
        }
    }
    
    /**
     * 解析日期时间字符串
     */
    private fun parseDateTime(dateTimeStr: String): Date? {
        return try {
            if (dateTimeStr.isBlank() || dateTimeStr == "9999-12-31 23:59:59") {
                Date() // 使用当前时间
            } else {
                dateFormat.parse(dateTimeStr)
            }
        } catch (e: Exception) {
            Logger.logStreamE("$TAG 解析日期时间失败: $dateTimeStr", e)
            null
        }
    }
    
    /**
     * 查找时间范围内的日志文件
     */
    private fun findLogFilesInTimeRange(beginTime: Date?, endTime: Date?): List<File> {
        return try {
            val logCollector = LogCollector(context)
            val logDir = File(logCollector.getLogDirectoryPath())
            val uploadDir = File(logCollector.getUploadDirectoryPath())
            
            val allFiles = mutableListOf<File>()
            
            // 检查原始日志文件
            logDir.listFiles { file ->
                file.isFile && (file.name.endsWith(".log") || file.name.endsWith(".gz"))
            }?.let { allFiles.addAll(it) }
            
            // 检查上传目录中的压缩文件
            uploadDir.listFiles { file ->
                file.isFile && file.name.endsWith(".gz")
            }?.let { allFiles.addAll(it) }
            
            // 根据时间范围过滤文件
            allFiles.filter { file ->
                val fileTime = Date(file.lastModified())
                val afterBegin = beginTime?.let { fileTime.after(it) } ?: true
                val beforeEnd = endTime?.let { fileTime.before(it) } ?: true
                afterBegin && beforeEnd
            }.sortedBy { it.lastModified() }
            
        } catch (e: Exception) {
            Logger.logStreamE("$TAG 查找日志文件失败", e)
            emptyList()
        }
    }
    
    /**
     * 压缩日志文件
     */
    private suspend fun compressLogFile(logFile: File): File? {
        return try {
            val logProcessor = LogProcessor(context)
            val compressResult = logProcessor.compressFile(logFile, CompressionType.GZIP)
            
            if (compressResult.isSuccess) {
                val compressedInfo = compressResult.getOrNull()
                if (compressedInfo != null) {
                    File(compressedInfo.path)
                } else {
                    null
                }
            } else {
                Logger.logStreamE("$TAG 压缩文件失败: ${logFile.name}", compressResult.exceptionOrNull())
                null
            }
        } catch (e: Exception) {
            Logger.logStreamE("$TAG 压缩文件异常: ${logFile.name}", e)
            null
        }
    }
    
    /**
     * 上传日志文件
     */
    private suspend fun uploadLogFile(file: File, url: String): Boolean {
        return try {
            val logUploader = LogUploader(context)
            val uploadResult = logUploader.uploadFileToS3(file.absolutePath)
            uploadResult.isSuccess
        } catch (e: Exception) {
            Logger.logStreamE("$TAG 上传文件异常: ${file.name}", e)
            false
        }
    }

    /**
     * 处理日志流上传
     */
    private suspend fun handleLogStreamUpload(
        beginDate: String,
        endDate: String,
        period: String,
        url: String,
        taskId: String,
        serviceId: String
    ) {
        try {
            Logger.logStream("$TAG ========== 开始处理日志流上传 ==========")
            Logger.logStream("$TAG 原始参数 - beginDate: $beginDate, endDate: $endDate, period: $period")

            // 1. 处理时间范围
            val timeRange = processTimeRangeWithPeriod(beginDate, endDate, period)
            Logger.logStream("$TAG 处理后时间范围 - 开始: ${timeRange.first}, 结束: ${timeRange.second}")

            // 2. 判断是否需要上传（基于时间范围）
            val shouldUpload = shouldUploadBasedOnTimeRange(timeRange.first, timeRange.second)
            Logger.logStream("$TAG 时间范围判断结果 - 是否需要上传: $shouldUpload")

            if (!shouldUpload) {
                Logger.logStream("$TAG 当前时间不在上传范围内，跳过上传")
                return
            }

            // 3. 上传Recent日志
            Logger.logStream("$TAG 步骤1: 上传Recent日志")
            uploadRecentLog()

            // 4. 启动压缩包日志上传
            Logger.logStream("$TAG 步骤2: 启动压缩包日志上传")
            startLogServiceUpload()

            // 5. 发送C0901响应
            Logger.logStream("$TAG 步骤3: 发送C0901应用信息响应")
            sendLogStreamResponse(taskId, serviceId)

            Logger.logStream("$TAG ========== 日志流上传处理完成 ==========")

        } catch (e: Exception) {
            Logger.logStreamE("$TAG 处理日志流上传失败", e)
        }
    }

    /**
     * 上传Recent日志
     */
    private suspend fun uploadRecentLog() {
        try {
            Logger.logStream("$TAG 开始上传Recent日志")

            // 获取当前正在写入的日志文件
            val logCollector = LogCollector(context)
            val currentLogFile = logCollector.getCurrentLogFile()

            if (currentLogFile == null || !currentLogFile.exists()) {
                Logger.logStreamW("$TAG 当前日志文件不存在，无法生成Recent日志")
                return
            }

            Logger.logStream("$TAG 当前日志文件: ${currentLogFile.name}, 大小: ${currentLogFile.length()}")

            // 生成Recent日志文件（截取最后256KB）
            val recentFile = generateRecentLogFile(currentLogFile)
            if (recentFile == null) {
                Logger.logStreamE("$TAG 生成Recent日志文件失败")
                return
            }

            Logger.logStream("$TAG Recent日志文件生成成功: ${recentFile.name}, 大小: ${recentFile.length()}")

            // 压缩Recent日志文件
            val recentGzFile = compressRecentLogFile(recentFile)
            if (recentGzFile == null) {
                Logger.logStreamE("$TAG 压缩Recent日志文件失败")
                recentFile.delete()
                return
            }

            // 上传到S3 (路径: dev/sn/recent.gz)
            val uploadSuccess = uploadRecentFileToS3(recentGzFile)

            // 清理临时文件
            recentFile.delete()
            recentGzFile.delete()

            if (uploadSuccess) {
                Logger.logStream("$TAG Recent日志上传成功")
            } else {
                Logger.logStreamE("$TAG Recent日志上传失败")
            }

        } catch (e: Exception) {
            Logger.logStreamE("$TAG 上传Recent日志异常", e)
        }
    }

    /**
     * 启动压缩包日志上传
     */
    private suspend fun startLogServiceUpload() {
        try {
            Logger.logStream("$TAG 开始启动压缩包日志上传")

            // 触发日志压缩检查
            logStreamManager.triggerCompressionCheck()

            // 处理压缩队列中的文件上传
            processCompressedLogUpload()

            Logger.logStream("$TAG 压缩包日志上传启动完成")

        } catch (e: Exception) {
            Logger.logStreamE("$TAG 启动压缩包日志上传失败", e)
        }
    }

    /**
     * 生成Recent日志文件
     * 从当前日志文件截取最后256KB内容
     */
    private fun generateRecentLogFile(currentLogFile: File): File? {
        return try {
            val recentSize = 256 * 1024L // 256KB
            val fileSize = currentLogFile.length()

            Logger.logStream("$TAG 生成Recent日志 - 原文件大小: ${fileSize}B, Recent限制: ${recentSize}B")

            // 创建Recent日志文件
            val uploadDir = File(currentLogFile.parent, "upload")
            if (!uploadDir.exists()) {
                uploadDir.mkdirs()
                Logger.logStream("$TAG 创建upload目录: ${uploadDir.absolutePath}")
            }

            val recentFile = File(uploadDir, "recent.log")
            Logger.logStream("$TAG Recent文件路径: ${recentFile.absolutePath}")

            if (fileSize <= recentSize) {
                // 文件小于等于256KB，直接复制（有多少传多少）
                currentLogFile.copyTo(recentFile, overwrite = true)
                Logger.logStream("$TAG 文件≤256KB，直接复制全部内容: ${fileSize}B")
            } else {
                // 文件大于256KB，截取最后256KB
                val randomAccessFile = java.io.RandomAccessFile(currentLogFile, "r")
                val outputStream = java.io.FileOutputStream(recentFile)

                try {
                    // 定位到文件末尾前256KB的位置
                    val startPosition = fileSize - recentSize
                    randomAccessFile.seek(startPosition)

                    // 读取并写入最后256KB
                    val buffer = ByteArray(8192)
                    var totalRead = 0L

                    while (totalRead < recentSize) {
                        val bytesRead = randomAccessFile.read(buffer)
                        if (bytesRead == -1) break

                        val bytesToWrite = kotlin.math.min(bytesRead, (recentSize - totalRead).toInt())
                        outputStream.write(buffer, 0, bytesToWrite)
                        totalRead += bytesToWrite
                    }

                    Logger.logStream("$TAG 截取最后256KB完成，实际读取: ${totalRead}B")
                } finally {
                    randomAccessFile.close()
                    outputStream.close()
                }
            }

            // 验证生成的文件
            if (recentFile.exists() && recentFile.length() > 0) {
                Logger.logStream("$TAG Recent日志文件生成成功: ${recentFile.name}, 大小: ${recentFile.length()}B")
                recentFile
            } else {
                Logger.logStreamE("$TAG Recent日志文件生成失败或为空")
                null
            }

        } catch (e: Exception) {
            Logger.logStreamE("$TAG 生成Recent日志文件失败", e)
            null
        }
    }

    /**
     * 压缩Recent日志文件
     */
    private suspend fun compressRecentLogFile(recentFile: File): File? {
        return try {
            val uploadDir = File(recentFile.parent)
            val gzFile = File(uploadDir, "recent.gz")

            Logger.logStream("$TAG 开始压缩Recent日志: ${recentFile.name} -> ${gzFile.name}")
            Logger.logStream("$TAG 压缩目标路径: ${gzFile.absolutePath}")

            val logProcessor = LogProcessor(context)
            // 直接压缩到upload目录，使用指定文件名
            val compressResult = logProcessor.compressFile(recentFile, uploadDir, "recent.gz",
                CompressionType.GZIP
            )

            if (compressResult.isSuccess) {
                val compressedInfo = compressResult.getOrNull()
                if (compressedInfo != null) {
                    val actualGzFile = File(compressedInfo.path)
                    Logger.logStream("$TAG Recent日志压缩成功: ${actualGzFile.absolutePath}")
                    Logger.logStream("$TAG Recent日志压缩完成: ${actualGzFile.name}, 大小: ${actualGzFile.length()}")
                    actualGzFile
                } else {
                    Logger.logStreamE("$TAG Recent日志压缩结果为空")
                    null
                }
            } else {
                Logger.logStreamE("$TAG Recent日志压缩失败", compressResult.exceptionOrNull())
                null
            }

        } catch (e: Exception) {
            Logger.logStreamE("$TAG 压缩Recent日志文件异常", e)
            null
        }
    }

    /**
     * 上传Recent文件到S3
     * 路径: dev/sn/recent.gz
     */
    private suspend fun uploadRecentFileToS3(recentGzFile: File): Boolean {
        return try {
            Logger.logStream("$TAG 开始上传Recent文件到S3")

            val s3Uploader = S3LogUploader(context)
            val success = s3Uploader.uploadRecentFile(recentGzFile)

            if (success) {
                Logger.logStream("$TAG Recent文件S3上传成功")
            } else {
                Logger.logStreamE("$TAG Recent文件S3上传失败")
            }

            success

        } catch (e: Exception) {
            Logger.logStreamE("$TAG 上传Recent文件到S3异常", e)
            false
        }
    }

    /**
     * 处理压缩日志上传
     * 上传路径: dev/sn/日期/xxx.gz
     */
    private suspend fun processCompressedLogUpload() {
        try {
            Logger.logStream("$TAG 开始处理压缩日志上传")

            val logCollector = LogCollector(context)
            val uploadDir = File(logCollector.getLogDir(), "upload")

            if (!uploadDir.exists()) {
                Logger.logStreamW("$TAG 上传目录不存在")
                return
            }

            // 获取所有压缩文件
            val gzFiles = uploadDir.listFiles { file ->
                file.isFile && file.name.endsWith(".gz") && file.name != "recent.gz"
            } ?: emptyArray()

            Logger.logStream("$TAG 找到 ${gzFiles.size} 个压缩文件待上传")

            val s3Uploader = S3LogUploader(context)

            for (gzFile in gzFiles) {
                try {
                    Logger.logStream("$TAG 上传压缩文件: ${gzFile.name}")

                    val success = s3Uploader.uploadCompressedFile(gzFile)

                    if (success) {
                        Logger.logStream("$TAG 压缩文件上传成功，删除本地文件: ${gzFile.name}")
                        gzFile.delete()
                    } else {
                        Logger.logStreamE("$TAG 压缩文件上传失败: ${gzFile.name}")
                    }

                } catch (e: Exception) {
                    Logger.logStreamE("$TAG 上传压缩文件异常: ${gzFile.name}", e)
                }
            }

            Logger.logStream("$TAG 压缩日志上传处理完成")

        } catch (e: Exception) {
            Logger.logStreamE("$TAG 处理压缩日志上传异常", e)
        }
    }

    /**
     * 处理时间范围
     */
    private fun processTimeRangeWithPeriod(beginDate: String, endDate: String, period: String): Pair<String, String> {
        return try {
            val currentTime = System.currentTimeMillis()
            val dateFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault())

            Logger.logStream("$TAG 处理时间范围 - 当前时间: ${dateFormat.format(Date(currentTime))}")

            // 检查是否为默认的不明确时间范围
            val isDefaultTimeRange = ("1970-01-01 00:00:00" == beginDate && "9999-12-31 23:59:59" == endDate) ||
                                   ("9999-12-31 23:59:59" == endDate)

            if (isDefaultTimeRange) {
                Logger.logStream("$TAG 检测到不明确时间范围，使用period计算实际时间")

                // 计算开始时间（当前时间）
                val actualBeginDate = dateFormat.format(Date(currentTime))

                // 根据period计算结束时间
                val actualEndDate = try {
                    val periodHours = period.toIntOrNull() ?: 1
                    Logger.logStream("$TAG Period解析结果: ${periodHours}小时")

                    val endTime = currentTime + (periodHours * 60 * 60 * 1000L)
                    dateFormat.format(Date(endTime))
                } catch (e: Exception) {
                    Logger.logStreamE("$TAG Period解析失败，使用默认3天", e)
                    val endTime = currentTime + (3 * 24 * 60 * 60 * 1000L)
                    dateFormat.format(Date(endTime))
                }

                Logger.logStream("$TAG 计算后时间范围 - 开始: $actualBeginDate, 结束: $actualEndDate")
                Pair(actualBeginDate, actualEndDate)

            } else {
                Logger.logStream("$TAG 使用原始时间范围")
                Pair(beginDate, endDate)
            }

        } catch (e: Exception) {
            Logger.logStreamE("$TAG 处理时间范围异常，使用原始值", e)
            Pair(beginDate, endDate)
        }
    }

    /**
     * 判断是否需要上传（基于时间范围）
     */
    private fun shouldUploadBasedOnTimeRange(beginDate: String, endDate: String): Boolean {
        return try {
            val dateFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault())
            val currentTime = System.currentTimeMillis()

            val beginTime = dateFormat.parse(beginDate)?.time ?: 0L
            val endTime = dateFormat.parse(endDate)?.time ?: Long.MAX_VALUE

            Logger.logStream("$TAG 时间判断 - 当前: $currentTime, 开始: $beginTime, 结束: $endTime")

            // 检查当前时间是否在指定范围内
            val inTimeRange = currentTime >= beginTime && currentTime <= endTime

            Logger.logStream("$TAG 时间范围检查结果: $inTimeRange")

            // 特殊处理：如果结束时间是"9999-12-31 23:59:59"，表示长期有效
            if (endDate == "9999-12-31 23:59:59") {
                Logger.logStream("$TAG 检测到长期有效时间，检查是否在周期内")
                // 检查是否在24小时*3天的周期内
                val periodEnd = beginTime + (24 * 3 * 60 * 60 * 1000L)
                val inPeriod = currentTime >= beginTime && currentTime < periodEnd
                Logger.logStream("$TAG 周期检查结果: $inPeriod (周期结束: ${dateFormat.format(Date(periodEnd))})")
                return inPeriod
            }

            inTimeRange

        } catch (e: Exception) {
            Logger.logStreamE("$TAG 时间范围判断异常，默认允许上传", e)
            true
        }
    }

    /**
     * 发送日志流响应
     */
    private fun sendLogStreamResponse(taskId: String, serviceId: String) {
        try {
            Logger.logStream("$TAG 开始发送C0901日志流响应")

            // 获取WebSocket消息发送器
            val messageSender = com.dspread.mdm.service.network.websocket.message.WsMessageSender

            // 构建服务信息（包含日志流服务状态）
            val serviceInfo = buildLogStreamServiceInfo(taskId, serviceId)
            Logger.logStream("$TAG 构建服务信息完成: $serviceInfo")

            // 发送C0901应用信息上传消息
            messageSender.uploadAppInfoWithServiceInfo(serviceInfo)

            Logger.logStream("$TAG C0901日志流响应发送完成")

        } catch (e: Exception) {
            Logger.logStreamE("$TAG 发送日志流响应失败", e)
        }
    }

    /**
     * 构建日志流服务信息
     */
    private fun buildLogStreamServiceInfo(taskId: String, serviceId: String): JSONObject {
        return try {
            val currentTime = System.currentTimeMillis()
            val dateFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault())

            JSONObject().apply {
                put("taskId", taskId)
                put("serviceId", serviceId)
                put("serviceName", "Log Stream")
                put("taskType", "05")
                put("command", "C03")
                put("stateDesc", "IMPLEMENTED")  // 表示服务已实施
                put("beginDate", dateFormat.format(Date(currentTime)))
                put("endDate", "9999-12-31 23:59:59")  // 长期有效
                put("period", "1")
                put("request_id", "")
                put("request_time", "")

                Logger.logStream("$TAG 日志流服务信息构建完成")
            }

        } catch (e: Exception) {
            Logger.logStreamE("$TAG 构建日志流服务信息失败", e)
            JSONObject()
        }
    }
}
