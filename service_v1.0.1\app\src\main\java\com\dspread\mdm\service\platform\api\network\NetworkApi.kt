package com.dspread.mdm.service.platform.api.network

import android.content.Context
import android.net.ConnectivityManager
import android.net.NetworkCapabilities
import android.os.Build
import com.dspread.mdm.service.utils.log.Logger
import com.dspread.mdm.service.platform.api.model.SystemOperationResult
import java.net.InetAddress
import java.util.regex.Pattern

/**
 * 网络API
 * 提供网络连接状态检查、网络配置等功能
 *
 * 使用单例模式，避免重复实例化和资源浪费
 */
class NetworkApi(private val context: Context) {

    companion object {
        private const val TAG = "NetworkApi"

        // IP地址正则表达式
        private val IP_PATTERN = Pattern.compile(
            "^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$"
        )

        @Volatile
        private var INSTANCE: NetworkApi? = null

        /**
         * 获取单例实例
         */
        private fun getInstance(context: Context): NetworkApi {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: NetworkApi(context.applicationContext).also {
                    INSTANCE = it
                    Logger.platform("NetworkApi 单例实例已创建")
                }
            }
        }

        // ==================== 静态方法接口 ====================

        /**
         * 检查网络是否可用
         */
        fun isNetworkAvailable(context: Context): Boolean {
            return getInstance(context).isNetworkAvailable()
        }

        /**
         * 检查WiFi是否连接
         */
        fun isWifiConnected(context: Context): Boolean {
            return getInstance(context).isWifiConnected()
        }

        /**
         * 检查移动网络是否连接
         */
        fun isMobileConnected(context: Context): Boolean {
            return getInstance(context).isCellularConnected()
        }

        /**
         * 获取网络类型名称
         */
        fun getNetworkTypeName(context: Context): String {
            return getInstance(context).getNetworkTypeName()
        }

        /**
         * 验证IP地址格式
         */
        fun isValidIP(ip: String?): Boolean {
            return !ip.isNullOrEmpty() && IP_PATTERN.matcher(ip).matches()
        }

        /**
         * 检查网络连通性
         */
        fun checkNetworkConnectivity(context: Context, host: String, timeout: Int): SystemOperationResult {
            return getInstance(context).checkConnectivity(host, timeout)
        }

        /**
         * 检查主机是否可达
         */
        fun isHostReachable(context: Context, host: String, timeout: Int = 5000): Boolean {
            return getInstance(context).isHostReachable(host, timeout)
        }
    }

    private val connectivityManager by lazy {
        context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
    }

    /**
     * 检查网络是否可用
     */
    fun isNetworkAvailable(): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            val network = connectivityManager.activeNetwork ?: return false
            val networkCapabilities = connectivityManager.getNetworkCapabilities(network) ?: return false
            
            networkCapabilities.hasTransport(NetworkCapabilities.TRANSPORT_WIFI) ||
            networkCapabilities.hasTransport(NetworkCapabilities.TRANSPORT_CELLULAR) ||
            networkCapabilities.hasTransport(NetworkCapabilities.TRANSPORT_ETHERNET)
        } else {
            @Suppress("DEPRECATION")
            val networkInfo = connectivityManager.activeNetworkInfo
            networkInfo?.isConnected == true
        }
    }
    
    /**
     * 检查是否连接到WiFi
     */
    fun isWifiConnected(): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            val network = connectivityManager.activeNetwork ?: return false
            val networkCapabilities = connectivityManager.getNetworkCapabilities(network) ?: return false
            networkCapabilities.hasTransport(NetworkCapabilities.TRANSPORT_WIFI)
        } else {
            @Suppress("DEPRECATION")
            val networkInfo = connectivityManager.activeNetworkInfo
            networkInfo?.type == ConnectivityManager.TYPE_WIFI && networkInfo.isConnected
        }
    }
    
    /**
     * 检查是否连接到移动网络
     */
    fun isCellularConnected(): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            val network = connectivityManager.activeNetwork ?: return false
            val networkCapabilities = connectivityManager.getNetworkCapabilities(network) ?: return false
            networkCapabilities.hasTransport(NetworkCapabilities.TRANSPORT_CELLULAR)
        } else {
            @Suppress("DEPRECATION")
            val networkInfo = connectivityManager.activeNetworkInfo
            networkInfo?.type == ConnectivityManager.TYPE_MOBILE && networkInfo.isConnected
        }
    }
    
    /**
     * 获取网络类型名称
     */
    fun getNetworkTypeName(): String {
        return try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                val network = connectivityManager.activeNetwork ?: return "无网络"
                val networkCapabilities = connectivityManager.getNetworkCapabilities(network) ?: return "未知"
                
                when {
                    networkCapabilities.hasTransport(NetworkCapabilities.TRANSPORT_WIFI) -> "WiFi"
                    networkCapabilities.hasTransport(NetworkCapabilities.TRANSPORT_CELLULAR) -> "移动网络"
                    networkCapabilities.hasTransport(NetworkCapabilities.TRANSPORT_ETHERNET) -> "以太网"
                    else -> "其他"
                }
            } else {
                @Suppress("DEPRECATION")
                val networkInfo = connectivityManager.activeNetworkInfo
                networkInfo?.typeName ?: "无网络"
            }
        } catch (e: Exception) {
            Logger.platformE("获取网络类型失败", e)
            "未知"
        }
    }
    
    /**
     * 验证IP地址格式
     */
    fun isValidIpAddress(ip: String?): Boolean {
        return !ip.isNullOrEmpty() && IP_PATTERN.matcher(ip).matches()
    }

    /**
     * 检查两个IP是否在同一子网
     */
    fun isMachedIP(remoteIp: String?, storeIp: String?): Boolean {
        if (storeIp.isNullOrEmpty() || remoteIp.isNullOrEmpty()) {
            return false
        }

        return try {
            val remoteIpTrimmed = remoteIp.trim()
            val storeIpTrimmed = storeIp.trim()

            val remoteSubNet = remoteIpTrimmed.substring(0, remoteIpTrimmed.lastIndexOf("."))
            val storeSubNet = storeIpTrimmed.substring(0, storeIpTrimmed.lastIndexOf("."))

            remoteSubNet == storeSubNet
        } catch (e: Exception) {
            Logger.platformE("IP匹配检查失败: remote=$remoteIp, store=$storeIp", e)
            false
        }
    }
    
    /**
     * 检查网络连通性
     */
    fun checkConnectivity(host: String, timeout: Int): SystemOperationResult {
        return try {
            val isReachable = isHostReachable(host, timeout)
            if (isReachable) {
                SystemOperationResult.success("网络连通性检查成功")
            } else {
                SystemOperationResult.failure("主机不可达: $host")
            }
        } catch (e: Exception) {
            Logger.platformE("网络连通性检查失败: $host", e)
            SystemOperationResult.failure("网络连通性检查异常: ${e.message}")
        }
    }

    /**
     * 检查主机是否可达
     */
    fun isHostReachable(host: String, timeout: Int = 5000): Boolean {
        return try {
            val address = InetAddress.getByName(host)
            address.isReachable(timeout)
        } catch (e: Exception) {
            Logger.platformE("检查主机可达性失败: $host", e)
            false
        }
    }
    
    /**
     * 获取网络详细信息
     */
    fun getNetworkInfo(): Map<String, Any> {
        return try {
            mapOf(
                "isAvailable" to isNetworkAvailable(),
                "isWifiConnected" to isWifiConnected(),
                "isCellularConnected" to isCellularConnected(),
                "networkType" to getNetworkTypeName(),
                "timestamp" to System.currentTimeMillis()
            )
        } catch (e: Exception) {
            Logger.platformE("获取网络信息失败", e)
            mapOf(
                "error" to (e.message ?: "Unknown error"),
                "timestamp" to System.currentTimeMillis()
            )
        }
    }
}
