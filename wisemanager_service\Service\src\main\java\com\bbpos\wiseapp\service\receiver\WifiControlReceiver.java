package com.bbpos.wiseapp.service.receiver;

import static android.content.Context.WIFI_SERVICE;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.net.wifi.WifiManager;

import com.bbpos.wiseapp.logger.BBLog;
import com.bbpos.wiseapp.service.common.RequestPermission;
import com.bbpos.wiseapp.tms.utils.BroadcastActions;

public class WifiControlReceiver extends BroadcastReceiver {
    private static boolean b_close_wifi = false;

    @Override
    public void onReceive(final Context context, final Intent intent) {
        String action = intent.getAction();
        BBLog.i(BBLog.TAG, "WifiControlReceiver config: action = " + action);
        if (BroadcastActions.CLOSE_WIFI.equals(action)) {
            b_close_wifi = true;
            WifiManager mWifiManager = (WifiManager) context.getApplicationContext().getSystemService(WIFI_SERVICE);
            if (mWifiManager.isWifiEnabled()) {
                mWifiManager.setWifiEnabled(false);
            }
        } else if (BroadcastActions.OPEN_WIFI.equals(action)) {
            b_close_wifi = false;
            WifiManager mWifiManager = (WifiManager) context.getApplicationContext().getSystemService(WIFI_SERVICE);
            if (!mWifiManager.isWifiEnabled()) {
                mWifiManager.setWifiEnabled(true);
            }
        } else if (BroadcastActions.GET_WIFI_STATE.equals(action)) {
            Intent it = new Intent(BroadcastActions.WIFI_STATE);
            it.putExtra("wifi_state", b_close_wifi);
            context.sendBroadcast(it, RequestPermission.REQUEST_PERMISSION_BBPOS);
            BBLog.i(BBLog.TAG, "WifiControlReceiver sendBroadcast");
        } else if (WifiManager.WIFI_STATE_CHANGED_ACTION.equals(action)) {
            int wifi_state = intent.getIntExtra(WifiManager.EXTRA_WIFI_STATE, 0);
            BBLog.i(BBLog.TAG, "WifiControlReceiver wifi_state = " + wifi_state);
            if (wifi_state == WifiManager.WIFI_STATE_ENABLING) {
                if (b_close_wifi) {
                    BBLog.i(BBLog.TAG, "WifiControlReceiver wifi_state = " + wifi_state);
                    WifiManager mWifiManager = (WifiManager) context.getApplicationContext().getSystemService(WIFI_SERVICE);
                    mWifiManager.setWifiEnabled(false);
                }
            }
        }
    }
}
