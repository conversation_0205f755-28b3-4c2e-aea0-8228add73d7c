--------- beginning of system
--------- beginning of main
2025-08-18 16:14:05.850   961-1047  ActivityManager         pid-961                              E  Sending non-protected broadcast com.dspread.mdm.service.WSTASK_EXEC_BC from system uid 1000 pkg com.dspread.mdm.service (Ask Gemini)
                                                                                                    java.lang.Throwable
                                                                                                    	at com.android.server.am.ActivityManagerService.checkBroadcastFromSystem(ActivityManagerService.java:15082)
                                                                                                    	at com.android.server.am.ActivityManagerService.broadcastIntentLocked(ActivityManagerService.java:15719)
                                                                                                    	at com.android.server.am.ActivityManagerService.broadcastIntentInPackage(ActivityManagerService.java:15916)
                                                                                                    	at com.android.server.am.ActivityManagerService$LocalService.broadcastIntentInPackage(ActivityManagerService.java:18556)
                                                                                                    	at com.android.server.am.PendingIntentRecord.sendInner(PendingIntentRecord.java:432)
                                                                                                    	at com.android.server.am.PendingIntentRecord.sendWithResult(PendingIntentRecord.java:276)
                                                                                                    	at com.android.server.am.ActivityManagerService.sendIntentSender(ActivityManagerService.java:5633)
                                                                                                    	at android.app.PendingIntent.sendAndReturnResult(PendingIntent.java:896)
                                                                                                    	at android.app.PendingIntent.send(PendingIntent.java:878)
                                                                                                    	at com.android.server.AlarmManagerService$DeliveryTracker.deliverLocked(AlarmManagerService.java:4760)
                                                                                                    	at com.android.server.AlarmManagerService.deliverAlarmsLocked(AlarmManagerService.java:3899)
                                                                                                    	at com.android.server.AlarmManagerService$AlarmThread.run(AlarmManagerService.java:4160)
2025-08-18 16:15:05.869   961-1047  ActivityManager         pid-961                              E  Sending non-protected broadcast com.dspread.mdm.service.WSTASK_EXEC_BC from system uid 1000 pkg com.dspread.mdm.service (Ask Gemini)
                                                                                                    java.lang.Throwable
                                                                                                    	at com.android.server.am.ActivityManagerService.checkBroadcastFromSystem(ActivityManagerService.java:15082)
                                                                                                    	at com.android.server.am.ActivityManagerService.broadcastIntentLocked(ActivityManagerService.java:15719)
                                                                                                    	at com.android.server.am.ActivityManagerService.broadcastIntentInPackage(ActivityManagerService.java:15916)
                                                                                                    	at com.android.server.am.ActivityManagerService$LocalService.broadcastIntentInPackage(ActivityManagerService.java:18556)
                                                                                                    	at com.android.server.am.PendingIntentRecord.sendInner(PendingIntentRecord.java:432)
                                                                                                    	at com.android.server.am.PendingIntentRecord.sendWithResult(PendingIntentRecord.java:276)
                                                                                                    	at com.android.server.am.ActivityManagerService.sendIntentSender(ActivityManagerService.java:5633)
                                                                                                    	at android.app.PendingIntent.sendAndReturnResult(PendingIntent.java:896)
                                                                                                    	at android.app.PendingIntent.send(PendingIntent.java:878)
                                                                                                    	at com.android.server.AlarmManagerService$DeliveryTracker.deliverLocked(AlarmManagerService.java:4760)
                                                                                                    	at com.android.server.AlarmManagerService.deliverAlarmsLocked(AlarmManagerService.java:3899)
                                                                                                    	at com.android.server.AlarmManagerService$AlarmThread.run(AlarmManagerService.java:4160)
2025-08-18 16:15:24.223   961-1047  ActivityManager         system_server                        E  Sending non-protected broadcast com.dspread.mdm.service.SERVICE_GUARD_TIMER from system uid 1000 pkg com.dspread.mdm.service (Ask Gemini)
                                                                                                    java.lang.Throwable
                                                                                                    	at com.android.server.am.ActivityManagerService.checkBroadcastFromSystem(ActivityManagerService.java:15082)
                                                                                                    	at com.android.server.am.ActivityManagerService.broadcastIntentLocked(ActivityManagerService.java:15719)
                                                                                                    	at com.android.server.am.ActivityManagerService.broadcastIntentInPackage(ActivityManagerService.java:15916)
                                                                                                    	at com.android.server.am.ActivityManagerService$LocalService.broadcastIntentInPackage(ActivityManagerService.java:18556)
                                                                                                    	at com.android.server.am.PendingIntentRecord.sendInner(PendingIntentRecord.java:432)
                                                                                                    	at com.android.server.am.PendingIntentRecord.sendWithResult(PendingIntentRecord.java:276)
                                                                                                    	at com.android.server.am.ActivityManagerService.sendIntentSender(ActivityManagerService.java:5633)
                                                                                                    	at android.app.PendingIntent.sendAndReturnResult(PendingIntent.java:896)
                                                                                                    	at android.app.PendingIntent.send(PendingIntent.java:878)
                                                                                                    	at com.android.server.AlarmManagerService$DeliveryTracker.deliverLocked(AlarmManagerService.java:4760)
                                                                                                    	at com.android.server.AlarmManagerService.deliverAlarmsLocked(AlarmManagerService.java:3899)
                                                                                                    	at com.android.server.AlarmManagerService$AlarmThread.run(AlarmManagerService.java:4160)
2025-08-18 16:15:24.242  4087-4125  Common                  com.dspread.mdm.service              D  🔧 ServiceGuardEventHandler 开始执行保活动作
2025-08-18 16:15:24.247  4087-4125  Common                  com.dspread.mdm.service              I  ✅ ⏰ 设置服务守护定时器成功，下次执行: 120秒后 (2分钟)
2025-08-18 16:15:24.250  4087-4125  ContextImpl             com.dspread.mdm.service              W  Calling a method in the system process without a qualified user: android.app.ContextImpl.startService:1571 android.content.ContextWrapper.startService:669 com.dspread.mdm.service.broadcast.handlers.service.ServiceGuardEventHandler.startKeepAliveService:211 com.dspread.mdm.service.broadcast.handlers.service.ServiceGuardEventHandler.performKeepAliveActions:193 com.dspread.mdm.service.broadcast.handlers.service.ServiceGuardEventHandler.access$performKeepAliveActions:24 
2025-08-18 16:15:24.253  4087-4087  Service                 com.dspread.mdm.service              D  🔧 ServiceKeepAliveService 保活服务启动
2025-08-18 16:15:24.253  4087-4125  Common                  com.dspread.mdm.service              D  🔧 ServiceGuardEventHandler 启动保活服务
2025-08-18 16:15:24.254  4087-4087  Platform                com.dspread.mdm.service              D  🔧 WakeLockManager WakeLock不存在或未持有: ServiceKeepAlive
2025-08-18 16:15:24.258  4087-4087  Platform                com.dspread.mdm.service              D  🔧 WakeLockManager 获取WakeLock成功: ServiceKeepAlive (超时: 30000ms)
2025-08-18 16:15:24.261  4087-4125  Common                  com.dspread.mdm.service              D  🔧 ServiceGuardEventHandler 保活动作执行完成
2025-08-18 16:15:27.265  4087-4125  Common                  com.dspread.mdm.service              D  🔧 ServiceGuardEventHandler WakeLock已释放
2025-08-18 16:15:33.385  4087-4087  ActivityThread          com.dspread.mdm.service              W  handleWindowVisibility: no activity for token android.os.BinderProxy@bf2e84f
2025-08-18 16:15:33.490  4087-4087  PhoneWindow             com.dspread.mdm.service              V  DecorView setVisiblity: visibility = 4, Parent = null, this = DecorView@7d9d60d[]
2025-08-18 16:15:33.495  4087-4087  ViewRootIm...tActivity] com.dspread.mdm.service              D  hardware acceleration = true , fakeHwAccelerated = false, sRendererDisabled = false, forceHwAccelerated = false, sSystemRendererDisabled = false
2025-08-18 16:15:33.501  4087-4087  InputTransport          com.dspread.mdm.service              I  Create ARC handle: 0x7eeb8360
2025-08-18 16:15:33.502  4087-4087  PhoneWindow             com.dspread.mdm.service              V  DecorView setVisiblity: visibility = 0, Parent = android.view.ViewRootImpl@1930b10, this = DecorView@7d9d60d[TestActivity]
2025-08-18 16:15:33.515  4087-4125  Platform                com.dspread.mdm.service              W  ⚠️ SpVersionApi SP版本信息格式异常，未找到SP_VERSION标记
2025-08-18 16:15:33.517  4087-4125  Platform                com.dspread.mdm.service              D  🔧 SpVersionApi 从Settings获取SP版本: 588.30.241.59
2025-08-18 16:15:33.539  4087-4130  Surface                 com.dspread.mdm.service              D  Surface::connect(this=0x7f0e3000,api=1)
2025-08-18 16:15:33.542  4087-4130  Surface                 com.dspread.mdm.service              D  Surface::setBufferCount(this=0x7f0e3000,bufferCount=3)
2025-08-18 16:15:33.542  4087-4130  Surface                 com.dspread.mdm.service              D  Surface::allocateBuffers(this=0x7f0e3000)
2025-08-18 16:15:33.599  4087-4087  WebSocket               com.dspread.mdm.service              I  🔧 C0905 主动式上送: manual_trigger (主动: 11)
2025-08-18 16:15:33.604  4087-4087  WebSocket               com.dspread.mdm.service              I  🔧 首次上送，上送当日数据
2025-08-18 16:15:33.617  4087-4087  WebSocket               com.dspread.mdm.service              I  🔧 消息已缓存: C0905 (缓存数量: 6)
2025-08-18 16:15:33.619  4087-4087  WebSocket               com.dspread.mdm.service              I  🔧 ✅ C0905 网络流量统计上送成功 (1) - 总流量: 9.1MB - 触发: manual_trigger - 数据条数: 1
2025-08-18 16:15:33.620  4087-4087  Common                  com.dspread.mdm.service              D  🔧 刷新时自动上送流量统计
2025-08-18 16:15:53.321  4087-4087  Receiver                com.dspread.mdm.service              D  🔧 电池状态更新: 电池状态: 电量=41%, 温度=36.0°C, 健康=2, 充电=是, 低电量=否, 耗尽=否
2025-08-18 16:15:53.350  4087-4087  Receiver                com.dspread.mdm.service              D  🔧 电池状态更新: 电池状态: 电量=41%, 温度=36.1°C, 健康=2, 充电=是, 低电量=否, 耗尽=否
2025-08-18 16:15:54.261  4087-4087  Platform                com.dspread.mdm.service              D  🔧 WakeLockManager WakeLock不存在或未持有: ServiceKeepAlive
2025-08-18 16:15:54.262  4087-4087  Service                 com.dspread.mdm.service              D  🔧 ServiceKeepAliveService 保活任务完成，释放WakeLock
2025-08-18 16:16:05.889   961-1047  ActivityManager         system_server                        E  Sending non-protected broadcast com.dspread.mdm.service.WSTASK_EXEC_BC from system uid 1000 pkg com.dspread.mdm.service (Ask Gemini)
                                                                                                    java.lang.Throwable
                                                                                                    	at com.android.server.am.ActivityManagerService.checkBroadcastFromSystem(ActivityManagerService.java:15082)
                                                                                                    	at com.android.server.am.ActivityManagerService.broadcastIntentLocked(ActivityManagerService.java:15719)
                                                                                                    	at com.android.server.am.ActivityManagerService.broadcastIntentInPackage(ActivityManagerService.java:15916)
                                                                                                    	at com.android.server.am.ActivityManagerService$LocalService.broadcastIntentInPackage(ActivityManagerService.java:18556)
                                                                                                    	at com.android.server.am.PendingIntentRecord.sendInner(PendingIntentRecord.java:432)
                                                                                                    	at com.android.server.am.PendingIntentRecord.sendWithResult(PendingIntentRecord.java:276)
                                                                                                    	at com.android.server.am.ActivityManagerService.sendIntentSender(ActivityManagerService.java:5633)
                                                                                                    	at android.app.PendingIntent.sendAndReturnResult(PendingIntent.java:896)
                                                                                                    	at android.app.PendingIntent.send(PendingIntent.java:878)
                                                                                                    	at com.android.server.AlarmManagerService$DeliveryTracker.deliverLocked(AlarmManagerService.java:4760)
                                                                                                    	at com.android.server.AlarmManagerService.deliverAlarmsLocked(AlarmManagerService.java:3899)
                                                                                                    	at com.android.server.AlarmManagerService$AlarmThread.run(AlarmManagerService.java:4160)
2025-08-18 16:16:05.910  4087-4087  Common                  com.dspread.mdm.service              I  ✅ ⏰ 设置任务执行定时器成功，下次执行: 60秒后 (1分钟)
2025-08-18 16:16:11.466  4087-4087  Receiver                com.dspread.mdm.service              D  🔧 电池状态更新: 电池状态: 电量=41%, 温度=35.8°C, 健康=2, 充电=是, 低电量=否, 耗尽=否
2025-08-18 16:16:52.055  4087-4087  Receiver                com.dspread.mdm.service              D  🔧 电池状态更新: 电池状态: 电量=42%, 温度=35.6°C, 健康=2, 充电=是, 低电量=否, 耗尽=否
2025-08-18 16:17:05.757   961-1047  ActivityManager         system_server                        E  Sending non-protected broadcast com.dspread.mdm.service.POLL_TIMER_START from system uid 1000 pkg com.dspread.mdm.service (Ask Gemini)
                                                                                                    java.lang.Throwable
                                                                                                    	at com.android.server.am.ActivityManagerService.checkBroadcastFromSystem(ActivityManagerService.java:15082)
                                                                                                    	at com.android.server.am.ActivityManagerService.broadcastIntentLocked(ActivityManagerService.java:15719)
                                                                                                    	at com.android.server.am.ActivityManagerService.broadcastIntentInPackage(ActivityManagerService.java:15916)
                                                                                                    	at com.android.server.am.ActivityManagerService$LocalService.broadcastIntentInPackage(ActivityManagerService.java:18556)
                                                                                                    	at com.android.server.am.PendingIntentRecord.sendInner(PendingIntentRecord.java:432)
                                                                                                    	at com.android.server.am.PendingIntentRecord.sendWithResult(PendingIntentRecord.java:276)
                                                                                                    	at com.android.server.am.ActivityManagerService.sendIntentSender(ActivityManagerService.java:5633)
                                                                                                    	at android.app.PendingIntent.sendAndReturnResult(PendingIntent.java:896)
                                                                                                    	at android.app.PendingIntent.send(PendingIntent.java:878)
                                                                                                    	at com.android.server.AlarmManagerService$DeliveryTracker.deliverLocked(AlarmManagerService.java:4760)
                                                                                                    	at com.android.server.AlarmManagerService.deliverAlarmsLocked(AlarmManagerService.java:3899)
                                                                                                    	at com.android.server.AlarmManagerService$AlarmThread.run(AlarmManagerService.java:4160)
2025-08-18 16:17:05.774  4087-4087  Receiver                com.dspread.mdm.service              D  🔧 收到轮询定时器广播
2025-08-18 16:17:05.774  4087-4087  Receiver                com.dspread.mdm.service              D  🔧 WebSocket未连接，跳过轮询任务
2025-08-18 16:17:05.779  4087-4087  Common                  com.dspread.mdm.service              D  🔧 TimerConfig 使用Provisioning配置的心跳间隔: 300秒
2025-08-18 16:17:05.783  4087-4087  Common                  com.dspread.mdm.service              I  ✅ ⏰ 设置心跳定时器成功，下次执行: 300秒后 (5分钟)
2025-08-18 16:17:05.910   961-1047  ActivityManager         system_server                        E  Sending non-protected broadcast com.dspread.mdm.service.WSTASK_EXEC_BC from system uid 1000 pkg com.dspread.mdm.service (Ask Gemini)
                                                                                                    java.lang.Throwable
                                                                                                    	at com.android.server.am.ActivityManagerService.checkBroadcastFromSystem(ActivityManagerService.java:15082)
                                                                                                    	at com.android.server.am.ActivityManagerService.broadcastIntentLocked(ActivityManagerService.java:15719)
                                                                                                    	at com.android.server.am.ActivityManagerService.broadcastIntentInPackage(ActivityManagerService.java:15916)
                                                                                                    	at com.android.server.am.ActivityManagerService$LocalService.broadcastIntentInPackage(ActivityManagerService.java:18556)
                                                                                                    	at com.android.server.am.PendingIntentRecord.sendInner(PendingIntentRecord.java:432)
                                                                                                    	at com.android.server.am.PendingIntentRecord.sendWithResult(PendingIntentRecord.java:276)
                                                                                                    	at com.android.server.am.ActivityManagerService.sendIntentSender(ActivityManagerService.java:5633)
                                                                                                    	at android.app.PendingIntent.sendAndReturnResult(PendingIntent.java:896)
                                                                                                    	at android.app.PendingIntent.send(PendingIntent.java:878)
                                                                                                    	at com.android.server.AlarmManagerService$DeliveryTracker.deliverLocked(AlarmManagerService.java:4760)
                                                                                                    	at com.android.server.AlarmManagerService.deliverAlarmsLocked(AlarmManagerService.java:3899)
                                                                                                    	at com.android.server.AlarmManagerService$AlarmThread.run(AlarmManagerService.java:4160)
2025-08-18 16:17:05.926  4087-4087  Common                  com.dspread.mdm.service              I  ✅ ⏰ 设置任务执行定时器成功，下次执行: 60秒后 (1分钟)
2025-08-18 16:17:11.468  4087-4087  Receiver                com.dspread.mdm.service              D  🔧 电池状态更新: 电池状态: 电量=42%, 温度=35.4°C, 健康=2, 充电=是, 低电量=否, 耗尽=否
2025-08-18 16:17:24.247   961-1047  ActivityManager         system_server                        E  Sending non-protected broadcast com.dspread.mdm.service.SERVICE_GUARD_TIMER from system uid 1000 pkg com.dspread.mdm.service (Ask Gemini)
                                                                                                    java.lang.Throwable
                                                                                                    	at com.android.server.am.ActivityManagerService.checkBroadcastFromSystem(ActivityManagerService.java:15082)
                                                                                                    	at com.android.server.am.ActivityManagerService.broadcastIntentLocked(ActivityManagerService.java:15719)
                                                                                                    	at com.android.server.am.ActivityManagerService.broadcastIntentInPackage(ActivityManagerService.java:15916)
                                                                                                    	at com.android.server.am.ActivityManagerService$LocalService.broadcastIntentInPackage(ActivityManagerService.java:18556)
                                                                                                    	at com.android.server.am.PendingIntentRecord.sendInner(PendingIntentRecord.java:432)
                                                                                                    	at com.android.server.am.PendingIntentRecord.sendWithResult(PendingIntentRecord.java:276)
                                                                                                    	at com.android.server.am.ActivityManagerService.sendIntentSender(ActivityManagerService.java:5633)
                                                                                                    	at android.app.PendingIntent.sendAndReturnResult(PendingIntent.java:896)
                                                                                                    	at android.app.PendingIntent.send(PendingIntent.java:878)
                                                                                                    	at com.android.server.AlarmManagerService$DeliveryTracker.deliverLocked(AlarmManagerService.java:4760)
                                                                                                    	at com.android.server.AlarmManagerService.deliverAlarmsLocked(AlarmManagerService.java:3899)
                                                                                                    	at com.android.server.AlarmManagerService$AlarmThread.run(AlarmManagerService.java:4160)
2025-08-18 16:17:24.265  4087-4125  Common                  com.dspread.mdm.service              D  🔧 ServiceGuardEventHandler 开始执行保活动作
2025-08-18 16:17:24.271  4087-4125  Common                  com.dspread.mdm.service              I  ✅ ⏰ 设置服务守护定时器成功，下次执行: 120秒后 (2分钟)
2025-08-18 16:17:24.272  4087-4125  ContextImpl             com.dspread.mdm.service              W  Calling a method in the system process without a qualified user: android.app.ContextImpl.startService:1571 android.content.ContextWrapper.startService:669 com.dspread.mdm.service.broadcast.handlers.service.ServiceGuardEventHandler.startKeepAliveService:211 com.dspread.mdm.service.broadcast.handlers.service.ServiceGuardEventHandler.performKeepAliveActions:193 com.dspread.mdm.service.broadcast.handlers.service.ServiceGuardEventHandler.access$performKeepAliveActions:24 
2025-08-18 16:17:24.276  4087-4125  Common                  com.dspread.mdm.service              D  🔧 ServiceGuardEventHandler 启动保活服务
2025-08-18 16:17:24.277  4087-4087  Service                 com.dspread.mdm.service              D  🔧 ServiceKeepAliveService 保活服务启动
2025-08-18 16:17:24.277  4087-4087  Platform                com.dspread.mdm.service              D  🔧 WakeLockManager WakeLock不存在或未持有: ServiceKeepAlive
2025-08-18 16:17:24.282  4087-4125  Common                  com.dspread.mdm.service              D  🔧 ServiceGuardEventHandler 保活动作执行完成
2025-08-18 16:17:24.282  4087-4087  Platform                com.dspread.mdm.service              D  🔧 WakeLockManager 获取WakeLock成功: ServiceKeepAlive (超时: 30000ms)
2025-08-18 16:17:27.286  4087-4125  Common                  com.dspread.mdm.service              D  🔧 ServiceGuardEventHandler WakeLock已释放
2025-08-18 16:17:31.832  4087-4087  Receiver                com.dspread.mdm.service              D  🔧 电池状态更新: 电池状态: 电量=42%, 温度=35.4°C, 健康=2, 充电=是, 低电量=否, 耗尽=否
2025-08-18 16:17:47.251  4087-4087  Receiver                com.dspread.mdm.service              D  🔧 网络状态变化: 连接=false, 类型=NONE
2025-08-18 16:17:47.252  4087-4087  Receiver                com.dspread.mdm.service              D  🔧 网络不可用，WebSocket连接可能受影响
2025-08-18 16:17:47.252  4087-4087  Receiver                com.dspread.mdm.service              D  🔧 ServiceManagementEventHandler 处理网络变化事件
2025-08-18 16:17:47.254  4087-4087  Common                  com.dspread.mdm.service              D  🔧 ServiceStartupManager 服务正在运行，无需启动: NETWORK_CHANGE
2025-08-18 16:17:47.254  4087-4087  Receiver                com.dspread.mdm.service              D  🔧 ServiceManagementEventHandler 网络变化后服务检查完成
2025-08-18 16:17:47.258  4087-4087  Provisioning            com.dspread.mdm.service              D  🔧 ProvisioningEventHandler 网络状态变化
2025-08-18 16:17:47.271  4087-4087  Provisioning            com.dspread.mdm.service              D  🔧 ProvisioningEventHandler 忽略初始启动时的网络连接事件
2025-08-18 16:17:51.421  4087-4087  Receiver                com.dspread.mdm.service              D  🔧 网络状态变化: 连接=true, 类型=WIFI
2025-08-18 16:17:51.422  4087-4087  Receiver                com.dspread.mdm.service              D  🔧 网络恢复，尝试重新连接WebSocket
2025-08-18 16:17:51.422  4087-4087  Receiver                com.dspread.mdm.service              D  🔧 ServiceManagementEventHandler 处理网络变化事件
2025-08-18 16:17:51.424  4087-4087  Common                  com.dspread.mdm.service              D  🔧 ServiceStartupManager 服务正在运行，无需启动: NETWORK_CHANGE
2025-08-18 16:17:51.425  4087-4087  Receiver                com.dspread.mdm.service              D  🔧 ServiceManagementEventHandler 网络变化后服务检查完成
2025-08-18 16:17:51.425  4087-4087  Provisioning            com.dspread.mdm.service              D  🔧 ProvisioningEventHandler 网络状态变化
2025-08-18 16:17:51.425  4087-4087  Provisioning            com.dspread.mdm.service              D  🔧 ProvisioningEventHandler 忽略初始启动时的网络连接事件
2025-08-18 16:17:54.285  4087-4087  Platform                com.dspread.mdm.service              D  🔧 WakeLockManager WakeLock不存在或未持有: ServiceKeepAlive
2025-08-18 16:17:54.286  4087-4087  Service                 com.dspread.mdm.service              D  🔧 ServiceKeepAliveService 保活任务完成，释放WakeLock
2025-08-18 16:18:05.925   961-1047  ActivityManager         system_server                        E  Sending non-protected broadcast com.dspread.mdm.service.WSTASK_EXEC_BC from system uid 1000 pkg com.dspread.mdm.service (Ask Gemini)
                                                                                                    java.lang.Throwable
                                                                                                    	at com.android.server.am.ActivityManagerService.checkBroadcastFromSystem(ActivityManagerService.java:15082)
                                                                                                    	at com.android.server.am.ActivityManagerService.broadcastIntentLocked(ActivityManagerService.java:15719)
                                                                                                    	at com.android.server.am.ActivityManagerService.broadcastIntentInPackage(ActivityManagerService.java:15916)
                                                                                                    	at com.android.server.am.ActivityManagerService$LocalService.broadcastIntentInPackage(ActivityManagerService.java:18556)
                                                                                                    	at com.android.server.am.PendingIntentRecord.sendInner(PendingIntentRecord.java:432)
                                                                                                    	at com.android.server.am.PendingIntentRecord.sendWithResult(PendingIntentRecord.java:276)
                                                                                                    	at com.android.server.am.ActivityManagerService.sendIntentSender(ActivityManagerService.java:5633)
                                                                                                    	at android.app.PendingIntent.sendAndReturnResult(PendingIntent.java:896)
                                                                                                    	at android.app.PendingIntent.send(PendingIntent.java:878)
                                                                                                    	at com.android.server.AlarmManagerService$DeliveryTracker.deliverLocked(AlarmManagerService.java:4760)
                                                                                                    	at com.android.server.AlarmManagerService.deliverAlarmsLocked(AlarmManagerService.java:3899)
                                                                                                    	at com.android.server.AlarmManagerService$AlarmThread.run(AlarmManagerService.java:4160)
2025-08-18 16:18:05.948  4087-4087  Common                  com.dspread.mdm.service              I  ✅ ⏰ 设置任务执行定时器成功，下次执行: 60秒后 (1分钟)
2025-08-18 16:18:11.466  4087-4087  Receiver                com.dspread.mdm.service              D  🔧 电池状态更新: 电池状态: 电量=42%, 温度=35.5°C, 健康=2, 充电=是, 低电量=否, 耗尽=否
2025-08-18 16:18:48.929  4087-4087  Receiver                com.dspread.mdm.service              D  🔧 电池状态更新: 电池状态: 电量=42%, 温度=35.2°C, 健康=2, 充电=是, 低电量=否, 耗尽=否
2025-08-18 16:19:05.946   961-1047  ActivityManager         system_server                        E  Sending non-protected broadcast com.dspread.mdm.service.WSTASK_EXEC_BC from system uid 1000 pkg com.dspread.mdm.service (Ask Gemini)
                                                                                                    java.lang.Throwable
                                                                                                    	at com.android.server.am.ActivityManagerService.checkBroadcastFromSystem(ActivityManagerService.java:15082)
                                                                                                    	at com.android.server.am.ActivityManagerService.broadcastIntentLocked(ActivityManagerService.java:15719)
                                                                                                    	at com.android.server.am.ActivityManagerService.broadcastIntentInPackage(ActivityManagerService.java:15916)
                                                                                                    	at com.android.server.am.ActivityManagerService$LocalService.broadcastIntentInPackage(ActivityManagerService.java:18556)
                                                                                                    	at com.android.server.am.PendingIntentRecord.sendInner(PendingIntentRecord.java:432)
                                                                                                    	at com.android.server.am.PendingIntentRecord.sendWithResult(PendingIntentRecord.java:276)
                                                                                                    	at com.android.server.am.ActivityManagerService.sendIntentSender(ActivityManagerService.java:5633)
                                                                                                    	at android.app.PendingIntent.sendAndReturnResult(PendingIntent.java:896)
                                                                                                    	at android.app.PendingIntent.send(PendingIntent.java:878)
                                                                                                    	at com.android.server.AlarmManagerService$DeliveryTracker.deliverLocked(AlarmManagerService.java:4760)
                                                                                                    	at com.android.server.AlarmManagerService.deliverAlarmsLocked(AlarmManagerService.java:3899)
                                                                                                    	at com.android.server.AlarmManagerService$AlarmThread.run(AlarmManagerService.java:4160)
2025-08-18 16:19:05.965  4087-4087  Common                  com.dspread.mdm.service              I  ✅ ⏰ 设置任务执行定时器成功，下次执行: 60秒后 (1分钟)
2025-08-18 16:19:11.466  4087-4087  Receiver                com.dspread.mdm.service              D  🔧 电池状态更新: 电池状态: 电量=42%, 温度=35.3°C, 健康=2, 充电=是, 低电量=否, 耗尽=否
2025-08-18 16:19:24.268   961-1047  ActivityManager         system_server                        E  Sending non-protected broadcast com.dspread.mdm.service.SERVICE_GUARD_TIMER from system uid 1000 pkg com.dspread.mdm.service (Ask Gemini)
                                                                                                    java.lang.Throwable
                                                                                                    	at com.android.server.am.ActivityManagerService.checkBroadcastFromSystem(ActivityManagerService.java:15082)
                                                                                                    	at com.android.server.am.ActivityManagerService.broadcastIntentLocked(ActivityManagerService.java:15719)
                                                                                                    	at com.android.server.am.ActivityManagerService.broadcastIntentInPackage(ActivityManagerService.java:15916)
                                                                                                    	at com.android.server.am.ActivityManagerService$LocalService.broadcastIntentInPackage(ActivityManagerService.java:18556)
                                                                                                    	at com.android.server.am.PendingIntentRecord.sendInner(PendingIntentRecord.java:432)
                                                                                                    	at com.android.server.am.PendingIntentRecord.sendWithResult(PendingIntentRecord.java:276)
                                                                                                    	at com.android.server.am.ActivityManagerService.sendIntentSender(ActivityManagerService.java:5633)
                                                                                                    	at android.app.PendingIntent.sendAndReturnResult(PendingIntent.java:896)
                                                                                                    	at android.app.PendingIntent.send(PendingIntent.java:878)
                                                                                                    	at com.android.server.AlarmManagerService$DeliveryTracker.deliverLocked(AlarmManagerService.java:4760)
                                                                                                    	at com.android.server.AlarmManagerService.deliverAlarmsLocked(AlarmManagerService.java:3899)
                                                                                                    	at com.android.server.AlarmManagerService$AlarmThread.run(AlarmManagerService.java:4160)
2025-08-18 16:19:24.289  4087-4125  Common                  com.dspread.mdm.service              D  🔧 ServiceGuardEventHandler 开始执行保活动作
2025-08-18 16:19:24.296  4087-4125  Common                  com.dspread.mdm.service              I  ✅ ⏰ 设置服务守护定时器成功，下次执行: 120秒后 (2分钟)
2025-08-18 16:19:24.298  4087-4125  ContextImpl             com.dspread.mdm.service              W  Calling a method in the system process without a qualified user: android.app.ContextImpl.startService:1571 android.content.ContextWrapper.startService:669 com.dspread.mdm.service.broadcast.handlers.service.ServiceGuardEventHandler.startKeepAliveService:211 com.dspread.mdm.service.broadcast.handlers.service.ServiceGuardEventHandler.performKeepAliveActions:193 com.dspread.mdm.service.broadcast.handlers.service.ServiceGuardEventHandler.access$performKeepAliveActions:24 
2025-08-18 16:19:24.301  4087-4125  Common                  com.dspread.mdm.service              D  🔧 ServiceGuardEventHandler 启动保活服务
2025-08-18 16:19:24.302  4087-4087  Service                 com.dspread.mdm.service              D  🔧 ServiceKeepAliveService 保活服务启动
2025-08-18 16:19:24.303  4087-4087  Platform                com.dspread.mdm.service              D  🔧 WakeLockManager WakeLock不存在或未持有: ServiceKeepAlive
2025-08-18 16:19:24.307  4087-4087  Platform                com.dspread.mdm.service              D  🔧 WakeLockManager 获取WakeLock成功: ServiceKeepAlive (超时: 30000ms)
2025-08-18 16:19:24.307  4087-4125  Common                  com.dspread.mdm.service              D  🔧 ServiceGuardEventHandler 保活动作执行完成
2025-08-18 16:19:27.311  4087-4125  Common                  com.dspread.mdm.service              D  🔧 ServiceGuardEventHandler WakeLock已释放
2025-08-18 16:19:54.321  4087-4087  Platform                com.dspread.mdm.service              D  🔧 WakeLockManager WakeLock不存在或未持有: ServiceKeepAlive
2025-08-18 16:19:54.322  4087-4087  Service                 com.dspread.mdm.service              D  🔧 ServiceKeepAliveService 保活任务完成，释放WakeLock
2025-08-18 16:20:05.964   961-1047  ActivityManager         system_server                        E  Sending non-protected broadcast com.dspread.mdm.service.WSTASK_EXEC_BC from system uid 1000 pkg com.dspread.mdm.service (Ask Gemini)
                                                                                                    java.lang.Throwable
                                                                                                    	at com.android.server.am.ActivityManagerService.checkBroadcastFromSystem(ActivityManagerService.java:15082)
                                                                                                    	at com.android.server.am.ActivityManagerService.broadcastIntentLocked(ActivityManagerService.java:15719)
                                                                                                    	at com.android.server.am.ActivityManagerService.broadcastIntentInPackage(ActivityManagerService.java:15916)
                                                                                                    	at com.android.server.am.ActivityManagerService$LocalService.broadcastIntentInPackage(ActivityManagerService.java:18556)
                                                                                                    	at com.android.server.am.PendingIntentRecord.sendInner(PendingIntentRecord.java:432)
                                                                                                    	at com.android.server.am.PendingIntentRecord.sendWithResult(PendingIntentRecord.java:276)
                                                                                                    	at com.android.server.am.ActivityManagerService.sendIntentSender(ActivityManagerService.java:5633)
                                                                                                    	at android.app.PendingIntent.sendAndReturnResult(PendingIntent.java:896)
                                                                                                    	at android.app.PendingIntent.send(PendingIntent.java:878)
                                                                                                    	at com.android.server.AlarmManagerService$DeliveryTracker.deliverLocked(AlarmManagerService.java:4760)
                                                                                                    	at com.android.server.AlarmManagerService.deliverAlarmsLocked(AlarmManagerService.java:3899)
                                                                                                    	at com.android.server.AlarmManagerService$AlarmThread.run(AlarmManagerService.java:4160)
2025-08-18 16:20:05.984  4087-4087  Common                  com.dspread.mdm.service              I  ✅ ⏰ 设置任务执行定时器成功，下次执行: 60秒后 (1分钟)
2025-08-18 16:20:11.465  4087-4087  Receiver                com.dspread.mdm.service              D  🔧 电池状态更新: 电池状态: 电量=42%, 温度=35.2°C, 健康=2, 充电=是, 低电量=否, 耗尽=否
2025-08-18 16:20:26.367  4087-4087  Receiver                com.dspread.mdm.service              D  🔧 电池状态更新: 电池状态: 电量=43%, 温度=35.2°C, 健康=2, 充电=是, 低电量=否, 耗尽=否
2025-08-18 16:20:31.341  4087-4087  Receiver                com.dspread.mdm.service              D  🔧 电池状态更新: 电池状态: 电量=43%, 温度=35.2°C, 健康=2, 充电=是, 低电量=否, 耗尽=否
2025-08-18 16:21:05.982   961-1047  ActivityManager         system_server                        E  Sending non-protected broadcast com.dspread.mdm.service.WSTASK_EXEC_BC from system uid 1000 pkg com.dspread.mdm.service (Ask Gemini)
                                                                                                    java.lang.Throwable
                                                                                                    	at com.android.server.am.ActivityManagerService.checkBroadcastFromSystem(ActivityManagerService.java:15082)
                                                                                                    	at com.android.server.am.ActivityManagerService.broadcastIntentLocked(ActivityManagerService.java:15719)
                                                                                                    	at com.android.server.am.ActivityManagerService.broadcastIntentInPackage(ActivityManagerService.java:15916)
                                                                                                    	at com.android.server.am.ActivityManagerService$LocalService.broadcastIntentInPackage(ActivityManagerService.java:18556)
                                                                                                    	at com.android.server.am.PendingIntentRecord.sendInner(PendingIntentRecord.java:432)
                                                                                                    	at com.android.server.am.PendingIntentRecord.sendWithResult(PendingIntentRecord.java:276)
                                                                                                    	at com.android.server.am.ActivityManagerService.sendIntentSender(ActivityManagerService.java:5633)
                                                                                                    	at android.app.PendingIntent.sendAndReturnResult(PendingIntent.java:896)
                                                                                                    	at android.app.PendingIntent.send(PendingIntent.java:878)
                                                                                                    	at com.android.server.AlarmManagerService$DeliveryTracker.deliverLocked(AlarmManagerService.java:4760)
                                                                                                    	at com.android.server.AlarmManagerService.deliverAlarmsLocked(AlarmManagerService.java:3899)
                                                                                                    	at com.android.server.AlarmManagerService$AlarmThread.run(AlarmManagerService.java:4160)
2025-08-18 16:21:06.005  4087-4087  Common                  com.dspread.mdm.service              I  ✅ ⏰ 设置任务执行定时器成功，下次执行: 60秒后 (1分钟)
2025-08-18 16:21:11.464  4087-4087  Receiver                com.dspread.mdm.service              D  🔧 电池状态更新: 电池状态: 电量=43%, 温度=35.3°C, 健康=2, 充电=是, 低电量=否, 耗尽=否
2025-08-18 16:21:24.294   961-1047  ActivityManager         system_server                        E  Sending non-protected broadcast com.dspread.mdm.service.SERVICE_GUARD_TIMER from system uid 1000 pkg com.dspread.mdm.service (Ask Gemini)
                                                                                                    java.lang.Throwable
                                                                                                    	at com.android.server.am.ActivityManagerService.checkBroadcastFromSystem(ActivityManagerService.java:15082)
                                                                                                    	at com.android.server.am.ActivityManagerService.broadcastIntentLocked(ActivityManagerService.java:15719)
                                                                                                    	at com.android.server.am.ActivityManagerService.broadcastIntentInPackage(ActivityManagerService.java:15916)
                                                                                                    	at com.android.server.am.ActivityManagerService$LocalService.broadcastIntentInPackage(ActivityManagerService.java:18556)
                                                                                                    	at com.android.server.am.PendingIntentRecord.sendInner(PendingIntentRecord.java:432)
                                                                                                    	at com.android.server.am.PendingIntentRecord.sendWithResult(PendingIntentRecord.java:276)
                                                                                                    	at com.android.server.am.ActivityManagerService.sendIntentSender(ActivityManagerService.java:5633)
                                                                                                    	at android.app.PendingIntent.sendAndReturnResult(PendingIntent.java:896)
                                                                                                    	at android.app.PendingIntent.send(PendingIntent.java:878)
                                                                                                    	at com.android.server.AlarmManagerService$DeliveryTracker.deliverLocked(AlarmManagerService.java:4760)
                                                                                                    	at com.android.server.AlarmManagerService.deliverAlarmsLocked(AlarmManagerService.java:3899)
                                                                                                    	at com.android.server.AlarmManagerService$AlarmThread.run(AlarmManagerService.java:4160)
2025-08-18 16:21:24.310  4087-4125  Common                  com.dspread.mdm.service              D  🔧 ServiceGuardEventHandler 开始执行保活动作
2025-08-18 16:21:24.315  4087-4125  Common                  com.dspread.mdm.service              I  ✅ ⏰ 设置服务守护定时器成功，下次执行: 120秒后 (2分钟)
2025-08-18 16:21:24.316  4087-4125  ContextImpl             com.dspread.mdm.service              W  Calling a method in the system process without a qualified user: android.app.ContextImpl.startService:1571 android.content.ContextWrapper.startService:669 com.dspread.mdm.service.broadcast.handlers.service.ServiceGuardEventHandler.startKeepAliveService:211 com.dspread.mdm.service.broadcast.handlers.service.ServiceGuardEventHandler.performKeepAliveActions:193 com.dspread.mdm.service.broadcast.handlers.service.ServiceGuardEventHandler.access$performKeepAliveActions:24 
2025-08-18 16:21:24.319  4087-4125  Common                  com.dspread.mdm.service              D  🔧 ServiceGuardEventHandler 启动保活服务
2025-08-18 16:21:24.320  4087-4087  Service                 com.dspread.mdm.service              D  🔧 ServiceKeepAliveService 保活服务启动
2025-08-18 16:21:24.320  4087-4087  Platform                com.dspread.mdm.service              D  🔧 WakeLockManager WakeLock不存在或未持有: ServiceKeepAlive
2025-08-18 16:21:24.324  4087-4087  Platform                com.dspread.mdm.service              D  🔧 WakeLockManager 获取WakeLock成功: ServiceKeepAlive (超时: 30000ms)
2025-08-18 16:21:24.325  4087-4125  Common                  com.dspread.mdm.service              D  🔧 ServiceGuardEventHandler 保活动作执行完成
2025-08-18 16:21:27.328  4087-4125  Common                  com.dspread.mdm.service              D  🔧 ServiceGuardEventHandler WakeLock已释放
2025-08-18 16:21:54.335  4087-4087  Platform                com.dspread.mdm.service              D  🔧 WakeLockManager WakeLock不存在或未持有: ServiceKeepAlive
2025-08-18 16:21:54.336  4087-4087  Service                 com.dspread.mdm.service              D  🔧 ServiceKeepAliveService 保活任务完成，释放WakeLock
2025-08-18 16:22:04.430  4087-4087  Receiver                com.dspread.mdm.service              D  🔧 电池状态更新: 电池状态: 电量=43%, 温度=35.1°C, 健康=2, 充电=是, 低电量=否, 耗尽=否
2025-08-18 16:22:05.740   961-1047  ActivityManager         system_server                        E  Sending non-protected broadcast com.dspread.mdm.service.TER_INFO_UPLOAD_BC from system uid 1000 pkg com.dspread.mdm.service (Ask Gemini)
                                                                                                    java.lang.Throwable
                                                                                                    	at com.android.server.am.ActivityManagerService.checkBroadcastFromSystem(ActivityManagerService.java:15082)
                                                                                                    	at com.android.server.am.ActivityManagerService.broadcastIntentLocked(ActivityManagerService.java:15719)
                                                                                                    	at com.android.server.am.ActivityManagerService.broadcastIntentInPackage(ActivityManagerService.java:15916)
                                                                                                    	at com.android.server.am.ActivityManagerService$LocalService.broadcastIntentInPackage(ActivityManagerService.java:18556)
                                                                                                    	at com.android.server.am.PendingIntentRecord.sendInner(PendingIntentRecord.java:432)
                                                                                                    	at com.android.server.am.PendingIntentRecord.sendWithResult(PendingIntentRecord.java:276)
                                                                                                    	at com.android.server.am.ActivityManagerService.sendIntentSender(ActivityManagerService.java:5633)
                                                                                                    	at android.app.PendingIntent.sendAndReturnResult(PendingIntent.java:896)
                                                                                                    	at android.app.PendingIntent.send(PendingIntent.java:878)
                                                                                                    	at com.android.server.AlarmManagerService$DeliveryTracker.deliverLocked(AlarmManagerService.java:4760)
                                                                                                    	at com.android.server.AlarmManagerService.deliverAlarmsLocked(AlarmManagerService.java:3899)
                                                                                                    	at com.android.server.AlarmManagerService$AlarmThread.run(AlarmManagerService.java:4160)
2025-08-18 16:22:05.761  4087-4087  Common                  com.dspread.mdm.service              D  🔧 TimerConfig 使用Provisioning配置的终端信息上传间隔: 900秒
2025-08-18 16:22:05.765  4087-4087  Common                  com.dspread.mdm.service              I  ✅ ⏰ 设置终端信息定时器成功，下次执行: 900秒后 (15分钟)
2025-08-18 16:22:05.766  4087-4087  Receiver                com.dspread.mdm.service              D  🔧 TerminalInfoEventHandler WebSocket未连接，跳过终端信息上传
2025-08-18 16:22:05.782   961-1047  ActivityManager         system_server                        E  Sending non-protected broadcast com.dspread.mdm.service.POLL_TIMER_START from system uid 1000 pkg com.dspread.mdm.service (Ask Gemini)
                                                                                                    java.lang.Throwable
                                                                                                    	at com.android.server.am.ActivityManagerService.checkBroadcastFromSystem(ActivityManagerService.java:15082)
                                                                                                    	at com.android.server.am.ActivityManagerService.broadcastIntentLocked(ActivityManagerService.java:15719)
                                                                                                    	at com.android.server.am.ActivityManagerService.broadcastIntentInPackage(ActivityManagerService.java:15916)
                                                                                                    	at com.android.server.am.ActivityManagerService$LocalService.broadcastIntentInPackage(ActivityManagerService.java:18556)
                                                                                                    	at com.android.server.am.PendingIntentRecord.sendInner(PendingIntentRecord.java:432)
                                                                                                    	at com.android.server.am.PendingIntentRecord.sendWithResult(PendingIntentRecord.java:276)
                                                                                                    	at com.android.server.am.ActivityManagerService.sendIntentSender(ActivityManagerService.java:5633)
                                                                                                    	at android.app.PendingIntent.sendAndReturnResult(PendingIntent.java:896)
                                                                                                    	at android.app.PendingIntent.send(PendingIntent.java:878)
                                                                                                    	at com.android.server.AlarmManagerService$DeliveryTracker.deliverLocked(AlarmManagerService.java:4760)
                                                                                                    	at com.android.server.AlarmManagerService.deliverAlarmsLocked(AlarmManagerService.java:3899)
                                                                                                    	at com.android.server.AlarmManagerService$AlarmThread.run(AlarmManagerService.java:4160)
2025-08-18 16:22:05.792  4087-4087  Receiver                com.dspread.mdm.service              D  🔧 收到轮询定时器广播
2025-08-18 16:22:05.792  4087-4087  Receiver                com.dspread.mdm.service              D  🔧 WebSocket未连接，跳过轮询任务
2025-08-18 16:22:05.797  4087-4087  Common                  com.dspread.mdm.service              D  🔧 TimerConfig 使用Provisioning配置的心跳间隔: 300秒
2025-08-18 16:22:05.801  4087-4087  Common                  com.dspread.mdm.service              I  ✅ ⏰ 设置心跳定时器成功，下次执行: 300秒后 (5分钟)
2025-08-18 16:22:06.003   961-1047  ActivityManager         system_server                        E  Sending non-protected broadcast com.dspread.mdm.service.WSTASK_EXEC_BC from system uid 1000 pkg com.dspread.mdm.service (Ask Gemini)
                                                                                                    java.lang.Throwable
                                                                                                    	at com.android.server.am.ActivityManagerService.checkBroadcastFromSystem(ActivityManagerService.java:15082)
                                                                                                    	at com.android.server.am.ActivityManagerService.broadcastIntentLocked(ActivityManagerService.java:15719)
                                                                                                    	at com.android.server.am.ActivityManagerService.broadcastIntentInPackage(ActivityManagerService.java:15916)
                                                                                                    	at com.android.server.am.ActivityManagerService$LocalService.broadcastIntentInPackage(ActivityManagerService.java:18556)
                                                                                                    	at com.android.server.am.PendingIntentRecord.sendInner(PendingIntentRecord.java:432)
                                                                                                    	at com.android.server.am.PendingIntentRecord.sendWithResult(PendingIntentRecord.java:276)
                                                                                                    	at com.android.server.am.ActivityManagerService.sendIntentSender(ActivityManagerService.java:5633)
                                                                                                    	at android.app.PendingIntent.sendAndReturnResult(PendingIntent.java:896)
                                                                                                    	at android.app.PendingIntent.send(PendingIntent.java:878)
                                                                                                    	at com.android.server.AlarmManagerService$DeliveryTracker.deliverLocked(AlarmManagerService.java:4760)
                                                                                                    	at com.android.server.AlarmManagerService.deliverAlarmsLocked(AlarmManagerService.java:3899)
                                                                                                    	at com.android.server.AlarmManagerService$AlarmThread.run(AlarmManagerService.java:4160)
2025-08-18 16:22:06.015  4087-4087  Common                  com.dspread.mdm.service              I  ✅ ⏰ 设置任务执行定时器成功，下次执行: 60秒后 (1分钟)
2025-08-18 16:22:11.468  4087-4087  Receiver                com.dspread.mdm.service              D  🔧 电池状态更新: 电池状态: 电量=43%, 温度=35.2°C, 健康=2, 充电=是, 低电量=否, 耗尽=否
2025-08-18 16:23:00.842  4087-4087  Receiver                com.dspread.mdm.service              D  🔧 电池状态更新: 电池状态: 电量=43%, 温度=35.1°C, 健康=2, 充电=是, 低电量=否, 耗尽=否
2025-08-18 16:23:06.015   961-1047  ActivityManager         system_server                        E  Sending non-protected broadcast com.dspread.mdm.service.WSTASK_EXEC_BC from system uid 1000 pkg com.dspread.mdm.service (Ask Gemini)
                                                                                                    java.lang.Throwable
                                                                                                    	at com.android.server.am.ActivityManagerService.checkBroadcastFromSystem(ActivityManagerService.java:15082)
                                                                                                    	at com.android.server.am.ActivityManagerService.broadcastIntentLocked(ActivityManagerService.java:15719)
                                                                                                    	at com.android.server.am.ActivityManagerService.broadcastIntentInPackage(ActivityManagerService.java:15916)
                                                                                                    	at com.android.server.am.ActivityManagerService$LocalService.broadcastIntentInPackage(ActivityManagerService.java:18556)
                                                                                                    	at com.android.server.am.PendingIntentRecord.sendInner(PendingIntentRecord.java:432)
                                                                                                    	at com.android.server.am.PendingIntentRecord.sendWithResult(PendingIntentRecord.java:276)
                                                                                                    	at com.android.server.am.ActivityManagerService.sendIntentSender(ActivityManagerService.java:5633)
                                                                                                    	at android.app.PendingIntent.sendAndReturnResult(PendingIntent.java:896)
                                                                                                    	at android.app.PendingIntent.send(PendingIntent.java:878)
                                                                                                    	at com.android.server.AlarmManagerService$DeliveryTracker.deliverLocked(AlarmManagerService.java:4760)
                                                                                                    	at com.android.server.AlarmManagerService.deliverAlarmsLocked(AlarmManagerService.java:3899)
                                                                                                    	at com.android.server.AlarmManagerService$AlarmThread.run(AlarmManagerService.java:4160)
2025-08-18 16:23:06.030  4087-4087  Common                  com.dspread.mdm.service              I  ✅ ⏰ 设置任务执行定时器成功，下次执行: 60秒后 (1分钟)
2025-08-18 16:23:11.465  4087-4087  Receiver                com.dspread.mdm.service              D  🔧 电池状态更新: 电池状态: 电量=43%, 温度=35.2°C, 健康=2, 充电=是, 低电量=否, 耗尽=否
2025-08-18 16:23:20.385  4087-4087  WebSocket               com.dspread.mdm.service              I  🔧 重连 WebSocket: wss://api.dspreadserv.net/status/websocket/register?serialNo=MDEyNDMwMzAyMDI0MTIwOTAxMDE=&publicKey=TUlHZk1BMEdDU3FHU0liM0RRRUJBUVVBQTRHTkFEQ0JpUUtCZ1FEclkrK0Rkc1NNY2xTU2xzWDJoOW5HRzM2TnB3V1cxazgxcGV2YVRsQmIwZFJ3TVdJWE9PZTZ0eGExdG5rUDI4dXJzK1RpczdiMVVPSC9UM3Iyd3ZDd1lTaXpwdC85RG1TVGZoa0gvSzk4WENzREtSNXU4eHlLMFNxenBBSExFWDN0cTdoNUhQU2NJbGo4QUZHdmo2TitoN0RaOTh1QXlwcWNiYnppKzZnS2RRSURBUUFC&query=0&msgVer=3&timestamp=1755505400373&signature=AoaTJrj2VZvfXMQNHzM1AIWI/TyfIjQq05l8E2GlaAC0CzcwruPjeBZSr0KfITLL8dLnMmhryyaDijk29gTHRfdV6wNBG1N+VFB9KyCHHypy2m70VwCIo25032e7Ip45BYSoBMQDdwOK3GYFAI7RKY+jKKimSVIt2QP2PgPx3MI=
2025-08-18 16:23:20.386  4087-4087  WebSocket               com.dspread.mdm.service              I  🔧 未检测到系统代理设置
2025-08-18 16:23:20.408  4087-4525  System.out              com.dspread.mdm.service              I  [socket]:check permission begin!
2025-08-18 16:23:20.659  4087-4526  System.out              com.dspread.mdm.service              I  [socket]:check permission begin!
2025-08-18 16:23:21.988  4087-4527  Common                  com.dspread.mdm.service              I  ✅ WebSocket 物理连接成功，等待服务器 hellMsg
2025-08-18 16:23:21.988  4087-4527  WebSocket               com.dspread.mdm.service              I  🔧 ===== 心跳发送器实际配置 =====
2025-08-18 16:23:21.989  4087-4527  WebSocket               com.dspread.mdm.service              I  🔧 实际 PING 发送间隔: 60000ms (60秒)
2025-08-18 16:23:21.990  4087-4527  WebSocket               com.dspread.mdm.service              I  🔧 PING 定时器名称: PingSender
2025-08-18 16:23:21.991  4087-4527  WebSocket               com.dspread.mdm.service              I  🔧 实际 PONG 发送间隔: 0ms (0秒)
2025-08-18 16:23:21.992  4087-4527  WebSocket               com.dspread.mdm.service              I  🔧 PONG 定时器名称: PongSender
2025-08-18 16:23:21.993  4087-4527  WebSocket               com.dspread.mdm.service              I  🔧 ===============================
2025-08-18 16:23:22.011  4087-4527  Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"hellMsg":"hello 01243030202412090101","tranCode":"S0000","version":"1","rebootTime":"03:19:29","serialNo":"01243030202412090101","deviceStatus":"6"}
2025-08-18 16:23:22.012  4087-4527  Common                  com.dspread.mdm.service              I  ✅ 收到服务器 hellMsg，WebSocket 业务连接成功！
2025-08-18 16:23:22.012  4087-4527  Common                  com.dspread.mdm.service              I  ✅ WebSocket 业务连接成功！
2025-08-18 16:23:22.014  4087-4527  WebSocket               com.dspread.mdm.service              I  🔧 业务连接成功，重连计数已重置为 0
2025-08-18 16:23:22.020  4087-4527  Common                  com.dspread.mdm.service              I  ✅ ⏰ 设置任务执行定时器成功，下次执行: 60秒后 (1分钟)
2025-08-18 16:23:22.020  4087-4527  WebSocket               com.dspread.mdm.service              I  🔧 已启动任务执行定时器
2025-08-18 16:23:22.025  4087-4527  Common                  com.dspread.mdm.service              D  🔧 TimerConfig 使用Provisioning配置的心跳间隔: 300秒
2025-08-18 16:23:22.031  4087-4527  Common                  com.dspread.mdm.service              I  ✅ ⏰ 设置心跳定时器成功，下次执行: 300秒后 (5分钟)
2025-08-18 16:23:22.031  4087-4527  WebSocket               com.dspread.mdm.service              I  🔧 已启动心跳定时器
2025-08-18 16:23:22.035  4087-4527  Common                  com.dspread.mdm.service              D  🔧 TimerConfig 使用Provisioning配置的终端信息上传间隔: 900秒
2025-08-18 16:23:22.039  4087-4527  Common                  com.dspread.mdm.service              I  ✅ ⏰ 设置终端信息定时器成功，下次执行: 900秒后 (15分钟)
2025-08-18 16:23:22.039  4087-4527  WebSocket               com.dspread.mdm.service              I  🔧 已启动终端信息上传定时器
2025-08-18 16:23:22.039  4087-4527  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接成功，检查自身更新任务状态
2025-08-18 16:23:22.040  4087-4527  ContextImpl             com.dspread.mdm.service              W  Calling a method in the system process without a qualified user: android.app.ContextImpl.sendBroadcast:1046 android.content.ContextWrapper.sendBroadcast:448 com.dspread.mdm.service.broadcast.core.BroadcastSender.sendBroadcast:99 com.dspread.mdm.service.broadcast.core.BroadcastSender.sendBroadcast:23 com.dspread.mdm.service.network.websocket.connection.WsConnectionManager.checkSelfUpdateTaskStatus:311 
2025-08-18 16:23:22.042   961-2287  ActivityManager         system_server                        E  Sending non-protected broadcast com.dspread.mdm.service.CHECK_SELF_UPDATE_STATUS from system 4087:com.dspread.mdm.service/1000 pkg com.dspread.mdm.service (Ask Gemini)
                                                                                                    java.lang.Throwable
                                                                                                    	at com.android.server.am.ActivityManagerService.checkBroadcastFromSystem(ActivityManagerService.java:15078)
                                                                                                    	at com.android.server.am.ActivityManagerService.broadcastIntentLocked(ActivityManagerService.java:15625)
                                                                                                    	at com.android.server.am.ActivityManagerService.broadcastIntentLocked(ActivityManagerService.java:15095)
                                                                                                    	at com.android.server.am.ActivityManagerService.broadcastIntent(ActivityManagerService.java:15878)
                                                                                                    	at android.app.IActivityManager$Stub.onTransact(IActivityManager.java:2071)
                                                                                                    	at com.android.server.am.ActivityManagerService.onTransact(ActivityManagerService.java:2886)
                                                                                                    	at android.os.Binder.execTransactInternal(Binder.java:1021)
                                                                                                    	at android.os.Binder.execTransact(Binder.java:994)
2025-08-18 16:23:22.054  4087-4527  Receiver                com.dspread.mdm.service              D  🔧 发送广播: com.dspread.mdm.service.CHECK_SELF_UPDATE_STATUS
2025-08-18 16:23:22.055  4087-4087  Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 收到自身更新状态检查广播: com.dspread.mdm.service.CHECK_SELF_UPDATE_STATUS
2025-08-18 16:23:22.055  4087-4527  WebSocket               com.dspread.mdm.service              I  🔧 开始发送缓存消息，数量: 6
2025-08-18 16:23:22.056  4087-4087  Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 开始执行自身更新任务状态检查
2025-08-18 16:23:22.056  4087-4087  Task                    com.dspread.mdm.service              D  🔧 获取待执行任务数量: 0
2025-08-18 16:23:22.057  4087-4087  Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 待执行任务数量: 0
2025-08-18 16:23:22.062  4087-4527  WebSocket               com.dspread.mdm.service              I  🔧 C0902 主动式上送: cached_message_resend (主动: 12)
2025-08-18 16:23:22.068  4087-4527  Common                  com.dspread.mdm.service              I  ✅ 缓存消息发送成功: C0902
2025-08-18 16:23:22.090  4087-4102  System                  com.dspread.mdm.service              W  A resource failed to call close. 
2025-08-18 16:23:22.570  4087-4527  WebSocket               com.dspread.mdm.service              I  🔧 C0902 主动式上送: cached_message_resend (主动: 13)
2025-08-18 16:23:22.578  4087-4527  Common                  com.dspread.mdm.service              I  ✅ 缓存消息发送成功: C0902
2025-08-18 16:23:23.079  4087-4527  WebSocket               com.dspread.mdm.service              I  🔧 C0902 主动式上送: cached_message_resend (主动: 14)
2025-08-18 16:23:23.088  4087-4527  Common                  com.dspread.mdm.service              I  ✅ 缓存消息发送成功: C0902
2025-08-18 16:23:23.590  4087-4527  WebSocket               com.dspread.mdm.service              I  🔧 C0902 主动式上送: cached_message_resend (主动: 15)
2025-08-18 16:23:23.599  4087-4527  Common                  com.dspread.mdm.service              I  ✅ 缓存消息发送成功: C0902
2025-08-18 16:23:24.100  4087-4527  WebSocket               com.dspread.mdm.service              I  🔧 C0902 主动式上送: cached_message_resend (主动: 16)
2025-08-18 16:23:24.109  4087-4527  Common                  com.dspread.mdm.service              I  ✅ 缓存消息发送成功: C0902
2025-08-18 16:23:24.315   961-1047  ActivityManager         system_server                        E  Sending non-protected broadcast com.dspread.mdm.service.SERVICE_GUARD_TIMER from system uid 1000 pkg com.dspread.mdm.service (Ask Gemini)
                                                                                                    java.lang.Throwable
                                                                                                    	at com.android.server.am.ActivityManagerService.checkBroadcastFromSystem(ActivityManagerService.java:15082)
                                                                                                    	at com.android.server.am.ActivityManagerService.broadcastIntentLocked(ActivityManagerService.java:15719)
                                                                                                    	at com.android.server.am.ActivityManagerService.broadcastIntentInPackage(ActivityManagerService.java:15916)
                                                                                                    	at com.android.server.am.ActivityManagerService$LocalService.broadcastIntentInPackage(ActivityManagerService.java:18556)
                                                                                                    	at com.android.server.am.PendingIntentRecord.sendInner(PendingIntentRecord.java:432)
                                                                                                    	at com.android.server.am.PendingIntentRecord.sendWithResult(PendingIntentRecord.java:276)
                                                                                                    	at com.android.server.am.ActivityManagerService.sendIntentSender(ActivityManagerService.java:5633)
                                                                                                    	at android.app.PendingIntent.sendAndReturnResult(PendingIntent.java:896)
                                                                                                    	at android.app.PendingIntent.send(PendingIntent.java:878)
                                                                                                    	at com.android.server.AlarmManagerService$DeliveryTracker.deliverLocked(AlarmManagerService.java:4760)
                                                                                                    	at com.android.server.AlarmManagerService.deliverAlarmsLocked(AlarmManagerService.java:3899)
                                                                                                    	at com.android.server.AlarmManagerService$AlarmThread.run(AlarmManagerService.java:4160)
2025-08-18 16:23:24.336  4087-4125  Common                  com.dspread.mdm.service              D  🔧 ServiceGuardEventHandler 开始执行保活动作
2025-08-18 16:23:24.343  4087-4125  Common                  com.dspread.mdm.service              I  ✅ ⏰ 设置服务守护定时器成功，下次执行: 120秒后 (2分钟)
2025-08-18 16:23:24.344  4087-4125  ContextImpl             com.dspread.mdm.service              W  Calling a method in the system process without a qualified user: android.app.ContextImpl.startService:1571 android.content.ContextWrapper.startService:669 com.dspread.mdm.service.broadcast.handlers.service.ServiceGuardEventHandler.startKeepAliveService:211 com.dspread.mdm.service.broadcast.handlers.service.ServiceGuardEventHandler.performKeepAliveActions:193 com.dspread.mdm.service.broadcast.handlers.service.ServiceGuardEventHandler.access$performKeepAliveActions:24 
2025-08-18 16:23:24.348  4087-4125  Common                  com.dspread.mdm.service              D  🔧 ServiceGuardEventHandler 启动保活服务
2025-08-18 16:23:24.348  4087-4087  Service                 com.dspread.mdm.service              D  🔧 ServiceKeepAliveService 保活服务启动
2025-08-18 16:23:24.349  4087-4087  Platform                com.dspread.mdm.service              D  🔧 WakeLockManager WakeLock不存在或未持有: ServiceKeepAlive
2025-08-18 16:23:24.353  4087-4087  Platform                com.dspread.mdm.service              D  🔧 WakeLockManager 获取WakeLock成功: ServiceKeepAlive (超时: 30000ms)
2025-08-18 16:23:24.353  4087-4125  Common                  com.dspread.mdm.service              D  🔧 ServiceGuardEventHandler 保活动作执行完成
2025-08-18 16:23:24.610  4087-4527  WebSocket               com.dspread.mdm.service              I  🔧 C0905 主动式上送: cached_message_resend (主动: 17)
2025-08-18 16:23:24.620  4087-4527  Common                  com.dspread.mdm.service              I  ✅ 缓存消息发送成功: C0905
2025-08-18 16:23:24.622  4087-4527  WebSocket               com.dspread.mdm.service              I  🔧 缓存流量统计消息重发成功，已记录上送日期
2025-08-18 16:23:25.123  4087-4527  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接成功
2025-08-18 16:23:25.123  4087-4527  WebSocket               com.dspread.mdm.service              I  🔧 设备状态: 6
2025-08-18 16:23:25.124  4087-4527  WebSocket               com.dspread.mdm.service              I  🔧 检查首次连接标志: isFirstTimeWebSocketConnected=false
2025-08-18 16:23:25.124  4087-4527  WebSocket               com.dspread.mdm.service              I  🔧 非首次连接，跳过初始化信息发送和初始化检查
2025-08-18 16:23:25.125  4087-4527  WebSocket               com.dspread.mdm.service              I  🔧 执行必要的状态同步检查
2025-08-18 16:23:25.125  4087-4527  WebSocket               com.dspread.mdm.service              I  🔧 检查服务更新结果
2025-08-18 16:23:25.126  4087-4527  Task                    com.dspread.mdm.service              I  ℹ️ 本地任务列表长度: 0
2025-08-18 16:23:25.127  4087-4527  Task                    com.dspread.mdm.service              D  🔧 本地任务列表内容: []
2025-08-18 16:23:25.130  4087-4527  Task                    com.dspread.mdm.service              I  ℹ️ 当前应用版本: versionCode=13, versionName=1.0.10.20250818.DSPREAD.MDM.SERVICE
2025-08-18 16:23:25.130  4087-4527  Task                    com.dspread.mdm.service              I  ℹ️ 任务列表数量: 0
2025-08-18 16:23:25.130  4087-4527  WebSocket               com.dspread.mdm.service              I  🔧 检查OS升级状态...
2025-08-18 16:23:25.131  4087-4527  Common                  com.dspread.mdm.service              D  🔧 OsUpdateStatusChecker WebSocket连接成功，检查OS升级状态
2025-08-18 16:23:25.131  4087-4527  Common                  com.dspread.mdm.service              D  🔧 OsUpdateStatusChecker 没有待检查的OS升级任务
2025-08-18 16:23:25.132  4087-4527  WebSocket               com.dspread.mdm.service              I  🔧 OS升级状态检查已触发
2025-08-18 16:23:25.167  4087-4527  Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"msg_state":"P","request_time":"1755504707141","data":{"taskList":[{"apkMd5":"9f20f245b9123ce2afac04491bf4d9bc","apkName":"OpenVPN for Android","endDate":"9999-12-31 23:59:59","installBy":"0","versionName":"0.5.36a","versionCode":"65","url":"https://smartms.s3.sa-east-1.amazonaws.com/app/67b1a89781624afea3817778d3efaa48.apk","beginDate":"2024-08-18 08:11:47","taskType":"01","appId":"a20c98aa13384b8ea3c77c2b431e0c43","apkSize":"1622936","appIconUrl":"https://smartms.s3.sa-east-1.amazonaws.com/icon/57495b8a8d0441c5bbdedce4fd380acficon.png","packName":"de.blinkt.openvpn","taskId":"7635d8b800e640c39a7dcb0851a6525e&a20c98aa13384b8ea3c77c2b431e0c43"}]},"tranCode":"ST001","request_id":"1755504707141ST001","version":"1","serialNo":"01243030202412090101"}
2025-08-18 16:23:25.169  4087-4527  WebSocket               com.dspread.mdm.service              I  🔧 准备发送响应确认 - requestId: 1755504707141ST001, needResponse: true
2025-08-18 16:23:25.180  4087-4527  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0000","serialNo":"01243030202412090101","request_time":"1755505405171","request_id":"1755505405171C0000","version":"1","org_request_id":"1755504707141ST001","org_request_time":"1755504707141","response_state":"0","myVersionName":"1.0.10.20250818.DSPREAD.MDM.SERVICE","terminalDate":"20250818162325"}
2025-08-18 16:23:25.190  4087-4527  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0000","serialNo":"01243030202412090101","request_time":"1755505405183","request_id":"1755505405183C0000","version":"1","org_request_id":"1755504707141ST001","org_request_time":"1755504707141","response_state":"0","myVersionName":"1.0.10.20250818.DSPREAD.MDM.SERVICE","terminalDate":"20250818162325"}
2025-08-18 16:23:25.190  4087-4527  WebSocket               com.dspread.mdm.service              I  🔧 已发送C0000响应确认: requestId=1755504707141ST001
2025-08-18 16:23:25.191  4087-4527  Task                    com.dspread.mdm.service              D  🔧 处理任务消息: ST001
2025-08-18 16:23:25.192  4087-4527  Task                    com.dspread.mdm.service              I  ℹ️ 更新任务列表: requestId=1755504707141ST001, 任务数量=1
2025-08-18 16:23:25.193  4087-4527  Task                    com.dspread.mdm.service              D  🔧 任务插入到末尾: taskId=7635d8b800e640c39a7dcb0851a6525e&a20c98aa13384b8ea3c77c2b431e0c43
2025-08-18 16:23:25.194  4087-4527  Task                    com.dspread.mdm.service              D  🔧 添加/更新任务: taskId=7635d8b800e640c39a7dcb0851a6525e&a20c98aa13384b8ea3c77c2b431e0c43
2025-08-18 16:23:25.197  4087-4527  Task                    com.dspread.mdm.service              D  🔧 保存任务列表到存储: 1 个
2025-08-18 16:23:25.197  4087-4527  Task                    com.dspread.mdm.service              D  🔧 开始处理任务列表，共 1 个任务
2025-08-18 16:23:25.198  4087-4527  Task                    com.dspread.mdm.service              D  🔧 处理任务: taskId=7635d8b800e640c39a7dcb0851a6525e&a20c98aa13384b8ea3c77c2b431e0c43, type=01, package=de.blinkt.openvpn, apk=OpenVPN for Android
2025-08-18 16:23:25.199  4087-4527  Task                    com.dspread.mdm.service              D  🔧 处理安装/更新任务: 7635d8b800e640c39a7dcb0851a6525e&a20c98aa13384b8ea3c77c2b431e0c43
2025-08-18 16:23:25.199  4087-4527  Task                    com.dspread.mdm.service              D  🔧 执行安装任务: OpenVPN for Android (de.blinkt.openvpn)
2025-08-18 16:23:25.200  4087-4527  Task                    com.dspread.mdm.service              D  🔧 版本信息: 0.5.36a(65)
2025-08-18 16:23:25.200  4087-4527  Task                    com.dspread.mdm.service              D  🔧 允许降级: false, 强制安装: false
2025-08-18 16:23:25.201  4087-4527  Task                    com.dspread.mdm.service              D  🔧 判断 packageName=de.blinkt.openvpn versionCode=65 versionName=0.5.36a 是否安装
2025-08-18 16:23:25.202  4087-4527  Task                    com.dspread.mdm.service              D  🔧 de.blinkt.openvpn 未安装
2025-08-18 16:23:25.203  4087-4527  Task                    com.dspread.mdm.service              D  🔧 应用安装状态检查结果: 0
2025-08-18 16:23:25.203  4087-4527  Task                    com.dspread.mdm.service              D  🔧 应用未安装或版本较低，继续安装流程
2025-08-18 16:23:25.204  4087-4527  Task                    com.dspread.mdm.service              D  🔧 开始下载和安装应用: OpenVPN for Android
2025-08-18 16:23:25.204  4087-4527  Task                    com.dspread.mdm.service              D  🔧 下载URL: https://smartms.s3.sa-east-1.amazonaws.com/app/67b1a89781624afea3817778d3efaa48.apk
2025-08-18 16:23:25.205  4087-4527  Task                    com.dspread.mdm.service              D  🔧 包名: de.blinkt.openvpn, 版本: 0.5.36a(65)
2025-08-18 16:23:25.205  4087-4527  Task                    com.dspread.mdm.service              D  🔧 判断 packageName=de.blinkt.openvpn versionCode=65 versionName=0.5.36a 是否安装
2025-08-18 16:23:25.206  4087-4527  Task                    com.dspread.mdm.service              D  🔧 de.blinkt.openvpn 未安装
2025-08-18 16:23:25.207  4087-4527  Task                    com.dspread.mdm.service              D  🔧 检查应用安装状态: de.blinkt.openvpn = 0
2025-08-18 16:23:25.207  4087-4527  Task                    com.dspread.mdm.service              D  🔧 继续安装流程: status=0
2025-08-18 16:23:25.208  4087-4527  Task                    com.dspread.mdm.service              D  🔧 更新任务状态: taskId=7635d8b800e640c39a7dcb0851a6525e&a20c98aa13384b8ea3c77c2b431e0c43, state=A01
2025-08-18 16:23:25.209  4087-4527  Task                    com.dspread.mdm.service              D  🔧 保存任务列表到存储: 1 个
2025-08-18 16:23:25.210  4087-4527  Task                    com.dspread.mdm.service              D  🔧 保存任务列表到存储: 1 个
2025-08-18 16:23:25.210  4087-4527  WebSocket               com.dspread.mdm.service              I  🔧 ST001 主动式上送: task_failed (主动: 18)
2025-08-18 16:23:25.218  4087-4527  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0108","serialNo":"01243030202412090101","request_time":"1755505405212","request_id":"1755505405212C0108","version":"1","data":{"taskId":"7635d8b800e640c39a7dcb0851a6525e&a20c98aa13384b8ea3c77c2b431e0c43","taskResult":"A01","appId":"a20c98aa13384b8ea3c77c2b431e0c43"},"myVersionName":"1.0.10.20250818.DSPREAD.MDM.SERVICE","terminalDate":"20250818162325","org_request_id":"1755504707141ST001","org_request_time":"1755504707141"}
2025-08-18 16:23:25.218  4087-4527  WebSocket               com.dspread.mdm.service              I  🔧 任务结果实时上送: taskId=7635d8b800e640c39a7dcb0851a6525e&a20c98aa13384b8ea3c77c2b431e0c43, result=A01 (1)
2025-08-18 16:23:25.222  4087-4527  Task                    com.dspread.mdm.service              D  🔧 原始taskId: 7635d8b800e640c39a7dcb0851a6525e&a20c98aa13384b8ea3c77c2b431e0c43
2025-08-18 16:23:25.222  4087-4527  Task                    com.dspread.mdm.service              D  🔧 清理后taskId: 7635d8b800e640c39a7dcb0851a6525e_a20c98aa13384b8ea3c77c2b431e0c43
2025-08-18 16:23:25.223  4087-4527  Task                    com.dspread.mdm.service              D  🔧 清理后APK路径: /storage/emulated/0/Android/data/com.dspread.mdm.service/files/apk/7635d8b800e640c39a7dcb0851a6525e_a20c98aa13384b8ea3c77c2b431e0c43.apk
2025-08-18 16:23:25.223  4087-4527  Task                    com.dspread.mdm.service              D  🔧 APK文件名: 7635d8b800e640c39a7dcb0851a6525e_a20c98aa13384b8ea3c77c2b431e0c43.apk
2025-08-18 16:23:25.225  4087-4530  Platform                com.dspread.mdm.service              D  🔧 WakeLockManager WakeLock不存在或未持有: download_7635d8b800e640c39a7dcb0851a6525e&a20c98aa13384b8ea3c77c2b431e0c43
2025-08-18 16:23:25.227  4087-4530  Platform                com.dspread.mdm.service              D  🔧 WakeLockManager 获取WakeLock成功: download_7635d8b800e640c39a7dcb0851a6525e&a20c98aa13384b8ea3c77c2b431e0c43 (超时: 600000ms)
2025-08-18 16:23:25.228  4087-4530  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 尝试下载，第1次
2025-08-18 16:23:25.230  4087-4530  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 开始下载
2025-08-18 16:23:25.230  4087-4530  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader URL: https://smartms.s3.sa-east-1.amazonaws.com/app/67b1a89781624afea3817778d3efaa48.apk
2025-08-18 16:23:25.230  4087-4530  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 路径: /storage/emulated/0/Android/data/com.dspread.mdm.service/files/apk/7635d8b800e640c39a7dcb0851a6525e_a20c98aa13384b8ea3c77c2b431e0c43.apk
2025-08-18 16:23:25.232  4087-4530  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 本地文件不存在，创建文件
2025-08-18 16:23:25.234  4087-4530  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader allFileLength=1622936, 需要从服务器获取文件大小
2025-08-18 16:23:25.237  4087-4530  System.out              com.dspread.mdm.service              I  [okhttp]:check permission begin!
2025-08-18 16:23:25.237  4087-4530  System.out              com.dspread.mdm.service              I  [okhttp]:not MMS!
2025-08-18 16:23:25.237  4087-4530  System.out              com.dspread.mdm.service              I  [okhttp]:not Email!
2025-08-18 16:23:25.339  4087-4530  System.out              com.dspread.mdm.service              I  [socket]:check permission begin!
2025-08-18 16:23:26.097  4087-4527  Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"request_time":"1755505410585","org_request_time":"1755505405212","org_request_id":"1755505405212C0108","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755505410585S0000","serialNo":"01243030202412090101"}
2025-08-18 16:23:26.099  4087-4527  WebSocket               com.dspread.mdm.service              I  🔧 收到响应消息: transCode=S0000, orgRequestId=1755505405212C0108, state=0, remark=
2025-08-18 16:23:26.101  4087-4527  WebSocket               com.dspread.mdm.service              I  🔧 处理任务结果响应: state=0
2025-08-18 16:23:26.101  4087-4527  WebSocket               com.dspread.mdm.service              I  🔧 任务结果上传成功
2025-08-18 16:23:26.155  4087-4530  System.out              com.dspread.mdm.service              I  [OkHttp] sendRequest>>
2025-08-18 16:23:26.156  4087-4530  System.out              com.dspread.mdm.service              I  [OkHttp] sendRequest<<
2025-08-18 16:23:26.930  4087-4530  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader HTTP响应码: 200
2025-08-18 16:23:26.931  4087-4530  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 服务器返回Content-Length: 1622936
2025-08-18 16:23:26.932  4087-4530  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 完整下载模式
2025-08-18 16:23:26.933  4087-4530  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 使用调用方提供的文件大小: 1622936
2025-08-18 16:23:26.934  4087-4530  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader localFile = true /storage/emulated/0/Android/data/com.dspread.mdm.service/files/apk/7635d8b800e640c39a7dcb0851a6525e_a20c98aa13384b8ea3c77c2b431e0c43.apk, 追加模式: false
2025-08-18 16:23:27.283  4087-4530  Task                    com.dspread.mdm.service              D  🔧 应用下载进度: 0%
2025-08-18 16:23:27.357  4087-4126  Common                  com.dspread.mdm.service              D  🔧 ServiceGuardEventHandler WakeLock已释放
2025-08-18 16:23:28.127  4087-4530  Task                    com.dspread.mdm.service              D  🔧 应用下载进度: 10%
2025-08-18 16:23:28.928  4087-4530  Task                    com.dspread.mdm.service              D  🔧 应用下载进度: 20%
2025-08-18 16:23:29.440  4087-4530  Task                    com.dspread.mdm.service              D  🔧 应用下载进度: 30%
2025-08-18 16:23:30.143  4087-4530  Task                    com.dspread.mdm.service              D  🔧 应用下载进度: 40%
2025-08-18 16:23:30.582  4087-4530  Task                    com.dspread.mdm.service              D  🔧 应用下载进度: 50%
2025-08-18 16:23:31.000  4087-4530  Task                    com.dspread.mdm.service              D  🔧 应用下载进度: 60%
2025-08-18 16:23:32.182  4087-4530  Task                    com.dspread.mdm.service              D  🔧 应用下载进度: 70%
2025-08-18 16:23:32.268  4087-4530  Task                    com.dspread.mdm.service              D  🔧 应用下载进度: 80%
2025-08-18 16:23:32.948  4087-4530  Task                    com.dspread.mdm.service              D  🔧 应用下载进度: 90%
2025-08-18 16:23:33.744  4087-4530  Task                    com.dspread.mdm.service              D  🔧 应用下载进度: 100%
2025-08-18 16:23:33.746  4087-4530  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 计算MD5: 7635d8b800e640c39a7dcb0851a6525e_a20c98aa13384b8ea3c77c2b431e0c43.apk
2025-08-18 16:23:33.780  4087-4530  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 文件: 1622936 MD5: 9f20f245b9123ce2afac04491bf4d9bc
2025-08-18 16:23:33.780  4087-4530  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader MD5校验正确
2025-08-18 16:23:33.781  4087-4530  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 清理连接资源
2025-08-18 16:23:33.782  4087-4530  Task                    com.dspread.mdm.service              D  🔧 APK下载成功: /storage/emulated/0/Android/data/com.dspread.mdm.service/files/apk/7635d8b800e640c39a7dcb0851a6525e_a20c98aa13384b8ea3c77c2b431e0c43.apk
2025-08-18 16:23:33.783  4087-4530  Task                    com.dspread.mdm.service              D  🔧 更新任务状态: taskId=7635d8b800e640c39a7dcb0851a6525e&a20c98aa13384b8ea3c77c2b431e0c43, state=A03
2025-08-18 16:23:33.785  4087-4530  Task                    com.dspread.mdm.service              D  🔧 保存任务列表到存储: 1 个
2025-08-18 16:23:33.787  4087-4530  Task                    com.dspread.mdm.service              D  🔧 保存任务列表到存储: 1 个
2025-08-18 16:23:33.788  4087-4530  WebSocket               com.dspread.mdm.service              I  🔧 ST001 主动式上送: task_failed (主动: 19)
2025-08-18 16:23:33.799  4087-4530  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0108","serialNo":"01243030202412090101","request_time":"1755505413791","request_id":"1755505413791C0108","version":"1","data":{"taskId":"7635d8b800e640c39a7dcb0851a6525e&a20c98aa13384b8ea3c77c2b431e0c43","taskResult":"A03","appId":"a20c98aa13384b8ea3c77c2b431e0c43"},"myVersionName":"1.0.10.20250818.DSPREAD.MDM.SERVICE","terminalDate":"20250818162333","org_request_id":"1755504707141ST001","org_request_time":"1755504707141"}
2025-08-18 16:23:33.799  4087-4530  WebSocket               com.dspread.mdm.service              I  🔧 任务结果实时上送: taskId=7635d8b800e640c39a7dcb0851a6525e&a20c98aa13384b8ea3c77c2b431e0c43, result=A03 (1)
2025-08-18 16:23:33.800  4087-4530  Task                    com.dspread.mdm.service              D  🔧 设备空闲状态: true
2025-08-18 16:23:33.800  4087-4530  Task                    com.dspread.mdm.service              D  🔧 设备空闲或强制安装，立即开始安装
2025-08-18 16:23:33.801  4087-4530  Platform                com.dspread.mdm.service              D  🔧 WakeLockManager WakeLock不存在或未持有: install_7635d8b800e640c39a7dcb0851a6525e&a20c98aa13384b8ea3c77c2b431e0c43
2025-08-18 16:23:33.803  4087-4530  Platform                com.dspread.mdm.service              D  🔧 WakeLockManager 获取WakeLock成功: install_7635d8b800e640c39a7dcb0851a6525e&a20c98aa13384b8ea3c77c2b431e0c43 (超时: 300000ms)
2025-08-18 16:23:33.804  4087-4530  Task                    com.dspread.mdm.service              D  🔧 判断 packageName=de.blinkt.openvpn versionCode=65 versionName=0.5.36a 是否安装
2025-08-18 16:23:33.805  4087-4530  Task                    com.dspread.mdm.service              D  🔧 de.blinkt.openvpn 未安装
2025-08-18 16:23:33.806  4087-4530  Task                    com.dspread.mdm.service              D  🔧 检查应用安装状态: de.blinkt.openvpn = 0
2025-08-18 16:23:33.806  4087-4530  Task                    com.dspread.mdm.service              D  🔧 继续安装流程: status=0
2025-08-18 16:23:33.808  4087-4530  Task                    com.dspread.mdm.service              D  🔧 更新任务状态: taskId=7635d8b800e640c39a7dcb0851a6525e&a20c98aa13384b8ea3c77c2b431e0c43, state=B02
2025-08-18 16:23:33.809  4087-4530  Task                    com.dspread.mdm.service              D  🔧 保存任务列表到存储: 1 个
2025-08-18 16:23:33.811  4087-4530  Task                    com.dspread.mdm.service              D  🔧 保存任务列表到存储: 1 个
2025-08-18 16:23:33.811  4087-4530  WebSocket               com.dspread.mdm.service              I  🔧 ST001 主动式上送: task_failed (主动: 20)
2025-08-18 16:23:33.819  4087-4530  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0108","serialNo":"01243030202412090101","request_time":"1755505413814","request_id":"1755505413814C0108","version":"1","data":{"taskId":"7635d8b800e640c39a7dcb0851a6525e&a20c98aa13384b8ea3c77c2b431e0c43","taskResult":"B02","appId":"a20c98aa13384b8ea3c77c2b431e0c43"},"myVersionName":"1.0.10.20250818.DSPREAD.MDM.SERVICE","terminalDate":"20250818162333","org_request_id":"1755504707141ST001","org_request_time":"1755504707141"}
2025-08-18 16:23:33.820  4087-4530  WebSocket               com.dspread.mdm.service              I  🔧 任务结果实时上送: taskId=7635d8b800e640c39a7dcb0851a6525e&a20c98aa13384b8ea3c77c2b431e0c43, result=B02 (1)
2025-08-18 16:23:33.820  4087-4530  Task                    com.dspread.mdm.service              D  🔧 开始安装APK: /storage/emulated/0/Android/data/com.dspread.mdm.service/files/apk/7635d8b800e640c39a7dcb0851a6525e_a20c98aa13384b8ea3c77c2b431e0c43.apk
2025-08-18 16:23:33.822  4087-4530  AppManager              com.dspread.mdm.service              I  ℹ️ 创建SessionCallback
2025-08-18 16:23:33.823  4087-4530  AppManager              com.dspread.mdm.service              I  ℹ️ 注册SessionCallback
2025-08-18 16:23:33.863  4087-4530  SystemConfig            com.dspread.mdm.service              I  Non-xml file /system/etc/permissions/pms_sysapp_removable_system_list.txt in /system/etc/permissions directory, ignoring
2025-08-18 16:23:33.956  4087-4530  SystemConfig            com.dspread.mdm.service              I  Non-xml file /system/etc/permissions/pms_sysapp_removable_vendor_list.txt in /system/etc/permissions directory, ignoring
2025-08-18 16:23:34.018  4087-4530  SystemConfig            com.dspread.mdm.service              W  No directory /product/etc/sysconfig, skipping
2025-08-18 16:23:34.046  4087-4530  SystemConfig            com.dspread.mdm.service              W  No directory /product_services/etc/sysconfig, skipping
2025-08-18 16:23:34.047  4087-4530  SystemConfig            com.dspread.mdm.service              W  No directory /product_services/etc/permissions, skipping
2025-08-18 16:23:34.047  4087-4530  System                  com.dspread.mdm.service              W  ClassLoader referenced unknown path: /system/framework/mediatek-cta.jar
2025-08-18 16:23:34.049  4087-4530  CtaManagerFactory       com.dspread.mdm.service              W  CtaManagerFactoryImpl not found
2025-08-18 16:23:34.052  4087-4530  AppManager              com.dspread.mdm.service              I  ℹ️ 准备安装: de.blinkt.openvpn
2025-08-18 16:23:34.092  4087-4530  AppManager              com.dspread.mdm.service              I  ℹ️ APK验证通过: de.blinkt.openvpn v0.5.36a(65) 1584KB
2025-08-18 16:23:34.121  4087-4530  AppManager              com.dspread.mdm.service              I  ℹ️ 创建Session: sessionId=147372155
2025-08-18 16:23:34.270  4087-4530  AppManager              com.dspread.mdm.service              I  ℹ️ PackageInstaller installation committed: sessionId=147372155
2025-08-18 16:23:34.272  4087-4530  Platform                com.dspread.mdm.service              D  🔧 WakeLockManager 释放WakeLock成功: download_7635d8b800e640c39a7dcb0851a6525e&a20c98aa13384b8ea3c77c2b431e0c43
2025-08-18 16:23:34.282  4087-4087  AppManager              com.dspread.mdm.service              I  ℹ️ 安装进度: 90%
2025-08-18 16:23:34.345  4087-4527  Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"request_time":"1755505418804","org_request_time":"1755505413791","org_request_id":"1755505413791C0108","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755505418804S0000","serialNo":"01243030202412090101"}
2025-08-18 16:23:34.346  4087-4527  WebSocket               com.dspread.mdm.service              I  🔧 收到响应消息: transCode=S0000, orgRequestId=1755505413791C0108, state=0, remark=
2025-08-18 16:23:34.346  4087-4527  WebSocket               com.dspread.mdm.service              I  🔧 处理任务结果响应: state=0
2025-08-18 16:23:34.346  4087-4527  WebSocket               com.dspread.mdm.service              I  🔧 任务结果上传成功
2025-08-18 16:23:34.768  4087-4087  AppManager              com.dspread.mdm.service              I  ℹ️ 安装成功: de.blinkt.openvpn
2025-08-18 16:23:34.769  4087-4087  Task                    com.dspread.mdm.service              D  🔧 安装回调: pkg=de.blinkt.openvpn, returnCode=1, error=
2025-08-18 16:23:34.770  4087-4087  Task                    com.dspread.mdm.service              D  🔧 安装成功: de.blinkt.openvpn
2025-08-18 16:23:34.772  4087-4087  Task                    com.dspread.mdm.service              D  🔧 已删除APK文件: /storage/emulated/0/Android/data/com.dspread.mdm.service/files/apk/7635d8b800e640c39a7dcb0851a6525e_a20c98aa13384b8ea3c77c2b431e0c43.apk
2025-08-18 16:23:34.773  4087-4087  Platform                com.dspread.mdm.service              D  🔧 应用状态变化: INSTALL - de.blinkt.openvpn
2025-08-18 16:23:34.810  4087-4527  Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"request_time":"1755505419171","org_request_time":"1755505413814","org_request_id":"1755505413814C0108","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755505419171S0000","serialNo":"01243030202412090101"}
2025-08-18 16:23:34.812  4087-4527  WebSocket               com.dspread.mdm.service              I  🔧 收到响应消息: transCode=S0000, orgRequestId=1755505413814C0108, state=0, remark=
2025-08-18 16:23:34.814  4087-4527  WebSocket               com.dspread.mdm.service              I  🔧 处理任务结果响应: state=0
2025-08-18 16:23:34.815  4087-4527  WebSocket               com.dspread.mdm.service              I  🔧 任务结果上传成功
2025-08-18 16:23:34.836  4087-4087  Platform                com.dspread.mdm.service              D  🔧 重新收集应用信息（强制刷新）
2025-08-18 16:23:34.874  4087-4087  AppManager              com.dspread.mdm.service              I  ℹ️ 应用信息获取完成: 总数99(系统95/用户4) 返回4个
2025-08-18 16:23:34.884  4087-4087  Platform                com.dspread.mdm.service              D  🔧 应用信息: 4 个应用
2025-08-18 16:23:34.885  4087-4087  Task                    com.dspread.mdm.service              D  🔧 更新任务状态: taskId=7635d8b800e640c39a7dcb0851a6525e&a20c98aa13384b8ea3c77c2b431e0c43, state=B03
2025-08-18 16:23:34.886  4087-4087  Task                    com.dspread.mdm.service              D  🔧 保存任务列表到存储: 1 个
2025-08-18 16:23:34.887  4087-4087  Task                    com.dspread.mdm.service              D  🔧 保存任务列表到存储: 1 个
2025-08-18 16:23:34.887  4087-4087  WebSocket               com.dspread.mdm.service              I  🔧 ST001 主动式上送: task_failed (主动: 21)
2025-08-18 16:23:34.897  4087-4087  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0108","serialNo":"01243030202412090101","request_time":"1755505414891","request_id":"1755505414891C0108","version":"1","data":{"taskId":"7635d8b800e640c39a7dcb0851a6525e&a20c98aa13384b8ea3c77c2b431e0c43","taskResult":"B03","appId":"a20c98aa13384b8ea3c77c2b431e0c43"},"myVersionName":"1.0.10.20250818.DSPREAD.MDM.SERVICE","terminalDate":"20250818162334","org_request_id":"1755504707141ST001","org_request_time":"1755504707141"}
2025-08-18 16:23:34.898  4087-4087  WebSocket               com.dspread.mdm.service              I  🔧 任务结果实时上送: taskId=7635d8b800e640c39a7dcb0851a6525e&a20c98aa13384b8ea3c77c2b431e0c43, result=B03 (1)
2025-08-18 16:23:34.900  4087-4087  Platform                com.dspread.mdm.service              D  🔧 WakeLockManager 释放WakeLock成功: install_7635d8b800e640c39a7dcb0851a6525e&a20c98aa13384b8ea3c77c2b431e0c43
2025-08-18 16:23:34.900  4087-4087  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 收到广播: android.intent.action.PACKAGE_ADDED
2025-08-18 16:23:34.901  4087-4087  Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 收到包更新广播: android.intent.action.PACKAGE_ADDED, 包名: de.blinkt.openvpn
2025-08-18 16:23:34.907  4087-4087  Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 应用安装: de.blinkt.openvpn
2025-08-18 16:23:34.907  4087-4087  Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 检查WebSocket状态: packageName=de.blinkt.openvpn, action=INSTALL
2025-08-18 16:23:34.908  4087-4087  Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 通知rulebase处理应用安装: de.blinkt.openvpn
2025-08-18 16:23:34.909  4087-4087  RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 收到系统广播，应用已安装: de.blinkt.openvpn
2025-08-18 16:23:34.909  4087-4087  RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 未找到匹配的安装回调: de.blinkt.openvpn
2025-08-18 16:23:34.909  4087-4087  RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 未找到匹配的规则应用: de.blinkt.openvpn
2025-08-18 16:23:34.910  4087-4087  Platform                com.dspread.mdm.service              D  🔧 重新收集应用信息（强制刷新）
2025-08-18 16:23:34.936  4087-4100  ead.mdm.servic          com.dspread.mdm.service              I  Background young concurrent copying GC freed 17095(1375KB) AllocSpace objects, 46(984KB) LOS objects, 49% free, 2476KB/4919KB, paused 5.872ms total 37.216ms
2025-08-18 16:23:34.943  4087-4087  AppManager              com.dspread.mdm.service              I  ℹ️ 应用信息获取完成: 总数99(系统95/用户4) 返回4个
2025-08-18 16:23:34.951  4087-4087  Platform                com.dspread.mdm.service              D  🔧 应用信息: 4 个应用
2025-08-18 16:23:34.952  4087-4087  Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 应用信息缓存刷新完成
2025-08-18 16:23:34.953  4087-4087  ContextImpl             com.dspread.mdm.service              W  Calling a method in the system process without a qualified user: android.app.ContextImpl.sendBroadcast:1046 android.content.ContextWrapper.sendBroadcast:448 com.dspread.mdm.service.broadcast.core.BroadcastSender.sendBroadcast:99 com.dspread.mdm.service.broadcast.core.BroadcastSender.sendBroadcast:23 com.dspread.mdm.service.broadcast.handlers.system.PackageUpdateEventHandlerImpl.sendAppRefreshBroadcast:498 
2025-08-18 16:23:34.954  4087-4087  Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 发送应用刷新广播
2025-08-18 16:23:35.354  4087-4527  Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"request_time":"1755505419904","org_request_time":"1755505414891","org_request_id":"1755505414891C0108","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755505419904S0000","serialNo":"01243030202412090101"}
2025-08-18 16:23:35.355  4087-4527  WebSocket               com.dspread.mdm.service              I  🔧 收到响应消息: transCode=S0000, orgRequestId=1755505414891C0108, state=0, remark=
2025-08-18 16:23:35.356  4087-4527  WebSocket               com.dspread.mdm.service              I  🔧 处理任务结果响应: state=0
2025-08-18 16:23:35.356  4087-4527  WebSocket               com.dspread.mdm.service              I  🔧 任务结果上传成功
2025-08-18 16:23:42.685  4087-4087  Receiver                com.dspread.mdm.service              D  🔧 电池状态更新: 电池状态: 电量=44%, 温度=35.0°C, 健康=2, 充电=是, 低电量=否, 耗尽=否
2025-08-18 16:23:42.685  4087-4087  Receiver                com.dspread.mdm.service              D  🔧 电池状态变化: 电量=44%, 温度=35°C, 充电=true
2025-08-18 16:23:42.690  4087-4087  WebSocket               com.dspread.mdm.service              I  🔧 C0902 被动式上送: battery_level_change_5 (被动: 11)
2025-08-18 16:23:42.708  4087-4087  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0902","serialNo":"01243030202412090101","request_time":"1755505422698","request_id":"1755505422698C0902","version":"1","data":{"batteryLife":44,"batteryHealth":2,"temprature":"35.0","isCharging":"1","isLowBattery":"0","powerOff":"0","outOfBattery":"0"},"myVersionName":"1.0.10.20250818.DSPREAD.MDM.SERVICE","terminalDate":"20250818162342"}
2025-08-18 16:23:42.708  4087-4087  WebSocket               com.dspread.mdm.service              I  🔧 C0902 电池状态上送成功 (1, trigger=battery_level_change_5)
2025-08-18 16:23:54.367  4087-4087  Platform                com.dspread.mdm.service              D  🔧 WakeLockManager WakeLock不存在或未持有: ServiceKeepAlive
2025-08-18 16:23:54.368  4087-4087  Service                 com.dspread.mdm.service              D  🔧 ServiceKeepAliveService 保活任务完成，释放WakeLock
2025-08-18 16:24:11.467  4087-4087  Receiver                com.dspread.mdm.service              D  🔧 电池状态更新: 电池状态: 电量=44%, 温度=35.1°C, 健康=2, 充电=是, 低电量=否, 耗尽=否
2025-08-18 16:24:21.990  4087-4528  WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 1
2025-08-18 16:24:22.018   961-1047  ActivityManager         system_server                        E  Sending non-protected broadcast com.dspread.mdm.service.WSTASK_EXEC_BC from system uid 1000 pkg com.dspread.mdm.service (Ask Gemini)
                                                                                                    java.lang.Throwable
                                                                                                    	at com.android.server.am.ActivityManagerService.checkBroadcastFromSystem(ActivityManagerService.java:15082)
                                                                                                    	at com.android.server.am.ActivityManagerService.broadcastIntentLocked(ActivityManagerService.java:15719)
                                                                                                    	at com.android.server.am.ActivityManagerService.broadcastIntentInPackage(ActivityManagerService.java:15916)
                                                                                                    	at com.android.server.am.ActivityManagerService$LocalService.broadcastIntentInPackage(ActivityManagerService.java:18556)
                                                                                                    	at com.android.server.am.PendingIntentRecord.sendInner(PendingIntentRecord.java:432)
                                                                                                    	at com.android.server.am.PendingIntentRecord.sendWithResult(PendingIntentRecord.java:276)
                                                                                                    	at com.android.server.am.ActivityManagerService.sendIntentSender(ActivityManagerService.java:5633)
                                                                                                    	at android.app.PendingIntent.sendAndReturnResult(PendingIntent.java:896)
                                                                                                    	at android.app.PendingIntent.send(PendingIntent.java:878)
                                                                                                    	at com.android.server.AlarmManagerService$DeliveryTracker.deliverLocked(AlarmManagerService.java:4760)
                                                                                                    	at com.android.server.AlarmManagerService.deliverAlarmsLocked(AlarmManagerService.java:3899)
                                                                                                    	at com.android.server.AlarmManagerService$AlarmThread.run(AlarmManagerService.java:4160)
2025-08-18 16:24:22.040  4087-4087  Common                  com.dspread.mdm.service              I  ✅ ⏰ 设置任务执行定时器成功，下次执行: 60秒后 (1分钟)
2025-08-18 16:24:22.400  4087-4527  WebSocket               com.dspread.mdm.service              I  🔧 收到服务器 PONG 帧: 1
2025-08-18 16:25:20.993  4087-4087  Receiver                com.dspread.mdm.service              D  🔧 电池状态更新: 电池状态: 电量=44%, 温度=35.0°C, 健康=2, 充电=是, 低电量=否, 耗尽=否
2025-08-18 16:25:21.990  4087-4528  WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 2
2025-08-18 16:25:22.040   961-1047  ActivityManager         system_server                        E  Sending non-protected broadcast com.dspread.mdm.service.WSTASK_EXEC_BC from system uid 1000 pkg com.dspread.mdm.service (Ask Gemini)
                                                                                                    java.lang.Throwable
                                                                                                    	at com.android.server.am.ActivityManagerService.checkBroadcastFromSystem(ActivityManagerService.java:15082)
                                                                                                    	at com.android.server.am.ActivityManagerService.broadcastIntentLocked(ActivityManagerService.java:15719)
                                                                                                    	at com.android.server.am.ActivityManagerService.broadcastIntentInPackage(ActivityManagerService.java:15916)
                                                                                                    	at com.android.server.am.ActivityManagerService$LocalService.broadcastIntentInPackage(ActivityManagerService.java:18556)
                                                                                                    	at com.android.server.am.PendingIntentRecord.sendInner(PendingIntentRecord.java:432)
                                                                                                    	at com.android.server.am.PendingIntentRecord.sendWithResult(PendingIntentRecord.java:276)
                                                                                                    	at com.android.server.am.ActivityManagerService.sendIntentSender(ActivityManagerService.java:5633)
                                                                                                    	at android.app.PendingIntent.sendAndReturnResult(PendingIntent.java:896)
                                                                                                    	at android.app.PendingIntent.send(PendingIntent.java:878)
                                                                                                    	at com.android.server.AlarmManagerService$DeliveryTracker.deliverLocked(AlarmManagerService.java:4760)
                                                                                                    	at com.android.server.AlarmManagerService.deliverAlarmsLocked(AlarmManagerService.java:3899)
                                                                                                    	at com.android.server.AlarmManagerService$AlarmThread.run(AlarmManagerService.java:4160)
2025-08-18 16:25:22.058  4087-4087  Common                  com.dspread.mdm.service              I  ✅ ⏰ 设置任务执行定时器成功，下次执行: 60秒后 (1分钟)
2025-08-18 16:25:22.407  4087-4527  WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误 (Ask Gemini)
                                                                                                    com.neovisionaries.ws.client.NoMoreFrameException: No more WebSocket frame from the server.
                                                                                                    	at com.neovisionaries.ws.client.WebSocketInputStream.readFrame(WebSocketInputStream.java:54)
                                                                                                    	at com.neovisionaries.ws.client.ReadingThread.readFrame(ReadingThread.java:338)
                                                                                                    	at com.neovisionaries.ws.client.ReadingThread.main(ReadingThread.java:99)
                                                                                                    	at com.neovisionaries.ws.client.ReadingThread.runMain(ReadingThread.java:64)
                                                                                                    	at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45)
2025-08-18 16:25:22.408  4087-4527  WebSocket               com.dspread.mdm.service              I  🔧 onError: 不触发重连，等待 onDisconnected 处理
2025-08-18 16:25:22.413  4087-4528  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接断开 - 客户端主动断开
2025-08-18 16:25:22.414  4087-4528  WebSocket               com.dspread.mdm.service              I  🔧    服务器关闭帧: code=-1, reason=无原因
2025-08-18 16:25:22.415  4087-4528  WebSocket               com.dspread.mdm.service              I  🔧    客户端关闭帧: code=1002, reason=No more WebSocket frame from the server.
2025-08-18 16:25:22.415  4087-4528  WebSocket               com.dspread.mdm.service              I  🔧    断开发起方: 客户端
2025-08-18 16:25:22.417  4087-4528  WebSocket               com.dspread.mdm.service              I  🔧 reconnect() 被调用，调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onDisconnected:727
2025-08-18 16:25:22.418  4087-4528  WebSocket               com.dspread.mdm.service              I  🔧 开始重连 WebSocket... (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onDisconnected:727)
2025-08-18 16:25:22.422  4087-4528  WebSocket               com.dspread.mdm.service              I  🔧 使用Provisioning配置的重连策略: delayPolicy=30, delaySwitch=1, delayTime=900
2025-08-18 16:25:22.423  4087-4528  WebSocket               com.dspread.mdm.service              I  🔧 当前断开记录数: 1, 延迟开关: 1
2025-08-18 16:25:22.424  4087-4528  WebSocket               com.dspread.mdm.service              I  🔧 重连策略计算完成: 第1次重连，间隔3000ms (3秒)
2025-08-18 16:25:22.424  4087-4528  WebSocket               com.dspread.mdm.service              I  🔧 开始第1次重连，间隔3000ms (3秒) (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onDisconnected:727)
2025-08-18 16:25:24.340   961-1047  ActivityManager         system_server                        E  Sending non-protected broadcast com.dspread.mdm.service.SERVICE_GUARD_TIMER from system uid 1000 pkg com.dspread.mdm.service (Ask Gemini)
                                                                                                    java.lang.Throwable
                                                                                                    	at com.android.server.am.ActivityManagerService.checkBroadcastFromSystem(ActivityManagerService.java:15082)
                                                                                                    	at com.android.server.am.ActivityManagerService.broadcastIntentLocked(ActivityManagerService.java:15719)
                                                                                                    	at com.android.server.am.ActivityManagerService.broadcastIntentInPackage(ActivityManagerService.java:15916)
                                                                                                    	at com.android.server.am.ActivityManagerService$LocalService.broadcastIntentInPackage(ActivityManagerService.java:18556)
                                                                                                    	at com.android.server.am.PendingIntentRecord.sendInner(PendingIntentRecord.java:432)
                                                                                                    	at com.android.server.am.PendingIntentRecord.sendWithResult(PendingIntentRecord.java:276)
                                                                                                    	at com.android.server.am.ActivityManagerService.sendIntentSender(ActivityManagerService.java:5633)
                                                                                                    	at android.app.PendingIntent.sendAndReturnResult(PendingIntent.java:896)
                                                                                                    	at android.app.PendingIntent.send(PendingIntent.java:878)
                                                                                                    	at com.android.server.AlarmManagerService$DeliveryTracker.deliverLocked(AlarmManagerService.java:4760)
                                                                                                    	at com.android.server.AlarmManagerService.deliverAlarmsLocked(AlarmManagerService.java:3899)
                                                                                                    	at com.android.server.AlarmManagerService$AlarmThread.run(AlarmManagerService.java:4160)
2025-08-18 16:25:24.357  4087-4126  Common                  com.dspread.mdm.service              D  🔧 ServiceGuardEventHandler 开始执行保活动作
2025-08-18 16:25:24.363  4087-4126  Common                  com.dspread.mdm.service              I  ✅ ⏰ 设置服务守护定时器成功，下次执行: 120秒后 (2分钟)
2025-08-18 16:25:24.365  4087-4126  ContextImpl             com.dspread.mdm.service              W  Calling a method in the system process without a qualified user: android.app.ContextImpl.startService:1571 android.content.ContextWrapper.startService:669 com.dspread.mdm.service.broadcast.handlers.service.ServiceGuardEventHandler.startKeepAliveService:211 com.dspread.mdm.service.broadcast.handlers.service.ServiceGuardEventHandler.performKeepAliveActions:193 com.dspread.mdm.service.broadcast.handlers.service.ServiceGuardEventHandler.access$performKeepAliveActions:24 
2025-08-18 16:25:24.368  4087-4126  Common                  com.dspread.mdm.service              D  🔧 ServiceGuardEventHandler 启动保活服务
2025-08-18 16:25:24.369  4087-4087  Service                 com.dspread.mdm.service              D  🔧 ServiceKeepAliveService 保活服务启动
2025-08-18 16:25:24.370  4087-4087  Platform                com.dspread.mdm.service              D  🔧 WakeLockManager WakeLock不存在或未持有: ServiceKeepAlive
2025-08-18 16:25:24.373  4087-4087  Platform                com.dspread.mdm.service              D  🔧 WakeLockManager 获取WakeLock成功: ServiceKeepAlive (超时: 30000ms)
2025-08-18 16:25:24.374  4087-4126  Common                  com.dspread.mdm.service              D  🔧 ServiceGuardEventHandler 保活动作执行完成
2025-08-18 16:25:25.440  4087-4087  WebSocket               com.dspread.mdm.service              I  🔧 重连 WebSocket: wss://api.dspreadserv.net/status/websocket/register?serialNo=MDEyNDMwMzAyMDI0MTIwOTAxMDE=&publicKey=TUlHZk1BMEdDU3FHU0liM0RRRUJBUVVBQTRHTkFEQ0JpUUtCZ1FEclkrK0Rkc1NNY2xTU2xzWDJoOW5HRzM2TnB3V1cxazgxcGV2YVRsQmIwZFJ3TVdJWE9PZTZ0eGExdG5rUDI4dXJzK1RpczdiMVVPSC9UM3Iyd3ZDd1lTaXpwdC85RG1TVGZoa0gvSzk4WENzREtSNXU4eHlLMFNxenBBSExFWDN0cTdoNUhQU2NJbGo4QUZHdmo2TitoN0RaOTh1QXlwcWNiYnppKzZnS2RRSURBUUFC&query=0&msgVer=3&timestamp=1755505525429&signature=2qVAsO9YeK7xsxqjjqN8UzBcxpHxqzUxuaG6m9uFHXzecbdjQ01mAs1C6iBxnB3V23kixb9CUBeooKbizq7/d1iNvxUs1wZ/hzy0jT1wczLufMaeGmqa5IoSMpr9e4gCu6xd0msdEgFLrn6XoUs2nnK9pVhgcNa7z7yiID6u7qc=
2025-08-18 16:25:25.441  4087-4087  WebSocket               com.dspread.mdm.service              I  🔧 未检测到系统代理设置
2025-08-18 16:25:25.549  4087-4548  System.out              com.dspread.mdm.service              I  [socket]:check permission begin!
2025-08-18 16:25:25.800  4087-4549  System.out              com.dspread.mdm.service              I  [socket]:check permission begin!
2025-08-18 16:25:27.225  4087-4551  Common                  com.dspread.mdm.service              I  ✅ WebSocket 物理连接成功，等待服务器 hellMsg
2025-08-18 16:25:27.225  4087-4551  WebSocket               com.dspread.mdm.service              I  🔧 ===== 心跳发送器实际配置 =====
2025-08-18 16:25:27.226  4087-4551  WebSocket               com.dspread.mdm.service              I  🔧 实际 PING 发送间隔: 60000ms (60秒)
2025-08-18 16:25:27.227  4087-4551  WebSocket               com.dspread.mdm.service              I  🔧 PING 定时器名称: PingSender
2025-08-18 16:25:27.227  4087-4551  WebSocket               com.dspread.mdm.service              I  🔧 实际 PONG 发送间隔: 0ms (0秒)
2025-08-18 16:25:27.228  4087-4551  WebSocket               com.dspread.mdm.service              I  🔧 PONG 定时器名称: PongSender
2025-08-18 16:25:27.228  4087-4551  WebSocket               com.dspread.mdm.service              I  🔧 ===============================
2025-08-18 16:25:27.362  4087-4550  Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"hellMsg":"hello 01243030202412090101","tranCode":"S0000","version":"1","rebootTime":"02:50:39","serialNo":"01243030202412090101","deviceStatus":"6"}
2025-08-18 16:25:27.362  4087-4550  Common                  com.dspread.mdm.service              I  ✅ 收到服务器 hellMsg，WebSocket 业务连接成功！
2025-08-18 16:25:27.363  4087-4550  Common                  com.dspread.mdm.service              I  ✅ WebSocket 业务连接成功！
2025-08-18 16:25:27.363  4087-4550  WebSocket               com.dspread.mdm.service              I  🔧 业务连接成功，重连计数已重置为 0
2025-08-18 16:25:27.368  4087-4550  Common                  com.dspread.mdm.service              I  ✅ ⏰ 设置任务执行定时器成功，下次执行: 60秒后 (1分钟)
2025-08-18 16:25:27.369  4087-4550  WebSocket               com.dspread.mdm.service              I  🔧 已启动任务执行定时器
2025-08-18 16:25:27.374  4087-4550  Common                  com.dspread.mdm.service              D  🔧 TimerConfig 使用Provisioning配置的心跳间隔: 300秒
2025-08-18 16:25:27.378  4087-4126  Common                  com.dspread.mdm.service              D  🔧 ServiceGuardEventHandler WakeLock已释放
2025-08-18 16:25:27.379  4087-4550  Common                  com.dspread.mdm.service              I  ✅ ⏰ 设置心跳定时器成功，下次执行: 300秒后 (5分钟)
2025-08-18 16:25:27.379  4087-4550  WebSocket               com.dspread.mdm.service              I  🔧 已启动心跳定时器
2025-08-18 16:25:27.384  4087-4550  Common                  com.dspread.mdm.service              D  🔧 TimerConfig 使用Provisioning配置的终端信息上传间隔: 900秒
2025-08-18 16:25:27.388  4087-4550  Common                  com.dspread.mdm.service              I  ✅ ⏰ 设置终端信息定时器成功，下次执行: 900秒后 (15分钟)
2025-08-18 16:25:27.389  4087-4550  WebSocket               com.dspread.mdm.service              I  🔧 已启动终端信息上传定时器
2025-08-18 16:25:27.389  4087-4550  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接成功，检查自身更新任务状态
2025-08-18 16:25:27.391  4087-4550  ContextImpl             com.dspread.mdm.service              W  Calling a method in the system process without a qualified user: android.app.ContextImpl.sendBroadcast:1046 android.content.ContextWrapper.sendBroadcast:448 com.dspread.mdm.service.broadcast.core.BroadcastSender.sendBroadcast:99 com.dspread.mdm.service.broadcast.core.BroadcastSender.sendBroadcast:23 com.dspread.mdm.service.network.websocket.connection.WsConnectionManager.checkSelfUpdateTaskStatus:311 
2025-08-18 16:25:27.393   961-2388  ActivityManager         system_server                        E  Sending non-protected broadcast com.dspread.mdm.service.CHECK_SELF_UPDATE_STATUS from system 4087:com.dspread.mdm.service/1000 pkg com.dspread.mdm.service (Ask Gemini)
                                                                                                    java.lang.Throwable
                                                                                                    	at com.android.server.am.ActivityManagerService.checkBroadcastFromSystem(ActivityManagerService.java:15078)
                                                                                                    	at com.android.server.am.ActivityManagerService.broadcastIntentLocked(ActivityManagerService.java:15625)
                                                                                                    	at com.android.server.am.ActivityManagerService.broadcastIntentLocked(ActivityManagerService.java:15095)
                                                                                                    	at com.android.server.am.ActivityManagerService.broadcastIntent(ActivityManagerService.java:15878)
                                                                                                    	at android.app.IActivityManager$Stub.onTransact(IActivityManager.java:2071)
                                                                                                    	at com.android.server.am.ActivityManagerService.onTransact(ActivityManagerService.java:2886)
                                                                                                    	at android.os.Binder.execTransactInternal(Binder.java:1021)
                                                                                                    	at android.os.Binder.execTransact(Binder.java:994)
2025-08-18 16:25:27.407  4087-4550  Receiver                com.dspread.mdm.service              D  🔧 发送广播: com.dspread.mdm.service.CHECK_SELF_UPDATE_STATUS
2025-08-18 16:25:27.407  4087-4087  Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 收到自身更新状态检查广播: com.dspread.mdm.service.CHECK_SELF_UPDATE_STATUS
2025-08-18 16:25:27.408  4087-4087  Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 开始执行自身更新任务状态检查
2025-08-18 16:25:27.408  4087-4550  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接成功
2025-08-18 16:25:27.408  4087-4550  WebSocket               com.dspread.mdm.service              I  🔧 设备状态: 6
2025-08-18 16:25:27.408  4087-4550  WebSocket               com.dspread.mdm.service              I  🔧 检查首次连接标志: isFirstTimeWebSocketConnected=false
2025-08-18 16:25:27.409  4087-4087  Task                    com.dspread.mdm.service              D  🔧 获取待执行任务数量: 0
2025-08-18 16:25:27.409  4087-4550  WebSocket               com.dspread.mdm.service              I  🔧 非首次连接，跳过初始化信息发送和初始化检查
2025-08-18 16:25:27.409  4087-4087  Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 待执行任务数量: 0
2025-08-18 16:25:27.410  4087-4550  WebSocket               com.dspread.mdm.service              I  🔧 执行必要的状态同步检查
2025-08-18 16:25:27.410  4087-4550  WebSocket               com.dspread.mdm.service              I  🔧 检查服务更新结果
2025-08-18 16:25:27.411  4087-4550  Task                    com.dspread.mdm.service              I  ℹ️ 本地任务列表长度: 1
2025-08-18 16:25:27.412  4087-4550  Task                    com.dspread.mdm.service              D  🔧 本地任务列表内容: [{"apkMd5":"9f20f245b9123ce2afac04491bf4d9bc","apkName":"OpenVPN for Android","endDate":"9999-12-31 23:59:59","installBy":"0","versionName":"0.5.36a","versionCode":"65","url":"https:\/\/smartms.s3.sa-east-1.amazonaws.com\/app\/67b1a89781624afea3817778d3efaa48.apk","beginDate":"2024-08-18 08:11:47","taskType":"01","appId":"a20c98aa13384b8ea3c77c2b431e0c43","apkSize":"1622936","appIconUrl":"https:\/\/smartms.s3.sa-east-1.amazonaws.com\/icon\/57495b8a8d0441c5bbdedce4fd380acficon.png","packName":"de.blinkt.openvpn","taskId":"7635d8b800e640c39a7dcb0851a6525e&a20c98aa13384b8ea3c77c2b431e0c43","request_id":"1755504707141ST001","request_time":"1755504707141","silent_install":"","taskResult":"B03","lastUpdateTime":1755505414886}]
2025-08-18 16:25:27.415  4087-4550  Task                    com.dspread.mdm.service              I  ℹ️ 当前应用版本: versionCode=13, versionName=1.0.10.20250818.DSPREAD.MDM.SERVICE
2025-08-18 16:25:27.415  4087-4550  Task                    com.dspread.mdm.service              I  ℹ️ 任务列表数量: 1
2025-08-18 16:25:27.416  4087-4550  Task                    com.dspread.mdm.service              D  🔧 检查任务[0]: taskId=7635d8b800e640c39a7dcb0851a6525e&a20c98aa13384b8ea3c77c2b431e0c43, taskType=01, pkgName=de.blinkt.openvpn, versionCode=65, versionName=0.5.36a, taskResult=B03
2025-08-18 16:25:27.416  4087-4550  WebSocket               com.dspread.mdm.service              I  🔧 检查OS升级状态...
2025-08-18 16:25:27.417  4087-4550  Common                  com.dspread.mdm.service              D  🔧 OsUpdateStatusChecker WebSocket连接成功，检查OS升级状态
2025-08-18 16:25:27.417  4087-4550  Common                  com.dspread.mdm.service              D  🔧 OsUpdateStatusChecker 没有待检查的OS升级任务
2025-08-18 16:25:27.418  4087-4550  WebSocket               com.dspread.mdm.service              I  🔧 OS升级状态检查已触发
2025-08-18 16:25:54.386  4087-4087  Platform                com.dspread.mdm.service              D  🔧 WakeLockManager WakeLock不存在或未持有: ServiceKeepAlive
2025-08-18 16:25:54.387  4087-4087  Service                 com.dspread.mdm.service              D  🔧 ServiceKeepAliveService 保活任务完成，释放WakeLock
2025-08-18 16:26:00.356  4087-4087  Receiver                com.dspread.mdm.service              D  🔧 电池状态更新: 电池状态: 电量=44%, 温度=34.9°C, 健康=2, 充电=是, 低电量=否, 耗尽=否
2025-08-18 16:26:11.465  4087-4087  Receiver                com.dspread.mdm.service              D  🔧 电池状态更新: 电池状态: 电量=44%, 温度=35.0°C, 健康=2, 充电=是, 低电量=否, 耗尽=否
2025-08-18 16:26:27.226  4087-4551  WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 1
2025-08-18 16:26:27.227  4087-4550  WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误 (Ask Gemini)
                                                                                                    com.neovisionaries.ws.client.NoMoreFrameException: No more WebSocket frame from the server.
                                                                                                    	at com.neovisionaries.ws.client.WebSocketInputStream.readFrame(WebSocketInputStream.java:54)
                                                                                                    	at com.neovisionaries.ws.client.ReadingThread.readFrame(ReadingThread.java:338)
                                                                                                    	at com.neovisionaries.ws.client.ReadingThread.main(ReadingThread.java:99)
                                                                                                    	at com.neovisionaries.ws.client.ReadingThread.runMain(ReadingThread.java:64)
                                                                                                    	at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45)
2025-08-18 16:26:27.228  4087-4550  WebSocket               com.dspread.mdm.service              I  🔧 onError: 不触发重连，等待 onDisconnected 处理
2025-08-18 16:26:27.232  4087-4551  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接断开 - 客户端主动断开
2025-08-18 16:26:27.232  4087-4551  WebSocket               com.dspread.mdm.service              I  🔧    服务器关闭帧: code=-1, reason=无原因
2025-08-18 16:26:27.232  4087-4551  WebSocket               com.dspread.mdm.service              I  🔧    客户端关闭帧: code=1002, reason=No more WebSocket frame from the server.
2025-08-18 16:26:27.233  4087-4551  WebSocket               com.dspread.mdm.service              I  🔧    断开发起方: 客户端
2025-08-18 16:26:27.233  4087-4551  WebSocket               com.dspread.mdm.service              I  🔧 reconnect() 被调用，调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onDisconnected:727
2025-08-18 16:26:27.234  4087-4551  WebSocket               com.dspread.mdm.service              I  🔧 开始重连 WebSocket... (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onDisconnected:727)
2025-08-18 16:26:27.238  4087-4551  WebSocket               com.dspread.mdm.service              I  🔧 使用Provisioning配置的重连策略: delayPolicy=30, delaySwitch=1, delayTime=900
2025-08-18 16:26:27.238  4087-4551  WebSocket               com.dspread.mdm.service              I  🔧 当前断开记录数: 2, 延迟开关: 1
2025-08-18 16:26:27.239  4087-4551  WebSocket               com.dspread.mdm.service              I  🔧 重连策略计算完成: 第1次重连，间隔3000ms (3秒)
2025-08-18 16:26:27.240  4087-4551  WebSocket               com.dspread.mdm.service              I  🔧 开始第1次重连，间隔3000ms (3秒) (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onDisconnected:727)
2025-08-18 16:26:27.367   961-1047  ActivityManager         system_server                        E  Sending non-protected broadcast com.dspread.mdm.service.WSTASK_EXEC_BC from system uid 1000 pkg com.dspread.mdm.service (Ask Gemini)
                                                                                                    java.lang.Throwable
                                                                                                    	at com.android.server.am.ActivityManagerService.checkBroadcastFromSystem(ActivityManagerService.java:15082)
                                                                                                    	at com.android.server.am.ActivityManagerService.broadcastIntentLocked(ActivityManagerService.java:15719)
                                                                                                    	at com.android.server.am.ActivityManagerService.broadcastIntentInPackage(ActivityManagerService.java:15916)
                                                                                                    	at com.android.server.am.ActivityManagerService$LocalService.broadcastIntentInPackage(ActivityManagerService.java:18556)
                                                                                                    	at com.android.server.am.PendingIntentRecord.sendInner(PendingIntentRecord.java:432)
                                                                                                    	at com.android.server.am.PendingIntentRecord.sendWithResult(PendingIntentRecord.java:276)
                                                                                                    	at com.android.server.am.ActivityManagerService.sendIntentSender(ActivityManagerService.java:5633)
                                                                                                    	at android.app.PendingIntent.sendAndReturnResult(PendingIntent.java:896)
                                                                                                    	at android.app.PendingIntent.send(PendingIntent.java:878)
                                                                                                    	at com.android.server.AlarmManagerService$DeliveryTracker.deliverLocked(AlarmManagerService.java:4760)
                                                                                                    	at com.android.server.AlarmManagerService.deliverAlarmsLocked(AlarmManagerService.java:3899)
                                                                                                    	at com.android.server.AlarmManagerService$AlarmThread.run(AlarmManagerService.java:4160)
2025-08-18 16:26:27.387  4087-4087  Common                  com.dspread.mdm.service              I  ✅ ⏰ 设置任务执行定时器成功，下次执行: 60秒后 (1分钟)
2025-08-18 16:26:30.257  4087-4087  WebSocket               com.dspread.mdm.service              I  🔧 重连 WebSocket: wss://api.dspreadserv.net/status/websocket/register?serialNo=MDEyNDMwMzAyMDI0MTIwOTAxMDE=&publicKey=TUlHZk1BMEdDU3FHU0liM0RRRUJBUVVBQTRHTkFEQ0JpUUtCZ1FEclkrK0Rkc1NNY2xTU2xzWDJoOW5HRzM2TnB3V1cxazgxcGV2YVRsQmIwZFJ3TVdJWE9PZTZ0eGExdG5rUDI4dXJzK1RpczdiMVVPSC9UM3Iyd3ZDd1lTaXpwdC85RG1TVGZoa0gvSzk4WENzREtSNXU4eHlLMFNxenBBSExFWDN0cTdoNUhQU2NJbGo4QUZHdmo2TitoN0RaOTh1QXlwcWNiYnppKzZnS2RRSURBUUFC&query=0&msgVer=3&timestamp=1755505590246&signature=IHIvA+/vW23kA0U5GMKegzp9t3U6hUDw2ePeO5c+QgfNFuqfKZT1ySjlR56bnPU5UFxha5dz5FjxTBgt6jbY9Exi6ZjnhJit2iFybSSuH9ZeBldL1sfP4hlZv7PvtTG1G0yDEs3ZE9RQzp6nzmyX5BCXse3348rMFJFdHK4EmSU=
2025-08-18 16:26:30.258  4087-4087  WebSocket               com.dspread.mdm.service              I  🔧 未检测到系统代理设置
2025-08-18 16:26:30.369  4087-4562  System.out              com.dspread.mdm.service              I  [socket]:check permission begin!
2025-08-18 16:26:30.619  4087-4563  System.out              com.dspread.mdm.service              I  [socket]:check permission begin!
2025-08-18 16:26:31.838  4087-4564  Common                  com.dspread.mdm.service              I  ✅ WebSocket 物理连接成功，等待服务器 hellMsg
2025-08-18 16:26:31.839  4087-4564  WebSocket               com.dspread.mdm.service              I  🔧 ===== 心跳发送器实际配置 =====
2025-08-18 16:26:31.840  4087-4564  WebSocket               com.dspread.mdm.service              I  🔧 实际 PING 发送间隔: 60000ms (60秒)
2025-08-18 16:26:31.841  4087-4564  WebSocket               com.dspread.mdm.service              I  🔧 PING 定时器名称: PingSender
2025-08-18 16:26:31.841  4087-4564  WebSocket               com.dspread.mdm.service              I  🔧 实际 PONG 发送间隔: 0ms (0秒)
2025-08-18 16:26:31.842  4087-4564  WebSocket               com.dspread.mdm.service              I  🔧 PONG 定时器名称: PongSender
2025-08-18 16:26:31.842  4087-4564  WebSocket               com.dspread.mdm.service              I  🔧 ===============================
2025-08-18 16:26:31.875  4087-4564  Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"hellMsg":"hello 01243030202412090101","tranCode":"S0000","version":"1","rebootTime":"01:38:52","serialNo":"01243030202412090101","deviceStatus":"6"}
2025-08-18 16:26:31.875  4087-4564  Common                  com.dspread.mdm.service              I  ✅ 收到服务器 hellMsg，WebSocket 业务连接成功！
2025-08-18 16:26:31.876  4087-4564  Common                  com.dspread.mdm.service              I  ✅ WebSocket 业务连接成功！
2025-08-18 16:26:31.876  4087-4564  WebSocket               com.dspread.mdm.service              I  🔧 业务连接成功，重连计数已重置为 0
2025-08-18 16:26:31.880  4087-4564  Common                  com.dspread.mdm.service              I  ✅ ⏰ 设置任务执行定时器成功，下次执行: 60秒后 (1分钟)
2025-08-18 16:26:31.880  4087-4564  WebSocket               com.dspread.mdm.service              I  🔧 已启动任务执行定时器
2025-08-18 16:26:31.885  4087-4564  Common                  com.dspread.mdm.service              D  🔧 TimerConfig 使用Provisioning配置的心跳间隔: 300秒
2025-08-18 16:26:31.889  4087-4564  Common                  com.dspread.mdm.service              I  ✅ ⏰ 设置心跳定时器成功，下次执行: 300秒后 (5分钟)
2025-08-18 16:26:31.890  4087-4564  WebSocket               com.dspread.mdm.service              I  🔧 已启动心跳定时器
2025-08-18 16:26:31.894  4087-4564  Common                  com.dspread.mdm.service              D  🔧 TimerConfig 使用Provisioning配置的终端信息上传间隔: 900秒
2025-08-18 16:26:31.898  4087-4564  Common                  com.dspread.mdm.service              I  ✅ ⏰ 设置终端信息定时器成功，下次执行: 900秒后 (15分钟)
2025-08-18 16:26:31.899  4087-4564  WebSocket               com.dspread.mdm.service              I  🔧 已启动终端信息上传定时器
2025-08-18 16:26:31.900  4087-4564  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接成功，检查自身更新任务状态
2025-08-18 16:26:31.901  4087-4564  ContextImpl             com.dspread.mdm.service              W  Calling a method in the system process without a qualified user: android.app.ContextImpl.sendBroadcast:1046 android.content.ContextWrapper.sendBroadcast:448 com.dspread.mdm.service.broadcast.core.BroadcastSender.sendBroadcast:99 com.dspread.mdm.service.broadcast.core.BroadcastSender.sendBroadcast:23 com.dspread.mdm.service.network.websocket.connection.WsConnectionManager.checkSelfUpdateTaskStatus:311 
2025-08-18 16:26:31.903   961-2287  ActivityManager         system_server                        E  Sending non-protected broadcast com.dspread.mdm.service.CHECK_SELF_UPDATE_STATUS from system 4087:com.dspread.mdm.service/1000 pkg com.dspread.mdm.service (Ask Gemini)
                                                                                                    java.lang.Throwable
                                                                                                    	at com.android.server.am.ActivityManagerService.checkBroadcastFromSystem(ActivityManagerService.java:15078)
                                                                                                    	at com.android.server.am.ActivityManagerService.broadcastIntentLocked(ActivityManagerService.java:15625)
                                                                                                    	at com.android.server.am.ActivityManagerService.broadcastIntentLocked(ActivityManagerService.java:15095)
                                                                                                    	at com.android.server.am.ActivityManagerService.broadcastIntent(ActivityManagerService.java:15878)
                                                                                                    	at android.app.IActivityManager$Stub.onTransact(IActivityManager.java:2071)
                                                                                                    	at com.android.server.am.ActivityManagerService.onTransact(ActivityManagerService.java:2886)
                                                                                                    	at android.os.Binder.execTransactInternal(Binder.java:1021)
                                                                                                    	at android.os.Binder.execTransact(Binder.java:994)
2025-08-18 16:26:31.918  4087-4564  Receiver                com.dspread.mdm.service              D  🔧 发送广播: com.dspread.mdm.service.CHECK_SELF_UPDATE_STATUS
2025-08-18 16:26:31.919  4087-4087  Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 收到自身更新状态检查广播: com.dspread.mdm.service.CHECK_SELF_UPDATE_STATUS
2025-08-18 16:26:31.919  4087-4087  Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 开始执行自身更新任务状态检查
2025-08-18 16:26:31.919  4087-4564  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接成功
2025-08-18 16:26:31.920  4087-4564  WebSocket               com.dspread.mdm.service              I  🔧 设备状态: 6
2025-08-18 16:26:31.920  4087-4564  WebSocket               com.dspread.mdm.service              I  🔧 检查首次连接标志: isFirstTimeWebSocketConnected=false
2025-08-18 16:26:31.920  4087-4087  Task                    com.dspread.mdm.service              D  🔧 获取待执行任务数量: 0
2025-08-18 16:26:31.921  4087-4087  Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 待执行任务数量: 0
2025-08-18 16:26:31.921  4087-4564  WebSocket               com.dspread.mdm.service              I  🔧 非首次连接，跳过初始化信息发送和初始化检查
2025-08-18 16:26:31.922  4087-4564  WebSocket               com.dspread.mdm.service              I  🔧 执行必要的状态同步检查
2025-08-18 16:26:31.922  4087-4564  WebSocket               com.dspread.mdm.service              I  🔧 检查服务更新结果
2025-08-18 16:26:31.923  4087-4564  Task                    com.dspread.mdm.service              I  ℹ️ 本地任务列表长度: 1
2025-08-18 16:26:31.924  4087-4564  Task                    com.dspread.mdm.service              D  🔧 本地任务列表内容: [{"apkMd5":"9f20f245b9123ce2afac04491bf4d9bc","apkName":"OpenVPN for Android","endDate":"9999-12-31 23:59:59","installBy":"0","versionName":"0.5.36a","versionCode":"65","url":"https:\/\/smartms.s3.sa-east-1.amazonaws.com\/app\/67b1a89781624afea3817778d3efaa48.apk","beginDate":"2024-08-18 08:11:47","taskType":"01","appId":"a20c98aa13384b8ea3c77c2b431e0c43","apkSize":"1622936","appIconUrl":"https:\/\/smartms.s3.sa-east-1.amazonaws.com\/icon\/57495b8a8d0441c5bbdedce4fd380acficon.png","packName":"de.blinkt.openvpn","taskId":"7635d8b800e640c39a7dcb0851a6525e&a20c98aa13384b8ea3c77c2b431e0c43","request_id":"1755504707141ST001","request_time":"1755504707141","silent_install":"","taskResult":"B03","lastUpdateTime":1755505414886}]
2025-08-18 16:26:31.927  4087-4564  Task                    com.dspread.mdm.service              I  ℹ️ 当前应用版本: versionCode=13, versionName=1.0.10.20250818.DSPREAD.MDM.SERVICE
2025-08-18 16:26:31.928  4087-4564  Task                    com.dspread.mdm.service              I  ℹ️ 任务列表数量: 1
2025-08-18 16:26:31.928  4087-4564  Task                    com.dspread.mdm.service              D  🔧 检查任务[0]: taskId=7635d8b800e640c39a7dcb0851a6525e&a20c98aa13384b8ea3c77c2b431e0c43, taskType=01, pkgName=de.blinkt.openvpn, versionCode=65, versionName=0.5.36a, taskResult=B03
2025-08-18 16:26:31.929  4087-4564  WebSocket               com.dspread.mdm.service              I  🔧 检查OS升级状态...
2025-08-18 16:26:31.930  4087-4564  Common                  com.dspread.mdm.service              D  🔧 OsUpdateStatusChecker WebSocket连接成功，检查OS升级状态
2025-08-18 16:26:31.931  4087-4564  Common                  com.dspread.mdm.service              D  🔧 OsUpdateStatusChecker 没有待检查的OS升级任务
2025-08-18 16:26:31.931  4087-4564  WebSocket               com.dspread.mdm.service              I  🔧 OS升级状态检查已触发
