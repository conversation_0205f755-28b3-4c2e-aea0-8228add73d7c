package com.dspread.mdm.service.network.websocket.task

import android.content.Context
import android.content.SharedPreferences
import android.content.pm.PackageManager
import com.dspread.mdm.service.network.websocket.message.WsMessageSender
import com.dspread.mdm.service.network.websocket.connection.WsConnectionManager
import com.dspread.mdm.service.utils.log.Logger
import com.dspread.mdm.service.constants.TaskStateConstants
import org.json.JSONArray
import org.json.JSONObject
import java.text.SimpleDateFormat
import java.util.*
import java.util.concurrent.ConcurrentHashMap

/**
 * WebSocket 任务管理器
 *  WebSocketTaskListManager 的 Kotlin 实现
 */
object WsTaskManager {

    private const val TAG = "WsTaskManager"

    // SharedPreferences 键名
    private const val SP_NAME = "ws_task_manager"
    private const val SP_KEY_TASK_LIST = "websocket_task_list"
    private const val SP_KEY_TASK_RESULT_LIST = "websocket_task_execute_result_list"
    private const val SP_KEY_RULE_LIST = "websocket_rule_list"
    private const val SP_KEY_RULE_RESULT_LIST = "websocket_rule_execute_result_list"

    // 任务状态常量（ TaskState）
    private const val TASK_STATE_TODO = "TODO"
    private const val TASK_STATE_DOING = "DOING"
    private const val TASK_STATE_SUCCESS = "SUCCESS"
    private const val TASK_STATE_FAILED = "FAILED"

    // 内存缓存
    private val taskMap = ConcurrentHashMap<String, JSONObject>()
    private val taskResultMap = ConcurrentHashMap<String, JSONObject>()
    private val ruleMap = ConcurrentHashMap<String, JSONObject>()
    private val ruleResultMap = ConcurrentHashMap<String, JSONObject>()

    // SharedPreferences
    private var sharedPreferences: SharedPreferences? = null
    private var context: Context? = null

    /**
     * 初始化任务管理器
     */
    fun init(context: Context) {
        // 使用 ApplicationContext 避免内存泄漏
        this.context = context.applicationContext
        this.sharedPreferences = context.applicationContext.getSharedPreferences(SP_NAME, Context.MODE_PRIVATE)

        // 从持久化存储加载任务
        loadTasksFromStorage()
        loadRulesFromStorage()

        // 启动任务状态超时检测
        startTaskTimeoutCheck()

        Logger.wsm("WsTaskManager 初始化完成")
    }

    /**
     * 更新 WebSocket 任务列表
     */
    fun updateWSTaskList(
        requestId: String,
        requestTime: String,
        taskList: JSONArray,
        silentInstall: String = ""
    ) {
        try {
            Logger.taskI("更新任务列表: requestId=$requestId, 任务数量=${taskList.length()}")

            // 与本地任务列表进行对比、更新
            val localTaskList = getLocalTaskList()

            for (i in 0 until taskList.length()) {
                val newTask = taskList.getJSONObject(i)
                val taskId = newTask.optString("taskId")

                if (taskId.isNotEmpty()) {
                    // 添加请求信息到任务中
                    newTask.put("request_id", requestId)
                    newTask.put("request_time", requestTime)
                    newTask.put("silent_install", silentInstall)

                    // 检查是否已存在
                    var isExisting = false
                    for (j in 0 until localTaskList.length()) {
                        val localTask = localTaskList.getJSONObject(j)
                        if (taskId == localTask.optString("taskId")) {
                            // 更新现有任务
                            localTaskList.put(j, newTask)
                            isExisting = true
                            break
                        }
                    }

                    if (!isExisting) {
                        // 添加新任务 - 按时间顺序插入
                        insertTaskByTime(localTaskList, newTask)
                    }

                    // 更新内存缓存
                    taskMap[taskId] = newTask
                    Logger.task("添加/更新任务: taskId=$taskId")
                }
            }

            // 清理已完成的任务
            cleanupCompletedTasks(localTaskList)

            // 保存到持久化存储
            saveTaskListToStorage(localTaskList)

        } catch (e: Exception) {
            Logger.taskE("更新任务列表失败", e)
        }
    }

    /**
     * 按时间顺序插入任务
     */
    private fun insertTaskByTime(taskList: JSONArray, newTask: JSONObject) {
        try {
            val newTaskTime = newTask.optLong("request_time", 0L)
            var insertIndex = taskList.length()

            // 找到合适的插入位置（按request_time升序排列）
            for (i in 0 until taskList.length()) {
                val existingTask = taskList.getJSONObject(i)
                val existingTaskTime = existingTask.optLong("request_time", 0L)

                if (newTaskTime < existingTaskTime) {
                    insertIndex = i
                    break
                }
            }

            // 如果需要插入到中间位置，需要重新构建数组
            if (insertIndex < taskList.length()) {
                val tempList = mutableListOf<JSONObject>()

                // 复制插入位置之前的任务
                for (i in 0 until insertIndex) {
                    tempList.add(taskList.getJSONObject(i))
                }

                // 插入新任务
                tempList.add(newTask)

                // 复制插入位置之后的任务
                for (i in insertIndex until taskList.length()) {
                    tempList.add(taskList.getJSONObject(i))
                }

                // 清空原数组并重新填充
                while (taskList.length() > 0) {
                    taskList.remove(0)
                }

                tempList.forEach { task ->
                    taskList.put(task)
                }

                Logger.task("任务按时间插入到位置: $insertIndex, taskId=${newTask.optString("taskId")}")
            } else {
                // 插入到末尾
                taskList.put(newTask)
                Logger.task("任务插入到末尾: taskId=${newTask.optString("taskId")}")
            }

        } catch (e: Exception) {
            Logger.taskE("按时间插入任务失败", e)
            // 失败时直接添加到末尾
            taskList.put(newTask)
        }
    }

    /**
     * 清理已完成的任务
     */
    private fun cleanupCompletedTasks(taskList: JSONArray) {
        try {
            val completedTasks = mutableListOf<Int>()

            // 找出所有已完成的任务
            for (i in 0 until taskList.length()) {
                val task = taskList.getJSONObject(i)
                val taskResult = task.optString("taskResult", "")
                val taskId = task.optString("taskId", "")

                // 检查是否是终态任务
                if (isTaskCompleted(task)) {
                    completedTasks.add(i)
                    Logger.task("发现已完成任务: taskId=$taskId, result=$taskResult")
                }
            }

            // 从后往前删除，避免索引变化
            completedTasks.reversed().forEach { index ->
                val task = taskList.getJSONObject(index)
                val taskId = task.optString("taskId", "")
                val taskResult = task.optString("taskResult", "")

                // 上送C0108任务结果
                uploadTaskResultIfNeeded(task)

                // 从任务列表中删除
                taskList.remove(index)

                // 从内存缓存中删除
                taskMap.remove(taskId)
                taskResultMap.remove(taskId)

                Logger.task("清理已完成任务: taskId=$taskId, result=$taskResult")
            }

            if (completedTasks.isNotEmpty()) {
                Logger.task("清理了 ${completedTasks.size} 个已完成任务")
            }

        } catch (e: Exception) {
            Logger.taskE("清理已完成任务失败", e)
        }
    }



    /**
     * 如需要则上送任务结果
     */
    private fun uploadTaskResultIfNeeded(task: JSONObject) {
        try {
            val taskId = task.optString("taskId", "")
            val taskResult = task.optString("taskResult", "")

            // 检查是否已经上送过结果
            val lastUploadTime = task.optLong("lastUploadTime", 0L)
            val currentTime = System.currentTimeMillis()

            // 如果超过5分钟没有上送，重新上送
            if (currentTime - lastUploadTime > 5 * 60 * 1000) {
                Logger.task("上送任务结果: taskId=$taskId, result=$taskResult")

                // 这里应该调用实际的上送方法
                // WsMessageSender.uploadTaskResult(taskId, taskResult, null)

                // 更新上送时间
                task.put("lastUploadTime", currentTime)
            }

        } catch (e: Exception) {
            Logger.taskE("上送任务结果失败", e)
        }
    }

    /**
     * 根据任务ID获取任务对象
     */
    fun getWSTaskJsonObjById(taskId: String): JSONObject? {
        return taskMap[taskId]
    }

    /**
     * 添加任务结果
     */
    fun addWSTaskResultJsonObj(taskId: String, resultJsonObj: JSONObject) {
        try {
            taskResultMap[taskId] = resultJsonObj
            Logger.task("添加任务结果: taskId=$taskId")
        } catch (e: Exception) {
            Logger.taskE("添加任务结果失败", e)
        }
    }

    /**
     * 获取任务结果
     */
    fun getWSTaskResultJsonObj(taskId: String): JSONObject? {
        return taskResultMap[taskId]
    }

    /**
     * 更新规则列表
     */
    fun updateRuleList(
        requestId: String,
        requestTime: String,
        ruleList: JSONArray
    ) {
        try {
            Logger.wsm("更新规则列表: requestId=$requestId, 规则数量=${ruleList.length()}")
            
            for (i in 0 until ruleList.length()) {
                val rule = ruleList.getJSONObject(i)
                val ruleId = rule.optString("ruleId")
                
                if (ruleId.isNotEmpty()) {
                    // 添加请求信息到规则中
                    rule.put("request_id", requestId)
                    rule.put("request_time", requestTime)
                    
                    // 存储规则
                    ruleMap[ruleId] = rule
                    Logger.wsm("添加规则: ruleId=$ruleId")
                }
            }
            
        } catch (e: Exception) {
            Logger.wsmE("更新规则列表失败", e)
        }
    }

    /**
     * 根据规则ID获取规则对象
     */
    fun getRuleJsonObjById(ruleId: String): JSONObject? {
        return ruleMap[ruleId]
    }

    /**
     * 添加规则结果
     */
    fun addRuleResultJsonObj(ruleId: String, resultJsonObj: JSONObject) {
        try {
            ruleResultMap[ruleId] = resultJsonObj
            Logger.wsm("添加规则结果: ruleId=$ruleId")
        } catch (e: Exception) {
            Logger.wsmE("添加规则结果失败", e)
        }
    }

    /**
     * 获取规则结果
     */
    fun getRuleResultJsonObj(ruleId: String): JSONObject? {
        return ruleResultMap[ruleId]
    }

    /**
     * 移除任务
     */
    fun removeTask(taskId: String) {
        taskMap.remove(taskId)
        taskResultMap.remove(taskId)
        Logger.task("移除任务: taskId=$taskId")
    }

    /**
     * 移除规则
     */
    fun removeRule(ruleId: String) {
        ruleMap.remove(ruleId)
        ruleResultMap.remove(ruleId)
        Logger.wsm("移除规则: ruleId=$ruleId")
    }

    /**
     * 获取所有任务
     */
    fun getAllTasks(): Map<String, JSONObject> {
        return taskMap.toMap()
    }

    /**
     * 获取所有规则
     */
    fun getAllRules(): Map<String, JSONObject> {
        return ruleMap.toMap()
    }

    /**
     * 清空所有任务
     */
    fun clearAllTasks() {
        taskMap.clear()
        taskResultMap.clear()
        Logger.taskI("清空所有任务")
    }

    /**
     * 清空所有规则
     */
    fun clearAllRules() {
        ruleMap.clear()
        ruleResultMap.clear()
        Logger.wsm("清空所有规则")
    }

    /**
     * 获取下一个待执行任务（按时间顺序，带冲突检测）
     */
    fun getNextTodoWSTask(): JSONObject? {
        try {
            val localTaskList = getLocalTaskList()
            val nowTime = System.currentTimeMillis()
            val candidateTasks = mutableListOf<JSONObject>()

            // 收集所有符合条件的待执行任务
            for (i in 0 until localTaskList.length()) {
                val task = localTaskList.getJSONObject(i)

                if (isWSTaskTodo(task)) {
                    // 检查任务执行时间窗口
                    val beginDateStr = task.optString("beginDate")
                    val endDateStr = task.optString("endDate")

                    if (beginDateStr.isNotEmpty() && endDateStr.isNotEmpty()) {
                        val beginTime = parseDateTime(beginDateStr)
                        val endTime = parseDateTime(endDateStr)

                        if (nowTime >= beginTime && nowTime <= endTime) {
                            candidateTasks.add(task)
                        }
                    } else {
                        // 没有时间限制的任务
                        candidateTasks.add(task)
                    }
                }
            }

            if (candidateTasks.isEmpty()) {
                return null
            }

            // 按request_time排序（时间戳越小越早执行）
            candidateTasks.sortBy { it.optLong("request_time", 0L) }

            // 检查任务冲突并返回最合适的任务
            val selectedTask = selectTaskWithConflictResolution(candidateTasks)

            if (selectedTask != null) {
                val taskId = selectedTask.optString("taskId")
                val requestTime = selectedTask.optLong("request_time", 0L)
                Logger.task("找到待执行任务: $taskId (request_time: $requestTime)")
            }

            return selectedTask

        } catch (e: Exception) {
            Logger.taskE("获取下一个待执行任务失败", e)
            return null
        }
    }

    /**
     * 选择任务（严格按时间顺序，不跳过任务）
     */
    private fun selectTaskWithConflictResolution(candidateTasks: List<JSONObject>): JSONObject? {
        try {
            if (candidateTasks.isEmpty()) {
                return null
            }

            // 严格按时间顺序执行，返回时间最早的任务
            val earliestTask = candidateTasks.first() // 已经按时间排序了

            val taskId = earliestTask.optString("taskId")
            val taskType = earliestTask.optString("taskType")
            val packageName = earliestTask.optString("packName", "")
            val requestTime = earliestTask.optLong("request_time", 0L)

            // 检查是否有同包名的冲突任务（仅用于日志记录）
            val samePackageTasks = candidateTasks.filter {
                it.optString("packName", "") == packageName && packageName.isNotEmpty()
            }

            if (samePackageTasks.size > 1) {
                val installCount = samePackageTasks.count { it.optString("taskType") == "01" }
                val uninstallCount = samePackageTasks.count { it.optString("taskType") == "02" }

                Logger.task("检测到包 $packageName 有多个任务: 安装${installCount}个, 卸载${uninstallCount}个")
                Logger.task("按时间顺序执行，当前选择: $taskId ($taskType, request_time: $requestTime)")

                // 记录后续任务信息
                samePackageTasks.drop(1).forEach { task ->
                    val nextTaskId = task.optString("taskId")
                    val nextTaskType = task.optString("taskType")
                    val nextRequestTime = task.optLong("request_time", 0L)
                    Logger.task("后续任务: $nextTaskId ($nextTaskType, request_time: $nextRequestTime)")
                }
            }

            return earliestTask

        } catch (e: Exception) {
            Logger.taskE("任务选择失败", e)
            return candidateTasks.firstOrNull()
        }
    }

    /**
     * 更新任务状态 - 线程安全版本
     */
    @Synchronized
    fun updateWSTaskState(taskId: String, taskState: String) {
        try {
            val localTaskList = getLocalTaskList()

            for (i in 0 until localTaskList.length()) {
                val task = localTaskList.getJSONObject(i)
                if (taskId == task.optString("taskId")) {
                    task.put("taskResult", taskState)
                    Logger.task("更新任务状态: taskId=$taskId, state=$taskState")
                    break
                }
            }

            // 更新内存缓存
            taskMap[taskId]?.put("taskResult", taskState)

            // 保存到持久化存储
            saveTaskListToStorage(localTaskList)

        } catch (e: Exception) {
            Logger.taskE("更新任务状态失败", e)
        }
    }

    /**
     * 更新任务错误信息
     */
    @Synchronized
    fun updateTaskErrorMessage(taskId: String, errorMessage: String) {
        try {
            val localTaskList = getLocalTaskList()

            for (i in 0 until localTaskList.length()) {
                val task = localTaskList.getJSONObject(i)
                if (taskId == task.optString("taskId")) {
                    task.put("errorMessage", errorMessage)
                    Logger.task("更新任务错误信息: taskId=$taskId")
                    break
                }
            }

            // 更新内存缓存
            taskMap[taskId]?.put("errorMessage", errorMessage)

            // 保存到持久化存储
            saveTaskListToStorage(localTaskList)

        } catch (e: Exception) {
            Logger.taskE("更新任务错误信息失败", e)
        }
    }

    /**
     * 更新任务最后更新时间
     */
    @Synchronized
    fun updateTaskLastUpdateTime(taskId: String) {
        try {
            val currentTime = System.currentTimeMillis()
            val localTaskList = getLocalTaskList()

            for (i in 0 until localTaskList.length()) {
                val task = localTaskList.getJSONObject(i)
                if (taskId == task.optString("taskId")) {
                    task.put("lastUpdateTime", currentTime)
                    break
                }
            }

            // 更新内存缓存
            taskMap[taskId]?.put("lastUpdateTime", currentTime)

            // 保存到持久化存储
            saveTaskListToStorage(localTaskList)

        } catch (e: Exception) {
            Logger.taskE("更新任务最后更新时间失败", e)
        }
    }

    /**
     * 获取所有待执行任务（用于自身更新状态检查）
     */
    fun getAllPendingTasks(): List<JSONObject> {
        return try {
            val localTaskList = getLocalTaskList()
            val pendingTasks = mutableListOf<JSONObject>()

            for (i in 0 until localTaskList.length()) {
                val task = localTaskList.getJSONObject(i)
                val taskResult = task.optString("taskResult", "")

                // 查找待执行的任务（todo状态）
                if ("todo" == taskResult || taskResult.isEmpty()) {
                    pendingTasks.add(task)
                }
            }

            Logger.task("获取待执行任务数量: ${pendingTasks.size}")
            pendingTasks

        } catch (e: Exception) {
            Logger.taskE("获取待执行任务失败", e)
            emptyList()
        }
    }

    /**
     * 设置任务为删除状态
     */
    fun setWSTaskToDelete(taskId: String) {
        try {
            val localTaskList = getLocalTaskList()

            for (i in 0 until localTaskList.length()) {
                val task = localTaskList.getJSONObject(i)
                if (taskId == task.optString("taskId")) {
                    task.put("delete", "1") // 删除标志
                    Logger.wsm("设置任务删除标志: taskId=$taskId")
                    break
                }
            }

            // 保存到持久化存储
            saveTaskListToStorage(localTaskList)

        } catch (e: Exception) {
            Logger.wsmE("设置任务删除标志失败", e)
        }
    }

    /**
     * 从本地任务列表中物理删除任务
     */
    fun removeWSTaskJsonObjById(taskId: String) {
        try {
            val localTaskList = getLocalTaskList()
            val newTaskList = JSONArray()

            // 重新构建任务列表，排除要删除的任务
            for (i in 0 until localTaskList.length()) {
                val task = localTaskList.getJSONObject(i)
                if (taskId != task.optString("taskId")) {
                    newTaskList.put(task)
                }
            }

            // 保存新的任务列表
            saveTaskListToStorage(newTaskList)

            // 从内存缓存中移除
            taskMap.remove(taskId)
            taskResultMap.remove(taskId)

            Logger.wsm("从本地任务列表中删除任务: taskId=$taskId")

        } catch (e: Exception) {
            Logger.wsmE("删除任务失败", e)
        }
    }

    /**
     * 判断任务是否待执行
     */
    private fun isWSTaskTodo(task: JSONObject): Boolean {
        return try {
            val taskResult = task.optString("taskResult")

            // 使用正确的TaskState常量，明确定义待执行状态
            when (taskResult) {
                "", TASK_STATE_TODO -> true  // 空字符串或TODO状态表示待执行
                TaskStateConstants.DOWNLOAD_ING, TaskStateConstants.INSTALL_ING,
                TaskStateConstants.UPDATE_ING, TaskStateConstants.UNINSTALL_ING -> false  // 执行中状态不重复执行
                TaskStateConstants.DOWNLOAD_SUCCESS,
                TaskStateConstants.INSTALL_SUCCESS, TaskStateConstants.INSTALL_FAILED,
                TaskStateConstants.UPDATE_SUCCESS, TaskStateConstants.UPDATE_FAILED,
                TaskStateConstants.UNINSTALL_SUCCESS, TaskStateConstants.UNINSTALL_FAILED -> false  // 已完成状态不重复执行
                TaskStateConstants.DOWNLOAD_FAILED -> {
                    val taskType = task.optString("taskType")
                    taskType == "04" || taskType == "03"  // OS更新任务和SP下载任务的下载失败可以重试
                }
                else -> false  // 其他状态默认不执行
            }
        } catch (e: Exception) {
            false
        }
    }

    /**
     * 解析日期时间字符串
     */
    private fun parseDateTime(dateTimeStr: String): Long {
        return try {
            val format = SimpleDateFormat("yyyyMMddHHmmss", Locale.getDefault())
            format.parse(dateTimeStr)?.time ?: 0L
        } catch (e: Exception) {
            Logger.wsmE("解析日期时间失败: $dateTimeStr", e)
            0L
        }
    }

    /**
     * 获取任务统计信息
     */
    fun getTaskStats(): String {
        return "任务总数: ${taskMap.size}, 结果总数: ${taskResultMap.size}"
    }

    /**
     * 获取规则统计信息
     */
    fun getRuleStats(): String {
        return "规则总数: ${ruleMap.size}, 结果总数: ${ruleResultMap.size}"
    }

    // ==================== 持久化存储方法 ====================

    /**
     * 从存储加载任务列表
     */
    private fun loadTasksFromStorage() {
        try {
            val taskListStr = sharedPreferences?.getString(SP_KEY_TASK_LIST, "") ?: ""
            if (taskListStr.isNotEmpty()) {
                val taskList = JSONArray(taskListStr)
                for (i in 0 until taskList.length()) {
                    val task = taskList.getJSONObject(i)
                    val taskId = task.optString("taskId")
                    if (taskId.isNotEmpty()) {
                        taskMap[taskId] = task
                    }
                }
                Logger.task("从存储加载任务: ${taskMap.size} 个")
            }
        } catch (e: Exception) {
            Logger.taskE("从存储加载任务失败", e)
        }
    }

    /**
     * 从存储加载规则列表
     */
    private fun loadRulesFromStorage() {
        try {
            val ruleListStr = sharedPreferences?.getString(SP_KEY_RULE_LIST, "") ?: ""
            if (ruleListStr.isNotEmpty()) {
                val ruleList = JSONArray(ruleListStr)
                for (i in 0 until ruleList.length()) {
                    val rule = ruleList.getJSONObject(i)
                    val ruleId = rule.optString("ruleId")
                    if (ruleId.isNotEmpty()) {
                        ruleMap[ruleId] = rule
                    }
                }
                Logger.wsm("从存储加载规则: ${ruleMap.size} 个")
            }
        } catch (e: Exception) {
            Logger.wsmE("从存储加载规则失败", e)
        }
    }

    /**
     * 获取本地任务列表
     */
    fun getLocalTaskList(): JSONArray {
        return try {
            val taskListStr = sharedPreferences?.getString(SP_KEY_TASK_LIST, "") ?: ""
            if (taskListStr.isNotEmpty()) {
                JSONArray(taskListStr)
            } else {
                JSONArray()
            }
        } catch (e: Exception) {
            Logger.wsmE("获取本地任务列表失败", e)
            JSONArray()
        }
    }

    /**
     * 保存任务列表到存储
     */
    fun saveTaskListToStorage(taskList: JSONArray) {
        try {
            sharedPreferences?.edit()?.putString(SP_KEY_TASK_LIST, taskList.toString())?.apply()
            Logger.task("保存任务列表到存储: ${taskList.length()} 个")
        } catch (e: Exception) {
            Logger.taskE("保存任务列表到存储失败", e)
        }
    }

    /**
     * 批量上传任务结果
     */
    fun uploadWSTaskResult() {
        try {
            val resultListStr = sharedPreferences?.getString(SP_KEY_TASK_RESULT_LIST, "") ?: ""
            if (resultListStr.isNotEmpty()) {
                val resultList = JSONArray(resultListStr)
                for (i in 0 until resultList.length()) {
                    val result = resultList.getJSONObject(i)
                    // 实际发送消息
                    WsConnectionManager.sendMessage(result.toString())
                    Logger.wsm("重发任务结果: ${result.optJSONObject("data")?.optString("taskId")}")
                }
            }
        } catch (e: Exception) {
            Logger.wsmE("批量上传任务结果失败", e)
        }
    }

    /**
     * 批量上传规则结果
     */
    fun uploadRuleResult() {
        try {
            val resultListStr = sharedPreferences?.getString(SP_KEY_RULE_RESULT_LIST, "") ?: ""
            if (resultListStr.isNotEmpty()) {
                val resultList = JSONArray(resultListStr)
                for (i in 0 until resultList.length()) {
                    val result = resultList.getJSONObject(i)
                    // 实际发送消息
                    WsConnectionManager.sendMessage(result.toString())
                    Logger.wsm("重发规则结果: ${result.optJSONObject("data")?.optString("ruleId")}")
                }
            }
        } catch (e: Exception) {
            Logger.wsmE("批量上传规则结果失败", e)
        }
    }

    /**
     * 清空任务结果缓存
     */
    fun clearWSTaskResultCache() {
        sharedPreferences?.edit()?.remove(SP_KEY_TASK_RESULT_LIST)?.apply()
        taskResultMap.clear()
        Logger.wsm("清空任务结果缓存")
    }

    /**
     * 根据原始请求ID删除任务结果
     * 当服务器确认收到任务结果后，删除本地缓存避免重复发送
     */
    fun removeWSTaskResultJsonObjByOrgReqId(orgReqId: String) {
        try {
            val resultListStr = sharedPreferences?.getString(SP_KEY_TASK_RESULT_LIST, "") ?: ""
            if (resultListStr.isEmpty()) return

            Logger.wsm("removeWSTaskResultJsonObjByOrgReqId: $orgReqId")
            val resultList = JSONArray(resultListStr)
            val newResultList = JSONArray()

            // 清理空消息和匹配的结果
            for (i in 0 until resultList.length()) {
                val result = resultList.getJSONObject(i)

                // 跳过空消息或没有request_id的消息
                if (result.length() == 0 || !result.has("request_id")) {
                    Logger.wsm("删除空消息: $i")
                    continue
                }

                // 跳过匹配的orgReqId
                val reqId = result.optString("request_id")
                if (orgReqId == reqId) {
                    Logger.wsm("删除匹配的任务结果: $orgReqId")
                    continue
                }

                // 保留其他结果
                newResultList.put(result)
            }

            // 保存更新后的结果列表
            sharedPreferences?.edit()?.putString(SP_KEY_TASK_RESULT_LIST, newResultList.toString())?.apply()
            Logger.wsm("removeWSTaskResultJsonObjByOrgReqId 后队列剩余: ${newResultList.length()}")

        } catch (e: Exception) {
            Logger.wsmE("根据原始请求ID删除任务结果失败", e)
        }
    }

    /**
     * 根据原始请求ID删除规则结果
     * 当服务器确认收到规则结果后，删除本地缓存避免重复发送
     */
    fun removeRuleResultJsonObjByOrgReqId(orgReqId: String) {
        try {
            val resultListStr = sharedPreferences?.getString(SP_KEY_RULE_RESULT_LIST, "") ?: ""
            if (resultListStr.isEmpty()) return

            Logger.wsm("removeRuleResultJsonObjByOrgReqId: $orgReqId")
            val resultList = JSONArray(resultListStr)
            val newResultList = JSONArray()

            // 清理空消息和匹配的结果
            for (i in 0 until resultList.length()) {
                val result = resultList.getJSONObject(i)

                // 跳过空消息或没有request_id的消息
                if (result.length() == 0 || !result.has("request_id")) {
                    Logger.wsm("删除空消息: $i")
                    continue
                }

                // 跳过匹配的orgReqId
                val reqId = result.optString("request_id")
                if (orgReqId == reqId) {
                    Logger.wsm("删除匹配的规则结果: $orgReqId")
                    continue
                }

                // 保留其他结果
                newResultList.put(result)
            }

            // 保存更新后的结果列表
            sharedPreferences?.edit()?.putString(SP_KEY_RULE_RESULT_LIST, newResultList.toString())?.apply()
            Logger.wsm("removeRuleResultJsonObjByOrgReqId 后队列剩余: ${newResultList.length()}")

        } catch (e: Exception) {
            Logger.wsmE("根据原始请求ID删除规则结果失败", e)
        }
    }

    /**
     * 重置正在执行的任务状态为待执行
     * 借鉴wisemanager_service的状态重置逻辑
     */
    fun resetDoingWSTaskState() {
        try {
            val localTaskList = getLocalTaskList()
            var hasChanges = false

            Logger.wsm("开始重置任务状态，当前任务个数: ${localTaskList.length()}")

            for (i in 0 until localTaskList.length()) {
                val task = localTaskList.getJSONObject(i)
                val taskId = task.optString("taskId")
                val taskResult = task.optString("taskResult")
                val taskType = task.optString("taskType")
                val packageName = task.optString("packName", "")

                Logger.wsm("检查任务: taskId=$taskId, taskResult=$taskResult, taskType=$taskType")

                // 跳过已完成的任务（成功或失败）
                if (isTaskCompleted(task)) {
                    Logger.wsm("跳过已完成任务: $taskId -> $taskResult")
                    continue
                }

                // 特殊处理：自身更新任务在INSTALL_ING状态时不重置
                if (taskType == "01" &&
                    packageName == context?.packageName &&
                    taskResult == TaskStateConstants.INSTALL_ING) {
                    Logger.wsm("跳过自身更新任务: $taskId (INSTALL_ING)")
                    continue
                }

                // 特殊处理：OS更新任务在UPDATE_ING状态时不重置
                if (taskType == "OSUPDATE" && taskResult == TaskStateConstants.UPDATE_ING) {
                    Logger.wsm("跳过OS更新任务: $taskId (UPDATE_ING)")
                    continue
                }

                // 重置执行中的任务为待执行状态
                if (isExecutingState(taskResult)) {
                    task.put("taskResult", TASK_STATE_TODO)
                    task.put("lastUpdateTime", System.currentTimeMillis()) // 重置时间戳
                    hasChanges = true
                    Logger.wsm("重置任务状态: $taskId -> TODO (原状态: $taskResult)")
                }
            }

            if (hasChanges) {
                saveTaskListToStorage(localTaskList)
                Logger.wsm("任务状态重置完成")
            } else {
                Logger.wsm("没有需要重置的任务")
            }

            Logger.success("任务状态恢复完成")

        } catch (e: Exception) {
            Logger.wsmE("重置任务状态失败", e)
        }
    }

    /**
     * 判断任务是否已完成（成功或失败）
     * 借鉴wisemanager_service的isWSTaskSuccessed和isWSTaskFailed逻辑
     */
    private fun isTaskCompleted(task: JSONObject): Boolean {
        return try {
            val taskResult = task.optString("taskResult", "")
            TaskStateConstants.isCompletedState(taskResult)
        } catch (e: Exception) {
            false
        }
    }

    /**
     * 判断任务是否成功
     * 借鉴wisemanager_service的isWSTaskSuccessed逻辑
     */
    private fun isTaskSuccessed(task: JSONObject): Boolean {
        return try {
            val taskResult = task.optString("taskResult", "")
            TaskStateConstants.isSuccessState(taskResult)
        } catch (e: Exception) {
            false
        }
    }

    /**
     * 判断任务是否失败
     * 借鉴wisemanager_service的isWSTaskFailed逻辑
     */
    private fun isTaskFailed(task: JSONObject): Boolean {
        return try {
            val taskResult = task.optString("taskResult", "")
            TaskStateConstants.isFailureState(taskResult)
        } catch (e: Exception) {
            false
        }
    }

    /**
     * 判断任务是否处于执行中状态
     * 借鉴wisemanager_service的状态判断逻辑
     */
    private fun isExecutingState(taskResult: String): Boolean {
        return TaskStateConstants.isInProgressState(taskResult)
    }

    /**
     * 检查服务更新结果
     * 在WebSocket首次连接成功时调用
     */
    fun checkServiceUpdateResult() {
        try {
            val context = this.context ?: return
            Logger.wsm("检查服务更新结果")

            val localTaskListStr = sharedPreferences?.getString(SP_KEY_TASK_LIST, "") ?: ""
            if (localTaskListStr.isEmpty()) {
                Logger.taskW("任务列表为空，跳过服务更新结果检查")
                return
            }

            val localTaskList = JSONArray(localTaskListStr)
            Logger.taskI("本地任务列表长度: ${localTaskList.length()}")
            Logger.task("本地任务列表内容: $localTaskListStr")

            // 获取当前应用版本信息
            val packageManager = context.packageManager
            val packageInfo = packageManager.getPackageInfo(context.packageName, 0)
            val currentVersionCode = packageInfo.versionCode.toString()
            val currentVersionName = packageInfo.versionName

            Logger.taskI("当前应用版本: versionCode=$currentVersionCode, versionName=$currentVersionName")
            Logger.taskI("任务列表数量: ${localTaskList.length()}")

            for (i in 0 until localTaskList.length()) {
                val task = localTaskList.getJSONObject(i)
                val taskType = task.optString("taskType")
                val taskVersionCode = task.optString("versionCode")
                val taskVersionName = task.optString("versionName")
                val taskResult = task.optString("taskResult")
                val taskId = task.optString("taskId")

                // 统一处理包名字段（支持pkgName和packName两种格式）
                val packageName = task.optString("pkgName").takeIf { it.isNotEmpty() }
                    ?: task.optString("packName", "")

                Logger.task("检查任务[$i]: taskId=$taskId, taskType=$taskType, pkgName=$packageName, versionCode=$taskVersionCode, versionName=$taskVersionName, taskResult=$taskResult")

                // 检查是否是自身服务的更新任务
                val actualPackageName = packageName

                if ("01" == taskType &&
                    context.packageName == actualPackageName &&
                    currentVersionCode == taskVersionCode &&
                    currentVersionName == taskVersionName) {

                    Logger.taskI("找到自身服务更新任务: $taskId")

                    // 检查任务时间和状态
                    val beginDateStr = task.optString("beginDate")
                    val endDateStr = task.optString("endDate")
                    val nowTime = System.currentTimeMillis()

                    try {
                        val beginTime = parseDateTime(beginDateStr)

                        Logger.wsm("时间检查: nowTime=$nowTime, beginTime=$beginTime, taskResult=$taskResult")

                        // 如果任务已开始，检查是否需要上报安装成功
                        // 条件：1. 时间已到 2. 任务状态为B02(INSTALL_ING)或为空(可能是刚安装完成)
                        if (beginTime < nowTime && ("B02" == taskResult || taskResult.isEmpty())) {
                            Logger.wsm("服务更新已完成，上报安装成功")

                            // 更新任务状态并上报
                            updateWSTaskState(taskId, "B03") // B03 = INSTALL_SUCCESS
                            WsMessageSender.uploadTaskResult(taskId, "B03", null)

                            Logger.success("服务更新结果上报完成: $taskId -> B03")
                        } else {
                            Logger.wsm("任务状态不匹配: taskResult=$taskResult, beginTime=$beginTime, nowTime=$nowTime")
                            Logger.wsm("条件检查: beginTime < nowTime = ${beginTime < nowTime}, taskResult isEmpty = ${taskResult.isEmpty()}")
                        }

                    } catch (e: Exception) {
                        Logger.wsmE("解析任务时间失败", e)
                    }

                    break // 找到自身任务后退出循环
                }
            }

        } catch (e: PackageManager.NameNotFoundException) {
            Logger.wsmE("获取包信息失败", e)
        } catch (e: Exception) {
            Logger.wsmE("检查服务更新结果失败", e)
        }
    }

    /**
     * 启动任务状态超时检测 - 简化版本
     */
    private fun startTaskTimeoutCheck() {
        Timer().scheduleAtFixedRate(object : TimerTask() {
            override fun run() {
                try {
                    checkTaskTimeout()
                } catch (e: Exception) {
                    Logger.wsmE("任务超时检测异常", e)
                }
            }
        }, 300000, 600000) // 5分钟后开始，每10分钟检查一次（降低频率）
    }

    /**
     * 检查任务超时 - 简化版本
     */
    private fun checkTaskTimeout() {
        try {
            val currentTime = System.currentTimeMillis()
            val timeoutThreshold = 20 * 60 * 1000L // 20分钟超时（保守）
            val localTaskList = getLocalTaskList()

            for (i in 0 until localTaskList.length()) {
                val task = localTaskList.getJSONObject(i)
                val taskId = task.optString("taskId")
                val taskResult = task.optString("taskResult", "")
                val lastUpdateTime = task.optLong("lastUpdateTime", 0L)

                // 检测执行中状态的超时
                if (TaskStateConstants.isInProgressState(taskResult) &&
                    lastUpdateTime > 0 &&
                    (currentTime - lastUpdateTime) > timeoutThreshold) {

                    Logger.wsmW("检测到任务超时: $taskId, 状态: $taskResult")

                    // 根据当前状态确定失败状态
                    val failedState = when (taskResult) {
                        TaskStateConstants.DOWNLOAD_ING -> TaskStateConstants.DOWNLOAD_FAILED
                        TaskStateConstants.INSTALL_ING -> TaskStateConstants.INSTALL_FAILED
                        TaskStateConstants.UPDATE_ING -> TaskStateConstants.UPDATE_FAILED
                        TaskStateConstants.UNINSTALL_ING -> TaskStateConstants.UNINSTALL_FAILED
                        else -> TaskStateConstants.FAILED
                    }

                    // 标记为失败
                    task.put("taskResult", failedState)
                    task.put("lastUpdateTime", currentTime)

                    // 保存到本地
                    saveTaskListToStorage(localTaskList)

                    // 上报失败状态（会自动使用缓存机制）
                    WsMessageSender.uploadTaskResult(
                        taskId = taskId,
                        taskResult = failedState,
                        appId = task.optString("appId", ""),
                        errorMsg = "任务超时"
                    )
                }
            }

        } catch (e: Exception) {
            Logger.wsmE("检查任务超时异常", e)
        }
    }

    /**
     * 判断是否为终态状态（任务已结束，应该从本地删除）
     * 包括：A04(除OS和SP外)\B03\B04\B05\C03\C04\D02\D03\D04\E01\E02\E04\E05\F01\R03\R04
     * 特殊处理：A04状态的OS更新任务和SP下载任务不是终态（需要一直重试直到成功/失败）
     */
    private fun isTerminalState(taskResult: String, task: JSONObject? = null): Boolean {
        return when (taskResult) {
            // 下载失败：除OS更新和SP下载外都是终态
            TaskStateConstants.DOWNLOAD_FAILED -> {
                if (task != null) {
                    val taskType = task.optString("taskType")
                    // OS更新和SP下载任务的A04永远不是终态，可以无限重试直到终端置为终态
                    taskType != "04" && taskType != "03"
                } else {
                    // 没有任务信息时，默认A04是终态
                    true
                }
            }
            // 安装相关终态
            TaskStateConstants.INSTALL_SUCCESS,     // B03
            TaskStateConstants.INSTALL_FAILED,      // B04
            TaskStateConstants.INSTALL_OVERRIDE,    // B05
            // 更新相关终态
            TaskStateConstants.UPDATE_SUCCESS,      // C03
            TaskStateConstants.UPDATE_FAILED,       // C04
            // 卸载相关终态
            TaskStateConstants.UNINSTALL_SUCCESS,   // D02
            TaskStateConstants.UNINSTALL_FAILED,    // D03
            TaskStateConstants.UNINSTALL_EXPIRE,    // D04
            // 通用错误终态
            TaskStateConstants.TASK_CANCEL,         // E01
            TaskStateConstants.RELY_FAILED,         // E02
            TaskStateConstants.SERVER_FAILED,       // E04
            TaskStateConstants.FAILED,              // E05
            // 成功终态
            TaskStateConstants.SUCCESSED,           // F01
            // 规则相关终态
            TaskStateConstants.RULEBASED_SUCCESS,   // R03
            TaskStateConstants.RULEBASED_EXPIRED    // R04
            -> true

            else -> false
        }
    }

    /**
     * 清理终态任务（已完成的任务不需要保留在本地）
     */
    fun cleanupTerminalTasks() {
        try {
            val localTaskList = getLocalTaskList()
            val newTaskList = JSONArray()
            var removedCount = 0

            Logger.wsm("开始清理终态任务，当前任务数量: ${localTaskList.length()}")

            for (i in 0 until localTaskList.length()) {
                val task = localTaskList.getJSONObject(i)
                val taskId = task.optString("taskId")
                val taskResult = task.optString("taskResult")

                val isTerminal = isTerminalState(taskResult, task)

                Logger.wsm("🔍 检查任务: taskId=$taskId, taskResult=$taskResult, isTerminal=$isTerminal")

                if (isTerminal) {
                    // 终态任务，从本地删除
                    Logger.wsm("🗑️ 删除终态任务: taskId=$taskId, taskResult=$taskResult")

                    // 从内存缓存中移除
                    taskMap.remove(taskId)
                    taskResultMap.remove(taskId)

                    removedCount++
                } else {
                    // 非终态任务，保留
                    Logger.wsm("✅ 保留非终态任务: taskId=$taskId, taskResult=$taskResult")
                    newTaskList.put(task)
                }
            }

            if (removedCount > 0) {
                // 保存清理后的任务列表
                saveTaskListToStorage(newTaskList)
                Logger.wsm("✅ 终态任务清理完成，删除 $removedCount 个任务，剩余 ${newTaskList.length()} 个任务")

                // 验证保存是否成功
                val verifyTaskList = getLocalTaskList()
                Logger.wsm("验证清理结果: 存储中剩余任务数量 ${verifyTaskList.length()}")
            } else {
                Logger.wsm("没有需要清理的终态任务")
            }

        } catch (e: Exception) {
            Logger.wsmE("❌ 清理终态任务失败", e)
        }
    }


}
