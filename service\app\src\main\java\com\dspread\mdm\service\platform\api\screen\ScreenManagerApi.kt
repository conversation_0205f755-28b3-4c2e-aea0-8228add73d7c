package com.dspread.mdm.service.platform.api.screen

import android.content.Context
import android.os.PowerManager
import android.provider.Settings
import com.dspread.mdm.service.utils.log.Logger
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

/**
 * 屏幕管理工具类
 * 用于Remote View功能时管理屏幕状态
 *
 * 功能：
 * 1. 点亮屏幕
 * 2. 设置屏幕亮度
 * 3. 管理屏幕超时设置
 * 4. 恢复原始屏幕设置
 *
 * 注意：不管理WakeLock，因为服务本身已经在后台运行
 */
object ScreenManagerApi {

    private const val TAG = "ScreenManagerApi"

    // 保存原始设置，用于恢复
    private var originalScreenTimeout: Int = -1
    private var originalBrightness: Int = -1
    private var originalBrightnessMode: Int = -1

    /**
     * 启动Remote View时的屏幕设置
     * 点亮屏幕并优化显示设置
     */
    suspend fun enableRemoteViewScreenMode(context: Context): Result<Unit> {
        return withContext(Dispatchers.Main) {
            try {
                // 保存当前设置
                saveCurrentSettings(context)

                // 设置屏幕永不超时
                setScreenTimeout(context, Int.MAX_VALUE)

                // 设置屏幕亮度为最大
                setScreenBrightness(context, 255)

                // 强制唤醒屏幕
                forceWakeUpScreen(context)
                Result.success(Unit)

            } catch (e: Exception) {
                Logger.comE("$TAG 启动Remote View屏幕模式失败", e)
                Result.failure(e)
            }
        }
    }

    /**
     * 停止Remote View时恢复屏幕设置
     */
    suspend fun disableRemoteViewScreenMode(context: Context): Result<Unit> {
        return withContext(Dispatchers.Main) {
            try {
                // 恢复原始设置
                restoreOriginalSettings(context)
                Result.success(Unit)

            } catch (e: Exception) {
                Logger.comE("$TAG 停止Remote View屏幕模式失败", e)
                Result.failure(e)
            }
        }
    }

    /**
     * 保存当前屏幕设置
     */
    private fun saveCurrentSettings(context: Context) {
        try {
            val contentResolver = context.contentResolver

            // 保存屏幕超时设置
            originalScreenTimeout = Settings.System.getInt(
                contentResolver,
                Settings.System.SCREEN_OFF_TIMEOUT,
                30000 // 默认30秒
            )

            // 保存亮度设置
            originalBrightness = Settings.System.getInt(
                contentResolver,
                Settings.System.SCREEN_BRIGHTNESS,
                128 // 默认中等亮度
            )

            // 保存亮度模式
            originalBrightnessMode = Settings.System.getInt(
                contentResolver,
                Settings.System.SCREEN_BRIGHTNESS_MODE,
                Settings.System.SCREEN_BRIGHTNESS_MODE_AUTOMATIC
            )

        } catch (e: Exception) {
            Logger.comE("$TAG 保存原始设置失败", e)
        }
    }

    /**
     * 设置屏幕超时时间
     */
    private fun setScreenTimeout(context: Context, timeoutMs: Int) {
        try {
            Settings.System.putInt(
                context.contentResolver,
                Settings.System.SCREEN_OFF_TIMEOUT,
                timeoutMs
            )
        } catch (e: Exception) {
            Logger.comE("$TAG 设置屏幕超时失败", e)
        }
    }

    /**
     * 设置屏幕亮度
     */
    private fun setScreenBrightness(context: Context, brightness: Int) {
        try {
            val contentResolver = context.contentResolver

            // 设置为手动调节模式
            Settings.System.putInt(
                contentResolver,
                Settings.System.SCREEN_BRIGHTNESS_MODE,
                Settings.System.SCREEN_BRIGHTNESS_MODE_MANUAL
            )

            // 设置亮度值
            Settings.System.putInt(
                contentResolver,
                Settings.System.SCREEN_BRIGHTNESS,
                brightness
            )

            // 通知系统设置变更
            val uri = Settings.System.getUriFor(Settings.System.SCREEN_BRIGHTNESS)
            contentResolver.notifyChange(uri, null)

        } catch (e: Exception) {
            Logger.comE("$TAG 设置屏幕亮度失败", e)
        }
    }

    /**
     * 强制唤醒屏幕（多种方式确保成功）
     */
    private fun forceWakeUpScreen(context: Context) {
        val powerManager = context.getSystemService(Context.POWER_SERVICE) as PowerManager

        if (powerManager.isInteractive) {
            return
        }

        // 方法1：使用能够唤醒屏幕的 WakeLock
        var success = wakeUpWithWakeLock(context, powerManager)

        // 方法2：如果方法1失败，尝试其他方式
        if (!success) {
            success = wakeUpWithAlternativeMethod(context)
        }
    }

    /**
     * 方法1：使用 WakeLock 唤醒屏幕
     */
    private fun wakeUpWithWakeLock(context: Context, powerManager: PowerManager): Boolean {
        return try {
            // 使用能够唤醒屏幕的 WakeLock 类型
            @Suppress("DEPRECATION")
            val wakeUpLock = powerManager.newWakeLock(
                PowerManager.SCREEN_BRIGHT_WAKE_LOCK or PowerManager.ACQUIRE_CAUSES_WAKEUP,
                "SmartMDM:WakeUpScreen"
            )
            wakeUpLock.acquire(5000) // 5秒后自动释放
            true

        } catch (e: Exception) {
            Logger.comE("$TAG WakeLock唤醒失败", e)
            false
        }
    }

    /**
     * 方法2：备用唤醒方法
     */
    private fun wakeUpWithAlternativeMethod(context: Context): Boolean {
        return try {
            // 尝试发送按键事件唤醒屏幕
            val powerManager = context.getSystemService(Context.POWER_SERVICE) as PowerManager

            // 创建一个临时的 PARTIAL_WAKE_LOCK 来确保代码执行
            val tempWakeLock = powerManager.newWakeLock(
                PowerManager.PARTIAL_WAKE_LOCK,
                "SmartMDM:TempWakeUp"
            )
            tempWakeLock.acquire(3000)
            true

        } catch (e: Exception) {
            Logger.comE("$TAG 备用唤醒方法失败", e)
            false
        }
    }

    /**
     * 恢复原始设置
     */
    private fun restoreOriginalSettings(context: Context) {
        try {
            val contentResolver = context.contentResolver

            // 恢复屏幕超时
            if (originalScreenTimeout != -1) {
                Settings.System.putInt(
                    contentResolver,
                    Settings.System.SCREEN_OFF_TIMEOUT,
                    originalScreenTimeout
                )
            }

            // 恢复亮度模式
            if (originalBrightnessMode != -1) {
                Settings.System.putInt(
                    contentResolver,
                    Settings.System.SCREEN_BRIGHTNESS_MODE,
                    originalBrightnessMode
                )
            }

            // 恢复亮度值
            if (originalBrightness != -1) {
                Settings.System.putInt(
                    contentResolver,
                    Settings.System.SCREEN_BRIGHTNESS,
                    originalBrightness
                )

                val uri = Settings.System.getUriFor(Settings.System.SCREEN_BRIGHTNESS)
                contentResolver.notifyChange(uri, null)
            }

            // 重置保存的值
            originalScreenTimeout = -1
            originalBrightness = -1
            originalBrightnessMode = -1

        } catch (e: Exception) {
            Logger.comE("$TAG 恢复原始设置失败", e)
        }
    }
}
