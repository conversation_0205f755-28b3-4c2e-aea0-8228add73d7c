package com.dspread.mdm.service.modules.logstream

import android.content.Context
import com.dspread.mdm.service.config.LogStreamConfig
import com.dspread.mdm.service.platform.api.device.DeviceInfoApi
import com.dspread.mdm.service.utils.log.Logger
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.*
import java.net.HttpURLConnection
import java.net.URL
import java.net.URLEncoder
import java.nio.charset.StandardCharsets
import java.security.MessageDigest
import java.text.SimpleDateFormat
import java.util.*
import javax.crypto.Mac
import javax.crypto.spec.SecretKeySpec
import javax.net.ssl.HttpsURLConnection

/**
 * S3日志上传器
 */
class S3LogUploader(private val context: Context) {
    
    companion object {
        private const val TAG = "[S3LogUploader]"

        // S3上传配置
        private const val S3_LOG_STREAMING_CONTENT_TYPE = "application/octet-stream"
        private const val S3_LOG_STREAMING_BUFFER_SIZE = 8192

        // AWS V4签名配置
        private const val AWS_ALGORITHM = "AWS4-HMAC-SHA256"
        private const val AWS_REQUEST = "aws4_request"
        private const val AWS_SERVICE = "s3"
        private const val ISO8601_DATE_FORMAT = "yyyyMMdd'T'HHmmss'Z'"
        private const val DATE_FORMAT = "yyyyMMdd"

        // 重试配置
        private const val MAX_RETRY_ATTEMPTS = 3
        private const val RETRY_DELAY_MS = 5000L

        // 上传路径配置
        private const val MODE_DEVELOPMENT = "dev"
        private const val MODE_PRODUCTION = "prod"
    }

    // 动态S3配置
    private val s3Config by lazy {
        S3Config(
            url = LogStreamConfig.getUploadUrl(),
            bucketName = extractBucketName(LogStreamConfig.getUploadUrl()),
            region = extractRegion(LogStreamConfig.getUploadUrl()),
            accessKey = LogStreamConfig.getAccessKey(),
            secretKey = LogStreamConfig.getSecretKey()
        )
    }

    /**
     * S3配置数据类
     */
    private data class S3Config(
        val url: String,
        val bucketName: String,
        val region: String,
        val accessKey: String,
        val secretKey: String
    )
    
    /**
     * 从URL提取bucket名称
     */
    private fun extractBucketName(url: String): String {
        return try {
            // 从URL中提取bucket名称
            // 例如：https://smartms-applog.s3.sa-east-1.amazonaws.com/log/ -> smartms-applog
            val urlObj = URL(url)
            val host = urlObj.host
            host.split(".").firstOrNull() ?: "unknown-bucket"
        } catch (e: Exception) {
            Logger.logStreamE("$TAG 提取bucket名称失败: $url", e)
            "unknown-bucket"
        }
    }

    /**
     * 从URL提取AWS region
     */
    private fun extractRegion(url: String): String {
        return try {
            // 从URL中提取region
            // 例如：https://smartms-applog.s3.sa-east-1.amazonaws.com/log/ -> sa-east-1
            val urlObj = URL(url)
            val host = urlObj.host
            val parts = host.split(".")
            if (parts.size >= 3 && parts[1] == "s3") {
                parts[2] // region部分
            } else {
                "us-east-1" // 默认region
            }
        } catch (e: Exception) {
            Logger.logStreamE("$TAG 提取region失败: $url", e)
            "us-east-1"
        }
    }

    /**
     * 上传文件到S3（带重试机制）
     */
    suspend fun uploadFile(file: File): Boolean = withContext(Dispatchers.IO) {
        return@withContext try {
            Logger.logStream("$TAG 开始上传文件: ${file.name}, 大小: ${file.length()}")

            // 验证S3配置
            if (s3Config.url.isEmpty() || s3Config.accessKey.isEmpty() || s3Config.secretKey.isEmpty()) {
                Logger.logStreamE("$TAG S3配置不完整: url=${s3Config.url}, accessKey=${s3Config.accessKey.take(8)}***, bucket=${s3Config.bucketName}")
                return@withContext false
            }

            Logger.logStream("$TAG 使用S3配置: url=${s3Config.url}, bucket=${s3Config.bucketName}, accessKey=${s3Config.accessKey.take(8)}***")

            if (!file.exists() || file.length() == 0L) {
                Logger.logStreamW("$TAG 文件不存在或为空: ${file.absolutePath}")
                return@withContext false
            }

            // 构建S3文件路径
            val deviceSN = DeviceInfoApi(context).getSerialNumber()
            val filePath = "$MODE_DEVELOPMENT/$deviceSN"
            val fileName = "$filePath/${file.name}"
            
            Logger.logStream("$TAG S3文件路径: $fileName")
            
            // 带重试的上传
            var lastException: Exception? = null
            for (attempt in 1..MAX_RETRY_ATTEMPTS) {
                try {
                    Logger.logStream("$TAG 上传尝试 $attempt/$MAX_RETRY_ATTEMPTS")
                    
                    // 生成V4签名所需的信息
                    val timestamp = generateTimestamp()
                    val dateStamp = generateDateStamp(timestamp)
                    val sha256Hash = calculateSHA256Hex(file)
                    val authHeader = generateV4Authorization(fileName, timestamp, dateStamp, sha256Hash)

                    Logger.logStream("$TAG V4签名生成完成")

                    // 执行上传
                    val success = performV4Upload(file, fileName, timestamp, sha256Hash, authHeader)
                    
                    if (success) {
                        Logger.logStream("$TAG 文件上传成功: ${file.name} (尝试 $attempt)")
                        return@withContext true
                    } else {
                        Logger.logStreamW("$TAG 上传失败，尝试 $attempt/$MAX_RETRY_ATTEMPTS")
                    }
                    
                } catch (e: Exception) {
                    lastException = e
                    Logger.logStreamE("$TAG 上传异常，尝试 $attempt/$MAX_RETRY_ATTEMPTS: ${file.name}", e)
                    
                    if (attempt < MAX_RETRY_ATTEMPTS) {
                        Logger.logStream("$TAG 等待 ${RETRY_DELAY_MS}ms 后重试...")
                        kotlinx.coroutines.delay(RETRY_DELAY_MS)
                    }
                }
            }
            
            Logger.logStreamE("$TAG 文件上传失败，已重试 $MAX_RETRY_ATTEMPTS 次: ${file.name}")
            lastException?.let { Logger.logStreamE("$TAG 最后一次异常", it) }
            false
            
        } catch (e: Exception) {
            Logger.logStreamE("$TAG 上传文件异常: ${file.name}", e)
            false
        }
    }
    
    /**
     * 执行V4签名的HTTP上传
     */
    private fun performV4Upload(file: File, fileName: String, timestamp: String, payloadHash: String, authHeader: String): Boolean {
        return try {
            // 构建完整的S3 URL
            val baseUrl = s3Config.url.removeSuffix("/")
            val fullUrl = if (baseUrl.endsWith("/log")) {
                "$baseUrl/$fileName"
            } else {
                "$baseUrl/$fileName"
            }
            val url = URL(fullUrl)
            val connection = url.openConnection() as HttpsURLConnection

            Logger.logStream("$TAG V4上传到URL: $fullUrl")

            // 设置请求方法和属性
            connection.requestMethod = "PUT"
            connection.doOutput = true
            connection.useCaches = false

            // 设置V4签名请求头
            connection.setRequestProperty("Content-Type", S3_LOG_STREAMING_CONTENT_TYPE)
            connection.setRequestProperty("Authorization", authHeader)
            connection.setRequestProperty("x-amz-content-sha256", payloadHash)
            connection.setRequestProperty("x-amz-date", timestamp)
            connection.setRequestProperty("Content-Length", file.length().toString())

            // 设置超时
            connection.readTimeout = 60000 // 60秒
            connection.connectTimeout = 30000 // 30秒

            // 连接并上传文件
            connection.connect()

            file.inputStream().use { input ->
                connection.outputStream.use { output ->
                    val buffer = ByteArray(S3_LOG_STREAMING_BUFFER_SIZE)
                    var bytesRead: Int
                    while (input.read(buffer).also { bytesRead = it } != -1) {
                        output.write(buffer, 0, bytesRead)
                    }
                    output.flush()
                }
            }

            val responseCode = connection.responseCode
            Logger.logStream("$TAG V4上传响应码: $responseCode")

            if (responseCode == HttpURLConnection.HTTP_OK) {
                Logger.logStream("$TAG V4上传成功: ${file.name}")
                true
            } else {
                val errorStream = connection.errorStream
                val errorMessage = errorStream?.bufferedReader()?.readText() ?: "未知错误"
                Logger.logStreamE("$TAG V4上传失败: $responseCode - $errorMessage")
                false
            }

        } catch (e: Exception) {
            Logger.logStreamE("$TAG V4上传异常", e)
            false
        }
    }

    /**
     * 生成ISO8601时间戳
     */
    private fun generateTimestamp(): String {
        val dateFormat = SimpleDateFormat(ISO8601_DATE_FORMAT, Locale.US)
        dateFormat.timeZone = TimeZone.getTimeZone("UTC")
        return dateFormat.format(Date())
    }

    /**
     * 生成日期戳（YYYYMMDD格式）
     */
    private fun generateDateStamp(timestamp: String): String {
        return timestamp.substring(0, 8)
    }
    
    /**
     * 计算文件的MD5并转换为Base64
     */
    private fun calculateMD5Base64(file: File): String {
        return try {
            val md5Digest = MessageDigest.getInstance("MD5")
            val inputStream = FileInputStream(file)
            val buffer = ByteArray(S3_LOG_STREAMING_BUFFER_SIZE)
            var bytesRead: Int
            
            while (inputStream.read(buffer).also { bytesRead = it } != -1) {
                md5Digest.update(buffer, 0, bytesRead)
            }
            
            inputStream.close()
            
            val md5Bytes = md5Digest.digest()
            Base64.getEncoder().encodeToString(md5Bytes)
            
        } catch (e: Exception) {
            Logger.logStreamE("$TAG 计算MD5失败", e)
            ""
        }
    }

    /**
     * 计算文件的SHA256值（十六进制编码）
     */
    private fun calculateSHA256Hex(file: File): String {
        return try {
            val sha256 = MessageDigest.getInstance("SHA-256")
            file.inputStream().use { input ->
                val buffer = ByteArray(S3_LOG_STREAMING_BUFFER_SIZE)
                var bytesRead: Int
                while (input.read(buffer).also { bytesRead = it } != -1) {
                    sha256.update(buffer, 0, bytesRead)
                }
            }
            sha256.digest().joinToString("") { "%02x".format(it) }
        } catch (e: Exception) {
            Logger.logStreamE("$TAG 计算SHA256失败", e)
            ""
        }
    }

    /**
     * 计算字符串的SHA256值（十六进制编码）
     */
    private fun calculateSHA256Hex(text: String): String {
        return try {
            val sha256 = MessageDigest.getInstance("SHA-256")
            val bytes = text.toByteArray(StandardCharsets.UTF_8)
            sha256.digest(bytes).joinToString("") { "%02x".format(it) }
        } catch (e: Exception) {
            Logger.logStreamE("$TAG 计算字符串SHA256失败", e)
            ""
        }
    }

    /**
     * 生成AWS S3 V4签名授权头
     */
    private fun generateV4Authorization(fileName: String, timestamp: String, dateStamp: String, payloadHash: String): String {
        return try {
            // 1. 构建规范请求
            val canonicalUri = if (s3Config.url.contains("/log/")) {
                "/log/$fileName"
            } else {
                "/$fileName"
            }

            val canonicalQueryString = ""
            val canonicalHeaders = "host:${s3Config.bucketName}.s3.${s3Config.region}.amazonaws.com\n" +
                    "x-amz-content-sha256:$payloadHash\n" +
                    "x-amz-date:$timestamp\n"
            val signedHeaders = "host;x-amz-content-sha256;x-amz-date"

            val canonicalRequest = "PUT\n$canonicalUri\n$canonicalQueryString\n$canonicalHeaders\n$signedHeaders\n$payloadHash"

            // 2. 构建待签名字符串
            val credentialScope = "$dateStamp/${s3Config.region}/$AWS_SERVICE/$AWS_REQUEST"
            val stringToSign = "$AWS_ALGORITHM\n$timestamp\n$credentialScope\n${calculateSHA256Hex(canonicalRequest)}"

            // 3. 计算签名
            val signature = calculateV4Signature(s3Config.secretKey, dateStamp, s3Config.region, stringToSign)

            // 4. 构建授权头
            val credential = "${s3Config.accessKey}/$credentialScope"
            val authorizationHeader = "$AWS_ALGORITHM Credential=$credential, SignedHeaders=$signedHeaders, Signature=$signature"

            Logger.logStream("$TAG V4签名生成完成")
            Logger.logStream("$TAG 规范请求: $canonicalRequest")
            Logger.logStream("$TAG 待签名字符串: $stringToSign")

            authorizationHeader

        } catch (e: Exception) {
            Logger.logStreamE("$TAG 生成V4签名失败", e)
            ""
        }
    }

    /**
     * 计算AWS V4签名
     */
    private fun calculateV4Signature(secretKey: String, dateStamp: String, region: String, stringToSign: String): String {
        return try {
            // AWS V4签名密钥派生过程
            val kDate = hmacSHA256("AWS4$secretKey".toByteArray(), dateStamp)
            val kRegion = hmacSHA256(kDate, region)
            val kService = hmacSHA256(kRegion, AWS_SERVICE)
            val kSigning = hmacSHA256(kService, AWS_REQUEST)

            // 计算最终签名
            val signature = hmacSHA256(kSigning, stringToSign)
            signature.joinToString("") { "%02x".format(it) }

        } catch (e: Exception) {
            Logger.logStreamE("$TAG 计算V4签名失败", e)
            ""
        }
    }

    /**
     * HMAC-SHA256计算
     */
    private fun hmacSHA256(key: ByteArray, data: String): ByteArray {
        val mac = Mac.getInstance("HmacSHA256")
        val secretKeySpec = SecretKeySpec(key, "HmacSHA256")
        mac.init(secretKeySpec)
        return mac.doFinal(data.toByteArray(StandardCharsets.UTF_8))
    }

    /**
     * 批量上传文件
     */
    suspend fun uploadFiles(files: List<File>): Map<File, Boolean> = withContext(Dispatchers.IO) {
        val results = mutableMapOf<File, Boolean>()
        
        Logger.logStream("$TAG 开始批量上传 ${files.size} 个文件")
        
        for (file in files) {
            val success = uploadFile(file)
            results[file] = success
            
            if (success) {
                Logger.logStream("$TAG 批量上传成功: ${file.name}")
            } else {
                Logger.logStreamE("$TAG 批量上传失败: ${file.name}")
            }
        }
        
        val successCount = results.values.count { it }
        Logger.logStream("$TAG 批量上传完成: 成功 $successCount/${files.size}")
        
        results
    }
    
    /**
     * 上传并删除文件
     */
    suspend fun uploadAndDeleteFile(file: File): Boolean {
        val success = uploadFile(file)
        if (success) {
            try {
                if (file.delete()) {
                    Logger.logStream("$TAG 上传后删除文件成功: ${file.name}")
                } else {
                    Logger.logStreamW("$TAG 上传后删除文件失败: ${file.name}")
                }
            } catch (e: Exception) {
                Logger.logStreamE("$TAG 删除文件异常: ${file.name}", e)
            }
        }
        return success
    }

    /**
     * 上传Recent文件到S3
     * 路径: dev/sn/recent.gz
     */
    suspend fun uploadRecentFile(file: File): Boolean = withContext(Dispatchers.IO) {
        return@withContext try {
            Logger.logStream("$TAG 开始上传Recent文件: ${file.name}")

            if (!file.exists() || file.length() == 0L) {
                Logger.logStreamW("$TAG Recent文件不存在或为空: ${file.absolutePath}")
                return@withContext false
            }

            // 构建Recent文件S3路径 (dev/sn/recent.gz)
            val deviceSN = DeviceInfoApi(context).getSerialNumber()
            val fileName = "$MODE_DEVELOPMENT/$deviceSN/recent.gz"

            Logger.logStream("$TAG Recent文件S3路径: $fileName")

            // 执行上传
            val success = performS3Upload(file, fileName)

            if (success) {
                Logger.logStream("$TAG Recent文件上传成功")
            } else {
                Logger.logStreamE("$TAG Recent文件上传失败")
            }

            success

        } catch (e: Exception) {
            Logger.logStreamE("$TAG 上传Recent文件异常", e)
            false
        }
    }

    /**
     * 上传压缩文件到S3
     * 路径: dev/sn/日期/文件名.gz
     */
    suspend fun uploadCompressedFile(file: File): Boolean = withContext(Dispatchers.IO) {
        return@withContext try {
            Logger.logStream("$TAG 开始上传压缩文件: ${file.name}")

            if (!file.exists() || file.length() == 0L) {
                Logger.logStreamW("$TAG 压缩文件不存在或为空: ${file.absolutePath}")
                return@withContext false
            }

            // 构建压缩文件S3路径 (dev/sn/20250717/文件名.gz)
            val deviceSN = DeviceInfoApi(context).getSerialNumber()
            val dateFormat = java.text.SimpleDateFormat("yyyyMMdd", java.util.Locale.getDefault())
            val currentDate = dateFormat.format(java.util.Date())
            val fileName = "$MODE_DEVELOPMENT/$deviceSN/$currentDate/${file.name}"

            Logger.logStream("$TAG 压缩文件S3路径: $fileName")

            // 执行上传
            val success = performS3Upload(file, fileName)

            if (success) {
                Logger.logStream("$TAG 压缩文件上传成功: ${file.name}")
            } else {
                Logger.logStreamE("$TAG 压缩文件上传失败: ${file.name}")
            }

            success

        } catch (e: Exception) {
            Logger.logStreamE("$TAG 上传压缩文件异常: ${file.name}", e)
            false
        }
    }

    /**
     * 执行S3上传的通用方法（使用V4签名）
     */
    private fun performS3Upload(file: File, fileName: String): Boolean {
        return try {
            // 生成V4签名所需的信息
            val timestamp = generateTimestamp()
            val dateStamp = generateDateStamp(timestamp)
            val sha256Hash = calculateSHA256Hex(file)
            val authHeader = generateV4Authorization(fileName, timestamp, dateStamp, sha256Hash)

            Logger.logStream("$TAG S3 V4签名生成完成")

            // 执行V4上传
            performV4Upload(file, fileName, timestamp, sha256Hash, authHeader)

        } catch (e: Exception) {
            Logger.logStreamE("$TAG S3 V4上传异常: ${file.name}", e)
            false
        }
    }
}
