package com.dspread.mdm.service.broadcast.handlers.system

import android.content.Context
import android.content.Intent
import android.os.BatteryManager
import com.dspread.mdm.service.network.websocket.message.WsMessageSender
import com.dspread.mdm.service.broadcast.core.BatteryEventHandler
import com.dspread.mdm.service.constants.BatteryConstants
import com.dspread.mdm.service.network.websocket.message.strategy.UploadTriggers
import com.dspread.mdm.service.utils.log.Logger

/**
 * 电池事件处理器实现
 * 统一处理所有电池相关的广播事件
 */
class BatteryEventHandlerImpl : BatteryEventHandler {
    
    private val TAG = "BatteryEventHandler"
    
    // 状态缓存，用于检测变化
    private var lastBatteryLevel = -1
    private var lastBatteryTemperature = -1
    
    override fun getHandlerName(): String = TAG
    
    override fun getSupportedActions(): List<String> {
        return listOf(
            Intent.ACTION_BATTERY_CHANGED,
            Intent.ACTION_BATTERY_LOW,
            Intent.ACTION_BATTERY_OKAY,
            Intent.ACTION_POWER_CONNECTED,
            Intent.ACTION_POWER_DISCONNECTED
        )
    }
    
    override fun handleBroadcast(context: Context, intent: Intent): Boolean {
        val action = intent.action ?: return false
        
        return try {
            when (action) {
                Intent.ACTION_BATTERY_CHANGED -> {
                    handleBatteryChanged(context, intent)
                    true
                }
                Intent.ACTION_BATTERY_LOW -> {
                    onBatteryLow(context)
                    true
                }
                Intent.ACTION_BATTERY_OKAY -> {
                    handleBatteryOkay(context)
                    true
                }
                Intent.ACTION_POWER_CONNECTED -> {
                    onPowerConnectionChanged(context, true)
                    true
                }
                Intent.ACTION_POWER_DISCONNECTED -> {
                    onPowerConnectionChanged(context, false)
                    true
                }
                else -> false
            }
        } catch (e: Exception) {
            Logger.receiverE("$TAG 处理广播失败: $action", e)
            false
        }
    }
    
    override fun onBatteryChanged(context: Context, level: Int, temperature: Int, voltage: Int, status: Int) {
        // 检查是否有显著变化
        val levelChanged = Math.abs(level - lastBatteryLevel) >= 5 // 电量变化5%以上
        val tempChanged = Math.abs(temperature - lastBatteryTemperature) >= 50 // 温度变化5度以上
        
        if (levelChanged || tempChanged || lastBatteryLevel == -1) {
            lastBatteryLevel = level
            lastBatteryTemperature = temperature
            
            val isCharging = status == BatteryManager.BATTERY_STATUS_CHARGING ||
                            status == BatteryManager.BATTERY_STATUS_FULL
            
            Logger.receiver("电池状态变化: 电量=$level%, 温度=${temperature/10}°C, 充电=$isCharging")

            // 上报电池状态
            val trigger = when {
                levelChanged && tempChanged -> "battery_level_change_5" // 电量和温度都变化
                levelChanged -> "battery_level_change_5" // 电量变化5%
                tempChanged -> "temperature_change_5" // 温度变化5度
                else -> "battery_status_change" // 其他电池状态变化
            }
            WsMessageSender.uploadBatteryStatus(trigger)
        }
    }
    
    override fun onBatteryLow(context: Context) {
        Logger.receiver("电池电量低")

        // 只上报电池状态，不发送设备事件
        WsMessageSender.uploadBatteryStatus("battery_low_critical")
    }
    
    override fun onPowerConnectionChanged(context: Context, isConnected: Boolean) {
        if (isConnected) {
            Logger.receiver("电源已连接")
        } else {
            Logger.receiver("电源已断开")
        }

        // 只上报电池状态变化，不发送设备事件
        WsMessageSender.uploadBatteryStatus("charging_state_change")
    }
    
    /**
     * 处理电池电量恢复正常
     */
    private fun handleBatteryOkay(context: Context) {
        Logger.receiver("电池电量恢复正常")
        // 只上报电池状态，不发送设备事件
        WsMessageSender.uploadBatteryStatus(UploadTriggers.CHARGING_STATE_CHANGE)
    }

    /**
     * 处理电池状态变化
     */
    private fun handleBatteryChanged(context: Context, intent: Intent) {
        val level = intent.getIntExtra(BatteryManager.EXTRA_LEVEL, -1)
        val scale = intent.getIntExtra(BatteryManager.EXTRA_SCALE, 100)
        val temperature = intent.getIntExtra(BatteryManager.EXTRA_TEMPERATURE, -1)
        val voltage = intent.getIntExtra(BatteryManager.EXTRA_VOLTAGE, -1)
        val status = intent.getIntExtra(BatteryManager.EXTRA_STATUS, -1)
        val health = intent.getIntExtra(BatteryManager.EXTRA_HEALTH, BatteryManager.BATTERY_HEALTH_UNKNOWN)
        val plugged = intent.getIntExtra(BatteryManager.EXTRA_PLUGGED, 0)

        // 计算电池百分比
        val batteryLevel = if (scale > 0) (level * 100) / scale else level

        // 判断充电状态
        val isCharging = (status == BatteryManager.BATTERY_STATUS_CHARGING) ||
                        (status == BatteryManager.BATTERY_STATUS_FULL) ||
                        (plugged != 0)

        // 更新全局电池常量
        BatteryConstants.updateBatteryStatus(
            level = batteryLevel,
            temperature = temperature,
            health = health,
            isCharging = isCharging
        )

        Logger.receiver("电池状态更新: ${BatteryConstants.getBatteryStatusDescription()}")

        onBatteryChanged(context, batteryLevel, temperature, voltage, status)
    }
}
