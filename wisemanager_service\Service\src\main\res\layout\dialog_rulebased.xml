<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/bg_shape">
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginLeft="25dp"
        android:layout_marginRight="25dp"
        android:orientation="vertical"
        android:background="@color/white">
        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:paddingTop="10dp"
            android:paddingBottom="10dp">
            <TextView
                android:id="@+id/tv_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentLeft="true"
                android:layout_centerVertical="true"
                android:gravity="center"
                android:text="Install Apps"
                android:textSize="18sp"
                android:textColor="@color/black"/>
            <TextView
                android:id="@+id/tv_timeout"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:gravity="center"
                android:text=""
                android:textSize="18sp"
                android:textColor="@color/black"/>
        </RelativeLayout>
        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="#cccccc" />
        <LinearLayout
            android:id="@+id/ll_listapp"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:minHeight="350dp"
            android:orientation="vertical"
            android:background="@color/white">
            <ListView
                android:id="@+id/list_app"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:listSelector="@drawable/item_selector"
                android:dividerHeight="1dp"
                android:divider="@color/divider">
            </ListView>
        </LinearLayout>
        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="#cccccc" />
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:paddingBottom="8dp"
            android:gravity="center_vertical|right"
            android:orientation="horizontal">
            <TextView
                android:id="@+id/tv_delay"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:paddingRight="10dp"
                android:text="@string/later"
                android:textColor="@color/theme_green"
                android:textSize="16sp"/>
            <TextView
                android:id="@+id/tv_install"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:paddingLeft="10dp"
                android:text="@string/install_now"
                android:textColor="@color/theme_green"
                android:textSize="16sp"/>
        </LinearLayout>
    </LinearLayout>
</LinearLayout>