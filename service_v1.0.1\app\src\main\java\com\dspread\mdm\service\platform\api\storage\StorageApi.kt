package com.dspread.mdm.service.platform.api.storage

import android.content.Context
import android.os.Environment
import android.os.StatFs
import com.dspread.mdm.service.platform.api.model.SystemOperationResult
import com.dspread.mdm.service.utils.log.Logger
import java.io.File
import java.io.FileReader
import java.io.FileWriter

/**
 * 存储API
 * 提供文件存储、存储空间查询等功能
 */
class StorageApi(private val context: Context) {
    
    companion object {
        private const val TAG = "StorageApi"
    }
    
    /**
     * 保存字符串到文件
     */
    fun saveStringToFile(fileName: String, content: String): SystemOperationResult {
        return try {
            val file = File(context.filesDir, fileName)
            FileWriter(file).use { writer ->
                writer.write(content)
            }
            Logger.platformI("文件保存成功: $fileName")
            SystemOperationResult.success("File saved successfully: $fileName")
        } catch (e: Exception) {
            Logger.platformE("文件保存失败: $fileName", e)
            SystemOperationResult.failure("Failed to save file: ${e.message}")
        }
    }
    
    /**
     * 从文件读取字符串
     */
    fun readStringFromFile(fileName: String): SystemOperationResult {
        return try {
            val file = File(context.filesDir, fileName)
            if (file.exists()) {
                val content = FileReader(file).use { reader ->
                    reader.readText()
                }
                Logger.platformI("文件读取成功: $fileName")
                SystemOperationResult.success(content)
            } else {
                Logger.platformW("文件不存在: $fileName")
                SystemOperationResult.failure("File not found: $fileName")
            }
        } catch (e: Exception) {
            Logger.platformE("文件读取失败: $fileName", e)
            SystemOperationResult.failure("Failed to read file: ${e.message}")
        }
    }
    
    /**
     * 删除文件
     */
    fun deleteFile(fileName: String): SystemOperationResult {
        return try {
            val file = File(context.filesDir, fileName)
            val deleted = file.delete()
            if (deleted) {
                Logger.platformI("文件删除成功: $fileName")
                SystemOperationResult.success("File deleted successfully: $fileName")
            } else {
                Logger.platformW("文件删除失败: $fileName")
                SystemOperationResult.failure("Failed to delete file: $fileName")
            }
        } catch (e: Exception) {
            Logger.platformE("文件删除异常: $fileName", e)
            SystemOperationResult.failure("Delete file exception: ${e.message}")
        }
    }
    
    /**
     * 检查文件是否存在
     */
    fun fileExists(fileName: String): Boolean {
        return try {
            val file = File(context.filesDir, fileName)
            file.exists()
        } catch (e: Exception) {
            Logger.platformE("检查文件存在性失败: $fileName", e)
            false
        }
    }
    
    /**
     * 获取可用存储空间
     */
    fun getAvailableSpace(): Long {
        return try {
            val stat = StatFs(Environment.getDataDirectory().path)
            stat.availableBytes
        } catch (e: Exception) {
            Logger.platformE("获取可用存储空间失败", e)
            0L
        }
    }
    
    /**
     * 获取总存储空间
     */
    fun getTotalSpace(): Long {
        return try {
            val stat = StatFs(Environment.getDataDirectory().path)
            stat.totalBytes
        } catch (e: Exception) {
            Logger.platformE("获取总存储空间失败", e)
            0L
        }
    }
    
    /**
     * 获取已使用存储空间
     */
    fun getUsedSpace(): Long {
        return try {
            val total = getTotalSpace()
            val available = getAvailableSpace()
            total - available
        } catch (e: Exception) {
            Logger.platformE("计算已使用存储空间失败", e)
            0L
        }
    }
    
    /**
     * 获取存储信息
     */
    fun getStorageInfo(): Map<String, Any> {
        return try {
            val totalSpace = getTotalSpace()
            val availableSpace = getAvailableSpace()
            val usedSpace = totalSpace - availableSpace
            val usagePercentage = if (totalSpace > 0) {
                (usedSpace.toDouble() / totalSpace * 100).toInt()
            } else {
                0
            }
            
            mapOf(
                "totalSpace" to totalSpace,
                "availableSpace" to availableSpace,
                "usedSpace" to usedSpace,
                "usagePercentage" to usagePercentage,
                "totalSpaceGB" to String.format("%.2f", totalSpace / (1024.0 * 1024.0 * 1024.0)),
                "availableSpaceGB" to String.format("%.2f", availableSpace / (1024.0 * 1024.0 * 1024.0)),
                "usedSpaceGB" to String.format("%.2f", usedSpace / (1024.0 * 1024.0 * 1024.0))
            )
        } catch (e: Exception) {
            Logger.platformE("获取存储信息失败", e)
            mapOf(
                "error" to (e.message ?: "Unknown error"),
                "timestamp" to System.currentTimeMillis()
            )
        }
    }
    
    /**
     * 清理临时文件
     */
    fun cleanTempFiles(): SystemOperationResult {
        return try {
            val tempDir = File(context.cacheDir, "temp")
            if (tempDir.exists()) {
                val deleted = tempDir.deleteRecursively()
                if (deleted) {
                    Logger.platformI("临时文件清理成功")
                    SystemOperationResult.success("Temp files cleaned successfully")
                } else {
                    Logger.platformW("临时文件清理失败")
                    SystemOperationResult.failure("Failed to clean temp files")
                }
            } else {
                Logger.platformI("临时文件目录不存在")
                SystemOperationResult.success("No temp files to clean")
            }
        } catch (e: Exception) {
            Logger.platformE("清理临时文件异常", e)
            SystemOperationResult.failure("Clean temp files exception: ${e.message}")
        }
    }

    /**
     * 判断SD卡是否可用
     */
    fun isSDCardAvailable(): Boolean {
        return Environment.getExternalStorageState() == Environment.MEDIA_MOUNTED
    }

    /**
     * 获取公共SD卡路径
     */
    fun getPublicSDCardPath(): String? {
        return if (isSDCardAvailable()) {
            Environment.getExternalStorageDirectory().absolutePath + File.separator
        } else {
            null
        }
    }

    /**
     * 获取应用专属外部存储目录
     */
    fun getAppExternalPath(): String {
        return context.getExternalFilesDir(null)?.absolutePath + File.separator
    }

    /**
     * 获取配置文件路径（应用专属目录）
     */
    fun getConfigFileForApp(fileName: String = "config.txt"): File {
        return File(getAppExternalPath(), fileName)
    }

    /**
     * 获取配置文件路径（SD卡公共目录）
     */
    fun getConfigFileForSDCard(fileName: String = "config.txt"): File? {
        val sdPath = getPublicSDCardPath()
        return if (sdPath != null) {
            File(sdPath, fileName)
        } else {
            null
        }
    }

    /**
     * 获取指定路径的可用空间
     */
    fun getAvailableBytes(path: String): Long {
        return try {
            val statFs = StatFs(path)
            statFs.availableBlocksLong * statFs.blockSizeLong
        } catch (e: Exception) {
            Logger.platformE("获取可用空间失败: $path", e)
            0L
        }
    }

    /**
     * 获取指定路径的总空间
     */
    fun getTotalBytes(path: String): Long {
        return try {
            val statFs = StatFs(path)
            statFs.totalBytes
        } catch (e: Exception) {
            Logger.platformE("获取总空间失败: $path", e)
            0L
        }
    }

    /**
     * 获取外部存储详细信息
     */
    fun getExternalStorageInfo(): Map<String, Any> {
        return try {
            val isAvailable = isSDCardAvailable()
            val appPath = getAppExternalPath()
            val publicPath = getPublicSDCardPath()

            mapOf(
                "isSDCardAvailable" to isAvailable,
                "appExternalPath" to appPath,
                "publicSDCardPath" to (publicPath ?: ""),
                "appAvailableBytes" to getAvailableBytes(appPath),
                "appTotalBytes" to getTotalBytes(appPath),
                "publicAvailableBytes" to if (publicPath != null) getAvailableBytes(publicPath) else 0L,
                "publicTotalBytes" to if (publicPath != null) getTotalBytes(publicPath) else 0L,
                "timestamp" to System.currentTimeMillis()
            )
        } catch (e: Exception) {
            Logger.platformE("获取外部存储信息失败", e)
            mapOf(
                "error" to (e.message ?: "Unknown error"),
                "timestamp" to System.currentTimeMillis()
            )
        }
    }
}
