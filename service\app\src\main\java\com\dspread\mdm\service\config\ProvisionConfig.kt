package com.dspread.mdm.service.config

import android.content.Context
import com.dspread.mdm.service.modules.provisioning.model.*
import com.dspread.mdm.service.constants.Constants
import java.io.File

/**
 * Provisioning配置管理
 * 支持获取默认配置和配置下载目录
 */
object ProvisionConfig {

    // Provisioning 相关常量
    const val PROVISIONING_DEFAULT_INTERVAL = 12 * 60 * 60L // 12小时（秒）
    const val PROVISIONING_CONFIG_FILE = "provisioning_config.json"
    const val PROVISIONING_FLAGS_FILE = "provisioning_flags.json"
    const val PROVISIONING_RETRY_COUNT = 3
    const val PROVISIONING_RETRY_DELAY = 5000L // 5秒重试延迟

    // 默认配置服务器URL
    const val DEFAULT_CONFIG_URL = "http://35.75.3.206:8080/status/config"

    // 生产环境服务器URL
//    const val DEFAULT_CONFIG_URL = "https://config.dspreadserv.net/status/config"

    private var isDebugMode = false

    /**
     * 设置调试模式
     */
    fun setDebugMode(enabled: Boolean) {
        isDebugMode = enabled
    }
    
    /**
     * 获取默认配置
     */
    fun getDefaultConfig(): ProvisioningConfig {
        return ProvisioningConfig(
            cid = "10001",
            requestTime = "1755508964344",
            polling = PollingConfig(
                statusApiUrl = "wss://api.dspreadserv.net/status/websocket/register",
                remoteUrl = "wss://remote.dspreadserv.net/remoteWSS/websockify",
                heartbeatTime = "300",
                terminalInfoTime = "900",
                uploadMode = "1",
                wssReconnConfig = WssReconnConfig(
                    delayPolicy = "1",
                    delaySwitch = "1",
                    delayTime = "60",
                    pingInterval = "30",
                    pongTimeout = "90"
                )
            ),
            system = SystemConfig(
                logo = "https://siot-bucket01.s3.ap-northeast-1.amazonaws.com/test/logo.bin",
                logoMd5 = "ba1ee533924eae5c408465e7cddcbda4",
                bootAnimation = "https://siot-bucket01.s3.ap-northeast-1.amazonaws.com/test/bootanimation.zip",
                bootAnimationMd5 = "ba1ee533924eae5c408465e7cddcbda4",
                timezone = "Asia/Hong_Kong",
                gpsConfig = GpsConfig(
                    minUpdateTime = "30",
                    scheduleTime = "60",
                    maxLocateTime = "0",
                    minDistance = "10",
                    validDistance = "500",
                    care = "1"
                ),
                powerSaveMode = PowerSaveMode(
                    enable = "1",
                    screenTimeout = "0"
                )
            )
        )
    }
    
    /**
     * 获取基础存储路径（主目录的父目录）
     */
    private fun getBasePath(): String {
        val basePath = if (isDebugMode) {
            // 调试模式：第三优先级
            Constants.ModuleConstants.TERTIARY_PATH
        } else {
            // 生产模式：获取config目录的父目录
            val configPath = Constants.ModuleConstants.CONFIG_STORAGE_PATH
            val configDir = File(configPath)
            configDir.parent ?: Constants.ModuleConstants.PRIMARY_PATH
        }
        // 确保路径以斜杠结尾
        return if (basePath.endsWith("/")) basePath else "$basePath/"
    }

    /**
     * 获取配置文件目录
     */
    fun getConfigDownloadPath(): String {
        return if (isDebugMode) {
            // 调试模式：第三优先级
            Constants.ModuleConstants.TERTIARY_PATH + "config/"
        } else {
            // 生产模式：直接使用已设置的主目录
            Constants.ModuleConstants.CONFIG_STORAGE_PATH
        }
    }

    /**
     * 获取配置文件的完整路径
     */
    fun getConfigFilePath(fileName: String): String {
        return getConfigDownloadPath() + fileName
    }

    /**
     * 获取Provisioning配置文件路径
     */
    fun getProvisioningConfigFilePath(): String {
        return getConfigFilePath(PROVISIONING_CONFIG_FILE)
    }

    /**
     * 获取Provisioning标志文件路径
     */
    fun getProvisioningFlagsFilePath(): String {
        return getConfigFilePath(PROVISIONING_FLAGS_FILE)
    }

    /**
     * 获取Logo文件完整路径
     */
    fun getLogoFilePath(context: Context): String {
        val basePath = getBasePath()
        return if (isDebugMode || !basePath.startsWith("/data/pos/")) {
            // 调试模式或备选目录：文件在config目录
            "${basePath}config/logo.bin"
        } else {
            // 主目录：文件在media子目录
            "${basePath}media/logo.bin"
        }
    }

    /**
     * 获取开机动画文件完整路径
     */
    fun getBootAnimationFilePath(context: Context): String {
        val basePath = getBasePath()
        return if (isDebugMode || !basePath.startsWith("/data/pos/")) {
            // 调试模式或备选目录：文件在config目录
            "${basePath}config/bootanimation.zip"
        } else {
            // 主目录：文件在media子目录
            "${basePath}media/bootanimation.zip"
        }
    }



    /**
     * 获取默认Ping间隔（秒）
     */
    fun getDefaultPingInterval(): Long = 30L
}
