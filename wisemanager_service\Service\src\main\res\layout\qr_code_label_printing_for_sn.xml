<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="320dp"
    android:layout_height="wrap_content"
    android:layout_gravity="center"
    android:background="@drawable/shape_corner_round"
    android:gravity="center"
    android:orientation="vertical"
    android:padding="0dp"
    android:paddingLeft="0dp"
    android:paddingTop="0dp"
    android:paddingRight="0dp"
    android:paddingBottom="0dp">

    <TextView
        android:id="@+id/label"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_50"
        android:layout_gravity="center"
        android:gravity="center"
        android:text="Label Printing"
        android:textColor="@color/black"
        android:textSize="22sp"
        android:textStyle="bold" />

    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="@color/divider_ex"
        android:visibility="gone"/>

    <ImageView
        android:id="@+id/qrCodeImg"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:padding="10dp"
        android:src="@drawable/ic_launcher" />

    <LinearLayout
        android:id="@+id/full_info"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_5"
        android:layout_marginBottom="@dimen/dp_10"
        android:layout_gravity="center"
        android:gravity="center|left"
        android:orientation="vertical">

        <TextView
            android:id="@+id/full_info_sn"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center|left"
            android:padding="@dimen/dp_5"
            android:textColor="@color/black"
            android:textSize="18sp"
            tools:text="SN:WSS508933000009" />

        <TextView
            android:id="@+id/full_info_imie1"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center|left"
            android:padding="@dimen/dp_5"
            android:textColor="@color/black"
            android:textSize="18sp"
            tools:text="IMEI 1:508933000009" />

        <TextView
            android:id="@+id/full_info_imei2"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center|left"
            android:padding="@dimen/dp_5"
            android:textColor="@color/black"
            android:textSize="18sp"
            tools:text="IMEI 2:508933000009" />
    </LinearLayout>

    <TextView
        android:id="@+id/sn"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_50"
        android:layout_gravity="center"
        android:gravity="center"
        android:padding="@dimen/dp_10"
        android:textColor="@color/black"
        android:textSize="20sp"
        android:textStyle="bold"
        tools:text="SN:WSS508933000009" />

    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="@color/divider_ex" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/shape_corner_down"
        android:gravity="center_vertical|right"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/back"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:padding="15dp"
            android:text="BACK"
            android:textColor="@color/theme_green"
            android:textSize="16sp"
            android:visibility="visible" />

        <View
            android:id="@+id/divider"
            android:layout_width="1dp"
            android:layout_height="match_parent"
            android:background="@color/divider_ex"
            android:visibility="visible" />

        <TextView
            android:id="@+id/next"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:padding="15dp"
            android:text="Next"
            android:textColor="@color/theme_green"
            android:textSize="16sp" />
    </LinearLayout>

</LinearLayout>