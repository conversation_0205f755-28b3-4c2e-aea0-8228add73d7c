package com.dspread.mdm.service.constants

import android.content.Context
import com.dspread.mdm.service.utils.log.Logger
import java.io.File

/**
 * 路径常量
 */
object PathConstants {
    
    /**
     * 获取APK文件下载目录
     */
    fun getApkDownloadDir(context: Context): File {
        // 使用外部存储应用专用目录，无需额外权限
        val apkDir = File(context.getExternalFilesDir(null), "apk")
        
        // 确保目录存在
        if (!apkDir.exists()) {
            apkDir.mkdirs()
        }
        
        return apkDir
    }
    
    /**
     * 获取APK文件完整路径
     * @param context 上下文
     * @param taskId 任务ID
     * @return APK文件路径
     */
    fun getApkFilePath(context: Context, taskId: String): String {
        val apkDir = getApkDownloadDir(context)
        // 清理文件名中的特殊字符，避免PackageInstaller解析问题
        val cleanTaskId = taskId.replace("[^a-zA-Z0-9._-]".toRegex(), "_")
        return File(apkDir, "$cleanTaskId.apk").absolutePath
    }
    
    /**
     * 获取日志文件目录
     */
    fun getLogDir(context: Context): File {
        val logDir = File(context.getExternalFilesDir(null), "logs")
        if (!logDir.exists()) {
            logDir.mkdirs()
        }
        return logDir
    }
    
    /**
     * 获取配置文件目录
     */
    fun getConfigDir(context: Context): File {
        val configDir = File(context.getExternalFilesDir(null), "config")
        if (!configDir.exists()) {
            configDir.mkdirs()
        }
        return configDir
    }
    
    /**
     * 获取临时文件目录
     */
    fun getTempDir(context: Context): File {
        val tempDir = File(context.getExternalFilesDir(null), "temp")
        if (!tempDir.exists()) {
            tempDir.mkdirs()
        }
        return tempDir
    }
    
    /**
     * 清理过期的APK文件
     * @param context 上下文
     * @param maxAgeMs 最大保留时间（毫秒）
     */
    fun cleanupOldApkFiles(context: Context, maxAgeMs: Long = 7 * 24 * 60 * 60 * 1000L) {
        try {
            val apkDir = getApkDownloadDir(context)
            val currentTime = System.currentTimeMillis()
            
            apkDir.listFiles()?.forEach { file ->
                if (file.isFile && file.name.endsWith(".apk")) {
                    val fileAge = currentTime - file.lastModified()
                    if (fileAge > maxAgeMs) {
                        if (file.delete()) {
                            Logger.com("删除过期APK文件: ${file.name}")
                        }
                    }
                }
            }
        } catch (e: Exception) {
            Logger.com("清理过期APK文件失败: ${e.message}")
        }
    }
    
    /**
     * 获取目录使用情况
     */
    fun getDirectoryInfo(context: Context): String {
        return try {
            val apkDir = getApkDownloadDir(context)
            val files = apkDir.listFiles() ?: emptyArray()
            val totalSize = files.sumOf { it.length() }
            val apkCount = files.count { it.name.endsWith(".apk") }
            
            buildString {
                append("APK目录信息:\n")
                append("路径: ${apkDir.absolutePath}\n")
                append("文件数量: ${files.size}\n")
                append("APK文件数量: $apkCount\n")
                append("总大小: ${totalSize / 1024 / 1024}MB\n")
                append("可用空间: ${apkDir.freeSpace / 1024 / 1024}MB")
            }
        } catch (e: Exception) {
            "获取目录信息失败: ${e.message}"
        }
    }
}
