package com.dspread.mdm.service.ui.activity

import android.annotation.SuppressLint
import android.app.Activity
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.net.ConnectivityManager
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.os.Message
import android.os.PowerManager
import android.text.TextUtils
import android.util.TypedValue
import android.view.KeyEvent
import android.view.View
import android.widget.Button
import android.widget.LinearLayout
import android.widget.TextView
import android.widget.Toast
import com.dspread.mdm.service.R
import com.dspread.mdm.service.constants.Constants
import com.dspread.mdm.service.modules.geofence.location.GpsLocationManager
import com.dspread.mdm.service.network.websocket.message.WsMessageSender
import com.dspread.mdm.service.broadcast.core.BroadcastActions
import com.dspread.mdm.service.broadcast.core.BroadcastSender
import com.dspread.mdm.service.ui.dialog.RebootFloatWindow
import com.dspread.mdm.service.ui.view.PasswordEditText
import com.dspread.mdm.service.utils.log.Logger
import java.util.*

/**
 * 地理围栏锁屏Activity
 * 实现PasswordEditText.PasswordFullListener接口
 */
class LockScreenActivity : Activity(), PasswordEditText.PasswordFullListener {
    
    companion object {
        private const val TAG = "LockScreen"

        // 静态变量，用于OTP管理
        @Volatile
        private var unlockPwd = ""
        @Volatile
        private var expireTime = 0L
        @Volatile
        private var lastOTPGotTime = 0L
    }
    
    // UI组件
    private lateinit var llLockdeviceInput: LinearLayout
    private lateinit var llLockdevice: LinearLayout
    private lateinit var tvTip: TextView
    private lateinit var tvWarning: TextView
    private lateinit var tvNoPwd: TextView
    private lateinit var passwordEditText: PasswordEditText
    private lateinit var btnSubmit: Button
    
    // 状态变量
    private var mCount = 0
    private var mWipeDataCount = GpsLocationManager.geoWipeMins
    private var mode = "0" // mode代表创建LockScreen时是否显示解锁密码输入框模式UI
    private var finishFromExternal = false
    private var finishWithoutCallback = false
    private var isScreenOff = false
    private var hasResume = false // 表明LockScreen UI是否已经恢复最前面显示
    private var bDataWiped = false
    private var otpCounter = 5
    private var isUIInitialized = false // UI组件是否已初始化完成
    
    // 定时器
    private var timer: Timer? = null
    private var mTimerTask: TimerTask? = null

    // 悬浮窗
    private var wipeDataFloatWindow: RebootFloatWindow? = null
    
    // Handler
    private val mHandler = Handler(Looper.getMainLooper()) { msg ->
        when (msg.what) {
            0 -> {
                Logger.geo("$TAG Handler消息0: 关闭Activity")
                finishFromExternal = true
                finish()
                true
            }
            else -> false
        }
    }
    
    // 广播接收器
    private lateinit var mIntentFilter: IntentFilter
    private val mReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context, intent: Intent) {
            val action = intent.action
            Logger.geo("$TAG LockScreenActivity BroadcastReceiver: $action")
            
            when (action) {
                BroadcastActions.ACTION_CLOSE_LOCKSCREEN -> {
                    Logger.geo("$TAG ACTION_CLOSE_LOCKSCREEN bDataWiped=$bDataWiped")
                    Logger.geo("$TAG Constants.geofenceStatus=${Constants.geofenceStatus}")

                    if (Constants.geofenceStatus == GpsLocationManager.WIPE_DATA) {
                        setLauncherApp()
                    } else {
                        if (bDataWiped) {
                            setLauncherApp()
                        }
                    }

                    val message = Message()
                    message.what = 0
                    mHandler.sendMessageDelayed(message, 200)
                }
                
                BroadcastActions.ACTION_DATA_WIPED -> {
                    bDataWiped = true
                    showPwdErrorWarning(false)
                    showStopWipingUI()
                    otpCounter = 5
                    saveOtpCounter()
                    Logger.geo("$TAG ACTION_DATA_WIPED bDataWiped=$bDataWiped")
                    GpsLocationManager.setCurrentGPSStatus(this@LockScreenActivity, GpsLocationManager.WIPE_DATA)
                    // 上报数据信息
                    WsMessageSender.uploadDataInfoForGeo(System.currentTimeMillis())
                }

                BroadcastActions.ACTION_ENTER_GEOFENCE -> {

                    if (Constants.geofenceStatus == GpsLocationManager.LOCK_SCREEN && GpsLocationManager.geoWipeStatus) {
                        timer?.let {
                            stopTimeSchedule()
                        }
                    }
                    showLockDeviceInputDialog()
                }

                BroadcastActions.ACTION_OUT_OF_GEOFENCE -> {
                    if (Constants.geofenceStatus == GpsLocationManager.LOCK_SCREEN && GpsLocationManager.geoWipeStatus) {
                        if (timer == null) {
                            startTimeSchedule()
                        }
                    }
                    showLockDeviceDialog()
                }

                BroadcastActions.ACTION_GET_ONETIME_PWD -> {
                    if (intent.hasExtra("password") && intent.hasExtra("expiration_time")) {
                        lastOTPGotTime = System.currentTimeMillis()
                        unlockPwd = intent.getStringExtra("password") ?: ""
                        expireTime = intent.getLongExtra("expiration_time", 0L)
                        hideNoPwdWarning()

                        mHandler.post {
                            Toast.makeText(this@LockScreenActivity, "A new OTP is created, please check with help desk", Toast.LENGTH_LONG).show()
                        }
                        Logger.geo("$TAG 收到OTP密码，过期时间: $expireTime")
                    }
                }

                BroadcastActions.ACTION_WIPE_PARAM_CHANGE -> {
                    val wipeStatus = intent.getBooleanExtra("GEO_WIPE_STATUS", false)
                    val wipeMins = intent.getIntExtra("GEO_WIPE_MINS", GpsLocationManager.geoWipeMins)
                    Logger.geo("$TAG update param: wipeStatus=$wipeStatus wipeMins=$wipeMins")
                    Logger.geo("$TAG original param: wipeStatus=${GpsLocationManager.geoWipeStatus} wipeMins=${GpsLocationManager.geoWipeMins}")

                    if (GpsLocationManager.geoWipeStatus && !wipeStatus) {
                        if (Constants.geofenceStatus == GpsLocationManager.LOCK_SCREEN && GpsLocationManager.geoWipeStatus) {
                            stopTimeSchedule()
                            GpsLocationManager.geoWipeStatus = wipeStatus
                            GpsLocationManager.geoWipeMins = wipeMins

                            // 保存擦除状态和时间到SharedPreferences
                            saveWipeStatus(wipeStatus)
                            saveWipeMins(wipeMins)
                                
                        }
                    } else if (!GpsLocationManager.geoWipeStatus && wipeStatus) {
                        if (Constants.geofenceStatus == GpsLocationManager.LOCK_SCREEN && !GpsLocationManager.geoWipeStatus) {
                            GpsLocationManager.geoWipeStatus = wipeStatus
                            GpsLocationManager.geoWipeMins = wipeMins

                            // 保存擦除状态和时间到SharedPreferences
                            saveWipeStatus(wipeStatus)
                            saveWipeMins(wipeMins)

                            mWipeDataCount = GpsLocationManager.geoWipeMins
                            startTimeSchedule()
                        }
                    } else if (GpsLocationManager.geoWipeStatus && wipeStatus && GpsLocationManager.geoWipeMins != wipeMins) {
                        if (Constants.geofenceStatus == GpsLocationManager.LOCK_SCREEN && GpsLocationManager.geoWipeStatus) {
                            GpsLocationManager.geoWipeMins = wipeMins
                            // 保存擦除时间到SharedPreferences
                            saveWipeMins(wipeMins)
                            mWipeDataCount = GpsLocationManager.geoWipeMins
                            startTimeSchedule()
                        }
                    }
                }

                BroadcastActions.ACTION_WEBSOCKET_CONNECTION -> {
                    if (intent.hasExtra("connection")) {
                        val websocketConnected = intent.getBooleanExtra("connection", false)
                        Logger.geo("$TAG WebSocket连接状态: $websocketConnected")
                        if (websocketConnected) {
                            // WebSocket连接成功的处理
                        } else {
                            // WebSocket连接断开的处理
                        }
                    }
                }

                Intent.ACTION_SCREEN_OFF -> {
                    isScreenOff = true
                    Logger.geo("$TAG 屏幕关闭")
                }
                
                Intent.ACTION_SCREEN_ON -> {
                    isScreenOff = false
                    
                    if (!hasResume) {
                        //屏幕点亮时，如果已经是最前端显示，则不再重复启动LockScreen；
                        //否则LockScreen已被覆盖，则启动（解决锁屏时，息屏亮屏后回到login UI，lockScreen被覆盖的问题）
                        //做个延时，避免快速点击的时候，被自己onPause的代码执行到onDestroy杀死；
                        mHandler.postDelayed({
                            Logger.geo("$TAG recreate from screen on")
                            finishWithoutCallback = true
                            finishFromExternal = false
                            finish()

                            // 根据当前UI状态重新启动对应的LockScreen
                            if (::passwordEditText.isInitialized && passwordEditText.visibility == View.VISIBLE) {
                                // 当前是输入模式，启动输入模式的LockScreen
                                val intent = Intent(this@LockScreenActivity, LockScreenActivity::class.java)
                                intent.putExtra("mode", "1")
                                startActivity(intent)
                            } else {
                                // 当前是普通锁屏模式，启动普通LockScreen
                                val intent = Intent(this@LockScreenActivity, LockScreenActivity::class.java)
                                startActivity(intent)
                            }

                        }, 1000)
                    }
                }
                
                ConnectivityManager.CONNECTIVITY_ACTION -> {
                    Logger.geo("$TAG LockScreenActivity receives CONNECTIVITY_ACTION")
                }
            }
        }
    }
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        Logger.geo("$TAG LockScreenActivity onCreate")
        
        // mode代表创建LockScreen时是否显示解锁密码输入框模式UI
        if (intent.hasExtra("mode")) {
            mode = intent.getStringExtra("mode") ?: "0"
        }
        
        setContentView(R.layout.activity_lock_screen)
        
        // 检查地理围栏状态
        if (!GpsLocationManager.geoStatus) {
            Logger.geo("$TAG **** GEOFENCE is disable，finish ****")
            GpsLocationManager.setCurrentGPSStatus(this, GpsLocationManager.IN_ZONE)
            finishFromExternal = true
            finish()
            return
        }
        
        // 处理不同的地理围栏状态
        handleGeofenceStatus()
        
        // 获取OTP计数器
        otpCounter = getOtpCounter()
        
        initView()
        registerBroadcastReceiver()
    }

    override fun onResume() {
        super.onResume()
        Logger.geo("$TAG LockScreenActivity onResume ${Constants.geofenceStatus}")
        hasResume = true
        // 更新系统属性
        // SysIntermediateApi.getInstance().updateSystemProperty("true")
    }

    override fun onPause() {
        super.onPause()
        Logger.geo("$TAG LockScreenActivity onPause Constants.geofenceStatus=${Constants.geofenceStatus}")
        Logger.geo("$TAG LockScreenActivity onPause finishFromExternal=$finishFromExternal")
        hasResume = false

        if (!finishFromExternal) {
            // 以下这段onPause代码作用于LockScreen被其他UI覆盖退到后台时，能再次startActivity回到前台；
            // 但是lockScreen一直在前台时的息屏动作，也会触发onPause，此时应做排除；
            // 用isScreenOff变量来判断是否是息屏的情况（但是ACTION_SCREEN_OFF的广播比较onPause慢，所以用isScreenOn来判断实际情况）
            // 为避免此种case会误执行restartActivity，那么此时靠hasResume变量，在onResume中提前设置，来表明lockScreen是否已经在亮屏时是在前台；
            // isScreenOff 和 hasResume 和 isScreenOn()共同作用，来区分lockScreen一直在前台时手动息屏亮屏时，能够不执行restartActivity的代码；
            mHandler.postDelayed({
                Logger.geo("$TAG LockScreenActivity isScreenOff：$isScreenOff, hasResume：$hasResume")
                if (!isScreenOff && !hasResume && isScreenOn()) {
                    Logger.geo("$TAG recreate from pause")
                    finishWithoutCallback = true
                    finishFromExternal = false
                    finish()

                    // 重新启动LockScreenActivity
                    val intent = Intent(this, LockScreenActivity::class.java)
                    if (::passwordEditText.isInitialized && passwordEditText.visibility == View.VISIBLE) {
                        intent.putExtra("mode", "1")
                    }
                    startActivity(intent)
                }
            }, 1000)
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        Logger.geo("$TAG LockScreenActivity onDestroy")

        // 第一阶段：系统状态清理和重置
        if (!finishWithoutCallback) {
            Logger.geo("$TAG LockScreenActivity onDestroy 第一阶段：系统状态清理和重置" )
            if (Constants.ModuleConstants.M_GEOFENCE_SET_SERVICE_LAUNCHER) {
                // 方案A: Service作为Launcher的处理
                if (Constants.geofenceStatus == GpsLocationManager.WIPE_DATA) {
                    // 设置Launcher应用
                    setLauncherApp()
                }
            } else {
                // 方案B: 传统Unbox方案的处理
                if (Constants.isUnboxRunning) {
                    Constants.isUnboxRunning = true
                    GpsLocationManager.shutDownGeoFenceDetect(this)
                }
            }
            // 更新系统属性为false（表示锁屏结束）
            // SysIntermediateApi.getInstance().updateSystemProperty("false")
        }

        // 第二阶段：强制资源释放（无论什么情况都要执行）
        Logger.geo("$TAG LockScreenActivity onDestroy 中间阶段：强制资源释放" )
        unregisterBroadcastReceiver()
        if (timer != null) {
            timer?.cancel()
            timer = null
        }
        // 关闭数据擦除倒计时悬浮窗
        closeWipeDataFloatWindow()

        // 第三阶段：状态恢复和数据上报
        if (!finishWithoutCallback) {
            Logger.geo("$TAG LockScreenActivity onDestroy 第三阶段：状态恢复和数据上报" )
            if (Constants.geofenceStatus != GpsLocationManager.ROAMING) {
                // 正常状态恢复：设置为IN_ZONE并重新注册位置监听
                GpsLocationManager.setCurrentGPSStatus(this, GpsLocationManager.IN_ZONE)
                GpsLocationManager.registerLocationChangeListener(this)
            } else {
                // 漫游状态的特殊处理
                if (bDataWiped) {
                    setLauncherApp()
                    // LauncherDeamonReceiver.restartLoaderProcess() // 如果需要重启Loader进程
                }
            }
            // 最终数据上报
            WsMessageSender.uploadDataInfoForGeo(System.currentTimeMillis())
        }
    }

    override fun onBackPressed() {
        // 禁用返回键
        Logger.geo("$TAG 返回键被禁用")
    }

    override fun onKeyDown(keyCode: Int, event: KeyEvent?): Boolean {
        // 禁用Home键和菜单键
        return when (keyCode) {
            KeyEvent.KEYCODE_HOME,
            KeyEvent.KEYCODE_MENU,
            KeyEvent.KEYCODE_BACK -> {
                Logger.geo("$TAG 系统按键被禁用: $keyCode")
                true
            }
            else -> super.onKeyDown(keyCode, event)
        }
    }

    // ========== 功能实现方法 ==========

    /**
     * 处理地理围栏状态
     */
    private fun handleGeofenceStatus() {
        when {
            Constants.geofenceStatus <= GpsLocationManager.LOCK_SCREEN -> {
                if (Constants.geofenceStatus < GpsLocationManager.LOCK_SCREEN) {
                    GpsLocationManager.setCurrentGPSStatus(this, GpsLocationManager.LOCK_SCREEN)
                    mWipeDataCount = GpsLocationManager.geoWipeMins
                    Logger.geo("$TAG 状态变更为LOCK_SCREEN，重置擦除倒计时: ${mWipeDataCount}mins")
                    saveWipeCountdown(mWipeDataCount)
                    // 状态变更时上报数据信息
                    WsMessageSender.uploadDataInfoForGeo(System.currentTimeMillis())
                } else if (Constants.geofenceStatus == GpsLocationManager.LOCK_SCREEN) {
                    val savedCountdown = getWipeCountdown()
                    Logger.geo("$TAG 当前已是LOCK_SCREEN状态")
                    Logger.geo("$TAG 从SharedPreferences读取倒计时: ${savedCountdown}mins")
                    Logger.geo("$TAG 当前配置的擦除时间: ${GpsLocationManager.geoWipeMins}mins")

                    // 直接使用保存的倒计时值
                    mWipeDataCount = savedCountdown
                    Logger.geo("$TAG 使用保存的倒计时值: ${mWipeDataCount}mins")
                }

                // 在LOCK_SCREEN状态下检查是否启动倒计时
                if (GpsLocationManager.geoWipeStatus) {
                    Logger.geo("$TAG onCreate时检查倒计时启动条件")
                    Logger.geo("$TAG   geoWipeStatus=${GpsLocationManager.geoWipeStatus}")
                    Logger.geo("$TAG   geoWipeMins=${GpsLocationManager.geoWipeMins}")
                    Logger.geo("$TAG   mWipedataCount最终值=${mWipeDataCount}")
                    Logger.geo("$TAG   从SharedPreferences读取的值=${getWipeCountdown()}")

                    // 检查mWipedataCount是否合理
                    if (mWipeDataCount <= 0) {
                        Logger.geo("$TAG mWipedataCount值异常(${mWipeDataCount})，强制重置为geoWipeMins")
                        mWipeDataCount = GpsLocationManager.geoWipeMins
                        saveWipeCountdown(mWipeDataCount)
                    }

                    Logger.geo("$TAG onCreate时启动倒计时: ${mWipeDataCount} mins")
                    startTimeSchedule()
                } else {
                    Logger.geo("$TAG onCreate时不启动倒计时，geoWipeStatus=${GpsLocationManager.geoWipeStatus}")
                }
            }

            Constants.geofenceStatus == GpsLocationManager.WIPE_DATA -> {
                bDataWiped = true
                Logger.geo("$TAG onCreate时检测到WIPE_DATA状态，bDataWiped=$bDataWiped")
            }
        }
    }

    /**
     * 初始化视图
     */
    @SuppressLint("CutPasteId", "SetTextI18n")
    private fun initView() {
        try {
            // 设置字体
            findViewById<TextView>(R.id.tv_title_1)?.let { textView ->
                // textView.typeface = customTypeface // 如果有自定义字体
            }
            findViewById<TextView>(R.id.tv_content_1)?.let { textView ->
                // textView.typeface = customTypeface // 如果有自定义字体
            }
            findViewById<TextView>(R.id.tv_title_2)?.let { textView ->
                // textView.typeface = customTypeface // 如果有自定义字体
            }

            // 设置设备序列号
            findViewById<TextView>(R.id.tv_sn)?.text = "Device ID: ${getDeviceSerialNumber()}"

            // 初始化UI组件
            tvWarning = findViewById(R.id.tv_warning)
            tvNoPwd = findViewById(R.id.tv_no_pwd)
            tvTip = findViewById(R.id.tv_tip)

            // 初始化主要容器
            llLockdevice = findViewById(R.id.ll_lockdevice)
            llLockdevice.visibility = View.VISIBLE
            llLockdeviceInput = findViewById(R.id.ll_lockdevice_input)
            llLockdeviceInput.visibility = View.GONE

            // 初始化密码输入框和按钮
            passwordEditText = findViewById(R.id.pet_pwd)
            passwordEditText.setOnPasswordFullListener(this)

            showPwdErrorWarning(true);

            btnSubmit = findViewById(R.id.btn_submit)

            // 检查是否有解锁密码
            if (TextUtils.isEmpty(unlockPwd)) {
                showNoPwdWarning()
            }

            // 设置提交按钮点击监听
            setupPasswordInput()

            // 根据mode显示不同UI
            if (mode == "1") {
                showLockDeviceInputDialog()
            } else {
                showLockDeviceDialog()
            }

            // 🔧标记UI初始化完成
            isUIInitialized = true
            Logger.geo("$TAG UI组件初始化完成")

        } catch (e: Exception) {
            Logger.geoE("$TAG 初始化视图失败", e)
        }
    }

    /**
     * 设置密码输入
     */
    private fun setupPasswordInput() {
        btnSubmit.setOnClickListener { v ->
            val passwordText = passwordEditText.text.toString()
            Logger.geo("$TAG btnSubmit ${passwordText.take(4).padEnd(passwordText.length, '*')}")

            if (TextUtils.isEmpty(unlockPwd)) {
                Logger.geo("$TAG $passwordText")
                Toast.makeText(this, "No verify password", Toast.LENGTH_SHORT).show()
                errorAndShakeClear()
                return@setOnClickListener
            }

            if (TextUtils.isEmpty(passwordText)) {
                Toast.makeText(this, "Error input", Toast.LENGTH_SHORT).show()
                errorAndShakeClear()
                return@setOnClickListener
            }

            if (!TextUtils.isEmpty(unlockPwd) && passwordText != unlockPwd) {
                Toast.makeText(this, "Incorrect password", Toast.LENGTH_SHORT).show()
                checkOTPCounter()
                errorAndShakeClear()
            } else {
                Logger.geo("$TAG System.currentTimeMillis()=${System.currentTimeMillis()} expireTime=$expireTime")
                if (expireTime > 0 && System.currentTimeMillis() > expireTime) {
                    Toast.makeText(this, "OTP expire", Toast.LENGTH_SHORT).show()
                    checkOTPCounter()
                    errorAndShakeClear()
                } else {
                    otpCounter = 5
                    unlockPwd = ""
                    expireTime = 0L
                    saveOtpCounter()
                    Logger.geo("$TAG Constants.geofenceStatus=${Constants.geofenceStatus}")
                    if (Constants.geofenceStatus == GpsLocationManager.WIPE_DATA) {
                        setLauncherApp()
                    } else {
                        if (bDataWiped) {
                            setLauncherApp()

                            // 🔧如果设备之前被设置了wipeData状态，发送UNBIND_LAUNCHER广播执行清除操作
                            if (Constants.geofenceStatus == GpsLocationManager.WIPE_DATA ||
                                Constants.bUnboxResetFromGeo) {
                                Logger.geo("$TAG 检测到需要执行数据擦除，发送UNBIND_LAUNCHER广播")
                                val intent = Intent(BroadcastActions.UNBIND_LAUNCHER)
                                sendBroadcast(intent, "com.dspread.mdm.service.permission.DSPREAD")
                            }
                        }
                    }
                    val message = Message()
                    message.what = 0
                    mHandler.sendMessageDelayed(message, 200)
                }
            }
        }

        btnSubmit.isClickable = false
    }

    /**
     * 定时器任务类
     */
    inner class MyTimerTask : TimerTask() {
        override fun run() {
            mWipeDataCount--
            val timeDisplay = formatCountdownTime(mWipeDataCount)

            Logger.geo("$TAG ⏰ 擦除倒计时进行中: 剩余 $timeDisplay (${mWipeDataCount}分钟)")
            saveWipeCountdown(mWipeDataCount)

            // 在主线程中更新UI，增加安全检查
            mHandler.post {
                try {
                    // 检查Activity是否还存在且未被销毁
                    if (!isFinishing && !isDestroyed) {
                        // 倒计时UI现在完全由悬浮窗处理，无需更新Activity内的UI
                    } else {
                        Logger.geo("$TAG Activity已销毁，跳过UI更新")
                    }
                } catch (e: Exception) {
                    Logger.geoE("$TAG Timer UI更新失败", e)
                }
            }

            if (mWipeDataCount == 0) {
                // 设置重置标志
                Constants.bUnboxResetFromGeo = true
                GpsLocationManager.setCurrentGPSStatus(this@LockScreenActivity, GpsLocationManager.WIPE_DATA)
                // 在主线程中更新UI，增加安全检查
                mHandler.post {
                    try {
                        if (!isFinishing && !isDestroyed) {
                            showStartWipingUI()
                        } else {
                            Logger.geo("$TAG Activity已销毁，跳过擦除UI显示")
                        }
                    } catch (e: Exception) {
                        Logger.geoE("$TAG 显示擦除UI失败", e)
                    }
                }
                Logger.geo("$TAG sendBroadcast: UNBIND_LAUNCHER")
                BroadcastSender.sendBroadcast(this@LockScreenActivity, BroadcastActions.UNBIND_LAUNCHER)
                timer?.cancel()
            }
        }
    }

    /**
     * 格式化倒计时显示为时分秒格式
     */
    @SuppressLint("DefaultLocale")
    private fun formatCountdownTime(minutes: Int): String {
        return when {
            minutes <= 0 -> "00:00:00"
            minutes == 1 -> "00:01:00"
            minutes < 60 -> String.format("00:%02d:00", minutes)
            else -> {
                val hours = minutes / 60
                val mins = minutes % 60
                String.format("%02d:%02d:00", hours, mins)
            }
        }
    }

    /**
     * 启动定时调度
     */
    private fun startTimeSchedule() {
        val timeDisplay = formatCountdownTime(mWipeDataCount)

        Logger.geo("$TAG ⏰ 启动擦除倒计时: $timeDisplay (${mWipeDataCount}分钟)")
        Logger.geo("$TAG ⏰ 倒计时详情: geoWipeStatus=${GpsLocationManager.geoWipeStatus}, geoWipeMins=${GpsLocationManager.geoWipeMins}")
        Logger.geo("$TAG ⏰ Timer状态: timer=$timer, mTimerTask=$mTimerTask")
        Logger.geo("$TAG ⏰ 当前mWipedataCount值: $mWipeDataCount")

        // 显示数据擦除倒计时悬浮窗
        showWipeDataFloatWindow(mWipeDataCount * 60) // 转换为秒

        if (mTimerTask == null) {
            mTimerTask = MyTimerTask()
        } else {
            mTimerTask?.cancel()
            mTimerTask = null
            mTimerTask = MyTimerTask()
        }

        if (timer == null) {
            timer = Timer()
        } else {
            timer?.cancel()
            timer?.purge()
            timer = null
            timer = Timer()
        }

        timer?.schedule(mTimerTask, 60 * 1000, 60 * 1000)
        Logger.geo("$TAG ⏰ Timer已启动，首次执行延迟60秒，间隔60秒，倒计时: $timeDisplay")

        // 倒计时显示现在完全由悬浮窗处理
    }

    /**
     * 停止定时调度
     */
    private fun stopTimeSchedule() {
        Logger.geo("$TAG LockScreen timer cancelled")

        if (mTimerTask != null) {
            mTimerTask?.cancel()
            mTimerTask = null
        }

        if (timer != null) {
            timer?.cancel()
            timer?.purge()
            timer = null
        }

        // 关闭数据擦除倒计时悬浮窗
        closeWipeDataFloatWindow()
    }

    /**
     * 检查OTP计数器
     */
    private fun checkOTPCounter() {
        if (GpsLocationManager.LOCK_SCREEN == Constants.geofenceStatus) {
            if (otpCounter > 0) {
                otpCounter--
                saveOtpCounter()

                if (otpCounter <= 0) {
                    showPwdErrorWarning(false)
                    showStartWipingUI()
                    // 设置重置标志
                    Constants.bUnboxResetFromGeo = true
                    GpsLocationManager.setCurrentGPSStatus(this, GpsLocationManager.WIPE_DATA)
                    Logger.geo("$TAG sendBroadcast: UNBIND_LAUNCHER")
                    BroadcastSender.sendBroadcast(this, BroadcastActions.UNBIND_LAUNCHER)
                    timer?.cancel()
                } else {
                    showPwdErrorWarning(true)
                    Toast.makeText(this, " All data will be wiped out after $otpCounter times Error Input!", Toast.LENGTH_LONG).show()
                }
            }
        } else {
            showPwdErrorWarning(false)
        }
    }

    /**
     * 错误处理和震动清除
     */
    private fun errorAndShakeClear() {
        try {
            // 加载震动动画资源文件
            // val shake = AnimationUtils.loadAnimation(this, R.anim.shake)
            // passwordEditText.startAnimation(shake) // 给组件播放动画效果

            // 简单的震动效果替代
            passwordEditText.animate()
                .translationX(-10f)
                .setDuration(50)
                .withEndAction {
                    passwordEditText.animate()
                        .translationX(10f)
                        .setDuration(50)
                        .withEndAction {
                            passwordEditText.animate()
                                .translationX(0f)
                                .setDuration(50)
                        }
                }

            passwordEditText.setText("")

        } catch (e: Exception) {
            Logger.geoE("$TAG 错误处理失败", e)
        }
    }

    /**
     * 显示锁定设备对话框
     */
    private fun showLockDeviceDialog() {
        Logger.geo("$TAG LockScreenActivity showLockDeviceDialog")
        if (llLockdevice.visibility == View.GONE) {
            llLockdevice = findViewById(R.id.ll_lockdevice)
            llLockdevice.visibility = View.VISIBLE
            llLockdeviceInput = findViewById(R.id.ll_lockdevice_input)
            llLockdeviceInput.visibility = View.GONE
        }
    }

    /**
     * 显示锁定设备输入对话框
     */
    private fun showLockDeviceInputDialog() {
        Logger.geo("$TAG LockScreenActivity showLockDeviceInputDialog")
        if (llLockdeviceInput.visibility == View.GONE) {
            llLockdevice = findViewById(R.id.ll_lockdevice)
            llLockdevice.visibility = View.GONE
            llLockdeviceInput = findViewById(R.id.ll_lockdevice_input)
            llLockdeviceInput.visibility = View.VISIBLE
            passwordEditText.setText("")
        }
    }



    /**
     * 显示密码错误警告
     */
    private fun showPwdErrorWarning(show: Boolean) {
        if (show && !bDataWiped) {
            tvWarning.text = "WARNING: All data on the device will be wiped out after $otpCounter incorrect attempts."
            tvWarning.visibility = View.VISIBLE
        } else {
            tvWarning.text = "WARNING: All data on the device will be wiped out after $otpCounter incorrect attempts."
            tvWarning.visibility = View.GONE
        }
    }

    /**
     * 显示开始擦除UI
     */
    private fun showStartWipingUI() {
        try {
            // 检查UI组件是否已初始化
            if (!isUIInitialized) {
                Logger.geo("$TAG UI未初始化完成，跳过擦除UI显示")
                return
            }

            if (::passwordEditText.isInitialized) {
                passwordEditText.isEnabled = false
            }

            if (::tvWarning.isInitialized) {
                tvWarning.text = "Please wait .... Wiping Device\n"
                tvWarning.visibility = View.VISIBLE
            }

            // 擦除状态现在由悬浮窗显示，这里只需要显示警告信息
            Logger.geo("$TAG 开始数据擦除，悬浮窗将显示擦除状态")
        } catch (e: Exception) {
            Logger.geoE("$TAG 显示开始擦除UI失败", e)
        }
    }

    /**
     * 显示停止擦除UI
     */
    private fun showStopWipingUI() {
        passwordEditText.isEnabled = true
        tvWarning.text = "Please wait .... Wiping Device\n"
        tvWarning.visibility = View.GONE
    }

    /**
     * 注册广播接收器
     */
    private fun registerBroadcastReceiver() {
        try {
            if (!::mIntentFilter.isInitialized) {
                mIntentFilter = IntentFilter().apply {
                    addAction(BroadcastActions.ACTION_CLOSE_LOCKSCREEN)
                    addAction(BroadcastActions.ACTION_DATA_WIPED)
                    addAction(BroadcastActions.ACTION_ENTER_GEOFENCE)
                    addAction(BroadcastActions.ACTION_OUT_OF_GEOFENCE)
                    addAction(BroadcastActions.ACTION_GET_ONETIME_PWD)
                    addAction(BroadcastActions.ACTION_WIPE_PARAM_CHANGE)
                    addAction(BroadcastActions.ACTION_WEBSOCKET_CONNECTION)
                    addAction(Intent.ACTION_SCREEN_OFF)
                    addAction(Intent.ACTION_SCREEN_ON)
                    if (android.os.Build.VERSION.SDK_INT < android.os.Build.VERSION_CODES.N) {
                        addAction(ConnectivityManager.CONNECTIVITY_ACTION)
                    }
                }
            }

            // 注册广播接收器，包含权限参数
            registerReceiver(mReceiver, mIntentFilter, "com.dspread.mdm.service.permission.MY_BROADCAST", null)
            Logger.geo("$TAG 广播接收器注册成功")

        } catch (e: Exception) {
            Logger.geoE("$TAG 注册广播接收器失败", e)
        }
    }

    /**
     * 注销广播接收器
     */
    private fun unregisterBroadcastReceiver() {
        try {
            unregisterReceiver(mReceiver)
            Logger.geo("$TAG 广播接收器注销成功")
        } catch (e: Exception) {
            Logger.geoE("$TAG 注销广播接收器失败", e)
        }
    }

    /**
     * 检查屏幕是否开启
     */
    private fun isScreenOn(): Boolean {
        val pm = getSystemService(Context.POWER_SERVICE) as PowerManager
        val isScreenOn = pm.isInteractive
        Logger.geo("$TAG isScreenOn() = $isScreenOn")
        return isScreenOn
    }

    /**
     * 设置Launcher应用
     */
    private fun setLauncherApp() {
        try {
            Logger.geo("$TAG 设置Launcher应用")
            // 简化实现 - 可以根据需要扩展
        } catch (e: Exception) {
            Logger.geoE("$TAG 设置Launcher应用失败", e)
        }
    }

    /**
     * 获取OTP计数器
     */
    private fun getOtpCounter(): Int {
        return try {
            val prefs = getSharedPreferences("geofence_prefs", Context.MODE_PRIVATE)
            prefs.getInt("otp_counter", 5)
        } catch (e: Exception) {
            Logger.geoE("$TAG 获取OTP计数器失败", e)
            5
        }
    }

    /**
     * 保存OTP计数器
     */
    private fun saveOtpCounter() {
        try {
            val prefs = getSharedPreferences("geofence_prefs", Context.MODE_PRIVATE)
            prefs.edit().putInt("otp_counter", otpCounter).apply()
            Logger.geo("$TAG 保存OTP计数器: $otpCounter")
        } catch (e: Exception) {
            Logger.geoE("$TAG 保存OTP计数器失败", e)
        }
    }

    /**
     * 获取擦除倒计时
     */
    private fun getWipeCountdown(): Int {
        return try {
            val prefs = getSharedPreferences("geofence_prefs", Context.MODE_PRIVATE)
            // 使用合理的默认值：如果geoWipeMins为-1，则使用5作为默认值
            val defaultValue = if (GpsLocationManager.geoWipeMins > 0) {
                GpsLocationManager.geoWipeMins
            } else {
                5  
            }
            prefs.getInt("wipe_countdown", defaultValue)
        } catch (e: Exception) {
            Logger.geoE("$TAG 获取擦除倒计时失败", e)
            if (GpsLocationManager.geoWipeMins > 0) GpsLocationManager.geoWipeMins else 5
        }
    }

    /**
     * 保存擦除状态
     */
    private fun saveWipeStatus(status: Boolean) {
        try {
            val prefs = getSharedPreferences("geofence_prefs", Context.MODE_PRIVATE)
            prefs.edit().putBoolean("wipe_status", status).apply()
            Logger.geo("$TAG 保存擦除状态: $status")
        } catch (e: Exception) {
            Logger.geoE("$TAG 保存擦除状态失败", e)
        }
    }

    /**
     * 保存擦除时间
     */
    private fun saveWipeMins(mins: Int) {
        try {
            val prefs = getSharedPreferences("geofence_prefs", Context.MODE_PRIVATE)
            prefs.edit().putInt("wipe_mins", mins).apply()
            Logger.geo("$TAG 保存擦除时间: ${mins}分钟")
        } catch (e: Exception) {
            Logger.geoE("$TAG 保存擦除时间失败", e)
        }
    }

    /**
     * 获取设备序列号
     */
    private fun getDeviceSerialNumber(): String {
        return try {
            // 可以根据需要实现具体的序列号获取逻辑
            android.os.Build.SERIAL.takeIf { it.isNotEmpty() } ?: "Unknown"
        } catch (e: Exception) {
            Logger.geoE("$TAG 获取设备序列号失败", e)
            "Unknown"
        }
    }

    /**
     * 保存擦除倒计时
     */
    private fun saveWipeCountdown(countdown: Int) {
        try {
            val prefs = getSharedPreferences("geofence_prefs", Context.MODE_PRIVATE)
            prefs.edit().putInt("wipe_countdown", countdown).apply()
            Logger.geo("$TAG 保存擦除倒计时: $countdown")
        } catch (e: Exception) {
            Logger.geoE("$TAG 保存擦除倒计时失败", e)
        }
    }

    /**
     * 显示Toast消息
     */
    private fun showToast(message: String) {
        Toast.makeText(this, message, Toast.LENGTH_SHORT).show()
    }

    /**
     * 密码输入完成回调（PasswordEditText.PasswordFullListener接口）
     */
    @SuppressLint("UseCompatLoadingForDrawables")
    override fun passwordFull(password: String, bfull: Boolean) {
        if (bfull) {
            // 密码输入完成，启用提交按钮
            btnSubmit.setTextSize(TypedValue.COMPLEX_UNIT_SP, 25f)
            btnSubmit.text = "SUBMIT"
            btnSubmit.background = getDrawable(R.drawable.btn_bord_green)
            btnSubmit.isClickable = true
            btnSubmit.setTextColor(resources.getColor(R.color.white))
        } else {
            // 密码未完成，禁用提交按钮
            btnSubmit.background = getDrawable(R.drawable.btn_bord_gray)
            btnSubmit.isClickable = false
            btnSubmit.setTextColor(resources.getColor(R.color.slate_grey))
        }
    }

    /**
     * 隐藏无密码警告
     */
    private fun hideNoPwdWarning() {
        try {
            tvNoPwd.visibility = View.INVISIBLE
            btnSubmit.visibility = View.VISIBLE
            if (Constants.geofenceStatus != GpsLocationManager.WIPE_DATA) {
                tvWarning.visibility = View.VISIBLE
            }
            passwordEditText.visibility = View.VISIBLE
            tvTip.visibility = View.VISIBLE
            Logger.geo("$TAG 隐藏无密码警告")
        } catch (e: Exception) {
            Logger.geoE("$TAG 隐藏无密码警告失败", e)
        }
    }

    /**
     * 显示无密码警告
     */
    private fun showNoPwdWarning() {
        try {
            tvNoPwd.visibility = View.VISIBLE
            btnSubmit.visibility = View.INVISIBLE
            tvWarning.visibility = View.GONE
            passwordEditText.visibility = View.INVISIBLE
            tvTip.visibility = View.INVISIBLE
            Logger.geo("$TAG 显示无密码警告")
        } catch (e: Exception) {
            Logger.geoE("$TAG 显示无密码警告失败", e)
        }
    }

    /**
     * 显示数据擦除倒计时悬浮窗
     */
    private fun showWipeDataFloatWindow(timeoutSeconds: Int) {
        try {
            Logger.geo("$TAG 显示数据擦除倒计时悬浮窗: ${timeoutSeconds}秒")

            // 关闭之前的悬浮窗
            closeWipeDataFloatWindow()

            // 创建新的悬浮窗，注意：悬浮窗会自己管理倒计时
            // 这里传入的是总的倒计时秒数，悬浮窗会每秒更新显示
            wipeDataFloatWindow = RebootFloatWindow(this, RebootFloatWindow.WindowType.WIPE_DATA)
            wipeDataFloatWindow?.apply {
                createWindowManager()
                createDesktopLayout()
                showDesk(timeoutSeconds)
            }

            Logger.geo("$TAG 数据擦除倒计时悬浮窗已显示")

        } catch (e: Exception) {
            Logger.geoE("$TAG 显示数据擦除倒计时悬浮窗失败", e)
        }
    }

    /**
     * 关闭数据擦除倒计时悬浮窗
     */
    private fun closeWipeDataFloatWindow() {
        try {
            wipeDataFloatWindow?.closeDesk()
            wipeDataFloatWindow = null
            Logger.geo("$TAG 数据擦除倒计时悬浮窗已关闭")
        } catch (e: Exception) {
            Logger.geoE("$TAG 关闭数据擦除倒计时悬浮窗失败", e)
        }
    }

}
