package com.bbpos.wiseapp.websocket.nv;

import java.security.KeyStore;
import java.util.Arrays;

import javax.net.ssl.SSLContext;
import javax.net.ssl.SSLSocketFactory;
import javax.net.ssl.TrustManager;
import javax.net.ssl.TrustManagerFactory;
import javax.net.ssl.X509TrustManager;

public class WmSSlContent {
	private static X509TrustManager trustManager;
	private static SSLContext sslContext;

	public static SSLContext getSSLContext(){
		if (sslContext == null) {
			try {
				X509TrustManager tm = getTrustManager();
				sslContext = SSLContext.getInstance("TLSv1.2");
				sslContext.init(null, (tm != null) ? new X509TrustManager[]{tm} : null, null);
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
		return sslContext;
	}

	public static X509TrustManager getTrustManager(){
		if (trustManager == null) {
			try {
				TrustManagerFactory trustManagerFactory = TrustManagerFactory.getInstance(TrustManagerFactory.getDefaultAlgorithm());
				trustManagerFactory.init((KeyStore) null);

				TrustManager[] trustManagers = trustManagerFactory.getTrustManagers();
				if (trustManagers.length != 1 || !(trustManagers[0] instanceof X509TrustManager)) {
					throw new IllegalStateException("Unexpected default trust managers:" + Arrays.toString(trustManagers));
				}
				trustManager = (X509TrustManager) trustManagers[0];
				return trustManager;
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
		return trustManager;
	}


	public static SSLSocketFactory getSSLSocketFactory(){
		return getSSLContext().getSocketFactory();
	}
}
