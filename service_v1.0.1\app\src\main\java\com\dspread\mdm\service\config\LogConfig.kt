package com.dspread.mdm.service.config

import com.dspread.mdm.service.utils.log.Logger

/**
 * 日志配置中心
 */
object LogConfig {
    
    var globalEnabled = true

    // 每个模块一个开关，简单明了
    var commonEnabled = false
    var httpsEnabled = false
    var websocketEnabled = false
    var platformEnabled = false
    var serviceEnabled = false
    var provisioningEnabled = false
    var taskEnabled = false
    var ruleBaseEnabled = false
    var appMgrEnabled = false
    var logStreamEnabled = false
    var remoteViewEnabled = false
    var wifiProfileEnabled = false
    var apnProfileEnabled = false
    var geofenceEnabled = false
    var receiverEnabled = false

    fun setProductionMode() {
        commonEnabled = true
        httpsEnabled = true
        websocketEnabled = true
        platformEnabled = true
        serviceEnabled = true
        provisioningEnabled = true
        taskEnabled = true
        ruleBaseEnabled = true
        appMgrEnabled = true
        logStreamEnabled = false
        remoteViewEnabled = false
        wifiProfileEnabled = false
        apnProfileEnabled = false
        geofenceEnabled = false
        receiverEnabled = true

        Logger.com("日志配置：生产环境模式")
    }

    fun setDebugMode() {
        commonEnabled = true
        httpsEnabled = true
        websocketEnabled = true
        platformEnabled = true
        serviceEnabled = true
        provisioningEnabled = true
        taskEnabled = true
        ruleBaseEnabled = true
        appMgrEnabled = true
        logStreamEnabled = false
        remoteViewEnabled = false
        wifiProfileEnabled = false
        apnProfileEnabled = false
        geofenceEnabled = false
        receiverEnabled = true

        Logger.com("日志配置：测试环境模式")
    }

    /**
     * 获取当前配置状态
     */
    fun getCurrentStatus(): String {
        return """
        |========== 日志配置状态 ==========
        |全局开关: $globalEnabled
        |
        |模块开关:
        |  Common: $commonEnabled
        |  HTTPS: $httpsEnabled
        |  WebSocket: $websocketEnabled
        |  API: $platformEnabled
        |  Service: $serviceEnabled
        |  Provisioning: $provisioningEnabled
        |  Task: $taskEnabled
        |  RuleBase: $ruleBaseEnabled
        |  Application: $appMgrEnabled
        |  LogStream: $logStreamEnabled
        |  RemoteView: $remoteViewEnabled
        |  Wi-Fi: $wifiProfileEnabled
        |  APN: $apnProfileEnabled
        |  GeoFence: $geofenceEnabled
        |  Receiver: $receiverEnabled
        |================================
        """.trimMargin()
    }
}
