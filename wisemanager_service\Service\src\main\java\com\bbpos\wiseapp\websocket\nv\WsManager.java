package com.bbpos.wiseapp.websocket.nv;

import android.content.Context;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import android.os.Handler;
import android.os.Looper;
import android.os.Message;

import com.bbpos.wiseapp.logger.BBLog;
import com.bbpos.wiseapp.service.common.UsualData;
import com.bbpos.wiseapp.tms.utils.ContextUtil;
import com.bbpos.wiseapp.tms.utils.Helpers;
import com.bbpos.wiseapp.utils.LimitedQueue;
import com.bbpos.wiseapp.utils.SharedPreferencesUtils;
import com.bbpos.wiseapp.websocket.SocketListener;
import com.bbpos.wiseapp.websocket.TextResponse;
import com.neovisionaries.ws.client.WebSocket;
import com.neovisionaries.ws.client.WebSocketAdapter;
import com.neovisionaries.ws.client.WebSocketException;
import com.neovisionaries.ws.client.WebSocketFactory;
import com.neovisionaries.ws.client.WebSocketFrame;
import com.neovisionaries.ws.client.WebsocketStatus;

import java.util.List;
import java.util.Map;


public class WsManager {
    private static WsManager mInstance;
    private LimitedQueue<Long> disc_record = new LimitedQueue<Long>(10);
    private final String TAG = "WiseApp2.0";
    /**
     * WebSocket config
     */
    private static final int FRAME_QUEUE_SIZE = 5;
    private static final int CONNECT_TIMEOUT = 10000;
    private static final int WHAT_RECONNECT = 1;
//    private static final String DEF_URL = BuildConfig.DEBUG ? DEF_TEST_URL : DEF_RELEASE_URL;


    private WebSocket ws;
    private WebsocketStatus mStatus;
    private String url;
    private SocketListener mSocketListener;
    private WsListener mListener;
    private String proxy;//eg: http://192.168.34.121:8080
    private int conneccTimeout;
    private boolean mHandStop = false;
    private Context context;

//    private Handler mHandler = new Handler(Looper.getMainLooper());
    private volatile int reconnectCount = 0;//重连次数
    private long minInterval = 3000;//重连最小时间间隔
    private long maxInterval = 15000;//重连最大时间间隔

    private Builder builder;
    private Handler mHandler = new Handler(Looper.getMainLooper()){
        @Override
        public void handleMessage(Message msg) {
            super.handleMessage(msg);
            switch (msg.what){
                case WHAT_RECONNECT:
                    builder.Proxy(ContextUtil.getProxy());
                    reInit();
                    break;
            }
        }
    };

    private WsManager() {}


    public static WsManager getInstance() {
        if (mInstance == null) {
            synchronized (WsManager.class) {
                if (mInstance == null) {
                    mInstance = new WsManager();
                }
            }
        }
        return mInstance;
    }

    private WsManager build(Builder builder){
        this.builder = builder;
        this.context = builder.context;
        this.url = builder.serviceUrl;
        this.conneccTimeout = builder.connectTimeout;
        this.proxy = builder.proxy;
        this.mSocketListener = builder.mSocketListener;
        return this;
    }

    public void init(Builder builder){
        checkParamIsValid(builder);
        this.context = builder.context;
        try {
            ws = new WebSocketFactory()
                    .getProxySettings()
                    .setServer(builder.proxy)
                    .getWebSocketFactory()
                    .setConnectionTimeout(builder.connectTimeout)
                    .createSocket(builder.serviceUrl)
                    .setFrameQueueSize(FRAME_QUEUE_SIZE)//设置帧队列最大值为5
                    .setMissingCloseFrameAllowed(false)//设置不允许服务端关闭连接却未发送关闭帧
                    .addListener(mListener = new WsListener())
                    .connectAsynchronously();

            setStatus(WebsocketStatus.CONNECTING);
            build(builder);
            BBLog.d(TAG, "nv websocket 第一次连接 ");
        } catch (Exception e) {
            e.printStackTrace();
            setStatus(WebsocketStatus.CONNECT_FAIL);
        }
    }


    public static class Builder{
        private Context context;
        private String serviceUrl;
        private String serviceUrlForReconn;
        private SocketListener mSocketListener;
        private WsListener mListener;
        private String proxy;//eg: http://192.168.34.121:8080
        private int connectTimeout;

        public Builder Context(Context context){
            this.context = context;
            return this;
        }

        public Builder SrviceUrl(String serviceUrl){
            this.serviceUrl = serviceUrl;
            return this;
        }

        public Builder SrviceUrlForReconn(String serviceUrlForReconn){
            this.serviceUrlForReconn = serviceUrlForReconn;
            return this;
        }

        public Builder SocketListener(SocketListener mSocketListener){
            this.mSocketListener = mSocketListener;
            return this;
        }

//        public Builder WsListener(WsListener mListener){
//            this.mListener = mListener;
//            return this;
//        }

        public Builder Proxy(String proxy){
            this.proxy = proxy;
            return this;
        }

        public Builder ConneccTimeout(int conneccTimeout){
            if (conneccTimeout< 0)
                this.connectTimeout = CONNECT_TIMEOUT;
            this.connectTimeout = conneccTimeout;
            return this;
        }
    }

    public void sendMsg(String msg){
        try {
            ws.sendText(msg);
            BBLog.e(TAG, "sendMsg: 消息发送成功");
        } catch (Exception e) {
            e.printStackTrace();
            BBLog.e(TAG, "sendMsg: 消息发送失败");
        }
    }

    /**
     * 重新创建websocket实例
     * @param builder
     */
    public void reCreate(Builder builder){
        disconnect();
        init(builder);
    }

    private void checkParamIsValid(Builder builder) {
        if (builder == null)
            throw new IllegalArgumentException("builder can't be null, please check");

        if (builder.serviceUrl == null)
            throw new IllegalArgumentException("serviceUrl can't be null, please check");

        if (builder.context == null)
            throw new IllegalArgumentException("context can't be null, please check");
    }


    private void setStatus(WebsocketStatus status) {
        this.mStatus = status;
    }


    public WebsocketStatus getStatus() {
        return mStatus;
    }

    public void setSocketListener(SocketListener socketListener) {
        this.mSocketListener = socketListener;
    }

    public void disconnect() {
        if (ws != null) {
            ws.disconnect();
            ws = null;
            mHandStop = true;
        }
    }



    private void cancelReconnect() {
        reconnectCount = 0;
        mHandler.removeMessages(1);
//        mHandler.removeCallbacks(mReconnectTask);
    }


    public void reconnect() {
        if (mHandStop) {
            return;
        }
//        if (!isNetConnect()) {
//            reconnectCount = 0;
//            BBLog.e(TAG, "nv websocket 重连失败网络不可用 ");
//            return;
//        }
        BBLog.e(TAG, "ws = " + ws + "  isOpen=" + ws.isOpen() + "  getStatus=" + getStatus());
        if (ws != null && getStatus() != WebsocketStatus.CONNECTING) {//不是正在重连状态
            if (ws.isOpen()) { //当前连接断开了
                ws.disconnect();
            }
            reconnectCount++;
            setStatus(WebsocketStatus.CONNECTING);

            long reconnectTime = minInterval;
            if ("1".equals(SharedPreferencesUtils.getSharePreferencesValue(UsualData.SHARED_PREFERENCES_DELAY_SWITCH, "0"))) {
                long timePolicy = 10;
                try {
                    timePolicy = Long.valueOf(SharedPreferencesUtils.getSharePreferencesValue(UsualData.SHARED_PREFERENCES_DELAY_POLICY, "10"));
                } catch (Exception e) {
                    e.printStackTrace();
                    timePolicy = 10;
                }
                if (disc_record.size() == 10) {
                    long theFirstDisc = disc_record.getFirst();
                    long theLastDisc = disc_record.getLast();
                    BBLog.e(TAG, "断开记录 最早一次断开 = " + Helpers.getTransDateStr(theFirstDisc));
                    BBLog.e(TAG, "断开记录 最后一次断开 = " + Helpers.getTransDateStr(theLastDisc));
                    if ((theLastDisc - theFirstDisc) < timePolicy * 60 * 1000) {
                        try {
                            reconnectTime = Long.valueOf(SharedPreferencesUtils.getSharePreferencesValue(UsualData.SHARED_PREFERENCES_DELAY_TIME, "900000"));
                        } catch (Exception e) {
                            reconnectTime = 15 * 60 * 1000;
                        }
                        reconnectCount = 0;
                    } else {
                        if (reconnectCount > 3) {
                            long temp = minInterval * (reconnectCount - 2);
                            reconnectTime = temp > maxInterval ? maxInterval : temp;
                        }
                        if (reconnectCount >= Integer.MAX_VALUE) {
                            reconnectCount = 0;
                        }
                    }
                } else {
                    if (reconnectCount > 3) {
                        long temp = minInterval * (reconnectCount - 2);
                        reconnectTime = temp > maxInterval ? maxInterval : temp;
                    }
                    if (reconnectCount >= Integer.MAX_VALUE) {
                        reconnectCount = 0;
                    }
                }
            } else {
                if (reconnectCount > 3) {
                    long temp = minInterval * (reconnectCount - 2);
                    reconnectTime = temp > maxInterval ? maxInterval : temp;
                }
                if (reconnectCount >= Integer.MAX_VALUE) {
                    reconnectCount = 0;
                }
            }
            BBLog.d(TAG, String.format("开始第%d次重连,间隔%d",reconnectCount, reconnectTime));
//            mHandler.postDelayed(mReconnectTask, reconnectTime);
            mHandler.sendEmptyMessageDelayed(WHAT_RECONNECT,reconnectTime);
        }
    }

    private boolean isNetConnect() {
        ConnectivityManager connectivity = (ConnectivityManager) context.getSystemService(Context.CONNECTIVITY_SERVICE);
        if (connectivity != null) {
            NetworkInfo info = connectivity.getActiveNetworkInfo();
            if (info != null && info.isConnected()) {
                // 当前网络是连接的
                if (info.getState() == NetworkInfo.State.CONNECTED) {
                    // 当前所连接的网络可用
                    return true;
                }
            }
        }
        return false;
    }

   private void reInit(){
       try {
           ws = new WebSocketFactory()
                   .getProxySettings()
                   .setServer(builder.proxy)
                   .getWebSocketFactory()
                   .setConnectionTimeout(builder.connectTimeout)
                   .createSocket(builder.serviceUrlForReconn)
                   .setFrameQueueSize(FRAME_QUEUE_SIZE)//设置帧队列最大值为5
                   .setMissingCloseFrameAllowed(false)//设置不允许服务端关闭连接却未发送关闭帧
                   .addListener(mListener = new WsListener())
                   .connectAsynchronously();
       } catch (Exception e) {
           e.printStackTrace();
       }
   }

    private Runnable mReconnectTask = new Runnable() {

        @Override
        public void run() {
            try {
                ws = new WebSocketFactory()
                        .getProxySettings()
                        .setServer(builder.proxy)
                        .getWebSocketFactory()
                        .createSocket(builder.serviceUrlForReconn)
                        .setFrameQueueSize(FRAME_QUEUE_SIZE)//设置帧队列最大值为5
                        .setMissingCloseFrameAllowed(false)//设置不允许服务端关闭连接却未发送关闭帧
                        .addListener(mListener = new WsListener())
                        .connectAsynchronously();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    };


    /**
     * 继承默认的监听空实现WebSocketAdapter,重写我们需要的方法
     * onTextMessage 收到文字信息
     * onConnected 连接成功
     * onConnectError 连接失败
     * onDisconnected 连接关闭
     */
    class WsListener extends WebSocketAdapter {
        @Override
        public void onTextMessage(WebSocket websocket, String text) throws Exception {
            super.onTextMessage(websocket, text);
//            BBLog.e(TAG, "onTextMessage: 收到报文： "+text);
            if (mSocketListener!=null){
                mSocketListener.onMessageResponse(new TextResponse(text));
            }
        }


        @Override
        public void onConnected(WebSocket websocket, Map<String, List<String>> headers)
                throws Exception {
            super.onConnected(websocket, headers);
            BBLog.e(TAG, "onConnected: 连接成功");
            if (mSocketListener!=null){
                mSocketListener.onConnected();
            }
            setStatus(WebsocketStatus.CONNECT_SUCCESS);
            cancelReconnect();//连接成功的时候取消重连,初始化连接次数
        }


        @Override
        public void onConnectError(WebSocket websocket, WebSocketException exception)
                throws Exception {
            super.onConnectError(websocket, exception);
            BBLog.e(TAG, "onConnected: 连接错误: "+exception.getMessage());
            if (mSocketListener!=null)
                mSocketListener.onConnectError(exception);
            setStatus(WebsocketStatus.CONNECT_FAIL);
            reconnect();//连接错误的时候调用重连方法
        }


        @Override
        public void onDisconnected(WebSocket websocket, WebSocketFrame serverCloseFrame, WebSocketFrame clientCloseFrame, boolean closedByServer)
                throws Exception {
            super.onDisconnected(websocket, serverCloseFrame, clientCloseFrame, closedByServer);
            BBLog.e(TAG, "onDisconnected: 断开连接: ");
            disc_record.add(System.currentTimeMillis());
            if (mSocketListener!=null)
                mSocketListener.onDisconnected();
            setStatus(WebsocketStatus.CONNECT_FAIL);
            reconnect();//连接断开的时候调用重连方法
        }
    }


}
