package com.dspread.mdm.service.platform.api.device

import android.content.Context
import android.content.pm.PackageInfo
import android.content.pm.PackageManager
import android.os.Build
import com.dspread.mdm.service.utils.log.Logger

/**
 * 包管理API
 * 提供应用包信息查询、版本检查等功能
 */
class PackageApi(private val context: Context) {
    
    companion object {
        private const val TAG = "PackageApi"
    }
    
    private val packageManager by lazy { context.packageManager }

    /**
     * 检查应用是否已安装
     */
    fun isAppInstalled(packageName: String): Boolean {
        return try {
            packageManager.getPackageInfo(packageName, PackageManager.GET_ACTIVITIES)
            true
        } catch (e: PackageManager.NameNotFoundException) {
            false
        } catch (e: Exception) {
            Logger.platformE("检查应用安装状态失败: $packageName", e)
            false
        }
    }

    /**
     * 获取指定包名应用的版本信息
     */
    fun getAppVersion(packageName: String): String? {
        return try {
            val packageInfo: PackageInfo = packageManager.getPackageInfo(packageName, 0)
            packageInfo.versionName
        } catch (e: PackageManager.NameNotFoundException) {
            null
        } catch (e: Exception) {
            Logger.platformE("获取应用版本失败: $packageName", e)
            null
        }
    }

    /**
     * 获取应用的版本码
     */
    fun getAppVersionCode(packageName: String): Long {
        return try {
            val packageInfo: PackageInfo = packageManager.getPackageInfo(packageName, 0)
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
                packageInfo.longVersionCode
            } else {
                @Suppress("DEPRECATION")
                packageInfo.versionCode.toLong()
            }
        } catch (e: PackageManager.NameNotFoundException) {
            -1L
        } catch (e: Exception) {
            Logger.platformE("获取应用版本码失败: $packageName", e)
            -1L
        }
    }

    /**
     * 获取应用的包信息
     */
    fun getPackageInfo(packageName: String): PackageInfo? {
        return try {
            packageManager.getPackageInfo(packageName, PackageManager.GET_META_DATA)
        } catch (e: PackageManager.NameNotFoundException) {
            null
        } catch (e: Exception) {
            Logger.platformE("获取包信息失败: $packageName", e)
            null
        }
    }

    /**
     * 获取应用的安装时间
     */
    fun getAppInstallTime(packageName: String): Long {
        return try {
            val packageInfo = packageManager.getPackageInfo(packageName, 0)
            packageInfo.firstInstallTime
        } catch (e: Exception) {
            Logger.platformE("获取应用安装时间失败: $packageName", e)
            0L
        }
    }

    /**
     * 获取应用的更新时间
     */
    fun getAppUpdateTime(packageName: String): Long {
        return try {
            val packageInfo = packageManager.getPackageInfo(packageName, 0)
            packageInfo.lastUpdateTime
        } catch (e: Exception) {
            Logger.platformE("获取应用更新时间失败: $packageName", e)
            0L
        }
    }

    /**
     * 检查应用是否为系统应用
     */
    fun isSystemApp(packageName: String): Boolean {
        return try {
            val packageInfo = packageManager.getPackageInfo(packageName, 0)
            val applicationInfo = packageInfo.applicationInfo
            (applicationInfo.flags and android.content.pm.ApplicationInfo.FLAG_SYSTEM) != 0
        } catch (e: Exception) {
            Logger.platformE("检查系统应用状态失败: $packageName", e)
            false
        }
    }

    /**
     * 获取应用的目标SDK版本
     */
    fun getTargetSdkVersion(packageName: String): Int {
        return try {
            val packageInfo = packageManager.getPackageInfo(packageName, 0)
            packageInfo.applicationInfo.targetSdkVersion
        } catch (e: Exception) {
            Logger.platformE("获取目标SDK版本失败: $packageName", e)
            0
        }
    }

    /**
     * 获取应用的最小SDK版本
     */
    fun getMinSdkVersion(packageName: String): Int {
        return try {
            val packageInfo = packageManager.getPackageInfo(packageName, 0)
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                packageInfo.applicationInfo.minSdkVersion
            } else {
                0
            }
        } catch (e: Exception) {
            Logger.platformE("获取最小SDK版本失败: $packageName", e)
            0
        }
    }

    /**
     * 获取所有已安装的应用包名列表
     */
    fun getInstalledPackages(): List<String> {
        return try {
            val packages = packageManager.getInstalledPackages(0)
            packages.map { it.packageName }
        } catch (e: Exception) {
            Logger.platformE("获取已安装应用列表失败", e)
            emptyList()
        }
    }

    /**
     * 获取应用的详细信息
     */
    fun getAppDetails(packageName: String): Map<String, Any?> {
        return try {
            mapOf(
                "packageName" to packageName,
                "isInstalled" to isAppInstalled(packageName),
                "version" to getAppVersion(packageName),
                "versionCode" to getAppVersionCode(packageName),
                "installTime" to getAppInstallTime(packageName),
                "updateTime" to getAppUpdateTime(packageName),
                "isSystemApp" to isSystemApp(packageName),
                "targetSdk" to getTargetSdkVersion(packageName),
                "minSdk" to getMinSdkVersion(packageName)
            )
        } catch (e: Exception) {
            Logger.platformE("获取应用详细信息失败: $packageName", e)
            mapOf(
                "packageName" to packageName,
                "error" to e.message
            )
        }
    }
}
