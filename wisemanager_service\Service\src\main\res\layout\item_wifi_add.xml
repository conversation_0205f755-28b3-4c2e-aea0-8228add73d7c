<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/ll_background"
    android:layout_width="match_parent"
    android:layout_height="72dp">

    <LinearLayout
        android:layout_width="72dp"
        android:layout_height="match_parent"
        android:gravity="center">
        <ImageView
            android:id="@+id/iv_signal"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:background="@drawable/add_selector"/>
    </LinearLayout>
    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="1"
        android:gravity="center_vertical"
        android:orientation="vertical">
        <TextView
            android:id="@+id/tv_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:text="@string/add_wifi"
            android:textSize="16sp"
            android:textColor="@color/title" />
    </LinearLayout>
</LinearLayout>
