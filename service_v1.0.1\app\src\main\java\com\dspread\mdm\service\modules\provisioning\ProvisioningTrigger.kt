package com.dspread.mdm.service.modules.provisioning

import android.app.AlarmManager
import android.app.PendingIntent
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.net.ConnectivityManager
import android.os.Build
import com.dspread.mdm.service.constants.Constants
import com.dspread.mdm.service.platform.api.network.NetworkApi
import com.dspread.mdm.service.modules.provisioning.model.ProvisioningTrigger as ProvisioningTriggerType
import com.dspread.mdm.service.utils.log.Logger
import com.dspread.mdm.service.utils.storage.PreferencesManager

/**
 * Provisioning触发器
 * 负责管理各种触发Provisioning的场景
 */
class ProvisioningTrigger private constructor(private val context: Context) {
    
    companion object {
        @Volatile
        private var INSTANCE: ProvisioningTrigger? = null
        
        fun getInstance(context: Context): ProvisioningTrigger {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: ProvisioningTrigger(context.applicationContext).also { INSTANCE = it }
            }
        }
        
        private const val TAG = "ProvisioningTrigger"
        private const val ACTION_PROVISIONING_TIMER = "com.dspread.mdm.service.PROVISIONING_TIMER"
        private const val PREFS_KEY_CONFIG_URL = "provisioning_config_url"
        private const val PREFS_KEY_POLLING_INTERVAL = "provisioning_polling_interval"
        
        // 默认配置URL（从ProvisionConfig获取）
        private val DEFAULT_CONFIG_URL = com.dspread.mdm.service.config.ProvisionConfig.DEFAULT_CONFIG_URL
    }
    
    // PreferencesManager是object，直接使用
    private val provisioningManager = ProvisioningManager.getInstance(context)
    private val alarmManager = context.getSystemService(Context.ALARM_SERVICE) as AlarmManager
    
    private var isRegistered = false
    private var isInitialStartup = true  // 标记是否为初始启动
    
    /**
     * 广播接收器 - 处理各种触发事件
     */
    private val broadcastReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context, intent: Intent) {
            when (intent.action) {
                ACTION_PROVISIONING_TIMER -> {
                    Logger.prov("定时器触发Provisioning（12小时间隔）")
                    handleTimerTrigger()
                }

                ConnectivityManager.CONNECTIVITY_ACTION -> {
                    Logger.prov("网络状态变化")
                    // 忽略初始启动时的网络连接事件
                    if (!isInitialStartup) {
                        handleNetworkChange()
                    } else {
                        Logger.prov("忽略初始启动时的网络连接事件")
                    }
                }

                Intent.ACTION_BOOT_COMPLETED -> {
                    Logger.prov("系统启动完成")
                    handleBootCompleted()
                }
            }
        }
    }
    
    /**
     * 初始化触发器
     */
    fun initialize() {
        if (!isRegistered) {
            registerBroadcastReceiver()
            setupTimer()
            isRegistered = true
            Logger.provI("Provisioning触发器初始化完成")
        }
    }
    
    /**
     * 注册广播接收器
     */
    private fun registerBroadcastReceiver() {
        val filter = IntentFilter().apply {
            addAction(ACTION_PROVISIONING_TIMER)
            addAction(ConnectivityManager.CONNECTIVITY_ACTION)
            addAction(Intent.ACTION_BOOT_COMPLETED)
        }
        
        context.registerReceiver(broadcastReceiver, filter)
        Logger.prov("广播接收器注册完成")
    }
    
    /**
     * 设置定时器
     */
    fun setupTimer() {
        val interval = getPollingInterval()
        val intent = Intent(ACTION_PROVISIONING_TIMER)
        val pendingIntent = PendingIntent.getBroadcast(
            context, 
            0, 
            intent, 
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )
        
        // 取消之前的定时器
        alarmManager.cancel(pendingIntent)
        
        // 设置新的定时器
        val triggerTime = System.currentTimeMillis() + (interval * 1000)
        
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            alarmManager.setExactAndAllowWhileIdle(
                AlarmManager.RTC_WAKEUP,
                triggerTime,
                pendingIntent
            )
        } else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
            alarmManager.setExact(
                AlarmManager.RTC_WAKEUP,
                triggerTime,
                pendingIntent
            )
        } else {
            alarmManager.set(
                AlarmManager.RTC_WAKEUP,
                triggerTime,
                pendingIntent
            )
        }

    }
    
    /**
     * 处理定时器触发
     */
    private fun handleTimerTrigger() {
        val configUrl = getConfigUrl()
        
        provisioningManager.executeProvisioning(
            configUrl = configUrl,
            trigger = ProvisioningTriggerType.TIMER,
            callback = object : ProvisioningManager.ProvisioningCallback {
                override fun onProgress(result: com.dspread.mdm.service.modules.provisioning.model.ProvisioningResult) {
                    Logger.prov("定时器触发进度: ${result.message}")
                }

                override fun onCompleted(result: com.dspread.mdm.service.modules.provisioning.model.ProvisioningResult) {
                    Logger.provI("定时器触发完成: ${result.message}")
                    // 设置下次定时器
                    setupTimer()
                }

                override fun onError(result: com.dspread.mdm.service.modules.provisioning.model.ProvisioningResult) {
                    Logger.provE("定时器触发失败: ${result.message}")
                    // 即使失败也要设置下次定时器
                    setupTimer()
                }
            }
        )
    }
    
    /**
     * 处理网络状态变化
     */
    private fun handleNetworkChange() {
        if (!NetworkApi.isNetworkAvailable(context)) {
            Logger.prov("网络不可用，跳过处理")
            return
        }

        val flags = provisioningManager.getFlags()

        // 只有在首次配置未完成时，才在网络重连时重试Provisioning
        if (!flags.isFirstProvisioningCompleted) {
            Logger.provI("检测到网络重连且Provisioning未成功，开始重试配置")

            val configUrl = getConfigUrl()
            val trigger = ProvisioningTriggerType.NETWORK_CONNECTED

            provisioningManager.executeProvisioning(
                configUrl = configUrl,
                trigger = trigger,
                callback = object : ProvisioningManager.ProvisioningCallback {
                    override fun onProgress(result: com.dspread.mdm.service.modules.provisioning.model.ProvisioningResult) {
                        Logger.prov("网络重连重试进度: ${result.message}")
                    }

                    override fun onCompleted(result: com.dspread.mdm.service.modules.provisioning.model.ProvisioningResult) {
                        Logger.provI("网络重连重试完成: ${result.message}")
                    }

                    override fun onError(result: com.dspread.mdm.service.modules.provisioning.model.ProvisioningResult) {
                        Logger.provE("网络重连重试失败: ${result.message}")
                    }
                }
            )
        } else {
            Logger.prov("Provisioning已完成，网络重连时不重新请求配置")
        }
    }
    
    /**
     * 处理系统启动完成
     */
    private fun handleBootCompleted() {
        // 系统启动后，检查是否需要执行首次配置
        val flags = provisioningManager.getFlags()
        
        if (!flags.isFirstProvisioningCompleted && NetworkApi.isNetworkAvailable(context)) {
            val configUrl = getConfigUrl()
            
            provisioningManager.executeProvisioning(
                configUrl = configUrl,
                trigger = ProvisioningTriggerType.FIRST_BOOT,
                callback = object : ProvisioningManager.ProvisioningCallback {
                    override fun onProgress(result: com.dspread.mdm.service.modules.provisioning.model.ProvisioningResult) {
                        Logger.prov("首次启动进度: ${result.message}")
                    }
                    
                    override fun onCompleted(result: com.dspread.mdm.service.modules.provisioning.model.ProvisioningResult) {
                        Logger.provI("首次启动完成: ${result.message}")
                    }
                    
                    override fun onError(result: com.dspread.mdm.service.modules.provisioning.model.ProvisioningResult) {
                        Logger.provE("首次启动失败: ${result.message}")
                    }
                }
            )
        }
    }
    
    /**
     * 手动触发Provisioning
     */
    fun triggerManually() {
        val configUrl = getConfigUrl()
        
        provisioningManager.executeProvisioning(
            configUrl = configUrl,
            trigger = ProvisioningTriggerType.MANUAL,
            callback = object : ProvisioningManager.ProvisioningCallback {
                override fun onProgress(result: com.dspread.mdm.service.modules.provisioning.model.ProvisioningResult) {
                    Logger.prov("手动触发进度: ${result.message}")
                }
                
                override fun onCompleted(result: com.dspread.mdm.service.modules.provisioning.model.ProvisioningResult) {
                    Logger.provI("手动触发完成: ${result.message}")
                }
                
                override fun onError(result: com.dspread.mdm.service.modules.provisioning.model.ProvisioningResult) {
                    Logger.provE("手动触发失败: ${result.message}")
                }
            }
        )
    }
    
    /**
     * 获取配置URL
     */
    fun getConfigUrl(): String {
        return PreferencesManager.getString(PREFS_KEY_CONFIG_URL, DEFAULT_CONFIG_URL)
    }

    /**
     * 获取轮询间隔（秒）
     */
    fun getPollingInterval(): Long {
        return PreferencesManager.getLong(
            PREFS_KEY_POLLING_INTERVAL,
            Constants.ModuleConstants.PROVISIONING_DEFAULT_INTERVAL
        )
    }

    /**
     * 设置配置URL
     */
    fun setConfigUrl(url: String) {
        PreferencesManager.putString(PREFS_KEY_CONFIG_URL, url)
    }

    /**
     * 设置轮询间隔
     */
    fun setPollingInterval(intervalSeconds: Long) {
        PreferencesManager.putLong(PREFS_KEY_POLLING_INTERVAL, intervalSeconds)
        // 重新设置定时器
        setupTimer()
    }
    
    /**
     * 标记初始启动完成
     * 在首次Provisioning完成后调用，之后网络重连才会触发重试
     */
    fun markInitialStartupCompleted() {
        isInitialStartup = false
        Logger.provI("初始启动标记完成，网络重连重试功能已激活")
    }

    /**
     * 销毁触发器
     */
    fun destroy() {
        if (isRegistered) {
            try {
                context.unregisterReceiver(broadcastReceiver)
            } catch (e: Exception) {
                Logger.provW("$TAG 注销广播接收器失败: ${e.message}")
            }
            isRegistered = false
            Logger.provI("Provisioning触发器已销毁")
        }
    }
}
