package com.bbpos.wiseapp.service.appdata;

import java.lang.reflect.Field;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.bbpos.wiseapp.logger.BBLog;
import com.bbpos.wiseapp.service.appdata.db.DataCollectDBHelper;
import com.bbpos.wiseapp.system.api.Helper;
import android.annotation.SuppressLint;
import android.app.IntentService;
import android.app.usage.UsageStats;
import android.app.usage.UsageStatsManager;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.util.Log;

/**
 * 数据收集服务
 * 触发时机 -间隔定时进行 -网络类型改变事件触发 -0点触发(需要特殊处理) -关机事件触发
 * */
@SuppressLint("SimpleDateFormat")
public class DataCollectService extends IntentService{
	private static final String TAG = DataCollectService.class.getName();
	public static final String EXE_TYPE = "exeType";
	/**标识每小时58分触发执行，需要特殊处理*/
	public static final String EXE_TYPE_CLEAR = "clear";
	public static final String EXE_TYPE_NORMAL = "normal";
	
	private DataCollectDBHelper dbHelper;
//	/**触发类型*/
//	private String exeType;

	public DataCollectService() {
		super(DataCollectService.class.getSimpleName());
		dbHelper = DataCollectDBHelper.getInstance(this);
	}

	@SuppressLint("NewApi")
	@Override
	protected void onHandleIntent(Intent intent) {
		usageStatCollect();   //使用情况统计
	}
	
	@SuppressLint({ "NewApi", "InlinedApi", "SimpleDateFormat" })
	private void usageStatCollect(){
		try {
			BBLog.v(BBLog.TAG, "usageStatCollect start");
			//使用次数与使用时长统计
			Date endDate = new Date();
			SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
	
			Date beginDate = sdf.parse(sdf.format(endDate));//从当天0点开始统计
			Date lastHour = getLastHour();
			if (lastHour.getTime()>beginDate.getTime()) {
				beginDate = lastHour;
			}
			UsageStatsManager usm = (UsageStatsManager)getSystemService(Context.USAGE_STATS_SERVICE);
			List<UsageStats> queryUsageStats = usm.queryUsageStats(UsageStatsManager.INTERVAL_DAILY, beginDate.getTime(), endDate.getTime());

			Map<String,PackageInfo> pkgMaps = getPackageMap();

			Field pkgFiled = Helper.getField(UsageStats.class, "mPackageName");
			Field launchCountFiled = Helper.getField(UsageStats.class, "mLaunchCount");
			Field totalTimeInForegroundFiled = Helper.getField(UsageStats.class, "mTotalTimeInForeground");
			
			for (UsageStats usageStat : queryUsageStats){
				BBLog.v(BBLog.TAG, "usageStat: "+usageStat);
				String pkgName = (String) pkgFiled.get(usageStat);
				int launchCount = launchCountFiled.getInt(usageStat);
				long mTotalTimeInForeground = totalTimeInForegroundFiled.getLong(usageStat)/1000;
				
				PackageInfo pkgInfo = pkgMaps.get(pkgName);
				int versionCode = pkgInfo.versionCode;
				String versionName = pkgInfo.versionName;
				dbHelper.updateUsageStat(sdf.format(new Date()),pkgName,versionCode,versionName,
						launchCount,mTotalTimeInForeground);
			}
			BBLog.v(BBLog.TAG, "usageStatCollect end");
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	private Map<String,PackageInfo> getPackageMap(){
		PackageManager pm = DataCollectService.this.getPackageManager();
		List<PackageInfo> allPackageInfos = pm.getInstalledPackages(0);
		Map<String,PackageInfo> pkgsMap = new HashMap<String,PackageInfo>();
		for (PackageInfo packageInfo : allPackageInfos) {
			pkgsMap.put(packageInfo.packageName, packageInfo);
		}
		return pkgsMap;
	}

	//获取前一小时的时间
	private Date getLastHour() {
		Date date = new Date();
		Calendar cal = Calendar.getInstance();
		cal.setTime(date);//date 换成已经已知的Date对象
		cal.add(Calendar.HOUR_OF_DAY, -1);// before 8 hour
		SimpleDateFormat format = new SimpleDateFormat("yyyyMMdd HH:mm:ss");	
		String lasthour = format.format(cal.getTime());
		
	    try {
	    	date = 	format.parse(lasthour);
		} catch (ParseException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
	    BBLog.e("lasthour:"+lasthour, "date:"+date);
	    return date;
	}
}
