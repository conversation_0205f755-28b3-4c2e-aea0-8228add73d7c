package com.dspread.mdm.service.utils.log

import android.content.Context
import java.io.File

/**
 * 日志配置管理
 */
object LoggerConfig {
    // 日志目录
    private const val LOG_DIR = "logs"
    
    // 日志文件最大数量
    private const val MAX_LOG_FILES = 20
    
    // 日志级别
    private var logLevel = LogLevel.DEBUG
    
    // 是否输出到控制台
    private var consoleOutput = true
    
    // 是否输出到文件
    private var fileOutput = true
    
    // 日志写入器列表
    private val logWriters = mutableListOf<LogWriter>()
    
    // 日志目录
    private lateinit var logDirectory: File
    
    /**
     * 初始化日志配置
     */
    fun init(context: Context, level: Int = LogLevel.DEBUG, console: Boolean = true, file: Boolean = true) {
        // 设置日志级别
        logLevel = level
        
        // 设置输出选项
        consoleOutput = console
        fileOutput = file
        
        // 清除现有写入器
        logWriters.forEach { it.close() }
        logWriters.clear()
        
        // 创建日志目录
        logDirectory = File(context.filesDir, LOG_DIR)
        if (!logDirectory.exists()) {
            logDirectory.mkdirs()
        }
        
        // 添加控制台写入器
        if (consoleOutput) {
            logWriters.add(ConsoleLogWriter())
        }
        
        // 添加文件写入器
        if (fileOutput) {
            logWriters.add(FileLogWriter(logDirectory))
        }
        
        // 清理旧日志文件
        cleanupOldLogFiles()
    }
    
    /**
     * 获取当前日志级别
     */
    fun getLogLevel(): Int {
        return logLevel
    }
    
    /**
     * 设置日志级别
     */
    fun setLogLevel(level: Int) {
        logLevel = level
    }
    
    /**
     * 是否启用控制台输出
     */
    fun isConsoleOutputEnabled(): Boolean {
        return consoleOutput
    }
    
    /**
     * 设置控制台输出
     */
    fun setConsoleOutput(enabled: Boolean) {
        if (consoleOutput != enabled) {
            consoleOutput = enabled
            
            if (enabled) {
                // 添加控制台写入器
                if (logWriters.none { it is ConsoleLogWriter }) {
                    logWriters.add(ConsoleLogWriter())
                }
            } else {
                // 移除控制台写入器
                logWriters.removeAll { it is ConsoleLogWriter }
            }
        }
    }
    
    /**
     * 是否启用文件输出
     */
    fun isFileOutputEnabled(): Boolean {
        return fileOutput
    }
    
    /**
     * 设置文件输出
     */
    fun setFileOutput(enabled: Boolean) {
        if (fileOutput != enabled) {
            fileOutput = enabled
            
            if (enabled) {
                // 添加文件写入器
                if (logWriters.none { it is FileLogWriter }) {
                    logWriters.add(FileLogWriter(logDirectory))
                }
            } else {
                // 移除文件写入器
                logWriters.removeAll { it is FileLogWriter }
            }
        }
    }
    
    /**
     * 获取日志写入器列表
     */
    fun getLogWriters(): List<LogWriter> {
        return logWriters
    }
    
    /**
     * 刷新日志缓存
     */
    fun flushLogs() {
        logWriters.forEach { it.flush() }
    }
    
    /**
     * 清理旧日志文件
     */
    fun cleanupOldLogFiles() {
        try {
            val fileLogWriter = logWriters.find { it is FileLogWriter } as? FileLogWriter
            val logFiles = fileLogWriter?.getLogFiles() ?: return
            
            if (logFiles.size > MAX_LOG_FILES) {
                // 按修改时间排序，保留最新的文件
                val filesToDelete = logFiles.sortedByDescending { it.lastModified() }
                    .drop(MAX_LOG_FILES)
                
                // 删除多余的文件
                filesToDelete.forEach { it.delete() }
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }
    
    /**
     * 获取日志目录
     */
    fun getLogDirectory(): File {
        return logDirectory
    }
}
