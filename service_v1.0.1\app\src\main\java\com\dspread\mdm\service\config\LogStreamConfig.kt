package com.dspread.mdm.service.config

import android.content.Context
import android.content.SharedPreferences

/**
 * 日志流配置管理
 * 从DebugConfig中剥离的logstreaming相关功能
 */
object LogStreamConfig {
    
    private const val PREF_NAME = "logstream_config"
    
    // SharedPreferences keys
    private const val KEY_ENABLED = "logstream_enabled"
    private const val KEY_LOG_FILE_SIZE = "log_file_size"
    private const val KEY_COMPRESSED_STORAGE_LIMIT = "compressed_storage_limit"
    private const val KEY_RAW_STORAGE_LIMIT = "raw_storage_limit"
    private const val KEY_RECENT_LOG_SIZE = "recent_log_size"
    private const val KEY_UPLOAD_URL = "upload_url"
    private const val KEY_ACCESS_KEY = "access_key"
    private const val KEY_SECRET_KEY = "secret_key"
    
    // 生产模式默认配置
    private const val PRODUCTION_LOG_FILE_SIZE = 5 * 1024 * 1024L // 5MB
    private const val PRODUCTION_COMPRESSED_STORAGE = 250 * 1024 * 1024L // 250MB
    private const val PRODUCTION_RAW_STORAGE = 250 * 1024 * 1024L // 250MB
    private const val PRODUCTION_RECENT_SIZE = 128 * 1024L // 128KB

    // 调试模式默认配置
    private const val DEBUG_LOG_FILE_SIZE = 100 * 1024L // 100KB
    private const val DEBUG_COMPRESSED_STORAGE = 10 * 1024 * 1024L // 10MB
    private const val DEBUG_RAW_STORAGE = 10 * 1024 * 1024L // 10MB
    private const val DEBUG_RECENT_SIZE = 1 * 1024L // 1KB
    
    // AWS S3配置
//    private const val DEFAULT_UPLOAD_URL = "https://siot-log01.s3.ap-northeast-1.amazonaws.com/log/"
//    private const val DEFAULT_ACCESS_KEY = "********************"
//    private const val DEFAULT_SECRET_KEY = "6gZ0Naru/H3q4LjsBJApTnTCJDbmi6TPP2+pieVV"

    private const val DEFAULT_UPLOAD_URL = "https://smartms-applog.s3.sa-east-1.amazonaws.com/log/"
    private const val DEFAULT_ACCESS_KEY = "********************"
    private const val DEFAULT_SECRET_KEY = "KU4eHKtJpgrRLoU3blf3N+E/mv0Ym6ViGO6WTubA"
    
    private var sharedPreferences: SharedPreferences? = null
    private var isDebugMode = false
    
    /**
     * 初始化LogStream配置
     */
    fun init(context: Context, debugMode: Boolean = false) {
        sharedPreferences = context.getSharedPreferences(PREF_NAME, Context.MODE_PRIVATE)
        isDebugMode = debugMode
        
        // 初始化默认值
        initializeDefaults()
    }
    
    /**
     * 设置调试模式
     */
    fun setDebugMode(enabled: Boolean) {
        isDebugMode = enabled
        // 根据模式更新配置
        updateConfigForMode()
    }
    
    /**
     * 是否启用LogStream
     * 默认关闭，只有在接收到平台命令时才启用
     */
    fun isEnabled(): Boolean {
        return sharedPreferences?.getBoolean(KEY_ENABLED, false) ?: false
    }
    
    /**
     * 设置LogStream启用状态
     */
    fun setEnabled(enabled: Boolean) {
        sharedPreferences?.edit()?.putBoolean(KEY_ENABLED, enabled)?.apply()
    }
    
    /**
     * 获取单个日志文件大小限制
     */
    fun getLogFileSize(): Long {
        val defaultSize = if (isDebugMode) DEBUG_LOG_FILE_SIZE else PRODUCTION_LOG_FILE_SIZE
        return sharedPreferences?.getLong(KEY_LOG_FILE_SIZE, defaultSize) ?: defaultSize
    }
    
    /**
     * 设置单个日志文件大小限制
     */
    fun setLogFileSize(size: Long) {
        sharedPreferences?.edit()?.putLong(KEY_LOG_FILE_SIZE, size)?.apply()
    }
    
    /**
     * 获取压缩文件总存储限制
     */
    fun getCompressedStorageLimit(): Long {
        val defaultLimit = if (isDebugMode) DEBUG_COMPRESSED_STORAGE else PRODUCTION_COMPRESSED_STORAGE
        return sharedPreferences?.getLong(KEY_COMPRESSED_STORAGE_LIMIT, defaultLimit) ?: defaultLimit
    }
    
    /**
     * 设置压缩文件总存储限制
     */
    fun setCompressedStorageLimit(limit: Long) {
        sharedPreferences?.edit()?.putLong(KEY_COMPRESSED_STORAGE_LIMIT, limit)?.apply()
    }
    
    /**
     * 获取原始日志总存储限制
     */
    fun getRawStorageLimit(): Long {
        val defaultLimit = if (isDebugMode) DEBUG_RAW_STORAGE else PRODUCTION_RAW_STORAGE
        return sharedPreferences?.getLong(KEY_RAW_STORAGE_LIMIT, defaultLimit) ?: defaultLimit
    }
    
    /**
     * 设置原始日志总存储限制
     */
    fun setRawStorageLimit(limit: Long) {
        sharedPreferences?.edit()?.putLong(KEY_RAW_STORAGE_LIMIT, limit)?.apply()
    }
    
    /**
     * 获取Recent日志大小限制
     */
    fun getRecentLogSize(): Long {
        val defaultSize = if (isDebugMode) DEBUG_RECENT_SIZE else PRODUCTION_RECENT_SIZE
        return sharedPreferences?.getLong(KEY_RECENT_LOG_SIZE, defaultSize) ?: defaultSize
    }
    
    /**
     * 设置Recent日志大小限制
     */
    fun setRecentLogSize(size: Long) {
        sharedPreferences?.edit()?.putLong(KEY_RECENT_LOG_SIZE, size)?.apply()
    }
    
    /**
     * 获取上传URL
     */
    fun getUploadUrl(): String {
        return sharedPreferences?.getString(KEY_UPLOAD_URL, DEFAULT_UPLOAD_URL) ?: DEFAULT_UPLOAD_URL
    }
    
    /**
     * 设置上传URL
     */
    fun setUploadUrl(url: String) {
        sharedPreferences?.edit()?.putString(KEY_UPLOAD_URL, url)?.apply()
    }
    
    /**
     * 获取访问密钥
     */
    fun getAccessKey(): String {
        return sharedPreferences?.getString(KEY_ACCESS_KEY, DEFAULT_ACCESS_KEY) ?: DEFAULT_ACCESS_KEY
    }
    
    /**
     * 设置访问密钥
     */
    fun setAccessKey(key: String) {
        sharedPreferences?.edit()?.putString(KEY_ACCESS_KEY, key)?.apply()
    }
    
    /**
     * 获取秘密密钥
     */
    fun getSecretKey(): String {
        return sharedPreferences?.getString(KEY_SECRET_KEY, DEFAULT_SECRET_KEY) ?: DEFAULT_SECRET_KEY
    }
    
    /**
     * 设置秘密密钥
     */
    fun setSecretKey(key: String) {
        sharedPreferences?.edit()?.putString(KEY_SECRET_KEY, key)?.apply()
    }
    
    /**
     * 获取配置描述
     */
    fun getConfigDescription(): String {
        val mode = if (isDebugMode) "调试模式" else "生产模式"
        return """
            LogStream配置 ($mode):
            启用状态: ${isEnabled()}
            单个日志文件: ${formatSize(getLogFileSize())}
            压缩文件总限制: ${formatSize(getCompressedStorageLimit())}
            原始日志总限制: ${formatSize(getRawStorageLimit())}
            Recent日志大小: ${formatSize(getRecentLogSize())}
            上传URL: ${getUploadUrl()}
        """.trimIndent()
    }
    
    /**
     * 初始化默认值
     */
    private fun initializeDefaults() {
        val editor = sharedPreferences?.edit()
        
        if (!sharedPreferences?.contains(KEY_ENABLED)!!) {
            editor?.putBoolean(KEY_ENABLED, true)
        }
        if (!sharedPreferences?.contains(KEY_UPLOAD_URL)!!) {
            editor?.putString(KEY_UPLOAD_URL, DEFAULT_UPLOAD_URL)
        }
        if (!sharedPreferences?.contains(KEY_ACCESS_KEY)!!) {
            editor?.putString(KEY_ACCESS_KEY, DEFAULT_ACCESS_KEY)
        }
        if (!sharedPreferences?.contains(KEY_SECRET_KEY)!!) {
            editor?.putString(KEY_SECRET_KEY, DEFAULT_SECRET_KEY)
        }
        
        editor?.apply()
        updateConfigForMode()
    }
    
    /**
     * 根据模式更新配置
     */
    private fun updateConfigForMode() {
        val editor = sharedPreferences?.edit()
        
        if (isDebugMode) {
            editor?.putLong(KEY_LOG_FILE_SIZE, DEBUG_LOG_FILE_SIZE)
            editor?.putLong(KEY_COMPRESSED_STORAGE_LIMIT, DEBUG_COMPRESSED_STORAGE)
            editor?.putLong(KEY_RAW_STORAGE_LIMIT, DEBUG_RAW_STORAGE)
            editor?.putLong(KEY_RECENT_LOG_SIZE, DEBUG_RECENT_SIZE)
        } else {
            editor?.putLong(KEY_LOG_FILE_SIZE, PRODUCTION_LOG_FILE_SIZE)
            editor?.putLong(KEY_COMPRESSED_STORAGE_LIMIT, PRODUCTION_COMPRESSED_STORAGE)
            editor?.putLong(KEY_RAW_STORAGE_LIMIT, PRODUCTION_RAW_STORAGE)
            editor?.putLong(KEY_RECENT_LOG_SIZE, PRODUCTION_RECENT_SIZE)
        }
        
        editor?.apply()
    }
    
    /**
     * 格式化文件大小
     */
    private fun formatSize(bytes: Long): String {
        return when {
            bytes >= 1024 * 1024 * 1024 -> "${bytes / (1024 * 1024 * 1024)}GB"
            bytes >= 1024 * 1024 -> "${bytes / (1024 * 1024)}MB"
            bytes >= 1024 -> "${bytes / 1024}KB"
            else -> "${bytes}B"
        }
    }
    
    /**
     * 重置为默认配置
     */
    fun resetToDefault() {
        sharedPreferences?.edit()?.clear()?.apply()
        initializeDefaults()
    }
}
