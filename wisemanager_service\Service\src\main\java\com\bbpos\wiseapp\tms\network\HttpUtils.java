package com.bbpos.wiseapp.tms.network;

import android.os.Build;
import android.os.Environment;

import com.bbpos.wiseapp.logger.BBLog;
import com.bbpos.wiseapp.service.R;
import com.bbpos.wiseapp.system.api.DeviceInfoApi;
import com.bbpos.wiseapp.tms.adapter.SystemManagerAdapter;
import com.bbpos.wiseapp.tms.adapter.SystemManagerAdapter.BindServiceSuccess;
import com.bbpos.wiseapp.tms.listener.device.HardwareInfo;
import com.bbpos.wiseapp.tms.utils.Constants;
import com.bbpos.wiseapp.tms.utils.ContextUtil;
import com.bbpos.wiseapp.tms.utils.FileUtils;
import com.bbpos.wiseapp.tms.utils.Helpers;
import com.bbpos.wiseapp.tms.utils.IOUtils;
import com.bbpos.wiseapp.tms.utils.ParameterFactory;
import com.bbpos.wiseapp.tms.utils.ParameterName;
import com.bbpos.wiseapp.utils.RSAUtils;
import com.bbpos.wiseapp.websocket.WebSocketManager;
import com.bbpos.wiseapp.websocket.nv.WmSSlContent;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.BufferedInputStream;
import java.io.BufferedOutputStream;
import java.io.BufferedReader;
import java.io.ByteArrayOutputStream;
import java.io.DataInputStream;
import java.io.DataOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.io.SyncFailedException;
import java.net.MalformedURLException;
import java.net.URL;
import java.security.cert.CertPath;
import java.security.cert.CertPathValidatorException;
import java.util.Calendar;
import java.util.Date;

import javax.net.ssl.HostnameVerifier;
import javax.net.ssl.HttpsURLConnection;
import javax.net.ssl.SSLContext;
import javax.net.ssl.SSLSession;

public class HttpUtils {
    private static final String TAG = "HttpUtils";
    private static final int DOWNLOAD_RETRY = 4;
    private static final int NETOFF_RETRY = 3;

    public static JSONObject request(JSONObject paramJsonObj) {
        DataOutputStream bOutputStream = null;
        InputStream inputStream = null;
        JSONObject responseJson;
        String errorMsg;
        HttpsURLConnection urlConnection = null;
        try {
            BBLog.v(BBLog.TAG, "url = " + Constants.HTTP_TRANS_URL(paramJsonObj.getString(ParameterName.TYPE)));
            URL url = new URL(Constants.HTTP_TRANS_URL(paramJsonObj.getString(ParameterName.TYPE)));
            urlConnection = (HttpsURLConnection) url.openConnection();

			SSLContext sc = WmSSlContent.getSSLContext();
			urlConnection.setSSLSocketFactory(sc.getSocketFactory());
			urlConnection.setHostnameVerifier(new HostnameVerifier() {
				@Override
				public boolean verify(String hostname, SSLSession session) {
					HostnameVerifier hv=HttpsURLConnection.getDefaultHostnameVerifier();
                    BBLog.e(TAG, "request, httpconnect verify = " + hv.verify(hostname,session));
					return hv.verify(hostname,session);
				}
			});

            //  BBLog.i(BBLog.TAG, "url = "+url.toString());
            urlConnection.setDoInput(true);
            urlConnection.setDoOutput(true);
            urlConnection.setConnectTimeout(Constants.ConnectionTimeout);
            urlConnection.setReadTimeout(Constants.SoTimeout);
            urlConnection.setRequestMethod("POST");
            urlConnection.setRequestProperty("Connection", "Keep-Alive");
            urlConnection.setRequestProperty("Charset", "UTF-8");
            urlConnection.setRequestProperty("User-Agent", "directclient");
            urlConnection.setRequestProperty("Content-Type", "application/x-java-serialized-object");
            bOutputStream = new DataOutputStream(urlConnection.getOutputStream());
            String paramStr = paramJsonObj.toString();
            BBLog.i(BBLog.TAG, "http request,param=" + paramStr);
            bOutputStream.write(paramStr.getBytes("UTF-8"));
            bOutputStream.flush();
            //  BBLog.v(BBLog.TAG, "http response,ResponseMessage=" + urlConnection.getResponseCode() + " ResponseMessage=" + urlConnection.getResponseMessage());

            inputStream = urlConnection.getInputStream();
            StringBuffer responseSb = new StringBuffer();
            // 读取服务器数据
            if (inputStream != null) {
                ByteArrayOutputStream baOutputStream = new ByteArrayOutputStream();
                int len = -1;
                byte[] buf = new byte[1024];
                while ((len = inputStream.read(buf)) != -1) {
                    baOutputStream.write(buf, 0, len);
                }
                baOutputStream.flush();
                responseSb.append(baOutputStream.toString());
            }

            // 解析服务器返回数据
            responseJson = new JSONObject(responseSb.toString());
            BBLog.i(BBLog.TAG, "http response,type=" + paramJsonObj.getString(ParameterName.TYPE) + " jsonParam=" + responseJson.toString());
            return responseJson;
        } catch (IOException e) {
            e.printStackTrace();
            errorMsg = e.getMessage();
            BBLog.i(BBLog.TAG, "http response,IOException==" + errorMsg);
        } catch (JSONException e) {
            e.printStackTrace();
            errorMsg = e.getMessage();
            BBLog.i(BBLog.TAG, "http response,JSONException");
        } catch (Exception e) {
            e.printStackTrace();
            errorMsg = e.getMessage();
            BBLog.i(BBLog.TAG, "http response,Exception");
        } finally {
            if (urlConnection != null)
                urlConnection.disconnect();
            IOUtils.flushCloseOutputStream(bOutputStream);
            IOUtils.closeInputStream(inputStream);
        }
        responseJson = new JSONObject();
        try {
            responseJson.put(ParameterName.errorMsg, errorMsg);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return responseJson;
    }

    /**
     * websocket 请求参数封装
     *
     * @param type
     * @param dataType
     * @param serialNo
     * @param dataJson
     * @return
     */
    public static JSONObject request(String type, String dataType, String serialNo, JSONObject dataJson) {
        JSONObject responseJson;
        String errorMsg = null;
        try {
            JSONObject paramJsonObj = new JSONObject();
            paramJsonObj.put(ParameterName.TYPE, type);
            //  if(Helpers.isStrNoEmpty(dataType))
            //  paramJsonObj.put(ParameterName.DATA_TYPE, dataType);
            paramJsonObj.put(ParameterName.serialNo_, serialNo);
            long time = System.currentTimeMillis();
            paramJsonObj.put(ParameterName.request_id, time + type);//请求ID,格式为时间戳+协议编号，例如1555299581321C0109
            paramJsonObj.put(ParameterName.request_time, time + "");
            paramJsonObj.put(ParameterName.VERSION, Constants.SERVER_INTERFACE_VERSION);
            //  paramJsonObj.put(ParameterName.POS, ParameterFactory.createPosInfo());
            if (dataJson != null)
                paramJsonObj.put(ParameterName.DATA, dataJson);

            paramJsonObj.put(ParameterName.myVersionName, Constants.U_MY_VERSION);
            paramJsonObj.put(ParameterName.terminalDate, Helpers.getTransDateStr(new Date()));
            return paramJsonObj;
        } catch (JSONException e) {
            e.printStackTrace();
            errorMsg = e.getMessage();
        } catch (Exception e) {
            e.printStackTrace();
            errorMsg = e.getMessage();
        }
        responseJson = new JSONObject();
        try {
            responseJson.put(ParameterName.errorMsg, errorMsg);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return responseJson;
    }

    public static JSONObject request(String type, String dataType, JSONObject dataJson) {
        JSONObject responseJson;
        String errorMsg = null;
        try {
            JSONObject paramJsonObj = new JSONObject();
            paramJsonObj.put(ParameterName.TYPE, type);
            if (Helpers.isStrNoEmpty(dataType))
                paramJsonObj.put(ParameterName.DATA_TYPE, dataType);
            paramJsonObj.put(ParameterName.VERSION, Constants.SERVER_INTERFACE_VERSION);
            paramJsonObj.put(ParameterName.POS, ParameterFactory.createPosInfo());
            if (dataJson != null)
                paramJsonObj.put(ParameterName.DATA, dataJson);

            return request(paramJsonObj);
        } catch (JSONException e) {
            e.printStackTrace();
            errorMsg = e.getMessage();
        } catch (Exception e) {
            e.printStackTrace();
            errorMsg = e.getMessage();
        }
        responseJson = new JSONObject();
        try {
            responseJson.put(ParameterName.errorMsg, errorMsg);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return responseJson;
    }

    public static JSONObject request(String type, String dataType, JSONArray dataJson) {
        JSONObject responseJson;
        String errorMsg = null;
        try {
            JSONObject paramJsonObj = new JSONObject();
            paramJsonObj.put(ParameterName.TYPE, type);
            if (Helpers.isStrNoEmpty(dataType))
                paramJsonObj.put(ParameterName.DATA_TYPE, dataType);
            paramJsonObj.put(ParameterName.VERSION, Constants.SERVER_INTERFACE_VERSION);
            paramJsonObj.put(ParameterName.POS, ParameterFactory.createPosInfo());
            if (dataJson != null)
                paramJsonObj.put(ParameterName.DATA, dataJson);

            return request(paramJsonObj);
        } catch (JSONException e) {
            e.printStackTrace();
            errorMsg = e.getMessage();
        } catch (Exception e) {
            e.printStackTrace();
            errorMsg = e.getMessage();
        }
        responseJson = new JSONObject();
        try {
            responseJson.put(ParameterName.errorMsg, errorMsg);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return responseJson;
    }

    public static JSONObject request(String type, JSONObject dataJson) {
        return request(type, null, dataJson);
    }

    public static JSONObject request(String type, JSONArray dataJson) {
        return request(type, null, dataJson);
    }

    public static void request(final String type, final JSONObject jsonParams, final RequestCallBack callback) {
        if (ParameterName.facNo.equals("") || ParameterName.facNo == null) {
            SystemManagerAdapter.bindServiceInit(ContextUtil.getInstance(), new BindServiceSuccess() {

                @Override
                public void onBindSuccess() {
                    // TODO Auto-generated method stub
                    request(type, jsonParams, callback, NETOFF_RETRY);

                }
            });
        } else {
            request(type, jsonParams, callback, NETOFF_RETRY);
        }
    }

    public static void requestArray(final String type, final JSONArray jsonArrayParams, final RequestCallBack callback) {
        if (ParameterName.facNo.equals("") || ParameterName.facNo == null) {
            SystemManagerAdapter.bindServiceInit(ContextUtil.getInstance(), new BindServiceSuccess() {

                @Override
                public void onBindSuccess() {
                    // TODO Auto-generated method stub
                    requestArray(type, jsonArrayParams, callback, NETOFF_RETRY);
                }
            });
        } else {
            requestArray(type, jsonArrayParams, callback, NETOFF_RETRY);
        }
    }

    public static void request(String type, JSONObject jsonParams, RequestCallBack callback, int retryTime) {
        try {
            BBLog.e(BBLog.TAG, "发送给服务器：" + jsonParams);
            JSONObject responseJson = request(type, jsonParams);
            BBLog.e(BBLog.TAG, "服务器应答：" + responseJson);
            if (responseJson.has(ParameterName.errorMsg) || responseJson.equals(new JSONObject())) {
                if (retryTime == 0) {
                    callback.requestFail(0, ContextUtil.getInstance().getString(R.string.timeout_and_no_response));
                } else {
                    BBLog.e(BBLog.TAG, "retryTime" + retryTime);
                    retryTime--;
                    request(type, jsonParams, callback, retryTime);
                }
            } else {
                callback.requestSuccess(responseJson);
            }
        } catch (JSONException e) {
            e.printStackTrace();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void requestArray(String type, JSONArray jsonArrayParams, RequestCallBack callback, int retryTime) {
        try {
            BBLog.e("--HttpUtils--", "发送给服务器数据：" + jsonArrayParams.toJSONObject(jsonArrayParams));
            JSONObject responseJson = request(type, jsonArrayParams);
            BBLog.e("--HttpUtils--", "服务器应答：" + responseJson);
            if (responseJson.has(ParameterName.errorMsg) || responseJson.equals(new JSONObject())) {
                if (retryTime == 0) {
                    callback.requestFail(0, ContextUtil.getInstance().getString(R.string.timeout_and_no_response));
                } else {
                    BBLog.e("--retryTime-", "retryTime" + retryTime);
                    retryTime--;
                    requestArray(type, jsonArrayParams, callback, retryTime);
                }
            } else {
                callback.requestSuccess(responseJson);
            }
        } catch (JSONException e) {
            e.printStackTrace();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 文件下载 增加重试机制
     */
    public static void requestForFileWithRetry(String type, JSONObject jsonParams, String fileName, final RequestCallBack callbackWihtRetry) {
        final JSONObject returnJsonObj = new JSONObject();
        final String RESPONSE_JSON_KEY = "RESPONSE_JSON_KEY";
        for (int i = 0; i < Constants.FILE_DOWNLOAD_RETRY; i++) {
            if (returnJsonObj.has(RESPONSE_JSON_KEY)) { //下载成功 退出循环
                try {
                    callbackWihtRetry.requestSuccess(returnJsonObj.getJSONObject(RESPONSE_JSON_KEY));
                } catch (Exception e) {
                    e.printStackTrace();
                }
                return;
            } else if (returnJsonObj.has(ParameterName.isStorageOverflow)) { //内部存储空间不足，退出循环
                try {
                    callbackWihtRetry.requestFail(0, returnJsonObj.getString(ParameterName.isStorageOverflow));
                } catch (Exception e) {
                    e.printStackTrace();
                }
                return;
            }
            requestForFile(type, jsonParams, fileName, new FileDownloadCallBack() {
                @Override
                public void requestSuccess(JSONObject responseJson) throws Exception {
                    returnJsonObj.put(RESPONSE_JSON_KEY, responseJson);
                    return;
                }

                @Override
                public void requestFail(int errorCode, String errorStr) {
                    try {
                        if (Constants.STORAGE_OVER_FLOW.equals(errorStr))
                            returnJsonObj.put(ParameterName.isStorageOverflow, errorStr);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                    BBLog.e(BBLog.TAG, "requestForFileWithRetry:fiel download failed." + errorStr);
                }

                @Override
                public void onDownloading(long curFileSize, long fileSize) {
                    if (callbackWihtRetry != null && callbackWihtRetry instanceof FileDownloadCallBack)
                        ((FileDownloadCallBack) callbackWihtRetry).onDownloading(curFileSize, fileSize);
                }
            });
        }
        callbackWihtRetry.requestFail(0, "fiel download failed.");
    }

    public static void requestForFile(String type, JSONObject jsonParams, String fileName, final RequestCallBack callback) {
        DataOutputStream bOutputStream = null;
        InputStream inputStream = null;
        FileOutputStream fileOutputStream = null;
        HttpsURLConnection urlConnection = null;
        long curFileLen = 0;
        try {
            if (Helpers.isStrNoEmpty(fileName)) {
                File fileTmp = new File(fileName);
                try {
                    if (!fileTmp.getCanonicalPath().startsWith(Environment.getExternalStorageDirectory().getCanonicalPath())) {
                        BBLog.e("HttpUtils", "Path Traversal");
                    }
                } catch (IOException e) {
                    e.printStackTrace();
                }
                if (fileTmp.exists()) {
                    curFileLen = fileTmp.length();
                    jsonParams.put(ParameterName.starPoint, curFileLen + "");
                    BBLog.v(BBLog.TAG, "requestForFile:file:" + fileName + " exist.length=" + curFileLen);
                } else {
                    File fileP = new File(fileTmp.getParent());
                    if (!fileP.exists()) {
                        fileP.mkdirs();
                    }
                    jsonParams.put(ParameterName.starPoint, "0");
                }
            }
            URL url = new URL(Constants.HTTP_TRANS_URL(type));
            urlConnection = (HttpsURLConnection) url.openConnection();

			SSLContext sc = WmSSlContent.getSSLContext();
			urlConnection.setSSLSocketFactory(sc.getSocketFactory());
			urlConnection.setHostnameVerifier(new HostnameVerifier() {
				@Override
				public boolean verify(String hostname, SSLSession session) {
					HostnameVerifier hv=HttpsURLConnection.getDefaultHostnameVerifier();
                    BBLog.e(TAG, "requestForFile, httpconnect verify = " + hv.verify(hostname,session));
					return hv.verify(hostname,session);
				}
			});

            urlConnection.setDoInput(true);
            urlConnection.setDoOutput(true);
            urlConnection.setConnectTimeout(Constants.ConnectionTimeout);
            urlConnection.setReadTimeout(Constants.SoTimeout);
            urlConnection.setRequestMethod("POST");
            urlConnection.setRequestProperty("Connection", "Keep-Alive");
            urlConnection.setRequestProperty("Charset", "UTF-8");
            urlConnection.setRequestProperty("Content-Type", "application/x-java-serialized-object");

            bOutputStream = new DataOutputStream(urlConnection.getOutputStream());
            JSONObject paramJsonObj = new JSONObject();
            paramJsonObj.put(ParameterName.TYPE, type);
            paramJsonObj.put(ParameterName.VERSION, Constants.SERVER_INTERFACE_VERSION);
            paramJsonObj.put(ParameterName.POS, ParameterFactory.createPosInfo());

            if (jsonParams != null)
                paramJsonObj.put(ParameterName.DATA, jsonParams);
            String paramStr = paramJsonObj.toString();
            BBLog.i(BBLog.TAG, "http request,param=" + paramStr);
            bOutputStream.write(paramStr.getBytes("UTF-8"));
            bOutputStream.flush();

            //  File curFile = new File(fileName);
            inputStream = urlConnection.getInputStream();
            //文件信息：json串长度（2B）+json串文件信息
            //  byte[] lengthBytes = new byte[2];
            //	inputStream.read(lengthBytes, 0, lengthBytes.length);
            // int fileJsonSize = (int) (((lengthBytes[0]& 0xFF)<<8) | ((lengthBytes[1]& 0xFF)));
            //	byte[] fileJsonBytes = new byte[fileJsonSize];
            //	inputStream.read(fileJsonBytes, 0, fileJsonBytes.length);
            //	String fileJsonStr = new String(fileJsonBytes);

            StringBuffer responseSb = new StringBuffer();
            // 读取服务器数据
            if (inputStream != null) {
                ByteArrayOutputStream baOutputStream = new ByteArrayOutputStream();
                int len = -1;
                byte[] buf = new byte[1024];
                while ((len = inputStream.read(buf)) != -1) {
                    baOutputStream.write(buf, 0, len);
                }
                baOutputStream.flush();
                responseSb.append(baOutputStream.toString());
            }

            // 解析服务器返回数据
            JSONObject responseJson = new JSONObject(responseSb.toString());
            //			    BBLog.i(BBLog.TAG, "jsonObject json:"+responseJson.toString());
            BBLog.i(BBLog.TAG, "file title json:" + responseSb.toString());
            //文件大小 文件md5 校验用
            String data = responseJson.getString(ParameterName.DATA);
            JSONObject fileInfoJson = new JSONObject(data);
            final JSONObject downloadFileInfoJson = new JSONObject();
            downloadFileInfoJson.put(ParameterName.downloadFileName, fileName);
            downloadFileInfoJson.put(ParameterName.downloadFileInfo, fileInfoJson);
            try {
                String fileMd5 = fileInfoJson.getString(ParameterName.fileMd5);
                final long totalfileSize = fileInfoJson.getLong(ParameterName.fileSize);
                String fileUrl = fileInfoJson.getString(ParameterName.url);

                HttpUtils.fileDownloadByUrlWithRetry(fileUrl, fileName, totalfileSize, fileMd5, new HttpUtils.FileDownloadCallBack() {
                    @Override
                    public void requestSuccess(JSONObject responseJson) throws Exception {
                        // TODO Auto-generated method stub
                        callback.requestSuccess(downloadFileInfoJson);
                    }

                    @Override
                    public void requestFail(int errorCode, String errorStr) {
                        // TODO Auto-generated method stub
                        callback.requestFail(errorCode, "download file failed");
                    }

                    @Override
                    public void onDownloading(long curFileSize, long fileSize) {
                        //下载进度反馈 TODO
                        if (callback != null && callback instanceof FileDownloadCallBack)
                            ((FileDownloadCallBack) callback).onDownloading(curFileSize, fileSize);
                    }
                });
            } catch (Exception e) {
                BBLog.e(BBLog.TAG, "error in http response data: " + e.toString());
            }
        } catch (IOException e) {
            e.printStackTrace();
            if (callback != null)
                callback.requestFail(0, e.getMessage());
        } catch (JSONException e) {
            e.printStackTrace();
            if (callback != null)
                callback.requestFail(0, e.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
            if (callback != null)
                callback.requestFail(0, e.getMessage());
        } finally {
            if (urlConnection != null)
                urlConnection.disconnect();
            IOUtils.flushCloseOutputStream(fileOutputStream);
            IOUtils.flushCloseOutputStream(bOutputStream);
            IOUtils.closeInputStream(inputStream);
        }
    }

    /**
     * 文件下载  无断点续传、MD5校验
     */
    public static boolean requestForFileWithoutCheck(String type, JSONObject jsonParams, String fileName, RequestCallBack callback) {
        boolean isDownloadSucc = false;
        DataOutputStream bOutputStream = null;
        InputStream inputStream = null;
        FileOutputStream fileOutputStream = null;

        HttpsURLConnection urlConnection = null;
        try {
            File curFile = new File(fileName);
            if (curFile.exists()) {
                curFile.delete();
            }
            if (!curFile.getParentFile().exists())
                curFile.getParentFile().mkdirs();
            curFile.createNewFile();

            URL url = new URL(Constants.HTTP_TRANS_URL(type));
            urlConnection = (HttpsURLConnection) url.openConnection();

			SSLContext sc = WmSSlContent.getSSLContext();
			urlConnection.setSSLSocketFactory(sc.getSocketFactory());
			urlConnection.setHostnameVerifier(new HostnameVerifier() {
				@Override
				public boolean verify(String hostname, SSLSession session) {
					HostnameVerifier hv=HttpsURLConnection.getDefaultHostnameVerifier();
                    BBLog.e(TAG, "requestForFileWithoutCheck, httpconnect verify = " + hv.verify(hostname,session));
					return hv.verify(hostname,session);
				}
			});

            urlConnection.setDoInput(true);
            urlConnection.setDoOutput(true);
            urlConnection.setConnectTimeout(Constants.ConnectionTimeout);
            urlConnection.setReadTimeout(Constants.SoTimeout);
            urlConnection.setRequestMethod("POST");
            urlConnection.setRequestProperty("Connection", "Keep-Alive");
            urlConnection.setRequestProperty("Charset", "UTF-8");
            urlConnection.setRequestProperty("Content-Type", "application/x-java-serialized-object");

            bOutputStream = new DataOutputStream(urlConnection.getOutputStream());
            JSONObject paramJsonObj = new JSONObject();
            paramJsonObj.put(ParameterName.TYPE, type);
            paramJsonObj.put(ParameterName.VERSION, Constants.SERVER_INTERFACE_VERSION);
            paramJsonObj.put(ParameterName.POS, ParameterFactory.createPosInfo());

            if (jsonParams != null)
                paramJsonObj.put(ParameterName.DATA, jsonParams);
            String paramStr = paramJsonObj.toString();
            BBLog.i(BBLog.TAG, "http request,param=" + paramStr);
            bOutputStream.write(paramStr.getBytes("UTF-8"));
            bOutputStream.flush();


            inputStream = urlConnection.getInputStream();
            //文件信息：json串长度（2B）+json串文件信息
            byte[] lengthBytes = new byte[2];
            inputStream.read(lengthBytes, 0, lengthBytes.length);
            int fileJsonSize = (int) (((lengthBytes[0] & 0xFF) << 8) | ((lengthBytes[1] & 0xFF)));
            byte[] fileJsonBytes = new byte[fileJsonSize];
            inputStream.read(fileJsonBytes, 0, fileJsonBytes.length);
            String fileJsonStr = new String(fileJsonBytes);
            JSONObject fileJson = new JSONObject(fileJsonStr);
            if (!Helpers.isReturnSuccess(fileJson)) {
                String errorMsg = "";
                if (fileJson.has(ParameterName.stateDesc)) {
                    errorMsg = fileJson.getString(ParameterName.stateDesc);
                }
                if (callback != null) {
                    callback.requestFail(0, errorMsg);
                }
                return false;
            }
            String fileMd5 = fileJson.getString(ParameterName.fileMd5);
            BBLog.i(BBLog.TAG, "file title json:" + fileJsonStr);

            byte[] bytes = new byte[4 * 1024];
            int numReadByte = 0;
            // 读取服务器数据 并写入到文件中
            fileOutputStream = new FileOutputStream(curFile, true);
            while ((numReadByte = inputStream.read(bytes, 0, bytes.length)) > 0) {
                fileOutputStream.write(bytes, 0, numReadByte);
            }
            fileOutputStream.flush();
            BBLog.i(BBLog.TAG, "http response,type=" + type + " filePath=" + fileName);
            String localFileMd5 = FileUtils.getMd5ByFile(curFile);
            if (localFileMd5.equals(fileMd5)) {
                if (fileOutputStream != null) {
                    fileOutputStream.close();
                    fileOutputStream = null;
                }
                curFile.delete();//下载文件错误 删除文件
                callback.requestFail(0, "md5 check failed 2");
                return isDownloadSucc;
            }

            if (callback != null) {
                callback.requestSuccess(fileJson);
            }
            isDownloadSucc = true;
        } catch (IOException e) {
            isDownloadSucc = false;
            e.printStackTrace();
            if (callback != null)
                callback.requestFail(0, e.getMessage());
        } catch (JSONException e) {
            isDownloadSucc = false;
            e.printStackTrace();
            if (callback != null)
                callback.requestFail(0, e.getMessage());
        } catch (Exception e) {
            isDownloadSucc = false;
            e.printStackTrace();
            if (callback != null)
                callback.requestFail(0, e.getMessage());
        } finally {
            if (urlConnection != null)
                urlConnection.disconnect();
            IOUtils.flushCloseOutputStream(fileOutputStream);
            IOUtils.flushCloseOutputStream(bOutputStream);
            IOUtils.closeInputStream(inputStream);
        }
        return isDownloadSucc;
    }

    public static JSONObject fileUpload(File file, String type, String jsonParams, RequestCallBack callback) {
        BBLog.i(BBLog.TAG, "http fileUpload,type=" + type + " jsonParam=" + jsonParams.toString());

        InputStream inputStream = null;
        BufferedOutputStream bOutputStream = null;
        FileInputStream fileInputStream = null;
        HttpsURLConnection urlConnection = null;
        try {
            String urlWithParam = Constants.HTTP_UPLOAD_URL + "?" + ParameterName.TYPE + "=" + type + "&"
                    + ParameterFactory.createPosInfoForUrl() + "&" + jsonParams.toString();
            URL url = new URL(urlWithParam);
            urlConnection = (HttpsURLConnection) url.openConnection();

			SSLContext sc = WmSSlContent.getSSLContext();
			urlConnection.setSSLSocketFactory(sc.getSocketFactory());
			urlConnection.setHostnameVerifier(new HostnameVerifier() {
				@Override
				public boolean verify(String hostname, SSLSession session) {
					HostnameVerifier hv=HttpsURLConnection.getDefaultHostnameVerifier();
					BBLog.e(TAG, "fileUpload, httpconnect : verify = "+ hv.verify(hostname,session));
					return hv.verify(hostname,session);
				}
			});

            String end = "\r\n";
            String twoHyphens = "--";
            String boundary = "********";

            urlConnection.setDoInput(true);
            urlConnection.setDoOutput(true);
            urlConnection.setConnectTimeout(Constants.ConnectionTimeout);
            urlConnection.setReadTimeout(Constants.SoTimeout);
            urlConnection.setRequestMethod("POST");
            urlConnection.addRequestProperty("FileName", file.getName());
            urlConnection.setRequestProperty("Connection", "Keep-Alive");
            urlConnection.setRequestProperty("Charset", "UTF-8");
            urlConnection.setRequestProperty("Content-Type", "multipart/form-data;boundary=" + boundary);
            bOutputStream = new BufferedOutputStream(urlConnection.getOutputStream());

            StringBuffer strBuf = new StringBuffer();
            strBuf.append(twoHyphens + boundary + end);
            strBuf.append("Content-Disposition: form-data; name=\"uploadPic\"; filename=\"" + file.getName() + "\"" + end);
            strBuf.append("Content-Type: application/octet-stream; charset=utf-8" + end + end);
            bOutputStream.write(strBuf.toString().getBytes());

            // 读取文件上传到服务器
            fileInputStream = new FileInputStream(file);
            byte[] bytes = new byte[1024];
            int numReadByte = 0;
            while ((numReadByte = fileInputStream.read(bytes, 0, 1024)) > 0) {
                bOutputStream.write(bytes, 0, numReadByte);
            }
            bOutputStream.write((end + twoHyphens + boundary + twoHyphens + end).getBytes());
            bOutputStream.flush();
            BBLog.v(BBLog.TAG, "http response,ResponseMessage=" + urlConnection.getResponseCode() + " ResponseMessage=" + urlConnection.getResponseMessage());

            inputStream = urlConnection.getInputStream();
            // 读取buffer
            byte[] buffer = new byte[4 * 1024];
            StringBuffer responseSb = new StringBuffer();
            // 读取服务器数据
            while (inputStream.read(buffer) != -1) {
                responseSb.append(new String(buffer));
            }

            // 解析服务器返回数据
            JSONObject responseJson = new JSONObject(responseSb.toString());
            BBLog.i(BBLog.TAG, "http response,type=" + type + " jsonParam=" + responseJson.toString());
            if (callback != null) {
                callback.requestSuccess(responseJson);
            }
            return responseJson;
        } catch (Exception e) {
            e.printStackTrace();
            if (callback != null) {
                callback.requestFail(0, e.getMessage());
            }
        } finally {
            if (urlConnection != null)
                urlConnection.disconnect();
            IOUtils.closeInputStream(inputStream);
            IOUtils.closeInputStream(fileInputStream);
            IOUtils.flushCloseOutputStream(bOutputStream);
        }
        return null;
    }

    /**
     * 上传图片
     *
     * @param urlStr
     * @param filePath
     * @param callback
     * @return
     */
    public static String fileUpload(String urlStr, String filePath, RequestCallBack callback) {
        String res = "";
        HttpsURLConnection urlConnection = null;
        String end = "\r\n";
        String twoHyphens = "--";
        String boundary = "********";//boundary就是request头和上传文件内容的分隔符
        OutputStream out = null;
        DataInputStream in = null;
        BufferedReader reader = null;
        try {
            URL url = new URL(urlStr);
            urlConnection = (HttpsURLConnection) url.openConnection();

			SSLContext sc = WmSSlContent.getSSLContext();
            urlConnection.setSSLSocketFactory(sc.getSocketFactory());
            urlConnection.setHostnameVerifier(new HostnameVerifier() {
				@Override
				public boolean verify(String hostname, SSLSession session) {
					HostnameVerifier hv=HttpsURLConnection.getDefaultHostnameVerifier();
					BBLog.e(TAG, "fileUpload, httpconnect : verify = "+ hv.verify(hostname,session));
					return hv.verify(hostname,session);
				}
			});

            urlConnection.setConnectTimeout(5000);
            urlConnection.setReadTimeout(30000);
            urlConnection.setDoOutput(true);
            urlConnection.setDoInput(true);
            urlConnection.setUseCaches(false);
            urlConnection.setRequestMethod("POST");
            urlConnection.setRequestProperty("Connection", "Keep-Alive");
            urlConnection.setRequestProperty("User-Agent", "Mozilla/5.0 (Windows; U; Windows NT 6.1; zh-CN; rv:*******)");
            urlConnection.setRequestProperty("Content-Type", "multipart/form-data; boundary=" + boundary);

            out = new DataOutputStream(urlConnection.getOutputStream());

            // file
            File file = new File(filePath);
            String filename = file.getName();

            StringBuffer strBufSend = new StringBuffer();
            strBufSend.append("\r\n").append("--").append(boundary).append("\r\n");
            strBufSend.append("Content-Disposition: form-data; name=\"uploadPic\"; filename=\"" + filename + "\"" + end);
            strBufSend.append("Content-Type: application/octet-stream; charset=utf-8" + end + end);

            out.write(strBufSend.toString().getBytes());

            in = new DataInputStream(new FileInputStream(file));
            int bytes = 0;
            byte[] bufferOut = new byte[4096];
            while ((bytes = in.read(bufferOut)) != -1) {
                out.write(bufferOut, 0, bytes);
            }

            byte[] endData = ("\r\n--" + boundary + "--\r\n").getBytes();
            out.write(endData);

            // 读取返回数据
            StringBuffer strBuf = new StringBuffer();
            reader = new BufferedReader(new InputStreamReader(urlConnection.getInputStream()));
            String line = null;
            while ((line = reader.readLine()) != null) {
                strBuf.append(line).append("\n");
            }
            res = strBuf.toString();
        } catch (Exception e) {
            System.out.println("发送POST请求出错。" + urlStr);
            e.printStackTrace();

            if (e instanceof CertPathValidatorException){
                CertPathValidatorException exception = (CertPathValidatorException) e;
                CertPath certPath = exception.getCertPath();
                BBLog.e(TAG, "SSLContent certification path : "+certPath );
            }

        } finally {
            IOUtils.closeInputStream(in);
            IOUtils.flushCloseOutputStream(out);
            try {
                if (reader != null) {
                    reader.close();
                    reader = null;
                }
                if (urlConnection != null) {
                    urlConnection.disconnect();
                    urlConnection = null;
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return res;
    }

    public static boolean fileDownloadByUrlWithRetry(String fileUrl, String filePath, long fileLength, String md5,
                                                     final RequestCallBack callBackWithRetry) {
        boolean isDownloadSuccess = false;
        final int[] n_errorCode = {0};
        final JSONObject returnJsonObj = new JSONObject();
        for (int i = 0; i < DOWNLOAD_RETRY; i++) {
            if (isDownloadSuccess) { //下载成功 退出循环
                try {
                    callBackWithRetry.requestSuccess(null);
                } catch (Exception e) {
                    e.printStackTrace();
                }
                return true;
            } else if (returnJsonObj.has(ParameterName.isStorageOverflow)) { //内部存储空间不足，退出循环
                try {
                    callBackWithRetry.requestFail(0, returnJsonObj.getString(ParameterName.isStorageOverflow));
                } catch (Exception e) {
                    e.printStackTrace();
                }
                return false;
            }

            BBLog.i(BBLog.TAG, " try download, try times = " + (i + 1));
            isDownloadSuccess = fileDownloadByUrl(fileUrl, filePath, fileLength, md5, new FileDownloadCallBack() {
                @Override
                public void requestSuccess(JSONObject responseJson) throws Exception {

                }

                @Override
                public void requestFail(int errorCode, String errorStr) {
                    try {
                        n_errorCode[0] = errorCode;
                        if (Constants.STORAGE_OVER_FLOW.equals(errorStr))
                            returnJsonObj.put(ParameterName.isStorageOverflow, errorStr);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                    BBLog.e(BBLog.TAG, "requestForFileWithRetry:file download failed. " + errorCode + " " + errorStr);
                }

                @Override
                public void onDownloading(long curFileSize, long fileSize) {
                    if (callBackWithRetry != null && callBackWithRetry instanceof FileDownloadCallBack)
                        ((FileDownloadCallBack) callBackWithRetry).onDownloading(curFileSize, fileSize);
                }
            });
        }
        if (!isDownloadSuccess) {
            BBLog.e(BBLog.TAG, "requestForFileWithRetry:file download failed. " + n_errorCode[0]);
            callBackWithRetry.requestFail(n_errorCode[0], "file download failed.");
        } else {
            try {
                callBackWithRetry.requestSuccess(null);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return isDownloadSuccess;
    }

    /**
     * 文件下载
     */
    public static boolean fileDownload(String fileUrl, String filePath, long fileLength, String md5) {
        return fileDownloadByUrl(fileUrl, filePath, fileLength, md5, null);
    }

    /**
     * 文件下载
     */
    public static boolean fileDownloadByUrl(String fileUrl, String filePath, long allFileLength, String md5,
                                            FileDownloadCallBack callBack) {
        BBLog.i(BBLog.TAG, "download begin. fileUrl:" + fileUrl);
        BBLog.i(BBLog.TAG, "download begin. filePath:" + filePath);
        fileUrl = fileUrl.replace(" ", "%20");

        // 下载文件
        HttpsURLConnection urlConnection = null;
        InputStream is = null;
        FileOutputStream fos = null;
        String downFailInfo;
        boolean isDownLoadSucc = false;
        try {
            /** 保存文件到本地 */
            File localFile = new File(filePath);
            try {
                if (!localFile.getCanonicalPath().startsWith(Environment.getExternalStorageDirectory().getCanonicalPath())) {
                    BBLog.e("HttpUtils", "Path Traversal");
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
            if (!localFile.exists()) {
                BBLog.i(BBLog.TAG, "localFile does not exists");
                if (!localFile.getParentFile().exists()) {
                    localFile.getParentFile().mkdirs();
                }
                localFile.createNewFile();
            }
            long curFileLength = localFile.length();

            //判断文件是否已下载完成
            if (curFileLength > allFileLength) {
                callBack.requestFail(0, "curFileLen:" + curFileLength + " > serverFileSize:" + allFileLength + ". download file failed");
                localFile.delete();//文件大小大于总文件 删除文件
                return false;
            } else if (curFileLength == allFileLength) {
                // 文件下载成功，MD5校验
                String localFileMd5 = FileUtils.getMd5ByFile(localFile);
                if (md5.equalsIgnoreCase(localFileMd5)) {
                    Calendar cal = Calendar.getInstance();
                    localFile.setLastModified(cal.getTimeInMillis());
                    BBLog.i(BBLog.TAG, "file already downloaded");
                    return true;
                } else {
                    localFile.delete();
                    if (callBack != null)
                        callBack.requestFail(0, "file md5 check failed. 3");
                    return false;
                }
            } else if (HardwareInfo.isROMOverflow(allFileLength)) {
                //内存预留值检测
                if (callBack != null) {
                    callBack.requestFail(0, Constants.STORAGE_OVER_FLOW);
                }
                return false;
            }

            URL url = new URL(fileUrl);
            urlConnection = (HttpsURLConnection) url.openConnection();

			SSLContext sc = WmSSlContent.getSSLContext();
			urlConnection.setSSLSocketFactory(sc.getSocketFactory());
			urlConnection.setHostnameVerifier(new HostnameVerifier() {
				@Override
				public boolean verify(String hostname, SSLSession session) {
					HostnameVerifier hv=HttpsURLConnection.getDefaultHostnameVerifier();
					BBLog.e(TAG, "fileDownloadByUrl, httpconnect : verify = "+ hv.verify(hostname,session));
					return hv.verify(hostname,session);
				}
			});

            /** 设置超时时间 */
            urlConnection.setConnectTimeout(Constants.ConnectionTimeout);
            /** 设置读取超时时间 */
            urlConnection.setReadTimeout(Constants.SoTimeout);
            urlConnection.setRequestMethod("GET");
            /** 设置断点位置 */
            urlConnection.setRequestProperty("Range", String.format("bytes=%d-", curFileLength));
            urlConnection.setRequestProperty("User-Agent", "Mozilla/4.0 (compatible; MSIE 7.0; Windows NT 5.1)");
            //增加处理 java.io.EOFException异常
            urlConnection.setRequestProperty("Connection", "Keep-Alive");
            urlConnection.setRequestProperty(Constants.HTTP_HEAD_KEY_VERSION, Constants.HTTP_HEAD_VALUE_VERSION);
            int returnCode = urlConnection.getResponseCode();
            if ((returnCode != HttpsURLConnection.HTTP_PARTIAL) && (returnCode != HttpsURLConnection.HTTP_OK)) {
                downFailInfo = "http error,response code:" + returnCode;
                localFile.delete();
                BBLog.e(BBLog.TAG, downFailInfo);
                if (callBack != null) {
                    callBack.requestFail(returnCode, "server response fail");
                }
                return isDownLoadSucc;
            }

            long toDownloadFileLength = urlConnection.getContentLength();

            urlConnection.getHeaderFields();
            /** 获取服务器的输入流,获取数据 */
            is = new BufferedInputStream(urlConnection.getInputStream());

            urlConnection.getResponseMessage();
            /** 追加文件 */
            BBLog.e(BBLog.TAG, "localFile = " + localFile.exists() + "  " + localFile.getPath());
            if (DeviceInfoApi.getIntance().isWisePos4G() && !Build.DISPLAY.contains("WiseManager")) {
                fos = new FileOutputStream(localFile, false);
            } else {
                fos = new FileOutputStream(localFile, true);
            }
            /** 读取buffer */
            byte[] buffer = new byte[4 * 1024];
            /** 读取长度 */
            int len;

            /** 读取服务器数据 */
            while ((len = is.read(buffer)) != -1) {
                /** 保存文件到本地 */
                fos.write(buffer, 0, len);
                /** 同步到文件系统 */
                fos.getFD().sync();
                /** 通知下载进度 */
                if (callBack != null) {
                    curFileLength += len;
//					long progress = curFileLength * 100 / toDownloadFileLength;
//					if(curFileLength%100==0)
//						BBLog.e("loading", curFileLength+"/"+allFileLength+"/"+toDownloadFileLength);
                    callBack.onDownloading(curFileLength, allFileLength);
                }
            }
            fos.flush();
            BBLog.i(BBLog.TAG, "download completed.");

            // 文件下载成功，进行校验
            if (!FileUtils.isFileDownloadSuccessed(filePath, allFileLength, md5)) {
                IOUtils.closeInputStream(is);
                IOUtils.flushCloseOutputStream(fos);
                localFile.delete();//下载文件错误 删除文件
                callBack.requestFail(0, "download file failed");
                return isDownLoadSucc;
            }
            BBLog.i(BBLog.TAG, "md5 check right.");
//			// 文件下载成功，MD5校验
//			String localFileMd5 = FileUtils.getMd5ByFile(localFile);
//			if (!md5.equals(localFileMd5)) {
//				BBLog.e(BBLog.TAG, "md5 check failed.4" + md5 + "!=" + localFileMd5);
//				localFile.delete();
//				return isDownLoadSucc;
//			}
            isDownLoadSucc = true;
        } catch (MalformedURLException e) {
            isDownLoadSucc = false;
            downFailInfo = "download throw MalformedURLException e = " + e.toString();
            BBLog.e(BBLog.TAG, downFailInfo);
        } catch (SyncFailedException e) {
            isDownLoadSucc = false;
            downFailInfo = "download file to sdcard SyncFailedException error e = " + e.toString();
            BBLog.e(BBLog.TAG, downFailInfo);
        } catch (IOException e) {
            isDownLoadSucc = false;
            downFailInfo = "download throw IOException e = " + e.toString();
            BBLog.e(BBLog.TAG, downFailInfo);
        } catch (Exception e) {
            isDownLoadSucc = false;
            downFailInfo = "download throw Exception e = " + e.toString();
            BBLog.e(BBLog.TAG, downFailInfo);

            if (e instanceof CertPathValidatorException){
                CertPathValidatorException exception = (CertPathValidatorException) e;
                CertPath certPath = exception.getCertPath();
                BBLog.e(TAG, "SSLContent certification path : "+certPath );
            }

        } finally {
            BBLog.i(BBLog.TAG, "urlConnection. = " + urlConnection);
            if (urlConnection != null) {
                BBLog.i(BBLog.TAG, "urlConnection. disconnect");
                urlConnection.disconnect();
            }
            IOUtils.closeInputStream(is);
            IOUtils.flushCloseOutputStream(fos);
        }
        return isDownLoadSucc;
    }

    /**
     * 断点续传
     *
     * <AUTHOR>
     */
    public static JSONObject fileUploadWithStartPoint(File file,
                                                      String type,
                                                      String fileId,
                                                      String fileStartPoint,
                                                      String jsonParams,
                                                      RequestCallBack callback) {
        BBLog.i(BBLog.TAG, "http fileUpload,type=" + type + " jsonParam=" + jsonParams.toString());

        InputStream inputStream = null;
        BufferedOutputStream bOutputStream = null;
        FileInputStream fileInputStream = null;
        HttpsURLConnection urlConnection = null;
        try {
            String urlWithParam = Constants.HTTP_UPLOAD_URL + "?" +
                    ParameterName.TYPE + "=" + type + "&"
                    + "fileId=" + fileId + "&"
                    + "fileStartPoint=" + fileStartPoint + "&"
                    + "fileSize=" + file.length() + "&"
                    + "fileName=" + file.getName() + "&"
                    + "md5=" + FileUtils.getMd5ByFile(file) + "&"
                    + ParameterFactory.createPosInfoForUrl() + "&"
                    + jsonParams.toString();
//			BBLog.e("fileUploadWithStartPoint", "urlWithParam:"+urlWithParam);
            URL url = new URL(urlWithParam);
            urlConnection = (HttpsURLConnection) url.openConnection();

			SSLContext sc = WmSSlContent.getSSLContext();
			urlConnection.setSSLSocketFactory(sc.getSocketFactory());
			urlConnection.setHostnameVerifier(new HostnameVerifier() {
				@Override
				public boolean verify(String hostname, SSLSession session) {
					HostnameVerifier hv=HttpsURLConnection.getDefaultHostnameVerifier();
					BBLog.e(TAG, "fileUploadWithStartPoint, httpconnect : verify = "+ hv.verify(hostname,session));
					return hv.verify(hostname,session);
				}
			});

            String end = "\r\n";
            String twoHyphens = "--";
            String boundary = "********";

            urlConnection.setDoInput(true);
            urlConnection.setDoOutput(true);
            urlConnection.setConnectTimeout(Constants.ConnectionTimeout);
            urlConnection.setReadTimeout(Constants.SoTimeout);
            urlConnection.setRequestMethod("POST");
            urlConnection.addRequestProperty("FileName", file.getName());
            urlConnection.setRequestProperty("Connection", "Keep-Alive");
            urlConnection.setRequestProperty("Charset", "UTF-8");
            urlConnection.setRequestProperty("Content-Type", "multipart/form-data;boundary=" + boundary);
            bOutputStream = new BufferedOutputStream(urlConnection.getOutputStream());

            StringBuffer strBuf = new StringBuffer();
            strBuf.append(twoHyphens + boundary + end);
            strBuf.append("Content-Disposition: form-data; name=\"uploadPic\"; filename=\"" + file.getName() + "\"" + end);
            strBuf.append("Content-Type: application/octet-stream; charset=utf-8" + end + end);
            bOutputStream.write(strBuf.toString().getBytes());

            // 读取文件上传到服务器
            fileInputStream = new FileInputStream(file);
            byte[] bytes = new byte[1024];
            int numReadByte = 0;
            while ((numReadByte = fileInputStream.read(bytes, 0, 1024)) > 0) {
                bOutputStream.write(bytes, 0, numReadByte);
            }
            bOutputStream.write((end + twoHyphens + boundary + twoHyphens + end).getBytes());
            bOutputStream.flush();
            BBLog.v(BBLog.TAG, "http response,ResponseMessage=" + urlConnection.getResponseCode() + " ResponseMessage=" + urlConnection.getResponseMessage());

            inputStream = urlConnection.getInputStream();
            // 读取buffer
            byte[] buffer = new byte[4 * 1024];
            StringBuffer responseSb = new StringBuffer();
            // 读取服务器数据
            while (inputStream.read(buffer) != -1) {
                responseSb.append(new String(buffer));
            }

            // 解析服务器返回数据
            JSONObject responseJson = new JSONObject(responseSb.toString());
            BBLog.i(BBLog.TAG, "http response,type=" + type + " jsonParam=" + responseJson.toString());
            if (callback != null) {
                callback.requestSuccess(responseJson);
            }
            return responseJson;
        } catch (Exception e) {
            e.printStackTrace();
            if (callback != null) {
                callback.requestFail(0, e.getMessage());
            }

            if (e instanceof CertPathValidatorException){
                CertPathValidatorException exception = (CertPathValidatorException) e;
                CertPath certPath = exception.getCertPath();
                BBLog.e(TAG, "SSLContent certification path : "+certPath );
            }

        } finally {
            if (urlConnection != null)
                urlConnection.disconnect();
            IOUtils.closeInputStream(inputStream);
            IOUtils.closeInputStream(fileInputStream);
            IOUtils.flushCloseOutputStream(bOutputStream);
        }
        return null;
    }

    public static String postGetFileUrl(String str_url, String content) {
        String token = "";
        String result = "";
        String errorMsg;
        DataOutputStream bOutputStream = null;
        InputStream inputStream = null;
        HttpsURLConnection urlConnection = null;
        try {
            BBLog.v(BBLog.TAG, "url = "+ str_url);
            URL url = new URL(str_url + "mdm/resources/regfileurl?" + content);
            urlConnection = (HttpsURLConnection) url.openConnection();

            SSLContext sc = WmSSlContent.getSSLContext();
            urlConnection.setSSLSocketFactory(sc.getSocketFactory());
            urlConnection.setHostnameVerifier(new HostnameVerifier() {
                @Override
                public boolean verify(String hostname, SSLSession session) {
                    HostnameVerifier hv = HttpsURLConnection.getDefaultHostnameVerifier();
                    BBLog.e(TAG, "connectPost, httpconnect verify = " + hv.verify(hostname,session));
                    return hv.verify(hostname,session);
                }
            });
//			BBLog.i(BBLog.TAG, "url = "+url.toString());
            urlConnection.setDoInput(true);
            urlConnection.setDoOutput(false);
            urlConnection.setConnectTimeout(Constants.ConnectionTimeout);
            urlConnection.setReadTimeout(Constants.SoTimeout);
            urlConnection.setRequestMethod("GET");
//			urlConnection.setRequestProperty("Connection", "Keep-Alive");
//			urlConnection.setRequestProperty("Charset", "UTF-8");
//			urlConnection.setRequestProperty("User-Agent", "directclient");
            urlConnection.setRequestProperty("Content-Type","application/json; charset=utf-8");
            BBLog.i(BBLog.TAG, "http post content="+content);
//			BBLog.v(BBLog.TAG, "http response,ResponseMessage=" + urlConnection.getResponseCode() + " ResponseMessage=" + urlConnection.getResponseMessage());

            inputStream = urlConnection.getInputStream();
            StringBuffer responseSb = new StringBuffer();
            // 读取服务器数据
            if (inputStream != null) {
                ByteArrayOutputStream baOutputStream = new ByteArrayOutputStream();
                int len = -1;
                byte[] buf = new byte[1024];
                while ((len = inputStream.read(buf)) != -1) {
                    baOutputStream.write(buf, 0, len);
                }
                baOutputStream.flush();
                responseSb.append(baOutputStream.toString());
            }

            // 解析服务器返回数据
            result = responseSb.toString();
            BBLog.i(BBLog.TAG, "http post result=" + result);
        } catch (IOException e) {
            e.printStackTrace();
            errorMsg = e.getMessage();
            Helpers.sendToastMsgBroad(ContextUtil.getInstance(), errorMsg);
            BBLog.i(BBLog.TAG, "http response,IOException=="+errorMsg);
        } catch (Exception e) {
            e.printStackTrace();
            errorMsg = e.getMessage();
            Helpers.sendToastMsgBroad(ContextUtil.getInstance(), errorMsg);
            BBLog.i(BBLog.TAG, "http response,Exception=="+errorMsg);

            if (e instanceof CertPathValidatorException){
                CertPathValidatorException exception = (CertPathValidatorException) e;
                CertPath certPath = exception.getCertPath();
                BBLog.e(TAG, "SSLContent certification path : "+certPath );
            }
        } finally {
            if(urlConnection != null) {
                urlConnection.disconnect();
            }
            IOUtils.flushCloseOutputStream(bOutputStream);
            IOUtils.closeInputStream(inputStream);
        }

        try {
            JSONObject jsonObject = new JSONObject(result);
            boolean res = jsonObject.optBoolean("success");
            if (res) {
                token = jsonObject.optString("fileUrl");
                BBLog.e(BBLog.TAG, "get S3 token url = " + token);
            }
        } catch (JSONException e) {
            e.printStackTrace();
        }

        return token;
    }

    public static String getPostTokenRequestContent(String fileKey) {
        String plainText = "";
        String encryptText = "";
        try {
            plainText = "serialNo=" + DeviceInfoApi.getIntance().getSerialNumber() + "&filekey=" + fileKey + "&timestamp=" + System.currentTimeMillis();
            encryptText = RSAUtils.sign(plainText.getBytes(), WebSocketManager.m_private_key);
        } catch (Exception e) {
            e.printStackTrace();
            return "";
        }
        return plainText + "&signature=" + encryptText;
    }

    public interface RequestCallBack {
        /**
         * 服务器有正常返回的情况
         */
        public void requestSuccess(JSONObject responseJson) throws Exception;

        /**
         * 服务器无正常返回的情况
         */
        public void requestFail(int errorCode, String errorStr);
    }

    public interface FileDownloadCallBack extends RequestCallBack {
        public void onDownloading(long curFileSize, long fileSize);
    }

}
