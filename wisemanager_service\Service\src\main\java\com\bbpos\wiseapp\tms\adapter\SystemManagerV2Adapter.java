package com.bbpos.wiseapp.tms.adapter;

import android.Manifest;
import android.content.Context;
import android.content.pm.PackageManager;
import android.os.Build;
import android.os.Bundle;
import android.os.IBinder;
import android.os.RemoteException;
import androidx.core.app.ActivityCompat;

import com.bbpos.wiseapp.logger.BBLog;
import com.bbpos.wiseapp.tms.listener.device.MidwareInterface;
import com.bbpos.wiseapp.tms.utils.Constants;
import com.bbpos.wiseapp.tms.utils.Helpers;
import com.bbpos.wiseapp.tms.utils.ParameterName;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.lang.reflect.Method;
import java.util.List;

import com.bbpos.wiseapp.sdk.app.IAppDeleteObserver;
import com.bbpos.wiseapp.sdk.app.IAppInstallObserver;
import com.bbpos.wiseapp.sdk.app.IAppManager;
import com.bbpos.wiseapp.sdk.app.UsageStats;
import com.bbpos.wiseapp.sdk.device.DeviceInfoConstants;
import com.bbpos.wiseapp.sdk.device.DeviceStatus;
import com.bbpos.wiseapp.sdk.device.IDeviceManager;
import com.bbpos.wiseapp.sdk.main.ICloudService;
import com.bbpos.wiseapp.sdk.main.ManagerType;
import com.bbpos.wiseapp.sdk.system.IBackupObserver;
import com.bbpos.wiseapp.sdk.system.IRestoreObserver;
import com.bbpos.wiseapp.sdk.system.ISystemManager;

public class SystemManagerV2Adapter implements ISystemManger{
	private static String TAG = "SystemManagerAdapterV2";
	
	private static ICloudService cloudService = null;
	private static IDeviceManager deviceManager = null;
	private static ISystemManager systemManager = null;
	private static IAppManager appManager = null;

	@Override
	public void initCloudManager(IBinder binder) {
		cloudService = ICloudService.Stub.asInterface(binder);
		try {
			deviceManager = IDeviceManager.Stub.asInterface(cloudService.getManager(ManagerType.DEVICE_MANAGER));
			systemManager = ISystemManager.Stub.asInterface(cloudService.getManager(ManagerType.SYSTEM_MANAGER));
			appManager  = IAppManager.Stub.asInterface(cloudService.getManager(ManagerType.APP_MANAGER));
//			initTerParam();
		} catch (RemoteException e) {
			e.printStackTrace();
		} catch (Exception e){
			e.printStackTrace();
		}
	}

	@Override
	public void unBindCloudManager() {
		cloudService = null;
		BBLog.w(TAG, "cloudService unBindCloudManager. =");
	}
	
	public ICloudService getCloudManager() {
		return cloudService;
	}

	@Override
	public void initTerParam(Context context) {
		try {
			Bundle bundle = deviceManager.getDeviceInfo();
			if(bundle.size() == 0)
				return;
			MidwareInterface.manu = bundle.getString(DeviceInfoConstants.VENDOR);
			if ((Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) && ActivityCompat.checkSelfPermission(context, Manifest.permission.READ_PHONE_STATE) == PackageManager.PERMISSION_GRANTED) {
				MidwareInterface.serial = Build.getSerial();
			} else {
				MidwareInterface.serial = bundle.getString(DeviceInfoConstants.SN);
			}
			MidwareInterface.model = bundle.getString(DeviceInfoConstants.MODEL);
			MidwareInterface.osVer = bundle.getString(DeviceInfoConstants.OS_VERSION);
			MidwareInterface.curTermPkgs = createNullCurTermPkgs().toString();
			MidwareInterface.platform = "";
			MidwareInterface.serviceVer = bundle.getString(DeviceInfoConstants.SERVICE_APP_VERSION);
		} catch (RemoteException e) {
			BBLog.e(BBLog.TAG, e.getMessage(), e);
		} catch (Exception e){
			e.printStackTrace();
		}
	}

	@Override
	public boolean isServiceBinded() {
		return cloudService != null && deviceManager != null && systemManager != null && appManager != null;
	}

	@Override
	public void reboot(Context context) {
		try {
			systemManager.reboot();
		} catch (Exception e) {
			BBLog.e(BBLog.TAG, e.getMessage(), e);
		}
	}

	@Override
	public void installApk(Context context, String appPath, final SystemManagerAdapter.ApkInstallCompleted observer) {
		try {
			IAppInstallObserver observerTmp = new IAppInstallObserver.Stub() {
				@Override
				public void onInstallFinished(String pkgName, int returnCode, String returnMsg) throws RemoteException {
					observer.onInstallFinished(pkgName, returnCode);
				}
			};
			appManager.installApp(Helpers.addApkSuffix(appPath), observerTmp, Constants.INSTALLER_BBPOS);
		} catch (RemoteException e) {
			BBLog.e(BBLog.TAG, e.getMessage(), e);
		} catch (Exception e) {
			BBLog.e(BBLog.TAG, e.getMessage(), e);
		}		
	}

	@Override
	public void updateSystem(Context context, String osFilePath, int updateType) {
		try {
			systemManager.updateSystem(osFilePath, updateType);
		} catch (RemoteException e) {
			BBLog.e(BBLog.TAG, e.getMessage(), e);
		}catch (Exception e) {
			BBLog.e(BBLog.TAG, e.getMessage(), e);
		}		
	}

	@Override
	public void unInstallApk(Context context, String apkPkgs,final SystemManagerAdapter.ApkUnInstallCompleted observer) {
		try {
			IAppDeleteObserver observerTmp = new IAppDeleteObserver.Stub() {
				@Override
				public void onDeleteFinished(String pkgName, int returnCode, String returnMsg) throws RemoteException {
					observer.onDeleteFinished(returnCode);				
				}
			};
			appManager.uninstallApp(apkPkgs, observerTmp);
		} catch (RemoteException e) {
			BBLog.e(BBLog.TAG, e.getMessage(), e);
		}catch (Exception e) {
			BBLog.e(BBLog.TAG, e.getMessage(), e);
		}			
	}

	@Override
	public void backupByPkgName(List<String> pkgList, final SystemManagerAdapter.BackupCompleted listener) {
		try {
			IBackupObserver observerTmp = new IBackupObserver.Stub() {
				@Override
				public void onBackupFinished(int returnCode, String msg, String filePath) throws RemoteException {
					listener.onResult(returnCode, filePath);					
				}
			};
			systemManager.backupByPackage(pkgList, observerTmp);
		} catch (RemoteException e) {
			BBLog.e(BBLog.TAG, e.getMessage(), e);
		} catch (Exception e) {
			BBLog.e(BBLog.TAG, e.getMessage(), e);
		}				
	}

	@Override
	public void restore(String path, final SystemManagerAdapter.RecoveryCompleted listener) {
		try {
			IRestoreObserver observerTmp = new IRestoreObserver.Stub() {
				@Override
				public void onRestoreFinished(int arg0, String arg1) throws RemoteException {
					listener.onResult(arg0);
				}
			};
			systemManager.restore(path, observerTmp);
		} catch (RemoteException e) {
			BBLog.e(BBLog.TAG, e.getMessage(), e);
		} catch (Exception e) {
			BBLog.e(BBLog.TAG, e.getMessage(), e);
		}				
	}

	@Override
	public List<UsageStats> getUsageStats(String yyyyMMdd) {
		// TODO Auto-generated method stub
		try {
			return appManager.getUsageStats(yyyyMMdd);
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		return null;
	}
	
	@Override
	public DeviceStatus getDeviceStatus(int type) {
		// TODO Auto-generated method stub
		try {
			return deviceManager.getDeviceStatus(type);
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		return null;
	}

	@Override
	public List<DeviceStatus> getAllDeviceStatus() {
		// TODO Auto-generated method stub
		try {
			return	deviceManager.getAllDeviceStatus();
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		return null;
	}

	public static JSONObject createNullCurTermPkgs() {
		JSONObject jsonObj = new JSONObject();
        try {
        	jsonObj.put(ParameterName.ProgFree, 0);
        	jsonObj.put(ParameterName.DataFree, 0);
        	jsonObj.put(ParameterName.MemoryFree, 0);
        	jsonObj.put(ParameterName.PkgNum, 0);
        	jsonObj.put(ParameterName.ParamNum, 0);
        	jsonObj.put(ParameterName.ExistAppInfo, createNullExistAppInfo());
        	jsonObj.put(ParameterName.ConfigVer, "");
        	jsonObj.put(ParameterName.EssentialNum, 0);
        	jsonObj.put(ParameterName.ConfigModule, createNullConfigModule());
	        
		} catch (Exception e) {
			e.printStackTrace();
		}
        return jsonObj;
    }
	
	private static JSONArray createNullExistAppInfo() {
		JSONArray ExistAppInfoArray = new JSONArray();
		JSONObject ExistAppInfoObj = new JSONObject();
        try {
        	ExistAppInfoObj.put(ParameterName.FileName, "");
        	ExistAppInfoObj.put(ParameterName.FileSize, 0);
        	ExistAppInfoObj.put(ParameterName.ProgramSize, 0);
        	ExistAppInfoObj.put(ParameterName.DataFileSize, 0);
        	ExistAppInfoObj.put(ParameterName.MemoryFileSize, 0);
        	ExistAppInfoObj.put(ParameterName.ExtendLen, 0);
        	ExistAppInfoObj.put(ParameterName.FileType, "");
        	ExistAppInfoObj.put(ParameterName.DisplayName, "");
        	ExistAppInfoObj.put(ParameterName.Version, "");
        	ExistAppInfoObj.put(ParameterName.AreaName, "");
        	ExistAppInfoObj.put(ParameterName.ProcFlag, 0);
        	ExistAppInfoObj.put(ParameterName.BakFlag, 0);
        	ExistAppInfoObj.put(ParameterName.Priority, 0);
        	ExistAppInfoObj.put(ParameterName.DefaultApp, 0);
        	ExistAppInfoArray.put(ExistAppInfoObj);
		} catch (Exception e) {
			e.printStackTrace();
		}
        return ExistAppInfoArray;
    }
	
	private static JSONArray createNullConfigModule() {
		JSONArray ConfigModuleArray = new JSONArray();
		JSONObject ConfigModuleObj = new JSONObject();
        try {
        	ConfigModuleObj.put(ParameterName.ModuleName, "");
        	ConfigModuleArray.put(ConfigModuleObj);
	        
		} catch (Exception e) {
			e.printStackTrace();
		}
        return ConfigModuleArray;
    }
}
