2025-08-14 18:39:54.906 19136-19136 Platform                com.dspread.mdm.service              D  🔧 WakeLockManager WakeLock不存在或未持有: ServiceKeepAlive
2025-08-14 18:39:54.913 19136-19136 Service                 com.dspread.mdm.service              D  🔧 ServiceKeepAliveService 保活任务完成，释放WakeLock
2025-08-14 18:39:56.863 19136-19207 Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"msg_state":"P","request_time":"1755167995721","data":{"ruleList":[{"deleteAppList":[{"apkName":"Via","appId":"353795033bc0410dac6dee5cb8702b06","packName":"mark.via","versionName":"6.6.0","versionCode":"20250713"}],"modifyDate":"2025-08-14 10:39:55","endDate":"9999-12-31 23:59:59","restartAfter":"0","installBy":"0","beginDate":"2024-08-14 10:39:55","appList":[{"apkMd5":"54cc3a371939423d4288d63e1ac88019","apkName":"cl960","appId":"f32ac615171249418e3fc4b5bc2f4773","apkSize":"6155416","appIconUrl":"https://siot-bucket01.s3.ap-northeast-1.amazonaws.com/icon/c940c121d9c04565a85e36fb5665fac3ic_launcher.png","packName":"com.chileaf.cl960.sample","versionName":"1.1.0","versionCode":"1","url":"https://siot-bucket01.s3.ap-northeast-1.amazonaws.com/app/42e16b87eaa849bcaaf0f5fadcac6aab.apk"},{"apkMd5":"ba1ee533924eae5c408465e7cddcbda4","apkName":"逍遥助手","appId":"225a0a30994e45089dad1a270f8d85a1","apkSize":"4771451","appIconUrl":"https://siot-bucket01.s3.ap-northeast-1.amazonaws.com/icon/6dc157c7ff1649448fb7084544d2e8f5icon.png","packName":"plus.H52FFB9A5","versionName":"1.0","versionCode":"100","url":"https://siot-bucket01.s3.ap-northeast-1.amazonaws.com/app/203831945bb84d42b21decf1bf936815.apk"}],"serviceList":[],"action":"A","ruleName":"d60_test_2992","ruleId":"fb4a8c49e3854b00b51034df8d6cf113","createDate":"2025-08-14 10:39:55","orgRuleId":""}]},"tranCode":"ST005","request_id":"1755167995721ST005","version":"1","serialNo":"01354090202503050399"}
2025-08-14 18:39:56.873 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 准备发送响应确认 - requestId: 1755167995721ST005, needResponse: true
2025-08-14 18:39:56.891 19136-19207 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0000","serialNo":"01354090202503050399","request_time":"1755167996877","request_id":"1755167996877C0000","version":"1","org_request_id":"1755167995721ST005","org_request_time":"1755167995721","response_state":"0","myVersionName":"1.0.08.20250814.DSPREAD.MDM.SERVICE","terminalDate":"20250814183956"}
2025-08-14 18:39:56.914 19136-19207 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0000","serialNo":"01354090202503050399","request_time":"1755167996900","request_id":"1755167996900C0000","version":"1","org_request_id":"1755167995721ST005","org_request_time":"1755167995721","response_state":"0","myVersionName":"1.0.08.20250814.DSPREAD.MDM.SERVICE","terminalDate":"20250814183956"}
2025-08-14 18:39:56.918 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 已发送C0000响应确认: requestId=1755167995721ST005
2025-08-14 18:39:56.927 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleHandler 处理规则消息: ST005
2025-08-14 18:39:56.932 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleHandler 处理规则列表，数量: 1
2025-08-14 18:39:56.936 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleHandler 处理规则: ruleId=fb4a8c49e3854b00b51034df8d6cf113, action=A
2025-08-14 18:39:56.941 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析规则JSON: ruleId=fb4a8c49e3854b00b51034df8d6cf113
2025-08-14 18:39:56.956 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule appList数组: [{"apkMd5":"54cc3a371939423d4288d63e1ac88019","apkName":"cl960","appId":"f32ac615171249418e3fc4b5bc2f4773","apkSize":"6155416","appIconUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/icon\/c940c121d9c04565a85e36fb5665fac3ic_launcher.png","packName":"com.chileaf.cl960.sample","versionName":"1.1.0","versionCode":"1","url":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/42e16b87eaa849bcaaf0f5fadcac6aab.apk"},{"apkMd5":"ba1ee533924eae5c408465e7cddcbda4","apkName":"逍遥助手","appId":"225a0a30994e45089dad1a270f8d85a1","apkSize":"4771451","appIconUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/icon\/6dc157c7ff1649448fb7084544d2e8f5icon.png","packName":"plus.H52FFB9A5","versionName":"1.0","versionCode":"100","url":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/203831945bb84d42b21decf1bf936815.apk"}]
2025-08-14 18:39:56.962 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule deleteAppList数组: [{"apkName":"Via","appId":"353795033bc0410dac6dee5cb8702b06","packName":"mark.via","versionName":"6.6.0","versionCode":"20250713"}]
2025-08-14 18:39:56.967 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=com.chileaf.cl960.sample, apkName=cl960
2025-08-14 18:39:56.972 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=plus.H52FFB9A5, apkName=逍遥助手
2025-08-14 18:39:56.977 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=mark.via, apkName=Via
2025-08-14 18:39:56.981 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后appList数量: 2
2025-08-14 18:39:56.985 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后deleteAppList数量: 1
2025-08-14 18:39:56.990 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleBaseStorage 处理规则: fb4a8c49e3854b00b51034df8d6cf113, 操作: A
2025-08-14 18:39:56.995 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 验证规则: ruleId=fb4a8c49e3854b00b51034df8d6cf113, ruleType=app_management, action=A
2025-08-14 18:39:56.999 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule appList数量: 2, deleteAppList数量: 1
2025-08-14 18:39:57.020 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 验证应用管理规则: action=A
2025-08-14 18:39:57.024 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 验证应用列表: appList=2, deleteAppList=1
2025-08-14 18:39:57.030 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 应用列表验证通过
2025-08-14 18:39:57.060 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析规则JSON: ruleId=937eccc7e4ff4fc0bc9eaf2788685515
2025-08-14 18:39:57.069 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule appList数组: [{"packName":"mark.via","apkName":"Via","versionName":"6.6.0","versionCode":"20250713","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/4d256c4123b346fd817537b491342204.apk","apkMd5":"0d1225260d03e10a8ffc8409369c442a","apkSize":2565110,"installBy":"0","isSystemApp":false,"priority":0}]
2025-08-14 18:39:57.073 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule deleteAppList数组: []
2025-08-14 18:39:57.078 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=mark.via, apkName=Via
2025-08-14 18:39:57.083 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后appList数量: 1
2025-08-14 18:39:57.087 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后deleteAppList数量: 0
2025-08-14 18:39:57.092 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析规则JSON: ruleId=01f42138486940dd8ba8a136d84e239d
2025-08-14 18:39:57.097 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule appList数组: []
2025-08-14 18:39:57.104 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule deleteAppList数组: [{"packName":"mark.via","apkName":"Via","versionName":"6.6.0","versionCode":"20250713","apkUrl":"","apkMd5":"","apkSize":0,"installBy":"0","isSystemApp":false,"priority":0}]
2025-08-14 18:39:57.108 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=mark.via, apkName=Via
2025-08-14 18:39:57.113 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后appList数量: 0
2025-08-14 18:39:57.117 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后deleteAppList数量: 1
2025-08-14 18:39:57.122 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析规则JSON: ruleId=e195e52efa414316818a8d4b21fa4e9c
2025-08-14 18:39:57.127 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule appList数组: []
2025-08-14 18:39:57.137 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule deleteAppList数组: [{"packName":"com.chileaf.cl960.sample","apkName":"cl960","versionName":"1.1.0","versionCode":"1","apkUrl":"","apkMd5":"","apkSize":0,"installBy":"0","isSystemApp":false,"priority":0},{"packName":"plus.H52FFB9A5","apkName":"逍遥助手","versionName":"1.0","versionCode":"100","apkUrl":"","apkMd5":"","apkSize":0,"installBy":"0","isSystemApp":false,"priority":0}]
2025-08-14 18:39:57.141 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=com.chileaf.cl960.sample, apkName=cl960
2025-08-14 18:39:57.146 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=plus.H52FFB9A5, apkName=逍遥助手
2025-08-14 18:39:57.151 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后appList数量: 0
2025-08-14 18:39:57.155 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后deleteAppList数量: 2
2025-08-14 18:39:57.160 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析规则JSON: ruleId=6eb6f82614c74c3bb1f5ca0e32ef787e
2025-08-14 18:39:57.171 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule appList数组: [{"packName":"mark.via","apkName":"Via","versionName":"6.6.0","versionCode":"20250713","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/4d256c4123b346fd817537b491342204.apk","apkMd5":"0d1225260d03e10a8ffc8409369c442a","apkSize":2565110,"installBy":"0","isSystemApp":false,"priority":0},{"packName":"com.chileaf.cl960.sample","apkName":"cl960","versionName":"1.1.0","versionCode":"1","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/42e16b87eaa849bcaaf0f5fadcac6aab.apk","apkMd5":"54cc3a371939423d4288d63e1ac88019","apkSize":6155416,"installBy":"0","isSystemApp":false,"priority":0}]
2025-08-14 18:39:57.176 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule deleteAppList数组: []
2025-08-14 18:39:57.181 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=mark.via, apkName=Via
2025-08-14 18:39:57.185 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=com.chileaf.cl960.sample, apkName=cl960
2025-08-14 18:39:57.190 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后appList数量: 2
2025-08-14 18:39:57.194 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后deleteAppList数量: 0
2025-08-14 18:39:57.199 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析规则JSON: ruleId=83d4a14ed05348efa59fbc626e5d8b19
2025-08-14 18:39:57.208 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule appList数组: [{"packName":"plus.H52FFB9A5","apkName":"逍遥助手","versionName":"1.0","versionCode":"100","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/203831945bb84d42b21decf1bf936815.apk","apkMd5":"ba1ee533924eae5c408465e7cddcbda4","apkSize":4771451,"installBy":"0","isSystemApp":false,"priority":0}]
2025-08-14 18:39:57.215 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule deleteAppList数组: [{"packName":"mark.via","apkName":"Via","versionName":"6.6.0","versionCode":"20250713","apkUrl":"","apkMd5":"","apkSize":0,"installBy":"0","isSystemApp":false,"priority":0}]
2025-08-14 18:39:57.220 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=plus.H52FFB9A5, apkName=逍遥助手
2025-08-14 18:39:57.225 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=mark.via, apkName=Via
2025-08-14 18:39:57.229 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后appList数量: 1
2025-08-14 18:39:57.234 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后deleteAppList数量: 1
2025-08-14 18:39:57.239 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析规则JSON: ruleId=168fee4bca5148a0be863080acad09ee
2025-08-14 18:39:57.251 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule appList数组: [{"packName":"mark.via","apkName":"Via","versionName":"6.6.0","versionCode":"20250713","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/4d256c4123b346fd817537b491342204.apk","apkMd5":"0d1225260d03e10a8ffc8409369c442a","apkSize":2565110,"installBy":"0","isSystemApp":false,"priority":0},{"packName":"com.lingjingnet.lazycat","apkName":"懒猫趣播","versionName":"1.0.1","versionCode":"10","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/f03e70448bf9461abf1804e9da65b36f.apk","apkMd5":"accb937c84dcd0d346da7f8fee7bf0ae","apkSize":13140733,"installBy":"0","isSystemApp":false,"priority":0}]
2025-08-14 18:39:57.261 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule deleteAppList数组: [{"packName":"com.chileaf.cl960.sample","apkName":"cl960","versionName":"1.1.0","versionCode":"1","apkUrl":"","apkMd5":"","apkSize":0,"installBy":"0","isSystemApp":false,"priority":0},{"packName":"plus.H52FFB9A5","apkName":"逍遥助手","versionName":"1.0","versionCode":"100","apkUrl":"","apkMd5":"","apkSize":0,"installBy":"0","isSystemApp":false,"priority":0}]
2025-08-14 18:39:57.266 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=mark.via, apkName=Via
2025-08-14 18:39:57.271 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=com.lingjingnet.lazycat, apkName=懒猫趣播
2025-08-14 18:39:57.276 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=com.chileaf.cl960.sample, apkName=cl960
2025-08-14 18:39:57.280 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=plus.H52FFB9A5, apkName=逍遥助手
2025-08-14 18:39:57.285 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后appList数量: 2
2025-08-14 18:39:57.289 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后deleteAppList数量: 2
2025-08-14 18:39:57.319 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析规则JSON: ruleId=937eccc7e4ff4fc0bc9eaf2788685515
2025-08-14 18:39:57.327 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule appList数组: [{"packName":"mark.via","apkName":"Via","versionName":"6.6.0","versionCode":"20250713","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/4d256c4123b346fd817537b491342204.apk","apkMd5":"0d1225260d03e10a8ffc8409369c442a","apkSize":2565110,"installBy":"0","isSystemApp":false,"priority":0}]
2025-08-14 18:39:57.332 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule deleteAppList数组: []
2025-08-14 18:39:57.337 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=mark.via, apkName=Via
2025-08-14 18:39:57.341 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后appList数量: 1
2025-08-14 18:39:57.346 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后deleteAppList数量: 0
2025-08-14 18:39:57.350 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析规则JSON: ruleId=01f42138486940dd8ba8a136d84e239d
2025-08-14 18:39:57.355 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule appList数组: []
2025-08-14 18:39:57.362 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule deleteAppList数组: [{"packName":"mark.via","apkName":"Via","versionName":"6.6.0","versionCode":"20250713","apkUrl":"","apkMd5":"","apkSize":0,"installBy":"0","isSystemApp":false,"priority":0}]
2025-08-14 18:39:57.366 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=mark.via, apkName=Via
2025-08-14 18:39:57.371 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后appList数量: 0
2025-08-14 18:39:57.376 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后deleteAppList数量: 1
2025-08-14 18:39:57.380 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析规则JSON: ruleId=e195e52efa414316818a8d4b21fa4e9c
2025-08-14 18:39:57.385 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule appList数组: []
2025-08-14 18:39:57.395 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule deleteAppList数组: [{"packName":"com.chileaf.cl960.sample","apkName":"cl960","versionName":"1.1.0","versionCode":"1","apkUrl":"","apkMd5":"","apkSize":0,"installBy":"0","isSystemApp":false,"priority":0},{"packName":"plus.H52FFB9A5","apkName":"逍遥助手","versionName":"1.0","versionCode":"100","apkUrl":"","apkMd5":"","apkSize":0,"installBy":"0","isSystemApp":false,"priority":0}]
2025-08-14 18:39:57.399 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=com.chileaf.cl960.sample, apkName=cl960
2025-08-14 18:39:57.404 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=plus.H52FFB9A5, apkName=逍遥助手
2025-08-14 18:39:57.409 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后appList数量: 0
2025-08-14 18:39:57.413 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后deleteAppList数量: 2
2025-08-14 18:39:57.418 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析规则JSON: ruleId=6eb6f82614c74c3bb1f5ca0e32ef787e
2025-08-14 18:39:57.430 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule appList数组: [{"packName":"mark.via","apkName":"Via","versionName":"6.6.0","versionCode":"20250713","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/4d256c4123b346fd817537b491342204.apk","apkMd5":"0d1225260d03e10a8ffc8409369c442a","apkSize":2565110,"installBy":"0","isSystemApp":false,"priority":0},{"packName":"com.chileaf.cl960.sample","apkName":"cl960","versionName":"1.1.0","versionCode":"1","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/42e16b87eaa849bcaaf0f5fadcac6aab.apk","apkMd5":"54cc3a371939423d4288d63e1ac88019","apkSize":6155416,"installBy":"0","isSystemApp":false,"priority":0}]
2025-08-14 18:39:57.434 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule deleteAppList数组: []
2025-08-14 18:39:57.439 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=mark.via, apkName=Via
2025-08-14 18:39:57.444 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=com.chileaf.cl960.sample, apkName=cl960
2025-08-14 18:39:57.449 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后appList数量: 2
2025-08-14 18:39:57.453 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后deleteAppList数量: 0
2025-08-14 18:39:57.458 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析规则JSON: ruleId=83d4a14ed05348efa59fbc626e5d8b19
2025-08-14 18:39:57.466 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule appList数组: [{"packName":"plus.H52FFB9A5","apkName":"逍遥助手","versionName":"1.0","versionCode":"100","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/203831945bb84d42b21decf1bf936815.apk","apkMd5":"ba1ee533924eae5c408465e7cddcbda4","apkSize":4771451,"installBy":"0","isSystemApp":false,"priority":0}]
2025-08-14 18:39:57.473 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule deleteAppList数组: [{"packName":"mark.via","apkName":"Via","versionName":"6.6.0","versionCode":"20250713","apkUrl":"","apkMd5":"","apkSize":0,"installBy":"0","isSystemApp":false,"priority":0}]
2025-08-14 18:39:57.478 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=plus.H52FFB9A5, apkName=逍遥助手
2025-08-14 18:39:57.482 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=mark.via, apkName=Via
2025-08-14 18:39:57.487 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后appList数量: 1
2025-08-14 18:39:57.491 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后deleteAppList数量: 1
2025-08-14 18:39:57.496 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析规则JSON: ruleId=168fee4bca5148a0be863080acad09ee
2025-08-14 18:39:57.508 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule appList数组: [{"packName":"mark.via","apkName":"Via","versionName":"6.6.0","versionCode":"20250713","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/4d256c4123b346fd817537b491342204.apk","apkMd5":"0d1225260d03e10a8ffc8409369c442a","apkSize":2565110,"installBy":"0","isSystemApp":false,"priority":0},{"packName":"com.lingjingnet.lazycat","apkName":"懒猫趣播","versionName":"1.0.1","versionCode":"10","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/f03e70448bf9461abf1804e9da65b36f.apk","apkMd5":"accb937c84dcd0d346da7f8fee7bf0ae","apkSize":13140733,"installBy":"0","isSystemApp":false,"priority":0}]
2025-08-14 18:39:57.518 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule deleteAppList数组: [{"packName":"com.chileaf.cl960.sample","apkName":"cl960","versionName":"1.1.0","versionCode":"1","apkUrl":"","apkMd5":"","apkSize":0,"installBy":"0","isSystemApp":false,"priority":0},{"packName":"plus.H52FFB9A5","apkName":"逍遥助手","versionName":"1.0","versionCode":"100","apkUrl":"","apkMd5":"","apkSize":0,"installBy":"0","isSystemApp":false,"priority":0}]
2025-08-14 18:39:57.522 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=mark.via, apkName=Via
2025-08-14 18:39:57.527 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=com.lingjingnet.lazycat, apkName=懒猫趣播
2025-08-14 18:39:57.532 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=com.chileaf.cl960.sample, apkName=cl960
2025-08-14 18:39:57.536 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=plus.H52FFB9A5, apkName=逍遥助手
2025-08-14 18:39:57.541 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后appList数量: 2
2025-08-14 18:39:57.545 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后deleteAppList数量: 2
2025-08-14 18:39:57.626 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleBaseStorage 保存 7 个规则到存储
2025-08-14 18:39:57.631 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleBaseStorage Rule fb4a8c49e3854b00b51034df8d6cf113 添加成功
2025-08-14 18:39:57.635 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleBaseStorage 触发规则执行: fb4a8c49e3854b00b51034df8d6cf113
2025-08-14 18:39:57.641 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleHandler 规则处理成功: fb4a8c49e3854b00b51034df8d6cf113
2025-08-14 18:39:57.641 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 RuleBaseStorage 设备空闲，立即执行规则: fb4a8c49e3854b00b51034df8d6cf113
2025-08-14 18:39:57.646 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 RuleBaseManager 直接执行规则: fb4a8c49e3854b00b51034df8d6cf113
2025-08-14 18:39:57.647 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleHandler 规则列表处理完成: 处理完成 - 成功: 1, 失败: 0, 忽略: 0
2025-08-14 18:39:57.653 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleHandler 上报规则处理结果: 0 - 处理完成 - 成功: 1, 失败: 0, 忽略: 0
2025-08-14 18:39:57.653 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 RuleBaseManager 处理规则: fb4a8c49e3854b00b51034df8d6cf113, 操作: A
2025-08-14 18:39:57.660 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 C0107 主动式上送: rulebase_update (主动: 54)
2025-08-14 18:39:57.661 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析规则JSON: ruleId=fb4a8c49e3854b00b51034df8d6cf113
2025-08-14 18:39:57.677 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 Rule appList数组: [{"packName":"com.chileaf.cl960.sample","apkName":"cl960","versionName":"1.1.0","versionCode":"1","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/42e16b87eaa849bcaaf0f5fadcac6aab.apk","apkMd5":"54cc3a371939423d4288d63e1ac88019","apkSize":6155416,"installBy":"0","isSystemApp":false,"priority":0},{"packName":"plus.H52FFB9A5","apkName":"逍遥助手","versionName":"1.0","versionCode":"100","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/203831945bb84d42b21decf1bf936815.apk","apkMd5":"ba1ee533924eae5c408465e7cddcbda4","apkSize":4771451,"installBy":"0","isSystemApp":false,"priority":0}]
2025-08-14 18:39:57.684 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 添加规则结果: ruleId=batch_rules
2025-08-14 18:39:57.686 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 Rule deleteAppList数组: [{"packName":"mark.via","apkName":"Via","versionName":"6.6.0","versionCode":"20250713","apkUrl":"","apkMd5":"","apkSize":0,"installBy":"0","isSystemApp":false,"priority":0}]
2025-08-14 18:39:57.691 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=com.chileaf.cl960.sample, apkName=cl960
2025-08-14 18:39:57.696 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=plus.H52FFB9A5, apkName=逍遥助手
2025-08-14 18:39:57.701 19136-19207 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0107","serialNo":"01354090202503050399","request_time":"1755167997677","request_id":"1755167997677C0107","version":"1","data":{"ruleId":"batch_rules","taskResult":"0"},"myVersionName":"1.0.08.20250814.DSPREAD.MDM.SERVICE","terminalDate":"20250814183957","org_request_id":"1755167995721ST005","org_request_time":"1755167995721"}
2025-08-14 18:39:57.702 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=mark.via, apkName=Via
2025-08-14 18:39:57.705 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 规则结果实时上送: ruleId=batch_rules, result=0 (1)
2025-08-14 18:39:57.706 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后appList数量: 2
2025-08-14 18:39:57.711 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后deleteAppList数量: 1
2025-08-14 18:39:57.715 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 RuleBaseStorage 处理规则: fb4a8c49e3854b00b51034df8d6cf113, 操作: A
2025-08-14 18:39:57.720 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 Rule 验证规则: ruleId=fb4a8c49e3854b00b51034df8d6cf113, ruleType=app_management, action=A
2025-08-14 18:39:57.725 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 Rule appList数量: 2, deleteAppList数量: 1
2025-08-14 18:39:57.746 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 Rule 验证应用管理规则: action=A
2025-08-14 18:39:57.751 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 Rule 验证应用列表: appList=2, deleteAppList=1
2025-08-14 18:39:57.758 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 Rule 应用列表验证通过
2025-08-14 18:39:57.793 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析规则JSON: ruleId=937eccc7e4ff4fc0bc9eaf2788685515
2025-08-14 18:39:57.800 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 Rule appList数组: [{"packName":"mark.via","apkName":"Via","versionName":"6.6.0","versionCode":"20250713","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/4d256c4123b346fd817537b491342204.apk","apkMd5":"0d1225260d03e10a8ffc8409369c442a","apkSize":2565110,"installBy":"0","isSystemApp":false,"priority":0}]
2025-08-14 18:39:57.805 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 Rule deleteAppList数组: []
2025-08-14 18:39:57.809 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=mark.via, apkName=Via
2025-08-14 18:39:57.814 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后appList数量: 1
2025-08-14 18:39:57.819 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后deleteAppList数量: 0
2025-08-14 18:39:57.823 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析规则JSON: ruleId=01f42138486940dd8ba8a136d84e239d
2025-08-14 18:39:57.828 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 Rule appList数组: []
2025-08-14 18:39:57.835 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 Rule deleteAppList数组: [{"packName":"mark.via","apkName":"Via","versionName":"6.6.0","versionCode":"20250713","apkUrl":"","apkMd5":"","apkSize":0,"installBy":"0","isSystemApp":false,"priority":0}]
2025-08-14 18:39:57.839 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=mark.via, apkName=Via
2025-08-14 18:39:57.844 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后appList数量: 0
2025-08-14 18:39:57.848 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后deleteAppList数量: 1
2025-08-14 18:39:57.853 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析规则JSON: ruleId=e195e52efa414316818a8d4b21fa4e9c
2025-08-14 18:39:57.857 19136-19207 Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"request_time":"1755167996830","org_request_time":"1755167997677","org_request_id":"1755167997677C0107","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755167996830S0000","serialNo":"01354090202503050399"}
2025-08-14 18:39:57.859 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 Rule appList数组: []
2025-08-14 18:39:57.866 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 收到响应消息: transCode=S0000, orgRequestId=1755167997677C0107, state=0, remark=
2025-08-14 18:39:57.870 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 Rule deleteAppList数组: [{"packName":"com.chileaf.cl960.sample","apkName":"cl960","versionName":"1.1.0","versionCode":"1","apkUrl":"","apkMd5":"","apkSize":0,"installBy":"0","isSystemApp":false,"priority":0},{"packName":"plus.H52FFB9A5","apkName":"逍遥助手","versionName":"1.0","versionCode":"100","apkUrl":"","apkMd5":"","apkSize":0,"installBy":"0","isSystemApp":false,"priority":0}]
2025-08-14 18:39:57.871 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 处理规则结果响应: state=0
2025-08-14 18:39:57.875 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=com.chileaf.cl960.sample, apkName=cl960
2025-08-14 18:39:57.876 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 规则结果上传成功
2025-08-14 18:39:57.880 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=plus.H52FFB9A5, apkName=逍遥助手
2025-08-14 18:39:57.885 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后appList数量: 0
2025-08-14 18:39:57.889 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后deleteAppList数量: 2
2025-08-14 18:39:57.894 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析规则JSON: ruleId=6eb6f82614c74c3bb1f5ca0e32ef787e
2025-08-14 18:39:57.905 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 Rule appList数组: [{"packName":"mark.via","apkName":"Via","versionName":"6.6.0","versionCode":"20250713","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/4d256c4123b346fd817537b491342204.apk","apkMd5":"0d1225260d03e10a8ffc8409369c442a","apkSize":2565110,"installBy":"0","isSystemApp":false,"priority":0},{"packName":"com.chileaf.cl960.sample","apkName":"cl960","versionName":"1.1.0","versionCode":"1","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/42e16b87eaa849bcaaf0f5fadcac6aab.apk","apkMd5":"54cc3a371939423d4288d63e1ac88019","apkSize":6155416,"installBy":"0","isSystemApp":false,"priority":0}]
2025-08-14 18:39:57.909 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 Rule deleteAppList数组: []
2025-08-14 18:39:57.914 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=mark.via, apkName=Via
2025-08-14 18:39:57.919 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=com.chileaf.cl960.sample, apkName=cl960
2025-08-14 18:39:57.923 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后appList数量: 2
2025-08-14 18:39:57.928 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后deleteAppList数量: 0
2025-08-14 18:39:57.932 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析规则JSON: ruleId=83d4a14ed05348efa59fbc626e5d8b19
2025-08-14 18:39:57.941 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 Rule appList数组: [{"packName":"plus.H52FFB9A5","apkName":"逍遥助手","versionName":"1.0","versionCode":"100","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/203831945bb84d42b21decf1bf936815.apk","apkMd5":"ba1ee533924eae5c408465e7cddcbda4","apkSize":4771451,"installBy":"0","isSystemApp":false,"priority":0}]
2025-08-14 18:39:57.948 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 Rule deleteAppList数组: [{"packName":"mark.via","apkName":"Via","versionName":"6.6.0","versionCode":"20250713","apkUrl":"","apkMd5":"","apkSize":0,"installBy":"0","isSystemApp":false,"priority":0}]
2025-08-14 18:39:57.952 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=plus.H52FFB9A5, apkName=逍遥助手
2025-08-14 18:39:57.957 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=mark.via, apkName=Via
2025-08-14 18:39:57.962 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后appList数量: 1
2025-08-14 18:39:57.966 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后deleteAppList数量: 1
2025-08-14 18:39:57.971 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析规则JSON: ruleId=168fee4bca5148a0be863080acad09ee
2025-08-14 18:39:57.983 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 Rule appList数组: [{"packName":"mark.via","apkName":"Via","versionName":"6.6.0","versionCode":"20250713","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/4d256c4123b346fd817537b491342204.apk","apkMd5":"0d1225260d03e10a8ffc8409369c442a","apkSize":2565110,"installBy":"0","isSystemApp":false,"priority":0},{"packName":"com.lingjingnet.lazycat","apkName":"懒猫趣播","versionName":"1.0.1","versionCode":"10","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/f03e70448bf9461abf1804e9da65b36f.apk","apkMd5":"accb937c84dcd0d346da7f8fee7bf0ae","apkSize":13140733,"installBy":"0","isSystemApp":false,"priority":0}]
2025-08-14 18:39:57.992 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 Rule deleteAppList数组: [{"packName":"com.chileaf.cl960.sample","apkName":"cl960","versionName":"1.1.0","versionCode":"1","apkUrl":"","apkMd5":"","apkSize":0,"installBy":"0","isSystemApp":false,"priority":0},{"packName":"plus.H52FFB9A5","apkName":"逍遥助手","versionName":"1.0","versionCode":"100","apkUrl":"","apkMd5":"","apkSize":0,"installBy":"0","isSystemApp":false,"priority":0}]
2025-08-14 18:39:57.997 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=mark.via, apkName=Via
2025-08-14 18:39:58.001 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=com.lingjingnet.lazycat, apkName=懒猫趣播
2025-08-14 18:39:58.006 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=com.chileaf.cl960.sample, apkName=cl960
2025-08-14 18:39:58.011 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=plus.H52FFB9A5, apkName=逍遥助手
2025-08-14 18:39:58.015 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后appList数量: 2
2025-08-14 18:39:58.020 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后deleteAppList数量: 2
2025-08-14 18:39:58.024 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析规则JSON: ruleId=fb4a8c49e3854b00b51034df8d6cf113
2025-08-14 18:39:58.036 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 Rule appList数组: [{"packName":"com.chileaf.cl960.sample","apkName":"cl960","versionName":"1.1.0","versionCode":"1","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/42e16b87eaa849bcaaf0f5fadcac6aab.apk","apkMd5":"54cc3a371939423d4288d63e1ac88019","apkSize":6155416,"installBy":"0","isSystemApp":false,"priority":0},{"packName":"plus.H52FFB9A5","apkName":"逍遥助手","versionName":"1.0","versionCode":"100","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/203831945bb84d42b21decf1bf936815.apk","apkMd5":"ba1ee533924eae5c408465e7cddcbda4","apkSize":4771451,"installBy":"0","isSystemApp":false,"priority":0}]
2025-08-14 18:39:58.043 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 Rule deleteAppList数组: [{"packName":"mark.via","apkName":"Via","versionName":"6.6.0","versionCode":"20250713","apkUrl":"","apkMd5":"","apkSize":0,"installBy":"0","isSystemApp":false,"priority":0}]
2025-08-14 18:39:58.048 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=com.chileaf.cl960.sample, apkName=cl960
2025-08-14 18:39:58.052 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=plus.H52FFB9A5, apkName=逍遥助手
2025-08-14 18:39:58.057 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=mark.via, apkName=Via
2025-08-14 18:39:58.062 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后appList数量: 2
2025-08-14 18:39:58.066 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后deleteAppList数量: 1
2025-08-14 18:39:58.071 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 RuleBaseStorage Rule fb4a8c49e3854b00b51034df8d6cf113 已存在，忽略Add操作
2025-08-14 18:39:58.076 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 RuleStateMachine 规则初始化成功: fb4a8c49e3854b00b51034df8d6cf113 -> RuleState(code=todo, description=等待执行)
2025-08-14 18:39:58.081 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 RuleStateMachine 规则已存在: fb4a8c49e3854b00b51034df8d6cf113, 当前状态: RuleState(code=todo, description=等待执行)
2025-08-14 18:39:58.085 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 创建应用状态: packName=com.chileaf.cl960.sample, apkName=cl960
2025-08-14 18:39:58.090 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 创建应用状态: packName=plus.H52FFB9A5, apkName=逍遥助手
2025-08-14 18:39:58.095 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 初始化规则应用状态: fb4a8c49e3854b00b51034df8d6cf113, 应用数量: 3
2025-08-14 18:39:58.100 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 RuleStateMonitor 收到状态变化: fb4a8c49e3854b00b51034df8d6cf113, todo -> R01
2025-08-14 18:39:58.105 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 RuleStateMonitor 状态变化已记录: fb4a8c49e3854b00b51034df8d6cf113, R01
2025-08-14 18:39:58.109 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 RuleStateMachine 状态转换成功: fb4a8c49e3854b00b51034df8d6cf113, todo -> R01
2025-08-14 18:39:58.114 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 RuleStateMonitor 收到状态变化: fb4a8c49e3854b00b51034df8d6cf113, R01 -> R02
2025-08-14 18:39:58.119 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 RuleStateMonitor 状态变化已记录: fb4a8c49e3854b00b51034df8d6cf113, R02
2025-08-14 18:39:58.123 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 RuleStateMachine 状态转换成功: fb4a8c49e3854b00b51034df8d6cf113, R01 -> R02
2025-08-14 18:39:58.128 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 开始执行规则: fb4a8c49e3854b00b51034df8d6cf113
2025-08-14 18:39:58.132 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 执行卸载应用，数量: 1
2025-08-14 18:39:58.137 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 开始卸载应用，数量: 1
2025-08-14 18:39:58.141 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 卸载应用: mark.via
2025-08-14 18:39:58.147 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 更新应用状态: fb4a8c49e3854b00b51034df8d6cf113, mark.via -> D01
2025-08-14 18:39:58.152 19136-19165 WebSocket               com.dspread.mdm.service              I  🔧 C0107 主动式上送: rulebase_update (主动: 55)
2025-08-14 18:39:58.170 19136-19165 WebSocket               com.dspread.mdm.service              I  🔧 添加规则结果: ruleId=fb4a8c49e3854b00b51034df8d6cf113
2025-08-14 18:39:58.205 19136-19165 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0107","serialNo":"01354090202503050399","request_time":"1755167998165","request_id":"1755167998165C0107","version":"1","data":{"ruleId":"fb4a8c49e3854b00b51034df8d6cf113","taskResult":"R02","failedApkList":[{"packName":"com.chileaf.cl960.sample","apkName":"cl960","versionName":"1.1.0","versionCode":"1","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/42e16b87eaa849bcaaf0f5fadcac6aab.apk","apkMd5":"54cc3a371939423d4288d63e1ac88019","apkSize":6155416,"installBy":"0","isSystemApp":false,"priority":0,"result":"W01","taskType":"01"},{"packName":"plus.H52FFB9A5","apkName":"逍遥助手","versionName":"1.0","versionCode":"100","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/203831945bb84d42b21decf1bf936815.apk","apkMd5":"ba1ee533924eae5c408465e7cddcbda4","apkSize":4771451,"installBy":"0","isSystemApp":false,"priority":0,"result":"W01","taskType":"01"},{"packName":"mark.via","apkName":"Via","versionName":"6.6.0","versionCode":"20250713","apkUrl":"","apkMd5":"","apkSize":0,"installBy":"0","isSystemApp":false,"priority":0,"result":"D01","taskType":"02"}]},"myVersionName":"1.0.08.20250814.DSPREAD.MDM.SERVICE","terminalDate":"20250814183958"}
2025-08-14 18:39:58.209 19136-19165 WebSocket               com.dspread.mdm.service              I  🔧 规则结果实时上送: ruleId=fb4a8c49e3854b00b51034df8d6cf113, result=R02 (1)
2025-08-14 18:39:58.214 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 上报规则执行中状态: fb4a8c49e3854b00b51034df8d6cf113, 应用数量: 3
2025-08-14 18:39:58.219 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 注册卸载回调: mark.via (规则ID: fb4a8c49e3854b00b51034df8d6cf113)
2025-08-14 18:39:58.223 19136-19165 AppManager              com.dspread.mdm.service              I  ℹ️ Uninstalling app via PackageInstaller: mark.via
2025-08-14 18:39:58.228 19136-19165 AppManager              com.dspread.mdm.service              I  ℹ️ Deleting package via PackageInstaller: mark.via
2025-08-14 18:39:58.236 19136-19165 AppManager              com.dspread.mdm.service              I  ℹ️ PackageInstaller uninstall initiated
2025-08-14 18:39:58.241 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 执行安装应用，数量: 2
2025-08-14 18:39:58.246 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 开始安装应用，数量: 2
2025-08-14 18:39:58.251 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 安装应用: com.chileaf.cl960.sample, URL: https://siot-bucket01.s3.ap-northeast-1.amazonaws.com/app/42e16b87eaa849bcaaf0f5fadcac6aab.apk
2025-08-14 18:39:58.256 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 检查应用安装状态: com.chileaf.cl960.sample
2025-08-14 18:39:58.313 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 应用是否已安装: false
2025-08-14 18:39:58.318 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 应用未安装，需要安装
2025-08-14 18:39:58.321 19136-19207 Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"request_time":"1755167997324","org_request_time":"1755167998165","org_request_id":"1755167998165C0107","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755167997324S0000","serialNo":"01354090202503050399"}
2025-08-14 18:39:58.323 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 更新应用状态: fb4a8c49e3854b00b51034df8d6cf113, com.chileaf.cl960.sample -> A01
2025-08-14 18:39:58.328 19136-19165 WebSocket               com.dspread.mdm.service              I  🔧 C0107 主动式上送: rulebase_update (主动: 56)
2025-08-14 18:39:58.328 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 收到响应消息: transCode=S0000, orgRequestId=1755167998165C0107, state=0, remark=
2025-08-14 18:39:58.334 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 处理规则结果响应: state=0
2025-08-14 18:39:58.339 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 规则结果上传成功
2025-08-14 18:39:58.391 19136-19165 WebSocket               com.dspread.mdm.service              I  🔧 添加规则结果: ruleId=fb4a8c49e3854b00b51034df8d6cf113
2025-08-14 18:39:58.432 19136-19165 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0107","serialNo":"01354090202503050399","request_time":"1755167998341","request_id":"1755167998341C0107","version":"1","data":{"ruleId":"fb4a8c49e3854b00b51034df8d6cf113","taskResult":"R02","failedApkList":[{"packName":"com.chileaf.cl960.sample","apkName":"cl960","versionName":"1.1.0","versionCode":"1","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/42e16b87eaa849bcaaf0f5fadcac6aab.apk","apkMd5":"54cc3a371939423d4288d63e1ac88019","apkSize":6155416,"installBy":"0","isSystemApp":false,"priority":0,"result":"A01","taskType":"01"},{"packName":"plus.H52FFB9A5","apkName":"逍遥助手","versionName":"1.0","versionCode":"100","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/203831945bb84d42b21decf1bf936815.apk","apkMd5":"ba1ee533924eae5c408465e7cddcbda4","apkSize":4771451,"installBy":"0","isSystemApp":false,"priority":0,"result":"W01","taskType":"01"},{"packName":"mark.via","apkName":"Via","versionName":"6.6.0","versionCode":"20250713","apkUrl":"","apkMd5":"","apkSize":0,"installBy":"0","isSystemApp":false,"priority":0,"result":"D01","taskType":"02"}]},"myVersionName":"1.0.08.20250814.DSPREAD.MDM.SERVICE","terminalDate":"20250814183958"}
2025-08-14 18:39:58.437 19136-19165 WebSocket               com.dspread.mdm.service              I  🔧 规则结果实时上送: ruleId=fb4a8c49e3854b00b51034df8d6cf113, result=R02 (1)
2025-08-14 18:39:58.442 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 上报规则执行中状态: fb4a8c49e3854b00b51034df8d6cf113, 应用数量: 3
2025-08-14 18:39:58.449 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 安装应用: plus.H52FFB9A5, URL: https://siot-bucket01.s3.ap-northeast-1.amazonaws.com/app/203831945bb84d42b21decf1bf936815.apk
2025-08-14 18:39:58.455 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 检查应用安装状态: plus.H52FFB9A5
2025-08-14 18:39:58.460 19136-19534 HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 尝试下载，第1次
2025-08-14 18:39:58.461 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 应用是否已安装: false
2025-08-14 18:39:58.465 19136-19534 HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 开始下载
2025-08-14 18:39:58.465 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 应用未安装，需要安装
2025-08-14 18:39:58.473 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 更新应用状态: fb4a8c49e3854b00b51034df8d6cf113, plus.H52FFB9A5 -> A01
2025-08-14 18:39:58.479 19136-19165 WebSocket               com.dspread.mdm.service              I  🔧 C0107 主动式上送: rulebase_update (主动: 57)
2025-08-14 18:39:58.494 19136-19136 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 收到广播: android.intent.action.PACKAGE_REMOVED
2025-08-14 18:39:58.500 19136-19136 Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 收到包更新广播: android.intent.action.PACKAGE_REMOVED, 包名: mark.via
2025-08-14 18:39:58.504 19136-19534 HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader URL: https://siot-bucket01.s3.ap-northeast-1.amazonaws.com/app/42e16b87eaa849bcaaf0f5fadcac6aab.apk
2025-08-14 18:39:58.512 19136-19165 WebSocket               com.dspread.mdm.service              I  🔧 添加规则结果: ruleId=fb4a8c49e3854b00b51034df8d6cf113
2025-08-14 18:39:58.516 19136-19136 Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 应用卸载: mark.via
2025-08-14 18:39:58.527 19136-19534 HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 路径: /data/user/0/com.dspread.mdm.service/files/downloads/rule_fb4a8c49e3854b00b51034df8d6cf113_com.chileaf.cl960.sample.apk
2025-08-14 18:39:58.536 19136-19136 Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 检查WebSocket状态: packageName=mark.via, action=UNINSTALL
2025-08-14 18:39:58.553 19136-19136 Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 通知rulebase处理应用卸载: mark.via
2025-08-14 18:39:58.557 19136-19534 HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 本地文件不存在，创建文件
2025-08-14 18:39:58.558 19136-19207 Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"request_time":"1755167997546","org_request_time":"1755167998341","org_request_id":"1755167998341C0107","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755167997546S0000","serialNo":"01354090202503050399"}
2025-08-14 18:39:58.564 19136-19165 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0107","serialNo":"01354090202503050399","request_time":"1755167998504","request_id":"1755167998504C0107","version":"1","data":{"ruleId":"fb4a8c49e3854b00b51034df8d6cf113","taskResult":"R02","failedApkList":[{"packName":"com.chileaf.cl960.sample","apkName":"cl960","versionName":"1.1.0","versionCode":"1","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/42e16b87eaa849bcaaf0f5fadcac6aab.apk","apkMd5":"54cc3a371939423d4288d63e1ac88019","apkSize":6155416,"installBy":"0","isSystemApp":false,"priority":0,"result":"A01","taskType":"01"},{"packName":"plus.H52FFB9A5","apkName":"逍遥助手","versionName":"1.0","versionCode":"100","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/203831945bb84d42b21decf1bf936815.apk","apkMd5":"ba1ee533924eae5c408465e7cddcbda4","apkSize":4771451,"installBy":"0","isSystemApp":false,"priority":0,"result":"A01","taskType":"01"},{"packName":"mark.via","apkName":"Via","versionName":"6.6.0","versionCode":"20250713","apkUrl":"","apkMd5":"","apkSize":0,"installBy":"0","isSystemApp":false,"priority":0,"result":"D01","taskType":"02"}]},"myVersionName":"1.0.08.20250814.DSPREAD.MDM.SERVICE","terminalDate":"20250814183958"}
2025-08-14 18:39:58.566 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 收到响应消息: transCode=S0000, orgRequestId=1755167998341C0107, state=0, remark=
2025-08-14 18:39:58.574 19136-19534 HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader allFileLength=6155416, 需要从服务器获取文件大小
2025-08-14 18:39:58.575 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 收到系统广播，应用已卸载: mark.via
2025-08-14 18:39:58.575 19136-19165 WebSocket               com.dspread.mdm.service              I  🔧 规则结果实时上送: ruleId=fb4a8c49e3854b00b51034df8d6cf113, result=R02 (1)
2025-08-14 18:39:58.579 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 处理规则结果响应: state=0
2025-08-14 18:39:58.580 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 上报规则执行中状态: fb4a8c49e3854b00b51034df8d6cf113, 应用数量: 3
2025-08-14 18:39:58.583 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 规则结果上传成功
2025-08-14 18:39:58.592 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 找到匹配的卸载回调，触发: mark.via
2025-08-14 18:39:58.592 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 规则执行启动成功: fb4a8c49e3854b00b51034df8d6cf113
2025-08-14 18:39:58.601 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 RuleBaseManager 规则添加成功: fb4a8c49e3854b00b51034df8d6cf113
2025-08-14 18:39:58.603 19136-19544 HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 尝试下载，第1次
2025-08-14 18:39:58.606 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 卸载回调被触发: pkg=mark.via, returnCode=1, error=
2025-08-14 18:39:58.611 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 应用卸载启动成功: mark.via
2025-08-14 18:39:58.617 19136-19544 HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 开始下载
2025-08-14 18:39:58.622 19136-19544 HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader URL: https://siot-bucket01.s3.ap-northeast-1.amazonaws.com/app/203831945bb84d42b21decf1bf936815.apk
2025-08-14 18:39:58.626 19136-19544 HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 路径: /data/user/0/com.dspread.mdm.service/files/downloads/rule_fb4a8c49e3854b00b51034df8d6cf113_plus.H52FFB9A5.apk
2025-08-14 18:39:58.632 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 从广播更新应用状态: fb4a8c49e3854b00b51034df8d6cf113, mark.via -> D02
2025-08-14 18:39:58.637 19136-19544 HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 本地文件不存在，创建文件
2025-08-14 18:39:58.643 19136-19136 WebSocket               com.dspread.mdm.service              I  🔧 C0107 主动式上送: rulebase_update (主动: 58)
2025-08-14 18:39:58.645 19136-19544 HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader allFileLength=4771451, 需要从服务器获取文件大小
2025-08-14 18:39:58.666 19136-19136 WebSocket               com.dspread.mdm.service              I  🔧 添加规则结果: ruleId=fb4a8c49e3854b00b51034df8d6cf113
2025-08-14 18:39:58.704 19136-19136 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0107","serialNo":"01354090202503050399","request_time":"1755167998660","request_id":"1755167998660C0107","version":"1","data":{"ruleId":"fb4a8c49e3854b00b51034df8d6cf113","taskResult":"R02","failedApkList":[{"packName":"com.chileaf.cl960.sample","apkName":"cl960","versionName":"1.1.0","versionCode":"1","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/42e16b87eaa849bcaaf0f5fadcac6aab.apk","apkMd5":"54cc3a371939423d4288d63e1ac88019","apkSize":6155416,"installBy":"0","isSystemApp":false,"priority":0,"result":"A01","taskType":"01"},{"packName":"plus.H52FFB9A5","apkName":"逍遥助手","versionName":"1.0","versionCode":"100","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/203831945bb84d42b21decf1bf936815.apk","apkMd5":"ba1ee533924eae5c408465e7cddcbda4","apkSize":4771451,"installBy":"0","isSystemApp":false,"priority":0,"result":"A01","taskType":"01"},{"packName":"mark.via","apkName":"Via","versionName":"6.6.0","versionCode":"20250713","apkUrl":"","apkMd5":"","apkSize":0,"installBy":"0","isSystemApp":false,"priority":0,"result":"D02","taskType":"02"}]},"myVersionName":"1.0.08.20250814.DSPREAD.MDM.SERVICE","terminalDate":"20250814183958"}
2025-08-14 18:39:58.709 19136-19136 WebSocket               com.dspread.mdm.service              I  🔧 规则结果实时上送: ruleId=fb4a8c49e3854b00b51034df8d6cf113, result=R02 (1)
2025-08-14 18:39:58.714 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 上报规则执行中状态: fb4a8c49e3854b00b51034df8d6cf113, 应用数量: 3
2025-08-14 18:39:58.719 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 开始检查规则完成状态: fb4a8c49e3854b00b51034df8d6cf113, 总应用数: 3
2025-08-14 18:39:58.724 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 检查应用状态: com.chileaf.cl960.sample -> A01
2025-08-14 18:39:58.726 19136-19534 TrafficStats            com.dspread.mdm.service              D  tagSocket(98) with statsTag=0xffffffff, statsUid=-1
2025-08-14 18:39:58.731 19136-19544 TrafficStats            com.dspread.mdm.service              D  tagSocket(102) with statsTag=0xffffffff, statsUid=-1
2025-08-14 18:39:58.735 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 发现未完成状态: com.chileaf.cl960.sample -> A01
2025-08-14 18:39:58.740 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 规则完成检查结果: fb4a8c49e3854b00b51034df8d6cf113, 全部完成: false, 完成数: 0/3, 有失败: false
2025-08-14 18:39:58.745 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 规则未完成: fb4a8c49e3854b00b51034df8d6cf113, 完成数: 0/3
2025-08-14 18:39:58.750 19136-19136 Platform                com.dspread.mdm.service              D  🔧 重新收集应用信息（强制刷新）
2025-08-14 18:39:58.759 19136-19207 Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"request_time":"1755167997668","org_request_time":"1755167998504","org_request_id":"1755167998504C0107","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755167997668S0000","serialNo":"01354090202503050399"}
2025-08-14 18:39:58.772 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 收到响应消息: transCode=S0000, orgRequestId=1755167998504C0107, state=0, remark=
2025-08-14 18:39:58.779 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 处理规则结果响应: state=0
2025-08-14 18:39:58.791 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 规则结果上传成功
2025-08-14 18:39:58.855 19136-19136 AppManager              com.dspread.mdm.service              I  ℹ️ 应用信息获取完成: 总数131(系统129/用户2) 返回2个
2025-08-14 18:39:58.859 19136-19207 Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"request_time":"1755167997818","org_request_time":"1755167998660","org_request_id":"1755167998660C0107","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755167997818S0000","serialNo":"01354090202503050399"}
2025-08-14 18:39:58.867 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 收到响应消息: transCode=S0000, orgRequestId=1755167998660C0107, state=0, remark=
2025-08-14 18:39:58.872 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 处理规则结果响应: state=0
2025-08-14 18:39:58.872 19136-19136 Platform                com.dspread.mdm.service              D  🔧 应用信息: 2 个应用
2025-08-14 18:39:58.876 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 规则结果上传成功
2025-08-14 18:39:58.877 19136-19136 Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 应用信息缓存刷新完成
2025-08-14 18:39:58.882 19136-19136 ContextImpl             com.dspread.mdm.service              W  Calling a method in the system process without a qualified user: android.app.ContextImpl.sendBroadcast:1220 android.content.ContextWrapper.sendBroadcast:504 com.dspread.mdm.service.broadcast.core.BroadcastSender.sendBroadcast:99 com.dspread.mdm.service.broadcast.core.BroadcastSender.sendBroadcast:23 com.dspread.mdm.service.broadcast.handlers.system.PackageUpdateEventHandlerImpl.sendAppRefreshBroadcast:491 
2025-08-14 18:39:58.884 19136-19136 Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 发送应用刷新广播
2025-08-14 18:39:58.890 19136-19136 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 收到广播: android.intent.action.PACKAGE_REMOVED
2025-08-14 18:39:58.895 19136-19136 Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 收到包更新广播: android.intent.action.PACKAGE_REMOVED, 包名: mark.via
2025-08-14 18:39:58.900 19136-19136 Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 应用卸载: mark.via
2025-08-14 18:39:58.905 19136-19136 Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 检查WebSocket状态: packageName=mark.via, action=UNINSTALL
2025-08-14 18:39:58.910 19136-19136 Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 通知rulebase处理应用卸载: mark.via
2025-08-14 18:39:58.915 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 收到系统广播，应用已卸载: mark.via
2025-08-14 18:39:58.920 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 未找到匹配的卸载回调: mark.via
2025-08-14 18:39:58.925 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 从广播更新应用状态: fb4a8c49e3854b00b51034df8d6cf113, mark.via -> D02
2025-08-14 18:39:59.052 19136-19136 WebSocket               com.dspread.mdm.service              I  🔧 C0107 主动式上送: rulebase_update (主动: 59)
2025-08-14 18:39:59.078 19136-19136 WebSocket               com.dspread.mdm.service              I  🔧 添加规则结果: ruleId=fb4a8c49e3854b00b51034df8d6cf113
2025-08-14 18:39:59.118 19136-19136 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0107","serialNo":"01354090202503050399","request_time":"1755167999070","request_id":"1755167999070C0107","version":"1","data":{"ruleId":"fb4a8c49e3854b00b51034df8d6cf113","taskResult":"R02","failedApkList":[{"packName":"com.chileaf.cl960.sample","apkName":"cl960","versionName":"1.1.0","versionCode":"1","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/42e16b87eaa849bcaaf0f5fadcac6aab.apk","apkMd5":"54cc3a371939423d4288d63e1ac88019","apkSize":6155416,"installBy":"0","isSystemApp":false,"priority":0,"result":"A01","taskType":"01"},{"packName":"plus.H52FFB9A5","apkName":"逍遥助手","versionName":"1.0","versionCode":"100","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/203831945bb84d42b21decf1bf936815.apk","apkMd5":"ba1ee533924eae5c408465e7cddcbda4","apkSize":4771451,"installBy":"0","isSystemApp":false,"priority":0,"result":"A01","taskType":"01"},{"packName":"mark.via","apkName":"Via","versionName":"6.6.0","versionCode":"20250713","apkUrl":"","apkMd5":"","apkSize":0,"installBy":"0","isSystemApp":false,"priority":0,"result":"D02","taskType":"02"}]},"myVersionName":"1.0.08.20250814.DSPREAD.MDM.SERVICE","terminalDate":"20250814183959"}
2025-08-14 18:39:59.124 19136-19136 WebSocket               com.dspread.mdm.service              I  🔧 规则结果实时上送: ruleId=fb4a8c49e3854b00b51034df8d6cf113, result=R02 (1)
2025-08-14 18:39:59.131 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 上报规则执行中状态: fb4a8c49e3854b00b51034df8d6cf113, 应用数量: 3
2025-08-14 18:39:59.136 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 开始检查规则完成状态: fb4a8c49e3854b00b51034df8d6cf113, 总应用数: 3
2025-08-14 18:39:59.141 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 检查应用状态: com.chileaf.cl960.sample -> A01
2025-08-14 18:39:59.146 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 发现未完成状态: com.chileaf.cl960.sample -> A01
2025-08-14 18:39:59.152 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 规则完成检查结果: fb4a8c49e3854b00b51034df8d6cf113, 全部完成: false, 完成数: 0/3, 有失败: false
2025-08-14 18:39:59.157 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 规则未完成: fb4a8c49e3854b00b51034df8d6cf113, 完成数: 0/3
2025-08-14 18:39:59.162 19136-19136 Platform                com.dspread.mdm.service              D  🔧 重新收集应用信息（强制刷新）
2025-08-14 18:39:59.269 19136-19136 AppManager              com.dspread.mdm.service              I  ℹ️ 应用信息获取完成: 总数131(系统129/用户2) 返回2个
2025-08-14 18:39:59.286 19136-19534 HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader HTTP响应码: 200
2025-08-14 18:39:59.289 19136-19136 Platform                com.dspread.mdm.service              D  🔧 应用信息: 2 个应用
2025-08-14 18:39:59.292 19136-19534 HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 服务器返回Content-Length: 6155416
2025-08-14 18:39:59.294 19136-19136 Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 应用信息缓存刷新完成
2025-08-14 18:39:59.297 19136-19534 HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader localFile = true /data/user/0/com.dspread.mdm.service/files/downloads/rule_fb4a8c49e3854b00b51034df8d6cf113_com.chileaf.cl960.sample.apk
2025-08-14 18:39:59.300 19136-19136 ContextImpl             com.dspread.mdm.service              W  Calling a method in the system process without a qualified user: android.app.ContextImpl.sendBroadcast:1220 android.content.ContextWrapper.sendBroadcast:504 com.dspread.mdm.service.broadcast.core.BroadcastSender.sendBroadcast:99 com.dspread.mdm.service.broadcast.core.BroadcastSender.sendBroadcast:23 com.dspread.mdm.service.broadcast.handlers.system.PackageUpdateEventHandlerImpl.sendAppRefreshBroadcast:491 
2025-08-14 18:39:59.302 19136-19136 Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 发送应用刷新广播
2025-08-14 18:39:59.309 19136-19534 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 下载进度: com.chileaf.cl960.sample - 0%
2025-08-14 18:39:59.321 19136-19207 Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"request_time":"1755167998239","org_request_time":"1755167999070","org_request_id":"1755167999070C0107","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755167998239S0000","serialNo":"01354090202503050399"}
2025-08-14 18:39:59.329 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 收到响应消息: transCode=S0000, orgRequestId=1755167999070C0107, state=0, remark=
2025-08-14 18:39:59.334 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 处理规则结果响应: state=0
2025-08-14 18:39:59.339 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 规则结果上传成功
2025-08-14 18:39:59.345 19136-19544 HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader HTTP响应码: 200
2025-08-14 18:39:59.351 19136-19544 HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 服务器返回Content-Length: 4771451
2025-08-14 18:39:59.356 19136-19544 HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader localFile = true /data/user/0/com.dspread.mdm.service/files/downloads/rule_fb4a8c49e3854b00b51034df8d6cf113_plus.H52FFB9A5.apk
2025-08-14 18:39:59.365 19136-19544 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 下载进度: plus.H52FFB9A5 - 0%
2025-08-14 18:40:00.086 19136-19544 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 下载进度: plus.H52FFB9A5 - 10%
2025-08-14 18:40:00.199 19136-19534 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 下载进度: com.chileaf.cl960.sample - 10%
2025-08-14 18:40:00.956 19136-19544 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 下载进度: plus.H52FFB9A5 - 20%
2025-08-14 18:40:01.724 19136-19534 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 下载进度: com.chileaf.cl960.sample - 20%
2025-08-14 18:40:01.732 19136-19544 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 下载进度: plus.H52FFB9A5 - 30%
2025-08-14 18:40:02.671 19136-19544 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 下载进度: plus.H52FFB9A5 - 40%
2025-08-14 18:40:03.425 19136-19534 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 下载进度: com.chileaf.cl960.sample - 30%
2025-08-14 18:40:03.448 19136-19544 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 下载进度: plus.H52FFB9A5 - 50%
2025-08-14 18:40:04.318 19136-19544 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 下载进度: plus.H52FFB9A5 - 60%
2025-08-14 18:40:05.124 19136-19534 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 下载进度: com.chileaf.cl960.sample - 40%
2025-08-14 18:40:05.255 19136-19544 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 下载进度: plus.H52FFB9A5 - 70%
2025-08-14 18:40:06.425 19136-19544 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 下载进度: plus.H52FFB9A5 - 80%
2025-08-14 18:40:06.704 19136-19534 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 下载进度: com.chileaf.cl960.sample - 50%
2025-08-14 18:40:07.507 19136-19544 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 下载进度: plus.H52FFB9A5 - 90%
2025-08-14 18:40:08.167 19136-19534 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 下载进度: com.chileaf.cl960.sample - 60%
2025-08-14 18:40:08.501 19136-19544 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 下载进度: plus.H52FFB9A5 - 100%
2025-08-14 18:40:08.512 19136-19544 HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 计算MD5: rule_fb4a8c49e3854b00b51034df8d6cf113_plus.H52FFB9A5.apk
2025-08-14 18:40:08.602 19136-19544 HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 文件: 4771451 MD5: ba1ee533924eae5c408465e7cddcbda4
2025-08-14 18:40:08.607 19136-19544 HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader MD5校验正确
2025-08-14 18:40:08.611 19136-19544 HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 清理连接资源
2025-08-14 18:40:08.616 19136-19544 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine APK下载成功: /data/user/0/com.dspread.mdm.service/files/downloads/rule_fb4a8c49e3854b00b51034df8d6cf113_plus.H52FFB9A5.apk
2025-08-14 18:40:08.621 19136-19544 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 更新应用状态: fb4a8c49e3854b00b51034df8d6cf113, plus.H52FFB9A5 -> A03
2025-08-14 18:40:08.625 19136-19544 WebSocket               com.dspread.mdm.service              I  🔧 C0107 主动式上送: rulebase_update (主动: 60)
2025-08-14 18:40:08.646 19136-19544 WebSocket               com.dspread.mdm.service              I  🔧 添加规则结果: ruleId=fb4a8c49e3854b00b51034df8d6cf113
2025-08-14 18:40:08.683 19136-19544 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0107","serialNo":"01354090202503050399","request_time":"1755168008639","request_id":"1755168008639C0107","version":"1","data":{"ruleId":"fb4a8c49e3854b00b51034df8d6cf113","taskResult":"R02","failedApkList":[{"packName":"com.chileaf.cl960.sample","apkName":"cl960","versionName":"1.1.0","versionCode":"1","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/42e16b87eaa849bcaaf0f5fadcac6aab.apk","apkMd5":"54cc3a371939423d4288d63e1ac88019","apkSize":6155416,"installBy":"0","isSystemApp":false,"priority":0,"result":"A01","taskType":"01","errorMsg":"下载中: 60%"},{"packName":"plus.H52FFB9A5","apkName":"逍遥助手","versionName":"1.0","versionCode":"100","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/203831945bb84d42b21decf1bf936815.apk","apkMd5":"ba1ee533924eae5c408465e7cddcbda4","apkSize":4771451,"installBy":"0","isSystemApp":false,"priority":0,"result":"A03","taskType":"01"},{"packName":"mark.via","apkName":"Via","versionName":"6.6.0","versionCode":"20250713","apkUrl":"","apkMd5":"","apkSize":0,"installBy":"0","isSystemApp":false,"priority":0,"result":"D02","taskType":"02"}]},"myVersionName":"1.0.08.20250814.DSPREAD.MDM.SERVICE","terminalDate":"20250814184008"}
2025-08-14 18:40:08.689 19136-19544 WebSocket               com.dspread.mdm.service              I  🔧 规则结果实时上送: ruleId=fb4a8c49e3854b00b51034df8d6cf113, result=R02 (1)
2025-08-14 18:40:08.694 19136-19544 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 上报规则执行中状态: fb4a8c49e3854b00b51034df8d6cf113, 应用数量: 3
2025-08-14 18:40:08.699 19136-19544 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 开始安装APK: /data/user/0/com.dspread.mdm.service/files/downloads/rule_fb4a8c49e3854b00b51034df8d6cf113_plus.H52FFB9A5.apk
2025-08-14 18:40:08.704 19136-19544 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 更新应用状态: fb4a8c49e3854b00b51034df8d6cf113, plus.H52FFB9A5 -> B02
2025-08-14 18:40:08.709 19136-19544 WebSocket               com.dspread.mdm.service              I  🔧 C0107 主动式上送: rulebase_update (主动: 61)
2025-08-14 18:40:08.727 19136-19544 WebSocket               com.dspread.mdm.service              I  🔧 添加规则结果: ruleId=fb4a8c49e3854b00b51034df8d6cf113
2025-08-14 18:40:08.763 19136-19544 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0107","serialNo":"01354090202503050399","request_time":"1755168008722","request_id":"1755168008722C0107","version":"1","data":{"ruleId":"fb4a8c49e3854b00b51034df8d6cf113","taskResult":"R02","failedApkList":[{"packName":"com.chileaf.cl960.sample","apkName":"cl960","versionName":"1.1.0","versionCode":"1","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/42e16b87eaa849bcaaf0f5fadcac6aab.apk","apkMd5":"54cc3a371939423d4288d63e1ac88019","apkSize":6155416,"installBy":"0","isSystemApp":false,"priority":0,"result":"A01","taskType":"01","errorMsg":"下载中: 60%"},{"packName":"plus.H52FFB9A5","apkName":"逍遥助手","versionName":"1.0","versionCode":"100","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/203831945bb84d42b21decf1bf936815.apk","apkMd5":"ba1ee533924eae5c408465e7cddcbda4","apkSize":4771451,"installBy":"0","isSystemApp":false,"priority":0,"result":"B02","taskType":"01"},{"packName":"mark.via","apkName":"Via","versionName":"6.6.0","versionCode":"20250713","apkUrl":"","apkMd5":"","apkSize":0,"installBy":"0","isSystemApp":false,"priority":0,"result":"D02","taskType":"02"}]},"myVersionName":"1.0.08.20250814.DSPREAD.MDM.SERVICE","terminalDate":"20250814184008"}
2025-08-14 18:40:08.768 19136-19544 WebSocket               com.dspread.mdm.service              I  🔧 规则结果实时上送: ruleId=fb4a8c49e3854b00b51034df8d6cf113, result=R02 (1)
2025-08-14 18:40:08.772 19136-19544 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 上报规则执行中状态: fb4a8c49e3854b00b51034df8d6cf113, 应用数量: 3
2025-08-14 18:40:08.777 19136-19544 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 注册安装回调: plus.H52FFB9A5 (规则ID: fb4a8c49e3854b00b51034df8d6cf113)
2025-08-14 18:40:08.793 19136-19544 PackageParser           com.dspread.mdm.service              W  Unknown element under <manifest>: queries at /data/user/0/com.dspread.mdm.service/files/downloads/rule_fb4a8c49e3854b00b51034df8d6cf113_plus.H52FFB9A5.apk Binary XML file line #14
2025-08-14 18:40:08.811 19136-19544 AppManager              com.dspread.mdm.service              I  ℹ️ 准备安装: plus.H52FFB9A5
2025-08-14 18:40:08.826 19136-19544 PackageParser           com.dspread.mdm.service              W  Unknown element under <manifest>: queries at /data/user/0/com.dspread.mdm.service/files/downloads/rule_fb4a8c49e3854b00b51034df8d6cf113_plus.H52FFB9A5.apk Binary XML file line #14
2025-08-14 18:40:08.850 19136-19544 AppManager              com.dspread.mdm.service              I  ℹ️ APK验证通过: plus.H52FFB9A5 v1.0(100) 4659KB
2025-08-14 18:40:08.857 19136-19544 AppManager              com.dspread.mdm.service              I  ℹ️ 创建Session: sessionId=915451125
2025-08-14 18:40:08.862 19136-19207 Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"request_time":"1755168007798","org_request_time":"1755168008639","org_request_id":"1755168008639C0107","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755168007798S0000","serialNo":"01354090202503050399"}
2025-08-14 18:40:08.871 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 收到响应消息: transCode=S0000, orgRequestId=1755168008639C0107, state=0, remark=
2025-08-14 18:40:08.876 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 处理规则结果响应: state=0
2025-08-14 18:40:08.881 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 规则结果上传成功
2025-08-14 18:40:08.909 19136-19207 Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"request_time":"1755168007882","org_request_time":"1755168008722","org_request_id":"1755168008722C0107","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755168007882S0000","serialNo":"01354090202503050399"}
2025-08-14 18:40:08.917 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 收到响应消息: transCode=S0000, orgRequestId=1755168008722C0107, state=0, remark=
2025-08-14 18:40:08.922 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 处理规则结果响应: state=0
2025-08-14 18:40:08.926 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 规则结果上传成功
2025-08-14 18:40:09.071 19136-19544 AppManager              com.dspread.mdm.service              I  ℹ️ PackageInstaller installation committed: sessionId=915451125
2025-08-14 18:40:09.137 19136-19136 AppManager              com.dspread.mdm.service              I  ℹ️ 安装进度: 90%
2025-08-14 18:40:09.363 19136-19534 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 下载进度: com.chileaf.cl960.sample - 70%
2025-08-14 18:40:10.316 19136-19534 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 下载进度: com.chileaf.cl960.sample - 80%
2025-08-14 18:40:10.694 19136-19136 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 收到广播: android.intent.action.PACKAGE_ADDED
2025-08-14 18:40:10.710 19136-19136 Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 收到包更新广播: android.intent.action.PACKAGE_ADDED, 包名: plus.H52FFB9A5
2025-08-14 18:40:10.721 19136-19136 Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 应用安装: plus.H52FFB9A5
2025-08-14 18:40:10.730 19136-19136 Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 检查WebSocket状态: packageName=plus.H52FFB9A5, action=INSTALL
2025-08-14 18:40:10.746 19136-19136 Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 通知rulebase处理应用安装: plus.H52FFB9A5
2025-08-14 18:40:10.772 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 收到系统广播，应用已安装: plus.H52FFB9A5
2025-08-14 18:40:10.785 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 找到匹配的安装回调，触发: plus.H52FFB9A5
2025-08-14 18:40:10.804 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 安装回调被触发: pkg=plus.H52FFB9A5, returnCode=1, error=
2025-08-14 18:40:10.827 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 应用安装启动成功: plus.H52FFB9A5
2025-08-14 18:40:10.862 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 从广播更新应用状态: fb4a8c49e3854b00b51034df8d6cf113, plus.H52FFB9A5 -> B03
2025-08-14 18:40:10.867 19136-19136 WebSocket               com.dspread.mdm.service              I  🔧 C0107 主动式上送: rulebase_update (主动: 62)
2025-08-14 18:40:10.923 19136-19136 WebSocket               com.dspread.mdm.service              I  🔧 添加规则结果: ruleId=fb4a8c49e3854b00b51034df8d6cf113
2025-08-14 18:40:10.974 19136-19136 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0107","serialNo":"01354090202503050399","request_time":"1755168010913","request_id":"1755168010913C0107","version":"1","data":{"ruleId":"fb4a8c49e3854b00b51034df8d6cf113","taskResult":"R02","failedApkList":[{"packName":"com.chileaf.cl960.sample","apkName":"cl960","versionName":"1.1.0","versionCode":"1","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/42e16b87eaa849bcaaf0f5fadcac6aab.apk","apkMd5":"54cc3a371939423d4288d63e1ac88019","apkSize":6155416,"installBy":"0","isSystemApp":false,"priority":0,"result":"A01","taskType":"01","errorMsg":"下载中: 80%"},{"packName":"plus.H52FFB9A5","apkName":"逍遥助手","versionName":"1.0","versionCode":"100","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/203831945bb84d42b21decf1bf936815.apk","apkMd5":"ba1ee533924eae5c408465e7cddcbda4","apkSize":4771451,"installBy":"0","isSystemApp":false,"priority":0,"result":"B03","taskType":"01"},{"packName":"mark.via","apkName":"Via","versionName":"6.6.0","versionCode":"20250713","apkUrl":"","apkMd5":"","apkSize":0,"installBy":"0","isSystemApp":false,"priority":0,"result":"D02","taskType":"02"}]},"myVersionName":"1.0.08.20250814.DSPREAD.MDM.SERVICE","terminalDate":"20250814184010"}
2025-08-14 18:40:10.979 19136-19136 WebSocket               com.dspread.mdm.service              I  🔧 规则结果实时上送: ruleId=fb4a8c49e3854b00b51034df8d6cf113, result=R02 (1)
2025-08-14 18:40:10.985 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 上报规则执行中状态: fb4a8c49e3854b00b51034df8d6cf113, 应用数量: 3
2025-08-14 18:40:10.990 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 开始检查规则完成状态: fb4a8c49e3854b00b51034df8d6cf113, 总应用数: 3
2025-08-14 18:40:10.995 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 检查应用状态: com.chileaf.cl960.sample -> A01
2025-08-14 18:40:11.000 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 发现未完成状态: com.chileaf.cl960.sample -> A01
2025-08-14 18:40:11.005 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 规则完成检查结果: fb4a8c49e3854b00b51034df8d6cf113, 全部完成: false, 完成数: 0/3, 有失败: false
2025-08-14 18:40:11.010 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 规则未完成: fb4a8c49e3854b00b51034df8d6cf113, 完成数: 0/3
2025-08-14 18:40:11.015 19136-19136 Platform                com.dspread.mdm.service              D  🔧 重新收集应用信息（强制刷新）
2025-08-14 18:40:11.125 19136-19136 AppManager              com.dspread.mdm.service              I  ℹ️ 应用信息获取完成: 总数132(系统129/用户3) 返回3个
2025-08-14 18:40:11.151 19136-19136 Platform                com.dspread.mdm.service              D  🔧 应用信息: 3 个应用
2025-08-14 18:40:11.156 19136-19136 Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 应用信息缓存刷新完成
2025-08-14 18:40:11.161 19136-19136 ContextImpl             com.dspread.mdm.service              W  Calling a method in the system process without a qualified user: android.app.ContextImpl.sendBroadcast:1220 android.content.ContextWrapper.sendBroadcast:504 com.dspread.mdm.service.broadcast.core.BroadcastSender.sendBroadcast:99 com.dspread.mdm.service.broadcast.core.BroadcastSender.sendBroadcast:23 com.dspread.mdm.service.broadcast.handlers.system.PackageUpdateEventHandlerImpl.sendAppRefreshBroadcast:491 
2025-08-14 18:40:11.163 19136-19136 Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 发送应用刷新广播
2025-08-14 18:40:11.242 19136-19534 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 下载进度: com.chileaf.cl960.sample - 90%
2025-08-14 18:40:11.321 19136-19207 Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"request_time":"1755168010087","org_request_time":"1755168010913","org_request_id":"1755168010913C0107","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755168010087S0000","serialNo":"01354090202503050399"}
2025-08-14 18:40:11.328 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 收到响应消息: transCode=S0000, orgRequestId=1755168010913C0107, state=0, remark=
2025-08-14 18:40:11.333 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 处理规则结果响应: state=0
2025-08-14 18:40:11.337 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 规则结果上传成功
2025-08-14 18:40:12.111 19136-19534 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 下载进度: com.chileaf.cl960.sample - 100%
2025-08-14 18:40:12.119 19136-19534 HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 计算MD5: rule_fb4a8c49e3854b00b51034df8d6cf113_com.chileaf.cl960.sample.apk
2025-08-14 18:40:12.213 19136-19534 HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 文件: 6155416 MD5: 54cc3a371939423d4288d63e1ac88019
2025-08-14 18:40:12.217 19136-19534 HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader MD5校验正确
2025-08-14 18:40:12.221 19136-19534 HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 清理连接资源
2025-08-14 18:40:12.226 19136-19534 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine APK下载成功: /data/user/0/com.dspread.mdm.service/files/downloads/rule_fb4a8c49e3854b00b51034df8d6cf113_com.chileaf.cl960.sample.apk
2025-08-14 18:40:12.231 19136-19534 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 更新应用状态: fb4a8c49e3854b00b51034df8d6cf113, com.chileaf.cl960.sample -> A03
2025-08-14 18:40:12.235 19136-19534 WebSocket               com.dspread.mdm.service              I  🔧 C0107 主动式上送: rulebase_update (主动: 63)
2025-08-14 18:40:12.254 19136-19534 WebSocket               com.dspread.mdm.service              I  🔧 添加规则结果: ruleId=fb4a8c49e3854b00b51034df8d6cf113
2025-08-14 18:40:12.290 19136-19534 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0107","serialNo":"01354090202503050399","request_time":"1755168012248","request_id":"1755168012248C0107","version":"1","data":{"ruleId":"fb4a8c49e3854b00b51034df8d6cf113","taskResult":"R02","failedApkList":[{"packName":"com.chileaf.cl960.sample","apkName":"cl960","versionName":"1.1.0","versionCode":"1","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/42e16b87eaa849bcaaf0f5fadcac6aab.apk","apkMd5":"54cc3a371939423d4288d63e1ac88019","apkSize":6155416,"installBy":"0","isSystemApp":false,"priority":0,"result":"A03","taskType":"01"},{"packName":"plus.H52FFB9A5","apkName":"逍遥助手","versionName":"1.0","versionCode":"100","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/203831945bb84d42b21decf1bf936815.apk","apkMd5":"ba1ee533924eae5c408465e7cddcbda4","apkSize":4771451,"installBy":"0","isSystemApp":false,"priority":0,"result":"B03","taskType":"01"},{"packName":"mark.via","apkName":"Via","versionName":"6.6.0","versionCode":"20250713","apkUrl":"","apkMd5":"","apkSize":0,"installBy":"0","isSystemApp":false,"priority":0,"result":"D02","taskType":"02"}]},"myVersionName":"1.0.08.20250814.DSPREAD.MDM.SERVICE","terminalDate":"20250814184012"}
2025-08-14 18:40:12.294 19136-19534 WebSocket               com.dspread.mdm.service              I  🔧 规则结果实时上送: ruleId=fb4a8c49e3854b00b51034df8d6cf113, result=R02 (1)
2025-08-14 18:40:12.299 19136-19534 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 上报规则执行中状态: fb4a8c49e3854b00b51034df8d6cf113, 应用数量: 3
2025-08-14 18:40:12.303 19136-19534 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 开始安装APK: /data/user/0/com.dspread.mdm.service/files/downloads/rule_fb4a8c49e3854b00b51034df8d6cf113_com.chileaf.cl960.sample.apk
2025-08-14 18:40:12.308 19136-19534 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 更新应用状态: fb4a8c49e3854b00b51034df8d6cf113, com.chileaf.cl960.sample -> B02
2025-08-14 18:40:12.312 19136-19534 WebSocket               com.dspread.mdm.service              I  🔧 C0107 主动式上送: rulebase_update (主动: 64)
2025-08-14 18:40:12.329 19136-19534 WebSocket               com.dspread.mdm.service              I  🔧 添加规则结果: ruleId=fb4a8c49e3854b00b51034df8d6cf113
2025-08-14 18:40:12.364 19136-19534 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0107","serialNo":"01354090202503050399","request_time":"1755168012324","request_id":"1755168012324C0107","version":"1","data":{"ruleId":"fb4a8c49e3854b00b51034df8d6cf113","taskResult":"R02","failedApkList":[{"packName":"com.chileaf.cl960.sample","apkName":"cl960","versionName":"1.1.0","versionCode":"1","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/42e16b87eaa849bcaaf0f5fadcac6aab.apk","apkMd5":"54cc3a371939423d4288d63e1ac88019","apkSize":6155416,"installBy":"0","isSystemApp":false,"priority":0,"result":"B02","taskType":"01"},{"packName":"plus.H52FFB9A5","apkName":"逍遥助手","versionName":"1.0","versionCode":"100","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/203831945bb84d42b21decf1bf936815.apk","apkMd5":"ba1ee533924eae5c408465e7cddcbda4","apkSize":4771451,"installBy":"0","isSystemApp":false,"priority":0,"result":"B03","taskType":"01"},{"packName":"mark.via","apkName":"Via","versionName":"6.6.0","versionCode":"20250713","apkUrl":"","apkMd5":"","apkSize":0,"installBy":"0","isSystemApp":false,"priority":0,"result":"D02","taskType":"02"}]},"myVersionName":"1.0.08.20250814.DSPREAD.MDM.SERVICE","terminalDate":"20250814184012"}
2025-08-14 18:40:12.369 19136-19534 WebSocket               com.dspread.mdm.service              I  🔧 规则结果实时上送: ruleId=fb4a8c49e3854b00b51034df8d6cf113, result=R02 (1)
2025-08-14 18:40:12.373 19136-19534 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 上报规则执行中状态: fb4a8c49e3854b00b51034df8d6cf113, 应用数量: 3
2025-08-14 18:40:12.378 19136-19534 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 注册安装回调: com.chileaf.cl960.sample (规则ID: fb4a8c49e3854b00b51034df8d6cf113)
2025-08-14 18:40:12.405 19136-19534 AppManager              com.dspread.mdm.service              I  ℹ️ 准备安装: com.chileaf.cl960.sample
2025-08-14 18:40:12.417 19136-19207 Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"request_time":"1755168011405","org_request_time":"1755168012248","org_request_id":"1755168012248C0107","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755168011405S0000","serialNo":"01354090202503050399"}
2025-08-14 18:40:12.424 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 收到响应消息: transCode=S0000, orgRequestId=1755168012248C0107, state=0, remark=
2025-08-14 18:40:12.428 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 处理规则结果响应: state=0
2025-08-14 18:40:12.431 19136-19534 AppManager              com.dspread.mdm.service              I  ℹ️ APK验证通过: com.chileaf.cl960.sample v1.1.0(1) 6011KB
2025-08-14 18:40:12.432 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 规则结果上传成功
2025-08-14 18:40:12.438 19136-19534 AppManager              com.dspread.mdm.service              I  ℹ️ 创建Session: sessionId=1949033990
2025-08-14 18:40:12.550 19136-19207 Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"request_time":"1755168011522","org_request_time":"1755168012324","org_request_id":"1755168012324C0107","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755168011522S0000","serialNo":"01354090202503050399"}
2025-08-14 18:40:12.557 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 收到响应消息: transCode=S0000, orgRequestId=1755168012324C0107, state=0, remark=
2025-08-14 18:40:12.562 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 处理规则结果响应: state=0
2025-08-14 18:40:12.566 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 规则结果上传成功
2025-08-14 18:40:12.692 19136-19534 AppManager              com.dspread.mdm.service              I  ℹ️ PackageInstaller installation committed: sessionId=1949033990
2025-08-14 18:40:12.729 19136-19136 AppManager              com.dspread.mdm.service              I  ℹ️ 安装进度: 90%
2025-08-14 18:40:12.939 19136-19136 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 收到广播: android.intent.action.PACKAGE_ADDED
2025-08-14 18:40:12.974 19136-19136 Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 收到包更新广播: android.intent.action.PACKAGE_ADDED, 包名: com.chileaf.cl960.sample
2025-08-14 18:40:12.995 19136-19136 Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 应用安装: com.chileaf.cl960.sample
2025-08-14 18:40:13.024 19136-19136 Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 检查WebSocket状态: packageName=com.chileaf.cl960.sample, action=INSTALL
2025-08-14 18:40:13.036 19136-19136 Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 通知rulebase处理应用安装: com.chileaf.cl960.sample
2025-08-14 18:40:13.051 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 收到系统广播，应用已安装: com.chileaf.cl960.sample
2025-08-14 18:40:13.056 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 找到匹配的安装回调，触发: com.chileaf.cl960.sample
2025-08-14 18:40:13.063 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 安装回调被触发: pkg=com.chileaf.cl960.sample, returnCode=1, error=
2025-08-14 18:40:13.068 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 应用安装启动成功: com.chileaf.cl960.sample
2025-08-14 18:40:13.077 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 从广播更新应用状态: fb4a8c49e3854b00b51034df8d6cf113, com.chileaf.cl960.sample -> B03
2025-08-14 18:40:13.083 19136-19136 WebSocket               com.dspread.mdm.service              I  🔧 C0107 主动式上送: rulebase_update (主动: 65)
2025-08-14 18:40:13.111 19136-19136 WebSocket               com.dspread.mdm.service              I  🔧 添加规则结果: ruleId=fb4a8c49e3854b00b51034df8d6cf113
2025-08-14 18:40:13.164 19136-19136 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0107","serialNo":"01354090202503050399","request_time":"1755168013099","request_id":"1755168013099C0107","version":"1","data":{"ruleId":"fb4a8c49e3854b00b51034df8d6cf113","taskResult":"R02","failedApkList":[{"packName":"com.chileaf.cl960.sample","apkName":"cl960","versionName":"1.1.0","versionCode":"1","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/42e16b87eaa849bcaaf0f5fadcac6aab.apk","apkMd5":"54cc3a371939423d4288d63e1ac88019","apkSize":6155416,"installBy":"0","isSystemApp":false,"priority":0,"result":"B03","taskType":"01"},{"packName":"plus.H52FFB9A5","apkName":"逍遥助手","versionName":"1.0","versionCode":"100","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/203831945bb84d42b21decf1bf936815.apk","apkMd5":"ba1ee533924eae5c408465e7cddcbda4","apkSize":4771451,"installBy":"0","isSystemApp":false,"priority":0,"result":"B03","taskType":"01"},{"packName":"mark.via","apkName":"Via","versionName":"6.6.0","versionCode":"20250713","apkUrl":"","apkMd5":"","apkSize":0,"installBy":"0","isSystemApp":false,"priority":0,"result":"D02","taskType":"02"}]},"myVersionName":"1.0.08.20250814.DSPREAD.MDM.SERVICE","terminalDate":"20250814184013"}
2025-08-14 18:40:13.169 19136-19136 WebSocket               com.dspread.mdm.service              I  🔧 规则结果实时上送: ruleId=fb4a8c49e3854b00b51034df8d6cf113, result=R02 (1)
2025-08-14 18:40:13.174 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 上报规则执行中状态: fb4a8c49e3854b00b51034df8d6cf113, 应用数量: 3
2025-08-14 18:40:13.180 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 开始检查规则完成状态: fb4a8c49e3854b00b51034df8d6cf113, 总应用数: 3
2025-08-14 18:40:13.185 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 检查应用状态: com.chileaf.cl960.sample -> B03
2025-08-14 18:40:13.189 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 发现成功状态: com.chileaf.cl960.sample -> B03
2025-08-14 18:40:13.194 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 检查应用状态: plus.H52FFB9A5 -> B03
2025-08-14 18:40:13.199 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 发现成功状态: plus.H52FFB9A5 -> B03
2025-08-14 18:40:13.204 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 检查应用状态: mark.via -> D02
2025-08-14 18:40:13.209 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 发现成功状态: mark.via -> D02
2025-08-14 18:40:13.214 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 规则完成检查结果: fb4a8c49e3854b00b51034df8d6cf113, 全部完成: true, 完成数: 3/3, 有失败: false
2025-08-14 18:40:13.219 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 规则执行全部成功，上送RULEBASED_SUCCESS: fb4a8c49e3854b00b51034df8d6cf113
2025-08-14 18:40:13.224 19136-19136 WebSocket               com.dspread.mdm.service              I  🔧 C0107 主动式上送: rulebase_update (主动: 66)
2025-08-14 18:40:13.245 19136-19136 WebSocket               com.dspread.mdm.service              I  🔧 添加规则结果: ruleId=fb4a8c49e3854b00b51034df8d6cf113
2025-08-14 18:40:13.280 19136-19136 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0107","serialNo":"01354090202503050399","request_time":"1755168013238","request_id":"1755168013238C0107","version":"1","data":{"ruleId":"fb4a8c49e3854b00b51034df8d6cf113","taskResult":"R03","failedApkList":[{"packName":"com.chileaf.cl960.sample","apkName":"cl960","versionName":"1.1.0","versionCode":"1","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/42e16b87eaa849bcaaf0f5fadcac6aab.apk","apkMd5":"54cc3a371939423d4288d63e1ac88019","apkSize":6155416,"installBy":"0","isSystemApp":false,"priority":0,"result":"B03","taskType":"01"},{"packName":"plus.H52FFB9A5","apkName":"逍遥助手","versionName":"1.0","versionCode":"100","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/203831945bb84d42b21decf1bf936815.apk","apkMd5":"ba1ee533924eae5c408465e7cddcbda4","apkSize":4771451,"installBy":"0","isSystemApp":false,"priority":0,"result":"B03","taskType":"01"},{"packName":"mark.via","apkName":"Via","versionName":"6.6.0","versionCode":"20250713","apkUrl":"","apkMd5":"","apkSize":0,"installBy":"0","isSystemApp":false,"priority":0,"result":"D02","taskType":"02"}]},"myVersionName":"1.0.08.20250814.DSPREAD.MDM.SERVICE","terminalDate":"20250814184013"}
2025-08-14 18:40:13.286 19136-19136 WebSocket               com.dspread.mdm.service              I  🔧 规则结果实时上送: ruleId=fb4a8c49e3854b00b51034df8d6cf113, result=R03 (1)
2025-08-14 18:40:13.292 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 上报规则状态: fb4a8c49e3854b00b51034df8d6cf113 -> R03
2025-08-14 18:40:13.297 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleStateMonitor 收到状态变化: fb4a8c49e3854b00b51034df8d6cf113, R02 -> R03
2025-08-14 18:40:13.311 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleStateMonitor 状态变化已记录: fb4a8c49e3854b00b51034df8d6cf113, R03
2025-08-14 18:40:13.316 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleStateMachine 状态转换成功: fb4a8c49e3854b00b51034df8d6cf113, R02 -> R03
2025-08-14 18:40:13.321 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 规则执行完成: fb4a8c49e3854b00b51034df8d6cf113, 有失败: false
2025-08-14 18:40:13.325 19136-19136 Platform                com.dspread.mdm.service              D  🔧 重新收集应用信息（强制刷新）
2025-08-14 18:40:13.327 19136-19207 Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"request_time":"1755168012273","org_request_time":"1755168013099","org_request_id":"1755168013099C0107","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755168012273S0000","serialNo":"01354090202503050399"}
2025-08-14 18:40:13.335 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 收到响应消息: transCode=S0000, orgRequestId=1755168013099C0107, state=0, remark=
2025-08-14 18:40:13.339 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 处理规则结果响应: state=0
2025-08-14 18:40:13.343 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 规则结果上传成功
2025-08-14 18:40:13.385 19136-19207 Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"request_time":"1755168012396","org_request_time":"1755168013238","org_request_id":"1755168013238C0107","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755168012396S0000","serialNo":"01354090202503050399"}
2025-08-14 18:40:13.392 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 收到响应消息: transCode=S0000, orgRequestId=1755168013238C0107, state=0, remark=
2025-08-14 18:40:13.396 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 处理规则结果响应: state=0
2025-08-14 18:40:13.401 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 规则结果上传成功
2025-08-14 18:40:13.421 19136-19136 AppManager              com.dspread.mdm.service              I  ℹ️ 应用信息获取完成: 总数133(系统129/用户4) 返回4个
2025-08-14 18:40:13.450 19136-19136 Platform                com.dspread.mdm.service              D  🔧 应用信息: 4 个应用
2025-08-14 18:40:13.454 19136-19136 Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 应用信息缓存刷新完成
2025-08-14 18:40:13.459 19136-19136 ContextImpl             com.dspread.mdm.service              W  Calling a method in the system process without a qualified user: android.app.ContextImpl.sendBroadcast:1220 android.content.ContextWrapper.sendBroadcast:504 com.dspread.mdm.service.broadcast.core.BroadcastSender.sendBroadcast:99 com.dspread.mdm.service.broadcast.core.BroadcastSender.sendBroadcast:23 com.dspread.mdm.service.broadcast.handlers.system.PackageUpdateEventHandlerImpl.sendAppRefreshBroadcast:491 
2025-08-14 18:40:13.461 19136-19136 Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 发送应用刷新广播
