package com.dspread.mdm.service.broadcast.core

/**
 * 广播Action常量统一管理
 * 集中管理所有广播相关的常量定义
 */
object BroadcastActions {

    // ==================== 系统广播 ====================
    
    /**
     * 开机完成广播
     */
    const val ACTION_BOOT_COMPLETED = "android.intent.action.BOOT_COMPLETED"
    
    /**
     * 快速开机广播（部分厂商）
     */
    const val ACTION_QUICKBOOT_POWERON = "android.intent.action.QUICKBOOT_POWERON"
    
    /**
     * 网络连接状态变化广播
     */
    const val ACTION_NETWORK_CHANGED = "android.net.conn.CONNECTIVITY_CHANGE"
    
    /**
     * 屏幕点亮广播
     */
    const val ACTION_SCREEN_ON = "android.intent.action.SCREEN_ON"
    
    /**
     * 屏幕关闭广播
     */
    const val ACTION_SCREEN_OFF = "android.intent.action.SCREEN_OFF"

    // ==================== WebSocket相关广播 ====================
    
    /**
     * WebSocket连接广播
     */
    const val ACTION_WEBSOCKET_CONNECT = "com.dspread.mdm.service.WEBSOCKET_CONNECT"
    
    /**
     * WebSocket断开连接广播
     */
    const val ACTION_WEBSOCKET_DISCONNECT = "com.dspread.mdm.service.WEBSOCKET_DISCONNECT"
    
    /**
     * WebSocket重连广播
     */
    const val ACTION_WEBSOCKET_RECONNECT = "com.dspread.mdm.service.WEBSOCKET_RECONNECT"
    
    /**
     * WebSocket发送心跳广播
     */
    const val ACTION_WEBSOCKET_SEND_HEARTBEAT = "com.dspread.mdm.service.WEBSOCKET_SEND_HEARTBEAT"

    // ==================== 定时器相关广播 ====================
    
    /**
     * 轮询定时器启动广播
     */
    const val ACTION_POLL_TIMER_START = "com.dspread.mdm.service.POLL_TIMER_START"

    /**
     * 终端信息上传定时器广播
     */
    const val TER_INFO_UPLOAD_BC = "com.dspread.mdm.service.TER_INFO_UPLOAD_BC"

    /**
     * 服务守护定时器广播
     */
    const val ACTION_SERVICE_GUARD_TIMER = "com.dspread.mdm.service.SERVICE_GUARD_TIMER"

    /**
     * 任务检查定时器广播
     */
    const val ACTION_WSTASK_EXEC_BC = "com.dspread.mdm.service.WSTASK_EXEC_BC"

    /**
     * Provisioning定时器广播
     */
    const val ACTION_PROVISIONING_TIMER = "com.dspread.mdm.service.PROVISIONING_TIMER"

    /**
     * 检查自身更新状态广播
     */
    const val CHECK_SELF_UPDATE_STATUS = "com.dspread.mdm.service.CHECK_SELF_UPDATE_STATUS"

    // ==================== 应用相关广播 ====================
    
    /**
     * 服务启动广播
     */
    const val ACTION_SERVICE_START = "com.dspread.mdm.service.SERVICE_START"

    /**
     * 服务停止广播
     */
    const val ACTION_SERVICE_STOP = "com.dspread.mdm.service.SERVICE_STOP"

    /**
     * 服务重启广播
     */
    const val ACTION_SERVICE_RESTART = "com.dspread.mdm.service.SERVICE_RESTART"

    /**
     * WakeLock续期广播
     */
    const val ACTION_WAKELOCK_RENEWAL = "com.dspread.mdm.service.WAKELOCK_RENEWAL"

    /**
     * WakeLock获取广播
     */
    const val ACTION_WAKELOCK_ACQUIRE = "com.dspread.mdm.service.WAKELOCK_ACQUIRE"

    /**
     * WakeLock释放广播
     */
    const val ACTION_WAKELOCK_RELEASE = "com.dspread.mdm.service.WAKELOCK_RELEASE"

    // ==================== 设备相关广播 ====================

    /**
     * 设备状态变化广播
     */
    const val ACTION_DEVICE_STATUS_CHANGED = "com.dspread.mdm.service.DEVICE_STATUS_CHANGED"

    /**
     * 电池状态变化广播
     */
    const val ACTION_BATTERY_STATUS_CHANGED = "com.dspread.mdm.service.BATTERY_STATUS_CHANGED"

    // ==================== 地理围栏相关广播 ====================

    /**
     * 地理围栏警告广播
     */
    const val ACTION_GEOFENCING_DETECTED_WARNING = "com.dspread.mdm.service.ACTION_GEOFENCING_DETECTED_WARNING"

    /**
     * 关闭地理围栏警告广播
     */
    const val ACTION_GEOFENCING_DETECTED_WARNING_CLOSE = "com.dspread.mdm.service.ACTION_GEOFENCING_DETECTED_WARNING_CLOSE"

    /**
     * 地理围栏锁屏广播
     */
    const val ACTION_GEOFENCING_DETECTED_LOCK = "com.dspread.mdm.service.ACTION_GEOFENCING_DETECTED_LOCK"

    /**
     * 重置锁屏定时器广播
     */
    const val ACTION_GEOFENCING_RESET_LOCKSCREEN_TIMER = "com.dspread.mdm.service.ACTION_GEOFENCING_RESET_LOCKSCREEN_TIMER"

    /**
     * 关闭锁屏广播
     */
    const val ACTION_CLOSE_LOCKSCREEN = "com.dspread.mdm.service.ACTION_CLOSE_LOCKSCREEN"

    /**
     * 进入地理围栏广播
     */
    const val ACTION_ENTER_GEOFENCE = "com.dspread.mdm.service.ACTION_ENTER_GEOFENCE"

    /**
     * 到期重启广播
     */
    const val ACTION_EXPIRE_REBOOT = "com.dspread.mdm.service.ACTION_EXPIRE_REBOOT"

    /**
     * 系统定时器广播
     */
    const val ACTION_SYSTEM_TIMER = "com.dspread.mdm.service.ACTION_SYSTEM_TIMER"

    /**
     * 地理围栏检查广播
     */
    const val ACTION_GEOFENCE_CHECK = "com.dspread.mdm.service.ACTION_GEOFENCE_CHECK"

    /**
     * 蓝牙扫描定时器广播
     */
    const val ACTION_BLE_SCAN_TIMER = "com.dspread.mdm.service.ACTION_BLE_SCAN_TIMER"

    /**
     * 数据擦除完成广播
     */
    const val ACTION_DATA_WIPED = "com.dspread.mdm.service.ACTION_DATA_WIPED"

    /**
     * 解绑Launcher广播
     */
    const val UNBIND_LAUNCHER = "com.dspread.mdm.service.UNBIND_LAUNCHER"

    /**
     * 获取一次性密码广播
     */
    const val ACTION_GET_ONETIME_PWD = "com.dspread.mdm.service.ACTION_GET_ONETIME_PWD"

    /**
     * 擦除参数变更广播
     */
    const val ACTION_WIPE_PARAM_CHANGE = "com.dspread.mdm.service.ACTION_WIPE_PARAM_CHANGE"

    /**
     * WebSocket连接状态广播
     */
    const val ACTION_WEBSOCKET_CONNECTION = "com.dspread.mdm.service.ACTION_WEBSOCKET_CONNECTION"

    /**
     * 离开地理围栏广播
     */
    const val ACTION_OUT_OF_GEOFENCE = "com.dspread.mdm.service.ACTION_OUT_OF_GEOFENCE"

    /**启动初始化广播*/
    const val GPS_SCHEDULE_TIME="com.dspread.mdm.service.GPS_SCHEDULE_TIME"


    // ==================== 权限定义 ====================
    
    /**
     * 自定义广播权限
     */
    const val PERMISSION_MY_BROADCAST = "com.dspread.mdm.service.permissions.MY_BROADCAST"
}
