package com.dspread.mdm.service.broadcast.receivers

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.net.ConnectivityManager
import android.net.NetworkInfo
import android.net.wifi.WifiManager
import com.dspread.mdm.service.utils.log.Logger
import com.dspread.mdm.service.broadcast.core.BroadcastSender
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch

/**
 * WiFi Profile模块广播接收器
 * 监听WiFi相关的系统广播事件
 */
class WifiProfileReceiver : BroadcastReceiver() {
    
    companion object {
        private const val TAG = "[WifiProfileReceiver]"
        
        /**
         * 创建IntentFilter用于注册广播
         */
        fun createIntentFilter(): IntentFilter {
            return IntentFilter().apply {
                // WiFi状态变化
                addAction(WifiManager.WIFI_STATE_CHANGED_ACTION)
                addAction(WifiManager.NETWORK_STATE_CHANGED_ACTION)
                addAction(WifiManager.SUPPLICANT_STATE_CHANGED_ACTION)
                addAction(WifiManager.SCAN_RESULTS_AVAILABLE_ACTION)
                
                // 网络连接状态变化
                addAction(ConnectivityManager.CONNECTIVITY_ACTION)
                
                // 自定义广播（如果需要）
                addAction("com.dspread.mdm.WIFI_PROFILE_CHECK")
            }
        }
    }
    
    override fun onReceive(context: Context, intent: Intent) {
        val action = intent.action ?: return
        
        Logger.wifi("$TAG 收到广播: $action")
        
        try {
            when (action) {
                WifiManager.WIFI_STATE_CHANGED_ACTION -> {
                    handleWifiStateChanged(context, intent)
                }
                
                WifiManager.NETWORK_STATE_CHANGED_ACTION -> {
                    handleNetworkStateChanged(context, intent)
                }
                
                WifiManager.SUPPLICANT_STATE_CHANGED_ACTION -> {
                    handleSupplicantStateChanged(context, intent)
                }
                
                WifiManager.SCAN_RESULTS_AVAILABLE_ACTION -> {
                    handleScanResultsAvailable(context, intent)
                }
                
                ConnectivityManager.CONNECTIVITY_ACTION -> {
                    handleConnectivityChanged(context, intent)
                }
                
                "com.dspread.mdm.WIFI_PROFILE_CHECK" -> {
                    handleWifiProfileCheck(context, intent)
                }
                
                else -> {
                    Logger.wifi("$TAG 未处理的广播: $action")
                }
            }
        } catch (e: Exception) {
            Logger.wifiE("$TAG 处理广播失败: $action", e)
        }
    }
    
    /**
     * 处理WiFi状态变化
     */
    private fun handleWifiStateChanged(context: Context, intent: Intent) {
        val wifiState = intent.getIntExtra(WifiManager.EXTRA_WIFI_STATE, WifiManager.WIFI_STATE_UNKNOWN)
        
        when (wifiState) {
            WifiManager.WIFI_STATE_ENABLED -> {
                Logger.wsm("$TAG WiFi已启用")
                // WiFi启用后，可能需要重新检查配置
                triggerWifiProfileCheck(context)
            }
            
            WifiManager.WIFI_STATE_DISABLED -> {
                Logger.wsm("$TAG WiFi已禁用")
            }
            
            WifiManager.WIFI_STATE_ENABLING -> {
                Logger.wsm("$TAG WiFi启用中...")
            }
            
            WifiManager.WIFI_STATE_DISABLING -> {
                Logger.wsm("$TAG WiFi禁用中...")
            }
            
            else -> {
                Logger.wsm("$TAG WiFi状态未知: $wifiState")
            }
        }
    }
    
    /**
     * 处理网络状态变化
     */
    private fun handleNetworkStateChanged(context: Context, intent: Intent) {
        val networkInfo = intent.getParcelableExtra<NetworkInfo>(WifiManager.EXTRA_NETWORK_INFO)
        
        networkInfo?.let { info ->
            Logger.wsm("$TAG 网络状态变化: ${info.state}, 类型: ${info.typeName}")
            
            when (info.state) {
                NetworkInfo.State.CONNECTED -> {
                    Logger.wsm("$TAG WiFi网络已连接")
                    handleWifiConnected(context, intent)
                }
                
                NetworkInfo.State.DISCONNECTED -> {
                    Logger.wsm("$TAG WiFi网络已断开")
                    handleWifiDisconnected(context, intent)
                }
                
                NetworkInfo.State.CONNECTING -> {
                    Logger.wsm("$TAG WiFi网络连接中...")
                }
                
                else -> {
                    Logger.wsm("$TAG WiFi网络状态: ${info.state}")
                }
            }
        }
    }
    
    /**
     * 处理WiFi认证状态变化
     */
    private fun handleSupplicantStateChanged(context: Context, intent: Intent) {
        val supplicantState = intent.getParcelableExtra<android.net.wifi.SupplicantState>(
            WifiManager.EXTRA_NEW_STATE
        )
        
        supplicantState?.let { state ->
            Logger.wsm("$TAG WiFi认证状态: $state")
            
            // 检查是否有认证错误
            if (intent.hasExtra(WifiManager.EXTRA_SUPPLICANT_ERROR)) {
                val error = intent.getIntExtra(WifiManager.EXTRA_SUPPLICANT_ERROR, -1)
                Logger.wsmW("$TAG WiFi认证错误: $error")
                
                // 认证失败时可能需要重新尝试其他WiFi配置
                handleWifiAuthenticationError(context, error)
            }
        }
    }
    
    /**
     * 处理WiFi扫描结果可用
     */
    private fun handleScanResultsAvailable(context: Context, intent: Intent) {
        val success = intent.getBooleanExtra(WifiManager.EXTRA_RESULTS_UPDATED, false)
        
        if (success) {
            Logger.wsm("$TAG WiFi扫描结果已更新")
            // 扫描结果更新后，可能需要检查是否有新的可连接WiFi
            // 这里可以触发WiFi配置检查
        } else {
            Logger.wsmW("$TAG WiFi扫描失败")
        }
    }
    
    /**
     * 处理网络连接状态变化
     */
    private fun handleConnectivityChanged(context: Context, intent: Intent) {
        val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
        val activeNetwork = connectivityManager.activeNetworkInfo
        
        if (activeNetwork != null && activeNetwork.isConnected) {
            Logger.wsm("$TAG 网络已连接: ${activeNetwork.typeName}")
            
            if (activeNetwork.type == ConnectivityManager.TYPE_WIFI) {
                Logger.wsm("$TAG WiFi网络连接成功")
                // WiFi连接成功，可能需要验证网络连通性
                verifyNetworkConnectivity(context)
            }
        } else {
            Logger.wsm("$TAG 网络已断开")
            // 网络断开时，可能需要尝试连接其他WiFi配置
            handleNetworkDisconnected(context)
        }
    }
    
    /**
     * 处理WiFi配置检查广播
     */
    private fun handleWifiProfileCheck(context: Context, intent: Intent) {
        Logger.wsm("$TAG 收到WiFi配置检查请求")
        
        // 触发WiFi配置检查
        triggerWifiProfileCheck(context)
    }
    
    /**
     * 处理WiFi连接成功
     */
    private fun handleWifiConnected(context: Context, intent: Intent) {
        // 获取连接的WiFi信息
        val wifiManager = context.getSystemService(Context.WIFI_SERVICE) as WifiManager
        val wifiInfo = wifiManager.connectionInfo
        
        if (wifiInfo != null) {
            val ssid = wifiInfo.ssid?.replace("\"", "") ?: "Unknown"
            Logger.wsm("$TAG 已连接到WiFi: $ssid")
            
            // 可以在这里记录连接成功的WiFi，清除失败标记等
            GlobalScope.launch {
                try {
                    // TODO: 通知WiFi Profile管理器连接成功
                    // wifiProfileManager.onWifiConnected(ssid)
                } catch (e: Exception) {
                    Logger.wsmE("$TAG 处理WiFi连接成功事件失败", e)
                }
            }
        }
    }
    
    /**
     * 处理WiFi断开连接
     */
    private fun handleWifiDisconnected(context: Context, intent: Intent) {
        Logger.wsm("$TAG WiFi连接已断开")
        
        // WiFi断开后，可能需要尝试连接其他配置
        GlobalScope.launch {
            try {
                // 延迟一段时间后尝试重新连接
                kotlinx.coroutines.delay(5000)
                triggerWifiProfileCheck(context)
            } catch (e: Exception) {
                Logger.wsmE("$TAG 处理WiFi断开事件失败", e)
            }
        }
    }
    
    /**
     * 处理WiFi认证错误
     */
    private fun handleWifiAuthenticationError(context: Context, errorCode: Int) {
        Logger.wsmW("$TAG WiFi认证失败，错误码: $errorCode")
        
        // 认证失败时，可能需要标记当前WiFi为失败状态，尝试其他配置
        GlobalScope.launch {
            try {
                // TODO: 通知WiFi Profile管理器认证失败
                // wifiProfileManager.onWifiAuthenticationFailed(errorCode)
                
                // 尝试连接其他WiFi配置
                triggerWifiProfileCheck(context)
            } catch (e: Exception) {
                Logger.wsmE("$TAG 处理WiFi认证错误失败", e)
            }
        }
    }
    
    /**
     * 验证网络连通性
     */
    private fun verifyNetworkConnectivity(context: Context) {
        GlobalScope.launch {
            try {
                // TODO: 使用NetworkUtils进行ping测试
                // val networkUtils = NetworkApi()
                // val result = networkUtils.pingTest("*******")
                // if (result.isSuccess) {
                //     Logger.wsm("$TAG 网络连通性验证成功")
                // } else {
                //     Logger.wsmW("$TAG 网络连通性验证失败")
                // }
            } catch (e: Exception) {
                Logger.wsmE("$TAG 网络连通性验证异常", e)
            }
        }
    }
    
    /**
     * 处理网络断开
     */
    private fun handleNetworkDisconnected(context: Context) {
        Logger.wsm("$TAG 处理网络断开事件")
        
        // 网络断开后，延迟一段时间尝试重新连接
        GlobalScope.launch {
            try {
                kotlinx.coroutines.delay(10000) // 等待10秒
                triggerWifiProfileCheck(context)
            } catch (e: Exception) {
                Logger.wsmE("$TAG 处理网络断开失败", e)
            }
        }
    }
    
    /**
     * 触发WiFi配置检查
     */
    private fun triggerWifiProfileCheck(context: Context) {
        try {
            // 发送自定义广播触发WiFi配置检查
            BroadcastSender.sendBroadcast(context, "com.dspread.mdm.WIFI_PROFILE_TRIGGER_CHECK")

            Logger.wsm("$TAG 已触发WiFi配置检查")
        } catch (e: Exception) {
            Logger.wsmE("$TAG 触发WiFi配置检查失败", e)
        }
    }
}
