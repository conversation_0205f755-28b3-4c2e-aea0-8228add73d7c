apply plugin: 'com.android.application'

android {
    signingConfigs {
        config {
            keyAlias 'platform'
            keyPassword 'bbpos123456'
            storeFile file('../platform.keystore')
            storePassword 'bbpos123456'
        }
        newconfig {
            keyAlias 'androiddebugkey'
            keyPassword 'android'
            storeFile file('../platform_new.keystore')
            storePassword 'android'
        }
        wiseposplus {
            keyAlias 'platform_WisePosPlus'
            keyPassword 'bbpos123456'
            storeFile file('../platform_WisePosPlus.keystore')
            storePassword 'bbpos123456'
        }
        appconfig {
            keyAlias 'wiseapp'
            keyPassword 'bbpos123456'
            storeFile file('../WiseApp.keystore')
            storePassword 'bbpos123456'
        }
        wiseposgo {
            keyAlias 'platform'
            keyPassword 'android'
            storeFile file('../platform_WisePosGo.keystore')
            storePassword 'android'
        }
        d30 {
            keyAlias "platform"
            storeFile file("../platform_d30.jks")
            storePassword "android"
            keyPassword "android"
        }
    }
    compileSdkVersion 28
    buildToolsVersion '28.0.3'

    defaultConfig {
        applicationId "com.bbpos.wiseapp.service"
        minSdkVersion 21
        targetSdkVersion 28
        versionCode 251
        versionName "25.1.0." + versionTag() + ".WISEAPP.SERVICE.DEMO"
        flavorDimensions "versionCode"
    }
    buildTypes {
        release {
            minifyEnabled true
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
            signingConfig signingConfigs.appconfig
        }
        debug {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
            signingConfig signingConfigs.d30
        }
    }

    externalNativeBuild {
        cmake {
            path "CMakeLists.txt"
        }
    }
    packagingOptions {
        exclude 'META-INF/DEPENDENCIES'
        exclude 'META-INF/NOTICE'
        exclude 'META-INF/LICENSE'
        exclude 'META-INF/LICENSE.txt'
        exclude 'META-INF/NOTICE.txt'
    }
    lintOptions {
        checkReleaseBuilds false
        // Or, if you prefer, you can continue to check for errors in release builds,
        // but continue the build even when errors are found:
        abortOnError false
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
    buildFeatures {
        aidl true
    }
    ndkVersion '23.1.7779620'
    android.applicationVariants.all {
        variant ->
            variant.outputs.all {
                //这里修改apk文件名
                def suffix = ''
                if (variant.buildType.name.equals('release')) {
                    suffix = ''
                } else if (variant.buildType.name.equals('debug')) {
                    suffix = '_debug'
                }
                outputFileName = "service_v${defaultConfig.versionName}${suffix}.apk"
            }
    }
}

static def versionTag() {
    return new Date().format("yyyyMMdd", TimeZone.getTimeZone("Asia/Shanghai"))
}

dependencies {
    implementation fileTree(include: ['*.jar'], dir: 'libs')
    implementation files('libs/bbdevice-android-3.28.0.jar')
    implementation 'androidx.appcompat:appcompat:1.0.0'
    implementation 'com.google.code.gson:gson:2.8.9'
    compileOnly files('libs/bbpos_android.jar')

    implementation project(':WebSocket')
}