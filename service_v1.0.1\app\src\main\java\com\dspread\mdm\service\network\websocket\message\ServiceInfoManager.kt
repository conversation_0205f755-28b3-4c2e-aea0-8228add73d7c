package com.dspread.mdm.service.network.websocket.message

import org.json.JSONArray
import org.json.JSONObject
import java.util.concurrent.ConcurrentHashMap

/**
 * 服务信息管理器
 * 管理当前活跃的服务信息，用于C0901消息中的serviceInfo字段
 */
object ServiceInfoManager {
    
    private const val TAG = "[ServiceInfoManager]"
    
    // 存储当前活跃的服务信息
    private val activeServices = ConcurrentHashMap<String, JSONObject>()
    
    /**
     * 添加或更新服务信息
     */
    fun addOrUpdateService(serviceId: String, serviceInfo: JSONObject) {
        try {
            activeServices[serviceId] = serviceInfo
        } catch (e: Exception) {
            // 静默处理异常，避免影响主流程
        }
    }
    
    /**
     * 移除服务信息
     */
    fun removeService(serviceId: String) {
        try {
            activeServices.remove(serviceId)
        } catch (e: Exception) {
            // 静默处理异常
        }
    }

    /**
     * 获取指定服务信息
     */
    fun getService(serviceId: String): JSONObject? {
        return try {
            activeServices[serviceId]
        } catch (e: Exception) {
            null
        }
    }
    
    /**
     * 获取所有活跃服务信息的JSONArray
     * 用于C0901消息的serviceInfo字段
     */
    fun getActiveServicesArray(): JSONArray {
        return try {
            val serviceArray = JSONArray()
            activeServices.values.forEach { serviceInfo ->
                serviceArray.put(serviceInfo)
            }
            serviceArray
        } catch (e: Exception) {
            // 出错时返回空数组
            JSONArray()
        }
    }
    
    /**
     * 清空所有服务信息
     */
    fun clearAllServices() {
        try {
            activeServices.clear()
        } catch (e: Exception) {
            // 静默处理异常
        }
    }
    
    /**
     * 获取服务数量
     */
    fun getServiceCount(): Int {
        return try {
            activeServices.size
        } catch (e: Exception) {
            0
        }
    }
}
