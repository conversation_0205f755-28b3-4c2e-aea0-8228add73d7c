package com.bbpos.wiseapp.websocket;

import android.net.ConnectivityManager;
import android.text.TextUtils;

import com.bbpos.wiseapp.logger.BBLog;
import com.bbpos.wiseapp.network.WifiContivityManager;
import com.bbpos.wiseapp.service.common.UsualData;
import com.bbpos.wiseapp.settings.utils.WirelessUtil;
import com.bbpos.wiseapp.system.api.DeviceInfoApi;
import com.bbpos.wiseapp.tms.location.CellLocationManager;
import com.bbpos.wiseapp.tms.location.GpsLocationManager;
import com.bbpos.wiseapp.tms.location.WifiLocationManager;
import com.bbpos.wiseapp.tms.model.AppInfo;
import com.bbpos.wiseapp.tms.utils.Constants;
import com.bbpos.wiseapp.tms.utils.ContextUtil;
import com.bbpos.wiseapp.tms.utils.Helpers;
import com.bbpos.wiseapp.tms.utils.ParameterFactory;
import com.bbpos.wiseapp.tms.utils.ParameterName;
import com.bbpos.wiseapp.tms.utils.WSCallType;
import com.bbpos.wiseapp.tms.utils.WebSocketServiceListManager;
import com.bbpos.wiseapp.tms.utils.WebSocketTaskListManager;
import com.bbpos.wiseapp.utils.SharedPreferencesUtils;
import com.bbpos.wiseapp.websocket.handler.RulebasedListHandler;

import org.json.JSONArray;
import org.json.JSONObject;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Locale;

public class WebSocketSender {
    public static final String TAG = "WebSocketSender";
//    static Handler mHandler = new Handler(Looper.getMainLooper());
    public static boolean waiting_storeId = false;      //等待SR002返回StoreId list
    public static boolean bRequestCR001StoreId = false;  //是否需要请求StoreId
    public static boolean bRequestCR001PairedStoreId = false;    //是否請求過匹配的StoreID；
    public static boolean bRequestCR002StoreIdConfirm = false;  //是否需要请求StoreId
    public static boolean bRequestCR003AppDownload = false;    //是否請求過匹配的StoreID；
    public static boolean bResponeSR003AppDownload = false;    //是否請求過匹配的StoreID；
    public static String pairedBy  = "";    //是否請求過匹配的StoreID；

    public static void C0000_wsRespone(String request_id, String request_time, String response_state, String response_remark) {
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put(ParameterName.tranCode, WSCallType.WS_RESPONE);
            jsonObject.put(ParameterName.serialNo_, DeviceInfoApi.getIntance().getSerialNumber());
            String taskTimestamp = String.valueOf(System.currentTimeMillis());
            jsonObject.put(ParameterName.request_time, taskTimestamp);
            jsonObject.put(ParameterName.request_id, taskTimestamp + WSCallType.WS_RESPONE);
            jsonObject.put(ParameterName.version, "1");
            jsonObject.put(ParameterName.org_request_id, request_id);
            jsonObject.put(ParameterName.org_request_time, request_time);
            jsonObject.put(ParameterName.response_state, response_state);
            jsonObject.put(ParameterName.response_remark, response_remark);

            jsonObject.put(ParameterName.myVersionName, Constants.U_MY_VERSION);
            jsonObject.put(ParameterName.terminalDate, Helpers.getTransDateStr(new Date()));
        } catch (Exception e) {
            e.printStackTrace();
        }
        WebSocketCenter.sendMessage(jsonObject.toString(), true);
    }

    public static void heartBeat() {
        WebSocketCenter.sendMessage("1", false);
    }

    public static void C0108_uploadWSServiceAppResult(JSONObject serviceJsonObj, String taskResult,String errorMsg) {
        if (serviceJsonObj==null || TextUtils.isEmpty(serviceJsonObj.toString())) {
            return;
        }
        BBLog.i(TAG, "C0108_uploadWSServiceAppResult" + serviceJsonObj.toString());
        String final_taskid = "";
        JSONObject jsonObject = new JSONObject();
        JSONObject data = new JSONObject();
        try {
            String appId = null;
            if(serviceJsonObj.has(ParameterName.appId)){
                appId = serviceJsonObj.getString(ParameterName.appId);
                data.put(ParameterName.appId, appId);
            }
            String taskId = serviceJsonObj.getString(ParameterName.taskId);
            final_taskid = taskId;
            String stateDesc = serviceJsonObj.getString(ParameterName.stateDesc);
            data.put(ParameterName.taskId, taskId);
            data.put(ParameterName.taskResult, taskResult);
            data.put(ParameterName.stateDesc, stateDesc);
            if (!TextUtils.isEmpty(errorMsg)) {
                data.put(ParameterName.errorMsg, errorMsg);
            }

            jsonObject.put(ParameterName.tranCode, WSCallType.TASK_RESULT_UPLOAD);
            jsonObject.put(ParameterName.serialNo_, DeviceInfoApi.getIntance().getSerialNumber());
            String taskTimestamp = String.valueOf(System.currentTimeMillis());
            jsonObject.put(ParameterName.request_time, taskTimestamp);
            jsonObject.put(ParameterName.request_id, taskTimestamp + WSCallType.TASK_RESULT_UPLOAD);
            jsonObject.put(ParameterName.version, "1");
            jsonObject.put(ParameterName.DATA, data);
            if (serviceJsonObj.has(ParameterName.request_id)) {
                jsonObject.put(ParameterName.org_request_id, serviceJsonObj.getString(ParameterName.request_id));
            }
            if (serviceJsonObj.has(ParameterName.request_time)) {
                jsonObject.put(ParameterName.org_request_time, serviceJsonObj.getString(ParameterName.request_time));
            }

            jsonObject.put(ParameterName.myVersionName, Constants.U_MY_VERSION);
            jsonObject.put(ParameterName.terminalDate, Helpers.getTransDateStr(new Date()));
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (jsonObject.length()>0 && jsonObject.has(ParameterName.request_id)) {
            WebSocketTaskListManager.addWSTaskResultJsonObj(final_taskid, jsonObject);
        }
        WebSocketCenter.sendMessage(jsonObject.toString(), true);
    }

    public static void C0108_uploadWSTaskResult(String task_id, String errorMsg) {
        JSONObject taskJsonObj = WebSocketTaskListManager.getWSTaskJsonObjById(task_id);
		if (taskJsonObj==null || TextUtils.isEmpty(taskJsonObj.toString())) {
            return;
        }
        BBLog.i(TAG, "C0108_uploadWSTaskResult" + taskJsonObj.toString());
        JSONObject jsonObject = new JSONObject();
        JSONObject data = new JSONObject();
        try {
            String appId = null;
            if(taskJsonObj.has(ParameterName.appId)){
                appId = taskJsonObj.getString(ParameterName.appId);
                data.put(ParameterName.appId, appId);
            }
            String otaId = null;
            if (taskJsonObj.has(ParameterName.otaId)){
                otaId = taskJsonObj.getString(ParameterName.otaId);
                data.put(ParameterName.otaId, otaId);
            }
            String taskId = taskJsonObj.getString(ParameterName.taskId);
            String taskResult = taskJsonObj.getString(ParameterName.taskResult);
            data.put(ParameterName.taskId, taskId);
            data.put(ParameterName.taskResult, taskResult);
            if (!TextUtils.isEmpty(errorMsg)) {
                data.put(ParameterName.errorMsg, errorMsg);
            }
            String relativeId = null;
            if (taskJsonObj.has(ParameterName.relativeId)) {
                relativeId = taskJsonObj.getString(ParameterName.relativeId);
                data.put(ParameterName.relativeId, relativeId);
            }

            jsonObject.put(ParameterName.tranCode, WSCallType.TASK_RESULT_UPLOAD);
            jsonObject.put(ParameterName.serialNo_, DeviceInfoApi.getIntance().getSerialNumber());
            String taskTimestamp = String.valueOf(System.currentTimeMillis());
            jsonObject.put(ParameterName.request_time, taskTimestamp);
            jsonObject.put(ParameterName.request_id, taskTimestamp + WSCallType.TASK_RESULT_UPLOAD);
            jsonObject.put(ParameterName.version, "1");
            jsonObject.put(ParameterName.DATA, data);
            if (taskJsonObj.has(ParameterName.request_id)) {
                jsonObject.put(ParameterName.org_request_id, taskJsonObj.getString(ParameterName.request_id));
            }
            if (taskJsonObj.has(ParameterName.request_time)) {
                jsonObject.put(ParameterName.org_request_time, taskJsonObj.getString(ParameterName.request_time));
            }

            jsonObject.put(ParameterName.myVersionName, Constants.U_MY_VERSION);
            jsonObject.put(ParameterName.terminalDate, Helpers.getTransDateStr(new Date()));
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (jsonObject.length()>0 && jsonObject.has(ParameterName.request_id)) {
            WebSocketTaskListManager.addWSTaskResultJsonObj(task_id, jsonObject);
        }
        WebSocketCenter.sendMessage(jsonObject.toString(), true);
    }

    public static void C0107_uploadRulebasedResult(String rule_id, String result, JSONArray failedList) {
        JSONObject rulebased = RulebasedListHandler.getWSRuleJsonObjById(rule_id);
        if (rulebased==null || TextUtils.isEmpty(rulebased.toString())) {
          return;
        }
        BBLog.i(TAG, "C0107_uploadRulebasedResult: " + rule_id);
        JSONObject jsonObject = new JSONObject();
        JSONObject data = new JSONObject();
        try {
            data.put(ParameterName.ruleId, rulebased.getString(ParameterName.ruleId));
            data.put(ParameterName.taskResult, result);
            if (failedList!=null) {
                data.put(ParameterName.failedApkList, failedList);
            }

            jsonObject.put(ParameterName.tranCode, WSCallType.RULEBASED_RESULT_UPLOAD);
            jsonObject.put(ParameterName.serialNo_, DeviceInfoApi.getIntance().getSerialNumber());
            String taskTimestamp = String.valueOf(System.currentTimeMillis());
            jsonObject.put(ParameterName.request_time, taskTimestamp);
            jsonObject.put(ParameterName.request_id, taskTimestamp + WSCallType.RULEBASED_RESULT_UPLOAD);
            jsonObject.put(ParameterName.version, "1");
            jsonObject.put(ParameterName.DATA, data);
            if (rulebased.has(ParameterName.request_id)) {
                jsonObject.put(ParameterName.org_request_id, rulebased.getString(ParameterName.request_id));
            }
            if (rulebased.has(ParameterName.request_time)) {
                jsonObject.put(ParameterName.org_request_time, rulebased.getString(ParameterName.request_time));
            }

            jsonObject.put(ParameterName.myVersionName, Constants.U_MY_VERSION);
            jsonObject.put(ParameterName.terminalDate, Helpers.getTransDateStr(new Date()));
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (jsonObject.length()>0 && jsonObject.has(ParameterName.request_id)) {
            RulebasedListHandler.addRuleResultJsonObj(rule_id, jsonObject);
        }
        WebSocketCenter.sendMessage(jsonObject.toString(), true);
    }

    //关于requestStoreId，只需要执行一次，但是需要依赖定位成功；
    //bNeedRequestStoreId的作用：1、收到WIFI_FOUND，则为true，需要发送；2、如果超时，则为false不需要发送了；3、如果收到STORE_SELECTED为用户已手输，也不需要发送了
    public static void CR001_requestStoreId() {
//        int count = 0;
        BBLog.w(TAG, "CR001_requestStoreId()");
        if (bRequestCR001PairedStoreId && WebSocketCenter.isWebSocketConnected) {
            WebSocketSender.C0201_DeviceStatusUpload("2", Constants.IS_IN_USE);
            SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_UNBOX_STATUS, "2");

            JSONObject jsonObject = new JSONObject();
            JSONObject data = new JSONObject();
            JSONObject gps = new JSONObject();
            try {
                gps.put(ParameterName.latitude, GpsLocationManager.getLatitudeStr());
                gps.put(ParameterName.longitude, GpsLocationManager.getLongitudeStr());
                data.put(ParameterName.gps, gps);
                data.put(ParameterName.pairedBy, "1");

                jsonObject.put(ParameterName.tranCode, WSCallType.GET_STORE_ID);
                jsonObject.put(ParameterName.serialNo_, DeviceInfoApi.getIntance().getSerialNumber());
                String taskTimestamp = String.valueOf(System.currentTimeMillis());
                jsonObject.put(ParameterName.request_time, taskTimestamp);
                jsonObject.put(ParameterName.request_id, taskTimestamp + WSCallType.GET_STORE_ID);
                jsonObject.put(ParameterName.version, "1");
                jsonObject.put(ParameterName.DATA, data);

                jsonObject.put(ParameterName.myVersionName, Constants.U_MY_VERSION);
                jsonObject.put(ParameterName.terminalDate, Helpers.getTransDateStr(new Date()));
            } catch (Exception e) {
                e.printStackTrace();
            }
            waiting_storeId = true;
            WebSocketCenter.sendMessage(jsonObject.toString(), true);
            bRequestCR001PairedStoreId = false;
        }
        if (bRequestCR001StoreId && GpsLocationManager.isGpsValid() && WebSocketCenter.isWebSocketConnected) {
            JSONObject jsonObject = new JSONObject();
            JSONObject data = new JSONObject();
            JSONObject gps = new JSONObject();
            try {
                gps.put(ParameterName.latitude, GpsLocationManager.getLatitudeStr());
                gps.put(ParameterName.longitude, GpsLocationManager.getLongitudeStr());
                data.put(ParameterName.gps, gps);
                data.put(ParameterName.pairedBy, "0");

                jsonObject.put(ParameterName.tranCode, WSCallType.GET_STORE_ID);
                jsonObject.put(ParameterName.serialNo_, DeviceInfoApi.getIntance().getSerialNumber());
                String taskTimestamp = String.valueOf(System.currentTimeMillis());
                jsonObject.put(ParameterName.request_time, taskTimestamp);
                jsonObject.put(ParameterName.request_id, taskTimestamp + WSCallType.GET_STORE_ID);
                jsonObject.put(ParameterName.version, "1");
                jsonObject.put(ParameterName.DATA, data);

                jsonObject.put(ParameterName.myVersionName, Constants.U_MY_VERSION);
                jsonObject.put(ParameterName.terminalDate, Helpers.getTransDateStr(new Date()));
            } catch (Exception e) {
                e.printStackTrace();
            }
            waiting_storeId = true;
            WebSocketCenter.sendMessage(jsonObject.toString(), true);
            bRequestCR001StoreId = false;
        }
    }

    public static void CR003_requestPreInstallAppList() {
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put(ParameterName.tranCode, WSCallType.GET_PREINSTALL_APPLIST);
            jsonObject.put(ParameterName.serialNo_, DeviceInfoApi.getIntance().getSerialNumber());
            String taskTimestamp = String.valueOf(System.currentTimeMillis());
            jsonObject.put(ParameterName.request_time, taskTimestamp);
            jsonObject.put(ParameterName.request_id, taskTimestamp + WSCallType.GET_PREINSTALL_APPLIST);
            jsonObject.put(ParameterName.version, "1");

            jsonObject.put(ParameterName.myVersionName, Constants.U_MY_VERSION);
            jsonObject.put(ParameterName.terminalDate, Helpers.getTransDateStr(new Date()));
        } catch (Exception e) {
            e.printStackTrace();
        }
        WebSocketCenter.sendMessage(jsonObject.toString(), true);
    }

    public static void CR002_responeStoreId(String storeId, String networkAddress, String pairedBy) {
        JSONObject jsonObject = new JSONObject();
        JSONObject data = new JSONObject();
        try {
            data.put(ParameterName.storeId, storeId);
            data.put(ParameterName.networkAddress, networkAddress);
            data.put(ParameterName.pairedBy, pairedBy);

            jsonObject.put(ParameterName.tranCode, WSCallType.UPLOAD_STORE_ID);
            jsonObject.put(ParameterName.serialNo_, DeviceInfoApi.getIntance().getSerialNumber());
            String taskTimestamp = String.valueOf(System.currentTimeMillis());
            jsonObject.put(ParameterName.request_time, taskTimestamp);
            jsonObject.put(ParameterName.request_id, taskTimestamp + WSCallType.UPLOAD_STORE_ID);
            jsonObject.put(ParameterName.version, "1");
            jsonObject.put(ParameterName.DATA, data);

            jsonObject.put(ParameterName.myVersionName, Constants.U_MY_VERSION);
            jsonObject.put(ParameterName.terminalDate, Helpers.getTransDateStr(new Date()));
        } catch (Exception e) {
            e.printStackTrace();
        }
        WebSocketCenter.sendMessage(jsonObject.toString(), true);
    }

    public static void CR004_checkOSUpdate(String taskid) {
        JSONObject jsonObject = new JSONObject();
        JSONObject data = new JSONObject();
        try {
            data.put(ParameterName.buildNumber, DeviceInfoApi.getIntance().getCustomVersion());
            data.put(ParameterName.relativeId, taskid);

            jsonObject.put(ParameterName.tranCode, WSCallType.CHECK_OS_UPDATE);
            jsonObject.put(ParameterName.serialNo_, DeviceInfoApi.getIntance().getSerialNumber());
            String taskTimestamp = String.valueOf(System.currentTimeMillis());
            jsonObject.put(ParameterName.request_time, taskTimestamp);
            jsonObject.put(ParameterName.request_id, taskTimestamp + WSCallType.CHECK_OS_UPDATE);
            jsonObject.put(ParameterName.version, "1");
            jsonObject.put(ParameterName.DATA, data);

            jsonObject.put(ParameterName.myVersionName, Constants.U_MY_VERSION);
            jsonObject.put(ParameterName.terminalDate, Helpers.getTransDateStr(new Date()));
        } catch (Exception e) {
            e.printStackTrace();
        }
        WebSocketCenter.sendMessage(jsonObject.toString(), true);
    }

    public static void CC004_unboxResetDone(String org_request_id, String org_request_time, String resultCode, String resultMsg) {
        JSONObject jsonObject = new JSONObject();
        JSONObject data = new JSONObject();
        try {
            data.put(ParameterName.resultCode, resultCode);
            data.put(ParameterName.resultMsg, resultMsg);

            jsonObject.put(ParameterName.tranCode, WSCallType.UNBOX_RESET_DONE);
            jsonObject.put(ParameterName.serialNo_, DeviceInfoApi.getIntance().getSerialNumber());
            String taskTimestamp = String.valueOf(System.currentTimeMillis());
            jsonObject.put(ParameterName.request_time, taskTimestamp);
            jsonObject.put(ParameterName.request_id, taskTimestamp + WSCallType.UNBOX_RESET_DONE);
            jsonObject.put(ParameterName.version, "1");
            jsonObject.put(ParameterName.org_request_id, org_request_id);
            jsonObject.put(ParameterName.org_request_time, org_request_time);
            jsonObject.put(ParameterName.DATA, data);

            jsonObject.put(ParameterName.myVersionName, Constants.U_MY_VERSION);
            jsonObject.put(ParameterName.terminalDate, Helpers.getTransDateStr(new Date()));
        } catch (Exception e) {
            e.printStackTrace();
        }
        WebSocketCenter.sendMessage(jsonObject.toString(), true);
    }

    public static void C0902_BatteryStatusUpload(int batLevel, int batHealth, int temperature, boolean isCharging, boolean isLowBattery, boolean powerOff, boolean outOfBattery) {
        if (batLevel==0 && temperature==0) {
            return;
        }

        JSONObject jsonObject = new JSONObject();
        JSONObject data = new JSONObject();
        try {
            data.put(ParameterName.batLevel, batLevel);
            data.put(ParameterName.batHealth, batHealth);
            float temp = temperature;
            data.put(ParameterName.temprature, String.format(Locale.US, "%.1f", temp/10.0));
            if (isCharging) {
                data.put(ParameterName.isCharging, "1");
            } else {
                data.put(ParameterName.isCharging, "0");
            }
            if (isLowBattery) {
                data.put(ParameterName.isLowBattery, "1");
            } else {
                data.put(ParameterName.isLowBattery, "0");
            }
            if (powerOff) {
                data.put(ParameterName.powerOff, "1");
            } else {
                data.put(ParameterName.powerOff, "0");
            }
            if (outOfBattery) {
                data.put(ParameterName.outOfBattery, "1");
            } else {
                data.put(ParameterName.outOfBattery, "0");
            }

            jsonObject.put(ParameterName.tranCode, WSCallType.BATTERY_STATUS_UPLOAD);
            jsonObject.put(ParameterName.serialNo_, DeviceInfoApi.getIntance().getSerialNumber());
            String taskTimestamp = String.valueOf(System.currentTimeMillis());
            jsonObject.put(ParameterName.request_time, taskTimestamp);
            jsonObject.put(ParameterName.request_id, taskTimestamp + WSCallType.BATTERY_STATUS_UPLOAD);
            jsonObject.put(ParameterName.version, "1");
            jsonObject.put(ParameterName.DATA, data);

            jsonObject.put(ParameterName.myVersionName, Constants.U_MY_VERSION);
            jsonObject.put(ParameterName.terminalDate, Helpers.getTransDateStr(new Date()));
        } catch (Exception e) {
            e.printStackTrace();
        }
        WebSocketCenter.sendMessage(jsonObject.toString(), true);
    }

    public static void C0201_DeviceStatusUpload(String deviceStatus, boolean isInUse) {
        if (Constants.IS_UNBOX_RESETTING) {
            return;
        }

        JSONObject jsonObject = new JSONObject();
        JSONObject data = new JSONObject();
        try {
            data.put(ParameterName.unboxStatus, deviceStatus);

            if (isInUse) {
                data.put(ParameterName.isInUse, "1");
            } else {
                data.put(ParameterName.isInUse, "0");
            }

            data.put("websocketConnected", WebSocketCenter.isWebSocketConnected);
            data.put("loginSuccess", WebSocketReceiver.getLoginSuccessFlag());
            data.put("unboxing", Constants.B_UNBOX_RUNNING);
            data.put(ParameterName.myVersionName, Constants.U_MY_VERSION);
            data.put(ParameterName.terminalDate, Helpers.getTransDateStr(new Date()));

            jsonObject.put(ParameterName.tranCode, WSCallType.DEVICE_STATUE_UPLOAD);
            jsonObject.put(ParameterName.serialNo_, DeviceInfoApi.getIntance().getSerialNumber());
            String taskTimestamp = String.valueOf(System.currentTimeMillis());
            jsonObject.put(ParameterName.request_time, taskTimestamp);
            jsonObject.put(ParameterName.request_id, taskTimestamp + WSCallType.DEVICE_STATUE_UPLOAD);
            jsonObject.put(ParameterName.version, "1");
            jsonObject.put(ParameterName.DATA, data);

            jsonObject.put(ParameterName.myVersionName, Constants.U_MY_VERSION);
            jsonObject.put(ParameterName.terminalDate, Helpers.getTransDateStr(new Date()));
        } catch (Exception e) {
            e.printStackTrace();
        }
        WebSocketCenter.sendMessage(jsonObject.toString(), true);
    }

    public static void C0202_DeviceEventUpload(String eventType, String eventDesc, List<AppInfo> appInfoList) {
        JSONObject jsonObject = new JSONObject();
        JSONObject data = new JSONObject();
        JSONArray appInfoArray = new JSONArray();
        try {
            data.put(ParameterName.eventType, eventType);
            data.put(ParameterName.eventTime, System.currentTimeMillis());
            data.put(ParameterName.eventDesc, eventDesc);
            data.put(ParameterName.formatDate, new SimpleDateFormat("yyyyMMdd").format(new Date(System.currentTimeMillis())));

            if (appInfoList != null) {
                for (int i = 0; i < appInfoList.size(); i ++) {
                    JSONObject appInfo = new JSONObject();
                    appInfo.put(ParameterName.apkName, appInfoList.get(i).getApk_name());
                    appInfo.put(ParameterName.packName, appInfoList.get(i).getPackage_name());
                    appInfo.put(ParameterName.versionName, appInfoList.get(i).getVersion_name());
                    appInfo.put(ParameterName.versionCode, appInfoList.get(i).getVersion_code());
                    appInfo.put(ParameterName.crashTime, appInfoList.get(i).getCrash_info().getDate());
                    appInfo.put(ParameterName.crashCount, appInfoList.get(i).getCrash_Count());
                    appInfo.put(ParameterName.crashInfo, appInfoList.get(i).getCrash_info().getInfo());
                    appInfoArray.put(appInfo);
                }
                data.put(ParameterName.apkInfoList, appInfoArray);
            }

            jsonObject.put(ParameterName.tranCode, WSCallType.DEVICE_EVENT_UPLOAD);
            jsonObject.put(ParameterName.serialNo_, DeviceInfoApi.getIntance().getSerialNumber());
            String taskTimestamp = String.valueOf(System.currentTimeMillis());
            jsonObject.put(ParameterName.request_time, taskTimestamp);
            jsonObject.put(ParameterName.request_id, taskTimestamp + WSCallType.DEVICE_EVENT_UPLOAD);
            jsonObject.put(ParameterName.version, "1");
            jsonObject.put(ParameterName.DATA, data);

            jsonObject.put(ParameterName.myVersionName, Constants.U_MY_VERSION);
            jsonObject.put(ParameterName.terminalDate, Helpers.getTransDateStr(new Date()));
        } catch (Exception e) {
            e.printStackTrace();
        }
        WebSocketCenter.sendMessage(jsonObject.toString(), true);
    }

    public static void C0904_NetworkStatusUpload() {
        JSONObject jsonObject = new JSONObject();
        JSONObject data = new JSONObject();
        try {
            WifiLocationManager.getInstance(ContextUtil.getInstance()).scanWifi();
            Thread.sleep(5000);
            CellLocationManager cellManager = new CellLocationManager(ContextUtil.getInstance());

            data.put(ParameterName.baseSite, cellManager.getLocationJsonCell());
            data.put(ParameterName.wifi, WifiLocationManager.getInstance(ContextUtil.getInstance()).getLocationJsonWifiFirst());
            data.put(ParameterName.wifiContivity, WifiContivityManager.getInstance().getContivityList(ConnectivityManager.TYPE_WIFI));
            data.put(ParameterName.mobileContivity, WifiContivityManager.getInstance().getContivityList(ConnectivityManager.TYPE_MOBILE));

            jsonObject.put(ParameterName.tranCode, WSCallType.NETWORK_STATUS_UPLOAD);
            jsonObject.put(ParameterName.serialNo_, DeviceInfoApi.getIntance().getSerialNumber());
            String taskTimestamp = String.valueOf(System.currentTimeMillis());
            jsonObject.put(ParameterName.request_time, taskTimestamp);
            jsonObject.put(ParameterName.request_id, taskTimestamp + WSCallType.NETWORK_STATUS_UPLOAD);
            jsonObject.put(ParameterName.version, "1");
            jsonObject.put(ParameterName.DATA, data);

            jsonObject.put(ParameterName.myVersionName, Constants.U_MY_VERSION);
            jsonObject.put(ParameterName.terminalDate, Helpers.getTransDateStr(new Date()));
        } catch (Exception e) {
            e.printStackTrace();
        }
        WebSocketCenter.sendMessage(jsonObject.toString(), true);
        WifiContivityManager.getInstance().clearContivityInfos();
    }

    public static void C0901_AppInfoUpload() {
        JSONObject jsonObject = new JSONObject();
        JSONObject data = new JSONObject();
        try {
            data.put(ParameterName.apkInfoInTer, ParameterFactory.createApkInfoInTer(null));
            data.put(ParameterName.serviceInfo, WebSocketServiceListManager.uploadServiceAppList());
			data.put(ParameterName.sytemInfo, ParameterFactory.createSystemInfo());

            jsonObject.put(ParameterName.tranCode, WSCallType.APPS_INFO_UPLOAD);
            jsonObject.put(ParameterName.serialNo_, DeviceInfoApi.getIntance().getSerialNumber());
            String taskTimestamp = String.valueOf(System.currentTimeMillis());
            jsonObject.put(ParameterName.request_time, taskTimestamp);
            jsonObject.put(ParameterName.request_id, taskTimestamp + WSCallType.APPS_INFO_UPLOAD);
            jsonObject.put(ParameterName.version, "1");
            jsonObject.put(ParameterName.DATA, data);

            jsonObject.put(ParameterName.myVersionName, Constants.U_MY_VERSION);
            jsonObject.put(ParameterName.terminalDate, Helpers.getTransDateStr(new Date()));
        } catch (Exception e) {
            e.printStackTrace();
        }
        WebSocketCenter.sendMessage(jsonObject.toString(), true);
    }

    public static void C0903_DataInfoUpload() {
        JSONObject jsonObject = new JSONObject();
        JSONObject data = new JSONObject();
        try {
            data.put(ParameterName.hardwareInfoTer, ParameterFactory.createTerHardwareInfo());
            data.put(ParameterName.locationInfoInTer, ParameterFactory.creatLocationInfoInTer(ContextUtil.getInstance()));
            data.put(ParameterName.cardDeviceUsage, ParameterFactory.createCardDeviceUsage(ContextUtil.getInstance()));

            jsonObject.put(ParameterName.tranCode, WSCallType.DATA_INFO_UPLOAD);
            jsonObject.put(ParameterName.serialNo_, DeviceInfoApi.getIntance().getSerialNumber());
            String taskTimestamp = String.valueOf(System.currentTimeMillis());
            jsonObject.put(ParameterName.request_time, taskTimestamp);
            jsonObject.put(ParameterName.request_id, taskTimestamp + WSCallType.DATA_INFO_UPLOAD);
            jsonObject.put(ParameterName.version, "1");
            jsonObject.put(ParameterName.DATA, data);

            jsonObject.put(ParameterName.myVersionName, Constants.U_MY_VERSION);
            jsonObject.put(ParameterName.terminalDate, Helpers.getTransDateStr(new Date()));
        } catch (Exception e) {
            e.printStackTrace();
        }
        WebSocketCenter.sendMessage(jsonObject.toString(), true);
    }

    public static void C0903_DataInfoUpload(String cpu_usage, String rom_usage, String mem_usage) {
        JSONObject jsonObject = new JSONObject();
        JSONObject data = new JSONObject();
        try {
            data.put(ParameterName.hardwareInfoTer, ParameterFactory.createTerHardwareInfo(cpu_usage, rom_usage, mem_usage));
            data.put(ParameterName.locationInfoInTer, ParameterFactory.creatLocationInfoInTer(ContextUtil.getInstance()));
            data.put(ParameterName.cardDeviceUsage, ParameterFactory.createCardDeviceUsage(ContextUtil.getInstance()));

            jsonObject.put(ParameterName.tranCode, WSCallType.DATA_INFO_UPLOAD);
            jsonObject.put(ParameterName.serialNo_, DeviceInfoApi.getIntance().getSerialNumber());
            String taskTimestamp = String.valueOf(System.currentTimeMillis());
            jsonObject.put(ParameterName.request_time, taskTimestamp);
            jsonObject.put(ParameterName.request_id, taskTimestamp + WSCallType.DATA_INFO_UPLOAD);
            jsonObject.put(ParameterName.version, "1");
            jsonObject.put(ParameterName.DATA, data);

            jsonObject.put(ParameterName.myVersionName, Constants.U_MY_VERSION);
            jsonObject.put(ParameterName.terminalDate, Helpers.getTransDateStr(new Date()));
        } catch (Exception e) {
            e.printStackTrace();
        }
        WebSocketCenter.sendMessage(jsonObject.toString(), true);
    }

    public static void C0903_DataInfoUploadForGeo(long timestamp) {
        JSONObject jsonObject = new JSONObject();
        JSONObject data = new JSONObject();
        try {
            JSONObject locationJSONObj = new JSONObject();
            locationJSONObj.put("gps", GpsLocationManager.getGpsLocation());
            data.put(ParameterName.locationInfoInTer, locationJSONObj);

            jsonObject.put(ParameterName.tranCode, WSCallType.DATA_INFO_UPLOAD);
            jsonObject.put(ParameterName.serialNo_, DeviceInfoApi.getIntance().getSerialNumber());
            String taskTimestamp = String.valueOf(timestamp);
            jsonObject.put(ParameterName.request_time, taskTimestamp);
            jsonObject.put(ParameterName.request_id, taskTimestamp + WSCallType.DATA_INFO_UPLOAD);
            jsonObject.put(ParameterName.version, "1");
            jsonObject.put(ParameterName.DATA, data);

            jsonObject.put(ParameterName.myVersionName, Constants.U_MY_VERSION);
            jsonObject.put(ParameterName.terminalDate, Helpers.getTransDateStr(new Date()));
        } catch (Exception e) {
            e.printStackTrace();
        }
        WebSocketCenter.sendMessage(jsonObject.toString(), true);
    }

	public static void C0906_DeviceInfoUpload(JSONObject deviceInfo) {
		JSONObject jsonObject = new JSONObject();
		JSONObject data = new JSONObject();
		try {
			data.put(ParameterName.firmware_info, deviceInfo);
            // TODO: 2021/5/7 eric 新增属性
            data.put(ParameterName.imei_1,DeviceInfoApi.getIntance().getIMEI(ContextUtil.getInstance(),0));
            data.put(ParameterName.imei_2,DeviceInfoApi.getIntance().getIMEI(ContextUtil.getInstance(),1));
            data.put(ParameterName.wifi_mac,WirelessUtil.getMac(ContextUtil.getInstance()));
            data.put(ParameterName.bt_mac, Constants.BT_MAC);
            data.put(ParameterName.bsn,DeviceInfoApi.getIntance().getBSN());

			jsonObject.put(ParameterName.tranCode, WSCallType.GET_DEVICE_BID_UPLOAD);
			jsonObject.put(ParameterName.serialNo_, DeviceInfoApi.getIntance().getSerialNumber());
			String taskTimestamp = String.valueOf(System.currentTimeMillis());
			jsonObject.put(ParameterName.request_time, taskTimestamp);
			jsonObject.put(ParameterName.request_id, taskTimestamp + WSCallType.GET_DEVICE_BID_UPLOAD);
			jsonObject.put(ParameterName.version, "1");
			jsonObject.put(ParameterName.DATA, data);

			jsonObject.put(ParameterName.myVersionName, Constants.U_MY_VERSION);
			jsonObject.put(ParameterName.terminalDate, Helpers.getTransDateStr(new Date()));
		} catch (Exception e) {
			e.printStackTrace();
		}
		WebSocketCenter.sendMessage(jsonObject.toString(), true);
	}

	public static void C0908_DeviceFullInfoUpload(String fullDeviceInfo) {
		JSONObject jsonObject = new JSONObject();
		JSONObject data = new JSONObject();
		try {
			data.put(ParameterName.device_info, new JSONObject(fullDeviceInfo));

			jsonObject.put(ParameterName.tranCode, WSCallType.GET_DEVICE_FULL_UPLOAD);
			jsonObject.put(ParameterName.serialNo_, DeviceInfoApi.getIntance().getSerialNumber());
			String taskTimestamp = String.valueOf(System.currentTimeMillis());
			jsonObject.put(ParameterName.request_time, taskTimestamp);
			jsonObject.put(ParameterName.request_id, taskTimestamp + WSCallType.GET_DEVICE_FULL_UPLOAD);
			jsonObject.put(ParameterName.version, "1");
			jsonObject.put(ParameterName.DATA, data);

			jsonObject.put(ParameterName.myVersionName, Constants.U_MY_VERSION);
			jsonObject.put(ParameterName.terminalDate, Helpers.getTransDateStr(new Date()));
		} catch (Exception e) {
			e.printStackTrace();
		}
		WebSocketCenter.sendMessage(jsonObject.toString(), true);
	}

    public static void C0203_GdsOsUpgradeStatusUpload(JSONObject osUpgradeResultData) {
        JSONObject jsonObject = new JSONObject();
        try {

            jsonObject.put(ParameterName.tranCode, WSCallType.DEVICE_GDS_OS_STATUS_UPLOAD);
            jsonObject.put(ParameterName.serialNo_, DeviceInfoApi.getIntance().getSerialNumber());
            String taskTimestamp = String.valueOf(System.currentTimeMillis());
            jsonObject.put(ParameterName.request_time, taskTimestamp);
            jsonObject.put(ParameterName.request_id, taskTimestamp + WSCallType.DEVICE_GDS_OS_STATUS_UPLOAD);
            jsonObject.put(ParameterName.version, "1");
            jsonObject.put(ParameterName.DATA, osUpgradeResultData);

            jsonObject.put(ParameterName.myVersionName, Constants.U_MY_VERSION);
            jsonObject.put(ParameterName.terminalDate, Helpers.getTransDateStr(new Date()));
        } catch (Exception e) {
            e.printStackTrace();
        }
        WebSocketCenter.sendMessage(jsonObject.toString(), true);
    }
}
