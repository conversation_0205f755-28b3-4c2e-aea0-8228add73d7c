package com.bbpos.wiseapp.websocket.handler;

import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;

import com.bbpos.wiseapp.logger.BBLog;
import com.bbpos.wiseapp.logger.LLQueue;
import com.bbpos.wiseapp.logstream.LogService;
import com.bbpos.wiseapp.service.common.RequestPermission;
import com.bbpos.wiseapp.service.common.UsualData;
import com.bbpos.wiseapp.settings.utils.WifiProxyUtil;
import com.bbpos.wiseapp.tms.service.DownloadService;
import com.bbpos.wiseapp.tms.utils.Constants;
import com.bbpos.wiseapp.tms.utils.ContextUtil;
import com.bbpos.wiseapp.tms.utils.Helpers;
import com.bbpos.wiseapp.tms.utils.ParameterName;
import com.bbpos.wiseapp.tms.utils.RuleStatus;
import com.bbpos.wiseapp.tms.utils.RulebasedAppListManager;
import com.bbpos.wiseapp.tms.utils.WebSocketServiceListManager;
import com.bbpos.wiseapp.websocket.WebSocketManager;
import com.bbpos.wiseapp.websocket.WebSocketReceiver;
import com.bbpos.wiseapp.websocket.WebSocketSender;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

public class RulebasedHandler {
    private static LLQueue<String> mRulebasedApkQueue = new LLQueue<>();
    private WebSocketManager webSocketManager;
    private Context mContext;

    public static String[] service_code = new String[] {
        "C01",
        "C02",
        "C03",
        "C04",
        "C05",
        "C06",
    };

    public RulebasedHandler(Context context, WebSocketManager manager) {
        mContext = context;
        webSocketManager = manager;
    }

    public void handleMsg(String response) {
        try {
            JSONArray appList = null;
            JSONArray serviceList = null;
            JSONObject responseJson = new JSONObject(response);
            JSONObject responseData = Helpers.getDataFromResponse(responseJson);
            if (responseData == null) {
                return;
            }
            BBLog.e(Constants.TAG, "RulebasedHandler = " + responseData.toString());
            if (responseData.has(ParameterName.appList)) {
                appList = responseData.getJSONArray(ParameterName.appList);
                RulebasedAppListManager.updateAppList(appList);

                Intent it = new Intent(UsualData.RULEBASED_EXEC_BC);
                it.putExtra("response", response);
                it.setComponent(new ComponentName(mContext.getPackageName(), WebSocketReceiver.class.getName()));
                it.setFlags(Intent.FLAG_INCLUDE_STOPPED_PACKAGES);
                mContext.sendBroadcast(it, RequestPermission.REQUEST_PERMISSION_MY_BROADCAST);
//                Helpers.sendBroad(mContext, UsualData.RULEBASED_EXEC_BC);
            }

            if (responseData.has(ParameterName.serviceList)) {
                serviceList = responseData.getJSONArray(ParameterName.serviceList);

                String requestId = responseJson.optString(ParameterName.request_id);
                String requestTime = responseJson.optString(ParameterName.request_time);

                WebSocketServiceListManager.updateServiceList(requestId, requestTime, serviceList, responseJson.optString(ParameterName.beginDate), responseJson.optString(ParameterName.endDate), RuleStatus.READY);
/*
                for (int i=0; i<service_code.length; i++) {
                    boolean found = false;
                    for (int j=0;j<serviceList.length();j++) {
                        JSONObject serviceJson = serviceList.getJSONObject(j);
                        if (service_code[i].equals(serviceJson.getString(ParameterName.command))) {
                            executeService(serviceJson);
                            found = true;
                            break;
                        }
                    }
                    if (!found) {
                        executeServiceReset(service_code[i]);
                    }
                }
*/
            }
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    private synchronized void updateQueue(JSONArray taskList) {
        try {
            mRulebasedApkQueue.clear();
            for (int i=0; i<taskList.length(); i++) {
                JSONObject jsonObject = (JSONObject) taskList.get(i);
                mRulebasedApkQueue.enQueue(jsonObject.toString());
            }
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    public static void executeServiceReset(String command) {
        switch (command) {
            case "C01":
                WifiProxyUtil.setMobileDataState(ContextUtil.getInstance(), false);
                break;
            case "C02":
                break;
            case "C03":
//                ContextUtil.stopLogService();
                LogService.stopLogServiceUpload();
                break;
        }
    }

    public static void executeService(JSONObject jsonObject) {
        try {
            BBLog.e(BBLog.TAG, "executeService 執行 Service App: " + jsonObject);
            String command = jsonObject.getString(ParameterName.command);
            String taskType = jsonObject.getString(ParameterName.taskType);
            switch (command) {
                case "C01":
                    if ("05".equals(taskType)) {
                        WifiProxyUtil.setMobileDataState(ContextUtil.getInstance(), true);
                    } else if ("06".equals(taskType)) {
                        WifiProxyUtil.setMobileDataState(ContextUtil.getInstance(), false);
                    }
                    break;
                case "C02":
                    WebSocketSender.CR004_checkOSUpdate(jsonObject.optString(ParameterName.taskId));
                    break;
                case "C03":
                    ContextUtil.startLogServiceScheduleJson(jsonObject);
                    break;
                case "C05":
                case "C06":
                case "C07":
                    Intent downloadService = new Intent(ContextUtil.getInstance(), DownloadService.class);
                    downloadService.putExtra("service_app", jsonObject.toString());
                    ContextUtil.getInstance().startService(downloadService);
                    break;
            }
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    public static void executeServiceFallback(JSONObject jsonObject) {
        try {
            String command = jsonObject.getString(ParameterName.command);
            String taskType = jsonObject.getString(ParameterName.taskType);
            switch (command) {
                case "C01":
                    if ("05".equals(taskType)) {
                        WifiProxyUtil.setMobileDataState(ContextUtil.getInstance(), false);
                    } else if ("06".equals(taskType)) {
                        WifiProxyUtil.setMobileDataState(ContextUtil.getInstance(), true);
                    }
                    break;
                case "C02":
//                    WebSocketSender.CR004_checkOSUpdate();
                    break;
                case "C03":
//                    ContextUtil.stopLogService();
                    LogService.stopLogServiceUpload();
                    break;
            }
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }
}
