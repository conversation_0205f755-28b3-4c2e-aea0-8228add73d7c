<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="fill_parent"
    android:layout_height="fill_parent"
    android:clickable="true"
    android:orientation="vertical"
    android:padding="20.0dip" >

    <LinearLayout
        android:layout_width="fill_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:background="@android:color/white"
        android:orientation="vertical" >

        <TextView
            android:id="@+id/title"
            style="@style/text_18_000000"
            android:layout_width="fill_parent"
            android:layout_height="40.0dip"
            android:gravity="center"
            android:text="标题"
            android:visibility="visible" />
        <View
            android:layout_width="fill_parent"
            android:layout_height="1.0px"
            android:background="#ffd0d0d0" />
        <LinearLayout
            android:id="@+id/content"
            android:layout_width="fill_parent"
            android:layout_height="wrap_content"
            android:padding="10dp"
            android:orientation="vertical"
            android:gravity="center" >

            <TextView
                android:id="@+id/message"
                style="@style/text_16_666666"
                android:layout_width="fill_parent"
                android:layout_height="wrap_content"
                android:gravity="left|center"
                android:lineSpacingMultiplier="1.5"
                android:minHeight="120.0dip"
                android:paddingBottom="15.0dip"
                android:paddingLeft="20.0dip"
                android:paddingRight="20.0dip"
                android:paddingTop="15.0dip"
                android:visibility="gone" />
            
              <EditText
                android:id="@+id/editcontent"
                style="@style/text_16_666666"
                android:layout_width="fill_parent"
                android:layout_height="wrap_content"
                android:gravity="left|top"
                android:minHeight="120.0dip"
                android:padding="10.0dip"
                android:background="@drawable/dialog_bg_shape"
                android:visibility="gone"/> 
        </LinearLayout>

        <View
            android:layout_width="fill_parent"
            android:layout_height="1.0px"
            android:background="#ffd0d0d0" />

        <LinearLayout
            android:layout_width="fill_parent"
            android:layout_height="50.0dip"
            android:layout_gravity="bottom"
            android:gravity="center"
            android:paddingLeft="10dp"
            android:paddingRight="10dp"
            android:orientation="horizontal" >

            <Button
                android:id="@+id/positiveButton"
                style="@style/text_15_ffffff_sdw"
                android:layout_width="0dip"
                android:layout_weight="1"
                android:layout_height="40.0dip"
                android:background="@drawable/btn_ok_selector"
                android:gravity="center"
                android:text="save" />

            <Button
                android:id="@+id/negativeButton"
                style="@style/text_15_666666_sdw"
                android:layout_width="0dip"
                android:layout_weight="1"
                android:layout_height="40.0dip"
                android:layout_marginLeft="20.0dip"
                android:background="@drawable/btn_cancel_selector"
                android:gravity="center"
                android:visibility="gone"
                android:text="cancel" />
        </LinearLayout>
    </LinearLayout>
</FrameLayout>
