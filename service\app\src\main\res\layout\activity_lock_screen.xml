<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:gravity="center"
    android:fitsSystemWindows="false"
    android:background="#99000000">
    <FrameLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content">
        <LinearLayout
            android:id="@+id/ll_lockdevice"
            android:orientation="vertical"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="40dp"
            android:layout_marginLeft="16dp"
            android:layout_marginRight="16dp"
            android:background="@drawable/bg_shape">
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="17dp"
                android:gravity="center"
                android:background="@color/white">
                <ImageView
                    android:id="@+id/iv_warning_1"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:scaleType="center"
                    android:background="@drawable/credit_card"
                    android:layout_gravity="center"
                    android:contentDescription="Device locked icon" />
            </LinearLayout>
            <LinearLayout
                android:layout_height="wrap_content"
                android:layout_width="match_parent"
                android:gravity="center"
                android:orientation="vertical"
                android:background="@drawable/shape_corner_down">
                <TextView
                    android:id="@+id/tv_title_1"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:padding="5dp"
                    android:text="DEVICE LOCKED"
                    android:gravity="center"
                    android:textStyle="bold"
                    android:textSize="36sp"
                    android:textColor="#3a3c46"/>
                <TextView
                    android:id="@+id/tv_content_1"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:text="YOUR DEVICE IS BEYOND THE ACCEPTABLE RANGE"
                    android:textSize="19sp"
                    android:textStyle="bold"
                    android:textColor="#3a3c46"/>
                <TextView
                    android:id="@+id/tv_prompt_1"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:paddingTop="15dp"
                    android:paddingLeft="40dp"
                    android:paddingRight="40dp"
                    android:paddingBottom="15dp"
                    android:gravity="center"
                    android:text="Please return to the store and follow unlock instructions"
                    android:textColor="#6d7278"
                    android:textSize="12sp"/>

            </LinearLayout>
        </LinearLayout>
        <LinearLayout
            android:id="@+id/ll_lockdevice_input"
            android:orientation="vertical"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginTop="40dp"
            android:layout_marginLeft="16dp"
            android:layout_marginRight="16dp"
            android:background="@drawable/bg_shape">
            <FrameLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent">
                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="15dp"
                    android:orientation="vertical">
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="17dp"
                        android:gravity="center"
                        android:background="@color/white">
                        <ImageView
                            android:id="@+id/iv_warning_2"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:scaleType="center"
                            android:background="@drawable/geo_warning"
                            android:layout_gravity="center"
                            android:contentDescription="Geo warning icon" />
                    </LinearLayout>
                    <LinearLayout
                        android:layout_height="wrap_content"
                        android:layout_width="match_parent"
                        android:gravity="center"
                        android:orientation="vertical"
                        android:background="@drawable/shape_corner_down">
                        <TextView
                            android:id="@+id/tv_title_2"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:padding="5dp"
                            android:text="HELP DESK CALL REQUIRED"
                            android:lineSpacingExtra="-10dp"
                            android:gravity="center"
                            android:textStyle="bold"
                            android:textSize="36sp"
                            android:textColor="#3a3c46"/>
                        <TextView
                            android:id="@+id/tv_prompt_2"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:paddingLeft="35dp"
                            android:paddingRight="35dp"
                            android:gravity="center"
                            android:text="To receive a OTP Key &amp; unlock your device\nplease call the Help Desk number."
                            android:textColor="#6d7278"
                            android:textSize="12sp"/>
                        <TextView
                            android:id="@+id/tv_tip"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="20dp"
                            android:gravity="center"
                            android:text="Enter OTP Key to unlock device"
                            android:textSize="14sp"
                            android:textColor="#107f62"/>
                        <com.dspread.mdm.service.ui.view.PasswordEditText
                            android:id="@+id/pet_pwd"
                            android:layout_width="match_parent"
                            android:layout_height="50dp"
                            android:layout_marginTop="8dp"
                            android:layout_marginLeft="16dp"
                            android:layout_marginRight="16dp"
                            android:inputType="numberPassword"
                            app:showPassword="true"
                            app:bgCorner="5dp"
                            android:maxLength="6"
                            android:background="@null"/>
                        <TextView
                            android:id="@+id/tv_warning"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginLeft="16dp"
                            android:layout_gravity="left"
                            android:gravity="left"
                            android:text="WARNING: All data on the device will be wiped out after 5 incorrect attempts."
                            android:textSize="10sp"
                            android:textColor="@color/red"
                            android:visibility="gone"/>

                        <Button
                            android:id="@+id/btn_submit"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_marginTop="18dp"
                            android:layout_marginLeft="16dp"
                            android:layout_marginRight="16dp"
                            android:layout_marginBottom="20dp"
                            android:gravity="center"
                            android:text="SUBMIT"
                            android:textAllCaps="false"
                            android:textSize="25sp"
                            android:textColor="@color/white"
                            android:background="@drawable/button_primary"/>
                    </LinearLayout>
                </LinearLayout>
                <TextView
                    android:id="@+id/tv_sn"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_margin="5dp"
                    android:textSize="15dp"
                    android:textStyle="italic"
                    android:textColor="@color/gray"
                    android:text="1234567890"/>
                <TextView
                    android:id="@+id/tv_no_pwd"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_margin="5dp"
                    android:layout_gravity="bottom"
                    android:textSize="15dp"
                    android:textColor="@color/red"
                    android:text="WARNING: Please obtain the OTP first"
                    android:visibility="gone"/>
            </FrameLayout>
        </LinearLayout>
    </FrameLayout>
</LinearLayout>
