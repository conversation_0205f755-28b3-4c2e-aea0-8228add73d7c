package com.bbpos.wiseapp.service.bbdevice;

import android.os.SystemClock;

import com.bbpos.bbdevice.BBDeviceController;
import com.bbpos.wiseapp.logger.BBLog;

import java.util.Hashtable;

public class MyBBDeviceControllerListener extends DefaultBBDeviceControllerListener {
	public static final String TAG = "bbdevice";
	private boolean serialConnected;
	private BBDeviceController bbDeviceController;

	public void setBbDeviceController(BBDeviceController bbDeviceController) {
		this.bbDeviceController = bbDeviceController;
	}

	public boolean isSerialConnected() {
		return serialConnected;
	}

	@Override
	public void onReturnDeviceInfo(Hashtable<String, String> deviceInfoData) {

	}

	@Override
	public void onError(BBDeviceController.Error errorState, String errorMessage) {
		BBLog.e(TAG, "Error : " + errorState + ", errorMessage : " + errorMessage);
		if ("Cannot start serial again while serial is connected".equals(errorMessage)) {
			serialConnected = true;
			if (bbDeviceController!=null)
				bbDeviceController.getDeviceInfo();
		} else {
			if (serialConnected && bbDeviceController!=null) {
				serialConnected = false;
				bbDeviceController.stopSerial();
			}
		}
	}

	@Override
	public void onSerialConnected() {
		BBLog.e(TAG, "onSerialConnected");
		serialConnected = true;
		if (bbDeviceController!=null)
			bbDeviceController.getDeviceInfo();
	}

	@Override
	public void onSerialDisconnected() {
		BBLog.e(TAG, "onSerialDisconnected");
		serialConnected = false;
	}
}
