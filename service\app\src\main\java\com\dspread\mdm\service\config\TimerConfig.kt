package com.dspread.mdm.service.config

import android.content.Context
import com.dspread.mdm.service.modules.provisioning.ProvisioningManager
import com.dspread.mdm.service.utils.log.Logger

/**
 * 定时器配置管理
 * 支持生产环境和调试环境的不同定时器间隔配置
 */
object TimerConfig {
    
    private const val TAG = "TimerConfig"
    private var isDebugMode = false
    
    // ==================== 生产环境配置 ====================

    /**
     * 生产环境 - 心跳定时器间隔（默认5分钟）
     */
    private const val PROD_HEARTBEAT_INTERVAL = 5 * 60L // 5分钟
    
    /**
     * 生产环境 - 终端信息上传间隔（默认15分钟）
     */
    private const val PROD_TERMINAL_INFO_INTERVAL = 15 * 60L // 15分钟
    
    /**
     * 生产环境 - 任务执行间隔（1分钟）
     */
    private const val PROD_TASK_EXECUTION_INTERVAL = 60L // 1分钟
    
    /**
     * 生产环境 - 服务守护间隔（2分钟）
     */
    private const val PROD_SERVICE_GUARD_INTERVAL = 2 * 60L // 2分钟
    
    /**
     * 生产环境 - Provisioning间隔（12小时）
     */
    private const val PROD_PROVISIONING_INTERVAL = 12 * 60 * 60L // 12小时
    
    // ==================== 调试环境配置 ====================
    
    /**
     * 调试环境 - 心跳定时器间隔（1分钟）
     */
    private const val DEBUG_HEARTBEAT_INTERVAL = 60L // 1分钟
    
    /**
     * 调试环境 - 终端信息上传间隔（2分钟）
     */
    private const val DEBUG_TERMINAL_INFO_INTERVAL = 2 * 60L // 2分钟
    
    /**
     * 调试环境 - 任务执行间隔（1分钟）
     */
    private const val DEBUG_TASK_EXECUTION_INTERVAL = 60L // 1分钟
    
    /**
     * 调试环境 - 服务守护间隔（2分钟）
     */
    private const val DEBUG_SERVICE_GUARD_INTERVAL = 2 * 60L // 2分钟
    
    /**
     * 调试环境 - Provisioning间隔（5分钟）
     */
    private const val DEBUG_PROVISIONING_INTERVAL = 5 * 60L // 5分钟
    
    // ==================== 配置管理方法 ====================
    
    /**
     * 设置调试模式
     */
    fun setDebugMode(debugMode: Boolean) {
        isDebugMode = debugMode
    }
    
    /**
     * 是否处于调试模式
     */
    fun isDebugMode(): Boolean {
        return isDebugMode
    }
    
    /**
     * 获取心跳间隔
     */
    fun getHeartbeatInterval(context: Context): Long {
        return if (isDebugMode) {
            DEBUG_HEARTBEAT_INTERVAL
        } else {
            try {
                val provisioningManager = ProvisioningManager.getInstance(context)
                val config = provisioningManager.getCurrentConfig()

                if (config != null) {
                    val heartbeatTime = config.polling.heartbeatTime.toLongOrNull()
                    if (heartbeatTime != null && heartbeatTime > 0) {
                        return heartbeatTime
                    }
                }
            } catch (e: Exception) {
                // 静默处理异常
            }
            PROD_HEARTBEAT_INTERVAL
        }
    }

    /**
     * 获取终端信息间隔
     */
    fun getTerminalInfoInterval(context: Context): Long {
        return if (isDebugMode) {
            DEBUG_TERMINAL_INFO_INTERVAL
        } else {
            try {
                val provisioningManager = ProvisioningManager.getInstance(context)
                val config = provisioningManager.getCurrentConfig()

                if (config != null) {
                    val terminalInfoTime = config.polling.terminalInfoTime.toLongOrNull()
                    if (terminalInfoTime != null && terminalInfoTime > 0) {
                        return terminalInfoTime
                    }
                }
            } catch (e: Exception) {
                // 静默处理异常
            }
            PROD_TERMINAL_INFO_INTERVAL
        }
    }
    
    /**
     * 获取任务执行间隔（秒）
     */
    fun getTaskExecutionInterval(): Long {
        return if (isDebugMode) {
            // 调试模式不再每次都打印日志
            DEBUG_TASK_EXECUTION_INTERVAL
        } else {
            // 生产环境也不再每次都打印日志
            PROD_TASK_EXECUTION_INTERVAL
        }
    }
    
    /**
     * 获取服务守护间隔（秒）
     */
    fun getServiceGuardInterval(): Long {
        return if (isDebugMode) {
            // 调试模式不再每次都打印日志
            DEBUG_SERVICE_GUARD_INTERVAL
        } else {
            // 生产环境也不再每次都打印日志
            PROD_SERVICE_GUARD_INTERVAL
        }
    }
    
    /**
     * 获取Provisioning间隔（秒）
     */
    fun getProvisioningInterval(): Long {
        return if (isDebugMode) {
            // 调试模式不再每次都打印日志
            DEBUG_PROVISIONING_INTERVAL
        } else {
            val prodInterval = ProvisionConfig.PROVISIONING_DEFAULT_INTERVAL
            // 生产环境也不再每次都打印日志
            prodInterval
        }
    }
    
    /**
     * 打印当前所有定时器配置
     */
    fun printCurrentConfig(context: Context) {
        val mode = if (isDebugMode) "调试模式" else "生产模式"

        // 静默获取所有间隔值，避免产生额外日志
        val heartbeatInterval = getHeartbeatInterval(context)
        val terminalInfoInterval = getTerminalInfoInterval(context)
        val taskInterval = getTaskExecutionInterval()
        val serviceGuardInterval = getServiceGuardInterval()
        val provisioningInterval = getProvisioningInterval()

        Logger.com("$TAG ========== 当前定时器配置 ($mode) ==========")
        Logger.com("$TAG ①心跳定时器: ${heartbeatInterval}秒")
        Logger.com("$TAG ②终端信息上传定时器: ${terminalInfoInterval}秒")
        Logger.com("$TAG ③任务执行定时器: ${taskInterval}秒")
        Logger.com("$TAG ④服务守护定时器: ${serviceGuardInterval}秒")
        Logger.com("$TAG ⑤初始化定时器: ${provisioningInterval}秒")
        Logger.com("$TAG ===============================================")
    }
}
