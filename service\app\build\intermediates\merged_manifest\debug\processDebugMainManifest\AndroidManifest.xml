<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.dspread.mdm.service"
    android:sharedUserId="android.uid.system"
    android:versionCode="22"
    android:versionName="1.1.03.20250822.DSPREAD.MDM.SERVICE" >

    <uses-sdk
        android:minSdkVersion="21"
        android:targetSdkVersion="34" />

    <!-- ==================== 存储/文件管理权限 ==================== -->
    <uses-permission android:name="android.permission.MOUNT_UNMOUNT_FILESYSTEMS" />
    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.SCHEDULE_EXACT_ALARM" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.WRITE_MEDIA_STORAGE" />
    <uses-permission android:name="android.permission.MANAGE_DOCUMENTS" />

    <!-- ==================== 网络/通信权限 ==================== -->
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />
    <uses-permission android:name="android.permission.CHANGE_NETWORK_STATE" />
    <uses-permission android:name="android.permission.CHANGE_WIFI_MULTICAST_STATE" />

    <!-- ==================== 系统基础权限 ==================== -->
    <!-- WakeLock权限，防止系统深度休眠 -->
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <!-- 开机启动权限 -->
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
    <!-- 前台服务权限 -->
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <!-- 查询运行服务权限 -->
    <uses-permission android:name="android.permission.GET_TASKS" />
    <uses-permission android:name="android.permission.DOWNLOAD_WITHOUT_NOTIFICATION" />

    <!-- ==================== 系统更新权限 ==================== -->
    <uses-permission android:name="android.permission.RECOVERY" />
    <uses-permission android:name="android.permission.ACCESS_CACHE_FILESYSTEM" />
    <uses-permission android:name="android.permission.REBOOT" />
    <uses-permission android:name="android.permission.UPDATE_DEVICE_STATS" />
    <uses-permission android:name="android.permission.WRITE_SECURE_SETTINGS" />

    <!-- UpdateEngine专用权限 -->
    <uses-permission android:name="android.permission.BIND_UPDATE_ENGINE" />
    <uses-permission android:name="android.permission.ACCESS_UPDATE_ENGINE" />
    <uses-permission android:name="android.permission.UPDATE_ENGINE" />

    <!-- Android 14+ 额外权限 -->
    <uses-permission android:name="android.permission.MANAGE_ROLLBACKS" />
    <uses-permission android:name="android.permission.TEST_MANAGE_ROLLBACKS" />
    <uses-permission android:name="android.permission.RECOVERY_REFRESH" />

    <!-- ==================== 蓝牙/NFC权限 ==================== -->
    <uses-permission android:name="android.permission.BLUETOOTH" />
    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />
    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />
    <uses-permission android:name="android.permission.BLUETOOTH_SCAN" />
    <uses-permission android:name="android.permission.BLUETOOTH_ADVERTISE" />
    <uses-permission android:name="android.permission.BLUETOOTH_PRIVILEGED" />
    <uses-permission android:name="android.permission.NFC" />

    <!-- ==================== 位置/基站/传感器权限 ==================== -->
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_LOCATION_EXTRA_COMMANDS" />
    <uses-permission android:name="android.permission.ACCESS_CELL_LOCATION" />
    <uses-permission android:name="android.permission.READ_CELL_BROADCASTS" />
    <uses-permission android:name="android.permission.ACTIVITY_RECOGNITION" />
    <uses-permission android:name="android.permission.BODY_SENSORS" />

    <!-- ==================== 电话/设备信息权限 ==================== -->
    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
    <uses-permission android:name="android.permission.READ_PRIVILEGED_PHONE_STATE" />
    <uses-permission android:name="android.permission.CALL_PHONE" />
    <uses-permission android:name="android.permission.ANSWER_PHONE_CALLS" />
    <uses-permission android:name="android.permission.PROCESS_OUTGOING_CALLS" />
    <uses-permission android:name="android.permission.USE_SIP" />
    <uses-permission android:name="android.permission.MODIFY_PHONE_STATE" />

    <!-- ==================== 系统/电源/管理权限 ==================== -->
    <uses-permission android:name="android.permission.SHUTDOWN" />
    <uses-permission android:name="android.permission.DEVICE_POWER" />
    <uses-permission android:name="android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS" />
    <uses-permission android:name="android.permission.BATTERY_STATS" />
    <uses-permission android:name="android.permission.SET_TIME" />
    <uses-permission android:name="android.permission.SET_TIME_ZONE" />
    <uses-permission android:name="android.permission.WRITE_SETTINGS" />
    <uses-permission android:name="android.permission.CHANGE_COMPONENT_ENABLED_STATE" />
    <uses-permission android:name="android.permission.KILL_BACKGROUND_PROCESSES" />
    <uses-permission android:name="android.permission.GET_PACKAGE_SIZE" />
    <uses-permission android:name="android.permission.INTERACT_ACROSS_USERS" />
    <uses-permission android:name="android.permission.INTERACT_ACROSS_USERS_FULL" />
    <uses-permission android:name="android.permission.BROADCAST_STICKY" />
    <uses-permission android:name="android.permission.REORDER_TASKS" />
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
    <uses-permission android:name="android.permission.EXPAND_STATUS_BAR" />
    <uses-permission android:name="android.permission.STATUS_BAR" />
    <uses-permission android:name="android.permission.STATUS_BAR_SERVICE" />
    <uses-permission android:name="android.permission.MANAGE_USERS" />
    <uses-permission android:name="android.permission.PACKAGE_USAGE_STATS" />
    <uses-permission android:name="android.permission.DUMP" />
    <uses-permission android:name="android.permission.READ_LOGS" />
    <uses-permission android:name="android.permission.SET_DEBUG_APP" />
    <uses-permission android:name="android.permission.PERSISTENT_ACTIVITY" />
    <uses-permission android:name="android.permission.CLEAR_APP_USER_DATA" />
    <uses-permission android:name="android.permission.MANAGE_APP_OPS_MODES" />
    <uses-permission android:name="android.permission.BIND_CARRIER_SERVICE" />
    <uses-permission android:name="android.permission.WRITE_APN_SETTINGS" />

    <!-- ==================== 应用/包管理权限 ==================== -->
    <uses-permission android:name="android.permission.INSTALL_PACKAGES" />
    <uses-permission android:name="android.permission.DELETE_PACKAGES" />
    <uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES" />
    <uses-permission android:name="android.permission.FORCE_STOP_PACKAGES" />
    <uses-permission android:name="android.permission.REQUEST_DELETE_PACKAGES" />
    <uses-permission android:name="android.permission.QUERY_ALL_PACKAGES" />

    <!-- ==================== 通知/前台服务权限 ==================== -->
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
    <uses-permission android:name="android.permission.ACCESS_NOTIFICATION_POLICY" />

    <!-- ==================== 音频/摄像头/多媒体权限 ==================== -->
    <uses-permission android:name="android.permission.RECORD_AUDIO" />
    <uses-permission android:name="android.permission.CAMERA" />
    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
    <uses-permission android:name="android.permission.VIBRATE" />

    <!-- 系统级多媒体捕获权限 -->
    <uses-permission android:name="android.permission.CAPTURE_AUDIO_OUTPUT" />
    <uses-permission android:name="android.permission.CAPTURE_VIDEO_OUTPUT" />
    <uses-permission android:name="android.permission.CAPTURE_SECURE_VIDEO_OUTPUT" />

    <!-- MediaProjection权限 -->
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_MEDIA_PROJECTION" />

    <!-- ==================== 辅助功能/无障碍权限 ==================== -->
    <uses-permission android:name="android.permission.BIND_ACCESSIBILITY_SERVICE" />

    <!-- ==================== 账户/联系人/日历/SMS/通话/同步相关权限 ==================== -->
    <uses-permission android:name="android.permission.GET_ACCOUNTS" />
    <uses-permission android:name="android.permission.READ_CALENDAR" />
    <uses-permission android:name="android.permission.READ_CALL_LOG" />
    <uses-permission android:name="android.permission.READ_CONTACTS" />
    <uses-permission android:name="android.permission.READ_SMS" />
    <uses-permission android:name="android.permission.READ_SYNC_SETTINGS" />
    <uses-permission android:name="android.permission.READ_SYNC_STATS" />
    <uses-permission android:name="android.permission.RECEIVE_MMS" />
    <uses-permission android:name="android.permission.RECEIVE_SMS" />
    <uses-permission android:name="android.permission.RECEIVE_WAP_PUSH" />
    <uses-permission android:name="android.permission.SEND_SMS" />

    <!-- ==================== 桌面快捷方式/壁纸相关权限 ==================== -->
    <uses-permission android:name="android.permission.UNINSTALL_SHORTCUT" />
    <uses-permission android:name="android.permission.INSTALL_SHORTCUT" />
    <uses-permission android:name="android.permission.SET_WALLPAPER" />
    <uses-permission android:name="android.permission.SET_WALLPAPER_HINTS" />

    <!-- ==================== 自定义权限定义 ==================== -->
    <uses-permission android:name="android.permission.DSPREAD" />
    <uses-permission android:name="com.dspread.mdm.service.permissions.MY_BROADCAST" />

    <!-- Android 10+ 广播权限 -->
    <uses-permission android:name="android.permission.BROADCAST_CLOSE_SYSTEM_DIALOGS" />
    <uses-permission android:name="android.permission.BROADCAST_PACKAGE_ADDED" />
    <uses-permission android:name="android.permission.BROADCAST_PACKAGE_CHANGED" />
    <uses-permission android:name="android.permission.BROADCAST_PACKAGE_INSTALL" />
    <uses-permission android:name="android.permission.BROADCAST_PACKAGE_REPLACED" />
    <uses-permission android:name="android.permission.BROADCAST_PACKAGE_REMOVED" />
    <uses-permission android:name="android.permission.BROADCAST_PACKAGE_RESTARTED" />
    <uses-permission android:name="android.permission.BROADCAST_PACKAGE_FIRST_LAUNCH" />
    <uses-permission android:name="android.permission.BROADCAST_PACKAGE_NEEDS_VERIFICATION" />
    <uses-permission android:name="android.permission.BROADCAST_PACKAGE_VERIFIED" />

    <permission
        android:name="com.dspread.mdm.service.permissions.MY_BROADCAST"
        android:protectionLevel="signature" />
    <permission
        android:name="com.dspread.mdm.service.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
        android:protectionLevel="signature" />

    <uses-permission android:name="com.dspread.mdm.service.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" /> <!-- ==================== 应用程序配置 ==================== -->
    <application
        android:name="com.dspread.mdm.service.SmartMdmServiceApp"
        android:allowBackup="true"
        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:debuggable="true"
        android:extractNativeLibs="true"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:networkSecurityConfig="@xml/network_security_config"
        android:requestLegacyExternalStorage="true"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.MyTheme"
        android:usesCleartextTraffic="true" >

        <!-- ==================== Activity组件 ==================== -->


        <!-- 测试Activity -->
        <activity
            android:name="com.dspread.mdm.service.ui.activity.TestActivity"
            android:exported="true"
            android:label="System Info" >
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <!-- OS升级测试Activity -->
        <activity
            android:name="com.dspread.mdm.service.ui.activity.OsUpdateTestActivity"
            android:exported="false"
            android:label="OS升级测试"
            android:theme="@style/Theme.MyTheme" />

        <!-- MediaProjection权限请求Activity -->
        <activity
            android:name="com.dspread.mdm.service.modules.remoteview.RequestMediaProjectionActivity"
            android:excludeFromRecents="true"
            android:exported="false"
            android:launchMode="singleTop"
            android:theme="@android:style/Theme.Translucent.NoTitleBar" />

        <!-- 锁屏Activity -->
        <activity
            android:name="com.dspread.mdm.service.ui.activity.LockScreenActivity"
            android:enabled="true"
            android:excludeFromRecents="true"
            android:exported="true"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"
            android:theme="@style/no_statusbar_activity" >
            <intent-filter android:priority="-100" >
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.HOME" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>

        <!-- ==================== Service组件 ==================== -->


        <!-- 主后台服务 -->
        <service
            android:name="com.dspread.mdm.service.services.SmartMdmBackgroundService"
            android:enabled="true"
            android:exported="true"
            android:foregroundServiceType="dataSync" >
            <intent-filter>
                <action android:name="com.dspread.mdm.service.START_SERVICE" />

                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </service>

        <!-- Provisioning服务 - 异步处理配置任务 -->
        <service
            android:name="com.dspread.mdm.service.services.ProvisioningService"
            android:enabled="true"
            android:exported="false" />

        <!-- MediaProjection前台服务 -->
        <service
            android:name="com.dspread.mdm.service.services.MediaProjectionService"
            android:enabled="true"
            android:exported="false"
            android:foregroundServiceType="mediaProjection" />

        <!-- 服务保活服务 -->
        <service
            android:name="com.dspread.mdm.service.services.ServiceKeepAliveService"
            android:enabled="true"
            android:exported="false" />

        <!-- 应用安装服务 -->
        <service
            android:name="com.dspread.mdm.service.services.AppInstallService"
            android:enabled="true"
            android:exported="false" />

        <!-- 应用卸载服务 -->
        <service
            android:name="com.dspread.mdm.service.services.AppUninstallService"
            android:enabled="true"
            android:exported="false" />

        <!-- 用户交互监控辅助功能服务 -->
        <service
            android:name="com.dspread.mdm.service.platform.monitor.UserInteractionAccessibilityService"
            android:exported="false"
            android:permission="android.permission.BIND_ACCESSIBILITY_SERVICE" >
            <intent-filter>
                <action android:name="android.accessibilityservice.AccessibilityService" />
            </intent-filter>

            <meta-data
                android:name="android.accessibilityservice"
                android:resource="@xml/accessibility_service_config" />
        </service>

        <!-- ==================== BroadcastReceiver组件 ==================== -->


        <!-- Geofence操作广播接收器 -->
        <receiver
            android:name="com.dspread.mdm.service.broadcast.receivers.GeofenceReceiver"
            android:enabled="true"
            android:exported="false" >
            <intent-filter>
                <action android:name="android.intent.action.SCREEN_ON" />
                <action android:name="android.intent.action.SCREEN_OFF" />
                <action android:name="android.intent.action.USER_PRESENT" />
                <action android:name="android.intent.action.BOOT_COMPLETED" />
                <action android:name="android.net.wifi.RSSI_CHANGED" />
                <action android:name="android.net.wifi.NETWORK_STATE_CHANGED" />
            </intent-filter>
        </receiver>

        <provider
            android:name="androidx.startup.InitializationProvider"
            android:authorities="com.dspread.mdm.service.androidx-startup"
            android:exported="false" >
            <meta-data
                android:name="androidx.emoji2.text.EmojiCompatInitializer"
                android:value="androidx.startup" />
            <meta-data
                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
                android:value="androidx.startup" />
            <meta-data
                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
                android:value="androidx.startup" />
        </provider>

        <receiver
            android:name="androidx.profileinstaller.ProfileInstallReceiver"
            android:directBootAware="false"
            android:enabled="true"
            android:exported="true"
            android:permission="android.permission.DUMP" >
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
            </intent-filter>
        </receiver>
    </application>

</manifest>