package com.dspread.mdm.service.ui.dialog

import android.app.Dialog
import android.content.Context
import android.os.CountDownTimer
import android.os.Handler
import android.os.Looper
import android.view.LayoutInflater
import android.view.WindowManager
import android.widget.TextView
import com.dspread.mdm.service.R
import com.dspread.mdm.service.utils.log.Logger

/**
 * 重启警告弹窗
 * 
 * 功能：
 * - 显示重启警告信息
 * - 倒计时显示
 * - 延迟重启或立即重启选择
 * - 支持悬浮窗模式
 */
class RebootWarningDialog private constructor(
    context: Context,
    private val title: String,
    private val message: String,
    private val onDelayClick: () -> Unit,
    private val onRebootClick: () -> Unit
) : Dialog(context, android.R.style.Theme_DeviceDefault_Dialog) {

    companion object {
        private const val TAG = "RebootWarningDialog"
        private var currentDialog: RebootWarningDialog? = null
        
        /**
         * 显示重启警告弹窗
         */
        fun showRebootWarningDialog(
            context: Context,
            title: String,
            message: String,
            onDelayClick: () -> Unit,
            onRebootClick: () -> Unit
        ): RebootWarningDialog {
            Logger.geo("$TAG 显示重启警告弹窗")
            
            // 关闭之前的弹窗
            currentDialog?.dismiss()
            currentDialog = null
            
            // 创建新弹窗
            val dialog = RebootWarningDialog(context, title, message, onDelayClick, onRebootClick)
            currentDialog = dialog
            
            // 设置弹框属性
            dialog.setCanceledOnTouchOutside(false)
            dialog.setCancelable(false)
            
            // 设置全屏显示
            dialog.window?.let { window ->
                val layoutParams = window.attributes
                layoutParams.width = WindowManager.LayoutParams.MATCH_PARENT
                layoutParams.height = WindowManager.LayoutParams.MATCH_PARENT
                layoutParams.type = WindowManager.LayoutParams.TYPE_SYSTEM_ALERT
                window.attributes = layoutParams
            }
            
            dialog.show()
            return dialog
        }
    }

    private lateinit var titleView: TextView
    private lateinit var messageView: TextView
    private lateinit var countdownView: TextView
    private lateinit var delayButton: TextView
    private lateinit var rebootButton: TextView
    
    private var countDownTimer: CountDownTimer? = null
    private val handler = Handler(Looper.getMainLooper())
    
    // 默认5分钟倒计时
    private var countdownSeconds = 300

    init {
        initViews()
        startCountdown()
    }

    private fun initViews() {
        val view = LayoutInflater.from(context).inflate(R.layout.dialog_reboot_warning, null)
        setContentView(view)
        
        titleView = view.findViewById(R.id.tv_title)
        messageView = view.findViewById(R.id.tv_message)
        countdownView = view.findViewById(R.id.tv_countdown)
        delayButton = view.findViewById(R.id.btn_delay)
        rebootButton = view.findViewById(R.id.btn_reboot_now)
        
        // 设置内容
        titleView.text = title
        messageView.text = message
        
        // 设置按钮点击事件
        delayButton.setOnClickListener {
            Logger.geo("$TAG 用户选择延迟重启")
            countDownTimer?.cancel()
            dismiss()
            onDelayClick()
        }
        
        rebootButton.setOnClickListener {
            Logger.geo("$TAG 用户选择立即重启")
            countDownTimer?.cancel()
            dismiss()
            onRebootClick()
        }
    }

    private fun startCountdown() {
        countDownTimer?.cancel()
        
        countDownTimer = object : CountDownTimer((countdownSeconds * 1000).toLong(), 1000) {
            override fun onTick(millisUntilFinished: Long) {
                val secondsLeft = (millisUntilFinished / 1000).toInt()
                updateCountdownDisplay(secondsLeft)
            }
            
            override fun onFinish() {
                Logger.geo("$TAG 倒计时结束，自动执行重启")
                dismiss()
                onRebootClick()
            }
        }
        
        countDownTimer?.start()
    }
    
    private fun updateCountdownDisplay(seconds: Int) {
        val minutes = seconds / 60
        val remainingSeconds = seconds % 60
        val timeString = String.format("%02d:%02d", minutes, remainingSeconds)
        
        handler.post {
            countdownView.text = timeString
        }
    }

    override fun dismiss() {
        countDownTimer?.cancel()
        countDownTimer = null
        currentDialog = null
        super.dismiss()
    }
    
    /**
     * 创建悬浮窗管理器
     */
    fun createWindowManager(): RebootFloatWindow {
        return RebootFloatWindow(context)
    }
}
