package com.dspread.mdm.service.modules.wifi

import android.content.Context
import android.net.wifi.WifiManager
import android.util.Base64
import com.dspread.mdm.service.constants.Constants.ModuleConstants
import com.dspread.mdm.service.modules.BaseModuleManager
import com.dspread.mdm.service.modules.ModuleStatus
import com.dspread.mdm.service.utils.log.Logger
import com.dspread.mdm.service.config.LogConfig
import com.google.gson.Gson
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.cancel
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import java.util.concurrent.atomic.AtomicBoolean

/**
 * WiFi Profile管理器
 * 负责WiFi配置的处理、连接管理和状态监控
 */
class WifiProfileManager(
    private val context: Context
) : BaseModuleManager() {

    companion object {
        private const val TAG = "[WifiProfileManager]"
    }

    // WiFi系统服务
    private val wifiManager = context.applicationContext.getSystemService(Context.WIFI_SERVICE) as WifiManager

    // 配置缓存和状态管理
    private val configurationHelper = WifiConfigurationHelper(context)
    private val performanceManager = WifiPerformanceManager(context)
    private val isProcessing = AtomicBoolean(false)
    private var cachedProfiles: List<WifiProfile> = emptyList()
    private val gson = Gson()

    // WiFi专用协程作用域
    private val wifiScope = CoroutineScope(Dispatchers.IO + SupervisorJob())

    override fun getModuleName(): String = "WiFi Profile Manager"

    override suspend fun onInitialize(): Result<Unit> {
        return try {
            Logger.wifi("$TAG 初始化WiFi Profile管理器")

            // 初始化性能管理器
            performanceManager.initialize()

            // 初始化WiFi配置助手
            configurationHelper.initialize()

            // 加载缓存的配置
            loadCachedProfiles()

            Logger.wifi("$TAG WiFi Profile管理器初始化完成")
            Result.success(Unit)
        } catch (e: Exception) {
            Logger.wifiE("$TAG WiFi Profile管理器初始化失败", e)
            Result.failure(e)
        }
    }

    override suspend fun onStart(): Result<Unit> {
        return try {
            Logger.wifi("$TAG 启动WiFi Profile服务")

            // 启动定时检查任务
            startPeriodicCheck()

            // 启动网络状态监控和故障恢复
            startNetworkMonitoring()

            Logger.wifi("$TAG WiFi Profile服务启动完成")
            Result.success(Unit)
        } catch (e: Exception) {
            Logger.wifiE("$TAG WiFi Profile服务启动失败", e)
            Result.failure(e)
        }
    }

    override suspend fun onStop(): Result<Unit> {
        return try {
            Logger.wifi("$TAG 停止WiFi Profile服务")

            // 停止性能管理器
            performanceManager.shutdown()

            // 停止定时检查任务
            // pollTimer.stopTimer() // 暂时注释

            // 取消WiFi协程作用域
            wifiScope.cancel()

            Logger.wifi("$TAG WiFi Profile服务停止完成")
            Result.success(Unit)
        } catch (e: Exception) {
            Logger.wifiE("$TAG WiFi Profile服务停止失败", e)
            Result.failure(e)
        }
    }

    /**
     * 处理WiFi配置列表
     * 这是核心业务逻辑入口
     */
    suspend fun processWifiProfiles(profiles: List<WifiProfile>): Result<Unit> {
        // 防止并发处理
        if (!isProcessing.compareAndSet(false, true)) {
            Logger.wifiW("$TAG WiFi配置处理正在进行中，跳过本次请求")
            return Result.success(Unit)
        }

        return try {
            Logger.wifi("$TAG 开始处理 ${profiles.size} 个WiFi配置")

            // 缓存配置
            cachedProfiles = profiles
            saveCachedProfiles(profiles)

            // 1. 清理过期配置
            val nonExpiredProfiles = removeExpiredWifiProfiles(profiles)
            Logger.wifi("$TAG 清理过期配置后: ${nonExpiredProfiles.size} 个")

            // 2. 过滤有效配置
            val validProfiles = filterValidProfiles(nonExpiredProfiles)
            Logger.wifi("$TAG 过滤后有效配置: ${validProfiles.size} 个")

            if (validProfiles.isEmpty()) {
                Logger.wifiW("$TAG 没有有效的WiFi配置")
                return Result.success(Unit)
            }

            // 3. 按时间窗口过滤
            val timeFilteredProfiles = filterByTimeWindow(validProfiles)
            Logger.wifi("$TAG 时间窗口过滤后: ${timeFilteredProfiles.size} 个")

            // 4. 过滤延迟执行的配置
            val readyProfiles = filterDelayedProfiles(timeFilteredProfiles)
            Logger.wifi("$TAG 排除延迟执行后: ${readyProfiles.size} 个")

            if (readyProfiles.isEmpty()) {
                Logger.wifiW("$TAG 当前没有可执行的WiFi配置")
                return Result.success(Unit)
            }

            // 5. 按优先级排序
            val sortedProfiles = sortByPriority(readyProfiles)

            // 5.5. 处理隐藏网络特殊逻辑
            val processedProfiles = processHiddenWifiProfiles(sortedProfiles)

            // 6. 尝试连接WiFi
            val result = connectToWifiProfiles(processedProfiles)

            // 7. 清理非列表WiFi（如果连接成功）
            if (result.isSuccess) {
                cleanupNonListWifi(profiles)
            }

            result

        } catch (e: Exception) {
            Logger.wifiE("$TAG WiFi配置处理异常", e)
            Result.failure(e)
        } finally {
            isProcessing.set(false)
        }
    }

    /**
     * 启动定时检查任务
     */
    fun startPeriodicCheck() {
        // pollTimer.startTimer( // 暂时注释
        //     interval = ModuleConstants.WIFI_PROFILE_CHECK_INTERVAL,
        //     callback = ::performPeriodicCheck
        // )
        Logger.wifi("$TAG 启动WiFi配置定时检查，间隔: ${ModuleConstants.WIFI_PROFILE_CHECK_INTERVAL}ms")
    }

    /**
     * 执行定时检查
     */
    private fun performPeriodicCheck() {
        if (getStatus() != ModuleStatus.RUNNING) {
            return
        }

        Logger.wifi("$TAG 执行定时WiFi配置检查")

        // 异步处理，避免阻塞定时器
        wifiScope.launch {
            try {
                if (cachedProfiles.isNotEmpty()) {
                    processWifiProfiles(cachedProfiles)
                }
            } catch (e: Exception) {
                Logger.wifiE("$TAG 定时检查处理失败", e)
            }
        }
    }

    /**
     * 过滤有效的WiFi配置
     * 包含安全类型、密码和代理配置验证
     */
    private fun filterValidProfiles(profiles: List<WifiProfile>): List<WifiProfile> {
        return profiles.filter { profile ->
            // 基本有效性检查
            val isValid = profile.isValid()
            if (!isValid) {
                Logger.wifiW("$TAG 无效的WiFi配置: ${profile.ssid}")
                return@filter false
            }

            // 代理配置验证
            val isValidProxy = configurationHelper.validateProxyConfiguration(profile)
            if (!isValidProxy) {
                Logger.wifiW("$TAG WiFi代理配置无效: ${profile.ssid}, ${profile.getProxyDescription()}")
                return@filter false
            }

            // 隐藏网络配置验证
            val isValidHidden = configurationHelper.validateHiddenNetworkConfiguration(profile)
            if (!isValidHidden) {
                Logger.wifiW("$TAG WiFi隐藏网络配置无效: ${profile.ssid}")
                return@filter false
            }

            Logger.wifi("$TAG WiFi配置验证通过: ${profile.getWifiTypeDescription()}")
            true
        }
    }

    /**
     * 按时间窗口过滤配置
     * 支持完整日期时间范围和简单时间窗口
     */
    private fun filterByTimeWindow(profiles: List<WifiProfile>): List<WifiProfile> {
        return profiles.filter { profile ->
            // 检查是否已过期
            if (profile.isExpired()) {
                Logger.wifi("$TAG WiFi配置已过期: ${profile.ssid} (endDate: ${profile.endDate})")
                return@filter false
            }

            // 检查是否还未生效
            if (profile.isNotYetActive()) {
                Logger.wifi("$TAG WiFi配置还未生效: ${profile.ssid} (beginDate: ${profile.beginDate})")
                return@filter false
            }

            // 检查时间窗口
            val inTimeWindow = profile.isInTimeWindow()
            if (!inTimeWindow) {
                val timeInfo = if (profile.beginDate.isNotEmpty() && profile.endDate.isNotEmpty()) {
                    "(${profile.beginDate} - ${profile.endDate})"
                } else {
                    "(${profile.startTime} - ${profile.endTime})"
                }
                Logger.wifi("$TAG WiFi配置不在时间窗口内: ${profile.ssid} $timeInfo")
            }
            inTimeWindow
        }
    }

    /**
     * 过滤需要延迟执行的配置
     * 包含24小时故障延迟重试机制
     */
    private fun filterDelayedProfiles(profiles: List<WifiProfile>): List<WifiProfile> {
        return profiles.filter { profile ->
            // 检查时间窗口延迟
            val shouldDelayByTime = profile.shouldDelayExecution()
            if (shouldDelayByTime) {
                Logger.wifi("$TAG WiFi配置需要延迟执行(时间窗口): ${profile.ssid}")
                return@filter false
            }

            // 检查故障状态延迟（24小时重试机制）
            val isInFailureState = profile.isInFailureState()
            if (isInFailureState) {
                Logger.wifi("$TAG WiFi配置需要延迟执行(故障状态): ${profile.ssid}, ${profile.getFailureStateDescription()}")
                return@filter false
            }

            // 可以执行
            true
        }
    }

    /**
     * 按优先级排序
     * Default WiFi作为回退机制，不参与正常优先级排序
     */
    private fun sortByPriority(profiles: List<WifiProfile>): List<WifiProfile> {
        return profiles.sortedWith { profile1, profile2 ->
            // 只按order字段排序，Default WiFi不享有特殊优先级
            profile1.order.compareTo(profile2.order)
        }.also { sortedProfiles ->
            val defaultCount = sortedProfiles.count { it.isDefault }
            val normalCount = sortedProfiles.size - defaultCount
            Logger.wifi("$TAG WiFi配置按优先级排序完成: Default WiFi: $defaultCount 个, 普通WiFi: $normalCount 个")

            // 记录排序结果
            sortedProfiles.forEachIndexed { index, profile ->
                val type = if (profile.isDefault) "[Default]" else "[Normal]"
                Logger.wifi("$TAG   ${index + 1}. $type ${profile.ssid} (order: ${profile.order})")
            }
        }
    }

    /**
     * 尝试连接WiFi配置列表
     * Default WiFi作为回退机制，在普通WiFi失败后才尝试
     */
    private suspend fun connectToWifiProfiles(profiles: List<WifiProfile>): Result<Unit> {
        if (profiles.isEmpty()) {
            return Result.failure(Exception("WiFi配置列表为空"))
        }

        // 分离Default WiFi和普通WiFi
        val defaultWifis = profiles.filter { it.isDefault }
        val normalWifis = profiles.filterNot { it.isDefault }

        Logger.wifi("$TAG 开始WiFi连接处理: 普通WiFi: ${normalWifis.size} 个, Default WiFi: ${defaultWifis.size} 个")

        // 1. 首先尝试普通WiFi
        if (normalWifis.isNotEmpty()) {
            Logger.wifi("$TAG 开始尝试普通WiFi连接")
            val normalResult = tryConnectNormalWifis(normalWifis)
            if (normalResult.isSuccess) {
                return normalResult
            }
            Logger.wifiW("$TAG 所有普通WiFi连接失败，尝试Default WiFi作为回退")
        }

        // 2. 普通WiFi失败后，尝试Default WiFi作为回退机制
        if (defaultWifis.isNotEmpty()) {
            Logger.wifi("$TAG 开始尝试Default WiFi连接（回退机制）")
            val defaultResult = tryConnectDefaultWifis(defaultWifis)
            if (defaultResult.isSuccess) {
                return defaultResult
            }
            Logger.wifiW("$TAG 所有Default WiFi连接失败")
        }

        // 3. 如果所有WiFi都失败，尝试故障恢复
        Logger.wifiW("$TAG 所有WiFi配置连接失败，尝试故障恢复")
        return tryFailureRecovery(profiles)
    }

    /**
     * 尝试连接Default WiFi列表
     * Default WiFi有特殊的处理逻辑
     */
    private suspend fun tryConnectDefaultWifis(defaultWifis: List<WifiProfile>): Result<Unit> {
        for ((index, profile) in defaultWifis.withIndex()) {
            Logger.wifi("$TAG 尝试连接Default WiFi [${index + 1}/${defaultWifis.size}]: ${profile.ssid}")

            val result = connectToWifiWithValidation(profile, isDefault = true)

            if (result.isSuccess) {
                Logger.wifi("$TAG Default WiFi连接成功: ${profile.ssid}")
                // Default WiFi连接成功后，保存为缓存WiFi
                saveSuccessfulWifiProfile(profile)
                return Result.success(Unit)
            } else {
                Logger.wifiW("$TAG Default WiFi连接失败: ${profile.ssid}")
                // Default WiFi失败时也标记失败时间，但重试策略可能不同
                markWifiFailure(profile)
            }
        }

        return Result.failure(Exception("所有Default WiFi连接失败"))
    }

    /**
     * 尝试连接普通WiFi列表
     */
    private suspend fun tryConnectNormalWifis(normalWifis: List<WifiProfile>): Result<Unit> {
        for ((index, profile) in normalWifis.withIndex()) {
            Logger.wifi("$TAG 尝试连接普通WiFi [${index + 1}/${normalWifis.size}]: ${profile.ssid}")

            val result = connectToWifiWithValidation(profile, isDefault = false)

            if (result.isSuccess) {
                Logger.wifi("$TAG 普通WiFi连接成功: ${profile.ssid}")
                // 普通WiFi连接成功后，也保存为缓存WiFi
                saveSuccessfulWifiProfile(profile)
                return Result.success(Unit)
            } else {
                Logger.wifiW("$TAG 普通WiFi连接失败: ${profile.ssid}")
                markWifiFailure(profile)
            }
        }

        return Result.failure(Exception("所有普通WiFi连接失败"))
    }

    /**
     * 尝试故障恢复
     * 当所有WiFi都失败时，尝试使用上次成功的WiFi
     */
    private suspend fun tryFailureRecovery(profiles: List<WifiProfile>): Result<Unit> {
        Logger.wifi("$TAG 开始故障恢复流程")

        // 尝试从缓存中恢复上次成功的WiFi
        val lastSuccessfulWifi = loadLastSuccessfulWifiProfile()
        if (lastSuccessfulWifi != null) {
            Logger.wifi("$TAG 尝试使用上次成功的WiFi进行故障恢复: ${lastSuccessfulWifi.ssid}")

            val result = connectToWifiWithValidation(lastSuccessfulWifi, isDefault = lastSuccessfulWifi.isDefault)
            if (result.isSuccess) {
                Logger.wifi("$TAG 故障恢复成功: ${lastSuccessfulWifi.ssid}")
                return Result.success(Unit)
            } else {
                Logger.wifiW("$TAG 故障恢复失败: ${lastSuccessfulWifi.ssid}")
            }
        } else {
            Logger.wifiW("$TAG 没有找到上次成功的WiFi配置")
        }

        return Result.failure(Exception("故障恢复失败，所有WiFi连接尝试均失败"))
    }

    /**
     * 连接WiFi并进行验证
     * 包含信号范围检测和错误处理
     */
    private suspend fun connectToWifiWithValidation(profile: WifiProfile, isDefault: Boolean): Result<Unit> {
        val wifiType = if (isDefault) "Default" else "普通"

        return try {
            // 1. 检查WiFi信号范围（隐藏网络跳过此检查）
            if (!profile.isHiddenWifi()) {
                Logger.wifi("$TAG 检查${wifiType}WiFi信号范围: ${profile.ssid}")
                val isInRange = configurationHelper.checkIsWifiInRange(profile)
                if (!isInRange) {
                    val exception = WifiErrorHandler.WifiException(
                        WifiErrorHandler.WifiErrorCode.WIFI_NOT_IN_RANGE,
                        profile.ssid,
                        "WiFi不在信号范围内"
                    )
                    throw exception
                }
                Logger.wifi("$TAG ${wifiType}WiFi在信号范围内: ${profile.ssid}")
            } else {
                Logger.wifi("$TAG ${wifiType}隐藏WiFi跳过信号范围检查: ${profile.ssid}")
            }

            // 2. 连接WiFi（隐藏网络使用特殊处理）
            val connectResult = connectToWifiWithRetry(profile)
            if (connectResult.isFailure) {
                throw connectResult.exceptionOrNull() ?: Exception("连接失败")
            }

            Logger.wifiI("$TAG ${wifiType}WiFi连接成功: ${profile.ssid}")

            // 3. 验证网络连通性
            val validationResult = validateConnectionWithRetry()
            if (validationResult.isFailure) {
                throw WifiErrorHandler.WifiException(
                    WifiErrorHandler.WifiErrorCode.NETWORK_UNREACHABLE,
                    profile.ssid,
                    "网络连通性验证失败"
                )
            }

            Logger.wifiI("$TAG ${wifiType}WiFi网络验证成功: ${profile.ssid}")
            Result.success(Unit)

        } catch (e: Exception) {
            val wifiException = WifiErrorHandler.handleException(e, profile.ssid, "${wifiType}WiFi连接")

            // 尝试错误恢复
            val recovered = WifiErrorHandler.executeRecovery(wifiException) {
                // 重试连接逻辑
                connectToWifi(profile).getOrThrow()
            }

            if (recovered) {
                Logger.wifiI("$TAG ${wifiType}WiFi错误恢复成功: ${profile.ssid}")
                Result.success(Unit)
            } else {
                Result.failure(wifiException)
            }
        }
    }

    /**
     * 连接到指定WiFi
     * 支持隐藏网络的特殊处理和性能监控
     */
    private suspend fun connectToWifi(profile: WifiProfile): Result<Boolean> {
        // 记录连接尝试
        performanceManager.recordConnectionAttempt()

        return performanceManager.executeWithTimeout(
            operation = "WiFi连接-${profile.ssid}",
            timeout = performanceManager.getConnectionTimeout()
        ) {
            Logger.wifi("$TAG 开始连接WiFi: ${profile.getWifiTypeDescription()}")

            val networkId = if (profile.isHiddenWifi()) {
                // 隐藏网络使用特殊连接方法
                Logger.wifi("$TAG 使用隐藏网络连接方法: ${profile.ssid}")
                configurationHelper.connectToHiddenWifi(profile)
            } else {
                // 普通网络使用标准连接方法
                Logger.wifi("$TAG 使用标准网络连接方法: ${profile.ssid}")
                val config = configurationHelper.createWifiConfiguration(profile)
                if (config != null) {
                    val addedNetworkId = configurationHelper.addWifiConfiguration(config, profile)
                    if (addedNetworkId != -1) {
                        // 连接到新添加的网络
                        configurationHelper.connectToNetwork(addedNetworkId)
                    }
                    addedNetworkId
                } else {
                    throw WifiErrorHandler.createConfigurationException(
                        profile.ssid,
                        "WiFi配置创建失败"
                    )
                }
            }

            if (networkId == -1) {
                throw WifiErrorHandler.createConnectionException(
                    profile.ssid,
                    "WiFi配置添加到系统失败"
                )
            }

            Logger.wifi("$TAG WiFi配置创建成功: ${profile.ssid}, networkId: $networkId")

            // 等待连接完成
            delay(5000)

            // 检查是否连接到目标WiFi
            val currentWifi = configurationHelper.getCurrentWifiInfo()
            val isConnectedToTarget = currentWifi == profile.ssid

            if (isConnectedToTarget) {
                Logger.wifiI("$TAG WiFi连接成功: ${profile.ssid}")
                true
            } else {
                val errorMsg = if (currentWifi != null) {
                    "连接到了错误的WiFi: $currentWifi，期望: ${profile.ssid}"
                } else {
                    "WiFi连接失败，未连接到任何网络"
                }
                Logger.wifiE("$TAG $errorMsg")
                throw WifiErrorHandler.createConnectionException(profile.ssid, errorMsg)
            }
        }.let { result ->
            // 记录连接结果
            if (result) {
                performanceManager.recordConnectionSuccess(0) // 实际耗时由performanceManager记录
                Result.success(result)
            } else {
                performanceManager.recordConnectionFailure()
                Result.failure(Exception("连接失败"))
            }
        }
    }

    /**
     * 带重试机制的WiFi连接
     */
    private suspend fun connectToWifiWithRetry(profile: WifiProfile): Result<Boolean> {
        return try {
            connectToWifi(profile)
        } catch (e: Exception) {
            val wifiException = if (e is WifiErrorHandler.WifiException) e else
                WifiErrorHandler.handleException(e, profile.ssid, "WiFi连接重试")

            val recovered = WifiErrorHandler.executeRecovery(wifiException) {
                connectToWifi(profile).getOrThrow()
            }

            if (recovered) {
                Result.success(true)
            } else {
                Result.failure(wifiException)
            }
        }
    }

    /**
     * 验证网络连通性
     */
    private suspend fun validateConnection(): Result<Boolean> {
        return try {
            // 等待网络稳定
            delay(3000)

            // 使用现有网络工具进行ping测试
            // val pingResult = networkUtils.pingTest("*******") // 暂时注释
            val pingResult = Result.success(true) // 临时返回成功

            if (pingResult.isSuccess) {
                Logger.wifi("$TAG 网络连通性验证成功")
                Result.success(true)
            } else {
                throw WifiErrorHandler.WifiException(
                    WifiErrorHandler.WifiErrorCode.NETWORK_UNREACHABLE,
                    null,
                    "ping测试失败"
                )
            }
        } catch (e: Exception) {
            val wifiException = WifiErrorHandler.handleException(e, null, "网络连通性验证")
            Result.failure(wifiException)
        }
    }

    /**
     * 带重试机制的网络连通性验证
     */
    private suspend fun validateConnectionWithRetry(): Result<Boolean> {
        return try {
            validateConnection()
        } catch (e: Exception) {
            val wifiException = if (e is WifiErrorHandler.WifiException) e else
                WifiErrorHandler.handleException(e, null, "网络验证重试")

            val recovered = WifiErrorHandler.executeRecovery(wifiException) {
                validateConnection().getOrThrow()
            }

            if (recovered) {
                Result.success(true)
            } else {
                Result.failure(wifiException)
            }
        }
    }

    /**
     * 标记WiFi连接失败
     * 实现24小时延迟重试机制
     */
    private fun markWifiFailure(profile: WifiProfile) {
        try {
            val failedProfile = profile.markAsFailure()

            // 更新缓存中的配置
            cachedProfiles = cachedProfiles.map { cached ->
                if (cached.ssid == profile.ssid) failedProfile else cached
            }

            // 保存到持久化存储
            saveFailedWifiProfile(failedProfile)

            Logger.wifi("$TAG 标记WiFi连接失败: ${profile.ssid}, 24小时内不再重试")
        } catch (e: Exception) {
            Logger.wifiE("$TAG 标记WiFi失败时出错", e)
        }
    }

    /**
     * 清除WiFi故障状态
     */
    private fun clearWifiFailure(profile: WifiProfile) {
        try {
            val clearedProfile = profile.clearFailureState()

            // 更新缓存中的配置
            cachedProfiles = cachedProfiles.map { cached ->
                if (cached.ssid == profile.ssid) clearedProfile else cached
            }

            // 从故障列表中移除
            removeFailedWifiProfile(profile.ssid)

            Logger.wifi("$TAG 清除WiFi故障状态: ${profile.ssid}")
        } catch (e: Exception) {
            Logger.wifiE("$TAG 清除WiFi故障状态时出错", e)
        }
    }

    /**
     * 保存失败的WiFi配置到持久化存储
     */
    private fun saveFailedWifiProfile(profile: WifiProfile) {
        try {
            val sharedPrefs = context.getSharedPreferences("wifi_failures", Context.MODE_PRIVATE)
            val failureData = mapOf(
                "ssid" to profile.ssid,
                "failureTime" to profile.wifiLostTime,
                "securityType" to profile.securityType,
                "isDefault" to profile.isDefault,
                "isHidden" to profile.isHiddenWifi()
            )

            val failureJson = gson.toJson(failureData)
            sharedPrefs.edit()
                .putString("failure_${profile.ssid}", failureJson)
                .apply()

            Logger.wifi("$TAG 保存失败WiFi配置: ${profile.ssid}")
        } catch (e: Exception) {
            Logger.wifiE("$TAG 保存失败WiFi配置时出错: ${profile.ssid}", e)
        }
    }

    /**
     * 从故障列表中移除WiFi配置
     */
    private fun removeFailedWifiProfile(ssid: String) {
        try {
            val sharedPrefs = context.getSharedPreferences("wifi_failures", Context.MODE_PRIVATE)
            sharedPrefs.edit()
                .remove("failure_$ssid")
                .apply()

            Logger.wifi("$TAG 从故障列表移除WiFi: $ssid")
        } catch (e: Exception) {
            Logger.wifiE("$TAG 移除故障WiFi配置时出错: $ssid", e)
        }
    }

    /**
     * 清理不在配置列表中的WiFi
     */
    private suspend fun cleanupNonListWifi(profiles: List<WifiProfile>) {
        try {
            Logger.wifi("$TAG 开始清理非列表WiFi配置")
            configurationHelper.removeNonListWifi(profiles.map { it.ssid })
            Logger.wifi("$TAG 非列表WiFi配置清理完成")
        } catch (e: Exception) {
            Logger.wifiE("$TAG 清理非列表WiFi配置失败", e)
        }
    }

    /**
     * 加载缓存的配置
     */
    private fun loadCachedProfiles() {
        try {
            cachedProfiles = configurationHelper.loadCachedProfiles()
            Logger.wifi("$TAG 加载缓存配置: ${cachedProfiles.size} 个")
        } catch (e: Exception) {
            Logger.wifiE("$TAG 加载缓存配置失败", e)
            cachedProfiles = emptyList()
        }
    }

    /**
     * 保存配置到缓存
     */
    private fun saveCachedProfiles(profiles: List<WifiProfile>) {
        try {
            configurationHelper.saveCachedProfiles(profiles)
            Logger.wifi("$TAG 保存配置到缓存: ${profiles.size} 个")
        } catch (e: Exception) {
            Logger.wifiE("$TAG 保存配置到缓存失败", e)
        }
    }

    /**
     * 清理过期的WiFi配置
     * 根据beginDate/endDate自动清理过期配置
     */
    private suspend fun removeExpiredWifiProfiles(profiles: List<WifiProfile>): List<WifiProfile> {
        val expiredProfiles = profiles.filter { it.isExpired() }

        if (expiredProfiles.isNotEmpty()) {
            Logger.wifi("$TAG 发现 ${expiredProfiles.size} 个过期WiFi配置，开始清理")

            for (expiredProfile in expiredProfiles) {
                try {
                    configurationHelper.removeWifiConfiguration(expiredProfile.ssid)
                    Logger.wifi("$TAG 已清理过期WiFi配置: ${expiredProfile.ssid}")
                } catch (e: Exception) {
                    Logger.wifiE("$TAG 清理过期WiFi配置失败: ${expiredProfile.ssid}", e)
                }
            }
        }

        return profiles.filterNot { it.isExpired() }
    }

    /**
     * 获取有效的WiFi配置列表
     * 排除过期和未生效的配置
     */
    private fun getValidWifiProfiles(profiles: List<WifiProfile>): List<WifiProfile> {
        return profiles.filter { profile ->
            !profile.isExpired() && !profile.isNotYetActive()
        }
    }

    /**
     * 保存成功连接的WiFi配置
     * 用于故障恢复，同时保存到配置差异检测缓存
     */
    private fun saveSuccessfulWifiProfile(profile: WifiProfile) {
        try {
            // 标记为缓存WiFi
            val cacheProfile = profile.copy(wifiFlagCache = true)

            // 使用AES加密保存
            val profileJson = gson.toJson(cacheProfile)
            val encryptedData = encryptWifiProfile(profileJson)

            // 保存到SharedPreferences
            val sharedPrefs = context.getSharedPreferences("wifi_cache", Context.MODE_PRIVATE)
            sharedPrefs.edit()
                .putString("last_successful_wifi", encryptedData)
                .putLong("last_successful_time", System.currentTimeMillis())
                .apply()

            // 同时保存到配置差异检测缓存
            configurationHelper.saveCurrentWifiProfileCache(profile)

            Logger.wifi("$TAG 保存成功WiFi配置: ${profile.ssid}")

        } catch (e: Exception) {
            Logger.wifiE("$TAG 保存成功WiFi配置失败: ${profile.ssid}", e)
        }
    }

    /**
     * 加载上次成功的WiFi配置
     */
    private fun loadLastSuccessfulWifiProfile(): WifiProfile? {
        return try {
            val sharedPrefs = context.getSharedPreferences("wifi_cache", Context.MODE_PRIVATE)
            val encryptedData = sharedPrefs.getString("last_successful_wifi", null)
            val lastSuccessTime = sharedPrefs.getLong("last_successful_time", 0L)

            if (encryptedData.isNullOrEmpty()) {
                Logger.wifi("$TAG 没有找到缓存的WiFi配置")
                return null
            }

            // 检查缓存是否过期（例如7天）
            val cacheExpireTime = 7 * 24 * 60 * 60 * 1000L // 7天
            if (System.currentTimeMillis() - lastSuccessTime > cacheExpireTime) {
                Logger.wifi("$TAG 缓存的WiFi配置已过期")
                return null
            }

            // 解密并解析WiFi配置
            val profileJson = decryptWifiProfile(encryptedData)
            val profile = gson.fromJson(profileJson, WifiProfile::class.java)

            Logger.wifi("$TAG 加载缓存WiFi配置: ${profile.ssid}")
            profile

        } catch (e: Exception) {
            Logger.wifiE("$TAG 加载缓存WiFi配置失败", e)
            null
        }
    }

    /**
     * 加密WiFi配置（简化实现）
     */
    private fun encryptWifiProfile(data: String): String {
        // 这里应该使用AES加密，简化实现使用Base64
        return Base64.encodeToString(data.toByteArray(), Base64.DEFAULT)
    }

    /**
     * 解密WiFi配置（简化实现）
     */
    private fun decryptWifiProfile(encryptedData: String): String {
        // 这里应该使用AES解密，简化实现使用Base64
        return String(Base64.decode(encryptedData, Base64.DEFAULT))
    }

    /**
     * 获取周边WiFi信息
     * 用于调试和监控
     */
    fun getSurroundingWifiInfo(): List<String> {
        return try {
            Logger.wifi("$TAG 获取周边WiFi信息")
            val wifiList = configurationHelper.getWifiInRange()

            if (wifiList.isNotEmpty()) {
                Logger.wifi("$TAG 周边WiFi列表:")
                wifiList.forEachIndexed { index, ssid ->
                    Logger.wifi("$TAG   ${index + 1}. $ssid")
                }
            } else {
                Logger.wifi("$TAG 未发现周边WiFi")
            }

            wifiList
        } catch (e: Exception) {
            Logger.wifiE("$TAG 获取周边WiFi信息失败", e)
            emptyList()
        }
    }

    /**
     * 检查指定WiFi是否在范围内
     */
    fun checkWifiInRange(ssid: String): Boolean {
        return try {
            val wifiList = configurationHelper.getWifiInRange()
            val isInRange = wifiList.contains(ssid)
            Logger.wifi("$TAG WiFi范围检查: $ssid -> ${if (isInRange) "在范围内" else "不在范围内"}")
            isInRange
        } catch (e: Exception) {
            Logger.wifiE("$TAG 检查WiFi范围失败: $ssid", e)
            false
        }
    }

    /**
     * 处理隐藏网络的特殊逻辑
     */
    private fun processHiddenWifiProfiles(profiles: List<WifiProfile>): List<WifiProfile> {
        val hiddenProfiles = profiles.filter { it.isHiddenWifi() }
        val normalProfiles = profiles.filterNot { it.isHiddenWifi() }

        if (hiddenProfiles.isNotEmpty()) {
            Logger.wifi("$TAG 发现 ${hiddenProfiles.size} 个隐藏网络配置:")
            hiddenProfiles.forEach { profile ->
                Logger.wifi("$TAG   - ${profile.getWifiTypeDescription()}")
            }
        }

        // 隐藏网络和普通网络分别处理，隐藏网络优先级可以更高
        return hiddenProfiles + normalProfiles
    }

    /**
     * 获取隐藏网络信息
     */
    fun getHiddenWifiInfo(): List<WifiProfile> {
        return try {
            cachedProfiles.filter { it.isHiddenWifi() }.also { hiddenProfiles ->
                Logger.wifi("$TAG 当前隐藏网络配置: ${hiddenProfiles.size} 个")
                hiddenProfiles.forEach { profile ->
                    Logger.wifi("$TAG   - ${profile.getWifiTypeDescription()}")
                }
            }
        } catch (e: Exception) {
            Logger.wifiE("$TAG 获取隐藏网络信息失败", e)
            emptyList()
        }
    }

    /**
     * 测试隐藏网络连接
     */
    suspend fun testHiddenWifiConnection(ssid: String, password: String, securityType: String): Result<Boolean> {
        return try {
            Logger.wifi("$TAG 测试隐藏网络连接: $ssid")

            val testProfile = WifiProfile(
                ssid = ssid,
                password = password,
                securityType = securityType,
                order = 999,
                startTime = "",
                endTime = "",
                connectHidden = true, // 标记为隐藏网络
                proxyType = "NONE",
                proxyHost = "",
                proxyPort = 0,
                proxyUser = "",
                proxyPassword = "",
                wifiLostTime = 0L
            )

            // 验证配置
            if (!configurationHelper.validateHiddenNetworkConfiguration(testProfile)) {
                return Result.failure(Exception("隐藏网络配置验证失败"))
            }

            // 尝试连接
            connectToWifi(testProfile)

        } catch (e: Exception) {
            Logger.wifiE("$TAG 测试隐藏网络连接失败: $ssid", e)
            Result.failure(e)
        }
    }

    /**
     * 网络状态监控和故障恢复
     */
    fun startNetworkMonitoring() {
        Logger.wifi("$TAG 启动网络状态监控")

        wifiScope.launch {
            while (getStatus() == ModuleStatus.RUNNING) {
                try {
                    // 检查网络连接状态
                    val isConnected = configurationHelper.isWifiConnected()
                    if (!isConnected) {
                        Logger.wifiW("$TAG 检测到WiFi连接断开，尝试故障恢复")
                        performFailureRecovery()
                    } else {
                        // 网络正常，检查是否有故障状态的WiFi可以清除
                        checkAndClearRecoveredWifi()
                    }

                    // 每30秒检查一次
                    delay(30000)

                } catch (e: Exception) {
                    Logger.wifiE("$TAG 网络状态监控异常", e)
                    delay(60000) // 异常时延长检查间隔
                }
            }
        }
    }

    /**
     * 执行故障恢复
     */
    private suspend fun performFailureRecovery() {
        try {
            Logger.wifi("$TAG 开始执行故障恢复")

            // 1. 尝试重新连接当前配置
            if (cachedProfiles.isNotEmpty()) {
                val availableProfiles = cachedProfiles.filter { !it.isInFailureState() }
                if (availableProfiles.isNotEmpty()) {
                    Logger.wifi("$TAG 尝试重新连接可用WiFi配置: ${availableProfiles.size} 个")
                    processWifiProfiles(availableProfiles)
                    return
                }
            }

            // 2. 尝试使用缓存的成功WiFi
            val lastSuccessfulWifi = loadLastSuccessfulWifiProfile()
            if (lastSuccessfulWifi != null && !lastSuccessfulWifi.isInFailureState()) {
                Logger.wifi("$TAG 尝试使用缓存的成功WiFi进行故障恢复: ${lastSuccessfulWifi.ssid}")
                val result = connectToWifiWithValidation(lastSuccessfulWifi, lastSuccessfulWifi.isDefault)
                if (result.isSuccess) {
                    Logger.wifi("$TAG 故障恢复成功: ${lastSuccessfulWifi.ssid}")
                    return
                }
            }

            // 3. 检查是否有过期的故障WiFi可以重试
            val expiredFailureProfiles = cachedProfiles.filter {
                it.wifiLostTime != 0L && !it.isInFailureState()
            }

            if (expiredFailureProfiles.isNotEmpty()) {
                Logger.wifi("$TAG 发现 ${expiredFailureProfiles.size} 个故障延迟已过期的WiFi，尝试重新连接")
                for (profile in expiredFailureProfiles) {
                    clearWifiFailure(profile) // 清除故障状态
                }
                processWifiProfiles(expiredFailureProfiles)
            }

        } catch (e: Exception) {
            Logger.wifiE("$TAG 故障恢复执行异常", e)
        }
    }

    /**
     * 检查并清除已恢复的WiFi故障状态
     */
    private fun checkAndClearRecoveredWifi() {
        try {
            val failedProfiles = cachedProfiles.filter { it.wifiLostTime != 0L }
            if (failedProfiles.isEmpty()) {
                return
            }

            // 检查当前连接的WiFi
            val currentWifiInfo = wifiManager.connectionInfo
            if (currentWifiInfo != null && currentWifiInfo.networkId != -1) {
                val currentSsid = currentWifiInfo.ssid?.replace("\"", "") ?: ""

                // 如果当前连接的WiFi在故障列表中，清除其故障状态
                val recoveredProfile = failedProfiles.find { it.ssid == currentSsid }
                if (recoveredProfile != null) {
                    Logger.wifi("$TAG WiFi已恢复连接，清除故障状态: $currentSsid")
                    clearWifiFailure(recoveredProfile)
                }
            }

        } catch (e: Exception) {
            Logger.wifiE("$TAG 检查已恢复WiFi时异常", e)
        }
    }

    /**
     * 获取故障WiFi统计信息
     */
    fun getFailureStatistics(): Map<String, Any> {
        return try {
            val failedProfiles = cachedProfiles.filter { it.wifiLostTime != 0L }
            val inDelayProfiles = failedProfiles.filter { it.isInFailureState() }
            val expiredDelayProfiles = failedProfiles.filter { !it.isInFailureState() }

            mapOf(
                "totalFailed" to failedProfiles.size,
                "inDelay" to inDelayProfiles.size,
                "expiredDelay" to expiredDelayProfiles.size,
                "failedList" to failedProfiles.map {
                    mapOf(
                        "ssid" to it.ssid,
                        "failureTime" to it.wifiLostTime,
                        "status" to it.getFailureStateDescription(),
                        "canRetry" to it.canRetryConnection()
                    )
                }
            )
        } catch (e: Exception) {
            Logger.wifiE("$TAG 获取故障统计信息异常", e)
            emptyMap()
        }
    }

    /**
     * 手动重置WiFi故障状态
     */
    fun resetWifiFailureState(ssid: String): Boolean {
        return try {
            val profile = cachedProfiles.find { it.ssid == ssid }
            if (profile != null && profile.wifiLostTime != 0L) {
                clearWifiFailure(profile)
                Logger.wifi("$TAG 手动重置WiFi故障状态: $ssid")
                true
            } else {
                Logger.wifiW("$TAG WiFi不在故障状态或不存在: $ssid")
                false
            }
        } catch (e: Exception) {
            Logger.wifiE("$TAG 重置WiFi故障状态异常: $ssid", e)
            false
        }
    }

    /**
     * 日志使用示例和最佳实践
     * 展示如何在WiFi模块中正确使用日志方法
     */
    private fun demonstrateLoggingBestPractices() {
        // 使用 wfl() 记录一般的调试信息和操作流程
        Logger.wifi("开始处理WiFi配置列表，共 ${cachedProfiles.size} 个配置")
        Logger.wifi("WiFi扫描完成，发现 5 个热点")
        Logger.wifi("创建WiFi配置成功: TestWiFi")

        // 使用 wflI() 记录重要的状态变化和成功事件
        Logger.wifiI("WiFi连接成功: TestWiFi")
        Logger.wifiI("Default WiFi配置加载完成")
        Logger.wifiI("网络状态监控已启动")
        Logger.wifiI("故障恢复成功，已连接到缓存WiFi")

        // 使用 wflW() 记录警告信息和需要注意的情况
        Logger.wifiW("WiFi信号过弱: TestWiFi, 信号强度: -85 dBm")
        Logger.wifiW("WiFi配置验证失败: 密码长度不符合要求")
        Logger.wifiW("检测到WiFi连接断开，尝试故障恢复")
        Logger.wifiW("隐藏网络配置不完整: TestHiddenWiFi")

        // 使用 wflE() 记录错误信息，可以包含异常对象
        Logger.wifiE("WiFi连接失败: TestWiFi")
        Logger.wifiE("创建WiFi配置异常: TestWiFi", Exception("示例异常"))
        Logger.wifiE("网络验证失败，无法访问互联网")
        Logger.wifiE("故障恢复失败，所有WiFi连接尝试均失败")
    }

    /**
     * 获取WiFi模块日志配置信息
     */
    fun getWifiLogInfo(): Map<String, Any> {
        return mapOf(
            "enableWifiLog" to LogConfig.wifiProfileEnabled,
            "loggerClass" to "com.dspread.mdm.service.utils.log.Logger",
            "availableMethods" to listOf("wfl()", "wflI()", "wflW()", "wflE()"),
            "logTag" to "WiFi",
            "description" to "WiFi模块专用日志系统，使用Timber作为底层实现"
        )
    }

    /**
     * 获取WiFi性能统计信息
     */
    fun getWifiPerformanceStats(): Map<String, Any> {
        return try {
            val performanceStats = performanceManager.getPerformanceStats()
            val failureStats = getFailureStatistics()

            mapOf(
                "performance" to performanceStats,
                "failures" to failureStats,
                "timeouts" to mapOf(
                    "connection" to "${performanceManager.getConnectionTimeout()}ms",
                    "scan" to "${performanceManager.getScanTimeout()}ms",
                    "validation" to "${performanceManager.getValidationTimeout()}ms"
                ),
                "cacheInfo" to mapOf(
                    "cachedProfiles" to cachedProfiles.size,
                    "defaultProfiles" to cachedProfiles.count { it.isDefault },
                    "hiddenProfiles" to cachedProfiles.count { it.isHiddenWifi() },
                    "failedProfiles" to cachedProfiles.count { it.wifiLostTime != 0L }
                ),
                "timestamp" to System.currentTimeMillis()
            )
        } catch (e: Exception) {
            Logger.wifiE("$TAG 获取性能统计信息失败", e)
            mapOf("error" to e.message)
        } as Map<String, Any>
    }

    /**
     * 设置WiFi超时配置
     */
    fun setWifiTimeouts(
        connectionTimeout: Long? = null,
        scanTimeout: Long? = null,
        validationTimeout: Long? = null
    ) {
        try {
            connectionTimeout?.let {
                performanceManager.setConnectionTimeout(it)
                Logger.wifiI("$TAG 设置连接超时: ${it}ms")
            }

            scanTimeout?.let {
                performanceManager.setScanTimeout(it)
                Logger.wifiI("$TAG 设置扫描超时: ${it}ms")
            }

            validationTimeout?.let {
                performanceManager.setValidationTimeout(it)
                Logger.wifiI("$TAG 设置验证超时: ${it}ms")
            }

        } catch (e: Exception) {
            Logger.wifiE("$TAG 设置WiFi超时配置失败", e)
        }
    }

    /**
     * 优化WiFi模块性能
     */
    fun optimizePerformance() {
        try {
            Logger.wifiI("$TAG 开始优化WiFi模块性能")

            // 清理过期的故障记录
            val expiredFailures = cachedProfiles.filter {
                it.wifiLostTime != 0L && !it.isInFailureState()
            }

            if (expiredFailures.isNotEmpty()) {
                Logger.wifi("$TAG 清理 ${expiredFailures.size} 个过期故障记录")
                expiredFailures.forEach { profile ->
                    clearWifiFailure(profile)
                }
            }

            // 建议垃圾回收
            System.gc()

            Logger.wifiI("$TAG WiFi模块性能优化完成")

        } catch (e: Exception) {
            Logger.wifiE("$TAG WiFi模块性能优化失败", e)
        }
    }
}
