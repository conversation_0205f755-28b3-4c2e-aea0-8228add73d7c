package com.bbpos.wiseapp.service.receiver;

import android.app.ActivityManager;
import android.app.AlarmManager;
import android.app.PendingIntent;
import android.app.Service;
import android.content.ActivityNotFoundException;
import android.content.BroadcastReceiver;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.content.pm.ResolveInfo;
import android.os.Build;
import android.os.SystemClock;
import android.util.Log;

import com.bbpos.wiseapp.logger.BBLog;
import com.bbpos.wiseapp.service.common.UsualData;
import com.bbpos.wiseapp.tms.utils.ContextUtil;

import java.util.ArrayList;
import java.util.List;

public class LauncherDeamonReceiver extends BroadcastReceiver{
	public static long TIME_INTERVAL = 30 * 60 * 1000;
	public static final String ACTION = "com.bbpos.wiseapp.service.receiver.action.launcher.daemon";
	public static final String LAUNCHER_PROCESS_NAME = "com.bbpos.wiseapp.launcher";
	private AlarmManager alarmManager;
	private PendingIntent pendingIntent;
	private Context context;

	@Override
	public void onReceive(Context context, Intent intent) {
		String action = intent.getAction();
		this.context = context;
		if (ACTION.equals(action)){
			BBLog.d(BBLog.TAG, "onReceive: 收到广播，action: "+action);
			// 重复定时任务
			setAlertManager();
			// check is launcher running
			if (!isLauncherRunning()){
				restartLauncherProcess();
			}
		}
	}


	/************************ Launcher 守护相关 ***************************/
	public static void restartLauncherProcess() {
		BBLog.d(BBLog.TAG, "restartLauncherProcess");
		startApp("com.bbpos.wiseapp.launcher", "com.bbpos.wiseapp.launcher.MainActivity",null);
//		restartLauncher();
	}

	public static void restartLoaderProcess() {
		BBLog.d(BBLog.TAG, "restartLoaderProcess");
		startApp(UsualData.LOADER_711_PACKAGE_NAME, null,null);
//		restartLauncher();
	}

	private boolean isLauncherRunning() {
		BBLog.d(BBLog.TAG, "isLauncherRunning: 判断luancher process 是否正运行");
		ActivityManager myManager = (ActivityManager) context.getApplicationContext().getSystemService(Context.ACTIVITY_SERVICE);
		ArrayList<ActivityManager.RunningAppProcessInfo> runningService = (ArrayList<ActivityManager.RunningAppProcessInfo>) myManager.getRunningAppProcesses();
		for (int i = 0; i < runningService.size(); i++) {
            BBLog.i(BBLog.TAG, "正在运行进程： runningService["+ i + "] name = " + runningService.get(i).processName);
			if (runningService.get(i).processName
					.equals(LAUNCHER_PROCESS_NAME)) {
				return true;
			}
		}
		return false;
	}

	private void setAlertManager() {
		Intent i = new Intent(context, LauncherDeamonReceiver.class);
		i.setAction(ACTION);
		pendingIntent = PendingIntent.getBroadcast(context, 0, i,PendingIntent.FLAG_UPDATE_CURRENT);

		alarmManager =(AlarmManager)context.getSystemService(Service.ALARM_SERVICE);
		if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
			alarmManager.setExactAndAllowWhileIdle(AlarmManager.ELAPSED_REALTIME_WAKEUP, SystemClock.elapsedRealtime()+TIME_INTERVAL, pendingIntent);
		} else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
			alarmManager.setExact(AlarmManager.ELAPSED_REALTIME_WAKEUP, SystemClock.elapsedRealtime()+TIME_INTERVAL, pendingIntent);
		} else {
			alarmManager.setRepeating(AlarmManager.ELAPSED_REALTIME_WAKEUP,SystemClock.elapsedRealtime(), TIME_INTERVAL, pendingIntent);
		}
		BBLog.d(BBLog.TAG, "setAlertManager: 重新注册定时器");
	}


	public static boolean checkActivityIsExist(Context context, String strPackage, String strActivity) {
		boolean isExit = false;
		if(strPackage==null || strActivity==null || strPackage.isEmpty() || strActivity.isEmpty()) {
			return isExit;
		}

		Intent intentActivity = new Intent();
		intentActivity.setClassName(strPackage, strActivity);
		if(context.getPackageManager().resolveActivity(intentActivity, 0) != null) {
			isExit=true;
		}
		BBLog.d(BBLog.TAG, "checkActivityIsExist: 判断Launcher 启动类是否存在,result: "+isExit);
		return isExit;
	}

	public static void startApp(String strPackage, String strActivity, String extraValue) {
		if(strPackage != null ) {
			PackageManager packageManager = ContextUtil.getInstance().getPackageManager();
			Intent intent = new Intent(Intent.ACTION_MAIN);
			intent.addCategory(Intent.CATEGORY_LAUNCHER);

			List<ResolveInfo> apps = packageManager.queryIntentActivities(intent, 0);
			if (apps != null) {
				boolean bAppExit = false;
				for (ResolveInfo info : apps) {
					if (strPackage.equals(info.activityInfo.packageName)) {
						if(strActivity!=null && !strActivity.isEmpty()) {
							if(checkActivityIsExist(ContextUtil.getInstance(), strPackage,strActivity)) {
								bAppExit = true;
//								BBLog.d(BBLog.TAG, "bAppExit:Launcehr 启动类是否存在，bAppExit: "+ bAppExit);
							}
						} else {
							bAppExit = true;
						}
						break;
					}
				}

				if (bAppExit) {
					if(strActivity!=null && !strActivity.isEmpty()) {
						ComponentName comp = new ComponentName(strPackage, strActivity);
						if (extraValue != null) {
							if (!extraValue.isEmpty()) {
								intent.putExtra("transName", extraValue);
							}
						}
						intent.setComponent(comp);
						intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_RESET_TASK_IF_NEEDED);
					} else {
						intent= packageManager.getLaunchIntentForPackage(strPackage);
					}

					try {
						if(intent !=null) {
							ContextUtil.getInstance().startActivity(intent);
//							BBLog.d(BBLog.TAG, "startActivity: 重新启动Launcher ");
						}
					} catch (ActivityNotFoundException e) {
						e.printStackTrace();
					} catch (Exception e) {
						e.printStackTrace();
					}
				}
			}
		}
	}
}
