package com.bbpos.wiseapp.websocket.handler;

import android.content.Context;
import android.util.Log;

import com.bbpos.wiseapp.logger.BBLog;
import com.bbpos.wiseapp.service.common.UsualData;
import com.bbpos.wiseapp.tms.utils.Constants;
import com.bbpos.wiseapp.tms.utils.ContextUtil;
import com.bbpos.wiseapp.tms.utils.Helpers;
import com.bbpos.wiseapp.tms.utils.ParameterName;
import com.bbpos.wiseapp.tms.utils.WebSocketTaskListManager;
import com.bbpos.wiseapp.websocket.WebSocketManager;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

public class WebSocketTaskHandler{
    private WebSocketManager webSocketManager;
    private Context mContext;

    public WebSocketTaskHandler(Context context, WebSocketManager manager) {
        mContext = context;
        webSocketManager = manager;
    }

    public void handleMsg(String response) {
        String tranCode = "";
        JSONObject jsonObject = null;
        try {
            //判断是否有任务列表
            String silentInstall = "";
            JSONArray taskList = null;
            JSONObject responseJson = new JSONObject(response);
            if (responseJson.has(ParameterName.silent_install)) {
                silentInstall = responseJson.getString(ParameterName.silent_install);
            }
            JSONObject responseData = Helpers.getDataFromResponse(responseJson);
            if (responseData == null) {
                return;
            }
            if (responseData.has(ParameterName.taskList)) {
                taskList = responseData.getJSONArray(ParameterName.taskList);
                String requestId = "";
                String requestTime = "";
                if (responseJson.has(ParameterName.request_id)) {
                    requestId = responseJson.getString(ParameterName.request_id);
                }
                if (responseJson.has(ParameterName.request_time)) {
                    requestTime = responseJson.getString(ParameterName.request_time);
                }

                WebSocketTaskListManager.updateWSTaskList(requestId, requestTime, taskList, silentInstall);

                Helpers.sendBroad(ContextUtil.getInstance(), UsualData.WSTASK_UPDATED_BC);
            }
        } catch (JSONException e) {
            e.printStackTrace();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
