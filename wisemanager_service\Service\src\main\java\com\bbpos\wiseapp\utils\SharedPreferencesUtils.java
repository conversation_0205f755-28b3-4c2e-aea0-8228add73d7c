package com.bbpos.wiseapp.utils;

import android.content.Context;
import android.content.SharedPreferences;
import android.text.TextUtils;

import com.bbpos.wiseapp.logger.BBLog;
import com.bbpos.wiseapp.service.common.UsualData;
import com.bbpos.wiseapp.tms.utils.SPKeys;

public class SharedPreferencesUtils {
    public static final String SERVICE_CONFIG = "SERVICE_CONFIG";

    private static Context mContext;
    private static SharedPreferences mSharedPreferencesApp = null;

    public static void init(Context context) {
        mContext = context;
    }

    private static SharedPreferences getSharedPreferences() {
        if (mSharedPreferencesApp == null) {
            mSharedPreferencesApp = mContext.getSharedPreferences(SERVICE_CONFIG, Context.MODE_PRIVATE);
        }

        return mSharedPreferencesApp;
    }

    public static void setSharePreferencesValue(String KEY, String value) {
        getSharedPreferences().edit().putString(KEY, value).commit();
    }

    public static void setSharePreferencesValue(String KEY, boolean value) {
        getSharedPreferences().edit().putBoolean(KEY, value).commit();
    }

    public static void setSharePreferencesValue(String KEY, int value) {
        getSharedPreferences().edit().putInt(KEY, value).commit();
    }

    public static String getSharePreferencesValue(String KEY, String defaultValue) {
        return getSharedPreferences().getString(KEY, defaultValue);
    }

    public static boolean getSharePreferencesValue(String KEY, boolean defaultValue) {
        return getSharedPreferences().getBoolean(KEY, defaultValue);
    }

    public static int getSharePreferencesValue(String KEY, int defaultValue) {
        return getSharedPreferences().getInt(KEY, defaultValue);
    }

    public static void clearSharePreference() {
        getSharedPreferences().edit().clear().commit();
    }

    public static void clearByKey(String key){
        if (TextUtils.isEmpty(key))return;
        BBLog.e(BBLog.TAG, "清除SharedPreferences 键值 = " + key);
        getSharedPreferences().edit().remove(key).commit();
    }

    public static void clearWebSocketTask() {
        getSharedPreferences().edit().remove(SPKeys.WEBSOCKET_RULEBASED_LIST);
        getSharedPreferences().edit().remove(SPKeys.RULEBASED_LIST_APP_LIST);
        getSharedPreferences().edit().remove(SPKeys.WEBSOCKET_SERVICE_LIST);
        getSharedPreferences().edit().remove(SPKeys.WEBSOCKET_TASK_LIST);
        getSharedPreferences().edit().remove(SPKeys.RULEBASED_APP_LIST);
        getSharedPreferences().edit().remove(UsualData.LOG_STREAM_SERVICE_APP_EXECUTING);
        getSharedPreferences().edit().commit();
    }
}
