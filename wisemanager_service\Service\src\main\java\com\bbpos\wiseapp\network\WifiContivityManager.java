package com.bbpos.wiseapp.network;

import android.app.AlarmManager;
import android.app.PendingIntent;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import android.net.wifi.SupplicantState;
import android.net.wifi.WifiInfo;
import android.net.wifi.WifiManager;
import android.os.BatteryManager;
import android.os.Build;
import android.os.SystemClock;
import android.text.TextUtils;

import com.bbpos.wiseapp.logger.BBLog;
import com.bbpos.wiseapp.service.common.RequestPermission;
import com.bbpos.wiseapp.tms.location.CellLocationManager;
import com.bbpos.wiseapp.tms.location.WifiLocationManager;
import com.bbpos.wiseapp.tms.utils.ContextUtil;
import com.bbpos.wiseapp.tms.utils.Helpers;
import com.bbpos.wiseapp.websocket.WebSocketSender;

import org.json.JSONArray;
import org.json.JSONObject;

import java.util.ArrayDeque;
import java.util.ArrayList;
import java.util.Queue;

public class WifiContivityManager {
    public static final String TAG = BBLog.TAG;
    public static final String WIFI_CONTIVITY_ACTION = "com.bbpos.wiseapp.WIFI_CONTIVITY_ACTION";
    private static final int MAX_RECORDS = 6;
    private static final int INTERVAL_TIME = 10 * 60 * 1000;
    public static final String WIFISSID_NONE = "<unknown ssid>";
    private Context mContext;
    private WifiStateReceiver mWifiStateReceiver = null;
    private static WifiContivityManager wifiContivityManager = null;
    //save wifi contivity datas
    private FixedSizeQueue<JSONObject> queue = new FixedSizeQueue(MAX_RECORDS);
    private FixedSizeQueue<JSONObject> queueTemp = new FixedSizeQueue(1);
    //save mobile datas
    private FixedSizeQueue<JSONObject> queueMobile = new FixedSizeQueue(MAX_RECORDS);
    private FixedSizeQueue<JSONObject> queueMobileTemp = new FixedSizeQueue(1);

    public static WifiContivityManager getInstance() {
        if (wifiContivityManager == null) {
            wifiContivityManager = new WifiContivityManager();
        }

        return wifiContivityManager;
    }

    public void init(Context context) {
        mContext = context;
        registerReceiver();
        startLogSchedule(context);
    }

    private void registerReceiver(){
        if (mWifiStateReceiver == null) {
            mWifiStateReceiver = new WifiStateReceiver();
            IntentFilter intentFilter = new IntentFilter();
            intentFilter.addAction(WifiManager.NETWORK_STATE_CHANGED_ACTION);
            intentFilter.addAction(WIFI_CONTIVITY_ACTION);
            intentFilter.addAction(ConnectivityManager.CONNECTIVITY_ACTION);
            intentFilter.addAction(Intent.ACTION_SHUTDOWN);
            ContextUtil.getInstance().registerReceiver(mWifiStateReceiver, intentFilter, RequestPermission.REQUEST_PERMISSION_MY_BROADCAST, null);
        }
    }

    public void startLogSchedule(Context context) {
        BBLog.i(TAG, "startLogSchedule");
        Intent intentTmp = new Intent(WIFI_CONTIVITY_ACTION);
        PendingIntent pi = PendingIntent.getBroadcast(context, 0, intentTmp, PendingIntent.FLAG_UPDATE_CURRENT);
        AlarmManager am = (AlarmManager) context.getSystemService(Context.ALARM_SERVICE);
        am.cancel(pi);
        long timeOnMillis = SystemClock.elapsedRealtime()  + INTERVAL_TIME;
        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.KITKAT) {
            am.set(AlarmManager.ELAPSED_REALTIME_WAKEUP, timeOnMillis, pi);
        } else {
            am.setExact(AlarmManager.ELAPSED_REALTIME_WAKEUP, timeOnMillis, pi);
        }
    }

    private class WifiStateReceiver extends BroadcastReceiver {
        @Override
        public void onReceive(Context context, Intent intent) {
            BBLog.d(TAG, "WifiStateReceiver action = " + intent.getAction());
            if (WifiManager.NETWORK_STATE_CHANGED_ACTION.equals(intent.getAction())) {
                NetworkInfo networkInfo = intent.getParcelableExtra(WifiManager.EXTRA_NETWORK_INFO);
                if (networkInfo != null && networkInfo.getType() == ConnectivityManager.TYPE_WIFI) {
                    WifiManager wifiManager = (WifiManager) context.getSystemService(Context.WIFI_SERVICE);
                    WifiInfo wifiInfo = wifiManager.getConnectionInfo();
                    if (networkInfo.getState() == NetworkInfo.State.CONNECTED) {
                        try {
                            JSONObject wifiContivity = getTempContivityInfo(ConnectivityManager.TYPE_WIFI);
                            //由于在连接过程中会频繁收到NETWORK_STATE_CHANGED_ACTION，connected时也会重复收到，这里做一次过滤
                            if (wifiContivity != null && System.currentTimeMillis() - wifiContivity.getLong("startTime") <= 5*1000) {
                                return;
                            }
                            if (wifiContivity != null) {
                                wifiContivity.put("endTime",String.valueOf(System.currentTimeMillis()));
                            } else {
                                wifiContivity = new JSONObject();
                                wifiContivity.put("startTime", String.valueOf(System.currentTimeMillis()));
                                wifiContivity.put("message", "");
                                wifiContivity.put("SSID", wifiInfo.getSSID().replace("\"", ""));
                                wifiContivity.put("SSTH", "" + wifiInfo.getRssi());
                                wifiContivity.put("FREQ", WifiLocationManager.getInstance(context).getWifiFrequency(wifiInfo.getFrequency()));
                            }
                            BBLog.d(TAG,"WifiStateReceiver NETWORK_STATE_CHANGED_ACTION WIFI--> add a record.");
                            addContivityInfo(wifiContivity,ConnectivityManager.TYPE_WIFI);

                            if (queueTemp.size() == 0) {
                                BBLog.d(TAG,"WifiStateReceiver NETWORK_STATE_CHANGED_ACTION WIFI--> Cache queue's empty, add a new record.");
                                addContivityInfo( createContivityInfo(ConnectivityManager.TYPE_WIFI),ConnectivityManager.TYPE_WIFI);
                            }
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    } else if (networkInfo.getState() == NetworkInfo.State.DISCONNECTED) {
                        SupplicantState supplicantState = wifiInfo.getSupplicantState();
                        String reasonText = getDisconnectReasonText(supplicantState);
                        BBLog.d(TAG, "WiFi disconnected reason: " + reasonText);

                        JSONObject wifiContivityInfo = getTempContivityInfo(ConnectivityManager.TYPE_WIFI);
                        if (wifiContivityInfo != null) {
                            try {
                                wifiContivityInfo.put("endTime",String.valueOf(System.currentTimeMillis()));
                                wifiContivityInfo.put("message",reasonText);
                                addContivityInfo(wifiContivityInfo,ConnectivityManager.TYPE_WIFI);
                            } catch (Exception e) {
                                e.printStackTrace();
                            }
                        }
                    }
                }
            } else if (ConnectivityManager.CONNECTIVITY_ACTION.equals(intent.getAction())) {
                WifiManager wifiManager = (WifiManager)ContextUtil.getInstance().getApplicationContext().getSystemService(android.content.Context.WIFI_SERVICE);
                WifiInfo wifiInfo = wifiManager.getConnectionInfo();
                BBLog.d(TAG,"WifiStateReceiver CONNECTIVITY_ACTION Mobile  wifiInfo--> " + wifiInfo);
                if (wifiInfo != null && !WIFISSID_NONE.equals(wifiInfo.getSSID())) {
                    BBLog.d(TAG,"WifiStateReceiver CONNECTIVITY_ACTION Mobile--> 终端WIFI enable.");
                    return;
                }
                ConnectivityManager connectivityManager = (ConnectivityManager) context.getSystemService(Context.CONNECTIVITY_SERVICE);
                if (connectivityManager != null) {
                    NetworkInfo networkInfo = connectivityManager.getActiveNetworkInfo();
                    if (networkInfo != null && networkInfo.getType() == ConnectivityManager.TYPE_MOBILE) {
                        if (networkInfo.getState() == NetworkInfo.State.CONNECTED) {
                            try {
                                JSONObject mobileContivity = getTempContivityInfo(ConnectivityManager.TYPE_MOBILE);
//                              //由于在连接过程中会频繁收到android.net.conn.CONNECTIVITY_CHANGE，connected时也会重复收到，这里做一次过滤
                                if (mobileContivity != null && System.currentTimeMillis() - mobileContivity.getLong("startTime") <= 5*1000) {
                                    return;
                                }
                                if (mobileContivity != null) {
                                    mobileContivity.put("endTime",String.valueOf(System.currentTimeMillis()));
                                } else {
                                    CellLocationManager cellManager = new CellLocationManager(ContextUtil.getInstance());
                                    mobileContivity = new JSONObject();
                                    mobileContivity.put("startTime", String.valueOf(System.currentTimeMillis()));
                                    mobileContivity.put("message", "");
                                    mobileContivity.put("IMSI  ",cellManager.getIMSI());
                                    mobileContivity.put("ICCID", cellManager.getICCID());
                                    mobileContivity.put("DBM", ""+cellManager.getSimSignalStrength());
                                }
                                BBLog.d(TAG,"WifiStateReceiver NETWORK_STATE_CHANGED_ACTION Mobile--> add a record.");
                                addContivityInfo(mobileContivity,ConnectivityManager.TYPE_MOBILE);

                                if (queueMobileTemp.size() == 0) {
                                    BBLog.d(TAG,"WifiStateReceiver NETWORK_STATE_CHANGED_ACTION Mobile--> Cache queue's empty, add a new record.");
                                    addContivityInfo(createContivityInfo(ConnectivityManager.TYPE_MOBILE),ConnectivityManager.TYPE_MOBILE);
                                }
                            } catch (Exception e) {
                                e.printStackTrace();
                            }
                        } else if (networkInfo.getState() == NetworkInfo.State.DISCONNECTED) {
                            String reasonText = "Disconnected";
                            BBLog.d(TAG, "Mobile disconnected reason: " + reasonText);
                            JSONObject mobileContivity = getTempContivityInfo(ConnectivityManager.TYPE_MOBILE);
                            if (mobileContivity != null) {
                                try {
                                    mobileContivity.put("endTime",String.valueOf(System.currentTimeMillis()));
                                    mobileContivity.put("message",reasonText);
                                    addContivityInfo(mobileContivity,ConnectivityManager.TYPE_MOBILE);
                                } catch (Exception e) {
                                    e.printStackTrace();
                                }
                            }
                        }
                    } else {//network is null
                        String reasonText = "Disable";
                        if (!isSimCardAvailable(ContextUtil.getInstance())) {
                            reasonText = "No SimCard";
                        }
                        BBLog.d(TAG, "Mobile disconnected reason: " + reasonText);
                        JSONObject mobileContivity = getTempContivityInfo(ConnectivityManager.TYPE_MOBILE);
                        if (mobileContivity != null) {
                            try {
                                mobileContivity.put("endTime", String.valueOf(System.currentTimeMillis()));
                                mobileContivity.put("message", reasonText);
                                addContivityInfo(mobileContivity, ConnectivityManager.TYPE_MOBILE);
                            } catch (Exception e) {
                                e.printStackTrace();
                            }
                        }
                    }
                }
            }else if (WIFI_CONTIVITY_ACTION.equals(intent.getAction())) {
                if (Helpers.isWifi(context) && Helpers.isOnline(context)) {
                   scheduledUploadData(ConnectivityManager.TYPE_WIFI);
                } else if (Helpers.isMobile(context) && Helpers.isOnline(context)) {
                    scheduledUploadData(ConnectivityManager.TYPE_MOBILE);
                }
                startLogSchedule(context);
            } else if (Intent.ACTION_SHUTDOWN.equals(intent.getAction())) {
                //关机前上送一次
                String errorMsg = "Power Off";
                BatteryManager batteryManager = (BatteryManager) context.getSystemService(Context.BATTERY_SERVICE);
                int level = batteryManager.getIntProperty(BatteryManager.BATTERY_PROPERTY_CAPACITY);
                if (level <=1) {
                    errorMsg = "Out Of Battery";
                }

                try {
                    //将缓存wifi数据取出，并更新endTime,形成闭环
                    JSONObject object = getTempContivityInfo(ConnectivityManager.TYPE_WIFI);
                    if (object != null) {
                        object.put("endTime",String.valueOf(System.currentTimeMillis()));
                        if (!TextUtils.isEmpty(errorMsg)) {
                            object.put("message",errorMsg);
                        }
                        addContivityInfo(object,ConnectivityManager.TYPE_WIFI);
                    }

                    //将缓存mobile数据取出，并更新endTime,形成闭环
                    object = getTempContivityInfo(ConnectivityManager.TYPE_MOBILE);
                    if (object != null) {
                        object.put("endTime",String.valueOf(System.currentTimeMillis()));
                        if (!TextUtils.isEmpty(errorMsg)) {
                            object.put("message",errorMsg);
                        }
                        addContivityInfo(object,ConnectivityManager.TYPE_MOBILE);
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
                WebSocketSender.C0904_NetworkStatusUpload();
            }
        }

        private String getDisconnectReasonText(SupplicantState supplicantState) {
            switch (supplicantState) {
                case DISCONNECTED:
                    return "Disconnected";
                case DORMANT:
                    return "Dormant";
                case INACTIVE:
                    return "Inactive";
                case INTERFACE_DISABLED:
                    return "Interface disabled";
                case INVALID:
                    return "Invalid";
                case UNINITIALIZED:
                    return "Uninitialized";
                default:
                    return "Unknown reason";
            }
        }
    }
    
   /***************************** Contivity************************************/
    public JSONArray getContivityList(int type){
        JSONArray infos = new JSONArray();
        switch (type) {
            case ConnectivityManager.TYPE_WIFI:
                if (this.queueTemp != null && this.queueTemp.size() > 0) {
                    try {
                        //先将缓存数据取出，并更新endTime,形成闭环
                        JSONObject object = getTempContivityInfo(type);
                        object.put("endTime",String.valueOf(System.currentTimeMillis()));
                        addContivityInfo(object,type);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }

                if (Helpers.isWifi(ContextUtil.getInstance())) {
                    //由于当前wifi未断开，重新往缓存队列中添加记录,只更新startTime
                    addContivityInfo(createContivityInfo(type), type);
                }

                if (this.queue != null && this.queue.size() > 0) {
                    for (int i = 0; i < queue.size(); i++) {
                        infos.put(this.queue.get(i));
                    }
                }
                break;
            case ConnectivityManager.TYPE_MOBILE:
                if (this.queueMobileTemp != null && this.queueMobileTemp.size() > 0) {
                    try {
                        //先将缓存数据取出，并更新endTime,形成闭环
                        JSONObject object = getTempContivityInfo(type);
                        object.put("endTime",String.valueOf(System.currentTimeMillis()));
                        addContivityInfo(object,type);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }

                if (Helpers.isMobile(ContextUtil.getInstance())) {
                    //由于当前mobile未断开，重新往缓存队列中添加记录,只更新startTime
                    addContivityInfo(createContivityInfo(type), type);
                }

                if (this.queueMobile != null && this.queueMobile.size() > 0) {
                    for (int i = 0; i < queueMobile.size(); i++) {
                        infos.put(this.queueMobile.get(i));
                    }
                }
                break;
        }
        return infos;
    }

    public JSONObject createContivityInfo(int type){
        try {
            switch (type) {
                case ConnectivityManager.TYPE_WIFI:
                    if (this.queueTemp.size() == 0) {
                        WifiInfo info = WifiLocationManager.getInstance(ContextUtil.getInstance()).getCurrentWIFI(mContext);
                        if (info != null) {
                            JSONObject object = new JSONObject();
                            object.put("startTime",String.valueOf(System.currentTimeMillis()));
                            object.put("message","");
                            object.put("SSID",info.getSSID().replace("\"",""));
                            object.put("SSTH", "" + info.getRssi());
                            object.put("FREQ",WifiLocationManager.getInstance(mContext).getWifiFrequency(info.getFrequency()));
                            return object;
                        }
                    }
                    break;
                case ConnectivityManager.TYPE_MOBILE:
                    if (this.queueMobileTemp.size() == 0) {
                        CellLocationManager cellManager = new CellLocationManager(ContextUtil.getInstance());
                        if (cellManager != null) {
                            JSONObject object = new JSONObject();
                            object.put("startTime",String.valueOf(System.currentTimeMillis()));
                            object.put("message","");

                            object.put("IMSI  ",cellManager.getIMSI());
                            object.put("ICCID", cellManager.getICCID());
                            object.put("DBM", ""+cellManager.getSimSignalStrength());
                            return object;
                        }
                    }
                    break;
            }

        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public void clearContivityInfos(){
        if (this.queue != null) {
            this.queue.clear();
        }
        if (this.queueMobile != null) {
            this.queueMobile.clear();
        }
    }

    public void addContivityInfo(JSONObject jsonObject, int type) {
        if (jsonObject != null) {
            switch (type) {
                case ConnectivityManager.TYPE_WIFI:
                    BBLog.d(TAG,"WifiStateReceiver -->addWifiContivityInfo : " + jsonObject);
                    this.queueTemp.add(jsonObject);
                    if (jsonObject.has("startTime") && jsonObject.has("endTime")) {
                        this.queue.add(this.queueTemp.queue.poll());
                    }
                    break;
                case ConnectivityManager.TYPE_MOBILE:
                    BBLog.d(TAG,"WifiStateReceiver -->addMobileContivityInfo : " + jsonObject);
                    this.queueMobileTemp.add(jsonObject);
                    if (jsonObject.has("startTime") && jsonObject.has("endTime")) {
                        this.queueMobile.add(this.queueMobileTemp.queue.poll());
                    }
                    break;
            }
        }
    }

    public JSONObject getTempContivityInfo(int type){
        switch (type){
            case ConnectivityManager.TYPE_WIFI:
                if (this.queueTemp != null) {
                    return this.queueTemp.getQueue().poll();
                }
                break;
            case ConnectivityManager.TYPE_MOBILE:
                if (this.queueMobileTemp != null) {
                    return this.queueMobileTemp.getQueue().poll();
                }
                break;
        }
        return null;
    }

    /**
     * 定时上送
     * @param type
     * @return
     */
    public void scheduledUploadData(int type){
        switch (type){
            case ConnectivityManager.TYPE_WIFI:
                if ( queue.size() < MAX_RECORDS) {
                    if ( queueTemp.size() != 0) {
                        try {
                            //先将缓存数据取出，并更新endTime,形成闭环
                            JSONObject object =  getTempContivityInfo(ConnectivityManager.TYPE_WIFI);
                            BBLog.d(TAG,"WifiStateReceiver scheduledUploadData --> getTempWifiContivityInfo:" + object );
                            object.put("endTime",String.valueOf(System.currentTimeMillis()));
                            addContivityInfo(object,ConnectivityManager.TYPE_WIFI);
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }
                }
                BBLog.d(TAG,"WifiStateReceiver --> Wifi Pre-upload queue's size:" + queue.size() );

                if (queueTemp.size() == 0) {
                    //由于当前wifi未断开，重新往缓存队列中添加记录,只更新startTime
                    BBLog.d(TAG,"WifiStateReceiver -->Wifi Cache queue's empty, add a new record.");
                    addContivityInfo( createContivityInfo(ConnectivityManager.TYPE_WIFI),ConnectivityManager.TYPE_WIFI);
                }

                if (queue.size() == MAX_RECORDS) {
                    BBLog.d(TAG,"WifiStateReceiver -->Wifi Pre-upload queue's full, upload records.");
                    WebSocketSender.C0904_NetworkStatusUpload();
                }
                break;
            case ConnectivityManager.TYPE_MOBILE:
                if ( queueMobile.size() < MAX_RECORDS) {
                    if ( queueMobileTemp.size() != 0) {
                        try {
                            //先将缓存数据取出，并更新endTime,形成闭环
                            JSONObject object =  getTempContivityInfo(ConnectivityManager.TYPE_MOBILE);
                            BBLog.d(TAG,"WifiStateReceiver scheduledUploadData--> getTempMobileContivityInfo :" + object );
                            object.put("endTime",String.valueOf(System.currentTimeMillis()));
                            addContivityInfo(object,ConnectivityManager.TYPE_MOBILE);
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }
                }
                BBLog.d(TAG,"WifiStateReceiver --> Mobile Pre-upload queue's size:" + queueMobile.size() );

                if (queueMobileTemp.size() == 0) {
                    //由于当前mobile未断开，重新往缓存队列中添加记录,只更新startTime
                    BBLog.d(TAG,"WifiStateReceiver -->Mobile Cache queue's empty, add a new record.");
                    addContivityInfo( createContivityInfo(ConnectivityManager.TYPE_MOBILE),ConnectivityManager.TYPE_MOBILE);
                }

                if (queueMobile.size() == MAX_RECORDS) {
                    BBLog.d(TAG,"WifiStateReceiver -->Mobile Pre-upload queue's full, upload records.");
                    WebSocketSender.C0904_NetworkStatusUpload();
                }
                break;
        }
    }

    /***************************** Contivity************************************/

    public static boolean isSimCardAvailable(Context context) {
        try {
            android.telephony.TelephonyManager telephonyManager = (android.telephony.TelephonyManager) context.getSystemService(android.content.Context.TELEPHONY_SERVICE);
            if (telephonyManager != null) {
                int simState = telephonyManager.getSimState();
                switch (simState) {
                    case android.telephony.TelephonyManager.SIM_STATE_ABSENT:
                    case android.telephony.TelephonyManager.SIM_STATE_UNKNOWN:
                        // SIM卡不存在或状态未知
                        return false;
                    default:
                        return true;
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

    private class FixedSizeQueue<E> {
        private Queue<E> queue;
        private int maxSize;

        public FixedSizeQueue(int maxSize) {
            this.maxSize = maxSize;
            this.queue = new ArrayDeque<>(maxSize);
        }

        public void add(E element) {
            if (queue.size() >= maxSize) {
                queue.poll(); // 移除队列头部元素
            }
            queue.offer(element); // 添加元素到队列尾部
        }

        public E get(int index) {
            return new ArrayList<>(queue).get(index);
        }

        public int size() {
            return queue.size();
        }

        public Queue<E> getQueue() {
            return queue;
        }

        public void clear() {
            queue.clear();
        }
    }
}
