<resources>

    <!-- Base application theme. -->
    <style name="AppTheme" parent="Theme.AppCompat.Light.DarkActionBar">
        <!-- Customize your theme here. -->
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="colorPrimaryDark">@color/colorPrimaryDark</item>
        <item name="colorAccent">@color/colorAccent</item>
    </style>

    <style name="dialog_style" parent="@android:style/Theme.Dialog">
        <item name="android:windowFrame">@null</item>
        <item name="android:windowNoTitle">true</item>//无标题
        <item name="android:windowBackground">@null</item>//背景
        <item name="android:windowIsFloating">true</item>//悬浮
        <item name="android:windowContentOverlay">@null</item>
    </style>

    <style name="dialog_style_ex" parent="@android:style/Theme.Dialog">
        <item name="android:windowFrame">@null</item>
        <item name="android:windowNoTitle">true</item>//无标题
        <item name="android:windowBackground">@android:color/transparent</item>//背景
        <item name="android:windowIsFloating">true</item>//悬浮
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowIsTranslucent">true</item>
    </style>

    <style name="checkbox_style" parent="@android:style/Widget.CompoundButton.CheckBox">
        <item name="android:button">@drawable/checkbox_selector</item>
    </style>

    <style name="arrow_style" parent="@android:style/Widget.CompoundButton.CheckBox">
        <item name="android:button">@drawable/arrow_selector</item>
    </style>

    <style name="wfif_dialog_activity">
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowIsTranslucent">true</item>
    </style>

    <style name="no_statusbar_activity">
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowFullscreen">true</item>
        <item name="android:windowNoTitle">true</item>
    </style>

    <style name="wifi_item">
        <item name="android:layout_marginTop">8dip</item>
        <item name="android:layout_marginStart">8dip</item>
        <item name="android:layout_marginEnd">8dip</item>
        <item name="android:paddingStart">8dip</item>
        <item name="android:paddingEnd">8dip</item>
        <item name="android:orientation">vertical</item>
        <item name="android:gravity">start</item>
    </style>

    <style name="wifi_item_label">
        <item name="android:paddingStart">8dip</item>
        <item name="android:textSize">14sp</item>
        <item name="android:textAlignment">viewStart</item>
        <item name="android:textAppearance">@android:style/TextAppearance.Material.Body1</item>
    </style>
    <!--
    <style name="AppTheme" parent="android:Theme.Material.Light.NoActionBar">
        <item name="android:windowTranslucentStatus">true</item>
        <item name="android:windowShowWallpaper">true</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:colorBackgroundCacheHint">@null</item>
    </style>
    -->
    <style name="MenuIcon">
        <item name="android:layout_width">35dp</item>
        <item name="android:layout_height">35dp</item>
        <item name="android:layout_marginLeft">10dp</item>
        <item name="android:scaleType">fitCenter</item>
        <item name="android:clickable">false</item>
        <item name="android:layout_gravity">center_vertical</item>
    </style>
    <style name="MenuText">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">match_parent</item>
        <item name="android:textColor">#ffffff</item>
        <item name="android:textSize">18sp</item>
        <item name="android:singleLine">true</item>
        <item name="android:gravity">center_vertical</item>
        <item name="android:clickable">false</item>
        <item name="android:text">@string/app_name</item>
    </style>

    <style name="text_18_000000">
        <item name="android:textSize">18.0dip</item>
        <item name="android:textColor">#ff000000</item>
    </style>
    <style name="text_16_666666">
        <item name="android:textSize">16.0dip</item>
        <item name="android:textColor">#ff666666</item>
    </style>
    <style name="sdw_white">
        <item name="android:shadowColor">#7fffffff</item>
        <item name="android:shadowDx">0.0</item>
        <item name="android:shadowDy">0.65</item>
        <item name="android:shadowRadius">1.0</item>
    </style>
    <style name="sdw_79351b">
        <item name="android:shadowColor">#ff79351b</item>
        <item name="android:shadowDx">0.0</item>
        <item name="android:shadowDy">1.0</item>
        <item name="android:shadowRadius">1.0</item>
    </style>
    <style name="text_15_ffffff_sdw" parent="@style/sdw_79351b">
        <item name="android:textSize">15.0dip</item>
        <item name="android:textColor">#ffffffff</item>
    </style>
    <style name="text_15_666666_sdw" parent="@style/sdw_white">
        <item name="android:textSize">15.0dip</item>
        <item name="android:textColor">#ff666666</item>
    </style>
    <style name="Dialog" parent="android:style/Theme.Dialog">
        <item name="android:background">#00000000</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowIsFloating">true</item>
    </style>
    <style name="MainMenuRelativeLayout">
        <item name="android:layout_width">fill_parent</item>
        <item name="android:layout_height">fill_parent</item>
        <item name="android:layout_weight">1.0</item>
        <item name="android:gravity">center</item>
        <item name="android:clickable">true</item>
    </style>
    <style name="MainMenuTextView" parent="@android:style/TextAppearance.DeviceDefault.Widget.TextView">
        <item name="android:layout_width">fill_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:gravity">center</item>
        <item name="android:layout_centerHorizontal">true</item>
        <item name="android:textSize">14sp</item>
    </style>
    <!--
    <style parent="@android:style/Theme.Dialog" name="dialog_style">
        <item name="android:windowFrame">@null</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowBackground">@null</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowContentOverlay">@null</item>
    </style>
    -->
</resources>
