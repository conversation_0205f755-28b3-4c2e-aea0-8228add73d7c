package com.dspread.mdm.service.broadcast.handlers.system

import android.content.Context
import android.content.Intent
import com.dspread.mdm.service.broadcast.core.BroadcastActions
import com.dspread.mdm.service.broadcast.core.BroadcastEventHandler
import com.dspread.mdm.service.network.websocket.message.WsMessageSender
import com.dspread.mdm.service.utils.log.Logger

/**
 * 屏幕事件处理器实现
 * 专门处理屏幕开关相关的事件
 */
class ScreenEventHandlerImpl : BroadcastEventHandler {
    
    private val TAG = "ScreenEventHandler"
    
    override fun getHandlerName(): String = TAG
    
    override fun getSupportedActions(): List<String> {
        return listOf(
            BroadcastActions.ACTION_SCREEN_ON,
            BroadcastActions.ACTION_SCREEN_OFF
        )
    }
    
    override fun handleBroadcast(context: Context, intent: Intent): Boolean {
        val action = intent.action ?: return false
        
        return try {
            when (action) {
                BroadcastActions.ACTION_SCREEN_ON -> {
                    handleScreenOn(context)
                    true
                }
                BroadcastActions.ACTION_SCREEN_OFF -> {
                    handleScreenOff(context)
                    true
                }
                else -> false
            }
        } catch (e: Exception) {
            Logger.receiverE("$TAG 处理广播失败: $action", e)
            false
        }
    }
    
    /**
     * 处理屏幕点亮
     */
    private fun handleScreenOn(context: Context) {
        Logger.receiver("屏幕点亮")
        
        try {
            // 屏幕点亮时的处理逻辑
            // 1. 恢复某些后台任务
            // 2. 检查连接状态
            // 3. 更新设备使用状态
            
            // 示例：上报设备使用状态变化
            WsMessageSender.uploadDeviceStatus(
                deviceStatus = "normal",
                isInUse = true,
                trigger = "screen_on"
            )
            
            Logger.receiver("屏幕点亮处理完成")
            
        } catch (e: Exception) {
            Logger.receiverE("处理屏幕点亮事件失败", e)
        }
    }
    
    /**
     * 处理屏幕关闭
     */
    private fun handleScreenOff(context: Context) {
        Logger.receiver("屏幕关闭")
        
        try {
            // 屏幕关闭时的处理逻辑
            // 1. 暂停某些任务
            // 2. 降低活动频率
            // 3. 更新设备使用状态
            
            // 示例：上报设备使用状态变化
            WsMessageSender.uploadDeviceStatus(
                deviceStatus = "normal", 
                isInUse = false,
                trigger = "screen_off"
            )
            
            Logger.receiver("屏幕关闭处理完成")
            
        } catch (e: Exception) {
            Logger.receiverE("处理屏幕关闭事件失败", e)
        }
    }
}
