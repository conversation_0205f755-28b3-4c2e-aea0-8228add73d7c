package com.bbpos.bootupcustom;

import android.util.Log;

import com.bbpos.wiseapp.logger.BBLog;
import com.bbpos.wiseapp.system.api.DeviceInfoApi;

import java.io.FileDescriptor;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;

public class LogoBlockDevice {

    private static final String TAG = "LogoBlockDevice";

    private FileDescriptor mFd;
    private FileInputStream mFileInputStream;
    private FileOutputStream mFileOutputStream;

    public LogoBlockDevice() throws SecurityException, IOException {
        if (DeviceInfoApi.getIntance().isWisePos5Plus() || DeviceInfoApi.getIntance().isWisePos5()) {
            mFd = open("/dev/block/bootdevice/by-name/splash");
        } else {
            mFd = open("/dev/block/platform/bootdevice/by-name/logo");
        }
        if (mFd == null) {
            BBLog.e(BBLog.TAG, "native open returns null");
            throw new IOException();
        }
        mFileInputStream = new FileInputStream(mFd);
        mFileOutputStream = new FileOutputStream(mFd);
    }

    // Getters and setters
    public InputStream getInputStream() {
        return mFileInputStream;
    }

    public OutputStream getOutputStream() {
        return mFileOutputStream;
    }

    // JNI
    private native static FileDescriptor open(String file);
    public native void close();
    static {
        System.loadLibrary("logo_block_device");
    }
}
