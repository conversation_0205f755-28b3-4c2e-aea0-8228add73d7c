package com.dspread.mdm.service.modules.apn

import android.annotation.SuppressLint
import android.content.Context
import android.net.TrafficStats
import android.os.Build
import android.telephony.*
import com.dspread.mdm.service.modules.apn.model.CarrierInfo
import com.dspread.mdm.service.modules.apn.model.DataUsage
import com.dspread.mdm.service.modules.apn.model.NetworkStatus
import com.dspread.mdm.service.modules.apn.model.NetworkType
import com.dspread.mdm.service.platform.api.device.DeviceInfoApi
import com.dspread.mdm.service.platform.collector.DeviceDataCollector
import com.dspread.mdm.service.utils.log.Logger
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

/**
 * 运营商检测器
 * 基于现有DeviceInfoApi和DeviceDataCollector扩展运营商检测功能
 */
class CarrierDetector(
    private val context: Context
) {
    
    companion object {
        private const val TAG = "[CarrierDetector]"
    }
    
    // 复用现有组件
    private val deviceInfoApi = DeviceInfoApi(context)
    private val deviceDataCollector = DeviceDataCollector(context)
    private val telephonyManager = context.getSystemService(Context.TELEPHONY_SERVICE) as TelephonyManager
    private val subscriptionManager by lazy {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP_MR1) {
            context.getSystemService(Context.TELEPHONY_SUBSCRIPTION_SERVICE) as SubscriptionManager
        } else {
            null
        }
    }
    
    /**
     * 检测所有SIM卡的运营商信息
     */
    suspend fun detectAllCarriers(): List<CarrierInfo> {
        return withContext(Dispatchers.IO) {
            try {
                Logger.apnI("$TAG 开始检测运营商信息")
                
                val carriers = mutableListOf<CarrierInfo>()
                
                // 检测双卡设备
                if (isDualSimDevice()) {
                    Logger.apnI("$TAG 检测到双卡设备")
                    
                    // 获取所有活跃的订阅
                    val subscriptions = getActiveSubscriptions()
                    
                    subscriptions.forEachIndexed { index, subscription ->
                        try {
                            val carrierInfo = detectCarrierForSubscription(subscription, index)
                            if (carrierInfo != null) {
                                carriers.add(carrierInfo)
                            }
                        } catch (e: Exception) {
                            Logger.apnE("$TAG 检测SIM卡 $index 运营商信息失败", e)
                        }
                    }
                } else {
                    Logger.apn("$TAG 检测到单卡设备")
                    
                    // 单卡设备
                    val carrierInfo = detectPrimaryCarrier()
                    if (carrierInfo != null) {
                        carriers.add(carrierInfo)
                    }
                }
                
                Logger.apnI("$TAG 运营商检测完成，发现 ${carriers.size} 个运营商")
                carriers
                
            } catch (e: Exception) {
                Logger.apnE("$TAG 检测运营商信息失败", e)
                emptyList()
            }
        }
    }
    
    /**
     * 检测主要运营商信息
     */
    @SuppressLint("MissingPermission")
    private fun detectPrimaryCarrier(): CarrierInfo? {
        return try {
            // 复用现有的基站信息收集功能
            val baseSiteInfo = deviceDataCollector.collectBaseSiteInfo()
            
            val mcc = baseSiteInfo.optString("MCC", "")
            val mnc = baseSiteInfo.optString("MNC", "")
            val imsi = baseSiteInfo.optString("IMSI", "")
            
            // 获取运营商信息
            val simOperator = telephonyManager.simOperator ?: ""
            val simOperatorName = telephonyManager.simOperatorName ?: ""
            val networkOperator = telephonyManager.networkOperator ?: ""
            val networkOperatorName = telephonyManager.networkOperatorName ?: ""
            val countryIso = telephonyManager.simCountryIso ?: ""
            
            // 获取SIM卡状态
            val simState = telephonyManager.simState
            val dataState = telephonyManager.dataState
            val networkType = telephonyManager.networkType
            val isRoaming = telephonyManager.isNetworkRoaming
            
            // 获取信号强度
            val signalStrength = getSignalStrength()
            
            // 生成运营商ID
            val carrierId = generateCarrierId(mcc, mnc)
            
            CarrierInfo(
                carrierId = carrierId,
                carrierName = getCarrierName(mcc, mnc) ?: simOperatorName,
                mcc = mcc.ifEmpty { simOperator.take(3) },
                mnc = mnc.ifEmpty { simOperator.drop(3) },
                numeric = simOperator,
                countryIso = countryIso,
                simOperator = simOperator,
                simOperatorName = simOperatorName,
                networkOperator = networkOperator,
                networkOperatorName = networkOperatorName,
                simSlot = 0,
                simState = simState,
                networkType = networkType,
                isRoaming = isRoaming,
                signalStrength = signalStrength,
                dataState = dataState
            )
            
        } catch (e: Exception) {
            Logger.apnE("$TAG 检测主要运营商失败", e)
            null
        }
    }
    
    /**
     * 检测指定订阅的运营商信息
     */
    @SuppressLint("MissingPermission")
    private fun detectCarrierForSubscription(subscription: SubscriptionInfo, slotIndex: Int): CarrierInfo? {
        return try {
            if (Build.VERSION.SDK_INT < Build.VERSION_CODES.LOLLIPOP_MR1) {
                return null
            }
            
            val mcc = subscription.mcc.toString().padStart(3, '0')
            val mnc = subscription.mnc.toString().let { 
                if (it.length == 1) "0$it" else it 
            }
            val numeric = "${mcc}${mnc}"
            
            // 获取运营商名称
            val carrierName = subscription.carrierName?.toString() ?: ""
            val displayName = subscription.displayName?.toString() ?: ""
            
            // 获取国家代码
            val countryIso = subscription.countryIso ?: ""
            
            // 获取SIM卡状态
            val simState = getSimStateForSlot(slotIndex)
            val dataState = getDataStateForSlot(slotIndex)
            val networkType = getNetworkTypeForSlot(slotIndex)
            val isRoaming = getRoamingStateForSlot(slotIndex)
            
            // 获取信号强度
            val signalStrength = getSignalStrengthForSlot(slotIndex)
            
            // 生成运营商ID（系统应用简化处理）
            val carrierId = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                subscription.carrierId
            } else {
                // 对于低版本Android，使用MCC+MNC生成运营商ID
                generateCarrierId(mcc, mnc)
            }
            
            CarrierInfo(
                carrierId = carrierId,
                carrierName = getCarrierName(mcc, mnc) ?: carrierName,
                mcc = mcc,
                mnc = mnc,
                numeric = numeric,
                countryIso = countryIso,
                simOperator = numeric,
                simOperatorName = carrierName,
                networkOperator = numeric,
                networkOperatorName = displayName,
                simSlot = slotIndex,
                simState = simState,
                networkType = networkType,
                isRoaming = isRoaming,
                signalStrength = signalStrength,
                dataState = dataState
            )
            
        } catch (e: Exception) {
            Logger.apnE("$TAG 检测订阅 $slotIndex 运营商失败", e)
            null
        }
    }
    
    /**
     * 检查是否为双卡设备
     */
    @SuppressLint("MissingPermission")
    private fun isDualSimDevice(): Boolean {
        return try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                telephonyManager.phoneCount > 1
            } else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP_MR1) {
                // 对于API 22，使用SubscriptionManager检查
                (subscriptionManager?.activeSubscriptionInfoList?.size ?: 0) > 1
            } else {
                false
            }
        } catch (e: Exception) {
            Logger.apnE("$TAG 检查双卡设备失败", e)
            false
        }
    }
    
    /**
     * 获取活跃的订阅信息
     */
    @SuppressLint("MissingPermission")
    private fun getActiveSubscriptions(): List<SubscriptionInfo> {
        return try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP_MR1 && subscriptionManager != null) {
                subscriptionManager!!.activeSubscriptionInfoList ?: emptyList()
            } else {
                emptyList()
            }
        } catch (e: Exception) {
            Logger.apnE("$TAG 获取活跃订阅失败", e)
            emptyList()
        }
    }
    
    /**
     * 获取信号强度
     */
    @SuppressLint("MissingPermission")
    private fun getSignalStrength(): Int {
        return try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
                // Android 9.0+ 使用SignalStrength API
                val signalStrength = telephonyManager.signalStrength
                signalStrength?.level ?: -1
            } else {
                // 低版本Android使用CellInfo获取信号强度
                val cellInfos = telephonyManager.allCellInfo
                if (cellInfos != null && cellInfos.isNotEmpty()) {
                    val registeredCell = cellInfos.find { it.isRegistered }
                    when (registeredCell) {
                        is android.telephony.CellInfoGsm -> {
                            registeredCell.cellSignalStrength.level
                        }
                        is android.telephony.CellInfoLte -> {
                            registeredCell.cellSignalStrength.level
                        }
                        is android.telephony.CellInfoWcdma -> {
                            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR2) {
                                registeredCell.cellSignalStrength.level
                            } else {
                                -1
                            }
                        }
                        is android.telephony.CellInfoCdma -> {
                            registeredCell.cellSignalStrength.level
                        }
                        else -> -1
                    }
                } else {
                    Logger.apnW("$TAG 无法获取CellInfo，返回默认信号强度")
                    -1
                }
            }
        } catch (e: Exception) {
            Logger.apnE("$TAG 获取信号强度失败", e)
            -1
        }
    }
    
    /**
     * 获取指定卡槽的SIM卡状态
     */
    private fun getSimStateForSlot(slotIndex: Int): Int {
        return try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                telephonyManager.getSimState(slotIndex)
            } else {
                telephonyManager.simState
            }
        } catch (e: Exception) {
            Logger.apnE("$TAG 获取SIM卡状态失败: slot $slotIndex", e)
            TelephonyManager.SIM_STATE_UNKNOWN
        }
    }
    
    /**
     * 获取指定卡槽的数据连接状态
     */
    private fun getDataStateForSlot(slotIndex: Int): Int {
        return try {
            telephonyManager.dataState
        } catch (e: Exception) {
            Logger.apnE("$TAG 获取数据连接状态失败: slot $slotIndex", e)
            TelephonyManager.DATA_UNKNOWN
        }
    }
    
    /**
     * 获取指定卡槽的网络类型
     */
    @SuppressLint("MissingPermission")
    private fun getNetworkTypeForSlot(slotIndex: Int): Int {
        return try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                telephonyManager.getDataNetworkType()
            } else {
                telephonyManager.networkType
            }
        } catch (e: Exception) {
            Logger.apnE("$TAG 获取网络类型失败: slot $slotIndex", e)
            TelephonyManager.NETWORK_TYPE_UNKNOWN
        }
    }
    
    /**
     * 获取指定卡槽的漫游状态
     */
    private fun getRoamingStateForSlot(slotIndex: Int): Boolean {
        return try {
            telephonyManager.isNetworkRoaming
        } catch (e: Exception) {
            Logger.apnE("$TAG 获取漫游状态失败: slot $slotIndex", e)
            false
        }
    }
    
    /**
     * 获取指定卡槽的信号强度
     */
    private fun getSignalStrengthForSlot(slotIndex: Int): Int {
        return try {
            // 这里可以实现更精确的信号强度获取
            getSignalStrength()
        } catch (e: Exception) {
            Logger.apnE("$TAG 获取信号强度失败: slot $slotIndex", e)
            -1
        }
    }
    
    /**
     * 生成运营商ID
     */
    private fun generateCarrierId(mcc: String, mnc: String): Int {
        return try {
            val numeric = "$mcc$mnc"
            // 使用numeric的整数值作为运营商ID，更稳定可靠
            numeric.toIntOrNull() ?: 0
        } catch (e: Exception) {
            Logger.apnE("$TAG 生成运营商ID失败: mcc=$mcc, mnc=$mnc", e)
            0
        }
    }
    
    /**
     * 根据MCC/MNC获取运营商名称
     */
    private fun getCarrierName(mcc: String, mnc: String): String? {
        return when ("$mcc$mnc") {
            // 中国移动
            "46000", "46002", "46007", "46008" -> "中国移动"
            
            // 中国联通
            "46001", "46006", "46009" -> "中国联通"
            
            // 中国电信
            "46003", "46005", "46011" -> "中国电信"
            
            // 中国广电
            "46004" -> "中国广电"
            
            // 美国运营商
            "310260" -> "T-Mobile USA"
            "310410" -> "AT&T"
            "311480" -> "Verizon"
            
            // 其他常见运营商可以继续添加
            else -> null
        }
    }
    
    /**
     * 获取当前数据使用情况
     */
    fun getCurrentDataUsage(): DataUsage {
        return try {
            val startTime = System.currentTimeMillis() - 3600000 // 1小时前
            val endTime = System.currentTimeMillis()
            
            val rxBytes = TrafficStats.getMobileRxBytes()
            val txBytes = TrafficStats.getMobileTxBytes()
            val rxPackets = TrafficStats.getMobileRxPackets()
            val txPackets = TrafficStats.getMobileTxPackets()
            
            DataUsage(
                rxBytes = if (rxBytes != TrafficStats.UNSUPPORTED.toLong()) rxBytes else 0,
                txBytes = if (txBytes != TrafficStats.UNSUPPORTED.toLong()) txBytes else 0,
                rxPackets = if (rxPackets != TrafficStats.UNSUPPORTED.toLong()) rxPackets else 0,
                txPackets = if (txPackets != TrafficStats.UNSUPPORTED.toLong()) txPackets else 0,
                startTime = startTime,
                endTime = endTime
            )
        } catch (e: Exception) {
            Logger.apnE("$TAG 获取数据使用情况失败", e)
            DataUsage(0, 0, 0, 0, System.currentTimeMillis(), System.currentTimeMillis())
        }
    }
    
    /**
     * 检测网络状态
     */
    fun detectNetworkStatus(): NetworkStatus {
        return try {
            // val carrierInfos = detectAllCarriers() // 暂时注释，避免协程调用错误
            val carrierInfos = emptyList<CarrierInfo>() // 临时返回空列表
            val primaryCarrier = carrierInfos.firstOrNull()
            
            val isConnected = primaryCarrier?.isDataConnected() ?: false
            val networkType = if (primaryCarrier != null) {
                NetworkType.fromTelephonyType(primaryCarrier.networkType)
            } else {
                NetworkType.NONE
            }
            
            val signalStrength = primaryCarrier?.signalStrength ?: -1
            val dataUsage = getCurrentDataUsage()
            
            NetworkStatus(
                isConnected = isConnected,
                networkType = networkType,
                signalStrength = signalStrength,
                dataUsage = dataUsage,
                timestamp = System.currentTimeMillis()
            )
        } catch (e: Exception) {
            Logger.apnE("$TAG 检测网络状态失败", e)
            NetworkStatus(
                isConnected = false,
                networkType = NetworkType.NONE,
                signalStrength = -1,
                dataUsage = DataUsage(0, 0, 0, 0, System.currentTimeMillis(), System.currentTimeMillis())
            )
        }
    }
    
    /**
     * 检查运营商检测器是否可用
     */
    fun isAvailable(): Boolean {
        return try {
            telephonyManager != null && deviceInfoApi.isAvailable()
        } catch (e: Exception) {
            Logger.apnE("$TAG 运营商检测器不可用", e)
            false
        }
    }


    /**
     * 获取当前运营商numeric
     */
    @SuppressLint("MissingPermission")
    fun getCurrentOperatorNumeric(): String {
        return try {
            Logger.apnI("$TAG 获取当前运营商numeric")

            val numeric = telephonyManager.simOperator
            if (numeric.isNullOrEmpty()) {
                Logger.apnW("$TAG 无法获取运营商numeric，可能未插入SIM卡")
                return ""
            }

            Logger.apn("$TAG 当前运营商numeric: $numeric")
            numeric

        } catch (e: Exception) {
            Logger.apnE("$TAG 获取当前运营商numeric失败", e)
            ""
        }
    }

}
