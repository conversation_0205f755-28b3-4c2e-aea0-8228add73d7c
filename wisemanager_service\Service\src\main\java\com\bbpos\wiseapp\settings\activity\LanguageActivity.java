package com.bbpos.wiseapp.settings.activity;

import android.app.Activity;
import android.content.Intent;
import android.content.res.Configuration;
import android.os.Bundle;
import android.view.View;
import android.view.View.OnClickListener;
import android.widget.AdapterView;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.ListView;
import android.widget.TextView;

import com.bbpos.wiseapp.service.R;
import com.bbpos.wiseapp.settings.adapter.LanguageAdapter;
import com.bbpos.wiseapp.settings.utils.HelperUtil;

import java.util.ArrayList;
import java.util.List;
import java.util.Locale;

//登录界面
public class LanguageActivity extends Activity implements View.OnClickListener{
	private List<String> listItem = new ArrayList<>();

	private ListView listView;
	private ImageView iv_close;

	@Override
	public void onCreate(Bundle savedInstanseBundle){
		setContentView(R.layout.activity_language);
		initData();
		initView();
		super.onCreate(savedInstanseBundle);
	}

	private void initData() {
		listItem.add(getString(R.string.english));
		listItem.add(getString(R.string.chinese_simplified));
		listItem.add(getString(R.string.chinese_traditional));
	}

	@Override
	protected void onResume() {
		super.onResume();
	}

	@Override
	public void onConfigurationChanged(Configuration newConfig) {
		super.onConfigurationChanged(newConfig);
		finish();
	}

	private void initView() {
		TextView title = findViewById(R.id.toolbar_title_tv);
		title.setText(getString(R.string.language));

		iv_close = findViewById(R.id.toolbar_left_btn);
		iv_close.setOnClickListener(this);

		//初始化列表
		listView = (ListView) findViewById(R.id.list_language);

		final LanguageAdapter listAdapter = new LanguageAdapter(LanguageActivity.this, listItem);
		listView.setAdapter(listAdapter);
		listView.setOnItemClickListener(new AdapterView.OnItemClickListener() {
			@Override
			public void onItemClick(AdapterView<?> parent, View view, final int position, long id) {
				if (HelperUtil.isFastDoubleClick()) { //判断是否是快速点击
					return;
				}
				//設置系統語言
				if (getString(R.string.english).equals(listItem.get(position))) {
					Locale locale = new Locale("en", "US");
					HelperUtil.changeSystemLanguage(locale);
				} else if (getString(R.string.chinese_simplified).equals(listItem.get(position))) {
					Locale locale = new Locale("zh", "CN");
					HelperUtil.changeSystemLanguage(locale);
				} else if (getString(R.string.chinese_traditional).equals(listItem.get(position))) {
					Locale locale = new Locale("zh", "HK");
					HelperUtil.changeSystemLanguage(locale);
				}
			}
		});
	}

	@Override
	public void onClick(View v) {
		switch (v.getId()) {
			case R.id.toolbar_left_btn:
				finish();
				break;

		}
	}
}
