package com.dspread.mdm.service.modules.logstream

import com.dspread.mdm.service.utils.log.Logger
import java.io.File

/**
 * 日志存储管理器
 * 负责管理日志文件的存储空间，清理旧文件
 */
class LogStorageManager(
    private val logDirectory: File,
    private val maxStorageSize: Long = 500 * 1024 * 1024L // 500MB
) {
    
    companion object {
        private const val TAG = "[LogStorageManager]"
    }
    
    /**
     * 检查存储空间并清理旧文件
     */
    fun checkAndCleanupStorage(): Boolean {
        return try {
            Logger.logStream("$TAG 开始检查存储空间")
            
            // 获取当前存储使用情况
            val currentSize = calculateDirectorySize(logDirectory)
            Logger.logStream("$TAG 当前存储使用: ${formatSize(currentSize)} / ${formatSize(maxStorageSize)}")
            
            // 如果超过限制，清理旧文件
            if (currentSize > maxStorageSize) {
                Logger.logStream("$TAG 存储空间超限，开始清理旧文件")
                cleanupOldFiles(currentSize - maxStorageSize)
            } else {
                Logger.logStream("$TAG 存储空间正常")
            }
            
            true
        } catch (e: Exception) {
            Logger.logStreamE("$TAG 检查存储空间失败", e)
            false
        }
    }
    
    /**
     * 清理旧文件
     * @param targetSize 需要清理的目标大小
     */
    private fun cleanupOldFiles(targetSize: Long) {
        try {
            Logger.logStream("$TAG 需要清理空间: ${formatSize(targetSize)}")
            
            // 获取所有压缩文件，按修改时间排序（最旧的在前）
            val compressedFiles = getCompressedFiles().sortedBy { it.lastModified() }
            
            var deletedSize = 0L
            var deletedCount = 0
            
            // 删除最旧的文件直到达到目标大小
            for (file in compressedFiles) {
                if (deletedSize >= targetSize) {
                    break
                }
                
                val fileSize = file.length()
                if (file.delete()) {
                    deletedSize += fileSize
                    deletedCount++
                    Logger.logStream("$TAG 删除旧文件: ${file.name} (${formatSize(fileSize)})")
                } else {
                    Logger.logStreamW("$TAG 删除文件失败: ${file.name}")
                }
            }
            
            Logger.logStream("$TAG 清理完成，删除文件: $deletedCount 个，释放空间: ${formatSize(deletedSize)}")
        } catch (e: Exception) {
            Logger.logStreamE("$TAG 清理旧文件失败", e)
        }
    }
    
    /**
     * 获取所有压缩文件
     */
    private fun getCompressedFiles(): List<File> {
        return try {
            logDirectory.walkTopDown()
                .filter { it.isFile && it.name.endsWith(".gz") }
                .toList()
        } catch (e: Exception) {
            Logger.logStreamE("$TAG 获取压缩文件列表失败", e)
            emptyList()
        }
    }
    
    /**
     * 计算目录大小
     */
    private fun calculateDirectorySize(directory: File): Long {
        return try {
            directory.walkTopDown()
                .filter { it.isFile }
                .map { it.length() }
                .sum()
        } catch (e: Exception) {
            Logger.logStreamE("$TAG 计算目录大小失败", e)
            0L
        }
    }
    
    /**
     * 格式化文件大小
     */
    private fun formatSize(size: Long): String {
        return when {
            size < 1024 -> "${size}B"
            size < 1024 * 1024 -> "${size / 1024}KB"
            size < 1024 * 1024 * 1024 -> "${size / (1024 * 1024)}MB"
            else -> "${size / (1024 * 1024 * 1024)}GB"
        }
    }
    
    /**
     * 获取存储统计信息
     */
    fun getStorageStats(): StorageStats {
        return try {
            val totalSize = calculateDirectorySize(logDirectory)
            val compressedFiles = getCompressedFiles()
            val compressedSize = compressedFiles.sumOf { it.length() }
            val compressedCount = compressedFiles.size
            
            // 获取未压缩文件
            val uncompressedFiles = logDirectory.walkTopDown()
                .filter { it.isFile && !it.name.endsWith(".gz") }
                .toList()
            val uncompressedSize = uncompressedFiles.sumOf { it.length() }
            val uncompressedCount = uncompressedFiles.size
            
            StorageStats(
                totalSize = totalSize,
                maxSize = maxStorageSize,
                compressedSize = compressedSize,
                compressedCount = compressedCount,
                uncompressedSize = uncompressedSize,
                uncompressedCount = uncompressedCount,
                usagePercentage = (totalSize * 100 / maxStorageSize).toInt()
            )
        } catch (e: Exception) {
            Logger.logStreamE("$TAG 获取存储统计信息失败", e)
            StorageStats()
        }
    }
    
    /**
     * 清理所有日志文件
     */
    fun clearAllLogs(): Boolean {
        return try {
            Logger.logStream("$TAG 开始清理所有日志文件")
            
            val allFiles = logDirectory.walkTopDown()
                .filter { it.isFile }
                .toList()
            
            var deletedCount = 0
            var deletedSize = 0L
            
            allFiles.forEach { file ->
                val fileSize = file.length()
                if (file.delete()) {
                    deletedCount++
                    deletedSize += fileSize
                }
            }
            
            Logger.logStream("$TAG 清理完成，删除文件: $deletedCount 个，释放空间: ${formatSize(deletedSize)}")
            true
        } catch (e: Exception) {
            Logger.logStreamE("$TAG 清理所有日志文件失败", e)
            false
        }
    }
}

/**
 * 存储统计信息
 */
data class StorageStats(
    val totalSize: Long = 0L,
    val maxSize: Long = 0L,
    val compressedSize: Long = 0L,
    val compressedCount: Int = 0,
    val uncompressedSize: Long = 0L,
    val uncompressedCount: Int = 0,
    val usagePercentage: Int = 0
)
