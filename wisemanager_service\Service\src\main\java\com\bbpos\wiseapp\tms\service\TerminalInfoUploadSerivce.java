package com.bbpos.wiseapp.tms.service;

import android.annotation.SuppressLint;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.SharedPreferences;
import android.os.BatteryManager;
import android.os.Handler;
import android.os.Looper;
import android.preference.PreferenceManager;
import android.util.Log;

import com.bbpos.wiseapp.logger.BBLog;
import com.bbpos.wiseapp.service.common.RequestPermission;
import com.bbpos.wiseapp.system.api.DeviceInfoApi;
import com.bbpos.wiseapp.tms.utils.WSCallType;
import com.bbpos.wiseapp.tms.adapter.IDeviceStatusCellectObserver;
import com.bbpos.wiseapp.tms.adapter.IUsageStatsObserver;
import com.bbpos.wiseapp.tms.adapter.SystemManagerAdapter;
import com.bbpos.wiseapp.service.WakeLockService;
import com.bbpos.wiseapp.tms.listener.device.HardwareInfo;
import com.bbpos.wiseapp.tms.network.HttpUtils;
import com.bbpos.wiseapp.tms.utils.Constants;
import com.bbpos.wiseapp.tms.utils.ParameterFactory;
import com.bbpos.wiseapp.tms.utils.ParameterName;
import com.bbpos.wiseapp.tms.utils.SPKeys;
import com.bbpos.wiseapp.websocket.Constant;
import com.bbpos.wiseapp.websocket.WebSocketCenter;
import com.bbpos.wiseapp.websocket.WebSocketSender;

import org.json.JSONObject;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

import com.bbpos.wiseapp.sdk.app.UsageStats;
import com.bbpos.wiseapp.sdk.device.DeviceStatus;

@SuppressLint("SimpleDateFormat")
public class TerminalInfoUploadSerivce extends WakeLockService {
	private BroadcastReceiver mBatInfoReveiver = null;
	private boolean bGotBatteryInfo = false;

	public TerminalInfoUploadSerivce() {
		super("TerminalInfoUploadSerivce");
	}
	private int temperature;
	private int intHealth = 0;
	private int intLevel = 0;
	private int intScale = 0;

	@Override
	public void onDestroy() {
		if (mBatInfoReveiver != null) {
			unregisterReceiver(mBatInfoReveiver);
		}
		super.onDestroy();
	}

	@Override
	protected void onHandleIntent(Intent intent) {
		if ("0".equals(Constants.UPLOAD_MODE)) {
			bGotBatteryInfo = false;
			//注册电池电量广播
			mBatInfoReveiver = new BroadcastReceiver() {
				@Override
				public void onReceive(Context context, Intent intent) {
					// TODO Auto-generated method stub
					String action = intent.getAction();
					// 如果捕捉到action是ACRION_BATTERY_CHANGED
					// 就运行onBatteryInfoReveiver()
					if (intent.ACTION_BATTERY_CHANGED.equals(action)) {
						Constants.BAT_TEMP = intent.getIntExtra(BatteryManager.EXTRA_TEMPERATURE,0);
						Constants.BAT_LEVEL = intent.getIntExtra(BatteryManager.EXTRA_LEVEL, 0);
						Constants.BAT_HEALTH = intent.getIntExtra(BatteryManager.EXTRA_HEALTH,0);
						int intScale = intent.getIntExtra(BatteryManager.EXTRA_SCALE, 100);
						int status = intent.getIntExtra(BatteryManager.EXTRA_STATUS, -1);
						Constants.IS_BATTERY_LOW = Constants.BAT_LEVEL <= 15;
						Constants.IS_OUT_OF_BATTERY = Constants.BAT_LEVEL <= 1;
						Constants.IS_BATTERY_CHARGING = (status==BatteryManager.BATTERY_STATUS_CHARGING || status==BatteryManager.BATTERY_STATUS_FULL);
						int chargePlug = intent.getIntExtra(BatteryManager.EXTRA_PLUGGED, -1);
						boolean usbCharge = (chargePlug == BatteryManager.BATTERY_PLUGGED_USB);
						boolean acCharge = (chargePlug == BatteryManager.BATTERY_PLUGGED_AC);
						BBLog.i(BBLog.TAG, "level=" + Constants.BAT_LEVEL + ", scale=" + intScale + ", isCharging=" + Constants.IS_BATTERY_CHARGING + ", usbCharge=" + usbCharge + ", acCharge=" + acCharge + ", temperature="+Constants.BAT_TEMP);
						bGotBatteryInfo = true;
						unregisterReceiver(mBatInfoReveiver);
						mBatInfoReveiver = null;
					}
				}
			};
			registerReceiver(mBatInfoReveiver, new IntentFilter(Intent.ACTION_BATTERY_CHANGED),  RequestPermission.REQUEST_PERMISSION_MY_BROADCAST, null);
			while (true) {
				if (bGotBatteryInfo) {
					uploadInfo(null, Constants.BAT_LEVEL, Constants.BAT_TEMP, Constants.BAT_HEALTH, Constants.IS_BATTERY_CHARGING, Constants.IS_BATTERY_LOW, false, Constants.IS_OUT_OF_BATTERY);
					break;
				} else {
					try {
						Thread.sleep(50);
					} catch (InterruptedException e) {
						e.printStackTrace();
					}
				}
			}
		} else {
			uploadInfo(null, intLevel, temperature, intHealth, Constants.IS_BATTERY_CHARGING, Constants.IS_BATTERY_LOW, false, Constants.IS_OUT_OF_BATTERY);
		}
	}

	private void uploadInfo(List<UsageStats> UsageList, int intLevel,int temperature, int intHealth, boolean isCharging, boolean isLowBattery, boolean powerOff, boolean outOfBattery) {
		JSONObject sendRequest = ParameterFactory.createTerInfoUploadParam(TerminalInfoUploadSerivce.this,UsageList,intLevel,temperature,intHealth,isCharging,isLowBattery,powerOff,outOfBattery);
		final JSONObject responseJson = HttpUtils.request(WSCallType.TER_INFO_UPLOAD, "", DeviceInfoApi.getIntance().getSerialNumber(),sendRequest);
		BBLog.d(Constants.TAG, "WebSocket: C0109上送本次requestId: "+responseJson.optString(ParameterName.request_id));
		WebSocketCenter.sendMessage(responseJson, true);
	}
}
