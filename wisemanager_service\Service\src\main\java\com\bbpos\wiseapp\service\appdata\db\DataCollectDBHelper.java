package com.bbpos.wiseapp.service.appdata.db;

import java.util.ArrayList;
import java.util.List;

import com.bbpos.wiseapp.logger.BBLog;
import com.bbpos.wiseapp.service.appdata.model.UsageData;
import android.content.ContentValues;
import android.content.Context;
import android.database.Cursor;
import android.database.sqlite.SQLiteDatabase;
import android.database.sqlite.SQLiteOpenHelper;
import android.util.Log;

public class DataCollectDBHelper extends SQLiteOpenHelper{
	private final static String TAG = DataCollectDBHelper.class.getName();
	private final static String DATABASE_NAME = "data_collect.db";
	private final static int DATABASE_VERSION = 1;
	private final static String USAGE_TABLE_NAME = "usage";

	private static DataCollectDBHelper mInstance = null;

	public synchronized static DataCollectDBHelper getInstance(Context context) {
		if (mInstance == null) {
			mInstance = new DataCollectDBHelper(context);
		}
		return mInstance;
	};

	public DataCollectDBHelper(Context context) {
		super(context, DATABASE_NAME, null, DATABASE_VERSION);
	}

	@Override
	public void onCreate(SQLiteDatabase db) {
		BBLog.v(BBLog.TAG, "create table if not exists");

		String sql = "CREATE TABLE IF NOT EXISTS " + USAGE_TABLE_NAME + " ("
				+ UsageData.DATA_DATE + " TEXT, "
				+ UsageData.PKG_NAME+ " TEXT, "
				+ UsageData.VERSION_CODE+ " INT, "
				+ UsageData.VERSION_NAME + " TEXT, "				
				+ UsageData.LAUNCK_COUNT + " INT, "				
				+ UsageData.TOTAL_TIME_IN_FOREGROUND + " INT"+")";
		db.execSQL(sql);
	}

	@Override
	public void onUpgrade(SQLiteDatabase db, int oldVersion, int newVersion) {
		String	sql = "DROP TABLE IF EXISTS " + USAGE_TABLE_NAME;
		db.execSQL(sql);
		onCreate(db);
	}

	/**
	 * 更新使用状态 若未存在 则新增
	 * @param date 
	 * @param pkgName
	 * @param launchCount 使用次数
	 * @param totalTimeInForeground 使用时长 单位毫秒
	 */
	public void updateUsageStat(String date, String pkgName,int versionCode,String versionName,
			int launchCount, long totalTimeInForeground) {
		SQLiteDatabase db = this.getReadableDatabase();
		String selectionStr = UsageData.DATA_DATE + "=? AND " +UsageData.PKG_NAME + "=? AND " + UsageData.VERSION_CODE + "=?";
		String selectionArgStr = date+","+pkgName+","+versionCode;
		BBLog.v(BBLog.TAG, "updateUsageStat selectionArgStr:"+selectionArgStr);
		String[] selectionArgs = selectionArgStr.split(",");
		Cursor cursor = db.query(USAGE_TABLE_NAME, null, selectionStr, selectionArgs, null, null, null);		
		
		if (cursor.getCount() >0) {
			//如果不是唯一则删除并新增
			if(!isCursorSingle(cursor)) {
				BBLog.e(BBLog.TAG, "updateUsageStat() -usage data must be single");
				db.delete(USAGE_TABLE_NAME, selectionStr, selectionArgs);
				//删除错误数据 新增
				addUsageData(new UsageData(date, pkgName, versionCode,versionName,launchCount,totalTimeInForeground));
				return;
			}
			//原数据存在 进行更新
			UsageData dataModel = null;
			while (cursor.moveToNext()) {
				dataModel = new UsageData(cursor);
			}
			
			ContentValues cv = new ContentValues();
			cv.put(UsageData.LAUNCK_COUNT, launchCount+dataModel.launchCount);
			cv.put(UsageData.TOTAL_TIME_IN_FOREGROUND, totalTimeInForeground+dataModel.totalTimeInForeground);
			db.update(USAGE_TABLE_NAME, cv, selectionStr, selectionArgs);
		} else {
			//原数据不存在 直接新增
			addUsageData(new UsageData(date, pkgName, versionCode,versionName,launchCount,totalTimeInForeground));
		}
	}

	/**新增使用状态统计数据*/
	public long addUsageData(UsageData dataModel) {
		if (dataModel == null) {
			return -1;
		}
		SQLiteDatabase db = this.getWritableDatabase();
		
		ContentValues cv = new ContentValues();
		cv.put(UsageData.DATA_DATE, dataModel.dataDate);
		cv.put(UsageData.PKG_NAME, dataModel.pkgName);
		cv.put(UsageData.VERSION_CODE, dataModel.versionCode);
		cv.put(UsageData.VERSION_NAME, dataModel.versionName);
		cv.put(UsageData.LAUNCK_COUNT, dataModel.launchCount);
		cv.put(UsageData.TOTAL_TIME_IN_FOREGROUND, dataModel.totalTimeInForeground);
		return db.insert(USAGE_TABLE_NAME, null, cv);
	}

	public List<UsageData> getUsageDataList(String dateStr) {
//		BBLog.v(BBLog.TAG, "getUsageDataList...");
		SQLiteDatabase db = this.getReadableDatabase();
		List<UsageData> usageList = new ArrayList<UsageData>();
		String selectionStr = UsageData.DATA_DATE + "="+dateStr;
		
		Cursor cursor = db.query(USAGE_TABLE_NAME, null, selectionStr, null, null, null, null);
		if(cursor.getCount()>0) {
			while (cursor.moveToNext()) {
				usageList.add(new UsageData(cursor));
			}
		}
		cursor.close();
		return usageList;
	}
	
	public boolean isCursorSingle(Cursor cursor) {
		if(cursor.getCount() == 1) {
			return true;
		}

		return false;
	}
}
