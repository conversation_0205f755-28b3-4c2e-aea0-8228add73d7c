package com.dspread.mdm.service.platform.manager

import android.app.ActivityManager
import android.content.Context
import android.content.Intent

import com.dspread.mdm.service.services.SmartMdmBackgroundService
import com.dspread.mdm.service.utils.log.Logger

object ServiceManager {

    /**
     * 启动后台服务
     */
    fun startBackgroundService(context: Context): Bo<PERSON>an {
        return try {
            Logger.com("ServiceManager: 准备启动后台服务")

            val serviceIntent = Intent(context, SmartMdmBackgroundService::class.java)
            val componentName = context.startService(serviceIntent)

            if (componentName != null) {
                Logger.com("ServiceManager: 后台服务启动成功，ComponentName: $componentName")
                true
            } else {
                Logger.comE("ServiceManager: 后台服务启动失败，ComponentName为null")
                false
            }

        } catch (e: Exception) {
            Logger.comE("ServiceManager: 启动后台服务异常", e)
            false
        }
    }

    /**
     * 停止后台服务
     */
    fun stopBackgroundService(context: Context) {
        try {
            Logger.com("ServiceManager: 准备停止后台服务")

            val serviceIntent = Intent(context, SmartMdmBackgroundService::class.java)
            context.stopService(serviceIntent)

            Logger.com("ServiceManager: 后台服务停止请求已发送")

        } catch (e: Exception) {
            Logger.comE("ServiceManager: 停止后台服务失败", e)
        }
    }

    /**
     * 重启后台服务
     */
    fun restartBackgroundService(context: Context): Boolean {
        return try {
            Logger.com("ServiceManager: 重启后台服务")
            stopBackgroundService(context)

            // 稍微延迟后启动，确保服务完全停止
            Thread.sleep(2000)
            startBackgroundService(context)

        } catch (e: Exception) {
            Logger.comE("ServiceManager: 重启后台服务失败", e)
            false
        }
    }

    /**
     * 检查后台服务是否正在运行
     * 用于强制停止后的服务状态检查
     */
    fun isBackgroundServiceRunning(context: Context): Boolean {
        return try {
            val activityManager = context.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
            val runningServices = activityManager.getRunningServices(Integer.MAX_VALUE)

            for (serviceInfo in runningServices) {
                if (SmartMdmBackgroundService::class.java.name == serviceInfo.service.className) {
                    return true
                }
            }
            false

        } catch (e: Exception) {
            Logger.comE("ServiceManager: 检查服务状态失败", e)
            false
        }
    }

}
