<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tool="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="320dp"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <View
                android:layout_width="match_parent"
                android:layout_height="35dp"
                android:alpha="0" />

            <View
                android:layout_width="match_parent"
                android:layout_height="35dp"
                android:background="@color/white" />
        </LinearLayout>

        <ImageView
            android:id="@+id/iv_image"
            android:layout_width="70dp"
            android:layout_height="70dp"
            android:layout_gravity="center"
            android:background="@drawable/info"
            android:contentDescription="TODO"
            android:scaleType="center" />
    </FrameLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:gravity="center"
        android:orientation="vertical">

        <TextView
            android:id="@+id/tv_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:padding="5dp"
            android:text="System Info Verfiy Result"
            android:textColor="@color/title"
            android:textSize="25sp" />


        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tv_model"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentLeft="true"
                tools:text="MODEL : "
                android:textColor="@color/black"
                android:textSize="14sp" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:orientation="vertical"
            android:paddingLeft="@dimen/dp_20"
            android:paddingRight="@dimen/dp_20">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_weight="4"
                    android:layout_gravity="center_vertical"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentLeft="true"
                        android:maxLines="3"
                        android:text="ROM INFO: "
                        android:textColor="@color/lightblack"
                        android:textSize="14sp" />

                    <TextView
                        android:id="@+id/tv_rom_version"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentLeft="true"
                        android:layout_weight="4"
                        android:maxLines="4"
                        android:textColor="@color/lightblack"
                        android:textSize="12sp"
                        tools:text="detail : " />
                </LinearLayout>
            </LinearLayout>

            <TextView
                android:id="@+id/tv_rom_version_result"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:gravity="right|bottom"
                android:textSize="14sp"
                android:layout_marginRight="15dp"
                tools:text="PASS" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical">

                <TextView
                    android:id="@+id/tv_imie"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentLeft="true"
                    android:layout_weight="4"
                    tools:text="IMEI : "
                    android:textColor="@color/lightblack"
                    android:textSize="14sp" />

                <TextView
                    android:id="@+id/tv_imie_result"
                    android:layout_width="60dp"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:textColor="@color/gray"
                    android:textSize="14sp"
                    tools:text="PASS" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical">

                <TextView
                    android:id="@+id/tv_gms_serial"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_weight="4"
                    android:text="CALIBRATION DATA"
                    android:textColor="@color/lightblack"
                    android:textSize="13sp" />

                <TextView
                    android:id="@+id/tv_gms_serial_result"
                    android:layout_width="60dp"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:textColor="@color/gray"
                    android:textSize="14sp"
                    tools:text="PASS" />
            </LinearLayout>


            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical">

                <TextView
                    android:id="@+id/tv_sn"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_weight="4"
                    tools:text="SN : "
                    android:textColor="@color/lightblack"
                    android:textSize="14sp" />

                <TextView
                    android:id="@+id/tv_sn_result"
                    android:layout_width="60dp"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:textColor="@color/gray"
                    android:textSize="14sp"
                    tools:text="PASS" />
            </LinearLayout>

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_margin="@dimen/dp_5"
                android:text="@string/system_info_verify_tip"
                android:textColor="@color/red_light"
                android:visibility="gone"/>


            <TextView
                android:id="@+id/tv_result"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginBottom="@dimen/dp_20"
                android:gravity="center"
                android:paddingLeft="@dimen/dp_20"
                android:paddingTop="@dimen/dp_5"
                android:paddingRight="@dimen/dp_20"
                android:text="@string/system_info_verify_info"
                android:textColor="@color/red"
                />
        </LinearLayout>
    </LinearLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="@color/divider_ex" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:gravity="center_vertical|right"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/tv_cancel"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:padding="15dp"
            android:text="CANCEL"
            android:textColor="@color/theme_green"
            android:textSize="16sp"
            android:visibility="gone" />

        <View
            android:layout_width="1dp"
            android:layout_height="match_parent"
            android:background="@color/divider_ex"
            android:visibility="gone" />

        <TextView
            android:id="@+id/tv_sure"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:padding="15dp"
            android:text="OK"
            android:textColor="@color/theme_green"
            android:textSize="16sp" />
    </LinearLayout>

</LinearLayout>