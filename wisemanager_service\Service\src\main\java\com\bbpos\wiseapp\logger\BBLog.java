package com.bbpos.wiseapp.logger;

import android.text.TextUtils;
import android.util.Log;

import com.orhanobut.logger.Logger;
import com.socks.library.KLog;

public class BBLog {
    public static final String ROOT_TAG = "WA2.0";

    public static final String TAG = "WiseApp2.0";

    private static boolean isUseLogger = true;          //如果isUseLogger为true，使用Logger输出日志信息。如果为false，使用Log输出日志信息。
    private static int curLogLevel = LogLevel.FULL;     // 设置日志的级别
    public static boolean gLogCatOutput = false;      //全局的控制台输出log的开关

    public static boolean isgLogCatOutput() {
        return gLogCatOutput;
    }

    public static void setgLogCatOutput(boolean gLogCatOutput) {
        BBLog.gLogCatOutput = gLogCatOutput;
    }

    public static int getCurLogLevel() {
        return curLogLevel;
    }

    public static void setCurLogLevel(int curLogLevel) {
        BBLog.curLogLevel = curLogLevel;
    }

    /**
     * 设置日志输出方向
     * @param isLogcatOutput 输出到控制台
     * @param isWriteFile 输出到文件
     */
    public static void setLogOutput(boolean isLogcatOutput, boolean isWriteFile){
        setgLogCatOutput(isLogcatOutput);

        if(isWriteFile)
            LoggerConfig.initLogger(false, isWriteFile);
        else{
            Log.i(BBLog.TAG, "will write to console, but will not write file");   //目前默认的打印log不支持输出到文件
        }

    }

    public static void deleteOldLogFiles(){
        try {
            new Thread(){
                @Override
                public void run() {
                    super.run();
                    LoggerConfig.deleteOldLogFiles();    //删除旧的运行日志文件
                }
            };
        }catch (Exception e){
            //FIXME:一般只可能是权限不对，即不让读写sdcard文件.在别的地方提示用户去开启权限,否则正常功能无法使用(存取日志)
        }
    }

    /**
     * 把内存中的log冲到文件
     */
    public static void flushMemoryLogToFileAsync(){
        if(isUseLogger){
            LoggerConfig.flushLoggerMemoryLogToFile();
        }
    }

    public static void v(String tag, String msg) {
        if (curLogLevel > LogLevel.VERBOSE) {
            return;
        }
        if (isUseLogger) {
            Logger.v(msg);//Logger会获取log的位置，不用再写tag
            if (isgLogCatOutput()) {
                Log.v(BBLog.ROOT_TAG+"_"+tag, msg);
            }
        } else {
            Log.v(BBLog.ROOT_TAG+"_"+tag, msg);
        }
    }

    public static void d(String tag, String msg) {
        if (curLogLevel > LogLevel.DEBUG) {
            return;
        }
        if (isUseLogger) {
            Logger.d(msg);
            if (isgLogCatOutput()) {
                Log.d(BBLog.ROOT_TAG+"_"+tag, msg);
            }
        } else {
            Log.d(BBLog.ROOT_TAG+"_"+tag, msg);
        }
    }

    public static void i(String tag, String msg) {
        if (curLogLevel > LogLevel.INFO) {
            return;
        }
        if (isUseLogger) {
            Logger.i(msg);
            if (isgLogCatOutput()) {
                Log.i(BBLog.ROOT_TAG+"_"+tag, msg);
            }
        } else {
            Log.i(BBLog.ROOT_TAG+"_"+tag, msg);
        }
    }

    public static void w(String tag, String msg) {
        if (curLogLevel > LogLevel.WARN) {
            return;
        }
        if (isUseLogger) {
            Logger.w(msg);
            if (isgLogCatOutput()) {
                Log.w(BBLog.ROOT_TAG+"_"+tag, msg);
            }
        } else {
            Log.w(BBLog.ROOT_TAG+"_"+tag, msg);
        }

    }

    public static void e(String tag, String msg) {
        if (curLogLevel > LogLevel.ERROR) {
            return;
        }
        if (isUseLogger) {
            Logger.e(msg);
            if (isgLogCatOutput()) {
                Log.e(BBLog.ROOT_TAG+"_"+tag, msg);
            }
        } else {
            Log.e(BBLog.ROOT_TAG+"_"+tag, msg);
        }

    }

    public static void json(String json) {
        if (curLogLevel >= LogLevel.NONE) {
            return;
        }
        if (isUseLogger) {
            Logger.json(json);
        } else {
            KLog.json(json);
        }
    }

    public static void xml(String xml) {
        if (curLogLevel >= LogLevel.NONE) {
            return;
        }
        if (isUseLogger) {
            Logger.xml(xml);
        } else {
            KLog.xml(xml);
        }
    }

    public static void e(Throwable throwable, String tag, Object... args) {
        if (curLogLevel > LogLevel.ERROR) {
            return;
        }
        if (isUseLogger) {
            Logger.e(throwable, tag, args);
        } else {
            KLog.e(tag, throwable, args);
        }
    }

    public static void d(String object) {
        if (curLogLevel > LogLevel.DEBUG) {
            return;
        }
        if (isUseLogger) {
            Logger.d(object);
        } else {
            Log.d(headString(object), object);
        }
    }

    public static void e(String message, Object... args) {
        if (curLogLevel > LogLevel.ERROR) {
            return;
        }
        if (isUseLogger) {
            Logger.e(message + args);
        } else {
            KLog.e(message, "", args);
        }
    }

    public static void v(String message, Object... args) {
        if (curLogLevel > LogLevel.VERBOSE) {
            return;
        }
        if (isUseLogger) {
            Logger.v(message + args);
        } else {
            KLog.v(message, "", args);
        }
    }

    public static void w(String message, Object... args) {
        if (curLogLevel > LogLevel.WARN) {
            return;
        }
        if (isUseLogger) {
            Logger.w(message + args);
        } else {
            KLog.w(message, "", args);
        }
    }

    public static boolean isUseLogger() {
        return isUseLogger;
    }

    public static void setIsUseLogger(boolean isUseLogger) {
        BBLog.isUseLogger = isUseLogger;
    }

    private static String headString(String tagStr, Object... objects) {
        String[] contents = wrapperContent(tagStr, new Object[]{objects});
        String tag = contents[0];
        String msg = contents[1];
        String headString = contents[2];
        return headString;
    }

    /***
     * 获取log的文件位置
     * @param tagStr
     * @param objects
     * @return
     */
    private static String[] wrapperContent(String tagStr, Object... objects) {
        StackTraceElement[] stackTrace = Thread.currentThread().getStackTrace();
        StackTraceElement targetElement = stackTrace[5];
        String className = targetElement.getClassName();
        String[] classNameInfo = className.split("\\.");
        if (classNameInfo.length > 0) {
            className = classNameInfo[classNameInfo.length - 1] + ".java";
        }

        String methodName = targetElement.getMethodName();
        int lineNumber = targetElement.getLineNumber();
        if (lineNumber < 0) {
            lineNumber = 0;
        }

        String methodNameShort = methodName.substring(0, 1).toUpperCase() + methodName.substring(1);
        String tag = tagStr == null ? className : tagStr;
        if (TextUtils.isEmpty(tag)) {
            tag = "HiLog";
        }

        String msg = objects == null ? "Log with null object" : getObjectsString(objects);
        String headString = "[ (" + className + ":" + lineNumber + ")#" + methodNameShort + " ] ";
        return new String[]{tag, msg, headString};
    }

    /***
     * 获取log的位置的tag
     * @param objects
     * @return
     */
    private static String getObjectsString(Object... objects) {
        if (objects.length > 1) {
            StringBuilder var4 = new StringBuilder();
            var4.append("\n");

            for (int i = 0; i < objects.length; ++i) {
                Object object1 = objects[i];
                if (object1 == null) {
                    var4.append("Param").append("[").append(i).append("]").append(" = ").append("null").append("\n");
                } else {
                    var4.append("Param").append("[").append(i).append("]").append(" = ").append(object1.toString()).append("\n");
                }
            }

            return var4.toString();
        } else {
            Object object = objects[0];
            return object == null ? "null" : object.toString();
        }
    }
}
