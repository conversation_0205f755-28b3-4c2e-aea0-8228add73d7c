package com.dspread.mdm.service.modules.logstream

import com.dspread.mdm.service.utils.log.Logger
import java.io.File
import java.io.FileInputStream
import java.io.FileOutputStream
import java.util.zip.GZIPOutputStream

/**
 * 日志压缩器
 * 负责将日志文件压缩为GZ格式
 */
class LogCompressor {
    
    companion object {
        private const val TAG = "[LogCompressor]"
        private const val BUFFER_SIZE = 8192 // 8KB
    }
    
    /**
     * 压缩日志文件
     * @param sourceFile 源文件
     * @param targetFile 目标文件
     * @return 压缩结果
     */
    fun compressFile(sourceFile: File, targetFile: File): Boolean {
        return try {
            Logger.logStream("$TAG 开始压缩文件: ${sourceFile.name} -> ${targetFile.name}")
            
            // 确保目标目录存在
            targetFile.parentFile?.mkdirs()
            
            // 创建输入输出流
            FileInputStream(sourceFile).use { inputStream ->
                GZIPOutputStream(FileOutputStream(targetFile)).use { outputStream ->
                    val buffer = ByteArray(BUFFER_SIZE)
                    var length: Int
                    
                    // 读取源文件并写入压缩文件
                    while (inputStream.read(buffer).also { length = it } > 0) {
                        outputStream.write(buffer, 0, length)
                    }
                    
                    // 刷新并关闭流
                    outputStream.finish()
                    outputStream.flush()
                }
            }
            
            Logger.logStream("$TAG 文件压缩成功: ${sourceFile.name} (${sourceFile.length()} bytes) -> ${targetFile.name} (${targetFile.length()} bytes)")
            true
        } catch (e: Exception) {
            Logger.logStreamE("$TAG 文件压缩失败: ${sourceFile.name}", e)
            false
        }
    }
    
    /**
     * 批量压缩日志文件
     * @param sourceFiles 源文件列表
     * @param targetDir 目标目录
     * @return 成功压缩的文件数量
     */
    fun compressFiles(sourceFiles: List<File>, targetDir: File): Int {
        var successCount = 0
        
        try {
            Logger.logStream("$TAG 开始批量压缩文件，数量: ${sourceFiles.size}")
            
            // 确保目标目录存在
            targetDir.mkdirs()
            
            // 遍历源文件列表
            sourceFiles.forEach { sourceFile ->
                // 创建目标文件
                val targetFile = File(targetDir, "${sourceFile.nameWithoutExtension}.gz")
                
                // 压缩文件
                if (compressFile(sourceFile, targetFile)) {
                    successCount++
                }
            }
            
            Logger.logStream("$TAG 批量压缩完成，成功: $successCount/${sourceFiles.size}")
        } catch (e: Exception) {
            Logger.logStreamE("$TAG 批量压缩失败", e)
        }
        
        return successCount
    }
    
    /**
     * 压缩并删除源文件
     * @param sourceFile 源文件
     * @param targetFile 目标文件
     * @return 压缩结果
     */
    fun compressAndDeleteFile(sourceFile: File, targetFile: File): Boolean {
        return try {
            // 压缩文件
            val result = compressFile(sourceFile, targetFile)
            
            // 如果压缩成功，删除源文件
            if (result) {
                if (sourceFile.delete()) {
                    Logger.logStream("$TAG 源文件已删除: ${sourceFile.name}")
                } else {
                    Logger.logStreamW("$TAG 源文件删除失败: ${sourceFile.name}")
                }
            }
            
            result
        } catch (e: Exception) {
            Logger.logStreamE("$TAG 压缩并删除源文件失败: ${sourceFile.name}", e)
            false
        }
    }
    
    /**
     * 批量压缩并删除源文件
     * @param sourceFiles 源文件列表
     * @param targetDir 目标目录
     * @return 成功压缩的文件数量
     */
    fun compressAndDeleteFiles(sourceFiles: List<File>, targetDir: File): Int {
        var successCount = 0
        
        try {
            Logger.logStream("$TAG 开始批量压缩并删除文件，数量: ${sourceFiles.size}")
            
            // 确保目标目录存在
            targetDir.mkdirs()
            
            // 遍历源文件列表
            sourceFiles.forEach { sourceFile ->
                // 创建目标文件
                val targetFile = File(targetDir, "${sourceFile.nameWithoutExtension}.gz")
                
                // 压缩并删除文件
                if (compressAndDeleteFile(sourceFile, targetFile)) {
                    successCount++
                }
            }
            
            Logger.logStream("$TAG 批量压缩并删除完成，成功: $successCount/${sourceFiles.size}")
        } catch (e: Exception) {
            Logger.logStreamE("$TAG 批量压缩并删除失败", e)
        }
        
        return successCount
    }
}
