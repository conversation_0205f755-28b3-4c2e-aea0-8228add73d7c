package com.dspread.mdm.service.platform.api.app

import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.content.pm.ApplicationInfo
import android.content.pm.PackageInstaller
import android.content.pm.PackageManager
import com.dspread.mdm.service.utils.log.Logger
import com.dspread.mdm.service.platform.api.model.AppInfo
import com.dspread.mdm.service.platform.api.model.SystemOperationResult
import java.io.File

/**
 * 应用管理API
 * 提供应用安装、卸载、信息获取等功能
 *
 * API使用建议：
 * - 对于RuleBase等长期任务，推荐使用 xxxAsync 版本，完全依赖系统广播
 * - 对于TaskHandler等需要立即响应的场景，可使用带回调的版本
 * - 系统广播 (ACTION_PACKAGE_ADDED/REMOVED) 是最可靠的状态通知机制
 *
 * 使用单例模式，避免重复实例化和资源浪费
 */
class AppManagerApi(private val context: Context) {

    companion object {
        private const val TAG = "AppManagerApi"
        
        private var sessionCallback: PackageInstaller.SessionCallback? = null
        private var bRegister = false
        private val callbackMap = mutableMapOf<Int, ((String, Int, String) -> Unit)?>()
        private val packageNameMap = mutableMapOf<Int, String>()

        @Volatile
        private var INSTANCE: AppManagerApi? = null

        /**
         * 获取单例实例
         */
        private fun getInstance(context: Context): AppManagerApi {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: AppManagerApi(context.applicationContext).also {
                    INSTANCE = it
                    Logger.platform("AppManagerApi 单例实例已创建")
                }
            }
        }

        // ==================== 静态方法接口 ====================
        // 暂时保持实例调用方式，避免复杂的静态方法问题
        // 可以在需要时逐步添加静态便捷方法
    }
    
    private val packageManager = context.packageManager

    // ==================== 公共接口方法 ====================

    /**
     * 静默安装APK（简化版本，主要依赖系统广播通知结果）
     * @param apkPath APK文件路径
     * @param callback 可选的回调，主要用于兼容性，建议依赖系统广播
     * @return SystemOperationResult 操作结果
     */
    fun installApkSilently(apkPath: String, callback: ((String, Int, String) -> Unit)? = null): SystemOperationResult {
        return try {


            val success = installApkViaInstaller(apkPath, null, callback)
            if (success) {
                SystemOperationResult.success("Installation initiated via PackageInstaller")
            } else {
                SystemOperationResult.failure("Failed to initiate installation")
            }
        } catch (e: Exception) {
            Logger.appMgrE("Failed to install APK: $apkPath", e)
            callback?.invoke(getPackageNameFromApk(apkPath), 0, e.message ?: "Unknown error")
            SystemOperationResult.failure("Installation failed: ${e.message}")
        }
    }

    /**
     * 静默安装APK（无回调版本，完全依赖系统广播）
     * 推荐使用此版本，更简洁且可靠
     */
    fun installApkSilentlyAsync(apkPath: String): SystemOperationResult {
        return installApkSilently(apkPath, null)
    }

    /**
     * 静默卸载应用（简化版本，主要依赖系统广播通知结果）
     * @param packageName 包名
     * @param callback 可选的回调，主要用于兼容性，建议依赖系统广播
     * @return SystemOperationResult 操作结果
     */
    fun uninstallAppSilently(packageName: String, callback: ((String, Int, String) -> Unit)? = null): SystemOperationResult {
        return try {
            Logger.appMgrI("Uninstalling app via PackageInstaller: $packageName")

            val success = uninstallAppViaInstaller(packageName, callback)
            if (success) {
                SystemOperationResult.success("Uninstallation initiated via PackageInstaller")
            } else {
                SystemOperationResult.failure("Failed to initiate uninstallation")
            }
        } catch (e: Exception) {
            Logger.appMgrE("Failed to uninstall app: $packageName", e)
            callback?.invoke(packageName, 0, e.message ?: "Unknown error")
            SystemOperationResult.failure("Uninstallation failed: ${e.message}")
        }
    }

    /**
     * 静默卸载应用（无回调版本，完全依赖系统广播）
     * 推荐使用此版本，更简洁且可靠
     */
    fun uninstallAppSilentlyAsync(packageName: String): SystemOperationResult {
        return uninstallAppSilently(packageName, null)
    }

    /**
     * 获取已安装应用列表
     */
    fun getInstalledApplications(includeSystemApps: Boolean = false): List<AppInfo> {
        return try {
            // Logger.appMgrI("开始获取已安装应用列表，includeSystemApps=$includeSystemApps")
            
            val packages = packageManager.getInstalledPackages(0)
            // Logger.appMgrI("系统返回 ${packages.size} 个已安装包")

            var systemAppCount = 0
            var userAppCount = 0
            var processedCount = 0
            var errorCount = 0
            var includedCount = 0

            // 定义特殊的系统应用包名
            val specialSystemApps = setOf(
                "com.dspread.mdm.service",  // 对应SERVICE_PACKAGE_NAME
            )

            val result = packages.mapNotNull { packageInfo ->
                try {
                    processedCount++
                    val appInfo = packageInfo.applicationInfo
                    val isSystemApp = (appInfo.flags and ApplicationInfo.FLAG_SYSTEM) != 0 ||
                                    (appInfo.flags and ApplicationInfo.FLAG_UPDATED_SYSTEM_APP) != 0

                    if (isSystemApp) {
                        systemAppCount++
                    } else {
                        userAppCount++
                    }
                    
                    if (isSystemApp && !specialSystemApps.contains(packageInfo.packageName)) {
                        return@mapNotNull null
                    }

                    val appName = packageManager.getApplicationLabel(appInfo).toString()
                    includedCount++



                    AppInfo(
                        packageName = packageInfo.packageName,
                        appName = appName,
                        versionName = packageInfo.versionName ?: "",
                        versionCode = packageInfo.longVersionCode,
                        installPath = appInfo.sourceDir,
                        isSystemApp = isSystemApp,
                        isEnabled = appInfo.enabled,
                        firstInstallTime = packageInfo.firstInstallTime,
                        lastUpdateTime = packageInfo.lastUpdateTime
                    )
                } catch (e: Exception) {
                    errorCount++
                    Logger.appMgrW("获取应用信息失败: ${packageInfo.packageName}: ${e.message}")
                    null
                }
            }


            Logger.appMgrI("应用信息获取完成: 总数${packages.size}(系统${systemAppCount}/用户${userAppCount}) 返回${result.size}个")

            result
        } catch (e: Exception) {
            Logger.appMgrE("获取已安装应用列表失败", e)
            emptyList()
        }
    }
    
    /**
     * 获取应用信息
     */
    fun getApplicationInfo(packageName: String): AppInfo? {
        return try {
            val appInfo = packageManager.getApplicationInfo(packageName, PackageManager.GET_META_DATA)
            val packageInfo = packageManager.getPackageInfo(packageName, 0)
            
            AppInfo(
                packageName = packageName,
                appName = packageManager.getApplicationLabel(appInfo).toString(),
                versionName = packageInfo.versionName ?: "",
                versionCode = packageInfo.longVersionCode,
                installPath = appInfo.sourceDir,
                isSystemApp = (appInfo.flags and ApplicationInfo.FLAG_SYSTEM) != 0,
                isEnabled = appInfo.enabled,
                firstInstallTime = packageInfo.firstInstallTime,
                lastUpdateTime = packageInfo.lastUpdateTime
            )
        } catch (e: Exception) {
            Logger.appMgrW("Failed to get app info for: $packageName: ${e.message}")
            null
        }
    }
    
    /**
     * 检查应用是否已安装
     */
    fun isApplicationInstalled(packageName: String): Boolean {
        return try {
            packageManager.getApplicationInfo(packageName, 0)
            true
        } catch (e: PackageManager.NameNotFoundException) {
            false
        } catch (e: Exception) {
            Logger.appMgrW("Failed to check if app is installed: $packageName: ${e.message}")
            false
        }
    }
    
    /**
     * 检查API可用性
     */
    fun isAvailable(): Boolean {
        return try {
            packageManager != null
        } catch (e: Exception) {
            Logger.appMgrW("AppManagerApi not available: ${e.message}")
            false
        }
    }

    // 私有方法：从APK文件获取包名
    private fun getPackageNameFromApk(apkPath: String): String {
        return try {
            val packageInfo = packageManager.getPackageArchiveInfo(apkPath, PackageManager.GET_ACTIVITIES)
            packageInfo?.packageName ?: ""
        } catch (e: Exception) {
            Logger.appMgrW("Failed to get package name from APK: $apkPath: ${e.message}")
            ""
        }
    }

    /**
     * 静默卸载应用（底层PackageInstaller实现）
     */
    private fun uninstallAppViaInstaller(
        packageName: String,
        callback: ((String, Int, String) -> Unit)? = null
    ): Boolean {
        return try {
            Logger.appMgrI("Deleting package via PackageInstaller: $packageName")

            val intent = Intent()
            val sender = PendingIntent.getActivity(context, 0, intent, PendingIntent.FLAG_IMMUTABLE)
            val packageInstaller = packageManager.packageInstaller

            packageInstaller.uninstall(packageName, sender.intentSender)
            Logger.appMgrI("PackageInstaller uninstall initiated")

            // 注意：不再使用轮询机制，主要依赖系统广播 ACTION_PACKAGE_REMOVED
            // 如果提供了回调，启动简单的超时检查作为备用机制
            if (callback != null) {
                Thread {
                    Thread.sleep(5000) // 等待5秒

                    if (!isApplicationInstalled(packageName)) {
                        Logger.appMgrI("Package uninstalled (backup check): $packageName")
                        callback.invoke(packageName, 1, "")
                    } else {
                        Logger.appMgrW("Package still exists after 5s, may need more time: $packageName")
                        // 不调用失败回调，让系统广播处理
                    }
                }.start()
            }

            true
        } catch (e: Exception) {
            Logger.appMgrE("PackageInstaller deletion failed", e)
            callback?.invoke(packageName, 0, e.message ?: "Deletion failed")
            false
        }
    }

    /**
     * 静默安装APK（底层PackageInstaller实现）
     * @param apkPath APK文件路径
     * @param packageName 包名（可选，如果提供则不需要解析APK）
     * @param callback 安装结果回调
     */
    private fun installApkViaInstaller(
        apkPath: String,
        packageName: String? = null,
        callback: ((String, Int, String) -> Unit)? = null
    ): Boolean {
        return try {


            // 获取PackageInstaller
            val packageInstaller = packageManager.packageInstaller

            // 初始化SessionCallback
            if (sessionCallback == null) {
                Logger.appMgrI("创建SessionCallback")
                sessionCallback = object : PackageInstaller.SessionCallback() {
                    override fun onCreated(sessionId: Int) {
                    }

                    override fun onBadgingChanged(sessionId: Int) {
                    }

                    override fun onActiveChanged(sessionId: Int, active: Boolean) {
                    }

                    override fun onProgressChanged(sessionId: Int, progress: Float) {
                        if (progress >= 0.9f) {
                            Logger.appMgrI("安装进度: ${(progress * 100).toInt()}%")
                        }
                    }

                    override fun onFinished(sessionId: Int, success: Boolean) {


                        try {
                            val callback = callbackMap[sessionId]
                            val packageName = packageNameMap[sessionId]

                            if (callback != null && packageName != null) {
                                if (success) {
                                    Logger.appMgrI("安装成功: $packageName")
                                    callback.invoke(packageName, 1, "")
                                } else {
                                    Logger.appMgrE("安装失败: $packageName")

                                    val detailedError = getInstallFailureReason(sessionId, packageName)

                                    callback.invoke(packageName, 0, detailedError)
                                }

                                // 清理回调
                                callbackMap.remove(sessionId)
                                packageNameMap.remove(sessionId)
                            }
                        } catch (e: Exception) {
                            Logger.appMgrE("SessionCallback处理失败", e)
                        }
                    }
                }
            }

            // 注册SessionCallback
            if (!bRegister) {
                Logger.appMgrI("注册SessionCallback")
                packageInstaller.registerSessionCallback(sessionCallback!!, android.os.Handler(android.os.Looper.getMainLooper()))
                bRegister = true
            }

            // 获取目标包名
            val targetPackageName = packageName ?: getPackageNameFromApk(apkPath)
            if (targetPackageName.isEmpty()) {
                Logger.appMgrE("无法获取包名: $apkPath")
                callback?.invoke("", 0, "无法获取包名")
                return false
            }

            Logger.appMgrI("准备安装: $targetPackageName")

            // 验证APK文件
            val apkValidation = validateApkFile(apkPath, targetPackageName)
            if (!apkValidation.first) {
                Logger.appMgrE("APK文件验证失败: ${apkValidation.second}")
                callback?.invoke(targetPackageName, 0, "APK验证失败: ${apkValidation.second}")
                return false
            }

            val params = PackageInstaller.SessionParams(PackageInstaller.SessionParams.MODE_FULL_INSTALL)

            var session: PackageInstaller.Session? = null
            var outputStream: java.io.OutputStream? = null
            var inputStream: java.io.FileInputStream? = null

            try {
                // 创建Session
                val sessionId = packageInstaller.createSession(params)
                Logger.appMgrI("创建Session: sessionId=$sessionId")

                // 保存回调信息
                callbackMap[sessionId] = callback
                packageNameMap[sessionId] = targetPackageName

                val file = File(apkPath)
                val apkName = apkPath.substring(apkPath.lastIndexOf(File.separator) + 1, apkPath.lastIndexOf(".apk"))

                // 开启Session
                session = packageInstaller.openSession(sessionId)

                // 获取输出流，用于将apk写入session
                outputStream = session.openWrite(apkName, 0, -1)
                inputStream = java.io.FileInputStream(file)

                val buffer = ByteArray(4096)
                var n: Int

                // 读取apk文件写入session
                while (inputStream.read(buffer).also { n = it } > 0) {
                    outputStream.write(buffer, 0, n)
                }

                // 写完需要关闭流，否则会抛异常"files still open"
                inputStream.close()
                inputStream = null
                outputStream.flush()
                outputStream.close()
                outputStream = null

                // 配置安装完成后发起的intent
                val intent = Intent()
                val pendingIntent = PendingIntent.getActivity(context, 0, intent, PendingIntent.FLAG_IMMUTABLE)
                val intentSender = pendingIntent.intentSender

                // 提交启动安装
                session.commit(intentSender)
                Logger.appMgrI("PackageInstaller installation committed: sessionId=$sessionId")

            } catch (e: java.io.IOException) {
                Logger.appMgrE("安装过程IO异常", e)
                callback?.invoke(targetPackageName, 0, "IO异常: ${e.message}")
                return false
            } catch (e: RuntimeException) {
                Logger.appMgrE("安装过程运行时异常", e)
                session?.abandon()
                callback?.invoke(targetPackageName, 0, "运行时异常: ${e.message}")
                return false
            } finally {
                // 清理资源
                try {
                    inputStream?.close()
                } catch (e: Exception) {
                    Logger.appMgrE("关闭inputStream失败", e)
                }
                try {
                    outputStream?.close()
                } catch (e: Exception) {
                    Logger.appMgrE("关闭outputStream失败", e)
                }
            }

            // 安装已提交，等待SessionCallback回调结果

            true
        } catch (e: Exception) {
            Logger.appMgrE("PackageInstaller installation failed", e)
            callback?.invoke("", 0, e.message ?: "Installation failed")
            false
        }
    }

    /**
     * 获取安装失败的详细原因
     */
    private fun getInstallFailureReason(sessionId: Int, packageName: String): String {
        return try {
            val packageInstaller = packageManager.packageInstaller
            val sessionInfo = packageInstaller.getSessionInfo(sessionId)

            val reason = when {
                sessionInfo == null -> "Session信息为空"
                else -> {
                    // 检查常见的安装失败原因
                    val reasons = mutableListOf<String>()

                    // 检查存储空间
                    val apkDir = context.getExternalFilesDir(null)
                    val freeSpace = apkDir?.freeSpace ?: 0
                    if (freeSpace < 100 * 1024 * 1024) { // 小于100MB
                        reasons.add("存储空间不足: ${freeSpace / 1024 / 1024}MB")
                    }

                    // 检查应用是否已安装
                    if (isApplicationInstalled(packageName)) {
                        reasons.add("应用已安装，可能需要卸载旧版本")
                    }

                    // 检查权限
                    val hasInstallPermission = context.packageManager.canRequestPackageInstalls()
                    if (!hasInstallPermission) {
                        reasons.add("缺少安装权限")
                    }

                    if (reasons.isEmpty()) {
                        "未知安装失败原因 (sessionId: $sessionId)"
                    } else {
                        reasons.joinToString("; ")
                    }
                }
            }

            Logger.appMgrE("安装失败分析: $reason")
            reason

        } catch (e: Exception) {
            Logger.appMgrE("获取安装失败原因时出错", e)
            "获取失败原因时出错: ${e.message}"
        }
    }

    /**
     * 验证APK文件
     */
    private fun validateApkFile(apkPath: String, expectedPackageName: String): Pair<Boolean, String> {
        return try {
            val file = File(apkPath)

            // 检查文件是否存在
            if (!file.exists()) {
                return Pair(false, "APK文件不存在")
            }

            // 检查文件大小
            if (file.length() == 0L) {
                return Pair(false, "APK文件为空")
            }

            // 检查文件是否可读
            if (!file.canRead()) {
                return Pair(false, "APK文件无法读取")
            }

            // 尝试解析APK包信息
            val packageInfo = packageManager.getPackageArchiveInfo(apkPath, PackageManager.GET_ACTIVITIES)
            if (packageInfo == null) {
                return Pair(false, "无法解析APK文件，可能文件损坏")
            }

            // 检查包名是否匹配
            if (packageInfo.packageName != expectedPackageName) {
                return Pair(false, "包名不匹配: 期望=$expectedPackageName, 实际=${packageInfo.packageName}")
            }


            Logger.appMgrI("APK验证通过: ${packageInfo.packageName} v${packageInfo.versionName}(${packageInfo.versionCode}) ${file.length() / 1024}KB")

            Pair(true, "验证通过")

        } catch (e: Exception) {
            Logger.appMgrE("APK验证异常", e)
            Pair(false, "验证异常: ${e.message}")
        }
    }
}
