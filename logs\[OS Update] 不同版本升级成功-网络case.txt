--------- beginning of system
--------- beginning of crash
--------- beginning of main
2025-08-18 13:24:15.554   781-1418  BufferQueueDebug        surfaceflinger                       E  [ActivityRecord{53d556 u0 com.dspread.mdm.service/.ui.activity.TestActivity#359](this:0xa5ff2c40,id:-1,api:0,p:-1,c:-1) id info cannot be read from 'ActivityRecord{53d556 u0 com.dspread.mdm.service/.ui.activity.TestActivity#359'
2025-08-18 13:24:15.579   781-1418  BufferQueueDebug        surfaceflinger                       E  [2ba9c73 Splash Screen com.dspread.mdm.service#360](this:0xa5fecc40,id:-1,api:0,p:-1,c:-1) id info cannot be read from '2ba9c73 Splash Screen com.dspread.mdm.service#360'
2025-08-18 13:24:15.594   781-1418  BufferQueueDebug        surfaceflinger                       E  [Splash Screen com.dspread.mdm.service#361](this:0xa5fa1c40,id:-1,api:0,p:-1,c:-1) id info cannot be read from 'Splash Screen com.dspread.mdm.service#361'
2025-08-18 13:24:15.646   781-1017  BufferQueueDebug        surfaceflinger                       E  [186c371 ActivityRecordInputSink com.dspread.mdm.service/.ui.activity.TestActivity#364](this:0xa5f9cc40,id:-1,api:0,p:-1,c:-1) id info cannot be read from '186c371 ActivityRecordInputSink com.dspread.mdm.service/.ui.activity.TestActivity#364'
---------------------------- PROCESS STARTED (4254) for package com.dspread.mdm.service ----------------------------
2025-08-18 13:24:15.785  4254-4254  ziparchive              com.dspread.mdm.service              W  Unable to open '/data/app/~~nYvpNnWl1he0-QBaxuBOEA==/com.dspread.mdm.service-vTpuTL2ha4BdLD63K5LHDg==/base.dm': No such file or directory
2025-08-18 13:24:16.228  4254-4254  nativeloader            com.dspread.mdm.service              D  Configuring clns-4 for other apk /data/app/~~nYvpNnWl1he0-QBaxuBOEA==/com.dspread.mdm.service-vTpuTL2ha4BdLD63K5LHDg==/base.apk. target_sdk_version=34, uses_libraries=, library_path=/data/app/~~nYvpNnWl1he0-QBaxuBOEA==/com.dspread.mdm.service-vTpuTL2ha4BdLD63K5LHDg==/lib/arm, permitted_path=/data:/mnt/expand:/data/user/0/com.dspread.mdm.service
2025-08-18 13:24:16.256  4254-4254  GraphicsEnvironment     com.dspread.mdm.service              V  Currently set values for:
2025-08-18 13:24:16.256  4254-4254  GraphicsEnvironment     com.dspread.mdm.service              V    angle_gl_driver_selection_pkgs=[]
2025-08-18 13:24:16.256  4254-4254  GraphicsEnvironment     com.dspread.mdm.service              V    angle_gl_driver_selection_values=[]
2025-08-18 13:24:16.257  4254-4254  GraphicsEnvironment     com.dspread.mdm.service              V  ANGLE GameManagerService for com.dspread.mdm.service: false
2025-08-18 13:24:16.258  4254-4254  GraphicsEnvironment     com.dspread.mdm.service              V  com.dspread.mdm.service is not listed in per-application setting
2025-08-18 13:24:16.258  4254-4254  GraphicsEnvironment     com.dspread.mdm.service              V  Neither updatable production driver nor prerelease driver is supported.
2025-08-18 13:24:16.284  4254-4254  Compatibil...geReporter com.dspread.mdm.service              D  Compat change id reported: 183155436; UID 1000; state: ENABLED
2025-08-18 13:24:16.304  4254-4254  MbrainDebugManagerImpl  com.dspread.mdm.service              D  getService failed
2025-08-18 13:24:16.391  4254-4254  Common                  com.dspread.mdm.service              D  🔧 日志配置：生产环境模式
2025-08-18 13:24:16.401  4254-4254  Common                  com.dspread.mdm.service              D  🔧 [DebugConfig] 定时器调试模式已禁用
2025-08-18 13:24:16.408  4254-4254  Common                  com.dspread.mdm.service              D  🔧 TimerConfig ========== 当前定时器配置 (生产模式) ==========
2025-08-18 13:24:16.425  4254-4254  Provisioning            com.dspread.mdm.service              I  ℹ️ 主目录 创建成功: /data/pos/config
2025-08-18 13:24:16.431  4254-4254  Provisioning            com.dspread.mdm.service              D  🔧 主目录 权限验证通过
2025-08-18 13:24:16.437  4254-4254  Provisioning            com.dspread.mdm.service              I  ℹ️ 使用主配置目录: /data/pos/config/
2025-08-18 13:24:16.443  4254-4254  Provisioning            com.dspread.mdm.service              I  ℹ️ 配置路径初始化完成: 系统目录
2025-08-18 13:24:16.449  4254-4254  Provisioning            com.dspread.mdm.service              D  🔧 状态标志文件不存在，使用默认值
2025-08-18 13:24:16.461  4254-4254  Common                  com.dspread.mdm.service              D  🔧 TimerConfig 使用Provisioning配置的心跳间隔: 300秒
2025-08-18 13:24:16.468  4254-4254  Common                  com.dspread.mdm.service              D  🔧 TimerConfig ①心跳定时器: 300秒
2025-08-18 13:24:16.475  4254-4254  Common                  com.dspread.mdm.service              D  🔧 TimerConfig 使用Provisioning配置的终端信息上传间隔: 900秒
2025-08-18 13:24:16.481  4254-4254  Common                  com.dspread.mdm.service              D  🔧 TimerConfig ②终端信息上传: 900秒
2025-08-18 13:24:16.487  4254-4254  Common                  com.dspread.mdm.service              D  🔧 TimerConfig ③任务执行: 60秒
2025-08-18 13:24:16.494  4254-4254  Common                  com.dspread.mdm.service              D  🔧 TimerConfig ④服务守护: 120秒
2025-08-18 13:24:16.500  4254-4254  Common                  com.dspread.mdm.service              D  🔧 TimerConfig ⑤Provisioning: 43200秒
2025-08-18 13:24:16.507  4254-4254  Common                  com.dspread.mdm.service              D  🔧 TimerConfig ===============================================
2025-08-18 13:24:16.513  4254-4254  Common                  com.dspread.mdm.service              D  🔧 日志配置：生产环境模式
2025-08-18 13:24:16.519  4254-4254  Common                  com.dspread.mdm.service              D  🔧 [DebugConfig] 调试模式已禁用
2025-08-18 13:24:16.525  4254-4254  Common                  com.dspread.mdm.service              D  🔧 [DebugConfig] ========== 当前日志配置 ==========
2025-08-18 13:24:16.675  4254-4254  Common                  com.dspread.mdm.service              D  🔧 [DebugConfig]             ========== 统一配置状态 (生产模式) ==========
                                                                                                    
                                                                                                                LogStream配置 (生产模式):
                                                                                                    启用状态: true
                                                                                                    单个日志文件: 5MB
                                                                                                    压缩文件总限制: 250MB
                                                                                                    原始日志总限制: 250MB
                                                                                                    Recent日志大小: 128KB
                                                                                                    上传URL: https://siot-log01.s3.ap-northeast-1.amazonaws.com/log/
                                                                                                    
                                                                                                                Provisioning配置 (生产模式):
                                                                                                    配置目录: /data/pos/config/
                                                                                                    媒体目录: /data/pos/media/
                                                                                                    Logo目录: /data/pos/media/logo/
                                                                                                    开机动画目录: /data/pos/media/bootanimation/
                                                                                                    
                                                                                                                ========== 日志配置状态 ==========
                                                                                                    全局开关: true
                                                                                                    
                                                                                                    模块开关:
                                                                                                      Common: true
                                                                                                      HTTPS: true
                                                                                                      WebSocket: true
                                                                                                      API: true
                                                                                                      Service: true
                                                                                                      Provisioning: true
                                                                                                      Task: true
                                                                                                      RuleBase: true
                                                                                                      Application: true
                                                                                                      LogStream: false
                                                                                                      RemoteView: false
                                                                                                      Wi-Fi: false
                                                                                                      APN: false
                                                                                                      GeoFence: false
                                                                                                      Receiver: true
                                                                                                    ================================
                                                                                                                =========================================
2025-08-18 13:24:16.681  4254-4254  Common                  com.dspread.mdm.service              D  🔧 [DebugConfig] =====================================
2025-08-18 13:24:16.687  4254-4254  Common                  com.dspread.mdm.service              D  🔧 [DebugConfig] 统一配置管理器初始化完成，当前模式: 生产模式
2025-08-18 13:24:16.694  4254-4254  Common                  com.dspread.mdm.service              D  🔧 [DebugConfig] 定时器调试模式已禁用
2025-08-18 13:24:16.700  4254-4254  Common                  com.dspread.mdm.service              D  🔧 TimerConfig ========== 当前定时器配置 (生产模式) ==========
2025-08-18 13:24:16.706  4254-4254  Common                  com.dspread.mdm.service              D  🔧 TimerConfig 使用Provisioning配置的心跳间隔: 300秒
2025-08-18 13:24:16.713  4254-4254  Common                  com.dspread.mdm.service              D  🔧 TimerConfig ①心跳定时器: 300秒
2025-08-18 13:24:16.719  4254-4254  Common                  com.dspread.mdm.service              D  🔧 TimerConfig 使用Provisioning配置的终端信息上传间隔: 900秒
2025-08-18 13:24:16.725  4254-4254  Common                  com.dspread.mdm.service              D  🔧 TimerConfig ②终端信息上传: 900秒
2025-08-18 13:24:16.731  4254-4254  Common                  com.dspread.mdm.service              D  🔧 TimerConfig ③任务执行: 60秒
2025-08-18 13:24:16.737  4254-4254  Common                  com.dspread.mdm.service              D  🔧 TimerConfig ④服务守护: 120秒
2025-08-18 13:24:16.743  4254-4254  Common                  com.dspread.mdm.service              D  🔧 TimerConfig ⑤Provisioning: 43200秒
2025-08-18 13:24:16.749  4254-4254  Common                  com.dspread.mdm.service              D  🔧 TimerConfig ===============================================
2025-08-18 13:24:16.755  4254-4254  Common                  com.dspread.mdm.service              D  🔧 日志配置：生产环境模式
2025-08-18 13:24:16.761  4254-4254  Common                  com.dspread.mdm.service              D  🔧 [DebugConfig] 调试模式已禁用
2025-08-18 13:24:16.766  4254-4254  Common                  com.dspread.mdm.service              D  🔧 [DebugConfig] ========== 当前日志配置 ==========
2025-08-18 13:24:16.871  4254-4254  Common                  com.dspread.mdm.service              D  🔧 [DebugConfig]             ========== 统一配置状态 (生产模式) ==========
                                                                                                    
                                                                                                                LogStream配置 (生产模式):
                                                                                                    启用状态: true
                                                                                                    单个日志文件: 5MB
                                                                                                    压缩文件总限制: 250MB
                                                                                                    原始日志总限制: 250MB
                                                                                                    Recent日志大小: 128KB
                                                                                                    上传URL: https://siot-log01.s3.ap-northeast-1.amazonaws.com/log/
                                                                                                    
                                                                                                                Provisioning配置 (生产模式):
                                                                                                    配置目录: /data/pos/config/
                                                                                                    媒体目录: /data/pos/media/
                                                                                                    Logo目录: /data/pos/media/logo/
                                                                                                    开机动画目录: /data/pos/media/bootanimation/
                                                                                                    
                                                                                                                ========== 日志配置状态 ==========
                                                                                                    全局开关: true
                                                                                                    
                                                                                                    模块开关:
                                                                                                      Common: true
                                                                                                      HTTPS: true
                                                                                                      WebSocket: true
                                                                                                      API: true
                                                                                                      Service: true
                                                                                                      Provisioning: true
                                                                                                      Task: true
                                                                                                      RuleBase: true
                                                                                                      Application: true
                                                                                                      LogStream: false
                                                                                                      RemoteView: false
                                                                                                      Wi-Fi: false
                                                                                                      APN: false
                                                                                                      GeoFence: false
                                                                                                      Receiver: true
                                                                                                    ================================
                                                                                                                =========================================
2025-08-18 13:24:16.877  4254-4254  Common                  com.dspread.mdm.service              D  🔧 [DebugConfig] =====================================
2025-08-18 13:24:16.883  4254-4254  Common                  com.dspread.mdm.service              D  🔧 SmartMdmServiceApp: 统一配置管理器初始化完成
2025-08-18 13:24:16.888  4254-4254  Common                  com.dspread.mdm.service              D  🔧 SmartMdmServiceApp: 开始初始化系统目录...
2025-08-18 13:24:16.895  4254-4254  Common                  com.dspread.mdm.service              D  🔧 SmartMdmServiceApp: 配置目录已存在 - /data/pos/config
2025-08-18 13:24:16.903  4254-4254  Common                  com.dspread.mdm.service              D  🔧 SmartMdmServiceApp: Logo目录创建 - 路径: /data/data/com.dspread.mdm.service/files/media/logo, 结果: true
2025-08-18 13:24:16.910  4254-4254  Common                  com.dspread.mdm.service              D  🔧 SmartMdmServiceApp: 开机动画目录创建 - 路径: /data/data/com.dspread.mdm.service/files/media/anim, 结果: true
2025-08-18 13:24:16.916  4254-4254  Common                  com.dspread.mdm.service              D  🔧 SmartMdmServiceApp: 系统目录初始化完成
2025-08-18 13:24:16.925  4254-4254  Platform                com.dspread.mdm.service              D  🔧 NetworkTrafficMonitor 初始化网络流量监控器
2025-08-18 13:24:16.936  4254-4254  Platform                com.dspread.mdm.service              D  🔧 NetworkTrafficMonitor 新的一天，重置流量统计
2025-08-18 13:24:16.941  4254-4254  Platform                com.dspread.mdm.service              D  🔧 NetworkTrafficMonitor 流量统计已重置
2025-08-18 13:24:16.947  4254-4254  Platform                com.dspread.mdm.service              D  🔧 NetworkTrafficMonitor 检测到日期变化:  -> 2025-08-18
2025-08-18 13:24:16.953  4254-4254  Platform                com.dspread.mdm.service              D  🔧 NetworkTrafficMonitor 流量统计已重置
2025-08-18 13:24:17.016  4254-4254  Platform                com.dspread.mdm.service              D  🔧 NetworkTrafficMonitor 每日上送任务已启动
2025-08-18 13:24:17.022  4254-4254  Platform                com.dspread.mdm.service              D  🔧 NetworkTrafficMonitor 网络流量监控器初始化完成
2025-08-18 13:24:17.031  4254-4254  Receiver                com.dspread.mdm.service              D  🔧 ServiceGuardEventHandler 启动服务守护定时器: 120s
2025-08-18 13:24:17.037  4254-4254  Compatibil...geReporter com.dspread.mdm.service              D  Compat change id reported: 160794467; UID 1000; state: ENABLED
2025-08-18 13:24:17.043  4254-4254  Common                  com.dspread.mdm.service              I  ✅ ⏰ 设置服务守护定时器成功，下次执行: 120秒后 (2分钟)
2025-08-18 13:24:17.048  4254-4254  Common                  com.dspread.mdm.service              D  🔧 SmartMdmServiceApp: 服务守护定时器启动成功
2025-08-18 13:24:17.055  4254-4254  Common                  com.dspread.mdm.service              D  🔧 ServiceStartupManager 收到服务启动请求: APPLICATION_STARTUP, 强制启动: false
2025-08-18 13:24:17.066  4254-4254  Common                  com.dspread.mdm.service              D  🔧 ServiceStartupManager 开始执行服务启动: APPLICATION_STARTUP
2025-08-18 13:24:17.072  4254-4254  Common                  com.dspread.mdm.service              D  🔧 ServiceManager: 准备启动后台服务
2025-08-18 13:24:17.078  4254-4254  ContextImpl             com.dspread.mdm.service              W  Calling a method in the system process without a qualified user: android.app.ContextImpl.startService:1899 android.content.ContextWrapper.startService:825 com.dspread.mdm.service.platform.manager.ServiceManager.startBackgroundService:20 com.dspread.mdm.service.platform.manager.ServiceStartupManager.performServiceStart:121 com.dspread.mdm.service.platform.manager.ServiceStartupManager.startService:66 
2025-08-18 13:24:17.083  4254-4254  Common                  com.dspread.mdm.service              D  🔧 ServiceManager: 后台服务启动成功，ComponentName: ComponentInfo{com.dspread.mdm.service/com.dspread.mdm.service.services.SmartMdmBackgroundService}
2025-08-18 13:24:18.092  4254-4254  Common                  com.dspread.mdm.service              D  🔧 ServiceStartupManager 服务启动验证成功: APPLICATION_STARTUP
2025-08-18 13:24:18.098  4254-4254  Common                  com.dspread.mdm.service              D  🔧 ServiceStartupManager 服务启动成功: APPLICATION_STARTUP
2025-08-18 13:24:18.103  4254-4254  Common                  com.dspread.mdm.service              D  🔧 SmartMdmServiceApp: 主服务启动请求已提交
2025-08-18 13:24:18.109  4254-4254  Common                  com.dspread.mdm.service              D  🔧 SmartMdmServiceApp: Application启动完成，包名: com.dspread.mdm.service
2025-08-18 13:24:18.123  4254-4254  M-ProMotion             com.dspread.mdm.service              I  M-ProMotion is disabled
2025-08-18 13:24:18.188  4254-4254  Compatibil...geReporter com.dspread.mdm.service              D  Compat change id reported: 210923482; UID 1000; state: ENABLED
2025-08-18 13:24:18.223  4254-4254  Compatibil...geReporter com.dspread.mdm.service              D  Compat change id reported: 171228096; UID 1000; state: ENABLED
2025-08-18 13:24:18.267  4254-4254  getAnimDeal             com.dspread.mdm.service              I  mode:3 packageName:com.dspread.mdm.service result: false
2025-08-18 13:24:18.276  4254-4287  PowerHalWrapper         com.dspread.mdm.service              I  PowerHalWrapper.getInstance 
2025-08-18 13:24:18.276  4254-4254  SurfaceFactory          com.dspread.mdm.service              I  [static] sSurfaceFactory = com.mediatek.view.impl.SurfaceFactoryImpl@b1d7826
2025-08-18 13:24:18.281  4254-4254  Compatibil...geReporter com.dspread.mdm.service              D  Compat change id reported: 237531167; UID 1000; state: DISABLED
2025-08-18 13:24:18.286  4254-4254  OpenGLRenderer          com.dspread.mdm.service              W  Unknown dataspace 0
2025-08-18 13:24:18.292  4254-4254  VRI[TestActivity]       com.dspread.mdm.service              D  hardware acceleration = true, forceHwAccelerated = false
2025-08-18 13:24:18.296   781-1419  BufferQueueDebug        surfaceflinger                       E  [87d0f3e com.dspread.mdm.service/com.dspread.mdm.service.ui.activity.TestActivity#368](this:0xa5fa9c40,id:-1,api:0,p:-1,c:-1) id info cannot be read from '87d0f3e com.dspread.mdm.service/com.dspread.mdm.service.ui.activity.TestActivity#368'
2025-08-18 13:24:18.305  4254-4254  InputTransport          com.dspread.mdm.service              D  Create ARC handle: 0xb1ef07b0
2025-08-18 13:24:18.306  4254-4254  InputEventReceiver      com.dspread.mdm.service              D  Input log is disabled in InputEventReceiver.
2025-08-18 13:24:18.306  4254-4254  InputTransport          com.dspread.mdm.service              D  Input log is disabled in InputChannel.
2025-08-18 13:24:18.310  4254-4254  Choreographer           com.dspread.mdm.service              I  Skipped 119 frames!  The application may be doing too much work on its main thread.
2025-08-18 13:24:18.369   781-1419  BufferQueueDebug        surfaceflinger                       E  [com.dspread.mdm.service/com.dspread.mdm.service.ui.activity.TestActivity#369](this:0xa5f9ec40,id:-1,api:0,p:-1,c:-1) id info cannot be read from 'com.dspread.mdm.service/com.dspread.mdm.service.ui.activity.TestActivity#369'
2025-08-18 13:24:18.386  4254-4254  BufferQueueConsumer     com.dspread.mdm.service              D  [](id:109e00000000,api:0,p:-1,c:4254) connect: controlledByApp=false
2025-08-18 13:24:18.422  4254-4288  OpenGLRenderer          com.dspread.mdm.service              E  Unable to match the desired swap behavior.
2025-08-18 13:24:18.431  4254-4292  ion                     com.dspread.mdm.service              E  ioctl c0044901 failed with code -1: Invalid argument
2025-08-18 13:24:18.540  4254-4288  BLASTBufferQueue        com.dspread.mdm.service              D  [VRI[TestActivity]#0](f:0,a:1) acquireNextBufferLocked size=480x854 mFrameNumber=1 applyTransaction=true mTimestamp=197759352934(auto) mPendingTransactions.size=0 graphicBufferId=18270790877189 transform=3
2025-08-18 13:24:18.546   781-1419  BufferQueueDebug        surfaceflinger                       E  [Surface(name=87d0f3e com.dspread.mdm.service/com.dspread.mdm.service.ui.activity.TestActivity)/@0x4286384 - animation-leash of starting_reveal#372](this:0xa5f80c40,id:-1,api:0,p:-1,c:-1) id info cannot be read from 'Surface(name=87d0f3e com.dspread.mdm.service/com.dspread.mdm.service.ui.activity.TestActivity)/@0x4286384 - animation-leash of starting_reveal#372'
2025-08-18 13:24:18.554  4254-4272  OpenGLRenderer          com.dspread.mdm.service              I  Davey! duration=2218ms; Flags=1, FrameTimelineVsyncId=40712, IntendedVsync=195539594397, Vsync=197526557984, InputEventId=0, HandleInputStart=197531845164, AnimationStart=197531868549, PerformTraversalsStart=197532560626, DrawStart=197643290164, FrameDeadline=195565094397, FrameInterval=197530538780, FrameStartTime=16697173, SyncQueued=197657376934, SyncStart=197660105549, IssueDrawCommandsStart=197660519934, SwapBuffers=197757921472, FrameCompleted=197761314780, DequeueBufferDuration=0, QueueBufferDuration=1082769, GpuCompleted=197761314780, SwapBuffersCompleted=197760911011, DisplayPresentTime=32765890657609823, CommandSubmissionCompleted=197757921472, 
2025-08-18 13:24:18.558  4254-4254  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 服务启动
2025-08-18 13:24:18.597  4254-4254  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 开始服务初始化
2025-08-18 13:24:18.604  4254-4254  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] Android 14+: 初始化DSPREAD服务
2025-08-18 13:24:18.613  4254-4254  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 服务创建完成
2025-08-18 13:24:18.614  4254-4298  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 后台线程：初始化DSPREAD服务
2025-08-18 13:24:18.621  4254-4254  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] onStartCommand调用
2025-08-18 13:24:18.627  4254-4298  Platform                com.dspread.mdm.service              D  🔧 DspreadService 开始初始化DSPREAD服务
2025-08-18 13:24:18.637  4254-4254  Platform                com.dspread.mdm.service              D  🔧 WakeLockManager 获取智能WakeLock成功，持续时间: 60000ms
2025-08-18 13:24:18.638  4254-4298  ContextImpl             com.dspread.mdm.service              W  Calling a method in the system process without a qualified user: android.app.ContextImpl.bindService:1988 android.content.ContextWrapper.bindService:861 com.dspread.mdm.service.services.DspreadService.bindSysService:110 com.dspread.mdm.service.services.DspreadService.initialize:62 com.dspread.mdm.service.services.SmartMdmBackgroundService.initializeDspreadServiceAsync$lambda$1:227 
2025-08-18 13:24:18.646  4254-4298  Platform                com.dspread.mdm.service              D  🔧 DspreadService 系统服务绑定请求已发送
2025-08-18 13:24:18.652  4254-4254  Platform                com.dspread.mdm.service              D  🔧 WakeLockManager 设置WakeLock续期定时器: 60000ms
2025-08-18 13:24:18.685  4254-4254  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 初始化基础组件
2025-08-18 13:24:18.695  4254-4254  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 开始初始化...
2025-08-18 13:24:18.712  4254-4254  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 包更新广播接收器注册完成
2025-08-18 13:24:18.718  4254-4254  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 统一广播接收器注册完成
2025-08-18 13:24:18.724  4254-4254  Common                  com.dspread.mdm.service              I  ✅ BroadcastManager 初始化完成
2025-08-18 13:24:18.733  4254-4254  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: NetworkEventHandler -> android.net.conn.CONNECTIVITY_CHANGE
2025-08-18 13:24:18.739  4254-4254  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: NetworkEventHandler -> android.net.wifi.STATE_CHANGE
2025-08-18 13:24:18.747  4254-4254  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: BatteryEventHandler -> android.intent.action.BATTERY_CHANGED
2025-08-18 13:24:18.753  4254-4254  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: BatteryEventHandler -> android.intent.action.BATTERY_LOW
2025-08-18 13:24:18.760  4254-4254  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: BatteryEventHandler -> android.intent.action.BATTERY_OKAY
2025-08-18 13:24:18.766  4254-4254  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: BatteryEventHandler -> android.intent.action.ACTION_POWER_CONNECTED
2025-08-18 13:24:18.773  4254-4254  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: BatteryEventHandler -> android.intent.action.ACTION_POWER_DISCONNECTED
2025-08-18 13:24:18.781  4254-4254  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: SystemEventHandler -> android.intent.action.BOOT_COMPLETED
2025-08-18 13:24:18.781  4254-4283  Platform                com.dspread.mdm.service              D  🔧 DeviceInfoApi 单例实例已创建
2025-08-18 13:24:18.787  4254-4254  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: SystemEventHandler -> android.intent.action.QUICKBOOT_POWERON
2025-08-18 13:24:18.793  4254-4254  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: SystemEventHandler -> android.intent.action.LOCKED_BOOT_COMPLETED
2025-08-18 13:24:18.800  4254-4254  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: SystemEventHandler -> android.intent.action.ACTION_SHUTDOWN
2025-08-18 13:24:18.806  4254-4254  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: SystemEventHandler -> android.intent.action.REBOOT
2025-08-18 13:24:18.813  4254-4254  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: ScreenEventHandler -> android.intent.action.SCREEN_ON
2025-08-18 13:24:18.819  4254-4254  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: ScreenEventHandler -> android.intent.action.SCREEN_OFF
2025-08-18 13:24:18.828  4254-4254  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: HeartbeatEventHandler -> com.dspread.mdm.service.POLL_TIMER_START
2025-08-18 13:24:18.838  4254-4254  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: PackageUpdateEventHandler -> android.intent.action.PACKAGE_ADDED
2025-08-18 13:24:18.844  4254-4254  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: PackageUpdateEventHandler -> android.intent.action.PACKAGE_REMOVED
2025-08-18 13:24:18.851  4254-4254  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: PackageUpdateEventHandler -> android.intent.action.PACKAGE_REPLACED
2025-08-18 13:24:18.856  4254-4254  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: PackageUpdateEventHandler -> com.dspread.mdm.service.CHECK_SELF_UPDATE_STATUS
2025-08-18 13:24:18.865  4254-4254  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: TaskExecuteEventHandler -> com.dspread.mdm.service.WSTASK_EXEC_BC
2025-08-18 13:24:18.872  4254-4254  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: TerminalInfoEventHandler -> com.dspread.mdm.service.TER_INFO_UPLOAD_BC
2025-08-18 13:24:18.878  4254-4254  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: ServiceGuardEventHandler -> com.dspread.mdm.service.SERVICE_GUARD_TIMER
2025-08-18 13:24:18.885  4254-4254  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: ServiceManagementEventHandler -> android.intent.action.USER_PRESENT
2025-08-18 13:24:18.892  4254-4254  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: ServiceManagementEventHandler -> android.intent.action.TIME_TICK
2025-08-18 13:24:18.898  4254-4254  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: ServiceManagementEventHandler -> com.dspread.mdm.service.SERVICE_RESTART
2025-08-18 13:24:18.904  4254-4254  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: ServiceManagementEventHandler -> android.net.conn.CONNECTIVITY_CHANGE
2025-08-18 13:24:18.911  4254-4254  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: WakeLockEventHandler -> com.dspread.mdm.service.WAKELOCK_RENEWAL
2025-08-18 13:24:18.917  4254-4254  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: WakeLockEventHandler -> com.dspread.mdm.service.WAKELOCK_ACQUIRE
2025-08-18 13:24:18.923  4254-4254  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: WakeLockEventHandler -> com.dspread.mdm.service.WAKELOCK_RELEASE
2025-08-18 13:24:18.931  4254-4254  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: ProvisioningEventHandler -> com.dspread.mdm.service.PROVISIONING_TIMER
2025-08-18 13:24:18.937  4254-4254  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: ProvisioningEventHandler -> android.net.conn.CONNECTIVITY_CHANGE
2025-08-18 13:24:18.943  4254-4254  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: ProvisioningEventHandler -> android.intent.action.BOOT_COMPLETED
2025-08-18 13:24:18.951   781-1017  BufferQueueDebug        surfaceflinger                       E  [Surface(name=2ba9c73 Splash Screen com.dspread.mdm.service)/@0xf51e552 - animation-leash of window_animation#373](this:0xa5f6cc40,id:-1,api:0,p:-1,c:-1) id info cannot be read from 'Surface(name=2ba9c73 Splash Screen com.dspread.mdm.service)/@0xf51e552 - animation-leash of window_animation#373'
2025-08-18 13:24:18.994  4254-4254  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 服务初始化完成
2025-08-18 13:24:19.012  4254-4254  Platform                com.dspread.mdm.service              D  🔧 DspreadService 系统服务连接成功: ComponentInfo{com.dspread.sdkservice/com.dspread.sdkservice.SDKSysService}
2025-08-18 13:24:19.019  4254-4254  Platform                com.dspread.mdm.service              D  🔧 DspreadService 系统服务绑定完成
2025-08-18 13:24:19.027  4254-4298  ContextImpl             com.dspread.mdm.service              W  Calling a method in the system process without a qualified user: android.app.ContextImpl.bindService:1988 android.content.ContextWrapper.bindService:861 com.dspread.mdm.service.services.DspreadService.bindDevService:165 com.dspread.mdm.service.services.DspreadService.initialize:65 com.dspread.mdm.service.services.SmartMdmBackgroundService.initializeDspreadServiceAsync$lambda$1:227 
2025-08-18 13:24:19.031  4254-4298  Platform                com.dspread.mdm.service              D  🔧 DspreadService 设备服务绑定请求已发送
2025-08-18 13:24:19.036  4254-4254  Receiver                com.dspread.mdm.service              D  🔧 电池状态更新: 电池状态: 电量=73%, 温度=29.1°C, 健康=2, 充电=是, 低电量=否, 耗尽=否
2025-08-18 13:24:19.042  4254-4254  Receiver                com.dspread.mdm.service              D  🔧 电池状态变化: 电量=73%, 温度=29°C, 充电=true
2025-08-18 13:24:19.070  4254-4254  WebSocket               com.dspread.mdm.service              I  🔧 C0902 被动式上送: battery_level_change_5 (被动: 1)
2025-08-18 13:24:19.087  4254-4254  Platform                com.dspread.mdm.service              E  ❌ DspreadService 设备服务不可用，无法获取序列号
2025-08-18 13:24:19.110  4254-4254  WebSocket               com.dspread.mdm.service              I  🔧 消息已缓存: C0902 (缓存数量: 1)
2025-08-18 13:24:19.115  4254-4254  WebSocket               com.dspread.mdm.service              I  🔧 C0902 电池状态上送成功 (1, trigger=battery_level_change_5)
2025-08-18 13:24:19.125  4254-4254  Platform                com.dspread.mdm.service              D  🔧 NetworkApi 单例实例已创建
2025-08-18 13:24:19.137  4254-4254  Receiver                com.dspread.mdm.service              D  🔧 网络状态变化: 连接=true, 类型=WIFI
2025-08-18 13:24:19.145  4254-4254  Receiver                com.dspread.mdm.service              D  🔧 网络恢复，尝试重新连接WebSocket
2025-08-18 13:24:19.151  4254-4254  Receiver                com.dspread.mdm.service              D  🔧 ServiceManagementEventHandler 处理网络变化事件
2025-08-18 13:24:19.161  4254-4254  Common                  com.dspread.mdm.service              D  🔧 ServiceStartupManager 服务正在运行，无需启动: NETWORK_CHANGE
2025-08-18 13:24:19.167  4254-4254  Receiver                com.dspread.mdm.service              D  🔧 ServiceManagementEventHandler 网络变化后服务检查完成
2025-08-18 13:24:19.173  4254-4254  Provisioning            com.dspread.mdm.service              D  🔧 ProvisioningEventHandler 网络状态变化
2025-08-18 13:24:19.179  4254-4254  Provisioning            com.dspread.mdm.service              D  🔧 ProvisioningEventHandler 忽略初始启动时的网络连接事件
2025-08-18 13:24:19.306  4254-4254  WebSocket               com.dspread.mdm.service              I  🔧 C0905 主动式上送: manual_trigger (主动: 1)
2025-08-18 13:24:19.313  4254-4254  WebSocket               com.dspread.mdm.service              I  🔧 首次上送，上送当日数据
2025-08-18 13:24:19.319  4254-4254  WebSocket               com.dspread.mdm.service              I  🔧 没有流量数据需要上送
2025-08-18 13:24:19.325  4254-4254  Common                  com.dspread.mdm.service              D  🔧 刷新时自动上送流量统计
2025-08-18 13:24:19.364  4254-4254  Platform                com.dspread.mdm.service              D  🔧 DspreadService 设备服务连接成功: ComponentInfo{com.dspread.sdkservice/com.dspread.sdkservice.SDKDevService}
2025-08-18 13:24:19.371  4254-4254  Platform                com.dspread.mdm.service              D  🔧 DspreadService 设备服务绑定完成
2025-08-18 13:24:19.408  4254-4283  Platform                com.dspread.mdm.service              D  🔧 DspreadService 解析SP版本: V1.0.5
2025-08-18 13:24:19.415  4254-4283  Platform                com.dspread.mdm.service              D  🔧 DspreadService 首次获取SP版本成功: V1.0.5 (原始: *SP_VERSION:V1.0.5#*COMMIT:2d7cc90#*DATETIME:20250...)
2025-08-18 13:24:19.487  4254-4254  Common                  com.dspread.mdm.service              D  🔧 TestActivity UI已更新: SP=V1.0.5, SN=01354090202503050399
2025-08-18 13:24:20.379  4254-4298  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 等待DSPREAD服务就绪 (1/10): true
2025-08-18 13:24:20.384  4254-4298  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] DSPREAD服务就绪，启动Provisioning
2025-08-18 13:24:20.390  4254-4298  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 验证DSPREAD服务功能
2025-08-18 13:24:20.396  4254-4298  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 设备序列号验证: 成功(01354090202503050399)
2025-08-18 13:24:20.402  4254-4298  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 服务状态 - 系统服务:✅ 设备服务:✅
2025-08-18 13:24:20.413  4254-4305  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 启动ProvisioningService
2025-08-18 13:24:20.426  4254-4305  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] ProvisioningService启动完成
2025-08-18 13:24:20.427  4254-4283  Provisioning            com.dspread.mdm.service              I  ℹ️ 开始执行Provisioning配置，触发类型: FIRST_BOOT
2025-08-18 13:24:20.438  4254-4283  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] Provisioning进度: 正在下载配置文件...
2025-08-18 13:24:20.448  4254-4283  Provisioning            com.dspread.mdm.service              I  ℹ️ 开始请求远程配置...
2025-08-18 13:24:20.461  4254-4283  Provisioning            com.dspread.mdm.service              I  ℹ️ 请求配置: http://***********:8080/status/config?SN=MDEzNTQwOTAyMDI1MDMwNTAzOTk=&timestamp=1755494660460
2025-08-18 13:24:20.489  4254-4283  TrafficStats            com.dspread.mdm.service              D  tagSocket(117) with statsTag=0xffffffff, statsUid=-1
2025-08-18 13:24:20.743  4254-4283  Provisioning            com.dspread.mdm.service              I  ℹ️ 远程配置获取成功
2025-08-18 13:24:20.752  4254-4283  Provisioning            com.dspread.mdm.service              I  ℹ️ 远程配置请求成功
2025-08-18 13:24:20.762  4254-4283  Provisioning            com.dspread.mdm.service              I  ℹ️ 远程配置解析成功 - CID: 10012001
2025-08-18 13:24:20.806  4254-4283  Provisioning            com.dspread.mdm.service              I  ℹ️ API响应已保存到本地: /data/pos/config/provisioning_config.json, 具体内容为:
                                                                                                    {"mode":"dev","data":{"requestTime":"1755494659713","customization":{"system":{"timezone":"Asia\/Hong_Kong","logo":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/test\/logo.bin","logoMd5":"ba1ee533924eae5c408465e7cddcbda4","bootAnimationMd5":"ba1ee533924eae5c408465e7cddcbda4","gps":{"minUpdateTime":"30","scheduleTime":"60","maxLocateTime":"0","minDistance":"10","valid_distance":"500","care":"1"},"powerSaveMode":{"enable":"1","screenTimeout":"0"},"bootAnimation":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/test\/bootanimation.zip"},"polling":{"heartbeatTime":"300","terminalInfoTime":"900","statusApiUrl":"ws:\/\/***********:8080\/status\/websocket\/register","remoteUrl":"ws:\/\/***********:8080\/remoteWSS\/websockify","uploadMode":"1","wssreconn":{"delayPolicy":"30","delaySwitch":"1","delayTime":"900"}}},"client":"default","cid":"10012001"},"function":"getProvisioningConfig","description":"SUCCESS","stateCode":"0","version":"v1.0.1.20250801"}
2025-08-18 13:24:20.814  4254-4283  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] Provisioning进度: 正在下载 Logo...
2025-08-18 13:24:20.823  4254-4283  Provisioning            com.dspread.mdm.service              I  ℹ️ 文件不存在，开始下载: Logo
2025-08-18 13:24:20.829  4254-4283  Provisioning            com.dspread.mdm.service              I  ℹ️ 下载: https://siot-bucket01.s3.ap-northeast-1.amazonaws.com/test/logo.bin -> /data/data/com.dspread.mdm.service/files/media/logo/logo.bin
2025-08-18 13:24:20.840  4254-4283  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 尝试下载，第1次
2025-08-18 13:24:20.846  4254-4283  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 开始下载
2025-08-18 13:24:20.853  4254-4283  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader URL: https://siot-bucket01.s3.ap-northeast-1.amazonaws.com/test/logo.bin
2025-08-18 13:24:20.859  4254-4283  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 路径: /data/data/com.dspread.mdm.service/files/media/logo/logo.bin
2025-08-18 13:24:20.866  4254-4283  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 本地文件不存在，创建文件
2025-08-18 13:24:20.873  4254-4283  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader allFileLength=0, 需要从服务器获取文件大小
2025-08-18 13:24:20.990  4254-4283  TrafficStats            com.dspread.mdm.service              D  tagSocket(121) with statsTag=0xffffffff, statsUid=-1
2025-08-18 13:24:21.734  4254-4283  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader HTTP响应码: 200
2025-08-18 13:24:21.742  4254-4283  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 服务器返回Content-Length: 4771451
2025-08-18 13:24:21.748  4254-4283  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 完整下载模式
2025-08-18 13:24:21.753  4254-4283  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 完整下载，使用服务器返回大小: 4771451
2025-08-18 13:24:21.759  4254-4283  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader localFile = true /data/data/com.dspread.mdm.service/files/media/logo/logo.bin, 追加模式: false
2025-08-18 13:24:22.036  4254-4285  Platform                com.dspread.mdm.service              D  🔧 NetworkTrafficMonitor 等待下次上送时间: 2025-08-19 00:00:00
2025-08-18 13:24:24.088  4254-4311  ProfileInstaller        com.dspread.mdm.service              D  Installing profile for com.dspread.mdm.service
2025-08-18 13:24:27.623  4254-4283  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 计算MD5: logo.bin
2025-08-18 13:24:27.698  4254-4283  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 文件: 4771451 MD5: ba1ee533924eae5c408465e7cddcbda4
2025-08-18 13:24:27.704  4254-4283  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader MD5校验正确
2025-08-18 13:24:27.709  4254-4283  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 清理连接资源
2025-08-18 13:24:27.715  4254-4283  Provisioning            com.dspread.mdm.service              I  ℹ️ Logo 下载完成: /data/data/com.dspread.mdm.service/files/media/logo/logo.bin
2025-08-18 13:24:27.721  4254-4283  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] Provisioning进度: 正在下载 BootAnimation...
2025-08-18 13:24:27.727  4254-4283  Provisioning            com.dspread.mdm.service              I  ℹ️ 文件不存在，开始下载: BootAnimation
2025-08-18 13:24:27.733  4254-4283  Provisioning            com.dspread.mdm.service              I  ℹ️ 下载: https://siot-bucket01.s3.ap-northeast-1.amazonaws.com/test/bootanimation.zip -> /data/data/com.dspread.mdm.service/files/media/anim/bootanimation.zip
2025-08-18 13:24:27.739  4254-4283  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 尝试下载，第1次
2025-08-18 13:24:27.744  4254-4283  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 开始下载
2025-08-18 13:24:27.750  4254-4283  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader URL: https://siot-bucket01.s3.ap-northeast-1.amazonaws.com/test/bootanimation.zip
2025-08-18 13:24:27.755  4254-4283  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 路径: /data/data/com.dspread.mdm.service/files/media/anim/bootanimation.zip
2025-08-18 13:24:27.762  4254-4283  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 本地文件不存在，创建文件
2025-08-18 13:24:27.768  4254-4283  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader allFileLength=0, 需要从服务器获取文件大小
2025-08-18 13:24:27.950  4254-4283  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader HTTP响应码: 200
2025-08-18 13:24:27.958  4254-4283  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 服务器返回Content-Length: 4771451
2025-08-18 13:24:27.965  4254-4283  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 完整下载模式
2025-08-18 13:24:27.973  4254-4283  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 完整下载，使用服务器返回大小: 4771451
2025-08-18 13:24:27.981  4254-4283  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader localFile = true /data/data/com.dspread.mdm.service/files/media/anim/bootanimation.zip, 追加模式: false
2025-08-18 13:24:34.128  4254-4283  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 计算MD5: bootanimation.zip
2025-08-18 13:24:34.216  4254-4283  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 文件: 4771451 MD5: ba1ee533924eae5c408465e7cddcbda4
2025-08-18 13:24:34.222  4254-4283  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader MD5校验正确
2025-08-18 13:24:34.227  4254-4283  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 清理连接资源
2025-08-18 13:24:34.233  4254-4283  Provisioning            com.dspread.mdm.service              I  ℹ️ BootAnimation 下载完成: /data/data/com.dspread.mdm.service/files/media/anim/bootanimation.zip
2025-08-18 13:24:34.238  4254-4283  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] Provisioning进度: 正在应用配置...
2025-08-18 13:24:34.244  4254-4283  Provisioning            com.dspread.mdm.service              I  ℹ️ 配置应用完成
2025-08-18 13:24:34.249  4254-4283  Provisioning            com.dspread.mdm.service              I  ℹ️ 远程配置成功，标记首次配置完成
2025-08-18 13:24:34.258  4254-4283  Provisioning            com.dspread.mdm.service              D  🔧 状态标志保存成功: /data/pos/config/provisioning_flags.json
2025-08-18 13:24:34.263  4254-4283  Provisioning            com.dspread.mdm.service              I  ℹ️ Provisioning配置执行完成
2025-08-18 13:24:34.269  4254-4283  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] Provisioning完成，启动核心组件
2025-08-18 13:24:34.275  4254-4254  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 开始初始化核心组件
2025-08-18 13:24:34.299  4254-4254  WebSocket               com.dspread.mdm.service              I  🔧 开始初始化 WebSocket 组件...
2025-08-18 13:24:34.310  4254-4254  WebSocket               com.dspread.mdm.service              I  🔧 初始化 WebSocket 密钥管理器
2025-08-18 13:24:34.319  4254-4254  WebSocket               com.dspread.mdm.service              I  🔧 生成客户端公私钥对
2025-08-18 13:24:34.416  4254-4254  Common                  com.dspread.mdm.service              I  ✅ WebSocket 密钥管理器初始化完成
2025-08-18 13:24:34.426  4254-4254  WebSocket               com.dspread.mdm.service              I  🔧 开始初始化 WsMessageCenter...
2025-08-18 13:24:34.431  4254-4254  WebSocket               com.dspread.mdm.service              I  🔧 WsMessageCenter 首次初始化
2025-08-18 13:24:34.449  4254-4254  WebSocket               com.dspread.mdm.service              I  🔧 WsTaskManager 初始化完成
2025-08-18 13:24:34.549  4254-4254  WebSocket               com.dspread.mdm.service              I  🔧 WsMessageCenter 初始化完成
2025-08-18 13:24:34.555  4254-4254  WebSocket               com.dspread.mdm.service              I  🔧 开始清理终态任务，当前任务数量: 0
2025-08-18 13:24:34.560  4254-4254  WebSocket               com.dspread.mdm.service              I  🔧 没有需要清理的终态任务
2025-08-18 13:24:34.566  4254-4254  WebSocket               com.dspread.mdm.service              I  🔧 开始重置任务状态，当前任务个数: 0
2025-08-18 13:24:34.572  4254-4254  WebSocket               com.dspread.mdm.service              I  🔧 没有需要重置的任务
2025-08-18 13:24:34.577  4254-4254  Common                  com.dspread.mdm.service              I  ✅ 任务状态恢复完成
2025-08-18 13:24:34.593  4254-4254  WebSocket               com.dspread.mdm.service              I  🔧 使用Provisioning配置的WebSocket URL: ws://***********:8080/status/websocket/register
2025-08-18 13:24:34.599  4254-4254  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接管理器初始化完成
2025-08-18 13:24:34.605  4254-4254  Common                  com.dspread.mdm.service              I  ✅ WebSocket 管理器初始化完成
2025-08-18 13:24:34.610  4254-4254  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 管理器初始化完成
2025-08-18 13:24:34.617  4254-4254  Common                  com.dspread.mdm.service              I  ✅ WebSocket 组件初始化完成
2025-08-18 13:24:34.622  4254-4254  WebSocket               com.dspread.mdm.service              I  🔧 启动 WebSocket 连接...
2025-08-18 13:24:34.628  4254-4254  WebSocket               com.dspread.mdm.service              I  🔧 开始连接 WebSocket...
2025-08-18 13:24:34.648  4254-4254  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接到: ws://***********:8080/status/websocket/register?serialNo=MDEzNTQwOTAyMDI1MDMwNTAzOTk=&publicKey=TUlHZk1BMEdDU3FHU0liM0RRRUJBUVVBQTRHTkFEQ0JpUUtCZ1FDc0ZOdmorMTIwUDRELzdram5LUjRrS3VnM0t1cy9nc3RsZCtxdWNuWE9TcWlpRDE2azVWWUV2VnY5UlBpM0tXNXAzTVYwdEVmR2hkU3BMNDNjSzVPWlhVWVM3MzdRNlAycXBSNzZsdTFPQjBVeHRFUGgzOFN4ZmpYQjBpK2pXRXVjNFh1Q0hjYTg2Vm5CbWFYSjZZNEcxd2hHSGJUQUE3MktaSXBsUURjZWZRSURBUUFC&query=1&msgVer=3&timestamp=1755494674633&signature=AdEUkTZKBaTz4TitEjzvBO/wdwK2vHO1r1pwdjOsdc5wMxD4/aC7X+6jgG7JuzWK5vhbCjEd/9+CdJjBLowdyYePj83zZc5eQPSDb/JdbHhCUdGrbUp9DXvgS9t67fwVf4cnF01rjCuVI4ckvNHaC6ZDXjSWzR9B2P/VthOwEQo=
2025-08-18 13:24:34.656  4254-4254  WebSocket               com.dspread.mdm.service              I  🔧 未检测到系统代理设置
2025-08-18 13:24:34.689  4254-4254  WebSocket               com.dspread.mdm.service              I  🔧 ===== WebSocket 库默认配置 =====
2025-08-18 13:24:34.695  4254-4254  WebSocket               com.dspread.mdm.service              I  🔧 默认 PING 间隔: 60000ms (60秒)
2025-08-18 13:24:34.700  4254-4254  WebSocket               com.dspread.mdm.service              I  🔧 默认 PONG 间隔: 0ms (0秒)
2025-08-18 13:24:34.706  4254-4254  WebSocket               com.dspread.mdm.service              I  🔧 帧队列大小: 5
2025-08-18 13:24:34.712  4254-4254  WebSocket               com.dspread.mdm.service              I  🔧 连接超时: 15000ms (15秒)
2025-08-18 13:24:34.717  4254-4254  WebSocket               com.dspread.mdm.service              I  🔧 允许缺失关闭帧: false
2025-08-18 13:24:34.723  4254-4254  WebSocket               com.dspread.mdm.service              I  🔧 ================================
2025-08-18 13:24:34.729  4254-4254  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接请求已发送
2025-08-18 13:24:34.734  4254-4317  TrafficStats            com.dspread.mdm.service              D  tagSocket(5) with statsTag=0xffffffff, statsUid=-1
2025-08-18 13:24:34.735  4254-4254  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接启动完成
2025-08-18 13:24:34.743  4254-4254  Common                  com.dspread.mdm.service              D  🔧 [ModuleManagerRegistry] 模块管理器注册中心初始化完成
2025-08-18 13:24:34.760  4254-4254  RuleBase                com.dspread.mdm.service              D  🔧 RuleBaseManager 开始初始化RuleBaseManager...
2025-08-18 13:24:34.770  4254-4254  RuleBase                com.dspread.mdm.service              D  🔧 RuleBaseStorage RuleBase存储管理器初始化完成
2025-08-18 13:24:34.776  4254-4254  RuleBase                com.dspread.mdm.service              D  🔧 RuleStateMonitor 状态监控器初始化完成
2025-08-18 13:24:34.781  4254-4254  RuleBase                com.dspread.mdm.service              D  🔧 RuleBaseManager RuleBaseManager初始化完成
2025-08-18 13:24:34.787  4254-4254  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] RuleBaseManager初始化成功
2025-08-18 13:24:34.794  4254-4254  Provisioning            com.dspread.mdm.service              D  🔧 ProvisioningEventHandler 启动Provisioning定时器: 43200s (12小时)
2025-08-18 13:24:34.794  4254-4283  Common                  com.dspread.mdm.service              D  🔧 [ModuleManagerRegistry] 启动所有模块
2025-08-18 13:24:34.804  4254-4254  Common                  com.dspread.mdm.service              I  ✅ ⏰ 设置初始化定时器成功，下次执行: 43200秒后 (12小时)
2025-08-18 13:24:34.807  4254-4283  Common                  com.dspread.mdm.service              D  🔧 [ModuleManagerRegistry] LogStreamManager实例创建完成
2025-08-18 13:24:34.810  4254-4254  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] Provisioning定时器启动成功
2025-08-18 13:24:34.813  4254-4283  Common                  com.dspread.mdm.service              D  🔧 Module LogStream Manager status changed to: INITIALIZED
2025-08-18 13:24:34.815  4254-4254  Common                  com.dspread.mdm.service              D  🔧 TimerConfig ========== 当前定时器配置 (生产模式) ==========
2025-08-18 13:24:34.822  4254-4283  Common                  com.dspread.mdm.service              D  🔧 [ModuleManagerRegistry] LogStreamManager初始化成功
2025-08-18 13:24:34.827  4254-4283  Common                  com.dspread.mdm.service              D  🔧 Module LogStream Manager status changed to: STARTING
2025-08-18 13:24:34.830  4254-4254  Common                  com.dspread.mdm.service              D  🔧 TimerConfig 使用Provisioning配置的心跳间隔: 300秒
2025-08-18 13:24:34.836  4254-4254  Common                  com.dspread.mdm.service              D  🔧 TimerConfig ①心跳定时器: 300秒
2025-08-18 13:24:34.852  4254-4254  Common                  com.dspread.mdm.service              D  🔧 TimerConfig 使用Provisioning配置的终端信息上传间隔: 900秒
2025-08-18 13:24:34.858  4254-4254  Common                  com.dspread.mdm.service              D  🔧 TimerConfig ②终端信息上传: 900秒
2025-08-18 13:24:34.864  4254-4254  Common                  com.dspread.mdm.service              D  🔧 TimerConfig ③任务执行: 60秒
2025-08-18 13:24:34.870  4254-4254  Common                  com.dspread.mdm.service              D  🔧 TimerConfig ④服务守护: 120秒
2025-08-18 13:24:34.876  4254-4254  Common                  com.dspread.mdm.service              D  🔧 TimerConfig ⑤Provisioning: 43200秒
2025-08-18 13:24:34.882  4254-4254  Common                  com.dspread.mdm.service              D  🔧 TimerConfig ===============================================
2025-08-18 13:24:34.888  4254-4254  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 核心组件初始化完成
2025-08-18 13:24:34.918  4254-4283  Common                  com.dspread.mdm.service              D  🔧 Module LogStream Manager status changed to: RUNNING
2025-08-18 13:24:34.925  4254-4283  Common                  com.dspread.mdm.service              D  🔧 [ModuleManagerRegistry] LogStreamManager启动成功
2025-08-18 13:24:34.931  4254-4283  Common                  com.dspread.mdm.service              D  🔧 [ModuleManagerRegistry] LogStreamManager启动成功
2025-08-18 13:24:34.935  4254-4324  Common                  com.dspread.mdm.service              I  ✅ WebSocket 物理连接成功，等待服务器 hellMsg
2025-08-18 13:24:34.937  4254-4283  Common                  com.dspread.mdm.service              D  🔧 [ModuleManagerRegistry] 开始初始化地理围栏管理器
2025-08-18 13:24:34.942  4254-4324  WebSocket               com.dspread.mdm.service              I  🔧 ===== 心跳发送器实际配置 =====
2025-08-18 13:24:34.948  4254-4324  WebSocket               com.dspread.mdm.service              I  🔧 实际 PING 发送间隔: 60000ms (60秒)
2025-08-18 13:24:34.954  4254-4324  WebSocket               com.dspread.mdm.service              I  🔧 PING 定时器名称: PingSender
2025-08-18 13:24:34.960  4254-4324  WebSocket               com.dspread.mdm.service              I  🔧 实际 PONG 发送间隔: 0ms (0秒)
2025-08-18 13:24:34.962  4254-4283  Compatibil...geReporter com.dspread.mdm.service              D  Compat change id reported: 263076149; UID 1000; state: ENABLED
2025-08-18 13:24:34.966  4254-4324  WebSocket               com.dspread.mdm.service              I  🔧 PONG 定时器名称: PongSender
2025-08-18 13:24:34.972  4254-4324  WebSocket               com.dspread.mdm.service              I  🔧 ===============================
2025-08-18 13:24:34.973  4254-4283  Common                  com.dspread.mdm.service              D  🔧 Module Geofence Manager status changed to: INITIALIZED
2025-08-18 13:24:34.980  4254-4283  Common                  com.dspread.mdm.service              D  🔧 Module Geofence Manager status changed to: STARTING
2025-08-18 13:24:35.007  4254-4254  Platform                com.dspread.mdm.service              D  🔧 [WiFiManagerApi] WiFiManagerApi 单例实例已创建
2025-08-18 13:24:35.008  4254-4283  Platform                com.dspread.mdm.service              D  🔧 用户交互监控已启动（简化版本）
2025-08-18 13:24:35.013  4254-4283  Platform                com.dspread.mdm.service              D  🔧 使用模拟用户交互检测
2025-08-18 13:24:35.019  4254-4283  Platform                com.dspread.mdm.service              D  🔧 用户交互监控已启动
2025-08-18 13:24:35.026  4254-4324  Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"msg_state":"P","request_time":"1755148854367","data":{"taskList":[{"beginDate":"2024-08-14 05:20:54","taskType":"02","apkName":"Via","endDate":"9999-12-31 23:59:59","appId":"1755148854367","packName":"mark.via","versionName":"6.6.0","taskId":"1755148854367","versionCode":"20250713"}]},"tranCode":"ST001","request_id":"1755148854367ST001","version":"1","serialNo":"01354090202503050399"}
2025-08-18 13:24:35.026  4254-4283  Service                 com.dspread.mdm.service              D  🔧 [GeofenceManager]Constants.geofenceStatus = 1
2025-08-18 13:24:35.032  4254-4283  Service                 com.dspread.mdm.service              D  🔧 [GeofenceManager]-- Constants.storeId =  -- Constants.storeSsid =  -- Constants.storeIp = 
2025-08-18 13:24:35.034  4254-4324  WebSocket               com.dspread.mdm.service              I  🔧 准备发送响应确认 - requestId: 1755148854367ST001, needResponse: true
2025-08-18 13:24:35.040  4254-4283  Common                  com.dspread.mdm.service              D  🔧 Module Geofence Manager status changed to: RUNNING
2025-08-18 13:24:35.045  4254-4324  WebSocket               com.dspread.mdm.service              I  🔧 非重要消息，不缓存: C0000
2025-08-18 13:24:35.046  4254-4283  Common                  com.dspread.mdm.service              D  🔧 [ModuleManagerRegistry] 地理围栏管理器初始化成功
2025-08-18 13:24:35.052  4254-4283  Common                  com.dspread.mdm.service              D  🔧 [ModuleManagerRegistry] GeofenceManager启动成功
2025-08-18 13:24:35.057  4254-4324  WebSocket               com.dspread.mdm.service              I  🔧 非重要消息，不缓存: C0000
2025-08-18 13:24:35.058  4254-4283  Common                  com.dspread.mdm.service              D  🔧 [ModuleManagerRegistry] 所有模块启动完成
2025-08-18 13:24:35.064  4254-4324  WebSocket               com.dspread.mdm.service              I  🔧 已发送C0000响应确认: requestId=1755148854367ST001
2025-08-18 13:24:35.072  4254-4324  Task                    com.dspread.mdm.service              D  🔧 处理任务消息: ST001
2025-08-18 13:24:35.078  4254-4324  Task                    com.dspread.mdm.service              I  ℹ️ 更新任务列表: requestId=1755148854367ST001, 任务数量=1
2025-08-18 13:24:35.084  4254-4324  Task                    com.dspread.mdm.service              D  🔧 添加/更新任务: taskId=1755148854367
2025-08-18 13:24:35.094  4254-4324  Task                    com.dspread.mdm.service              D  🔧 保存任务列表到存储: 1 个
2025-08-18 13:24:35.099  4254-4324  Task                    com.dspread.mdm.service              D  🔧 开始处理任务列表，共 1 个任务
2025-08-18 13:24:35.106  4254-4324  Task                    com.dspread.mdm.service              D  🔧 处理任务: taskId=1755148854367, type=02, package=mark.via, apk=Via
2025-08-18 13:24:35.112  4254-4324  Task                    com.dspread.mdm.service              D  🔧 处理卸载任务: 1755148854367
2025-08-18 13:24:35.124  4254-4324  Task                    com.dspread.mdm.service              D  🔧 更新任务状态: taskId=1755148854367, state=D02
2025-08-18 13:24:35.134  4254-4324  Task                    com.dspread.mdm.service              D  🔧 保存任务列表到存储: 1 个
2025-08-18 13:24:35.146  4254-4324  Task                    com.dspread.mdm.service              D  🔧 保存任务列表到存储: 1 个
2025-08-18 13:24:35.152  4254-4324  WebSocket               com.dspread.mdm.service              I  🔧 ST001 主动式上送: task_failed (主动: 2)
2025-08-18 13:24:35.176  4254-4324  WebSocket               com.dspread.mdm.service              I  🔧 消息已缓存: C0108 (缓存数量: 2)
2025-08-18 13:24:35.182  4254-4324  WebSocket               com.dspread.mdm.service              I  🔧 任务结果实时上送: taskId=1755148854367, result=D02 (1)
2025-08-18 13:24:35.216  4254-4324  Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"hellMsg":"hello 01354090202503050399","tranCode":"S0000","version":"1","rebootTime":"03:05:02","serialNo":"01354090202503050399","deviceStatus":"6"}
2025-08-18 13:24:35.223  4254-4324  Common                  com.dspread.mdm.service              I  ✅ 收到服务器 hellMsg，WebSocket 业务连接成功！
2025-08-18 13:24:35.228  4254-4324  Common                  com.dspread.mdm.service              I  ✅ WebSocket 业务连接成功！
2025-08-18 13:24:35.234  4254-4324  WebSocket               com.dspread.mdm.service              I  🔧 业务连接成功，重连计数已重置为 0
2025-08-18 13:24:35.245  4254-4324  Common                  com.dspread.mdm.service              I  ✅ ⏰ 设置任务执行定时器成功，下次执行: 60秒后 (1分钟)
2025-08-18 13:24:35.251  4254-4324  WebSocket               com.dspread.mdm.service              I  🔧 已启动任务执行定时器
2025-08-18 13:24:35.266  4254-4324  Common                  com.dspread.mdm.service              D  🔧 TimerConfig 使用Provisioning配置的心跳间隔: 300秒
2025-08-18 13:24:35.276  4254-4324  Common                  com.dspread.mdm.service              I  ✅ ⏰ 设置心跳定时器成功，下次执行: 300秒后 (5分钟)
2025-08-18 13:24:35.281  4254-4324  WebSocket               com.dspread.mdm.service              I  🔧 已启动心跳定时器
2025-08-18 13:24:35.296  4254-4324  Common                  com.dspread.mdm.service              D  🔧 TimerConfig 使用Provisioning配置的终端信息上传间隔: 900秒
2025-08-18 13:24:35.306  4254-4324  Common                  com.dspread.mdm.service              I  ✅ ⏰ 设置终端信息定时器成功，下次执行: 900秒后 (15分钟)
2025-08-18 13:24:35.312  4254-4324  WebSocket               com.dspread.mdm.service              I  🔧 已启动终端信息上传定时器
2025-08-18 13:24:35.318  4254-4324  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接成功，检查自身更新任务状态
2025-08-18 13:24:35.325  4254-4324  ContextImpl             com.dspread.mdm.service              W  Calling a method in the system process without a qualified user: android.app.ContextImpl.sendBroadcast:1220 android.content.ContextWrapper.sendBroadcast:504 com.dspread.mdm.service.broadcast.core.BroadcastSender.sendBroadcast:99 com.dspread.mdm.service.broadcast.core.BroadcastSender.sendBroadcast:23 com.dspread.mdm.service.network.websocket.connection.WsConnectionManager.checkSelfUpdateTaskStatus:311 
2025-08-18 13:24:35.328  4254-4324  Receiver                com.dspread.mdm.service              D  🔧 发送广播: com.dspread.mdm.service.CHECK_SELF_UPDATE_STATUS
2025-08-18 13:24:35.329  4254-4254  Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 收到自身更新状态检查广播: com.dspread.mdm.service.CHECK_SELF_UPDATE_STATUS
2025-08-18 13:24:35.334  4254-4324  WebSocket               com.dspread.mdm.service              I  🔧 开始发送缓存消息，数量: 2
2025-08-18 13:24:35.335  4254-4254  Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 开始执行自身更新任务状态检查
2025-08-18 13:24:35.342  4254-4254  Task                    com.dspread.mdm.service              D  🔧 获取待执行任务数量: 0
2025-08-18 13:24:35.348  4254-4324  WebSocket               com.dspread.mdm.service              I  🔧 C0902 主动式上送: cached_message_resend (主动: 3)
2025-08-18 13:24:35.349  4254-4254  Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 待执行任务数量: 0
2025-08-18 13:24:35.383  4254-4324  Common                  com.dspread.mdm.service              I  ✅ 缓存消息发送成功: C0902
2025-08-18 13:24:35.890  4254-4324  WebSocket               com.dspread.mdm.service              I  🔧 C0108 主动式上送: cached_message_resend (主动: 4)
2025-08-18 13:24:35.911  4254-4324  Common                  com.dspread.mdm.service              I  ✅ 缓存消息发送成功: C0108
2025-08-18 13:24:36.418  4254-4324  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接成功
2025-08-18 13:24:36.425  4254-4324  WebSocket               com.dspread.mdm.service              I  🔧 设备状态: 6
2025-08-18 13:24:36.431  4254-4324  WebSocket               com.dspread.mdm.service              I  🔧 检查首次连接标志: isFirstTimeWebSocketConnected=true
2025-08-18 13:24:36.437  4254-4324  WebSocket               com.dspread.mdm.service              I  🔧 首次WebSocket连接成功，发送初始化信息并执行初始化检查
2025-08-18 13:24:36.443  4254-4324  WebSocket               com.dspread.mdm.service              I  🔧 开始发送初始化信息（带流量控制）
2025-08-18 13:24:36.449  4254-4324  WebSocket               com.dspread.mdm.service              I  🔧 C0901 被动式上送: first_connection (被动: 2)
2025-08-18 13:24:36.465  4254-4324  Platform                com.dspread.mdm.service              D  🔧 重新收集应用信息
2025-08-18 13:24:36.561  4254-4324  AppManager              com.dspread.mdm.service              I  ℹ️ 应用信息获取完成: 总数130(系统129/用户1) 返回1个
2025-08-18 13:24:36.573  4254-4324  Platform                com.dspread.mdm.service              D  🔧 应用信息: 1 个应用
2025-08-18 13:24:36.599  4254-4324  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0901","serialNo":"01354090202503050399","request_time":"1755494676579","request_id":"1755494676579C0901","version":"1","data":{"apkInfo":[{"packName":"com.dspread.mdm.service","apkName":"service","versionCode":13,"versionName":"1.0.10.20250818.DSPREAD.MDM.SERVICE","updateDate":"2025-08-18 13:24:14"}],"sytemInfo":{"androidVersion":"14","buildNumber":"BASE_D30M-MU_GEN_AP_V1.0.8_202507031912","aspl":"2025-03-05"},"serviceInfo":[]},"myVersionName":"1.0.10.20250818.DSPREAD.MDM.SERVICE","terminalDate":"20250818132436"}
2025-08-18 13:24:36.605  4254-4324  WebSocket               com.dspread.mdm.service              I  🔧 C0901 应用信息上送成功 (1)
2025-08-18 13:24:37.612  4254-4324  WebSocket               com.dspread.mdm.service              I  🔧 C0902 被动式上送: first_connection (被动: 3)
2025-08-18 13:24:37.646  4254-4324  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0902","serialNo":"01354090202503050399","request_time":"1755494677631","request_id":"1755494677631C0902","version":"1","data":{"batteryLife":73,"batteryHealth":2,"temprature":"29.1","isCharging":"1","isLowBattery":"0","powerOff":"0","outOfBattery":"0"},"myVersionName":"1.0.10.20250818.DSPREAD.MDM.SERVICE","terminalDate":"20250818132437"}
2025-08-18 13:24:37.653  4254-4324  WebSocket               com.dspread.mdm.service              I  🔧 C0902 电池状态上送成功 (1, trigger=first_connection)
2025-08-18 13:24:38.659  4254-4324  WebSocket               com.dspread.mdm.service              I  🔧 C0903 被动式上送: first_connection (被动: 4)
2025-08-18 13:24:38.705  4254-4324  Platform                com.dspread.mdm.service              D  🔧 内存总容量为： 1.86GB 
2025-08-18 13:24:38.712  4254-4324  Platform                com.dspread.mdm.service              D  🔧 内存使用量为： 0.81GB 
2025-08-18 13:24:38.836  4254-4324  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0903","serialNo":"01354090202503050399","request_time":"1755494678800","request_id":"1755494678800C0903","version":"1","data":{"hardwareInfo":[{"name":"CPU Usage","desc":"cpu:0.50"},{"name":"storage_usage_status","desc":" 4.41GB \/ 32.00GB ","state":"1"},{"name":"total_memory","desc":" 0.81GB \/ 1.86GB ","state":"1"},{"name":"androidVersion","desc":"14","state":"1"},{"name":"network_status","desc":"wifi","state":"1"},{"name":"OSVer","desc":"14","state":"1"}],"locationInfo":{"gps":{"longitude":"-999.0","latitude":"-999.0","satelliteCount":0,"satelliteCountInUsed":0,"satelliteAverSNR":"0.00","distance":"0.0","lockStatus":"1","lockMeter":"200","proId":"","proName":""},"baseSite":{"MCC":"","MNC":"","CID":"-1","LAC":"-1","DBM":"-999","IMSI":"","ICCID":""},"wifi":{"SSID":"fubox_2.4G","IP":"**************","MAC":"BE:87:C2:42:DF:75","SSTH":"-35","BSSID":"7c:10:c9:d6:9f:10"},"address":"无法获取"}},"myVersionName":"1.0.10.20250818.DSPREAD.MDM.SERVICE","terminalDate":"20250818132438"}
2025-08-18 13:24:38.842  4254-4324  WebSocket               com.dspread.mdm.service              I  🔧 C0903 数据信息上送成功 (1)
2025-08-18 13:24:39.849  4254-4324  WebSocket               com.dspread.mdm.service              I  🔧 C0904 被动式上送: first_connection (被动: 5)
2025-08-18 13:24:39.961  4254-4324  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0904","serialNo":"01354090202503050399","request_time":"1755494679924","request_id":"1755494679924C0904","version":"1","data":{"wifiOption":[{"SSID":"2306","SSTH":"-73"},{"SSID":"2205_5G","SSTH":"-58"},{"SSID":"2103","SSTH":"-60"},{"SSID":"DIRECT-C6-Mi All-in-One Inkjet","SSTH":"-49"},{"SSID":"2207-5G","SSTH":"-85"},{"SSID":"2207","SSTH":"-59"},{"SSID":"2103_5G","SSTH":"-76"},{"SSID":"@Ruijie-1816_5G","SSTH":"-68"},{"SSID":"2201","SSTH":"-77"},{"SSID":"fubox_2.4G","SSTH":"-35"},{"SSID":"2106","SSTH":"-69"},{"SSID":"DIRECT-CC-HP Laser 1188w","SSTH":"-61"},{"SSID":"2206","SSTH":"-28"},{"SSID":"DIRECT-1D-HP Laser 1188w","SSTH":"-79"},{"SSID":"2206-5G","SSTH":"-38"},{"SSID":"YTJH","SSTH":"-87"},{"SSID":"CMCC-Cc5b","SSTH":"-71"},{"SSID":"jqsf","SSTH":"-82"},{"SSID":"@Ruijie-1816","SSTH":"-41"}],"baseSite":{"MCC":"","MNC":"","CID":"-1","LAC":"-1","DBM":"-999","IMSI":"","ICCID":""},"wifi":{"SSID":"fubox_2.4G","IP":"**************","MAC":"BE:87:C2:42:DF:75","SSTH":"-35","BSSID":"7c:10:c9:d6:9f:10"}},"myVersionName":"1.0.10.20250818.DSPREAD.MDM.SERVICE","terminalDate":"20250818132439"}
2025-08-18 13:24:39.967  4254-4324  WebSocket               com.dspread.mdm.service              I  🔧 C0904 网络状态上送成功 (1)
2025-08-18 13:24:40.974  4254-4324  WebSocket               com.dspread.mdm.service              I  🔧 C0906 被动式上送: first_connection (被动: 6)
2025-08-18 13:24:40.991  4254-4324  Platform                com.dspread.mdm.service              D  🔧 DspreadService 使用缓存的SP版本: V1.0.5
2025-08-18 13:24:40.997  4254-4324  Platform                com.dspread.mdm.service              D  🔧 SpVersionApi 通过DspreadService获取SP版本: V1.0.5
2025-08-18 13:24:41.026  4254-4324  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0906","serialNo":"01354090202503050399","request_time":"1755494681009","request_id":"1755494681009C0906","version":"1","data":{"firmWareInfo":{"spfw":"V1.0.5"},"imei_1":"864177060143618","imei_2":"","wifi_mac":"be:87:c2:42:df:75","bt_mac":"","bsn":""},"myVersionName":"1.0.10.20250818.DSPREAD.MDM.SERVICE","terminalDate":"20250818132441"}
2025-08-18 13:24:41.033  4254-4324  WebSocket               com.dspread.mdm.service              I  🔧 C0906 设备信息上送成功 (1)
2025-08-18 13:24:41.040  4254-4324  WebSocket               com.dspread.mdm.service              I  🔧 C0109 被动式上送: first_connection (被动: 7)
2025-08-18 13:24:41.056  4254-4324  Platform                com.dspread.mdm.service              D  🔧 重新收集应用信息（强制刷新）
2025-08-18 13:24:41.143  4254-4324  AppManager              com.dspread.mdm.service              I  ℹ️ 应用信息获取完成: 总数130(系统129/用户1) 返回1个
2025-08-18 13:24:41.154  4254-4324  Platform                com.dspread.mdm.service              D  🔧 应用信息: 1 个应用
2025-08-18 13:24:41.231  4254-4324  Platform                com.dspread.mdm.service              D  🔧 内存总容量为： 1.86GB 
2025-08-18 13:24:41.237  4254-4324  Platform                com.dspread.mdm.service              D  🔧 内存使用量为： 0.81GB 
2025-08-18 13:24:41.329  4254-4324  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0109","serialNo":"01354090202503050399","request_time":"1755494681263","request_id":"1755494681263C0109","version":"1","data":{"apkInfo":[{"packName":"com.dspread.mdm.service","apkName":"service","versionCode":13,"versionName":"1.0.10.20250818.DSPREAD.MDM.SERVICE","updateDate":"2025-08-18 13:24:14"}],"locationInfo":{"gps":{"longitude":"-999.0","latitude":"-999.0","satelliteCount":0,"satelliteCountInUsed":0,"satelliteAverSNR":"0.00","distance":"0.0","lockStatus":"1","lockMeter":"200","proId":"","proName":""},"baseSite":{"MCC":"","MNC":"","CID":"-1","LAC":"-1","DBM":"-999","IMSI":"","ICCID":""},"wifi":{"SSID":"fubox_2.4G","IP":"**************","MAC":"BE:87:C2:42:DF:75","SSTH":"-34","BSSID":"7c:10:c9:d6:9f:10"},"address":"无法获取"},"hardwareInfo":[{"name":"CPU Usage","desc":"cpu:0.50"},{"name":"storage_usage_status","desc":" 4.41GB \/ 32.00GB ","state":"1"},{"name":"total_memory","desc":" 0.81GB \/ 1.86GB ","state":"1"},{"name":"androidVersion","desc":"14","state":"1"},{"name":"network_status","desc":"wifi","state":"1"},{"name":"OSVer","desc":"14","state":"1"}],"wifiOption":[{"SSID":"2306","SSTH":"-73"},{"SSID":"2205_5G","SSTH":"-58"},{"SSID":"2103","SSTH":"-60"},{"SSID":"DIRECT-C6-Mi All-in-One Inkjet","SSTH":"-49"},{"SSID":"2207-5G","SSTH":"-85"},{"SSID":"2207","SSTH":"-59"},{"SSID":"2103_5G","SSTH":"-76"},{"SSID":"@Ruijie-1816_5G","SSTH":"-68"},{"SSID":"2201","SSTH":"-77"},{"SSID":"fubox_2.4G","SSTH":"-35"},{"SSID":"2106","SSTH":"-69"},{"SSID":"DIRECT-CC-HP Laser 1188w","SSTH":"-61"},{"SSID":"2206","SSTH":"-28"},{"SSID":"DIRECT-1D-HP Laser 1188w","SSTH":"-79"},{"SSID":"2206-5G","SSTH":"-38"},{"SSID":"YTJH","SSTH":"-87"},{"SSID":"CMCC-Cc5b","SSTH":"-71"},{"SSID":"jqsf","SSTH":"-82"},{"SSID":"@Ruijie-1816","SSTH":"-41"}],"sytemInfo":{"androidVersion":"14","buildNumber":"BASE_D30M-MU_GEN_AP_V1.0.8_202507031912","aspl":"2025-03-05"}},"myVersionName":"1.0.10.20250818.DSPREAD.MDM.SERVICE","terminalDate":"20250818132441"}
2025-08-18 13:24:41.335  4254-4324  WebSocket               com.dspread.mdm.service              I  🔧 C0109 终端信息上传完成 (1, trigger=first_connection)
2025-08-18 13:24:41.341  4254-4324  Common                  com.dspread.mdm.service              I  ✅ 初始化信息发送完成
2025-08-18 13:24:41.348  4254-4324  WebSocket               com.dspread.mdm.service              I  🔧 检查服务更新结果
2025-08-18 13:24:41.356  4254-4324  Task                    com.dspread.mdm.service              I  ℹ️ 本地任务列表长度: 1
2025-08-18 13:24:41.362  4254-4324  Task                    com.dspread.mdm.service              D  🔧 本地任务列表内容: [{"beginDate":"2024-08-14 05:20:54","taskType":"02","apkName":"Via","endDate":"9999-12-31 23:59:59","appId":"1755148854367","packName":"mark.via","versionName":"6.6.0","taskId":"1755148854367","versionCode":"20250713","request_id":"1755148854367ST001","request_time":"1755148854367","silent_install":"","taskResult":"D02","lastUpdateTime":1755494675140}]
2025-08-18 13:24:41.369  4254-4324  Task                    com.dspread.mdm.service              I  ℹ️ 当前应用版本: versionCode=13, versionName=1.0.10.20250818.DSPREAD.MDM.SERVICE
2025-08-18 13:24:41.375  4254-4324  Task                    com.dspread.mdm.service              I  ℹ️ 任务列表数量: 1
2025-08-18 13:24:41.381  4254-4324  Task                    com.dspread.mdm.service              D  🔧 检查任务[0]: taskId=1755148854367, taskType=02, pkgName=mark.via, versionCode=20250713, versionName=6.6.0, taskResult=D02
2025-08-18 13:24:41.387  4254-4324  WebSocket               com.dspread.mdm.service              I  🔧 检查OS升级状态...
2025-08-18 13:24:41.396  4254-4324  Common                  com.dspread.mdm.service              D  🔧 OsUpdateStatusChecker WebSocket连接成功，检查OS升级状态
2025-08-18 13:24:41.402  4254-4324  Common                  com.dspread.mdm.service              D  🔧 OsUpdateStatusChecker 没有待检查的OS升级任务
2025-08-18 13:24:41.409  4254-4324  WebSocket               com.dspread.mdm.service              I  🔧 OS升级状态检查已触发
2025-08-18 13:24:41.415  4254-4324  WebSocket               com.dspread.mdm.service              I  🔧 首次连接初始化检查完成
2025-08-18 13:24:41.449  4254-4324  Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"request_time":"1755494674997","org_request_time":"1755494675167","org_request_id":"1755494675167C0108","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755494674997S0000","serialNo":"01354090202503050399"}
2025-08-18 13:24:41.458  4254-4324  WebSocket               com.dspread.mdm.service              I  🔧 收到响应消息: transCode=S0000, orgRequestId=1755494675167C0108, state=0, remark=
2025-08-18 13:24:41.465  4254-4324  WebSocket               com.dspread.mdm.service              I  🔧 处理任务结果响应: state=0
2025-08-18 13:24:41.471  4254-4324  WebSocket               com.dspread.mdm.service              I  🔧 任务结果上传成功
2025-08-18 13:24:41.504  4254-4324  Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"request_time":"1755494680106","org_request_time":"1755494681009","org_request_id":"1755494681009C0906","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755494680106S0000","serialNo":"01354090202503050399"}
2025-08-18 13:24:41.514  4254-4324  WebSocket               com.dspread.mdm.service              I  🔧 收到响应消息: transCode=S0000, orgRequestId=1755494681009C0906, state=0, remark=
2025-08-18 13:24:41.520  4254-4324  WebSocket               com.dspread.mdm.service              I  🔧 处理设备信息响应: state=0
2025-08-18 13:24:41.527  4254-4324  WebSocket               com.dspread.mdm.service              I  🔧 设备信息上传成功
2025-08-18 13:24:41.561  4254-4324  Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"request_time":"1755494680382","org_request_time":"1755494681263","org_request_id":"1755494681263C0109","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755494680382S0000","serialNo":"01354090202503050399"}
2025-08-18 13:24:41.570  4254-4324  WebSocket               com.dspread.mdm.service              I  🔧 收到响应消息: transCode=S0000, orgRequestId=1755494681263C0109, state=0, remark=
2025-08-18 13:24:41.576  4254-4324  WebSocket               com.dspread.mdm.service              I  🔧 终端信息上传成功
2025-08-18 13:24:41.583  4254-4324  WebSocket               com.dspread.mdm.service              I  🔧 终端信息上传成功标志已设置
2025-08-18 13:25:04.118  4254-4288  GED                     com.dspread.mdm.service              I  ged_boost_gpu_freq, level 100, eOrigin 2, final_idx 2, oppidx_max 2, oppidx_min 0
2025-08-18 13:25:04.130  4254-4254  BLASTBufferQueue        com.dspread.mdm.service              D  [VRI[TestActivity]#0](f:0,a:1) destructor()
2025-08-18 13:25:04.130  4254-4254  BufferQueueConsumer     com.dspread.mdm.service              D  [VRI[TestActivity]#0(BLAST Consumer)0](id:109e00000000,api:0,p:-1,c:4254) disconnect
2025-08-18 13:25:04.133  4254-4288  GED                     com.dspread.mdm.service              I  ged_boost_gpu_freq, level 100, eOrigin 2, final_idx 2, oppidx_max 2, oppidx_min 0
2025-08-18 13:25:04.439  4254-4254  Receiver                com.dspread.mdm.service              D  🔧 电池状态更新: 电池状态: 电量=73%, 温度=29.4°C, 健康=2, 充电=是, 低电量=否, 耗尽=否
2025-08-18 13:25:04.475  4254-4254  Receiver                com.dspread.mdm.service              D  🔧 屏幕关闭
2025-08-18 13:25:04.487  4254-4254  WebSocket               com.dspread.mdm.service              W  ⚠️ 未知触发条件: screen_off，默认按被动式处理
2025-08-18 13:25:04.512  4254-4254  WebSocket               com.dspread.mdm.service              I  🔧 C0201 被动式上送拒绝: screen_off (拒绝: 1)
2025-08-18 13:25:04.520  4254-4254  WebSocket               com.dspread.mdm.service              I  🔧 C0201 设备状态上送被流量控制阻止: 被动事件 'screen_off' 在平衡模式 - 重要变化下未启用
2025-08-18 13:25:04.527  4254-4254  Receiver                com.dspread.mdm.service              D  🔧 屏幕关闭处理完成
2025-08-18 13:25:15.374  4254-4324  Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"msg_state":"P","request_time":"1755494713915","data":{"taskList":[{"beginDate":"2024-08-18 05:25:13","taskType":"04","fileName":"e0369aa3315b48648f5354907bf40eef.zip","endDate":"9999-12-31 23:59:59","fileSize":"2820528","installBy":"0","fileMd5":"5350f88fbd2626b07917b165b24ec7d8","displayVer":"MU_GEN_AP_V1.0.8_202507031912-BASE_D30M-MU_GEN_AP_V1.0.8.1_202507032000_ota_test","taskId":"79b1560a8eb14c5c958b49e8521f3624&175f338e396f458e8d5935cfadb98f60","url":"https://siot-bucket01.s3.ap-northeast-1.amazonaws.com/os/upload/e0369aa3315b48648f5354907bf40eef.zip"}]},"tranCode":"ST001","request_id":"1755494713915ST001","version":"1","serialNo":"01354090202503050399"}
2025-08-18 13:25:15.384  4254-4324  WebSocket               com.dspread.mdm.service              I  🔧 准备发送响应确认 - requestId: 1755494713915ST001, needResponse: true
2025-08-18 13:25:15.415  4254-4324  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0000","serialNo":"01354090202503050399","request_time":"1755494715398","request_id":"1755494715398C0000","version":"1","org_request_id":"1755494713915ST001","org_request_time":"1755494713915","response_state":"0","myVersionName":"1.0.10.20250818.DSPREAD.MDM.SERVICE","terminalDate":"20250818132515"}
2025-08-18 13:25:15.440  4254-4324  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0000","serialNo":"01354090202503050399","request_time":"1755494715424","request_id":"1755494715424C0000","version":"1","org_request_id":"1755494713915ST001","org_request_time":"1755494713915","response_state":"0","myVersionName":"1.0.10.20250818.DSPREAD.MDM.SERVICE","terminalDate":"20250818132515"}
2025-08-18 13:25:15.450  4254-4324  WebSocket               com.dspread.mdm.service              I  🔧 已发送C0000响应确认: requestId=1755494713915ST001
2025-08-18 13:25:15.462  4254-4324  Task                    com.dspread.mdm.service              D  🔧 处理任务消息: ST001
2025-08-18 13:25:15.471  4254-4324  Task                    com.dspread.mdm.service              I  ℹ️ 更新任务列表: requestId=1755494713915ST001, 任务数量=1
2025-08-18 13:25:15.482  4254-4324  Task                    com.dspread.mdm.service              D  🔧 添加/更新任务: taskId=79b1560a8eb14c5c958b49e8521f3624&175f338e396f458e8d5935cfadb98f60
2025-08-18 13:25:15.500  4254-4324  Task                    com.dspread.mdm.service              D  🔧 保存任务列表到存储: 2 个
2025-08-18 13:25:15.506  4254-4324  Task                    com.dspread.mdm.service              D  🔧 开始处理任务列表，共 1 个任务
2025-08-18 13:25:15.514  4254-4324  Task                    com.dspread.mdm.service              D  🔧 处理任务: taskId=79b1560a8eb14c5c958b49e8521f3624&175f338e396f458e8d5935cfadb98f60, type=04, package=, apk=
2025-08-18 13:25:15.521  4254-4324  Task                    com.dspread.mdm.service              D  🔧 处理OS更新任务: 79b1560a8eb14c5c958b49e8521f3624&175f338e396f458e8d5935cfadb98f60
2025-08-18 13:25:15.529  4254-4324  Task                    com.dspread.mdm.service              D  🔧 执行OS更新任务: 79b1560a8eb14c5c958b49e8521f3624&175f338e396f458e8d5935cfadb98f60
2025-08-18 13:25:15.536  4254-4324  Task                    com.dspread.mdm.service              D  🔧 目标版本: MU_GEN_AP_V1.0.8_202507031912-BASE_D30M-MU_GEN_AP_V1.0.8.1_202507032000_ota_test, 文件: e0369aa3315b48648f5354907bf40eef.zip
2025-08-18 13:25:15.543  4254-4324  Task                    com.dspread.mdm.service              D  🔧 Android 14+设备，执行升级前检查...
2025-08-18 13:25:15.568  4254-4324  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineManager UpdateEngine初始化成功 (Android 34)
2025-08-18 13:25:15.579  4254-4324  Platform                com.dspread.mdm.service              I  ℹ️ SystemUpdateApi SystemUpdateApi initialized for Android 34
2025-08-18 13:25:15.588  4254-4324  Platform                com.dspread.mdm.service              I  ℹ️ SystemUpdateApi 创建更新临时目录: /sdcard/Android/data/com.dspread.mdm.service/files/updates
2025-08-18 13:25:15.596  4254-4324  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineUpgradeStrategy Android 14+ 强制支持UpdateEngine
2025-08-18 13:25:15.645  4254-4324  Platform                com.dspread.mdm.service              I  ℹ️ RecoverySystemUpgradeStrategy RecoverySystem支持检查: false (Android 34, A/B分区: true)
2025-08-18 13:25:15.694  4254-4324  Platform                com.dspread.mdm.service              I  ℹ️ SystemUpdateApi 升级策略支持状态: {android_version=34, android_release=14, update_engine_supported=true, recovery_system_supported=false, ab_partition=true, current_slot=_a, device_model=D30, device_manufacturer=Dspread}
2025-08-18 13:25:15.702  4254-4324  Task                    com.dspread.mdm.service              D  🔧 SystemUpdateApi已初始化
2025-08-18 13:25:15.711  4254-4324  Task                    com.dspread.mdm.service              D  🔧 注意：Android 14+对升级包时间戳有严格要求
2025-08-18 13:25:15.719  4254-4324  Task                    com.dspread.mdm.service              D  🔧 如果升级失败，可能需要更新的升级包
2025-08-18 13:25:15.727  4254-4324  Task                    com.dspread.mdm.service              D  🔧 开始OS升级流程: 79b1560a8eb14c5c958b49e8521f3624&175f338e396f458e8d5935cfadb98f60
2025-08-18 13:25:15.740  4254-4324  Task                    com.dspread.mdm.service              D  🔧 更新任务状态: taskId=79b1560a8eb14c5c958b49e8521f3624&175f338e396f458e8d5935cfadb98f60, state=A01
2025-08-18 13:25:15.757  4254-4324  Task                    com.dspread.mdm.service              D  🔧 保存任务列表到存储: 2 个
2025-08-18 13:25:15.768  4254-4324  Task                    com.dspread.mdm.service              D  🔧 更新任务错误信息: taskId=79b1560a8eb14c5c958b49e8521f3624&175f338e396f458e8d5935cfadb98f60
2025-08-18 13:25:15.787  4254-4324  Task                    com.dspread.mdm.service              D  🔧 保存任务列表到存储: 2 个
2025-08-18 13:25:15.809  4254-4324  Task                    com.dspread.mdm.service              D  🔧 保存任务列表到存储: 2 个
2025-08-18 13:25:15.817  4254-4324  WebSocket               com.dspread.mdm.service              I  🔧 ST001 主动式上送: task_failed (主动: 5)
2025-08-18 13:25:15.854  4254-4324  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0108","serialNo":"01354090202503050399","request_time":"1755494715834","request_id":"1755494715834C0108","version":"1","data":{"taskId":"79b1560a8eb14c5c958b49e8521f3624&175f338e396f458e8d5935cfadb98f60","taskResult":"A01","appId":"","errorMsg":"开始下载升级包"},"myVersionName":"1.0.10.20250818.DSPREAD.MDM.SERVICE","terminalDate":"20250818132515","org_request_id":"1755494713915ST001","org_request_time":"1755494713915"}
2025-08-18 13:25:15.862  4254-4324  WebSocket               com.dspread.mdm.service              I  🔧 任务结果实时上送: taskId=79b1560a8eb14c5c958b49e8521f3624&175f338e396f458e8d5935cfadb98f60, result=A01 (1)
2025-08-18 13:25:15.873  4254-4283  Task                    com.dspread.mdm.service              D  🔧 开始下载OS升级文件: https://siot-bucket01.s3.ap-northeast-1.amazonaws.com/os/upload/e0369aa3315b48648f5354907bf40eef.zip -> /sdcard/Android/data/com.dspread.mdm.service/files/updates/e0369aa3315b48648f5354907bf40eef.zip
2025-08-18 13:25:15.882  4254-4283  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 尝试下载，第1次
2025-08-18 13:25:15.891  4254-4283  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 开始下载
2025-08-18 13:25:15.898  4254-4283  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader URL: https://siot-bucket01.s3.ap-northeast-1.amazonaws.com/os/upload/e0369aa3315b48648f5354907bf40eef.zip
2025-08-18 13:25:15.906  4254-4283  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 路径: /sdcard/Android/data/com.dspread.mdm.service/files/updates/e0369aa3315b48648f5354907bf40eef.zip
2025-08-18 13:25:15.916  4254-4283  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 本地文件不存在，创建文件
2025-08-18 13:25:15.925  4254-4283  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader allFileLength=2820528, 需要从服务器获取文件大小
2025-08-18 13:25:16.052  4254-4283  TrafficStats            com.dspread.mdm.service              D  tagSocket(108) with statsTag=0xffffffff, statsUid=-1
2025-08-18 13:25:16.228  4254-4324  Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"request_time":"1755494715034","org_request_time":"1755494715834","org_request_id":"1755494715834C0108","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755494715034S0000","serialNo":"01354090202503050399"}
2025-08-18 13:25:16.240  4254-4324  WebSocket               com.dspread.mdm.service              I  🔧 收到响应消息: transCode=S0000, orgRequestId=1755494715834C0108, state=0, remark=
2025-08-18 13:25:16.247  4254-4324  WebSocket               com.dspread.mdm.service              I  🔧 处理任务结果响应: state=0
2025-08-18 13:25:16.255  4254-4324  WebSocket               com.dspread.mdm.service              I  🔧 任务结果上传成功
2025-08-18 13:25:16.958  4254-4283  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader HTTP响应码: 200
2025-08-18 13:25:16.966  4254-4283  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 服务器返回Content-Length: 2820528
2025-08-18 13:25:16.974  4254-4283  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 完整下载模式
2025-08-18 13:25:16.981  4254-4283  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 使用调用方提供的文件大小: 2820528
2025-08-18 13:25:16.989  4254-4283  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader localFile = true /sdcard/Android/data/com.dspread.mdm.service/files/updates/e0369aa3315b48648f5354907bf40eef.zip, 追加模式: false
2025-08-18 13:25:17.001  4254-4283  Task                    com.dspread.mdm.service              D  🔧 OS下载进度: 0%
2025-08-18 13:25:17.674  4254-4283  Task                    com.dspread.mdm.service              D  🔧 OS下载进度: 10%
2025-08-18 13:25:18.241  4254-4283  Task                    com.dspread.mdm.service              D  🔧 OS下载进度: 20%
2025-08-18 13:25:18.747  4254-4283  Task                    com.dspread.mdm.service              D  🔧 OS下载进度: 30%
2025-08-18 13:25:19.147  4254-4283  Task                    com.dspread.mdm.service              D  🔧 OS下载进度: 40%
2025-08-18 13:25:19.508  4254-4283  Task                    com.dspread.mdm.service              D  🔧 OS下载进度: 50%
2025-08-18 13:25:19.877  4254-4283  Task                    com.dspread.mdm.service              D  🔧 OS下载进度: 60%
2025-08-18 13:25:20.074  4254-4254  Receiver                com.dspread.mdm.service              D  🔧 屏幕点亮
2025-08-18 13:25:20.085  4254-4254  WebSocket               com.dspread.mdm.service              W  ⚠️ 未知触发条件: screen_on，默认按被动式处理
2025-08-18 13:25:20.139  4254-4254  WebSocket               com.dspread.mdm.service              I  🔧 C0201 被动式上送拒绝: screen_on (拒绝: 2)
2025-08-18 13:25:20.154  4254-4254  WebSocket               com.dspread.mdm.service              I  🔧 C0201 设备状态上送被流量控制阻止: 被动事件 'screen_on' 在平衡模式 - 重要变化下未启用
2025-08-18 13:25:20.162  4254-4254  Receiver                com.dspread.mdm.service              D  🔧 屏幕点亮处理完成
2025-08-18 13:25:20.221  4254-4283  Task                    com.dspread.mdm.service              D  🔧 OS下载进度: 70%
2025-08-18 13:25:20.572  4254-4283  Task                    com.dspread.mdm.service              D  🔧 OS下载进度: 80%
2025-08-18 13:25:20.810  4254-4283  Task                    com.dspread.mdm.service              D  🔧 OS下载进度: 90%
2025-08-18 13:25:21.157  4254-4283  Task                    com.dspread.mdm.service              D  🔧 OS下载进度: 100%
2025-08-18 13:25:21.165  4254-4283  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 计算MD5: e0369aa3315b48648f5354907bf40eef.zip
2025-08-18 13:25:21.210  4254-4283  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 文件: 2820528 MD5: 5350f88fbd2626b07917b165b24ec7d8
2025-08-18 13:25:21.217  4254-4283  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader MD5校验正确
2025-08-18 13:25:21.223  4254-4283  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 清理连接资源
2025-08-18 13:25:21.230  4254-4283  Task                    com.dspread.mdm.service              D  🔧 OS升级文件下载成功: /sdcard/Android/data/com.dspread.mdm.service/files/updates/e0369aa3315b48648f5354907bf40eef.zip
2025-08-18 13:25:21.237  4254-4283  Task                    com.dspread.mdm.service              D  🔧 OS升级文件下载完成: /sdcard/Android/data/com.dspread.mdm.service/files/updates/e0369aa3315b48648f5354907bf40eef.zip
2025-08-18 13:25:21.289  4254-4283  Task                    com.dspread.mdm.service              D  🔧 文件MD5验证: 期望=5350f88fbd2626b07917b165b24ec7d8, 实际=5350f88fbd2626b07917b165b24ec7d8, 结果=通过
2025-08-18 13:25:21.300  4254-4283  Task                    com.dspread.mdm.service              D  🔧 更新任务状态: taskId=79b1560a8eb14c5c958b49e8521f3624&175f338e396f458e8d5935cfadb98f60, state=A03
2025-08-18 13:25:21.319  4254-4283  Task                    com.dspread.mdm.service              D  🔧 保存任务列表到存储: 2 个
2025-08-18 13:25:21.329  4254-4283  Task                    com.dspread.mdm.service              D  🔧 更新任务错误信息: taskId=79b1560a8eb14c5c958b49e8521f3624&175f338e396f458e8d5935cfadb98f60
2025-08-18 13:25:21.348  4254-4283  Task                    com.dspread.mdm.service              D  🔧 保存任务列表到存储: 2 个
2025-08-18 13:25:21.371  4254-4283  Task                    com.dspread.mdm.service              D  🔧 保存任务列表到存储: 2 个
2025-08-18 13:25:21.377  4254-4283  WebSocket               com.dspread.mdm.service              I  🔧 ST001 主动式上送: task_failed (主动: 6)
2025-08-18 13:25:21.411  4254-4283  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0108","serialNo":"01354090202503050399","request_time":"1755494721392","request_id":"1755494721392C0108","version":"1","data":{"taskId":"79b1560a8eb14c5c958b49e8521f3624&175f338e396f458e8d5935cfadb98f60","taskResult":"A03","appId":"","errorMsg":"下载完成"},"myVersionName":"1.0.10.20250818.DSPREAD.MDM.SERVICE","terminalDate":"20250818132521","org_request_id":"1755494713915ST001","org_request_time":"1755494713915"}
2025-08-18 13:25:21.418  4254-4283  WebSocket               com.dspread.mdm.service              I  🔧 任务结果实时上送: taskId=79b1560a8eb14c5c958b49e8521f3624&175f338e396f458e8d5935cfadb98f60, result=A03 (1)
2025-08-18 13:25:21.426  4254-4283  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineManager UpdateEngine初始化成功 (Android 34)
2025-08-18 13:25:21.433  4254-4283  Platform                com.dspread.mdm.service              I  ℹ️ SystemUpdateApi SystemUpdateApi initialized for Android 34
2025-08-18 13:25:21.440  4254-4283  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineUpgradeStrategy Android 14+ 强制支持UpdateEngine
2025-08-18 13:25:21.480  4254-4283  Platform                com.dspread.mdm.service              I  ℹ️ RecoverySystemUpgradeStrategy RecoverySystem支持检查: false (Android 34, A/B分区: true)
2025-08-18 13:25:21.524  4254-4283  Platform                com.dspread.mdm.service              I  ℹ️ SystemUpdateApi 升级策略支持状态: {android_version=34, android_release=14, update_engine_supported=true, recovery_system_supported=false, ab_partition=true, current_slot=_a, device_model=D30, device_manufacturer=Dspread}
2025-08-18 13:25:21.535  4254-4283  Platform                com.dspread.mdm.service              I  ℹ️ SystemUpdateApi 开始分析升级包: e0369aa3315b48648f5354907bf40eef.zip
2025-08-18 13:25:21.544  4254-4283  Platform                com.dspread.mdm.service              I  ℹ️ SystemUpdateApi 检测到A/B升级包格式
2025-08-18 13:25:21.559  4254-4283  Platform                com.dspread.mdm.service              I  ℹ️ SystemUpdateApi 从build信息提取版本: 1.0.8.1_202507032000
2025-08-18 13:25:21.566  4254-4283  Platform                com.dspread.mdm.service              I  ℹ️ SystemUpdateApi A14+格式提取版本: 1.0.8.1_202507032000
2025-08-18 13:25:21.574  4254-4283  Platform                com.dspread.mdm.service              I  ℹ️ SystemUpdateApi 版本比较: 当前=BASE_D30M-MU_GEN_AP_V1.0.8_202507031912, 升级包=1.0.8.1_202507032000
2025-08-18 13:25:21.581  4254-4283  Platform                com.dspread.mdm.service              I  ℹ️ SystemUpdateApi 详细版本比较: 'BASE_D30M-MU_GEN_AP_V1.0.8_202507031912' vs '1.0.8.1_202507032000'
2025-08-18 13:25:21.587  4254-4283  Platform                com.dspread.mdm.service              I  ℹ️ SystemUpdateApi 清理版本字符串: 'BASE_D30M-MU_GEN_AP_V1.0.8_202507031912'
2025-08-18 13:25:21.595  4254-4283  Platform                com.dspread.mdm.service              I  ℹ️ SystemUpdateApi 提取完整版本: '1.0.8_202507031912'
2025-08-18 13:25:21.602  4254-4283  Platform                com.dspread.mdm.service              I  ℹ️ SystemUpdateApi 清理版本字符串: '1.0.8.1_202507032000'
2025-08-18 13:25:21.609  4254-4283  Platform                com.dspread.mdm.service              I  ℹ️ SystemUpdateApi 提取完整版本: '1.0.8.1_202507032000'
2025-08-18 13:25:21.616  4254-4283  Platform                com.dspread.mdm.service              I  ℹ️ SystemUpdateApi 清理后版本: '1.0.8_202507031912' vs '1.0.8.1_202507032000'
2025-08-18 13:25:21.625  4254-4283  Platform                com.dspread.mdm.service              I  ℹ️ SystemUpdateApi 核心版本: '1.0.8' vs '1.0.8.1'
2025-08-18 13:25:21.638  4254-4283  Platform                com.dspread.mdm.service              I  ℹ️ SystemUpdateApi 版本数字: [1, 0, 8, 0] vs [1, 0, 8, 1, 0]
2025-08-18 13:25:21.644  4254-4283  Platform                com.dspread.mdm.service              I  ℹ️ SystemUpdateApi 版本1更旧: 0 < 1
2025-08-18 13:25:21.652  4254-4283  Task                    com.dspread.mdm.service              D  🔧 版本兼容性检查: taskId=79b1560a8eb14c5c958b49e8521f3624&175f338e396f458e8d5935cfadb98f60
2025-08-18 13:25:21.658  4254-4283  Task                    com.dspread.mdm.service              D  🔧 当前版本: BASE_D30M-MU_GEN_AP_V1.0.8_202507031912
2025-08-18 13:25:21.665  4254-4283  Task                    com.dspread.mdm.service              D  🔧 升级包版本: 1.0.8.1_202507032000
2025-08-18 13:25:21.671  4254-4283  Task                    com.dspread.mdm.service              D  🔧 目标版本: MU_GEN_AP_V1.0.8_202507031912-BASE_D30M-MU_GEN_AP_V1.0.8.1_202507032000_ota_test
2025-08-18 13:25:21.678  4254-4283  Task                    com.dspread.mdm.service              D  🔧 检查状态: NEWER_VERSION
2025-08-18 13:25:21.685  4254-4283  Task                    com.dspread.mdm.service              D  🔧 升级包版本高于当前系统版本，正常升级
2025-08-18 13:25:21.691  4254-4324  Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"request_time":"1755494720492","org_request_time":"1755494721392","org_request_id":"1755494721392C0108","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755494720492S0000","serialNo":"01354090202503050399"}
2025-08-18 13:25:21.695  4254-4254  Platform                com.dspread.mdm.service              I  ℹ️ OsUpgradeDialog 显示OS升级弹框
2025-08-18 13:25:21.701  4254-4324  WebSocket               com.dspread.mdm.service              I  🔧 收到响应消息: transCode=S0000, orgRequestId=1755494721392C0108, state=0, remark=
2025-08-18 13:25:21.708  4254-4324  WebSocket               com.dspread.mdm.service              I  🔧 处理任务结果响应: state=0
2025-08-18 13:25:21.714  4254-4324  WebSocket               com.dspread.mdm.service              I  🔧 任务结果上传成功
2025-08-18 13:25:21.794  4254-4254  VRI[]                   com.dspread.mdm.service              D  hardware acceleration = true, forceHwAccelerated = false
2025-08-18 13:25:21.801   781-1017  BufferQueueDebug        surfaceflinger                       E  [f5285a0 com.dspread.mdm.service#382](this:0xa5feac40,id:-1,api:0,p:-1,c:-1) id info cannot be read from 'f5285a0 com.dspread.mdm.service#382'
2025-08-18 13:25:21.819  4254-4254  InputTransport          com.dspread.mdm.service              D  Create ARC handle: 0xb3963480
2025-08-18 13:25:21.819  4254-4254  InputEventReceiver      com.dspread.mdm.service              D  Input log is disabled in InputEventReceiver.
2025-08-18 13:25:21.819  4254-4254  InputTransport          com.dspread.mdm.service              D  Input log is disabled in InputChannel.
2025-08-18 13:25:21.913  4254-4254  BufferQueueConsumer     com.dspread.mdm.service              D  [](id:109e00000001,api:0,p:-1,c:4254) connect: controlledByApp=false
2025-08-18 13:25:21.919  4254-4288  OpenGLRenderer          com.dspread.mdm.service              E  Unable to match the desired swap behavior.
2025-08-18 13:25:22.020  4254-4288  BLASTBufferQueue        com.dspread.mdm.service              D  [VRI[]#1](f:0,a:1) acquireNextBufferLocked size=566x602 mFrameNumber=1 applyTransaction=true mTimestamp=261239745784(auto) mPendingTransactions.size=0 graphicBufferId=18270790877199 transform=3
2025-08-18 13:25:22.575  4254-4254  getAnimDeal             com.dspread.mdm.service              I  mode:3 packageName:com.dspread.mdm.service result: false
2025-08-18 13:25:22.596   781-1017  BufferQueueDebug        surfaceflinger                       E  [com.dspread.mdm.service/com.dspread.mdm.service.ui.activity.TestActivity#387](this:0xa5f6cc40,id:-1,api:0,p:-1,c:-1) id info cannot be read from 'com.dspread.mdm.service/com.dspread.mdm.service.ui.activity.TestActivity#387'
2025-08-18 13:25:22.625   781-1017  BufferQueueDebug        surfaceflinger                       E  [186c371 ActivityRecordInputSink com.dspread.mdm.service/.ui.activity.TestActivity#389](this:0xa5f63c40,id:-1,api:0,p:-1,c:-1) id info cannot be read from '186c371 ActivityRecordInputSink com.dspread.mdm.service/.ui.activity.TestActivity#389'
2025-08-18 13:25:22.626  4254-4254  BufferQueueConsumer     com.dspread.mdm.service              D  [](id:109e00000002,api:0,p:-1,c:4254) connect: controlledByApp=false
2025-08-18 13:25:22.643  4254-4288  OpenGLRenderer          com.dspread.mdm.service              E  Unable to match the desired swap behavior.
2025-08-18 13:25:22.712  4254-4288  BLASTBufferQueue        com.dspread.mdm.service              D  [VRI[TestActivity]#2](f:0,a:1) acquireNextBufferLocked size=480x854 mFrameNumber=1 applyTransaction=true mTimestamp=261931365784(auto) mPendingTransactions.size=0 graphicBufferId=18270790877205 transform=3
2025-08-18 13:25:22.746   781-1418  BufferQueueDebug        surfaceflinger                       E  [Surface(name=f5285a0 com.dspread.mdm.service)/@0x1630685 - animation-leash of window_animation#391](this:0xa5f5ec40,id:-1,api:0,p:-1,c:-1) id info cannot be read from 'Surface(name=f5285a0 com.dspread.mdm.service)/@0x1630685 - animation-leash of window_animation#391'
2025-08-18 13:25:22.832   781-1017  BufferQueueDebug        surfaceflinger                       E  [Surface(name=f5285a0 com.dspread.mdm.service)/@0x1630685 - animation-leash of window_animation#394](this:0xa5f4ec40,id:-1,api:0,p:-1,c:-1) id info cannot be read from 'Surface(name=f5285a0 com.dspread.mdm.service)/@0x1630685 - animation-leash of window_animation#394'
2025-08-18 13:25:25.352  4254-4254  Platform                com.dspread.mdm.service              I  ℹ️ OsUpgradeDialog 用户选择立即升级
2025-08-18 13:25:25.360  4254-4254  WindowOnBackDispatcher  com.dspread.mdm.service              W  sendCancelIfRunning: isInProgress=falsecallback=android.view.ViewRootImpl$$ExternalSyntheticLambda19@20c4545
2025-08-18 13:25:25.372  4254-4254  View                    com.dspread.mdm.service              D  [Warning] assignParent to null: this = DecorView@516f0d8[]
2025-08-18 13:25:25.372  4254-4254  BLASTBufferQueue        com.dspread.mdm.service              D  [VRI[]#1](f:0,a:1) destructor()
2025-08-18 13:25:25.373  4254-4254  BufferQueueConsumer     com.dspread.mdm.service              D  [VRI[]#1(BLAST Consumer)1](id:109e00000001,api:0,p:-1,c:4254) disconnect
2025-08-18 13:25:25.378   781-1418  BufferQueueDebug        surfaceflinger                       E  [Surface(name=f5285a0 com.dspread.mdm.service)/@0x1630685 - animation-leash of window_animation#399](this:0xa5fa0c40,id:-1,api:0,p:-1,c:-1) id info cannot be read from 'Surface(name=f5285a0 com.dspread.mdm.service)/@0x1630685 - animation-leash of window_animation#399'
2025-08-18 13:25:25.397  4254-4254  InputTransport          com.dspread.mdm.service              D  Destroy ARC handle: 0xb3963480
2025-08-18 13:25:25.402  4254-4254  Task                    com.dspread.mdm.service              D  🔧 用户选择立即升级
2025-08-18 13:25:25.419  4254-4283  Task                    com.dspread.mdm.service              D  🔧 开始执行OS升级: 79b1560a8eb14c5c958b49e8521f3624&175f338e396f458e8d5935cfadb98f60, 目标版本: MU_GEN_AP_V1.0.8_202507031912-BASE_D30M-MU_GEN_AP_V1.0.8.1_202507032000_ota_test
2025-08-18 13:25:25.438  4254-4283  Common                  com.dspread.mdm.service              D  🔧 OsUpdateStatusChecker 保存OS升级任务信息: 79b1560a8eb14c5c958b49e8521f3624&175f338e396f458e8d5935cfadb98f60, 目标版本: MU_GEN_AP_V1.0.8_202507031912-BASE_D30M-MU_GEN_AP_V1.0.8.1_202507032000_ota_test
2025-08-18 13:25:25.460  4254-4283  Common                  com.dspread.mdm.service              D  🔧 OsUpdateStatusChecker 保存升级前版本: BASE_D30M-MU_GEN_AP_V1.0.8_202507031912
2025-08-18 13:25:25.487  4254-4283  Task                    com.dspread.mdm.service              D  🔧 更新任务状态: taskId=79b1560a8eb14c5c958b49e8521f3624&175f338e396f458e8d5935cfadb98f60, state=C02
2025-08-18 13:25:25.521  4254-4283  Task                    com.dspread.mdm.service              D  🔧 保存任务列表到存储: 2 个
2025-08-18 13:25:25.536  4254-4283  Task                    com.dspread.mdm.service              D  🔧 更新任务错误信息: taskId=79b1560a8eb14c5c958b49e8521f3624&175f338e396f458e8d5935cfadb98f60
2025-08-18 13:25:25.560  4254-4283  Task                    com.dspread.mdm.service              D  🔧 保存任务列表到存储: 2 个
2025-08-18 13:25:25.691  4254-4283  Task                    com.dspread.mdm.service              D  🔧 保存任务列表到存储: 2 个
2025-08-18 13:25:25.697  4254-4283  WebSocket               com.dspread.mdm.service              I  🔧 ST001 主动式上送: task_failed (主动: 7)
2025-08-18 13:25:25.731  4254-4283  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0108","serialNo":"01354090202503050399","request_time":"1755494725711","request_id":"1755494725711C0108","version":"1","data":{"taskId":"79b1560a8eb14c5c958b49e8521f3624&175f338e396f458e8d5935cfadb98f60","taskResult":"C02","appId":"","errorMsg":"开始升级"},"myVersionName":"1.0.10.20250818.DSPREAD.MDM.SERVICE","terminalDate":"20250818132525","org_request_id":"1755494713915ST001","org_request_time":"1755494713915"}
2025-08-18 13:25:25.737  4254-4283  WebSocket               com.dspread.mdm.service              I  🔧 任务结果实时上送: taskId=79b1560a8eb14c5c958b49e8521f3624&175f338e396f458e8d5935cfadb98f60, result=C02 (1)
2025-08-18 13:25:25.744  4254-4283  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineManager UpdateEngine初始化成功 (Android 34)
2025-08-18 13:25:25.750  4254-4283  Platform                com.dspread.mdm.service              I  ℹ️ SystemUpdateApi SystemUpdateApi initialized for Android 34
2025-08-18 13:25:25.756  4254-4283  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineUpgradeStrategy Android 14+ 强制支持UpdateEngine
2025-08-18 13:25:25.797  4254-4283  Platform                com.dspread.mdm.service              I  ℹ️ RecoverySystemUpgradeStrategy RecoverySystem支持检查: false (Android 34, A/B分区: true)
2025-08-18 13:25:25.839  4254-4283  Platform                com.dspread.mdm.service              I  ℹ️ SystemUpdateApi 升级策略支持状态: {android_version=34, android_release=14, update_engine_supported=true, recovery_system_supported=false, ab_partition=true, current_slot=_a, device_model=D30, device_manufacturer=Dspread}
2025-08-18 13:25:25.845  4254-4283  Platform                com.dspread.mdm.service              I  ℹ️ SystemUpdateApi 开始安装OTA更新: /sdcard/Android/data/com.dspread.mdm.service/files/updates/e0369aa3315b48648f5354907bf40eef.zip
2025-08-18 13:25:25.853  4254-4283  Platform                com.dspread.mdm.service              I  ℹ️ SystemUpdateApi 执行升级前系统检查
2025-08-18 13:25:25.858  4254-4283  Platform                com.dspread.mdm.service              I  ℹ️ SystemUpdateApi Android 14+设备，检查并重置UpdateEngine状态
2025-08-18 13:25:25.864  4254-4283  Platform                com.dspread.mdm.service              I  ℹ️ SystemUpdateApi 尝试重置UpdateEngine状态
2025-08-18 13:25:25.870  4254-4283  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineManager UpdateEngine初始化成功 (Android 34)
2025-08-18 13:25:25.875  4254-4283  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineManager 尝试重置UpdateEngine状态
2025-08-18 13:25:25.881  4254-4324  Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"request_time":"1755494724811","org_request_time":"1755494725711","org_request_id":"1755494725711C0108","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755494724811S0000","serialNo":"01354090202503050399"}
2025-08-18 13:25:25.889  4254-4283  Platform                com.dspread.mdm.service              W  ⚠️ UpdateEngineManager cancel方法调用失败: null
2025-08-18 13:25:25.890  4254-4324  WebSocket               com.dspread.mdm.service              I  🔧 收到响应消息: transCode=S0000, orgRequestId=1755494725711C0108, state=0, remark=
2025-08-18 13:25:25.895  4254-4324  WebSocket               com.dspread.mdm.service              I  🔧 处理任务结果响应: state=0
2025-08-18 13:25:25.900  4254-4324  WebSocket               com.dspread.mdm.service              I  🔧 任务结果上传成功
2025-08-18 13:25:26.961  4254-4283  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineManager 通过resetStatus方法重置成功
2025-08-18 13:25:26.968  4254-4283  Platform                com.dspread.mdm.service              I  ℹ️ SystemUpdateApi UpdateEngine状态重置成功
2025-08-18 13:25:27.986  4254-4283  Platform                com.dspread.mdm.service              I  ℹ️ SystemUpdateApi 升级前检查通过
2025-08-18 13:25:27.995  4254-4283  Platform                com.dspread.mdm.service              I  ℹ️ UpgradeStrategyFactory 分析升级策略...
2025-08-18 13:25:28.003  4254-4283  Platform                com.dspread.mdm.service              I  ℹ️ UpgradeStrategyFactory Android版本: 34 (14)
2025-08-18 13:25:28.011  4254-4283  Platform                com.dspread.mdm.service              I  ℹ️ UpgradeStrategyFactory 更新文件: e0369aa3315b48648f5354907bf40eef.zip
2025-08-18 13:25:28.026  4254-4283  Platform                com.dspread.mdm.service              I  ℹ️ UpgradeStrategyFactory 文件格式 - Payload: true, ZIP: true
2025-08-18 13:25:28.035  4254-4283  Platform                com.dspread.mdm.service              I  ℹ️ UpgradeStrategyFactory 选择升级策略: UpdateEngine (Android 14+)
2025-08-18 13:25:28.043  4254-4283  Platform                com.dspread.mdm.service              I  ℹ️ UpgradeStrategyFactory 策略描述: Android 14+ 使用UpdateEngine进行A/B分区升级
2025-08-18 13:25:28.050  4254-4283  Platform                com.dspread.mdm.service              I  ℹ️ UpgradeStrategyFactory 支持状态: true
2025-08-18 13:25:28.057  4254-4283  Platform                com.dspread.mdm.service              I  ℹ️ UpgradeStrategyFactory 选择原因: Android 14+ 强制要求使用UpdateEngine
2025-08-18 13:25:28.064  4254-4283  Platform                com.dspread.mdm.service              I  ℹ️ UpgradeStrategyFactory 使用UpdateEngine策略执行升级
2025-08-18 13:25:28.070  4254-4283  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineUpgradeStrategy 开始UpdateEngine升级: /sdcard/Android/data/com.dspread.mdm.service/files/updates/e0369aa3315b48648f5354907bf40eef.zip
2025-08-18 13:25:28.076  4254-4283  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineUpgradeStrategy Android版本: 34 (14)
2025-08-18 13:25:28.085  4254-4283  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineUpgradeStrategy OTA包格式检查: Payload格式
2025-08-18 13:25:28.090  4254-4283  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineUpgradeStrategy 检测到ZIP格式，提取payload.bin
2025-08-18 13:25:28.142  4254-4283  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineUpgradeStrategy 成功提取payload.bin: /data/user/0/com.dspread.mdm.service/cache/ota_extract/payload.bin
2025-08-18 13:25:28.149  4254-4283  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineUpgradeStrategy 成功提取payload_properties.txt: /data/user/0/com.dspread.mdm.service/cache/ota_extract/payload_properties.txt
2025-08-18 13:25:28.228  4254-4283  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineUpgradeStrategy 文件复制成功: /data/ota_package/payload.bin
2025-08-18 13:25:28.233  4254-4283  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineManager 开始应用UpdateEngine payload
2025-08-18 13:25:28.238  4254-4283  Platform                com.dspread.mdm.service              W  ⚠️ UpdateEngineManager UpdateEngineCallback回调异常，启动增强监控
2025-08-18 13:25:28.244  4254-4283  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineManager 启动UpdateEngine增强监控
2025-08-18 13:25:28.249  4254-4283  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineManager 启动系统属性监控
2025-08-18 13:25:28.254  4254-4283  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineManager 启动文件系统监控
2025-08-18 13:25:28.261  4254-4373  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineManager 检测到文件系统活动: /data/ota_package
2025-08-18 13:25:28.262  4254-4283  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineManager 开始读取payload参数，文件: /data/ota_package/payload.bin
2025-08-18 13:25:28.267  4254-4283  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineManager 查找payload_properties.txt:
2025-08-18 13:25:28.260  4254-4254  Thread-12               com.dspread.mdm.service              W  type=1400 audit(0.0:187): avc:  denied  { getattr } for  path="/data/cache/recovery" dev="mmcblk0p47" ino=193 scontext=u:r:system_app:s0 tcontext=u:object_r:cache_recovery_file:s0 tclass=dir permissive=0
2025-08-18 13:25:28.260  4254-4254  Thread-12               com.dspread.mdm.service              W  type=1400 audit(0.0:188): avc:  denied  { getattr } for  path="/dev/block/platform/bootdevice/by-name" dev="tmpfs" ino=7300 scontext=u:r:system_app:s0 tcontext=u:object_r:block_device:s0 tclass=dir permissive=0
2025-08-18 13:25:28.273  4254-4283  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineManager   payload文件: /data/ota_package/payload.bin
2025-08-18 13:25:28.279  4254-4283  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineManager   查找目录: /data/ota_package
2025-08-18 13:25:28.284  4254-4283  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineManager   properties文件: /data/ota_package/payload_properties.txt
2025-08-18 13:25:28.291  4254-4283  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineManager   文件存在: false
2025-08-18 13:25:28.297  4254-4283  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineManager 尝试缓存目录:
2025-08-18 13:25:28.303  4254-4283  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineManager   缓存目录: /data/user/0/com.dspread.mdm.service/cache/ota_extract
2025-08-18 13:25:28.309  4254-4283  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineManager   缓存properties文件: /data/user/0/com.dspread.mdm.service/cache/ota_extract/payload_properties.txt
2025-08-18 13:25:28.314  4254-4283  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineManager   缓存文件存在: true
2025-08-18 13:25:28.320  4254-4283  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineManager 找到payload_properties.txt: /data/user/0/com.dspread.mdm.service/cache/ota_extract/payload_properties.txt
2025-08-18 13:25:28.326  4254-4283  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineManager payload_properties.txt内容:
2025-08-18 13:25:28.338  4254-4283  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineManager   FILE_HASH=x/53I9LRkm74Z6EgFM4uEbarni/VIyXAskIT6jq6ReI=
2025-08-18 13:25:28.344  4254-4283  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineManager   FILE_SIZE=2812788
2025-08-18 13:25:28.349  4254-4283  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineManager   METADATA_HASH=PfJ/2VVj97IDNB3F0lTB3qqAjJHzWVRB19Eb9jMR/xQ=
2025-08-18 13:25:28.354  4254-4283  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineManager   METADATA_SIZE=71613
2025-08-18 13:25:28.366  4254-4283  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineManager 解析得到的参数:
2025-08-18 13:25:28.371  4254-4283  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineManager   FILE_HASH=x/53I9LRkm74Z6EgFM4uEbarni/VIyXAskIT6jq6ReI=
2025-08-18 13:25:28.376  4254-4283  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineManager   FILE_SIZE=2812788
2025-08-18 13:25:28.382  4254-4283  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineManager   METADATA_HASH=PfJ/2VVj97IDNB3F0lTB3qqAjJHzWVRB19Eb9jMR/xQ=
2025-08-18 13:25:28.387  4254-4283  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineManager   METADATA_SIZE=71613
2025-08-18 13:25:28.392  4254-4283  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineManager   SWITCH_SLOT_ON_REBOOT=1
2025-08-18 13:25:28.397  4254-4283  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineManager 从payload_properties.txt读取参数完成
2025-08-18 13:25:28.402  4254-4283  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineManager 最终headerKeyValuePairs:
2025-08-18 13:25:28.407  4254-4283  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineManager   [0] FILE_HASH=x/53I9LRkm74Z6EgFM4uEbarni/VIyXAskIT6jq6ReI=
2025-08-18 13:25:28.412  4254-4283  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineManager   [1] FILE_SIZE=2812788
2025-08-18 13:25:28.418  4254-4283  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineManager   [2] METADATA_HASH=PfJ/2VVj97IDNB3F0lTB3qqAjJHzWVRB19Eb9jMR/xQ=
2025-08-18 13:25:28.423  4254-4283  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineManager   [3] METADATA_SIZE=71613
2025-08-18 13:25:28.428  4254-4283  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineManager   [4] SWITCH_SLOT_ON_REBOOT=1
2025-08-18 13:25:28.433  4254-4283  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineManager UpdateEngine参数:
2025-08-18 13:25:28.438  4254-4283  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineManager   URL: file:///data/ota_package/payload.bin
2025-08-18 13:25:28.444  4254-4283  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineManager   Size: 2812788 bytes
2025-08-18 13:25:28.449  4254-4283  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineManager   Headers: FILE_HASH=x/53I9LRkm74Z6EgFM4uEbarni/VIyXAskIT6jq6ReI=, FILE_SIZE=2812788, METADATA_HASH=PfJ/2VVj97IDNB3F0lTB3qqAjJHzWVRB19Eb9jMR/xQ=, METADATA_SIZE=71613, SWITCH_SLOT_ON_REBOOT=1
2025-08-18 13:25:28.547  4254-4283  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineManager UpdateEngine payload应用成功
2025-08-18 13:25:28.553  4254-4283  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineUpgradeStrategy UpdateEngine升级已启动
2025-08-18 13:25:28.558  4254-4283  Platform                com.dspread.mdm.service              I  ℹ️ SystemUpdateApi 升级执行结果: 成功
2025-08-18 13:25:30.262  4254-4374  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineManager 启动改进的日志监控
2025-08-18 13:25:30.282  4254-4386  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineManager 日志监控开始时间: 08-18 13:25:30.272
2025-08-18 13:25:30.291  4254-4386  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineManager 开始监控UpdateEngine日志
2025-08-18 13:25:30.335  4254-4386  Platform                com.dspread.mdm.service              D  🔧 UpdateEngineManager 解析时间戳: 08-18 13:25:25.881 -> 1755494725881
2025-08-18 13:25:30.341  4254-4386  Platform                com.dspread.mdm.service              D  🔧 UpdateEngineManager 忽略历史错误日志: 08-18 13:25:25.881 E/update_engine( 1012): [ERROR:update_attempter_android.cc(-1)] unknown(...): Domain=update_engine, Code=generic_error, Message=No ongoing update to cancel.
2025-08-18 13:25:30.356  4254-4386  Platform                com.dspread.mdm.service              D  🔧 UpdateEngineManager 解析时间戳: 08-18 13:25:25.885 -> 1755494725885
2025-08-18 13:25:30.362  4254-4386  Platform                com.dspread.mdm.service              D  🔧 UpdateEngineManager 忽略历史错误日志: 08-18 13:25:25.885 E/update_engine( 1012): [ERROR:update_attempter_android.cc(94)] Replying with failure: pc:0x122b231: No ongoing update to cancel.
2025-08-18 13:25:30.382  4254-4386  Platform                com.dspread.mdm.service              D  🔧 UpdateEngineManager 解析时间戳: 08-18 13:25:26.955 -> 1755494726955
2025-08-18 13:25:30.388  4254-4386  Platform                com.dspread.mdm.service              D  🔧 UpdateEngineManager 忽略历史错误日志: 08-18 13:25:26.955 E/update_engine( 1012): [ERROR:snapshot.cpp(2929)] Read state file failed: No such file or directory
2025-08-18 13:25:30.406  4254-4386  Platform                com.dspread.mdm.service              D  🔧 UpdateEngineManager 解析时间戳: 08-18 13:25:28.495 -> 1755494728495
2025-08-18 13:25:30.412  4254-4386  Platform                com.dspread.mdm.service              D  🔧 UpdateEngineManager 忽略历史错误日志: 08-18 13:25:28.495 E/update_engine( 1012): [ERROR:snapshot.cpp(2929)] Read state file failed: No such file or directory
2025-08-18 13:25:30.454  4254-4386  Platform                com.dspread.mdm.service              D  🔧 UpdateEngineManager 解析时间戳: 08-18 13:25:28.589 -> 1755494728589
2025-08-18 13:25:30.460  4254-4386  Platform                com.dspread.mdm.service              D  🔧 UpdateEngineManager 忽略历史错误日志: 08-18 13:25:28.589 E/update_engine( 1012): [ERROR:snapshot.cpp(2929)] Read state file failed: No such file or directory
2025-08-18 13:25:30.475  4254-4386  Platform                com.dspread.mdm.service              D  🔧 UpdateEngineManager 解析时间戳: 08-18 13:25:28.593 -> 1755494728593
2025-08-18 13:25:30.482  4254-4386  Platform                com.dspread.mdm.service              D  🔧 UpdateEngineManager 忽略历史错误日志: 08-18 13:25:28.593 E/update_engine( 1012): [ERROR:snapshot.cpp(2929)] Read state file failed: No such file or directory
2025-08-18 13:25:30.497  4254-4386  Platform                com.dspread.mdm.service              D  🔧 UpdateEngineManager 解析时间戳: 08-18 13:25:28.596 -> 1755494728596
2025-08-18 13:25:30.503  4254-4386  Platform                com.dspread.mdm.service              D  🔧 UpdateEngineManager 忽略历史错误日志: 08-18 13:25:28.596 E/update_engine( 1012): [ERROR:snapshot.cpp(2929)] Read state file failed: No such file or directory
2025-08-18 13:25:30.521  4254-4386  Platform                com.dspread.mdm.service              D  🔧 UpdateEngineManager 解析时间戳: 08-18 13:25:28.608 -> 1755494728608
2025-08-18 13:25:30.527  4254-4386  Platform                com.dspread.mdm.service              D  🔧 UpdateEngineManager 忽略历史错误日志: 08-18 13:25:28.608 E/update_engine( 1012): [ERROR:snapshot.cpp(2929)] Read state file failed: No such file or directory
2025-08-18 13:25:30.587  4254-4386  Platform                com.dspread.mdm.service              D  🔧 UpdateEngineManager 解析时间戳: 08-18 13:25:28.785 -> 1755494728785
2025-08-18 13:25:30.593  4254-4386  Platform                com.dspread.mdm.service              D  🔧 UpdateEngineManager 忽略历史错误日志: 08-18 13:25:28.785 E/update_engine( 1012): [ERROR:snapshot.cpp(2929)] Read state file failed: No such file or directory
2025-08-18 13:25:30.609  4254-4386  Platform                com.dspread.mdm.service              D  🔧 UpdateEngineManager 解析时间戳: 08-18 13:25:29.796 -> 1755494729796
2025-08-18 13:25:30.615  4254-4386  Platform                com.dspread.mdm.service              D  🔧 UpdateEngineManager 忽略历史错误日志: 08-18 13:25:29.796 E/update_engine( 1012): [ERROR:snapshot.cpp(2929)] Read state file failed: No such file or directory
2025-08-18 13:25:31.437  4254-4386  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineManager 从日志解析进度: 10%
2025-08-18 13:25:31.447  4254-4254  Task                    com.dspread.mdm.service              D  🔧 升级进度: DOWNLOADING (10%)
2025-08-18 13:25:31.458  4254-4254  Task                    com.dspread.mdm.service              D  🔧 更新任务状态: taskId=79b1560a8eb14c5c958b49e8521f3624&175f338e396f458e8d5935cfadb98f60, state=C02
2025-08-18 13:25:31.458  4254-4386  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineManager 从日志解析进度: 20%
2025-08-18 13:25:31.470  4254-4386  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineManager 从日志解析进度: 30%
2025-08-18 13:25:31.481  4254-4254  Task                    com.dspread.mdm.service              D  🔧 保存任务列表到存储: 2 个
2025-08-18 13:25:31.490  4254-4386  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineManager 从日志解析进度: 40%
2025-08-18 13:25:31.497  4254-4254  Task                    com.dspread.mdm.service              D  🔧 更新任务错误信息: taskId=79b1560a8eb14c5c958b49e8521f3624&175f338e396f458e8d5935cfadb98f60
2025-08-18 13:25:31.514  4254-4254  Task                    com.dspread.mdm.service              D  🔧 保存任务列表到存储: 2 个
2025-08-18 13:25:31.535  4254-4254  Task                    com.dspread.mdm.service              D  🔧 保存任务列表到存储: 2 个
2025-08-18 13:25:31.541  4254-4254  WebSocket               com.dspread.mdm.service              I  🔧 ST001 主动式上送: task_failed (主动: 8)
2025-08-18 13:25:31.576  4254-4254  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0108","serialNo":"01354090202503050399","request_time":"1755494731557","request_id":"1755494731557C0108","version":"1","data":{"taskId":"79b1560a8eb14c5c958b49e8521f3624&175f338e396f458e8d5935cfadb98f60","taskResult":"C02","appId":"","errorMsg":"DOWNLOADING (10%)"},"myVersionName":"1.0.10.20250818.DSPREAD.MDM.SERVICE","terminalDate":"20250818132531","org_request_id":"1755494713915ST001","org_request_time":"1755494713915"}
2025-08-18 13:25:31.583  4254-4254  WebSocket               com.dspread.mdm.service              I  🔧 任务结果实时上送: taskId=79b1560a8eb14c5c958b49e8521f3624&175f338e396f458e8d5935cfadb98f60, result=C02 (1)
2025-08-18 13:25:31.589  4254-4254  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineUpgradeStrategy UpdateEngine状态: DOWNLOADING (3), 进度: 10%
2025-08-18 13:25:31.594  4254-4254  Task                    com.dspread.mdm.service              D  🔧 升级进度: VERIFYING (20%)
2025-08-18 13:25:31.605  4254-4254  Task                    com.dspread.mdm.service              D  🔧 更新任务状态: taskId=79b1560a8eb14c5c958b49e8521f3624&175f338e396f458e8d5935cfadb98f60, state=C02
2025-08-18 13:25:31.621  4254-4254  Task                    com.dspread.mdm.service              D  🔧 保存任务列表到存储: 2 个
2025-08-18 13:25:31.632  4254-4254  Task                    com.dspread.mdm.service              D  🔧 更新任务错误信息: taskId=79b1560a8eb14c5c958b49e8521f3624&175f338e396f458e8d5935cfadb98f60
2025-08-18 13:25:31.648  4254-4254  Task                    com.dspread.mdm.service              D  🔧 保存任务列表到存储: 2 个
2025-08-18 13:25:31.669  4254-4254  Task                    com.dspread.mdm.service              D  🔧 保存任务列表到存储: 2 个
2025-08-18 13:25:31.675  4254-4254  WebSocket               com.dspread.mdm.service              I  🔧 ST001 主动式上送: task_failed (主动: 9)
2025-08-18 13:25:31.711  4254-4254  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0108","serialNo":"01354090202503050399","request_time":"1755494731691","request_id":"1755494731691C0108","version":"1","data":{"taskId":"79b1560a8eb14c5c958b49e8521f3624&175f338e396f458e8d5935cfadb98f60","taskResult":"C02","appId":"","errorMsg":"VERIFYING (20%)"},"myVersionName":"1.0.10.20250818.DSPREAD.MDM.SERVICE","terminalDate":"20250818132531","org_request_id":"1755494713915ST001","org_request_time":"1755494713915"}
2025-08-18 13:25:31.718  4254-4254  WebSocket               com.dspread.mdm.service              I  🔧 任务结果实时上送: taskId=79b1560a8eb14c5c958b49e8521f3624&175f338e396f458e8d5935cfadb98f60, result=C02 (1)
2025-08-18 13:25:31.723  4254-4254  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineUpgradeStrategy UpdateEngine状态: VERIFYING (4), 进度: 20%
2025-08-18 13:25:31.730  4254-4254  Task                    com.dspread.mdm.service              D  🔧 升级进度: VERIFYING (30%)
2025-08-18 13:25:31.741  4254-4254  Task                    com.dspread.mdm.service              D  🔧 更新任务状态: taskId=79b1560a8eb14c5c958b49e8521f3624&175f338e396f458e8d5935cfadb98f60, state=C02
2025-08-18 13:25:31.757  4254-4254  Task                    com.dspread.mdm.service              D  🔧 保存任务列表到存储: 2 个
2025-08-18 13:25:31.768  4254-4254  Task                    com.dspread.mdm.service              D  🔧 更新任务错误信息: taskId=79b1560a8eb14c5c958b49e8521f3624&175f338e396f458e8d5935cfadb98f60
2025-08-18 13:25:31.785  4254-4254  Task                    com.dspread.mdm.service              D  🔧 保存任务列表到存储: 2 个
2025-08-18 13:25:31.807  4254-4254  Task                    com.dspread.mdm.service              D  🔧 保存任务列表到存储: 2 个
2025-08-18 13:25:31.813  4254-4254  WebSocket               com.dspread.mdm.service              I  🔧 ST001 主动式上送: task_failed (主动: 10)
2025-08-18 13:25:31.846  4254-4254  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0108","serialNo":"01354090202503050399","request_time":"1755494731828","request_id":"1755494731828C0108","version":"1","data":{"taskId":"79b1560a8eb14c5c958b49e8521f3624&175f338e396f458e8d5935cfadb98f60","taskResult":"C02","appId":"","errorMsg":"VERIFYING (30%)"},"myVersionName":"1.0.10.20250818.DSPREAD.MDM.SERVICE","terminalDate":"20250818132531","org_request_id":"1755494713915ST001","org_request_time":"1755494713915"}
2025-08-18 13:25:31.852  4254-4254  WebSocket               com.dspread.mdm.service              I  🔧 任务结果实时上送: taskId=79b1560a8eb14c5c958b49e8521f3624&175f338e396f458e8d5935cfadb98f60, result=C02 (1)
2025-08-18 13:25:31.858  4254-4254  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineUpgradeStrategy UpdateEngine状态: VERIFYING (4), 进度: 30%
2025-08-18 13:25:31.864  4254-4254  Task                    com.dspread.mdm.service              D  🔧 升级进度: VERIFYING (40%)
2025-08-18 13:25:31.874  4254-4254  Task                    com.dspread.mdm.service              D  🔧 更新任务状态: taskId=79b1560a8eb14c5c958b49e8521f3624&175f338e396f458e8d5935cfadb98f60, state=C02
2025-08-18 13:25:31.890  4254-4254  Task                    com.dspread.mdm.service              D  🔧 保存任务列表到存储: 2 个
2025-08-18 13:25:31.900  4254-4254  Task                    com.dspread.mdm.service              D  🔧 更新任务错误信息: taskId=79b1560a8eb14c5c958b49e8521f3624&175f338e396f458e8d5935cfadb98f60
2025-08-18 13:25:31.916  4254-4254  Task                    com.dspread.mdm.service              D  🔧 保存任务列表到存储: 2 个
2025-08-18 13:25:31.932  4254-4324  Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"request_time":"1755494730659","org_request_time":"1755494731557","org_request_id":"1755494731557C0108","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755494730659S0000","serialNo":"01354090202503050399"}
2025-08-18 13:25:31.937  4254-4254  Task                    com.dspread.mdm.service              D  🔧 保存任务列表到存储: 2 个
2025-08-18 13:25:31.941  4254-4324  WebSocket               com.dspread.mdm.service              I  🔧 收到响应消息: transCode=S0000, orgRequestId=1755494731557C0108, state=0, remark=
2025-08-18 13:25:31.942  4254-4254  WebSocket               com.dspread.mdm.service              I  🔧 ST001 主动式上送: task_failed (主动: 11)
2025-08-18 13:25:31.946  4254-4324  WebSocket               com.dspread.mdm.service              I  🔧 处理任务结果响应: state=0
2025-08-18 13:25:31.952  4254-4324  WebSocket               com.dspread.mdm.service              I  🔧 任务结果上传成功
2025-08-18 13:25:31.974  4254-4254  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0108","serialNo":"01354090202503050399","request_time":"1755494731957","request_id":"1755494731957C0108","version":"1","data":{"taskId":"79b1560a8eb14c5c958b49e8521f3624&175f338e396f458e8d5935cfadb98f60","taskResult":"C02","appId":"","errorMsg":"VERIFYING (40%)"},"myVersionName":"1.0.10.20250818.DSPREAD.MDM.SERVICE","terminalDate":"20250818132531","org_request_id":"1755494713915ST001","org_request_time":"1755494713915"}
2025-08-18 13:25:31.980  4254-4254  WebSocket               com.dspread.mdm.service              I  🔧 任务结果实时上送: taskId=79b1560a8eb14c5c958b49e8521f3624&175f338e396f458e8d5935cfadb98f60, result=C02 (1)
2025-08-18 13:25:31.986  4254-4254  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineUpgradeStrategy UpdateEngine状态: VERIFYING (4), 进度: 40%
2025-08-18 13:25:31.986  4254-4324  Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"request_time":"1755494730788","org_request_time":"1755494731691","org_request_id":"1755494731691C0108","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755494730788S0000","serialNo":"01354090202503050399"}
2025-08-18 13:25:31.995  4254-4324  WebSocket               com.dspread.mdm.service              I  🔧 收到响应消息: transCode=S0000, orgRequestId=1755494731691C0108, state=0, remark=
2025-08-18 13:25:32.001  4254-4324  WebSocket               com.dspread.mdm.service              I  🔧 处理任务结果响应: state=0
2025-08-18 13:25:32.006  4254-4324  WebSocket               com.dspread.mdm.service              I  🔧 任务结果上传成功
2025-08-18 13:25:32.064  4254-4324  Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"request_time":"1755494730986","org_request_time":"1755494731828","org_request_id":"1755494731828C0108","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755494730986S0000","serialNo":"01354090202503050399"}
2025-08-18 13:25:32.073  4254-4324  WebSocket               com.dspread.mdm.service              I  🔧 收到响应消息: transCode=S0000, orgRequestId=1755494731828C0108, state=0, remark=
2025-08-18 13:25:32.078  4254-4324  WebSocket               com.dspread.mdm.service              I  🔧 处理任务结果响应: state=0
2025-08-18 13:25:32.084  4254-4324  WebSocket               com.dspread.mdm.service              I  🔧 任务结果上传成功
2025-08-18 13:25:32.230  4254-4324  Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"request_time":"1755494731074","org_request_time":"1755494731957","org_request_id":"1755494731957C0108","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755494731074S0000","serialNo":"01354090202503050399"}
2025-08-18 13:25:32.238  4254-4324  WebSocket               com.dspread.mdm.service              I  🔧 收到响应消息: transCode=S0000, orgRequestId=1755494731957C0108, state=0, remark=
2025-08-18 13:25:32.244  4254-4324  WebSocket               com.dspread.mdm.service              I  🔧 处理任务结果响应: state=0
2025-08-18 13:25:32.249  4254-4324  WebSocket               com.dspread.mdm.service              I  🔧 任务结果上传成功
2025-08-18 13:25:33.269  4254-4373  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineManager 检测到文件系统活动: /data/ota_package
2025-08-18 13:25:33.270  4254-4254  Thread-12               com.dspread.mdm.service              W  type=1400 audit(0.0:189): avc:  denied  { getattr } for  path="/data/cache/recovery" dev="mmcblk0p47" ino=193 scontext=u:r:system_app:s0 tcontext=u:object_r:cache_recovery_file:s0 tclass=dir permissive=0
2025-08-18 13:25:33.270  4254-4254  Thread-12               com.dspread.mdm.service              W  type=1400 audit(0.0:190): avc:  denied  { getattr } for  path="/dev/block/platform/bootdevice/by-name" dev="tmpfs" ino=7300 scontext=u:r:system_app:s0 tcontext=u:object_r:block_device:s0 tclass=dir permissive=0
2025-08-18 13:25:34.936  4254-4325  WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 1
2025-08-18 13:25:35.038  4254-4324  WebSocket               com.dspread.mdm.service              I  🔧 收到服务器 PONG 帧: 1
2025-08-18 13:25:35.248  4254-4254  Common                  com.dspread.mdm.service              I  ✅ ⏰ 设置任务执行定时器成功，下次执行: 60秒后 (1分钟)
2025-08-18 13:25:35.602  4254-4386  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineManager 从日志解析进度: 50%
2025-08-18 13:25:35.613  4254-4254  Task                    com.dspread.mdm.service              D  🔧 升级进度: VERIFYING (50%)
2025-08-18 13:25:35.632  4254-4254  Task                    com.dspread.mdm.service              D  🔧 更新任务状态: taskId=79b1560a8eb14c5c958b49e8521f3624&175f338e396f458e8d5935cfadb98f60, state=C02
2025-08-18 13:25:35.656  4254-4254  Task                    com.dspread.mdm.service              D  🔧 保存任务列表到存储: 2 个
2025-08-18 13:25:35.668  4254-4254  Task                    com.dspread.mdm.service              D  🔧 更新任务错误信息: taskId=79b1560a8eb14c5c958b49e8521f3624&175f338e396f458e8d5935cfadb98f60
2025-08-18 13:25:35.688  4254-4254  Task                    com.dspread.mdm.service              D  🔧 保存任务列表到存储: 2 个
2025-08-18 13:25:35.711  4254-4254  Task                    com.dspread.mdm.service              D  🔧 保存任务列表到存储: 2 个
2025-08-18 13:25:35.718  4254-4254  WebSocket               com.dspread.mdm.service              I  🔧 ST001 主动式上送: task_failed (主动: 12)
2025-08-18 13:25:35.751  4254-4254  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0108","serialNo":"01354090202503050399","request_time":"1755494735733","request_id":"1755494735733C0108","version":"1","data":{"taskId":"79b1560a8eb14c5c958b49e8521f3624&175f338e396f458e8d5935cfadb98f60","taskResult":"C02","appId":"","errorMsg":"VERIFYING (50%)"},"myVersionName":"1.0.10.20250818.DSPREAD.MDM.SERVICE","terminalDate":"20250818132535","org_request_id":"1755494713915ST001","org_request_time":"1755494713915"}
2025-08-18 13:25:35.758  4254-4254  WebSocket               com.dspread.mdm.service              I  🔧 任务结果实时上送: taskId=79b1560a8eb14c5c958b49e8521f3624&175f338e396f458e8d5935cfadb98f60, result=C02 (1)
2025-08-18 13:25:35.763  4254-4254  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineUpgradeStrategy UpdateEngine状态: VERIFYING (4), 进度: 50%
2025-08-18 13:25:35.885  4254-4324  Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"request_time":"1755494734832","org_request_time":"1755494735733","org_request_id":"1755494735733C0108","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755494734832S0000","serialNo":"01354090202503050399"}
2025-08-18 13:25:35.894  4254-4324  WebSocket               com.dspread.mdm.service              I  🔧 收到响应消息: transCode=S0000, orgRequestId=1755494735733C0108, state=0, remark=
2025-08-18 13:25:35.901  4254-4324  WebSocket               com.dspread.mdm.service              I  🔧 处理任务结果响应: state=0
2025-08-18 13:25:35.907  4254-4324  WebSocket               com.dspread.mdm.service              I  🔧 任务结果上传成功
2025-08-18 13:25:37.048  4254-4386  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineManager 从日志解析进度: 60%
2025-08-18 13:25:37.055  4254-4254  Task                    com.dspread.mdm.service              D  🔧 升级进度: VERIFYING (60%)
2025-08-18 13:25:37.065  4254-4254  Task                    com.dspread.mdm.service              D  🔧 更新任务状态: taskId=79b1560a8eb14c5c958b49e8521f3624&175f338e396f458e8d5935cfadb98f60, state=C02
2025-08-18 13:25:37.082  4254-4254  Task                    com.dspread.mdm.service              D  🔧 保存任务列表到存储: 2 个
2025-08-18 13:25:37.092  4254-4254  Task                    com.dspread.mdm.service              D  🔧 更新任务错误信息: taskId=79b1560a8eb14c5c958b49e8521f3624&175f338e396f458e8d5935cfadb98f60
2025-08-18 13:25:37.109  4254-4254  Task                    com.dspread.mdm.service              D  🔧 保存任务列表到存储: 2 个
2025-08-18 13:25:37.130  4254-4254  Task                    com.dspread.mdm.service              D  🔧 保存任务列表到存储: 2 个
2025-08-18 13:25:37.136  4254-4254  WebSocket               com.dspread.mdm.service              I  🔧 ST001 主动式上送: task_failed (主动: 13)
2025-08-18 13:25:37.169  4254-4254  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0108","serialNo":"01354090202503050399","request_time":"1755494737151","request_id":"1755494737151C0108","version":"1","data":{"taskId":"79b1560a8eb14c5c958b49e8521f3624&175f338e396f458e8d5935cfadb98f60","taskResult":"C02","appId":"","errorMsg":"VERIFYING (60%)"},"myVersionName":"1.0.10.20250818.DSPREAD.MDM.SERVICE","terminalDate":"20250818132537","org_request_id":"1755494713915ST001","org_request_time":"1755494713915"}
2025-08-18 13:25:37.175  4254-4254  WebSocket               com.dspread.mdm.service              I  🔧 任务结果实时上送: taskId=79b1560a8eb14c5c958b49e8521f3624&175f338e396f458e8d5935cfadb98f60, result=C02 (1)
2025-08-18 13:25:37.181  4254-4254  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineUpgradeStrategy UpdateEngine状态: VERIFYING (4), 进度: 60%
2025-08-18 13:25:37.294  4254-4324  Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"request_time":"1755494736248","org_request_time":"1755494737151","org_request_id":"1755494737151C0108","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755494736248S0000","serialNo":"01354090202503050399"}
2025-08-18 13:25:37.304  4254-4324  WebSocket               com.dspread.mdm.service              I  🔧 收到响应消息: transCode=S0000, orgRequestId=1755494737151C0108, state=0, remark=
2025-08-18 13:25:37.310  4254-4324  WebSocket               com.dspread.mdm.service              I  🔧 处理任务结果响应: state=0
2025-08-18 13:25:37.316  4254-4324  WebSocket               com.dspread.mdm.service              I  🔧 任务结果上传成功
2025-08-18 13:25:38.277  4254-4373  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineManager 检测到文件系统活动: /data/ota_package
2025-08-18 13:25:38.280  4254-4254  Thread-12               com.dspread.mdm.service              W  type=1400 audit(0.0:191): avc:  denied  { getattr } for  path="/data/cache/recovery" dev="mmcblk0p47" ino=193 scontext=u:r:system_app:s0 tcontext=u:object_r:cache_recovery_file:s0 tclass=dir permissive=0
2025-08-18 13:25:38.280  4254-4254  Thread-12               com.dspread.mdm.service              W  type=1400 audit(0.0:192): avc:  denied  { getattr } for  path="/dev/block/platform/bootdevice/by-name" dev="tmpfs" ino=7300 scontext=u:r:system_app:s0 tcontext=u:object_r:block_device:s0 tclass=dir permissive=0
2025-08-18 13:25:40.527  4254-4386  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineManager 从日志解析进度: 70%
2025-08-18 13:25:40.534  4254-4254  Task                    com.dspread.mdm.service              D  🔧 升级进度: VERIFYING (70%)
2025-08-18 13:25:40.544  4254-4254  Task                    com.dspread.mdm.service              D  🔧 更新任务状态: taskId=79b1560a8eb14c5c958b49e8521f3624&175f338e396f458e8d5935cfadb98f60, state=C02
2025-08-18 13:25:40.561  4254-4254  Task                    com.dspread.mdm.service              D  🔧 保存任务列表到存储: 2 个
2025-08-18 13:25:40.571  4254-4254  Task                    com.dspread.mdm.service              D  🔧 更新任务错误信息: taskId=79b1560a8eb14c5c958b49e8521f3624&175f338e396f458e8d5935cfadb98f60
2025-08-18 13:25:40.588  4254-4254  Task                    com.dspread.mdm.service              D  🔧 保存任务列表到存储: 2 个
2025-08-18 13:25:40.609  4254-4254  Task                    com.dspread.mdm.service              D  🔧 保存任务列表到存储: 2 个
2025-08-18 13:25:40.616  4254-4254  WebSocket               com.dspread.mdm.service              I  🔧 ST001 主动式上送: task_failed (主动: 14)
2025-08-18 13:25:40.649  4254-4254  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0108","serialNo":"01354090202503050399","request_time":"1755494740631","request_id":"1755494740631C0108","version":"1","data":{"taskId":"79b1560a8eb14c5c958b49e8521f3624&175f338e396f458e8d5935cfadb98f60","taskResult":"C02","appId":"","errorMsg":"VERIFYING (70%)"},"myVersionName":"1.0.10.20250818.DSPREAD.MDM.SERVICE","terminalDate":"20250818132540","org_request_id":"1755494713915ST001","org_request_time":"1755494713915"}
2025-08-18 13:25:40.656  4254-4254  WebSocket               com.dspread.mdm.service              I  🔧 任务结果实时上送: taskId=79b1560a8eb14c5c958b49e8521f3624&175f338e396f458e8d5935cfadb98f60, result=C02 (1)
2025-08-18 13:25:40.662  4254-4254  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineUpgradeStrategy UpdateEngine状态: VERIFYING (4), 进度: 70%
2025-08-18 13:25:40.884  4254-4324  Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"request_time":"1755494739730","org_request_time":"1755494740631","org_request_id":"1755494740631C0108","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755494739730S0000","serialNo":"01354090202503050399"}
2025-08-18 13:25:40.893  4254-4324  WebSocket               com.dspread.mdm.service              I  🔧 收到响应消息: transCode=S0000, orgRequestId=1755494740631C0108, state=0, remark=
2025-08-18 13:25:40.899  4254-4324  WebSocket               com.dspread.mdm.service              I  🔧 处理任务结果响应: state=0
2025-08-18 13:25:40.905  4254-4324  WebSocket               com.dspread.mdm.service              I  🔧 任务结果上传成功
2025-08-18 13:25:43.286  4254-4373  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineManager 检测到文件系统活动: /data/ota_package
2025-08-18 13:25:43.290  4254-4254  Thread-12               com.dspread.mdm.service              W  type=1400 audit(0.0:193): avc:  denied  { getattr } for  path="/data/cache/recovery" dev="mmcblk0p47" ino=193 scontext=u:r:system_app:s0 tcontext=u:object_r:cache_recovery_file:s0 tclass=dir permissive=0
2025-08-18 13:25:43.290  4254-4254  Thread-12               com.dspread.mdm.service              W  type=1400 audit(0.0:194): avc:  denied  { getattr } for  path="/dev/block/platform/bootdevice/by-name" dev="tmpfs" ino=7300 scontext=u:r:system_app:s0 tcontext=u:object_r:block_device:s0 tclass=dir permissive=0
2025-08-18 13:25:44.691  4254-4386  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineManager 从日志解析进度: 80%
2025-08-18 13:25:44.698  4254-4254  Task                    com.dspread.mdm.service              D  🔧 升级进度: FINALIZING (80%)
2025-08-18 13:25:44.708  4254-4254  Task                    com.dspread.mdm.service              D  🔧 更新任务状态: taskId=79b1560a8eb14c5c958b49e8521f3624&175f338e396f458e8d5935cfadb98f60, state=C02
2025-08-18 13:25:44.725  4254-4254  Task                    com.dspread.mdm.service              D  🔧 保存任务列表到存储: 2 个
2025-08-18 13:25:44.735  4254-4254  Task                    com.dspread.mdm.service              D  🔧 更新任务错误信息: taskId=79b1560a8eb14c5c958b49e8521f3624&175f338e396f458e8d5935cfadb98f60
2025-08-18 13:25:44.752  4254-4254  Task                    com.dspread.mdm.service              D  🔧 保存任务列表到存储: 2 个
2025-08-18 13:25:44.774  4254-4254  Task                    com.dspread.mdm.service              D  🔧 保存任务列表到存储: 2 个
2025-08-18 13:25:44.780  4254-4254  WebSocket               com.dspread.mdm.service              I  🔧 ST001 主动式上送: task_failed (主动: 15)
2025-08-18 13:25:44.814  4254-4254  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0108","serialNo":"01354090202503050399","request_time":"1755494744795","request_id":"1755494744795C0108","version":"1","data":{"taskId":"79b1560a8eb14c5c958b49e8521f3624&175f338e396f458e8d5935cfadb98f60","taskResult":"C02","appId":"","errorMsg":"FINALIZING (80%)"},"myVersionName":"1.0.10.20250818.DSPREAD.MDM.SERVICE","terminalDate":"20250818132544","org_request_id":"1755494713915ST001","org_request_time":"1755494713915"}
2025-08-18 13:25:44.820  4254-4254  WebSocket               com.dspread.mdm.service              I  🔧 任务结果实时上送: taskId=79b1560a8eb14c5c958b49e8521f3624&175f338e396f458e8d5935cfadb98f60, result=C02 (1)
2025-08-18 13:25:44.827  4254-4254  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineUpgradeStrategy UpdateEngine状态: FINALIZING (5), 进度: 80%
2025-08-18 13:25:44.994  4254-4324  Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"request_time":"1755494743895","org_request_time":"1755494744795","org_request_id":"1755494744795C0108","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755494743895S0000","serialNo":"01354090202503050399"}
2025-08-18 13:25:45.004  4254-4324  WebSocket               com.dspread.mdm.service              I  🔧 收到响应消息: transCode=S0000, orgRequestId=1755494744795C0108, state=0, remark=
2025-08-18 13:25:45.011  4254-4324  WebSocket               com.dspread.mdm.service              I  🔧 处理任务结果响应: state=0
2025-08-18 13:25:45.017  4254-4324  WebSocket               com.dspread.mdm.service              I  🔧 任务结果上传成功
2025-08-18 13:25:48.290  4254-4254  Thread-12               com.dspread.mdm.service              W  type=1400 audit(0.0:195): avc:  denied  { getattr } for  path="/data/cache/recovery" dev="mmcblk0p47" ino=193 scontext=u:r:system_app:s0 tcontext=u:object_r:cache_recovery_file:s0 tclass=dir permissive=0
2025-08-18 13:25:48.293  4254-4373  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineManager 检测到文件系统活动: /data/ota_package
2025-08-18 13:25:48.290  4254-4254  Thread-12               com.dspread.mdm.service              W  type=1400 audit(0.0:196): avc:  denied  { getattr } for  path="/dev/block/platform/bootdevice/by-name" dev="tmpfs" ino=7300 scontext=u:r:system_app:s0 tcontext=u:object_r:block_device:s0 tclass=dir permissive=0
2025-08-18 13:25:48.881  4254-4386  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineManager 从日志解析进度: 90%
2025-08-18 13:25:48.887  4254-4254  Task                    com.dspread.mdm.service              D  🔧 升级进度: FINALIZING (90%)
2025-08-18 13:25:48.898  4254-4254  Task                    com.dspread.mdm.service              D  🔧 更新任务状态: taskId=79b1560a8eb14c5c958b49e8521f3624&175f338e396f458e8d5935cfadb98f60, state=C02
2025-08-18 13:25:48.915  4254-4254  Task                    com.dspread.mdm.service              D  🔧 保存任务列表到存储: 2 个
2025-08-18 13:25:48.925  4254-4254  Task                    com.dspread.mdm.service              D  🔧 更新任务错误信息: taskId=79b1560a8eb14c5c958b49e8521f3624&175f338e396f458e8d5935cfadb98f60
2025-08-18 13:25:48.942  4254-4254  Task                    com.dspread.mdm.service              D  🔧 保存任务列表到存储: 2 个
2025-08-18 13:25:48.964  4254-4254  Task                    com.dspread.mdm.service              D  🔧 保存任务列表到存储: 2 个
2025-08-18 13:25:48.970  4254-4254  WebSocket               com.dspread.mdm.service              I  🔧 ST001 主动式上送: task_failed (主动: 16)
2025-08-18 13:25:49.012  4254-4254  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0108","serialNo":"01354090202503050399","request_time":"1755494748987","request_id":"1755494748987C0108","version":"1","data":{"taskId":"79b1560a8eb14c5c958b49e8521f3624&175f338e396f458e8d5935cfadb98f60","taskResult":"C02","appId":"","errorMsg":"FINALIZING (90%)"},"myVersionName":"1.0.10.20250818.DSPREAD.MDM.SERVICE","terminalDate":"20250818132548","org_request_id":"1755494713915ST001","org_request_time":"1755494713915"}
2025-08-18 13:25:49.019  4254-4254  WebSocket               com.dspread.mdm.service              I  🔧 任务结果实时上送: taskId=79b1560a8eb14c5c958b49e8521f3624&175f338e396f458e8d5935cfadb98f60, result=C02 (1)
2025-08-18 13:25:49.026  4254-4254  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineUpgradeStrategy UpdateEngine状态: FINALIZING (5), 进度: 90%
2025-08-18 13:25:49.233  4254-4324  Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"request_time":"1755494748093","org_request_time":"1755494748987","org_request_id":"1755494748987C0108","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755494748093S0000","serialNo":"01354090202503050399"}
2025-08-18 13:25:49.243  4254-4324  WebSocket               com.dspread.mdm.service              I  🔧 收到响应消息: transCode=S0000, orgRequestId=1755494748987C0108, state=0, remark=
2025-08-18 13:25:49.249  4254-4324  WebSocket               com.dspread.mdm.service              I  🔧 处理任务结果响应: state=0
2025-08-18 13:25:49.255  4254-4324  WebSocket               com.dspread.mdm.service              I  🔧 任务结果上传成功
2025-08-18 13:25:52.975  4254-4386  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineManager 从日志解析进度: 100%
2025-08-18 13:25:52.982  4254-4254  Task                    com.dspread.mdm.service              D  🔧 升级进度: FINALIZING (100%)
2025-08-18 13:25:52.993  4254-4254  Task                    com.dspread.mdm.service              D  🔧 更新任务状态: taskId=79b1560a8eb14c5c958b49e8521f3624&175f338e396f458e8d5935cfadb98f60, state=C02
2025-08-18 13:25:53.010  4254-4254  Task                    com.dspread.mdm.service              D  🔧 保存任务列表到存储: 2 个
2025-08-18 13:25:53.021  4254-4254  Task                    com.dspread.mdm.service              D  🔧 更新任务错误信息: taskId=79b1560a8eb14c5c958b49e8521f3624&175f338e396f458e8d5935cfadb98f60
2025-08-18 13:25:53.038  4254-4254  Task                    com.dspread.mdm.service              D  🔧 保存任务列表到存储: 2 个
2025-08-18 13:25:53.060  4254-4254  Task                    com.dspread.mdm.service              D  🔧 保存任务列表到存储: 2 个
2025-08-18 13:25:53.066  4254-4254  WebSocket               com.dspread.mdm.service              I  🔧 ST001 主动式上送: task_failed (主动: 17)
2025-08-18 13:25:53.100  4254-4254  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0108","serialNo":"01354090202503050399","request_time":"1755494753082","request_id":"1755494753082C0108","version":"1","data":{"taskId":"79b1560a8eb14c5c958b49e8521f3624&175f338e396f458e8d5935cfadb98f60","taskResult":"C02","appId":"","errorMsg":"FINALIZING (100%)"},"myVersionName":"1.0.10.20250818.DSPREAD.MDM.SERVICE","terminalDate":"20250818132553","org_request_id":"1755494713915ST001","org_request_time":"1755494713915"}
2025-08-18 13:25:53.106  4254-4254  WebSocket               com.dspread.mdm.service              I  🔧 任务结果实时上送: taskId=79b1560a8eb14c5c958b49e8521f3624&175f338e396f458e8d5935cfadb98f60, result=C02 (1)
2025-08-18 13:25:53.113  4254-4254  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineUpgradeStrategy UpdateEngine状态: FINALIZING (5), 进度: 100%
2025-08-18 13:25:53.233  4254-4324  Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"request_time":"1755494752181","org_request_time":"1755494753082","org_request_id":"1755494753082C0108","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755494752181S0000","serialNo":"01354090202503050399"}
2025-08-18 13:25:53.243  4254-4324  WebSocket               com.dspread.mdm.service              I  🔧 收到响应消息: transCode=S0000, orgRequestId=1755494753082C0108, state=0, remark=
2025-08-18 13:25:53.249  4254-4324  WebSocket               com.dspread.mdm.service              I  🔧 处理任务结果响应: state=0
2025-08-18 13:25:53.255  4254-4324  WebSocket               com.dspread.mdm.service              I  🔧 任务结果上传成功
2025-08-18 13:25:53.300  4254-4373  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineManager 检测到文件系统活动: /data/ota_package
2025-08-18 13:25:53.300  4254-4254  Thread-12               com.dspread.mdm.service              W  type=1400 audit(0.0:197): avc:  denied  { getattr } for  path="/data/cache/recovery" dev="mmcblk0p47" ino=193 scontext=u:r:system_app:s0 tcontext=u:object_r:cache_recovery_file:s0 tclass=dir permissive=0
2025-08-18 13:25:53.300  4254-4254  Thread-12               com.dspread.mdm.service              W  type=1400 audit(0.0:198): avc:  denied  { getattr } for  path="/dev/block/platform/bootdevice/by-name" dev="tmpfs" ino=7300 scontext=u:r:system_app:s0 tcontext=u:object_r:block_device:s0 tclass=dir permissive=0
2025-08-18 13:25:58.309  4254-4373  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineManager 检测到文件系统活动: /data/ota_package
2025-08-18 13:25:58.310  4254-4254  Thread-12               com.dspread.mdm.service              W  type=1400 audit(0.0:199): avc:  denied  { getattr } for  path="/data/cache/recovery" dev="mmcblk0p47" ino=193 scontext=u:r:system_app:s0 tcontext=u:object_r:cache_recovery_file:s0 tclass=dir permissive=0
2025-08-18 13:25:58.310  4254-4254  Thread-12               com.dspread.mdm.service              W  type=1400 audit(0.0:200): avc:  denied  { getattr } for  path="/dev/block/platform/bootdevice/by-name" dev="tmpfs" ino=7300 scontext=u:r:system_app:s0 tcontext=u:object_r:block_device:s0 tclass=dir permissive=0
2025-08-18 13:26:03.318  4254-4373  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineManager 检测到文件系统活动: /data/ota_package
2025-08-18 13:26:03.320  4254-4254  Thread-12               com.dspread.mdm.service              W  type=1400 audit(0.0:201): avc:  denied  { getattr } for  path="/data/cache/recovery" dev="mmcblk0p47" ino=193 scontext=u:r:system_app:s0 tcontext=u:object_r:cache_recovery_file:s0 tclass=dir permissive=0
2025-08-18 13:26:03.320  4254-4254  Thread-12               com.dspread.mdm.service              W  type=1400 audit(0.0:202): avc:  denied  { getattr } for  path="/dev/block/platform/bootdevice/by-name" dev="tmpfs" ino=7300 scontext=u:r:system_app:s0 tcontext=u:object_r:block_device:s0 tclass=dir permissive=0
2025-08-18 13:26:04.425  4254-4254  Receiver                com.dspread.mdm.service              D  🔧 电池状态更新: 电池状态: 电量=73%, 温度=29.4°C, 健康=2, 充电=是, 低电量=否, 耗尽=否
2025-08-18 13:26:08.327  4254-4373  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineManager 检测到文件系统活动: /data/ota_package
2025-08-18 13:26:08.330  4254-4254  Thread-12               com.dspread.mdm.service              W  type=1400 audit(0.0:203): avc:  denied  { getattr } for  path="/data/cache/recovery" dev="mmcblk0p47" ino=193 scontext=u:r:system_app:s0 tcontext=u:object_r:cache_recovery_file:s0 tclass=dir permissive=0
2025-08-18 13:26:08.330  4254-4254  Thread-12               com.dspread.mdm.service              W  type=1400 audit(0.0:204): avc:  denied  { getattr } for  path="/dev/block/platform/bootdevice/by-name" dev="tmpfs" ino=7300 scontext=u:r:system_app:s0 tcontext=u:object_r:block_device:s0 tclass=dir permissive=0
2025-08-18 13:26:13.338  4254-4373  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineManager 检测到文件系统活动: /data/ota_package
2025-08-18 13:26:13.340  4254-4254  Thread-12               com.dspread.mdm.service              W  type=1400 audit(0.0:205): avc:  denied  { getattr } for  path="/data/cache/recovery" dev="mmcblk0p47" ino=193 scontext=u:r:system_app:s0 tcontext=u:object_r:cache_recovery_file:s0 tclass=dir permissive=0
2025-08-18 13:26:13.340  4254-4254  Thread-12               com.dspread.mdm.service              W  type=1400 audit(0.0:206): avc:  denied  { getattr } for  path="/dev/block/platform/bootdevice/by-name" dev="tmpfs" ino=7300 scontext=u:r:system_app:s0 tcontext=u:object_r:block_device:s0 tclass=dir permissive=0
2025-08-18 13:26:17.046  4254-4284  Common                  com.dspread.mdm.service              D  🔧 ServiceGuardEventHandler 开始执行保活动作
2025-08-18 13:26:17.057  4254-4284  Common                  com.dspread.mdm.service              I  ✅ ⏰ 设置服务守护定时器成功，下次执行: 120秒后 (2分钟)
2025-08-18 13:26:17.064  4254-4284  ContextImpl             com.dspread.mdm.service              W  Calling a method in the system process without a qualified user: android.app.ContextImpl.startService:1899 android.content.ContextWrapper.startService:825 com.dspread.mdm.service.broadcast.handlers.service.ServiceGuardEventHandler.startKeepAliveService:211 com.dspread.mdm.service.broadcast.handlers.service.ServiceGuardEventHandler.performKeepAliveActions:193 com.dspread.mdm.service.broadcast.handlers.service.ServiceGuardEventHandler.access$performKeepAliveActions:24 
2025-08-18 13:26:17.068  4254-4284  Common                  com.dspread.mdm.service              D  🔧 ServiceGuardEventHandler 启动保活服务
2025-08-18 13:26:17.073  4254-4254  Service                 com.dspread.mdm.service              D  🔧 ServiceKeepAliveService 保活服务创建
2025-08-18 13:26:17.080  4254-4254  Service                 com.dspread.mdm.service              D  🔧 ServiceKeepAliveService 保活服务启动
2025-08-18 13:26:17.082  4254-4284  Common                  com.dspread.mdm.service              D  🔧 ServiceGuardEventHandler 保活动作执行完成
2025-08-18 13:26:17.089  4254-4254  Platform                com.dspread.mdm.service              D  🔧 WakeLockManager WakeLock不存在或未持有: ServiceKeepAlive
2025-08-18 13:26:17.098  4254-4254  Platform                com.dspread.mdm.service              D  🔧 WakeLockManager 获取WakeLock成功: ServiceKeepAlive (超时: 30000ms)
2025-08-18 13:26:18.349  4254-4373  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineManager 检测到文件系统活动: /data/ota_package
2025-08-18 13:26:18.350  4254-4254  Thread-12               com.dspread.mdm.service              W  type=1400 audit(0.0:207): avc:  denied  { getattr } for  path="/data/cache/recovery" dev="mmcblk0p47" ino=193 scontext=u:r:system_app:s0 tcontext=u:object_r:cache_recovery_file:s0 tclass=dir permissive=0
2025-08-18 13:26:18.350  4254-4254  Thread-12               com.dspread.mdm.service              W  type=1400 audit(0.0:208): avc:  denied  { getattr } for  path="/dev/block/platform/bootdevice/by-name" dev="tmpfs" ino=7300 scontext=u:r:system_app:s0 tcontext=u:object_r:block_device:s0 tclass=dir permissive=0
2025-08-18 13:26:20.084  4254-4284  Common                  com.dspread.mdm.service              D  🔧 ServiceGuardEventHandler WakeLock已释放
2025-08-18 13:26:23.360  4254-4373  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineManager 检测到文件系统活动: /data/ota_package
2025-08-18 13:26:23.360  4254-4254  Thread-12               com.dspread.mdm.service              W  type=1400 audit(0.0:209): avc:  denied  { getattr } for  path="/data/cache/recovery" dev="mmcblk0p47" ino=193 scontext=u:r:system_app:s0 tcontext=u:object_r:cache_recovery_file:s0 tclass=dir permissive=0
2025-08-18 13:26:23.360  4254-4254  Thread-12               com.dspread.mdm.service              W  type=1400 audit(0.0:210): avc:  denied  { getattr } for  path="/dev/block/platform/bootdevice/by-name" dev="tmpfs" ino=7300 scontext=u:r:system_app:s0 tcontext=u:object_r:block_device:s0 tclass=dir permissive=0
2025-08-18 13:26:25.632  4254-4288  GED                     com.dspread.mdm.service              I  ged_boost_gpu_freq, level 100, eOrigin 2, final_idx 2, oppidx_max 2, oppidx_min 0
2025-08-18 13:26:25.644  4254-4254  BLASTBufferQueue        com.dspread.mdm.service              D  [VRI[TestActivity]#2](f:0,a:1) destructor()
2025-08-18 13:26:25.644  4254-4254  BufferQueueConsumer     com.dspread.mdm.service              D  [VRI[TestActivity]#2(BLAST Consumer)2](id:109e00000002,api:0,p:-1,c:4254) disconnect
2025-08-18 13:26:25.647  4254-4288  GED                     com.dspread.mdm.service              I  ged_boost_gpu_freq, level 100, eOrigin 2, final_idx 2, oppidx_max 2, oppidx_min 0
2025-08-18 13:26:26.050  4254-4254  Receiver                com.dspread.mdm.service              D  🔧 屏幕关闭
2025-08-18 13:26:26.059  4254-4254  WebSocket               com.dspread.mdm.service              W  ⚠️ 未知触发条件: screen_off，默认按被动式处理
2025-08-18 13:26:26.101  4254-4254  WebSocket               com.dspread.mdm.service              I  🔧 C0201 被动式上送拒绝: screen_off (拒绝: 3)
2025-08-18 13:26:26.109  4254-4254  WebSocket               com.dspread.mdm.service              I  🔧 C0201 设备状态上送被流量控制阻止: 被动事件 'screen_off' 在平衡模式 - 重要变化下未启用
2025-08-18 13:26:26.118  4254-4254  Receiver                com.dspread.mdm.service              D  🔧 屏幕关闭处理完成
2025-08-18 13:26:28.370  4254-4254  Thread-12               com.dspread.mdm.service              W  type=1400 audit(0.0:211): avc:  denied  { getattr } for  path="/data/cache/recovery" dev="mmcblk0p47" ino=193 scontext=u:r:system_app:s0 tcontext=u:object_r:cache_recovery_file:s0 tclass=dir permissive=0
2025-08-18 13:26:28.370  4254-4254  Thread-12               com.dspread.mdm.service              W  type=1400 audit(0.0:212): avc:  denied  { getattr } for  path="/dev/block/platform/bootdevice/by-name" dev="tmpfs" ino=7300 scontext=u:r:system_app:s0 tcontext=u:object_r:block_device:s0 tclass=dir permissive=0
2025-08-18 13:26:33.370  4254-4254  Thread-12               com.dspread.mdm.service              W  type=1400 audit(0.0:213): avc:  denied  { getattr } for  path="/data/cache/recovery" dev="mmcblk0p47" ino=193 scontext=u:r:system_app:s0 tcontext=u:object_r:cache_recovery_file:s0 tclass=dir permissive=0
2025-08-18 13:26:33.370  4254-4254  Thread-12               com.dspread.mdm.service              W  type=1400 audit(0.0:214): avc:  denied  { getattr } for  path="/dev/block/platform/bootdevice/by-name" dev="tmpfs" ino=7300 scontext=u:r:system_app:s0 tcontext=u:object_r:block_device:s0 tclass=dir permissive=0
2025-08-18 13:26:34.937  4254-4325  WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 2
2025-08-18 13:26:35.188  4254-4324  WebSocket               com.dspread.mdm.service              I  🔧 收到服务器 PONG 帧: 2
2025-08-18 13:26:35.259  4254-4254  Common                  com.dspread.mdm.service              I  ✅ ⏰ 设置任务执行定时器成功，下次执行: 60秒后 (1分钟)
2025-08-18 13:26:38.370  4254-4254  Thread-12               com.dspread.mdm.service              W  type=1400 audit(0.0:215): avc:  denied  { getattr } for  path="/data/cache/recovery" dev="mmcblk0p47" ino=193 scontext=u:r:system_app:s0 tcontext=u:object_r:cache_recovery_file:s0 tclass=dir permissive=0
2025-08-18 13:26:38.370  4254-4254  Thread-12               com.dspread.mdm.service              W  type=1400 audit(0.0:216): avc:  denied  { getattr } for  path="/dev/block/platform/bootdevice/by-name" dev="tmpfs" ino=7300 scontext=u:r:system_app:s0 tcontext=u:object_r:block_device:s0 tclass=dir permissive=0
2025-08-18 13:26:43.370  4254-4254  Thread-12               com.dspread.mdm.service              W  type=1400 audit(0.0:217): avc:  denied  { getattr } for  path="/data/cache/recovery" dev="mmcblk0p47" ino=193 scontext=u:r:system_app:s0 tcontext=u:object_r:cache_recovery_file:s0 tclass=dir permissive=0
2025-08-18 13:26:43.370  4254-4254  Thread-12               com.dspread.mdm.service              W  type=1400 audit(0.0:218): avc:  denied  { getattr } for  path="/dev/block/platform/bootdevice/by-name" dev="tmpfs" ino=7300 scontext=u:r:system_app:s0 tcontext=u:object_r:block_device:s0 tclass=dir permissive=0
2025-08-18 13:26:47.107  4254-4254  Platform                com.dspread.mdm.service              D  🔧 WakeLockManager WakeLock不存在或未持有: ServiceKeepAlive
2025-08-18 13:26:47.117  4254-4254  Service                 com.dspread.mdm.service              D  🔧 ServiceKeepAliveService 保活任务完成，释放WakeLock
2025-08-18 13:26:48.370  4254-4254  Thread-12               com.dspread.mdm.service              W  type=1400 audit(0.0:219): avc:  denied  { getattr } for  path="/data/cache/recovery" dev="mmcblk0p47" ino=193 scontext=u:r:system_app:s0 tcontext=u:object_r:cache_recovery_file:s0 tclass=dir permissive=0
2025-08-18 13:26:48.370  4254-4254  Thread-12               com.dspread.mdm.service              W  type=1400 audit(0.0:220): avc:  denied  { getattr } for  path="/dev/block/platform/bootdevice/by-name" dev="tmpfs" ino=7300 scontext=u:r:system_app:s0 tcontext=u:object_r:block_device:s0 tclass=dir permissive=0
2025-08-18 13:26:53.370  4254-4254  Thread-12               com.dspread.mdm.service              W  type=1400 audit(0.0:221): avc:  denied  { getattr } for  path="/data/cache/recovery" dev="mmcblk0p47" ino=193 scontext=u:r:system_app:s0 tcontext=u:object_r:cache_recovery_file:s0 tclass=dir permissive=0
2025-08-18 13:26:53.370  4254-4254  Thread-12               com.dspread.mdm.service              W  type=1400 audit(0.0:222): avc:  denied  { getattr } for  path="/dev/block/platform/bootdevice/by-name" dev="tmpfs" ino=7300 scontext=u:r:system_app:s0 tcontext=u:object_r:block_device:s0 tclass=dir permissive=0
2025-08-18 13:26:58.380  4254-4254  Thread-12               com.dspread.mdm.service              W  type=1400 audit(0.0:223): avc:  denied  { getattr } for  path="/data/cache/recovery" dev="mmcblk0p47" ino=193 scontext=u:r:system_app:s0 tcontext=u:object_r:cache_recovery_file:s0 tclass=dir permissive=0
2025-08-18 13:26:58.380  4254-4254  Thread-12               com.dspread.mdm.service              W  type=1400 audit(0.0:224): avc:  denied  { getattr } for  path="/dev/block/platform/bootdevice/by-name" dev="tmpfs" ino=7300 scontext=u:r:system_app:s0 tcontext=u:object_r:block_device:s0 tclass=dir permissive=0
2025-08-18 13:27:03.380  4254-4254  Thread-12               com.dspread.mdm.service              W  type=1400 audit(0.0:225): avc:  denied  { getattr } for  path="/data/cache/recovery" dev="mmcblk0p47" ino=193 scontext=u:r:system_app:s0 tcontext=u:object_r:cache_recovery_file:s0 tclass=dir permissive=0
2025-08-18 13:27:03.380  4254-4254  Thread-12               com.dspread.mdm.service              W  type=1400 audit(0.0:226): avc:  denied  { getattr } for  path="/dev/block/platform/bootdevice/by-name" dev="tmpfs" ino=7300 scontext=u:r:system_app:s0 tcontext=u:object_r:block_device:s0 tclass=dir permissive=0
2025-08-18 13:27:04.414  4254-4254  Receiver                com.dspread.mdm.service              D  🔧 电池状态更新: 电池状态: 电量=73%, 温度=29.4°C, 健康=2, 充电=是, 低电量=否, 耗尽=否
2025-08-18 13:27:08.380  4254-4254  Thread-12               com.dspread.mdm.service              W  type=1400 audit(0.0:227): avc:  denied  { getattr } for  path="/data/cache/recovery" dev="mmcblk0p47" ino=193 scontext=u:r:system_app:s0 tcontext=u:object_r:cache_recovery_file:s0 tclass=dir permissive=0
2025-08-18 13:27:08.380  4254-4254  Thread-12               com.dspread.mdm.service              W  type=1400 audit(0.0:228): avc:  denied  { getattr } for  path="/dev/block/platform/bootdevice/by-name" dev="tmpfs" ino=7300 scontext=u:r:system_app:s0 tcontext=u:object_r:block_device:s0 tclass=dir permissive=0
2025-08-18 13:27:13.390  4254-4254  Thread-12               com.dspread.mdm.service              W  type=1400 audit(0.0:229): avc:  denied  { getattr } for  path="/data/cache/recovery" dev="mmcblk0p47" ino=193 scontext=u:r:system_app:s0 tcontext=u:object_r:cache_recovery_file:s0 tclass=dir permissive=0
2025-08-18 13:27:13.390  4254-4254  Thread-12               com.dspread.mdm.service              W  type=1400 audit(0.0:230): avc:  denied  { getattr } for  path="/dev/block/platform/bootdevice/by-name" dev="tmpfs" ino=7300 scontext=u:r:system_app:s0 tcontext=u:object_r:block_device:s0 tclass=dir permissive=0
2025-08-18 13:27:18.390  4254-4254  Thread-12               com.dspread.mdm.service              W  type=1400 audit(0.0:231): avc:  denied  { getattr } for  path="/data/cache/recovery" dev="mmcblk0p47" ino=193 scontext=u:r:system_app:s0 tcontext=u:object_r:cache_recovery_file:s0 tclass=dir permissive=0
2025-08-18 13:27:18.400  4254-4254  Thread-12               com.dspread.mdm.service              W  type=1400 audit(0.0:232): avc:  denied  { getattr } for  path="/dev/block/platform/bootdevice/by-name" dev="tmpfs" ino=7300 scontext=u:r:system_app:s0 tcontext=u:object_r:block_device:s0 tclass=dir permissive=0
2025-08-18 13:27:23.400  4254-4254  Thread-12               com.dspread.mdm.service              W  type=1400 audit(0.0:233): avc:  denied  { getattr } for  path="/data/cache/recovery" dev="mmcblk0p47" ino=193 scontext=u:r:system_app:s0 tcontext=u:object_r:cache_recovery_file:s0 tclass=dir permissive=0
2025-08-18 13:27:23.400  4254-4254  Thread-12               com.dspread.mdm.service              W  type=1400 audit(0.0:234): avc:  denied  { getattr } for  path="/dev/block/platform/bootdevice/by-name" dev="tmpfs" ino=7300 scontext=u:r:system_app:s0 tcontext=u:object_r:block_device:s0 tclass=dir permissive=0
2025-08-18 13:27:28.410  4254-4254  Thread-12               com.dspread.mdm.service              W  type=1400 audit(0.0:235): avc:  denied  { getattr } for  path="/data/cache/recovery" dev="mmcblk0p47" ino=193 scontext=u:r:system_app:s0 tcontext=u:object_r:cache_recovery_file:s0 tclass=dir permissive=0
2025-08-18 13:27:28.410  4254-4254  Thread-12               com.dspread.mdm.service              W  type=1400 audit(0.0:236): avc:  denied  { getattr } for  path="/dev/block/platform/bootdevice/by-name" dev="tmpfs" ino=7300 scontext=u:r:system_app:s0 tcontext=u:object_r:block_device:s0 tclass=dir permissive=0
2025-08-18 13:27:33.410  4254-4254  Thread-12               com.dspread.mdm.service              W  type=1400 audit(0.0:237): avc:  denied  { getattr } for  path="/data/cache/recovery" dev="mmcblk0p47" ino=193 scontext=u:r:system_app:s0 tcontext=u:object_r:cache_recovery_file:s0 tclass=dir permissive=0
2025-08-18 13:27:33.410  4254-4254  Thread-12               com.dspread.mdm.service              W  type=1400 audit(0.0:238): avc:  denied  { getattr } for  path="/dev/block/platform/bootdevice/by-name" dev="tmpfs" ino=7300 scontext=u:r:system_app:s0 tcontext=u:object_r:block_device:s0 tclass=dir permissive=0
2025-08-18 13:27:34.938  4254-4325  WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 3
2025-08-18 13:27:35.264  4254-4254  Common                  com.dspread.mdm.service              I  ✅ ⏰ 设置任务执行定时器成功，下次执行: 60秒后 (1分钟)
2025-08-18 13:27:35.295  4254-4324  WebSocket               com.dspread.mdm.service              I  🔧 收到服务器 PONG 帧: 3
2025-08-18 13:27:38.410  4254-4254  Thread-12               com.dspread.mdm.service              W  type=1400 audit(0.0:239): avc:  denied  { getattr } for  path="/data/cache/recovery" dev="mmcblk0p47" ino=193 scontext=u:r:system_app:s0 tcontext=u:object_r:cache_recovery_file:s0 tclass=dir permissive=0
2025-08-18 13:27:38.410  4254-4254  Thread-12               com.dspread.mdm.service              W  type=1400 audit(0.0:240): avc:  denied  { getattr } for  path="/dev/block/platform/bootdevice/by-name" dev="tmpfs" ino=7300 scontext=u:r:system_app:s0 tcontext=u:object_r:block_device:s0 tclass=dir permissive=0
2025-08-18 13:27:43.410  4254-4254  Thread-12               com.dspread.mdm.service              W  type=1400 audit(0.0:241): avc:  denied  { getattr } for  path="/data/cache/recovery" dev="mmcblk0p47" ino=193 scontext=u:r:system_app:s0 tcontext=u:object_r:cache_recovery_file:s0 tclass=dir permissive=0
2025-08-18 13:27:43.410  4254-4254  Thread-12               com.dspread.mdm.service              W  type=1400 audit(0.0:242): avc:  denied  { getattr } for  path="/dev/block/platform/bootdevice/by-name" dev="tmpfs" ino=7300 scontext=u:r:system_app:s0 tcontext=u:object_r:block_device:s0 tclass=dir permissive=0
2025-08-18 13:27:48.410  4254-4254  Thread-12               com.dspread.mdm.service              W  type=1400 audit(0.0:243): avc:  denied  { getattr } for  path="/data/cache/recovery" dev="mmcblk0p47" ino=193 scontext=u:r:system_app:s0 tcontext=u:object_r:cache_recovery_file:s0 tclass=dir permissive=0
2025-08-18 13:27:48.410  4254-4254  Thread-12               com.dspread.mdm.service              W  type=1400 audit(0.0:244): avc:  denied  { getattr } for  path="/dev/block/platform/bootdevice/by-name" dev="tmpfs" ino=7300 scontext=u:r:system_app:s0 tcontext=u:object_r:block_device:s0 tclass=dir permissive=0
2025-08-18 13:27:53.420  4254-4254  Thread-12               com.dspread.mdm.service              W  type=1400 audit(0.0:245): avc:  denied  { getattr } for  path="/data/cache/recovery" dev="mmcblk0p47" ino=193 scontext=u:r:system_app:s0 tcontext=u:object_r:cache_recovery_file:s0 tclass=dir permissive=0
2025-08-18 13:27:53.420  4254-4254  Thread-12               com.dspread.mdm.service              W  type=1400 audit(0.0:246): avc:  denied  { getattr } for  path="/dev/block/platform/bootdevice/by-name" dev="tmpfs" ino=7300 scontext=u:r:system_app:s0 tcontext=u:object_r:block_device:s0 tclass=dir permissive=0
2025-08-18 13:27:58.420  4254-4254  Thread-12               com.dspread.mdm.service              W  type=1400 audit(0.0:247): avc:  denied  { getattr } for  path="/data/cache/recovery" dev="mmcblk0p47" ino=193 scontext=u:r:system_app:s0 tcontext=u:object_r:cache_recovery_file:s0 tclass=dir permissive=0
2025-08-18 13:27:58.420  4254-4254  Thread-12               com.dspread.mdm.service              W  type=1400 audit(0.0:248): avc:  denied  { getattr } for  path="/dev/block/platform/bootdevice/by-name" dev="tmpfs" ino=7300 scontext=u:r:system_app:s0 tcontext=u:object_r:block_device:s0 tclass=dir permissive=0
2025-08-18 13:28:03.420  4254-4254  Thread-12               com.dspread.mdm.service              W  type=1400 audit(0.0:249): avc:  denied  { getattr } for  path="/data/cache/recovery" dev="mmcblk0p47" ino=193 scontext=u:r:system_app:s0 tcontext=u:object_r:cache_recovery_file:s0 tclass=dir permissive=0
2025-08-18 13:28:03.420  4254-4254  Thread-12               com.dspread.mdm.service              W  type=1400 audit(0.0:250): avc:  denied  { getattr } for  path="/dev/block/platform/bootdevice/by-name" dev="tmpfs" ino=7300 scontext=u:r:system_app:s0 tcontext=u:object_r:block_device:s0 tclass=dir permissive=0
2025-08-18 13:28:04.412  4254-4254  Receiver                com.dspread.mdm.service              D  🔧 电池状态更新: 电池状态: 电量=73%, 温度=29.5°C, 健康=2, 充电=是, 低电量=否, 耗尽=否
2025-08-18 13:28:06.642  4254-4386  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineManager 检测到UpdateEngine完成
2025-08-18 13:28:06.656  4254-4254  Task                    com.dspread.mdm.service              D  🔧 OS升级完成: 79b1560a8eb14c5c958b49e8521f3624&175f338e396f458e8d5935cfadb98f60
2025-08-18 13:28:06.660  4254-4386  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineManager UpdateEngine日志监控结束
2025-08-18 13:28:06.686  4254-4254  Task                    com.dspread.mdm.service              D  🔧 更新任务状态: taskId=79b1560a8eb14c5c958b49e8521f3624&175f338e396f458e8d5935cfadb98f60, state=C03
2025-08-18 13:28:06.713  4254-4254  Task                    com.dspread.mdm.service              D  🔧 保存任务列表到存储: 2 个
2025-08-18 13:28:06.728  4254-4254  Task                    com.dspread.mdm.service              D  🔧 更新任务错误信息: taskId=79b1560a8eb14c5c958b49e8521f3624&175f338e396f458e8d5935cfadb98f60
2025-08-18 13:28:06.750  4254-4254  Task                    com.dspread.mdm.service              D  🔧 保存任务列表到存储: 2 个
2025-08-18 13:28:06.775  4254-4254  Task                    com.dspread.mdm.service              D  🔧 保存任务列表到存储: 2 个
2025-08-18 13:28:06.782  4254-4254  WebSocket               com.dspread.mdm.service              I  🔧 ST001 主动式上送: task_failed (主动: 18)
2025-08-18 13:28:06.837  4254-4254  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0108","serialNo":"01354090202503050399","request_time":"1755494886802","request_id":"1755494886802C0108","version":"1","data":{"taskId":"79b1560a8eb14c5c958b49e8521f3624&175f338e396f458e8d5935cfadb98f60","taskResult":"C03","appId":"","errorMsg":"UpdateEngine A\/B分区升级完成，即将重启"},"myVersionName":"1.0.10.20250818.DSPREAD.MDM.SERVICE","terminalDate":"20250818132806","org_request_id":"1755494713915ST001","org_request_time":"1755494713915"}
2025-08-18 13:28:06.845  4254-4254  WebSocket               com.dspread.mdm.service              I  🔧 任务结果实时上送: taskId=79b1560a8eb14c5c958b49e8521f3624&175f338e396f458e8d5935cfadb98f60, result=C03 (1)
2025-08-18 13:28:06.852  4254-4254  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineUpgradeStrategy UpdateEngine升级完成: 成功
2025-08-18 13:28:06.859  4254-4254  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineUpgradeStrategy 安排5秒后自动重启
2025-08-18 13:28:07.099  4254-4324  Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"request_time":"1755494885917","org_request_time":"1755494886802","org_request_id":"1755494886802C0108","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755494885917S0000","serialNo":"01354090202503050399"}
2025-08-18 13:28:07.115  4254-4324  WebSocket               com.dspread.mdm.service              I  🔧 收到响应消息: transCode=S0000, orgRequestId=1755494886802C0108, state=0, remark=
2025-08-18 13:28:07.124  4254-4324  WebSocket               com.dspread.mdm.service              I  🔧 处理任务结果响应: state=0
2025-08-18 13:28:07.132  4254-4324  WebSocket               com.dspread.mdm.service              I  🔧 任务结果上传成功
2025-08-18 13:28:08.420  4254-4254  Thread-12               com.dspread.mdm.service              W  type=1400 audit(0.0:260): avc:  denied  { getattr } for  path="/data/cache/recovery" dev="mmcblk0p47" ino=193 scontext=u:r:system_app:s0 tcontext=u:object_r:cache_recovery_file:s0 tclass=dir permissive=0
2025-08-18 13:28:08.420  4254-4254  Thread-12               com.dspread.mdm.service              W  type=1400 audit(0.0:261): avc:  denied  { getattr } for  path="/dev/block/platform/bootdevice/by-name" dev="tmpfs" ino=7300 scontext=u:r:system_app:s0 tcontext=u:object_r:block_device:s0 tclass=dir permissive=0
2025-08-18 13:28:11.869  4254-4877  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineUpgradeStrategy 开始执行重启...
2025-08-18 13:28:11.888  4254-4877  Platform                com.dspread.mdm.service              I  ℹ️ Attempting to reboot system, reason: UpdateEngine升级完成
2025-08-18 13:28:11.965  4254-4254  Receiver                com.dspread.mdm.service              D  🔧 系统即将关机
2025-08-18 13:28:11.989  4254-4254  WebSocket               com.dspread.mdm.service              I  🔧 C0202 被动式上送拒绝: device_usage_change (拒绝: 4)
2025-08-18 13:28:11.999  4254-4254  WebSocket               com.dspread.mdm.service              I  🔧 C0202 设备事件上送被流量控制阻止: 被动事件 'device_usage_change' 在平衡模式 - 重要变化下未启用
2025-08-18 13:28:12.007  4254-4254  WebSocket               com.dspread.mdm.service              I  🔧 C0902 主动式上送: system_shutdown (主动: 19)
2025-08-18 13:28:12.055  4254-4254  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0902","serialNo":"01354090202503050399","request_time":"1755494892033","request_id":"1755494892033C0902","version":"1","data":{"batteryLife":73,"batteryHealth":2,"temprature":"29.5","isCharging":"1","isLowBattery":"0","powerOff":"0","outOfBattery":"0"},"myVersionName":"1.0.10.20250818.DSPREAD.MDM.SERVICE","terminalDate":"20250818132812"}
2025-08-18 13:28:12.064  4254-4254  WebSocket               com.dspread.mdm.service              I  🔧 C0902 电池状态上送成功 (1, trigger=system_shutdown)
