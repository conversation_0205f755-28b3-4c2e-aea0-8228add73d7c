package com.dspread.mdm.service.broadcast.core

import android.content.Context
import android.content.Intent
import android.os.Bundle
import com.dspread.mdm.service.utils.log.Logger

/**
 * 统一的广播发送器
 * 用于发送所有自定义广播，避免系统"非保护广播"警告
 * 与BroadcastManager（广播接收）配对使用
 *
 */
object BroadcastSender {

    /**
     * 发送自定义广播（显式Intent，避免系统警告）
     *
     * @param context 上下文
     * @param action 广播动作
     */
    fun sendBroadcast(context: Context, action: String) {
        sendBroadcast(context, action, null as Bundle?)
    }

    /**
     * 发送带单个字符串参数的广播
     */
    fun sendBroadcast(context: Context, action: String, key: String, value: String) {
        val extras = Bundle().apply {
            putString(key, value)
        }
        sendBroadcast(context, action, extras)
    }

    /**
     * 发送带单个整数参数的广播
     */
    fun sendBroadcast(context: Context, action: String, key: String, value: Int) {
        val extras = Bundle().apply {
            putInt(key, value)
        }
        sendBroadcast(context, action, extras)
    }

    /**
     * 发送带单个长整数参数的广播
     */
    fun sendBroadcast(context: Context, action: String, key: String, value: Long) {
        val extras = Bundle().apply {
            putLong(key, value)
        }
        sendBroadcast(context, action, extras)
    }

    /**
     * 发送带单个布尔参数的广播
     */
    fun sendBroadcast(context: Context, action: String, key: String, value: Boolean) {
        val extras = Bundle().apply {
            putBoolean(key, value)
        }
        sendBroadcast(context, action, extras)
    }

    /**
     * 发送带多个参数的广播
     */
    fun sendBroadcast(context: Context, action: String, vararg pairs: Pair<String, Any>) {
        val extras = Bundle().apply {
            pairs.forEach { (key, value) ->
                when (value) {
                    is String -> putString(key, value)
                    is Int -> putInt(key, value)
                    is Long -> putLong(key, value)
                    is Boolean -> putBoolean(key, value)
                    is Float -> putFloat(key, value)
                    is Double -> putDouble(key, value)
                    else -> putString(key, value.toString())
                }
            }
        }
        sendBroadcast(context, action, extras)
    }

    /**
     * 发送带Bundle参数的广播（核心方法）
     */
    fun sendBroadcast(context: Context, action: String, extras: Bundle?) {
        try {
            val intent = Intent(action).apply {
                // 使用显式Intent，指定包名避免系统警告
                setPackage(context.packageName)

                // 添加额外数据
                extras?.let { putExtras(it) }
            }

            context.sendBroadcast(intent)
            // 只记录关键广播的发送，减少日志冗余
            val isKeyAction = action in listOf(
                BroadcastActions.ACTION_PROVISIONING_TIMER,
                BroadcastActions.CHECK_SELF_UPDATE_STATUS
            )
            if (isKeyAction) {
                Logger.receiver("发送广播: $action")
            }

        } catch (e: Exception) {
            Logger.receiverE("发送广播失败: $action", e)
        }
    }

    /**
     * 发送带参数和 flags 的广播
     *
     * @param context 上下文
     * @param action 广播动作
     * @param flags 要设置在 Intent 上的标志位（如 Intent.FLAG_ACTIVITY_NEW_TASK）
     * @param pairs 可变参数键值对，将作为 extras 附带
     */
    fun sendBroadcast(
        context: Context,
        action: String,
        flags: Int = 0,
        vararg pairs: Pair<String, Any>
    ) {
        val intent = Intent(action).apply {
            if (flags != 0) {
                this.flags = flags
            }
            pairs.forEach { (key, value) ->
                when (value) {
                    is String -> putExtra(key, value)
                    is Int -> putExtra(key, value)
                    is Long -> putExtra(key, value)
                    is Boolean -> putExtra(key, value)
                    is Float -> putExtra(key, value)
                    is Double -> putExtra(key, value)
                    else -> putExtra(key, value.toString())
                }
            }
        }
        context.sendBroadcast(intent)
    }

    /**
     * 创建用于动态广播的PendingIntent
     * 用于AlarmManager等需要PendingIntent的场景，广播会被动态注册的接收器接收
     */
    fun createDynamicPendingIntent(
        context: Context,
        action: String,
        requestCode: Int,
        flags: Int = android.app.PendingIntent.FLAG_UPDATE_CURRENT or android.app.PendingIntent.FLAG_IMMUTABLE
    ): android.app.PendingIntent {
        val intent = Intent(action).apply {
            setPackage(context.packageName)
        }
        return android.app.PendingIntent.getBroadcast(context, requestCode, intent, flags)
    }

    /**
     * 创建用于静态广播的PendingIntent
     * 明确指定接收器类名，用于静态注册的广播接收器
     */
    fun createStaticPendingIntent(
        context: Context,
        className: String,
        action: String,
        requestCode: Int,
        flags: Int = android.app.PendingIntent.FLAG_UPDATE_CURRENT or android.app.PendingIntent.FLAG_IMMUTABLE
    ): android.app.PendingIntent {
        val intent = Intent(action).apply {
            setPackage(context.packageName)
            setClassName(context.packageName, className)
        }
        return android.app.PendingIntent.getBroadcast(context, requestCode, intent, flags)
    }

}
