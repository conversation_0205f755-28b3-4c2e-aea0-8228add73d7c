/*
 * Copyright (C) 2010 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.bbpos.wiseapp.settings.activity;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.net.wifi.WifiManager;
import android.provider.Settings;
import android.util.Log;
import android.widget.CompoundButton;
import android.widget.TextView;
import android.widget.Toast;

import com.bbpos.wiseapp.logger.BBLog;
import com.bbpos.wiseapp.service.R;
import com.bbpos.wiseapp.settings.utils.WirelessUtil;
import com.bbpos.wiseapp.settings.widget.ToggleSwitch;

public class WifiEnabler implements ToggleSwitch.OnCheckedChangeListener  {
    private static final String TAG = "WifiEnabler";
    private Context mContext;
    private ToggleSwitch mSwitchBar;
    private TextView mTVStatus;
    private boolean mListeningToOnSwitchChange = false;

    private final WifiManager mWifiManager;
    private boolean mStateMachineEvent;

    public WifiEnabler(Context context, ToggleSwitch switchBar, TextView status) {
        mContext = context;
        mSwitchBar = switchBar;
        mTVStatus = status;
        mWifiManager = (WifiManager) context.getApplicationContext().getSystemService(Context.WIFI_SERVICE);
        setupSwitchBar();
    }

    public void setupSwitchBar() {
        final int state = mWifiManager.getWifiState();
        handleWifiStateChanged(state);
        if (!mListeningToOnSwitchChange) {
            mSwitchBar.setOnCheckedChangeListener(this);
            mListeningToOnSwitchChange = true;
        }
    }

    public void teardownSwitchBar() {
        if (mListeningToOnSwitchChange) {
            mSwitchBar.setOnCheckedChangeListener(null);
            mListeningToOnSwitchChange = false;
        }
    }

    public void resume(Context context) {
        mContext = context;
        // Wi-Fi state is sticky, so just let the receiver update UI
        if (!mListeningToOnSwitchChange) {
            mSwitchBar.setOnCheckedChangeListener(this);
            mListeningToOnSwitchChange = true;
        }
    }

    public void pause() {
        if (mListeningToOnSwitchChange) {
            mSwitchBar.setOnCheckedChangeListener(null);
            mListeningToOnSwitchChange = false;
        }
    }

    public void handleWifiStateChanged(int state) {
        BBLog.d(BBLog.TAG, "handleWifiStateChanged, state = " + state);
        // Clear any previous state
        switch (state) {
            case WifiManager.WIFI_STATE_ENABLING:
                break;
            case WifiManager.WIFI_STATE_ENABLED:
                setSwitchBarChecked(true);
                break;
            case WifiManager.WIFI_STATE_DISABLING:
                break;
            case WifiManager.WIFI_STATE_DISABLED:
                setSwitchBarChecked(false);
                break;
            default:
                setSwitchBarChecked(false);
        }
    }

    private void setSwitchBarChecked(boolean checked) {
        BBLog.d(BBLog.TAG, "setSwitchChecked, checked = " + checked); //M
        mStateMachineEvent = true;
        mSwitchBar.setChecked(checked);
        if (checked) {
            mTVStatus.setText(mContext.getString(R.string.on));
            mWifiManager.startScan();
        } else {
            mTVStatus.setText(mContext.getString(R.string.off));
        }
        mStateMachineEvent = false;
    }

    @Override
    public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
        BBLog.d(BBLog.TAG, "onCheckedChanged, isChecked = " + isChecked);
        //Do nothing if called as a result of a state machine event
        if (mStateMachineEvent) {
            return;
        }

        // Show toast message if Wi-Fi is not allowed in airplane mode
        if (isChecked && !WirelessUtil.isRadioAllowed(mContext, Settings.Global.RADIO_WIFI)) {
            Toast.makeText(mContext, R.string.wifi_in_airplane_mode, Toast.LENGTH_SHORT).show();
            // Reset switch to off. No infinite check/listenenr loop.
            mSwitchBar.setChecked(false);
            return;
        }


        BBLog.d(BBLog.TAG, "onCheckedChanged, setWifiEnabled = " + isChecked);
        if (!mWifiManager.setWifiEnabled(isChecked)) {
            // Error
            mSwitchBar.setEnabled(true);
            Toast.makeText(mContext, R.string.wifi_error, Toast.LENGTH_SHORT).show();
        }
    }
}
