package com.dspread.mdm.service.network.websocket.message.handler

import android.content.Context
import com.dspread.mdm.service.utils.log.Logger
import com.dspread.mdm.service.utils.storage.PreferencesManager
import org.json.JSONObject

/**
 * 响应处理器
 * 处理 S0000 响应消息
 */
class ResponseHandler(context: Context) : BaseMessageHandler(context) {

    companion object {
        // 响应状态
        private const val RESPONSE_SUCCESS = "0"    // 成功
        private const val RESPONSE_FAILED = "1"     // 失败
        private const val RESPONSE_PARTIAL = "2"    // 部分成功
    }

    override fun handleMessage(message: String) {
        val jsonObject = parseMessage(message) ?: return
        val messageInfo = getMessageInfo(jsonObject)
        
        try {
            val orgRequestId = jsonObject.optString("org_request_id")
            val responseState = jsonObject.optString("response_state")
            val responseRemark = jsonObject.optString("response_remark")
            
            Logger.wsm("收到响应消息: transCode=${messageInfo.tranCode}, orgRequestId=$orgRequestId, state=$responseState, remark=$responseRemark")
            
            // 根据原始请求ID处理不同类型的响应
            handleResponseByRequestId(orgRequestId, responseState, responseRemark, jsonObject)
            
        } catch (e: Exception) {
            Logger.wsmE("处理响应消息失败", e)
        }
    }

    /**
     * 根据原始请求ID处理响应
     */
    private fun handleResponseByRequestId(
        orgRequestId: String,
        responseState: String,
        responseRemark: String,
        jsonObject: JSONObject
    ) {
        try {
            // 从请求ID中提取事务代码（通常在ID末尾）
            when (val tranCode = extractTranCodeFromRequestId(orgRequestId)) {
                "C0108" -> handleTaskResultResponse(responseState, responseRemark, jsonObject)
                "C0107" -> handleRuleResultResponse(responseState, responseRemark, jsonObject)
                "C0109" -> handleTerminalInfoResponse(responseState, responseRemark, jsonObject)
                "C0201" -> handleDeviceStatusResponse(responseState, responseRemark, jsonObject)
                "C0202" -> handleDeviceEventResponse(responseState, responseRemark, jsonObject)
                "C0901" -> handleAppInfoResponse(responseState, responseRemark, jsonObject)
                "C0902" -> handleBatteryStatusResponse(responseState, responseRemark, jsonObject)
                "C0903" -> handleDataInfoResponse(responseState, responseRemark, jsonObject)
                "C0904" -> handleNetworkStatusResponse(responseState, responseRemark, jsonObject)
                "C0906" -> handleDeviceInfoResponse(responseState, responseRemark, jsonObject)
                else -> {
                    Logger.wsmW("未知的响应类型: $tranCode for requestId: $orgRequestId")
                }
            }
            
        } catch (e: Exception) {
            Logger.wsmE("根据请求ID处理响应失败", e)
        }
    }

    /**
     * 从请求ID中提取事务代码
     */
    private fun extractTranCodeFromRequestId(requestId: String): String {
        return try {
            // 假设请求ID格式为: timestamp + tranCode
            // 例如: 1234567890123C0108
            if (requestId.length >= 5) {
                requestId.takeLast(5) // 取最后5位作为事务代码
            } else {
                ""
            }
        } catch (e: Exception) {
            Logger.wsmE("提取事务代码失败: $requestId", e)
            ""
        }
    }

    /**
     * 处理任务结果响应
     */
    private fun handleTaskResultResponse(responseState: String, responseRemark: String, jsonObject: JSONObject) {
        Logger.wsm("处理任务结果响应: state=$responseState")
        
        when (responseState) {
            RESPONSE_SUCCESS -> {
                Logger.wsm("任务结果上传成功")
            }
            RESPONSE_FAILED -> {
                Logger.wsmE("任务结果上传失败: $responseRemark")
            }
            RESPONSE_PARTIAL -> {
                Logger.wsmW("任务结果部分成功: $responseRemark")
            }
        }
    }

    /**
     * 处理规则结果响应
     */
    private fun handleRuleResultResponse(responseState: String, responseRemark: String, jsonObject: JSONObject) {
        Logger.wsm("处理规则结果响应: state=$responseState")
        
        when (responseState) {
            RESPONSE_SUCCESS -> {
                Logger.wsm("规则结果上传成功")
            }
            RESPONSE_FAILED -> {
                Logger.wsmE("规则结果上传失败: $responseRemark")
            }
            RESPONSE_PARTIAL -> {
                Logger.wsmW("规则结果部分成功: $responseRemark")
            }
        }
    }

    /**
     * 处理终端信息响应
     */
    private fun handleTerminalInfoResponse(responseState: String, responseRemark: String, jsonObject: JSONObject) {
//        Logger.wsm("处理终端信息响应: state=$responseState")

        when (responseState) {
            RESPONSE_SUCCESS -> {
                Logger.wsm("终端信息上传成功")
                // 设置终端信息上传成功标志
                try {
                    PreferencesManager.putString("flag_last_ter_info_upload_alive", "true")
                    Logger.wsm("终端信息上传成功标志已设置")
                } catch (e: Exception) {
                    Logger.wsmE("设置终端信息上传标志失败", e)
                }
            }
            RESPONSE_FAILED -> {
                Logger.wsmE("终端信息上传失败: $responseRemark")
            }
            RESPONSE_PARTIAL -> {
                Logger.wsmW("终端信息部分成功: $responseRemark")
            }
        }
    }

    /**
     * 处理设备状态响应
     */
    private fun handleDeviceStatusResponse(responseState: String, responseRemark: String, jsonObject: JSONObject) {
        Logger.wsm("处理设备状态响应: state=$responseState")
        
        when (responseState) {
            RESPONSE_SUCCESS -> {
                Logger.wsm("设备状态上传成功")
            }
            RESPONSE_FAILED -> {
                Logger.wsmE("设备状态上传失败: $responseRemark")
            }
        }
    }

    /**
     * 处理设备事件响应
     */
    private fun handleDeviceEventResponse(responseState: String, responseRemark: String, jsonObject: JSONObject) {
        Logger.wsm("处理设备事件响应: state=$responseState")
        
        when (responseState) {
            RESPONSE_SUCCESS -> {
                Logger.wsm("设备事件上传成功")
            }
            RESPONSE_FAILED -> {
                Logger.wsmE("设备事件上传失败: $responseRemark")
            }
        }
    }

    /**
     * 处理应用信息响应
     */
    private fun handleAppInfoResponse(responseState: String, responseRemark: String, jsonObject: JSONObject) {
        Logger.wsm("处理应用信息响应: state=$responseState")
        
        when (responseState) {
            RESPONSE_SUCCESS -> {
                Logger.wsm("应用信息上传成功")
            }
            RESPONSE_FAILED -> {
                Logger.wsmE("应用信息上传失败: $responseRemark")
            }
        }
    }

    /**
     * 处理电池状态响应
     */
    private fun handleBatteryStatusResponse(responseState: String, responseRemark: String, jsonObject: JSONObject) {
        Logger.wsm("处理电池状态响应: state=$responseState")
        
        when (responseState) {
            RESPONSE_SUCCESS -> {
                Logger.wsm("电池状态上传成功")
            }
            RESPONSE_FAILED -> {
                Logger.wsmE("电池状态上传失败: $responseRemark")
            }
        }
    }

    /**
     * 处理数据信息响应
     */
    private fun handleDataInfoResponse(responseState: String, responseRemark: String, jsonObject: JSONObject) {
        Logger.wsm("处理数据信息响应: state=$responseState")
        
        when (responseState) {
            RESPONSE_SUCCESS -> {
                Logger.wsm("数据信息上传成功")
            }
            RESPONSE_FAILED -> {
                Logger.wsmE("数据信息上传失败: $responseRemark")
            }
        }
    }

    /**
     * 处理网络状态响应
     */
    private fun handleNetworkStatusResponse(responseState: String, responseRemark: String, jsonObject: JSONObject) {
        Logger.wsm("处理网络状态响应: state=$responseState")
        
        when (responseState) {
            RESPONSE_SUCCESS -> {
                Logger.wsm("网络状态上传成功")
            }
            RESPONSE_FAILED -> {
                Logger.wsmE("网络状态上传失败: $responseRemark")
            }
        }
    }

    /**
     * 处理设备信息响应
     */
    private fun handleDeviceInfoResponse(responseState: String, responseRemark: String, jsonObject: JSONObject) {
        Logger.wsm("处理设备信息响应: state=$responseState")
        
        when (responseState) {
            RESPONSE_SUCCESS -> {
                Logger.wsm("设备信息上传成功")
            }
            RESPONSE_FAILED -> {
                Logger.wsmE("设备信息上传失败: $responseRemark")
            }
        }
    }
}
