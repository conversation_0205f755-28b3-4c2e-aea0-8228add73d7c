---------------------------- PROCESS ENDED (18345) for package com.dspread.mdm.service ----------------------------
2025-08-14 18:08:43.430   644-919   BufferQueueDebug        surfaceflinger                       E  [ActivityRecord{65e2acf u0 com.dspread.mdm.service/.ui.activity.TestActivity#5012](this:0xaf077c40,id:-1,api:0,p:-1,c:-1) id info cannot be read from 'ActivityRecord{65e2acf u0 com.dspread.mdm.service/.ui.activity.TestActivity#5012'
2025-08-14 18:08:43.459   644-919   BufferQueueDebug        surfaceflinger                       E  [b2718e1 Splash Screen com.dspread.mdm.service#5013](this:0xaefaec40,id:-1,api:0,p:-1,c:-1) id info cannot be read from 'b2718e1 Splash Screen com.dspread.mdm.service#5013'
2025-08-14 18:08:43.471   644-919   BufferQueueDebug        surfaceflinger                       E  [Splash Screen com.dspread.mdm.service#5014](this:0xaef84c40,id:-1,api:0,p:-1,c:-1) id info cannot be read from 'Splash Screen com.dspread.mdm.service#5014'
2025-08-14 18:08:43.506   644-1292  BufferQueueDebug        surfaceflinger                       E  [2818f2e ActivityRecordInputSink com.dspread.mdm.service/.ui.activity.TestActivity#5018](this:0xaeefcc40,id:-1,api:0,p:-1,c:-1) id info cannot be read from '2818f2e ActivityRecordInputSink com.dspread.mdm.service/.ui.activity.TestActivity#5018'
---------------------------- PROCESS STARTED (18617) for package com.dspread.mdm.service ----------------------------
2025-08-14 18:08:43.640 18617-18617 ziparchive              com.dspread.mdm.service              W  Unable to open '/data/app/~~zmDE2geUVIjYDsFCMK1rcA==/com.dspread.mdm.service-5_0U4ygCU_ddyAKoqckuyQ==/base.dm': No such file or directory
2025-08-14 18:08:44.059 18617-18617 nativeloader            com.dspread.mdm.service              D  Configuring clns-4 for other apk /data/app/~~zmDE2geUVIjYDsFCMK1rcA==/com.dspread.mdm.service-5_0U4ygCU_ddyAKoqckuyQ==/base.apk. target_sdk_version=34, uses_libraries=, library_path=/data/app/~~zmDE2geUVIjYDsFCMK1rcA==/com.dspread.mdm.service-5_0U4ygCU_ddyAKoqckuyQ==/lib/arm, permitted_path=/data:/mnt/expand:/data/user/0/com.dspread.mdm.service
2025-08-14 18:08:44.085 18617-18617 GraphicsEnvironment     com.dspread.mdm.service              V  Currently set values for:
2025-08-14 18:08:44.085 18617-18617 GraphicsEnvironment     com.dspread.mdm.service              V    angle_gl_driver_selection_pkgs=[]
2025-08-14 18:08:44.085 18617-18617 GraphicsEnvironment     com.dspread.mdm.service              V    angle_gl_driver_selection_values=[]
2025-08-14 18:08:44.087 18617-18617 GraphicsEnvironment     com.dspread.mdm.service              V  ANGLE GameManagerService for com.dspread.mdm.service: false
2025-08-14 18:08:44.087 18617-18617 GraphicsEnvironment     com.dspread.mdm.service              V  com.dspread.mdm.service is not listed in per-application setting
2025-08-14 18:08:44.087 18617-18617 GraphicsEnvironment     com.dspread.mdm.service              V  Neither updatable production driver nor prerelease driver is supported.
2025-08-14 18:08:44.113 18617-18617 Compatibil...geReporter com.dspread.mdm.service              D  Compat change id reported: 183155436; UID 1000; state: ENABLED
2025-08-14 18:08:44.132 18617-18617 MbrainDebugManagerImpl  com.dspread.mdm.service              D  getService failed
2025-08-14 18:08:44.217 18617-18617 Common                  com.dspread.mdm.service              D  🔧 日志配置：生产环境模式
2025-08-14 18:08:44.227 18617-18617 Common                  com.dspread.mdm.service              D  🔧 [DebugConfig] 定时器调试模式已禁用
2025-08-14 18:08:44.234 18617-18617 Common                  com.dspread.mdm.service              D  🔧 TimerConfig ========== 当前定时器配置 (生产模式) ==========
2025-08-14 18:08:44.248 18617-18617 Provisioning            com.dspread.mdm.service              D  🔧 主目录 已存在: /data/pos/config
2025-08-14 18:08:44.254 18617-18617 Provisioning            com.dspread.mdm.service              I  ℹ️ 使用主配置目录: /data/pos/config/
2025-08-14 18:08:44.260 18617-18617 Provisioning            com.dspread.mdm.service              I  ℹ️ 配置路径初始化完成: 系统目录
2025-08-14 18:08:44.276 18617-18617 Provisioning            com.dspread.mdm.service              D  🔧 状态标志加载成功: /data/pos/config/provisioning_flags.json
2025-08-14 18:08:44.303 18617-18617 Common                  com.dspread.mdm.service              D  🔧 TimerConfig 使用Provisioning配置的心跳间隔: 300秒
2025-08-14 18:08:44.309 18617-18617 Common                  com.dspread.mdm.service              D  🔧 TimerConfig ①心跳定时器: 300秒
2025-08-14 18:08:44.328 18617-18617 Common                  com.dspread.mdm.service              D  🔧 TimerConfig 使用Provisioning配置的终端信息上传间隔: 900秒
2025-08-14 18:08:44.335 18617-18617 Common                  com.dspread.mdm.service              D  🔧 TimerConfig ②终端信息上传: 900秒
2025-08-14 18:08:44.341 18617-18617 Common                  com.dspread.mdm.service              D  🔧 TimerConfig ③任务执行: 60秒
2025-08-14 18:08:44.348 18617-18617 Common                  com.dspread.mdm.service              D  🔧 TimerConfig ④服务守护: 120秒
2025-08-14 18:08:44.354 18617-18617 Common                  com.dspread.mdm.service              D  🔧 TimerConfig ⑤Provisioning: 43200秒
2025-08-14 18:08:44.360 18617-18617 Common                  com.dspread.mdm.service              D  🔧 TimerConfig ===============================================
2025-08-14 18:08:44.367 18617-18617 Common                  com.dspread.mdm.service              D  🔧 日志配置：生产环境模式
2025-08-14 18:08:44.373 18617-18617 Common                  com.dspread.mdm.service              D  🔧 [DebugConfig] 调试模式已禁用
2025-08-14 18:08:44.379 18617-18617 Common                  com.dspread.mdm.service              D  🔧 [DebugConfig] ========== 当前日志配置 ==========
2025-08-14 18:08:44.530 18617-18617 Common                  com.dspread.mdm.service              D  🔧 [DebugConfig]             ========== 统一配置状态 (生产模式) ==========
                                                                                                    
                                                                                                                LogStream配置 (生产模式):
                                                                                                    启用状态: true
                                                                                                    单个日志文件: 5MB
                                                                                                    压缩文件总限制: 250MB
                                                                                                    原始日志总限制: 250MB
                                                                                                    Recent日志大小: 128KB
                                                                                                    上传URL: https://siot-log01.s3.ap-northeast-1.amazonaws.com/log/
                                                                                                    
                                                                                                                Provisioning配置 (生产模式):
                                                                                                    配置目录: /data/pos/config/
                                                                                                    媒体目录: /data/pos/media/
                                                                                                    Logo目录: /data/pos/media/logo/
                                                                                                    开机动画目录: /data/pos/media/bootanimation/
                                                                                                    
                                                                                                                ========== 日志配置状态 ==========
                                                                                                    全局开关: true
                                                                                                    
                                                                                                    模块开关:
                                                                                                      Common: true
                                                                                                      HTTPS: true
                                                                                                      WebSocket: true
                                                                                                      API: true
                                                                                                      Service: true
                                                                                                      Provisioning: true
                                                                                                      Task: true
                                                                                                      RuleBase: true
                                                                                                      Application: true
                                                                                                      LogStream: false
                                                                                                      RemoteView: false
                                                                                                      Wi-Fi: false
                                                                                                      APN: false
                                                                                                      GeoFence: false
                                                                                                      Receiver: true
                                                                                                    ================================
                                                                                                                =========================================
2025-08-14 18:08:44.536 18617-18617 Common                  com.dspread.mdm.service              D  🔧 [DebugConfig] =====================================
2025-08-14 18:08:44.542 18617-18617 Common                  com.dspread.mdm.service              D  🔧 [DebugConfig] 统一配置管理器初始化完成，当前模式: 生产模式
2025-08-14 18:08:44.548 18617-18617 Common                  com.dspread.mdm.service              D  🔧 [DebugConfig] 定时器调试模式已禁用
2025-08-14 18:08:44.554 18617-18617 Common                  com.dspread.mdm.service              D  🔧 TimerConfig ========== 当前定时器配置 (生产模式) ==========
2025-08-14 18:08:44.571 18617-18617 Common                  com.dspread.mdm.service              D  🔧 TimerConfig 使用Provisioning配置的心跳间隔: 300秒
2025-08-14 18:08:44.577 18617-18617 Common                  com.dspread.mdm.service              D  🔧 TimerConfig ①心跳定时器: 300秒
2025-08-14 18:08:44.594 18617-18617 Common                  com.dspread.mdm.service              D  🔧 TimerConfig 使用Provisioning配置的终端信息上传间隔: 900秒
2025-08-14 18:08:44.600 18617-18617 Common                  com.dspread.mdm.service              D  🔧 TimerConfig ②终端信息上传: 900秒
2025-08-14 18:08:44.606 18617-18617 Common                  com.dspread.mdm.service              D  🔧 TimerConfig ③任务执行: 60秒
2025-08-14 18:08:44.612 18617-18617 Common                  com.dspread.mdm.service              D  🔧 TimerConfig ④服务守护: 120秒
2025-08-14 18:08:44.618 18617-18617 Common                  com.dspread.mdm.service              D  🔧 TimerConfig ⑤Provisioning: 43200秒
2025-08-14 18:08:44.624 18617-18617 Common                  com.dspread.mdm.service              D  🔧 TimerConfig ===============================================
2025-08-14 18:08:44.630 18617-18617 Common                  com.dspread.mdm.service              D  🔧 日志配置：生产环境模式
2025-08-14 18:08:44.636 18617-18617 Common                  com.dspread.mdm.service              D  🔧 [DebugConfig] 调试模式已禁用
2025-08-14 18:08:44.641 18617-18617 Common                  com.dspread.mdm.service              D  🔧 [DebugConfig] ========== 当前日志配置 ==========
2025-08-14 18:08:44.743 18617-18617 Common                  com.dspread.mdm.service              D  🔧 [DebugConfig]             ========== 统一配置状态 (生产模式) ==========
                                                                                                    
                                                                                                                LogStream配置 (生产模式):
                                                                                                    启用状态: true
                                                                                                    单个日志文件: 5MB
                                                                                                    压缩文件总限制: 250MB
                                                                                                    原始日志总限制: 250MB
                                                                                                    Recent日志大小: 128KB
                                                                                                    上传URL: https://siot-log01.s3.ap-northeast-1.amazonaws.com/log/
                                                                                                    
                                                                                                                Provisioning配置 (生产模式):
                                                                                                    配置目录: /data/pos/config/
                                                                                                    媒体目录: /data/pos/media/
                                                                                                    Logo目录: /data/pos/media/logo/
                                                                                                    开机动画目录: /data/pos/media/bootanimation/
                                                                                                    
                                                                                                                ========== 日志配置状态 ==========
                                                                                                    全局开关: true
                                                                                                    
                                                                                                    模块开关:
                                                                                                      Common: true
                                                                                                      HTTPS: true
                                                                                                      WebSocket: true
                                                                                                      API: true
                                                                                                      Service: true
                                                                                                      Provisioning: true
                                                                                                      Task: true
                                                                                                      RuleBase: true
                                                                                                      Application: true
                                                                                                      LogStream: false
                                                                                                      RemoteView: false
                                                                                                      Wi-Fi: false
                                                                                                      APN: false
                                                                                                      GeoFence: false
                                                                                                      Receiver: true
                                                                                                    ================================
                                                                                                                =========================================
2025-08-14 18:08:44.749 18617-18617 Common                  com.dspread.mdm.service              D  🔧 [DebugConfig] =====================================
2025-08-14 18:08:44.754 18617-18617 Common                  com.dspread.mdm.service              D  🔧 SmartMdmServiceApp: 统一配置管理器初始化完成
2025-08-14 18:08:44.760 18617-18617 Common                  com.dspread.mdm.service              D  🔧 SmartMdmServiceApp: 开始初始化系统目录...
2025-08-14 18:08:44.766 18617-18617 Common                  com.dspread.mdm.service              D  🔧 SmartMdmServiceApp: 配置目录已存在 - /data/pos/config
2025-08-14 18:08:44.773 18617-18617 Common                  com.dspread.mdm.service              D  🔧 SmartMdmServiceApp: Logo目录创建 - 路径: /data/data/com.dspread.mdm.service/files/media/logo, 结果: true
2025-08-14 18:08:44.779 18617-18617 Common                  com.dspread.mdm.service              D  🔧 SmartMdmServiceApp: 开机动画目录创建 - 路径: /data/data/com.dspread.mdm.service/files/media/anim, 结果: true
2025-08-14 18:08:44.785 18617-18617 Common                  com.dspread.mdm.service              D  🔧 SmartMdmServiceApp: 系统目录初始化完成
2025-08-14 18:08:44.794 18617-18617 Platform                com.dspread.mdm.service              D  🔧 NetworkTrafficMonitor 初始化网络流量监控器
2025-08-14 18:08:44.805 18617-18617 Platform                com.dspread.mdm.service              D  🔧 NetworkTrafficMonitor 新的一天，重置流量统计
2025-08-14 18:08:44.810 18617-18617 Platform                com.dspread.mdm.service              D  🔧 NetworkTrafficMonitor 流量统计已重置
2025-08-14 18:08:44.816 18617-18617 Platform                com.dspread.mdm.service              D  🔧 NetworkTrafficMonitor 检测到日期变化:  -> 2025-08-14
2025-08-14 18:08:44.822 18617-18617 Platform                com.dspread.mdm.service              D  🔧 NetworkTrafficMonitor 流量统计已重置
2025-08-14 18:08:44.883 18617-18617 Platform                com.dspread.mdm.service              D  🔧 NetworkTrafficMonitor 每日上送任务已启动
2025-08-14 18:08:44.889 18617-18617 Platform                com.dspread.mdm.service              D  🔧 NetworkTrafficMonitor 网络流量监控器初始化完成
2025-08-14 18:08:44.898 18617-18617 Receiver                com.dspread.mdm.service              D  🔧 ServiceGuardEventHandler 启动服务守护定时器: 120s
2025-08-14 18:08:44.905 18617-18617 Compatibil...geReporter com.dspread.mdm.service              D  Compat change id reported: 160794467; UID 1000; state: ENABLED
2025-08-14 18:08:44.911 18617-18617 Common                  com.dspread.mdm.service              I  ✅ ⏰ 设置服务守护定时器成功，下次执行: 120秒后 (2分钟)
2025-08-14 18:08:44.916 18617-18617 Common                  com.dspread.mdm.service              D  🔧 SmartMdmServiceApp: 服务守护定时器启动成功
2025-08-14 18:08:44.923 18617-18617 Common                  com.dspread.mdm.service              D  🔧 ServiceStartupManager 收到服务启动请求: APPLICATION_STARTUP, 强制启动: false
2025-08-14 18:08:44.933 18617-18617 Common                  com.dspread.mdm.service              D  🔧 ServiceStartupManager 开始执行服务启动: APPLICATION_STARTUP
2025-08-14 18:08:44.939 18617-18617 Common                  com.dspread.mdm.service              D  🔧 ServiceManager: 准备启动后台服务
2025-08-14 18:08:44.945 18617-18617 ContextImpl             com.dspread.mdm.service              W  Calling a method in the system process without a qualified user: android.app.ContextImpl.startService:1899 android.content.ContextWrapper.startService:825 com.dspread.mdm.service.platform.manager.ServiceManager.startBackgroundService:20 com.dspread.mdm.service.platform.manager.ServiceStartupManager.performServiceStart:121 com.dspread.mdm.service.platform.manager.ServiceStartupManager.startService:66 
2025-08-14 18:08:44.949 18617-18617 Common                  com.dspread.mdm.service              D  🔧 ServiceManager: 后台服务启动成功，ComponentName: ComponentInfo{com.dspread.mdm.service/com.dspread.mdm.service.services.SmartMdmBackgroundService}
2025-08-14 18:08:45.959 18617-18617 Common                  com.dspread.mdm.service              D  🔧 ServiceStartupManager 服务启动验证成功: APPLICATION_STARTUP
2025-08-14 18:08:45.964 18617-18617 Common                  com.dspread.mdm.service              D  🔧 ServiceStartupManager 服务启动成功: APPLICATION_STARTUP
2025-08-14 18:08:45.970 18617-18617 Common                  com.dspread.mdm.service              D  🔧 SmartMdmServiceApp: 主服务启动请求已提交
2025-08-14 18:08:45.975 18617-18617 Common                  com.dspread.mdm.service              D  🔧 SmartMdmServiceApp: Application启动完成，包名: com.dspread.mdm.service
2025-08-14 18:08:45.989 18617-18617 M-ProMotion             com.dspread.mdm.service              I  M-ProMotion is disabled
2025-08-14 18:08:46.052 18617-18617 Compatibil...geReporter com.dspread.mdm.service              D  Compat change id reported: 210923482; UID 1000; state: ENABLED
2025-08-14 18:08:46.079 18617-18617 Compatibil...geReporter com.dspread.mdm.service              D  Compat change id reported: 171228096; UID 1000; state: ENABLED
2025-08-14 18:08:46.124 18617-18617 getAnimDeal             com.dspread.mdm.service              I  mode:3 packageName:com.dspread.mdm.service result: false
2025-08-14 18:08:46.131 18617-18651 PowerHalWrapper         com.dspread.mdm.service              I  PowerHalWrapper.getInstance 
2025-08-14 18:08:46.132 18617-18617 SurfaceFactory          com.dspread.mdm.service              I  [static] sSurfaceFactory = com.mediatek.view.impl.SurfaceFactoryImpl@723b5c1
2025-08-14 18:08:46.137 18617-18617 Compatibil...geReporter com.dspread.mdm.service              D  Compat change id reported: 237531167; UID 1000; state: DISABLED
2025-08-14 18:08:46.142 18617-18617 OpenGLRenderer          com.dspread.mdm.service              W  Unknown dataspace 0
2025-08-14 18:08:46.148 18617-18617 VRI[TestActivity]       com.dspread.mdm.service              D  hardware acceleration = true, forceHwAccelerated = false
2025-08-14 18:08:46.153   644-2046  BufferQueueDebug        surfaceflinger                       E  [6ef0832 com.dspread.mdm.service/com.dspread.mdm.service.ui.activity.TestActivity#5019](this:0xaef7dc40,id:-1,api:0,p:-1,c:-1) id info cannot be read from '6ef0832 com.dspread.mdm.service/com.dspread.mdm.service.ui.activity.TestActivity#5019'
2025-08-14 18:08:46.160 18617-18617 InputTransport          com.dspread.mdm.service              D  Create ARC handle: 0xb1525750
2025-08-14 18:08:46.161 18617-18617 InputEventReceiver      com.dspread.mdm.service              D  Input log is disabled in InputEventReceiver.
2025-08-14 18:08:46.161 18617-18617 InputTransport          com.dspread.mdm.service              D  Input log is disabled in InputChannel.
2025-08-14 18:08:46.165 18617-18617 Choreographer           com.dspread.mdm.service              I  Skipped 121 frames!  The application may be doing too much work on its main thread.
2025-08-14 18:08:46.203   644-2046  BufferQueueDebug        surfaceflinger                       E  [com.dspread.mdm.service/com.dspread.mdm.service.ui.activity.TestActivity#5020](this:0xaef6fc40,id:-1,api:0,p:-1,c:-1) id info cannot be read from 'com.dspread.mdm.service/com.dspread.mdm.service.ui.activity.TestActivity#5020'
2025-08-14 18:08:46.218 18617-18617 BufferQueueConsumer     com.dspread.mdm.service              D  [](id:48b900000000,api:0,p:-1,c:18617) connect: controlledByApp=false
2025-08-14 18:08:46.254 18617-18652 OpenGLRenderer          com.dspread.mdm.service              E  Unable to match the desired swap behavior.
2025-08-14 18:08:46.262 18617-18656 ion                     com.dspread.mdm.service              E  ioctl c0044901 failed with code -1: Invalid argument
2025-08-14 18:08:46.370 18617-18652 BLASTBufferQueue        com.dspread.mdm.service              D  [VRI[TestActivity]#0](f:0,a:1) acquireNextBufferLocked size=480x854 mFrameNumber=1 applyTransaction=true mTimestamp=242208422534281(auto) mPendingTransactions.size=0 graphicBufferId=79959406149637 transform=3
2025-08-14 18:08:46.376 18617-18636 OpenGLRenderer          com.dspread.mdm.service              I  Davey! duration=2225ms; Flags=1, FrameTimelineVsyncId=1104919, IntendedVsync=242206195559349, Vsync=242208216045421, InputEventId=0, HandleInputStart=242208219117435, AnimationStart=242208219142588, PerformTraversalsStart=242208219749742, DrawStart=242208307668819, FrameDeadline=242206221059349, FrameInterval=242208217838973, FrameStartTime=16698232, SyncQueued=242208321501358, SyncStart=242208324593358, IssueDrawCommandsStart=242208325118665, SwapBuffers=242208421248742, FrameCompleted=242208424418050, DequeueBufferDuration=0, QueueBufferDuration=1196693, GpuCompleted=242208424418050, SwapBuffersCompleted=242208424151281, DisplayPresentTime=848891696580112, CommandSubmissionCompleted=242208421248742, 
2025-08-14 18:08:46.379   644-2046  BufferQueueDebug        surfaceflinger                       E  [Surface(name=6ef0832 com.dspread.mdm.service/com.dspread.mdm.service.ui.activity.TestActivity)/@0xf403618 - animation-leash of starting_reveal#5023](this:0xaef09c40,id:-1,api:0,p:-1,c:-1) id info cannot be read from 'Surface(name=6ef0832 com.dspread.mdm.service/com.dspread.mdm.service.ui.activity.TestActivity)/@0xf403618 - animation-leash of starting_reveal#5023'
2025-08-14 18:08:46.392 18617-18617 Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 服务启动
2025-08-14 18:08:46.428 18617-18617 Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 开始服务初始化
2025-08-14 18:08:46.434 18617-18617 Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] Android 14+: 初始化DSPREAD服务
2025-08-14 18:08:46.444 18617-18617 Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 服务创建完成
2025-08-14 18:08:46.445 18617-18662 Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 后台线程：初始化DSPREAD服务
2025-08-14 18:08:46.452 18617-18617 Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] onStartCommand调用
2025-08-14 18:08:46.455 18617-18662 Platform                com.dspread.mdm.service              D  🔧 DspreadService 开始初始化DSPREAD服务
2025-08-14 18:08:46.464 18617-18662 ContextImpl             com.dspread.mdm.service              W  Calling a method in the system process without a qualified user: android.app.ContextImpl.bindService:1988 android.content.ContextWrapper.bindService:861 com.dspread.mdm.service.services.DspreadService.bindSysService:110 com.dspread.mdm.service.services.DspreadService.initialize:62 com.dspread.mdm.service.services.SmartMdmBackgroundService.initializeDspreadServiceAsync$lambda$1:227 
2025-08-14 18:08:46.471 18617-18617 Platform                com.dspread.mdm.service              D  🔧 WakeLockManager 获取智能WakeLock成功，持续时间: 60000ms
2025-08-14 18:08:46.476 18617-18662 Platform                com.dspread.mdm.service              D  🔧 DspreadService 系统服务绑定请求已发送
2025-08-14 18:08:46.483 18617-18617 Platform                com.dspread.mdm.service              D  🔧 WakeLockManager 设置WakeLock续期定时器: 60000ms
2025-08-14 18:08:46.514 18617-18617 Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 初始化基础组件
2025-08-14 18:08:46.527 18617-18617 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 开始初始化...
2025-08-14 18:08:46.543 18617-18617 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 包更新广播接收器注册完成
2025-08-14 18:08:46.550 18617-18617 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 统一广播接收器注册完成
2025-08-14 18:08:46.556 18617-18617 Common                  com.dspread.mdm.service              I  ✅ BroadcastManager 初始化完成
2025-08-14 18:08:46.563 18617-18617 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: NetworkEventHandler -> android.net.conn.CONNECTIVITY_CHANGE
2025-08-14 18:08:46.570 18617-18617 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: NetworkEventHandler -> android.net.wifi.STATE_CHANGE
2025-08-14 18:08:46.577 18617-18617 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: BatteryEventHandler -> android.intent.action.BATTERY_CHANGED
2025-08-14 18:08:46.583 18617-18617 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: BatteryEventHandler -> android.intent.action.BATTERY_LOW
2025-08-14 18:08:46.589 18617-18617 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: BatteryEventHandler -> android.intent.action.BATTERY_OKAY
2025-08-14 18:08:46.595 18617-18617 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: BatteryEventHandler -> android.intent.action.ACTION_POWER_CONNECTED
2025-08-14 18:08:46.602 18617-18617 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: BatteryEventHandler -> android.intent.action.ACTION_POWER_DISCONNECTED
2025-08-14 18:08:46.606 18617-18647 Platform                com.dspread.mdm.service              D  🔧 DeviceInfoApi 单例实例已创建
2025-08-14 18:08:46.610 18617-18617 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: SystemEventHandler -> android.intent.action.BOOT_COMPLETED
2025-08-14 18:08:46.616 18617-18617 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: SystemEventHandler -> android.intent.action.QUICKBOOT_POWERON
2025-08-14 18:08:46.622 18617-18617 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: SystemEventHandler -> android.intent.action.LOCKED_BOOT_COMPLETED
2025-08-14 18:08:46.629 18617-18617 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: SystemEventHandler -> android.intent.action.ACTION_SHUTDOWN
2025-08-14 18:08:46.635 18617-18617 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: SystemEventHandler -> android.intent.action.REBOOT
2025-08-14 18:08:46.642 18617-18617 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: ScreenEventHandler -> android.intent.action.SCREEN_ON
2025-08-14 18:08:46.649 18617-18617 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: ScreenEventHandler -> android.intent.action.SCREEN_OFF
2025-08-14 18:08:46.656 18617-18617 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: HeartbeatEventHandler -> com.dspread.mdm.service.POLL_TIMER_START
2025-08-14 18:08:46.667 18617-18617 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: PackageUpdateEventHandler -> android.intent.action.PACKAGE_ADDED
2025-08-14 18:08:46.674 18617-18617 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: PackageUpdateEventHandler -> android.intent.action.PACKAGE_REMOVED
2025-08-14 18:08:46.680 18617-18617 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: PackageUpdateEventHandler -> android.intent.action.PACKAGE_REPLACED
2025-08-14 18:08:46.686 18617-18617 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: PackageUpdateEventHandler -> com.dspread.mdm.service.CHECK_SELF_UPDATE_STATUS
2025-08-14 18:08:46.695 18617-18617 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: TaskExecuteEventHandler -> com.dspread.mdm.service.WSTASK_EXEC_BC
2025-08-14 18:08:46.701 18617-18617 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: TerminalInfoEventHandler -> com.dspread.mdm.service.TER_INFO_UPLOAD_BC
2025-08-14 18:08:46.707 18617-18617 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: ServiceGuardEventHandler -> com.dspread.mdm.service.SERVICE_GUARD_TIMER
2025-08-14 18:08:46.714 18617-18617 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: ServiceManagementEventHandler -> android.intent.action.USER_PRESENT
2025-08-14 18:08:46.721 18617-18617 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: ServiceManagementEventHandler -> android.intent.action.TIME_TICK
2025-08-14 18:08:46.727 18617-18617 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: ServiceManagementEventHandler -> com.dspread.mdm.service.SERVICE_RESTART
2025-08-14 18:08:46.733 18617-18617 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: ServiceManagementEventHandler -> android.net.conn.CONNECTIVITY_CHANGE
2025-08-14 18:08:46.740 18617-18617 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: WakeLockEventHandler -> com.dspread.mdm.service.WAKELOCK_RENEWAL
2025-08-14 18:08:46.746 18617-18617 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: WakeLockEventHandler -> com.dspread.mdm.service.WAKELOCK_ACQUIRE
2025-08-14 18:08:46.752 18617-18617 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: WakeLockEventHandler -> com.dspread.mdm.service.WAKELOCK_RELEASE
2025-08-14 18:08:46.761 18617-18617 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: ProvisioningEventHandler -> com.dspread.mdm.service.PROVISIONING_TIMER
2025-08-14 18:08:46.767 18617-18617 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: ProvisioningEventHandler -> android.net.conn.CONNECTIVITY_CHANGE
2025-08-14 18:08:46.773 18617-18617 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: ProvisioningEventHandler -> android.intent.action.BOOT_COMPLETED
2025-08-14 18:08:46.790   644-2046  BufferQueueDebug        surfaceflinger                       E  [Surface(name=b2718e1 Splash Screen com.dspread.mdm.service)/@0x6956fe1 - animation-leash of window_animation#5024](this:0xaee1cc40,id:-1,api:0,p:-1,c:-1) id info cannot be read from 'Surface(name=b2718e1 Splash Screen com.dspread.mdm.service)/@0x6956fe1 - animation-leash of window_animation#5024'
2025-08-14 18:08:46.821 18617-18617 Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 服务初始化完成
2025-08-14 18:08:46.844 18617-18617 Platform                com.dspread.mdm.service              D  🔧 DspreadService 系统服务连接成功: ComponentInfo{com.dspread.sdkservice/com.dspread.sdkservice.SDKSysService}
2025-08-14 18:08:46.851 18617-18617 Platform                com.dspread.mdm.service              D  🔧 DspreadService 系统服务绑定完成
2025-08-14 18:08:46.860 18617-18662 ContextImpl             com.dspread.mdm.service              W  Calling a method in the system process without a qualified user: android.app.ContextImpl.bindService:1988 android.content.ContextWrapper.bindService:861 com.dspread.mdm.service.services.DspreadService.bindDevService:165 com.dspread.mdm.service.services.DspreadService.initialize:65 com.dspread.mdm.service.services.SmartMdmBackgroundService.initializeDspreadServiceAsync$lambda$1:227 
2025-08-14 18:08:46.867 18617-18617 Receiver                com.dspread.mdm.service              D  🔧 电池状态更新: 电池状态: 电量=100%, 温度=29.7°C, 健康=2, 充电=是, 低电量=否, 耗尽=否
2025-08-14 18:08:46.870 18617-18662 Platform                com.dspread.mdm.service              D  🔧 DspreadService 设备服务绑定请求已发送
2025-08-14 18:08:46.874 18617-18617 Receiver                com.dspread.mdm.service              D  🔧 电池状态变化: 电量=100%, 温度=29°C, 充电=true
2025-08-14 18:08:46.910 18617-18617 WebSocket               com.dspread.mdm.service              I  🔧 C0902 被动式上送: battery_level_change_5 (被动: 1)
2025-08-14 18:08:46.927 18617-18617 Platform                com.dspread.mdm.service              E  ❌ DspreadService 设备服务不可用，无法获取序列号
2025-08-14 18:08:46.949 18617-18617 WebSocket               com.dspread.mdm.service              I  🔧 消息已缓存: C0902 (缓存数量: 1)
2025-08-14 18:08:46.955 18617-18617 WebSocket               com.dspread.mdm.service              I  🔧 C0902 电池状态上送成功 (1, trigger=battery_level_change_5)
2025-08-14 18:08:46.965 18617-18617 Platform                com.dspread.mdm.service              D  🔧 NetworkApi 单例实例已创建
2025-08-14 18:08:46.976 18617-18617 Receiver                com.dspread.mdm.service              D  🔧 网络状态变化: 连接=true, 类型=WIFI
2025-08-14 18:08:46.984 18617-18617 Receiver                com.dspread.mdm.service              D  🔧 网络恢复，尝试重新连接WebSocket
2025-08-14 18:08:46.990 18617-18617 Receiver                com.dspread.mdm.service              D  🔧 ServiceManagementEventHandler 处理网络变化事件
2025-08-14 18:08:47.000 18617-18617 Common                  com.dspread.mdm.service              D  🔧 ServiceStartupManager 服务正在运行，无需启动: NETWORK_CHANGE
2025-08-14 18:08:47.006 18617-18617 Receiver                com.dspread.mdm.service              D  🔧 ServiceManagementEventHandler 网络变化后服务检查完成
2025-08-14 18:08:47.012 18617-18617 Provisioning            com.dspread.mdm.service              D  🔧 ProvisioningEventHandler 网络状态变化
2025-08-14 18:08:47.018 18617-18617 Provisioning            com.dspread.mdm.service              D  🔧 ProvisioningEventHandler 忽略初始启动时的网络连接事件
2025-08-14 18:08:47.101 18617-18617 WebSocket               com.dspread.mdm.service              I  🔧 C0905 主动式上送: manual_trigger (主动: 1)
2025-08-14 18:08:47.116 18617-18617 WebSocket               com.dspread.mdm.service              I  🔧 首次上送，上送当日数据
2025-08-14 18:08:47.122 18617-18617 WebSocket               com.dspread.mdm.service              I  🔧 没有流量数据需要上送
2025-08-14 18:08:47.128 18617-18617 Common                  com.dspread.mdm.service              D  🔧 刷新时自动上送流量统计
2025-08-14 18:08:47.166 18617-18617 Platform                com.dspread.mdm.service              D  🔧 DspreadService 设备服务连接成功: ComponentInfo{com.dspread.sdkservice/com.dspread.sdkservice.SDKDevService}
2025-08-14 18:08:47.174 18617-18617 Platform                com.dspread.mdm.service              D  🔧 DspreadService 设备服务绑定完成
2025-08-14 18:08:47.204 18617-18647 Platform                com.dspread.mdm.service              D  🔧 DspreadService 解析SP版本: V1.0.5
2025-08-14 18:08:47.211 18617-18647 Platform                com.dspread.mdm.service              D  🔧 DspreadService 首次获取SP版本成功: V1.0.5 (原始: *SP_VERSION:V1.0.5#*COMMIT:2d7cc90#*DATETIME:20250...)
2025-08-14 18:08:47.282 18617-18617 Common                  com.dspread.mdm.service              D  🔧 TestActivity UI已更新: SP=V1.0.5, SN=01354090202503050399
2025-08-14 18:08:48.181 18617-18662 Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 等待DSPREAD服务就绪 (1/10): true
2025-08-14 18:08:48.187 18617-18662 Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] DSPREAD服务就绪，启动Provisioning
2025-08-14 18:08:48.192 18617-18662 Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 验证DSPREAD服务功能
2025-08-14 18:08:48.198 18617-18662 Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 设备序列号验证: 成功(01354090202503050399)
2025-08-14 18:08:48.207 18617-18662 Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 服务状态 - 系统服务:✅ 设备服务:✅
2025-08-14 18:08:48.217 18617-18662 Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 启动ProvisioningService
2025-08-14 18:08:48.229 18617-18662 Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] ProvisioningService启动完成
2025-08-14 18:08:48.229 18617-18647 Provisioning            com.dspread.mdm.service              I  ℹ️ 开始执行Provisioning配置，触发类型: FIRST_BOOT
2025-08-14 18:08:48.238 18617-18647 Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] Provisioning进度: 正在下载配置文件...
2025-08-14 18:08:48.244 18617-18647 Provisioning            com.dspread.mdm.service              I  ℹ️ 开始请求远程配置...
2025-08-14 18:08:48.252 18617-18647 Provisioning            com.dspread.mdm.service              I  ℹ️ 请求配置: http://35.75.3.206:8080/status/config?SN=MDEzNTQwOTAyMDI1MDMwNTAzOTk=&timestamp=1755166128251
2025-08-14 18:08:48.272 18617-18647 TrafficStats            com.dspread.mdm.service              D  tagSocket(115) with statsTag=0xffffffff, statsUid=-1
2025-08-14 18:08:49.905 18617-18649 Platform                com.dspread.mdm.service              D  🔧 NetworkTrafficMonitor 等待下次上送时间: 2025-08-15 00:00:00
2025-08-14 18:08:51.603 18617-18673 ProfileInstaller        com.dspread.mdm.service              D  Installing profile for com.dspread.mdm.service
2025-08-14 18:09:03.387 18617-18647 Provisioning            com.dspread.mdm.service              E  ❌ 配置API请求失败 (Ask Gemini)
                                                                                                    java.net.SocketTimeoutException: timeout
                                                                                                      at com.android.okhttp.okio.Okio$3.newTimeoutException(Okio.java:214)
                                                                                                      at com.android.okhttp.okio.AsyncTimeout.exit(AsyncTimeout.java:263)
                                                                                                      at com.android.okhttp.okio.AsyncTimeout$2.read(AsyncTimeout.java:217)
                                                                                                      at com.android.okhttp.okio.RealBufferedSource.indexOf(RealBufferedSource.java:307)
                                                                                                      at com.android.okhttp.okio.RealBufferedSource.indexOf(RealBufferedSource.java:301)
                                                                                                      at com.android.okhttp.okio.RealBufferedSource.readUtf8LineStrict(RealBufferedSource.java:197)
                                                                                                      at com.android.okhttp.internal.http.Http1xStream.readResponse(Http1xStream.java:188)
                                                                                                      at com.android.okhttp.internal.http.Http1xStream.readResponseHeaders(Http1xStream.java:129)
                                                                                                      at com.android.okhttp.internal.http.HttpEngine.readNetworkResponse(HttpEngine.java:750)
                                                                                                      at com.android.okhttp.internal.http.HttpEngine.readResponse(HttpEngine.java:622)
                                                                                                      at com.android.okhttp.internal.huc.HttpURLConnectionImpl.execute(HttpURLConnectionImpl.java:475)
                                                                                                      at com.android.okhttp.internal.huc.HttpURLConnectionImpl.getResponse(HttpURLConnectionImpl.java:411)
                                                                                                      at com.android.okhttp.internal.huc.HttpURLConnectionImpl.getResponseCode(HttpURLConnectionImpl.java:542)
                                                                                                      at com.dspread.mdm.service.modules.provisioning.ProvisioningManager$requestConfig$2.invokeSuspend(ProvisioningManager.kt:213)
                                                                                                      at com.dspread.mdm.service.modules.provisioning.ProvisioningManager$requestConfig$2.invoke(Unknown Source:8)
                                                                                                      at com.dspread.mdm.service.modules.provisioning.ProvisioningManager$requestConfig$2.invoke(Unknown Source:4)
                                                                                                      at kotlinx.coroutines.intrinsics.UndispatchedKt.startUndispatchedOrReturn(Undispatched.kt:78)
                                                                                                      at kotlinx.coroutines.BuildersKt__Builders_commonKt.withContext(Builders.common.kt:167)
                                                                                                      at kotlinx.coroutines.BuildersKt.withContext(Unknown Source:1)
                                                                                                      at com.dspread.mdm.service.modules.provisioning.ProvisioningManager.requestConfig(ProvisioningManager.kt:197)
                                                                                                      at com.dspread.mdm.service.modules.provisioning.ProvisioningManager.access$requestConfig(ProvisioningManager.kt:24)
                                                                                                      at com.dspread.mdm.service.modules.provisioning.ProvisioningManager$executeProvisioning$1.invokeSuspend(ProvisioningManager.kt:86)
                                                                                                      at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
                                                                                                      at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:108)
                                                                                                      at kotlinx.coroutines.internal.LimitedDispatcher$Worker.run(LimitedDispatcher.kt:115)
                                                                                                      at kotlinx.coroutines.scheduling.TaskImpl.run(Tasks.kt:103)
                                                                                                      at kotlinx.coroutines.scheduling.CoroutineScheduler.runSafely(CoroutineScheduler.kt:584)
                                                                                                      at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.executeTask(CoroutineScheduler.kt:793)
                                                                                                      at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.runWorker(CoroutineScheduler.kt:697)
                                                                                                      at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.run(CoroutineScheduler.kt:684)
                                                                                                    Caused by: java.net.SocketException: Socket closed
                                                                                                      at java.net.SocketInputStream.read(SocketInputStream.java:188)
                                                                                                      at java.net.SocketInputStream.read(SocketInputStream.java:143)
                                                                                                      at com.android.okhttp.okio.Okio$2.read(Okio.java:138)
                                                                                                      at com.android.okhttp.okio.AsyncTimeout$2.read(AsyncTimeout.java:213)
                                                                                                      at com.android.okhttp.okio.RealBufferedSource.indexOf(RealBufferedSource.java:307) 
                                                                                                      at com.android.okhttp.okio.RealBufferedSource.indexOf(RealBufferedSource.java:301) 
                                                                                                      at com.android.okhttp.okio.RealBufferedSource.readUtf8LineStrict(RealBufferedSource.java:197) 
                                                                                                      at com.android.okhttp.internal.http.Http1xStream.readResponse(Http1xStream.java:188) 
                                                                                                      at com.android.okhttp.internal.http.Http1xStream.readResponseHeaders(Http1xStream.java:129) 
                                                                                                      at com.android.okhttp.internal.http.HttpEngine.readNetworkResponse(HttpEngine.java:750) 
                                                                                                      at com.android.okhttp.internal.http.HttpEngine.readResponse(HttpEngine.java:622) 
                                                                                                      at com.android.okhttp.internal.huc.HttpURLConnectionImpl.execute(HttpURLConnectionImpl.java:475) 
                                                                                                      at com.android.okhttp.internal.huc.HttpURLConnectionImpl.getResponse(HttpURLConnectionImpl.java:411) 
                                                                                                      at com.android.okhttp.internal.huc.HttpURLConnectionImpl.getResponseCode(HttpURLConnectionImpl.java:542) 
                                                                                                      at com.dspread.mdm.service.modules.provisioning.ProvisioningManager$requestConfig$2.invokeSuspend(ProvisioningManager.kt:213) 
                                                                                                      at com.dspread.mdm.service.modules.provisioning.ProvisioningManager$requestConfig$2.invoke(Unknown Source:8) 
                                                                                                      at com.dspread.mdm.service.modules.provisioning.ProvisioningManager$requestConfig$2.invoke(Unknown Source:4) 
                                                                                                      at kotlinx.coroutines.intrinsics.UndispatchedKt.startUndispatchedOrReturn(Undispatched.kt:78) 
                                                                                                      at kotlinx.coroutines.BuildersKt__Builders_commonKt.withContext(Builders.common.kt:167) 
                                                                                                      at kotlinx.coroutines.BuildersKt.withContext(Unknown Source:1) 
                                                                                                      at com.dspread.mdm.service.modules.provisioning.ProvisioningManager.requestConfig(ProvisioningManager.kt:197) 
                                                                                                      at com.dspread.mdm.service.modules.provisioning.ProvisioningManager.access$requestConfig(ProvisioningManager.kt:24) 
                                                                                                      at com.dspread.mdm.service.modules.provisioning.ProvisioningManager$executeProvisioning$1.invokeSuspend(ProvisioningManager.kt:86) 
                                                                                                      at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33) 
                                                                                                      at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:108) 
                                                                                                      at kotlinx.coroutines.internal.LimitedDispatcher$Worker.run(LimitedDispatcher.kt:115) 
                                                                                                      at kotlinx.coroutines.scheduling.TaskImpl.run(Tasks.kt:103) 
                                                                                                      at kotlinx.coroutines.scheduling.CoroutineScheduler.runSafely(CoroutineScheduler.kt:584) 
                                                                                                      at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.executeTask(CoroutineScheduler.kt:793) 
                                                                                                      at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.runWorker(CoroutineScheduler.kt:697) 
                                                                                                      at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.run(CoroutineScheduler.kt:684) 
2025-08-14 18:09:03.407 18617-18647 Provisioning            com.dspread.mdm.service              W  ⚠️ 远程配置请求失败: timeout
2025-08-14 18:09:03.430 18617-18647 Provisioning            com.dspread.mdm.service              I  ℹ️ 📂 使用本地保存的配置 - CID: 10012001
2025-08-14 18:09:03.439 18617-18647 Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] Provisioning进度: 正在下载 Logo...
2025-08-14 18:09:03.449 18617-18647 Provisioning            com.dspread.mdm.service              I  ℹ️ 文件不存在，开始下载: Logo
2025-08-14 18:09:03.456 18617-18647 Provisioning            com.dspread.mdm.service              I  ℹ️ 下载: https://siot-bucket01.s3.ap-northeast-1.amazonaws.com/test/logo.bin -> /data/data/com.dspread.mdm.service/files/media/logo/logo.bin
2025-08-14 18:09:03.467 18617-18647 HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 尝试下载，第1次
2025-08-14 18:09:03.473 18617-18647 HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 开始下载
2025-08-14 18:09:03.478 18617-18647 HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader URL: https://siot-bucket01.s3.ap-northeast-1.amazonaws.com/test/logo.bin
2025-08-14 18:09:03.484 18617-18647 HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 路径: /data/data/com.dspread.mdm.service/files/media/logo/logo.bin
2025-08-14 18:09:03.490 18617-18647 HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 本地文件不存在，创建文件
2025-08-14 18:09:03.496 18617-18647 HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader allFileLength=0, 需要从服务器获取文件大小
2025-08-14 18:09:03.691 18617-18647 TrafficStats            com.dspread.mdm.service              D  tagSocket(5) with statsTag=0xffffffff, statsUid=-1
2025-08-14 18:09:04.339 18617-18647 HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader HTTP响应码: 200
2025-08-14 18:09:04.347 18617-18647 HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 服务器返回Content-Length: 4771451
2025-08-14 18:09:04.353 18617-18647 HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 使用服务器返回的文件大小: 4771451
2025-08-14 18:09:04.359 18617-18647 HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader localFile = true /data/data/com.dspread.mdm.service/files/media/logo/logo.bin
2025-08-14 18:09:05.376 18617-18649 Platform                com.dspread.mdm.service              D  🔧 NetworkTrafficMonitor 流量统计保存完成
2025-08-14 18:09:10.466 18617-18647 HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 计算MD5: logo.bin
2025-08-14 18:09:10.540 18617-18647 HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 文件: 4771451 MD5: ba1ee533924eae5c408465e7cddcbda4
2025-08-14 18:09:10.545 18617-18647 HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader MD5校验正确
2025-08-14 18:09:10.551 18617-18647 HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 清理连接资源
2025-08-14 18:09:10.556 18617-18647 Provisioning            com.dspread.mdm.service              I  ℹ️ Logo 下载完成: /data/data/com.dspread.mdm.service/files/media/logo/logo.bin
2025-08-14 18:09:10.562 18617-18647 Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] Provisioning进度: 正在下载 BootAnimation...
2025-08-14 18:09:10.569 18617-18647 Provisioning            com.dspread.mdm.service              I  ℹ️ 文件不存在，开始下载: BootAnimation
2025-08-14 18:09:10.575 18617-18647 Provisioning            com.dspread.mdm.service              I  ℹ️ 下载: https://siot-bucket01.s3.ap-northeast-1.amazonaws.com/test/bootanimation.zip -> /data/data/com.dspread.mdm.service/files/media/anim/bootanimation.zip
2025-08-14 18:09:10.581 18617-18647 HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 尝试下载，第1次
2025-08-14 18:09:10.586 18617-18647 HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 开始下载
2025-08-14 18:09:10.592 18617-18647 HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader URL: https://siot-bucket01.s3.ap-northeast-1.amazonaws.com/test/bootanimation.zip
2025-08-14 18:09:10.597 18617-18647 HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 路径: /data/data/com.dspread.mdm.service/files/media/anim/bootanimation.zip
2025-08-14 18:09:10.604 18617-18647 HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 本地文件不存在，创建文件
2025-08-14 18:09:10.610 18617-18647 HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader allFileLength=0, 需要从服务器获取文件大小
2025-08-14 18:09:10.861 18617-18647 HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader HTTP响应码: 200
2025-08-14 18:09:10.868 18617-18647 HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 服务器返回Content-Length: 4771451
2025-08-14 18:09:10.873 18617-18647 HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 使用服务器返回的文件大小: 4771451
2025-08-14 18:09:10.879 18617-18647 HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader localFile = true /data/data/com.dspread.mdm.service/files/media/anim/bootanimation.zip
2025-08-14 18:09:16.968 18617-18647 HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 计算MD5: bootanimation.zip
2025-08-14 18:09:17.054 18617-18647 HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 文件: 4771451 MD5: ba1ee533924eae5c408465e7cddcbda4
2025-08-14 18:09:17.059 18617-18647 HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader MD5校验正确
2025-08-14 18:09:17.065 18617-18647 HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 清理连接资源
2025-08-14 18:09:17.071 18617-18647 Provisioning            com.dspread.mdm.service              I  ℹ️ BootAnimation 下载完成: /data/data/com.dspread.mdm.service/files/media/anim/bootanimation.zip
2025-08-14 18:09:17.076 18617-18647 Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] Provisioning进度: 正在应用配置...
2025-08-14 18:09:17.082 18617-18647 Provisioning            com.dspread.mdm.service              I  ℹ️ 配置应用完成
2025-08-14 18:09:17.088 18617-18647 Provisioning            com.dspread.mdm.service              W  ⚠️ 远程配置失败，首次配置状态保持未完成，下次启动将重试
2025-08-14 18:09:17.096 18617-18647 Provisioning            com.dspread.mdm.service              D  🔧 状态标志保存成功: /data/pos/config/provisioning_flags.json
2025-08-14 18:09:17.101 18617-18647 Provisioning            com.dspread.mdm.service              I  ℹ️ Provisioning配置执行完成
2025-08-14 18:09:17.107 18617-18647 Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] Provisioning完成，启动核心组件
2025-08-14 18:09:17.113 18617-18617 Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 开始初始化核心组件
2025-08-14 18:09:17.135 18617-18617 WebSocket               com.dspread.mdm.service              I  🔧 开始初始化 WebSocket 组件...
2025-08-14 18:09:17.146 18617-18617 WebSocket               com.dspread.mdm.service              I  🔧 初始化 WebSocket 密钥管理器
2025-08-14 18:09:17.156 18617-18617 WebSocket               com.dspread.mdm.service              I  🔧 生成客户端公私钥对
2025-08-14 18:09:17.290 18617-18617 Common                  com.dspread.mdm.service              I  ✅ WebSocket 密钥管理器初始化完成
2025-08-14 18:09:17.299 18617-18617 WebSocket               com.dspread.mdm.service              I  🔧 开始初始化 WsMessageCenter...
2025-08-14 18:09:17.305 18617-18617 WebSocket               com.dspread.mdm.service              I  🔧 WsMessageCenter 首次初始化
2025-08-14 18:09:17.322 18617-18617 WebSocket               com.dspread.mdm.service              I  🔧 WsTaskManager 初始化完成
2025-08-14 18:09:17.422 18617-18617 WebSocket               com.dspread.mdm.service              I  🔧 WsMessageCenter 初始化完成
2025-08-14 18:09:17.428 18617-18617 WebSocket               com.dspread.mdm.service              I  🔧 开始清理终态任务，当前任务数量: 0
2025-08-14 18:09:17.433 18617-18617 WebSocket               com.dspread.mdm.service              I  🔧 没有需要清理的终态任务
2025-08-14 18:09:17.439 18617-18617 WebSocket               com.dspread.mdm.service              I  🔧 开始重置任务状态，当前任务个数: 0
2025-08-14 18:09:17.445 18617-18617 WebSocket               com.dspread.mdm.service              I  🔧 没有需要重置的任务
2025-08-14 18:09:17.450 18617-18617 Common                  com.dspread.mdm.service              I  ✅ 任务状态恢复完成
2025-08-14 18:09:17.466 18617-18617 WebSocket               com.dspread.mdm.service              I  🔧 使用Provisioning配置的WebSocket URL: ws://35.75.3.206:8080/status/websocket/register
2025-08-14 18:09:17.472 18617-18617 WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接管理器初始化完成
2025-08-14 18:09:17.478 18617-18617 Common                  com.dspread.mdm.service              I  ✅ WebSocket 管理器初始化完成
2025-08-14 18:09:17.483 18617-18617 WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 管理器初始化完成
2025-08-14 18:09:17.490 18617-18617 Common                  com.dspread.mdm.service              I  ✅ WebSocket 组件初始化完成
2025-08-14 18:09:17.496 18617-18617 WebSocket               com.dspread.mdm.service              I  🔧 启动 WebSocket 连接...
2025-08-14 18:09:17.501 18617-18617 WebSocket               com.dspread.mdm.service              I  🔧 开始连接 WebSocket...
2025-08-14 18:09:17.526 18617-18617 WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接到: ws://35.75.3.206:8080/status/websocket/register?serialNo=MDEzNTQwOTAyMDI1MDMwNTAzOTk=&publicKey=TUlHZk1BMEdDU3FHU0liM0RRRUJBUVVBQTRHTkFEQ0JpUUtCZ1FDNkI5SFJROUtpVnp5Vkora2xoc0wvaENTdkxEbTAyZWdqa0trV2NxaldpWm83UkVwbmVhaytYR2pLN0ZxeDNoem43a1VkVDBheU5XZ0NJTGF6elBRNDVRVTBnbnh3aUxxTm9wbWhUWWx0dzBpdWMvVWcvQk1Ed244em9DU0ZLbm04TkRpZ1YrdzEwK0pBTnUxenVxK0lkU3hWREtKRG1JMHZMNUp1aDYrbnN3SURBUUFC&query=1&msgVer=3&timestamp=1755166157511&signature=fPH1tHm7pFAwHB8s794v9z2LKkGtEX48Hv+e8V+H2+plPG6/qrgIcHOYuvgSb3kKDrrBRWPpjKskvEr42fdF/oYVWreGrRYCaQ7Zr31es8kKEXhEynWtsv5XxJQkiEbHul+ErlCLZrOijo8rRQ78SYwjbXY7lZycmx+2WxantKw=
2025-08-14 18:09:17.534 18617-18617 WebSocket               com.dspread.mdm.service              I  🔧 未检测到系统代理设置
2025-08-14 18:09:17.566 18617-18617 WebSocket               com.dspread.mdm.service              I  🔧 ===== WebSocket 库默认配置 =====
2025-08-14 18:09:17.571 18617-18617 WebSocket               com.dspread.mdm.service              I  🔧 默认 PING 间隔: 60000ms (60秒)
2025-08-14 18:09:17.577 18617-18617 WebSocket               com.dspread.mdm.service              I  🔧 默认 PONG 间隔: 0ms (0秒)
2025-08-14 18:09:17.583 18617-18617 WebSocket               com.dspread.mdm.service              I  🔧 帧队列大小: 5
2025-08-14 18:09:17.589 18617-18617 WebSocket               com.dspread.mdm.service              I  🔧 连接超时: 15000ms (15秒)
2025-08-14 18:09:17.594 18617-18617 WebSocket               com.dspread.mdm.service              I  🔧 允许缺失关闭帧: false
2025-08-14 18:09:17.600 18617-18617 WebSocket               com.dspread.mdm.service              I  🔧 ================================
2025-08-14 18:09:17.606 18617-18617 WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接请求已发送
2025-08-14 18:09:17.611 18617-18681 TrafficStats            com.dspread.mdm.service              D  tagSocket(125) with statsTag=0xffffffff, statsUid=-1
2025-08-14 18:09:17.613 18617-18617 WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接启动完成
2025-08-14 18:09:17.620 18617-18617 Common                  com.dspread.mdm.service              D  🔧 [ModuleManagerRegistry] 模块管理器注册中心初始化完成
2025-08-14 18:09:17.638 18617-18617 RuleBase                com.dspread.mdm.service              D  🔧 RuleBaseManager 开始初始化RuleBaseManager...
2025-08-14 18:09:17.648 18617-18617 RuleBase                com.dspread.mdm.service              D  🔧 RuleBaseStorage RuleBase存储管理器初始化完成
2025-08-14 18:09:17.654 18617-18617 RuleBase                com.dspread.mdm.service              D  🔧 RuleStateMonitor 状态监控器初始化完成
2025-08-14 18:09:17.660 18617-18617 RuleBase                com.dspread.mdm.service              D  🔧 RuleBaseManager RuleBaseManager初始化完成
2025-08-14 18:09:17.665 18617-18617 Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] RuleBaseManager初始化成功
2025-08-14 18:09:17.672 18617-18617 Provisioning            com.dspread.mdm.service              D  🔧 ProvisioningEventHandler 启动Provisioning定时器: 43200s (12小时)
2025-08-14 18:09:17.673 18617-18647 Common                  com.dspread.mdm.service              D  🔧 [ModuleManagerRegistry] 启动所有模块
2025-08-14 18:09:17.683 18617-18617 Common                  com.dspread.mdm.service              I  ✅ ⏰ 设置初始化定时器成功，下次执行: 43200秒后 (12小时)
2025-08-14 18:09:17.686 18617-18647 Common                  com.dspread.mdm.service              D  🔧 [ModuleManagerRegistry] LogStreamManager实例创建完成
2025-08-14 18:09:17.689 18617-18617 Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] Provisioning定时器启动成功
2025-08-14 18:09:17.693 18617-18647 Common                  com.dspread.mdm.service              D  🔧 Module LogStream Manager status changed to: INITIALIZED
2025-08-14 18:09:17.694 18617-18617 Common                  com.dspread.mdm.service              D  🔧 TimerConfig ========== 当前定时器配置 (生产模式) ==========
2025-08-14 18:09:17.701 18617-18647 Common                  com.dspread.mdm.service              D  🔧 [ModuleManagerRegistry] LogStreamManager初始化成功
2025-08-14 18:09:17.707 18617-18647 Common                  com.dspread.mdm.service              D  🔧 Module LogStream Manager status changed to: STARTING
2025-08-14 18:09:17.710 18617-18617 Common                  com.dspread.mdm.service              D  🔧 TimerConfig 使用Provisioning配置的心跳间隔: 300秒
2025-08-14 18:09:17.715 18617-18617 Common                  com.dspread.mdm.service              D  🔧 TimerConfig ①心跳定时器: 300秒
2025-08-14 18:09:17.730 18617-18617 Common                  com.dspread.mdm.service              D  🔧 TimerConfig 使用Provisioning配置的终端信息上传间隔: 900秒
2025-08-14 18:09:17.736 18617-18617 Common                  com.dspread.mdm.service              D  🔧 TimerConfig ②终端信息上传: 900秒
2025-08-14 18:09:17.742 18617-18617 Common                  com.dspread.mdm.service              D  🔧 TimerConfig ③任务执行: 60秒
2025-08-14 18:09:17.749 18617-18617 Common                  com.dspread.mdm.service              D  🔧 TimerConfig ④服务守护: 120秒
2025-08-14 18:09:17.755 18617-18617 Common                  com.dspread.mdm.service              D  🔧 TimerConfig ⑤Provisioning: 43200秒
2025-08-14 18:09:17.761 18617-18617 Common                  com.dspread.mdm.service              D  🔧 TimerConfig ===============================================
2025-08-14 18:09:17.767 18617-18617 Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 核心组件初始化完成
2025-08-14 18:09:17.780 18617-18647 Common                  com.dspread.mdm.service              D  🔧 Module LogStream Manager status changed to: RUNNING
2025-08-14 18:09:17.787 18617-18647 Common                  com.dspread.mdm.service              D  🔧 [ModuleManagerRegistry] LogStreamManager启动成功
2025-08-14 18:09:17.793 18617-18647 Common                  com.dspread.mdm.service              D  🔧 [ModuleManagerRegistry] LogStreamManager启动成功
2025-08-14 18:09:17.799 18617-18647 Common                  com.dspread.mdm.service              D  🔧 [ModuleManagerRegistry] 开始初始化地理围栏管理器
2025-08-14 18:09:17.822 18617-18647 Compatibil...geReporter com.dspread.mdm.service              D  Compat change id reported: 263076149; UID 1000; state: ENABLED
2025-08-14 18:09:17.833 18617-18647 Common                  com.dspread.mdm.service              D  🔧 Module Geofence Manager status changed to: INITIALIZED
2025-08-14 18:09:17.840 18617-18647 Common                  com.dspread.mdm.service              D  🔧 Module Geofence Manager status changed to: STARTING
2025-08-14 18:09:17.865 18617-18647 Platform                com.dspread.mdm.service              D  🔧 用户交互监控已启动（简化版本）
2025-08-14 18:09:17.866 18617-18617 Platform                com.dspread.mdm.service              D  🔧 [WiFiManagerApi] WiFiManagerApi 单例实例已创建
2025-08-14 18:09:17.872 18617-18647 Platform                com.dspread.mdm.service              D  🔧 使用模拟用户交互检测
2025-08-14 18:09:17.878 18617-18647 Platform                com.dspread.mdm.service              D  🔧 用户交互监控已启动
2025-08-14 18:09:17.885 18617-18647 Service                 com.dspread.mdm.service              D  🔧 [GeofenceManager]Constants.geofenceStatus = 1
2025-08-14 18:09:17.891 18617-18647 Service                 com.dspread.mdm.service              D  🔧 [GeofenceManager]-- Constants.storeId =  -- Constants.storeSsid =  -- Constants.storeIp = 
2025-08-14 18:09:17.898 18617-18647 Common                  com.dspread.mdm.service              D  🔧 Module Geofence Manager status changed to: RUNNING
2025-08-14 18:09:17.904 18617-18647 Common                  com.dspread.mdm.service              D  🔧 [ModuleManagerRegistry] 地理围栏管理器初始化成功
2025-08-14 18:09:17.910 18617-18647 Common                  com.dspread.mdm.service              D  🔧 [ModuleManagerRegistry] GeofenceManager启动成功
2025-08-14 18:09:17.916 18617-18647 Common                  com.dspread.mdm.service              D  🔧 [ModuleManagerRegistry] 所有模块启动完成
2025-08-14 18:10:04.328 18617-18692 Common                  com.dspread.mdm.service              I  ✅ WebSocket 物理连接成功，等待服务器 hellMsg
2025-08-14 18:10:04.341 18617-18692 WebSocket               com.dspread.mdm.service              I  🔧 ===== 心跳发送器实际配置 =====
2025-08-14 18:10:04.353 18617-18692 WebSocket               com.dspread.mdm.service              I  🔧 实际 PING 发送间隔: 60000ms (60秒)
2025-08-14 18:10:04.364 18617-18692 WebSocket               com.dspread.mdm.service              I  🔧 PING 定时器名称: PingSender
2025-08-14 18:10:04.373 18617-18692 WebSocket               com.dspread.mdm.service              I  🔧 实际 PONG 发送间隔: 0ms (0秒)
2025-08-14 18:10:04.381 18617-18692 WebSocket               com.dspread.mdm.service              I  🔧 PONG 定时器名称: PongSender
2025-08-14 18:10:04.388 18617-18692 WebSocket               com.dspread.mdm.service              I  🔧 ===============================
2025-08-14 18:10:05.169 18617-18692 Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"hellMsg":"hello 01354090202503050399","tranCode":"S0000","version":"1","rebootTime":"03:05:02","serialNo":"01354090202503050399","deviceStatus":"6"}
2025-08-14 18:10:05.178 18617-18692 Common                  com.dspread.mdm.service              I  ✅ 收到服务器 hellMsg，WebSocket 业务连接成功！
2025-08-14 18:10:05.185 18617-18692 Common                  com.dspread.mdm.service              I  ✅ WebSocket 业务连接成功！
2025-08-14 18:10:05.193 18617-18692 WebSocket               com.dspread.mdm.service              I  🔧 业务连接成功，重连计数已重置为 0
2025-08-14 18:10:05.204 18617-18692 Common                  com.dspread.mdm.service              I  ✅ ⏰ 设置任务执行定时器成功，下次执行: 60秒后 (1分钟)
2025-08-14 18:10:05.212 18617-18692 WebSocket               com.dspread.mdm.service              I  🔧 已启动任务执行定时器
2025-08-14 18:10:05.234 18617-18692 Common                  com.dspread.mdm.service              D  🔧 TimerConfig 使用Provisioning配置的心跳间隔: 300秒
2025-08-14 18:10:05.244 18617-18692 Common                  com.dspread.mdm.service              I  ✅ ⏰ 设置心跳定时器成功，下次执行: 300秒后 (5分钟)
2025-08-14 18:10:05.250 18617-18692 WebSocket               com.dspread.mdm.service              I  🔧 已启动心跳定时器
2025-08-14 18:10:05.265 18617-18692 Common                  com.dspread.mdm.service              D  🔧 TimerConfig 使用Provisioning配置的终端信息上传间隔: 900秒
2025-08-14 18:10:05.275 18617-18692 Common                  com.dspread.mdm.service              I  ✅ ⏰ 设置终端信息定时器成功，下次执行: 900秒后 (15分钟)
2025-08-14 18:10:05.281 18617-18692 WebSocket               com.dspread.mdm.service              I  🔧 已启动终端信息上传定时器
2025-08-14 18:10:05.287 18617-18692 WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接成功，检查自身更新任务状态
2025-08-14 18:10:05.294 18617-18692 ContextImpl             com.dspread.mdm.service              W  Calling a method in the system process without a qualified user: android.app.ContextImpl.sendBroadcast:1220 android.content.ContextWrapper.sendBroadcast:504 com.dspread.mdm.service.broadcast.core.BroadcastSender.sendBroadcast:99 com.dspread.mdm.service.broadcast.core.BroadcastSender.sendBroadcast:23 com.dspread.mdm.service.network.websocket.connection.WsConnectionManager.checkSelfUpdateTaskStatus:311 
2025-08-14 18:10:05.297 18617-18692 Receiver                com.dspread.mdm.service              D  🔧 发送广播: com.dspread.mdm.service.CHECK_SELF_UPDATE_STATUS
2025-08-14 18:10:05.298 18617-18617 Receiver                com.dspread.mdm.service              E  ❌ BroadcastManager 处理器 PackageUpdateEventHandler 处理失败: com.dspread.mdm.service.CHECK_SELF_UPDATE_STATUS
2025-08-14 18:10:05.303 18617-18692 WebSocket               com.dspread.mdm.service              I  🔧 开始发送缓存消息，数量: 1
2025-08-14 18:10:05.313 18617-18692 WebSocket               com.dspread.mdm.service              I  🔧 C0902 主动式上送: cached_message_resend (主动: 2)
2025-08-14 18:10:05.347 18617-18692 Common                  com.dspread.mdm.service              I  ✅ 缓存消息发送成功: C0902
2025-08-14 18:10:05.855 18617-18692 WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接成功
2025-08-14 18:10:05.862 18617-18692 WebSocket               com.dspread.mdm.service              I  🔧 设备状态: 6
2025-08-14 18:10:05.869 18617-18692 WebSocket               com.dspread.mdm.service              I  🔧 检查首次连接标志: isFirstTimeWebSocketConnected=true
2025-08-14 18:10:05.875 18617-18692 WebSocket               com.dspread.mdm.service              I  🔧 首次WebSocket连接成功，发送初始化信息并执行初始化检查
2025-08-14 18:10:05.881 18617-18692 WebSocket               com.dspread.mdm.service              I  🔧 开始发送初始化信息（带流量控制）
2025-08-14 18:10:05.887 18617-18692 WebSocket               com.dspread.mdm.service              I  🔧 C0901 被动式上送: first_connection (被动: 2)
2025-08-14 18:10:05.902 18617-18692 Platform                com.dspread.mdm.service              D  🔧 重新收集应用信息
2025-08-14 18:10:06.012 18617-18692 AppManager              com.dspread.mdm.service              I  ℹ️ 应用信息获取完成: 总数133(系统129/用户4) 返回4个
2025-08-14 18:10:06.041 18617-18692 Platform                com.dspread.mdm.service              D  🔧 应用信息: 4 个应用
2025-08-14 18:10:06.086 18617-18692 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0901","serialNo":"01354090202503050399","request_time":"1755166206048","request_id":"1755166206048C0901","version":"1","data":{"apkInfo":[{"packName":"plus.H52FFB9A5","apkName":"逍遥助手","versionCode":100,"versionName":"1.0","updateDate":"2025-08-14 17:51:02"},{"packName":"com.dspread.mdm.service","apkName":"service","versionCode":11,"versionName":"1.0.08.20250814.DSPREAD.MDM.SERVICE","updateDate":"2025-08-14 18:08:42"},{"packName":"mark.via","apkName":"Via","versionCode":20250713,"versionName":"6.6.0","updateDate":"2025-08-14 17:44:27"},{"packName":"com.chileaf.cl960.sample","apkName":"cl960","versionCode":1,"versionName":"1.1.0","updateDate":"2025-08-14 17:48:41"}],"sytemInfo":{"androidVersion":"14","buildNumber":"BASE_D30M-MU_GEN_AP_V1.0.8.1_202507032000_ota_test","aspl":"2025-03-05"},"serviceInfo":[]},"myVersionName":"1.0.08.20250814.DSPREAD.MDM.SERVICE","terminalDate":"20250814181006"}
2025-08-14 18:10:06.092 18617-18692 WebSocket               com.dspread.mdm.service              I  🔧 C0901 应用信息上送成功 (1)
2025-08-14 18:10:07.099 18617-18692 WebSocket               com.dspread.mdm.service              I  🔧 C0902 被动式上送: first_connection (被动: 3)
2025-08-14 18:10:07.133 18617-18692 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0902","serialNo":"01354090202503050399","request_time":"1755166207118","request_id":"1755166207118C0902","version":"1","data":{"batteryLife":100,"batteryHealth":2,"temprature":"29.7","isCharging":"1","isLowBattery":"0","powerOff":"0","outOfBattery":"0"},"myVersionName":"1.0.08.20250814.DSPREAD.MDM.SERVICE","terminalDate":"20250814181007"}
2025-08-14 18:10:07.140 18617-18692 WebSocket               com.dspread.mdm.service              I  🔧 C0902 电池状态上送成功 (1, trigger=first_connection)
2025-08-14 18:10:08.147 18617-18692 WebSocket               com.dspread.mdm.service              I  🔧 C0903 被动式上送: first_connection (被动: 4)
2025-08-14 18:10:08.191 18617-18692 Platform                com.dspread.mdm.service              D  🔧 内存总容量为： 1.86GB 
2025-08-14 18:10:08.198 18617-18692 Platform                com.dspread.mdm.service              D  🔧 内存使用量为： 0.76GB 
2025-08-14 18:10:08.302 18617-18692 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0903","serialNo":"01354090202503050399","request_time":"1755166208266","request_id":"1755166208266C0903","version":"1","data":{"hardwareInfo":[{"name":"CPU Usage","desc":"cpu:0.50"},{"name":"storage_usage_status","desc":" 5.45GB \/ 32.00GB ","state":"1"},{"name":"total_memory","desc":" 0.76GB \/ 1.86GB ","state":"1"},{"name":"androidVersion","desc":"14","state":"1"},{"name":"network_status","desc":"wifi","state":"1"},{"name":"OSVer","desc":"14","state":"1"}],"locationInfo":{"gps":{"longitude":"-999.0","latitude":"-999.0","satelliteCount":0,"satelliteCountInUsed":0,"satelliteAverSNR":"0.00","distance":"0.0","lockStatus":"1","lockMeter":"200","proId":"","proName":""},"baseSite":{"MCC":"","MNC":"","CID":"-1","LAC":"-1","DBM":"-999","IMSI":"","ICCID":""},"wifi":{"SSID":"fubox_2.4G","IP":"*************","MAC":"8A:B9:BB:91:76:7E","SSTH":"-32","BSSID":"7c:10:c9:d6:9f:10"},"address":"无法获取"}},"myVersionName":"1.0.08.20250814.DSPREAD.MDM.SERVICE","terminalDate":"20250814181008"}
2025-08-14 18:10:08.308 18617-18692 WebSocket               com.dspread.mdm.service              I  🔧 C0903 数据信息上送成功 (1)
2025-08-14 18:10:09.315 18617-18692 WebSocket               com.dspread.mdm.service              I  🔧 C0904 被动式上送: first_connection (被动: 5)
2025-08-14 18:10:09.413 18617-18692 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0904","serialNo":"01354090202503050399","request_time":"1755166209381","request_id":"1755166209381C0904","version":"1","data":{"wifiOption":[{"SSID":"2306","SSTH":"-74"},{"SSID":"2205","SSTH":"-53"},{"SSID":"2205_5G","SSTH":"-50"},{"SSID":"DIRECT-C6-Mi All-in-One Inkjet","SSTH":"-49"},{"SSID":"2207-5G","SSTH":"-86"},{"SSID":"2207","SSTH":"-56"},{"SSID":"2106-5G","SSTH":"-94"},{"SSID":"2103_5G","SSTH":"-76"},{"SSID":"@Ruijie-1816_5G","SSTH":"-71"},{"SSID":"fubox_2.4G","SSTH":"-31"},{"SSID":"2206","SSTH":"-25"},{"SSID":"2206-5G","SSTH":"-34"},{"SSID":"@Ruijie-1816","SSTH":"-41"}],"baseSite":{"MCC":"","MNC":"","CID":"-1","LAC":"-1","DBM":"-999","IMSI":"","ICCID":""},"wifi":{"SSID":"fubox_2.4G","IP":"*************","MAC":"8A:B9:BB:91:76:7E","SSTH":"-32","BSSID":"7c:10:c9:d6:9f:10"}},"myVersionName":"1.0.08.20250814.DSPREAD.MDM.SERVICE","terminalDate":"20250814181009"}
2025-08-14 18:10:09.420 18617-18692 WebSocket               com.dspread.mdm.service              I  🔧 C0904 网络状态上送成功 (1)
2025-08-14 18:10:10.426 18617-18692 WebSocket               com.dspread.mdm.service              I  🔧 C0906 被动式上送: first_connection (被动: 6)
2025-08-14 18:10:10.443 18617-18692 Platform                com.dspread.mdm.service              D  🔧 DspreadService 使用缓存的SP版本: V1.0.5
2025-08-14 18:10:10.449 18617-18692 Platform                com.dspread.mdm.service              D  🔧 SpVersionApi 通过DspreadService获取SP版本: V1.0.5
2025-08-14 18:10:10.478 18617-18692 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0906","serialNo":"01354090202503050399","request_time":"1755166210460","request_id":"1755166210460C0906","version":"1","data":{"firmWareInfo":{"spfw":"V1.0.5"},"imei_1":"864177060143618","imei_2":"","wifi_mac":"8a:b9:bb:91:76:7e","bt_mac":"","bsn":""},"myVersionName":"1.0.08.20250814.DSPREAD.MDM.SERVICE","terminalDate":"20250814181010"}
2025-08-14 18:10:10.486 18617-18692 WebSocket               com.dspread.mdm.service              I  🔧 C0906 设备信息上送成功 (1)
2025-08-14 18:10:10.493 18617-18692 WebSocket               com.dspread.mdm.service              I  🔧 C0109 被动式上送: first_connection (被动: 7)
2025-08-14 18:10:10.509 18617-18692 Platform                com.dspread.mdm.service              D  🔧 重新收集应用信息（强制刷新）
2025-08-14 18:10:10.599 18617-18692 AppManager              com.dspread.mdm.service              I  ℹ️ 应用信息获取完成: 总数133(系统129/用户4) 返回4个
2025-08-14 18:10:10.622 18617-18692 Platform                com.dspread.mdm.service              D  🔧 应用信息: 4 个应用
2025-08-14 18:10:10.695 18617-18692 Platform                com.dspread.mdm.service              D  🔧 内存总容量为： 1.86GB 
2025-08-14 18:10:10.702 18617-18692 Platform                com.dspread.mdm.service              D  🔧 内存使用量为： 0.76GB 
2025-08-14 18:10:10.794 18617-18692 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0109","serialNo":"01354090202503050399","request_time":"1755166210723","request_id":"1755166210723C0109","version":"1","data":{"apkInfo":[{"packName":"plus.H52FFB9A5","apkName":"逍遥助手","versionCode":100,"versionName":"1.0","updateDate":"2025-08-14 17:51:02"},{"packName":"com.dspread.mdm.service","apkName":"service","versionCode":11,"versionName":"1.0.08.20250814.DSPREAD.MDM.SERVICE","updateDate":"2025-08-14 18:08:42"},{"packName":"mark.via","apkName":"Via","versionCode":20250713,"versionName":"6.6.0","updateDate":"2025-08-14 17:44:27"},{"packName":"com.chileaf.cl960.sample","apkName":"cl960","versionCode":1,"versionName":"1.1.0","updateDate":"2025-08-14 17:48:41"}],"locationInfo":{"gps":{"longitude":"-999.0","latitude":"-999.0","satelliteCount":0,"satelliteCountInUsed":0,"satelliteAverSNR":"0.00","distance":"0.0","lockStatus":"1","lockMeter":"200","proId":"","proName":""},"baseSite":{"MCC":"","MNC":"","CID":"-1","LAC":"-1","DBM":"-999","IMSI":"","ICCID":""},"wifi":{"SSID":"fubox_2.4G","IP":"*************","MAC":"8A:B9:BB:91:76:7E","SSTH":"-32","BSSID":"7c:10:c9:d6:9f:10"},"address":"无法获取"},"hardwareInfo":[{"name":"CPU Usage","desc":"cpu:0.50"},{"name":"storage_usage_status","desc":" 5.45GB \/ 32.00GB ","state":"1"},{"name":"total_memory","desc":" 0.76GB \/ 1.86GB ","state":"1"},{"name":"androidVersion","desc":"14","state":"1"},{"name":"network_status","desc":"wifi","state":"1"},{"name":"OSVer","desc":"14","state":"1"}],"wifiOption":[{"SSID":"2306","SSTH":"-74"},{"SSID":"2205","SSTH":"-53"},{"SSID":"2205_5G","SSTH":"-50"},{"SSID":"DIRECT-C6-Mi All-in-One Inkjet","SSTH":"-49"},{"SSID":"2207-5G","SSTH":"-86"},{"SSID":"2207","SSTH":"-56"},{"SSID":"2106-5G","SSTH":"-94"},{"SSID":"2103_5G","SSTH":"-76"},{"SSID":"@Ruijie-1816_5G","SSTH":"-71"},{"SSID":"fubox_2.4G","SSTH":"-31"},{"SSID":"2206","SSTH":"-25"},{"SSID":"2206-5G","SSTH":"-34"},{"SSID":"@Ruijie-1816","SSTH":"-41"}],"sytemInfo":{"androidVersion":"14","buildNumber":"BASE_D30M-MU_GEN_AP_V1.0.8.1_202507032000_ota_test","aspl":"2025-03-05"}},"myVersionName":"1.0.08.20250814.DSPREAD.MDM.SERVICE","terminalDate":"20250814181010"}
2025-08-14 18:10:10.801 18617-18692 WebSocket               com.dspread.mdm.service              I  🔧 C0109 终端信息上传完成 (1, trigger=first_connection)
2025-08-14 18:10:10.807 18617-18692 Common                  com.dspread.mdm.service              I  ✅ 初始化信息发送完成
2025-08-14 18:10:10.814 18617-18692 WebSocket               com.dspread.mdm.service              I  🔧 检查服务更新结果
2025-08-14 18:10:10.820 18617-18692 Task                    com.dspread.mdm.service              W  ⚠️ 任务列表为空，跳过服务更新结果检查
2025-08-14 18:10:10.826 18617-18692 WebSocket               com.dspread.mdm.service              I  🔧 检查OS升级状态...
2025-08-14 18:10:10.833 18617-18692 Common                  com.dspread.mdm.service              D  🔧 OsUpdateStatusChecker WebSocket连接成功，检查OS升级状态
2025-08-14 18:10:10.840 18617-18692 Common                  com.dspread.mdm.service              D  🔧 OsUpdateStatusChecker 没有待检查的OS升级任务
2025-08-14 18:10:10.846 18617-18692 WebSocket               com.dspread.mdm.service              I  🔧 OS升级状态检查已触发
2025-08-14 18:10:10.852 18617-18692 WebSocket               com.dspread.mdm.service              I  🔧 首次连接初始化检查完成
2025-08-14 18:10:10.900 18617-18692 Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"msg_state":"P","request_time":"1755148854367","data":{"taskList":[{"beginDate":"2024-08-14 05:20:54","taskType":"02","apkName":"Via","endDate":"9999-12-31 23:59:59","appId":"1755148854367","packName":"mark.via","versionName":"6.6.0","taskId":"1755148854367","versionCode":"20250713"}]},"tranCode":"ST001","request_id":"1755148854367ST001","version":"1","serialNo":"01354090202503050399"}
2025-08-14 18:10:10.909 18617-18692 WebSocket               com.dspread.mdm.service              I  🔧 准备发送响应确认 - requestId: 1755148854367ST001, needResponse: true
2025-08-14 18:10:10.932 18617-18692 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0000","serialNo":"01354090202503050399","request_time":"1755166210915","request_id":"1755166210915C0000","version":"1","org_request_id":"1755148854367ST001","org_request_time":"1755148854367","response_state":"0","myVersionName":"1.0.08.20250814.DSPREAD.MDM.SERVICE","terminalDate":"20250814181010"}
2025-08-14 18:10:10.955 18617-18692 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0000","serialNo":"01354090202503050399","request_time":"1755166210940","request_id":"1755166210940C0000","version":"1","org_request_id":"1755148854367ST001","org_request_time":"1755148854367","response_state":"0","myVersionName":"1.0.08.20250814.DSPREAD.MDM.SERVICE","terminalDate":"20250814181010"}
2025-08-14 18:10:10.961 18617-18692 WebSocket               com.dspread.mdm.service              I  🔧 已发送C0000响应确认: requestId=1755148854367ST001
2025-08-14 18:10:10.969 18617-18692 Task                    com.dspread.mdm.service              D  🔧 处理任务消息: ST001
2025-08-14 18:10:10.976 18617-18692 Task                    com.dspread.mdm.service              I  ℹ️ 更新任务列表: requestId=1755148854367ST001, 任务数量=1
2025-08-14 18:10:10.982 18617-18692 Task                    com.dspread.mdm.service              D  🔧 添加/更新任务: taskId=1755148854367
2025-08-14 18:10:10.992 18617-18692 Task                    com.dspread.mdm.service              D  🔧 保存任务列表到存储: 1 个
2025-08-14 18:10:10.998 18617-18692 Task                    com.dspread.mdm.service              D  🔧 开始处理任务列表，共 1 个任务
2025-08-14 18:10:11.005 18617-18692 Task                    com.dspread.mdm.service              D  🔧 处理任务: taskId=1755148854367, type=02, package=mark.via, apk=Via
2025-08-14 18:10:11.011 18617-18692 Task                    com.dspread.mdm.service              D  🔧 处理卸载任务: 1755148854367
2025-08-14 18:10:11.018 18617-18692 AppManager              com.dspread.mdm.service              I  ℹ️ Uninstalling app via PackageInstaller: mark.via
2025-08-14 18:10:11.024 18617-18692 AppManager              com.dspread.mdm.service              I  ℹ️ Deleting package via PackageInstaller: mark.via
2025-08-14 18:10:11.034 18617-18692 AppManager              com.dspread.mdm.service              I  ℹ️ PackageInstaller uninstall initiated
2025-08-14 18:10:11.120 18617-18692 Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"request_time":"1755166209606","org_request_time":"1755166210460","org_request_id":"1755166210460C0906","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755166209606S0000","serialNo":"01354090202503050399"}
2025-08-14 18:10:11.130 18617-18692 WebSocket               com.dspread.mdm.service              I  🔧 收到响应消息: transCode=S0000, orgRequestId=1755166210460C0906, state=0, remark=
2025-08-14 18:10:11.137 18617-18692 WebSocket               com.dspread.mdm.service              I  🔧 处理设备信息响应: state=0
2025-08-14 18:10:11.143 18617-18692 WebSocket               com.dspread.mdm.service              I  🔧 设备信息上传成功
2025-08-14 18:10:11.280 18617-18617 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 收到广播: android.intent.action.PACKAGE_REMOVED
2025-08-14 18:10:11.288 18617-18617 Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 收到包更新广播: android.intent.action.PACKAGE_REMOVED, 包名: mark.via
2025-08-14 18:10:11.295 18617-18617 Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 应用卸载: mark.via
2025-08-14 18:10:11.312 18617-18617 Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 检查WebSocket状态: packageName=mark.via, action=UNINSTALL
2025-08-14 18:10:11.331 18617-18617 Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 通知rulebase处理应用卸载: mark.via
2025-08-14 18:10:11.344 18617-18617 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 收到系统广播，应用已卸载: mark.via
2025-08-14 18:10:11.352 18617-18617 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 未找到匹配的卸载回调: mark.via
2025-08-14 18:10:11.360 18617-18617 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 未找到匹配的规则应用: mark.via
2025-08-14 18:10:11.367 18617-18617 Platform                com.dspread.mdm.service              D  🔧 重新收集应用信息（强制刷新）
2025-08-14 18:10:11.486 18617-18617 AppManager              com.dspread.mdm.service              I  ℹ️ 应用信息获取完成: 总数132(系统129/用户3) 返回3个
2025-08-14 18:10:11.519 18617-18617 Platform                com.dspread.mdm.service              D  🔧 应用信息: 3 个应用
2025-08-14 18:10:11.526 18617-18617 Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 应用信息缓存刷新完成
2025-08-14 18:10:11.533 18617-18617 ContextImpl             com.dspread.mdm.service              W  Calling a method in the system process without a qualified user: android.app.ContextImpl.sendBroadcast:1220 android.content.ContextWrapper.sendBroadcast:504 com.dspread.mdm.service.broadcast.core.BroadcastSender.sendBroadcast:99 com.dspread.mdm.service.broadcast.core.BroadcastSender.sendBroadcast:23 com.dspread.mdm.service.broadcast.handlers.system.PackageUpdateEventHandlerImpl.sendAppRefreshBroadcast:476 
2025-08-14 18:10:11.535 18617-18617 Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 发送应用刷新广播
2025-08-14 18:10:16.044 18617-18696 AppManager              com.dspread.mdm.service              I  ℹ️ Package uninstalled (backup check): mark.via
2025-08-14 18:10:16.058 18617-18696 Task                    com.dspread.mdm.service              D  🔧 卸载成功，通知应用状态变化
2025-08-14 18:10:16.071 18617-18696 Platform                com.dspread.mdm.service              D  🔧 应用状态变化: UNINSTALL - mark.via
2025-08-14 18:10:16.084 18617-18696 Platform                com.dspread.mdm.service              D  🔧 重新收集应用信息（强制刷新）
2025-08-14 18:10:16.196 18617-18696 AppManager              com.dspread.mdm.service              I  ℹ️ 应用信息获取完成: 总数132(系统129/用户3) 返回3个
2025-08-14 18:10:16.220 18617-18696 Platform                com.dspread.mdm.service              D  🔧 应用信息: 3 个应用
2025-08-14 18:10:16.228 18617-18696 Task                    com.dspread.mdm.service              D  🔧 更新任务状态: taskId=1755148854367, state=D02
2025-08-14 18:10:16.238 18617-18696 Task                    com.dspread.mdm.service              D  🔧 保存任务列表到存储: 1 个
2025-08-14 18:10:16.250 18617-18696 Task                    com.dspread.mdm.service              D  🔧 保存任务列表到存储: 1 个
2025-08-14 18:10:16.256 18617-18696 WebSocket               com.dspread.mdm.service              I  🔧 ST001 主动式上送: task_failed (主动: 3)
2025-08-14 18:10:16.290 18617-18696 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0108","serialNo":"01354090202503050399","request_time":"1755166216271","request_id":"1755166216271C0108","version":"1","data":{"taskId":"1755148854367","taskResult":"D02","appId":"1755148854367"},"myVersionName":"1.0.08.20250814.DSPREAD.MDM.SERVICE","terminalDate":"20250814181016","org_request_id":"1755148854367ST001","org_request_time":"1755148854367"}
2025-08-14 18:10:16.297 18617-18696 WebSocket               com.dspread.mdm.service              I  🔧 任务结果实时上送: taskId=1755148854367, result=D02 (1)
2025-08-14 18:10:17.136 18617-18692 Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"request_time":"1755166215418","org_request_time":"1755166216271","org_request_id":"1755166216271C0108","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755166215418S0000","serialNo":"01354090202503050399"}
2025-08-14 18:10:17.146 18617-18692 WebSocket               com.dspread.mdm.service              I  🔧 收到响应消息: transCode=S0000, orgRequestId=1755166216271C0108, state=0, remark=
2025-08-14 18:10:17.152 18617-18692 WebSocket               com.dspread.mdm.service              I  🔧 处理任务结果响应: state=0
2025-08-14 18:10:17.158 18617-18692 WebSocket               com.dspread.mdm.service              I  🔧 任务结果上传成功
2025-08-14 18:10:22.624 18617-18652 GED                     com.dspread.mdm.service              I  ged_boost_gpu_freq, level 100, eOrigin 2, final_idx 2, oppidx_max 2, oppidx_min 0
2025-08-14 18:10:22.642 18617-18617 BLASTBufferQueue        com.dspread.mdm.service              D  [VRI[TestActivity]#0](f:0,a:1) destructor()
2025-08-14 18:10:22.642 18617-18617 BufferQueueConsumer     com.dspread.mdm.service              D  [VRI[TestActivity]#0(BLAST Consumer)0](id:48b900000000,api:0,p:-1,c:18617) disconnect
2025-08-14 18:10:22.646 18617-18652 GED                     com.dspread.mdm.service              I  ged_boost_gpu_freq, level 100, eOrigin 2, final_idx 2, oppidx_max 2, oppidx_min 0
