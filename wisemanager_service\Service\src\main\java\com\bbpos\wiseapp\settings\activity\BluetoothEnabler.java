/*
 * Copyright (C) 2010 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.bbpos.wiseapp.settings.activity;

import android.bluetooth.BluetoothAdapter;
import android.content.Context;
import android.net.wifi.WifiManager;
import android.provider.Settings;
import android.util.Log;
import android.widget.CompoundButton;
import android.widget.TextView;
import android.widget.Toast;

import com.bbpos.wiseapp.logger.BBLog;
import com.bbpos.wiseapp.service.R;
import com.bbpos.wiseapp.settings.utils.WirelessUtil;
import com.bbpos.wiseapp.settings.widget.ToggleSwitch;

public class BluetoothEnabler implements ToggleSwitch.OnCheckedChangeListener  {
    private static final String TAG = "WifiEnabler";
    private Context mContext;
    private ToggleSwitch mSwitchBar;
    private TextView mTVStatus;
    private boolean mListeningToOnSwitchChange = false;

    private final BluetoothAdapter mBluetoothAdapter;
    private boolean mStateMachineEvent;

    public BluetoothEnabler(Context context, ToggleSwitch switchBar, TextView status) {
        mContext = context;
        mSwitchBar = switchBar;
        mTVStatus = status;
        mBluetoothAdapter = BluetoothAdapter.getDefaultAdapter();
        setupSwitchBar();
    }

    public void setupSwitchBar() {
        int state = mBluetoothAdapter.getState();
        handleBluetoothStateChanged(state);
        if (!mListeningToOnSwitchChange) {
            mSwitchBar.setOnCheckedChangeListener(this);
            mListeningToOnSwitchChange = true;
        }
    }

    public void teardownSwitchBar() {
        if (mListeningToOnSwitchChange) {
            mSwitchBar.setOnCheckedChangeListener(null);
            mListeningToOnSwitchChange = false;
        }
    }

    public void resume(Context context) {
        mContext = context;
        // Wi-Fi state is sticky, so just let the receiver update UI
        if (!mListeningToOnSwitchChange) {
            mSwitchBar.setOnCheckedChangeListener(this);
            mListeningToOnSwitchChange = true;
        }
    }

    public void pause() {
        if (mListeningToOnSwitchChange) {
            mSwitchBar.setOnCheckedChangeListener(null);
            mListeningToOnSwitchChange = false;
        }
    }

    public void handleBluetoothStateChanged(int state) {
        BBLog.d(BBLog.TAG, "handleWifiStateChanged, state = " + state);
        // Clear any previous state
        switch (state) {
            case BluetoothAdapter.STATE_ON://蓝牙被打开
                setSwitchBarChecked(true);
                break;
            case BluetoothAdapter.STATE_OFF://蓝牙被关闭:
                setSwitchBarChecked(false);
                break;
            case BluetoothAdapter.STATE_TURNING_ON://蓝牙正在打开:
                break;
            case BluetoothAdapter.STATE_TURNING_OFF://蓝牙正在关闭:
                break;
        }
    }

    private void setSwitchBarChecked(boolean checked) {
        BBLog.d(BBLog.TAG, "setSwitchChecked, checked = " + checked); //M
        mStateMachineEvent = true;
        mSwitchBar.setChecked(checked);
        if (checked) {
            mTVStatus.setText(mContext.getString(R.string.on));
        } else {
            mTVStatus.setText(mContext.getString(R.string.off));
        }
        mStateMachineEvent = false;
    }

    @Override
    public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
        BBLog.d(BBLog.TAG, "onCheckedChanged, isChecked = " + isChecked);
        //Do nothing if called as a result of a state machine event
        if (mStateMachineEvent) {
            return;
        }

        // Show toast message if Wi-Fi is not allowed in airplane mode
        if (isChecked && !WirelessUtil.isRadioAllowed(mContext, Settings.Global.RADIO_BLUETOOTH)) {
            Toast.makeText(mContext, R.string.wifi_in_airplane_mode, Toast.LENGTH_SHORT).show();
            // Reset switch to off. No infinite check/listenenr loop.
            mSwitchBar.setChecked(false);
            return;
        }

        BBLog.d(BBLog.TAG, "onCheckedChanged, setWifiEnabled = " + isChecked);
        if (isChecked) {
            if (!mBluetoothAdapter.enable()) {
                // Error
                mSwitchBar.setChecked(false);
                Toast.makeText(mContext, R.string.bluetooth_error, Toast.LENGTH_SHORT).show();
            }
        } else {
            if (!mBluetoothAdapter.disable()) {
                // Error
                mSwitchBar.setChecked(false);
                Toast.makeText(mContext, R.string.bluetooth_error, Toast.LENGTH_SHORT).show();
            }
        }
    }
}
