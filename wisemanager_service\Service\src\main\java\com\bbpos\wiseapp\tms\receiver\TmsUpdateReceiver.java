package com.bbpos.wiseapp.tms.receiver;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.os.Handler;
import android.os.Looper;

import com.bbpos.wiseapp.logger.BBLog;
import com.bbpos.wiseapp.service.common.RequestPermission;
import com.bbpos.wiseapp.service.common.UsualData;
import com.bbpos.wiseapp.tms.adapter.SystemManagerAdapter;
import com.bbpos.wiseapp.tms.adapter.SystemManagerAdapter.BindServiceSuccess;
import com.bbpos.wiseapp.tms.utils.BroadcastActions;
import com.bbpos.wiseapp.tms.utils.Constants;
import com.bbpos.wiseapp.tms.utils.Helpers;
import com.bbpos.wiseapp.websocket.WebSocketSender;

public class TmsUpdateReceiver extends BroadcastReceiver{
	private static final String TAG = "WAUpdate";
	private Handler mHandler = new Handler(Looper.getMainLooper());
	@Override
	public void onReceive(final Context context, Intent intent) {
		BBLog.v(TAG, "TmsUpdateReceiver receive "+ intent.getAction());
		if (intent.getAction().equals(Intent.ACTION_PACKAGE_REPLACED)) {
            String packageName = intent.getData().getSchemeSpecificPart();	
         	BBLog.v(TAG, packageName+"ACTION_PACKAGE_REPLACED updated. "+context.getPackageName());
            if (packageName.equals(UsualData.LAUNCHER_PACKAGE_NAME)){
				Helpers.returnHome(context);
			} else if(packageName.equals(Helpers.getPkgNameByServiceName(Constants.SERVICE_SYSTEM_MANAGER))) {
				BBLog.e(TAG, "update service apk==="+packageName);
				SystemManagerAdapter.bindServiceInit(context, new BindServiceSuccess() {
					@Override
					public void onBindSuccess() {
					}
				});
			} else if (packageName.equals(UsualData.OTA_TRIGGER_APP_NAME)) {
//				SystemManagerAdapter.reboot(context);
				Helpers.sendBroad(context, UsualData.ACTION_TODO_OTAUPGRADE);
			}/*else if (packageName.equals(UsualData.SYSTEM_OTA_TRIGGER_APP_PACKAGENAME)){
				BBLog.e(BBLog.TAG, "检测到应用升级，package ===11111"+packageName);
				Helpers.sendBroad(context, UsualData.ACTION_TODO_OTAUPGRADE);
			}*/
		} else if (Intent.ACTION_PACKAGE_ADDED.equals(intent.getAction())) {
			String packageName = intent.getData().getSchemeSpecificPart();
			BBLog.d(TAG, "onReceive: ACTION_PACKAGE_ADDED packageName ----> " + packageName);
			if (packageName.equals(UsualData.OTA_TRIGGER_APP_NAME)) {
//				SystemManagerAdapter.reboot(context);
				Helpers.sendBroad(context, UsualData.ACTION_TODO_OTAUPGRADE);
			}else if (packageName.equals(UsualData.SYSTEM_OTA_TRIGGER_APP_PACKAGENAME)){//7MD OTA
				Helpers.sendBroad(context, UsualData.ACTION_TODO_OTA_UPDATE);
			}else if (packageName.equals(UsualData.SYSTEM_SP_OTA_TRIGGER_APP_PACKAGENAME)){//7MD SP
				Helpers.sendUpdateBroadByAction(context,packageName, UsualData.ACTION_TODO_SP_UPDATE);
			}else if (packageName.equals(UsualData.SYSTEM_TMT_OTA_TRIGGER_APP_PACKAGENAME)){//P1000 P500 TMT
				Helpers.sendUpdateTMTBroadByAction(context,packageName, UsualData.ACTION_TODO_TMT_UPDATE);
			}else if (packageName.equals(UsualData.SYSTEM_WISEPOS_OTA_TRIGGER_APP_PACKAGENAME)){//WISEPOS OTA
				Helpers.sendUpdateBroadByAction(context,packageName, UsualData.ACTION_TODO_WISEPOS_OTA_UPDATE);
			}else if (packageName.equals(UsualData.SYSTEM_KEY_UPDATE_TRIGGER_APP_PACKAGENAME)){//P1000 P500 KEY UPDATE
				Helpers.sendUpdateBroadByAction(context,packageName, UsualData.ACTION_TODO_KEY_UPDATE);
			}

			Intent it = new Intent(BroadcastActions.APP_REFRESH);
			BBLog.w(TAG, "sendBroadcast: APP_REFRESH");
			context.sendBroadcast(it, RequestPermission.REQUEST_PERMISSION_MY_BROADCAST);
			WebSocketSender.C0901_AppInfoUpload();
		}else if (Intent.ACTION_PACKAGE_REMOVED.equals(intent.getAction())) {
			String packageName = intent.getData().getSchemeSpecificPart();
			final boolean replacing = intent.getBooleanExtra(Intent.EXTRA_REPLACING, false);
			if (!replacing) {
				Intent it = new Intent(BroadcastActions.APP_REFRESH);
				BBLog.w(TAG, "sendBroadcast: APP_REFRESH");
				context.sendBroadcast(it, RequestPermission.REQUEST_PERMISSION_MY_BROADCAST);
				WebSocketSender.C0901_AppInfoUpload();
				// 如果有app被卸載，則更新app+ 中的相應的task，以防止rule刪除app時判斷app+的情況
//				WebSocketTaskListManager.removeWSTaskJsonObjByPackageName(SPKeys.WEBSOCKET_TASK_LIST, packageName);

				if (UsualData.LOADER_711_PACKAGE_NAME.equals(packageName)) {
					Constants.B_UNBOX_RUNNING = false;
				}
			}
		}
	}

}
