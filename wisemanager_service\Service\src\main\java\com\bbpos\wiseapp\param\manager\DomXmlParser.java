package com.bbpos.wiseapp.param.manager;

import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;
import org.xml.sax.EntityResolver;
import org.xml.sax.InputSource;
import org.xml.sax.SAXException;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.parsers.ParserConfigurationException;

public class DomXmlParser implements XmlParseInterface {

	@Override
	public List<ParamModel> parse(InputStream is) {
		try {
			List<ParamModel> params = new ArrayList<ParamModel>();
			DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance(); // 取得DocumentBuilderFactory实例
			factory.setExpandEntityReferences(false);
			// 从factory获取DocumentBuilder实例
			DocumentBuilder builder = factory.newDocumentBuilder();
			builder.setEntityResolver(new IgnoreDTDEntityResolver());
			builder = factory.newDocumentBuilder();
			Document doc = builder.parse(is); // 解析输入流 得到Document实例
			Element rootElement = doc.getDocumentElement();
			NodeList items = rootElement.getElementsByTagName("para");
			for (int i = 0; i < items.getLength(); i++) {
				ParamModel paramModel = new ParamModel("","","","","");
				Node item = items.item(i);
				NodeList properties = item.getChildNodes();
				for (int j = 0; j < properties.getLength(); j++) {
					Node property = properties.item(j);
					String nodeName = property.getNodeName();
					if (nodeName.equals("key")) {
						if(property.getFirstChild() == null)
							paramModel.key = "";
						else
							paramModel.key = property.getFirstChild().getNodeValue();
					} else if (nodeName.equals("value")) {
						if(property.getFirstChild() == null)
							paramModel.value = "";
						else
							paramModel.value = property.getFirstChild().getNodeValue();
					}
				}
				params.add(paramModel);
			}
			return params;
		} catch (ParserConfigurationException e) {
			e.printStackTrace();
		} catch (SAXException e) {
			e.printStackTrace();
		} catch (IOException e) {
			e.printStackTrace();
		} catch (Exception e) {
			e.printStackTrace();
		}
		return null;
	}

	class IgnoreDTDEntityResolver implements EntityResolver {
		@Override
		public InputSource resolveEntity(String arg0, String arg1) throws SAXException, IOException {
			return new InputSource(new ByteArrayInputStream("<?xml version='1.0' encoding='UTF-8'?>".getBytes()));
		}
	}
}
