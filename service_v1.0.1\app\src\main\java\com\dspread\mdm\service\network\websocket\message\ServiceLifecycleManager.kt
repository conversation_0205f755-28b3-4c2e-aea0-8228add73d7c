package com.dspread.mdm.service.network.websocket.message

import com.dspread.mdm.service.utils.log.Logger
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.json.JSONObject
import java.text.SimpleDateFormat
import java.util.*
import java.util.concurrent.atomic.AtomicBoolean

/**
 * 服务生命周期管理器
 * 管理服务的状态变化、过期检查等
 */
object ServiceLifecycleManager {
    
    private const val TAG = "[ServiceLifecycleManager]"
    
    // 服务状态常量
    object ServiceState {
        const val READY = "READY"
        const val IMPLEMENTED = "IMPLEMENTED" 
        const val COMPLETED = "COMPLETED"
    }
    
    // 日期格式
    private val dateFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault())
    
    // 是否正在运行检查任务
    private val isRunning = AtomicBoolean(false)
    
    /**
     * 启动服务生命周期检查
     */
    fun startLifecycleCheck() {
        if (isRunning.compareAndSet(false, true)) {
            GlobalScope.launch {
                runLifecycleCheck()
            }
        }
    }
    
    /**
     * 停止服务生命周期检查
     */
    fun stopLifecycleCheck() {
        isRunning.set(false)
    }
    
    /**
     * 运行生命周期检查循环
     */
    private suspend fun runLifecycleCheck() {
        while (isRunning.get()) {
            try {
                checkServiceLifecycle()
                delay(60000) // 每分钟检查一次
            } catch (e: Exception) {
                Logger.wsmE("$TAG 服务生命周期检查异常", e)
                delay(60000) // 出错也要等待
            }
        }
    }
    
    /**
     * 检查服务生命周期
     */
    private fun checkServiceLifecycle() {
        try {
            val currentTime = System.currentTimeMillis()
            val activeServices = ServiceInfoManager.getActiveServicesArray()
            var needUpload = false
            
            for (i in 0 until activeServices.length()) {
                val serviceInfo = activeServices.getJSONObject(i)
                val serviceId = serviceInfo.optString("serviceId", "")
                val command = serviceInfo.optString("command", "")
                val taskType = serviceInfo.optString("taskType", "")
                val stateDesc = serviceInfo.optString("stateDesc", "")
                val beginDateStr = serviceInfo.optString("beginDate", "")
                val endDateStr = serviceInfo.optString("endDate", "")
                
                // 解析时间
                val beginTime = parseDate(beginDateStr)
                val endTime = parseDate(endDateStr)
                
                if (beginTime == -1L || endTime == -1L) continue
                
                // 检查服务状态
                when (stateDesc) {
                    ServiceState.IMPLEMENTED -> {
                        // 检查是否过期
                        if (shouldExpireService(currentTime, beginTime, endTime, command, taskType, endDateStr)) {
                            Logger.wsm("$TAG 服务过期: $serviceId")
                            
                            // 更新状态为COMPLETED
                            serviceInfo.put("stateDesc", ServiceState.COMPLETED)
                            ServiceInfoManager.addOrUpdateService(serviceId, serviceInfo)
                            
                            // 发送过期通知
                            sendServiceExpiredNotification(serviceInfo)
                            needUpload = true
                        }
                    }
                }
            }
            
            // 如果有状态变化，触发C0901上传
            if (needUpload) {
                WsMessageSender.uploadAppInfo()
            }
            
        } catch (e: Exception) {
            Logger.wsmE("$TAG 检查服务生命周期失败", e)
        }
    }
    
    /**
     * 判断服务是否应该过期
     */
    private fun shouldExpireService(
        currentTime: Long,
        beginTime: Long, 
        endTime: Long,
        command: String,
        taskType: String,
        endDateStr: String
    ): Boolean {
        // 针对日志流的特殊处理
        if (command == "C03" && taskType == "05" && endDateStr == "9999-12-31 23:59:59") {
            // 日志流服务有效期是3天
            return currentTime >= (beginTime + 24 * 3 * 60 * 60 * 1000L)
        }
        
        // 普通服务按结束时间判断
        return currentTime > endTime
    }
    
    /**
     * 解析日期字符串
     */
    private fun parseDate(dateStr: String): Long {
        return try {
            if (dateStr.isEmpty()) return -1L
            dateFormat.parse(dateStr)?.time ?: -1L
        } catch (e: Exception) {
            -1L
        }
    }
    
    /**
     * 发送服务过期通知
     */
    private fun sendServiceExpiredNotification(serviceInfo: JSONObject) {
        try {
            // 这里可以发送C0108服务结果上报
            Logger.wsm("$TAG 服务过期通知: ${serviceInfo.optString("serviceId")}")
        } catch (e: Exception) {
            Logger.wsmE("$TAG 发送服务过期通知失败", e)
        }
    }
}
