package com.bbpos.wiseapp.service.receiver;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;

import com.bbpos.wiseapp.logger.BBLog;

/**
 * 安全芯片 受攻击 广播接收
 *
 * Intent intent = new Intent();
 * intent.setAction("com.bbpos.bbdevice.ERROR_NOTIFICATION");
 * intent.putExtra("error","TAMPER");
 * intent.putExtra("errorMessage",errorMessage);
 * context.sendBroadcast(intent, "com.bbpos.wiseapp.permissions.MY_BROADCAST");
 */
public class SecurityTamperReceiver extends BroadcastReceiver {
    public static final String ACTION_TAMPER = "com.bbpos.bbdevice.ERROR_NOTIFICATION";
    private String  error; //tepmper
    private String  errMsg;// 错误信息

    @Override
    public void onReceive(Context context, Intent intent) {
        BBLog.e(BBLog.TAG, "onReceive: Security Tamper occur , action:  "+intent.getAction() );
        if (ACTION_TAMPER.equals(intent.getAction())){
            String error = intent.getStringExtra("error");
            String errMsg = intent.getStringExtra("errorMessage");
            BBLog.e(BBLog.TAG, "onReceive: Security Tamper occur , error:  "+error + ", errMsg: "+errMsg );

            // TODO: 2019/11/27 清除终端私密数据，待定
        }
    }
}
