package com.bbpos.wiseapp.service.contentprovider;

import android.content.ContentProvider;
import android.content.ContentValues;
import android.database.Cursor;
import android.database.sqlite.SQLiteDatabase;
import android.net.Uri;

import com.bbpos.wiseapp.logger.BBLog;
import com.bbpos.wiseapp.service.contentprovider.db.CommonDataOpenHelper;
import com.bbpos.wiseapp.service.contentprovider.db.CommonDataOperation;
import com.bbpos.wiseapp.service.contentprovider.db.DbConstants;

import java.util.regex.Pattern;

public class CommonDataProvider extends ContentProvider  {

	private CommonDataOpenHelper db;
	@Override
	public int delete(Uri arg0, String arg1, String[] arg2) {
		return 0;
	}

	@Override
	public String getType(Uri arg0) {
		return null;
	}

	@Override
	public Uri insert(Uri arg0, ContentValues arg1) {
		BBLog.e("urlprovider", "update-----");
		SQLiteDatabase sqdb = db.getWritableDatabase();
		sqdb.insert(DbConstants.DB_TABLE, null, arg1);
		return arg0;
	}

	@Override
	public boolean onCreate() {
		db = CommonDataOperation.open(getContext());
		return false;
	}

	@Override
	public Cursor query(Uri arg0, String[] arg1, String arg2, String[] arg3,
			String arg4) {
		BBLog.e("urlprovider", "query-----");
		SQLiteDatabase sqdb = db.getWritableDatabase();
		Cursor cursor = null;
		cursor = sqdb.query(DbConstants.DB_TABLE, arg1, arg2, arg3, null, null, arg4);
		return cursor;
	}

	@Override
	public int update(Uri arg0, ContentValues arg1, String arg2, String[] arg3) {
		BBLog.e("urlprovider", "update-----");
		SQLiteDatabase sqdb = db.getWritableDatabase();
		if (!Pattern.matches("^(.+)\\sand\\s(.+)|(.+)\\sor(.+)\\s$", arg2)) {
			return 0;
		}
		return sqdb.update(DbConstants.DB_TABLE, arg1, arg2, arg3);
	}
}
