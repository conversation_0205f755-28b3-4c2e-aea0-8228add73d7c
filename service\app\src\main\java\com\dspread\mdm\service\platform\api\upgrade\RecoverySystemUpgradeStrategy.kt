package com.dspread.mdm.service.platform.api.upgrade

import android.content.Context
import android.os.Build
import android.os.RecoverySystem
import com.dspread.mdm.service.utils.log.Logger
import com.dspread.mdm.service.platform.api.model.SystemOperationResult
import java.io.File
import java.io.FileInputStream
import java.io.FileOutputStream

/**
 * RecoverySystem升级策略
 * 适用于Android < 14或不支持A/B分区的设备
 * 使用传统的RecoverySystem.installPackage进行OTA升级
 */
class RecoverySystemUpgradeStrategy(private val context: Context) {

    companion object {
        private const val TAG = "RecoverySystemUpgradeStrategy"

        // MTK平台特定配置
        private const val MTK_UPDATE_PARTITION = "/data/ota_package"
        private const val UPDATE_FILE_PERMISSIONS = "666"

        // 验证模式 (可通过系统属性控制)
        private const val DEFAULT_STRICT_VERIFICATION = false  // 默认使用宽松验证
    }

    /**
     * 检查是否支持RecoverySystem升级
     */
    fun isSupported(): Boolean {
        return try {
            // Android < 14 或不支持A/B分区的设备使用RecoverySystem
            val abUpdate = getSystemProperty("ro.build.ab_update")
            val currentSlot = getSystemProperty("ro.boot.slot_suffix")
            val hasABPartition = abUpdate == "true" || currentSlot.isNotEmpty()
            
            val supported = Build.VERSION.SDK_INT < 34 || !hasABPartition
            Logger.platformI("$TAG RecoverySystem支持检查: $supported (Android ${Build.VERSION.SDK_INT}, A/B分区: $hasABPartition)")
            supported
        } catch (e: Exception) {
            Logger.platformE("$TAG 检查RecoverySystem支持失败", e)
            true // 默认支持，作为fallback
        }
    }

    /**
     * 执行RecoverySystem升级
     */
    fun performUpgrade(
        updateFile: File,
        taskId: String? = null
    ): SystemOperationResult {
        return try {
            Logger.platformI("$TAG 开始RecoverySystem升级: ${updateFile.absolutePath}")
            Logger.platformI("$TAG Android版本: ${Build.VERSION.SDK_INT} (${Build.VERSION.RELEASE})")

            if (!updateFile.exists()) {
                return SystemOperationResult.failure("Update file not found: ${updateFile.absolutePath}")
            }

            // 验证更新包
            Logger.platformI("$TAG 开始验证更新包...")
            if (!verifyUpdatePackage(updateFile)) {
                val errorMsg = "Update package verification failed"
                Logger.platformE("$TAG $errorMsg")
                return SystemOperationResult.failure(errorMsg)
            }
            Logger.platformI("$TAG 更新包验证通过")

            // 检查是否为ZIP格式（RecoverySystem只支持ZIP格式）
            if (!isZipFormat(updateFile)) {
                return SystemOperationResult.failure("RecoverySystem requires ZIP format OTA package")
            }

            // 准备更新文件（复制到系统更新目录）
            val targetPath = prepareUpdateFile(updateFile)
            val targetFile = File(targetPath)

            // 设置文件权限
            setUpdateFilePermissions(targetFile)

            // 使用RecoverySystem安装
            Logger.platformI("$TAG 调用RecoverySystem.installPackage")
            RecoverySystem.installPackage(context, targetFile)

            Logger.platformI("$TAG RecoverySystem升级已启动，设备将重启到Recovery模式")
            SystemOperationResult.success("RecoverySystem upgrade initiated, device will reboot to recovery")

        } catch (e: Exception) {
            Logger.platformE("$TAG RecoverySystem升级失败", e)
            SystemOperationResult.failure("RecoverySystem upgrade failed: ${e.message}")
        }
    }

    /**
     * 验证更新包
     */
    private fun verifyUpdatePackage(updateFile: File): Boolean {
        // 检查是否启用严格验证（可通过系统属性控制）
        val strictVerification = getSystemProperty("persist.mdm.strict_ota_verify", DEFAULT_STRICT_VERIFICATION.toString()).toBoolean()

        return try {
            if (!strictVerification) {
                // 宽松验证模式：只检查文件存在和基本格式
                val isValid = updateFile.exists() && updateFile.length() > 0 && isZipFormat(updateFile)
                Logger.platformI("$TAG 更新包基础验证通过 (宽松模式): $isValid")
                Logger.platformI("$TAG 文件大小: ${updateFile.length() / (1024 * 1024)}MB")
                return isValid
            }

            if (Build.VERSION.SDK_INT >= 19) {
                // Android 4.4+ 使用RecoverySystem严格验证
                Logger.platformI("$TAG 开始严格验证更新包...")
                RecoverySystem.verifyPackage(updateFile, null, null)
                Logger.platformI("$TAG 更新包严格验证成功")
                true
            } else {
                // Android < 4.4 简单验证文件存在和大小
                val isValid = updateFile.exists() && updateFile.length() > 0
                Logger.platformI("$TAG 更新包基础验证: $isValid")
                isValid
            }
        } catch (e: Exception) {
            Logger.platformW("$TAG 更新包验证失败: ${e.message}")

            if (!strictVerification) {
                // 宽松模式下，即使验证失败也继续（仅用于测试）
                Logger.platformW("$TAG 宽松模式：忽略验证失败，继续升级")
                return updateFile.exists() && updateFile.length() > 0
            }

            false
        }
    }

    /**
     * 检查是否为ZIP格式
     */
    private fun isZipFormat(file: File): Boolean {
        return try {
            val buffer = ByteArray(4)
            file.inputStream().use { input ->
                input.read(buffer)
            }

            val magic = String(buffer, Charsets.US_ASCII)
            val isZip = magic == "PK\u0003\u0004"
            Logger.platformI("$TAG OTA包格式检查: ${if (isZip) "ZIP格式" else "非ZIP格式"}")
            isZip

        } catch (e: Exception) {
            Logger.platformW("$TAG 检查OTA包格式失败: ${e.message}")
            false
        }
    }

    /**
     * 准备更新文件
     */
    private fun prepareUpdateFile(sourceFile: File): String {
        val targetPath = "$MTK_UPDATE_PARTITION/${sourceFile.name}"
        
        try {
            Logger.platformI("$TAG 准备更新文件: $targetPath")
            
            // 确保目标目录存在
            val targetDir = File(MTK_UPDATE_PARTITION)
            if (!targetDir.exists()) {
                targetDir.mkdirs()
                Logger.platformI("$TAG 创建目标目录: $MTK_UPDATE_PARTITION")
            }
            
            // 删除已存在的文件
            val targetFile = File(targetPath)
            if (targetFile.exists()) {
                targetFile.delete()
                Logger.platformI("$TAG 删除已存在的文件: $targetPath")
            }
            
            // 复制文件
            Logger.platformI("$TAG 开始复制文件...")
            FileInputStream(sourceFile).use { input ->
                FileOutputStream(targetFile).use { output ->
                    val buffer = ByteArray(8192) // 8KB buffer
                    var totalBytes = 0L
                    var count: Int
                    while (input.read(buffer).also { count = it } > 0) {
                        output.write(buffer, 0, count)
                        totalBytes += count
                        
                        // 每10MB打印一次进度
                        if (totalBytes % (10 * 1024 * 1024) == 0L) {
                            Logger.platformI("$TAG 复制进度: ${totalBytes / (1024 * 1024)}MB")
                        }
                    }
                    output.flush()
                }
            }
            
            Logger.platformI("$TAG 文件复制完成: $targetPath")
            Logger.platformI("$TAG 文件大小: ${targetFile.length()} bytes")
            
            // 删除源文件以节省空间
            try {
                sourceFile.delete()
                Logger.platformI("$TAG 删除源文件: ${sourceFile.absolutePath}")
            } catch (e: Exception) {
                Logger.platformW("$TAG 删除源文件失败: ${e.message}")
            }
            
            return targetPath
            
        } catch (e: Exception) {
            Logger.platformE("$TAG 准备更新文件失败", e)
            throw e
        }
    }

    /**
     * 设置更新文件权限
     */
    private fun setUpdateFilePermissions(updateFile: File) {
        try {
            // 设置文件为可读可写
            updateFile.setReadable(true, false)
            updateFile.setWritable(true, false)
            
            // 通过shell设置权限
            val result = Runtime.getRuntime().exec("chmod $UPDATE_FILE_PERMISSIONS ${updateFile.absolutePath}")
            result.waitFor()
            
            Logger.platformI("$TAG 文件权限设置完成: ${updateFile.absolutePath}")
        } catch (e: Exception) {
            Logger.platformW("$TAG 设置文件权限失败: ${e.message}")
        }
    }

    /**
     * 获取系统属性
     */
    private fun getSystemProperty(key: String): String {
        return try {
            val process = Runtime.getRuntime().exec("getprop $key")
            process.inputStream.bufferedReader().readText().trim()
        } catch (e: Exception) {
            ""
        }
    }

    /**
     * 获取系统属性（带默认值）
     */
    private fun getSystemProperty(key: String, defaultValue: String): String {
        val value = getSystemProperty(key)
        return if (value.isNotEmpty()) value else defaultValue
    }

    /**
     * 重启到Recovery模式
     */
    fun rebootToRecovery(): SystemOperationResult {
        return try {
            Logger.platformI("$TAG 重启到Recovery模式")
            Runtime.getRuntime().exec("reboot recovery")
            SystemOperationResult.success("Reboot to recovery initiated")
        } catch (e: Exception) {
            Logger.platformE("$TAG 重启到Recovery失败", e)
            SystemOperationResult.failure("Reboot to recovery failed: ${e.message}")
        }
    }

    /**
     * 清理更新文件
     */
    fun cleanupUpdateFiles(): SystemOperationResult {
        return try {
            Logger.platformI("$TAG 清理更新文件")

            val updateDir = File(MTK_UPDATE_PARTITION)
            if (updateDir.exists()) {
                updateDir.listFiles()?.forEach { file ->
                    if (file.isFile()) {
                        // 删除所有OTA相关文件
                        val shouldDelete = file.name.endsWith(".zip") ||
                                         file.name.endsWith(".bin") ||
                                         file.name.endsWith(".txt") ||
                                         file.name.startsWith("update")

                        if (shouldDelete) {
                            val deleted = file.delete()
                            if (deleted) {
                                Logger.platformI("$TAG 删除更新文件: ${file.name}")
                            } else {
                                Logger.platformW("$TAG 无法删除文件: ${file.name}")
                            }
                        }
                    }
                }
            }

            SystemOperationResult.success("Update files cleaned")
        } catch (e: Exception) {
            Logger.platformE("$TAG 清理更新文件失败", e)
            SystemOperationResult.failure("Cleanup failed: ${e.message}")
        }
    }

    /**
     * 获取升级进度（RecoverySystem无法获取实时进度）
     */
    fun getUpgradeProgress(): SystemOperationResult {
        return try {
            // RecoverySystem升级无法获取实时进度
            // 只能通过检查系统状态来判断
            val bootMode = getSystemProperty("ro.bootmode")
            val inRecovery = bootMode == "recovery"
            
            if (inRecovery) {
                Logger.platformI("$TAG 设备处于Recovery模式，升级可能正在进行")
                SystemOperationResult.success("Device in recovery mode")
            } else {
                Logger.platformI("$TAG 设备处于正常模式")
                SystemOperationResult.success("Device in normal mode")
            }
        } catch (e: Exception) {
            Logger.platformE("$TAG 获取升级进度失败", e)
            SystemOperationResult.failure("Failed to get upgrade progress: ${e.message}")
        }
    }
}
