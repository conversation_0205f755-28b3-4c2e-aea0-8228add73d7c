package com.dspread.mdm.service.broadcast.handlers.websocket

import android.content.Context
import android.content.Intent
import com.dspread.mdm.service.network.websocket.WebSocketCenter
import com.dspread.mdm.service.network.websocket.message.WsMessageSender
import com.dspread.mdm.service.network.websocket.task.WsTaskManager
import com.dspread.mdm.service.broadcast.core.BroadcastActions
import com.dspread.mdm.service.broadcast.core.HeartbeatEventHandler
import com.dspread.mdm.service.utils.log.Logger

import com.dspread.mdm.service.config.TimerConfig


/**
 * 定时器事件处理器实现
 * 统一处理所有定时器相关的广播事件
 */
class HeartbeatEventHandlerImpl : HeartbeatEventHandler {
    
    private val TAG = "HeartbeatEventHandler"
    
    override fun getHandlerName(): String = TAG
    
    override fun getSupportedActions(): List<String> {
        return listOf(
            BroadcastActions.ACTION_POLL_TIMER_START
            // 注意：WSTASK_EXEC_BC由TaskExecuteEventHandlerImpl专门处理
            // 注意：TER_INFO_UPLOAD_BC由TerminalInfoEventHandler专门处理
        )
    }
    
    override fun handleBroadcast(context: Context, intent: Intent): Boolean {
        val action = intent.action ?: return false
        
        return try {
            when (action) {
                BroadcastActions.ACTION_POLL_TIMER_START -> {
                    onPollTimerStart(context)
                    true
                }
                else -> false
            }
        } catch (e: Exception) {
            Logger.receiverE("$TAG 处理广播失败: $action", e)
            false
        }
    }
    
    override fun onPollTimerStart(context: Context) {
        Logger.receiver("收到轮询定时器广播")

        try {
            // 检查WebSocket连接状态
            val isConnected = WebSocketCenter.isConnected()

            if (isConnected) {
                // 发送心跳（hello message "1"）
                WebSocketCenter.heartBeat()
//                Logger.receiver("发送心跳")

                // 执行其他轮询任务
                executePollingTasks(context)

            } else {
                Logger.receiver("WebSocket未连接，跳过轮询任务")
            }

            // 安排下一次心跳（5分钟后）
            scheduleNextHeartbeat(context)

        } catch (e: Exception) {
            Logger.receiverE("执行轮询任务失败", e)
        }
    }





    override fun onTimerEvent(context: Context, timerType: String, data: String?) {
        Logger.receiver("处理定时器事件: 类型=$timerType, 数据=$data")
        
        try {
            when (timerType) {
                "status_report" -> {
                    // 状态上报定时器
                    executeStatusReport(context)
                }
                "health_check" -> {
                    // 健康检查定时器
                    executeHealthCheck(context)
                }
                "log_upload" -> {
                    // 日志上传定时器
                    executeLogUpload(context)
                }
                "traffic_upload" -> {
                    // 流量统计上传定时器
                    executeTrafficUpload(context)
                }
                else -> {
                    Logger.receiver("未知的定时器类型: $timerType")
                }
            }
        } catch (e: Exception) {
            Logger.receiverE("处理定时器事件失败: $timerType", e)
        }
    }
    
    // 设备状态上传频率控制
    private var lastDeviceStatusUploadTime = System.currentTimeMillis() // 初始化为当前时间
    private val DEVICE_STATUS_INTERVAL = 60000L // 60秒

    /**
     * 执行轮询任务
     */
    private fun executePollingTasks(context: Context) {
        try {
            val currentTime = System.currentTimeMillis()

            // 注意：任务执行由独立的TaskExecuteEventHandler处理，心跳定时器不再触发任务执行
            // 这样避免了定时器冲突和重复触发的问题

            // 定时重发未确认的任务和规则结果
            uploadPendingResults()

            // 上报设备状态（降低频率，使用正确的触发条件）
            if (currentTime - lastDeviceStatusUploadTime > DEVICE_STATUS_INTERVAL) {
                WsMessageSender.uploadDeviceStatus(
                    deviceStatus = "normal",
                    isInUse = true,
                    isWebSocketConnected = WebSocketCenter.isConnected(),
                    trigger = "scheduled_report"  // 定时上报
                )
                lastDeviceStatusUploadTime = currentTime
            }

        } catch (e: Exception) {
            Logger.receiverE("执行轮询任务失败", e)
        }
    }



    /**
     * 安排下一次心跳（根据Provisioning配置的间隔）
     */
    fun scheduleNextHeartbeat(context: Context) {
        try {
            // 获取心跳间隔（从Provisioning配置读取）
            val heartbeatInterval = getHeartbeatInterval(context)

            val intent = Intent(BroadcastActions.ACTION_POLL_TIMER_START).apply {
                setPackage(context.packageName)
            }
            val pendingIntent = android.app.PendingIntent.getBroadcast(
                context,
                0, // 心跳定时器专用requestCode
                intent,
                android.app.PendingIntent.FLAG_UPDATE_CURRENT or android.app.PendingIntent.FLAG_IMMUTABLE
            )

            val alarmManager = context.getSystemService(Context.ALARM_SERVICE) as android.app.AlarmManager
            val triggerTime = System.currentTimeMillis() + (heartbeatInterval * 1000)

            // 取消之前的定时器
            alarmManager.cancel(pendingIntent)

            // 使用精确的闹钟
            alarmManager.setExact(
                android.app.AlarmManager.RTC_WAKEUP,
                triggerTime,
                pendingIntent
            )

            val minutes = heartbeatInterval / 60
            val timeDesc = if (minutes >= 1) "${minutes}分钟" else "${heartbeatInterval}秒"
            Logger.success("⏰ 设置心跳定时器成功，下次执行: ${heartbeatInterval}秒后 ($timeDesc)")

        } catch (e: Exception) {
            Logger.receiverE("安排下一次心跳失败", e)
        }
    }

    /**
     * 获取心跳间隔（支持调试模式和生产模式）
     */
    private fun getHeartbeatInterval(context: Context): Long {
        return TimerConfig.getHeartbeatInterval(context)
    }


    /**
     * 上传待发送的结果
     */
    private fun uploadPendingResults() {
        try {
            Logger.receiver("检查待上传的任务和规则结果")

            // 上传待发送的任务结果
            WsTaskManager.uploadWSTaskResult()

            // 上传待发送的规则结果（需要实现）
            uploadPendingRuleResults()

        } catch (e: Exception) {
            Logger.receiverE("上传待发送结果失败", e)
        }
    }

    /**
     * 上传待发送的规则结果
     */
    private fun uploadPendingRuleResults() {
        try {
            // 调用WsTaskManager的规则结果重发方法
            WsTaskManager.uploadRuleResult()
        } catch (e: Exception) {
            Logger.receiverE("上传待发送规则结果失败", e)
        }
    }


    
    /**
     * 执行状态上报
     */
    private fun executeStatusReport(context: Context) {
        try {
            Logger.receiver("执行状态上报任务")
            
            // 上报设备状态
            WsMessageSender.uploadDeviceStatus("normal", true, WebSocketCenter.isConnected())
            
            // 上报电池状态
            WsMessageSender.uploadBatteryStatus()
            
            // 上报网络状态
            WsMessageSender.uploadNetworkStatus()
            
        } catch (e: Exception) {
            Logger.receiverE("执行状态上报失败", e)
        }
    }
    
    /**
     * 执行健康检查
     */
    private fun executeHealthCheck(context: Context) {
        try {
            Logger.receiver("执行健康检查任务")
            
            // 检查WebSocket连接状态
            if (!WebSocketCenter.isConnected()) {
                Logger.receiver("WebSocket连接异常，尝试重连")
            }
            
            // 检查内存使用情况
            val runtime = Runtime.getRuntime()
            val usedMemory = runtime.totalMemory() - runtime.freeMemory()
            val maxMemory = runtime.maxMemory()
            val memoryUsage = (usedMemory * 100 / maxMemory).toInt()
            
            Logger.receiver("内存使用率: $memoryUsage%")
            
            if (memoryUsage > 80) {
                Logger.receiver("内存使用率过高，建议清理")
                // 可以触发垃圾回收或其他清理操作
                System.gc()
            }
            
        } catch (e: Exception) {
            Logger.receiverE("执行健康检查失败", e)
        }
    }
    
    /**
     * 执行日志上传
     */
    private fun executeLogUpload(context: Context) {
        try {
            Logger.receiver("执行日志上传任务")

            // 这里可以添加日志上传逻辑
            // 例如：收集日志文件、压缩、上传等

        } catch (e: Exception) {
            Logger.receiverE("执行日志上传失败", e)
        }
    }

    /**
     * 执行流量统计上传
     */
    private fun executeTrafficUpload(context: Context) {
        try {
            Logger.receiver("执行流量统计上传任务")

            // 调用WsMessageSender的流量上送方法
            WsMessageSender.uploadNetworkTraffic(
                trigger = "daily_traffic_report",
                forceUpload = true
            )

        } catch (e: Exception) {
            Logger.receiverE("执行流量统计上传失败", e)
        }
    }
    
    /**
     * 从Action中提取定时器类型
     */
    private fun extractTimerType(action: String): String? {
        return when {
            action.contains("STATUS_REPORT") -> "status_report"
            action.contains("HEALTH_CHECK") -> "health_check"
            action.contains("LOG_UPLOAD") -> "log_upload"
            else -> null
        }
    }
}
