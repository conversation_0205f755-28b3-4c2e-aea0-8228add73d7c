package com.bbpos.wiseapp.tms.location;

import android.content.Context;
import android.os.Build;
import android.telephony.CellSignalStrength;
import android.telephony.ServiceState;
import android.telephony.SignalStrength;
import android.telephony.TelephonyManager;
import android.telephony.cdma.CdmaCellLocation;
import android.telephony.gsm.GsmCellLocation;
import android.util.Log;

import com.bbpos.wiseapp.logger.BBLog;
import com.bbpos.wiseapp.service.R;
import com.bbpos.wiseapp.settings.utils.APNManager;
import com.bbpos.wiseapp.settings.utils.WirelessUtil;
import com.bbpos.wiseapp.tms.utils.ContextUtil;
import com.bbpos.wiseapp.utils.SystemUtils;
import com.bbpos.wiseapp.websocket.handler.ApnHandler;

import org.json.JSONObject;

public class CellLocationManager {

	private Context mContext;
	private TelephonyManager telephonyManager;

	public CellLocationManager(Context context) {
		mContext = context;
		telephonyManager = (TelephonyManager) ContextUtil.getInstance().getSystemService(Context.TELEPHONY_SERVICE);
	}

	/** 基站信息结构体 */
	public class SCell {
		public String MCC;
		public String MNC;
		public int LAC;
		public int CID;
		public int DBM;
	}

	/**
	 * 获取基站信息
	 * 
	 * @throws Exception
	 */
	public SCell getCellInfo() throws Exception {
		SCell cell = new SCell();

		/** 调用API获取基站信息 */
		TelephonyManager mTelNet = (TelephonyManager) mContext.getSystemService(Context.TELEPHONY_SERVICE);
		String operator = mTelNet.getNetworkOperator();
		if (operator != null) {
			cell.MCC = operator.substring(0, 3);
			cell.MNC = operator.substring(3);
		}

		// 获取网络类型
		int phoneType = mTelNet.getPhoneType();

		if (phoneType == TelephonyManager.PHONE_TYPE_CDMA) {
			CdmaCellLocation location = (CdmaCellLocation) mTelNet.getCellLocation();
			if (location == null)
				throw new Exception(mContext.getResources().getString(R.string.obtain_bs_failed));
			int cellIDs = location.getBaseStationId();
			int networkID = location.getNetworkId();
			StringBuilder sb = new StringBuilder();
			sb.append(location.getSystemId());
			/** 将获得的数据放到结构体中 */
			cell.LAC = networkID;
			cell.CID = cellIDs;
		} else if (phoneType == TelephonyManager.PHONE_TYPE_GSM) {
			GsmCellLocation location = (GsmCellLocation) mTelNet.getCellLocation();

			if (location == null)
				throw new Exception(mContext.getResources().getString(R.string.obtain_bs_failed));
			int cid = location.getCid();
			int lac = location.getLac();

			/** 将获得的数据放到结构体中 */

			cell.LAC = lac;
			cell.CID = cid;
		}
		cell.DBM = WirelessUtil.getMobileDbm(ContextUtil.getInstance());
		return cell;
	}

	/**return null if loaction get false*/
	public JSONObject getLocationJsonCell() {
		SCell cell = null;
		try {
			cell = getCellInfo();
			/** 构造POST的JSON数据 */
			JSONObject tower = new JSONObject();
			tower.put("MCC",cell.MCC);
			tower.put("MNC", cell.MNC);
			tower.put("CID", ""+cell.CID);
			tower.put("LAC", ""+cell.LAC);
			tower.put("DBM", ""+cell.DBM);
			tower.put("IMSI", WirelessUtil.getIMSI(ContextUtil.getInstance()));
			tower.put("ICCID", WirelessUtil.getICCID(ContextUtil.getInstance()));
			/** 获取当前apn信息 */
			if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
				APNManager.APN currentApn = APNManager.getInstance().getDefaultApnBySubId(APNManager.getInstance().getDefaultSubscriptionId());
				if (currentApn != null) {
					tower.put("APN_NAME",currentApn.getName());
					tower.put("APN",currentApn.getApn());

					String proId = "";
					if (currentApn != null) {
						proId = ApnHandler.getAnpProIdFromCacheIfExits(currentApn.getApn(),currentApn.getMcc(),currentApn.getMnc());
					}
					tower.put("APN_PROFILE_ID",proId);
				}
			}

			return tower;
//			JSONArray towerarray = new JSONArray();
//			towerarray.put(tower);
//
//			/** 调用API获取基站信息 */
//			TelephonyManager mTelNet = (TelephonyManager) mContext.getSystemService(Context.TELEPHONY_SERVICE);
//
//			List<NeighboringCellInfo> infos = mTelNet.getNeighboringCellInfo();
//
//			for (NeighboringCellInfo info1 : infos) { // 根据邻区总数进行循环
//				JSONObject towers = new JSONObject();
//				towers.put("MCC", "");
//				towers.put("MNC", "");
//				towers.put("CID", info1.getCid());
//				towers.put("LAC", info1.getLac());
//				towerarray.put(towers);
//			}
//			return towerarray.toString();
		} catch (Exception e) {
			BBLog.e("CellLocationManager", "get cell info error" + e.getMessage());
			return null;
		}
	}

	/**
	 * 获取4G网络信号
	 * @return
	 */
	public int getSimSignalStrength() {
		if (telephonyManager != null) {
			int networkType = telephonyManager.getDataNetworkType();
			int dbm = -1;

			// Check if the network type is 4G
			if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
				if (networkType == TelephonyManager.NETWORK_TYPE_LTE) {
					dbm = getLTESignalStrength();
				}
			}
			return dbm;
		}

		return -1;
	}

	private int getLTESignalStrength() {
		int dbm = -1;
		if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
			SignalStrength signalStrength = telephonyManager.getSignalStrength();
			if (signalStrength != null) {
				int gsmSignalStrength = signalStrength.getGsmSignalStrength();
				int asu = (gsmSignalStrength == 99) ? -1 : gsmSignalStrength;
				dbm = -113 + (2 * asu);
				BBLog.d("CellLocationManager", "LTE Signal Strength: " + dbm + " dBm");
			}
		}
		return dbm;
	}

	public String getIMSI() {
		if (telephonyManager != null) {
			try {
				return telephonyManager.getSubscriberId();
			} catch (SecurityException e) {
				// 处理没有相应权限的情况
				e.printStackTrace();
			}
		}
		return "";
	}

	public String getICCID() {
		if (telephonyManager != null) {
			try {
				return telephonyManager.getSimSerialNumber();
			} catch (SecurityException e) {
				e.printStackTrace();
			}
		}
		return "";
	}
}
