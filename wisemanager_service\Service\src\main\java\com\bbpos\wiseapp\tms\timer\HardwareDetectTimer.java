package com.bbpos.wiseapp.tms.timer;

import android.app.AlarmManager;
import android.app.PendingIntent;
import android.content.BroadcastReceiver;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.os.Build;

import com.bbpos.wiseapp.logger.BBLog;
import com.bbpos.wiseapp.system.api.ServiceApi;
import com.bbpos.wiseapp.tms.service.HardwareDetectUploadSerivce;
import com.bbpos.wiseapp.tms.service.TerminalInfoUploadSerivce;
import com.bbpos.wiseapp.tms.utils.BroadcastActions;
import com.bbpos.wiseapp.tms.utils.Constants;
import com.bbpos.wiseapp.websocket.WebSocketCenter;

public class HardwareDetectTimer extends BroadcastReceiver {
    private static final String TAG = "HardwareDetectTimer";
    @Override
    public void onReceive(Context context, Intent intent) {
        String action = intent.getAction();
        BBLog.v(BBLog.TAG, "HardwareDetectTimer Receiver Broadcast " + action);
        if (BroadcastActions.HARDWARE_DETECT_UPLOAD_BC.equals(action)){
            if (WebSocketCenter.isWebSocketConnected) {
                Intent intentService = new Intent(context, HardwareDetectUploadSerivce.class);
                ServiceApi.getIntance().startService(intentService);
            }
            startHardwareDetectTimer(context);
        }
    }

    /**启动轮询定时广播*/
    public static void startHardwareDetectTimer(Context context){
        BBLog.i(TAG, "startHardwareDetectTimer in " + 60 * 1000);
        Intent intentTmp = new Intent(BroadcastActions.HARDWARE_DETECT_UPLOAD_BC);
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            intentTmp.setComponent(new ComponentName(context.getPackageName(), HardwareDetectTimer.class.getName()));
        }
        PendingIntent pi = PendingIntent.getBroadcast(context, 0, intentTmp, PendingIntent.FLAG_UPDATE_CURRENT);
        AlarmManager am = (AlarmManager) context.getSystemService(Context.ALARM_SERVICE);
        am.cancel(pi);
        long timeOnMillis = System.currentTimeMillis() + 60 * 1000;

        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.KITKAT) {
            am.set(AlarmManager.RTC_WAKEUP, timeOnMillis, pi);
        } else {
            am.setExact(AlarmManager.RTC_WAKEUP, timeOnMillis, pi);
        }
    }
}
