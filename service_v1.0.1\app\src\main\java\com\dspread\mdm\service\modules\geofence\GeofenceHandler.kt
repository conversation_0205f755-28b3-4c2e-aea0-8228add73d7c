package com.dspread.mdm.service.modules.geofence

import android.content.Context
import android.preference.PreferenceManager
import android.text.TextUtils
import com.dspread.mdm.service.modules.BaseModuleHandler
import com.dspread.mdm.service.modules.geofence.location.GpsLocationManager
import com.dspread.mdm.service.modules.geofence.model.GeofenceConfig
import com.dspread.mdm.service.modules.geofence.model.LocationInfo
import com.dspread.mdm.service.utils.log.Logger
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import org.json.JSONArray
import org.json.JSONObject
import java.text.SimpleDateFormat
import java.util.*

/**
 * 地理围栏WebSocket消息处理器
 * 处理SC008类型的地理围栏配置消息
 */
class GeofenceHandler(
    private val context: Context
) : BaseModuleHandler() {
    
    companion object {
        private const val TAG = "[GeofenceHandler]"

        private const val DATE_FORMAT = "yyyy-MM-dd HH:mm:ss"

        // SharedPreferences键名
        private const val PREF_GEO_PROFILE_LIST = "WEBSOCKET_GEOFENCE_PROFILE_LIST"
        private const val PREF_GEO_CURRENT_PROFILE = "geofence_current_profile"
    }
    
    private val geofenceManager by lazy { GeofenceManager(context) }
    
    override fun getModuleName(): String = "Geofence"
    
    override suspend fun handleMessage(message: String): Result<Unit> {
        return try {
            // 检查模块是否启用
            checkModuleEnabled().getOrThrow()

            Logger.geo("$TAG 开始处理地理围栏配置消息")

            val jsonObject = parseJsonMessage(message)

            // 发送C0000响应确认
            sendAcknowledgment(jsonObject)

            // 直接处理地理围栏配置消息
            val data = jsonObject.optJSONObject("data")
            if (data != null) {
                val geoInfo = data.optJSONObject("geoInfo")
                if (geoInfo != null) {
                    // 直接处理Profile消息
                    val success = handleGeoProfileMessage(geoInfo)
                    if (success) {
                        Logger.geo("$TAG 地理围栏Profile处理成功")
                    } else {
                        Logger.geoW("$TAG 地理围栏Profile处理失败")
                    }
                } else {
                    // 解析地理围栏配置
                    val geofenceConfig = parseGeofenceConfig(jsonObject)

                    if (geofenceConfig == null) {
                        Logger.geoW("$TAG 地理围栏配置解析失败或无效")
                        return Result.success(Unit)
                    }

                    Logger.geo("$TAG 解析到地理围栏配置: 中心(${geofenceConfig.latitude}, ${geofenceConfig.longitude}), 半径=${geofenceConfig.radius}米")

                    // 异步处理地理围栏配置（避免阻塞WebSocket消息处理）
                    GlobalScope.launch {
                        val result = geofenceManager.updateGeofenceConfig(geofenceConfig)
                        if (result.isFailure) {
                            Logger.geoE("$TAG 地理围栏配置更新失败", result.exceptionOrNull())
                        } else {
                            Logger.geo("$TAG 地理围栏配置更新完成")
                        }
                    }
                }
            }

            Result.success(Unit)

        } catch (e: Exception) {
            Logger.geoE("$TAG 地理围栏消息处理失败", e)
            Result.failure(e)
        }
    }
    
    /**
     * 解析地理围栏配置
     */
    private fun parseGeofenceConfig(jsonObject: JSONObject): GeofenceConfig? {
        return try {
            val data = jsonObject.optJSONObject("data")
            if (data == null) {
                Logger.geoW("$TAG 消息中缺少data字段")
                return null
            }
            
            // 检查是否有地理围栏配置
            val geoInfo = data.optJSONObject("geoInfo")
            if (geoInfo != null) {
                // 新格式：geoInfo对象
                parseGeofenceFromGeoInfo(geoInfo)
            } else {
                // 旧格式：直接在data中
                parseGeofenceFromData(data)
            }
            
        } catch (e: Exception) {
            Logger.geoE("$TAG 解析地理围栏配置失败", e)
            null
        }
    }
    
    /**
     * 从geoInfo对象解析地理围栏配置
     */
    private fun parseGeofenceFromGeoInfo(geoInfo: JSONObject): GeofenceConfig? {
        return try {
            val config = GeofenceConfig.fromJson(geoInfo)
            
            if (config?.isValid() == true) {
                Logger.geo("$TAG 从geoInfo解析地理围栏配置成功")
                config
            } else {
                Logger.geoW("$TAG geoInfo中的地理围栏配置无效")
                null
            }
        } catch (e: Exception) {
            Logger.geoE("$TAG 从geoInfo解析地理围栏配置失败", e)
            null
        }
    }
    
    /**
     * 从data对象解析地理围栏配置（兼容旧格式）
     */
    private fun parseGeofenceFromData(data: JSONObject): GeofenceConfig? {
        return try {
            // 检查是否包含地理围栏相关字段
            if (!data.has("latitude") || !data.has("longitude")) {
                Logger.geo("$TAG data中未找到地理围栏配置")
                return null
            }
            
            val config = GeofenceConfig.fromJson(data)
            
            if (config?.isValid() == true) {
                Logger.geo("$TAG 从data解析地理围栏配置成功")
                config
            } else {
                Logger.geoW("$TAG data中的地理围栏配置无效")
                null
            }
        } catch (e: Exception) {
            Logger.geoE("$TAG 从data解析地理围栏配置失败", e)
            null
        }
    }
    
    /**
     * 处理地理围栏状态查询消息
     */
    suspend fun handleStatusQuery(message: String): Result<JSONObject> {
        return try {
            Logger.geo("$TAG 处理地理围栏状态查询")
            
            val currentStatus = geofenceManager.getCurrentStatus()
            val currentLocation = geofenceManager.getCurrentLocation()
            val config = geofenceManager.getCurrentConfig()
            
            val response = JSONObject().apply {
                put("status", currentStatus.value)
                put("statusDescription", currentStatus.description)
                
                currentLocation?.let { location ->
                    put("currentLatitude", location.latitude)
                    put("currentLongitude", location.longitude)
                    put("accuracy", location.accuracy)
                    put("timestamp", location.timestamp)
                }
                
                config?.let { cfg ->
                    put("geofenceLatitude", cfg.latitude)
                    put("geofenceLongitude", cfg.longitude)
                    put("geofenceRadius", cfg.radius)
                    
                    currentLocation?.let { location ->
                        val distance = location.distanceToGeofence(cfg)
                        put("distanceToCenter", distance)
                        put("isInGeofence", distance <= cfg.radius)
                    }
                }
                
                put("gpsEnabled", geofenceManager.isGpsEnabled())
                put("bluetoothEnabled", geofenceManager.isBluetoothEnabled())
                put("wifiEnabled", geofenceManager.isWifiEnabled())
            }
            
            Logger.geo("$TAG 地理围栏状态查询完成")
            Result.success(response)
            
        } catch (e: Exception) {
            Logger.geoE("$TAG 地理围栏状态查询失败", e)
            Result.failure(e)
        }
    }
    
    /**
     * 处理地理围栏控制命令
     */
    suspend fun handleControlCommand(command: String, parameters: JSONObject): Result<Unit> {
        return try {
            Logger.geo("$TAG 处理地理围栏控制命令: $command")
            
            when (command.lowercase()) {
                "start" -> {
                    geofenceManager.startMonitoring()
                }
                
                "stop" -> {
                    geofenceManager.stopMonitoring()
                }
                
                "reset" -> {
                    geofenceManager.resetStatus()
                }
                
                "test_location" -> {
                    // 测试位置（用于调试）
                    val lat = parameters.optDouble("latitude", 0.0)
                    val lon = parameters.optDouble("longitude", 0.0)
                    if (lat != 0.0 && lon != 0.0) {
                        val testLocation = LocationInfo(
                            latitude = lat,
                            longitude = lon,
                            accuracy = 10f,
                            timestamp = System.currentTimeMillis(),
                            provider = "test"
                        )
                        geofenceManager.processLocationUpdate(testLocation)
                    }
                }
                
                "force_security_action" -> {
                    // 强制执行安全措施（用于测试）
                    val actionType = parameters.optString("action", "")
                    when (actionType) {
                        "lock" -> geofenceManager.executeLockScreen()
                        "wipe" -> geofenceManager.executeDataWipe()
                        "reboot" -> geofenceManager.executeReboot()
                    }
                }
                
                else -> {
                    Logger.geoW("$TAG 未知的地理围栏控制命令: $command")
                    return Result.failure(IllegalArgumentException("Unknown command: $command"))
                }
            }
            
            Logger.geo("$TAG 地理围栏控制命令执行完成: $command")
            Result.success(Unit)
            
        } catch (e: Exception) {
            Logger.geoE("$TAG 地理围栏控制命令执行失败: $command", e)
            Result.failure(e)
        }
    }
    
    /**
     * 处理紧急情况
     */
    suspend fun handleEmergencyMode(enable: Boolean): Result<Unit> {
        return try {
            Logger.geo("$TAG 处理紧急模式: ${if (enable) "启用" else "禁用"}")
            
            if (enable) {
                // 启用紧急模式：暂停地理围栏监控
                geofenceManager.enableEmergencyMode()
                Logger.geo("$TAG 紧急模式已启用，地理围栏监控已暂停")
            } else {
                // 禁用紧急模式：恢复地理围栏监控
                geofenceManager.disableEmergencyMode()
                Logger.geo("$TAG 紧急模式已禁用，地理围栏监控已恢复")
            }
            
            Result.success(Unit)
            
        } catch (e: Exception) {
            Logger.geoE("$TAG 紧急模式处理失败", e)
            Result.failure(e)
        }
    }
    
    /**
     * 获取地理围栏统计信息
     */
    suspend fun getGeofenceStatistics(): Result<JSONObject> {
        return try {
            val stats = geofenceManager.getStatistics()
            
            val response = JSONObject().apply {
                put("totalLocationUpdates", stats.totalLocationUpdates)
                put("gpsLocationUpdates", stats.gpsLocationUpdates)
                put("networkLocationUpdates", stats.networkLocationUpdates)
                put("bluetoothDetections", stats.bluetoothDetections)
                put("wifiDetections", stats.wifiDetections)
                put("geofenceEnterEvents", stats.geofenceEnterEvents)
                put("geofenceExitEvents", stats.geofenceExitEvents)
                put("securityActionsExecuted", stats.securityActionsExecuted)
                put("lastLocationUpdate", stats.lastLocationUpdate)
                put("lastGeofenceEvent", stats.lastGeofenceEvent)
                put("averageGpsAccuracy", stats.averageGpsAccuracy)
                put("batteryOptimizationEnabled", stats.batteryOptimizationEnabled)
            }
            
            Logger.geo("$TAG 地理围栏统计信息获取完成")
            Result.success(response)
            
        } catch (e: Exception) {
            Logger.geoE("$TAG 获取地理围栏统计信息失败", e)
            Result.failure(e)
        }
    }

    /**
     * 处理地理围栏配置消息
     */
    fun handleGeoProfileMessage(geoInfo: JSONObject): Boolean {
        return try {
            Logger.geo("$TAG ========== 开始处理地理围栏配置消息 ==========")
            Logger.geo("$TAG 接收到的配置信息: $geoInfo")

            val action = geoInfo.optString("action", "")
            val proId = geoInfo.optString("proId", "")
            Logger.geo("$TAG 操作类型: $action, Profile ID: $proId")

            if (geoInfo.has("action") && "D" == geoInfo.getString("action")) {
                // 删除Profile
                Logger.geo("$TAG 执行删除Profile操作")
                deleteGeoProfileFromList(geoInfo)
            } else {
                // 添加或更新Profile
                Logger.geo("$TAG 执行添加/更新Profile操作")
                processGeoProfile(geoInfo)
            }

            Logger.geo("$TAG ========== 地理围栏配置消息处理完成 ==========")
            true
        } catch (e: Exception) {
            Logger.geoE("$TAG 处理地理围栏配置失败", e)
            false
        }
    }

    /**
     * 处理地理围栏Profile
     */
    private fun processGeoProfile(geoInfo: JSONObject) {
        try {
            Logger.geo("$TAG --- 开始处理地理围栏Profile ---")
            val proId = geoInfo.optString("proId", "")
            Logger.geo("$TAG 处理Profile ID: $proId")
            Logger.geo("$TAG 处理地理围栏Profile: $geoInfo")

           // 获取店铺信息
           val latitudeStr = geoInfo.optString("storeLat", "")
           val longitudeStr = geoInfo.optString("storeLng", "")
           Logger.geo("${TAG} 解析坐标字符串: storeLat='$latitudeStr', storeLng='$longitudeStr'")

           // 保存店铺坐标信息
           if (!TextUtils.isEmpty(latitudeStr) && !TextUtils.isEmpty(longitudeStr)) {
               try {
                   val latitude = latitudeStr.toDouble()
                   val longitude = longitudeStr.toDouble()

                   if (latitude != 0.0 && longitude != 0.0) {
                       // 保存到GPS配置
                       val sharedPrefs = context.getSharedPreferences("gps_config", Context.MODE_PRIVATE)
                       sharedPrefs.edit().apply {
                           putString("store_latitude", latitudeStr)
                           putString("store_longitude", longitudeStr)
                           apply()
                       }
                       Logger.geo("${TAG} 店铺坐标已保存: ($latitudeStr, $longitudeStr)")
                   } else {
                       Logger.geo("${TAG} 店铺坐标为零值: lat=$latitude, lng=$longitude")
                   }
               } catch (e: NumberFormatException) {
                   Logger.geoE("${TAG} 店铺坐标格式错误: storeLat='$latitudeStr', storeLng='$longitudeStr'", e)
               }
           } else {
               Logger.geo("${TAG} 店铺坐标字符串为空")
           }

           // 保存其他店铺信息
           val storeId = geoInfo.optString("storeId", "")
           val storeSsid = geoInfo.optString("storeSsid", "")
           val storeIp = geoInfo.optString("storeIp", "")

           if (!TextUtils.isEmpty(storeId) || !TextUtils.isEmpty(storeSsid) || !TextUtils.isEmpty(storeIp)) {
               val sharedPrefs = context.getSharedPreferences("store_config", Context.MODE_PRIVATE)
               sharedPrefs.edit().apply {
                   if (!TextUtils.isEmpty(storeId)) {
                       putString("store_id", storeId)
                       Logger.geo("${TAG} 保存店铺ID: $storeId")
                   }
                   if (!TextUtils.isEmpty(storeSsid)) {
                       putString("store_ssid", storeSsid)
                       Logger.geo("${TAG} 保存店铺SSID: $storeSsid")
                   }
                   if (!TextUtils.isEmpty(storeIp)) {
                       putString("store_ip", storeIp)
                       Logger.geo("${TAG} 保存店铺IP: $storeIp")
                   }
                   apply()
               }
           }

            val currentProfile = getCurrentProfile()
            val nowTime = System.currentTimeMillis()



            // 检查时间有效性
            val beginDateStr = geoInfo.getString("beginDate")
            val endDateStr = geoInfo.getString("endDate")
            val modifyDateStr = geoInfo.optString("modifyDate", "")

            val (beginTime, endTime, modifyTime) = parseTimeStrings(beginDateStr, endDateStr, modifyDateStr)

            // 检查当前Profile的修改时间
            var currentModifyTime = 0L
            var checkModifyDate = false

            if (!TextUtils.isEmpty(currentProfile)) {
                Logger.geo("$TAG 检查当前Profile修改时间...")
                try {
                    val currentProfileJson = JSONObject(currentProfile)
                    val currentModifyDateStr = currentProfileJson.optString("modifyDate", "")
                    Logger.geo("$TAG 当前Profile修改时间字符串: $currentModifyDateStr")

                    if (!TextUtils.isEmpty(currentModifyDateStr)) {
                        currentModifyTime = if (isNumeric(currentModifyDateStr)) {
                            currentModifyDateStr.toLong()
                        } else {
                            SimpleDateFormat(DATE_FORMAT, Locale.getDefault()).parse(currentModifyDateStr)?.time ?: 0L
                        }
                        checkModifyDate = true
                        Logger.geo("$TAG 当前Profile修改时间: $currentModifyTime (${SimpleDateFormat(DATE_FORMAT, Locale.getDefault()).format(Date(currentModifyTime))})")
                    } else {
                        Logger.geo("$TAG 当前Profile没有修改时间信息")
                    }
                } catch (e: Exception) {
                    Logger.geoE("$TAG 解析当前Profile修改时间失败", e)
                }
            } else {
                Logger.geo("$TAG 当前没有Profile信息")
            }

            // 判断是否执行Profile
            Logger.geo("$TAG 时间验证:")
            Logger.geo("$TAG 当前时间: $nowTime (${SimpleDateFormat(DATE_FORMAT, Locale.getDefault()).format(Date(nowTime))})")
            Logger.geo("$TAG 生效时间: $beginTime (${SimpleDateFormat(DATE_FORMAT, Locale.getDefault()).format(Date(beginTime))})")
            Logger.geo("$TAG 失效时间: $endTime (${SimpleDateFormat(DATE_FORMAT, Locale.getDefault()).format(Date(endTime))})")
            Logger.geo("$TAG 修改时间: $modifyTime (${SimpleDateFormat(DATE_FORMAT, Locale.getDefault()).format(Date(modifyTime))})")

            when {
                beginTime < nowTime && endTime > nowTime -> {
                    // 在有效时间范围内
                    Logger.geo("$TAG Profile在有效时间范围内")
                    if (!checkModifyDate || (checkModifyDate && modifyTime > currentModifyTime)) {
                        Logger.geo("$TAG 修改时间验证通过，开始检查disableTag...")
                        val disableTag = "1" == geoInfo.optString("disableTag")
                        Logger.geo("$TAG disableTag状态: $disableTag")

                        if (disableTag) {
                            Logger.geo("$TAG Profile被禁用，检查是否需要恢复默认配置...")
                            if (!TextUtils.isEmpty(currentProfile)) {
                                val currentProfileJson = JSONObject(currentProfile)
                                val currentProfileId = currentProfileJson.getString("proId")
                                val newProfileId = geoInfo.getString("proId")
                                Logger.geo("$TAG 当前Profile ID: $currentProfileId, 新Profile ID: $newProfileId")

                                if (currentProfileId == newProfileId) {
                                    Logger.geo("$TAG 执行默认Profile配置")
                                    val defaultProfile = getDefaultGeoProfile()
                                    if (defaultProfile != null) {
                                        GpsLocationManager.executeGeoProfile(context, defaultProfile)
                                    } else {
                                        Logger.geo("$TAG 未找到默认Profile，禁用地理围栏")
                                        GpsLocationManager.executeGeoProfile(context, null)
                                    }
                                } else {
                                    Logger.geo("$TAG Profile ID不匹配，跳过默认配置")
                                }
                            } else {
                                Logger.geo("$TAG 当前没有Profile，跳过默认配置")
                            }
                        } else {
                            Logger.geo("$TAG 执行新的Profile配置")
                            GpsLocationManager.executeGeoProfile(context, geoInfo)
                        }
                    } else {
                        Logger.geo("$TAG 修改时间验证失败，当前Profile的modifyDate晚于新推送profile，不执行")
                        Logger.geo("$TAG 当前修改时间: $currentModifyTime, 新修改时间: $modifyTime")
                    }
                }
                nowTime < beginTime -> {
                    // 未到生效时间，加入列表等待
                    Logger.geo("$TAG Profile未到生效时间，加入等待列表")
                    Logger.geo("$TAG 距离生效还有: ${(beginTime - nowTime) / 1000 / 60} 分钟")

                    if (!TextUtils.isEmpty(currentProfile)) {
                        val currentProfileJson = JSONObject(currentProfile)
                        val currentProfileId = currentProfileJson.getString("proId")
                        val newProfileId = geoInfo.getString("proId")
                        Logger.geo("$TAG 检查是否需要恢复默认配置: 当前ID=$currentProfileId, 新ID=$newProfileId")

                        if (currentProfileId == newProfileId) {
                            Logger.geo("$TAG 恢复默认Profile配置")
                            val defaultProfile = getDefaultGeoProfile()
                            if (defaultProfile != null) {
                                GpsLocationManager.executeGeoProfile(context, defaultProfile)
                            } else {
                                Logger.geo("$TAG 未找到默认Profile，禁用地理围栏")
                                GpsLocationManager.executeGeoProfile(context, null)
                            }
                        }
                    }
                    updateGeoProfileList(geoInfo)
                }
                else -> {
                    // 已过期，废弃
                    Logger.geo("$TAG Profile已过期，废弃")
                    Logger.geo("$TAG 过期时间: ${SimpleDateFormat(DATE_FORMAT, Locale.getDefault()).format(Date(endTime))}")
                    Logger.geo("$TAG 已过期: ${(nowTime - endTime) / 1000 / 60} 分钟")
                }
            }

            Logger.geo("$TAG --- 地理围栏Profile处理完成 ---")

        } catch (e: Exception) {
            Logger.geoE("$TAG 处理地理围栏Profile失败", e)
        }
    }

    /**
     * 解析时间字符串
     */
    private fun parseTimeStrings(beginDateStr: String, endDateStr: String, modifyDateStr: String): Triple<Long, Long, Long> {
        val beginTime = if (isNumeric(beginDateStr)) {
            beginDateStr.toLong()
        } else {
            SimpleDateFormat(DATE_FORMAT, Locale.getDefault()).parse(beginDateStr)?.time ?: 0L
        }

        val endTime = if (isNumeric(endDateStr)) {
            endDateStr.toLong()
        } else {
            SimpleDateFormat(DATE_FORMAT, Locale.getDefault()).parse(endDateStr)?.time ?: 0L
        }

        val modifyTime = if (!TextUtils.isEmpty(modifyDateStr)) {
            if (isNumeric(modifyDateStr)) {
                modifyDateStr.toLong()
            } else {
                SimpleDateFormat(DATE_FORMAT, Locale.getDefault()).parse(modifyDateStr)?.time ?: 0L
            }
        } else {
            0L
        }

        return Triple(beginTime, endTime, modifyTime)
    }

    /**
     * 检查字符串是否为数字
     */
    private fun isNumeric(str: String): Boolean {
        return try {
            str.toLong()
            true
        } catch (e: NumberFormatException) {
            false
        }
    }

    /**
     * 获取当前Profile
     */
    private fun getCurrentProfile(): String {
        val prefs = PreferenceManager.getDefaultSharedPreferences(context)
        return prefs.getString(PREF_GEO_CURRENT_PROFILE, "") ?: ""
    }

    /**
     * 保存当前Profile
     */
    private fun saveCurrentProfile(profile: JSONObject) {
        val prefs = PreferenceManager.getDefaultSharedPreferences(context)
        prefs.edit().putString(PREF_GEO_CURRENT_PROFILE, profile.toString()).apply()
        Logger.geo("$TAG 当前Profile已保存")
    }

    /**
     * 获取默认地理围栏Profile
     * 从本地Profile列表中查找标记为isDefault="1"的Profile
     */
    private fun getDefaultGeoProfile(): JSONObject? {
        return try {
            val prefs = PreferenceManager.getDefaultSharedPreferences(context)
            val localGeoProfileListStr = prefs.getString(PREF_GEO_PROFILE_LIST, "")
            Logger.geo("$TAG 当前本地Profile列表: $localGeoProfileListStr")

            if (!TextUtils.isEmpty(localGeoProfileListStr)) {
                val localProfileList = JSONArray(localGeoProfileListStr)
                Logger.geo("$TAG 本地不为空，遍历查找默认Profile，数量: ${localProfileList.length()}")

                for (i in 0 until localProfileList.length()) {
                    val profileJsonObj = localProfileList.getJSONObject(i)
                    if (profileJsonObj.has("isDefault") && "1" == profileJsonObj.getString("isDefault")) {
                        Logger.geo("$TAG 找到默认Profile: ${profileJsonObj.getString("proId")}")
                        return profileJsonObj
                    }
                }
                Logger.geo("$TAG 未找到标记为默认的Profile")
            } else {
                Logger.geo("$TAG 本地Profile列表为空")
            }

            null
        } catch (e: Exception) {
            Logger.geoE("$TAG 获取默认Profile失败", e)
            null
        }
    }

    /**
     * 更新地理围栏Profile列表
     */
    private fun updateGeoProfileList(geoInfo: JSONObject) {
        try {
            Logger.geo("$TAG 更新Profile列表")
            val prefs = PreferenceManager.getDefaultSharedPreferences(context)
            val profileListStr = prefs.getString(PREF_GEO_PROFILE_LIST, "[]") ?: "[]"
            val profileList = JSONArray(profileListStr)

            val proId = geoInfo.getString("proId")
            var found = false

            // 查找是否已存在相同ID的Profile
            for (i in 0 until profileList.length()) {
                val profile = profileList.getJSONObject(i)
                if (profile.getString("proId") == proId) {
                    // 更新现有Profile
                    profileList.put(i, geoInfo)
                    found = true
                    Logger.geo("$TAG 更新现有Profile: $proId")
                    break
                }
            }

            if (!found) {
                // 添加新Profile
                profileList.put(geoInfo)
                Logger.geo("$TAG 添加新Profile: $proId")
            }

            // 保存更新后的列表
            prefs.edit().putString(PREF_GEO_PROFILE_LIST, profileList.toString()).apply()
            Logger.geo("$TAG Profile列表已更新，当前数量: ${profileList.length()}")

        } catch (e: Exception) {
            Logger.geoE("$TAG 更新Profile列表失败", e)
        }
    }

    /**
     * 从Profile列表中删除指定Profile
     */
    private fun deleteGeoProfileFromList(geoInfo: JSONObject) {
        try {
            Logger.geo("$TAG 从Profile列表中删除Profile")
            val prefs = PreferenceManager.getDefaultSharedPreferences(context)
            val profileListStr = prefs.getString(PREF_GEO_PROFILE_LIST, "[]") ?: "[]"
            val profileList = JSONArray(profileListStr)
            val newProfileList = JSONArray()

            val proIdToDelete = geoInfo.getString("proId")
            Logger.geo("$TAG 要删除的Profile ID: $proIdToDelete")

            var deleted = false
            for (i in 0 until profileList.length()) {
                val profile = profileList.getJSONObject(i)
                val proId = profile.getString("proId")

                if (proId != proIdToDelete) {
                    newProfileList.put(profile)
                } else {
                    deleted = true
                    Logger.geo("$TAG 删除Profile: $proId")
                }
            }

            if (deleted) {
                // 保存更新后的列表
                prefs.edit().putString(PREF_GEO_PROFILE_LIST, newProfileList.toString()).apply()
                Logger.geo("$TAG Profile删除成功，剩余数量: ${newProfileList.length()}")

                // 检查是否需要恢复默认配置
                val currentProfile = getCurrentProfile()
                if (!TextUtils.isEmpty(currentProfile)) {
                    val currentProfileJson = JSONObject(currentProfile)
                    val currentProfileId = currentProfileJson.getString("proId")

                    if (currentProfileId == proIdToDelete) {
                        Logger.geo("$TAG 当前Profile被删除，恢复默认配置")
                        val defaultProfile = getDefaultGeoProfile()
                        if (defaultProfile != null) {
                            GpsLocationManager.executeGeoProfile(context, defaultProfile)
                        } else {
                            Logger.geo("$TAG 未找到默认Profile，禁用地理围栏")
                            GpsLocationManager.executeGeoProfile(context, null)
                        }
                    }
                }
            } else {
                Logger.geo("$TAG 未找到要删除的Profile: $proIdToDelete")
            }

        } catch (e: Exception) {
            Logger.geoE("$TAG 删除Profile失败", e)
        }
    }

    /**
     * 执行到期的地理围栏Profile
     */
    fun executeOnDueGeoProfile(context: Context) {
        try {
            Logger.geo("$TAG 检查并执行到期的地理围栏Profile")
            val prefs = PreferenceManager.getDefaultSharedPreferences(context)
            val profileListStr = prefs.getString(PREF_GEO_PROFILE_LIST, "[]") ?: "[]"
            val profileList = JSONArray(profileListStr)

            if (profileList.length() == 0) {
                Logger.geo("$TAG 没有待执行的Profile")
                return
            }

            val nowTime = System.currentTimeMillis()
            val newProfileList = JSONArray()
            var hasExecuted = false

            Logger.geo("$TAG 当前时间: $nowTime (${SimpleDateFormat(DATE_FORMAT, Locale.getDefault()).format(Date(nowTime))})")
            Logger.geo("$TAG 检查${profileList.length()}个Profile")

            for (i in 0 until profileList.length()) {
                val profile = profileList.getJSONObject(i)
                val proId = profile.getString("proId")
                val beginDateStr = profile.getString("beginDate")
                val endDateStr = profile.getString("endDate")

                val beginTime = if (isNumeric(beginDateStr)) {
                    beginDateStr.toLong()
                } else {
                    SimpleDateFormat(DATE_FORMAT, Locale.getDefault()).parse(beginDateStr)?.time ?: 0L
                }

                val endTime = if (isNumeric(endDateStr)) {
                    endDateStr.toLong()
                } else {
                    SimpleDateFormat(DATE_FORMAT, Locale.getDefault()).parse(endDateStr)?.time ?: 0L
                }

                Logger.geo("$TAG Profile $proId: 生效时间=${SimpleDateFormat(DATE_FORMAT, Locale.getDefault()).format(Date(beginTime))}, 失效时间=${SimpleDateFormat(DATE_FORMAT, Locale.getDefault()).format(Date(endTime))}")

                when {
                    beginTime <= nowTime && endTime > nowTime -> {
                        // 到期执行
                        Logger.geo("$TAG Profile $proId 到期执行")
                        val disableTag = "1" == profile.optString("disableTag")

                        if (disableTag) {
                            Logger.geo("$TAG Profile $proId 被禁用，执行默认配置")
                            val defaultProfile = getDefaultGeoProfile()
                            if (defaultProfile != null) {
                                GpsLocationManager.executeGeoProfile(context, defaultProfile)
                            } else {
                                Logger.geo("$TAG 未找到默认Profile，禁用地理围栏")
                                GpsLocationManager.executeGeoProfile(context, null)
                            }
                        } else {
                            Logger.geo("$TAG 执行Profile $proId")
                            GpsLocationManager.executeGeoProfile(context, profile)
                        }
                        hasExecuted = true
                        // 不再加入新列表，表示已执行
                    }
                    nowTime < beginTime -> {
                        // 还未到期，继续等待
                        Logger.geo("$TAG Profile $proId 还未到期，继续等待")
                        newProfileList.put(profile)
                    }
                    else -> {
                        // 已过期，废弃
                        Logger.geo("$TAG Profile $proId 已过期，废弃")
                    }
                }
            }

            // 更新Profile列表
            prefs.edit().putString(PREF_GEO_PROFILE_LIST, newProfileList.toString()).apply()
            Logger.geo("$TAG Profile列表已更新，剩余数量: ${newProfileList.length()}")

            if (hasExecuted) {
                Logger.geo("$TAG 有Profile被执行")
            } else {
                Logger.geo("$TAG 没有Profile需要执行")
            }

        } catch (e: Exception) {
            Logger.geoE("$TAG 执行到期Profile失败", e)
        }
    }
}
