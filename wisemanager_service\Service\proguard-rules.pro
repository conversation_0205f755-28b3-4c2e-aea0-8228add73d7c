# Add project specific ProGuard rules here.
# By default, the flags in this file are appended to flags specified
# in D:\android_tools\android studio\sdk/tools/proguard/proguard-android.txt
# You can edit the include path and order by changing the proguardFiles
# directive in build.gradle.
#
# For more details, see
#   http://developer.android.com/guide/developing/tools/proguard.html

# Add any project specific keep options here:

# If your project uses WebView with JS, uncomment the following
# and specify the fully qualified class name to the JavaScript interface
# class:
#-keepclassmembers class fqcn.of.javascript.interface.for.webview {
#   public *;
#}

-dontoptimize
-dontpreverify
-dontskipnonpubliclibraryclasses
-dontskipnonpubliclibraryclassmembers
-dontshrink
-dontusemixedcaseclassnames  #Aa aA
-dontwarn

-ignorewarnings

-optimizations !code/simplification/arithmetic,!field/*,!class/merging/* #->混淆采用的算法.
-optimizationpasses 7 #->设置混淆的压缩比率 0 ~ 7

-verbose #->混淆后生产映射文件 map 类名->转化后类名的映射

-keepattributes *Annotation*
-keepattributes Signature

-dontwarn android.backport.webp.**
-dontwarn android.content.pm.**
-dontwarn android.support.v4.**
-dontwarn org.apache.**
-dontwarn org.xutils.**

-dontwarn com.bbpos.wiseapp.service.**

-keepattributes SourceFile,LineNumberTable,InnerClasses

-keep class android.backport.webp.** {*;}
-keep class android.os.** { *;}  
-keep class android.support.** { *;}  
-keep class org.apache.** { *;}  
-keep class org.json.** { *;}
-keep class org.xutils.** { *;}
-keep class org.java_websocket.** { *;}
-keep class org.slf4j.** { *;}

-keep class * extends android.content.pm.**
-keep class * extends android.app.**

-keep public class * extends android.app.Activity  #->所有activity的子类不要去混淆
-keep public class * extends android.app.Application
-keep public class * extends android.app.Service
-keep public class * extends android.support.v4.**
-keep public class * extends android.content.BroadcastReceiver
-keep public class * extends android.content.ContentProvider

-keep class  com.android.internal.telephony.ITelephony { *; }

-keep public class com.bbpos.wiseapp.tms.network.**{*;}
-keep class  com.bbpos.wiseapp.service.utils$* { *;}
-keep class  com.bbpos.wiseapp.service.AppManagerService$* { *;}
-keep class  com.bbpos.wiseapp.service.CloudService$* { *;}
-keep class  com.bbpos.wiseapp.service.DeviceManagerSerice$* { *;}
-keep class  com.bbpos.wiseapp.service.SystemManagerService$* { *;}

-dontwarn com.bbpos.bb03z.**
-dontwarn com.bbpos.bbdevice.**
-dontwarn com.bbpos.bbdevice001.**
-keep class com.bbpos.bb03z..** {*;}
-keep class com.bbpos.bbdevice.** {*;}
-keep class com.bbpos.bbdevice001.** {*;}

-keep class android.bbpos.** {*;}

-keepclasseswithmembernames class * {
    native <methods>; #-> 所有native的方法不能去混淆.
}
-keepclasseswithmembernames class * {
    public <init>(android.content.Context, android.util.AttributeSet);
}
-keepclasseswithmembernames class * {
    public <init>(android.content.Context, android.util.AttributeSet, int);
    # -->某些构造方法不能去混淆
}
-keepclassmembers enum * { #  -> 枚举类不能去混淆.
    public static **[] values();
    public static ** valueOf(java.lang.String);
}
-keep class * implements android.os.Parcelable {  #-> aidl文件不能去混淆.
  public static final android.os.Parcelable$Creator *;
}

-keepnames public class com.bbpos.wiseapp.tms.listener.device.* {
    public <fields>;
    public <methods>;
}
-keepnames public class com.bbpos.wiseapp.tms.utils.* {
    public <fields>;
    public <methods>;
}
-keepnames public class com.bbpos.wiseapp.contentprovider.** {
    public <fields>;
    public <methods>;
}
-keepnames public class com.bbpos.wiseapp.tms.traffic.** {
   public <fields>;
   public <methods>;
}
