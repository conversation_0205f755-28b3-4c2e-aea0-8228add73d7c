package com.bbpos.wiseapp.tms.timer;

import android.app.AlarmManager;
import android.app.PendingIntent;
import android.content.BroadcastReceiver;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.net.ConnectivityManager;
import android.os.Build;

import com.bbpos.wiseapp.logger.BBLog;
import com.bbpos.wiseapp.service.common.UsualData;
import com.bbpos.wiseapp.system.api.SystemApi;
import com.bbpos.wiseapp.tms.location.GpsLocationManager;
import com.bbpos.wiseapp.tms.model.AppInfo;
import com.bbpos.wiseapp.utils.SharedPreferencesUtils;
import com.bbpos.wiseapp.tms.utils.BroadcastActions;
import com.bbpos.wiseapp.tms.utils.Constants;
import com.bbpos.wiseapp.tms.utils.Helpers;
import com.bbpos.wiseapp.websocket.WebSocketCenter;
import com.bbpos.wiseapp.websocket.WebSocketSender;

import java.util.List;

public class PollTimer extends BroadcastReceiver{
	private static final String TAG = "HardwareDetectTimer";
	@Override
	public void onReceive(Context context, Intent intent) {
		String action = intent.getAction();
		BBLog.v(BBLog.TAG, "PollTimer Receiver Broadcast " + action);
		
		if (BroadcastActions.BOOT_COMPLETED_BC.equals(action)){
			//开启日志服务
			if (!Constants.IS_FIRST_POLL_COMPLETED && Helpers.isOnline(context))
				startPollTimer(context);
		} else if (BroadcastActions.POLL_TIMER_START_BC.equals(action)){
				startPollTimer(context);
				SharedPreferencesUtils.setSharePreferencesValue(Constants.FLAG_LAST_POLL_SUCCESS,"false");
				BBLog.d(BBLog.TAG, "onReceive: 收到0101上报广播，开启上报任务");
		} else if (BroadcastActions.BLE_SCAN_TIMER_START_BC.equals(action)){
			if (Constants.BT_ENABLE) {
				GpsLocationManager.startBLEScanTimer(context);
			} else {
				GpsLocationManager.stopBLEScanTimer(context);
			}
		} else if (ConnectivityManager.CONNECTIVITY_ACTION.equals(action)){
			if (Helpers.isOnline(context)) {
				startPollTimer(context);
			}
		}
	}
	
	/**启动轮询定时广播*/
	private void startPollTimer(Context context){
		WebSocketSender.heartBeat();
		if (WebSocketCenter.isWebSocketConnected) {
			try {
				List<AppInfo> crashAppListInfo = SystemApi.getCrashLog(context);
				if ((crashAppListInfo != null) && (crashAppListInfo.size() > 0)) {
					WebSocketSender.C0202_DeviceEventUpload("1", "", crashAppListInfo);
				}
				int fallCount = Integer.parseInt(SharedPreferencesUtils.getSharePreferencesValue(UsualData.SHARED_PREFERENCES_FALL_COUNT, "0"));
				if (fallCount > 0) {
					for (int i = 0; i < fallCount; i ++) {
						WebSocketSender.C0202_DeviceEventUpload("4", "0", null);
					}
					SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_FALL_COUNT, "0");
				}
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
		BBLog.i(TAG, "startPollTimer in " + Constants.POLL_INTERVAL() * 1000);
		Intent intentTmp = new Intent(BroadcastActions.POLL_TIMER_START_BC);
		if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
			intentTmp.setComponent(new ComponentName(context.getPackageName(), PollTimer.class.getName()));
		}
		PendingIntent pi = PendingIntent.getBroadcast(context, 0, intentTmp, PendingIntent.FLAG_UPDATE_CURRENT);
		AlarmManager am = (AlarmManager) context.getSystemService(Context.ALARM_SERVICE);
		am.cancel(pi);
		long timeOnMillis = System.currentTimeMillis() + (Constants.POLL_INTERVAL() * 1000);

			if (Build.VERSION.SDK_INT < Build.VERSION_CODES.KITKAT) {
				am.set(AlarmManager.RTC_WAKEUP, timeOnMillis, pi);
			} else {
				am.setExact(AlarmManager.RTC_WAKEUP, timeOnMillis, pi);
			}
	}
}