package com.bbpos.wiseapp.service.appdata;

import java.util.ArrayList;
import java.util.List;
import com.bbpos.wiseapp.sdk.app.UsageStats;

import com.bbpos.wiseapp.logger.BBLog;
import com.bbpos.wiseapp.service.appdata.db.DataCollectDBHelper;
import com.bbpos.wiseapp.service.appdata.model.UsageData;
import android.content.Context;
import android.util.Log;

public class DataCollect {
	private  Context mContext;
	public DataCollect(Context context) {
		mContext = context;
	}
	
	public  List<UsageStats> getUsageStatsByDate(String yyyyMMdd) {
		List<UsageStats> usageStatsList = new  ArrayList<UsageStats>();
		DataCollectDBHelper dbHelper = DataCollectDBHelper.getInstance(mContext);
		List<UsageData> usageDataList = dbHelper.getUsageDataList(yyyyMMdd);
		
		for (UsageData usage : usageDataList) {
			usageStatsList.add(
					new UsageStats(
							usage.pkgName, 
							""+usage.launchCount,
							""+usage.totalTimeInForeground, 
							yyyyMMdd));
			BBLog.e("DataCollect", "-"+usage.pkgName+" -- "+usage.launchCount+" -- "+usage.totalTimeInForeground);
		}
		return usageStatsList;
	}

}
