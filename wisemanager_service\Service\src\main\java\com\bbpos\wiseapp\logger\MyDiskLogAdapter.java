package com.bbpos.wiseapp.logger;

import com.orhanobut.logger.FormatStrategy;
import com.orhanobut.logger.LogAdapter;

public class MyDiskLogAdapter implements LogAdapter {

  private final FormatStrategy formatStrategy;

  public MyDiskLogAdapter() {
    formatStrategy = MyCsvFormatStrategy.newBuilder().build();
  }

  public MyDiskLogAdapter(FormatStrategy formatStrategy) {
    this.formatStrategy = formatStrategy;
  }

  @Override
  public boolean isLoggable(int priority, String tag) {
    return true;
  }

  @Override
  public void log(int priority, String tag, String message) {
    formatStrategy.log(priority, tag, message);
  }

  public void sendFlushMsg(){
    try {
      ((MyCsvFormatStrategy)formatStrategy).flushMemoryLog();
    }catch (Exception e){
      e.printStackTrace();
    }
  }
}