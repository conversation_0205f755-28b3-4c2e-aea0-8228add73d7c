package com.dspread.mdm.service.utils.log

/**
 * 日志级别定义
 */
object LogLevel {
    const val VERBOSE = 0    // 最详细的日志级别，输出所有日志
    const val DEBUG = 1      // 调试日志级别，输出调试信息
    const val INFO = 2       // 信息日志级别，输出一般信息
    const val WARN = 3       // 警告日志级别，输出警告信息
    const val ERROR = 4      // 错误日志级别，输出错误信息
    const val NONE = 5       // 不输出任何日志
    
    /**
     * 获取日志级别名称
     */
    fun getLevelName(level: Int): String {
        return when (level) {
            VERBOSE -> "VERBOSE"
            DEBUG -> "DEBUG"
            INFO -> "INFO"
            WARN -> "WARN"
            ERROR -> "ERROR"
            NONE -> "NONE"
            else -> "UNKNOWN"
        }
    }
    
    /**
     * 获取日志级别简称
     */
    fun getLevelShortName(level: Int): String {
        return when (level) {
            VERBOSE -> "V"
            DEBUG -> "D"
            INFO -> "I"
            WARN -> "W"
            ERROR -> "E"
            NONE -> "N"
            else -> "U"
        }
    }
}
