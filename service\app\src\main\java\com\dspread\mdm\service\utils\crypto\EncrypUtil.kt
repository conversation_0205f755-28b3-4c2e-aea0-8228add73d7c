package com.dspread.mdm.service.utils.crypto

import java.io.ByteArrayOutputStream
import java.io.UnsupportedEncodingException
import java.security.KeyFactory
import java.security.spec.PKCS8EncodedKeySpec
import java.security.spec.X509EncodedKeySpec
import javax.crypto.Cipher

/**
 * RSA 加密解密工具类
 */
object EncrypUtil {
    
    private val base64EncodeChars = charArrayOf(
        'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T',
        'U', 'V', 'W', 'X', 'Y', 'Z', 'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j', 'k', 'l', 'm',
        'n', 'o', 'p', 'q', 'r', 's', 't', 'u', 'v', 'w', 'x', 'y', 'z', '0', '1', '2', '3', '4', '5',
        '6', '7', '8', '9', '+', '/'
    )
    
    private val base64DecodeChars = byteArrayOf(
        -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1,
        -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 62, -1, -1, -1, 63, 52, 53,
        54, 55, 56, 57, 58, 59, 60, 61, -1, -1, -1, -1, -1, -1, -1, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11,
        12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, -1, -1, -1, -1, -1, -1, 26, 27, 28, 29,
        30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, -1, -1,
        -1, -1, -1
    )

    const val KEY_ALGORITHM = "RSA"
    private const val MAX_DECRYPT_BLOCK = 128
    private const val MAX_ENCRYPT_BLOCK = 117

    /**
     * 使用私钥解密数据
     */
    @JvmStatic
    @Throws(Exception::class)
    fun decryptByPrivateKey(encryptedData: ByteArray, privateKey: String): ByteArray {
        // RSA 解密开始

        val keyBytes = decode(privateKey)
//        Logger.wsm("私钥解码后长度: ${keyBytes.size}")

        val pkcs8KeySpec = PKCS8EncodedKeySpec(keyBytes)
        val keyFactory = KeyFactory.getInstance(KEY_ALGORITHM)
        val privateK = keyFactory.generatePrivate(pkcs8KeySpec)

        val cipher = Cipher.getInstance("RSA/None/PKCS1Padding")
        cipher.init(Cipher.DECRYPT_MODE, privateK)

        val inputLen = encryptedData.size
        val out = ByteArrayOutputStream()
        var offSet = 0
        var i = 0

        // 对数据分段解密
        while (inputLen - offSet > 0) {
            val cache = if (inputLen - offSet > MAX_DECRYPT_BLOCK) {
                cipher.doFinal(encryptedData, offSet, MAX_DECRYPT_BLOCK)
            } else {
                cipher.doFinal(encryptedData, offSet, inputLen - offSet)
            }

            out.write(cache, 0, cache.size)
            i++
            offSet = i * MAX_DECRYPT_BLOCK
        }

        val decryptedData = out.toByteArray()
        out.close()

        return decryptedData
    }

    /**
     * 使用公钥加密数据
     */
    @JvmStatic
    @Throws(Exception::class)
    fun encryptByPublicKey(data: ByteArray, publicKey: String): ByteArray {
        val keyBytes = decode(publicKey)
        val x509KeySpec = X509EncodedKeySpec(keyBytes)
        val keyFactory = KeyFactory.getInstance(KEY_ALGORITHM)
        val publicK = keyFactory.generatePublic(x509KeySpec)

        // 对数据加密
        val cipher = Cipher.getInstance("RSA/None/PKCS1Padding")
        cipher.init(Cipher.ENCRYPT_MODE, publicK)
        val inputLen = data.size
        val out = ByteArrayOutputStream()
        var offSet = 0
        var i = 0

        // 对数据分段加密
        while (inputLen - offSet > 0) {
            val cache = if (inputLen - offSet > MAX_ENCRYPT_BLOCK) {
                cipher.doFinal(data, offSet, MAX_ENCRYPT_BLOCK)
            } else {
                cipher.doFinal(data, offSet, inputLen - offSet)
            }
            out.write(cache, 0, cache.size)
            i++
            offSet = i * MAX_ENCRYPT_BLOCK
        }

        val encryptedData = out.toByteArray()
        out.close()
        return encryptedData
    }

    /**
     * Base64 编码
     */
    @JvmStatic
    fun encode(data: ByteArray): String {
        val sb = StringBuilder()
        val len = data.size
        var i = 0

        while (i < len) {
            val b1 = data[i++].toInt() and 0xff
            if (i == len) {
                sb.append(base64EncodeChars[b1 ushr 2])
                sb.append(base64EncodeChars[(b1 and 0x3) shl 4])
                sb.append("==")
                break
            }

            val b2 = data[i++].toInt() and 0xff
            if (i == len) {
                sb.append(base64EncodeChars[b1 ushr 2])
                sb.append(base64EncodeChars[((b1 and 0x03) shl 4) or ((b2 and 0xf0) ushr 4)])
                sb.append(base64EncodeChars[(b2 and 0x0f) shl 2])
                sb.append("=")
                break
            }

            val b3 = data[i++].toInt() and 0xff
            sb.append(base64EncodeChars[b1 ushr 2])
            sb.append(base64EncodeChars[((b1 and 0x03) shl 4) or ((b2 and 0xf0) ushr 4)])
            sb.append(base64EncodeChars[((b2 and 0x0f) shl 2) or ((b3 and 0xc0) ushr 6)])
            sb.append(base64EncodeChars[b3 and 0x3f])
        }
        return sb.toString()
    }

    /**
     * Base64 解码
     */
    @JvmStatic
    fun decode(str: String): ByteArray {
        return try {
            decodePrivate(str)
        } catch (e: UnsupportedEncodingException) {
            e.printStackTrace()
            byteArrayOf()
        }
    }

    /**
     * Base64 解码私有方法
     */
    @Throws(UnsupportedEncodingException::class)
    private fun decodePrivate(str: String): ByteArray {
        val sb = StringBuilder()
        val data = str.toByteArray(charset("US-ASCII"))
        val len = data.size
        var i = 0

        while (i < len) {
            var b1: Int
            do {
                b1 = base64DecodeChars[data[i++].toInt()].toInt()
            } while (i < len && b1 == -1)
            if (b1 == -1) break

            var b2: Int
            do {
                b2 = base64DecodeChars[data[i++].toInt()].toInt()
            } while (i < len && b2 == -1)
            if (b2 == -1) break
            sb.append(((b1 shl 2) or ((b2 and 0x30) ushr 4)).toChar())

            var b3: Int
            do {
                b3 = data[i++].toInt()
                if (b3 == 61) return sb.toString().toByteArray(charset("iso8859-1"))
                b3 = base64DecodeChars[b3].toInt()
            } while (i < len && b3 == -1)
            if (b3 == -1) break
            sb.append((((b2 and 0x0f) shl 4) or ((b3 and 0x3c) ushr 2)).toChar())

            var b4: Int
            do {
                b4 = data[i++].toInt()
                if (b4 == 61) return sb.toString().toByteArray(charset("iso8859-1"))
                b4 = base64DecodeChars[b4].toInt()
            } while (i < len && b4 == -1)
            if (b4 == -1) break
            sb.append((((b3 and 0x03) shl 6) or b4).toChar())
        }
        return sb.toString().toByteArray(charset("iso8859-1"))
    }
}
