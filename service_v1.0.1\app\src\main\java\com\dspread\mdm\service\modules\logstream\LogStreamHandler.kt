package com.dspread.mdm.service.modules.logstream

import android.content.Context
import com.dspread.mdm.service.modules.BaseModuleHandler
import com.dspread.mdm.service.modules.logstream.model.LogSource
import com.dspread.mdm.service.modules.logstream.model.LogStreamConfig
import com.dspread.mdm.service.utils.log.Logger
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import org.json.JSONArray
import org.json.JSONObject

/**
 * 日志流WebSocket消息处理器
 * 处理SC009类型的日志流控制消息
 */
class LogStreamHandler(
    private val context: Context
) : BaseModuleHandler() {
    
    companion object {
        private const val TAG = "[LogStreamHandler]"
    }
    
    private val logStreamManager by lazy { LogStreamManager(context) }
    
    override fun getModuleName(): String = "LogStream"
    
    override suspend fun handleMessage(message: String): Result<Unit> {
        return try {
            // 检查模块是否启用
            checkModuleEnabled().getOrThrow()
            
            Logger.logStream("$TAG 开始处理日志流控制消息")
            
            val jsonObject = parseJsonMessage(message)
            
            // 发送C0000响应确认
            sendAcknowledgment(jsonObject)
            
            // 解析消息类型和数据
            val messageType = parseMessageType(jsonObject)
            
            when (messageType) {
                LogStreamMessageType.CONFIG -> {
                    handleConfigMessage(jsonObject)
                }
                LogStreamMessageType.CONTROL -> {
                    handleControlMessage(jsonObject)
                }
                LogStreamMessageType.QUERY -> {
                    handleQueryMessage(jsonObject)
                }
                LogStreamMessageType.UPLOAD -> {
                    handleUploadMessage(jsonObject)
                }
                else -> {
                    Logger.logStreamW("$TAG 未知的日志流消息类型: $messageType")
                }
            }
            
            Result.success(Unit)
            
        } catch (e: Exception) {
            Logger.logStreamE("$TAG 日志流消息处理失败", e)
            Result.failure(e)
        }
    }
    
    /**
     * 解析消息类型
     */
    private fun parseMessageType(jsonObject: JSONObject): LogStreamMessageType {
        val data = jsonObject.optJSONObject("data")
        
        return when {
            data?.has("logConfig") == true -> LogStreamMessageType.CONFIG
            data?.has("action") == true -> LogStreamMessageType.CONTROL
            data?.has("query") == true -> LogStreamMessageType.QUERY
            data?.has("uploadRequest") == true -> LogStreamMessageType.UPLOAD
            else -> LogStreamMessageType.UNKNOWN
        }
    }
    
    /**
     * 处理配置消息
     */
    private suspend fun handleConfigMessage(jsonObject: JSONObject): Result<Unit> {
        return try {
            Logger.logStream("$TAG 处理日志流配置消息")
            
            val data = jsonObject.getJSONObject("data")
            val logConfig = data.getJSONObject("logConfig")
            
            // 解析日志流配置
            val config = parseLogStreamConfig(logConfig)
            
            if (config == null) {
                Logger.logStreamW("$TAG 日志流配置解析失败或无效")
                return Result.success(Unit)
            }
            
            Logger.logStream("$TAG 解析到日志流配置: 启用=${config.enabled}, 上传间隔=${config.uploadInterval}ms")
            
            // 异步更新配置
            GlobalScope.launch {
                val result = logStreamManager.updateConfig(config)
                if (result.isFailure) {
                    Logger.logStreamE("$TAG 日志流配置更新失败", result.exceptionOrNull())
                } else {
                    Logger.logStream("$TAG 日志流配置更新完成")
                }
            }
            
            Result.success(Unit)
            
        } catch (e: Exception) {
            Logger.logStreamE("$TAG 处理配置消息失败", e)
            Result.failure(e)
        }
    }
    
    /**
     * 处理控制消息
     */
    private suspend fun handleControlMessage(jsonObject: JSONObject): Result<Unit> {
        return try {
            Logger.logStream("$TAG 处理日志流控制消息")
            
            val data = jsonObject.getJSONObject("data")
            val action = data.getString("action")
            val parameters = data.optJSONObject("parameters") ?: JSONObject()
            
            Logger.logStream("$TAG 日志流控制动作: $action")
            
            when (action.lowercase()) {
                "start" -> {
                    logStreamManager.startStreaming()
                }
                
                "stop" -> {
                    logStreamManager.stopStreaming()
                }
                
                "pause" -> {
                    logStreamManager.pauseStreaming()
                }
                
                "resume" -> {
                    logStreamManager.resumeStreaming()
                }
                
                "collect" -> {
                    // 立即收集日志
                    logStreamManager.collectLogs()
                }
                
                "upload" -> {
                    // 立即上传
                    val filePaths = emptyList<String>()
                    val uploadUrl = parameters.optString("uploadUrl", "")
                    logStreamManager.uploadLogs(filePaths, uploadUrl)
                }
                
                "clear" -> {
                    // 清理日志
                    logStreamManager.clearLogs()
                }
                
                "reset" -> {
                    // 重置状态
                    logStreamManager.resetStatus()
                }
                
                else -> {
                    Logger.logStreamW("$TAG 未知的控制动作: $action")
                    return Result.failure(IllegalArgumentException("Unknown action: $action"))
                }
            }
            
            Logger.logStream("$TAG 日志流控制动作执行完成: $action")
            Result.success(Unit)
            
        } catch (e: Exception) {
            Logger.logStreamE("$TAG 处理控制消息失败", e)
            Result.failure(e)
        }
    }
    
    /**
     * 处理查询消息
     */
    private suspend fun handleQueryMessage(jsonObject: JSONObject): Result<Unit> {
        return try {
            Logger.logStream("$TAG 处理日志流查询消息")
            
            val data = jsonObject.getJSONObject("data")
            val query = data.getString("query")
            
            val response = when (query.lowercase()) {
                "status" -> {
                    logStreamManager.getStatus()
                }
                
                "config" -> {
                    logStreamManager.getCurrentConfig()?.toJson() ?: JSONObject()
                }
                
                "statistics" -> {
                    getStatisticsResponse()
                }
                
                "tasks" -> {
                    getUploadTasksResponse()
                }
                
                "logs" -> {
                    val count = data.optInt("count", 100)
                    val source = data.optString("source", "")
                    getRecentLogsResponse(count, source)
                }
                
                else -> {
                    Logger.logStreamW("$TAG 未知的查询类型: $query")
                    JSONObject().apply { put("error", "Unknown query type") }
                }
            }
            
            // 发送查询响应
            val jsonResponse = when (response) {
                is JSONObject -> response
                else -> JSONObject().apply { put("data", response.toString()) }
            }
            sendQueryResponse(jsonObject, jsonResponse)
            
            Logger.logStream("$TAG 日志流查询处理完成: $query")
            Result.success(Unit)
            
        } catch (e: Exception) {
            Logger.logStreamE("$TAG 处理查询消息失败", e)
            Result.failure(e)
        }
    }
    
    /**
     * 处理上传消息
     */
    private suspend fun handleUploadMessage(jsonObject: JSONObject): Result<Unit> {
        return try {
            Logger.logStream("$TAG 处理日志流上传消息")
            
            val data = jsonObject.getJSONObject("data")
            val uploadRequest = data.getJSONObject("uploadRequest")
            
            val taskId = uploadRequest.optString("taskId", "")
            val action = uploadRequest.optString("action", "start")
            
            when (action.lowercase()) {
                "start" -> {
                    val filePath = uploadRequest.getString("filePath")
                    val uploadUrl = uploadRequest.getString("uploadUrl")
                    val headers = parseHeaders(uploadRequest.optJSONObject("headers"))
                    
                    logStreamManager.startUploadTask(filePath, uploadUrl, headers)
                }
                
                "pause" -> {
                    if (taskId.isNotEmpty()) {
                        logStreamManager.pauseUploadTask(taskId)
                    }
                }
                
                "resume" -> {
                    if (taskId.isNotEmpty()) {
                        logStreamManager.resumeUploadTask(taskId)
                    }
                }
                
                "cancel" -> {
                    if (taskId.isNotEmpty()) {
                        logStreamManager.cancelUploadTask(taskId)
                    }
                }
                
                else -> {
                    Logger.logStreamW("$TAG 未知的上传动作: $action")
                }
            }
            
            Logger.logStream("$TAG 日志流上传消息处理完成: $action")
            Result.success(Unit)
            
        } catch (e: Exception) {
            Logger.logStreamE("$TAG 处理上传消息失败", e)
            Result.failure(e)
        }
    }
    
    /**
     * 解析日志流配置
     */
    private fun parseLogStreamConfig(configJson: JSONObject): LogStreamConfig? {
        return try {
            LogStreamConfig.fromJson(configJson)
        } catch (e: Exception) {
            Logger.logStreamE("$TAG 解析日志流配置失败", e)
            null
        }
    }
    
    /**
     * 解析日志源列表
     */
    private fun parseLogSources(sourcesArray: JSONArray?): List<LogSource> {
        val sources = mutableListOf<LogSource>()
        
        if (sourcesArray != null) {
            for (i in 0 until sourcesArray.length()) {
                try {
                    val sourceObj = sourcesArray.getJSONObject(i)
                    LogSource.fromJson(sourceObj)?.let { sources.add(it) }
                } catch (e: Exception) {
                    Logger.logStreamE("$TAG 解析日志源失败", e)
                }
            }
        }
        
        return sources
    }
    
    /**
     * 解析请求头
     */
    private fun parseHeaders(headersObj: JSONObject?): Map<String, String> {
        val headers = mutableMapOf<String, String>()
        
        if (headersObj != null) {
            headersObj.keys().forEach { key ->
                try {
                    headers[key] = headersObj.getString(key)
                } catch (e: Exception) {
                    Logger.logStreamE("$TAG 解析请求头失败: $key", e)
                }
            }
        }
        
        return headers
    }
    
    /**
     * 获取统计信息响应
     */
    private suspend fun getStatisticsResponse(): JSONObject {
        return try {
            val stats = logStreamManager.getStatistics()
            
            JSONObject().apply {
                put("totalLogEntries", stats.totalLogEntries)
                put("totalUploadTasks", stats.totalUploadTasks)
                put("completedUploads", stats.completedUploads)
                put("failedUploads", stats.failedUploads)
                put("totalUploadedBytes", stats.totalUploadedBytes)
                put("successRate", stats.getSuccessRate())
                put("averageUploadSpeed", stats.averageUploadSpeed)
                put("lastUploadTime", stats.lastUploadTime)
                put("lastCollectionTime", stats.lastCollectionTime)
                put("activeUploadTasks", stats.activeUploadTasks)
                put("errorCount", stats.errorCount)
            }
        } catch (e: Exception) {
            Logger.logStreamE("$TAG 获取统计信息失败", e)
            JSONObject().apply { put("error", e.message) }
        }
    }
    
    /**
     * 获取上传任务响应
     */
    private suspend fun getUploadTasksResponse(): JSONObject {
        return try {
            val tasks = logStreamManager.getUploadTasks()
            val tasksArray = JSONArray()
            
            tasks.forEach { task ->
                tasksArray.put(JSONObject().apply {
                    put("id", task.id)
                    put("filePath", task.filePath)
                    put("status", task.status.name)
                    put("progress", task.getProgress())
                    put("fileSize", task.fileSize)
                    put("uploadedSize", task.uploadedSize)
                    put("createdTime", task.createdTime)
                    put("retryCount", task.retryCount)
                    put("errorMessage", task.errorMessage)
                })
            }
            
            JSONObject().apply {
                put("tasks", tasksArray)
                put("count", tasks.size)
            }
        } catch (e: Exception) {
            Logger.logStreamE("$TAG 获取上传任务失败", e)
            JSONObject().apply { put("error", e.message) }
        }
    }
    
    /**
     * 获取最近日志响应
     */
    private suspend fun getRecentLogsResponse(count: Int, source: String): JSONObject {
        return try {
            val logs = logStreamManager.getRecentLogs(count, source)
            val logsArray = JSONArray()
            
            logs.forEach { log ->
                logsArray.put(log.toJson())
            }
            
            JSONObject().apply {
                put("logs", logsArray)
                put("count", logs.size)
                put("source", source)
            }
        } catch (e: Exception) {
            Logger.logStreamE("$TAG 获取最近日志失败", e)
            JSONObject().apply { put("error", e.message) }
        }
    }
    
    /**
     * 发送查询响应
     */
    private fun sendQueryResponse(originalMessage: JSONObject, response: JSONObject) {
        try {
            val responseMessage = JSONObject().apply {
                put("tranCode", "S0000")
                put("request_id", originalMessage.optString("request_id", ""))
                put("response_time", System.currentTimeMillis().toString())
                put("data", response)
            }
            
            // 这里应该通过WebSocket发送响应
            // webSocketCenter.sendMessage(responseMessage.toString())
            
            Logger.logStream("$TAG 查询响应已发送")
        } catch (e: Exception) {
            Logger.logStreamE("$TAG 发送查询响应失败", e)
        }
    }
}

/**
 * 日志流消息类型枚举
 */
enum class LogStreamMessageType {
    CONFIG,     // 配置消息
    CONTROL,    // 控制消息
    QUERY,      // 查询消息
    UPLOAD,     // 上传消息
    UNKNOWN     // 未知消息
}
