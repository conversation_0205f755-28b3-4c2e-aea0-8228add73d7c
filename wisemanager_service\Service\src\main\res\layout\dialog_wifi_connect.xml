<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="24dp"
    android:background="@drawable/dialog_shape">

    <LinearLayout
        android:layout_height="0dp"
        android:layout_width="match_parent"
        android:layout_weight="1"
        android:gravity="center_vertical"
        android:orientation="vertical">
        <TextView
            android:id="@+id/tv_header"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:text="Header"
            android:textSize="20sp"
            android:textColor="@color/title"/>

        <TextView
            android:id="@+id/tv_wifissid"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:text="@string/wifi_ssid"
            android:textSize="14sp"
            android:textColor="@color/subtitle"/>

        <EditText
            android:id="@+id/et_wifissid"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="5dp"
            android:maxLines="1"
            android:requiresFadingEdge="horizontal"
            android:ellipsize="none"
            android:textDirection="locale"
            android:maxLength="64"
            android:textSize="16sp"
            android:textCursorDrawable="@drawable/cursor_shape"
            android:background="@drawable/edittext_bord"/>
    </LinearLayout>
    <LinearLayout
        android:layout_height="wrap_content"
        android:layout_width="match_parent"
        android:layout_marginTop="12dp"
        android:orientation="vertical">
        <TextView
            android:id="@+id/tv_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:text="@string/pwd"
            android:textSize="14sp"
            android:textColor="@color/subtitle"/>

        <EditText
            android:id="@+id/et_password"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="5dp"
            android:maxLines="1"
            android:requiresFadingEdge="horizontal"
            android:ellipsize="none"
            android:password="true"
            android:textDirection="locale"
            android:maxLength="64"
            android:textSize="16sp"
            android:textCursorDrawable="@drawable/cursor_shape"
            android:background="@drawable/edittext_bord"/>
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="15dp">
            <CheckBox
                android:id="@+id/cb_showpwd"
                android:layout_width="24dp"
                android:layout_height="24dp"
                style="@style/checkbox_style"/>
            <TextView
                android:id="@+id/tv_showpwd"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:padding="5dp"
                android:gravity="center"
                android:text="@string/show_pwd"
                android:textSize="14sp"
                android:textColor="@color/subtitle"/>
        </LinearLayout>
        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:visibility="gone">
            <TextView
                android:id="@+id/tv_advanced"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentLeft="true"
                android:gravity="center"
                android:text="@string/advanced_option"
                android:textSize="14sp"
                android:textColor="@color/subtitle"/>
            <CheckBox
                android:id="@+id/cb_showopt"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                style="@style/arrow_style"/>
        </RelativeLayout>
    </LinearLayout>
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="24dp"
        android:gravity="center_vertical|right"
        android:orientation="horizontal">
        <TextView
            android:id="@+id/tv_cancel"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:text="@string/cancel"
            android:textColor="@color/theme_green"
            android:textSize="14sp"/>
        <TextView
            android:id="@+id/tv_connect"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="20dp"
            android:layout_marginRight="10dp"
            android:gravity="center"
            android:text="@string/connect"
            android:textColor="@color/theme_green"
            android:textSize="14sp"/>
    </LinearLayout>

</LinearLayout>