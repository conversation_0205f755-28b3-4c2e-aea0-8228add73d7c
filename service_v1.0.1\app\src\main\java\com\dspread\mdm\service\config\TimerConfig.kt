package com.dspread.mdm.service.config

import android.content.Context
import com.dspread.mdm.service.modules.provisioning.ProvisioningManager
import com.dspread.mdm.service.utils.log.Logger
import com.dspread.mdm.service.constants.Constants

/**
 * 定时器配置管理
 * 支持生产环境和调试环境的不同定时器间隔配置
 */
object TimerConfig {
    
    private const val TAG = "TimerConfig"
    
    // ==================== 生产环境配置 ====================
    
    /**
     * 生产环境 - 心跳定时器间隔（默认5分钟）
     */
    private const val PROD_HEARTBEAT_INTERVAL = 5 * 60L // 5分钟
    
    /**
     * 生产环境 - 终端信息上传间隔（默认15分钟）
     */
    private const val PROD_TERMINAL_INFO_INTERVAL = 15 * 60L // 15分钟
    
    /**
     * 生产环境 - 任务执行间隔（1分钟）
     */
    private const val PROD_TASK_EXECUTION_INTERVAL = 60L // 1分钟
    
    /**
     * 生产环境 - 服务守护间隔（2分钟）
     */
    private const val PROD_SERVICE_GUARD_INTERVAL = 2 * 60L // 2分钟
    
    /**
     * 生产环境 - Provisioning间隔（12小时）
     */
    private const val PROD_PROVISIONING_INTERVAL = 12 * 60 * 60L // 12小时
    
    // ==================== 调试环境配置 ====================
    
    /**
     * 调试环境 - 心跳定时器间隔（1分钟）
     */
    private const val DEBUG_HEARTBEAT_INTERVAL = 60L // 1分钟
    
    /**
     * 调试环境 - 终端信息上传间隔（2分钟）
     */
    private const val DEBUG_TERMINAL_INFO_INTERVAL = 2 * 60L // 2分钟
    
    /**
     * 调试环境 - 任务执行间隔（1分钟）
     */
    private const val DEBUG_TASK_EXECUTION_INTERVAL = 60L // 1分钟
    
    /**
     * 调试环境 - 服务守护间隔（2分钟）
     */
    private const val DEBUG_SERVICE_GUARD_INTERVAL = 2 * 60L // 2分钟
    
    /**
     * 调试环境 - Provisioning间隔（5分钟）
     */
    private const val DEBUG_PROVISIONING_INTERVAL = 5 * 60L // 5分钟
    
    // ==================== 公共方法 ====================
    
    /**
     * 获取心跳定时器间隔（秒）
     */
    fun getHeartbeatInterval(context: Context): Long {
        return if (DebugConfig.isTimerDebugMode()) {
            // 调试模式不再每次都打印日志
            DEBUG_HEARTBEAT_INTERVAL
        } else {
            // 生产环境优先从Provisioning配置读取
            try {
                val provisioningManager = ProvisioningManager.getInstance(context)
                val config = provisioningManager.getCurrentConfig()
                
                if (config != null) {
                    val heartbeatTime = config.polling.heartbeatTime.toLongOrNull()
                    if (heartbeatTime != null && heartbeatTime > 0) {
                        Logger.com("$TAG 使用Provisioning配置的心跳间隔: ${heartbeatTime}秒")
                        return heartbeatTime
                    }
                }
            } catch (e: Exception) {
                Logger.comE("$TAG 读取Provisioning心跳配置失败", e)
            }
            
            Logger.com("$TAG 使用生产环境默认心跳间隔: ${PROD_HEARTBEAT_INTERVAL}秒")
            PROD_HEARTBEAT_INTERVAL
        }
    }
    
    /**
     * 获取终端信息上传间隔（秒）
     */
    fun getTerminalInfoInterval(context: Context): Long {
        return if (DebugConfig.isTimerDebugMode()) {
            // 调试模式不再每次都打印日志
            DEBUG_TERMINAL_INFO_INTERVAL
        } else {
            // 生产环境优先从Provisioning配置读取
            try {
                val provisioningManager = ProvisioningManager.getInstance(context)
                val config = provisioningManager.getCurrentConfig()
                
                if (config != null) {
                    val terminalInfoTime = config.polling.terminalInfoTime.toLongOrNull()
                    if (terminalInfoTime != null && terminalInfoTime > 0) {
                        Logger.com("$TAG 使用Provisioning配置的终端信息上传间隔: ${terminalInfoTime}秒")
                        return terminalInfoTime
                    }
                }
            } catch (e: Exception) {
                Logger.comE("$TAG 读取Provisioning终端信息配置失败", e)
            }
            
            Logger.com("$TAG 使用生产环境默认终端信息上传间隔: ${PROD_TERMINAL_INFO_INTERVAL}秒")
            PROD_TERMINAL_INFO_INTERVAL
        }
    }
    
    /**
     * 获取任务执行间隔（秒）
     */
    fun getTaskExecutionInterval(): Long {
        return if (DebugConfig.isTimerDebugMode()) {
            // 调试模式不再每次都打印日志
            DEBUG_TASK_EXECUTION_INTERVAL
        } else {
            // 生产环境也不再每次都打印日志
            PROD_TASK_EXECUTION_INTERVAL
        }
    }
    
    /**
     * 获取服务守护间隔（秒）
     */
    fun getServiceGuardInterval(): Long {
        return if (DebugConfig.isTimerDebugMode()) {
            // 调试模式不再每次都打印日志
            DEBUG_SERVICE_GUARD_INTERVAL
        } else {
            // 生产环境也不再每次都打印日志
            PROD_SERVICE_GUARD_INTERVAL
        }
    }
    
    /**
     * 获取Provisioning间隔（秒）
     */
    fun getProvisioningInterval(): Long {
        return if (DebugConfig.isTimerDebugMode()) {
            // 调试模式不再每次都打印日志
            DEBUG_PROVISIONING_INTERVAL
        } else {
            val prodInterval = Constants.ModuleConstants.PROVISIONING_DEFAULT_INTERVAL
            // 生产环境也不再每次都打印日志
            prodInterval
        }
    }
    
    /**
     * 打印当前所有定时器配置
     */
    fun printCurrentConfig(context: Context) {
        val mode = if (DebugConfig.isTimerDebugMode()) "调试模式" else "生产模式"
        Logger.com("$TAG ========== 当前定时器配置 ($mode) ==========")
        Logger.com("$TAG ①心跳定时器: ${getHeartbeatInterval(context)}秒")
        Logger.com("$TAG ②终端信息上传: ${getTerminalInfoInterval(context)}秒")
        Logger.com("$TAG ③任务执行: ${getTaskExecutionInterval()}秒")
        Logger.com("$TAG ④服务守护: ${getServiceGuardInterval()}秒")
        Logger.com("$TAG ⑤Provisioning: ${getProvisioningInterval()}秒")
        Logger.com("$TAG ===============================================")
    }
}
