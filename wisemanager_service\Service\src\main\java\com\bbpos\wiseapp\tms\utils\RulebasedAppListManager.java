package com.bbpos.wiseapp.tms.utils;

import android.content.SharedPreferences;
import android.content.pm.PackageInfo;
import android.preference.PreferenceManager;
import android.util.Log;

import com.bbpos.wiseapp.logger.BBLog;
import com.bbpos.wiseapp.utils.ActivityUtils;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

/**
 * 本地任务列表管理
 * 固化任务数据至shareprefrence中
 * */
public class RulebasedAppListManager {
	public static final String INSTALLED = "INSTALLED";
	public static final String EXECUTING = "EXECUTING";
	public static final String UNINSTALLED = "UNINSTALLED";
	public static final String DOWNLOAD_FAILED = "DOWNLOAD_FAILED";

	public static final SharedPreferences sp = PreferenceManager.getDefaultSharedPreferences(ContextUtil.getInstance());

	/**与本地任务列表进行对比、更新*/
	public static void updateAppList(JSONArray appList){
		try {
			if (appList == null) {
				BBLog.e(BBLog.TAG, "taskList is empty.updateTaskList failed");
				return;
			}

			//更新至sp
			sp.edit().putString(SPKeys.RULEBASED_APP_LIST, appList.toString()).commit();
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	
	/**获取已到执行时间的待执行任务*/
	public static JSONObject getNextRulebased() {
		try {
			String localAppListStr = sp.getString(SPKeys.RULEBASED_APP_LIST, "");
			BBLog.e(BBLog.TAG, "获取下一个Rulebased任务 RULEBASED_APP_LIST localAppListStr = " + localAppListStr);
			if (!Helpers.isStrNoEmpty(localAppListStr)) {
				return null;
			} else {
				long nowTime = System.currentTimeMillis();
				JSONArray localAppList = new JSONArray(localAppListStr);
				for (int i = 0; i < localAppList.length(); i++) {
					JSONObject localAppJsonObj = (JSONObject) localAppList.get(i);
					if (!localAppJsonObj.has(ParameterName.taskResult)
							|| (localAppJsonObj.has(ParameterName.taskResult)&&RulebasedAppListManager.DOWNLOAD_FAILED.equals(localAppJsonObj.getString(ParameterName.taskResult))))
					{
						if (isApkInstalled(localAppJsonObj) == 0) {
							//状态为空或todo
							return localAppJsonObj;
						}
					}
				}
			}
		} catch (JSONException e) {
			e.printStackTrace();
		} catch (Exception e) {
			e.printStackTrace();
		}
		return null;
	}
	
	public static void updateAppState(String packName, String taskState) {
		try {
			String localAppListStr = sp.getString(SPKeys.RULEBASED_APP_LIST, "");
			if (!Helpers.isStrNoEmpty(localAppListStr))
				return;
			JSONArray localAppList = new JSONArray(localAppListStr);
			for (int i = 0; i < localAppList.length(); i++) {
				JSONObject localAppJsonObj = (JSONObject) localAppList.get(i);
				String packNameTmp = localAppJsonObj.getString(ParameterName.packName);
				if (packName.equals(packNameTmp)) {
					localAppJsonObj.put(ParameterName.taskResult, taskState);
					break;
				}
			}
			sp.edit().putString(SPKeys.RULEBASED_APP_LIST, localAppList.toString()).commit();
		} catch (JSONException e) {
			e.printStackTrace();
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	
	public static int isApkInstalled(JSONObject appJsonObj) {

		String packName = null;
		String versionCode = null;
		String versionName = null;
		try {
			packName = appJsonObj.getString(ParameterName.packName);
			versionCode = appJsonObj.getString(ParameterName.versionCode);
			versionName = appJsonObj.getString(ParameterName.versionName);
			BBLog.e(BBLog.TAG, "判断" + "packName=" + packName + " versionCode=" + versionCode + " versionName=" + versionName + "是否安装");
			PackageInfo info = ActivityUtils.getPackageInfo(ContextUtil.getInstance().getApplicationContext(), packName);
			if (info != null) {
				if (packName.equals(info.packageName)
						&& versionCode.equals(""+info.versionCode)
						&& versionName.equals(info.versionName)) {
					BBLog.e(BBLog.TAG, packName + "已安装并且版本一致");
					return 1;
				} else if (packName.equals(info.packageName)
						&& Integer.valueOf(versionCode) < Integer.valueOf(info.versionCode)) {
					BBLog.e(BBLog.TAG, packName + "已安装高版本");
					return 2;
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		BBLog.e(BBLog.TAG, packName + "未安装");
		return 0;
	}

	public static int isApkInstalled(String packName, String versionName, String versionCode) {
		BBLog.e(BBLog.TAG, "判断" + "packName=" + packName + " versionCode=" + versionCode + " versionName=" + versionName + "是否安装");
		PackageInfo info = ActivityUtils.getPackageInfo(ContextUtil.getInstance().getApplicationContext(), packName);
		if (info != null) {
			if (packName.equals(info.packageName)
					&& versionCode.equals(""+info.versionCode)
					&& versionName.equals(info.versionName)) {
				BBLog.e(BBLog.TAG, packName + "已安装并且版本一致");
				return 1;
			} else if (packName.equals(info.packageName)
					&& Integer.parseInt(versionCode) < info.versionCode) {
				BBLog.e(BBLog.TAG, packName + "已安装高版本");
				return 2;
			}
		}
		BBLog.e(BBLog.TAG, packName + "未安装");
		return 0;
	}

	public static boolean isRulebasedApp(String packageName) {
		BBLog.e(BBLog.TAG, "遍历判断" + packageName + "是否RulebasedApp?");
		String localAppListStr = sp.getString(SPKeys.RULEBASED_APP_LIST, "");
		if (!Helpers.isStrNoEmpty(localAppListStr))
			return false;
		try {
			JSONArray localAppList = new JSONArray(localAppListStr);
			for (int i = 0; i < localAppList.length(); i++) {
				JSONObject localAppJsonObj = (JSONObject) localAppList.get(i);
				BBLog.e(BBLog.TAG, "isRulebasedApp "+localAppJsonObj);
				String packNameTmp = localAppJsonObj.getString(ParameterName.packName);
				if (packageName.equals(packNameTmp)) {
					BBLog.e(BBLog.TAG, "是RulebasedApp");
					return true;
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		BBLog.e(BBLog.TAG, "不是RulebasedApp");
		return false;
	}
}
