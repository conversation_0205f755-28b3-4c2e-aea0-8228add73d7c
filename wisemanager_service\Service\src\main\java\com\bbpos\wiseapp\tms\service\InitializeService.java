package com.bbpos.wiseapp.tms.service;

import android.content.Context;
import android.content.Intent;
import android.os.IBinder;
import android.widget.Toast;

import com.bbpos.wiseapp.logger.BBLog;
import com.bbpos.wiseapp.service.R;
import com.bbpos.wiseapp.tms.adapter.SystemManagerAdapter;
import com.bbpos.wiseapp.service.WakeLockService;
import com.bbpos.wiseapp.tms.traffic.utils.TrafficHelpers;
import com.bbpos.wiseapp.tms.utils.Constants;

public class InitializeService extends WakeLockService {

    private final static String TAG = "InitializeService";
    private Context mContext = null;

    public InitializeService() {
        super("InitializeService");
    }

    @Override
    public void onHandleIntent(Intent intent) {
        super.onCreate();
        mContext = getApplicationContext();

        //开始定时数据收集
        TrafficHelpers.startDataCollectTimer(mContext, Constants.DATA_COLLECT_INVERTAL*1000);
		
//        Helpers.cleanAppRequestCache();

        if(!Constants.isActionServiceExist()) {
            Toast.makeText(this, getResources().getText(R.string.strServiceUninstall), Toast.LENGTH_LONG).show();
        } else {
            SystemManagerAdapter.bindServiceInit(getApplicationContext(), new SystemManagerAdapter.BindServiceSuccess() {
                @Override
                public void onBindSuccess() {
                    // TODO Auto-generated method stub
                    BBLog.i(BBLog.TAG, "onBindSuccess...");
                }
            });
        }
        BBLog.i(BBLog.TAG, "onCreate end...");
    }

    @Override
    public IBinder onBind(Intent intent) {
        return null;
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
    }
}
