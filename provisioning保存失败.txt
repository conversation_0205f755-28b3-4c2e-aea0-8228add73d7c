--------- beginning of crash
---------------------------- PROCESS STARTED (3149) for package com.dspread.mdm.service ----------------------------
2025-08-21 16:42:12.136  3149-3149  ead.mdm.service         com.dspread.mdm.service              W  type=1400 audit(0.0:284): avc: denied { write } for name="com.dspread.mdm.service-GCysYnsmYmAgz386-EGgUg==" dev="dm-6" ino=14551 scontext=u:r:system_app:s0 tcontext=u:object_r:apk_data_file:s0 tclass=dir permissive=0
2025-08-21 16:42:12.835  3149-3149  NetworkSecurityConfig   com.dspread.mdm.service              D  Using Network Security Config from resource network_security_config debugBuild: true
2025-08-21 16:42:12.839  3149-3149  NetworkSecurityConfig   com.dspread.mdm.service              D  Using Network Security Config from resource network_security_config debugBuild: true
2025-08-21 16:42:12.945  3149-3149  Common                  com.dspread.mdm.service              D  🔧 日志配置：测试环境模式
2025-08-21 16:42:12.951  3149-3149  Common                  com.dspread.mdm.service              D  🔧 [DebugConfig] 调试模式已启用
2025-08-21 16:42:12.953  3149-3149  Common                  com.dspread.mdm.service              D  🔧 SmartMdmServiceApp: 统一配置管理器初始化完成
2025-08-21 16:42:12.956  3149-3149  Common                  com.dspread.mdm.service              D  🔧 SmartMdmServiceApp: 系统目录初始化完成 - /data/pos/config/
2025-08-21 16:42:12.998  3149-3149  Receiver                com.dspread.mdm.service              D  🔧 ServiceGuardEventHandler 启动服务守护定时器: 120s
2025-08-21 16:42:13.005  3149-3149  Common                  com.dspread.mdm.service              I  ✅ ⏰ 设置服务守护定时器成功，下次执行: 120秒后 (2分钟)
2025-08-21 16:42:13.007  3149-3149  Common                  com.dspread.mdm.service              D  🔧 SmartMdmServiceApp: 服务守护定时器启动成功
2025-08-21 16:42:13.013  3149-3149  Common                  com.dspread.mdm.service              D  🔧 ServiceStartupManager 收到服务启动请求: APPLICATION_STARTUP, 强制启动: false
2025-08-21 16:42:13.019  3149-3149  Common                  com.dspread.mdm.service              D  🔧 ServiceStartupManager 开始执行服务启动: APPLICATION_STARTUP
2025-08-21 16:42:13.022  3149-3149  Common                  com.dspread.mdm.service              D  🔧 ServiceManager: 准备启动后台服务
2025-08-21 16:42:13.025  3149-3149  ContextImpl             com.dspread.mdm.service              W  Calling a method in the system process without a qualified user: android.app.ContextImpl.startService:1669 android.content.ContextWrapper.startService:720 com.dspread.mdm.service.platform.manager.ServiceManager.startBackgroundService:20 com.dspread.mdm.service.platform.manager.ServiceStartupManager.performServiceStart:121 com.dspread.mdm.service.platform.manager.ServiceStartupManager.startService:66 
2025-08-21 16:42:13.029  3149-3149  Common                  com.dspread.mdm.service              D  🔧 ServiceManager: 后台服务启动成功，ComponentName: ComponentInfo{com.dspread.mdm.service/com.dspread.mdm.service.services.SmartMdmBackgroundService}
2025-08-21 16:42:14.074  3149-3149  Common                  com.dspread.mdm.service              D  🔧 ServiceStartupManager 服务启动验证成功: APPLICATION_STARTUP
2025-08-21 16:42:14.076  3149-3149  Common                  com.dspread.mdm.service              D  🔧 ServiceStartupManager 服务启动成功: APPLICATION_STARTUP
2025-08-21 16:42:14.078  3149-3149  Common                  com.dspread.mdm.service              D  🔧 SmartMdmServiceApp: 主服务启动请求已提交
2025-08-21 16:42:14.081  3149-3149  Common                  com.dspread.mdm.service              D  🔧 SmartMdmServiceApp: Application启动完成，包名: com.dspread.mdm.service
2025-08-21 16:42:14.219  3149-3149  SurfaceFactory          com.dspread.mdm.service              I  [static] sSurfaceFactory = com.mediatek.view.impl.SurfaceFactoryImpl@624d998
2025-08-21 16:42:14.229  3149-3149  ViewRootIm...tActivity] com.dspread.mdm.service              D  hardware acceleration = true , fakeHwAccelerated = false, sRendererDisabled = false, forceHwAccelerated = false, sSystemRendererDisabled = false
2025-08-21 16:42:14.236  3149-3149  InputTransport          com.dspread.mdm.service              I  Create ARC handle: 0xa4ed9f10
2025-08-21 16:42:14.238  3149-3149  PhoneWindow             com.dspread.mdm.service              V  DecorView setVisiblity: visibility = 0, Parent = android.view.ViewRootImpl@59f744, this = DecorView@23d302d[TestActivity]
2025-08-21 16:42:14.244  3149-3149  PhoneWindow             com.dspread.mdm.service              V  DecorView setVisiblity: visibility = 4, Parent = android.view.ViewRootImpl@59f744, this = DecorView@23d302d[TestActivity]
2025-08-21 16:42:14.246  3149-3149  Choreographer           com.dspread.mdm.service              I  Skipped 80 frames!  The application may be doing too much work on its main thread.
2025-08-21 16:42:14.281  3149-3149  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 服务启动
2025-08-21 16:42:14.291  3149-3149  setContentIntent        com.dspread.mdm.service              D  packageName = com.dspread.mdm.service
2025-08-21 16:42:14.297  3149-3149  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 开始服务初始化
2025-08-21 16:42:14.298   947-975   NotificationService     system_server                        E  enqueueNotificationInternal pkg: com.dspread.mdm.service
2025-08-21 16:42:14.300  3149-3149  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] Android < 14: 启动Provisioning服务
2025-08-21 16:42:14.304  3149-3149  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 服务创建完成
2025-08-21 16:42:14.304  3149-3180  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 启动ProvisioningService
2025-08-21 16:42:14.307  3149-3149  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] onStartCommand调用
2025-08-21 16:42:14.314  3149-3180  Provisioning            com.dspread.mdm.service              I  ℹ️ ProvisioningManager初始化完成
2025-08-21 16:42:14.317  3149-3149  Platform                com.dspread.mdm.service              D  🔧 WakeLockManager 获取智能WakeLock成功，持续时间: 60000ms
2025-08-21 16:42:14.317  3149-3180  Provisioning            com.dspread.mdm.service              D  🔧 状态标志文件不存在，使用默认值
2025-08-21 16:42:14.322  3149-3149  Platform                com.dspread.mdm.service              D  🔧 WakeLockManager 设置WakeLock续期定时器: 60000ms
2025-08-21 16:42:14.333  3149-3180  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] ProvisioningService启动完成
2025-08-21 16:42:14.333  3149-3181  Provisioning            com.dspread.mdm.service              I  ℹ️ 开始执行Provisioning配置，触发类型: FIRST_BOOT
2025-08-21 16:42:14.337  3149-3181  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] Provisioning进度: 正在下载配置文件...
2025-08-21 16:42:14.337  3149-3149  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 初始化基础组件
2025-08-21 16:42:14.339  3149-3181  Provisioning            com.dspread.mdm.service              I  ℹ️ 开始请求远程配置...
2025-08-21 16:42:14.340  3149-3149  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 开始初始化...
2025-08-21 16:42:14.349  3149-3149  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 包更新广播接收器注册完成
2025-08-21 16:42:14.352  3149-3149  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 统一广播接收器注册完成
2025-08-21 16:42:14.354  3149-3149  Common                  com.dspread.mdm.service              I  ✅ BroadcastManager 初始化完成
2025-08-21 16:42:14.364  3149-3181  Provisioning            com.dspread.mdm.service              I  ℹ️ 请求配置: https://config.dspreadserv.net/status/config?SN=MDE2MTAwNDAyMDIzMDcwNjAyMjc=&timestamp=1755765734364
2025-08-21 16:42:14.375  3149-3181  System.out              com.dspread.mdm.service              I  [okhttp]:check permission begin!
2025-08-21 16:42:14.376  3149-3181  System                  com.dspread.mdm.service              W  ClassLoader referenced unknown path: system/framework/mediatek-cta.jar
2025-08-21 16:42:14.377  3149-3181  System.out              com.dspread.mdm.service              I  [okhttp] e:java.lang.ClassNotFoundException: com.mediatek.cta.CtaUtils
2025-08-21 16:42:14.414  3149-3149  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 服务初始化完成
2025-08-21 16:42:14.420  3149-3149  Platform                com.dspread.mdm.service              D  🔧 NetworkApi 单例实例已创建
2025-08-21 16:42:14.424  3149-3149  TetheringManager        com.dspread.mdm.service              I  registerTetheringEventCallback:com.dspread.mdm.service
2025-08-21 16:42:14.433  3149-3182  Platform                com.dspread.mdm.service              D  🔧 DeviceInfoApi 单例实例已创建
2025-08-21 16:42:14.435  3149-3149  Receiver                com.dspread.mdm.service              D  🔧 网络状态变化: 连接=true, 类型=WIFI
2025-08-21 16:42:14.444  3149-3149  Receiver                com.dspread.mdm.service              D  🔧 网络恢复，尝试重新连接WebSocket
2025-08-21 16:42:14.446  3149-3149  Receiver                com.dspread.mdm.service              D  🔧 ServiceManagementEventHandler 处理网络变化事件
2025-08-21 16:42:14.451  3149-3149  Common                  com.dspread.mdm.service              D  🔧 ServiceStartupManager 服务正在运行，无需启动: NETWORK_CHANGE
2025-08-21 16:42:14.454  3149-3149  Receiver                com.dspread.mdm.service              D  🔧 ServiceManagementEventHandler 网络变化后服务检查完成
2025-08-21 16:42:14.457  3149-3149  Provisioning            com.dspread.mdm.service              D  🔧 ProvisioningEventHandler 网络状态变化
2025-08-21 16:42:14.459  3149-3149  Provisioning            com.dspread.mdm.service              D  🔧 ProvisioningEventHandler 忽略初始启动时的网络连接事件
2025-08-21 16:42:14.463  3149-3149  Receiver                com.dspread.mdm.service              D  🔧 电池状态变化: 电量=82%, 温度=27°C, 充电=true
2025-08-21 16:42:14.463  3149-3182  Platform                com.dspread.mdm.service              W  ⚠️ SpVersionApi SP版本信息格式异常，未找到SP_VERSION标记
2025-08-21 16:42:14.476  3149-3182  Platform                com.dspread.mdm.service              D  🔧 SpVersionApi 从Settings获取SP版本: STD 582.1.238.8
2025-08-21 16:42:14.491  3149-3149  WebSocket               com.dspread.mdm.service              I  🔧 C0902 被动式上送: battery_level_change_5 (被动: 1)
2025-08-21 16:42:14.512  3149-3149  WebSocket               com.dspread.mdm.service              I  🔧 消息已缓存: C0902 (缓存数量: 1)
2025-08-21 16:42:14.514  3149-3149  WebSocket               com.dspread.mdm.service              I  🔧 C0902 电池状态上送成功 (1, trigger=battery_level_change_5)
2025-08-21 16:42:14.517  3149-3149  WebSocket               com.dspread.mdm.service              I  🔧 C0905 主动式上送: manual_trigger (主动: 1)
2025-08-21 16:42:14.525  3149-3149  WebSocket               com.dspread.mdm.service              I  🔧 首次上送，上送当日数据
2025-08-21 16:42:14.529  3149-3149  WebSocket               com.dspread.mdm.service              I  🔧 没有流量数据需要上送
2025-08-21 16:42:14.531  3149-3149  Common                  com.dspread.mdm.service              D  🔧 刷新时自动上送流量统计
2025-08-21 16:42:14.709  3149-3181  System.out              com.dspread.mdm.service              I  [socket]:check permission begin!
2025-08-21 16:42:14.710  3149-3181  System                  com.dspread.mdm.service              W  ClassLoader referenced unknown path: system/framework/mediatek-cta.jar
2025-08-21 16:42:14.711  3149-3181  System.out              com.dspread.mdm.service              I  [socket] e:java.lang.ClassNotFoundException: com.mediatek.cta.CtaUtils
2025-08-21 16:42:15.809  3149-3181  System.out              com.dspread.mdm.service              I  [OkHttp] sendRequest>>
2025-08-21 16:42:15.810  3149-3181  System.out              com.dspread.mdm.service              I  [OkHttp] sendRequest<<
2025-08-21 16:42:17.068  3149-3181  Provisioning            com.dspread.mdm.service              I  ℹ️ 远程配置获取成功
2025-08-21 16:42:17.071  3149-3181  Provisioning            com.dspread.mdm.service              I  ℹ️ 远程配置请求成功
2025-08-21 16:42:17.074  3149-3181  Provisioning            com.dspread.mdm.service              I  ℹ️ 远程配置解析成功 - CID: 1001
2025-08-21 16:42:17.085  3149-3181  Provisioning            com.dspread.mdm.service              E  ❌ 保存API响应失败 (Ask Gemini)
                                                                                                    java.io.FileNotFoundException: /sdcard/Android/data/com.dspread.mdm.service/files/config/provisioning_config.json: open failed: ENOENT (No such file or directory)
                                                                                                    	at libcore.io.IoBridge.open(IoBridge.java:492)
                                                                                                    	at java.io.FileOutputStream.<init>(FileOutputStream.java:236)
                                                                                                    	at java.io.FileOutputStream.<init>(FileOutputStream.java:186)
                                                                                                    	at kotlin.io.FilesKt__FileReadWriteKt.writeBytes(FileReadWrite.kt:108)
                                                                                                    	at kotlin.io.FilesKt__FileReadWriteKt.writeText(FileReadWrite.kt:134)
                                                                                                    	at kotlin.io.FilesKt__FileReadWriteKt.writeText$default(FileReadWrite.kt:134)
                                                                                                    	at com.dspread.mdm.service.modules.provisioning.ProvisioningManager.saveApiResponse(ProvisioningManager.kt:611)
                                                                                                    	at com.dspread.mdm.service.modules.provisioning.ProvisioningManager.access$saveApiResponse(ProvisioningManager.kt:22)
                                                                                                    	at com.dspread.mdm.service.modules.provisioning.ProvisioningManager$executeProvisioning$1.invokeSuspend(ProvisioningManager.kt:95)
                                                                                                    	at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
                                                                                                    	at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:108)
                                                                                                    	at kotlinx.coroutines.internal.LimitedDispatcher$Worker.run(LimitedDispatcher.kt:115)
                                                                                                    	at kotlinx.coroutines.scheduling.TaskImpl.run(Tasks.kt:103)
                                                                                                    	at kotlinx.coroutines.scheduling.CoroutineScheduler.runSafely(CoroutineScheduler.kt:584)
                                                                                                    	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.executeTask(CoroutineScheduler.kt:793)
                                                                                                    	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.runWorker(CoroutineScheduler.kt:697)
                                                                                                    	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.run(CoroutineScheduler.kt:684)
                                                                                                    Caused by: android.system.ErrnoException: open failed: ENOENT (No such file or directory)
                                                                                                    	at libcore.io.Linux.open(Native Method)
                                                                                                    	at libcore.io.ForwardingOs.open(ForwardingOs.java:166)
                                                                                                    	at libcore.io.BlockGuardOs.open(BlockGuardOs.java:254)
                                                                                                    	at libcore.io.ForwardingOs.open(ForwardingOs.java:166)
                                                                                                    	at android.app.ActivityThread$AndroidOs.open(ActivityThread.java:7607)
                                                                                                    	at libcore.io.IoBridge.open(IoBridge.java:478)
                                                                                                    	at java.io.FileOutputStream.<init>(FileOutputStream.java:236) 
                                                                                                    	at java.io.FileOutputStream.<init>(FileOutputStream.java:186) 
                                                                                                    	at kotlin.io.FilesKt__FileReadWriteKt.writeBytes(FileReadWrite.kt:108) 
                                                                                                    	at kotlin.io.FilesKt__FileReadWriteKt.writeText(FileReadWrite.kt:134) 
                                                                                                    	at kotlin.io.FilesKt__FileReadWriteKt.writeText$default(FileReadWrite.kt:134) 
                                                                                                    	at com.dspread.mdm.service.modules.provisioning.ProvisioningManager.saveApiResponse(ProvisioningManager.kt:611) 
                                                                                                    	at com.dspread.mdm.service.modules.provisioning.ProvisioningManager.access$saveApiResponse(ProvisioningManager.kt:22) 
                                                                                                    	at com.dspread.mdm.service.modules.provisioning.ProvisioningManager$executeProvisioning$1.invokeSuspend(ProvisioningManager.kt:95) 
                                                                                                    	at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33) 
                                                                                                    	at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:108) 
                                                                                                    	at kotlinx.coroutines.internal.LimitedDispatcher$Worker.run(LimitedDispatcher.kt:115) 
                                                                                                    	at kotlinx.coroutines.scheduling.TaskImpl.run(Tasks.kt:103) 
                                                                                                    	at kotlinx.coroutines.scheduling.CoroutineScheduler.runSafely(CoroutineScheduler.kt:584) 
                                                                                                    	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.executeTask(CoroutineScheduler.kt:793) 
                                                                                                    	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.runWorker(CoroutineScheduler.kt:697) 
                                                                                                    	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.run(CoroutineScheduler.kt:684) 
2025-08-21 16:42:17.089  3149-3181  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] Provisioning进度: 正在下载 Logo...
2025-08-21 16:42:17.094  3149-3181  Provisioning            com.dspread.mdm.service              I  ℹ️ 文件不存在，开始下载: Logo
2025-08-21 16:42:17.096  3149-3181  Provisioning            com.dspread.mdm.service              I  ℹ️ 下载: https://siot-bucket01.s3.ap-northeast-1.amazonaws.com/test/logo.bin -> /sdcard/Android/data/com.dspread.mdm.service/files/config/logo.bin
2025-08-21 16:42:17.106  3149-3181  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 尝试下载，第1次
2025-08-21 16:42:17.109  3149-3181  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 开始下载
2025-08-21 16:42:17.111  3149-3181  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader URL: https://siot-bucket01.s3.ap-northeast-1.amazonaws.com/test/logo.bin
2025-08-21 16:42:17.113  3149-3181  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 路径: /sdcard/Android/data/com.dspread.mdm.service/files/config/logo.bin
2025-08-21 16:42:17.115  3149-3181  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 本地文件不存在，创建文件
2025-08-21 16:42:17.118  3149-3181  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader allFileLength=0, 需要从服务器获取文件大小
2025-08-21 16:42:17.122  3149-3181  System.out              com.dspread.mdm.service              I  [okhttp]:check permission begin!
2025-08-21 16:42:17.122  3149-3181  System.out              com.dspread.mdm.service              I  [okhttp]:not MMS!
2025-08-21 16:42:17.122  3149-3181  System.out              com.dspread.mdm.service              I  [okhttp]:not Email!
2025-08-21 16:42:17.474  3149-3181  System.out              com.dspread.mdm.service              I  [socket]:check permission begin!
2025-08-21 16:42:17.953  3149-3181  System.out              com.dspread.mdm.service              I  [OkHttp] sendRequest>>
2025-08-21 16:42:17.954  3149-3181  System.out              com.dspread.mdm.service              I  [OkHttp] sendRequest<<
2025-08-21 16:42:18.600  3149-3181  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader HTTP响应码: 200
2025-08-21 16:42:18.604  3149-3181  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 服务器返回Content-Length: 4771451
2025-08-21 16:42:18.606  3149-3181  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 完整下载模式
2025-08-21 16:42:18.608  3149-3181  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 完整下载，使用服务器返回大小: 4771451
2025-08-21 16:42:18.610  3149-3181  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader localFile = true /sdcard/Android/data/com.dspread.mdm.service/files/config/logo.bin, 追加模式: false
2025-08-21 16:42:19.354  3149-3197  ProfileInstaller        com.dspread.mdm.service              D  Installing profile for com.dspread.mdm.service
2025-08-21 16:42:25.351  3149-3181  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 计算MD5: logo.bin
2025-08-21 16:42:25.431  3149-3181  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 文件: 4771451 MD5: ba1ee533924eae5c408465e7cddcbda4
2025-08-21 16:42:25.433  3149-3181  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader MD5校验正确
2025-08-21 16:42:25.435  3149-3181  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 清理连接资源
2025-08-21 16:42:25.438  3149-3181  Provisioning            com.dspread.mdm.service              I  ℹ️ Logo 下载完成: /sdcard/Android/data/com.dspread.mdm.service/files/config/logo.bin
2025-08-21 16:42:25.440  3149-3181  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] Provisioning进度: 正在下载 BootAnimation...
2025-08-21 16:42:25.443  3149-3181  Provisioning            com.dspread.mdm.service              I  ℹ️ 文件不存在，开始下载: BootAnimation
2025-08-21 16:42:25.446  3149-3181  Provisioning            com.dspread.mdm.service              I  ℹ️ 下载: https://siot-bucket01.s3.ap-northeast-1.amazonaws.com/test/bootanimation.zip -> /sdcard/Android/data/com.dspread.mdm.service/files/config/bootanimation.zip
2025-08-21 16:42:25.447  3149-3181  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 尝试下载，第1次
2025-08-21 16:42:25.450  3149-3181  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 开始下载
2025-08-21 16:42:25.452  3149-3181  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader URL: https://siot-bucket01.s3.ap-northeast-1.amazonaws.com/test/bootanimation.zip
2025-08-21 16:42:25.454  3149-3181  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 路径: /sdcard/Android/data/com.dspread.mdm.service/files/config/bootanimation.zip
2025-08-21 16:42:25.456  3149-3181  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 本地文件不存在，创建文件
2025-08-21 16:42:25.459  3149-3181  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader allFileLength=0, 需要从服务器获取文件大小
2025-08-21 16:42:25.463  3149-3181  System.out              com.dspread.mdm.service              I  [okhttp]:check permission begin!
2025-08-21 16:42:25.463  3149-3181  System.out              com.dspread.mdm.service              I  [okhttp]:not MMS!
2025-08-21 16:42:25.463  3149-3181  System.out              com.dspread.mdm.service              I  [okhttp]:not Email!
2025-08-21 16:42:25.465  3149-3181  System.out              com.dspread.mdm.service              I  [OkHttp] sendRequest>>
2025-08-21 16:42:25.466  3149-3181  System.out              com.dspread.mdm.service              I  [OkHttp] sendRequest<<
2025-08-21 16:42:25.769  3149-3181  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader HTTP响应码: 200
2025-08-21 16:42:25.772  3149-3181  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 服务器返回Content-Length: 4771451
2025-08-21 16:42:25.775  3149-3181  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 完整下载模式
2025-08-21 16:42:25.777  3149-3181  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 完整下载，使用服务器返回大小: 4771451
2025-08-21 16:42:25.780  3149-3181  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader localFile = true /sdcard/Android/data/com.dspread.mdm.service/files/config/bootanimation.zip, 追加模式: false
2025-08-21 16:42:31.581  3149-3181  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 计算MD5: bootanimation.zip
2025-08-21 16:42:31.657  3149-3181  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 文件: 4771451 MD5: ba1ee533924eae5c408465e7cddcbda4
2025-08-21 16:42:31.659  3149-3181  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader MD5校验正确
2025-08-21 16:42:31.661  3149-3181  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 清理连接资源
2025-08-21 16:42:31.663  3149-3181  Provisioning            com.dspread.mdm.service              I  ℹ️ BootAnimation 下载完成: /sdcard/Android/data/com.dspread.mdm.service/files/config/bootanimation.zip
2025-08-21 16:42:31.665  3149-3181  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] Provisioning进度: 正在应用配置...
2025-08-21 16:42:31.667  3149-3181  Provisioning            com.dspread.mdm.service              I  ℹ️ 配置应用完成
2025-08-21 16:42:31.669  3149-3181  Provisioning            com.dspread.mdm.service              I  ℹ️ 远程配置成功，标记首次配置完成
2025-08-21 16:42:31.673  3149-3181  Provisioning            com.dspread.mdm.service              D  🔧 状态标志保存成功: /sdcard/Android/data/com.dspread.mdm.service/files/config/provisioning_flags.json
2025-08-21 16:42:31.675  3149-3181  Provisioning            com.dspread.mdm.service              I  ℹ️ ProvisioningManager: 初始启动标记完成，网络重连重试功能已激活
2025-08-21 16:42:31.677  3149-3181  Provisioning            com.dspread.mdm.service              I  ℹ️ Provisioning配置执行完成
2025-08-21 16:42:31.678  3149-3181  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] Provisioning完成，启动核心组件
2025-08-21 16:42:31.687  3149-3149  WebSocket               com.dspread.mdm.service              I  🔧 开始初始化 WebSocket 组件...
2025-08-21 16:42:31.693  3149-3149  WebSocket               com.dspread.mdm.service              I  🔧 初始化 WebSocket 密钥管理器
2025-08-21 16:42:31.696  3149-3149  WebSocket               com.dspread.mdm.service              I  🔧 生成客户端公私钥对
2025-08-21 16:42:31.860  3149-3149  Common                  com.dspread.mdm.service              I  ✅ WebSocket 密钥管理器初始化完成
2025-08-21 16:42:31.869  3149-3149  WebSocket               com.dspread.mdm.service              I  🔧 开始初始化 WsMessageCenter...
2025-08-21 16:42:31.871  3149-3149  WebSocket               com.dspread.mdm.service              I  🔧 WsMessageCenter 首次初始化
2025-08-21 16:42:31.894  3149-3149  Task                    com.dspread.mdm.service              D  🔧 从存储加载任务: 1 个
2025-08-21 16:42:31.897  3149-3149  WebSocket               com.dspread.mdm.service              I  🔧 WsTaskManager 初始化完成
2025-08-21 16:42:32.001  3149-3149  WebSocket               com.dspread.mdm.service              I  🔧 WsMessageCenter 初始化完成
2025-08-21 16:42:32.004  3149-3149  WebSocket               com.dspread.mdm.service              I  🔧 开始清理终态任务，当前任务数量: 1
2025-08-21 16:42:32.006  3149-3149  WebSocket               com.dspread.mdm.service              I  🔧 🔍 检查任务: taskId=1754445639206, taskResult=D02, isTerminal=true
2025-08-21 16:42:32.008  3149-3149  WebSocket               com.dspread.mdm.service              I  🔧 🗑️ 删除终态任务: taskId=1754445639206, taskResult=D02
2025-08-21 16:42:32.011  3149-3149  Task                    com.dspread.mdm.service              D  🔧 保存任务列表到存储: 0 个
2025-08-21 16:42:32.013  3149-3149  WebSocket               com.dspread.mdm.service              I  🔧 ✅ 终态任务清理完成，删除 1 个任务，剩余 0 个任务
2025-08-21 16:42:32.015  3149-3149  WebSocket               com.dspread.mdm.service              I  🔧 验证清理结果: 存储中剩余任务数量 0
2025-08-21 16:42:32.017  3149-3149  WebSocket               com.dspread.mdm.service              I  🔧 开始重置任务状态，当前任务个数: 0
2025-08-21 16:42:32.020  3149-3149  WebSocket               com.dspread.mdm.service              I  🔧 没有需要重置的任务
2025-08-21 16:42:32.023  3149-3149  Common                  com.dspread.mdm.service              I  ✅ 任务状态恢复完成
2025-08-21 16:42:32.028  3149-3149  WebSocket               com.dspread.mdm.service              I  🔧 使用Provisioning配置的WebSocket URL: wss://api.dspreadserv.net/status/websocket/register
2025-08-21 16:42:32.030  3149-3149  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接管理器初始化完成
2025-08-21 16:42:32.032  3149-3149  Common                  com.dspread.mdm.service              I  ✅ WebSocket 管理器初始化完成
2025-08-21 16:42:32.034  3149-3149  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 管理器初始化完成
2025-08-21 16:42:32.037  3149-3149  Common                  com.dspread.mdm.service              I  ✅ WebSocket 组件初始化完成
2025-08-21 16:42:32.039  3149-3149  WebSocket               com.dspread.mdm.service              I  🔧 启动 WebSocket 连接...
2025-08-21 16:42:32.041  3149-3149  WebSocket               com.dspread.mdm.service              I  🔧 开始连接 WebSocket...
2025-08-21 16:42:32.052  3149-3149  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接到: wss://api.dspreadserv.net/status/websocket/register?serialNo=MDE2MTAwNDAyMDIzMDcwNjAyMjc=&publicKey=TUlHZk1BMEdDU3FHU0liM0RRRUJBUVVBQTRHTkFEQ0JpUUtCZ1FDdlN6OEhuYXFwTVpoZkE3T0RzZEhwNnVYMzh3K3BPMEdGNFlVakUrTzVEWGU5MjJzNkZvN2ljSUhpS25CUmVuVXhNZkxCZUEvUjZYUFZQUndGQlloQlV0QnpTVm9mcngzWXhEVTJiNFp1SzZMSXQ2TWc0SmtoajNoRHFIdDhDM2xCb2hjODRkYk9JMFFiQ3BTbFVkek1TcUp1VkxNUWpoOGtXMmhjSjNRSmNRSURBUUFC&query=1&msgVer=3&timestamp=1755765752044&signature=DX8Hd546qevTV+pD7xufh0G4X6WdApL8aMEhQmw46wYauGS0wzpufYmkx/ybxcJb49RzU3fzKDpxSQ2lIpxvAzgc8pB0bnj89hZ0B8TJH03TWR3IweX3mi6WXTTVZ1+dDmB2tIoutvbuqUywacNGVrUhui8meuDAz6q9w2PBVO8=
2025-08-21 16:42:32.057  3149-3149  WebSocket               com.dspread.mdm.service              I  🔧 未检测到系统代理设置
2025-08-21 16:42:32.077  3149-3149  WebSocket               com.dspread.mdm.service              I  🔧 ===== WebSocket 库默认配置 =====
2025-08-21 16:42:32.079  3149-3149  WebSocket               com.dspread.mdm.service              I  🔧 默认 PING 间隔: 30000ms (30秒)
2025-08-21 16:42:32.081  3149-3149  WebSocket               com.dspread.mdm.service              I  🔧 默认 PONG 间隔: 0ms (0秒)
2025-08-21 16:42:32.084  3149-3149  WebSocket               com.dspread.mdm.service              I  🔧 帧队列大小: 5
2025-08-21 16:42:32.086  3149-3149  WebSocket               com.dspread.mdm.service              I  🔧 连接超时: 15000ms (15秒)
2025-08-21 16:42:32.088  3149-3149  WebSocket               com.dspread.mdm.service              I  🔧 允许缺失关闭帧: false
2025-08-21 16:42:32.090  3149-3149  WebSocket               com.dspread.mdm.service              I  🔧 ================================
2025-08-21 16:42:32.093  3149-3149  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接请求已发送
2025-08-21 16:42:32.096  3149-3149  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接启动完成
2025-08-21 16:42:32.099  3149-3149  Common                  com.dspread.mdm.service              D  🔧 [ModuleManagerRegistry] 模块管理器注册中心初始化完成
2025-08-21 16:42:32.102  3149-3149  Provisioning            com.dspread.mdm.service              D  🔧 ProvisioningEventHandler 启动Provisioning定时器: 300s (0小时)
2025-08-21 16:42:32.102  3149-3181  Common                  com.dspread.mdm.service              D  🔧 [ModuleManagerRegistry] 启动所有模块
2025-08-21 16:42:32.107  3149-3149  Common                  com.dspread.mdm.service              I  ✅ ⏰ 设置初始化定时器成功，下次执行: 300秒后 (0小时)
2025-08-21 16:42:32.111  3149-3181  Common                  com.dspread.mdm.service              D  🔧 Module LogStream Manager status changed to: INITIALIZED
2025-08-21 16:42:32.112  3149-3149  Common                  com.dspread.mdm.service              I  ✅ ⏰ 设置心跳定时器成功，下次执行: 60秒后 (1分钟)
2025-08-21 16:42:32.114  3149-3205  System.out              com.dspread.mdm.service              I  [socket]:check permission begin!
2025-08-21 16:42:32.116  3149-3149  Common                  com.dspread.mdm.service              I  ✅ ⏰ 设置任务执行定时器成功，下次执行: 60秒后 (1分钟)
2025-08-21 16:42:32.117  3149-3181  Common                  com.dspread.mdm.service              D  🔧 Module LogStream Manager status changed to: STARTING
2025-08-21 16:42:32.121  3149-3149  Common                  com.dspread.mdm.service              I  ✅ ⏰ 设置终端信息定时器成功，下次执行: 120秒后 (2分钟)
2025-08-21 16:42:32.123  3149-3149  Common                  com.dspread.mdm.service              D  🔧 TimerConfig ========== 当前定时器配置 (调试模式) ==========
2025-08-21 16:42:32.125  3149-3149  Common                  com.dspread.mdm.service              D  🔧 TimerConfig ①心跳定时器: 60秒
2025-08-21 16:42:32.128  3149-3149  Common                  com.dspread.mdm.service              D  🔧 TimerConfig ②终端信息上传定时器: 120秒
2025-08-21 16:42:32.130  3149-3149  Common                  com.dspread.mdm.service              D  🔧 TimerConfig ③任务执行定时器: 60秒
2025-08-21 16:42:32.133  3149-3149  Common                  com.dspread.mdm.service              D  🔧 TimerConfig ④服务守护定时器: 120秒
2025-08-21 16:42:32.135  3149-3149  Common                  com.dspread.mdm.service              D  🔧 TimerConfig ⑤初始化定时器: 300秒
2025-08-21 16:42:32.138  3149-3149  Common                  com.dspread.mdm.service              D  🔧 TimerConfig ===============================================
2025-08-21 16:42:32.140  3149-3149  Common                  com.dspread.mdm.service              I  ✅ [SmartMdmBackgroundService] 所有定时器启动完成
2025-08-21 16:42:32.143  3149-3149  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 核心组件初始化完成
2025-08-21 16:42:32.198  3149-3181  Common                  com.dspread.mdm.service              D  🔧 Module LogStream Manager status changed to: RUNNING
2025-08-21 16:42:32.201  3149-3181  Common                  com.dspread.mdm.service              D  🔧 [ModuleManagerRegistry] LogStreamManager启动成功
2025-08-21 16:42:32.225  3149-3181  Common                  com.dspread.mdm.service              D  🔧 Module Geofence Manager status changed to: INITIALIZED
2025-08-21 16:42:32.229  3149-3181  Common                  com.dspread.mdm.service              D  🔧 Module Geofence Manager status changed to: STARTING
2025-08-21 16:42:32.247  3149-3181  Platform                com.dspread.mdm.service              D  🔧 用户交互监控已启动（简化版本）
2025-08-21 16:42:32.247  3149-3149  Platform                com.dspread.mdm.service              D  🔧 [WiFiManagerApi] WiFiManagerApi 单例实例已创建
2025-08-21 16:42:32.248  3149-3181  Platform                com.dspread.mdm.service              D  🔧 使用模拟用户交互检测
2025-08-21 16:42:32.251  3149-3181  Platform                com.dspread.mdm.service              D  🔧 用户交互监控已启动
2025-08-21 16:42:32.253  3149-3181  Service                 com.dspread.mdm.service              D  🔧 [GeofenceManager]Constants.geofenceStatus = 1
2025-08-21 16:42:32.255  3149-3181  Service                 com.dspread.mdm.service              D  🔧 [GeofenceManager]-- Constants.storeId =  -- Constants.storeSsid =  -- Constants.storeIp = 
2025-08-21 16:42:32.259  3149-3181  Common                  com.dspread.mdm.service              D  🔧 Module Geofence Manager status changed to: RUNNING
2025-08-21 16:42:32.261  3149-3181  Common                  com.dspread.mdm.service              D  🔧 [ModuleManagerRegistry] GeofenceManager启动成功
2025-08-21 16:42:32.274  3149-3181  RuleBase                com.dspread.mdm.service              D  🔧 RuleBaseManager 开始初始化RuleBaseManager...
2025-08-21 16:42:32.280  3149-3181  RuleBase                com.dspread.mdm.service              D  🔧 RuleBaseStorage RuleBase存储管理器初始化完成
2025-08-21 16:42:32.282  3149-3181  RuleBase                com.dspread.mdm.service              D  🔧 RuleStateMonitor 状态监控器初始化完成
2025-08-21 16:42:32.284  3149-3181  RuleBase                com.dspread.mdm.service              D  🔧 RuleBaseManager RuleBaseManager初始化完成
2025-08-21 16:42:32.286  3149-3181  Common                  com.dspread.mdm.service              D  🔧 [ModuleManagerRegistry] RuleBaseManager启动成功
2025-08-21 16:42:32.288  3149-3181  Common                  com.dspread.mdm.service              D  🔧 [ModuleManagerRegistry] 所有模块启动完成
2025-08-21 16:42:32.359  3149-3206  System.out              com.dspread.mdm.service              I  [socket]:check permission begin!
2025-08-21 16:42:34.277  3149-3217  Common                  com.dspread.mdm.service              I  ✅ WebSocket 物理连接成功，等待服务器 hellMsg
2025-08-21 16:42:34.281  3149-3217  WebSocket               com.dspread.mdm.service              I  🔧 ===== 心跳发送器实际配置 =====
2025-08-21 16:42:34.284  3149-3217  WebSocket               com.dspread.mdm.service              I  🔧 实际 PING 发送间隔: 30000ms (30秒)
2025-08-21 16:42:34.287  3149-3217  WebSocket               com.dspread.mdm.service              I  🔧 PING 定时器名称: PingSender
2025-08-21 16:42:34.290  3149-3217  WebSocket               com.dspread.mdm.service              I  🔧 实际 PONG 发送间隔: 0ms (0秒)
2025-08-21 16:42:34.294  3149-3217  WebSocket               com.dspread.mdm.service              I  🔧 PONG 定时器名称: PongSender
2025-08-21 16:42:34.296  3149-3217  WebSocket               com.dspread.mdm.service              I  🔧 ===============================
2025-08-21 16:42:34.299  3149-3217  WebSocket               com.dspread.mdm.service              I  🔧 启动PING/PONG检测，阈值: 连续3个PING无PONG响应
2025-08-21 16:42:34.327  3149-3217  Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"msg_state":"P","request_time":"1755580536662","data":{"taskList":[{"beginDate":"2024-08-19 05:15:36","taskType":"02","apkName":"Via","endDate":"9999-12-31 23:59:59","appId":"1755580536662","packName":"mark.via","versionName":"6.2.0","taskId":"1755580536662","versionCode":"20250117"}]},"tranCode":"ST001","request_id":"1755580536662ST001","version":"1","serialNo":"01610040202307060227"}
2025-08-21 16:42:34.330  3149-3217  WebSocket               com.dspread.mdm.service              I  🔧 准备发送响应确认 - requestId: 1755580536662ST001, needResponse: true
2025-08-21 16:42:34.337  3149-3217  WebSocket               com.dspread.mdm.service              I  🔧 非重要消息，不缓存: C0000
2025-08-21 16:42:34.343  3149-3217  WebSocket               com.dspread.mdm.service              I  🔧 非重要消息，不缓存: C0000
2025-08-21 16:42:34.346  3149-3217  WebSocket               com.dspread.mdm.service              I  🔧 已发送C0000响应确认: requestId=1755580536662ST001
2025-08-21 16:42:34.349  3149-3217  Task                    com.dspread.mdm.service              D  🔧 处理任务消息: ST001
2025-08-21 16:42:34.351  3149-3217  Task                    com.dspread.mdm.service              I  ℹ️ 更新任务列表: requestId=1755580536662ST001, 任务数量=1
2025-08-21 16:42:34.352  3149-3217  Task                    com.dspread.mdm.service              D  🔧 任务插入到末尾: taskId=1755580536662
2025-08-21 16:42:34.355  3149-3217  Task                    com.dspread.mdm.service              D  🔧 添加/更新任务: taskId=1755580536662
2025-08-21 16:42:34.358  3149-3217  Task                    com.dspread.mdm.service              D  🔧 保存任务列表到存储: 1 个
2025-08-21 16:42:34.360  3149-3217  Task                    com.dspread.mdm.service              D  🔧 开始处理任务列表，共 1 个任务
2025-08-21 16:42:34.362  3149-3217  Task                    com.dspread.mdm.service              D  🔧 处理任务: taskId=1755580536662, type=02, package=mark.via, apk=Via
2025-08-21 16:42:34.364  3149-3217  Task                    com.dspread.mdm.service              D  🔧 处理卸载任务: 1755580536662
2025-08-21 16:42:34.371  3149-3217  Task                    com.dspread.mdm.service              D  🔧 更新任务状态: taskId=1755580536662, state=D02
2025-08-21 16:42:34.374  3149-3217  Task                    com.dspread.mdm.service              D  🔧 保存任务列表到存储: 1 个
2025-08-21 16:42:34.377  3149-3217  Task                    com.dspread.mdm.service              D  🔧 保存任务列表到存储: 1 个
2025-08-21 16:42:34.378  3149-3217  WebSocket               com.dspread.mdm.service              I  🔧 ST001 主动式上送: task_failed (主动: 2)
2025-08-21 16:42:34.385  3149-3217  WebSocket               com.dspread.mdm.service              I  🔧 消息已缓存: C0108 (缓存数量: 2)
2025-08-21 16:42:34.387  3149-3217  WebSocket               com.dspread.mdm.service              I  🔧 任务结果实时上送: taskId=1755580536662, result=D02 (1)
2025-08-21 16:42:34.401  3149-3217  Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"msg_state":"P","request_time":"1755580524415","data":{"taskList":[{"beginDate":"2024-08-19 05:15:24","taskType":"02","apkName":"OpenVPN for Android","endDate":"9999-12-31 23:59:59","appId":"1755580524415","packName":"de.blinkt.openvpn","versionName":"0.5.36a","taskId":"1755580524415","versionCode":"65"}]},"tranCode":"ST001","request_id":"1755580524415ST001","version":"1","serialNo":"01610040202307060227"}
2025-08-21 16:42:34.404  3149-3217  WebSocket               com.dspread.mdm.service              I  🔧 准备发送响应确认 - requestId: 1755580524415ST001, needResponse: true
2025-08-21 16:42:34.408  3149-3217  WebSocket               com.dspread.mdm.service              I  🔧 非重要消息，不缓存: C0000
2025-08-21 16:42:34.413  3149-3217  WebSocket               com.dspread.mdm.service              I  🔧 非重要消息，不缓存: C0000
2025-08-21 16:42:34.415  3149-3217  WebSocket               com.dspread.mdm.service              I  🔧 已发送C0000响应确认: requestId=1755580524415ST001
2025-08-21 16:42:34.417  3149-3217  Task                    com.dspread.mdm.service              D  🔧 处理任务消息: ST001
2025-08-21 16:42:34.419  3149-3217  Task                    com.dspread.mdm.service              I  ℹ️ 更新任务列表: requestId=1755580524415ST001, 任务数量=1
2025-08-21 16:42:34.421  3149-3217  Task                    com.dspread.mdm.service              D  🔧 任务按时间插入到位置: 0, taskId=1755580524415
2025-08-21 16:42:34.422  3149-3217  Task                    com.dspread.mdm.service              D  🔧 添加/更新任务: taskId=1755580524415
2025-08-21 16:42:34.425  3149-3217  Task                    com.dspread.mdm.service              D  🔧 发现已完成任务: taskId=1755580536662, result=D02
2025-08-21 16:42:34.427  3149-3217  Task                    com.dspread.mdm.service              D  🔧 上送任务结果: taskId=1755580536662, result=D02
2025-08-21 16:42:34.428  3149-3217  Task                    com.dspread.mdm.service              D  🔧 清理已完成任务: taskId=1755580536662, result=D02
2025-08-21 16:42:34.430  3149-3217  Task                    com.dspread.mdm.service              D  🔧 清理了 1 个已完成任务
2025-08-21 16:42:34.432  3149-3217  Task                    com.dspread.mdm.service              D  🔧 保存任务列表到存储: 1 个
2025-08-21 16:42:34.434  3149-3217  Task                    com.dspread.mdm.service              D  🔧 开始处理任务列表，共 1 个任务
2025-08-21 16:42:34.436  3149-3217  Task                    com.dspread.mdm.service              D  🔧 处理任务: taskId=1755580524415, type=02, package=de.blinkt.openvpn, apk=OpenVPN for Android
2025-08-21 16:42:34.437  3149-3217  Task                    com.dspread.mdm.service              D  🔧 处理卸载任务: 1755580524415
2025-08-21 16:42:34.440  3149-3217  Task                    com.dspread.mdm.service              D  🔧 更新任务状态: taskId=1755580524415, state=D02
2025-08-21 16:42:34.443  3149-3217  Task                    com.dspread.mdm.service              D  🔧 保存任务列表到存储: 1 个
2025-08-21 16:42:34.445  3149-3217  Task                    com.dspread.mdm.service              D  🔧 保存任务列表到存储: 1 个
2025-08-21 16:42:34.448  3149-3217  WebSocket               com.dspread.mdm.service              I  🔧 ST001 主动式上送: task_failed (主动: 3)
2025-08-21 16:42:34.455  3149-3217  WebSocket               com.dspread.mdm.service              I  🔧 消息已缓存: C0108 (缓存数量: 3)
2025-08-21 16:42:34.456  3149-3217  WebSocket               com.dspread.mdm.service              I  🔧 任务结果实时上送: taskId=1755580524415, result=D02 (1)
2025-08-21 16:42:34.472  3149-3217  Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"msg_state":"P","request_time":"1755763228221","data":{"geoInfo":{"wipeMin":"","modifyDate":"2025-08-21 08:00:28","endDate":"9999-12-31 23:59:59","roamingTag":"0","lockMin":"1","beginDate":"2024-08-21 08:00:28","isDefault":"1","lockMeter":"25","disableTag":"0","proId":"b8605d052cc945019e0ca09fbf63bf20","wipeStatus":"0","action":"M","proName":"Default","createDate":"2025-08-21 06:05:11"}},"tranCode":"ST007","request_id":"1755763228221ST007","version":"1","serialNo":"01610040202307060227"}
2025-08-21 16:42:34.474  3149-3217  WebSocket               com.dspread.mdm.service              I  🔧 准备发送响应确认 - requestId: 1755763228221ST007, needResponse: true
2025-08-21 16:42:34.479  3149-3217  WebSocket               com.dspread.mdm.service              I  🔧 非重要消息，不缓存: C0000
2025-08-21 16:42:34.484  3149-3217  WebSocket               com.dspread.mdm.service              I  🔧 非重要消息，不缓存: C0000
2025-08-21 16:42:34.486  3149-3217  WebSocket               com.dspread.mdm.service              I  🔧 已发送C0000响应确认: requestId=1755763228221ST007
2025-08-21 16:42:34.487  3149-3217  WebSocket               com.dspread.mdm.service              I  🔧 处理地理围栏配置消息 (ST007)
2025-08-21 16:42:34.491  3149-3181  Common                  com.dspread.mdm.service              D  🔧 发送C0000响应确认: requestId=1755763228221ST007, module=Geofence
2025-08-21 16:42:34.499  3149-3217  Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"hellMsg":"hello 01610040202307060227","tranCode":"S0000","version":"1","rebootTime":"03:22:42","serialNo":"01610040202307060227","deviceStatus":"6"}
2025-08-21 16:42:34.501  3149-3217  Common                  com.dspread.mdm.service              I  ✅ 收到服务器 hellMsg，WebSocket 业务连接成功！
2025-08-21 16:42:34.503  3149-3217  Common                  com.dspread.mdm.service              I  ✅ WebSocket 业务连接成功！
2025-08-21 16:42:34.504  3149-3217  WebSocket               com.dspread.mdm.service              I  🔧 业务连接成功，重连计数已重置为 0
2025-08-21 16:42:34.508  3149-3217  Platform                com.dspread.mdm.service              D  🔧 WakeLockManager 获取WiFi锁成功，防止WiFi休眠
2025-08-21 16:42:34.510  3149-3217  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接成功，检查自身更新任务状态
2025-08-21 16:42:34.513  3149-3217  Receiver                com.dspread.mdm.service              D  🔧 发送广播: com.dspread.mdm.service.CHECK_SELF_UPDATE_STATUS
2025-08-21 16:42:34.514  3149-3181  WebSocket               com.dspread.mdm.service              I  🔧 C0903 被动式上送: location_geofence_change (被动: 2)
2025-08-21 16:42:34.514  3149-3217  WebSocket               com.dspread.mdm.service              I  🔧 开始发送缓存消息，数量: 3
2025-08-21 16:42:34.518  3149-3217  WebSocket               com.dspread.mdm.service              I  🔧 C0902 主动式上送: cached_message_resend (主动: 4)
2025-08-21 16:42:34.523  3149-3217  Common                  com.dspread.mdm.service              I  ✅ 缓存消息发送成功: C0902
2025-08-21 16:42:34.556  3149-3181  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0903","serialNo":"01610040202307060227","request_time":"1755765754513","request_id":"1755765754513C0903","version":"1","data":{"hardwareInfo":[{"name":"CPU Usage","desc":"cpu:0.50"},{"name":"storage_usage_status","desc":" 5.42GB \/ 32.00GB ","state":"1"},{"name":"total_memory","desc":" 0.53GB \/ 1.86GB ","state":"1"},{"name":"androidVersion","desc":"11","state":"1"},{"name":"network_status","desc":"wifi","state":"1"},{"name":"OSVer","desc":"11","state":"1"}],"cardDeviceUsage":"[]","locationInfo":{"gps":{"longitude":"-999.0","latitude":"-999.0","satelliteCount":0,"satelliteCountInUsed":0,"satelliteAverSNR":"0.00","distance":"0.0","lockStatus":"1","lockMeter":"25","proId":"b8605d052cc945019e0ca09fbf63bf20","proName":"Default"}}},"myVersionName":"1.1.03.20250821.DSPREAD.MDM.SERVICE","terminalDate":"20250821164234"}
2025-08-21 16:42:35.026  3149-3217  WebSocket               com.dspread.mdm.service              I  🔧 C0108 主动式上送: cached_message_resend (主动: 5)
2025-08-21 16:42:35.031  3149-3217  Common                  com.dspread.mdm.service              I  ✅ 缓存消息发送成功: C0108
2025-08-21 16:42:35.534  3149-3217  WebSocket               com.dspread.mdm.service              I  🔧 C0108 主动式上送: cached_message_resend (主动: 6)
2025-08-21 16:42:35.539  3149-3217  Common                  com.dspread.mdm.service              I  ✅ 缓存消息发送成功: C0108
2025-08-21 16:42:36.042  3149-3217  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接成功
2025-08-21 16:42:36.045  3149-3217  WebSocket               com.dspread.mdm.service              I  🔧 设备状态: 6
2025-08-21 16:42:36.047  3149-3217  WebSocket               com.dspread.mdm.service              I  🔧 检查首次连接标志: isFirstTimeWebSocketConnected=true
2025-08-21 16:42:36.049  3149-3217  WebSocket               com.dspread.mdm.service              I  🔧 首次WebSocket连接成功，发送初始化信息并执行初始化检查
2025-08-21 16:42:36.051  3149-3217  WebSocket               com.dspread.mdm.service              I  🔧 开始发送初始化信息（带流量控制）
2025-08-21 16:42:36.053  3149-3217  WebSocket               com.dspread.mdm.service              I  🔧 C0901 被动式上送: first_connection (被动: 3)
2025-08-21 16:42:36.056  3149-3217  Platform                com.dspread.mdm.service              D  🔧 重新收集应用信息
2025-08-21 16:42:36.133  3149-3217  AppManager              com.dspread.mdm.service              I  ℹ️ 应用信息获取完成: 总数120(系统119/用户1) 返回1个
2025-08-21 16:42:36.138  3149-3217  Platform                com.dspread.mdm.service              D  🔧 应用信息: 1 个应用
2025-08-21 16:42:36.148  3149-3217  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0901","serialNo":"01610040202307060227","request_time":"1755765756141","request_id":"1755765756141C0901","version":"1","data":{"apkInfo":[{"packName":"com.dspread.mdm.service","apkName":"service","versionCode":22,"versionName":"1.1.03.20250821.DSPREAD.MDM.SERVICE","updateDate":"2025-08-21 16:42:11"}],"sytemInfo":{"androidVersion":"11","buildNumber":"D60_Baseline_debug_20250708_ota_test","aspl":"2021-12-05"},"serviceInfo":[]},"myVersionName":"1.1.03.20250821.DSPREAD.MDM.SERVICE","terminalDate":"20250821164236"}
2025-08-21 16:42:36.149  3149-3217  WebSocket               com.dspread.mdm.service              I  🔧 C0901 应用信息上送成功 (1)
2025-08-21 16:42:37.152  3149-3217  WebSocket               com.dspread.mdm.service              I  🔧 C0902 被动式上送: first_connection (被动: 4)
2025-08-21 16:42:37.163  3149-3217  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0902","serialNo":"01610040202307060227","request_time":"1755765757157","request_id":"1755765757157C0902","version":"1","data":{"batteryLife":82,"batteryHealth":2,"temprature":"27.3","isCharging":"1","isLowBattery":"0","powerOff":"0","outOfBattery":"0"},"myVersionName":"1.1.03.20250821.DSPREAD.MDM.SERVICE","terminalDate":"20250821164237"}
2025-08-21 16:42:37.166  3149-3217  WebSocket               com.dspread.mdm.service              I  🔧 C0902 电池状态上送成功 (1, trigger=first_connection)
2025-08-21 16:42:38.169  3149-3217  WebSocket               com.dspread.mdm.service              I  🔧 C0903 被动式上送: first_connection (被动: 5)
2025-08-21 16:42:38.286  3149-3217  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0903","serialNo":"01610040202307060227","request_time":"1755765758274","request_id":"1755765758274C0903","version":"1","data":{"hardwareInfo":[{"name":"CPU Usage","desc":"cpu:0.50"},{"name":"storage_usage_status","desc":" 5.42GB \/ 32.00GB ","state":"1"},{"name":"total_memory","desc":" 0.53GB \/ 1.86GB ","state":"1"},{"name":"androidVersion","desc":"11","state":"1"},{"name":"network_status","desc":"wifi","state":"1"},{"name":"OSVer","desc":"11","state":"1"}],"locationInfo":{"gps":{"longitude":"-999.0","latitude":"-999.0","satelliteCount":0,"satelliteCountInUsed":0,"satelliteAverSNR":"0.00","distance":"0.0","lockStatus":"1","lockMeter":"25","proId":"b8605d052cc945019e0ca09fbf63bf20","proName":"Default"},"baseSite":{"MCC":"","MNC":"","CID":"-1","LAC":"-1","DBM":"-999","IMSI":"","ICCID":""},"wifi":{"SSID":"fubox_2.4G","IP":"**************","MAC":"52:C3:63:13:8C:EE","SSTH":"-25","BSSID":"7c:10:c9:d6:9f:10"},"address":"无法获取"}},"myVersionName":"1.1.03.20250821.DSPREAD.MDM.SERVICE","terminalDate":"20250821164238"}
2025-08-21 16:42:38.288  3149-3217  WebSocket               com.dspread.mdm.service              I  🔧 C0903 数据信息上送成功 (1)
2025-08-21 16:42:39.290  3149-3217  WebSocket               com.dspread.mdm.service              I  🔧 C0904 被动式上送: first_connection (被动: 6)
2025-08-21 16:42:39.386  3149-3217  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0904","serialNo":"01610040202307060227","request_time":"1755765759373","request_id":"1755765759373C0904","version":"1","data":{"wifiOption":[{"SSID":"fubox_2.4G","SSTH":"-28"},{"SSID":"2206-5G","SSTH":"-32"},{"SSID":"fubox_5G","SSTH":"-40"},{"SSID":"2206","SSTH":"-41"},{"SSID":"2205_5G","SSTH":"-51"},{"SSID":"2205","SSTH":"-53"},{"SSID":"@Ruijie-1816","SSTH":"-56"},{"SSID":"2207","SSTH":"-67"},{"SSID":"DIRECT-C6-Mi All-in-One Inkjet","SSTH":"-70"},{"SSID":"DIRECT-CC-HP Laser 1188w","SSTH":"-71"},{"SSID":"2306","SSTH":"-72"},{"SSID":"2106","SSTH":"-72"},{"SSID":"2103","SSTH":"-77"},{"SSID":"2207-5G","SSTH":"-80"},{"SSID":"诺富特酒店2208","SSTH":"-81"},{"SSID":"dingjie888","SSTH":"-82"},{"SSID":"FJQS","SSTH":"-83"},{"SSID":"ChinaNet-SnSC","SSTH":"-85"},{"SSID":"2405","SSTH":"-86"},{"SSID":"2103_5G","SSTH":"-86"},{"SSID":"1621","SSTH":"-87"},{"SSID":"HUAWEI_B311_福","SSTH":"-92"},{"SSID":"2106-5G","SSTH":"-94"}],"baseSite":{"MCC":"","MNC":"","CID":"-1","LAC":"-1","DBM":"-999","IMSI":"","ICCID":""},"wifi":{"SSID":"fubox_2.4G","IP":"**************","MAC":"52:C3:63:13:8C:EE","SSTH":"-25","BSSID":"7c:10:c9:d6:9f:10"}},"myVersionName":"1.1.03.20250821.DSPREAD.MDM.SERVICE","terminalDate":"20250821164239"}
2025-08-21 16:42:39.388  3149-3217  WebSocket               com.dspread.mdm.service              I  🔧 C0904 网络状态上送成功 (1)
2025-08-21 16:42:40.391  3149-3217  WebSocket               com.dspread.mdm.service              I  🔧 C0906 被动式上送: first_connection (被动: 7)
2025-08-21 16:42:40.395  3149-3217  Platform                com.dspread.mdm.service              W  ⚠️ SpVersionApi SP版本信息格式异常，未找到SP_VERSION标记
2025-08-21 16:42:40.397  3149-3217  Platform                com.dspread.mdm.service              D  🔧 SpVersionApi 从Settings获取SP版本: STD 582.1.238.8
2025-08-21 16:42:40.411  3149-3217  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0906","serialNo":"01610040202307060227","request_time":"1755765760403","request_id":"1755765760403C0906","version":"1","data":{"firmWareInfo":{"spfw":"STD 582.1.238.8"},"imei_1":"014250620000709","imei_2":"","wifi_mac":"52:c3:63:13:8c:ee","bt_mac":"","bsn":""},"myVersionName":"1.1.03.20250821.DSPREAD.MDM.SERVICE","terminalDate":"20250821164240"}
2025-08-21 16:42:40.414  3149-3217  WebSocket               com.dspread.mdm.service              I  🔧 C0906 设备信息上送成功 (1)
2025-08-21 16:42:40.417  3149-3217  WebSocket               com.dspread.mdm.service              I  🔧 C0109 被动式上送: first_connection (被动: 8)
2025-08-21 16:42:40.421  3149-3217  Platform                com.dspread.mdm.service              D  🔧 重新收集应用信息（强制刷新）
2025-08-21 16:42:40.487  3149-3217  AppManager              com.dspread.mdm.service              I  ℹ️ 应用信息获取完成: 总数120(系统119/用户1) 返回1个
2025-08-21 16:42:40.491  3149-3217  Platform                com.dspread.mdm.service              D  🔧 应用信息: 1 个应用
2025-08-21 16:42:40.601  3149-3165  System                  com.dspread.mdm.service              W  A resource failed to call close. 
2025-08-21 16:42:40.604  3149-3217  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0109","serialNo":"01610040202307060227","request_time":"1755765760587","request_id":"1755765760587C0109","version":"1","data":{"apkInfo":[{"packName":"com.dspread.mdm.service","apkName":"service","versionCode":22,"versionName":"1.1.03.20250821.DSPREAD.MDM.SERVICE","updateDate":"2025-08-21 16:42:11"}],"locationInfo":{"gps":{"longitude":"-999.0","latitude":"-999.0","satelliteCount":0,"satelliteCountInUsed":0,"satelliteAverSNR":"0.00","distance":"0.0","lockStatus":"1","lockMeter":"25","proId":"b8605d052cc945019e0ca09fbf63bf20","proName":"Default"},"baseSite":{"MCC":"","MNC":"","CID":"-1","LAC":"-1","DBM":"-999","IMSI":"","ICCID":""},"wifi":{"SSID":"fubox_2.4G","IP":"**************","MAC":"52:C3:63:13:8C:EE","SSTH":"-25","BSSID":"7c:10:c9:d6:9f:10"},"address":"无法获取"},"hardwareInfo":[{"name":"CPU Usage","desc":"cpu:0.50"},{"name":"storage_usage_status","desc":" 5.42GB \/ 32.00GB ","state":"1"},{"name":"total_memory","desc":" 0.53GB \/ 1.86GB ","state":"1"},{"name":"androidVersion","desc":"11","state":"1"},{"name":"network_status","desc":"wifi","state":"1"},{"name":"OSVer","desc":"11","state":"1"}],"wifiOption":[{"SSID":"fubox_2.4G","SSTH":"-28"},{"SSID":"2206-5G","SSTH":"-32"},{"SSID":"fubox_5G","SSTH":"-40"},{"SSID":"2206","SSTH":"-41"},{"SSID":"2205_5G","SSTH":"-51"},{"SSID":"2205","SSTH":"-53"},{"SSID":"@Ruijie-1816","SSTH":"-56"},{"SSID":"2207","SSTH":"-67"},{"SSID":"DIRECT-C6-Mi All-in-One Inkjet","SSTH":"-70"},{"SSID":"DIRECT-CC-HP Laser 1188w","SSTH":"-71"},{"SSID":"2306","SSTH":"-72"},{"SSID":"2106","SSTH":"-72"},{"SSID":"2103","SSTH":"-77"},{"SSID":"2207-5G","SSTH":"-80"},{"SSID":"诺富特酒店2208","SSTH":"-81"},{"SSID":"dingjie888","SSTH":"-82"},{"SSID":"FJQS","SSTH":"-83"},{"SSID":"ChinaNet-SnSC","SSTH":"-85"},{"SSID":"2405","SSTH":"-86"},{"SSID":"2103_5G","SSTH":"-86"},{"SSID":"1621","SSTH":"-87"},{"SSID":"HUAWEI_B311_福","SSTH":"-92"},{"SSID":"2106-5G","SSTH":"-94"}],"sytemInfo":{"androidVersion":"11","buildNumber":"D60_Baseline_debug_20250708_ota_test","aspl":"2021-12-05"}},"myVersionName":"1.1.03.20250821.DSPREAD.MDM.SERVICE","terminalDate":"20250821164240"}
2025-08-21 16:42:40.607  3149-3217  WebSocket               com.dspread.mdm.service              I  🔧 C0109 终端信息上传完成 (1, trigger=first_connection)
2025-08-21 16:42:40.608  3149-3217  Common                  com.dspread.mdm.service              I  ✅ 初始化信息发送完成
2025-08-21 16:42:40.610  3149-3217  WebSocket               com.dspread.mdm.service              I  🔧 检查服务更新结果
2025-08-21 16:42:40.612  3149-3217  Task                    com.dspread.mdm.service              I  ℹ️ 本地任务列表长度: 1
2025-08-21 16:42:40.614  3149-3217  Task                    com.dspread.mdm.service              D  🔧 本地任务列表内容: [{"beginDate":"2024-08-19 05:15:24","taskType":"02","apkName":"OpenVPN for Android","endDate":"9999-12-31 23:59:59","appId":"1755580524415","packName":"de.blinkt.openvpn","versionName":"0.5.36a","taskId":"1755580524415","versionCode":"65","request_id":"1755580524415ST001","request_time":"1755580524415","silent_install":"","taskResult":"D02","lastUpdateTime":1755765754444}]
2025-08-21 16:42:40.616  3149-3217  Task                    com.dspread.mdm.service              I  ℹ️ 当前应用版本: versionCode=22, versionName=1.1.03.20250821.DSPREAD.MDM.SERVICE
2025-08-21 16:42:40.618  3149-3217  Task                    com.dspread.mdm.service              I  ℹ️ 任务列表数量: 1
2025-08-21 16:42:40.619  3149-3217  Task                    com.dspread.mdm.service              D  🔧 检查任务[0]: taskId=1755580524415, taskType=02, pkgName=de.blinkt.openvpn, versionCode=65, versionName=0.5.36a, taskResult=D02
2025-08-21 16:42:40.621  3149-3217  WebSocket               com.dspread.mdm.service              I  🔧 检查OS升级状态...
2025-08-21 16:42:40.625  3149-3217  Common                  com.dspread.mdm.service              D  🔧 OsUpdateStatusChecker WebSocket连接成功，检查OS升级状态
2025-08-21 16:42:40.627  3149-3217  Common                  com.dspread.mdm.service              D  🔧 OsUpdateStatusChecker 没有待检查的OS升级任务
2025-08-21 16:42:40.629  3149-3217  WebSocket               com.dspread.mdm.service              I  🔧 OS升级状态检查已触发
2025-08-21 16:42:40.630  3149-3217  WebSocket               com.dspread.mdm.service              I  🔧 首次连接初始化检查完成
2025-08-21 16:42:40.640  3149-3217  Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"request_time":"1755765756654","org_request_time":"1755765754380","org_request_id":"1755765754380C0108","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755765756654S0000","serialNo":"01610040202307060227"}
2025-08-21 16:42:40.643  3149-3217  WebSocket               com.dspread.mdm.service              I  🔧 收到响应消息: transCode=S0000, orgRequestId=1755765754380C0108, state=0, remark=
2025-08-21 16:42:40.644  3149-3217  WebSocket               com.dspread.mdm.service              I  🔧 处理任务结果响应: state=0
2025-08-21 16:42:40.646  3149-3217  WebSocket               com.dspread.mdm.service              I  🔧 任务结果上传成功
2025-08-21 16:42:40.655  3149-3217  Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"request_time":"1755765757267","org_request_time":"1755765754450","org_request_id":"1755765754450C0108","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755765757267S0000","serialNo":"01610040202307060227"}
2025-08-21 16:42:40.658  3149-3217  WebSocket               com.dspread.mdm.service              I  🔧 收到响应消息: transCode=S0000, orgRequestId=1755765754450C0108, state=0, remark=
2025-08-21 16:42:40.660  3149-3217  WebSocket               com.dspread.mdm.service              I  🔧 处理任务结果响应: state=0
2025-08-21 16:42:40.661  3149-3217  WebSocket               com.dspread.mdm.service              I  🔧 任务结果上传成功
2025-08-21 16:42:40.931  3149-3217  Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"request_time":"1755765761881","org_request_time":"1755765760403","org_request_id":"1755765760403C0906","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755765761881S0000","serialNo":"01610040202307060227"}
2025-08-21 16:42:40.933  3149-3217  WebSocket               com.dspread.mdm.service              I  🔧 收到响应消息: transCode=S0000, orgRequestId=1755765760403C0906, state=0, remark=
2025-08-21 16:42:40.935  3149-3217  WebSocket               com.dspread.mdm.service              I  🔧 处理设备信息响应: state=0
2025-08-21 16:42:40.937  3149-3217  WebSocket               com.dspread.mdm.service              I  🔧 设备信息上传成功
2025-08-21 16:42:41.341  3149-3217  Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"request_time":"1755765762388","org_request_time":"1755765760587","org_request_id":"1755765760587C0109","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755765762388S0000","serialNo":"01610040202307060227"}
2025-08-21 16:42:41.344  3149-3217  WebSocket               com.dspread.mdm.service              I  🔧 收到响应消息: transCode=S0000, orgRequestId=1755765760587C0109, state=0, remark=
2025-08-21 16:42:41.346  3149-3217  WebSocket               com.dspread.mdm.service              I  🔧 终端信息上传成功
