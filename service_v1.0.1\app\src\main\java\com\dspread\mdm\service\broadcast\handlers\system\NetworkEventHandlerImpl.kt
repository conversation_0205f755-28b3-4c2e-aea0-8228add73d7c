package com.dspread.mdm.service.broadcast.handlers.system

import android.content.Context
import android.content.Intent
import android.net.ConnectivityManager
import android.net.NetworkCapabilities
import com.dspread.mdm.service.network.websocket.WebSocketCenter
import com.dspread.mdm.service.network.websocket.message.WsMessageSender
import com.dspread.mdm.service.platform.api.network.NetworkApi
import com.dspread.mdm.service.broadcast.core.BroadcastActions
import com.dspread.mdm.service.broadcast.core.NetworkEventHandler
import com.dspread.mdm.service.utils.log.Logger

/**
 * 网络事件处理器实现
 * 统一处理所有网络相关的广播事件
 */
class NetworkEventHandlerImpl : NetworkEventHandler {

    private val TAG = "NetworkEventHandler"

    companion object {
        private const val NETWORK_RECOVERY_DEBOUNCE_TIME = 60000L // 1分钟防抖
    }

    // 上次网络恢复上送时间
    @Volatile
    private var lastNetworkRecoveryTime = 0L
    
    override fun getHandlerName(): String = TAG
    
    override fun getSupportedActions(): List<String> {
        return listOf(
            BroadcastActions.ACTION_NETWORK_CHANGED,
            "android.net.wifi.STATE_CHANGE"
        )
    }
    
    override fun handleBroadcast(context: Context, intent: Intent): Boolean {
        val action = intent.action ?: return false
        
        return try {
            when (action) {
                BroadcastActions.ACTION_NETWORK_CHANGED -> {
                    handleNetworkChanged(context)
                    true
                }
                "android.net.wifi.STATE_CHANGE" -> {
                    handleWifiStateChanged(context)
                    true
                }
                else -> false
            }
        } catch (e: Exception) {
            Logger.receiverE("$TAG 处理广播失败: $action", e)
            false
        }
    }
    
    override fun onNetworkChanged(context: Context, isConnected: Boolean, networkType: String) {
        Logger.receiver("网络状态变化: 连接=$isConnected, 类型=$networkType")
        
        if (isConnected) {
            // 网络可用时，检查WebSocket连接状态
            if (!WebSocketCenter.isConnected()) {
                Logger.receiver("网络恢复，尝试重新连接WebSocket")

                // 延迟重连，避免网络刚恢复时立即连接失败
                android.os.Handler(android.os.Looper.getMainLooper()).postDelayed({
                    try {
                        // WebSocket重连逻辑
                    } catch (e: Exception) {
                        Logger.receiverE("网络恢复后重连WebSocket失败", e)
                    }
                }, 3000) // 延迟3秒
            }

            // 网络恢复后重新上传网络状态（包含基站信息）- 添加防抖机制
            android.os.Handler(android.os.Looper.getMainLooper()).postDelayed({
                try {
                    if (WebSocketCenter.isConnected()) {
                        val currentTime = System.currentTimeMillis()

                        // 防抖机制：1分钟内只允许一次网络恢复上送
                        if (currentTime - lastNetworkRecoveryTime >= NETWORK_RECOVERY_DEBOUNCE_TIME) {
                            Logger.receiver("网络恢复，重新上传网络状态信息（防抖通过）")
                            WsMessageSender.uploadNetworkStatus("NETWORK_RECOVERY", forceUpload = false)
                            lastNetworkRecoveryTime = currentTime
                        } else {
                            val remainingTime = NETWORK_RECOVERY_DEBOUNCE_TIME - (currentTime - lastNetworkRecoveryTime)
                            Logger.receiver("网络恢复上送被防抖阻止，剩余时间: ${remainingTime}ms")
                        }
                    }
                } catch (e: Exception) {
                    Logger.receiverE("网络恢复后上传网络状态失败", e)
                }
            }, 15000) // 延迟15秒，确保网络完全稳定

        } else {
            // 网络不可用时，记录状态
            Logger.receiver("网络不可用，WebSocket连接可能受影响")
        }
    }
    
    override fun onWifiStateChanged(context: Context, wifiState: Int, ssid: String?) {
        Logger.receiver("WiFi状态变化: 状态=$wifiState, SSID=$ssid")
        
        // 这里可以添加WiFi状态变化的处理逻辑
        // 例如：记录WiFi连接历史、上报网络状态等
    }
    
    /**
     * 处理网络连接变化
     */
    private fun handleNetworkChanged(context: Context) {
        val isNetworkAvailable = NetworkApi.isNetworkAvailable(context)
        val networkType = getNetworkType(context)
        
        onNetworkChanged(context, isNetworkAvailable, networkType)
    }
    
    /**
     * 处理WiFi状态变化
     */
    private fun handleWifiStateChanged(context: Context) {
        // 获取WiFi状态信息
        val wifiState = getWifiState(context)
        val ssid = getWifiSSID(context)
        
        onWifiStateChanged(context, wifiState, ssid)
    }
    

    
    /**
     * 获取网络类型
     */
    private fun getNetworkType(context: Context): String {
        return try {
            val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
            
            if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.M) {
                val network = connectivityManager.activeNetwork ?: return "NONE"
                val capabilities = connectivityManager.getNetworkCapabilities(network) ?: return "NONE"
                
                when {
                    capabilities.hasTransport(NetworkCapabilities.TRANSPORT_WIFI) -> "WIFI"
                    capabilities.hasTransport(NetworkCapabilities.TRANSPORT_CELLULAR) -> "CELLULAR"
                    capabilities.hasTransport(NetworkCapabilities.TRANSPORT_ETHERNET) -> "ETHERNET"
                    else -> "UNKNOWN"
                }
            } else {
                @Suppress("DEPRECATION")
                val networkInfo = connectivityManager.activeNetworkInfo
                networkInfo?.typeName ?: "NONE"
            }
        } catch (e: Exception) {
            Logger.receiverE("获取网络类型失败", e)
            "ERROR"
        }
    }
    
    /**
     * 获取WiFi状态
     */
    private fun getWifiState(context: Context): Int {
        return try {
            val wifiManager = context.applicationContext.getSystemService(Context.WIFI_SERVICE) as android.net.wifi.WifiManager
            wifiManager.wifiState
        } catch (e: Exception) {
            Logger.receiverE("获取WiFi状态失败", e)
            -1
        }
    }
    
    /**
     * 获取WiFi SSID
     */
    private fun getWifiSSID(context: Context): String? {
        return try {
            val wifiManager = context.applicationContext.getSystemService(Context.WIFI_SERVICE) as android.net.wifi.WifiManager
            val wifiInfo = wifiManager.connectionInfo
            wifiInfo?.ssid?.replace("\"", "")
        } catch (e: Exception) {
            Logger.receiverE("获取WiFi SSID失败", e)
            null
        }
    }
}
