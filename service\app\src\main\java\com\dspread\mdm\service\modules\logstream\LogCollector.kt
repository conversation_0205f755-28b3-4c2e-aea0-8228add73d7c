package com.dspread.mdm.service.modules.logstream

import android.content.Context
import android.os.Environment
import com.dspread.mdm.service.config.LogStreamConfig as LogStreamConfigManager
import com.dspread.mdm.service.utils.log.Logger
import kotlinx.coroutines.*
import java.io.BufferedReader
import java.io.File
import java.io.InputStreamReader
import java.text.SimpleDateFormat
import java.util.*

/**
 * 系统日志收集器
 */
class LogCollector(private val context: Context) {

    /**
     * 文件轮转回调接口
     */
    interface OnFileRotationListener {
        fun onFileRotated()
    }

    private var rotationListener: OnFileRotationListener? = null
    
    companion object {
        private const val TAG = "[LogCollector]"

        // 日志目录配置 - 使用应用专用目录
        private const val LOG_DIR_NAME = "logs"
        private const val UPLOAD_DIR_NAME = "upload"

        // 日志文件配置
        private const val LOG_FILE_PREFIX = "mdm_log_"
        private const val LOG_FILE_EXTENSION = ".log"

        // logcat命令配置
        private const val LOGCAT_BUFFER_SIZE = 8192
    }
    
    // 日志目录 - 使用应用专用外部存储目录
    private val logDirectory: File by lazy {
        val appExternalDir = if (Environment.getExternalStorageState() == Environment.MEDIA_MOUNTED) {
            // 使用 /sdcard/Android/data/com.dspread.mdm.service/files/logs/
            File(context.getExternalFilesDir(null), LOG_DIR_NAME)
        } else {
            // 降级到内部存储
            File(context.filesDir, LOG_DIR_NAME)
        }

        appExternalDir.apply {
            if (!exists()) {
                mkdirs()
                Logger.logStream("$TAG 创建日志目录: ${absolutePath}")
            }
        }
    }
    
    // 上传目录
    private val uploadDirectory: File by lazy {
        File(logDirectory, UPLOAD_DIR_NAME).apply {
            if (!exists()) mkdirs()
        }
    }
    
    // 日期格式化器
    private val dateFormat = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault())
    
    // 当前日志文件
    private var currentLogFile: File? = null
    
    // 当前日志进程
    private var logcatProcess: Process? = null
    
    // 收集任务
    private var collectorJob: Job? = null
    
    // 是否正在收集
    private var isCollecting = false
    
    /**
     * 启动日志收集
     */
    fun start() {
        if (isCollecting) {
            Logger.logStream("$TAG 日志收集器已在运行")
            return
        }
        
        try {
            Logger.logStream("$TAG 启动日志收集器")
            
            // 清除日志缓存
            clearLogCache()
            
            // 关闭已有的logcat进程
            killLogcatProcesses()
            
            // 创建新的日志文件
            createNewLogFile()
            
            // 启动收集任务
            startCollectorJob()
            
            isCollecting = true
            Logger.logStream("$TAG 日志收集器启动成功")
        } catch (e: Exception) {
            Logger.logStreamE("$TAG 启动日志收集器失败", e)
        }
    }
    
    /**
     * 停止日志收集
     * 优化：立即停止收集，确保快速响应停止命令
     */
    fun stop() {
        if (!isCollecting) {
            Logger.logStream("$TAG 日志收集器未运行")
            return
        }

        try {
            Logger.logStream("$TAG 立即停止日志收集器")

            // 立即取消收集任务
            collectorJob?.cancel(CancellationException("日志收集器停止"))
            collectorJob = null

            // 立即关闭logcat进程
            logcatProcess?.destroy()
            logcatProcess = null

            // 立即更新状态
            isCollecting = false
            Logger.logStream("$TAG 日志收集器停止成功")
        } catch (e: Exception) {
            Logger.logStreamE("$TAG 停止日志收集器失败", e)
        }
    }
    
    /**
     * 清除日志缓存
     */
    private fun clearLogCache() {
        try {
            Logger.logStream("$TAG 清除日志缓存")
            Runtime.getRuntime().exec("logcat -c").waitFor()
        } catch (e: Exception) {
            Logger.logStreamE("$TAG 清除日志缓存失败", e)
        }
    }
    
    /**
     * 关闭已有的logcat进程
     */
    private fun killLogcatProcesses() {
        try {
            Logger.logStream("$TAG 关闭已有的logcat进程")
            
            // 获取所有进程
            val process = Runtime.getRuntime().exec("ps")
            val reader = BufferedReader(InputStreamReader(process.inputStream))
            val processInfoList = mutableListOf<String>()
            
            var line: String?
            while (reader.readLine().also { line = it } != null) {
                if (line?.contains("logcat") == true) {
                    processInfoList.add(line!!)
                }
            }
            
            // 关闭logcat进程
            for (processInfo in processInfoList) {
                val parts = processInfo.trim().split("\\s+".toRegex())
                if (parts.size > 1) {
                    val pid = parts[1]
                    Runtime.getRuntime().exec("kill $pid").waitFor()
                    Logger.logStream("$TAG 关闭logcat进程: $pid")
                }
            }
        } catch (e: Exception) {
            Logger.logStreamE("$TAG 关闭logcat进程失败", e)
        }
    }
    
    /**
     * 创建新的日志文件
     */
    private fun createNewLogFile(): File {
        val timestamp = dateFormat.format(Date())
        val logFile = File(logDirectory, "$LOG_FILE_PREFIX$timestamp$LOG_FILE_EXTENSION")
        currentLogFile = logFile
        Logger.logStream("$TAG 创建新日志文件: ${logFile.name}")
        return logFile
    }
    
    /**
     * 启动收集任务
     */
    private fun startCollectorJob() {
        collectorJob = CoroutineScope(Dispatchers.IO).launch {
            try {
                val logFile = currentLogFile ?: createNewLogFile()

                // 构建logcat命令 - 不使用-f参数，改为手动写入文件
                val commandList = mutableListOf<String>()
                commandList.add("logcat")
                commandList.add("-v")
                commandList.add("time")

                // 启动logcat进程
                logcatProcess = Runtime.getRuntime().exec(commandList.toTypedArray())

                // 添加到日志队列
                LogQueue.enqueue(logFile.absolutePath)
                Logger.logStream("$TAG 开始收集日志到文件: ${logFile.name}")

                // 读取logcat输出并写入文件
                logcatProcess?.let { process ->
                    val reader = BufferedReader(InputStreamReader(process.inputStream))
                    logFile.bufferedWriter(Charsets.UTF_8, 8192).use { writer ->
                        var lineCount = 0
                        while (!Thread.currentThread().isInterrupted) {
                            val line = reader.readLine()
                            if (line == null) break

                            writer.write(line)
                            writer.newLine()
                            writer.flush()

                            // 每50行检查一次文件大小，避免频繁IO但保证及时轮转
                            lineCount++
                            if (lineCount % 50 == 0) {
                                if (logFile.length() >= LogStreamConfigManager.getLogFileSize()) {
                                    Logger.logStream("$TAG 日志文件达到大小限制，触发轮转")
                                    break
                                }
                            }
                        }
                    }

                    // 检查是否需要轮转（统一使用>=比较）
                    if (logFile.length() >= LogStreamConfigManager.getLogFileSize()) {
                        Logger.logStream("$TAG 文件大小超限，开始轮转: ${formatSize(logFile.length())}")
                        rotateLogFile()
                    }
                }

                Logger.logStream("$TAG 日志收集任务结束")
            } catch (e: Exception) {
                Logger.logStreamE("$TAG 日志收集任务异常", e)
            }
        }
    }
    
    /**
     * 获取当前日志文件
     */
    fun getCurrentLogFile(): File? {
        // 如果当前日志文件存在且有效，返回它
        if (currentLogFile != null && currentLogFile!!.exists() && currentLogFile!!.length() > 0) {
            return currentLogFile
        }

        // 否则查找最新的日志文件
        return try {
            val logFiles = logDirectory.listFiles { file ->
                file.isFile && file.name.startsWith("mdm_log_") && file.name.endsWith(".log")
            }?.sortedByDescending { it.lastModified() }

            val latestFile = logFiles?.firstOrNull()
            if (latestFile != null && latestFile.exists() && latestFile.length() > 0) {
                Logger.logStream("$TAG 找到最新日志文件: ${latestFile.name}")
                latestFile
            } else {
                Logger.logStreamW("$TAG 没有找到有效的日志文件")
                null
            }
        } catch (e: Exception) {
            Logger.logStreamE("$TAG 获取当前日志文件异常", e)
            null
        }
    }
    
    /**
     * 获取所有日志文件
     */
    fun getAllLogFiles(): List<File> {
        return logDirectory.listFiles { file ->
            file.isFile && file.name.startsWith(LOG_FILE_PREFIX) && file.name.endsWith(
                LOG_FILE_EXTENSION
            )
        }?.toList() ?: emptyList()
    }


    
    /**
     * 检查日志文件大小，如果超过限制则轮转文件
     */
    fun checkLogFileSize() {
        val logFile = currentLogFile
        val maxSize = LogStreamConfigManager.getLogFileSize()

        if (logFile != null && logFile.exists()) {
            val currentSize = logFile.length()

            if (currentSize > maxSize) {
                Logger.logStream("$TAG 日志文件超过大小限制 (${formatSize(currentSize)}/${formatSize(maxSize)})，开始轮转")
                rotateLogFile()
            }
        } else {
            Logger.logStreamW("$TAG 当前日志文件不存在或无效: ${logFile?.absolutePath}")
        }
    }

    /**
     * 轮转日志文件（保持日志流连续性）
     */
    private fun rotateLogFile() {
        try {
            val oldLogFile = currentLogFile
            Logger.logStream("$TAG 开始轮转日志文件: ${oldLogFile?.name}")

            // 创建新文件（但不停止当前logcat进程）
            val newLogFile = createNewLogFile()

            // 将旧文件标记为待压缩
            oldLogFile?.let { file ->
                if (file.exists() && file.length() > 0) {
                    Logger.logStream("$TAG 将文件加入压缩队列: ${file.name} (${formatSize(file.length())})")
                    // 文件已经在队列中，LogStreamManager会自动处理压缩
                }
            }

            // 重新启动收集任务到新文件（不中断logcat进程）
            restartCollectorToNewFile(newLogFile)

            // 通知监听器进行压缩检查
            rotationListener?.onFileRotated()

            Logger.logStream("$TAG 日志文件轮转完成，继续收集到新文件: ${newLogFile.name}")
        } catch (e: Exception) {
            Logger.logStreamE("$TAG 日志文件轮转失败", e)
        }
    }

    /**
     * 重新启动收集任务到新文件（保持logcat进程连续）
     */
    private fun restartCollectorToNewFile(newLogFile: File) {
        try {
            // 取消当前收集任务
            collectorJob?.cancel()

            // 启动新的收集任务
            collectorJob = CoroutineScope(Dispatchers.IO).launch {
                try {
                    // 如果logcat进程不存在，重新创建
                    if (logcatProcess == null) {
                        val commandList = mutableListOf<String>()
                        commandList.add("logcat")
                        commandList.add("-v")
                        commandList.add("time")
                        logcatProcess = Runtime.getRuntime().exec(commandList.toTypedArray())
                        Logger.logStream("$TAG 重新创建logcat进程")
                    }

                    // 添加新文件到日志队列
                    LogQueue.enqueue(newLogFile.absolutePath)
                    Logger.logStream("$TAG 开始收集日志到新文件: ${newLogFile.name}")

                    // 继续读取logcat输出并写入新文件
                    logcatProcess?.let { process ->
                        val reader = BufferedReader(InputStreamReader(process.inputStream))
                        newLogFile.bufferedWriter(Charsets.UTF_8, 8192).use { writer ->
                            var lineCount = 0
                            while (!Thread.currentThread().isInterrupted) {
                                val line = reader.readLine()
                                if (line == null) break

                                writer.write(line)
                                writer.newLine()
                                writer.flush()

                                // 每50行检查一次文件大小
                                lineCount++
                                if (lineCount % 50 == 0) {
                                    if (newLogFile.length() >= LogStreamConfigManager.getLogFileSize()) {
                                        Logger.logStream("$TAG 新日志文件达到大小限制，触发轮转")
                                        break
                                    }
                                }
                            }
                        }

                        // 检查是否需要再次轮转（统一使用>=比较）
                        if (newLogFile.length() >= LogStreamConfigManager.getLogFileSize()) {
                            Logger.logStream("$TAG 新文件大小超限，开始轮转: ${formatSize(newLogFile.length())}")
                            rotateLogFile()
                        }
                    }

                    Logger.logStream("$TAG 日志收集任务结束")
                } catch (e: Exception) {
                    Logger.logStreamE("$TAG 收集日志到新文件失败", e)
                }
            }
        } catch (e: Exception) {
            Logger.logStreamE("$TAG 重新启动收集任务失败", e)
        }
    }

    /**
     * 设置文件轮转监听器
     */
    fun setRotationListener(listener: OnFileRotationListener?) {
        this.rotationListener = listener
    }

    /**
     * 检查是否正在收集日志
     */
    fun isCollecting(): Boolean {
        return isCollecting
    }

    /**
     * 获取日志目录路径
     */
    fun getLogDirectoryPath(): String {
        return logDirectory.absolutePath
    }

    /**
     * 获取日志目录
     */
    fun getLogDir(): File {
        return logDirectory
    }

    /**
     * 获取上传目录路径
     */
    fun getUploadDirectoryPath(): String {
        return uploadDirectory.absolutePath
    }

    /**
     * 检查存储空间并清理旧文件
     */
    fun checkStorageAndCleanup() {
        try {
            Logger.logStream("$TAG 检查存储空间并清理旧文件")

            val maxCompressedStorage = LogStreamConfigManager.getCompressedStorageLimit()
            val maxRawStorage = LogStreamConfigManager.getRawStorageLimit()

            // 检查压缩文件总大小
            val compressedSize = getCompressedFilesTotalSize()
            Logger.logStream("$TAG 当前压缩文件总大小: ${formatSize(compressedSize)} / ${formatSize(maxCompressedStorage)}")

            if (compressedSize > maxCompressedStorage) {
                Logger.logStream("$TAG 压缩文件超过限制，开始清理最旧的文件")
                cleanupOldestCompressedFiles(compressedSize - maxCompressedStorage)
            }

            // 检查原始日志文件总大小
            val rawSize = getRawLogFilesTotalSize()
            Logger.logStream("$TAG 当前原始日志总大小: ${formatSize(rawSize)} / ${formatSize(maxRawStorage)}")

            if (rawSize > maxRawStorage) {
                Logger.logStream("$TAG 原始日志超过限制，开始清理最旧的文件")
                cleanupOldestRawLogFiles(rawSize - maxRawStorage)
            }

        } catch (e: Exception) {
            Logger.logStreamE("$TAG 检查存储空间失败", e)
        }
    }

    /**
     * 获取压缩文件总大小
     */
    private fun getCompressedFilesTotalSize(): Long {
        return try {
            uploadDirectory.listFiles { file ->
                file.isFile && file.name.endsWith(".gz")
            }?.sumOf { it.length() } ?: 0L
        } catch (e: Exception) {
            Logger.logStreamE("$TAG 计算压缩文件大小失败", e)
            0L
        }
    }

    /**
     * 获取原始日志文件总大小
     */
    private fun getRawLogFilesTotalSize(): Long {
        return try {
            logDirectory.listFiles { file ->
                file.isFile && file.name.endsWith(LOG_FILE_EXTENSION)
            }?.sumOf { it.length() } ?: 0L
        } catch (e: Exception) {
            Logger.logStreamE("$TAG 计算原始日志大小失败", e)
            0L
        }
    }

    /**
     * 清理最旧的压缩文件
     */
    private fun cleanupOldestCompressedFiles(sizeToClean: Long) {
        try {
            val compressedFiles = uploadDirectory.listFiles { file ->
                file.isFile && file.name.endsWith(".gz")
            }?.sortedBy { it.lastModified() } ?: return

            var cleanedSize = 0L
            for (file in compressedFiles) {
                if (cleanedSize >= sizeToClean) break

                Logger.logStream("$TAG 删除旧压缩文件: ${file.name}")
                cleanedSize += file.length()
                file.delete()
            }

            Logger.logStream("$TAG 清理压缩文件完成，释放空间: ${cleanedSize / 1024 / 1024}MB")

        } catch (e: Exception) {
            Logger.logStreamE("$TAG 清理压缩文件失败", e)
        }
    }

    /**
     * 清理最旧的原始日志文件
     */
    private fun cleanupOldestRawLogFiles(sizeToClean: Long) {
        try {
            val rawFiles = logDirectory.listFiles { file ->
                file.isFile && file.name.endsWith(LOG_FILE_EXTENSION) && file != currentLogFile
            }?.sortedBy { it.lastModified() } ?: return

            var cleanedSize = 0L
            for (file in rawFiles) {
                if (cleanedSize >= sizeToClean) break

                Logger.logStream("$TAG 删除旧日志文件: ${file.name}")
                cleanedSize += file.length()

                // 从队列中移除
                LogQueue.removeLogFile(file.absolutePath)
                file.delete()
            }

            Logger.logStream("$TAG 清理原始日志完成，释放空间: ${cleanedSize / 1024 / 1024}MB")

        } catch (e: Exception) {
            Logger.logStreamE("$TAG 清理原始日志失败", e)
        }
    }

    /**
     * 格式化文件大小显示
     */
    private fun formatSize(bytes: Long): String {
        return when {
            bytes >= 1024 * 1024 * 1024 -> "${bytes / (1024 * 1024 * 1024)}GB"
            bytes >= 1024 * 1024 -> "${bytes / (1024 * 1024)}MB"
            bytes >= 1024 -> "${bytes / 1024}KB"
            else -> "${bytes}B"
        }
    }
}
