package com.dspread.mdm.service.platform.api.upgrade

import android.content.Context
import android.os.Build
import com.dspread.mdm.service.utils.log.Logger
import com.dspread.mdm.service.platform.api.model.SystemOperationResult
import com.dspread.mdm.service.platform.manager.UpdateEngineManager
import java.io.File

/**
 * UpdateEngine升级策略
 * 适用于Android 14+和支持A/B分区的设备
 * 使用UpdateEngine API进行OTA升级
 */
class UpdateEngineUpgradeStrategy(private val context: Context) {

    companion object {
        private const val TAG = "UpdateEngineUpgradeStrategy"
    }

    private val updateEngineManager = UpdateEngineManager(context)

    /**
     * 更新状态监听器
     */
    interface UpdateStatusListener {
        fun onStatusUpdate(status: String, progress: Int)
        fun onUpdateCompleted(success: Boolean, message: String)
    }

    /**
     * 检查是否支持UpdateEngine升级
     */
    fun isSupported(): Boolean {
        return try {
            // Android 14+ 强制使用UpdateEngine
            if (Build.VERSION.SDK_INT >= 34) {
                Logger.platformI("$TAG Android 14+ 强制支持UpdateEngine")
                return true
            }
            
            // Android 7.0+ 检查A/B分区支持
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                val abUpdate = getSystemProperty("ro.build.ab_update")
                val currentSlot = getSystemProperty("ro.boot.slot_suffix")
                val supported = abUpdate == "true" || currentSlot.isNotEmpty()
                Logger.platformI("$TAG Android 7+ A/B分区支持: $supported (ab_update=$abUpdate, slot=$currentSlot)")
                return supported
            }
            
            Logger.platformI("$TAG Android < 7.0 不支持UpdateEngine")
            false
        } catch (e: Exception) {
            Logger.platformE("$TAG 检查UpdateEngine支持失败", e)
            false
        }
    }

    /**
     * 执行UpdateEngine升级
     */
    fun performUpgrade(
        updateFile: File,
        taskId: String? = null,
        listener: UpdateStatusListener? = null
    ): SystemOperationResult {
        return try {
            Logger.platformI("$TAG 开始UpdateEngine升级: ${updateFile.absolutePath}")
            Logger.platformI("$TAG Android版本: ${Build.VERSION.SDK_INT} (${Build.VERSION.RELEASE})")

            if (!updateFile.exists()) {
                return SystemOperationResult.failure("Update file not found: ${updateFile.absolutePath}")
            }

            // 检查是否为Payload格式
            if (!isPayloadFormat(updateFile)) {
                return SystemOperationResult.failure("UpdateEngine requires payload format OTA package")
            }

            // 处理ZIP包含payload.bin的情况
            val payloadFile = if (updateFile.name.endsWith(".zip")) {
                Logger.platformI("$TAG 检测到ZIP格式，提取payload.bin")
                extractPayloadFromZip(updateFile)
            } else {
                updateFile
            }

            if (payloadFile == null) {
                return SystemOperationResult.failure("Failed to extract payload.bin from ZIP")
            }

            // 复制到系统可访问位置
            val systemFile = copyToSystemLocation(payloadFile)
            if (systemFile == null) {
                return SystemOperationResult.failure("Failed to copy payload to system location")
            }

            // 创建UpdateEngine监听器适配器
            val engineListener = listener?.let { originalListener ->
                object : UpdateEngineManager.UpdateStatusListener {
                    override fun onStatusUpdate(status: Int, percent: Float) {
                        val statusName = getUpdateEngineStatusName(status)
                        originalListener.onStatusUpdate(statusName, percent.toInt())
                        Logger.platformI("$TAG UpdateEngine状态: $statusName ($status), 进度: ${percent.toInt()}%")
                    }

                    override fun onUpdateCompleted(success: Boolean) {
                        val message = if (success) {
                            "UpdateEngine升级完成，设备将重启"
                        } else {
                            "UpdateEngine升级失败"
                        }
                        originalListener.onUpdateCompleted(success, message)
                        Logger.platformI("$TAG UpdateEngine升级完成: ${if (success) "成功" else "失败"}")
                        
                        if (success && Build.VERSION.SDK_INT >= 34) {
                            // Android 14+ 需要手动重启
                            scheduleReboot()
                        }
                    }
                }
            }

            // 执行升级
            val result = updateEngineManager.applyPayload(systemFile, engineListener)
            
            if (result.isSuccess) {
                Logger.platformI("$TAG UpdateEngine升级已启动")
                SystemOperationResult.success("UpdateEngine upgrade started")
            } else {
                Logger.platformE("$TAG UpdateEngine升级启动失败")
                result
            }

        } catch (e: Exception) {
            Logger.platformE("$TAG UpdateEngine升级异常", e)
            SystemOperationResult.failure("UpdateEngine upgrade failed: ${e.message}")
        }
    }

    /**
     * 检查是否为Payload格式
     */
    private fun isPayloadFormat(file: File): Boolean {
        return try {
            val buffer = ByteArray(4)
            file.inputStream().use { input ->
                input.read(buffer)
            }

            val magic = String(buffer, Charsets.US_ASCII)
            val isDirectPayload = magic == "CrAU"
            
            // 检查ZIP是否包含payload.bin
            val containsPayload = if (!isDirectPayload && magic == "PK\u0003\u0004") {
                checkZipContainsPayload(file)
            } else {
                false
            }

            val isPayload = isDirectPayload || containsPayload
            Logger.platformI("$TAG OTA包格式检查: ${if (isPayload) "Payload格式" else "ZIP格式"}")
            isPayload

        } catch (e: Exception) {
            Logger.platformW("$TAG 检查OTA包格式失败: ${e.message}")
            false
        }
    }

    /**
     * 检查ZIP是否包含payload.bin
     */
    private fun checkZipContainsPayload(zipFile: File): Boolean {
        return try {
            java.util.zip.ZipFile(zipFile).use { zip ->
                val hasPayload = zip.getEntry("payload.bin") != null
                val hasProperties = zip.getEntry("payload_properties.txt") != null
                hasPayload && hasProperties
            }
        } catch (e: Exception) {
            false
        }
    }

    /**
     * 从ZIP提取payload.bin和payload_properties.txt
     */
    private fun extractPayloadFromZip(zipFile: File): File? {
        return try {
            val tempDir = File(context.cacheDir, "ota_extract")
            if (!tempDir.exists()) {
                tempDir.mkdirs()
            }

            val payloadFile = File(tempDir, "payload.bin")
            val propertiesFile = File(tempDir, "payload_properties.txt")

            java.util.zip.ZipFile(zipFile).use { zip ->
                // 提取payload.bin
                val payloadEntry = zip.getEntry("payload.bin")
                if (payloadEntry == null) {
                    Logger.platformE("$TAG ZIP中未找到payload.bin")
                    return null
                }

                zip.getInputStream(payloadEntry).use { input ->
                    payloadFile.outputStream().use { output ->
                        input.copyTo(output)
                    }
                }
                Logger.platformI("$TAG 成功提取payload.bin: ${payloadFile.absolutePath}")

                // 提取payload_properties.txt（如果存在）
                val propertiesEntry = zip.getEntry("payload_properties.txt")
                if (propertiesEntry != null) {
                    zip.getInputStream(propertiesEntry).use { input ->
                        propertiesFile.outputStream().use { output ->
                            input.copyTo(output)
                        }
                    }
                    Logger.platformI("$TAG 成功提取payload_properties.txt: ${propertiesFile.absolutePath}")
                } else {
                    Logger.platformW("$TAG ZIP中未找到payload_properties.txt，UpdateEngine将使用默认参数")
                }
            }

            payloadFile

        } catch (e: Exception) {
            Logger.platformE("$TAG 提取payload文件失败", e)
            null
        }
    }

    /**
     * 复制到系统可访问位置
     */
    private fun copyToSystemLocation(sourceFile: File): File? {
        return try {
            val systemLocations = listOf(
                "/data/ota_package",
                "/cache",
                "/data/local/tmp"
            )

            for (systemDir in systemLocations) {
                try {
                    val systemDirFile = File(systemDir)
                    if (!systemDirFile.exists()) {
                        systemDirFile.mkdirs()
                    }

                    val targetFile = File(systemDir, sourceFile.name)
                    sourceFile.copyTo(targetFile, overwrite = true)

                    // 设置权限
                    Runtime.getRuntime().exec("chmod 644 ${targetFile.absolutePath}").waitFor()
                    
                    Logger.platformI("$TAG 文件复制成功: ${targetFile.absolutePath}")
                    return targetFile

                } catch (e: Exception) {
                    Logger.platformW("$TAG 复制到 $systemDir 失败: ${e.message}")
                    continue
                }
            }

            Logger.platformE("$TAG 所有系统位置都无法访问")
            null

        } catch (e: Exception) {
            Logger.platformE("$TAG 复制文件失败", e)
            null
        }
    }

    /**
     * 获取系统属性
     */
    private fun getSystemProperty(key: String): String {
        return try {
            val process = Runtime.getRuntime().exec("getprop $key")
            process.inputStream.bufferedReader().readText().trim()
        } catch (e: Exception) {
            ""
        }
    }

    /**
     * 获取UpdateEngine状态名称
     */
    private fun getUpdateEngineStatusName(status: Int): String {
        return when (status) {
            0 -> "IDLE"
            1 -> "CHECKING_FOR_UPDATE"
            2 -> "UPDATE_AVAILABLE"
            3 -> "DOWNLOADING"
            4 -> "VERIFYING"
            5 -> "FINALIZING"
            6 -> "UPDATED_NEED_REBOOT"
            7 -> "REPORTING_ERROR_EVENT"
            8 -> "ATTEMPTING_ROLLBACK"
            9 -> "DISABLED"
            else -> "UNKNOWN($status)"
        }
    }

    /**
     * 安排重启
     */
    private fun scheduleReboot() {
        try {
            Logger.platformI("$TAG 安排5秒后自动重启")
            Thread {
                try {
                    Thread.sleep(5000)
                    Logger.platformI("$TAG 开始执行重启...")

                    // 使用现有的SystemControlApi进行重启
                    val systemControlApi = com.dspread.mdm.service.platform.api.system.SystemControlApi(context)
                    val result = systemControlApi.reboot("UpdateEngine升级完成")

                    if (result.isSuccess) {
                        Logger.platformI("$TAG 重启成功")
                    } else {
                        Logger.platformE("$TAG 重启失败: ${result}")
                    }

                } catch (e: Exception) {
                    Logger.platformE("$TAG 自动重启失败", e)
                }
            }.start()
        } catch (e: Exception) {
            Logger.platformE("$TAG 安排重启失败", e)
        }
    }

    /**
     * 释放资源
     */
    fun release() {
        try {
            updateEngineManager.release()
        } catch (e: Exception) {
            Logger.platformW("$TAG 释放资源失败: ${e.message}")
        }
    }
}
