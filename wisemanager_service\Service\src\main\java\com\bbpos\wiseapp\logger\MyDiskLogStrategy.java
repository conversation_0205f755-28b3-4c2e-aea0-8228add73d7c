package com.bbpos.wiseapp.logger;

import android.os.Handler;
import android.os.Looper;
import android.os.Message;
import android.text.TextUtils;
import android.util.Log;

import com.bbpos.wiseapp.ZipJava;
import com.bbpos.wiseapp.service.common.UsualData;
import com.bbpos.wiseapp.system.api.SystemApi;
import com.bbpos.wiseapp.tms.listener.device.HardwareInfo;
import com.bbpos.wiseapp.tms.utils.Constants;
import com.bbpos.wiseapp.tms.utils.ContextUtil;
import com.bbpos.wiseapp.utils.FileUtils;
import com.bbpos.wiseapp.utils.HttpUtils;
import com.bbpos.wiseapp.system.api.DeviceInfoApi;
import com.bbpos.wiseapp.utils.SharedPreferencesUtils;
import com.bbpos.wiseapp.zip.GzipJava;
import com.orhanobut.logger.LogStrategy;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.TimeZone;

/**
 * Abstract class that takes care of background threading the file log operation on Android.
 * implementing classes are free to directly perform I/O operations there.
 * 光升修改
 */
public class MyDiskLogStrategy implements LogStrategy {

  private final Handler handler;
  private static final int FLUSH_MEM_LOG = 998;
  private static LLQueue<String> mLogQueue = new LLQueue<>();
  private static int DEF_QUEUE_MAX_SIZE = 1000; //最大行数,可调

  private static StringBuffer mStringBuf;

  public static SimpleDateFormat sdfshort = new SimpleDateFormat("yyyyMMdd");//日志名称格式
  public static SimpleDateFormat sdftshort = new SimpleDateFormat("yyyyMMdd_HHmmss");//日志名称格式

  private static boolean b_enable_save_log = true;
  public static boolean b_enable_upload_log = true;

  public MyDiskLogStrategy(Handler handler) {
    this.handler = handler;
  }

  @Override
  public void log(int level, String tag, String message) {
    // do nothing on the calling thread, simply pass the tag/msg to the background thread
    if (b_enable_save_log) {
      handler.sendMessage(handler.obtainMessage(level, message));
    }
  }

  public void flushMemoryLog(){
    handler.sendMessage(handler.obtainMessage(FLUSH_MEM_LOG, null));
  }

  public static class WriteHandler extends Handler {

    private final String folder;
    private final int maxFileSize;

    WriteHandler(Looper looper, String folder, int maxFileSize) {
      super(looper);
      this.folder = folder;
      this.maxFileSize = maxFileSize;
    }

    @SuppressWarnings("checkstyle:emptyblock")
    @Override
    public void handleMessage(Message msg) {
      int level = msg.what;
      String content = (String) msg.obj;

      /*
      缓存写入磁盘,
    1)缓存将满时写(判断是否大于DEF_QUEUE_MAX_SIZE)
    2)定时写,(在AlarmReceiver里)
    3)退出程序之前写，(在onTrimMemory及onTerminate里)
    4)crash之前写(在crash handler里)
       */

      switch (level){
        case FLUSH_MEM_LOG: //save memory cache to log file
          appendQueueToFileNew();
          break;
        default:            //save to memory cache
          mLogQueue.enQueue(content);
          if(mLogQueue.queueLength()>=DEF_QUEUE_MAX_SIZE){
            appendQueueToFileNew();
          }
          break;
      }
    }

    @Deprecated
    private void appendToFile(String content){
      FileWriter fileWriter = null;
      File logFile = getLogFileNew(folder, "logs");

      try {
        fileWriter = new FileWriter(logFile, true);

        writeLog(fileWriter, content);

        fileWriter.flush();
        fileWriter.close();
      } catch (IOException e) {
        if (fileWriter != null) {
          try {
            fileWriter.flush();
            fileWriter.close();
          } catch (IOException e1) { /* fail silently */ }
        }
      }
    }

    private synchronized void appendQueueToFileNew(){
      FileWriter fileWriter = null;
      File logFile = getLogFileNew(folder, "logs");
      StringBuffer stringBuffer = new StringBuffer();
      try {
        fileWriter = new FileWriter(logFile, true);

        int size = mLogQueue.queueLength();
        for(int i=0; i<size; i++) { //写入size个,可能在写期间不断有新的项写入,不理会,待下个时机再写入
          String line = mLogQueue.deQueue();
          if(line==null) {//队列异常取空了
            break;
          }
          stringBuffer.append(line);
          writeLog(fileWriter, line);
        }

//        mStringBuf = stringBuffer;
//        new Thread(new Runnable() {
//          @Override
//          public void run() {
//            String logURL = "https://57r5mjzooa.execute-api.eu-west-1.amazonaws.com/default/WiseLauncherLogger";
//            JSONObject jsonObject = new JSONObject();
//            try {
//              jsonObject.put("SN", DeviceInfoApi.getIntance().getSerialNumber());
//              jsonObject.put("content", mStringBuf.toString());
//            } catch (JSONException e) {
//              e.printStackTrace();
//            }
//            HttpUtils.connectPost(logURL, jsonObject.toString());
//          }
//        }).start();
        fileWriter.flush();
        fileWriter.close();
      } catch (Exception e) {
        if (fileWriter != null) {
          try {
            fileWriter.flush();
            fileWriter.close();
          } catch (IOException e1) { /* fail silently */ }
        }
      }
    }

    /**
     * This is always called on a single background thread. Implementing classes must ONLY write
     * to the fileWriter and nothing more. The abstract class takes care of everything else
     * including close the stream and catching IOException
     *
     * @param fileWriter an instance of FileWriter already initialised to the correct file
     */
    private void writeLog(FileWriter fileWriter, String content) throws IOException {
      fileWriter.append(content);
    }

    private File getLogFile(String folderName, String fileName) {

      File folder = new File(folderName);
      if (!folder.exists()) {
        //TODO: What if logFolder is not created, what happens then?
        folder.mkdirs();
      }

      int newFileCount = 0;
      File newFile;
      File existingFile = null;

      newFile = new File(folder, String.format("%s_%s.csv", fileName, newFileCount));
      while (newFile.exists()) {
        existingFile = newFile;
        newFileCount++;
        newFile = new File(folder, String.format("%s_%s.csv", fileName, newFileCount));
      }

      if (existingFile != null) {
        if (existingFile.length() >= maxFileSize) {
          return newFile;
        }
        return existingFile;
      }

      return newFile;
    }

    /**
     * 得到应写的文件实例
     * @param folderName
     * @param fileNamePrefix 前缀,例如logs
     * @return
     */
    private File getLogFileNew(String folderName, String fileNamePrefix) {
      /*
       * 如果有时间最新的本日的文件，且不超长，就继续写
       * 如果有时间最新的本日的文件，但超长，则新建一个
       * 如果没有本日的文件，就新建一个
       */

      //保证目录一定要存在如果不存在新建
      File folder = new File(folderName);
      if (!folder.exists()) {
        //TODO: What if folder is not created, what happens then?
        folder.mkdirs();
      }

      File latestFile = getLatestLogFile(folder);

      File wantedFile;
      if(latestFile==null){//没找到就新建一个
        sdftshort.setTimeZone(TimeZone.getDefault());
        wantedFile = new File(folder, String.format("%s.log", sdftshort.format(new Date())));
        return wantedFile;
      }else{
        if(latestFile.length()>=maxFileSize){
//          ContextUtil.saveUploadLog(latestFile.getAbsolutePath());
          try {
            sdftshort.setTimeZone(TimeZone.getDefault());
            Date laestFileDate = sdftshort.parse(getFileNameWithoutExtension(latestFile.getName()));
            if (laestFileDate.after(new Date())) {
              wantedFile = latestFile;
            } else {
              wantedFile = new File(folder, String.format("%s.log", sdftshort.format(new Date())));
            }
          } catch (ParseException e) {
            e.printStackTrace();
            wantedFile = new File(folder, String.format("%s.log", sdftshort.format(new Date())));
          }
        }else{
          wantedFile = latestFile;
        }
      }

      return wantedFile;

    }

    /**
     * 找到今天日期开头的最新的
     * @param folder
     * @return
     */
    private File getTodayLatestLogFile(File folder){
      File latestFile;

      List<File> list = new ArrayList<>();
      for(File file:folder.listFiles()){
        if(file.getName().startsWith(sdfshort.format(new Date()))){  //找到以今天日期开头的最新的
          list.add(file);
        }
      }
      Collections.sort(list, new FileComparatorAsc());

      if(list!=null && !list.isEmpty()){
        latestFile = list.get(list.size()-1);
      }else{
        latestFile = null;
      }
      //BBLog.v("MyDislLogStrategy", "latestFile=" + latestFile);
      return latestFile;
    }

    /**
     * 找到今天日期开头的最新的
     * @param folder
     * @return
     */
    private File getLatestLogFile(File folder){
      File latestFile;

      List<File> list = new ArrayList<>();
      for(File file:folder.listFiles()){
        try {
//          BBLog.e(BBLog.TAG, "getLatestLogFile: " + getFileNameWithoutExtension(file.getName()));
          sdftshort.setLenient(false);
          sdftshort.setTimeZone(TimeZone.getDefault());
          sdftshort.parse(getFileNameWithoutExtension(file.getName()));   //如果时间格式正确就是日志文件
          if (file.getName().endsWith(".log")) {
            list.add(file);
          }
        } catch (Exception e) {
          e.printStackTrace();
          BBLog.e(BBLog.TAG, getFileNameWithoutExtension(file.getName()) + "解析错误");
          file.delete();
        }
      }
      Collections.sort(list, new FileComparatorAsc());

      if(list!=null && !list.isEmpty()){
        latestFile = list.get(list.size()-1);
      }else{
        latestFile = null;
      }
      BBLog.v("MyDislLogStrategy", "latestFile=" + latestFile);
      return latestFile;
    }

    class FileComparatorAsc implements Comparator<File> {
      public int compare(File file1, File file2) {
        String createInfo1 = getFileNameWithoutExtension(file1.getName());
        String createInfo2 = getFileNameWithoutExtension(file2.getName());

        try {
          sdftshort.setTimeZone(TimeZone.getDefault());
          Date create1 = sdftshort.parse(createInfo1);
          Date create2 = sdftshort.parse(createInfo2);
          if(create1.before(create2)){//按升序排列
            return -1;
          }else{
            return 1;
          }
        } catch (ParseException e) {
          return 0;
        }
      }
    }
    /**
     * 去除文件的扩展类型（.log）
     * @param fileName
     * @return
     */
    public static String getFileNameWithoutExtension(String fileName){
      if (fileName.contains(".")) {
        return fileName.substring(0, fileName.indexOf("."));
      }

      return fileName;
    }
    /**
     * 删除旧的日志文件
     */
    private void clearOldLogFiles() {

    }

  }

  public static void enableSaveLoggerFile(boolean able) {
    BBLog.e(BBLog.TAG, "设置WiseLog保存开关 = " + able);
    b_enable_save_log = true;
    b_enable_upload_log = able;
  }

  public static void uploadLoggerFile() {
    BBLog.e(BBLog.TAG, "开始检查上送: ");
    try {
      File folder = new File(LoggerConfig.logFolder + File.separator);
      if (!folder.exists()) {
        return;
      }

      int length = folder.listFiles().length;
      BBLog.e(BBLog.TAG, "Logger 开始检查上送，当前Log条数：" + length);
      if (length == 0) {
        return;
      }

      SimpleDateFormat sdft_short = new SimpleDateFormat("yyyyMMdd_HHmmss");//日志名称格式
      List<File> list = new ArrayList<>();
      for(File file:folder.listFiles()){
        try {
//          BBLog.e(BBLog.TAG, "uploadLoggerFile 找到文件: " + MyDiskLogStrategy.WriteHandler.getFileNameWithoutExtension(file.getName()));
          sdft_short.setLenient(false);
          sdft_short.parse(MyDiskLogStrategy.WriteHandler.getFileNameWithoutExtension(file.getName()));   //如果时间格式正确就是日志文件
          if (file.getName().endsWith(".log")) {
            list.add(file);
          }
        } catch (Exception e) {
          e.printStackTrace();
          BBLog.e(BBLog.TAG, MyDiskLogStrategy.WriteHandler.getFileNameWithoutExtension(file.getName()) + "解析错误");
          file.delete();
        }
      }
      Collections.sort(list, new Comparator<File>() {
        @Override
        public int compare(File file1, File file2) {
          String createInfo1 = MyDiskLogStrategy.WriteHandler.getFileNameWithoutExtension(file1.getName());
          String createInfo2 = MyDiskLogStrategy.WriteHandler.getFileNameWithoutExtension(file2.getName());
          try {
            Date create1 = sdft_short.parse(createInfo1);
            Date create2 = sdft_short.parse(createInfo2);
            if(create1.before(create2)){//按升序排列
              return -1;
            }else{
              return 1;
            }
          } catch (ParseException e) {
            return 0;
          }
        }

        @Override
        public boolean equals(Object obj) {
          return false;
        }
      });

      File fileUpload = list.get(0);
      //只有一个文件的时候，是当前在写入的文件，此时必须满足500KB以上大小，才能上传
      if (list.size() == 1) {
        if (fileUpload.length() < MyCsvFormatStrategy.Builder.MAX_BYTES) {
          return;
        }
      } else {
        //如果多条，如果有存在文件时间晚于当前的时间的，先上送最晚的
        String createInfo = MyDiskLogStrategy.WriteHandler.getFileNameWithoutExtension(list.get(list.size() - 1).getName());
        try {
          Date createDate = sdft_short.parse(createInfo);
          if (createDate.after(new Date())) {
            fileUpload = list.get(list.size() - 1);
          }
        } catch (ParseException e) {
          e.printStackTrace();
        }
      }
      BBLog.e(BBLog.TAG, "uploadLoggerFile 找到最早的要上送的文件: " + fileUpload.getName());
      String filePath = fileUpload.getAbsolutePath();
      if (fileUpload.exists()) {
        File finalFileUpload = fileUpload;
        new Thread(new Runnable() {
          @Override
          public void run() {
            try {
				Long dirSize = FileUtils.getTotalSizeOfFilesInDir(folder);
				BBLog.e(BBLog.TAG, "Logger  當前Log文件夾总大小： "+ dirSize );
				if (dirSize >= Constants.WISEAPP_LOG_FILE_MAX_SIZE){
					BBLog.i(BBLog.TAG, "Logger  當前Log文件夾大小超出3G,清空log文件夹 ");
					SystemApi.execCommand("rm -rf /sdcard/wiseapp/log/");
					return;
				}

              String gzName = filePath.substring(0, filePath.lastIndexOf(".")) + ".gz";
              File fileZip = new File(gzName);
              BBLog.i(BBLog.TAG, "WiseApp 日志壓縮成文件 " + gzName);
              GzipJava.compressGZIP(finalFileUpload, fileZip);

              for (int i=0; i<1; i++) {
                String mode = SharedPreferencesUtils.getSharePreferencesValue(UsualData.SHARED_PREFERENCES_MODE_IN_CONFIG, "uat");
                String sn = DeviceInfoApi.getIntance().getSerialNumber();
                if (b_enable_upload_log && HttpUtils.urlUploadFile(HttpUtils.S3_LOG_STREAMING_URL, HttpUtils.S3_LOG_STREAMING_BUCKET_NAME, "wiseapp/" + mode + "/" + sn, fileZip)) {
                  if (fileZip != null) {
                    fileZip.delete();
                  }
                  if (finalFileUpload != null) {
                    finalFileUpload.delete();
                  }
                  BBLog.w(BBLog.TAG, "Logger " + finalFileUpload.getAbsolutePath() + " 上送成功");
                  break;
                } else {
                  BBLog.w(BBLog.TAG, "Logger " + finalFileUpload.getAbsolutePath() + " 上送失败");
                  if (dirSize > MyCsvFormatStrategy.Builder.MAX_BYTES * 256 ){
					  BBLog.e(BBLog.TAG, "Logger " + folder.getAbsolutePath() + " 占用空间: "+dirSize+ " ,大于128M，需清理最早日志文件 ");
					  if (fileZip != null) {
						  fileZip.delete();
						  BBLog.w(BBLog.TAG, "Logger delete file ---> " + fileZip.getAbsolutePath());
					  }
					  if (finalFileUpload != null) {
						  finalFileUpload.delete();
						  BBLog.w(BBLog.TAG, "Logger delete file ---> " + finalFileUpload.getAbsolutePath());
					  }
				  }
                }
              }

            } catch (Exception e) {
              e.printStackTrace();
            }
          }
        }).start();
      } else {
        BBLog.w(BBLog.TAG, "Logger " + fileUpload.getAbsolutePath() + "文件不存在");
      }
    } catch (Exception e) {
      e.printStackTrace();
    }
  }

  public static void zipUploadWiseLog() {
    new Thread(new Runnable() {
      @Override
      public void run() {
        try {
          String destFilePath = FileUtils.getWiseAppConfigPath() + File.separator + "WiseLog" + sdftshort.format(new Date()) + ".gz";
          File zipFile = new File(destFilePath);
          if (zipFile.exists()) {
            zipFile.delete();
          }
          ZipJava.zip(LoggerConfig.logFolder, destFilePath);
          String mode = SharedPreferencesUtils.getSharePreferencesValue(UsualData.SHARED_PREFERENCES_MODE_IN_CONFIG, "uat");
          String sn = DeviceInfoApi.getIntance().getSerialNumber();
          for (int i=0; i<3; i++) {
            if (HttpUtils.urlUploadFile(HttpUtils.S3_LOG_STREAMING_URL, HttpUtils.S3_LOG_STREAMING_BUCKET_NAME, "wiseapp/" + mode + "/" + sn, new File(destFilePath))) {
              FileUtils.delAllFile(LoggerConfig.logFolder);
              break;
            }
          }
          new File(destFilePath).delete();
        } catch (IOException e) {
          e.printStackTrace();
        }
      }
    }).start();
  }
}