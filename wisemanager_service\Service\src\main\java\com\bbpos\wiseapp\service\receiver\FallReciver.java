package com.bbpos.wiseapp.service.receiver;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.hardware.Sensor;
import android.hardware.SensorEvent;
import android.hardware.SensorEventListener;
import android.hardware.SensorManager;
import android.media.AudioManager;
import android.media.ToneGenerator;
import android.os.Bundle;
import android.os.Handler;
import android.os.Message;
import android.provider.Settings;
import android.util.Log;

import com.bbpos.wiseapp.logger.BBLog;
import com.bbpos.wiseapp.service.common.UsualData;
import com.bbpos.wiseapp.utils.FileUtils;
import com.bbpos.wiseapp.utils.SharedPreferencesUtils;
import com.bbpos.wiseapp.websocket.WebSocketCenter;
import com.bbpos.wiseapp.websocket.WebSocketSender;

import java.util.Date;

public class FallReciver extends BroadcastReceiver {
    private final static String TAG = BBLog.TAG;//"FallReciver";
    private Context mContext;

    private SensorManager mSensorManager;
    private Sensor mSensor;
    private FileUtils mFileUtils;

    public final static String ACTION_FALL_FREE = "kphone.intent.action.FALL_FREE";
    private final static String EXTRA_STATE = "status";
    private final static String FALL = "fall";
    private final static String FREE = "free";

    private final static int MSG_FALL_FREE = 10010;
    private final static int MSG_SENSOR_RECODER = 10011;

    private final static long FALL_FREE_DETECT_STOP_DELAY_TIME = 3000;
    private final static long FALL_FREE_DETECT_TIME_INTERVAL = 5000; //前后收到自由落体广播间隔大于5秒才予以统计

    private final static int FREE_ACCELERATION_STANDARD_LIMIT = 9;
    private final static int FREE_ACCELERATION_COUNT_BASIC_LIMIT = 0;
    private final static int FREE_ACCELERATION_COUNT_LIMIT = 70;
    private final static int FALL_ACCELERATION_COUNT_LIMIT = 15;
    private final static double FALL_FREE_WHOLE_ACCELERATION_VALUE_LIMIT = 50;
    private final static double FALL_FREE_1_POINT_5_METRE_TIME_LIMIT_MS = 380; //正常是553ms，7MD统计有时延

    private static long mFallLastDetectTime = 0;
    private static long mFallNewestDetectTime = 0;
    private static boolean mFallDetectValid = false;
    private long mFallDetectTimeDValue = 0;

    private static long mFreeLastDetectTime = 0;
    private static long mFreeNewestDetectTime = 0;
    private static boolean mFreeDetectValid = false;
    private long mFreeDetectTimeDValue = 0;

    private boolean mFreeFirstDetectedFlag = false;
    private long mFreeFirstDetectedTime = 0;
    private long mFreeFirstDetectedTimeValid = 0;
    private long mFreeSecondDetectedTime = 0;
    private int mFreeAccelerationCount = 0;
    private int mFreeAccelerationCountValid = 0;
    private boolean mFreeValid = false;
    private static int mFreeCount = 0;

    private boolean mFallToGroundValid = true;
    private long mFallToGroundTime = 0;
    private int mFallAccelerationCount = 0;

    private int mFallFreeCount = 0;

    private int lastFlag = 0;
    private int switchCount = 0;
    private int lineCount = 0;

    private boolean DEBUG = true;

    private StringBuffer mGSensorLogContent = new StringBuffer("");

    private SensorEventListener mSensorEventListener = new SensorEventListener() {
        @Override
        public void onAccuracyChanged(Sensor arg0, int arg1) {
            // TODO Auto-generated method stub
        }

        @Override
        public void onSensorChanged(SensorEvent event) {
            // TODO Auto-generated method stub
            float x_acceleration, y_acceleration, z_acceleration, whole_acceleration;
            x_acceleration = event.values[SensorManager.DATA_X];
            y_acceleration = event.values[SensorManager.DATA_Y];
            z_acceleration = event.values[SensorManager.DATA_Z];

            float x_square_acceleration = Math.abs(x_acceleration) * Math.abs(x_acceleration);
            float y_square_acceleration = Math.abs(y_acceleration) * Math.abs(y_acceleration);
            float z_square_acceleration = Math.abs(z_acceleration) * Math.abs(z_acceleration);
            whole_acceleration = x_square_acceleration + y_square_acceleration + z_square_acceleration;
            lineCount ++;
            if (!mFallToGroundValid) {
                if ((whole_acceleration > 0) && (whole_acceleration < FALL_FREE_WHOLE_ACCELERATION_VALUE_LIMIT)) {
                    if (lastFlag != 1) {
                        lastFlag = 1;
                    }
                    if (!mFreeFirstDetectedFlag) {
                        mFreeFirstDetectedTimeValid = mFreeFirstDetectedTime;
//                        if (lineCount == 1) {
//                            mFreeFirstDetectedTimeValid = mFreeFirstDetectedTime;
//                        } else {
//                            mFreeFirstDetectedTimeValid = new Date().getTime();
//                        }
                        mFreeFirstDetectedFlag = true;
//                        if (lineCount > 25) {
//                            mFallToGroundValid = true;
//                            if (DEBUG) {
//                                BBLog.e(TAG, "检测到设备是 < 1.5米落地，第一次检测到 < 50的行数为" + lineCount + "，> 25行直接判为落地");
//                            }
//                        }
                    }
                    if (mFreeAccelerationCount == FREE_ACCELERATION_COUNT_BASIC_LIMIT) {//至少连续10次都<9才出在自由落体过程
                        mFreeValid = true;
                    }
                    mFreeAccelerationCount++;//自由落体总行数
                    if (whole_acceleration < FREE_ACCELERATION_STANDARD_LIMIT) {//<9
                        mFreeAccelerationCountValid ++;//自由落体有效行数
                    }
                } else {
                    if (mFreeValid) {
                        if (lastFlag != 2) {
                            lastFlag = 2;
                            switchCount ++;
                            mFallToGroundTime = new Date().getTime();
                            if (DEBUG && mFallAccelerationCount != 0) {
                                BBLog.d(TAG, "检测到第" + switchCount + "误差，时间为" + mFallToGroundTime);
                            }
                        }
//                        if (mFallAccelerationCount >= 5 || switchCount == 2) {
                        if (mFallAccelerationCount >= FALL_ACCELERATION_COUNT_LIMIT) {
                            long fall_free_use_time = mFallToGroundTime - mFreeFirstDetectedTimeValid;
                            if (DEBUG) {
                                BBLog.e(TAG, "检测到设备自由落体" +
                                        "开始时间：" + mFileUtils.mSimpleDateFormat.format(new Date(mFreeFirstDetectedTimeValid)) + "，" +
                                        "落地时间：" + mFileUtils.mSimpleDateFormat.format(new Date(mFallToGroundTime)) + "，" +
                                        "有效时延：" + fall_free_use_time + "，" +
                                        "自由落体总行数：" + mFreeAccelerationCount + "，" +
                                        "自由落体有效行数：" + mFreeAccelerationCountValid);
                            }
                            mFallAccelerationCount = 0;
                            if ((lineCount > FREE_ACCELERATION_COUNT_LIMIT) && (fall_free_use_time > FALL_FREE_1_POINT_5_METRE_TIME_LIMIT_MS)) {
                                if (DEBUG) {
                                    BBLog.e(TAG, "检测到设备是 >=1.5米落地，计数加1。");
                                }
                                mFallFreeCount ++;
                                if (WebSocketCenter.isWebSocketConnected) {
                                    WebSocketSender.C0202_DeviceEventUpload("4", "1", null);
                                } else {
                                    if (DEBUG) {
                                        BBLog.e(TAG, "检测到有效跌落，但没有网络");
                                    }
                                    int fallCount = Integer.parseInt(SharedPreferencesUtils.getSharePreferencesValue(UsualData.SHARED_PREFERENCES_FALL_COUNT, "0")) + 1;
                                    SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_FALL_COUNT, String.format("%d", fallCount));
                                }
                            } else {
                                if (DEBUG) {
                                    BBLog.e(TAG, "检测到设备是 < 1.5米落地。");
                                }
                            }
                            mFreeValid = false;
                            mFallToGroundValid = true;

                            lineCount = 0;
                        }

                        mFallAccelerationCount ++;
                        if (DEBUG && (mFallAccelerationCount == 1)) {
                            BBLog.d(TAG, "time: " + mFileUtils.mSimpleDateFormat.format(new Date(mFallToGroundTime)) + "，" +
                                    "X: " + String.format("%06f", x_square_acceleration) + "，" +
                                    "Y: " + String.format("%06f", y_square_acceleration) + "，" +
                                    "Z: " + String.format("%06f", z_square_acceleration) + "，" +
                                    "A: " + String.format("%06f", whole_acceleration));
                        }
                    }
                }
            }
//            BBLog.i(TAG, mFileUtils.mSimpleDateFormat.format(new Date()) + ": " +
//                    x_square_acceleration + ", " +
//                    y_square_acceleration + ", " +
//                    z_square_acceleration + ", " +
//                    whole_acceleration);
            mGSensorLogContent.append(mFileUtils.mSimpleDateFormat.format(new Date()) + ": " +
                    x_square_acceleration + ", " +
                    y_square_acceleration + ", " +
                    z_square_acceleration + ", " +
                    whole_acceleration +
                    "\r\n");
        }
    };

    public FallReciver() {
        // TODO Auto-generated constructor stub
        mFileUtils = new FileUtils();
    }
    
    @Override
    public void onReceive(Context context, Intent intent) {
        // TODO Auto-generated method stub
        mContext = context.getApplicationContext();

        if(intent.getAction().equals(ACTION_FALL_FREE)) {
//            mHandler.sendMessage(getFallFreeMessage(intent.getStringExtra(EXTRA_STATE)));
            String status = intent.getStringExtra(EXTRA_STATE);
            if (FALL.equals(status)) {
                mFallNewestDetectTime = new Date().getTime();
                mFallDetectTimeDValue = mFallNewestDetectTime - mFallLastDetectTime;
//                BBLog.i(TAG, "G-Sensor检测到设备fall，上次检测时间点：" + mFallLastDetectTime +
//                        "，最新检测时间点：" + mFallNewestDetectTime + "，差值：" + mFallDetectTimeDValue);
                if (mFallDetectTimeDValue > FALL_FREE_DETECT_TIME_INTERVAL) {
//                    BBLog.e(TAG, "设备自由落体检测到Fall时间：" + mFileUtils.mSimpleDateFormat.format(new Date()));
                    mFallDetectValid = true;
                }
                mFallLastDetectTime = mFallNewestDetectTime;
            } else if (FREE.equals(status)) {
                mFreeNewestDetectTime = new Date().getTime();
//                BBLog.e(TAG, "检测到设备自由落体第" + (mFreeCount + 1) + "次...");
                if (mFreeCount == 1) {
                    mFreeSecondDetectedTime = mFreeNewestDetectTime;
//                    BBLog.e(TAG, "检测到设备自由落体第2次，时间点：" + mFileUtils.mSimpleDateFormat.format(new Date()));
                }
                mFreeDetectTimeDValue = mFreeNewestDetectTime - mFreeLastDetectTime;
//                BBLog.i(TAG, "G-Sensor检测到设备free，上次检测时间点：" + mFreeLastDetectTime +
//                        "，最新检测时间点：" + mFreeNewestDetectTime + "，差值：" + mFreeDetectTimeDValue);
                if (mFreeDetectTimeDValue > FALL_FREE_DETECT_TIME_INTERVAL) {
                    mFreeDetectValid = true;
                }
                mFreeLastDetectTime = mFreeNewestDetectTime;
                mFreeCount ++;
            }

            if (mFallDetectValid && mFreeDetectValid) {
                lineCount = 0;
                lastFlag = 0;
                switchCount = 0;
                mFallAccelerationCount = 0;
                mFreeAccelerationCount = 0;
                mFreeAccelerationCountValid = 0;
                mFallDetectValid = false;
                mFreeDetectValid = false;
                mFallToGroundValid = false;
                mFreeFirstDetectedFlag = false;
                mFreeFirstDetectedTime = new Date().getTime();
                mFreeFirstDetectedTimeValid = 0;
                BBLog.e(TAG, "检测到设备自由落体时间：" + mFileUtils.mSimpleDateFormat.format(new Date(mFreeFirstDetectedTime)));
                mHandler.sendMessage(getFallFreeMessage(FREE));
            }
        }
    }

    private Message getFallFreeMessage(String status) {
        Bundle bundle = new Bundle();
        bundle.putString(EXTRA_STATE, status);

        Message message = new Message();
        message.what = MSG_FALL_FREE;
        message.setData(bundle);

        return message;
    }

    private Handler mHandler = new Handler() {
        public void handleMessage(Message msg) {
            switch (msg.what) {
            case MSG_FALL_FREE:
                startRecoder(msg.getData().getString(EXTRA_STATE));
                break;
            case MSG_SENSOR_RECODER:
                stopRecoder();
                break;
            default:
                break;
            }
        };
    };
    
    private void startRecoder(String status) {
        if(status.equals(FREE)) {
            if(mSensorManager == null) {
                mSensorManager = (SensorManager) mContext.getSystemService(Context.SENSOR_SERVICE);
            }
            if(mSensor == null) {
                mSensor = mSensorManager.getDefaultSensor(mSensor.TYPE_ACCELEROMETER);
            }
            mFallFreeCount = Settings.System.getInt(mContext.getContentResolver(), mFileUtils.FALL_COUNT, 0) + 1;
            mGSensorLogContent.append(mFileUtils.mSimpleDateFormat.format(new Date()) + ": ---第" + mFallFreeCount + "次自由落体---\r\n");
            Settings.System.putInt(mContext.getContentResolver(), mFileUtils.FALL_COUNT, mFallFreeCount);
            BBLog.i(TAG,  "K-free检测到设备自由落体次数" + mFallFreeCount);

            // 设备跌落语音报警
//            ToneGenerator tg = new ToneGenerator(AudioManager.STREAM_MUSIC, ToneGenerator.MAX_VOLUME);
//            tg.startTone(ToneGenerator.TONE_CDMA_NETWORK_CALLWAITING);

            /**
             * 第一个参数就是 SensorEventListener 的实例
             * 第二个参数是 mSensor 的实例
             * 第三个参数是用于表示传感器输出信息的更新速率
             *      SENSOR_DELAY_UI、
             *      SENSOR_DELAY_NORMAL、
             *      SENSOR_DELAY_GAME、
             *      SENSOR_DELAY_FASTEST这四种值可选，它们的更新速率是依次递增的
             */
            mSensorManager.registerListener(mSensorEventListener, mSensor, SensorManager.SENSOR_DELAY_FASTEST);
            mHandler.sendEmptyMessageDelayed(MSG_SENSOR_RECODER, FALL_FREE_DETECT_STOP_DELAY_TIME);
        }
    }
    
    private void stopRecoder() {
        if(mSensorManager != null) {
            mSensorManager.unregisterListener(mSensorEventListener);
        }

        if(mFileUtils != null) {
            boolean flag = mFileUtils.writeFile(mFileUtils.getWiseAppConfigPath(), mFileUtils.FILE_NAME, mGSensorLogContent.toString(), true);
            if(flag) {
                mGSensorLogContent.setLength(0);
            }
        }
    }
}
