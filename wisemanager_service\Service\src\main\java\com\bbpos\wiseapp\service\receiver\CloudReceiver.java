package com.bbpos.wiseapp.service.receiver;

import android.annotation.SuppressLint;
import android.app.AlarmManager;
import android.app.Dialog;
import android.app.PendingIntent;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.graphics.Color;
import android.net.wifi.WifiManager;
import android.os.Build;
import android.os.CountDownTimer;
import android.os.Handler;
import android.os.Looper;
import android.text.TextUtils;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.WindowManager;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.TextView;

import com.bbpos.wiseapp.logger.BBLog;
import com.bbpos.wiseapp.service.ListeningService;
import com.bbpos.wiseapp.service.R;
import com.bbpos.wiseapp.service.appdata.DataCollectService;
import com.bbpos.wiseapp.service.common.RequestPermission;
import com.bbpos.wiseapp.service.common.UsualData;
import com.bbpos.wiseapp.system.api.DeviceInfoApi;
import com.bbpos.wiseapp.system.api.ServiceApi;
import com.bbpos.wiseapp.tms.adapter.SystemManagerAdapter;
import com.bbpos.wiseapp.tms.location.GpsLocationManager;
import com.bbpos.wiseapp.tms.service.InitializeService;
import com.bbpos.wiseapp.tms.utils.BroadcastActions;
import com.bbpos.wiseapp.tms.utils.Constants;
import com.bbpos.wiseapp.tms.utils.ContextUtil;
import com.bbpos.wiseapp.tms.widget.MRebootEventDialog;
import com.bbpos.wiseapp.utils.SharedPreferencesUtils;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.io.IOException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;

/**
 * 数据流量统计部分移植到TMS*/
@SuppressLint("InlinedApi")
public class CloudReceiver extends BroadcastReceiver {
	private static final String TAG = "CloudReceiver";
	private static Handler mHandler = new Handler(Looper.getMainLooper());
	private static Dialog warningDialog;
	private static CountDownTimer countDownTimer;

	@SuppressLint("WrongConstant")
	@Override
	public void onReceive(Context context, Intent intent) {
		String action = intent.getAction();
		BBLog.d(TAG, "CloudReceiver Broadcast action:" + action);
		if(Intent.ACTION_BOOT_COMPLETED.equals(action)) {
//			initConfigure(context);

			if (Build.VERSION.SDK_INT > Build.VERSION_CODES.N) {
				Intent intentService = new Intent(context, InitializeService.class);
				ServiceApi.getIntance().startService(intentService);

				startDataCellectTimer(context);
			}

			//只允许7MD设备执行wifi profile schedule
//			if (DeviceInfoApi.getIntance().isWisePosPro()){
//				WifiProfileHandler.scheduleWifProfileTask(context);
//			}

			//读取参数文件 关闭生态链 TODO

			//流量统计相关业务
			//删除last_time记录 
//			DataCollectDBHelper dbHelper = DataCollectDBHelper.getInstance(context);
//			dbHelper.deleteLastTimeTrafficData();
			//开始定时数据收集
//			TrafficHelpers.startDataCollectTimer(context, Constants.DATA_COLLECT_INVERTAL*1000);

			Intent intent_boot = new Intent(BroadcastActions.BOOT_COMPLETED_BC);
			if(Build.VERSION.SDK_INT >= 26) {
				//https://www.jianshu.com/p/5283ebc225d5?utm_source=oschina-app
//				intent_boot.addFlags(Intent.FLAG_RECEIVER_EXCLUDE_BACKGROUND|Intent.FLAG_RECEIVER_FROM_SHELL);
				intent_boot.addFlags(0x01000000 | 0x00400000);
			}
			ContextUtil.getInstance().sendBroadcast(intent_boot, RequestPermission.REQUEST_PERMISSION_MY_BROADCAST);
			BBLog.d(TAG, "CloudReceiver Broadcast sender boot completed ");

			checkOSPatchUpgradeResult(context);
		} else if (WifiManager.WIFI_STATE_CHANGED_ACTION.equals(action)) {
			//wifi发生改变
//            int wifiState = intent.getIntExtra(WifiManager.EXTRA_WIFI_STATE, 0); 
//             
//            if(wifiState == WifiManager.WIFI_STATE_DISABLED
//            		|| wifiState == WifiManager.WIFI_STATE_ENABLED){
//            	TrafficHelpers.startDataCollectService(context);
//            }
        } else if (Intent.ACTION_SHUTDOWN.equals(action)) {
			//关机广播 
//            TrafficHelpers.startDataCollectService(context);
        } else if (BroadcastActions.ACTION_EXPIRE_REBOOT.equals(action)) {
			if (ListeningService.isDeviceIdle(false)) {
				BBLog.i(TAG, "time's up to reboot, the terminal's idle and restart directly");
				SystemManagerAdapter.reboot(ContextUtil.getInstance());
			} else {
				BBLog.i(TAG, "time's up to reboot, the terminal's busy, prompt to restart");
				MRebootEventDialog.showRebootEventDialog(ContextUtil.getInstance(), context.getString(R.string.prompt_sys_maintenance), context.getString(R.string.expire_to_reboot),
					new View.OnClickListener() {
						@Override
						public void onClick(View v) {
							int timeout = 300;
							MRebootEventDialog.mDialog.createWindowManager();
							MRebootEventDialog.mDialog.createDesktopLayout();
							MRebootEventDialog.mDialog.showDesk(timeout);
						}
					},
					new View.OnClickListener() {
						@Override
						public void onClick(View v) {
							SystemManagerAdapter.reboot(context);
						}
					});
			}
		} else if (BroadcastActions.ACTION_GEOFENCING_DETECTED_WARNING.equals(action)) {
			if (intent.hasExtra("warning_status")) {
				int status = intent.getIntExtra("warning_status", 0);
				if (GpsLocationManager.WARNING_STATUS_OUT_OF_FENCE_AT_REBOOT == status) {
					long leftTime = SharedPreferencesUtils.getSharePreferencesValue(UsualData.SHARED_PREFERENCES_GPS_GEOFENCE_WARN_COUNTDOWN_SECS, GpsLocationManager.GEO_LOCK_MINS * 60 * 1000);
					BBLog.e(TAG, "time to LOCK DEVICE： " + leftTime + " secs");
					setTimerToLock(leftTime);
					showGeofenceWarningDialog(context);
				} else if (GpsLocationManager.WARNING_STATUS_IN_FENCE == status) {
					//如果從IN_ZONE變成OUT_OF_ZONE，則啓動定時器，持續OUT_OF_ZONE過程中不再重複設置；
					setTimerToLock(GpsLocationManager.GEO_LOCK_MINS * 60 * 1000);
					showGeofenceWarningDialog(context);
				} else if (GpsLocationManager.WARNING_STATUS_OUT_OF_FENCE == status) {
					showGeofenceWarningDialog(context);
				}
			}
		} else if (BroadcastActions.ACTION_GEOFENCING_DETECTED_WARNING_CLOSE.equals(action)) {
			closeGeofenceWarningDialog(context);
			unsetTimerToLock();
		} else if (BroadcastActions.ACTION_GEOFENCING_DETECTED_LOCK.equals(action)) {
			closeGeofenceWarningDialog(context);
			GpsLocationManager.gotoLockDeviceScreen();
		} else if (BroadcastActions.ACTION_GEOFENCING_RESET_LOCKSCREEN_TIMER.equals(action)) {
			unsetTimerToLock();
			setTimerToLock(GpsLocationManager.GEO_LOCK_MINS * 60 * 1000);
		}
	}

	//启动心跳定时器
	private void startDataCellectTimer(Context context) {
		int pollFrequency = 60*60*1000;
        // 定时轮询Intent
        Intent i = new Intent(context, DataCollectService.class);
        PendingIntent pi = PendingIntent.getService(context, 0, i,PendingIntent.FLAG_UPDATE_CURRENT);

        // 取消旧的轮询定时
        AlarmManager am = (AlarmManager) context.getSystemService(Context.ALARM_SERVICE);
        am.cancel(pi);
        am.setRepeating(AlarmManager.RTC_WAKEUP, getNextHour().getTime(), pollFrequency , pi);
	}

	//获取前一小时的时间
	private Date getNextHour() {
		Date date = new Date();
		Calendar cal = Calendar.getInstance();
		cal.setTime(date);//date 换成已经已知的Date对象
		if (cal.get(Calendar.MINUTE)<57) {
			cal.set(Calendar.MINUTE, 58);
		} else {
			cal.add(Calendar.HOUR_OF_DAY, +1);
			cal.set(Calendar.MINUTE, 58);	
		}
		SimpleDateFormat format = new SimpleDateFormat("yyyyMMdd HH:mm:ss");	
		String nexthour = format.format(cal.getTime());
		
	    try {
	    	date = 	format.parse(nexthour);
		} catch (ParseException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
//	    BBLog.e("NextHour:"+nexthour, "date:"+date);
	    return date;
	}

	private void showGeofenceWarningDialog(Context context) {
		mHandler.post(new Runnable() {
			@Override
			public void run() {
				BBLog.i(TAG, "warningDialog="+warningDialog+(warningDialog==null?"":(" isShowing()="+warningDialog.isShowing())));
				if (warningDialog!=null && warningDialog.isShowing()) {
					return;
				}
				if (warningDialog != null) {
					warningDialog.dismiss();
					warningDialog = null;
					return;
				}
				warningDialog = new Dialog(context, R.style.dialog_style_ex);
				View dialogView = LayoutInflater.from(context).inflate(R.layout.dialog_geowarning, null);
				((TextView)dialogView.findViewById(R.id.tv_title)).setTypeface(ContextUtil.tf_trasandina_w03_bold);
				warningDialog.setContentView(dialogView);
				warningDialog.setCanceledOnTouchOutside(false);
				Button btn_ok = (Button) warningDialog.findViewById(R.id.btn_ok);
				btn_ok.setOnClickListener(
						new View.OnClickListener() {
							@Override
							public void onClick(View v) {
								warningDialog.dismiss();
								warningDialog = null;
							}
						});
				warningDialog.getWindow().setType((WindowManager.LayoutParams.TYPE_SYSTEM_ALERT));
				warningDialog.show();

			}
		});
	}

	private void closeGeofenceWarningDialog(Context context) {
		mHandler.post(new Runnable() {
			@Override
			public void run() {
				if (warningDialog!=null && warningDialog.isShowing()) {
					warningDialog.dismiss();
					warningDialog = null;
				}
			}
		});
	}

	private static Runnable showLockScreen = new Runnable() {
		@Override
		public void run() {
			GpsLocationManager.showOutOfGeofenceLockScreen();
		}
	};

	private void setTimerToLock(long timerToLock) {
		BBLog.e(TAG, "start timer: " + timerToLock/1000 + " secs left before LOCK DEVICE");
//		mHandler.postDelayed(showLockScreen, GpsLocationManager.GEO_LOCK_MINS * 60 * 1000);
		if (countDownTimer!=null) {
			countDownTimer.cancel();
			countDownTimer = null;
		}
		if (countDownTimer == null) {
			countDownTimer = new CountDownTimer(timerToLock, 1000) {
				@Override
				public void onTick(long millisUntilFinished) {
					SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_GPS_GEOFENCE_WARN_COUNTDOWN_SECS, (int)millisUntilFinished);
				}

				@Override
				public void onFinish() {
					showLockScreen.run();
				}
			}.start();
		} else {
			countDownTimer.start();
		}
	}

	private void unsetTimerToLock() {
		BBLog.e(TAG, "LOCK DEVICE timer cancelled");
//		mHandler.removeCallbacks(showLockScreen);
		if (countDownTimer != null) {
			countDownTimer.cancel();
			countDownTimer = null;
		}
	}

	private void checkOSPatchUpgradeResult(Context context) {
		try {
			String  tempString = "";
			File file = new File(Constants.OTA_FLAG_PATH);
			Log.e(BBLog.TAG, "checkOSPatchUpgradeResult: file exits = " + file.exists());
			if (file.exists()){
				BufferedReader reader = new BufferedReader(new FileReader(file));
				while ((tempString = reader.readLine())!= null){
					break;
				}
				reader.close();
				Log.e(BBLog.TAG, "checkOSPatchUpgradeResult: tempString = " + tempString);
			}

			if (!TextUtils.isEmpty(tempString)){
				showOsUpgradeResultDialog(context,DeviceInfoApi.getIntance().getCustomVersion().equals(tempString));
			}
			file.delete();
		} catch (IOException e) {
			e.printStackTrace();
		}
	}

	private Dialog osUpgradeResultDialog;
	private void showOsUpgradeResultDialog(Context context,boolean result) {
		mHandler.postDelayed(new Runnable() {
			@Override
			public void run() {
				if (osUpgradeResultDialog !=null && osUpgradeResultDialog.isShowing()) {
					return;
				}
				if (osUpgradeResultDialog != null) {
					osUpgradeResultDialog.dismiss();
					osUpgradeResultDialog = null;
					return;
				}
				osUpgradeResultDialog = new Dialog(context, R.style.dialog_style_ex);
				View dialogView = LayoutInflater.from(context).inflate(R.layout.dialog_os_upgrade_result, null);
//        ((TextView)dialogView.findViewById(R.id.tv_title)).setTypeface(ContextUtil.tf_trasandina_w03_bold);
				((TextView)dialogView.findViewById(R.id.tv_title)).setText(result ? "OS Upgrade Completed" : "OS Upgrade Fail");
				((TextView)dialogView.findViewById(R.id.tv_title)).setTextColor(result ? Color.parseColor("#3a3c46") : Color.parseColor("#ff0000"));
				((ImageView)dialogView.findViewById(R.id.iv_image)).setBackground(result ? context.getResources().getDrawable(R.drawable.success) : context.getResources().getDrawable(R.drawable.fail));
				((TextView)dialogView.findViewById(R.id.tv_content)).setText("Current Version: "+DeviceInfoApi.getIntance().getCustomVersion());
				osUpgradeResultDialog.setContentView(dialogView);
				osUpgradeResultDialog.setCanceledOnTouchOutside(false);
				Button btn_ok = (Button) osUpgradeResultDialog.findViewById(R.id.btn_ok);
				btn_ok.setOnClickListener(
						new View.OnClickListener() {
							@Override
							public void onClick(View v) {
								osUpgradeResultDialog.dismiss();
								osUpgradeResultDialog = null;
							}
						});
        		osUpgradeResultDialog.getWindow().setType((WindowManager.LayoutParams.TYPE_SYSTEM_ALERT));
				osUpgradeResultDialog.show();
			}
		},5000);

	}
}