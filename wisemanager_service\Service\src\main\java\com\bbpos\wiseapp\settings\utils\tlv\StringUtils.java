package com.bbpos.wiseapp.settings.utils.tlv;

import java.io.ByteArrayOutputStream;

public class StringUtils {
	protected static final char[] hexArray = "0123456789ABCDEF".toCharArray();

	/**
	 * Convert Hex String to byte array
	 * @param data
	 * @return
	 */
	public static byte[] String2Hex(String data)
	{
		byte[] result;

		result = new byte[data.length()/2];
		for (int i=0; i<data.length(); i+=2)
			result[i/2] = (byte)(Integer.parseInt(data.substring(i, i+2), 16));

		return result;
	}

	/**
	 * Convert Byte Array to Hex String
	 * @param data
	 * @return
	 */
	public static String Hex2String(byte[] data)
	{
		if(data == null) {
			return "";
		}
		String result = "";
		for (int i=0; i<data.length; i++)
		{
			int tmp = (data[i] >> 4);
			result += Integer.toString((tmp & 0x0F), 16);
			tmp = (data[i] & 0x0F);
			result += Integer.toString((tmp & 0x0F), 16);
		}

		return result;
	}

    public static String asciiStringToHexString(String asciiValue) {
        char[] chars = asciiValue.toCharArray();
        StringBuffer hex = new StringBuffer();
        for (int i = 0; i < chars.length; i++) {
            hex.append(Integer.toHexString((int) chars[i]));
        }
        return hex.toString();
    }

	public static String toHexString(byte b) {
        return Integer.toString( ( b & 0xFF ) + 0x100, 16).substring( 1 );
    }

	public static String toHexString(byte[] b) {
		if(b == null) {
			return "null";
		}
		String result = "";
		for (int i=0; i < b.length; i++) {
			result += Integer.toString( ( b[i] & 0xFF ) + 0x100, 16).substring( 1 );
		}
		return result;
	}

	public static byte[] hexToByteArray(String s) {
		if(s == null) {
			s = "";
		}
		ByteArrayOutputStream bout = new ByteArrayOutputStream();
		for(int i = 0; i < s.length() - 1; i += 2) {
			String data = s.substring(i, i + 2);
			bout.write(Integer.parseInt(data, 16));
		}
		return bout.toByteArray();
	}

	public static int hexToDecimal(String hex)
	{
		int decimalValue=0;
		for(int i=0;i<hex.length();i++)
		{
			char hexChar=hex.charAt(i);
			decimalValue=decimalValue*16+hexCharToDecimal(hexChar);
		}
		return decimalValue;
	}

	public static int hexCharToDecimal(char hexChar)
	{
		if(hexChar>='A'&&hexChar<='F')
			return 10+hexChar-'A';
		else
			return hexChar-'0';//切记不能写成int类型的0，因为字符'0'转换为int时值为48
	}

	public static boolean isNumeric(String str){
		for (int i = str.length();--i>=0;){
			if (!Character.isDigit(str.charAt(i))){
				return false;
			}
		}
		return true;
	}
}
