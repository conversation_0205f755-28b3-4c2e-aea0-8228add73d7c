R_DEF: Internal format may change without notice
local
anim dialog_enter
anim dialog_exit
attr? bgColor
attr? bgCorner
attr? bgSize
attr? divisionLineColor
attr? divisionLineSize
attr? passwordColor
attr? passwordNumber
attr? passwordRadius
attr? showPassword
color black
color gray
color purple_200
color purple_500
color purple_700
color red
color slate_grey
color teal_200
color teal_700
color white
dimen dp_15
dimen dp_20
dimen dp_40
dimen dp_5
drawable bg_countdown_warning
drawable bg_shape
drawable btn_bord_gray
drawable btn_bord_green
drawable button_background
drawable button_background_red
drawable button_delay_selector
drawable button_primary
drawable button_reboot_selector
drawable button_secondary
drawable countdown_background
drawable credit_card
drawable dialog_background
drawable dialog_content_background
drawable dialog_reboot_background
drawable dialog_reboot_header_bg
drawable edit_text_background
drawable float_window_background
drawable geo_warning
drawable ic_launcher_background
drawable ic_launcher_foreground
drawable ic_lock
drawable ic_notification
drawable ic_reboot_warning
drawable ic_timer
drawable ic_warning_geofence
drawable shape_corner_down
id btn_config
id btn_delay
id btn_install
id btn_reboot_now
id btn_start
id btn_stop
id btn_submit
id iv_float_icon
id iv_warning_1
id iv_warning_2
id ll_lockdevice
id ll_lockdevice_input
id pet_pwd
id tv_content_1
id tv_countdown
id tv_message
id tv_no_pwd
id tv_prompt_1
id tv_prompt_2
id tv_reboot_message
id tv_sn
id tv_stats
id tv_status
id tv_timeout
id tv_tip
id tv_title
id tv_title_1
id tv_title_2
id tv_warning
layout activity_lock_screen
layout activity_remote_view_test
layout dialog_os_upgrade
layout dialog_reboot_warning
layout view_reboot_float_window
mipmap ic_launcher
mipmap ic_launcher_round
string accessibility_service_description
string app_name
style GeofenceDialogAnimation
style GeofenceWarningDialogStyle
style Theme.MyTheme
style no_statusbar_activity
styleable PasswordEditText passwordNumber passwordRadius passwordColor divisionLineColor divisionLineSize bgColor bgSize bgCorner showPassword
xml accessibility_service_config
xml backup_rules
xml data_extraction_rules
xml network_security_config
