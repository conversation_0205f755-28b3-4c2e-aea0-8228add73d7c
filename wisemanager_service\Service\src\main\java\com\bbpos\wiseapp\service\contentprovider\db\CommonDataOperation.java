package com.bbpos.wiseapp.service.contentprovider.db;

import android.content.ContentValues;
import android.content.Context;
import android.database.Cursor;
import android.database.SQLException;
import android.database.sqlite.SQLiteDatabase;

public class CommonDataOperation {
	private Context mContext = null;
//	private SQLiteDatabase mSQLiteDatabase = null;
	private static CommonDataOpenHelper mInstanceDb=null;	

	public  CommonDataOperation(Context context) {
		mContext=context;
	}
	/**
	 * 	open the database
	 * 
	 */
	public static CommonDataOpenHelper open(Context context) throws SQLException {
		if (mInstanceDb == null) {
			mInstanceDb = new CommonDataOpenHelper(context);
//			mSQLiteDatabase = mInstanceDb.getWritableDatabase();
		}
		return mInstanceDb;
	}	
	
	/**
	 * 	close the database
	 * 
	 */
	public void close() {
//		mSQLiteDatabase.close();
		mInstanceDb.close();
	}	
	
	/**
	 * 插入数据  如果type已存在就更新
	 * @param param_key
	 * @param param_value
	 * @return
	 */
	public boolean insertData(String param_key,String param_value ) {
		SQLiteDatabase mSQLiteDatabase = mInstanceDb.getWritableDatabase();
		if(getOneExist(param_key)) {
			return updateData( param_key, param_value);
		}
		
		ContentValues initialValues = new ContentValues();
		initialValues.put("param_key", param_key);
		initialValues.put("param_value", param_value);
		long ret = mSQLiteDatabase.insert(DbConstants.DB_TABLE, "_id", initialValues);
//		mSQLiteDatabase.close();
		if (ret > 0) {
			return true;
		}else {
			return false;
		}
	}	
	
	
	public boolean updateData(String param_key,String param_value) {
		SQLiteDatabase mSQLiteDatabase = mInstanceDb.getWritableDatabase();
		ContentValues initialValues = new ContentValues();
		initialValues.put("param_key", param_key);
		initialValues.put("param_value", param_value);
		int ret = mSQLiteDatabase.update(DbConstants.DB_TABLE, initialValues, "param_key" + "= '"
				+ param_key+"'", null);
//		mSQLiteDatabase.close();
		if (ret > 0) {
			return true;
		}else {
			return false;
		}
	}
	
	/**
	 * 判断 type是否已存在
	 * @param param_key
	 * @return
	 * @throws SQLException
	 */
	public boolean getOneExist(String param_key) throws SQLException {
		boolean result = false;
		SQLiteDatabase mSQLiteDatabase = mInstanceDb.getWritableDatabase();
		Cursor mCursor = mSQLiteDatabase.query(true, DbConstants.DB_TABLE,
				new String[] {"param_value"}, "param_key = '"+param_key
				+"'", null, null, null, null, null);			
		if (mCursor != null && mCursor.getCount() > 0) {
			result = true;
		}
		mCursor.close();
//		mSQLiteDatabase.close();
		return  result;
	}	
	
	public Cursor getAll() throws SQLException {
		SQLiteDatabase mSQLiteDatabase = mInstanceDb.getWritableDatabase();
		Cursor mCursor = mSQLiteDatabase.query(true, DbConstants.DB_TABLE,
				new String[] {"_id","param_key","param_value"}, 
				null,null, null, null,null, null);			
		if (mCursor != null && mCursor.getCount() > 0) {
			mCursor.moveToFirst();
		}
//		mSQLiteDatabase.close();
		return  mCursor;
	}	
}
