package com.bbpos.wiseapp.system.api;

import android.app.PendingIntent;
import android.content.Context;
import android.content.Intent;
import android.content.IntentSender;
import android.content.pm.IPackageDeleteObserver;
import android.content.pm.IPackageInstallObserver;
import android.content.pm.PackageInstaller;
import android.content.pm.PackageManager;
import android.net.Uri;
import android.os.Handler;
import android.os.Looper;
import android.os.RemoteException;
import androidx.annotation.NonNull;
import android.util.Log;

import com.bbpos.wiseapp.logger.BBLog;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.Map;

import com.bbpos.wiseapp.sdk.app.IAppDeleteObserver;
import com.bbpos.wiseapp.sdk.app.IAppInstallObserver;

public class Helper {
    public static final String TAG = "installer";
    public static PackageInstaller.SessionCallback sessionCallback = null;
    private static boolean bRegister = false;
    private static Map<Integer, IAppInstallObserver> m_callback = new HashMap<>();
    private static Map<Integer, String> m_callback_pkg = new HashMap<>();

    public static void installPackage(Context context, Uri uri, IPackageInstallObserver observer, int flags, String installer) throws InvocationTargetException, IllegalAccessException, NoSuchMethodException {
        Method method = getMethod(PackageManager.class, "installPackage", Uri.class, IPackageInstallObserver.class, int.class, String.class);
        method.invoke(context.getPackageManager(), uri, observer, flags, installer);
    }

    public static synchronized void installPackage(@NonNull Context context, @NonNull String path, final IAppInstallObserver observer, String installerPackageName) throws Exception {
        //获取PackageInstaller
        PackageInstaller packageInstaller = context.getPackageManager().getPackageInstaller();
        BBLog.i(BBLog.TAG, "installPackage: SessionCallback, registerSessionCallback");
        sessionCallback = new PackageInstaller.SessionCallback() {
            @Override
            public void onCreated(int sessionId) {
                BBLog.i(TAG, "installPackage: SessionCallback, onCreated" + "sessionId = " + sessionId);
            }

            @Override
            public void onBadgingChanged(int sessionId) {
                BBLog.i(TAG, "installPackage: SessionCallback, onBadgingChanged" + "sessionId = " + sessionId);
            }

            @Override
            public void onActiveChanged(int sessionId, boolean active) {
                BBLog.i(TAG, "installPackage: SessionCallback, onActiveChanged active = " + active + "sessionId = " + sessionId);
            }

            @Override
            public void onProgressChanged(int sessionId, float progress) {
                BBLog.i(TAG, "installPackage: SessionCallback, onProgressChanged progress = " + progress + "sessionId = " + sessionId);
            }

            @Override
            public void onFinished(int sessionId, boolean success) {
                BBLog.i(TAG, "installPackage: SessionCallback, onFinished sessionId = " + sessionId + ", success = " + success);
                try {
                    if (sessionCallback != null) {
//                        packageInstaller.unregisterSessionCallback(sessionCallback);
//                        BBLog.i(TAG, "installPackage: SessionCallback, onFinished unregisterSessionCallback");
                    }
                    if (success) {
                        if (m_callback.get(sessionId) != null) {
                            m_callback.get(sessionId).onInstallFinished(m_callback_pkg.get(sessionId), 1, "");
                            m_callback.remove(sessionId);
                            m_callback_pkg.remove(sessionId);
                        }
                    } else {
                        if (m_callback.get(sessionId) != null) {
                            m_callback.get(sessionId).onInstallFinished(m_callback_pkg.get(sessionId), 99, "");
                            m_callback.remove(sessionId);
                            m_callback_pkg.remove(sessionId);
                        }
                    }
                } catch (RemoteException e) {
                    e.printStackTrace();
                }
            }
        };

        if (!bRegister) {
            packageInstaller.registerSessionCallback(sessionCallback, new Handler(Looper.getMainLooper()));
            bRegister = true;
        }

        PackageInstaller.SessionParams params= new PackageInstaller.SessionParams(PackageInstaller.SessionParams.MODE_FULL_INSTALL);
        PackageInstaller.Session session=null;
        OutputStream outputStream=null;
        FileInputStream inputStream=null;
        try {
            File file=new File(path);
            String apkName=path.substring(path.lastIndexOf(File.separator)+1,path.lastIndexOf(".apk"));

            //创建Session
            int sessionId = packageInstaller.createSession(params);

            m_callback.put(sessionId, observer);
            m_callback_pkg.put(sessionId, installerPackageName);
            //开启Session
            session=packageInstaller.openSession(sessionId);
            //获取输出流，用于将apk写入session
            outputStream = session.openWrite(apkName, 0, -1);
            inputStream=new FileInputStream(file);
            byte[] buffer=new byte[4096];
            int n;
            //读取apk文件写入session
            while ((n=inputStream.read(buffer))>0){
                outputStream.write(buffer,0,n);
            }
            //写完需要关闭流，否则会抛异常“files still open”
            inputStream.close();
            inputStream=null;
            outputStream.flush();
            outputStream.close();
            outputStream=null;
            //配置安装完成后发起的intent，通常是打开activity
            Intent intent=new Intent();
            PendingIntent pendingIntent=PendingIntent.getActivity(context,0,intent,0);
            IntentSender intentSender = pendingIntent.getIntentSender();
            //提交启动安装
            session.commit(intentSender);
            BBLog.i(TAG, "installPackage: install commit");
        } catch (IOException e) {
            observer.onInstallFinished(installerPackageName, 99,"");
            throw new RuntimeException("Couldn't install package 99", e);
        } catch (RuntimeException e) {
            observer.onInstallFinished(installerPackageName, 99,"");
            if (session != null) {
                session.abandon();
            }
            throw e;
        } finally {
            try {
                if (inputStream != null) {
                    inputStream.close();
                    inputStream = null;
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
            try {
                if (outputStream != null) {
                    outputStream.close();
                    outputStream = null;
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    public static void deletePackage(Context context, String packageName, IPackageDeleteObserver observer, int flags) throws NoSuchMethodException, InvocationTargetException, IllegalAccessException {
        Method method = getMethod(PackageManager.class, "deletePackage", String.class, IPackageDeleteObserver.class, int.class);
        method.invoke(context.getPackageManager(), packageName, observer, flags);
    }

    public static void deletePackage(Context context, String packageName, IAppDeleteObserver observer) {
        Intent intent = new Intent();
        PendingIntent sender = PendingIntent.getActivity(context, 0, intent, 0);
        PackageInstaller mPackageInstaller = context.getPackageManager().getPackageInstaller();
        mPackageInstaller.uninstall(packageName, sender.getIntentSender());
        try {
            BBLog.i(BBLog.TAG, "install onDeleted");
            observer.onDeleteFinished(packageName, 1,"");
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    public static Method getMethod(Class<?> cls, String name, Class<?>... params) throws NoSuchMethodException, InvocationTargetException, IllegalAccessException {
        Method method = cls.getDeclaredMethod(name, params);
        method.setAccessible(true);
        return method;
    }

    public static Field getField(Class<?> cls, String name) throws NoSuchMethodException, InvocationTargetException, IllegalAccessException, NoSuchFieldException {
        Field field = cls.getDeclaredField(name);
        field.setAccessible(true);
        return field;
    }
}
