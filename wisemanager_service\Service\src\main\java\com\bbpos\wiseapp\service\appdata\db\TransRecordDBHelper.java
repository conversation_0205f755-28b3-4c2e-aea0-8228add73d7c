package com.bbpos.wiseapp.service.appdata.db;

import android.content.ContentValues;
import android.content.Context;
import android.database.Cursor;
import android.database.sqlite.SQLiteDatabase;
import android.database.sqlite.SQLiteOpenHelper;
import android.util.Log;

import com.bbpos.wiseapp.logger.BBLog;
import com.bbpos.wiseapp.service.appdata.model.TransRecordData;

import java.util.ArrayList;
import java.util.List;

public class TransRecordDBHelper extends SQLiteOpenHelper{
	private final static String DATABASE_NAME = "trans_record.db";
	private final static int DATABASE_VERSION = 1;
	private final static String TRANS_RECORD_TABLE_NAME = "record";

	private static TransRecordDBHelper mInstance = null;

	public synchronized static TransRecordDBHelper getInstance(Context context) {
		if (mInstance == null) {
			mInstance = new TransRecordDBHelper(context);
		}
		return mInstance;
	};

	public TransRecordDBHelper(Context context) {
		super(context, DATABASE_NAME, null, DATABASE_VERSION);
	}

	@Override
	public void onCreate(SQLiteDatabase db) {
		String sql = "CREATE TABLE IF NOT EXISTS " + TRANS_RECORD_TABLE_NAME + " ("
				+ TransRecordData.ID + " INTEGER PRIMARY KEY AUTOINCREMENT, "
				+ TransRecordData.TRAN_APP_PKG + " TEXT NOT NULL, "
				+ TransRecordData.PAYMENT_DATA+ " TEXT NOT NULL, "
				+ TransRecordData.PAYMENT_DATA_MD5 + " TEXT NOT NULL"+")";
		db.execSQL(sql);
	}

	@Override
	public void onUpgrade(SQLiteDatabase db, int oldVersion, int newVersion) {
		String	sql = "DROP TABLE IF EXISTS " + TRANS_RECORD_TABLE_NAME;
		db.execSQL(sql);
		onCreate(db);
	}


	/**
	 * 新增記錄
	 */
	public synchronized long addRecordData(TransRecordData dataModel) {
		if (dataModel == null) {
			return -1;
		}
		return addRecordData(dataModel.packageName,dataModel.paymentData,dataModel.paymentDataMD5);
	}

	/**
	 * 新增記錄
	 * @param packageName
	 * @param paymentData
	 * @param paymentDataMd5
	 * @return
	 */
	public synchronized long addRecordData(String packageName, String paymentData,String paymentDataMd5) {
		long index = -1;
		SQLiteDatabase db = this.getWritableDatabase();
		try {
			db.beginTransaction();
			ContentValues cv = new ContentValues();
			cv.put(TransRecordData.TRAN_APP_PKG, packageName);
			cv.put(TransRecordData.PAYMENT_DATA, paymentData);
			cv.put(TransRecordData.PAYMENT_DATA_MD5, paymentDataMd5);
			index = db.insert(TRANS_RECORD_TABLE_NAME, null, cv);
			BBLog.v(BBLog.TAG, "addRecordData index :"+index);
			db.setTransactionSuccessful();
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			db.endTransaction();
		}
		return index;
	}

	public synchronized TransRecordData queryRecordData(String packageName, String paymentDataMd5) {
		SQLiteDatabase db = this.getReadableDatabase();
		TransRecordData recordData = null;
		try {
			String selectionStr = TransRecordData.TRAN_APP_PKG  + "=? AND " + TransRecordData.PAYMENT_DATA_MD5 + "=?";
			String selectionArgStr = packageName +","+paymentDataMd5;
			BBLog.v(BBLog.TAG, "queryRecordData selectionArgStr:"+selectionArgStr);
			String[] selectionArgs = selectionArgStr.split(",");
			Cursor cursor = db.query(TRANS_RECORD_TABLE_NAME, null, selectionStr, selectionArgs, null, null, null);
			if(cursor.getCount()>0) {
				while (cursor.moveToNext()) {
					recordData = new TransRecordData(cursor);
				}
			}
			cursor.close();
		} catch (Exception e) {
			e.printStackTrace();
		}
		return recordData;
	}


	public synchronized TransRecordData queryRecordData(String packageName, String paymentData,String paymentDataMd5) {
		SQLiteDatabase db = this.getReadableDatabase();
		TransRecordData recordData = null;
		try {
			String selectionStr = TransRecordData.TRAN_APP_PKG + "=? AND " +TransRecordData.PAYMENT_DATA + "=? AND " + TransRecordData.PAYMENT_DATA_MD5 + "=?";
			String selectionArgStr = packageName+","+paymentData+","+paymentDataMd5;
			BBLog.v(BBLog.TAG, "queryRecordData selectionArgStr:"+selectionArgStr);
			String[] selectionArgs = selectionArgStr.split(",");
			Cursor cursor = db.query(TRANS_RECORD_TABLE_NAME, null, selectionStr, selectionArgs, null, null, null);
			if(cursor.getCount()>0) {
				recordData = new TransRecordData(cursor);
			}
			cursor.close();
		} catch (Exception e) {
			e.printStackTrace();
		}
		return recordData;
	}

	/**
	 * 查詢所有記錄
	 * @return
	 */
	public synchronized List<TransRecordData> getAllRecordDataList() {
		SQLiteDatabase db = this.getReadableDatabase();
		List<TransRecordData> recordList = new ArrayList<TransRecordData>();
		try {
			Cursor cursor = db.query(TRANS_RECORD_TABLE_NAME, null, null, null, null, null, null);
			Log.e(BBLog.TAG, "getAllRecordDataList: count = " + cursor.getCount() );
			if(cursor.getCount()>0) {
				while (cursor.moveToNext()) {
					recordList.add(new TransRecordData(cursor));
				}
			}
			cursor.close();
		} catch (Exception e) {
			e.printStackTrace();
		}
		return recordList;
	}

	/** 刪除記錄
	 * @param dataModel
	 * @return
	 */
	public synchronized long deleteRecordData(TransRecordData dataModel) {
		if (dataModel == null) {
			return -1;
		}
		return deleteRecordData(dataModel.packageName,dataModel.paymentData,dataModel.paymentDataMD5);
	}

	public synchronized long deleteRecordData(String packageName, String paymentDataMd5) {
		int index = -1;
		SQLiteDatabase db = this.getWritableDatabase();
		try {
			db.beginTransaction();
			String selectionStr = TransRecordData.TRAN_APP_PKG  + "=? AND " + TransRecordData.PAYMENT_DATA_MD5 + "=?";
			String selectionArgStr = packageName +","+paymentDataMd5;
			String[] selectionArgs = selectionArgStr.split(",");
			index = db.delete(TRANS_RECORD_TABLE_NAME, selectionStr, selectionArgs);
			db.setTransactionSuccessful();
			BBLog.v(BBLog.TAG, "deleteRecordData index :"+index);
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			db.endTransaction();
		}
		return index;
	}

	/**
	 * 刪除記錄
	 * @param packageName
	 * @param paymentData
	 * @param paymentDataMd5
	 * @return
	 */
	public synchronized long deleteRecordData(String packageName, String paymentData,String paymentDataMd5) {
		int index = -1;
		SQLiteDatabase db = this.getWritableDatabase();
		try {
			db.beginTransaction();
			String selectionStr = TransRecordData.TRAN_APP_PKG + "=? AND " +TransRecordData.PAYMENT_DATA + "=? AND " + TransRecordData.PAYMENT_DATA_MD5 + "=?";
			String selectionArgStr = packageName+","+paymentData+","+paymentDataMd5;
			String[] selectionArgs = selectionArgStr.split(",");
			index = db.delete(TRANS_RECORD_TABLE_NAME, selectionStr, selectionArgs);
			db.setTransactionSuccessful();
			BBLog.v(BBLog.TAG, "deleteRecordData index :"+index);
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			db.endTransaction();
		}
		return index;
	}
}
