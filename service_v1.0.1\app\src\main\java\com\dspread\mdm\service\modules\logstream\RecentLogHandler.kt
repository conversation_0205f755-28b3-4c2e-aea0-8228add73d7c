package com.dspread.mdm.service.modules.logstream

import android.content.Context
import com.dspread.mdm.service.config.LogStreamConfig as LogStreamConfigManager
import com.dspread.mdm.service.utils.log.Logger
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.File
import java.io.FileOutputStream
import java.io.RandomAccessFile

/**
 * Recent日志处理器
 * 用于生成最近的日志片段（recent.gz）
 */
class RecentLogHandler(private val context: Context) {
    
    companion object {
        private const val TAG = "[RecentLogHandler]"
        private const val RECENT_LOG_FILENAME = "recent.log"
        private const val RECENT_GZ_FILENAME = "recent.gz"
        private const val READ_BUFFER_SIZE = 4 * 1024 // 4KB
    }
    
    private val logProcessor = LogProcessor(context)
    
    /**
     * 生成Recent日志文件
     * 从当前日志文件的末尾提取指定大小的内容
     */
    suspend fun generateRecentLog(currentLogFile: File): File? = withContext(Dispatchers.IO) {
        return@withContext try {
            Logger.logStream("$TAG 开始生成Recent日志文件")
            
            if (!currentLogFile.exists()) {
                Logger.logStreamW("$TAG 当前日志文件不存在: ${currentLogFile.absolutePath}")
                return@withContext null
            }
            
            val recentSize = LogStreamConfigManager.getRecentLogSize()
            Logger.logStream("$TAG Recent日志大小限制: ${recentSize}字节")
            
            // 创建recent.log文件
            val uploadDir = File(currentLogFile.parent, "upload")
            if (!uploadDir.exists()) {
                uploadDir.mkdirs()
            }
            
            val recentFile = File(uploadDir, RECENT_LOG_FILENAME)
            if (recentFile.exists()) {
                recentFile.delete()
            }
            recentFile.createNewFile()
            
            // 读取当前日志文件的最后部分
            val extractedSize = extractRecentContent(currentLogFile, recentFile, recentSize)
            
            if (extractedSize > 0) {
                Logger.logStream("$TAG Recent日志文件生成成功: ${recentFile.name}, 大小: ${extractedSize}字节")
                
                // 压缩recent.log为recent.gz
                val recentGzFile = compressRecentLog(recentFile)
                
                // 删除临时的recent.log文件
                recentFile.delete()
                
                recentGzFile
            } else {
                Logger.logStreamW("$TAG Recent日志内容为空")
                recentFile.delete()
                null
            }
            
        } catch (e: Exception) {
            Logger.logStreamE("$TAG 生成Recent日志文件失败", e)
            null
        }
    }
    
    /**
     * 提取Recent内容
     */
    private fun extractRecentContent(sourceFile: File, targetFile: File, recentSize: Long): Long {
        return try {
            val outputStream = FileOutputStream(targetFile)
            val accessFile = RandomAccessFile(sourceFile, "r")
            
            val currentLogSize = accessFile.length()
            val totalRecentSize: Long
            val recentOffset: Long
            
            // 计算读取的偏移量和大小
            if (currentLogSize > recentSize) {
                totalRecentSize = recentSize
                recentOffset = currentLogSize - recentSize
            } else {
                totalRecentSize = currentLogSize
                recentOffset = 0
            }
            
            Logger.logStream("$TAG 当前日志大小: ${currentLogSize}字节, 读取大小: ${totalRecentSize}字节, 偏移: ${recentOffset}字节")
            
            val buffer = ByteArray(READ_BUFFER_SIZE)
            var counter = 0L
            
            // 按块读取数据
            val totalBlocks = (totalRecentSize / READ_BUFFER_SIZE).toInt()
            for (i in 0..totalBlocks) {
                val currentOffset = recentOffset + i * READ_BUFFER_SIZE
                accessFile.seek(currentOffset)
                
                val remainingBytes = (totalRecentSize - counter).toInt()
                val bytesToRead = minOf(READ_BUFFER_SIZE, remainingBytes)
                
                if (bytesToRead <= 0) break
                
                val len = accessFile.read(buffer, 0, bytesToRead)
                if (len > 0) {
                    outputStream.write(buffer, 0, len)
                    counter += len
                    
                    if (counter >= totalRecentSize) {
                        break
                    }
                } else if (len == -1) {
                    break
                }
            }
            
            outputStream.close()
            accessFile.close()
            
            Logger.logStream("$TAG Recent内容提取完成，实际写入: ${counter}字节")
            counter
            
        } catch (e: Exception) {
            Logger.logStreamE("$TAG 提取Recent内容失败", e)
            0L
        }
    }
    
    /**
     * 压缩Recent日志文件
     */
    private suspend fun compressRecentLog(recentFile: File): File? {
        return try {
            // 目标文件在upload目录
            val uploadDir = File(recentFile.parent, "upload")
            if (!uploadDir.exists()) {
                uploadDir.mkdirs()
            }
            val recentGzFile = File(uploadDir, RECENT_GZ_FILENAME)

            Logger.logStream("$TAG 开始压缩Recent日志: ${recentFile.name} -> ${recentGzFile.name}")
            Logger.logStream("$TAG 压缩目标路径: ${recentGzFile.absolutePath}")

            val compressResult = logProcessor.compressFile(recentFile, CompressionType.GZIP)
            
            if (compressResult.isSuccess) {
                val compressedInfo = compressResult.getOrNull()
                if (compressedInfo != null) {
                    val actualGzFile = File(compressedInfo.path)
                    
                    // 移动压缩文件到目标位置
                    val finalFile = moveCompressedFileToTarget(actualGzFile, recentGzFile)
                    if (finalFile != null) {
                        Logger.logStream("$TAG Recent日志压缩成功: ${finalFile.name}, 压缩后大小: ${finalFile.length()}字节")
                        finalFile
                    } else {
                        Logger.logStreamE("$TAG 移动Recent压缩文件失败，使用原始位置: ${actualGzFile.name}")
                        // 即使移动失败，也返回原始压缩文件
                        actualGzFile
                    }
                } else {
                    Logger.logStreamE("$TAG Recent日志压缩结果为空")
                    null
                }
            } else {
                Logger.logStreamE("$TAG Recent日志压缩失败", compressResult.exceptionOrNull())
                null
            }
            
        } catch (e: Exception) {
            Logger.logStreamE("$TAG 压缩Recent日志异常", e)
            null
        }
    }
    
    /**
     * 上传Recent日志文件
     */
    suspend fun uploadRecentLog(recentGzFile: File): Boolean {
        return try {
            Logger.logStream("$TAG 开始上传Recent日志: ${recentGzFile.name}")
            
            val logUploader = LogUploader(context)
            val uploadResult = logUploader.uploadFileToS3(recentGzFile.absolutePath)
            
            if (uploadResult.isSuccess) {
                Logger.logStream("$TAG Recent日志上传成功: ${recentGzFile.name}")
                
                // 上传成功后删除本地文件
                recentGzFile.delete()
                true
            } else {
                Logger.logStreamE("$TAG Recent日志上传失败: ${recentGzFile.name}", uploadResult.exceptionOrNull())
                false
            }
            
        } catch (e: Exception) {
            Logger.logStreamE("$TAG 上传Recent日志异常", e)
            false
        }
    }
    
    /**
     * 生成并上传Recent日志（完整流程）
     */
    suspend fun generateAndUploadRecentLog(currentLogFile: File): Boolean {
        return try {
            Logger.logStream("$TAG 开始生成并上传Recent日志")
            
            val recentGzFile = generateRecentLog(currentLogFile)
            if (recentGzFile != null && recentGzFile.exists()) {
                val uploadSuccess = uploadRecentLog(recentGzFile)
                
                if (uploadSuccess) {
                    Logger.logStream("$TAG Recent日志生成并上传成功")
                } else {
                    Logger.logStreamE("$TAG Recent日志上传失败")
                }
                
                uploadSuccess
            } else {
                Logger.logStreamW("$TAG Recent日志生成失败，跳过上传")
                false
            }
            
        } catch (e: Exception) {
            Logger.logStreamE("$TAG 生成并上传Recent日志失败", e)
            false
        }
    }

    /**
     * 移动压缩文件到目标位置
     */
    private fun moveCompressedFileToTarget(sourceFile: File, targetFile: File): File? {
        return try {
            Logger.logStream("$TAG 移动压缩文件: ${sourceFile.absolutePath} -> ${targetFile.absolutePath}")

            // 检查源文件是否存在
            if (!sourceFile.exists()) {
                Logger.logStreamE("$TAG 源文件不存在: ${sourceFile.absolutePath}")
                return null
            }

            // 检查源文件和目标文件是否相同
            if (sourceFile.absolutePath == targetFile.absolutePath) {
                Logger.logStream("$TAG 源文件和目标文件路径相同，无需移动: ${sourceFile.name}")
                return sourceFile
            }

            // 确保目标目录存在
            targetFile.parentFile?.let { parentDir ->
                if (!parentDir.exists()) {
                    Logger.logStream("$TAG 创建目标目录: ${parentDir.absolutePath}")
                    parentDir.mkdirs()
                }
            }

            // 如果目标文件已存在，先删除
            if (targetFile.exists()) {
                Logger.logStreamW("$TAG 目标文件已存在，删除: ${targetFile.name}")
                targetFile.delete()
            }

            // 尝试重命名（移动）
            if (sourceFile.renameTo(targetFile)) {
                Logger.logStream("$TAG 文件移动成功: ${targetFile.name}")
                return targetFile
            } else {
                Logger.logStreamW("$TAG 重命名失败，尝试复制+删除")
                return copyAndDeleteFile(sourceFile, targetFile)
            }

        } catch (e: Exception) {
            Logger.logStreamE("$TAG 移动压缩文件异常", e)
            null
        }
    }

    /**
     * 复制并删除源文件
     */
    private fun copyAndDeleteFile(sourceFile: File, targetFile: File): File? {
        return try {
            Logger.logStream("$TAG 复制文件: ${sourceFile.name} -> ${targetFile.name}")

            sourceFile.copyTo(targetFile, overwrite = true)

            if (targetFile.exists() && targetFile.length() == sourceFile.length()) {
                sourceFile.delete()
                Logger.logStream("$TAG 文件复制成功: ${targetFile.name}")
                targetFile
            } else {
                Logger.logStreamE("$TAG 文件复制验证失败")
                null
            }

        } catch (e: Exception) {
            Logger.logStreamE("$TAG 复制文件异常", e)
            null
        }
    }
}
