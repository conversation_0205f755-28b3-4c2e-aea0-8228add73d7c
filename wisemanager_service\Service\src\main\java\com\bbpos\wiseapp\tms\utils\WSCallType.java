package com.bbpos.wiseapp.tms.utils;

/**
 * Websocket  通讯协议定义
 */
public class WSCallType {
	public static final String WS_RESPONE="C0000";//终端信息上送
	public static final String TER_INFO_UPLOAD="C0109";//终端信息上送
	public static final String TASK_RESULT_UPLOAD="C0108";//结果上送
	public static final String RULEBASED_RESULT_UPLOAD="C0107";//结果上送
	public static final String GET_STORE_ID="CR001";//获取StoreID
	public static final String UPLOAD_STORE_ID="CR002";//StoreID上送确认
	public static final String GET_PREINSTALL_APPLIST="CR003";//获取预安装APP列表
	public static final String CHECK_OS_UPDATE="CR004";//OS更新检查
	public static final String UNBOX_RESET_DONE="CC004";//unbox Reset成功
	public static final String DEVICE_STATUE_UPLOAD ="C0201";//终端使用状态上送
	public static final String DEVICE_EVENT_UPLOAD ="C0202";//终端上送事件信息
	public static final String DEVICE_GDS_OS_STATUS_UPLOAD ="C0203";//终端上送gds-tmt os升级状态
	public static final String APPS_INFO_UPLOAD ="C0901";//電池狀態事件信息
	public static final String BATTERY_STATUS_UPLOAD ="C0902";//電池狀態事件信息
	public static final String DATA_INFO_UPLOAD ="C0903";//電池狀態事件信息
	public static final String NETWORK_STATUS_UPLOAD ="C0904";//電池狀態事件信息
	public static final String GET_DEVICE_BID_UPLOAD ="C0906";//上送終端bid 信息
	public static final String GET_DEVICE_FULL_UPLOAD ="C0908";//上送終端完整DeviceInfo信息
}
