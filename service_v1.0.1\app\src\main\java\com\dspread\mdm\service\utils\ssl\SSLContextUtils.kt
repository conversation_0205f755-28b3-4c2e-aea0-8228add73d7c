package com.dspread.mdm.service.utils.ssl

import com.dspread.mdm.service.utils.log.Logger
import java.security.KeyStore
import javax.net.ssl.SSLContext
import javax.net.ssl.SSLSocketFactory
import javax.net.ssl.TrustManagerFactory
import javax.net.ssl.X509TrustManager

/**
 * SSL/TLS 配置工具类
 *  WmSSlContent.java 的 Kotlin 实现
 * 提供SSL上下文和证书管理功能
 */
object SSLContextUtils {

    private const val TAG = "SSLContextUtils"
    
    @Volatile
    private var trustManager: X509TrustManager? = null
    
    @Volatile
    private var sslContext: SSLContext? = null

    /**
     * 获取 SSL 上下文
     */
    fun getSSLContext(): SSLContext? {
        if (sslContext == null) {
            synchronized(this) {
                if (sslContext == null) {
                    try {
                        val tm = getTrustManager()
                        val context = SSLContext.getInstance("TLSv1.2")
                        context.init(null, if (tm != null) arrayOf(tm) else null, null)
                        sslContext = context
                        Logger.com("SSL 上下文初始化成功")
                    } catch (e: Exception) {
                        Logger.comE("SSL 上下文初始化失败", e)
                    }
                }
            }
        }
        return sslContext
    }

    /**
     * 获取信任管理器
     */
    fun getTrustManager(): X509TrustManager? {
        if (trustManager == null) {
            synchronized(this) {
                if (trustManager == null) {
                    try {
                        val trustManagerFactory = TrustManagerFactory.getInstance(
                            TrustManagerFactory.getDefaultAlgorithm()
                        )
                        trustManagerFactory.init(null as KeyStore?)
                        
                        val trustManagers = trustManagerFactory.trustManagers
                        if (trustManagers.size != 1 || trustManagers[0] !is X509TrustManager) {
                            throw IllegalStateException(
                                "Unexpected default trust managers: ${trustManagers.contentToString()}"
                            )
                        }
                        
                        trustManager = trustManagers[0] as X509TrustManager
                        Logger.com("信任管理器初始化成功")
                        
                    } catch (e: Exception) {
                        Logger.comE("信任管理器初始化失败", e)
                    }
                }
            }
        }
        return trustManager
    }

    /**
     * 获取 SSL Socket 工厂
     */
    fun getSSLSocketFactory(): SSLSocketFactory? {
        return getSSLContext()?.socketFactory
    }

    /**
     * 重置 SSL 配置（用于测试或重新初始化）
     */
    fun reset() {
        synchronized(this) {
            trustManager = null
            sslContext = null
            Logger.com("SSL 配置已重置")
        }
    }
}
