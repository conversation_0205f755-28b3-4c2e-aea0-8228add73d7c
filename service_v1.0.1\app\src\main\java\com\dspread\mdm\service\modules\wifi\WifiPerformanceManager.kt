package com.dspread.mdm.service.modules.wifi

import android.content.Context
import com.dspread.mdm.service.utils.log.Logger
import kotlinx.coroutines.*
import java.lang.ref.WeakReference
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.atomic.AtomicLong

/**
 * WiFi性能优化管理器
 * 负责连接超时控制、内存管理和资源清理
 */
class WifiPerformanceManager(context: Context) {
    
    companion object {
        private const val TAG = "[WifiPerformanceManager]"
        
        // 性能配置常量
        private const val DEFAULT_CONNECTION_TIMEOUT = 30000L // 30秒
        private const val DEFAULT_SCAN_TIMEOUT = 10000L // 10秒
        private const val DEFAULT_VALIDATION_TIMEOUT = 15000L // 15秒
        private const val MEMORY_CLEANUP_INTERVAL = 300000L // 5分钟
        private const val PERFORMANCE_LOG_INTERVAL = 60000L // 1分钟
        
        // 内存管理常量
        private const val MAX_CACHED_PROFILES = 50
        private const val MAX_FAILURE_RECORDS = 100
        private const val MAX_PERFORMANCE_RECORDS = 200
    }
    
    private val contextRef = WeakReference(context)
    
    // 性能监控数据
    private val performanceMetrics = ConcurrentHashMap<String, PerformanceMetric>()
    private val connectionAttempts = AtomicLong(0)
    private val connectionSuccesses = AtomicLong(0)
    private val connectionFailures = AtomicLong(0)
    private val totalConnectionTime = AtomicLong(0)
    
    // 超时控制
    private var connectionTimeout = DEFAULT_CONNECTION_TIMEOUT
    private var scanTimeout = DEFAULT_SCAN_TIMEOUT
    private var validationTimeout = DEFAULT_VALIDATION_TIMEOUT
    
    // 协程作用域
    private val performanceScope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    
    // 内存管理
    private var memoryCleanupJob: Job? = null
    private var performanceLogJob: Job? = null
    
    /**
     * 性能指标数据类
     */
    data class PerformanceMetric(
        val operation: String,
        val startTime: Long,
        val endTime: Long,
        val duration: Long,
        val success: Boolean,
        val errorCode: String? = null,
        val memoryUsage: Long = 0
    )
    
    /**
     * 初始化性能管理器
     */
    fun initialize() {
        Logger.wifi("$TAG 初始化WiFi性能管理器")
        
        // 启动内存清理任务
        startMemoryCleanupTask()
        
        // 启动性能日志任务
        startPerformanceLoggingTask()
        
        Logger.wifiI("$TAG WiFi性能管理器初始化完成")
    }
    
    /**
     * 停止性能管理器
     */
    fun shutdown() {
        Logger.wifi("$TAG 停止WiFi性能管理器")
        
        // 取消所有协程任务
        memoryCleanupJob?.cancel()
        performanceLogJob?.cancel()
        performanceScope.cancel()
        
        // 清理性能数据
        performanceMetrics.clear()
        
        Logger.wifiI("$TAG WiFi性能管理器已停止")
    }
    
    /**
     * 设置连接超时时间
     */
    fun setConnectionTimeout(timeout: Long) {
        connectionTimeout = timeout
        Logger.wifi("$TAG 设置连接超时时间: ${timeout}ms")
    }
    
    /**
     * 设置扫描超时时间
     */
    fun setScanTimeout(timeout: Long) {
        scanTimeout = timeout
        Logger.wifi("$TAG 设置扫描超时时间: ${timeout}ms")
    }
    
    /**
     * 设置验证超时时间
     */
    fun setValidationTimeout(timeout: Long) {
        validationTimeout = timeout
        Logger.wifi("$TAG 设置验证超时时间: ${timeout}ms")
    }
    
    /**
     * 获取连接超时时间
     */
    fun getConnectionTimeout(): Long = connectionTimeout
    
    /**
     * 获取扫描超时时间
     */
    fun getScanTimeout(): Long = scanTimeout
    
    /**
     * 获取验证超时时间
     */
    fun getValidationTimeout(): Long = validationTimeout
    
    /**
     * 执行带超时控制的操作
     */
    suspend fun <T> executeWithTimeout(
        operation: String,
        timeout: Long,
        action: suspend () -> T
    ): T {
        val startTime = System.currentTimeMillis()
        val memoryBefore = getMemoryUsage()
        
        return try {
            Logger.wifi("$TAG 开始执行操作: $operation (超时: ${timeout}ms)")
            
            val result = withTimeout(timeout) {
                action()
            }
            
            val endTime = System.currentTimeMillis()
            val duration = endTime - startTime
            val memoryAfter = getMemoryUsage()
            
            // 记录性能指标
            recordPerformanceMetric(
                PerformanceMetric(
                    operation = operation,
                    startTime = startTime,
                    endTime = endTime,
                    duration = duration,
                    success = true,
                    memoryUsage = memoryAfter - memoryBefore
                )
            )
            
            Logger.wifi("$TAG 操作完成: $operation (耗时: ${duration}ms)")
            result
            
        } catch (e: TimeoutCancellationException) {
            val endTime = System.currentTimeMillis()
            val duration = endTime - startTime
            
            // 记录超时指标
            recordPerformanceMetric(
                PerformanceMetric(
                    operation = operation,
                    startTime = startTime,
                    endTime = endTime,
                    duration = duration,
                    success = false,
                    errorCode = "TIMEOUT"
                )
            )
            
            Logger.wifiE("$TAG 操作超时: $operation (${timeout}ms)")
            throw e
        } catch (e: Exception) {
            val endTime = System.currentTimeMillis()
            val duration = endTime - startTime
            
            // 记录错误指标
            recordPerformanceMetric(
                PerformanceMetric(
                    operation = operation,
                    startTime = startTime,
                    endTime = endTime,
                    duration = duration,
                    success = false,
                    errorCode = e.javaClass.simpleName
                )
            )
            
            Logger.wifiE("$TAG 操作异常: $operation", e)
            throw e
        }
    }
    
    /**
     * 记录连接尝试
     */
    fun recordConnectionAttempt() {
        connectionAttempts.incrementAndGet()
    }
    
    /**
     * 记录连接成功
     */
    fun recordConnectionSuccess(duration: Long) {
        connectionSuccesses.incrementAndGet()
        totalConnectionTime.addAndGet(duration)
    }
    
    /**
     * 记录连接失败
     */
    fun recordConnectionFailure() {
        connectionFailures.incrementAndGet()
    }
    
    /**
     * 获取性能统计信息
     */
    fun getPerformanceStats(): Map<String, Any> {
        val attempts = connectionAttempts.get()
        val successes = connectionSuccesses.get()
        val failures = connectionFailures.get()
        val totalTime = totalConnectionTime.get()
        
        val successRate = if (attempts > 0) {
            (successes.toDouble() / attempts * 100).toInt()
        } else 0
        
        val avgConnectionTime = if (successes > 0) {
            totalTime / successes
        } else 0
        
        return mapOf(
            "connectionAttempts" to attempts,
            "connectionSuccesses" to successes,
            "connectionFailures" to failures,
            "successRate" to "$successRate%",
            "averageConnectionTime" to "${avgConnectionTime}ms",
            "totalConnectionTime" to "${totalTime}ms",
            "performanceRecords" to performanceMetrics.size,
            "memoryUsage" to "${getMemoryUsage()}MB",
            "timestamp" to System.currentTimeMillis()
        )
    }
    
    /**
     * 记录性能指标
     */
    private fun recordPerformanceMetric(metric: PerformanceMetric) {
        val key = "${metric.operation}_${metric.startTime}"
        performanceMetrics[key] = metric
        
        // 限制性能记录数量，防止内存泄漏
        if (performanceMetrics.size > MAX_PERFORMANCE_RECORDS) {
            cleanupOldPerformanceRecords()
        }
    }
    
    /**
     * 清理旧的性能记录
     */
    private fun cleanupOldPerformanceRecords() {
        val sortedEntries = performanceMetrics.entries.sortedBy { it.value.startTime }
        val toRemove = sortedEntries.take(performanceMetrics.size - MAX_PERFORMANCE_RECORDS + 10)
        
        toRemove.forEach { entry ->
            performanceMetrics.remove(entry.key)
        }
        
        Logger.wifi("$TAG 清理了 ${toRemove.size} 条旧的性能记录")
    }
    
    /**
     * 获取内存使用量（MB）
     */
    private fun getMemoryUsage(): Long {
        val runtime = Runtime.getRuntime()
        val usedMemory = runtime.totalMemory() - runtime.freeMemory()
        return usedMemory / (1024 * 1024) // 转换为MB
    }
    
    /**
     * 启动内存清理任务
     */
    private fun startMemoryCleanupTask() {
        memoryCleanupJob = performanceScope.launch {
            while (isActive) {
                try {
                    delay(MEMORY_CLEANUP_INTERVAL)
                    performMemoryCleanup()
                } catch (e: Exception) {
                    Logger.wifiE("$TAG 内存清理任务异常", e)
                }
            }
        }
    }
    
    /**
     * 启动性能日志任务
     */
    private fun startPerformanceLoggingTask() {
        performanceLogJob = performanceScope.launch {
            while (isActive) {
                try {
                    delay(PERFORMANCE_LOG_INTERVAL)
                    logPerformanceStats()
                } catch (e: Exception) {
                    Logger.wifiE("$TAG 性能日志任务异常", e)
                }
            }
        }
    }
    
    /**
     * 执行内存清理
     */
    private fun performMemoryCleanup() {
        val beforeMemory = getMemoryUsage()
        
        // 清理旧的性能记录
        if (performanceMetrics.size > MAX_PERFORMANCE_RECORDS) {
            cleanupOldPerformanceRecords()
        }
        
        // 建议垃圾回收
        System.gc()
        
        val afterMemory = getMemoryUsage()
        val cleaned = beforeMemory - afterMemory
        
        if (cleaned > 0) {
            Logger.wifi("$TAG 内存清理完成，释放: ${cleaned}MB")
        }
    }
    
    /**
     * 记录性能统计日志
     */
    private fun logPerformanceStats() {
        val stats = getPerformanceStats()
        Logger.wifiI("$TAG 性能统计: $stats")
    }
}
