package com.dspread.mdm.service.modules.remoteview

import android.content.Context
import com.dspread.mdm.service.modules.BaseModuleManager
import com.dspread.mdm.service.modules.remoteview.model.RemoteViewConfig
import com.dspread.mdm.service.modules.remoteview.model.RemoteViewEvent
import com.dspread.mdm.service.modules.remoteview.model.RemoteViewPerformanceStats
import com.dspread.mdm.service.modules.remoteview.model.RemoteViewStatus
import com.dspread.mdm.service.modules.remoteview.model.ScreenCaptureResult
import com.dspread.mdm.service.modules.remoteview.model.WebSocketConnectionState
import com.dspread.mdm.service.utils.log.Logger
import com.dspread.mdm.service.network.websocket.WebSocketCenter
import com.dspread.mdm.service.platform.api.screen.ScreenCaptureApi
import com.dspread.mdm.service.platform.api.screen.ScreenManagerApi
import com.dspread.mdm.service.services.MediaProjectionService
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow

/**
 * Remote View 管理器
 * 负责Remote View功能的生命周期管理
 */
class RemoteViewManager(
    private val context: Context,
    private var config: RemoteViewConfig = RemoteViewConfig(
        websocketUrl = RemoteViewWebSocketManager.getRemoteViewWebSocketUrl(context)
    )
) : BaseModuleManager() {

    // 核心组件
    private var webSocketManager: RemoteViewWebSocketManager? = null
    private var screenCaptureApi: ScreenCaptureApi? = null
    private var captureJob: Job? = null
    private var managerScope: CoroutineScope? = null

    // 状态管理
    private val _remoteViewStatus = MutableStateFlow(RemoteViewStatus.STOPPED)
    val remoteViewStatus: StateFlow<RemoteViewStatus> = _remoteViewStatus.asStateFlow()

    // 添加简单的boolean标志，避免复杂状态机问题
    @Volatile
    private var isViewExecuting = false

    private val _connectionState = MutableStateFlow(WebSocketConnectionState())
    val connectionState: StateFlow<WebSocketConnectionState> = _connectionState.asStateFlow()

    private val _performanceStats = MutableStateFlow(RemoteViewPerformanceStats())
    val performanceStats: StateFlow<RemoteViewPerformanceStats> = _performanceStats.asStateFlow()

    // 帧去重：保存上一帧的哈希值，节省流量
    private var lastFrameHash: String? = null
    private var duplicateFrameCount = 0
    private var totalFrameCount = 0

    // 事件回调
    var onEvent: ((RemoteViewEvent) -> Unit)? = null

    override fun getModuleName(): String = "RemoteView"

    override suspend fun onInitialize(): Result<Unit> {
        return try {
            Logger.remote("初始化Remote View管理器")

            managerScope = CoroutineScope(Dispatchers.Main + SupervisorJob())
            screenCaptureApi = ScreenCaptureApi.getInstance(context)

            // 初始化MediaProjection权限和服务
            initializeMediaProjection()

            Logger.remote("Remote View管理器初始化完成")
            Result.success(Unit)
        } catch (e: Exception) {
            Logger.remoteE("Remote View管理器初始化失败", e)
            Result.failure(e)
        }
    }

    override suspend fun onStart(): Result<Unit> {
        return try {
            // 优先检查简单boolean标志
            if (isViewExecuting) {
                Logger.remote("Remote View服务已在运行中（boolean检查）")
                return Result.success(Unit)
            }

            // 检查当前状态，如果已经在运行则忽略重复启动
            val currentStatus = _remoteViewStatus.value
            if (currentStatus == RemoteViewStatus.RUNNING) {
                Logger.remote("Remote View服务已在运行中，忽略重复启动指令")
                return Result.success(Unit)
            }

            if (currentStatus == RemoteViewStatus.STARTING) {
                Logger.remote("Remote View服务正在启动中，忽略重复启动指令")
                return Result.success(Unit)
            }

            // 简单粗暴的状态处理
            if (currentStatus == RemoteViewStatus.STOPPING || currentStatus == RemoteViewStatus.STARTING) {
                Logger.remote("Remote View状态异常($currentStatus)，直接强制重置为STOPPED")
                // 不管什么状态，直接强制重置
                updateRemoteViewStatus(RemoteViewStatus.STOPPED)
                Logger.remote("状态已强制重置，继续启动")
            }

            Logger.remote("启动Remote View服务")
            updateRemoteViewStatus(RemoteViewStatus.STARTING)

            // 重置帧去重状态，开始新的会话
            resetFrameDeduplicationState()

            // 启动Remote View屏幕模式：点亮屏幕并保持常亮
            val screenResult = ScreenManagerApi.enableRemoteViewScreenMode(context)
            if (screenResult.isFailure) {
                Logger.remoteW("启动屏幕常亮模式失败，但继续启动服务")
            } else {
                Logger.remote("屏幕常亮模式启动成功")
            }

            // 检查WebSocket连接状态
            initializeWebSocketManager()
            val webSocketConnected = webSocketManager?.isConnected() == true

            if (!webSocketConnected) {
                Logger.remote("WebSocket未连接，开始连接...")
                val connected = webSocketManager?.connect() ?: false
                if (!connected) {
                    // 连接失败时恢复屏幕设置
                    ScreenManagerApi.disableRemoteViewScreenMode(context)
                    updateRemoteViewStatus(RemoteViewStatus.ERROR)
                    return Result.failure(Exception("WebSocket连接失败，无法启动截屏"))
                }
                Logger.remote("WebSocket连接成功")
            } else {
                Logger.remote("WebSocket已连接，直接开始截屏")
            }

            // 开始截屏循环（单例检查在startScreenCapture内部）
            startScreenCapture()

            // 设置boolean标志
            isViewExecuting = true

            updateRemoteViewStatus(RemoteViewStatus.RUNNING)
            onEvent?.invoke(RemoteViewEvent.Started)

            Logger.remote("Remote View服务启动成功")
            Result.success(Unit)
        } catch (e: Exception) {
            Logger.remoteE("Remote View服务启动失败", e)
            // 启动失败时恢复屏幕设置
            ScreenManagerApi.disableRemoteViewScreenMode(context)
            updateRemoteViewStatus(RemoteViewStatus.ERROR)
            onEvent?.invoke(RemoteViewEvent.Error("启动失败: ${e.message}", e))
            Result.failure(e)
        }
    }

    override suspend fun onStop(): Result<Unit> {
        return try {
            Logger.remote("停止Remote View服务")

            // 立即设置boolean标志为false，让循环退出
            isViewExecuting = false

            // 不设置STOPPING状态，避免状态卡死
            // updateRemoteViewStatus(RemoteViewStatus.STOPPING)

            // 停止截屏循环
            stopScreenCapture()
            delay(50)

            // 释放MediaProjection资源
            releaseMediaProjectionResources()
            delay(100)

            // 停止MediaProjection前台服务
            stopMediaProjectionService()
            delay(200)

            // 断开WebSocket连接
            webSocketManager?.disconnect()
            webSocketManager?.release()
            webSocketManager = null

            // 恢复屏幕设置
            ScreenManagerApi.disableRemoteViewScreenMode(context)

            // 清理帧去重状态
            resetFrameDeduplicationState()

            updateRemoteViewStatus(RemoteViewStatus.STOPPED)
            onEvent?.invoke(RemoteViewEvent.Stopped)

            Logger.remote("Remote View服务已停止")
            Result.success(Unit)
        } catch (e: Exception) {
            Logger.remoteE("Remote View服务停止失败", e)
            // 即使出现异常也要尝试恢复屏幕设置和停止服务
            try {
                ScreenManagerApi.disableRemoteViewScreenMode(context)
                stopMediaProjectionService()
                webSocketManager?.disconnect()
                webSocketManager?.release()
                webSocketManager = null
            } catch (cleanupException: Exception) {
                Logger.remoteE("清理资源时发生异常", cleanupException)
            }
            // 确保状态重置为STOPPED，而不是ERROR，这样可以重新启动
            updateRemoteViewStatus(RemoteViewStatus.STOPPED)
            Result.failure(e)
        }
    }

    /**
     * 初始化WebSocket管理器
     */
    private fun initializeWebSocketManager() {
        webSocketManager = RemoteViewWebSocketManager(context, config).apply {
            onConnectionStateChanged = { state ->
                _connectionState.value = state
                if (state.isConnected) {
                    onEvent?.invoke(RemoteViewEvent.WebSocketConnected(config.websocketUrl))
                } else {
                    onEvent?.invoke(
                        RemoteViewEvent.WebSocketDisconnected(
                            state.lastError ?: "未知原因"
                        )
                    )
                }
            }
            
            onMessageReceived = { message ->
                Logger.remote("收到WebSocket消息: $message")
                // 这里可以处理服务器发送的控制命令
            }
            
            onError = { message, throwable ->
                Logger.remoteE("WebSocket错误: $message", throwable)
                onEvent?.invoke(RemoteViewEvent.Error(message, throwable))
            }
        }
    }

    /**
     * 开始截屏循环 - 单例模式，避免重复开启
     */
    private fun startScreenCapture() {
        // 检查是否已有活跃的截屏循环
        if (captureJob?.isActive == true) {
            Logger.remote("截屏循环已在运行中，跳过重复开启")
            return
        }

        // 取消旧的job（如果存在）
        captureJob?.cancel()

        captureJob = managerScope?.launch {
            Logger.remote("开始截屏循环，间隔: ${config.captureInterval}ms")

            // 使用简单的boolean检查
            while (isActive && isViewExecuting) {
                try {
                    // 优先检查boolean标志，立即响应停止命令
                    if (!isViewExecuting) {
                        Logger.remote("检测到boolean标志变更，立即停止截屏循环")
                        break
                    }

                    // 备用检查：服务状态
                    if (_remoteViewStatus.value != RemoteViewStatus.RUNNING) {
                        Logger.remote("检测到服务状态变更，立即停止截屏循环")
                        break
                    }

                    // 检查WebSocket连接状态，如果连接失败则停止截屏
                    val webSocketConnected = webSocketManager?.isConnected() == true
                    if (!webSocketConnected) {
                        val connectionState = webSocketManager?.getConnectionState()
                        val reconnectCount = connectionState?.reconnectCount ?: 0

                        Logger.remoteW("WebSocket未连接，跳过本次截屏 - 状态: ${connectionState?.lastError ?: "未知"}, 重连次数: $reconnectCount")

                        // 如果连续失败次数过多，停止RemoteView服务
                        if (reconnectCount > 10) {
                            Logger.remoteE("WebSocket连续失败次数过多($reconnectCount)，停止RemoteView服务")
                            updateRemoteViewStatus(RemoteViewStatus.ERROR)
                            onEvent?.invoke(RemoteViewEvent.Error("WebSocket连接持续失败，服务已停止", null))
                            break
                        }

                        // 根据失败次数调整等待时间
                        val waitTime = when {
                            reconnectCount > 5 -> config.captureInterval * 5 // 失败多次时等待更长时间
                            reconnectCount > 3 -> config.captureInterval * 3
                            else -> config.captureInterval
                        }

                        delay(waitTime)
                        continue
                    }

                    // 执行截屏（使用ScreenCaptureApi）
                    val screenshotData = screenCaptureApi?.captureScreenToBytes(
                        quality = config.compressionQuality
                    )

                    // 构造结果对象（兼容原有逻辑）
                    val result = if (screenshotData != null) {
                        ScreenCaptureResult(
                            success = true,
                            data = screenshotData,
                            captureTime = 0L,
                            compressTime = 0L,
                            dataSize = screenshotData.size
                        )
                    } else {
                        ScreenCaptureResult(
                            success = false,
                            data = null,
                            error = "截屏失败"
                        )
                    }

                    if (result?.success == true && result.data != null) {
                        totalFrameCount++

                        // 帧去重检查：根据配置决定是否启用去重
                        val isDuplicate = if (config.enableFrameDeduplication) {
                            val currentFrameHash = calculateFrameHash(result.data)
                            val duplicate = currentFrameHash == lastFrameHash
                            if (!duplicate) {
                                lastFrameHash = currentFrameHash
                            }
                            duplicate
                        } else {
                            false // 禁用去重时，所有帧都不重复
                        }

                        if (isDuplicate) {
                            duplicateFrameCount++
                            // 减少重复帧日志频率，只在每5个重复帧记录一次
                            if (duplicateFrameCount % 5 == 0) {
                                val savePercentage = if (totalFrameCount > 0) {
                                    String.format("%.1f", duplicateFrameCount * 100.0 / totalFrameCount)
                                } else {
                                    "0.0"
                                }
                                Logger.remote("检测到重复帧，跳过发送 (重复帧: $duplicateFrameCount/$totalFrameCount, 节省: $savePercentage%)")
                            }
                            // 更新性能统计（标记为跳过）
                            updatePerformanceStats(result, true, skipped = true)
                        } else {
                            // 发送前再次检查状态，确保不在停止过程中发送数据
                            if (_remoteViewStatus.value != RemoteViewStatus.RUNNING) {
                                Logger.remote("检测到服务正在停止，取消数据发送")
                                break
                            }

                            // 发送截屏数据
                            val sent = webSocketManager?.sendBinaryData(result.data) ?: false

                            // 更新性能统计
                            updatePerformanceStats(result, sent)

                            if (sent) {
                                // 减少成功日志频率，只在每10帧记录一次详细信息
                                if (totalFrameCount % 10 == 0) {
                                    Logger.remote(
                                        "截屏发送成功 - " +
                                        "截屏耗时: ${result.captureTime}ms, " +
                                        "压缩耗时: ${result.compressTime}ms, " +
                                        "数据大小: ${result.dataSize} bytes"
                                    )
                                }
                            } else {
                                Logger.remoteW("截屏数据发送失败")
                            }
                        }
                    } else {
                        Logger.remoteW("截屏失败: ${result?.error}")
                        updatePerformanceStats(result, false)
                    }

                } catch (e: CancellationException) {
                    // 协程取消异常是正常的停止流程，不记录为错误
                    Logger.remote("截屏循环被取消（正常停止）")
                    break
                } catch (e: Exception) {
                    // 只有非取消异常才记录为错误
                    Logger.remoteE("截屏循环异常", e)
                    onEvent?.invoke(RemoteViewEvent.Error("截屏异常: ${e.message}", e))

                    // 检查是否应该继续运行
                    if (_remoteViewStatus.value != RemoteViewStatus.RUNNING) {
                        break
                    }
                }

                // 等待前检查boolean标志，立即响应停止
                if (!isViewExecuting) {
                    Logger.remote("检测到boolean标志变更，停止等待")
                    break
                }

                // 备用检查：服务状态
                if (_remoteViewStatus.value != RemoteViewStatus.RUNNING) {
                    Logger.remote("检测到服务状态变更，停止等待")
                    break
                }

                // 使用更高的截屏频率提升流畅度
                // 等待下次截屏
                try {
                    val frameInterval = if (config.captureInterval > 100) {
                        // 如果配置的间隔太长，50ms间隔（约20FPS）
                        50L
                    } else {
                        config.captureInterval
                    }
                    delay(frameInterval)
                } catch (e: CancellationException) {
                    Logger.remote("等待被取消，截屏循环结束")
                    break
                }
            }

            Logger.remote("截屏循环已结束")
        }
    }

    /**
     * 停止截屏循环
     */
    private fun stopScreenCapture() {
        try {
            // 立即取消截屏任务，使用CancellationException
            captureJob?.cancel(CancellationException("Remote View服务停止"))
            captureJob = null
            Logger.remote("截屏循环已停止")
        } catch (e: Exception) {
            Logger.remoteE("停止截屏循环异常", e)
        }
    }

    /**
     * 计算帧哈希值，用于去重检测
     */
    private fun calculateFrameHash(data: ByteArray): String {
        return try {
            val digest = java.security.MessageDigest.getInstance("MD5")
            val hashBytes = digest.digest(data)
            hashBytes.joinToString("") { "%02x".format(it) }
        } catch (e: Exception) {
            Logger.remoteE("计算帧哈希失败", e)
            // 返回时间戳作为备用哈希
            System.currentTimeMillis().toString()
        }
    }

    /**
     * 重置帧去重状态
     */
    private fun resetFrameDeduplicationState() {
        lastFrameHash = null
        duplicateFrameCount = 0
        totalFrameCount = 0
        Logger.remote("帧去重状态已重置")
    }

    /**
     * 更新性能统计
     */
    private fun updatePerformanceStats(result: ScreenCaptureResult?, sent: Boolean, skipped: Boolean = false) {
        val currentStats = _performanceStats.value
        val newStats = currentStats.copy(
            totalFrames = currentStats.totalFrames + 1,
            // 跳过的帧不计入成功或失败，只计入总数
            successFrames = if (sent && !skipped) currentStats.successFrames + 1 else currentStats.successFrames,
            failedFrames = if (!sent && !skipped) currentStats.failedFrames + 1 else currentStats.failedFrames,
            averageCaptureTime = if (result != null) {
                (currentStats.averageCaptureTime + result.captureTime) / 2
            } else currentStats.averageCaptureTime,
            averageCompressTime = if (result != null) {
                (currentStats.averageCompressTime + result.compressTime) / 2
            } else currentStats.averageCompressTime,
            averageDataSize = if (result != null) {
                (currentStats.averageDataSize + result.dataSize) / 2
            } else currentStats.averageDataSize
        )

        _performanceStats.value = newStats
        // 减少事件触发频率，每10帧触发一次
        if (newStats.totalFrames % 10L == 0L) {
            onEvent?.invoke(RemoteViewEvent.FrameCaptured(newStats))
        }
    }

    /**
     * 更新Remote View状态
     */
    private fun updateRemoteViewStatus(status: RemoteViewStatus) {
        _remoteViewStatus.value = status
        Logger.remote("Remote View状态更新: $status")
    }

    /**
     * 更新配置
     */
    fun updateConfig(newConfig: RemoteViewConfig) {
        Logger.remote("更新Remote View配置")
        this.config = newConfig
        onEvent?.invoke(RemoteViewEvent.ConfigUpdated(newConfig))
        
        // 如果正在运行，需要重启服务以应用新配置
        if (_remoteViewStatus.value == RemoteViewStatus.RUNNING) {
            managerScope?.launch {
                stop()
                delay(1000) // 等待完全停止
                start()
            }
        }
    }

    /**
     * 设置bucket路径
     */
    fun setBucketPath(bucketPath: String) {
        Logger.remote("设置bucket路径: $bucketPath")
        val newConfig = config.copy(bucketPath = bucketPath)
        updateConfig(newConfig)
    }

    /**
     * 获取当前配置
     */
    fun getCurrentConfig(): RemoteViewConfig = config

    /**
     * 释放MediaProjection相关资源
     */
    private fun releaseMediaProjectionResources() {
        try {
            // 停止MediaProjection实例
            val mediaProjection = MediaProjectionService.getMediaProjection()
            mediaProjection?.stop()

            // 清空截屏工具引用
            screenCaptureApi = null

        } catch (e: Exception) {
            Logger.remoteE("释放MediaProjection资源失败", e)
        }
    }

    /**
     * 停止MediaProjection前台服务
     */
    private fun stopMediaProjectionService() {
        try {
            MediaProjectionService.stopService(context)
        } catch (e: Exception) {
            Logger.remoteE("停止MediaProjection前台服务失败", e)
        }
    }

    /**
     * 释放资源
     */
    fun release() {
        Logger.remote("释放Remote View管理器资源")

        // 不使用runBlocking，改为异步处理
        try {
            // 立即取消所有协程
            managerScope?.cancel()
            captureJob?.cancel()

            // 确保MediaProjection资源和服务被释放
            releaseMediaProjectionResources()
            stopMediaProjectionService()

            // 释放WebSocket资源
            webSocketManager?.release()
            webSocketManager = null

            // 异步恢复屏幕设置，避免阻塞
            GlobalScope.launch {
                try {
                    ScreenManagerApi.disableRemoteViewScreenMode(context)
                    Logger.remote("资源释放时屏幕设置恢复成功")
                } catch (e: Exception) {
                    Logger.remoteE("资源释放时屏幕设置恢复失败", e)
                }
            }

            Logger.remote("Remote View管理器资源释放完成")
        } catch (e: Exception) {
            Logger.remoteE("释放Remote View管理器资源异常", e)
        }
    }

    /**
     * 初始化MediaProjection权限和服务
     */
    private suspend fun initializeMediaProjection() {
        return withContext(Dispatchers.Main) {
            try {
                Logger.remote("初始化MediaProjection权限和服务")

                // 检查是否已经有MediaProjection权限数据
                val existingData = RequestMediaProjectionActivity.getMediaProjectionData()
                if (existingData != null) {
                    Logger.remote("发现已有MediaProjection权限，启动服务")

                    // 启动MediaProjection服务
                    MediaProjectionService.startService(context, existingData)

                    // 等待服务启动
                    delay(1000)

                    val isAvailable = MediaProjectionService.isMediaProjectionAvailable()
                    if (isAvailable) {
                        Logger.remote("MediaProjection服务启动成功")
                    } else {
                        Logger.remoteW("MediaProjection服务启动失败，将使用Shell命令截屏")
                    }
                } else {
                    Logger.remoteW("没有MediaProjection权限，将使用Shell命令截屏")
                }

            } catch (e: Exception) {
                Logger.remoteE("初始化MediaProjection异常", e)
            }
        }
    }
}
