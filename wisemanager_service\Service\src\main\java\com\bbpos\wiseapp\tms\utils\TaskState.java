package com.bbpos.wiseapp.tms.utils;

public class TaskState {
	public static final String TODO = "todo";
	public static final String WATING = "wating";//加入到IntentService队列中，等待执行
	public static final String EXECUTE_WATING ="W01";
	public static final String DOWNLOAD_ING ="A01";
	public static final String DOWNLOAD_SUCCESS = "A03";
	public static final String DOWNLOAD_FAILED = "A04";
	
	public static final String SUCCESSED= "F01";
	public static final String FAILED = "E05";
	public static final String SERVER_FAILED = "E04";
	public static final String RELY_FAILED = "E02";
	public static final String TASK_CANCEL="E01";
	public static final String LOW_BAT = "E03";
	public static final String INSTALL_WAITING = "B01";
	public static final String INSTALL_ING = "B02";
	public static final String INSTALL_SUCCESS = "B03";
	public static final String INSTALL_FAILED = "B04";
	public static final String INSTALL_OVERRIDE = "B05";
	public static final String UPDATE_ING = "C02";
	public static final String UPDATE_SUCCESS = "C03";
	public static final String UPDATE_FAILED = "C04";
	public static final String UPDATE_DOWNGRADE_FORBIDDEN = "C05";
	public static final String UNINSTALL_ING = "D01";
	public static final String UNINSTALL_FAILED = "D03";
	public static final String UNINSTALL_SUCCESS = "D02";
	public static final String UNINSTALL_EXPIRE = "D04";
	public static final String RULEBASED_STARTING = "R01";
	public static final String RULEBASED_EXECUTING = "R02";
	public static final String RULEBASED_SUCCESS = "R03";
	public static final String RULEBASED_EXPIRED = "R04";		//過時

	public static final String DELAY = "delay";//已加入定时器的任务，时间更改
	public static final String DELETE = "delete";//已加入定时器的任务，被取消
	
	public static final String NO_NET = "no_net";//任务执行时间已到，由于无网络挂起
}
