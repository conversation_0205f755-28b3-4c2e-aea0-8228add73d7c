package com.bbpos.wiseapp.tms.traffic.model;

import android.database.Cursor;

public class TrafficData extends BaseDataModel{
	/**数据日期 格式yyyyMMdd*/
	public static final String DATA_DATE = "dataDate"; //pk
	public static final String PKG_NAME = "pkgName"; //pk
	public static final String APP_NAME = "appName";
	public static final String VERSION_CODE = "versionCode";//pk 
	public static final String VERSION_NAME = "versionName";
	
	/**网络类型包括 wifi 移动网络*/
	public static final String NET_TYPE = "netType";//pk
	/**下载流量*/
	public static final String RX_BYTES = "rxBytes";
	/**上传流量*/
	public static final String TX_BYTES = "txBytes";
	
	public static final String DATA_TYPE_LAST_TRAFFIC = "last_traffic";
	public static final String DATA_TYPE_TRAFFIC = "traffic";
	
	public String dataDate;
	
	public String pkgName;
	
	public String appName;
	
	public int versionCode;
	
	public String versionName;
	
	public String netType;
	
	public long rxBytes;
	
	public long txBytes;
	
	public TrafficData(Cursor cursor,String dataType){
		if(DATA_TYPE_LAST_TRAFFIC.equals(dataType)){
			this.pkgName = getCursorString(cursor,PKG_NAME);
			this.versionCode = getCursorInt(cursor,VERSION_CODE);
			this.rxBytes = getCursorLong(cursor, RX_BYTES);
			this.txBytes = getCursorLong(cursor, TX_BYTES);
		}
		else if(DATA_TYPE_TRAFFIC.equals(dataType)){
			this.dataDate = getCursorString(cursor, DATA_DATE);
			this.pkgName = getCursorString(cursor,PKG_NAME);
			this.appName = getCursorString(cursor, APP_NAME);
			this.versionCode = getCursorInt(cursor,VERSION_CODE);
			this.versionName = getCursorString(cursor, VERSION_NAME);
			this.netType = getCursorString(cursor, NET_TYPE);
			this.rxBytes = getCursorLong(cursor, RX_BYTES);
			this.txBytes = getCursorLong(cursor,TX_BYTES);
		}
		
	}
	
	public TrafficData(String dataDate, String pkgName, String appName, int versionCode, String versionName,
			String netType, long rxBytes, long txBytes) {
		super();
		this.dataDate = dataDate;
		this.pkgName = pkgName;
		this.appName = appName;
		this.versionCode = versionCode;
		this.versionName = versionName;
		this.netType = netType;
		this.rxBytes = rxBytes;
		this.txBytes = txBytes;
	}
	
}
