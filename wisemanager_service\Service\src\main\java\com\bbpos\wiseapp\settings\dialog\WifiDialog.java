/*
 * Copyright (C) 2010 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.bbpos.wiseapp.settings.dialog;

import android.app.AlertDialog;
import android.content.Context;
import android.content.DialogInterface;
import android.net.wifi.ScanResult;
import android.net.wifi.WifiManager;
import android.os.Bundle;
import android.text.Editable;
import android.text.InputType;
import android.text.TextWatcher;
import android.util.DisplayMetrics;
import android.view.Display;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.Button;
import android.widget.CheckBox;
import android.widget.CompoundButton;
import android.widget.EditText;
import android.widget.TextView;

import com.bbpos.wiseapp.service.R;
import com.bbpos.wiseapp.settings.activity.WiFiActivity;

import java.lang.reflect.Constructor;
import java.lang.reflect.InvocationTargetException;

public class WifiDialog extends AlertDialog implements View.OnClickListener {
    public interface WifiDialogListener {
        void onForget();
        void onSubmit(ScanResult scanResult, String password);
    }

    private Context mContext;
    private final int mMode;
    private final WifiDialogListener mListener;

    private View mView;
    private boolean mHideSubmitButton;

    private final ScanResult mScanResult;
    private EditText et_password;
    private EditText et_wifissid;
    private CheckBox checkbox;
    private TextView tv_cancel;
    private TextView tv_connect;

    public WifiDialog(Context context, WifiDialogListener listener, ScanResult scanResult, int mode, boolean hideSubmitButton) {
        this(context, listener, scanResult, mode);
        mHideSubmitButton = hideSubmitButton;
    }

    public WifiDialog(Context context, WifiDialogListener listener, ScanResult scanResult, int mode) {
        super(context);
        mMode = mode;
        mListener = listener;
        mScanResult = scanResult;
        mHideSubmitButton = false;
        mContext = context;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        mView = getLayoutInflater().inflate(R.layout.dialog_wifi_connect, null);
        setView(mView);
        setInverseBackgroundForced(true);
        super.onCreate(savedInstanceState);

        if (mMode == 1) {
            TextView tv_header = findViewById(R.id.tv_header);
            tv_header.setText(mScanResult.SSID);
            TextView tv_name = findViewById(R.id.tv_wifissid);
            tv_name.setVisibility(View.GONE);
            TextView et_name = findViewById(R.id.et_wifissid);
            et_name.setVisibility(View.GONE);
        } else {
            TextView tv_header = findViewById(R.id.tv_header);
            tv_header.setText(getContext().getString(R.string.add_wifi));
            et_wifissid = findViewById(R.id.et_wifissid);
        }

        et_password = findViewById(R.id.et_password);
        et_password.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
                if (start+count >= 8) {
                    tv_connect.setEnabled(true);
                    tv_connect.setTextColor(mContext.getResources().getColor(R.color.theme_green));
                } else {
                    tv_connect.setEnabled(false);
                    tv_connect.setTextColor(mContext.getResources().getColor(R.color.divider));
                }
            }

            @Override
            public void afterTextChanged(Editable s) {

            }
        });
        checkbox = findViewById(R.id.cb_showpwd);
        checkbox.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                if (isChecked) {
                    et_password.setInputType(InputType.TYPE_CLASS_TEXT | InputType.TYPE_TEXT_VARIATION_VISIBLE_PASSWORD);
                    et_password.setSelection(et_password.length());
                } else {
                    et_password.setInputType(InputType.TYPE_CLASS_TEXT | InputType.TYPE_TEXT_VARIATION_PASSWORD);
                    et_password.setSelection(et_password.length());
                }
            }
        });

        tv_cancel = findViewById(R.id.tv_cancel);
        tv_connect = findViewById(R.id.tv_connect);
        tv_cancel.setOnClickListener(this);
        tv_connect.setOnClickListener(this);
        tv_connect.setEnabled(false);
        tv_connect.setTextColor(mContext.getResources().getColor(R.color.divider));

        if (mHideSubmitButton) {
//            mController.hideSubmitButton();
        } else {
            /* During creation, the submit button can be unavailable to determine
             * visibility. Right after creation, update button visibility */
//            mController.enableSubmitIfAppropriate();
        }

//        if (mAccessPoint == null) {
//            mController.hideForgetButton();
//        }

        /*
         * 将对话框的大小按屏幕大小的百分比设置
         */
        Window window = getWindow() ;
        WindowManager m =(WindowManager) mContext.getSystemService(Context.WINDOW_SERVICE);
        DisplayMetrics dm = new DisplayMetrics();; // 获取屏幕宽、高用
        m.getDefaultDisplay().getMetrics(dm);
        WindowManager.LayoutParams p = window.getAttributes(); // 获取对话框当前的参数值
        p.width = (int) (dm.widthPixels * 0.9);
        window.setAttributes(p);
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.tv_cancel:
                dismiss();
                break;
            case R.id.tv_connect:
                //连接WIFI
                if (mMode == 1) {
                    mListener.onSubmit(mScanResult, et_password.getText().toString());
                } else {
                    try {
                        ScanResult sr1 = null;
                        Constructor<ScanResult> ctor = null;
                        ctor =ScanResult.class.getDeclaredConstructor(ScanResult.class);
                        ctor.setAccessible(true);
                        ScanResult sr = ctor.newInstance(sr1);
                        sr.SSID = et_wifissid.getText().toString();
                        sr.capabilities = "WPA2-PSK";

                        mListener.onSubmit(sr, et_password.getText().toString());
                    } catch (NoSuchMethodException e) {
                        e.printStackTrace();
                    } catch (IllegalAccessException e) {
                        e.printStackTrace();
                    } catch (InstantiationException e) {
                        e.printStackTrace();
                    } catch (InvocationTargetException e) {
                        e.printStackTrace();
                    }
                }
                dismiss();
                break;
        }
    }
}
