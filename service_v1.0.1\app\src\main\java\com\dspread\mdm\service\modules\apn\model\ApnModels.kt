package com.dspread.mdm.service.modules.apn.model

import android.telephony.TelephonyManager
import org.json.JSONObject

/**
 * APN配置数据类
 */
data class ApnConfig(
    val id: Long = -1,                       // ⭐ APN ID
    val name: String,                        // APN名称
    val apn: String,                         // APN地址
    val proxy: String = "",                  // 代理地址
    val port: String = "",                   // 代理端口
    val user: String = "",                   // 用户名
    val password: String = "",               // 密码
    val server: String = "",                 // 服务器地址
    val mmsc: String = "",                   // MMSC地址
    val mmsProxy: String = "",               // MMS代理
    val mmsPort: String = "",                // MMS端口
    val mcc: String = "",                    // 移动国家代码
    val mnc: String = "",                    // 移动网络代码
    val numeric: String = "",                // 数字标识符(mcc+mnc)
    val authType: Int = 0,                   // 认证类型
    val type: String = "default,supl",       // APN类型
    val protocol: String = "IP",             // 协议类型
    val roamingProtocol: String = "IP",      // 漫游协议
    val carrierEnabled: Boolean = true,      // 运营商启用
    val bearer: String = "",                 // 承载类型
    val bearerBitmask: String = "",          // 承载位掩码
    val mvnoType: String = "",               // MVNO类型
    val mvnoMatchData: String = "",          // MVNO匹配数据
    val current: String = "0",               // 是否当前选中
    val proId: String = "",                  // 关联的APN profile ID
    val priority: Int = 1,                   // 优先级
    val simSlot: Int = 0,                    // SIM卡槽位（0或1）
    val isDefault: Boolean = false,          // 是否为默认APN
    val isActive: Boolean = true             // 是否激活
) {
    
    /**
     * 检查配置是否有效
     */
    fun isValid(): Boolean {
        return name.isNotEmpty() && 
               apn.isNotEmpty() && 
               simSlot in 0..1
    }
    
    /**
     * 检查是否为MMS APN
     */
    fun isMmsApn(): Boolean {
        return type.contains("mms", ignoreCase = true)
    }
    
    /**
     * 检查是否为默认数据APN
     */
    fun isDataApn(): Boolean {
        return type.contains("default", ignoreCase = true)
    }
    
    /**
     * 转换为JSON对象
     */
    fun toJson(): JSONObject {
        return JSONObject().apply {
            put("id", id)
            put("name", name)
            put("apn", apn)
            put("proxy", proxy)
            put("port", port)
            put("user", user)
            put("password", password)
            put("server", server)
            put("mmsc", mmsc)
            put("mmsProxy", mmsProxy)
            put("mmsPort", mmsPort)
            put("mcc", mcc)
            put("mnc", mnc)
            put("numeric", numeric)
            put("authType", authType)
            put("type", type)
            put("protocol", protocol)
            put("roamingProtocol", roamingProtocol)
            put("carrierEnabled", carrierEnabled)
            put("bearer", bearer)
            put("bearerBitmask", bearerBitmask)
            put("mvnoType", mvnoType)
            put("mvnoMatchData", mvnoMatchData)
            put("current", current)
            put("proId", proId)
            put("priority", priority)
            put("simSlot", simSlot)
            put("isDefault", isDefault)
            put("isActive", isActive)
        }
    }
    
    companion object {
        /**
         * 从JSON对象创建配置
         */
        fun fromJson(json: JSONObject): ApnConfig? {
            return try {
                ApnConfig(
                    id = json.optLong("id", -1),
                    name = json.getString("name"),
                    apn = json.getString("apn"),
                    proxy = json.optString("proxy", ""),
                    port = json.optString("port", ""),
                    user = json.optString("user", ""),
                    password = json.optString("password", ""),
                    server = json.optString("server", ""),
                    mmsc = json.optString("mmsc", ""),
                    mmsProxy = json.optString("mmsProxy", ""),
                    mmsPort = json.optString("mmsPort", ""),
                    mcc = json.optString("mcc", ""),
                    mnc = json.optString("mnc", ""),
                    numeric = json.optString("numeric", ""),
                    authType = json.optInt("authType", 0),
                    type = json.optString("type", "default,supl"),
                    protocol = json.optString("protocol", "IP"),
                    roamingProtocol = json.optString("roamingProtocol", "IP"),
                    carrierEnabled = json.optBoolean("carrierEnabled", true),
                    bearer = json.optString("bearer", ""),
                    bearerBitmask = json.optString("bearerBitmask", ""),
                    mvnoType = json.optString("mvnoType", ""),
                    mvnoMatchData = json.optString("mvnoMatchData", ""),
                    current = json.optString("current", "0"),
                    proId = json.optString("proId", ""),
                    priority = json.optInt("priority", 1),
                    simSlot = json.optInt("simSlot", 0),
                    isDefault = json.optBoolean("isDefault", false),
                    isActive = json.optBoolean("isActive", true)
                )
            } catch (e: Exception) {
                null
            }
        }
    }
}

/**
 * 运营商信息数据类
 */
data class CarrierInfo(
    val carrierId: Int,                      // 运营商ID
    val carrierName: String,                 // 运营商名称
    val mcc: String,                         // 移动国家代码
    val mnc: String,                         // 移动网络代码
    val numeric: String,                     // 数字标识符
    val countryIso: String,                  // 国家ISO代码
    val simOperator: String,                 // SIM运营商
    val simOperatorName: String,             // SIM运营商名称
    val networkOperator: String,             // 网络运营商
    val networkOperatorName: String,         // 网络运营商名称
    val simSlot: Int,                        // SIM卡槽位
    val simState: Int,                       // SIM卡状态
    val networkType: Int,                    // 网络类型
    val isRoaming: Boolean,                  // 是否漫游
    val signalStrength: Int = -1,            // 信号强度
    val dataState: Int = TelephonyManager.DATA_UNKNOWN // 数据连接状态
) {
    
    /**
     * 获取网络类型描述
     */
    fun getNetworkTypeDescription(): String {
        return when (networkType) {
            TelephonyManager.NETWORK_TYPE_GPRS -> "GPRS"
            TelephonyManager.NETWORK_TYPE_EDGE -> "EDGE"
            TelephonyManager.NETWORK_TYPE_UMTS -> "UMTS"
            TelephonyManager.NETWORK_TYPE_HSDPA -> "HSDPA"
            TelephonyManager.NETWORK_TYPE_HSUPA -> "HSUPA"
            TelephonyManager.NETWORK_TYPE_HSPA -> "HSPA"
            TelephonyManager.NETWORK_TYPE_LTE -> "LTE"
            TelephonyManager.NETWORK_TYPE_NR -> "5G"
            else -> "Unknown($networkType)"
        }
    }
    
    /**
     * 获取SIM卡状态描述
     */
    fun getSimStateDescription(): String {
        return when (simState) {
            TelephonyManager.SIM_STATE_ABSENT -> "无SIM卡"
            TelephonyManager.SIM_STATE_PIN_REQUIRED -> "需要PIN码"
            TelephonyManager.SIM_STATE_PUK_REQUIRED -> "需要PUK码"
            TelephonyManager.SIM_STATE_NETWORK_LOCKED -> "网络锁定"
            TelephonyManager.SIM_STATE_READY -> "就绪"
            TelephonyManager.SIM_STATE_NOT_READY -> "未就绪"
            TelephonyManager.SIM_STATE_PERM_DISABLED -> "永久禁用"
            TelephonyManager.SIM_STATE_CARD_IO_ERROR -> "卡IO错误"
            TelephonyManager.SIM_STATE_CARD_RESTRICTED -> "卡受限"
            else -> "未知($simState)"
        }
    }
    
    /**
     * 获取数据连接状态描述
     */
    fun getDataStateDescription(): String {
        return when (dataState) {
            TelephonyManager.DATA_DISCONNECTED -> "已断开"
            TelephonyManager.DATA_CONNECTING -> "连接中"
            TelephonyManager.DATA_CONNECTED -> "已连接"
            TelephonyManager.DATA_SUSPENDED -> "已暂停"
            else -> "未知($dataState)"
        }
    }
    
    /**
     * 检查SIM卡是否可用
     */
    fun isSimReady(): Boolean {
        return simState == TelephonyManager.SIM_STATE_READY
    }
    
    /**
     * 检查数据连接是否可用
     */
    fun isDataConnected(): Boolean {
        return dataState == TelephonyManager.DATA_CONNECTED
    }
}

/**
 * 网络状态数据类
 */
data class NetworkStatus(
    val isConnected: Boolean,                // 是否连接
    val networkType: NetworkType,            // 网络类型
    val signalStrength: Int,                 // 信号强度
    val dataUsage: DataUsage,                // 数据使用情况
    val latency: Long = -1,                  // 延迟（毫秒）
    val downloadSpeed: Long = -1,            // 下载速度（bps）
    val uploadSpeed: Long = -1,              // 上传速度（bps）
    val timestamp: Long = System.currentTimeMillis() // 时间戳
)

/**
 * 网络类型枚举
 */
enum class NetworkType(val description: String) {
    NONE("无网络"),
    WIFI("WiFi"),
    MOBILE_2G("2G"),
    MOBILE_3G("3G"),
    MOBILE_4G("4G"),
    MOBILE_5G("5G"),
    ETHERNET("以太网"),
    BLUETOOTH("蓝牙"),
    VPN("VPN"),
    OTHER("其他");
    
    companion object {
        fun fromTelephonyType(type: Int): NetworkType {
            return when (type) {
                TelephonyManager.NETWORK_TYPE_GPRS,
                TelephonyManager.NETWORK_TYPE_EDGE,
                TelephonyManager.NETWORK_TYPE_CDMA,
                TelephonyManager.NETWORK_TYPE_1xRTT,
                TelephonyManager.NETWORK_TYPE_IDEN -> MOBILE_2G
                
                TelephonyManager.NETWORK_TYPE_UMTS,
                TelephonyManager.NETWORK_TYPE_EVDO_0,
                TelephonyManager.NETWORK_TYPE_EVDO_A,
                TelephonyManager.NETWORK_TYPE_HSDPA,
                TelephonyManager.NETWORK_TYPE_HSUPA,
                TelephonyManager.NETWORK_TYPE_HSPA,
                TelephonyManager.NETWORK_TYPE_EVDO_B,
                TelephonyManager.NETWORK_TYPE_EHRPD,
                TelephonyManager.NETWORK_TYPE_HSPAP -> MOBILE_3G
                
                TelephonyManager.NETWORK_TYPE_LTE -> MOBILE_4G
                TelephonyManager.NETWORK_TYPE_NR -> MOBILE_5G
                
                else -> OTHER
            }
        }
    }
}

/**
 * 数据使用情况数据类
 */
data class DataUsage(
    val rxBytes: Long,                       // 接收字节数
    val txBytes: Long,                       // 发送字节数
    val rxPackets: Long,                     // 接收包数
    val txPackets: Long,                     // 发送包数
    val startTime: Long,                     // 统计开始时间
    val endTime: Long                        // 统计结束时间
) {
    
    /**
     * 获取总流量
     */
    fun getTotalBytes(): Long = rxBytes + txBytes
    
    /**
     * 获取总包数
     */
    fun getTotalPackets(): Long = rxPackets + txPackets
    
    /**
     * 获取统计时长（毫秒）
     */
    fun getDuration(): Long = endTime - startTime
    
    /**
     * 获取平均速度（字节/秒）
     */
    fun getAverageSpeed(): Long {
        val duration = getDuration()
        return if (duration > 0) {
            (getTotalBytes() * 1000) / duration
        } else {
            0
        }
    }
}

/**
 * APN操作结果数据类
 */
data class ApnOperationResult(
    val success: Boolean,                    // 是否成功
    val apnId: Long = -1,                    // APN ID
    val message: String = "",                // 结果消息
    val errorCode: Int = 0,                  // 错误代码
    val affectedRows: Int = 0                // 影响的行数
)

/**
 * APN验证结果数据类
 */
data class ApnValidationResult(
    val isValid: Boolean,                    // 是否有效
    val canConnect: Boolean,                 // 是否可连接
    val responseTime: Long = -1,             // 响应时间
    val errorMessage: String = "",           // 错误消息
    val testUrl: String = "",                // 测试URL
    val timestamp: Long = System.currentTimeMillis() // 验证时间
)

/**
 * APN管理统计信息数据类
 */
data class ApnStatistics(
    var totalConfigurations: Long = 0,       // 总配置数
    var activeConfigurations: Long = 0,      // 活跃配置数
    var successfulConnections: Long = 0,     // 成功连接数
    var failedConnections: Long = 0,         // 失败连接数
    var totalDataUsage: Long = 0,            // 总数据使用量
    var averageSignalStrength: Float = 0f,   // 平均信号强度
    var lastConfigurationTime: Long = 0,     // 最后配置时间
    var lastConnectionTime: Long = 0,        // 最后连接时间
    var carrierSwitchCount: Long = 0,        // 运营商切换次数
    var roamingEvents: Long = 0              // 漫游事件数
) {
    
    /**
     * 记录配置操作
     */
    fun recordConfiguration() {
        totalConfigurations++
        lastConfigurationTime = System.currentTimeMillis()
    }
    
    /**
     * 记录连接成功
     */
    fun recordConnectionSuccess() {
        successfulConnections++
        lastConnectionTime = System.currentTimeMillis()
    }
    
    /**
     * 记录连接失败
     */
    fun recordConnectionFailure() {
        failedConnections++
    }
    
    /**
     * 记录运营商切换
     */
    fun recordCarrierSwitch() {
        carrierSwitchCount++
    }
    
    /**
     * 记录漫游事件
     */
    fun recordRoamingEvent() {
        roamingEvents++
    }
    
    /**
     * 计算连接成功率
     */
    fun getConnectionSuccessRate(): Float {
        val totalConnections = successfulConnections + failedConnections
        return if (totalConnections > 0) {
            (successfulConnections.toFloat() / totalConnections.toFloat()) * 100f
        } else {
            0f
        }
    }
    
    /**
     * 重置统计信息
     */
    fun reset() {
        totalConfigurations = 0
        activeConfigurations = 0
        successfulConnections = 0
        failedConnections = 0
        totalDataUsage = 0
        averageSignalStrength = 0f
        lastConfigurationTime = 0
        lastConnectionTime = 0
        carrierSwitchCount = 0
        roamingEvents = 0
    }
}

/**
 * APN认证类型枚举
 */
enum class ApnAuthType(val value: Int, val description: String) {
    NONE(0, "无认证"),
    PAP(1, "PAP"),
    CHAP(2, "CHAP"),
    PAP_OR_CHAP(3, "PAP或CHAP");
    
    companion object {
        fun fromValue(value: Int): ApnAuthType {
            return values().find { it.value == value } ?: NONE
        }
    }
}

/**
 * APN协议类型枚举
 */
enum class ApnProtocol(val value: String, val description: String) {
    IP("IP", "IPv4"),
    IPV6("IPV6", "IPv6"),
    IPV4V6("IPV4V6", "IPv4/IPv6"),
    PPP("PPP", "PPP");
    
    companion object {
        fun fromValue(value: String): ApnProtocol {
            return values().find { it.value.equals(value, ignoreCase = true) } ?: IP
        }
    }
}

/**
 * APN工具类
 */
object ApnUtils {
    
    /**
     * 验证MCC是否有效
     */
    fun isValidMcc(mcc: String): Boolean {
        return mcc.matches(Regex("\\d{3}"))
    }
    
    /**
     * 验证MNC是否有效
     */
    fun isValidMnc(mnc: String): Boolean {
        return mnc.matches(Regex("\\d{2,3}"))
    }
    
    /**
     * 生成数字标识符
     */
    fun generateNumeric(mcc: String, mnc: String): String {
        return if (isValidMcc(mcc) && isValidMnc(mnc)) {
            "$mcc$mnc"
        } else {
            ""
        }
    }
    
    /**
     * 解析数字标识符
     */
    fun parseNumeric(numeric: String): Pair<String, String> {
        return if (numeric.length >= 5) {
            val mcc = numeric.substring(0, 3)
            val mnc = numeric.substring(3)
            Pair(mcc, mnc)
        } else {
            Pair("", "")
        }
    }
    
    /**
     * 检查APN名称是否有效
     */
    fun isValidApnName(apn: String): Boolean {
        return apn.isNotEmpty() && 
               apn.length <= 64 && 
               apn.matches(Regex("[a-zA-Z0-9._-]+"))
    }
    
    /**
     * 格式化数据大小
     */
    fun formatDataSize(bytes: Long): String {
        return when {
            bytes < 1024 -> "${bytes}B"
            bytes < 1024 * 1024 -> "${bytes / 1024}KB"
            bytes < 1024 * 1024 * 1024 -> "${bytes / (1024 * 1024)}MB"
            else -> "${bytes / (1024 * 1024 * 1024)}GB"
        }
    }
    
    /**
     * 格式化速度
     */
    fun formatSpeed(bps: Long): String {
        return when {
            bps < 1024 -> "${bps}bps"
            bps < 1024 * 1024 -> "${bps / 1024}Kbps"
            bps < 1024 * 1024 * 1024 -> "${bps / (1024 * 1024)}Mbps"
            else -> "${bps / (1024 * 1024 * 1024)}Gbps"
        }
    }
}
