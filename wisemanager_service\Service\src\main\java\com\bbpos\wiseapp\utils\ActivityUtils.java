package com.bbpos.wiseapp.utils;

import android.annotation.TargetApi;
import android.app.ActivityManager;
import android.app.usage.StorageStatsManager;
import android.content.Context;
import android.content.Intent;
import android.content.pm.ApplicationInfo;
import android.content.pm.IPackageStatsObserver;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.content.pm.PackageStats;
import android.os.Build;
import android.os.Debug;
import android.os.RemoteException;
import android.os.storage.StorageManager;
import android.os.storage.StorageVolume;
import android.text.TextUtils;

import com.bbpos.wiseapp.logger.BBLog;
import com.bbpos.wiseapp.service.common.UsualData;
import com.bbpos.wiseapp.tms.utils.Constants;
import com.bbpos.wiseapp.tms.utils.ContextUtil;
import com.bbpos.wiseapp.tms.utils.ParameterName;
import com.bbpos.wiseapp.websocket.OTANotifyService;

import org.json.JSONObject;

import java.io.File;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.List;
import java.util.Locale;
import java.util.UUID;

public class ActivityUtils {
    private final static String TAG = "ActivityUtils";

    //根据包名判断应用是否已经安装
    public static boolean isInstallApp(Context context,String packageName) {
        if (TextUtils.isEmpty(packageName))return false;
        final PackageManager packageManager = context.getPackageManager();// 获取packagemanager
        List<PackageInfo> pinfo = packageManager.getInstalledPackages(0);// 获取所有已安装程序的包信息
        if (pinfo != null) {
            for (int i = 0; i < pinfo.size(); i++) {
                String pn = pinfo.get(i).packageName.toLowerCase(Locale.ENGLISH);
                if (pn.equals(packageName)) {
                    return true;
                }
            }
        }
        return false;
    }

    public static boolean isApplicationAvilible(Context context, String packageName){
        final PackageManager packageManager = context.getPackageManager();//获取packagemanager
        List<PackageInfo> pinfo = packageManager.getInstalledPackages(0);//获取所有已安装程序的包信息
        List<String> pName = new ArrayList<String>();//用于存储所有已安装程序的包名
        //从pinfo中将包名字逐一取出，压入pName list中
        if(pinfo != null){
            for(int i = 0; i < pinfo.size(); i++){
                String pn = pinfo.get(i).packageName;
                pName.add(pn);
            }
        }

        return pName.contains(packageName);//判断pName中是否有目标程序的包名，有TRUE，没有FALSE
    }

    public static boolean isApplictionOnTop(Context context, String packageName){
        ActivityManager mActivityManager = (ActivityManager) context.getSystemService(Context.ACTIVITY_SERVICE);
        List<ActivityManager.RunningTaskInfo> rti = mActivityManager.getRunningTasks(1);
        if (packageName.equals(rti.get(0).topActivity.getPackageName())) {
//            BBLog.i(BBLog.TAG, packageName + " isApplictionOnTop: " + true);
            return true;
        }

//        BBLog.i(BBLog.TAG, packageName + " isApplictionOnTop: " + false);
        return false;
    }

    public static boolean isApplictionInBackground(Context context, String packageName) {
        ActivityManager activityManager = (ActivityManager) context.getSystemService(Context.ACTIVITY_SERVICE);
        List<ActivityManager.RunningAppProcessInfo> appProcesses = activityManager.getRunningAppProcesses();
        for (ActivityManager.RunningAppProcessInfo appProcess : appProcesses) {
            if (appProcess.processName.equals(packageName)) {
                if (appProcess.importance != ActivityManager.RunningAppProcessInfo.IMPORTANCE_FOREGROUND) {
//                    BBLog.i(BBLog.TAG, "后台" + appProcess.processName);
                    return true;
                } else {
//                    BBLog.i(BBLog.TAG, "前台" + appProcess.processName);
                    return false;
                }
            }
        }

        return true;
    }

    public static boolean isServiceRunning(Context context) {
        BBLog.d(BBLog.TAG, "isLauncherRunning: 判断luancher process 是否正运行");
        ActivityManager myManager = (ActivityManager) context.getApplicationContext().getSystemService(Context.ACTIVITY_SERVICE);
//		ArrayList<ActivityManager.RunningAppProcessInfo> runningService = (ArrayList<ActivityManager.RunningAppProcessInfo>) myManager.getRunningAppProcesses();
        ArrayList<ActivityManager.RunningServiceInfo> runningService = (ArrayList<ActivityManager.RunningServiceInfo>) myManager.getRunningServices(30);
        for (int i = 0; i < runningService.size(); i++) {
            BBLog.i(BBLog.TAG, "正在运行进程： runningService["+ i + "] name = " + runningService.get(i).service.getPackageName());
            if (runningService.get(i).service.getPackageName()
                    .equals(UsualData.SERVICE_PACKAGE_NAME)) {
                return true;
            }
        }
        return false;
    }

    public static boolean isApkDebugable(Context context) {
        try {
            ApplicationInfo info= context.getApplicationInfo();
            return (info.flags&ApplicationInfo.FLAG_DEBUGGABLE)!=0;
        } catch (Exception e) {

        }
        return false;
    }

    public static PackageInfo getPackageInfo(Context context, String packageName) {
        PackageManager packageManager = context.getPackageManager();
        List<PackageInfo> pinfo = packageManager.getInstalledPackages(0);//获取所有已安装程序的包信息
        if(pinfo != null){
            for(int i = 0; i < pinfo.size(); i++){
                String pn = pinfo.get(i).packageName;
                if (packageName.equals(pn)) {
                    return pinfo.get(i);
                }
            }
        }

        return null;
    }


    public static void startApp(Context context,String packageName) {
        try{
            if (!TextUtils.isEmpty(packageName) && ActivityUtils.isApplicationAvilible(context, packageName)) {
                Intent intent = context.getPackageManager().getLaunchIntentForPackage(packageName);
                intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                context.startActivity(intent);
                BBLog.i(Constants.TAG, "启动该应用");
            }  else {
                BBLog.i(Constants.TAG, "包名为空或者该应用未安装");
            }
        } catch(Exception e) {
            e.printStackTrace();
            BBLog.i(Constants.TAG, "没有安装该应用");
        }
    }

    public static void startOTAService(JSONObject todoWSTaskJsonObj, int typeForOTA){
        try {
            switch (typeForOTA){
				case Constants.TYPE_WISECUBE_FW_UPGRADE:
					SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_NEED_WISEPOSPRO_OTA_FLAG, true);
					SharedPreferencesUtils.setSharePreferencesValue(Constants.FLAG_WISECUBE_FW_UPGRADE_EXIST, true);
					break;
				case Constants.TYPE_SP_FW_UPGRADE:
					SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_NEED_WISEPOSPRO_OTA_FLAG, true);
					SharedPreferencesUtils.setSharePreferencesValue(Constants.FLAG_SP_UPGRADE_EXIST, true);
					break;
				case Constants.TYPE_TMT_UPGRADE:
					SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_NEED_WISEPOS5_OTA_FLAG, true);
					SharedPreferencesUtils.setSharePreferencesValue(Constants.FLAG_TMT_UPGRADE_EXIST, true);
					break;
				case Constants.TYPE_KEY_UPGRADE:
					SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_NEED_WISEPOS5_OTA_FLAG, true);
					SharedPreferencesUtils.setSharePreferencesValue(Constants.FLAG_KEY_UPGRADE_EXIST, true);
					break;
				case Constants.TYPE_WISEPOS_TMT_UPGRADE:
					SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_NEED_WISEPOS4G_OTA_FLAG, true);
					break;
			}

			if (Constants.isOTARunning){
				BBLog.e(TAG, "startOTAService: 當前正在執行OTA/SP Upgrade,本次升級任務退出 " );
				return;
			}

			if (todoWSTaskJsonObj!=null && todoWSTaskJsonObj.has(ParameterName.packName) && !TextUtils.isEmpty(todoWSTaskJsonObj.getString(ParameterName.packName))) {
                Intent targetIntent = new Intent(ContextUtil.getInstance(), OTANotifyService.class);
                targetIntent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                targetIntent.putExtra(ParameterName.flag_need_ota, true);
                targetIntent.putExtra(ParameterName.type_for_ota, typeForOTA);
                targetIntent.putExtra(ParameterName.packName, todoWSTaskJsonObj.getString(ParameterName.packName));
                ContextUtil.getInstance().startService(targetIntent);
            }
        } catch (Exception e) {
            e.printStackTrace();
            BBLog.e(TAG, "startOTAService: 传入参数信息不完整 ,todoWSTaskJsonObj = "+ todoWSTaskJsonObj );
        }
    }

    public static boolean isServiceWork(Context mContext, String serviceName) {
        boolean isWork = false;
        ActivityManager myAM = (ActivityManager) mContext.getSystemService(Context.ACTIVITY_SERVICE);
        List<ActivityManager.RunningServiceInfo> myList = myAM.getRunningServices(Integer.MAX_VALUE);
        if (myList.size() <= 0) {
            return false;
        }
        for (int i = 0; i < myList.size(); i++) {
            String mName = myList.get(i).service.getClassName().toString();
            if (mName.equals(serviceName)) {
                isWork = true;
                break;
            }
        }
        return isWork;
    }


    @TargetApi(Build.VERSION_CODES.O)
    public static UUID getStorageVolumeUUID(Context context){
        UUID uuid = StorageManager.UUID_DEFAULT;
        try {
            StorageManager storageManager = (StorageManager) context.getSystemService(Context.STORAGE_SERVICE);
            List<StorageVolume> storageVolumes = storageManager.getStorageVolumes();
            for (StorageVolume storageVolume : storageVolumes) {
                String uuidStr = storageVolume.getUuid();
                try {
                    if (TextUtils.isEmpty(uuidStr)) {
                        uuid = StorageManager.UUID_DEFAULT;
                    } else {
                        uuid = UUID.fromString(uuidStr);
                    }
                } catch (Exception e) {
                    uuid = StorageManager.UUID_DEFAULT;
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return uuid;
    }

    public static long[] handlePackageSizeInfo(Context context, String packageName) {
        long[] appDataSizes = new long[4];
        PackageManager mPackageManager = context.getPackageManager();
        Method getPackageSizeInfo = null;
        try {
            getPackageSizeInfo = mPackageManager.getClass().getMethod("getPackageSizeInfo", String.class, IPackageStatsObserver.class);
            if (getPackageSizeInfo != null) {
                try {
                    getPackageSizeInfo.setAccessible(true);
                    getPackageSizeInfo.invoke(mPackageManager, packageName, new IPackageStatsObserver.Stub() {
                        @Override
                        public void onGetStatsCompleted(PackageStats pStats, boolean succeeded) throws RemoteException {
                            BBLog.d(BBLog.TAG," handlePackageSizeInfo onGetStatsCompleted");
                            appDataSizes[0] = pStats.codeSize;
                            appDataSizes[1] = pStats.cacheSize;
                            appDataSizes[2] = pStats.dataSize;
                            appDataSizes[3] = pStats.codeSize + pStats.cacheSize + pStats.dataSize;

                        }
                    });
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        } catch (NoSuchMethodException e) {
            e.printStackTrace();
        }
        return appDataSizes;
    }

    // 获得系统进程信息
    public static int getRunningAppProcessInfo(int  uid) {
        int memSize = 0;
        try {
            ActivityManager mActivityManager = (ActivityManager) ContextUtil.getInstance().getSystemService(Context.ACTIVITY_SERVICE);
            List<ActivityManager.RunningAppProcessInfo> appProcessList = mActivityManager
                    .getRunningAppProcesses();
            for (ActivityManager.RunningAppProcessInfo appProcessInfo : appProcessList) {
                int pid = appProcessInfo.pid;
                if (uid == appProcessInfo.uid) {
                    int[] processPid = new int[]{pid};
                    Debug.MemoryInfo[] memoryInfo = mActivityManager.getProcessMemoryInfo(processPid);
                    memSize = memoryInfo[0].dalvikPrivateDirty * 1024 + memoryInfo[0].nativePrivateDirty * 1024 + memoryInfo[0].otherPrivateDirty * 1024;
                    break;
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return memSize;
    }
}
