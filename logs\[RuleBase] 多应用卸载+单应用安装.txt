2025-08-14 18:41:22.633 19136-19207 Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"msg_state":"P","request_time":"1755168081444","data":{"ruleList":[{"deleteAppList":[{"apkName":"懒猫趣播","appId":"8a6d45b6779244d59837eb1d83f882c9","packName":"com.lingjingnet.lazycat","versionName":"1.0.1","versionCode":"10"},{"apkName":"逍遥助手","appId":"225a0a30994e45089dad1a270f8d85a1","packName":"plus.H52FFB9A5","versionName":"1.0","versionCode":"100"}],"modifyDate":"2025-08-14 10:41:21","endDate":"9999-12-31 23:59:59","restartAfter":"0","installBy":"0","beginDate":"2024-08-14 10:41:21","appList":[{"apkMd5":"0d1225260d03e10a8ffc8409369c442a","apkName":"Via","appId":"353795033bc0410dac6dee5cb8702b06","apkSize":"2565110","appIconUrl":"https://siot-bucket01.s3.ap-northeast-1.amazonaws.com/icon/88cceb7e7cae4b8792791b5eb9d04c47ic_launcher.png","packName":"mark.via","versionName":"6.6.0","versionCode":"20250713","url":"https://siot-bucket01.s3.ap-northeast-1.amazonaws.com/app/4d256c4123b346fd817537b491342204.apk"}],"serviceList":[],"action":"A","ruleName":"d60_test_2993","ruleId":"1861b5712484475383e893c83908a034","createDate":"2025-08-14 10:41:21","orgRuleId":""}]},"tranCode":"ST005","request_id":"1755168081444ST005","version":"1","serialNo":"01354090202503050399"}
2025-08-14 18:41:22.642 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 准备发送响应确认 - requestId: 1755168081444ST005, needResponse: true
2025-08-14 18:41:22.665 19136-19207 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0000","serialNo":"01354090202503050399","request_time":"1755168082651","request_id":"1755168082651C0000","version":"1","org_request_id":"1755168081444ST005","org_request_time":"1755168081444","response_state":"0","myVersionName":"1.0.08.20250814.DSPREAD.MDM.SERVICE","terminalDate":"20250814184122"}
2025-08-14 18:41:22.685 19136-19207 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0000","serialNo":"01354090202503050399","request_time":"1755168082673","request_id":"1755168082673C0000","version":"1","org_request_id":"1755168081444ST005","org_request_time":"1755168081444","response_state":"0","myVersionName":"1.0.08.20250814.DSPREAD.MDM.SERVICE","terminalDate":"20250814184122"}
2025-08-14 18:41:22.690 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 已发送C0000响应确认: requestId=1755168081444ST005
2025-08-14 18:41:22.698 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleHandler 处理规则消息: ST005
2025-08-14 18:41:22.702 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleHandler 处理规则列表，数量: 1
2025-08-14 18:41:22.707 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleHandler 处理规则: ruleId=1861b5712484475383e893c83908a034, action=A
2025-08-14 18:41:22.711 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析规则JSON: ruleId=1861b5712484475383e893c83908a034
2025-08-14 18:41:22.719 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule appList数组: [{"apkMd5":"0d1225260d03e10a8ffc8409369c442a","apkName":"Via","appId":"353795033bc0410dac6dee5cb8702b06","apkSize":"2565110","appIconUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/icon\/88cceb7e7cae4b8792791b5eb9d04c47ic_launcher.png","packName":"mark.via","versionName":"6.6.0","versionCode":"20250713","url":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/4d256c4123b346fd817537b491342204.apk"}]
2025-08-14 18:41:22.726 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule deleteAppList数组: [{"apkName":"懒猫趣播","appId":"8a6d45b6779244d59837eb1d83f882c9","packName":"com.lingjingnet.lazycat","versionName":"1.0.1","versionCode":"10"},{"apkName":"逍遥助手","appId":"225a0a30994e45089dad1a270f8d85a1","packName":"plus.H52FFB9A5","versionName":"1.0","versionCode":"100"}]
2025-08-14 18:41:22.731 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=mark.via, apkName=Via
2025-08-14 18:41:22.735 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=com.lingjingnet.lazycat, apkName=懒猫趣播
2025-08-14 18:41:22.740 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=plus.H52FFB9A5, apkName=逍遥助手
2025-08-14 18:41:22.744 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后appList数量: 1
2025-08-14 18:41:22.749 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后deleteAppList数量: 2
2025-08-14 18:41:22.753 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleBaseStorage 处理规则: 1861b5712484475383e893c83908a034, 操作: A
2025-08-14 18:41:22.757 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 验证规则: ruleId=1861b5712484475383e893c83908a034, ruleType=app_management, action=A
2025-08-14 18:41:22.762 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule appList数量: 1, deleteAppList数量: 2
2025-08-14 18:41:22.782 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 验证应用管理规则: action=A
2025-08-14 18:41:22.786 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 验证应用列表: appList=1, deleteAppList=2
2025-08-14 18:41:22.792 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 应用列表验证通过
2025-08-14 18:41:22.827 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析规则JSON: ruleId=937eccc7e4ff4fc0bc9eaf2788685515
2025-08-14 18:41:22.834 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule appList数组: [{"packName":"mark.via","apkName":"Via","versionName":"6.6.0","versionCode":"20250713","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/4d256c4123b346fd817537b491342204.apk","apkMd5":"0d1225260d03e10a8ffc8409369c442a","apkSize":2565110,"installBy":"0","isSystemApp":false,"priority":0}]
2025-08-14 18:41:22.839 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule deleteAppList数组: []
2025-08-14 18:41:22.843 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=mark.via, apkName=Via
2025-08-14 18:41:22.848 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后appList数量: 1
2025-08-14 18:41:22.852 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后deleteAppList数量: 0
2025-08-14 18:41:22.857 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析规则JSON: ruleId=01f42138486940dd8ba8a136d84e239d
2025-08-14 18:41:22.861 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule appList数组: []
2025-08-14 18:41:22.867 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule deleteAppList数组: [{"packName":"mark.via","apkName":"Via","versionName":"6.6.0","versionCode":"20250713","apkUrl":"","apkMd5":"","apkSize":0,"installBy":"0","isSystemApp":false,"priority":0}]
2025-08-14 18:41:22.872 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=mark.via, apkName=Via
2025-08-14 18:41:22.876 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后appList数量: 0
2025-08-14 18:41:22.881 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后deleteAppList数量: 1
2025-08-14 18:41:22.885 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析规则JSON: ruleId=e195e52efa414316818a8d4b21fa4e9c
2025-08-14 18:41:22.890 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule appList数组: []
2025-08-14 18:41:22.898 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule deleteAppList数组: [{"packName":"com.chileaf.cl960.sample","apkName":"cl960","versionName":"1.1.0","versionCode":"1","apkUrl":"","apkMd5":"","apkSize":0,"installBy":"0","isSystemApp":false,"priority":0},{"packName":"plus.H52FFB9A5","apkName":"逍遥助手","versionName":"1.0","versionCode":"100","apkUrl":"","apkMd5":"","apkSize":0,"installBy":"0","isSystemApp":false,"priority":0}]
2025-08-14 18:41:22.903 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=com.chileaf.cl960.sample, apkName=cl960
2025-08-14 18:41:22.908 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=plus.H52FFB9A5, apkName=逍遥助手
2025-08-14 18:41:22.913 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后appList数量: 0
2025-08-14 18:41:22.917 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后deleteAppList数量: 2
2025-08-14 18:41:22.922 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析规则JSON: ruleId=6eb6f82614c74c3bb1f5ca0e32ef787e
2025-08-14 18:41:22.932 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule appList数组: [{"packName":"mark.via","apkName":"Via","versionName":"6.6.0","versionCode":"20250713","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/4d256c4123b346fd817537b491342204.apk","apkMd5":"0d1225260d03e10a8ffc8409369c442a","apkSize":2565110,"installBy":"0","isSystemApp":false,"priority":0},{"packName":"com.chileaf.cl960.sample","apkName":"cl960","versionName":"1.1.0","versionCode":"1","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/42e16b87eaa849bcaaf0f5fadcac6aab.apk","apkMd5":"54cc3a371939423d4288d63e1ac88019","apkSize":6155416,"installBy":"0","isSystemApp":false,"priority":0}]
2025-08-14 18:41:22.936 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule deleteAppList数组: []
2025-08-14 18:41:22.940 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=mark.via, apkName=Via
2025-08-14 18:41:22.945 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=com.chileaf.cl960.sample, apkName=cl960
2025-08-14 18:41:22.949 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后appList数量: 2
2025-08-14 18:41:22.953 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后deleteAppList数量: 0
2025-08-14 18:41:22.958 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析规则JSON: ruleId=83d4a14ed05348efa59fbc626e5d8b19
2025-08-14 18:41:22.966 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule appList数组: [{"packName":"plus.H52FFB9A5","apkName":"逍遥助手","versionName":"1.0","versionCode":"100","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/203831945bb84d42b21decf1bf936815.apk","apkMd5":"ba1ee533924eae5c408465e7cddcbda4","apkSize":4771451,"installBy":"0","isSystemApp":false,"priority":0}]
2025-08-14 18:41:22.972 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule deleteAppList数组: [{"packName":"mark.via","apkName":"Via","versionName":"6.6.0","versionCode":"20250713","apkUrl":"","apkMd5":"","apkSize":0,"installBy":"0","isSystemApp":false,"priority":0}]
2025-08-14 18:41:22.977 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=plus.H52FFB9A5, apkName=逍遥助手
2025-08-14 18:41:22.981 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=mark.via, apkName=Via
2025-08-14 18:41:22.985 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后appList数量: 1
2025-08-14 18:41:22.990 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后deleteAppList数量: 1
2025-08-14 18:41:22.994 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析规则JSON: ruleId=168fee4bca5148a0be863080acad09ee
2025-08-14 18:41:23.005 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule appList数组: [{"packName":"mark.via","apkName":"Via","versionName":"6.6.0","versionCode":"20250713","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/4d256c4123b346fd817537b491342204.apk","apkMd5":"0d1225260d03e10a8ffc8409369c442a","apkSize":2565110,"installBy":"0","isSystemApp":false,"priority":0},{"packName":"com.lingjingnet.lazycat","apkName":"懒猫趣播","versionName":"1.0.1","versionCode":"10","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/f03e70448bf9461abf1804e9da65b36f.apk","apkMd5":"accb937c84dcd0d346da7f8fee7bf0ae","apkSize":13140733,"installBy":"0","isSystemApp":false,"priority":0}]
2025-08-14 18:41:23.014 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule deleteAppList数组: [{"packName":"com.chileaf.cl960.sample","apkName":"cl960","versionName":"1.1.0","versionCode":"1","apkUrl":"","apkMd5":"","apkSize":0,"installBy":"0","isSystemApp":false,"priority":0},{"packName":"plus.H52FFB9A5","apkName":"逍遥助手","versionName":"1.0","versionCode":"100","apkUrl":"","apkMd5":"","apkSize":0,"installBy":"0","isSystemApp":false,"priority":0}]
2025-08-14 18:41:23.018 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=mark.via, apkName=Via
2025-08-14 18:41:23.022 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=com.lingjingnet.lazycat, apkName=懒猫趣播
2025-08-14 18:41:23.027 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=com.chileaf.cl960.sample, apkName=cl960
2025-08-14 18:41:23.031 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=plus.H52FFB9A5, apkName=逍遥助手
2025-08-14 18:41:23.036 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后appList数量: 2
2025-08-14 18:41:23.040 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后deleteAppList数量: 2
2025-08-14 18:41:23.044 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析规则JSON: ruleId=fb4a8c49e3854b00b51034df8d6cf113
2025-08-14 18:41:23.055 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule appList数组: [{"packName":"com.chileaf.cl960.sample","apkName":"cl960","versionName":"1.1.0","versionCode":"1","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/42e16b87eaa849bcaaf0f5fadcac6aab.apk","apkMd5":"54cc3a371939423d4288d63e1ac88019","apkSize":6155416,"installBy":"0","isSystemApp":false,"priority":0},{"packName":"plus.H52FFB9A5","apkName":"逍遥助手","versionName":"1.0","versionCode":"100","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/203831945bb84d42b21decf1bf936815.apk","apkMd5":"ba1ee533924eae5c408465e7cddcbda4","apkSize":4771451,"installBy":"0","isSystemApp":false,"priority":0}]
2025-08-14 18:41:23.061 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule deleteAppList数组: [{"packName":"mark.via","apkName":"Via","versionName":"6.6.0","versionCode":"20250713","apkUrl":"","apkMd5":"","apkSize":0,"installBy":"0","isSystemApp":false,"priority":0}]
2025-08-14 18:41:23.066 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=com.chileaf.cl960.sample, apkName=cl960
2025-08-14 18:41:23.070 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=plus.H52FFB9A5, apkName=逍遥助手
2025-08-14 18:41:23.075 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=mark.via, apkName=Via
2025-08-14 18:41:23.079 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后appList数量: 2
2025-08-14 18:41:23.083 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后deleteAppList数量: 1
2025-08-14 18:41:23.118 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析规则JSON: ruleId=937eccc7e4ff4fc0bc9eaf2788685515
2025-08-14 18:41:23.125 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule appList数组: [{"packName":"mark.via","apkName":"Via","versionName":"6.6.0","versionCode":"20250713","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/4d256c4123b346fd817537b491342204.apk","apkMd5":"0d1225260d03e10a8ffc8409369c442a","apkSize":2565110,"installBy":"0","isSystemApp":false,"priority":0}]
2025-08-14 18:41:23.130 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule deleteAppList数组: []
2025-08-14 18:41:23.134 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=mark.via, apkName=Via
2025-08-14 18:41:23.138 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后appList数量: 1
2025-08-14 18:41:23.142 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后deleteAppList数量: 0
2025-08-14 18:41:23.147 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析规则JSON: ruleId=01f42138486940dd8ba8a136d84e239d
2025-08-14 18:41:23.151 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule appList数组: []
2025-08-14 18:41:23.158 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule deleteAppList数组: [{"packName":"mark.via","apkName":"Via","versionName":"6.6.0","versionCode":"20250713","apkUrl":"","apkMd5":"","apkSize":0,"installBy":"0","isSystemApp":false,"priority":0}]
2025-08-14 18:41:23.162 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=mark.via, apkName=Via
2025-08-14 18:41:23.166 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后appList数量: 0
2025-08-14 18:41:23.170 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后deleteAppList数量: 1
2025-08-14 18:41:23.175 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析规则JSON: ruleId=e195e52efa414316818a8d4b21fa4e9c
2025-08-14 18:41:23.179 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule appList数组: []
2025-08-14 18:41:23.187 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule deleteAppList数组: [{"packName":"com.chileaf.cl960.sample","apkName":"cl960","versionName":"1.1.0","versionCode":"1","apkUrl":"","apkMd5":"","apkSize":0,"installBy":"0","isSystemApp":false,"priority":0},{"packName":"plus.H52FFB9A5","apkName":"逍遥助手","versionName":"1.0","versionCode":"100","apkUrl":"","apkMd5":"","apkSize":0,"installBy":"0","isSystemApp":false,"priority":0}]
2025-08-14 18:41:23.192 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=com.chileaf.cl960.sample, apkName=cl960
2025-08-14 18:41:23.196 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=plus.H52FFB9A5, apkName=逍遥助手
2025-08-14 18:41:23.200 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后appList数量: 0
2025-08-14 18:41:23.204 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后deleteAppList数量: 2
2025-08-14 18:41:23.209 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析规则JSON: ruleId=6eb6f82614c74c3bb1f5ca0e32ef787e
2025-08-14 18:41:23.218 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule appList数组: [{"packName":"mark.via","apkName":"Via","versionName":"6.6.0","versionCode":"20250713","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/4d256c4123b346fd817537b491342204.apk","apkMd5":"0d1225260d03e10a8ffc8409369c442a","apkSize":2565110,"installBy":"0","isSystemApp":false,"priority":0},{"packName":"com.chileaf.cl960.sample","apkName":"cl960","versionName":"1.1.0","versionCode":"1","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/42e16b87eaa849bcaaf0f5fadcac6aab.apk","apkMd5":"54cc3a371939423d4288d63e1ac88019","apkSize":6155416,"installBy":"0","isSystemApp":false,"priority":0}]
2025-08-14 18:41:23.222 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule deleteAppList数组: []
2025-08-14 18:41:23.227 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=mark.via, apkName=Via
2025-08-14 18:41:23.231 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=com.chileaf.cl960.sample, apkName=cl960
2025-08-14 18:41:23.235 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后appList数量: 2
2025-08-14 18:41:23.240 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后deleteAppList数量: 0
2025-08-14 18:41:23.244 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析规则JSON: ruleId=83d4a14ed05348efa59fbc626e5d8b19
2025-08-14 18:41:23.251 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule appList数组: [{"packName":"plus.H52FFB9A5","apkName":"逍遥助手","versionName":"1.0","versionCode":"100","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/203831945bb84d42b21decf1bf936815.apk","apkMd5":"ba1ee533924eae5c408465e7cddcbda4","apkSize":4771451,"installBy":"0","isSystemApp":false,"priority":0}]
2025-08-14 18:41:23.258 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule deleteAppList数组: [{"packName":"mark.via","apkName":"Via","versionName":"6.6.0","versionCode":"20250713","apkUrl":"","apkMd5":"","apkSize":0,"installBy":"0","isSystemApp":false,"priority":0}]
2025-08-14 18:41:23.262 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=plus.H52FFB9A5, apkName=逍遥助手
2025-08-14 18:41:23.266 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=mark.via, apkName=Via
2025-08-14 18:41:23.271 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后appList数量: 1
2025-08-14 18:41:23.275 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后deleteAppList数量: 1
2025-08-14 18:41:23.279 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析规则JSON: ruleId=168fee4bca5148a0be863080acad09ee
2025-08-14 18:41:23.290 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule appList数组: [{"packName":"mark.via","apkName":"Via","versionName":"6.6.0","versionCode":"20250713","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/4d256c4123b346fd817537b491342204.apk","apkMd5":"0d1225260d03e10a8ffc8409369c442a","apkSize":2565110,"installBy":"0","isSystemApp":false,"priority":0},{"packName":"com.lingjingnet.lazycat","apkName":"懒猫趣播","versionName":"1.0.1","versionCode":"10","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/f03e70448bf9461abf1804e9da65b36f.apk","apkMd5":"accb937c84dcd0d346da7f8fee7bf0ae","apkSize":13140733,"installBy":"0","isSystemApp":false,"priority":0}]
2025-08-14 18:41:23.298 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule deleteAppList数组: [{"packName":"com.chileaf.cl960.sample","apkName":"cl960","versionName":"1.1.0","versionCode":"1","apkUrl":"","apkMd5":"","apkSize":0,"installBy":"0","isSystemApp":false,"priority":0},{"packName":"plus.H52FFB9A5","apkName":"逍遥助手","versionName":"1.0","versionCode":"100","apkUrl":"","apkMd5":"","apkSize":0,"installBy":"0","isSystemApp":false,"priority":0}]
2025-08-14 18:41:23.302 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=mark.via, apkName=Via
2025-08-14 18:41:23.307 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=com.lingjingnet.lazycat, apkName=懒猫趣播
2025-08-14 18:41:23.312 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=com.chileaf.cl960.sample, apkName=cl960
2025-08-14 18:41:23.317 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=plus.H52FFB9A5, apkName=逍遥助手
2025-08-14 18:41:23.321 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后appList数量: 2
2025-08-14 18:41:23.325 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后deleteAppList数量: 2
2025-08-14 18:41:23.330 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析规则JSON: ruleId=fb4a8c49e3854b00b51034df8d6cf113
2025-08-14 18:41:23.340 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule appList数组: [{"packName":"com.chileaf.cl960.sample","apkName":"cl960","versionName":"1.1.0","versionCode":"1","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/42e16b87eaa849bcaaf0f5fadcac6aab.apk","apkMd5":"54cc3a371939423d4288d63e1ac88019","apkSize":6155416,"installBy":"0","isSystemApp":false,"priority":0},{"packName":"plus.H52FFB9A5","apkName":"逍遥助手","versionName":"1.0","versionCode":"100","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/203831945bb84d42b21decf1bf936815.apk","apkMd5":"ba1ee533924eae5c408465e7cddcbda4","apkSize":4771451,"installBy":"0","isSystemApp":false,"priority":0}]
2025-08-14 18:41:23.346 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule deleteAppList数组: [{"packName":"mark.via","apkName":"Via","versionName":"6.6.0","versionCode":"20250713","apkUrl":"","apkMd5":"","apkSize":0,"installBy":"0","isSystemApp":false,"priority":0}]
2025-08-14 18:41:23.351 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=com.chileaf.cl960.sample, apkName=cl960
2025-08-14 18:41:23.355 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=plus.H52FFB9A5, apkName=逍遥助手
2025-08-14 18:41:23.359 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=mark.via, apkName=Via
2025-08-14 18:41:23.364 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后appList数量: 2
2025-08-14 18:41:23.368 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后deleteAppList数量: 1
2025-08-14 18:41:23.447 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleBaseStorage 保存 8 个规则到存储
2025-08-14 18:41:23.452 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleBaseStorage Rule 1861b5712484475383e893c83908a034 添加成功
2025-08-14 18:41:23.456 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleBaseStorage 触发规则执行: 1861b5712484475383e893c83908a034
2025-08-14 18:41:23.461 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleHandler 规则处理成功: 1861b5712484475383e893c83908a034
2025-08-14 18:41:23.461 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 RuleBaseStorage 设备空闲，立即执行规则: 1861b5712484475383e893c83908a034
2025-08-14 18:41:23.465 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 RuleBaseManager 直接执行规则: 1861b5712484475383e893c83908a034
2025-08-14 18:41:23.466 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleHandler 规则列表处理完成: 处理完成 - 成功: 1, 失败: 0, 忽略: 0
2025-08-14 18:41:23.470 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleHandler 上报规则处理结果: 0 - 处理完成 - 成功: 1, 失败: 0, 忽略: 0
2025-08-14 18:41:23.470 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 RuleBaseManager 处理规则: 1861b5712484475383e893c83908a034, 操作: A
2025-08-14 18:41:23.475 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 C0107 主动式上送: rulebase_update (主动: 67)
2025-08-14 18:41:23.476 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析规则JSON: ruleId=1861b5712484475383e893c83908a034
2025-08-14 18:41:23.482 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 Rule appList数组: [{"packName":"mark.via","apkName":"Via","versionName":"6.6.0","versionCode":"20250713","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/4d256c4123b346fd817537b491342204.apk","apkMd5":"0d1225260d03e10a8ffc8409369c442a","apkSize":2565110,"installBy":"0","isSystemApp":false,"priority":0}]
2025-08-14 18:41:23.491 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 Rule deleteAppList数组: [{"packName":"com.lingjingnet.lazycat","apkName":"懒猫趣播","versionName":"1.0.1","versionCode":"10","apkUrl":"","apkMd5":"","apkSize":0,"installBy":"0","isSystemApp":false,"priority":0},{"packName":"plus.H52FFB9A5","apkName":"逍遥助手","versionName":"1.0","versionCode":"100","apkUrl":"","apkMd5":"","apkSize":0,"installBy":"0","isSystemApp":false,"priority":0}]
2025-08-14 18:41:23.492 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 添加规则结果: ruleId=batch_rules
2025-08-14 18:41:23.495 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=mark.via, apkName=Via
2025-08-14 18:41:23.500 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=com.lingjingnet.lazycat, apkName=懒猫趣播
2025-08-14 18:41:23.504 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=plus.H52FFB9A5, apkName=逍遥助手
2025-08-14 18:41:23.505 19136-19207 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0107","serialNo":"01354090202503050399","request_time":"1755168083487","request_id":"1755168083487C0107","version":"1","data":{"ruleId":"batch_rules","taskResult":"0"},"myVersionName":"1.0.08.20250814.DSPREAD.MDM.SERVICE","terminalDate":"20250814184123","org_request_id":"1755168081444ST005","org_request_time":"1755168081444"}
2025-08-14 18:41:23.509 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后appList数量: 1
2025-08-14 18:41:23.509 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 规则结果实时上送: ruleId=batch_rules, result=0 (1)
2025-08-14 18:41:23.513 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后deleteAppList数量: 2
2025-08-14 18:41:23.518 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 RuleBaseStorage 处理规则: 1861b5712484475383e893c83908a034, 操作: A
2025-08-14 18:41:23.522 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 Rule 验证规则: ruleId=1861b5712484475383e893c83908a034, ruleType=app_management, action=A
2025-08-14 18:41:23.526 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 Rule appList数量: 1, deleteAppList数量: 2
2025-08-14 18:41:23.546 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 Rule 验证应用管理规则: action=A
2025-08-14 18:41:23.550 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 Rule 验证应用列表: appList=1, deleteAppList=2
2025-08-14 18:41:23.556 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 Rule 应用列表验证通过
2025-08-14 18:41:23.596 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析规则JSON: ruleId=937eccc7e4ff4fc0bc9eaf2788685515
2025-08-14 18:41:23.603 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 Rule appList数组: [{"packName":"mark.via","apkName":"Via","versionName":"6.6.0","versionCode":"20250713","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/4d256c4123b346fd817537b491342204.apk","apkMd5":"0d1225260d03e10a8ffc8409369c442a","apkSize":2565110,"installBy":"0","isSystemApp":false,"priority":0}]
2025-08-14 18:41:23.607 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 Rule deleteAppList数组: []
2025-08-14 18:41:23.612 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=mark.via, apkName=Via
2025-08-14 18:41:23.616 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后appList数量: 1
2025-08-14 18:41:23.620 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后deleteAppList数量: 0
2025-08-14 18:41:23.625 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析规则JSON: ruleId=01f42138486940dd8ba8a136d84e239d
2025-08-14 18:41:23.629 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 Rule appList数组: []
2025-08-14 18:41:23.631 19136-19207 Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"request_time":"1755168082634","org_request_time":"1755168083487","org_request_id":"1755168083487C0107","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755168082634S0000","serialNo":"01354090202503050399"}
2025-08-14 18:41:23.635 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 Rule deleteAppList数组: [{"packName":"mark.via","apkName":"Via","versionName":"6.6.0","versionCode":"20250713","apkUrl":"","apkMd5":"","apkSize":0,"installBy":"0","isSystemApp":false,"priority":0}]
2025-08-14 18:41:23.638 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 收到响应消息: transCode=S0000, orgRequestId=1755168083487C0107, state=0, remark=
2025-08-14 18:41:23.639 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=mark.via, apkName=Via
2025-08-14 18:41:23.642 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 处理规则结果响应: state=0
2025-08-14 18:41:23.643 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后appList数量: 0
2025-08-14 18:41:23.646 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 规则结果上传成功
2025-08-14 18:41:23.647 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后deleteAppList数量: 1
2025-08-14 18:41:23.652 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析规则JSON: ruleId=e195e52efa414316818a8d4b21fa4e9c
2025-08-14 18:41:23.656 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 Rule appList数组: []
2025-08-14 18:41:23.664 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 Rule deleteAppList数组: [{"packName":"com.chileaf.cl960.sample","apkName":"cl960","versionName":"1.1.0","versionCode":"1","apkUrl":"","apkMd5":"","apkSize":0,"installBy":"0","isSystemApp":false,"priority":0},{"packName":"plus.H52FFB9A5","apkName":"逍遥助手","versionName":"1.0","versionCode":"100","apkUrl":"","apkMd5":"","apkSize":0,"installBy":"0","isSystemApp":false,"priority":0}]
2025-08-14 18:41:23.668 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=com.chileaf.cl960.sample, apkName=cl960
2025-08-14 18:41:23.673 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=plus.H52FFB9A5, apkName=逍遥助手
2025-08-14 18:41:23.677 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后appList数量: 0
2025-08-14 18:41:23.681 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后deleteAppList数量: 2
2025-08-14 18:41:23.686 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析规则JSON: ruleId=6eb6f82614c74c3bb1f5ca0e32ef787e
2025-08-14 18:41:23.697 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 Rule appList数组: [{"packName":"mark.via","apkName":"Via","versionName":"6.6.0","versionCode":"20250713","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/4d256c4123b346fd817537b491342204.apk","apkMd5":"0d1225260d03e10a8ffc8409369c442a","apkSize":2565110,"installBy":"0","isSystemApp":false,"priority":0},{"packName":"com.chileaf.cl960.sample","apkName":"cl960","versionName":"1.1.0","versionCode":"1","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/42e16b87eaa849bcaaf0f5fadcac6aab.apk","apkMd5":"54cc3a371939423d4288d63e1ac88019","apkSize":6155416,"installBy":"0","isSystemApp":false,"priority":0}]
2025-08-14 18:41:23.704 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 Rule deleteAppList数组: []
2025-08-14 18:41:23.709 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=mark.via, apkName=Via
2025-08-14 18:41:23.714 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=com.chileaf.cl960.sample, apkName=cl960
2025-08-14 18:41:23.718 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后appList数量: 2
2025-08-14 18:41:23.722 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后deleteAppList数量: 0
2025-08-14 18:41:23.726 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析规则JSON: ruleId=83d4a14ed05348efa59fbc626e5d8b19
2025-08-14 18:41:23.733 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 Rule appList数组: [{"packName":"plus.H52FFB9A5","apkName":"逍遥助手","versionName":"1.0","versionCode":"100","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/203831945bb84d42b21decf1bf936815.apk","apkMd5":"ba1ee533924eae5c408465e7cddcbda4","apkSize":4771451,"installBy":"0","isSystemApp":false,"priority":0}]
2025-08-14 18:41:23.739 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 Rule deleteAppList数组: [{"packName":"mark.via","apkName":"Via","versionName":"6.6.0","versionCode":"20250713","apkUrl":"","apkMd5":"","apkSize":0,"installBy":"0","isSystemApp":false,"priority":0}]
2025-08-14 18:41:23.743 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=plus.H52FFB9A5, apkName=逍遥助手
2025-08-14 18:41:23.748 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=mark.via, apkName=Via
2025-08-14 18:41:23.752 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后appList数量: 1
2025-08-14 18:41:23.756 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后deleteAppList数量: 1
2025-08-14 18:41:23.760 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析规则JSON: ruleId=168fee4bca5148a0be863080acad09ee
2025-08-14 18:41:23.770 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 Rule appList数组: [{"packName":"mark.via","apkName":"Via","versionName":"6.6.0","versionCode":"20250713","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/4d256c4123b346fd817537b491342204.apk","apkMd5":"0d1225260d03e10a8ffc8409369c442a","apkSize":2565110,"installBy":"0","isSystemApp":false,"priority":0},{"packName":"com.lingjingnet.lazycat","apkName":"懒猫趣播","versionName":"1.0.1","versionCode":"10","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/f03e70448bf9461abf1804e9da65b36f.apk","apkMd5":"accb937c84dcd0d346da7f8fee7bf0ae","apkSize":13140733,"installBy":"0","isSystemApp":false,"priority":0}]
2025-08-14 18:41:23.778 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 Rule deleteAppList数组: [{"packName":"com.chileaf.cl960.sample","apkName":"cl960","versionName":"1.1.0","versionCode":"1","apkUrl":"","apkMd5":"","apkSize":0,"installBy":"0","isSystemApp":false,"priority":0},{"packName":"plus.H52FFB9A5","apkName":"逍遥助手","versionName":"1.0","versionCode":"100","apkUrl":"","apkMd5":"","apkSize":0,"installBy":"0","isSystemApp":false,"priority":0}]
2025-08-14 18:41:23.782 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=mark.via, apkName=Via
2025-08-14 18:41:23.787 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=com.lingjingnet.lazycat, apkName=懒猫趣播
2025-08-14 18:41:23.791 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=com.chileaf.cl960.sample, apkName=cl960
2025-08-14 18:41:23.795 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=plus.H52FFB9A5, apkName=逍遥助手
2025-08-14 18:41:23.799 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后appList数量: 2
2025-08-14 18:41:23.804 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后deleteAppList数量: 2
2025-08-14 18:41:23.808 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析规则JSON: ruleId=fb4a8c49e3854b00b51034df8d6cf113
2025-08-14 18:41:23.818 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 Rule appList数组: [{"packName":"com.chileaf.cl960.sample","apkName":"cl960","versionName":"1.1.0","versionCode":"1","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/42e16b87eaa849bcaaf0f5fadcac6aab.apk","apkMd5":"54cc3a371939423d4288d63e1ac88019","apkSize":6155416,"installBy":"0","isSystemApp":false,"priority":0},{"packName":"plus.H52FFB9A5","apkName":"逍遥助手","versionName":"1.0","versionCode":"100","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/203831945bb84d42b21decf1bf936815.apk","apkMd5":"ba1ee533924eae5c408465e7cddcbda4","apkSize":4771451,"installBy":"0","isSystemApp":false,"priority":0}]
2025-08-14 18:41:23.823 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 Rule deleteAppList数组: [{"packName":"mark.via","apkName":"Via","versionName":"6.6.0","versionCode":"20250713","apkUrl":"","apkMd5":"","apkSize":0,"installBy":"0","isSystemApp":false,"priority":0}]
2025-08-14 18:41:23.828 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=com.chileaf.cl960.sample, apkName=cl960
2025-08-14 18:41:23.832 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=plus.H52FFB9A5, apkName=逍遥助手
2025-08-14 18:41:23.836 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=mark.via, apkName=Via
2025-08-14 18:41:23.840 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后appList数量: 2
2025-08-14 18:41:23.844 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后deleteAppList数量: 1
2025-08-14 18:41:23.849 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析规则JSON: ruleId=1861b5712484475383e893c83908a034
2025-08-14 18:41:23.855 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 Rule appList数组: [{"packName":"mark.via","apkName":"Via","versionName":"6.6.0","versionCode":"20250713","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/4d256c4123b346fd817537b491342204.apk","apkMd5":"0d1225260d03e10a8ffc8409369c442a","apkSize":2565110,"installBy":"0","isSystemApp":false,"priority":0}]
2025-08-14 18:41:23.864 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 Rule deleteAppList数组: [{"packName":"com.lingjingnet.lazycat","apkName":"懒猫趣播","versionName":"1.0.1","versionCode":"10","apkUrl":"","apkMd5":"","apkSize":0,"installBy":"0","isSystemApp":false,"priority":0},{"packName":"plus.H52FFB9A5","apkName":"逍遥助手","versionName":"1.0","versionCode":"100","apkUrl":"","apkMd5":"","apkSize":0,"installBy":"0","isSystemApp":false,"priority":0}]
2025-08-14 18:41:23.868 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=mark.via, apkName=Via
2025-08-14 18:41:23.872 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=com.lingjingnet.lazycat, apkName=懒猫趣播
2025-08-14 18:41:23.876 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=plus.H52FFB9A5, apkName=逍遥助手
2025-08-14 18:41:23.881 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后appList数量: 1
2025-08-14 18:41:23.885 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后deleteAppList数量: 2
2025-08-14 18:41:23.889 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 RuleBaseStorage Rule 1861b5712484475383e893c83908a034 已存在，忽略Add操作
2025-08-14 18:41:23.894 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 RuleStateMachine 规则初始化成功: 1861b5712484475383e893c83908a034 -> RuleState(code=todo, description=等待执行)
2025-08-14 18:41:23.898 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 RuleStateMachine 规则已存在: 1861b5712484475383e893c83908a034, 当前状态: RuleState(code=todo, description=等待执行)
2025-08-14 18:41:23.902 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 创建应用状态: packName=mark.via, apkName=Via
2025-08-14 18:41:23.907 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 初始化规则应用状态: 1861b5712484475383e893c83908a034, 应用数量: 3
2025-08-14 18:41:23.911 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 RuleStateMonitor 收到状态变化: 1861b5712484475383e893c83908a034, todo -> R01
2025-08-14 18:41:23.915 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 RuleStateMonitor 状态变化已记录: 1861b5712484475383e893c83908a034, R01
2025-08-14 18:41:23.919 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 RuleStateMachine 状态转换成功: 1861b5712484475383e893c83908a034, todo -> R01
2025-08-14 18:41:23.924 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 RuleStateMonitor 收到状态变化: 1861b5712484475383e893c83908a034, R01 -> R02
2025-08-14 18:41:23.928 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 RuleStateMonitor 状态变化已记录: 1861b5712484475383e893c83908a034, R02
2025-08-14 18:41:23.932 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 RuleStateMachine 状态转换成功: 1861b5712484475383e893c83908a034, R01 -> R02
2025-08-14 18:41:23.936 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 开始执行规则: 1861b5712484475383e893c83908a034
2025-08-14 18:41:23.940 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 执行卸载应用，数量: 2
2025-08-14 18:41:23.944 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 开始卸载应用，数量: 2
2025-08-14 18:41:23.948 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 卸载应用: com.lingjingnet.lazycat
2025-08-14 18:41:23.952 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 更新应用状态: 1861b5712484475383e893c83908a034, com.lingjingnet.lazycat -> D01
2025-08-14 18:41:23.957 19136-19206 WebSocket               com.dspread.mdm.service              I  🔧 C0107 主动式上送: rulebase_update (主动: 68)
2025-08-14 18:41:23.974 19136-19206 WebSocket               com.dspread.mdm.service              I  🔧 添加规则结果: ruleId=1861b5712484475383e893c83908a034
2025-08-14 18:41:23.999 19136-19206 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0107","serialNo":"01354090202503050399","request_time":"1755168083969","request_id":"1755168083969C0107","version":"1","data":{"ruleId":"1861b5712484475383e893c83908a034","taskResult":"R02","failedApkList":[{"packName":"mark.via","apkName":"Via","versionName":"6.6.0","versionCode":"20250713","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/4d256c4123b346fd817537b491342204.apk","apkMd5":"0d1225260d03e10a8ffc8409369c442a","apkSize":2565110,"installBy":"0","isSystemApp":false,"priority":0,"result":"W01","taskType":"01"},{"packName":"com.lingjingnet.lazycat","apkName":"懒猫趣播","versionName":"1.0.1","versionCode":"10","apkUrl":"","apkMd5":"","apkSize":0,"installBy":"0","isSystemApp":false,"priority":0,"result":"D01","taskType":"02"},{"packName":"plus.H52FFB9A5","apkName":"逍遥助手","versionName":"1.0","versionCode":"100","apkUrl":"","apkMd5":"","apkSize":0,"installBy":"0","isSystemApp":false,"priority":0,"result":"W01","taskType":"02"}]},"myVersionName":"1.0.08.20250814.DSPREAD.MDM.SERVICE","terminalDate":"20250814184123"}
2025-08-14 18:41:24.003 19136-19206 WebSocket               com.dspread.mdm.service              I  🔧 规则结果实时上送: ruleId=1861b5712484475383e893c83908a034, result=R02 (1)
2025-08-14 18:41:24.007 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 上报规则执行中状态: 1861b5712484475383e893c83908a034, 应用数量: 3
2025-08-14 18:41:24.011 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 注册卸载回调: com.lingjingnet.lazycat (规则ID: 1861b5712484475383e893c83908a034)
2025-08-14 18:41:24.015 19136-19206 AppManager              com.dspread.mdm.service              I  ℹ️ Uninstalling app via PackageInstaller: com.lingjingnet.lazycat
2025-08-14 18:41:24.019 19136-19206 AppManager              com.dspread.mdm.service              I  ℹ️ Deleting package via PackageInstaller: com.lingjingnet.lazycat
2025-08-14 18:41:24.026 19136-19206 AppManager              com.dspread.mdm.service              I  ℹ️ PackageInstaller uninstall initiated
2025-08-14 18:41:24.031 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 卸载应用: plus.H52FFB9A5
2025-08-14 18:41:24.036 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 更新应用状态: 1861b5712484475383e893c83908a034, plus.H52FFB9A5 -> D01
2025-08-14 18:41:24.041 19136-19206 WebSocket               com.dspread.mdm.service              I  🔧 C0107 主动式上送: rulebase_update (主动: 69)
2025-08-14 18:41:24.109 19136-19206 WebSocket               com.dspread.mdm.service              I  🔧 添加规则结果: ruleId=1861b5712484475383e893c83908a034
2025-08-14 18:41:24.135 19136-19206 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0107","serialNo":"01354090202503050399","request_time":"1755168084053","request_id":"1755168084053C0107","version":"1","data":{"ruleId":"1861b5712484475383e893c83908a034","taskResult":"R02","failedApkList":[{"packName":"mark.via","apkName":"Via","versionName":"6.6.0","versionCode":"20250713","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/4d256c4123b346fd817537b491342204.apk","apkMd5":"0d1225260d03e10a8ffc8409369c442a","apkSize":2565110,"installBy":"0","isSystemApp":false,"priority":0,"result":"W01","taskType":"01"},{"packName":"com.lingjingnet.lazycat","apkName":"懒猫趣播","versionName":"1.0.1","versionCode":"10","apkUrl":"","apkMd5":"","apkSize":0,"installBy":"0","isSystemApp":false,"priority":0,"result":"D01","taskType":"02"},{"packName":"plus.H52FFB9A5","apkName":"逍遥助手","versionName":"1.0","versionCode":"100","apkUrl":"","apkMd5":"","apkSize":0,"installBy":"0","isSystemApp":false,"priority":0,"result":"D01","taskType":"02"}]},"myVersionName":"1.0.08.20250814.DSPREAD.MDM.SERVICE","terminalDate":"20250814184124"}
2025-08-14 18:41:24.140 19136-19206 WebSocket               com.dspread.mdm.service              I  🔧 规则结果实时上送: ruleId=1861b5712484475383e893c83908a034, result=R02 (1)
2025-08-14 18:41:24.144 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 上报规则执行中状态: 1861b5712484475383e893c83908a034, 应用数量: 3
2025-08-14 18:41:24.149 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 注册卸载回调: plus.H52FFB9A5 (规则ID: 1861b5712484475383e893c83908a034)
2025-08-14 18:41:24.154 19136-19206 AppManager              com.dspread.mdm.service              I  ℹ️ Uninstalling app via PackageInstaller: plus.H52FFB9A5
2025-08-14 18:41:24.158 19136-19206 AppManager              com.dspread.mdm.service              I  ℹ️ Deleting package via PackageInstaller: plus.H52FFB9A5
2025-08-14 18:41:24.176 19136-19206 AppManager              com.dspread.mdm.service              I  ℹ️ PackageInstaller uninstall initiated
2025-08-14 18:41:24.181 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 执行安装应用，数量: 1
2025-08-14 18:41:24.186 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 开始安装应用，数量: 1
2025-08-14 18:41:24.190 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 安装应用: mark.via, URL: https://siot-bucket01.s3.ap-northeast-1.amazonaws.com/app/4d256c4123b346fd817537b491342204.apk
2025-08-14 18:41:24.195 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 检查应用安装状态: mark.via
2025-08-14 18:41:24.202 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 应用是否已安装: false
2025-08-14 18:41:24.208 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 应用未安装，需要安装
2025-08-14 18:41:24.213 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 更新应用状态: 1861b5712484475383e893c83908a034, mark.via -> A01
2025-08-14 18:41:24.218 19136-19206 WebSocket               com.dspread.mdm.service              I  🔧 C0107 主动式上送: rulebase_update (主动: 70)
2025-08-14 18:41:24.287 19136-19206 WebSocket               com.dspread.mdm.service              I  🔧 添加规则结果: ruleId=1861b5712484475383e893c83908a034
2025-08-14 18:41:24.323 19136-19206 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0107","serialNo":"01354090202503050399","request_time":"1755168084231","request_id":"1755168084231C0107","version":"1","data":{"ruleId":"1861b5712484475383e893c83908a034","taskResult":"R02","failedApkList":[{"packName":"mark.via","apkName":"Via","versionName":"6.6.0","versionCode":"20250713","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/4d256c4123b346fd817537b491342204.apk","apkMd5":"0d1225260d03e10a8ffc8409369c442a","apkSize":2565110,"installBy":"0","isSystemApp":false,"priority":0,"result":"A01","taskType":"01"},{"packName":"com.lingjingnet.lazycat","apkName":"懒猫趣播","versionName":"1.0.1","versionCode":"10","apkUrl":"","apkMd5":"","apkSize":0,"installBy":"0","isSystemApp":false,"priority":0,"result":"D01","taskType":"02"},{"packName":"plus.H52FFB9A5","apkName":"逍遥助手","versionName":"1.0","versionCode":"100","apkUrl":"","apkMd5":"","apkSize":0,"installBy":"0","isSystemApp":false,"priority":0,"result":"D01","taskType":"02"}]},"myVersionName":"1.0.08.20250814.DSPREAD.MDM.SERVICE","terminalDate":"20250814184124"}
2025-08-14 18:41:24.341 19136-19206 WebSocket               com.dspread.mdm.service              I  🔧 规则结果实时上送: ruleId=1861b5712484475383e893c83908a034, result=R02 (1)
2025-08-14 18:41:24.346 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 上报规则执行中状态: 1861b5712484475383e893c83908a034, 应用数量: 3
2025-08-14 18:41:24.348 19136-19207 Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"request_time":"1755168083125","org_request_time":"1755168083969","org_request_id":"1755168083969C0107","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755168083125S0000","serialNo":"01354090202503050399"}
2025-08-14 18:41:24.352 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 规则执行启动成功: 1861b5712484475383e893c83908a034
2025-08-14 18:41:24.353 19136-19590 HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 尝试下载，第1次
2025-08-14 18:41:24.356 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 RuleBaseManager 规则添加成功: 1861b5712484475383e893c83908a034
2025-08-14 18:41:24.357 19136-19590 HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 开始下载
2025-08-14 18:41:24.358 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 收到响应消息: transCode=S0000, orgRequestId=1755168083969C0107, state=0, remark=
2025-08-14 18:41:24.362 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 处理规则结果响应: state=0
2025-08-14 18:41:24.369 19136-19590 HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader URL: https://siot-bucket01.s3.ap-northeast-1.amazonaws.com/app/4d256c4123b346fd817537b491342204.apk
2025-08-14 18:41:24.374 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 规则结果上传成功
2025-08-14 18:41:24.376 19136-19590 HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 路径: /data/user/0/com.dspread.mdm.service/files/downloads/rule_1861b5712484475383e893c83908a034_mark.via.apk
2025-08-14 18:41:24.383 19136-19590 HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 本地文件不存在，创建文件
2025-08-14 18:41:24.390 19136-19590 HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader allFileLength=2565110, 需要从服务器获取文件大小
2025-08-14 18:41:24.429 19136-19207 Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"request_time":"1755168083255","org_request_time":"1755168084053","org_request_id":"1755168084053C0107","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755168083255S0000","serialNo":"01354090202503050399"}
2025-08-14 18:41:24.443 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 收到响应消息: transCode=S0000, orgRequestId=1755168084053C0107, state=0, remark=
2025-08-14 18:41:24.448 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 处理规则结果响应: state=0
2025-08-14 18:41:24.452 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 规则结果上传成功
2025-08-14 18:41:24.480 19136-19207 Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"request_time":"1755168083436","org_request_time":"1755168084231","org_request_id":"1755168084231C0107","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755168083436S0000","serialNo":"01354090202503050399"}
2025-08-14 18:41:24.487 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 收到响应消息: transCode=S0000, orgRequestId=1755168084231C0107, state=0, remark=
2025-08-14 18:41:24.492 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 处理规则结果响应: state=0
2025-08-14 18:41:24.495 19136-19136 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 收到广播: android.intent.action.PACKAGE_REMOVED
2025-08-14 18:41:24.497 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 规则结果上传成功
2025-08-14 18:41:24.503 19136-19136 Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 收到包更新广播: android.intent.action.PACKAGE_REMOVED, 包名: com.lingjingnet.lazycat
2025-08-14 18:41:24.544 19136-19136 Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 应用卸载: com.lingjingnet.lazycat
2025-08-14 18:41:24.554 19136-19136 Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 检查WebSocket状态: packageName=com.lingjingnet.lazycat, action=UNINSTALL
2025-08-14 18:41:24.564 19136-19136 Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 通知rulebase处理应用卸载: com.lingjingnet.lazycat
2025-08-14 18:41:24.584 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 收到系统广播，应用已卸载: com.lingjingnet.lazycat
2025-08-14 18:41:24.595 19136-19590 TrafficStats            com.dspread.mdm.service              D  tagSocket(98) with statsTag=0xffffffff, statsUid=-1
2025-08-14 18:41:24.600 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 找到匹配的卸载回调，触发: com.lingjingnet.lazycat
2025-08-14 18:41:24.605 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 卸载回调被触发: pkg=com.lingjingnet.lazycat, returnCode=1, error=
2025-08-14 18:41:24.610 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 应用卸载启动成功: com.lingjingnet.lazycat
2025-08-14 18:41:24.615 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 从广播更新应用状态: 1861b5712484475383e893c83908a034, com.lingjingnet.lazycat -> D02
2025-08-14 18:41:24.620 19136-19136 WebSocket               com.dspread.mdm.service              I  🔧 C0107 主动式上送: rulebase_update (主动: 71)
2025-08-14 18:41:24.640 19136-19136 WebSocket               com.dspread.mdm.service              I  🔧 添加规则结果: ruleId=1861b5712484475383e893c83908a034
2025-08-14 18:41:24.666 19136-19136 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0107","serialNo":"01354090202503050399","request_time":"1755168084633","request_id":"1755168084633C0107","version":"1","data":{"ruleId":"1861b5712484475383e893c83908a034","taskResult":"R02","failedApkList":[{"packName":"mark.via","apkName":"Via","versionName":"6.6.0","versionCode":"20250713","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/4d256c4123b346fd817537b491342204.apk","apkMd5":"0d1225260d03e10a8ffc8409369c442a","apkSize":2565110,"installBy":"0","isSystemApp":false,"priority":0,"result":"A01","taskType":"01"},{"packName":"com.lingjingnet.lazycat","apkName":"懒猫趣播","versionName":"1.0.1","versionCode":"10","apkUrl":"","apkMd5":"","apkSize":0,"installBy":"0","isSystemApp":false,"priority":0,"result":"D02","taskType":"02"},{"packName":"plus.H52FFB9A5","apkName":"逍遥助手","versionName":"1.0","versionCode":"100","apkUrl":"","apkMd5":"","apkSize":0,"installBy":"0","isSystemApp":false,"priority":0,"result":"D01","taskType":"02"}]},"myVersionName":"1.0.08.20250814.DSPREAD.MDM.SERVICE","terminalDate":"20250814184124"}
2025-08-14 18:41:24.671 19136-19136 WebSocket               com.dspread.mdm.service              I  🔧 规则结果实时上送: ruleId=1861b5712484475383e893c83908a034, result=R02 (1)
2025-08-14 18:41:24.675 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 上报规则执行中状态: 1861b5712484475383e893c83908a034, 应用数量: 3
2025-08-14 18:41:24.680 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 开始检查规则完成状态: 1861b5712484475383e893c83908a034, 总应用数: 3
2025-08-14 18:41:24.685 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 检查应用状态: mark.via -> A01
2025-08-14 18:41:24.689 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 发现未完成状态: mark.via -> A01
2025-08-14 18:41:24.693 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 规则完成检查结果: 1861b5712484475383e893c83908a034, 全部完成: false, 完成数: 0/3, 有失败: false
2025-08-14 18:41:24.698 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 规则未完成: 1861b5712484475383e893c83908a034, 完成数: 0/3
2025-08-14 18:41:24.702 19136-19136 Platform                com.dspread.mdm.service              D  🔧 重新收集应用信息（强制刷新）
2025-08-14 18:41:24.803 19136-19136 AppManager              com.dspread.mdm.service              I  ℹ️ 应用信息获取完成: 总数131(系统129/用户2) 返回2个
2025-08-14 18:41:24.820 19136-19136 Platform                com.dspread.mdm.service              D  🔧 应用信息: 2 个应用
2025-08-14 18:41:24.825 19136-19136 Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 应用信息缓存刷新完成
2025-08-14 18:41:24.830 19136-19136 ContextImpl             com.dspread.mdm.service              W  Calling a method in the system process without a qualified user: android.app.ContextImpl.sendBroadcast:1220 android.content.ContextWrapper.sendBroadcast:504 com.dspread.mdm.service.broadcast.core.BroadcastSender.sendBroadcast:99 com.dspread.mdm.service.broadcast.core.BroadcastSender.sendBroadcast:23 com.dspread.mdm.service.broadcast.handlers.system.PackageUpdateEventHandlerImpl.sendAppRefreshBroadcast:491 
2025-08-14 18:41:24.833 19136-19136 Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 发送应用刷新广播
2025-08-14 18:41:24.838 19136-19136 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 收到广播: android.intent.action.PACKAGE_REMOVED
2025-08-14 18:41:24.844 19136-19136 Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 收到包更新广播: android.intent.action.PACKAGE_REMOVED, 包名: com.lingjingnet.lazycat
2025-08-14 18:41:24.848 19136-19136 Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 应用卸载: com.lingjingnet.lazycat
2025-08-14 18:41:24.861 19136-19136 Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 检查WebSocket状态: packageName=com.lingjingnet.lazycat, action=UNINSTALL
2025-08-14 18:41:24.866 19136-19136 Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 通知rulebase处理应用卸载: com.lingjingnet.lazycat
2025-08-14 18:41:24.872 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 收到系统广播，应用已卸载: com.lingjingnet.lazycat
2025-08-14 18:41:24.877 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 未找到匹配的卸载回调: com.lingjingnet.lazycat
2025-08-14 18:41:24.950 19136-19207 Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"request_time":"1755168083787","org_request_time":"1755168084633","org_request_id":"1755168084633C0107","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755168083787S0000","serialNo":"01354090202503050399"}
2025-08-14 18:41:24.959 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 收到响应消息: transCode=S0000, orgRequestId=1755168084633C0107, state=0, remark=
2025-08-14 18:41:24.983 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 处理规则结果响应: state=0
2025-08-14 18:41:24.998 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 规则结果上传成功
2025-08-14 18:41:25.040 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 从广播更新应用状态: 1861b5712484475383e893c83908a034, com.lingjingnet.lazycat -> D02
2025-08-14 18:41:25.047 19136-19136 WebSocket               com.dspread.mdm.service              I  🔧 C0107 主动式上送: rulebase_update (主动: 72)
2025-08-14 18:41:25.085 19136-19136 WebSocket               com.dspread.mdm.service              I  🔧 添加规则结果: ruleId=1861b5712484475383e893c83908a034
2025-08-14 18:41:25.123 19136-19136 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0107","serialNo":"01354090202503050399","request_time":"1755168085072","request_id":"1755168085072C0107","version":"1","data":{"ruleId":"1861b5712484475383e893c83908a034","taskResult":"R02","failedApkList":[{"packName":"mark.via","apkName":"Via","versionName":"6.6.0","versionCode":"20250713","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/4d256c4123b346fd817537b491342204.apk","apkMd5":"0d1225260d03e10a8ffc8409369c442a","apkSize":2565110,"installBy":"0","isSystemApp":false,"priority":0,"result":"A01","taskType":"01"},{"packName":"com.lingjingnet.lazycat","apkName":"懒猫趣播","versionName":"1.0.1","versionCode":"10","apkUrl":"","apkMd5":"","apkSize":0,"installBy":"0","isSystemApp":false,"priority":0,"result":"D02","taskType":"02"},{"packName":"plus.H52FFB9A5","apkName":"逍遥助手","versionName":"1.0","versionCode":"100","apkUrl":"","apkMd5":"","apkSize":0,"installBy":"0","isSystemApp":false,"priority":0,"result":"D01","taskType":"02"}]},"myVersionName":"1.0.08.20250814.DSPREAD.MDM.SERVICE","terminalDate":"20250814184125"}
2025-08-14 18:41:25.130 19136-19136 WebSocket               com.dspread.mdm.service              I  🔧 规则结果实时上送: ruleId=1861b5712484475383e893c83908a034, result=R02 (1)
2025-08-14 18:41:25.134 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 上报规则执行中状态: 1861b5712484475383e893c83908a034, 应用数量: 3
2025-08-14 18:41:25.141 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 开始检查规则完成状态: 1861b5712484475383e893c83908a034, 总应用数: 3
2025-08-14 18:41:25.150 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 检查应用状态: mark.via -> A01
2025-08-14 18:41:25.154 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 发现未完成状态: mark.via -> A01
2025-08-14 18:41:25.161 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 规则完成检查结果: 1861b5712484475383e893c83908a034, 全部完成: false, 完成数: 0/3, 有失败: false
2025-08-14 18:41:25.165 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 规则未完成: 1861b5712484475383e893c83908a034, 完成数: 0/3
2025-08-14 18:41:25.177 19136-19136 Platform                com.dspread.mdm.service              D  🔧 重新收集应用信息（强制刷新）
2025-08-14 18:41:25.245 19136-19590 HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader HTTP响应码: 200
2025-08-14 18:41:25.257 19136-19590 HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 服务器返回Content-Length: 2565110
2025-08-14 18:41:25.271 19136-19590 HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader localFile = true /data/user/0/com.dspread.mdm.service/files/downloads/rule_1861b5712484475383e893c83908a034_mark.via.apk
2025-08-14 18:41:25.279 19136-19590 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 下载进度: mark.via - 0%
2025-08-14 18:41:25.290 19136-19136 AppManager              com.dspread.mdm.service              I  ℹ️ 应用信息获取完成: 总数131(系统129/用户2) 返回2个
2025-08-14 18:41:25.306 19136-19136 Platform                com.dspread.mdm.service              D  🔧 应用信息: 2 个应用
2025-08-14 18:41:25.311 19136-19136 Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 应用信息缓存刷新完成
2025-08-14 18:41:25.316 19136-19136 ContextImpl             com.dspread.mdm.service              W  Calling a method in the system process without a qualified user: android.app.ContextImpl.sendBroadcast:1220 android.content.ContextWrapper.sendBroadcast:504 com.dspread.mdm.service.broadcast.core.BroadcastSender.sendBroadcast:99 com.dspread.mdm.service.broadcast.core.BroadcastSender.sendBroadcast:23 com.dspread.mdm.service.broadcast.handlers.system.PackageUpdateEventHandlerImpl.sendAppRefreshBroadcast:491 
2025-08-14 18:41:25.318 19136-19136 Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 发送应用刷新广播
2025-08-14 18:41:25.322 19136-19136 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 收到广播: android.intent.action.PACKAGE_REMOVED
2025-08-14 18:41:25.327 19136-19136 Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 收到包更新广播: android.intent.action.PACKAGE_REMOVED, 包名: plus.H52FFB9A5
2025-08-14 18:41:25.331 19136-19207 Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"request_time":"1755168084241","org_request_time":"1755168085072","org_request_id":"1755168085072C0107","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755168084241S0000","serialNo":"01354090202503050399"}
2025-08-14 18:41:25.332 19136-19136 Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 应用卸载: plus.H52FFB9A5
2025-08-14 18:41:25.340 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 收到响应消息: transCode=S0000, orgRequestId=1755168085072C0107, state=0, remark=
2025-08-14 18:41:25.342 19136-19136 Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 检查WebSocket状态: packageName=plus.H52FFB9A5, action=UNINSTALL
2025-08-14 18:41:25.344 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 处理规则结果响应: state=0
2025-08-14 18:41:25.348 19136-19136 Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 通知rulebase处理应用卸载: plus.H52FFB9A5
2025-08-14 18:41:25.349 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 规则结果上传成功
2025-08-14 18:41:25.354 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 收到系统广播，应用已卸载: plus.H52FFB9A5
2025-08-14 18:41:25.358 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 找到匹配的卸载回调，触发: plus.H52FFB9A5
2025-08-14 18:41:25.363 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 卸载回调被触发: pkg=plus.H52FFB9A5, returnCode=1, error=
2025-08-14 18:41:25.367 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 应用卸载启动成功: plus.H52FFB9A5
2025-08-14 18:41:25.372 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 从广播更新应用状态: 1861b5712484475383e893c83908a034, plus.H52FFB9A5 -> D02
2025-08-14 18:41:25.377 19136-19136 WebSocket               com.dspread.mdm.service              I  🔧 C0107 主动式上送: rulebase_update (主动: 73)
2025-08-14 18:41:25.397 19136-19136 WebSocket               com.dspread.mdm.service              I  🔧 添加规则结果: ruleId=1861b5712484475383e893c83908a034
2025-08-14 18:41:25.422 19136-19136 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0107","serialNo":"01354090202503050399","request_time":"1755168085391","request_id":"1755168085391C0107","version":"1","data":{"ruleId":"1861b5712484475383e893c83908a034","taskResult":"R02","failedApkList":[{"packName":"mark.via","apkName":"Via","versionName":"6.6.0","versionCode":"20250713","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/4d256c4123b346fd817537b491342204.apk","apkMd5":"0d1225260d03e10a8ffc8409369c442a","apkSize":2565110,"installBy":"0","isSystemApp":false,"priority":0,"result":"A01","taskType":"01","errorMsg":"下载中: 0%"},{"packName":"com.lingjingnet.lazycat","apkName":"懒猫趣播","versionName":"1.0.1","versionCode":"10","apkUrl":"","apkMd5":"","apkSize":0,"installBy":"0","isSystemApp":false,"priority":0,"result":"D02","taskType":"02"},{"packName":"plus.H52FFB9A5","apkName":"逍遥助手","versionName":"1.0","versionCode":"100","apkUrl":"","apkMd5":"","apkSize":0,"installBy":"0","isSystemApp":false,"priority":0,"result":"D02","taskType":"02"}]},"myVersionName":"1.0.08.20250814.DSPREAD.MDM.SERVICE","terminalDate":"20250814184125"}
2025-08-14 18:41:25.426 19136-19136 WebSocket               com.dspread.mdm.service              I  🔧 规则结果实时上送: ruleId=1861b5712484475383e893c83908a034, result=R02 (1)
2025-08-14 18:41:25.430 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 上报规则执行中状态: 1861b5712484475383e893c83908a034, 应用数量: 3
2025-08-14 18:41:25.435 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 开始检查规则完成状态: 1861b5712484475383e893c83908a034, 总应用数: 3
2025-08-14 18:41:25.439 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 检查应用状态: mark.via -> A01
2025-08-14 18:41:25.444 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 发现未完成状态: mark.via -> A01
2025-08-14 18:41:25.449 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 规则完成检查结果: 1861b5712484475383e893c83908a034, 全部完成: false, 完成数: 0/3, 有失败: false
2025-08-14 18:41:25.453 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 规则未完成: 1861b5712484475383e893c83908a034, 完成数: 0/3
2025-08-14 18:41:25.458 19136-19136 Platform                com.dspread.mdm.service              D  🔧 重新收集应用信息（强制刷新）
2025-08-14 18:41:25.537 19136-19136 AppManager              com.dspread.mdm.service              I  ℹ️ 应用信息获取完成: 总数131(系统129/用户2) 返回2个
2025-08-14 18:41:25.555 19136-19136 Platform                com.dspread.mdm.service              D  🔧 应用信息: 2 个应用
2025-08-14 18:41:25.559 19136-19136 Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 应用信息缓存刷新完成
2025-08-14 18:41:25.565 19136-19136 ContextImpl             com.dspread.mdm.service              W  Calling a method in the system process without a qualified user: android.app.ContextImpl.sendBroadcast:1220 android.content.ContextWrapper.sendBroadcast:504 com.dspread.mdm.service.broadcast.core.BroadcastSender.sendBroadcast:99 com.dspread.mdm.service.broadcast.core.BroadcastSender.sendBroadcast:23 com.dspread.mdm.service.broadcast.handlers.system.PackageUpdateEventHandlerImpl.sendAppRefreshBroadcast:491 
2025-08-14 18:41:25.566 19136-19136 Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 发送应用刷新广播
2025-08-14 18:41:25.572 19136-19136 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 收到广播: android.intent.action.PACKAGE_REMOVED
2025-08-14 18:41:25.577 19136-19136 Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 收到包更新广播: android.intent.action.PACKAGE_REMOVED, 包名: plus.H52FFB9A5
2025-08-14 18:41:25.582 19136-19136 Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 应用卸载: plus.H52FFB9A5
2025-08-14 18:41:25.587 19136-19136 Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 检查WebSocket状态: packageName=plus.H52FFB9A5, action=UNINSTALL
2025-08-14 18:41:25.592 19136-19136 Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 通知rulebase处理应用卸载: plus.H52FFB9A5
2025-08-14 18:41:25.592 19136-19207 Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"request_time":"1755168084545","org_request_time":"1755168085391","org_request_id":"1755168085391C0107","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755168084545S0000","serialNo":"01354090202503050399"}
2025-08-14 18:41:25.596 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 收到系统广播，应用已卸载: plus.H52FFB9A5
2025-08-14 18:41:25.599 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 收到响应消息: transCode=S0000, orgRequestId=1755168085391C0107, state=0, remark=
2025-08-14 18:41:25.600 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 未找到匹配的卸载回调: plus.H52FFB9A5
2025-08-14 18:41:25.603 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 处理规则结果响应: state=0
2025-08-14 18:41:25.605 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 从广播更新应用状态: 1861b5712484475383e893c83908a034, plus.H52FFB9A5 -> D02
2025-08-14 18:41:25.607 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 规则结果上传成功
2025-08-14 18:41:25.609 19136-19136 WebSocket               com.dspread.mdm.service              I  🔧 C0107 主动式上送: rulebase_update (主动: 74)
2025-08-14 18:41:25.627 19136-19136 WebSocket               com.dspread.mdm.service              I  🔧 添加规则结果: ruleId=1861b5712484475383e893c83908a034
2025-08-14 18:41:25.653 19136-19136 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0107","serialNo":"01354090202503050399","request_time":"1755168085620","request_id":"1755168085620C0107","version":"1","data":{"ruleId":"1861b5712484475383e893c83908a034","taskResult":"R02","failedApkList":[{"packName":"mark.via","apkName":"Via","versionName":"6.6.0","versionCode":"20250713","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/4d256c4123b346fd817537b491342204.apk","apkMd5":"0d1225260d03e10a8ffc8409369c442a","apkSize":2565110,"installBy":"0","isSystemApp":false,"priority":0,"result":"A01","taskType":"01","errorMsg":"下载中: 0%"},{"packName":"com.lingjingnet.lazycat","apkName":"懒猫趣播","versionName":"1.0.1","versionCode":"10","apkUrl":"","apkMd5":"","apkSize":0,"installBy":"0","isSystemApp":false,"priority":0,"result":"D02","taskType":"02"},{"packName":"plus.H52FFB9A5","apkName":"逍遥助手","versionName":"1.0","versionCode":"100","apkUrl":"","apkMd5":"","apkSize":0,"installBy":"0","isSystemApp":false,"priority":0,"result":"D02","taskType":"02"}]},"myVersionName":"1.0.08.20250814.DSPREAD.MDM.SERVICE","terminalDate":"20250814184125"}
2025-08-14 18:41:25.658 19136-19136 WebSocket               com.dspread.mdm.service              I  🔧 规则结果实时上送: ruleId=1861b5712484475383e893c83908a034, result=R02 (1)
2025-08-14 18:41:25.662 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 上报规则执行中状态: 1861b5712484475383e893c83908a034, 应用数量: 3
2025-08-14 18:41:25.667 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 开始检查规则完成状态: 1861b5712484475383e893c83908a034, 总应用数: 3
2025-08-14 18:41:25.671 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 检查应用状态: mark.via -> A01
2025-08-14 18:41:25.676 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 发现未完成状态: mark.via -> A01
2025-08-14 18:41:25.680 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 规则完成检查结果: 1861b5712484475383e893c83908a034, 全部完成: false, 完成数: 0/3, 有失败: false
2025-08-14 18:41:25.683 19136-19590 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 下载进度: mark.via - 10%
2025-08-14 18:41:25.684 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 规则未完成: 1861b5712484475383e893c83908a034, 完成数: 0/3
2025-08-14 18:41:25.688 19136-19136 Platform                com.dspread.mdm.service              D  🔧 重新收集应用信息（强制刷新）
2025-08-14 18:41:25.774 19136-19136 AppManager              com.dspread.mdm.service              I  ℹ️ 应用信息获取完成: 总数131(系统129/用户2) 返回2个
2025-08-14 18:41:25.788 19136-19136 Platform                com.dspread.mdm.service              D  🔧 应用信息: 2 个应用
2025-08-14 18:41:25.792 19136-19136 Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 应用信息缓存刷新完成
2025-08-14 18:41:25.797 19136-19136 ContextImpl             com.dspread.mdm.service              W  Calling a method in the system process without a qualified user: android.app.ContextImpl.sendBroadcast:1220 android.content.ContextWrapper.sendBroadcast:504 com.dspread.mdm.service.broadcast.core.BroadcastSender.sendBroadcast:99 com.dspread.mdm.service.broadcast.core.BroadcastSender.sendBroadcast:23 com.dspread.mdm.service.broadcast.handlers.system.PackageUpdateEventHandlerImpl.sendAppRefreshBroadcast:491 
2025-08-14 18:41:25.799 19136-19136 Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 发送应用刷新广播
2025-08-14 18:41:25.805 19136-19206 Common                  com.dspread.mdm.service              D  🔧 ServiceGuardEventHandler 开始执行保活动作
2025-08-14 18:41:25.813 19136-19206 Common                  com.dspread.mdm.service              I  ✅ ⏰ 设置服务守护定时器成功，下次执行: 120秒后 (2分钟)
2025-08-14 18:41:25.817 19136-19206 ContextImpl             com.dspread.mdm.service              W  Calling a method in the system process without a qualified user: android.app.ContextImpl.startService:1899 android.content.ContextWrapper.startService:825 com.dspread.mdm.service.broadcast.handlers.service.ServiceGuardEventHandler.startKeepAliveService:211 com.dspread.mdm.service.broadcast.handlers.service.ServiceGuardEventHandler.performKeepAliveActions:193 com.dspread.mdm.service.broadcast.handlers.service.ServiceGuardEventHandler.access$performKeepAliveActions:24 
2025-08-14 18:41:25.820 19136-19206 Common                  com.dspread.mdm.service              D  🔧 ServiceGuardEventHandler 启动保活服务
2025-08-14 18:41:25.821 19136-19136 Service                 com.dspread.mdm.service              D  🔧 ServiceKeepAliveService 保活服务启动
2025-08-14 18:41:25.825 19136-19136 Platform                com.dspread.mdm.service              D  🔧 WakeLockManager WakeLock不存在或未持有: ServiceKeepAlive
2025-08-14 18:41:25.828 19136-19206 Common                  com.dspread.mdm.service              D  🔧 ServiceGuardEventHandler 保活动作执行完成
2025-08-14 18:41:25.830 19136-19136 Platform                com.dspread.mdm.service              D  🔧 WakeLockManager 获取WakeLock成功: ServiceKeepAlive (超时: 30000ms)
2025-08-14 18:41:25.877 19136-19207 Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"request_time":"1755168084797","org_request_time":"1755168085620","org_request_id":"1755168085620C0107","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755168084797S0000","serialNo":"01354090202503050399"}
2025-08-14 18:41:25.887 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 收到响应消息: transCode=S0000, orgRequestId=1755168085620C0107, state=0, remark=
2025-08-14 18:41:25.893 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 处理规则结果响应: state=0
2025-08-14 18:41:25.898 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 规则结果上传成功
2025-08-14 18:41:26.087 19136-19590 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 下载进度: mark.via - 20%
2025-08-14 18:41:26.436 19136-19590 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 下载进度: mark.via - 30%
2025-08-14 18:41:26.712 19136-19590 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 下载进度: mark.via - 40%
2025-08-14 18:41:27.004 19136-19590 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 下载进度: mark.via - 50%
2025-08-14 18:41:27.298 19136-19590 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 下载进度: mark.via - 60%
2025-08-14 18:41:27.673 19136-19590 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 下载进度: mark.via - 70%
2025-08-14 18:41:27.974 19136-19590 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 下载进度: mark.via - 80%
2025-08-14 18:41:28.348 19136-19590 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 下载进度: mark.via - 90%
2025-08-14 18:41:28.601 19136-19590 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 下载进度: mark.via - 100%
2025-08-14 18:41:28.608 19136-19590 HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 计算MD5: rule_1861b5712484475383e893c83908a034_mark.via.apk
2025-08-14 18:41:28.654 19136-19590 HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 文件: 2565110 MD5: 0d1225260d03e10a8ffc8409369c442a
2025-08-14 18:41:28.658 19136-19590 HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader MD5校验正确
2025-08-14 18:41:28.662 19136-19590 HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 清理连接资源
2025-08-14 18:41:28.666 19136-19590 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine APK下载成功: /data/user/0/com.dspread.mdm.service/files/downloads/rule_1861b5712484475383e893c83908a034_mark.via.apk
2025-08-14 18:41:28.670 19136-19590 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 更新应用状态: 1861b5712484475383e893c83908a034, mark.via -> A03
2025-08-14 18:41:28.674 19136-19590 WebSocket               com.dspread.mdm.service              I  🔧 C0107 主动式上送: rulebase_update (主动: 75)
2025-08-14 18:41:28.691 19136-19590 WebSocket               com.dspread.mdm.service              I  🔧 添加规则结果: ruleId=1861b5712484475383e893c83908a034
2025-08-14 18:41:28.716 19136-19590 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0107","serialNo":"01354090202503050399","request_time":"1755168088686","request_id":"1755168088686C0107","version":"1","data":{"ruleId":"1861b5712484475383e893c83908a034","taskResult":"R02","failedApkList":[{"packName":"mark.via","apkName":"Via","versionName":"6.6.0","versionCode":"20250713","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/4d256c4123b346fd817537b491342204.apk","apkMd5":"0d1225260d03e10a8ffc8409369c442a","apkSize":2565110,"installBy":"0","isSystemApp":false,"priority":0,"result":"A03","taskType":"01"},{"packName":"com.lingjingnet.lazycat","apkName":"懒猫趣播","versionName":"1.0.1","versionCode":"10","apkUrl":"","apkMd5":"","apkSize":0,"installBy":"0","isSystemApp":false,"priority":0,"result":"D02","taskType":"02"},{"packName":"plus.H52FFB9A5","apkName":"逍遥助手","versionName":"1.0","versionCode":"100","apkUrl":"","apkMd5":"","apkSize":0,"installBy":"0","isSystemApp":false,"priority":0,"result":"D02","taskType":"02"}]},"myVersionName":"1.0.08.20250814.DSPREAD.MDM.SERVICE","terminalDate":"20250814184128"}
2025-08-14 18:41:28.721 19136-19590 WebSocket               com.dspread.mdm.service              I  🔧 规则结果实时上送: ruleId=1861b5712484475383e893c83908a034, result=R02 (1)
2025-08-14 18:41:28.725 19136-19590 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 上报规则执行中状态: 1861b5712484475383e893c83908a034, 应用数量: 3
2025-08-14 18:41:28.729 19136-19590 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 开始安装APK: /data/user/0/com.dspread.mdm.service/files/downloads/rule_1861b5712484475383e893c83908a034_mark.via.apk
2025-08-14 18:41:28.733 19136-19590 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 更新应用状态: 1861b5712484475383e893c83908a034, mark.via -> B02
2025-08-14 18:41:28.737 19136-19590 WebSocket               com.dspread.mdm.service              I  🔧 C0107 主动式上送: rulebase_update (主动: 76)
2025-08-14 18:41:28.754 19136-19590 WebSocket               com.dspread.mdm.service              I  🔧 添加规则结果: ruleId=1861b5712484475383e893c83908a034
2025-08-14 18:41:28.779 19136-19590 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0107","serialNo":"01354090202503050399","request_time":"1755168088749","request_id":"1755168088749C0107","version":"1","data":{"ruleId":"1861b5712484475383e893c83908a034","taskResult":"R02","failedApkList":[{"packName":"mark.via","apkName":"Via","versionName":"6.6.0","versionCode":"20250713","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/4d256c4123b346fd817537b491342204.apk","apkMd5":"0d1225260d03e10a8ffc8409369c442a","apkSize":2565110,"installBy":"0","isSystemApp":false,"priority":0,"result":"B02","taskType":"01"},{"packName":"com.lingjingnet.lazycat","apkName":"懒猫趣播","versionName":"1.0.1","versionCode":"10","apkUrl":"","apkMd5":"","apkSize":0,"installBy":"0","isSystemApp":false,"priority":0,"result":"D02","taskType":"02"},{"packName":"plus.H52FFB9A5","apkName":"逍遥助手","versionName":"1.0","versionCode":"100","apkUrl":"","apkMd5":"","apkSize":0,"installBy":"0","isSystemApp":false,"priority":0,"result":"D02","taskType":"02"}]},"myVersionName":"1.0.08.20250814.DSPREAD.MDM.SERVICE","terminalDate":"20250814184128"}
2025-08-14 18:41:28.783 19136-19590 WebSocket               com.dspread.mdm.service              I  🔧 规则结果实时上送: ruleId=1861b5712484475383e893c83908a034, result=R02 (1)
2025-08-14 18:41:28.787 19136-19590 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 上报规则执行中状态: 1861b5712484475383e893c83908a034, 应用数量: 3
2025-08-14 18:41:28.791 19136-19590 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 注册安装回调: mark.via (规则ID: 1861b5712484475383e893c83908a034)
2025-08-14 18:41:28.809 19136-19590 PackageParser           com.dspread.mdm.service              W  Unknown element under <manifest>: queries at /data/user/0/com.dspread.mdm.service/files/downloads/rule_1861b5712484475383e893c83908a034_mark.via.apk Binary XML file line #65
2025-08-14 18:41:28.832 19136-19590 AppManager              com.dspread.mdm.service              I  ℹ️ 准备安装: mark.via
2025-08-14 18:41:28.833 19136-19206 Common                  com.dspread.mdm.service              D  🔧 ServiceGuardEventHandler WakeLock已释放
2025-08-14 18:41:28.851 19136-19590 PackageParser           com.dspread.mdm.service              W  Unknown element under <manifest>: queries at /data/user/0/com.dspread.mdm.service/files/downloads/rule_1861b5712484475383e893c83908a034_mark.via.apk Binary XML file line #65
2025-08-14 18:41:28.872 19136-19207 Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"request_time":"1755168087836","org_request_time":"1755168088686","org_request_id":"1755168088686C0107","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755168087836S0000","serialNo":"01354090202503050399"}
2025-08-14 18:41:28.876 19136-19590 AppManager              com.dspread.mdm.service              I  ℹ️ APK验证通过: mark.via v6.6.0(20250713) 2504KB
2025-08-14 18:41:28.879 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 收到响应消息: transCode=S0000, orgRequestId=1755168088686C0107, state=0, remark=
2025-08-14 18:41:28.883 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 处理规则结果响应: state=0
2025-08-14 18:41:28.884 19136-19590 AppManager              com.dspread.mdm.service              I  ℹ️ 创建Session: sessionId=1292636326
2025-08-14 18:41:28.887 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 规则结果上传成功
2025-08-14 18:41:29.006 19136-19590 AppManager              com.dspread.mdm.service              I  ℹ️ PackageInstaller installation committed: sessionId=1292636326
2025-08-14 18:41:29.022  1235-1305  PackageInstallerSession system_server                        E  com.dspread.mdm.service drops manifest attribute android:installLocation in base.apk for mark.via
2025-08-14 18:41:29.026 19136-19136 AppManager              com.dspread.mdm.service              I  ℹ️ 安装进度: 90%
2025-08-14 18:41:29.421 19136-19207 Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"request_time":"1755168087918","org_request_time":"1755168088749","org_request_id":"1755168088749C0107","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755168087918S0000","serialNo":"01354090202503050399"}
2025-08-14 18:41:29.428 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 收到响应消息: transCode=S0000, orgRequestId=1755168088749C0107, state=0, remark=
2025-08-14 18:41:29.433 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 处理规则结果响应: state=0
2025-08-14 18:41:29.437 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 规则结果上传成功
2025-08-14 18:41:30.292 19136-19136 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 收到广播: android.intent.action.PACKAGE_ADDED
2025-08-14 18:41:30.301 19136-19136 Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 收到包更新广播: android.intent.action.PACKAGE_ADDED, 包名: mark.via
2025-08-14 18:41:30.311 19136-19136 Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 应用安装: mark.via
2025-08-14 18:41:30.328 19136-19136 Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 检查WebSocket状态: packageName=mark.via, action=INSTALL
2025-08-14 18:41:30.337 19136-19136 Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 通知rulebase处理应用安装: mark.via
2025-08-14 18:41:30.344 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 收到系统广播，应用已安装: mark.via
2025-08-14 18:41:30.349 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 找到匹配的安装回调，触发: mark.via
2025-08-14 18:41:30.364 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 安装回调被触发: pkg=mark.via, returnCode=1, error=
2025-08-14 18:41:30.370 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 应用安装启动成功: mark.via
2025-08-14 18:41:30.379 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 从广播更新应用状态: 1861b5712484475383e893c83908a034, mark.via -> B03
2025-08-14 18:41:30.385 19136-19136 WebSocket               com.dspread.mdm.service              I  🔧 C0107 主动式上送: rulebase_update (主动: 77)
2025-08-14 18:41:30.412 19136-19136 WebSocket               com.dspread.mdm.service              I  🔧 添加规则结果: ruleId=1861b5712484475383e893c83908a034
2025-08-14 18:41:30.441 19136-19136 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0107","serialNo":"01354090202503050399","request_time":"1755168090400","request_id":"1755168090400C0107","version":"1","data":{"ruleId":"1861b5712484475383e893c83908a034","taskResult":"R02","failedApkList":[{"packName":"mark.via","apkName":"Via","versionName":"6.6.0","versionCode":"20250713","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/4d256c4123b346fd817537b491342204.apk","apkMd5":"0d1225260d03e10a8ffc8409369c442a","apkSize":2565110,"installBy":"0","isSystemApp":false,"priority":0,"result":"B03","taskType":"01"},{"packName":"com.lingjingnet.lazycat","apkName":"懒猫趣播","versionName":"1.0.1","versionCode":"10","apkUrl":"","apkMd5":"","apkSize":0,"installBy":"0","isSystemApp":false,"priority":0,"result":"D02","taskType":"02"},{"packName":"plus.H52FFB9A5","apkName":"逍遥助手","versionName":"1.0","versionCode":"100","apkUrl":"","apkMd5":"","apkSize":0,"installBy":"0","isSystemApp":false,"priority":0,"result":"D02","taskType":"02"}]},"myVersionName":"1.0.08.20250814.DSPREAD.MDM.SERVICE","terminalDate":"20250814184130"}
2025-08-14 18:41:30.446 19136-19136 WebSocket               com.dspread.mdm.service              I  🔧 规则结果实时上送: ruleId=1861b5712484475383e893c83908a034, result=R02 (1)
2025-08-14 18:41:30.451 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 上报规则执行中状态: 1861b5712484475383e893c83908a034, 应用数量: 3
2025-08-14 18:41:30.455 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 开始检查规则完成状态: 1861b5712484475383e893c83908a034, 总应用数: 3
2025-08-14 18:41:30.461 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 检查应用状态: mark.via -> B03
2025-08-14 18:41:30.467 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 发现成功状态: mark.via -> B03
2025-08-14 18:41:30.471 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 检查应用状态: com.lingjingnet.lazycat -> D02
2025-08-14 18:41:30.476 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 发现成功状态: com.lingjingnet.lazycat -> D02
2025-08-14 18:41:30.481 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 检查应用状态: plus.H52FFB9A5 -> D02
2025-08-14 18:41:30.486 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 发现成功状态: plus.H52FFB9A5 -> D02
2025-08-14 18:41:30.491 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 规则完成检查结果: 1861b5712484475383e893c83908a034, 全部完成: true, 完成数: 3/3, 有失败: false
2025-08-14 18:41:30.496 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 规则执行全部成功，上送RULEBASED_SUCCESS: 1861b5712484475383e893c83908a034
2025-08-14 18:41:30.501 19136-19136 WebSocket               com.dspread.mdm.service              I  🔧 C0107 主动式上送: rulebase_update (主动: 78)
2025-08-14 18:41:30.532 19136-19136 WebSocket               com.dspread.mdm.service              I  🔧 添加规则结果: ruleId=1861b5712484475383e893c83908a034
2025-08-14 18:41:30.558 19136-19136 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0107","serialNo":"01354090202503050399","request_time":"1755168090522","request_id":"1755168090522C0107","version":"1","data":{"ruleId":"1861b5712484475383e893c83908a034","taskResult":"R03","failedApkList":[{"packName":"mark.via","apkName":"Via","versionName":"6.6.0","versionCode":"20250713","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/4d256c4123b346fd817537b491342204.apk","apkMd5":"0d1225260d03e10a8ffc8409369c442a","apkSize":2565110,"installBy":"0","isSystemApp":false,"priority":0,"result":"B03","taskType":"01"},{"packName":"com.lingjingnet.lazycat","apkName":"懒猫趣播","versionName":"1.0.1","versionCode":"10","apkUrl":"","apkMd5":"","apkSize":0,"installBy":"0","isSystemApp":false,"priority":0,"result":"D02","taskType":"02"},{"packName":"plus.H52FFB9A5","apkName":"逍遥助手","versionName":"1.0","versionCode":"100","apkUrl":"","apkMd5":"","apkSize":0,"installBy":"0","isSystemApp":false,"priority":0,"result":"D02","taskType":"02"}]},"myVersionName":"1.0.08.20250814.DSPREAD.MDM.SERVICE","terminalDate":"20250814184130"}
2025-08-14 18:41:30.563 19136-19136 WebSocket               com.dspread.mdm.service              I  🔧 规则结果实时上送: ruleId=1861b5712484475383e893c83908a034, result=R03 (1)
2025-08-14 18:41:30.567 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 上报规则状态: 1861b5712484475383e893c83908a034 -> R03
2025-08-14 18:41:30.572 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleStateMonitor 收到状态变化: 1861b5712484475383e893c83908a034, R02 -> R03
2025-08-14 18:41:30.574 19136-19207 Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"request_time":"1755168089564","org_request_time":"1755168090400","org_request_id":"1755168090400C0107","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755168089564S0000","serialNo":"01354090202503050399"}
2025-08-14 18:41:30.577 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleStateMonitor 状态变化已记录: 1861b5712484475383e893c83908a034, R03
2025-08-14 18:41:30.583 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleStateMachine 状态转换成功: 1861b5712484475383e893c83908a034, R02 -> R03
2025-08-14 18:41:30.586 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 收到响应消息: transCode=S0000, orgRequestId=1755168090400C0107, state=0, remark=
2025-08-14 18:41:30.588 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 规则执行完成: 1861b5712484475383e893c83908a034, 有失败: false
2025-08-14 18:41:30.593 19136-19136 Platform                com.dspread.mdm.service              D  🔧 重新收集应用信息（强制刷新）
2025-08-14 18:41:30.599 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 处理规则结果响应: state=0
2025-08-14 18:41:30.608 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 规则结果上传成功
2025-08-14 18:41:30.694 19136-19136 AppManager              com.dspread.mdm.service              I  ℹ️ 应用信息获取完成: 总数132(系统129/用户3) 返回3个
2025-08-14 18:41:30.716 19136-19136 Platform                com.dspread.mdm.service              D  🔧 应用信息: 3 个应用
2025-08-14 18:41:30.721 19136-19136 Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 应用信息缓存刷新完成
2025-08-14 18:41:30.727 19136-19136 ContextImpl             com.dspread.mdm.service              W  Calling a method in the system process without a qualified user: android.app.ContextImpl.sendBroadcast:1220 android.content.ContextWrapper.sendBroadcast:504 com.dspread.mdm.service.broadcast.core.BroadcastSender.sendBroadcast:99 com.dspread.mdm.service.broadcast.core.BroadcastSender.sendBroadcast:23 com.dspread.mdm.service.broadcast.handlers.system.PackageUpdateEventHandlerImpl.sendAppRefreshBroadcast:491 
2025-08-14 18:41:30.729 19136-19136 Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 发送应用刷新广播
2025-08-14 18:41:30.752 19136-19207 Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"request_time":"1755168089680","org_request_time":"1755168090522","org_request_id":"1755168090522C0107","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755168089680S0000","serialNo":"01354090202503050399"}
2025-08-14 18:41:30.761 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 收到响应消息: transCode=S0000, orgRequestId=1755168090522C0107, state=0, remark=
2025-08-14 18:41:30.765 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 处理规则结果响应: state=0
2025-08-14 18:41:30.769 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 规则结果上传成功
