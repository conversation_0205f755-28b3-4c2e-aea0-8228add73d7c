package com.bbpos.wiseapp.service.sysupdate;

import android.content.Context;

import java.util.ArrayList;

/** 解析确认包后，需要提供各种信息，比如是否有apk，各个数据项大小等*/
public class ConfirmPacketUtil {
	/** 解析后的数据项数组*/
	private ArrayList<ConfirmPacketData> mConfirmPacketData;
	
	public ConfirmPacketUtil(String confirmPacket){
		mConfirmPacketData = ConfirmPacketData.parse(confirmPacket);
	}
	
	public ConfirmPacketUtil(Context mContext,String confirmPacket){
		mConfirmPacketData = ConfirmPacketData.parse(confirmPacket);
	}
	
	/** 获取数据项个数*/
	public int getDataItemNum(){
		return mConfirmPacketData.size();
	}
	
	/** 获取数据项数组*/
	public ArrayList<ConfirmPacketData> getDataArray(){
		return mConfirmPacketData;
	}
	
	/** 返回APK数据*/
	public ArrayList<ConfirmPacketData> getApkData(){
		/** 保存各个apk的信息*/
		ArrayList<ConfirmPacketData> apks = new ArrayList<ConfirmPacketData>();
		/** 判断是否有apk，有的话记录数据*/
		for(ConfirmPacketData data : mConfirmPacketData){
			String type = data.getType();
			if ((type != null) && (type.equals(FileType.TYPE_APK))){
				apks.add(data);
			}
		}
		
		return apks;
	}
	
	/** 返回PATCH类型的apk数据*/
	public ArrayList<ConfirmPacketData> getPatchData(){
		/** 后缀为apk的patch包*/
		//String apk_suffix = "apk";
		/** 保存各个patch的信息*/
		ArrayList<ConfirmPacketData> patches = new ArrayList<ConfirmPacketData>();
		/** 判断是否有apk，有的话记录数据*/
		for(ConfirmPacketData data : mConfirmPacketData){
			String type = data.getType();
			if ((type != null) && (type.equals(FileType.TYPE_PATCH))){
				/** 暂时patch只针对apk，不做后缀名判断了*/
				patches.add(data);
			}
		}
		
		return patches;
	}
	
	/** 返回其他文件类型(除apk或apk的patch文件外)的确认包数据*/
	public ArrayList<ConfirmPacketData> getOsFileData(){
		/** 保存各个file的信息*/
		ArrayList<ConfirmPacketData> osfiles = new ArrayList<ConfirmPacketData>();
		/** 类型为OS*/
		for(ConfirmPacketData data : mConfirmPacketData){
			String type = data.getType();
			if ((type != null) && (type.equals(FileType.TYPE_OS))){
				osfiles.add(data);
			}
		}
		
		return osfiles;
	}
	
	/** 解析文件名，获取文件的后缀名*/
	@SuppressWarnings("unused")
	private String getFileSuffix(String filename){
		String suffix = filename.substring(filename.lastIndexOf("."), filename.length());
		return suffix;
	}
	
	/** 获取各个文件的长度*/
	public long[] getFileLens(){
		int length = getDataItemNum();
		long[] lens = new long[length];
		for(int i = 0; i < length; i++){
			lens[i] = Long.valueOf(mConfirmPacketData.get(i).getFileLen());
		}
		return lens;
	}
	
	/** 获取文件名 */
	public String[] getFileName(){
		/** 文件个数*/
		int length = getDataItemNum();
		
		/** 获取各个文件路径*/
		String[] filePathArray = new String[length];
    	for(int i = 0; i < length; i++){
    		filePathArray[i] = mConfirmPacketData.get(i).getFileName();
    	}
    	return filePathArray;
	}
	
	/** 获取文件全路径 */
	public String[] getFilePath(String path){
		/** 文件个数*/
		int length = getDataItemNum();
		
		/** 获取各个文件路径*/
		String[] filePathArray = new String[length];
    	for(int i = 0; i < length; i++){
    		String name = mConfirmPacketData.get(i).getFileName();
    		String filePath = path + "/" + name;
    		filePathArray[i] = filePath;
    	}
    	return filePathArray;
	}
    
    /** 获取确认包中各个文件的MD5 */
	public String[] getAllMD5(){
		/** 文件个数*/
		int length = getDataItemNum();
		
		/** 获取各个文件MD5*/
		String[] MD5Array = new String[length];
    	for(int i = 0; i < length; i++){
    		MD5Array[i] = mConfirmPacketData.get(i).getMD5();
    	}
		
		return MD5Array;
	}
	
	/** 判断是否有apk升级*/
	public boolean hasApkUpdate(){
		boolean ret = false;
		
		for(ConfirmPacketData data : mConfirmPacketData){
			String type = data.getType();
			if ((type != null) && 
				((type.equals(FileType.TYPE_APK)) || (type.equals(FileType.TYPE_PATCH)))){
				/** 存在类型为apk或patch的升级信息，返回true*/
				ret = true;
				break;
			}
		}
		
		return ret;
	}
	
	/** 判断是否有os升级*/
	public boolean hasOsUpdate(){
		boolean ret = false;
		
		for(ConfirmPacketData data : mConfirmPacketData){
			String type = data.getType();
			if ((type != null) && (type.equals(FileType.TYPE_OS))){
				/** 存在类型为OS的升级信息，返回true*/
				ret = true;
				break;
			}
		}
		
		return ret;
	}

	public void removePkg(String apkPath) {
		ConfirmPacketData toRemovePacketData = null;
		for (ConfirmPacketData packetData:mConfirmPacketData) {
			if((packetData.getType().equals(FileType.TYPE_APK)
					|| packetData.getType().equals(FileType.TYPE_PATCH))
					&& packetData.getFileName().equals(apkPath)){
				toRemovePacketData = packetData;
				break;
			}
		}
		if(mConfirmPacketData != null)
			mConfirmPacketData.remove(toRemovePacketData);
	}

	/** 更新包中的文件类型 */
	public class FileType {
		/** apk */
		public static final String TYPE_APK = "APK";
		/** patch */
		public static final String TYPE_PATCH = "PATCH";
		/** OS */
		public static final String TYPE_OS = "OS";
		/** CRT */
		public static final String TYPE_CRT = "CRT";
	}
}
