package com.bbpos.wiseapp.service;

import android.app.Service;
import android.content.Intent;
import android.os.IBinder;
import android.os.RemoteException;
import android.text.TextUtils;
import android.util.Log;

import com.bbpos.wiseapp.logger.BBLog;
import com.bbpos.wiseapp.service.common.UsualData;
import com.bbpos.wiseapp.utils.FileUtils;
import com.bbpos.wiseapp.utils.SharedPreferencesUtils;

import org.json.JSONException;
import org.json.JSONObject;

import com.bbpos.wiseapp.sdk.main.ICloudService;
import com.bbpos.wiseapp.sdk.main.ManagerType;
import com.bbpos.wiseapp.sdk.utils.SdkInfo;

public class CloudService extends Service{
	public static final String V2_TAG = "CloudService";
	private ServiceBinder serviceBinder = new ServiceBinder(); 
	
	@Override
	public IBinder onBind(Intent arg0) {
		return serviceBinder;
	}

	public class ServiceBinder extends ICloudService.Stub{
		@Override
		public IBinder getManager(int managerType) throws RemoteException {
			IBinder binder = null;
			switch (managerType) {
			case ManagerType.APP_MANAGER:
				binder = new AppManagerService(CloudService.this);
				break;
			case ManagerType.DEVICE_MANAGER:
				binder = new DeviceManagerSerice(CloudService.this);
				break;
			case ManagerType.SYSTEM_MANAGER:
				binder = new SystemManagerService(CloudService.this);
				break;
			default:
				BBLog.e(V2_TAG, "error manger type.");
				break;
			}
			return binder;
		}

		@Override
		public String getServiceSdkVersion() throws RemoteException {
			return SdkInfo.getVersion();
		}
	}

	private static String getValueFromJsonObject(JSONObject jsonObject, String key) {
		try {
			return (jsonObject != null && jsonObject.has(key)) ? jsonObject.getString(key) : null;
		} catch (Exception e) {
			e.printStackTrace();
		}

		return null;
	}

	public static void setConfigInfoKeyValue() {
		String configInfo = FileUtils.readFile(FileUtils.getWiseAppConfigPath());
		if (!TextUtils.isEmpty(configInfo)) {
			try {
				JSONObject jsonObject = new JSONObject(configInfo);
				if (getValueFromJsonObject(jsonObject, "data") != null) {
					JSONObject dataJsonObject = new JSONObject(jsonObject.getString("data"));
					if (getValueFromJsonObject(dataJsonObject, "customization") != null) {
						JSONObject customJsonObject = new JSONObject(dataJsonObject.getString("customization"));
						if (getValueFromJsonObject(customJsonObject, "system") != null) {
							JSONObject systemJsonObject = new JSONObject(customJsonObject.getString("system"));
							BBLog.i(BBLog.TAG, "ConfigInfo.data.customization.system: " + systemJsonObject.toString());
							SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_TIMEZONE,
									getValueFromJsonObject(systemJsonObject, UsualData.SHARED_PREFERENCES_TIMEZONE));
						}
					}
					SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_REQUEST_TIME,
							getValueFromJsonObject(dataJsonObject, UsualData.SHARED_PREFERENCES_REQUEST_TIME));
				}
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
	}
}
