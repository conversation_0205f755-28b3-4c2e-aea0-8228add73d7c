package com.bbpos.wiseapp.system.api;

import android.app.ActivityManager;
import android.app.AlarmManager;
import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.Service;
import android.content.Context;
import android.content.Intent;
import android.content.pm.ApplicationInfo;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.net.Uri;
import android.os.RecoverySystem;
import android.provider.Settings;
import androidx.core.app.NotificationCompat;
import android.text.TextUtils;
import android.util.Log;

import com.bbpos.bootupcustom.LogoBlockDevice;
import com.bbpos.wiseapp.logger.BBLog;
import com.bbpos.wiseapp.security.SecurityOperate;
import com.bbpos.wiseapp.service.common.RequestPermission;
import com.bbpos.wiseapp.service.common.UsualData;
import com.bbpos.wiseapp.tms.model.AppInfo;
import com.bbpos.wiseapp.tms.model.CrashInfo;
import com.bbpos.wiseapp.tms.utils.ContextUtil;
import com.bbpos.wiseapp.tms.utils.Helpers;
import com.bbpos.wiseapp.tms.utils.IOUtils;
import com.bbpos.wiseapp.tms.utils.TaskState;
import com.bbpos.wiseapp.utils.SharedPreferencesUtils;

import java.io.BufferedReader;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.Iterator;
import java.util.List;

public class SystemApi {
    private final static String TAG = "SystemApi";

    public static final String ACTION_REBOOT = "android.intent.action.REBOOT";
    public static final String ACTION_REQUEST_SHUTDOWN = "android.intent.action.ACTION_REQUEST_SHUTDOWN";
    public static final String EXTRA_KEY_CONFIRM = "android.intent.extra.KEY_CONFIRM";

    public static final String BOOTANIMATION_DEFAULT_PATH = "/sdcard/wiseapp/bootanimation.zip";
    public static final String BOOTANIMATION_SYSTEM_PATH = "/bbpos/animation/bootanimation.zip";
    public static final String LOGO_DEFAULT_PATH = "/sdcard/wiseapp/logo.bin";
    public static final String LOGO_SYSTEM_PATH = "";
    public static final String UPDATE_ZIP_DEFAULT_PATH = "/sdcard/Updates/update.zip";
    public static final String UPDATE_ZIP_SDCARD_PATH = "/sdcard/update.zip";
    public static final String UPDATE_ZIP_SYSTEM_PATH = "/data/ota_package/update.zip";
    public static final String UPDATE_ZIP_CACHE_PATH = "/cache/update.zip";

    // crash definition
    public static final String CRASH_LOGCAT_COMMAND = "logcat -db crash";

    public static final String CRASH_DATE_LABEL = "crash_date";
    public static final String CRASH_INFO_LABEL = "crash_info";
    public static final String CRASH_PACKAGE_NAME_LABEL = "crash_package_name";

    public static final String CRASH_FATAL_EXCEPTION_KEY = "FATAL EXCEPTION";
    public static final String CRASH_PROCESS_KEY  = "Process";
    public static final String CRASH_ANDROID_RUNTIME_KEY = "AndroidRuntime";
    public static final String CRASH_FILTER_SYMBOL = ",";

    public static final int CRASH_FATAL_EXCEPTION_LINE = 0;
    public static final int CRASH_PROCESS_LINE = CRASH_FATAL_EXCEPTION_LINE + 1;
    public static final int CRASH_INFO_LINE = CRASH_PROCESS_LINE + 1;

    public static final int CRASH_COUNT_DEFAULT = 1;
    public static final int CRASH_DATE_COLUMN = 19; //时间固定列长

    public static void setTimeZone(Context context, String timezone) {
        try {
            AlarmManager am = (AlarmManager) context.getSystemService(Context.ALARM_SERVICE);
            am.setTimeZone(timezone);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    
    public static void setTime(Context context, String time) {
        long timeNow = new Long(time).longValue();
        if (timeNow / 1000 < Integer.MAX_VALUE) {
            AlarmManager am = (AlarmManager) context.getSystemService(Context.ALARM_SERVICE);
            am.setTime(timeNow);
        }
    }
    
    public static void shutdown(Context context) {
        BBLog.v(TAG, "broadcast->shutdown");
        Intent intent = new Intent(ACTION_REQUEST_SHUTDOWN);
        //其中false换成true,会弹出是否关机的确认窗口
        intent.putExtra(EXTRA_KEY_CONFIRM, false);
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        context.startActivity(intent);
    }
    
    public static void reboot(Context context) { 
        BBLog.v(TAG, "broadcast->reboot");
        Intent intent = new Intent(ACTION_REBOOT);
        intent.putExtra("nowait", 1);
        intent.putExtra("interval", 1);
        intent.putExtra("window", 0);
        context.sendBroadcast(intent, RequestPermission.REQUEST_PERMISSION_SHUTDOWN);
    }

    public static void factoryReset(Context context) {
        Intent intent = new Intent("android.intent.action.FACTORY_RESET");
        intent.setPackage("android");
        intent.addFlags(Intent.FLAG_RECEIVER_FOREGROUND);
        intent.putExtra("android.intent.extra.REASON", "MasterClearConfirm");
        intent.putExtra("android.intent.extra.WIPE_EXTERNAL_STORAGE", true);
        context.sendBroadcast(intent, RequestPermission.REQUEST_PERMISSION_SHUTDOWN);
    }

    public static void forceStopProgress(Context context, String pkgName) {
        BBLog.i(TAG, "forceStopProgress start...");
        ActivityManager am = (ActivityManager) context.getSystemService(Context.ACTIVITY_SERVICE);

        try {
            Method forceStopPackage = am.getClass().getDeclaredMethod("forceStopPackage", String.class);
            forceStopPackage.setAccessible(true);
            forceStopPackage.invoke(am, pkgName);
            BBLog.i(TAG, "forceStopProgress end...");
        } catch (NoSuchMethodException e) {
            e.printStackTrace();
        } catch (IllegalAccessException e) {
            e.printStackTrace();
        } catch (IllegalArgumentException e) {
            e.printStackTrace();
        } catch (InvocationTargetException e) {
            e.printStackTrace();
        }  catch (Exception e) {
            e.printStackTrace();
        } 
    }
    
    public static void replaceLogo(String path) {
        if (TextUtils.isEmpty(path)) {
            path = LOGO_DEFAULT_PATH;
        }
        BBLog.v(TAG, "replaceLogo path: " + path);
        if (DeviceInfoApi.getIntance().isWisePosTouch() || DeviceInfoApi.getIntance().isWisePosTouchPlus() || DeviceInfoApi.getIntance().isWisePosGo()
                || DeviceInfoApi.getIntance().isWisePosLE() || DeviceInfoApi.getIntance().isWisePosLP()) {
            CustomServiceManager.getInstance().replaceBootLogo(path);
        } else {
            FileInputStream input = null;
            OutputStream output = null;
            try {
                input = new FileInputStream(path);
                LogoBlockDevice device = new LogoBlockDevice(); // Use JNI for test android block device SE-Linux permission
                output = device.getOutputStream();

                byte[] buffer = new byte[1024];
                int count = 0;
                while ((count = input.read(buffer)) > 0) {
                    output.write(buffer, 0, count);
                }
            } catch (Exception e) {
                e.printStackTrace();
            } finally {
                IOUtils.closeInputStream(input);
                IOUtils.flushCloseOutputStream(output);
            }
        }
    }
    
    public static void replaceBootAnimation(String path) {
        if (TextUtils.isEmpty(path)) {
            path = BOOTANIMATION_DEFAULT_PATH;
        }
        BBLog.v(TAG, "replaceBootAnimation path: " + path);
        if (DeviceInfoApi.getIntance().isWisePosTouch() || DeviceInfoApi.getIntance().isWisePosTouchPlus()
                || DeviceInfoApi.getIntance().isWisePosLE() || DeviceInfoApi.getIntance().isWisePosLP()) {
            CustomServiceManager.getInstance().replaceBootAnimation(path);
        } else {
            FileInputStream input = null;
            FileOutputStream output = null;
            try {
                File file = new File(BOOTANIMATION_SYSTEM_PATH);
                if (file.exists() && file.isFile())
                    file.delete();

                input = new FileInputStream(path);
                output = new FileOutputStream(BOOTANIMATION_SYSTEM_PATH);

                byte[] buffer = new byte[1024];
                int count = 0;
                while ((count = input.read(buffer)) > 0) {
                    output.write(buffer, 0, count);
                }
                output.flush();
                SecurityOperate.getInstance().changeFilemode(ContextUtil.getInstance(), BOOTANIMATION_SYSTEM_PATH);
            } catch (Exception e) {
                e.printStackTrace();
            } finally {
                try {
                    output.close();
                    output = null;
                } catch (Exception e) {
                    e.printStackTrace();
                }
                try {
                    input.close();
                    input = null;
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
    }

    /**
     * 获得锁屏时间  毫秒
     */
    public static int getScreenOffTime(Context context){
        int screenOffTime=0;
        try{
            screenOffTime = Settings.System.getInt(context.getContentResolver(), Settings.System.SCREEN_OFF_TIMEOUT);
        } catch (Exception localException){

        }

        return screenOffTime;
    }

    /**
     * 设置背光时间  毫秒
     */
    public static void setScreenOffTime(Context context, int paramInt){
        try{
            Settings.System.putInt(context.getContentResolver(), Settings.System.SCREEN_OFF_TIMEOUT, paramInt * 1000);
        }catch (Exception localException){
            localException.printStackTrace();
        }
    }

    public static void updateSystem(Service service, String path) {
        if (TextUtils.isEmpty(path)) {
            path = UPDATE_ZIP_DEFAULT_PATH;
        }

        if (DeviceInfoApi.getIntance().isWisePosPro() || DeviceInfoApi.getIntance().isWisePos4G()) {
            String actual_update_path = UPDATE_ZIP_SYSTEM_PATH;
            FileInputStream input = null;
            FileOutputStream output = null;
            try {
                File file = new File(actual_update_path);
                if (file.exists() && file.isFile())
                    file.delete();

                input = new FileInputStream(path);
                output = new FileOutputStream(actual_update_path);

                byte[] buffer = new byte[1024];
                int count = 0;
                while ((count = input.read(buffer)) > 0) {
                    output.write(buffer, 0, count);
                }

                SecurityOperate.getInstance().changeFilemode(service.getApplicationContext(), actual_update_path);
                File temp = new File(path);
                if (temp.exists() && temp.isFile()) {
                    temp.delete();
                }
                RecoverySystem.installPackage(service.getApplicationContext(), new File(actual_update_path));
            } catch (Exception e) {
                e.printStackTrace();
            } finally {
                IOUtils.closeInputStream(input);
                IOUtils.flushCloseOutputStream(output);
            }
        } else if (DeviceInfoApi.getIntance().isWisePos5Plus()|| DeviceInfoApi.getIntance().isWisePos5()){
            BBLog.e(TAG, "startOSUpdate updateFile = " + path);
            if (CustomServiceManager.getInstance().hasInit()) {
                CustomServiceManager.getInstance().startOSUpdate(path, false);
            } else {
                Intent it = new Intent("com.qualcomm.update.REBOOT");
                it.setData(Uri.fromFile(new File(path)));
                it.putExtra("update_confirm", false); //升级是否弹框确认升级
                it.putExtra("update_verify", true);//是否验证升级文件正确性
                it.putExtra("update_mode", 2);
                it.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                ContextUtil.getInstance().startActivity(it);
            }
        } else if (DeviceInfoApi.getIntance().isWisePosTouch()|| DeviceInfoApi.getIntance().isWisePosTouchPlus()
                || DeviceInfoApi.getIntance().isWisePosGo()
                || DeviceInfoApi.getIntance().isWisePosLE()
                || DeviceInfoApi.getIntance().isWisePosLP()){
            CustomServiceManager.getInstance().startSystemUpdate(path);
        }
    }

    @SuppressWarnings("rawtypes")
    public static String getPackName(Context context, int pid) {
        String processName = null;
        ActivityManager am = (ActivityManager) context.getSystemService(Context.ACTIVITY_SERVICE);
        List l = am.getRunningAppProcesses();
        Iterator i = l.iterator();
        while (i.hasNext()) {
            ActivityManager.RunningAppProcessInfo info = (ActivityManager.RunningAppProcessInfo) (i.next());
            try {
                if (info.pid == pid) {
                    processName = info.processName;
                }
            } catch (Exception e) {

            }
        }

        return processName;
    }

    public static List<AppInfo> getCrashLog(Context context) {
        List<AppInfo> appInfoList = new ArrayList<>();
        List<CrashInfo> crashInfoList = new ArrayList<>();
        PackageManager packageManager = context.getPackageManager();
        if (ContextUtil.isApkInDebug()) {
            BBLog.i(TAG, "start get crash log...");
        }
        try {
            // -d：收集一次日志停止 -b：过滤 crash：缓存区
//            Process exec = Runtime.getRuntime().exec(CRASH_LOGCAT_COMMAND);
            Process exec =  SecurityOperate.getInstance().execCommand(ContextUtil.getInstance(),CRASH_LOGCAT_COMMAND);
            InputStream inputStream = exec.getInputStream();
            InputStreamReader buInputStreamReader = new InputStreamReader(inputStream);
            BufferedReader bufferedReader = new BufferedReader(buInputStreamReader);
            String str = null;
            int count = 0;
            boolean exception_flag = false;
            CrashInfo crashInfo = new CrashInfo();
            while((str = bufferedReader.readLine())!=null){
                if (str.contains(CRASH_FATAL_EXCEPTION_KEY)) {
                    exception_flag = true;
                    count = CRASH_FATAL_EXCEPTION_LINE;
                    crashInfo = new CrashInfo();
                    Date date = Helpers.getTransDate(getCrashInfo(str, CRASH_DATE_LABEL));
                    if (date != null) {
                        crashInfo.setDate(String.valueOf(date.getTime()));
                    }
                }
                if (exception_flag) {
                    if (count == CRASH_PROCESS_LINE) {
                        crashInfo.setPackage_name(getCrashInfo(str, CRASH_PACKAGE_NAME_LABEL));
                    } else if (count == CRASH_INFO_LINE) {
                        crashInfo.setInfo(getCrashInfo(str, CRASH_INFO_LABEL));
                        long lastReadTimestamp = Long.parseLong(SharedPreferencesUtils.getSharePreferencesValue(UsualData.LAST_READ_LOG_TIME, String.valueOf(System.currentTimeMillis())));
                        long logTimestamp = 0;
                        String dateValid = crashInfo.getDate();
                        if (!TextUtils.isEmpty(dateValid)) {
                            logTimestamp = Long.parseLong(dateValid);
                        } else {
                            logTimestamp = lastReadTimestamp + 1;
                        }
                        if (ContextUtil.isApkInDebug()) {
                            BBLog.i(BBLog.TAG, "lastReadTimestamp: " + lastReadTimestamp + ", logTimestamp: " + logTimestamp);
                        }
                        if (logTimestamp > lastReadTimestamp) {
                            crashInfoList.add(crashInfo);
                        }
                        exception_flag = false;
                    }
                }
                count ++;
            }
            for (int i = 0; i < crashInfoList.size(); i ++) {
                if (ContextUtil.isApkInDebug()) {
                    BBLog.i(BBLog.TAG, "crashInfoList[" + i + "]: " + crashInfoList.get(i).toString());
                }
                String package_name = crashInfoList.get(i).getPackage_name();
                if (!TextUtils.isEmpty(package_name)) {
                    if (appInfoList.size() == 0) {
                        AppInfo appInfo = new AppInfo();
                        try {
                            PackageInfo packageInfo = packageManager.getPackageInfo(package_name, 0);
                            ApplicationInfo applicationInfo = packageManager.getApplicationInfo(package_name, 0);
                            appInfo.setApk_name(packageManager.getApplicationLabel(applicationInfo).toString());
                            appInfo.setPackage_name(packageInfo.packageName);
                            appInfo.setVersion_name(packageInfo.versionName);
                            appInfo.setVersion_code(String.format("%d", packageInfo.versionCode));
                        } catch (PackageManager.NameNotFoundException e) {
                            e.printStackTrace();
                        }
                        appInfo.setCrash_Count(String.format("%d", CRASH_COUNT_DEFAULT));
                        appInfo.setCrash_info(crashInfoList.get(i));
                        appInfoList.add(appInfo);
                    } else {
                        boolean crash_app_same_flag = false;
                        int j;
                        for (j = 0; j < appInfoList.size(); j ++) {
                            if (appInfoList.get(j).getCrash_info().getPackage_name().equals(package_name)) {
                                crash_app_same_flag = true;
                                break;
                            }
                        }
                        if (crash_app_same_flag) {
                            int crash_count = Integer.parseInt(appInfoList.get(j).getCrash_Count());
                            crash_count ++;
                            appInfoList.get(j).setCrash_Count(String.format("%d", crash_count));
                            appInfoList.get(j).getCrash_info().setDate(crashInfoList.get(i).getDate());
                        } else {
                            AppInfo appInfo = new AppInfo();
                            try {
                                PackageInfo packageInfo = packageManager.getPackageInfo(package_name, 0);
                                ApplicationInfo applicationInfo = packageManager.getApplicationInfo(package_name, 0);
                                appInfo.setApk_name(packageManager.getApplicationLabel(applicationInfo).toString());
                                appInfo.setPackage_name(packageInfo.packageName);
                                appInfo.setVersion_name(packageInfo.versionName);
                                appInfo.setVersion_code(String.format("%d", packageInfo.versionCode));
                            } catch (PackageManager.NameNotFoundException e) {
                                e.printStackTrace();
                            }
                            appInfo.setCrash_info(crashInfoList.get(i));
                            appInfo.setCrash_Count(String.format("%d", CRASH_COUNT_DEFAULT));
                            appInfoList.add(appInfo);
                        }
                    }
                }
            }
        } catch (Exception e) {
            SharedPreferencesUtils.setSharePreferencesValue(UsualData.LAST_READ_LOG_TIME, String.valueOf(System.currentTimeMillis()));
            e.printStackTrace();
        }
        SharedPreferencesUtils.setSharePreferencesValue(UsualData.LAST_READ_LOG_TIME, String.valueOf(System.currentTimeMillis()));

        if (ContextUtil.isApkInDebug()) {
            for (int k = 0; k < appInfoList.size(); k++) {
                BBLog.i(BBLog.TAG, "appInfoList[" + k + "]: " + appInfoList.get(k).toString());
            }
        }

        return appInfoList;
    }

    private static String getCrashInfo(String crash_log, String content_type) {
        String content = null;
        int crash_log_len = crash_log.length();
        if (crash_log_len < (CRASH_DATE_COLUMN + 1)) {
            return null;
        }

        if (CRASH_DATE_LABEL.equals(content_type)) {
            content = Calendar.getInstance().get(Calendar.YEAR) + "-" + crash_log.substring(0, CRASH_DATE_COLUMN);
        } else if (CRASH_PACKAGE_NAME_LABEL.equals(content_type)) {
            int frontIndex = crash_log.indexOf(CRASH_PROCESS_KEY);
            int behindIndex = crash_log.indexOf(CRASH_FILTER_SYMBOL);
            if (behindIndex > frontIndex) {
                content = crash_log.substring(frontIndex + CRASH_PROCESS_KEY.length() + 2, behindIndex);
            }
        } else if (CRASH_INFO_LABEL.equals(content_type)) {
            int frontIndex = crash_log.indexOf(CRASH_ANDROID_RUNTIME_KEY);
            content = crash_log.substring(frontIndex + CRASH_ANDROID_RUNTIME_KEY.length() + 2, crash_log_len);
        }

        return content;
    }

    public static NotificationManager createCustomnNotificationManager(Context context, String channelID, String channelName){
        NotificationManager manager = (NotificationManager) context.getSystemService(Context.NOTIFICATION_SERVICE);
        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.O) {
            NotificationChannel channel = new NotificationChannel(channelID, channelName, NotificationManager.IMPORTANCE_DEFAULT);
            channel.enableLights(false);
            channel.enableVibration(false);
            channel.setVibrationPattern(new long[]{0});
            channel.setSound(null, null);
            manager.createNotificationChannel(channel);
        }
        return manager;
    }

    public static Notification createCustomnNotification(Context context, String channelID, int icon, String contentTitle, String contentText){
        final NotificationCompat.Builder builder = new NotificationCompat.Builder(context, channelID);
        builder.setSmallIcon(icon);
        builder.setContentTitle(contentTitle);
        builder.setContentText(contentText);
        builder.setAutoCancel(true);
        builder.setDefaults(NotificationCompat.FLAG_ONLY_ALERT_ONCE);
        builder.setVibrate(new long[]{0});
        builder.setSound(null);
        //创建通知时指定channelID
        builder.setChannelId(channelID);
        return builder.build();
    }

    public static void screenShotByShell(String filePath){
        String shotCmd = "screencap -p " + filePath + " \n";
        try {
//            Process exec = Runtime.getRuntime().exec(shotCmd);
            Process exec = SecurityOperate.getInstance().execCommand(ContextUtil.getInstance(),shotCmd);
            int retCode = exec.waitFor();
//            BBLog.e(BBLog.TAG, "screenShotByShell retCode: " + retCode);
            exec.destroy();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void execCommand(String command) {
		InputStream input = null;
		try {
            Process process = SecurityOperate.getInstance().execCommand(ContextUtil.getInstance(), command);
			//获得结果的输入流
			input = process.getInputStream();
			BufferedReader br = new BufferedReader(new InputStreamReader(input));
			String strLine;
			StringBuilder builder = new StringBuilder();
			while(null != (strLine = br.readLine())){
				builder.append(strLine).append("\r\n");
			}

			BBLog.w(TAG,"execCommand ---> "+ command +", result : \r\n"+ builder.toString()+"\r\n");
        } catch (Exception e) {
            e.printStackTrace();
        }
        finally {
            IOUtils.closeInputStream(input);
		}
    }

    /**
     * 設置允許指定應用安裝APK
     *  1、Settings.System.putString(getContentResolver(), "InstallerPackageName", "ALL");
     *  2、 Settings.System.putString(getContentResolver(), "InstallerPackageName", "com.bbpos.wiseapp.service");这是只允许WM安装
     *  3、 Settings.System.putString(getContentResolver(), "InstallerPackageName", "com.bbpos.wiseapp.service|com.cyanogenmod.filemanager|com.mswipe.mswip_appst_a_in");这是只允许WM，和其他两个File Manage安装
     */
    public static void setAllowInstallApkPackeName(Context context,String  packNames){
        BBLog.e(TAG, "setAllowInstallApkPackeName: " + packNames);
        Settings.System.putString(context.getContentResolver(), "InstallerPackageName", packNames);
    }

    /**
     * 压缩图片到指定位置(默认JPG格式)
     *
     * @param compressPath 生成文件路径(例如: /storage/imageCache/1.jpg)
     * @param quality      图片质量，0~100
     * @return if true,保存成功
     */
    public static boolean compressBitmap(String filePath, String compressPath, int quality) {
        FileOutputStream stream = null;
        try {
            stream = new FileOutputStream(compressPath);
            if (stream == null) {
                return false;
            }

            Bitmap bitmap = getHalfBitmap(filePath);
            for (int i = 0; i < 50; i ++) {
                if (bitmap != null) {
                    break;
                }
                Thread.sleep(20);
                bitmap = getHalfBitmap(filePath);
            }
            bitmap.compress(Bitmap.CompressFormat.JPEG, quality, stream);// (0-100)压缩文件
            return true;
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                if (stream != null) {
                    stream.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }

        return false;
    }

    public static byte[] compressPicture(String filePath, int quality) {
        Bitmap bitmap = getHalfBitmap(filePath);
        for (int i = 0; i < 50; i ++) {
            if (bitmap != null) {
                break;
            }
            try {
                Thread.sleep(20);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
            bitmap = getHalfBitmap(filePath);
        }

        ByteArrayOutputStream out = new ByteArrayOutputStream();
        bitmap.compress(Bitmap.CompressFormat.JPEG, quality, out);// (0-100)压缩文件
        return out.toByteArray();
    }

    public static Bitmap getHalfBitmap(String imgPath){
        BitmapFactory.Options options = new BitmapFactory.Options();
        options.inSampleSize = 2;//直接设置它的压缩率，表示1/2
        Bitmap b = null;
        try {
            b = BitmapFactory.decodeFile(imgPath, options);
        } catch (Exception e) {
            e.printStackTrace();
        }

        return b;
    }
}
