package com.dspread.mdm.service.modules.rulebase.core

import android.os.Handler
import android.os.Looper
import com.dspread.mdm.service.constants.TaskStateConstants
import com.dspread.mdm.service.utils.log.Logger
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.atomic.AtomicReference

/**
 * Rulebase状态机 - 重新设计版本
 * 采用状态模式，确保状态转换的原子性和一致性
 */
class RuleStateMachine {
    
    companion object {
        private const val TAG = "RuleStateMachine"
        private const val CLEANUP_DELAY = 10_000L // 10秒延迟清理
        
        @Volatile
        private var instance: RuleStateMachine? = null
        
        fun getInstance(): RuleStateMachine {
            return instance ?: synchronized(this) {
                instance ?: RuleStateMachine().also { instance = it }
            }
        }
    }
    
    // 规则状态映射表
    private val ruleStates = ConcurrentHashMap<String, AtomicReference<RuleState>>()
    
    // 状态变化监听器
    private val stateChangeListeners = mutableListOf<RuleStateChangeListener>()
    
    // 延迟清理处理器
    private val cleanupHandler = Handler(Looper.getMainLooper())
    
    /**
     * 规则状态 - 使用TaskStateConstants
     */
    data class RuleState(val code: String, val description: String) {
        
            companion object {
        // 使用TaskStateConstants中定义的状态
        val PENDING = RuleState(TaskStateConstants.TODO, "等待执行")
        val STARTING = RuleState(TaskStateConstants.RULEBASED_STARTING, "规则启动中")
        val EXECUTING = RuleState(TaskStateConstants.RULEBASED_EXECUTING, "执行中")
        val COMPLETED = RuleState(TaskStateConstants.RULEBASED_SUCCESS, "执行完成")
        val EXPIRED = RuleState(TaskStateConstants.RULEBASED_EXPIRED, "规则已过期")
        val FAILED = RuleState(TaskStateConstants.FAILED, "执行失败")
        val ERROR = RuleState(TaskStateConstants.SERVER_FAILED, "执行错误")
        val TIMEOUT = RuleState(TaskStateConstants.TASK_CANCEL, "执行超时")
        
        // 应用相关状态（在规则执行过程中使用）
        val APP_DOWNLOADING = RuleState(TaskStateConstants.DOWNLOAD_ING, "应用下载中")
        val APP_DOWNLOAD_SUCCESS = RuleState(TaskStateConstants.DOWNLOAD_SUCCESS, "应用下载成功")
        val APP_DOWNLOAD_FAILED = RuleState(TaskStateConstants.DOWNLOAD_FAILED, "应用下载失败")
        val APP_INSTALL_WAITING = RuleState(TaskStateConstants.INSTALL_WAITING, "应用安装等待")
        val APP_INSTALLING = RuleState(TaskStateConstants.INSTALL_ING, "应用安装中")
        val APP_INSTALLED = RuleState(TaskStateConstants.INSTALL_SUCCESS, "应用已安装")
        val APP_INSTALL_FAILED = RuleState(TaskStateConstants.INSTALL_FAILED, "应用安装失败")
        val APP_INSTALL_OVERRIDE = RuleState(TaskStateConstants.INSTALL_OVERRIDE, "应用安装覆盖")
        val APP_UNINSTALLING = RuleState(TaskStateConstants.UNINSTALL_ING, "应用卸载中")
        val APP_UNINSTALLED = RuleState(TaskStateConstants.UNINSTALL_SUCCESS, "应用已卸载")
        val APP_UNINSTALL_FAILED = RuleState(TaskStateConstants.UNINSTALL_FAILED, "应用卸载失败")
        val APP_UNINSTALL_EXPIRE = RuleState(TaskStateConstants.UNINSTALL_EXPIRE, "应用卸载过期")
    }
        
        fun isTerminal(): Boolean = TaskStateConstants.isCompletedState(code)
        fun isActive(): Boolean = TaskStateConstants.isInProgressState(code)
    }
    
    /**
     * 状态转换事件
     */
    data class StateTransitionEvent(
        val ruleId: String,
        val fromState: RuleState,
        val toState: RuleState,
        val timestamp: Long = System.currentTimeMillis()
    )
    
    /**
     * 状态变化监听器接口
     */
    interface RuleStateChangeListener {
        fun onStateChanged(event: StateTransitionEvent)
        fun onStateTransitionFailed(ruleId: String, fromState: RuleState, toState: RuleState, reason: String)
    }
    
    /**
     * 初始化规则状态
     */
    fun initializeRule(ruleId: String): Boolean {
        return try {
            val currentState = ruleStates[ruleId]
            if (currentState != null) {
                Logger.rule("$TAG 规则已存在: $ruleId, 当前状态: $currentState")
                return true
            }
            
            val initialState = AtomicReference(RuleState.PENDING)
            ruleStates[ruleId] = initialState
            
            Logger.rule("$TAG 规则初始化成功: $ruleId -> ${initialState.get()}")
            true
            
        } catch (e: Exception) {
            Logger.ruleE("$TAG 规则初始化失败: $ruleId", e)
            false
        }
    }
    
    /**
     * 更新规则状态
     */
    fun updateRuleState(ruleId: String, newState: RuleState): Boolean {
        return try {
            val currentStateRef = ruleStates[ruleId]
            if (currentStateRef == null) {
                Logger.ruleE("$TAG 规则不存在，无法转换状态: $ruleId")
                return false
            }
            
            val currentState = currentStateRef.get()
            
            // 验证状态转换是否合法
            val errorMsg = validateStateTransition(currentState, newState)
            if (errorMsg != null) {
                Logger.ruleE("$TAG $errorMsg, 规则: $ruleId")
                return false
            }
            
            // 使用CAS操作确保原子性
            if (!currentStateRef.compareAndSet(currentState, newState)) {
                Logger.ruleE("$TAG 状态转换竞争失败: $ruleId, ${currentState.code} -> ${newState.code}")
                return false
            }
            
            // 通知状态变化
            notifyStateChanged(StateTransitionEvent(ruleId, currentState, newState))
            
            Logger.rule("$TAG 状态转换成功: $ruleId, ${currentState.code} -> ${newState.code}")
            true
            
        } catch (e: Exception) {
            Logger.ruleE("$TAG 状态转换异常: $ruleId", e)
            false
        }
    }
    
    /**
     * 获取规则状态
     */
    fun getRuleState(ruleId: String): RuleState? {
        return ruleStates[ruleId]?.get()
    }
    
    /**
     * 检查规则是否存在
     */
    fun ruleExists(ruleId: String): Boolean {
        return ruleStates.containsKey(ruleId)
    }
    
    /**
     * 获取所有规则状态
     */
    fun getAllRuleStates(): Map<String, RuleState> {
        return ruleStates.mapValues { it.value.get() }
    }
    
    /**
     * 清理规则状态
     */
    fun cleanupRule(ruleId: String, force: Boolean = false): Boolean {
        return try {
            val currentStateRef = ruleStates[ruleId]
            if (currentStateRef == null) {
                Logger.rule("$TAG 规则不存在，无需清理: $ruleId")
                return true
            }
            
            val currentState = currentStateRef.get()
            if (!force && !currentState.isTerminal()) {
                Logger.ruleE("$TAG 规则未完成，不能强制清理: $ruleId, 状态: ${currentState.code}")
                return false
            }
            
            // 安排延迟清理
            if (!force && currentState.isTerminal()) {
                scheduleDelayedCleanup(ruleId)
                return true
            }
            
            // 立即清理
            ruleStates.remove(ruleId)
            Logger.rule("$TAG 规则清理完成: $ruleId")
            true
            
        } catch (e: Exception) {
            Logger.ruleE("$TAG 规则清理失败: $ruleId", e)
            false
        }
    }
    
    /**
     * 清理所有规则
     */
    fun clearAllRules() {
        try {
            Logger.rule("$TAG 开始清理所有规则...")
            ruleStates.clear()
            Logger.rule("$TAG 所有规则清理完成")
        } catch (e: Exception) {
            Logger.ruleE("$TAG 清理所有规则失败", e)
        }
    }
    
    /**
     * 添加状态变化监听器
     */
    fun addStateChangeListener(listener: RuleStateChangeListener) {
        stateChangeListeners.add(listener)
    }
    
    /**
     * 移除状态变化监听器
     */
    fun removeStateChangeListener(listener: RuleStateChangeListener) {
        stateChangeListeners.remove(listener)
    }
    
    // ==================== 私有方法 ====================
    
    /**
     * 验证状态转换是否合法
     */
    private fun validateStateTransition(fromState: RuleState, toState: RuleState): String? {
        // 使用TaskStateConstants的辅助方法进行验证
        return when {
            // 从待执行状态
            fromState.code == TaskStateConstants.TODO -> {
                when (toState.code) {
                    TaskStateConstants.RULEBASED_STARTING, TaskStateConstants.FAILED, 
                    TaskStateConstants.SERVER_FAILED, TaskStateConstants.RULEBASED_EXPIRED -> null
                    else -> "PENDING状态只能转换为STARTING、FAILED、ERROR或EXPIRED"
                }
            }
            // 从规则启动中状态
            fromState.code == TaskStateConstants.RULEBASED_STARTING -> {
                when (toState.code) {
                    TaskStateConstants.RULEBASED_EXECUTING, TaskStateConstants.FAILED, 
                    TaskStateConstants.SERVER_FAILED, TaskStateConstants.RULEBASED_EXPIRED -> null
                    else -> "STARTING状态只能转换为EXECUTING、FAILED、ERROR或EXPIRED"
                }
            }
            // 从执行中状态 - 可以转换为应用相关状态或完成状态
            fromState.code == TaskStateConstants.RULEBASED_EXECUTING -> {
                when (toState.code) {
                    // 应用相关状态
                    TaskStateConstants.DOWNLOAD_ING, TaskStateConstants.INSTALL_ING, TaskStateConstants.UNINSTALL_ING,
                    // 完成状态
                    TaskStateConstants.RULEBASED_SUCCESS, TaskStateConstants.RULEBASED_EXPIRED,
                    TaskStateConstants.FAILED, TaskStateConstants.SERVER_FAILED, TaskStateConstants.TASK_CANCEL -> null
                    else -> "EXECUTING状态转换不合法"
                }
            }
            // 从下载中状态
            fromState.code == TaskStateConstants.DOWNLOAD_ING -> {
                when (toState.code) {
                    TaskStateConstants.DOWNLOAD_SUCCESS, TaskStateConstants.DOWNLOAD_FAILED,
                    TaskStateConstants.RULEBASED_EXECUTING, TaskStateConstants.FAILED -> null
                    else -> "DOWNLOADING状态转换不合法"
                }
            }
            // 从下载成功状态
            fromState.code == TaskStateConstants.DOWNLOAD_SUCCESS -> {
                when (toState.code) {
                    TaskStateConstants.INSTALL_WAITING, TaskStateConstants.INSTALL_ING,
                    TaskStateConstants.RULEBASED_EXECUTING -> null
                    else -> "DOWNLOAD_SUCCESS状态转换不合法"
                }
            }
            // 从下载失败状态
            fromState.code == TaskStateConstants.DOWNLOAD_FAILED -> {
                when (toState.code) {
                    TaskStateConstants.RULEBASED_EXECUTING, TaskStateConstants.FAILED -> null
                    else -> "DOWNLOAD_FAILED状态转换不合法"
                }
            }
            // 从安装等待状态
            fromState.code == TaskStateConstants.INSTALL_WAITING -> {
                when (toState.code) {
                    TaskStateConstants.INSTALL_ING, TaskStateConstants.RULEBASED_EXECUTING -> null
                    else -> "INSTALL_WAITING状态转换不合法"
                }
            }
            // 从安装中状态
            fromState.code == TaskStateConstants.INSTALL_ING -> {
                when (toState.code) {
                    TaskStateConstants.INSTALL_SUCCESS, TaskStateConstants.INSTALL_FAILED,
                    TaskStateConstants.INSTALL_OVERRIDE, TaskStateConstants.RULEBASED_EXECUTING -> null
                    else -> "INSTALLING状态转换不合法"
                }
            }
            // 从安装成功状态
            fromState.code == TaskStateConstants.INSTALL_SUCCESS -> {
                when (toState.code) {
                    TaskStateConstants.RULEBASED_EXECUTING, TaskStateConstants.RULEBASED_SUCCESS -> null
                    else -> "INSTALL_SUCCESS状态转换不合法"
                }
            }
            // 从安装失败状态
            fromState.code == TaskStateConstants.INSTALL_FAILED -> {
                when (toState.code) {
                    TaskStateConstants.RULEBASED_EXECUTING, TaskStateConstants.FAILED -> null
                    else -> "INSTALL_FAILED状态转换不合法"
                }
            }
            // 从卸载中状态
            fromState.code == TaskStateConstants.UNINSTALL_ING -> {
                when (toState.code) {
                    TaskStateConstants.UNINSTALL_SUCCESS, TaskStateConstants.UNINSTALL_FAILED,
                    TaskStateConstants.UNINSTALL_EXPIRE, TaskStateConstants.RULEBASED_EXECUTING -> null
                    else -> "UNINSTALLING状态转换不合法"
                }
            }
            // 从卸载成功状态
            fromState.code == TaskStateConstants.UNINSTALL_SUCCESS -> {
                when (toState.code) {
                    TaskStateConstants.RULEBASED_EXECUTING, TaskStateConstants.RULEBASED_SUCCESS -> null
                    else -> "UNINSTALL_SUCCESS状态转换不合法"
                }
            }
            // 从卸载失败状态
            fromState.code == TaskStateConstants.UNINSTALL_FAILED -> {
                when (toState.code) {
                    TaskStateConstants.RULEBASED_EXECUTING, TaskStateConstants.FAILED -> null
                    else -> "UNINSTALL_FAILED状态转换不合法"
                }
            }
            // 终端状态不能再次转换
            TaskStateConstants.isCompletedState(fromState.code) -> {
                "终端状态不能再次转换"
            }
            else -> null // 其他状态转换允许
        }
    }
    
    /**
     * 安排延迟清理
     */
    private fun scheduleDelayedCleanup(ruleId: String) {
        Logger.rule("$TAG 安排延迟清理: $ruleId, ${CLEANUP_DELAY}ms后")
        
        cleanupHandler.postDelayed({
            performDelayedCleanup(ruleId)
        }, CLEANUP_DELAY)
    }
    
    /**
     * 执行延迟清理
     */
    private fun performDelayedCleanup(ruleId: String) {
        try {
            Logger.rule("$TAG 开始清理过程: $ruleId")
            
            val currentStateRef = ruleStates[ruleId]
            if (currentStateRef == null) {
                Logger.rule("$TAG 规则已被清理: $ruleId")
                return
            }
            
            val currentState = currentStateRef.get()
            if (!currentState.isTerminal()) {
                Logger.rule("$TAG 规则未完成，跳过清理: $ruleId, 状态: ${currentState.code}")
                return
            }
            
            Logger.rule("$TAG 执行清理逻辑: $ruleId")
            ruleStates.remove(ruleId)
            
        } catch (e: Exception) {
            Logger.ruleE("$TAG 延迟清理失败: $ruleId", e)
        }
    }
    
    /**
     * 通知状态变化
     */
    private fun notifyStateChanged(event: StateTransitionEvent) {
        try {
            stateChangeListeners.forEach { listener ->
                try {
                    listener.onStateChanged(event)
                } catch (e: Exception) {
                    Logger.ruleE("$TAG 状态变化通知失败", e)
                }
            }
        } catch (e: Exception) {
            Logger.ruleE("$TAG 状态转换失败通知异常", e)
        }
    }
} 