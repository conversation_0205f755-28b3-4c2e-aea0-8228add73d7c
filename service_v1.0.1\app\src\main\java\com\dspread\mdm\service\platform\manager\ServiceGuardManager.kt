package com.dspread.mdm.service.platform.manager

import android.app.AlarmManager
import android.app.PendingIntent
import android.content.BroadcastReceiver
import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.os.Build
import com.dspread.mdm.service.broadcast.core.BroadcastActions
import com.dspread.mdm.service.services.ServiceKeepAliveService
import com.dspread.mdm.service.utils.log.Logger
import com.dspread.mdm.service.config.TimerConfig
import kotlinx.coroutines.*

/**
 * 服务守护管理器
 * 负责定时保活机制，确保自更新时服务能正常重启
 *
 * 保活策略：
 * 1. 定期启动ServiceKeepAliveService保持进程活跃
 * 2. 使用智能WakeLock管理，避免长期持有
 * 3. 确保PackageUpdateReceiver能正常接收自更新广播
 */
class ServiceGuardManager : BroadcastReceiver() {

    companion object {
        private const val TAG = "ServiceGuardManager"

        // 防重复执行标志
        @Volatile
        private var isExecuting = false

        // 上次执行时间，防止过于频繁执行
        @Volatile
        private var lastExecuteTime = 0L

        // 最小执行间隔（30秒）
        private const val MIN_EXECUTE_INTERVAL = 30 * 1000L

        // 服务守护专用协程作用域
        private val guardScope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    }

    override fun onReceive(context: Context, intent: Intent) {
        val action = intent.action
        if (BroadcastActions.ACTION_SERVICE_GUARD_TIMER == action) {
            // 将耗时操作移到后台线程执行
            val pendingResult = goAsync()

            guardScope.launch {
                try {
                    executeServiceGuard(context)
                } finally {
                    pendingResult.finish()
                }
            }
        }
    }

    /**
     * 启动服务守护定时器
     */
    fun startServiceGuardTimer(context: Context) {
        try {
            Logger.com("$TAG 启动服务守护定时器")

            val alarmManager = context.getSystemService(Context.ALARM_SERVICE) as AlarmManager
            val intent = Intent(BroadcastActions.ACTION_SERVICE_GUARD_TIMER)
            intent.component = ComponentName(context, ServiceGuardManager::class.java)

            val pendingIntent = PendingIntent.getBroadcast(
                context,
                0,
                intent,
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                    PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
                } else {
                    PendingIntent.FLAG_UPDATE_CURRENT
                }
            )

            // 设置定时器：使用配置的服务守护间隔
            val intervalMillis = TimerConfig.getServiceGuardInterval() * 1000L
            val triggerAtMillis = System.currentTimeMillis() + intervalMillis

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                alarmManager.setExactAndAllowWhileIdle(
                    AlarmManager.RTC_WAKEUP,
                    triggerAtMillis,
                    pendingIntent
                )
            } else {
                alarmManager.setRepeating(
                    AlarmManager.RTC_WAKEUP,
                    triggerAtMillis,
                    intervalMillis,
                    pendingIntent
                )
            }

        } catch (e: Exception) {
            Logger.comE("$TAG 启动服务守护定时器失败", e)
        }
    }

    /**
     * 停止服务守护定时器
     */
    fun stopServiceGuardTimer(context: Context) {
        try {
            Logger.com("$TAG 停止服务守护定时器")

            val alarmManager = context.getSystemService(Context.ALARM_SERVICE) as AlarmManager
            val intent = Intent(BroadcastActions.ACTION_SERVICE_GUARD_TIMER)
            intent.component = ComponentName(context, ServiceGuardManager::class.java)

            val pendingIntent = PendingIntent.getBroadcast(
                context,
                0,
                intent,
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                    PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
                } else {
                    PendingIntent.FLAG_UPDATE_CURRENT
                }
            )

            alarmManager.cancel(pendingIntent)
            Logger.com("$TAG 服务守护定时器停止成功")

        } catch (e: Exception) {
            Logger.comE("$TAG 停止服务守护定时器失败", e)
        }
    }

    /**
     * 执行服务守护逻辑
     */
    private suspend fun executeServiceGuard(context: Context) {
        try {
            // 防重复执行检查
            val currentTime = System.currentTimeMillis()
            if (isExecuting || (currentTime - lastExecuteTime) < MIN_EXECUTE_INTERVAL) {
                Logger.com("$TAG 跳过执行：正在执行中或间隔时间不足")
                return
            }

            isExecuting = true
            lastExecuteTime = currentTime

            Logger.com("$TAG 执行服务守护逻辑")

            // 智能WakeLock管理：短时间持有，确保操作完成
            WakeLockManager.acquireWakeLock(context, "ServiceGuard", 30000) // 30秒

            try {
                // 启动保活服务
                startKeepAliveService(context)

                Logger.com("$TAG 服务守护执行完成")

            } finally {
                // 释放WakeLock
                WakeLockManager.releaseWakeLock("ServiceGuard")
            }

        } catch (e: Exception) {
            Logger.comE("$TAG 服务守护执行失败", e)
        } finally {
            isExecuting = false
        }
    }

    /**
     * 启动保活服务
     */
    private fun startKeepAliveService(context: Context) {
        try {
            val intent = Intent(context, ServiceKeepAliveService::class.java)
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                context.startForegroundService(intent)
            } else {
                context.startService(intent)
            }
            Logger.com("$TAG 保活服务启动成功")
        } catch (e: Exception) {
            Logger.comE("$TAG 启动保活服务失败", e)
        }
    }
}
