---------------------------- PROCESS STARTED (3681) for package com.dspread.mdm.service ----------------------------
2025-08-21 17:07:48.896  3681-3681  ead.mdm.service         com.dspread.mdm.service              W  type=1400 audit(0.0:414): avc: denied { write } for name="com.dspread.mdm.service-qoQ9VGWKHX9yNU2lpUfF2w==" dev="dm-6" ino=14792 scontext=u:r:system_app:s0 tcontext=u:object_r:apk_data_file:s0 tclass=dir permissive=0
2025-08-21 17:07:49.581  3681-3681  NetworkSecurityConfig   com.dspread.mdm.service              D  Using Network Security Config from resource network_security_config debugBuild: true
2025-08-21 17:07:49.585  3681-3681  NetworkSecurityConfig   com.dspread.mdm.service              D  Using Network Security Config from resource network_security_config debugBuild: true
2025-08-21 17:07:49.677  3681-3681  Common                  com.dspread.mdm.service              D  🔧 日志配置：测试环境模式
2025-08-21 17:07:49.683  3681-3681  Common                  com.dspread.mdm.service              D  🔧 [DebugConfig] 调试模式已启用
2025-08-21 17:07:49.685  3681-3681  Common                  com.dspread.mdm.service              D  🔧 SmartMdmServiceApp: 统一配置管理器初始化完成
2025-08-21 17:07:49.688  3681-3681  Common                  com.dspread.mdm.service              D  🔧 SmartMdmServiceApp: 系统目录初始化完成 - /data/pos/config/
2025-08-21 17:07:49.690  3681-3681  Common                  com.dspread.mdm.service              D  🔧 SmartMdmServiceApp: 开始初始化广播管理器...
2025-08-21 17:07:49.693  3681-3681  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 开始初始化...
2025-08-21 17:07:49.700  3681-3681  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 包更新广播接收器注册完成
2025-08-21 17:07:49.702  3681-3681  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 统一广播接收器注册完成
2025-08-21 17:07:49.704  3681-3681  Common                  com.dspread.mdm.service              I  ✅ BroadcastManager 初始化完成
2025-08-21 17:07:49.732  3681-3681  Common                  com.dspread.mdm.service              I  ✅ SmartMdmServiceApp: 广播管理器初始化完成
2025-08-21 17:07:49.774  3681-3681  Receiver                com.dspread.mdm.service              D  🔧 ServiceGuardEventHandler 启动服务守护定时器: 120s
2025-08-21 17:07:49.781  3681-3681  Common                  com.dspread.mdm.service              I  ✅ ⏰ 设置服务守护定时器成功，下次执行: 120秒后 (2分钟)
2025-08-21 17:07:49.783  3681-3681  Common                  com.dspread.mdm.service              D  🔧 SmartMdmServiceApp: 服务守护定时器启动成功
2025-08-21 17:07:49.789  3681-3681  Common                  com.dspread.mdm.service              D  🔧 ServiceStartupManager 收到服务启动请求: APPLICATION_STARTUP, 强制启动: false
2025-08-21 17:07:49.795  3681-3681  Common                  com.dspread.mdm.service              D  🔧 ServiceStartupManager 开始执行服务启动: APPLICATION_STARTUP
2025-08-21 17:07:49.798  3681-3681  Common                  com.dspread.mdm.service              D  🔧 ServiceManager: 准备启动后台服务
2025-08-21 17:07:49.801  3681-3681  ContextImpl             com.dspread.mdm.service              W  Calling a method in the system process without a qualified user: android.app.ContextImpl.startService:1669 android.content.ContextWrapper.startService:720 com.dspread.mdm.service.platform.manager.ServiceManager.startBackgroundService:20 com.dspread.mdm.service.platform.manager.ServiceStartupManager.performServiceStart:121 com.dspread.mdm.service.platform.manager.ServiceStartupManager.startService:66 
2025-08-21 17:07:49.804  3681-3681  Common                  com.dspread.mdm.service              D  🔧 ServiceManager: 后台服务启动成功，ComponentName: ComponentInfo{com.dspread.mdm.service/com.dspread.mdm.service.services.SmartMdmBackgroundService}
2025-08-21 17:07:50.849  3681-3681  Common                  com.dspread.mdm.service              D  🔧 ServiceStartupManager 服务启动验证成功: APPLICATION_STARTUP
2025-08-21 17:07:50.852  3681-3681  Common                  com.dspread.mdm.service              D  🔧 ServiceStartupManager 服务启动成功: APPLICATION_STARTUP
2025-08-21 17:07:50.854  3681-3681  Common                  com.dspread.mdm.service              D  🔧 SmartMdmServiceApp: 主服务启动请求已提交
2025-08-21 17:07:50.856  3681-3681  Common                  com.dspread.mdm.service              D  🔧 SmartMdmServiceApp: Application启动完成，包名: com.dspread.mdm.service
2025-08-21 17:07:50.987  3681-3681  SurfaceFactory          com.dspread.mdm.service              I  [static] sSurfaceFactory = com.mediatek.view.impl.SurfaceFactoryImpl@b8d25f1
2025-08-21 17:07:50.996  3681-3681  ViewRootIm...tActivity] com.dspread.mdm.service              D  hardware acceleration = true , fakeHwAccelerated = false, sRendererDisabled = false, forceHwAccelerated = false, sSystemRendererDisabled = false
2025-08-21 17:07:51.004  3681-3681  InputTransport          com.dspread.mdm.service              I  Create ARC handle: 0x8e819060
2025-08-21 17:07:51.006  3681-3681  PhoneWindow             com.dspread.mdm.service              V  DecorView setVisiblity: visibility = 0, Parent = android.view.ViewRootImpl@23d302d, this = DecorView@8896962[TestActivity]
2025-08-21 17:07:51.013  3681-3681  PhoneWindow             com.dspread.mdm.service              V  DecorView setVisiblity: visibility = 4, Parent = android.view.ViewRootImpl@23d302d, this = DecorView@8896962[TestActivity]
2025-08-21 17:07:51.015  3681-3681  Choreographer           com.dspread.mdm.service              I  Skipped 81 frames!  The application may be doing too much work on its main thread.
2025-08-21 17:07:51.046  3681-3681  Platform                com.dspread.mdm.service              D  🔧 NetworkApi 单例实例已创建
2025-08-21 17:07:51.050  3681-3681  TetheringManager        com.dspread.mdm.service              I  registerTetheringEventCallback:com.dspread.mdm.service
2025-08-21 17:07:51.062  3681-3681  Receiver                com.dspread.mdm.service              D  🔧 网络状态变化: 连接=true, 类型=WIFI
2025-08-21 17:07:51.073  3681-3681  Receiver                com.dspread.mdm.service              D  🔧 网络恢复，尝试重新连接WebSocket
2025-08-21 17:07:51.075  3681-3681  Provisioning            com.dspread.mdm.service              D  🔧 ProvisioningEventHandler 网络状态变化
2025-08-21 17:07:51.082  3681-3681  Provisioning            com.dspread.mdm.service              I  ℹ️ ProvisioningManager初始化完成
2025-08-21 17:07:51.085  3681-3681  Provisioning            com.dspread.mdm.service              D  🔧 状态标志文件不存在，使用默认值
2025-08-21 17:07:51.089  3681-3681  Provisioning            com.dspread.mdm.service              D  🔧 ProvisioningEventHandler 忽略初始启动时的网络连接事件
2025-08-21 17:07:51.092  3681-3681  Receiver                com.dspread.mdm.service              D  🔧 电池状态变化: 电量=85%, 温度=27°C, 充电=true
2025-08-21 17:07:51.193  3681-3681  WebSocket               com.dspread.mdm.service              I  🔧 C0902 被动式上送: battery_level_change_5 (被动: 1)
2025-08-21 17:07:51.208  3681-3681  Platform                com.dspread.mdm.service              D  🔧 DeviceInfoApi 单例实例已创建
2025-08-21 17:07:51.224  3681-3681  WebSocket               com.dspread.mdm.service              I  🔧 消息已缓存: C0902 (缓存数量: 1)
2025-08-21 17:07:51.227  3681-3681  WebSocket               com.dspread.mdm.service              I  🔧 C0902 电池状态上送成功 (1, trigger=battery_level_change_5)
2025-08-21 17:07:51.234  3681-3681  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 服务启动
2025-08-21 17:07:51.246  3681-3681  setContentIntent        com.dspread.mdm.service              D  packageName = com.dspread.mdm.service
2025-08-21 17:07:51.251   947-975   NotificationService     system_server                        E  enqueueNotificationInternal pkg: com.dspread.mdm.service
2025-08-21 17:07:51.252  3681-3681  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 开始服务初始化
2025-08-21 17:07:51.254  3681-3681  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] Android < 14: 启动Provisioning服务
2025-08-21 17:07:51.258  3681-3713  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 启动ProvisioningService
2025-08-21 17:07:51.259  3681-3681  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 服务创建完成
2025-08-21 17:07:51.262  3681-3681  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] onStartCommand调用
2025-08-21 17:07:51.269  3681-3681  Platform                com.dspread.mdm.service              D  🔧 WakeLockManager 获取智能WakeLock成功，持续时间: 60000ms
2025-08-21 17:07:51.274  3681-3713  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] ProvisioningService启动完成
2025-08-21 17:07:51.276  3681-3681  Platform                com.dspread.mdm.service              D  🔧 WakeLockManager 设置WakeLock续期定时器: 60000ms
2025-08-21 17:07:51.277  3681-3714  Provisioning            com.dspread.mdm.service              I  ℹ️ 开始执行Provisioning配置，触发类型: FIRST_BOOT
2025-08-21 17:07:51.280  3681-3714  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] Provisioning进度: 正在下载配置文件...
2025-08-21 17:07:51.283  3681-3714  Provisioning            com.dspread.mdm.service              I  ℹ️ 开始请求远程配置...
2025-08-21 17:07:51.287  3681-3681  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 初始化基础组件
2025-08-21 17:07:51.292  3681-3714  Provisioning            com.dspread.mdm.service              I  ℹ️ 请求配置: https://config.dspreadserv.net/status/config?SN=MDE2MTAwNDAyMDIzMDcwNjAyMjc=&timestamp=1755767271292
2025-08-21 17:07:51.303  3681-3714  System.out              com.dspread.mdm.service              I  [okhttp]:check permission begin!
2025-08-21 17:07:51.304  3681-3714  System                  com.dspread.mdm.service              W  ClassLoader referenced unknown path: system/framework/mediatek-cta.jar
2025-08-21 17:07:51.305  3681-3714  System.out              com.dspread.mdm.service              I  [okhttp] e:java.lang.ClassNotFoundException: com.mediatek.cta.CtaUtils
2025-08-21 17:07:51.309  3681-3716  Platform                com.dspread.mdm.service              W  ⚠️ SpVersionApi SP版本信息格式异常，未找到SP_VERSION标记
2025-08-21 17:07:51.320  3681-3681  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 服务初始化完成
2025-08-21 17:07:51.322  3681-3716  Platform                com.dspread.mdm.service              D  🔧 SpVersionApi 从Settings获取SP版本: STD 582.1.238.8
2025-08-21 17:07:51.340  3681-3681  WebSocket               com.dspread.mdm.service              I  🔧 C0905 主动式上送: manual_trigger (主动: 1)
2025-08-21 17:07:51.348  3681-3681  WebSocket               com.dspread.mdm.service              I  🔧 首次上送，上送当日数据
2025-08-21 17:07:51.351  3681-3681  WebSocket               com.dspread.mdm.service              I  🔧 没有流量数据需要上送
2025-08-21 17:07:51.353  3681-3681  Common                  com.dspread.mdm.service              D  🔧 刷新时自动上送流量统计
2025-08-21 17:07:51.639  3681-3714  System.out              com.dspread.mdm.service              I  [socket]:check permission begin!
2025-08-21 17:07:51.640  3681-3714  System                  com.dspread.mdm.service              W  ClassLoader referenced unknown path: system/framework/mediatek-cta.jar
2025-08-21 17:07:51.641  3681-3714  System.out              com.dspread.mdm.service              I  [socket] e:java.lang.ClassNotFoundException: com.mediatek.cta.CtaUtils
2025-08-21 17:07:52.745  3681-3714  System.out              com.dspread.mdm.service              I  [OkHttp] sendRequest>>
2025-08-21 17:07:52.745  3681-3714  System.out              com.dspread.mdm.service              I  [OkHttp] sendRequest<<
2025-08-21 17:07:53.998  3681-3714  Provisioning            com.dspread.mdm.service              I  ℹ️ 远程配置获取成功
2025-08-21 17:07:54.000  3681-3714  Provisioning            com.dspread.mdm.service              I  ℹ️ 远程配置请求成功
2025-08-21 17:07:54.003  3681-3714  Provisioning            com.dspread.mdm.service              I  ℹ️ 远程配置解析成功 - CID: 1001
2025-08-21 17:07:54.013  3681-3714  Provisioning            com.dspread.mdm.service              E  ❌ 保存API响应失败 (Ask Gemini)
                                                                                                    java.io.FileNotFoundException: /sdcard/Android/data/com.dspread.mdm.service/files/config/provisioning_config.json: open failed: ENOENT (No such file or directory)
                                                                                                         at libcore.io.IoBridge.open(IoBridge.java:492)
                                                                                                         at java.io.FileOutputStream.<init>(FileOutputStream.java:236)
                                                                                                         at java.io.FileOutputStream.<init>(FileOutputStream.java:186)
                                                                                                         at kotlin.io.FilesKt__FileReadWriteKt.writeBytes(FileReadWrite.kt:108)
                                                                                                         at kotlin.io.FilesKt__FileReadWriteKt.writeText(FileReadWrite.kt:134)
                                                                                                         at kotlin.io.FilesKt__FileReadWriteKt.writeText$default(FileReadWrite.kt:134)
                                                                                                         at com.dspread.mdm.service.modules.provisioning.ProvisioningManager.saveApiResponse(ProvisioningManager.kt:611)
                                                                                                         at com.dspread.mdm.service.modules.provisioning.ProvisioningManager.access$saveApiResponse(ProvisioningManager.kt:22)
                                                                                                         at com.dspread.mdm.service.modules.provisioning.ProvisioningManager$executeProvisioning$1.invokeSuspend(ProvisioningManager.kt:95)
                                                                                                         at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
                                                                                                         at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:108)
                                                                                                         at kotlinx.coroutines.internal.LimitedDispatcher$Worker.run(LimitedDispatcher.kt:115)
                                                                                                         at kotlinx.coroutines.scheduling.TaskImpl.run(Tasks.kt:103)
                                                                                                         at kotlinx.coroutines.scheduling.CoroutineScheduler.runSafely(CoroutineScheduler.kt:584)
                                                                                                         at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.executeTask(CoroutineScheduler.kt:793)
                                                                                                         at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.runWorker(CoroutineScheduler.kt:697)
                                                                                                         at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.run(CoroutineScheduler.kt:684)
                                                                                                    Caused by: android.system.ErrnoException: open failed: ENOENT (No such file or directory)
                                                                                                         at libcore.io.Linux.open(Native Method)
                                                                                                         at libcore.io.ForwardingOs.open(ForwardingOs.java:166)
                                                                                                         at libcore.io.BlockGuardOs.open(BlockGuardOs.java:254)
                                                                                                         at libcore.io.ForwardingOs.open(ForwardingOs.java:166)
                                                                                                         at android.app.ActivityThread$AndroidOs.open(ActivityThread.java:7607)
                                                                                                         at libcore.io.IoBridge.open(IoBridge.java:478)
                                                                                                         at java.io.FileOutputStream.<init>(FileOutputStream.java:236) 
                                                                                                         at java.io.FileOutputStream.<init>(FileOutputStream.java:186) 
                                                                                                         at kotlin.io.FilesKt__FileReadWriteKt.writeBytes(FileReadWrite.kt:108) 
                                                                                                         at kotlin.io.FilesKt__FileReadWriteKt.writeText(FileReadWrite.kt:134) 
                                                                                                         at kotlin.io.FilesKt__FileReadWriteKt.writeText$default(FileReadWrite.kt:134) 
                                                                                                         at com.dspread.mdm.service.modules.provisioning.ProvisioningManager.saveApiResponse(ProvisioningManager.kt:611) 
                                                                                                         at com.dspread.mdm.service.modules.provisioning.ProvisioningManager.access$saveApiResponse(ProvisioningManager.kt:22) 
                                                                                                         at com.dspread.mdm.service.modules.provisioning.ProvisioningManager$executeProvisioning$1.invokeSuspend(ProvisioningManager.kt:95) 
                                                                                                         at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33) 
                                                                                                         at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:108) 
                                                                                                         at kotlinx.coroutines.internal.LimitedDispatcher$Worker.run(LimitedDispatcher.kt:115) 
                                                                                                         at kotlinx.coroutines.scheduling.TaskImpl.run(Tasks.kt:103) 
                                                                                                         at kotlinx.coroutines.scheduling.CoroutineScheduler.runSafely(CoroutineScheduler.kt:584) 
                                                                                                         at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.executeTask(CoroutineScheduler.kt:793) 
                                                                                                         at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.runWorker(CoroutineScheduler.kt:697) 
                                                                                                         at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.run(CoroutineScheduler.kt:684) 
2025-08-21 17:07:54.017  3681-3714  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] Provisioning进度: 正在下载 Logo...
2025-08-21 17:07:54.021  3681-3714  Provisioning            com.dspread.mdm.service              I  ℹ️ 文件不存在，开始下载: Logo
2025-08-21 17:07:54.023  3681-3714  Provisioning            com.dspread.mdm.service              I  ℹ️ 下载: https://siot-bucket01.s3.ap-northeast-1.amazonaws.com/test/logo.bin -> /sdcard/Android/data/com.dspread.mdm.service/files/config/logo.bin
2025-08-21 17:07:54.032  3681-3714  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 尝试下载，第1次
2025-08-21 17:07:54.036  3681-3714  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 开始下载
2025-08-21 17:07:54.038  3681-3714  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader URL: https://siot-bucket01.s3.ap-northeast-1.amazonaws.com/test/logo.bin
2025-08-21 17:07:54.041  3681-3714  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 路径: /sdcard/Android/data/com.dspread.mdm.service/files/config/logo.bin
2025-08-21 17:07:54.043  3681-3714  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 本地文件不存在，创建文件
2025-08-21 17:07:54.047  3681-3714  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader allFileLength=0, 需要从服务器获取文件大小
2025-08-21 17:07:54.051  3681-3714  System.out              com.dspread.mdm.service              I  [okhttp]:check permission begin!
2025-08-21 17:07:54.051  3681-3714  System.out              com.dspread.mdm.service              I  [okhttp]:not MMS!
2025-08-21 17:07:54.051  3681-3714  System.out              com.dspread.mdm.service              I  [okhttp]:not Email!
2025-08-21 17:07:54.404  3681-3714  System.out              com.dspread.mdm.service              I  [socket]:check permission begin!
2025-08-21 17:07:54.878  3681-3714  System.out              com.dspread.mdm.service              I  [OkHttp] sendRequest>>
2025-08-21 17:07:54.879  3681-3714  System.out              com.dspread.mdm.service              I  [OkHttp] sendRequest<<
2025-08-21 17:07:55.530  3681-3714  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader HTTP响应码: 200
2025-08-21 17:07:55.533  3681-3714  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 服务器返回Content-Length: 4771451
2025-08-21 17:07:55.535  3681-3714  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 完整下载模式
2025-08-21 17:07:55.537  3681-3714  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 完整下载，使用服务器返回大小: 4771451
2025-08-21 17:07:55.539  3681-3714  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader localFile = true /sdcard/Android/data/com.dspread.mdm.service/files/config/logo.bin, 追加模式: false
2025-08-21 17:07:56.536  3681-3727  ProfileInstaller        com.dspread.mdm.service              D  Installing profile for com.dspread.mdm.service
2025-08-21 17:08:02.288  3681-3681  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 没有找到处理器: android.intent.action.TIME_TICK
2025-08-21 17:08:02.317  3681-3714  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 计算MD5: logo.bin
2025-08-21 17:08:02.394  3681-3714  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 文件: 4771451 MD5: ba1ee533924eae5c408465e7cddcbda4
2025-08-21 17:08:02.396  3681-3714  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader MD5校验正确
2025-08-21 17:08:02.398  3681-3714  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 清理连接资源
2025-08-21 17:08:02.401  3681-3714  Provisioning            com.dspread.mdm.service              I  ℹ️ Logo 下载完成: /sdcard/Android/data/com.dspread.mdm.service/files/config/logo.bin
2025-08-21 17:08:02.403  3681-3714  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] Provisioning进度: 正在下载 BootAnimation...
2025-08-21 17:08:02.406  3681-3714  Provisioning            com.dspread.mdm.service              I  ℹ️ 文件不存在，开始下载: BootAnimation
2025-08-21 17:08:02.408  3681-3714  Provisioning            com.dspread.mdm.service              I  ℹ️ 下载: https://siot-bucket01.s3.ap-northeast-1.amazonaws.com/test/bootanimation.zip -> /sdcard/Android/data/com.dspread.mdm.service/files/config/bootanimation.zip
2025-08-21 17:08:02.410  3681-3714  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 尝试下载，第1次
2025-08-21 17:08:02.413  3681-3714  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 开始下载
2025-08-21 17:08:02.416  3681-3714  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader URL: https://siot-bucket01.s3.ap-northeast-1.amazonaws.com/test/bootanimation.zip
2025-08-21 17:08:02.418  3681-3714  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 路径: /sdcard/Android/data/com.dspread.mdm.service/files/config/bootanimation.zip
2025-08-21 17:08:02.421  3681-3714  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 本地文件不存在，创建文件
2025-08-21 17:08:02.423  3681-3714  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader allFileLength=0, 需要从服务器获取文件大小
2025-08-21 17:08:02.428  3681-3714  System.out              com.dspread.mdm.service              I  [okhttp]:check permission begin!
2025-08-21 17:08:02.428  3681-3714  System.out              com.dspread.mdm.service              I  [okhttp]:not MMS!
2025-08-21 17:08:02.428  3681-3714  System.out              com.dspread.mdm.service              I  [okhttp]:not Email!
2025-08-21 17:08:02.428  3681-3714  System.out              com.dspread.mdm.service              I  [OkHttp] sendRequest>>
2025-08-21 17:08:02.429  3681-3714  System.out              com.dspread.mdm.service              I  [OkHttp] sendRequest<<
2025-08-21 17:08:02.803  3681-3714  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader HTTP响应码: 200
2025-08-21 17:08:02.807  3681-3714  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 服务器返回Content-Length: 4771451
2025-08-21 17:08:02.809  3681-3714  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 完整下载模式
2025-08-21 17:08:02.811  3681-3714  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 完整下载，使用服务器返回大小: 4771451
2025-08-21 17:08:02.814  3681-3714  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader localFile = true /sdcard/Android/data/com.dspread.mdm.service/files/config/bootanimation.zip, 追加模式: false
2025-08-21 17:08:08.520  3681-3714  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 计算MD5: bootanimation.zip
2025-08-21 17:08:08.599  3681-3714  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 文件: 4771451 MD5: ba1ee533924eae5c408465e7cddcbda4
2025-08-21 17:08:08.601  3681-3714  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader MD5校验正确
2025-08-21 17:08:08.603  3681-3714  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 清理连接资源
2025-08-21 17:08:08.605  3681-3714  Provisioning            com.dspread.mdm.service              I  ℹ️ BootAnimation 下载完成: /sdcard/Android/data/com.dspread.mdm.service/files/config/bootanimation.zip
2025-08-21 17:08:08.607  3681-3714  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] Provisioning进度: 正在应用配置...
2025-08-21 17:08:08.608  3681-3714  Provisioning            com.dspread.mdm.service              I  ℹ️ 配置应用完成
2025-08-21 17:08:08.610  3681-3714  Provisioning            com.dspread.mdm.service              I  ℹ️ 远程配置成功，标记首次配置完成
2025-08-21 17:08:08.614  3681-3714  Provisioning            com.dspread.mdm.service              D  🔧 状态标志保存成功: /sdcard/Android/data/com.dspread.mdm.service/files/config/provisioning_flags.json
2025-08-21 17:08:08.616  3681-3714  Provisioning            com.dspread.mdm.service              I  ℹ️ ProvisioningManager: 初始启动标记完成，网络重连重试功能已激活
2025-08-21 17:08:08.618  3681-3714  Provisioning            com.dspread.mdm.service              I  ℹ️ Provisioning配置执行完成
2025-08-21 17:08:08.621  3681-3714  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] Provisioning完成，启动核心组件
2025-08-21 17:08:08.628  3681-3681  WebSocket               com.dspread.mdm.service              I  🔧 开始初始化 WebSocket 组件...
2025-08-21 17:08:08.633  3681-3681  WebSocket               com.dspread.mdm.service              I  🔧 初始化 WebSocket 密钥管理器
2025-08-21 17:08:08.637  3681-3681  WebSocket               com.dspread.mdm.service              I  🔧 生成客户端公私钥对
2025-08-21 17:08:08.840  3681-3681  Common                  com.dspread.mdm.service              I  ✅ WebSocket 密钥管理器初始化完成
2025-08-21 17:08:08.848  3681-3681  WebSocket               com.dspread.mdm.service              I  🔧 开始初始化 WsMessageCenter...
2025-08-21 17:08:08.850  3681-3681  WebSocket               com.dspread.mdm.service              I  🔧 WsMessageCenter 首次初始化
2025-08-21 17:08:08.866  3681-3681  WebSocket               com.dspread.mdm.service              I  🔧 WsTaskManager 初始化完成
2025-08-21 17:08:08.966  3681-3681  WebSocket               com.dspread.mdm.service              I  🔧 WsMessageCenter 初始化完成
2025-08-21 17:08:08.968  3681-3681  WebSocket               com.dspread.mdm.service              I  🔧 开始清理终态任务，当前任务数量: 0
2025-08-21 17:08:08.970  3681-3681  WebSocket               com.dspread.mdm.service              I  🔧 没有需要清理的终态任务
2025-08-21 17:08:08.972  3681-3681  WebSocket               com.dspread.mdm.service              I  🔧 开始重置任务状态，当前任务个数: 0
2025-08-21 17:08:08.975  3681-3681  WebSocket               com.dspread.mdm.service              I  🔧 没有需要重置的任务
2025-08-21 17:08:08.977  3681-3681  Common                  com.dspread.mdm.service              I  ✅ 任务状态恢复完成
2025-08-21 17:08:08.983  3681-3681  WebSocket               com.dspread.mdm.service              I  🔧 使用Provisioning配置的WebSocket URL: wss://api.dspreadserv.net/status/websocket/register
2025-08-21 17:08:08.986  3681-3681  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接管理器初始化完成
2025-08-21 17:08:08.988  3681-3681  Common                  com.dspread.mdm.service              I  ✅ WebSocket 管理器初始化完成
2025-08-21 17:08:08.991  3681-3681  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 管理器初始化完成
2025-08-21 17:08:08.995  3681-3681  Common                  com.dspread.mdm.service              I  ✅ WebSocket 组件初始化完成
2025-08-21 17:08:08.997  3681-3681  WebSocket               com.dspread.mdm.service              I  🔧 启动 WebSocket 连接...
2025-08-21 17:08:08.999  3681-3681  WebSocket               com.dspread.mdm.service              I  🔧 开始连接 WebSocket...
2025-08-21 17:08:09.011  3681-3681  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接到: wss://api.dspreadserv.net/status/websocket/register?serialNo=MDE2MTAwNDAyMDIzMDcwNjAyMjc=&publicKey=TUlHZk1BMEdDU3FHU0liM0RRRUJBUVVBQTRHTkFEQ0JpUUtCZ1FEQ2pZR1UvckRtaVpjL25xNndSdWE0T2MvVmRRTlVrWURES0tVL3ZqZkRrc2FieXAvcHhkTkRvNm92MncyMUNKd25qSmR5bmNMcTk3SFZ6M3NKMHBZN1l3N1M4Z2twU1ptdVRzNUpiYnhjc2tmZVZpWUdiNFlyWUpXODU4eEJySzBCZEFBeG1wR2VIbkoxRmVYYkxZUnRSU1NkTTlFa2NGOW5tZTdpUkdmcVR3SURBUUFC&query=1&msgVer=3&timestamp=1755767289003&signature=M7zr1l0+eosh8PNbHEvquDeFXyXrf+NzBE80oTYfRHTG0fbSCcS7l1QsDLNLL6N1+IZzLkfUUNAC/BZHrceDJLIxOAiwf89IBILuyEgqVjxqFbD2DDVVM/I5Zj4MqC+oXixnTjuFy9c4HOEMyHFzl1NqApp1r9T6Z3en+ssZrok=
2025-08-21 17:08:09.017  3681-3681  WebSocket               com.dspread.mdm.service              I  🔧 未检测到系统代理设置
2025-08-21 17:08:09.036  3681-3681  WebSocket               com.dspread.mdm.service              I  🔧 ===== WebSocket 库默认配置 =====
2025-08-21 17:08:09.038  3681-3681  WebSocket               com.dspread.mdm.service              I  🔧 默认 PING 间隔: 30000ms (30秒)
2025-08-21 17:08:09.040  3681-3681  WebSocket               com.dspread.mdm.service              I  🔧 默认 PONG 间隔: 0ms (0秒)
2025-08-21 17:08:09.043  3681-3681  WebSocket               com.dspread.mdm.service              I  🔧 帧队列大小: 5
2025-08-21 17:08:09.045  3681-3681  WebSocket               com.dspread.mdm.service              I  🔧 连接超时: 15000ms (15秒)
2025-08-21 17:08:09.047  3681-3681  WebSocket               com.dspread.mdm.service              I  🔧 允许缺失关闭帧: false
2025-08-21 17:08:09.049  3681-3681  WebSocket               com.dspread.mdm.service              I  🔧 ================================
2025-08-21 17:08:09.053  3681-3681  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接请求已发送
2025-08-21 17:08:09.055  3681-3681  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接启动完成
2025-08-21 17:08:09.059  3681-3681  Common                  com.dspread.mdm.service              D  🔧 [ModuleManagerRegistry] 模块管理器注册中心初始化完成
2025-08-21 17:08:09.062  3681-3681  Provisioning            com.dspread.mdm.service              D  🔧 ProvisioningEventHandler 启动Provisioning定时器: 300s (0小时)
2025-08-21 17:08:09.062  3681-3714  Common                  com.dspread.mdm.service              D  🔧 [ModuleManagerRegistry] 启动所有模块
2025-08-21 17:08:09.067  3681-3681  Common                  com.dspread.mdm.service              I  ✅ ⏰ 设置初始化定时器成功，下次执行: 300秒后 (0小时)
2025-08-21 17:08:09.071  3681-3714  Common                  com.dspread.mdm.service              D  🔧 Module LogStream Manager status changed to: INITIALIZED
2025-08-21 17:08:09.071  3681-3681  Common                  com.dspread.mdm.service              I  ✅ ⏰ 设置心跳定时器成功，下次执行: 60秒后 (1分钟)
2025-08-21 17:08:09.075  3681-3681  Common                  com.dspread.mdm.service              I  ✅ ⏰ 设置任务执行定时器成功，下次执行: 60秒后 (1分钟)
2025-08-21 17:08:09.075  3681-3714  Common                  com.dspread.mdm.service              D  🔧 Module LogStream Manager status changed to: STARTING
2025-08-21 17:08:09.081  3681-3681  Common                  com.dspread.mdm.service              I  ✅ ⏰ 设置终端信息定时器成功，下次执行: 120秒后 (2分钟)
2025-08-21 17:08:09.083  3681-3681  Common                  com.dspread.mdm.service              D  🔧 TimerConfig ========== 当前定时器配置 (调试模式) ==========
2025-08-21 17:08:09.085  3681-3681  Common                  com.dspread.mdm.service              D  🔧 TimerConfig ①心跳定时器: 60秒
2025-08-21 17:08:09.087  3681-3681  Common                  com.dspread.mdm.service              D  🔧 TimerConfig ②终端信息上传定时器: 120秒
2025-08-21 17:08:09.089  3681-3681  Common                  com.dspread.mdm.service              D  🔧 TimerConfig ③任务执行定时器: 60秒
2025-08-21 17:08:09.091  3681-3681  Common                  com.dspread.mdm.service              D  🔧 TimerConfig ④服务守护定时器: 120秒
2025-08-21 17:08:09.093  3681-3681  Common                  com.dspread.mdm.service              D  🔧 TimerConfig ⑤初始化定时器: 300秒
2025-08-21 17:08:09.096  3681-3681  Common                  com.dspread.mdm.service              D  🔧 TimerConfig ===============================================
2025-08-21 17:08:09.098  3681-3681  Common                  com.dspread.mdm.service              I  ✅ [SmartMdmBackgroundService] 所有定时器启动完成
2025-08-21 17:08:09.101  3681-3681  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 核心组件初始化完成
2025-08-21 17:08:09.150  3681-3714  Common                  com.dspread.mdm.service              D  🔧 Module LogStream Manager status changed to: RUNNING
2025-08-21 17:08:09.153  3681-3714  Common                  com.dspread.mdm.service              D  🔧 [ModuleManagerRegistry] LogStreamManager启动成功
2025-08-21 17:08:09.176  3681-3714  Common                  com.dspread.mdm.service              D  🔧 Module Geofence Manager status changed to: INITIALIZED
2025-08-21 17:08:09.181  3681-3714  Common                  com.dspread.mdm.service              D  🔧 Module Geofence Manager status changed to: STARTING
2025-08-21 17:08:09.201  3681-3681  Platform                com.dspread.mdm.service              D  🔧 [WiFiManagerApi] WiFiManagerApi 单例实例已创建
2025-08-21 17:08:09.202  3681-3714  Platform                com.dspread.mdm.service              D  🔧 用户交互监控已启动（简化版本）
2025-08-21 17:08:09.203  3681-3714  Platform                com.dspread.mdm.service              D  🔧 使用模拟用户交互检测
2025-08-21 17:08:09.205  3681-3714  Platform                com.dspread.mdm.service              D  🔧 用户交互监控已启动
2025-08-21 17:08:09.207  3681-3714  Service                 com.dspread.mdm.service              D  🔧 [GeofenceManager]Constants.geofenceStatus = 1
2025-08-21 17:08:09.209  3681-3714  Service                 com.dspread.mdm.service              D  🔧 [GeofenceManager]-- Constants.storeId =  -- Constants.storeSsid =  -- Constants.storeIp = 
2025-08-21 17:08:09.211  3681-3714  Common                  com.dspread.mdm.service              D  🔧 Module Geofence Manager status changed to: RUNNING
2025-08-21 17:08:09.214  3681-3714  Common                  com.dspread.mdm.service              D  🔧 [ModuleManagerRegistry] GeofenceManager启动成功
2025-08-21 17:08:09.226  3681-3714  RuleBase                com.dspread.mdm.service              D  🔧 RuleBaseManager 开始初始化RuleBaseManager...
2025-08-21 17:08:09.233  3681-3714  RuleBase                com.dspread.mdm.service              D  🔧 RuleBaseStorage RuleBase存储管理器初始化完成
2025-08-21 17:08:09.235  3681-3714  RuleBase                com.dspread.mdm.service              D  🔧 RuleStateMonitor 状态监控器初始化完成
2025-08-21 17:08:09.237  3681-3714  RuleBase                com.dspread.mdm.service              D  🔧 RuleBaseManager RuleBaseManager初始化完成
2025-08-21 17:08:09.239  3681-3714  Common                  com.dspread.mdm.service              D  🔧 [ModuleManagerRegistry] RuleBaseManager启动成功
2025-08-21 17:08:09.241  3681-3714  Common                  com.dspread.mdm.service              D  🔧 [ModuleManagerRegistry] 所有模块启动完成
2025-08-21 17:08:09.460  3681-3744  System.out              com.dspread.mdm.service              I  [socket]:check permission begin!
2025-08-21 17:08:09.709  3681-3745  System.out              com.dspread.mdm.service              I  [socket]:check permission begin!
2025-08-21 17:08:11.821  3681-3746  Common                  com.dspread.mdm.service              I  ✅ WebSocket 物理连接成功，等待服务器 hellMsg
2025-08-21 17:08:11.824  3681-3746  WebSocket               com.dspread.mdm.service              I  🔧 ===== 心跳发送器实际配置 =====
2025-08-21 17:08:11.826  3681-3746  WebSocket               com.dspread.mdm.service              I  🔧 实际 PING 发送间隔: 30000ms (30秒)
2025-08-21 17:08:11.829  3681-3746  WebSocket               com.dspread.mdm.service              I  🔧 PING 定时器名称: PingSender
2025-08-21 17:08:11.832  3681-3746  WebSocket               com.dspread.mdm.service              I  🔧 实际 PONG 发送间隔: 0ms (0秒)
2025-08-21 17:08:11.834  3681-3746  WebSocket               com.dspread.mdm.service              I  🔧 PONG 定时器名称: PongSender
2025-08-21 17:08:11.837  3681-3746  WebSocket               com.dspread.mdm.service              I  🔧 ===============================
2025-08-21 17:08:11.839  3681-3746  WebSocket               com.dspread.mdm.service              I  🔧 启动PING/PONG检测，阈值: 连续3个PING无PONG响应
2025-08-21 17:08:11.865  3681-3746  Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"msg_state":"P","request_time":"1755580536662","data":{"taskList":[{"beginDate":"2024-08-19 05:15:36","taskType":"02","apkName":"Via","endDate":"9999-12-31 23:59:59","appId":"1755580536662","packName":"mark.via","versionName":"6.2.0","taskId":"1755580536662","versionCode":"20250117"}]},"tranCode":"ST001","request_id":"1755580536662ST001","version":"1","serialNo":"01610040202307060227"}
2025-08-21 17:08:11.868  3681-3746  WebSocket               com.dspread.mdm.service              I  🔧 准备发送响应确认 - requestId: 1755580536662ST001, needResponse: true
2025-08-21 17:08:11.874  3681-3746  WebSocket               com.dspread.mdm.service              I  🔧 非重要消息，不缓存: C0000
2025-08-21 17:08:11.881  3681-3746  WebSocket               com.dspread.mdm.service              I  🔧 非重要消息，不缓存: C0000
2025-08-21 17:08:11.882  3681-3746  WebSocket               com.dspread.mdm.service              I  🔧 已发送C0000响应确认: requestId=1755580536662ST001
2025-08-21 17:08:11.885  3681-3746  Task                    com.dspread.mdm.service              D  🔧 处理任务消息: ST001
2025-08-21 17:08:11.887  3681-3746  Task                    com.dspread.mdm.service              I  ℹ️ 更新任务列表: requestId=1755580536662ST001, 任务数量=1
2025-08-21 17:08:11.890  3681-3746  Task                    com.dspread.mdm.service              D  🔧 任务插入到末尾: taskId=1755580536662
2025-08-21 17:08:11.892  3681-3746  Task                    com.dspread.mdm.service              D  🔧 添加/更新任务: taskId=1755580536662
2025-08-21 17:08:11.895  3681-3746  Task                    com.dspread.mdm.service              D  🔧 保存任务列表到存储: 1 个
2025-08-21 17:08:11.898  3681-3746  Task                    com.dspread.mdm.service              D  🔧 开始处理任务列表，共 1 个任务
2025-08-21 17:08:11.900  3681-3746  Task                    com.dspread.mdm.service              D  🔧 处理任务: taskId=1755580536662, type=02, package=mark.via, apk=Via
2025-08-21 17:08:11.901  3681-3746  Task                    com.dspread.mdm.service              D  🔧 处理卸载任务: 1755580536662
2025-08-21 17:08:11.909  3681-3746  Task                    com.dspread.mdm.service              D  🔧 更新任务状态: taskId=1755580536662, state=D02
2025-08-21 17:08:11.911  3681-3746  Task                    com.dspread.mdm.service              D  🔧 保存任务列表到存储: 1 个
2025-08-21 17:08:11.914  3681-3746  Task                    com.dspread.mdm.service              D  🔧 保存任务列表到存储: 1 个
2025-08-21 17:08:11.917  3681-3746  WebSocket               com.dspread.mdm.service              I  🔧 ST001 主动式上送: task_failed (主动: 2)
2025-08-21 17:08:11.925  3681-3746  WebSocket               com.dspread.mdm.service              I  🔧 消息已缓存: C0108 (缓存数量: 2)
2025-08-21 17:08:11.926  3681-3746  WebSocket               com.dspread.mdm.service              I  🔧 任务结果实时上送: taskId=1755580536662, result=D02 (1)
2025-08-21 17:08:11.941  3681-3746  Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"msg_state":"P","request_time":"1755580524415","data":{"taskList":[{"beginDate":"2024-08-19 05:15:24","taskType":"02","apkName":"OpenVPN for Android","endDate":"9999-12-31 23:59:59","appId":"1755580524415","packName":"de.blinkt.openvpn","versionName":"0.5.36a","taskId":"1755580524415","versionCode":"65"}]},"tranCode":"ST001","request_id":"1755580524415ST001","version":"1","serialNo":"01610040202307060227"}
2025-08-21 17:08:11.944  3681-3746  WebSocket               com.dspread.mdm.service              I  🔧 准备发送响应确认 - requestId: 1755580524415ST001, needResponse: true
2025-08-21 17:08:11.948  3681-3746  WebSocket               com.dspread.mdm.service              I  🔧 非重要消息，不缓存: C0000
2025-08-21 17:08:11.954  3681-3746  WebSocket               com.dspread.mdm.service              I  🔧 非重要消息，不缓存: C0000
2025-08-21 17:08:11.955  3681-3746  WebSocket               com.dspread.mdm.service              I  🔧 已发送C0000响应确认: requestId=1755580524415ST001
2025-08-21 17:08:11.958  3681-3746  Task                    com.dspread.mdm.service              D  🔧 处理任务消息: ST001
2025-08-21 17:08:11.959  3681-3746  Task                    com.dspread.mdm.service              I  ℹ️ 更新任务列表: requestId=1755580524415ST001, 任务数量=1
2025-08-21 17:08:11.962  3681-3746  Task                    com.dspread.mdm.service              D  🔧 任务按时间插入到位置: 0, taskId=1755580524415
2025-08-21 17:08:11.963  3681-3746  Task                    com.dspread.mdm.service              D  🔧 添加/更新任务: taskId=1755580524415
2025-08-21 17:08:11.965  3681-3746  Task                    com.dspread.mdm.service              D  🔧 发现已完成任务: taskId=1755580536662, result=D02
2025-08-21 17:08:11.968  3681-3746  Task                    com.dspread.mdm.service              D  🔧 上送任务结果: taskId=1755580536662, result=D02
2025-08-21 17:08:11.969  3681-3746  Task                    com.dspread.mdm.service              D  🔧 清理已完成任务: taskId=1755580536662, result=D02
2025-08-21 17:08:11.971  3681-3746  Task                    com.dspread.mdm.service              D  🔧 清理了 1 个已完成任务
2025-08-21 17:08:11.973  3681-3746  Task                    com.dspread.mdm.service              D  🔧 保存任务列表到存储: 1 个
2025-08-21 17:08:11.975  3681-3746  Task                    com.dspread.mdm.service              D  🔧 开始处理任务列表，共 1 个任务
2025-08-21 17:08:11.977  3681-3746  Task                    com.dspread.mdm.service              D  🔧 处理任务: taskId=1755580524415, type=02, package=de.blinkt.openvpn, apk=OpenVPN for Android
2025-08-21 17:08:11.979  3681-3746  Task                    com.dspread.mdm.service              D  🔧 处理卸载任务: 1755580524415
2025-08-21 17:08:11.982  3681-3746  Task                    com.dspread.mdm.service              D  🔧 更新任务状态: taskId=1755580524415, state=D02
2025-08-21 17:08:11.985  3681-3746  Task                    com.dspread.mdm.service              D  🔧 保存任务列表到存储: 1 个
2025-08-21 17:08:11.988  3681-3746  Task                    com.dspread.mdm.service              D  🔧 保存任务列表到存储: 1 个
2025-08-21 17:08:11.989  3681-3746  WebSocket               com.dspread.mdm.service              I  🔧 ST001 主动式上送: task_failed (主动: 3)
2025-08-21 17:08:11.996  3681-3746  WebSocket               com.dspread.mdm.service              I  🔧 消息已缓存: C0108 (缓存数量: 3)
2025-08-21 17:08:11.998  3681-3746  WebSocket               com.dspread.mdm.service              I  🔧 任务结果实时上送: taskId=1755580524415, result=D02 (1)
2025-08-21 17:08:12.009  3681-3746  Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"hellMsg":"hello 01610040202307060227","tranCode":"S0000","version":"1","rebootTime":"03:42:51","serialNo":"01610040202307060227","deviceStatus":"6"}
2025-08-21 17:08:12.011  3681-3746  Common                  com.dspread.mdm.service              I  ✅ 收到服务器 hellMsg，WebSocket 业务连接成功！
2025-08-21 17:08:12.013  3681-3746  Common                  com.dspread.mdm.service              I  ✅ WebSocket 业务连接成功！
2025-08-21 17:08:12.014  3681-3746  WebSocket               com.dspread.mdm.service              I  🔧 业务连接成功，重连计数已重置为 0
2025-08-21 17:08:12.018  3681-3746  Platform                com.dspread.mdm.service              D  🔧 WakeLockManager 获取WiFi锁成功，防止WiFi休眠
2025-08-21 17:08:12.020  3681-3746  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接成功，检查自身更新任务状态
2025-08-21 17:08:12.023  3681-3746  Receiver                com.dspread.mdm.service              D  🔧 发送广播: com.dspread.mdm.service.CHECK_SELF_UPDATE_STATUS
2025-08-21 17:08:12.025  3681-3746  WebSocket               com.dspread.mdm.service              I  🔧 开始发送缓存消息，数量: 3
2025-08-21 17:08:12.031  3681-3746  WebSocket               com.dspread.mdm.service              I  🔧 C0902 主动式上送: cached_message_resend (主动: 4)
2025-08-21 17:08:12.037  3681-3746  Common                  com.dspread.mdm.service              I  ✅ 缓存消息发送成功: C0902
2025-08-21 17:08:12.540  3681-3746  WebSocket               com.dspread.mdm.service              I  🔧 C0108 主动式上送: cached_message_resend (主动: 5)
2025-08-21 17:08:12.545  3681-3746  Common                  com.dspread.mdm.service              I  ✅ 缓存消息发送成功: C0108
2025-08-21 17:08:13.048  3681-3746  WebSocket               com.dspread.mdm.service              I  🔧 C0108 主动式上送: cached_message_resend (主动: 6)
2025-08-21 17:08:13.053  3681-3746  Common                  com.dspread.mdm.service              I  ✅ 缓存消息发送成功: C0108
2025-08-21 17:08:13.556  3681-3746  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接成功
2025-08-21 17:08:13.558  3681-3746  WebSocket               com.dspread.mdm.service              I  🔧 设备状态: 6
2025-08-21 17:08:13.561  3681-3746  WebSocket               com.dspread.mdm.service              I  🔧 检查首次连接标志: isFirstTimeWebSocketConnected=true
2025-08-21 17:08:13.563  3681-3746  WebSocket               com.dspread.mdm.service              I  🔧 首次WebSocket连接成功，发送初始化信息并执行初始化检查
2025-08-21 17:08:13.566  3681-3746  WebSocket               com.dspread.mdm.service              I  🔧 开始发送初始化信息（带流量控制）
2025-08-21 17:08:13.568  3681-3746  WebSocket               com.dspread.mdm.service              I  🔧 C0901 被动式上送: first_connection (被动: 2)
2025-08-21 17:08:13.571  3681-3746  Platform                com.dspread.mdm.service              D  🔧 重新收集应用信息
2025-08-21 17:08:13.645  3681-3746  AppManager              com.dspread.mdm.service              I  ℹ️ 应用信息获取完成: 总数120(系统119/用户1) 返回1个
2025-08-21 17:08:13.650  3681-3746  Platform                com.dspread.mdm.service              D  🔧 应用信息: 1 个应用
2025-08-21 17:08:13.660  3681-3746  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0901","serialNo":"01610040202307060227","request_time":"1755767293653","request_id":"1755767293653C0901","version":"1","data":{"apkInfo":[{"packName":"com.dspread.mdm.service","apkName":"service","versionCode":22,"versionName":"1.1.03.20250821.DSPREAD.MDM.SERVICE","updateDate":"2025-08-21 17:07:48"}],"sytemInfo":{"androidVersion":"11","buildNumber":"D60_Baseline_debug_20250708_ota_test","aspl":"2021-12-05"},"serviceInfo":[]},"myVersionName":"1.1.03.20250821.DSPREAD.MDM.SERVICE","terminalDate":"20250821170813"}
2025-08-21 17:08:13.661  3681-3746  WebSocket               com.dspread.mdm.service              I  🔧 C0901 应用信息上送成功 (1)
2025-08-21 17:08:14.664  3681-3746  WebSocket               com.dspread.mdm.service              I  🔧 C0902 被动式上送: first_connection (被动: 3)
2025-08-21 17:08:14.676  3681-3746  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0902","serialNo":"01610040202307060227","request_time":"1755767294669","request_id":"1755767294669C0902","version":"1","data":{"batteryLife":86,"batteryHealth":2,"temprature":"27.6","isCharging":"1","isLowBattery":"0","powerOff":"0","outOfBattery":"0"},"myVersionName":"1.1.03.20250821.DSPREAD.MDM.SERVICE","terminalDate":"20250821170814"}
2025-08-21 17:08:14.679  3681-3746  WebSocket               com.dspread.mdm.service              I  🔧 C0902 电池状态上送成功 (1, trigger=first_connection)
2025-08-21 17:08:15.683  3681-3746  WebSocket               com.dspread.mdm.service              I  🔧 C0903 被动式上送: first_connection (被动: 4)
2025-08-21 17:08:15.823  3681-3746  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0903","serialNo":"01610040202307060227","request_time":"1755767295813","request_id":"1755767295813C0903","version":"1","data":{"hardwareInfo":[{"name":"CPU Usage","desc":"cpu:0.50"},{"name":"storage_usage_status","desc":" 5.43GB \/ 32.00GB ","state":"1"},{"name":"total_memory","desc":" 0.53GB \/ 1.86GB ","state":"1"},{"name":"androidVersion","desc":"11","state":"1"},{"name":"network_status","desc":"wifi","state":"1"},{"name":"OSVer","desc":"11","state":"1"}],"locationInfo":{"gps":{"longitude":"-999.0","latitude":"-999.0","satelliteCount":0,"satelliteCountInUsed":0,"satelliteAverSNR":"0.00","distance":"0.0","lockStatus":"1","lockMeter":"200","proId":"","proName":""},"baseSite":{"MCC":"","MNC":"","CID":"-1","LAC":"-1","DBM":"-999","IMSI":"","ICCID":""},"wifi":{"SSID":"fubox_2.4G","IP":"**************","MAC":"52:C3:63:13:8C:EE","SSTH":"-25","BSSID":"7c:10:c9:d6:9f:10"},"address":"无法获取"}},"myVersionName":"1.1.03.20250821.DSPREAD.MDM.SERVICE","terminalDate":"20250821170815"}
2025-08-21 17:08:15.826  3681-3746  WebSocket               com.dspread.mdm.service              I  🔧 C0903 数据信息上送成功 (1)
2025-08-21 17:08:16.828  3681-3746  WebSocket               com.dspread.mdm.service              I  🔧 C0904 被动式上送: first_connection (被动: 5)
2025-08-21 17:08:16.923  3681-3746  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0904","serialNo":"01610040202307060227","request_time":"1755767296912","request_id":"1755767296912C0904","version":"1","data":{"wifiOption":[{"SSID":"fubox_2.4G","SSTH":"-28"},{"SSID":"2206-5G","SSTH":"-32"},{"SSID":"fubox_5G","SSTH":"-40"},{"SSID":"2206","SSTH":"-41"},{"SSID":"2205_5G","SSTH":"-51"},{"SSID":"2205","SSTH":"-53"},{"SSID":"@Ruijie-1816","SSTH":"-56"},{"SSID":"2207","SSTH":"-67"},{"SSID":"DIRECT-C6-Mi All-in-One Inkjet","SSTH":"-70"},{"SSID":"DIRECT-CC-HP Laser 1188w","SSTH":"-71"},{"SSID":"2306","SSTH":"-72"},{"SSID":"2106","SSTH":"-72"},{"SSID":"2103","SSTH":"-77"},{"SSID":"2207-5G","SSTH":"-80"},{"SSID":"诺富特酒店2208","SSTH":"-81"},{"SSID":"dingjie888","SSTH":"-82"},{"SSID":"FJQS","SSTH":"-83"},{"SSID":"ChinaNet-SnSC","SSTH":"-85"},{"SSID":"2405","SSTH":"-86"},{"SSID":"2103_5G","SSTH":"-86"},{"SSID":"1621","SSTH":"-87"},{"SSID":"HUAWEI_B311_福","SSTH":"-92"},{"SSID":"2106-5G","SSTH":"-94"}],"baseSite":{"MCC":"","MNC":"","CID":"-1","LAC":"-1","DBM":"-999","IMSI":"","ICCID":""},"wifi":{"SSID":"fubox_2.4G","IP":"**************","MAC":"52:C3:63:13:8C:EE","SSTH":"-25","BSSID":"7c:10:c9:d6:9f:10"}},"myVersionName":"1.1.03.20250821.DSPREAD.MDM.SERVICE","terminalDate":"20250821170816"}
2025-08-21 17:08:16.926  3681-3746  WebSocket               com.dspread.mdm.service              I  🔧 C0904 网络状态上送成功 (1)
2025-08-21 17:08:17.929  3681-3746  WebSocket               com.dspread.mdm.service              I  🔧 C0906 被动式上送: first_connection (被动: 6)
2025-08-21 17:08:17.933  3681-3746  Platform                com.dspread.mdm.service              W  ⚠️ SpVersionApi SP版本信息格式异常，未找到SP_VERSION标记
2025-08-21 17:08:17.936  3681-3746  Platform                com.dspread.mdm.service              D  🔧 SpVersionApi 从Settings获取SP版本: STD 582.1.238.8
2025-08-21 17:08:17.951  3681-3746  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0906","serialNo":"01610040202307060227","request_time":"1755767297942","request_id":"1755767297942C0906","version":"1","data":{"firmWareInfo":{"spfw":"STD 582.1.238.8"},"imei_1":"014250620000709","imei_2":"","wifi_mac":"52:c3:63:13:8c:ee","bt_mac":"","bsn":""},"myVersionName":"1.1.03.20250821.DSPREAD.MDM.SERVICE","terminalDate":"20250821170817"}
2025-08-21 17:08:17.954  3681-3746  WebSocket               com.dspread.mdm.service              I  🔧 C0906 设备信息上送成功 (1)
2025-08-21 17:08:17.957  3681-3746  WebSocket               com.dspread.mdm.service              I  🔧 C0109 被动式上送: first_connection (被动: 7)
2025-08-21 17:08:17.961  3681-3746  Platform                com.dspread.mdm.service              D  🔧 重新收集应用信息（强制刷新）
2025-08-21 17:08:18.020  3681-3746  AppManager              com.dspread.mdm.service              I  ℹ️ 应用信息获取完成: 总数120(系统119/用户1) 返回1个
2025-08-21 17:08:18.025  3681-3746  Platform                com.dspread.mdm.service              D  🔧 应用信息: 1 个应用
2025-08-21 17:08:18.123  3681-3746  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0109","serialNo":"01610040202307060227","request_time":"1755767298106","request_id":"1755767298106C0109","version":"1","data":{"apkInfo":[{"packName":"com.dspread.mdm.service","apkName":"service","versionCode":22,"versionName":"1.1.03.20250821.DSPREAD.MDM.SERVICE","updateDate":"2025-08-21 17:07:48"}],"locationInfo":{"gps":{"longitude":"-999.0","latitude":"-999.0","satelliteCount":0,"satelliteCountInUsed":0,"satelliteAverSNR":"0.00","distance":"0.0","lockStatus":"1","lockMeter":"200","proId":"","proName":""},"baseSite":{"MCC":"","MNC":"","CID":"-1","LAC":"-1","DBM":"-999","IMSI":"","ICCID":""},"wifi":{"SSID":"fubox_2.4G","IP":"**************","MAC":"52:C3:63:13:8C:EE","SSTH":"-25","BSSID":"7c:10:c9:d6:9f:10"},"address":"无法获取"},"hardwareInfo":[{"name":"CPU Usage","desc":"cpu:0.50"},{"name":"storage_usage_status","desc":" 5.43GB \/ 32.00GB ","state":"1"},{"name":"total_memory","desc":" 0.54GB \/ 1.86GB ","state":"1"},{"name":"androidVersion","desc":"11","state":"1"},{"name":"network_status","desc":"wifi","state":"1"},{"name":"OSVer","desc":"11","state":"1"}],"wifiOption":[{"SSID":"fubox_2.4G","SSTH":"-28"},{"SSID":"2206-5G","SSTH":"-32"},{"SSID":"fubox_5G","SSTH":"-40"},{"SSID":"2206","SSTH":"-41"},{"SSID":"2205_5G","SSTH":"-51"},{"SSID":"2205","SSTH":"-53"},{"SSID":"@Ruijie-1816","SSTH":"-56"},{"SSID":"2207","SSTH":"-67"},{"SSID":"DIRECT-C6-Mi All-in-One Inkjet","SSTH":"-70"},{"SSID":"DIRECT-CC-HP Laser 1188w","SSTH":"-71"},{"SSID":"2306","SSTH":"-72"},{"SSID":"2106","SSTH":"-72"},{"SSID":"2103","SSTH":"-77"},{"SSID":"2207-5G","SSTH":"-80"},{"SSID":"诺富特酒店2208","SSTH":"-81"},{"SSID":"dingjie888","SSTH":"-82"},{"SSID":"FJQS","SSTH":"-83"},{"SSID":"ChinaNet-SnSC","SSTH":"-85"},{"SSID":"2405","SSTH":"-86"},{"SSID":"2103_5G","SSTH":"-86"},{"SSID":"1621","SSTH":"-87"},{"SSID":"HUAWEI_B311_福","SSTH":"-92"},{"SSID":"2106-5G","SSTH":"-94"}],"sytemInfo":{"androidVersion":"11","buildNumber":"D60_Baseline_debug_20250708_ota_test","aspl":"2021-12-05"}},"myVersionName":"1.1.03.20250821.DSPREAD.MDM.SERVICE","terminalDate":"20250821170818"}
2025-08-21 17:08:18.125  3681-3746  WebSocket               com.dspread.mdm.service              I  🔧 C0109 终端信息上传完成 (1, trigger=first_connection)
2025-08-21 17:08:18.127  3681-3746  Common                  com.dspread.mdm.service              I  ✅ 初始化信息发送完成
2025-08-21 17:08:18.129  3681-3746  WebSocket               com.dspread.mdm.service              I  🔧 检查服务更新结果
2025-08-21 17:08:18.132  3681-3746  Task                    com.dspread.mdm.service              I  ℹ️ 本地任务列表长度: 1
2025-08-21 17:08:18.134  3681-3746  Task                    com.dspread.mdm.service              D  🔧 本地任务列表内容: [{"beginDate":"2024-08-19 05:15:24","taskType":"02","apkName":"OpenVPN for Android","endDate":"9999-12-31 23:59:59","appId":"1755580524415","packName":"de.blinkt.openvpn","versionName":"0.5.36a","taskId":"1755580524415","versionCode":"65","request_id":"1755580524415ST001","request_time":"1755580524415","silent_install":"","taskResult":"D02","lastUpdateTime":1755767291986}]
2025-08-21 17:08:18.137  3681-3746  Task                    com.dspread.mdm.service              I  ℹ️ 当前应用版本: versionCode=22, versionName=1.1.03.20250821.DSPREAD.MDM.SERVICE
2025-08-21 17:08:18.139  3681-3746  Task                    com.dspread.mdm.service              I  ℹ️ 任务列表数量: 1
2025-08-21 17:08:18.141  3681-3746  Task                    com.dspread.mdm.service              D  🔧 检查任务[0]: taskId=1755580524415, taskType=02, pkgName=de.blinkt.openvpn, versionCode=65, versionName=0.5.36a, taskResult=D02
2025-08-21 17:08:18.143  3681-3746  WebSocket               com.dspread.mdm.service              I  🔧 检查OS升级状态...
2025-08-21 17:08:18.148  3681-3746  Common                  com.dspread.mdm.service              D  🔧 OsUpdateStatusChecker WebSocket连接成功，检查OS升级状态
2025-08-21 17:08:18.150  3681-3746  Common                  com.dspread.mdm.service              D  🔧 OsUpdateStatusChecker 没有待检查的OS升级任务
2025-08-21 17:08:18.152  3681-3746  WebSocket               com.dspread.mdm.service              I  🔧 OS升级状态检查已触发
2025-08-21 17:08:18.155  3681-3746  WebSocket               com.dspread.mdm.service              I  🔧 首次连接初始化检查完成
2025-08-21 17:08:18.166  3681-3746  Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"request_time":"1755767294012","org_request_time":"1755767291919","org_request_id":"1755767291919C0108","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755767294012S0000","serialNo":"01610040202307060227"}
2025-08-21 17:08:18.169  3681-3746  WebSocket               com.dspread.mdm.service              I  🔧 收到响应消息: transCode=S0000, orgRequestId=1755767291919C0108, state=0, remark=
2025-08-21 17:08:18.171  3681-3746  WebSocket               com.dspread.mdm.service              I  🔧 处理任务结果响应: state=0
2025-08-21 17:08:18.173  3681-3746  WebSocket               com.dspread.mdm.service              I  🔧 任务结果上传成功
2025-08-21 17:08:18.187  3681-3746  Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"request_time":"1755767294608","org_request_time":"1755767291991","org_request_id":"1755767291991C0108","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755767294608S0000","serialNo":"01610040202307060227"}
2025-08-21 17:08:18.190  3681-3746  WebSocket               com.dspread.mdm.service              I  🔧 收到响应消息: transCode=S0000, orgRequestId=1755767291991C0108, state=0, remark=
2025-08-21 17:08:18.192  3681-3746  WebSocket               com.dspread.mdm.service              I  🔧 处理任务结果响应: state=0
2025-08-21 17:08:18.194  3681-3746  WebSocket               com.dspread.mdm.service              I  🔧 任务结果上传成功
2025-08-21 17:08:18.209  3681-3697  System                  com.dspread.mdm.service              W  A resource failed to call close. 
2025-08-21 17:08:18.577  3681-3746  Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"request_time":"1755767299423","org_request_time":"1755767297942","org_request_id":"1755767297942C0906","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755767299423S0000","serialNo":"01610040202307060227"}
2025-08-21 17:08:18.580  3681-3746  WebSocket               com.dspread.mdm.service              I  🔧 收到响应消息: transCode=S0000, orgRequestId=1755767297942C0906, state=0, remark=
2025-08-21 17:08:18.582  3681-3746  WebSocket               com.dspread.mdm.service              I  🔧 处理设备信息响应: state=0
2025-08-21 17:08:18.584  3681-3746  WebSocket               com.dspread.mdm.service              I  🔧 设备信息上传成功
2025-08-21 17:08:18.987  3681-3746  Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"request_time":"1755767300035","org_request_time":"1755767298106","org_request_id":"1755767298106C0109","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755767300035S0000","serialNo":"01610040202307060227"}
2025-08-21 17:08:18.990  3681-3746  WebSocket               com.dspread.mdm.service              I  🔧 收到响应消息: transCode=S0000, orgRequestId=1755767298106C0109, state=0, remark=
2025-08-21 17:08:18.992  3681-3746  WebSocket               com.dspread.mdm.service              I  🔧 终端信息上传成功
2025-08-21 17:08:41.824  3681-3747  WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 1 (第1个，待响应: 1)
2025-08-21 17:08:42.326  3681-3746  WebSocket               com.dspread.mdm.service              I  🔧 收到服务器 PONG 帧: 1 (待响应PING: 0)
2025-08-21 17:09:02.357  3681-3681  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 没有找到处理器: android.intent.action.TIME_TICK
2025-08-21 17:09:11.825  3681-3747  WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 2 (第2个，待响应: 1)
2025-08-21 17:09:12.432  3681-3746  WebSocket               com.dspread.mdm.service              I  🔧 收到服务器 PONG 帧: 2 (待响应PING: 0)
2025-08-21 17:09:41.826  3681-3747  WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 3 (第3个，待响应: 1)
2025-08-21 17:09:42.231  3681-3746  WebSocket               com.dspread.mdm.service              I  🔧 收到服务器 PONG 帧: 3 (待响应PING: 0)
2025-08-21 17:10:02.399  3681-3681  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 没有找到处理器: android.intent.action.TIME_TICK
2025-08-21 17:10:11.827  3681-3747  WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 4 (第4个，待响应: 1)
2025-08-21 17:10:12.336  3681-3746  WebSocket               com.dspread.mdm.service              I  🔧 收到服务器 PONG 帧: 4 (待响应PING: 0)
2025-08-21 17:10:41.828  3681-3747  WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 5 (第5个，待响应: 1)
2025-08-21 17:10:42.441  3681-3746  WebSocket               com.dspread.mdm.service              I  🔧 收到服务器 PONG 帧: 5 (待响应PING: 0)
2025-08-21 17:11:02.464  3681-3681  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 没有找到处理器: android.intent.action.TIME_TICK
2025-08-21 17:11:11.830  3681-3747  WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 6 (第6个，待响应: 1)
2025-08-21 17:11:12.343  3681-3746  WebSocket               com.dspread.mdm.service              I  🔧 收到服务器 PONG 帧: 6 (待响应PING: 0)
