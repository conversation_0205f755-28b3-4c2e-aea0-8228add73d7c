package com.bbpos.wiseapp.system.api;

import android.content.Context;
import android.os.Build;
import android.telephony.TelephonyManager;
import android.text.TextUtils;

import com.bbpos.wiseapp.tms.utils.ContextUtil;

import java.lang.reflect.Method;

import static android.content.Context.TELEPHONY_SERVICE;

public class DeviceInfoApi {
    //WisePOS 2G
    private final static String DEVICE_MODEL_WISEPOS_2G = "BBPOS_WSC2.0";

    //WisePOS 4G
    private final static String DEVICE_MODEL_WISEPOS_4G = "WiseManager";
    private final static String DEVICE_MODEL_WISEPOS_4G_NORMAL = "WisePOS 4G";
    private final static String DEVICE_MODEL_WISEPOS_4G_OLD = "Android";
    private final static String DEVICE_MODEL_WISEPOS_4G_TEMPORARY = "BBPOS_WSC4.0";

    //WisePOS Touch - P500
    private final static String DEVICE_MODEL_WISEPOS_5_BASEWIN = "P500";
    private final static String DEVICE_MODEL_WISEPOS_5 = "WisePOSTouch";
    private final static String DEVICE_MODEL_WISEPOS_5_NORMAL = "WisePOS Touch";
    private final static String DEVICE_MODEL_WISEPOS_5_NEW = "WisePOSE";
    private final static String DEVICE_MODEL_WISEPOS_5_NEW_NORMAL = "WisePOS E";

    //WisePOS Plus  - P1000
    private final static String DEVICE_MODEL_WISEPOS_5_PLUS_BASEWIN = "P1000";
    private final static String DEVICE_MODEL_WISEPOS_5_PLUS_SPECIAL = "S900";
    private final static String DEVICE_MODEL_WISEPOS_5_PLUS = "WisePOSPlus";
    private final static String DEVICE_MODEL_WISEPOS_5_PLUS_NORMAL = "WisePOS Plus";
    private final static String DEVICE_MODEL_WISEPOS_5_PLUS_NEO = "WisePOSNeo";
    private final static String DEVICE_MODEL_WISEPOS_5_PLUS_NEO_NORMAL = "WisePOS Neo";
    private final static String DEVICE_MODEL_WISEPOS_5_PLUS_NEW = "WisePOSE+";
    private final static String DEVICE_MODEL_WISEPOS_5_PLUS_NEW_NORMAL = "WisePOS E+";

    //WisePOS Pro   - 7MD
    private final static String DEVICE_MODEL_WISEPOS_PRO = "WisePOSPro";
    private final static String DEVICE_MODEL_WISEPOS_PRO_NORMAL = "WisePOS Pro";
    private final static String DEVICE_MODEL_WISEPOS_PRO_OLD = "7MD";

    //WSC6X
    private final static String DEVICE_MODEL_WISEPOS_TOUCH_GENERAL = "WTH1";

    private final static String DEVICE_MODEL_WISEPOS_TOUCH_ORIGINAL = "WSC6X";
    private final static String DEVICE_MODEL_WISEPOS_TOUCH = "WisePOS Touch";

    //WSS6X
    private final static String DEVICE_MODEL_WISEPOS_TOUCH_PLUS_ORIGINAL = "WSS6X";
    private final static String DEVICE_MODEL_WISEPOS_TOUCH_PLUS = "WisePOS Touch+";

    //WisePOS GO
    private final static String DEVICE_MODEL_WISEPOS_GO = "WisePosGo";
    private final static String DEVICE_MODEL_WISEPOS_GO_NORMAL = "WisePOS Go";
    private final static String DEVICE_MODEL_WISEPOS_GO_TEMPORARY = "rk3326_go";

    //WisePos LE
    private final static String DEVICE_MODEL_WISEPOS_LE = "WisePOS LE";
    private final static String DEVICE_MODEL_WISEPOS_LE_GENERAL = "WisePos LE";
    private final static String DEVICE_MODEL_WISEPOS_LE_ORIGINAL = "WLE1";
    //WisePos LP
    private final static String DEVICE_MODEL_WISEPOS_LP = "WisePOS LP";
    private final static String DEVICE_MODEL_WISEPOS_LP_GENERAL = "WisePos LP";
    private final static String DEVICE_MODEL_WISEPOS_LP_ORIGINAL = "WLP1";

    private static DeviceInfoApi mDeviceInfoApi = null;
    public static DeviceInfoApi getIntance() {
        if (mDeviceInfoApi == null) {
            mDeviceInfoApi = new DeviceInfoApi();
        }

        return mDeviceInfoApi;
    }

    public boolean isWisePos4G() {
        if (getModel().contains(DEVICE_MODEL_WISEPOS_4G.toLowerCase()) ||
            getModel().contains(DEVICE_MODEL_WISEPOS_4G_NORMAL.toLowerCase()) ||
            getModel().contains(DEVICE_MODEL_WISEPOS_4G_OLD.toLowerCase()) ||
            getModel().contains(DEVICE_MODEL_WISEPOS_4G_TEMPORARY.toLowerCase()) ||
            getModel().contains(DEVICE_MODEL_WISEPOS_2G.toLowerCase())) {
            return true;
        }

        return false;
    }
	
	public boolean isWisePos5() {
        if (getModel().contains(DEVICE_MODEL_WISEPOS_5_BASEWIN.toLowerCase()) ||
            getModel().contains(DEVICE_MODEL_WISEPOS_5.toLowerCase()) ||
            getModel().contains(DEVICE_MODEL_WISEPOS_5_NORMAL.toLowerCase()) ||
            getModel().contains(DEVICE_MODEL_WISEPOS_5_NEW.toLowerCase()) ||
            getModel().contains(DEVICE_MODEL_WISEPOS_5_NEW_NORMAL.toLowerCase())) {
            try {
                Class<?> c =Class.forName("android.os.SystemProperties");
                Method get =c.getMethod("get", String.class);
                String custVersion = (String)get.invoke(c, "ro.build.sw.version");
                if (custVersion.contains("WSC5X")) {
                    return true;
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        return false;
    }

    public boolean isWisePos5Plus() {
        if (getModel().contains(DEVICE_MODEL_WISEPOS_5_PLUS_BASEWIN.toLowerCase()) ||
            getModel().contains(DEVICE_MODEL_WISEPOS_5_PLUS_SPECIAL.toLowerCase()) ||
            getModel().contains(DEVICE_MODEL_WISEPOS_5_PLUS.toLowerCase()) ||
            getModel().contains(DEVICE_MODEL_WISEPOS_5_PLUS_NORMAL.toLowerCase()) ||
            getModel().contains(DEVICE_MODEL_WISEPOS_5_PLUS_NEO.toLowerCase()) ||
            getModel().contains(DEVICE_MODEL_WISEPOS_5_PLUS_NEO_NORMAL.toLowerCase()) ||
            getModel().contains(DEVICE_MODEL_WISEPOS_5_PLUS_NEW.toLowerCase()) ||
            getModel().contains(DEVICE_MODEL_WISEPOS_5_PLUS_NEW_NORMAL.toLowerCase())) {
            return true;
        }

        return false;
    }

    public boolean isWisePosTouch() {
        if (getModel().contains(DEVICE_MODEL_WISEPOS_TOUCH.toLowerCase())
         || getModel().contains(DEVICE_MODEL_WISEPOS_TOUCH_ORIGINAL.toLowerCase())
         || getModel().contains(DEVICE_MODEL_WISEPOS_TOUCH_GENERAL.toLowerCase())) {
            try {
                Class<?> c =Class.forName("android.os.SystemProperties");
                Method get =c.getMethod("get", String.class);
                String custVersion = (String)get.invoke(c, "ro.build.bbpos_rom_version");
                if (custVersion.contains("WSC6X") || custVersion.contains("WTH1") || custVersion.contains("WTP1")) {
                    return true;
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        return false;
    }

    public boolean isWisePosTouchPlus() {
        if (getModel().contains(DEVICE_MODEL_WISEPOS_TOUCH_PLUS.toLowerCase())
         || getModel().contains(DEVICE_MODEL_WISEPOS_TOUCH_PLUS_ORIGINAL.toLowerCase())
         || getModel().contains(DEVICE_MODEL_WISEPOS_TOUCH_GENERAL.toLowerCase())) {
            return true;
        }

        return false;
    }

    public boolean isWisePosPro() {
        if (getModel().contains(DEVICE_MODEL_WISEPOS_PRO.toLowerCase()) ||
            getModel().contains(DEVICE_MODEL_WISEPOS_PRO_NORMAL.toLowerCase()) ||
            getModel().contains(DEVICE_MODEL_WISEPOS_PRO_OLD.toLowerCase())) {
            return true;
        }

        return false;
    }

    public boolean isShopifyDevice(){
        if ("WTH11".equals(SysIntermediateApi.getIntance().getProp("persist.bbpos.product_id"))) {
            return true;
        }
        return false;
    }

    public boolean isWisePosGo() {
        if (getModel().contains(DEVICE_MODEL_WISEPOS_GO.toLowerCase()) ||
                getModel().contains(DEVICE_MODEL_WISEPOS_GO_NORMAL.toLowerCase()) ||
                getModel().contains(DEVICE_MODEL_WISEPOS_GO_TEMPORARY.toLowerCase())) {
            return true;
        }

        return false;
    }

    public String getWisePOSModel() {
        if (isWisePos4G()) {
            return "WisePOS 4G";
        } else if (isWisePosPro()) {
            return "WisePOS PRO";
        } else if (isWisePos5()) {
            return "WisePOS E";
        } else if (isWisePos5Plus()) {
            return "WisePOS E+";
        } else if (isWisePosTouch()) {
            return "WisePOS Touch";
        } else if (isWisePosTouchPlus()) {
            return "WisePOS Touch+";
        } else if (isWisePosGo()) {
            return "WisePOS GO";
        } else if (isWisePosLE()) {
            return "WisePOS LE";
        } else if (isWisePosLP()) {
            return "WisePOS LP";
        }

        return "WisePOS UNKNOWN";
    }

    public boolean isWisePosLE() {
        if (getModel().contains(DEVICE_MODEL_WISEPOS_LE.toLowerCase())
                || getModel().contains(DEVICE_MODEL_WISEPOS_LE_ORIGINAL.toLowerCase())
                || getModel().contains(DEVICE_MODEL_WISEPOS_LE_GENERAL.toLowerCase())) {
            try {
                Class<?> c =Class.forName("android.os.SystemProperties");
                Method get =c.getMethod("get", String.class);
                String custVersion = (String)get.invoke(c, "ro.build.bbpos_rom_version");
                if (custVersion.contains("WLX1X")
                        || custVersion.contains("WLE1X")
                        || custVersion.contains("WisePOS LE")
                        || custVersion.contains("WisePos LE")) {
                    return true;
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        return false;
    }

    public boolean isWisePosLP() {
        if (getModel().contains(DEVICE_MODEL_WISEPOS_LP.toLowerCase())
                || getModel().contains(DEVICE_MODEL_WISEPOS_LP_ORIGINAL.toLowerCase())
                || getModel().contains(DEVICE_MODEL_WISEPOS_LP_GENERAL.toLowerCase())) {
            try {
                Class<?> c =Class.forName("android.os.SystemProperties");
                Method get =c.getMethod("get", String.class);
                String custVersion = (String)get.invoke(c, "ro.build.bbpos_rom_version");
                if (custVersion.contains("WLX1X")
                        || custVersion.contains("WLP1X")
                        || custVersion.contains("WisePOS LP")
                        || custVersion.contains("WisePos LP")) {
                    return true;
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        return false;
    }

    public String getSerialNumber() {
        String serialNo = null;
        String ro_serialNo = null;
        String dsn_serialNo = null;

        try {
            Class<?> c =Class.forName("android.os.SystemProperties");
            Method get =c.getMethod("get", String.class);
            ro_serialNo = (String)get.invoke(c, "ro.serialno");
            dsn_serialNo = (String)get.invoke(c, "sys.dsn");
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                serialNo = Build.getSerial();
            } else {
                serialNo = Build.SERIAL;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        if (isWisePos4G()) {
            serialNo = ro_serialNo;
        } else if (isWisePos5Plus() || isWisePos5()){
            if (TextUtils.isEmpty(serialNo)) {
                serialNo = dsn_serialNo;
            }
        } else {
            if (TextUtils.isEmpty(serialNo)) {
                serialNo = ro_serialNo;
            }
        }

        return serialNo;
    }

    // 获取系统硬件制造商
    private String getManufacturer() {
        return Build.MANUFACTURER;
    }

    // 获取系统硬件版本
    private String getHardwareVer() {
        return Build.HARDWARE;
    }

    // 获取系统型号
    public String getModel() {
        return Build.MODEL.toLowerCase();
    }

    // 获取系统版本
    public String getVersion() {
        return Build.VERSION.RELEASE;
    }

    //獲取IMIE
    public String getIMEI(){
        TelephonyManager tm = (TelephonyManager) ContextUtil.getInstance().getSystemService(TELEPHONY_SERVICE);
        return tm.getDeviceId();
    }
    // 获取sdk版本
    private String getSdkVersion() {
        return Build.VERSION.SDK;
    }

    public String getCustomVersion(){
        String cusVersion = "unknown";
        try {
            Class<?> c =Class.forName("android.os.SystemProperties");
            Method get =c.getMethod("get", String.class);
            if (isWisePos5Plus() || isWisePos5() || isWisePosGo()){
                cusVersion = (String)get.invoke(c, "ro.build.sw.version");
            } else if (isWisePosTouch() || isWisePosTouchPlus() || isWisePosLE() || isWisePosLP()) {
                cusVersion = (String)get.invoke(c, "ro.build.bbpos_rom_version");
            } else {
                cusVersion = (String)get.invoke(c, "ro.build.custom.version");
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return cusVersion;
    }

    /**
     * 获取系统校准信息数据
     * SystemProperties.get("vendor.gsm.serial");
     * @return
     */
    public String getGMSSerial(){
        String gmsSerial = "";
        try {
            Class<?> c =Class.forName("android.os.SystemProperties");
            Method get =c.getMethod("get", String.class);
            if (isWisePosPro()){
                gmsSerial = (String)get.invoke(c, "vendor.gsm.serial");
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return gmsSerial;
    }

    /**
     * 获取系统校准信息数据
     * SystemProperties.get("vendor.gsm.mb.serial");
     * @return
     */
    public String getBSN(){
        String gmsSerial = "";
        try {
            Class<?> c =Class.forName("android.os.SystemProperties");
            Method get =c.getMethod("get", String.class);
            if (isWisePosPro()){
                gmsSerial = (String)get.invoke(c, "vendor.gsm.mb.serial");
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return gmsSerial;
    }

    /**
     * 获取手机IMEI
     * @param context
     * @param slotId 卡槽位置，0 ，1
     * @return
     */
    public static final String getIMEI(Context context, int slotId) {
        try {
            //实例化TelephonyManager对象
            TelephonyManager telephonyManager = (TelephonyManager) context.getSystemService(Context.TELEPHONY_SERVICE);
            //获取IMEI号
            Method method = telephonyManager.getClass().getMethod("getImei", int.class);
            //获取IMEI号
            String imei = (String) method.invoke(telephonyManager, slotId);
            if (imei == null) {
                imei = "";
            }
            return imei;
        } catch (Exception e) {
            e.printStackTrace();
            return "";
        }
    }

    /*
    7MD 系統屬性：
    ro.sys.battery.burn=20  //不充电需大于20%
    ro.sys.battery.charger.burn=15 //充电時,需大于15%,
    ro.sys.lowbattery.warning=15  //低電量警告*/
    public String getOSUpdateLowBatLevel(){
        String level = "";
        try {
            level = SysIntermediateApi.getIntance().getProp("ro.sys.battery.burn");
        } catch (Exception e) {
            e.printStackTrace();
        }
        return level;
    }

    public String getOSUpdateLowBatLevelCharging(){
        String level = "";
        try {
            level = SysIntermediateApi.getIntance().getProp("ro.sys.battery.charger.burn");
        } catch (Exception e) {
            e.printStackTrace();
        }
        return level;
    }

    public String getSystemLowBatLevel(){
        String level = "";
        try {
            level = SysIntermediateApi.getIntance().getProp("ro.sys.lowbattery.warning");
        } catch (Exception e) {
            e.printStackTrace();
        }
        return level;
    }
}