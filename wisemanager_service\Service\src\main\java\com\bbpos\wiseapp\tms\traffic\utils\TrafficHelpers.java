package com.bbpos.wiseapp.tms.traffic.utils;

import android.annotation.SuppressLint;
import android.app.AlarmManager;
import android.app.PendingIntent;
import android.content.Context;
import android.content.Intent;
import android.util.Log;

import com.bbpos.wiseapp.logger.BBLog;
import com.bbpos.wiseapp.system.api.ServiceApi;
import com.bbpos.wiseapp.tms.traffic.DataCollectService;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;

public class TrafficHelpers {
	private static String TAG = "TrafficHelpers";
	/**定时器类型 每日启动*/
	public static final int CLEAR_REQUEST_CODE = 0;
	/**定时器类型 间隔执行*/
	public static final int NORMAL_REQUEST_CODE = 1;
	
	public static void startDataCollectService(Context context){
		Intent intentTmp = new Intent(context, DataCollectService.class);
    	intentTmp.putExtra(DataCollectService.EXE_TYPE, DataCollectService.EXE_TYPE_NORMAL);
    	ServiceApi.getIntance().startService(intentTmp);
	}
	
	/**启动数据收集定时器*/
	public static void startDataCollectTimer(Context context,long interval){
		BBLog.v(BBLog.TAG, "startDataCollectTimer in:"+interval);
		// 定时轮询Intent
        Intent i = new Intent(context, DataCollectService.class);
        i.putExtra(DataCollectService.EXE_TYPE, DataCollectService.EXE_TYPE_NORMAL);
        PendingIntent pi = PendingIntent.getService(context, NORMAL_REQUEST_CODE, i,PendingIntent.FLAG_UPDATE_CURRENT);

        // 取消旧的轮询定时
        AlarmManager am = (AlarmManager) context.getSystemService(Context.ALARM_SERVICE);
        am.cancel(pi);
        am.setRepeating(AlarmManager.RTC_WAKEUP, System.currentTimeMillis(), interval , pi);
	}
	
	/**启动数据清分定时器，每日0点触发*/
	@SuppressLint("SimpleDateFormat")
	public static void startDataCollectClear(Context context){
		// 定时轮询Intent
        Intent i = new Intent(context, DataCollectService.class);
        i.putExtra(DataCollectService.EXE_TYPE, DataCollectService.EXE_TYPE_CLEAR);
        PendingIntent pi = PendingIntent.getService(context, CLEAR_REQUEST_CODE, i,PendingIntent.FLAG_UPDATE_CURRENT);

        // 取消旧的轮询定时
        AlarmManager am = (AlarmManager) context.getSystemService(Context.ALARM_SERVICE);
        am.cancel(pi);
        
        //计算下一个0点的毫秒数
        long DAY = 24 * 60 * 60 * 1000;
        Calendar calendar = Calendar.getInstance();  
        calendar.setTimeInMillis(System.currentTimeMillis());
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        //只保留日期
        try {
        	Date newDate = sdf.parse(sdf.format(calendar.getTime()));
			calendar.setTime(newDate);
		} catch (ParseException e) {
			e.printStackTrace();
		}
        //日期+1
        calendar.add(Calendar.DATE, 1);
        long firstTime = calendar.getTimeInMillis();

        am.setRepeating(AlarmManager.RTC_WAKEUP, firstTime, DAY , pi);
	}
}
