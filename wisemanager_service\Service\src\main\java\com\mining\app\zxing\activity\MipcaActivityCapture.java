package com.mining.app.zxing.activity;

import android.app.Activity;
import android.app.AlertDialog;
import android.app.Dialog;
import android.content.ComponentName;
import android.content.DialogInterface;
import android.content.Intent;
import android.content.res.AssetFileDescriptor;
import android.graphics.Bitmap;
import android.graphics.Color;
import android.hardware.Camera;
import android.media.AudioManager;
import android.media.MediaPlayer;
import android.media.MediaPlayer.OnCompletionListener;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.Message;
import android.os.Vibrator;
import android.text.TextUtils;
import android.view.KeyEvent;
import android.view.SurfaceHolder;
import android.view.SurfaceHolder.Callback;
import android.view.SurfaceView;
import android.view.View;
import android.view.WindowManager;
import android.widget.LinearLayout;
import android.widget.TextView;
import android.widget.Toast;

import com.bbpos.wiseapp.logger.BBLog;
import com.bbpos.wiseapp.provisioning.ProvisionService;
import com.bbpos.wiseapp.provisioning.ProvisionTimer;
import com.bbpos.wiseapp.service.R;
import com.bbpos.wiseapp.service.bbdevice.BBDeviceManager;
import com.bbpos.wiseapp.service.common.RequestPermission;
import com.bbpos.wiseapp.service.common.UsualData;
import com.bbpos.wiseapp.system.api.DeviceInfoApi;
import com.bbpos.wiseapp.tms.utils.Constants;
import com.bbpos.wiseapp.tms.utils.ContextUtil;
import com.bbpos.wiseapp.utils.FileUtils;
import com.bbpos.wiseapp.utils.QRCodeDialogUtils;
import com.bbpos.wiseapp.utils.QRCodeDialogUtils.StepEnum;
import com.bbpos.wiseapp.utils.SharedPreferencesUtils;
import com.bbpos.wiseapp.utils.SystemUtils;
import com.bbpos.wiseapp.websocket.Constant;
import com.google.zxing.BarcodeFormat;
import com.google.zxing.Result;
import com.mining.app.zxing.camera.CameraManager;
import com.mining.app.zxing.decoding.CaptureActivityHandler;
import com.mining.app.zxing.decoding.InactivityTimer;
import com.mining.app.zxing.view.ViewfinderView;

import org.json.JSONException;
import org.json.JSONObject;

import java.io.IOException;
import java.util.Vector;

/**
 * Initial the camera
 * <AUTHOR>
 */
public class MipcaActivityCapture extends Activity implements Callback, View.OnClickListener {
	final public static int SCANNIN_REQUEST_CODE = 0;
	final public static int SHOW_DIALOG = 100;
	final public static int SHOW_EGINX_SETTING = 101;

	private CaptureActivityHandler handler;
	private ViewfinderView viewfinderView;
	private boolean hasSurface;
	private Vector<BarcodeFormat> decodeFormats;
	private String characterSet;
	private InactivityTimer inactivityTimer;
	private MediaPlayer mediaPlayer;
	private boolean playBeep;
	private static final float BEEP_VOLUME = 0.10f;
	private boolean vibrate;

	private Dialog dialog;
	private String resultString;
	private LinearLayout layout_close;

	private static boolean SCAN_CODE = true;

	private Handler mHandler = new Handler() {
		@Override
		public void handleMessage(Message msg) {
			super.handleMessage(msg);
			switch (msg.what) {
				case SHOW_DIALOG:
                    BBLog.e(Constants.TAG, "handle, SHOW_DIALOG");
					dialog = new Dialog(MipcaActivityCapture.this, R.style.dialog_style_ex);
					dialog.setContentView(R.layout.dialog_confirm);
					TextView tv_title = (TextView) dialog.findViewById(R.id.tv_title);
					tv_title.setText(getString(R.string.prompt));
					TextView tv_content = (TextView) dialog.findViewById(R.id.tv_content);
					tv_content.setText(getString(R.string.qrcode_invalid));
					dialog.setCanceledOnTouchOutside(false);
					TextView tv_cancel = (TextView) dialog.findViewById(R.id.tv_cancel);
					tv_cancel.setVisibility(View.GONE);
					TextView tv_install = (TextView) dialog.findViewById(R.id.tv_install);
					tv_install.setText(R.string.confirm);
					tv_install.setOnClickListener(
							new View.OnClickListener() {
								@Override
								public void onClick(View v) {
									continuePreview();
									dialog.dismiss();
								}
							});
					dialog.show();
					break;
				case SHOW_EGINX_SETTING:
					BBLog.e(Constants.TAG, "handle, SHOW_DIALOG");
					dialog = new Dialog(MipcaActivityCapture.this, R.style.dialog_style_ex);
					dialog.setContentView(R.layout.dialog_confirm);
					TextView tv_title_ex = (TextView) dialog.findViewById(R.id.tv_title);
					tv_title_ex.setText(getString(R.string.prompt));
					TextView tv_content_ex = (TextView) dialog.findViewById(R.id.tv_content);
					tv_content_ex.setText("NGINX ENABLE: " + Constants.EGINX_PROXY_ENABLE + "\nNGINX IP: " + Constants.EGINX_PROXY_IP);
					dialog.setCanceledOnTouchOutside(false);
					TextView tv_cancel_ex = (TextView) dialog.findViewById(R.id.tv_cancel);
					tv_cancel_ex.setVisibility(View.GONE);
					TextView tv_install_ex = (TextView) dialog.findViewById(R.id.tv_install);
					tv_install_ex.setText(R.string.confirm);
					tv_install_ex.setOnClickListener(
							new View.OnClickListener() {
								@Override
								public void onClick(View v) {
									continuePreview();
									dialog.dismiss();
								}
							});
					dialog.show();
					break;
			}
		}
	};

	/** Called when the activity is first created. */
	@Override
	public void onCreate(Bundle savedInstanceState) {
		super.onCreate(savedInstanceState);
		setContentView(R.layout.activity_capture);

		initView();

		CameraManager.init(getApplication());
		viewfinderView = (ViewfinderView) findViewById(R.id.viewfinder_view);
		hasSurface = false;
		inactivityTimer = new InactivityTimer(this);
	}

	private void initView() {
		TextView title = (TextView) findViewById(R.id.toolbar_title_tv);
		title.setText(getString(R.string.wise_scan));

		layout_close = (LinearLayout) findViewById(R.id.toolbar_left_btn);
		layout_close.setOnClickListener(this);
	}

	@Override
	protected void onResume() {
		super.onResume();
		if (SCAN_CODE) {
			BBLog.d(BBLog.TAG, "Start Scan!");
			SurfaceView surfaceView = (SurfaceView) findViewById(R.id.preview_view);
			SurfaceHolder surfaceHolder = surfaceView.getHolder();
			if (hasSurface) {
				initCamera(surfaceHolder);
			} else {
			surfaceHolder.addCallback(this);
				surfaceHolder.setType(SurfaceHolder.SURFACE_TYPE_PUSH_BUFFERS);
			}
			decodeFormats = null;
			characterSet = null;

			playBeep = true;
			AudioManager audioService = (AudioManager) getSystemService(AUDIO_SERVICE);
			if (audioService.getRingerMode() != AudioManager.RINGER_MODE_NORMAL) {
				playBeep = false;
			}
			initBeepSound();
			vibrate = true;
		}
		mHandler.post(new Runnable() {
			@Override
			public void run() {
				final String[] items = getResources().getStringArray(R.array.item);
				new AlertDialog.Builder(MipcaActivityCapture.this)
						.setTitle("Options")
						.setIcon(getDrawable(R.drawable.ic_launcher_wisescan))
						.setOnDismissListener(new DialogInterface.OnDismissListener() {
							@Override
							public void onDismiss(DialogInterface dialog) {
								if (Camera.getNumberOfCameras() == 0) {
									finish();
								}
							}
						})
						.setItems(items, new DialogInterface.OnClickListener() {
							public void onClick(DialogInterface dialog, int which) {
								if (which == 0) {

								} else if (which == 1) {
									final Dialog qr_dialog = QRCodeDialogUtils.showDialogByStep(StepEnum.LABEL_PRINTING_SN);
									qr_dialog.setOnDismissListener(new DialogInterface.OnDismissListener() {
										@Override
										public void onDismiss(DialogInterface dialog) {
											finish();
										}
									});
									qr_dialog.findViewById(R.id.qrCodeImg).setOnClickListener(new View.OnClickListener() {
										@Override
										public void onClick(View v) {
											dialog.dismiss();
										}
									});
									qr_dialog.getWindow().setType(WindowManager.LayoutParams.TYPE_SYSTEM_ALERT);//这句话是关键
									qr_dialog.show();
									finish();
								} else if (which == 2) {
									final Dialog qr_dialog = QRCodeDialogUtils.showDialogByStep(StepEnum.LASER_MARKING_QRCODE);
									qr_dialog.setOnDismissListener(new DialogInterface.OnDismissListener() {
										@Override
										public void onDismiss(DialogInterface dialog) {
											finish();
										}
									});
									qr_dialog.getWindow().setType(WindowManager.LayoutParams.TYPE_SYSTEM_ALERT);//这句话是关键
									qr_dialog.show();
									finish();
								} else if (which == 3) {
									BBDeviceManager.getInstance().startSetDeviceName();
									finish();
								}
							}
						}).show();
			}
		});
	}

	@Override
	protected void onPause() {
		super.onPause();
		if (handler != null) {
			handler.quitSynchronously();
			handler = null;
		}
		CameraManager.get().closeDriver();
	}

	@Override
	protected void onDestroy() {
		inactivityTimer.shutdown();
		super.onDestroy();
	}
	
	/**
	 * 处理扫描结果
	 * @param result
	 * @param barcode
	 */
	public void handleDecode(Result result, Bitmap barcode) {
		BBLog.e(BBLog.TAG, "handleDecode: result ="+result.getText() );
		inactivityTimer.onActivity();
		playBeepSoundAndVibrate();
		resultString = result.getText();
		if (resultString.equals("")) {
			Toast.makeText(MipcaActivityCapture.this, "Scan failed!", Toast.LENGTH_SHORT).show();
			return;
		}else {
			//增加 ROM信息结果比对操作
			//sysInfoVerify,imie ,buildnumber 、gms-serial length<10 ,model ,sn
			if (resultString.contains("sysInfoVerify") || resultString.contains("nginx")){
				promptForSysVerifyQrCode(barcode);
			}else {
				promptForQrCode(barcode);
			}
		}
	}

	//实现连续扫码！
	private void continuePreview(){
		SurfaceView surfaceView = (SurfaceView) findViewById(R.id.preview_view);
		SurfaceHolder surfaceHolder = surfaceView.getHolder();
		initCamera(surfaceHolder);
		if (handler != null){
			handler.restartPreviewAndDecode();
		}
	}

	private void promptForSysVerifyQrCode(final Bitmap barcode){
		if (resultString.contains("nginx")) {
			try {
				JSONObject json = new JSONObject(resultString);
				if (json.has("nginx_enable") && json.has("nginx_ip")) {
					if ("1".equals(json.getString("nginx_enable"))) {
						Constants.EGINX_PROXY_ENABLE = true;
						SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_NGINX_ENABLE, true);
					} else {
						Constants.EGINX_PROXY_ENABLE = false;
						SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_NGINX_ENABLE, false);
					}

					Constants.EGINX_PROXY_IP = json.getString("nginx_ip");
					SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_NGINX_IP, Constants.EGINX_PROXY_IP);
					FileUtils.writeFile(FileUtils.getWiseAppConfigPath(), "nginx_info.txt", resultString, false);
//					ProvisionTimer.startProvisionTimer(ContextUtil.getInstance(), "0");
				}
			} catch (JSONException e) {
				mHandler.sendEmptyMessage(SHOW_DIALOG);
				e.printStackTrace();
				return;
			}
			mHandler.sendEmptyMessage(SHOW_EGINX_SETTING);
			return;
		}

		if (dialog != null) {
			dialog.dismiss();
			dialog = null;
		}
		dialog = new Dialog(MipcaActivityCapture.this, R.style.dialog_style_ex);
		dialog.setContentView(R.layout.dialog_sysinfo_confirm);
		TextView tv_title = (TextView) dialog.findViewById(R.id.tv_title);
		tv_title.setText(getString(R.string.prompt_sysInfo_verfiy));
		TextView tv_rom_version = (TextView) dialog.findViewById(R.id.tv_rom_version);
		TextView tv_rom_version_result = (TextView) dialog.findViewById(R.id.tv_rom_version_result);
		TextView tv_imie = (TextView) dialog.findViewById(R.id.tv_imie);
		TextView tv_imie_result = (TextView) dialog.findViewById(R.id.tv_imie_result);
		TextView tv_gms_serial = (TextView) dialog.findViewById(R.id.tv_gms_serial);
		TextView tv_gms_serial_result = (TextView) dialog.findViewById(R.id.tv_gms_serial_result);
		TextView tv_model = (TextView) dialog.findViewById(R.id.tv_model);
		TextView tv_sn = (TextView) dialog.findViewById(R.id.tv_sn);
		TextView tv_sn_result = (TextView) dialog.findViewById(R.id.tv_sn_result);
		TextView tv_result = (TextView) dialog.findViewById(R.id.tv_result);
		TextView tv_sure = (TextView) dialog.findViewById(R.id.tv_sure);

		BBLog.i(Constants.TAG, "WiseScan Result = " + resultString);
		//sysInfoVerify,imie ,buildnumber 、gms-serial length<10 ,model ,sn
		JSONObject jsonObject = null;
		boolean result = false;
		try {
			jsonObject = new JSONObject(resultString);
			if (jsonObject != null && jsonObject.has("sysInfoVerify")) {
				String buildNumber = jsonObject.has("buildNumber") ? jsonObject.getString("buildNumber"): "";
				boolean IMIEChecked ,buildNameChecked, GMSSerialChecked, serialNumberChecked;
				BBLog.e(BBLog.TAG, "promptForSysVerifyQrCode: buildNumber = "+buildNumber );
				BBLog.e(BBLog.TAG, "promptForSysVerifyQrCode: local buildNumber = "+DeviceInfoApi.getIntance().getCustomVersion() );

				tv_rom_version.setText("QR CODE: \r\n    "+ (TextUtils.isEmpty(buildNumber) ? "" : buildNumber)
													+"\r\nLOCAL: \r\n    "+(TextUtils.isEmpty(DeviceInfoApi.getIntance().getCustomVersion()) ? "" : DeviceInfoApi.getIntance().getCustomVersion()));
				if (!TextUtils.isEmpty(buildNumber) && buildNumber.trim().equals(DeviceInfoApi.getIntance().getCustomVersion().trim())){
					tv_rom_version_result.setText("PASS");
					tv_rom_version_result.setTextColor(Color.BLACK);
					buildNameChecked = true;
				}else {
					tv_rom_version_result.setText("FAIL");
					tv_rom_version_result.setTextColor(Color.RED);
					buildNameChecked= false;
				}

				IMIEChecked = !TextUtils.isEmpty(DeviceInfoApi.getIntance().getIMEI());
				tv_imie.setText("IMEI: "+ (IMIEChecked ? DeviceInfoApi.getIntance().getIMEI():""));
				tv_imie_result.setText(IMIEChecked ? "PASS" : "FAIL");
				tv_imie_result.setTextColor(IMIEChecked ? Color.BLACK : Color.RED );

				String gmsSerialValue= DeviceInfoApi.getIntance().getGMSSerial();
				BBLog.e(BBLog.TAG, "promptForSysVerifyQrCode: gmsSerialValue = "+gmsSerialValue );
				GMSSerialChecked = !TextUtils.isEmpty(gmsSerialValue) /*&& gmsSerialValue.length() <= 10*/;
				tv_gms_serial_result.setText(GMSSerialChecked ? "PASS" : "FAIL");
				tv_gms_serial_result.setTextColor(GMSSerialChecked ? Color.BLACK : Color.RED);

				String serial = DeviceInfoApi.getIntance().getSerialNumber();
				serialNumberChecked = !TextUtils.isEmpty(serial) && serial.length()==15;
				tv_sn.setText("SN : "+ (TextUtils.isEmpty(DeviceInfoApi.getIntance().getSerialNumber()) ? "" : DeviceInfoApi.getIntance().getSerialNumber()));
				tv_sn_result.setText(serialNumberChecked ? "PASS" : "FAIL");
				tv_sn_result.setTextColor(serialNumberChecked ? Color.BLACK : Color.RED);

				tv_model.setText("MODEL: "+ (TextUtils.isEmpty(DeviceInfoApi.getIntance().getModel()) ? "": DeviceInfoApi.getIntance().getModel().toUpperCase()) + "("+SystemUtils.get7MDModel(ContextUtil.getInstance())+")");

				result = buildNameChecked && IMIEChecked && GMSSerialChecked && serialNumberChecked;
				BBLog.e(BBLog.TAG, "promptForSysVerifyQrCode: IMIEChecked = "+IMIEChecked+", buildNameChecked ="+buildNameChecked+", GMSSerialChecked = "+ GMSSerialChecked +", serialNumberChecked = "+ serialNumberChecked);
				if (result){
					tv_result.setVisibility(View.GONE);
					tv_sure.setVisibility(View.VISIBLE);
				}else {
					tv_result.setVisibility(View.VISIBLE);
					tv_sure.setVisibility(View.GONE);
				}
			}
		} catch (JSONException e) {
			mHandler.sendEmptyMessage(SHOW_DIALOG);
			e.printStackTrace();
			return;
		}

		dialog.setCanceledOnTouchOutside(false);
		dialog.findViewById(R.id.tv_sure).setOnClickListener(
				new View.OnClickListener() {
					@Override
					public void onClick(View v) {
						finish();
					}
				});
		boolean finalResult = result;
		dialog.setOnKeyListener(new DialogInterface.OnKeyListener() {
			@Override
			public boolean onKey(DialogInterface dialog, int keyCode, KeyEvent event) {
				if (keyCode == KeyEvent.KEYCODE_BACK && event.getRepeatCount() == 0){
					if (!finalResult){
						Toast.makeText(ContextUtil.getInstance(),getString(R.string.system_info_verify_fail_back),Toast.LENGTH_LONG).show();
						return true;
					}
				}
				return false;
			}
		});
		dialog.show();
	}

	private void promptForQrCode(final Bitmap barcode){
		if (dialog != null) {
			dialog.dismiss();
			dialog = null;
		}
		dialog = new Dialog(MipcaActivityCapture.this, R.style.dialog_style_ex);
		dialog.setContentView(R.layout.dialog_confirm);
		TextView tv_title = (TextView) dialog.findViewById(R.id.tv_title);
		tv_title.setText(getString(R.string.prompt_install));
		TextView tv_content = (TextView) dialog.findViewById(R.id.tv_content);
		BBLog.i(Constants.TAG, "WiseScan Result = " + resultString);
		String cid = "";
		String soid = "";
		JSONObject jsonObject = null;
		try {
			jsonObject = new JSONObject(resultString);
//			if (jsonObject != null && jsonObject.has("CID")) {
			cid = jsonObject.getString("CID");
			soid = jsonObject.getString("SOID");
//			}
		} catch (JSONException e) {
			mHandler.sendEmptyMessage(SHOW_DIALOG);
			e.printStackTrace();
			return;
		}
		tv_content.setText(getString(R.string.prompt_install_content1) + " " + cid + "?");
		dialog.setCanceledOnTouchOutside(false);

		dialog.findViewById(R.id.tv_cancel).setOnClickListener(
				new View.OnClickListener() {
					@Override
					public void onClick(View v) {
						continuePreview();
						dialog.dismiss();
					}
				});

		dialog.findViewById(R.id.tv_install).setOnClickListener(
				new View.OnClickListener() {
					@Override
					public void onClick(View v) {
//						Intent resultIntent = new Intent();
//						Bundle bundle = new Bundle();
//						bundle.putString("result", resultString);
//						bundle.putParcelable("bitmap", barcode);
//						resultIntent.putExtras(bundle);
//						MipcaActivityCapture.this.setResult(RESULT_OK, resultIntent);
						//
//						Intent resultIntent = new Intent(Intent.ACTION_MAIN);
//						resultIntent.addCategory(Intent.CATEGORY_LAUNCHER);
//						resultIntent.setComponent(new ComponentName("com.bbpos.wiseapp.launcher", "com.bbpos.wiseapp.launcher.MainActivity"));
//						Bundle bundle_ex = new Bundle();
//						bundle_ex.putString("result", resultString);
//						bundle_ex.putParcelable("bitmap", barcode);
//						BBLog.i("WiseScan", resultString);
//						resultIntent.putExtras(bundle_ex);
//						startActivity(resultIntent);
						Intent intent = new Intent();
						Bundle bundle_ex = new Bundle();
//						resultString = "{ \"SOID\": \"170\", \"loginToken\": \"ce663eac-c5ff-4a6d-8e7e-eab7e0441442\", \"PID\": \"57534332\", \"CID\": \"CYZZ\", \"DID\": \"BBZZ\", \"quantity\": \"1000\", \"ts\": \"1547520462\" }";
						bundle_ex.putString("result", resultString);
//						bundle_ex.putParcelable("bitmap", barcode);
						intent.putExtras(bundle_ex);
						intent.setAction("com.bbpos.wiseapp.wisescan.SCAN_CODE");
						if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
							intent.setComponent(new ComponentName(getPackageName(), "com.bbpos.wiseapp.provisioning.ProvisionTimer"));
						}
						sendBroadcast(intent, RequestPermission.REQUEST_PERMISSION_MY_BROADCAST);
						dialog.dismiss();
						finish();
					}
				});
		dialog.setOnKeyListener(new DialogInterface.OnKeyListener() {
			@Override
			public boolean onKey(DialogInterface dialog, int keyCode, KeyEvent event) {
				if (keyCode == KeyEvent.KEYCODE_BACK && event.getRepeatCount() == 0){
					continuePreview();
					dialog.dismiss();
					return true;
				}
				return false;
			}
		});
		dialog.show();
	}
	
	private void initCamera(SurfaceHolder surfaceHolder) {
		try {
			CameraManager.get().openDriver(surfaceHolder);
		} catch (IOException ioe) {
			return;
		} catch (RuntimeException e) {
			return;
		}
		if (handler == null) {
			handler = new CaptureActivityHandler(this, decodeFormats,
					characterSet);
		}
	}

	@Override
	public void surfaceChanged(SurfaceHolder holder, int format, int width,
			int height) {

	}

	@Override
	public void surfaceCreated(SurfaceHolder holder) {
		if (!hasSurface) {
			hasSurface = true;
			initCamera(holder);
		}
	}

	@Override
	public void surfaceDestroyed(SurfaceHolder holder) {
		hasSurface = false;
	}

	public ViewfinderView getViewfinderView() {
		return viewfinderView;
	}

	public Handler getHandler() {
		return handler;
	}

	public void drawViewfinder() {
		viewfinderView.drawViewfinder();
	}

	private void initBeepSound() {
		if (playBeep && mediaPlayer == null) {
			// The volume on STREAM_SYSTEM is not adjustable, and users found it
			// too loud,
			// so we now play on the music stream.
			setVolumeControlStream(AudioManager.STREAM_MUSIC);
			mediaPlayer = new MediaPlayer();
			mediaPlayer.setAudioStreamType(AudioManager.STREAM_MUSIC);
			mediaPlayer.setOnCompletionListener(beepListener);

			AssetFileDescriptor file = getResources().openRawResourceFd(R.raw.beep);
			try {
				mediaPlayer.setDataSource(file.getFileDescriptor(),
						file.getStartOffset(), file.getLength());
				file.close();
				mediaPlayer.setVolume(BEEP_VOLUME, BEEP_VOLUME);
				mediaPlayer.prepare();
			} catch (IOException e) {
				mediaPlayer = null;
			}
		}
	}

	private static final long VIBRATE_DURATION = 200L;

	private void playBeepSoundAndVibrate() {
		if (playBeep && mediaPlayer != null) {
			mediaPlayer.start();
		}
		if (vibrate) {
			Vibrator vibrator = (Vibrator) getSystemService(VIBRATOR_SERVICE);
			vibrator.vibrate(VIBRATE_DURATION);
		}
	}

	/**
	 * When the beep has finished playing, rewind to queue up another one.
	 */
	private final OnCompletionListener beepListener = new OnCompletionListener() {
		public void onCompletion(MediaPlayer mediaPlayer) {
			mediaPlayer.seekTo(0);
		}
	};

	@Override
	public void onClick(View v) {
		switch (v.getId()) {
			case R.id.toolbar_left_btn:
				finish();
				break;
		}
	}
}