package com.bbpos.wiseapp.settings.utils;

import android.annotation.TargetApi;
import android.content.Context;
import android.net.ConnectivityManager;
import android.net.LinkAddress;
import android.net.ProxyInfo;
import android.net.wifi.WifiConfiguration;
import android.net.wifi.WifiInfo;
import android.net.wifi.WifiManager;
import android.os.Build;
import android.telephony.TelephonyManager;
import android.text.TextUtils;
import android.util.Log;

import com.bbpos.wiseapp.logger.BBLog;
import com.bbpos.wiseapp.provisioning.ProvisionService;
import com.bbpos.wiseapp.tms.utils.ContextUtil;
import com.bbpos.wiseapp.utils.FileUtils;

import org.json.JSONException;
import org.json.JSONObject;

import java.lang.reflect.Constructor;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.net.InetAddress;
import java.util.List;

/**
 * wifi 代理设置
 *
 * 使用示例：
 *    set proxy：
 *          WifiProxyUtil.setWifiProxySettingsFor17And(this,"**************",1080,null);
 *
 *    unProxy:
 *          WifiProxyUtil.unsetWifiProxySettingsFor17And(this);
 */
public class WifiProxyUtil {
    public static void setEnumField(Object obj, String value, String name) throws Exception{
        Field f = obj.getClass().getField(name);
        f.set(obj, Enum.valueOf((Class<Enum>) f.getType(), value));
    }

    public static void setObjField(Object obj, Object value, String name)
            throws SecurityException, NoSuchFieldException,
            IllegalArgumentException, IllegalAccessException {
        Field f = obj.getClass().getDeclaredField(name);
        f.setAccessible(true);
        f.set(obj, value);
    }

    // getField只能获取类的public 字段.
    public static Object getFieldObject(Object obj, String name) throws Exception{
//        Field f = obj.getClass().getField(name);
        Field f = obj.getClass().getDeclaredField(name);
        f.setAccessible(true);
        Object out = f.get(obj);
        return out;
    }


    public static WifiManager getWifiManager(Context context) {
        return (WifiManager) context.getSystemService(Context.WIFI_SERVICE);
    }

    // 获取当前的Wifi连接
    public static WifiConfiguration getCurrentWifiConfiguration(WifiManager wifiManager) {
        if (!wifiManager.isWifiEnabled()) {
            return null;
        }
        List<WifiConfiguration> configurationList = wifiManager.getConfiguredNetworks();
        WifiConfiguration configuration = null;
        int cur = wifiManager.getConnectionInfo().getNetworkId();

        for (int i = 0; i < configurationList.size(); ++i) {
            WifiConfiguration wifiConfiguration = configurationList.get(i);
            if (wifiConfiguration.networkId == cur) {
                configuration = wifiConfiguration;
                break;
            }
        }
        return configuration;
    }

    /****************************** WIFI proxy **********************************/

    /**
     *
     * @param context
     * @param host  代理主机/服务器
     * @param port  端口
     * @param exclList  需要过滤的地址列表
     */
    public static void setWifiProxySettingsFor17And(Context context, String host, int port, List<String> exclList) {
        WifiManager wifiManager = getWifiManager(context);
        WifiConfiguration config = getCurrentWifiConfiguration(wifiManager);
        setWifiProxySettingsFor17And(context,config,host,port,exclList);
    }

    public static void setWifiProxySettingsFor17And(Context context,WifiConfiguration config, String host, int port, List<String> exclList) {
       setWifiProxySettingsFor17And(context, config, host, port, exclList,true);
    }

    public static void setWifiProxySettingsFor17And(Context context,WifiConfiguration config, String host, int port, List<String> exclList,boolean isFreshWifi) {
        WifiManager wifiManager = getWifiManager(context);
        if (config ==null) return;
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP){
                setHttpProxyForLOLLIPOP(config,host,port,exclList);
            }else {
                setHttpProxyForKitKat(config,host,port,exclList);
            }
            if (isFreshWifi)
                refreshWifiConfig(wifiManager,config);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    // 取消代理设置
    public static void unsetWifiProxySettingsFor17And(Context context) {
        unsetWifiProxySettingsFor17And(context,true);
    }

    public static void unsetWifiProxySettingsFor17And(Context context,boolean isRefreshWifi) {
        WifiManager wifiManager = getWifiManager(context);
        WifiConfiguration config = getCurrentWifiConfiguration(wifiManager);
        unsetWifiProxySettingsFor17And(context,config,true);
    }

    public static void unsetWifiProxySettingsFor17And(Context context,WifiConfiguration config,boolean isRefreshWifi) {
        WifiManager wifiManager = getWifiManager(context);
        if (config == null) return;
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP){
                unSetHttpProxyForLOLLIPOP(config);
            }else {
                unSetHttpProxyForKitKat(context,config);
            }
            if (isRefreshWifi)
                refreshWifiConfig(wifiManager,config);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 试用与 4.x 以下
     * @param config
     * @param host
     * @param port
     * @param exclList
     * @throws
     */
    private static void setHttpProxyForKitKat(WifiConfiguration config,String host, int port, List<String> exclList) throws Exception{
        if (config == null) {
            return;
        }

            //get the link properties from the wifi configuration
            Object linkProperties = getFieldObject(config, "linkProperties");
            if (null == linkProperties) {
                return;
            }

            //获取类 LinkProperties的setHttpProxy方法
            Class<?> proxyPropertiesClass = Class.forName("android.net.ProxyProperties");
            Class<?>[] setHttpProxyParams = new Class[1];
            setHttpProxyParams[0] = proxyPropertiesClass;
            Class<?> lpClass = Class.forName("android.net.LinkProperties");
            Method setHttpProxy = lpClass.getDeclaredMethod("setHttpProxy", setHttpProxyParams);
            setHttpProxy.setAccessible(true);

            // 获取类 ProxyProperties的构造函数
            Constructor<?> proxyPropertiesCtor = proxyPropertiesClass.getConstructor(String.class, int.class, String.class);

            // 实例化类ProxyProperties
            Object proxySettings = proxyPropertiesCtor.newInstance(host, port, exclList);
            //pass the new object to setHttpProxy
            Object[] params = new Object[1];
            params[0] = proxySettings;
            setHttpProxy.invoke(linkProperties, params);
            setEnumField(config, "STATIC", "proxySettings");
    }


    private static void setHttpProxyForLOLLIPOP( WifiConfiguration config,String host, int port, List<String> exclList)
    {
        try
        {
            //linkProperties is no longer in WifiConfiguration
            Class proxyInfoClass = Class.forName("android.net.ProxyInfo");
            Class[] setHttpProxyParams = new Class[1];
            setHttpProxyParams[0] = proxyInfoClass;
            Class wifiConfigClass = Class.forName("android.net.wifi.WifiConfiguration");
            Method setHttpProxy = wifiConfigClass.getDeclaredMethod("setHttpProxy", setHttpProxyParams);
            setHttpProxy.setAccessible(true);

            //Method 1 to get the ENUM ProxySettings in IpConfiguration
            Class ipConfigClass = Class.forName("android.net.IpConfiguration");
            Field f = ipConfigClass.getField("proxySettings");
            Class proxySettingsClass = f.getType();

            //Method 2 to get the ENUM ProxySettings in IpConfiguration
            //Note the $ between the class and ENUM
            //Class proxySettingsClass = Class.forName("android.net.IpConfiguration$ProxySettings");

            Class[] setProxySettingsParams = new Class[1];
            setProxySettingsParams[0] = proxySettingsClass;
            Method setProxySettings = wifiConfigClass.getDeclaredMethod("setProxySettings", setProxySettingsParams);
            setProxySettings.setAccessible(true);

            ProxyInfo pi = null;
            if (exclList == null || exclList.size() == 0){
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                    pi = ProxyInfo.buildDirectProxy(host, port);
                }
            }else {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                    pi = ProxyInfo.buildDirectProxy(host, port, exclList);
                }
            }
            //Android 5 supports a PAC file
            //ENUM value is "PAC"
            //ProxyInfo pacInfo = ProxyInfo.buildPacProxy(Uri.parse("http://localhost/pac"));

            //pass the new object to setHttpProxy
            Object[] params_SetHttpProxy = new Object[1];
            params_SetHttpProxy[0] = pi;
            setHttpProxy.invoke(config, params_SetHttpProxy);

            //pass the enum to setProxySettings
            Object[] params_setProxySettings = new Object[1];
            params_setProxySettings[0] = Enum.valueOf((Class<Enum>) proxySettingsClass, "STATIC");
            setProxySettings.invoke(config, params_setProxySettings);
        }
        catch(Exception e)
        {
            e.printStackTrace();
        }
    }

    private static void unSetHttpProxyForKitKat(Context context,WifiConfiguration config) throws Exception{
        Object linkProperties = getFieldObject(config, "linkProperties");
        if (null == linkProperties) {
            return;
        }

        //get the setHttpProxy method for LinkProperties
        Class<?> proxyPropertiesClass = Class.forName("android.net.ProxyProperties");
        Class<?>[] setHttpProxyParams = new Class[1];
        setHttpProxyParams[0] = proxyPropertiesClass;
        Class<?> lpClass = Class.forName("android.net.LinkProperties");
        Method setHttpProxy = lpClass.getDeclaredMethod("setHttpProxy", setHttpProxyParams);
        setHttpProxy.setAccessible(true);

        //pass null as the proxy
        Object[] params = new Object[1];
        params[0] = null;
        setHttpProxy.invoke(linkProperties, params);
        setEnumField(config, "NONE", "proxySettings");
    }

    @Deprecated
    private static void unSetHttpProxyForLOLLIPOP(Context context)throws Exception{
        WifiManager wifiManager = getWifiManager(context);
        WifiConfiguration configuration = getCurrentWifiConfiguration(wifiManager);
        ProxyInfo mInfo = null;
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP){
            mInfo = ProxyInfo.buildDirectProxy(null,0);
        }
        if (configuration != null){
            Class clazz = Class.forName("android.net.wifi.WifiConfiguration");
            Class parmars = Class.forName("android.net.ProxyInfo");
            Method method = clazz.getMethod("setHttpProxy",parmars);
            method.invoke(configuration,mInfo);
                    Object mIpConfiguration = getFieldObject(configuration,"mIpConfiguration");
            setEnumField(mIpConfiguration, "NONE", "proxySettings");
            setObjField(configuration,mIpConfiguration,"mIpConfiguration");
        }
    }


    private static void unSetHttpProxyForLOLLIPOP( WifiConfiguration config){
        try {
            //linkProperties is no longer in WifiConfiguration
            Class proxyInfoClass = Class.forName("android.net.ProxyInfo");
            Class[] setHttpProxyParams = new Class[1];
            setHttpProxyParams[0] = proxyInfoClass;
            Class wifiConfigClass = Class.forName("android.net.wifi.WifiConfiguration");
            Method setHttpProxy = wifiConfigClass.getDeclaredMethod("setHttpProxy", setHttpProxyParams);
            setHttpProxy.setAccessible(true);

            //Method 1 to get the ENUM ProxySettings in IpConfiguration
            Class ipConfigClass = Class.forName("android.net.IpConfiguration");
            Field f = ipConfigClass.getField("proxySettings");
            Class proxySettingsClass = f.getType();

            //Method 2 to get the ENUM ProxySettings in IpConfiguration
            //Note the $ between the class and ENUM
            //Class proxySettingsClass = Class.forName("android.net.IpConfiguration$ProxySettings");

            Class[] setProxySettingsParams = new Class[1];
            setProxySettingsParams[0] = proxySettingsClass;
            Method setProxySettings = wifiConfigClass.getDeclaredMethod("setProxySettings", setProxySettingsParams);
            setProxySettings.setAccessible(true);

            ProxyInfo pi = null;
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                pi = ProxyInfo.buildDirectProxy(null, 0);
            }
            //Android 5 supports a PAC file
            //ENUM value is "PAC"
            //ProxyInfo pacInfo = ProxyInfo.buildPacProxy(Uri.parse("http://localhost/pac"));

            //pass the new object to setHttpProxy
            Object[] params_SetHttpProxy = new Object[1];
            params_SetHttpProxy[0] = pi;
            setHttpProxy.invoke(config, params_SetHttpProxy);

            //pass the enum to setProxySettings
            Object[] params_setProxySettings = new Object[1];
            params_setProxySettings[0] = Enum.valueOf((Class<Enum>) proxySettingsClass, "NONE");
            setProxySettings.invoke(config, params_setProxySettings);
        }catch(Exception e){
            e.printStackTrace();
        }
    }

    public static boolean refreshWifiConfig(WifiManager wifiManager, WifiConfiguration config) {
        boolean result = wifiManager.updateNetwork(config) != -1; //apply the setting
        if (result) result = wifiManager.saveConfiguration(); //Save it
        if (result) wifiManager.reassociate(); // reconnect with the new static IP
        int netId = wifiManager.addNetwork(config);
        wifiManager.disableNetwork(netId);
        return wifiManager.enableNetwork(netId, true);
    }

    public static boolean setConfigProxy(Context context) {
        String configPath = FileUtils.getConfigFilePath();
        String config = FileUtils.readFile(configPath);
        BBLog.d(BBLog.TAG, "setConfigProxy: config path---->"+configPath);
        if (!TextUtils.isEmpty(config)) {
            BBLog.e(BBLog.TAG, "設置代理: ");
            try {
                ProxyHandler.handlWifiProxy(context, new JSONObject(config));
            } catch (Exception e) {
                e.printStackTrace();
            }
            return true;
        }
        return false;
    }

    public static void resetConfigProxy(Context context) {
        WifiManager wifiManager = WifiProxyUtil.getWifiManager(context);
        WifiConfiguration config  = WifiProxyUtil.getCurrentWifiConfiguration(wifiManager);
        unsetWifiProxySettingsFor17And(context,config,false);
        setWifiStaticIp(context,config,true,null,0,null,null,null,false);
        refreshWifiConfig(wifiManager,config);
    }

    /****************************** WIFI proxy **********************************/


    /****************************** WIFI STATIC IP **********************************/

    @TargetApi(Build.VERSION_CODES.LOLLIPOP)
    public static boolean setWifiStaticIp(Context context, boolean dhcp, String ip, int prefix, String dns1, String dns2, String gateway){
        WifiManager wifiManager = (WifiManager) context.getSystemService(Context.WIFI_SERVICE);
        boolean flag = false;
        if (!wifiManager.isWifiEnabled()) {
            // wifi is disabled
            return flag;
        }
        // get the current wifi configuration
        WifiConfiguration wifiConfig = null;

        WifiInfo connectionInfo = wifiManager.getConnectionInfo();
        List<WifiConfiguration> configuredNetworks = wifiManager.getConfiguredNetworks();
        if (configuredNetworks != null) {
            for (WifiConfiguration conf : configuredNetworks) {
                if (conf.networkId == connectionInfo.getNetworkId()) {
                    wifiConfig = conf;
                    break;
                }
            }
        }
        if (wifiConfig == null) return false;

        flag = setWifiStaticIp(context, wifiConfig,dhcp, ip, prefix, dns1, dns2, gateway);
        return flag;
    }

    @TargetApi(Build.VERSION_CODES.LOLLIPOP)
    public static boolean setWifiStaticIp(Context context,WifiConfiguration wifiConfig, boolean dhcp, String ip, int prefix, String dns1, String dns2, String gateway) {
        return setWifiStaticIp(context, wifiConfig, dhcp, ip, prefix, dns1, dns2, gateway,true);
    }

    @TargetApi(Build.VERSION_CODES.LOLLIPOP)
    public static boolean setWifiStaticIp(Context context,WifiConfiguration wifiConfig, boolean dhcp, String ip, int prefix, String dns1, String dns2, String gateway,boolean isRefreshWifi){
        boolean flag = false;
        if (wifiConfig == null) return false;

        WifiManager wifiManager = (WifiManager) context.getSystemService(Context.WIFI_SERVICE);
        if (Build.VERSION.SDK_INT > Build.VERSION_CODES.LOLLIPOP) { // 如果是android3.x版本及以上的话
            try {
                Class<?> ipAssignment = wifiConfig.getClass().getMethod("getIpAssignment").invoke(wifiConfig).getClass();
                Object staticConf = wifiConfig.getClass().getMethod("getStaticIpConfiguration").invoke(wifiConfig);
                if (dhcp) {
                    wifiConfig.getClass().getMethod("setIpAssignment", ipAssignment).invoke(wifiConfig, Enum.valueOf((Class<Enum>) ipAssignment, "DHCP"));
                    if (staticConf != null) {
                        staticConf.getClass().getMethod("clear").invoke(staticConf);
                        BBLog.e("自动分配模式", "staticConf!=null");
                    }
                } else {
                    wifiConfig.getClass().getMethod("setIpAssignment", ipAssignment).invoke(wifiConfig, Enum.valueOf((Class<Enum>) ipAssignment, "STATIC"));
                    if (staticConf == null) {
                        BBLog.e("静态IP模式", "staticConf==null");
                        Class<?> staticConfigClass = Class.forName("android.net.StaticIpConfiguration");
                        staticConf = staticConfigClass.newInstance();
                        if (staticConf == null) {
                            BBLog.e("静态IP模式", "staticConf还是==null");
                        }
                    } else {
                        BBLog.e("静态IP模式", "staticConf！=null");
                    }
                    // STATIC IP AND MASK PREFIX
                    Constructor<?> laConstructor = LinkAddress.class.getConstructor(InetAddress.class, int.class);
                    LinkAddress linkAddress = (LinkAddress) laConstructor.newInstance(
                            InetAddress.getByName(ip),
                            prefix);
                    staticConf.getClass().getField("ipAddress").set(staticConf, linkAddress);
                    // GATEWAY
                    staticConf.getClass().getField("gateway").set(staticConf, InetAddress.getByName(gateway));
                    // DNS
                    List<InetAddress> dnsServers = (List<InetAddress>) staticConf.getClass().getField("dnsServers").get(staticConf);
                    dnsServers.clear();
                    dnsServers.add(InetAddress.getByName(dns1));
                    dnsServers.add(InetAddress.getByName(dns2));
                    wifiConfig.getClass().getMethod("setStaticIpConfiguration", staticConf.getClass()).invoke(wifiConfig, staticConf);
                }
                // apply the configuration change
                if (isRefreshWifi)
                    flag = refreshWifiConfig(wifiManager,wifiConfig);
            } catch (Exception e) {
                e.printStackTrace();
                flag = false;
                BBLog.d("staiciputils", "setWifiStaticIp: ex:"+ e.getMessage());
            }
        }
        return flag;
    }


    /********************************WIFI STATIC IP********************************/


    /********************************4G  开闭********************************/

    public static void setMobileDataState(Context context, boolean isEnable){
        BBLog.w(BBLog.TAG, "设置移动数据开关：" + isEnable);
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            setMobileDataStateForLollipop(context,isEnable);
        }else {
            setMobileDataStateForKitKat(context, isEnable);
        }
    }

    public static boolean getMobileDataState(Context context){
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            return getMobileDataStateForLollipop(context);
        }else {
            return getMobileDataStateForKitKat(context);
        }
    }



    /**
     * 适用于5.0 以下
     * @param context
     * @param isEnable
     */
    public static void setMobileDataStateForKitKat(Context context,boolean isEnable) {
        Class[] setArgArray = new Class[] {boolean.class};
        try {
            ConnectivityManager conManager = (ConnectivityManager) context.getSystemService(Context.CONNECTIVITY_SERVICE);
            Method mSetMethod = conManager.getClass().getMethod("setMobileDataEnabled", setArgArray);
            if (mSetMethod !=null)
                mSetMethod.invoke(conManager,isEnable);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    public static Boolean getMobileDataStateForKitKat(Context context) {
        try {
            ConnectivityManager conManager = (ConnectivityManager) context.getSystemService(Context.CONNECTIVITY_SERVICE);
            Method mGetMethod = conManager.getClass().getMethod("getMobileDataEnabled");
            return (Boolean) mGetMethod.invoke(conManager);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }


    /**
     * 适用于5.0 以下
     * @param context
     * @param isEnable
     */
    public static void setMobileDataStateForLollipop(Context context, boolean isEnable) {
        TelephonyManager telephonyService = (TelephonyManager) context.getSystemService(Context.TELEPHONY_SERVICE);
        try {
            Method setDataEnabled = telephonyService.getClass().getDeclaredMethod("setDataEnabled",boolean.class);
            if (null != setDataEnabled) {
                setDataEnabled.invoke(telephonyService, isEnable);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }



    public static boolean getMobileDataStateForLollipop(Context context) {
        TelephonyManager telephonyService = (TelephonyManager) context.getSystemService(Context.TELEPHONY_SERVICE);
        try {
            Method getDataEnabled = telephonyService.getClass().getDeclaredMethod("getDataEnabled");
            if (null != getDataEnabled) {
                return (Boolean) getDataEnabled.invoke(telephonyService);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

    /******************************* 4G  开闭 *********************************/

    public static boolean isWifiProxy(Context context) {

        final boolean IS_ICS_OR_LATER = Build.VERSION.SDK_INT >= Build.VERSION_CODES.ICE_CREAM_SANDWICH;
        String proxyAddress;
        int proxyPort;
        if (IS_ICS_OR_LATER) {
            proxyAddress = System.getProperty("http.proxyHost");
            String portStr = System.getProperty("http.proxyPort");
            proxyPort = Integer.parseInt((portStr != null ? portStr : "-1"));
        } else {
            proxyAddress = android.net.Proxy.getHost(context);
            proxyPort = android.net.Proxy.getPort(context);
        }
        BBLog.e(BBLog.TAG, "isWifiProxy: 判断当前wifi是否设置代理，"+(Boolean.valueOf(!TextUtils.isEmpty(proxyAddress)) && (proxyPort != -1)));
        return (!TextUtils.isEmpty(proxyAddress)) && (proxyPort != -1);
    }
}
