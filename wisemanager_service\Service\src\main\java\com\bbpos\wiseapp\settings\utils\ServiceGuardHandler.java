package com.bbpos.wiseapp.settings.utils;

import android.app.AlarmManager;
import android.app.PendingIntent;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.content.pm.PackageManager.NameNotFoundException;
import android.os.Build;
import android.os.Debug;
import android.os.Environment;
import android.util.Log;

import com.bbpos.wiseapp.logger.BBLog;
import com.bbpos.wiseapp.settings.activity.SettingActivity;
import com.bbpos.wiseapp.tms.service.InitializeService;
import com.bbpos.wiseapp.tms.utils.ContextUtil;

import java.io.File;
import java.io.FileOutputStream;
import java.io.PrintWriter;
import java.io.StringWriter;
import java.io.Writer;
import java.lang.Thread.UncaughtExceptionHandler;
import java.lang.reflect.Field;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * 处理程序中未捕获的异常，重启应用
 * 将异常写入日志文件
 */
public class ServiceGuardHandler implements UncaughtExceptionHandler {
    public static final String TAG = ServiceGuardHandler.class.getSimpleName();

    private static ServiceGuardHandler instance = null;

    private Context mContext;

    private UncaughtExceptionHandler mDefaultHandler;

    private Map<String, String> infos = new HashMap<String, String>();

    private ServiceGuardHandler() {
    }

    private ServiceGuardHandler(Context context) {
        mContext = context;
        mDefaultHandler = Thread.getDefaultUncaughtExceptionHandler();
        Thread.setDefaultUncaughtExceptionHandler(this);
    }

    public static ServiceGuardHandler getInstance(Context context) {
        if (instance == null) {
            synchronized (ServiceGuardHandler.class) {
                if (instance == null) {
                    instance = new ServiceGuardHandler(context);
                }
            }
        }
        return instance;
    }

    @Override
    public void uncaughtException(Thread thread, Throwable ex) {
        BBLog.d(TAG, "uncaughtException: 捕获到service异常");
        try {
            if (!handleException(ex) && mDefaultHandler != null) {
                mDefaultHandler.uncaughtException(thread, ex);
            }

            restartApp();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void restartApp(){
        Intent mStartActivity = new Intent(ContextUtil.getInstance(), InitializeService.class);
        int mPendingIntentId = 123456;
        PendingIntent mPendingIntent = PendingIntent.getService(ContextUtil.getInstance(), mPendingIntentId, mStartActivity, 0);
        AlarmManager mgr = (AlarmManager) ContextUtil.getInstance().getSystemService(Context.ALARM_SERVICE);
        mgr.set(AlarmManager.RTC, System.currentTimeMillis() + 2000, mPendingIntent);

        android.os.Process.killProcess(android.os.Process.myPid());
    }

    private boolean handleException(final Throwable ex) {
        ex.printStackTrace();
        // 如果是调试状态则不生成异常文件，让系统默认的异常处理器来处理
        if (Debug.isDebuggerConnected())
            return true;
        if (ex == null)
            return false;
        // 收集设备参数信息
        collectDeviceInfo(mContext);
        // 保存日志文件
        saveCrashInfo2File(ex);
        return true;
    }

    private void collectDeviceInfo(Context ctx) {
        try {
            PackageManager pm = ctx.getPackageManager();
            PackageInfo pi = pm.getPackageInfo(ctx.getPackageName(), PackageManager.GET_ACTIVITIES);
            if (pi != null) {
                String versionName = pi.versionName == null ? "null" : pi.versionName;
                String versionCode = pi.versionCode + "";
                infos.put("versionName", versionName);
                infos.put("versionCode", versionCode);
            }
        } catch (Exception e) {
            BBLog.e(TAG, "an error occurred when collect package info", e);
        }
        Field[] fields = Build.class.getDeclaredFields();
        for (Field field : fields) {
            try {
                field.setAccessible(true);
                infos.put(field.getName(), field.get(null).toString());
            } catch (Exception e) {
                BBLog.e(TAG, "an error occurred when collect crash info", e);
            }
        }
    }

    private void saveCrashInfo2File(Throwable ex) {
        StringBuffer sb = new StringBuffer();
        for (Map.Entry<String, String> entry : infos.entrySet()) {
            String key = entry.getKey();
            String value = entry.getValue();
            sb.append(key + "=" + value + "\n");
        }

        Writer writer = new StringWriter();
        PrintWriter printWriter = new PrintWriter(writer);
        ex.printStackTrace(printWriter);
        Throwable cause = ex.getCause();
        while (cause != null) {
            cause.printStackTrace(printWriter);
            cause = cause.getCause();
        }
        printWriter.close();
        String result = writer.toString();
        sb.append(result);
        DateFormat df = new SimpleDateFormat("yyyyMMddHHmmssSSS");
        try {
            String fileName = String.format("crash-%s.log", df.format(new Date(System.currentTimeMillis())));
            if (Environment.MEDIA_MOUNTED.equals(Environment.getExternalStorageState())) {
                String path = "/sdcard/wiseapp/crashlog/";
                File dir = new File(path);
                if (!dir.exists())
                    dir.mkdirs();
                FileOutputStream fos = new FileOutputStream(path + fileName);
                fos.write(sb.toString().getBytes());
                fos.close();
            }
        } catch (Exception e) {
            BBLog.e(TAG, "an error occured while writing file...", e);
        }
    }
}
