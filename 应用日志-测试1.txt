---------------------------- PROCESS STARTED (3540) for package com.dspread.mdm.service ----------------------------
--------- beginning of main
2025-08-21 16:12:34.757  3540-3540  Common                  com.dspread.mdm.service              D  🔧 TimerConfig ========== 当前定时器配置 (生产模式) ==========
2025-08-21 16:12:34.763  3540-3540  Common                  com.dspread.mdm.service              D  🔧 TimerConfig ①心跳定时器: 300秒
2025-08-21 16:12:34.769  3540-3540  Common                  com.dspread.mdm.service              D  🔧 TimerConfig ②终端信息上传定时器: 900秒
2025-08-21 16:12:34.775  3540-3540  Common                  com.dspread.mdm.service              D  🔧 TimerConfig ③任务执行定时器: 60秒
2025-08-21 16:12:34.781  3540-3540  Common                  com.dspread.mdm.service              D  🔧 TimerConfig ④服务守护定时器: 120秒
2025-08-21 16:12:34.787  3540-3540  Common                  com.dspread.mdm.service              D  🔧 TimerConfig ⑤初始化定时器: 43200秒
2025-08-21 16:12:34.787  3540-3580  Common                  com.dspread.mdm.service              D  🔧 Module LogStream Manager status changed to: RUNNING
2025-08-21 16:12:34.793  3540-3540  Common                  com.dspread.mdm.service              D  🔧 TimerConfig ===============================================
2025-08-21 16:12:34.794  3540-3580  Common                  com.dspread.mdm.service              D  🔧 [ModuleManagerRegistry] LogStreamManager启动成功
2025-08-21 16:12:34.799  3540-3540  Common                  com.dspread.mdm.service              I  ✅ [SmartMdmBackgroundService] 所有定时器启动完成
2025-08-21 16:12:34.805  3540-3540  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 核心组件初始化完成
2025-08-21 16:12:34.821  3540-3580  Compatibil...geReporter com.dspread.mdm.service              D  Compat change id reported: 263076149; UID 1000; state: ENABLED
2025-08-21 16:12:34.832  3540-3580  Common                  com.dspread.mdm.service              D  🔧 Module Geofence Manager status changed to: INITIALIZED
2025-08-21 16:12:34.839  3540-3580  Common                  com.dspread.mdm.service              D  🔧 Module Geofence Manager status changed to: STARTING
2025-08-21 16:12:34.866  3540-3580  Platform                com.dspread.mdm.service              D  🔧 用户交互监控已启动（简化版本）
2025-08-21 16:12:34.866  3540-3540  Platform                com.dspread.mdm.service              D  🔧 [WiFiManagerApi] WiFiManagerApi 单例实例已创建
2025-08-21 16:12:34.872  3540-3580  Platform                com.dspread.mdm.service              D  🔧 使用模拟用户交互检测
2025-08-21 16:12:34.877  3540-3580  Platform                com.dspread.mdm.service              D  🔧 用户交互监控已启动
2025-08-21 16:12:34.884  3540-3580  Service                 com.dspread.mdm.service              D  🔧 [GeofenceManager]Constants.geofenceStatus = 1
2025-08-21 16:12:34.890  3540-3580  Service                 com.dspread.mdm.service              D  🔧 [GeofenceManager]-- Constants.storeId =  -- Constants.storeSsid =  -- Constants.storeIp = 
2025-08-21 16:12:34.897  3540-3580  Common                  com.dspread.mdm.service              D  🔧 Module Geofence Manager status changed to: RUNNING
2025-08-21 16:12:34.903  3540-3580  Common                  com.dspread.mdm.service              D  🔧 [ModuleManagerRegistry] GeofenceManager启动成功
2025-08-21 16:12:34.921  3540-3580  RuleBase                com.dspread.mdm.service              D  🔧 RuleBaseManager 开始初始化RuleBaseManager...
2025-08-21 16:12:34.931  3540-3580  RuleBase                com.dspread.mdm.service              D  🔧 RuleBaseStorage RuleBase存储管理器初始化完成
2025-08-21 16:12:34.937  3540-3580  RuleBase                com.dspread.mdm.service              D  🔧 RuleStateMonitor 状态监控器初始化完成
2025-08-21 16:12:34.942  3540-3580  RuleBase                com.dspread.mdm.service              D  🔧 RuleBaseManager RuleBaseManager初始化完成
2025-08-21 16:12:34.948  3540-3580  Common                  com.dspread.mdm.service              D  🔧 [ModuleManagerRegistry] RuleBaseManager启动成功
2025-08-21 16:12:34.954  3540-3580  Common                  com.dspread.mdm.service              D  🔧 [ModuleManagerRegistry] 所有模块启动完成
2025-08-21 16:12:35.140  3540-3607  Common                  com.dspread.mdm.service              I  ✅ WebSocket 物理连接成功，等待服务器 hellMsg
2025-08-21 16:12:35.149  3540-3607  WebSocket               com.dspread.mdm.service              I  🔧 ===== 心跳发送器实际配置 =====
2025-08-21 16:12:35.156  3540-3607  WebSocket               com.dspread.mdm.service              I  🔧 实际 PING 发送间隔: 30000ms (30秒)
2025-08-21 16:12:35.164  3540-3607  WebSocket               com.dspread.mdm.service              I  🔧 PING 定时器名称: PingSender
2025-08-21 16:12:35.171  3540-3607  WebSocket               com.dspread.mdm.service              I  🔧 实际 PONG 发送间隔: 0ms (0秒)
2025-08-21 16:12:35.177  3540-3607  WebSocket               com.dspread.mdm.service              I  🔧 PONG 定时器名称: PongSender
2025-08-21 16:12:35.183  3540-3607  WebSocket               com.dspread.mdm.service              I  🔧 ===============================
2025-08-21 16:12:35.189  3540-3607  WebSocket               com.dspread.mdm.service              I  🔧 启动PING/PONG检测，阈值: 连续3个PING无PONG响应
2025-08-21 16:12:35.242  3540-3607  Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"msg_state":"P","request_time":"1755148854367","data":{"taskList":[{"beginDate":"2024-08-14 05:20:54","taskType":"02","apkName":"Via","endDate":"9999-12-31 23:59:59","appId":"1755148854367","packName":"mark.via","versionName":"6.6.0","taskId":"1755148854367","versionCode":"20250713"}]},"tranCode":"ST001","request_id":"1755148854367ST001","version":"1","serialNo":"01354090202503050399"}
2025-08-21 16:12:35.250  3540-3607  WebSocket               com.dspread.mdm.service              I  🔧 准备发送响应确认 - requestId: 1755148854367ST001, needResponse: true
2025-08-21 16:12:35.261  3540-3607  WebSocket               com.dspread.mdm.service              I  🔧 非重要消息，不缓存: C0000
2025-08-21 16:12:35.273  3540-3607  WebSocket               com.dspread.mdm.service              I  🔧 非重要消息，不缓存: C0000
2025-08-21 16:12:35.279  3540-3607  WebSocket               com.dspread.mdm.service              I  🔧 已发送C0000响应确认: requestId=1755148854367ST001
2025-08-21 16:12:35.287  3540-3607  Task                    com.dspread.mdm.service              D  🔧 处理任务消息: ST001
2025-08-21 16:12:35.293  3540-3607  Task                    com.dspread.mdm.service              I  ℹ️ 更新任务列表: requestId=1755148854367ST001, 任务数量=1
2025-08-21 16:12:35.299  3540-3607  Task                    com.dspread.mdm.service              D  🔧 任务插入到末尾: taskId=1755148854367
2025-08-21 16:12:35.305  3540-3607  Task                    com.dspread.mdm.service              D  🔧 添加/更新任务: taskId=1755148854367
2025-08-21 16:12:35.315  3540-3607  Task                    com.dspread.mdm.service              D  🔧 保存任务列表到存储: 1 个
2025-08-21 16:12:35.320  3540-3607  Task                    com.dspread.mdm.service              D  🔧 开始处理任务列表，共 1 个任务
2025-08-21 16:12:35.327  3540-3607  Task                    com.dspread.mdm.service              D  🔧 处理任务: taskId=1755148854367, type=02, package=mark.via, apk=Via
2025-08-21 16:12:35.332  3540-3607  Task                    com.dspread.mdm.service              D  🔧 处理卸载任务: 1755148854367
2025-08-21 16:12:35.345  3540-3607  Task                    com.dspread.mdm.service              D  🔧 更新任务状态: taskId=1755148854367, state=D02
2025-08-21 16:12:35.355  3540-3607  Task                    com.dspread.mdm.service              D  🔧 保存任务列表到存储: 1 个
2025-08-21 16:12:35.366  3540-3607  Task                    com.dspread.mdm.service              D  🔧 保存任务列表到存储: 1 个
2025-08-21 16:12:35.372  3540-3607  WebSocket               com.dspread.mdm.service              I  🔧 ST001 主动式上送: task_failed (主动: 2)
2025-08-21 16:12:35.397  3540-3607  WebSocket               com.dspread.mdm.service              I  🔧 消息已缓存: C0108 (缓存数量: 2)
2025-08-21 16:12:35.403  3540-3607  WebSocket               com.dspread.mdm.service              I  🔧 任务结果实时上送: taskId=1755148854367, result=D02 (1)
2025-08-21 16:12:35.450  3540-3607  Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"msg_state":"P","request_time":"1755500779963","data":{"taskList":[{"beginDate":"2024-08-18 07:06:19","taskType":"02","apkName":"Via","endDate":"9999-12-31 23:59:59","appId":"1755500779963","packName":"mark.via","versionName":"6.6.0","taskId":"1755500779963","versionCode":"20250713"}]},"tranCode":"ST001","request_id":"1755500779963ST001","version":"1","serialNo":"01354090202503050399"}
2025-08-21 16:12:35.458  3540-3607  WebSocket               com.dspread.mdm.service              I  🔧 准备发送响应确认 - requestId: 1755500779963ST001, needResponse: true
2025-08-21 16:12:35.468  3540-3607  WebSocket               com.dspread.mdm.service              I  🔧 非重要消息，不缓存: C0000
2025-08-21 16:12:35.481  3540-3607  WebSocket               com.dspread.mdm.service              I  🔧 非重要消息，不缓存: C0000
2025-08-21 16:12:35.487  3540-3607  WebSocket               com.dspread.mdm.service              I  🔧 已发送C0000响应确认: requestId=1755500779963ST001
2025-08-21 16:12:35.494  3540-3607  Task                    com.dspread.mdm.service              D  🔧 处理任务消息: ST001
2025-08-21 16:12:35.500  3540-3607  Task                    com.dspread.mdm.service              I  ℹ️ 更新任务列表: requestId=1755500779963ST001, 任务数量=1
2025-08-21 16:12:35.508  3540-3607  Task                    com.dspread.mdm.service              D  🔧 任务插入到末尾: taskId=1755500779963
2025-08-21 16:12:35.514  3540-3607  Task                    com.dspread.mdm.service              D  🔧 添加/更新任务: taskId=1755500779963
2025-08-21 16:12:35.520  3540-3607  Task                    com.dspread.mdm.service              D  🔧 发现已完成任务: taskId=1755148854367, result=D02
2025-08-21 16:12:35.526  3540-3607  Task                    com.dspread.mdm.service              D  🔧 上送任务结果: taskId=1755148854367, result=D02
2025-08-21 16:12:35.532  3540-3607  Task                    com.dspread.mdm.service              D  🔧 清理已完成任务: taskId=1755148854367, result=D02
2025-08-21 16:12:35.537  3540-3607  Task                    com.dspread.mdm.service              D  🔧 清理了 1 个已完成任务
2025-08-21 16:12:35.547  3540-3607  Task                    com.dspread.mdm.service              D  🔧 保存任务列表到存储: 1 个
2025-08-21 16:12:35.553  3540-3607  Task                    com.dspread.mdm.service              D  🔧 开始处理任务列表，共 1 个任务
2025-08-21 16:12:35.559  3540-3607  Task                    com.dspread.mdm.service              D  🔧 处理任务: taskId=1755500779963, type=02, package=mark.via, apk=Via
2025-08-21 16:12:35.564  3540-3607  Task                    com.dspread.mdm.service              D  🔧 处理卸载任务: 1755500779963
2025-08-21 16:12:35.573  3540-3607  Task                    com.dspread.mdm.service              D  🔧 更新任务状态: taskId=1755500779963, state=D02
2025-08-21 16:12:35.582  3540-3607  Task                    com.dspread.mdm.service              D  🔧 保存任务列表到存储: 1 个
2025-08-21 16:12:35.594  3540-3607  Task                    com.dspread.mdm.service              D  🔧 保存任务列表到存储: 1 个
2025-08-21 16:12:35.600  3540-3607  WebSocket               com.dspread.mdm.service              I  🔧 ST001 主动式上送: task_failed (主动: 3)
2025-08-21 16:12:35.625  3540-3607  WebSocket               com.dspread.mdm.service              I  🔧 消息已缓存: C0108 (缓存数量: 3)
2025-08-21 16:12:35.631  3540-3607  WebSocket               com.dspread.mdm.service              I  🔧 任务结果实时上送: taskId=1755500779963, result=D02 (1)
2025-08-21 16:12:35.665  3540-3607  Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"hellMsg":"hello 01354090202503050399","tranCode":"S0000","version":"1","rebootTime":"03:05:02","serialNo":"01354090202503050399","deviceStatus":"6"}
2025-08-21 16:12:35.671  3540-3607  Common                  com.dspread.mdm.service              I  ✅ 收到服务器 hellMsg，WebSocket 业务连接成功！
2025-08-21 16:12:35.677  3540-3607  Common                  com.dspread.mdm.service              I  ✅ WebSocket 业务连接成功！
2025-08-21 16:12:35.683  3540-3607  WebSocket               com.dspread.mdm.service              I  🔧 业务连接成功，重连计数已重置为 0
2025-08-21 16:12:35.691  3540-3607  Platform                com.dspread.mdm.service              D  🔧 WakeLockManager 获取WiFi锁成功，防止WiFi休眠
--------- beginning of system
2025-08-21 16:12:35.697  3540-3607  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接成功，检查自身更新任务状态
2025-08-21 16:12:35.706  3540-3607  Receiver                com.dspread.mdm.service              D  🔧 发送广播: com.dspread.mdm.service.CHECK_SELF_UPDATE_STATUS
2025-08-21 16:12:35.712  3540-3607  WebSocket               com.dspread.mdm.service              I  🔧 开始发送缓存消息，数量: 3
2025-08-21 16:12:35.726  3540-3607  WebSocket               com.dspread.mdm.service              I  🔧 C0902 主动式上送: cached_message_resend (主动: 4)
2025-08-21 16:12:35.762  3540-3607  Common                  com.dspread.mdm.service              I  ✅ 缓存消息发送成功: C0902
2025-08-21 16:12:36.269  3540-3607  WebSocket               com.dspread.mdm.service              I  🔧 C0108 主动式上送: cached_message_resend (主动: 5)
2025-08-21 16:12:36.290  3540-3607  Common                  com.dspread.mdm.service              I  ✅ 缓存消息发送成功: C0108
2025-08-21 16:12:36.797  3540-3607  WebSocket               com.dspread.mdm.service              I  🔧 C0108 主动式上送: cached_message_resend (主动: 6)
2025-08-21 16:12:36.818  3540-3607  Common                  com.dspread.mdm.service              I  ✅ 缓存消息发送成功: C0108
2025-08-21 16:12:37.326  3540-3607  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接成功
2025-08-21 16:12:37.332  3540-3607  WebSocket               com.dspread.mdm.service              I  🔧 设备状态: 6
2025-08-21 16:12:37.338  3540-3607  WebSocket               com.dspread.mdm.service              I  🔧 检查首次连接标志: isFirstTimeWebSocketConnected=true
2025-08-21 16:12:37.344  3540-3607  WebSocket               com.dspread.mdm.service              I  🔧 首次WebSocket连接成功，发送初始化信息并执行初始化检查
2025-08-21 16:12:37.350  3540-3607  WebSocket               com.dspread.mdm.service              I  🔧 开始发送初始化信息（带流量控制）
2025-08-21 16:12:37.357  3540-3607  WebSocket               com.dspread.mdm.service              I  🔧 C0901 被动式上送: first_connection (被动: 2)
2025-08-21 16:12:37.372  3540-3607  Platform                com.dspread.mdm.service              D  🔧 重新收集应用信息
2025-08-21 16:12:37.464  3540-3607  AppManager              com.dspread.mdm.service              I  ℹ️ 应用信息获取完成: 总数130(系统129/用户1) 返回1个
2025-08-21 16:12:37.477  3540-3607  Platform                com.dspread.mdm.service              D  🔧 应用信息: 1 个应用
2025-08-21 16:12:37.504  3540-3607  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0901","serialNo":"01354090202503050399","request_time":"1755763957484","request_id":"1755763957484C0901","version":"1","data":{"apkInfo":[{"packName":"com.dspread.mdm.service","apkName":"service","versionCode":22,"versionName":"1.1.03.20250821.DSPREAD.MDM.SERVICE","updateDate":"2025-08-21 16:12:28"}],"sytemInfo":{"androidVersion":"14","buildNumber":"BASE_D30M-MU_GEN_AP_V1.0.8.1_202507032000_ota_test","aspl":"2025-03-05"},"serviceInfo":[]},"myVersionName":"1.1.03.20250821.DSPREAD.MDM.SERVICE","terminalDate":"20250821161237"}
2025-08-21 16:12:37.510  3540-3607  WebSocket               com.dspread.mdm.service              I  🔧 C0901 应用信息上送成功 (1)
2025-08-21 16:12:37.616  3540-3610  ProfileInstaller        com.dspread.mdm.service              D  Installing profile for com.dspread.mdm.service
2025-08-21 16:12:38.517  3540-3607  WebSocket               com.dspread.mdm.service              I  🔧 C0902 被动式上送: first_connection (被动: 3)
2025-08-21 16:12:38.555  3540-3607  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0902","serialNo":"01354090202503050399","request_time":"1755763958537","request_id":"1755763958537C0902","version":"1","data":{"batteryLife":100,"batteryHealth":2,"temprature":"28.5","isCharging":"1","isLowBattery":"0","powerOff":"0","outOfBattery":"0"},"myVersionName":"1.1.03.20250821.DSPREAD.MDM.SERVICE","terminalDate":"20250821161238"}
2025-08-21 16:12:38.562  3540-3607  WebSocket               com.dspread.mdm.service              I  🔧 C0902 电池状态上送成功 (1, trigger=first_connection)
2025-08-21 16:12:39.569  3540-3607  WebSocket               com.dspread.mdm.service              I  🔧 C0903 被动式上送: first_connection (被动: 4)
2025-08-21 16:12:39.730  3540-3607  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0903","serialNo":"01354090202503050399","request_time":"1755763959688","request_id":"1755763959688C0903","version":"1","data":{"hardwareInfo":[{"name":"CPU Usage","desc":"cpu:0.50"},{"name":"storage_usage_status","desc":" 4.56GB \/ 32.00GB ","state":"1"},{"name":"total_memory","desc":" 0.66GB \/ 1.86GB ","state":"1"},{"name":"androidVersion","desc":"14","state":"1"},{"name":"network_status","desc":"wifi","state":"1"},{"name":"OSVer","desc":"14","state":"1"}],"locationInfo":{"gps":{"longitude":"-999.0","latitude":"-999.0","satelliteCount":0,"satelliteCountInUsed":0,"satelliteAverSNR":"0.00","distance":"0.0","lockStatus":"1","lockMeter":"200","proId":"","proName":""},"baseSite":{"MCC":"","MNC":"","CID":"-1","LAC":"-1","DBM":"-999","IMSI":"","ICCID":""},"wifi":{"SSID":"fubox_2.4G","IP":"**************","MAC":"BE:87:C2:42:DF:75","SSTH":"-34","BSSID":"7c:10:c9:d6:9f:10"},"address":"无法获取"}},"myVersionName":"1.1.03.20250821.DSPREAD.MDM.SERVICE","terminalDate":"20250821161239"}
2025-08-21 16:12:39.737  3540-3607  WebSocket               com.dspread.mdm.service              I  🔧 C0903 数据信息上送成功 (1)
2025-08-21 16:12:40.745  3540-3607  WebSocket               com.dspread.mdm.service              I  🔧 C0904 被动式上送: first_connection (被动: 5)
2025-08-21 16:12:40.853  3540-3607  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0904","serialNo":"01354090202503050399","request_time":"1755763960818","request_id":"1755763960818C0904","version":"1","data":{"wifiOption":[{"SSID":"fubox_5G","SSTH":"-42"},{"SSID":"2306","SSTH":"-79"},{"SSID":"2205","SSTH":"-43"},{"SSID":"2205_5G","SSTH":"-64"},{"SSID":"fubox_2.4G","SSTH":"-35"},{"SSID":"DIRECT-CC-HP Laser 1188w","SSTH":"-64"},{"SSID":"2206","SSTH":"-31"},{"SSID":"2206-5G","SSTH":"-32"},{"SSID":"CMCC-EtAF","SSTH":"-86"},{"SSID":"DIRECT-C6-Mi All-in-One Inkjet","SSTH":"-53"},{"SSID":"2207-5G","SSTH":"-73"},{"SSID":"2106-5G","SSTH":"-91"},{"SSID":"2103_5G","SSTH":"-78"},{"SSID":"@Ruijie-1816_5G","SSTH":"-71"},{"SSID":"CMCC-2203","SSTH":"-63"},{"SSID":"@Ruijie-1816","SSTH":"-46"}],"baseSite":{"MCC":"","MNC":"","CID":"-1","LAC":"-1","DBM":"-999","IMSI":"","ICCID":""},"wifi":{"SSID":"fubox_2.4G","IP":"**************","MAC":"BE:87:C2:42:DF:75","SSTH":"-34","BSSID":"7c:10:c9:d6:9f:10"}},"myVersionName":"1.1.03.20250821.DSPREAD.MDM.SERVICE","terminalDate":"20250821161240"}
2025-08-21 16:12:40.859  3540-3607  WebSocket               com.dspread.mdm.service              I  🔧 C0904 网络状态上送成功 (1)
2025-08-21 16:12:41.866  3540-3607  WebSocket               com.dspread.mdm.service              I  🔧 C0906 被动式上送: first_connection (被动: 6)
2025-08-21 16:12:41.881  3540-3607  Platform                com.dspread.mdm.service              D  🔧 DspreadService 使用缓存的SP版本: V1.0.5
2025-08-21 16:12:41.887  3540-3607  Platform                com.dspread.mdm.service              D  🔧 SpVersionApi 通过DspreadService获取SP版本: V1.0.5
2025-08-21 16:12:41.918  3540-3607  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0906","serialNo":"01354090202503050399","request_time":"1755763961899","request_id":"1755763961899C0906","version":"1","data":{"firmWareInfo":{"spfw":"V1.0.5"},"imei_1":"864177060143618","imei_2":"","wifi_mac":"be:87:c2:42:df:75","bt_mac":"","bsn":""},"myVersionName":"1.1.03.20250821.DSPREAD.MDM.SERVICE","terminalDate":"20250821161241"}
2025-08-21 16:12:41.925  3540-3607  WebSocket               com.dspread.mdm.service              I  🔧 C0906 设备信息上送成功 (1)
2025-08-21 16:12:41.932  3540-3607  WebSocket               com.dspread.mdm.service              I  🔧 C0109 被动式上送: first_connection (被动: 7)
2025-08-21 16:12:41.948  3540-3607  Platform                com.dspread.mdm.service              D  🔧 重新收集应用信息（强制刷新）
2025-08-21 16:12:42.036  3540-3607  AppManager              com.dspread.mdm.service              I  ℹ️ 应用信息获取完成: 总数130(系统129/用户1) 返回1个
2025-08-21 16:12:42.048  3540-3607  Platform                com.dspread.mdm.service              D  🔧 应用信息: 1 个应用
2025-08-21 16:12:42.211  3540-3607  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0109","serialNo":"01354090202503050399","request_time":"1755763962148","request_id":"1755763962148C0109","version":"1","data":{"apkInfo":[{"packName":"com.dspread.mdm.service","apkName":"service","versionCode":22,"versionName":"1.1.03.20250821.DSPREAD.MDM.SERVICE","updateDate":"2025-08-21 16:12:28"}],"locationInfo":{"gps":{"longitude":"-999.0","latitude":"-999.0","satelliteCount":0,"satelliteCountInUsed":0,"satelliteAverSNR":"0.00","distance":"0.0","lockStatus":"1","lockMeter":"200","proId":"","proName":""},"baseSite":{"MCC":"","MNC":"","CID":"-1","LAC":"-1","DBM":"-999","IMSI":"","ICCID":""},"wifi":{"SSID":"fubox_2.4G","IP":"**************","MAC":"BE:87:C2:42:DF:75","SSTH":"-34","BSSID":"7c:10:c9:d6:9f:10"},"address":"无法获取"},"hardwareInfo":[{"name":"CPU Usage","desc":"cpu:0.50"},{"name":"storage_usage_status","desc":" 4.56GB \/ 32.00GB ","state":"1"},{"name":"total_memory","desc":" 0.66GB \/ 1.86GB ","state":"1"},{"name":"androidVersion","desc":"14","state":"1"},{"name":"network_status","desc":"wifi","state":"1"},{"name":"OSVer","desc":"14","state":"1"}],"wifiOption":[{"SSID":"fubox_5G","SSTH":"-42"},{"SSID":"2306","SSTH":"-79"},{"SSID":"2205","SSTH":"-43"},{"SSID":"2205_5G","SSTH":"-64"},{"SSID":"fubox_2.4G","SSTH":"-35"},{"SSID":"DIRECT-CC-HP Laser 1188w","SSTH":"-64"},{"SSID":"2206","SSTH":"-31"},{"SSID":"2206-5G","SSTH":"-32"},{"SSID":"CMCC-EtAF","SSTH":"-86"},{"SSID":"DIRECT-C6-Mi All-in-One Inkjet","SSTH":"-53"},{"SSID":"2207-5G","SSTH":"-73"},{"SSID":"2106-5G","SSTH":"-91"},{"SSID":"2103_5G","SSTH":"-78"},{"SSID":"@Ruijie-1816_5G","SSTH":"-71"},{"SSID":"CMCC-2203","SSTH":"-63"},{"SSID":"@Ruijie-1816","SSTH":"-46"}],"sytemInfo":{"androidVersion":"14","buildNumber":"BASE_D30M-MU_GEN_AP_V1.0.8.1_202507032000_ota_test","aspl":"2025-03-05"}},"myVersionName":"1.1.03.20250821.DSPREAD.MDM.SERVICE","terminalDate":"20250821161242"}
2025-08-21 16:12:42.218  3540-3607  WebSocket               com.dspread.mdm.service              I  🔧 C0109 终端信息上传完成 (1, trigger=first_connection)
2025-08-21 16:12:42.224  3540-3607  Common                  com.dspread.mdm.service              I  ✅ 初始化信息发送完成
2025-08-21 16:12:42.230  3540-3607  WebSocket               com.dspread.mdm.service              I  🔧 检查服务更新结果
2025-08-21 16:12:42.238  3540-3607  Task                    com.dspread.mdm.service              I  ℹ️ 本地任务列表长度: 1
2025-08-21 16:12:42.244  3540-3607  Task                    com.dspread.mdm.service              D  🔧 本地任务列表内容: [{"beginDate":"2024-08-18 07:06:19","taskType":"02","apkName":"Via","endDate":"9999-12-31 23:59:59","appId":"1755500779963","packName":"mark.via","versionName":"6.6.0","taskId":"1755500779963","versionCode":"20250713","request_id":"1755500779963ST001","request_time":"1755500779963","silent_install":"","taskResult":"D02","lastUpdateTime":1755763955587}]
2025-08-21 16:12:42.250  3540-3607  Task                    com.dspread.mdm.service              I  ℹ️ 当前应用版本: versionCode=22, versionName=1.1.03.20250821.DSPREAD.MDM.SERVICE
2025-08-21 16:12:42.257  3540-3607  Task                    com.dspread.mdm.service              I  ℹ️ 任务列表数量: 1
2025-08-21 16:12:42.263  3540-3607  Task                    com.dspread.mdm.service              D  🔧 检查任务[0]: taskId=1755500779963, taskType=02, pkgName=mark.via, versionCode=20250713, versionName=6.6.0, taskResult=D02
2025-08-21 16:12:42.269  3540-3607  WebSocket               com.dspread.mdm.service              I  🔧 检查OS升级状态...
2025-08-21 16:12:42.277  3540-3607  Common                  com.dspread.mdm.service              D  🔧 OsUpdateStatusChecker WebSocket连接成功，检查OS升级状态
2025-08-21 16:12:42.283  3540-3607  Common                  com.dspread.mdm.service              D  🔧 OsUpdateStatusChecker 没有待检查的OS升级任务
2025-08-21 16:12:42.289  3540-3607  WebSocket               com.dspread.mdm.service              I  🔧 OS升级状态检查已触发
2025-08-21 16:12:42.295  3540-3607  WebSocket               com.dspread.mdm.service              I  🔧 首次连接初始化检查完成
2025-08-21 16:12:42.329  3540-3607  Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"request_time":"1755763955855","org_request_time":"1755763955387","org_request_id":"1755763955387C0108","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755763955855S0000","serialNo":"01354090202503050399"}
2025-08-21 16:12:42.339  3540-3607  WebSocket               com.dspread.mdm.service              I  🔧 收到响应消息: transCode=S0000, orgRequestId=1755763955387C0108, state=0, remark=
2025-08-21 16:12:42.345  3540-3607  WebSocket               com.dspread.mdm.service              I  🔧 处理任务结果响应: state=0
2025-08-21 16:12:42.351  3540-3607  WebSocket               com.dspread.mdm.service              I  🔧 任务结果上传成功
2025-08-21 16:12:42.385  3540-3607  Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"request_time":"1755763956383","org_request_time":"1755763955614","org_request_id":"1755763955614C0108","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755763956383S0000","serialNo":"01354090202503050399"}
2025-08-21 16:12:42.394  3540-3607  WebSocket               com.dspread.mdm.service              I  🔧 收到响应消息: transCode=S0000, orgRequestId=1755763955614C0108, state=0, remark=
2025-08-21 16:12:42.400  3540-3607  WebSocket               com.dspread.mdm.service              I  🔧 处理任务结果响应: state=0
2025-08-21 16:12:42.406  3540-3607  WebSocket               com.dspread.mdm.service              I  🔧 任务结果上传成功
2025-08-21 16:12:42.441  3540-3607  Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"request_time":"1755763961477","org_request_time":"1755763961899","org_request_id":"1755763961899C0906","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755763961477S0000","serialNo":"01354090202503050399"}
2025-08-21 16:12:42.450  3540-3607  WebSocket               com.dspread.mdm.service              I  🔧 收到响应消息: transCode=S0000, orgRequestId=1755763961899C0906, state=0, remark=
2025-08-21 16:12:42.456  3540-3607  WebSocket               com.dspread.mdm.service              I  🔧 处理设备信息响应: state=0
2025-08-21 16:12:42.462  3540-3607  WebSocket               com.dspread.mdm.service              I  🔧 设备信息上传成功
2025-08-21 16:12:43.036  3540-3607  Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"request_time":"1755763961748","org_request_time":"1755763962148","org_request_id":"1755763962148C0109","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755763961748S0000","serialNo":"01354090202503050399"}
2025-08-21 16:12:43.045  3540-3607  WebSocket               com.dspread.mdm.service              I  🔧 收到响应消息: transCode=S0000, orgRequestId=1755763962148C0109, state=0, remark=
2025-08-21 16:12:43.051  3540-3607  WebSocket               com.dspread.mdm.service              I  🔧 终端信息上传成功
2025-08-21 16:12:47.365  3540-3540  Receiver                com.dspread.mdm.service              D  🔧 网络恢复，重新上传网络状态信息（防抖通过）
2025-08-21 16:12:47.387  3540-3540  WebSocket               com.dspread.mdm.service              I  🔧 C0904 被动式上送拒绝: NETWORK_RECOVERY (拒绝: 1)
2025-08-21 16:12:47.396  3540-3540  WebSocket               com.dspread.mdm.service              I  🔧 C0904 网络状态上送被流量控制阻止: 被动事件 'NETWORK_RECOVERY' 在平衡模式 - 重要变化下未启用
2025-08-21 16:13:05.143  3540-3608  WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 1 (第1个，待响应: 1)
2025-08-21 16:13:05.332  3540-3607  WebSocket               com.dspread.mdm.service              I  🔧 收到服务器 PONG 帧: 1 (待响应PING: 0)
2025-08-21 16:13:10.265  3540-3540  Receiver                com.dspread.mdm.service              D  🔧 电源已断开
2025-08-21 16:13:10.273  3540-3540  WebSocket               com.dspread.mdm.service              I  🔧 C0902 主动式上送: charging_state_change (主动: 7)
2025-08-21 16:13:10.421  3540-3540  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0902","serialNo":"01354090202503050399","request_time":"1755763990298","request_id":"1755763990298C0902","version":"1","data":{"batteryLife":100,"batteryHealth":2,"temprature":"28.7","isCharging":"0","isLowBattery":"0","powerOff":"0","outOfBattery":"0"},"myVersionName":"1.1.03.20250821.DSPREAD.MDM.SERVICE","terminalDate":"20250821161310"}
2025-08-21 16:13:10.436  3540-3540  WebSocket               com.dspread.mdm.service              I  🔧 C0902 电池状态上送成功 (1, trigger=charging_state_change)
2025-08-21 16:13:10.452  3540-3540  Receiver                com.dspread.mdm.service              D  🔧 屏幕点亮
2025-08-21 16:13:10.512  3540-3540  WebSocket               com.dspread.mdm.service              W  ⚠️ 未知触发条件: screen_on，默认按被动式处理
2025-08-21 16:13:10.541  3540-3540  WebSocket               com.dspread.mdm.service              I  🔧 C0201 被动式上送拒绝: screen_on (拒绝: 2)
2025-08-21 16:13:10.550  3540-3540  WebSocket               com.dspread.mdm.service              I  🔧 C0201 设备状态上送被流量控制阻止: 被动事件 'screen_on' 在平衡模式 - 重要变化下未启用
2025-08-21 16:13:10.558  3540-3540  Receiver                com.dspread.mdm.service              D  🔧 屏幕点亮处理完成
2025-08-21 16:13:10.669  3540-3540  getAnimDeal             com.dspread.mdm.service              I  mode:3 packageName:com.dspread.mdm.service result: false
2025-08-21 16:13:10.690   647-1290  BufferQueueDebug        pid-647                              E  [5c1dd9e Splash Screen com.dspread.mdm.service#232](this:0xaa1adc40,id:-1,api:0,p:-1,c:-1) id info cannot be read from '5c1dd9e Splash Screen com.dspread.mdm.service#232'
2025-08-21 16:13:10.695   647-1290  BufferQueueDebug        pid-647                              E  [com.dspread.mdm.service/com.dspread.mdm.service.ui.activity.TestActivity#233](this:0xaa1abc40,id:-1,api:0,p:-1,c:-1) id info cannot be read from 'com.dspread.mdm.service/com.dspread.mdm.service.ui.activity.TestActivity#233'
2025-08-21 16:13:10.737  3540-3540  BufferQueueConsumer     com.dspread.mdm.service              D  [](id:dd400000001,api:0,p:-1,c:3540) connect: controlledByApp=false
2025-08-21 16:13:10.746   647-2018  BufferQueueDebug        pid-647                              E  [Splash Screen com.dspread.mdm.service#234](this:0xaa07ec40,id:-1,api:0,p:-1,c:-1) id info cannot be read from 'Splash Screen com.dspread.mdm.service#234'
2025-08-21 16:13:10.845  3540-3569  OpenGLRenderer          com.dspread.mdm.service              E  Unable to match the desired swap behavior.
2025-08-21 16:13:10.846   647-964   BufferQueueDebug        pid-647                              E  [6aaaaa0 ActivityRecordInputSink com.dspread.mdm.service/.ui.activity.TestActivity#237](this:0xaa039c40,id:-1,api:0,p:-1,c:-1) id info cannot be read from '6aaaaa0 ActivityRecordInputSink com.dspread.mdm.service/.ui.activity.TestActivity#237'
2025-08-21 16:13:10.928  3540-3569  BLASTBufferQueue        com.dspread.mdm.service              D  [VRI[TestActivity]#1](f:0,a:1) acquireNextBufferLocked size=480x854 mFrameNumber=1 applyTransaction=true mTimestamp=900398364821(auto) mPendingTransactions.size=0 graphicBufferId=15204184227851 transform=3
2025-08-21 16:13:10.933   647-2018  BufferQueueDebug        pid-647                              E  [Surface(name=e3ff900 com.dspread.mdm.service/com.dspread.mdm.service.ui.activity.TestActivity)/@0x4ee6919 - animation-leash of starting_reveal#238](this:0xaa037c40,id:-1,api:0,p:-1,c:-1) id info cannot be read from 'Surface(name=e3ff900 com.dspread.mdm.service/com.dspread.mdm.service.ui.activity.TestActivity)/@0x4ee6919 - animation-leash of starting_reveal#238'
2025-08-21 16:13:11.231   647-964   BufferQueueDebug        pid-647                              E  [Surface(name=5c1dd9e Splash Screen com.dspread.mdm.service)/@0xd53e724 - animation-leash of window_animation#241](this:0xa9ff2c40,id:-1,api:0,p:-1,c:-1) id info cannot be read from 'Surface(name=5c1dd9e Splash Screen com.dspread.mdm.service)/@0xd53e724 - animation-leash of window_animation#241'
2025-08-21 16:13:13.426  3540-3540  Receiver                com.dspread.mdm.service              D  🔧 电源已连接
2025-08-21 16:13:13.433  3540-3540  WebSocket               com.dspread.mdm.service              I  🔧 C0902 主动式上送: charging_state_change (主动: 8)
2025-08-21 16:13:13.495  3540-3540  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0902","serialNo":"01354090202503050399","request_time":"1755763993469","request_id":"1755763993469C0902","version":"1","data":{"batteryLife":100,"batteryHealth":2,"temprature":"28.6","isCharging":"1","isLowBattery":"0","powerOff":"0","outOfBattery":"0"},"myVersionName":"1.1.03.20250821.DSPREAD.MDM.SERVICE","terminalDate":"20250821161313"}
2025-08-21 16:13:13.502  3540-3540  WebSocket               com.dspread.mdm.service              I  🔧 C0902 电池状态上送成功 (1, trigger=charging_state_change)
2025-08-21 16:13:35.144  3540-3608  WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 2 (第2个，待响应: 1)
2025-08-21 16:13:35.258  3540-3607  WebSocket               com.dspread.mdm.service              I  🔧 收到服务器 PONG 帧: 2 (待响应PING: 0)
2025-08-21 16:14:05.145  3540-3608  WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 3 (第3个，待响应: 1)
2025-08-21 16:14:05.255  3540-3607  WebSocket               com.dspread.mdm.service              I  🔧 收到服务器 PONG 帧: 3 (待响应PING: 0)
2025-08-21 16:14:13.710  3540-3569  GED                     com.dspread.mdm.service              I  ged_boost_gpu_freq, level 100, eOrigin 2, final_idx 2, oppidx_max 2, oppidx_min 0
2025-08-21 16:14:13.722  3540-3540  BLASTBufferQueue        com.dspread.mdm.service              D  [VRI[TestActivity]#1](f:0,a:1) destructor()
2025-08-21 16:14:13.722  3540-3540  BufferQueueConsumer     com.dspread.mdm.service              D  [VRI[TestActivity]#1(BLAST Consumer)1](id:dd400000001,api:0,p:-1,c:3540) disconnect
2025-08-21 16:14:13.725  3540-3569  GED                     com.dspread.mdm.service              I  ged_boost_gpu_freq, level 100, eOrigin 2, final_idx 2, oppidx_max 2, oppidx_min 0
2025-08-21 16:14:14.013  3540-3540  Receiver                com.dspread.mdm.service              D  🔧 屏幕关闭
2025-08-21 16:14:14.025  3540-3540  WebSocket               com.dspread.mdm.service              W  ⚠️ 未知触发条件: screen_off，默认按被动式处理
2025-08-21 16:14:14.048  3540-3540  WebSocket               com.dspread.mdm.service              I  🔧 C0201 被动式上送拒绝: screen_off (拒绝: 3)
2025-08-21 16:14:14.058  3540-3540  WebSocket               com.dspread.mdm.service              I  🔧 C0201 设备状态上送被流量控制阻止: 被动事件 'screen_off' 在平衡模式 - 重要变化下未启用
2025-08-21 16:14:14.066  3540-3540  Receiver                com.dspread.mdm.service              D  🔧 屏幕关闭处理完成
2025-08-21 16:14:35.146  3540-3608  WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 4 (第4个，待响应: 1)
2025-08-21 16:14:35.548  3540-3607  WebSocket               com.dspread.mdm.service              I  🔧 收到服务器 PONG 帧: 4 (待响应PING: 0)
2025-08-21 16:15:05.147  3540-3608  WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 5 (第5个，待响应: 1)
2025-08-21 16:15:05.449  3540-3607  WebSocket               com.dspread.mdm.service              I  🔧 收到服务器 PONG 帧: 5 (待响应PING: 0)
2025-08-21 16:15:35.148  3540-3608  WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 6 (第6个，待响应: 1)
2025-08-21 16:15:35.555  3540-3607  WebSocket               com.dspread.mdm.service              I  🔧 收到服务器 PONG 帧: 6 (待响应PING: 0)
2025-08-21 16:16:05.148  3540-3608  WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 7 (第7个，待响应: 1)
2025-08-21 16:16:05.353  3540-3607  WebSocket               com.dspread.mdm.service              I  🔧 收到服务器 PONG 帧: 7 (待响应PING: 0)
2025-08-21 16:16:35.149  3540-3608  WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 8 (第8个，待响应: 1)
2025-08-21 16:16:35.561  3540-3607  WebSocket               com.dspread.mdm.service              I  🔧 收到服务器 PONG 帧: 8 (待响应PING: 0)
2025-08-21 16:16:54.981  3540-3607  Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"msg_state":"P","request_time":"1755764214301","data":{"param":"","c_type":"CALLC0109"},"expire_time":1755767814,"tranCode":"SC004","request_id":"1755764214301SC004","version":"1","serialNo":"01354090202503050399"}
2025-08-21 16:16:54.994  3540-3607  WebSocket               com.dspread.mdm.service              I  🔧 准备发送响应确认 - requestId: 1755764214301SC004, needResponse: true
2025-08-21 16:16:55.029  3540-3607  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0000","serialNo":"01354090202503050399","request_time":"1755764215009","request_id":"1755764215009C0000","version":"1","org_request_id":"1755764214301SC004","org_request_time":"1755764214301","response_state":"0","myVersionName":"1.1.03.20250821.DSPREAD.MDM.SERVICE","terminalDate":"20250821161655"}
2025-08-21 16:16:55.055  3540-3607  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0000","serialNo":"01354090202503050399","request_time":"1755764215038","request_id":"1755764215038C0000","version":"1","org_request_id":"1755764214301SC004","org_request_time":"1755764214301","response_state":"0","myVersionName":"1.1.03.20250821.DSPREAD.MDM.SERVICE","terminalDate":"20250821161655"}
2025-08-21 16:16:55.062  3540-3607  WebSocket               com.dspread.mdm.service              I  🔧 已发送C0000响应确认: requestId=1755764214301SC004
2025-08-21 16:16:55.070  3540-3607  WebSocket               com.dspread.mdm.service              I  🔧 处理命令: SC004
2025-08-21 16:16:55.077  3540-3607  WebSocket               com.dspread.mdm.service              I  🔧 收到SC004命令，类型: CALLC0109
2025-08-21 16:16:55.084  3540-3607  WebSocket               com.dspread.mdm.service              I  🔧 服务器请求终端信息上传
2025-08-21 16:16:55.091  3540-3607  WebSocket               com.dspread.mdm.service              I  🔧 C0109 主动式上送: terminal_info_timer (主动: 9)
2025-08-21 16:16:55.108  3540-3607  Platform                com.dspread.mdm.service              D  🔧 重新收集应用信息（强制刷新）
2025-08-21 16:16:55.207  3540-3607  AppManager              com.dspread.mdm.service              I  ℹ️ 应用信息获取完成: 总数130(系统129/用户1) 返回1个
2025-08-21 16:16:55.219  3540-3607  Platform                com.dspread.mdm.service              D  🔧 应用信息: 1 个应用
2025-08-21 16:16:55.396  3540-3607  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0109","serialNo":"01354090202503050399","request_time":"1755764215332","request_id":"1755764215332C0109","version":"1","data":{"apkInfo":[{"packName":"com.dspread.mdm.service","apkName":"service","versionCode":22,"versionName":"1.1.03.20250821.DSPREAD.MDM.SERVICE","updateDate":"2025-08-21 16:12:28"}],"locationInfo":{"gps":{"longitude":"-999.0","latitude":"-999.0","satelliteCount":0,"satelliteCountInUsed":0,"satelliteAverSNR":"0.00","distance":"0.0","lockStatus":"1","lockMeter":"200","proId":"","proName":""},"baseSite":{"MCC":"","MNC":"","CID":"-1","LAC":"-1","DBM":"-999","IMSI":"","ICCID":""},"wifi":{"SSID":"fubox_2.4G","IP":"**************","MAC":"BE:87:C2:42:DF:75","SSTH":"-32","BSSID":"7c:10:c9:d6:9f:10"},"address":"无法获取"},"hardwareInfo":[{"name":"CPU Usage","desc":"cpu:0.50"},{"name":"storage_usage_status","desc":" 4.56GB \/ 32.00GB ","state":"1"},{"name":"total_memory","desc":" 0.67GB \/ 1.86GB ","state":"1"},{"name":"androidVersion","desc":"14","state":"1"},{"name":"network_status","desc":"wifi","state":"1"},{"name":"OSVer","desc":"14","state":"1"}],"wifiOption":[{"SSID":"fubox_5G","SSTH":"-42"},{"SSID":"2306","SSTH":"-79"},{"SSID":"2205","SSTH":"-43"},{"SSID":"2205_5G","SSTH":"-64"},{"SSID":"fubox_2.4G","SSTH":"-35"},{"SSID":"DIRECT-CC-HP Laser 1188w","SSTH":"-64"},{"SSID":"2206","SSTH":"-31"},{"SSID":"2206-5G","SSTH":"-32"},{"SSID":"CMCC-EtAF","SSTH":"-86"},{"SSID":"DIRECT-C6-Mi All-in-One Inkjet","SSTH":"-53"},{"SSID":"2207-5G","SSTH":"-73"},{"SSID":"2106-5G","SSTH":"-91"},{"SSID":"2103_5G","SSTH":"-78"},{"SSID":"@Ruijie-1816_5G","SSTH":"-71"},{"SSID":"CMCC-2203","SSTH":"-63"},{"SSID":"@Ruijie-1816","SSTH":"-46"}],"sytemInfo":{"androidVersion":"14","buildNumber":"BASE_D30M-MU_GEN_AP_V1.0.8.1_202507032000_ota_test","aspl":"2025-03-05"}},"myVersionName":"1.1.03.20250821.DSPREAD.MDM.SERVICE","terminalDate":"20250821161655"}
2025-08-21 16:16:55.403  3540-3607  WebSocket               com.dspread.mdm.service              I  🔧 C0109 终端信息上传完成 (1, trigger=terminal_info_timer)
2025-08-21 16:16:55.410  3540-3607  WebSocket               com.dspread.mdm.service              I  🔧 同时上传C0901应用信息（含服务信息）
2025-08-21 16:16:55.417  3540-3607  WebSocket               com.dspread.mdm.service              I  🔧 检查默认开启的服务
2025-08-21 16:16:55.424  3540-3607  WebSocket               com.dspread.mdm.service              I  🔧 本地无LogStream服务信息
2025-08-21 16:16:55.443  3540-3607  WebSocket               com.dspread.mdm.service              I  🔧 LogStream服务未启用，跳过保存
2025-08-21 16:16:55.449  3540-3607  WebSocket               com.dspread.mdm.service              I  🔧 默认服务检查完成
2025-08-21 16:16:55.456  3540-3607  WebSocket               com.dspread.mdm.service              I  🔧 C0901 主动式上送: command_trigger (主动: 10)
2025-08-21 16:16:55.473  3540-3607  Platform                com.dspread.mdm.service              D  🔧 使用缓存的应用信息
2025-08-21 16:16:55.500  3540-3607  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0901","serialNo":"01354090202503050399","request_time":"1755764215479","request_id":"1755764215479C0901","version":"1","data":{"apkInfo":[{"packName":"com.dspread.mdm.service","apkName":"service","versionCode":22,"versionName":"1.1.03.20250821.DSPREAD.MDM.SERVICE","updateDate":"2025-08-21 16:12:28"}],"sytemInfo":{"androidVersion":"14","buildNumber":"BASE_D30M-MU_GEN_AP_V1.0.8.1_202507032000_ota_test","aspl":"2025-03-05"},"serviceInfo":[]},"myVersionName":"1.1.03.20250821.DSPREAD.MDM.SERVICE","terminalDate":"20250821161655"}
2025-08-21 16:16:55.507  3540-3607  WebSocket               com.dspread.mdm.service              I  🔧 C0901 应用信息上送成功 (1)
2025-08-21 16:16:55.795  3540-3607  Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"request_time":"1755764214935","org_request_time":"1755764215332","org_request_id":"1755764215332C0109","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755764214935S0000","serialNo":"01354090202503050399"}
2025-08-21 16:16:55.806  3540-3607  WebSocket               com.dspread.mdm.service              I  🔧 收到响应消息: transCode=S0000, orgRequestId=1755764215332C0109, state=0, remark=
2025-08-21 16:16:55.813  3540-3607  WebSocket               com.dspread.mdm.service              I  🔧 终端信息上传成功
2025-08-21 16:17:05.150  3540-3608  WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 9 (第9个，待响应: 1)
2025-08-21 16:17:05.461  3540-3607  WebSocket               com.dspread.mdm.service              I  🔧 收到服务器 PONG 帧: 9 (待响应PING: 0)
2025-08-21 16:17:35.151  3540-3608  WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 10 (第10个，待响应: 1)
2025-08-21 16:17:35.567  3540-3607  WebSocket               com.dspread.mdm.service              I  🔧 收到服务器 PONG 帧: 10 (待响应PING: 0)
2025-08-21 16:18:05.152  3540-3608  WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 11 (第11个，待响应: 1)
2025-08-21 16:18:05.366  3540-3607  WebSocket               com.dspread.mdm.service              I  🔧 收到服务器 PONG 帧: 11 (待响应PING: 0)
2025-08-21 16:18:35.153  3540-3608  WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 12 (第12个，待响应: 1)
2025-08-21 16:18:35.575  3540-3607  WebSocket               com.dspread.mdm.service              I  🔧 收到服务器 PONG 帧: 12 (待响应PING: 0)
2025-08-21 16:19:05.154  3540-3608  WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 13 (第13个，待响应: 1)
2025-08-21 16:19:05.372  3540-3607  WebSocket               com.dspread.mdm.service              I  🔧 收到服务器 PONG 帧: 13 (待响应PING: 0)
2025-08-21 16:19:35.154  3540-3608  WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 14 (第14个，待响应: 1)
2025-08-21 16:19:35.479  3540-3607  WebSocket               com.dspread.mdm.service              I  🔧 收到服务器 PONG 帧: 14 (待响应PING: 0)
2025-08-21 16:20:05.155  3540-3608  WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 15 (第15个，待响应: 1)
2025-08-21 16:20:05.585  3540-3607  WebSocket               com.dspread.mdm.service              I  🔧 收到服务器 PONG 帧: 15 (待响应PING: 0)
2025-08-21 16:20:35.156  3540-3608  WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 16 (第16个，待响应: 1)
2025-08-21 16:20:35.386  3540-3607  WebSocket               com.dspread.mdm.service              I  🔧 收到服务器 PONG 帧: 16 (待响应PING: 0)
2025-08-21 16:21:05.157  3540-3608  WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 17 (第17个，待响应: 1)
2025-08-21 16:21:05.489  3540-3607  WebSocket               com.dspread.mdm.service              I  🔧 收到服务器 PONG 帧: 17 (待响应PING: 0)
2025-08-21 16:21:35.157  3540-3608  WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 18 (第18个，待响应: 1)
2025-08-21 16:21:35.594  3540-3607  WebSocket               com.dspread.mdm.service              I  🔧 收到服务器 PONG 帧: 18 (待响应PING: 0)
2025-08-21 16:22:05.158  3540-3608  WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 19 (第19个，待响应: 1)
2025-08-21 16:22:05.496  3540-3607  WebSocket               com.dspread.mdm.service              I  🔧 收到服务器 PONG 帧: 19 (待响应PING: 0)
2025-08-21 16:22:35.159  3540-3608  WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 20 (第20个，待响应: 1)
2025-08-21 16:22:35.294  3540-3607  WebSocket               com.dspread.mdm.service              I  🔧 收到服务器 PONG 帧: 20 (待响应PING: 0)
2025-08-21 16:23:05.160  3540-3608  WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 21 (第21个，待响应: 1)
2025-08-21 16:23:05.502  3540-3607  WebSocket               com.dspread.mdm.service              I  🔧 收到服务器 PONG 帧: 21 (待响应PING: 0)
2025-08-21 16:23:35.161  3540-3608  WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 22 (第22个，待响应: 1)
2025-08-21 16:23:35.608  3540-3607  WebSocket               com.dspread.mdm.service              I  🔧 收到服务器 PONG 帧: 22 (待响应PING: 0)
2025-08-21 16:24:05.161  3540-3608  WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 23 (第23个，待响应: 1)
2025-08-21 16:24:05.508  3540-3607  WebSocket               com.dspread.mdm.service              I  🔧 收到服务器 PONG 帧: 23 (待响应PING: 0)
2025-08-21 16:24:35.162  3540-3608  WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 24 (第24个，待响应: 1)
2025-08-21 16:24:35.883  3540-3607  WebSocket               com.dspread.mdm.service              I  🔧 收到服务器 PONG 帧: 24 (待响应PING: 0)
2025-08-21 16:25:05.163  3540-3608  WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 25 (第25个，待响应: 1)
2025-08-21 16:25:05.516  3540-3607  WebSocket               com.dspread.mdm.service              I  🔧 收到服务器 PONG 帧: 25 (待响应PING: 0)
2025-08-21 16:25:35.164  3540-3608  WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 26 (第26个，待响应: 1)
2025-08-21 16:25:35.524  3540-3607  WebSocket               com.dspread.mdm.service              I  🔧 收到服务器 PONG 帧: 26 (待响应PING: 0)
2025-08-21 16:26:05.165  3540-3608  WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 27 (第27个，待响应: 1)
2025-08-21 16:26:05.522  3540-3607  WebSocket               com.dspread.mdm.service              I  🔧 收到服务器 PONG 帧: 27 (待响应PING: 0)
2025-08-21 16:26:35.165  3540-3608  WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 28 (第28个，待响应: 1)
2025-08-21 16:26:35.525  3540-3607  WebSocket               com.dspread.mdm.service              I  🔧 收到服务器 PONG 帧: 28 (待响应PING: 0)
2025-08-21 16:27:05.166  3540-3608  WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 29 (第29个，待响应: 1)
2025-08-21 16:27:05.529  3540-3607  WebSocket               com.dspread.mdm.service              I  🔧 收到服务器 PONG 帧: 29 (待响应PING: 0)
