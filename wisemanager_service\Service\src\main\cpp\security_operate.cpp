#include <jni.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <fcntl.h>

#include "android/log.h"
static const char *TAG="wiseapp2.0";
#define LOGI(fmt, args...) __android_log_print(ANDROID_LOG_INFO,  TAG, fmt, ##args)
#define LOGD(fmt, args...) __android_log_print(ANDROID_LOG_DEBUG, TAG, fmt, ##args)
#define LOGE(fmt, args...) __android_log_print(ANDROID_LOG_ERROR, TAG, fmt, ##args)

/**
    之前生成好的签名字符串
*/
const char* RELEASE_SIGN = "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";
const char* RELEASE_SIGN_NEW = "308203a130820289a00302010202147e71f2454b774e23226ee9c19aba2a9c3d98743c300d06092a864886f70d01010b05003061310b300906035504061302484b3112301006035504080c09486f6e67204b6f6e673112301006035504070c09547375656e2057616e31163014060355040a0c0d4242504f53204c696d697465643112301006035504030c096262706f732e636f6d301e170d3231313132393033313030325a170d3439303431363033313030325a3061310b300906035504061302484b3112301006035504080c09486f6e67204b6f6e673112301006035504070c09547375656e2057616e31163014060355040a0c0d4242504f53204c696d697465643112301006035504030c096262706f732e636f6d30820120300d06092a864886f70d01010105000382010d00308201080282010100b5a5f564d71bc587982ff9febd4aa8f637b9ba0a09555ebace6d7d36cb0a6d5be60a7384effdd1be3b8c0233475bf7da30bba0e1dd642152c1bb07237a2a4a5d0ce67cab6b207c2e26926f58e9ad19d32d66115ea5da61ce08c2506eeac7b4d0ffa13b6ffcac1112803abd8ab4169cf1edfd433fece45fc12e839bde7bf308b61178c5f1cbcee6007bf1d090743c2b8cf2836ca29af65b182c12d86c9f109b87348b022d7fd6dcb864525863fb3fc101b0a60ded92874f321e6744e613fd9c95bd9708f7e13a3968168ba10dda939f0949de63c22aa0be9f30d6abf2e11bba482e6bb92b9da12c9cbe0760d65f9da493e830b9287e3e2a5995f6185b8aaf8079020103a3533051301d0603551d0e04160414673b4d34492c962ba8bf0819cc343d1ddcd6de3e301f0603551d23041830168014673b4d34492c962ba8bf0819cc343d1ddcd6de3e300f0603551d130101ff040530030101ff300d06092a864886f70d01010b05000382010100664403095e1e1065673cb27c925d781b92822475de798fe2db2bf620939d2fd934d9e0672072cab3273d363ef23563dbc70a3af1fcaa2b05cfd455f8528ad3221112cf87af948c9eb52e7a53e828ce62dbda353615b05c33085c302406fe89a38936235159e7a2959009a9a68db0dba5623fdfe762011ce9b1f8d82daed633309f4a9ef73b341f82a45106e14fe4802185847c904182c94933ee530085825d9e650324c71730865c59d81f517323575d4ed85e448edae4238f58145d6f2d0ba2887c728895c6a3b3575b2fa6aad877fd874ca02398bc32351a436271069be629c29290b90ea05d79ab6d65a5291e7389b098b97b7c3e9ff4cf03f3b92887eb0f";

/*
    根据context对象,获取签名字符串
*/
const char* getSignString(JNIEnv *env,jobject contextObject) {
    // 获得 Context 类
    jclass native_clazz = (env)->GetObjectClass(contextObject);

    jmethodID getPackageManagerId = (env)->GetMethodID(native_clazz, "getPackageManager","()Landroid/content/pm/PackageManager;");
    // 获得应用包的管理器
    jobject packageManagerObject =  (env)->CallObjectMethod(contextObject, getPackageManagerId);
    // 获得 PackageManager 类
    jclass packageManagerClazz = (env)->GetObjectClass(packageManagerObject);
    // 得到 getPackageInfo 方法的 ID
    jmethodID getPackageInfoId = (env)->GetMethodID(packageManagerClazz, "getPackageInfo", "(Ljava/lang/String;I)Landroid/content/pm/PackageInfo;");
    // 得到 getPackageName 方法的 ID
    jmethodID getPackageNameId = (env)->GetMethodID(native_clazz, "getPackageName", "()Ljava/lang/String;");
    // 获得当前应用的包名
    jstring packNameString =  (jstring)(env)->CallObjectMethod(contextObject, getPackageNameId);
    // 获得应用包的信息
    jobject packageInfoObject = (env)->CallObjectMethod(packageManagerObject, getPackageInfoId, packNameString, 64);
    jclass packageInfoClazz = env->GetObjectClass(packageInfoObject);

    jfieldID signaturefieldID =(env)->GetFieldID(packageInfoClazz,"signatures", "[Landroid/content/pm/Signature;");
    jobjectArray signatureArray = (jobjectArray)(env)->GetObjectField(packageInfoObject, signaturefieldID);
    jobject signatureObject =  (env)->GetObjectArrayElement(signatureArray,0);
    jclass signatureClazz = env->GetObjectClass(signatureObject);
    jmethodID signToStringId = (env)->GetMethodID(signatureClazz, "toCharsString","()Ljava/lang/String;");
    jstring str = static_cast<jstring>(env->CallObjectMethod(signatureObject, signToStringId));
    return (env)->GetStringUTFChars(str,0);
}

extern "C" JNIEXPORT jstring JNICALL
Java_com_bbpos_wiseapp_security_SecurityOperate_getDK(JNIEnv *env,jobject thiz,jobject contextObject) {
    const char* signStrng =  getSignString(env,contextObject);

    if(1)//strcmp(signStrng, RELEASE_SIGN)==0 || strcmp(signStrng, RELEASE_SIGN_NEW)==0)//签名一致  返回合法的 api key，否则返回错误
    {
        return (env)->NewStringUTF("HONGKONG");
    }
    else
    {
        return (env)->NewStringUTF("");
    }
}

extern "C" JNIEXPORT jstring JNICALL
Java_com_bbpos_wiseapp_security_SecurityOperate_getDIV(JNIEnv *env,jobject thiz,jobject contextObject) {
    const char* signStrng =  getSignString(env,contextObject);

    if(1)//strcmp(signStrng, RELEASE_SIGN)==0 || strcmp(signStrng, RELEASE_SIGN_NEW)==0)//签名一致  返回合法的 api key，否则返回错误
    {
        return (env)->NewStringUTF("BBPOSLTD");
    }
    else
    {
        return (env)->NewStringUTF("");
    }
}

extern "C" JNIEXPORT jstring JNICALL
Java_com_bbpos_wiseapp_security_SecurityOperate_getAESVector(JNIEnv *env,jobject thiz,jobject contextObject) {
    const char* signStrng =  getSignString(env,contextObject);

    if(1)//strcmp(signStrng, RELEASE_SIGN)==0 || strcmp(signStrng, RELEASE_SIGN_NEW)==0)//签名一致  返回合法的 api key，否则返回错误
    {
        return (env)->NewStringUTF("Fb38hpze23vTFrsz");
    }
    else
    {
        return (env)->NewStringUTF("");
    }
}

extern "C" JNIEXPORT jstring JNICALL
Java_com_bbpos_wiseapp_security_SecurityOperate_getAESSecret(JNIEnv *env,jobject thiz,jobject contextObject) {
    const char* signStrng =  getSignString(env,contextObject);

    if(1)//strcmp(signStrng, RELEASE_SIGN)==0 || strcmp(signStrng, RELEASE_SIGN_NEW)==0)//签名一致  返回合法的 api key，否则返回错误
    {
        return (env)->NewStringUTF("kq7Q8wJBFqT5cepK");
    }
    else
    {
        return (env)->NewStringUTF("");
    }
}

extern "C" JNIEXPORT jstring JNICALL
Java_com_bbpos_wiseapp_security_SecurityOperate_getS3KeyID(JNIEnv *env,jobject thiz,jobject contextObject) {
    const char* signStrng =  getSignString(env,contextObject);

    if(1)//strcmp(signStrng, RELEASE_SIGN)==0 || strcmp(signStrng, RELEASE_SIGN_NEW)==0)//签名一致  返回合法的 api key，否则返回错误
    {
        return (env)->NewStringUTF("********************");
    }
    else
    {
        return (env)->NewStringUTF("");
    }
}

extern "C" JNIEXPORT jstring JNICALL
Java_com_bbpos_wiseapp_security_SecurityOperate_getS3KeySecret(JNIEnv *env,jobject thiz,jobject contextObject) {
    const char* signStrng =  getSignString(env,contextObject);

    if(1)//strcmp(signStrng, RELEASE_SIGN)==0 || strcmp(signStrng, RELEASE_SIGN_NEW)==0)//签名一致  返回合法的 api key，否则返回错误
    {
        return (env)->NewStringUTF("T8671AvCpNNBfJmgsqX6LPlRTMQ4T+REQEaJxyvC");
    }
    else
    {
        return (env)->NewStringUTF("");
    }
}

extern "C" JNIEXPORT jstring JNICALL
Java_com_bbpos_wiseapp_security_SecurityOperate_getSPK(JNIEnv *env,jobject thiz,jobject contextObject) {
    const char* signStrng =  getSignString(env,contextObject);

    if(1)//strcmp(signStrng, RELEASE_SIGN)==0 || strcmp(signStrng, RELEASE_SIGN_NEW)==0)//签名一致  返回合法的 api key，否则返回错误
    {
        return (env)->NewStringUTF("MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCaKyZWYb1iHWCXvePbxGFp5XQFiSprNjLdXKKRw6XyqpkJWMD2DFtD20yqfOKv7SUXmwOcerf4NRwiWW/+JXDNAa/jASxsFp75oHPevoDrL2OB9fd5tJbgg1lW/bhUNhJHBoD7c4lP2VpV6/z8FmC+twrPRs1bAHtollUmX+r/TwIDAQAB");
    }
    else
    {
        return (env)->NewStringUTF("");
    }
}

extern "C" JNIEXPORT jstring JNICALL
Java_com_bbpos_wiseapp_security_SecurityOperate_getSPWD(JNIEnv *env,jobject thiz,jobject contextObject) {
    const char* signStrng =  getSignString(env,contextObject);

    if(1)//strcmp(signStrng, RELEASE_SIGN)==0 || strcmp(signStrng, RELEASE_SIGN_NEW)==0)//签名一致  返回合法的 api key，否则返回错误
    {
        return (env)->NewStringUTF("bbpos123456");
    }
    else
    {
        return (env)->NewStringUTF("");
    }
}

extern "C"
JNIEXPORT jobject JNICALL
Java_com_bbpos_wiseapp_security_SecurityOperate_execCmd(JNIEnv *env, jobject thiz, jobject contextObject, jstring cmd) {
    __android_log_print(ANDROID_LOG_DEBUG, TAG, "jni execCmd-------->");
    const char* signStrng =  getSignString(env,contextObject);
//    if(!strcmp(signStrng, RELEASE_SIGN)==0)//签名一致  返回合法的 api key，否则返回错误
//    {
//        __android_log_print(ANDROID_LOG_ERROR, TAG, "Your are not allow to perform this method [execCmd]!");
//        return nullptr;
//    }

    jclass  native_clazz = (env)->FindClass("java/lang/Runtime");
    if (native_clazz == nullptr) {
        __android_log_print(ANDROID_LOG_ERROR, TAG, "Couldn't find java/lang/Runtime");
        return nullptr;
    }
    //Runtime.getRuntime()
    jmethodID  runtime_getInstance = (env)->GetStaticMethodID(native_clazz,"getRuntime", "()Ljava/lang/Runtime;");
    jobject runtime_instance = (env)->CallStaticObjectMethod(native_clazz,runtime_getInstance);
    //Runtime.exec()
    jmethodID execMethodId = (env)->GetMethodID(native_clazz, "exec", "(Ljava/lang/String;)Ljava/lang/Process;");
    //return Process
    jobject processObj = (env)->CallObjectMethod(runtime_instance,execMethodId,cmd);

    return processObj;
}

extern "C" JNIEXPORT void JNICALL
Java_com_bbpos_wiseapp_security_SecurityOperate_changeFileMode(JNIEnv *env, jobject thiz, jobject contextObject, jstring cmd) {
    __android_log_print(ANDROID_LOG_DEBUG, TAG, "jni execCmd-------->");
    const char* signStrng =  getSignString(env,contextObject);
//    if(!strcmp(signStrng, RELEASE_SIGN)==0)//签名一致  返回合法的 api key，否则返回错误
//    {
//        __android_log_print(ANDROID_LOG_ERROR, TAG, "Your are not allow to perform this method [execCmd]!");
//        return;
//    }

    jclass  native_clazz = (env)->FindClass("java/lang/Runtime");
    if (native_clazz == nullptr) {
        __android_log_print(ANDROID_LOG_ERROR, TAG, "Couldn't find java/lang/Runtime");
        return;
    }
    //Runtime.getRuntime()
    jmethodID  runtime_getInstance = (env)->GetStaticMethodID(native_clazz,"getRuntime", "()Ljava/lang/Runtime;");
    jobject runtime_instance = (env)->CallStaticObjectMethod(native_clazz,runtime_getInstance);
    //Runtime.exec()
    jmethodID execMethodId = (env)->GetMethodID(native_clazz, "exec", "(Ljava/lang/String;)Ljava/lang/Process;");
    //return Process

    char cmdStr[1024] = {0};
    const char *c_str = NULL;
    jboolean isCopy;	// 返回JNI_TRUE表示原字符串的拷贝。返回JNI_FALSE表示返回原字符串的指针
    c_str = (env)->GetStringUTFChars(cmd, 0);
    snprintf(cmdStr, sizeof(cmdStr), "chmod 666 %s", c_str);

    jstring cmdString = (env)->NewStringUTF(cmdStr);
    (env)->CallObjectMethod(runtime_instance,execMethodId, cmdString);
    (env)->DeleteLocalRef (cmdString);
}