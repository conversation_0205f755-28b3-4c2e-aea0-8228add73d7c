2025-08-14 18:32:14.391 19136-19207 Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"msg_state":"P","request_time":"1755167532897","data":{"ruleList":[{"deleteAppList":[],"modifyDate":"2025-08-14 10:32:12","endDate":"9999-12-31 23:59:59","restartAfter":"0","installBy":"0","beginDate":"2024-08-14 10:32:12","appList":[{"apkMd5":"0d1225260d03e10a8ffc8409369c442a","apkName":"Via","appId":"353795033bc0410dac6dee5cb8702b06","apkSize":"2565110","appIconUrl":"https://siot-bucket01.s3.ap-northeast-1.amazonaws.com/icon/88cceb7e7cae4b8792791b5eb9d04c47ic_launcher.png","packName":"mark.via","versionName":"6.6.0","versionCode":"20250713","url":"https://siot-bucket01.s3.ap-northeast-1.amazonaws.com/app/4d256c4123b346fd817537b491342204.apk"}],"serviceList":[],"action":"A","ruleName":"d60_test_295","ruleId":"937eccc7e4ff4fc0bc9eaf2788685515","createDate":"2025-08-14 10:32:12","orgRuleId":""}]},"tranCode":"ST005","request_id":"1755167532897ST005","version":"1","serialNo":"01354090202503050399"}
2025-08-14 18:32:14.401 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 准备发送响应确认 - requestId: 1755167532897ST005, needResponse: true
2025-08-14 18:32:14.421 19136-19207 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0000","serialNo":"01354090202503050399","request_time":"1755167534407","request_id":"1755167534407C0000","version":"1","org_request_id":"1755167532897ST005","org_request_time":"1755167532897","response_state":"0","myVersionName":"1.0.08.20250814.DSPREAD.MDM.SERVICE","terminalDate":"20250814183214"}
2025-08-14 18:32:14.445 19136-19207 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0000","serialNo":"01354090202503050399","request_time":"1755167534431","request_id":"1755167534431C0000","version":"1","org_request_id":"1755167532897ST005","org_request_time":"1755167532897","response_state":"0","myVersionName":"1.0.08.20250814.DSPREAD.MDM.SERVICE","terminalDate":"20250814183214"}
2025-08-14 18:32:14.451 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 已发送C0000响应确认: requestId=1755167532897ST005
2025-08-14 18:32:14.461 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleHandler 处理规则消息: ST005
2025-08-14 18:32:14.467 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleHandler 处理规则列表，数量: 1
2025-08-14 18:32:14.474 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleHandler 处理规则: ruleId=937eccc7e4ff4fc0bc9eaf2788685515, action=A
2025-08-14 18:32:14.485 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析规则JSON: ruleId=937eccc7e4ff4fc0bc9eaf2788685515
2025-08-14 18:32:14.495 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule appList数组: [{"apkMd5":"0d1225260d03e10a8ffc8409369c442a","apkName":"Via","appId":"353795033bc0410dac6dee5cb8702b06","apkSize":"2565110","appIconUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/icon\/88cceb7e7cae4b8792791b5eb9d04c47ic_launcher.png","packName":"mark.via","versionName":"6.6.0","versionCode":"20250713","url":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/4d256c4123b346fd817537b491342204.apk"}]
2025-08-14 18:32:14.502 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule deleteAppList数组: []
2025-08-14 18:32:14.510 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=mark.via, apkName=Via
2025-08-14 18:32:14.516 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后appList数量: 1
2025-08-14 18:32:14.522 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后deleteAppList数量: 0
2025-08-14 18:32:14.530 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleBaseStorage 处理规则: 937eccc7e4ff4fc0bc9eaf2788685515, 操作: A
2025-08-14 18:32:14.536 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 验证规则: ruleId=937eccc7e4ff4fc0bc9eaf2788685515, ruleType=app_management, action=A
2025-08-14 18:32:14.543 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule appList数量: 1, deleteAppList数量: 0
2025-08-14 18:32:14.566 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 验证应用管理规则: action=A
2025-08-14 18:32:14.573 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 验证应用列表: appList=1, deleteAppList=0
2025-08-14 18:32:14.580 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 应用列表验证通过
2025-08-14 18:32:14.596 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleBaseStorage 保存 1 个规则到存储
2025-08-14 18:32:14.602 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleBaseStorage Rule 937eccc7e4ff4fc0bc9eaf2788685515 添加成功
2025-08-14 18:32:14.608 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleBaseStorage 触发规则执行: 937eccc7e4ff4fc0bc9eaf2788685515
2025-08-14 18:32:14.616 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 RuleBaseStorage 设备空闲，立即执行规则: 937eccc7e4ff4fc0bc9eaf2788685515
2025-08-14 18:32:14.616 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleHandler 规则处理成功: 937eccc7e4ff4fc0bc9eaf2788685515
2025-08-14 18:32:14.624 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 RuleBaseManager 直接执行规则: 937eccc7e4ff4fc0bc9eaf2788685515
2025-08-14 18:32:14.624 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleHandler 规则列表处理完成: 处理完成 - 成功: 1, 失败: 0, 忽略: 0
2025-08-14 18:32:14.631 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 RuleBaseManager 处理规则: 937eccc7e4ff4fc0bc9eaf2788685515, 操作: A
2025-08-14 18:32:14.631 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleHandler 上报规则处理结果: 0 - 处理完成 - 成功: 1, 失败: 0, 忽略: 0
2025-08-14 18:32:14.638 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 C0107 主动式上送: rulebase_update (主动: 3)
2025-08-14 18:32:14.639 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析规则JSON: ruleId=937eccc7e4ff4fc0bc9eaf2788685515
2025-08-14 18:32:14.649 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 Rule appList数组: [{"packName":"mark.via","apkName":"Via","versionName":"6.6.0","versionCode":"20250713","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/4d256c4123b346fd817537b491342204.apk","apkMd5":"0d1225260d03e10a8ffc8409369c442a","apkSize":2565110,"installBy":"0","isSystemApp":false,"priority":0}]
2025-08-14 18:32:14.656 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 Rule deleteAppList数组: []
2025-08-14 18:32:14.660 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 添加规则结果: ruleId=batch_rules
2025-08-14 18:32:14.662 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=mark.via, apkName=Via
2025-08-14 18:32:14.668 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后appList数量: 1
2025-08-14 18:32:14.675 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后deleteAppList数量: 0
2025-08-14 18:32:14.676 19136-19207 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0107","serialNo":"01354090202503050399","request_time":"1755167534654","request_id":"1755167534654C0107","version":"1","data":{"ruleId":"batch_rules","taskResult":"0"},"myVersionName":"1.0.08.20250814.DSPREAD.MDM.SERVICE","terminalDate":"20250814183214","org_request_id":"1755167532897ST005","org_request_time":"1755167532897"}
2025-08-14 18:32:14.681 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 RuleBaseStorage 处理规则: 937eccc7e4ff4fc0bc9eaf2788685515, 操作: A
2025-08-14 18:32:14.683 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 规则结果实时上送: ruleId=batch_rules, result=0 (1)
2025-08-14 18:32:14.688 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 Rule 验证规则: ruleId=937eccc7e4ff4fc0bc9eaf2788685515, ruleType=app_management, action=A
2025-08-14 18:32:14.694 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 Rule appList数量: 1, deleteAppList数量: 0
2025-08-14 18:32:14.717 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 Rule 验证应用管理规则: action=A
2025-08-14 18:32:14.723 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 Rule 验证应用列表: appList=1, deleteAppList=0
2025-08-14 18:32:14.730 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 Rule 应用列表验证通过
2025-08-14 18:32:14.741 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析规则JSON: ruleId=937eccc7e4ff4fc0bc9eaf2788685515
2025-08-14 18:32:14.751 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 Rule appList数组: [{"packName":"mark.via","apkName":"Via","versionName":"6.6.0","versionCode":"20250713","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/4d256c4123b346fd817537b491342204.apk","apkMd5":"0d1225260d03e10a8ffc8409369c442a","apkSize":2565110,"installBy":"0","isSystemApp":false,"priority":0}]
2025-08-14 18:32:14.757 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 Rule deleteAppList数组: []
2025-08-14 18:32:14.763 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=mark.via, apkName=Via
2025-08-14 18:32:14.770 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后appList数量: 1
2025-08-14 18:32:14.776 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后deleteAppList数量: 0
2025-08-14 18:32:14.783 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 RuleBaseStorage Rule 937eccc7e4ff4fc0bc9eaf2788685515 已存在，忽略Add操作
2025-08-14 18:32:14.791 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 RuleStateMachine 规则初始化成功: 937eccc7e4ff4fc0bc9eaf2788685515 -> RuleState(code=todo, description=等待执行)
2025-08-14 18:32:14.796 19136-19207 Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"request_time":"1755167533804","org_request_time":"1755167534654","org_request_id":"1755167534654C0107","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755167533804S0000","serialNo":"01354090202503050399"}
2025-08-14 18:32:14.798 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 RuleStateMachine 规则已存在: 937eccc7e4ff4fc0bc9eaf2788685515, 当前状态: RuleState(code=todo, description=等待执行)
2025-08-14 18:32:14.805 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 创建应用状态: packName=mark.via, apkName=Via
2025-08-14 18:32:14.805 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 收到响应消息: transCode=S0000, orgRequestId=1755167534654C0107, state=0, remark=
2025-08-14 18:32:14.812 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 处理规则结果响应: state=0
2025-08-14 18:32:14.813 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 初始化规则应用状态: 937eccc7e4ff4fc0bc9eaf2788685515, 应用数量: 1
2025-08-14 18:32:14.819 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 规则结果上传成功
2025-08-14 18:32:14.820 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 RuleStateMonitor 收到状态变化: 937eccc7e4ff4fc0bc9eaf2788685515, todo -> R01
2025-08-14 18:32:14.827 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 RuleStateMonitor 状态变化已记录: 937eccc7e4ff4fc0bc9eaf2788685515, R01
2025-08-14 18:32:14.833 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 RuleStateMachine 状态转换成功: 937eccc7e4ff4fc0bc9eaf2788685515, todo -> R01
2025-08-14 18:32:14.840 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 RuleStateMonitor 收到状态变化: 937eccc7e4ff4fc0bc9eaf2788685515, R01 -> R02
2025-08-14 18:32:14.846 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 RuleStateMonitor 状态变化已记录: 937eccc7e4ff4fc0bc9eaf2788685515, R02
2025-08-14 18:32:14.853 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 RuleStateMachine 状态转换成功: 937eccc7e4ff4fc0bc9eaf2788685515, R01 -> R02
2025-08-14 18:32:14.859 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 开始执行规则: 937eccc7e4ff4fc0bc9eaf2788685515
2025-08-14 18:32:14.865 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 执行安装应用，数量: 1
2025-08-14 18:32:14.872 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 开始安装应用，数量: 1
2025-08-14 18:32:14.878 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 安装应用: mark.via, URL: https://siot-bucket01.s3.ap-northeast-1.amazonaws.com/app/4d256c4123b346fd817537b491342204.apk
2025-08-14 18:32:14.885 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 检查应用安装状态: mark.via
2025-08-14 18:32:14.892 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 应用是否已安装: false
2025-08-14 18:32:14.899 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 应用未安装，需要安装
2025-08-14 18:32:14.905 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 更新应用状态: 937eccc7e4ff4fc0bc9eaf2788685515, mark.via -> A01
2025-08-14 18:32:14.912 19136-19165 WebSocket               com.dspread.mdm.service              I  🔧 C0107 主动式上送: rulebase_update (主动: 4)
2025-08-14 18:32:14.932 19136-19165 WebSocket               com.dspread.mdm.service              I  🔧 添加规则结果: ruleId=937eccc7e4ff4fc0bc9eaf2788685515
2025-08-14 18:32:14.955 19136-19165 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0107","serialNo":"01354090202503050399","request_time":"1755167534927","request_id":"1755167534927C0107","version":"1","data":{"ruleId":"937eccc7e4ff4fc0bc9eaf2788685515","taskResult":"R02","failedApkList":[{"packName":"mark.via","apkName":"Via","versionName":"6.6.0","versionCode":"20250713","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/4d256c4123b346fd817537b491342204.apk","apkMd5":"0d1225260d03e10a8ffc8409369c442a","apkSize":2565110,"installBy":"0","isSystemApp":false,"priority":0,"result":"A01","taskType":"01"}]},"myVersionName":"1.0.08.20250814.DSPREAD.MDM.SERVICE","terminalDate":"20250814183214"}
2025-08-14 18:32:14.962 19136-19165 WebSocket               com.dspread.mdm.service              I  🔧 规则结果实时上送: ruleId=937eccc7e4ff4fc0bc9eaf2788685515, result=R02 (1)
2025-08-14 18:32:14.968 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 上报规则执行中状态: 937eccc7e4ff4fc0bc9eaf2788685515, 应用数量: 1
2025-08-14 18:32:14.975 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 规则执行启动成功: 937eccc7e4ff4fc0bc9eaf2788685515
2025-08-14 18:32:14.977 19136-19246 HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 尝试下载，第1次
2025-08-14 18:32:14.982 19136-19165 RuleBase                com.dspread.mdm.service              D  🔧 RuleBaseManager 规则添加成功: 937eccc7e4ff4fc0bc9eaf2788685515
2025-08-14 18:32:14.983 19136-19246 HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 开始下载
2025-08-14 18:32:14.990 19136-19246 HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader URL: https://siot-bucket01.s3.ap-northeast-1.amazonaws.com/app/4d256c4123b346fd817537b491342204.apk
2025-08-14 18:32:14.996 19136-19246 HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 路径: /data/user/0/com.dspread.mdm.service/files/downloads/rule_937eccc7e4ff4fc0bc9eaf2788685515_mark.via.apk
2025-08-14 18:32:15.004 19136-19246 HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 本地文件不存在，创建文件
2025-08-14 18:32:15.011 19136-19246 HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader allFileLength=2565110, 需要从服务器获取文件大小
2025-08-14 18:32:15.143 19136-19246 TrafficStats            com.dspread.mdm.service              D  tagSocket(98) with statsTag=0xffffffff, statsUid=-1
2025-08-14 18:32:15.256 19136-19207 Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"request_time":"1755167534078","org_request_time":"1755167534927","org_request_id":"1755167534927C0107","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755167534078S0000","serialNo":"01354090202503050399"}
2025-08-14 18:32:15.266 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 收到响应消息: transCode=S0000, orgRequestId=1755167534927C0107, state=0, remark=
2025-08-14 18:32:15.272 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 处理规则结果响应: state=0
2025-08-14 18:32:15.279 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 规则结果上传成功
2025-08-14 18:32:15.749 19136-19246 HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader HTTP响应码: 200
2025-08-14 18:32:15.758 19136-19246 HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 服务器返回Content-Length: 2565110
2025-08-14 18:32:15.765 19136-19246 HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader localFile = true /data/user/0/com.dspread.mdm.service/files/downloads/rule_937eccc7e4ff4fc0bc9eaf2788685515_mark.via.apk
2025-08-14 18:32:15.783 19136-19246 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 下载进度: mark.via - 0%
2025-08-14 18:32:16.182 19136-19246 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 下载进度: mark.via - 10%
2025-08-14 18:32:16.500 19136-19246 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 下载进度: mark.via - 20%
2025-08-14 18:32:16.876 19136-19246 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 下载进度: mark.via - 30%
2025-08-14 18:32:17.178 19136-19246 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 下载进度: mark.via - 40%
2025-08-14 18:32:17.544 19136-19246 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 下载进度: mark.via - 50%
2025-08-14 18:32:17.863 19136-19246 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 下载进度: mark.via - 60%
2025-08-14 18:32:18.138 19136-19246 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 下载进度: mark.via - 70%
2025-08-14 18:32:18.398 19136-19246 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 下载进度: mark.via - 80%
2025-08-14 18:32:18.752 19136-19246 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 下载进度: mark.via - 90%
2025-08-14 18:32:19.014 19136-19246 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 下载进度: mark.via - 100%
2025-08-14 18:32:19.025 19136-19246 HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 计算MD5: rule_937eccc7e4ff4fc0bc9eaf2788685515_mark.via.apk
2025-08-14 18:32:19.075 19136-19246 HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 文件: 2565110 MD5: 0d1225260d03e10a8ffc8409369c442a
2025-08-14 18:32:19.081 19136-19246 HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader MD5校验正确
2025-08-14 18:32:19.088 19136-19246 HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 清理连接资源
2025-08-14 18:32:19.095 19136-19246 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine APK下载成功: /data/user/0/com.dspread.mdm.service/files/downloads/rule_937eccc7e4ff4fc0bc9eaf2788685515_mark.via.apk
2025-08-14 18:32:19.102 19136-19246 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 更新应用状态: 937eccc7e4ff4fc0bc9eaf2788685515, mark.via -> A03
2025-08-14 18:32:19.109 19136-19246 WebSocket               com.dspread.mdm.service              I  🔧 C0107 主动式上送: rulebase_update (主动: 5)
2025-08-14 18:32:19.134 19136-19246 WebSocket               com.dspread.mdm.service              I  🔧 添加规则结果: ruleId=937eccc7e4ff4fc0bc9eaf2788685515
2025-08-14 18:32:19.158 19136-19246 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0107","serialNo":"01354090202503050399","request_time":"1755167539124","request_id":"1755167539124C0107","version":"1","data":{"ruleId":"937eccc7e4ff4fc0bc9eaf2788685515","taskResult":"R02","failedApkList":[{"packName":"mark.via","apkName":"Via","versionName":"6.6.0","versionCode":"20250713","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/4d256c4123b346fd817537b491342204.apk","apkMd5":"0d1225260d03e10a8ffc8409369c442a","apkSize":2565110,"installBy":"0","isSystemApp":false,"priority":0,"result":"A03","taskType":"01"}]},"myVersionName":"1.0.08.20250814.DSPREAD.MDM.SERVICE","terminalDate":"20250814183219"}
2025-08-14 18:32:19.165 19136-19246 WebSocket               com.dspread.mdm.service              I  🔧 规则结果实时上送: ruleId=937eccc7e4ff4fc0bc9eaf2788685515, result=R02 (1)
2025-08-14 18:32:19.172 19136-19246 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 上报规则执行中状态: 937eccc7e4ff4fc0bc9eaf2788685515, 应用数量: 1
2025-08-14 18:32:19.178 19136-19246 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 开始安装APK: /data/user/0/com.dspread.mdm.service/files/downloads/rule_937eccc7e4ff4fc0bc9eaf2788685515_mark.via.apk
2025-08-14 18:32:19.185 19136-19246 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 更新应用状态: 937eccc7e4ff4fc0bc9eaf2788685515, mark.via -> B02
2025-08-14 18:32:19.192 19136-19246 WebSocket               com.dspread.mdm.service              I  🔧 C0107 主动式上送: rulebase_update (主动: 6)
2025-08-14 18:32:19.212 19136-19246 WebSocket               com.dspread.mdm.service              I  🔧 添加规则结果: ruleId=937eccc7e4ff4fc0bc9eaf2788685515
2025-08-14 18:32:19.236 19136-19246 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0107","serialNo":"01354090202503050399","request_time":"1755167539207","request_id":"1755167539207C0107","version":"1","data":{"ruleId":"937eccc7e4ff4fc0bc9eaf2788685515","taskResult":"R02","failedApkList":[{"packName":"mark.via","apkName":"Via","versionName":"6.6.0","versionCode":"20250713","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/4d256c4123b346fd817537b491342204.apk","apkMd5":"0d1225260d03e10a8ffc8409369c442a","apkSize":2565110,"installBy":"0","isSystemApp":false,"priority":0,"result":"B02","taskType":"01"}]},"myVersionName":"1.0.08.20250814.DSPREAD.MDM.SERVICE","terminalDate":"20250814183219"}
2025-08-14 18:32:19.242 19136-19246 WebSocket               com.dspread.mdm.service              I  🔧 规则结果实时上送: ruleId=937eccc7e4ff4fc0bc9eaf2788685515, result=R02 (1)
2025-08-14 18:32:19.249 19136-19246 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 上报规则执行中状态: 937eccc7e4ff4fc0bc9eaf2788685515, 应用数量: 1
2025-08-14 18:32:19.256 19136-19246 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 注册安装回调: mark.via (规则ID: 937eccc7e4ff4fc0bc9eaf2788685515)
2025-08-14 18:32:19.265 19136-19246 AppManager              com.dspread.mdm.service              I  ℹ️ 创建SessionCallback
2025-08-14 18:32:19.272 19136-19246 AppManager              com.dspread.mdm.service              I  ℹ️ 注册SessionCallback
2025-08-14 18:32:19.284 19136-19207 Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"request_time":"1755167538281","org_request_time":"1755167539124","org_request_id":"1755167539124C0107","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755167538281S0000","serialNo":"01354090202503050399"}
2025-08-14 18:32:19.294 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 收到响应消息: transCode=S0000, orgRequestId=1755167539124C0107, state=0, remark=
2025-08-14 18:32:19.295 19136-19246 PackageParser           com.dspread.mdm.service              W  Unknown element under <manifest>: queries at /data/user/0/com.dspread.mdm.service/files/downloads/rule_937eccc7e4ff4fc0bc9eaf2788685515_mark.via.apk Binary XML file line #65
2025-08-14 18:32:19.301 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 处理规则结果响应: state=0
2025-08-14 18:32:19.307 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 规则结果上传成功
2025-08-14 18:32:19.321 19136-19246 AppManager              com.dspread.mdm.service              I  ℹ️ 准备安装: mark.via
2025-08-14 18:32:19.341 19136-19246 PackageParser           com.dspread.mdm.service              W  Unknown element under <manifest>: queries at /data/user/0/com.dspread.mdm.service/files/downloads/rule_937eccc7e4ff4fc0bc9eaf2788685515_mark.via.apk Binary XML file line #65
2025-08-14 18:32:19.365 19136-19246 AppManager              com.dspread.mdm.service              I  ℹ️ APK验证通过: mark.via v6.6.0(20250713) 2504KB
2025-08-14 18:32:19.375 19136-19246 AppManager              com.dspread.mdm.service              I  ℹ️ 创建Session: sessionId=1732478518
2025-08-14 18:32:19.393 19136-19207 Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"request_time":"1755167538362","org_request_time":"1755167539207","org_request_id":"1755167539207C0107","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755167538362S0000","serialNo":"01354090202503050399"}
2025-08-14 18:32:19.403 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 收到响应消息: transCode=S0000, orgRequestId=1755167539207C0107, state=0, remark=
2025-08-14 18:32:19.500 19136-19246 AppManager              com.dspread.mdm.service              I  ℹ️ PackageInstaller installation committed: sessionId=1732478518
2025-08-14 18:32:19.513 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 处理规则结果响应: state=0
2025-08-14 18:32:19.517  1235-1305  PackageInstallerSession system_server                        E  com.dspread.mdm.service drops manifest attribute android:installLocation in base.apk for mark.via
2025-08-14 18:32:19.518 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 规则结果上传成功
2025-08-14 18:32:19.523 19136-19136 AppManager              com.dspread.mdm.service              I  ℹ️ 安装进度: 90%
2025-08-14 18:32:20.785 19136-19136 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 收到广播: android.intent.action.PACKAGE_ADDED
2025-08-14 18:32:20.809 19136-19136 Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 收到包更新广播: android.intent.action.PACKAGE_ADDED, 包名: mark.via
2025-08-14 18:32:20.815 19136-19136 Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 应用安装: mark.via
2025-08-14 18:32:20.822 19136-19136 Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 检查WebSocket状态: packageName=mark.via, action=INSTALL
2025-08-14 18:32:20.865 19136-19136 Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 通知rulebase处理应用安装: mark.via
2025-08-14 18:32:20.881 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 收到系统广播，应用已安装: mark.via
2025-08-14 18:32:20.891 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 找到匹配的安装回调，触发: mark.via
2025-08-14 18:32:20.897 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 安装回调被触发: pkg=mark.via, returnCode=1, error=
2025-08-14 18:32:20.902 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 应用安装启动成功: mark.via
2025-08-14 18:32:20.909 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 从广播更新应用状态: 937eccc7e4ff4fc0bc9eaf2788685515, mark.via -> B03
2025-08-14 18:32:20.914 19136-19136 WebSocket               com.dspread.mdm.service              I  🔧 C0107 主动式上送: rulebase_update (主动: 7)
2025-08-14 18:32:20.939 19136-19136 WebSocket               com.dspread.mdm.service              I  🔧 添加规则结果: ruleId=937eccc7e4ff4fc0bc9eaf2788685515
2025-08-14 18:32:20.964 19136-19136 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0107","serialNo":"01354090202503050399","request_time":"1755167540931","request_id":"1755167540931C0107","version":"1","data":{"ruleId":"937eccc7e4ff4fc0bc9eaf2788685515","taskResult":"R02","failedApkList":[{"packName":"mark.via","apkName":"Via","versionName":"6.6.0","versionCode":"20250713","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/4d256c4123b346fd817537b491342204.apk","apkMd5":"0d1225260d03e10a8ffc8409369c442a","apkSize":2565110,"installBy":"0","isSystemApp":false,"priority":0,"result":"B03","taskType":"01"}]},"myVersionName":"1.0.08.20250814.DSPREAD.MDM.SERVICE","terminalDate":"20250814183220"}
2025-08-14 18:32:20.971 19136-19136 WebSocket               com.dspread.mdm.service              I  🔧 规则结果实时上送: ruleId=937eccc7e4ff4fc0bc9eaf2788685515, result=R02 (1)
2025-08-14 18:32:20.976 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 上报规则执行中状态: 937eccc7e4ff4fc0bc9eaf2788685515, 应用数量: 1
2025-08-14 18:32:20.982 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 开始检查规则完成状态: 937eccc7e4ff4fc0bc9eaf2788685515, 总应用数: 1
2025-08-14 18:32:20.988 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 检查应用状态: mark.via -> B03
2025-08-14 18:32:20.994 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 发现成功状态: mark.via -> B03
2025-08-14 18:32:21.000 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 规则完成检查结果: 937eccc7e4ff4fc0bc9eaf2788685515, 全部完成: true, 完成数: 1/1, 有失败: false
2025-08-14 18:32:21.006 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 规则执行全部成功，上送RULEBASED_SUCCESS: 937eccc7e4ff4fc0bc9eaf2788685515
2025-08-14 18:32:21.013 19136-19136 WebSocket               com.dspread.mdm.service              I  🔧 C0107 主动式上送: rulebase_update (主动: 8)
2025-08-14 18:32:21.039 19136-19136 WebSocket               com.dspread.mdm.service              I  🔧 添加规则结果: ruleId=937eccc7e4ff4fc0bc9eaf2788685515
2025-08-14 18:32:21.077 19136-19136 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0107","serialNo":"01354090202503050399","request_time":"1755167541031","request_id":"1755167541031C0107","version":"1","data":{"ruleId":"937eccc7e4ff4fc0bc9eaf2788685515","taskResult":"R03","failedApkList":[{"packName":"mark.via","apkName":"Via","versionName":"6.6.0","versionCode":"20250713","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/4d256c4123b346fd817537b491342204.apk","apkMd5":"0d1225260d03e10a8ffc8409369c442a","apkSize":2565110,"installBy":"0","isSystemApp":false,"priority":0,"result":"B03","taskType":"01"}]},"myVersionName":"1.0.08.20250814.DSPREAD.MDM.SERVICE","terminalDate":"20250814183221"}
2025-08-14 18:32:21.083 19136-19136 WebSocket               com.dspread.mdm.service              I  🔧 规则结果实时上送: ruleId=937eccc7e4ff4fc0bc9eaf2788685515, result=R03 (1)
2025-08-14 18:32:21.089 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 上报规则状态: 937eccc7e4ff4fc0bc9eaf2788685515 -> R03
2025-08-14 18:32:21.095 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleStateMonitor 收到状态变化: 937eccc7e4ff4fc0bc9eaf2788685515, R02 -> R03
2025-08-14 18:32:21.100 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleStateMonitor 状态变化已记录: 937eccc7e4ff4fc0bc9eaf2788685515, R03
2025-08-14 18:32:21.106 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleStateMachine 状态转换成功: 937eccc7e4ff4fc0bc9eaf2788685515, R02 -> R03
2025-08-14 18:32:21.112 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 规则执行完成: 937eccc7e4ff4fc0bc9eaf2788685515, 有失败: false
2025-08-14 18:32:21.118 19136-19136 Platform                com.dspread.mdm.service              D  🔧 重新收集应用信息（强制刷新）
2025-08-14 18:32:21.238 19136-19136 AppManager              com.dspread.mdm.service              I  ℹ️ 应用信息获取完成: 总数133(系统129/用户4) 返回4个
2025-08-14 18:32:21.262 19136-19207 Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"request_time":"1755167540199","org_request_time":"1755167541031","org_request_id":"1755167541031C0107","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755167540199S0000","serialNo":"01354090202503050399"}
2025-08-14 18:32:21.269 19136-19136 Platform                com.dspread.mdm.service              D  🔧 应用信息: 4 个应用
2025-08-14 18:32:21.271 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 收到响应消息: transCode=S0000, orgRequestId=1755167541031C0107, state=0, remark=
2025-08-14 18:32:21.274 19136-19136 Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 应用信息缓存刷新完成
2025-08-14 18:32:21.276 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 处理规则结果响应: state=0
2025-08-14 18:32:21.280 19136-19136 ContextImpl             com.dspread.mdm.service              W  Calling a method in the system process without a qualified user: android.app.ContextImpl.sendBroadcast:1220 android.content.ContextWrapper.sendBroadcast:504 com.dspread.mdm.service.broadcast.core.BroadcastSender.sendBroadcast:99 com.dspread.mdm.service.broadcast.core.BroadcastSender.sendBroadcast:23 com.dspread.mdm.service.broadcast.handlers.system.PackageUpdateEventHandlerImpl.sendAppRefreshBroadcast:491 
2025-08-14 18:32:21.282 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 规则结果上传成功
2025-08-14 18:32:21.283 19136-19136 Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 发送应用刷新广播
2025-08-14 18:32:21.318 19136-19207 Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"request_time":"1755167540112","org_request_time":"1755167540931","org_request_id":"1755167540931C0107","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755167540112S0000","serialNo":"01354090202503050399"}
2025-08-14 18:32:21.326 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 收到响应消息: transCode=S0000, orgRequestId=1755167540931C0107, state=0, remark=
2025-08-14 18:32:21.331 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 处理规则结果响应: state=0
2025-08-14 18:32:21.336 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 规则结果上传成功
2025-08-14 18:32:42.097 19136-19208 WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 3
2025-08-14 18:32:42.218 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 收到服务器 PONG 帧: 3
2025-08-14 18:32:42.285 19136-19136 Common                  com.dspread.mdm.service              I  ✅ ⏰ 设置任务执行定时器成功，下次执行: 60秒后 (1分钟)
