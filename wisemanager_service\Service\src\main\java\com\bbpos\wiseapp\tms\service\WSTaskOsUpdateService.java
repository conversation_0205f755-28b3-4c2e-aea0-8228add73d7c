package com.bbpos.wiseapp.tms.service;

import android.app.Dialog;
import android.app.NotificationManager;
import android.bbpos.ISystemUpdateCallback;
import android.bbpos.SystemUpdateConstants;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import android.net.Uri;
import android.os.Handler;
import android.os.Looper;
import android.os.RecoverySystem;
import android.os.RemoteException;
import android.preference.PreferenceManager;
import android.text.TextUtils;
import android.view.View;
import android.view.WindowManager;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.core.app.NotificationCompat;

import com.bbpos.wiseapp.logger.BBLog;
import com.bbpos.wiseapp.security.SecurityOperate;
import com.bbpos.wiseapp.service.R;
import com.bbpos.wiseapp.service.WakeLockService;
import com.bbpos.wiseapp.service.common.UsualData;
import com.bbpos.wiseapp.system.api.CustomServiceManager;
import com.bbpos.wiseapp.system.api.DeviceInfoApi;
import com.bbpos.wiseapp.system.api.SystemApi;
import com.bbpos.wiseapp.tms.adapter.SystemManagerAdapter;
import com.bbpos.wiseapp.tms.network.HttpUtils;
import com.bbpos.wiseapp.tms.utils.Constants;
import com.bbpos.wiseapp.tms.utils.ContextUtil;
import com.bbpos.wiseapp.tms.utils.Helpers;
import com.bbpos.wiseapp.tms.utils.IOUtils;
import com.bbpos.wiseapp.tms.utils.ParameterName;
import com.bbpos.wiseapp.tms.utils.SPKeys;
import com.bbpos.wiseapp.tms.utils.TaskState;
import com.bbpos.wiseapp.tms.widget.MOSUpgradeDialog;
import com.bbpos.wiseapp.tms.widget.MRebootEventDialog;
import com.bbpos.wiseapp.utils.SharedPreferencesUtils;

import org.json.JSONException;
import org.json.JSONObject;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;

public class WSTaskOsUpdateService extends WakeLockService {
	private static final String TAG = "AppPlusOSUpdate";
	private Handler mHandler = new Handler(Looper.getMainLooper());
	private static String mTaskId = "";
	private NotificationManager manager;
	private BroadcastReceiver mBatInfoReveiver = null;
	private int pre_progress = 0;

	public WSTaskOsUpdateService() {
		super("WSTaskOsUpdateService");
	}

	@Override
	public void onDestroy() {
		if (mBatInfoReveiver != null) {
			unregisterReceiver(mBatInfoReveiver);
		}

		super.onDestroy();
		if (manager != null) {
			manager.cancel(3);
		}
		BBLog.w(BBLog.TAG, "WSTaskOsUpdateService 結束 發送 WSTASK_EXEC_BC");
		Helpers.sendBroad(WSTaskOsUpdateService.this, UsualData.WSTASK_EXEC_BC);
	}

	@Override
	public int onStartCommand(@Nullable Intent intent, int flags, int startId) {
		return super.onStartCommand(intent, flags, startId);

	}

	@Override
	protected void onHandleIntent(Intent intent) {
		try {
			BBLog.e(TAG, "WSTaskOsUpdateService started");
			final String taskId = intent.getExtras().getString(ParameterName.taskId);
			String fileMd5 = intent.getExtras().getString(ParameterName.fileMd5Ex);
			final long totalfileSize = Long.parseLong(intent.getExtras().getString(ParameterName.fileSizeEx));
			String fileUrl = intent.getExtras().getString(ParameterName.url);
			String displayVer = intent.getExtras().getString(ParameterName.displayVer);
			final String fileKey = intent.getExtras().getString(ParameterName.fileKey);
			if (!TextUtils.isEmpty(displayVer)) {
				SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_OSUPDATE_TASKID, taskId);
			}
			final String fileName = "update.zip";//intent.getExtras().getString(ParameterName.fileName);
			final String filePath = Constants.OTA_FILE_PATH + "update.zip";
			final String updatePath = "/data/ota_package/update.zip";
			Constants.OTA_UPDATE_FILE_NAME = fileName;
			//如果已经有文件则删除
			File tmpFile = new File(Constants.OTA_FILE_PATH);
			if (tmpFile.exists()) {
				File[] files = tmpFile.listFiles();// 声明目录下所有的文件 files[];
				for (int j = 0; j < files.length; j++) {// 遍历目录下所有的文件
					if (!files[j].getName().equals(fileName)) {
						BBLog.i(TAG, "WSTaskOsUpdateService file delete: " + files[j].getName());
						files[j].delete();
					}
				}
			}
			//记住文件的MD5
			final SharedPreferences preferences = PreferenceManager.getDefaultSharedPreferences(ContextUtil.getInstance());
			preferences.edit().putString(SPKeys.OTA_FILE_NAME, fileName).commit();
			preferences.edit().putString(SPKeys.OTA_FILE_MD5, fileMd5).commit();

			//上送下载中报文
			String channelID = "3";
			String channelName = "channel_3";
			manager = SystemApi.createCustomnNotificationManager(WSTaskOsUpdateService.this, channelID, channelName);
			final NotificationCompat.Builder builder = new NotificationCompat.Builder(WSTaskOsUpdateService.this, channelID);
			builder.setSmallIcon(android.R.drawable.stat_sys_download);
			builder.setContentTitle(getString(R.string.ota_download));
			builder.setContentText(getString(R.string.downloading));
			builder.setAutoCancel(true);
			builder.setDefaults(NotificationCompat.FLAG_ONLY_ALERT_ONCE);
			builder.setVibrate(new long[]{0});
			builder.setSound(null);
			//创建通知时指定channelID
			builder.setChannelId(channelID);

			//上送下载中报文
			if (!TextUtils.isEmpty(fileKey)) {
				String response = "";
				for (int i=0; i<3; i++) {
					response = HttpUtils.postGetFileUrl(Constants.S3_RESOURCE_URL, HttpUtils.getPostTokenRequestContent(fileKey));
					if (!TextUtils.isEmpty(response)) {
						break;
					}
				}
				if (TextUtils.isEmpty(response)) {
					Helpers.updateWSTaskStateAndUpload(WSTaskOsUpdateService.this, taskId, TaskState.DOWNLOAD_FAILED, null);
					return;
				} else {
					fileUrl = response;
				}
			}
			Helpers.updateWSTaskStateAndUpload(getApplicationContext(), taskId, TaskState.DOWNLOAD_ING, "");
			HttpUtils.fileDownloadByUrlWithRetry(fileUrl, filePath, totalfileSize, fileMd5, new HttpUtils.FileDownloadCallBack() {
				@Override
				public void requestSuccess(JSONObject responseJson) throws Exception {
					// TODO Auto-generated method stub
					builder.setProgress(0, 0,false).setContentText(getString(R.string.download_complete));
					manager.notify(3, builder.build());
					//上送安装中报文
					Helpers.updateWSTaskStateAndUpload(getApplicationContext(), taskId, TaskState.DOWNLOAD_SUCCESS, "");
					if (Constants.IS_BATTERY_LOW_FOR_OSUPDATE==true) {
						Helpers.updateWSTaskStateAndUpload(getApplicationContext(), taskId, TaskState.LOW_BAT, null);
						mHandler.post(new Runnable() {
							@Override
							public void run() {
								final Dialog dialog = new Dialog(WSTaskOsUpdateService.this, R.style.dialog_style_ex);
								dialog.setContentView(R.layout.dialog_confirm);
								ImageView imageView = (ImageView) dialog.findViewById(R.id.iv_image);
								imageView.setBackground(WSTaskOsUpdateService.this.getDrawable(R.drawable.low_bat));
								TextView tv_title = (TextView) dialog.findViewById(R.id.tv_title);
								tv_title.setText(WSTaskOsUpdateService.this.getString(R.string.low_battery));
								TextView tv_content = (TextView) dialog.findViewById(R.id.tv_content);
								tv_content.setText(WSTaskOsUpdateService.this.getString(R.string.install_tip_dialog_message_low_power));
								dialog.setCanceledOnTouchOutside(false);
								TextView tv_cancel = (TextView) dialog.findViewById(R.id.tv_cancel);
								tv_cancel.setVisibility(View.GONE);
								TextView tv_install = (TextView) dialog.findViewById(R.id.tv_install);
								tv_install.setText(R.string.update);
								tv_install.setOnClickListener(
										new View.OnClickListener() {
											@Override
											public void onClick(View v) {
												dialog.dismiss();
											}
										});
								dialog.getWindow().setType((WindowManager.LayoutParams.TYPE_SYSTEM_ALERT));
								dialog.show();
							}
						});
					} else {
						mHandler.post(new Runnable() {
							@Override
							public void run() {
								mTaskId = taskId;
								MOSUpgradeDialog.showOSUpgradeDialog(WSTaskOsUpdateService.this,
									getString(R.string.update_shade_update_tip), getString(R.string.install_tip_dialog_message)
									, new View.OnClickListener() {
										@Override
										public void onClick(View v) {
											mHandler.postDelayed(new Runnable() {
												@Override
												public void run() {
													gotoOSUpdate(taskId, filePath, updatePath);
												}
											}, 300000);
										}
									}, new View.OnClickListener() {
										@Override
										public void onClick(View v) {
											new Thread(new Runnable() {
												@Override
												public void run() {
													try {
														Thread.sleep(1000);
													} catch (InterruptedException e) {
														e.printStackTrace();
													}
													gotoOSUpdate(taskId, filePath, updatePath);
												}
											}).start();
										}
									}
								);
							}
						});
					}
				}

				@Override
				public void requestFail(int errorCode, String errorStr) {
					// TODO Auto-generated method stub
					builder.setProgress(0, 0,false).setContentText(getString(R.string.download_failed));
					manager.notify(3, builder.build());
					if (errorCode > 400) {
						Helpers.updateWSTaskStateAndUpload(getApplicationContext(), taskId, TaskState.SERVER_FAILED, null);
					} else {
						Helpers.updateWSTaskStateAndUpload(getApplicationContext(), taskId, TaskState.DOWNLOAD_FAILED, null);
					}
					BBLog.e(TAG,"error in download ota file: " + errorCode + " " + errorStr);
				}

				@Override
				public void onDownloading(long curFileSize, long fileSize) {
					//下载进度反馈 TODO
					int progress = (int)((curFileSize*100)/totalfileSize);
					if (progress > pre_progress) {
						builder.setProgress(100, progress, false);
						builder.setOngoing(true);
						builder.setWhen(System.currentTimeMillis());
						builder.setContentText(getString(R.string.downloading) + " " + progress + "%");
						//				BBLog.e(Constants.TAG,getString(R.string.downloading) + " " + progress + "%");
						manager.notify(3, builder.build());
					}
					pre_progress = progress;
				}
			});
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	private void gotoOSUpdate(String taskId, String filePath, String updatePath) {
		if (DeviceInfoApi.getIntance().isWisePosPro() || DeviceInfoApi.getIntance().isWisePos4G()) {
			osUpdate(taskId, filePath, updatePath);
		} else if (DeviceInfoApi.getIntance().isWisePos5Plus()|| DeviceInfoApi.getIntance().isWisePos5()){
			startOSUpdate(ContextUtil.getInstance(), taskId, filePath);
		} else if (DeviceInfoApi.getIntance().isWisePosTouch()|| DeviceInfoApi.getIntance().isWisePosTouchPlus()
				|| DeviceInfoApi.getIntance().isWisePosGo() || DeviceInfoApi.getIntance().isWisePosLE() || DeviceInfoApi.getIntance().isWisePosLP()){
			Helpers.updateWSTaskStateAndUpload(getApplicationContext(), taskId, TaskState.UPDATE_ING, null);
			CustomServiceManager.getInstance().startSystemUpdate(filePath);
		}
	}

	private void startOSUpdate(Context context, String taskId, String updateFile){
		Helpers.updateWSTaskStateAndUpload(getApplicationContext(), taskId, TaskState.UPDATE_ING, null);
		BBLog.e(TAG, "startOSUpdate updateFile = " + updateFile);

		if (CustomServiceManager.getInstance().hasInit()) {
			CustomServiceManager.getInstance().startOSUpdate(updateFile, false);
		} else {
			Intent it = new Intent("com.qualcomm.update.REBOOT");
//			String updateFile = "/mnt/sdcard/update.zip";
			it.setData(Uri.fromFile(new File(updateFile)));
			it.putExtra("update_confirm", false); //升级是否弹框确认升级
			it.putExtra("update_verify", true);//是否验证升级文件正确性
			it.putExtra("update_mode", 2);
			it.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
			context.startActivity(it);
		}
	}

	/**绑定service 进行os更新*/
	public void osUpdate(String taskId, String osFilePath, String osUpdatePath){
		FileInputStream input = null;
		FileOutputStream output = null;
		try {
			File file = new File(osUpdatePath);
			if (file.exists() && file.isFile())
				file.delete();

			input = new FileInputStream(osFilePath);
			output = new FileOutputStream(osUpdatePath);

			byte[] buffer = new byte[1024];
			int count = 0;
			while ((count = input.read(buffer)) > 0) {
				output.write(buffer, 0, count);
			}

			SecurityOperate.getInstance().changeFilemode(ContextUtil.getInstance(), osUpdatePath);
			File temp = new File(osFilePath);
			if (temp.exists() && temp.isFile()) {
				temp.delete();
			}
			Helpers.updateWSTaskStateAndUpload(getApplicationContext(), taskId, TaskState.UPDATE_ING, null);
			RecoverySystem.installPackage(getApplicationContext(), new File(osUpdatePath));
		} catch (Exception e) {
			e.printStackTrace();
			Helpers.updateWSTaskStateAndUpload(getApplicationContext(), taskId, TaskState.UPDATE_FAILED, null);
		} finally {
			IOUtils.closeInputStream(input);
			IOUtils.flushCloseOutputStream(output);
		}
	}

	public static final ISystemUpdateCallback mSystemUpdateCallback = new ISystemUpdateCallback.Stub() {
		@Override
		public void onStatusUpdate(int status, float percent) throws RemoteException {
			BBLog.e(TAG, String.format("onStatusUpdate %d, Percent %.3f", status, percent));
			if (status == SystemUpdateConstants.UpdateStatusConstants.DOWNLOADING) {
				ContextUtil.sendProgressDialog(status, percent);
			} else if (status == SystemUpdateConstants.UpdateStatusConstants.VERIFYING) {
				ContextUtil.sendProgressDialog(status, percent);
			} else if (status == SystemUpdateConstants.UpdateStatusConstants.FINALIZING) {
				ContextUtil.sendProgressDialog(status, percent);
			} else if (status == SystemUpdateConstants.UpdateStatusConstants.UPDATED_NEED_REBOOT) {
				new Handler(Looper.getMainLooper()).post(new Runnable() {
					@Override
					public void run() {
						MRebootEventDialog.showRebootEventDialog(ContextUtil.getInstance(), ContextUtil.getInstance().getString(R.string.prompt), ContextUtil.getInstance().getString(R.string.prompt_osupdate_reboot),
								new View.OnClickListener() {
									@Override
									public void onClick(View v) {
										int timeout = 300;
										MRebootEventDialog.mDialog.createWindowManager();
										MRebootEventDialog.mDialog.createDesktopLayout();
										MRebootEventDialog.mDialog.showDesk(timeout);
									}
								},
								new View.OnClickListener() {
									@Override
									public void onClick(View v) {
										SystemManagerAdapter.reboot(ContextUtil.getInstance());
									}
								});
					}
				});
			}
		}

		@Override
		public void onPayloadApplicationComplete(int errorCode) throws RemoteException {
			BBLog.e(BBLog.TAG, String.format("onPayloadApplicationComplete %d", errorCode));
			ContextUtil.closeProgressDialog();
			if (errorCode != 0) {
				Helpers.updateWSTaskStateAndUpload(ContextUtil.getInstance(), mTaskId, TaskState.FAILED, null);
			}
		}
	};
}
