package com.bbpos.wiseapp.settings.activity;

import android.app.Activity;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.res.Configuration;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import android.net.wifi.ScanResult;
import android.net.wifi.WifiConfiguration;
import android.net.wifi.WifiManager;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;
import android.view.View.OnClickListener;
import android.view.Window;
import android.widget.AdapterView;
import android.widget.LinearLayout;
import android.widget.ListView;
import android.widget.TextView;

import com.bbpos.wiseapp.logger.BBLog;
import com.bbpos.wiseapp.service.R;
import com.bbpos.wiseapp.service.common.RequestPermission;
import com.bbpos.wiseapp.settings.adapter.WiFiAdapter;
import com.bbpos.wiseapp.settings.dialog.WifiDialog;
import com.bbpos.wiseapp.settings.utils.HelperUtil;
import com.bbpos.wiseapp.settings.utils.WirelessUtil;
import com.bbpos.wiseapp.settings.wifi.WifiController;

import java.util.ArrayList;
import java.util.List;

//登录界面
public class WiFiDialogActivity extends Activity implements OnClickListener{
	private List<ScanResult> listItem;

	private LinearLayout ll_listwifi;
	private ListView listView;
	private TextView tv_prompt;
	private TextView tv_skip;
	private TextView tv_connect_detail;
	private WiFiAdapter listAdapter;
	private WifiManager mWifiManager;
	private WifiController mWifiController;
	private ConnectivityManager mConnectivityManager;
	private IntentFilter mIntentFilter;
	private WifiDialog dialog;
	private int wifistate = 1000;
	private boolean isWifConnecting = false;

	private final BroadcastReceiver mReceiver = new BroadcastReceiver() {
		@Override
		public void onReceive(Context context, Intent intent) {
			String action = intent.getAction();
			if (action.equals(WifiManager.WIFI_STATE_CHANGED_ACTION)) {
				if (wifistate != intent.getIntExtra(WifiManager.EXTRA_WIFI_STATE, WifiManager.WIFI_STATE_UNKNOWN)) {
					wifistate = intent.getIntExtra(WifiManager.EXTRA_WIFI_STATE, WifiManager.WIFI_STATE_UNKNOWN);
					onAccessPointsChanged(wifistate);
				}
			} else if (action.equals(WifiManager.RSSI_CHANGED_ACTION)) {
				updataWifiState();
			} else if (action.equals(WifiManager.SCAN_RESULTS_AVAILABLE_ACTION)) {
				updataWifiState();
			} else if (intent.getAction().equals(WifiManager.NETWORK_STATE_CHANGED_ACTION)) {//wifi连接网络状态变化
				updataWifiState();
				NetworkInfo.DetailedState state = ((NetworkInfo) intent.getParcelableExtra(WifiManager.EXTRA_NETWORK_INFO)).getDetailedState();
				onNetWorkStateChanged(state);
			} else if (intent.getAction().equals(ConnectivityManager.CONNECTIVITY_ACTION)) {
				if (context == null) {
					throw new UnsupportedOperationException("context is null, check your code first please!");
				}
				ConnectivityManager connectivityManager = (ConnectivityManager) context.getSystemService(Context.CONNECTIVITY_SERVICE);
				if (connectivityManager != null) {
					NetworkInfo activeNetworkInfo = connectivityManager.getActiveNetworkInfo();
					if (activeNetworkInfo != null && activeNetworkInfo.isConnected()) {
						finish();
					}
				}
			}
		}
	};

	public synchronized void updataWifiState() {
		if (wifistate==WifiManager.WIFI_STATE_DISABLED || isWifConnecting) {
			BBLog.i(BBLog.TAG, "updataWifiState return");
			return;
		}
		listItem = getScanResultNotNull();
//		listItem = mWifiManager.getScanResults();
//		String ssid = WirelessUtil.getCurConnectSSID(WiFiActivity.this);
//		for(int i = 0 ; i < listItem.size() ; i++) {
//			if (ssid.equals(listItem.get(i).SSID)) {
//				if (i == 0) {
//					break;
//				}
//				ScanResult scanResult = listItem.get(i);
//				listItem.remove(i);
//				listItem.add(0, scanResult);
//				break;
//			}
//		}
		if (View.INVISIBLE == ll_listwifi.getVisibility()) {
			ll_listwifi.setVisibility(View.VISIBLE);
		}
		listAdapter.setData(listItem);
		listAdapter.notifyDataSetChanged();
	}

	@Override
	public void onCreate(Bundle savedInstanseBundle){
		super.onCreate(savedInstanseBundle);

		requestWindowFeature(Window.FEATURE_NO_TITLE);

		setContentView(R.layout.activity_dialog_wifi);

		mConnectivityManager = (ConnectivityManager) getSystemService(CONNECTIVITY_SERVICE);
		mWifiManager = (WifiManager) getApplicationContext().getSystemService(WIFI_SERVICE);
//		if (!mWifiManager.isWifiEnabled()) {
//			mWifiManager.setWifiEnabled(true);
//		}
//		mWifiManager.startScan();

		initData();
		initView();

		mIntentFilter = new IntentFilter(WifiManager.WIFI_STATE_CHANGED_ACTION);
		// The order matters! We really should not depend on this. :(
		mIntentFilter.addAction(WifiManager.SUPPLICANT_STATE_CHANGED_ACTION);
		mIntentFilter.addAction(WifiManager.NETWORK_STATE_CHANGED_ACTION);
		mIntentFilter.addAction(WifiManager.RSSI_CHANGED_ACTION);
		mIntentFilter.addAction(WifiManager.SCAN_RESULTS_AVAILABLE_ACTION);
		mIntentFilter.addAction(ConnectivityManager.CONNECTIVITY_ACTION);

		mWifiController = new WifiController(mConnectivityManager, mWifiManager);
	}

	private void initData() {
//		listItem = mWifiManager.getScanResults();
		listItem = getScanResultNotNull();
		String ssid = WirelessUtil.getCurConnectSSID(WiFiDialogActivity.this);
		for(int i = 0 ; i < listItem.size() ; i++) {
			if (ssid.equals(listItem.get(i).SSID)) {
				ScanResult scanResult = listItem.get(i);
				listItem.remove(i);
				listItem.add(0, scanResult);
				break;
			}
		}
	}

	/**
	 * 过滤ssid为空的wifi
	 * @return
	 */
	private List<ScanResult> getScanResultNotNull(){
		if (listItem !=null)
			listItem.clear();
		else
			listItem = new ArrayList<>();
		List<ScanResult> results = mWifiManager.getScanResults();
		for (ScanResult  result: results){
			if (!TextUtils.isEmpty(result.SSID)){
				listItem.add(result);
			}
		}
		return listItem;
	}

	private void showWifiStateDesc(String desc) {
		if (View.VISIBLE == ll_listwifi.getVisibility()) {
			ll_listwifi.setVisibility(View.INVISIBLE);
		}
		tv_prompt.setVisibility(View.VISIBLE);
		tv_prompt.setText(desc);
	}

	private void onAccessPointsChanged(int wifiState) {
		switch (wifiState) {
			case WifiManager.WIFI_STATE_ENABLED:
				showWifiStateDesc(getString(R.string.wifi_empty_list_wifi_on));
				updataWifiState();
				break;
			case WifiManager.WIFI_STATE_ENABLING:
				showWifiStateDesc(getString(R.string.wifi_starting));
				break;
			case WifiManager.WIFI_STATE_DISABLING:
				showWifiStateDesc(getString(R.string.wifi_stopping));
				break;
			case WifiManager.WIFI_STATE_DISABLED:
				showWifiStateDesc(getString(R.string.wifi_empty_list_wifi_off));
				break;
		}
	}

	private void setConnectDetail(String desc) {
		if (tv_connect_detail != null) {
			tv_connect_detail.setVisibility(View.VISIBLE);
			tv_connect_detail.setText(desc);
		}
	}

	private void onNetWorkStateChanged(NetworkInfo.DetailedState state) {
		if (state == NetworkInfo.DetailedState.SCANNING) {
			BBLog.i(BBLog.TAG, "onReceive onNetWorkStateChanged: +正在扫描");
			setConnectDetail(getString(R.string.scanning));
		} else if (state == NetworkInfo.DetailedState.CONNECTING) {
			isWifConnecting = true;
			BBLog.i(BBLog.TAG, "onReceive onNetWorkStateChanged: +正在连接");
			setConnectDetail(getString(R.string.connecting));
		} else if (state == NetworkInfo.DetailedState.OBTAINING_IPADDR) {
			BBLog.i(BBLog.TAG, "onReceive onNetWorkStateChanged: +获取IP地址");
			setConnectDetail(getString(R.string.obtaining));
		} else if (state == NetworkInfo.DetailedState.CONNECTED) {
			if (isWifConnecting == true){
				isWifConnecting = false;
				BBLog.i(BBLog.TAG, "onReceive onNetWorkStateChanged: +建立连接");
				setConnectDetail(getString(R.string.connected));
				updataWifiState();
				finish();
			}
		} else if (state == NetworkInfo.DetailedState.DISCONNECTING) {
			BBLog.i(BBLog.TAG, "onReceive onNetWorkStateChanged: +正在断开连接");
			setConnectDetail(getString(R.string.disconnecting));
		} else if (state == NetworkInfo.DetailedState.DISCONNECTED) {
			BBLog.i(BBLog.TAG, "onReceive onNetWorkStateChanged: +已断开连接");
			setConnectDetail(getString(R.string.disconnected));
		} else if (state == NetworkInfo.DetailedState.FAILED) {
			isWifConnecting = false;
			BBLog.i(BBLog.TAG, "onReceive onNetWorkStateChanged: +连接失败");
			setConnectDetail(getString(R.string.connect_failed));
		}
	}

	@Override
	protected void onResume() {
		super.onResume();
		BBLog.i(BBLog.TAG, "onResume");
		registerReceiver(mReceiver, mIntentFilter,  RequestPermission.REQUEST_PERMISSION_MY_BROADCAST, null);
//		mWifiManager = (WifiManager) getApplicationContext().getSystemService(WIFI_SERVICE);
		if (!mWifiManager.isWifiEnabled()) {
			mWifiManager.setWifiEnabled(true);
		}
		mWifiManager.startScan();
		if (mWifiController.isWifiConnected()) {
			BBLog.i(BBLog.TAG, "isWifiConnected");
			finish();
		}
	}

	@Override
	protected void onPause() {
		super.onPause();
		unregisterReceiver(mReceiver);
	}

	@Override
	public void onConfigurationChanged(Configuration newConfig) {
		super.onConfigurationChanged(newConfig);
		finish();
	}

	private void initView() {
		tv_skip = findViewById(R.id.tv_skip);
		tv_skip.setOnClickListener(this);

		ll_listwifi = findViewById(R.id.ll_listwifi);
		listView = findViewById(R.id.list_wifi);

		tv_prompt = findViewById(R.id.tv_prompt);
		if (HelperUtil.isWifiEnable(WiFiDialogActivity.this)) {
			ll_listwifi.setVisibility(View.VISIBLE);
			tv_prompt.setVisibility(View.INVISIBLE);
		} else {
			ll_listwifi.setVisibility(View.INVISIBLE);
			tv_prompt.setVisibility(View.VISIBLE);
			tv_prompt.setText(getString(R.string.wifi_empty_list_wifi_off));
		}

		//初始化列表
		listView = (ListView) findViewById(R.id.list_wifi);

		listAdapter = new WiFiAdapter(WiFiDialogActivity.this, listItem);
		listView.setAdapter(listAdapter);
		listView.setOnItemClickListener(new AdapterView.OnItemClickListener() {
			@Override
			public void onItemClick(AdapterView<?> parent, View view, final int position, long id) {
				if (HelperUtil.isFastDoubleClick()) { //判断是否是快速点击
					return;
				}
				if (position == listItem.size() - 1) {
					//添加网络
					promptForWifiConnect(null);
				} else {
					if (view != null) {
						tv_connect_detail = view.findViewById(R.id.tv_status);
					}
					promptForWifiConnect(listItem.get(position));
				}
			}
		});
	}

	@Override
	public void onClick(View v) {
		switch (v.getId()) {
			case R.id.tv_skip:
				setResult(99);
				finish();
				break;
		}
	}

	private void promptForWifiConnect(ScanResult scanResult){
		if (scanResult == null) {
			if (dialog != null) {
				dialog.dismiss();
				dialog = null;
			}
			dialog = new WifiDialog(WiFiDialogActivity.this, wifiDialogListener, scanResult, 0);
			dialog.show();
		} else {
			if (scanResult.SSID.equals(WirelessUtil.getCurConnectSSID(WiFiDialogActivity.this))) {

			} else {
				if (scanResult.capabilities.contains("WEP")
						|| scanResult.capabilities.contains("PSK")
						|| scanResult.capabilities.contains("EAP")) {
					if (dialog != null) {
						dialog.dismiss();
						dialog = null;
					}
					dialog = new WifiDialog(WiFiDialogActivity.this, wifiDialogListener, scanResult, 1);
					dialog.show();
				} else {
					if (!isWifConnecting) {
						WirelessUtil.connectWifi(WiFiDialogActivity.this, scanResult.SSID, "", WirelessUtil.SECURITY_NONE);
					}
				}
			}
		}
	}

	private WifiDialog.WifiDialogListener wifiDialogListener = new WifiDialog.WifiDialogListener() {
		@Override
		public void onForget() {
			List<WifiConfiguration> list = mWifiManager.getConfiguredNetworks();
			for( WifiConfiguration i : list ) {
				mWifiManager.removeNetwork(i.networkId);
				mWifiManager.saveConfiguration();
			}
		}

		@Override
		public void onSubmit(ScanResult scanResult, String password) {
			WifiConfiguration config = mWifiController.creatWifiConfiguration(scanResult, password);
			mWifiController.connect(config);
		}
	};
}
