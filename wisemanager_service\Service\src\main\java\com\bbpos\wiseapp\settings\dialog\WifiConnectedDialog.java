/*
 * Copyright (C) 2010 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.bbpos.wiseapp.settings.dialog;

import android.app.AlertDialog;
import android.content.Context;
import android.net.wifi.ScanResult;
import android.net.wifi.WifiConfiguration;
import android.net.wifi.WifiInfo;
import android.net.wifi.WifiManager;
import android.os.Bundle;
import android.util.DisplayMetrics;
import android.util.Log;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.view.WindowManager;
import android.widget.TextView;

import com.bbpos.wiseapp.logger.BBLog;
import com.bbpos.wiseapp.service.R;
import com.bbpos.wiseapp.settings.utils.WirelessUtil;
import com.bbpos.wiseapp.settings.wifi.WifiController;

public class WifiConnectedDialog extends AlertDialog implements View.OnClickListener {
    /**
     * Lower bound on the 2.4 GHz (802.11b/g/n) WLAN channels
     */
    public static final int LOWER_FREQ_24GHZ = 2400;
    /**
     * Upper bound on the 2.4 GHz (802.11b/g/n) WLAN channels
     */
    public static final int HIGHER_FREQ_24GHZ = 2500;
    /**
     * Lower bound on the 5.0 GHz (802.11a/h/j/n/ac) WLAN channels
     */
    public static final int LOWER_FREQ_5GHZ = 4900;
    /**
     * Upper bound on the 5.0 GHz (802.11a/h/j/n/ac) WLAN channels
     */
    public static final int HIGHER_FREQ_5GHZ = 5900;
    private String[] mLevels;

    private Context mContext;
    private final WifiDialog.WifiDialogListener mListener;
    private WifiManager mWifiManager;
    private WifiController mController;

    private View mView;

    private final ScanResult mScanResult;
    private TextView tv_cancel;
    private TextView tv_forgot;


    public WifiConnectedDialog(Context context, WifiManager manager, WifiController controller, WifiDialog.WifiDialogListener listener, ScanResult scanResult) {
        super(context);
        mListener = listener;
        mScanResult = scanResult;
        mController = controller;
        mContext = context;
        mWifiManager = manager;
    }

    private void addRow(ViewGroup group, int name, String value) {
        View row = getLayoutInflater().inflate(R.layout.wifi_dialog_row, group, false);
        ((TextView) row.findViewById(R.id.name)).setText(name);
        ((TextView) row.findViewById(R.id.value)).setText(value);
        group.addView(row);
    }

    private String getSignalString(int level){
        return (level > -1 && level < mLevels.length) ? mLevels[level] : null;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        mView = getLayoutInflater().inflate(R.layout.dialog_wifi_connected, null);
        setView(mView);
        setInverseBackgroundForced(true);
        super.onCreate(savedInstanceState);

        TextView tv_header = findViewById(R.id.tv_header);
        tv_header.setText(mScanResult.SSID);

        ViewGroup group = (ViewGroup) mView.findViewById(R.id.info);

        mLevels = mContext.getResources().getStringArray(R.array.wifi_signal);

        WifiConfiguration config = mController.isWifiSave(mScanResult.SSID);
        WifiInfo wifiInfo = mWifiManager.getConnectionInfo();
        int i = wifiInfo.getIpAddress();

        addRow(group, R.string.wifi_ip_address, WirelessUtil.int2ip(i));
        addRow(group, R.string.wifi_status, mContext.getString(R.string.wifi_display_status_connected));
        addRow(group, R.string.wifi_signal, getSignalString(WirelessUtil.getWifiLevel(mScanResult.level, 4)));
        addRow(group, R.string.wifi_speed, wifiInfo.getLinkSpeed() + WifiInfo.LINK_SPEED_UNITS);
        if (wifiInfo != null && wifiInfo.getFrequency() != -1) {
            final int frequency = wifiInfo.getFrequency();
            String band = null;

            if (frequency >= LOWER_FREQ_24GHZ
                    && frequency < HIGHER_FREQ_24GHZ) {
                band = mContext.getString(R.string.wifi_band_24ghz);
            } else if (frequency >= LOWER_FREQ_5GHZ
                    && frequency < HIGHER_FREQ_5GHZ) {
                band = mContext.getString(R.string.wifi_band_5ghz);
            } else {
                BBLog.e(BBLog.TAG, "Unexpected frequency " + frequency);
            }
            if (band != null) {
                addRow(group, R.string.wifi_frequency, band);
            }
        }

        tv_cancel = findViewById(R.id.tv_cancel);
        tv_forgot = findViewById(R.id.tv_forgot);
        tv_cancel.setOnClickListener(this);
        tv_forgot.setOnClickListener(this);

        /*
         * 将对话框的大小按屏幕大小的百分比设置
         */
        Window window = getWindow() ;
        WindowManager m =(WindowManager) mContext.getSystemService(Context.WINDOW_SERVICE);
        DisplayMetrics dm = new DisplayMetrics();; // 获取屏幕宽、高用
        m.getDefaultDisplay().getMetrics(dm);
        WindowManager.LayoutParams p = window.getAttributes(); // 获取对话框当前的参数值
        p.width = (int) (dm.widthPixels * 0.9);
        window.setAttributes(p);
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.tv_cancel:
                dismiss();
                break;
            case R.id.tv_forgot:
                //斷開WIFI
                mListener.onForget();
                dismiss();
                break;
        }
    }
}
