package com.bbpos.wiseapp.service.bbdevice;

import android.bbpos.CustServiceManager;
import android.text.TextUtils;

import com.bbpos.bbdevice.BBDeviceController;
import com.bbpos.wiseapp.logger.BBLog;
import com.bbpos.wiseapp.system.api.CustomServiceManager;
import com.bbpos.wiseapp.tms.utils.ContextUtil;
import com.bbpos.wiseapp.utils.ByteUtils;

import java.util.Hashtable;

public class BBDeviceManager {
    private static BBDeviceManager instance;
    public static final String TAG = "bbdevice";
    private boolean serialConnected;
    private BBDeviceController bbDeviceController = BBDeviceController.getInstance(ContextUtil.getInstance(), new MBBDeviceControllerListener());
    private SessionData sessionData = new SessionData();

    public static BBDeviceManager getInstance() {
        if (instance == null) {
            instance = new BBDeviceManager();
        }

        return instance;
    }
    public void startSetDeviceName() {
        bbDeviceController.startSerial();
    }

    class MBBDeviceControllerListener extends DefaultBBDeviceControllerListener {
        @Override
        public void onReturnDeviceInfo(Hashtable<String, String> deviceInfoData) {
            sessionData.setBid(deviceInfoData.get("bID"));
            sessionData.setSerialNumber(deviceInfoData.get("serialNumber"));
            bbDeviceController.readTerminalSetting("DF8408DF8409");
        }

        @Override
        public void onError(BBDeviceController.Error errorState, String errorMessage) {
            ContextUtil.getInstance().makeToast("Error : " + errorState + ", errorMessage : " + errorMessage);
            if ("Cannot start serial again while serial is connected".equals(errorMessage)) {
                serialConnected = true;
                if (bbDeviceController!=null)
                    bbDeviceController.getDeviceInfo();
            } else {
                if (serialConnected && bbDeviceController!=null) {
                    serialConnected = false;
                    bbDeviceController.stopSerial();
                }
            }
        }

        @Override
        public void onSerialConnected() {
            BBLog.e(TAG, "onSerialConnected");
            serialConnected = true;
            if (bbDeviceController!=null)
                bbDeviceController.getDeviceInfo();
        }

        @Override
        public void onSerialDisconnected() {
            BBLog.e(TAG, "onSerialDisconnected");
            serialConnected = false;
        }

        @Override
        public void onReturnReadTerminalSettingResult(Hashtable<String, Object> data) {
            boolean flagUpdatedDeviceName = false;
            try {
                Object devicePrefix = data.get("DF8408");
                Object deviceNumOfLastSerial = data.get("DF8409");
                if ((devicePrefix != null) && (deviceNumOfLastSerial != null)) {
                    if ((devicePrefix instanceof String) && (deviceNumOfLastSerial instanceof String)) {
                        String deviceName = ByteUtils.hexString2AsciiString((String)devicePrefix);
                        int numOfLastDigit = ByteUtils.hexStringToInt((String)deviceNumOfLastSerial);
                        deviceName = deviceName + sessionData.getSerialNumber().substring(sessionData.getSerialNumber().length() - numOfLastDigit);

                        if (!(deviceName == null) && !TextUtils.isEmpty(deviceName)) {
                            CustomServiceManager.getInstance().setDeviceName(deviceName);
                            flagUpdatedDeviceName = true;
                        }
                    }
                }
            } catch (Exception e) {
                ContextUtil.getInstance().makeToast("e : " + e.toString());
            }

            if (flagUpdatedDeviceName) {
                ContextUtil.getInstance().makeToast("Update device name success.");
            } else {
                ContextUtil.getInstance().makeToast("Update device name failed!!");
            }
            bbDeviceController.stopSerial();
        }
    }
}
