2025-08-13 13:07:45.595   644-2046  BufferQueueDebug        surfaceflinger                       E  [ActivityRecord{287768c u0 com.dspread.mdm.service/.ui.activity.TestActivity#3299](this:0xaef09c40,id:-1,api:0,p:-1,c:-1) id info cannot be read from 'ActivityRecord{287768c u0 com.dspread.mdm.service/.ui.activity.TestActivity#3299'
2025-08-13 13:07:45.622   644-1293  BufferQueueDebug        surfaceflinger                       E  [57afeb6 Splash Screen com.dspread.mdm.service#3300](this:0xaee24c40,id:-1,api:0,p:-1,c:-1) id info cannot be read from '57afeb6 Splash Screen com.dspread.mdm.service#3300'
2025-08-13 13:07:45.637   644-916   BufferQueueDebug        surfaceflinger                       E  [Splash Screen com.dspread.mdm.service#3301](this:0xaee0ec40,id:-1,api:0,p:-1,c:-1) id info cannot be read from 'Splash Screen com.dspread.mdm.service#3301'
2025-08-13 13:07:45.691   644-1293  BufferQueueDebug        surfaceflinger                       E  [8e7efbf ActivityRecordInputSink com.dspread.mdm.service/.ui.activity.TestActivity#3304](this:0xaed3bc40,id:-1,api:0,p:-1,c:-1) id info cannot be read from '8e7efbf ActivityRecordInputSink com.dspread.mdm.service/.ui.activity.TestActivity#3304'
2025-08-13 13:07:45.835  6603-6603  ziparchive              com.dspread.mdm.service              W  Unable to open '/data/app/~~o6_VcjpmToCIUr1Jv34QRQ==/com.dspread.mdm.service-CLGLCCFvureHWBzVJcO7GA==/base.dm': No such file or directory
2025-08-13 13:07:46.259  6603-6603  nativeloader            com.dspread.mdm.service              D  Configuring clns-4 for other apk /data/app/~~o6_VcjpmToCIUr1Jv34QRQ==/com.dspread.mdm.service-CLGLCCFvureHWBzVJcO7GA==/base.apk. target_sdk_version=34, uses_libraries=, library_path=/data/app/~~o6_VcjpmToCIUr1Jv34QRQ==/com.dspread.mdm.service-CLGLCCFvureHWBzVJcO7GA==/lib/arm, permitted_path=/data:/mnt/expand:/data/user/0/com.dspread.mdm.service
2025-08-13 13:07:46.286  6603-6603  GraphicsEnvironment     com.dspread.mdm.service              V  Currently set values for:
2025-08-13 13:07:46.286  6603-6603  GraphicsEnvironment     com.dspread.mdm.service              V    angle_gl_driver_selection_pkgs=[]
2025-08-13 13:07:46.286  6603-6603  GraphicsEnvironment     com.dspread.mdm.service              V    angle_gl_driver_selection_values=[]
2025-08-13 13:07:46.287  6603-6603  GraphicsEnvironment     com.dspread.mdm.service              V  ANGLE GameManagerService for com.dspread.mdm.service: false
2025-08-13 13:07:46.288  6603-6603  GraphicsEnvironment     com.dspread.mdm.service              V  com.dspread.mdm.service is not listed in per-application setting
2025-08-13 13:07:46.288  6603-6603  GraphicsEnvironment     com.dspread.mdm.service              V  Neither updatable production driver nor prerelease driver is supported.
2025-08-13 13:07:46.314  6603-6603  Compatibil...geReporter com.dspread.mdm.service              D  Compat change id reported: 183155436; UID 1000; state: ENABLED
2025-08-13 13:07:46.333  6603-6603  MbrainDebugManagerImpl  com.dspread.mdm.service              D  getService failed
2025-08-13 13:07:46.618  6603-6603  Common                  com.dspread.mdm.service              D  🔧 日志配置：测试环境模式
2025-08-13 13:07:46.627  6603-6603  Common                  com.dspread.mdm.service              D  🔧 [DebugConfig] 调试模式已启用
2025-08-13 13:07:46.633  6603-6603  Common                  com.dspread.mdm.service              D  🔧 [DebugConfig] ========== 当前日志配置 ==========
2025-08-13 13:07:46.751  6603-6603  Common                  com.dspread.mdm.service              D  🔧 [DebugConfig]             ========== 统一配置状态 (调试模式) ==========
                                                                                                    
                                                                                                                LogStream配置 (调试模式):
                                                                                                    启用状态: true
                                                                                                    单个日志文件: 100KB
                                                                                                    压缩文件总限制: 10MB
                                                                                                    原始日志总限制: 10MB
                                                                                                    Recent日志大小: 1KB
                                                                                                    上传URL: https://siot-log01.s3.ap-northeast-1.amazonaws.com/log/
                                                                                                    
                                                                                                                Provisioning配置 (调试模式):
                                                                                                    配置目录: /sdcard/Android/data/com.dspread.mdm.service/files/config/
                                                                                                    媒体目录: /sdcard/Android/data/com.dspread.mdm.service/files/media/
                                                                                                    Logo目录: /sdcard/Android/data/com.dspread.mdm.service/files/media/logo/
                                                                                                    开机动画目录: /sdcard/Android/data/com.dspread.mdm.service/files/media/bootanimation/
                                                                                                    
                                                                                                                ========== 日志配置状态 ==========
                                                                                                    全局开关: true
                                                                                                    
                                                                                                    模块开关:
                                                                                                      Common: true
                                                                                                      HTTPS: true
                                                                                                      WebSocket: true
                                                                                                      API: true
                                                                                                      Service: true
                                                                                                      Provisioning: true
                                                                                                      Task: true
                                                                                                      RuleBase: true
                                                                                                      Application: true
                                                                                                      LogStream: false
                                                                                                      RemoteView: false
                                                                                                      Wi-Fi: false
                                                                                                      APN: false
                                                                                                      GeoFence: false
                                                                                                      Receiver: true
                                                                                                    ================================
                                                                                                                =========================================
2025-08-13 13:07:46.757  6603-6603  Common                  com.dspread.mdm.service              D  🔧 [DebugConfig] =====================================
2025-08-13 13:07:46.763  6603-6603  Common                  com.dspread.mdm.service              D  🔧 SmartMdmServiceApp: 统一配置管理器初始化完成
2025-08-13 13:07:46.769  6603-6603  Common                  com.dspread.mdm.service              D  🔧 SmartMdmServiceApp: 开始初始化系统目录...
2025-08-13 13:07:46.775  6603-6603  Common                  com.dspread.mdm.service              D  🔧 SmartMdmServiceApp: 配置目录已存在 - /data/pos/config
2025-08-13 13:07:46.783  6603-6603  Common                  com.dspread.mdm.service              D  🔧 SmartMdmServiceApp: Logo目录创建 - 路径: /data/data/com.dspread.mdm.service/files/media/logo, 结果: true
2025-08-13 13:07:46.790  6603-6603  Common                  com.dspread.mdm.service              D  🔧 SmartMdmServiceApp: 开机动画目录创建 - 路径: /data/data/com.dspread.mdm.service/files/media/anim, 结果: true
2025-08-13 13:07:46.796  6603-6603  Common                  com.dspread.mdm.service              D  🔧 SmartMdmServiceApp: 系统目录初始化完成
2025-08-13 13:07:46.805  6603-6603  Platform                com.dspread.mdm.service              D  🔧 NetworkTrafficMonitor 初始化网络流量监控器
2025-08-13 13:07:46.816  6603-6603  Platform                com.dspread.mdm.service              D  🔧 NetworkTrafficMonitor 新的一天，重置流量统计
2025-08-13 13:07:46.822  6603-6603  Platform                com.dspread.mdm.service              D  🔧 NetworkTrafficMonitor 流量统计已重置
2025-08-13 13:07:46.829  6603-6603  Platform                com.dspread.mdm.service              D  🔧 NetworkTrafficMonitor 检测到日期变化:  -> 2025-08-13
2025-08-13 13:07:46.835  6603-6603  Platform                com.dspread.mdm.service              D  🔧 NetworkTrafficMonitor 流量统计已重置
2025-08-13 13:07:46.896  6603-6603  Platform                com.dspread.mdm.service              D  🔧 NetworkTrafficMonitor 每日上送任务已启动
2025-08-13 13:07:46.902  6603-6603  Platform                com.dspread.mdm.service              D  🔧 NetworkTrafficMonitor 网络流量监控器初始化完成
2025-08-13 13:07:46.911  6603-6603  Common                  com.dspread.mdm.service              D  🔧 TimerConfig 使用调试模式服务守护间隔: 120秒
2025-08-13 13:07:46.917  6603-6603  Receiver                com.dspread.mdm.service              D  🔧 ServiceGuardEventHandler 启动服务守护定时器: 120s
2025-08-13 13:07:46.924  6603-6603  ContextImpl             com.dspread.mdm.service              W  Calling a method in the system process without a qualified user: android.app.ContextImpl.sendBroadcast:1220 android.content.ContextWrapper.sendBroadcast:504 com.dspread.mdm.service.broadcast.handlers.service.ServiceGuardEventHandler.startServiceGuardTimer:120 com.dspread.mdm.service.SmartMdmServiceApp.startServiceGuardTimer:98 com.dspread.mdm.service.SmartMdmServiceApp.onCreate:33 
2025-08-13 13:07:46.926  6603-6603  Common                  com.dspread.mdm.service              D  🔧 SmartMdmServiceApp: 服务守护定时器启动成功
2025-08-13 13:07:46.933  6603-6603  Common                  com.dspread.mdm.service              D  🔧 ServiceStartupManager 收到服务启动请求: APPLICATION_STARTUP, 强制启动: false
2025-08-13 13:07:46.944  6603-6603  Common                  com.dspread.mdm.service              D  🔧 ServiceStartupManager 开始执行服务启动: APPLICATION_STARTUP
2025-08-13 13:07:46.951  6603-6603  Common                  com.dspread.mdm.service              D  🔧 ServiceManager: 准备启动后台服务
2025-08-13 13:07:46.957  6603-6603  ContextImpl             com.dspread.mdm.service              W  Calling a method in the system process without a qualified user: android.app.ContextImpl.startService:1899 android.content.ContextWrapper.startService:825 com.dspread.mdm.service.platform.manager.ServiceManager.startBackgroundService:20 com.dspread.mdm.service.platform.manager.ServiceStartupManager.performServiceStart:121 com.dspread.mdm.service.platform.manager.ServiceStartupManager.startService:66 
2025-08-13 13:07:46.961  6603-6603  Common                  com.dspread.mdm.service              D  🔧 ServiceManager: 后台服务启动成功，ComponentName: ComponentInfo{com.dspread.mdm.service/com.dspread.mdm.service.services.SmartMdmBackgroundService}
2025-08-13 13:07:47.971  6603-6603  Common                  com.dspread.mdm.service              D  🔧 ServiceStartupManager 服务启动验证成功: APPLICATION_STARTUP
2025-08-13 13:07:47.977  6603-6603  Common                  com.dspread.mdm.service              D  🔧 ServiceStartupManager 服务启动成功: APPLICATION_STARTUP
2025-08-13 13:07:47.983  6603-6603  Common                  com.dspread.mdm.service              D  🔧 SmartMdmServiceApp: 主服务启动请求已提交
2025-08-13 13:07:47.989  6603-6603  Common                  com.dspread.mdm.service              D  🔧 SmartMdmServiceApp: Application启动完成，包名: com.dspread.mdm.service
2025-08-13 13:07:48.003  6603-6603  M-ProMotion             com.dspread.mdm.service              I  M-ProMotion is disabled
2025-08-13 13:07:48.066  6603-6603  Compatibil...geReporter com.dspread.mdm.service              D  Compat change id reported: 210923482; UID 1000; state: ENABLED
2025-08-13 13:07:48.093  6603-6603  Compatibil...geReporter com.dspread.mdm.service              D  Compat change id reported: 171228096; UID 1000; state: ENABLED
2025-08-13 13:07:48.137  6603-6603  getAnimDeal             com.dspread.mdm.service              I  mode:3 packageName:com.dspread.mdm.service result: false
2025-08-13 13:07:48.145  6603-6637  PowerHalWrapper         com.dspread.mdm.service              I  PowerHalWrapper.getInstance 
2025-08-13 13:07:48.145  6603-6603  SurfaceFactory          com.dspread.mdm.service              I  [static] sSurfaceFactory = com.mediatek.view.impl.SurfaceFactoryImpl@723b5c1
2025-08-13 13:07:48.151  6603-6603  Compatibil...geReporter com.dspread.mdm.service              D  Compat change id reported: 237531167; UID 1000; state: DISABLED
2025-08-13 13:07:48.156  6603-6603  OpenGLRenderer          com.dspread.mdm.service              W  Unknown dataspace 0
2025-08-13 13:07:48.161  6603-6603  VRI[TestActivity]       com.dspread.mdm.service              D  hardware acceleration = true, forceHwAccelerated = false
2025-08-13 13:07:48.166   644-916   BufferQueueDebug        surfaceflinger                       E  [e535eab com.dspread.mdm.service/com.dspread.mdm.service.ui.activity.TestActivity#3308](this:0xaf077c40,id:-1,api:0,p:-1,c:-1) id info cannot be read from 'e535eab com.dspread.mdm.service/com.dspread.mdm.service.ui.activity.TestActivity#3308'
2025-08-13 13:07:48.173  6603-6603  InputTransport          com.dspread.mdm.service              D  Create ARC handle: 0xb1a7c700
2025-08-13 13:07:48.174  6603-6603  InputEventReceiver      com.dspread.mdm.service              D  Input log is disabled in InputEventReceiver.
2025-08-13 13:07:48.174  6603-6603  InputTransport          com.dspread.mdm.service              D  Input log is disabled in InputChannel.
2025-08-13 13:07:48.178  6603-6603  Choreographer           com.dspread.mdm.service              I  Skipped 109 frames!  The application may be doing too much work on its main thread.
2025-08-13 13:07:48.216   644-916   BufferQueueDebug        surfaceflinger                       E  [com.dspread.mdm.service/com.dspread.mdm.service.ui.activity.TestActivity#3309](this:0xaef84c40,id:-1,api:0,p:-1,c:-1) id info cannot be read from 'com.dspread.mdm.service/com.dspread.mdm.service.ui.activity.TestActivity#3309'
2025-08-13 13:07:48.232  6603-6603  BufferQueueConsumer     com.dspread.mdm.service              D  [](id:19cb00000000,api:0,p:-1,c:6603) connect: controlledByApp=false
2025-08-13 13:07:48.268  6603-6638  OpenGLRenderer          com.dspread.mdm.service              E  Unable to match the desired swap behavior.
2025-08-13 13:07:48.275  6603-6642  ion                     com.dspread.mdm.service              E  ioctl c0044901 failed with code -1: Invalid argument
2025-08-13 13:07:48.382  6603-6638  BLASTBufferQueue        com.dspread.mdm.service              D  [VRI[TestActivity]#0](f:0,a:1) acquireNextBufferLocked size=480x854 mFrameNumber=1 applyTransaction=true mTimestamp=182973808392596(auto) mPendingTransactions.size=0 graphicBufferId=28359669055493 transform=3
2025-08-13 13:07:48.389   644-2046  BufferQueueDebug        surfaceflinger                       E  [Surface(name=e535eab com.dspread.mdm.service/com.dspread.mdm.service.ui.activity.TestActivity)/@0x7eeead9 - animation-leash of starting_reveal#3312](this:0xaed15c40,id:-1,api:0,p:-1,c:-1) id info cannot be read from 'Surface(name=e535eab com.dspread.mdm.service/com.dspread.mdm.service.ui.activity.TestActivity)/@0x7eeead9 - animation-leash of starting_reveal#3312'
2025-08-13 13:07:48.398  6603-6603  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 服务启动
2025-08-13 13:07:48.406  6603-6648  OpenGLRenderer          com.dspread.mdm.service              I  Davey! duration=2030ms; Flags=1, FrameTimelineVsyncId=508640, IntendedVsync=182971777306240, Vsync=182973593972943, InputEventId=0, HandleInputStart=182973606157442, AnimationStart=182973606184289, PerformTraversalsStart=182973607039596, DrawStart=182973695504212, FrameDeadline=182971802806240, FrameInterval=182973604551058, FrameStartTime=16666667, SyncQueued=182973708266673, SyncStart=182973710928904, IssueDrawCommandsStart=182973711445135, SwapBuffers=182973807155596, FrameCompleted=182973810139596, DequeueBufferDuration=0, QueueBufferDuration=996000, GpuCompleted=182973810139596, SwapBuffersCompleted=182973809802366, DisplayPresentTime=848891696645888, CommandSubmissionCompleted=182973807155596, 
2025-08-13 13:07:48.435  6603-6603  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 开始服务初始化
2025-08-13 13:07:48.442  6603-6603  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] Android 14+: 初始化DSPREAD服务
2025-08-13 13:07:48.449  6603-6603  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 服务创建完成
2025-08-13 13:07:48.450  6603-6649  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 后台线程：初始化DSPREAD服务
2025-08-13 13:07:48.458  6603-6603  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] onStartCommand调用
2025-08-13 13:07:48.468  6603-6649  Platform                com.dspread.mdm.service              D  🔧 DspreadService 开始初始化DSPREAD服务
2025-08-13 13:07:48.472  6603-6603  Platform                com.dspread.mdm.service              D  🔧 WakeLockManager 获取智能WakeLock成功，持续时间: 60000ms
2025-08-13 13:07:48.476  6603-6649  ContextImpl             com.dspread.mdm.service              W  Calling a method in the system process without a qualified user: android.app.ContextImpl.bindService:1988 android.content.ContextWrapper.bindService:861 com.dspread.mdm.service.services.DspreadService.bindSysService:110 com.dspread.mdm.service.services.DspreadService.initialize:62 com.dspread.mdm.service.services.SmartMdmBackgroundService.initializeDspreadServiceAsync$lambda$1:225 
2025-08-13 13:07:48.480  6603-6603  Compatibil...geReporter com.dspread.mdm.service              D  Compat change id reported: 160794467; UID 1000; state: ENABLED
2025-08-13 13:07:48.487  6603-6649  Platform                com.dspread.mdm.service              D  🔧 DspreadService 系统服务绑定请求已发送
2025-08-13 13:07:48.490  6603-6603  Platform                com.dspread.mdm.service              D  🔧 WakeLockManager 设置WakeLock续期定时器: 60000ms
2025-08-13 13:07:48.522  6603-6603  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 初始化基础组件
2025-08-13 13:07:48.531  6603-6603  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 开始初始化...
2025-08-13 13:07:48.549  6603-6603  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 包更新广播接收器注册完成
2025-08-13 13:07:48.557  6603-6603  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 统一广播接收器注册完成
2025-08-13 13:07:48.563  6603-6603  Common                  com.dspread.mdm.service              I  ✅ BroadcastManager 初始化完成
2025-08-13 13:07:48.571  6603-6603  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: NetworkEventHandler -> android.net.conn.CONNECTIVITY_CHANGE
2025-08-13 13:07:48.578  6603-6603  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: NetworkEventHandler -> android.net.wifi.STATE_CHANGE
2025-08-13 13:07:48.585  6603-6603  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: BatteryEventHandler -> android.intent.action.BATTERY_CHANGED
2025-08-13 13:07:48.592  6603-6603  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: BatteryEventHandler -> android.intent.action.BATTERY_LOW
2025-08-13 13:07:48.598  6603-6603  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: BatteryEventHandler -> android.intent.action.ACTION_POWER_CONNECTED
2025-08-13 13:07:48.604  6603-6603  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: BatteryEventHandler -> android.intent.action.ACTION_POWER_DISCONNECTED
2025-08-13 13:07:48.612  6603-6603  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: SystemEventHandler -> android.intent.action.BOOT_COMPLETED
2025-08-13 13:07:48.615  6603-6632  Platform                com.dspread.mdm.service              D  🔧 DeviceInfoApi 单例实例已创建
2025-08-13 13:07:48.619  6603-6603  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: SystemEventHandler -> android.intent.action.QUICKBOOT_POWERON
2025-08-13 13:07:48.625  6603-6603  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: SystemEventHandler -> android.intent.action.SCREEN_ON
2025-08-13 13:07:48.631  6603-6603  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: SystemEventHandler -> android.intent.action.SCREEN_OFF
2025-08-13 13:07:48.638  6603-6603  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: SystemEventHandler -> android.intent.action.ACTION_SHUTDOWN
2025-08-13 13:07:48.645  6603-6603  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: SystemEventHandler -> android.intent.action.REBOOT
2025-08-13 13:07:48.654  6603-6603  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: TimerEventHandler -> com.dspread.mdm.service.POLL_TIMER_START
2025-08-13 13:07:48.665  6603-6603  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: PackageUpdateEventHandler -> android.intent.action.PACKAGE_ADDED
2025-08-13 13:07:48.671  6603-6603  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: PackageUpdateEventHandler -> android.intent.action.PACKAGE_REMOVED
2025-08-13 13:07:48.678  6603-6603  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: PackageUpdateEventHandler -> android.intent.action.PACKAGE_REPLACED
2025-08-13 13:07:48.684  6603-6603  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: PackageUpdateEventHandler -> com.dspread.mdm.service.CHECK_SELF_UPDATE_STATUS
2025-08-13 13:07:48.693  6603-6603  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: TaskExecuteEventHandler -> com.dspread.mdm.service.WSTASK_EXEC_BC
2025-08-13 13:07:48.700  6603-6603  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: TerminalInfoEventHandler -> com.dspread.mdm.service.TER_INFO_UPLOAD_BC
2025-08-13 13:07:48.706  6603-6603  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: ServiceGuardEventHandler -> com.dspread.mdm.service.SERVICE_GUARD_TIMER
2025-08-13 13:07:48.714  6603-6603  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: ProvisioningEventHandler -> com.dspread.mdm.service.PROVISIONING_TIMER
2025-08-13 13:07:48.720  6603-6603  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: ProvisioningEventHandler -> android.net.conn.CONNECTIVITY_CHANGE
2025-08-13 13:07:48.726  6603-6603  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: ProvisioningEventHandler -> android.intent.action.BOOT_COMPLETED
2025-08-13 13:07:48.770  6603-6603  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 服务初始化完成
2025-08-13 13:07:48.785  6603-6603  Platform                com.dspread.mdm.service              D  🔧 DspreadService 系统服务连接成功: ComponentInfo{com.dspread.sdkservice/com.dspread.sdkservice.SDKSysService}
2025-08-13 13:07:48.792  6603-6603  Platform                com.dspread.mdm.service              D  🔧 DspreadService 系统服务绑定完成
2025-08-13 13:07:48.799  6603-6649  ContextImpl             com.dspread.mdm.service              W  Calling a method in the system process without a qualified user: android.app.ContextImpl.bindService:1988 android.content.ContextWrapper.bindService:861 com.dspread.mdm.service.services.DspreadService.bindDevService:165 com.dspread.mdm.service.services.DspreadService.initialize:65 com.dspread.mdm.service.services.SmartMdmBackgroundService.initializeDspreadServiceAsync$lambda$1:225 
2025-08-13 13:07:48.803   644-916   BufferQueueDebug        surfaceflinger                       E  [Surface(name=57afeb6 Splash Screen com.dspread.mdm.service)/@0xb4d8e8b - animation-leash of window_animation#3314](this:0xaecffc40,id:-1,api:0,p:-1,c:-1) id info cannot be read from 'Surface(name=57afeb6 Splash Screen com.dspread.mdm.service)/@0xb4d8e8b - animation-leash of window_animation#3314'
2025-08-13 13:07:48.807  6603-6603  Receiver                com.dspread.mdm.service              D  🔧 电池状态变化: 电量=84%, 温度=32°C, 充电=true
2025-08-13 13:07:48.807  6603-6649  Platform                com.dspread.mdm.service              D  🔧 DspreadService 设备服务绑定请求已发送
2025-08-13 13:07:48.851  6603-6603  WebSocket               com.dspread.mdm.service              I  🔧 C0902 被动式上送: battery_level_change_5 (被动: 1)
2025-08-13 13:07:48.869  6603-6603  Platform                com.dspread.mdm.service              E  ❌ DspreadService 设备服务不可用，无法获取序列号
2025-08-13 13:07:48.891  6603-6603  WebSocket               com.dspread.mdm.service              I  🔧 消息已缓存: C0902 (缓存数量: 1)
2025-08-13 13:07:48.897  6603-6603  WebSocket               com.dspread.mdm.service              I  🔧 C0902 电池状态上送成功 (1, trigger=battery_level_change_5)
2025-08-13 13:07:48.906  6603-6603  Platform                com.dspread.mdm.service              D  🔧 NetworkApi 单例实例已创建
2025-08-13 13:07:48.917  6603-6603  Receiver                com.dspread.mdm.service              D  🔧 网络状态变化: 连接=true, 类型=WIFI
2025-08-13 13:07:48.924  6603-6603  Receiver                com.dspread.mdm.service              D  🔧 网络恢复，尝试重新连接WebSocket
2025-08-13 13:07:48.930  6603-6603  Provisioning            com.dspread.mdm.service              D  🔧 ProvisioningEventHandler 网络状态变化
2025-08-13 13:07:48.936  6603-6603  Provisioning            com.dspread.mdm.service              D  🔧 ProvisioningEventHandler 忽略初始启动时的网络连接事件
2025-08-13 13:07:49.025  6603-6603  WebSocket               com.dspread.mdm.service              I  🔧 C0905 主动式上送: manual_trigger (主动: 1)
2025-08-13 13:07:49.040  6603-6603  WebSocket               com.dspread.mdm.service              I  🔧 📊 首次上送，上送当日数据
2025-08-13 13:07:49.047  6603-6603  WebSocket               com.dspread.mdm.service              I  🔧 📊 没有流量数据需要上送
2025-08-13 13:07:49.052  6603-6603  Common                  com.dspread.mdm.service              D  🔧 刷新时自动上送流量统计
2025-08-13 13:07:49.087  6603-6603  Platform                com.dspread.mdm.service              D  🔧 DspreadService 设备服务连接成功: ComponentInfo{com.dspread.sdkservice/com.dspread.sdkservice.SDKDevService}
2025-08-13 13:07:49.094  6603-6603  Platform                com.dspread.mdm.service              D  🔧 DspreadService 设备服务绑定完成
2025-08-13 13:07:49.120  6603-6632  Platform                com.dspread.mdm.service              D  🔧 DspreadService 解析SP版本: V1.0.5
2025-08-13 13:07:49.126  6603-6632  Platform                com.dspread.mdm.service              D  🔧 DspreadService 首次获取SP版本成功: V1.0.5 (原始: *SP_VERSION:V1.0.5#*COMMIT:2d7cc90#*DATETIME:20250...)
2025-08-13 13:07:49.199  6603-6603  Common                  com.dspread.mdm.service              D  🔧 TestActivity UI已更新: SP=V1.0.5, SN=01354090202503050399
2025-08-13 13:07:50.101  6603-6649  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 等待DSPREAD服务就绪 (1/10): true
2025-08-13 13:07:50.107  6603-6649  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] DSPREAD服务就绪，启动Provisioning
2025-08-13 13:07:50.112  6603-6649  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 验证DSPREAD服务功能
2025-08-13 13:07:50.118  6603-6649  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 设备序列号验证: 成功(01354090202503050399)
2025-08-13 13:07:50.123  6603-6649  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 服务状态 - 系统服务:✅ 设备服务:✅
2025-08-13 13:07:50.130  6603-6656  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 启动ProvisioningService
2025-08-13 13:07:50.142  6603-6656  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] ProvisioningService启动完成
2025-08-13 13:07:50.142  6603-6632  Provisioning            com.dspread.mdm.service              I  ℹ️ 开始执行Provisioning配置，触发类型: FIRST_BOOT
2025-08-13 13:07:50.150  6603-6632  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] Provisioning进度: 正在下载配置文件...
2025-08-13 13:07:50.157  6603-6632  Provisioning            com.dspread.mdm.service              I  ℹ️ 开始请求远程配置...
2025-08-13 13:07:50.166  6603-6632  Provisioning            com.dspread.mdm.service              I  ℹ️ 请求配置: http://***********:8080/status/config?SN=MDEzNTQwOTAyMDI1MDMwNTAzOTk=&timestamp=1755061670165
2025-08-13 13:07:50.198  6603-6632  TrafficStats            com.dspread.mdm.service              D  tagSocket(120) with statsTag=0xffffffff, statsUid=-1
2025-08-13 13:07:50.400  6603-6632  Provisioning            com.dspread.mdm.service              I  ℹ️ 远程配置获取成功
2025-08-13 13:07:50.411  6603-6632  Provisioning            com.dspread.mdm.service              I  ℹ️ 远程配置请求成功
2025-08-13 13:07:50.421  6603-6632  Provisioning            com.dspread.mdm.service              I  ℹ️ 远程配置解析成功 - CID: 10012001
2025-08-13 13:07:50.464  6603-6632  Provisioning            com.dspread.mdm.service              I  ℹ️ API响应已保存到本地: /data/pos/config/provisioning_config.json, 具体内容为:
                                                                                                    {"mode":"dev","data":{"requestTime":"1755061669431","customization":{"system":{"timezone":"Asia\/Hong_Kong","logo":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/test\/logo.bin","logoMd5":"ba1ee533924eae5c408465e7cddcbda4","bootAnimationMd5":"ba1ee533924eae5c408465e7cddcbda4","gps":{"minUpdateTime":"30","scheduleTime":"60","maxLocateTime":"0","minDistance":"10","valid_distance":"500","care":"1"},"powerSaveMode":{"enable":"1","screenTimeout":"0"},"bootAnimation":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/test\/bootanimation.zip"},"polling":{"heartbeatTime":"300","terminalInfoTime":"900","statusApiUrl":"ws:\/\/***********:8080\/status\/websocket\/register","remoteUrl":"ws:\/\/***********:8080\/remoteWSS\/websockify","uploadMode":"1","wssreconn":{"delayPolicy":"30","delaySwitch":"1","delayTime":"900"}}},"client":"default","cid":"10012001"},"function":"getProvisioningConfig","description":"SUCCESS","stateCode":"0","version":"v1.0.1.20250801"}
2025-08-13 13:07:50.471  6603-6632  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] Provisioning进度: 正在下载 Logo...
2025-08-13 13:07:50.481  6603-6632  Provisioning            com.dspread.mdm.service              I  ℹ️ 文件不存在，开始下载: Logo
2025-08-13 13:07:50.487  6603-6632  Provisioning            com.dspread.mdm.service              I  ℹ️ 下载: https://siot-bucket01.s3.ap-northeast-1.amazonaws.com/test/logo.bin -> /data/data/com.dspread.mdm.service/files/media/logo/logo.bin
2025-08-13 13:07:50.496  6603-6632  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 尝试下载，第1次
2025-08-13 13:07:50.502  6603-6632  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 开始下载
2025-08-13 13:07:50.508  6603-6632  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader URL: https://siot-bucket01.s3.ap-northeast-1.amazonaws.com/test/logo.bin
2025-08-13 13:07:50.514  6603-6632  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 路径: /data/data/com.dspread.mdm.service/files/media/logo/logo.bin
2025-08-13 13:07:50.520  6603-6632  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 本地文件不存在，创建文件
2025-08-13 13:07:50.527  6603-6632  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader allFileLength=0, 需要从服务器获取文件大小
2025-08-13 13:07:50.594  6603-6632  TrafficStats            com.dspread.mdm.service              D  tagSocket(145) with statsTag=0xffffffff, statsUid=-1
2025-08-13 13:07:51.918  6603-6634  Platform                com.dspread.mdm.service              D  🔧 NetworkTrafficMonitor 等待下次上送时间: 2025-08-14 00:00:00
2025-08-13 13:07:53.576  6603-6663  ProfileInstaller        com.dspread.mdm.service              D  Installing profile for com.dspread.mdm.service
2025-08-13 13:08:20.628  6603-6632  TrafficStats            com.dspread.mdm.service              D  tagSocket(5) with statsTag=0xffffffff, statsUid=-1
2025-08-13 13:08:37.389  6603-6632  HTTPS                   com.dspread.mdm.service              E  ❌ HttpDownloader download throw Exception e = java.net.ConnectException: Failed to connect to siot-bucket01.s3.ap-northeast-1.amazonaws.com/202.160.129.36:443 (Ask Gemini)
                                                                                                    java.net.ConnectException: Failed to connect to siot-bucket01.s3.ap-northeast-1.amazonaws.com/202.160.129.36:443
                                                                                                    	at com.android.okhttp.internal.io.RealConnection.connectSocket(RealConnection.java:147)
                                                                                                    	at com.android.okhttp.internal.io.RealConnection.connect(RealConnection.java:116)
                                                                                                    	at com.android.okhttp.internal.http.StreamAllocation.findConnection(StreamAllocation.java:186)
                                                                                                    	at com.android.okhttp.internal.http.StreamAllocation.findHealthyConnection(StreamAllocation.java:128)
                                                                                                    	at com.android.okhttp.internal.http.StreamAllocation.newStream(StreamAllocation.java:97)
                                                                                                    	at com.android.okhttp.internal.http.HttpEngine.connect(HttpEngine.java:289)
                                                                                                    	at com.android.okhttp.internal.http.HttpEngine.sendRequest(HttpEngine.java:232)
                                                                                                    	at com.android.okhttp.internal.huc.HttpURLConnectionImpl.execute(HttpURLConnectionImpl.java:465)
                                                                                                    	at com.android.okhttp.internal.huc.HttpURLConnectionImpl.getResponse(HttpURLConnectionImpl.java:411)
                                                                                                    	at com.android.okhttp.internal.huc.HttpURLConnectionImpl.getResponseCode(HttpURLConnectionImpl.java:542)
                                                                                                    	at com.android.okhttp.internal.huc.DelegatingHttpsURLConnection.getResponseCode(DelegatingHttpsURLConnection.java:106)
                                                                                                    	at com.android.okhttp.internal.huc.HttpsURLConnectionImpl.getResponseCode(HttpsURLConnectionImpl.java:30)
                                                                                                    	at com.dspread.mdm.service.network.https.HttpDownloader.fileDownloadByUrl(HttpDownloader.kt:205)
                                                                                                    	at com.dspread.mdm.service.network.https.HttpDownloader.fileDownloadByUrlWithRetry(HttpDownloader.kt:72)
                                                                                                    	at com.dspread.mdm.service.modules.provisioning.ProvisioningManager$executeDownloadTask$2.invokeSuspend(ProvisioningManager.kt:394)
                                                                                                    	at com.dspread.mdm.service.modules.provisioning.ProvisioningManager$executeDownloadTask$2.invoke(Unknown Source:8)
                                                                                                    	at com.dspread.mdm.service.modules.provisioning.ProvisioningManager$executeDownloadTask$2.invoke(Unknown Source:4)
                                                                                                    	at kotlinx.coroutines.intrinsics.UndispatchedKt.startUndispatchedOrReturn(Undispatched.kt:78)
                                                                                                    	at kotlinx.coroutines.BuildersKt__Builders_commonKt.withContext(Builders.common.kt:167)
                                                                                                    	at kotlinx.coroutines.BuildersKt.withContext(Unknown Source:1)
                                                                                                    	at com.dspread.mdm.service.modules.provisioning.ProvisioningManager.executeDownloadTask(ProvisioningManager.kt:305)
                                                                                                    	at com.dspread.mdm.service.modules.provisioning.ProvisioningManager.access$executeDownloadTask(ProvisioningManager.kt:24)
                                                                                                    	at com.dspread.mdm.service.modules.provisioning.ProvisioningManager$executeProvisioning$1.invokeSuspend(ProvisioningManager.kt:130)
                                                                                                    	at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
                                                                                                    	at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:108)
                                                                                                    	at kotlinx.coroutines.internal.LimitedDispatcher$Worker.run(LimitedDispatcher.kt:115)
                                                                                                    	at kotlinx.coroutines.scheduling.TaskImpl.run(Tasks.kt:103)
                                                                                                    	at kotlinx.coroutines.scheduling.CoroutineScheduler.runSafely(CoroutineScheduler.kt:584)
                                                                                                    	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.executeTask(CoroutineScheduler.kt:793)
                                                                                                    	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.runWorker(CoroutineScheduler.kt:697)
                                                                                                    	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.run(CoroutineScheduler.kt:684)
2025-08-13 13:08:37.441  6603-6603  Receiver                com.dspread.mdm.service              D  🔧 网络状态变化: 连接=false, 类型=NONE
2025-08-13 13:08:37.445  6603-6632  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 清理连接资源
2025-08-13 13:08:37.449  6603-6603  Receiver                com.dspread.mdm.service              D  🔧 网络不可用，WebSocket连接可能受影响
2025-08-13 13:08:37.451  6603-6632  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 尝试下载，第2次
2025-08-13 13:08:37.461  6603-6632  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 开始下载
2025-08-13 13:08:37.463  6603-6603  Provisioning            com.dspread.mdm.service              D  🔧 ProvisioningEventHandler 网络状态变化
2025-08-13 13:08:37.469  6603-6603  Provisioning            com.dspread.mdm.service              D  🔧 ProvisioningEventHandler 忽略初始启动时的网络连接事件
2025-08-13 13:08:37.470  6603-6632  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader URL: https://siot-bucket01.s3.ap-northeast-1.amazonaws.com/test/logo.bin
2025-08-13 13:08:37.477  6603-6632  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 路径: /data/data/com.dspread.mdm.service/files/media/logo/logo.bin
2025-08-13 13:08:37.488  6603-6632  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader allFileLength=0, 需要从服务器获取文件大小
2025-08-13 13:08:37.512  6603-6632  HTTPS                   com.dspread.mdm.service              E  ❌ HttpDownloader download throw Exception e = java.net.UnknownHostException: Unable to resolve host "siot-bucket01.s3.ap-northeast-1.amazonaws.com": No address associated with hostname
2025-08-13 13:08:37.529  6603-6632  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 清理连接资源
2025-08-13 13:08:37.536  6603-6632  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 尝试下载，第3次
2025-08-13 13:08:37.545  6603-6632  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 开始下载
2025-08-13 13:08:37.551  6603-6632  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader URL: https://siot-bucket01.s3.ap-northeast-1.amazonaws.com/test/logo.bin
2025-08-13 13:08:37.558  6603-6632  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 路径: /data/data/com.dspread.mdm.service/files/media/logo/logo.bin
2025-08-13 13:08:37.566  6603-6632  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader allFileLength=0, 需要从服务器获取文件大小
2025-08-13 13:08:37.585  6603-6632  HTTPS                   com.dspread.mdm.service              E  ❌ HttpDownloader download throw Exception e = java.net.UnknownHostException: Unable to resolve host "siot-bucket01.s3.ap-northeast-1.amazonaws.com": No address associated with hostname
2025-08-13 13:08:37.600  6603-6632  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 清理连接资源
2025-08-13 13:08:37.608  6603-6632  HTTPS                   com.dspread.mdm.service              E  ❌ HttpDownloader 下载失败，错误码: 0
2025-08-13 13:08:37.628  6603-6632  Provisioning            com.dspread.mdm.service              E  ❌ Provisioning执行失败 (Ask Gemini)
                                                                                                    java.lang.Exception: Logo 下载失败: file download failed.
                                                                                                    	at com.dspread.mdm.service.modules.provisioning.ProvisioningManager$executeDownloadTask$2$success$2.requestFail(ProvisioningManager.kt:404)
                                                                                                    	at com.dspread.mdm.service.network.https.HttpDownloader.fileDownloadByUrlWithRetry(HttpDownloader.kt:96)
                                                                                                    	at com.dspread.mdm.service.modules.provisioning.ProvisioningManager$executeDownloadTask$2.invokeSuspend(ProvisioningManager.kt:394)
                                                                                                    	at com.dspread.mdm.service.modules.provisioning.ProvisioningManager$executeDownloadTask$2.invoke(Unknown Source:8)
                                                                                                    	at com.dspread.mdm.service.modules.provisioning.ProvisioningManager$executeDownloadTask$2.invoke(Unknown Source:4)
                                                                                                    	at kotlinx.coroutines.intrinsics.UndispatchedKt.startUndispatchedOrReturn(Undispatched.kt:78)
                                                                                                    	at kotlinx.coroutines.BuildersKt__Builders_commonKt.withContext(Builders.common.kt:167)
                                                                                                    	at kotlinx.coroutines.BuildersKt.withContext(Unknown Source:1)
                                                                                                    	at com.dspread.mdm.service.modules.provisioning.ProvisioningManager.executeDownloadTask(ProvisioningManager.kt:305)
                                                                                                    	at com.dspread.mdm.service.modules.provisioning.ProvisioningManager.access$executeDownloadTask(ProvisioningManager.kt:24)
                                                                                                    	at com.dspread.mdm.service.modules.provisioning.ProvisioningManager$executeProvisioning$1.invokeSuspend(ProvisioningManager.kt:130)
                                                                                                    	at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
                                                                                                    	at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:108)
                                                                                                    	at kotlinx.coroutines.internal.LimitedDispatcher$Worker.run(LimitedDispatcher.kt:115)
                                                                                                    	at kotlinx.coroutines.scheduling.TaskImpl.run(Tasks.kt:103)
                                                                                                    	at kotlinx.coroutines.scheduling.CoroutineScheduler.runSafely(CoroutineScheduler.kt:584)
                                                                                                    	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.executeTask(CoroutineScheduler.kt:793)
                                                                                                    	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.runWorker(CoroutineScheduler.kt:697)
                                                                                                    	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.run(CoroutineScheduler.kt:684)
2025-08-13 13:08:37.639  6603-6632  Service                 com.dspread.mdm.service              E  ❌ [SmartMdmBackgroundService] Provisioning失败: 配置执行失败: Logo 下载失败: file download failed.，使用降级配置
2025-08-13 13:08:37.646  6603-6603  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 开始初始化核心组件
2025-08-13 13:08:37.655  6603-6632  Provisioning            com.dspread.mdm.service              I  ℹ️ 创建紧急默认配置
2025-08-13 13:08:37.662  6603-6632  Provisioning            com.dspread.mdm.service              D  🔧 配置文件已存在，跳过默认配置初始化
2025-08-13 13:08:37.667  6603-6603  WebSocket               com.dspread.mdm.service              I  🔧 开始初始化 WebSocket 组件...
2025-08-13 13:08:37.669  6603-6632  Provisioning            com.dspread.mdm.service              I  ℹ️ Provisioning完成: 即使配置失败也启动业务流程
2025-08-13 13:08:37.674  6603-6603  WebSocket               com.dspread.mdm.service              I  🔧 初始化 WebSocket 密钥管理器
2025-08-13 13:08:37.681  6603-6603  WebSocket               com.dspread.mdm.service              I  🔧 生成客户端公私钥对
2025-08-13 13:08:37.765  6603-6603  Common                  com.dspread.mdm.service              I  ✅ WebSocket 密钥管理器初始化完成
2025-08-13 13:08:37.777  6603-6603  WebSocket               com.dspread.mdm.service              I  🔧 开始初始化 WsMessageCenter...
2025-08-13 13:08:37.784  6603-6603  WebSocket               com.dspread.mdm.service              I  🔧 WsMessageCenter 首次初始化
2025-08-13 13:08:37.806  6603-6603  WebSocket               com.dspread.mdm.service              I  🔧 WsTaskManager 初始化完成
2025-08-13 13:08:37.940  6603-6603  WebSocket               com.dspread.mdm.service              I  🔧 WsMessageCenter 初始化完成
2025-08-13 13:08:37.947  6603-6603  WebSocket               com.dspread.mdm.service              I  🔧 🧹 开始清理终态任务，当前任务数量: 0
2025-08-13 13:08:37.953  6603-6603  WebSocket               com.dspread.mdm.service              I  🔧 ℹ️ 没有需要清理的终态任务
2025-08-13 13:08:37.960  6603-6603  WebSocket               com.dspread.mdm.service              I  🔧 开始重置任务状态，当前任务个数: 0
2025-08-13 13:08:37.966  6603-6603  WebSocket               com.dspread.mdm.service              I  🔧 没有需要重置的任务
2025-08-13 13:08:37.972  6603-6603  Common                  com.dspread.mdm.service              I  ✅ 任务状态恢复完成
2025-08-13 13:08:37.990  6603-6603  WebSocket               com.dspread.mdm.service              I  🔧 使用Provisioning配置的WebSocket URL: ws://***********:8080/status/websocket/register
2025-08-13 13:08:37.997  6603-6603  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接管理器初始化完成
2025-08-13 13:08:38.004  6603-6603  Common                  com.dspread.mdm.service              I  ✅ WebSocket 管理器初始化完成
2025-08-13 13:08:38.010  6603-6603  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 管理器初始化完成
2025-08-13 13:08:38.018  6603-6603  Common                  com.dspread.mdm.service              I  ✅ WebSocket 组件初始化完成
2025-08-13 13:08:38.024  6603-6603  WebSocket               com.dspread.mdm.service              I  🔧 启动 WebSocket 连接...
2025-08-13 13:08:38.031  6603-6603  WebSocket               com.dspread.mdm.service              I  🔧 开始连接 WebSocket...
2025-08-13 13:08:38.060  6603-6603  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接到: ws://***********:8080/status/websocket/register?serialNo=MDEzNTQwOTAyMDI1MDMwNTAzOTk=&publicKey=TUlHZk1BMEdDU3FHU0liM0RRRUJBUVVBQTRHTkFEQ0JpUUtCZ1FEQjVUM0ZBRzBnN09oejZOQlkrWldhdHZBLytmNVlPOGh6czBFUFFzVERqSFU0ZkNGQ25IeG9EZS9JTmpYSUxzeWFUWXYxQ3JKM0UyR21PNHF5NnpxMUhiL1Mra2hKQy81czV6Z29HenpwRm5vR1RlY1MrVkFxcHNNTFQvSER2ckZ4NCtIRm5Qa3AxdE83REhxei9oQWZDKzZCQjdOTkdVVjZXdFJQVEJoa3V3SURBUUFC&query=1&msgVer=3&timestamp=1755061718044&signature=EsFWL8u6sETsCn9QMw9L8Rr8uWkGtXJ3dF/OCvzlSFJiNU/LCmGCWMJlfy0K4bzlFNsEfy7ONmjz/+XLzDyrXnZtXQBQqS7o6zTQQRGwDn+lwaeqpqHMRfAumnzyDoHPrsEF1GraNcf8eUfEivK1YneKppNT8KZs8J5ZtIOPXPE=
2025-08-13 13:08:38.071  6603-6603  WebSocket               com.dspread.mdm.service              I  🔧 未检测到系统代理设置
2025-08-13 13:08:38.106  6603-6603  WebSocket               com.dspread.mdm.service              I  🔧 ===== WebSocket 库默认配置 =====
2025-08-13 13:08:38.114  6603-6603  WebSocket               com.dspread.mdm.service              I  🔧 默认 PING 间隔: 60000ms (60秒)
2025-08-13 13:08:38.120  6603-6603  WebSocket               com.dspread.mdm.service              I  🔧 默认 PONG 间隔: 0ms (0秒)
2025-08-13 13:08:38.127  6603-6603  WebSocket               com.dspread.mdm.service              I  🔧 帧队列大小: 5
2025-08-13 13:08:38.134  6603-6603  WebSocket               com.dspread.mdm.service              I  🔧 连接超时: 15000ms (15秒)
2025-08-13 13:08:38.140  6603-6603  WebSocket               com.dspread.mdm.service              I  🔧 允许缺失关闭帧: false
2025-08-13 13:08:38.147  6603-6603  WebSocket               com.dspread.mdm.service              I  🔧 ================================
2025-08-13 13:08:38.154  6603-6603  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接请求已发送
2025-08-13 13:08:38.160  6603-6685  TrafficStats            com.dspread.mdm.service              D  tagSocket(5) with statsTag=0xffffffff, statsUid=-1
2025-08-13 13:08:38.163  6603-6603  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接启动完成
2025-08-13 13:08:38.171  6603-6603  Common                  com.dspread.mdm.service              D  🔧 [ModuleManagerRegistry] 模块管理器注册中心初始化完成
2025-08-13 13:08:38.178  6603-6684  WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误 (Ask Gemini)
                                                                                                    com.neovisionaries.ws.client.WebSocketException: Failed to connect to '***********:8080': failed to connect to /*********** (port 8080) from /:: (port 0) after 15000ms: connect failed: ENETUNREACH (Network is unreachable)
                                                                                                    	at com.neovisionaries.ws.client.SocketConnector.connectSocket(SocketConnector.java:126)
                                                                                                    	at com.neovisionaries.ws.client.SocketConnector.doConnect(SocketConnector.java:238)
                                                                                                    	at com.neovisionaries.ws.client.SocketConnector.connect(SocketConnector.java:189)
                                                                                                    	at com.neovisionaries.ws.client.WebSocket.connect(WebSocket.java:2351)
                                                                                                    	at com.neovisionaries.ws.client.ConnectThread.runMain(ConnectThread.java:32)
                                                                                                    	at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45)
                                                                                                    Caused by: java.net.ConnectException: failed to connect to /*********** (port 8080) from /:: (port 0) after 15000ms: connect failed: ENETUNREACH (Network is unreachable)
                                                                                                    	at libcore.io.IoBridge.connect(IoBridge.java:187)
                                                                                                    	at java.net.PlainSocketImpl.socketConnect(PlainSocketImpl.java:142)
                                                                                                    	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:390)
                                                                                                    	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:230)
                                                                                                    	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:212)
                                                                                                    	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:436)
                                                                                                    	at java.net.Socket.connect(Socket.java:646)
                                                                                                    	at com.neovisionaries.ws.client.SocketInitiator$SocketRacer.run(SocketInitiator.java:126)
                                                                                                    Caused by: android.system.ErrnoException: connect failed: ENETUNREACH (Network is unreachable)
                                                                                                    	at libcore.io.Linux.connect(Native Method)
                                                                                                    	at libcore.io.ForwardingOs.connect(ForwardingOs.java:201)
                                                                                                    	at libcore.io.BlockGuardOs.connect(BlockGuardOs.java:158)
                                                                                                    	at libcore.io.ForwardingOs.connect(ForwardingOs.java:201)
                                                                                                    	at libcore.io.IoBridge.connectErrno(IoBridge.java:218)
                                                                                                    	at libcore.io.IoBridge.connect(IoBridge.java:179)
                                                                                                    	at java.net.PlainSocketImpl.socketConnect(PlainSocketImpl.java:142) 
                                                                                                    	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:390) 
                                                                                                    	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:230) 
                                                                                                    	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:212) 
                                                                                                    	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:436) 
                                                                                                    	at java.net.Socket.connect(Socket.java:646) 
                                                                                                    	at com.neovisionaries.ws.client.SocketInitiator$SocketRacer.run(SocketInitiator.java:126) 
2025-08-13 13:08:38.179  6603-6603  Common                  com.dspread.mdm.service              D  🔧 TimerConfig 使用调试模式Provisioning间隔: 300秒
2025-08-13 13:08:38.180  6603-6632  Common                  com.dspread.mdm.service              D  🔧 [ModuleManagerRegistry] 启动所有模块
2025-08-13 13:08:38.187  6603-6603  Provisioning            com.dspread.mdm.service              D  🔧 ProvisioningEventHandler 启动Provisioning定时器: 300s (0小时)
2025-08-13 13:08:38.187  6603-6684  WebSocket               com.dspread.mdm.service              I  🔧 onError: 不触发重连，等待 onDisconnected 处理
2025-08-13 13:08:38.193  6603-6603  ContextImpl             com.dspread.mdm.service              W  Calling a method in the system process without a qualified user: android.app.ContextImpl.sendBroadcast:1220 android.content.ContextWrapper.sendBroadcast:504 com.dspread.mdm.service.broadcast.handlers.system.ProvisioningEventHandler.startProvisioningTimer:106 com.dspread.mdm.service.services.SmartMdmBackgroundService.startProvisioningTimer:391 com.dspread.mdm.service.services.SmartMdmBackgroundService.initializeCoreComponents:317 
2025-08-13 13:08:38.195  6603-6632  Common                  com.dspread.mdm.service              D  🔧 [ModuleManagerRegistry] LogStreamManager实例创建完成
2025-08-13 13:08:38.196  6603-6603  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] Provisioning定时器启动成功
2025-08-13 13:08:38.201  6603-6684  WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误 (Ask Gemini)
                                                                                                    com.neovisionaries.ws.client.WebSocketException: Failed to connect to '***********:8080': failed to connect to /*********** (port 8080) from /:: (port 0) after 15000ms: connect failed: ENETUNREACH (Network is unreachable)
                                                                                                    	at com.neovisionaries.ws.client.SocketConnector.connectSocket(SocketConnector.java:126)
                                                                                                    	at com.neovisionaries.ws.client.SocketConnector.doConnect(SocketConnector.java:238)
                                                                                                    	at com.neovisionaries.ws.client.SocketConnector.connect(SocketConnector.java:189)
                                                                                                    	at com.neovisionaries.ws.client.WebSocket.connect(WebSocket.java:2351)
                                                                                                    	at com.neovisionaries.ws.client.ConnectThread.runMain(ConnectThread.java:32)
                                                                                                    	at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45)
                                                                                                    Caused by: java.net.ConnectException: failed to connect to /*********** (port 8080) from /:: (port 0) after 15000ms: connect failed: ENETUNREACH (Network is unreachable)
                                                                                                    	at libcore.io.IoBridge.connect(IoBridge.java:187)
                                                                                                    	at java.net.PlainSocketImpl.socketConnect(PlainSocketImpl.java:142)
                                                                                                    	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:390)
                                                                                                    	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:230)
                                                                                                    	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:212)
                                                                                                    	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:436)
                                                                                                    	at java.net.Socket.connect(Socket.java:646)
                                                                                                    	at com.neovisionaries.ws.client.SocketInitiator$SocketRacer.run(SocketInitiator.java:126)
                                                                                                    Caused by: android.system.ErrnoException: connect failed: ENETUNREACH (Network is unreachable)
                                                                                                    	at libcore.io.Linux.connect(Native Method)
                                                                                                    	at libcore.io.ForwardingOs.connect(ForwardingOs.java:201)
                                                                                                    	at libcore.io.BlockGuardOs.connect(BlockGuardOs.java:158)
                                                                                                    	at libcore.io.ForwardingOs.connect(ForwardingOs.java:201)
                                                                                                    	at libcore.io.IoBridge.connectErrno(IoBridge.java:218)
                                                                                                    	at libcore.io.IoBridge.connect(IoBridge.java:179)
                                                                                                    	at java.net.PlainSocketImpl.socketConnect(PlainSocketImpl.java:142) 
                                                                                                    	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:390) 
                                                                                                    	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:230) 
                                                                                                    	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:212) 
                                                                                                    	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:436) 
                                                                                                    	at java.net.Socket.connect(Socket.java:646) 
                                                                                                    	at com.neovisionaries.ws.client.SocketInitiator$SocketRacer.run(SocketInitiator.java:126) 
2025-08-13 13:08:38.202  6603-6632  Common                  com.dspread.mdm.service              D  🔧 Module LogStream Manager status changed to: INITIALIZED
2025-08-13 13:08:38.203  6603-6603  Common                  com.dspread.mdm.service              D  🔧 TimerConfig ========== 当前定时器配置 (调试模式) ==========
2025-08-13 13:08:38.209  6603-6603  Common                  com.dspread.mdm.service              D  🔧 TimerConfig 使用调试模式心跳间隔: 60秒
2025-08-13 13:08:38.210  6603-6684  WebSocket               com.dspread.mdm.service              I  🔧 reconnect() 被调用，调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onConnectError:681
2025-08-13 13:08:38.214  6603-6632  Common                  com.dspread.mdm.service              D  🔧 [ModuleManagerRegistry] LogStreamManager初始化成功
2025-08-13 13:08:38.215  6603-6603  Common                  com.dspread.mdm.service              D  🔧 TimerConfig ①心跳定时器: 60秒
2025-08-13 13:08:38.217  6603-6684  WebSocket               com.dspread.mdm.service              I  🔧 开始重连 WebSocket... (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onConnectError:681)
2025-08-13 13:08:38.221  6603-6603  Common                  com.dspread.mdm.service              D  🔧 TimerConfig 使用调试模式终端信息上传间隔: 120秒
2025-08-13 13:08:38.222  6603-6632  Common                  com.dspread.mdm.service              D  🔧 Module LogStream Manager status changed to: STARTING
2025-08-13 13:08:38.229  6603-6603  Common                  com.dspread.mdm.service              D  🔧 TimerConfig ②终端信息上传: 120秒
2025-08-13 13:08:38.234  6603-6684  WebSocket               com.dspread.mdm.service              I  🔧 使用Provisioning配置的重连策略: delayPolicy=30, delaySwitch=1, delayTime=900
2025-08-13 13:08:38.236  6603-6603  Common                  com.dspread.mdm.service              D  🔧 TimerConfig 使用调试模式任务执行间隔: 60秒
2025-08-13 13:08:38.241  6603-6603  Common                  com.dspread.mdm.service              D  🔧 TimerConfig ③任务执行: 60秒
2025-08-13 13:08:38.242  6603-6684  WebSocket               com.dspread.mdm.service              I  🔧 当前断开记录数: 0, 延迟开关: 1
2025-08-13 13:08:38.249  6603-6684  WebSocket               com.dspread.mdm.service              I  🔧 重连策略计算完成: 第1次重连，间隔3000ms (3秒)
2025-08-13 13:08:38.250  6603-6603  Common                  com.dspread.mdm.service              D  🔧 TimerConfig 使用调试模式服务守护间隔: 120秒
2025-08-13 13:08:38.257  6603-6684  WebSocket               com.dspread.mdm.service              I  🔧 开始第1次重连，间隔3000ms (3秒) (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onConnectError:681)
2025-08-13 13:08:38.258  6603-6603  Common                  com.dspread.mdm.service              D  🔧 TimerConfig ④服务守护: 120秒
2025-08-13 13:08:38.265  6603-6603  Common                  com.dspread.mdm.service              D  🔧 TimerConfig 使用调试模式Provisioning间隔: 300秒
2025-08-13 13:08:38.272  6603-6603  Common                  com.dspread.mdm.service              D  🔧 TimerConfig ⑤Provisioning: 300秒
2025-08-13 13:08:38.280  6603-6603  Common                  com.dspread.mdm.service              D  🔧 TimerConfig ===============================================
2025-08-13 13:08:38.287  6603-6603  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 核心组件初始化完成
2025-08-13 13:08:38.307  6603-6632  Common                  com.dspread.mdm.service              D  🔧 Module LogStream Manager status changed to: RUNNING
2025-08-13 13:08:38.315  6603-6632  Common                  com.dspread.mdm.service              D  🔧 [ModuleManagerRegistry] LogStreamManager启动成功
2025-08-13 13:08:38.323  6603-6632  Common                  com.dspread.mdm.service              D  🔧 [ModuleManagerRegistry] LogStreamManager启动成功
2025-08-13 13:08:38.330  6603-6632  Common                  com.dspread.mdm.service              D  🔧 [ModuleManagerRegistry] 开始初始化地理围栏管理器
2025-08-13 13:08:38.356  6603-6632  Compatibil...geReporter com.dspread.mdm.service              D  Compat change id reported: 263076149; UID 1000; state: ENABLED
2025-08-13 13:08:38.367  6603-6632  Common                  com.dspread.mdm.service              D  🔧 Module Geofence Manager status changed to: INITIALIZED
2025-08-13 13:08:38.375  6603-6632  Common                  com.dspread.mdm.service              D  🔧 Module Geofence Manager status changed to: STARTING
2025-08-13 13:08:38.405  6603-6632  Platform                com.dspread.mdm.service              D  🔧 用户交互监控已启动（简化版本）
2025-08-13 13:08:38.411  6603-6632  Platform                com.dspread.mdm.service              D  🔧 使用模拟用户交互检测
2025-08-13 13:08:38.418  6603-6632  Platform                com.dspread.mdm.service              D  🔧 用户交互监控已启动
2025-08-13 13:08:38.426  6603-6632  Service                 com.dspread.mdm.service              D  🔧 [GeofenceManager]Constants.geofenceStatus = 1
2025-08-13 13:08:38.433  6603-6632  Service                 com.dspread.mdm.service              D  🔧 [GeofenceManager]-- Constants.storeId =  -- Constants.storeSsid =  -- Constants.storeIp = 
2025-08-13 13:08:38.441  6603-6632  Common                  com.dspread.mdm.service              D  🔧 Module Geofence Manager status changed to: RUNNING
2025-08-13 13:08:38.449  6603-6632  Common                  com.dspread.mdm.service              D  🔧 [ModuleManagerRegistry] 地理围栏管理器初始化成功
2025-08-13 13:08:38.456  6603-6632  Common                  com.dspread.mdm.service              D  🔧 [ModuleManagerRegistry] GeofenceManager启动成功
2025-08-13 13:08:38.463  6603-6632  Common                  com.dspread.mdm.service              D  🔧 [ModuleManagerRegistry] 所有模块启动完成
2025-08-13 13:08:41.036  6603-6603  Platform                com.dspread.mdm.service              D  🔧 [WiFiManagerApi] WiFiManagerApi 单例实例已创建
2025-08-13 13:08:41.197  6603-6603  Receiver                com.dspread.mdm.service              D  🔧 网络状态变化: 连接=true, 类型=WIFI
2025-08-13 13:08:41.205  6603-6603  Receiver                com.dspread.mdm.service              D  🔧 网络恢复，尝试重新连接WebSocket
2025-08-13 13:08:41.212  6603-6603  Provisioning            com.dspread.mdm.service              D  🔧 ProvisioningEventHandler 网络状态变化
2025-08-13 13:08:41.220  6603-6603  Provisioning            com.dspread.mdm.service              D  🔧 ProvisioningEventHandler 忽略初始启动时的网络连接事件
2025-08-13 13:08:41.279  6603-6603  WebSocket               com.dspread.mdm.service              I  🔧 重连 WebSocket: ws://***********:8080/status/websocket/register?serialNo=MDEzNTQwOTAyMDI1MDMwNTAzOTk=&publicKey=TUlHZk1BMEdDU3FHU0liM0RRRUJBUVVBQTRHTkFEQ0JpUUtCZ1FEQjVUM0ZBRzBnN09oejZOQlkrWldhdHZBLytmNVlPOGh6czBFUFFzVERqSFU0ZkNGQ25IeG9EZS9JTmpYSUxzeWFUWXYxQ3JKM0UyR21PNHF5NnpxMUhiL1Mra2hKQy81czV6Z29HenpwRm5vR1RlY1MrVkFxcHNNTFQvSER2ckZ4NCtIRm5Qa3AxdE83REhxei9oQWZDKzZCQjdOTkdVVjZXdFJQVEJoa3V3SURBUUFC&query=0&msgVer=3&timestamp=1755061721264&signature=sGUIR5l1IRUFRZ/Uh0XuJwY18bGD8HJUqg4YYYnYd3speRbZM+VcP96PG6v5Xt13SCSeDAW0A/xvwFqbeGPkWnph8cNAK6KxvUjIQKYNqcpvJnT+UTWV/wnanVC/kSoycPzYpBqHHavRNxSY8wc+eOq/BrQ6ODD5fNG5oo9pAkQ=
2025-08-13 13:08:41.287  6603-6603  WebSocket               com.dspread.mdm.service              I  🔧 未检测到系统代理设置
2025-08-13 13:08:41.311  6603-6709  TrafficStats            com.dspread.mdm.service              D  tagSocket(167) with statsTag=0xffffffff, statsUid=-1
2025-08-13 13:08:41.506  6603-6710  Common                  com.dspread.mdm.service              I  ✅ WebSocket 物理连接成功，等待服务器 hellMsg
2025-08-13 13:08:41.513  6603-6710  WebSocket               com.dspread.mdm.service              I  🔧 ===== 心跳发送器实际配置 =====
2025-08-13 13:08:41.521  6603-6710  WebSocket               com.dspread.mdm.service              I  🔧 实际 PING 发送间隔: 60000ms (60秒)
2025-08-13 13:08:41.529  6603-6710  WebSocket               com.dspread.mdm.service              I  🔧 PING 定时器名称: PingSender
2025-08-13 13:08:41.536  6603-6710  WebSocket               com.dspread.mdm.service              I  🔧 实际 PONG 发送间隔: 0ms (0秒)
2025-08-13 13:08:41.544  6603-6710  WebSocket               com.dspread.mdm.service              I  🔧 PONG 定时器名称: PongSender
2025-08-13 13:08:41.551  6603-6710  WebSocket               com.dspread.mdm.service              I  🔧 ===============================
2025-08-13 13:08:42.152  6603-6710  Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"hellMsg":"hello 01354090202503050399","tranCode":"S0000","version":"1","rebootTime":"03:05:02","serialNo":"01354090202503050399","deviceStatus":"6"}
2025-08-13 13:08:42.160  6603-6710  WebSocket               com.dspread.mdm.service              I  🔧 收到业务消息 - tranCode: S0000
2025-08-13 13:08:42.167  6603-6710  Common                  com.dspread.mdm.service              I  ✅ 收到服务器 hellMsg，WebSocket 业务连接成功！
2025-08-13 13:08:42.174  6603-6710  Common                  com.dspread.mdm.service              I  ✅ WebSocket 业务连接成功！
2025-08-13 13:08:42.181  6603-6710  WebSocket               com.dspread.mdm.service              I  🔧 业务连接成功，重连计数已重置为 0
2025-08-13 13:08:42.189  6603-6710  ContextImpl             com.dspread.mdm.service              W  Calling a method in the system process without a qualified user: android.app.ContextImpl.sendBroadcast:1220 android.content.ContextWrapper.sendBroadcast:504 com.dspread.mdm.service.broadcast.core.BroadcastSender.sendBroadcast:99 com.dspread.mdm.service.broadcast.core.BroadcastSender.sendBroadcast:23 com.dspread.mdm.service.network.websocket.connection.WsConnectionManager.setWebSocketConnected:272 
2025-08-13 13:08:42.193  6603-6710  Receiver                com.dspread.mdm.service              D  🔧 发送广播: com.dspread.mdm.service.WSTASK_EXEC_BC
2025-08-13 13:08:42.193  6603-6603  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 收到广播: com.dspread.mdm.service.WSTASK_EXEC_BC
2025-08-13 13:08:42.199  6603-6710  WebSocket               com.dspread.mdm.service              I  🔧 已启动任务执行定时器
2025-08-13 13:08:42.200  6603-6603  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 分发广播事件: com.dspread.mdm.service.WSTASK_EXEC_BC -> 1个处理器
2025-08-13 13:08:42.206  6603-6603  Common                  com.dspread.mdm.service              D  🔧 TimerConfig 使用调试模式任务执行间隔: 60秒
2025-08-13 13:08:42.207  6603-6710  ContextImpl             com.dspread.mdm.service              W  Calling a method in the system process without a qualified user: android.app.ContextImpl.sendBroadcast:1220 android.content.ContextWrapper.sendBroadcast:504 com.dspread.mdm.service.broadcast.core.BroadcastSender.sendBroadcast:99 com.dspread.mdm.service.broadcast.core.BroadcastSender.sendBroadcast:23 com.dspread.mdm.service.network.websocket.connection.WsConnectionManager.setWebSocketConnected:276 
2025-08-13 13:08:42.209  6603-6710  Receiver                com.dspread.mdm.service              D  🔧 发送广播: com.dspread.mdm.service.POLL_TIMER_START
2025-08-13 13:08:42.216  6603-6710  WebSocket               com.dspread.mdm.service              I  🔧 已启动心跳定时器
2025-08-13 13:08:42.219  6603-6603  Receiver                com.dspread.mdm.service              D  🔧 没有任务
2025-08-13 13:08:42.223  6603-6710  ContextImpl             com.dspread.mdm.service              W  Calling a method in the system process without a qualified user: android.app.ContextImpl.sendBroadcast:1220 android.content.ContextWrapper.sendBroadcast:504 com.dspread.mdm.service.broadcast.core.BroadcastSender.sendBroadcast:99 com.dspread.mdm.service.broadcast.core.BroadcastSender.sendBroadcast:23 com.dspread.mdm.service.network.websocket.connection.WsConnectionManager.setWebSocketConnected:280 
2025-08-13 13:08:42.226  6603-6710  Receiver                com.dspread.mdm.service              D  🔧 发送广播: com.dspread.mdm.service.TER_INFO_UPLOAD_BC
2025-08-13 13:08:42.226  6603-6603  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 处理器 TaskExecuteEventHandler 处理成功: com.dspread.mdm.service.WSTASK_EXEC_BC
2025-08-13 13:08:42.233  6603-6710  WebSocket               com.dspread.mdm.service              I  🔧 已启动终端信息上传定时器
2025-08-13 13:08:42.240  6603-6710  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接成功，检查自身更新任务状态
2025-08-13 13:08:42.240  6603-6603  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 收到广播: com.dspread.mdm.service.POLL_TIMER_START
2025-08-13 13:08:42.247  6603-6603  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 分发广播事件: com.dspread.mdm.service.POLL_TIMER_START -> 1个处理器
2025-08-13 13:08:42.247  6603-6710  ContextImpl             com.dspread.mdm.service              W  Calling a method in the system process without a qualified user: android.app.ContextImpl.sendBroadcast:1220 android.content.ContextWrapper.sendBroadcast:504 com.dspread.mdm.service.broadcast.core.BroadcastSender.sendBroadcast:99 com.dspread.mdm.service.broadcast.core.BroadcastSender.sendBroadcast:23 com.dspread.mdm.service.network.websocket.connection.WsConnectionManager.checkSelfUpdateTaskStatus:317 
2025-08-13 13:08:42.250  6603-6710  Receiver                com.dspread.mdm.service              D  🔧 发送广播: com.dspread.mdm.service.CHECK_SELF_UPDATE_STATUS
2025-08-13 13:08:42.254  6603-6603  Receiver                com.dspread.mdm.service              D  🔧 收到轮询定时器广播
2025-08-13 13:08:42.256  6603-6710  WebSocket               com.dspread.mdm.service              I  🔧 开始发送缓存消息，数量: 1
2025-08-13 13:08:42.260  6603-6603  WebSocket               com.dspread.mdm.service              I  🔧 WebSocketCenter: 发送心跳
2025-08-13 13:08:42.267  6603-6710  WebSocket               com.dspread.mdm.service              I  🔧 C0902 主动式上送: cached_message_resend (主动: 2)
2025-08-13 13:08:42.267  6603-6603  WebSocket               com.dspread.mdm.service              I  🔧 发送心跳消息
2025-08-13 13:08:42.274  6603-6603  Receiver                com.dspread.mdm.service              D  🔧 发送心跳
2025-08-13 13:08:42.280  6603-6603  Receiver                com.dspread.mdm.service              D  🔧 检查待上传的任务和规则结果
2025-08-13 13:08:42.287  6603-6603  Common                  com.dspread.mdm.service              D  🔧 TimerConfig 使用调试模式心跳间隔: 60秒
2025-08-13 13:08:42.298  6603-6603  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 处理器 TimerEventHandler 处理成功: com.dspread.mdm.service.POLL_TIMER_START
2025-08-13 13:08:42.305  6603-6710  Common                  com.dspread.mdm.service              I  ✅ 缓存消息发送成功: C0902
2025-08-13 13:08:42.305  6603-6603  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 收到广播: com.dspread.mdm.service.TER_INFO_UPLOAD_BC
2025-08-13 13:08:42.312  6603-6603  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 分发广播事件: com.dspread.mdm.service.TER_INFO_UPLOAD_BC -> 1个处理器
2025-08-13 13:08:42.319  6603-6603  Receiver                com.dspread.mdm.service              D  🔧 ⏰ C0109 定时上报触发
2025-08-13 13:08:42.325  6603-6603  Common                  com.dspread.mdm.service              D  🔧 TimerConfig 使用调试模式终端信息上传间隔: 120秒
2025-08-13 13:08:42.336  6603-6603  Common                  com.dspread.mdm.service              I  ✅ ⏰ 设置 C0109 定时器成功，下次执行: 120秒后 (2分钟)
2025-08-13 13:08:42.343  6603-6603  WebSocket               com.dspread.mdm.service              I  🔧 C0109 主动式上送: terminal_info_timer (主动: 3)
2025-08-13 13:08:42.359  6603-6603  Platform                com.dspread.mdm.service              D  🔧 重新收集应用信息（强制刷新）
2025-08-13 13:08:42.467  6603-6603  AppManager              com.dspread.mdm.service              I  ℹ️ 应用信息获取完成: 总数130(系统129/用户1) 返回1个
2025-08-13 13:08:42.481  6603-6603  Platform                com.dspread.mdm.service              D  🔧 应用信息: 1 个应用
2025-08-13 13:08:42.594  6603-6603  Platform                com.dspread.mdm.service              D  🔧 内存总容量为： 1.86GB 
2025-08-13 13:08:42.601  6603-6603  Platform                com.dspread.mdm.service              D  🔧 内存使用量为： 0.79GB 
2025-08-13 13:08:42.672  6603-6603  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0109","serialNo":"01354090202503050399","request_time":"1755061722618","request_id":"1755061722618C0109","version":"1","data":{"apkInfo":[{"packName":"com.dspread.mdm.service","apkName":"service","versionCode":11,"versionName":"1.0.08.20250813.DSPREAD.MDM.SERVICE","updateDate":"2025-08-13 13:07:44"}],"locationInfo":{"gps":{"longitude":"-999.0","latitude":"-999.0","satelliteCount":0,"satelliteCountInUsed":0,"satelliteAverSNR":"0.00","distance":"0.0","lockStatus":"1","lockMeter":"200","proId":"","proName":""},"baseSite":{"MCC":"","MNC":"","CID":"-1","LAC":"-1","DBM":"-999","IMSI":"","ICCID":""},"wifi":{"SSID":"fubox_2.4G","IP":"*************","MAC":"8A:B9:BB:91:76:7E","SSTH":"-39","BSSID":"7c:10:c9:d6:9f:10"},"address":"无法获取"},"hardwareInfo":[{"name":"CPU Usage","desc":"cpu:0.50"},{"name":"storage_usage_status","desc":" 5.41GB \/ 32.00GB ","state":"1"},{"name":"total_memory","desc":" 0.79GB \/ 1.86GB ","state":"1"},{"name":"androidVersion","desc":"14","state":"1"},{"name":"network_status","desc":"wifi","state":"1"},{"name":"OSVer","desc":"14","state":"1"}],"wifiOption":[{"SSID":"360行车记录仪Z300-1203","SSTH":"-82"},{"SSID":"HZA50(74f652)","SSTH":"-35"},{"SSID":"MIFI-9727","SSTH":"-91"}],"sytemInfo":{"androidVersion":"14","buildNumber":"BASE_D30M-MU_GEN_AP_V1.0.8.1_202507032000_ota_test","aspl":"2025-03-05"}},"myVersionName":"1.0.08.20250813.DSPREAD.MDM.SERVICE","terminalDate":"20250813130842"}
2025-08-13 13:08:42.679  6603-6603  WebSocket               com.dspread.mdm.service              I  🔧 C0109 终端信息上传完成 (1, trigger=terminal_info_timer)
2025-08-13 13:08:42.686  6603-6603  Receiver                com.dspread.mdm.service              D  🔧 TerminalInfoEventHandler 终端信息上传完成
2025-08-13 13:08:42.694  6603-6603  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 处理器 TerminalInfoEventHandler 处理成功: com.dspread.mdm.service.TER_INFO_UPLOAD_BC
2025-08-13 13:08:42.813  6603-6710  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接成功
2025-08-13 13:08:42.820  6603-6710  WebSocket               com.dspread.mdm.service              I  🔧 设备状态: 6
2025-08-13 13:08:42.828  6603-6710  WebSocket               com.dspread.mdm.service              I  🔧 启动 C0109 定时器
2025-08-13 13:08:42.836  6603-6710  ContextImpl             com.dspread.mdm.service              W  Calling a method in the system process without a qualified user: android.app.ContextImpl.sendBroadcast:1220 android.content.ContextWrapper.sendBroadcast:504 com.dspread.mdm.service.broadcast.core.BroadcastSender.sendBroadcast:99 com.dspread.mdm.service.broadcast.core.BroadcastSender.sendBroadcast:23 com.dspread.mdm.service.network.websocket.message.WsMessageCenter.startTerminalInfoUploadTimer:388 
2025-08-13 13:08:42.839  6603-6710  Receiver                com.dspread.mdm.service              D  🔧 发送广播: com.dspread.mdm.service.TER_INFO_UPLOAD_BC
2025-08-13 13:08:42.840  6603-6603  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 收到广播: com.dspread.mdm.service.TER_INFO_UPLOAD_BC
2025-08-13 13:08:42.845  6603-6710  WebSocket               com.dspread.mdm.service              I  🔧 检查首次连接标志: isFirstTimeWebSocketConnected=true
2025-08-13 13:08:42.846  6603-6603  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 分发广播事件: com.dspread.mdm.service.TER_INFO_UPLOAD_BC -> 1个处理器
2025-08-13 13:08:42.853  6603-6603  Receiver                com.dspread.mdm.service              D  🔧 ⏰ C0109 定时上报触发
2025-08-13 13:08:42.853  6603-6710  WebSocket               com.dspread.mdm.service              I  🔧 首次WebSocket连接成功，发送初始化信息并执行初始化检查
2025-08-13 13:08:42.860  6603-6710  WebSocket               com.dspread.mdm.service              I  🔧 开始发送初始化信息（带流量控制）
2025-08-13 13:08:42.861  6603-6603  Common                  com.dspread.mdm.service              D  🔧 TimerConfig 使用调试模式终端信息上传间隔: 120秒
2025-08-13 13:08:42.869  6603-6710  WebSocket               com.dspread.mdm.service              I  🔧 C0901 被动式上送: first_connection (被动: 2)
2025-08-13 13:08:42.874  6603-6603  Common                  com.dspread.mdm.service              I  ✅ ⏰ 设置 C0109 定时器成功，下次执行: 120秒后 (2分钟)
2025-08-13 13:08:42.882  6603-6603  WebSocket               com.dspread.mdm.service              I  🔧 C0109 主动式上送: terminal_info_timer (主动: 4)
2025-08-13 13:08:42.888  6603-6710  Platform                com.dspread.mdm.service              D  🔧 使用缓存的应用信息
2025-08-13 13:08:42.899  6603-6603  Platform                com.dspread.mdm.service              D  🔧 重新收集应用信息（强制刷新）
2025-08-13 13:08:42.915  6603-6710  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0901","serialNo":"01354090202503050399","request_time":"1755061722895","request_id":"1755061722895C0901","version":"1","data":{"apkInfo":[{"packName":"com.dspread.mdm.service","apkName":"service","versionCode":11,"versionName":"1.0.08.20250813.DSPREAD.MDM.SERVICE","updateDate":"2025-08-13 13:07:44"}],"sytemInfo":{"androidVersion":"14","buildNumber":"BASE_D30M-MU_GEN_AP_V1.0.8.1_202507032000_ota_test","aspl":"2025-03-05"},"serviceInfo":[]},"myVersionName":"1.0.08.20250813.DSPREAD.MDM.SERVICE","terminalDate":"20250813130842"}
2025-08-13 13:08:42.922  6603-6710  WebSocket               com.dspread.mdm.service              I  🔧 C0901 应用信息上送成功 (1)
2025-08-13 13:08:43.034  6603-6603  AppManager              com.dspread.mdm.service              I  ℹ️ 应用信息获取完成: 总数130(系统129/用户1) 返回1个
2025-08-13 13:08:43.054  6603-6603  Platform                com.dspread.mdm.service              D  🔧 应用信息: 1 个应用
2025-08-13 13:08:43.196  6603-6603  Platform                com.dspread.mdm.service              D  🔧 内存总容量为： 1.86GB 
2025-08-13 13:08:43.208  6603-6603  Platform                com.dspread.mdm.service              D  🔧 内存使用量为： 0.79GB 
2025-08-13 13:08:43.317  6603-6603  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0109","serialNo":"01354090202503050399","request_time":"1755061723232","request_id":"1755061723232C0109","version":"1","data":{"apkInfo":[{"packName":"com.dspread.mdm.service","apkName":"service","versionCode":11,"versionName":"1.0.08.20250813.DSPREAD.MDM.SERVICE","updateDate":"2025-08-13 13:07:44"}],"locationInfo":{"gps":{"longitude":"-999.0","latitude":"-999.0","satelliteCount":0,"satelliteCountInUsed":0,"satelliteAverSNR":"0.00","distance":"0.0","lockStatus":"1","lockMeter":"200","proId":"","proName":""},"baseSite":{"MCC":"","MNC":"","CID":"-1","LAC":"-1","DBM":"-999","IMSI":"","ICCID":""},"wifi":{"SSID":"fubox_2.4G","IP":"*************","MAC":"8A:B9:BB:91:76:7E","SSTH":"-39","BSSID":"7c:10:c9:d6:9f:10"},"address":"无法获取"},"hardwareInfo":[{"name":"CPU Usage","desc":"cpu:0.50"},{"name":"storage_usage_status","desc":" 5.41GB \/ 32.00GB ","state":"1"},{"name":"total_memory","desc":" 0.79GB \/ 1.86GB ","state":"1"},{"name":"androidVersion","desc":"14","state":"1"},{"name":"network_status","desc":"wifi","state":"1"},{"name":"OSVer","desc":"14","state":"1"}],"wifiOption":[{"SSID":"360行车记录仪Z300-1203","SSTH":"-82"},{"SSID":"HZA50(74f652)","SSTH":"-35"},{"SSID":"MIFI-9727","SSTH":"-91"}],"sytemInfo":{"androidVersion":"14","buildNumber":"BASE_D30M-MU_GEN_AP_V1.0.8.1_202507032000_ota_test","aspl":"2025-03-05"}},"myVersionName":"1.0.08.20250813.DSPREAD.MDM.SERVICE","terminalDate":"20250813130843"}
2025-08-13 13:08:43.325  6603-6603  WebSocket               com.dspread.mdm.service              I  🔧 C0109 终端信息上传完成 (1, trigger=terminal_info_timer)
2025-08-13 13:08:43.332  6603-6603  Receiver                com.dspread.mdm.service              D  🔧 TerminalInfoEventHandler 终端信息上传完成
2025-08-13 13:08:43.339  6603-6603  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 处理器 TerminalInfoEventHandler 处理成功: com.dspread.mdm.service.TER_INFO_UPLOAD_BC
2025-08-13 13:08:43.933  6603-6710  WebSocket               com.dspread.mdm.service              I  🔧 C0902 被动式上送: first_connection (被动: 3)
2025-08-13 13:08:43.972  6603-6710  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0902","serialNo":"01354090202503050399","request_time":"1755061723955","request_id":"1755061723955C0902","version":"1","data":{"batteryLife":84,"batteryHealth":2,"temprature":"32.3","isCharging":"1","isLowBattery":"0","powerOff":"0","outOfBattery":"0"},"myVersionName":"1.0.08.20250813.DSPREAD.MDM.SERVICE","terminalDate":"20250813130843"}
2025-08-13 13:08:43.979  6603-6710  WebSocket               com.dspread.mdm.service              I  🔧 C0902 电池状态上送成功 (1, trigger=first_connection)
2025-08-13 13:08:44.987  6603-6710  WebSocket               com.dspread.mdm.service              I  🔧 C0903 被动式上送: first_connection (被动: 4)
2025-08-13 13:08:45.032  6603-6710  Platform                com.dspread.mdm.service              D  🔧 内存总容量为： 1.86GB 
2025-08-13 13:08:45.039  6603-6710  Platform                com.dspread.mdm.service              D  🔧 内存使用量为： 0.80GB 
2025-08-13 13:08:45.140  6603-6710  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0903","serialNo":"01354090202503050399","request_time":"1755061725102","request_id":"1755061725102C0903","version":"1","data":{"hardwareInfo":[{"name":"CPU Usage","desc":"cpu:0.50"},{"name":"storage_usage_status","desc":" 5.41GB \/ 32.00GB ","state":"1"},{"name":"total_memory","desc":" 0.80GB \/ 1.86GB ","state":"1"},{"name":"androidVersion","desc":"14","state":"1"},{"name":"network_status","desc":"wifi","state":"1"},{"name":"OSVer","desc":"14","state":"1"}],"locationInfo":{"gps":{"longitude":"-999.0","latitude":"-999.0","satelliteCount":0,"satelliteCountInUsed":0,"satelliteAverSNR":"0.00","distance":"0.0","lockStatus":"1","lockMeter":"200","proId":"","proName":""},"baseSite":{"MCC":"","MNC":"","CID":"-1","LAC":"-1","DBM":"-999","IMSI":"","ICCID":""},"wifi":{"SSID":"fubox_2.4G","IP":"*************","MAC":"8A:B9:BB:91:76:7E","SSTH":"-35","BSSID":"7c:10:c9:d6:9f:10"},"address":"无法获取"}},"myVersionName":"1.0.08.20250813.DSPREAD.MDM.SERVICE","terminalDate":"20250813130845"}
2025-08-13 13:08:45.147  6603-6710  WebSocket               com.dspread.mdm.service              I  🔧 C0903 数据信息上送成功 (1)
2025-08-13 13:08:46.155  6603-6710  WebSocket               com.dspread.mdm.service              I  🔧 C0904 被动式上送: first_connection (被动: 5)
2025-08-13 13:08:46.249  6603-6710  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0904","serialNo":"01354090202503050399","request_time":"1755061726221","request_id":"1755061726221C0904","version":"1","data":{"wifiOption":[{"SSID":"360行车记录仪Z300-1203","SSTH":"-82"},{"SSID":"HZA50(74f652)","SSTH":"-35"},{"SSID":"MIFI-9727","SSTH":"-91"}],"baseSite":{"MCC":"","MNC":"","CID":"-1","LAC":"-1","DBM":"-999","IMSI":"","ICCID":""},"wifi":{"SSID":"fubox_2.4G","IP":"*************","MAC":"8A:B9:BB:91:76:7E","SSTH":"-35","BSSID":"7c:10:c9:d6:9f:10"}},"myVersionName":"1.0.08.20250813.DSPREAD.MDM.SERVICE","terminalDate":"20250813130846"}
2025-08-13 13:08:46.256  6603-6710  WebSocket               com.dspread.mdm.service              I  🔧 C0904 网络状态上送成功 (1)
2025-08-13 13:08:47.265  6603-6710  WebSocket               com.dspread.mdm.service              I  🔧 C0906 被动式上送: first_connection (被动: 6)
2025-08-13 13:08:47.282  6603-6710  Platform                com.dspread.mdm.service              D  🔧 DspreadService 使用缓存的SP版本: V1.0.5
2025-08-13 13:08:47.290  6603-6710  Platform                com.dspread.mdm.service              D  🔧 SpVersionApi 通过DspreadService获取SP版本: V1.0.5
2025-08-13 13:08:47.321  6603-6710  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0906","serialNo":"01354090202503050399","request_time":"1755061727303","request_id":"1755061727303C0906","version":"1","data":{"firmWareInfo":{"spfw":"V1.0.5"},"imei_1":"864177060143618","imei_2":"","wifi_mac":"8a:b9:bb:91:76:7e","bt_mac":"","bsn":""},"myVersionName":"1.0.08.20250813.DSPREAD.MDM.SERVICE","terminalDate":"20250813130847"}
2025-08-13 13:08:47.329  6603-6710  WebSocket               com.dspread.mdm.service              I  🔧 C0906 设备信息上送成功 (1)
2025-08-13 13:08:47.337  6603-6710  Common                  com.dspread.mdm.service              I  ✅ 初始化信息发送完成
2025-08-13 13:08:47.344  6603-6710  WebSocket               com.dspread.mdm.service              I  🔧 检查服务更新结果
2025-08-13 13:08:47.351  6603-6710  Task                    com.dspread.mdm.service              W  ⚠️ 任务列表为空，跳过服务更新结果检查
2025-08-13 13:08:47.357  6603-6710  WebSocket               com.dspread.mdm.service              I  🔧 检查OS升级状态...
2025-08-13 13:08:47.365  6603-6710  Common                  com.dspread.mdm.service              D  🔧 OsUpdateStatusChecker WebSocket连接成功，检查OS升级状态
2025-08-13 13:08:47.372  6603-6710  Common                  com.dspread.mdm.service              D  🔧 OsUpdateStatusChecker 没有待检查的OS升级任务
2025-08-13 13:08:47.378  6603-6710  WebSocket               com.dspread.mdm.service              I  🔧 OS升级状态检查已触发
2025-08-13 13:08:47.385  6603-6710  WebSocket               com.dspread.mdm.service              I  🔧 首次连接初始化检查完成
2025-08-13 13:08:47.419  6603-6710  Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"request_time":"1755061721792","org_request_time":"1755061722618","org_request_id":"1755061722618C0109","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755061721792S0000","serialNo":"01354090202503050399"}
2025-08-13 13:08:47.427  6603-6710  WebSocket               com.dspread.mdm.service              I  🔧 收到业务消息 - tranCode: S0000
2025-08-13 13:08:47.433  6603-6710  WebSocket               com.dspread.mdm.service              I  🔧 解密后消息内容: {"request_time":"1755061721792","org_request_time":"1755061722618","org_request_id":"1755061722618C0109","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755061721792S0000","serial
2025-08-13 13:08:47.443  6603-6710  WebSocket               com.dspread.mdm.service              I  🔧 收到响应消息: transCode=S0000, orgRequestId=1755061722618C0109, state=0, remark=
2025-08-13 13:08:47.452  6603-6710  WebSocket               com.dspread.mdm.service              W  ⚠️ 未知的响应类型: C0109 for requestId: 1755061722618C0109
2025-08-13 13:08:47.492  6603-6710  Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"request_time":"1755061722442","org_request_time":"1755061723232","org_request_id":"1755061723232C0109","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755061722442S0000","serialNo":"01354090202503050399"}
2025-08-13 13:08:47.499  6603-6710  WebSocket               com.dspread.mdm.service              I  🔧 收到业务消息 - tranCode: S0000
2025-08-13 13:08:47.506  6603-6710  WebSocket               com.dspread.mdm.service              I  🔧 解密后消息内容: {"request_time":"1755061722442","org_request_time":"1755061723232","org_request_id":"1755061723232C0109","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755061722442S0000","serial
2025-08-13 13:08:47.515  6603-6710  WebSocket               com.dspread.mdm.service              I  🔧 收到响应消息: transCode=S0000, orgRequestId=1755061723232C0109, state=0, remark=
2025-08-13 13:08:47.521  6603-6710  WebSocket               com.dspread.mdm.service              W  ⚠️ 未知的响应类型: C0109 for requestId: 1755061723232C0109
2025-08-13 13:08:48.148  6603-6710  Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"request_time":"1755061726604","org_request_time":"1755061727303","org_request_id":"1755061727303C0906","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755061726604S0000","serialNo":"01354090202503050399"}
2025-08-13 13:08:48.156  6603-6710  WebSocket               com.dspread.mdm.service              I  🔧 收到业务消息 - tranCode: S0000
2025-08-13 13:08:48.163  6603-6710  WebSocket               com.dspread.mdm.service              I  🔧 解密后消息内容: {"request_time":"1755061726604","org_request_time":"1755061727303","org_request_id":"1755061727303C0906","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755061726604S0000","serial
2025-08-13 13:08:48.172  6603-6710  WebSocket               com.dspread.mdm.service              I  🔧 收到响应消息: transCode=S0000, orgRequestId=1755061727303C0906, state=0, remark=
2025-08-13 13:08:48.180  6603-6710  WebSocket               com.dspread.mdm.service              I  🔧 处理设备信息响应: state=0
2025-08-13 13:08:48.187  6603-6710  WebSocket               com.dspread.mdm.service              I  🔧 设备信息上传成功
2025-08-13 13:08:56.218  6603-6603  Receiver                com.dspread.mdm.service              D  🔧 网络恢复，重新上传网络状态信息（防抖通过）
2025-08-13 13:08:56.241  6603-6603  WebSocket               com.dspread.mdm.service              I  🔧 C0904 被动式上送拒绝: NETWORK_RECOVERY (拒绝: 1)
2025-08-13 13:08:56.250  6603-6603  WebSocket               com.dspread.mdm.service              I  🔧 C0904 网络状态上送被流量控制阻止: 被动事件 'NETWORK_RECOVERY' 在平衡模式 - 重要变化下未启用
