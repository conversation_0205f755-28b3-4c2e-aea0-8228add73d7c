2025-08-14 18:35:14.935 19136-19207 Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"msg_state":"P","request_time":"1755167713792","data":{"ruleList":[{"deleteAppList":[],"modifyDate":"2025-08-14 10:35:13","endDate":"9999-12-31 23:59:59","restartAfter":"0","installBy":"0","beginDate":"2024-08-14 10:35:13","appList":[{"apkMd5":"0d1225260d03e10a8ffc8409369c442a","apkName":"Via","appId":"353795033bc0410dac6dee5cb8702b06","apkSize":"2565110","appIconUrl":"https://siot-bucket01.s3.ap-northeast-1.amazonaws.com/icon/88cceb7e7cae4b8792791b5eb9d04c47ic_launcher.png","packName":"mark.via","versionName":"6.6.0","versionCode":"20250713","url":"https://siot-bucket01.s3.ap-northeast-1.amazonaws.com/app/4d256c4123b346fd817537b491342204.apk"},{"apkMd5":"54cc3a371939423d4288d63e1ac88019","apkName":"cl960","appId":"f32ac615171249418e3fc4b5bc2f4773","apkSize":"6155416","appIconUrl":"https://siot-bucket01.s3.ap-northeast-1.amazonaws.com/icon/c940c121d9c04565a85e36fb5665fac3ic_launcher.png","packName":"com.chileaf.cl960.sample","versionName":"1.1.0","versionCode":"1","url":"https://siot-bucket01.s3.ap-northeast-1.amazonaws.com/app/42e16b87eaa849bcaaf0f5fadcac6aab.apk"}],"serviceList":[],"action":"A","ruleName":"d60_test_298","ruleId":"6eb6f82614c74c3bb1f5ca0e32ef787e","createDate":"2025-08-14 10:35:13","orgRuleId":""}]},"tranCode":"ST005","request_id":"1755167713792ST005","version":"1","serialNo":"01354090202503050399"}
2025-08-14 18:35:14.947 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 准备发送响应确认 - requestId: 1755167713792ST005, needResponse: true
2025-08-14 18:35:14.972 19136-19207 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0000","serialNo":"01354090202503050399","request_time":"1755167714958","request_id":"1755167714958C0000","version":"1","org_request_id":"1755167713792ST005","org_request_time":"1755167713792","response_state":"0","myVersionName":"1.0.08.20250814.DSPREAD.MDM.SERVICE","terminalDate":"20250814183514"}
2025-08-14 18:35:14.997 19136-19207 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0000","serialNo":"01354090202503050399","request_time":"1755167714983","request_id":"1755167714983C0000","version":"1","org_request_id":"1755167713792ST005","org_request_time":"1755167713792","response_state":"0","myVersionName":"1.0.08.20250814.DSPREAD.MDM.SERVICE","terminalDate":"20250814183514"}
2025-08-14 18:35:15.004 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 已发送C0000响应确认: requestId=1755167713792ST005
2025-08-14 18:35:15.014 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleHandler 处理规则消息: ST005
2025-08-14 18:35:15.021 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleHandler 处理规则列表，数量: 1
2025-08-14 18:35:15.027 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleHandler 处理规则: ruleId=6eb6f82614c74c3bb1f5ca0e32ef787e, action=A
2025-08-14 18:35:15.034 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析规则JSON: ruleId=6eb6f82614c74c3bb1f5ca0e32ef787e
2025-08-14 18:35:15.049 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule appList数组: [{"apkMd5":"0d1225260d03e10a8ffc8409369c442a","apkName":"Via","appId":"353795033bc0410dac6dee5cb8702b06","apkSize":"2565110","appIconUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/icon\/88cceb7e7cae4b8792791b5eb9d04c47ic_launcher.png","packName":"mark.via","versionName":"6.6.0","versionCode":"20250713","url":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/4d256c4123b346fd817537b491342204.apk"},{"apkMd5":"54cc3a371939423d4288d63e1ac88019","apkName":"cl960","appId":"f32ac615171249418e3fc4b5bc2f4773","apkSize":"6155416","appIconUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/icon\/c940c121d9c04565a85e36fb5665fac3ic_launcher.png","packName":"com.chileaf.cl960.sample","versionName":"1.1.0","versionCode":"1","url":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/42e16b87eaa849bcaaf0f5fadcac6aab.apk"}]
2025-08-14 18:35:15.056 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule deleteAppList数组: []
2025-08-14 18:35:15.063 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=mark.via, apkName=Via
2025-08-14 18:35:15.070 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=com.chileaf.cl960.sample, apkName=cl960
2025-08-14 18:35:15.076 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后appList数量: 2
2025-08-14 18:35:15.083 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后deleteAppList数量: 0
2025-08-14 18:35:15.089 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleBaseStorage 处理规则: 6eb6f82614c74c3bb1f5ca0e32ef787e, 操作: A
2025-08-14 18:35:15.096 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 验证规则: ruleId=6eb6f82614c74c3bb1f5ca0e32ef787e, ruleType=app_management, action=A
2025-08-14 18:35:15.102 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule appList数量: 2, deleteAppList数量: 0
2025-08-14 18:35:15.124 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 验证应用管理规则: action=A
2025-08-14 18:35:15.131 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 验证应用列表: appList=2, deleteAppList=0
2025-08-14 18:35:15.139 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 应用列表验证通过
2025-08-14 18:35:15.157 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析规则JSON: ruleId=937eccc7e4ff4fc0bc9eaf2788685515
2025-08-14 18:35:15.167 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule appList数组: [{"packName":"mark.via","apkName":"Via","versionName":"6.6.0","versionCode":"20250713","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/4d256c4123b346fd817537b491342204.apk","apkMd5":"0d1225260d03e10a8ffc8409369c442a","apkSize":2565110,"installBy":"0","isSystemApp":false,"priority":0}]
2025-08-14 18:35:15.174 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule deleteAppList数组: []
2025-08-14 18:35:15.180 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=mark.via, apkName=Via
2025-08-14 18:35:15.187 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后appList数量: 1
2025-08-14 18:35:15.193 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后deleteAppList数量: 0
2025-08-14 18:35:15.200 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析规则JSON: ruleId=01f42138486940dd8ba8a136d84e239d
2025-08-14 18:35:15.206 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule appList数组: []
2025-08-14 18:35:15.215 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule deleteAppList数组: [{"packName":"mark.via","apkName":"Via","versionName":"6.6.0","versionCode":"20250713","apkUrl":"","apkMd5":"","apkSize":0,"installBy":"0","isSystemApp":false,"priority":0}]
2025-08-14 18:35:15.222 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=mark.via, apkName=Via
2025-08-14 18:35:15.228 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后appList数量: 0
2025-08-14 18:35:15.235 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后deleteAppList数量: 1
2025-08-14 18:35:15.242 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析规则JSON: ruleId=e195e52efa414316818a8d4b21fa4e9c
2025-08-14 18:35:15.248 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule appList数组: []
2025-08-14 18:35:15.260 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule deleteAppList数组: [{"packName":"com.chileaf.cl960.sample","apkName":"cl960","versionName":"1.1.0","versionCode":"1","apkUrl":"","apkMd5":"","apkSize":0,"installBy":"0","isSystemApp":false,"priority":0},{"packName":"plus.H52FFB9A5","apkName":"逍遥助手","versionName":"1.0","versionCode":"100","apkUrl":"","apkMd5":"","apkSize":0,"installBy":"0","isSystemApp":false,"priority":0}]
2025-08-14 18:35:15.267 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=com.chileaf.cl960.sample, apkName=cl960
2025-08-14 18:35:15.273 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=plus.H52FFB9A5, apkName=逍遥助手
2025-08-14 18:35:15.280 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后appList数量: 0
2025-08-14 18:35:15.286 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后deleteAppList数量: 2
2025-08-14 18:35:15.304 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析规则JSON: ruleId=937eccc7e4ff4fc0bc9eaf2788685515
2025-08-14 18:35:15.314 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule appList数组: [{"packName":"mark.via","apkName":"Via","versionName":"6.6.0","versionCode":"20250713","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/4d256c4123b346fd817537b491342204.apk","apkMd5":"0d1225260d03e10a8ffc8409369c442a","apkSize":2565110,"installBy":"0","isSystemApp":false,"priority":0}]
2025-08-14 18:35:15.321 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule deleteAppList数组: []
2025-08-14 18:35:15.327 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=mark.via, apkName=Via
2025-08-14 18:35:15.334 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后appList数量: 1
2025-08-14 18:35:15.340 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后deleteAppList数量: 0
2025-08-14 18:35:15.347 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析规则JSON: ruleId=01f42138486940dd8ba8a136d84e239d
2025-08-14 18:35:15.354 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule appList数组: []
2025-08-14 18:35:15.363 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule deleteAppList数组: [{"packName":"mark.via","apkName":"Via","versionName":"6.6.0","versionCode":"20250713","apkUrl":"","apkMd5":"","apkSize":0,"installBy":"0","isSystemApp":false,"priority":0}]
2025-08-14 18:35:15.369 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=mark.via, apkName=Via
2025-08-14 18:35:15.376 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后appList数量: 0
2025-08-14 18:35:15.382 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后deleteAppList数量: 1
2025-08-14 18:35:15.389 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析规则JSON: ruleId=e195e52efa414316818a8d4b21fa4e9c
2025-08-14 18:35:15.396 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule appList数组: []
2025-08-14 18:35:15.408 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule deleteAppList数组: [{"packName":"com.chileaf.cl960.sample","apkName":"cl960","versionName":"1.1.0","versionCode":"1","apkUrl":"","apkMd5":"","apkSize":0,"installBy":"0","isSystemApp":false,"priority":0},{"packName":"plus.H52FFB9A5","apkName":"逍遥助手","versionName":"1.0","versionCode":"100","apkUrl":"","apkMd5":"","apkSize":0,"installBy":"0","isSystemApp":false,"priority":0}]
2025-08-14 18:35:15.414 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=com.chileaf.cl960.sample, apkName=cl960
2025-08-14 18:35:15.421 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=plus.H52FFB9A5, apkName=逍遥助手
2025-08-14 18:35:15.527 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后appList数量: 0
2025-08-14 18:35:15.532 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后deleteAppList数量: 2
2025-08-14 18:35:15.576 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleBaseStorage 保存 4 个规则到存储
2025-08-14 18:35:15.582 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleBaseStorage Rule 6eb6f82614c74c3bb1f5ca0e32ef787e 添加成功
2025-08-14 18:35:15.586 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleBaseStorage 触发规则执行: 6eb6f82614c74c3bb1f5ca0e32ef787e
2025-08-14 18:35:15.592 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleHandler 规则处理成功: 6eb6f82614c74c3bb1f5ca0e32ef787e
2025-08-14 18:35:15.593 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 RuleBaseStorage 设备空闲，立即执行规则: 6eb6f82614c74c3bb1f5ca0e32ef787e
2025-08-14 18:35:15.598 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 RuleBaseManager 直接执行规则: 6eb6f82614c74c3bb1f5ca0e32ef787e
2025-08-14 18:35:15.598 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleHandler 规则列表处理完成: 处理完成 - 成功: 1, 失败: 0, 忽略: 0
2025-08-14 18:35:15.604 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 RuleBaseManager 处理规则: 6eb6f82614c74c3bb1f5ca0e32ef787e, 操作: A
2025-08-14 18:35:15.604 19136-19207 RuleBase                com.dspread.mdm.service              D  🔧 RuleHandler 上报规则处理结果: 0 - 处理完成 - 成功: 1, 失败: 0, 忽略: 0
2025-08-14 18:35:15.610 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 C0107 主动式上送: rulebase_update (主动: 19)
2025-08-14 18:35:15.611 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析规则JSON: ruleId=6eb6f82614c74c3bb1f5ca0e32ef787e
2025-08-14 18:35:15.623 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 Rule appList数组: [{"packName":"mark.via","apkName":"Via","versionName":"6.6.0","versionCode":"20250713","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/4d256c4123b346fd817537b491342204.apk","apkMd5":"0d1225260d03e10a8ffc8409369c442a","apkSize":2565110,"installBy":"0","isSystemApp":false,"priority":0},{"packName":"com.chileaf.cl960.sample","apkName":"cl960","versionName":"1.1.0","versionCode":"1","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/42e16b87eaa849bcaaf0f5fadcac6aab.apk","apkMd5":"54cc3a371939423d4288d63e1ac88019","apkSize":6155416,"installBy":"0","isSystemApp":false,"priority":0}]
2025-08-14 18:35:15.628 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 Rule deleteAppList数组: []
2025-08-14 18:35:15.629 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 添加规则结果: ruleId=batch_rules
2025-08-14 18:35:15.633 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=mark.via, apkName=Via
2025-08-14 18:35:15.638 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=com.chileaf.cl960.sample, apkName=cl960
2025-08-14 18:35:15.643 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后appList数量: 2
2025-08-14 18:35:15.644 19136-19207 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0107","serialNo":"01354090202503050399","request_time":"1755167715624","request_id":"1755167715624C0107","version":"1","data":{"ruleId":"batch_rules","taskResult":"0"},"myVersionName":"1.0.08.20250814.DSPREAD.MDM.SERVICE","terminalDate":"20250814183515","org_request_id":"1755167713792ST005","org_request_time":"1755167713792"}
2025-08-14 18:35:15.649 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后deleteAppList数量: 0
2025-08-14 18:35:15.649 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 规则结果实时上送: ruleId=batch_rules, result=0 (1)
2025-08-14 18:35:15.654 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 RuleBaseStorage 处理规则: 6eb6f82614c74c3bb1f5ca0e32ef787e, 操作: A
2025-08-14 18:35:15.659 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 Rule 验证规则: ruleId=6eb6f82614c74c3bb1f5ca0e32ef787e, ruleType=app_management, action=A
2025-08-14 18:35:15.664 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 Rule appList数量: 2, deleteAppList数量: 0
2025-08-14 18:35:15.686 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 Rule 验证应用管理规则: action=A
2025-08-14 18:35:15.691 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 Rule 验证应用列表: appList=2, deleteAppList=0
2025-08-14 18:35:15.697 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 Rule 应用列表验证通过
2025-08-14 18:35:15.718 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析规则JSON: ruleId=937eccc7e4ff4fc0bc9eaf2788685515
2025-08-14 18:35:15.727 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 Rule appList数组: [{"packName":"mark.via","apkName":"Via","versionName":"6.6.0","versionCode":"20250713","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/4d256c4123b346fd817537b491342204.apk","apkMd5":"0d1225260d03e10a8ffc8409369c442a","apkSize":2565110,"installBy":"0","isSystemApp":false,"priority":0}]
2025-08-14 18:35:15.732 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 Rule deleteAppList数组: []
2025-08-14 18:35:15.738 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=mark.via, apkName=Via
2025-08-14 18:35:15.743 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后appList数量: 1
2025-08-14 18:35:15.748 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后deleteAppList数量: 0
2025-08-14 18:35:15.753 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析规则JSON: ruleId=01f42138486940dd8ba8a136d84e239d
2025-08-14 18:35:15.758 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 Rule appList数组: []
2025-08-14 18:35:15.765 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 Rule deleteAppList数组: [{"packName":"mark.via","apkName":"Via","versionName":"6.6.0","versionCode":"20250713","apkUrl":"","apkMd5":"","apkSize":0,"installBy":"0","isSystemApp":false,"priority":0}]
2025-08-14 18:35:15.771 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=mark.via, apkName=Via
2025-08-14 18:35:15.776 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后appList数量: 0
2025-08-14 18:35:15.781 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后deleteAppList数量: 1
2025-08-14 18:35:15.786 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析规则JSON: ruleId=e195e52efa414316818a8d4b21fa4e9c
2025-08-14 18:35:15.791 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 Rule appList数组: []
2025-08-14 18:35:15.806 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 Rule deleteAppList数组: [{"packName":"com.chileaf.cl960.sample","apkName":"cl960","versionName":"1.1.0","versionCode":"1","apkUrl":"","apkMd5":"","apkSize":0,"installBy":"0","isSystemApp":false,"priority":0},{"packName":"plus.H52FFB9A5","apkName":"逍遥助手","versionName":"1.0","versionCode":"100","apkUrl":"","apkMd5":"","apkSize":0,"installBy":"0","isSystemApp":false,"priority":0}]
2025-08-14 18:35:15.811 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=com.chileaf.cl960.sample, apkName=cl960
2025-08-14 18:35:15.817 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=plus.H52FFB9A5, apkName=逍遥助手
2025-08-14 18:35:15.820 19136-19207 Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"request_time":"1755167714774","org_request_time":"1755167715624","org_request_id":"1755167715624C0107","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755167714774S0000","serialNo":"01354090202503050399"}
2025-08-14 18:35:15.822 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后appList数量: 0
2025-08-14 18:35:15.827 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后deleteAppList数量: 2
2025-08-14 18:35:15.829 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 收到响应消息: transCode=S0000, orgRequestId=1755167715624C0107, state=0, remark=
2025-08-14 18:35:15.832 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析规则JSON: ruleId=6eb6f82614c74c3bb1f5ca0e32ef787e
2025-08-14 18:35:15.833 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 处理规则结果响应: state=0
2025-08-14 18:35:15.838 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 规则结果上传成功
2025-08-14 18:35:15.845 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 Rule appList数组: [{"packName":"mark.via","apkName":"Via","versionName":"6.6.0","versionCode":"20250713","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/4d256c4123b346fd817537b491342204.apk","apkMd5":"0d1225260d03e10a8ffc8409369c442a","apkSize":2565110,"installBy":"0","isSystemApp":false,"priority":0},{"packName":"com.chileaf.cl960.sample","apkName":"cl960","versionName":"1.1.0","versionCode":"1","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/42e16b87eaa849bcaaf0f5fadcac6aab.apk","apkMd5":"54cc3a371939423d4288d63e1ac88019","apkSize":6155416,"installBy":"0","isSystemApp":false,"priority":0}]
2025-08-14 18:35:15.851 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 Rule deleteAppList数组: []
2025-08-14 18:35:15.857 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=mark.via, apkName=Via
2025-08-14 18:35:15.864 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=com.chileaf.cl960.sample, apkName=cl960
2025-08-14 18:35:15.870 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后appList数量: 2
2025-08-14 18:35:15.875 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后deleteAppList数量: 0
2025-08-14 18:35:15.880 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 RuleBaseStorage Rule 6eb6f82614c74c3bb1f5ca0e32ef787e 已存在，忽略Add操作
2025-08-14 18:35:15.886 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 RuleStateMachine 规则初始化成功: 6eb6f82614c74c3bb1f5ca0e32ef787e -> RuleState(code=todo, description=等待执行)
2025-08-14 18:35:15.891 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 RuleStateMachine 规则已存在: 6eb6f82614c74c3bb1f5ca0e32ef787e, 当前状态: RuleState(code=todo, description=等待执行)
2025-08-14 18:35:15.896 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 创建应用状态: packName=mark.via, apkName=Via
2025-08-14 18:35:15.902 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 创建应用状态: packName=com.chileaf.cl960.sample, apkName=cl960
2025-08-14 18:35:15.907 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 初始化规则应用状态: 6eb6f82614c74c3bb1f5ca0e32ef787e, 应用数量: 2
2025-08-14 18:35:15.913 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 RuleStateMonitor 收到状态变化: 6eb6f82614c74c3bb1f5ca0e32ef787e, todo -> R01
2025-08-14 18:35:15.918 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 RuleStateMonitor 状态变化已记录: 6eb6f82614c74c3bb1f5ca0e32ef787e, R01
2025-08-14 18:35:15.923 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 RuleStateMachine 状态转换成功: 6eb6f82614c74c3bb1f5ca0e32ef787e, todo -> R01
2025-08-14 18:35:15.928 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 RuleStateMonitor 收到状态变化: 6eb6f82614c74c3bb1f5ca0e32ef787e, R01 -> R02
2025-08-14 18:35:15.933 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 RuleStateMonitor 状态变化已记录: 6eb6f82614c74c3bb1f5ca0e32ef787e, R02
2025-08-14 18:35:15.939 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 RuleStateMachine 状态转换成功: 6eb6f82614c74c3bb1f5ca0e32ef787e, R01 -> R02
2025-08-14 18:35:15.944 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 开始执行规则: 6eb6f82614c74c3bb1f5ca0e32ef787e
2025-08-14 18:35:15.949 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 执行安装应用，数量: 2
2025-08-14 18:35:15.954 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 开始安装应用，数量: 2
2025-08-14 18:35:15.959 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 安装应用: mark.via, URL: https://siot-bucket01.s3.ap-northeast-1.amazonaws.com/app/4d256c4123b346fd817537b491342204.apk
2025-08-14 18:35:15.964 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 检查应用安装状态: mark.via
2025-08-14 18:35:15.970 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 应用是否已安装: false
2025-08-14 18:35:15.975 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 应用未安装，需要安装
2025-08-14 18:35:15.981 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 更新应用状态: 6eb6f82614c74c3bb1f5ca0e32ef787e, mark.via -> A01
2025-08-14 18:35:15.986 19136-19206 WebSocket               com.dspread.mdm.service              I  🔧 C0107 主动式上送: rulebase_update (主动: 20)
2025-08-14 18:35:16.005 19136-19206 WebSocket               com.dspread.mdm.service              I  🔧 添加规则结果: ruleId=6eb6f82614c74c3bb1f5ca0e32ef787e
2025-08-14 18:35:16.034 19136-19206 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0107","serialNo":"01354090202503050399","request_time":"1755167716000","request_id":"1755167716000C0107","version":"1","data":{"ruleId":"6eb6f82614c74c3bb1f5ca0e32ef787e","taskResult":"R02","failedApkList":[{"packName":"mark.via","apkName":"Via","versionName":"6.6.0","versionCode":"20250713","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/4d256c4123b346fd817537b491342204.apk","apkMd5":"0d1225260d03e10a8ffc8409369c442a","apkSize":2565110,"installBy":"0","isSystemApp":false,"priority":0,"result":"A01","taskType":"01"},{"packName":"com.chileaf.cl960.sample","apkName":"cl960","versionName":"1.1.0","versionCode":"1","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/42e16b87eaa849bcaaf0f5fadcac6aab.apk","apkMd5":"54cc3a371939423d4288d63e1ac88019","apkSize":6155416,"installBy":"0","isSystemApp":false,"priority":0,"result":"W01","taskType":"01"}]},"myVersionName":"1.0.08.20250814.DSPREAD.MDM.SERVICE","terminalDate":"20250814183516"}
2025-08-14 18:35:16.040 19136-19206 WebSocket               com.dspread.mdm.service              I  🔧 规则结果实时上送: ruleId=6eb6f82614c74c3bb1f5ca0e32ef787e, result=R02 (1)
2025-08-14 18:35:16.045 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 上报规则执行中状态: 6eb6f82614c74c3bb1f5ca0e32ef787e, 应用数量: 2
2025-08-14 18:35:16.051 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 安装应用: com.chileaf.cl960.sample, URL: https://siot-bucket01.s3.ap-northeast-1.amazonaws.com/app/42e16b87eaa849bcaaf0f5fadcac6aab.apk
2025-08-14 18:35:16.052 19136-19363 HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 尝试下载，第1次
2025-08-14 18:35:16.056 19136-19363 HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 开始下载
2025-08-14 18:35:16.057 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 检查应用安装状态: com.chileaf.cl960.sample
2025-08-14 18:35:16.062 19136-19363 HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader URL: https://siot-bucket01.s3.ap-northeast-1.amazonaws.com/app/4d256c4123b346fd817537b491342204.apk
2025-08-14 18:35:16.063 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 应用是否已安装: false
2025-08-14 18:35:16.068 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 应用未安装，需要安装
2025-08-14 18:35:16.068 19136-19363 HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 路径: /data/user/0/com.dspread.mdm.service/files/downloads/rule_6eb6f82614c74c3bb1f5ca0e32ef787e_mark.via.apk
2025-08-14 18:35:16.073 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 更新应用状态: 6eb6f82614c74c3bb1f5ca0e32ef787e, com.chileaf.cl960.sample -> A01
2025-08-14 18:35:16.075 19136-19363 HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 本地文件不存在，创建文件
2025-08-14 18:35:16.079 19136-19206 WebSocket               com.dspread.mdm.service              I  🔧 C0107 主动式上送: rulebase_update (主动: 21)
2025-08-14 18:35:16.080 19136-19363 HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader allFileLength=2565110, 需要从服务器获取文件大小
2025-08-14 18:35:16.098 19136-19206 WebSocket               com.dspread.mdm.service              I  🔧 添加规则结果: ruleId=6eb6f82614c74c3bb1f5ca0e32ef787e
2025-08-14 18:35:16.128 19136-19206 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0107","serialNo":"01354090202503050399","request_time":"1755167716093","request_id":"1755167716093C0107","version":"1","data":{"ruleId":"6eb6f82614c74c3bb1f5ca0e32ef787e","taskResult":"R02","failedApkList":[{"packName":"mark.via","apkName":"Via","versionName":"6.6.0","versionCode":"20250713","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/4d256c4123b346fd817537b491342204.apk","apkMd5":"0d1225260d03e10a8ffc8409369c442a","apkSize":2565110,"installBy":"0","isSystemApp":false,"priority":0,"result":"A01","taskType":"01"},{"packName":"com.chileaf.cl960.sample","apkName":"cl960","versionName":"1.1.0","versionCode":"1","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/42e16b87eaa849bcaaf0f5fadcac6aab.apk","apkMd5":"54cc3a371939423d4288d63e1ac88019","apkSize":6155416,"installBy":"0","isSystemApp":false,"priority":0,"result":"A01","taskType":"01"}]},"myVersionName":"1.0.08.20250814.DSPREAD.MDM.SERVICE","terminalDate":"20250814183516"}
2025-08-14 18:35:16.134 19136-19206 WebSocket               com.dspread.mdm.service              I  🔧 规则结果实时上送: ruleId=6eb6f82614c74c3bb1f5ca0e32ef787e, result=R02 (1)
2025-08-14 18:35:16.139 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 上报规则执行中状态: 6eb6f82614c74c3bb1f5ca0e32ef787e, 应用数量: 2
2025-08-14 18:35:16.145 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 规则执行启动成功: 6eb6f82614c74c3bb1f5ca0e32ef787e
2025-08-14 18:35:16.145 19136-19366 HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 尝试下载，第1次
2025-08-14 18:35:16.150 19136-19206 RuleBase                com.dspread.mdm.service              D  🔧 RuleBaseManager 规则添加成功: 6eb6f82614c74c3bb1f5ca0e32ef787e
2025-08-14 18:35:16.150 19136-19366 HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 开始下载
2025-08-14 18:35:16.156 19136-19366 HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader URL: https://siot-bucket01.s3.ap-northeast-1.amazonaws.com/app/42e16b87eaa849bcaaf0f5fadcac6aab.apk
2025-08-14 18:35:16.161 19136-19366 HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 路径: /data/user/0/com.dspread.mdm.service/files/downloads/rule_6eb6f82614c74c3bb1f5ca0e32ef787e_com.chileaf.cl960.sample.apk
2025-08-14 18:35:16.167 19136-19366 HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 本地文件不存在，创建文件
2025-08-14 18:35:16.173 19136-19366 HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader allFileLength=6155416, 需要从服务器获取文件大小
2025-08-14 18:35:16.190 19136-19363 TrafficStats            com.dspread.mdm.service              D  tagSocket(98) with statsTag=0xffffffff, statsUid=-1
2025-08-14 18:35:16.190 19136-19366 TrafficStats            com.dspread.mdm.service              D  tagSocket(102) with statsTag=0xffffffff, statsUid=-1
2025-08-14 18:35:16.286 19136-19207 Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"request_time":"1755167715248","org_request_time":"1755167716093","org_request_id":"1755167716093C0107","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755167715248S0000","serialNo":"01354090202503050399"}
2025-08-14 18:35:16.295 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 收到响应消息: transCode=S0000, orgRequestId=1755167716093C0107, state=0, remark=
2025-08-14 18:35:16.300 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 处理规则结果响应: state=0
2025-08-14 18:35:16.305 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 规则结果上传成功
2025-08-14 18:35:16.336 19136-19207 Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"request_time":"1755167715155","org_request_time":"1755167716000","org_request_id":"1755167716000C0107","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755167715155S0000","serialNo":"01354090202503050399"}
2025-08-14 18:35:16.344 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 收到响应消息: transCode=S0000, orgRequestId=1755167716000C0107, state=0, remark=
2025-08-14 18:35:16.350 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 处理规则结果响应: state=0
2025-08-14 18:35:16.355 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 规则结果上传成功
2025-08-14 18:35:16.754 19136-19366 HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader HTTP响应码: 200
2025-08-14 18:35:16.763 19136-19366 HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 服务器返回Content-Length: 6155416
2025-08-14 18:35:16.766 19136-19363 HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader HTTP响应码: 200
2025-08-14 18:35:16.769 19136-19366 HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader localFile = true /data/user/0/com.dspread.mdm.service/files/downloads/rule_6eb6f82614c74c3bb1f5ca0e32ef787e_com.chileaf.cl960.sample.apk
2025-08-14 18:35:16.773 19136-19363 HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 服务器返回Content-Length: 2565110
2025-08-14 18:35:16.778 19136-19366 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 下载进度: com.chileaf.cl960.sample - 0%
2025-08-14 18:35:16.779 19136-19363 HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader localFile = true /data/user/0/com.dspread.mdm.service/files/downloads/rule_6eb6f82614c74c3bb1f5ca0e32ef787e_mark.via.apk
2025-08-14 18:35:16.789 19136-19363 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 下载进度: mark.via - 0%
2025-08-14 18:35:17.519 19136-19363 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 下载进度: mark.via - 10%
2025-08-14 18:35:18.184 19136-19363 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 下载进度: mark.via - 20%
2025-08-14 18:35:18.302 19136-19366 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 下载进度: com.chileaf.cl960.sample - 10%
2025-08-14 18:35:18.794 19136-19363 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 下载进度: mark.via - 30%
2025-08-14 18:35:19.438 19136-19366 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 下载进度: com.chileaf.cl960.sample - 20%
2025-08-14 18:35:19.503 19136-19363 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 下载进度: mark.via - 40%
2025-08-14 18:35:20.260 19136-19363 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 下载进度: mark.via - 50%
2025-08-14 18:35:20.514 19136-19366 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 下载进度: com.chileaf.cl960.sample - 30%
2025-08-14 18:35:20.965 19136-19363 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 下载进度: mark.via - 60%
2025-08-14 18:35:21.658 19136-19363 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 下载进度: mark.via - 70%
2025-08-14 18:35:21.669 19136-19366 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 下载进度: com.chileaf.cl960.sample - 40%
2025-08-14 18:35:22.292 19136-19363 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 下载进度: mark.via - 80%
2025-08-14 18:35:22.812 19136-19366 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 下载进度: com.chileaf.cl960.sample - 50%
2025-08-14 18:35:22.835 19136-19363 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 下载进度: mark.via - 90%
2025-08-14 18:35:23.449 19136-19363 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 下载进度: mark.via - 100%
2025-08-14 18:35:23.459 19136-19363 HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 计算MD5: rule_6eb6f82614c74c3bb1f5ca0e32ef787e_mark.via.apk
2025-08-14 18:35:23.512 19136-19363 HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 文件: 2565110 MD5: 0d1225260d03e10a8ffc8409369c442a
2025-08-14 18:35:23.518 19136-19363 HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader MD5校验正确
2025-08-14 18:35:23.523 19136-19363 HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 清理连接资源
2025-08-14 18:35:23.530 19136-19363 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine APK下载成功: /data/user/0/com.dspread.mdm.service/files/downloads/rule_6eb6f82614c74c3bb1f5ca0e32ef787e_mark.via.apk
2025-08-14 18:35:23.536 19136-19363 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 更新应用状态: 6eb6f82614c74c3bb1f5ca0e32ef787e, mark.via -> A03
2025-08-14 18:35:23.542 19136-19363 WebSocket               com.dspread.mdm.service              I  🔧 C0107 主动式上送: rulebase_update (主动: 22)
2025-08-14 18:35:23.563 19136-19363 WebSocket               com.dspread.mdm.service              I  🔧 添加规则结果: ruleId=6eb6f82614c74c3bb1f5ca0e32ef787e
2025-08-14 18:35:23.597 19136-19363 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0107","serialNo":"01354090202503050399","request_time":"1755167723557","request_id":"1755167723557C0107","version":"1","data":{"ruleId":"6eb6f82614c74c3bb1f5ca0e32ef787e","taskResult":"R02","failedApkList":[{"packName":"mark.via","apkName":"Via","versionName":"6.6.0","versionCode":"20250713","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/4d256c4123b346fd817537b491342204.apk","apkMd5":"0d1225260d03e10a8ffc8409369c442a","apkSize":2565110,"installBy":"0","isSystemApp":false,"priority":0,"result":"A03","taskType":"01"},{"packName":"com.chileaf.cl960.sample","apkName":"cl960","versionName":"1.1.0","versionCode":"1","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/42e16b87eaa849bcaaf0f5fadcac6aab.apk","apkMd5":"54cc3a371939423d4288d63e1ac88019","apkSize":6155416,"installBy":"0","isSystemApp":false,"priority":0,"result":"A01","taskType":"01","errorMsg":"下载中: 50%"}]},"myVersionName":"1.0.08.20250814.DSPREAD.MDM.SERVICE","terminalDate":"20250814183523"}
2025-08-14 18:35:23.602 19136-19363 WebSocket               com.dspread.mdm.service              I  🔧 规则结果实时上送: ruleId=6eb6f82614c74c3bb1f5ca0e32ef787e, result=R02 (1)
2025-08-14 18:35:23.608 19136-19363 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 上报规则执行中状态: 6eb6f82614c74c3bb1f5ca0e32ef787e, 应用数量: 2
2025-08-14 18:35:23.614 19136-19363 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 开始安装APK: /data/user/0/com.dspread.mdm.service/files/downloads/rule_6eb6f82614c74c3bb1f5ca0e32ef787e_mark.via.apk
2025-08-14 18:35:23.619 19136-19363 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 更新应用状态: 6eb6f82614c74c3bb1f5ca0e32ef787e, mark.via -> B02
2025-08-14 18:35:23.625 19136-19363 WebSocket               com.dspread.mdm.service              I  🔧 C0107 主动式上送: rulebase_update (主动: 23)
2025-08-14 18:35:23.645 19136-19363 WebSocket               com.dspread.mdm.service              I  🔧 添加规则结果: ruleId=6eb6f82614c74c3bb1f5ca0e32ef787e
2025-08-14 18:35:23.681 19136-19363 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0107","serialNo":"01354090202503050399","request_time":"1755167723639","request_id":"1755167723639C0107","version":"1","data":{"ruleId":"6eb6f82614c74c3bb1f5ca0e32ef787e","taskResult":"R02","failedApkList":[{"packName":"mark.via","apkName":"Via","versionName":"6.6.0","versionCode":"20250713","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/4d256c4123b346fd817537b491342204.apk","apkMd5":"0d1225260d03e10a8ffc8409369c442a","apkSize":2565110,"installBy":"0","isSystemApp":false,"priority":0,"result":"B02","taskType":"01"},{"packName":"com.chileaf.cl960.sample","apkName":"cl960","versionName":"1.1.0","versionCode":"1","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/42e16b87eaa849bcaaf0f5fadcac6aab.apk","apkMd5":"54cc3a371939423d4288d63e1ac88019","apkSize":6155416,"installBy":"0","isSystemApp":false,"priority":0,"result":"A01","taskType":"01","errorMsg":"下载中: 50%"}]},"myVersionName":"1.0.08.20250814.DSPREAD.MDM.SERVICE","terminalDate":"20250814183523"}
2025-08-14 18:35:23.687 19136-19363 WebSocket               com.dspread.mdm.service              I  🔧 规则结果实时上送: ruleId=6eb6f82614c74c3bb1f5ca0e32ef787e, result=R02 (1)
2025-08-14 18:35:23.692 19136-19363 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 上报规则执行中状态: 6eb6f82614c74c3bb1f5ca0e32ef787e, 应用数量: 2
2025-08-14 18:35:23.697 19136-19207 Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"request_time":"1755167722713","org_request_time":"1755167723557","org_request_id":"1755167723557C0107","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755167722713S0000","serialNo":"01354090202503050399"}
2025-08-14 18:35:23.698 19136-19363 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 注册安装回调: mark.via (规则ID: 6eb6f82614c74c3bb1f5ca0e32ef787e)
2025-08-14 18:35:23.706 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 收到响应消息: transCode=S0000, orgRequestId=1755167723557C0107, state=0, remark=
2025-08-14 18:35:23.712 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 处理规则结果响应: state=0
2025-08-14 18:35:23.717 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 规则结果上传成功
2025-08-14 18:35:23.718 19136-19363 PackageParser           com.dspread.mdm.service              W  Unknown element under <manifest>: queries at /data/user/0/com.dspread.mdm.service/files/downloads/rule_6eb6f82614c74c3bb1f5ca0e32ef787e_mark.via.apk Binary XML file line #65
2025-08-14 18:35:23.741 19136-19363 AppManager              com.dspread.mdm.service              I  ℹ️ 准备安装: mark.via
2025-08-14 18:35:23.763 19136-19363 PackageParser           com.dspread.mdm.service              W  Unknown element under <manifest>: queries at /data/user/0/com.dspread.mdm.service/files/downloads/rule_6eb6f82614c74c3bb1f5ca0e32ef787e_mark.via.apk Binary XML file line #65
2025-08-14 18:35:23.789 19136-19363 AppManager              com.dspread.mdm.service              I  ℹ️ APK验证通过: mark.via v6.6.0(20250713) 2504KB
2025-08-14 18:35:23.798 19136-19363 AppManager              com.dspread.mdm.service              I  ℹ️ 创建Session: sessionId=602653410
2025-08-14 18:35:23.823 19136-19207 Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"request_time":"1755167722796","org_request_time":"1755167723639","org_request_id":"1755167723639C0107","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755167722796S0000","serialNo":"01354090202503050399"}
2025-08-14 18:35:23.832 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 收到响应消息: transCode=S0000, orgRequestId=1755167723639C0107, state=0, remark=
2025-08-14 18:35:23.837 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 处理规则结果响应: state=0
2025-08-14 18:35:23.843 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 规则结果上传成功
2025-08-14 18:35:23.861 19136-19366 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 下载进度: com.chileaf.cl960.sample - 60%
2025-08-14 18:35:23.929 19136-19363 AppManager              com.dspread.mdm.service              I  ℹ️ PackageInstaller installation committed: sessionId=602653410
2025-08-14 18:35:23.944  1235-1305  PackageInstallerSession system_server                        E  com.dspread.mdm.service drops manifest attribute android:installLocation in base.apk for mark.via
2025-08-14 18:35:23.951 19136-19136 AppManager              com.dspread.mdm.service              I  ℹ️ 安装进度: 90%
2025-08-14 18:35:24.715 19136-19366 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 下载进度: com.chileaf.cl960.sample - 70%
2025-08-14 18:35:24.794 19136-19206 Common                  com.dspread.mdm.service              D  🔧 ServiceGuardEventHandler 开始执行保活动作
2025-08-14 18:35:24.805 19136-19206 Common                  com.dspread.mdm.service              I  ✅ ⏰ 设置服务守护定时器成功，下次执行: 120秒后 (2分钟)
2025-08-14 18:35:24.812 19136-19206 ContextImpl             com.dspread.mdm.service              W  Calling a method in the system process without a qualified user: android.app.ContextImpl.startService:1899 android.content.ContextWrapper.startService:825 com.dspread.mdm.service.broadcast.handlers.service.ServiceGuardEventHandler.startKeepAliveService:211 com.dspread.mdm.service.broadcast.handlers.service.ServiceGuardEventHandler.performKeepAliveActions:193 com.dspread.mdm.service.broadcast.handlers.service.ServiceGuardEventHandler.access$performKeepAliveActions:24 
2025-08-14 18:35:24.820 19136-19206 Common                  com.dspread.mdm.service              D  🔧 ServiceGuardEventHandler 启动保活服务
2025-08-14 18:35:24.821 19136-19136 Service                 com.dspread.mdm.service              D  🔧 ServiceKeepAliveService 保活服务启动
2025-08-14 18:35:24.829 19136-19206 Common                  com.dspread.mdm.service              D  🔧 ServiceGuardEventHandler 保活动作执行完成
2025-08-14 18:35:24.837 19136-19136 Platform                com.dspread.mdm.service              D  🔧 WakeLockManager WakeLock不存在或未持有: ServiceKeepAlive
2025-08-14 18:35:24.862 19136-19136 Platform                com.dspread.mdm.service              D  🔧 WakeLockManager 获取WakeLock成功: ServiceKeepAlive (超时: 30000ms)
2025-08-14 18:35:25.382 19136-19136 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 收到广播: android.intent.action.PACKAGE_ADDED
2025-08-14 18:35:25.400 19136-19136 Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 收到包更新广播: android.intent.action.PACKAGE_ADDED, 包名: mark.via
2025-08-14 18:35:25.406 19136-19136 Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 应用安装: mark.via
2025-08-14 18:35:25.413 19136-19136 Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 检查WebSocket状态: packageName=mark.via, action=INSTALL
2025-08-14 18:35:25.420 19136-19136 Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 通知rulebase处理应用安装: mark.via
2025-08-14 18:35:25.429 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 收到系统广播，应用已安装: mark.via
2025-08-14 18:35:25.437 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 找到匹配的安装回调，触发: mark.via
2025-08-14 18:35:25.447 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 安装回调被触发: pkg=mark.via, returnCode=1, error=
2025-08-14 18:35:25.453 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 应用安装启动成功: mark.via
2025-08-14 18:35:25.460 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 从广播更新应用状态: 6eb6f82614c74c3bb1f5ca0e32ef787e, mark.via -> B03
2025-08-14 18:35:25.521 19136-19136 WebSocket               com.dspread.mdm.service              I  🔧 C0107 主动式上送: rulebase_update (主动: 24)
2025-08-14 18:35:25.623 19136-19136 WebSocket               com.dspread.mdm.service              I  🔧 添加规则结果: ruleId=6eb6f82614c74c3bb1f5ca0e32ef787e
2025-08-14 18:35:25.667 19136-19136 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0107","serialNo":"01354090202503050399","request_time":"1755167725593","request_id":"1755167725593C0107","version":"1","data":{"ruleId":"6eb6f82614c74c3bb1f5ca0e32ef787e","taskResult":"R02","failedApkList":[{"packName":"mark.via","apkName":"Via","versionName":"6.6.0","versionCode":"20250713","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/4d256c4123b346fd817537b491342204.apk","apkMd5":"0d1225260d03e10a8ffc8409369c442a","apkSize":2565110,"installBy":"0","isSystemApp":false,"priority":0,"result":"B03","taskType":"01"},{"packName":"com.chileaf.cl960.sample","apkName":"cl960","versionName":"1.1.0","versionCode":"1","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/42e16b87eaa849bcaaf0f5fadcac6aab.apk","apkMd5":"54cc3a371939423d4288d63e1ac88019","apkSize":6155416,"installBy":"0","isSystemApp":false,"priority":0,"result":"A01","taskType":"01","errorMsg":"下载中: 70%"}]},"myVersionName":"1.0.08.20250814.DSPREAD.MDM.SERVICE","terminalDate":"20250814183525"}
2025-08-14 18:35:25.674 19136-19136 WebSocket               com.dspread.mdm.service              I  🔧 规则结果实时上送: ruleId=6eb6f82614c74c3bb1f5ca0e32ef787e, result=R02 (1)
2025-08-14 18:35:25.680 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 上报规则执行中状态: 6eb6f82614c74c3bb1f5ca0e32ef787e, 应用数量: 2
2025-08-14 18:35:25.687 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 开始检查规则完成状态: 6eb6f82614c74c3bb1f5ca0e32ef787e, 总应用数: 2
2025-08-14 18:35:25.693 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 检查应用状态: mark.via -> B03
2025-08-14 18:35:25.694 19136-19366 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 下载进度: com.chileaf.cl960.sample - 80%
2025-08-14 18:35:25.699 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 发现成功状态: mark.via -> B03
2025-08-14 18:35:25.705 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 检查应用状态: com.chileaf.cl960.sample -> A01
2025-08-14 18:35:25.712 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 发现未完成状态: com.chileaf.cl960.sample -> A01
2025-08-14 18:35:25.723 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 规则完成检查结果: 6eb6f82614c74c3bb1f5ca0e32ef787e, 全部完成: false, 完成数: 1/2, 有失败: false
2025-08-14 18:35:25.730 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 规则未完成: 6eb6f82614c74c3bb1f5ca0e32ef787e, 完成数: 1/2
2025-08-14 18:35:25.738 19136-19136 Platform                com.dspread.mdm.service              D  🔧 重新收集应用信息（强制刷新）
2025-08-14 18:35:25.832 19136-19207 Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"request_time":"1755167724783","org_request_time":"1755167725593","org_request_id":"1755167725593C0107","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755167724783S0000","serialNo":"01354090202503050399"}
2025-08-14 18:35:25.843 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 收到响应消息: transCode=S0000, orgRequestId=1755167725593C0107, state=0, remark=
2025-08-14 18:35:25.850 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 处理规则结果响应: state=0
2025-08-14 18:35:25.856 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 规则结果上传成功
2025-08-14 18:35:25.856 19136-19136 AppManager              com.dspread.mdm.service              I  ℹ️ 应用信息获取完成: 总数131(系统129/用户2) 返回2个
2025-08-14 18:35:25.880 19136-19136 Platform                com.dspread.mdm.service              D  🔧 应用信息: 2 个应用
2025-08-14 18:35:25.889 19136-19136 Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 应用信息缓存刷新完成
2025-08-14 18:35:25.899 19136-19136 ContextImpl             com.dspread.mdm.service              W  Calling a method in the system process without a qualified user: android.app.ContextImpl.sendBroadcast:1220 android.content.ContextWrapper.sendBroadcast:504 com.dspread.mdm.service.broadcast.core.BroadcastSender.sendBroadcast:99 com.dspread.mdm.service.broadcast.core.BroadcastSender.sendBroadcast:23 com.dspread.mdm.service.broadcast.handlers.system.PackageUpdateEventHandlerImpl.sendAppRefreshBroadcast:491 
2025-08-14 18:35:25.902 19136-19136 Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 发送应用刷新广播
2025-08-14 18:35:26.479 19136-19366 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 下载进度: com.chileaf.cl960.sample - 90%
2025-08-14 18:35:27.208 19136-19366 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 下载进度: com.chileaf.cl960.sample - 100%
2025-08-14 18:35:27.219 19136-19366 HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 计算MD5: rule_6eb6f82614c74c3bb1f5ca0e32ef787e_com.chileaf.cl960.sample.apk
2025-08-14 18:35:27.306 19136-19366 HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 文件: 6155416 MD5: 54cc3a371939423d4288d63e1ac88019
2025-08-14 18:35:27.312 19136-19366 HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader MD5校验正确
2025-08-14 18:35:27.318 19136-19366 HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 清理连接资源
2025-08-14 18:35:27.324 19136-19366 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine APK下载成功: /data/user/0/com.dspread.mdm.service/files/downloads/rule_6eb6f82614c74c3bb1f5ca0e32ef787e_com.chileaf.cl960.sample.apk
2025-08-14 18:35:27.329 19136-19366 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 更新应用状态: 6eb6f82614c74c3bb1f5ca0e32ef787e, com.chileaf.cl960.sample -> A03
2025-08-14 18:35:27.335 19136-19366 WebSocket               com.dspread.mdm.service              I  🔧 C0107 主动式上送: rulebase_update (主动: 25)
2025-08-14 18:35:27.356 19136-19366 WebSocket               com.dspread.mdm.service              I  🔧 添加规则结果: ruleId=6eb6f82614c74c3bb1f5ca0e32ef787e
2025-08-14 18:35:27.386 19136-19366 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0107","serialNo":"01354090202503050399","request_time":"1755167727349","request_id":"1755167727349C0107","version":"1","data":{"ruleId":"6eb6f82614c74c3bb1f5ca0e32ef787e","taskResult":"R02","failedApkList":[{"packName":"mark.via","apkName":"Via","versionName":"6.6.0","versionCode":"20250713","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/4d256c4123b346fd817537b491342204.apk","apkMd5":"0d1225260d03e10a8ffc8409369c442a","apkSize":2565110,"installBy":"0","isSystemApp":false,"priority":0,"result":"B03","taskType":"01"},{"packName":"com.chileaf.cl960.sample","apkName":"cl960","versionName":"1.1.0","versionCode":"1","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/42e16b87eaa849bcaaf0f5fadcac6aab.apk","apkMd5":"54cc3a371939423d4288d63e1ac88019","apkSize":6155416,"installBy":"0","isSystemApp":false,"priority":0,"result":"A03","taskType":"01"}]},"myVersionName":"1.0.08.20250814.DSPREAD.MDM.SERVICE","terminalDate":"20250814183527"}
2025-08-14 18:35:27.392 19136-19366 WebSocket               com.dspread.mdm.service              I  🔧 规则结果实时上送: ruleId=6eb6f82614c74c3bb1f5ca0e32ef787e, result=R02 (1)
2025-08-14 18:35:27.398 19136-19366 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 上报规则执行中状态: 6eb6f82614c74c3bb1f5ca0e32ef787e, 应用数量: 2
2025-08-14 18:35:27.404 19136-19366 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 开始安装APK: /data/user/0/com.dspread.mdm.service/files/downloads/rule_6eb6f82614c74c3bb1f5ca0e32ef787e_com.chileaf.cl960.sample.apk
2025-08-14 18:35:27.410 19136-19366 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 更新应用状态: 6eb6f82614c74c3bb1f5ca0e32ef787e, com.chileaf.cl960.sample -> B02
2025-08-14 18:35:27.415 19136-19366 WebSocket               com.dspread.mdm.service              I  🔧 C0107 主动式上送: rulebase_update (主动: 26)
2025-08-14 18:35:27.435 19136-19366 WebSocket               com.dspread.mdm.service              I  🔧 添加规则结果: ruleId=6eb6f82614c74c3bb1f5ca0e32ef787e
2025-08-14 18:35:27.465 19136-19366 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0107","serialNo":"01354090202503050399","request_time":"1755167727430","request_id":"1755167727430C0107","version":"1","data":{"ruleId":"6eb6f82614c74c3bb1f5ca0e32ef787e","taskResult":"R02","failedApkList":[{"packName":"mark.via","apkName":"Via","versionName":"6.6.0","versionCode":"20250713","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/4d256c4123b346fd817537b491342204.apk","apkMd5":"0d1225260d03e10a8ffc8409369c442a","apkSize":2565110,"installBy":"0","isSystemApp":false,"priority":0,"result":"B03","taskType":"01"},{"packName":"com.chileaf.cl960.sample","apkName":"cl960","versionName":"1.1.0","versionCode":"1","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/42e16b87eaa849bcaaf0f5fadcac6aab.apk","apkMd5":"54cc3a371939423d4288d63e1ac88019","apkSize":6155416,"installBy":"0","isSystemApp":false,"priority":0,"result":"B02","taskType":"01"}]},"myVersionName":"1.0.08.20250814.DSPREAD.MDM.SERVICE","terminalDate":"20250814183527"}
2025-08-14 18:35:27.471 19136-19366 WebSocket               com.dspread.mdm.service              I  🔧 规则结果实时上送: ruleId=6eb6f82614c74c3bb1f5ca0e32ef787e, result=R02 (1)
2025-08-14 18:35:27.477 19136-19366 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 上报规则执行中状态: 6eb6f82614c74c3bb1f5ca0e32ef787e, 应用数量: 2
2025-08-14 18:35:27.483 19136-19366 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 注册安装回调: com.chileaf.cl960.sample (规则ID: 6eb6f82614c74c3bb1f5ca0e32ef787e)
2025-08-14 18:35:27.501 19136-19207 Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"request_time":"1755167726504","org_request_time":"1755167727349","org_request_id":"1755167727349C0107","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755167726504S0000","serialNo":"01354090202503050399"}
2025-08-14 18:35:27.510 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 收到响应消息: transCode=S0000, orgRequestId=1755167727349C0107, state=0, remark=
2025-08-14 18:35:27.511 19136-19366 AppManager              com.dspread.mdm.service              I  ℹ️ 准备安装: com.chileaf.cl960.sample
2025-08-14 18:35:27.517 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 处理规则结果响应: state=0
2025-08-14 18:35:27.522 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 规则结果上传成功
2025-08-14 18:35:27.539 19136-19366 AppManager              com.dspread.mdm.service              I  ℹ️ APK验证通过: com.chileaf.cl960.sample v1.1.0(1) 6011KB
2025-08-14 18:35:27.548 19136-19366 AppManager              com.dspread.mdm.service              I  ℹ️ 创建Session: sessionId=1955902098
2025-08-14 18:35:27.589 19136-19207 Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"request_time":"1755167726587","org_request_time":"1755167727430","org_request_id":"1755167727430C0107","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755167726587S0000","serialNo":"01354090202503050399"}
2025-08-14 18:35:27.600 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 收到响应消息: transCode=S0000, orgRequestId=1755167727430C0107, state=0, remark=
2025-08-14 18:35:27.608 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 处理规则结果响应: state=0
2025-08-14 18:35:27.613 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 规则结果上传成功
2025-08-14 18:35:27.805 19136-19366 AppManager              com.dspread.mdm.service              I  ℹ️ PackageInstaller installation committed: sessionId=1955902098
2025-08-14 18:35:27.833 19136-19206 Common                  com.dspread.mdm.service              D  🔧 ServiceGuardEventHandler WakeLock已释放
2025-08-14 18:35:27.844 19136-19136 AppManager              com.dspread.mdm.service              I  ℹ️ 安装进度: 90%
2025-08-14 18:35:28.054 19136-19136 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 收到广播: android.intent.action.PACKAGE_ADDED
2025-08-14 18:35:28.061 19136-19136 Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 收到包更新广播: android.intent.action.PACKAGE_ADDED, 包名: com.chileaf.cl960.sample
2025-08-14 18:35:28.068 19136-19136 Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 应用安装: com.chileaf.cl960.sample
2025-08-14 18:35:28.078 19136-19136 Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 检查WebSocket状态: packageName=com.chileaf.cl960.sample, action=INSTALL
2025-08-14 18:35:28.084 19136-19136 Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 通知rulebase处理应用安装: com.chileaf.cl960.sample
2025-08-14 18:35:28.094 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 收到系统广播，应用已安装: com.chileaf.cl960.sample
2025-08-14 18:35:28.102 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 找到匹配的安装回调，触发: com.chileaf.cl960.sample
2025-08-14 18:35:28.108 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 安装回调被触发: pkg=com.chileaf.cl960.sample, returnCode=1, error=
2025-08-14 18:35:28.114 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 应用安装启动成功: com.chileaf.cl960.sample
2025-08-14 18:35:28.131 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 从广播更新应用状态: 6eb6f82614c74c3bb1f5ca0e32ef787e, com.chileaf.cl960.sample -> B03
2025-08-14 18:35:28.141 19136-19136 WebSocket               com.dspread.mdm.service              I  🔧 C0107 主动式上送: rulebase_update (主动: 27)
2025-08-14 18:35:28.165 19136-19136 WebSocket               com.dspread.mdm.service              I  🔧 添加规则结果: ruleId=6eb6f82614c74c3bb1f5ca0e32ef787e
2025-08-14 18:35:28.199 19136-19136 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0107","serialNo":"01354090202503050399","request_time":"1755167728157","request_id":"1755167728157C0107","version":"1","data":{"ruleId":"6eb6f82614c74c3bb1f5ca0e32ef787e","taskResult":"R02","failedApkList":[{"packName":"mark.via","apkName":"Via","versionName":"6.6.0","versionCode":"20250713","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/4d256c4123b346fd817537b491342204.apk","apkMd5":"0d1225260d03e10a8ffc8409369c442a","apkSize":2565110,"installBy":"0","isSystemApp":false,"priority":0,"result":"B03","taskType":"01"},{"packName":"com.chileaf.cl960.sample","apkName":"cl960","versionName":"1.1.0","versionCode":"1","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/42e16b87eaa849bcaaf0f5fadcac6aab.apk","apkMd5":"54cc3a371939423d4288d63e1ac88019","apkSize":6155416,"installBy":"0","isSystemApp":false,"priority":0,"result":"B03","taskType":"01"}]},"myVersionName":"1.0.08.20250814.DSPREAD.MDM.SERVICE","terminalDate":"20250814183528"}
2025-08-14 18:35:28.204 19136-19136 WebSocket               com.dspread.mdm.service              I  🔧 规则结果实时上送: ruleId=6eb6f82614c74c3bb1f5ca0e32ef787e, result=R02 (1)
2025-08-14 18:35:28.211 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 上报规则执行中状态: 6eb6f82614c74c3bb1f5ca0e32ef787e, 应用数量: 2
2025-08-14 18:35:28.217 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 开始检查规则完成状态: 6eb6f82614c74c3bb1f5ca0e32ef787e, 总应用数: 2
2025-08-14 18:35:28.222 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 检查应用状态: mark.via -> B03
2025-08-14 18:35:28.228 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 发现成功状态: mark.via -> B03
2025-08-14 18:35:28.235 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 检查应用状态: com.chileaf.cl960.sample -> B03
2025-08-14 18:35:28.242 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 发现成功状态: com.chileaf.cl960.sample -> B03
2025-08-14 18:35:28.248 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 规则完成检查结果: 6eb6f82614c74c3bb1f5ca0e32ef787e, 全部完成: true, 完成数: 2/2, 有失败: false
2025-08-14 18:35:28.254 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 规则执行全部成功，上送RULEBASED_SUCCESS: 6eb6f82614c74c3bb1f5ca0e32ef787e
2025-08-14 18:35:28.260 19136-19136 WebSocket               com.dspread.mdm.service              I  🔧 C0107 主动式上送: rulebase_update (主动: 28)
2025-08-14 18:35:28.292 19136-19136 WebSocket               com.dspread.mdm.service              I  🔧 添加规则结果: ruleId=6eb6f82614c74c3bb1f5ca0e32ef787e
2025-08-14 18:35:28.328 19136-19207 Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"request_time":"1755167727320","org_request_time":"1755167728157","org_request_id":"1755167728157C0107","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755167727320S0000","serialNo":"01354090202503050399"}
2025-08-14 18:35:28.338 19136-19136 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0107","serialNo":"01354090202503050399","request_time":"1755167728282","request_id":"1755167728282C0107","version":"1","data":{"ruleId":"6eb6f82614c74c3bb1f5ca0e32ef787e","taskResult":"R03","failedApkList":[{"packName":"mark.via","apkName":"Via","versionName":"6.6.0","versionCode":"20250713","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/4d256c4123b346fd817537b491342204.apk","apkMd5":"0d1225260d03e10a8ffc8409369c442a","apkSize":2565110,"installBy":"0","isSystemApp":false,"priority":0,"result":"B03","taskType":"01"},{"packName":"com.chileaf.cl960.sample","apkName":"cl960","versionName":"1.1.0","versionCode":"1","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/42e16b87eaa849bcaaf0f5fadcac6aab.apk","apkMd5":"54cc3a371939423d4288d63e1ac88019","apkSize":6155416,"installBy":"0","isSystemApp":false,"priority":0,"result":"B03","taskType":"01"}]},"myVersionName":"1.0.08.20250814.DSPREAD.MDM.SERVICE","terminalDate":"20250814183528"}
2025-08-14 18:35:28.342 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 收到响应消息: transCode=S0000, orgRequestId=1755167728157C0107, state=0, remark=
2025-08-14 18:35:28.344 19136-19136 WebSocket               com.dspread.mdm.service              I  🔧 规则结果实时上送: ruleId=6eb6f82614c74c3bb1f5ca0e32ef787e, result=R03 (1)
2025-08-14 18:35:28.350 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 处理规则结果响应: state=0
2025-08-14 18:35:28.351 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 上报规则状态: 6eb6f82614c74c3bb1f5ca0e32ef787e -> R03
2025-08-14 18:35:28.356 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 规则结果上传成功
2025-08-14 18:35:28.358 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleStateMonitor 收到状态变化: 6eb6f82614c74c3bb1f5ca0e32ef787e, R02 -> R03
2025-08-14 18:35:28.363 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleStateMonitor 状态变化已记录: 6eb6f82614c74c3bb1f5ca0e32ef787e, R03
2025-08-14 18:35:28.369 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleStateMachine 状态转换成功: 6eb6f82614c74c3bb1f5ca0e32ef787e, R02 -> R03
2025-08-14 18:35:28.375 19136-19136 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 规则执行完成: 6eb6f82614c74c3bb1f5ca0e32ef787e, 有失败: false
2025-08-14 18:35:28.380 19136-19136 Platform                com.dspread.mdm.service              D  🔧 重新收集应用信息（强制刷新）
2025-08-14 18:35:28.485 19136-19136 AppManager              com.dspread.mdm.service              I  ℹ️ 应用信息获取完成: 总数132(系统129/用户3) 返回3个
2025-08-14 18:35:28.503 19136-19207 Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"request_time":"1755167727458","org_request_time":"1755167728282","org_request_id":"1755167728282C0107","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755167727458S0000","serialNo":"01354090202503050399"}
2025-08-14 18:35:28.507 19136-19136 Platform                com.dspread.mdm.service              D  🔧 应用信息: 3 个应用
2025-08-14 18:35:28.511 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 收到响应消息: transCode=S0000, orgRequestId=1755167728282C0107, state=0, remark=
2025-08-14 18:35:28.512 19136-19136 Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 应用信息缓存刷新完成
2025-08-14 18:35:28.515 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 处理规则结果响应: state=0
2025-08-14 18:35:28.516 19136-19136 ContextImpl             com.dspread.mdm.service              W  Calling a method in the system process without a qualified user: android.app.ContextImpl.sendBroadcast:1220 android.content.ContextWrapper.sendBroadcast:504 com.dspread.mdm.service.broadcast.core.BroadcastSender.sendBroadcast:99 com.dspread.mdm.service.broadcast.core.BroadcastSender.sendBroadcast:23 com.dspread.mdm.service.broadcast.handlers.system.PackageUpdateEventHandlerImpl.sendAppRefreshBroadcast:491 
2025-08-14 18:35:28.519 19136-19136 Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 发送应用刷新广播
2025-08-14 18:35:28.521 19136-19207 WebSocket               com.dspread.mdm.service              I  🔧 规则结果上传成功
