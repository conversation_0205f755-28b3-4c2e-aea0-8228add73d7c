---------------------------- PROCESS STARTED (4396) for package com.dspread.mdm.service ----------------------------
--------- beginning of system
--------- beginning of main
2025-08-21 11:11:22.692  4396-9685  WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误 (Ask Gemini)
                                                                                                    com.neovisionaries.ws.client.NoMoreFrameException: No more WebSocket frame from the server.
                                                                                                         at com.neovisionaries.ws.client.WebSocketInputStream.readFrame(WebSocketInputStream.java:54)
                                                                                                         at com.neovisionaries.ws.client.ReadingThread.readFrame(ReadingThread.java:338)
                                                                                                         at com.neovisionaries.ws.client.ReadingThread.main(ReadingThread.java:99)
                                                                                                         at com.neovisionaries.ws.client.ReadingThread.runMain(ReadingThread.java:64)
                                                                                                         at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45)
2025-08-21 11:11:22.699  4396-9685  WebSocket               com.dspread.mdm.service              I  🔧 onError: 不触发重连，等待 onDisconnected 处理
2025-08-21 11:11:22.716  4396-9686  WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误 (Ask Gemini)
                                                                                                    com.neovisionaries.ws.client.WebSocketException: Flushing frames to the server failed: Socket closed
                                                                                                         at com.neovisionaries.ws.client.WritingThread.doFlush(WritingThread.java:434)
                                                                                                         at com.neovisionaries.ws.client.WritingThread.sendFrames(WritingThread.java:384)
                                                                                                         at com.neovisionaries.ws.client.WritingThread.main(WritingThread.java:108)
                                                                                                         at com.neovisionaries.ws.client.WritingThread.runMain(WritingThread.java:55)
                                                                                                         at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45)
                                                                                                    Caused by: java.net.SocketException: Socket closed
                                                                                                         at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.writeInternal(ConscryptEngineSocket.java:728)
                                                                                                         at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.write(ConscryptEngineSocket.java:699)
                                                                                                         at java.io.BufferedOutputStream.flushBuffer(BufferedOutputStream.java:81)
                                                                                                         at java.io.BufferedOutputStream.flush(BufferedOutputStream.java:142)
                                                                                                         at java.io.BufferedOutputStream.flush(BufferedOutputStream.java:143)
                                                                                                         at com.neovisionaries.ws.client.WritingThread.flush(WritingThread.java:271)
                                                                                                         at com.neovisionaries.ws.client.WritingThread.doFlush(WritingThread.java:422)
                                                                                                         at com.neovisionaries.ws.client.WritingThread.sendFrames(WritingThread.java:384) 
                                                                                                         at com.neovisionaries.ws.client.WritingThread.main(WritingThread.java:108) 
                                                                                                         at com.neovisionaries.ws.client.WritingThread.runMain(WritingThread.java:55) 
                                                                                                         at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45) 
2025-08-21 11:11:22.723  4396-9686  WebSocket               com.dspread.mdm.service              I  🔧 onError: 不触发重连，等待 onDisconnected 处理
2025-08-21 11:11:22.734  4396-9686  WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误 (Ask Gemini)
                                                                                                    com.neovisionaries.ws.client.WebSocketException: Flushing frames to the server failed: Socket closed
                                                                                                         at com.neovisionaries.ws.client.WritingThread.doFlush(WritingThread.java:434)
                                                                                                         at com.neovisionaries.ws.client.WritingThread.sendFrames(WritingThread.java:384)
                                                                                                         at com.neovisionaries.ws.client.WritingThread.main(WritingThread.java:120)
                                                                                                         at com.neovisionaries.ws.client.WritingThread.runMain(WritingThread.java:55)
                                                                                                         at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45)
                                                                                                    Caused by: java.net.SocketException: Socket closed
                                                                                                         at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.writeInternal(ConscryptEngineSocket.java:728)
                                                                                                         at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.write(ConscryptEngineSocket.java:699)
                                                                                                         at java.io.BufferedOutputStream.flushBuffer(BufferedOutputStream.java:81)
                                                                                                         at java.io.BufferedOutputStream.flush(BufferedOutputStream.java:142)
                                                                                                         at java.io.BufferedOutputStream.flush(BufferedOutputStream.java:143)
                                                                                                         at com.neovisionaries.ws.client.WritingThread.flush(WritingThread.java:271)
                                                                                                         at com.neovisionaries.ws.client.WritingThread.doFlush(WritingThread.java:422)
                                                                                                         at com.neovisionaries.ws.client.WritingThread.sendFrames(WritingThread.java:384) 
                                                                                                         at com.neovisionaries.ws.client.WritingThread.main(WritingThread.java:120) 
                                                                                                         at com.neovisionaries.ws.client.WritingThread.runMain(WritingThread.java:55) 
                                                                                                         at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45) 
2025-08-21 11:11:22.741  4396-9686  WebSocket               com.dspread.mdm.service              I  🔧 onError: 不触发重连，等待 onDisconnected 处理
2025-08-21 11:11:22.749  4396-9686  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接断开 - 客户端主动断开
2025-08-21 11:11:22.755  4396-9686  WebSocket               com.dspread.mdm.service              I  🔧    服务器关闭帧: code=-1, reason=无原因
2025-08-21 11:11:22.760  4396-9686  WebSocket               com.dspread.mdm.service              I  🔧    客户端关闭帧: code=1002, reason=No more WebSocket frame from the server.
2025-08-21 11:11:22.764  4396-9686  WebSocket               com.dspread.mdm.service              I  🔧    断开发起方: 客户端
2025-08-21 11:11:22.769  4396-9686  WebSocket               com.dspread.mdm.service              I  🔧 停止PING/PONG检测
2025-08-21 11:11:22.774  4396-9686  WebSocket               com.dspread.mdm.service              I  🔧 reconnect() 被调用，调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onDisconnected:727
2025-08-21 11:11:22.778  4396-9686  WebSocket               com.dspread.mdm.service              I  🔧 开始重连 WebSocket... (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onDisconnected:727)
2025-08-21 11:11:22.787  4396-9686  WebSocket               com.dspread.mdm.service              I  🔧 使用Provisioning配置的重连策略: delayPolicy=1, delaySwitch=1, delayTime=60
2025-08-21 11:11:22.791  4396-9686  WebSocket               com.dspread.mdm.service              I  🔧 当前断开记录数: 10, 延迟开关: 1
2025-08-21 11:11:22.799  4396-9686  WebSocket               com.dspread.mdm.service              I  🔧 断开记录 最早一次断开: 2025-08-21 10:54:15
2025-08-21 11:11:22.806  4396-9686  WebSocket               com.dspread.mdm.service              I  🔧 断开记录 最后一次断开: 2025-08-21 11:11:22
2025-08-21 11:11:22.810  4396-9686  WebSocket               com.dspread.mdm.service              I  🔧 时间差: 17分钟 (配置阈值: 1分钟)
2025-08-21 11:11:22.814  4396-9686  WebSocket               com.dspread.mdm.service              I  🔧 断开间隔正常，使用标准重连策略
2025-08-21 11:11:22.817  4396-9686  WebSocket               com.dspread.mdm.service              I  🔧 重连策略计算完成: 第1次重连，间隔3000ms (3秒)
2025-08-21 11:11:22.821  4396-9686  WebSocket               com.dspread.mdm.service              I  🔧 开始第1次重连，间隔3000ms (3秒) (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onDisconnected:727)
2025-08-21 11:12:20.732  4396-4396  WebSocket               com.dspread.mdm.service              I  🔧 重连 WebSocket: wss://api.dspreadserv.net/status/websocket/register?serialNo=MDEzNTQwOTAyMDI1MDMwNTAzOTk=&publicKey=TUlHZk1BMEdDU3FHU0liM0RRRUJBUVVBQTRHTkFEQ0JpUUtCZ1FENjVsY3lJc2p6b0swWFpMZ2RUQmpOaCtKVDhpdGNlUHp1eVVMUGVyS281VzRxWjZRUy9QZS9zWU1PdG04RzhoZk5pSzZEN0xKQnZ2V243RW1nVTdVM2x3YjR4VXI3SS84V1hFZEFBWW1WNVlTdzdOenBQYkFaTUxSNVFmazBiK2lvcDZxaTlkeFpTazFsOGhlL0E3ZEV2MC92ZnNmRTU4a2k5UzRLVVRMVUhRSURBUUFC&query=0&msgVer=3&timestamp=1755745940719&signature=uTyj31z2v81bTlb+KN7+z/HTGQtrwVlIu8A0hZTZ6WE1CvSa5AkPHfKYdGnj/BWK1jJ+3rtYNCwBlm3Wa6Fk8HC4wMY4Si0NURbNb0zxMOgAWzTjqftCUyBICp47I4vONOiK6NRdoSfZOvfa7WQijB8YWm31gpTswmUuHx9a4yM=
2025-08-21 11:12:20.739  4396-4396  WebSocket               com.dspread.mdm.service              I  🔧 未检测到系统代理设置
2025-08-21 11:12:20.800  4396-9691  TrafficStats            com.dspread.mdm.service              D  tagSocket(111) with statsTag=0xffffffff, statsUid=-1
2025-08-21 11:12:21.051  4396-9692  TrafficStats            com.dspread.mdm.service              D  tagSocket(118) with statsTag=0xffffffff, statsUid=-1
2025-08-21 11:12:24.027  4396-9694  Common                  com.dspread.mdm.service              I  ✅ WebSocket 物理连接成功，等待服务器 hellMsg
2025-08-21 11:12:24.034  4396-9694  WebSocket               com.dspread.mdm.service              I  🔧 ===== 心跳发送器实际配置 =====
2025-08-21 11:12:24.040  4396-9694  WebSocket               com.dspread.mdm.service              I  🔧 实际 PING 发送间隔: 30000ms (30秒)
2025-08-21 11:12:24.047  4396-9694  WebSocket               com.dspread.mdm.service              I  🔧 PING 定时器名称: PingSender
2025-08-21 11:12:24.051  4396-9693  Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"hellMsg":"hello 01354090202503050399","tranCode":"S0000","version":"1","rebootTime":"01:39:44","serialNo":"01354090202503050399","deviceStatus":"6"}
2025-08-21 11:12:24.053  4396-9694  WebSocket               com.dspread.mdm.service              I  🔧 实际 PONG 发送间隔: 0ms (0秒)
2025-08-21 11:12:24.057  4396-9694  WebSocket               com.dspread.mdm.service              I  🔧 PONG 定时器名称: PongSender
2025-08-21 11:12:24.057  4396-9693  Common                  com.dspread.mdm.service              I  ✅ 收到服务器 hellMsg，WebSocket 业务连接成功！
2025-08-21 11:12:24.062  4396-9694  WebSocket               com.dspread.mdm.service              I  🔧 ===============================
2025-08-21 11:12:24.062  4396-9693  Common                  com.dspread.mdm.service              I  ✅ WebSocket 业务连接成功！
2025-08-21 11:12:24.066  4396-9694  WebSocket               com.dspread.mdm.service              I  🔧 启动PING/PONG检测，阈值: 连续3个PING无PONG响应
2025-08-21 11:12:24.067  4396-9693  WebSocket               com.dspread.mdm.service              I  🔧 业务连接成功，重连计数已重置为 0
2025-08-21 11:12:24.071  4396-9693  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接成功，检查自身更新任务状态
2025-08-21 11:12:24.077  4396-9693  Receiver                com.dspread.mdm.service              D  🔧 发送广播: com.dspread.mdm.service.CHECK_SELF_UPDATE_STATUS
2025-08-21 11:12:24.082  4396-9693  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接成功
2025-08-21 11:12:24.086  4396-9693  WebSocket               com.dspread.mdm.service              I  🔧 设备状态: 6
2025-08-21 11:12:24.090  4396-9693  WebSocket               com.dspread.mdm.service              I  🔧 检查首次连接标志: isFirstTimeWebSocketConnected=false
2025-08-21 11:12:24.095  4396-9693  WebSocket               com.dspread.mdm.service              I  🔧 非首次连接，跳过初始化信息发送和初始化检查
2025-08-21 11:12:24.099  4396-9693  WebSocket               com.dspread.mdm.service              I  🔧 执行必要的状态同步检查
2025-08-21 11:12:24.103  4396-9693  WebSocket               com.dspread.mdm.service              I  🔧 检查服务更新结果
2025-08-21 11:12:24.107  4396-9693  Task                    com.dspread.mdm.service              W  ⚠️ 任务列表为空，跳过服务更新结果检查
2025-08-21 11:12:24.111  4396-9693  WebSocket               com.dspread.mdm.service              I  🔧 检查OS升级状态...
2025-08-21 11:12:24.115  4396-9693  Common                  com.dspread.mdm.service              D  🔧 OsUpdateStatusChecker WebSocket连接成功，检查OS升级状态
2025-08-21 11:12:24.119  4396-9693  Common                  com.dspread.mdm.service              D  🔧 OsUpdateStatusChecker 没有待检查的OS升级任务
2025-08-21 11:12:24.123  4396-9693  WebSocket               com.dspread.mdm.service              I  🔧 OS升级状态检查已触发
2025-08-21 11:13:24.112  4396-9693  WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误 (Ask Gemini)
                                                                                                    com.neovisionaries.ws.client.NoMoreFrameException: No more WebSocket frame from the server.
                                                                                                         at com.neovisionaries.ws.client.WebSocketInputStream.readFrame(WebSocketInputStream.java:54)
                                                                                                         at com.neovisionaries.ws.client.ReadingThread.readFrame(ReadingThread.java:338)
                                                                                                         at com.neovisionaries.ws.client.ReadingThread.main(ReadingThread.java:99)
                                                                                                         at com.neovisionaries.ws.client.ReadingThread.runMain(ReadingThread.java:64)
                                                                                                         at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45)
2025-08-21 11:13:24.120  4396-9693  WebSocket               com.dspread.mdm.service              I  🔧 onError: 不触发重连，等待 onDisconnected 处理
2025-08-21 11:13:24.134  4396-9694  WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误 (Ask Gemini)
                                                                                                    com.neovisionaries.ws.client.WebSocketException: Flushing frames to the server failed: Socket closed
                                                                                                         at com.neovisionaries.ws.client.WritingThread.doFlush(WritingThread.java:434)
                                                                                                         at com.neovisionaries.ws.client.WritingThread.sendFrames(WritingThread.java:384)
                                                                                                         at com.neovisionaries.ws.client.WritingThread.main(WritingThread.java:108)
                                                                                                         at com.neovisionaries.ws.client.WritingThread.runMain(WritingThread.java:55)
                                                                                                         at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45)
                                                                                                    Caused by: java.net.SocketException: Socket closed
                                                                                                         at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.writeInternal(ConscryptEngineSocket.java:728)
                                                                                                         at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.write(ConscryptEngineSocket.java:699)
                                                                                                         at java.io.BufferedOutputStream.flushBuffer(BufferedOutputStream.java:81)
                                                                                                         at java.io.BufferedOutputStream.flush(BufferedOutputStream.java:142)
                                                                                                         at java.io.BufferedOutputStream.flush(BufferedOutputStream.java:143)
                                                                                                         at com.neovisionaries.ws.client.WritingThread.flush(WritingThread.java:271)
                                                                                                         at com.neovisionaries.ws.client.WritingThread.doFlush(WritingThread.java:422)
                                                                                                         at com.neovisionaries.ws.client.WritingThread.sendFrames(WritingThread.java:384) 
                                                                                                         at com.neovisionaries.ws.client.WritingThread.main(WritingThread.java:108) 
                                                                                                         at com.neovisionaries.ws.client.WritingThread.runMain(WritingThread.java:55) 
                                                                                                         at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45) 
2025-08-21 11:13:24.140  4396-9694  WebSocket               com.dspread.mdm.service              I  🔧 onError: 不触发重连，等待 onDisconnected 处理
2025-08-21 11:13:24.148  4396-9694  WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误 (Ask Gemini)
                                                                                                    com.neovisionaries.ws.client.WebSocketException: Flushing frames to the server failed: Socket closed
                                                                                                         at com.neovisionaries.ws.client.WritingThread.doFlush(WritingThread.java:434)
                                                                                                         at com.neovisionaries.ws.client.WritingThread.sendFrames(WritingThread.java:384)
                                                                                                         at com.neovisionaries.ws.client.WritingThread.main(WritingThread.java:120)
                                                                                                         at com.neovisionaries.ws.client.WritingThread.runMain(WritingThread.java:55)
                                                                                                         at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45)
                                                                                                    Caused by: java.net.SocketException: Socket closed
                                                                                                         at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.writeInternal(ConscryptEngineSocket.java:728)
                                                                                                         at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.write(ConscryptEngineSocket.java:699)
                                                                                                         at java.io.BufferedOutputStream.flushBuffer(BufferedOutputStream.java:81)
                                                                                                         at java.io.BufferedOutputStream.flush(BufferedOutputStream.java:142)
                                                                                                         at java.io.BufferedOutputStream.flush(BufferedOutputStream.java:143)
                                                                                                         at com.neovisionaries.ws.client.WritingThread.flush(WritingThread.java:271)
                                                                                                         at com.neovisionaries.ws.client.WritingThread.doFlush(WritingThread.java:422)
                                                                                                         at com.neovisionaries.ws.client.WritingThread.sendFrames(WritingThread.java:384) 
                                                                                                         at com.neovisionaries.ws.client.WritingThread.main(WritingThread.java:120) 
                                                                                                         at com.neovisionaries.ws.client.WritingThread.runMain(WritingThread.java:55) 
                                                                                                         at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45) 
2025-08-21 11:13:24.194  4396-9694  WebSocket               com.dspread.mdm.service              I  🔧 onError: 不触发重连，等待 onDisconnected 处理
2025-08-21 11:13:24.200  4396-9694  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接断开 - 客户端主动断开
2025-08-21 11:13:24.204  4396-9694  WebSocket               com.dspread.mdm.service              I  🔧    服务器关闭帧: code=-1, reason=无原因
2025-08-21 11:13:24.209  4396-9694  WebSocket               com.dspread.mdm.service              I  🔧    客户端关闭帧: code=1002, reason=No more WebSocket frame from the server.
2025-08-21 11:13:24.214  4396-9694  WebSocket               com.dspread.mdm.service              I  🔧    断开发起方: 客户端
2025-08-21 11:13:24.219  4396-9694  WebSocket               com.dspread.mdm.service              I  🔧 停止PING/PONG检测
2025-08-21 11:13:24.221  4396-4407  System                  com.dspread.mdm.service              W  A resource failed to call close. 
2025-08-21 11:13:24.223  4396-9694  WebSocket               com.dspread.mdm.service              I  🔧 reconnect() 被调用，调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onDisconnected:727
2025-08-21 11:13:24.224  4396-4407  System                  com.dspread.mdm.service              W  A resource failed to call close. 
2025-08-21 11:13:24.227  4396-9694  WebSocket               com.dspread.mdm.service              I  🔧 开始重连 WebSocket... (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onDisconnected:727)
2025-08-21 11:13:24.235  4396-9694  WebSocket               com.dspread.mdm.service              I  🔧 使用Provisioning配置的重连策略: delayPolicy=1, delaySwitch=1, delayTime=60
2025-08-21 11:13:24.238  4396-9694  WebSocket               com.dspread.mdm.service              I  🔧 当前断开记录数: 10, 延迟开关: 1
2025-08-21 11:13:24.245  4396-9694  WebSocket               com.dspread.mdm.service              I  🔧 断开记录 最早一次断开: 2025-08-21 10:56:09
2025-08-21 11:13:24.252  4396-9694  WebSocket               com.dspread.mdm.service              I  🔧 断开记录 最后一次断开: 2025-08-21 11:13:24
2025-08-21 11:13:24.256  4396-9694  WebSocket               com.dspread.mdm.service              I  🔧 时间差: 17分钟 (配置阈值: 1分钟)
2025-08-21 11:13:24.259  4396-9694  WebSocket               com.dspread.mdm.service              I  🔧 断开间隔正常，使用标准重连策略
2025-08-21 11:13:24.263  4396-9694  WebSocket               com.dspread.mdm.service              I  🔧 重连策略计算完成: 第1次重连，间隔3000ms (3秒)
2025-08-21 11:13:24.266  4396-9694  WebSocket               com.dspread.mdm.service              I  🔧 开始第1次重连，间隔3000ms (3秒) (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onDisconnected:727)
2025-08-21 11:14:14.225  4396-4396  WebSocket               com.dspread.mdm.service              I  🔧 重连 WebSocket: wss://api.dspreadserv.net/status/websocket/register?serialNo=MDEzNTQwOTAyMDI1MDMwNTAzOTk=&publicKey=TUlHZk1BMEdDU3FHU0liM0RRRUJBUVVBQTRHTkFEQ0JpUUtCZ1FENjVsY3lJc2p6b0swWFpMZ2RUQmpOaCtKVDhpdGNlUHp1eVVMUGVyS281VzRxWjZRUy9QZS9zWU1PdG04RzhoZk5pSzZEN0xKQnZ2V243RW1nVTdVM2x3YjR4VXI3SS84V1hFZEFBWW1WNVlTdzdOenBQYkFaTUxSNVFmazBiK2lvcDZxaTlkeFpTazFsOGhlL0E3ZEV2MC92ZnNmRTU4a2k5UzRLVVRMVUhRSURBUUFC&query=0&msgVer=3&timestamp=1755746054210&signature=QyFkIBASak80jlHAieyDUw384KFHzOhl6XBBiHVBD2Dmp3qeLOoWDv8RLfLPm/Z0f6mfGVVw/F8eqVdhNTyF8X6LOmEQNfXJOujABprY8N8B32EZhW5cpC3ReF8WVRE/jxooNXbmRL/71P8WLaNTzvgSDdVokVlr6RoETSs5IRw=
2025-08-21 11:14:14.232  4396-4396  WebSocket               com.dspread.mdm.service              I  🔧 未检测到系统代理设置
2025-08-21 11:14:14.268  4396-9699  TrafficStats            com.dspread.mdm.service              D  tagSocket(111) with statsTag=0xffffffff, statsUid=-1
2025-08-21 11:14:14.519  4396-9700  TrafficStats            com.dspread.mdm.service              D  tagSocket(115) with statsTag=0xffffffff, statsUid=-1
2025-08-21 11:14:16.159  4396-9701  Common                  com.dspread.mdm.service              I  ✅ WebSocket 物理连接成功，等待服务器 hellMsg
2025-08-21 11:14:16.164  4396-9701  WebSocket               com.dspread.mdm.service              I  🔧 ===== 心跳发送器实际配置 =====
2025-08-21 11:14:16.169  4396-9701  WebSocket               com.dspread.mdm.service              I  🔧 实际 PING 发送间隔: 30000ms (30秒)
2025-08-21 11:14:16.174  4396-9701  WebSocket               com.dspread.mdm.service              I  🔧 PING 定时器名称: PingSender
2025-08-21 11:14:16.179  4396-9701  WebSocket               com.dspread.mdm.service              I  🔧 实际 PONG 发送间隔: 0ms (0秒)
2025-08-21 11:14:16.184  4396-9701  WebSocket               com.dspread.mdm.service              I  🔧 PONG 定时器名称: PongSender
2025-08-21 11:14:16.188  4396-9701  WebSocket               com.dspread.mdm.service              I  🔧 ===============================
2025-08-21 11:14:16.193  4396-9701  WebSocket               com.dspread.mdm.service              I  🔧 启动PING/PONG检测，阈值: 连续3个PING无PONG响应
2025-08-21 11:14:16.212  4396-9701  Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"hellMsg":"hello 01354090202503050399","tranCode":"S0000","version":"1","rebootTime":"03:39:35","serialNo":"01354090202503050399","deviceStatus":"6"}
2025-08-21 11:14:16.216  4396-9701  Common                  com.dspread.mdm.service              I  ✅ 收到服务器 hellMsg，WebSocket 业务连接成功！
2025-08-21 11:14:16.220  4396-9701  Common                  com.dspread.mdm.service              I  ✅ WebSocket 业务连接成功！
2025-08-21 11:14:16.224  4396-9701  WebSocket               com.dspread.mdm.service              I  🔧 业务连接成功，重连计数已重置为 0
2025-08-21 11:14:16.228  4396-9701  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接成功，检查自身更新任务状态
2025-08-21 11:14:16.234  4396-9701  Receiver                com.dspread.mdm.service              D  🔧 发送广播: com.dspread.mdm.service.CHECK_SELF_UPDATE_STATUS
2025-08-21 11:14:16.239  4396-9701  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接成功
2025-08-21 11:14:16.242  4396-9701  WebSocket               com.dspread.mdm.service              I  🔧 设备状态: 6
2025-08-21 11:14:16.246  4396-9701  WebSocket               com.dspread.mdm.service              I  🔧 检查首次连接标志: isFirstTimeWebSocketConnected=false
2025-08-21 11:14:16.250  4396-9701  WebSocket               com.dspread.mdm.service              I  🔧 非首次连接，跳过初始化信息发送和初始化检查
2025-08-21 11:14:16.253  4396-9701  WebSocket               com.dspread.mdm.service              I  🔧 执行必要的状态同步检查
2025-08-21 11:14:16.257  4396-9701  WebSocket               com.dspread.mdm.service              I  🔧 检查服务更新结果
2025-08-21 11:14:16.260  4396-9701  Task                    com.dspread.mdm.service              W  ⚠️ 任务列表为空，跳过服务更新结果检查
2025-08-21 11:14:16.264  4396-9701  WebSocket               com.dspread.mdm.service              I  🔧 检查OS升级状态...
2025-08-21 11:14:16.267  4396-9701  Common                  com.dspread.mdm.service              D  🔧 OsUpdateStatusChecker WebSocket连接成功，检查OS升级状态
2025-08-21 11:14:16.271  4396-9701  Common                  com.dspread.mdm.service              D  🔧 OsUpdateStatusChecker 没有待检查的OS升级任务
2025-08-21 11:14:16.274  4396-9701  WebSocket               com.dspread.mdm.service              I  🔧 OS升级状态检查已触发
2025-08-21 11:15:16.366  4396-9701  WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误 (Ask Gemini)
                                                                                                    com.neovisionaries.ws.client.NoMoreFrameException: No more WebSocket frame from the server.
                                                                                                         at com.neovisionaries.ws.client.WebSocketInputStream.readFrame(WebSocketInputStream.java:54)
                                                                                                         at com.neovisionaries.ws.client.ReadingThread.readFrame(ReadingThread.java:338)
                                                                                                         at com.neovisionaries.ws.client.ReadingThread.main(ReadingThread.java:99)
                                                                                                         at com.neovisionaries.ws.client.ReadingThread.runMain(ReadingThread.java:64)
                                                                                                         at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45)
2025-08-21 11:15:16.372  4396-9701  WebSocket               com.dspread.mdm.service              I  🔧 onError: 不触发重连，等待 onDisconnected 处理
2025-08-21 11:15:16.386  4396-9702  WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误 (Ask Gemini)
                                                                                                    com.neovisionaries.ws.client.WebSocketException: Flushing frames to the server failed: Socket closed
                                                                                                         at com.neovisionaries.ws.client.WritingThread.doFlush(WritingThread.java:434)
                                                                                                         at com.neovisionaries.ws.client.WritingThread.sendFrames(WritingThread.java:384)
                                                                                                         at com.neovisionaries.ws.client.WritingThread.main(WritingThread.java:108)
                                                                                                         at com.neovisionaries.ws.client.WritingThread.runMain(WritingThread.java:55)
                                                                                                         at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45)
                                                                                                    Caused by: java.net.SocketException: Socket closed
                                                                                                         at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.writeInternal(ConscryptEngineSocket.java:728)
                                                                                                         at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.write(ConscryptEngineSocket.java:699)
                                                                                                         at java.io.BufferedOutputStream.flushBuffer(BufferedOutputStream.java:81)
                                                                                                         at java.io.BufferedOutputStream.flush(BufferedOutputStream.java:142)
                                                                                                         at java.io.BufferedOutputStream.flush(BufferedOutputStream.java:143)
                                                                                                         at com.neovisionaries.ws.client.WritingThread.flush(WritingThread.java:271)
                                                                                                         at com.neovisionaries.ws.client.WritingThread.doFlush(WritingThread.java:422)
                                                                                                         at com.neovisionaries.ws.client.WritingThread.sendFrames(WritingThread.java:384) 
                                                                                                         at com.neovisionaries.ws.client.WritingThread.main(WritingThread.java:108) 
                                                                                                         at com.neovisionaries.ws.client.WritingThread.runMain(WritingThread.java:55) 
                                                                                                         at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45) 
2025-08-21 11:15:16.393  4396-9702  WebSocket               com.dspread.mdm.service              I  🔧 onError: 不触发重连，等待 onDisconnected 处理
2025-08-21 11:15:16.403  4396-9702  WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误 (Ask Gemini)
                                                                                                    com.neovisionaries.ws.client.WebSocketException: Flushing frames to the server failed: Socket closed
                                                                                                         at com.neovisionaries.ws.client.WritingThread.doFlush(WritingThread.java:434)
                                                                                                         at com.neovisionaries.ws.client.WritingThread.sendFrames(WritingThread.java:384)
                                                                                                         at com.neovisionaries.ws.client.WritingThread.main(WritingThread.java:120)
                                                                                                         at com.neovisionaries.ws.client.WritingThread.runMain(WritingThread.java:55)
                                                                                                         at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45)
                                                                                                    Caused by: java.net.SocketException: Socket closed
                                                                                                         at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.writeInternal(ConscryptEngineSocket.java:728)
                                                                                                         at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.write(ConscryptEngineSocket.java:699)
                                                                                                         at java.io.BufferedOutputStream.flushBuffer(BufferedOutputStream.java:81)
                                                                                                         at java.io.BufferedOutputStream.flush(BufferedOutputStream.java:142)
                                                                                                         at java.io.BufferedOutputStream.flush(BufferedOutputStream.java:143)
                                                                                                         at com.neovisionaries.ws.client.WritingThread.flush(WritingThread.java:271)
                                                                                                         at com.neovisionaries.ws.client.WritingThread.doFlush(WritingThread.java:422)
                                                                                                         at com.neovisionaries.ws.client.WritingThread.sendFrames(WritingThread.java:384) 
                                                                                                         at com.neovisionaries.ws.client.WritingThread.main(WritingThread.java:120) 
                                                                                                         at com.neovisionaries.ws.client.WritingThread.runMain(WritingThread.java:55) 
                                                                                                         at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45) 
2025-08-21 11:15:16.409  4396-9702  WebSocket               com.dspread.mdm.service              I  🔧 onError: 不触发重连，等待 onDisconnected 处理
2025-08-21 11:15:16.418  4396-9702  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接断开 - 客户端主动断开
2025-08-21 11:15:16.424  4396-9702  WebSocket               com.dspread.mdm.service              I  🔧    服务器关闭帧: code=-1, reason=无原因
2025-08-21 11:15:16.430  4396-9702  WebSocket               com.dspread.mdm.service              I  🔧    客户端关闭帧: code=1002, reason=No more WebSocket frame from the server.
2025-08-21 11:15:16.436  4396-9702  WebSocket               com.dspread.mdm.service              I  🔧    断开发起方: 客户端
2025-08-21 11:15:16.442  4396-9702  WebSocket               com.dspread.mdm.service              I  🔧 停止PING/PONG检测
2025-08-21 11:15:16.447  4396-9702  WebSocket               com.dspread.mdm.service              I  🔧 reconnect() 被调用，调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onDisconnected:727
2025-08-21 11:15:16.452  4396-9702  WebSocket               com.dspread.mdm.service              I  🔧 开始重连 WebSocket... (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onDisconnected:727)
2025-08-21 11:15:16.462  4396-9702  WebSocket               com.dspread.mdm.service              I  🔧 使用Provisioning配置的重连策略: delayPolicy=1, delaySwitch=1, delayTime=60
2025-08-21 11:15:16.466  4396-9702  WebSocket               com.dspread.mdm.service              I  🔧 当前断开记录数: 10, 延迟开关: 1
2025-08-21 11:15:16.475  4396-9702  WebSocket               com.dspread.mdm.service              I  🔧 断开记录 最早一次断开: 2025-08-21 10:57:16
2025-08-21 11:15:16.483  4396-9702  WebSocket               com.dspread.mdm.service              I  🔧 断开记录 最后一次断开: 2025-08-21 11:15:16
2025-08-21 11:15:16.487  4396-9702  WebSocket               com.dspread.mdm.service              I  🔧 时间差: 17分钟 (配置阈值: 1分钟)
2025-08-21 11:15:16.490  4396-9702  WebSocket               com.dspread.mdm.service              I  🔧 断开间隔正常，使用标准重连策略
2025-08-21 11:15:16.494  4396-9702  WebSocket               com.dspread.mdm.service              I  🔧 重连策略计算完成: 第1次重连，间隔3000ms (3秒)
2025-08-21 11:15:16.498  4396-9702  WebSocket               com.dspread.mdm.service              I  🔧 开始第1次重连，间隔3000ms (3秒) (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onDisconnected:727)
2025-08-21 11:15:24.660  4396-4396  WebSocket               com.dspread.mdm.service              I  🔧 重连 WebSocket: wss://api.dspreadserv.net/status/websocket/register?serialNo=MDEzNTQwOTAyMDI1MDMwNTAzOTk=&publicKey=TUlHZk1BMEdDU3FHU0liM0RRRUJBUVVBQTRHTkFEQ0JpUUtCZ1FENjVsY3lJc2p6b0swWFpMZ2RUQmpOaCtKVDhpdGNlUHp1eVVMUGVyS281VzRxWjZRUy9QZS9zWU1PdG04RzhoZk5pSzZEN0xKQnZ2V243RW1nVTdVM2x3YjR4VXI3SS84V1hFZEFBWW1WNVlTdzdOenBQYkFaTUxSNVFmazBiK2lvcDZxaTlkeFpTazFsOGhlL0E3ZEV2MC92ZnNmRTU4a2k5UzRLVVRMVUhRSURBUUFC&query=0&msgVer=3&timestamp=1755746124649&signature=jo3OVI83Xd2Flps8oLtiIQr2FxN+ojThVeEkHytxeQCJMNCkb7FBlgqmILMjHO1ys81ttwIN0cq+SuxaKsfp7DRAMTenoZ3pZUnaIf16+fcI2zf5jjRh4s4cln4iWW/3Qq5oxPrWisvGIZL8Jmt3mhT87t7LZPb03SkS6kumPFM=
2025-08-21 11:15:24.666  4396-4396  WebSocket               com.dspread.mdm.service              I  🔧 未检测到系统代理设置
2025-08-21 11:15:24.726  4396-9707  TrafficStats            com.dspread.mdm.service              D  tagSocket(115) with statsTag=0xffffffff, statsUid=-1
2025-08-21 11:15:24.977  4396-9708  TrafficStats            com.dspread.mdm.service              D  tagSocket(118) with statsTag=0xffffffff, statsUid=-1
2025-08-21 11:15:28.936  4396-9709  Common                  com.dspread.mdm.service              I  ✅ WebSocket 物理连接成功，等待服务器 hellMsg
2025-08-21 11:15:28.945  4396-9709  WebSocket               com.dspread.mdm.service              I  🔧 ===== 心跳发送器实际配置 =====
2025-08-21 11:15:28.949  4396-9709  WebSocket               com.dspread.mdm.service              I  🔧 实际 PING 发送间隔: 30000ms (30秒)
2025-08-21 11:15:28.954  4396-9709  WebSocket               com.dspread.mdm.service              I  🔧 PING 定时器名称: PingSender
2025-08-21 11:15:28.958  4396-9709  WebSocket               com.dspread.mdm.service              I  🔧 实际 PONG 发送间隔: 0ms (0秒)
2025-08-21 11:15:28.962  4396-9709  WebSocket               com.dspread.mdm.service              I  🔧 PONG 定时器名称: PongSender
2025-08-21 11:15:28.967  4396-9709  WebSocket               com.dspread.mdm.service              I  🔧 ===============================
2025-08-21 11:15:28.971  4396-9709  WebSocket               com.dspread.mdm.service              I  🔧 启动PING/PONG检测，阈值: 连续3个PING无PONG响应
2025-08-21 11:15:28.988  4396-9709  Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"hellMsg":"hello 01354090202503050399","tranCode":"S0000","version":"1","rebootTime":"01:10:48","serialNo":"01354090202503050399","deviceStatus":"6"}
2025-08-21 11:15:28.992  4396-9709  Common                  com.dspread.mdm.service              I  ✅ 收到服务器 hellMsg，WebSocket 业务连接成功！
2025-08-21 11:15:28.995  4396-9709  Common                  com.dspread.mdm.service              I  ✅ WebSocket 业务连接成功！
2025-08-21 11:15:28.999  4396-9709  WebSocket               com.dspread.mdm.service              I  🔧 业务连接成功，重连计数已重置为 0
2025-08-21 11:15:29.002  4396-9709  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接成功，检查自身更新任务状态
2025-08-21 11:15:29.009  4396-9709  Receiver                com.dspread.mdm.service              D  🔧 发送广播: com.dspread.mdm.service.CHECK_SELF_UPDATE_STATUS
2025-08-21 11:15:29.013  4396-9709  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接成功
2025-08-21 11:15:29.016  4396-9709  WebSocket               com.dspread.mdm.service              I  🔧 设备状态: 6
2025-08-21 11:15:29.020  4396-9709  WebSocket               com.dspread.mdm.service              I  🔧 检查首次连接标志: isFirstTimeWebSocketConnected=false
2025-08-21 11:15:29.023  4396-9709  WebSocket               com.dspread.mdm.service              I  🔧 非首次连接，跳过初始化信息发送和初始化检查
2025-08-21 11:15:29.027  4396-9709  WebSocket               com.dspread.mdm.service              I  🔧 执行必要的状态同步检查
2025-08-21 11:15:29.030  4396-9709  WebSocket               com.dspread.mdm.service              I  🔧 检查服务更新结果
2025-08-21 11:15:29.034  4396-9709  Task                    com.dspread.mdm.service              W  ⚠️ 任务列表为空，跳过服务更新结果检查
2025-08-21 11:15:29.037  4396-9709  WebSocket               com.dspread.mdm.service              I  🔧 检查OS升级状态...
2025-08-21 11:15:29.041  4396-9709  Common                  com.dspread.mdm.service              D  🔧 OsUpdateStatusChecker WebSocket连接成功，检查OS升级状态
2025-08-21 11:15:29.044  4396-9709  Common                  com.dspread.mdm.service              D  🔧 OsUpdateStatusChecker 没有待检查的OS升级任务
2025-08-21 11:15:29.048  4396-9709  WebSocket               com.dspread.mdm.service              I  🔧 OS升级状态检查已触发
2025-08-21 11:16:28.250  4396-9709  WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误 (Ask Gemini)
                                                                                                    com.neovisionaries.ws.client.NoMoreFrameException: No more WebSocket frame from the server.
                                                                                                         at com.neovisionaries.ws.client.WebSocketInputStream.readFrame(WebSocketInputStream.java:54)
                                                                                                         at com.neovisionaries.ws.client.ReadingThread.readFrame(ReadingThread.java:338)
                                                                                                         at com.neovisionaries.ws.client.ReadingThread.main(ReadingThread.java:99)
                                                                                                         at com.neovisionaries.ws.client.ReadingThread.runMain(ReadingThread.java:64)
                                                                                                         at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45)
2025-08-21 11:16:28.257  4396-9709  WebSocket               com.dspread.mdm.service              I  🔧 onError: 不触发重连，等待 onDisconnected 处理
2025-08-21 11:16:28.271  4396-9710  WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误 (Ask Gemini)
                                                                                                    com.neovisionaries.ws.client.WebSocketException: Flushing frames to the server failed: Socket closed
                                                                                                         at com.neovisionaries.ws.client.WritingThread.doFlush(WritingThread.java:434)
                                                                                                         at com.neovisionaries.ws.client.WritingThread.sendFrames(WritingThread.java:384)
                                                                                                         at com.neovisionaries.ws.client.WritingThread.main(WritingThread.java:108)
                                                                                                         at com.neovisionaries.ws.client.WritingThread.runMain(WritingThread.java:55)
                                                                                                         at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45)
                                                                                                    Caused by: java.net.SocketException: Socket closed
                                                                                                         at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.writeInternal(ConscryptEngineSocket.java:728)
                                                                                                         at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.write(ConscryptEngineSocket.java:699)
                                                                                                         at java.io.BufferedOutputStream.flushBuffer(BufferedOutputStream.java:81)
                                                                                                         at java.io.BufferedOutputStream.flush(BufferedOutputStream.java:142)
                                                                                                         at java.io.BufferedOutputStream.flush(BufferedOutputStream.java:143)
                                                                                                         at com.neovisionaries.ws.client.WritingThread.flush(WritingThread.java:271)
                                                                                                         at com.neovisionaries.ws.client.WritingThread.doFlush(WritingThread.java:422)
                                                                                                         at com.neovisionaries.ws.client.WritingThread.sendFrames(WritingThread.java:384) 
                                                                                                         at com.neovisionaries.ws.client.WritingThread.main(WritingThread.java:108) 
                                                                                                         at com.neovisionaries.ws.client.WritingThread.runMain(WritingThread.java:55) 
                                                                                                         at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45) 
2025-08-21 11:16:28.277  4396-9710  WebSocket               com.dspread.mdm.service              I  🔧 onError: 不触发重连，等待 onDisconnected 处理
2025-08-21 11:16:28.287  4396-9710  WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误 (Ask Gemini)
                                                                                                    com.neovisionaries.ws.client.WebSocketException: Flushing frames to the server failed: Socket closed
                                                                                                         at com.neovisionaries.ws.client.WritingThread.doFlush(WritingThread.java:434)
                                                                                                         at com.neovisionaries.ws.client.WritingThread.sendFrames(WritingThread.java:384)
                                                                                                         at com.neovisionaries.ws.client.WritingThread.main(WritingThread.java:120)
                                                                                                         at com.neovisionaries.ws.client.WritingThread.runMain(WritingThread.java:55)
                                                                                                         at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45)
                                                                                                    Caused by: java.net.SocketException: Socket closed
                                                                                                         at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.writeInternal(ConscryptEngineSocket.java:728)
                                                                                                         at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.write(ConscryptEngineSocket.java:699)
                                                                                                         at java.io.BufferedOutputStream.flushBuffer(BufferedOutputStream.java:81)
                                                                                                         at java.io.BufferedOutputStream.flush(BufferedOutputStream.java:142)
                                                                                                         at java.io.BufferedOutputStream.flush(BufferedOutputStream.java:143)
                                                                                                         at com.neovisionaries.ws.client.WritingThread.flush(WritingThread.java:271)
                                                                                                         at com.neovisionaries.ws.client.WritingThread.doFlush(WritingThread.java:422)
                                                                                                         at com.neovisionaries.ws.client.WritingThread.sendFrames(WritingThread.java:384) 
                                                                                                         at com.neovisionaries.ws.client.WritingThread.main(WritingThread.java:120) 
                                                                                                         at com.neovisionaries.ws.client.WritingThread.runMain(WritingThread.java:55) 
                                                                                                         at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45) 
2025-08-21 11:16:28.294  4396-9710  WebSocket               com.dspread.mdm.service              I  🔧 onError: 不触发重连，等待 onDisconnected 处理
2025-08-21 11:16:28.307  4396-9710  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接断开 - 客户端主动断开
2025-08-21 11:16:28.314  4396-9710  WebSocket               com.dspread.mdm.service              I  🔧    服务器关闭帧: code=-1, reason=无原因
2025-08-21 11:16:28.319  4396-9710  WebSocket               com.dspread.mdm.service              I  🔧    客户端关闭帧: code=1002, reason=No more WebSocket frame from the server.
2025-08-21 11:16:28.324  4396-9710  WebSocket               com.dspread.mdm.service              I  🔧    断开发起方: 客户端
2025-08-21 11:16:28.329  4396-9710  WebSocket               com.dspread.mdm.service              I  🔧 停止PING/PONG检测
2025-08-21 11:16:28.334  4396-9710  WebSocket               com.dspread.mdm.service              I  🔧 reconnect() 被调用，调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onDisconnected:727
2025-08-21 11:16:28.339  4396-9710  WebSocket               com.dspread.mdm.service              I  🔧 开始重连 WebSocket... (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onDisconnected:727)
2025-08-21 11:16:28.349  4396-9710  WebSocket               com.dspread.mdm.service              I  🔧 使用Provisioning配置的重连策略: delayPolicy=1, delaySwitch=1, delayTime=60
2025-08-21 11:16:28.354  4396-9710  WebSocket               com.dspread.mdm.service              I  🔧 当前断开记录数: 10, 延迟开关: 1
2025-08-21 11:16:28.362  4396-9710  WebSocket               com.dspread.mdm.service              I  🔧 断开记录 最早一次断开: 2025-08-21 10:59:13
2025-08-21 11:16:28.370  4396-9710  WebSocket               com.dspread.mdm.service              I  🔧 断开记录 最后一次断开: 2025-08-21 11:16:28
2025-08-21 11:16:28.374  4396-9710  WebSocket               com.dspread.mdm.service              I  🔧 时间差: 17分钟 (配置阈值: 1分钟)
2025-08-21 11:16:28.378  4396-9710  WebSocket               com.dspread.mdm.service              I  🔧 断开间隔正常，使用标准重连策略
2025-08-21 11:16:28.382  4396-9710  WebSocket               com.dspread.mdm.service              I  🔧 重连策略计算完成: 第1次重连，间隔3000ms (3秒)
2025-08-21 11:16:28.386  4396-9710  WebSocket               com.dspread.mdm.service              I  🔧 开始第1次重连，间隔3000ms (3秒) (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onDisconnected:727)
2025-08-21 11:17:27.199  4396-4396  WebSocket               com.dspread.mdm.service              I  🔧 重连 WebSocket: wss://api.dspreadserv.net/status/websocket/register?serialNo=MDEzNTQwOTAyMDI1MDMwNTAzOTk=&publicKey=TUlHZk1BMEdDU3FHU0liM0RRRUJBUVVBQTRHTkFEQ0JpUUtCZ1FENjVsY3lJc2p6b0swWFpMZ2RUQmpOaCtKVDhpdGNlUHp1eVVMUGVyS281VzRxWjZRUy9QZS9zWU1PdG04RzhoZk5pSzZEN0xKQnZ2V243RW1nVTdVM2x3YjR4VXI3SS84V1hFZEFBWW1WNVlTdzdOenBQYkFaTUxSNVFmazBiK2lvcDZxaTlkeFpTazFsOGhlL0E3ZEV2MC92ZnNmRTU4a2k5UzRLVVRMVUhRSURBUUFC&query=0&msgVer=3&timestamp=1755746247184&signature=6SK2Ub/DmouuWr+xqYrZ46XGvoRgLTHOxuoqJv57E50sgz56fBQBUR5F3hZPIlO2n6dsm6GAMJUor8kNqBb4p2mkjb6OQDUDf5RyFpmLdtAQzJSnLlEgLvEEosmz+SNXzaTh/EKRQU00uSizPe31t5zGlq5K1lsfB7xqSnNVi1Q=
2025-08-21 11:17:27.206  4396-4396  WebSocket               com.dspread.mdm.service              I  🔧 未检测到系统代理设置
2025-08-21 11:17:27.246  4396-9715  TrafficStats            com.dspread.mdm.service              D  tagSocket(115) with statsTag=0xffffffff, statsUid=-1
2025-08-21 11:17:27.498  4396-9716  TrafficStats            com.dspread.mdm.service              D  tagSocket(123) with statsTag=0xffffffff, statsUid=-1
2025-08-21 11:17:31.796  4396-9717  Common                  com.dspread.mdm.service              I  ✅ WebSocket 物理连接成功，等待服务器 hellMsg
2025-08-21 11:17:31.805  4396-9717  WebSocket               com.dspread.mdm.service              I  🔧 ===== 心跳发送器实际配置 =====
2025-08-21 11:17:31.812  4396-9717  WebSocket               com.dspread.mdm.service              I  🔧 实际 PING 发送间隔: 30000ms (30秒)
2025-08-21 11:17:31.818  4396-9717  WebSocket               com.dspread.mdm.service              I  🔧 PING 定时器名称: PingSender
2025-08-21 11:17:31.828  4396-9717  WebSocket               com.dspread.mdm.service              I  🔧 实际 PONG 发送间隔: 0ms (0秒)
2025-08-21 11:17:31.834  4396-9717  WebSocket               com.dspread.mdm.service              I  🔧 PONG 定时器名称: PongSender
2025-08-21 11:17:31.840  4396-9717  WebSocket               com.dspread.mdm.service              I  🔧 ===============================
2025-08-21 11:17:31.845  4396-9717  WebSocket               com.dspread.mdm.service              I  🔧 启动PING/PONG检测，阈值: 连续3个PING无PONG响应
2025-08-21 11:17:31.848  4396-4407  System                  com.dspread.mdm.service              W  A resource failed to call close. 
2025-08-21 11:17:31.851  4396-4407  System                  com.dspread.mdm.service              W  A resource failed to call close. 
2025-08-21 11:17:31.855  4396-4407  System                  com.dspread.mdm.service              W  A resource failed to call close. 
2025-08-21 11:17:31.864  4396-9717  Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"hellMsg":"hello 01354090202503050399","tranCode":"S0000","version":"1","rebootTime":"02:44:32","serialNo":"01354090202503050399","deviceStatus":"6"}
2025-08-21 11:17:31.868  4396-9717  Common                  com.dspread.mdm.service              I  ✅ 收到服务器 hellMsg，WebSocket 业务连接成功！
2025-08-21 11:17:31.872  4396-9717  Common                  com.dspread.mdm.service              I  ✅ WebSocket 业务连接成功！
2025-08-21 11:17:31.876  4396-9717  WebSocket               com.dspread.mdm.service              I  🔧 业务连接成功，重连计数已重置为 0
2025-08-21 11:17:31.879  4396-9717  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接成功，检查自身更新任务状态
2025-08-21 11:17:31.885  4396-9717  Receiver                com.dspread.mdm.service              D  🔧 发送广播: com.dspread.mdm.service.CHECK_SELF_UPDATE_STATUS
2025-08-21 11:17:31.889  4396-9717  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接成功
2025-08-21 11:17:31.893  4396-9717  WebSocket               com.dspread.mdm.service              I  🔧 设备状态: 6
2025-08-21 11:17:31.896  4396-9717  WebSocket               com.dspread.mdm.service              I  🔧 检查首次连接标志: isFirstTimeWebSocketConnected=false
2025-08-21 11:17:31.900  4396-9717  WebSocket               com.dspread.mdm.service              I  🔧 非首次连接，跳过初始化信息发送和初始化检查
2025-08-21 11:17:31.903  4396-9717  WebSocket               com.dspread.mdm.service              I  🔧 执行必要的状态同步检查
2025-08-21 11:17:31.907  4396-9717  WebSocket               com.dspread.mdm.service              I  🔧 检查服务更新结果
2025-08-21 11:17:31.910  4396-9717  Task                    com.dspread.mdm.service              W  ⚠️ 任务列表为空，跳过服务更新结果检查
2025-08-21 11:17:31.914  4396-9717  WebSocket               com.dspread.mdm.service              I  🔧 检查OS升级状态...
2025-08-21 11:17:31.917  4396-9717  Common                  com.dspread.mdm.service              D  🔧 OsUpdateStatusChecker WebSocket连接成功，检查OS升级状态
2025-08-21 11:17:31.921  4396-9717  Common                  com.dspread.mdm.service              D  🔧 OsUpdateStatusChecker 没有待检查的OS升级任务
2025-08-21 11:17:31.925  4396-9717  WebSocket               com.dspread.mdm.service              I  🔧 OS升级状态检查已触发
2025-08-21 11:18:31.405  4396-9717  WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误 (Ask Gemini)
                                                                                                    com.neovisionaries.ws.client.NoMoreFrameException: No more WebSocket frame from the server.
                                                                                                         at com.neovisionaries.ws.client.WebSocketInputStream.readFrame(WebSocketInputStream.java:54)
                                                                                                         at com.neovisionaries.ws.client.ReadingThread.readFrame(ReadingThread.java:338)
                                                                                                         at com.neovisionaries.ws.client.ReadingThread.main(ReadingThread.java:99)
                                                                                                         at com.neovisionaries.ws.client.ReadingThread.runMain(ReadingThread.java:64)
                                                                                                         at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45)
2025-08-21 11:18:31.418  4396-9717  WebSocket               com.dspread.mdm.service              I  🔧 onError: 不触发重连，等待 onDisconnected 处理
2025-08-21 11:18:31.444  4396-9718  WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误 (Ask Gemini)
                                                                                                    com.neovisionaries.ws.client.WebSocketException: Flushing frames to the server failed: Socket closed
                                                                                                         at com.neovisionaries.ws.client.WritingThread.doFlush(WritingThread.java:434)
                                                                                                         at com.neovisionaries.ws.client.WritingThread.sendFrames(WritingThread.java:384)
                                                                                                         at com.neovisionaries.ws.client.WritingThread.main(WritingThread.java:108)
                                                                                                         at com.neovisionaries.ws.client.WritingThread.runMain(WritingThread.java:55)
                                                                                                         at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45)
                                                                                                    Caused by: java.net.SocketException: Socket closed
                                                                                                         at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.writeInternal(ConscryptEngineSocket.java:728)
                                                                                                         at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.write(ConscryptEngineSocket.java:699)
                                                                                                         at java.io.BufferedOutputStream.flushBuffer(BufferedOutputStream.java:81)
                                                                                                         at java.io.BufferedOutputStream.flush(BufferedOutputStream.java:142)
                                                                                                         at java.io.BufferedOutputStream.flush(BufferedOutputStream.java:143)
                                                                                                         at com.neovisionaries.ws.client.WritingThread.flush(WritingThread.java:271)
                                                                                                         at com.neovisionaries.ws.client.WritingThread.doFlush(WritingThread.java:422)
                                                                                                         at com.neovisionaries.ws.client.WritingThread.sendFrames(WritingThread.java:384) 
                                                                                                         at com.neovisionaries.ws.client.WritingThread.main(WritingThread.java:108) 
                                                                                                         at com.neovisionaries.ws.client.WritingThread.runMain(WritingThread.java:55) 
                                                                                                         at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45) 
2025-08-21 11:18:31.451  4396-9718  WebSocket               com.dspread.mdm.service              I  🔧 onError: 不触发重连，等待 onDisconnected 处理
2025-08-21 11:18:31.460  4396-9718  WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误 (Ask Gemini)
                                                                                                    com.neovisionaries.ws.client.WebSocketException: Flushing frames to the server failed: Socket closed
                                                                                                         at com.neovisionaries.ws.client.WritingThread.doFlush(WritingThread.java:434)
                                                                                                         at com.neovisionaries.ws.client.WritingThread.sendFrames(WritingThread.java:384)
                                                                                                         at com.neovisionaries.ws.client.WritingThread.main(WritingThread.java:120)
                                                                                                         at com.neovisionaries.ws.client.WritingThread.runMain(WritingThread.java:55)
                                                                                                         at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45)
                                                                                                    Caused by: java.net.SocketException: Socket closed
                                                                                                         at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.writeInternal(ConscryptEngineSocket.java:728)
                                                                                                         at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.write(ConscryptEngineSocket.java:699)
                                                                                                         at java.io.BufferedOutputStream.flushBuffer(BufferedOutputStream.java:81)
                                                                                                         at java.io.BufferedOutputStream.flush(BufferedOutputStream.java:142)
                                                                                                         at java.io.BufferedOutputStream.flush(BufferedOutputStream.java:143)
                                                                                                         at com.neovisionaries.ws.client.WritingThread.flush(WritingThread.java:271)
                                                                                                         at com.neovisionaries.ws.client.WritingThread.doFlush(WritingThread.java:422)
                                                                                                         at com.neovisionaries.ws.client.WritingThread.sendFrames(WritingThread.java:384) 
                                                                                                         at com.neovisionaries.ws.client.WritingThread.main(WritingThread.java:120) 
                                                                                                         at com.neovisionaries.ws.client.WritingThread.runMain(WritingThread.java:55) 
                                                                                                         at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45) 
2025-08-21 11:18:31.466  4396-9718  WebSocket               com.dspread.mdm.service              I  🔧 onError: 不触发重连，等待 onDisconnected 处理
2025-08-21 11:18:31.473  4396-9718  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接断开 - 客户端主动断开
2025-08-21 11:18:31.478  4396-9718  WebSocket               com.dspread.mdm.service              I  🔧    服务器关闭帧: code=-1, reason=无原因
2025-08-21 11:18:31.483  4396-9718  WebSocket               com.dspread.mdm.service              I  🔧    客户端关闭帧: code=1002, reason=No more WebSocket frame from the server.
2025-08-21 11:18:31.487  4396-9718  WebSocket               com.dspread.mdm.service              I  🔧    断开发起方: 客户端
2025-08-21 11:18:31.491  4396-9718  WebSocket               com.dspread.mdm.service              I  🔧 停止PING/PONG检测
2025-08-21 11:18:31.496  4396-9718  WebSocket               com.dspread.mdm.service              I  🔧 reconnect() 被调用，调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onDisconnected:727
2025-08-21 11:18:31.500  4396-9718  WebSocket               com.dspread.mdm.service              I  🔧 开始重连 WebSocket... (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onDisconnected:727)
2025-08-21 11:18:31.508  4396-9718  WebSocket               com.dspread.mdm.service              I  🔧 使用Provisioning配置的重连策略: delayPolicy=1, delaySwitch=1, delayTime=60
2025-08-21 11:18:31.512  4396-9718  WebSocket               com.dspread.mdm.service              I  🔧 当前断开记录数: 10, 延迟开关: 1
2025-08-21 11:18:31.519  4396-9718  WebSocket               com.dspread.mdm.service              I  🔧 断开记录 最早一次断开: 2025-08-21 11:01:05
2025-08-21 11:18:31.527  4396-9718  WebSocket               com.dspread.mdm.service              I  🔧 断开记录 最后一次断开: 2025-08-21 11:18:31
2025-08-21 11:18:31.530  4396-9718  WebSocket               com.dspread.mdm.service              I  🔧 时间差: 17分钟 (配置阈值: 1分钟)
2025-08-21 11:18:31.534  4396-9718  WebSocket               com.dspread.mdm.service              I  🔧 断开间隔正常，使用标准重连策略
2025-08-21 11:18:31.537  4396-9718  WebSocket               com.dspread.mdm.service              I  🔧 重连策略计算完成: 第1次重连，间隔3000ms (3秒)
2025-08-21 11:18:31.541  4396-9718  WebSocket               com.dspread.mdm.service              I  🔧 开始第1次重连，间隔3000ms (3秒) (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onDisconnected:727)
2025-08-21 11:19:29.235  4396-4396  WebSocket               com.dspread.mdm.service              I  🔧 重连 WebSocket: wss://api.dspreadserv.net/status/websocket/register?serialNo=MDEzNTQwOTAyMDI1MDMwNTAzOTk=&publicKey=TUlHZk1BMEdDU3FHU0liM0RRRUJBUVVBQTRHTkFEQ0JpUUtCZ1FENjVsY3lJc2p6b0swWFpMZ2RUQmpOaCtKVDhpdGNlUHp1eVVMUGVyS281VzRxWjZRUy9QZS9zWU1PdG04RzhoZk5pSzZEN0xKQnZ2V243RW1nVTdVM2x3YjR4VXI3SS84V1hFZEFBWW1WNVlTdzdOenBQYkFaTUxSNVFmazBiK2lvcDZxaTlkeFpTazFsOGhlL0E3ZEV2MC92ZnNmRTU4a2k5UzRLVVRMVUhRSURBUUFC&query=0&msgVer=3&timestamp=1755746369216&signature=7H8KwUFildxOGqE0pVVNoJsfISR6lPZxEBFLj9BkAdugc7iYTfe6xV2czO/vPUA5u+34ZFjqkA4n2rwta4J5wP2zZZZEjffBu6r7AugB+FoRzWmqreNQiTiPYB4ajXuKyOrsnbwtQ1VuRZn5CK0nJpRxSv3ZO/ttyX+Q/exK+AU=
2025-08-21 11:19:29.240  4396-4396  WebSocket               com.dspread.mdm.service              I  🔧 未检测到系统代理设置
2025-08-21 11:19:29.299  4396-9723  TrafficStats            com.dspread.mdm.service              D  tagSocket(111) with statsTag=0xffffffff, statsUid=-1
2025-08-21 11:19:29.552  4396-9724  TrafficStats            com.dspread.mdm.service              D  tagSocket(115) with statsTag=0xffffffff, statsUid=-1
2025-08-21 11:19:31.144  4396-9728  Common                  com.dspread.mdm.service              I  ✅ WebSocket 物理连接成功，等待服务器 hellMsg
2025-08-21 11:19:31.149  4396-9728  WebSocket               com.dspread.mdm.service              I  🔧 ===== 心跳发送器实际配置 =====
2025-08-21 11:19:31.154  4396-9728  WebSocket               com.dspread.mdm.service              I  🔧 实际 PING 发送间隔: 30000ms (30秒)
2025-08-21 11:19:31.165  4396-9728  WebSocket               com.dspread.mdm.service              I  🔧 PING 定时器名称: PingSender
2025-08-21 11:19:31.171  4396-9728  WebSocket               com.dspread.mdm.service              I  🔧 实际 PONG 发送间隔: 0ms (0秒)
2025-08-21 11:19:31.176  4396-9728  WebSocket               com.dspread.mdm.service              I  🔧 PONG 定时器名称: PongSender
2025-08-21 11:19:31.181  4396-9728  WebSocket               com.dspread.mdm.service              I  🔧 ===============================
2025-08-21 11:19:31.186  4396-9728  WebSocket               com.dspread.mdm.service              I  🔧 启动PING/PONG检测，阈值: 连续3个PING无PONG响应
2025-08-21 11:19:31.207  4396-9728  Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"hellMsg":"hello 01354090202503050399","tranCode":"S0000","version":"1","rebootTime":"03:40:03","serialNo":"01354090202503050399","deviceStatus":"6"}
2025-08-21 11:19:31.212  4396-9728  Common                  com.dspread.mdm.service              I  ✅ 收到服务器 hellMsg，WebSocket 业务连接成功！
2025-08-21 11:19:31.216  4396-9728  Common                  com.dspread.mdm.service              I  ✅ WebSocket 业务连接成功！
2025-08-21 11:19:31.220  4396-9728  WebSocket               com.dspread.mdm.service              I  🔧 业务连接成功，重连计数已重置为 0
2025-08-21 11:19:31.224  4396-9728  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接成功，检查自身更新任务状态
2025-08-21 11:19:31.230  4396-9728  Receiver                com.dspread.mdm.service              D  🔧 发送广播: com.dspread.mdm.service.CHECK_SELF_UPDATE_STATUS
2025-08-21 11:19:31.234  4396-9728  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接成功
2025-08-21 11:19:31.237  4396-9728  WebSocket               com.dspread.mdm.service              I  🔧 设备状态: 6
2025-08-21 11:19:31.241  4396-9728  WebSocket               com.dspread.mdm.service              I  🔧 检查首次连接标志: isFirstTimeWebSocketConnected=false
2025-08-21 11:19:31.244  4396-9728  WebSocket               com.dspread.mdm.service              I  🔧 非首次连接，跳过初始化信息发送和初始化检查
2025-08-21 11:19:31.248  4396-9728  WebSocket               com.dspread.mdm.service              I  🔧 执行必要的状态同步检查
2025-08-21 11:19:31.252  4396-9728  WebSocket               com.dspread.mdm.service              I  🔧 检查服务更新结果
2025-08-21 11:19:31.256  4396-9728  Task                    com.dspread.mdm.service              W  ⚠️ 任务列表为空，跳过服务更新结果检查
2025-08-21 11:19:31.259  4396-9728  WebSocket               com.dspread.mdm.service              I  🔧 检查OS升级状态...
2025-08-21 11:19:31.263  4396-9728  Common                  com.dspread.mdm.service              D  🔧 OsUpdateStatusChecker WebSocket连接成功，检查OS升级状态
2025-08-21 11:19:31.266  4396-9728  Common                  com.dspread.mdm.service              D  🔧 OsUpdateStatusChecker 没有待检查的OS升级任务
2025-08-21 11:19:31.270  4396-9728  WebSocket               com.dspread.mdm.service              I  🔧 OS升级状态检查已触发
2025-08-21 11:20:31.247  4396-9728  WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误 (Ask Gemini)
                                                                                                    com.neovisionaries.ws.client.NoMoreFrameException: No more WebSocket frame from the server.
                                                                                                         at com.neovisionaries.ws.client.WebSocketInputStream.readFrame(WebSocketInputStream.java:54)
                                                                                                         at com.neovisionaries.ws.client.ReadingThread.readFrame(ReadingThread.java:338)
                                                                                                         at com.neovisionaries.ws.client.ReadingThread.main(ReadingThread.java:99)
                                                                                                         at com.neovisionaries.ws.client.ReadingThread.runMain(ReadingThread.java:64)
                                                                                                         at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45)
2025-08-21 11:20:31.255  4396-9728  WebSocket               com.dspread.mdm.service              I  🔧 onError: 不触发重连，等待 onDisconnected 处理
2025-08-21 11:20:31.268  4396-9729  WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误 (Ask Gemini)
                                                                                                    com.neovisionaries.ws.client.WebSocketException: Flushing frames to the server failed: Socket closed
                                                                                                         at com.neovisionaries.ws.client.WritingThread.doFlush(WritingThread.java:434)
                                                                                                         at com.neovisionaries.ws.client.WritingThread.sendFrames(WritingThread.java:384)
                                                                                                         at com.neovisionaries.ws.client.WritingThread.main(WritingThread.java:108)
                                                                                                         at com.neovisionaries.ws.client.WritingThread.runMain(WritingThread.java:55)
                                                                                                         at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45)
                                                                                                    Caused by: java.net.SocketException: Socket closed
                                                                                                         at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.writeInternal(ConscryptEngineSocket.java:728)
                                                                                                         at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.write(ConscryptEngineSocket.java:699)
                                                                                                         at java.io.BufferedOutputStream.flushBuffer(BufferedOutputStream.java:81)
                                                                                                         at java.io.BufferedOutputStream.flush(BufferedOutputStream.java:142)
                                                                                                         at java.io.BufferedOutputStream.flush(BufferedOutputStream.java:143)
                                                                                                         at com.neovisionaries.ws.client.WritingThread.flush(WritingThread.java:271)
                                                                                                         at com.neovisionaries.ws.client.WritingThread.doFlush(WritingThread.java:422)
                                                                                                         at com.neovisionaries.ws.client.WritingThread.sendFrames(WritingThread.java:384) 
                                                                                                         at com.neovisionaries.ws.client.WritingThread.main(WritingThread.java:108) 
                                                                                                         at com.neovisionaries.ws.client.WritingThread.runMain(WritingThread.java:55) 
                                                                                                         at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45) 
2025-08-21 11:20:31.275  4396-9729  WebSocket               com.dspread.mdm.service              I  🔧 onError: 不触发重连，等待 onDisconnected 处理
2025-08-21 11:20:31.285  4396-9729  WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误 (Ask Gemini)
                                                                                                    com.neovisionaries.ws.client.WebSocketException: Flushing frames to the server failed: Socket closed
                                                                                                         at com.neovisionaries.ws.client.WritingThread.doFlush(WritingThread.java:434)
                                                                                                         at com.neovisionaries.ws.client.WritingThread.sendFrames(WritingThread.java:384)
                                                                                                         at com.neovisionaries.ws.client.WritingThread.main(WritingThread.java:120)
                                                                                                         at com.neovisionaries.ws.client.WritingThread.runMain(WritingThread.java:55)
                                                                                                         at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45)
                                                                                                    Caused by: java.net.SocketException: Socket closed
                                                                                                         at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.writeInternal(ConscryptEngineSocket.java:728)
                                                                                                         at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.write(ConscryptEngineSocket.java:699)
                                                                                                         at java.io.BufferedOutputStream.flushBuffer(BufferedOutputStream.java:81)
                                                                                                         at java.io.BufferedOutputStream.flush(BufferedOutputStream.java:142)
                                                                                                         at java.io.BufferedOutputStream.flush(BufferedOutputStream.java:143)
                                                                                                         at com.neovisionaries.ws.client.WritingThread.flush(WritingThread.java:271)
                                                                                                         at com.neovisionaries.ws.client.WritingThread.doFlush(WritingThread.java:422)
                                                                                                         at com.neovisionaries.ws.client.WritingThread.sendFrames(WritingThread.java:384) 
                                                                                                         at com.neovisionaries.ws.client.WritingThread.main(WritingThread.java:120) 
                                                                                                         at com.neovisionaries.ws.client.WritingThread.runMain(WritingThread.java:55) 
                                                                                                         at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45) 
2025-08-21 11:20:31.292  4396-9729  WebSocket               com.dspread.mdm.service              I  🔧 onError: 不触发重连，等待 onDisconnected 处理
2025-08-21 11:20:31.301  4396-9729  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接断开 - 客户端主动断开
2025-08-21 11:20:31.307  4396-9729  WebSocket               com.dspread.mdm.service              I  🔧    服务器关闭帧: code=-1, reason=无原因
2025-08-21 11:20:31.313  4396-9729  WebSocket               com.dspread.mdm.service              I  🔧    客户端关闭帧: code=1002, reason=No more WebSocket frame from the server.
2025-08-21 11:20:31.319  4396-9729  WebSocket               com.dspread.mdm.service              I  🔧    断开发起方: 客户端
2025-08-21 11:20:31.324  4396-9729  WebSocket               com.dspread.mdm.service              I  🔧 停止PING/PONG检测
2025-08-21 11:20:31.329  4396-9729  WebSocket               com.dspread.mdm.service              I  🔧 reconnect() 被调用，调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onDisconnected:727
2025-08-21 11:20:31.334  4396-9729  WebSocket               com.dspread.mdm.service              I  🔧 开始重连 WebSocket... (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onDisconnected:727)
2025-08-21 11:20:31.344  4396-9729  WebSocket               com.dspread.mdm.service              I  🔧 使用Provisioning配置的重连策略: delayPolicy=1, delaySwitch=1, delayTime=60
2025-08-21 11:20:31.348  4396-9729  WebSocket               com.dspread.mdm.service              I  🔧 当前断开记录数: 10, 延迟开关: 1
2025-08-21 11:20:31.356  4396-9729  WebSocket               com.dspread.mdm.service              I  🔧 断开记录 最早一次断开: 2025-08-21 11:02:11
2025-08-21 11:20:31.364  4396-9729  WebSocket               com.dspread.mdm.service              I  🔧 断开记录 最后一次断开: 2025-08-21 11:20:31
2025-08-21 11:20:31.368  4396-9729  WebSocket               com.dspread.mdm.service              I  🔧 时间差: 18分钟 (配置阈值: 1分钟)
2025-08-21 11:20:31.372  4396-9729  WebSocket               com.dspread.mdm.service              I  🔧 断开间隔正常，使用标准重连策略
2025-08-21 11:20:31.375  4396-9729  WebSocket               com.dspread.mdm.service              I  🔧 重连策略计算完成: 第1次重连，间隔3000ms (3秒)
2025-08-21 11:20:31.379  4396-9729  WebSocket               com.dspread.mdm.service              I  🔧 开始第1次重连，间隔3000ms (3秒) (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onDisconnected:727)
2025-08-21 11:21:31.228  4396-4396  WebSocket               com.dspread.mdm.service              I  🔧 重连 WebSocket: wss://api.dspreadserv.net/status/websocket/register?serialNo=MDEzNTQwOTAyMDI1MDMwNTAzOTk=&publicKey=TUlHZk1BMEdDU3FHU0liM0RRRUJBUVVBQTRHTkFEQ0JpUUtCZ1FENjVsY3lJc2p6b0swWFpMZ2RUQmpOaCtKVDhpdGNlUHp1eVVMUGVyS281VzRxWjZRUy9QZS9zWU1PdG04RzhoZk5pSzZEN0xKQnZ2V243RW1nVTdVM2x3YjR4VXI3SS84V1hFZEFBWW1WNVlTdzdOenBQYkFaTUxSNVFmazBiK2lvcDZxaTlkeFpTazFsOGhlL0E3ZEV2MC92ZnNmRTU4a2k5UzRLVVRMVUhRSURBUUFC&query=0&msgVer=3&timestamp=1755746491215&signature=w3boxHOOzGoWyvG6LgmzJmKJf5fVndMas69giRJkNHDxIlcBorbjlnTBCJeh+XH+/uLE1h1mFzs14xss83lq6qHMES0vHOjboRLVW5cGL4nvOzlUVkXB8k4/tFjTD9FKK9n2BkMXDlWSFNYyDd07KLCKSP/tsd7kO+W68GWi3uk=
2025-08-21 11:21:31.235  4396-4396  WebSocket               com.dspread.mdm.service              I  🔧 未检测到系统代理设置
2025-08-21 11:21:31.296  4396-9734  TrafficStats            com.dspread.mdm.service              D  tagSocket(111) with statsTag=0xffffffff, statsUid=-1
2025-08-21 11:21:31.548  4396-9735  TrafficStats            com.dspread.mdm.service              D  tagSocket(118) with statsTag=0xffffffff, statsUid=-1
2025-08-21 11:21:33.102  4396-9736  Common                  com.dspread.mdm.service              I  ✅ WebSocket 物理连接成功，等待服务器 hellMsg
2025-08-21 11:21:33.107  4396-9736  WebSocket               com.dspread.mdm.service              I  🔧 ===== 心跳发送器实际配置 =====
2025-08-21 11:21:33.113  4396-9736  WebSocket               com.dspread.mdm.service              I  🔧 实际 PING 发送间隔: 30000ms (30秒)
2025-08-21 11:21:33.118  4396-9736  WebSocket               com.dspread.mdm.service              I  🔧 PING 定时器名称: PingSender
2025-08-21 11:21:33.123  4396-9736  WebSocket               com.dspread.mdm.service              I  🔧 实际 PONG 发送间隔: 0ms (0秒)
2025-08-21 11:21:33.128  4396-9736  WebSocket               com.dspread.mdm.service              I  🔧 PONG 定时器名称: PongSender
2025-08-21 11:21:33.132  4396-9736  WebSocket               com.dspread.mdm.service              I  🔧 ===============================
2025-08-21 11:21:33.136  4396-9736  WebSocket               com.dspread.mdm.service              I  🔧 启动PING/PONG检测，阈值: 连续3个PING无PONG响应
2025-08-21 11:21:33.349  4396-9736  Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"hellMsg":"hello 01354090202503050399","tranCode":"S0000","version":"1","rebootTime":"03:48:18","serialNo":"01354090202503050399","deviceStatus":"6"}
2025-08-21 11:21:33.355  4396-9736  Common                  com.dspread.mdm.service              I  ✅ 收到服务器 hellMsg，WebSocket 业务连接成功！
2025-08-21 11:21:33.360  4396-9736  Common                  com.dspread.mdm.service              I  ✅ WebSocket 业务连接成功！
2025-08-21 11:21:33.365  4396-9736  WebSocket               com.dspread.mdm.service              I  🔧 业务连接成功，重连计数已重置为 0
2025-08-21 11:21:33.370  4396-9736  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接成功，检查自身更新任务状态
2025-08-21 11:21:33.378  4396-9736  Receiver                com.dspread.mdm.service              D  🔧 发送广播: com.dspread.mdm.service.CHECK_SELF_UPDATE_STATUS
2025-08-21 11:21:33.384  4396-9736  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接成功
2025-08-21 11:21:33.389  4396-9736  WebSocket               com.dspread.mdm.service              I  🔧 设备状态: 6
2025-08-21 11:21:33.393  4396-9736  WebSocket               com.dspread.mdm.service              I  🔧 检查首次连接标志: isFirstTimeWebSocketConnected=false
2025-08-21 11:21:33.398  4396-9736  WebSocket               com.dspread.mdm.service              I  🔧 非首次连接，跳过初始化信息发送和初始化检查
2025-08-21 11:21:33.402  4396-9736  WebSocket               com.dspread.mdm.service              I  🔧 执行必要的状态同步检查
2025-08-21 11:21:33.406  4396-9736  WebSocket               com.dspread.mdm.service              I  🔧 检查服务更新结果
2025-08-21 11:21:33.410  4396-9736  Task                    com.dspread.mdm.service              W  ⚠️ 任务列表为空，跳过服务更新结果检查
2025-08-21 11:21:33.414  4396-9736  WebSocket               com.dspread.mdm.service              I  🔧 检查OS升级状态...
2025-08-21 11:21:33.418  4396-9736  Common                  com.dspread.mdm.service              D  🔧 OsUpdateStatusChecker WebSocket连接成功，检查OS升级状态
2025-08-21 11:21:33.422  4396-9736  Common                  com.dspread.mdm.service              D  🔧 OsUpdateStatusChecker 没有待检查的OS升级任务
2025-08-21 11:21:33.426  4396-9736  WebSocket               com.dspread.mdm.service              I  🔧 OS升级状态检查已触发
2025-08-21 11:22:33.792  4396-9736  WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误 (Ask Gemini)
                                                                                                    com.neovisionaries.ws.client.NoMoreFrameException: No more WebSocket frame from the server.
                                                                                                         at com.neovisionaries.ws.client.WebSocketInputStream.readFrame(WebSocketInputStream.java:54)
                                                                                                         at com.neovisionaries.ws.client.ReadingThread.readFrame(ReadingThread.java:338)
                                                                                                         at com.neovisionaries.ws.client.ReadingThread.main(ReadingThread.java:99)
                                                                                                         at com.neovisionaries.ws.client.ReadingThread.runMain(ReadingThread.java:64)
                                                                                                         at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45)
2025-08-21 11:22:33.800  4396-9736  WebSocket               com.dspread.mdm.service              I  🔧 onError: 不触发重连，等待 onDisconnected 处理
2025-08-21 11:22:33.818  4396-9737  WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误 (Ask Gemini)
                                                                                                    com.neovisionaries.ws.client.WebSocketException: Flushing frames to the server failed: Socket closed
                                                                                                         at com.neovisionaries.ws.client.WritingThread.doFlush(WritingThread.java:434)
                                                                                                         at com.neovisionaries.ws.client.WritingThread.sendFrames(WritingThread.java:384)
                                                                                                         at com.neovisionaries.ws.client.WritingThread.main(WritingThread.java:108)
                                                                                                         at com.neovisionaries.ws.client.WritingThread.runMain(WritingThread.java:55)
                                                                                                         at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45)
                                                                                                    Caused by: java.net.SocketException: Socket closed
                                                                                                         at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.writeInternal(ConscryptEngineSocket.java:728)
                                                                                                         at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.write(ConscryptEngineSocket.java:699)
                                                                                                         at java.io.BufferedOutputStream.flushBuffer(BufferedOutputStream.java:81)
                                                                                                         at java.io.BufferedOutputStream.flush(BufferedOutputStream.java:142)
                                                                                                         at java.io.BufferedOutputStream.flush(BufferedOutputStream.java:143)
                                                                                                         at com.neovisionaries.ws.client.WritingThread.flush(WritingThread.java:271)
                                                                                                         at com.neovisionaries.ws.client.WritingThread.doFlush(WritingThread.java:422)
                                                                                                         at com.neovisionaries.ws.client.WritingThread.sendFrames(WritingThread.java:384) 
                                                                                                         at com.neovisionaries.ws.client.WritingThread.main(WritingThread.java:108) 
                                                                                                         at com.neovisionaries.ws.client.WritingThread.runMain(WritingThread.java:55) 
                                                                                                         at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45) 
2025-08-21 11:22:33.826  4396-9737  WebSocket               com.dspread.mdm.service              I  🔧 onError: 不触发重连，等待 onDisconnected 处理
2025-08-21 11:22:33.836  4396-9737  WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误 (Ask Gemini)
                                                                                                    com.neovisionaries.ws.client.WebSocketException: Flushing frames to the server failed: Socket closed
                                                                                                         at com.neovisionaries.ws.client.WritingThread.doFlush(WritingThread.java:434)
                                                                                                         at com.neovisionaries.ws.client.WritingThread.sendFrames(WritingThread.java:384)
                                                                                                         at com.neovisionaries.ws.client.WritingThread.main(WritingThread.java:120)
                                                                                                         at com.neovisionaries.ws.client.WritingThread.runMain(WritingThread.java:55)
                                                                                                         at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45)
                                                                                                    Caused by: java.net.SocketException: Socket closed
                                                                                                         at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.writeInternal(ConscryptEngineSocket.java:728)
                                                                                                         at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.write(ConscryptEngineSocket.java:699)
                                                                                                         at java.io.BufferedOutputStream.flushBuffer(BufferedOutputStream.java:81)
                                                                                                         at java.io.BufferedOutputStream.flush(BufferedOutputStream.java:142)
                                                                                                         at java.io.BufferedOutputStream.flush(BufferedOutputStream.java:143)
                                                                                                         at com.neovisionaries.ws.client.WritingThread.flush(WritingThread.java:271)
                                                                                                         at com.neovisionaries.ws.client.WritingThread.doFlush(WritingThread.java:422)
                                                                                                         at com.neovisionaries.ws.client.WritingThread.sendFrames(WritingThread.java:384) 
                                                                                                         at com.neovisionaries.ws.client.WritingThread.main(WritingThread.java:120) 
                                                                                                         at com.neovisionaries.ws.client.WritingThread.runMain(WritingThread.java:55) 
                                                                                                         at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45) 
2025-08-21 11:22:33.843  4396-9737  WebSocket               com.dspread.mdm.service              I  🔧 onError: 不触发重连，等待 onDisconnected 处理
2025-08-21 11:22:33.852  4396-9737  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接断开 - 客户端主动断开
2025-08-21 11:22:33.856  4396-9737  WebSocket               com.dspread.mdm.service              I  🔧    服务器关闭帧: code=-1, reason=无原因
2025-08-21 11:22:33.861  4396-9737  WebSocket               com.dspread.mdm.service              I  🔧    客户端关闭帧: code=1002, reason=No more WebSocket frame from the server.
2025-08-21 11:22:33.868  4396-9737  WebSocket               com.dspread.mdm.service              I  🔧    断开发起方: 客户端
2025-08-21 11:22:33.874  4396-9737  WebSocket               com.dspread.mdm.service              I  🔧 停止PING/PONG检测
2025-08-21 11:22:33.880  4396-9737  WebSocket               com.dspread.mdm.service              I  🔧 reconnect() 被调用，调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onDisconnected:727
2025-08-21 11:22:33.885  4396-9737  WebSocket               com.dspread.mdm.service              I  🔧 开始重连 WebSocket... (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onDisconnected:727)
2025-08-21 11:22:33.896  4396-9737  WebSocket               com.dspread.mdm.service              I  🔧 使用Provisioning配置的重连策略: delayPolicy=1, delaySwitch=1, delayTime=60
2025-08-21 11:22:33.898  4396-4407  System                  com.dspread.mdm.service              W  A resource failed to call close. 
2025-08-21 11:22:33.900  4396-9737  WebSocket               com.dspread.mdm.service              I  🔧 当前断开记录数: 10, 延迟开关: 1
2025-08-21 11:22:33.901  4396-4407  System                  com.dspread.mdm.service              W  A resource failed to call close. 
2025-08-21 11:22:33.908  4396-9737  WebSocket               com.dspread.mdm.service              I  🔧 断开记录 最早一次断开: 2025-08-21 11:03:24
2025-08-21 11:22:33.915  4396-9737  WebSocket               com.dspread.mdm.service              I  🔧 断开记录 最后一次断开: 2025-08-21 11:22:33
2025-08-21 11:22:33.918  4396-9737  WebSocket               com.dspread.mdm.service              I  🔧 时间差: 19分钟 (配置阈值: 1分钟)
2025-08-21 11:22:33.922  4396-9737  WebSocket               com.dspread.mdm.service              I  🔧 断开间隔正常，使用标准重连策略
2025-08-21 11:22:33.926  4396-9737  WebSocket               com.dspread.mdm.service              I  🔧 重连策略计算完成: 第1次重连，间隔3000ms (3秒)
2025-08-21 11:22:33.929  4396-9737  WebSocket               com.dspread.mdm.service              I  🔧 开始第1次重连，间隔3000ms (3秒) (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onDisconnected:727)
2025-08-21 11:22:49.642  4396-4396  WebSocket               com.dspread.mdm.service              I  🔧 重连 WebSocket: wss://api.dspreadserv.net/status/websocket/register?serialNo=MDEzNTQwOTAyMDI1MDMwNTAzOTk=&publicKey=TUlHZk1BMEdDU3FHU0liM0RRRUJBUVVBQTRHTkFEQ0JpUUtCZ1FENjVsY3lJc2p6b0swWFpMZ2RUQmpOaCtKVDhpdGNlUHp1eVVMUGVyS281VzRxWjZRUy9QZS9zWU1PdG04RzhoZk5pSzZEN0xKQnZ2V243RW1nVTdVM2x3YjR4VXI3SS84V1hFZEFBWW1WNVlTdzdOenBQYkFaTUxSNVFmazBiK2lvcDZxaTlkeFpTazFsOGhlL0E3ZEV2MC92ZnNmRTU4a2k5UzRLVVRMVUhRSURBUUFC&query=0&msgVer=3&timestamp=1755746569630&signature=qJWWPKt+/XHvDljbTIyVkxdv5eF4kt8MuUXK4jPws8saTA+OC57te3V/lCvJ+pc06T0YPOuZB/Eu0KFE/4RJGuztz2PBQr4mpRqz1Tbmn0G1pxUUlG0C00EG0NFhhJOFiMO2lpAhFyIcxwoumBndEJ2I/YoPdsHvuzbOcieB0xQ=
2025-08-21 11:22:49.648  4396-4396  WebSocket               com.dspread.mdm.service              I  🔧 未检测到系统代理设置
2025-08-21 11:22:49.682  4396-9742  TrafficStats            com.dspread.mdm.service              D  tagSocket(111) with statsTag=0xffffffff, statsUid=-1
2025-08-21 11:22:49.933  4396-9743  TrafficStats            com.dspread.mdm.service              D  tagSocket(115) with statsTag=0xffffffff, statsUid=-1
2025-08-21 11:22:52.259  4396-9744  Common                  com.dspread.mdm.service              I  ✅ WebSocket 物理连接成功，等待服务器 hellMsg
2025-08-21 11:22:52.265  4396-9744  WebSocket               com.dspread.mdm.service              I  🔧 ===== 心跳发送器实际配置 =====
2025-08-21 11:22:52.270  4396-9744  WebSocket               com.dspread.mdm.service              I  🔧 实际 PING 发送间隔: 30000ms (30秒)
2025-08-21 11:22:52.275  4396-9744  WebSocket               com.dspread.mdm.service              I  🔧 PING 定时器名称: PingSender
2025-08-21 11:22:52.280  4396-9744  WebSocket               com.dspread.mdm.service              I  🔧 实际 PONG 发送间隔: 0ms (0秒)
2025-08-21 11:22:52.285  4396-9744  WebSocket               com.dspread.mdm.service              I  🔧 PONG 定时器名称: PongSender
2025-08-21 11:22:52.289  4396-9744  WebSocket               com.dspread.mdm.service              I  🔧 ===============================
2025-08-21 11:22:52.293  4396-9744  WebSocket               com.dspread.mdm.service              I  🔧 启动PING/PONG检测，阈值: 连续3个PING无PONG响应
2025-08-21 11:22:52.312  4396-9744  Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"hellMsg":"hello 01354090202503050399","tranCode":"S0000","version":"1","rebootTime":"01:05:33","serialNo":"01354090202503050399","deviceStatus":"6"}
2025-08-21 11:22:52.316  4396-9744  Common                  com.dspread.mdm.service              I  ✅ 收到服务器 hellMsg，WebSocket 业务连接成功！
2025-08-21 11:22:52.320  4396-9744  Common                  com.dspread.mdm.service              I  ✅ WebSocket 业务连接成功！
2025-08-21 11:22:52.324  4396-9744  WebSocket               com.dspread.mdm.service              I  🔧 业务连接成功，重连计数已重置为 0
2025-08-21 11:22:52.327  4396-9744  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接成功，检查自身更新任务状态
2025-08-21 11:22:52.333  4396-9744  Receiver                com.dspread.mdm.service              D  🔧 发送广播: com.dspread.mdm.service.CHECK_SELF_UPDATE_STATUS
2025-08-21 11:22:52.337  4396-9744  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接成功
2025-08-21 11:22:52.341  4396-9744  WebSocket               com.dspread.mdm.service              I  🔧 设备状态: 6
2025-08-21 11:22:52.344  4396-9744  WebSocket               com.dspread.mdm.service              I  🔧 检查首次连接标志: isFirstTimeWebSocketConnected=false
2025-08-21 11:22:52.348  4396-9744  WebSocket               com.dspread.mdm.service              I  🔧 非首次连接，跳过初始化信息发送和初始化检查
2025-08-21 11:22:52.351  4396-9744  WebSocket               com.dspread.mdm.service              I  🔧 执行必要的状态同步检查
2025-08-21 11:22:52.355  4396-9744  WebSocket               com.dspread.mdm.service              I  🔧 检查服务更新结果
2025-08-21 11:22:52.358  4396-9744  Task                    com.dspread.mdm.service              W  ⚠️ 任务列表为空，跳过服务更新结果检查
2025-08-21 11:22:52.362  4396-9744  WebSocket               com.dspread.mdm.service              I  🔧 检查OS升级状态...
2025-08-21 11:22:52.366  4396-9744  Common                  com.dspread.mdm.service              D  🔧 OsUpdateStatusChecker WebSocket连接成功，检查OS升级状态
2025-08-21 11:22:52.369  4396-9744  Common                  com.dspread.mdm.service              D  🔧 OsUpdateStatusChecker 没有待检查的OS升级任务
2025-08-21 11:22:52.373  4396-9744  WebSocket               com.dspread.mdm.service              I  🔧 OS升级状态检查已触发
2025-08-21 11:23:52.756  4396-9744  WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误 (Ask Gemini)
                                                                                                    com.neovisionaries.ws.client.NoMoreFrameException: No more WebSocket frame from the server.
                                                                                                         at com.neovisionaries.ws.client.WebSocketInputStream.readFrame(WebSocketInputStream.java:54)
                                                                                                         at com.neovisionaries.ws.client.ReadingThread.readFrame(ReadingThread.java:338)
                                                                                                         at com.neovisionaries.ws.client.ReadingThread.main(ReadingThread.java:99)
                                                                                                         at com.neovisionaries.ws.client.ReadingThread.runMain(ReadingThread.java:64)
                                                                                                         at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45)
2025-08-21 11:23:52.764  4396-9744  WebSocket               com.dspread.mdm.service              I  🔧 onError: 不触发重连，等待 onDisconnected 处理
2025-08-21 11:23:52.788  4396-9745  WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误 (Ask Gemini)
                                                                                                    com.neovisionaries.ws.client.WebSocketException: Flushing frames to the server failed: Socket closed
                                                                                                         at com.neovisionaries.ws.client.WritingThread.doFlush(WritingThread.java:434)
                                                                                                         at com.neovisionaries.ws.client.WritingThread.sendFrames(WritingThread.java:384)
                                                                                                         at com.neovisionaries.ws.client.WritingThread.main(WritingThread.java:108)
                                                                                                         at com.neovisionaries.ws.client.WritingThread.runMain(WritingThread.java:55)
                                                                                                         at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45)
                                                                                                    Caused by: java.net.SocketException: Socket closed
                                                                                                         at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.writeInternal(ConscryptEngineSocket.java:728)
                                                                                                         at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.write(ConscryptEngineSocket.java:699)
                                                                                                         at java.io.BufferedOutputStream.flushBuffer(BufferedOutputStream.java:81)
                                                                                                         at java.io.BufferedOutputStream.flush(BufferedOutputStream.java:142)
                                                                                                         at java.io.BufferedOutputStream.flush(BufferedOutputStream.java:143)
                                                                                                         at com.neovisionaries.ws.client.WritingThread.flush(WritingThread.java:271)
                                                                                                         at com.neovisionaries.ws.client.WritingThread.doFlush(WritingThread.java:422)
                                                                                                         at com.neovisionaries.ws.client.WritingThread.sendFrames(WritingThread.java:384) 
                                                                                                         at com.neovisionaries.ws.client.WritingThread.main(WritingThread.java:108) 
                                                                                                         at com.neovisionaries.ws.client.WritingThread.runMain(WritingThread.java:55) 
                                                                                                         at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45) 
2025-08-21 11:23:52.795  4396-9745  WebSocket               com.dspread.mdm.service              I  🔧 onError: 不触发重连，等待 onDisconnected 处理
2025-08-21 11:23:52.805  4396-9745  WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误 (Ask Gemini)
                                                                                                    com.neovisionaries.ws.client.WebSocketException: Flushing frames to the server failed: Socket closed
                                                                                                         at com.neovisionaries.ws.client.WritingThread.doFlush(WritingThread.java:434)
                                                                                                         at com.neovisionaries.ws.client.WritingThread.sendFrames(WritingThread.java:384)
                                                                                                         at com.neovisionaries.ws.client.WritingThread.main(WritingThread.java:120)
                                                                                                         at com.neovisionaries.ws.client.WritingThread.runMain(WritingThread.java:55)
                                                                                                         at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45)
                                                                                                    Caused by: java.net.SocketException: Socket closed
                                                                                                         at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.writeInternal(ConscryptEngineSocket.java:728)
                                                                                                         at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.write(ConscryptEngineSocket.java:699)
                                                                                                         at java.io.BufferedOutputStream.flushBuffer(BufferedOutputStream.java:81)
                                                                                                         at java.io.BufferedOutputStream.flush(BufferedOutputStream.java:142)
                                                                                                         at java.io.BufferedOutputStream.flush(BufferedOutputStream.java:143)
                                                                                                         at com.neovisionaries.ws.client.WritingThread.flush(WritingThread.java:271)
                                                                                                         at com.neovisionaries.ws.client.WritingThread.doFlush(WritingThread.java:422)
                                                                                                         at com.neovisionaries.ws.client.WritingThread.sendFrames(WritingThread.java:384) 
                                                                                                         at com.neovisionaries.ws.client.WritingThread.main(WritingThread.java:120) 
                                                                                                         at com.neovisionaries.ws.client.WritingThread.runMain(WritingThread.java:55) 
                                                                                                         at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45) 
2025-08-21 11:23:52.812  4396-9745  WebSocket               com.dspread.mdm.service              I  🔧 onError: 不触发重连，等待 onDisconnected 处理
2025-08-21 11:23:52.818  4396-9745  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接断开 - 客户端主动断开
2025-08-21 11:23:52.823  4396-9745  WebSocket               com.dspread.mdm.service              I  🔧    服务器关闭帧: code=-1, reason=无原因
2025-08-21 11:23:52.827  4396-9745  WebSocket               com.dspread.mdm.service              I  🔧    客户端关闭帧: code=1002, reason=No more WebSocket frame from the server.
2025-08-21 11:23:52.832  4396-9745  WebSocket               com.dspread.mdm.service              I  🔧    断开发起方: 客户端
2025-08-21 11:23:52.836  4396-9745  WebSocket               com.dspread.mdm.service              I  🔧 停止PING/PONG检测
2025-08-21 11:23:52.840  4396-9745  WebSocket               com.dspread.mdm.service              I  🔧 reconnect() 被调用，调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onDisconnected:727
2025-08-21 11:23:52.844  4396-9745  WebSocket               com.dspread.mdm.service              I  🔧 开始重连 WebSocket... (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onDisconnected:727)
2025-08-21 11:23:52.853  4396-9745  WebSocket               com.dspread.mdm.service              I  🔧 使用Provisioning配置的重连策略: delayPolicy=1, delaySwitch=1, delayTime=60
2025-08-21 11:23:52.856  4396-9745  WebSocket               com.dspread.mdm.service              I  🔧 当前断开记录数: 10, 延迟开关: 1
2025-08-21 11:23:52.864  4396-9745  WebSocket               com.dspread.mdm.service              I  🔧 断开记录 最早一次断开: 2025-08-21 11:05:20
2025-08-21 11:23:52.871  4396-9745  WebSocket               com.dspread.mdm.service              I  🔧 断开记录 最后一次断开: 2025-08-21 11:23:52
2025-08-21 11:23:52.875  4396-9745  WebSocket               com.dspread.mdm.service              I  🔧 时间差: 18分钟 (配置阈值: 1分钟)
2025-08-21 11:23:52.878  4396-9745  WebSocket               com.dspread.mdm.service              I  🔧 断开间隔正常，使用标准重连策略
2025-08-21 11:23:52.882  4396-9745  WebSocket               com.dspread.mdm.service              I  🔧 重连策略计算完成: 第1次重连，间隔3000ms (3秒)
2025-08-21 11:23:52.886  4396-9745  WebSocket               com.dspread.mdm.service              I  🔧 开始第1次重连，间隔3000ms (3秒) (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onDisconnected:727)
2025-08-21 11:24:35.801  4396-4396  WebSocket               com.dspread.mdm.service              I  🔧 重连 WebSocket: wss://api.dspreadserv.net/status/websocket/register?serialNo=MDEzNTQwOTAyMDI1MDMwNTAzOTk=&publicKey=TUlHZk1BMEdDU3FHU0liM0RRRUJBUVVBQTRHTkFEQ0JpUUtCZ1FENjVsY3lJc2p6b0swWFpMZ2RUQmpOaCtKVDhpdGNlUHp1eVVMUGVyS281VzRxWjZRUy9QZS9zWU1PdG04RzhoZk5pSzZEN0xKQnZ2V243RW1nVTdVM2x3YjR4VXI3SS84V1hFZEFBWW1WNVlTdzdOenBQYkFaTUxSNVFmazBiK2lvcDZxaTlkeFpTazFsOGhlL0E3ZEV2MC92ZnNmRTU4a2k5UzRLVVRMVUhRSURBUUFC&query=0&msgVer=3&timestamp=1755746675777&signature=rUhbGGD7Doosjh2NGUy6f917Wqf9a+Bp+0fa83kaPsLrwhFLztLkEcKTCMapU/IumzQmnVvoV/J6cIjofvyJm2YEnrz3CPdbiMwk7jQiW8kigodqTZWK+St0cUCbDyNlivNcJ2jGf2g8/WQN2gN++KtZ9/hNAlT4Rrw9bif09pQ=
2025-08-21 11:24:35.808  4396-4396  WebSocket               com.dspread.mdm.service              I  🔧 未检测到系统代理设置
2025-08-21 11:24:35.841  4396-9750  TrafficStats            com.dspread.mdm.service              D  tagSocket(111) with statsTag=0xffffffff, statsUid=-1
2025-08-21 11:24:36.092  4396-9751  TrafficStats            com.dspread.mdm.service              D  tagSocket(118) with statsTag=0xffffffff, statsUid=-1
2025-08-21 11:24:39.060  4396-9752  Common                  com.dspread.mdm.service              I  ✅ WebSocket 物理连接成功，等待服务器 hellMsg
2025-08-21 11:24:39.065  4396-9752  WebSocket               com.dspread.mdm.service              I  🔧 ===== 心跳发送器实际配置 =====
2025-08-21 11:24:39.069  4396-9752  WebSocket               com.dspread.mdm.service              I  🔧 实际 PING 发送间隔: 30000ms (30秒)
2025-08-21 11:24:39.073  4396-9752  WebSocket               com.dspread.mdm.service              I  🔧 PING 定时器名称: PingSender
2025-08-21 11:24:39.076  4396-9752  WebSocket               com.dspread.mdm.service              I  🔧 实际 PONG 发送间隔: 0ms (0秒)
2025-08-21 11:24:39.080  4396-9752  WebSocket               com.dspread.mdm.service              I  🔧 PONG 定时器名称: PongSender
2025-08-21 11:24:39.084  4396-9752  WebSocket               com.dspread.mdm.service              I  🔧 ===============================
2025-08-21 11:24:39.087  4396-9752  WebSocket               com.dspread.mdm.service              I  🔧 启动PING/PONG检测，阈值: 连续3个PING无PONG响应
2025-08-21 11:24:39.103  4396-9752  Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"hellMsg":"hello 01354090202503050399","tranCode":"S0000","version":"1","rebootTime":"03:15:23","serialNo":"01354090202503050399","deviceStatus":"6"}
2025-08-21 11:24:39.107  4396-9752  Common                  com.dspread.mdm.service              I  ✅ 收到服务器 hellMsg，WebSocket 业务连接成功！
2025-08-21 11:24:39.111  4396-9752  Common                  com.dspread.mdm.service              I  ✅ WebSocket 业务连接成功！
2025-08-21 11:24:39.115  4396-9752  WebSocket               com.dspread.mdm.service              I  🔧 业务连接成功，重连计数已重置为 0
2025-08-21 11:24:39.118  4396-9752  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接成功，检查自身更新任务状态
2025-08-21 11:24:39.124  4396-9752  Receiver                com.dspread.mdm.service              D  🔧 发送广播: com.dspread.mdm.service.CHECK_SELF_UPDATE_STATUS
2025-08-21 11:24:39.128  4396-9752  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接成功
2025-08-21 11:24:39.132  4396-9752  WebSocket               com.dspread.mdm.service              I  🔧 设备状态: 6
2025-08-21 11:24:39.135  4396-9752  WebSocket               com.dspread.mdm.service              I  🔧 检查首次连接标志: isFirstTimeWebSocketConnected=false
2025-08-21 11:24:39.139  4396-9752  WebSocket               com.dspread.mdm.service              I  🔧 非首次连接，跳过初始化信息发送和初始化检查
2025-08-21 11:24:39.142  4396-9752  WebSocket               com.dspread.mdm.service              I  🔧 执行必要的状态同步检查
2025-08-21 11:24:39.146  4396-9752  WebSocket               com.dspread.mdm.service              I  🔧 检查服务更新结果
2025-08-21 11:24:39.149  4396-9752  Task                    com.dspread.mdm.service              W  ⚠️ 任务列表为空，跳过服务更新结果检查
2025-08-21 11:24:39.153  4396-9752  WebSocket               com.dspread.mdm.service              I  🔧 检查OS升级状态...
2025-08-21 11:24:39.157  4396-9752  Common                  com.dspread.mdm.service              D  🔧 OsUpdateStatusChecker WebSocket连接成功，检查OS升级状态
2025-08-21 11:24:39.160  4396-9752  Common                  com.dspread.mdm.service              D  🔧 OsUpdateStatusChecker 没有待检查的OS升级任务
2025-08-21 11:24:39.164  4396-9752  WebSocket               com.dspread.mdm.service              I  🔧 OS升级状态检查已触发
2025-08-21 11:25:39.131  4396-9752  WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误 (Ask Gemini)
                                                                                                    com.neovisionaries.ws.client.NoMoreFrameException: No more WebSocket frame from the server.
                                                                                                         at com.neovisionaries.ws.client.WebSocketInputStream.readFrame(WebSocketInputStream.java:54)
                                                                                                         at com.neovisionaries.ws.client.ReadingThread.readFrame(ReadingThread.java:338)
                                                                                                         at com.neovisionaries.ws.client.ReadingThread.main(ReadingThread.java:99)
                                                                                                         at com.neovisionaries.ws.client.ReadingThread.runMain(ReadingThread.java:64)
                                                                                                         at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45)
2025-08-21 11:25:39.139  4396-9752  WebSocket               com.dspread.mdm.service              I  🔧 onError: 不触发重连，等待 onDisconnected 处理
2025-08-21 11:25:39.156  4396-9753  WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误 (Ask Gemini)
                                                                                                    com.neovisionaries.ws.client.WebSocketException: Flushing frames to the server failed: Socket closed
                                                                                                         at com.neovisionaries.ws.client.WritingThread.doFlush(WritingThread.java:434)
                                                                                                         at com.neovisionaries.ws.client.WritingThread.sendFrames(WritingThread.java:384)
                                                                                                         at com.neovisionaries.ws.client.WritingThread.main(WritingThread.java:108)
                                                                                                         at com.neovisionaries.ws.client.WritingThread.runMain(WritingThread.java:55)
                                                                                                         at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45)
                                                                                                    Caused by: java.net.SocketException: Socket closed
                                                                                                         at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.writeInternal(ConscryptEngineSocket.java:728)
                                                                                                         at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.write(ConscryptEngineSocket.java:699)
                                                                                                         at java.io.BufferedOutputStream.flushBuffer(BufferedOutputStream.java:81)
                                                                                                         at java.io.BufferedOutputStream.flush(BufferedOutputStream.java:142)
                                                                                                         at java.io.BufferedOutputStream.flush(BufferedOutputStream.java:143)
                                                                                                         at com.neovisionaries.ws.client.WritingThread.flush(WritingThread.java:271)
                                                                                                         at com.neovisionaries.ws.client.WritingThread.doFlush(WritingThread.java:422)
                                                                                                         at com.neovisionaries.ws.client.WritingThread.sendFrames(WritingThread.java:384) 
                                                                                                         at com.neovisionaries.ws.client.WritingThread.main(WritingThread.java:108) 
                                                                                                         at com.neovisionaries.ws.client.WritingThread.runMain(WritingThread.java:55) 
                                                                                                         at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45) 
2025-08-21 11:25:39.163  4396-9753  WebSocket               com.dspread.mdm.service              I  🔧 onError: 不触发重连，等待 onDisconnected 处理
2025-08-21 11:25:39.173  4396-9753  WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误 (Ask Gemini)
                                                                                                    com.neovisionaries.ws.client.WebSocketException: Flushing frames to the server failed: Socket closed
                                                                                                         at com.neovisionaries.ws.client.WritingThread.doFlush(WritingThread.java:434)
                                                                                                         at com.neovisionaries.ws.client.WritingThread.sendFrames(WritingThread.java:384)
                                                                                                         at com.neovisionaries.ws.client.WritingThread.main(WritingThread.java:120)
                                                                                                         at com.neovisionaries.ws.client.WritingThread.runMain(WritingThread.java:55)
                                                                                                         at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45)
                                                                                                    Caused by: java.net.SocketException: Socket closed
                                                                                                         at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.writeInternal(ConscryptEngineSocket.java:728)
                                                                                                         at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.write(ConscryptEngineSocket.java:699)
                                                                                                         at java.io.BufferedOutputStream.flushBuffer(BufferedOutputStream.java:81)
                                                                                                         at java.io.BufferedOutputStream.flush(BufferedOutputStream.java:142)
                                                                                                         at java.io.BufferedOutputStream.flush(BufferedOutputStream.java:143)
                                                                                                         at com.neovisionaries.ws.client.WritingThread.flush(WritingThread.java:271)
                                                                                                         at com.neovisionaries.ws.client.WritingThread.doFlush(WritingThread.java:422)
                                                                                                         at com.neovisionaries.ws.client.WritingThread.sendFrames(WritingThread.java:384) 
                                                                                                         at com.neovisionaries.ws.client.WritingThread.main(WritingThread.java:120) 
                                                                                                         at com.neovisionaries.ws.client.WritingThread.runMain(WritingThread.java:55) 
                                                                                                         at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45) 
2025-08-21 11:25:39.179  4396-9753  WebSocket               com.dspread.mdm.service              I  🔧 onError: 不触发重连，等待 onDisconnected 处理
2025-08-21 11:25:39.186  4396-9753  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接断开 - 客户端主动断开
2025-08-21 11:25:39.190  4396-9753  WebSocket               com.dspread.mdm.service              I  🔧    服务器关闭帧: code=-1, reason=无原因
2025-08-21 11:25:39.194  4396-9753  WebSocket               com.dspread.mdm.service              I  🔧    客户端关闭帧: code=1002, reason=No more WebSocket frame from the server.
2025-08-21 11:25:39.199  4396-9753  WebSocket               com.dspread.mdm.service              I  🔧    断开发起方: 客户端
2025-08-21 11:25:39.203  4396-9753  WebSocket               com.dspread.mdm.service              I  🔧 停止PING/PONG检测
2025-08-21 11:25:39.207  4396-9753  WebSocket               com.dspread.mdm.service              I  🔧 reconnect() 被调用，调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onDisconnected:727
2025-08-21 11:25:39.211  4396-9753  WebSocket               com.dspread.mdm.service              I  🔧 开始重连 WebSocket... (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onDisconnected:727)
2025-08-21 11:25:39.219  4396-9753  WebSocket               com.dspread.mdm.service              I  🔧 使用Provisioning配置的重连策略: delayPolicy=1, delaySwitch=1, delayTime=60
2025-08-21 11:25:39.223  4396-9753  WebSocket               com.dspread.mdm.service              I  🔧 当前断开记录数: 10, 延迟开关: 1
2025-08-21 11:25:39.230  4396-9753  WebSocket               com.dspread.mdm.service              I  🔧 断开记录 最早一次断开: 2025-08-21 11:09:48
2025-08-21 11:25:39.237  4396-9753  WebSocket               com.dspread.mdm.service              I  🔧 断开记录 最后一次断开: 2025-08-21 11:25:39
2025-08-21 11:25:39.241  4396-9753  WebSocket               com.dspread.mdm.service              I  🔧 时间差: 15分钟 (配置阈值: 1分钟)
2025-08-21 11:25:39.245  4396-9753  WebSocket               com.dspread.mdm.service              I  🔧 断开间隔正常，使用标准重连策略
2025-08-21 11:25:39.248  4396-9753  WebSocket               com.dspread.mdm.service              I  🔧 重连策略计算完成: 第1次重连，间隔3000ms (3秒)
2025-08-21 11:25:39.252  4396-9753  WebSocket               com.dspread.mdm.service              I  🔧 开始第1次重连，间隔3000ms (3秒) (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onDisconnected:727)
2025-08-21 11:25:42.280  4396-4396  WebSocket               com.dspread.mdm.service              I  🔧 重连 WebSocket: wss://api.dspreadserv.net/status/websocket/register?serialNo=MDEzNTQwOTAyMDI1MDMwNTAzOTk=&publicKey=TUlHZk1BMEdDU3FHU0liM0RRRUJBUVVBQTRHTkFEQ0JpUUtCZ1FENjVsY3lJc2p6b0swWFpMZ2RUQmpOaCtKVDhpdGNlUHp1eVVMUGVyS281VzRxWjZRUy9QZS9zWU1PdG04RzhoZk5pSzZEN0xKQnZ2V243RW1nVTdVM2x3YjR4VXI3SS84V1hFZEFBWW1WNVlTdzdOenBQYkFaTUxSNVFmazBiK2lvcDZxaTlkeFpTazFsOGhlL0E3ZEV2MC92ZnNmRTU4a2k5UzRLVVRMVUhRSURBUUFC&query=0&msgVer=3&timestamp=1755746742268&signature=SikzAcOjff0K82w6yPd24IFUP9RqkopVF7IvigMFsmx2+eGNZ4B/2cySjjrjS2W1nyApe57MlDXTR1GTFsA/ug6Tv0rU0sbH5wUlGppQ4kF1EGfc7dlV7Sr2KUFiobExt8e1nL6a87ZAw3fLmG/BAqR0EQNdckJlX2LvEwOxnb8=
2025-08-21 11:25:42.287  4396-4396  WebSocket               com.dspread.mdm.service              I  🔧 未检测到系统代理设置
2025-08-21 11:25:42.650  4396-9758  TrafficStats            com.dspread.mdm.service              D  tagSocket(118) with statsTag=0xffffffff, statsUid=-1
2025-08-21 11:25:42.901  4396-9759  TrafficStats            com.dspread.mdm.service              D  tagSocket(123) with statsTag=0xffffffff, statsUid=-1
2025-08-21 11:25:47.011  4396-9761  Common                  com.dspread.mdm.service              I  ✅ WebSocket 物理连接成功，等待服务器 hellMsg
2025-08-21 11:25:47.017  4396-9761  WebSocket               com.dspread.mdm.service              I  🔧 ===== 心跳发送器实际配置 =====
2025-08-21 11:25:47.022  4396-9761  WebSocket               com.dspread.mdm.service              I  🔧 实际 PING 发送间隔: 30000ms (30秒)
2025-08-21 11:25:47.026  4396-9761  WebSocket               com.dspread.mdm.service              I  🔧 PING 定时器名称: PingSender
2025-08-21 11:25:47.029  4396-9760  Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"hellMsg":"hello 01354090202503050399","tranCode":"S0000","version":"1","rebootTime":"01:47:03","serialNo":"01354090202503050399","deviceStatus":"6"}
2025-08-21 11:25:47.030  4396-9761  WebSocket               com.dspread.mdm.service              I  🔧 实际 PONG 发送间隔: 0ms (0秒)
2025-08-21 11:25:47.033  4396-9761  WebSocket               com.dspread.mdm.service              I  🔧 PONG 定时器名称: PongSender
2025-08-21 11:25:47.034  4396-9760  Common                  com.dspread.mdm.service              I  ✅ 收到服务器 hellMsg，WebSocket 业务连接成功！
2025-08-21 11:25:47.038  4396-9761  WebSocket               com.dspread.mdm.service              I  🔧 ===============================
2025-08-21 11:25:47.040  4396-9760  Common                  com.dspread.mdm.service              I  ✅ WebSocket 业务连接成功！
2025-08-21 11:25:47.041  4396-9761  WebSocket               com.dspread.mdm.service              I  🔧 启动PING/PONG检测，阈值: 连续3个PING无PONG响应
2025-08-21 11:25:47.044  4396-9760  WebSocket               com.dspread.mdm.service              I  🔧 业务连接成功，重连计数已重置为 0
2025-08-21 11:25:47.048  4396-9760  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接成功，检查自身更新任务状态
2025-08-21 11:25:47.055  4396-9760  Receiver                com.dspread.mdm.service              D  🔧 发送广播: com.dspread.mdm.service.CHECK_SELF_UPDATE_STATUS
2025-08-21 11:25:47.059  4396-9760  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接成功
2025-08-21 11:25:47.063  4396-9760  WebSocket               com.dspread.mdm.service              I  🔧 设备状态: 6
2025-08-21 11:25:47.066  4396-9760  WebSocket               com.dspread.mdm.service              I  🔧 检查首次连接标志: isFirstTimeWebSocketConnected=false
2025-08-21 11:25:47.070  4396-9760  WebSocket               com.dspread.mdm.service              I  🔧 非首次连接，跳过初始化信息发送和初始化检查
2025-08-21 11:25:47.074  4396-9760  WebSocket               com.dspread.mdm.service              I  🔧 执行必要的状态同步检查
2025-08-21 11:25:47.077  4396-9760  WebSocket               com.dspread.mdm.service              I  🔧 检查服务更新结果
2025-08-21 11:25:47.112  4396-9760  Task                    com.dspread.mdm.service              W  ⚠️ 任务列表为空，跳过服务更新结果检查
2025-08-21 11:25:47.115  4396-9760  WebSocket               com.dspread.mdm.service              I  🔧 检查OS升级状态...
2025-08-21 11:25:47.119  4396-9760  Common                  com.dspread.mdm.service              D  🔧 OsUpdateStatusChecker WebSocket连接成功，检查OS升级状态
2025-08-21 11:25:47.122  4396-9760  Common                  com.dspread.mdm.service              D  🔧 OsUpdateStatusChecker 没有待检查的OS升级任务
2025-08-21 11:25:47.126  4396-9760  WebSocket               com.dspread.mdm.service              I  🔧 OS升级状态检查已触发
2025-08-21 11:26:46.521  4396-9760  WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误 (Ask Gemini)
                                                                                                    com.neovisionaries.ws.client.NoMoreFrameException: No more WebSocket frame from the server.
                                                                                                         at com.neovisionaries.ws.client.WebSocketInputStream.readFrame(WebSocketInputStream.java:54)
                                                                                                         at com.neovisionaries.ws.client.ReadingThread.readFrame(ReadingThread.java:338)
                                                                                                         at com.neovisionaries.ws.client.ReadingThread.main(ReadingThread.java:99)
                                                                                                         at com.neovisionaries.ws.client.ReadingThread.runMain(ReadingThread.java:64)
                                                                                                         at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45)
2025-08-21 11:26:46.528  4396-9760  WebSocket               com.dspread.mdm.service              I  🔧 onError: 不触发重连，等待 onDisconnected 处理
2025-08-21 11:26:46.546  4396-9761  WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误 (Ask Gemini)
                                                                                                    com.neovisionaries.ws.client.WebSocketException: Flushing frames to the server failed: Socket closed
                                                                                                         at com.neovisionaries.ws.client.WritingThread.doFlush(WritingThread.java:434)
                                                                                                         at com.neovisionaries.ws.client.WritingThread.sendFrames(WritingThread.java:384)
                                                                                                         at com.neovisionaries.ws.client.WritingThread.main(WritingThread.java:108)
                                                                                                         at com.neovisionaries.ws.client.WritingThread.runMain(WritingThread.java:55)
                                                                                                         at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45)
                                                                                                    Caused by: java.net.SocketException: Socket closed
                                                                                                         at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.writeInternal(ConscryptEngineSocket.java:728)
                                                                                                         at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.write(ConscryptEngineSocket.java:699)
                                                                                                         at java.io.BufferedOutputStream.flushBuffer(BufferedOutputStream.java:81)
                                                                                                         at java.io.BufferedOutputStream.flush(BufferedOutputStream.java:142)
                                                                                                         at java.io.BufferedOutputStream.flush(BufferedOutputStream.java:143)
                                                                                                         at com.neovisionaries.ws.client.WritingThread.flush(WritingThread.java:271)
                                                                                                         at com.neovisionaries.ws.client.WritingThread.doFlush(WritingThread.java:422)
                                                                                                         at com.neovisionaries.ws.client.WritingThread.sendFrames(WritingThread.java:384) 
                                                                                                         at com.neovisionaries.ws.client.WritingThread.main(WritingThread.java:108) 
                                                                                                         at com.neovisionaries.ws.client.WritingThread.runMain(WritingThread.java:55) 
                                                                                                         at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45) 
2025-08-21 11:26:46.553  4396-9761  WebSocket               com.dspread.mdm.service              I  🔧 onError: 不触发重连，等待 onDisconnected 处理
2025-08-21 11:26:46.562  4396-9761  WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误 (Ask Gemini)
                                                                                                    com.neovisionaries.ws.client.WebSocketException: Flushing frames to the server failed: Socket closed
                                                                                                         at com.neovisionaries.ws.client.WritingThread.doFlush(WritingThread.java:434)
                                                                                                         at com.neovisionaries.ws.client.WritingThread.sendFrames(WritingThread.java:384)
                                                                                                         at com.neovisionaries.ws.client.WritingThread.main(WritingThread.java:120)
                                                                                                         at com.neovisionaries.ws.client.WritingThread.runMain(WritingThread.java:55)
                                                                                                         at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45)
                                                                                                    Caused by: java.net.SocketException: Socket closed
                                                                                                         at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.writeInternal(ConscryptEngineSocket.java:728)
                                                                                                         at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.write(ConscryptEngineSocket.java:699)
                                                                                                         at java.io.BufferedOutputStream.flushBuffer(BufferedOutputStream.java:81)
                                                                                                         at java.io.BufferedOutputStream.flush(BufferedOutputStream.java:142)
                                                                                                         at java.io.BufferedOutputStream.flush(BufferedOutputStream.java:143)
                                                                                                         at com.neovisionaries.ws.client.WritingThread.flush(WritingThread.java:271)
                                                                                                         at com.neovisionaries.ws.client.WritingThread.doFlush(WritingThread.java:422)
                                                                                                         at com.neovisionaries.ws.client.WritingThread.sendFrames(WritingThread.java:384) 
                                                                                                         at com.neovisionaries.ws.client.WritingThread.main(WritingThread.java:120) 
                                                                                                         at com.neovisionaries.ws.client.WritingThread.runMain(WritingThread.java:55) 
                                                                                                         at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45) 
2025-08-21 11:26:46.567  4396-9761  WebSocket               com.dspread.mdm.service              I  🔧 onError: 不触发重连，等待 onDisconnected 处理
2025-08-21 11:26:46.578  4396-4396  Receiver                com.dspread.mdm.service              D  🔧 屏幕点亮
2025-08-21 11:26:46.600  4396-4396  WebSocket               com.dspread.mdm.service              W  ⚠️ 未知触发条件: screen_on，默认按被动式处理
2025-08-21 11:26:46.610  4396-4396  WebSocket               com.dspread.mdm.service              I  🔧 C0201 被动式上送拒绝: screen_on (拒绝: 7)
2025-08-21 11:26:46.614  4396-9761  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接断开 - 客户端主动断开
2025-08-21 11:26:46.624  4396-9761  WebSocket               com.dspread.mdm.service              I  🔧    服务器关闭帧: code=-1, reason=无原因
2025-08-21 11:26:46.628  4396-9761  WebSocket               com.dspread.mdm.service              I  🔧    客户端关闭帧: code=1002, reason=No more WebSocket frame from the server.
2025-08-21 11:26:46.638  4396-9761  WebSocket               com.dspread.mdm.service              I  🔧    断开发起方: 客户端
2025-08-21 11:26:46.643  4396-4396  WebSocket               com.dspread.mdm.service              I  🔧 C0201 设备状态上送被流量控制阻止: 被动事件 'screen_on' 在平衡模式 - 重要变化下未启用
2025-08-21 11:26:46.647  4396-4396  Receiver                com.dspread.mdm.service              D  🔧 屏幕点亮处理完成
2025-08-21 11:26:46.654  4396-9761  WebSocket               com.dspread.mdm.service              I  🔧 停止PING/PONG检测
2025-08-21 11:26:46.662  4396-9761  WebSocket               com.dspread.mdm.service              I  🔧 reconnect() 被调用，调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onDisconnected:727
2025-08-21 11:26:46.667  4396-9761  WebSocket               com.dspread.mdm.service              I  🔧 开始重连 WebSocket... (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onDisconnected:727)
2025-08-21 11:26:46.677  4396-9761  WebSocket               com.dspread.mdm.service              I  🔧 使用Provisioning配置的重连策略: delayPolicy=1, delaySwitch=1, delayTime=60
2025-08-21 11:26:46.681  4396-9761  WebSocket               com.dspread.mdm.service              I  🔧 当前断开记录数: 10, 延迟开关: 1
2025-08-21 11:26:46.691  4396-9761  WebSocket               com.dspread.mdm.service              I  🔧 断开记录 最早一次断开: 2025-08-21 11:11:22
2025-08-21 11:26:46.710  4396-9761  WebSocket               com.dspread.mdm.service              I  🔧 断开记录 最后一次断开: 2025-08-21 11:26:46
2025-08-21 11:26:46.719  4396-9761  WebSocket               com.dspread.mdm.service              I  🔧 时间差: 15分钟 (配置阈值: 1分钟)
2025-08-21 11:26:46.725  4396-9761  WebSocket               com.dspread.mdm.service              I  🔧 断开间隔正常，使用标准重连策略
2025-08-21 11:26:46.729  4396-9761  WebSocket               com.dspread.mdm.service              I  🔧 重连策略计算完成: 第1次重连，间隔3000ms (3秒)
2025-08-21 11:26:46.735  4396-9761  WebSocket               com.dspread.mdm.service              I  🔧 开始第1次重连，间隔3000ms (3秒) (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onDisconnected:727)
2025-08-21 11:26:49.761  4396-4396  WebSocket               com.dspread.mdm.service              I  🔧 重连 WebSocket: wss://api.dspreadserv.net/status/websocket/register?serialNo=MDEzNTQwOTAyMDI1MDMwNTAzOTk=&publicKey=TUlHZk1BMEdDU3FHU0liM0RRRUJBUVVBQTRHTkFEQ0JpUUtCZ1FENjVsY3lJc2p6b0swWFpMZ2RUQmpOaCtKVDhpdGNlUHp1eVVMUGVyS281VzRxWjZRUy9QZS9zWU1PdG04RzhoZk5pSzZEN0xKQnZ2V243RW1nVTdVM2x3YjR4VXI3SS84V1hFZEFBWW1WNVlTdzdOenBQYkFaTUxSNVFmazBiK2lvcDZxaTlkeFpTazFsOGhlL0E3ZEV2MC92ZnNmRTU4a2k5UzRLVVRMVUhRSURBUUFC&query=0&msgVer=3&timestamp=1755746809750&signature=L0YrXnWhPMkQVjH/sapIG4k9esst1TCTxY65t4VUiKiHpaXnk3fEksfM7t3SDcDlkM7etAaePAl5/MNONMrZgmI4cieJKYg3I3/wy7ULQjtAg700ZockmIaq+WYjTE+NyTVVzHVpLIyqnjRCXak8YU4peQvdwUVWenigUf2ipvo=
2025-08-21 11:26:49.768  4396-4396  WebSocket               com.dspread.mdm.service              I  🔧 未检测到系统代理设置
2025-08-21 11:26:49.897  4396-9768  TrafficStats            com.dspread.mdm.service              D  tagSocket(118) with statsTag=0xffffffff, statsUid=-1
2025-08-21 11:26:50.148  4396-9769  TrafficStats            com.dspread.mdm.service              D  tagSocket(126) with statsTag=0xffffffff, statsUid=-1
2025-08-21 11:26:50.513  4396-4396  Receiver                com.dspread.mdm.service              D  🔧 电源已连接
2025-08-21 11:26:50.520  4396-4396  WebSocket               com.dspread.mdm.service              I  🔧 C0902 主动式上送: charging_state_change (主动: 5)
2025-08-21 11:26:50.566  4396-4396  WebSocket               com.dspread.mdm.service              I  🔧 消息已缓存: C0902 (缓存数量: 1)
2025-08-21 11:26:50.569  4396-4396  WebSocket               com.dspread.mdm.service              I  🔧 C0902 电池状态上送成功 (1, trigger=charging_state_change)
2025-08-21 11:26:51.463  4396-9794  Common                  com.dspread.mdm.service              I  ✅ WebSocket 物理连接成功，等待服务器 hellMsg
2025-08-21 11:26:51.476  4396-9794  WebSocket               com.dspread.mdm.service              I  🔧 ===== 心跳发送器实际配置 =====
2025-08-21 11:26:51.489  4396-9794  WebSocket               com.dspread.mdm.service              I  🔧 实际 PING 发送间隔: 30000ms (30秒)
2025-08-21 11:26:51.493  4396-9794  WebSocket               com.dspread.mdm.service              I  🔧 PING 定时器名称: PingSender
2025-08-21 11:26:51.497  4396-9794  WebSocket               com.dspread.mdm.service              I  🔧 实际 PONG 发送间隔: 0ms (0秒)
2025-08-21 11:26:51.502  4396-9794  WebSocket               com.dspread.mdm.service              I  🔧 PONG 定时器名称: PongSender
2025-08-21 11:26:51.506  4396-9794  WebSocket               com.dspread.mdm.service              I  🔧 ===============================
2025-08-21 11:26:51.509  4396-9794  WebSocket               com.dspread.mdm.service              I  🔧 启动PING/PONG检测，阈值: 连续3个PING无PONG响应
2025-08-21 11:26:51.530  4396-9794  Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"hellMsg":"hello 01354090202503050399","tranCode":"S0000","version":"1","rebootTime":"02:04:45","serialNo":"01354090202503050399","deviceStatus":"6"}
2025-08-21 11:26:51.534  4396-9794  Common                  com.dspread.mdm.service              I  ✅ 收到服务器 hellMsg，WebSocket 业务连接成功！
2025-08-21 11:26:51.538  4396-9794  Common                  com.dspread.mdm.service              I  ✅ WebSocket 业务连接成功！
2025-08-21 11:26:51.541  4396-9794  WebSocket               com.dspread.mdm.service              I  🔧 业务连接成功，重连计数已重置为 0
2025-08-21 11:26:51.545  4396-9794  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接成功，检查自身更新任务状态
2025-08-21 11:26:51.550  4396-9794  Receiver                com.dspread.mdm.service              D  🔧 发送广播: com.dspread.mdm.service.CHECK_SELF_UPDATE_STATUS
2025-08-21 11:26:51.554  4396-9794  WebSocket               com.dspread.mdm.service              I  🔧 开始发送缓存消息，数量: 1
2025-08-21 11:26:51.560  4396-9794  WebSocket               com.dspread.mdm.service              I  🔧 C0902 主动式上送: cached_message_resend (主动: 6)
2025-08-21 11:26:51.573  4396-9794  Common                  com.dspread.mdm.service              I  ✅ 缓存消息发送成功: C0902
2025-08-21 11:26:52.079  4396-9794  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接成功
2025-08-21 11:26:52.083  4396-9794  WebSocket               com.dspread.mdm.service              I  🔧 设备状态: 6
2025-08-21 11:26:52.087  4396-9794  WebSocket               com.dspread.mdm.service              I  🔧 检查首次连接标志: isFirstTimeWebSocketConnected=false
2025-08-21 11:26:52.090  4396-9794  WebSocket               com.dspread.mdm.service              I  🔧 非首次连接，跳过初始化信息发送和初始化检查
2025-08-21 11:26:52.094  4396-9794  WebSocket               com.dspread.mdm.service              I  🔧 执行必要的状态同步检查
2025-08-21 11:26:52.099  4396-9794  WebSocket               com.dspread.mdm.service              I  🔧 检查服务更新结果
2025-08-21 11:26:52.104  4396-9794  Task                    com.dspread.mdm.service              W  ⚠️ 任务列表为空，跳过服务更新结果检查
2025-08-21 11:26:52.107  4396-9794  WebSocket               com.dspread.mdm.service              I  🔧 检查OS升级状态...
2025-08-21 11:26:52.110  4396-9794  Common                  com.dspread.mdm.service              D  🔧 OsUpdateStatusChecker WebSocket连接成功，检查OS升级状态
2025-08-21 11:26:52.114  4396-9794  Common                  com.dspread.mdm.service              D  🔧 OsUpdateStatusChecker 没有待检查的OS升级任务
2025-08-21 11:26:52.117  4396-9794  WebSocket               com.dspread.mdm.service              I  🔧 OS升级状态检查已触发
2025-08-21 11:26:52.189  4396-4396  getAnimDeal             com.dspread.mdm.service              I  mode:3 packageName:com.dspread.mdm.service result: false
2025-08-21 11:26:52.205   646-939   BufferQueueDebug        surfaceflinger                       E  [com.dspread.mdm.service/com.dspread.mdm.service.ui.activity.TestActivity#689](this:0xa57fdc40,id:-1,api:0,p:-1,c:-1) id info cannot be read from 'com.dspread.mdm.service/com.dspread.mdm.service.ui.activity.TestActivity#689'
2025-08-21 11:26:52.231   646-1594  BufferQueueDebug        surfaceflinger                       E  [cd6e33b ActivityRecordInputSink com.dspread.mdm.service/.ui.activity.TestActivity#691](this:0xa56f1c40,id:-1,api:0,p:-1,c:-1) id info cannot be read from 'cd6e33b ActivityRecordInputSink com.dspread.mdm.service/.ui.activity.TestActivity#691'
2025-08-21 11:26:52.235  4396-4396  BufferQueueConsumer     com.dspread.mdm.service              D  [](id:112c00000002,api:0,p:-1,c:4396) connect: controlledByApp=false
2025-08-21 11:26:52.258  4396-4423  OpenGLRenderer          com.dspread.mdm.service              E  Unable to match the desired swap behavior.
2025-08-21 11:26:52.332  4396-4423  BLASTBufferQueue        com.dspread.mdm.service              D  [VRI[TestActivity]#2](f:0,a:1) acquireNextBufferLocked size=480x854 mFrameNumber=1 applyTransaction=true mTimestamp=13894956127827(auto) mPendingTransactions.size=0 graphicBufferId=18880676233241 transform=3
2025-08-21 11:26:57.074  4396-4423  GED                     com.dspread.mdm.service              I  ged_boost_gpu_freq, level 100, eOrigin 2, final_idx 2, oppidx_max 2, oppidx_min 0
2025-08-21 11:26:57.101  4396-4396  BLASTBufferQueue        com.dspread.mdm.service              D  [VRI[TestActivity]#2](f:0,a:1) destructor()
2025-08-21 11:26:57.102  4396-4396  BufferQueueConsumer     com.dspread.mdm.service              D  [VRI[TestActivity]#2(BLAST Consumer)2](id:112c00000002,api:0,p:-1,c:4396) disconnect
2025-08-21 11:26:57.138  4396-4423  GED                     com.dspread.mdm.service              I  ged_boost_gpu_freq, level 100, eOrigin 2, final_idx 2, oppidx_max 2, oppidx_min 0
2025-08-21 11:26:58.629  4396-4396  WindowOnBackDispatcher  com.dspread.mdm.service              W  sendCancelIfRunning: isInProgress=falsecallback=android.view.ViewRootImpl$$ExternalSyntheticLambda19@118157e
2025-08-21 11:26:58.638  4396-4423  GED                     com.dspread.mdm.service              I  ged_boost_gpu_freq, level 100, eOrigin 2, final_idx 2, oppidx_max 2, oppidx_min 0
2025-08-21 11:26:58.639  4396-4396  View                    com.dspread.mdm.service              D  [Warning] assignParent to null: this = DecorView@68b222d[TestActivity]
2025-08-21 11:26:58.648  4396-4396  InputTransport          com.dspread.mdm.service              D  Destroy ARC handle: 0xb1b84680
2025-08-21 11:26:58.655  4396-4396  ContextImpl             com.dspread.mdm.service              W  Calling a method in the system process without a qualified user: android.app.ContextImpl.startService:1899 android.content.ContextWrapper.startService:825 com.dspread.mdm.service.services.SmartMdmBackgroundService.onTaskRemoved:87 android.app.ActivityThread.handleServiceArgs:4864 android.app.ActivityThread.-$$Nest$mhandleServiceArgs:0 
2025-08-21 11:26:58.666  4396-4396  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] onStartCommand调用
2025-08-21 11:26:58.674  4396-4396  Platform                com.dspread.mdm.service              D  🔧 WakeLockManager 获取智能WakeLock成功，持续时间: 60000ms
2025-08-21 11:26:58.684   646-1594  BufferQueueDebug        surfaceflinger                       E  [ActivityRecord{9c73756 u0 com.dspread.mdm.service/.ui.activity.LockScreenActivity#719](this:0xa56f1c40,id:-1,api:0,p:-1,c:-1) id info cannot be read from 'ActivityRecord{9c73756 u0 com.dspread.mdm.service/.ui.activity.LockScreenActivity#719'
2025-08-21 11:26:58.684  4396-4396  Platform                com.dspread.mdm.service              D  🔧 WakeLockManager 设置WakeLock续期定时器: 60000ms
2025-08-21 11:26:58.770  4396-4407  System                  com.dspread.mdm.service              W  A resource failed to call close. 
2025-08-21 11:26:58.773  4396-4407  System                  com.dspread.mdm.service              W  A resource failed to call close. 
2025-08-21 11:26:58.778  4396-4407  System                  com.dspread.mdm.service              W  A resource failed to call close. 
2025-08-21 11:26:58.781  4396-4407  System                  com.dspread.mdm.service              W  A resource failed to call close. 
2025-08-21 11:26:59.275  4396-4396  WebSocket               com.dspread.mdm.service              I  🔧 C0903 被动式上送: location_geofence_change (被动: 9)
2025-08-21 11:26:59.344  4396-4396  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0903","serialNo":"01354090202503050399","request_time":"1755746819267","request_id":"1755746819267C0903","version":"1","data":{"hardwareInfo":[{"name":"CPU Usage","desc":"cpu:0.50"},{"name":"storage_usage_status","desc":" 4.58GB \/ 32.00GB ","state":"1"},{"name":"total_memory","desc":" 0.70GB \/ 1.86GB ","state":"1"},{"name":"androidVersion","desc":"14","state":"1"},{"name":"network_status","desc":"wifi","state":"1"},{"name":"OSVer","desc":"14","state":"1"}],"cardDeviceUsage":"[]","locationInfo":{"gps":{"longitude":"-999.0","latitude":"-999.0","satelliteCount":0,"satelliteCountInUsed":0,"satelliteAverSNR":"0.00","distance":"0.0","lockStatus":"1","lockMeter":"200","proId":"","proName":""}}},"myVersionName":"1.1.03.20250820.DSPREAD.MDM.SERVICE","terminalDate":"20250821112659"}
2025-08-21 11:27:09.311  4396-4396  Receiver                com.dspread.mdm.service              D  🔧 电源已断开
2025-08-21 11:27:09.316  4396-4396  WebSocket               com.dspread.mdm.service              I  🔧 C0902 主动式上送: charging_state_change (主动: 7)
2025-08-21 11:27:09.360  4396-4396  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0902","serialNo":"01354090202503050399","request_time":"1755746829341","request_id":"1755746829341C0902","version":"1","data":{"batteryLife":95,"batteryHealth":2,"temprature":"26.0","isCharging":"0","isLowBattery":"0","powerOff":"0","outOfBattery":"0"},"myVersionName":"1.1.03.20250820.DSPREAD.MDM.SERVICE","terminalDate":"20250821112709"}
2025-08-21 11:27:09.366  4396-4396  WebSocket               com.dspread.mdm.service              I  🔧 C0902 电池状态上送成功 (1, trigger=charging_state_change)
2025-08-21 11:27:11.643  4396-4396  Receiver                com.dspread.mdm.service              D  🔧 电源已连接
2025-08-21 11:27:11.649  4396-4396  WebSocket               com.dspread.mdm.service              I  🔧 C0902 主动式上送: charging_state_change (主动: 8)
2025-08-21 11:27:11.689  4396-4396  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0902","serialNo":"01354090202503050399","request_time":"1755746831673","request_id":"1755746831673C0902","version":"1","data":{"batteryLife":95,"batteryHealth":2,"temprature":"26.0","isCharging":"1","isLowBattery":"0","powerOff":"0","outOfBattery":"0"},"myVersionName":"1.1.03.20250820.DSPREAD.MDM.SERVICE","terminalDate":"20250821112711"}
2025-08-21 11:27:11.694  4396-4396  WebSocket               com.dspread.mdm.service              I  🔧 C0902 电池状态上送成功 (1, trigger=charging_state_change)
2025-08-21 11:27:21.468  4396-9795  WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 1 (第1个，待响应: 1)
2025-08-21 11:27:21.974  4396-9794  WebSocket               com.dspread.mdm.service              I  🔧 收到服务器 PONG 帧: 1 (待响应PING: 0)
2025-08-21 11:27:51.469  4396-9795  WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 2 (第2个，待响应: 1)
2025-08-21 11:27:51.977  4396-9794  WebSocket               com.dspread.mdm.service              I  🔧 收到服务器 PONG 帧: 2 (待响应PING: 0)
2025-08-21 11:28:14.450  4396-4396  Receiver                com.dspread.mdm.service              D  🔧 屏幕关闭
2025-08-21 11:28:14.456  4396-4396  WebSocket               com.dspread.mdm.service              W  ⚠️ 未知触发条件: screen_off，默认按被动式处理
2025-08-21 11:28:14.471  4396-4396  WebSocket               com.dspread.mdm.service              I  🔧 C0201 被动式上送拒绝: screen_off (拒绝: 8)
2025-08-21 11:28:14.480  4396-4396  WebSocket               com.dspread.mdm.service              I  🔧 C0201 设备状态上送被流量控制阻止: 被动事件 'screen_off' 在平衡模式 - 重要变化下未启用
2025-08-21 11:28:14.486  4396-4396  Receiver                com.dspread.mdm.service              D  🔧 屏幕关闭处理完成
2025-08-21 11:28:21.470  4396-9795  WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 3 (第3个，待响应: 1)
2025-08-21 11:28:21.879  4396-9794  WebSocket               com.dspread.mdm.service              I  🔧 收到服务器 PONG 帧: 3 (待响应PING: 0)
2025-08-21 11:28:51.471  4396-9795  WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 4 (第4个，待响应: 1)
2025-08-21 11:28:51.984  4396-9794  WebSocket               com.dspread.mdm.service              I  🔧 收到服务器 PONG 帧: 4 (待响应PING: 0)
2025-08-21 11:29:21.472  4396-9795  WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 5 (第5个，待响应: 1)
2025-08-21 11:29:22.090  4396-9794  WebSocket               com.dspread.mdm.service              I  🔧 收到服务器 PONG 帧: 5 (待响应PING: 0)
2025-08-21 11:29:51.472  4396-9795  WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 6 (第6个，待响应: 1)
2025-08-21 11:29:51.991  4396-9794  WebSocket               com.dspread.mdm.service              I  🔧 收到服务器 PONG 帧: 6 (待响应PING: 0)
2025-08-21 11:30:21.472  4396-9795  WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 7 (第7个，待响应: 1)
2025-08-21 11:30:22.097  4396-9794  WebSocket               com.dspread.mdm.service              I  🔧 收到服务器 PONG 帧: 7 (待响应PING: 0)
2025-08-21 11:30:51.473  4396-9795  WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 8 (第8个，待响应: 1)
2025-08-21 11:30:51.998  4396-9794  WebSocket               com.dspread.mdm.service              I  🔧 收到服务器 PONG 帧: 8 (待响应PING: 0)
2025-08-21 11:31:21.474  4396-9795  WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 9 (第9个，待响应: 1)
2025-08-21 11:31:22.103  4396-9794  WebSocket               com.dspread.mdm.service              I  🔧 收到服务器 PONG 帧: 9 (待响应PING: 0)
2025-08-21 11:31:51.475  4396-9795  WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 10 (第10个，待响应: 1)
2025-08-21 11:31:52.005  4396-9794  WebSocket               com.dspread.mdm.service              I  🔧 收到服务器 PONG 帧: 10 (待响应PING: 0)
2025-08-21 11:32:21.476  4396-9795  WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 11 (第11个，待响应: 1)
2025-08-21 11:32:22.110  4396-9794  WebSocket               com.dspread.mdm.service              I  🔧 收到服务器 PONG 帧: 11 (待响应PING: 0)
