--------- beginning of system
--------- beginning of main
2025-08-21 14:03:33.987  2772-3260  WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误 (Ask Gemini)
                                                                                                    com.neovisionaries.ws.client.NoMoreFrameException: No more WebSocket frame from the server.
                                                                                                    	at com.neovisionaries.ws.client.WebSocketInputStream.readFrame(WebSocketInputStream.java:54)
                                                                                                    	at com.neovisionaries.ws.client.ReadingThread.readFrame(ReadingThread.java:338)
                                                                                                    	at com.neovisionaries.ws.client.ReadingThread.main(ReadingThread.java:99)
                                                                                                    	at com.neovisionaries.ws.client.ReadingThread.runMain(ReadingThread.java:64)
                                                                                                    	at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45)
2025-08-21 14:03:33.999  2772-3260  WebSocket               com.dspread.mdm.service              I  🔧 onError: 不触发重连，等待 onDisconnected 处理
2025-08-21 14:03:34.034  2772-3261  WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误 (Ask Gemini)
                                                                                                    com.neovisionaries.ws.client.WebSocketException: Flushing frames to the server failed: Socket closed
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.doFlush(WritingThread.java:434)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.sendFrames(WritingThread.java:384)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.main(WritingThread.java:108)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.runMain(WritingThread.java:55)
                                                                                                    	at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45)
                                                                                                    Caused by: java.net.SocketException: Socket closed
                                                                                                    	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.writeInternal(ConscryptEngineSocket.java:728)
                                                                                                    	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.write(ConscryptEngineSocket.java:699)
                                                                                                    	at java.io.BufferedOutputStream.flushBuffer(BufferedOutputStream.java:81)
                                                                                                    	at java.io.BufferedOutputStream.flush(BufferedOutputStream.java:142)
                                                                                                    	at java.io.BufferedOutputStream.flush(BufferedOutputStream.java:143)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.flush(WritingThread.java:271)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.doFlush(WritingThread.java:422)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.sendFrames(WritingThread.java:384) 
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.main(WritingThread.java:108) 
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.runMain(WritingThread.java:55) 
                                                                                                    	at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45) 
2025-08-21 14:03:34.044  2772-3261  WebSocket               com.dspread.mdm.service              I  🔧 onError: 不触发重连，等待 onDisconnected 处理
2025-08-21 14:03:34.057  2772-3261  WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误 (Ask Gemini)
                                                                                                    com.neovisionaries.ws.client.WebSocketException: Flushing frames to the server failed: Socket closed
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.doFlush(WritingThread.java:434)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.sendFrames(WritingThread.java:384)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.main(WritingThread.java:120)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.runMain(WritingThread.java:55)
                                                                                                    	at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45)
                                                                                                    Caused by: java.net.SocketException: Socket closed
                                                                                                    	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.writeInternal(ConscryptEngineSocket.java:728)
                                                                                                    	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.write(ConscryptEngineSocket.java:699)
                                                                                                    	at java.io.BufferedOutputStream.flushBuffer(BufferedOutputStream.java:81)
                                                                                                    	at java.io.BufferedOutputStream.flush(BufferedOutputStream.java:142)
                                                                                                    	at java.io.BufferedOutputStream.flush(BufferedOutputStream.java:143)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.flush(WritingThread.java:271)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.doFlush(WritingThread.java:422)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.sendFrames(WritingThread.java:384) 
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.main(WritingThread.java:120) 
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.runMain(WritingThread.java:55) 
                                                                                                    	at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45) 
2025-08-21 14:03:34.064  2772-3261  WebSocket               com.dspread.mdm.service              I  🔧 onError: 不触发重连，等待 onDisconnected 处理
2025-08-21 14:03:34.071  2772-3261  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接断开 - 客户端主动断开
2025-08-21 14:03:34.077  2772-3261  WebSocket               com.dspread.mdm.service              I  🔧    服务器关闭帧: code=-1, reason=无原因
2025-08-21 14:03:34.083  2772-3261  WebSocket               com.dspread.mdm.service              I  🔧    客户端关闭帧: code=1002, reason=No more WebSocket frame from the server.
2025-08-21 14:03:34.088  2772-3261  WebSocket               com.dspread.mdm.service              I  🔧    断开发起方: 客户端
2025-08-21 14:03:34.093  2772-3261  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 业务连接断开
2025-08-21 14:03:34.101  2772-3261  Platform                com.dspread.mdm.service              D  🔧 WakeLockManager 释放WiFi锁成功
2025-08-21 14:03:34.106  2772-3261  WebSocket               com.dspread.mdm.service              I  🔧 停止PING/PONG检测
2025-08-21 14:03:34.112  2772-3261  WebSocket               com.dspread.mdm.service              I  🔧 reconnect() 被调用，调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onDisconnected:746
2025-08-21 14:03:34.117  2772-3261  WebSocket               com.dspread.mdm.service              I  🔧 开始重连 WebSocket... (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onDisconnected:746)
2025-08-21 14:03:34.132  2772-3261  WebSocket               com.dspread.mdm.service              I  🔧 使用Provisioning配置的重连策略: delayPolicy=1, delaySwitch=1, delayTime=60
2025-08-21 14:03:34.137  2772-3261  WebSocket               com.dspread.mdm.service              I  🔧 当前断开记录数: 10, 延迟开关: 1
2025-08-21 14:03:34.147  2772-3261  WebSocket               com.dspread.mdm.service              I  🔧 断开记录 最早一次断开: 2025-08-21 13:50:11
2025-08-21 14:03:34.157  2772-3261  WebSocket               com.dspread.mdm.service              I  🔧 断开记录 最后一次断开: 2025-08-21 14:03:34
2025-08-21 14:03:34.162  2772-3261  WebSocket               com.dspread.mdm.service              I  🔧 时间差: 13分钟 (配置阈值: 1分钟)
2025-08-21 14:03:34.167  2772-3261  WebSocket               com.dspread.mdm.service              I  🔧 断开间隔正常，使用标准重连策略
2025-08-21 14:03:34.172  2772-3261  WebSocket               com.dspread.mdm.service              I  🔧 重连策略计算完成: 第1次重连，间隔3000ms (3秒)
2025-08-21 14:03:34.177  2772-3261  WebSocket               com.dspread.mdm.service              I  🔧 开始第1次重连，间隔3000ms (3秒) (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onDisconnected:746)
2025-08-21 14:03:59.369  2772-2772  WebSocket               com.dspread.mdm.service              I  🔧 重连 WebSocket: wss://api.dspreadserv.net/status/websocket/register?serialNo=MDEzNTQwOTAyMDI1MDMwNTAzOTk=&publicKey=TUlHZk1BMEdDU3FHU0liM0RRRUJBUVVBQTRHTkFEQ0JpUUtCZ1FEUFBSaDVCRDhCTldJNWtvUFlTVDdrSVI5a015QkJ0S2t6QXhjdmRpdUNhUUdUMFpVbTNQQjUwME12RENNTi9kZ1FncmNPNEI5cnFXYzBLam5nbDJKbVlKQVJXNW5sdndTakd3TUZDR1l3OFRrRG5CV2JLZHduNUpUc2w0ZFhOWlNvQUNjZEJ6cjlrTEE3WlFGNlFBZDFXVjdtUU1QaVAxMzk1a2VGOXpVdXZRSURBUUFC&query=0&msgVer=3&timestamp=1755756239339&signature=PGGWxb3GPwZca9aUE2ulhTHfB9JfJ+faeTJNe/4AgvurFk90dsAF5h20YabL0VRbQzTaNatJHRmEMuajfUmrTXt79V7On05oCZbJbfO2cuQ9S6CmXjNCuW8p1He6MbrDbM+pFeZ690zNEFR3v8xpe4pw40+wQ3dr+Yy3L56YwjY=
2025-08-21 14:03:59.379  2772-2772  WebSocket               com.dspread.mdm.service              I  🔧 未检测到系统代理设置
2025-08-21 14:04:00.414  2772-3269  TrafficStats            com.dspread.mdm.service              D  tagSocket(86) with statsTag=0xffffffff, statsUid=-1
2025-08-21 14:04:00.666  2772-3270  TrafficStats            com.dspread.mdm.service              D  tagSocket(96) with statsTag=0xffffffff, statsUid=-1
2025-08-21 14:04:03.813  2772-3271  Common                  com.dspread.mdm.service              I  ✅ WebSocket 物理连接成功，等待服务器 hellMsg
2025-08-21 14:04:03.823  2772-3271  WebSocket               com.dspread.mdm.service              I  🔧 ===== 心跳发送器实际配置 =====
2025-08-21 14:04:03.832  2772-3271  WebSocket               com.dspread.mdm.service              I  🔧 实际 PING 发送间隔: 30000ms (30秒)
2025-08-21 14:04:03.840  2772-3271  WebSocket               com.dspread.mdm.service              I  🔧 PING 定时器名称: PingSender
2025-08-21 14:04:03.847  2772-3271  WebSocket               com.dspread.mdm.service              I  🔧 实际 PONG 发送间隔: 0ms (0秒)
2025-08-21 14:04:03.854  2772-3271  WebSocket               com.dspread.mdm.service              I  🔧 PONG 定时器名称: PongSender
2025-08-21 14:04:03.860  2772-3271  WebSocket               com.dspread.mdm.service              I  🔧 ===============================
2025-08-21 14:04:03.866  2772-3271  WebSocket               com.dspread.mdm.service              I  🔧 启动PING/PONG检测，阈值: 连续3个PING无PONG响应
2025-08-21 14:04:03.902  2772-3271  Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"hellMsg":"hello 01354090202503050399","tranCode":"S0000","version":"1","rebootTime":"03:18:59","serialNo":"01354090202503050399","deviceStatus":"6"}
2025-08-21 14:04:03.908  2772-3271  Common                  com.dspread.mdm.service              I  ✅ 收到服务器 hellMsg，WebSocket 业务连接成功！
2025-08-21 14:04:03.913  2772-3271  Common                  com.dspread.mdm.service              I  ✅ WebSocket 业务连接成功！
2025-08-21 14:04:03.918  2772-3271  WebSocket               com.dspread.mdm.service              I  🔧 业务连接成功，重连计数已重置为 0
2025-08-21 14:04:03.926  2772-3271  Platform                com.dspread.mdm.service              D  🔧 WakeLockManager 获取WiFi锁成功，防止WiFi休眠
2025-08-21 14:04:03.931  2772-3271  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接成功，检查自身更新任务状态
2025-08-21 14:04:03.940  2772-3271  Receiver                com.dspread.mdm.service              D  🔧 发送广播: com.dspread.mdm.service.CHECK_SELF_UPDATE_STATUS
2025-08-21 14:04:03.946  2772-3271  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接成功
2025-08-21 14:04:03.951  2772-3271  WebSocket               com.dspread.mdm.service              I  🔧 设备状态: 6
2025-08-21 14:04:03.957  2772-3271  WebSocket               com.dspread.mdm.service              I  🔧 检查首次连接标志: isFirstTimeWebSocketConnected=false
2025-08-21 14:04:03.962  2772-3271  WebSocket               com.dspread.mdm.service              I  🔧 非首次连接，跳过初始化信息发送和初始化检查
2025-08-21 14:04:03.967  2772-3271  WebSocket               com.dspread.mdm.service              I  🔧 执行必要的状态同步检查
2025-08-21 14:04:03.972  2772-3271  WebSocket               com.dspread.mdm.service              I  🔧 检查服务更新结果
2025-08-21 14:04:03.977  2772-3271  Task                    com.dspread.mdm.service              W  ⚠️ 任务列表为空，跳过服务更新结果检查
2025-08-21 14:04:03.982  2772-3271  WebSocket               com.dspread.mdm.service              I  🔧 检查OS升级状态...
2025-08-21 14:04:03.987  2772-3271  Common                  com.dspread.mdm.service              D  🔧 OsUpdateStatusChecker WebSocket连接成功，检查OS升级状态
2025-08-21 14:04:03.993  2772-3271  Common                  com.dspread.mdm.service              D  🔧 OsUpdateStatusChecker 没有待检查的OS升级任务
2025-08-21 14:04:03.998  2772-3271  WebSocket               com.dspread.mdm.service              I  🔧 OS升级状态检查已触发
2025-08-21 14:05:03.888  2772-3271  WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误 (Ask Gemini)
                                                                                                    com.neovisionaries.ws.client.NoMoreFrameException: No more WebSocket frame from the server.
                                                                                                    	at com.neovisionaries.ws.client.WebSocketInputStream.readFrame(WebSocketInputStream.java:54)
                                                                                                    	at com.neovisionaries.ws.client.ReadingThread.readFrame(ReadingThread.java:338)
                                                                                                    	at com.neovisionaries.ws.client.ReadingThread.main(ReadingThread.java:99)
                                                                                                    	at com.neovisionaries.ws.client.ReadingThread.runMain(ReadingThread.java:64)
                                                                                                    	at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45)
2025-08-21 14:05:03.899  2772-3271  WebSocket               com.dspread.mdm.service              I  🔧 onError: 不触发重连，等待 onDisconnected 处理
2025-08-21 14:05:03.923  2772-3272  WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误 (Ask Gemini)
                                                                                                    com.neovisionaries.ws.client.WebSocketException: Flushing frames to the server failed: Socket closed
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.doFlush(WritingThread.java:434)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.sendFrames(WritingThread.java:384)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.main(WritingThread.java:108)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.runMain(WritingThread.java:55)
                                                                                                    	at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45)
                                                                                                    Caused by: java.net.SocketException: Socket closed
                                                                                                    	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.writeInternal(ConscryptEngineSocket.java:728)
                                                                                                    	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.write(ConscryptEngineSocket.java:699)
                                                                                                    	at java.io.BufferedOutputStream.flushBuffer(BufferedOutputStream.java:81)
                                                                                                    	at java.io.BufferedOutputStream.flush(BufferedOutputStream.java:142)
                                                                                                    	at java.io.BufferedOutputStream.flush(BufferedOutputStream.java:143)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.flush(WritingThread.java:271)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.doFlush(WritingThread.java:422)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.sendFrames(WritingThread.java:384) 
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.main(WritingThread.java:108) 
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.runMain(WritingThread.java:55) 
                                                                                                    	at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45) 
2025-08-21 14:05:03.932  2772-3272  WebSocket               com.dspread.mdm.service              I  🔧 onError: 不触发重连，等待 onDisconnected 处理
2025-08-21 14:05:03.946  2772-3272  WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误 (Ask Gemini)
                                                                                                    com.neovisionaries.ws.client.WebSocketException: Flushing frames to the server failed: Socket closed
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.doFlush(WritingThread.java:434)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.sendFrames(WritingThread.java:384)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.main(WritingThread.java:120)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.runMain(WritingThread.java:55)
                                                                                                    	at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45)
                                                                                                    Caused by: java.net.SocketException: Socket closed
                                                                                                    	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.writeInternal(ConscryptEngineSocket.java:728)
                                                                                                    	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.write(ConscryptEngineSocket.java:699)
                                                                                                    	at java.io.BufferedOutputStream.flushBuffer(BufferedOutputStream.java:81)
                                                                                                    	at java.io.BufferedOutputStream.flush(BufferedOutputStream.java:142)
                                                                                                    	at java.io.BufferedOutputStream.flush(BufferedOutputStream.java:143)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.flush(WritingThread.java:271)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.doFlush(WritingThread.java:422)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.sendFrames(WritingThread.java:384) 
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.main(WritingThread.java:120) 
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.runMain(WritingThread.java:55) 
                                                                                                    	at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45) 
2025-08-21 14:05:03.954  2772-3272  WebSocket               com.dspread.mdm.service              I  🔧 onError: 不触发重连，等待 onDisconnected 处理
2025-08-21 14:05:03.962  2772-3272  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接断开 - 客户端主动断开
2025-08-21 14:05:03.968  2772-3272  WebSocket               com.dspread.mdm.service              I  🔧    服务器关闭帧: code=-1, reason=无原因
2025-08-21 14:05:03.974  2772-3272  WebSocket               com.dspread.mdm.service              I  🔧    客户端关闭帧: code=1002, reason=No more WebSocket frame from the server.
2025-08-21 14:05:03.979  2772-3272  WebSocket               com.dspread.mdm.service              I  🔧    断开发起方: 客户端
2025-08-21 14:05:03.985  2772-3272  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 业务连接断开
2025-08-21 14:05:03.992  2772-3272  Platform                com.dspread.mdm.service              D  🔧 WakeLockManager 释放WiFi锁成功
2025-08-21 14:05:03.998  2772-3272  WebSocket               com.dspread.mdm.service              I  🔧 停止PING/PONG检测
2025-08-21 14:05:04.003  2772-3272  WebSocket               com.dspread.mdm.service              I  🔧 reconnect() 被调用，调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onDisconnected:746
2025-08-21 14:05:04.008  2772-3272  WebSocket               com.dspread.mdm.service              I  🔧 开始重连 WebSocket... (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onDisconnected:746)
2025-08-21 14:05:04.023  2772-3272  WebSocket               com.dspread.mdm.service              I  🔧 使用Provisioning配置的重连策略: delayPolicy=1, delaySwitch=1, delayTime=60
2025-08-21 14:05:04.028  2772-3272  WebSocket               com.dspread.mdm.service              I  🔧 当前断开记录数: 10, 延迟开关: 1
2025-08-21 14:05:04.038  2772-3272  WebSocket               com.dspread.mdm.service              I  🔧 断开记录 最早一次断开: 2025-08-21 13:51:17
2025-08-21 14:05:04.048  2772-3272  WebSocket               com.dspread.mdm.service              I  🔧 断开记录 最后一次断开: 2025-08-21 14:05:03
2025-08-21 14:05:04.053  2772-3272  WebSocket               com.dspread.mdm.service              I  🔧 时间差: 13分钟 (配置阈值: 1分钟)
2025-08-21 14:05:04.058  2772-3272  WebSocket               com.dspread.mdm.service              I  🔧 断开间隔正常，使用标准重连策略
2025-08-21 14:05:04.064  2772-3272  WebSocket               com.dspread.mdm.service              I  🔧 重连策略计算完成: 第1次重连，间隔3000ms (3秒)
2025-08-21 14:05:04.069  2772-3272  WebSocket               com.dspread.mdm.service              I  🔧 开始第1次重连，间隔3000ms (3秒) (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onDisconnected:746)
2025-08-21 14:06:00.229  2772-2772  WebSocket               com.dspread.mdm.service              I  🔧 重连 WebSocket: wss://api.dspreadserv.net/status/websocket/register?serialNo=MDEzNTQwOTAyMDI1MDMwNTAzOTk=&publicKey=TUlHZk1BMEdDU3FHU0liM0RRRUJBUVVBQTRHTkFEQ0JpUUtCZ1FEUFBSaDVCRDhCTldJNWtvUFlTVDdrSVI5a015QkJ0S2t6QXhjdmRpdUNhUUdUMFpVbTNQQjUwME12RENNTi9kZ1FncmNPNEI5cnFXYzBLam5nbDJKbVlKQVJXNW5sdndTakd3TUZDR1l3OFRrRG5CV2JLZHduNUpUc2w0ZFhOWlNvQUNjZEJ6cjlrTEE3WlFGNlFBZDFXVjdtUU1QaVAxMzk1a2VGOXpVdXZRSURBUUFC&query=0&msgVer=3&timestamp=1755756360191&signature=ml2b51p2wTW/jD1EUyFrI6B7gNBgiRTCfex7t8W4Z1dpMk1VnpSpiP4BU4SuLJSi7xSXlbtoz5ENOK+21b7SUxYxXzv7YiLmn+NBMFI2V5P9f9UAl7E/aSQZW0PO+oJOu8WPSluGKY84DNxj9tD8+oQDmwq0u3EUAeSNfNnBmJs=
2025-08-21 14:06:00.237  2772-2772  WebSocket               com.dspread.mdm.service              I  🔧 未检测到系统代理设置
2025-08-21 14:06:00.323  2772-2787  System                  com.dspread.mdm.service              W  A resource failed to call close. 
2025-08-21 14:06:00.327  2772-2787  System                  com.dspread.mdm.service              W  A resource failed to call close. 
2025-08-21 14:06:00.446  2772-3277  TrafficStats            com.dspread.mdm.service              D  tagSocket(86) with statsTag=0xffffffff, statsUid=-1
2025-08-21 14:06:00.697  2772-3278  TrafficStats            com.dspread.mdm.service              D  tagSocket(89) with statsTag=0xffffffff, statsUid=-1
2025-08-21 14:06:02.498  2772-3279  Common                  com.dspread.mdm.service              I  ✅ WebSocket 物理连接成功，等待服务器 hellMsg
2025-08-21 14:06:02.506  2772-3279  WebSocket               com.dspread.mdm.service              I  🔧 ===== 心跳发送器实际配置 =====
2025-08-21 14:06:02.514  2772-3279  WebSocket               com.dspread.mdm.service              I  🔧 实际 PING 发送间隔: 30000ms (30秒)
2025-08-21 14:06:02.523  2772-3279  WebSocket               com.dspread.mdm.service              I  🔧 PING 定时器名称: PingSender
2025-08-21 14:06:02.532  2772-3279  WebSocket               com.dspread.mdm.service              I  🔧 实际 PONG 发送间隔: 0ms (0秒)
2025-08-21 14:06:02.540  2772-3279  WebSocket               com.dspread.mdm.service              I  🔧 PONG 定时器名称: PongSender
2025-08-21 14:06:02.548  2772-3279  WebSocket               com.dspread.mdm.service              I  🔧 ===============================
2025-08-21 14:06:02.555  2772-3279  WebSocket               com.dspread.mdm.service              I  🔧 启动PING/PONG检测，阈值: 连续3个PING无PONG响应
2025-08-21 14:06:02.622  2772-3279  Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"msg_state":"P","request_time":"1755756311471","data":{"geoInfo":{"wipeMin":"","modifyDate":"2025-08-21 06:05:11","endDate":"9999-12-31 23:59:59","roamingTag":"0","lockMin":"3","beginDate":"2024-08-21 06:05:11","isDefault":"1","lockMeter":"25","disableTag":"0","proId":"b8605d052cc945019e0ca09fbf63bf20","wipeStatus":"0","action":"A","proName":"Default","createDate":"2025-08-21 06:05:11"}},"tranCode":"ST007","request_id":"1755756311471ST007","version":"1","serialNo":"01354090202503050399"}
2025-08-21 14:06:02.630  2772-3279  WebSocket               com.dspread.mdm.service              I  🔧 准备发送响应确认 - requestId: 1755756311471ST007, needResponse: true
2025-08-21 14:06:02.641  2772-3279  WebSocket               com.dspread.mdm.service              I  🔧 非重要消息，不缓存: C0000
2025-08-21 14:06:02.654  2772-3279  WebSocket               com.dspread.mdm.service              I  🔧 非重要消息，不缓存: C0000
2025-08-21 14:06:02.659  2772-3279  WebSocket               com.dspread.mdm.service              I  🔧 已发送C0000响应确认: requestId=1755756311471ST007
2025-08-21 14:06:02.664  2772-3279  WebSocket               com.dspread.mdm.service              I  🔧 处理地理围栏配置消息 (ST007)
2025-08-21 14:06:02.674  2772-2855  Common                  com.dspread.mdm.service              D  🔧 发送C0000响应确认: requestId=1755756311471ST007, module=Geofence
2025-08-21 14:06:02.699  2772-3279  Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"hellMsg":"hello 01354090202503050399","tranCode":"S0000","version":"1","rebootTime":"03:01:49","serialNo":"01354090202503050399","deviceStatus":"6"}
2025-08-21 14:06:02.705  2772-3279  Common                  com.dspread.mdm.service              I  ✅ 收到服务器 hellMsg，WebSocket 业务连接成功！
2025-08-21 14:06:02.710  2772-3279  Common                  com.dspread.mdm.service              I  ✅ WebSocket 业务连接成功！
2025-08-21 14:06:02.715  2772-3279  WebSocket               com.dspread.mdm.service              I  🔧 业务连接成功，重连计数已重置为 0
2025-08-21 14:06:02.723  2772-3279  Platform                com.dspread.mdm.service              D  🔧 WakeLockManager 获取WiFi锁成功，防止WiFi休眠
2025-08-21 14:06:02.728  2772-3279  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接成功，检查自身更新任务状态
2025-08-21 14:06:02.735  2772-3279  Receiver                com.dspread.mdm.service              D  🔧 发送广播: com.dspread.mdm.service.CHECK_SELF_UPDATE_STATUS
2025-08-21 14:06:02.741  2772-3279  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接成功
2025-08-21 14:06:02.746  2772-3279  WebSocket               com.dspread.mdm.service              I  🔧 设备状态: 6
2025-08-21 14:06:02.748  2772-2855  WebSocket               com.dspread.mdm.service              I  🔧 C0903 被动式上送: location_geofence_change (被动: 8)
2025-08-21 14:06:02.752  2772-3279  WebSocket               com.dspread.mdm.service              I  🔧 检查首次连接标志: isFirstTimeWebSocketConnected=false
2025-08-21 14:06:02.757  2772-3279  WebSocket               com.dspread.mdm.service              I  🔧 非首次连接，跳过初始化信息发送和初始化检查
2025-08-21 14:06:02.762  2772-3279  WebSocket               com.dspread.mdm.service              I  🔧 执行必要的状态同步检查
2025-08-21 14:06:02.767  2772-3279  WebSocket               com.dspread.mdm.service              I  🔧 检查服务更新结果
2025-08-21 14:06:02.772  2772-3279  Task                    com.dspread.mdm.service              W  ⚠️ 任务列表为空，跳过服务更新结果检查
2025-08-21 14:06:02.777  2772-3279  WebSocket               com.dspread.mdm.service              I  🔧 检查OS升级状态...
2025-08-21 14:06:02.783  2772-3279  Common                  com.dspread.mdm.service              D  🔧 OsUpdateStatusChecker WebSocket连接成功，检查OS升级状态
2025-08-21 14:06:02.789  2772-3279  Common                  com.dspread.mdm.service              D  🔧 OsUpdateStatusChecker 没有待检查的OS升级任务
2025-08-21 14:06:02.794  2772-3279  WebSocket               com.dspread.mdm.service              I  🔧 OS升级状态检查已触发
2025-08-21 14:06:02.828  2772-2855  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0903","serialNo":"01354090202503050399","request_time":"1755756362737","request_id":"1755756362737C0903","version":"1","data":{"hardwareInfo":[{"name":"CPU Usage","desc":"cpu:0.50"},{"name":"storage_usage_status","desc":" 4.57GB \/ 32.00GB ","state":"1"},{"name":"total_memory","desc":" 0.62GB \/ 1.86GB ","state":"1"},{"name":"androidVersion","desc":"14","state":"1"},{"name":"network_status","desc":"wifi","state":"1"},{"name":"OSVer","desc":"14","state":"1"}],"cardDeviceUsage":"[]","locationInfo":{"gps":{"longitude":"-999.0","latitude":"-999.0","satelliteCount":0,"satelliteCountInUsed":0,"satelliteAverSNR":"0.00","distance":"0.0","lockStatus":"1","lockMeter":"25","proId":"b8605d052cc945019e0ca09fbf63bf20","proName":"Default"}}},"myVersionName":"1.1.03.20250821.DSPREAD.MDM.SERVICE","terminalDate":"20250821140602"}
2025-08-21 14:07:03.318  2772-3279  WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误 (Ask Gemini)
                                                                                                    com.neovisionaries.ws.client.NoMoreFrameException: No more WebSocket frame from the server.
                                                                                                    	at com.neovisionaries.ws.client.WebSocketInputStream.readFrame(WebSocketInputStream.java:54)
                                                                                                    	at com.neovisionaries.ws.client.ReadingThread.readFrame(ReadingThread.java:338)
                                                                                                    	at com.neovisionaries.ws.client.ReadingThread.main(ReadingThread.java:99)
                                                                                                    	at com.neovisionaries.ws.client.ReadingThread.runMain(ReadingThread.java:64)
                                                                                                    	at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45)
2025-08-21 14:07:03.334  2772-3279  WebSocket               com.dspread.mdm.service              I  🔧 onError: 不触发重连，等待 onDisconnected 处理
2025-08-21 14:07:03.359  2772-3280  WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误 (Ask Gemini)
                                                                                                    com.neovisionaries.ws.client.WebSocketException: Flushing frames to the server failed: Socket closed
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.doFlush(WritingThread.java:434)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.sendFrames(WritingThread.java:384)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.main(WritingThread.java:108)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.runMain(WritingThread.java:55)
                                                                                                    	at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45)
                                                                                                    Caused by: java.net.SocketException: Socket closed
                                                                                                    	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.writeInternal(ConscryptEngineSocket.java:728)
                                                                                                    	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.write(ConscryptEngineSocket.java:699)
                                                                                                    	at java.io.BufferedOutputStream.flushBuffer(BufferedOutputStream.java:81)
                                                                                                    	at java.io.BufferedOutputStream.flush(BufferedOutputStream.java:142)
                                                                                                    	at java.io.BufferedOutputStream.flush(BufferedOutputStream.java:143)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.flush(WritingThread.java:271)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.doFlush(WritingThread.java:422)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.sendFrames(WritingThread.java:384) 
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.main(WritingThread.java:108) 
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.runMain(WritingThread.java:55) 
                                                                                                    	at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45) 
2025-08-21 14:07:03.371  2772-3280  WebSocket               com.dspread.mdm.service              I  🔧 onError: 不触发重连，等待 onDisconnected 处理
2025-08-21 14:07:03.393  2772-3280  WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误 (Ask Gemini)
                                                                                                    com.neovisionaries.ws.client.WebSocketException: Flushing frames to the server failed: Socket closed
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.doFlush(WritingThread.java:434)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.sendFrames(WritingThread.java:384)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.main(WritingThread.java:120)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.runMain(WritingThread.java:55)
                                                                                                    	at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45)
                                                                                                    Caused by: java.net.SocketException: Socket closed
                                                                                                    	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.writeInternal(ConscryptEngineSocket.java:728)
                                                                                                    	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.write(ConscryptEngineSocket.java:699)
                                                                                                    	at java.io.BufferedOutputStream.flushBuffer(BufferedOutputStream.java:81)
                                                                                                    	at java.io.BufferedOutputStream.flush(BufferedOutputStream.java:142)
                                                                                                    	at java.io.BufferedOutputStream.flush(BufferedOutputStream.java:143)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.flush(WritingThread.java:271)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.doFlush(WritingThread.java:422)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.sendFrames(WritingThread.java:384) 
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.main(WritingThread.java:120) 
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.runMain(WritingThread.java:55) 
                                                                                                    	at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45) 
2025-08-21 14:07:03.404  2772-3280  WebSocket               com.dspread.mdm.service              I  🔧 onError: 不触发重连，等待 onDisconnected 处理
2025-08-21 14:07:03.414  2772-3280  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接断开 - 客户端主动断开
2025-08-21 14:07:03.420  2772-3280  WebSocket               com.dspread.mdm.service              I  🔧    服务器关闭帧: code=-1, reason=无原因
2025-08-21 14:07:03.533  2772-3280  WebSocket               com.dspread.mdm.service              I  🔧    客户端关闭帧: code=1002, reason=No more WebSocket frame from the server.
2025-08-21 14:07:03.538  2772-3280  WebSocket               com.dspread.mdm.service              I  🔧    断开发起方: 客户端
2025-08-21 14:07:03.543  2772-3280  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 业务连接断开
2025-08-21 14:07:03.550  2772-3280  Platform                com.dspread.mdm.service              D  🔧 WakeLockManager 释放WiFi锁成功
2025-08-21 14:07:03.556  2772-3280  WebSocket               com.dspread.mdm.service              I  🔧 停止PING/PONG检测
2025-08-21 14:07:03.561  2772-3280  WebSocket               com.dspread.mdm.service              I  🔧 reconnect() 被调用，调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onDisconnected:746
2025-08-21 14:07:03.566  2772-3280  WebSocket               com.dspread.mdm.service              I  🔧 开始重连 WebSocket... (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onDisconnected:746)
2025-08-21 14:07:03.581  2772-3280  WebSocket               com.dspread.mdm.service              I  🔧 使用Provisioning配置的重连策略: delayPolicy=1, delaySwitch=1, delayTime=60
2025-08-21 14:07:03.586  2772-3280  WebSocket               com.dspread.mdm.service              I  🔧 当前断开记录数: 10, 延迟开关: 1
2025-08-21 14:07:03.596  2772-3280  WebSocket               com.dspread.mdm.service              I  🔧 断开记录 最早一次断开: 2025-08-21 13:52:23
2025-08-21 14:07:03.605  2772-3280  WebSocket               com.dspread.mdm.service              I  🔧 断开记录 最后一次断开: 2025-08-21 14:07:03
2025-08-21 14:07:03.610  2772-3280  WebSocket               com.dspread.mdm.service              I  🔧 时间差: 14分钟 (配置阈值: 1分钟)
2025-08-21 14:07:03.615  2772-3280  WebSocket               com.dspread.mdm.service              I  🔧 断开间隔正常，使用标准重连策略
2025-08-21 14:07:03.620  2772-3280  WebSocket               com.dspread.mdm.service              I  🔧 重连策略计算完成: 第1次重连，间隔3000ms (3秒)
2025-08-21 14:07:03.626  2772-3280  WebSocket               com.dspread.mdm.service              I  🔧 开始第1次重连，间隔3000ms (3秒) (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onDisconnected:746)
2025-08-21 14:07:57.225  2772-2772  WebSocket               com.dspread.mdm.service              I  🔧 重连 WebSocket: wss://api.dspreadserv.net/status/websocket/register?serialNo=MDEzNTQwOTAyMDI1MDMwNTAzOTk=&publicKey=TUlHZk1BMEdDU3FHU0liM0RRRUJBUVVBQTRHTkFEQ0JpUUtCZ1FEUFBSaDVCRDhCTldJNWtvUFlTVDdrSVI5a015QkJ0S2t6QXhjdmRpdUNhUUdUMFpVbTNQQjUwME12RENNTi9kZ1FncmNPNEI5cnFXYzBLam5nbDJKbVlKQVJXNW5sdndTakd3TUZDR1l3OFRrRG5CV2JLZHduNUpUc2w0ZFhOWlNvQUNjZEJ6cjlrTEE3WlFGNlFBZDFXVjdtUU1QaVAxMzk1a2VGOXpVdXZRSURBUUFC&query=0&msgVer=3&timestamp=1755756477188&signature=qjvg8uHRnqm/y/CLyee6D3DoLK4R7qQe6V7iQV3S8EmTLedU2qYOip7N0VilZKZa2XV6HhAUrf7t9J+RG5bJ0UCm7GcSi3D27AJafNp3ylX+R5men3aL6TmlDWlO/BS2MB+Quv51arc9rww17aGLhJdnVxechJpNVup+RmPJulU=
2025-08-21 14:07:57.232  2772-2772  WebSocket               com.dspread.mdm.service              I  🔧 未检测到系统代理设置
2025-08-21 14:07:57.492  2772-3285  TrafficStats            com.dspread.mdm.service              D  tagSocket(86) with statsTag=0xffffffff, statsUid=-1
2025-08-21 14:07:57.742  2772-3286  TrafficStats            com.dspread.mdm.service              D  tagSocket(96) with statsTag=0xffffffff, statsUid=-1
2025-08-21 14:08:00.767  2772-3287  Common                  com.dspread.mdm.service              I  ✅ WebSocket 物理连接成功，等待服务器 hellMsg
2025-08-21 14:08:00.775  2772-3287  WebSocket               com.dspread.mdm.service              I  🔧 ===== 心跳发送器实际配置 =====
2025-08-21 14:08:00.782  2772-3287  WebSocket               com.dspread.mdm.service              I  🔧 实际 PING 发送间隔: 30000ms (30秒)
2025-08-21 14:08:00.787  2772-3287  WebSocket               com.dspread.mdm.service              I  🔧 PING 定时器名称: PingSender
2025-08-21 14:08:00.792  2772-3287  WebSocket               com.dspread.mdm.service              I  🔧 实际 PONG 发送间隔: 0ms (0秒)
2025-08-21 14:08:00.797  2772-3287  WebSocket               com.dspread.mdm.service              I  🔧 PONG 定时器名称: PongSender
2025-08-21 14:08:00.802  2772-3287  WebSocket               com.dspread.mdm.service              I  🔧 ===============================
2025-08-21 14:08:00.807  2772-3287  WebSocket               com.dspread.mdm.service              I  🔧 启动PING/PONG检测，阈值: 连续3个PING无PONG响应
2025-08-21 14:08:00.841  2772-3287  Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"hellMsg":"hello 01354090202503050399","tranCode":"S0000","version":"1","rebootTime":"01:57:05","serialNo":"01354090202503050399","deviceStatus":"6"}
2025-08-21 14:08:00.847  2772-3287  Common                  com.dspread.mdm.service              I  ✅ 收到服务器 hellMsg，WebSocket 业务连接成功！
2025-08-21 14:08:00.852  2772-3287  Common                  com.dspread.mdm.service              I  ✅ WebSocket 业务连接成功！
2025-08-21 14:08:00.857  2772-3287  WebSocket               com.dspread.mdm.service              I  🔧 业务连接成功，重连计数已重置为 0
2025-08-21 14:08:00.865  2772-3287  Platform                com.dspread.mdm.service              D  🔧 WakeLockManager 获取WiFi锁成功，防止WiFi休眠
2025-08-21 14:08:00.870  2772-3287  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接成功，检查自身更新任务状态
2025-08-21 14:08:00.878  2772-3287  Receiver                com.dspread.mdm.service              D  🔧 发送广播: com.dspread.mdm.service.CHECK_SELF_UPDATE_STATUS
2025-08-21 14:08:00.884  2772-3287  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接成功
2025-08-21 14:08:00.889  2772-3287  WebSocket               com.dspread.mdm.service              I  🔧 设备状态: 6
2025-08-21 14:08:00.894  2772-3287  WebSocket               com.dspread.mdm.service              I  🔧 检查首次连接标志: isFirstTimeWebSocketConnected=false
2025-08-21 14:08:00.899  2772-3287  WebSocket               com.dspread.mdm.service              I  🔧 非首次连接，跳过初始化信息发送和初始化检查
2025-08-21 14:08:00.904  2772-3287  WebSocket               com.dspread.mdm.service              I  🔧 执行必要的状态同步检查
2025-08-21 14:08:00.908  2772-3287  WebSocket               com.dspread.mdm.service              I  🔧 检查服务更新结果
2025-08-21 14:08:00.914  2772-3287  Task                    com.dspread.mdm.service              W  ⚠️ 任务列表为空，跳过服务更新结果检查
2025-08-21 14:08:00.918  2772-3287  WebSocket               com.dspread.mdm.service              I  🔧 检查OS升级状态...
2025-08-21 14:08:00.923  2772-3287  Common                  com.dspread.mdm.service              D  🔧 OsUpdateStatusChecker WebSocket连接成功，检查OS升级状态
2025-08-21 14:08:00.929  2772-3287  Common                  com.dspread.mdm.service              D  🔧 OsUpdateStatusChecker 没有待检查的OS升级任务
2025-08-21 14:08:00.934  2772-3287  WebSocket               com.dspread.mdm.service              I  🔧 OS升级状态检查已触发
2025-08-21 14:08:00.986  2772-3287  Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"msg_state":"P","request_time":"1755756311471","data":{"geoInfo":{"wipeMin":"","modifyDate":"2025-08-21 06:05:11","endDate":"9999-12-31 23:59:59","roamingTag":"0","lockMin":"3","beginDate":"2024-08-21 06:05:11","isDefault":"1","lockMeter":"25","disableTag":"0","proId":"b8605d052cc945019e0ca09fbf63bf20","wipeStatus":"0","action":"A","proName":"Default","createDate":"2025-08-21 06:05:11"}},"tranCode":"ST007","request_id":"1755756311471ST007","version":"1","serialNo":"01354090202503050399"}
2025-08-21 14:08:00.994  2772-3287  WebSocket               com.dspread.mdm.service              I  🔧 准备发送响应确认 - requestId: 1755756311471ST007, needResponse: true
2025-08-21 14:08:01.014  2772-3287  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0000","serialNo":"01354090202503050399","request_time":"1755756480998","request_id":"1755756480998C0000","version":"1","org_request_id":"1755756311471ST007","org_request_time":"1755756311471","response_state":"0","myVersionName":"1.1.03.20250821.DSPREAD.MDM.SERVICE","terminalDate":"20250821140801"}
2025-08-21 14:08:01.036  2772-3287  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0000","serialNo":"01354090202503050399","request_time":"1755756481021","request_id":"1755756481021C0000","version":"1","org_request_id":"1755756311471ST007","org_request_time":"1755756311471","response_state":"0","myVersionName":"1.1.03.20250821.DSPREAD.MDM.SERVICE","terminalDate":"20250821140801"}
2025-08-21 14:08:01.041  2772-3287  WebSocket               com.dspread.mdm.service              I  🔧 已发送C0000响应确认: requestId=1755756311471ST007
2025-08-21 14:08:01.046  2772-3287  WebSocket               com.dspread.mdm.service              I  🔧 处理地理围栏配置消息 (ST007)
2025-08-21 14:08:01.055  2772-2855  Common                  com.dspread.mdm.service              D  🔧 发送C0000响应确认: requestId=1755756311471ST007, module=Geofence
2025-08-21 14:09:02.404  2772-3287  WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误 (Ask Gemini)
                                                                                                    com.neovisionaries.ws.client.NoMoreFrameException: No more WebSocket frame from the server.
                                                                                                    	at com.neovisionaries.ws.client.WebSocketInputStream.readFrame(WebSocketInputStream.java:54)
                                                                                                    	at com.neovisionaries.ws.client.ReadingThread.readFrame(ReadingThread.java:338)
                                                                                                    	at com.neovisionaries.ws.client.ReadingThread.main(ReadingThread.java:99)
                                                                                                    	at com.neovisionaries.ws.client.ReadingThread.runMain(ReadingThread.java:64)
                                                                                                    	at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45)
2025-08-21 14:09:02.415  2772-3287  WebSocket               com.dspread.mdm.service              I  🔧 onError: 不触发重连，等待 onDisconnected 处理
2025-08-21 14:09:02.453  2772-3288  WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误 (Ask Gemini)
                                                                                                    com.neovisionaries.ws.client.WebSocketException: Flushing frames to the server failed: Socket closed
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.doFlush(WritingThread.java:434)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.sendFrames(WritingThread.java:384)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.main(WritingThread.java:108)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.runMain(WritingThread.java:55)
                                                                                                    	at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45)
                                                                                                    Caused by: java.net.SocketException: Socket closed
                                                                                                    	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.writeInternal(ConscryptEngineSocket.java:728)
                                                                                                    	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.write(ConscryptEngineSocket.java:699)
                                                                                                    	at java.io.BufferedOutputStream.flushBuffer(BufferedOutputStream.java:81)
                                                                                                    	at java.io.BufferedOutputStream.flush(BufferedOutputStream.java:142)
                                                                                                    	at java.io.BufferedOutputStream.flush(BufferedOutputStream.java:143)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.flush(WritingThread.java:271)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.doFlush(WritingThread.java:422)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.sendFrames(WritingThread.java:384) 
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.main(WritingThread.java:108) 
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.runMain(WritingThread.java:55) 
                                                                                                    	at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45) 
2025-08-21 14:09:02.460  2772-3288  WebSocket               com.dspread.mdm.service              I  🔧 onError: 不触发重连，等待 onDisconnected 处理
2025-08-21 14:09:02.472  2772-3288  WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误 (Ask Gemini)
                                                                                                    com.neovisionaries.ws.client.WebSocketException: Flushing frames to the server failed: Socket closed
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.doFlush(WritingThread.java:434)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.sendFrames(WritingThread.java:384)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.main(WritingThread.java:120)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.runMain(WritingThread.java:55)
                                                                                                    	at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45)
                                                                                                    Caused by: java.net.SocketException: Socket closed
                                                                                                    	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.writeInternal(ConscryptEngineSocket.java:728)
                                                                                                    	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.write(ConscryptEngineSocket.java:699)
                                                                                                    	at java.io.BufferedOutputStream.flushBuffer(BufferedOutputStream.java:81)
                                                                                                    	at java.io.BufferedOutputStream.flush(BufferedOutputStream.java:142)
                                                                                                    	at java.io.BufferedOutputStream.flush(BufferedOutputStream.java:143)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.flush(WritingThread.java:271)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.doFlush(WritingThread.java:422)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.sendFrames(WritingThread.java:384) 
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.main(WritingThread.java:120) 
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.runMain(WritingThread.java:55) 
                                                                                                    	at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45) 
2025-08-21 14:09:02.478  2772-3288  WebSocket               com.dspread.mdm.service              I  🔧 onError: 不触发重连，等待 onDisconnected 处理
2025-08-21 14:09:02.485  2772-3288  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接断开 - 客户端主动断开
2025-08-21 14:09:02.490  2772-3288  WebSocket               com.dspread.mdm.service              I  🔧    服务器关闭帧: code=-1, reason=无原因
2025-08-21 14:09:02.495  2772-3288  WebSocket               com.dspread.mdm.service              I  🔧    客户端关闭帧: code=1002, reason=No more WebSocket frame from the server.
2025-08-21 14:09:02.500  2772-3288  WebSocket               com.dspread.mdm.service              I  🔧    断开发起方: 客户端
2025-08-21 14:09:02.505  2772-3288  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 业务连接断开
2025-08-21 14:09:02.512  2772-3288  Platform                com.dspread.mdm.service              D  🔧 WakeLockManager 释放WiFi锁成功
2025-08-21 14:09:02.517  2772-3288  WebSocket               com.dspread.mdm.service              I  🔧 停止PING/PONG检测
2025-08-21 14:09:02.523  2772-3288  WebSocket               com.dspread.mdm.service              I  🔧 reconnect() 被调用，调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onDisconnected:746
2025-08-21 14:09:02.528  2772-3288  WebSocket               com.dspread.mdm.service              I  🔧 开始重连 WebSocket... (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onDisconnected:746)
2025-08-21 14:09:02.542  2772-3288  WebSocket               com.dspread.mdm.service              I  🔧 使用Provisioning配置的重连策略: delayPolicy=1, delaySwitch=1, delayTime=60
2025-08-21 14:09:02.548  2772-3288  WebSocket               com.dspread.mdm.service              I  🔧 当前断开记录数: 10, 延迟开关: 1
2025-08-21 14:09:02.557  2772-3288  WebSocket               com.dspread.mdm.service              I  🔧 断开记录 最早一次断开: 2025-08-21 13:53:31
2025-08-21 14:09:02.567  2772-3288  WebSocket               com.dspread.mdm.service              I  🔧 断开记录 最后一次断开: 2025-08-21 14:09:02
2025-08-21 14:09:02.572  2772-3288  WebSocket               com.dspread.mdm.service              I  🔧 时间差: 15分钟 (配置阈值: 1分钟)
2025-08-21 14:09:02.577  2772-3288  WebSocket               com.dspread.mdm.service              I  🔧 断开间隔正常，使用标准重连策略
2025-08-21 14:09:02.582  2772-3288  WebSocket               com.dspread.mdm.service              I  🔧 重连策略计算完成: 第1次重连，间隔3000ms (3秒)
2025-08-21 14:09:02.588  2772-3288  WebSocket               com.dspread.mdm.service              I  🔧 开始第1次重连，间隔3000ms (3秒) (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onDisconnected:746)
2025-08-21 14:10:03.256  2772-2772  WebSocket               com.dspread.mdm.service              I  🔧 重连 WebSocket: wss://api.dspreadserv.net/status/websocket/register?serialNo=MDEzNTQwOTAyMDI1MDMwNTAzOTk=&publicKey=TUlHZk1BMEdDU3FHU0liM0RRRUJBUVVBQTRHTkFEQ0JpUUtCZ1FEUFBSaDVCRDhCTldJNWtvUFlTVDdrSVI5a015QkJ0S2t6QXhjdmRpdUNhUUdUMFpVbTNQQjUwME12RENNTi9kZ1FncmNPNEI5cnFXYzBLam5nbDJKbVlKQVJXNW5sdndTakd3TUZDR1l3OFRrRG5CV2JLZHduNUpUc2w0ZFhOWlNvQUNjZEJ6cjlrTEE3WlFGNlFBZDFXVjdtUU1QaVAxMzk1a2VGOXpVdXZRSURBUUFC&query=0&msgVer=3&timestamp=1755756603225&signature=KPXabYrdGk3PwJMZLV7BSBPJy7ICE9T5AS/j2KITuMUwYrLhR4Gz34M/2VfqaT9awC9j3R8EZKAfFSR4nwDR75WDs8SKxlV4UmUJ0cCV2UjaUnvOrpi0RZUdQaWpld67fXlYJUyYu08VAtX1ocWOqjnI4VIOMjQATjEwutdHkgw=
2025-08-21 14:10:03.262  2772-2772  WebSocket               com.dspread.mdm.service              I  🔧 未检测到系统代理设置
2025-08-21 14:10:03.442  2772-3293  TrafficStats            com.dspread.mdm.service              D  tagSocket(96) with statsTag=0xffffffff, statsUid=-1
2025-08-21 14:10:03.693  2772-3294  TrafficStats            com.dspread.mdm.service              D  tagSocket(101) with statsTag=0xffffffff, statsUid=-1
2025-08-21 14:10:05.290  2772-3295  Common                  com.dspread.mdm.service              I  ✅ WebSocket 物理连接成功，等待服务器 hellMsg
2025-08-21 14:10:05.300  2772-3295  WebSocket               com.dspread.mdm.service              I  🔧 ===== 心跳发送器实际配置 =====
2025-08-21 14:10:05.308  2772-3295  WebSocket               com.dspread.mdm.service              I  🔧 实际 PING 发送间隔: 30000ms (30秒)
2025-08-21 14:10:05.316  2772-3295  WebSocket               com.dspread.mdm.service              I  🔧 PING 定时器名称: PingSender
2025-08-21 14:10:05.322  2772-3295  WebSocket               com.dspread.mdm.service              I  🔧 实际 PONG 发送间隔: 0ms (0秒)
2025-08-21 14:10:05.329  2772-3295  WebSocket               com.dspread.mdm.service              I  🔧 PONG 定时器名称: PongSender
2025-08-21 14:10:05.335  2772-3295  WebSocket               com.dspread.mdm.service              I  🔧 ===============================
2025-08-21 14:10:05.340  2772-3295  WebSocket               com.dspread.mdm.service              I  🔧 启动PING/PONG检测，阈值: 连续3个PING无PONG响应
2025-08-21 14:10:05.376  2772-3295  Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"hellMsg":"hello 01354090202503050399","tranCode":"S0000","version":"1","rebootTime":"01:09:51","serialNo":"01354090202503050399","deviceStatus":"6"}
2025-08-21 14:10:05.382  2772-3295  Common                  com.dspread.mdm.service              I  ✅ 收到服务器 hellMsg，WebSocket 业务连接成功！
2025-08-21 14:10:05.387  2772-3295  Common                  com.dspread.mdm.service              I  ✅ WebSocket 业务连接成功！
2025-08-21 14:10:05.391  2772-3295  WebSocket               com.dspread.mdm.service              I  🔧 业务连接成功，重连计数已重置为 0
2025-08-21 14:10:05.399  2772-3295  Platform                com.dspread.mdm.service              D  🔧 WakeLockManager 获取WiFi锁成功，防止WiFi休眠
2025-08-21 14:10:05.404  2772-3295  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接成功，检查自身更新任务状态
2025-08-21 14:10:05.411  2772-3295  Receiver                com.dspread.mdm.service              D  🔧 发送广播: com.dspread.mdm.service.CHECK_SELF_UPDATE_STATUS
2025-08-21 14:10:05.417  2772-3295  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接成功
2025-08-21 14:10:05.422  2772-3295  WebSocket               com.dspread.mdm.service              I  🔧 设备状态: 6
2025-08-21 14:10:05.427  2772-3295  WebSocket               com.dspread.mdm.service              I  🔧 检查首次连接标志: isFirstTimeWebSocketConnected=false
2025-08-21 14:10:05.431  2772-3295  WebSocket               com.dspread.mdm.service              I  🔧 非首次连接，跳过初始化信息发送和初始化检查
2025-08-21 14:10:05.436  2772-3295  WebSocket               com.dspread.mdm.service              I  🔧 执行必要的状态同步检查
2025-08-21 14:10:05.441  2772-3295  WebSocket               com.dspread.mdm.service              I  🔧 检查服务更新结果
2025-08-21 14:10:05.446  2772-3295  Task                    com.dspread.mdm.service              W  ⚠️ 任务列表为空，跳过服务更新结果检查
2025-08-21 14:10:05.451  2772-3295  WebSocket               com.dspread.mdm.service              I  🔧 检查OS升级状态...
2025-08-21 14:10:05.455  2772-3295  Common                  com.dspread.mdm.service              D  🔧 OsUpdateStatusChecker WebSocket连接成功，检查OS升级状态
2025-08-21 14:10:05.461  2772-3295  Common                  com.dspread.mdm.service              D  🔧 OsUpdateStatusChecker 没有待检查的OS升级任务
2025-08-21 14:10:05.466  2772-3295  WebSocket               com.dspread.mdm.service              I  🔧 OS升级状态检查已触发
2025-08-21 14:11:05.495  2772-3295  WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误 (Ask Gemini)
                                                                                                    com.neovisionaries.ws.client.NoMoreFrameException: No more WebSocket frame from the server.
                                                                                                    	at com.neovisionaries.ws.client.WebSocketInputStream.readFrame(WebSocketInputStream.java:54)
                                                                                                    	at com.neovisionaries.ws.client.ReadingThread.readFrame(ReadingThread.java:338)
                                                                                                    	at com.neovisionaries.ws.client.ReadingThread.main(ReadingThread.java:99)
                                                                                                    	at com.neovisionaries.ws.client.ReadingThread.runMain(ReadingThread.java:64)
                                                                                                    	at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45)
2025-08-21 14:11:05.503  2772-3295  WebSocket               com.dspread.mdm.service              I  🔧 onError: 不触发重连，等待 onDisconnected 处理
2025-08-21 14:11:05.524  2772-3296  WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误 (Ask Gemini)
                                                                                                    com.neovisionaries.ws.client.WebSocketException: Flushing frames to the server failed: Socket closed
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.doFlush(WritingThread.java:434)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.sendFrames(WritingThread.java:384)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.main(WritingThread.java:108)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.runMain(WritingThread.java:55)
                                                                                                    	at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45)
                                                                                                    Caused by: java.net.SocketException: Socket closed
                                                                                                    	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.writeInternal(ConscryptEngineSocket.java:728)
                                                                                                    	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.write(ConscryptEngineSocket.java:699)
                                                                                                    	at java.io.BufferedOutputStream.flushBuffer(BufferedOutputStream.java:81)
                                                                                                    	at java.io.BufferedOutputStream.flush(BufferedOutputStream.java:142)
                                                                                                    	at java.io.BufferedOutputStream.flush(BufferedOutputStream.java:143)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.flush(WritingThread.java:271)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.doFlush(WritingThread.java:422)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.sendFrames(WritingThread.java:384) 
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.main(WritingThread.java:108) 
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.runMain(WritingThread.java:55) 
                                                                                                    	at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45) 
2025-08-21 14:11:05.536  2772-3296  WebSocket               com.dspread.mdm.service              I  🔧 onError: 不触发重连，等待 onDisconnected 处理
2025-08-21 14:11:05.557  2772-3296  WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误 (Ask Gemini)
                                                                                                    com.neovisionaries.ws.client.WebSocketException: Flushing frames to the server failed: Socket closed
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.doFlush(WritingThread.java:434)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.sendFrames(WritingThread.java:384)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.main(WritingThread.java:120)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.runMain(WritingThread.java:55)
                                                                                                    	at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45)
                                                                                                    Caused by: java.net.SocketException: Socket closed
                                                                                                    	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.writeInternal(ConscryptEngineSocket.java:728)
                                                                                                    	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.write(ConscryptEngineSocket.java:699)
                                                                                                    	at java.io.BufferedOutputStream.flushBuffer(BufferedOutputStream.java:81)
                                                                                                    	at java.io.BufferedOutputStream.flush(BufferedOutputStream.java:142)
                                                                                                    	at java.io.BufferedOutputStream.flush(BufferedOutputStream.java:143)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.flush(WritingThread.java:271)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.doFlush(WritingThread.java:422)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.sendFrames(WritingThread.java:384) 
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.main(WritingThread.java:120) 
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.runMain(WritingThread.java:55) 
                                                                                                    	at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45) 
2025-08-21 14:11:05.568  2772-3296  WebSocket               com.dspread.mdm.service              I  🔧 onError: 不触发重连，等待 onDisconnected 处理
2025-08-21 14:11:05.577  2772-3296  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接断开 - 客户端主动断开
2025-08-21 14:11:05.584  2772-3296  WebSocket               com.dspread.mdm.service              I  🔧    服务器关闭帧: code=-1, reason=无原因
2025-08-21 14:11:05.596  2772-3296  WebSocket               com.dspread.mdm.service              I  🔧    客户端关闭帧: code=1002, reason=No more WebSocket frame from the server.
2025-08-21 14:11:05.602  2772-3296  WebSocket               com.dspread.mdm.service              I  🔧    断开发起方: 客户端
2025-08-21 14:11:05.608  2772-3296  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 业务连接断开
2025-08-21 14:11:05.617  2772-3296  Platform                com.dspread.mdm.service              D  🔧 WakeLockManager 释放WiFi锁成功
2025-08-21 14:11:05.623  2772-3296  WebSocket               com.dspread.mdm.service              I  🔧 停止PING/PONG检测
2025-08-21 14:11:05.629  2772-3296  WebSocket               com.dspread.mdm.service              I  🔧 reconnect() 被调用，调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onDisconnected:746
2025-08-21 14:11:05.635  2772-3296  WebSocket               com.dspread.mdm.service              I  🔧 开始重连 WebSocket... (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onDisconnected:746)
2025-08-21 14:11:05.649  2772-3296  WebSocket               com.dspread.mdm.service              I  🔧 使用Provisioning配置的重连策略: delayPolicy=1, delaySwitch=1, delayTime=60
2025-08-21 14:11:05.655  2772-3296  WebSocket               com.dspread.mdm.service              I  🔧 当前断开记录数: 10, 延迟开关: 1
2025-08-21 14:11:05.664  2772-3296  WebSocket               com.dspread.mdm.service              I  🔧 断开记录 最早一次断开: 2025-08-21 13:54:50
2025-08-21 14:11:05.674  2772-3296  WebSocket               com.dspread.mdm.service              I  🔧 断开记录 最后一次断开: 2025-08-21 14:11:05
2025-08-21 14:11:05.678  2772-3296  WebSocket               com.dspread.mdm.service              I  🔧 时间差: 16分钟 (配置阈值: 1分钟)
2025-08-21 14:11:05.683  2772-3296  WebSocket               com.dspread.mdm.service              I  🔧 断开间隔正常，使用标准重连策略
2025-08-21 14:11:05.688  2772-3296  WebSocket               com.dspread.mdm.service              I  🔧 重连策略计算完成: 第1次重连，间隔3000ms (3秒)
2025-08-21 14:11:05.694  2772-3296  WebSocket               com.dspread.mdm.service              I  🔧 开始第1次重连，间隔3000ms (3秒) (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onDisconnected:746)
2025-08-21 14:11:29.276  2772-2787  System                  com.dspread.mdm.service              W  A resource failed to call close. 
2025-08-21 14:11:29.286  2772-2787  System                  com.dspread.mdm.service              W  A resource failed to call close. 
2025-08-21 14:11:29.289  2772-2787  System                  com.dspread.mdm.service              W  A resource failed to call close. 
2025-08-21 14:11:30.658  2772-2772  WebSocket               com.dspread.mdm.service              I  🔧 重连 WebSocket: wss://api.dspreadserv.net/status/websocket/register?serialNo=MDEzNTQwOTAyMDI1MDMwNTAzOTk=&publicKey=TUlHZk1BMEdDU3FHU0liM0RRRUJBUVVBQTRHTkFEQ0JpUUtCZ1FEUFBSaDVCRDhCTldJNWtvUFlTVDdrSVI5a015QkJ0S2t6QXhjdmRpdUNhUUdUMFpVbTNQQjUwME12RENNTi9kZ1FncmNPNEI5cnFXYzBLam5nbDJKbVlKQVJXNW5sdndTakd3TUZDR1l3OFRrRG5CV2JLZHduNUpUc2w0ZFhOWlNvQUNjZEJ6cjlrTEE3WlFGNlFBZDFXVjdtUU1QaVAxMzk1a2VGOXpVdXZRSURBUUFC&query=0&msgVer=3&timestamp=1755756690629&signature=vnSnaWGGW6z7rXmEp8MolNVLKP3KcZYXewO8wHBHyY25tMR1MkE5nMrV1dPhQp2b4ZVieTV7ONz8IpjIggvMbi4xVZpXdDNOhAqy4Vj5GpZGbl/rKQ2pBDvod3jryqmuoe4qQEhc7ZCog4kTqRtecHzQiEyvhI+vkzPu/uTmi04=
2025-08-21 14:11:30.666  2772-2772  WebSocket               com.dspread.mdm.service              I  🔧 未检测到系统代理设置
2025-08-21 14:11:30.891  2772-3301  TrafficStats            com.dspread.mdm.service              D  tagSocket(86) with statsTag=0xffffffff, statsUid=-1
2025-08-21 14:11:31.143  2772-3302  TrafficStats            com.dspread.mdm.service              D  tagSocket(89) with statsTag=0xffffffff, statsUid=-1
2025-08-21 14:11:33.248  2772-3303  Common                  com.dspread.mdm.service              I  ✅ WebSocket 物理连接成功，等待服务器 hellMsg
2025-08-21 14:11:33.256  2772-3303  WebSocket               com.dspread.mdm.service              I  🔧 ===== 心跳发送器实际配置 =====
2025-08-21 14:11:33.264  2772-3303  WebSocket               com.dspread.mdm.service              I  🔧 实际 PING 发送间隔: 30000ms (30秒)
2025-08-21 14:11:33.272  2772-3303  WebSocket               com.dspread.mdm.service              I  🔧 PING 定时器名称: PingSender
2025-08-21 14:11:33.279  2772-3303  WebSocket               com.dspread.mdm.service              I  🔧 实际 PONG 发送间隔: 0ms (0秒)
2025-08-21 14:11:33.285  2772-3303  WebSocket               com.dspread.mdm.service              I  🔧 PONG 定时器名称: PongSender
2025-08-21 14:11:33.291  2772-3303  WebSocket               com.dspread.mdm.service              I  🔧 ===============================
2025-08-21 14:11:33.297  2772-3303  WebSocket               com.dspread.mdm.service              I  🔧 启动PING/PONG检测，阈值: 连续3个PING无PONG响应
2025-08-21 14:11:33.334  2772-3303  Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"hellMsg":"hello 01354090202503050399","tranCode":"S0000","version":"1","rebootTime":"02:35:21","serialNo":"01354090202503050399","deviceStatus":"6"}
2025-08-21 14:11:33.339  2772-3303  Common                  com.dspread.mdm.service              I  ✅ 收到服务器 hellMsg，WebSocket 业务连接成功！
2025-08-21 14:11:33.344  2772-3303  Common                  com.dspread.mdm.service              I  ✅ WebSocket 业务连接成功！
2025-08-21 14:11:33.349  2772-3303  WebSocket               com.dspread.mdm.service              I  🔧 业务连接成功，重连计数已重置为 0
2025-08-21 14:11:33.357  2772-3303  Platform                com.dspread.mdm.service              D  🔧 WakeLockManager 获取WiFi锁成功，防止WiFi休眠
2025-08-21 14:11:33.362  2772-3303  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接成功，检查自身更新任务状态
2025-08-21 14:11:33.369  2772-3303  Receiver                com.dspread.mdm.service              D  🔧 发送广播: com.dspread.mdm.service.CHECK_SELF_UPDATE_STATUS
2025-08-21 14:11:33.375  2772-3303  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接成功
2025-08-21 14:11:33.379  2772-3303  WebSocket               com.dspread.mdm.service              I  🔧 设备状态: 6
2025-08-21 14:11:33.384  2772-3303  WebSocket               com.dspread.mdm.service              I  🔧 检查首次连接标志: isFirstTimeWebSocketConnected=false
2025-08-21 14:11:33.389  2772-3303  WebSocket               com.dspread.mdm.service              I  🔧 非首次连接，跳过初始化信息发送和初始化检查
2025-08-21 14:11:33.394  2772-3303  WebSocket               com.dspread.mdm.service              I  🔧 执行必要的状态同步检查
2025-08-21 14:11:33.399  2772-3303  WebSocket               com.dspread.mdm.service              I  🔧 检查服务更新结果
2025-08-21 14:11:33.404  2772-3303  Task                    com.dspread.mdm.service              W  ⚠️ 任务列表为空，跳过服务更新结果检查
2025-08-21 14:11:33.408  2772-3303  WebSocket               com.dspread.mdm.service              I  🔧 检查OS升级状态...
2025-08-21 14:11:33.413  2772-3303  Common                  com.dspread.mdm.service              D  🔧 OsUpdateStatusChecker WebSocket连接成功，检查OS升级状态
2025-08-21 14:11:33.418  2772-3303  Common                  com.dspread.mdm.service              D  🔧 OsUpdateStatusChecker 没有待检查的OS升级任务
2025-08-21 14:11:33.423  2772-3303  WebSocket               com.dspread.mdm.service              I  🔧 OS升级状态检查已触发
2025-08-21 14:12:33.611  2772-3303  WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误 (Ask Gemini)
                                                                                                    com.neovisionaries.ws.client.NoMoreFrameException: No more WebSocket frame from the server.
                                                                                                    	at com.neovisionaries.ws.client.WebSocketInputStream.readFrame(WebSocketInputStream.java:54)
                                                                                                    	at com.neovisionaries.ws.client.ReadingThread.readFrame(ReadingThread.java:338)
                                                                                                    	at com.neovisionaries.ws.client.ReadingThread.main(ReadingThread.java:99)
                                                                                                    	at com.neovisionaries.ws.client.ReadingThread.runMain(ReadingThread.java:64)
                                                                                                    	at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45)
2025-08-21 14:12:33.622  2772-3303  WebSocket               com.dspread.mdm.service              I  🔧 onError: 不触发重连，等待 onDisconnected 处理
2025-08-21 14:12:33.643  2772-3304  WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误 (Ask Gemini)
                                                                                                    com.neovisionaries.ws.client.WebSocketException: Flushing frames to the server failed: Socket closed
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.doFlush(WritingThread.java:434)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.sendFrames(WritingThread.java:384)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.main(WritingThread.java:108)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.runMain(WritingThread.java:55)
                                                                                                    	at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45)
                                                                                                    Caused by: java.net.SocketException: Socket closed
                                                                                                    	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.writeInternal(ConscryptEngineSocket.java:728)
                                                                                                    	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.write(ConscryptEngineSocket.java:699)
                                                                                                    	at java.io.BufferedOutputStream.flushBuffer(BufferedOutputStream.java:81)
                                                                                                    	at java.io.BufferedOutputStream.flush(BufferedOutputStream.java:142)
                                                                                                    	at java.io.BufferedOutputStream.flush(BufferedOutputStream.java:143)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.flush(WritingThread.java:271)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.doFlush(WritingThread.java:422)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.sendFrames(WritingThread.java:384) 
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.main(WritingThread.java:108) 
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.runMain(WritingThread.java:55) 
                                                                                                    	at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45) 
2025-08-21 14:12:33.653  2772-3304  WebSocket               com.dspread.mdm.service              I  🔧 onError: 不触发重连，等待 onDisconnected 处理
2025-08-21 14:12:33.668  2772-3304  WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误 (Ask Gemini)
                                                                                                    com.neovisionaries.ws.client.WebSocketException: Flushing frames to the server failed: Socket closed
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.doFlush(WritingThread.java:434)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.sendFrames(WritingThread.java:384)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.main(WritingThread.java:120)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.runMain(WritingThread.java:55)
                                                                                                    	at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45)
                                                                                                    Caused by: java.net.SocketException: Socket closed
                                                                                                    	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.writeInternal(ConscryptEngineSocket.java:728)
                                                                                                    	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.write(ConscryptEngineSocket.java:699)
                                                                                                    	at java.io.BufferedOutputStream.flushBuffer(BufferedOutputStream.java:81)
                                                                                                    	at java.io.BufferedOutputStream.flush(BufferedOutputStream.java:142)
                                                                                                    	at java.io.BufferedOutputStream.flush(BufferedOutputStream.java:143)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.flush(WritingThread.java:271)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.doFlush(WritingThread.java:422)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.sendFrames(WritingThread.java:384) 
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.main(WritingThread.java:120) 
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.runMain(WritingThread.java:55) 
                                                                                                    	at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45) 
2025-08-21 14:12:33.674  2772-3304  WebSocket               com.dspread.mdm.service              I  🔧 onError: 不触发重连，等待 onDisconnected 处理
2025-08-21 14:12:33.681  2772-3304  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接断开 - 客户端主动断开
2025-08-21 14:12:33.687  2772-3304  WebSocket               com.dspread.mdm.service              I  🔧    服务器关闭帧: code=-1, reason=无原因
2025-08-21 14:12:33.692  2772-3304  WebSocket               com.dspread.mdm.service              I  🔧    客户端关闭帧: code=1002, reason=No more WebSocket frame from the server.
2025-08-21 14:12:33.698  2772-3304  WebSocket               com.dspread.mdm.service              I  🔧    断开发起方: 客户端
2025-08-21 14:12:33.703  2772-3304  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 业务连接断开
2025-08-21 14:12:33.710  2772-3304  Platform                com.dspread.mdm.service              D  🔧 WakeLockManager 释放WiFi锁成功
2025-08-21 14:12:33.716  2772-3304  WebSocket               com.dspread.mdm.service              I  🔧 停止PING/PONG检测
2025-08-21 14:12:33.721  2772-3304  WebSocket               com.dspread.mdm.service              I  🔧 reconnect() 被调用，调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onDisconnected:746
2025-08-21 14:12:33.727  2772-3304  WebSocket               com.dspread.mdm.service              I  🔧 开始重连 WebSocket... (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onDisconnected:746)
2025-08-21 14:12:33.742  2772-3304  WebSocket               com.dspread.mdm.service              I  🔧 使用Provisioning配置的重连策略: delayPolicy=1, delaySwitch=1, delayTime=60
2025-08-21 14:12:33.747  2772-3304  WebSocket               com.dspread.mdm.service              I  🔧 当前断开记录数: 10, 延迟开关: 1
2025-08-21 14:12:33.757  2772-3304  WebSocket               com.dspread.mdm.service              I  🔧 断开记录 最早一次断开: 2025-08-21 13:56:53
2025-08-21 14:12:33.766  2772-3304  WebSocket               com.dspread.mdm.service              I  🔧 断开记录 最后一次断开: 2025-08-21 14:12:33
2025-08-21 14:12:33.771  2772-3304  WebSocket               com.dspread.mdm.service              I  🔧 时间差: 15分钟 (配置阈值: 1分钟)
2025-08-21 14:12:33.776  2772-3304  WebSocket               com.dspread.mdm.service              I  🔧 断开间隔正常，使用标准重连策略
2025-08-21 14:12:33.781  2772-3304  WebSocket               com.dspread.mdm.service              I  🔧 重连策略计算完成: 第1次重连，间隔3000ms (3秒)
2025-08-21 14:12:33.786  2772-3304  WebSocket               com.dspread.mdm.service              I  🔧 开始第1次重连，间隔3000ms (3秒) (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onDisconnected:746)
2025-08-21 14:12:36.831  2772-2772  WebSocket               com.dspread.mdm.service              I  🔧 重连 WebSocket: wss://api.dspreadserv.net/status/websocket/register?serialNo=MDEzNTQwOTAyMDI1MDMwNTAzOTk=&publicKey=TUlHZk1BMEdDU3FHU0liM0RRRUJBUVVBQTRHTkFEQ0JpUUtCZ1FEUFBSaDVCRDhCTldJNWtvUFlTVDdrSVI5a015QkJ0S2t6QXhjdmRpdUNhUUdUMFpVbTNQQjUwME12RENNTi9kZ1FncmNPNEI5cnFXYzBLam5nbDJKbVlKQVJXNW5sdndTakd3TUZDR1l3OFRrRG5CV2JLZHduNUpUc2w0ZFhOWlNvQUNjZEJ6cjlrTEE3WlFGNlFBZDFXVjdtUU1QaVAxMzk1a2VGOXpVdXZRSURBUUFC&query=0&msgVer=3&timestamp=1755756756801&signature=u++UOOH2JhoPQMh+bPI87YTnv90AgISomf9VT02osUI6uU1dx4mbh2B9WQQ/tTHQvk6s/voDeHf70EbVEWSFAGB2C7jK6agxwQQGtQEx3Ahjlj/COequ2TNZMyULlvwxPOcJDlhcd2mbdVQBb625pTCljfxXHmVNZkiFR7TdaDA=
2025-08-21 14:12:36.840  2772-2772  WebSocket               com.dspread.mdm.service              I  🔧 未检测到系统代理设置
2025-08-21 14:12:37.248  2772-3309  TrafficStats            com.dspread.mdm.service              D  tagSocket(86) with statsTag=0xffffffff, statsUid=-1
2025-08-21 14:12:37.498  2772-3310  TrafficStats            com.dspread.mdm.service              D  tagSocket(96) with statsTag=0xffffffff, statsUid=-1
2025-08-21 14:12:39.096  2772-3311  Common                  com.dspread.mdm.service              I  ✅ WebSocket 物理连接成功，等待服务器 hellMsg
2025-08-21 14:12:39.104  2772-3311  WebSocket               com.dspread.mdm.service              I  🔧 ===== 心跳发送器实际配置 =====
2025-08-21 14:12:39.111  2772-3311  WebSocket               com.dspread.mdm.service              I  🔧 实际 PING 发送间隔: 30000ms (30秒)
2025-08-21 14:12:39.117  2772-3311  WebSocket               com.dspread.mdm.service              I  🔧 PING 定时器名称: PingSender
2025-08-21 14:12:39.124  2772-3311  WebSocket               com.dspread.mdm.service              I  🔧 实际 PONG 发送间隔: 0ms (0秒)
2025-08-21 14:12:39.129  2772-3311  WebSocket               com.dspread.mdm.service              I  🔧 PONG 定时器名称: PongSender
2025-08-21 14:12:39.135  2772-3311  WebSocket               com.dspread.mdm.service              I  🔧 ===============================
2025-08-21 14:12:39.140  2772-3311  WebSocket               com.dspread.mdm.service              I  🔧 启动PING/PONG检测，阈值: 连续3个PING无PONG响应
2025-08-21 14:12:39.424  2772-3311  Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"hellMsg":"hello 01354090202503050399","tranCode":"S0000","version":"1","rebootTime":"02:32:01","serialNo":"01354090202503050399","deviceStatus":"6"}
2025-08-21 14:12:39.430  2772-3311  Common                  com.dspread.mdm.service              I  ✅ 收到服务器 hellMsg，WebSocket 业务连接成功！
2025-08-21 14:12:39.436  2772-3311  Common                  com.dspread.mdm.service              I  ✅ WebSocket 业务连接成功！
2025-08-21 14:12:39.442  2772-3311  WebSocket               com.dspread.mdm.service              I  🔧 业务连接成功，重连计数已重置为 0
2025-08-21 14:12:39.450  2772-3311  Platform                com.dspread.mdm.service              D  🔧 WakeLockManager 获取WiFi锁成功，防止WiFi休眠
2025-08-21 14:12:39.456  2772-3311  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接成功，检查自身更新任务状态
2025-08-21 14:12:39.464  2772-3311  Receiver                com.dspread.mdm.service              D  🔧 发送广播: com.dspread.mdm.service.CHECK_SELF_UPDATE_STATUS
2025-08-21 14:12:39.470  2772-3311  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接成功
2025-08-21 14:12:39.475  2772-3311  WebSocket               com.dspread.mdm.service              I  🔧 设备状态: 6
2025-08-21 14:12:39.480  2772-3311  WebSocket               com.dspread.mdm.service              I  🔧 检查首次连接标志: isFirstTimeWebSocketConnected=false
2025-08-21 14:12:39.485  2772-3311  WebSocket               com.dspread.mdm.service              I  🔧 非首次连接，跳过初始化信息发送和初始化检查
2025-08-21 14:12:39.489  2772-3311  WebSocket               com.dspread.mdm.service              I  🔧 执行必要的状态同步检查
2025-08-21 14:12:39.494  2772-3311  WebSocket               com.dspread.mdm.service              I  🔧 检查服务更新结果
2025-08-21 14:12:39.499  2772-3311  Task                    com.dspread.mdm.service              W  ⚠️ 任务列表为空，跳过服务更新结果检查
2025-08-21 14:12:39.504  2772-3311  WebSocket               com.dspread.mdm.service              I  🔧 检查OS升级状态...
2025-08-21 14:12:39.509  2772-3311  Common                  com.dspread.mdm.service              D  🔧 OsUpdateStatusChecker WebSocket连接成功，检查OS升级状态
2025-08-21 14:12:39.514  2772-3311  Common                  com.dspread.mdm.service              D  🔧 OsUpdateStatusChecker 没有待检查的OS升级任务
2025-08-21 14:12:39.519  2772-3311  WebSocket               com.dspread.mdm.service              I  🔧 OS升级状态检查已触发
2025-08-21 14:13:39.969  2772-3311  WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误 (Ask Gemini)
                                                                                                    com.neovisionaries.ws.client.NoMoreFrameException: No more WebSocket frame from the server.
                                                                                                    	at com.neovisionaries.ws.client.WebSocketInputStream.readFrame(WebSocketInputStream.java:54)
                                                                                                    	at com.neovisionaries.ws.client.ReadingThread.readFrame(ReadingThread.java:338)
                                                                                                    	at com.neovisionaries.ws.client.ReadingThread.main(ReadingThread.java:99)
                                                                                                    	at com.neovisionaries.ws.client.ReadingThread.runMain(ReadingThread.java:64)
                                                                                                    	at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45)
2025-08-21 14:13:39.978  2772-3311  WebSocket               com.dspread.mdm.service              I  🔧 onError: 不触发重连，等待 onDisconnected 处理
2025-08-21 14:13:40.002  2772-3312  WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误 (Ask Gemini)
                                                                                                    com.neovisionaries.ws.client.WebSocketException: Flushing frames to the server failed: Socket closed
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.doFlush(WritingThread.java:434)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.sendFrames(WritingThread.java:384)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.main(WritingThread.java:108)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.runMain(WritingThread.java:55)
                                                                                                    	at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45)
                                                                                                    Caused by: java.net.SocketException: Socket closed
                                                                                                    	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.writeInternal(ConscryptEngineSocket.java:728)
                                                                                                    	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.write(ConscryptEngineSocket.java:699)
                                                                                                    	at java.io.BufferedOutputStream.flushBuffer(BufferedOutputStream.java:81)
                                                                                                    	at java.io.BufferedOutputStream.flush(BufferedOutputStream.java:142)
                                                                                                    	at java.io.BufferedOutputStream.flush(BufferedOutputStream.java:143)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.flush(WritingThread.java:271)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.doFlush(WritingThread.java:422)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.sendFrames(WritingThread.java:384) 
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.main(WritingThread.java:108) 
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.runMain(WritingThread.java:55) 
                                                                                                    	at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45) 
2025-08-21 14:13:40.012  2772-3312  WebSocket               com.dspread.mdm.service              I  🔧 onError: 不触发重连，等待 onDisconnected 处理
2025-08-21 14:13:40.025  2772-3312  WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误 (Ask Gemini)
                                                                                                    com.neovisionaries.ws.client.WebSocketException: Flushing frames to the server failed: Socket closed
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.doFlush(WritingThread.java:434)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.sendFrames(WritingThread.java:384)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.main(WritingThread.java:120)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.runMain(WritingThread.java:55)
                                                                                                    	at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45)
                                                                                                    Caused by: java.net.SocketException: Socket closed
                                                                                                    	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.writeInternal(ConscryptEngineSocket.java:728)
                                                                                                    	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.write(ConscryptEngineSocket.java:699)
                                                                                                    	at java.io.BufferedOutputStream.flushBuffer(BufferedOutputStream.java:81)
                                                                                                    	at java.io.BufferedOutputStream.flush(BufferedOutputStream.java:142)
                                                                                                    	at java.io.BufferedOutputStream.flush(BufferedOutputStream.java:143)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.flush(WritingThread.java:271)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.doFlush(WritingThread.java:422)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.sendFrames(WritingThread.java:384) 
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.main(WritingThread.java:120) 
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.runMain(WritingThread.java:55) 
                                                                                                    	at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45) 
2025-08-21 14:13:40.032  2772-3312  WebSocket               com.dspread.mdm.service              I  🔧 onError: 不触发重连，等待 onDisconnected 处理
2025-08-21 14:13:40.040  2772-3312  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接断开 - 客户端主动断开
2025-08-21 14:13:40.045  2772-3312  WebSocket               com.dspread.mdm.service              I  🔧    服务器关闭帧: code=-1, reason=无原因
2025-08-21 14:13:40.051  2772-3312  WebSocket               com.dspread.mdm.service              I  🔧    客户端关闭帧: code=1002, reason=No more WebSocket frame from the server.
2025-08-21 14:13:40.056  2772-3312  WebSocket               com.dspread.mdm.service              I  🔧    断开发起方: 客户端
2025-08-21 14:13:40.061  2772-3312  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 业务连接断开
2025-08-21 14:13:40.068  2772-3312  Platform                com.dspread.mdm.service              D  🔧 WakeLockManager 释放WiFi锁成功
2025-08-21 14:13:40.073  2772-3312  WebSocket               com.dspread.mdm.service              I  🔧 停止PING/PONG检测
2025-08-21 14:13:40.079  2772-3312  WebSocket               com.dspread.mdm.service              I  🔧 reconnect() 被调用，调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onDisconnected:746
2025-08-21 14:13:40.084  2772-3312  WebSocket               com.dspread.mdm.service              I  🔧 开始重连 WebSocket... (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onDisconnected:746)
2025-08-21 14:13:40.098  2772-3312  WebSocket               com.dspread.mdm.service              I  🔧 使用Provisioning配置的重连策略: delayPolicy=1, delaySwitch=1, delayTime=60
2025-08-21 14:13:40.103  2772-3312  WebSocket               com.dspread.mdm.service              I  🔧 当前断开记录数: 10, 延迟开关: 1
2025-08-21 14:13:40.113  2772-3312  WebSocket               com.dspread.mdm.service              I  🔧 断开记录 最早一次断开: 2025-08-21 13:57:59
2025-08-21 14:13:40.122  2772-3312  WebSocket               com.dspread.mdm.service              I  🔧 断开记录 最后一次断开: 2025-08-21 14:13:40
2025-08-21 14:13:40.127  2772-3312  WebSocket               com.dspread.mdm.service              I  🔧 时间差: 15分钟 (配置阈值: 1分钟)
2025-08-21 14:13:40.132  2772-3312  WebSocket               com.dspread.mdm.service              I  🔧 断开间隔正常，使用标准重连策略
2025-08-21 14:13:40.137  2772-3312  WebSocket               com.dspread.mdm.service              I  🔧 重连策略计算完成: 第1次重连，间隔3000ms (3秒)
2025-08-21 14:13:40.142  2772-3312  WebSocket               com.dspread.mdm.service              I  🔧 开始第1次重连，间隔3000ms (3秒) (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onDisconnected:746)
2025-08-21 14:14:10.399  2772-2772  WebSocket               com.dspread.mdm.service              I  🔧 重连 WebSocket: wss://api.dspreadserv.net/status/websocket/register?serialNo=MDEzNTQwOTAyMDI1MDMwNTAzOTk=&publicKey=TUlHZk1BMEdDU3FHU0liM0RRRUJBUVVBQTRHTkFEQ0JpUUtCZ1FEUFBSaDVCRDhCTldJNWtvUFlTVDdrSVI5a015QkJ0S2t6QXhjdmRpdUNhUUdUMFpVbTNQQjUwME12RENNTi9kZ1FncmNPNEI5cnFXYzBLam5nbDJKbVlKQVJXNW5sdndTakd3TUZDR1l3OFRrRG5CV2JLZHduNUpUc2w0ZFhOWlNvQUNjZEJ6cjlrTEE3WlFGNlFBZDFXVjdtUU1QaVAxMzk1a2VGOXpVdXZRSURBUUFC&query=0&msgVer=3&timestamp=1755756850369&signature=WJy9zk/Rnv76RaX8mg5wOv0rkGAiXkmt6hvoFQOkzy0kTiG/v5Xu0hOuwBqHUk0iSPdWEPhfKUsSuIZbJgXdbBfZL4GFO83kgoDQ3xvsgmZcLQag69QylPwrJeiX5TdNsEgnR9CjnZ7OeVsEaMUkIamHcAPlmFNqAZWwxSaaATw=
2025-08-21 14:14:10.409  2772-2772  WebSocket               com.dspread.mdm.service              I  🔧 未检测到系统代理设置
2025-08-21 14:14:10.843  2772-3317  TrafficStats            com.dspread.mdm.service              D  tagSocket(86) with statsTag=0xffffffff, statsUid=-1
2025-08-21 14:14:11.094  2772-3318  TrafficStats            com.dspread.mdm.service              D  tagSocket(101) with statsTag=0xffffffff, statsUid=-1
2025-08-21 14:14:14.123  2772-3319  Common                  com.dspread.mdm.service              I  ✅ WebSocket 物理连接成功，等待服务器 hellMsg
2025-08-21 14:14:14.130  2772-3319  WebSocket               com.dspread.mdm.service              I  🔧 ===== 心跳发送器实际配置 =====
2025-08-21 14:14:14.139  2772-3319  WebSocket               com.dspread.mdm.service              I  🔧 实际 PING 发送间隔: 30000ms (30秒)
2025-08-21 14:14:14.148  2772-3319  WebSocket               com.dspread.mdm.service              I  🔧 PING 定时器名称: PingSender
2025-08-21 14:14:14.157  2772-3319  WebSocket               com.dspread.mdm.service              I  🔧 实际 PONG 发送间隔: 0ms (0秒)
2025-08-21 14:14:14.165  2772-3319  WebSocket               com.dspread.mdm.service              I  🔧 PONG 定时器名称: PongSender
2025-08-21 14:14:14.172  2772-3319  WebSocket               com.dspread.mdm.service              I  🔧 ===============================
2025-08-21 14:14:14.179  2772-3319  WebSocket               com.dspread.mdm.service              I  🔧 启动PING/PONG检测，阈值: 连续3个PING无PONG响应
2025-08-21 14:14:14.462  2772-3319  Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"hellMsg":"hello 01354090202503050399","tranCode":"S0000","version":"1","rebootTime":"01:00:13","serialNo":"01354090202503050399","deviceStatus":"6"}
2025-08-21 14:14:14.470  2772-3319  Common                  com.dspread.mdm.service              I  ✅ 收到服务器 hellMsg，WebSocket 业务连接成功！
2025-08-21 14:14:14.477  2772-3319  Common                  com.dspread.mdm.service              I  ✅ WebSocket 业务连接成功！
2025-08-21 14:14:14.483  2772-3319  WebSocket               com.dspread.mdm.service              I  🔧 业务连接成功，重连计数已重置为 0
2025-08-21 14:14:14.492  2772-3319  Platform                com.dspread.mdm.service              D  🔧 WakeLockManager 获取WiFi锁成功，防止WiFi休眠
2025-08-21 14:14:14.498  2772-3319  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接成功，检查自身更新任务状态
2025-08-21 14:14:14.507  2772-3319  Receiver                com.dspread.mdm.service              D  🔧 发送广播: com.dspread.mdm.service.CHECK_SELF_UPDATE_STATUS
2025-08-21 14:14:14.513  2772-3319  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接成功
2025-08-21 14:14:14.519  2772-3319  WebSocket               com.dspread.mdm.service              I  🔧 设备状态: 6
2025-08-21 14:14:14.525  2772-3319  WebSocket               com.dspread.mdm.service              I  🔧 检查首次连接标志: isFirstTimeWebSocketConnected=false
2025-08-21 14:14:14.530  2772-3319  WebSocket               com.dspread.mdm.service              I  🔧 非首次连接，跳过初始化信息发送和初始化检查
2025-08-21 14:14:14.535  2772-3319  WebSocket               com.dspread.mdm.service              I  🔧 执行必要的状态同步检查
2025-08-21 14:14:14.540  2772-3319  WebSocket               com.dspread.mdm.service              I  🔧 检查服务更新结果
2025-08-21 14:14:14.545  2772-3319  Task                    com.dspread.mdm.service              W  ⚠️ 任务列表为空，跳过服务更新结果检查
2025-08-21 14:14:14.549  2772-3319  WebSocket               com.dspread.mdm.service              I  🔧 检查OS升级状态...
2025-08-21 14:14:14.554  2772-3319  Common                  com.dspread.mdm.service              D  🔧 OsUpdateStatusChecker WebSocket连接成功，检查OS升级状态
2025-08-21 14:14:14.560  2772-3319  Common                  com.dspread.mdm.service              D  🔧 OsUpdateStatusChecker 没有待检查的OS升级任务
2025-08-21 14:14:14.565  2772-3319  WebSocket               com.dspread.mdm.service              I  🔧 OS升级状态检查已触发
2025-08-21 14:15:14.262  2772-3319  WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误 (Ask Gemini)
                                                                                                    com.neovisionaries.ws.client.NoMoreFrameException: No more WebSocket frame from the server.
                                                                                                    	at com.neovisionaries.ws.client.WebSocketInputStream.readFrame(WebSocketInputStream.java:54)
                                                                                                    	at com.neovisionaries.ws.client.ReadingThread.readFrame(ReadingThread.java:338)
                                                                                                    	at com.neovisionaries.ws.client.ReadingThread.main(ReadingThread.java:99)
                                                                                                    	at com.neovisionaries.ws.client.ReadingThread.runMain(ReadingThread.java:64)
                                                                                                    	at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45)
2025-08-21 14:15:14.274  2772-3319  WebSocket               com.dspread.mdm.service              I  🔧 onError: 不触发重连，等待 onDisconnected 处理
2025-08-21 14:15:14.299  2772-3320  WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误 (Ask Gemini)
                                                                                                    com.neovisionaries.ws.client.WebSocketException: Flushing frames to the server failed: Socket closed
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.doFlush(WritingThread.java:434)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.sendFrames(WritingThread.java:384)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.main(WritingThread.java:108)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.runMain(WritingThread.java:55)
                                                                                                    	at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45)
                                                                                                    Caused by: java.net.SocketException: Socket closed
                                                                                                    	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.writeInternal(ConscryptEngineSocket.java:728)
                                                                                                    	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.write(ConscryptEngineSocket.java:699)
                                                                                                    	at java.io.BufferedOutputStream.flushBuffer(BufferedOutputStream.java:81)
                                                                                                    	at java.io.BufferedOutputStream.flush(BufferedOutputStream.java:142)
                                                                                                    	at java.io.BufferedOutputStream.flush(BufferedOutputStream.java:143)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.flush(WritingThread.java:271)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.doFlush(WritingThread.java:422)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.sendFrames(WritingThread.java:384) 
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.main(WritingThread.java:108) 
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.runMain(WritingThread.java:55) 
                                                                                                    	at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45) 
2025-08-21 14:15:14.311  2772-3320  WebSocket               com.dspread.mdm.service              I  🔧 onError: 不触发重连，等待 onDisconnected 处理
2025-08-21 14:15:14.331  2772-3320  WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误 (Ask Gemini)
                                                                                                    com.neovisionaries.ws.client.WebSocketException: Flushing frames to the server failed: Socket closed
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.doFlush(WritingThread.java:434)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.sendFrames(WritingThread.java:384)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.main(WritingThread.java:120)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.runMain(WritingThread.java:55)
                                                                                                    	at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45)
                                                                                                    Caused by: java.net.SocketException: Socket closed
                                                                                                    	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.writeInternal(ConscryptEngineSocket.java:728)
                                                                                                    	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.write(ConscryptEngineSocket.java:699)
                                                                                                    	at java.io.BufferedOutputStream.flushBuffer(BufferedOutputStream.java:81)
                                                                                                    	at java.io.BufferedOutputStream.flush(BufferedOutputStream.java:142)
                                                                                                    	at java.io.BufferedOutputStream.flush(BufferedOutputStream.java:143)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.flush(WritingThread.java:271)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.doFlush(WritingThread.java:422)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.sendFrames(WritingThread.java:384) 
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.main(WritingThread.java:120) 
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.runMain(WritingThread.java:55) 
                                                                                                    	at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45) 
2025-08-21 14:15:14.341  2772-3320  WebSocket               com.dspread.mdm.service              I  🔧 onError: 不触发重连，等待 onDisconnected 处理
2025-08-21 14:15:14.349  2772-3320  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接断开 - 客户端主动断开
2025-08-21 14:15:14.356  2772-3320  WebSocket               com.dspread.mdm.service              I  🔧    服务器关闭帧: code=-1, reason=无原因
2025-08-21 14:15:14.362  2772-3320  WebSocket               com.dspread.mdm.service              I  🔧    客户端关闭帧: code=1002, reason=No more WebSocket frame from the server.
2025-08-21 14:15:14.367  2772-3320  WebSocket               com.dspread.mdm.service              I  🔧    断开发起方: 客户端
2025-08-21 14:15:14.373  2772-3320  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 业务连接断开
2025-08-21 14:15:14.381  2772-3320  Platform                com.dspread.mdm.service              D  🔧 WakeLockManager 释放WiFi锁成功
2025-08-21 14:15:14.386  2772-3320  WebSocket               com.dspread.mdm.service              I  🔧 停止PING/PONG检测
2025-08-21 14:15:14.391  2772-3320  WebSocket               com.dspread.mdm.service              I  🔧 reconnect() 被调用，调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onDisconnected:746
2025-08-21 14:15:14.397  2772-3320  WebSocket               com.dspread.mdm.service              I  🔧 开始重连 WebSocket... (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onDisconnected:746)
2025-08-21 14:15:14.412  2772-3320  WebSocket               com.dspread.mdm.service              I  🔧 使用Provisioning配置的重连策略: delayPolicy=1, delaySwitch=1, delayTime=60
2025-08-21 14:15:14.417  2772-3320  WebSocket               com.dspread.mdm.service              I  🔧 当前断开记录数: 10, 延迟开关: 1
2025-08-21 14:15:14.426  2772-3320  WebSocket               com.dspread.mdm.service              I  🔧 断开记录 最早一次断开: 2025-08-21 13:59:17
2025-08-21 14:15:14.436  2772-3320  WebSocket               com.dspread.mdm.service              I  🔧 断开记录 最后一次断开: 2025-08-21 14:15:14
2025-08-21 14:15:14.441  2772-3320  WebSocket               com.dspread.mdm.service              I  🔧 时间差: 15分钟 (配置阈值: 1分钟)
2025-08-21 14:15:14.446  2772-3320  WebSocket               com.dspread.mdm.service              I  🔧 断开间隔正常，使用标准重连策略
2025-08-21 14:15:14.451  2772-3320  WebSocket               com.dspread.mdm.service              I  🔧 重连策略计算完成: 第1次重连，间隔3000ms (3秒)
2025-08-21 14:15:14.456  2772-3320  WebSocket               com.dspread.mdm.service              I  🔧 开始第1次重连，间隔3000ms (3秒) (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onDisconnected:746)
2025-08-21 14:16:11.745  2772-2772  WebSocket               com.dspread.mdm.service              I  🔧 重连 WebSocket: wss://api.dspreadserv.net/status/websocket/register?serialNo=MDEzNTQwOTAyMDI1MDMwNTAzOTk=&publicKey=TUlHZk1BMEdDU3FHU0liM0RRRUJBUVVBQTRHTkFEQ0JpUUtCZ1FEUFBSaDVCRDhCTldJNWtvUFlTVDdrSVI5a015QkJ0S2t6QXhjdmRpdUNhUUdUMFpVbTNQQjUwME12RENNTi9kZ1FncmNPNEI5cnFXYzBLam5nbDJKbVlKQVJXNW5sdndTakd3TUZDR1l3OFRrRG5CV2JLZHduNUpUc2w0ZFhOWlNvQUNjZEJ6cjlrTEE3WlFGNlFBZDFXVjdtUU1QaVAxMzk1a2VGOXpVdXZRSURBUUFC&query=0&msgVer=3&timestamp=1755756971714&signature=m+fFjzKXd8F0hfv1DnDMrNCI+nAkdXgNsP+bLvvirJDqNNC3YYaNhy6cvB9cwhqPyhSohoiLwANsjn2ZUc1xBSCxgJCVMxdVejv4Lz+bjwYE2PqrgUutQBqH85BoJ4Vy498Jk6Z4+IeXA1zpwPVkURbcF34BXyuBX6dwCOFQ01Q=
2025-08-21 14:16:11.754  2772-2772  WebSocket               com.dspread.mdm.service              I  🔧 未检测到系统代理设置
2025-08-21 14:16:12.187  2772-3325  TrafficStats            com.dspread.mdm.service              D  tagSocket(86) with statsTag=0xffffffff, statsUid=-1
2025-08-21 14:16:12.438  2772-3326  TrafficStats            com.dspread.mdm.service              D  tagSocket(104) with statsTag=0xffffffff, statsUid=-1
2025-08-21 14:16:13.289  2772-2787  System                  com.dspread.mdm.service              W  A resource failed to call close. 
2025-08-21 14:16:13.294  2772-2787  System                  com.dspread.mdm.service              W  A resource failed to call close. 
2025-08-21 14:16:13.298  2772-2787  System                  com.dspread.mdm.service              W  A resource failed to call close. 
2025-08-21 14:16:13.303  2772-2787  System                  com.dspread.mdm.service              W  A resource failed to call close. 
2025-08-21 14:16:14.134  2772-3327  Common                  com.dspread.mdm.service              I  ✅ WebSocket 物理连接成功，等待服务器 hellMsg
2025-08-21 14:16:14.141  2772-3327  WebSocket               com.dspread.mdm.service              I  🔧 ===== 心跳发送器实际配置 =====
2025-08-21 14:16:14.149  2772-3327  WebSocket               com.dspread.mdm.service              I  🔧 实际 PING 发送间隔: 30000ms (30秒)
2025-08-21 14:16:14.157  2772-3327  WebSocket               com.dspread.mdm.service              I  🔧 PING 定时器名称: PingSender
2025-08-21 14:16:14.166  2772-3327  WebSocket               com.dspread.mdm.service              I  🔧 实际 PONG 发送间隔: 0ms (0秒)
2025-08-21 14:16:14.173  2772-3327  WebSocket               com.dspread.mdm.service              I  🔧 PONG 定时器名称: PongSender
2025-08-21 14:16:14.180  2772-3327  WebSocket               com.dspread.mdm.service              I  🔧 ===============================
2025-08-21 14:16:14.186  2772-3327  WebSocket               com.dspread.mdm.service              I  🔧 启动PING/PONG检测，阈值: 连续3个PING无PONG响应
2025-08-21 14:16:14.224  2772-3327  Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"hellMsg":"hello 01354090202503050399","tranCode":"S0000","version":"1","rebootTime":"03:02:01","serialNo":"01354090202503050399","deviceStatus":"6"}
2025-08-21 14:16:14.230  2772-3327  Common                  com.dspread.mdm.service              I  ✅ 收到服务器 hellMsg，WebSocket 业务连接成功！
2025-08-21 14:16:14.235  2772-3327  Common                  com.dspread.mdm.service              I  ✅ WebSocket 业务连接成功！
2025-08-21 14:16:14.240  2772-3327  WebSocket               com.dspread.mdm.service              I  🔧 业务连接成功，重连计数已重置为 0
2025-08-21 14:16:14.249  2772-3327  Platform                com.dspread.mdm.service              D  🔧 WakeLockManager 获取WiFi锁成功，防止WiFi休眠
2025-08-21 14:16:14.254  2772-3327  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接成功，检查自身更新任务状态
2025-08-21 14:16:14.261  2772-3327  Receiver                com.dspread.mdm.service              D  🔧 发送广播: com.dspread.mdm.service.CHECK_SELF_UPDATE_STATUS
2025-08-21 14:16:14.267  2772-3327  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接成功
2025-08-21 14:16:14.272  2772-3327  WebSocket               com.dspread.mdm.service              I  🔧 设备状态: 6
2025-08-21 14:16:14.278  2772-3327  WebSocket               com.dspread.mdm.service              I  🔧 检查首次连接标志: isFirstTimeWebSocketConnected=false
2025-08-21 14:16:14.282  2772-3327  WebSocket               com.dspread.mdm.service              I  🔧 非首次连接，跳过初始化信息发送和初始化检查
2025-08-21 14:16:14.287  2772-3327  WebSocket               com.dspread.mdm.service              I  🔧 执行必要的状态同步检查
2025-08-21 14:16:14.292  2772-3327  WebSocket               com.dspread.mdm.service              I  🔧 检查服务更新结果
2025-08-21 14:16:14.297  2772-3327  Task                    com.dspread.mdm.service              W  ⚠️ 任务列表为空，跳过服务更新结果检查
2025-08-21 14:16:14.302  2772-3327  WebSocket               com.dspread.mdm.service              I  🔧 检查OS升级状态...
2025-08-21 14:16:14.307  2772-3327  Common                  com.dspread.mdm.service              D  🔧 OsUpdateStatusChecker WebSocket连接成功，检查OS升级状态
2025-08-21 14:16:14.312  2772-3327  Common                  com.dspread.mdm.service              D  🔧 OsUpdateStatusChecker 没有待检查的OS升级任务
2025-08-21 14:16:14.317  2772-3327  WebSocket               com.dspread.mdm.service              I  🔧 OS升级状态检查已触发
2025-08-21 14:17:14.516  2772-3327  WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误 (Ask Gemini)
                                                                                                    com.neovisionaries.ws.client.NoMoreFrameException: No more WebSocket frame from the server.
                                                                                                    	at com.neovisionaries.ws.client.WebSocketInputStream.readFrame(WebSocketInputStream.java:54)
                                                                                                    	at com.neovisionaries.ws.client.ReadingThread.readFrame(ReadingThread.java:338)
                                                                                                    	at com.neovisionaries.ws.client.ReadingThread.main(ReadingThread.java:99)
                                                                                                    	at com.neovisionaries.ws.client.ReadingThread.runMain(ReadingThread.java:64)
                                                                                                    	at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45)
2025-08-21 14:17:14.540  2772-3327  WebSocket               com.dspread.mdm.service              I  🔧 onError: 不触发重连，等待 onDisconnected 处理
2025-08-21 14:17:14.561  2772-3328  WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误 (Ask Gemini)
                                                                                                    com.neovisionaries.ws.client.WebSocketException: Flushing frames to the server failed: Socket closed
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.doFlush(WritingThread.java:434)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.sendFrames(WritingThread.java:384)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.main(WritingThread.java:108)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.runMain(WritingThread.java:55)
                                                                                                    	at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45)
                                                                                                    Caused by: java.net.SocketException: Socket closed
                                                                                                    	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.writeInternal(ConscryptEngineSocket.java:728)
                                                                                                    	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.write(ConscryptEngineSocket.java:699)
                                                                                                    	at java.io.BufferedOutputStream.flushBuffer(BufferedOutputStream.java:81)
                                                                                                    	at java.io.BufferedOutputStream.flush(BufferedOutputStream.java:142)
                                                                                                    	at java.io.BufferedOutputStream.flush(BufferedOutputStream.java:143)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.flush(WritingThread.java:271)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.doFlush(WritingThread.java:422)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.sendFrames(WritingThread.java:384) 
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.main(WritingThread.java:108) 
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.runMain(WritingThread.java:55) 
                                                                                                    	at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45) 
2025-08-21 14:17:14.568  2772-3328  WebSocket               com.dspread.mdm.service              I  🔧 onError: 不触发重连，等待 onDisconnected 处理
2025-08-21 14:17:14.583  2772-3328  WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误 (Ask Gemini)
                                                                                                    com.neovisionaries.ws.client.WebSocketException: Flushing frames to the server failed: Socket closed
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.doFlush(WritingThread.java:434)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.sendFrames(WritingThread.java:384)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.main(WritingThread.java:120)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.runMain(WritingThread.java:55)
                                                                                                    	at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45)
                                                                                                    Caused by: java.net.SocketException: Socket closed
                                                                                                    	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.writeInternal(ConscryptEngineSocket.java:728)
                                                                                                    	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.write(ConscryptEngineSocket.java:699)
                                                                                                    	at java.io.BufferedOutputStream.flushBuffer(BufferedOutputStream.java:81)
                                                                                                    	at java.io.BufferedOutputStream.flush(BufferedOutputStream.java:142)
                                                                                                    	at java.io.BufferedOutputStream.flush(BufferedOutputStream.java:143)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.flush(WritingThread.java:271)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.doFlush(WritingThread.java:422)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.sendFrames(WritingThread.java:384) 
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.main(WritingThread.java:120) 
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.runMain(WritingThread.java:55) 
                                                                                                    	at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45) 
2025-08-21 14:17:14.591  2772-3328  WebSocket               com.dspread.mdm.service              I  🔧 onError: 不触发重连，等待 onDisconnected 处理
2025-08-21 14:17:14.599  2772-3328  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接断开 - 客户端主动断开
2025-08-21 14:17:14.605  2772-3328  WebSocket               com.dspread.mdm.service              I  🔧    服务器关闭帧: code=-1, reason=无原因
2025-08-21 14:17:14.611  2772-3328  WebSocket               com.dspread.mdm.service              I  🔧    客户端关闭帧: code=1002, reason=No more WebSocket frame from the server.
2025-08-21 14:17:14.616  2772-3328  WebSocket               com.dspread.mdm.service              I  🔧    断开发起方: 客户端
2025-08-21 14:17:14.622  2772-3328  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 业务连接断开
2025-08-21 14:17:14.629  2772-3328  Platform                com.dspread.mdm.service              D  🔧 WakeLockManager 释放WiFi锁成功
2025-08-21 14:17:14.635  2772-3328  WebSocket               com.dspread.mdm.service              I  🔧 停止PING/PONG检测
2025-08-21 14:17:14.640  2772-3328  WebSocket               com.dspread.mdm.service              I  🔧 reconnect() 被调用，调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onDisconnected:746
2025-08-21 14:17:14.646  2772-3328  WebSocket               com.dspread.mdm.service              I  🔧 开始重连 WebSocket... (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onDisconnected:746)
2025-08-21 14:17:14.661  2772-3328  WebSocket               com.dspread.mdm.service              I  🔧 使用Provisioning配置的重连策略: delayPolicy=1, delaySwitch=1, delayTime=60
2025-08-21 14:17:14.666  2772-3328  WebSocket               com.dspread.mdm.service              I  🔧 当前断开记录数: 10, 延迟开关: 1
2025-08-21 14:17:14.676  2772-3328  WebSocket               com.dspread.mdm.service              I  🔧 断开记录 最早一次断开: 2025-08-21 14:00:57
2025-08-21 14:17:14.686  2772-3328  WebSocket               com.dspread.mdm.service              I  🔧 断开记录 最后一次断开: 2025-08-21 14:17:14
2025-08-21 14:17:14.691  2772-3328  WebSocket               com.dspread.mdm.service              I  🔧 时间差: 16分钟 (配置阈值: 1分钟)
2025-08-21 14:17:14.696  2772-3328  WebSocket               com.dspread.mdm.service              I  🔧 断开间隔正常，使用标准重连策略
2025-08-21 14:17:14.701  2772-3328  WebSocket               com.dspread.mdm.service              I  🔧 重连策略计算完成: 第1次重连，间隔3000ms (3秒)
2025-08-21 14:17:14.706  2772-3328  WebSocket               com.dspread.mdm.service              I  🔧 开始第1次重连，间隔3000ms (3秒) (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onDisconnected:746)
2025-08-21 14:18:13.744  2772-2772  WebSocket               com.dspread.mdm.service              I  🔧 重连 WebSocket: wss://api.dspreadserv.net/status/websocket/register?serialNo=MDEzNTQwOTAyMDI1MDMwNTAzOTk=&publicKey=TUlHZk1BMEdDU3FHU0liM0RRRUJBUVVBQTRHTkFEQ0JpUUtCZ1FEUFBSaDVCRDhCTldJNWtvUFlTVDdrSVI5a015QkJ0S2t6QXhjdmRpdUNhUUdUMFpVbTNQQjUwME12RENNTi9kZ1FncmNPNEI5cnFXYzBLam5nbDJKbVlKQVJXNW5sdndTakd3TUZDR1l3OFRrRG5CV2JLZHduNUpUc2w0ZFhOWlNvQUNjZEJ6cjlrTEE3WlFGNlFBZDFXVjdtUU1QaVAxMzk1a2VGOXpVdXZRSURBUUFC&query=0&msgVer=3&timestamp=1755757093713&signature=Z1h1CGkJ06wJrHRCIaGH9K1a2Yqh+DSOoqlDeuJHvfPnthfoBo6HOBYn+xbknJjj9xh8j6nM102F+X5xIHo1ezTm/hzKQoUIIZYf8ip3Nr4DP7j/5nK3ukxlDGBrxiE/SfcdSS+BdVDqWfHcKwAdhNtHVdCgYCVAweQHIk85Usg=
2025-08-21 14:18:13.751  2772-2772  WebSocket               com.dspread.mdm.service              I  🔧 未检测到系统代理设置
2025-08-21 14:18:14.146  2772-3333  TrafficStats            com.dspread.mdm.service              D  tagSocket(86) with statsTag=0xffffffff, statsUid=-1
2025-08-21 14:18:14.397  2772-3334  TrafficStats            com.dspread.mdm.service              D  tagSocket(89) with statsTag=0xffffffff, statsUid=-1
2025-08-21 14:18:16.916  2772-3335  Common                  com.dspread.mdm.service              I  ✅ WebSocket 物理连接成功，等待服务器 hellMsg
2025-08-21 14:18:16.923  2772-3335  WebSocket               com.dspread.mdm.service              I  🔧 ===== 心跳发送器实际配置 =====
2025-08-21 14:18:16.931  2772-3335  WebSocket               com.dspread.mdm.service              I  🔧 实际 PING 发送间隔: 30000ms (30秒)
2025-08-21 14:18:16.938  2772-3335  WebSocket               com.dspread.mdm.service              I  🔧 PING 定时器名称: PingSender
2025-08-21 14:18:16.945  2772-3335  WebSocket               com.dspread.mdm.service              I  🔧 实际 PONG 发送间隔: 0ms (0秒)
2025-08-21 14:18:16.951  2772-3335  WebSocket               com.dspread.mdm.service              I  🔧 PONG 定时器名称: PongSender
2025-08-21 14:18:16.957  2772-3335  WebSocket               com.dspread.mdm.service              I  🔧 ===============================
2025-08-21 14:18:16.963  2772-3335  WebSocket               com.dspread.mdm.service              I  🔧 启动PING/PONG检测，阈值: 连续3个PING无PONG响应
2025-08-21 14:18:17.247  2772-3335  Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"hellMsg":"hello 01354090202503050399","tranCode":"S0000","version":"1","rebootTime":"03:18:44","serialNo":"01354090202503050399","deviceStatus":"6"}
2025-08-21 14:18:17.254  2772-3335  Common                  com.dspread.mdm.service              I  ✅ 收到服务器 hellMsg，WebSocket 业务连接成功！
2025-08-21 14:18:17.260  2772-3335  Common                  com.dspread.mdm.service              I  ✅ WebSocket 业务连接成功！
2025-08-21 14:18:17.266  2772-3335  WebSocket               com.dspread.mdm.service              I  🔧 业务连接成功，重连计数已重置为 0
2025-08-21 14:18:17.275  2772-3335  Platform                com.dspread.mdm.service              D  🔧 WakeLockManager 获取WiFi锁成功，防止WiFi休眠
2025-08-21 14:18:17.281  2772-3335  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接成功，检查自身更新任务状态
2025-08-21 14:18:17.289  2772-3335  Receiver                com.dspread.mdm.service              D  🔧 发送广播: com.dspread.mdm.service.CHECK_SELF_UPDATE_STATUS
2025-08-21 14:18:17.295  2772-3335  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接成功
2025-08-21 14:18:17.300  2772-3335  WebSocket               com.dspread.mdm.service              I  🔧 设备状态: 6
2025-08-21 14:18:17.306  2772-3335  WebSocket               com.dspread.mdm.service              I  🔧 检查首次连接标志: isFirstTimeWebSocketConnected=false
2025-08-21 14:18:17.311  2772-3335  WebSocket               com.dspread.mdm.service              I  🔧 非首次连接，跳过初始化信息发送和初始化检查
2025-08-21 14:18:17.315  2772-3335  WebSocket               com.dspread.mdm.service              I  🔧 执行必要的状态同步检查
2025-08-21 14:18:17.320  2772-3335  WebSocket               com.dspread.mdm.service              I  🔧 检查服务更新结果
2025-08-21 14:18:17.325  2772-3335  Task                    com.dspread.mdm.service              W  ⚠️ 任务列表为空，跳过服务更新结果检查
2025-08-21 14:18:17.330  2772-3335  WebSocket               com.dspread.mdm.service              I  🔧 检查OS升级状态...
2025-08-21 14:18:17.335  2772-3335  Common                  com.dspread.mdm.service              D  🔧 OsUpdateStatusChecker WebSocket连接成功，检查OS升级状态
2025-08-21 14:18:17.340  2772-3335  Common                  com.dspread.mdm.service              D  🔧 OsUpdateStatusChecker 没有待检查的OS升级任务
2025-08-21 14:18:17.345  2772-3335  WebSocket               com.dspread.mdm.service              I  🔧 OS升级状态检查已触发
2025-08-21 14:19:17.190  2772-3335  WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误 (Ask Gemini)
                                                                                                    com.neovisionaries.ws.client.NoMoreFrameException: No more WebSocket frame from the server.
                                                                                                    	at com.neovisionaries.ws.client.WebSocketInputStream.readFrame(WebSocketInputStream.java:54)
                                                                                                    	at com.neovisionaries.ws.client.ReadingThread.readFrame(ReadingThread.java:338)
                                                                                                    	at com.neovisionaries.ws.client.ReadingThread.main(ReadingThread.java:99)
                                                                                                    	at com.neovisionaries.ws.client.ReadingThread.runMain(ReadingThread.java:64)
                                                                                                    	at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45)
2025-08-21 14:19:17.201  2772-3335  WebSocket               com.dspread.mdm.service              I  🔧 onError: 不触发重连，等待 onDisconnected 处理
2025-08-21 14:19:17.225  2772-3336  WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误 (Ask Gemini)
                                                                                                    com.neovisionaries.ws.client.WebSocketException: Flushing frames to the server failed: Socket closed
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.doFlush(WritingThread.java:434)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.sendFrames(WritingThread.java:384)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.main(WritingThread.java:108)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.runMain(WritingThread.java:55)
                                                                                                    	at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45)
                                                                                                    Caused by: java.net.SocketException: Socket closed
                                                                                                    	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.writeInternal(ConscryptEngineSocket.java:728)
                                                                                                    	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.write(ConscryptEngineSocket.java:699)
                                                                                                    	at java.io.BufferedOutputStream.flushBuffer(BufferedOutputStream.java:81)
                                                                                                    	at java.io.BufferedOutputStream.flush(BufferedOutputStream.java:142)
                                                                                                    	at java.io.BufferedOutputStream.flush(BufferedOutputStream.java:143)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.flush(WritingThread.java:271)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.doFlush(WritingThread.java:422)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.sendFrames(WritingThread.java:384) 
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.main(WritingThread.java:108) 
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.runMain(WritingThread.java:55) 
                                                                                                    	at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45) 
2025-08-21 14:19:17.236  2772-3336  WebSocket               com.dspread.mdm.service              I  🔧 onError: 不触发重连，等待 onDisconnected 处理
2025-08-21 14:19:17.251  2772-3336  WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误 (Ask Gemini)
                                                                                                    com.neovisionaries.ws.client.WebSocketException: Flushing frames to the server failed: Socket closed
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.doFlush(WritingThread.java:434)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.sendFrames(WritingThread.java:384)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.main(WritingThread.java:120)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.runMain(WritingThread.java:55)
                                                                                                    	at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45)
                                                                                                    Caused by: java.net.SocketException: Socket closed
                                                                                                    	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.writeInternal(ConscryptEngineSocket.java:728)
                                                                                                    	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.write(ConscryptEngineSocket.java:699)
                                                                                                    	at java.io.BufferedOutputStream.flushBuffer(BufferedOutputStream.java:81)
                                                                                                    	at java.io.BufferedOutputStream.flush(BufferedOutputStream.java:142)
                                                                                                    	at java.io.BufferedOutputStream.flush(BufferedOutputStream.java:143)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.flush(WritingThread.java:271)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.doFlush(WritingThread.java:422)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.sendFrames(WritingThread.java:384) 
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.main(WritingThread.java:120) 
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.runMain(WritingThread.java:55) 
                                                                                                    	at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45) 
2025-08-21 14:19:17.257  2772-3336  WebSocket               com.dspread.mdm.service              I  🔧 onError: 不触发重连，等待 onDisconnected 处理
2025-08-21 14:19:17.264  2772-3336  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接断开 - 客户端主动断开
2025-08-21 14:19:17.269  2772-3336  WebSocket               com.dspread.mdm.service              I  🔧    服务器关闭帧: code=-1, reason=无原因
2025-08-21 14:19:17.275  2772-3336  WebSocket               com.dspread.mdm.service              I  🔧    客户端关闭帧: code=1002, reason=No more WebSocket frame from the server.
2025-08-21 14:19:17.280  2772-3336  WebSocket               com.dspread.mdm.service              I  🔧    断开发起方: 客户端
2025-08-21 14:19:17.285  2772-3336  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 业务连接断开
2025-08-21 14:19:17.293  2772-3336  Platform                com.dspread.mdm.service              D  🔧 WakeLockManager 释放WiFi锁成功
2025-08-21 14:19:17.298  2772-3336  WebSocket               com.dspread.mdm.service              I  🔧 停止PING/PONG检测
2025-08-21 14:19:17.304  2772-3336  WebSocket               com.dspread.mdm.service              I  🔧 reconnect() 被调用，调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onDisconnected:746
2025-08-21 14:19:17.309  2772-3336  WebSocket               com.dspread.mdm.service              I  🔧 开始重连 WebSocket... (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onDisconnected:746)
2025-08-21 14:19:17.323  2772-3336  WebSocket               com.dspread.mdm.service              I  🔧 使用Provisioning配置的重连策略: delayPolicy=1, delaySwitch=1, delayTime=60
2025-08-21 14:19:17.328  2772-3336  WebSocket               com.dspread.mdm.service              I  🔧 当前断开记录数: 10, 延迟开关: 1
2025-08-21 14:19:17.337  2772-3336  WebSocket               com.dspread.mdm.service              I  🔧 断开记录 最早一次断开: 2025-08-21 14:03:34
2025-08-21 14:19:18.294  2772-3336  WebSocket               com.dspread.mdm.service              I  🔧 断开记录 最后一次断开: 2025-08-21 14:19:17
2025-08-21 14:19:18.299  2772-3336  WebSocket               com.dspread.mdm.service              I  🔧 时间差: 15分钟 (配置阈值: 1分钟)
2025-08-21 14:19:18.304  2772-3336  WebSocket               com.dspread.mdm.service              I  🔧 断开间隔正常，使用标准重连策略
2025-08-21 14:19:18.308  2772-3336  WebSocket               com.dspread.mdm.service              I  🔧 重连策略计算完成: 第1次重连，间隔3000ms (3秒)
2025-08-21 14:19:18.314  2772-3336  WebSocket               com.dspread.mdm.service              I  🔧 开始第1次重连，间隔3000ms (3秒) (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onDisconnected:746)
2025-08-21 14:20:15.241  2772-2772  WebSocket               com.dspread.mdm.service              I  🔧 重连 WebSocket: wss://api.dspreadserv.net/status/websocket/register?serialNo=MDEzNTQwOTAyMDI1MDMwNTAzOTk=&publicKey=TUlHZk1BMEdDU3FHU0liM0RRRUJBUVVBQTRHTkFEQ0JpUUtCZ1FEUFBSaDVCRDhCTldJNWtvUFlTVDdrSVI5a015QkJ0S2t6QXhjdmRpdUNhUUdUMFpVbTNQQjUwME12RENNTi9kZ1FncmNPNEI5cnFXYzBLam5nbDJKbVlKQVJXNW5sdndTakd3TUZDR1l3OFRrRG5CV2JLZHduNUpUc2w0ZFhOWlNvQUNjZEJ6cjlrTEE3WlFGNlFBZDFXVjdtUU1QaVAxMzk1a2VGOXpVdXZRSURBUUFC&query=0&msgVer=3&timestamp=1755757215205&signature=KYwz0npX/RRrExifD0Sw2HGzTygH2wISvKYfLvrnpV2LZCo18GqEG7+pgzfs/acJhIfrhn2RVjBemjYPHzPXs3nizHNrqztSHNCWBBV84hBJjNb/sXu+841/ZpKgYHH+2SZbsW3casHjpM3A0enI4tGCBOatlshKfsp7xI8+HA0=
2025-08-21 14:20:15.248  2772-2772  WebSocket               com.dspread.mdm.service              I  🔧 未检测到系统代理设置
2025-08-21 14:20:15.593  2772-3341  TrafficStats            com.dspread.mdm.service              D  tagSocket(86) with statsTag=0xffffffff, statsUid=-1
2025-08-21 14:20:15.843  2772-3342  TrafficStats            com.dspread.mdm.service              D  tagSocket(96) with statsTag=0xffffffff, statsUid=-1
2025-08-21 14:20:18.564  2772-3343  Common                  com.dspread.mdm.service              I  ✅ WebSocket 物理连接成功，等待服务器 hellMsg
2025-08-21 14:20:18.570  2772-3343  WebSocket               com.dspread.mdm.service              I  🔧 ===== 心跳发送器实际配置 =====
2025-08-21 14:20:18.575  2772-3343  WebSocket               com.dspread.mdm.service              I  🔧 实际 PING 发送间隔: 30000ms (30秒)
2025-08-21 14:20:18.580  2772-3343  WebSocket               com.dspread.mdm.service              I  🔧 PING 定时器名称: PingSender
2025-08-21 14:20:18.586  2772-3343  WebSocket               com.dspread.mdm.service              I  🔧 实际 PONG 发送间隔: 0ms (0秒)
2025-08-21 14:20:18.591  2772-3343  WebSocket               com.dspread.mdm.service              I  🔧 PONG 定时器名称: PongSender
2025-08-21 14:20:18.595  2772-3343  WebSocket               com.dspread.mdm.service              I  🔧 ===============================
2025-08-21 14:20:18.600  2772-3343  WebSocket               com.dspread.mdm.service              I  🔧 启动PING/PONG检测，阈值: 连续3个PING无PONG响应
2025-08-21 14:20:18.634  2772-3343  Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"hellMsg":"hello 01354090202503050399","tranCode":"S0000","version":"1","rebootTime":"03:24:58","serialNo":"01354090202503050399","deviceStatus":"6"}
2025-08-21 14:20:18.639  2772-3343  Common                  com.dspread.mdm.service              I  ✅ 收到服务器 hellMsg，WebSocket 业务连接成功！
2025-08-21 14:20:18.644  2772-3343  Common                  com.dspread.mdm.service              I  ✅ WebSocket 业务连接成功！
2025-08-21 14:20:18.649  2772-3343  WebSocket               com.dspread.mdm.service              I  🔧 业务连接成功，重连计数已重置为 0
2025-08-21 14:20:18.657  2772-3343  Platform                com.dspread.mdm.service              D  🔧 WakeLockManager 获取WiFi锁成功，防止WiFi休眠
2025-08-21 14:20:18.662  2772-3343  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接成功，检查自身更新任务状态
2025-08-21 14:20:18.670  2772-3343  Receiver                com.dspread.mdm.service              D  🔧 发送广播: com.dspread.mdm.service.CHECK_SELF_UPDATE_STATUS
2025-08-21 14:20:18.675  2772-3343  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接成功
2025-08-21 14:20:18.680  2772-3343  WebSocket               com.dspread.mdm.service              I  🔧 设备状态: 6
2025-08-21 14:20:18.685  2772-3343  WebSocket               com.dspread.mdm.service              I  🔧 检查首次连接标志: isFirstTimeWebSocketConnected=false
2025-08-21 14:20:18.690  2772-3343  WebSocket               com.dspread.mdm.service              I  🔧 非首次连接，跳过初始化信息发送和初始化检查
2025-08-21 14:20:18.695  2772-3343  WebSocket               com.dspread.mdm.service              I  🔧 执行必要的状态同步检查
2025-08-21 14:20:18.699  2772-3343  WebSocket               com.dspread.mdm.service              I  🔧 检查服务更新结果
2025-08-21 14:20:18.704  2772-3343  Task                    com.dspread.mdm.service              W  ⚠️ 任务列表为空，跳过服务更新结果检查
2025-08-21 14:20:18.709  2772-3343  WebSocket               com.dspread.mdm.service              I  🔧 检查OS升级状态...
2025-08-21 14:20:18.714  2772-3343  Common                  com.dspread.mdm.service              D  🔧 OsUpdateStatusChecker WebSocket连接成功，检查OS升级状态
2025-08-21 14:20:18.719  2772-3343  Common                  com.dspread.mdm.service              D  🔧 OsUpdateStatusChecker 没有待检查的OS升级任务
2025-08-21 14:20:18.724  2772-3343  WebSocket               com.dspread.mdm.service              I  🔧 OS升级状态检查已触发
2025-08-21 14:21:18.637  2772-3343  WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误 (Ask Gemini)
                                                                                                    com.neovisionaries.ws.client.NoMoreFrameException: No more WebSocket frame from the server.
                                                                                                    	at com.neovisionaries.ws.client.WebSocketInputStream.readFrame(WebSocketInputStream.java:54)
                                                                                                    	at com.neovisionaries.ws.client.ReadingThread.readFrame(ReadingThread.java:338)
                                                                                                    	at com.neovisionaries.ws.client.ReadingThread.main(ReadingThread.java:99)
                                                                                                    	at com.neovisionaries.ws.client.ReadingThread.runMain(ReadingThread.java:64)
                                                                                                    	at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45)
2025-08-21 14:21:18.648  2772-3343  WebSocket               com.dspread.mdm.service              I  🔧 onError: 不触发重连，等待 onDisconnected 处理
2025-08-21 14:21:18.673  2772-3344  WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误 (Ask Gemini)
                                                                                                    com.neovisionaries.ws.client.WebSocketException: Flushing frames to the server failed: Socket closed
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.doFlush(WritingThread.java:434)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.sendFrames(WritingThread.java:384)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.main(WritingThread.java:108)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.runMain(WritingThread.java:55)
                                                                                                    	at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45)
                                                                                                    Caused by: java.net.SocketException: Socket closed
                                                                                                    	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.writeInternal(ConscryptEngineSocket.java:728)
                                                                                                    	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.write(ConscryptEngineSocket.java:699)
                                                                                                    	at java.io.BufferedOutputStream.flushBuffer(BufferedOutputStream.java:81)
                                                                                                    	at java.io.BufferedOutputStream.flush(BufferedOutputStream.java:142)
                                                                                                    	at java.io.BufferedOutputStream.flush(BufferedOutputStream.java:143)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.flush(WritingThread.java:271)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.doFlush(WritingThread.java:422)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.sendFrames(WritingThread.java:384) 
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.main(WritingThread.java:108) 
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.runMain(WritingThread.java:55) 
                                                                                                    	at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45) 
2025-08-21 14:21:18.683  2772-3344  WebSocket               com.dspread.mdm.service              I  🔧 onError: 不触发重连，等待 onDisconnected 处理
2025-08-21 14:21:18.701  2772-3344  WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误 (Ask Gemini)
                                                                                                    com.neovisionaries.ws.client.WebSocketException: Flushing frames to the server failed: Socket closed
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.doFlush(WritingThread.java:434)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.sendFrames(WritingThread.java:384)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.main(WritingThread.java:120)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.runMain(WritingThread.java:55)
                                                                                                    	at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45)
                                                                                                    Caused by: java.net.SocketException: Socket closed
                                                                                                    	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.writeInternal(ConscryptEngineSocket.java:728)
                                                                                                    	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.write(ConscryptEngineSocket.java:699)
                                                                                                    	at java.io.BufferedOutputStream.flushBuffer(BufferedOutputStream.java:81)
                                                                                                    	at java.io.BufferedOutputStream.flush(BufferedOutputStream.java:142)
                                                                                                    	at java.io.BufferedOutputStream.flush(BufferedOutputStream.java:143)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.flush(WritingThread.java:271)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.doFlush(WritingThread.java:422)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.sendFrames(WritingThread.java:384) 
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.main(WritingThread.java:120) 
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.runMain(WritingThread.java:55) 
                                                                                                    	at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45) 
2025-08-21 14:21:18.708  2772-3344  WebSocket               com.dspread.mdm.service              I  🔧 onError: 不触发重连，等待 onDisconnected 处理
2025-08-21 14:21:18.715  2772-3344  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接断开 - 客户端主动断开
2025-08-21 14:21:18.720  2772-3344  WebSocket               com.dspread.mdm.service              I  🔧    服务器关闭帧: code=-1, reason=无原因
2025-08-21 14:21:18.725  2772-3344  WebSocket               com.dspread.mdm.service              I  🔧    客户端关闭帧: code=1002, reason=No more WebSocket frame from the server.
2025-08-21 14:21:18.730  2772-3344  WebSocket               com.dspread.mdm.service              I  🔧    断开发起方: 客户端
2025-08-21 14:21:18.735  2772-3344  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 业务连接断开
2025-08-21 14:21:18.742  2772-3344  Platform                com.dspread.mdm.service              D  🔧 WakeLockManager 释放WiFi锁成功
2025-08-21 14:21:18.747  2772-3344  WebSocket               com.dspread.mdm.service              I  🔧 停止PING/PONG检测
2025-08-21 14:21:18.752  2772-3344  WebSocket               com.dspread.mdm.service              I  🔧 reconnect() 被调用，调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onDisconnected:746
2025-08-21 14:21:18.757  2772-3344  WebSocket               com.dspread.mdm.service              I  🔧 开始重连 WebSocket... (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onDisconnected:746)
2025-08-21 14:21:18.771  2772-3344  WebSocket               com.dspread.mdm.service              I  🔧 使用Provisioning配置的重连策略: delayPolicy=1, delaySwitch=1, delayTime=60
2025-08-21 14:21:18.777  2772-3344  WebSocket               com.dspread.mdm.service              I  🔧 当前断开记录数: 10, 延迟开关: 1
2025-08-21 14:21:18.786  2772-3344  WebSocket               com.dspread.mdm.service              I  🔧 断开记录 最早一次断开: 2025-08-21 14:05:03
2025-08-21 14:21:18.795  2772-3344  WebSocket               com.dspread.mdm.service              I  🔧 断开记录 最后一次断开: 2025-08-21 14:21:18
2025-08-21 14:21:18.800  2772-3344  WebSocket               com.dspread.mdm.service              I  🔧 时间差: 16分钟 (配置阈值: 1分钟)
2025-08-21 14:21:18.805  2772-3344  WebSocket               com.dspread.mdm.service              I  🔧 断开间隔正常，使用标准重连策略
2025-08-21 14:21:18.810  2772-3344  WebSocket               com.dspread.mdm.service              I  🔧 重连策略计算完成: 第1次重连，间隔3000ms (3秒)
2025-08-21 14:21:18.815  2772-3344  WebSocket               com.dspread.mdm.service              I  🔧 开始第1次重连，间隔3000ms (3秒) (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onDisconnected:746)
2025-08-21 14:21:58.242  2772-2772  WebSocket               com.dspread.mdm.service              I  🔧 重连 WebSocket: wss://api.dspreadserv.net/status/websocket/register?serialNo=MDEzNTQwOTAyMDI1MDMwNTAzOTk=&publicKey=TUlHZk1BMEdDU3FHU0liM0RRRUJBUVVBQTRHTkFEQ0JpUUtCZ1FEUFBSaDVCRDhCTldJNWtvUFlTVDdrSVI5a015QkJ0S2t6QXhjdmRpdUNhUUdUMFpVbTNQQjUwME12RENNTi9kZ1FncmNPNEI5cnFXYzBLam5nbDJKbVlKQVJXNW5sdndTakd3TUZDR1l3OFRrRG5CV2JLZHduNUpUc2w0ZFhOWlNvQUNjZEJ6cjlrTEE3WlFGNlFBZDFXVjdtUU1QaVAxMzk1a2VGOXpVdXZRSURBUUFC&query=0&msgVer=3&timestamp=1755757318201&signature=d5TNwQx+jJyT/uk6lN8FmIkPO+D/KEuNC/NZiZaA4wCsDFk7YAiyKRcsmsHf3HHbqOHku56AQmJ7PqPzmLQgH/GLIHK+MLpkWrWwNOIAPAzFJqJkE7iGBVaS5/A7gjlDDtou29AIGoMfXKvVc3x58S+L24okavd4ruJke0aZMSo=
2025-08-21 14:21:58.249  2772-2772  WebSocket               com.dspread.mdm.service              I  🔧 未检测到系统代理设置
2025-08-21 14:21:58.712  2772-3352  TrafficStats            com.dspread.mdm.service              D  tagSocket(96) with statsTag=0xffffffff, statsUid=-1
2025-08-21 14:21:58.964  2772-3353  TrafficStats            com.dspread.mdm.service              D  tagSocket(101) with statsTag=0xffffffff, statsUid=-1
2025-08-21 14:22:01.737  2772-3354  Common                  com.dspread.mdm.service              I  ✅ WebSocket 物理连接成功，等待服务器 hellMsg
2025-08-21 14:22:01.747  2772-3354  WebSocket               com.dspread.mdm.service              I  🔧 ===== 心跳发送器实际配置 =====
2025-08-21 14:22:01.753  2772-3354  WebSocket               com.dspread.mdm.service              I  🔧 实际 PING 发送间隔: 30000ms (30秒)
2025-08-21 14:22:01.761  2772-3354  WebSocket               com.dspread.mdm.service              I  🔧 PING 定时器名称: PingSender
2025-08-21 14:22:01.767  2772-3354  WebSocket               com.dspread.mdm.service              I  🔧 实际 PONG 发送间隔: 0ms (0秒)
2025-08-21 14:22:01.774  2772-3354  WebSocket               com.dspread.mdm.service              I  🔧 PONG 定时器名称: PongSender
2025-08-21 14:22:01.780  2772-3354  WebSocket               com.dspread.mdm.service              I  🔧 ===============================
2025-08-21 14:22:01.785  2772-3354  WebSocket               com.dspread.mdm.service              I  🔧 启动PING/PONG检测，阈值: 连续3个PING无PONG响应
2025-08-21 14:22:01.818  2772-3354  Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"hellMsg":"hello 01354090202503050399","tranCode":"S0000","version":"1","rebootTime":"02:27:52","serialNo":"01354090202503050399","deviceStatus":"6"}
2025-08-21 14:22:01.824  2772-3354  Common                  com.dspread.mdm.service              I  ✅ 收到服务器 hellMsg，WebSocket 业务连接成功！
2025-08-21 14:22:01.828  2772-3354  Common                  com.dspread.mdm.service              I  ✅ WebSocket 业务连接成功！
2025-08-21 14:22:01.833  2772-3354  WebSocket               com.dspread.mdm.service              I  🔧 业务连接成功，重连计数已重置为 0
2025-08-21 14:22:01.841  2772-3354  Platform                com.dspread.mdm.service              D  🔧 WakeLockManager 获取WiFi锁成功，防止WiFi休眠
2025-08-21 14:22:01.846  2772-3354  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接成功，检查自身更新任务状态
2025-08-21 14:22:01.853  2772-3354  Receiver                com.dspread.mdm.service              D  🔧 发送广播: com.dspread.mdm.service.CHECK_SELF_UPDATE_STATUS
2025-08-21 14:22:01.858  2772-3354  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接成功
2025-08-21 14:22:01.863  2772-3354  WebSocket               com.dspread.mdm.service              I  🔧 设备状态: 6
2025-08-21 14:22:01.868  2772-3354  WebSocket               com.dspread.mdm.service              I  🔧 检查首次连接标志: isFirstTimeWebSocketConnected=false
2025-08-21 14:22:01.873  2772-3354  WebSocket               com.dspread.mdm.service              I  🔧 非首次连接，跳过初始化信息发送和初始化检查
2025-08-21 14:22:01.878  2772-3354  WebSocket               com.dspread.mdm.service              I  🔧 执行必要的状态同步检查
2025-08-21 14:22:01.882  2772-3354  WebSocket               com.dspread.mdm.service              I  🔧 检查服务更新结果
2025-08-21 14:22:01.887  2772-3354  Task                    com.dspread.mdm.service              W  ⚠️ 任务列表为空，跳过服务更新结果检查
2025-08-21 14:22:01.892  2772-3354  WebSocket               com.dspread.mdm.service              I  🔧 检查OS升级状态...
2025-08-21 14:22:01.897  2772-3354  Common                  com.dspread.mdm.service              D  🔧 OsUpdateStatusChecker WebSocket连接成功，检查OS升级状态
2025-08-21 14:22:01.902  2772-3354  Common                  com.dspread.mdm.service              D  🔧 OsUpdateStatusChecker 没有待检查的OS升级任务
2025-08-21 14:22:01.907  2772-3354  WebSocket               com.dspread.mdm.service              I  🔧 OS升级状态检查已触发
2025-08-21 14:23:01.252  2772-3354  WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误 (Ask Gemini)
                                                                                                    com.neovisionaries.ws.client.NoMoreFrameException: No more WebSocket frame from the server.
                                                                                                    	at com.neovisionaries.ws.client.WebSocketInputStream.readFrame(WebSocketInputStream.java:54)
                                                                                                    	at com.neovisionaries.ws.client.ReadingThread.readFrame(ReadingThread.java:338)
                                                                                                    	at com.neovisionaries.ws.client.ReadingThread.main(ReadingThread.java:99)
                                                                                                    	at com.neovisionaries.ws.client.ReadingThread.runMain(ReadingThread.java:64)
                                                                                                    	at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45)
2025-08-21 14:23:01.264  2772-3354  WebSocket               com.dspread.mdm.service              I  🔧 onError: 不触发重连，等待 onDisconnected 处理
2025-08-21 14:23:01.290  2772-3355  WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误 (Ask Gemini)
                                                                                                    com.neovisionaries.ws.client.WebSocketException: Flushing frames to the server failed: Socket closed
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.doFlush(WritingThread.java:434)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.sendFrames(WritingThread.java:384)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.main(WritingThread.java:108)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.runMain(WritingThread.java:55)
                                                                                                    	at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45)
                                                                                                    Caused by: java.net.SocketException: Socket closed
                                                                                                    	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.writeInternal(ConscryptEngineSocket.java:728)
                                                                                                    	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.write(ConscryptEngineSocket.java:699)
                                                                                                    	at java.io.BufferedOutputStream.flushBuffer(BufferedOutputStream.java:81)
                                                                                                    	at java.io.BufferedOutputStream.flush(BufferedOutputStream.java:142)
                                                                                                    	at java.io.BufferedOutputStream.flush(BufferedOutputStream.java:143)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.flush(WritingThread.java:271)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.doFlush(WritingThread.java:422)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.sendFrames(WritingThread.java:384) 
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.main(WritingThread.java:108) 
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.runMain(WritingThread.java:55) 
                                                                                                    	at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45) 
2025-08-21 14:23:01.299  2772-3355  WebSocket               com.dspread.mdm.service              I  🔧 onError: 不触发重连，等待 onDisconnected 处理
2025-08-21 14:23:01.313  2772-3355  WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误 (Ask Gemini)
                                                                                                    com.neovisionaries.ws.client.WebSocketException: Flushing frames to the server failed: Socket closed
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.doFlush(WritingThread.java:434)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.sendFrames(WritingThread.java:384)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.main(WritingThread.java:120)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.runMain(WritingThread.java:55)
                                                                                                    	at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45)
                                                                                                    Caused by: java.net.SocketException: Socket closed
                                                                                                    	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.writeInternal(ConscryptEngineSocket.java:728)
                                                                                                    	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.write(ConscryptEngineSocket.java:699)
                                                                                                    	at java.io.BufferedOutputStream.flushBuffer(BufferedOutputStream.java:81)
                                                                                                    	at java.io.BufferedOutputStream.flush(BufferedOutputStream.java:142)
                                                                                                    	at java.io.BufferedOutputStream.flush(BufferedOutputStream.java:143)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.flush(WritingThread.java:271)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.doFlush(WritingThread.java:422)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.sendFrames(WritingThread.java:384) 
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.main(WritingThread.java:120) 
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.runMain(WritingThread.java:55) 
                                                                                                    	at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45) 
2025-08-21 14:23:01.321  2772-3355  WebSocket               com.dspread.mdm.service              I  🔧 onError: 不触发重连，等待 onDisconnected 处理
2025-08-21 14:23:01.328  2772-3355  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接断开 - 客户端主动断开
2025-08-21 14:23:01.333  2772-3355  WebSocket               com.dspread.mdm.service              I  🔧    服务器关闭帧: code=-1, reason=无原因
2025-08-21 14:23:01.339  2772-3355  WebSocket               com.dspread.mdm.service              I  🔧    客户端关闭帧: code=1002, reason=No more WebSocket frame from the server.
2025-08-21 14:23:01.344  2772-3355  WebSocket               com.dspread.mdm.service              I  🔧    断开发起方: 客户端
2025-08-21 14:23:01.349  2772-3355  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 业务连接断开
2025-08-21 14:23:01.356  2772-3355  Platform                com.dspread.mdm.service              D  🔧 WakeLockManager 释放WiFi锁成功
2025-08-21 14:23:01.361  2772-3355  WebSocket               com.dspread.mdm.service              I  🔧 停止PING/PONG检测
2025-08-21 14:23:01.367  2772-3355  WebSocket               com.dspread.mdm.service              I  🔧 reconnect() 被调用，调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onDisconnected:746
2025-08-21 14:23:01.372  2772-3355  WebSocket               com.dspread.mdm.service              I  🔧 开始重连 WebSocket... (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onDisconnected:746)
2025-08-21 14:23:01.386  2772-3355  WebSocket               com.dspread.mdm.service              I  🔧 使用Provisioning配置的重连策略: delayPolicy=1, delaySwitch=1, delayTime=60
2025-08-21 14:23:01.391  2772-3355  WebSocket               com.dspread.mdm.service              I  🔧 当前断开记录数: 10, 延迟开关: 1
2025-08-21 14:23:01.402  2772-3355  WebSocket               com.dspread.mdm.service              I  🔧 断开记录 最早一次断开: 2025-08-21 14:07:03
2025-08-21 14:23:01.414  2772-3355  WebSocket               com.dspread.mdm.service              I  🔧 断开记录 最后一次断开: 2025-08-21 14:23:01
2025-08-21 14:23:01.419  2772-3355  WebSocket               com.dspread.mdm.service              I  🔧 时间差: 15分钟 (配置阈值: 1分钟)
2025-08-21 14:23:01.421  2772-2787  System                  com.dspread.mdm.service              W  A resource failed to call close. 
2025-08-21 14:23:01.424  2772-3355  WebSocket               com.dspread.mdm.service              I  🔧 断开间隔正常，使用标准重连策略
2025-08-21 14:23:01.425  2772-2787  System                  com.dspread.mdm.service              W  A resource failed to call close. 
2025-08-21 14:23:01.427  2772-2787  System                  com.dspread.mdm.service              W  A resource failed to call close. 
2025-08-21 14:23:01.429  2772-3355  WebSocket               com.dspread.mdm.service              I  🔧 重连策略计算完成: 第1次重连，间隔3000ms (3秒)
2025-08-21 14:23:01.435  2772-3355  WebSocket               com.dspread.mdm.service              I  🔧 开始第1次重连，间隔3000ms (3秒) (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onDisconnected:746)
2025-08-21 14:23:04.479  2772-2772  WebSocket               com.dspread.mdm.service              I  🔧 重连 WebSocket: wss://api.dspreadserv.net/status/websocket/register?serialNo=MDEzNTQwOTAyMDI1MDMwNTAzOTk=&publicKey=TUlHZk1BMEdDU3FHU0liM0RRRUJBUVVBQTRHTkFEQ0JpUUtCZ1FEUFBSaDVCRDhCTldJNWtvUFlTVDdrSVI5a015QkJ0S2t6QXhjdmRpdUNhUUdUMFpVbTNQQjUwME12RENNTi9kZ1FncmNPNEI5cnFXYzBLam5nbDJKbVlKQVJXNW5sdndTakd3TUZDR1l3OFRrRG5CV2JLZHduNUpUc2w0ZFhOWlNvQUNjZEJ6cjlrTEE3WlFGNlFBZDFXVjdtUU1QaVAxMzk1a2VGOXpVdXZRSURBUUFC&query=0&msgVer=3&timestamp=1755757384449&signature=u9FuuBZCoxpw4TWYilvlVW2x9YCZuXP1m/wyGtxXtfBDCgQqPtnkzPMJFctG6rsJTHX30IkvIToTuCBi8/KVUu2KcDQNcfDBf3KztmQPRz2cPBDeAd+QYaMU5SHRzeAMpZ1HlzI1V+Af0Cum/ZS1i6H32lpjsHiciQiwH6jLfw4=
2025-08-21 14:23:04.489  2772-2772  WebSocket               com.dspread.mdm.service              I  🔧 未检测到系统代理设置
2025-08-21 14:23:04.766  2772-3360  TrafficStats            com.dspread.mdm.service              D  tagSocket(86) with statsTag=0xffffffff, statsUid=-1
2025-08-21 14:23:05.016  2772-3361  TrafficStats            com.dspread.mdm.service              D  tagSocket(89) with statsTag=0xffffffff, statsUid=-1
2025-08-21 14:23:07.571  2772-3362  Common                  com.dspread.mdm.service              I  ✅ WebSocket 物理连接成功，等待服务器 hellMsg
2025-08-21 14:23:07.578  2772-3362  WebSocket               com.dspread.mdm.service              I  🔧 ===== 心跳发送器实际配置 =====
2025-08-21 14:23:07.584  2772-3362  WebSocket               com.dspread.mdm.service              I  🔧 实际 PING 发送间隔: 30000ms (30秒)
2025-08-21 14:23:07.589  2772-3362  WebSocket               com.dspread.mdm.service              I  🔧 PING 定时器名称: PingSender
2025-08-21 14:23:07.594  2772-3362  WebSocket               com.dspread.mdm.service              I  🔧 实际 PONG 发送间隔: 0ms (0秒)
2025-08-21 14:23:07.599  2772-3362  WebSocket               com.dspread.mdm.service              I  🔧 PONG 定时器名称: PongSender
2025-08-21 14:23:07.604  2772-3362  WebSocket               com.dspread.mdm.service              I  🔧 ===============================
2025-08-21 14:23:07.609  2772-3362  WebSocket               com.dspread.mdm.service              I  🔧 启动PING/PONG检测，阈值: 连续3个PING无PONG响应
2025-08-21 14:23:07.642  2772-3362  Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"hellMsg":"hello 01354090202503050399","tranCode":"S0000","version":"1","rebootTime":"02:59:33","serialNo":"01354090202503050399","deviceStatus":"6"}
2025-08-21 14:23:07.648  2772-3362  Common                  com.dspread.mdm.service              I  ✅ 收到服务器 hellMsg，WebSocket 业务连接成功！
2025-08-21 14:23:07.653  2772-3362  Common                  com.dspread.mdm.service              I  ✅ WebSocket 业务连接成功！
2025-08-21 14:23:07.658  2772-3362  WebSocket               com.dspread.mdm.service              I  🔧 业务连接成功，重连计数已重置为 0
2025-08-21 14:23:07.666  2772-3362  Platform                com.dspread.mdm.service              D  🔧 WakeLockManager 获取WiFi锁成功，防止WiFi休眠
2025-08-21 14:23:07.671  2772-3362  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接成功，检查自身更新任务状态
2025-08-21 14:23:07.678  2772-3362  Receiver                com.dspread.mdm.service              D  🔧 发送广播: com.dspread.mdm.service.CHECK_SELF_UPDATE_STATUS
2025-08-21 14:23:07.684  2772-3362  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接成功
2025-08-21 14:23:07.689  2772-3362  WebSocket               com.dspread.mdm.service              I  🔧 设备状态: 6
2025-08-21 14:23:07.694  2772-3362  WebSocket               com.dspread.mdm.service              I  🔧 检查首次连接标志: isFirstTimeWebSocketConnected=false
2025-08-21 14:23:07.699  2772-3362  WebSocket               com.dspread.mdm.service              I  🔧 非首次连接，跳过初始化信息发送和初始化检查
2025-08-21 14:23:07.703  2772-3362  WebSocket               com.dspread.mdm.service              I  🔧 执行必要的状态同步检查
2025-08-21 14:23:07.708  2772-3362  WebSocket               com.dspread.mdm.service              I  🔧 检查服务更新结果
2025-08-21 14:23:07.713  2772-3362  Task                    com.dspread.mdm.service              W  ⚠️ 任务列表为空，跳过服务更新结果检查
2025-08-21 14:23:07.718  2772-3362  WebSocket               com.dspread.mdm.service              I  🔧 检查OS升级状态...
2025-08-21 14:23:07.723  2772-3362  Common                  com.dspread.mdm.service              D  🔧 OsUpdateStatusChecker WebSocket连接成功，检查OS升级状态
2025-08-21 14:23:07.728  2772-3362  Common                  com.dspread.mdm.service              D  🔧 OsUpdateStatusChecker 没有待检查的OS升级任务
2025-08-21 14:23:07.733  2772-3362  WebSocket               com.dspread.mdm.service              I  🔧 OS升级状态检查已触发
2025-08-21 14:24:07.710  2772-3362  WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误 (Ask Gemini)
                                                                                                    com.neovisionaries.ws.client.NoMoreFrameException: No more WebSocket frame from the server.
                                                                                                    	at com.neovisionaries.ws.client.WebSocketInputStream.readFrame(WebSocketInputStream.java:54)
                                                                                                    	at com.neovisionaries.ws.client.ReadingThread.readFrame(ReadingThread.java:338)
                                                                                                    	at com.neovisionaries.ws.client.ReadingThread.main(ReadingThread.java:99)
                                                                                                    	at com.neovisionaries.ws.client.ReadingThread.runMain(ReadingThread.java:64)
                                                                                                    	at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45)
2025-08-21 14:24:07.721  2772-3362  WebSocket               com.dspread.mdm.service              I  🔧 onError: 不触发重连，等待 onDisconnected 处理
2025-08-21 14:24:07.749  2772-3363  WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误 (Ask Gemini)
                                                                                                    com.neovisionaries.ws.client.WebSocketException: Flushing frames to the server failed: Socket closed
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.doFlush(WritingThread.java:434)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.sendFrames(WritingThread.java:384)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.main(WritingThread.java:108)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.runMain(WritingThread.java:55)
                                                                                                    	at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45)
                                                                                                    Caused by: java.net.SocketException: Socket closed
                                                                                                    	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.writeInternal(ConscryptEngineSocket.java:728)
                                                                                                    	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.write(ConscryptEngineSocket.java:699)
                                                                                                    	at java.io.BufferedOutputStream.flushBuffer(BufferedOutputStream.java:81)
                                                                                                    	at java.io.BufferedOutputStream.flush(BufferedOutputStream.java:142)
                                                                                                    	at java.io.BufferedOutputStream.flush(BufferedOutputStream.java:143)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.flush(WritingThread.java:271)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.doFlush(WritingThread.java:422)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.sendFrames(WritingThread.java:384) 
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.main(WritingThread.java:108) 
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.runMain(WritingThread.java:55) 
                                                                                                    	at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45) 
2025-08-21 14:24:07.760  2772-3363  WebSocket               com.dspread.mdm.service              I  🔧 onError: 不触发重连，等待 onDisconnected 处理
2025-08-21 14:24:07.773  2772-3363  WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误 (Ask Gemini)
                                                                                                    com.neovisionaries.ws.client.WebSocketException: Flushing frames to the server failed: Socket closed
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.doFlush(WritingThread.java:434)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.sendFrames(WritingThread.java:384)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.main(WritingThread.java:120)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.runMain(WritingThread.java:55)
                                                                                                    	at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45)
                                                                                                    Caused by: java.net.SocketException: Socket closed
                                                                                                    	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.writeInternal(ConscryptEngineSocket.java:728)
                                                                                                    	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.write(ConscryptEngineSocket.java:699)
                                                                                                    	at java.io.BufferedOutputStream.flushBuffer(BufferedOutputStream.java:81)
                                                                                                    	at java.io.BufferedOutputStream.flush(BufferedOutputStream.java:142)
                                                                                                    	at java.io.BufferedOutputStream.flush(BufferedOutputStream.java:143)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.flush(WritingThread.java:271)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.doFlush(WritingThread.java:422)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.sendFrames(WritingThread.java:384) 
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.main(WritingThread.java:120) 
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.runMain(WritingThread.java:55) 
                                                                                                    	at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45) 
2025-08-21 14:24:07.781  2772-3363  WebSocket               com.dspread.mdm.service              I  🔧 onError: 不触发重连，等待 onDisconnected 处理
2025-08-21 14:24:07.788  2772-3363  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接断开 - 客户端主动断开
2025-08-21 14:24:07.793  2772-3363  WebSocket               com.dspread.mdm.service              I  🔧    服务器关闭帧: code=-1, reason=无原因
2025-08-21 14:24:07.799  2772-3363  WebSocket               com.dspread.mdm.service              I  🔧    客户端关闭帧: code=1002, reason=No more WebSocket frame from the server.
2025-08-21 14:24:07.804  2772-3363  WebSocket               com.dspread.mdm.service              I  🔧    断开发起方: 客户端
2025-08-21 14:24:07.809  2772-3363  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 业务连接断开
2025-08-21 14:24:07.816  2772-3363  Platform                com.dspread.mdm.service              D  🔧 WakeLockManager 释放WiFi锁成功
2025-08-21 14:24:07.821  2772-3363  WebSocket               com.dspread.mdm.service              I  🔧 停止PING/PONG检测
2025-08-21 14:24:07.826  2772-3363  WebSocket               com.dspread.mdm.service              I  🔧 reconnect() 被调用，调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onDisconnected:746
2025-08-21 14:24:07.831  2772-3363  WebSocket               com.dspread.mdm.service              I  🔧 开始重连 WebSocket... (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onDisconnected:746)
2025-08-21 14:24:07.846  2772-3363  WebSocket               com.dspread.mdm.service              I  🔧 使用Provisioning配置的重连策略: delayPolicy=1, delaySwitch=1, delayTime=60
2025-08-21 14:24:07.851  2772-3363  WebSocket               com.dspread.mdm.service              I  🔧 当前断开记录数: 10, 延迟开关: 1
2025-08-21 14:24:07.860  2772-3363  WebSocket               com.dspread.mdm.service              I  🔧 断开记录 最早一次断开: 2025-08-21 14:09:02
2025-08-21 14:24:07.870  2772-3363  WebSocket               com.dspread.mdm.service              I  🔧 断开记录 最后一次断开: 2025-08-21 14:24:07
2025-08-21 14:24:07.874  2772-3363  WebSocket               com.dspread.mdm.service              I  🔧 时间差: 15分钟 (配置阈值: 1分钟)
2025-08-21 14:24:07.879  2772-3363  WebSocket               com.dspread.mdm.service              I  🔧 断开间隔正常，使用标准重连策略
2025-08-21 14:24:07.884  2772-3363  WebSocket               com.dspread.mdm.service              I  🔧 重连策略计算完成: 第1次重连，间隔3000ms (3秒)
2025-08-21 14:24:07.890  2772-3363  WebSocket               com.dspread.mdm.service              I  🔧 开始第1次重连，间隔3000ms (3秒) (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onDisconnected:746)
2025-08-21 14:25:21.230  2772-2772  WebSocket               com.dspread.mdm.service              I  🔧 重连 WebSocket: wss://api.dspreadserv.net/status/websocket/register?serialNo=MDEzNTQwOTAyMDI1MDMwNTAzOTk=&publicKey=TUlHZk1BMEdDU3FHU0liM0RRRUJBUVVBQTRHTkFEQ0JpUUtCZ1FEUFBSaDVCRDhCTldJNWtvUFlTVDdrSVI5a015QkJ0S2t6QXhjdmRpdUNhUUdUMFpVbTNQQjUwME12RENNTi9kZ1FncmNPNEI5cnFXYzBLam5nbDJKbVlKQVJXNW5sdndTakd3TUZDR1l3OFRrRG5CV2JLZHduNUpUc2w0ZFhOWlNvQUNjZEJ6cjlrTEE3WlFGNlFBZDFXVjdtUU1QaVAxMzk1a2VGOXpVdXZRSURBUUFC&query=0&msgVer=3&timestamp=1755757521197&signature=HpdLtnYfmRYL1iQvB/TL76NASxFOf1frTqzxTF5PTb+jK+qzF6FdGwgsnFM2QRa+dtDHBdaxOdWwNkme+UwmjOIc5qDEefPhRNp/G053T/JIIFHbkqWqRX/Vg0bhZ2cqlsy3khjJR8bnZPHy4ciagNLNPS4fvQKoSI7cF6EN5jE=
2025-08-21 14:25:21.237  2772-2772  WebSocket               com.dspread.mdm.service              I  🔧 未检测到系统代理设置
2025-08-21 14:25:22.246  2772-3372  TrafficStats            com.dspread.mdm.service              D  tagSocket(86) with statsTag=0xffffffff, statsUid=-1
2025-08-21 14:25:22.497  2772-3373  TrafficStats            com.dspread.mdm.service              D  tagSocket(96) with statsTag=0xffffffff, statsUid=-1
2025-08-21 14:25:24.132  2772-3374  Common                  com.dspread.mdm.service              I  ✅ WebSocket 物理连接成功，等待服务器 hellMsg
2025-08-21 14:25:24.140  2772-3374  WebSocket               com.dspread.mdm.service              I  🔧 ===== 心跳发送器实际配置 =====
2025-08-21 14:25:24.147  2772-3374  WebSocket               com.dspread.mdm.service              I  🔧 实际 PING 发送间隔: 30000ms (30秒)
2025-08-21 14:25:24.153  2772-3374  WebSocket               com.dspread.mdm.service              I  🔧 PING 定时器名称: PingSender
2025-08-21 14:25:24.159  2772-3374  WebSocket               com.dspread.mdm.service              I  🔧 实际 PONG 发送间隔: 0ms (0秒)
2025-08-21 14:25:24.165  2772-3374  WebSocket               com.dspread.mdm.service              I  🔧 PONG 定时器名称: PongSender
2025-08-21 14:25:24.170  2772-3374  WebSocket               com.dspread.mdm.service              I  🔧 ===============================
2025-08-21 14:25:24.176  2772-3374  WebSocket               com.dspread.mdm.service              I  🔧 启动PING/PONG检测，阈值: 连续3个PING无PONG响应
2025-08-21 14:25:24.458  2772-3374  Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"hellMsg":"hello 01354090202503050399","tranCode":"S0000","version":"1","rebootTime":"03:20:11","serialNo":"01354090202503050399","deviceStatus":"6"}
2025-08-21 14:25:24.465  2772-3374  Common                  com.dspread.mdm.service              I  ✅ 收到服务器 hellMsg，WebSocket 业务连接成功！
2025-08-21 14:25:24.470  2772-3374  Common                  com.dspread.mdm.service              I  ✅ WebSocket 业务连接成功！
2025-08-21 14:25:24.476  2772-3374  WebSocket               com.dspread.mdm.service              I  🔧 业务连接成功，重连计数已重置为 0
2025-08-21 14:25:24.485  2772-3374  Platform                com.dspread.mdm.service              D  🔧 WakeLockManager 获取WiFi锁成功，防止WiFi休眠
2025-08-21 14:25:24.490  2772-3374  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接成功，检查自身更新任务状态
2025-08-21 14:25:24.498  2772-3374  Receiver                com.dspread.mdm.service              D  🔧 发送广播: com.dspread.mdm.service.CHECK_SELF_UPDATE_STATUS
2025-08-21 14:25:24.504  2772-3374  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接成功
2025-08-21 14:25:24.509  2772-3374  WebSocket               com.dspread.mdm.service              I  🔧 设备状态: 6
2025-08-21 14:25:24.514  2772-3374  WebSocket               com.dspread.mdm.service              I  🔧 检查首次连接标志: isFirstTimeWebSocketConnected=false
2025-08-21 14:25:24.519  2772-3374  WebSocket               com.dspread.mdm.service              I  🔧 非首次连接，跳过初始化信息发送和初始化检查
2025-08-21 14:25:24.524  2772-3374  WebSocket               com.dspread.mdm.service              I  🔧 执行必要的状态同步检查
2025-08-21 14:25:24.529  2772-3374  WebSocket               com.dspread.mdm.service              I  🔧 检查服务更新结果
2025-08-21 14:25:24.534  2772-3374  Task                    com.dspread.mdm.service              W  ⚠️ 任务列表为空，跳过服务更新结果检查
2025-08-21 14:25:24.539  2772-3374  WebSocket               com.dspread.mdm.service              I  🔧 检查OS升级状态...
2025-08-21 14:25:24.544  2772-3374  Common                  com.dspread.mdm.service              D  🔧 OsUpdateStatusChecker WebSocket连接成功，检查OS升级状态
2025-08-21 14:25:24.553  2772-3374  Common                  com.dspread.mdm.service              D  🔧 OsUpdateStatusChecker 没有待检查的OS升级任务
2025-08-21 14:25:24.558  2772-3374  WebSocket               com.dspread.mdm.service              I  🔧 OS升级状态检查已触发
2025-08-21 14:26:24.172  2772-3374  WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误 (Ask Gemini)
                                                                                                    com.neovisionaries.ws.client.NoMoreFrameException: No more WebSocket frame from the server.
                                                                                                    	at com.neovisionaries.ws.client.WebSocketInputStream.readFrame(WebSocketInputStream.java:54)
                                                                                                    	at com.neovisionaries.ws.client.ReadingThread.readFrame(ReadingThread.java:338)
                                                                                                    	at com.neovisionaries.ws.client.ReadingThread.main(ReadingThread.java:99)
                                                                                                    	at com.neovisionaries.ws.client.ReadingThread.runMain(ReadingThread.java:64)
                                                                                                    	at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45)
2025-08-21 14:26:24.184  2772-3374  WebSocket               com.dspread.mdm.service              I  🔧 onError: 不触发重连，等待 onDisconnected 处理
2025-08-21 14:26:24.210  2772-3375  WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误 (Ask Gemini)
                                                                                                    com.neovisionaries.ws.client.WebSocketException: Flushing frames to the server failed: Socket closed
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.doFlush(WritingThread.java:434)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.sendFrames(WritingThread.java:384)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.main(WritingThread.java:108)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.runMain(WritingThread.java:55)
                                                                                                    	at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45)
                                                                                                    Caused by: java.net.SocketException: Socket closed
                                                                                                    	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.writeInternal(ConscryptEngineSocket.java:728)
                                                                                                    	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.write(ConscryptEngineSocket.java:699)
                                                                                                    	at java.io.BufferedOutputStream.flushBuffer(BufferedOutputStream.java:81)
                                                                                                    	at java.io.BufferedOutputStream.flush(BufferedOutputStream.java:142)
                                                                                                    	at java.io.BufferedOutputStream.flush(BufferedOutputStream.java:143)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.flush(WritingThread.java:271)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.doFlush(WritingThread.java:422)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.sendFrames(WritingThread.java:384) 
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.main(WritingThread.java:108) 
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.runMain(WritingThread.java:55) 
                                                                                                    	at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45) 
2025-08-21 14:26:24.222  2772-3375  WebSocket               com.dspread.mdm.service              I  🔧 onError: 不触发重连，等待 onDisconnected 处理
2025-08-21 14:26:24.243  2772-3375  WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误 (Ask Gemini)
                                                                                                    com.neovisionaries.ws.client.WebSocketException: Flushing frames to the server failed: Socket closed
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.doFlush(WritingThread.java:434)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.sendFrames(WritingThread.java:384)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.main(WritingThread.java:120)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.runMain(WritingThread.java:55)
                                                                                                    	at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45)
                                                                                                    Caused by: java.net.SocketException: Socket closed
                                                                                                    	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.writeInternal(ConscryptEngineSocket.java:728)
                                                                                                    	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.write(ConscryptEngineSocket.java:699)
                                                                                                    	at java.io.BufferedOutputStream.flushBuffer(BufferedOutputStream.java:81)
                                                                                                    	at java.io.BufferedOutputStream.flush(BufferedOutputStream.java:142)
                                                                                                    	at java.io.BufferedOutputStream.flush(BufferedOutputStream.java:143)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.flush(WritingThread.java:271)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.doFlush(WritingThread.java:422)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.sendFrames(WritingThread.java:384) 
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.main(WritingThread.java:120) 
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.runMain(WritingThread.java:55) 
                                                                                                    	at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45) 
2025-08-21 14:26:24.254  2772-3375  WebSocket               com.dspread.mdm.service              I  🔧 onError: 不触发重连，等待 onDisconnected 处理
2025-08-21 14:26:24.263  2772-3375  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接断开 - 客户端主动断开
2025-08-21 14:26:24.270  2772-3375  WebSocket               com.dspread.mdm.service              I  🔧    服务器关闭帧: code=-1, reason=无原因
2025-08-21 14:26:24.276  2772-3375  WebSocket               com.dspread.mdm.service              I  🔧    客户端关闭帧: code=1002, reason=No more WebSocket frame from the server.
2025-08-21 14:26:24.282  2772-3375  WebSocket               com.dspread.mdm.service              I  🔧    断开发起方: 客户端
2025-08-21 14:26:24.288  2772-3375  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 业务连接断开
2025-08-21 14:26:24.296  2772-3375  Platform                com.dspread.mdm.service              D  🔧 WakeLockManager 释放WiFi锁成功
2025-08-21 14:26:24.301  2772-3375  WebSocket               com.dspread.mdm.service              I  🔧 停止PING/PONG检测
2025-08-21 14:26:24.308  2772-3375  WebSocket               com.dspread.mdm.service              I  🔧 reconnect() 被调用，调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onDisconnected:746
2025-08-21 14:26:24.314  2772-3375  WebSocket               com.dspread.mdm.service              I  🔧 开始重连 WebSocket... (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onDisconnected:746)
2025-08-21 14:26:24.330  2772-3375  WebSocket               com.dspread.mdm.service              I  🔧 使用Provisioning配置的重连策略: delayPolicy=1, delaySwitch=1, delayTime=60
2025-08-21 14:26:24.336  2772-3375  WebSocket               com.dspread.mdm.service              I  🔧 当前断开记录数: 10, 延迟开关: 1
2025-08-21 14:26:24.347  2772-3375  WebSocket               com.dspread.mdm.service              I  🔧 断开记录 最早一次断开: 2025-08-21 14:11:05
2025-08-21 14:26:24.357  2772-3375  WebSocket               com.dspread.mdm.service              I  🔧 断开记录 最后一次断开: 2025-08-21 14:26:24
2025-08-21 14:26:24.362  2772-3375  WebSocket               com.dspread.mdm.service              I  🔧 时间差: 15分钟 (配置阈值: 1分钟)
2025-08-21 14:26:24.367  2772-3375  WebSocket               com.dspread.mdm.service              I  🔧 断开间隔正常，使用标准重连策略
2025-08-21 14:26:24.372  2772-3375  WebSocket               com.dspread.mdm.service              I  🔧 重连策略计算完成: 第1次重连，间隔3000ms (3秒)
2025-08-21 14:26:24.378  2772-3375  WebSocket               com.dspread.mdm.service              I  🔧 开始第1次重连，间隔3000ms (3秒) (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onDisconnected:746)
2025-08-21 14:27:23.479  2772-2772  WebSocket               com.dspread.mdm.service              I  🔧 重连 WebSocket: wss://api.dspreadserv.net/status/websocket/register?serialNo=MDEzNTQwOTAyMDI1MDMwNTAzOTk=&publicKey=TUlHZk1BMEdDU3FHU0liM0RRRUJBUVVBQTRHTkFEQ0JpUUtCZ1FEUFBSaDVCRDhCTldJNWtvUFlTVDdrSVI5a015QkJ0S2t6QXhjdmRpdUNhUUdUMFpVbTNQQjUwME12RENNTi9kZ1FncmNPNEI5cnFXYzBLam5nbDJKbVlKQVJXNW5sdndTakd3TUZDR1l3OFRrRG5CV2JLZHduNUpUc2w0ZFhOWlNvQUNjZEJ6cjlrTEE3WlFGNlFBZDFXVjdtUU1QaVAxMzk1a2VGOXpVdXZRSURBUUFC&query=0&msgVer=3&timestamp=1755757643449&signature=QmOBL2m0WsRdT4vgs8kC6505Hd7wdIN4jXiacQuac7qGU0LPI3stzWR7JGnoheICFeO0ry3M35bHm8gdc25stRV/hnXKbbWVzbZKtJM2ZGftKZN78X59/duvjG7bD3dBEyP6/GEmsYws3sNeb1oi476eGuzDHL3/AlwcmV06LZE=
2025-08-21 14:27:23.487  2772-2772  WebSocket               com.dspread.mdm.service              I  🔧 未检测到系统代理设置
2025-08-21 14:27:23.935  2772-3381  TrafficStats            com.dspread.mdm.service              D  tagSocket(86) with statsTag=0xffffffff, statsUid=-1
2025-08-21 14:27:24.186  2772-3382  TrafficStats            com.dspread.mdm.service              D  tagSocket(101) with statsTag=0xffffffff, statsUid=-1
2025-08-21 14:27:26.091  2772-3384  Common                  com.dspread.mdm.service              I  ✅ WebSocket 物理连接成功，等待服务器 hellMsg
2025-08-21 14:27:26.098  2772-3384  WebSocket               com.dspread.mdm.service              I  🔧 ===== 心跳发送器实际配置 =====
2025-08-21 14:27:26.104  2772-3384  WebSocket               com.dspread.mdm.service              I  🔧 实际 PING 发送间隔: 30000ms (30秒)
2025-08-21 14:27:26.110  2772-3384  WebSocket               com.dspread.mdm.service              I  🔧 PING 定时器名称: PingSender
2025-08-21 14:27:26.115  2772-3384  WebSocket               com.dspread.mdm.service              I  🔧 实际 PONG 发送间隔: 0ms (0秒)
2025-08-21 14:27:26.120  2772-3384  WebSocket               com.dspread.mdm.service              I  🔧 PONG 定时器名称: PongSender
2025-08-21 14:27:26.125  2772-3384  WebSocket               com.dspread.mdm.service              I  🔧 ===============================
2025-08-21 14:27:26.130  2772-3384  WebSocket               com.dspread.mdm.service              I  🔧 启动PING/PONG检测，阈值: 连续3个PING无PONG响应
2025-08-21 14:27:26.162  2772-3384  Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"hellMsg":"hello 01354090202503050399","tranCode":"S0000","version":"1","rebootTime":"03:02:52","serialNo":"01354090202503050399","deviceStatus":"6"}
2025-08-21 14:27:26.168  2772-3384  Common                  com.dspread.mdm.service              I  ✅ 收到服务器 hellMsg，WebSocket 业务连接成功！
2025-08-21 14:27:26.172  2772-3384  Common                  com.dspread.mdm.service              I  ✅ WebSocket 业务连接成功！
2025-08-21 14:27:26.177  2772-3384  WebSocket               com.dspread.mdm.service              I  🔧 业务连接成功，重连计数已重置为 0
2025-08-21 14:27:26.185  2772-3384  Platform                com.dspread.mdm.service              D  🔧 WakeLockManager 获取WiFi锁成功，防止WiFi休眠
2025-08-21 14:27:26.190  2772-3384  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接成功，检查自身更新任务状态
2025-08-21 14:27:26.198  2772-3384  Receiver                com.dspread.mdm.service              D  🔧 发送广播: com.dspread.mdm.service.CHECK_SELF_UPDATE_STATUS
2025-08-21 14:27:26.204  2772-3384  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接成功
2025-08-21 14:27:26.208  2772-3384  WebSocket               com.dspread.mdm.service              I  🔧 设备状态: 6
2025-08-21 14:27:26.213  2772-3384  WebSocket               com.dspread.mdm.service              I  🔧 检查首次连接标志: isFirstTimeWebSocketConnected=false
2025-08-21 14:27:26.218  2772-3384  WebSocket               com.dspread.mdm.service              I  🔧 非首次连接，跳过初始化信息发送和初始化检查
2025-08-21 14:27:26.223  2772-3384  WebSocket               com.dspread.mdm.service              I  🔧 执行必要的状态同步检查
2025-08-21 14:27:26.228  2772-3384  WebSocket               com.dspread.mdm.service              I  🔧 检查服务更新结果
2025-08-21 14:27:26.233  2772-3384  Task                    com.dspread.mdm.service              W  ⚠️ 任务列表为空，跳过服务更新结果检查
2025-08-21 14:27:26.238  2772-3384  WebSocket               com.dspread.mdm.service              I  🔧 检查OS升级状态...
2025-08-21 14:27:26.243  2772-3384  Common                  com.dspread.mdm.service              D  🔧 OsUpdateStatusChecker WebSocket连接成功，检查OS升级状态
2025-08-21 14:27:26.248  2772-3384  Common                  com.dspread.mdm.service              D  🔧 OsUpdateStatusChecker 没有待检查的OS升级任务
2025-08-21 14:27:26.253  2772-3384  WebSocket               com.dspread.mdm.service              I  🔧 OS升级状态检查已触发
2025-08-21 14:28:26.093  2772-3384  WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误 (Ask Gemini)
                                                                                                    com.neovisionaries.ws.client.NoMoreFrameException: No more WebSocket frame from the server.
                                                                                                    	at com.neovisionaries.ws.client.WebSocketInputStream.readFrame(WebSocketInputStream.java:54)
                                                                                                    	at com.neovisionaries.ws.client.ReadingThread.readFrame(ReadingThread.java:338)
                                                                                                    	at com.neovisionaries.ws.client.ReadingThread.main(ReadingThread.java:99)
                                                                                                    	at com.neovisionaries.ws.client.ReadingThread.runMain(ReadingThread.java:64)
                                                                                                    	at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45)
2025-08-21 14:28:26.105  2772-3384  WebSocket               com.dspread.mdm.service              I  🔧 onError: 不触发重连，等待 onDisconnected 处理
2025-08-21 14:28:26.129  2772-3385  WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误 (Ask Gemini)
                                                                                                    com.neovisionaries.ws.client.WebSocketException: Flushing frames to the server failed: Socket closed
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.doFlush(WritingThread.java:434)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.sendFrames(WritingThread.java:384)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.main(WritingThread.java:108)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.runMain(WritingThread.java:55)
                                                                                                    	at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45)
                                                                                                    Caused by: java.net.SocketException: Socket closed
                                                                                                    	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.writeInternal(ConscryptEngineSocket.java:728)
                                                                                                    	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.write(ConscryptEngineSocket.java:699)
                                                                                                    	at java.io.BufferedOutputStream.flushBuffer(BufferedOutputStream.java:81)
                                                                                                    	at java.io.BufferedOutputStream.flush(BufferedOutputStream.java:142)
                                                                                                    	at java.io.BufferedOutputStream.flush(BufferedOutputStream.java:143)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.flush(WritingThread.java:271)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.doFlush(WritingThread.java:422)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.sendFrames(WritingThread.java:384) 
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.main(WritingThread.java:108) 
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.runMain(WritingThread.java:55) 
                                                                                                    	at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45) 
2025-08-21 14:28:26.139  2772-3385  WebSocket               com.dspread.mdm.service              I  🔧 onError: 不触发重连，等待 onDisconnected 处理
2025-08-21 14:28:26.158  2772-3385  WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误 (Ask Gemini)
                                                                                                    com.neovisionaries.ws.client.WebSocketException: Flushing frames to the server failed: Socket closed
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.doFlush(WritingThread.java:434)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.sendFrames(WritingThread.java:384)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.main(WritingThread.java:120)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.runMain(WritingThread.java:55)
                                                                                                    	at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45)
                                                                                                    Caused by: java.net.SocketException: Socket closed
                                                                                                    	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.writeInternal(ConscryptEngineSocket.java:728)
                                                                                                    	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.write(ConscryptEngineSocket.java:699)
                                                                                                    	at java.io.BufferedOutputStream.flushBuffer(BufferedOutputStream.java:81)
                                                                                                    	at java.io.BufferedOutputStream.flush(BufferedOutputStream.java:142)
                                                                                                    	at java.io.BufferedOutputStream.flush(BufferedOutputStream.java:143)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.flush(WritingThread.java:271)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.doFlush(WritingThread.java:422)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.sendFrames(WritingThread.java:384) 
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.main(WritingThread.java:120) 
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.runMain(WritingThread.java:55) 
                                                                                                    	at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45) 
2025-08-21 14:28:26.168  2772-3385  WebSocket               com.dspread.mdm.service              I  🔧 onError: 不触发重连，等待 onDisconnected 处理
2025-08-21 14:28:26.177  2772-3385  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接断开 - 客户端主动断开
2025-08-21 14:28:26.184  2772-3385  WebSocket               com.dspread.mdm.service              I  🔧    服务器关闭帧: code=-1, reason=无原因
2025-08-21 14:28:26.190  2772-3385  WebSocket               com.dspread.mdm.service              I  🔧    客户端关闭帧: code=1002, reason=No more WebSocket frame from the server.
2025-08-21 14:28:26.196  2772-3385  WebSocket               com.dspread.mdm.service              I  🔧    断开发起方: 客户端
2025-08-21 14:28:26.202  2772-3385  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 业务连接断开
2025-08-21 14:28:26.210  2772-3385  Platform                com.dspread.mdm.service              D  🔧 WakeLockManager 释放WiFi锁成功
2025-08-21 14:28:26.216  2772-3385  WebSocket               com.dspread.mdm.service              I  🔧 停止PING/PONG检测
2025-08-21 14:28:26.222  2772-3385  WebSocket               com.dspread.mdm.service              I  🔧 reconnect() 被调用，调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onDisconnected:746
2025-08-21 14:28:26.227  2772-3385  WebSocket               com.dspread.mdm.service              I  🔧 开始重连 WebSocket... (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onDisconnected:746)
2025-08-21 14:28:26.241  2772-3385  WebSocket               com.dspread.mdm.service              I  🔧 使用Provisioning配置的重连策略: delayPolicy=1, delaySwitch=1, delayTime=60
2025-08-21 14:28:26.246  2772-3385  WebSocket               com.dspread.mdm.service              I  🔧 当前断开记录数: 10, 延迟开关: 1
2025-08-21 14:28:26.256  2772-3385  WebSocket               com.dspread.mdm.service              I  🔧 断开记录 最早一次断开: 2025-08-21 14:12:33
2025-08-21 14:28:26.265  2772-3385  WebSocket               com.dspread.mdm.service              I  🔧 断开记录 最后一次断开: 2025-08-21 14:28:26
2025-08-21 14:28:26.270  2772-3385  WebSocket               com.dspread.mdm.service              I  🔧 时间差: 15分钟 (配置阈值: 1分钟)
2025-08-21 14:28:26.275  2772-3385  WebSocket               com.dspread.mdm.service              I  🔧 断开间隔正常，使用标准重连策略
2025-08-21 14:28:26.280  2772-3385  WebSocket               com.dspread.mdm.service              I  🔧 重连策略计算完成: 第1次重连，间隔3000ms (3秒)
2025-08-21 14:28:26.285  2772-3385  WebSocket               com.dspread.mdm.service              I  🔧 开始第1次重连，间隔3000ms (3秒) (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onDisconnected:746)
2025-08-21 14:28:32.206  2772-2772  WebSocket               com.dspread.mdm.service              I  🔧 重连 WebSocket: wss://api.dspreadserv.net/status/websocket/register?serialNo=MDEzNTQwOTAyMDI1MDMwNTAzOTk=&publicKey=TUlHZk1BMEdDU3FHU0liM0RRRUJBUVVBQTRHTkFEQ0JpUUtCZ1FEUFBSaDVCRDhCTldJNWtvUFlTVDdrSVI5a015QkJ0S2t6QXhjdmRpdUNhUUdUMFpVbTNQQjUwME12RENNTi9kZ1FncmNPNEI5cnFXYzBLam5nbDJKbVlKQVJXNW5sdndTakd3TUZDR1l3OFRrRG5CV2JLZHduNUpUc2w0ZFhOWlNvQUNjZEJ6cjlrTEE3WlFGNlFBZDFXVjdtUU1QaVAxMzk1a2VGOXpVdXZRSURBUUFC&query=0&msgVer=3&timestamp=1755757712182&signature=YP76aXo1KWmlmketQ7vpoqPVnCITe9jFHQox2o0zsBZ3I+uv9EA2ZxH/j3u2jHnhKZDhRxPn0rOueRtZXkoeGeA3L5SqWgsbFfcS3qOB/tMFz69S3xT8O7r47/7ftegzgncr1CKK35uFJtPx1f6i4pzhhLmqbauZNEBAEfDclBg=
2025-08-21 14:28:32.220  2772-2772  WebSocket               com.dspread.mdm.service              I  🔧 未检测到系统代理设置
2025-08-21 14:28:32.646  2772-3392  TrafficStats            com.dspread.mdm.service              D  tagSocket(101) with statsTag=0xffffffff, statsUid=-1
2025-08-21 14:28:32.896  2772-3393  TrafficStats            com.dspread.mdm.service              D  tagSocket(104) with statsTag=0xffffffff, statsUid=-1
2025-08-21 14:28:34.495  2772-3394  Common                  com.dspread.mdm.service              I  ✅ WebSocket 物理连接成功，等待服务器 hellMsg
2025-08-21 14:28:34.502  2772-3394  WebSocket               com.dspread.mdm.service              I  🔧 ===== 心跳发送器实际配置 =====
2025-08-21 14:28:34.510  2772-3394  WebSocket               com.dspread.mdm.service              I  🔧 实际 PING 发送间隔: 30000ms (30秒)
2025-08-21 14:28:34.517  2772-3394  WebSocket               com.dspread.mdm.service              I  🔧 PING 定时器名称: PingSender
2025-08-21 14:28:34.523  2772-3394  WebSocket               com.dspread.mdm.service              I  🔧 实际 PONG 发送间隔: 0ms (0秒)
2025-08-21 14:28:34.529  2772-3394  WebSocket               com.dspread.mdm.service              I  🔧 PONG 定时器名称: PongSender
2025-08-21 14:28:34.535  2772-3394  WebSocket               com.dspread.mdm.service              I  🔧 ===============================
2025-08-21 14:28:34.541  2772-3394  WebSocket               com.dspread.mdm.service              I  🔧 启动PING/PONG检测，阈值: 连续3个PING无PONG响应
2025-08-21 14:28:34.823  2772-3394  Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"hellMsg":"hello 01354090202503050399","tranCode":"S0000","version":"1","rebootTime":"02:22:08","serialNo":"01354090202503050399","deviceStatus":"6"}
2025-08-21 14:28:34.830  2772-3394  Common                  com.dspread.mdm.service              I  ✅ 收到服务器 hellMsg，WebSocket 业务连接成功！
2025-08-21 14:28:34.837  2772-3394  Common                  com.dspread.mdm.service              I  ✅ WebSocket 业务连接成功！
2025-08-21 14:28:34.843  2772-3394  WebSocket               com.dspread.mdm.service              I  🔧 业务连接成功，重连计数已重置为 0
2025-08-21 14:28:34.852  2772-3394  Platform                com.dspread.mdm.service              D  🔧 WakeLockManager 获取WiFi锁成功，防止WiFi休眠
2025-08-21 14:28:34.858  2772-3394  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接成功，检查自身更新任务状态
2025-08-21 14:28:34.866  2772-3394  Receiver                com.dspread.mdm.service              D  🔧 发送广播: com.dspread.mdm.service.CHECK_SELF_UPDATE_STATUS
2025-08-21 14:28:34.872  2772-3394  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接成功
2025-08-21 14:28:34.877  2772-3394  WebSocket               com.dspread.mdm.service              I  🔧 设备状态: 6
2025-08-21 14:28:34.882  2772-3394  WebSocket               com.dspread.mdm.service              I  🔧 检查首次连接标志: isFirstTimeWebSocketConnected=false
2025-08-21 14:28:34.887  2772-3394  WebSocket               com.dspread.mdm.service              I  🔧 非首次连接，跳过初始化信息发送和初始化检查
2025-08-21 14:28:34.892  2772-3394  WebSocket               com.dspread.mdm.service              I  🔧 执行必要的状态同步检查
2025-08-21 14:28:34.896  2772-3394  WebSocket               com.dspread.mdm.service              I  🔧 检查服务更新结果
2025-08-21 14:28:34.901  2772-3394  Task                    com.dspread.mdm.service              W  ⚠️ 任务列表为空，跳过服务更新结果检查
2025-08-21 14:28:34.906  2772-3394  WebSocket               com.dspread.mdm.service              I  🔧 检查OS升级状态...
2025-08-21 14:28:34.911  2772-3394  Common                  com.dspread.mdm.service              D  🔧 OsUpdateStatusChecker WebSocket连接成功，检查OS升级状态
2025-08-21 14:28:34.916  2772-3394  Common                  com.dspread.mdm.service              D  🔧 OsUpdateStatusChecker 没有待检查的OS升级任务
2025-08-21 14:28:34.921  2772-3394  WebSocket               com.dspread.mdm.service              I  🔧 OS升级状态检查已触发
2025-08-21 14:29:06.963  2772-2772  Receiver                com.dspread.mdm.service              D  🔧 屏幕点亮
2025-08-21 14:29:06.974  2772-2772  WebSocket               com.dspread.mdm.service              W  ⚠️ 未知触发条件: screen_on，默认按被动式处理
2025-08-21 14:29:07.072  2772-2785  ead.mdm.service         com.dspread.mdm.service              I  Background young concurrent copying GC freed 40335(2413KB) AllocSpace objects, 7(140KB) LOS objects, 36% free, 4526KB/7126KB, paused 2.803ms,200us total 163.927ms
2025-08-21 14:29:07.088  2772-2787  System                  com.dspread.mdm.service              W  A resource failed to call close. 
2025-08-21 14:29:07.100  2772-2787  System                  com.dspread.mdm.service              W  A resource failed to call close. 
2025-08-21 14:29:07.108  2772-2772  WebSocket               com.dspread.mdm.service              I  🔧 C0201 被动式上送拒绝: screen_on (拒绝: 3)
2025-08-21 14:29:07.108  2772-2787  System                  com.dspread.mdm.service              W  A resource failed to call close. 
2025-08-21 14:29:07.111  2772-2787  System                  com.dspread.mdm.service              W  A resource failed to call close. 
2025-08-21 14:29:07.114  2772-2772  WebSocket               com.dspread.mdm.service              I  🔧 C0201 设备状态上送被流量控制阻止: 被动事件 'screen_on' 在平衡模式 - 重要变化下未启用
2025-08-21 14:29:07.120  2772-2772  Receiver                com.dspread.mdm.service              D  🔧 屏幕点亮处理完成
2025-08-21 14:29:09.374  2772-2772  Receiver                com.dspread.mdm.service              D  🔧 电源已连接
2025-08-21 14:29:09.384  2772-2772  WebSocket               com.dspread.mdm.service              I  🔧 C0902 主动式上送: charging_state_change (主动: 2)
2025-08-21 14:29:09.487  2772-2772  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0902","serialNo":"01354090202503050399","request_time":"1755757749411","request_id":"1755757749411C0902","version":"1","data":{"batteryLife":100,"batteryHealth":2,"temprature":"25.5","isCharging":"1","isLowBattery":"0","powerOff":"0","outOfBattery":"0"},"myVersionName":"1.1.03.20250821.DSPREAD.MDM.SERVICE","terminalDate":"20250821142909"}
2025-08-21 14:29:09.503  2772-2772  WebSocket               com.dspread.mdm.service              I  🔧 C0902 电池状态上送成功 (1, trigger=charging_state_change)
2025-08-21 14:29:29.603  2772-3395  WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 1 (第1个，待响应: 1)
2025-08-21 14:29:29.986  2772-3394  WebSocket               com.dspread.mdm.service              I  🔧 收到服务器 PONG 帧: 1 (待响应PING: 0)
