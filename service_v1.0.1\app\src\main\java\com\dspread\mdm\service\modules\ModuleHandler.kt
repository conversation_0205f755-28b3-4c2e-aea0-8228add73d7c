package com.dspread.mdm.service.modules

import com.dspread.mdm.service.utils.log.Logger
import org.json.JSONObject

/**
 * 模块处理器基础接口
 * 所有四大模块的Handler都需要实现此接口
 */
interface ModuleHandler {
    
    /**
     * 处理WebSocket消息
     * @param message 原始消息字符串
     * @return 处理结果
     */
    suspend fun handleMessage(message: String): Result<Unit>
    
    /**
     * 发送确认响应（C0000）
     * @param jsonObject 消息JSON对象
     */
    fun sendAcknowledgment(jsonObject: JSONObject)
    
    /**
     * 获取模块名称
     */
    fun getModuleName(): String
    
    /**
     * 模块是否启用
     */
    fun isEnabled(): Boolean
    
    /**
     * 启用/禁用模块
     */
    fun setEnabled(enabled: Boolean)
}

/**
 * 模块处理器抽象基类
 * 提供通用的实现
 */
abstract class BaseModuleHandler : ModuleHandler {
    
    private var _enabled: Boolean = true

    override fun isEnabled(): Boolean = _enabled

    override fun setEnabled(enabled: Boolean) {
        this._enabled = enabled
    }
    
    override fun sendAcknowledgment(jsonObject: JSONObject) {
        try {
            val requestId = jsonObject.optString("request_id")
            val requestTime = jsonObject.optString("request_time")
            
            if (requestId.isNotEmpty() && requestTime.isNotEmpty()) {
                // TODO: 集成到WsMessageSender
                // WsMessageSender.sendWebSocketResponse(requestId, requestTime, "0", null)
                Logger.com("发送C0000响应确认: requestId=$requestId, module=${getModuleName()}")
            }
        } catch (e: Exception) {
            Logger.com("发送确认响应失败: ${e.message}")
        }
    }
    
    /**
     * 解析JSON消息的通用方法
     */
    protected fun parseJsonMessage(message: String): JSONObject {
        return JSONObject(message)
    }
    
    /**
     * 检查模块是否启用，如果未启用则返回失败结果
     */
    protected fun checkModuleEnabled(): Result<Unit> {
        return if (_enabled) {
            Result.success(Unit)
        } else {
            Result.failure(IllegalStateException("Module ${getModuleName()} is disabled"))
        }
    }
}

/**
 * 模块管理器接口
 * 用于管理各个模块的生命周期
 */
interface ModuleManager {
    
    /**
     * 初始化模块
     */
    suspend fun initialize(): Result<Unit>
    
    /**
     * 启动模块
     */
    suspend fun start(): Result<Unit>
    
    /**
     * 停止模块
     */
    suspend fun stop(): Result<Unit>
    
    /**
     * 获取模块状态
     */
    fun getStatus(): ModuleStatus
    
    /**
     * 获取模块名称
     */
    fun getModuleName(): String
}

/**
 * 模块状态枚举
 */
enum class ModuleStatus {
    UNINITIALIZED,  // 未初始化
    INITIALIZED,    // 已初始化
    STARTING,       // 启动中
    RUNNING,        // 运行中
    STOPPING,       // 停止中
    STOPPED,        // 已停止
    ERROR           // 错误状态
}

/**
 * 模块管理器抽象基类
 */
abstract class BaseModuleManager : ModuleManager {
    
    private var _status: ModuleStatus = ModuleStatus.UNINITIALIZED

    override fun getStatus(): ModuleStatus = _status

    protected fun updateStatus(newStatus: ModuleStatus) {
        _status = newStatus
        Logger.com("Module ${getModuleName()} status changed to: $newStatus")
    }
    
    override suspend fun initialize(): Result<Unit> {
        return try {
            updateStatus(ModuleStatus.INITIALIZED)
            onInitialize()
        } catch (e: Exception) {
            updateStatus(ModuleStatus.ERROR)
            Result.failure(e)
        }
    }
    
    override suspend fun start(): Result<Unit> {
        return try {
            if (_status != ModuleStatus.INITIALIZED && _status != ModuleStatus.STOPPED) {
                return Result.failure(IllegalStateException("Module must be initialized before starting"))
            }
            
            updateStatus(ModuleStatus.STARTING)
            val result = onStart()
            
            if (result.isSuccess) {
                updateStatus(ModuleStatus.RUNNING)
            } else {
                updateStatus(ModuleStatus.ERROR)
            }
            
            result
        } catch (e: Exception) {
            updateStatus(ModuleStatus.ERROR)
            Result.failure(e)
        }
    }
    
    override suspend fun stop(): Result<Unit> {
        return try {
            updateStatus(ModuleStatus.STOPPING)
            val result = onStop()
            
            updateStatus(if (result.isSuccess) ModuleStatus.STOPPED else ModuleStatus.ERROR)
            result
        } catch (e: Exception) {
            updateStatus(ModuleStatus.ERROR)
            Result.failure(e)
        }
    }
    
    /**
     * 子类需要实现的初始化方法
     */
    protected abstract suspend fun onInitialize(): Result<Unit>
    
    /**
     * 子类需要实现的启动方法
     */
    protected abstract suspend fun onStart(): Result<Unit>
    
    /**
     * 子类需要实现的停止方法
     */
    protected abstract suspend fun onStop(): Result<Unit>
}

/**
 * 模块配置基类
 */
abstract class ModuleConfig {
    abstract fun isValid(): Boolean
    abstract fun toJson(): JSONObject
    
    companion object {
        inline fun <reified T : ModuleConfig> fromJson(json: JSONObject): T? {
            return try {
                // 子类需要实现具体的反序列化逻辑
                null
            } catch (e: Exception) {
                null
            }
        }
    }
}
