package com.dspread.mdm.service.network.websocket.message.strategy

import android.content.Context
import com.dspread.mdm.service.modules.provisioning.ProvisioningManager
import com.dspread.mdm.service.utils.log.Logger

/**
 * 消息上送策略管理器
 * 基于Provisioning Config中的uploadMode实现分等级的事件上送策略：
 * - uploadMode = "0"：省电上送（等级2）- 长间隔或严格条件
 * - uploadMode = "1"：策略上送（等级1）- 有条件触发
 * - uploadMode = "2"：实时上送（等级0）- 立即发送
 */
object UploadStrategy {

    /**
     * 上送等级枚举（对应uploadMode值）
     */
    enum class UploadLevel(val mode: String, val level: Int, val description: String) {
        REALTIME("2", 0, "实时上送"),
        STRATEGY("1", 1, "策略上送"),
        POWER_SAVING("0", 2, "省电上送")
    }

    /**
     * 从uploadMode获取上送等级
     */
    fun getUploadLevelFromMode(uploadMode: String): UploadLevel {
        return when (uploadMode) {
            "0" -> UploadLevel.POWER_SAVING
            "1" -> UploadLevel.STRATEGY
            "2" -> UploadLevel.REALTIME
            else -> {
                Logger.wsm("未知uploadMode: $uploadMode，默认使用策略模式")
                UploadLevel.STRATEGY
            }
        }
    }

    /**
     * 获取当前的上送等级（从Provisioning Config读取）
     */
    fun getCurrentUploadLevel(context: Context): UploadLevel {
        return try {
            val provisioningManager = ProvisioningManager.getInstance(context)
            val config = provisioningManager.getCurrentConfig()
            val uploadMode = config?.polling?.uploadMode ?: "1"

            Logger.wsm("当前uploadMode: $uploadMode")
            getUploadLevelFromMode(uploadMode)

        } catch (e: Exception) {
            Logger.wsmE("获取uploadMode失败，使用默认策略模式", e)
            UploadLevel.STRATEGY
        }
    }
    
    /**
     * 消息类型与上送策略的映射
     */
    private val messageStrategyMap = mapOf(
        // C0901 - 应用信息上传
        "C0901" to MessageStrategy(
            realTimeConditions = listOf("app_install", "app_uninstall", "service_change"),
            strategyConditions = listOf("app_update", "permission_change"),
            powerSavingInterval = 30 * 60 * 1000L, // 30分钟
            description = "应用信息上传"
        ),
        
        // C0902 - 电池状态上传
        "C0902" to MessageStrategy(
            realTimeConditions = listOf("battery_low_critical", "charging_state_change", "temperature_abnormal"),
            strategyConditions = listOf("battery_level_change_5", "temperature_change_5"),
            powerSavingInterval = 60 * 60 * 1000L, // 1小时
            description = "电池状态上传"
        ),
        
        // C0903 - 数据信息上传
        "C0903" to MessageStrategy(
            realTimeConditions = listOf("location_geofence_change"),
            strategyConditions = listOf("location_change", "hardware_change"),
            powerSavingInterval = 60 * 60 * 1000L, // 1小时
            description = "数据信息上传"
        ),
        
        // C0904 - 网络状态上传
        "C0904" to MessageStrategy(
            realTimeConditions = listOf("network_connect", "network_disconnect", "wifi_switch"),
            strategyConditions = listOf("signal_strength_change", "ip_change"),
            powerSavingInterval = 2 * 60 * 60 * 1000L, // 2小时
            description = "网络状态上传"
        ),
        
        // C0109 - 终端信息上传
        "C0109" to MessageStrategy(
            realTimeConditions = listOf("first_connection", "manual_trigger"),
            strategyConditions = listOf("significant_change"),
            powerSavingInterval = 4 * 60 * 60 * 1000L, // 4小时
            description = "终端信息上传"
        ),
        
        // C0202 - 设备事件上传
        "C0202" to MessageStrategy(
            realTimeConditions = listOf("crash", "security_event", "system_error"),
            strategyConditions = listOf("warning_event"),
            powerSavingInterval = Long.MAX_VALUE, // 不定时上送，只按事件触发
            description = "设备事件上传"
        )
    )
    
    /**
     * 消息策略数据类
     */
    data class MessageStrategy(
        val realTimeConditions: List<String>,      // 实时上送条件
        val strategyConditions: List<String>,      // 策略上送条件
        val powerSavingInterval: Long,             // 省电模式间隔（毫秒）
        val description: String                    // 描述
    )
    
    /**
     * 上送决策结果
     */
    data class UploadDecision(
        val shouldUpload: Boolean,                 // 是否应该上送
        val level: UploadLevel,                   // 上送等级
        val reason: String,                       // 决策原因
        val delay: Long = 0L                      // 延迟时间（毫秒）
    )
    
    /**
     * 判断是否应该上送消息（基于uploadMode）
     *
     * @param context 上下文
     * @param messageType 消息类型（如C0901）
     * @param trigger 触发条件
     * @param lastUploadTime 上次上送时间
     * @param currentTime 当前时间
     * @return 上送决策结果
     */
    fun shouldUpload(
        context: Context,
        messageType: String,
        trigger: String,
        lastUploadTime: Long = 0L,
        currentTime: Long = System.currentTimeMillis()
    ): UploadDecision {

        // 获取当前uploadMode对应的等级
        val currentLevel = getCurrentUploadLevel(context)
        val strategy = messageStrategyMap[messageType]

        if (strategy == null) {
            Logger.wsm("未知消息类型: $messageType，默认策略上送")
            return UploadDecision(true, currentLevel, "未知消息类型，默认策略")
        }

        Logger.wsm("$messageType 上送判断: uploadMode=${currentLevel.mode}, trigger=$trigger")

        // 根据当前uploadMode等级判断
        when (currentLevel) {
            UploadLevel.REALTIME -> {
                // 实时模式：几乎所有条件都上送
                if (trigger in strategy.realTimeConditions || trigger in strategy.strategyConditions) {
                    Logger.wsm("🔥 $messageType 实时模式上送: $trigger")
                    return UploadDecision(true, UploadLevel.REALTIME, "实时模式: $trigger")
                }
                // 实时模式下，定时上送间隔也更短
                val realtimeInterval = strategy.powerSavingInterval / 4 // 缩短到1/4
                if (realtimeInterval != Long.MAX_VALUE && currentTime - lastUploadTime >= realtimeInterval) {
                    Logger.wsm("🔥 $messageType 实时模式定时上送")
                    return UploadDecision(true, UploadLevel.REALTIME, "实时模式定时")
                }
            }

            UploadLevel.STRATEGY -> {
                // 策略模式：按照设计的条件上送
                if (trigger in strategy.realTimeConditions) {
                    Logger.wsm("⚡ $messageType 策略模式重要条件上送: $trigger")
                    return UploadDecision(true, UploadLevel.STRATEGY, "策略模式重要: $trigger")
                }
                if (trigger in strategy.strategyConditions) {
                    val minInterval = 2000L // 2秒最小间隔
                    if (currentTime - lastUploadTime >= minInterval) {
                        Logger.wsm("⚡ $messageType 策略模式条件上送: $trigger")
                        return UploadDecision(true, UploadLevel.STRATEGY, "策略条件: $trigger")
                    } else {
                        Logger.wsm("$messageType 策略模式发送过于频繁，延迟")
                        val delay = minInterval - (currentTime - lastUploadTime)
                        return UploadDecision(true, UploadLevel.STRATEGY, "策略延迟: $trigger", delay)
                    }
                }
                // 策略模式的定时上送
                if (strategy.powerSavingInterval != Long.MAX_VALUE && currentTime - lastUploadTime >= strategy.powerSavingInterval) {
                    Logger.wsm("⚡ $messageType 策略模式定时上送")
                    return UploadDecision(true, UploadLevel.STRATEGY, "策略定时")
                }
            }

            UploadLevel.POWER_SAVING -> {
                // 省电模式：只在最重要的条件下上送
                if (trigger in strategy.realTimeConditions) {
                    Logger.wsm("🔋 $messageType 省电模式重要条件上送: $trigger")
                    return UploadDecision(true, UploadLevel.POWER_SAVING, "省电重要: $trigger")
                }
                // 省电模式下，定时上送间隔更长
                val powerSavingInterval = if (strategy.powerSavingInterval == Long.MAX_VALUE) {
                    Long.MAX_VALUE
                } else {
                    strategy.powerSavingInterval * 2 // 延长到2倍
                }
                if (powerSavingInterval != Long.MAX_VALUE && currentTime - lastUploadTime >= powerSavingInterval) {
                    Logger.wsm("🔋 $messageType 省电模式定时上送")
                    return UploadDecision(true, UploadLevel.POWER_SAVING, "省电定时")
                }
            }
        }

        // 不满足任何上送条件
        Logger.wsm("$messageType 不满足上送条件: $trigger (mode=${currentLevel.mode})")
        return UploadDecision(false, currentLevel, "不满足上送条件: $trigger")
    }
    
    /**
     * 获取消息类型的策略信息
     */
    fun getMessageStrategy(messageType: String): MessageStrategy? {
        return messageStrategyMap[messageType]
    }
    
    /**
     * 获取所有支持的消息类型
     */
    fun getSupportedMessageTypes(): Set<String> {
        return messageStrategyMap.keys
    }
    
    /**
     * 获取策略统计信息
     */
    fun getStrategyStats(): String {
        return buildString {
            appendLine("消息上送策略统计:")
            messageStrategyMap.forEach { (type, strategy) ->
                appendLine("  $type - ${strategy.description}")
                appendLine("    实时条件: ${strategy.realTimeConditions.joinToString()}")
                appendLine("    策略条件: ${strategy.strategyConditions.joinToString()}")
                appendLine("    省电间隔: ${strategy.powerSavingInterval / 1000}秒")
            }
        }
    }
}


