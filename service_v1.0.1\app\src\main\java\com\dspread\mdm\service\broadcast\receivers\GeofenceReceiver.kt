package com.dspread.mdm.service.broadcast.receivers

import android.app.AlarmManager
import android.app.Dialog
import android.app.PendingIntent
import android.bluetooth.BluetoothAdapter
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.location.LocationManager
import android.net.ConnectivityManager
import android.net.NetworkInfo
import android.net.wifi.WifiManager
import android.os.BatteryManager
import android.os.Build
import android.os.CountDownTimer
import android.os.Handler
import android.os.Looper
import com.dspread.mdm.service.SmartMdmServiceApp
import com.dspread.mdm.service.broadcast.core.BroadcastSender
import com.dspread.mdm.service.constants.Constants
import com.dspread.mdm.service.modules.geofence.GeofenceHandler
import com.dspread.mdm.service.modules.geofence.GeofenceStateManager
import com.dspread.mdm.service.modules.geofence.SecurityActionHandler
import com.dspread.mdm.service.modules.geofence.location.GpsLocationManager
import kotlinx.coroutines.*
import com.dspread.mdm.service.network.websocket.message.WsMessageSender
import com.dspread.mdm.service.platform.api.network.WiFiManagerApi
import com.dspread.mdm.service.broadcast.core.BroadcastActions
import com.dspread.mdm.service.utils.log.Logger
import com.dspread.mdm.service.platform.monitor.UserInteractionMonitor
import com.dspread.mdm.service.ui.dialog.RebootWarningDialog
import com.dspread.mdm.service.ui.dialog.GeofenceWarningDialog
import com.dspread.mdm.service.ui.dialog.RebootFloatWindow


/**
 * 地理围栏模块广播接收器
 * 统一管理地理围栏相关的所有广播事件，包括：
 * - 系统事件（启动、关机、网络变化等）
 * - 地理围栏事件（警告、状态变更、进入/离开围栏等）
 * - 定时任务（地理围栏检查、蓝牙扫描等）
 */
class GeofenceReceiver : BroadcastReceiver() {

    companion object {
        private const val TAG = "[GeofenceReceiver]"

        // 定时器相关
        private const val SYSTEM_TIMER_INTERVAL = 1 // 每分钟检查一次
        private const val GEOFENCE_CHECK_INTERVAL = 1 // 每分钟检查GeoFence

        // 静态变量
        private var warningDialog: Dialog? = null
        private var countDownTimer: CountDownTimer? = null
        private var lockCountdownFloatWindow: RebootFloatWindow? = null
        private val handler = Handler(Looper.getMainLooper())

        // 地理围栏专用协程作用域
        private val geofenceScope = CoroutineScope(Dispatchers.IO + SupervisorJob())
        
        /**
         * 创建IntentFilter用于注册广播
         */
        fun createIntentFilter(): IntentFilter {
            return IntentFilter().apply {
                // 位置服务相关
                addAction(LocationManager.PROVIDERS_CHANGED_ACTION)
                addAction(LocationManager.MODE_CHANGED_ACTION)
                
                // 蓝牙相关
                addAction(BluetoothAdapter.ACTION_STATE_CHANGED)
                addAction(BluetoothAdapter.ACTION_DISCOVERY_STARTED)
                addAction(BluetoothAdapter.ACTION_DISCOVERY_FINISHED)
                
                // WiFi相关
                addAction(WifiManager.WIFI_STATE_CHANGED_ACTION)
                addAction(WifiManager.NETWORK_STATE_CHANGED_ACTION)
                
                // 网络连接相关
                addAction(ConnectivityManager.CONNECTIVITY_ACTION)
                
                // 电源相关
                addAction(Intent.ACTION_BATTERY_CHANGED)
                addAction(Intent.ACTION_POWER_CONNECTED)
                addAction(Intent.ACTION_POWER_DISCONNECTED)
                
                // 屏幕相关
                addAction(Intent.ACTION_SCREEN_ON)
                addAction(Intent.ACTION_SCREEN_OFF)
                addAction(Intent.ACTION_USER_PRESENT)
                
                // 系统相关
                addAction(Intent.ACTION_BOOT_COMPLETED)
                addAction(Intent.ACTION_SHUTDOWN)

                // 地理围栏相关广播
                addAction(BroadcastActions.ACTION_GEOFENCING_DETECTED_WARNING)
                addAction(BroadcastActions.ACTION_GEOFENCING_DETECTED_WARNING_CLOSE)
                addAction(BroadcastActions.ACTION_GEOFENCING_DETECTED_LOCK)
                addAction(BroadcastActions.ACTION_GEOFENCING_RESET_LOCKSCREEN_TIMER)
                addAction(BroadcastActions.ACTION_ENTER_GEOFENCE)
                addAction(BroadcastActions.ACTION_OUT_OF_GEOFENCE)
                addAction(BroadcastActions.ACTION_CLOSE_LOCKSCREEN)
                addAction(BroadcastActions.ACTION_EXPIRE_REBOOT)

                // 定时任务广播
                addAction(BroadcastActions.ACTION_SYSTEM_TIMER)
                addAction(BroadcastActions.ACTION_GEOFENCE_CHECK)
                addAction(BroadcastActions.ACTION_BLE_SCAN_TIMER)
                addAction(BroadcastActions.GPS_SCHEDULE_TIME)

                // 数据擦除相关广播
                addAction(BroadcastActions.UNBIND_LAUNCHER)

                // 自定义地理围栏广播
                addAction("com.dspread.mdm.GEOFENCE_WARNING")
                addAction("com.dspread.mdm.GEOFENCE_STATUS_CHANGE")
                addAction("com.dspread.mdm.GEOFENCE_EMERGENCY")
            }
        }

        /**
         * 启动系统定时器
         */
        fun startSystemTimer(context: Context) {
            Logger.geo("$TAG 启动系统定时器")

            try {
                // 启动主定时器
                setSystemTimer(context, BroadcastActions.ACTION_SYSTEM_TIMER, SYSTEM_TIMER_INTERVAL)

                // 启动地理围栏检查定时器
                setSystemTimer(context, BroadcastActions.ACTION_GEOFENCE_CHECK, GEOFENCE_CHECK_INTERVAL)

            } catch (e: Exception) {
                Logger.geoE("$TAG 启动系统定时器失败", e)
            }
        }

        /**
         * 停止系统定时器
         */
        fun stopSystemTimer(context: Context) {
            Logger.geo("$TAG 停止系统定时器")

            try {
                cancelSystemTimer(context, BroadcastActions.ACTION_SYSTEM_TIMER)
                cancelSystemTimer(context, BroadcastActions.ACTION_GEOFENCE_CHECK)
                cancelSystemTimer(context, BroadcastActions.ACTION_BLE_SCAN_TIMER)

            } catch (e: Exception) {
                Logger.geoE("$TAG 停止系统定时器失败", e)
            }
        }

        /**
         * 设置系统定时器
         */
        private fun setSystemTimer(context: Context, action: String, intervalMinutes: Int) {
            try {
                // 使用显式Intent，指定接收器类，避免系统警告
                val intent = Intent(action).apply {
                    setClass(context, GeofenceReceiver::class.java)
                    setPackage(context.packageName)
                }

                val flags = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                    PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
                } else {
                    PendingIntent.FLAG_UPDATE_CURRENT
                }

                val pendingIntent = PendingIntent.getBroadcast(context, action.hashCode(), intent, flags)
                val alarmManager = context.getSystemService(Context.ALARM_SERVICE) as AlarmManager

                // 取消之前的定时任务
                alarmManager.cancel(pendingIntent)

                // 设置新的定时任务
                val triggerTime = System.currentTimeMillis() + (intervalMinutes * 60 * 1000L)

                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
                    alarmManager.setExact(AlarmManager.RTC_WAKEUP, triggerTime, pendingIntent)
                } else {
                    alarmManager.set(AlarmManager.RTC_WAKEUP, triggerTime, pendingIntent)
                }

                Logger.geo("$TAG 设置定时器成功: $action, 间隔: ${intervalMinutes}分钟")

            } catch (e: Exception) {
                Logger.geoE("$TAG 设置定时器失败: $action", e)
            }
        }

        /**
         * 取消系统定时器
         */
        private fun cancelSystemTimer(context: Context, action: String) {
            try {
                val intent = Intent(action)
                val flags = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                    PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
                } else {
                    PendingIntent.FLAG_UPDATE_CURRENT
                }

                val pendingIntent = PendingIntent.getBroadcast(context, action.hashCode(), intent, flags)
                val alarmManager = context.getSystemService(Context.ALARM_SERVICE) as AlarmManager

                alarmManager.cancel(pendingIntent)
                Logger.geo("$TAG 取消定时器: $action")

            } catch (e: Exception) {
                Logger.geoE("$TAG 取消定时器失败: $action", e)
            }
        }
    }
    
    override fun onReceive(context: Context, intent: Intent) {
        val action = intent.action ?: return
        
        Logger.geo("$TAG 收到广播: $action")
        
        try {
            when (action) {
                // 位置服务相关
                LocationManager.PROVIDERS_CHANGED_ACTION -> {
                    handleLocationProvidersChanged(context, intent)
                }
                
                LocationManager.MODE_CHANGED_ACTION -> {
                    handleLocationModeChanged(context, intent)
                }
                
                // 蓝牙相关
                BluetoothAdapter.ACTION_STATE_CHANGED -> {
                    handleBluetoothStateChanged(context, intent)
                }
                
                BluetoothAdapter.ACTION_DISCOVERY_STARTED -> {
                    handleBluetoothDiscoveryStarted(context, intent)
                }
                
                BluetoothAdapter.ACTION_DISCOVERY_FINISHED -> {
                    handleBluetoothDiscoveryFinished(context, intent)
                }
                
                // WiFi相关
                WifiManager.WIFI_STATE_CHANGED_ACTION -> {
                    handleWifiStateChanged(context, intent)
                }
                
                WifiManager.NETWORK_STATE_CHANGED_ACTION -> {
                    handleWifiNetworkStateChanged(context, intent)
                }
                
                // 网络连接相关
                ConnectivityManager.CONNECTIVITY_ACTION -> {
                    handleConnectivityChanged(context, intent)
                }
                
                // 电源相关
                Intent.ACTION_BATTERY_CHANGED -> {
                    handleBatteryChanged(context, intent)
                }
                
                Intent.ACTION_POWER_CONNECTED -> {
                    handlePowerConnected(context, intent)
                }
                
                Intent.ACTION_POWER_DISCONNECTED -> {
                    handlePowerDisconnected(context, intent)
                }
                
                // 屏幕相关
                Intent.ACTION_SCREEN_ON -> {
                    handleScreenOn(context, intent)
                }
                
                Intent.ACTION_SCREEN_OFF -> {
                    handleScreenOff(context, intent)
                }
                
                Intent.ACTION_USER_PRESENT -> {
                    handleUserPresent(context, intent)
                }
                
                // 系统相关
                Intent.ACTION_BOOT_COMPLETED -> {
                    handleBootCompleted(context, intent)
                }
                
                Intent.ACTION_SHUTDOWN -> {
                    handleShutdown(context, intent)
                }

                // 地理围栏相关广播（从CloudReceiver合并）
                BroadcastActions.ACTION_GEOFENCING_DETECTED_WARNING -> {
                    handleGeofencingDetectedWarning(context, intent)
                }

                BroadcastActions.ACTION_GEOFENCING_DETECTED_WARNING_CLOSE -> {
                    handleGeofencingDetectedWarningClose(context, intent)
                }

                BroadcastActions.ACTION_GEOFENCING_DETECTED_LOCK -> {
                    handleGeofencingDetectedLock(context, intent)
                }

                BroadcastActions.ACTION_GEOFENCING_RESET_LOCKSCREEN_TIMER -> {
                    handleGeofencingResetLockscreenTimer(context, intent)
                }

                BroadcastActions.ACTION_ENTER_GEOFENCE -> {
//                    handleEnterGeofence(context, intent)
                }

                BroadcastActions.ACTION_OUT_OF_GEOFENCE -> {
//                    handleOutOfGeofence(context, intent)
                }

                BroadcastActions.ACTION_CLOSE_LOCKSCREEN -> {
//                    handleCloseLockscreen(context, intent)
                }

                BroadcastActions.ACTION_EXPIRE_REBOOT -> {
                    handleExpireReboot(context, intent)
                }

                // 定时任务广播（从SystemReceiver合并）
                BroadcastActions.ACTION_SYSTEM_TIMER -> {
                    handleSystemTimer(context, intent)
                }

                BroadcastActions.ACTION_GEOFENCE_CHECK -> {
                    handleGeofenceCheck(context, intent)
                }

                BroadcastActions.ACTION_BLE_SCAN_TIMER -> {
                    handleBleScanTimer(context, intent)
                }

                BroadcastActions.GPS_SCHEDULE_TIME -> {
                    handleGpsScheduleTime(context, intent)
                }

                // 数据擦除相关广播
                BroadcastActions.UNBIND_LAUNCHER -> {
                    handleUnbindLauncher(context, intent)
                }

                // 自定义地理围栏广播（统一使用标准广播处理）
                "com.dspread.mdm.GEOFENCE_WARNING" -> {
                    // 重定向到标准的地理围栏警告处理，避免重复弹窗
                    handleGeofencingDetectedWarning(context, intent)
                }

                "com.dspread.mdm.GEOFENCE_STATUS_CHANGE" -> {
                    handleGeofenceStatusChange(context, intent)
                }

                "com.dspread.mdm.GEOFENCE_EMERGENCY" -> {
                    handleGeofenceEmergency(context, intent)
                }
                
                else -> {
                    Logger.geo("$TAG 未处理的广播: $action")
                }
            }
        } catch (e: Exception) {
            Logger.geoE("$TAG 处理广播失败: $action", e)
        }
    }
    
    /**
     * 处理位置提供者变化
     */
    private fun handleLocationProvidersChanged(context: Context, intent: Intent) {
        Logger.geo("$TAG 位置提供者发生变化")
        
        val locationManager = context.getSystemService(Context.LOCATION_SERVICE) as LocationManager
        
        val gpsEnabled = locationManager.isProviderEnabled(LocationManager.GPS_PROVIDER)
        val networkEnabled = locationManager.isProviderEnabled(LocationManager.NETWORK_PROVIDER)
        
        Logger.geo("$TAG GPS启用状态: $gpsEnabled, 网络定位启用状态: $networkEnabled")
        
        // 通知地理围栏管理器位置提供者状态变化
        geofenceScope.launch {
            try {
                // TODO: 通知GeofenceManager位置提供者变化
                // geofenceManager.onLocationProvidersChanged(gpsEnabled, networkEnabled)
            } catch (e: Exception) {
                Logger.geoE("$TAG 处理位置提供者变化失败", e)
            }
        }
    }
    
    /**
     * 处理位置模式变化
     */
    private fun handleLocationModeChanged(context: Context, intent: Intent) {
        Logger.geo("$TAG 位置模式发生变化")

        // 检查当前位置模式（兼容不同API版本）
        val locationManager = context.getSystemService(Context.LOCATION_SERVICE) as LocationManager
        val isLocationEnabled = isLocationEnabled(locationManager)

        Logger.geo("$TAG 位置服务启用状态: $isLocationEnabled")

        if (!isLocationEnabled) {
            Logger.geoW("$TAG 位置服务已被禁用，地理围栏功能可能受影响")
        }
    }

    /**
     * 检查位置服务是否启用（兼容不同API版本）
     */
    private fun isLocationEnabled(locationManager: LocationManager): Boolean {
        return if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.P) {
            // API 28及以上使用isLocationEnabled
            locationManager.isLocationEnabled
        } else {
            // API 28以下检查GPS和网络定位提供者
            val gpsEnabled = locationManager.isProviderEnabled(LocationManager.GPS_PROVIDER)
            val networkEnabled = locationManager.isProviderEnabled(LocationManager.NETWORK_PROVIDER)
            gpsEnabled || networkEnabled
        }
    }
    
    /**
     * 处理蓝牙状态变化
     */
    private fun handleBluetoothStateChanged(context: Context, intent: Intent) {
        val state = intent.getIntExtra(BluetoothAdapter.EXTRA_STATE, BluetoothAdapter.ERROR)
        val previousState = intent.getIntExtra(BluetoothAdapter.EXTRA_PREVIOUS_STATE, BluetoothAdapter.ERROR)
        
        val stateString = when (state) {
            BluetoothAdapter.STATE_OFF -> "关闭"
            BluetoothAdapter.STATE_TURNING_ON -> "开启中"
            BluetoothAdapter.STATE_ON -> "开启"
            BluetoothAdapter.STATE_TURNING_OFF -> "关闭中"
            else -> "未知($state)"
        }
        
        Logger.geo("$TAG 蓝牙状态变化: $stateString")
        
        when (state) {
            BluetoothAdapter.STATE_ON -> {
                Logger.geo("$TAG 蓝牙已开启，可以开始信标扫描")
                // 通知地理围栏管理器蓝牙已可用
                notifyBluetoothAvailable(context, true)
            }
            
            BluetoothAdapter.STATE_OFF -> {
                Logger.geo("$TAG 蓝牙已关闭，停止信标扫描")
                // 通知地理围栏管理器蓝牙不可用
                notifyBluetoothAvailable(context, false)
            }
        }
    }
    
    /**
     * 处理蓝牙发现开始
     */
    private fun handleBluetoothDiscoveryStarted(context: Context, intent: Intent) {
        Logger.geo("$TAG 蓝牙设备发现开始")
    }
    
    /**
     * 处理蓝牙发现结束
     */
    private fun handleBluetoothDiscoveryFinished(context: Context, intent: Intent) {
        Logger.geo("$TAG 蓝牙设备发现结束")
    }
    
    /**
     * 处理WiFi状态变化
     */
    private fun handleWifiStateChanged(context: Context, intent: Intent) {
        val wifiState = intent.getIntExtra(WifiManager.EXTRA_WIFI_STATE, WifiManager.WIFI_STATE_UNKNOWN)
        
        val stateString = when (wifiState) {
            WifiManager.WIFI_STATE_DISABLED -> "禁用"
            WifiManager.WIFI_STATE_DISABLING -> "禁用中"
            WifiManager.WIFI_STATE_ENABLED -> "启用"
            WifiManager.WIFI_STATE_ENABLING -> "启用中"
            else -> "未知($wifiState)"
        }
        
        Logger.geo("$TAG WiFi状态变化: $stateString")
        
        when (wifiState) {
            WifiManager.WIFI_STATE_ENABLED -> {
                Logger.geo("$TAG WiFi已启用，可以进行WiFi辅助定位")
                notifyWifiAvailable(context, true)
            }
            
            WifiManager.WIFI_STATE_DISABLED -> {
                Logger.geo("$TAG WiFi已禁用，WiFi辅助定位不可用")
                notifyWifiAvailable(context, false)
            }
        }
    }
    
    /**
     * 处理WiFi网络状态变化
     * 合并了NetworkEventHandlerImpl中的地理围栏检查逻辑
     */
    private fun handleWifiNetworkStateChanged(context: Context, intent: Intent) {
        val networkInfo = intent.getParcelableExtra<NetworkInfo>(WifiManager.EXTRA_NETWORK_INFO)

        networkInfo?.let { info ->
            Logger.geo("$TAG WiFi网络状态: ${info.state}")

            if (info.isConnected) {
                val ssid = WiFiManagerApi.getCurConnectSSID(context)

                Logger.geo("$TAG 已连接到WiFi: $ssid")

                // 通知地理围栏管理器WiFi连接变化
                notifyWifiConnectionChanged(context, ssid ?: "", true)

                // 地理围栏检查逻辑（从NetworkEventHandlerImpl合并）
                checkGeofenceOnWifiChange(context, ssid ?: "")
            } else {
                Logger.geo("$TAG WiFi连接已断开")
                notifyWifiConnectionChanged(context, "", false)
            }
        }
    }

    /**
     * WiFi变化时检查地理围栏条件
     * 从NetworkEventHandlerImpl合并的逻辑
     */
    private fun checkGeofenceOnWifiChange(context: Context, ssid: String) {
        try {
            Logger.geo("WiFi变化时检查地理围栏条件: SSID=$ssid")

            when (Constants.geofenceStatus) {
                GpsLocationManager.OUT_OF_ZONE -> {
                    // 如果当前在围栏外，检查WiFi是否满足进入围栏条件
                    if (GpsLocationManager.conditionEnterGeoFence(context)) {
                        Logger.geo("WiFi条件满足，判断为回到商店，移除OUT_OF_FENCE状态")
                        GpsLocationManager.closeOutOfGeofenceWarning(context)
                        GpsLocationManager.setCurrentGPSStatus(context, GpsLocationManager.IN_ZONE)

                        // 上报地理围栏数据
                        WsMessageSender.uploadDataInfoForGeo(System.currentTimeMillis())
                    }
                }

                GpsLocationManager.LOCK_SCREEN, GpsLocationManager.WIPE_DATA -> {
                    // 如果当前处于锁屏或擦除状态，检查WiFi是否满足进入围栏条件
                    if (GpsLocationManager.conditionEnterGeoFence(context)) {
                        Logger.geo("WiFi条件满足，发送进入围栏广播")
                        BroadcastSender.sendBroadcast(context, BroadcastActions.ACTION_ENTER_GEOFENCE)
                    }
                }
            }
        } catch (e: Exception) {
            Logger.geoE("$TAG WiFi变化时地理围栏检查失败", e)
        }
    }
    
    /**
     * 处理网络连接变化
     */
    private fun handleConnectivityChanged(context: Context, intent: Intent) {
        val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
        val activeNetwork = connectivityManager.activeNetworkInfo
        
        if (activeNetwork != null && activeNetwork.isConnected) {
            Logger.geo("$TAG 网络已连接: ${activeNetwork.typeName}")
        } else {
            Logger.geo("$TAG 网络已断开")
        }
    }
    
    /**
     * 处理电池状态变化
     */
    private fun handleBatteryChanged(context: Context, intent: Intent) {
        val level = intent.getIntExtra(BatteryManager.EXTRA_LEVEL, -1)
        val scale = intent.getIntExtra(BatteryManager.EXTRA_SCALE, -1)
        val batteryPct = level * 100 / scale.toFloat()
        
        // 只在电量较低时记录日志
        if (batteryPct <= 20) {
            Logger.geoW("$TAG 电池电量较低: ${batteryPct.toInt()}%")
            
            // 电量低时可能需要调整地理围栏监控频率
            if (batteryPct <= 10) {
                notifyLowBattery(context, batteryPct.toInt())
            }
        }
    }
    
    /**
     * 处理电源连接
     */
    private fun handlePowerConnected(context: Context, intent: Intent) {
        Logger.geo("$TAG 电源已连接")
        notifyPowerStateChanged(context, true)
    }
    
    /**
     * 处理电源断开
     */
    private fun handlePowerDisconnected(context: Context, intent: Intent) {
        Logger.geo("$TAG 电源已断开")
        notifyPowerStateChanged(context, false)
    }
    
    /**
     * 处理屏幕开启
     */
    private fun handleScreenOn(context: Context, intent: Intent) {
        Logger.geo("$TAG 屏幕已开启")
        notifyScreenStateChanged(context, true)
    }
    
    /**
     * 处理屏幕关闭
     */
    private fun handleScreenOff(context: Context, intent: Intent) {
        Logger.geo("$TAG 屏幕已关闭")
        notifyScreenStateChanged(context, false)
    }
    
    /**
     * 处理用户解锁
     */
    private fun handleUserPresent(context: Context, intent: Intent) {
        Logger.geo("$TAG 用户已解锁设备")
        notifyUserPresent(context)
    }
    
    /**
     * 处理系统启动完成
     * 合并了CloudReceiver和SystemReceiver中的逻辑
     */
    private fun handleBootCompleted(context: Context, intent: Intent) {
        Logger.geo("$TAG 系统启动完成")

        geofenceScope.launch {
            try {
                // ，立即检查状态，不延迟
                Logger.geo("$TAG 系统启动完成，立即检查地理围栏状态")

                // 检查状态是否已经恢复（避免重复恢复）
                if (Constants.geofenceStatus == 1) { // 如果还是默认值，说明可能没有恢复
                    Logger.geo("$TAG 状态可能未恢复，执行状态恢复")
                    GeofenceStateManager.restoreGeofenceConfig(context)
                } else {
                    Logger.geo("$TAG 状态已恢复，跳过重复恢复")
                }
                Logger.geo("$TAG 重启后状态检查: geofenceStatus=${Constants.geofenceStatus}")

                // 检查是否需要在重启后恢复地理围栏状态
                when (Constants.geofenceStatus) {
                    GpsLocationManager.OUT_OF_ZONE -> {
                        // 设备在围栏外，检查倒计时状态
                        val leftTime = getGeofenceWarnCountdown(context, GpsLocationManager.geoLockMins * 60 * 1000L)
                        if (leftTime > 0) {
                            Logger.geo("$TAG 重启后恢复地理围栏警告倒计时: ${leftTime / 1000}秒")
                            setTimerToLock(context, leftTime)
                            showGeofenceWarningDialog(context)
                        } else {
                            Logger.geo("$TAG 倒计时已到期，执行锁屏操作")
                            clearGeofenceWarnCountdown(context)
                            // 倒计时已到期，执行锁屏
                            gotoLockDeviceScreen(context)
                        }
                    }

                    GpsLocationManager.LOCK_SCREEN, GpsLocationManager.WIPE_DATA -> {
                        // 设备已锁屏或擦除，直接显示锁屏界面
                        Logger.geo("$TAG 重启后恢复锁屏状态: ${Constants.geofenceStatus}")
                        gotoLockDeviceScreen(context)
                    }

                    else -> {
                        Logger.geo("$TAG 重启后地理围栏状态正常: ${Constants.geofenceStatus}")
                    }
                }

                // 启动系统定时器（从SystemReceiver合并）
                startSystemTimer(context)

                // 执行一次地理围栏检查
                executeOnDueGeoProfile(context)

                // 启动定时检查任务
                startDataCollectTimer(context)

            } catch (e: Exception) {
                Logger.geoE("$TAG 系统启动后处理失败", e)
            }
        }
    }

    /**
     * 启动数据收集定时器
     */
    private fun startDataCollectTimer(context: Context) {
        Logger.geo("$TAG 启动数据收集定时器")
        // TODO: 实现数据收集定时器逻辑
    }
    
    /**
     * 处理系统关机
     */
    private fun handleShutdown(context: Context, intent: Intent) {
        Logger.geo("$TAG 系统即将关机")
        
        // 保存地理围栏状态
        geofenceScope.launch {
            try {
                // TODO: 保存地理围栏状态
                // geofenceManager.onShutdown()
            } catch (e: Exception) {
                Logger.geoE("$TAG 系统关机前保存状态失败", e)
            }
        }
    }

    /**
     * 处理地理围栏状态变化
     */
    private fun handleGeofenceStatusChange(context: Context, intent: Intent) {
        val status = intent.getIntExtra("status", -1)
        val previousStatus = intent.getIntExtra("previousStatus", -1)

        Logger.geo("$TAG 地理围栏状态变化: $previousStatus -> $status")
    }

    /**
     * 处理地理围栏紧急情况
     */
    private fun handleGeofenceEmergency(context: Context, intent: Intent) {
        val emergencyType = intent.getStringExtra("type") ?: ""

        Logger.geo("$TAG 地理围栏紧急情况: $emergencyType")

        // 处理紧急情况
        when (emergencyType) {
            "disable" -> {
                // 禁用地理围栏
            }
            "reset" -> {
                // 重置地理围栏状态
            }
        }
    }


    // ==================== 从CloudReceiver合并的方法 ====================

    /**
     * 处理地理围栏警告检测
     */
    private fun handleGeofencingDetectedWarning(context: Context, intent: Intent) {
        Logger.geo("$TAG 处理地理围栏警告检测")

        if (intent.hasExtra("warning_status")) {
            val status = intent.getIntExtra("warning_status", 0)

            when (status) {
                GpsLocationManager.WARNING_STATUS_OUT_OF_FENCE_AT_REBOOT -> {
                    // 重启后离开围栏
                    val leftTime = getGeofenceWarnCountdown(context, GpsLocationManager.geoLockMins * 60 * 1000L)
                    Logger.geo("$TAG 设备锁定倒计时: ${leftTime / 1000}秒")
                    setTimerToLock(context, leftTime)
                    showGeofenceWarningDialog(context)
                }

                GpsLocationManager.WARNING_STATUS_IN_FENCE -> {
                    // 从围栏内变为围栏外
                    setTimerToLock(context, GpsLocationManager.geoLockMins * 60 * 1000L)
                    showGeofenceWarningDialog(context)
                }

                GpsLocationManager.WARNING_STATUS_OUT_OF_FENCE -> {
                    // 持续在围栏外
                    showGeofenceWarningDialog(context)
                }
            }
        }
    }

    /**
     * 处理关闭地理围栏警告
     */
    private fun handleGeofencingDetectedWarningClose(context: Context, intent: Intent) {
        Logger.geo("$TAG 关闭地理围栏警告")
        closeGeofenceWarningDialog(context)
        unsetTimerToLock()
    }

    /**
     * 处理地理围栏锁定检测
     */
    private fun handleGeofencingDetectedLock(context: Context, intent: Intent) {
        Logger.geo("$TAG 处理地理围栏锁定检测")
        closeGeofenceWarningDialog(context)
        closeLockCountdownFloatWindow()
        GpsLocationManager.gotoLockDeviceScreen(context)
    }

    /**
     * 处理重置锁屏定时器
     */
    private fun handleGeofencingResetLockscreenTimer(context: Context, intent: Intent) {
        Logger.geo("$TAG 重置锁屏定时器")
        unsetTimerToLock()
        setTimerToLock(context, GpsLocationManager.geoLockMins * 60 * 1000L)
    }

    /**
     * 处理进入地理围栏
     */
    private fun handleEnterGeofence(context: Context, intent: Intent) {
        Logger.geo("$TAG 设备进入地理围栏")

        // 取消倒计时
        unsetTimerToLock()

        // 关闭警告和锁屏
        closeGeofenceWarningDialog(context)
        closeLockCountdownFloatWindow()

        // 如果设备处于锁屏或数据擦除状态，可能需要OTP验证
        when (Constants.geofenceStatus) {
            GpsLocationManager.LOCK_SCREEN, GpsLocationManager.WIPE_DATA -> {
                Logger.geo("$TAG 设备已锁定/擦除，需要验证后解锁")
                // 这里可以启动OTP验证流程
            }
        }
    }

    /**
     * 处理离开地理围栏
     * 当设备离开地理围栏时触发锁屏流程
     */
    private fun handleOutOfGeofence(context: Context, intent: Intent) {
        Logger.geo("$TAG 设备离开地理围栏，开始锁屏流程")
        Logger.geo("$TAG 当前地理围栏参数:")
        Logger.geo("$TAG   geoWipeStatus=${GpsLocationManager.geoWipeStatus}")
        Logger.geo("$TAG   geoWipeMins=${GpsLocationManager.geoWipeMins}")
        Logger.geo("$TAG   geoLockMins=${GpsLocationManager.geoLockMins}")

        try {
            // 检查当前状态
            when (Constants.geofenceStatus) {
                GpsLocationManager.OUT_OF_ZONE -> {
                    Logger.geo("$TAG 设备已在OUT_OF_ZONE状态，启动双弹窗显示")

                    // 启动锁屏倒计时
                    val lockTimeMs = GpsLocationManager.geoLockMins * 60 * 1000L
                    setTimerToLock(context, lockTimeMs)

                    // 恢复双弹窗逻辑
                    // 1. 先启动LockScreenActivity（背景）
                    gotoLockDeviceScreen(context)

                    // 2. 延迟显示警告对话框（前景），确保LockScreenActivity已启动
                    handler.postDelayed({
                        showGeofenceWarningDialog(context)
                    }, 500) // 延迟500ms确保Activity启动完成

                    Logger.geo("$TAG 双弹窗逻辑已启动：LockScreenActivity(背景) + GeofenceWarningDialog(前景)")
                }

                GpsLocationManager.LOCK_SCREEN, GpsLocationManager.WIPE_DATA -> {
                    Logger.geo("$TAG 设备已处于锁定状态，确保锁屏界面显示")

                    // 直接启动锁屏界面
                    gotoLockDeviceScreen(context)
                }

                else -> {
                    Logger.geo("$TAG 设备状态: ${Constants.geofenceStatus}，等待状态更新")
                }
            }

        } catch (e: Exception) {
            Logger.geoE("$TAG 处理离开地理围栏失败", e)
        }
    }

    /**
     * 处理关闭锁屏
     */
    private fun handleCloseLockscreen(context: Context, intent: Intent) {
        Logger.geo("$TAG 关闭锁屏界面")

        // 检查当前地理围栏状态
        when (Constants.geofenceStatus) {
            GpsLocationManager.WIPE_DATA -> {
                Logger.geo("$TAG 设备处于数据擦除状态")
                // 可以在这里设置默认启动器等
            }
            else -> {
                Logger.geo("$TAG 锁屏界面关闭")
            }
        }
    }

    /**
     * 处理过期重启
     * 使用现代化的重启弹窗替代原有实现
     */
    private fun handleExpireReboot(context: Context, intent: Intent) {
        Logger.geo("$TAG 处理过期重启")

        // 检查设备是否空闲
        if (isDeviceIdle(context)) {
            Logger.geo("$TAG 设备空闲，执行重启")
            executeReboot(context)
        } else {
            Logger.geo("$TAG 设备忙碌，显示重启提示弹窗")

            // 显示现代化的重启警告弹窗
            showRebootWarningDialog(context)
        }
    }

    /**
     * 显示重启警告弹窗
     */
    private fun showRebootWarningDialog(context: Context) {
        try {
            val title = "系统维护提醒"
            val message = "系统需要重启以完成维护任务，请选择重启方式"

            val dialog = RebootWarningDialog.showRebootWarningDialog(
                context = context,
                title = title,
                message = message,
                onDelayClick = {
                    Logger.geo("$TAG 用户选择延迟重启，显示悬浮窗")
                    // 用户选择延迟，显示悬浮窗倒计时
                    showRebootFloatWindow(context, 300) // 5分钟倒计时
                },
                onRebootClick = {
                    Logger.geo("$TAG 用户选择立即重启")
                    // 用户选择立即重启
                    executeReboot(context)
                }
            )

            Logger.geo("$TAG 重启警告弹窗已显示")

        } catch (e: Exception) {
            Logger.geoE("$TAG 显示重启警告弹窗失败", e)
            // 如果弹窗显示失败，直接执行重启
            handler.postDelayed({
                executeReboot(context)
            }, 5 * 60 * 1000) // 5分钟后重启
        }
    }

    /**
     * 显示重启悬浮窗
     */
    private fun showRebootFloatWindow(context: Context, timeoutSeconds: Int) {
        try {
            val floatWindow = RebootWarningDialog.showRebootWarningDialog(
                context = context,
                title = "系统维护提醒",
                message = "系统需要重启以完成维护任务，请选择重启方式",
                onDelayClick = {},
                onRebootClick = { executeReboot(context) }
            ).createWindowManager()

            // 创建悬浮窗
            floatWindow.createWindowManager()
            floatWindow.createDesktopLayout()
            floatWindow.showDesk(timeoutSeconds)

            Logger.geo("$TAG 重启悬浮窗已显示，倒计时: ${timeoutSeconds}秒")

        } catch (e: Exception) {
            Logger.geoE("$TAG 显示重启悬浮窗失败", e)
            // 如果悬浮窗显示失败，直接执行重启
            handler.postDelayed({
                executeReboot(context)
            }, timeoutSeconds * 1000L)
        }
    }

    // ==================== 从SystemReceiver合并的方法 ====================

    /**
     * 处理系统定时器
     */
    private fun handleSystemTimer(context: Context, intent: Intent) {
        Logger.geo("$TAG 系统定时器触发")

        geofenceScope.launch {
            try {
                // 执行系统维护任务
                performSystemMaintenance(context)

                // 重新设置定时器
                setSystemTimer(context, BroadcastActions.ACTION_SYSTEM_TIMER, SYSTEM_TIMER_INTERVAL)

            } catch (e: Exception) {
                Logger.geoE("$TAG 系统定时器处理失败", e)
            }
        }
    }

    /**
     * 处理地理围栏检查
     */
    private fun handleGeofenceCheck(context: Context, intent: Intent) {
        Logger.geo("$TAG 地理围栏检查定时器触发")

        geofenceScope.launch {
            try {
                // 检查GeoFence到期执行情况
                executeOnDueGeoProfile(context)

                // 主动检查当前位置的地理围栏状态
                if (GpsLocationManager.geoStatus) {
                    Logger.geo("$TAG 执行主动地理围栏位置检查")
                    GpsLocationManager.performActiveGeofenceCheck(context)
                }

                // 重新设置定时器
                setSystemTimer(context, BroadcastActions.ACTION_GEOFENCE_CHECK, GEOFENCE_CHECK_INTERVAL)

            } catch (e: Exception) {
                Logger.geoE("$TAG 地理围栏检查失败", e)
            }
        }
    }

    /**
     * 处理蓝牙扫描定时器
     */
    private fun handleBleScanTimer(context: Context, intent: Intent) {
        Logger.geo("$TAG 蓝牙扫描定时器触发")

        geofenceScope.launch {
            try {
                // 启动蓝牙扫描
                if (Constants.geofenceStatus == GpsLocationManager.OUT_OF_ZONE) {
                    startBLEScanTimer(context)
                }

            } catch (e: Exception) {
                Logger.geoE("$TAG 蓝牙扫描定时器处理失败", e)
            }
        }
    }

    /**
     * 处理GPS定时调度
     */
    private fun handleGpsScheduleTime(context: Context, intent: Intent) {
        Logger.geo("$TAG GPS定时调度触发")

        geofenceScope.launch {
            try {
                // 重新启动GPS位置监听
                Logger.geo("$TAG 定时重新启动GPS位置监听")
                GpsLocationManager.registerLocationChangeListener(context)

            } catch (e: Exception) {
                Logger.geoE("$TAG GPS定时调度处理失败", e)
            }
        }
    }

    // 通知方法（这些方法将在实际集成时连接到GeofenceManager）
    private fun notifyBluetoothAvailable(context: Context, available: Boolean) {
        // TODO: 通知GeofenceManager蓝牙可用性变化
    }
    
    private fun notifyWifiAvailable(context: Context, available: Boolean) {
        // TODO: 通知GeofenceManager WiFi可用性变化
    }
    
    private fun notifyWifiConnectionChanged(context: Context, ssid: String, connected: Boolean) {
        // TODO: 通知GeofenceManager WiFi连接变化
    }
    
    private fun notifyLowBattery(context: Context, batteryLevel: Int) {
        // TODO: 通知GeofenceManager电量低，可能需要调整监控频率
    }
    
    private fun notifyPowerStateChanged(context: Context, connected: Boolean) {
        // TODO: 通知GeofenceManager电源状态变化
    }
    
    private fun notifyScreenStateChanged(context: Context, screenOn: Boolean) {
        // TODO: 通知GeofenceManager屏幕状态变化
    }
    
    private fun notifyUserPresent(context: Context) {
        // TODO: 通知GeofenceManager用户已解锁
    }

    // ==================== 辅助方法 ====================

    /**
     * 获取地理围栏警告倒计时（剩余毫秒数）
     */
    private fun getGeofenceWarnCountdown(context: Context, defaultTime: Long): Long {
        return try {
            val prefs = context.getSharedPreferences("geofence_prefs", Context.MODE_PRIVATE)
            // 获取保存的剩余毫秒数，如果没有保存则返回默认时间
            prefs.getLong("PREF_GEOFENCE_WARN_COUNTDOWN_MS", defaultTime)
        } catch (e: Exception) {
            Logger.geoE("$TAG 获取警告倒计时失败", e)
            defaultTime
        }
    }

    /**
     * 保存地理围栏警告倒计时（剩余毫秒数）
     */
    private fun saveGeofenceWarnCountdown(context: Context, countdownMs: Long) {
        try {
            val prefs = context.getSharedPreferences("geofence_prefs", Context.MODE_PRIVATE)
            // 保存剩余的毫秒数
            prefs.edit().putLong("PREF_GEOFENCE_WARN_COUNTDOWN_MS", countdownMs).apply()
        } catch (e: Exception) {
            Logger.geoE("$TAG 保存警告倒计时失败", e)
        }
    }

    /**
     * 清除地理围栏警告倒计时
     */
    private fun clearGeofenceWarnCountdown(context: Context) {
        try {
            val prefs = context.getSharedPreferences("geofence_prefs", Context.MODE_PRIVATE)
            prefs.edit().remove("PREF_GEOFENCE_WARN_COUNTDOWN_MS").apply()
            Logger.geo("$TAG 清除警告倒计时")
        } catch (e: Exception) {
            Logger.geoE("$TAG 清除警告倒计时失败", e)
        }
    }

    /**
     * 设置锁定定时器
     */
    fun setTimerToLock(context: Context, timeMs: Long) {
        Logger.geo("$TAG 设置锁定定时器: ${timeMs / 1000}秒")

        // 取消之前的定时器和悬浮窗
        countDownTimer?.cancel()
        closeLockCountdownFloatWindow()

        // 显示锁定倒计时悬浮窗
        showLockCountdownFloatWindow(context, (timeMs / 1000).toInt())

        // 创建新的倒计时器
        countDownTimer = object : CountDownTimer(timeMs, 1000) {
            override fun onTick(millisUntilFinished: Long) {
                // 保存剩余的毫秒数
                saveGeofenceWarnCountdown(context, millisUntilFinished)
                Logger.geo("$TAG 锁定倒计时: ${millisUntilFinished / 1000}秒")
            }

            override fun onFinish() {
                Logger.geo("$TAG 锁定定时器到期，执行锁定")

                // 倒计时到期时的完整处理流程
                // 1. 关闭警告弹窗和悬浮窗
                closeGeofenceWarningDialog(context)
                closeLockCountdownFloatWindow()

                // 2. 更新地理围栏状态为LOCK_SCREEN
//                GpsLocationManager.setCurrentGPSStatus(context, GpsLocationManager.LOCK_SCREEN)

                // 3. 清除保存的倒计时
                clearGeofenceWarnCountdown(context)

                // 4. 启动锁屏界面
                showLockScreen(context)

                Logger.geo("$TAG 倒计时到期处理完成：关闭警告弹窗 → 更新状态 → 启动锁屏")
            }
        }.start()
    }

    /**
     * 显示锁屏界面
     */
    private fun showLockScreen(context: Context) {
        Logger.geo("$TAG 显示锁屏界面")
        gotoLockDeviceScreen(context)
    }

    /**
     * 跳转到锁屏界面
     */
    private fun gotoLockDeviceScreen(context: Context) {
        Logger.geo("$TAG 跳转到锁屏界面")

        geofenceScope.launch {
            try {
                // 启动锁屏Activity
                val intent = Intent().apply {
                    setClassName(context.packageName, "com.dspread.mdm.service.ui.activity.LockScreenActivity")
                    addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                    addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP)
                }
                context.startActivity(intent)

            } catch (e: Exception) {
                Logger.geoE("$TAG 启动锁屏界面失败", e)
            }
        }
    }

    /**
     * 取消锁定定时器
     */
    private fun unsetTimerToLock() {
        Logger.geo("$TAG 取消锁定定时器")
        countDownTimer?.cancel()
        countDownTimer = null
        // 关闭悬浮窗
        closeLockCountdownFloatWindow()
        // 清除保存的倒计时
        clearGeofenceWarnCountdown(SmartMdmServiceApp.instance)
    }

    /**
     * 显示地理围栏警告对话框（使用专门的GeofenceWarningDialog类）
     */
    private fun showGeofenceWarningDialog(context: Context) {
        Logger.geo("$TAG 显示地理围栏警告对话框")

        handler.post {
            try {
                Logger.geo("$TAG warningDialog=$warningDialog${if (warningDialog == null) "" else " isShowing=${warningDialog?.isShowing()}"}")

                // 如果对话框已经显示，直接返回
                if (warningDialog?.isShowing() == true) {
                    return@post
                }

                // 关闭之前的对话框
                warningDialog?.dismiss()
                warningDialog = null

                // 使用新的GeofenceWarningDialog类
                warningDialog = GeofenceWarningDialog.show(context) {
                    // 对话框关闭回调
                    Logger.geo("$TAG 地理围栏警告对话框被用户关闭")
                    warningDialog = null
                }

                Logger.geo("$TAG 地理围栏警告对话框已显示")

            } catch (e: Exception) {
                Logger.geoE("$TAG 显示警告对话框失败", e)
            }
        }
    }



    /**
     * 关闭地理围栏警告对话框
     */
    private fun closeGeofenceWarningDialog(context: Context) {
        Logger.geo("$TAG 关闭地理围栏警告对话框")

        handler.post {
            try {
                if (warningDialog?.isShowing() == true) {
                    warningDialog?.dismiss()
                    warningDialog = null
                    Logger.geo("$TAG 地理围栏警告对话框已关闭")
                }
            } catch (e: Exception) {
                Logger.geoE("$TAG 关闭警告对话框失败", e)
            }
        }
    }

    /**
     * 检查设备是否空闲
     * 用于重启和关键操作前的安全检查
     */
    private fun isDeviceIdle(context: Context): Boolean {
        return try {
            // 使用现有的UserInteractionMonitor来检查设备空闲状态
            val userInteractionMonitor = UserInteractionMonitor(context)
            val isIdle = userInteractionMonitor.isDeviceIdle()

            Logger.geo("$TAG 设备空闲状态检查: $isIdle")
            isIdle

        } catch (e: Exception) {
            Logger.geoE("$TAG 检查设备空闲状态失败", e)
            // 异常时默认认为设备不空闲（保守策略）
            false
        }
    }

    /**
     * 执行重启
     */
    private fun executeReboot(context: Context) {
        Logger.geo("$TAG 执行系统重启")

        try {
            // 执行重启命令
            Runtime.getRuntime().exec("su -c reboot")
        } catch (e: Exception) {
            Logger.geoE("$TAG 执行重启失败", e)
        }
    }

    /**
     * 执行到期的地理围栏Profile
     */
    private fun executeOnDueGeoProfile(context: Context) {
        try {
            Logger.geo("$TAG 检查GeoFence到期执行情况 GEO_STATUS=${GpsLocationManager.geoStatus} M_GEOFENCE_STATUS=${Constants.geofenceStatus}")

            GeofenceHandler(context).executeOnDueGeoProfile(context)

        } catch (e: Exception) {
            Logger.geoE("$TAG 执行到期地理围栏Profile失败", e)
        }
    }

    /**
     * 执行系统维护任务
     */
    private fun performSystemMaintenance(context: Context) {
        try {
            Logger.geo("$TAG 执行系统维护任务")

            // 检查地理围栏状态
            if (GpsLocationManager.geoStatus) {
                Logger.geo("$TAG 地理围栏已启用，状态: ${Constants.geofenceStatus}")
            }

            // 可以在这里添加其他系统维护任务
            // 比如清理临时文件、检查系统状态等

        } catch (e: Exception) {
            Logger.geoE("$TAG 系统维护任务失败", e)
        }
    }

    /**
     * 启动蓝牙扫描定时器
     */
    private fun startBLEScanTimer(context: Context) {
        try {
            if (!GpsLocationManager.isBTBeaconScanNeed()) {
                return
            }

            Logger.geo("$TAG 启动蓝牙扫描定时器")

            // 启动蓝牙信标扫描
            GpsLocationManager.startBluetoothBeaconScan(context)

            // 设置下次扫描定时器 - 使用Constants中定义的扫描间隔
            val scanIntervalMs = Constants.ModuleConstants.BLUETOOTH_SCAN_INTERVAL // 30秒
            val scanIntervalMins = (scanIntervalMs / 60000).toInt().coerceAtLeast(1) // 至少1分钟

            setSystemTimer(context, BroadcastActions.ACTION_BLE_SCAN_TIMER, scanIntervalMins)

            Logger.geo("$TAG 蓝牙扫描定时器已设置，间隔: ${scanIntervalMins}分钟")

        } catch (e: Exception) {
            Logger.geoE("$TAG 启动蓝牙扫描定时器失败", e)
        }
    }

    /**
     * 处理UNBIND_LAUNCHER广播
     * 执行地理围栏触发的数据擦除操作
     */
    private fun handleUnbindLauncher(context: Context, intent: Intent) {
        try {
            Logger.geo("$TAG 收到UNBIND_LAUNCHER广播，开始执行数据擦除")
            Logger.geo("$TAG 当前状态: geofenceStatus=${Constants.geofenceStatus}")
            Logger.geo("$TAG 重置标志: bUnboxResetFromGeo=${Constants.bUnboxResetFromGeo}")

            // 检查是否是地理围栏触发的重置
            if (Constants.bUnboxResetFromGeo) {
                Logger.geo("$TAG 确认是地理围栏触发的数据擦除")

                // 执行数据擦除操作
                executeDataWipe(context)

            } else {
                Logger.geo("$TAG 非地理围栏触发的重置，忽略")
            }

        } catch (e: Exception) {
            Logger.geoE("$TAG 处理UNBIND_LAUNCHER广播失败", e)
        }
    }

    /**
     * 执行数据擦除操作
     */
    private fun executeDataWipe(context: Context) {
        try {
            Logger.geo("$TAG 🔥 开始执行数据擦除操作")

            // 使用协程在后台执行数据擦除
            geofenceScope.launch {
                try {
                    // 1. 创建SecurityActionHandler并执行数据擦除
                    val securityActionHandler = SecurityActionHandler(context)
                    val result = securityActionHandler.wipeData()

                    if (result.isSuccess) {
                        Logger.geo("$TAG 数据擦除操作执行成功")

                        // 2. 发送数据擦除完成广播
                        BroadcastSender.sendBroadcast(context, BroadcastActions.ACTION_DATA_WIPED)
                        Logger.geo("$TAG 已发送ACTION_DATA_WIPED广播")

                    } else {
                        Logger.geoE("$TAG 数据擦除操作执行失败")

                        // 如果数据擦除失败，尝试重启设备
                        Logger.geo("$TAG 数据擦除失败，尝试重启设备")
                        val rebootResult = securityActionHandler.reboot()

                        if (rebootResult.isSuccess) {
                            Logger.geo("$TAG 设备重启成功")
                        } else {
                            Logger.geoE("$TAG 设备重启也失败")
                        }
                    }

                } catch (e: Exception) {
                    Logger.geoE("$TAG 执行数据擦除操作异常", e)
                }
            }

        } catch (e: Exception) {
            Logger.geoE("$TAG 启动数据擦除操作失败", e)
        }
    }

    /**
     * 显示锁定倒计时悬浮窗
     */
    private fun showLockCountdownFloatWindow(context: Context, timeoutSeconds: Int) {
        try {
            Logger.geo("$TAG 显示锁定倒计时悬浮窗: ${timeoutSeconds}秒")

            // 关闭之前的悬浮窗
            closeLockCountdownFloatWindow()

            // 创建新的悬浮窗
            lockCountdownFloatWindow = RebootFloatWindow(context, RebootFloatWindow.WindowType.LOCK)
            lockCountdownFloatWindow?.apply {
                createWindowManager()
                createDesktopLayout()
                showDesk(timeoutSeconds)
            }

            Logger.geo("$TAG 锁定倒计时悬浮窗已显示")

        } catch (e: Exception) {
            Logger.geoE("$TAG 显示锁定倒计时悬浮窗失败", e)
        }
    }

    /**
     * 关闭锁定倒计时悬浮窗
     */
    private fun closeLockCountdownFloatWindow() {
        try {
            lockCountdownFloatWindow?.closeDesk()
            lockCountdownFloatWindow = null
            Logger.geo("$TAG 锁定倒计时悬浮窗已关闭")
        } catch (e: Exception) {
            Logger.geoE("$TAG 关闭锁定倒计时悬浮窗失败", e)
        }
    }
}
