package com.bbpos.wiseapp.activity;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.app.PendingIntent;
import android.bbpos.CustServiceManager;
import android.content.BroadcastReceiver;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageInfo;
import android.content.pm.ShortcutInfo;
import android.content.pm.ShortcutManager;
import android.graphics.drawable.Icon;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.IBinder;
import android.os.Message;
import android.os.RemoteException;
import android.provider.Settings;
import android.util.Log;
import android.view.View;
import android.widget.Button;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.bbpos.wiseapp.logger.BBLog;
import com.bbpos.wiseapp.sdk.app.IAppInstallObserver;
import com.bbpos.wiseapp.service.R;
import com.bbpos.wiseapp.service.WakeLockService;
import com.bbpos.wiseapp.service.common.RequestPermission;
import com.bbpos.wiseapp.system.api.Helper;
import com.bbpos.wiseapp.system.api.SystemApi;
import com.bbpos.wiseapp.tms.adapter.SystemManagerAdapter;
import com.bbpos.wiseapp.tms.utils.ContextUtil;
import com.bbpos.wiseapp.tms.utils.TaskState;
import com.bbpos.wiseapp.utils.ActivityUtils;

import java.io.File;
import java.io.FileOutputStream;
import java.io.InputStream;

import static android.os.SystemClock.sleep;

@SuppressLint("SimpleDateFormat")
public class CustomConfigActivity extends Activity {
    public static final String ACTION_ADD_SHORTCUT = "com.android.launcher.action.INSTALL_SHORTCUT";

    private final String StripeLauncherConfig = "StripeLauncherConfig.txt";
    private final String StripeApk = "com.stripe.updater-*******.apk";
    private CustServiceManager mCs = null;
    private static final int MSG_COMPLETED = 0;
    private static final int MSG_Stripe = 1;

    Handler mHandler = new Handler() {
        @Override
        public void handleMessage(Message msg) {
        switch (msg.what) {
            case MSG_COMPLETED:
                Toast.makeText(CustomConfigActivity.this,"Set successfull,Please factory reset",Toast.LENGTH_SHORT ).show();
                break;
            case MSG_Stripe:
                ((TextView)findViewById(R.id.textView)).setText("Set Stripe Config completed");
                break;
        }
        }
    };

	@Override
	public void onDestroy() {
		super.onDestroy();
	}

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_custom_config);

        mCs = (CustServiceManager) getSystemService("custservice");

        findViewById(R.id.exit).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                deleteLauncherShortcut(CustomConfigActivity.this);
                finish();
            }
        });

        findViewById(R.id.stripe).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                ((TextView)findViewById(R.id.textView)).setText("Please wait");
                ((Button)findViewById(R.id.stripe)).setEnabled(false);
                ((Button)findViewById(R.id.Factory_Reset)).setEnabled(false);

                try {
                    copyLauncherConfig(StripeLauncherConfig);

                    sleep(100);
                    testHiddenAppListInterface();
                    testEnablePermissions();
                    stripeLogoAndBootanimation();
                    installStripeApk();
                } catch (Exception e) {
                    e.printStackTrace();
                }

            }
        });
    }

    private void stripeLogoAndBootanimation() {

        String logo = "splash_stripe.img";
        String animation = "bootanimation_stripe.zip";

        SystemApi.replaceBootAnimation(animation);
        SystemApi.replaceLogo(logo);
    }

    private void installStripeApk() {
//        Settings.System.putString(getContentResolver(), "InstallerPackageName", "ALL");
//        saveNewPreinstallAppConfig("com.stripe.updater",false);
//
//        try {
//            InputStream input = getClass().getClassLoader().getResourceAsStream("assets/" + StripeApk);
//            if (input == null) {
//                ((Button)findViewById(R.id.stripe)).setEnabled(true);
//                ((Button)findViewById(R.id.Factory_Reset)).setEnabled(true);
//                Toast.makeText(CustomConfigActivity.this, "Stripe app doesn't exist!", Toast.LENGTH_LONG).show();
//                return;
//            }
//
//            FileOutputStream output = new FileOutputStream("/sdcard/" + StripeApk);
//            byte[] buffer = new byte[1024];
//            int count = 0;
//            while ((count = input.read(buffer)) > 0) {
//                output.write(buffer, 0, count);
//            }
//            output.flush();
//            output.close();
//            input.close();
//
//
//            Helper.installPackage(CustomConfigActivity.this, "/sdcard/" + StripeApk, new IAppInstallObserver() {
//                @Override
//                public void onInstallFinished(String s, int i, String s1) throws RemoteException {
//                    if(i == 1) {
//                        setDeviceOwer();
//
//                        mHandler.sendEmptyMessage(MSG_Stripe);
//                        mHandler.sendEmptyMessageDelayed(MSG_COMPLETED, 200);
//                        ((Button)findViewById(R.id.stripe)).setEnabled(true);
//                        ((Button)findViewById(R.id.Factory_Reset)).setEnabled(true);
//                    }
//                }
//
//                @Override
//                public IBinder asBinder() {
//                    return null;
//                }
//            }, "");
//        } catch (Exception e) {
//            e.printStackTrace();
//        }

        PackageInfo info = ActivityUtils.getPackageInfo(ContextUtil.getInstance().getApplicationContext(), "com.stripe.updater");
        if (info != null) {
            BBLog.e(BBLog.TAG, "com.stripe.updater 已安装");
            setDeviceOwer();
        }
    }

    private void copyLauncherConfig(String name) {
        try {
            InputStream input = getClass().getClassLoader().getResourceAsStream("assets/"+name);

            FileOutputStream output = new FileOutputStream("/bbpos/LauncherConfig.txt");
            byte[] buffer = new byte[1024];
            int count = 0;
            while ((count = input.read(buffer)) > 0) {
                output.write(buffer, 0, count);
            }
            output.flush();
            output.close();
            input.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void testEnablePermissions() {
        mCs.setProperty("persist.bbpos.enable_apk_permission","true");
        mCs.setProperty("persist.bbpos.enable_apk_verify","true");
        mCs.setProperty("persist.bbpos.close_password","true");
        mCs.setProperty("persist.bbpos.product_id","WSC5X");
        mCs.setProperty("persist.bbpos.close_dev_password","true");
        mCs.setProperty("persist.bbpos.auto_reboot_enable","true");
        mCs.setProperty("persist.bbpos.press_recovery_enable","true");
        mCs.setProperty("persist.sys.statusbar.disable","true");
        mCs.setProperty("persist.security.bbpos_debug","0");
        mCs.setProperty("persist.bbpos.disable_volume_key","true");
        mCs.setProperty("persist.bbpos.enable_adb_key","true");
    }

    private void testHiddenAppListInterface() {
        String str = mCs.getHiddenAppList();

        String newList = "PrebuiltGmsCorePi\n"
                + "GoogleServicesFramework\n"
                + "Messaging\n"
                + "Contacts\n"
                + "Mms\n"
                + "xtra_t_app\n"
                + "Service_WiseManage\n"
                + "Launcher_WiseManage\n"
                + "QtiDialer\n";

        mCs.saveHiddenAppsList(newList);
    }

    private void setDeviceOwer() {
        if (!mCs.isDeviceOwnerApp("com.stripe.updater"))
        {
            mCs.setDeviceOwner(new ComponentName("com.stripe.updater", "com.stripe.updater.deviceowner.UpdaterDeviceAdminReceiver"));
        }
    }

    public void saveNewPreinstallAppConfig(String packageName,boolean isUpdate){
        String getNewCofig = removeOldConfigIfPackageNameExistedInConfigList(packageName);
        String addNewPreinstallAppConfig = getNewCofig  +"|"+ "packageName="+packageName + ",update="+isUpdate; //e.g "packageName=com.bbpos.test1,update=false|packageName=com.bbpos.test2,update=false";
        mCs.savePreinstallAppsConfig(addNewPreinstallAppConfig);
        String getConfig = mCs.getPreinstallAppsConfig();
        Log.i("vincent",""+ getConfig);
    }

    private String removeOldConfigIfPackageNameExistedInConfigList(String packageName){
        String getConfig = mCs.getPreinstallAppsConfig(); //e.g "packageName=com.bbpos.test1,update=false|packageName=com.bbpos.test2,update=false";
        if(getConfig == null) return null;

        if (getConfig.contains(packageName)) {
            String [] preinstallApps = getConfig.split("\\|");  //e.g "packageName=com.bbpos.test1,update=false";
            for (int x = 0; x < preinstallApps.length; x++) {
                String[] preinstallApp = preinstallApps[x].split(",");//e.g "packageName=com.bbpos.test1" or "update=false";
                for (int y = 0; y < preinstallApp.length; y++) {
                    String[] preinstallAppMessage = preinstallApp[y].split("="); //e.g "packageName","com.bbpos.test1" or "update","false";
                    for (int k = 0; k < preinstallAppMessage.length; k++) {
                        if (preinstallAppMessage[k].equals("packageName")) {
                            String configPackageName = preinstallAppMessage[1];
                            if (configPackageName.equals(packageName)) {
                                getConfig = getConfig.replace(preinstallApps[x],"");
                                return  getConfig;
                            }
                        }
                    }
                }
            }
        }
        return getConfig;
    }

    public static void createLauncherShortcut(Context context) {
        if (Build.VERSION.SDK_INT <= 25) {
            Intent addShortcutIntent = new Intent(ACTION_ADD_SHORTCUT);

            // 不允许重复创建，不是根据快捷方式的名字判断重复的
            addShortcutIntent.putExtra("duplicate", false);
            addShortcutIntent.putExtra(Intent.EXTRA_SHORTCUT_NAME, "Shortcut Name");

            //图标
            addShortcutIntent.putExtra(Intent.EXTRA_SHORTCUT_ICON_RESOURCE, Intent.ShortcutIconResource.fromContext(context, R.mipmap.ic_launcher));

            // 设置关联程序
            Intent launcherIntent = new Intent();
            launcherIntent.setClass(context, CustomConfigActivity.class);
            addShortcutIntent.putExtra(Intent.EXTRA_SHORTCUT_INTENT, launcherIntent);

            // 发送广播
            context.sendBroadcast(addShortcutIntent, RequestPermission.REQUEST_PERMISSION_INSTALL_SHORTCUT);
        } else {
            String shortcutId = "The only id";
            boolean isExit = false;
            ShortcutManager shortcutManager = (ShortcutManager) context.getSystemService(Context.SHORTCUT_SERVICE);
//            for (ShortcutInfo info : shortcutManager.getPinnedShortcuts()) {
//                if (shortcutId.equals(info.getId())) {
//                    //判断快捷方式是否已存在
//                    isExit = true;
//                }
//            }
            if (!isExit && shortcutManager.isRequestPinShortcutSupported()) {
//                Uri content_url = Uri.parse("http://www.baidu.com/");
                Intent shortcutInfoIntent = new Intent(context, CustomConfigActivity.class);
                shortcutInfoIntent.setAction(Intent.ACTION_VIEW);
                ShortcutInfo info = new ShortcutInfo.Builder(context, shortcutId)
                        .setIcon(Icon.createWithResource(context, R.mipmap.ic_launcher))
                        .setShortLabel("Stripe config")
                        .setIntent(shortcutInfoIntent)
                        .build();
                PendingIntent shortcutCallbackIntent = PendingIntent.getBroadcast(context, 0, new Intent(context, MyReceiver.class), PendingIntent.FLAG_UPDATE_CURRENT);
                shortcutManager.requestPinShortcut(info, shortcutCallbackIntent.getIntentSender());
            }
        }
    }

    public void deleteLauncherShortcut(Context context) {
        String action = "android.intent.action.MAIN";
        String category = "android.intent.category.LAUNCHER";
        Intent shortcut = new Intent("com.android.launcher.action.UNINSTALL_SHORTCUT");
        // 快捷方式的名称
        shortcut.putExtra(Intent.EXTRA_SHORTCUT_NAME, "Stripe config");
        ComponentName componentName = new ComponentName(context.getPackageName(), CustomConfigActivity.class.getName());
        Intent respondIntent = new Intent();
        respondIntent.setComponent(componentName);
        respondIntent.setAction(action);
        respondIntent.addCategory(category);
        shortcut.putExtra(Intent.EXTRA_SHORTCUT_INTENT, respondIntent);
        sendBroadcast(shortcut, RequestPermission.REQUEST_PERMISSION_UNINSTALL_SHORTCUT);

    }

    //创建桌面快捷方式
    private void createShortCut() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            String shortcutId = "The only id";
            boolean isExit = false;
            ShortcutManager shortcutManager = (ShortcutManager) getSystemService(Context.SHORTCUT_SERVICE);
            for (ShortcutInfo info : shortcutManager.getPinnedShortcuts()) {
                if (shortcutId.equals(info.getId())) {
                    //判断快捷方式是否已存在
                    isExit = true;
                }
            }
            if (!isExit && shortcutManager.isRequestPinShortcutSupported()) {
//                Uri content_url = Uri.parse("http://www.baidu.com/");
                Intent shortcutInfoIntent = new Intent(Intent.ACTION_VIEW);
                shortcutInfoIntent.setAction(Intent.ACTION_VIEW);
                ShortcutInfo info = new ShortcutInfo.Builder(this, shortcutId)
                        .setIcon(Icon.createWithResource(this, R.mipmap.ic_launcher))
                        .setShortLabel("Stripe config")
                        .setIntent(shortcutInfoIntent)
                        .build();
                shortcutManager.requestPinShortcut(info, null);
            }
        } else {
            Intent intentAddShortcut = new Intent(ACTION_ADD_SHORTCUT);
            intentAddShortcut.putExtra(Intent.EXTRA_SHORTCUT_NAME, "快捷方式名字");
            intentAddShortcut.putExtra(Intent.EXTRA_SHORTCUT_ICON_RESOURCE,
                    Intent.ShortcutIconResource.fromContext(this,
                            R.mipmap.ic_launcher));//设置Launcher的Uri数据
            intentAddShortcut.putExtra("duplicate", false);
            Intent intentLauncher = new Intent();
            Uri content_url = Uri.parse("http://www.baidu.com/");
            intentLauncher.setData(content_url);
            intentAddShortcut.putExtra(Intent.EXTRA_SHORTCUT_INTENT, intentLauncher);
            sendBroadcast(intentAddShortcut, RequestPermission.REQUEST_PERMISSION_INSTALL_SHORTCUT);
        }
    }


    public class MyReceiver extends BroadcastReceiver {
        @Override
        public void onReceive(Context context, Intent intent) {

        }
    }
}
