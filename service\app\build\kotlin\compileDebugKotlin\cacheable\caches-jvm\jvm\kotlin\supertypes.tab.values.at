/ Header Record For PersistentHashMapValueStorage android.app.Application= <com.dspread.mdm.service.broadcast.core.BroadcastEventHandler= <com.dspread.mdm.service.broadcast.core.BroadcastEventHandler= <com.dspread.mdm.service.broadcast.core.BroadcastEventHandler= <com.dspread.mdm.service.broadcast.core.BroadcastEventHandler= <com.dspread.mdm.service.broadcast.core.BroadcastEventHandler" !android.content.BroadcastReceiver= <com.dspread.mdm.service.broadcast.core.BroadcastEventHandler= <com.dspread.mdm.service.broadcast.core.BroadcastEventHandler; :com.dspread.mdm.service.broadcast.core.BatteryEventHandler; :com.dspread.mdm.service.broadcast.core.NetworkEventHandler= <com.dspread.mdm.service.broadcast.core.BroadcastEventHandler= <com.dspread.mdm.service.broadcast.core.BroadcastEventHandler= <com.dspread.mdm.service.broadcast.core.BroadcastEventHandler: 9com.dspread.mdm.service.broadcast.core.SystemEventHandler= <com.dspread.mdm.service.broadcast.core.BroadcastEventHandler= <com.dspread.mdm.service.broadcast.core.HeartbeatEventHandler= <com.dspread.mdm.service.broadcast.core.BroadcastEventHandler= <com.dspread.mdm.service.broadcast.core.BroadcastEventHandler" !android.content.BroadcastReceiver" !android.content.BroadcastReceiver" !android.content.BroadcastReceiver" !android.content.BroadcastReceiver. -com.dspread.mdm.service.modules.ModuleHandler kotlin.Enum. -com.dspread.mdm.service.modules.ModuleManager2 1com.dspread.mdm.service.modules.BaseModuleHandler2 1com.dspread.mdm.service.modules.BaseModuleManager kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum2 1com.dspread.mdm.service.modules.BaseModuleHandler2 1com.dspread.mdm.service.modules.BaseModuleManager kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum2 1com.dspread.mdm.service.modules.BaseModuleHandler kotlin.Enum2 1com.dspread.mdm.service.modules.BaseModuleManager kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum2 1com.dspread.mdm.service.modules.BaseModuleHandler2 1com.dspread.mdm.service.modules.BaseModuleManager android.app.Activity- ,com.dspread.mdm.service.modules.ModuleConfig kotlin.Enum kotlin.EnumC Bcom.dspread.mdm.service.modules.remoteview.model.RemoteViewCommandC Bcom.dspread.mdm.service.modules.remoteview.model.RemoteViewCommandC Bcom.dspread.mdm.service.modules.remoteview.model.RemoteViewCommandC Bcom.dspread.mdm.service.modules.remoteview.model.RemoteViewCommandC Bcom.dspread.mdm.service.modules.remoteview.model.RemoteViewCommandC Bcom.dspread.mdm.service.modules.remoteview.model.RemoteViewCommandA @com.dspread.mdm.service.modules.remoteview.model.RemoteViewEventA @com.dspread.mdm.service.modules.remoteview.model.RemoteViewEventA @com.dspread.mdm.service.modules.remoteview.model.RemoteViewEventA @com.dspread.mdm.service.modules.remoteview.model.RemoteViewEventA @com.dspread.mdm.service.modules.remoteview.model.RemoteViewEventA @com.dspread.mdm.service.modules.remoteview.model.RemoteViewEventA @com.dspread.mdm.service.modules.remoteview.model.RemoteViewEventA @com.dspread.mdm.service.modules.remoteview.model.RemoteViewEventA @com.dspread.mdm.service.modules.remoteview.model.RemoteViewEvent; :com.dspread.mdm.service.modules.rulebase.RuleProcessResult; :com.dspread.mdm.service.modules.rulebase.RuleProcessResult; :com.dspread.mdm.service.modules.rulebase.RuleProcessResult@ ?com.dspread.mdm.service.modules.rulebase.model.ValidationResult@ ?com.dspread.mdm.service.modules.rulebase.model.ValidationResult kotlin.EnumW Vcom.dspread.mdm.service.modules.rulebase.core.RuleStateMachine.RuleStateChangeListener kotlin.Enum java.lang.Exception kotlin.Enum2 1com.dspread.mdm.service.modules.BaseModuleHandler kotlin.Enum kotlin.Enum2 1com.dspread.mdm.service.modules.BaseModuleManager kotlin.EnumM Lcom.dspread.mdm.service.network.websocket.message.handler.BaseMessageHandlerM Lcom.dspread.mdm.service.network.websocket.message.handler.BaseMessageHandlerM Lcom.dspread.mdm.service.network.websocket.message.handler.BaseMessageHandlerM Lcom.dspread.mdm.service.network.websocket.message.handler.BaseMessageHandlerM Lcom.dspread.mdm.service.network.websocket.message.handler.BaseMessageHandler kotlin.Enum kotlin.EnumA @com.dspread.mdm.service.platform.api.model.SystemOperationResultA @com.dspread.mdm.service.platform.api.model.SystemOperationResult kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum? >com.dspread.mdm.service.platform.api.model.WiFiOperationResult? >com.dspread.mdm.service.platform.api.model.WiFiOperationResult? >com.dspread.mdm.service.platform.api.model.WiFiOperationResult kotlin.Enum kotlin.Enum kotlin.Enum" !android.content.BroadcastReceiver kotlin.Enum java.net.HttpURLConnection java.io.FilterInputStream java.io.FilterOutputStream2 1android.accessibilityservice.AccessibilityService android.app.IntentService android.app.IntentService android.app.Service android.app.IntentService android.app.Service android.app.Service[ android.app.ActivityEcom.dspread.mdm.service.ui.view.PasswordEditText.PasswordFullListener java.util.TimerTask) (androidx.appcompat.app.AppCompatActivity android.app.Activity android.app.Dialog kotlin.Enum android.app.Dialog android.widget.EditText, +com.dspread.mdm.service.utils.log.LogWriter, +com.dspread.mdm.service.utils.log.LogWriter