package com.bbpos.wiseapp.settings.utils;

import android.app.ActivityManager;
import android.content.Context;

import com.bbpos.wiseapp.tms.utils.ContextUtil;

public class ServiceAliveUtils {

    public static boolean isServiceAlice() {
        boolean isServiceRunning = false;
        ActivityManager manager =
            (ActivityManager) ContextUtil.getInstance().getSystemService(Context.ACTIVITY_SERVICE);
        if (manager == null) {
            return true;
        }
        for (ActivityManager.RunningServiceInfo service : manager.getRunningServices(Integer.MAX_VALUE)) {
            if ("com.bbpos.wiseapp.service.LocalService".equals(service.service.getClassName())) {
                isServiceRunning = true;
            }
        }
        return isServiceRunning;
    }
}