package com.dspread.mdm.service.services

import android.app.IntentService
import android.content.Context
import android.content.Intent
import com.dspread.mdm.service.utils.log.Logger

/**
 * 应用卸载服务
 * 移动自modules/appmanager目录，统一放在services目录下
 */
@Suppress("DEPRECATION")
class AppUninstallService : IntentService("AppUninstallService") {
    
    companion object {
        private const val TAG = "AppUninstall"
        
        const val EXTRA_TASK_ID = "taskId"
        const val EXTRA_PACKAGE_NAME = "packageName"
        const val EXTRA_APK_NAME = "apkName"
        
        /**
         * 启动应用卸载服务
         */
        fun startUninstall(
            context: Context,
            taskId: String,
            packageName: String,
            apkName: String
        ) {
            val intent = Intent(context, AppUninstallService::class.java).apply {
                putExtra(EXTRA_TASK_ID, taskId)
                putExtra(EXTRA_PACKAGE_NAME, packageName)
                putExtra(EXTRA_APK_NAME, apkName)
            }
            context.startService(intent)
        }
    }
    
    override fun onHandleIntent(intent: Intent?) {
        if (intent == null) {
            Logger.appMgrE("$TAG Intent为空")
            return
        }

        val taskId = intent.getStringExtra(EXTRA_TASK_ID) ?: ""
        val packageName = intent.getStringExtra(EXTRA_PACKAGE_NAME) ?: ""
        val apkName = intent.getStringExtra(EXTRA_APK_NAME) ?: ""

        if (taskId.isEmpty() || packageName.isEmpty()) {
            Logger.appMgrE("$TAG 参数不完整: taskId=$taskId, packageName=$packageName")
            return
        }

        Logger.appMgr("$TAG 开始卸载应用: $apkName ($packageName)")

        try {
            uninstallApp(taskId, packageName, apkName)
        } catch (e: Exception) {
            Logger.appMgrE("$TAG 卸载过程异常: $packageName", e)
            reportUninstallResult(taskId, "D04", "卸载异常: ${e.message}")
        }
    }
    
    /**
     * 卸载应用
     */
    private fun uninstallApp(taskId: String, packageName: String, apkName: String) {
        try {
            // TODO: 需要实现AppManagerApi的相关方法
            // 1. 检查应用是否已安装
            // if (!AppManagerApi.isAppInstalled(packageName)) {
            //     Logger.appMgr("$TAG 应用未安装，无需卸载: $packageName")
            //     reportUninstallResult(taskId, "D03", "应用未安装")
            //     return
            // }

            // 2. 执行卸载
            Logger.appMgr("$TAG 开始执行卸载: $packageName")
            // val uninstallResult = AppManagerApi.deletePackageViaInstaller(packageName)

            // 暂时模拟卸载成功
            val uninstallResult = true

            if (uninstallResult) {
                // 3. 验证卸载是否成功
                Thread.sleep(2000) // 等待卸载完成
                // val isStillInstalled = AppManagerApi.isAppInstalled(packageName)
                val isStillInstalled = false // 模拟卸载成功

                if (!isStillInstalled) {
                    Logger.appMgr("$TAG 应用卸载成功: $packageName")
                    reportUninstallResult(taskId, "D03", "卸载成功")

                    // 4. 更新RuleBase
                    updateRuleBase(packageName)
                } else {
                    Logger.appMgrE("$TAG 应用卸载失败，应用仍然存在: $packageName")
                    reportUninstallResult(taskId, "D04", "卸载失败")
                }
            } else {
                Logger.appMgrE("$TAG 卸载操作失败: $packageName")
                reportUninstallResult(taskId, "D04", "卸载操作失败")
            }

        } catch (e: Exception) {
            Logger.appMgrE("$TAG 卸载应用异常: $packageName", e)
            reportUninstallResult(taskId, "D04", "卸载异常: ${e.message}")
        }
    }
    
    /**
     * 更新RuleBase
     */
    private fun updateRuleBase(packageName: String) {
        try {
            // TODO: 需要实现RuleBaseManager.getInstance方法
            // val ruleBaseManager = RuleBaseManager.getInstance(this)
            // 这里可以添加RuleBase更新逻辑，比如移除已卸载应用的规则
            Logger.appMgr("$TAG RuleBase更新完成: $packageName")
        } catch (e: Exception) {
            Logger.appMgrE("$TAG RuleBase更新失败", e)
        }
    }

    /**
     * 报告卸载结果
     */
    private fun reportUninstallResult(taskId: String, statusCode: String, message: String) {
        try {
            // TODO: 需要实现WsTaskManager.updateTaskStatus和WsMessageSender.sendTaskResult方法
            // 更新任务状态
            // WsTaskManager.updateTaskStatus(taskId, statusCode, message)

            // 发送WebSocket消息
            // WsMessageSender.sendTaskResult(taskId, statusCode, message)

            Logger.appMgr("$TAG 卸载结果已报告: $taskId - $statusCode - $message")

        } catch (e: Exception) {
            Logger.appMgrE("$TAG 报告卸载结果失败", e)
        }
    }
}
