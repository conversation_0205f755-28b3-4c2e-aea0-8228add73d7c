package com.dspread.mdm.service.platform.monitor

import android.accessibilityservice.AccessibilityService
import android.accessibilityservice.AccessibilityServiceInfo
import android.content.Context
import android.content.Intent
import android.view.accessibility.AccessibilityEvent
import com.dspread.mdm.service.network.websocket.message.WsMessageSender
import com.dspread.mdm.service.utils.log.Logger
import java.util.*
import kotlin.concurrent.timer

/**
 * 用户交互监控器
 * 实现C0201设备使用状态监控功能
 * 监控用户交互事件，判断设备是否在使用中
 */
class UserInteractionMonitor(private val context: Context) {

    companion object {
        // 设备空闲判断阈值：5分钟无交互即判定为空闲
        private const val IDLE_THRESHOLD_MS = 5 * 60 * 1000L // 5分钟
        
        // 状态检查间隔：每秒检查一次
        private const val CHECK_INTERVAL_MS = 1000L // 1秒
        
        // 设备使用状态
        @Volatile
        private var isDeviceInUse = false
        
        // 最后一次用户交互时间
        @Volatile
        private var lastInteractionTime = System.currentTimeMillis() - IDLE_THRESHOLD_MS
    }

    private var isMonitoring = false
    private var statusCheckTimer: Timer? = null
    private var accessibilityServiceIntent: Intent? = null

    /**
     * 开始监控用户交互
     */
    fun startMonitoring() {
        if (isMonitoring) {
            Logger.platform("用户交互监控已在运行")
            return
        }

        try {
            // 启动状态检查定时器
            startStatusCheckTimer()
            
            // 启动辅助功能服务来监听用户交互
            startAccessibilityMonitoring()
            
            isMonitoring = true
            Logger.platform("用户交互监控已启动")
            
        } catch (e: Exception) {
            Logger.platformE("启动用户交互监控失败", e)
        }
    }

    /**
     * 停止监控用户交互
     */
    fun stopMonitoring() {
        if (!isMonitoring) {
            Logger.platform("用户交互监控未在运行")
            return
        }

        try {
            // 停止状态检查定时器
            statusCheckTimer?.cancel()
            statusCheckTimer = null
            
            isMonitoring = false
            Logger.platform("用户交互监控已停止")
            
        } catch (e: Exception) {
            Logger.platformE("停止用户交互监控失败", e)
        }
    }

    /**
     * 启动状态检查定时器
     * 每秒检查一次设备是否空闲
     */
    private fun startStatusCheckTimer() {
        statusCheckTimer = timer(period = CHECK_INTERVAL_MS) {
            checkDeviceIdleStatus()
        }
    }

    /**
     * 启动辅助功能监控
     * 注意：这需要用户手动开启辅助功能权限
     * 目前使用简化版本，通过其他方式检测用户活动
     */
    private fun startAccessibilityMonitoring() {
        try {
            // 简化版本：定期检查系统活动
            // 在实际部署时，可以启用辅助功能服务
            Logger.platform("用户交互监控已启动（简化版本）")

            // 模拟用户交互检测
            simulateUserInteractionDetection()

        } catch (e: Exception) {
            Logger.platformE("启动用户交互监控失败", e)
        }
    }

    /**
     * 模拟用户交互检测
     * 在没有辅助功能权限时的备选方案
     */
    private fun simulateUserInteractionDetection() {
        // 这里可以添加其他检测用户活动的方法
        // 例如：检查屏幕状态、检查前台应用变化等
        Logger.platform("使用模拟用户交互检测")
    }

    /**
     * 检查设备空闲状态
     */
    private fun checkDeviceIdleStatus() {
        try {
            val currentTime = System.currentTimeMillis()
            val timeSinceLastInteraction = currentTime - lastInteractionTime
            val isCurrentlyIdle = timeSinceLastInteraction >= IDLE_THRESHOLD_MS

            // 如果状态发生变化，则上报
            if (isCurrentlyIdle && isDeviceInUse) {
                // 设备从使用中变为空闲
                isDeviceInUse = false
                reportDeviceStatusChange(false)
                Logger.platform("设备状态变更: 使用中 -> 空闲")
                
            } else if (!isCurrentlyIdle && !isDeviceInUse) {
                // 设备从空闲变为使用中
                isDeviceInUse = true
                reportDeviceStatusChange(true)
                Logger.platform("设备状态变更: 空闲 -> 使用中")
            }
            
        } catch (e: Exception) {
            Logger.platformE("检查设备空闲状态失败", e)
        }
    }

    /**
     * 上报设备状态变化（C0201消息）
     */
    private fun reportDeviceStatusChange(inUse: Boolean) {
        try {
            // 发送C0201设备状态上报消息
            WsMessageSender.uploadDeviceStatus("normal", inUse)
            Logger.platform("C0201设备状态已上报: inUse=$inUse")
            
        } catch (e: Exception) {
            Logger.platformE("上报设备状态失败", e)
        }
    }

    /**
     * 记录用户交互事件
     * 由AccessibilityService调用
     */
    fun recordUserInteraction() {
        lastInteractionTime = System.currentTimeMillis()
    }

    /**
     * 获取当前设备使用状态
     */
    fun isDeviceInUse(): Boolean {
        return isDeviceInUse
    }

    /**
     * 手动检查设备是否空闲
     */
    fun isDeviceIdle(): Boolean {
        val currentTime = System.currentTimeMillis()
        return (currentTime - lastInteractionTime) >= IDLE_THRESHOLD_MS
    }
}

/**
 * 用户交互辅助功能服务
 * 监听用户的各种交互事件
 */
class UserInteractionAccessibilityService : AccessibilityService() {

    override fun onServiceConnected() {
        super.onServiceConnected()
        Logger.platform("用户交互辅助功能服务已连接")
        
        // 配置监听的事件类型
        val config = AccessibilityServiceInfo().apply {
            // 监听所有类型的事件
            eventTypes = AccessibilityEvent.TYPES_ALL_MASK
            feedbackType = AccessibilityServiceInfo.FEEDBACK_GENERIC
            flags = AccessibilityServiceInfo.FLAG_INCLUDE_NOT_IMPORTANT_VIEWS
        }
        
        serviceInfo = config
    }

    override fun onAccessibilityEvent(event: AccessibilityEvent?) {
        event?.let {
            // 过滤掉一些不重要的事件类型
            if (isUserInteractionEvent(it.eventType)) {
                // 记录用户交互时间
                // UserInteractionMonitor.recordUserInteraction()
                Logger.platform("检测到用户交互: ${getEventTypeName(it.eventType)}")
            }
        }
    }

    override fun onInterrupt() {
        Logger.platform("用户交互辅助功能服务被中断")
    }

    /**
     * 判断是否为用户交互事件
     */
    private fun isUserInteractionEvent(eventType: Int): Boolean {
        return when (eventType) {
            AccessibilityEvent.TYPE_VIEW_CLICKED,
            AccessibilityEvent.TYPE_VIEW_LONG_CLICKED,
            AccessibilityEvent.TYPE_VIEW_SELECTED,
            AccessibilityEvent.TYPE_VIEW_FOCUSED,
            AccessibilityEvent.TYPE_VIEW_TEXT_CHANGED,
            AccessibilityEvent.TYPE_VIEW_SCROLLED,
            AccessibilityEvent.TYPE_WINDOW_STATE_CHANGED,
            AccessibilityEvent.TYPE_GESTURE_DETECTION_START,
            AccessibilityEvent.TYPE_GESTURE_DETECTION_END,
            AccessibilityEvent.TYPE_TOUCH_INTERACTION_START,
            AccessibilityEvent.TYPE_TOUCH_INTERACTION_END -> true
            
            // 排除通知和一些系统事件
            AccessibilityEvent.TYPE_NOTIFICATION_STATE_CHANGED,
            AccessibilityEvent.TYPE_WINDOW_CONTENT_CHANGED -> false
            
            else -> false
        }
    }

    /**
     * 获取事件类型名称（用于调试）
     */
    private fun getEventTypeName(eventType: Int): String {
        return when (eventType) {
            AccessibilityEvent.TYPE_VIEW_CLICKED -> "VIEW_CLICKED"
            AccessibilityEvent.TYPE_VIEW_LONG_CLICKED -> "VIEW_LONG_CLICKED"
            AccessibilityEvent.TYPE_VIEW_SELECTED -> "VIEW_SELECTED"
            AccessibilityEvent.TYPE_VIEW_FOCUSED -> "VIEW_FOCUSED"
            AccessibilityEvent.TYPE_VIEW_TEXT_CHANGED -> "VIEW_TEXT_CHANGED"
            AccessibilityEvent.TYPE_VIEW_SCROLLED -> "VIEW_SCROLLED"
            AccessibilityEvent.TYPE_WINDOW_STATE_CHANGED -> "WINDOW_STATE_CHANGED"
            else -> "UNKNOWN($eventType)"
        }
    }
}
