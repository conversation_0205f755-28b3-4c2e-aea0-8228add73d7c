package com.bbpos.wiseapp.settings.adapter;

import android.bluetooth.BluetoothAdapter;
import android.bluetooth.BluetoothClass;
import android.bluetooth.BluetoothDevice;
import android.content.Context;
import android.net.wifi.ScanResult;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseAdapter;
import android.widget.ImageView;
import android.widget.TextView;

import com.bbpos.wiseapp.service.R;
import com.bbpos.wiseapp.settings.bluetooth.BluetoothController;
import com.bbpos.wiseapp.settings.dialog.BluetoothDialog;
import com.bbpos.wiseapp.settings.utils.WirelessUtil;

import java.lang.reflect.Constructor;
import java.lang.reflect.InvocationTargetException;
import java.util.List;

import static android.bluetooth.BluetoothDevice.BOND_BONDED;

/**
 * Created by ThinkPad on 2018/1/17.
 */

public class BTAdapter extends BaseAdapter {
    private List<BluetoothDevice> list_item = null;
    private int selectItem;
    private Context context;

    public BTAdapter(Context context, List<BluetoothDevice> list) {
        super();
        this.context = context;
        setData(list);
    }

    public void setData(List<BluetoothDevice> list) {
        list_item = list;
    }

    @Override
    public int getCount() {
        return list_item!=null ? this.list_item.size() : 0;
    }

    @Override
    public Object getItem(int position) {
        return list_item!=null ? list_item.get(position) : null;
    }

    @Override
    public long getItemId(int position) {
        return position;
    }

    @Override
    public View getView(final int position, View convertView, ViewGroup parent) {
        if (list_item == null) {
            return null;
        }

        ViewHolder viewHolder = null;
        if (convertView == null) {
            convertView = LayoutInflater.from(context).inflate(R.layout.item_bluetooth, parent, false);
            viewHolder = new ViewHolder();
            viewHolder.mTVName = (TextView) convertView.findViewById(R.id.tv_name);
            viewHolder.mIVSignal = (ImageView) convertView.findViewById(R.id.iv_signal);
            viewHolder.mTVStutas = (TextView) convertView.findViewById(R.id.tv_status);
            viewHolder.mIVCheck = (ImageView) convertView.findViewById(R.id.iv_check);
            convertView.setTag(viewHolder);
        } else {
            viewHolder = (ViewHolder) convertView.getTag();
        }

        if (list_item.get(position).getName()==null || list_item.get(position).getName().isEmpty()) {
            viewHolder.mTVName.setText(list_item.get(position).getAddress());
        } else {
            viewHolder.mTVName.setText(list_item.get(position).getName());
        }
        viewHolder.mIVCheck.setBackgroundResource(R.drawable.ic_bt_config);
        final int pos = position;
        viewHolder.mIVCheck.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                final BluetoothDevice device = BluetoothAdapter.getDefaultAdapter().getRemoteDevice(list_item.get(pos).getAddress());
                BluetoothDialog dialog = new BluetoothDialog(context, new BluetoothDialog.BluetoothDialogListener() {
                    @Override
                    public void onForget() {
                        try {
                            BluetoothController.removeBond(device.getClass(), device);
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }
                    @Override
                    public void onSubmit(ScanResult scanResult, String password) {

                    }
                }, device);
                dialog.show();
            }
        });
        if (BOND_BONDED == list_item.get(position).getBondState()) {
            viewHolder.mIVCheck.setVisibility(View.VISIBLE);
        } else {
            viewHolder.mIVCheck.setVisibility(View.INVISIBLE);
        }
        viewHolder.mTVStutas.setVisibility(View.GONE);
        if (BluetoothClass.Device.Major.COMPUTER == list_item.get(position).getBluetoothClass().getMajorDeviceClass()) {
            viewHolder.mIVSignal.setImageResource(R.drawable.ic_bt_laptop);
        } else if (BluetoothClass.Device.Major.PHONE == list_item.get(position).getBluetoothClass().getMajorDeviceClass()) {
            viewHolder.mIVSignal.setImageResource(R.drawable.ic_bt_cellphone);
        } else if (BluetoothClass.Device.Major.IMAGING == list_item.get(position).getBluetoothClass().getMajorDeviceClass()) {
            viewHolder.mIVSignal.setImageResource(R.drawable.ic_bt_imaging);
        } else {
            viewHolder.mIVSignal.setImageResource(R.drawable.ic_settings_bluetooth2);
        }

        return convertView;
    }

    private static class ViewHolder {
        public TextView mTVName;
        public TextView mTVStutas;
        public ImageView mIVSignal;
        public ImageView mIVCheck;
    }
}
