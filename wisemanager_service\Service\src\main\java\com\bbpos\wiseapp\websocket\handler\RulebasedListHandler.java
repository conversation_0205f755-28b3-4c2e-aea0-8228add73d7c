package com.bbpos.wiseapp.websocket.handler;

import android.app.Dialog;
import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import android.os.Build;
import android.os.Handler;
import android.preference.PreferenceManager;
import android.text.TextUtils;
import android.view.View;
import android.view.WindowManager;
import android.widget.ImageView;
import android.widget.TextView;

import com.bbpos.wiseapp.logger.BBLog;
import com.bbpos.wiseapp.logger.LLQueue;
import com.bbpos.wiseapp.service.R;
import com.bbpos.wiseapp.service.common.UsualData;
import com.bbpos.wiseapp.system.api.DeviceInfoApi;
import com.bbpos.wiseapp.tms.utils.Constants;
import com.bbpos.wiseapp.tms.utils.ContextUtil;
import com.bbpos.wiseapp.tms.utils.Helpers;
import com.bbpos.wiseapp.tms.utils.ParameterName;
import com.bbpos.wiseapp.tms.utils.RuleStatus;
import com.bbpos.wiseapp.tms.utils.SPKeys;
import com.bbpos.wiseapp.utils.SharedPreferencesUtils;
import com.bbpos.wiseapp.websocket.RulebasedService;
import com.bbpos.wiseapp.websocket.WebSocketCenter;
import com.bbpos.wiseapp.websocket.WebSocketManager;
import com.bbpos.wiseapp.websocket.WebSocketReceiver;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;

public class RulebasedListHandler {
    public static final SharedPreferences sp = PreferenceManager.getDefaultSharedPreferences(ContextUtil.getInstance());
    public static final String Add = "A";
    public static final String Modify = "M";
    public static final String Delete = "D";

    private static Dialog mDialog = null;
    private static LLQueue<String> mRulebasedApkQueue = new LLQueue<>();
    private WebSocketManager webSocketManager;
    private Context mContext;

    private static String[] service_code = new String[] {
        "C01",
        "C02",
        "C03"
    };

    public RulebasedListHandler(Context context, WebSocketManager manager) {
        mContext = context;
        webSocketManager = manager;
    }

    public static void updateRuleState(String ruleId, String ruleStatus) {
        try {
            String localRuleListStr = sp.getString(SPKeys.WEBSOCKET_RULEBASED_LIST, "");
            if (!Helpers.isStrNoEmpty(localRuleListStr))
                return;
            JSONArray localRuleList = new JSONArray(localRuleListStr);
            for (int i = 0; i < localRuleList.length(); i++) {
                JSONObject localRulebasedJsonObj = (JSONObject) localRuleList.get(i);
                String ruleIdTmp = localRulebasedJsonObj.getString(ParameterName.ruleId);
                if (ruleId.equals(ruleIdTmp)) {
                    BBLog.i(BBLog.TAG, "更新ruleId=" + ruleId + "的 rule 状态为：" + ruleStatus);
                    localRulebasedJsonObj.put(ParameterName.ruleStatus, ruleStatus);
                    break;
                }
            }
            sp.edit().putString(SPKeys.WEBSOCKET_RULEBASED_LIST, localRuleList.toString()).commit();
        } catch (JSONException e) {
            e.printStackTrace();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void updateRuleAppListState(String ruleId, String packageName, String versionName, String versionCode, String appStatus) {
        try {
            String localRuleListStr = sp.getString(SPKeys.WEBSOCKET_RULEBASED_LIST, "");
            if (!Helpers.isStrNoEmpty(localRuleListStr))
                return;
            JSONArray localRuleList = new JSONArray(localRuleListStr);
            for (int i = 0; i < localRuleList.length(); i++) {
                JSONObject localRulebasedJsonObj = (JSONObject) localRuleList.get(i);
                String ruleIdTmp = localRulebasedJsonObj.getString(ParameterName.ruleId);
                if (ruleId.equals(ruleIdTmp)) {
                    if (localRulebasedJsonObj.has(ParameterName.appList)) {
                        JSONArray appList = localRulebasedJsonObj.getJSONArray(ParameterName.appList);
                        for (int j = 0; j < appList.length(); j++) {
                            JSONObject appJson = (JSONObject) appList.get(j);
                            if (appJson.has(ParameterName.packName) && appJson.getString(ParameterName.packName).equals(packageName)
                            && appJson.has(ParameterName.versionName) && appJson.getString(ParameterName.versionName).equals(versionName)
                            && appJson.has(ParameterName.versionCode) && appJson.getString(ParameterName.versionCode).equals(versionCode)) {
                                appJson.put(ParameterName.appStatus, appStatus);
                                BBLog.i(BBLog.TAG, "更新Rule中appList的[app] " + packageName + "的状态为 " + appStatus);
                                break;
                            }
                        }
                    }
                    break;
                }
            }
            sp.edit().putString(SPKeys.WEBSOCKET_RULEBASED_LIST, localRuleList.toString()).commit();
        } catch (JSONException e) {
            e.printStackTrace();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void executeRulebasedList(Context context, Handler handler) {
        try {
            BBLog.i(BBLog.TAG, "RulebasedService.isRuleExecuting = " + RulebasedService.isRuleExecuting);
            if (!Helpers.isOnline(ContextUtil.getInstance())) {
                BBLog.i(BBLog.TAG, "當前網絡不可用，返回");
                return;
            }

            String status = SharedPreferencesUtils.getSharePreferencesValue(UsualData.SHARED_UNBOX_STATUS, "");
            BBLog.i(BBLog.TAG, "终端状态值=" + status);
            if (DeviceInfoApi.getIntance().isWisePosPro() && !WebSocketReceiver.getLoginSuccessFlag() && !"6".equals(status)) {
                BBLog.i(BBLog.TAG, "终端未登录成功且未激活，返回");
                return;
            }

            if (Constants.B_UNBOX_RUNNING || RulebasedService.isRuleExecuting == true) {
                if (Constants.B_UNBOX_RUNNING) {
                    BBLog.i(BBLog.TAG, "终端正在做UNBOX，返回");
                }
                return;
            }

            BBLog.i(BBLog.TAG, "是否低電確認之後未充電=" + Constants.IF_BATLOW_AFTER_DIALOG);
            if (Constants.IF_BATLOW_AFTER_DIALOG) {
                return;
            }

            if (RulebasedService.isRuleNeedToDo()) {
                if (Constants.IS_BATTERY_CHARGING==false && Constants.IS_BATTERY_LOW==true) {
                    handler.post(new Runnable() {
                        @Override
                        public void run() {
                            if (mDialog == null) {
                                mDialog = new Dialog(context, R.style.dialog_style_ex);
                                mDialog.setContentView(R.layout.dialog_confirm);
                                ImageView imageView = (ImageView) mDialog.findViewById(R.id.iv_image);
                                imageView.setBackground(context.getDrawable(R.drawable.low_bat));
                                TextView tv_title = (TextView) mDialog.findViewById(R.id.tv_title);
                                tv_title.setText(context.getString(R.string.low_battery));
                                TextView tv_content = (TextView) mDialog.findViewById(R.id.tv_content);
                                tv_content.setText(context.getString(R.string.install_apk_tip_dialog_message_low_power));
                                mDialog.setCanceledOnTouchOutside(false);
                                TextView tv_cancel = (TextView) mDialog.findViewById(R.id.tv_cancel);
                                tv_cancel.setVisibility(View.GONE);
                                TextView tv_install = (TextView) mDialog.findViewById(R.id.tv_install);
                                tv_install.setText(R.string.confirm);
                                tv_install.setOnClickListener(new View.OnClickListener() {
                                    @Override
                                    public void onClick(View v) {
                                        mDialog.dismiss();
                                        mDialog = null;
                                        if (Constants.IS_BATTERY_CHARGING == true || Constants.IS_BATTERY_LOW == false) {
                                            //具体传输数据
                                            Intent serviceIntent = new Intent(ContextUtil.getInstance().getApplicationContext(), RulebasedService.class);
                                            ContextUtil.getInstance().getApplicationContext().startService(serviceIntent);
                                        } else {
                                            Constants.IF_BATLOW_AFTER_DIALOG = true;
                                        }
                                    }
                                });
                                mDialog.getWindow().setType((WindowManager.LayoutParams.TYPE_SYSTEM_ALERT));
                                mDialog.show();
                            }
                        }
                    });
                } else {
                    Intent serviceIntent = new Intent(ContextUtil.getInstance().getApplicationContext(), RulebasedService.class);
                    ContextUtil.getInstance().getApplicationContext().startService(serviceIntent);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void handleMsg(String response) {
        try {
            String silentInstall = "";
            JSONArray rulebasedList = null;
            JSONArray appList = null;
            JSONArray serviceList = null;
            JSONObject responseJson = new JSONObject(response);
            if (responseJson.has(ParameterName.silent_install)) {
                silentInstall = responseJson.getString(ParameterName.silent_install);
            }
            JSONObject responseData = Helpers.getDataFromResponse(responseJson);
            if (responseData == null) {
                return;
            }
            rulebasedList = responseData.getJSONArray(ParameterName.ruleList);
            for (int i=0; i<rulebasedList.length(); i++) {
                JSONObject rulebased = rulebasedList.getJSONObject(i);
                if (!TextUtils.isEmpty(silentInstall)) {
                    rulebased.put(ParameterName.silent_install, silentInstall);
                }
                String action = rulebased.getString(ParameterName.action);
                BBLog.i(BBLog.TAG, "action = " + action);
                if (Add.equals(action) || Modify.equals(action)) {
                    String ruleId = rulebased.getString(ParameterName.ruleId);
                    addWSRulebasedJsonObjById(ruleId, rulebased);
                } else if (Delete.equals(action)) {
                    String ruleId = rulebased.getString(ParameterName.ruleId);
                    deleteWSRulebasedJsonObjById(ruleId, rulebased);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**根据ID增改rule*/
    public static void addWSRulebasedJsonObjById(String ruleId, JSONObject rulebasedJson) {
        try {
            BBLog.i(BBLog.TAG, "addWSRulebasedJsonObjById: ruleId=" + ruleId + " ==> " + rulebasedJson);
            String localRuleListStr = sp.getString(SPKeys.WEBSOCKET_RULEBASED_LIST, "");
            if (!Helpers.isStrNoEmpty(localRuleListStr)) {
                JSONArray jsonArray = new JSONArray();
                rulebasedJson.put(ParameterName.ruleStatus, RuleStatus.READY);
                jsonArray.put(rulebasedJson);
                BBLog.i(BBLog.TAG, "localRuleListStr empty: " + jsonArray.toString());
                sp.edit().putString(SPKeys.WEBSOCKET_RULEBASED_LIST, jsonArray.toString()).commit();
                return;
            }
            JSONArray localRuleList = new JSONArray(localRuleListStr);
            List<String> toDeleteRuleId = new ArrayList<String>();
            for (int i = 0; i < localRuleList.length(); i++) {
                JSONObject localRulebasedJsonObj = (JSONObject) localRuleList.get(i);
                String ruleIdTmp = localRulebasedJsonObj.getString(ParameterName.ruleId);
                String ruleStatus = localRulebasedJsonObj.getString(ParameterName.ruleStatus);
                try {
                    if (localRulebasedJsonObj.has(ParameterName.orgRuleId)) {
                        String orgRuleId = localRulebasedJsonObj.getString(ParameterName.orgRuleId);
                        if (!TextUtils.isEmpty(orgRuleId)) {
                            JSONObject rulebasedOrg = getWSRuleJsonObjById(orgRuleId);
                            if (rulebasedOrg != null) {
                                if (RuleStatus.IMPLEMENTED.equals(rulebasedOrg.getString(ParameterName.ruleStatus))
                                        || RuleStatus.COMPLETED.equals(rulebasedOrg.getString(ParameterName.ruleStatus))) {
                                    //删除那些已完成的被编辑的原始rule记录
                                    toDeleteRuleId.add(orgRuleId);
                                    localRulebasedJsonObj.remove(ParameterName.orgRuleId);
                                }
                            } else {
                                localRulebasedJsonObj.remove(ParameterName.orgRuleId);
                            }
                        }
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
                if (ruleId.equals(ruleIdTmp)) {
                    //存在rulebased
                    localRulebasedJsonObj.put(ParameterName.ruleStatus, RuleStatus.READY);
                    localRulebasedJsonObj.put(ParameterName.ruleName, rulebasedJson.getString(ParameterName.ruleName));
                    localRulebasedJsonObj.put(ParameterName.action, rulebasedJson.getString(ParameterName.action));
                    localRulebasedJsonObj.put(ParameterName.beginDate, rulebasedJson.getString(ParameterName.beginDate));
                    localRulebasedJsonObj.put(ParameterName.modifyDate, rulebasedJson.optString(ParameterName.modifyDate));
                    localRulebasedJsonObj.put(ParameterName.endDate, rulebasedJson.getString(ParameterName.endDate));
                    if (rulebasedJson.has(ParameterName.delayTime)) {
                        localRulebasedJsonObj.put(ParameterName.delayTime, rulebasedJson.getString(ParameterName.delayTime));
                    } else {
                        localRulebasedJsonObj.remove(ParameterName.delayTime);
                    }
                    if (rulebasedJson.has(ParameterName.delayCount)) {
                        localRulebasedJsonObj.put(ParameterName.delayCount, rulebasedJson.getString(ParameterName.delayCount));
                    } else {
                        localRulebasedJsonObj.remove(ParameterName.delayCount);
                    }
                    localRulebasedJsonObj.put(ParameterName.appList, rulebasedJson.getJSONArray(ParameterName.appList));
                    if (rulebasedJson.has(ParameterName.deleteAppList)) {
                        localRulebasedJsonObj.put(ParameterName.deleteAppList, rulebasedJson.getJSONArray(ParameterName.deleteAppList));
                    } else {
                        localRulebasedJsonObj.remove(ParameterName.deleteAppList);
                    }
                    localRulebasedJsonObj.put(ParameterName.serviceList, rulebasedJson.getJSONArray(ParameterName.serviceList));
                    BBLog.i(BBLog.TAG, "存在相同Rule，進行覆蓋修改 " + localRuleList.toString());
                    sp.edit().putString(SPKeys.WEBSOCKET_RULEBASED_LIST, localRuleList.toString()).commit();
                    return;
                }
            }
            //沒找到已存在rulebased，直接插入
            try {
                if (rulebasedJson.has(ParameterName.orgRuleId)) {
                    String orgRuleId = rulebasedJson.getString(ParameterName.orgRuleId);
                    if (!TextUtils.isEmpty(orgRuleId)) {
                        JSONObject rulebasedOrg = getWSRuleJsonObjById(orgRuleId);
                        if (rulebasedOrg != null) {
                            if (!RulebasedService.isRuleInExecuting(rulebasedOrg)) {
                                //删除那些不在执行中的被编辑的原始rule记录
                                toDeleteRuleId.add(orgRuleId);
                                rulebasedJson.remove(ParameterName.orgRuleId);
                            }
                        }
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
            }

            rulebasedJson.put(ParameterName.ruleStatus, RuleStatus.READY);
            localRuleList.put(rulebasedJson);
            sp.edit().putString(SPKeys.WEBSOCKET_RULEBASED_LIST, localRuleList.toString()).commit();

            //删除标志为delete的任务
            for (int i = toDeleteRuleId.size()-1; i >= 0; i--) {
                BBLog.e(BBLog.TAG, "**** 删除被edit过得原始rule： " + toDeleteRuleId.get(i));
                removeRuleJsonObjById(toDeleteRuleId.get(i));
                RulebasedListHandler.removeAppList(toDeleteRuleId.get(i));
            }
            return;
        } catch (JSONException e) {
            e.printStackTrace();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return;
    }

    /**根据ID增改rule*/
    public static void deleteWSRulebasedJsonObjById(String ruleId, JSONObject rulebasedJson) {
        try {
            BBLog.i(BBLog.TAG, "deleteWSRulebasedJsonObjById");
            String localRuleListStr = sp.getString(SPKeys.WEBSOCKET_RULEBASED_LIST, "");
            JSONArray localRuleList = new JSONArray(localRuleListStr);
            for (int i = 0; i < localRuleList.length(); i++) {
                JSONObject localRulebasedJsonObj = (JSONObject) localRuleList.get(i);
                String ruleIdTmp = localRulebasedJsonObj.getString(ParameterName.ruleId);
                if (ruleId.equals(ruleIdTmp)) {
                    //存在rulebased
                    BBLog.i(BBLog.TAG, "找到要刪除的Rule ID");
                    if (RuleStatus.IMPLEMENTED.equals(localRulebasedJsonObj.getString(ParameterName.ruleStatus))) {
                        localRulebasedJsonObj.put(ParameterName.ruleStatus, RuleStatus.READY_TO_FALLBACK);
                    } else {
                        if(Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
                            localRuleList.remove(i);
                        } else {
                            localRuleList = Helpers.remove(localRuleList, i);
                        }
                    }
                    sp.edit().putString(SPKeys.WEBSOCKET_RULEBASED_LIST, localRuleList.toString()).commit();
                    return;
                }
            }
        } catch (JSONException e) {
            e.printStackTrace();
        } catch (Exception e) {
            e.printStackTrace();
        }
        BBLog.i(BBLog.TAG, "未找到要刪除的Rule ID");
        return;
    }

    /**与本地任务列表进行对比、更新*/
    public static void cleanAppList(){
        try {
            String ruleAppListStr = sp.getString(SPKeys.RULEBASED_LIST_APP_LIST, "");
            String localRuleListStr = sp.getString(SPKeys.WEBSOCKET_RULEBASED_LIST, "");
            if (TextUtils.isEmpty(ruleAppListStr)) {
                return;
            }
            JSONArray ruleAppList = new JSONArray(ruleAppListStr);
            List<Integer> toDeleteIndexs = new ArrayList<Integer>();
            for (int i = 0; i < ruleAppList.length(); i++) {
                JSONObject localRuleJsonObj = (JSONObject) ruleAppList.get(i);
                String ruleIdTmp = localRuleJsonObj.getString(ParameterName.ruleId);
                if (!localRuleListStr.contains(ruleIdTmp)) {
                    BBLog.e(BBLog.TAG, "cleanAppList 发现第"+i+"条不存在");
                    toDeleteIndexs.add(new Integer(i));
                }
            }

            //删除标志为delete的任务
            if (Build.VERSION.SDK_INT>=Build.VERSION_CODES.KITKAT) {
                for (int i = toDeleteIndexs.size()-1; i >= 0; i--) {
                    ruleAppList.remove(toDeleteIndexs.get(i));
                }
            } else{
                for (int i = toDeleteIndexs.size() - 1; i >= 0; i--) {
                    ruleAppList = Helpers.remove(ruleAppList, toDeleteIndexs.get(i));
                }
            }
            BBLog.e(BBLog.TAG, "cleanAppList 后的ruleAppList = " + ruleAppList.toString());
            sp.edit().putString(SPKeys.RULEBASED_LIST_APP_LIST, ruleAppList.toString()).commit();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void updateAppList(String ruleId, JSONArray appList){
        try {
            String ruleListStr = sp.getString(SPKeys.RULEBASED_LIST_APP_LIST, "");
            if (!Helpers.isStrNoEmpty(ruleListStr)) {
                JSONObject jsonObject = new JSONObject();
                jsonObject.put(ParameterName.ruleId, ruleId);
                jsonObject.put(ParameterName.appList, appList);
                JSONArray jsonArray = new JSONArray();
                jsonArray.put(jsonObject);
                BBLog.i(BBLog.TAG, "updateAppList: " + jsonArray);
                sp.edit().putString(SPKeys.RULEBASED_LIST_APP_LIST, jsonArray.toString()).commit();
                return;
            }

            JSONArray ruleList = new JSONArray(ruleListStr);
            for (int i = 0; i < ruleList.length(); i++) {
                JSONObject localRuleJsonObj = (JSONObject) ruleList.get(i);
                String ruleIdTmp = localRuleJsonObj.getString(ParameterName.ruleId);
                if (ruleId.equals(ruleIdTmp)) {
                    localRuleJsonObj.put(ParameterName.appList, appList);
                    BBLog.i(BBLog.TAG, "updateAppList: " + localRuleJsonObj);
                    sp.edit().putString(SPKeys.RULEBASED_LIST_APP_LIST, ruleList.toString()).commit();
                    return;
                }
            }

            JSONObject jsonObject = new JSONObject();
            jsonObject.put("ruleId", ruleId);
            jsonObject.put("appList", appList);
            BBLog.i(BBLog.TAG, "updateAppList: " + jsonObject);
            ruleList.put(jsonObject);
            sp.edit().putString(SPKeys.RULEBASED_LIST_APP_LIST, ruleList.toString()).commit();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void removeAppList(String ruleId){
        try {
            String ruleListStr = sp.getString(SPKeys.RULEBASED_LIST_APP_LIST, "");
            if (!Helpers.isStrNoEmpty(ruleListStr)) {
                return;
            }
            JSONArray ruleList = new JSONArray(ruleListStr);
            for (int i = 0; i < ruleList.length(); i++) {
                JSONObject localRuleJsonObj = (JSONObject) ruleList.get(i);
                String ruleIdTmp = localRuleJsonObj.getString(ParameterName.ruleId);
                if (ruleId.equals(ruleIdTmp)) {
                    if(Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
                        ruleList.remove(i);
                    } else {
                        ruleList = Helpers.remove(ruleList, i);
                    }
                    BBLog.i(BBLog.TAG, "removeAppList: 从所有Rule appList中移除该条Rule appList的记录");
                    sp.edit().putString(SPKeys.RULEBASED_LIST_APP_LIST, ruleList.toString()).commit();
                    return;
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static String isRulebasedListApp(String packageName, String verName, String verCode) {
        BBLog.e(BBLog.TAG, "遍历判断" + packageName + " -- " + verName + " -- " + verCode + "是否RulebasedApp?");
        String localRuleListStr = sp.getString(SPKeys.RULEBASED_LIST_APP_LIST, "");
        if (!Helpers.isStrNoEmpty(localRuleListStr)) {
            BBLog.e(BBLog.TAG, "RULEBASED_LIST_APP_LIST 为空，不是RulebasedApp, 退出结束判断");
            return "false";
        }
        try {
            JSONArray localRuleList = new JSONArray(localRuleListStr);
            BBLog.e(BBLog.TAG, "RULEBASED_LIST_APP_LIST length = " + localRuleList.length());
            for (int i=0; i<localRuleList.length(); i++) {
                JSONObject localRuleJsonObj = (JSONObject) localRuleList.get(i);
                JSONArray localAppList = localRuleJsonObj.getJSONArray(ParameterName.appList);
                BBLog.i(BBLog.TAG, "从该条rule中查找：ruleId=" + localRuleJsonObj.getString(ParameterName.ruleId)
                        + " appList=" + localAppList);
                for (int j=0; j<localAppList.length(); j++) {
                    JSONObject localAppJsonObj = (JSONObject) localAppList.get(j);
//                    BBLog.e(BBLog.TAG, "isRulebasedApp " + localAppJsonObj);
                    String packNameTmp = localAppJsonObj.getString(ParameterName.packName);
                    String versionCode = localAppJsonObj.getString(ParameterName.versionCode);
                    String versionName = localAppJsonObj.getString(ParameterName.versionName);
                    if (packageName.equals(packNameTmp) && verCode.equals(versionCode) && verName.equals(versionName)) {
                        BBLog.e(BBLog.TAG, "是RulebasedApp");
                        return "true";
                    } else if (packageName.equals(packNameTmp) && Integer.valueOf(verCode)>Integer.valueOf(versionCode)) {
                        BBLog.e(BBLog.TAG, "是RulebasedApp, 但需要降級");
                        return "true_downgrade"+localRuleJsonObj.getString(ParameterName.ruleId);
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        BBLog.e(BBLog.TAG, "不是RulebasedApp");
        return "false";
    }

    /**根据任务号返回任务json*/
    public static JSONObject getWSRuleJsonObjById(String ruleId) {
        try {
            String localRuleListStr = sp.getString(SPKeys.WEBSOCKET_RULEBASED_LIST, "");
            if (!Helpers.isStrNoEmpty(localRuleListStr))
                return null;
            JSONArray localRuleList = new JSONArray(localRuleListStr);
            for (int i = 0; i < localRuleList.length(); i++) {
                JSONObject localRuleJsonObj = (JSONObject) localRuleList.get(i);
                String ruleIdTmp = localRuleJsonObj.getString(ParameterName.ruleId);
                if (ruleId.equals(ruleIdTmp)) {
                    return localRuleJsonObj;
                }
            }
        } catch (JSONException e) {
            e.printStackTrace();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public static void removeRuleJsonObjById(String ruleId) {
        try {
            String localRuleListStr = sp.getString(SPKeys.WEBSOCKET_RULEBASED_LIST, "");
            if (!Helpers.isStrNoEmpty(localRuleListStr))
                return;
            BBLog.i(BBLog.TAG, "removeRuleJsonObjById: " + ruleId);
            JSONArray localRuleList = new JSONArray(localRuleListStr);
            for (int i = 0; i < localRuleList.length(); i++) {
                JSONObject localRuleJsonObj = (JSONObject) localRuleList.get(i);
                String ruleIdTmp = localRuleJsonObj.getString(ParameterName.ruleId);
                if (ruleId.equals(ruleIdTmp)) {
                    if(Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
                        localRuleList.remove(i);
                    } else {
                        localRuleList = Helpers.remove(localRuleList, i);
                    }
                    sp.edit().putString(SPKeys.WEBSOCKET_RULEBASED_LIST, localRuleList.toString()).commit();
                    return;
                }
            }
        } catch (JSONException e) {
            e.printStackTrace();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /////////////////RULE 執行結果處理/////////////////////
    public static void addRuleResultJsonObj(String ruleId, JSONObject jsonObject) {
        try {
            String ruleListStr = sp.getString(SPKeys.RULEBASED_EXECUTE_RESULT_LIST, "");
            if (!Helpers.isStrNoEmpty(ruleListStr)) {
                JSONArray jsonArray = new JSONArray();
                jsonArray.put(jsonObject);
                BBLog.i(BBLog.TAG, "addRuleResultJsonObj: " + jsonArray);
                sp.edit().putString(SPKeys.RULEBASED_EXECUTE_RESULT_LIST, jsonArray.toString()).commit();
                return;
            }

            JSONArray ruleList = new JSONArray(ruleListStr);
            for (int i = 0; i < ruleList.length(); i++) {
                JSONObject localRuleJsonObj = ruleList.getJSONObject(i);
                JSONObject data = localRuleJsonObj.optJSONObject(ParameterName.DATA);
                String ruleIdTmp = data.optString(ParameterName.ruleId);
                if (ruleId.equals(ruleIdTmp)) {
                    ruleList.put(i, jsonObject);
                    BBLog.i(BBLog.TAG, "addRuleResultJsonObj: " + localRuleJsonObj);
                    BBLog.i(BBLog.TAG, "保存结果: " + ruleList.toString());
                    sp.edit().putString(SPKeys.RULEBASED_EXECUTE_RESULT_LIST, ruleList.toString()).commit();
                    return;
                }
            }

            BBLog.i(BBLog.TAG, "addRuleResultJsonObj: " + jsonObject);
            ruleList.put(jsonObject);
            sp.edit().putString(SPKeys.RULEBASED_EXECUTE_RESULT_LIST, ruleList.toString()).commit();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void removeRuleResultJsonObjByOrgReqId(String orgReqId) {
        try {
            String localRuleListStr = sp.getString(SPKeys.RULEBASED_EXECUTE_RESULT_LIST, "");
            if (!Helpers.isStrNoEmpty(localRuleListStr))
                return;
            BBLog.i(BBLog.TAG, "removeRuleResultJsonObjByOrgReqId: " + orgReqId);
            JSONArray localRuleList = new JSONArray(localRuleListStr);
            for (int i=localRuleList.length()-1; i>=0 ; i--) {
                JSONObject localRuleJsonObj = (JSONObject) localRuleList.get(i);
                if (localRuleJsonObj.length() == 0 || !localRuleJsonObj.has(ParameterName.request_id)) {
                    BBLog.i(BBLog.TAG, "delete empty msg: " + i);
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
                        localRuleList.remove(i);
                    } else {
                        localRuleList = Helpers.remove(localRuleList, i);
                    }
                    BBLog.i(BBLog.TAG, "removeRuleResultJsonObjByOrgReqId 后队列剩余: " + localRuleList.length());
                }
            }

            for (int i = 0; i < localRuleList.length(); i++) {
                JSONObject localRuleJsonObj = (JSONObject) localRuleList.get(i);
                String reqIdTmp = localRuleJsonObj.getString(ParameterName.request_id);
                if (orgReqId.equals(reqIdTmp)) {
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
                        localRuleList.remove(i);
                    } else {
                        localRuleList = Helpers.remove(localRuleList, i);
                    }
                    BBLog.i(BBLog.TAG, "removeRuleResultJsonObjByOrgReqId 后队列剩余: " + localRuleList.length());
                    sp.edit().putString(SPKeys.RULEBASED_EXECUTE_RESULT_LIST, localRuleList.toString()).commit();
                    return;
                }
            }
        } catch (JSONException e) {
            e.printStackTrace();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void clearRuleResultCache() {
        sp.edit().remove(SPKeys.RULEBASED_EXECUTE_RESULT_LIST).commit();
    }

    public static void uploadRuleResult() {
        try {
            String ruleListStr = sp.getString(SPKeys.RULEBASED_EXECUTE_RESULT_LIST, "");
            if (Helpers.isStrNoEmpty(ruleListStr)) {
                JSONArray ruleList = new JSONArray(ruleListStr);
                for (int i = 0; i < ruleList.length(); i++) {
                    JSONObject localRuleJsonObj = (JSONObject) ruleList.get(i);
                    WebSocketCenter.sendMessage(localRuleJsonObj.toString(), true);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
