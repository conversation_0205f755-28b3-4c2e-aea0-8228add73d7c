<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context="com.bbpos.wiseapp.settings.activity.SettingActivity">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/background"
        android:orientation="vertical">

        <include layout="@layout/top_bar"/>

        <LinearLayout
            android:id="@+id/ll_display"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="58dp"
                android:layout_marginLeft="24dp"
                android:layout_marginRight="24dp">
                <ImageView
                    android:layout_width="18dp"
                    android:layout_height="18dp"
                    android:layout_margin="4dp"
                    android:layout_gravity="center_vertical"
                    android:alpha="0.52"
                    android:background="@drawable/display"/>
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:text="@string/display"
                    android:textSize="14sp"
                    android:textColor="@color/category"/>
            </LinearLayout>

            <LinearLayout
                android:id="@+id/ll_brightness"
                android:layout_width="match_parent"
                android:layout_height="104dp"
                android:background="@drawable/bg_shape"
                android:layout_marginLeft="24dp"
                android:layout_marginRight="24dp"
                android:orientation="vertical">
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="48dp"
                    android:paddingLeft="14dp"
                    android:paddingRight="14dp"
                    android:paddingTop="18dp"
                    android:layout_gravity="center"
                    android:orientation="horizontal">
                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:background="@drawable/brightness"/>
                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="10dp"
                        android:maxLines="1"
                        android:text="@string/brightnesslevel"
                        android:textSize="16sp"
                        android:textColor="@color/title"/>
                </LinearLayout>
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="0dp"
                    android:layout_weight="1"
                    android:layout_gravity="center_vertical"
                    android:orientation="horizontal">
                    <SeekBar
                        android:id="@+id/sb_brightness"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:progress="255"
                        android:max="255"/>
                </LinearLayout>
            </LinearLayout>
            <LinearLayout
                android:id="@+id/ll_language"
                android:layout_width="match_parent"
                android:layout_height="72dp"
                android:background="@drawable/bg_selector"
                android:layout_marginLeft="24dp"
                android:layout_marginRight="24dp"
                android:layout_marginTop="15dp"
                android:orientation="vertical">
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="42dp"
                    android:paddingLeft="14dp"
                    android:paddingRight="14dp"
                    android:paddingTop="18dp"
                    android:layout_gravity="center"
                    android:orientation="horizontal">
                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:background="@drawable/language"/>
                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="10dp"
                        android:maxLines="1"
                        android:text="@string/language"
                        android:textSize="16sp"
                        android:textColor="@color/title"/>
                </LinearLayout>
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="0dp"
                    android:layout_weight="1"
                    android:paddingLeft="48dp"
                    android:orientation="horizontal">
                    <TextView
                        android:id="@+id/tv_cur_language"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:maxLines="1"
                        android:text="English"
                        android:textColor="@color/subtitle"
                        android:textSize="14sp"/>
                </LinearLayout>
            </LinearLayout>
        </LinearLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"/>

        <LinearLayout
            android:id="@+id/ll_network"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="58dp"
                android:layout_marginLeft="24dp"
                android:layout_marginRight="24dp">
                <ImageView
                    android:layout_width="18dp"
                    android:layout_height="18dp"
                    android:layout_margin="4dp"
                    android:layout_gravity="center_vertical"
                    android:alpha="0.52"
                    android:background="@drawable/network"/>
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:text="@string/network"
                    android:textSize="14sp"
                    android:textColor="@color/category"/>
            </LinearLayout>
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="24dp"
                android:layout_marginRight="24dp"
                android:orientation="horizontal">
                <LinearLayout
                    android:id="@+id/ll_wifi"
                    android:layout_width="0dp"
                    android:layout_height="72dp"
                    android:layout_weight="1"
                    android:gravity="center_vertical"
                    android:background="@drawable/bg_selector"
                    android:orientation="vertical">
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:paddingLeft="14dp"
                        android:paddingRight="10dp"
                        android:orientation="horizontal">
                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:background="@drawable/wifi"/>
                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginLeft="10dp"
                            android:maxLines="1"
                            android:text="@string/wifi"
                            android:textSize="16sp"
                            android:textColor="@color/title"/>
                    </LinearLayout>
                </LinearLayout>
                <View
                    android:layout_width="12dp"
                    android:layout_height="match_parent"/>
                <LinearLayout
                    android:id="@+id/ll_bluetooth"
                    android:layout_width="0dp"
                    android:layout_height="72dp"
                    android:layout_weight="1"
                    android:gravity="center_vertical"
                    android:background="@drawable/bg_selector"
                    android:orientation="vertical">
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:paddingLeft="14dp"
                        android:paddingRight="10dp"
                        android:orientation="horizontal">
                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:background="@drawable/bluetooth"/>
                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:maxLines="1"
                            android:ellipsize="marquee"
                            android:text="@string/bluetooth"
                            android:textSize="16sp"
                            android:textColor="@color/title"/>
                    </LinearLayout>
                </LinearLayout>
            </LinearLayout>
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="3"
            android:orientation="vertical"
            android:gravity="center">
            <TextView
                android:id="@+id/tv_version"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:textColor="@color/category"
                android:textSize="10sp"/>
        </LinearLayout>
    </LinearLayout>
</LinearLayout>
