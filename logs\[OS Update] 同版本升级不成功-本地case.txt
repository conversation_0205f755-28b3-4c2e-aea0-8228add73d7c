2025-08-18 13:15:10.937  2990-2990  WebSocket               com.dspread.mdm.service              I  🔧 重连 WebSocket: ws://***********:8080/status/websocket/register?serialNo=MDEzNTQwOTAyMDI1MDMwNTAzOTk=&publicKey=TUlHZk1BMEdDU3FHU0liM0RRRUJBUVVBQTRHTkFEQ0JpUUtCZ1FEUkhsaTBCQWtXZm12cjZTdkdlbHFEWjNGc0I2a0d3UzNyN1ZzZzFiMWJvcDl3RmtRWjlWc1E1MlNnYlpFTjNWeGtUaFBISUFSeTFvSEhmQVNmK2VzZkc1TEErcExDZ3dlNjErdzljWWdFZ2JDSHZrRTY2eGRSNmJXNXgrTHNtT24rdjhSYmVMcWMyZTlyWlNrbTJjV0VuWmpENDZNYmlPaVhORkkrditJcWZ3SURBUUFC&query=0&msgVer=3&timestamp=1755494110912&signature=WAAtg2pH7LgpNNYV/lbosdkUNaOK3ggAdinv0oO0WfNacU+95d2K4DCNmgtsK4OW2v49oQ0ObuSSoXgBZ9da3xCnvQBDLZMSzZ37k5EEKJHvdUrq6eE6wmw+IrmU3lR0pmZV1jhv8WjHkiUA8Xml3EER1x0vOkcjUXhsqJA4y0g=
2025-08-18 13:15:10.949  2990-2990  WebSocket               com.dspread.mdm.service              I  🔧 未检测到系统代理设置
2025-08-18 13:15:10.987  2990-3153  TrafficStats            com.dspread.mdm.service              D  tagSocket(5) with statsTag=0xffffffff, statsUid=-1
2025-08-18 13:15:11.006  2990-3152  WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误 (Ask Gemini)
                                                                                                    com.neovisionaries.ws.client.WebSocketException: Failed to connect to '***********:8080': failed to connect to /*********** (port 8080) from /:: (port 0) after 15000ms: connect failed: ENETUNREACH (Network is unreachable)
                                                                                                    	at com.neovisionaries.ws.client.SocketConnector.connectSocket(SocketConnector.java:126)
                                                                                                    	at com.neovisionaries.ws.client.SocketConnector.doConnect(SocketConnector.java:238)
                                                                                                    	at com.neovisionaries.ws.client.SocketConnector.connect(SocketConnector.java:189)
                                                                                                    	at com.neovisionaries.ws.client.WebSocket.connect(WebSocket.java:2351)
                                                                                                    	at com.neovisionaries.ws.client.ConnectThread.runMain(ConnectThread.java:32)
                                                                                                    	at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45)
                                                                                                    Caused by: java.net.ConnectException: failed to connect to /*********** (port 8080) from /:: (port 0) after 15000ms: connect failed: ENETUNREACH (Network is unreachable)
                                                                                                    	at libcore.io.IoBridge.connect(IoBridge.java:187)
                                                                                                    	at java.net.PlainSocketImpl.socketConnect(PlainSocketImpl.java:142)
                                                                                                    	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:390)
                                                                                                    	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:230)
                                                                                                    	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:212)
                                                                                                    	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:436)
                                                                                                    	at java.net.Socket.connect(Socket.java:646)
                                                                                                    	at com.neovisionaries.ws.client.SocketInitiator$SocketRacer.run(SocketInitiator.java:126)
                                                                                                    Caused by: android.system.ErrnoException: connect failed: ENETUNREACH (Network is unreachable)
                                                                                                    	at libcore.io.Linux.connect(Native Method)
                                                                                                    	at libcore.io.ForwardingOs.connect(ForwardingOs.java:201)
                                                                                                    	at libcore.io.BlockGuardOs.connect(BlockGuardOs.java:158)
                                                                                                    	at libcore.io.ForwardingOs.connect(ForwardingOs.java:201)
                                                                                                    	at libcore.io.IoBridge.connectErrno(IoBridge.java:218)
                                                                                                    	at libcore.io.IoBridge.connect(IoBridge.java:179)
                                                                                                    	at java.net.PlainSocketImpl.socketConnect(PlainSocketImpl.java:142) 
                                                                                                    	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:390) 
                                                                                                    	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:230) 
                                                                                                    	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:212) 
                                                                                                    	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:436) 
                                                                                                    	at java.net.Socket.connect(Socket.java:646) 
                                                                                                    	at com.neovisionaries.ws.client.SocketInitiator$SocketRacer.run(SocketInitiator.java:126) 
2025-08-18 13:15:11.016  2990-3152  WebSocket               com.dspread.mdm.service              I  🔧 onError: 不触发重连，等待 onDisconnected 处理
2025-08-18 13:15:11.029  2990-3152  WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误 (Ask Gemini)
                                                                                                    com.neovisionaries.ws.client.WebSocketException: Failed to connect to '***********:8080': failed to connect to /*********** (port 8080) from /:: (port 0) after 15000ms: connect failed: ENETUNREACH (Network is unreachable)
                                                                                                    	at com.neovisionaries.ws.client.SocketConnector.connectSocket(SocketConnector.java:126)
                                                                                                    	at com.neovisionaries.ws.client.SocketConnector.doConnect(SocketConnector.java:238)
                                                                                                    	at com.neovisionaries.ws.client.SocketConnector.connect(SocketConnector.java:189)
                                                                                                    	at com.neovisionaries.ws.client.WebSocket.connect(WebSocket.java:2351)
                                                                                                    	at com.neovisionaries.ws.client.ConnectThread.runMain(ConnectThread.java:32)
                                                                                                    	at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45)
                                                                                                    Caused by: java.net.ConnectException: failed to connect to /*********** (port 8080) from /:: (port 0) after 15000ms: connect failed: ENETUNREACH (Network is unreachable)
                                                                                                    	at libcore.io.IoBridge.connect(IoBridge.java:187)
                                                                                                    	at java.net.PlainSocketImpl.socketConnect(PlainSocketImpl.java:142)
                                                                                                    	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:390)
                                                                                                    	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:230)
                                                                                                    	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:212)
                                                                                                    	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:436)
                                                                                                    	at java.net.Socket.connect(Socket.java:646)
                                                                                                    	at com.neovisionaries.ws.client.SocketInitiator$SocketRacer.run(SocketInitiator.java:126)
                                                                                                    Caused by: android.system.ErrnoException: connect failed: ENETUNREACH (Network is unreachable)
                                                                                                    	at libcore.io.Linux.connect(Native Method)
                                                                                                    	at libcore.io.ForwardingOs.connect(ForwardingOs.java:201)
                                                                                                    	at libcore.io.BlockGuardOs.connect(BlockGuardOs.java:158)
                                                                                                    	at libcore.io.ForwardingOs.connect(ForwardingOs.java:201)
                                                                                                    	at libcore.io.IoBridge.connectErrno(IoBridge.java:218)
                                                                                                    	at libcore.io.IoBridge.connect(IoBridge.java:179)
                                                                                                    	at java.net.PlainSocketImpl.socketConnect(PlainSocketImpl.java:142) 
                                                                                                    	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:390) 
                                                                                                    	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:230) 
                                                                                                    	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:212) 
                                                                                                    	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:436) 
                                                                                                    	at java.net.Socket.connect(Socket.java:646) 
                                                                                                    	at com.neovisionaries.ws.client.SocketInitiator$SocketRacer.run(SocketInitiator.java:126) 
2025-08-18 13:15:11.037  2990-3152  WebSocket               com.dspread.mdm.service              I  🔧 reconnect() 被调用，调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onConnectError:675
2025-08-18 13:15:11.044  2990-3152  WebSocket               com.dspread.mdm.service              I  🔧 开始重连 WebSocket... (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onConnectError:675)
2025-08-18 13:15:11.050  2990-3152  WebSocket               com.dspread.mdm.service              I  🔧 使用Provisioning配置的重连策略: delayPolicy=30, delaySwitch=1, delayTime=900
2025-08-18 13:15:11.056  2990-3152  WebSocket               com.dspread.mdm.service              I  🔧 当前断开记录数: 0, 延迟开关: 1
2025-08-18 13:15:11.062  2990-3152  WebSocket               com.dspread.mdm.service              I  🔧 重连策略计算完成: 第4次重连，间隔6000ms (6秒)
2025-08-18 13:15:11.069  2990-3152  WebSocket               com.dspread.mdm.service              I  🔧 开始第4次重连，间隔6000ms (6秒) (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onConnectError:675)
2025-08-18 13:15:14.199  2990-2990  Receiver                com.dspread.mdm.service              D  🔧 电池状态更新: 电池状态: 电量=73%, 温度=28.8°C, 健康=2, 充电=是, 低电量=否, 耗尽=否
2025-08-18 13:15:15.202   658-1335  BufferQueueDebug        surfaceflinger                       E  [ActivityRecord{956760c u0 com.dspread.mdm.service/.ui.activity.TestActivity#134](this:0xa5413c40,id:-1,api:0,p:-1,c:-1) id info cannot be read from 'ActivityRecord{956760c u0 com.dspread.mdm.service/.ui.activity.TestActivity#134'
2025-08-18 13:15:15.251   658-1335  BufferQueueDebug        surfaceflinger                       E  [3b26e5b Splash Screen com.dspread.mdm.service#135](this:0xa5411c40,id:-1,api:0,p:-1,c:-1) id info cannot be read from '3b26e5b Splash Screen com.dspread.mdm.service#135'
2025-08-18 13:15:15.260   658-1335  BufferQueueDebug        surfaceflinger                       E  [Splash Screen com.dspread.mdm.service#136](this:0xa540fc40,id:-1,api:0,p:-1,c:-1) id info cannot be read from 'Splash Screen com.dspread.mdm.service#136'
2025-08-18 13:15:15.307   658-972   BufferQueueDebug        surfaceflinger                       E  [9d3d3f ActivityRecordInputSink com.dspread.mdm.service/.ui.activity.TestActivity#139](this:0xa5409c40,id:-1,api:0,p:-1,c:-1) id info cannot be read from '9d3d3f ActivityRecordInputSink com.dspread.mdm.service/.ui.activity.TestActivity#139'
2025-08-18 13:15:15.337  2990-2990  Compatibil...geReporter com.dspread.mdm.service              D  Compat change id reported: 210923482; UID 1000; state: ENABLED
2025-08-18 13:15:15.386  2990-2990  Compatibil...geReporter com.dspread.mdm.service              D  Compat change id reported: 171228096; UID 1000; state: ENABLED
2025-08-18 13:15:15.442  2990-2990  getAnimDeal             com.dspread.mdm.service              I  mode:3 packageName:com.dspread.mdm.service result: false
2025-08-18 13:15:15.452  2990-2990  SurfaceFactory          com.dspread.mdm.service              I  [static] sSurfaceFactory = com.mediatek.view.impl.SurfaceFactoryImpl@18a0820
2025-08-18 13:15:15.453  2990-3051  PowerHalWrapper         com.dspread.mdm.service              I  PowerHalWrapper.getInstance 
2025-08-18 13:15:15.459  2990-2990  Compatibil...geReporter com.dspread.mdm.service              D  Compat change id reported: 237531167; UID 1000; state: DISABLED
2025-08-18 13:15:15.465  2990-2990  OpenGLRenderer          com.dspread.mdm.service              W  Unknown dataspace 0
2025-08-18 13:15:15.471  2990-2990  VRI[TestActivity]       com.dspread.mdm.service              D  hardware acceleration = true, forceHwAccelerated = false
2025-08-18 13:15:15.477   658-1335  BufferQueueDebug        surfaceflinger                       E  [2fcd4d4 com.dspread.mdm.service/com.dspread.mdm.service.ui.activity.TestActivity#140](this:0xa5400c40,id:-1,api:0,p:-1,c:-1) id info cannot be read from '2fcd4d4 com.dspread.mdm.service/com.dspread.mdm.service.ui.activity.TestActivity#140'
2025-08-18 13:15:15.487  2990-2990  InputTransport          com.dspread.mdm.service              D  Create ARC handle: 0xb12e3a80
2025-08-18 13:15:15.487  2990-2990  InputEventReceiver      com.dspread.mdm.service              D  Input log is disabled in InputEventReceiver.
2025-08-18 13:15:15.487  2990-2990  InputTransport          com.dspread.mdm.service              D  Input log is disabled in InputChannel.
2025-08-18 13:15:15.576   658-1335  BufferQueueDebug        surfaceflinger                       E  [com.dspread.mdm.service/com.dspread.mdm.service.ui.activity.TestActivity#141](this:0xa5403c40,id:-1,api:0,p:-1,c:-1) id info cannot be read from 'com.dspread.mdm.service/com.dspread.mdm.service.ui.activity.TestActivity#141'
2025-08-18 13:15:15.602  2990-2990  BufferQueueConsumer     com.dspread.mdm.service              D  [](id:bae00000000,api:0,p:-1,c:2990) connect: controlledByApp=false
2025-08-18 13:15:15.658  2990-3186  OpenGLRenderer          com.dspread.mdm.service              E  Unable to match the desired swap behavior.
2025-08-18 13:15:15.668  2990-3191  ion                     com.dspread.mdm.service              E  ioctl c0044901 failed with code -1: Invalid argument
2025-08-18 13:15:15.708  2990-3186  BLASTBufferQueue        com.dspread.mdm.service              D  [VRI[TestActivity]#0](f:0,a:1) acquireNextBufferLocked size=480x854 mFrameNumber=1 applyTransaction=true mTimestamp=71349535387(auto) mPendingTransactions.size=0 graphicBufferId=12841952215045 transform=3
2025-08-18 13:15:15.718   658-972   BufferQueueDebug        surfaceflinger                       E  [Surface(name=2fcd4d4 com.dspread.mdm.service/com.dspread.mdm.service.ui.activity.TestActivity)/@0x8c09322 - animation-leash of starting_reveal#146](this:0xa53fcc40,id:-1,api:0,p:-1,c:-1) id info cannot be read from 'Surface(name=2fcd4d4 com.dspread.mdm.service/com.dspread.mdm.service.ui.activity.TestActivity)/@0x8c09322 - animation-leash of starting_reveal#146'
2025-08-18 13:15:16.063  2990-2990  WebSocket               com.dspread.mdm.service              I  🔧 C0905 主动式上送: manual_trigger (主动: 1)
2025-08-18 13:15:16.070  2990-2990  WebSocket               com.dspread.mdm.service              I  🔧 首次上送，上送当日数据
2025-08-18 13:15:16.078  2990-2990  WebSocket               com.dspread.mdm.service              I  🔧 没有流量数据需要上送
2025-08-18 13:15:16.085  2990-2990  Common                  com.dspread.mdm.service              D  🔧 刷新时自动上送流量统计
2025-08-18 13:15:16.164   658-1333  BufferQueueDebug        surfaceflinger                       E  [Surface(name=3b26e5b Splash Screen com.dspread.mdm.service)/@0xdea08a0 - animation-leash of window_animation#148](this:0xa5607c40,id:-1,api:0,p:-1,c:-1) id info cannot be read from 'Surface(name=3b26e5b Splash Screen com.dspread.mdm.service)/@0xdea08a0 - animation-leash of window_animation#148'
2025-08-18 13:15:16.178  2990-3040  Platform                com.dspread.mdm.service              D  🔧 DspreadService 解析SP版本: V1.0.5
2025-08-18 13:15:16.203  2990-3040  Platform                com.dspread.mdm.service              D  🔧 DspreadService 首次获取SP版本成功: V1.0.5 (原始: *SP_VERSION:V1.0.5#*COMMIT:2d7cc90#*DATETIME:20250...)
2025-08-18 13:15:16.296  2990-2990  Common                  com.dspread.mdm.service              D  🔧 TestActivity UI已更新: SP=V1.0.5, SN=01354090202503050399
2025-08-18 13:15:17.087  2990-2990  WebSocket               com.dspread.mdm.service              I  🔧 重连 WebSocket: ws://***********:8080/status/websocket/register?serialNo=MDEzNTQwOTAyMDI1MDMwNTAzOTk=&publicKey=TUlHZk1BMEdDU3FHU0liM0RRRUJBUVVBQTRHTkFEQ0JpUUtCZ1FEUkhsaTBCQWtXZm12cjZTdkdlbHFEWjNGc0I2a0d3UzNyN1ZzZzFiMWJvcDl3RmtRWjlWc1E1MlNnYlpFTjNWeGtUaFBISUFSeTFvSEhmQVNmK2VzZkc1TEErcExDZ3dlNjErdzljWWdFZ2JDSHZrRTY2eGRSNmJXNXgrTHNtT24rdjhSYmVMcWMyZTlyWlNrbTJjV0VuWmpENDZNYmlPaVhORkkrditJcWZ3SURBUUFC&query=0&msgVer=3&timestamp=1755494117074&signature=FjAkJGg4c0OwRVHxasP2GhXhkpq/KXXhieIPq1o884iHcCJilxugtIfIPTuHUFsnnSflHzWq4Y6FW6qBR/k3CmsYJcfplmmKlujdt95CevtNRmFGd6gIVVXjC8pAFmjQx3aLx7r+tvwh9SfH9fxwmzW9U557k6X++aq0sZLRTn4=
2025-08-18 13:15:17.094  2990-2990  WebSocket               com.dspread.mdm.service              I  🔧 未检测到系统代理设置
2025-08-18 13:15:17.116  2990-3202  TrafficStats            com.dspread.mdm.service              D  tagSocket(129) with statsTag=0xffffffff, statsUid=-1
2025-08-18 13:15:17.133  2990-3201  WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误 (Ask Gemini)
                                                                                                    com.neovisionaries.ws.client.WebSocketException: Failed to connect to '***********:8080': failed to connect to /*********** (port 8080) from /:: (port 0) after 15000ms: connect failed: ENETUNREACH (Network is unreachable)
                                                                                                    	at com.neovisionaries.ws.client.SocketConnector.connectSocket(SocketConnector.java:126)
                                                                                                    	at com.neovisionaries.ws.client.SocketConnector.doConnect(SocketConnector.java:238)
                                                                                                    	at com.neovisionaries.ws.client.SocketConnector.connect(SocketConnector.java:189)
                                                                                                    	at com.neovisionaries.ws.client.WebSocket.connect(WebSocket.java:2351)
                                                                                                    	at com.neovisionaries.ws.client.ConnectThread.runMain(ConnectThread.java:32)
                                                                                                    	at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45)
                                                                                                    Caused by: java.net.ConnectException: failed to connect to /*********** (port 8080) from /:: (port 0) after 15000ms: connect failed: ENETUNREACH (Network is unreachable)
                                                                                                    	at libcore.io.IoBridge.connect(IoBridge.java:187)
                                                                                                    	at java.net.PlainSocketImpl.socketConnect(PlainSocketImpl.java:142)
                                                                                                    	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:390)
                                                                                                    	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:230)
                                                                                                    	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:212)
                                                                                                    	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:436)
                                                                                                    	at java.net.Socket.connect(Socket.java:646)
                                                                                                    	at com.neovisionaries.ws.client.SocketInitiator$SocketRacer.run(SocketInitiator.java:126)
                                                                                                    Caused by: android.system.ErrnoException: connect failed: ENETUNREACH (Network is unreachable)
                                                                                                    	at libcore.io.Linux.connect(Native Method)
                                                                                                    	at libcore.io.ForwardingOs.connect(ForwardingOs.java:201)
                                                                                                    	at libcore.io.BlockGuardOs.connect(BlockGuardOs.java:158)
                                                                                                    	at libcore.io.ForwardingOs.connect(ForwardingOs.java:201)
                                                                                                    	at libcore.io.IoBridge.connectErrno(IoBridge.java:218)
                                                                                                    	at libcore.io.IoBridge.connect(IoBridge.java:179)
                                                                                                    	at java.net.PlainSocketImpl.socketConnect(PlainSocketImpl.java:142) 
                                                                                                    	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:390) 
                                                                                                    	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:230) 
                                                                                                    	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:212) 
                                                                                                    	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:436) 
                                                                                                    	at java.net.Socket.connect(Socket.java:646) 
                                                                                                    	at com.neovisionaries.ws.client.SocketInitiator$SocketRacer.run(SocketInitiator.java:126) 
2025-08-18 13:15:17.141  2990-3201  WebSocket               com.dspread.mdm.service              I  🔧 onError: 不触发重连，等待 onDisconnected 处理
2025-08-18 13:15:17.158  2990-3201  WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误 (Ask Gemini)
                                                                                                    com.neovisionaries.ws.client.WebSocketException: Failed to connect to '***********:8080': failed to connect to /*********** (port 8080) from /:: (port 0) after 15000ms: connect failed: ENETUNREACH (Network is unreachable)
                                                                                                    	at com.neovisionaries.ws.client.SocketConnector.connectSocket(SocketConnector.java:126)
                                                                                                    	at com.neovisionaries.ws.client.SocketConnector.doConnect(SocketConnector.java:238)
                                                                                                    	at com.neovisionaries.ws.client.SocketConnector.connect(SocketConnector.java:189)
                                                                                                    	at com.neovisionaries.ws.client.WebSocket.connect(WebSocket.java:2351)
                                                                                                    	at com.neovisionaries.ws.client.ConnectThread.runMain(ConnectThread.java:32)
                                                                                                    	at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45)
                                                                                                    Caused by: java.net.ConnectException: failed to connect to /*********** (port 8080) from /:: (port 0) after 15000ms: connect failed: ENETUNREACH (Network is unreachable)
                                                                                                    	at libcore.io.IoBridge.connect(IoBridge.java:187)
                                                                                                    	at java.net.PlainSocketImpl.socketConnect(PlainSocketImpl.java:142)
                                                                                                    	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:390)
                                                                                                    	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:230)
                                                                                                    	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:212)
                                                                                                    	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:436)
                                                                                                    	at java.net.Socket.connect(Socket.java:646)
                                                                                                    	at com.neovisionaries.ws.client.SocketInitiator$SocketRacer.run(SocketInitiator.java:126)
                                                                                                    Caused by: android.system.ErrnoException: connect failed: ENETUNREACH (Network is unreachable)
                                                                                                    	at libcore.io.Linux.connect(Native Method)
                                                                                                    	at libcore.io.ForwardingOs.connect(ForwardingOs.java:201)
                                                                                                    	at libcore.io.BlockGuardOs.connect(BlockGuardOs.java:158)
                                                                                                    	at libcore.io.ForwardingOs.connect(ForwardingOs.java:201)
                                                                                                    	at libcore.io.IoBridge.connectErrno(IoBridge.java:218)
                                                                                                    	at libcore.io.IoBridge.connect(IoBridge.java:179)
                                                                                                    	at java.net.PlainSocketImpl.socketConnect(PlainSocketImpl.java:142) 
                                                                                                    	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:390) 
                                                                                                    	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:230) 
                                                                                                    	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:212) 
                                                                                                    	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:436) 
                                                                                                    	at java.net.Socket.connect(Socket.java:646) 
                                                                                                    	at com.neovisionaries.ws.client.SocketInitiator$SocketRacer.run(SocketInitiator.java:126) 
2025-08-18 13:15:17.167  2990-3201  WebSocket               com.dspread.mdm.service              I  🔧 reconnect() 被调用，调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onConnectError:675
2025-08-18 13:15:17.174  2990-3201  WebSocket               com.dspread.mdm.service              I  🔧 开始重连 WebSocket... (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onConnectError:675)
2025-08-18 13:15:17.182  2990-3201  WebSocket               com.dspread.mdm.service              I  🔧 使用Provisioning配置的重连策略: delayPolicy=30, delaySwitch=1, delayTime=900
2025-08-18 13:15:17.188  2990-3201  WebSocket               com.dspread.mdm.service              I  🔧 当前断开记录数: 0, 延迟开关: 1
2025-08-18 13:15:17.196  2990-3201  WebSocket               com.dspread.mdm.service              I  🔧 重连策略计算完成: 第5次重连，间隔9000ms (9秒)
2025-08-18 13:15:17.203  2990-3201  WebSocket               com.dspread.mdm.service              I  🔧 开始第5次重连，间隔9000ms (9秒) (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onConnectError:675)
2025-08-18 13:15:19.567  2990-2990  Common                  com.dspread.mdm.service              D  🔧 启动OS升级测试页面
2025-08-18 13:15:19.585   658-970   BufferQueueDebug        surfaceflinger                       E  [ActivityRecord{cfc61cd u0 com.dspread.mdm.service/.ui.activity.OsUpdateTestActivity#149](this:0xa5673c40,id:-1,api:0,p:-1,c:-1) id info cannot be read from 'ActivityRecord{cfc61cd u0 com.dspread.mdm.service/.ui.activity.OsUpdateTestActivity#149'
2025-08-18 13:15:19.633  2990-2990  AppCompatDelegate       com.dspread.mdm.service              D  Checking for metadata for AppLocalesMetadataHolderService : Service not found
2025-08-18 13:15:19.661  2990-2990  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineManager UpdateEngine初始化成功 (Android 34)
2025-08-18 13:15:19.668  2990-2990  Platform                com.dspread.mdm.service              I  ℹ️ SystemUpdateApi SystemUpdateApi initialized for Android 34
2025-08-18 13:15:19.676  2990-2990  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineUpgradeStrategy Android 14+ 强制支持UpdateEngine
2025-08-18 13:15:19.717  2990-2990  Platform                com.dspread.mdm.service              I  ℹ️ RecoverySystemUpgradeStrategy RecoverySystem支持检查: false (Android 34, A/B分区: true)
2025-08-18 13:15:19.758  2990-2990  Platform                com.dspread.mdm.service              I  ℹ️ SystemUpdateApi 升级策略支持状态: {android_version=34, android_release=14, update_engine_supported=true, recovery_system_supported=false, ab_partition=true, current_slot=_b, device_model=D30, device_manufacturer=Dspread}
2025-08-18 13:15:19.879  2990-2990  Common                  com.dspread.mdm.service              D  🔧 OsUpdateTest: 添加OS升级测试按钮...
2025-08-18 13:15:19.921  2990-2990  Common                  com.dspread.mdm.service              D  🔧 OsUpdateTest: OS升级测试按钮添加完成
2025-08-18 13:15:19.928  2990-2990  Common                  com.dspread.mdm.service              D  🔧 OsUpdateTest: 🚀 OS升级功能测试启动
2025-08-18 13:15:19.934  2990-2990  Common                  com.dspread.mdm.service              D  🔧 OsUpdateTest: 设备序列号: 01354090202503050399
2025-08-18 13:15:19.941  2990-2990  Common                  com.dspread.mdm.service              D  🔧 OsUpdateTest: OsUpdateTestActivity 初始化完成
2025-08-18 13:15:19.972  2990-2990  getAnimDeal             com.dspread.mdm.service              I  mode:3 packageName:com.dspread.mdm.service result: false
2025-08-18 13:15:19.982  2990-2990  VRI[OsUpda...tActivity] com.dspread.mdm.service              D  hardware acceleration = true, forceHwAccelerated = false
2025-08-18 13:15:19.985   658-1334  BufferQueueDebug        surfaceflinger                       E  [e60d9d0 com.dspread.mdm.service/com.dspread.mdm.service.ui.activity.OsUpdateTestActivity#150](this:0xa5636c40,id:-1,api:0,p:-1,c:-1) id info cannot be read from 'e60d9d0 com.dspread.mdm.service/com.dspread.mdm.service.ui.activity.OsUpdateTestActivity#150'
2025-08-18 13:15:19.993  2990-2990  InputTransport          com.dspread.mdm.service              D  Create ARC handle: 0x8548bb50
2025-08-18 13:15:19.993  2990-2990  InputEventReceiver      com.dspread.mdm.service              D  Input log is disabled in InputEventReceiver.
2025-08-18 13:15:19.993  2990-2990  InputTransport          com.dspread.mdm.service              D  Input log is disabled in InputChannel.
2025-08-18 13:15:20.051   658-970   BufferQueueDebug        surfaceflinger                       E  [com.dspread.mdm.service/com.dspread.mdm.service.ui.activity.OsUpdateTestActivity#151](this:0xa5630c40,id:-1,api:0,p:-1,c:-1) id info cannot be read from 'com.dspread.mdm.service/com.dspread.mdm.service.ui.activity.OsUpdateTestActivity#151'
2025-08-18 13:15:20.069  2990-2990  BufferQueueConsumer     com.dspread.mdm.service              D  [](id:bae00000001,api:0,p:-1,c:2990) connect: controlledByApp=false
2025-08-18 13:15:20.076  2990-3186  OpenGLRenderer          com.dspread.mdm.service              E  Unable to match the desired swap behavior.
2025-08-18 13:15:20.124  2990-3186  BLASTBufferQueue        com.dspread.mdm.service              D  [VRI[OsUpdateTestActivity]#1](f:0,a:1) acquireNextBufferLocked size=480x854 mFrameNumber=1 applyTransaction=true mTimestamp=75766743387(auto) mPendingTransactions.size=0 graphicBufferId=12841952215055 transform=3
2025-08-18 13:15:20.132   658-1334  BufferQueueDebug        surfaceflinger                       E  [Transition Root: ActivityRecord{cfc61cd u0 com.dspread.mdm.service/.ui.activity.OsUpdateTestActivity t10}#154](this:0xa55f7c40,id:-1,api:0,p:-1,c:-1) id info cannot be read from 'Transition Root: ActivityRecord{cfc61cd u0 com.dspread.mdm.service/.ui.activity.OsUpdateTestActivity t10}#154'
2025-08-18 13:15:20.140   658-1334  BufferQueueDebug        surfaceflinger                       E  [cf66b64 ActivityRecordInputSink com.dspread.mdm.service/.ui.activity.OsUpdateTestActivity#155](this:0xa55a3c40,id:-1,api:0,p:-1,c:-1) id info cannot be read from 'cf66b64 ActivityRecordInputSink com.dspread.mdm.service/.ui.activity.OsUpdateTestActivity#155'
2025-08-18 13:15:20.758  2990-2990  BLASTBufferQueue        com.dspread.mdm.service              D  [VRI[TestActivity]#0](f:0,a:1) destructor()
2025-08-18 13:15:20.758  2990-2990  BufferQueueConsumer     com.dspread.mdm.service              D  [VRI[TestActivity]#0(BLAST Consumer)0](id:bae00000000,api:0,p:-1,c:2990) disconnect
2025-08-18 13:15:24.873  2990-2990  Common                  com.dspread.mdm.service              D  🔧 OsUpdateTest: === 获取系统信息 ===
2025-08-18 13:15:24.889  2990-2990  Common                  com.dspread.mdm.service              D  🔧 OsUpdateTest: 获取系统信息...
2025-08-18 13:15:25.316  2990-2990  VRI[OsUpda...tActivity] com.dspread.mdm.service              D  hardware acceleration = true, forceHwAccelerated = false
2025-08-18 13:15:25.322   658-1335  BufferQueueDebug        surfaceflinger                       E  [e8215fb com.dspread.mdm.service/com.dspread.mdm.service.ui.activity.OsUpdateTestActivity#163](this:0xa5642c40,id:-1,api:0,p:-1,c:-1) id info cannot be read from 'e8215fb com.dspread.mdm.service/com.dspread.mdm.service.ui.activity.OsUpdateTestActivity#163'
2025-08-18 13:15:25.350  2990-2990  InputTransport          com.dspread.mdm.service              D  Create ARC handle: 0xae3c21e0
2025-08-18 13:15:25.350  2990-2990  InputEventReceiver      com.dspread.mdm.service              D  Input log is disabled in InputEventReceiver.
2025-08-18 13:15:25.350  2990-2990  InputTransport          com.dspread.mdm.service              D  Input log is disabled in InputChannel.
2025-08-18 13:15:25.354  2990-2990  Common                  com.dspread.mdm.service              D  🔧 OsUpdateTest: 系统信息获取成功
2025-08-18 13:15:25.558   658-1335  BufferQueueDebug        surfaceflinger                       E  [com.dspread.mdm.service/com.dspread.mdm.service.ui.activity.OsUpdateTestActivity#166](this:0xa55f7c40,id:-1,api:0,p:-1,c:-1) id info cannot be read from 'com.dspread.mdm.service/com.dspread.mdm.service.ui.activity.OsUpdateTestActivity#166'
2025-08-18 13:15:25.583  2990-2990  BufferQueueConsumer     com.dspread.mdm.service              D  [](id:bae00000002,api:0,p:-1,c:2990) connect: controlledByApp=false
2025-08-18 13:15:25.601  2990-3186  OpenGLRenderer          com.dspread.mdm.service              E  Unable to match the desired swap behavior.
2025-08-18 13:15:25.714  2990-3186  BLASTBufferQueue        com.dspread.mdm.service              D  [VRI[OsUpdateTestActivity]#2](f:0,a:1) acquireNextBufferLocked size=542x844 mFrameNumber=1 applyTransaction=true mTimestamp=81356068003(auto) mPendingTransactions.size=0 graphicBufferId=12841952215065 transform=3
2025-08-18 13:15:25.723  2990-3199  OpenGLRenderer          com.dspread.mdm.service              I  Davey! duration=781ms; Flags=1, FrameTimelineVsyncId=10311, IntendedVsync=80566913007, Vsync=81001101125, InputEventId=0, HandleInputStart=81015172695, AnimationStart=81015209619, PerformTraversalsStart=81018587157, DrawStart=81292870234, FrameDeadline=80592413007, FrameInterval=81014084311, FrameStartTime=16666667, SyncQueued=81326563080, SyncStart=81343515465, IssueDrawCommandsStart=81343708003, SwapBuffers=81355598465, FrameCompleted=81365580388, DequeueBufferDuration=0, QueueBufferDuration=1242154, GpuCompleted=81365580388, SwapBuffersCompleted=81357624695, DisplayPresentTime=0, CommandSubmissionCompleted=81355598465, 
2025-08-18 13:15:25.728   658-970   BufferQueueDebug        surfaceflinger                       E  [Surface(name=e8215fb com.dspread.mdm.service/com.dspread.mdm.service.ui.activity.OsUpdateTestActivity)/@0x6b28a2e - animation-leash of window_animation#167](this:0xa559ec40,id:-1,api:0,p:-1,c:-1) id info cannot be read from 'Surface(name=e8215fb com.dspread.mdm.service/com.dspread.mdm.service.ui.activity.OsUpdateTestActivity)/@0x6b28a2e - animation-leash of window_animation#167'
2025-08-18 13:15:26.224  2990-2990  WebSocket               com.dspread.mdm.service              I  🔧 重连 WebSocket: ws://***********:8080/status/websocket/register?serialNo=MDEzNTQwOTAyMDI1MDMwNTAzOTk=&publicKey=TUlHZk1BMEdDU3FHU0liM0RRRUJBUVVBQTRHTkFEQ0JpUUtCZ1FEUkhsaTBCQWtXZm12cjZTdkdlbHFEWjNGc0I2a0d3UzNyN1ZzZzFiMWJvcDl3RmtRWjlWc1E1MlNnYlpFTjNWeGtUaFBISUFSeTFvSEhmQVNmK2VzZkc1TEErcExDZ3dlNjErdzljWWdFZ2JDSHZrRTY2eGRSNmJXNXgrTHNtT24rdjhSYmVMcWMyZTlyWlNrbTJjV0VuWmpENDZNYmlPaVhORkkrditJcWZ3SURBUUFC&query=0&msgVer=3&timestamp=1755494126211&signature=vQhMT4irN6zepzpw/G21Nv6LUxwVQDl9mPuKT62bmS4nIfWdGgSGEXwZZe/MUmAinwN8PQ4DK9KphVGCiMpVzEdDgqHJIQkA6ETo8AXNSUXAYGVpLvmjlTYMb56+FOeyZ6t0fmC6HccH4j+YWhE8MQXap4T+P6GyA/Zx36y4f9Q=
2025-08-18 13:15:26.231  2990-2990  WebSocket               com.dspread.mdm.service              I  🔧 未检测到系统代理设置
2025-08-18 13:15:26.253  2990-3214  TrafficStats            com.dspread.mdm.service              D  tagSocket(155) with statsTag=0xffffffff, statsUid=-1
2025-08-18 13:15:26.267  2990-3213  WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误 (Ask Gemini)
                                                                                                    com.neovisionaries.ws.client.WebSocketException: Failed to connect to '***********:8080': failed to connect to /*********** (port 8080) from /:: (port 0) after 15000ms: connect failed: ENETUNREACH (Network is unreachable)
                                                                                                    	at com.neovisionaries.ws.client.SocketConnector.connectSocket(SocketConnector.java:126)
                                                                                                    	at com.neovisionaries.ws.client.SocketConnector.doConnect(SocketConnector.java:238)
                                                                                                    	at com.neovisionaries.ws.client.SocketConnector.connect(SocketConnector.java:189)
                                                                                                    	at com.neovisionaries.ws.client.WebSocket.connect(WebSocket.java:2351)
                                                                                                    	at com.neovisionaries.ws.client.ConnectThread.runMain(ConnectThread.java:32)
                                                                                                    	at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45)
                                                                                                    Caused by: java.net.ConnectException: failed to connect to /*********** (port 8080) from /:: (port 0) after 15000ms: connect failed: ENETUNREACH (Network is unreachable)
                                                                                                    	at libcore.io.IoBridge.connect(IoBridge.java:187)
                                                                                                    	at java.net.PlainSocketImpl.socketConnect(PlainSocketImpl.java:142)
                                                                                                    	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:390)
                                                                                                    	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:230)
                                                                                                    	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:212)
                                                                                                    	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:436)
                                                                                                    	at java.net.Socket.connect(Socket.java:646)
                                                                                                    	at com.neovisionaries.ws.client.SocketInitiator$SocketRacer.run(SocketInitiator.java:126)
                                                                                                    Caused by: android.system.ErrnoException: connect failed: ENETUNREACH (Network is unreachable)
                                                                                                    	at libcore.io.Linux.connect(Native Method)
                                                                                                    	at libcore.io.ForwardingOs.connect(ForwardingOs.java:201)
                                                                                                    	at libcore.io.BlockGuardOs.connect(BlockGuardOs.java:158)
                                                                                                    	at libcore.io.ForwardingOs.connect(ForwardingOs.java:201)
                                                                                                    	at libcore.io.IoBridge.connectErrno(IoBridge.java:218)
                                                                                                    	at libcore.io.IoBridge.connect(IoBridge.java:179)
                                                                                                    	at java.net.PlainSocketImpl.socketConnect(PlainSocketImpl.java:142) 
                                                                                                    	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:390) 
                                                                                                    	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:230) 
                                                                                                    	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:212) 
                                                                                                    	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:436) 
                                                                                                    	at java.net.Socket.connect(Socket.java:646) 
                                                                                                    	at com.neovisionaries.ws.client.SocketInitiator$SocketRacer.run(SocketInitiator.java:126) 
2025-08-18 13:15:26.279  2990-3213  WebSocket               com.dspread.mdm.service              I  🔧 onError: 不触发重连，等待 onDisconnected 处理
2025-08-18 13:15:26.299  2990-3213  WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误 (Ask Gemini)
                                                                                                    com.neovisionaries.ws.client.WebSocketException: Failed to connect to '***********:8080': failed to connect to /*********** (port 8080) from /:: (port 0) after 15000ms: connect failed: ENETUNREACH (Network is unreachable)
                                                                                                    	at com.neovisionaries.ws.client.SocketConnector.connectSocket(SocketConnector.java:126)
                                                                                                    	at com.neovisionaries.ws.client.SocketConnector.doConnect(SocketConnector.java:238)
                                                                                                    	at com.neovisionaries.ws.client.SocketConnector.connect(SocketConnector.java:189)
                                                                                                    	at com.neovisionaries.ws.client.WebSocket.connect(WebSocket.java:2351)
                                                                                                    	at com.neovisionaries.ws.client.ConnectThread.runMain(ConnectThread.java:32)
                                                                                                    	at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45)
                                                                                                    Caused by: java.net.ConnectException: failed to connect to /*********** (port 8080) from /:: (port 0) after 15000ms: connect failed: ENETUNREACH (Network is unreachable)
                                                                                                    	at libcore.io.IoBridge.connect(IoBridge.java:187)
                                                                                                    	at java.net.PlainSocketImpl.socketConnect(PlainSocketImpl.java:142)
                                                                                                    	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:390)
                                                                                                    	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:230)
                                                                                                    	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:212)
                                                                                                    	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:436)
                                                                                                    	at java.net.Socket.connect(Socket.java:646)
                                                                                                    	at com.neovisionaries.ws.client.SocketInitiator$SocketRacer.run(SocketInitiator.java:126)
                                                                                                    Caused by: android.system.ErrnoException: connect failed: ENETUNREACH (Network is unreachable)
                                                                                                    	at libcore.io.Linux.connect(Native Method)
                                                                                                    	at libcore.io.ForwardingOs.connect(ForwardingOs.java:201)
                                                                                                    	at libcore.io.BlockGuardOs.connect(BlockGuardOs.java:158)
                                                                                                    	at libcore.io.ForwardingOs.connect(ForwardingOs.java:201)
                                                                                                    	at libcore.io.IoBridge.connectErrno(IoBridge.java:218)
                                                                                                    	at libcore.io.IoBridge.connect(IoBridge.java:179)
                                                                                                    	at java.net.PlainSocketImpl.socketConnect(PlainSocketImpl.java:142) 
                                                                                                    	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:390) 
                                                                                                    	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:230) 
                                                                                                    	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:212) 
                                                                                                    	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:436) 
                                                                                                    	at java.net.Socket.connect(Socket.java:646) 
                                                                                                    	at com.neovisionaries.ws.client.SocketInitiator$SocketRacer.run(SocketInitiator.java:126) 
2025-08-18 13:15:26.309  2990-3213  WebSocket               com.dspread.mdm.service              I  🔧 reconnect() 被调用，调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onConnectError:675
2025-08-18 13:15:26.317  2990-3213  WebSocket               com.dspread.mdm.service              I  🔧 开始重连 WebSocket... (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onConnectError:675)
2025-08-18 13:15:26.325  2990-3213  WebSocket               com.dspread.mdm.service              I  🔧 使用Provisioning配置的重连策略: delayPolicy=30, delaySwitch=1, delayTime=900
2025-08-18 13:15:26.331  2990-3213  WebSocket               com.dspread.mdm.service              I  🔧 当前断开记录数: 0, 延迟开关: 1
2025-08-18 13:15:26.338  2990-3213  WebSocket               com.dspread.mdm.service              I  🔧 重连策略计算完成: 第6次重连，间隔12000ms (12秒)
2025-08-18 13:15:26.346  2990-3213  WebSocket               com.dspread.mdm.service              I  🔧 开始第6次重连，间隔12000ms (12秒) (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onConnectError:675)
2025-08-18 13:15:27.122  2990-2990  WindowOnBackDispatcher  com.dspread.mdm.service              W  sendCancelIfRunning: isInProgress=falsecallback=android.view.ViewRootImpl$$ExternalSyntheticLambda19@8eafc87
2025-08-18 13:15:27.135  2990-3186  OpenGLRenderer          com.dspread.mdm.service              D  endAllActiveAnimators on 0x851a3a00 (RippleDrawable) with handle 0xae390ac0
2025-08-18 13:15:27.137  2990-2990  View                    com.dspread.mdm.service              D  [Warning] assignParent to null: this = DecorView@35c9213[OsUpdateTestActivity]
2025-08-18 13:15:27.140   658-1334  BufferQueueDebug        surfaceflinger                       E  [Surface(name=e8215fb com.dspread.mdm.service/com.dspread.mdm.service.ui.activity.OsUpdateTestActivity)/@0x6b28a2e - animation-leash of window_animation#170](this:0xa5607c40,id:-1,api:0,p:-1,c:-1) id info cannot be read from 'Surface(name=e8215fb com.dspread.mdm.service/com.dspread.mdm.service.ui.activity.OsUpdateTestActivity)/@0x6b28a2e - animation-leash of window_animation#170'
2025-08-18 13:15:27.151  2990-2990  InputTransport          com.dspread.mdm.service              D  Destroy ARC handle: 0xae3c21e0
2025-08-18 13:15:27.161  2990-3012  BLASTBufferQueue        com.dspread.mdm.service              D  [VRI[OsUpdateTestActivity]#2](f:0,a:1) destructor()
2025-08-18 13:15:27.161  2990-3012  BufferQueueConsumer     com.dspread.mdm.service              D  [VRI[OsUpdateTestActivity]#2(BLAST Consumer)2](id:bae00000002,api:0,p:-1,c:2990) disconnect
2025-08-18 13:15:27.921  2990-2990  Common                  com.dspread.mdm.service              D  🔧 OsUpdateTest: === 选择升级包 ===
2025-08-18 13:15:29.609  2990-3186  GED                     com.dspread.mdm.service              I  ged_boost_gpu_freq, level 100, eOrigin 2, final_idx 2, oppidx_max 2, oppidx_min 0
2025-08-18 13:15:29.638  2990-2990  BLASTBufferQueue        com.dspread.mdm.service              D  [VRI[OsUpdateTestActivity]#1](f:0,a:1) destructor()
2025-08-18 13:15:29.638  2990-2990  BufferQueueConsumer     com.dspread.mdm.service              D  [VRI[OsUpdateTestActivity]#1(BLAST Consumer)1](id:bae00000001,api:0,p:-1,c:2990) disconnect
2025-08-18 13:15:29.644  2990-3186  GED                     com.dspread.mdm.service              I  ged_boost_gpu_freq, level 100, eOrigin 2, final_idx 2, oppidx_max 2, oppidx_min 0
2025-08-18 13:15:30.603  2990-2990  Compatibil...geReporter com.dspread.mdm.service              D  Compat change id reported: 78294732; UID 1000; state: ENABLED
2025-08-18 13:15:30.628  2990-2990  Common                  com.dspread.mdm.service              D  🔧 OsUpdateTest: 无法获取直接路径，复制文件到临时位置
2025-08-18 13:15:30.763  2990-2990  Common                  com.dspread.mdm.service              D  🔧 OsUpdateTest: 文件复制成功: BASE_D30M-MU_GEN_AP_V1.0.8.1_202507032000_ota_test.zip
2025-08-18 13:15:30.776  2990-2990  Common                  com.dspread.mdm.service              D  🔧 OsUpdateTest: 已选择升级包: /data/user/0/com.dspread.mdm.service/cache/BASE_D30M-MU_GEN_AP_V1.0.8.1_202507032000_ota_test.zip
2025-08-18 13:15:30.787  2990-2990  Compatibil...geReporter com.dspread.mdm.service              D  Compat change id reported: 147798919; UID 1000; state: ENABLED
2025-08-18 13:15:30.795  2990-2990  getAnimDeal             com.dspread.mdm.service              I  mode:3 packageName:com.dspread.mdm.service result: false
2025-08-18 13:15:30.809   658-970   BufferQueueDebug        surfaceflinger                       E  [com.dspread.mdm.service/com.dspread.mdm.service.ui.activity.OsUpdateTestActivity#190](this:0xa5630c40,id:-1,api:0,p:-1,c:-1) id info cannot be read from 'com.dspread.mdm.service/com.dspread.mdm.service.ui.activity.OsUpdateTestActivity#190'
2025-08-18 13:15:30.827  2990-2990  BufferQueueConsumer     com.dspread.mdm.service              D  [](id:bae00000003,api:0,p:-1,c:2990) connect: controlledByApp=false
2025-08-18 13:15:30.832  2990-3186  OpenGLRenderer          com.dspread.mdm.service              E  Unable to match the desired swap behavior.
2025-08-18 13:15:30.864  2990-3186  BLASTBufferQueue        com.dspread.mdm.service              D  [VRI[OsUpdateTestActivity]#3](f:0,a:1) acquireNextBufferLocked size=480x854 mFrameNumber=1 applyTransaction=true mTimestamp=86505455157(auto) mPendingTransactions.size=0 graphicBufferId=12841952215075 transform=3
2025-08-18 13:15:30.886   658-970   BufferQueueDebug        surfaceflinger                       E  [cf66b64 ActivityRecordInputSink com.dspread.mdm.service/.ui.activity.OsUpdateTestActivity#196](this:0xa559ac40,id:-1,api:0,p:-1,c:-1) id info cannot be read from 'cf66b64 ActivityRecordInputSink com.dspread.mdm.service/.ui.activity.OsUpdateTestActivity#196'
2025-08-18 13:15:35.939  2990-2990  Common                  com.dspread.mdm.service              D  🔧 OsUpdateTest: === 执行OTA升级 ===
2025-08-18 13:15:35.950  2990-2990  Common                  com.dspread.mdm.service              D  🔧 OsUpdateTest: 开始执行OTA升级...
2025-08-18 13:15:35.964  2990-2990  Common                  com.dspread.mdm.service              D  🔧 OsUpdateTest: 注意：这将开始系统升级过程
2025-08-18 13:15:36.020  2990-2990  Common                  com.dspread.mdm.service              D  🔧 OsUpdateTest: 使用用户选择的升级包: /data/user/0/com.dspread.mdm.service/cache/BASE_D30M-MU_GEN_AP_V1.0.8.1_202507032000_ota_test.zip
2025-08-18 13:15:36.038  2990-2990  Common                  com.dspread.mdm.service              D  🔧 OsUpdateTest: 找到升级文件: /data/user/0/com.dspread.mdm.service/cache/BASE_D30M-MU_GEN_AP_V1.0.8.1_202507032000_ota_test.zip
2025-08-18 13:15:36.054  2990-2990  Common                  com.dspread.mdm.service              D  🔧 OsUpdateTest: 文件名: BASE_D30M-MU_GEN_AP_V1.0.8.1_202507032000_ota_test.zip
2025-08-18 13:15:36.069  2990-2990  Common                  com.dspread.mdm.service              D  🔧 OsUpdateTest: 文件大小: 2MB
2025-08-18 13:15:36.082  2990-2990  Common                  com.dspread.mdm.service              D  🔧 OsUpdateTest: 升级策略: A/B分区无缝升级 (UpdateEngine)
2025-08-18 13:15:36.099  2990-2990  Common                  com.dspread.mdm.service              D  🔧 OsUpdateTest: 升级进度: 准备阶段: 检查系统版本 (5%)
2025-08-18 13:15:36.113  2990-2990  Common                  com.dspread.mdm.service              D  🔧 OsUpdateTest: 当前系统版本: BASE_D30M-MU_GEN_AP_V1.0.8.1_202507032000_ota_test
2025-08-18 13:15:36.129  2990-2990  Common                  com.dspread.mdm.service              D  🔧 OsUpdateTest: Android 14+设备，执行升级前检查...
2025-08-18 13:15:36.144  2990-2990  Common                  com.dspread.mdm.service              D  🔧 OsUpdateTest: 注意：Android 14+对升级包时间戳有严格要求
2025-08-18 13:15:36.164  2990-2990  Common                  com.dspread.mdm.service              D  🔧 OsUpdateTest: 升级进度: 准备阶段: Android 14+升级检查 (8%)
2025-08-18 13:15:36.182  2990-2990  Common                  com.dspread.mdm.service              D  🔧 OsUpdateTest: 升级进度: 准备阶段: 验证升级包 (10%)
2025-08-18 13:15:36.201  2990-2990  Common                  com.dspread.mdm.service              D  🔧 OsUpdateTest: 升级包验证通过: BASE_D30M-MU_GEN_AP_V1.0.8.1_202507032000_ota_test.zip, 大小: 2MB
2025-08-18 13:15:36.218  2990-2990  Common                  com.dspread.mdm.service              D  🔧 OsUpdateTest: 升级进度: 准备阶段: 升级包验证通过 (15%)
2025-08-18 13:15:36.232  2990-2990  Common                  com.dspread.mdm.service              D  🔧 OsUpdateTest: 升级进度: 准备阶段: 检查版本兼容性 (18%)
2025-08-18 13:15:36.252  2990-2990  Platform                com.dspread.mdm.service              I  ℹ️ SystemUpdateApi 开始分析升级包: BASE_D30M-MU_GEN_AP_V1.0.8.1_202507032000_ota_test.zip
2025-08-18 13:15:36.270  2990-2990  Platform                com.dspread.mdm.service              I  ℹ️ SystemUpdateApi 检测到A/B升级包格式
2025-08-18 13:15:36.294  2990-2990  Platform                com.dspread.mdm.service              I  ℹ️ SystemUpdateApi 从build信息提取版本: 1.0.8.1_202507032000
2025-08-18 13:15:36.308  2990-2990  Platform                com.dspread.mdm.service              I  ℹ️ SystemUpdateApi A14+格式提取版本: 1.0.8.1_202507032000
2025-08-18 13:15:36.323  2990-2990  Platform                com.dspread.mdm.service              I  ℹ️ SystemUpdateApi 版本比较: 当前=BASE_D30M-MU_GEN_AP_V1.0.8.1_202507032000_ota_test, 升级包=1.0.8.1_202507032000
2025-08-18 13:15:36.339  2990-2990  Platform                com.dspread.mdm.service              I  ℹ️ SystemUpdateApi 详细版本比较: 'BASE_D30M-MU_GEN_AP_V1.0.8.1_202507032000_ota_test' vs '1.0.8.1_202507032000'
2025-08-18 13:15:36.348  2990-2990  Platform                com.dspread.mdm.service              I  ℹ️ SystemUpdateApi 清理版本字符串: 'BASE_D30M-MU_GEN_AP_V1.0.8.1_202507032000_ota_test'
2025-08-18 13:15:36.358  2990-2990  Platform                com.dspread.mdm.service              I  ℹ️ SystemUpdateApi 提取完整版本: '1.0.8.1_202507032000'
2025-08-18 13:15:36.365  2990-2990  Platform                com.dspread.mdm.service              I  ℹ️ SystemUpdateApi 清理版本字符串: '1.0.8.1_202507032000'
2025-08-18 13:15:36.374  2990-2990  Platform                com.dspread.mdm.service              I  ℹ️ SystemUpdateApi 提取完整版本: '1.0.8.1_202507032000'
2025-08-18 13:15:36.383  2990-2990  Platform                com.dspread.mdm.service              I  ℹ️ SystemUpdateApi 清理后版本: '1.0.8.1_202507032000' vs '1.0.8.1_202507032000'
2025-08-18 13:15:36.390  2990-2990  Platform                com.dspread.mdm.service              I  ℹ️ SystemUpdateApi 清理后版本相同
2025-08-18 13:15:36.398  2990-2990  Common                  com.dspread.mdm.service              D  🔧 OsUpdateTest: 版本信息显示: 版本相同
2025-08-18 13:15:36.406  2990-2990  Common                  com.dspread.mdm.service              D  🔧 OsUpdateTest: 同版本升级被阻止：A/B分区机制不支持相同版本重复安装
2025-08-18 13:15:36.414  2990-2990  Common                  com.dspread.mdm.service              D  🔧 OsUpdateTest: 升级进度: 阻止: 同版本升级不支持 (0%)
2025-08-18 13:15:36.569  2990-2990  VRI[OsUpda...tActivity] com.dspread.mdm.service              D  hardware acceleration = true, forceHwAccelerated = false
2025-08-18 13:15:36.573   658-1335  BufferQueueDebug        surfaceflinger                       E  [d644d44 com.dspread.mdm.service/com.dspread.mdm.service.ui.activity.OsUpdateTestActivity#204](this:0xa55fbc40,id:-1,api:0,p:-1,c:-1) id info cannot be read from 'd644d44 com.dspread.mdm.service/com.dspread.mdm.service.ui.activity.OsUpdateTestActivity#204'
2025-08-18 13:15:36.586  2990-2990  InputTransport          com.dspread.mdm.service              D  Create ARC handle: 0xae14b0b0
2025-08-18 13:15:36.586  2990-2990  InputEventReceiver      com.dspread.mdm.service              D  Input log is disabled in InputEventReceiver.
2025-08-18 13:15:36.586  2990-2990  InputTransport          com.dspread.mdm.service              D  Input log is disabled in InputChannel.
2025-08-18 13:15:36.589  2990-2990  Choreographer           com.dspread.mdm.service              I  Skipped 38 frames!  The application may be doing too much work on its main thread.
2025-08-18 13:15:36.725  2990-3194  OpenGLRenderer          com.dspread.mdm.service              I  Davey! duration=743ms; Flags=0, FrameTimelineVsyncId=14022, IntendedVsync=91585831384, Vsync=92219164730, InputEventId=0, HandleInputStart=92234067465, AnimationStart=92234092312, PerformTraversalsStart=92236275465, DrawStart=92259557619, FrameDeadline=92335024270, FrameInterval=92232024927, FrameStartTime=16666667, SyncQueued=92277127773, SyncStart=92287423465, IssueDrawCommandsStart=92287839158, SwapBuffers=92336545235, FrameCompleted=92340053312, DequeueBufferDuration=13603769, QueueBufferDuration=497538, GpuCompleted=92340053312, SwapBuffersCompleted=92337864619, DisplayPresentTime=83613192003, CommandSubmissionCompleted=92336545235, 
2025-08-18 13:15:36.733   658-1335  BufferQueueDebug        surfaceflinger                       E  [com.dspread.mdm.service/com.dspread.mdm.service.ui.activity.OsUpdateTestActivity#207](this:0xa55a3c40,id:-1,api:0,p:-1,c:-1) id info cannot be read from 'com.dspread.mdm.service/com.dspread.mdm.service.ui.activity.OsUpdateTestActivity#207'
2025-08-18 13:15:36.746  2990-2990  BufferQueueConsumer     com.dspread.mdm.service              D  [](id:bae00000004,api:0,p:-1,c:2990) connect: controlledByApp=false
2025-08-18 13:15:36.753  2990-3186  OpenGLRenderer          com.dspread.mdm.service              E  Unable to match the desired swap behavior.
2025-08-18 13:15:36.829  2990-3186  BLASTBufferQueue        com.dspread.mdm.service              D  [VRI[OsUpdateTestActivity]#4](f:0,a:1) acquireNextBufferLocked size=542x643 mFrameNumber=1 applyTransaction=true mTimestamp=92471242850(auto) mPendingTransactions.size=0 graphicBufferId=12841952215085 transform=3
2025-08-18 13:15:36.834   658-1334  BufferQueueDebug        surfaceflinger                       E  [Surface(name=d644d44 com.dspread.mdm.service/com.dspread.mdm.service.ui.activity.OsUpdateTestActivity)/@0xe6195e5 - animation-leash of window_animation#208](this:0xa55a1c40,id:-1,api:0,p:-1,c:-1) id info cannot be read from 'Surface(name=d644d44 com.dspread.mdm.service/com.dspread.mdm.service.ui.activity.OsUpdateTestActivity)/@0xe6195e5 - animation-leash of window_animation#208'
2025-08-18 13:15:36.844  2990-3015  OpenGLRenderer          com.dspread.mdm.service              I  Davey! duration=876ms; Flags=1, FrameTimelineVsyncId=14022, IntendedVsync=91585831384, Vsync=92219164730, InputEventId=0, HandleInputStart=92234067465, AnimationStart=92234092312, PerformTraversalsStart=92236275465, DrawStart=92423759696, FrameDeadline=91611331384, FrameInterval=92232024927, FrameStartTime=16666667, SyncQueued=92445804388, SyncStart=92462368004, IssueDrawCommandsStart=92462536004, SwapBuffers=92470603081, FrameCompleted=92479114773, DequeueBufferDuration=0, QueueBufferDuration=776000, GpuCompleted=92479114773, SwapBuffersCompleted=92472545619, DisplayPresentTime=0, CommandSubmissionCompleted=92470603081, 
