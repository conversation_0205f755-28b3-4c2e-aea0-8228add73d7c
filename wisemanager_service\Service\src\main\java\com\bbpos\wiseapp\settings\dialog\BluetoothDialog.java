/*
 * Copyright (C) 2010 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.bbpos.wiseapp.settings.dialog;

import android.app.AlertDialog;
import android.bluetooth.BluetoothDevice;
import android.content.Context;
import android.net.wifi.ScanResult;
import android.os.Bundle;
import android.text.Editable;
import android.text.InputType;
import android.text.TextWatcher;
import android.view.View;
import android.widget.CheckBox;
import android.widget.CompoundButton;
import android.widget.EditText;
import android.widget.TextView;

import com.bbpos.wiseapp.service.R;

public class BluetoothDialog extends AlertDialog implements View.OnClickListener {
    public interface BluetoothDialogListener {
        void onForget();
        void onSubmit(ScanResult scanResult, String password);
    }

    private Context mContext;
    private final BluetoothDialogListener mListener;
    private BluetoothDevice mDevice;

    private View mView;

    private TextView tv_unbond;
    private TextView tv_done;


    public BluetoothDialog(Context context, BluetoothDialogListener listener, BluetoothDevice device) {
        super(context);
        mListener = listener;
        mContext = context;
        mDevice = device;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        mView = getLayoutInflater().inflate(R.layout.dialog_bluetooth_bonded, null);
        setView(mView);
        setInverseBackgroundForced(true);
        super.onCreate(savedInstanceState);

        EditText et = findViewById(R.id.et_btdevice);
        et.setText(mDevice.getName());
        et.setSelection(et.length());

        tv_unbond = findViewById(R.id.tv_unbond);
        tv_done = findViewById(R.id.tv_done);
        tv_unbond.setOnClickListener(this);
        tv_done.setOnClickListener(this);
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.tv_unbond:
                mListener.onForget();
                dismiss();
                break;
            case R.id.tv_done:
                dismiss();
                break;
        }
    }
}
