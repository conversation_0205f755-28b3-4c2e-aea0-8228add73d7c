package com.dspread.mdm.service.modules.rulebase

import android.content.Context
import android.content.SharedPreferences
import com.dspread.mdm.service.modules.rulebase.model.Rule
import com.dspread.mdm.service.modules.rulebase.model.RuleStatus
import com.dspread.mdm.service.utils.log.Logger
import kotlinx.coroutines.*
import org.json.JSONArray
import org.json.JSONObject
import java.util.concurrent.locks.ReentrantReadWriteLock
import kotlin.concurrent.read
import kotlin.concurrent.write

/**
 * RuleBase存储管理器
 * 负责规则的持久化、版本控制和数据一致性
 */
object RuleBaseStorage {
    
    private const val TAG = "RuleBaseStorage"
    private const val SP_NAME = "rulebase_storage"
    private const val SP_KEY_RULE_LIST = "rulebase_rule_list"
    private const val SP_KEY_EXECUTED_RULES = "rulebase_executed_rules"
    
    private var sharedPreferences: SharedPreferences? = null
    private var appContext: Context? = null
    private val ruleLock = ReentrantReadWriteLock()

    // 规则执行专用协程作用域
    private val ruleExecutionScope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    
    /**
     * 初始化存储管理器
     * 使用ApplicationContext避免内存泄漏
     */
    fun init(context: Context) {
        appContext = context.applicationContext
        sharedPreferences = appContext?.getSharedPreferences(SP_NAME, Context.MODE_PRIVATE)
        Logger.rule("$TAG RuleBase存储管理器初始化完成")
    }
    
    /**
     * 处理规则 - 核心逻辑
     * Add操作检查重复，Modify操作直接忽略
     */
    fun processRule(ruleData: JSONObject): RuleProcessResult {
        return ruleLock.write {
            try {
                val rule = Rule.fromJson(ruleData) 
                    ?: return RuleProcessResult.error("规则数据解析失败")
                
                Logger.rule("$TAG 处理规则: ${rule.ruleId}, 操作: ${rule.action}")
                
                // 1. 数据验证
                val validation = rule.validate()
                if (!validation.isSuccess) {
                    return RuleProcessResult.error("规则验证失败: ${validation.message}")
                }
                
                // 2. 处理不同操作类型（支持简化和完整的action值）
                when (rule.action) {
                    "Add", "A" -> handleAddRule(rule)
                    "Modify", "M" -> handleModifyRule(rule)
                    "Delete", "D" -> handleDeleteRule(rule)
                    else -> RuleProcessResult.error("未知操作类型: ${rule.action}")
                }
                
            } catch (e: Exception) {
                Logger.ruleE("$TAG 处理规则失败", e)
                RuleProcessResult.error("处理规则异常: ${e.message}")
            }
        }
    }
    
    /**
     * 处理Add操作 - 严格的重复检查
     * 只允许添加一次，不允许覆盖
     */
    private fun handleAddRule(rule: Rule): RuleProcessResult {
        // 检查规则是否已存在
        if (isRuleExists(rule.ruleId)) {
            Logger.rule("$TAG Rule ${rule.ruleId} 已存在，忽略Add操作")
            return RuleProcessResult.ignored("规则已存在，忽略添加")
        }
        
        // 检查是否已执行过
        if (isRuleExecuted(rule.ruleId)) {
            Logger.rule("$TAG Rule ${rule.ruleId} 已执行过，忽略Add操作")
            return RuleProcessResult.ignored("规则已执行过，忽略添加")
        }
        
        // 添加新规则
        val success = addNewRule(rule)
        return if (success) {
            Logger.rule("$TAG Rule ${rule.ruleId} 添加成功")

            // 规则添加成功后立即触发执行
            triggerRuleExecution(rule)

            RuleProcessResult.success("规则添加成功")
        } else {
            RuleProcessResult.error("规则添加失败")
        }
    }
    
    /**
     * 处理Modify操作 - 根据需求直接忽略
     * 完全忽略Modify操作，不做任何处理
     */
    private fun handleModifyRule(rule: Rule): RuleProcessResult {
        Logger.rule("$TAG Rule ${rule.ruleId} 收到Modify操作，根据设计要求忽略")
        return RuleProcessResult.ignored("根据设计要求忽略Modify操作")
    }
    
    /**
     * 处理Delete操作
     */
    private fun handleDeleteRule(rule: Rule): RuleProcessResult {
        return try {
            Logger.rule("$TAG 开始删除规则: ${rule.ruleId}")

            // 检查规则是否存在
            val existingRules = getAllRules()
            val ruleExists = existingRules.any { it.ruleId == rule.ruleId }

            if (!ruleExists) {
                // 规则不存在，视为删除成功
                Logger.rule("$TAG 规则不存在，视为删除成功: ${rule.ruleId}")
                return RuleProcessResult.Success("规则不存在，视为删除成功")
            }

            // 规则存在，执行删除操作
            val updatedRules = existingRules.filter { it.ruleId != rule.ruleId }
            saveRules(updatedRules)

            // 清理执行引擎状态
            cleanupExecutionEngineState(rule.ruleId)

            Logger.rule("$TAG 规则删除成功: ${rule.ruleId}")
            RuleProcessResult.Success("规则删除成功")

        } catch (e: Exception) {
            Logger.ruleE("$TAG 删除规则失败: ${rule.ruleId}", e)
            RuleProcessResult.Error("删除规则失败: ${e.message}")
        }
    }
    
    /**
     * 清理执行引擎中的规则状态
     */
    private fun cleanupExecutionEngineState(ruleId: String) {
        try {
            // 获取RuleBaseManager实例并清理执行状态
            val context = appContext
            if (context != null) {
                val ruleBaseManager = RuleBaseManager.getInstance(context)
                val executionEngine = ruleBaseManager.getExecutionEngine()
                val stateMachine = ruleBaseManager.getStateMachine()
                
                // 停止规则执行
                executionEngine.stopRuleExecution(ruleId)
                
                // 清理规则执行状态
                executionEngine.cleanupRuleExecution(ruleId)
                
                // 清理状态机中的规则状态
                stateMachine.cleanupRule(ruleId, true) // 强制清理
                
                Logger.rule("$TAG 执行引擎和状态机状态清理完成: $ruleId")
            }
        } catch (e: Exception) {
            Logger.ruleE("$TAG 清理执行引擎状态失败: $ruleId", e)
        }
    }
    
    /**
     * 获取所有有效规则
     */
    fun getAllValidRules(): List<Rule> {
        return ruleLock.read {
            try {
                val ruleListJson = sharedPreferences?.getString(SP_KEY_RULE_LIST, "[]") ?: "[]"
                val ruleArray = JSONArray(ruleListJson)
                val rules = mutableListOf<Rule>()
                
                for (i in 0 until ruleArray.length()) {
                    val ruleJson = ruleArray.getJSONObject(i)
                    Rule.fromJson(ruleJson)?.let { rule ->
                        // 只返回活跃状态的规则
                        if (rule.ruleStatus.isActiveState()) {
                            rules.add(rule)
                        }
                    }
                }
                
                Logger.rule("$TAG 获取到 ${rules.size} 个有效规则")
                rules
            } catch (e: Exception) {
                Logger.ruleE("$TAG 获取规则列表失败", e)
                emptyList()
            }
        }
    }
    
    /**
     * 更新规则状态 - 带状态转换验证
     */
    fun updateRuleStatus(ruleId: String, newStatus: RuleStatus): Boolean {
        return ruleLock.write {
            try {
                val rules = getAllRules().toMutableList()
                val ruleIndex = rules.indexOfFirst { it.ruleId == ruleId }
                
                if (ruleIndex == -1) {
                    Logger.ruleW("$TAG 规则不存在: $ruleId")
                    return@write false
                }
                
                val currentRule = rules[ruleIndex]
                
                // 验证状态转换是否合法
                if (!currentRule.ruleStatus.canTransitionTo(newStatus)) {
                    Logger.ruleW("$TAG 非法状态转换: ${currentRule.ruleStatus} -> $newStatus")
                    return@write false
                }
                
                // 更新规则状态
                val updatedRule = currentRule.withStatus(newStatus)
                rules[ruleIndex] = updatedRule
                saveRules(rules)
                
                Logger.rule("$TAG 规则状态更新成功: $ruleId ${currentRule.ruleStatus} -> $newStatus")
                true
                
            } catch (e: Exception) {
                Logger.ruleE("$TAG 更新规则状态失败", e)
                false
            }
        }
    }
    
    /**
     * 标记规则为已执行
     */
    fun markRuleExecuted(ruleId: String) {
        try {
            val executedRules = getExecutedRules().toMutableSet()
            executedRules.add(ruleId)
            
            sharedPreferences?.edit()
                ?.putStringSet(SP_KEY_EXECUTED_RULES, executedRules)
                ?.apply()
                
            Logger.rule("$TAG 规则标记为已执行: $ruleId")
        } catch (e: Exception) {
            Logger.ruleE("$TAG 标记规则执行失败", e)
        }
    }
    
    /**
     * 获取规则通过ID
     */
    fun getRuleById(ruleId: String): Rule? {
        return ruleLock.read {
            getAllRules().find { it.ruleId == ruleId }
        }
    }

    /**
     * 清空所有规则（用于测试）
     */
    fun clearAllRules(): Boolean {
        return ruleLock.write {
            try {
                sharedPreferences?.edit()
                    ?.putString(SP_KEY_RULE_LIST, "[]")
                    ?.putStringSet(SP_KEY_EXECUTED_RULES, emptySet())
                    ?.apply()

                Logger.rule("$TAG 清空所有规则完成")
                true
            } catch (e: Exception) {
                Logger.ruleE("$TAG 清空所有规则失败", e)
                false
            }
        }
    }
    
    // ==================== 私有辅助方法 ====================

    /**
     * 触发规则执行
     */
    private fun triggerRuleExecution(rule: Rule) {
        try {
            Logger.rule("$TAG 触发规则执行: ${rule.ruleId}")

            // 使用协程异步执行，避免阻塞当前线程
            ruleExecutionScope.launch {
                try {
                    // 检查设备状态，决定执行方式
                    if (isDeviceIdle()) {
                        Logger.rule("$TAG 设备空闲，立即执行规则: ${rule.ruleId}")
                        RuleBaseManager.executeRuleDirectly(rule)
                    } else {
                        Logger.rule("$TAG 设备忙碌，延迟执行规则: ${rule.ruleId}")
                        // 可以考虑显示用户确认对话框或延迟执行
                        RuleBaseManager.executeRuleDirectly(rule)
                    }
                } catch (e: Exception) {
                    Logger.ruleE("$TAG 执行规则失败: ${rule.ruleId}", e)
                }
            }

        } catch (e: Exception) {
            Logger.ruleE("$TAG 触发规则执行失败: ${rule.ruleId}", e)
        }
    }

    /**
     * 检查设备是否空闲（简化版本）
     */
    private fun isDeviceIdle(): Boolean {
        // 简化实现，总是返回true，表示立即执行
        // 实际实现可以检查屏幕状态、应用前台状态等
        return true
    }

    /**
     * 检查规则是否已存在
     */
    private fun isRuleExists(ruleId: String): Boolean {
        return getAllRules().any { it.ruleId == ruleId }
    }
    
    /**
     * 检查规则是否已执行过
     */
    private fun isRuleExecuted(ruleId: String): Boolean {
        return getExecutedRules().contains(ruleId)
    }
    
    /**
     * 添加新规则
     */
    private fun addNewRule(rule: Rule): Boolean {
        return try {
            val rules = getAllRules().toMutableList()
            val newRule = rule.copy(ruleStatus = RuleStatus.READY)
            rules.add(newRule)
            saveRules(rules)
            true
        } catch (e: Exception) {
            Logger.ruleE("$TAG 添加新规则失败", e)
            false
        }
    }
    
    /**
     * 删除规则
     */
    private fun deleteRule(ruleId: String): Boolean {
        return try {
            val rules = getAllRules().toMutableList()
            val removed = rules.removeAll { it.ruleId == ruleId }
            if (removed) {
                saveRules(rules)
            }
            removed
        } catch (e: Exception) {
            Logger.ruleE("$TAG 删除规则失败", e)
            false
        }
    }
    
    /**
     * 获取所有规则（包括非活跃状态）
     */
    private fun getAllRules(): List<Rule> {
        return try {
            val ruleListJson = sharedPreferences?.getString(SP_KEY_RULE_LIST, "[]") ?: "[]"
            val ruleArray = JSONArray(ruleListJson)
            val rules = mutableListOf<Rule>()
            
            for (i in 0 until ruleArray.length()) {
                val ruleJson = ruleArray.getJSONObject(i)
                Rule.fromJson(ruleJson)?.let { rule ->
                    rules.add(rule)
                }
            }
            
            rules
        } catch (e: Exception) {
            Logger.ruleE("$TAG 获取所有规则失败", e)
            emptyList()
        }
    }
    
    /**
     * 保存规则列表
     */
    private fun saveRules(rules: List<Rule>) {
        try {
            val ruleArray = JSONArray()
            rules.forEach { rule ->
                ruleArray.put(rule.toJson())
            }
            
            sharedPreferences?.edit()
                ?.putString(SP_KEY_RULE_LIST, ruleArray.toString())
                ?.apply()
                
            Logger.rule("$TAG 保存 ${rules.size} 个规则到存储")
        } catch (e: Exception) {
            Logger.ruleE("$TAG 保存规则失败", e)
        }
    }
    
    /**
     * 获取已执行的规则ID集合
     */
    private fun getExecutedRules(): Set<String> {
        return try {
            sharedPreferences?.getStringSet(SP_KEY_EXECUTED_RULES, emptySet()) ?: emptySet()
        } catch (e: Exception) {
            Logger.ruleE("$TAG 获取已执行规则失败", e)
            emptySet()
        }
    }
}

/**
 * 规则处理结果
 */
sealed class RuleProcessResult(val isSuccess: Boolean, val message: String) {
    class Success(message: String) : RuleProcessResult(true, message)
    class Error(message: String) : RuleProcessResult(false, message)
    class Ignored(message: String) : RuleProcessResult(true, message)
    
    companion object {
        fun success(message: String = "成功") = Success(message)
        fun error(message: String) = Error(message)
        fun ignored(message: String) = Ignored(message)
    }
}
