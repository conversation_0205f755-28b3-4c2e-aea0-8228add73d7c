package com.bbpos.wiseapp.param.manager;

import android.app.IntentService;
import android.content.Intent;
import android.os.Environment;

import com.bbpos.wiseapp.logger.BBLog;
import com.bbpos.wiseapp.service.common.RequestPermission;
import com.bbpos.wiseapp.service.contentprovider.db.ParamDbOperation;
import com.bbpos.wiseapp.tms.utils.BroadcastActions;
import com.bbpos.wiseapp.tms.utils.Constants;
import com.bbpos.wiseapp.tms.utils.FileUtils;
import com.bbpos.wiseapp.tms.utils.Helpers;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStream;
import java.util.List;
import java.util.regex.Pattern;

public class ParamParseService extends IntentService{
	private static final String TAG = "ParamParseService";
	public ParamParseService() {
		super("ParamParseService");
	}

	@Override
	protected void onHandleIntent(Intent intent) {
		BBLog.v(BBLog.TAG, "start ParamParseService.");
		//参数文件
		//file path //md5
		String filePath = intent.getExtras().getString(Constants.PARAM_FILE_PATH);
		String fileMd5 = intent.getExtras().getString(Constants.PARAM_FILE_MD5);
		if(!Pattern.matches("^((http|https)://)?([\\w-]+\\.)+[\\w-]+(/[\\w-./?%&=]*)?$", filePath) ||
				!Pattern.matches("^[A-Za-z0-9]+$", fileMd5) ) {
			return;
		}

		if(Helpers.isStrEmpty(filePath)){
			BBLog.w(TAG, "file md5 is empty.");
			return;
		}
			
		if(Helpers.isStrEmpty(fileMd5) && !FileUtils.isFileMd5Correct(filePath, fileMd5)){
			//校验md5 确保文件未被篡改
			BBLog.w(TAG, "file md5 has changed.");
			return;
		}
		
		if(Helpers.isXmlParaHadParsed(ParamParseService.this,filePath)){
			BBLog.w(TAG, "xml para had be parsed");
			return;
		}
		
		if(Constants.parsingParamFile.containsKey(filePath)){
			BBLog.w(TAG, filePath+"-xml para is parsing");
			return;
		}else{
			Constants.parsingParamFile.put(filePath, fileMd5);
		}
		
		try {
			XmlParseInterface xmlParser = new DomXmlParser();
			InputStream is;
			File file = new File(filePath);
			try {
				if (!file.getCanonicalPath().startsWith(Environment.getExternalStorageDirectory().getCanonicalPath())) {
					BBLog.e("ParamParseService", "Path Traversal");
				}
			} catch (IOException e) {
				e.printStackTrace();
			}
			is = new FileInputStream(file);
			List<ParamModel> paramList = xmlParser.parse(is);
			String pkgName = getPackageName(paramList);
			BBLog.v(BBLog.TAG, "parse "+pkgName +" paramSize:"+paramList.size());
			if(pkgName != null){
				//解析并更新par文件
				Helpers.updateParParam(paramList);
				//解析入库
				boolean isUpdated = ParamDbOperation.updateParams(this,pkgName, paramList);
				if(isUpdated){
					ParamPathModel paramPath = new ParamPathModel(filePath, fileMd5, Constants.PARSED_STAT_SUCCESS);
					ParamDbOperation.updateParamFile(this,paramPath);
					Intent i = new Intent(BroadcastActions.PARAM_UPDATE_COMPLETE_BC);
					sendBroadcast(i, RequestPermission.REQUEST_PERMISSION_MY_BROADCAST);
				}
			}
		} catch (FileNotFoundException e) {
			e.printStackTrace();
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			Constants.parsingParamFile.remove(filePath);
		}
	}

	private String getPackageName(List<ParamModel> paramList){
		for (ParamModel paraModel : paramList) {
			if(paraModel.key.equals(ParamModel.PackageName)){
				return paraModel.value;
			}
		}
		return null;
	}
}
