package com.dspread.mdm.service.platform.api.device

import android.content.Context
import android.os.Build
import android.provider.Settings
import com.dspread.mdm.service.services.DspreadService
import com.dspread.mdm.service.utils.log.Logger

/**
 * SP版本信息获取API
 */
object SpVersionApi {
    private const val TAG = "SpVersionApi"
    
    /**
     * 获取SP版本信息
     * @param context 上下文
     * @return SP版本字符串，获取失败返回空字符串
     */
    fun getSpVersion(context: Context): String {
        return try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE) {
                // Android 14及以上，使用DspreadService
                getSpVersionFromDspreadService()
            } else {
                // Android 14以下，从Settings获取
                getSpVersionFromSettings(context)
            }
        } catch (e: Exception) {
            Logger.platformE("$TAG 获取SP版本失败", e)
            ""
        }
    }
    
    /**
     * Android 14之前：从Settings获取SP版本
     */
    private fun getSpVersionFromSettings(context: Context): String {
        return try {
            val rawSpInfo = Settings.Global.getString(context.contentResolver, "sp_version") ?: ""
            // 解析SP版本信息，只提取版本号
            val parsedVersion = parseSpVersionInternal(rawSpInfo)
            Logger.platform("$TAG 从Settings获取SP版本: $parsedVersion")
            parsedVersion
        } catch (e: Exception) {
            Logger.platformE("$TAG 从Settings获取SP版本失败", e)
            ""
        }
    }

    /**
     * Android 14及以上：通过DspreadService获取SP版本
     */
    private fun getSpVersionFromDspreadService(): String {
        return try {
            val spVersion = DspreadService.getSpVersion()
            Logger.platform("$TAG 通过DspreadService获取SP版本: $spVersion")
            spVersion
        } catch (e: Exception) {
            Logger.platformE("$TAG DspreadService获取SP版本失败", e)
            ""
        }
    }

    /**
     * 获取固件信息对象，用于C0906上送
     */
    fun getFirmwareInfo(context: Context): FirmwareInfo {
        val spVersion = getSpVersion(context)

        return FirmwareInfo(
            spfw = spVersion
        )
    }

    /**
     * 解析SP版本信息，只提取版本号
     * 输入示例: *SP_VERSION:V1.0.5#*COMMIT:2d7cc90#*DATETIME:20250702#...
     * 输出: V1.0.5
     */
    internal fun parseSpVersionInternal(rawSpInfo: String?): String {
        if (rawSpInfo.isNullOrEmpty()) {
            return ""
        }

        try {
            // 查找 SP_VERSION: 标记
            val spVersionPrefix = "*SP_VERSION:"
            val startIndex = rawSpInfo.indexOf(spVersionPrefix)
            if (startIndex == -1) {
                Logger.platformW("$TAG SP版本信息格式异常，未找到SP_VERSION标记")
                return rawSpInfo.take(20) // 返回前20个字符作为备用
            }

            // 提取版本号部分
            val versionStart = startIndex + spVersionPrefix.length
            val endIndex = rawSpInfo.indexOf("#", versionStart)
            if (endIndex == -1) {
                Logger.platformW("$TAG SP版本信息格式异常，未找到结束标记")
                return rawSpInfo.substring(versionStart).take(20)
            }

            val version = rawSpInfo.substring(versionStart, endIndex)
            Logger.platform("$TAG 解析SP版本: $version")
            return version

        } catch (e: Exception) {
            Logger.platformE("$TAG 解析SP版本失败", e)
            return rawSpInfo.take(20) // 返回前20个字符作为备用
        }
    }
}

/**
 * 固件信息数据类
 */
data class FirmwareInfo(
    val spfw: String
) {
    /**
     * 转换为JSON对象
     */
    fun toJsonObject(): org.json.JSONObject {
        return org.json.JSONObject().apply {
            put("spfw", spfw)
        }
    }
}
