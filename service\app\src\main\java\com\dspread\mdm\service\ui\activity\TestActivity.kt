package com.dspread.mdm.service.ui.activity

import android.app.Activity
import android.content.Intent
import android.content.pm.PackageManager
import android.os.Build
import android.os.Bundle
import android.view.View
import android.widget.*
import com.dspread.mdm.service.network.websocket.message.WsMessageSender
import com.dspread.mdm.service.platform.monitor.NetworkTrafficMonitor
import com.dspread.mdm.service.utils.log.Logger
import kotlinx.coroutines.*
import java.text.SimpleDateFormat
import java.util.*

/**
 * 系统信息测试Activity
 * 显示系统和应用的基本信息
 */
class TestActivity : Activity() {

    private lateinit var mainLayout: LinearLayout
    private lateinit var scrollView: ScrollView
    private lateinit var infoTextView: TextView
    private lateinit var refreshButton: Button
    private lateinit var osUpdateTestButton: Button
    
    private val activityScope = CoroutineScope(Dispatchers.Main + SupervisorJob())

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setupUI()
        loadSystemInfo()
    }

    override fun onDestroy() {
        super.onDestroy()
        activityScope.cancel()
    }

    private fun setupUI() {
        // 创建主布局
        mainLayout = LinearLayout(this).apply {
            orientation = LinearLayout.VERTICAL
            setPadding(16, 16, 16, 16)
            setBackgroundColor(0xFFF5F5F5.toInt())
        }

        // 标题
        val titleText = TextView(this).apply {
            text = "系统信息"
            textSize = 20f
            setTextColor(0xFF333333.toInt())
            setPadding(0, 0, 0, 16)
        }
        mainLayout.addView(titleText)

        // 刷新按钮
        refreshButton = Button(this).apply {
            text = "刷新信息"
            setPadding(16, 12, 16, 12)
            layoutParams = LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.MATCH_PARENT,
                LinearLayout.LayoutParams.WRAP_CONTENT
            ).apply {
                setMargins(0, 8, 0, 0)
            }
            setOnClickListener { loadSystemInfo() }
        }
        mainLayout.addView(refreshButton)

        // OS升级测试按钮
        osUpdateTestButton = Button(this).apply {
            text = "OS升级测试"
            setTextColor(0xFFFFFFFF.toInt())
            setBackgroundColor(0xFF2196F3.toInt())
            setPadding(16, 12, 16, 12)
            layoutParams = LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.MATCH_PARENT,
                LinearLayout.LayoutParams.WRAP_CONTENT
            ).apply {
                setMargins(0, 8, 0, 0)
            }
            setOnClickListener {
                startOsUpdateTest()
            }
        }
        mainLayout.addView(osUpdateTestButton)

        // 分隔线
        val separator = View(this).apply {
            layoutParams = LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.MATCH_PARENT, 2
            ).apply { setMargins(0, 16, 0, 16) }
            setBackgroundColor(0xFFDDDDDD.toInt())
        }
        mainLayout.addView(separator)

        // 信息显示区域
        infoTextView = TextView(this).apply {
            textSize = 12f
            setTextColor(0xFF333333.toInt())
            setBackgroundColor(0xFFFFFFFF.toInt())
            setPadding(12, 12, 12, 12)
            typeface = android.graphics.Typeface.MONOSPACE
        }

        scrollView = ScrollView(this).apply {
            layoutParams = LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.MATCH_PARENT,
                LinearLayout.LayoutParams.MATCH_PARENT
            )
            addView(infoTextView)
        }
        mainLayout.addView(scrollView)

        setContentView(mainLayout)
    }

    private fun loadSystemInfo() {
        activityScope.launch {
            try {
                refreshButton.isEnabled = false
                refreshButton.text = "加载中..."

                val info = withContext(Dispatchers.IO) {
                    collectSystemInfo()
                }

                infoTextView.text = info

                // 只在Android 14+时异步更新DSPREAD服务信息
                if (Build.VERSION.SDK_INT >= 34) { // Android 14+
                    updateDspreadServiceInfo()
                }

                // 自动上送流量统计
                try {
                    WsMessageSender.uploadNetworkTraffic(
                        trigger = "manual_trigger",
                        forceUpload = true
                    )
                    Logger.com("刷新时自动上送流量统计")
                } catch (e: Exception) {
                    Logger.comE("自动上送流量统计失败", e)
                }

            } catch (e: Exception) {
                Logger.comE("系统信息加载失败", e)
                infoTextView.text = "信息加载失败: ${e.message}"
            } finally {
                refreshButton.isEnabled = true
                refreshButton.text = "刷新信息"
            }
        }
    }

    private fun collectSystemInfo(): String {
        val sb = StringBuilder()
        val dateFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault())
        
        sb.appendLine("系统信息报告")
        sb.appendLine("=" * 40)
        sb.appendLine("生成时间: ${dateFormat.format(Date())}")
        sb.appendLine()

        // 应用信息
        sb.appendLine("应用信息")
        sb.appendLine("-" * 30)
        try {
            val packageInfo = packageManager.getPackageInfo(packageName, 0)
            sb.appendLine("应用名称: ${getString(applicationInfo.labelRes)}")
            sb.appendLine("包名: $packageName")
            sb.appendLine("版本名称: ${packageInfo.versionName}")
            sb.appendLine("版本代码: ${if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) packageInfo.longVersionCode else packageInfo.versionCode}")
            sb.appendLine("目标SDK: ${applicationInfo.targetSdkVersion}")
            sb.appendLine("最小SDK: ${packageInfo.applicationInfo.minSdkVersion}")
            sb.appendLine("安装时间: ${dateFormat.format(Date(packageInfo.firstInstallTime))}")
            sb.appendLine("更新时间: ${dateFormat.format(Date(packageInfo.lastUpdateTime))}")
        } catch (e: Exception) {
            sb.appendLine("应用信息获取失败: ${e.message}")
        }
        sb.appendLine()

        // 设备信息
        sb.appendLine("设备信息")
        sb.appendLine("-" * 30)
        sb.appendLine("设备型号: ${Build.MODEL}")
        sb.appendLine("设备品牌: ${Build.BRAND}")
        sb.appendLine("设备制造商: ${Build.MANUFACTURER}")
        sb.appendLine("设备名称: ${Build.DEVICE}")
        sb.appendLine("产品名称: ${Build.PRODUCT}")
        sb.appendLine("硬件平台: ${Build.HARDWARE}")
        sb.appendLine("主板: ${Build.BOARD}")
        sb.appendLine("CPU架构: ${Build.SUPPORTED_ABIS.joinToString(", ")}")

        // 添加IMEI信息
        try {
            val deviceIMEI = com.dspread.mdm.service.platform.api.device.DeviceInfoApi.getImei(this)
            if (deviceIMEI.isNotEmpty()) {
                sb.appendLine("设备IMEI: $deviceIMEI")
            } else {
                sb.appendLine("设备IMEI: 未知")
            }
        } catch (e: Exception) {
            sb.appendLine("设备IMEI: 获取失败 (${e.message})")
        }

        // 添加MAC地址信息
        try {
            val macAddress = com.dspread.mdm.service.platform.api.device.DeviceInfoApi.getMacAddress(this)
            if (macAddress.isNotEmpty()) {
                sb.appendLine("MAC地址: $macAddress")
            } else {
                sb.appendLine("MAC地址: 未知")
            }
        } catch (e: Exception) {
            sb.appendLine("MAC地址: 获取失败 (${e.message})")
        }

        sb.appendLine()

        // 系统信息
        sb.appendLine("系统信息")
        sb.appendLine("-" * 30)
        sb.appendLine("Android版本: ${Build.VERSION.RELEASE}")
        sb.appendLine("API级别: ${Build.VERSION.SDK_INT}")
        sb.appendLine("安全补丁: ${Build.VERSION.SECURITY_PATCH}")
        sb.appendLine("构建ID: ${Build.ID}")
        sb.appendLine("构建时间: ${dateFormat.format(Date(Build.TIME))}")
        sb.appendLine("构建类型: ${Build.TYPE}")
        sb.appendLine("构建标签: ${Build.TAGS}")
        sb.appendLine("指纹: ${Build.FINGERPRINT}")
        sb.appendLine()

        // DSPREAD服务信息 - 根据Android版本区分处理
        sb.appendLine("服务信息")
        sb.appendLine("-" * 30)

        if (Build.VERSION.SDK_INT >= 34) { // Android 14+
            // Android 14+：需要异步等待服务绑定
            sb.appendLine("SP FW版本: 正在获取...")
            sb.appendLine("设备序列号: 正在获取...")
            sb.appendLine("服务状态: 正在检查...")
        } else {
            // Android < 14：直接同步获取
            try {
                val spVersion = com.dspread.mdm.service.platform.api.device.SpVersionApi.getSpVersion(this)
                sb.appendLine("SP FW版本: ${if (spVersion.isNotEmpty()) spVersion else "未知"}")
            } catch (e: Exception) {
                sb.appendLine("SP FW版本: 获取失败 (${e.message})")
            }

            try {
                val serialNumber = com.dspread.mdm.service.platform.api.device.DeviceInfoApi.getSerialNumber(this)
                sb.appendLine("设备序列号: ${if (!serialNumber.isNullOrEmpty()) serialNumber else "未知"}")
            } catch (e: Exception) {
                sb.appendLine("设备序列号: 获取失败 (${e.message})")
            }

            sb.appendLine("服务状态: Android < 14，使用传统API")
        }
        sb.appendLine()

        // 网络流量统计
/*
        try {
            val trafficStats = NetworkTrafficMonitor.getTodayTrafficStats()
            sb.appendLine("网络流量统计")
            sb.appendLine("-" * 30)
            sb.appendLine("HTTP上传: ${formatBytes(trafficStats.httpUploadBytes)}")
            sb.appendLine("HTTP下载: ${formatBytes(trafficStats.httpDownloadBytes)}")
            sb.appendLine("WebSocket上传: ${formatBytes(trafficStats.wsUploadBytes)}")
            sb.appendLine("WebSocket下载: ${formatBytes(trafficStats.wsDownloadBytes)}")
            sb.appendLine("总流量: ${formatBytes(trafficStats.getTotalBytes())}")
            sb.appendLine("统计日期: ${trafficStats.date}")
        } catch (e: Exception) {
            sb.appendLine("网络流量统计: 获取失败 - ${e.message}")
        }
        sb.appendLine()
*/

        // 运行时信息
        sb.appendLine("运行时信息")
        sb.appendLine("-" * 30)
        val runtime = Runtime.getRuntime()
        val maxMemory = runtime.maxMemory() / 1024 / 1024
        val totalMemory = runtime.totalMemory() / 1024 / 1024
        val freeMemory = runtime.freeMemory() / 1024 / 1024
        val usedMemory = totalMemory - freeMemory

        sb.appendLine("最大内存: ${maxMemory}MB")
        sb.appendLine("已分配内存: ${totalMemory}MB")
        sb.appendLine("空闲内存: ${freeMemory}MB")
        sb.appendLine("已使用内存: ${usedMemory}MB")
        sb.appendLine("处理器核心数: ${runtime.availableProcessors()}")

        // 存储信息
        try {
            val internalDir = filesDir
            val externalDir = getExternalFilesDir(null)

            sb.appendLine("内部存储路径: ${internalDir?.absolutePath}")
            sb.appendLine("外部存储路径: ${externalDir?.absolutePath}")

            internalDir?.let {
                val totalSpace = it.totalSpace / 1024 / 1024
                val freeSpace = it.freeSpace / 1024 / 1024
                val usedSpace = totalSpace - freeSpace
                sb.appendLine("内部存储总空间: ${totalSpace}MB")
                sb.appendLine("内部存储可用空间: ${freeSpace}MB")
                sb.appendLine("内部存储已用空间: ${usedSpace}MB")
            }
        } catch (e: Exception) {
            sb.appendLine("存储信息获取失败: ${e.message}")
        }
        sb.appendLine()

        // 权限信息
        sb.appendLine("关键权限状态")
        sb.appendLine("-" * 30)
        val permissions = arrayOf(
            "android.permission.INTERNET",
            "android.permission.ACCESS_NETWORK_STATE",
            "android.permission.WRITE_EXTERNAL_STORAGE",
            "android.permission.READ_EXTERNAL_STORAGE",
            "android.permission.ACCESS_FINE_LOCATION",
            "android.permission.ACCESS_COARSE_LOCATION",
            "android.permission.RECEIVE_BOOT_COMPLETED",
            "android.permission.WAKE_LOCK",
            "android.permission.FOREGROUND_SERVICE"
        )
        
        permissions.forEach { permission ->
            val status = if (checkSelfPermission(permission) == PackageManager.PERMISSION_GRANTED) {
                "已授权"
            } else {
                "未授权"
            }
            val shortName = permission.substringAfterLast(".")
            sb.appendLine("$shortName: $status")
        }
        sb.appendLine()

        sb.appendLine("=" * 40)

        return sb.toString()
    }

    /**
     * 异步更新DSPREAD服务信息（仅Android 14+）
     * Android < 14使用传统API直接同步获取
     */
    private fun updateDspreadServiceInfo() {
        activityScope.launch {
            try {
                // 在后台线程获取DSPREAD服务信息
                val serviceInfo = withContext(Dispatchers.IO) {
                    getDspreadServiceInfo()
                }

                // 在主线程更新UI
                updateServiceInfoInUI(serviceInfo)

            } catch (e: Exception) {
                Logger.comE("更新DSPREAD服务信息失败", e)
                updateServiceInfoInUI(DspreadServiceInfo(
                    spVersion = "获取失败: ${e.message}",
                    serialNumber = "获取失败: ${e.message}",
                    sysServiceStatus = "检查失败",
                    devServiceStatus = "检查失败"
                ))
            }
        }
    }

    /**
     * 获取DSPREAD服务信息（在后台线程执行）
     */
    private suspend fun getDspreadServiceInfo(): DspreadServiceInfo {
        return withContext(Dispatchers.IO) {
            var attempts = 0
            val maxAttempts = 10 // 最多等待10秒

            while (attempts < maxAttempts) {
                try {
                    val sysServiceAvailable = com.dspread.mdm.service.services.DspreadService.isSysServiceAvailable()
                    val devServiceAvailable = com.dspread.mdm.service.services.DspreadService.isDeviceServiceAvailable()

                    if (devServiceAvailable || sysServiceAvailable) {
                        // 服务可用，获取信息
                        val spVersion = try {
                            if (sysServiceAvailable) {
                                com.dspread.mdm.service.services.DspreadService.getSpVersion()
                            } else {
                                "系统服务不可用"
                            }
                        } catch (e: Exception) {
                            "获取失败: ${e.message}"
                        }

                        val serialNumber = try {
                            if (devServiceAvailable) {
                                com.dspread.mdm.service.services.DspreadService.getDeviceSerialNumber()
                            } else {
                                "设备服务不可用"
                            }
                        } catch (e: Exception) {
                            "获取失败: ${e.message}"
                        }

                        return@withContext DspreadServiceInfo(
                            spVersion = spVersion.ifEmpty { "未知" },
                            serialNumber = serialNumber.ifEmpty { "未知" },
                            sysServiceStatus = if (sysServiceAvailable) "可用" else "不可用",
                            devServiceStatus = if (devServiceAvailable) "可用" else "不可用"
                        )
                    }

                    // 服务还未就绪，等待1秒后重试
                    attempts++
                    delay(1000)

                } catch (e: Exception) {
                    Logger.comE("检查DSPREAD服务状态失败", e)
                    attempts++
                    delay(1000)
                }
            }

            // 超时，返回不可用状态
            DspreadServiceInfo(
                spVersion = "服务等待超时",
                serialNumber = "服务等待超时",
                sysServiceStatus = "等待超时",
                devServiceStatus = "等待超时"
            )
        }
    }

    /**
     * 在UI中更新服务信息
     */
    private fun updateServiceInfoInUI(serviceInfo: DspreadServiceInfo) {
        runOnUiThread {
            val currentText = infoTextView.text.toString()

            var updatedText = currentText
                .replace("SP FW版本: 正在获取...", "SP FW版本: ${serviceInfo.spVersion}")
                .replace("设备序列号: 正在获取...", "设备序列号: ${serviceInfo.serialNumber}")
                .replace("服务状态: 正在检查...", "服务状态:\n  - 系统服务: ${serviceInfo.sysServiceStatus}\n  - 设备服务: ${serviceInfo.devServiceStatus}")

            // 如果没有找到占位符，说明可能是刷新后的状态，直接更新相关行
            if (updatedText == currentText) {
                val lines = currentText.split("\n").toMutableList()
                for (i in lines.indices) {
                    when {
                        lines[i].startsWith("SP FW版本:") -> lines[i] = "SP FW版本: ${serviceInfo.spVersion}"
                        lines[i].startsWith("设备序列号:") -> lines[i] = "设备序列号: ${serviceInfo.serialNumber}"
                        lines[i].startsWith("服务状态:") -> {
                            lines[i] = "服务状态:"
                            // 确保下面两行存在
                            if (i + 1 < lines.size && lines[i + 1].contains("系统服务")) {
                                lines[i + 1] = "  - 系统服务: ${serviceInfo.sysServiceStatus}"
                            } else {
                                lines.add(i + 1, "  - 系统服务: ${serviceInfo.sysServiceStatus}")
                            }
                            if (i + 2 < lines.size && lines[i + 2].contains("设备服务")) {
                                lines[i + 2] = "  - 设备服务: ${serviceInfo.devServiceStatus}"
                            } else {
                                lines.add(i + 2, "  - 设备服务: ${serviceInfo.devServiceStatus}")
                            }
                            break
                        }
                    }
                }
                updatedText = lines.joinToString("\n")
            }

            infoTextView.text = updatedText

            // 添加调试日志
            Logger.com("TestActivity UI已更新: SP=${serviceInfo.spVersion}, SN=${serviceInfo.serialNumber}")
        }
    }

    /**
     * DSPREAD服务信息数据类
     */
    private data class DspreadServiceInfo(
        val spVersion: String,
        val serialNumber: String,
        val sysServiceStatus: String,
        val devServiceStatus: String
    )

    /**
     * 启动OS升级测试页面
     */
    private fun startOsUpdateTest() {
        try {
            Logger.com("启动OS升级测试页面")
            val intent = Intent(this, OsUpdateTestActivity::class.java)
            startActivity(intent)
        } catch (e: Exception) {
            Logger.comE("启动OS升级测试页面失败", e)
            Toast.makeText(this, "启动OS升级测试失败: ${e.message}", Toast.LENGTH_LONG).show()
        }
    }

    private operator fun String.times(n: Int): String = this.repeat(n)

    /**
     * 格式化字节数显示
     */
    private fun formatBytes(bytes: Long): String {
        return when {
            bytes < 1024 -> "${bytes}B"
            bytes < 1024 * 1024 -> String.format("%.1fKB", bytes / 1024.0)
            bytes < 1024 * 1024 * 1024 -> String.format("%.1fMB", bytes / (1024.0 * 1024.0))
            else -> String.format("%.1fGB", bytes / (1024.0 * 1024.0 * 1024.0))
        }
    }
}
