package com.dspread.mdm.service.broadcast.receivers

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.net.ConnectivityManager
import android.os.BatteryManager
import com.dspread.mdm.service.utils.log.Logger
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch

/**
 * 日志流模块广播接收器
 * 监听系统日志变化、网络状态、存储空间等相关的系统广播事件
 */
class LogStreamReceiver : BroadcastReceiver() {
    
    companion object {
        private const val TAG = "[LogStreamReceiver]"
        
        /**
         * 创建IntentFilter用于注册广播
         */
        fun createIntentFilter(): IntentFilter {
            return IntentFilter().apply {
                // 网络连接相关
                addAction(ConnectivityManager.CONNECTIVITY_ACTION)
                
                // 电源相关
                addAction(Intent.ACTION_BATTERY_CHANGED)
                addAction(Intent.ACTION_POWER_CONNECTED)
                addAction(Intent.ACTION_POWER_DISCONNECTED)
                addAction(Intent.ACTION_BATTERY_LOW)
                addAction(Intent.ACTION_BATTERY_OKAY)
                
                // 存储相关
                addAction(Intent.ACTION_DEVICE_STORAGE_LOW)
                addAction(Intent.ACTION_DEVICE_STORAGE_OK)
                addAction(Intent.ACTION_MEDIA_MOUNTED)
                addAction(Intent.ACTION_MEDIA_UNMOUNTED)
                
                // 系统相关
                addAction(Intent.ACTION_BOOT_COMPLETED)
                addAction(Intent.ACTION_SHUTDOWN)
                addAction(Intent.ACTION_REBOOT)
                
                // 时间相关
                addAction(Intent.ACTION_TIME_CHANGED)
                addAction(Intent.ACTION_TIMEZONE_CHANGED)
                
                // 自定义日志流广播
                addAction("com.dspread.mdm.LOG_STREAM_CONTROL")
                addAction("com.dspread.mdm.LOG_UPLOAD_STATUS")
                addAction("com.dspread.mdm.LOG_COLLECTION_TRIGGER")
                addAction("com.dspread.mdm.LOG_CLEANUP_TRIGGER")
            }
        }
    }
    
    override fun onReceive(context: Context, intent: Intent) {
        val action = intent.action ?: return
        
        Logger.logStream("$TAG 收到广播: $action")
        
        // 将耗时操作移到后台线程执行
        val pendingResult = goAsync()
        
        GlobalScope.launch {
            try {
                when (action) {
                    // 网络连接相关
                    ConnectivityManager.CONNECTIVITY_ACTION -> {
                        handleConnectivityChanged(context, intent)
                    }
                    
                    // 电源相关
                    Intent.ACTION_BATTERY_CHANGED -> {
                        handleBatteryChanged(context, intent)
                    }
                    
                    Intent.ACTION_POWER_CONNECTED -> {
                        handlePowerConnected(context, intent)
                    }
                    
                    Intent.ACTION_POWER_DISCONNECTED -> {
                        handlePowerDisconnected(context, intent)
                    }
                    
                    Intent.ACTION_BATTERY_LOW -> {
                        handleBatteryLow(context, intent)
                    }
                    
                    Intent.ACTION_BATTERY_OKAY -> {
                        handleBatteryOkay(context, intent)
                    }
                    
                    // 存储相关
                    Intent.ACTION_DEVICE_STORAGE_LOW -> {
                        handleStorageLow(context, intent)
                    }
                    
                    Intent.ACTION_DEVICE_STORAGE_OK -> {
                        handleStorageOkay(context, intent)
                    }
                    
                    Intent.ACTION_MEDIA_MOUNTED -> {
                        handleMediaMounted(context, intent)
                    }
                    
                    Intent.ACTION_MEDIA_UNMOUNTED -> {
                        handleMediaUnmounted(context, intent)
                    }
                    
                    // 系统相关
                    Intent.ACTION_BOOT_COMPLETED -> {
                        handleBootCompleted(context, intent)
                    }
                    
                    Intent.ACTION_SHUTDOWN -> {
                        handleShutdown(context, intent)
                    }
                    
                    Intent.ACTION_REBOOT -> {
                        handleReboot(context, intent)
                    }
                    
                    // 时间相关
                    Intent.ACTION_TIME_CHANGED -> {
                        handleTimeChanged(context, intent)
                    }
                    
                    Intent.ACTION_TIMEZONE_CHANGED -> {
                        handleTimezoneChanged(context, intent)
                    }
                    
                    // 自定义日志流广播
                    "com.dspread.mdm.LOG_STREAM_CONTROL" -> {
                        handleLogStreamControl(context, intent)
                    }
                    
                    "com.dspread.mdm.LOG_UPLOAD_STATUS" -> {
                        handleLogUploadStatus(context, intent)
                    }
                    
                    "com.dspread.mdm.LOG_COLLECTION_TRIGGER" -> {
                        handleLogCollectionTrigger(context, intent)
                    }
                    
                    "com.dspread.mdm.LOG_CLEANUP_TRIGGER" -> {
                        handleLogCleanupTrigger(context, intent)
                    }
                    
                    else -> {
                        Logger.logStream("$TAG 未处理的广播: $action")
                    }
                }
            } catch (e: Exception) {
                Logger.logStreamE("$TAG 处理广播失败: $action", e)
            } finally {
                // 确保完成异步操作
                pendingResult.finish()
            }
        }
    }
    
    /**
     * 处理网络连接变化
     */
    private fun handleConnectivityChanged(context: Context, intent: Intent) {
        val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
        val activeNetwork = connectivityManager.activeNetworkInfo
        
        if (activeNetwork != null && activeNetwork.isConnected) {
            Logger.logStream("$TAG 网络已连接: ${activeNetwork.typeName}")
            
            when (activeNetwork.type) {
                ConnectivityManager.TYPE_WIFI -> {
                    Logger.logStream("$TAG WiFi网络连接，可以开始上传日志")
                    notifyNetworkAvailable(context, "wifi", true)
                }
                
                ConnectivityManager.TYPE_MOBILE -> {
                    Logger.logStream("$TAG 移动网络连接，根据策略决定是否上传日志")
                    notifyNetworkAvailable(context, "mobile", true)
                }
                
                else -> {
                    Logger.logStream("$TAG 其他网络连接: ${activeNetwork.typeName}")
                    notifyNetworkAvailable(context, "other", true)
                }
            }
        } else {
            Logger.logStream("$TAG 网络已断开")
            notifyNetworkAvailable(context, "none", false)
        }
    }
    
    /**
     * 处理电池状态变化
     */
    private fun handleBatteryChanged(context: Context, intent: Intent) {
        val level = intent.getIntExtra(BatteryManager.EXTRA_LEVEL, 0)
        val scale = intent.getIntExtra(BatteryManager.EXTRA_SCALE, 100)
        val temperature = intent.getIntExtra(BatteryManager.EXTRA_TEMPERATURE, 0)
        val health = intent.getIntExtra(BatteryManager.EXTRA_HEALTH, BatteryManager.BATTERY_HEALTH_UNKNOWN)
        val status = intent.getIntExtra(BatteryManager.EXTRA_STATUS, BatteryManager.BATTERY_STATUS_UNKNOWN)
        val plugged = intent.getIntExtra(BatteryManager.EXTRA_PLUGGED, 0)
        
        val batteryLevel = (level * 100) / scale
        val batteryTemp = temperature / 10.0f // 温度单位是0.1摄氏度
        
        Logger.logStream("$TAG 电池状态变化: 电量=${batteryLevel}%, 温度=${batteryTemp}°C, 健康=${health}, 状态=${status}, 充电=${plugged}")
        
        // 根据电池状态调整日志收集策略
        when {
            batteryLevel < 15 -> {
                Logger.logStream("$TAG 电池电量低，暂停非关键日志收集")
                notifyBatteryStatus(context, "low", batteryLevel, batteryTemp)
            }
            batteryLevel > 80 && plugged > 0 -> {
                Logger.logStream("$TAG 电池电量充足且在充电，可以进行完整日志收集")
                notifyBatteryStatus(context, "high_charging", batteryLevel, batteryTemp)
            }
            else -> {
                Logger.logStream("$TAG 电池状态正常")
                notifyBatteryStatus(context, "normal", batteryLevel, batteryTemp)
            }
        }
    }
    
    /**
     * 处理电源连接
     */
    private fun handlePowerConnected(context: Context, intent: Intent) {
        Logger.logStream("$TAG 电源已连接，可以开始完整的日志收集和上传")
        notifyPowerStatus(context, true)
    }
    
    /**
     * 处理电源断开
     */
    private fun handlePowerDisconnected(context: Context, intent: Intent) {
        Logger.logStream("$TAG 电源已断开，切换到节能模式的日志收集")
        notifyPowerStatus(context, false)
    }
    
    /**
     * 处理电池电量低
     */
    private fun handleBatteryLow(context: Context, intent: Intent) {
        Logger.logStream("$TAG 电池电量低警告，暂停非关键日志收集")
        notifyBatteryLowWarning(context, true)
    }
    
    /**
     * 处理电池电量恢复正常
     */
    private fun handleBatteryOkay(context: Context, intent: Intent) {
        Logger.logStream("$TAG 电池电量恢复正常，恢复正常日志收集")
        notifyBatteryLowWarning(context, false)
    }
    
    /**
     * 处理存储空间不足
     */
    private fun handleStorageLow(context: Context, intent: Intent) {
        Logger.logStream("$TAG 存储空间不足，开始清理旧日志")
        
        val storageInfo = getStorageInfo(context)
        Logger.logStream("$TAG 当前存储信息: $storageInfo")
        notifyStorageStatus(context, "low", storageInfo)
        
        // 触发日志清理
        triggerLogCleanup(context, "storage_low")
    }
    
    /**
     * 处理存储空间恢复正常
     */
    private fun handleStorageOkay(context: Context, intent: Intent) {
        Logger.logStream("$TAG 存储空间恢复正常")
        val storageInfo = getStorageInfo(context)
        notifyStorageStatus(context, "okay", storageInfo)
    }
    
    /**
     * 处理外部存储挂载
     */
    private fun handleMediaMounted(context: Context, intent: Intent) {
        val path = intent.data?.path
        Logger.logStream("$TAG 外部存储已挂载: $path")
        
        if (path != null) {
            notifyExternalStorageAvailable(context, path, true)
        }
    }
    
    /**
     * 处理外部存储卸载
     */
    private fun handleMediaUnmounted(context: Context, intent: Intent) {
        val path = intent.data?.path
        Logger.logStream("$TAG 外部存储已卸载: $path")
        
        if (path != null) {
            notifyExternalStorageAvailable(context, path, false)
        }
    }
    
    // 简化的处理方法，避免文件过长
    private fun handleBootCompleted(context: Context, intent: Intent) {
        Logger.logStream("$TAG 系统启动完成，开始启动时日志收集")
        triggerBootLogCollection(context)
    }
    
    private fun handleShutdown(context: Context, intent: Intent) {
        Logger.logStream("$TAG 系统即将关闭，保存关键日志")
        triggerShutdownLogCollection(context)
    }
    
    private fun handleReboot(context: Context, intent: Intent) {
        Logger.logStream("$TAG 系统即将重启")
    }
    
    private fun handleTimeChanged(context: Context, intent: Intent) {
        Logger.logStream("$TAG 系统时间已更改")
        notifyTimeChanged(context)
    }
    
    private fun handleTimezoneChanged(context: Context, intent: Intent) {
        Logger.logStream("$TAG 系统时区已更改")
        notifyTimezoneChanged(context)
    }
    
    private fun handleLogStreamControl(context: Context, intent: Intent) {
        Logger.logStream("$TAG 收到日志流控制命令")
    }
    
    private fun handleLogUploadStatus(context: Context, intent: Intent) {
        Logger.logStream("$TAG 收到日志上传状态更新")
    }
    
    private fun handleLogCollectionTrigger(context: Context, intent: Intent) {
        Logger.logStream("$TAG 收到日志收集触发命令")
    }
    
    private fun handleLogCleanupTrigger(context: Context, intent: Intent) {
        Logger.logStream("$TAG 收到日志清理触发命令")
    }
    
    // 简化的通知方法
    private fun notifyNetworkAvailable(context: Context, type: String, available: Boolean) {}
    private fun notifyBatteryStatus(context: Context, status: String, level: Int, temp: Float) {}
    private fun notifyPowerStatus(context: Context, connected: Boolean) {}
    private fun notifyBatteryLowWarning(context: Context, isLow: Boolean) {}
    private fun notifyStorageStatus(context: Context, status: String, info: String) {}
    private fun notifyExternalStorageAvailable(context: Context, path: String, available: Boolean) {}
    private fun notifyTimeChanged(context: Context) {}
    private fun notifyTimezoneChanged(context: Context) {}
    private fun triggerBootLogCollection(context: Context) {}
    private fun triggerShutdownLogCollection(context: Context) {}
    private fun triggerLogCleanup(context: Context, reason: String) {}
    private fun getStorageInfo(context: Context): String = "Storage info placeholder"
}
