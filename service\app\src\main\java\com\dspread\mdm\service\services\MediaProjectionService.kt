package com.dspread.mdm.service.services

import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.Service
import android.content.Context
import android.content.Intent
import android.media.projection.MediaProjection
import android.media.projection.MediaProjectionManager
import android.os.Build
import android.os.IBinder
import androidx.core.app.NotificationCompat
import com.dspread.mdm.service.R
import com.dspread.mdm.service.utils.log.Logger

/**
 * MediaProjection前台服务
 * Android 14+要求MediaProjection必须在前台服务中运行
 * 移动自modules/remoteview目录，统一放在services目录下
 */
class MediaProjectionService : Service() {

    companion object {
        private const val TAG = "MediaProjectionService"
        private const val NOTIFICATION_ID = 2001
        private const val CHANNEL_ID = "media_projection_channel"
        private const val CHANNEL_NAME = "屏幕截取服务"
        
        // 静态变量保存MediaProjection实例
        @Volatile
        private var mediaProjection: MediaProjection? = null
        
        /**
         * 启动MediaProjection服务（基于稳定版本）
         */
        fun startService(context: Context, data: Intent): Boolean {
            return try {
                Logger.remote("启动MediaProjection前台服务")

                // 启动前先清理旧的MediaProjection（防止第二次启动问题）
                try {
                    mediaProjection?.let { oldProjection ->
                        Logger.remote("检测到旧的MediaProjection，先清理")
                        oldProjection.stop()
                        mediaProjection = null
                        Logger.remote("旧MediaProjection已清理")
                    }
                } catch (e: Exception) {
                    Logger.remoteE("清理旧MediaProjection异常", e)
                    mediaProjection = null // 强制清空
                }

                val intent = Intent(context, MediaProjectionService::class.java)
                intent.putExtra("media_projection_data", data)

                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                    context.startForegroundService(intent)
                } else {
                    context.startService(intent)
                }

                true
            } catch (e: Exception) {
                Logger.remoteE("启动MediaProjection服务失败", e)
                false
            }
        }
        
        /**
         * 停止MediaProjection服务
         */
        fun stopService(context: Context) {
            val intent = Intent(context, MediaProjectionService::class.java)
            context.stopService(intent)
        }
        
        /**
         * 获取MediaProjection实例
         */
        fun getMediaProjection(): MediaProjection? = mediaProjection
        
        /**
         * 检查MediaProjection是否可用
         */
        fun isMediaProjectionAvailable(): Boolean = mediaProjection != null
    }

    private var mediaProjectionManager: MediaProjectionManager? = null

    override fun onCreate() {
        super.onCreate()
        Logger.remote("$TAG 服务创建")
        
        // 创建通知渠道
        createNotificationChannel()
        
        // 启动前台服务
        startForeground(NOTIFICATION_ID, createNotification())
        
        // 初始化MediaProjectionManager
        mediaProjectionManager = getSystemService(Context.MEDIA_PROJECTION_SERVICE) as MediaProjectionManager
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        Logger.remote("$TAG 服务启动")

        try {
            // 立即处理MediaProjection数据（基于稳定版本）
            val data = intent?.getParcelableExtra<Intent>("media_projection_data")
            if (data != null) {
                Logger.remote("$TAG 收到MediaProjection数据，开始创建实例")
                // 在前台服务启动后立即创建MediaProjection
                createMediaProjection(data)
            } else {
                Logger.remoteW("$TAG 没有MediaProjection数据")
            }

        } catch (e: Exception) {
            Logger.remoteE("$TAG onStartCommand异常", e)
        }

        return START_STICKY
    }

    override fun onBind(intent: Intent?): IBinder? = null

    override fun onDestroy() {
        super.onDestroy()
        Logger.remote("$TAG 服务销毁")
        
        // 释放MediaProjection
        mediaProjection?.stop()
        mediaProjection = null
    }

    /**
     * 创建MediaProjection实例（基于稳定版本）
     */
    private fun createMediaProjection(data: Intent) {
        try {
            Logger.remote("$TAG 创建MediaProjection实例")

            if (mediaProjectionManager == null) {
                Logger.remoteE("$TAG MediaProjectionManager为null")
                return
            }

            Logger.remote("$TAG 调用getMediaProjection...")
            mediaProjection = mediaProjectionManager!!.getMediaProjection(android.app.Activity.RESULT_OK, data)

            if (mediaProjection != null) {
                Logger.remote("$TAG MediaProjection创建成功")

                // 注册回调
                mediaProjection?.registerCallback(object : MediaProjection.Callback() {
                    override fun onStop() {
                        super.onStop()
                        Logger.remote("$TAG MediaProjection已停止")
                        mediaProjection = null

                        // MediaProjection停止时，停止服务
                        stopSelf()
                    }
                }, null)

            } else {
                Logger.remoteE("$TAG MediaProjection创建失败 - 返回null")
            }

        } catch (e: Exception) {
            Logger.remoteE("$TAG 创建MediaProjection异常", e)
        }
    }

    /**
     * 创建通知渠道
     */
    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                CHANNEL_ID,
                CHANNEL_NAME,
                NotificationManager.IMPORTANCE_LOW
            ).apply {
                description = "用于屏幕截取和远程视图功能"
                setShowBadge(false)
            }
            
            val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            notificationManager.createNotificationChannel(channel)
        }
    }

    /**
     * 创建前台服务通知
     */
    private fun createNotification(): Notification {
        return NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle("屏幕截取服务")
            .setContentText("正在运行屏幕截取功能...")
            .setSmallIcon(R.drawable.ic_launcher_foreground)
            .setAutoCancel(false)
            .setOngoing(true)
            .build()
    }
}
