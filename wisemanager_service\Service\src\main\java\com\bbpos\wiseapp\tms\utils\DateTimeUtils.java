package com.bbpos.wiseapp.tms.utils;

import android.app.AlarmManager;
import android.app.PendingIntent;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.os.Build;
import android.text.TextUtils;
import android.util.Log;

import com.bbpos.wiseapp.logger.BBLog;
import com.bbpos.wiseapp.service.receiver.CloudReceiver;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.Locale;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class DateTimeUtils {
	public static String getCurrentTimeMills() {
		long currentTimeMillis = System.currentTimeMillis();
		return String.valueOf(currentTimeMillis);
	}

	public static Date parse(String timeStamp) {
		if (DateTimeUtils.isNumeric(timeStamp)) {
			return new Date(new Long(timeStamp).longValue());
		}
		DateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		try {
			return df.parse(timeStamp);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return null;
	}

	/**
	 * 判断 时间是否有效
	 * @param time
	 * @return
	 */
	public static boolean isRebootTimeVaild(String time){
		if (TextUtils.isEmpty(time)) return false;
		try {
			SimpleDateFormat sfd = new SimpleDateFormat("HH:mm:ss");
			sfd.setLenient(false);
			sfd.parse(time);
			return true;
		} catch (Exception e) {
			e.printStackTrace();
			BBLog.e(BBLog.TAG, "isRebootTimeVaild: 時間格式無效" );
			return false;
		}
	}

	public static boolean isNumeric(String str) {
		Pattern pattern = Pattern.compile("[0-9]*");
		Matcher isNum = pattern.matcher(str);
		if (!isNum.matches()) {
			return false;
		}
		return true;
	}

	public static void setRebootAlarmEveryday(Context context, String time) {
		BBLog.w(BBLog.TAG, "設置定時重啓時間：" + time);
		AlarmManager alarmManager = (AlarmManager) context.getSystemService(Context.ALARM_SERVICE);

		Calendar calendarTemp = Calendar.getInstance(Locale.getDefault());
		SimpleDateFormat sfd = new SimpleDateFormat("HH:mm:ss");
		Date start = null;
		try {
			start = sfd.parse(time);
		} catch (Exception e) {
			e.printStackTrace();
		}
		calendarTemp.setTime(start);

		Calendar calendar = Calendar.getInstance(Locale.getDefault());
		calendar.set(Calendar.HOUR_OF_DAY, calendarTemp.get(Calendar.HOUR_OF_DAY));
		calendar.set(Calendar.MINUTE, calendarTemp.get(Calendar.MINUTE));
		calendar.set(Calendar.SECOND, calendarTemp.get(Calendar.SECOND));
		calendar.set(Calendar.MILLISECOND, 0);

		if (calendar.before(Calendar.getInstance(Locale.getDefault()))) {
			calendar.add(Calendar.DATE, 1);
		}

		Intent intent = new Intent();
		intent.setAction(BroadcastActions.ACTION_EXPIRE_REBOOT);
		if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
			intent.setComponent(new ComponentName(ContextUtil.getInstance().getPackageName(), CloudReceiver.class.getName()));
		}
		PendingIntent pendingIntent = PendingIntent.getBroadcast(context, 0, intent, PendingIntent.FLAG_CANCEL_CURRENT);
		alarmManager.cancel(pendingIntent);
		alarmManager.setRepeating(AlarmManager.RTC_WAKEUP, calendar.getTimeInMillis(), AlarmManager.INTERVAL_DAY, pendingIntent);
	}
}