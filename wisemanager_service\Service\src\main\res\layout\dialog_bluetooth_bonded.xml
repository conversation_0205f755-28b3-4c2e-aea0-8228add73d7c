<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="24dp"
    android:background="@drawable/dialog_shape">

    <LinearLayout
        android:layout_height="0dp"
        android:layout_width="match_parent"
        android:layout_weight="1"
        android:gravity="center_vertical"
        android:orientation="vertical">
        <TextView
            android:id="@+id/tv_header"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:text="@string/bluetooth_preference_paired_dialog_title"
            android:textSize="20sp"
            android:textColor="@color/title"/>
    </LinearLayout>
    <LinearLayout
        android:layout_height="wrap_content"
        android:layout_width="match_parent"
        android:layout_marginTop="12dp"
        android:orientation="vertical">
        <TextView
            android:id="@+id/tv_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:text="@string/bluetooth_preference_paired_dialog_name_label"
            android:textSize="14sp"
            android:textColor="@color/subtitle"/>

        <EditText
            android:id="@+id/et_btdevice"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="5dp"
            android:maxLines="1"
            android:requiresFadingEdge="horizontal"
            android:ellipsize="none"
            android:textDirection="locale"
            android:maxLength="64"
            android:textSize="16sp"
            android:textCursorDrawable="@drawable/cursor_shape"
            android:background="@drawable/edittext_bord"/>
    </LinearLayout>
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="24dp"
        android:gravity="center_vertical|right"
        android:orientation="horizontal">
        <TextView
            android:id="@+id/tv_unbond"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:text="@string/bluetooth_unbond"
            android:textColor="@color/theme_green"
            android:textSize="14sp"/>
        <TextView
            android:id="@+id/tv_done"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="20dp"
            android:layout_marginRight="10dp"
            android:gravity="center"
            android:text="@string/bluetooth_done"
            android:textColor="@color/theme_green"
            android:textSize="14sp"/>
    </LinearLayout>

</LinearLayout>