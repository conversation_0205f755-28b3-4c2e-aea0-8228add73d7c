package com.dspread.mdm.service.modules

import android.content.Context
import com.dspread.mdm.service.platform.api.screen.ScreenManagerApi
import com.dspread.mdm.service.utils.log.Logger
import kotlinx.coroutines.*

/**
 * 模块停止管理器
 * 确保所有模块都能立即响应停止命令，任何时候都以收到指令为主
 * 
 * 功能：
 * 1. 统一管理所有模块的停止操作
 * 2. 确保立即响应停止命令
 * 3. 处理协程取消异常
 * 4. 提供优雅的停止机制
 */
object ModuleStopManager {

    private const val TAG = "ModuleStopManager"

    // 模块停止专用协程作用域
    private val stopScope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    
    /**
     * 立即停止Remote View模块
     * 收到CLOSE_REMOTE_CONTROL命令时调用
     */
    fun stopRemoteViewImmediately(manager: Any?, context: Context? = null) {
        try {
            Logger.com("ModuleStopManager: 立即停止Remote View")

            if (manager != null) {
                // 使用BaseModuleManager的便捷方法，并在协程中调用
                stopScope.launch {
                    try {
                        val stopMethod = manager.javaClass.getMethod("stop")
                        val result = stopMethod.invoke(manager) as? Result<*>

                        if (result?.isSuccess == true) {
                            Logger.com("ModuleStopManager: Remote View停止成功")

                            // 调用release方法释放资源
                            try {
                                val releaseMethod = manager.javaClass.getMethod("release")
                                releaseMethod.invoke(manager)
                                Logger.com("ModuleStopManager: Remote View资源释放成功")
                            } catch (e: Exception) {
                                Logger.comE("ModuleStopManager: Remote View资源释放失败", e)
                            }
                        } else {
                            Logger.comE("ModuleStopManager: Remote View停止失败")
                        }
                    } catch (e: Exception) {
                        Logger.comE("ModuleStopManager: 调用stop方法异常", e)
                    }
                }
            } else {
                Logger.comW("ModuleStopManager: Remote View管理器为空")

                // 即使管理器为空，也要尝试恢复屏幕设置
                if (context != null) {
                    GlobalScope.launch {
                        try {
                            ScreenManagerApi.disableRemoteViewScreenMode(context)
                            Logger.com("ModuleStopManager: 屏幕设置恢复成功")
                        } catch (e: Exception) {
                            Logger.comE("ModuleStopManager: 屏幕设置恢复失败", e)
                        }
                    }
                }
            }

        } catch (e: Exception) {
            Logger.comE("ModuleStopManager: 停止Remote View异常", e)

            // 异常情况下也要尝试恢复屏幕设置
            if (context != null) {
                GlobalScope.launch {
                    try {
                        ScreenManagerApi.disableRemoteViewScreenMode(context)
                        Logger.com("ModuleStopManager: 异常情况下屏幕设置恢复成功")
                    } catch (e: Exception) {
                        Logger.comE("ModuleStopManager: 异常情况下屏幕设置恢复失败", e)
                    }
                }
            }
        }
    }
    
    /**
     * 立即停止Log Stream模块
     * 收到CLOSE_LOG_STREAM命令时调用
     */
    fun stopLogStreamImmediately(manager: Any?) {
        try {
            Logger.com("ModuleStopManager: 立即停止Log Stream")
            
            if (manager != null) {
                GlobalScope.launch(Dispatchers.Main.immediate) {
                    try {
                        // 类型转换后直接调用LogStreamManager的stopStreaming方法
                        val logStreamManager = manager as com.dspread.mdm.service.modules.logstream.LogStreamManager
                        val result = logStreamManager.stopStreaming()

                        if (result.isSuccess) {
                            Logger.com("ModuleStopManager: Log Stream停止成功")
                        } else {
                            Logger.comE("ModuleStopManager: Log Stream停止失败", result.exceptionOrNull())
                        }
                    } catch (e: Exception) {
                        Logger.comE("ModuleStopManager: 停止Log Stream异常", e)
                    }
                }
            } else {
                Logger.comW("ModuleStopManager: Log Stream管理器为空")
            }
            
        } catch (e: Exception) {
            Logger.comE("ModuleStopManager: 停止Log Stream异常", e)
        }
    }
    
    /**
     * 立即停止所有模块
     * 紧急情况下调用
     */
    fun stopAllModulesImmediately(
        remoteViewManager: Any? = null,
        logStreamManager: Any? = null,
        wifiProfileManager: Any? = null,
        geofenceManager: Any? = null,
        apnManager: Any? = null
    ) {
        try {
            Logger.com("ModuleStopManager: 紧急停止所有模块")
            
            // 并行停止所有模块
            stopScope.launch(Dispatchers.Main.immediate) {
                val jobs = mutableListOf<Job>()
                
                // Remote View
                if (remoteViewManager != null) {
                    jobs.add(launch { stopRemoteViewImmediately(remoteViewManager) })
                }
                
                // Log Stream
                if (logStreamManager != null) {
                    jobs.add(launch { stopLogStreamImmediately(logStreamManager) })
                }
                
                // WiFi Profile
                if (wifiProfileManager != null) {
                    jobs.add(launch { stopModuleByReflection(wifiProfileManager, "WiFi Profile") })
                }
                
                // Geofence
                if (geofenceManager != null) {
                    jobs.add(launch { stopModuleByReflection(geofenceManager, "Geofence") })
                }
                
                // APN
                if (apnManager != null) {
                    jobs.add(launch { stopModuleByReflection(apnManager, "APN") })
                }
                
                // 等待所有停止操作完成
                jobs.joinAll()
                Logger.com("ModuleStopManager: 所有模块停止操作完成")
            }
            
        } catch (e: Exception) {
            Logger.comE("ModuleStopManager: 停止所有模块异常", e)
        }
    }
    
    /**
     * 通过反射停止模块
     */
    private suspend fun stopModuleByReflection(manager: Any, moduleName: String) {
        try {
            Logger.com("ModuleStopManager: 停止$moduleName 模块")

            // 尝试调用stop方法（BaseModuleManager的便捷方法）
            val stopMethod = manager.javaClass.getMethod("stop")
            val result = stopMethod.invoke(manager) as? Result<*>

            if (result?.isSuccess == true) {
                Logger.com("ModuleStopManager: $moduleName 停止成功")
            } else {
                Logger.comE("ModuleStopManager: $moduleName 停止失败")
            }

        } catch (e: NoSuchMethodException) {
            Logger.comW("ModuleStopManager: $moduleName 没有stop方法")
        } catch (e: Exception) {
            Logger.comE("ModuleStopManager: 停止$moduleName 异常", e)
        }
    }
    
    /**
     * 处理协程取消异常
     * 将CancellationException转换为正常的停止日志
     */
    fun handleCancellationException(e: CancellationException, moduleName: String) {
        Logger.com("ModuleStopManager: $moduleName 协程被取消（正常停止）")
    }
    
    /**
     * 检查是否为取消异常
     */
    fun isCancellationException(e: Throwable): Boolean {
        return e is CancellationException
    }
}
