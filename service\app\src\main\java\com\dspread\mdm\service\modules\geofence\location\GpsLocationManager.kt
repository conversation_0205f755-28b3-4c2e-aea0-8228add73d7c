package com.dspread.mdm.service.modules.geofence.location

import android.Manifest
import android.annotation.SuppressLint
import android.app.AlarmManager
import android.app.PendingIntent
import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.location.Criteria
import android.location.GnssStatus
import android.location.Location
import android.location.LocationListener
import android.location.LocationManager
import android.os.Build
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.preference.PreferenceManager
import android.text.TextUtils
import android.widget.Toast
import androidx.core.app.ActivityCompat
import com.dspread.mdm.service.broadcast.core.BroadcastActions
import com.dspread.mdm.service.constants.Constants
import com.dspread.mdm.service.SmartMdmServiceApp
import com.dspread.mdm.service.modules.geofence.BluetoothBeaconScanner
import com.dspread.mdm.service.modules.geofence.GeofenceHandler
import com.dspread.mdm.service.modules.geofence.GeofenceStateManager
import com.dspread.mdm.service.modules.geofence.model.BeaconInfo
import com.dspread.mdm.service.network.websocket.WebSocketCenter
import com.dspread.mdm.service.network.websocket.message.WsMessageSender
import com.dspread.mdm.service.broadcast.receivers.GeofenceReceiver
import com.dspread.mdm.service.utils.log.Logger
import com.dspread.mdm.service.broadcast.core.BroadcastSender
import com.dspread.mdm.service.platform.api.network.NetworkApi
import com.dspread.mdm.service.platform.api.network.WiFiManagerApi
import org.json.JSONArray
import org.json.JSONObject
import java.text.SimpleDateFormat
import java.util.Locale
import kotlin.math.*

/**
 * GPS位置管理器
 * 负责GPS定位、地理围栏监控和安全措施执行
 */
object GpsLocationManager {

    private const val TAG = "GPSLocation"
    private const val DEFAULT_VALUE = -999.0

    // 地理围栏状态常量 
    const val IN_ZONE = 1
    const val OUT_OF_ZONE = 2
    const val LOCK_SCREEN = 3
    const val WIPE_DATA = 4
    const val ROAMING = 5

    // 警告状态常量
    const val WARNING_STATUS_IN_FENCE = 1
    const val WARNING_STATUS_OUT_OF_FENCE = 2
    const val WARNING_STATUS_OUT_OF_FENCE_AT_REBOOT = 3

    // 默认配置
    const val DEFAULT_DISTANCE = 10f
    const val DEFAULT_TIME = 60L
    const val DEFAULT_SCHEDULE_TIME = 6 * 60 * 60L
    const val TIMEOUT = 5 * 60L

    // GPS状态变量
    @Volatile
    var latitude = DEFAULT_VALUE

    @Volatile
    var longitude = DEFAULT_VALUE

    @Volatile
    var isGpsValid = false

    @Volatile
    var firstLocation = true

    @Volatile
    var lastDistance = 0.0

    // GPS配置变量（已移动到地理围栏配置参数部分，避免重复）

    // 卫星信息
    @Volatile
    var satelliteCount = 0
        private set

    @Volatile
    var satelliteCountInUsed = 0
        private set

    @Volatile
    var satelliteAverageSnr = 0f
        private set

    // 地理围栏配置参数
    // GPS配置参数
    @Volatile
    var isCareGPS = true

    @Volatile
    var isLocate = true

    @Volatile
    private var updatesTime = DEFAULT_TIME

    @Volatile
    private var updatesDistance = DEFAULT_DISTANCE

    @Volatile
    private var updatesTimeCalc = DEFAULT_TIME

    @Volatile
    private var updatesDistanceCalc = DEFAULT_DISTANCE

    @Volatile
    private var scheduleTime = DEFAULT_SCHEDULE_TIME

    @Volatile
    private var maxLocateTimeout = TIMEOUT

    // 地理围栏配置参数
    @Volatile
    var validErrorDistance = 500f

    @Volatile
    var geoStatus = false
        get() = field
        set(value) {
            field = value
            // 立即保存到SharedPreferences（使用ApplicationContext）
            applicationContext?.let { context ->
                GeofenceStateManager.saveGeofenceEnabled(context, value)
            }
        }

    @Volatile
    var geoLockStatus = true

    @Volatile
    var geoLockMeter = 200

    @Volatile
    var geoLockMins = 5

    @Volatile
    var geoWipeStatus = false

    @Volatile
    var geoWipeMins = 5

    @Volatile
    var geoIsNearBeacon = false

    @Volatile
    var bGeoOutOfFence = true

    @Volatile
    var scanMode = 1 // 蓝牙常开模式

    // 位置监听器
    private var locationListener: LocationListener? = null
    private var locationManager: LocationManager? = null

    // 使用ApplicationContext避免内存泄漏
    private var applicationContext: Context? = null

    // GNSS状态回调
    private val gnssStatusCallback = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
        object : GnssStatus.Callback() {
            override fun onStarted() {
                super.onStarted()
                Logger.geo("GNSS定位开始")
            }

            override fun onSatelliteStatusChanged(status: GnssStatus) {
                super.onSatelliteStatusChanged(status)
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                    satelliteCount = status.satelliteCount
                    var countInUsed = 0
                    var totalSNR = 0f

                    for (i in 0 until satelliteCount) {
                        if (status.usedInFix(i)) {
                            countInUsed++
                            totalSNR += status.getCn0DbHz(i)
                        }
                    }

                    satelliteCountInUsed = countInUsed
                    satelliteAverageSnr = if (countInUsed > 0) totalSNR / countInUsed else 0f

                    Logger.geo("卫星状态更新: 总数=$satelliteCount, 使用中=$satelliteCountInUsed, 平均SNR=$satelliteAverageSnr")
                }
            }
        }
    } else null

    /**
     * 获取LocationManager实例
     */
    fun getLocationManager(context: Context): LocationManager {
        return locationManager ?: synchronized(this) {
            locationManager
                ?: (context.getSystemService(Context.LOCATION_SERVICE) as LocationManager).also {
                    locationManager = it
                }
        }
    }

    /**
     * 检查位置监听器是否活跃
     */
    fun isLocationListenerActive(): Boolean {
        return try {
            locationListener != null
        } catch (e: Exception) {
            Logger.geoE("$TAG 检查位置监听器状态失败", e)
            false
        }
    }

    /**
     * 获取GPS配置值
     */
    private fun getGpsConfigValue(key: String, defaultValue: String): String {
        return try {
            val context = SmartMdmServiceApp.instance
            val sharedPrefs = context.getSharedPreferences("gps_config", Context.MODE_PRIVATE)

            // 映射配置键名
            val prefKey = when (key) {
                "is_care_gps" -> "care"
                "update_distance" -> "minDistance"
                "update_time" -> "minUpdateTime"
                "schedule_time" -> "scheduleTime"
                "max_locate_timeout" -> "maxLocateTime"
                "store_latitude" -> "store_latitude"
                "store_longitude" -> "store_longitude"
                else -> key
            }

            sharedPrefs.getString(prefKey, defaultValue) ?: defaultValue
        } catch (e: Exception) {
            Logger.geoE("$TAG 获取GPS配置失败: $key", e)
            defaultValue
        }
    }

    /**
     * 获取纬度字符串
     */
    fun getLatitudeStr(): String = latitude.toString()

    /**
     * 获取经度字符串
     */
    fun getLongitudeStr(): String = longitude.toString()

    /**
     * 获取GPS位置信息JSON
     */
    fun getGpsLocation(context: Context): JSONObject {
        val tower = JSONObject()

        try {
            // 检查网络连接
            if (!isNetworkAvailable(context)) {
                Logger.geoW("网络不可用")  // 🌍 使用GPS专用日志
                if (isEmulator()) {
                    // 模拟器环境下提供模拟数据
                    return getSimulatorGpsData()
                } else {
                    tower.put("longitude", DEFAULT_VALUE.toString())
                    tower.put("latitude", DEFAULT_VALUE.toString())
                    return tower
                }
            }

            // 获取位置信息
            val shouldGetLocation = isCareGPS || (!isCareGPS && isLocate)
            if (shouldGetLocation) {
                val manager = getLocationManager(context)
                val isNetworkEnabled = manager.isProviderEnabled(LocationManager.NETWORK_PROVIDER)
                val isGpsEnabled = manager.isProviderEnabled(LocationManager.GPS_PROVIDER)

                Logger.geo("位置提供者状态: Network=$isNetworkEnabled, GPS=$isGpsEnabled")

                when {
                    isNetworkEnabled -> {
                        if (!getLocation(
                                context,
                                LocationManager.NETWORK_PROVIDER
                            ) && isGpsEnabled
                        ) {
                            getLocation(context, LocationManager.GPS_PROVIDER)
                        }
                    }

                    isGpsEnabled -> {
                        getLocation(context, LocationManager.GPS_PROVIDER)
                    }
                }
            }

            // 构建返回数据
            if (isEmulator() && latitude == DEFAULT_VALUE && longitude == DEFAULT_VALUE) {
                // 模拟器环境且没有获取到真实GPS数据，使用模拟数据
                Logger.geoI("模拟器环境，使用模拟GPS数据")  // 🌍 使用GPS专用日志
                return getSimulatorGpsData()
            } else {
                tower.put("longitude", longitude.toString())
                tower.put("latitude", latitude.toString())
                tower.put("satelliteCount", satelliteCount)
                tower.put("satelliteCountInUsed", satelliteCountInUsed)
                tower.put("satelliteAverSNR", String.format(Locale.US, "%.2f", satelliteAverageSnr))

                // 添加地理围栏相关字段
                addGeofenceFields(tower, context)
            }

        } catch (e: Exception) {
            Logger.geoE("获取GPS位置失败", e)  // 🌍 使用GPS专用日志
        }

        return tower
    }

    /**
     * 添加地理围栏相关字段到GPS信息中
     */
    private fun addGeofenceFields(tower: JSONObject, context: Context) {
        try {
            // 计算距离围栏中心的距离
            val storeLatStr = getGpsConfigValue("store_latitude", "")
            val storeLngStr = getGpsConfigValue("store_longitude", "")

            if (storeLatStr.isNotEmpty() && storeLngStr.isNotEmpty()) {
                val result = FloatArray(1)
                Location.distanceBetween(
                    storeLatStr.toDouble(),
                    storeLngStr.toDouble(),
                    latitude,
                    longitude,
                    result
                )
                tower.put("distance", result[0].toString())
                // 更新lastDistance
                lastDistance = result[0].toDouble()
            } else {
                tower.put("distance", lastDistance.toString())
            }

            // 地理围栏锁定状态
            tower.put("lockStatus", Constants.geofenceStatus.toString())

            // 围栏半径
            tower.put("lockMeter", geoLockMeter.toString())

            // 🔧从当前执行的地理围栏Profile中获取信息
            val currentProfile = getCurrentProfile(context)
            if (!TextUtils.isEmpty(currentProfile)) {
                try {
                    val profileJson = JSONObject(currentProfile)
                    val proId = profileJson.optString("proId", "")
                    val proName = profileJson.optString("proName", "")
                    tower.put("proId", proId)
                    tower.put("proName", proName)
                    Logger.geo("$TAG GPS上报信息: proId=$proId, proName=$proName")
                } catch (e: Exception) {
                    Logger.geoE("$TAG 解析当前Profile失败", e)
                    tower.put("proId", "")
                    tower.put("proName", "")
                }
            } else {
                Logger.geo("$TAG 当前Profile为空，信息设为空")
                tower.put("proId", "")
                tower.put("proName", "")
            }

        } catch (e: Exception) {
            Logger.geoE("添加地理围栏字段失败", e)
            // 设置默认值
            tower.put("distance", lastDistance.toString())
            tower.put("lockStatus", Constants.geofenceStatus.toString())
            tower.put("lockMeter", geoLockMeter.toString())
            tower.put("proId", "")
            tower.put("proName", "")
        }
    }

    /**
     * 通过GPS获取位置
     */
    fun getGPSLocation(context: Context): Location? {
        return try {
            val manager = getLocationManager(context)

            if (ActivityCompat.checkSelfPermission(
                    context,
                    Manifest.permission.ACCESS_FINE_LOCATION
                )
                != PackageManager.PERMISSION_GRANTED
            ) {
                return null
            }

            if (manager.isProviderEnabled(LocationManager.GPS_PROVIDER)) {
                manager.getLastKnownLocation(LocationManager.GPS_PROVIDER)
            } else {
                null
            }
        } catch (e: Exception) {
            Logger.geoE("获取GPS位置失败", e)
            null
        }
    }

    /**
     * 通过网络获取位置
     */
    fun getNetworkLocation(context: Context): Location? {
        return try {
            val manager = getLocationManager(context)

            if (ActivityCompat.checkSelfPermission(
                    context,
                    Manifest.permission.ACCESS_COARSE_LOCATION
                )
                != PackageManager.PERMISSION_GRANTED
            ) {
                return null
            }

            if (manager.isProviderEnabled(LocationManager.NETWORK_PROVIDER)) {
                Logger.geo("使用网络定位")
                manager.getLastKnownLocation(LocationManager.NETWORK_PROVIDER)
            } else {
                null
            }
        } catch (e: Exception) {
            Logger.geoE("获取网络位置失败", e)
            null
        }
    }

    /**
     * 获取最佳位置
     */
    fun getBestLocation(context: Context, criteria: Criteria? = null): Location? {
        return try {
            val manager = getLocationManager(context)
            val useCriteria = criteria ?: Criteria()
            val provider = manager.getBestProvider(useCriteria, true)

            if (provider.isNullOrEmpty()) {
                getNetworkLocation(context)
            } else {
                if (ActivityCompat.checkSelfPermission(
                        context,
                        Manifest.permission.ACCESS_COARSE_LOCATION
                    )
                    != PackageManager.PERMISSION_GRANTED &&
                    ActivityCompat.checkSelfPermission(
                        context,
                        Manifest.permission.ACCESS_FINE_LOCATION
                    )
                    != PackageManager.PERMISSION_GRANTED
                ) {
                    return null
                }
                manager.getLastKnownLocation(provider)
            }
        } catch (e: Exception) {
            Logger.geoE("获取最佳位置失败", e)
            null
        }
    }

    /**
     * 获取指定提供者的位置
     */
    private fun getLocation(context: Context, provider: String): Boolean {
        return try {
            val manager = getLocationManager(context)

            if (!manager.isProviderEnabled(provider)) {
                Logger.geo("位置提供者 $provider 未启用")
                return false
            }

            if (ActivityCompat.checkSelfPermission(
                    context,
                    Manifest.permission.ACCESS_FINE_LOCATION
                )
                != PackageManager.PERMISSION_GRANTED &&
                ActivityCompat.checkSelfPermission(
                    context,
                    Manifest.permission.ACCESS_COARSE_LOCATION
                )
                != PackageManager.PERMISSION_GRANTED
            ) {
                return false
            }

            val location = manager.getLastKnownLocation(provider)
            if (location != null) {
                Logger.geo("获取到位置: provider=$provider, lat=${location.latitude}, lng=${location.longitude}")
                latitude = location.latitude
                longitude = location.longitude
                isGpsValid = true
                return true
            } else {
                Logger.geo("$provider 提供者返回空位置")
            }

            false
        } catch (e: Exception) {
            Logger.geoE("获取位置失败: provider=$provider", e)
            false
        }
    }

    /**
     * 注册位置变化监听器
     */
    fun registerLocationChangeListener(context: Context) {
        // 检查网络连接
         if (!NetworkApi.isNetworkAvailable(context)) {
             Logger.geoE("$TAG 当前网络不可用")
             return
         }

        Logger.geo("$TAG GpsLocationManager, registerLocationChangeListener")

        // 保存ApplicationContext，避免内存泄漏
        applicationContext = context.applicationContext

        // 尝试使用FusedLocationProviderClient（如果可用）
        if (tryUseFusedLocationProvider(context)) {
            Logger.geo("$TAG 使用FusedLocationProviderClient进行定位")
            return
        }

        Logger.geo("$TAG 使用传统LocationManager进行定位")

        isCareGPS = "1" == getGpsConfigValue("is_care_gps", "0")
        updatesDistance =
            getGpsConfigValue("update_distance", DEFAULT_DISTANCE.toString()).toFloat()
        updatesTime = getGpsConfigValue("update_time", DEFAULT_TIME.toString()).toLong()
        scheduleTime = getGpsConfigValue("schedule_time", DEFAULT_SCHEDULE_TIME.toString()).toLong()
        maxLocateTimeout = getGpsConfigValue("max_locate_timeout", TIMEOUT.toString()).toLong()
        isLocate = true
        Logger.geo("$TAG registerLocationChangeListener: \nisCareGPS=$isCareGPS,\nupdatesDistance=$updatesDistance, \nupdatesTime=$updatesTime, \nscheduleTime=$scheduleTime, \nmaxLocateTimeout=$maxLocateTimeout, \nisLocate=$isLocate")

        // 如果不关注GPS，设置超时
        if (!isCareGPS) {
            Handler(Looper.getMainLooper()).postDelayed({
                unregisterLocationChangeListener()
            }, maxLocateTimeout * 1000)
        }

        if (locationListener == null) {
            locationListener = object : LocationListener {
                override fun onLocationChanged(location: Location) {
                    Logger.geo("$TAG registerLocationChangeListener: CONFIG updateTime=$updatesTimeCalc updateDistance=$updatesDistanceCalc")
                    // 使用统一的位置处理函数
                    handleLocationUpdate(location, context)
                }

                override fun onProviderDisabled(provider: String) {
                    isGpsValid = false
                    Logger.geo("$TAG 位置提供者被禁用: $provider")
                }

                override fun onProviderEnabled(provider: String) {
                    Logger.geo("$TAG 位置提供者被启用: $provider")
                }

                override fun onStatusChanged(provider: String, status: Int, extras: Bundle?) {
                    Logger.geo("$TAG 位置提供者状态变化: $provider, status=$status")
                }
            }

            // 获取经纬度间隔 30分钟 或移动50米
            Logger.geo("$TAG registerLocationChangeListener: start GPS locate request")

            // 检查权限
            if (ActivityCompat.checkSelfPermission(
                    context,
                    Manifest.permission.ACCESS_FINE_LOCATION
                ) != PackageManager.PERMISSION_GRANTED &&
                ActivityCompat.checkSelfPermission(
                    context,
                    Manifest.permission.ACCESS_COARSE_LOCATION
                ) != PackageManager.PERMISSION_GRANTED
            ) {
                return
            }

            val updatesTimeTemp = updatesTime * 5
            val updatesDistanceTemp = updatesDistance
            Logger.geo("$TAG registerLocationChangeListener updatesTime=${updatesTimeTemp}SECS, updatesDistance=${updatesDistanceTemp}METERS")

            updatesTimeCalc = updatesTimeTemp
            updatesDistanceCalc = updatesDistanceTemp

            val manager = getLocationManager(context)
            manager.requestLocationUpdates(
                LocationManager.GPS_PROVIDER,
                updatesTimeTemp * 1000,
                updatesDistanceTemp,
                locationListener!!,
                Looper.getMainLooper()
            )

            if (manager.isProviderEnabled(LocationManager.NETWORK_PROVIDER)) {
                Logger.geo("$TAG registerLocationChangeListener: support NETWORK PROVIDER")
                manager.requestLocationUpdates(
                    LocationManager.NETWORK_PROVIDER,
                    updatesTimeTemp * 1000,
                    updatesDistanceTemp,
                    locationListener!!,
                    Looper.getMainLooper()
                )
            }
        } else {
            // 获取经纬度间隔 30分钟 或移动50米
            Logger.geo("$TAG registerLocationChangeListener: start GPS locate request")

            // 检查权限
            if (ActivityCompat.checkSelfPermission(
                    context,
                    Manifest.permission.ACCESS_FINE_LOCATION
                ) != PackageManager.PERMISSION_GRANTED &&
                ActivityCompat.checkSelfPermission(
                    context,
                    Manifest.permission.ACCESS_COARSE_LOCATION
                ) != PackageManager.PERMISSION_GRANTED
            ) {
                return
            }

            val updatesTimeTemp = updatesTime
            val updatesDistanceTemp = updatesDistance
            Logger.geo("$TAG registerLocationChangeListener updatesTime=${updatesTimeTemp}SECS, updatesDistance=${updatesDistanceTemp}METERS")

            updatesTimeCalc = updatesTimeTemp
            updatesDistanceCalc = updatesDistanceTemp

            val manager = getLocationManager(context)
            if (manager.isProviderEnabled(LocationManager.GPS_PROVIDER)) {
                manager.requestLocationUpdates(
                    LocationManager.GPS_PROVIDER,
                    updatesTimeTemp * 1000,
                    updatesDistanceTemp,
                    locationListener!!
                )
            }
            if (manager.isProviderEnabled(LocationManager.NETWORK_PROVIDER)) {
                manager.requestLocationUpdates(
                    LocationManager.NETWORK_PROVIDER,
                    updatesTimeTemp * 1000,
                    updatesDistanceTemp,
                    locationListener!!
                )
            }
        }

        // 注册GNSS状态回调（需要在主线程中执行）
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N && gnssStatusCallback != null) {
            val contextRef = context // 捕获context引用
            Handler(Looper.getMainLooper()).post {
                try {
                    getLocationManager(contextRef).registerGnssStatusCallback(gnssStatusCallback)
                    Logger.geo("$TAG GNSS状态回调注册成功")
                } catch (e: Exception) {
                    Logger.geoE("$TAG GNSS状态回调注册失败", e)
                }
            }
        }
    }

    /**
     * 注销位置变化监听器
     */
    fun unregisterLocationChangeListener() {
        try {
            Logger.geo("$TAG GpsLocationManager, unregisterLocationChangeListener")

            if (locationListener != null) {
                locationManager?.removeUpdates(locationListener!!)
                locationListener = null
            }

            if (locationManager != null) {
                locationManager = null
            }

            // 释放资源后需要设置下次执行时间
            if (!isCareGPS) {
                isLocate = false
                setScheduleGPS()
            }

        } catch (e: Exception) {
            Logger.geoE("$TAG 注销位置监听器失败", e)
        }
    }

    /**
     * 设置GPS定时调度
     */
    @SuppressLint("MissingPermission", "ScheduleExactAlarm")
    private fun setScheduleGPS() {
        try {
            Logger.geo("$TAG GpsLocationManager, setScheduleGPS")

            val context = SmartMdmServiceApp.instance

            val pendingIntent = BroadcastSender.createDynamicPendingIntent(
                context,
                "com.dspread.mdm.service.broadcast.receivers.GeofenceReceiver",
                0)

            val alarmManager = context.getSystemService(Context.ALARM_SERVICE) as AlarmManager
            alarmManager.cancel(pendingIntent)

            val triggerTime = System.currentTimeMillis() + scheduleTime * 1000

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                alarmManager.setExactAndAllowWhileIdle(
                    AlarmManager.RTC_WAKEUP,
                    triggerTime,
                    pendingIntent
                )
            } else {
                alarmManager.setExact(AlarmManager.RTC_WAKEUP, triggerTime, pendingIntent)
            }

            Logger.geoW("$TAG 设置GPS定时调度，${scheduleTime}秒后执行")

        } catch (e: Exception) {
            Logger.geoE("$TAG 设置GPS定时调度失败", e)
        }
    }

    /**
     * 获取GPS监听器
     */
    fun getGpsListener(): LocationListener? {
        return locationListener
    }

    /**
     * 显示GPS调试Toast消息
     */
    private fun showGPSToastMsg(msg: String) {
        try {
            if (isApkInDebug()) {
                Handler(Looper.getMainLooper()).post {
                    Toast.makeText(
                        SmartMdmServiceApp.instance.applicationContext,
                        msg,
                        Toast.LENGTH_LONG
                    ).show()
                }
            }
        } catch (e: Exception) {
            Logger.geoE("$TAG 显示GPS Toast消息失败", e)
        }
    }

    /**
     * 检查应用是否处于Debug模式
     */
    private fun isApkInDebug(): Boolean {
        return try {
            val context = SmartMdmServiceApp.instance
            val info = context.applicationInfo
            (info.flags and android.content.pm.ApplicationInfo.FLAG_DEBUGGABLE) != 0
        } catch (e: Exception) {
            Logger.geoE("$TAG 检查Debug模式失败", e)
            false
        }
    }

    /**
     * 检查网络是否可用
     */
    private fun isNetworkAvailable(context: Context): Boolean {
        return try {
            val connectivityManager =
                context.getSystemService(Context.CONNECTIVITY_SERVICE) as android.net.ConnectivityManager
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                val network = connectivityManager.activeNetwork
                val capabilities = connectivityManager.getNetworkCapabilities(network)
                capabilities?.hasCapability(android.net.NetworkCapabilities.NET_CAPABILITY_INTERNET) == true
            } else {
                @Suppress("DEPRECATION")
                val networkInfo = connectivityManager.activeNetworkInfo
                networkInfo?.isConnected == true
            }
        } catch (e: Exception) {
            Logger.geoE("检查网络状态失败", e)
            true // 默认认为网络可用
        }
    }

    /**
     * 在模拟器环境下提供模拟GPS数据
     */
    private fun getSimulatorGpsData(): JSONObject {
        return JSONObject().apply {
            put("longitude", "-122.084")
            put("latitude", "37.421998333333335")
            put("satelliteCount", 6)
            put("satelliteCountInUsed", 0)
            put("satelliteAverSNR", "NaN")
        }
    }

    /**
     * 检查是否在模拟器环境
     */
    private fun isEmulator(): Boolean {
        return Build.FINGERPRINT.startsWith("generic") ||
                Build.FINGERPRINT.startsWith("unknown") ||
                Build.MODEL.contains("google_sdk") ||
                Build.MODEL.contains("Emulator") ||
                Build.MODEL.contains("Android SDK built for x86") ||
                Build.MANUFACTURER.contains("Genymotion") ||
                Build.BRAND.startsWith("generic") && Build.DEVICE.startsWith("generic") ||
                "google_sdk" == Build.PRODUCT
    }

// ==================== 地理围栏核心业务方法 ====================

    /**
     * 设置当前GPS状态
     */
    fun setCurrentGPSStatus(context: Context, status: Int) {
        Logger.geo("$TAG setCurrentGPSStatus = $status")
        Constants.geofenceStatus = status
        // 保存状态到SharedPreferences
        GeofenceStateManager.saveGeofenceStatus(context, status)
    }

    /**
     * 计算两点间距离（米）- 使用Haversine公式
     */
    fun getDistanceFromTwoPoints(lat1: Double, lon1: Double, lat2: Double, lon2: Double): Double {
        val earthRadius = 6.371229e6 // 地球半径（米）
        val degreesToRadians = PI / 180.0

        val dLat = (lat2 - lat1) * degreesToRadians
        val dLon = (lon2 - lon1) * degreesToRadians

        val a = sin(dLat / 2) * sin(dLat / 2) +
                cos(lat1 * degreesToRadians) * cos(lat2 * degreesToRadians) *
                sin(dLon / 2) * sin(dLon / 2)

        val c = 2 * atan2(sqrt(a), sqrt(1 - a))
        val h = a

        return earthRadius * 2 * atan2(sqrt(h), sqrt(1 - h))
    }

    /**
     * 显示离开围栏警告
     */
    fun showOutOfGeofenceWarning(context: Context, status: Int) {
        val intent = Intent().apply {
            action = BroadcastActions.ACTION_GEOFENCING_DETECTED_WARNING
            putExtra("warning_status", status)
        }
        sendGeofenceBroadcast(context, intent)
    }

    /**
     * 关闭离开围栏警告
     */
    fun closeOutOfGeofenceWarning(context: Context) {
        val intent = Intent().apply {
            action = BroadcastActions.ACTION_GEOFENCING_DETECTED_WARNING_CLOSE
        }
        sendGeofenceBroadcast(context, intent)
    }

    /**
     * 显示锁屏界面
     */
    fun showOutOfGeofenceLockScreen(context: Context) {
        Logger.geo("$TAG Broadcast to start LockScreen Activity")

        // 先启动LockScreenActivity，然后发送广播
        gotoLockDeviceScreen(context)

        // 延迟发送广播，确保Activity已启动并注册了接收器
        Handler(Looper.getMainLooper()).postDelayed({
            val intent = Intent().apply {
                action = BroadcastActions.ACTION_GEOFENCING_DETECTED_LOCK
            }
            sendGeofenceBroadcast(context, intent)
        }, 1000)
    }

    /**
     * 关闭锁屏界面
     */
    fun closeOutOfGeofenceLockScreen(context: Context) {
        val intent = Intent().apply {
            action = BroadcastActions.ACTION_CLOSE_LOCKSCREEN
        }
        sendGeofenceBroadcast(context, intent)
    }

    /**
     * 启动锁屏界面
     */
    fun gotoLockDeviceScreen(context: Context) {
        try {
            Logger.geo("$TAG gotoLockDeviceScreen geoLockStatus=$geoLockStatus, geofenceStatus=${Constants.geofenceStatus}")

            // 强制启动锁屏，不再检查geoLockStatus（因为状态已经是LOCK_SCREEN）
            if (Constants.geofenceStatus == LOCK_SCREEN || Constants.geofenceStatus == WIPE_DATA) {
                val intent = Intent().apply {
                    setClassName(
                        context.packageName,
                        "com.dspread.mdm.service.ui.activity.LockScreenActivity"
                    )
                    flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TOP
                }
                context.startActivity(intent)
                Logger.geo("$TAG LockScreenActivity启动成功 (强制启动)")
            } else if (geoLockStatus) {
                val intent = Intent().apply {
                    setClassName(
                        context.packageName,
                        "com.dspread.mdm.service.ui.activity.LockScreenActivity"
                    )
                    flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TOP
                }
                context.startActivity(intent)
                Logger.geo("$TAG LockScreenActivity启动成功 (geoLockStatus检查)")
            } else {
                Logger.geo("$TAG geoLockStatus=false且状态不是LOCK_SCREEN，不启动LockScreenActivity")
            }
        } catch (e: Exception) {
            Logger.geoE("$TAG 启动LockScreenActivity失败", e)
        }
    }

    /**
     * 启动锁屏界面（输入模式）
     */
    fun gotoLockDeviceScreenInput(context: Context) {
        try {
            Logger.geo("$TAG gotoLockDeviceScreenInput geoLockStatus=$geoLockStatus")
            if (geoLockStatus) {
                val intent = Intent().apply {
                    setClassName(
                        context.packageName,
                        "com.dspread.mdm.service.ui.activity.LockScreenActivity"
                    )
                    flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TOP
                    putExtra("mode", "1")
                }
                context.startActivity(intent)
                Logger.geo("$TAG LockScreenActivity(输入模式)启动成功")
            } else {
                Logger.geo("$TAG geoLockStatus=false，不启动LockScreenActivity")
            }
        } catch (e: Exception) {
            Logger.geoE("$TAG 启动LockScreenActivity(输入模式)失败", e)
        }
    }

    /**
     * 确保LockScreenActivity正在运行
     */
    private fun ensureLockScreenActivityRunning(context: Context) {
        try {
            Logger.geo("$TAG ensureLockScreenActivityRunning: geofenceStatus=${Constants.geofenceStatus}, geoLockStatus=$geoLockStatus")

            // 检查当前地理围栏状态是否需要LockScreenActivity
            if (Constants.geofenceStatus == LOCK_SCREEN || Constants.geofenceStatus == WIPE_DATA) {
                Logger.geo("$TAG 当前状态需要LockScreenActivity，确保其正在运行")
                gotoLockDeviceScreen(context)

                // 给Activity一些时间启动和注册广播接收器
                Thread.sleep(500)
            } else {
                Logger.geo("$TAG 当前状态不需要LockScreenActivity: geofenceStatus=${Constants.geofenceStatus}")
            }
        } catch (e: Exception) {
            Logger.geoE("$TAG 确保LockScreenActivity运行失败", e)
        }
    }

    /**
     * 发送地理围栏相关广播
     */
    private fun sendGeofenceBroadcast(context: Context, intent: Intent) {
        try {
            Logger.geo("$TAG 发送地理围栏广播: ${intent.action}")
            val action = intent.action ?: return

            // 提取Intent中的extras并转换为BroadcastUtils格式
            val extras = mutableListOf<Pair<String, Any>>()
            intent.extras?.let { bundle ->
                for (key in bundle.keySet()) {
                    bundle.get(key)?.let { value ->
                        extras.add(key to value)
                    }
                }
            }

            if (extras.isNotEmpty()) {
                BroadcastSender.sendBroadcast(context, action, *extras.toTypedArray())
            } else {
                BroadcastSender.sendBroadcast(context, action)
            }
        } catch (e: Exception) {
            Logger.geoE("$TAG 发送广播失败", e)
        }
    }


    /**
     * 判断是否满足进入围栏的条件 - 多重定位策略
     */
    fun conditionEnterGeoFence(context: Context): Boolean {
        // 策略1: WiFi辅助定位
        if (isWifiEnterGeofence(context)) {
            Logger.geo("$TAG conditionEnterGeoFence WiFi 匹配成功")
            return true
        }

        // 策略2: 蓝牙信标检测
        if (geoIsNearBeacon) {
            Logger.geo("$TAG conditionEnterGeoFence Beacon 匹配成功")
            return true
        }

        return false
    }

    /**
     * WiFi辅助定位检查
     */
    fun isWifiEnterGeofence(context: Context): Boolean {
        try {
            Logger.geo("$TAG 检查WiFi辅助定位")

            val curIP = WiFiManagerApi.getWifiIP(context)
            val curSSID = WiFiManagerApi.getCurConnectSSID(context)

            Logger.geo("$TAG 当前WiFi信息: SSID=$curSSID, IP=$curIP")
            Logger.geo("$TAG 商店信息: storeIp=${Constants.storeIp}, storeSsid=${Constants.storeSsid}")

            // 优先检查IP匹配
            if (!TextUtils.isEmpty(Constants.storeIp)) {
                if (!TextUtils.isEmpty(curIP) && NetworkApi(context).isMachedIP(
                        curIP,
                        Constants.storeIp
                    )
                ) {
                    Logger.geo("$TAG conditionEnterGeoFence IP matched: $curIP 匹配 ${Constants.storeIp}")
                    return true
                }
            } else {
                // 如果没有配置IP，则检查SSID匹配
                if (!TextUtils.isEmpty(curSSID)) {
                    // 检查是否匹配商店SSID
                    if (!TextUtils.isEmpty(Constants.storeSsid) && curSSID == Constants.storeSsid) {
                        Logger.geo("$TAG conditionEnterGeoFence 商店SSID matched: $curSSID")
                        return true
                    }

                    // 检查是否在WiFi Profile列表中
                    if (checkIsWifiProfileSSID(context, curSSID ?: "")) {
                        Logger.geo("$TAG conditionEnterGeoFence WiFi Profile SSID matched: $curSSID")
                        return true
                    }
                }
            }

            Logger.geo("$TAG WiFi辅助定位未匹配")
            return false
        } catch (e: Exception) {
            Logger.geoE("$TAG WiFi辅助定位检查失败", e)
            return false
        }
    }

    /**
     * 注册位置变化监听器（扩展版本，用于离开围栏时的高频监控）
     */
    fun registerLocationChangeListenerEx(context: Context) {
        try {
            Logger.geo("$TAG 注册扩展位置监听器（高频监控）")

            // 检查权限
            if (ActivityCompat.checkSelfPermission(
                    context,
                    Manifest.permission.ACCESS_FINE_LOCATION
                )
                != PackageManager.PERMISSION_GRANTED &&
                ActivityCompat.checkSelfPermission(
                    context,
                    Manifest.permission.ACCESS_COARSE_LOCATION
                )
                != PackageManager.PERMISSION_GRANTED
            ) {
                Logger.geoE("缺少位置权限")
                return
            }

            // 先注销现有监听器
            unregisterLocationChangeListener()

            // 设置高频监控参数
            updatesTimeCalc = updatesTime
//            updatesDistanceCalc = 0f // 距离间隔设为0，更敏感
            updatesDistanceCalc = updatesDistance // 距离间隔设为0，更敏感

            val manager = getLocationManager(context)
            val updateTimeMs = updatesTime * 1000

            Logger.geo("$TAG 启动高频GPS监控: 时间间隔=${updatesTime}秒, 距离间隔=0米")

            if (locationListener == null) {
                locationListener = createLocationListener(context)
            }

            // 注册GPS提供者
            if (manager.isProviderEnabled(LocationManager.GPS_PROVIDER)) {
                manager.requestLocationUpdates(
                    LocationManager.GPS_PROVIDER,
                    updateTimeMs,
//                    0f,
                    updatesDistanceCalc,
                    locationListener!!
                )
            }

            // 注册网络提供者
            if (manager.isProviderEnabled(LocationManager.NETWORK_PROVIDER)) {
                manager.requestLocationUpdates(
                    LocationManager.NETWORK_PROVIDER,
                    updateTimeMs,
//                    0f,
                    updatesDistanceCalc,
                    locationListener!!
                )
            }

        } catch (e: Exception) {
            Logger.geoE("$TAG 注册扩展位置监听器失败", e)  // 🌍 使用GPS专用日志
        }
    }

    /**
     * 创建位置监听器
     */
    private fun createLocationListener(context: Context): LocationListener {
        return object : LocationListener {
            override fun onLocationChanged(location: Location) {
                if (location != null) {
                    Logger.geo("位置变化: lat=${location.latitude}, lng=${location.longitude}")  // 🌍 使用GPS专用日志
                    Logger.geo("GPS精度: ${location.accuracy}米")  // 🌍 使用GPS专用日志

                    latitude = location.latitude
                    longitude = location.longitude
                    isGpsValid = true

                    // 地理围栏检查逻辑
                    if (geoStatus) {
                        try {
                            // 从SharedPreferences读取存储的围栏中心坐标
                            val latitudeStr = getGpsConfigValue("store_latitude", "")
                            val longitudeStr = getGpsConfigValue("store_longitude", "")

                            if (!TextUtils.isEmpty(latitudeStr) && !TextUtils.isEmpty(longitudeStr)) {
                                val result = FloatArray(1)
                                Logger.geo("$TAG **** distanceBetween: CENTER longitude=$longitudeStr latitude=$latitudeStr")
                                Logger.geo("$TAG **** distanceBetween: CURRENT longitude=$longitude latitude=$latitude")

                                Location.distanceBetween(
                                    latitudeStr.toDouble(),
                                    longitudeStr.toDouble(),
                                    latitude,
                                    longitude,
                                    result
                                )

                                Logger.geo("$TAG **** distanceBetween: ${result[0]} Radius = $geoLockMeter")
                                showGPSToastMsg(
                                    "**** distanceBetween: CENTER longitude=$longitudeStr latitude=$latitudeStr" +
                                            "\n**** distanceBetween: CURRENT longitude=$longitude latitude=$latitude" +
                                            "\n**** distanceBetween: ${result[0]} Radius = $geoLockMeter LastDistance = $lastDistance" +
                                            " ErrorDistance = $validErrorDistance"
                                )

                                val distance = result[0].toDouble()

                                // 检查与上次距离的差异，修复围栏状态变化检测
                                val distanceDiff = Math.abs(distance - lastDistance)
                                if (!firstLocation && distanceDiff > validErrorDistance) {
                                    // 检查是否是真实的围栏状态变化
                                    val wasInside = lastDistance <= geoLockMeter
                                    val nowInside = distance <= geoLockMeter

                                    if (wasInside != nowInside) {
                                        // 围栏状态发生变化，这是真实变化，不应忽略
                                        Logger.geo("$TAG 围栏状态变化: 之前${if (wasInside) "在围栏内" else "在围栏外"}(${lastDistance.toInt()}米), 现在${if (nowInside) "在围栏内" else "在围栏外"}(${distance.toInt()}米)")
                                        lastDistance = distance
                                    } else {
                                        // 同一状态下的大幅跳跃，可能是GPS误差
                                        Logger.geo("$TAG GPS跳跃: 距离差异${distanceDiff.toInt()}米 > 阈值${validErrorDistance.toInt()}米, 状态未变化，忽略")
                                        lastDistance = distance
                                        return
                                    }
                                } else {
                                    lastDistance = distance
                                }

                                // 忽略第一次定位（防止重启后未连WiFi时的误报）
                                if (firstLocation) {
                                    firstLocation = false
                                    Logger.geo("$TAG ignore the first location")
                                    return // 针对在reboot后，在未连上wifi时候之前，多台终端出现out of zone的情况，ignore the first Location
                                }

                                if (distance > geoLockMeter) {
                                    Logger.geoW("$TAG geofenceStatus: ${Constants.geofenceStatus}")
                                    when (Constants.geofenceStatus) {
                                        IN_ZONE -> {
                                            if (!conditionEnterGeoFence(context)) {
                                                Logger.geo("$TAG 设备离开围栏，发送ACTION_OUT_OF_GEOFENCE广播")

//                                                // 发送离开围栏广播
//                                                val intent =
//                                                    Intent(BroadcastActions.ACTION_OUT_OF_GEOFENCE)
//                                                sendGeofenceBroadcast(context, intent)

                                                showOutOfGeofenceWarning(
                                                    context,
                                                    WARNING_STATUS_IN_FENCE
                                                )
                                                setCurrentGPSStatus(context, OUT_OF_ZONE)
                                                // 确保在主线程中注册扩展位置监听器
                                                Handler(Looper.getMainLooper()).post {
                                                    registerLocationChangeListenerEx(context)
                                                }
                                                bGeoOutOfFence = true
                                            }
                                        }

                                        OUT_OF_ZONE -> {
                                            if (conditionEnterGeoFence(context)) {
                                                closeOutOfGeofenceWarning(context)
                                                setCurrentGPSStatus(context, IN_ZONE)
                                            } else {
                                                showOutOfGeofenceWarning(
                                                    context,
                                                    WARNING_STATUS_OUT_OF_FENCE
                                                )
                                            }
                                        }
                                    }
                                } else {
                                    bGeoOutOfFence = false
                                    when (Constants.geofenceStatus) {
                                        OUT_OF_ZONE -> {
                                            setCurrentGPSStatus(context, IN_ZONE)
                                            registerLocationChangeListener(context)
                                            closeOutOfGeofenceWarning(context)
                                        }

                                        LOCK_SCREEN, WIPE_DATA -> {
                                            val intent =
                                                Intent(BroadcastActions.ACTION_ENTER_GEOFENCE)
                                            sendGeofenceBroadcast(context, intent)
                                        }
                                    }
                                }
                            }
                        } catch (e: Exception) {
                            Logger.geoE("$TAG 地理围栏检查失败", e)
                        }
                    }
                } else {
                    isGpsValid = false
                }
            }

            override fun onProviderDisabled(provider: String) {
                isGpsValid = false
                Logger.geo("位置提供者被禁用: $provider")
            }

            override fun onProviderEnabled(provider: String) {
                Logger.geo("位置提供者被启用: $provider")
            }

            override fun onStatusChanged(provider: String, status: Int, extras: Bundle?) {
                Logger.geo("位置提供者状态变化: $provider, status=$status")
            }
        }
    }

    /**
     * 主动执行地理围栏检查
     * 用于定时器触发的主动检查
     */
    fun performActiveGeofenceCheck(context: Context) {
            try {
                Logger.geo("$TAG 执行主动地理围栏检查")

                if (!geoStatus) {
                    Logger.geo("$TAG 地理围栏功能未启用，跳过检查")
                    return
                }

                // 获取当前位置
                if (latitude != DEFAULT_VALUE && longitude != DEFAULT_VALUE) {
                    Logger.geo("$TAG 使用当前缓存位置进行检查: lat=$latitude, lng=$longitude")

                    // 创建Location对象进行检查
                    val currentLocation = Location("cached").apply {
                        latitude = <EMAIL>
                        longitude = <EMAIL>
                        accuracy = 50f // 默认精度
                    }

                    performGeofenceCheck(currentLocation, context)
                } else {
                    Logger.geo("$TAG 当前位置不可用，触发一次位置请求")
                    // 如果没有当前位置，触发一次位置请求（确保在主线程中执行）
                    Handler(Looper.getMainLooper()).post {
                        registerLocationChangeListener(context)
                    }
                }
            } catch (e: Exception) {
                Logger.geoE("$TAG 主动地理围栏检查失败", e)
            }
        }

    /**
     * 执行地理围栏Profile配置
     */
    fun executeGeoProfile(context: Context, geoInfo: JSONObject?) {
        try {
            Logger.geo("$TAG executeGeoProfile = $geoInfo")

            if (geoInfo == null) {
                geoStatus = false
                saveCurrentProfile(context, "")
                return
            }

            geoStatus = true

            // 检查disable标签
            val currentProfile = getCurrentProfile(context)
            if (!TextUtils.isEmpty(currentProfile)) {
                val currentProfileId = JSONObject(currentProfile).getString("proId")
                val disableTag = "1" == geoInfo.optString("disableTag")
                if (disableTag) {
                    if (currentProfileId == geoInfo.getString("proId")) {
                        // 如果disable的是当前执行的，执行default
                        Logger.geo("$TAG disable当前执行的Profile，执行默认Profile")
                        val defaultProfile = getDefaultGeoProfile(context)
                        if (defaultProfile != null) {
                            executeGeoProfile(context, defaultProfile)
                        } else {
                            Logger.geo("$TAG 未找到默认Profile，禁用地理围栏")
                            executeGeoProfile(context, null)
                        }
                        return
                    } else {
                        // 如果disable的不是当前执行的，直接忽略
                        Logger.geo("$TAG disable ID is not current executing ID，ignore $geoInfo")
                        return
                    }
                }
            }

            // roamingTag处理
            val roamingTag = "1" == geoInfo.optString("roamingTag")
            if (roamingTag) {
                geoStatus = false
                if (Constants.geofenceStatus >= LOCK_SCREEN || Constants.geofenceStatus >= WIPE_DATA) {
                    closeOutOfGeofenceLockScreen(context)
                } else if (Constants.geofenceStatus == OUT_OF_ZONE) {
                    closeOutOfGeofenceWarning(context)
                    closeOutOfGeofenceLockScreen(context)
                }
                setCurrentGPSStatus(context, ROAMING)
            } else {
                if (Constants.geofenceStatus == ROAMING) {
                    setCurrentGPSStatus(context, IN_ZONE)
                }
            }

            // lockMeter参数处理
            val lockMeters = geoInfo.optString("lockMeter")
            if (!TextUtils.isEmpty(lockMeters)) {
                geoLockMeter = lockMeters.toInt()
            }

            // lockMin参数处理
            val lockMins = geoInfo.optString("lockMin")
            if (!TextUtils.isEmpty(lockMins)) {
                val newLockMins = lockMins.toInt()
                if (geoLockMins != newLockMins) {
                    geoLockMins = newLockMins
                    if (Constants.geofenceStatus == OUT_OF_ZONE) {
                        BroadcastSender.sendBroadcast(context, BroadcastActions.ACTION_GEOFENCING_RESET_LOCKSCREEN_TIMER)
                    }
                }
            }

            // wipe参数处理
            val wipeData = "1" == geoInfo.optString("wipeStatus")
            val wipeMins = geoInfo.optString("wipeMin")
            val nWipeMins = if (!TextUtils.isEmpty(wipeMins)) wipeMins.toInt() else -1

            if (Constants.geofenceStatus == LOCK_SCREEN) {
                if (geoWipeStatus != wipeData || (nWipeMins > 0 && geoWipeMins != nWipeMins)) {
                    // 在LOCK_SCREEN状态下也要更新内存变量
//                    geoWipeStatus = wipeData
//                    if (nWipeMins > 0) {
//                        geoWipeMins = nWipeMins
//                    }

                    BroadcastSender.sendBroadcast(
                        context,
                        BroadcastActions.ACTION_WIPE_PARAM_CHANGE,
                        "GEO_WIPE_STATUS" to wipeData,
                        "GEO_WIPE_MINS" to nWipeMins
                    )

                    // LOCK_SCREEN状态下也要保存wipe参数，确保重启后配置一致
                    val prefs = context.getSharedPreferences("geofence_config", Context.MODE_PRIVATE)
                    prefs.edit().apply {
                        putString("lockMeter", geoInfo.optString("lockMeter"))
                        putString("lockMin", geoInfo.optString("lockMin"))
//                        putString("wipeStatus", geoInfo.optString("wipeStatus"))  // 添加wipe状态持久化
//                        putString("wipeMin", geoInfo.optString("wipeMin"))        // 添加wipe时间持久化
                        apply()
                    }

                    Logger.geo("$TAG LOCK_SCREEN状态下wipe参数更新完成: geoWipeStatus=$geoWipeStatus, geoWipeMins=$geoWipeMins")
                }
            } else {
                // 正常情况：更新内存变量
                geoWipeStatus = wipeData
                geoWipeMins = nWipeMins

                // 正常情况：保存所有参数
                val prefs = context.getSharedPreferences("geofence_config", Context.MODE_PRIVATE)
                prefs.edit().apply {
                    putString("lockMeter", geoInfo.optString("lockMeter"))
                    putString("lockMin", geoInfo.optString("lockMin"))
                    putString("wipeStatus", geoInfo.optString("wipeStatus"))
                    putString("wipeMin", geoInfo.optString("wipeMin"))
                    apply()
                }
            }

            // 保存当前Profile
            saveCurrentProfile(context, geoInfo.toString())

            // 添加：数据一致性验证
//            validateDataConsistency(context)

            // 上报数据信息
            WsMessageSender.uploadDataInfoForGeo(System.currentTimeMillis())

            Logger.geo("$TAG geoStatus = $geoStatus")

        } catch (e: Exception) {
            Logger.geoE("$TAG 执行地理围栏Profile失败", e)
        }
    }

    /**
     * 验证数据一致性
     * 确保内存变量与SharedPreferences中的值一致
     */
    private fun validateDataConsistency(context: Context) {
        try {
            val prefs = context.getSharedPreferences("geofence_config", Context.MODE_PRIVATE)

            // 验证lockMeter一致性
            val savedLockMeter = prefs.getString("lockMeter", "")?.toIntOrNull() ?: -1
            if (savedLockMeter != -1 && savedLockMeter != geoLockMeter) {
                Logger.geoW("$TAG lockMeter数据不一致: 内存=$geoLockMeter, SP=$savedLockMeter")
            }

            // 验证lockMin一致性
            val savedLockMin = prefs.getString("lockMin", "")?.toIntOrNull() ?: -1
            if (savedLockMin != -1 && savedLockMin != geoLockMins) {
                Logger.geoW("$TAG lockMin数据不一致: 内存=$geoLockMins, SP=$savedLockMin")
            }

            // 验证wipeStatus一致性
            val savedWipeStatus = "1" == prefs.getString("wipeStatus", "0")
            if (savedWipeStatus != geoWipeStatus) {
                Logger.geoW("$TAG wipeStatus数据不一致: 内存=$geoWipeStatus, SP=$savedWipeStatus")
            }

            // 验证wipeMin一致性
            val savedWipeMin = prefs.getString("wipeMin", "")?.toIntOrNull() ?: -1
            if (savedWipeMin != -1 && savedWipeMin != geoWipeMins) {
                Logger.geoW("$TAG wipeMin数据不一致: 内存=$geoWipeMins, SP=$savedWipeMin")
            }

            Logger.geo("$TAG 数据一致性验证完成")

        } catch (e: Exception) {
            Logger.geoE("$TAG 数据一致性验证失败", e)
        }
    }

    /**
     * 初始化地理围栏Profile
     * 检查Profile的有效期并执行相应的Profile
     */
    fun initGeoProfile(context: Context, geoInfo: JSONObject?) {
        Logger.geo("$TAG initGeoProfile = $geoInfo")

        if (geoInfo == null) {
            geoStatus = false
            GeofenceStateManager.saveCurrentProfile(context, "")
            return
        }

        try {
            val beginDateStr = geoInfo.getString("beginDate")
            val endDateStr = geoInfo.getString("endDate")
            val nowTime = System.currentTimeMillis()

            val (begTime, endTime) = if (isNumeric(beginDateStr) && isNumeric(endDateStr)) {
                Pair(beginDateStr.toLong(), endDateStr.toLong())
            } else {
                val sdf = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault())
                Pair(sdf.parse(beginDateStr)?.time ?: 0L, sdf.parse(endDateStr)?.time ?: 0L)
            }

            when {
                begTime < nowTime && endTime > nowTime -> {
                    // Profile在有效期内，直接执行
                    executeGeoProfileForInit(context, geoInfo)
                    return
                }
                nowTime > endTime -> {
                    // Profile已过期，查找并执行默认Profile
                    val prefs = PreferenceManager.getDefaultSharedPreferences(context)
                    val localGeoProfileListStr = prefs.getString("WEBSOCKET_GEOFENCE_PROFILE_LIST", "") ?: ""

                    Logger.geo("$TAG LOCAL: localGeoProfileListStr --> $localGeoProfileListStr")

                    if (localGeoProfileListStr.isNotEmpty()) {
                        Logger.geo("$TAG LOCAL isn't empty，loop to delete")
                        val localProfileList = JSONArray(localGeoProfileListStr)

                        for (i in 0 until localProfileList.length()) {
                            val profileJsonObj = localProfileList.getJSONObject(i)
                            if ("1" == profileJsonObj.optString("isDefault")) {
                                executeGeoProfileForInit(context, profileJsonObj)
                                return
                            }
                        }

                        // 如果找不到default，则没有可执行的
                        executeGeoProfileForInit(context, null)
                    }
                }
                else -> {
                    // Profile还未到执行时间
                    Logger.geo("$TAG Profile还未到执行时间: beginTime=$begTime, nowTime=$nowTime")
                }
            }
        } catch (e: Exception) {
            Logger.geoE("$TAG initGeoProfile执行失败", e)
        }
    }

    /**
     * 检查字符串是否为纯数字
     */
    private fun isNumeric(str: String): Boolean {
        return str.all { it.isDigit() }
    }

    /**
     * 初始化时执行地理围栏Profile
     */
    fun executeGeoProfileForInit(context: Context, geoInfo: JSONObject?) {
        Logger.geo("$TAG executeGeoProfileForInit = $geoInfo")

        if (geoInfo == null) {
            geoStatus = false
            saveCurrentProfile(context, "")
            return
        }

        geoStatus = true

        // executeGeoProfileForInit执行的就是current，如果是disable，直接执行default
        val disableTag = "1" == geoInfo.optString("disableTag")
        if (disableTag) {
            val defaultProfile = getDefaultGeoProfile(context)
            executeGeoProfile(context, defaultProfile)
            return
        }

        // roamingTag处理
        val roamingTag = "1" == geoInfo.optString("roamingTag")
        if (roamingTag) {
            geoStatus = false
            if (Constants.geofenceStatus >= LOCK_SCREEN) {
                closeOutOfGeofenceLockScreen(context)
            }
            setCurrentGPSStatus(context, ROAMING)
        } else {
            if (Constants.geofenceStatus == ROAMING) {
                setCurrentGPSStatus(context, IN_ZONE)
            }
        }

        // lockMeter参数处理
        val lockMeters = geoInfo.optString("lockMeter")
        if (!TextUtils.isEmpty(lockMeters)) {
            geoLockMeter = lockMeters.toInt()
        }

        // lockMin参数处理
        val lockMins = geoInfo.optString("lockMin")
        if (!TextUtils.isEmpty(lockMins)) {
            geoLockMins = lockMins.toInt()
        }

        // wipe参数处理
        val wipeData = "1" == geoInfo.optString("wipeStatus")
        val wipeMins = geoInfo.optString("wipeMin")
        val nWipeMins = if (!TextUtils.isEmpty(wipeMins)) wipeMins.toInt() else -1

        geoWipeStatus = wipeData
        geoWipeMins = nWipeMins

        // 保存所有参数到SharedPreferences
        val prefs = context.getSharedPreferences("geofence_config", Context.MODE_PRIVATE)
        prefs.edit().apply {
            putString("lockMeter", geoInfo.optString("lockMeter"))
            putString("lockMin", geoInfo.optString("lockMin"))
            putString("wipeStatus", geoInfo.optString("wipeStatus"))
            putString("wipeMin", geoInfo.optString("wipeMin"))
            apply()
        }
    }

    /**
     * 更新地理围栏配置参数
     */
    private fun updateGeofenceConfig(context: Context, geoInfo: JSONObject) {
        try {
            Logger.geo("$TAG 更新地理围栏配置参数...")
            Logger.geo("$TAG 接收到的配置: $geoInfo")

            // 锁屏距离（米）
            val lockMeters = geoInfo.optString("lockMeter")
            if (!TextUtils.isEmpty(lockMeters)) {
                geoLockMeter = lockMeters.toInt()
                Logger.geo("$TAG 设置锁屏距离: ${geoLockMeter}米")
            }

            // 锁屏时间（分钟）
            val lockMins = geoInfo.optString("lockMin")
            if (!TextUtils.isEmpty(lockMins)) {
                geoLockMins = lockMins.toInt()
                Logger.geo("$TAG 设置锁屏时间: ${geoLockMins}分钟")
            }

            // 添加：wipe参数处理，确保重启后能正确恢复
            val wipeData = "1" == geoInfo.optString("wipeStatus")
            val wipeMins = geoInfo.optString("wipeMin")
            val nWipeMins = if (!TextUtils.isEmpty(wipeMins)) wipeMins.toInt() else -1

            geoWipeStatus = wipeData
            if (nWipeMins > 0) {
                geoWipeMins = nWipeMins
            }
            Logger.geo("$TAG 设置wipe参数: wipeStatus=$geoWipeStatus, wipeMins=$geoWipeMins")

            // 保存所有参数到SharedPreferences（确保重启后一致性）
            val prefs = context.getSharedPreferences("geofence_config", Context.MODE_PRIVATE)
            prefs.edit().apply {
                putString("lockMeter", geoInfo.optString("lockMeter"))
                putString("lockMin", geoInfo.optString("lockMin"))
                putString("wipeStatus", geoInfo.optString("wipeStatus"))
                putString("wipeMin", geoInfo.optString("wipeMin"))
                apply()
            }
            Logger.geo("$TAG 地理围栏参数已保存到geofence_config")

            // 商店位置坐标 - ST007协议中是String类型
            val latitudeStr = geoInfo.optString("storeLat", "")
            val longitudeStr = geoInfo.optString("storeLng", "")
            Logger.geo("$TAG 解析店铺坐标字符串: storeLat='$latitudeStr', storeLng='$longitudeStr'")

            if (!TextUtils.isEmpty(latitudeStr) && !TextUtils.isEmpty(longitudeStr)) {
                try {
                    val latitude = latitudeStr.toDouble()
                    val longitude = longitudeStr.toDouble()

                    if (latitude != 0.0 && longitude != 0.0) {
                        Constants.geoLatitude = latitude
                        Constants.geoLongitude = longitude
                        Logger.geo("$TAG 设置地理围栏中心: ($latitude, $longitude)")

                        // 保存坐标到SharedPreferences
                        saveGeofenceCenterToGpsConfig(context, latitude, longitude)
                    } else {
                        Logger.geo("$TAG 坐标为零值: lat=$latitude, lng=$longitude")
                    }
                } catch (e: NumberFormatException) {
                    Logger.geoE(
                        "$TAG 坐标格式错误: storeLat='$latitudeStr', storeLng='$longitudeStr'",
                        e
                    )
                }
            } else {
                Logger.geo("$TAG 坐标字符串为空，未设置地理围栏中心")
            }

            // 商店ID
            val storeId = geoInfo.optString("storeId")
            if (!TextUtils.isEmpty(storeId)) {
                Constants.storeId = storeId
                Logger.geo("$TAG 设置商店ID: $storeId")
            }

            // 商店SSID
            val storeSsid = geoInfo.optString("storeSsid")
            if (!TextUtils.isEmpty(storeSsid)) {
                Constants.storeSsid = storeSsid
                Logger.geo("$TAG 设置商店SSID: $storeSsid")
            }

            // 商店IP
            val storeIp = geoInfo.optString("storeIp")
            if (!TextUtils.isEmpty(storeIp)) {
                Constants.storeIp = storeIp
                Logger.geo("$TAG 设置商店IP: $storeIp")
            }

            // 漫游标签处理
            val roamingTag = "1" == geoInfo.optString("roamingTag")
            if (roamingTag) {
                Logger.geo("$TAG 设备处于漫游状态，禁用地理围栏")
                geoStatus = false

                // 如果当前处于锁屏或数据擦除状态，需要关闭
                if (Constants.geofenceStatus >= LOCK_SCREEN || Constants.geofenceStatus >= WIPE_DATA) {
                    closeOutOfGeofenceLockScreen(context)
                } else if (Constants.geofenceStatus == OUT_OF_ZONE) {
                    closeOutOfGeofenceWarning(context)
                    closeOutOfGeofenceLockScreen(context)
                }

                setCurrentGPSStatus(context, ROAMING)
            } else {
                // 如果之前是漫游状态，现在恢复正常
                if (Constants.geofenceStatus == ROAMING) {
                    setCurrentGPSStatus(context, IN_ZONE)
                }
            }

            // wipe参数处理
//            val wipeData = "1" == geoInfo.optString("wipeStatus")
//            val wipeMins = geoInfo.optString("wipeMin")
//            val nWipeMins = if (!TextUtils.isEmpty(wipeMins)) wipeMins.toInt() else -1

            if (Constants.geofenceStatus == LOCK_SCREEN) {
                if (geoWipeStatus != wipeData || (nWipeMins > 0 && geoWipeMins != nWipeMins)) {
                    Logger.geo("$TAG 设备处于锁屏状态，wipe参数发生变化，发送广播并只保存lock参数")
                    // 在LOCK_SCREEN状态下也要更新内存变量
                    geoWipeStatus = wipeData
                    if (nWipeMins > 0) {
                        geoWipeMins = nWipeMins
                    }

                    // 发送广播
                    BroadcastSender.sendBroadcast(
                        context,
                        BroadcastActions.ACTION_WIPE_PARAM_CHANGE,
                        "GEO_WIPE_STATUS" to wipeData,
                        "GEO_WIPE_MINS" to nWipeMins
                    )

                    // 只保存lock参数，不保存wipe参数
                    val prefs = context.getSharedPreferences("geofence_config", Context.MODE_PRIVATE)
                    prefs.edit().apply {
                        putString("lockMeter", geoInfo.optString("lockMeter"))
                        putString("lockMin", geoInfo.optString("lockMin"))
                        apply()
                    }
                    Logger.geo("$TAG 已发送wipe参数变化广播并保存lock参数")
                }
            } else {
                // 正常情况：更新内存变量
                geoWipeStatus = wipeData
                geoWipeMins = nWipeMins
                Logger.geo("$TAG 正常状态，更新wipe参数: wipeStatus=$geoWipeStatus, wipeMins=$geoWipeMins")

                // 正常情况：保存所有参数
                val prefs = context.getSharedPreferences("geofence_config", Context.MODE_PRIVATE)
                prefs.edit().apply {
                    putString("lockMeter", geoInfo.optString("lockMeter"))
                    putString("lockMin", geoInfo.optString("lockMin"))
                    putString("wipeStatus", geoInfo.optString("wipeStatus"))
                    putString("wipeMin", geoInfo.optString("wipeMin"))
                    apply()
                }
                Logger.geo("$TAG 已保存所有地理围栏参数")
            }

            // Profile名称
            val proName = geoInfo.optString("proName")
            if (!TextUtils.isEmpty(proName)) {
                Logger.geo("$TAG Profile名称: $proName")
            }

            // 是否为默认Profile
            val isDefault = geoInfo.optString("isDefault")
            if (!TextUtils.isEmpty(isDefault)) {
                Logger.geo("$TAG 是否默认Profile: $isDefault")
            }

            Logger.geo("$TAG 地理围栏配置参数更新完成")
        } catch (e: Exception) {
            Logger.geoE("$TAG 更新地理围栏配置失败", e)
        }
    }

    /**
     * 保存当前Profile
     */
    private fun saveCurrentProfile(context: Context, profile: String) {
        try {
            val prefs = PreferenceManager.getDefaultSharedPreferences(context)
            prefs.edit().putString("geofence_current_profile", profile).apply()
        } catch (e: Exception) {
            Logger.geoE("$TAG 保存当前Profile失败", e)
        }
    }

    /**
     * 保存围栏中心坐标到GPS配置
     */
    private fun saveGeofenceCenterToGpsConfig(
        context: Context,
        latitude: Double,
        longitude: Double,
    ) {
        try {
            val sharedPrefs = context.getSharedPreferences("gps_config", Context.MODE_PRIVATE)
            sharedPrefs.edit().apply {
                putString("store_latitude", latitude.toString())  // 直接保存店铺坐标字符串
                putString("store_longitude", longitude.toString())  // 直接保存店铺坐标字符串
                apply()
            }
            Logger.geo("$TAG 围栏中心坐标已保存到GPS配置: ($latitude, $longitude)")
        } catch (e: Exception) {
            Logger.geoE("$TAG 保存围栏中心坐标失败", e)
        }
    }

    /**
     * 获取当前Profile
     */
    private fun getCurrentProfile(context: Context): String {
        return try {
            val prefs = PreferenceManager.getDefaultSharedPreferences(context)
            prefs.getString("geofence_current_profile", "") ?: ""
        } catch (e: Exception) {
            Logger.geoE("$TAG 获取当前Profile失败", e)
            ""
        }
    }

    /**
     * 获取默认Profile
     * 从本地Profile列表中查找标记为isDefault="1"的Profile
     */
    private fun getDefaultGeoProfile(context: Context): JSONObject? {
        return try {
            val prefs = PreferenceManager.getDefaultSharedPreferences(context)
            val localGeoProfileListStr = prefs.getString("WEBSOCKET_GEOFENCE_PROFILE_LIST", "")
            Logger.geo("$TAG 当前本地Profile列表: $localGeoProfileListStr")

            if (!TextUtils.isEmpty(localGeoProfileListStr)) {
                val localProfileList = JSONArray(localGeoProfileListStr)
                Logger.geo("$TAG 本地不为空，遍历查找默认Profile，数量: ${localProfileList.length()}")

                for (i in 0 until localProfileList.length()) {
                    val profileJsonObj = localProfileList.getJSONObject(i)
                    if (profileJsonObj.has("isDefault") && "1" == profileJsonObj.getString("isDefault")) {
                        Logger.geo("$TAG 找到默认Profile: ${profileJsonObj.getString("proId")}")
                        return profileJsonObj
                    }
                }
                Logger.geo("$TAG 未找到标记为默认的Profile")
            } else {
                Logger.geo("$TAG 本地Profile列表为空")
            }

            null
        } catch (e: Exception) {
            Logger.geoE("$TAG 获取默认Profile失败", e)
            null
        }
    }

    /**
     * 关闭地理围栏检测
     */
    fun shutDownGeoFenceDetect(context: Context) {
        geoStatus = false
        closeOutOfGeofenceWarning(context)
        closeOutOfGeofenceLockScreen(context)
        setCurrentGPSStatus(context, IN_ZONE)
    }

    /**
     * 初始化时执行地理围栏Profile（非空版本）
     * 用于系统启动时恢复地理围栏状态
     */
    fun executeGeoProfileForInitNonNull(context: Context, geoInfo: JSONObject) {
        try {
            Logger.geo("$TAG executeGeoProfileForInit - ST007协议")
            Logger.geo("$TAG 接收到的Profile: $geoInfo")

            // 解析Profile信息 - 使用ST007协议的字段名（String类型）
            val latitudeStr = geoInfo.optString("storeLat", "")
            val longitudeStr = geoInfo.optString("storeLng", "")

            Logger.geo("$TAG 解析坐标字符串: storeLat='$latitudeStr', storeLng='$longitudeStr'")

            if (TextUtils.isEmpty(latitudeStr) || TextUtils.isEmpty(longitudeStr)) {
                Logger.geoE("$TAG 坐标字符串为空: storeLat='$latitudeStr', storeLng='$longitudeStr'")
                return
            }

            // 将变量定义移到try块外，确保作用域正确
            val latitude: Double
            val longitude: Double

            try {
                latitude = latitudeStr.toDouble()
                longitude = longitudeStr.toDouble()

                if (latitude == 0.0 || longitude == 0.0) {
                    Logger.geoE("$TAG 无效的地理围栏坐标: lat=$latitude, lng=$longitude")
                    return
                }

                // 更新地理围栏配置
                Constants.geoLatitude = latitude
                Constants.geoLongitude = longitude
                Logger.geo("$TAG 设置地理围栏中心坐标: ($latitude, $longitude)")
            } catch (e: NumberFormatException) {
                Logger.geoE(
                    "$TAG 坐标格式错误: storeLat='$latitudeStr', storeLng='$longitudeStr'",
                    e
                )
                return
            }

            // 更新其他配置参数
            updateGeofenceConfig(context, geoInfo)

            // 启用地理围栏
            geoStatus = true

            // 设置初始地理围栏状态
            setInitialGeofenceStatus(context)

            // 保存围栏中心坐标到GPS配置
            saveGeofenceCenterToGpsConfig(context, latitude, longitude)

            // 启动位置监听（需要在主线程中执行）
            val contextRef = context // 捕获context引用
            Handler(Looper.getMainLooper()).post {
                try {
                    registerLocationChangeListener(contextRef)
                    Logger.geo("$TAG executeGeoProfileForInitNonNull 已启动GPS位置监听")  // 🌍 使用地理围栏专用日志
                } catch (e: Exception) {
                    Logger.geoE("$TAG executeGeoProfileForInitNonNull 启动GPS位置监听失败", e)  // 🌍 使用地理围栏专用日志
                }
            }

            // 上报地理围栏数据信息
            WsMessageSender.uploadDataInfoForGeo(System.currentTimeMillis())
            Logger.geo("$TAG executeGeoProfileForInitNonNull 已触发地理围栏数据上报")  // 🌍 使用地理围栏专用日志

            Logger.geo("$TAG 初始化地理围栏Profile完成: 中心($latitude, $longitude), 半径${geoLockMeter}米")  // 🌍 使用地理围栏专用日志

        } catch (e: Exception) {
            Logger.geoE("$TAG 初始化地理围栏Profile失败", e)
        }
    }

    /**
     * 设置初始地理围栏状态
     * 初始化时应该设置为IN_ZONE，让后续的GPS检查来判断实际状态
     */
    private fun setInitialGeofenceStatus(context: Context) {
        try {
            Logger.geo("$TAG 设置初始地理围栏状态")

            // 初始化时设置为IN_ZONE
            // 这样当GPS检测到距离超出时，会触发状态变更
            if (Constants.geofenceStatus == LOCK_SCREEN || Constants.geofenceStatus == WIPE_DATA) {
                Logger.geo("$TAG 当前状态为锁屏/擦除状态，保持不变: ${Constants.geofenceStatus}")
            } else {
                Logger.geo("$TAG 设置初始状态为IN_ZONE，等待GPS检查")
                setCurrentGPSStatus(context, IN_ZONE)
            }

            // 重置firstLocation标志，让下次GPS更新能正常处理
            firstLocation = true

        } catch (e: Exception) {
            Logger.geoE("$TAG 设置初始地理围栏状态失败", e)
        }
    }

    /**
     * 检查SSID是否在WiFi Profile列表中
     * 集成现有WiFi模块的Profile管理功能
     */
    private fun checkIsWifiProfileSSID(context: Context, ssid: String): Boolean {
        return try {
            if (TextUtils.isEmpty(ssid)) {
                return false
            }

            Logger.geo("$TAG 检查SSID是否在WiFi Profile中: $ssid")

            // 1. 首先检查是否与商店SSID匹配
            if (!TextUtils.isEmpty(Constants.storeSsid) && ssid == Constants.storeSsid) {
                Logger.geo("$TAG SSID匹配商店WiFi: $ssid")
                return true
            }

            // 2. 使用现有WiFi模块检查Profile列表
            val result = checkWifiProfileFromCache(context, ssid)

            if (result) {
                Logger.geo("$TAG SSID在WiFi Profile列表中: $ssid")
            } else {
                Logger.geo("$TAG SSID不在WiFi Profile列表中: $ssid")
            }

            result
        } catch (e: Exception) {
            Logger.geoE("$TAG 检查WiFi Profile SSID失败", e)
            false
        }
    }

    /**
     * 从缓存的WiFi Profile列表中检查SSID
     */
    private fun checkWifiProfileFromCache(context: Context, ssid: String): Boolean {
        return try {
            // 从SharedPreferences获取WiFi Profile列表
            val sharedPrefs =
                android.preference.PreferenceManager.getDefaultSharedPreferences(context)
            val wifiProfileJson = sharedPrefs.getString("wifi_profile_list", "")

            if (TextUtils.isEmpty(wifiProfileJson)) {
                Logger.geo("$TAG WiFi Profile列表为空")
                return false
            }

            val cacheData = org.json.JSONArray(wifiProfileJson)
            if (cacheData.length() == 0) {
                Logger.geo("$TAG WiFi Profile列表长度为0")
                return false
            }

            // 遍历检查SSID是否匹配
            for (i in 0 until cacheData.length()) {
                val profileObj = cacheData.getJSONObject(i)
                val profileSSID = profileObj.optString("ssid", "")

                if (ssid == profileSSID) {
                    Logger.geo("$TAG 找到匹配的WiFi Profile: $ssid")
                    return true
                }
            }

            Logger.geo("$TAG 未找到匹配的WiFi Profile: $ssid")
            false

        } catch (e: Exception) {
            Logger.geoE("$TAG 检查WiFi Profile缓存失败", e)
            false
        }
    }

    /**
     * 启动蓝牙信标扫描
     * 集成现有的BluetoothBeaconScanner
     */
    fun startBluetoothBeaconScan(context: Context) {
        try {
            if (TextUtils.isEmpty(Constants.storeId)) {
                Logger.geo("$TAG Store ID为空，跳过蓝牙信标扫描")
                return
            }

            Logger.geo("$TAG 启动蓝牙信标扫描，Store ID: ${Constants.storeId}")

            val bluetoothScanner = BluetoothBeaconScanner(context)
            if (bluetoothScanner.initialize()) {
                bluetoothScanner.startScan(Constants.storeId) { beacons ->
                    handleBluetoothBeacons(beacons)
                }
            } else {
                Logger.geoW("$TAG 蓝牙信标扫描器初始化失败")
            }

        } catch (e: Exception) {
            Logger.geoE("$TAG 启动蓝牙信标扫描失败", e)
        }
    }

    /**
     * 处理检测到的蓝牙信标
     */
    private fun handleBluetoothBeacons(beacons: List<BeaconInfo>) {
        try {
            if (beacons.isNotEmpty()) {
                Logger.geo("$TAG 检测到 ${beacons.size} 个蓝牙信标")

                // 检查是否有匹配的信标
                val matchingBeacons = beacons.filter { beacon ->
                    // 1. 检查Store ID匹配
                    val storeMatches = beacon.matchesStoreId(Constants.storeId)

                    // 2. 检查距离阈值
                    val distanceOk = beacon.distance <= 5.0 // 5米内，更严格的距离控制

                    // 3. 检查信号强度 - 确保信号足够强
                    val signalOk = beacon.rssi >= -70 // RSSI > -70dBm，信号较强

                    val matches = storeMatches && distanceOk && signalOk

                    if (matches) {
                        Logger.geo(
                            "$TAG 匹配的信标: UUID=${beacon.uuid}, Major=${beacon.major}, Minor=${beacon.minor}, Distance=${
                                String.format(
                                    "%.2f",
                                    beacon.distance
                                )
                            }m, RSSI=${beacon.rssi}dBm"
                        )
                    }

                    matches
                }

                if (matchingBeacons.isNotEmpty()) {
                    Logger.geo("$TAG 检测到 ${matchingBeacons.size} 个匹配的蓝牙信标，设置geoIsNearBeacon=true")
                    geoIsNearBeacon = true

                    // 如果当前在围栏外，蓝牙信标检测可以作为进入围栏的条件
                    if (Constants.geofenceStatus == OUT_OF_ZONE) {
                        Logger.geo("$TAG 蓝牙信标检测到，满足进入围栏条件")
                        // 这里会在conditionEnterGeoFence中被检查
                    }
                } else {
                    // 没有匹配的信标，但可能有其他信标
                    Logger.geo("$TAG 检测到信标但不匹配Store ID或距离/信号强度不满足要求")
                    geoIsNearBeacon = false
                }
            } else {
                Logger.geo("$TAG 未检测到任何蓝牙信标")
                geoIsNearBeacon = false
            }

        } catch (e: Exception) {
            Logger.geoE("$TAG 处理蓝牙信标失败", e)
            geoIsNearBeacon = false
        }
    }

    /**
     * 检查是否需要蓝牙信标扫描
     */
    fun isBTBeaconScanNeed(): Boolean {
        return try {
            // 1. 检查是否启用了地理围栏
            if (!geoStatus) {
                Logger.geo("$TAG 地理围栏未启用，不需要蓝牙扫描")
                return false
            }

            // 2. 检查是否在围栏外
            if (Constants.geofenceStatus != OUT_OF_ZONE) {
                Logger.geo("$TAG 不在围栏外，不需要蓝牙扫描")
                return false
            }

            // 3. 检查扫描模式 - scanMode=1表示蓝牙常开模式
            if (scanMode != 1) {
                Logger.geo("$TAG 蓝牙扫描模式未启用，scanMode=$scanMode")
                return false
            }

            // 4. 检查Store ID是否配置
            if (TextUtils.isEmpty(Constants.storeId)) {
                Logger.geo("$TAG Store ID未配置，不需要蓝牙扫描")
                return false
            }

            Logger.geo("$TAG 满足蓝牙信标扫描条件")
            true

        } catch (e: Exception) {
            Logger.geoE("$TAG 检查蓝牙信标扫描需求失败", e)
            false
        }
    }

// ==================== FusedLocationProviderClient 支持 ====================

    /**
     * 尝试使用FusedLocationProviderClient
     */
    private fun tryUseFusedLocationProvider(context: Context): Boolean {
        return try {
            // 检查是否有Google Play Services依赖
            val className = "com.google.android.gms.common.GoogleApiAvailability"
            Class.forName(className)

            Logger.geo("$TAG 检测到Google Play Services，尝试使用FusedLocationProvider")

            // 由于可能没有Google Play Services依赖，这里先返回false
            // 如果需要使用FusedLocationProvider，需要在build.gradle中添加：
            // implementation 'com.google.android.gms:play-services-location:21.0.1'
            Logger.geo("$TAG FusedLocationProvider功能需要添加Google Play Services依赖")
            false

        } catch (_: ClassNotFoundException) {
            Logger.geo("$TAG Google Play Services不可用，使用传统LocationManager")
            false
        } catch (e: Exception) {
            Logger.geo("$TAG FusedLocationProvider检查失败: ${e.message}")
            false
        }
    }

    /**
     * 处理位置更新（统一处理函数）
     */
    private fun handleLocationUpdate(location: Location, context: Context) {
        try {
            // 检查定位来源提供者
            val providerInfo = when (location.provider) {
                LocationManager.GPS_PROVIDER -> "GPS卫星定位"
                LocationManager.NETWORK_PROVIDER -> "网络定位(WiFi+基站)"
                LocationManager.PASSIVE_PROVIDER -> "被动定位"
                "fused" -> "融合定位(FusedLocationProvider)"
                else -> "未知来源: ${location.provider}"
            }

            Logger.geo("$TAG 定位来源: $providerInfo")
            Logger.geo("$TAG 位置更新: latitude=${location.latitude} longitude=${location.longitude}")
            Logger.geo("$TAG GPS精度: ${location.accuracy}米, 时间: ${location.time}")

            latitude = location.latitude
            longitude = location.longitude
            isGpsValid = true

            // 地理围栏检查逻辑
            Logger.geo("$TAG 地理围栏状态检查: geoStatus=$geoStatus, geofenceStatus=${Constants.geofenceStatus}, geoLockMeter=$geoLockMeter")
            Logger.geo("$TAG 当前位置: lat=${location.latitude}, lng=${location.longitude}, accuracy=${location.accuracy}")
            if (geoStatus) {
                performGeofenceCheck(location, context)
            } else {
                Logger.geo("$TAG 地理围栏未启用，跳过围栏检查")
                Logger.geo("$TAG 💡 提示：如果需要启用地理围栏，请确保已收到ST007消息并正确配置")
            }

            Handler(Looper.getMainLooper()).postDelayed({
                Logger.geo(  // 🌍 使用GPS专用日志
                    "$TAG onLocation SatelliteStatusChanged COUNT = $satelliteCount" +
                            " COUNT_IN_USED = $satelliteCountInUsed" +
                            " AVER_SNR = $satelliteAverageSnr"
                )
                WsMessageSender.uploadDataInfoForGeo(System.currentTimeMillis())
            }, 500)

            // 对不关注GPS的终端，当获取到位置信息时，需要进行解绑
            // 但如果地理围栏功能启用，则需要持续监控GPS位置
            if (!isCareGPS && latitude != DEFAULT_VALUE && longitude != DEFAULT_VALUE && !geoStatus) {
                unregisterLocationChangeListener()
            }

        } catch (e: Exception) {
            Logger.geoE("$TAG 处理位置更新失败", e)  // 🌍 使用GPS专用日志
        }
    }

    /**
     * 执行地理围栏检查
     */
    private fun performGeofenceCheck(location: Location, context: Context) {
        try {
            // 从SharedPreferences读取存储的围栏中心坐标
            val latitudeStr = getGpsConfigValue("store_latitude", "")
            val longitudeStr = getGpsConfigValue("store_longitude", "")

            Logger.geo("$TAG 围栏中心坐标配置: lat='$latitudeStr', lng='$longitudeStr'")
            Logger.geo("$TAG 当前位置: lat=${location.latitude}, lng=${location.longitude}")

            if (!TextUtils.isEmpty(latitudeStr) && !TextUtils.isEmpty(longitudeStr)) {
                val result = FloatArray(1)
                // 参数顺序（围栏中心在前，当前位置在后）
                Location.distanceBetween(
                    latitudeStr.toDouble(), longitudeStr.toDouble(),  // 围栏中心坐标
                    location.latitude, location.longitude,               // 当前位置坐标
                    result
                )
                val distance = result[0]
                Logger.geo("$TAG 距离围栏中心: ${distance.toInt()}米, 围栏半径: ${geoLockMeter}米")

                // 执行围栏逻辑判断
                executeGeofenceLogic(distance, context)
            } else {
                Logger.geo("$TAG Geofence准备未绪/围栏中心坐标未配置，无法进行围栏检查")
            }
        } catch (e: Exception) {
            Logger.geoE("$TAG 地理围栏检查失败", e)
        }
    }

    /**
     * 执行地理围栏逻辑
     */
    private fun executeGeofenceLogic(distance: Float, context: Context) {
        try {
            Logger.geo("$TAG executeGeofenceLogic: distance=${distance.toInt()}米, firstLocation=$firstLocation, lastDistance=$lastDistance")
            Logger.geo("$TAG 当前围栏状态: geofenceStatus=${Constants.geofenceStatus}, geoLockMeter=$geoLockMeter")

            // 距离验证逻辑 - 不应该忽略真实的围栏状态变化
            val distanceDiff = abs(distance - lastDistance)
            if (!firstLocation && distanceDiff > validErrorDistance) {
                // 检查是否是真实的围栏状态变化
                val wasInside = lastDistance <= geoLockMeter
                val nowInside = distance <= geoLockMeter

                if (wasInside != nowInside) {
                    // 围栏状态发生变化（进入或离开），这是真实变化，不应忽略
                    Logger.geo("$TAG 检测到围栏状态变化: 之前${if (wasInside) "在围栏内" else "在围栏外"}(${lastDistance.toInt()}米), 现在${if (nowInside) "在围栏内" else "在围栏外"}(${distance.toInt()}米)")
                    lastDistance = distance.toDouble()
                } else {
                    // 同一状态下的大幅跳跃，可能是GPS误差
                    Logger.geo("$TAG GPS跳跃检测: 距离差异${distanceDiff.toInt()}米 > 阈值${validErrorDistance.toInt()}米, 状态未变化，忽略")
                    lastDistance = distance.toDouble()
                    return
                }
            } else {
                lastDistance = distance.toDouble()
            }

            // 忽略第一次定位（防止重启后未连WiFi时的误报）
            // 但如果设备已经处于锁定状态，则不应忽略首次定位
            if (firstLocation) {
                firstLocation = false
                // 如果设备已经锁定且在围栏外，不应忽略首次定位
                if (Constants.geofenceStatus != LOCK_SCREEN && Constants.geofenceStatus != WIPE_DATA) {
                    Logger.geo("$TAG ignore the first location (防止重启后未连WiFi时的误报)")
                    return // 针对在reboot后，在未连上wifi时候之前，多台终端出现out of zone的情况，ignore the first Location
                } else {
                    Logger.geo("$TAG 设备已锁定，不忽略首次定位，继续执行地理围栏检查")
                }
            }

            // 地理围栏逻辑判断
            if (distance > geoLockMeter) {
                Logger.geo("$TAG 设备在围栏外: 距离=${distance.toInt()}米 > 围栏半径=${geoLockMeter}米, 当前状态=${Constants.geofenceStatus}")

                // 如果设备已经是LOCK_SCREEN状态且在围栏外，立即发送ACTION_OUT_OF_GEOFENCE广播
                if (Constants.geofenceStatus == LOCK_SCREEN || Constants.geofenceStatus == WIPE_DATA) {
                    Logger.geo("$TAG 设备已锁定且在围栏外，发送ACTION_OUT_OF_GEOFENCE广播确保锁屏显示")
                    val intent = Intent(BroadcastActions.ACTION_OUT_OF_GEOFENCE)
                    sendGeofenceBroadcast(context, intent)
                }

                when (Constants.geofenceStatus) {
                    IN_ZONE -> {
                        if (!conditionEnterGeoFence(context)) {
                            showOutOfGeofenceWarning(context, WARNING_STATUS_IN_FENCE)
                            setCurrentGPSStatus(context, OUT_OF_ZONE)
                            // 确保在主线程中注册扩展位置监听器
                            Handler(Looper.getMainLooper()).post {
                                registerLocationChangeListenerEx(context)
                            }
                            bGeoOutOfFence = true
                        }
                    }

                    OUT_OF_ZONE -> {
                        if (conditionEnterGeoFence(context)) {
                            // 查看终端的IP address, 如果不变和unbox时候IP的一样, 就算他是in fence
                            closeOutOfGeofenceWarning(context)
                            setCurrentGPSStatus(context, IN_ZONE)
                        } else {
                            showOutOfGeofenceWarning(context, WARNING_STATUS_OUT_OF_FENCE)
                        }
                    }

//                    LOCK_SCREEN, WIPE_DATA -> {
//                        // 当设备被锁定时，检查是否满足进入围栏的条件
//                        if (conditionEnterGeoFence(context)) {
//                            Logger.geo("$TAG 设备已锁定，但满足进入围栏条件，发送解锁广播")
//                            val intent = Intent(BroadcastActions.ACTION_ENTER_GEOFENCE)
//                            sendGeofenceBroadcast(context, intent)
//                        } else {
//                            Logger.geo("$TAG 设备已锁定，且不满足进入围栏条件，确保锁屏界面显示")
//                            // 确保锁屏界面正在显示
//                            ensureLockScreenActivityRunning(context)
//                        }
//                    }

                    LOCK_SCREEN, WIPE_DATA -> {
                        // 当设备被锁定时，检查是否满足进入围栏的条件
                        if (conditionEnterGeoFence(context)) {
                            Logger.geo("$TAG 设备已锁定，但满足进入围栏条件，发送解锁广播")
                            ensureLockScreenActivityRunning(context)
                            val intent = Intent(BroadcastActions.ACTION_ENTER_GEOFENCE)
                            sendGeofenceBroadcast(context, intent)
                        } else {
                            // 确保LockScreenActivity正在运行，然后发送离开围栏广播
                            ensureLockScreenActivityRunning(context)
                            val intent = Intent(BroadcastActions.ACTION_OUT_OF_GEOFENCE)
                            sendGeofenceBroadcast(context, intent)
                        }
                    }
                }
            } else {
                bGeoOutOfFence = false

                when (Constants.geofenceStatus) {
                    OUT_OF_ZONE -> {
                        setCurrentGPSStatus(context, IN_ZONE)
                        registerLocationChangeListener(context)
                        closeOutOfGeofenceWarning(context)
                    }

                    LOCK_SCREEN, WIPE_DATA -> {
                        Logger.geo("$TAG Constants.storeSsid=${Constants.storeSsid}  Constants.storeIp=${Constants.storeIp}")
                        Logger.geo(
                            "$TAG CurConnectSSID=${WiFiManagerApi.getCurConnectSSID(context)}  CurConnectIP=${WiFiManagerApi.getWifiIP(context)}"
                        )

                        if (WebSocketCenter.isConnected()) {
                            val intent = Intent(BroadcastActions.ACTION_ENTER_GEOFENCE)
                            sendGeofenceBroadcast(context, intent)
                        } else {
                            if (!WebSocketCenter.isConnected()) {
                                showGPSToastMsg("Enter Fence! Device is offline, ignore")
                                Logger.geo("$TAG Enter Fence! Device is offline, ignore")
                            }
                        }
                    }
                }
            }
        } catch (e: Exception) {
            Logger.geoE("$TAG executeGeofenceLogic failed", e)
        }
    }

    /**
     * 系统启动时恢复地理围栏状态
     */
    fun restoreGeofenceOnBoot(context: Context) {
        try {
            Logger.geo("$TAG ========== 系统启动时恢复地理围栏状态 ==========")

            // 1. 恢复地理围栏状态
            val prefs = PreferenceManager.getDefaultSharedPreferences(context)
            val currentProfile = prefs.getString("geofence_current_profile", "") ?: ""

            if (!TextUtils.isEmpty(currentProfile)) {
                Logger.geo("$TAG 发现已保存的地理围栏Profile，开始恢复...")
                val profileJson = JSONObject(currentProfile)

                // 执行恢复（使用executeGeoProfileForInitNonNull处理非空Profile）
                executeGeoProfileForInitNonNull(context, profileJson)
                Logger.geo("$TAG 地理围栏状态恢复完成")
            } else {
                Logger.geo("$TAG 未发现已保存的地理围栏Profile")

                // 检查是否有到期的Profile需要执行
                GeofenceHandler(context).executeOnDueGeoProfile(context)
            }

            // 2. 启动定时检查任务（现在由GeofenceReceiver管理）
            GeofenceReceiver.startSystemTimer(context)
            Logger.geo("$TAG 定时检查任务已启动")

            Logger.geo("$TAG ========== 地理围栏状态恢复完成 ==========")

        } catch (e: Exception) {
            Logger.geoE("$TAG 恢复地理围栏状态失败", e)
        }
    }
}