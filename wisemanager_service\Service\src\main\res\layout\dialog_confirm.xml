<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical"
    android:layout_width="320dp"
    android:layout_height="wrap_content">

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">
        <LinearLayout
            android:layout_height="match_parent"
            android:layout_width="match_parent"
            android:orientation="vertical">
            <View
                android:layout_width="match_parent"
                android:layout_height="35dp"
                android:alpha="0"/>
            <View
                android:layout_width="match_parent"
                android:layout_height="35dp"
                android:background="@drawable/shape_corner_up"/>
        </LinearLayout>
        <ImageView
            android:id="@+id/iv_image"
            android:layout_width="70dp"
            android:layout_height="70dp"
            android:scaleType="center"
            android:background="@drawable/info"
            android:layout_gravity="center"
            android:contentDescription="TODO" />
    </FrameLayout>
    <LinearLayout
        android:layout_height="wrap_content"
        android:layout_width="match_parent"
        android:gravity="center"
        android:orientation="vertical"
        android:background="@color/white">
        <TextView
            android:id="@+id/tv_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:padding="5dp"
            android:text="Battery low"
            android:gravity="center"
            android:textSize="30sp"
            android:textColor="@color/title"/>
        <TextView
            android:id="@+id/tv_content"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:padding="10dp"
            android:layout_marginTop="5dp"
            android:layout_marginBottom="20dp"
            android:text="content"
            android:textSize="16sp"
            android:textColor="@color/subtitle"/>
    </LinearLayout>
    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="@color/divider_ex"/>
    <LinearLayout
        android:layout_height="wrap_content"
        android:layout_width="match_parent"
        android:gravity="center_vertical|right"
        android:orientation="horizontal"
        android:background="@drawable/shape_corner_down">
        <TextView
            android:id="@+id/tv_cancel"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:padding="15dp"
            android:gravity="center"
            android:text="CANCEL"
            android:textColor="@color/theme_green"
            android:textSize="16sp"/>
        <View
            android:layout_width="1dp"
            android:layout_height="match_parent"
            android:background="@color/divider_ex"/>
        <TextView
            android:id="@+id/tv_install"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:padding="15dp"
            android:gravity="center"
            android:text="INSTALL"
            android:textColor="@color/theme_green"
            android:textSize="16sp"/>
    </LinearLayout>

</LinearLayout>