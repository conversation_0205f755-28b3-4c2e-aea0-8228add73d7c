package com.dspread.mdm.service.modules.geofence

import android.bluetooth.BluetoothAdapter
import android.bluetooth.BluetoothManager
import android.bluetooth.le.BluetoothLeScanner
import android.bluetooth.le.ScanCallback
import android.bluetooth.le.ScanFilter
import android.bluetooth.le.ScanResult
import android.bluetooth.le.ScanSettings
import android.content.Context
import android.content.pm.PackageManager
import com.dspread.mdm.service.modules.geofence.model.BeaconInfo
import com.dspread.mdm.service.utils.log.Logger
import kotlinx.coroutines.*
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.atomic.AtomicBoolean
import kotlin.math.pow

/**
 * 蓝牙信标扫描器
 * 负责扫描和识别特定的BLE信标
 */
class BluetoothBeaconScanner(
    private val context: Context
) {
    
    companion object {
        private const val TAG = "[BluetoothBeaconScanner]"
        private const val SCAN_DURATION = 10000L // 10秒扫描时间
        private const val MANUFACTURER_ID = 0x004C // Apple公司ID (用于iBeacon)
        private const val IBEACON_LAYOUT = "m:2-3=0215,i:4-19,i:20-21,i:22-23,p:24-24"
    }
    
    private val bluetoothManager = context.getSystemService(Context.BLUETOOTH_SERVICE) as BluetoothManager
    private val bluetoothAdapter: BluetoothAdapter? = bluetoothManager.adapter
    private val bluetoothLeScanner: BluetoothLeScanner? = bluetoothAdapter?.bluetoothLeScanner
    
    private val isScanning = AtomicBoolean(false)
    private val detectedBeacons = ConcurrentHashMap<String, BeaconInfo>()

    // 扫描专用协程作用域
    private val scanScope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    private var scanTimeoutJob: Job? = null

    private var currentStoreId: String = ""
    private var scanCallback: ((List<BeaconInfo>) -> Unit)? = null
    
    /**
     * 初始化蓝牙扫描器
     */
    fun initialize(): Boolean {
        return try {
            // 检查设备是否支持蓝牙LE
            if (!context.packageManager.hasSystemFeature(PackageManager.FEATURE_BLUETOOTH_LE)) {
                Logger.geoW("$TAG 设备不支持蓝牙LE")
                return false
            }
            
            // 检查蓝牙适配器
            if (bluetoothAdapter == null) {
                Logger.geoW("$TAG 蓝牙适配器不可用")
                return false
            }
            
            // 检查蓝牙是否启用
            if (!bluetoothAdapter.isEnabled) {
                Logger.geoW("$TAG 蓝牙未启用")
                return false
            }
            
            Logger.geo("$TAG 蓝牙信标扫描器初始化成功")
            true
            
        } catch (e: Exception) {
            Logger.geoE("$TAG 蓝牙信标扫描器初始化失败", e)
            false
        }
    }
    
    /**
     * 开始扫描指定Store ID的信标
     */
    fun startScan(storeId: String, callback: (List<BeaconInfo>) -> Unit) {
        if (!isBluetoothAvailable()) {
            Logger.geoW("$TAG 蓝牙不可用，无法开始扫描")
            return
        }
        
        if (isScanning.compareAndSet(false, true)) {
            currentStoreId = storeId
            scanCallback = callback
            
            Logger.geo("$TAG 开始扫描Store ID: $storeId 的蓝牙信标")
            
            try {
                val scanFilters = createScanFilters(storeId)
                val scanSettings = createScanSettings()
                
                bluetoothLeScanner?.startScan(scanFilters, scanSettings, bleScanCallback)
                
                // 设置扫描超时
                scanTimeoutJob = scanScope.launch {
                    delay(SCAN_DURATION)
                    stopScan()
                }
                
            } catch (e: Exception) {
                Logger.geoE("$TAG 开始蓝牙扫描失败", e)
                isScanning.set(false)
            }
        } else {
            Logger.geoW("$TAG 蓝牙扫描已在进行中")
        }
    }
    
    /**
     * 停止扫描
     */
    fun stopScan() {
        if (isScanning.compareAndSet(true, false)) {
            try {
                bluetoothLeScanner?.stopScan(bleScanCallback)
                Logger.geo("$TAG 蓝牙扫描已停止")
                
                // 返回检测到的信标
                val beacons = detectedBeacons.values.toList()
                if (beacons.isNotEmpty()) {
                    Logger.geo("$TAG 检测到 ${beacons.size} 个信标")
                    scanCallback?.invoke(beacons)
                } else {
                    Logger.geo("$TAG 未检测到任何信标")
                    scanCallback?.invoke(emptyList())
                }
                
                // 清理
                detectedBeacons.clear()
                scanTimeoutJob?.cancel()
                scanTimeoutJob = null
                
            } catch (e: Exception) {
                Logger.geoE("$TAG 停止蓝牙扫描失败", e)
            }
        }
    }
    
    /**
     * 创建扫描过滤器
     */
    private fun createScanFilters(storeId: String): List<ScanFilter> {
        val filters = mutableListOf<ScanFilter>()
        
        try {
            // 过滤器1：根据制造商数据过滤
            val manufacturerData = "Store:$storeId".toByteArray()
            val manufacturerDataMask = ByteArray(manufacturerData.size) { 0xFF.toByte() }
            
            val filter1 = ScanFilter.Builder()
                .setManufacturerData(MANUFACTURER_ID, manufacturerData, manufacturerDataMask)
                .build()
            filters.add(filter1)
            
            // 过滤器2：根据服务UUID过滤（如果有特定的服务UUID）
            // 这里可以添加更多的过滤条件
            
            Logger.geo("$TAG 创建了 ${filters.size} 个扫描过滤器")
            
        } catch (e: Exception) {
            Logger.geoE("$TAG 创建扫描过滤器失败", e)
        }
        
        return filters
    }
    
    /**
     * 创建扫描设置
     */
    private fun createScanSettings(): ScanSettings {
        return ScanSettings.Builder()
            .setScanMode(ScanSettings.SCAN_MODE_LOW_LATENCY) // 高性能模式
            .setCallbackType(ScanSettings.CALLBACK_TYPE_ALL_MATCHES)
            .setMatchMode(ScanSettings.MATCH_MODE_AGGRESSIVE)
            .setNumOfMatches(ScanSettings.MATCH_NUM_MAX_ADVERTISEMENT)
            .setReportDelay(0) // 立即报告
            .build()
    }
    
    /**
     * BLE扫描回调
     */
    private val bleScanCallback = object : ScanCallback() {
        
        override fun onScanResult(callbackType: Int, result: ScanResult) {
            try {
                val beaconInfo = parseBeaconInfo(result)
                if (beaconInfo != null && beaconInfo.matchesStoreId(currentStoreId)) {
                    val key = "${beaconInfo.uuid}-${beaconInfo.major}-${beaconInfo.minor}"
                    detectedBeacons[key] = beaconInfo
                    
                    Logger.geo("$TAG 检测到匹配的信标: UUID=${beaconInfo.uuid}, Major=${beaconInfo.major}, Minor=${beaconInfo.minor}, RSSI=${beaconInfo.rssi}")
                }
            } catch (e: Exception) {
                Logger.geoE("$TAG 处理扫描结果失败", e)
            }
        }
        
        override fun onBatchScanResults(results: MutableList<ScanResult>) {
            try {
                Logger.geo("$TAG 批量扫描结果: ${results.size} 个设备")
                
                for (result in results) {
                    onScanResult(ScanSettings.CALLBACK_TYPE_ALL_MATCHES, result)
                }
            } catch (e: Exception) {
                Logger.geoE("$TAG 处理批量扫描结果失败", e)
            }
        }
        
        override fun onScanFailed(errorCode: Int) {
            Logger.geoE("$TAG 蓝牙扫描失败，错误码: $errorCode")
            isScanning.set(false)
            
            val errorMessage = when (errorCode) {
                SCAN_FAILED_ALREADY_STARTED -> "扫描已经开始"
                SCAN_FAILED_APPLICATION_REGISTRATION_FAILED -> "应用注册失败"
                SCAN_FAILED_FEATURE_UNSUPPORTED -> "功能不支持"
                SCAN_FAILED_INTERNAL_ERROR -> "内部错误"
                else -> "未知错误"
            }
            
            Logger.geoE("$TAG 扫描失败原因: $errorMessage")
        }
    }
    
    /**
     * 解析信标信息
     */
    private fun parseBeaconInfo(result: ScanResult): BeaconInfo? {
        return try {
            val scanRecord = result.scanRecord ?: return null
            val manufacturerData = scanRecord.getManufacturerSpecificData(MANUFACTURER_ID)
            
            if (manufacturerData != null && manufacturerData.size >= 23) {
                // 解析iBeacon格式数据
                val uuid = parseUUID(manufacturerData, 2)
                val major = parseMajor(manufacturerData, 18)
                val minor = parseMinor(manufacturerData, 20)
                val txPower = manufacturerData[22].toInt()
                
                // 计算距离（基于RSSI和TxPower）
                val distance = calculateDistance(result.rssi, txPower)
                
                BeaconInfo(
                    uuid = uuid,
                    major = major,
                    minor = minor,
                    rssi = result.rssi,
                    distance = distance,
                    timestamp = System.currentTimeMillis(),
                    manufacturerData = manufacturerData
                )
            } else {
                // 尝试解析其他格式的信标数据
                parseCustomBeaconFormat(result)
            }
            
        } catch (e: Exception) {
            Logger.geoE("$TAG 解析信标信息失败", e)
            null
        }
    }
    
    /**
     * 解析自定义信标格式
     */
    private fun parseCustomBeaconFormat(result: ScanResult): BeaconInfo? {
        return try {
            val scanRecord = result.scanRecord ?: return null
            val manufacturerData = scanRecord.getManufacturerSpecificData(MANUFACTURER_ID)
            
            if (manufacturerData != null) {
                // 简化的信标信息
                BeaconInfo(
                    uuid = "custom-beacon",
                    major = 0,
                    minor = 0,
                    rssi = result.rssi,
                    distance = calculateDistance(result.rssi, -59), // 默认TxPower
                    timestamp = System.currentTimeMillis(),
                    manufacturerData = manufacturerData
                )
            } else {
                null
            }
        } catch (e: Exception) {
            Logger.geoE("$TAG 解析自定义信标格式失败", e)
            null
        }
    }
    
    /**
     * 解析UUID
     */
    private fun parseUUID(data: ByteArray, offset: Int): String {
        val uuid = StringBuilder()
        for (i in 0 until 16) {
            val b = data[offset + i].toInt() and 0xFF
            uuid.append(String.format("%02X", b))
            if (i == 3 || i == 5 || i == 7 || i == 9) {
                uuid.append("-")
            }
        }
        return uuid.toString()
    }
    
    /**
     * 解析Major值
     */
    private fun parseMajor(data: ByteArray, offset: Int): Int {
        return ((data[offset].toInt() and 0xFF) shl 8) or (data[offset + 1].toInt() and 0xFF)
    }
    
    /**
     * 解析Minor值
     */
    private fun parseMinor(data: ByteArray, offset: Int): Int {
        return ((data[offset].toInt() and 0xFF) shl 8) or (data[offset + 1].toInt() and 0xFF)
    }
    
    /**
     * 计算信标距离
     */
    private fun calculateDistance(rssi: Int, txPower: Int): Double {
        if (rssi == 0) return -1.0
        
        val ratio = txPower * 1.0 / rssi
        return if (ratio < 1.0) {
            ratio.pow(10.0)
        } else {
            val accuracy = (0.89976) * ratio.pow(7.7095) + 0.111
            accuracy
        }
    }
    
    /**
     * 检查蓝牙是否可用
     */
    private fun isBluetoothAvailable(): Boolean {
        return bluetoothAdapter?.isEnabled == true && bluetoothLeScanner != null
    }
    
    /**
     * 获取当前扫描状态
     */
    fun isScanning(): Boolean = isScanning.get()
    
    /**
     * 获取检测到的信标数量
     */
    fun getDetectedBeaconCount(): Int = detectedBeacons.size
}

/**
 * Double的扩展函数，用于计算幂
 */
// pow函数已经通过import kotlin.math.pow导入，不需要扩展函数
