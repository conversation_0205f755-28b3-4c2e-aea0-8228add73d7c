package com.bbpos.wiseapp.network.wificonfig;

import java.lang.reflect.Field;

/**
 * 对象 反射工具
 */
public class InjectTool {

    /**
     * 对 enum 重新赋值
     * @param obj
     * @param value
     * @param name
     */
    public static void setEnumField(Object obj, String value, String name) throws Exception{
        Field f = obj.getClass().getField(name);
        f.set(obj, Enum.valueOf((Class<Enum>) f.getType(), value));
    }

    /**
     * 对 属性进行重新赋值
     * @param obj
     * @param value
     * @param name
     */
    public static void setObjField(Object obj, Object value, String name)
            throws SecurityException, NoSuchFieldException,
            IllegalArgumentException, IllegalAccessException {
        Field f = obj.getClass().getDeclaredField(name);
        f.setAccessible(true);
        f.set(obj, value);
    }

    /**
     * 获取属性值
     *  getField只能获取类的public 字段.
     * @param obj
     * @param name
     * @return
     *
     */
    public static Object getFieldObject(Object obj, String name) throws Exception{
//        Field f = obj.getClass().getField(name);
        Field f = obj.getClass().getDeclaredField(name);
        f.setAccessible(true);
        Object out = f.get(obj);
        return out;
    }
}
