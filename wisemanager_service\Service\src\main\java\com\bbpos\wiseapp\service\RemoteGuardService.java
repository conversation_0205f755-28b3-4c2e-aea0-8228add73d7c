package com.bbpos.wiseapp.service;

import android.app.Service;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.ServiceConnection;
import android.os.IBinder;
import android.os.RemoteException;
import android.util.Log;

import com.bbpos.wiseapp.logger.BBLog;
import com.bbpos.wiseapp.settings.utils.ServiceAliveUtils;
/**
 * 本地进程，
 */
@Deprecated
public class RemoteGuardService extends Service {
    private RemoteGuardServiceBinder binder;
    private ServiceConnection connection = new ServiceConnection() {
        @Override
        public void onServiceConnected(ComponentName name, IBinder service) {
            BBLog.d(BBLog.TAG,"RemoteGuardService onServiceConnected ->");
            boolean isServiceRunning = ServiceAliveUtils.isServiceAlice();
            if (!isServiceRunning){
                BBLog.d(BBLog.TAG, "RemoteGuardService onServiceConnected -> 检测到service未存活，重新绑定 LocalService " );
                startService(new Intent(RemoteGuardService.this,LocalService.class));
            }

            KeepAliveConnection iMyAidlInterface = KeepAliveConnection.Stub.asInterface(service);
            try {
                BBLog.d(BBLog.TAG, "RemoteGuardService onServiceConnected ->connected with " + iMyAidlInterface.getServiceName());
            } catch (RemoteException e) {
                e.printStackTrace();
            }
        }

        @Override
        public void onServiceDisconnected(ComponentName name) {
            BBLog.d(BBLog.TAG,"RemoteGuardService onServiceDisconnected ->链接断开，重新启动 LocalService");
            startService(new Intent(RemoteGuardService.this,LocalService.class));
            bindService(new Intent(RemoteGuardService.this,LocalService.class),connection, Context.BIND_IMPORTANT);
        }
    };

    public RemoteGuardService() {
    }

    @Override
    public IBinder onBind(Intent intent) {
        binder = new RemoteGuardServiceBinder();
        return binder;
    }

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        BBLog.d(BBLog.TAG,"RemoteGuardService onStartCommand ->RemoteService 启动");
//        startService(new Intent(RemoteGuardService.this,LocalService.class));
        bindService(new Intent(RemoteGuardService.this,LocalService.class),connection, Context.BIND_IMPORTANT);
        return START_STICKY;
    }

    class RemoteGuardServiceBinder extends KeepAliveConnection.Stub{

        @Override
        public String getServiceName() throws RemoteException {
            return RemoteGuardService.class.getSimpleName();
        }
    }
}
