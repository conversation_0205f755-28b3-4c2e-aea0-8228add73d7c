package com.bbpos.wiseapp.tms.timer;

import android.app.AlarmManager;
import android.app.PendingIntent;
import android.content.BroadcastReceiver;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.os.Build;

import com.bbpos.wiseapp.logger.BBLog;
import com.bbpos.wiseapp.system.api.ServiceApi;
import com.bbpos.wiseapp.tms.service.HardwareDetectUploadSerivce;
import com.bbpos.wiseapp.tms.service.WiseLogUploadSerivce;
import com.bbpos.wiseapp.tms.utils.BroadcastActions;
import com.bbpos.wiseapp.tms.utils.Constants;
import com.bbpos.wiseapp.websocket.WebSocketCenter;

import java.util.Random;

public class WiseLogUploadTimer extends BroadcastReceiver {
    private static final String TAG = "WiseLogUploadTimer";
    @Override
    public void onReceive(Context context, Intent intent) {
        String action = intent.getAction();
        BBLog.v(BBLog.TAG, "WiseLogUploadTimer Receiver Broadcast " + action);
        if (BroadcastActions.WISE_LOG_UPLOAD_BC.equals(action)){
//            if (WebSocketCenter.isWebSocketConnected) {
                Intent intentService = new Intent(context, WiseLogUploadSerivce.class);
                ServiceApi.getIntance().startService(intentService);
//            }
            startWiseLogTimer(context);
        }
    }

    /**启动轮询定时广播*/
    public static void startWiseLogTimer(Context context){
        Intent intentTmp = new Intent(BroadcastActions.WISE_LOG_UPLOAD_BC);
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            intentTmp.setComponent(new ComponentName(context.getPackageName(), WiseLogUploadTimer.class.getName()));
        }
        PendingIntent pi = PendingIntent.getBroadcast(context, 0, intentTmp, PendingIntent.FLAG_UPDATE_CURRENT);
        AlarmManager am = (AlarmManager) context.getSystemService(Context.ALARM_SERVICE);
        am.cancel(pi);

        long timeOnMillis = System.currentTimeMillis() + (Constants.WISE_LOG_UPLOAD_INTERVAL() * 1000);

        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.KITKAT) {
            am.set(AlarmManager.RTC_WAKEUP, timeOnMillis, pi);
        } else {
            am.setExact(AlarmManager.RTC_WAKEUP, timeOnMillis, pi);
        }
    }
}
