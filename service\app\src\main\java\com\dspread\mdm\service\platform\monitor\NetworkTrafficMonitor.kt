package com.dspread.mdm.service.platform.monitor

import android.content.Context
import com.dspread.mdm.service.SmartMdmServiceApp
import com.dspread.mdm.service.network.websocket.message.WsMessageSender
import com.dspread.mdm.service.utils.log.Logger
import kotlinx.coroutines.*
import org.json.JSONObject
import java.text.SimpleDateFormat
import java.util.*
import java.util.concurrent.atomic.AtomicLong

/**
 * 网络流量监控器
 * 负责统计HTTP和WebSocket的流量数据，并提供定时上送功能
 */
object NetworkTrafficMonitor {
    
    private const val TAG = "NetworkTrafficMonitor"
    private const val PREFS_NAME = "network_traffic_stats"
    
    // SharedPreferences键名
    private const val KEY_HTTP_UPLOAD = "http_upload_bytes"
    private const val KEY_HTTP_DOWNLOAD = "http_download_bytes"
    private const val KEY_WS_UPLOAD = "ws_upload_bytes"
    private const val KEY_WS_DOWNLOAD = "ws_download_bytes"
    private const val KEY_LAST_RESET_DATE = "last_reset_date"
    
    // 内存中的流量统计（使用AtomicLong保证线程安全）
    private val httpUploadBytes = AtomicLong(0)
    private val httpDownloadBytes = AtomicLong(0)
    private val wsUploadBytes = AtomicLong(0)
    private val wsDownloadBytes = AtomicLong(0)
    
    // 日期格式化器（使用UTC时区）
    private val dateFormat = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault()).apply {
        timeZone = TimeZone.getTimeZone("UTC")
    }
    
    // 异步任务
    private var saveJob: Job? = null
    private var dailyUploadJob: Job? = null
    
    /**
     * 初始化流量监控器
     */
    fun init(context: Context) {
        try {
            Logger.platform("$TAG 初始化网络流量监控器")
            
            // 加载已保存的流量数据
            loadTrafficData(context)
            
            // 检查并重置日期
            checkAndResetIfNewDay(context)
            
            // 启动每日上送任务
            startDailyUploadTask(context)
            
            Logger.platform("$TAG 网络流量监控器初始化完成")
        } catch (e: Exception) {
            Logger.platformE("$TAG 初始化失败", e)
        }
    }
    
    /**
     * 记录HTTP上传流量
     */
    fun recordHttpUpload(bytes: Long) {
        if (bytes > 0) {
            httpUploadBytes.addAndGet(bytes)
            // 减少保存频率，只在累积一定流量时才保存
            if (httpUploadBytes.get() % 1024 == 0L) { // 每1KB保存一次
                saveTrafficAsync()
            }
        }
    }
    
    /**
     * 记录HTTP下载流量
     */
    fun recordHttpDownload(bytes: Long) {
        if (bytes > 0) {
            httpDownloadBytes.addAndGet(bytes)
            // 减少保存频率，只在累积一定流量时才保存
            if (httpDownloadBytes.get() % 1024 == 0L) { // 每1KB保存一次
                saveTrafficAsync()
            }
        }
    }
    
    /**
     * 记录WebSocket上传流量
     */
    fun recordWebSocketUpload(bytes: Long) {
        if (bytes > 0) {
            wsUploadBytes.addAndGet(bytes)
            // 减少保存频率，只在累积一定流量时才保存
            if (wsUploadBytes.get() % 1024 == 0L) { // 每1KB保存一次
                saveTrafficAsync()
            }
        }
    }
    
    /**
     * 记录WebSocket下载流量
     */
    fun recordWebSocketDownload(bytes: Long) {
        if (bytes > 0) {
            wsDownloadBytes.addAndGet(bytes)
            // 减少保存频率，只在累积一定流量时才保存
            if (wsDownloadBytes.get() % 1024 == 0L) { // 每1KB保存一次
                saveTrafficAsync()
            }
        }
    }
    
    /**
     * 获取今日流量统计
     */
    fun getTodayTrafficStats(): TrafficStats {
        val today = dateFormat.format(Date())
        return TrafficStats(
            httpUploadBytes = httpUploadBytes.get(),
            httpDownloadBytes = httpDownloadBytes.get(),
            wsUploadBytes = wsUploadBytes.get(),
            wsDownloadBytes = wsDownloadBytes.get(),
            date = today
        )
    }
    
    /**
     * 获取当前日期
     */
    fun getCurrentDate(): String {
        return dateFormat.format(Date())
    }
    
    /**
     * 获取上次上送日期
     */
    fun getLastUploadDate(context: Context): String {
        return try {
            val prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
            prefs.getString("last_upload_date", "") ?: ""
        } catch (e: Exception) {
            ""
        }
    }
    
    /**
     * 获取昨日流量统计
     */
    fun getYesterdayTrafficStats(): TrafficStats {
        val calendar = Calendar.getInstance(TimeZone.getTimeZone("UTC"))
        calendar.add(Calendar.DAY_OF_MONTH, -1)
        val yesterdayDate = dateFormat.format(calendar.time)
        
        val prefs = SmartMdmServiceApp.instance.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
        return TrafficStats(
            httpUploadBytes = prefs.getLong("${KEY_HTTP_UPLOAD}_$yesterdayDate", 0),
            httpDownloadBytes = prefs.getLong("${KEY_HTTP_DOWNLOAD}_$yesterdayDate", 0),
            wsUploadBytes = prefs.getLong("${KEY_WS_UPLOAD}_$yesterdayDate", 0),
            wsDownloadBytes = prefs.getLong("${KEY_WS_DOWNLOAD}_$yesterdayDate", 0),
            date = yesterdayDate
        )
    }
    
    /**
     * 记录上送日期
     */
    fun recordUploadDate(context: Context) {
        try {
            val prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
            prefs.edit()
                .putString("last_upload_date", dateFormat.format(Date()))
                .apply()
        } catch (e: Exception) {
            Logger.platformE("$TAG 记录上送日期失败", e)
        }
    }
    
    /**
     * 重置流量统计
     */
    fun resetTrafficStats() {
        httpUploadBytes.set(0)
        httpDownloadBytes.set(0)
        wsUploadBytes.set(0)
        wsDownloadBytes.set(0)
        Logger.platform("$TAG 流量统计已重置")
    }
    
    /**
     * 加载已保存的流量数据
     */
    private fun loadTrafficData(context: Context) {
        try {
            val prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
            val lastResetDate = prefs.getString(KEY_LAST_RESET_DATE, "") ?: ""
            val currentDate = dateFormat.format(Date())
            
            // 如果是同一天，加载已保存的数据
            if (lastResetDate == currentDate) {
                httpUploadBytes.set(prefs.getLong(KEY_HTTP_UPLOAD, 0))
                httpDownloadBytes.set(prefs.getLong(KEY_HTTP_DOWNLOAD, 0))
                wsUploadBytes.set(prefs.getLong(KEY_WS_UPLOAD, 0))
                wsDownloadBytes.set(prefs.getLong(KEY_WS_DOWNLOAD, 0))
                Logger.platform("$TAG 加载已保存的流量数据: ${formatBytes(getTodayTrafficStats().getTotalBytes())}")
            } else {
                Logger.platform("$TAG 新的一天，重置流量统计")
                resetTrafficStats()
            }
        } catch (e: Exception) {
            Logger.platformE("$TAG 加载流量数据失败", e)
            resetTrafficStats()
        }
    }
    
    /**
     * 检查并重置日期
     */
    private fun checkAndResetIfNewDay(context: Context) {
        try {
            val prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
            val lastResetDate = prefs.getString(KEY_LAST_RESET_DATE, "") ?: ""
            val currentDate = dateFormat.format(Date())
            
            if (lastResetDate != currentDate) {
                Logger.platform("$TAG 检测到日期变化: $lastResetDate -> $currentDate")
                
                // 保存前一天的流量数据
                if (lastResetDate.isNotEmpty()) {
                    val yesterdayStats = getTodayTrafficStats()
                    prefs.edit()
                        .putLong("${KEY_HTTP_UPLOAD}_$lastResetDate", yesterdayStats.httpUploadBytes)
                        .putLong("${KEY_HTTP_DOWNLOAD}_$lastResetDate", yesterdayStats.httpDownloadBytes)
                        .putLong("${KEY_WS_UPLOAD}_$lastResetDate", yesterdayStats.wsUploadBytes)
                        .putLong("${KEY_WS_DOWNLOAD}_$lastResetDate", yesterdayStats.wsDownloadBytes)
                        .apply()
                    Logger.platform("$TAG 保存昨日流量数据: ${formatBytes(yesterdayStats.getTotalBytes())}")
                }
                
                // 重置当前流量统计
                resetTrafficStats()
                
                // 更新重置日期
                prefs.edit()
                    .putString(KEY_LAST_RESET_DATE, currentDate)
                    .apply()
            }
        } catch (e: Exception) {
            Logger.platformE("$TAG 检查日期变化失败", e)
        }
    }
    
    /**
     * 异步保存流量数据
     */
    private fun saveTrafficAsync() {
        // 如果已经有正在运行的保存任务，直接返回，避免频繁取消
        if (saveJob?.isActive == true) {
            return
        }
        
        saveJob = CoroutineScope(Dispatchers.IO).launch {
            try {
                delay(1000) // 延迟避免频繁保存
                
                // 再次检查是否被取消
                if (!isActive) {
                    return@launch
                }
                
                val prefs = SmartMdmServiceApp.instance.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
                val today = dateFormat.format(Date())
                prefs.edit()
                    .putLong(KEY_HTTP_UPLOAD, httpUploadBytes.get())
                    .putLong(KEY_HTTP_DOWNLOAD, httpDownloadBytes.get())
                    .putLong(KEY_WS_UPLOAD, wsUploadBytes.get())
                    .putLong(KEY_WS_DOWNLOAD, wsDownloadBytes.get())
                    .putString(KEY_LAST_RESET_DATE, today)
                    .putLong("${KEY_HTTP_UPLOAD}_$today", httpUploadBytes.get()) // 保存到日期特定的键
                    .putLong("${KEY_HTTP_DOWNLOAD}_$today", httpDownloadBytes.get())
                    .putLong("${KEY_WS_UPLOAD}_$today", wsUploadBytes.get())
                    .putLong("${KEY_WS_DOWNLOAD}_$today", wsDownloadBytes.get())
                    .apply()
                Logger.platform("$TAG 流量统计保存完成")
            } catch (e: Exception) {
                // 只记录非取消异常
                if (e !is CancellationException) {
                    Logger.platformE("$TAG 保存流量统计失败", e)
                }
            }
        }
    }
    
    /**
     * 启动每日上送任务
     */
    private fun startDailyUploadTask(context: Context) {
        try {
            dailyUploadJob?.cancel()
            dailyUploadJob = CoroutineScope(Dispatchers.IO).launch {
                // 等待一段时间再开始，避免应用启动时立即执行
                delay(5000) // 等待5秒
                
                while (isActive) {
                    val nextUploadTime = getNextUploadTime()
                    val delayMillis = nextUploadTime - System.currentTimeMillis()
                    if (delayMillis > 0) {
                        Logger.platform("$TAG 等待下次上送时间: ${formatTime(nextUploadTime)}")
                        delay(delayMillis)
                    }
                    executeDailyUpload(context)
                }
            }
            Logger.platform("$TAG 每日上送任务已启动")
        } catch (e: Exception) {
            Logger.platformE("$TAG 启动每日上送任务失败", e)
        }
    }
    
    /**
     * 获取下次上送时间（UTC 00:00）
     */
    private fun getNextUploadTime(): Long {
        val calendar = Calendar.getInstance(TimeZone.getTimeZone("UTC"))
        calendar.add(Calendar.DAY_OF_MONTH, 1) // 明天
        calendar.set(Calendar.HOUR_OF_DAY, 0)
        calendar.set(Calendar.MINUTE, 0)
        calendar.set(Calendar.SECOND, 0)
        calendar.set(Calendar.MILLISECOND, 0)
        return calendar.timeInMillis
    }
    
    /**
     * 执行每日上送
     */
    private fun executeDailyUpload(context: Context) {
        try {
            Logger.platform("$TAG 执行每日上送")
            
            // 直接调用WsMessageSender，它会根据日期自动决定上送策略
            CoroutineScope(Dispatchers.IO).launch {
                try {
                    WsMessageSender.uploadNetworkTraffic(
                        trigger = com.dspread.mdm.service.network.websocket.message.strategy.UploadTriggers.DAILY_TRAFFIC_REPORT,
                        forceUpload = true
                    )
                    Logger.platform("$TAG 每日上送完成")
                } catch (e: Exception) {
                    Logger.platformE("$TAG 每日上送失败", e)
                }
            }
            
        } catch (e: Exception) {
            Logger.platformE("$TAG 执行每日上送失败", e)
        }
    }
    
    /**
     * 格式化时间显示
     */
    private fun formatTime(timestamp: Long): String {
        val dateFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault()).apply {
            timeZone = TimeZone.getTimeZone("UTC")
        }
        return dateFormat.format(Date(timestamp))
    }
    
    /**
     * 格式化字节数显示
     */
    private fun formatBytes(bytes: Long): String {
        return when {
            bytes < 1024 -> "${bytes}B"
            bytes < 1024 * 1024 -> String.format("%.1fKB", bytes / 1024.0)
            bytes < 1024 * 1024 * 1024 -> String.format("%.1fMB", bytes / (1024.0 * 1024.0))
            else -> String.format("%.1fGB", bytes / (1024.0 * 1024.0 * 1024.0))
        }
    }
    
    /**
     * 强制保存当前流量数据
     */
    fun forceSaveTrafficData() {
        try {
            val prefs = SmartMdmServiceApp.instance.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
            val today = dateFormat.format(Date())
            prefs.edit()
                .putLong(KEY_HTTP_UPLOAD, httpUploadBytes.get())
                .putLong(KEY_HTTP_DOWNLOAD, httpDownloadBytes.get())
                .putLong(KEY_WS_UPLOAD, wsUploadBytes.get())
                .putLong(KEY_WS_DOWNLOAD, wsDownloadBytes.get())
                .putString(KEY_LAST_RESET_DATE, today)
                .putLong("${KEY_HTTP_UPLOAD}_$today", httpUploadBytes.get())
                .putLong("${KEY_HTTP_DOWNLOAD}_$today", httpDownloadBytes.get())
                .putLong("${KEY_WS_UPLOAD}_$today", wsUploadBytes.get())
                .putLong("${KEY_WS_DOWNLOAD}_$today", wsDownloadBytes.get())
                .apply()
            Logger.platform("$TAG 强制保存流量统计完成")
        } catch (e: Exception) {
            Logger.platformE("$TAG 强制保存流量统计失败", e)
        }
    }
    
    /**
     * 清理资源
     */
    fun cleanup() {
        // 强制保存当前数据
        forceSaveTrafficData()
        
        // 取消异步任务
        saveJob?.cancel()
        dailyUploadJob?.cancel()
        Logger.platform("$TAG 资源清理完成")
    }
    
    /**
     * 流量统计数据类
     */
    data class TrafficStats(
        val httpUploadBytes: Long,
        val httpDownloadBytes: Long,
        val wsUploadBytes: Long,
        val wsDownloadBytes: Long,
        val date: String
    ) {
        /**
         * 获取总流量
         */
        fun getTotalBytes(): Long {
            return httpUploadBytes + httpDownloadBytes + wsUploadBytes + wsDownloadBytes
        }
        
        /**
         * 转换为JSON对象
         */
        fun toJsonObject(): JSONObject {
            return JSONObject().apply {
                put("date", convertToYyyyMmDd(date)) // 使用yyyyMMdd格式
                put("http_upload", httpUploadBytes)
                put("http_download", httpDownloadBytes)
                put("ws_upload", wsUploadBytes)
                put("ws_download", wsDownloadBytes)
                put("total", getTotalBytes())
            }
        }
        
        /**
         * 转换日期格式为yyyyMMdd
         */
        private fun convertToYyyyMmDd(dateStr: String): String {
            return try {
                val date = dateFormat.parse(dateStr)
                val targetFormat = SimpleDateFormat("yyyyMMdd", Locale.getDefault()).apply {
                    timeZone = TimeZone.getTimeZone("UTC")
                }
                targetFormat.format(date)
            } catch (e: Exception) {
                dateStr.replace("-", "")
            }
        }
    }
} 