package com.bbpos.wiseapp.tms.widget;

import android.app.Dialog;
import android.content.Context;
import android.os.Bundle;
import android.os.Handler;
import android.os.Message;
import android.util.DisplayMetrics;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.TextView;

import com.bbpos.wiseapp.logger.BBLog;
import com.bbpos.wiseapp.service.R;
import com.bbpos.wiseapp.tms.model.AppInfo;

import java.util.List;
import java.util.Timer;
import java.util.TimerTask;

public class MOSUpgradeDialog extends Dialog{
	private Context context;
	private String mTxtTitle = "";
	private String mTxtMsg = "";
	private TextView mTitle;
	private TextView mMsg;
	private TextView mTimeout;
	private TextView mBtnDelay;
	private TextView mBtnInstall;
	private View.OnClickListener delayListener;
	private View.OnClickListener installListener;
	public static MOSUpgradeDialog mDialog;

	private boolean isCreated = false;
	private Timer mTimer = null;
	private Handler mHandler = new Handler() {
		@Override
		public void handleMessage(Message msg) {
			super.handleMessage(msg);
			if (msg.what >= 0) {
				String str_hour = "";
				String str_min = "";
				String str_sec = "";
				int hour = msg.what/3600;
				int min = (msg.what-hour*3600)/60;
				int sec = msg.what%60;
				if (hour>0 && hour<10) {
					str_hour = "0"+hour+"";
				} else if (hour>=10) {
					str_hour = hour+"";
				}
				if (min>0 && min<10) {
					str_min = "0"+min+"";
				} else if (min>=10) {
					str_min = min+"";
				}
				if (sec>=0 && sec<10) {
					str_sec = "0"+sec+"";
				} else if (sec>=10) {
					str_sec = sec+"";
				}
				mTimeout.setText((hour>0?str_hour+":":"") + (min>0?str_min+":":"") +str_sec);
			} else {
				mBtnInstall.callOnClick();
				mTimer.cancel();
			}
		}
	};


	public static MOSUpgradeDialog showOSUpgradeDialog(Context context, String title, String msg, final View.OnClickListener onDelayClickListener, final View.OnClickListener onInstallClickListener){
		BBLog.w(BBLog.TAG, "showOSUpgradeDialog");
		if (mDialog != null) {
			mDialog.dismiss();
			mDialog = null;
		}
		mDialog = new MOSUpgradeDialog(context) ;
		mDialog.setTitle(title);
		mDialog.setMessage(msg);
		mDialog.setCanceledOnTouchOutside(false);
		mDialog.setDelayButton(new View.OnClickListener() {
			@Override
			public void onClick(View v) {
				if (mDialog.mTimer != null) {
					mDialog.mTimer.cancel();
				}
				mDialog.dismiss();
				onDelayClickListener.onClick(v);
			}
		});
		mDialog.setInstallButton(new View.OnClickListener() {
			@Override
			public void onClick(View v) {
				if (mDialog.mTimer != null) {
					mDialog.mTimer.cancel();
				}
				mDialog.dismiss();
				onInstallClickListener.onClick(v);
			}
		});

		Window dialogWindow = mDialog.getWindow();
		WindowManager.LayoutParams lp = dialogWindow.getAttributes();
		/*
		 * lp.x与lp.y表示相对于原始位置的偏移.
		 * 当参数值包含Gravity.LEFT时,对话框出现在左边,所以lp.x就表示相对左边的偏移,负值忽略.
		 * 当参数值包含Gravity.RIGHT时,对话框出现在右边,所以lp.x就表示相对右边的偏移,负值忽略.
		 * 当参数值包含Gravity.TOP时,对话框出现在上边,所以lp.y就表示相对上边的偏移,负值忽略.
		 * 当参数值包含Gravity.BOTTOM时,对话框出现在下边,所以lp.y就表示相对下边的偏移,负值忽略.
		 * 当参数值包含Gravity.CENTER_HORIZONTAL时
		 * ,对话框水平居中,所以lp.x就表示在水平居中的位置移动lp.x像素,正值向右移动,负值向左移动.
		 * 当参数值包含Gravity.CENTER_VERTICAL时
		 * ,对话框垂直居中,所以lp.y就表示在垂直居中的位置移动lp.y像素,正值向右移动,负值向左移动.
		 * gravity的默认值为Gravity.CENTER,即Gravity.CENTER_HORIZONTAL |
		 * Gravity.CENTER_VERTICAL.
		 *
		 * 本来setGravity的参数值为Gravity.LEFT | Gravity.TOP时对话框应出现在程序的左上角,但在
		 * 我手机上测试时发现距左边与上边都有一小段距离,而且垂直坐标把程序标题栏也计算在内了,
		 * Gravity.LEFT, Gravity.TOP, Gravity.BOTTOM与Gravity.RIGHT都是如此,据边界有一小段距离
		 */

		WindowManager m = (WindowManager)context.getSystemService(Context.WINDOW_SERVICE);
		DisplayMetrics dm = new DisplayMetrics();; // 获取屏幕宽、高用
		m.getDefaultDisplay().getMetrics(dm);
        WindowManager.LayoutParams p = dialogWindow.getAttributes(); // 获取对话框当前的参数值

		lp.width = (int) (dm.widthPixels); // 宽度
		lp.height = (int) (dm.heightPixels); // 宽度

		dialogWindow.setAttributes(lp);
		dialogWindow.setType(WindowManager.LayoutParams.TYPE_SYSTEM_ALERT);//这句话是关键
		mDialog.show();
		return mDialog;
	}

	TimerTask timerTask = new TimerTask() {
		int second = 300;
		@Override
		public void run() {
			Message msg = new Message();
			msg.what = second;
			mHandler.sendMessage(msg);
			second--;
		}
	};

	@Override
	protected void onCreate(Bundle savedInstanceState) {
		super.onCreate(savedInstanceState);
		init();

		if (mTimer == null) {
			mTimer = new Timer();
			mTimer.schedule(timerTask, 0, 1000);
		}
	}

	public MOSUpgradeDialog(Context context){
		super(context, R.style.dialog_style_ex);
		this.context = context;
	}

	public MOSUpgradeDialog(Context context, List<AppInfo> list) {
		super(context);
		this.context = context;
	}

	private void init(){
		 LayoutInflater inflater = LayoutInflater.from(context);
		 View view = inflater.inflate(R.layout.dialog_osupgrade,null);
		 setContentView(view);
		 isCreated = true;
		 findControls(view);
	}

	private void findControls(View view){
		mTitle = (TextView) view.findViewById(R.id.tv_title);
		mTitle.setText(mTxtTitle);
		mMsg = (TextView) view.findViewById(R.id.tv_msg);
		mMsg.setText(mTxtMsg);
		mTimeout = (TextView)view.findViewById(R.id.tv_timeout);
		mBtnDelay = (TextView)view.findViewById(R.id.tv_delay);
		mBtnDelay.setOnClickListener(delayListener);
		mBtnInstall = (TextView)view.findViewById(R.id.tv_install);
		mBtnInstall.setOnClickListener(installListener);
	}

	/** 
	* @Description  设置确定键内容（右键）
	* @param listener
	* @return void    
	*/
	public void setDelayButton(final View.OnClickListener listener){
		delayListener = listener;
		if(isCreated){
			mBtnDelay.setOnClickListener(delayListener);
		}
	}

	/** 
	* @Description  设置取消键内容（左键）
	* @param listener
	* @return void    
	*/
	public void setInstallButton(final View.OnClickListener listener){
		installListener = listener;
		if(isCreated){
			mBtnInstall.setOnClickListener(installListener);
		}
	}

	public void setTitle(String title){
		if (title != null) {
			mTxtTitle = title;
		}
	}

	public void setMessage(String msg){
		if (msg != null) {
			mTxtMsg = msg;
		}
	}
}
