package com.bbpos.wiseapp.tms.listener.device;

import android.content.Context;
import android.util.Log;

import com.bbpos.wiseapp.logger.BBLog;
import com.bbpos.wiseapp.tms.adapter.SystemManagerAdapter;
import com.bbpos.wiseapp.tms.utils.Constants;

import java.io.File;

/** 
 *  封装的中间件接口
 *  厂商自己的一些接口，比如下载缓冲区路径、大小、更新接口等
 * 
 * */
public class MidwareInterface{
	public static String curTermPkgs = null;
	public static String curOtaVer = null;
	/**终端os系统版本*/
	public static String osVer = null;
	/**厂商*/
	public static String manu = null;
	/**序列号*/
	public static String serial = null;
	/**型号*/
	public static String model = null;
	public static String platform = null;
	/**本地服务版本*/
	public static String serviceVer = null;
	
	public static void init(final Context context){
		try {
			SystemManagerAdapter.initTerParam(context);
		} catch (Exception e) {
			BBLog.e(BBLog.TAG, "error in getBaseSysInfo. ",e);
			manu = "";
			serial = "";
			model = "";
			osVer="";
		}
	}
}
