package com.bbpos.wiseapp.provisioning;

import android.app.Dialog;
import android.app.Notification;
import android.app.NotificationManager;
import android.app.WallpaperManager;
import android.bluetooth.BluetoothAdapter;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.graphics.Bitmap;
import android.graphics.Color;
import android.os.Build;
import android.os.Handler;
import android.os.IBinder;
import android.os.Looper;
import android.provider.Settings;
import android.text.TextUtils;
import android.util.DisplayMetrics;
import android.view.View;
import android.view.WindowManager;
import android.widget.ImageView;
import android.widget.Toast;

import com.bbpos.wiseapp.logger.BBLog;
import com.bbpos.wiseapp.logger.MyDiskLogStrategy;
import com.bbpos.wiseapp.service.R;
import com.bbpos.wiseapp.service.WakeLockService;
import com.bbpos.wiseapp.service.common.RequestPermission;
import com.bbpos.wiseapp.service.common.UsualData;
import com.bbpos.wiseapp.settings.utils.WirelessUtil;
import com.bbpos.wiseapp.settings.utils.tlv.StringUtils;
import com.bbpos.wiseapp.system.api.CustomServiceManager;
import com.bbpos.wiseapp.system.api.DeviceInfoApi;
import com.bbpos.wiseapp.system.api.SysIntermediateApi;
import com.bbpos.wiseapp.system.api.SystemApi;
import com.bbpos.wiseapp.tms.adapter.SystemManagerAdapter;
import com.bbpos.wiseapp.tms.location.GpsLocationManager;
import com.bbpos.wiseapp.tms.utils.Constants;
import com.bbpos.wiseapp.tms.utils.ContextUtil;
import com.bbpos.wiseapp.tms.utils.Helpers;
import com.bbpos.wiseapp.tms.utils.ParameterName;
import com.bbpos.wiseapp.utils.ActivityUtils;
import com.bbpos.wiseapp.utils.ByteUtils;
import com.bbpos.wiseapp.utils.FileUtils;
import com.bbpos.wiseapp.utils.HttpUtils;
import com.bbpos.wiseapp.utils.PaymentModeUtils;
import com.bbpos.wiseapp.utils.SharedPreferencesUtils;
import com.bbpos.wiseapp.utils.SystemUtils;
import com.bbpos.wiseapp.websocket.WebSocketManager;
import com.bbpos.wiseapp.zip.GzipJava;
import com.google.zxing.BarcodeFormat;
import com.google.zxing.EncodeHintType;
import com.google.zxing.MultiFormatWriter;
import com.google.zxing.common.BitMatrix;
import com.google.zxing.qrcode.decoder.ErrorCorrectionLevel;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.File;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.EnumMap;
import java.util.Map;

public class ProvisionService extends WakeLockService {
    public static final String TAG = "provisioning";
    private Context mContext = null;
    private NotificationManager manager;
    private Handler mHandler = new Handler(Looper.getMainLooper());
    private static ProgressUpdateListener listener;
    // config配置过程计数总数
    private int CONFIG_PART = 0;
    // config配置过程计数总数
    private int CONFIG_TOTAL = 6;//4

    private long lastProversionTime = 0;

    private String mType = "";

    public ProvisionService() {
        super("ProvisionService");
    }

    @Override
    protected void onHandleIntent(Intent intent) {
        mContext = getApplicationContext();

        mType = intent.getStringExtra("type");
        mHandler.post(new Runnable() {
            @Override
            public void run() {
                Toast.makeText(mContext, "Starting...", Toast.LENGTH_SHORT).show();
            }
        });
        new Thread(new Runnable() {
            @Override
            public void run() {
                uploadProductionTestLog();
            }
        }).start();
        BBLog.w(TAG, "[ProvisionService] ======onHandleIntent====== " + mType);
        processConfigInfo(mType);
    }

    private void uploadProductionTestLog() {
        try {
            File fileLog = new File("/sdcard/Share/com.bbpos.wiseposproductiontestdemo/prod_test.log");
            if (fileLog.exists()) {
                BBLog.i(TAG, "ProductionTestLog 上送日志壓縮文件 ");
                SimpleDateFormat mSimpleDateFormat = new SimpleDateFormat("yyyy-MM-dd-hh:mm:ss");
                String gzName = fileLog.getParent() + File.separator + mSimpleDateFormat.format(new Date());
                File fileLogZip = new File(gzName);
                BBLog.i(TAG, "ProductionTestLog 壓縮成文件 " + gzName);
                GzipJava.compressGZIP(fileLog, fileLogZip);
                if (fileLogZip.exists() && fileLogZip.length()>0) {
                    if (HttpUtils.urlUploadFile(HttpUtils.S3_LOG_STREAMING_URL, HttpUtils.S3_LOG_STREAMING_BUCKET_NAME, "production_test_log/" + DeviceInfoApi.getIntance().getSerialNumber(), fileLogZip)) {
                        BBLog.i(TAG, "ProductionTestLog 上传成功");
                        if (fileLogZip != null) {
                            fileLogZip.delete();
                        }
                        if (fileLog.exists()) {
                            fileLog.delete();
                        }
                    } else {
                        BBLog.i(TAG, "ProductionTestLog 上传失败");
                    }
                }
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    @Override
    public IBinder onBind(Intent intent) {
        return null;
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        if (manager != null) {
            manager.cancel(0);
        }
    }

    public static ProgressUpdateListener getUpdateListener() {
        return listener;
    }

    public static void setUpdateListener(ProgressUpdateListener updateListener) {
        listener = updateListener;
    }

    public static void startClientApp() {
        try{
            String clientAppPackageName = SharedPreferencesUtils.getSharePreferencesValue(UsualData.SHARED_PREFERENCES_STARTUP_APP_PACKAGE_NAME, "");
            if (!TextUtils.isEmpty(clientAppPackageName) && ActivityUtils.isApplicationAvilible(ContextUtil.getInstance(), clientAppPackageName)) {
                Intent intent = ContextUtil.getInstance().getPackageManager().getLaunchIntentForPackage(clientAppPackageName);
                ContextUtil.getInstance().startActivity(intent);
                BBLog.i(TAG, "START ClientApp");
            }  else {
                BBLog.i(TAG, "ClientApp not pointed or not installed");
            }
        } catch(Exception e) {
            BBLog.i(TAG, "ClientApp not installed");
        }
    }

    private synchronized void processConfigInfo(String triggerType) {
        boolean mClientSwitchFlag = false;
        boolean new_download = false;
        SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_INIT_CONFIG_FLAG, 0 + "");

        final String clientIdNew = SharedPreferencesUtils.getSharePreferencesValue(UsualData.SHARED_PREFERENCES_CLIENT_ID, "");
        final String clientIdOld = SharedPreferencesUtils.getSharePreferencesValue(UsualData.SHARED_PREFERENCES_LAST_CLIENT_ID, "");

        File configFile = null;
        String configFilePath = FileUtils.getConfigFilePath();
        BBLog.w(BBLog.TAG, "[ProvisionService] " + configFilePath);
        configFile = new File(configFilePath);
        if (Constants.DEBUG) {
            CONFIG_TOTAL = 8;
        } else {
            if (configFile.exists() && clientIdNew.equals(clientIdOld) && !"2".equals(triggerType)) {
                CONFIG_TOTAL = 8;
            } else {
//                CONFIG_TOTAL = 5;
                CONFIG_TOTAL = 8;
            }
        }
        BBLog.w(TAG, "[ProvisionService] ======Start Pro-versioning query======");
        String response = null;
        try {
            String configURL = null;
            if (!Constants.DEBUG) {
                configURL = SharedPreferencesUtils.getSharePreferencesValue(UsualData.SHARED_PREFERENCES_CONFIG_URL, UsualData.CONFIG_PRODUCTION_URL);
            } else {
                configURL = UsualData.CONFIG_UAT_URL;
            }
            BBLog.i(TAG, "[ProvisionService] ====== [ConfigInfo] ====== URL: " + configURL);
            String cid = SharedPreferencesUtils.getSharePreferencesValue(UsualData.SHARED_PREFERENCES_CLIENT_ID, "");
            String soid = SharedPreferencesUtils.getSharePreferencesValue(UsualData.SHARED_PREFERENCES_SOID, "");
            String mode = SharedPreferencesUtils.getSharePreferencesValue(UsualData.SHARED_PREFERENCES_MODE, "");
            JSONObject object = new JSONObject();
            BBLog.i(TAG, "[ProvisionService] ====== [ConfigInfo] ====== Serial: " + DeviceInfoApi.getIntance().getSerialNumber());
            object.put("SN", DeviceInfoApi.getIntance().getSerialNumber());
            if ("1".equals(triggerType)) {
                object.put("CID", cid);
                object.put("SOID", soid);
                object.put("MODE", mode);
            }
            object.put("MODEL", Build.MODEL);
            object.put("ANDROID", Build.VERSION.SDK_INT+"");

            for (int count=1; count<=3; count++) {
                BBLog.i(BBLog.TAG, "provisioning 請求第" + count + "次");
                for (int i = 0; i < 6; i++) {
                    response = HttpUtils.connectPost(configURL, object.toString());
                    if (response.contains("\r") || response.contains("\n")) {
                        response = response.replace("\r", "").replace("\n", "");
                    }
                    if (!TextUtils.isEmpty(response)) {
                        break;
                    }

                    if (i < 3) {
                        Thread.sleep(1000);
                    } else {
                        Thread.sleep(5000);
                    }
                    if (count == 3 && i == 5) {
                         if (listener!=null)
                             listener.onUpdateCompleted(false,getString(R.string.provisioning_fail));
                         return;
                    }else {
                        if (listener!=null) {
                            listener.onUpdateFail(getString(R.string.provisioning_fail_retry));
                        }
                    }
                }

//            } else {
                //本地数据了
//                response = FileUtils.readFile(FileUtils.getWiseAppConfigPath() +
//                        FileUtils.FILE_SEPARATOR + FileUtils.CONFIG_NAME + FileUtils.FILE_FORMAT);
//            }

                BBLog.i(BBLog.TAG, "[ProvisionService] ====== [ConfigInfo] ====== triggerType: " + triggerType + "\n" + response);
                if (!TextUtils.isEmpty(response)) {
                    lastProversionTime = System.currentTimeMillis();    //記錄前一次的proversioning的時間

                    FileUtils.writeFile(response);
                    JSONObject jsonObject = new JSONObject(response);
                    SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_VERSION,
                            getValueFromJsonObject(jsonObject, UsualData.SHARED_PREFERENCES_VERSION));
                    SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_MODE_IN_CONFIG,
                            getValueFromJsonObject(jsonObject, "mode"));
                    BBLog.i(BBLog.TAG, "[ProvisionService] ConfigInfo.version: " + getValueFromJsonObject(jsonObject, UsualData.SHARED_PREFERENCES_VERSION));
                    if (getValueFromJsonObject(jsonObject, "data") != null) {
                        JSONObject dataJsonObject = new JSONObject(jsonObject.getString("data"));
                        BBLog.i(BBLog.TAG, "[ProvisionService] ConfigInfo.data: " + dataJsonObject.toString());
                        mClientSwitchFlag = true;
                        cid = getValueFromJsonObject(dataJsonObject, UsualData.SHARED_PREFERENCES_CID);
                        String clientNameNew = getValueFromJsonObject(dataJsonObject, UsualData.SHARED_PREFERENCES_CLIENT_NAME);
                        String clientNameOld = SharedPreferencesUtils.getSharePreferencesValue(UsualData.SHARED_PREFERENCES_LAST_CLIENT_NAME, "");
                        BBLog.i(TAG, "[ProvisionService] local client name：" + clientNameOld + "  server client name：" + clientNameNew);
                        if (!clientNameNew.equals(clientNameOld)) {
                            mClientSwitchFlag = true;
                            SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_LAST_CLIENT_NAME, clientNameNew);
                        }
                        SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_CLIENT_NAME, clientNameNew);
                        BBLog.i(TAG, "[ProvisionService] local client ID：" + clientIdOld + "  server client ID：" + clientIdNew);
                        if (!clientIdNew.equals(clientIdOld)) {
                            mClientSwitchFlag = true;
                            SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_LAST_CLIENT_ID, clientIdNew);
                        }

                        //GPS
                        if (dataJsonObject.has(UsualData.SHARED_PREFERENCES_SYSTEM) && dataJsonObject.getJSONObject(UsualData.SHARED_PREFERENCES_SYSTEM)!=null) {
                            JSONObject systemObject = dataJsonObject.getJSONObject(UsualData.SHARED_PREFERENCES_SYSTEM);

                        }
                        if (mClientSwitchFlag) {
                            CONFIG_PART = 0;
                            BBLog.i(TAG, "[ProvisionService] Start upgrading local provisioning config");
                            CONFIG_PART++;

                            if (listener!=null) {
                                listener.onProgressUpdate(CONFIG_PART,CONFIG_TOTAL,"","");
                            }
                            BBLog.w(BBLog.TAG, "[customization] cur= "+CONFIG_PART+", total= "+CONFIG_TOTAL);
                            if (getValueFromJsonObject(dataJsonObject, "customization") != null) {
                                SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_REQUEST_TIME,
                                        getValueFromJsonObject(dataJsonObject, UsualData.SHARED_PREFERENCES_REQUEST_TIME));
                                JSONObject customJsonObject = new JSONObject(dataJsonObject.getString("customization"));
//                              BBLog.i(BBLog.TAG, "ConfigInfo.data.customization: " + customJsonObject.toString());
                                if (getValueFromJsonObject(customJsonObject, "system") != null) {
                                    JSONObject systemJsonObject = new JSONObject(customJsonObject.getString("system"));
                                    BBLog.i(BBLog.TAG, "[ProvisionService] ConfigInfo.data.customization.system: " + systemJsonObject.toString());
                                    SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_MODEL,
                                            getValueFromJsonObject(systemJsonObject, UsualData.SHARED_PREFERENCES_MODEL));
                                    SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_CONCEAL,
                                            getValueFromJsonObject(systemJsonObject, UsualData.SHARED_PREFERENCES_CONCEAL));
                                    if (getValueFromJsonObject(systemJsonObject, UsualData.SHARED_PREFERENCES_MODEL)!=null
                                            && (DeviceInfoApi.getIntance().isWisePos5Plus()
                                             || DeviceInfoApi.getIntance().isWisePos5())
                                             || DeviceInfoApi.getIntance().isWisePosTouch()
                                             || DeviceInfoApi.getIntance().isWisePosTouchPlus()
                                             || DeviceInfoApi.getIntance().isWisePosGo()
                                             || DeviceInfoApi.getIntance().isWisePosLE()
                                             || DeviceInfoApi.getIntance().isWisePosLP()) {
                                        SysIntermediateApi.getIntance().setProp("persist.bbpos.model_string", SharedPreferencesUtils.getSharePreferencesValue(UsualData.SHARED_PREFERENCES_MODEL, "WisePOS Plus"));
                                    }

                                    if (getValueFromJsonObject(systemJsonObject, UsualData.SHARED_PREFERENCES_PWD_MODE)!=null && !getValueFromJsonObject(systemJsonObject, UsualData.SHARED_PREFERENCES_PWD_MODE).equals(SharedPreferencesUtils.getSharePreferencesValue(UsualData.SHARED_PREFERENCES_PWD_MODE, "9"))) {
                                        SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_PWD_MODE,
                                                getValueFromJsonObject(systemJsonObject, UsualData.SHARED_PREFERENCES_PWD_MODE));
                                        if (getValueFromJsonObject(systemJsonObject, UsualData.SHARED_PREFERENCES_PWD_MODE) != null && (DeviceInfoApi.getIntance().isWisePos5Plus() || DeviceInfoApi.getIntance().isWisePos5())) {
                                            SysIntermediateApi.getIntance().setProp("persist.bbpos.pwd_mode", SharedPreferencesUtils.getSharePreferencesValue(UsualData.SHARED_PREFERENCES_PWD_MODE, "0"));
                                            if ("2".equals(SharedPreferencesUtils.getSharePreferencesValue(UsualData.SHARED_PREFERENCES_PWD_MODE, "0"))) {
                                                BBLog.i(TAG, "set OOBE PWD as 111111");
                                                Settings.System.putString(ContextUtil.getInstance().getContentResolver(), "oobe_password", "96E79218965EB72C92A549DD5A330112");
                                            } else if ("0".equals(SharedPreferencesUtils.getSharePreferencesValue(UsualData.SHARED_PREFERENCES_PWD_MODE, "0"))) {
                                                BBLog.i(TAG, "clear OOBE PWD");
                                                Settings.System.putString(ContextUtil.getInstance().getContentResolver(), "oobe_password", "null");
                                            }
                                        }
                                    }
                                    if (systemJsonObject.has(UsualData.SHARED_PREFERENCES_GMS_APPLIST)) {
                                        Constants.m_gms_applist = systemJsonObject.getJSONArray(UsualData.SHARED_PREFERENCES_GMS_APPLIST).toString();
                                        if (Constants.m_gms_applist!=null && Constants.m_gms_applist.length()>0) {
                                            SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_GMS_APPLIST, Constants.m_gms_applist);
                                            Constants.m_need_gms_download = true;
                                            Intent intentservice = new Intent(ProvisionService.this, ProvisionDownloadService.class);
                                            startService(intentservice);
                                        }
                                    }
                                    SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_SYSTEM_LOGO,
                                            getValueFromJsonObject(systemJsonObject, UsualData.SHARED_PREFERENCES_SYSTEM_LOGO));
                                    SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_SYSTEM_LOGO_MD5,
                                            getValueFromJsonObject(systemJsonObject, UsualData.SHARED_PREFERENCES_SYSTEM_LOGO_MD5));
                                    SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_BOOT_ANIMATION,
                                            getValueFromJsonObject(systemJsonObject, UsualData.SHARED_PREFERENCES_BOOT_ANIMATION));
                                    SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_BOOT_ANIMATION_MD5,
                                            getValueFromJsonObject(systemJsonObject, UsualData.SHARED_PREFERENCES_BOOT_ANIMATION_MD5));
                                    SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_TIMEZONE,
                                            getValueFromJsonObject(systemJsonObject, UsualData.SHARED_PREFERENCES_TIMEZONE));
                                    //可以延后做下载Logo和bootAnimation动作
                                    String kiosk = SharedPreferencesUtils.getSharePreferencesValue(UsualData.SHARED_PREFERENCES_KIOSK, "false");
                                    SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_KIOSK,
                                            getValueFromJsonObject(systemJsonObject, UsualData.SHARED_PREFERENCES_KIOSK));
                                    if (!kiosk.equals(getValueFromJsonObject(systemJsonObject, UsualData.SHARED_PREFERENCES_KIOSK))) {
                                        if ("true".equals(getValueFromJsonObject(systemJsonObject, UsualData.SHARED_PREFERENCES_KIOSK))) {
                                            SysIntermediateApi.getIntance().updateSystemProperty("true");
                                        } else if ("false".equals(getValueFromJsonObject(systemJsonObject, UsualData.SHARED_PREFERENCES_KIOSK))) {
                                            SysIntermediateApi.getIntance().updateSystemProperty("false");
                                        }
                                    } else {
                                        BBLog.e(BBLog.TAG, "Kiosk Mode not change! ignore");
                                    }

                                    if (!Helpers.isMobile(ProvisionService.this)) {
                                        SystemManagerAdapter.updateSystem(ProvisionService.this, null, 4);
                                    }
                                    if (getValueFromJsonObject(systemJsonObject, UsualData.SHARED_PREFERENCES_ENABLE_WISELOG) != null) {
                                        SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_ENABLE_WISELOG,
                                                getValueFromJsonObject(systemJsonObject, UsualData.SHARED_PREFERENCES_ENABLE_WISELOG));
                                        if ("true".equals(getValueFromJsonObject(systemJsonObject, UsualData.SHARED_PREFERENCES_ENABLE_WISELOG))) {
                                            MyDiskLogStrategy.enableSaveLoggerFile(true);
                                        } else {
                                            MyDiskLogStrategy.enableSaveLoggerFile(false);
                                        }
                                    }
                                    if (getValueFromJsonObject(systemJsonObject, UsualData.SHARED_PREFERENCES_ALLOW_BT_LISTENING) != null) {
                                        SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_ALLOW_BT_LISTENING,
                                                getValueFromJsonObject(systemJsonObject, UsualData.SHARED_PREFERENCES_ALLOW_BT_LISTENING));
                                    }
                                    if (getValueFromJsonObject(systemJsonObject, "powerSaveMode") != null) {
                                        JSONObject powerSaveModeJsonObject = new JSONObject(systemJsonObject.getString("powerSaveMode"));
                                        BBLog.i(BBLog.TAG, "[ProvisionService] ConfigInfo.data.customization.system.powerSaveMode: " + powerSaveModeJsonObject.toString());
                                        String powerSaveModeEnable = getValueFromJsonObject(powerSaveModeJsonObject, UsualData.SHARED_PREFERENCES_POWER_SAVE_MODE_ENABLE);
                                        SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_POWER_SAVE_MODE_ENABLE, powerSaveModeEnable);
                                        String screenTimeout = getValueFromJsonObject(powerSaveModeJsonObject, UsualData.SHARED_PREFERENCES_SCREEN_TIMEOUT);
                                        if (!TextUtils.isEmpty(screenTimeout) && ByteUtils.isNumeric(screenTimeout)) {
                                            int timeout = Integer.parseInt(screenTimeout);
//                                            BBLog.i(BBLog.TAG, "[ProvisionService] ConfigInfo.data.customization.system.powerSaveMode.enable: " + powerSaveModeEnable);
//                                            BBLog.i(BBLog.TAG, "[ProvisionService] ConfigInfo.data.customization.system.powerSaveMode.timeout: " + timeout);
                                            if (timeout != 0 && "1".equals(powerSaveModeEnable)) {
                                                SystemApi.setScreenOffTime(getApplicationContext(), timeout);
                                            }
                                        }
                                        SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_SCREEN_TIMEOUT, screenTimeout);
                                    }
                                    BBLog.e(BBLog.TAG, "processConfigInfo: "+getValueFromJsonObject(systemJsonObject, "gps"));
                                    if (getValueFromJsonObject(systemJsonObject, "gps") != null) {
                                        JSONObject gpsJsonObject = systemJsonObject.getJSONObject(UsualData.SHARED_PREFERENCES_GPS);
                                        BBLog.e(BBLog.TAG, "processConfigInfo: gpsJsonObject = "+gpsJsonObject);
//                                        BBLog.i(BBLog.TAG, "[ProvisionService] ConfigInfo.data.customization.system.gps: " + gpsJsonObject.toString());
                                        SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_ISCAREGPS,
                                                getValueFromJsonObject(gpsJsonObject, UsualData.SHARED_PREFERENCES_ISCAREGPS));
                                        SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_GPS_UPDATE_TIME ,
                                                getValueFromJsonObject(gpsJsonObject, UsualData.SHARED_PREFERENCES_GPS_UPDATE_TIME));
                                        SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_GPS_UPDATE_DISTANCE ,
                                                getValueFromJsonObject(gpsJsonObject, UsualData.SHARED_PREFERENCES_GPS_UPDATE_DISTANCE));
                                        SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_GPS_UPDATE_SCHEDULETIME ,
                                                getValueFromJsonObject(gpsJsonObject, UsualData.SHARED_PREFERENCES_GPS_UPDATE_SCHEDULETIME));
                                        SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_GPS_UPDATE_MAXLOCATETIME ,
                                                getValueFromJsonObject(gpsJsonObject, UsualData.SHARED_PREFERENCES_GPS_UPDATE_MAXLOCATETIME));
                                        if (getValueFromJsonObject(gpsJsonObject, UsualData.SHARED_PREFERENCES_GPS_GEOFENCE_VALID_DISTANCE) != null) {
                                            SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_GPS_GEOFENCE_VALID_DISTANCE,
                                                    getValueFromJsonObject(gpsJsonObject, UsualData.SHARED_PREFERENCES_GPS_GEOFENCE_VALID_DISTANCE));
                                            try {
                                                if (StringUtils.isNumeric(getValueFromJsonObject(gpsJsonObject, UsualData.SHARED_PREFERENCES_GPS_GEOFENCE_VALID_DISTANCE))) {
                                                    GpsLocationManager.validErrorDistance = Long.parseLong(getValueFromJsonObject(gpsJsonObject, UsualData.SHARED_PREFERENCES_GPS_GEOFENCE_VALID_DISTANCE));
                                                }
                                            } catch (Exception e) {
                                                e.printStackTrace();
                                            }
                                        }
                                    }
                                    if (getValueFromJsonObject(systemJsonObject, UsualData.SHARED_PREFERENCES_PACKAGE_MANAGER) != null) {
                                        JSONObject packageManagerJson = systemJsonObject.getJSONObject(UsualData.SHARED_PREFERENCES_PACKAGE_MANAGER);
                                        if (getValueFromJsonObject(packageManagerJson, UsualData.SHARED_PREFERENCES_APK_VERIFY) != null) {
                                            String apkVerify = packageManagerJson.getString(UsualData.SHARED_PREFERENCES_APK_VERIFY);
                                            BBLog.e(TAG, "— APK Signature Verify before installing = " + apkVerify);
                                            if (!TextUtils.isEmpty(apkVerify) && !apkVerify.equals(SharedPreferencesUtils.getSharePreferencesValue(UsualData.SHARED_PREFERENCES_APK_VERIFY , ""))) {
                                                if ("true".equals(apkVerify)) {
                                                    BBLog.e(TAG, "— Enable APK Signature Verification before installing —");
                                                    SysIntermediateApi.getIntance().setProp("persist.bbpos.enable_apk_verify", "true"); //Enable 驗簽
                                                    SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_APK_VERIFY , apkVerify);
                                                }
                                                if ("false".equals(apkVerify)) {
                                                    BBLog.e(TAG, "— Disable APK Signature Verification before installing —");
                                                    SysIntermediateApi.getIntance().setProp("persist.bbpos.enable_apk_verify", "false"); //Disable 驗簽
                                                    SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_APK_VERIFY , apkVerify);
                                                }
                                            }
                                        }
                                        if (getValueFromJsonObject(packageManagerJson, UsualData.SHARED_PREFERENCES_INSTALLER_PACKAGE_NAME) != null) {
                                            try {
                                                String package_list = "";
                                                JSONArray packageList = packageManagerJson.getJSONArray(UsualData.SHARED_PREFERENCES_INSTALLER_PACKAGE_NAME);
                                                BBLog.e(TAG, "— Installer Package Name — installerPackageName = " + packageList.toString());
                                                if (packageList.length()>0 && !packageList.toString().equals(SharedPreferencesUtils.getSharePreferencesValue(UsualData.SHARED_PREFERENCES_INSTALLER_PACKAGE_NAME , ""))) {
                                                    for (int i = 0; i < packageList.length(); i++) {
                                                        if (i == 0) {
                                                            package_list += packageList.getJSONObject(i).optString("package_name");
                                                        } else {
                                                            package_list = package_list + "|" + packageList.getJSONObject(i).optString("package_name");
                                                        }
                                                    }
                                                    SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_INSTALLER_PACKAGE_NAME, packageList.toString());
                                                    SystemApi.setAllowInstallApkPackeName(mContext, package_list);
                                                }
                                            } catch (JSONException ex) {
                                                ex.printStackTrace();
                                            }
                                        }
                                        if (getValueFromJsonObject(packageManagerJson, UsualData.SHARED_PREFERENCES_PAYMENT_PACKAGE_LIST) != null) {
                                            String package_list = getValueFromJsonObject(packageManagerJson, UsualData.SHARED_PREFERENCES_PAYMENT_PACKAGE_LIST);
                                            BBLog.e(TAG, "— Payment Package List — = " + package_list);
                                            if (!TextUtils.isEmpty(package_list)) {
                                                if (!package_list.equals(PaymentModeUtils.getEnablePaymentAppPackageList(mContext))) {
                                                    SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_PAYMENT_PACKAGE_LIST, Constants.PRE_PROVISION + package_list);
                                                    PaymentModeUtils.setEnablePaymentAppPackageList(mContext, package_list);
                                                }
                                            } else {
                                                SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_PAYMENT_PACKAGE_LIST,  Constants.PRE_PROVISION);
                                                PaymentModeUtils.setDisablePaymentFunction(mContext);
                                            }
                                        }
                                    }
                                    BBLog.d(BBLog.TAG,"Phone & MMS block : " + getValueFromJsonObject(systemJsonObject, UsualData.SHARED_PREFERENCES_APK_BLOCK));
                                    //設置禁用的應用程序
                                    if (getValueFromJsonObject(systemJsonObject, UsualData.SHARED_PREFERENCES_APK_BLOCK) != null) {
                                        JSONObject apkBlockJson = systemJsonObject.getJSONObject(UsualData.SHARED_PREFERENCES_APK_BLOCK);

                                        SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_APK_BLOCK_ENABLE ,
                                                getValueFromJsonObject(apkBlockJson, UsualData.SHARED_PREFERENCES_APK_BLOCK_ENABLE));

                                        long utc = 0l;
                                        long utc_target = 0l;
                                        try {
                                            utc = Long.valueOf(SysIntermediateApi.getIntance().getProp("ro.build.date.utc"));
                                            utc_target = new SimpleDateFormat("yyyy-MM-dd").parse("2022-04-21").getTime();
                                            BBLog.i(BBLog.TAG, "==> ro.build.date.utc=" + utc + "   utc_target=" + utc_target);
                                        } catch (Exception e) {
                                            e.printStackTrace();
                                        }

                                        if ( utc * 1000 > utc_target
                                                && (DeviceInfoApi.getIntance().isWisePos5()
                                                || DeviceInfoApi.getIntance().isWisePos5Plus())) {

                                            if (getValueFromJsonObject(apkBlockJson, UsualData.SHARED_PREFERENCES_APK_BLOCK_ENABLE) != null) {
                                                String enable = apkBlockJson.getString(UsualData.SHARED_PREFERENCES_APK_BLOCK_ENABLE);
                                                String localRomData = CustomServiceManager.getInstance().queryHiddenAppList() != null ? CustomServiceManager.getInstance().queryHiddenAppList() : "";
                                                String serverData = getValueFromJsonObject(apkBlockJson, UsualData.SHARED_PREFERENCES_APK_BLOCK_LIST);
                                                BBLog.e(BBLog.TAG, "Phone & MMS block enableFlag : " + enable + ", apkBlockList = " + getValueFromJsonObject(apkBlockJson, UsualData.SHARED_PREFERENCES_APK_BLOCK_LIST) + ", localRomData = " + localRomData);
                                                if ("true".equals(enable)) {
                                                    //下發的內容手動添加上\n分隔符
                                                    if (!TextUtils.isEmpty(serverData)) {
                                                        StringBuilder builder = new StringBuilder();
                                                        String[] appNames = serverData.split(",");
                                                        for (final String appName : appNames) {
                                                            builder.append(appName.trim()).append("\n");
                                                        }
                                                        serverData = builder.toString();
                                                    } else {
                                                        serverData = "";
                                                    }

                                                    if (CustomServiceManager.getInstance().hasInit()) {
                                                        if (TextUtils.isEmpty(serverData) && !TextUtils.isEmpty(localRomData)) {
                                                            BBLog.e(BBLog.TAG, " Phone & MMS block reset");
                                                            CustomServiceManager.getInstance().setHiddenAppList("");
                                                            SysIntermediateApi.getIntance().setProp("persist.security.disable_phone", "false");
                                                            SysIntermediateApi.getIntance().setProp("persist.security.disable_mms", "false");
                                                            SystemApi.reboot(mContext);
                                                        } else if (!localRomData.equals(serverData)) {
                                                            CustomServiceManager.getInstance().setHiddenAppList(serverData);
                                                            if (serverData.contains("Mms") || serverData.contains("QtiDialer")) {
                                                                if (serverData.contains("Mms")) {
                                                                    SysIntermediateApi.getIntance().setProp("persist.security.disable_mms", "true");
                                                                } else {
                                                                    SysIntermediateApi.getIntance().setProp("persist.security.disable_mms", "false");
                                                                }
                                                                if (serverData.contains("QtiDialer")) {
                                                                    SysIntermediateApi.getIntance().setProp("persist.security.disable_phone", "true");
                                                                } else {
                                                                    SysIntermediateApi.getIntance().setProp("persist.security.disable_phone", "false");
                                                                }
                                                                Constants.IS_NEED_REBOOT = true;
                                                            }
                                                        }
                                                    } else {
                                                        BBLog.e(BBLog.TAG, " Phone & MMS block CustomServiceManager no init: ");
                                                    }
                                                } else {
                                                    if (!TextUtils.isEmpty(localRomData)) {
                                                        if (CustomServiceManager.getInstance().hasInit()) {
                                                            CustomServiceManager.getInstance().setHiddenAppList("");
                                                            SysIntermediateApi.getIntance().setProp("persist.security.disable_phone", "false");
                                                            SysIntermediateApi.getIntance().setProp("persist.security.disable_mms", "false");
                                                            SystemApi.reboot(mContext);
                                                        } else {
                                                            BBLog.e(BBLog.TAG, "Phone & MMS block CustomServiceManager no init: ");
                                                        }
                                                    }
                                                }
                                            }
                                        } else {
                                            BBLog.e(BBLog.TAG, "Phone & MMS block --> 当前系统不支持 电话&短信 禁用功能");
                                        }
                                    }
                                    SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_DIALOG_SWITCH,
                                            getValueFromJsonObject(systemJsonObject, UsualData.SHARED_PREFERENCES_DIALOG_SWITCH));
                                }
                                if (getValueFromJsonObject(customJsonObject, "launcher") != null) {
                                    final JSONObject luancherJsonObject = new JSONObject(customJsonObject.getString("launcher"));
//                            BBLog.i(BBLog.TAG, "ConfigInfo.data.customization.launcher: " + luancherJsonObject.toString());
                                    SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_COLOR_STYLE,
                                            getValueFromJsonObject(luancherJsonObject, UsualData.SHARED_PREFERENCES_COLOR_STYLE));
                                    SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_LOGO_URL,
                                            getValueFromJsonObject(luancherJsonObject, UsualData.SHARED_PREFERENCES_LOGO_URL));
                                    SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_WALLPAPER_URL,
                                            getValueFromJsonObject(luancherJsonObject, UsualData.SHARED_PREFERENCES_WALLPAPER_URL));
                                    SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_STARTUP_APP_PACKAGE_NAME,
                                            getValueFromJsonObject(luancherJsonObject, UsualData.SHARED_PREFERENCES_STARTUP_APP_PACKAGE_NAME));
                                    SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_ACCESS_POLLING_TIME,
                                            getValueFromJsonObject(luancherJsonObject, UsualData.SHARED_PREFERENCES_ACCESS_POLLING_TIME));
                                    ProvisionTimer.resetProvisionTimer(ContextUtil.getInstance());
                                    SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_CLIENT_APP_URL,
                                            getValueFromJsonObject(luancherJsonObject, UsualData.SHARED_PREFERENCES_CLIENT_APP_URL));
//                                    SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_CLIENT_APP_URL_MD5,
//                                            getValueFromJsonObject(luancherJsonObject, UsualData.SHARED_PREFERENCES_CLIENT_APP_URL_MD5));
									//下載客戶證書信息
									SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_CUSTOMER_CA_CERT_SWITCH,
											getValueFromJsonObject(luancherJsonObject, UsualData.SHARED_PREFERENCES_CUSTOMER_CA_CERT_SWITCH));
									SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_CUSTOMER_CA_CERT_MD5,
											getValueFromJsonObject(luancherJsonObject, UsualData.SHARED_PREFERENCES_CUSTOMER_CA_CERT_MD5));
									SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_CUSTOMER_CA_CERT_URL,
											getValueFromJsonObject(luancherJsonObject, UsualData.SHARED_PREFERENCES_CUSTOMER_CA_CERT_URL));
									Thread.sleep(100);

                                    String channelID = "1";
                                    String channelName = "channel_1";
                                    manager = SystemApi.createCustomnNotificationManager(ProvisionService.this, channelID, channelName);
                                    Notification notification = SystemApi.createCustomnNotification(ProvisionService.this, channelID, android.R.drawable.stat_sys_download, getString(R.string.provisioning_download), getString(R.string.downloading));
                                    manager.notify(0, notification);

                                    CONFIG_PART++;
                                    int trycount = 3;
                                    boolean download_suc = false;
                                    BBLog.w(TAG, "Download Customer LOGO");
                                    if (listener!=null) {
                                        listener.onProgressUpdate(CONFIG_PART,CONFIG_TOTAL,"","");
                                    }
                                    BBLog.w(BBLog.TAG, "[开始下载客户LOGO] cur= "+CONFIG_PART+", total= "+CONFIG_TOTAL);
                                    while (trycount-- > 0 && !download_suc) {
                                        download_suc = HttpUtils.url2bitmap(getValueFromJsonObject(luancherJsonObject, UsualData.SHARED_PREFERENCES_LOGO_URL),
                                                getValueFromJsonObject(luancherJsonObject, UsualData.SHARED_PREFERENCES_LOGO_URL_MD5),
                                                "Logo", new HttpUtils.FileDownloadCallBack() {
                                                    @Override
                                                    public void requestSuccess(JSONObject responseJson) throws Exception {

                                                    }

                                                    @Override
                                                    public void requestFail(int errorCode, String errorStr) {

                                                    }

                                                    @Override
                                                    public void onDownloading(long curFileSize, long fileSize) {

                                                    }
                                                });
                                        BBLog.d(TAG, "processConfigInfo: download_suc1：  " + download_suc);
                                        Constants.IS_LOGO_DOWNLOAD_SUCCESS = download_suc;
                                    }
                                    CONFIG_PART++;
                                    trycount = 3;
                                    download_suc = false;
                                    BBLog.w(TAG, "Download Customer WALLPAPER");
                                    if (listener!=null) {
                                        listener.onProgressUpdate(CONFIG_PART,CONFIG_TOTAL,"","");
                                    }
                                    BBLog.w(BBLog.TAG, "[开始下载客户WALLPAPER] cur= "+CONFIG_PART+", total= "+CONFIG_TOTAL);
                                    while (trycount-- > 0 && !download_suc) {
                                        download_suc = HttpUtils.url2bitmap(getValueFromJsonObject(luancherJsonObject, UsualData.SHARED_PREFERENCES_WALLPAPER_URL),
                                                getValueFromJsonObject(luancherJsonObject, UsualData.SHARED_PREFERENCES_WALLPAPER_URL_MD5),
                                                "wallpaper", new HttpUtils.FileDownloadCallBack() {
                                                    @Override
                                                    public void onDownloading(long curFileSize, long fileSize) {

                                                    }

                                                    @Override
                                                    public void requestSuccess(JSONObject responseJson) throws Exception {

                                                    }

                                                    @Override
                                                    public void requestFail(int errorCode, String errorStr) {

                                                    }
                                                });
                                        BBLog.d(TAG, "processConfigInfo: download_suc2：  " + download_suc);
                                        Constants.IS_WALLPAPER_DOWNLOAD_SUCCESS = download_suc;
                                        if (download_suc) {
                                            WallpaperManager myWallpaperManager = WallpaperManager.getInstance(getApplicationContext());
                                            try {
                                                myWallpaperManager.setBitmap(Helpers.getImageDrawable(FileUtils.getWiseAppConfigPath()+FileUtils.FILE_SEPARATOR+FileUtils.FILE_WALLPAPER));
                                            } catch (IOException e) {
                                                e.printStackTrace();
                                            }
                                        }
                                    }
                                    CONFIG_PART++;
                                    trycount = 3;
                                    download_suc = false;
                                    if (listener!=null) {
                                        listener.onProgressUpdate(CONFIG_PART,CONFIG_TOTAL,"","");
                                    }
                                    BBLog.w(BBLog.TAG, "[开始下载系统LOGO] cur= "+CONFIG_PART+", total= "+CONFIG_TOTAL);
                                    String systemLogoUrl = SharedPreferencesUtils.getSharePreferencesValue(UsualData.SHARED_PREFERENCES_SYSTEM_LOGO, "");
                                    String systemLogoUrlMd5 = SharedPreferencesUtils.getSharePreferencesValue(UsualData.SHARED_PREFERENCES_SYSTEM_LOGO_MD5, "");
                                    if (!TextUtils.isEmpty(systemLogoUrl)) {
                                        BBLog.w(TAG, "Download System Boot LOGO");
                                        while (trycount-- > 0 && !download_suc) {
                                            download_suc = HttpUtils.url2file(systemLogoUrl, FileUtils.getWiseAppConfigPath() + FileUtils.FILE_SEPARATOR + "logo.bin", systemLogoUrlMd5, new HttpUtils.FileDownloadCallBack() {
                                                @Override
                                                public void onDownloading(long curFileSize, long fileSize) {

                                                }

                                                @Override
                                                public void requestSuccess(JSONObject responseJson) throws Exception {

                                                }

                                                @Override
                                                public void requestFail(int errorCode, String errorStr) {

                                                }
                                            });
                                        }
                                        if (download_suc) {
                                            SystemManagerAdapter.updateSystem(ProvisionService.this, null, 1);
                                            Constants.IS_CUSTOMER_BOOT_LOGO_DOWNLOAD_SUCCESS = true;
                                        }
                                    } else {
                                        Constants.IS_CUSTOMER_BOOT_LOGO_DOWNLOAD_SUCCESS = true;
                                    }
                                    CONFIG_PART++;
                                    trycount = 3;
                                    if (listener!=null) {
                                        listener.onProgressUpdate(CONFIG_PART,CONFIG_TOTAL,"","");
                                    }
                                    BBLog.w(BBLog.TAG, "[开始下载系统开机动画] cur= "+CONFIG_PART+", total= "+CONFIG_TOTAL);
                                    download_suc = false;
                                    String bootAnimationUrl = SharedPreferencesUtils.getSharePreferencesValue(UsualData.SHARED_PREFERENCES_BOOT_ANIMATION, "");
                                    String bootAnimationUrlMd5 = SharedPreferencesUtils.getSharePreferencesValue(UsualData.SHARED_PREFERENCES_BOOT_ANIMATION_MD5, "");
                                    if (!TextUtils.isEmpty(bootAnimationUrl)) {
                                        BBLog.w(TAG, "Download System Boot Animation");
                                        while (trycount-- > 0 && !download_suc) {
                                            download_suc = HttpUtils.url2file(bootAnimationUrl, FileUtils.getWiseAppConfigPath() + FileUtils.FILE_SEPARATOR + "bootanimation.zip", bootAnimationUrlMd5, new HttpUtils.FileDownloadCallBack() {
                                                @Override
                                                public void onDownloading(long curFileSize, long fileSize) {

                                                }

                                                @Override
                                                public void requestSuccess(JSONObject responseJson) throws Exception {

                                                }

                                                @Override
                                                public void requestFail(int errorCode, String errorStr) {

                                                }
                                            });
                                        }
                                        if (download_suc) {
                                            SystemManagerAdapter.updateSystem(ProvisionService.this, null, 2);
                                            Constants.IS_CUSTOMER_BOOT_ANIM_DOWNLOAD_SUCCESS = true;
                                        }
                                    } else {
                                        Constants.IS_CUSTOMER_BOOT_ANIM_DOWNLOAD_SUCCESS = true;
                                    }
                                    CONFIG_PART++;
                                    if (listener!=null) {
                                        listener.onProgressUpdate(CONFIG_PART,CONFIG_TOTAL,"","");
                                    }
                                    BBLog.w(BBLog.TAG, "[开始下載客戶APK] cur= "+CONFIG_PART+", total= "+CONFIG_TOTAL);
                                    BBLog.w(BBLog.TAG, "[开始下載客戶APK] OLD_MD5 = "+Constants.LAST_CLIENT_APK_MD5);
                                    final String clientAppPackageName = SharedPreferencesUtils.getSharePreferencesValue(UsualData.SHARED_PREFERENCES_STARTUP_APP_PACKAGE_NAME, "");
                                    String clientAppUrl = SharedPreferencesUtils.getSharePreferencesValue(UsualData.SHARED_PREFERENCES_CLIENT_APP_URL, "");
                                    String clientAppUrlMd5 = getValueFromJsonObject(luancherJsonObject, UsualData.SHARED_PREFERENCES_CLIENT_APP_URL_MD5);
                                    final String apkPath = FileUtils.getWiseAppConfigPath() + FileUtils.FILE_SEPARATOR + "clientApp";
                                    if (!TextUtils.isEmpty(clientAppPackageName) && !TextUtils.isEmpty(clientAppUrl) && !TextUtils.isEmpty(clientAppUrlMd5)) {
                                        if (!clientAppUrlMd5.equalsIgnoreCase(Constants.LAST_CLIENT_APK_MD5) || !ActivityUtils.isApplicationAvilible(ProvisionService.this, clientAppPackageName)) {
                                            BBLog.w(TAG, "Download Client APK url=" + clientAppUrl + "   md5=" + clientAppUrlMd5);
                                            trycount = 3;
                                            download_suc = false;
                                            while (trycount-- > 0 && !download_suc) {
                                                download_suc = HttpUtils.url2file(clientAppUrl, apkPath, clientAppUrlMd5, new HttpUtils.FileDownloadCallBack() {
                                                    @Override
                                                    public void onDownloading(long curFileSize, long fileSize) {
                                                        BBLog.w(Constants.TAG, "下載客戶APK：" + (curFileSize * 100) / fileSize);
                                                    }

                                                    @Override
                                                    public void requestSuccess(JSONObject responseJson) throws Exception {

                                                    }

                                                    @Override
                                                    public void requestFail(int errorCode, String errorStr) {

                                                    }
                                                });
                                                BBLog.d(TAG, "processConfigInfo: download_suc3：  " + download_suc);
                                                Constants.IS_CUSTOMER_APP_DOWNLOAD_SUCCESS = download_suc;
                                            }
                                            if (download_suc) {
                                                BBLog.w(TAG, "Download completed, start installing ClientApp");
                                                new_download = true;
                                                SystemManagerAdapter.installApk(ProvisionService.this, apkPath, new SystemManagerAdapter.ApkInstallCompleted() {
                                                    @Override
                                                    public void onInstallFinished(String pkgName, int returnCode) {
                                                        BBLog.i(TAG, "packageInstalled " + returnCode);
                                                        if (returnCode == 1) {
                                                            BBLog.w(TAG, "ClientApp install succ");
                                                            Constants.LAST_CLIENT_APK_MD5 = getValueFromJsonObject(luancherJsonObject, UsualData.SHARED_PREFERENCES_CLIENT_APP_URL_MD5);
                                                            SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_CLIENT_APP_URL_MD5,
                                                                getValueFromJsonObject(luancherJsonObject, UsualData.SHARED_PREFERENCES_CLIENT_APP_URL_MD5));
                                                            //设置为Launcher
//                                                            try {
//                                                                Thread.sleep(1000);
//                                                            } catch (InterruptedException e) {
//                                                                e.printStackTrace();
//                                                            }
                                                            if (UsualData.LOADER_711_PACKAGE_NAME.equals(clientAppPackageName)) {
//                                                                ContextUtil.setLauncherApp(getApplicationContext(), UsualData.LAUNCHER_PACKAGE_NAME);
                                                            } else {
                                                                startClientApp();
                                                            }
                                                        }
                                                    }
                                                });
                                            }
                                        } else {
                                            Constants.IS_CUSTOMER_APP_DOWNLOAD_SUCCESS = true;
                                        }


                                    } else {
                                        Constants.IS_CUSTOMER_APP_DOWNLOAD_SUCCESS = true;
                                    }
									//下載客戶證書信息
									CONFIG_PART++;
									trycount = 3;
									download_suc = false;
									if (listener!=null) {
										listener.onProgressUpdate(CONFIG_PART,CONFIG_TOTAL,"","");
									}
									BBLog.w(BBLog.TAG, "[开始下载客戶證書] cur= "+CONFIG_PART+", total= "+CONFIG_TOTAL);
									String caCertUrl = SharedPreferencesUtils.getSharePreferencesValue(UsualData.SHARED_PREFERENCES_CUSTOMER_CA_CERT_URL, "");
									String caCertMd5 = SharedPreferencesUtils.getSharePreferencesValue(UsualData.SHARED_PREFERENCES_CUSTOMER_CA_CERT_MD5, "");
									String caCertSwitch = SharedPreferencesUtils.getSharePreferencesValue(UsualData.SHARED_PREFERENCES_CUSTOMER_CA_CERT_SWITCH, "false");
									BBLog.e(BBLog.TAG, "processConfigInfo: caCertSwitch = " + caCertSwitch + ", caCertMd5 = " + caCertMd5 + ", caCertUrl = " + caCertUrl );
									if (Boolean.valueOf(caCertSwitch)) {
										if (!TextUtils.isEmpty(caCertUrl)) {
											BBLog.w(TAG, "Download customer'certificate");
											while (trycount-- > 0 && !download_suc) {
												download_suc = HttpUtils.url2file(caCertUrl, FileUtils.getWiseAppConfigPath() + FileUtils.FILE_SEPARATOR + FileUtils.FILE_CUSTOMER_CERT, caCertMd5, new HttpUtils.FileDownloadCallBack() {
													@Override
													public void onDownloading(long curFileSize, long fileSize) {

													}

													@Override
													public void requestSuccess(JSONObject responseJson) throws Exception {

													}

													@Override
													public void requestFail(int errorCode, String errorStr) {

													}
												});
											}
											if (download_suc) {
												Constants.IS_CUSTOMER_CA_CERT_DOWNLOAD_SUCCESS = true;
											}
										} else {
											Constants.IS_CUSTOMER_CA_CERT_DOWNLOAD_SUCCESS = true;
										}
									}else {
										com.bbpos.wiseapp.tms.utils.FileUtils.deleteFile(FileUtils.getWiseAppConfigPath() + FileUtils.FILE_SEPARATOR + FileUtils.FILE_CUSTOMER_CERT);
										Constants.IS_CUSTOMER_CA_CERT_DOWNLOAD_SUCCESS = true;
									}
                                }

                                if (getValueFromJsonObject(customJsonObject, "polling") != null) {
                                    JSONObject pollingJsonObject = new JSONObject(customJsonObject.getString("polling"));
//                            BBLog.i(BBLog.TAG, "[ProvisionService] ConfigInfo.data.customization.polling: " + pollingJsonObject.toString());
                                    SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_SERVER_URL,
                                            getValueFromJsonObject(pollingJsonObject, UsualData.SHARED_PREFERENCES_SERVER_URL));
                                    SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_HEAVY_SERVER_URL,
                                            getValueFromJsonObject(pollingJsonObject, UsualData.SHARED_PREFERENCES_HEAVY_SERVER_URL));
                                    SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_HEARTBEAT_TIME,
                                            getValueFromJsonObject(pollingJsonObject, UsualData.SHARED_PREFERENCES_HEARTBEAT_TIME));
                                    SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_WISELOG_TIME,
                                            getValueFromJsonObject(pollingJsonObject, UsualData.SHARED_PREFERENCES_WISELOG_TIME));
                                    SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_TERMINAL_INFO_TIME,
                                            getValueFromJsonObject(pollingJsonObject, UsualData.SHARED_PREFERENCES_TERMINAL_INFO_TIME));
                                    SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_UPLOAD_MODE,
                                            getValueFromJsonObject(pollingJsonObject, UsualData.SHARED_PREFERENCES_UPLOAD_MODE));
                                    Constants.UPLOAD_MODE = SharedPreferencesUtils.getSharePreferencesValue(UsualData.SHARED_PREFERENCES_UPLOAD_MODE, "1");
                                    BBLog.w(TAG, "set UploadMode：" + Constants.UPLOAD_MODE);

                                    SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_WEBSOCKET_SERVER_URL,
                                            getValueFromJsonObject(pollingJsonObject, UsualData.SHARED_PREFERENCES_WEBSOCKET_SERVER_URL));
                                    SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_WEBSOCKET_REMOTE_VIEW_SERVER_URL,
                                            getValueFromJsonObject(pollingJsonObject, UsualData.SHARED_PREFERENCES_WEBSOCKET_REMOTE_VIEW_SERVER_URL));
                                    if (getValueFromJsonObject(pollingJsonObject, UsualData.SHARED_PREFERENCES_WSSRECONN) != null) {
                                        JSONObject wssreconnJsonObject = pollingJsonObject.getJSONObject(UsualData.SHARED_PREFERENCES_WSSRECONN);
                                        SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_DELAY_SWITCH,
                                                getValueFromJsonObject(wssreconnJsonObject, UsualData.SHARED_PREFERENCES_DELAY_SWITCH));
                                        SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_DELAY_TIME,
                                                getValueFromJsonObject(wssreconnJsonObject, UsualData.SHARED_PREFERENCES_DELAY_TIME));
                                        SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_DELAY_POLICY,
                                                getValueFromJsonObject(wssreconnJsonObject, UsualData.SHARED_PREFERENCES_DELAY_POLICY));
                                    }
//                                String websocketUrl = getValueFromJsonObject(pollingJsonObject, UsualData.SHARED_PREFERENCES_WEBSOCKET_SERVER_URL);
//                                SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_WEBSOCKET_SERVER_URL, websocketUrl);
//                                if (!WebSocketManager.url.equals(websocketUrl)) {
//                                    WebSocketManager.url = websocketUrl;
//                                    this.stopService(WebSocketService.class);
//                                    ContextUtil.startWebsocketService(ContextUtil.getInstance(), WebSocketManager.m_public_key);
//                                }

                                    String otaUrl = getValueFromJsonObject(pollingJsonObject, UsualData.SHARED_PREFERENCES_OTA_SERVER_URL);
                                    BBLog.w(TAG, "OTA server url：" + otaUrl);
                                    SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_OTA_SERVER_URL, otaUrl);
                                    String tmtUrl = getValueFromJsonObject(pollingJsonObject, UsualData.SHARED_PREFERENCES_TMT_SERVER_URL);
                                    if (TextUtils.isEmpty(tmtUrl)) {
                                        tmtUrl = UsualData.WISEPOS_PULS_TMT_URL_DEFAULT;
                                    }
                                    BBLog.w(TAG, "TMT server url：" + tmtUrl);
                                    SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_TMT_SERVER_URL, tmtUrl);

                                    String wmapiUrl = getValueFromJsonObject(pollingJsonObject, UsualData.SHARED_PREFERENCES_WM_API_URL);
                                    BBLog.w(TAG, "wmapi Url：" + wmapiUrl);
                                    if (wmapiUrl == null) {
                                        Constants.S3_RESOURCE_URL = "";
                                    } else {
                                        Constants.S3_RESOURCE_URL = getValueFromJsonObject(pollingJsonObject, UsualData.SHARED_PREFERENCES_WM_API_URL);
                                        SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_WM_API_URL,
                                                getValueFromJsonObject(pollingJsonObject, UsualData.SHARED_PREFERENCES_WM_API_URL));
                                    }
                                }
                                if (getValueFromJsonObject(customJsonObject, "other") != null) {
                                    JSONObject otherJsonObject = new JSONObject(customJsonObject.getString("other"));
//                            BBLog.i(BBLog.TAG, "[ProvisionService] ConfigInfo.data.customization.other: " + otherJsonObject.toString());
                                    SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_LOCATION_METHOD,
                                            getValueFromJsonObject(otherJsonObject, UsualData.SHARED_PREFERENCES_LOCATION_METHOD));
                                }
                            }
                            CONFIG_PART++;
                            if (listener!=null) {
                                listener.onProgressUpdate(CONFIG_PART,CONFIG_TOTAL,"","");
                            }
                            BBLog.w(BBLog.TAG, "[over] cur= "+CONFIG_PART+", total= "+CONFIG_TOTAL);
//                      BBLog.i(BBLog.TAG, "[ProvisionService] ConfigInfo.data.requestTime: " + getValueFromJsonObject(dataJsonObject, "requestTime"));
//                        mHandler.sendEmptyMessage(CONFIG_INIT);
                            //移除OTA Key update, 在每一次Provisioning update, 只OTA firmware, configure. (不過, 第一次QR code scan後initiation, 必須要OTA key, firmware, configure 三部份)
                            //當切換CID時，做KeyInjection；
                            BBLog.i(TAG, "[ProvisionService] initialization done，ACTION_LAUNCHER_REFRESH");
                            BBLog.i(TAG, "[ProvisionService] initialization done，IS_CUSTOMER_APP_DOWNLOAD_SUCCESS: " + Constants.IS_CUSTOMER_APP_DOWNLOAD_SUCCESS);
                            BBLog.i(TAG, "[ProvisionService] initialization done，IS_WALLPAPER_DOWNLOAD_SUCCESS: " + Constants.IS_WALLPAPER_DOWNLOAD_SUCCESS);
                            BBLog.i(TAG, "[ProvisionService] initialization done，IS_LOGO_DOWNLOAD_SUCCESS: " + Constants.IS_LOGO_DOWNLOAD_SUCCESS);
                            BBLog.i(TAG, "[ProvisionService] initialization done，IS_CUSTOMER_BOOT_LOGO_DOWNLOAD_SUCCESS: " + Constants.IS_CUSTOMER_BOOT_LOGO_DOWNLOAD_SUCCESS);
                            BBLog.i(TAG, "[ProvisionService] initialization done，IS_CUSTOMER_BOOT_ANIM_DOWNLOAD_SUCCESS: " + Constants.IS_CUSTOMER_BOOT_ANIM_DOWNLOAD_SUCCESS);
                            BBLog.i(TAG, "[ProvisionService] initialization done，IS_CUSTOMER_CA_CERT_DOWNLOAD_SUCCESS: " + Constants.IS_CUSTOMER_CA_CERT_DOWNLOAD_SUCCESS);

                            if (Constants.IS_CUSTOMER_APP_DOWNLOAD_SUCCESS
                                    && Constants.IS_WALLPAPER_DOWNLOAD_SUCCESS
                                    && Constants.IS_LOGO_DOWNLOAD_SUCCESS
                                    && Constants.IS_CUSTOMER_BOOT_LOGO_DOWNLOAD_SUCCESS
                                    && Constants.IS_CUSTOMER_BOOT_ANIM_DOWNLOAD_SUCCESS
							        && Constants.IS_CUSTOMER_CA_CERT_DOWNLOAD_SUCCESS) {
                                BBLog.i(BBLog.TAG, "[ProvisionService] 初始化成功，重置IS_FIRST_PROVISIONING_COMPLETED = true");
                                Constants.IS_FIRST_PROVISIONING_COMPLETED = true;
                                //给launcher发送广播
                                Intent it = new Intent(UsualData.ACTION_LAUNCHER_REFRESH);
                                it.putExtra("config", response);
                                sendBroadcast(it);

                                Thread.sleep(500);
                                if (!TextUtils.isEmpty(cid)) {
                                    if (("BBZZ".equals(cid) || "BL".equals(cid)) && "1".equals(triggerType)) {
                                        mHandler.post(new Runnable() {
                                            @Override
                                            public void run() {
                                                final Dialog dialog = createQRCodeDialog();
                                                dialog.setOnDismissListener(new DialogInterface.OnDismissListener() {
                                                    @Override
                                                    public void onDismiss(DialogInterface dialog) {
                                                        try {
                                                            Thread.sleep(500);
                                                        } catch (InterruptedException e) {
                                                            e.printStackTrace();
                                                        }
                                                    }
                                                });
                                                dialog.findViewById(R.id.qrCodeImg).setOnClickListener(new View.OnClickListener() {
                                                    @Override
                                                    public void onClick(View v) {
                                                        dialog.dismiss();
                                                    }
                                                });
                                                dialog.getWindow().setType(WindowManager.LayoutParams.TYPE_SYSTEM_ALERT);//这句话是关键
                                                dialog.show();
                                            }
                                        });
                                    } else {
                                        Thread.sleep(500);
                                    }
                                } else {
                                    Thread.sleep(500);
                                }

                                if (!(!TextUtils.isEmpty(cid) && "SIZZ".equals(cid))) {
                                    //如果不是711客戶，Provisioning成功直接設置成6激活狀態
                                    if (TextUtils.isEmpty(SharedPreferencesUtils.getSharePreferencesValue(UsualData.SHARED_UNBOX_STATUS, ""))) {
                                        SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_UNBOX_STATUS, "6");
                                    }
                                }

//                                handleWebSocketURL();
                                break;
                            } else {
                                mHandler.postDelayed(new Runnable() {
                                    @Override
                                    public void run() {
                                        Toast.makeText(mContext, getString(R.string.provisioning_fail), Toast.LENGTH_LONG).show();
                                        if (listener!=null) {
                                            listener.onUpdateFail(getString(R.string.provisioning_fail));
                                        }
                                    }
                                }, 500);
//                                handleWebSocketURL();
                            }
                        } else {
                            BBLog.i(BBLog.TAG, "[ProvisionService] 客户相同，无需更新");
                            if (listener!=null) {
                                listener.onProgressUpdate(CONFIG_TOTAL,CONFIG_TOTAL,"","");
                                listener.onUpdateSuccess("");
                            }
                        }
                    }
                }
            }

            if (listener!=null)
                listener.onUpdateCompleted(true,getString(R.string.provisioning_success));

            if (Constants.IS_NEED_REBOOT) {
                BBLog.e(BBLog.TAG, "Phone & MMS block config changed, reboot...");
                SystemApi.reboot(getApplicationContext());
            }
        } catch (Exception e) {
            e.printStackTrace();
            if (listener!=null)
                listener.onUpdateCompleted(false,getString(R.string.provisioning_fail_retry));
        }

    }

    public static Dialog createQRCodeDialog() {
        if (TextUtils.isEmpty(Constants.BT_MAC)) {
            Constants.BT_MAC = WirelessUtil.getBluetoothMacAddress();
            if (!Constants.BT_STATUS) {
                BluetoothAdapter.getDefaultAdapter().disable();
            }
        }
        String bid = SharedPreferencesUtils.getSharePreferencesValue(UsualData.SHARED_PREFERENCES_DEVICE_FULL_INFO, "");
        String uid = "NULL";
        if (!TextUtils.isEmpty(bid)) {
            BBLog.e(BBLog.TAG, "WebSocketCenter: cache bid = " + bid);
            try {
                JSONObject infoData = new JSONObject(bid);
                if (infoData.has(ParameterName.bid)) {
                    bid = infoData.getString(ParameterName.bid);
                } else {
                    bid = "NULL";
                }

                if (infoData.has(ParameterName.uid)) {
                    uid = infoData.getString(ParameterName.uid);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        } else {
            bid = "NULL";
        }

        String QRcontent = "S/N:" + DeviceInfoApi.getIntance().getSerialNumber()
                + ",WIFI:" + WirelessUtil.getMac(ContextUtil.getInstance())
                + ",BT:" + Constants.BT_MAC
                + ",IMEI2:" + WirelessUtil.getIMEI(ContextUtil.getInstance());
        if (DeviceInfoApi.getIntance().isWisePosPro()) {
            QRcontent += ",MODEL:" + SystemUtils.get7MDModel(ContextUtil.getInstance());
        }
        QRcontent += ",bID:" + bid;
        QRcontent += ",uID:" + uid;
        BBLog.w(TAG, "QRcontent = " + QRcontent);
        WindowManager windowManager = (WindowManager) ContextUtil.getInstance().getSystemService(Context.WINDOW_SERVICE);
        Dialog dialog = new Dialog(ContextUtil.getInstance(), R.style.dialog_style);
        dialog.setContentView(R.layout.qr_code);
        dialog.setTitle("");

        DisplayMetrics displaymetrics = new DisplayMetrics();
        windowManager.getDefaultDisplay().getMetrics(displaymetrics);

        ImageView imageQR = null;
        int QRCodeWidth = displaymetrics.widthPixels / 5 * 3;
        int QRCodeHeight = displaymetrics.widthPixels / 5 * 3;
        Map<EncodeHintType, Object> hints = new EnumMap<EncodeHintType, Object>(EncodeHintType.class);
        hints.put(EncodeHintType.CHARACTER_SET, "UTF-8");

        MultiFormatWriter writer = new MultiFormatWriter();
        try {
            // 容錯率姑且可以將它想像成解析度，分為 4 級：L(7%)，M(15%)，Q(25%)，H(30%)
            // 設定 QR code 容錯率為 H
            hints.put(EncodeHintType.ERROR_CORRECTION, ErrorCorrectionLevel.H);
            hints.put(EncodeHintType.MARGIN, 0);
            // 建立 QR code 的資料矩陣
            BitMatrix result = writer.encode(QRcontent, BarcodeFormat.QR_CODE, QRCodeWidth, QRCodeHeight, hints);
            // ZXing 還可以生成其他形式條碼，如：BarcodeFormat.CODE_39、BarcodeFormat.CODE_93、BarcodeFormat.CODE_128、BarcodeFormat.EAN_8、BarcodeFormat.EAN_13...

            //建立點陣圖
            Bitmap bitmap = Bitmap.createBitmap(QRCodeWidth, QRCodeHeight, Bitmap.Config.ARGB_8888);
            // 將 QR code 資料矩陣繪製到點陣圖上
            for (int y = 0; y < QRCodeHeight; y++) {
                for (int x = 0; x < QRCodeWidth; x++) {
                    bitmap.setPixel(x, y, result.get(x, y) ? Color.BLACK : Color.WHITE);
                }
            }

            ImageView imgView = (ImageView) dialog.findViewById(R.id.qrCodeImg);
            // 設定為 QR code 影像
            imgView.setImageBitmap(bitmap);
        } catch (Exception e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }
        return  dialog;
    }

    private void handleWebSocketURL() {
        String newWebsocketUrl = SharedPreferencesUtils.getSharePreferencesValue(UsualData.SHARED_PREFERENCES_WEBSOCKET_SERVER_URL, "");
        BBLog.e(TAG, "newWebsocketUrl: " + newWebsocketUrl);
        BBLog.e(TAG, "WebSocketManager.url: " + WebSocketManager.url);
        if (!TextUtils.isEmpty(newWebsocketUrl) && !WebSocketManager.url.equalsIgnoreCase(newWebsocketUrl)) {
            if (TextUtils.isEmpty(WebSocketManager.url)) {
                WebSocketManager.url = newWebsocketUrl;
                if (WebSocketManager.m_need_rsa) {
                    ContextUtil.startWebsocketService(ContextUtil.getInstance(), WebSocketManager.m_public_key);
                } else {
                    ContextUtil.startWebsocketService(ContextUtil.getInstance());
                }
            } else {
                BBLog.e(TAG, "websocket url changed，start reboot...");
                SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_LAST_WEBSOCKET_SERVER_URL, newWebsocketUrl);
                SystemApi.reboot(getApplicationContext());
            }
        }
    }

    private String getValueFromJsonObject(JSONObject jsonObject, String key) {
        try {
            return (jsonObject != null && jsonObject.has(key)) ? jsonObject.getString(key) : null;
        } catch (Exception e) {
            e.printStackTrace();
        }

        return null;
    }
}
