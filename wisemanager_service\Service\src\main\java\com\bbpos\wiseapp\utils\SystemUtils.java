package com.bbpos.wiseapp.utils;

import android.content.Context;
import android.text.format.Time;
import android.util.Log;

import com.bbpos.wiseapp.logger.BBLog;
import com.bbpos.wiseapp.tms.utils.Constants;

import java.io.BufferedReader;
import java.io.FileReader;
import java.io.IOException;
import java.lang.reflect.Constructor;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.Locale;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class SystemUtils {
    public static String getCPURateDesc_All(){
        String path = "/proc/stat";// 系统CPU信息文件
        long totalJiffies[]=new long[2];
        long totalIdle[]=new long[2];
        int firstCPUNum=0;//设置这个参数，这要是防止两次读取文件获知的CPU数量不同，导致不能计算。这里统一以第一次的CPU数量为基准
        FileReader fileReader = null;
        BufferedReader bufferedReader = null;
        Pattern pattern=Pattern.compile(" [0-9]+");
        for(int i=0;i<2;i++) {
            totalJiffies[i]=0;
            totalIdle[i]=0;
            try {
                fileReader = new FileReader(path);
                bufferedReader = new BufferedReader(fileReader, 8192);
//                BBLog.w(BBLog.TAG, "getCPURateDesc_All bufferedReader = " + bufferedReader.readLine());
                int currentCPUNum=0;
                String str;
                while ((str = bufferedReader.readLine()) != null && (i==0||currentCPUNum<firstCPUNum)) {
//                    BBLog.w(BBLog.TAG, "getCPURateDesc_All str = " + str);
                    if (str.toLowerCase().startsWith("cpu")) {
                        currentCPUNum++;
                        int index = 0;
                        Matcher matcher = pattern.matcher(str);
                        while (matcher.find()) {
                            try {
                                long tempJiffies = Long.parseLong(matcher.group(0).trim());
                                totalJiffies[i] += tempJiffies;
                                if (index == 3) {//空闲时间为该行第4条栏目
                                    totalIdle[i] += tempJiffies;
                                }
                                index++;
                            } catch (Exception e) {
                                e.printStackTrace();
                            }
                        }
                    }
                    if(i==0){
                        firstCPUNum=currentCPUNum;
                        try {//暂停50毫秒，等待系统更新信息。
                            Thread.sleep(50);
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }
                }
            } catch (Exception e) {
                // TODO Auto-generated catch block
                e.printStackTrace();
            } finally {
                if (bufferedReader != null) {
                    try {
                        bufferedReader.close();
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
            }
        }
        double rate=-1;
        if (totalJiffies[0]>0&&totalJiffies[1]>0&&totalJiffies[0]!=totalJiffies[1]){
            rate=100.0*((totalJiffies[1]-totalIdle[1])-(totalJiffies[0]-totalIdle[0]))/(totalJiffies[1]-totalJiffies[0]);
        }

        BBLog.e(BBLog.TAG,"CpuUtils cpu_rate:"+rate);
        return String.format(Locale.US, "%.2f", rate);
    }

    public static String get7MDModel(Context context) {
        String model = "";
        String status = "";
        final String PSK_STATUS = "/sys/class/switch/psk_pin_status_switch/state";
        Class classCustServiceManagerNative = null;
        try {
            classCustServiceManagerNative = Class.forName("android.app.CustServiceManager");
            Constructor constructor = classCustServiceManagerNative.getDeclaredConstructor(Context.class);
            Object object = constructor.newInstance(context);
            Method readSysFileStatus = classCustServiceManagerNative.getDeclaredMethod("readSysFileStatus", String.class);
            status = (String) readSysFileStatus.invoke(object, PSK_STATUS);
        } catch (Exception e) {
            e.printStackTrace();
        }

        // "1" indicates Pistol Grip inserted, "0" indicates Pistol Grip NOT inserted
        if (Constants.MSR_TYPE== Constants.E_MSR_TYPE.MAXIM_MSR && "0".equals(status)) {
            model = "WSP72";
        } else if (Constants.MSR_TYPE== Constants.E_MSR_TYPE.MAXIM_MSR && "1".equals(status)) {
            model = "WSP73";
        } else if (Constants.MSR_TYPE== Constants.E_MSR_TYPE.MEGAHUNT_MSR && "0".equals(status)) {
            model = "WSP75";
        } else if (Constants.MSR_TYPE== Constants.E_MSR_TYPE.MEGAHUNT_MSR && "1".equals(status)) {
            model = "WSP76";
        } else {
            model = "unknown";
        }
        BBLog.e(BBLog.TAG, "7MD 手柄类型：" + model);
        return model;
    }

    /**
     * 判断当前系统时间是否在特定时间的段内
     *
     * @param beginHour 开始的小时，例如5
     * @param beginMin 开始小时的分钟数，例如00
     * @param endHour 结束小时，例如 8
     * @param endMin 结束小时的分钟数，例如00
     * @return true表示在范围内，否则false
     */
    public static boolean isCurrentInTimeScope(int beginHour, int beginMin, int endHour, int endMin) {
        boolean result = false;// 结果
        final long aDayInMillis = 1000 * 60 * 60 * 24;// 一天的全部毫秒数
        final long currentTimeMillis = System.currentTimeMillis();// 当前时间

        Time now = new Time();// 注意这里导入的时候选择android.text.format.Time类,而不是java.sql.Time类
        now.set(currentTimeMillis);

        Time startTime = new Time();
        startTime.set(currentTimeMillis);
        startTime.hour = beginHour;
        startTime.minute = beginMin;

        Time endTime = new Time();
        endTime.set(currentTimeMillis);
        endTime.hour = endHour;
        endTime.minute = endMin;

        if (!startTime.before(endTime)) {
            // 跨天的特殊情况（比如22:00-8:00）
            startTime.set(startTime.toMillis(true) - aDayInMillis);
            result = !now.before(startTime) && !now.after(endTime); // startTime <= now <= endTime
            Time startTimeInThisDay = new Time();
            startTimeInThisDay.set(startTime.toMillis(true) + aDayInMillis);
            if (!now.before(startTimeInThisDay)) {
                result = true;
            }
        } else {
            // 普通情况(比如 8:00 - 14:00)
            result = !now.before(startTime) && !now.after(endTime); // startTime <= now <= endTime
        }
        return result;
    }
}