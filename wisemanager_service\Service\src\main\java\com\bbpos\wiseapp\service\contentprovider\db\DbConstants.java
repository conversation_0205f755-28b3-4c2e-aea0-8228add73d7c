package com.bbpos.wiseapp.service.contentprovider.db;

import android.content.Context;

import com.bbpos.wiseapp.tms.utils.ContextUtil;

public class DbConstants {
	public static String DB_NAME="commom_data.db";
	public static String DB_TABLE="commom_data";
	public static String server_url="server_url";	
	public static String projectid="projectid";
	/**
	 * 更新server_url
	 * @param context
	 */
	
	public static void updateServerUrlData(Context context ,String url) {
//		BBLog.i(BBLog.TAG, "server_url:"+url);
		CommonDataOperation commonDataOperation= new CommonDataOperation(context);
		commonDataOperation.open(ContextUtil.getInstance().getApplicationContext());
		commonDataOperation.insertData(server_url, url);
		commonDataOperation.close();
	}
	
	public static void updateCommonData(Context context ,String valuekey,String value) {
//		BBLog.i(BBLog.TAG, "server_url:"+url);
		CommonDataOperation commonDataOperation= new CommonDataOperation(context);
		commonDataOperation.open(ContextUtil.getInstance().getApplicationContext());
		commonDataOperation.insertData(valuekey, value);
		commonDataOperation.close();
	}


}
