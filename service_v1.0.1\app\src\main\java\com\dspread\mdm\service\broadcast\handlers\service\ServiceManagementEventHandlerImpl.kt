package com.dspread.mdm.service.broadcast.handlers.service

import android.content.Context
import android.content.Intent
import com.dspread.mdm.service.broadcast.core.BroadcastActions
import com.dspread.mdm.service.broadcast.core.BroadcastEventHandler
import com.dspread.mdm.service.platform.manager.ServiceStartupManager
import com.dspread.mdm.service.utils.log.Logger

/**
 * 服务管理事件处理器实现
 * 统一处理所有与服务启动、重启、保活相关的事件
 * 
 * 功能包括：
 * - 开机启动服务
 * - 服务重启
 * - 用户解锁后服务检查
 * - 屏幕点亮后服务检查
 * - 网络变化后服务检查
 * - 定时服务检查
 */
class ServiceManagementEventHandlerImpl : BroadcastEventHandler {
    
    private val TAG = "ServiceManagementEventHandler"
    
    override fun getHandlerName(): String = TAG
    
    override fun getSupportedActions(): List<String> {
        return listOf(
            // 用户交互相关的服务检查
            Intent.ACTION_USER_PRESENT,
            Intent.ACTION_TIME_TICK,

            // 服务管理相关
            BroadcastActions.ACTION_SERVICE_RESTART,

            // 其他触发服务检查的事件
            "android.net.conn.CONNECTIVITY_CHANGE"
        )
    }
    
    override fun handleBroadcast(context: Context, intent: Intent): Boolean {
        val action = intent.action ?: return false
        
        return try {
            when (action) {
                Intent.ACTION_USER_PRESENT -> {
                    handleUserPresent(context)
                    true
                }
                Intent.ACTION_TIME_TICK -> {
                    handleTimeTick(context)
                    true
                }
                BroadcastActions.ACTION_SERVICE_RESTART -> {
                    handleServiceRestart(context)
                    true
                }
                "android.net.conn.CONNECTIVITY_CHANGE" -> {
                    handleNetworkChange(context)
                    true
                }
                else -> false
            }
        } catch (e: Exception) {
            Logger.receiverE("$TAG 处理广播失败: $action", e)
            false
        }
    }
    

    
    /**
     * 处理用户解锁（可能是从强制停止后解锁）
     */
    private fun handleUserPresent(context: Context) {
        Logger.receiver("$TAG 处理用户解锁事件")
        
        try {
            // 检查并启动服务（如果需要）
            ServiceStartupManager.checkAndStartService(
                context,
                ServiceStartupManager.StartupReason.USER_PRESENT
            )
            
            Logger.receiver("$TAG 用户解锁后服务检查完成")
            
        } catch (e: Exception) {
            Logger.receiverE("$TAG 用户解锁后服务检查失败", e)
        }
    }
    
    /**
     * 处理时间变化（每分钟触发一次，用于定时检查服务状态）
     */
    private fun handleTimeTick(context: Context) {
        try {
            // 静默检查服务状态，避免过多日志
            ServiceStartupManager.checkAndStartService(
                context,
                ServiceStartupManager.StartupReason.TIME_TICK,
                silent = true
            )
            
        } catch (e: Exception) {
            Logger.receiverE("$TAG 定时服务检查失败", e)
        }
    }
    
    /**
     * 处理服务重启请求
     */
    private fun handleServiceRestart(context: Context) {
        Logger.receiver("$TAG 处理服务重启请求")
        
        try {
            // 强制重启服务
            ServiceStartupManager.startService(
                context,
                ServiceStartupManager.StartupReason.SERVICE_RESTART,
                forceStart = true
            )
            
            Logger.receiver("$TAG 服务重启请求已提交")
            
        } catch (e: Exception) {
            Logger.receiverE("$TAG 服务重启失败", e)
        }
    }
    
    /**
     * 处理网络连接变化（可能触发服务检查）
     */
    private fun handleNetworkChange(context: Context) {
        Logger.receiver("$TAG 处理网络变化事件")
        
        try {
            // 网络变化时检查服务状态
            ServiceStartupManager.checkAndStartService(
                context,
                ServiceStartupManager.StartupReason.NETWORK_CHANGE
            )
            
            Logger.receiver("$TAG 网络变化后服务检查完成")
            
        } catch (e: Exception) {
            Logger.receiverE("$TAG 网络变化后服务检查失败", e)
        }
    }
}
