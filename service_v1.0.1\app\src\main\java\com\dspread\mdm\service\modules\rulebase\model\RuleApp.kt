package com.dspread.mdm.service.modules.rulebase.model

import com.dspread.mdm.service.utils.log.Logger
import org.json.JSONObject

/**
 * 规则关联的应用数据模型
 */
data class RuleApp(
    val packName: String,           // 包名
    val apkName: String,           // 应用名称（注意：虽然叫apkName，但存储的是应用名称）
    val versionName: String,        // 版本名称
    val versionCode: String,        // 版本号
    val apkUrl: String,            // APK下载地址
    val apkMd5: String,            // APK文件MD5
    val apkSize: Long = 0L,        // APK文件大小
    val installBy: String = "0",   // 安装方式 0=静默安装 1=用户确认
    val isSystemApp: Boolean = false, // 是否为系统应用
    val priority: Int = 0          // 安装优先级
) {
    
    /**
     * 验证应用数据完整性
     * @param requireDownload 是否需要下载相关字段（安装应用需要，卸载应用不需要）
     */
    fun validate(requireDownload: Boolean = true): ValidationResult {
        return when {
            packName.isBlank() -> ValidationResult.error("包名不能为空")
            apkName.isBlank() -> ValidationResult.error("应用名称不能为空")
            versionName.isBlank() -> ValidationResult.error("版本名称不能为空")
            versionCode.isBlank() -> ValidationResult.error("版本号不能为空")
            !isValidPackageName(packName) -> ValidationResult.error("包名格式无效: $packName")

            // 只有安装应用才需要下载相关字段
            requireDownload && apkUrl.isBlank() -> ValidationResult.error("APK下载地址不能为空")
            requireDownload && apkMd5.isBlank() -> ValidationResult.error("APK MD5不能为空")
            requireDownload && !isValidUrl(apkUrl) -> ValidationResult.error("APK下载地址格式无效: $apkUrl")
            requireDownload && !isValidMd5(apkMd5) -> ValidationResult.error("MD5格式无效: $apkMd5")

            else -> ValidationResult.success()
        }
    }
    
    /**
     * 转换为JSON格式
     */
    fun toJson(): JSONObject {
        return JSONObject().apply {
            put("packName", packName)
            put("apkName", apkName)
            put("versionName", versionName)
            put("versionCode", versionCode)
            put("apkUrl", apkUrl)
            put("apkMd5", apkMd5)
            put("apkSize", apkSize)
            put("installBy", installBy)
            put("isSystemApp", isSystemApp)
            put("priority", priority)
        }
    }
    
    /**
     * 获取应用唯一标识
     */
    fun getUniqueId(): String {
        return "${packName}_${versionName}_${versionCode}"
    }
    
    /**
     * 检查是否与另一个应用相同
     */
    fun isSameApp(other: RuleApp): Boolean {
        return packName == other.packName && 
               versionName == other.versionName && 
               versionCode == other.versionCode
    }
    
    /**
     * 检查版本是否比另一个应用新
     */
    fun isNewerThan(other: RuleApp): Boolean {
        if (packName != other.packName) return false
        
        return try {
            val thisVersionCode = versionCode.toIntOrNull() ?: 0
            val otherVersionCode = other.versionCode.toIntOrNull() ?: 0
            thisVersionCode > otherVersionCode
        } catch (e: Exception) {
            // 如果版本号不是数字，则比较版本名称
            versionName.compareTo(other.versionName) > 0
        }
    }
    
    companion object {
        /**
         * 从JSON创建RuleApp对象
         */
        fun fromJson(json: JSONObject): RuleApp? {
            return try {
                val packName = json.getString("packName")
                val apkName = json.optString("apkName", "")

                // 调试日志
                Logger.rule("RuleApp解析: packName=$packName, apkName=$apkName")

                RuleApp(
                    packName = packName,
                    apkName = if (apkName.isNotEmpty()) apkName else packName, // 只有apkName为空时才使用packName
                    versionName = json.getString("versionName"),
                    versionCode = json.getString("versionCode"),
                    apkUrl = json.optString("url", json.optString("apkUrl", "")), // 兼容两种字段名
                    apkMd5 = json.optString("apkMd5", ""),
                    apkSize = json.optLong("apkSize", 0L),
                    installBy = json.optString("installBy", "0"),
                    isSystemApp = json.optBoolean("isSystemApp", false),
                    priority = json.optInt("priority", 0)
                )
            } catch (e: Exception) {
                // 添加调试日志
                Logger.ruleE("RuleApp解析失败: ${e.message}, JSON: $json", e)
                null
            }
        }
        
        /**
         * 从JSON数组解析应用列表
         */
        fun parseAppList(jsonArray: org.json.JSONArray?): List<RuleApp> {
            if (jsonArray == null) return emptyList()
            
            val apps = mutableListOf<RuleApp>()
            for (i in 0 until jsonArray.length()) {
                try {
                    val appJson = jsonArray.getJSONObject(i)
                    fromJson(appJson)?.let { app ->
                        apps.add(app)
                    }
                } catch (e: Exception) {
                    // 忽略解析失败的应用，继续处理其他应用
                    continue
                }
            }
            return apps
        }
    }
    
    // 私有验证方法
    private fun isValidPackageName(packageName: String): Boolean {
        // 简单的包名格式验证
        return packageName.matches(Regex("^[a-zA-Z][a-zA-Z0-9_]*(?:\\.[a-zA-Z][a-zA-Z0-9_]*)*$"))
    }
    
    private fun isValidUrl(url: String): Boolean {
        return url.startsWith("http://") || url.startsWith("https://")
    }
    
    private fun isValidMd5(md5: String): Boolean {
        return md5.matches(Regex("^[a-fA-F0-9]{32}$"))
    }
}

/**
 * 验证结果类
 */
sealed class ValidationResult(val isSuccess: Boolean, val message: String) {
    class Success(message: String = "验证成功") : ValidationResult(true, message)
    class Error(message: String) : ValidationResult(false, message)
    
    companion object {
        fun success(message: String = "验证成功") = Success(message)
        fun error(message: String) = Error(message)
    }
}
