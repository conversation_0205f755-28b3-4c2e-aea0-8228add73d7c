###### 应用卸载后，应用重启

2025-08-20 17:49:21.922   648-2054  B<PERSON>erQueueDebug        surfaceflinger                       E  [ActivityRecord{c7a9de2 u0 com.dspread.mdm.service/.ui.activity.TestActivity#153](this:0xa9b46c40,id:-1,api:0,p:-1,c:-1) id info cannot be read from 'ActivityRecord{c7a9de2 u0 com.dspread.mdm.service/.ui.activity.TestActivity#153'
2025-08-20 17:49:22.103  4878-4878  ziparchive              com.dspread.mdm.service              W  Unable to open '/data/app/~~vomGg8kiFoE9-XFEv9rIHg==/com.dspread.mdm.service-mXfgcKUyk95e0Wn0Dv6R_g==/base.dm': No such file or directory
2025-08-20 17:49:22.511  4878-4878  nativeloader            com.dspread.mdm.service              D  Configuring clns-4 for other apk /data/app/~~vomGg8kiFoE9-XFEv9rIHg==/com.dspread.mdm.service-mXfgcKUyk95e0Wn0Dv6R_g==/base.apk. target_sdk_version=34, uses_libraries=, library_path=/data/app/~~vomGg8kiFoE9-XFEv9rIHg==/com.dspread.mdm.service-mXfgcKUyk95e0Wn0Dv6R_g==/lib/arm, permitted_path=/data:/mnt/expand:/data/user/0/com.dspread.mdm.service
2025-08-20 17:49:22.538  4878-4878  GraphicsEnvironment     com.dspread.mdm.service              V  Currently set values for:
2025-08-20 17:49:22.538  4878-4878  GraphicsEnvironment     com.dspread.mdm.service              V    angle_gl_driver_selection_pkgs=[]
2025-08-20 17:49:22.538  4878-4878  GraphicsEnvironment     com.dspread.mdm.service              V    angle_gl_driver_selection_values=[]
2025-08-20 17:49:22.540  4878-4878  GraphicsEnvironment     com.dspread.mdm.service              V  ANGLE GameManagerService for com.dspread.mdm.service: false
2025-08-20 17:49:22.540  4878-4878  GraphicsEnvironment     com.dspread.mdm.service              V  com.dspread.mdm.service is not listed in per-application setting
2025-08-20 17:49:22.540  4878-4878  GraphicsEnvironment     com.dspread.mdm.service              V  Neither updatable production driver nor prerelease driver is supported.
2025-08-20 17:49:22.570  4878-4878  Compatibil...geReporter com.dspread.mdm.service              D  Compat change id reported: 183155436; UID 1000; state: ENABLED
2025-08-20 17:49:22.606  4878-4878  MbrainDebugManagerImpl  com.dspread.mdm.service              D  getService failed
2025-08-20 17:49:22.675  4878-4878  Common                  com.dspread.mdm.service              D  🔧 日志配置：生产环境模式
2025-08-20 17:49:22.686  4878-4878  Common                  com.dspread.mdm.service              D  🔧 [DebugConfig] 调试模式已禁用
2025-08-20 17:49:22.691  4878-4878  Common                  com.dspread.mdm.service              D  🔧 SmartMdmServiceApp: 统一配置管理器初始化完成
2025-08-20 17:49:22.698  4878-4878  Common                  com.dspread.mdm.service              D  🔧 SmartMdmServiceApp: 系统目录初始化完成 - /data/pos/config/
2025-08-20 17:49:22.746  4878-4878  Receiver                com.dspread.mdm.service              D  🔧 ServiceGuardEventHandler 启动服务守护定时器: 120s
2025-08-20 17:49:22.754  4878-4878  Compatibil...geReporter com.dspread.mdm.service              D  Compat change id reported: 160794467; UID 1000; state: ENABLED
2025-08-20 17:49:22.759  4878-4878  Common                  com.dspread.mdm.service              I  ✅ ⏰ 设置服务守护定时器成功，下次执行: 120秒后 (2分钟)
2025-08-20 17:49:22.765  4878-4878  Common                  com.dspread.mdm.service              D  🔧 SmartMdmServiceApp: 服务守护定时器启动成功
2025-08-20 17:49:22.775  4878-4878  Common                  com.dspread.mdm.service              D  🔧 ServiceStartupManager 收到服务启动请求: APPLICATION_STARTUP, 强制启动: false
2025-08-20 17:49:22.785  4878-4878  Common                  com.dspread.mdm.service              D  🔧 ServiceStartupManager 开始执行服务启动: APPLICATION_STARTUP
2025-08-20 17:49:22.792  4878-4878  Common                  com.dspread.mdm.service              D  🔧 ServiceManager: 准备启动后台服务
2025-08-20 17:49:22.798  4878-4878  ContextImpl             com.dspread.mdm.service              W  Calling a method in the system process without a qualified user: android.app.ContextImpl.startService:1899 android.content.ContextWrapper.startService:825 com.dspread.mdm.service.platform.manager.ServiceManager.startBackgroundService:20 com.dspread.mdm.service.platform.manager.ServiceStartupManager.performServiceStart:121 com.dspread.mdm.service.platform.manager.ServiceStartupManager.startService:66 
2025-08-20 17:49:22.803  4878-4878  Common                  com.dspread.mdm.service              D  🔧 ServiceManager: 后台服务启动成功，ComponentName: ComponentInfo{com.dspread.mdm.service/com.dspread.mdm.service.services.SmartMdmBackgroundService}
2025-08-20 17:49:23.813  4878-4878  Common                  com.dspread.mdm.service              D  🔧 ServiceStartupManager 服务启动验证成功: APPLICATION_STARTUP
2025-08-20 17:49:23.819  4878-4878  Common                  com.dspread.mdm.service              D  🔧 ServiceStartupManager 服务启动成功: APPLICATION_STARTUP
2025-08-20 17:49:23.825  4878-4878  Common                  com.dspread.mdm.service              D  🔧 SmartMdmServiceApp: 主服务启动请求已提交
2025-08-20 17:49:23.830  4878-4878  Common                  com.dspread.mdm.service              D  🔧 SmartMdmServiceApp: Application启动完成，包名: com.dspread.mdm.service
2025-08-20 17:49:23.844  4878-4878  M-ProMotion             com.dspread.mdm.service              I  M-ProMotion is disabled
2025-08-20 17:49:23.912  4878-4878  Compatibil...geReporter com.dspread.mdm.service              D  Compat change id reported: 210923482; UID 1000; state: ENABLED
2025-08-20 17:49:23.947  4878-4878  Compatibil...geReporter com.dspread.mdm.service              D  Compat change id reported: 171228096; UID 1000; state: ENABLED
2025-08-20 17:49:24.004  4878-4878  getAnimDeal             com.dspread.mdm.service              I  mode:3 packageName:com.dspread.mdm.service result: false
2025-08-20 17:49:24.012  4878-4878  SurfaceFactory          com.dspread.mdm.service              I  [static] sSurfaceFactory = com.mediatek.view.impl.SurfaceFactoryImpl@8d07d32
2025-08-20 17:49:24.012  4878-4905  PowerHalWrapper         com.dspread.mdm.service              I  PowerHalWrapper.getInstance 
2025-08-20 17:49:24.017  4878-4878  Compatibil...geReporter com.dspread.mdm.service              D  Compat change id reported: 237531167; UID 1000; state: DISABLED
2025-08-20 17:49:24.022  4878-4878  OpenGLRenderer          com.dspread.mdm.service              W  Unknown dataspace 0
2025-08-20 17:49:24.027  4878-4878  VRI[TestActivity]       com.dspread.mdm.service              D  hardware acceleration = true, forceHwAccelerated = false
2025-08-20 17:49:24.032   648-1117  BufferQueueDebug        surfaceflinger                       E  [38f08b7 com.dspread.mdm.service/com.dspread.mdm.service.ui.activity.TestActivity#154](this:0xa9b42c40,id:-1,api:0,p:-1,c:-1) id info cannot be read from '38f08b7 com.dspread.mdm.service/com.dspread.mdm.service.ui.activity.TestActivity#154'
2025-08-20 17:49:24.040  4878-4878  InputTransport          com.dspread.mdm.service              D  Create ARC handle: 0xaa7436d0
2025-08-20 17:49:24.040  4878-4878  InputEventReceiver      com.dspread.mdm.service              D  Input log is disabled in InputEventReceiver.
2025-08-20 17:49:24.040  4878-4878  InputTransport          com.dspread.mdm.service              D  Input log is disabled in InputChannel.
2025-08-20 17:49:24.048  4878-4878  Choreographer           com.dspread.mdm.service              I  Skipped 85 frames!  The application may be doing too much work on its main thread.
2025-08-20 17:49:24.090   648-1117  BufferQueueDebug        surfaceflinger                       E  [com.dspread.mdm.service/com.dspread.mdm.service.ui.activity.TestActivity#155](this:0xa9b3dc40,id:-1,api:0,p:-1,c:-1) id info cannot be read from 'com.dspread.mdm.service/com.dspread.mdm.service.ui.activity.TestActivity#155'
2025-08-20 17:49:24.103  4878-4878  BufferQueueConsumer     com.dspread.mdm.service              D  [](id:130e00000000,api:0,p:-1,c:4878) connect: controlledByApp=false
2025-08-20 17:49:24.138  4878-4906  OpenGLRenderer          com.dspread.mdm.service              E  Unable to match the desired swap behavior.
2025-08-20 17:49:24.146  4878-4910  ion                     com.dspread.mdm.service              E  ioctl c0044901 failed with code -1: Invalid argument
2025-08-20 17:49:24.255  4878-4906  BLASTBufferQueue        com.dspread.mdm.service              D  [VRI[TestActivity]#0](f:0,a:1) acquireNextBufferLocked size=480x854 mFrameNumber=1 applyTransaction=true mTimestamp=9002697493764(auto) mPendingTransactions.size=0 graphicBufferId=20950850469893 transform=3
2025-08-20 17:49:24.260  4878-4906  GED                     com.dspread.mdm.service              I  ged_boost_gpu_freq, level 100, eOrigin 2, final_idx 2, oppidx_max 2, oppidx_min 0
2025-08-20 17:49:24.266  4878-4894  OpenGLRenderer          com.dspread.mdm.service              I  Davey! duration=1626ms; Flags=1, FrameTimelineVsyncId=29735, IntendedVsync=9001070163687, Vsync=9002486830382, InputEventId=0, HandleInputStart=9002493339995, AnimationStart=9002493363380, PerformTraversalsStart=9002493935611, DrawStart=9002582771072, FrameDeadline=9001086163687, FrameInterval=9002491976611, FrameStartTime=16666667, SyncQueued=9002596326687, SyncStart=9002598961841, IssueDrawCommandsStart=9002599393226, SwapBuffers=9002696155303, FrameCompleted=9002699556226, DequeueBufferDuration=0, QueueBufferDuration=1131615, GpuCompleted=9002699556226, SwapBuffersCompleted=9002699072534, DisplayPresentTime=0, CommandSubmissionCompleted=9002696155303, 
2025-08-20 17:49:24.273  4878-4878  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 服务启动
2025-08-20 17:49:24.301  4878-4878  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 开始服务初始化
2025-08-20 17:49:24.307  4878-4878  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] Android 14+: 初始化DSPREAD服务
2025-08-20 17:49:24.314  4878-4878  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 服务创建完成
2025-08-20 17:49:24.314  4878-4916  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 后台线程：初始化DSPREAD服务
2025-08-20 17:49:24.321  4878-4878  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] onStartCommand调用
2025-08-20 17:49:24.322  4878-4916  Platform                com.dspread.mdm.service              D  🔧 DspreadService 开始初始化DSPREAD服务
2025-08-20 17:49:24.329  4878-4916  ContextImpl             com.dspread.mdm.service              W  Calling a method in the system process without a qualified user: android.app.ContextImpl.bindService:1988 android.content.ContextWrapper.bindService:861 com.dspread.mdm.service.services.DspreadService.bindSysService:111 com.dspread.mdm.service.services.DspreadService.initialize:63 com.dspread.mdm.service.services.SmartMdmBackgroundService.initializeDspreadServiceAsync$lambda$1:227 
2025-08-20 17:49:24.333  4878-4878  Platform                com.dspread.mdm.service              D  🔧 WakeLockManager 获取智能WakeLock成功，持续时间: 60000ms
2025-08-20 17:49:24.335  4878-4916  Platform                com.dspread.mdm.service              D  🔧 DspreadService 系统服务绑定请求已发送
2025-08-20 17:49:24.343  4878-4878  Platform                com.dspread.mdm.service              D  🔧 WakeLockManager 设置WakeLock续期定时器: 60000ms
2025-08-20 17:49:24.372  4878-4906  GED                     com.dspread.mdm.service              I  ged_boost_gpu_freq, level 100, eOrigin 2, final_idx 2, oppidx_max 2, oppidx_min 0
2025-08-20 17:49:24.377  4878-4878  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 初始化基础组件
2025-08-20 17:49:24.385  4878-4878  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 开始初始化...
2025-08-20 17:49:24.398  4878-4878  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 包更新广播接收器注册完成
2025-08-20 17:49:24.404  4878-4878  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 统一广播接收器注册完成
2025-08-20 17:49:24.410  4878-4878  Common                  com.dspread.mdm.service              I  ✅ BroadcastManager 初始化完成
2025-08-20 17:49:24.464  4878-4917  Platform                com.dspread.mdm.service              D  🔧 DeviceInfoApi 单例实例已创建
2025-08-20 17:49:24.470  4878-4878  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 服务初始化完成
2025-08-20 17:49:24.477  4878-4878  Platform                com.dspread.mdm.service              D  🔧 DspreadService 系统服务连接成功: ComponentInfo{com.dspread.sdkservice/com.dspread.sdkservice.SDKSysService}
2025-08-20 17:49:24.484  4878-4878  Platform                com.dspread.mdm.service              D  🔧 DspreadService 系统服务绑定完成
2025-08-20 17:49:24.491  4878-4916  ContextImpl             com.dspread.mdm.service              W  Calling a method in the system process without a qualified user: android.app.ContextImpl.bindService:1988 android.content.ContextWrapper.bindService:861 com.dspread.mdm.service.services.DspreadService.bindDevService:166 com.dspread.mdm.service.services.DspreadService.initialize:66 com.dspread.mdm.service.services.SmartMdmBackgroundService.initializeDspreadServiceAsync$lambda$1:227 
2025-08-20 17:49:24.494  4878-4906  BLASTBufferQueue        com.dspread.mdm.service              D  [VRI[TestActivity]#0](f:0,a:1) destructor()
2025-08-20 17:49:24.494  4878-4906  BufferQueueConsumer     com.dspread.mdm.service              D  [VRI[TestActivity]#0(BLAST Consumer)0](id:130e00000000,api:0,p:-1,c:4878) disconnect
2025-08-20 17:49:24.495  4878-4906  GED                     com.dspread.mdm.service              I  ged_boost_gpu_freq, level 100, eOrigin 2, final_idx 2, oppidx_max 2, oppidx_min 0
2025-08-20 17:49:24.498  4878-4916  Platform                com.dspread.mdm.service              D  🔧 DspreadService 设备服务绑定请求已发送
2025-08-20 17:49:24.508  4878-4878  Receiver                com.dspread.mdm.service              D  🔧 电池状态变化: 电量=95%, 温度=28°C, 充电=true
2025-08-20 17:49:24.539  4878-4878  Provisioning            com.dspread.mdm.service              I  ℹ️ ProvisioningManager初始化完成
2025-08-20 17:49:24.555  4878-4878  Provisioning            com.dspread.mdm.service              D  🔧 状态标志加载成功: /data/pos/config/provisioning_flags.json
2025-08-20 17:49:24.577  4878-4878  WebSocket               com.dspread.mdm.service              I  🔧 C0902 被动式上送: battery_level_change_5 (被动: 1)
2025-08-20 17:49:24.594  4878-4878  Platform                com.dspread.mdm.service              E  ❌ DspreadService 设备服务不可用，无法获取序列号
2025-08-20 17:49:24.618  4878-4878  WebSocket               com.dspread.mdm.service              I  🔧 消息已缓存: C0902 (缓存数量: 1)
2025-08-20 17:49:24.624  4878-4878  WebSocket               com.dspread.mdm.service              I  🔧 C0902 电池状态上送成功 (1, trigger=battery_level_change_5)
2025-08-20 17:49:24.633  4878-4878  Platform                com.dspread.mdm.service              D  🔧 NetworkApi 单例实例已创建
2025-08-20 17:49:24.645  4878-4878  Receiver                com.dspread.mdm.service              D  🔧 网络状态变化: 连接=true, 类型=WIFI
2025-08-20 17:49:24.653  4878-4878  Receiver                com.dspread.mdm.service              D  🔧 网络恢复，尝试重新连接WebSocket
2025-08-20 17:49:24.659  4878-4878  Receiver                com.dspread.mdm.service              D  🔧 ServiceManagementEventHandler 处理网络变化事件
2025-08-20 17:49:24.669  4878-4878  Common                  com.dspread.mdm.service              D  🔧 ServiceStartupManager 服务正在运行，无需启动: NETWORK_CHANGE
2025-08-20 17:49:24.675  4878-4878  Receiver                com.dspread.mdm.service              D  🔧 ServiceManagementEventHandler 网络变化后服务检查完成
2025-08-20 17:49:24.681  4878-4878  Provisioning            com.dspread.mdm.service              D  🔧 ProvisioningEventHandler 网络状态变化
2025-08-20 17:49:24.687  4878-4878  Provisioning            com.dspread.mdm.service              D  🔧 ProvisioningEventHandler 忽略初始启动时的网络连接事件
2025-08-20 17:49:24.694  4878-4878  Platform                com.dspread.mdm.service              D  🔧 DspreadService 设备服务连接成功: ComponentInfo{com.dspread.sdkservice/com.dspread.sdkservice.SDKDevService}
2025-08-20 17:49:24.701  4878-4878  Platform                com.dspread.mdm.service              D  🔧 DspreadService 设备服务绑定完成
2025-08-20 17:49:24.784  4878-4878  WebSocket               com.dspread.mdm.service              I  🔧 C0905 主动式上送: manual_trigger (主动: 1)
2025-08-20 17:49:24.810  4878-4878  WebSocket               com.dspread.mdm.service              I  🔧 首次上送，上送当日数据
2025-08-20 17:49:24.816  4878-4878  WebSocket               com.dspread.mdm.service              I  🔧 没有流量数据需要上送
2025-08-20 17:49:24.822  4878-4878  Common                  com.dspread.mdm.service              D  🔧 刷新时自动上送流量统计
2025-08-20 17:49:24.835  4878-4906  GED                     com.dspread.mdm.service              I  ged_boost_gpu_freq, level 100, eOrigin 2, final_idx 2, oppidx_max 2, oppidx_min 0
2025-08-20 17:49:24.853  4878-4917  Platform                com.dspread.mdm.service              D  🔧 SpVersionApi 解析SP版本: V1.0.5
2025-08-20 17:49:24.868  4878-4917  Platform                com.dspread.mdm.service              D  🔧 DspreadService 首次获取SP版本成功: V1.0.5 (原始: *SP_VERSION:V1.0.5#*COMMIT:2d7cc90#*DATETIME:20250...)
2025-08-20 17:49:24.934  4878-4878  Common                  com.dspread.mdm.service              D  🔧 TestActivity UI已更新: SP=V1.0.5, SN=01354090202503050399
2025-08-20 17:49:25.708  4878-4916  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 等待DSPREAD服务就绪 (1/10): true
2025-08-20 17:49:25.714  4878-4916  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] DSPREAD服务就绪，启动Provisioning
2025-08-20 17:49:25.719  4878-4916  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 验证DSPREAD服务功能
2025-08-20 17:49:25.724  4878-4916  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 设备序列号验证: 成功(01354090202503050399)
2025-08-20 17:49:25.730  4878-4916  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 服务状态 - 系统服务:✅ 设备服务:✅
2025-08-20 17:49:25.736  4878-4924  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 启动ProvisioningService
2025-08-20 17:49:25.744  4878-4924  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] ProvisioningService启动完成
2025-08-20 17:49:25.744  4878-4917  Provisioning            com.dspread.mdm.service              I  ℹ️ 开始执行Provisioning配置，触发类型: FIRST_BOOT
2025-08-20 17:49:25.751  4878-4917  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] Provisioning进度: 正在下载配置文件...
2025-08-20 17:49:25.756  4878-4917  Provisioning            com.dspread.mdm.service              I  ℹ️ 开始请求远程配置...
2025-08-20 17:49:25.763  4878-4917  Provisioning            com.dspread.mdm.service              I  ℹ️ 请求配置: https://config.dspreadserv.net/status/config?SN=MDEzNTQwOTAyMDI1MDMwNTAzOTk=&timestamp=1755683365763
2025-08-20 17:49:25.901  4878-4917  TrafficStats            com.dspread.mdm.service              D  tagSocket(109) with statsTag=0xffffffff, statsUid=-1
2025-08-20 17:49:28.469  4878-4917  Provisioning            com.dspread.mdm.service              I  ℹ️ 远程配置获取成功
2025-08-20 17:49:28.474  4878-4917  Provisioning            com.dspread.mdm.service              I  ℹ️ 远程配置请求成功
2025-08-20 17:49:28.481  4878-4917  Provisioning            com.dspread.mdm.service              I  ℹ️ 远程配置解析成功 - CID: 1001
2025-08-20 17:49:28.510  4878-4917  Provisioning            com.dspread.mdm.service              I  ℹ️ API响应已保存到本地: /data/pos/config/provisioning_config.json, 具体内容为:
                                                                                                    {"mode":"dev","data":{"requestTime":"1755683368038","customization":{"system":{"timezone":"Asia\/Hong_Kong","logo":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/test\/logo.bin","logoMd5":"ba1ee533924eae5c408465e7cddcbda4","bootAnimationMd5":"ba1ee533924eae5c408465e7cddcbda4","gps":{"minUpdateTime":"30","scheduleTime":"60","maxLocateTime":"0","minDistance":"10","valid_distance":"500","care":"1"},"powerSaveMode":{"enable":"1","screenTimeout":"0"},"bootAnimation":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/test\/bootanimation.zip"},"polling":{"heartbeatTime":"300","terminalInfoTime":"900","statusApiUrl":"wss:\/\/api.dspreadserv.net\/status\/websocket\/register","remoteUrl":"wss:\/\/remote.dspreadserv.net\/remoteWSS\/websockify","uploadMode":"1","wssreconn":{"pingInterval":"30","delayPolicy":"1","delaySwitch":"1","delayTime":"60"}}},"client":"default","cid":"1001"},"function":"getProvisioningConfig","description":"SUCCESS","stateCode":"0","version":"v1.0.1.20250801"}
2025-08-20 17:49:28.517  4878-4917  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] Provisioning进度: 正在下载 Logo...
2025-08-20 17:49:28.592  4878-4917  Provisioning            com.dspread.mdm.service              I  ℹ️ 文件已存在且MD5匹配，跳过下载: Logo
2025-08-20 17:49:28.598  4878-4917  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] Provisioning进度: 正在下载 BootAnimation...
2025-08-20 17:49:28.670  4878-4917  Provisioning            com.dspread.mdm.service              I  ℹ️ 文件已存在且MD5匹配，跳过下载: BootAnimation
2025-08-20 17:49:28.675  4878-4917  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] Provisioning进度: 正在应用配置...
2025-08-20 17:49:28.681  4878-4917  Provisioning            com.dspread.mdm.service              I  ℹ️ 配置应用完成
2025-08-20 17:49:28.686  4878-4917  Provisioning            com.dspread.mdm.service              I  ℹ️ 远程配置成功，标记首次配置完成
2025-08-20 17:49:28.694  4878-4917  Provisioning            com.dspread.mdm.service              D  🔧 状态标志保存成功: /data/pos/config/provisioning_flags.json
2025-08-20 17:49:28.699  4878-4917  Provisioning            com.dspread.mdm.service              I  ℹ️ Provisioning配置执行完成
2025-08-20 17:49:28.705  4878-4917  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] Provisioning完成，启动核心组件
2025-08-20 17:49:28.723  4878-4878  WebSocket               com.dspread.mdm.service              I  🔧 开始初始化 WebSocket 组件...
2025-08-20 17:49:28.729  4878-4878  WebSocket               com.dspread.mdm.service              I  🔧 初始化 WebSocket 密钥管理器
2025-08-20 17:49:28.734  4878-4878  WebSocket               com.dspread.mdm.service              I  🔧 生成客户端公私钥对
2025-08-20 17:49:28.826  4878-4878  Common                  com.dspread.mdm.service              I  ✅ WebSocket 密钥管理器初始化完成
2025-08-20 17:49:28.835  4878-4878  WebSocket               com.dspread.mdm.service              I  🔧 开始初始化 WsMessageCenter...
2025-08-20 17:49:28.841  4878-4878  WebSocket               com.dspread.mdm.service              I  🔧 WsMessageCenter 首次初始化
2025-08-20 17:49:28.860  4878-4878  WebSocket               com.dspread.mdm.service              I  🔧 WsTaskManager 初始化完成
2025-08-20 17:49:28.967  4878-4878  WebSocket               com.dspread.mdm.service              I  🔧 WsMessageCenter 初始化完成
2025-08-20 17:49:28.972  4878-4878  WebSocket               com.dspread.mdm.service              I  🔧 开始清理终态任务，当前任务数量: 0
2025-08-20 17:49:28.978  4878-4878  WebSocket               com.dspread.mdm.service              I  🔧 没有需要清理的终态任务
2025-08-20 17:49:28.983  4878-4878  WebSocket               com.dspread.mdm.service              I  🔧 开始重置任务状态，当前任务个数: 0
2025-08-20 17:49:28.988  4878-4878  WebSocket               com.dspread.mdm.service              I  🔧 没有需要重置的任务
2025-08-20 17:49:28.994  4878-4878  Common                  com.dspread.mdm.service              I  ✅ 任务状态恢复完成
2025-08-20 17:49:29.010  4878-4878  WebSocket               com.dspread.mdm.service              I  🔧 使用Provisioning配置的WebSocket URL: wss://api.dspreadserv.net/status/websocket/register
2025-08-20 17:49:29.016  4878-4878  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接管理器初始化完成
2025-08-20 17:49:29.021  4878-4878  Common                  com.dspread.mdm.service              I  ✅ WebSocket 管理器初始化完成
2025-08-20 17:49:29.027  4878-4878  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 管理器初始化完成
2025-08-20 17:49:29.033  4878-4878  Common                  com.dspread.mdm.service              I  ✅ WebSocket 组件初始化完成
2025-08-20 17:49:29.039  4878-4878  WebSocket               com.dspread.mdm.service              I  🔧 启动 WebSocket 连接...
2025-08-20 17:49:29.044  4878-4878  WebSocket               com.dspread.mdm.service              I  🔧 开始连接 WebSocket...
2025-08-20 17:49:29.064  4878-4878  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接到: wss://api.dspreadserv.net/status/websocket/register?serialNo=MDEzNTQwOTAyMDI1MDMwNTAzOTk=&publicKey=TUlHZk1BMEdDU3FHU0liM0RRRUJBUVVBQTRHTkFEQ0JpUUtCZ1FEVmYvNzlNVUowTlhCQVhEdmpNREV5Qm1qVTFPUmkrM1VRWXcwNlVXZE5QLzBaNllYRWpJVzBMYTZHeTAxV2RKN0JEMTNoU3dVWlZrSERWRkRHaHY2b2ZRTGtjUVM5MFhqRWJ3N1kvTXF0ZnVXUDBrOFBldEZwM3FPcytFU0x2dTgwN1dHQUlKKzIzZ0E0WHZjeVFsN0l5SllTTVRnT29TcEZNb0dWNTZ2L1N3SURBUUFC&query=1&msgVer=3&timestamp=1755683369049&signature=d6wrPYwNDCvDoxzCy9mLbn8hc5kr516uV+8+NsIZFrig8089ClMH5CTDf3O5+9FFqD7XqYKy3MQ2lV0SW5FLm7m5HUGjLWzMopw34qUHjwqv7zRRvEDNS+SR7apzUN7SYul6PD5eqt4IgTYUiw662XBmx/rMnDrikV4IS537MAM=
2025-08-20 17:49:29.074  4878-4878  WebSocket               com.dspread.mdm.service              I  🔧 未检测到系统代理设置
2025-08-20 17:49:29.115  4878-4878  WebSocket               com.dspread.mdm.service              I  🔧 ===== WebSocket 库默认配置 =====
2025-08-20 17:49:29.121  4878-4878  WebSocket               com.dspread.mdm.service              I  🔧 默认 PING 间隔: 30000ms (30秒)
2025-08-20 17:49:29.127  4878-4878  WebSocket               com.dspread.mdm.service              I  🔧 默认 PONG 间隔: 0ms (0秒)
2025-08-20 17:49:29.132  4878-4878  WebSocket               com.dspread.mdm.service              I  🔧 帧队列大小: 5
2025-08-20 17:49:29.137  4878-4878  WebSocket               com.dspread.mdm.service              I  🔧 连接超时: 15000ms (15秒)
2025-08-20 17:49:29.143  4878-4878  WebSocket               com.dspread.mdm.service              I  🔧 允许缺失关闭帧: false
2025-08-20 17:49:29.148  4878-4878  WebSocket               com.dspread.mdm.service              I  🔧 ================================
2025-08-20 17:49:29.155  4878-4878  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接请求已发送
2025-08-20 17:49:29.161  4878-4878  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接启动完成
2025-08-20 17:49:29.167  4878-4878  Common                  com.dspread.mdm.service              D  🔧 [ModuleManagerRegistry] 模块管理器注册中心初始化完成
2025-08-20 17:49:29.175  4878-4878  Provisioning            com.dspread.mdm.service              D  🔧 ProvisioningEventHandler 启动Provisioning定时器: 43200s (12小时)
2025-08-20 17:49:29.175  4878-4917  Common                  com.dspread.mdm.service              D  🔧 [ModuleManagerRegistry] 启动所有模块
2025-08-20 17:49:29.177  4878-4938  TrafficStats            com.dspread.mdm.service              D  tagSocket(114) with statsTag=0xffffffff, statsUid=-1
2025-08-20 17:49:29.185  4878-4878  Common                  com.dspread.mdm.service              I  ✅ ⏰ 设置初始化定时器成功，下次执行: 43200秒后 (12小时)
2025-08-20 17:49:29.189  4878-4917  Common                  com.dspread.mdm.service              D  🔧 Module LogStream Manager status changed to: INITIALIZED
2025-08-20 17:49:29.198  4878-4917  Common                  com.dspread.mdm.service              D  🔧 Module LogStream Manager status changed to: STARTING
2025-08-20 17:49:29.205  4878-4878  Common                  com.dspread.mdm.service              I  ✅ ⏰ 设置心跳定时器成功，下次执行: 300秒后 (5分钟)
2025-08-20 17:49:29.216  4878-4878  Common                  com.dspread.mdm.service              I  ✅ ⏰ 设置任务执行定时器成功，下次执行: 60秒后 (1分钟)
2025-08-20 17:49:29.235  4878-4878  Common                  com.dspread.mdm.service              I  ✅ ⏰ 设置终端信息定时器成功，下次执行: 900秒后 (15分钟)
2025-08-20 17:49:29.261  4878-4878  Common                  com.dspread.mdm.service              D  🔧 TimerConfig ========== 当前定时器配置 (生产模式) ==========
2025-08-20 17:49:29.267  4878-4878  Common                  com.dspread.mdm.service              D  🔧 TimerConfig ①心跳定时器: 300秒
2025-08-20 17:49:29.273  4878-4878  Common                  com.dspread.mdm.service              D  🔧 TimerConfig ②终端信息上传定时器: 900秒
2025-08-20 17:49:29.280  4878-4878  Common                  com.dspread.mdm.service              D  🔧 TimerConfig ③任务执行定时器: 60秒
2025-08-20 17:49:29.285  4878-4878  Common                  com.dspread.mdm.service              D  🔧 TimerConfig ④服务守护定时器: 120秒
2025-08-20 17:49:29.290  4878-4917  Common                  com.dspread.mdm.service              D  🔧 Module LogStream Manager status changed to: RUNNING
2025-08-20 17:49:29.291  4878-4878  Common                  com.dspread.mdm.service              D  🔧 TimerConfig ⑤初始化定时器: 43200秒
2025-08-20 17:49:29.297  4878-4878  Common                  com.dspread.mdm.service              D  🔧 TimerConfig ===============================================
2025-08-20 17:49:29.301  4878-4917  Common                  com.dspread.mdm.service              D  🔧 [ModuleManagerRegistry] LogStreamManager启动成功
2025-08-20 17:49:29.303  4878-4878  Common                  com.dspread.mdm.service              I  ✅ [SmartMdmBackgroundService] 所有定时器启动完成
2025-08-20 17:49:29.309  4878-4878  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 核心组件初始化完成
2025-08-20 17:49:29.327  4878-4917  Compatibil...geReporter com.dspread.mdm.service              D  Compat change id reported: 263076149; UID 1000; state: ENABLED
2025-08-20 17:49:29.338  4878-4917  Common                  com.dspread.mdm.service              D  🔧 Module Geofence Manager status changed to: INITIALIZED
2025-08-20 17:49:29.345  4878-4917  Common                  com.dspread.mdm.service              D  🔧 Module Geofence Manager status changed to: STARTING
2025-08-20 17:49:29.371  4878-4917  Platform                com.dspread.mdm.service              D  🔧 用户交互监控已启动（简化版本）
2025-08-20 17:49:29.374  4878-4878  Platform                com.dspread.mdm.service              D  🔧 [WiFiManagerApi] WiFiManagerApi 单例实例已创建
2025-08-20 17:49:29.377  4878-4917  Platform                com.dspread.mdm.service              D  🔧 使用模拟用户交互检测
2025-08-20 17:49:29.383  4878-4917  Platform                com.dspread.mdm.service              D  🔧 用户交互监控已启动
2025-08-20 17:49:29.389  4878-4917  Service                 com.dspread.mdm.service              D  🔧 [GeofenceManager]Constants.geofenceStatus = 1
2025-08-20 17:49:29.395  4878-4917  Service                 com.dspread.mdm.service              D  🔧 [GeofenceManager]-- Constants.storeId =  -- Constants.storeSsid =  -- Constants.storeIp = 
2025-08-20 17:49:29.402  4878-4917  Common                  com.dspread.mdm.service              D  🔧 Module Geofence Manager status changed to: RUNNING
2025-08-20 17:49:29.409  4878-4917  Common                  com.dspread.mdm.service              D  🔧 [ModuleManagerRegistry] GeofenceManager启动成功
2025-08-20 17:49:29.426  4878-4917  RuleBase                com.dspread.mdm.service              D  🔧 RuleBaseManager 开始初始化RuleBaseManager...
2025-08-20 17:49:29.427  4878-4939  TrafficStats            com.dspread.mdm.service              D  tagSocket(123) with statsTag=0xffffffff, statsUid=-1
2025-08-20 17:49:29.437  4878-4917  RuleBase                com.dspread.mdm.service              D  🔧 RuleBaseStorage RuleBase存储管理器初始化完成
2025-08-20 17:49:29.443  4878-4917  RuleBase                com.dspread.mdm.service              D  🔧 RuleStateMonitor 状态监控器初始化完成
2025-08-20 17:49:29.448  4878-4917  RuleBase                com.dspread.mdm.service              D  🔧 RuleBaseManager RuleBaseManager初始化完成
2025-08-20 17:49:29.454  4878-4917  Common                  com.dspread.mdm.service              D  🔧 [ModuleManagerRegistry] RuleBaseManager启动成功
2025-08-20 17:49:29.460  4878-4917  Common                  com.dspread.mdm.service              D  🔧 [ModuleManagerRegistry] 所有模块启动完成
2025-08-20 17:49:29.520  4878-4949  ProfileInstaller        com.dspread.mdm.service              D  Installing profile for com.dspread.mdm.service
2025-08-20 17:49:31.688  4878-4950  Common                  com.dspread.mdm.service              I  ✅ WebSocket 物理连接成功，等待服务器 hellMsg
2025-08-20 17:49:31.697  4878-4950  WebSocket               com.dspread.mdm.service              I  🔧 ===== 心跳发送器实际配置 =====
2025-08-20 17:49:31.705  4878-4950  WebSocket               com.dspread.mdm.service              I  🔧 实际 PING 发送间隔: 30000ms (30秒)
2025-08-20 17:49:31.713  4878-4950  WebSocket               com.dspread.mdm.service              I  🔧 PING 定时器名称: PingSender
2025-08-20 17:49:31.720  4878-4950  WebSocket               com.dspread.mdm.service              I  🔧 实际 PONG 发送间隔: 0ms (0秒)
2025-08-20 17:49:31.727  4878-4950  WebSocket               com.dspread.mdm.service              I  🔧 PONG 定时器名称: PongSender
2025-08-20 17:49:31.733  4878-4950  WebSocket               com.dspread.mdm.service              I  🔧 ===============================
2025-08-20 17:49:31.739  4878-4950  WebSocket               com.dspread.mdm.service              I  🔧 启动PING/PONG检测，阈值: 连续3个PING无PONG响应
2025-08-20 17:49:31.779  4878-4950  Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"hellMsg":"hello 01354090202503050399","tranCode":"S0000","version":"1","rebootTime":"01:34:58","serialNo":"01354090202503050399","deviceStatus":"6"}
2025-08-20 17:49:31.786  4878-4950  Common                  com.dspread.mdm.service              I  ✅ 收到服务器 hellMsg，WebSocket 业务连接成功！
2025-08-20 17:49:31.792  4878-4950  Common                  com.dspread.mdm.service              I  ✅ WebSocket 业务连接成功！
2025-08-20 17:49:31.797  4878-4950  WebSocket               com.dspread.mdm.service              I  🔧 业务连接成功，重连计数已重置为 0
2025-08-20 17:49:31.803  4878-4950  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接成功，检查自身更新任务状态
2025-08-20 17:49:31.811  4878-4950  Receiver                com.dspread.mdm.service              D  🔧 发送广播: com.dspread.mdm.service.CHECK_SELF_UPDATE_STATUS
2025-08-20 17:49:31.817  4878-4950  WebSocket               com.dspread.mdm.service              I  🔧 开始发送缓存消息，数量: 1
2025-08-20 17:49:31.826  4878-4950  WebSocket               com.dspread.mdm.service              I  🔧 C0902 主动式上送: cached_message_resend (主动: 2)
2025-08-20 17:49:31.862  4878-4950  Common                  com.dspread.mdm.service              I  ✅ 缓存消息发送成功: C0902
2025-08-20 17:49:32.373  4878-4950  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接成功
2025-08-20 17:49:32.381  4878-4950  WebSocket               com.dspread.mdm.service              I  🔧 设备状态: 6
2025-08-20 17:49:32.391  4878-4950  WebSocket               com.dspread.mdm.service              I  🔧 检查首次连接标志: isFirstTimeWebSocketConnected=true
2025-08-20 17:49:32.402  4878-4950  WebSocket               com.dspread.mdm.service              I  🔧 首次WebSocket连接成功，发送初始化信息并执行初始化检查
2025-08-20 17:49:32.412  4878-4950  WebSocket               com.dspread.mdm.service              I  🔧 开始发送初始化信息（带流量控制）
2025-08-20 17:49:32.422  4878-4950  WebSocket               com.dspread.mdm.service              I  🔧 C0901 被动式上送: first_connection (被动: 2)
2025-08-20 17:49:32.445  4878-4950  Platform                com.dspread.mdm.service              D  🔧 重新收集应用信息
2025-08-20 17:49:32.449  4878-4889  System                  com.dspread.mdm.service              W  A resource failed to call close. 
2025-08-20 17:49:32.551  4878-4950  AppManager              com.dspread.mdm.service              I  ℹ️ 应用信息获取完成: 总数131(系统129/用户2) 返回2个
2025-08-20 17:49:32.569  4878-4950  Platform                com.dspread.mdm.service              D  🔧 应用信息: 2 个应用
2025-08-20 17:49:32.601  4878-4950  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0901","serialNo":"01354090202503050399","request_time":"1755683372575","request_id":"1755683372575C0901","version":"1","data":{"apkInfo":[{"packName":"com.dspread.mdm.service","apkName":"service","versionCode":22,"versionName":"1.1.03.20250820.DSPREAD.MDM.SERVICE","updateDate":"2025-08-20 17:49:20"},{"packName":"mark.via","apkName":"Via","versionCode":20250117,"versionName":"6.2.0","updateDate":"2025-08-18 17:50:20"}],"sytemInfo":{"androidVersion":"14","buildNumber":"BASE_D30M-MU_GEN_AP_V1.0.8.1_202507032000_ota_test","aspl":"2025-03-05"},"serviceInfo":[]},"myVersionName":"1.1.03.20250820.DSPREAD.MDM.SERVICE","terminalDate":"20250820174932"}
2025-08-20 17:49:32.607  4878-4950  WebSocket               com.dspread.mdm.service              I  🔧 C0901 应用信息上送成功 (1)
2025-08-20 17:49:33.613  4878-4950  WebSocket               com.dspread.mdm.service              I  🔧 C0902 被动式上送: first_connection (被动: 3)
2025-08-20 17:49:33.651  4878-4950  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0902","serialNo":"01354090202503050399","request_time":"1755683373634","request_id":"1755683373634C0902","version":"1","data":{"batteryLife":95,"batteryHealth":2,"temprature":"28.3","isCharging":"1","isLowBattery":"0","powerOff":"0","outOfBattery":"0"},"myVersionName":"1.1.03.20250820.DSPREAD.MDM.SERVICE","terminalDate":"20250820174933"}
2025-08-20 17:49:33.657  4878-4950  WebSocket               com.dspread.mdm.service              I  🔧 C0902 电池状态上送成功 (1, trigger=first_connection)
2025-08-20 17:49:34.663  4878-4950  WebSocket               com.dspread.mdm.service              I  🔧 C0903 被动式上送: first_connection (被动: 4)
2025-08-20 17:49:34.822  4878-4950  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0903","serialNo":"01354090202503050399","request_time":"1755683374785","request_id":"1755683374785C0903","version":"1","data":{"hardwareInfo":[{"name":"CPU Usage","desc":"cpu:0.50"},{"name":"storage_usage_status","desc":" 4.60GB \/ 32.00GB ","state":"1"},{"name":"total_memory","desc":" 0.67GB \/ 1.86GB ","state":"1"},{"name":"androidVersion","desc":"14","state":"1"},{"name":"network_status","desc":"wifi","state":"1"},{"name":"OSVer","desc":"14","state":"1"}],"locationInfo":{"gps":{"longitude":"-999.0","latitude":"-999.0","satelliteCount":0,"satelliteCountInUsed":0,"satelliteAverSNR":"0.00","distance":"0.0","lockStatus":"1","lockMeter":"200","proId":"","proName":""},"baseSite":{"MCC":"","MNC":"","CID":"-1","LAC":"-1","DBM":"-999","IMSI":"","ICCID":""},"wifi":{"SSID":"fubox_2.4G","IP":"**************","MAC":"BE:87:C2:42:DF:75","SSTH":"-34","BSSID":"7c:10:c9:d6:9f:10"},"address":"无法获取"}},"myVersionName":"1.1.03.20250820.DSPREAD.MDM.SERVICE","terminalDate":"20250820174934"}
2025-08-20 17:49:34.828  4878-4950  WebSocket               com.dspread.mdm.service              I  🔧 C0903 数据信息上送成功 (1)
2025-08-20 17:49:35.834  4878-4950  WebSocket               com.dspread.mdm.service              I  🔧 C0904 被动式上送: first_connection (被动: 5)
2025-08-20 17:49:35.939  4878-4950  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0904","serialNo":"01354090202503050399","request_time":"1755683375904","request_id":"1755683375904C0904","version":"1","data":{"wifiOption":[{"SSID":"2306","SSTH":"-72"},{"SSID":"2205","SSTH":"-50"},{"SSID":"2205_5G","SSTH":"-67"},{"SSID":"DIRECT-7D-HP Laser 136w","SSTH":"-77"},{"SSID":"fubox_2.4G","SSTH":"-32"},{"SSID":"2106","SSTH":"-66"},{"SSID":"DIRECT-CC-HP Laser 1188w","SSTH":"-85"},{"SSID":"2206","SSTH":"-30"},{"SSID":"2206-5G","SSTH":"-37"},{"SSID":"DIRECT-C6-Mi All-in-One Inkjet","SSTH":"-50"},{"SSID":"2207-5G","SSTH":"-78"},{"SSID":"2103_5G","SSTH":"-82"},{"SSID":"@Ruijie-1816_5G","SSTH":"-69"},{"SSID":"@Ruijie-1816","SSTH":"-50"},{"SSID":"jibao2","SSTH":"-72"}],"baseSite":{"MCC":"","MNC":"","CID":"-1","LAC":"-1","DBM":"-999","IMSI":"","ICCID":""},"wifi":{"SSID":"fubox_2.4G","IP":"**************","MAC":"BE:87:C2:42:DF:75","SSTH":"-34","BSSID":"7c:10:c9:d6:9f:10"}},"myVersionName":"1.1.03.20250820.DSPREAD.MDM.SERVICE","terminalDate":"20250820174935"}
2025-08-20 17:49:35.945  4878-4950  WebSocket               com.dspread.mdm.service              I  🔧 C0904 网络状态上送成功 (1)
2025-08-20 17:49:36.952  4878-4950  WebSocket               com.dspread.mdm.service              I  🔧 C0906 被动式上送: first_connection (被动: 6)
2025-08-20 17:49:36.967  4878-4950  Platform                com.dspread.mdm.service              D  🔧 DspreadService 使用缓存的SP版本: V1.0.5
2025-08-20 17:49:36.973  4878-4950  Platform                com.dspread.mdm.service              D  🔧 SpVersionApi 通过DspreadService获取SP版本: V1.0.5
2025-08-20 17:49:37.003  4878-4950  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0906","serialNo":"01354090202503050399","request_time":"1755683376985","request_id":"1755683376985C0906","version":"1","data":{"firmWareInfo":{"spfw":"V1.0.5"},"imei_1":"864177060143618","imei_2":"","wifi_mac":"be:87:c2:42:df:75","bt_mac":"","bsn":""},"myVersionName":"1.1.03.20250820.DSPREAD.MDM.SERVICE","terminalDate":"20250820174936"}
2025-08-20 17:49:37.010  4878-4950  WebSocket               com.dspread.mdm.service              I  🔧 C0906 设备信息上送成功 (1)
2025-08-20 17:49:37.017  4878-4950  WebSocket               com.dspread.mdm.service              I  🔧 C0109 被动式上送: first_connection (被动: 7)
2025-08-20 17:49:37.033  4878-4950  Platform                com.dspread.mdm.service              D  🔧 重新收集应用信息（强制刷新）
2025-08-20 17:49:37.121  4878-4950  AppManager              com.dspread.mdm.service              I  ℹ️ 应用信息获取完成: 总数131(系统129/用户2) 返回2个
2025-08-20 17:49:37.136  4878-4950  Platform                com.dspread.mdm.service              D  🔧 应用信息: 2 个应用
2025-08-20 17:49:37.298  4878-4950  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0109","serialNo":"01354090202503050399","request_time":"1755683377232","request_id":"1755683377232C0109","version":"1","data":{"apkInfo":[{"packName":"com.dspread.mdm.service","apkName":"service","versionCode":22,"versionName":"1.1.03.20250820.DSPREAD.MDM.SERVICE","updateDate":"2025-08-20 17:49:20"},{"packName":"mark.via","apkName":"Via","versionCode":20250117,"versionName":"6.2.0","updateDate":"2025-08-18 17:50:20"}],"locationInfo":{"gps":{"longitude":"-999.0","latitude":"-999.0","satelliteCount":0,"satelliteCountInUsed":0,"satelliteAverSNR":"0.00","distance":"0.0","lockStatus":"1","lockMeter":"200","proId":"","proName":""},"baseSite":{"MCC":"","MNC":"","CID":"-1","LAC":"-1","DBM":"-999","IMSI":"","ICCID":""},"wifi":{"SSID":"fubox_2.4G","IP":"**************","MAC":"BE:87:C2:42:DF:75","SSTH":"-34","BSSID":"7c:10:c9:d6:9f:10"},"address":"无法获取"},"hardwareInfo":[{"name":"CPU Usage","desc":"cpu:0.50"},{"name":"storage_usage_status","desc":" 4.60GB \/ 32.00GB ","state":"1"},{"name":"total_memory","desc":" 0.67GB \/ 1.86GB ","state":"1"},{"name":"androidVersion","desc":"14","state":"1"},{"name":"network_status","desc":"wifi","state":"1"},{"name":"OSVer","desc":"14","state":"1"}],"wifiOption":[{"SSID":"2306","SSTH":"-72"},{"SSID":"2205","SSTH":"-50"},{"SSID":"2205_5G","SSTH":"-67"},{"SSID":"DIRECT-7D-HP Laser 136w","SSTH":"-77"},{"SSID":"fubox_2.4G","SSTH":"-32"},{"SSID":"2106","SSTH":"-66"},{"SSID":"DIRECT-CC-HP Laser 1188w","SSTH":"-85"},{"SSID":"2206","SSTH":"-30"},{"SSID":"2206-5G","SSTH":"-37"},{"SSID":"DIRECT-C6-Mi All-in-One Inkjet","SSTH":"-50"},{"SSID":"2207-5G","SSTH":"-78"},{"SSID":"2103_5G","SSTH":"-82"},{"SSID":"@Ruijie-1816_5G","SSTH":"-69"},{"SSID":"@Ruijie-1816","SSTH":"-50"},{"SSID":"jibao2","SSTH":"-72"}],"sytemInfo":{"androidVersion":"14","buildNumber":"BASE_D30M-MU_GEN_AP_V1.0.8.1_202507032000_ota_test","aspl":"2025-03-05"}},"myVersionName":"1.1.03.20250820.DSPREAD.MDM.SERVICE","terminalDate":"20250820174937"}
2025-08-20 17:49:37.304  4878-4950  WebSocket               com.dspread.mdm.service              I  🔧 C0109 终端信息上传完成 (1, trigger=first_connection)
2025-08-20 17:49:37.309  4878-4950  Common                  com.dspread.mdm.service              I  ✅ 初始化信息发送完成
2025-08-20 17:49:37.316  4878-4950  WebSocket               com.dspread.mdm.service              I  🔧 检查服务更新结果
2025-08-20 17:49:37.321  4878-4950  Task                    com.dspread.mdm.service              W  ⚠️ 任务列表为空，跳过服务更新结果检查
2025-08-20 17:49:37.327  4878-4950  WebSocket               com.dspread.mdm.service              I  🔧 检查OS升级状态...
2025-08-20 17:49:37.335  4878-4950  Common                  com.dspread.mdm.service              D  🔧 OsUpdateStatusChecker WebSocket连接成功，检查OS升级状态
2025-08-20 17:49:37.341  4878-4950  Common                  com.dspread.mdm.service              D  🔧 OsUpdateStatusChecker 没有待检查的OS升级任务
2025-08-20 17:49:37.347  4878-4950  WebSocket               com.dspread.mdm.service              I  🔧 OS升级状态检查已触发
2025-08-20 17:49:37.353  4878-4950  WebSocket               com.dspread.mdm.service              I  🔧 首次连接初始化检查完成
2025-08-20 17:49:37.436  4878-4950  Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"request_time":"1755683377180","org_request_time":"1755683376985","org_request_id":"1755683376985C0906","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755683377180S0000","serialNo":"01354090202503050399"}
2025-08-20 17:49:37.446  4878-4950  WebSocket               com.dspread.mdm.service              I  🔧 收到响应消息: transCode=S0000, orgRequestId=1755683376985C0906, state=0, remark=
2025-08-20 17:49:37.452  4878-4950  WebSocket               com.dspread.mdm.service              I  🔧 处理设备信息响应: state=0
2025-08-20 17:49:37.458  4878-4950  WebSocket               com.dspread.mdm.service              I  🔧 设备信息上传成功
2025-08-20 17:49:38.111  4878-4950  Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"request_time":"1755683377543","org_request_time":"1755683377232","org_request_id":"1755683377232C0109","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755683377543S0000","serialNo":"01354090202503050399"}
2025-08-20 17:49:38.120  4878-4950  WebSocket               com.dspread.mdm.service              I  🔧 收到响应消息: transCode=S0000, orgRequestId=1755683377232C0109, state=0, remark=
2025-08-20 17:49:38.126  4878-4950  WebSocket               com.dspread.mdm.service              I  🔧 终端信息上传成功
2025-08-20 17:49:39.667  4878-4878  Receiver                com.dspread.mdm.service              D  🔧 网络恢复，重新上传网络状态信息（防抖通过）
2025-08-20 17:49:39.693  4878-4878  WebSocket               com.dspread.mdm.service              I  🔧 C0904 被动式上送拒绝: NETWORK_RECOVERY (拒绝: 1)
2025-08-20 17:49:39.703  4878-4878  WebSocket               com.dspread.mdm.service              I  🔧 C0904 网络状态上送被流量控制阻止: 被动事件 'NETWORK_RECOVERY' 在平衡模式 - 重要变化下未启用
2025-08-20 17:50:01.691  4878-4951  WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 1 (第1个，待响应: 1)
2025-08-20 17:50:02.354  4878-4950  WebSocket               com.dspread.mdm.service              I  🔧 收到服务器 PONG 帧: 1 (待响应PING: 0)


###### 系统重启后，应用重启（无网络）
--------- beginning of system
--------- beginning of crash
--------- beginning of main
---------------------------- PROCESS STARTED (2795) for package com.dspread.mdm.service ----------------------------
2025-08-20 17:52:46.211  2795-2795  nativeloader            com.dspread.mdm.service              D  Configuring clns-4 for other apk /data/app/~~vomGg8kiFoE9-XFEv9rIHg==/com.dspread.mdm.service-mXfgcKUyk95e0Wn0Dv6R_g==/base.apk. target_sdk_version=34, uses_libraries=, library_path=/data/app/~~vomGg8kiFoE9-XFEv9rIHg==/com.dspread.mdm.service-mXfgcKUyk95e0Wn0Dv6R_g==/lib/arm:/data/app/~~vomGg8kiFoE9-XFEv9rIHg==/com.dspread.mdm.service-mXfgcKUyk95e0Wn0Dv6R_g==/base.apk!/lib/armeabi-v7a, permitted_path=/data:/mnt/expand:/data/user/0/com.dspread.mdm.service
2025-08-20 17:52:46.242  2795-2795  GraphicsEnvironment     com.dspread.mdm.service              V  Currently set values for:
2025-08-20 17:52:46.243  2795-2795  GraphicsEnvironment     com.dspread.mdm.service              V    angle_gl_driver_selection_pkgs=[]
2025-08-20 17:52:46.244  2795-2795  GraphicsEnvironment     com.dspread.mdm.service              V    angle_gl_driver_selection_values=[]
2025-08-20 17:52:46.245  2795-2795  GraphicsEnvironment     com.dspread.mdm.service              V  ANGLE GameManagerService for com.dspread.mdm.service: false
2025-08-20 17:52:46.245  2795-2795  GraphicsEnvironment     com.dspread.mdm.service              V  com.dspread.mdm.service is not listed in per-application setting
2025-08-20 17:52:46.246  2795-2795  GraphicsEnvironment     com.dspread.mdm.service              V  Neither updatable production driver nor prerelease driver is supported.
2025-08-20 17:52:46.284  2795-2795  Compatibil...geReporter com.dspread.mdm.service              D  Compat change id reported: 183155436; UID 1000; state: ENABLED
2025-08-20 17:52:46.353  2795-2795  MbrainDebugManagerImpl  com.dspread.mdm.service              D  getService failed
2025-08-20 17:52:46.448  2795-2795  Common                  com.dspread.mdm.service              D  🔧 日志配置：生产环境模式
2025-08-20 17:52:46.461  2795-2795  Common                  com.dspread.mdm.service              D  🔧 [DebugConfig] 调试模式已禁用
2025-08-20 17:52:46.467  2795-2795  Common                  com.dspread.mdm.service              D  🔧 SmartMdmServiceApp: 统一配置管理器初始化完成
2025-08-20 17:52:46.475  2795-2795  Common                  com.dspread.mdm.service              D  🔧 SmartMdmServiceApp: 系统目录初始化完成 - /data/pos/config/
2025-08-20 17:52:46.536  2795-2795  Receiver                com.dspread.mdm.service              D  🔧 ServiceGuardEventHandler 启动服务守护定时器: 120s
2025-08-20 17:52:46.546  2795-2795  Compatibil...geReporter com.dspread.mdm.service              D  Compat change id reported: 160794467; UID 1000; state: ENABLED
2025-08-20 17:52:46.553  2795-2795  Common                  com.dspread.mdm.service              I  ✅ ⏰ 设置服务守护定时器成功，下次执行: 120秒后 (2分钟)
2025-08-20 17:52:46.560  2795-2795  Common                  com.dspread.mdm.service              D  🔧 SmartMdmServiceApp: 服务守护定时器启动成功
2025-08-20 17:52:46.572  2795-2795  Common                  com.dspread.mdm.service              D  🔧 ServiceStartupManager 收到服务启动请求: APPLICATION_STARTUP, 强制启动: false
2025-08-20 17:52:46.584  2795-2795  Common                  com.dspread.mdm.service              D  🔧 ServiceStartupManager 开始执行服务启动: APPLICATION_STARTUP
2025-08-20 17:52:46.591  2795-2795  Common                  com.dspread.mdm.service              D  🔧 ServiceManager: 准备启动后台服务
2025-08-20 17:52:46.599  2795-2795  ContextImpl             com.dspread.mdm.service              W  Calling a method in the system process without a qualified user: android.app.ContextImpl.startService:1899 android.content.ContextWrapper.startService:825 com.dspread.mdm.service.platform.manager.ServiceManager.startBackgroundService:20 com.dspread.mdm.service.platform.manager.ServiceStartupManager.performServiceStart:121 com.dspread.mdm.service.platform.manager.ServiceStartupManager.startService:66 
2025-08-20 17:52:46.605  2795-2795  Common                  com.dspread.mdm.service              D  🔧 ServiceManager: 后台服务启动成功，ComponentName: ComponentInfo{com.dspread.mdm.service/com.dspread.mdm.service.services.SmartMdmBackgroundService}
2025-08-20 17:52:47.617  2795-2795  Common                  com.dspread.mdm.service              D  🔧 ServiceStartupManager 服务启动验证成功: APPLICATION_STARTUP
2025-08-20 17:52:47.624  2795-2795  Common                  com.dspread.mdm.service              D  🔧 ServiceStartupManager 服务启动成功: APPLICATION_STARTUP
2025-08-20 17:52:47.631  2795-2795  Common                  com.dspread.mdm.service              D  🔧 SmartMdmServiceApp: 主服务启动请求已提交
2025-08-20 17:52:47.638  2795-2795  Common                  com.dspread.mdm.service              D  🔧 SmartMdmServiceApp: Application启动完成，包名: com.dspread.mdm.service
2025-08-20 17:52:47.658  2795-2795  M-ProMotion             com.dspread.mdm.service              I  M-ProMotion is disabled
2025-08-20 17:52:47.704  2795-2795  Choreographer           com.dspread.mdm.service              I  Skipped 80 frames!  The application may be doing too much work on its main thread.
2025-08-20 17:52:47.715  2795-2795  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 服务启动
2025-08-20 17:52:47.744  2795-2795  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 开始服务初始化
2025-08-20 17:52:47.750  2795-2795  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] Android 14+: 初始化DSPREAD服务
2025-08-20 17:52:47.757  2795-2795  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 服务创建完成
2025-08-20 17:52:47.758  2795-2916  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 后台线程：初始化DSPREAD服务
2025-08-20 17:52:47.765  2795-2795  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] onStartCommand调用
2025-08-20 17:52:47.767  2795-2916  Platform                com.dspread.mdm.service              D  🔧 DspreadService 开始初始化DSPREAD服务
2025-08-20 17:52:47.774  2795-2916  ContextImpl             com.dspread.mdm.service              W  Calling a method in the system process without a qualified user: android.app.ContextImpl.bindService:1988 android.content.ContextWrapper.bindService:861 com.dspread.mdm.service.services.DspreadService.bindSysService:111 com.dspread.mdm.service.services.DspreadService.initialize:63 com.dspread.mdm.service.services.SmartMdmBackgroundService.initializeDspreadServiceAsync$lambda$1:227 
2025-08-20 17:52:47.776  2795-2795  Platform                com.dspread.mdm.service              D  🔧 WakeLockManager 获取智能WakeLock成功，持续时间: 60000ms
2025-08-20 17:52:47.783  2795-2916  Platform                com.dspread.mdm.service              D  🔧 DspreadService 系统服务绑定请求已发送
2025-08-20 17:52:47.787  2795-2795  Platform                com.dspread.mdm.service              D  🔧 WakeLockManager 设置WakeLock续期定时器: 60000ms
2025-08-20 17:52:47.794  2795-2795  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 初始化基础组件
2025-08-20 17:52:47.801  2795-2795  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 开始初始化...
2025-08-20 17:52:47.815  2795-2795  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 包更新广播接收器注册完成
2025-08-20 17:52:47.821  2795-2795  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 统一广播接收器注册完成
2025-08-20 17:52:47.827  2795-2795  Common                  com.dspread.mdm.service              I  ✅ BroadcastManager 初始化完成
2025-08-20 17:52:47.872  2795-2795  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 服务初始化完成
2025-08-20 17:52:47.879  2795-2795  Platform                com.dspread.mdm.service              D  🔧 DspreadService 系统服务连接成功: ComponentInfo{com.dspread.sdkservice/com.dspread.sdkservice.SDKSysService}
2025-08-20 17:52:47.887  2795-2795  Platform                com.dspread.mdm.service              D  🔧 DspreadService 系统服务绑定完成
2025-08-20 17:52:47.895  2795-2916  ContextImpl             com.dspread.mdm.service              W  Calling a method in the system process without a qualified user: android.app.ContextImpl.bindService:1988 android.content.ContextWrapper.bindService:861 com.dspread.mdm.service.services.DspreadService.bindDevService:166 com.dspread.mdm.service.services.DspreadService.initialize:66 com.dspread.mdm.service.services.SmartMdmBackgroundService.initializeDspreadServiceAsync$lambda$1:227 
2025-08-20 17:52:47.897  2795-2795  Receiver                com.dspread.mdm.service              D  🔧 电池状态变化: 电量=95%, 温度=28°C, 充电=true
2025-08-20 17:52:47.900  2795-2916  Platform                com.dspread.mdm.service              D  🔧 DspreadService 设备服务绑定请求已发送
2025-08-20 17:52:48.004  2795-2795  Provisioning            com.dspread.mdm.service              I  ℹ️ ProvisioningManager初始化完成
2025-08-20 17:52:48.020  2795-2795  Provisioning            com.dspread.mdm.service              D  🔧 状态标志加载成功: /data/pos/config/provisioning_flags.json
2025-08-20 17:52:48.042  2795-2795  WebSocket               com.dspread.mdm.service              I  🔧 C0902 被动式上送: battery_level_change_5 (被动: 1)
2025-08-20 17:52:48.061  2795-2795  Platform                com.dspread.mdm.service              D  🔧 DeviceInfoApi 单例实例已创建
2025-08-20 17:52:48.068  2795-2795  Platform                com.dspread.mdm.service              E  ❌ DspreadService 设备服务不可用，无法获取序列号
2025-08-20 17:52:48.094  2795-2795  WebSocket               com.dspread.mdm.service              I  🔧 消息已缓存: C0902 (缓存数量: 1)
2025-08-20 17:52:48.101  2795-2795  WebSocket               com.dspread.mdm.service              I  🔧 C0902 电池状态上送成功 (1, trigger=battery_level_change_5)
2025-08-20 17:52:48.108  2795-2795  Platform                com.dspread.mdm.service              D  🔧 DspreadService 设备服务连接成功: ComponentInfo{com.dspread.sdkservice/com.dspread.sdkservice.SDKDevService}
2025-08-20 17:52:48.115  2795-2795  Platform                com.dspread.mdm.service              D  🔧 DspreadService 设备服务绑定完成
2025-08-20 17:52:49.124  2795-2916  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 等待DSPREAD服务就绪 (1/10): true
2025-08-20 17:52:49.137  2795-2916  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] DSPREAD服务就绪，启动Provisioning
2025-08-20 17:52:49.150  2795-2916  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 验证DSPREAD服务功能
2025-08-20 17:52:49.177  2795-2916  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 设备序列号验证: 成功(01354090202503050399)
2025-08-20 17:52:49.188  2795-2916  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 服务状态 - 系统服务:✅ 设备服务:✅
2025-08-20 17:52:49.198  2795-2916  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 启动ProvisioningService
2025-08-20 17:52:49.212  2795-2916  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] ProvisioningService启动完成
2025-08-20 17:52:49.213  2795-2910  Provisioning            com.dspread.mdm.service              I  ℹ️ 开始执行Provisioning配置，触发类型: FIRST_BOOT
2025-08-20 17:52:49.222  2795-2910  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] Provisioning进度: 正在下载配置文件...
2025-08-20 17:52:49.231  2795-2910  Provisioning            com.dspread.mdm.service              I  ℹ️ 开始请求远程配置...
2025-08-20 17:52:49.254  2795-2910  Provisioning            com.dspread.mdm.service              I  ℹ️ 请求配置: https://config.dspreadserv.net/status/config?SN=MDEzNTQwOTAyMDI1MDMwNTAzOTk=&timestamp=1755683569253
2025-08-20 17:52:49.285  2795-2910  Provisioning            com.dspread.mdm.service              E  ❌ 配置API请求失败
2025-08-20 17:52:49.299  2795-2910  Provisioning            com.dspread.mdm.service              W  ⚠️ 远程配置请求失败: Unable to resolve host "config.dspreadserv.net": No address associated with hostname
2025-08-20 17:52:49.315  2795-2910  Provisioning            com.dspread.mdm.service              I  ℹ️ 📂 使用本地保存的配置 - CID: 1001
2025-08-20 17:52:49.322  2795-2910  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] Provisioning进度: 正在下载 Logo...
2025-08-20 17:52:49.430  2795-2910  Provisioning            com.dspread.mdm.service              I  ℹ️ 文件已存在且MD5匹配，跳过下载: Logo
2025-08-20 17:52:49.436  2795-2910  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] Provisioning进度: 正在下载 BootAnimation...
2025-08-20 17:52:49.542  2795-2910  Provisioning            com.dspread.mdm.service              I  ℹ️ 文件已存在且MD5匹配，跳过下载: BootAnimation
2025-08-20 17:52:49.548  2795-2910  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] Provisioning进度: 正在应用配置...
2025-08-20 17:52:49.554  2795-2910  Provisioning            com.dspread.mdm.service              I  ℹ️ 配置应用完成
2025-08-20 17:52:49.559  2795-2910  Provisioning            com.dspread.mdm.service              W  ⚠️ 远程配置失败，首次配置状态保持未完成，下次启动将重试
2025-08-20 17:52:49.568  2795-2910  Provisioning            com.dspread.mdm.service              D  🔧 状态标志保存成功: /data/pos/config/provisioning_flags.json
2025-08-20 17:52:49.573  2795-2910  Provisioning            com.dspread.mdm.service              I  ℹ️ Provisioning配置执行完成
2025-08-20 17:52:49.579  2795-2910  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] Provisioning完成，启动核心组件
2025-08-20 17:52:49.606  2795-2795  WebSocket               com.dspread.mdm.service              I  🔧 开始初始化 WebSocket 组件...
2025-08-20 17:52:49.612  2795-2795  WebSocket               com.dspread.mdm.service              I  🔧 初始化 WebSocket 密钥管理器
2025-08-20 17:52:49.617  2795-2795  WebSocket               com.dspread.mdm.service              I  🔧 生成客户端公私钥对
2025-08-20 17:52:49.772  2795-2795  Common                  com.dspread.mdm.service              I  ✅ WebSocket 密钥管理器初始化完成
2025-08-20 17:52:49.782  2795-2795  WebSocket               com.dspread.mdm.service              I  🔧 开始初始化 WsMessageCenter...
2025-08-20 17:52:49.787  2795-2795  WebSocket               com.dspread.mdm.service              I  🔧 WsMessageCenter 首次初始化
2025-08-20 17:52:49.807  2795-2795  WebSocket               com.dspread.mdm.service              I  🔧 WsTaskManager 初始化完成
2025-08-20 17:52:49.910  2795-2795  WebSocket               com.dspread.mdm.service              I  🔧 WsMessageCenter 初始化完成
2025-08-20 17:52:49.916  2795-2795  WebSocket               com.dspread.mdm.service              I  🔧 开始清理终态任务，当前任务数量: 0
2025-08-20 17:52:49.921  2795-2795  WebSocket               com.dspread.mdm.service              I  🔧 没有需要清理的终态任务
2025-08-20 17:52:49.927  2795-2795  WebSocket               com.dspread.mdm.service              I  🔧 开始重置任务状态，当前任务个数: 0
2025-08-20 17:52:49.932  2795-2795  WebSocket               com.dspread.mdm.service              I  🔧 没有需要重置的任务
2025-08-20 17:52:49.938  2795-2795  Common                  com.dspread.mdm.service              I  ✅ 任务状态恢复完成
2025-08-20 17:52:49.954  2795-2795  WebSocket               com.dspread.mdm.service              I  🔧 使用Provisioning配置的WebSocket URL: wss://api.dspreadserv.net/status/websocket/register
2025-08-20 17:52:49.960  2795-2795  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接管理器初始化完成
2025-08-20 17:52:49.965  2795-2795  Common                  com.dspread.mdm.service              I  ✅ WebSocket 管理器初始化完成
2025-08-20 17:52:49.971  2795-2795  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 管理器初始化完成
2025-08-20 17:52:49.977  2795-2795  Common                  com.dspread.mdm.service              I  ✅ WebSocket 组件初始化完成
2025-08-20 17:52:49.983  2795-2795  WebSocket               com.dspread.mdm.service              I  🔧 启动 WebSocket 连接...
2025-08-20 17:52:49.988  2795-2795  WebSocket               com.dspread.mdm.service              I  🔧 开始连接 WebSocket...
2025-08-20 17:52:50.012  2795-2795  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接到: wss://api.dspreadserv.net/status/websocket/register?serialNo=MDEzNTQwOTAyMDI1MDMwNTAzOTk=&publicKey=TUlHZk1BMEdDU3FHU0liM0RRRUJBUVVBQTRHTkFEQ0JpUUtCZ1FDKytib1FDMkFNelBBdU0wcDkweG0valpCSStFSHZjTC9oY3hET2xoc2R6RnRsM1Q4RTVEVXlWQTh2emxxbDRTQnZhUTJudWJKK1JGdFVid1hWK2Q0b0NPQm9QSWs4TVBtK1JSNGtWRVRhaVRoclNmZFlOZHJNSEpoT241RUNaUjVnN21XeUUwbERzVlJkV0JKbUxQdEp5ZytSWTQ0TW1oNjVnR1NOdTI1OFN3SURBUUFC&query=1&msgVer=3&timestamp=1755683569997&signature=pFDmCgpNOXQzgtCJzmtHH8sGIM6IWGruHDw9+8R+N/4i91vqUIOAwZZavj5lRx6GKoXlB1DYxAJQT7RiU0CpEZLmQSfo6hiVQvBEwPRcKBguPwGo4k6VWGffw+yMeNXpkIQU+iR70Y3wy9iSfl6SZtpilQOtrwKfIpm/8lK6G3I=
2025-08-20 17:52:50.020  2795-2795  WebSocket               com.dspread.mdm.service              I  🔧 未检测到系统代理设置
2025-08-20 17:52:50.067  2795-2795  WebSocket               com.dspread.mdm.service              I  🔧 ===== WebSocket 库默认配置 =====
2025-08-20 17:52:50.072  2795-2795  WebSocket               com.dspread.mdm.service              I  🔧 默认 PING 间隔: 30000ms (30秒)
2025-08-20 17:52:50.078  2795-2795  WebSocket               com.dspread.mdm.service              I  🔧 默认 PONG 间隔: 0ms (0秒)
2025-08-20 17:52:50.083  2795-2795  WebSocket               com.dspread.mdm.service              I  🔧 帧队列大小: 5
2025-08-20 17:52:50.089  2795-2795  WebSocket               com.dspread.mdm.service              I  🔧 连接超时: 15000ms (15秒)
2025-08-20 17:52:50.094  2795-2795  WebSocket               com.dspread.mdm.service              I  🔧 允许缺失关闭帧: false
2025-08-20 17:52:50.100  2795-2795  WebSocket               com.dspread.mdm.service              I  🔧 ================================
2025-08-20 17:52:50.106  2795-2795  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接请求已发送
2025-08-20 17:52:50.112  2795-2795  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接启动完成
2025-08-20 17:52:50.113  2795-2927  WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误
2025-08-20 17:52:50.119  2795-2795  Common                  com.dspread.mdm.service              D  🔧 [ModuleManagerRegistry] 模块管理器注册中心初始化完成
2025-08-20 17:52:50.119  2795-2927  WebSocket               com.dspread.mdm.service              I  🔧 onError: 不触发重连，等待 onDisconnected 处理
2025-08-20 17:52:50.125  2795-2927  WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误
2025-08-20 17:52:50.126  2795-2795  Provisioning            com.dspread.mdm.service              D  🔧 ProvisioningEventHandler 启动Provisioning定时器: 43200s (12小时)
2025-08-20 17:52:50.126  2795-2910  Common                  com.dspread.mdm.service              D  🔧 [ModuleManagerRegistry] 启动所有模块
2025-08-20 17:52:50.132  2795-2927  WebSocket               com.dspread.mdm.service              I  🔧 reconnect() 被调用，调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onConnectError:672
2025-08-20 17:52:50.136  2795-2795  Common                  com.dspread.mdm.service              I  ✅ ⏰ 设置初始化定时器成功，下次执行: 43200秒后 (12小时)
2025-08-20 17:52:50.138  2795-2927  WebSocket               com.dspread.mdm.service              I  🔧 开始重连 WebSocket... (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onConnectError:672)
2025-08-20 17:52:50.141  2795-2910  Common                  com.dspread.mdm.service              D  🔧 Module LogStream Manager status changed to: INITIALIZED
2025-08-20 17:52:50.150  2795-2910  Common                  com.dspread.mdm.service              D  🔧 Module LogStream Manager status changed to: STARTING
2025-08-20 17:52:50.153  2795-2927  WebSocket               com.dspread.mdm.service              I  🔧 使用Provisioning配置的重连策略: delayPolicy=1, delaySwitch=1, delayTime=60
2025-08-20 17:52:50.155  2795-2795  Common                  com.dspread.mdm.service              I  ✅ ⏰ 设置心跳定时器成功，下次执行: 300秒后 (5分钟)
2025-08-20 17:52:50.159  2795-2927  WebSocket               com.dspread.mdm.service              I  🔧 当前断开记录数: 0, 延迟开关: 1
2025-08-20 17:52:50.164  2795-2795  Common                  com.dspread.mdm.service              I  ✅ ⏰ 设置任务执行定时器成功，下次执行: 60秒后 (1分钟)
2025-08-20 17:52:50.166  2795-2927  WebSocket               com.dspread.mdm.service              I  🔧 重连策略计算完成: 第1次重连，间隔3000ms (3秒)
2025-08-20 17:52:50.171  2795-2927  WebSocket               com.dspread.mdm.service              I  🔧 开始第1次重连，间隔3000ms (3秒) (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onConnectError:672)
2025-08-20 17:52:50.184  2795-2795  Common                  com.dspread.mdm.service              I  ✅ ⏰ 设置终端信息定时器成功，下次执行: 900秒后 (15分钟)
2025-08-20 17:52:50.208  2795-2795  Common                  com.dspread.mdm.service              D  🔧 TimerConfig ========== 当前定时器配置 (生产模式) ==========
2025-08-20 17:52:50.214  2795-2795  Common                  com.dspread.mdm.service              D  🔧 TimerConfig ①心跳定时器: 300秒
2025-08-20 17:52:50.220  2795-2795  Common                  com.dspread.mdm.service              D  🔧 TimerConfig ②终端信息上传定时器: 900秒
2025-08-20 17:52:50.226  2795-2795  Common                  com.dspread.mdm.service              D  🔧 TimerConfig ③任务执行定时器: 60秒
2025-08-20 17:52:50.232  2795-2795  Common                  com.dspread.mdm.service              D  🔧 TimerConfig ④服务守护定时器: 120秒
2025-08-20 17:52:50.237  2795-2795  Common                  com.dspread.mdm.service              D  🔧 TimerConfig ⑤初始化定时器: 43200秒
2025-08-20 17:52:50.243  2795-2795  Common                  com.dspread.mdm.service              D  🔧 TimerConfig ===============================================
2025-08-20 17:52:50.249  2795-2795  Common                  com.dspread.mdm.service              I  ✅ [SmartMdmBackgroundService] 所有定时器启动完成
2025-08-20 17:52:50.255  2795-2795  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 核心组件初始化完成
2025-08-20 17:52:50.270  2795-2910  Common                  com.dspread.mdm.service              D  🔧 Module LogStream Manager status changed to: RUNNING
2025-08-20 17:52:50.276  2795-2910  Common                  com.dspread.mdm.service              D  🔧 [ModuleManagerRegistry] LogStreamManager启动成功
2025-08-20 17:52:50.299  2795-2910  Compatibil...geReporter com.dspread.mdm.service              D  Compat change id reported: 263076149; UID 1000; state: ENABLED
2025-08-20 17:52:50.310  2795-2910  Common                  com.dspread.mdm.service              D  🔧 Module Geofence Manager status changed to: INITIALIZED
2025-08-20 17:52:50.317  2795-2910  Common                  com.dspread.mdm.service              D  🔧 Module Geofence Manager status changed to: STARTING
2025-08-20 17:52:50.336  2795-2910  Platform                com.dspread.mdm.service              D  🔧 用户交互监控已启动（简化版本）
2025-08-20 17:52:50.341  2795-2910  Platform                com.dspread.mdm.service              D  🔧 使用模拟用户交互检测
2025-08-20 17:52:50.347  2795-2910  Platform                com.dspread.mdm.service              D  🔧 用户交互监控已启动
2025-08-20 17:52:50.353  2795-2910  Service                 com.dspread.mdm.service              D  🔧 [GeofenceManager]Constants.geofenceStatus = 1
2025-08-20 17:52:50.358  2795-2910  Service                 com.dspread.mdm.service              D  🔧 [GeofenceManager]-- Constants.storeId =  -- Constants.storeSsid =  -- Constants.storeIp = 
2025-08-20 17:52:50.365  2795-2910  Common                  com.dspread.mdm.service              D  🔧 Module Geofence Manager status changed to: RUNNING
2025-08-20 17:52:50.371  2795-2910  Common                  com.dspread.mdm.service              D  🔧 [ModuleManagerRegistry] GeofenceManager启动成功
2025-08-20 17:52:50.388  2795-2910  RuleBase                com.dspread.mdm.service              D  🔧 RuleBaseManager 开始初始化RuleBaseManager...
2025-08-20 17:52:50.398  2795-2910  RuleBase                com.dspread.mdm.service              D  🔧 RuleBaseStorage RuleBase存储管理器初始化完成
2025-08-20 17:52:50.404  2795-2910  RuleBase                com.dspread.mdm.service              D  🔧 RuleStateMonitor 状态监控器初始化完成
2025-08-20 17:52:50.409  2795-2910  RuleBase                com.dspread.mdm.service              D  🔧 RuleBaseManager RuleBaseManager初始化完成
2025-08-20 17:52:50.415  2795-2910  Common                  com.dspread.mdm.service              D  🔧 [ModuleManagerRegistry] RuleBaseManager启动成功
2025-08-20 17:52:50.421  2795-2910  Common                  com.dspread.mdm.service              D  🔧 [ModuleManagerRegistry] 所有模块启动完成
2025-08-20 17:52:52.723  2795-2940  ProfileInstaller        com.dspread.mdm.service              D  Installing profile for com.dspread.mdm.service
2025-08-20 17:52:53.204  2795-2795  WebSocket               com.dspread.mdm.service              I  🔧 重连 WebSocket: wss://api.dspreadserv.net/status/websocket/register?serialNo=MDEzNTQwOTAyMDI1MDMwNTAzOTk=&publicKey=TUlHZk1BMEdDU3FHU0liM0RRRUJBUVVBQTRHTkFEQ0JpUUtCZ1FDKytib1FDMkFNelBBdU0wcDkweG0valpCSStFSHZjTC9oY3hET2xoc2R6RnRsM1Q4RTVEVXlWQTh2emxxbDRTQnZhUTJudWJKK1JGdFVid1hWK2Q0b0NPQm9QSWs4TVBtK1JSNGtWRVRhaVRoclNmZFlOZHJNSEpoT241RUNaUjVnN21XeUUwbERzVlJkV0JKbUxQdEp5ZytSWTQ0TW1oNjVnR1NOdTI1OFN3SURBUUFC&query=0&msgVer=3&timestamp=1755683573183&signature=p2XfjMPnZGy3paqPtjHNn5sI+UxjfJ/ffJf0y29o0qJDpA6FTPRUoKnJ+6KIzR8aEFqQahcTMTF8ueCCzaQce4pwsFYwzNqQ20FYWETFscEibMczGEcQKmo6JxZS/OxwuC7MqOTS9AxPeEagX05Wcm46OleoUJNnyEHRLpX1edo=
2025-08-20 17:52:53.213  2795-2795  WebSocket               com.dspread.mdm.service              I  🔧 未检测到系统代理设置
2025-08-20 17:52:53.252  2795-2951  WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误
2025-08-20 17:52:53.259  2795-2951  WebSocket               com.dspread.mdm.service              I  🔧 onError: 不触发重连，等待 onDisconnected 处理
2025-08-20 17:52:53.265  2795-2951  WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误
2025-08-20 17:52:53.272  2795-2951  WebSocket               com.dspread.mdm.service              I  🔧 reconnect() 被调用，调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onConnectError:672
2025-08-20 17:52:53.278  2795-2951  WebSocket               com.dspread.mdm.service              I  🔧 开始重连 WebSocket... (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onConnectError:672)
2025-08-20 17:52:53.293  2795-2951  WebSocket               com.dspread.mdm.service              I  🔧 使用Provisioning配置的重连策略: delayPolicy=1, delaySwitch=1, delayTime=60
2025-08-20 17:52:53.299  2795-2951  WebSocket               com.dspread.mdm.service              I  🔧 当前断开记录数: 0, 延迟开关: 1
2025-08-20 17:52:53.304  2795-2951  WebSocket               com.dspread.mdm.service              I  🔧 重连策略计算完成: 第2次重连，间隔3000ms (3秒)
2025-08-20 17:52:53.310  2795-2951  WebSocket               com.dspread.mdm.service              I  🔧 开始第2次重连，间隔3000ms (3秒) (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onConnectError:672)
2025-08-20 17:52:56.349  2795-2795  WebSocket               com.dspread.mdm.service              I  🔧 重连 WebSocket: wss://api.dspreadserv.net/status/websocket/register?serialNo=MDEzNTQwOTAyMDI1MDMwNTAzOTk=&publicKey=TUlHZk1BMEdDU3FHU0liM0RRRUJBUVVBQTRHTkFEQ0JpUUtCZ1FDKytib1FDMkFNelBBdU0wcDkweG0valpCSStFSHZjTC9oY3hET2xoc2R6RnRsM1Q4RTVEVXlWQTh2emxxbDRTQnZhUTJudWJKK1JGdFVid1hWK2Q0b0NPQm9QSWs4TVBtK1JSNGtWRVRhaVRoclNmZFlOZHJNSEpoT241RUNaUjVnN21XeUUwbERzVlJkV0JKbUxQdEp5ZytSWTQ0TW1oNjVnR1NOdTI1OFN3SURBUUFC&query=0&msgVer=3&timestamp=1755683576326&signature=eXhAyq4ZtGhvCLKP6CJoxA0L3MQjQJfDdpi63+lW9LC9vM0GksqHd0SxU2D6wrZaunZN6fXyZTEDOJS/BvgnSbnhzMeU5TZJSnKvmic1tb7xzyHg3K6/XhhSkxOC/BcbZTGGWsaWe6X0cSZUv0c6FuOsnfgEybUWVkplfHnFXfQ=
2025-08-20 17:52:56.365  2795-2795  WebSocket               com.dspread.mdm.service              I  🔧 未检测到系统代理设置
2025-08-20 17:52:56.421  2795-2954  WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误
2025-08-20 17:52:56.432  2795-2954  WebSocket               com.dspread.mdm.service              I  🔧 onError: 不触发重连，等待 onDisconnected 处理
2025-08-20 17:52:56.442  2795-2954  WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误
2025-08-20 17:52:56.454  2795-2954  WebSocket               com.dspread.mdm.service              I  🔧 reconnect() 被调用，调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onConnectError:672
2025-08-20 17:52:56.462  2795-2954  WebSocket               com.dspread.mdm.service              I  🔧 开始重连 WebSocket... (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onConnectError:672)
2025-08-20 17:52:56.482  2795-2954  WebSocket               com.dspread.mdm.service              I  🔧 使用Provisioning配置的重连策略: delayPolicy=1, delaySwitch=1, delayTime=60
2025-08-20 17:52:56.488  2795-2954  WebSocket               com.dspread.mdm.service              I  🔧 当前断开记录数: 0, 延迟开关: 1
2025-08-20 17:52:56.495  2795-2954  WebSocket               com.dspread.mdm.service              I  🔧 重连策略计算完成: 第3次重连，间隔3000ms (3秒)
2025-08-20 17:52:56.501  2795-2954  WebSocket               com.dspread.mdm.service              I  🔧 开始第3次重连，间隔3000ms (3秒) (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onConnectError:672)
2025-08-20 17:52:59.545  2795-2795  WebSocket               com.dspread.mdm.service              I  🔧 重连 WebSocket: wss://api.dspreadserv.net/status/websocket/register?serialNo=MDEzNTQwOTAyMDI1MDMwNTAzOTk=&publicKey=TUlHZk1BMEdDU3FHU0liM0RRRUJBUVVBQTRHTkFEQ0JpUUtCZ1FDKytib1FDMkFNelBBdU0wcDkweG0valpCSStFSHZjTC9oY3hET2xoc2R6RnRsM1Q4RTVEVXlWQTh2emxxbDRTQnZhUTJudWJKK1JGdFVid1hWK2Q0b0NPQm9QSWs4TVBtK1JSNGtWRVRhaVRoclNmZFlOZHJNSEpoT241RUNaUjVnN21XeUUwbERzVlJkV0JKbUxQdEp5ZytSWTQ0TW1oNjVnR1NOdTI1OFN3SURBUUFC&query=0&msgVer=3&timestamp=1755683579515&signature=mAxvTicbdHvXCLUyBFXsqIlMn5uV71jriSCpGvn1av21kNe4ilOiGq8uvSt49v5kFoADcKswr6qNF3RhT8mZyqmAjykglXSwVntOpl0OK1KhhvJ2iF3wK4pchBewwV6GHYIROKeuKAVsBXxD8DPpjyzOd5XVBd2ZXFPb8XPCtL4=
2025-08-20 17:52:59.557  2795-2795  WebSocket               com.dspread.mdm.service              I  🔧 未检测到系统代理设置
2025-08-20 17:52:59.612  2795-2956  WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误
2025-08-20 17:52:59.627  2795-2956  WebSocket               com.dspread.mdm.service              I  🔧 onError: 不触发重连，等待 onDisconnected 处理
2025-08-20 17:52:59.638  2795-2956  WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误
2025-08-20 17:52:59.647  2795-2956  WebSocket               com.dspread.mdm.service              I  🔧 reconnect() 被调用，调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onConnectError:672
2025-08-20 17:52:59.653  2795-2956  WebSocket               com.dspread.mdm.service              I  🔧 开始重连 WebSocket... (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onConnectError:672)
2025-08-20 17:52:59.668  2795-2956  WebSocket               com.dspread.mdm.service              I  🔧 使用Provisioning配置的重连策略: delayPolicy=1, delaySwitch=1, delayTime=60
2025-08-20 17:52:59.674  2795-2956  WebSocket               com.dspread.mdm.service              I  🔧 当前断开记录数: 0, 延迟开关: 1
2025-08-20 17:52:59.680  2795-2956  WebSocket               com.dspread.mdm.service              I  🔧 重连策略计算完成: 第4次重连，间隔6000ms (6秒)
2025-08-20 17:52:59.686  2795-2956  WebSocket               com.dspread.mdm.service              I  🔧 开始第4次重连，间隔6000ms (6秒) (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onConnectError:672)
2025-08-20 17:53:05.735  2795-2795  WebSocket               com.dspread.mdm.service              I  🔧 重连 WebSocket: wss://api.dspreadserv.net/status/websocket/register?serialNo=MDEzNTQwOTAyMDI1MDMwNTAzOTk=&publicKey=TUlHZk1BMEdDU3FHU0liM0RRRUJBUVVBQTRHTkFEQ0JpUUtCZ1FDKytib1FDMkFNelBBdU0wcDkweG0valpCSStFSHZjTC9oY3hET2xoc2R6RnRsM1Q4RTVEVXlWQTh2emxxbDRTQnZhUTJudWJKK1JGdFVid1hWK2Q0b0NPQm9QSWs4TVBtK1JSNGtWRVRhaVRoclNmZFlOZHJNSEpoT241RUNaUjVnN21XeUUwbERzVlJkV0JKbUxQdEp5ZytSWTQ0TW1oNjVnR1NOdTI1OFN3SURBUUFC&query=0&msgVer=3&timestamp=1755683585705&signature=KYgdebOehSxYEYZEIX0GoplliYpYEnF7HVsVKFtvSL8TWmHc4Z/8qnQR8/OnA5Yd7hmmXRlT+vvdwCW369s/qyfINSftWFxkvQDNzRv0IeyPt6I9gnoNwFvzeOdTW5k7PDz47SL+uzSjHszDCfeaQXMW0mP4zyWA64xVa2myBHU=
2025-08-20 17:53:05.748  2795-2795  WebSocket               com.dspread.mdm.service              I  🔧 未检测到系统代理设置
2025-08-20 17:53:05.802  2795-2959  WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误
2025-08-20 17:53:05.809  2795-2959  WebSocket               com.dspread.mdm.service              I  🔧 onError: 不触发重连，等待 onDisconnected 处理
2025-08-20 17:53:05.815  2795-2959  WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误
2025-08-20 17:53:05.823  2795-2959  WebSocket               com.dspread.mdm.service              I  🔧 reconnect() 被调用，调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onConnectError:672
2025-08-20 17:53:05.829  2795-2959  WebSocket               com.dspread.mdm.service              I  🔧 开始重连 WebSocket... (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onConnectError:672)
2025-08-20 17:53:05.845  2795-2959  WebSocket               com.dspread.mdm.service              I  🔧 使用Provisioning配置的重连策略: delayPolicy=1, delaySwitch=1, delayTime=60
2025-08-20 17:53:05.851  2795-2959  WebSocket               com.dspread.mdm.service              I  🔧 当前断开记录数: 0, 延迟开关: 1
2025-08-20 17:53:05.857  2795-2959  WebSocket               com.dspread.mdm.service              I  🔧 重连策略计算完成: 第5次重连，间隔9000ms (9秒)
2025-08-20 17:53:05.863  2795-2959  WebSocket               com.dspread.mdm.service              I  🔧 开始第5次重连，间隔9000ms (9秒) (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onConnectError:672)
2025-08-20 17:53:14.961  2795-2795  WebSocket               com.dspread.mdm.service              I  🔧 重连 WebSocket: wss://api.dspreadserv.net/status/websocket/register?serialNo=MDEzNTQwOTAyMDI1MDMwNTAzOTk=&publicKey=TUlHZk1BMEdDU3FHU0liM0RRRUJBUVVBQTRHTkFEQ0JpUUtCZ1FDKytib1FDMkFNelBBdU0wcDkweG0valpCSStFSHZjTC9oY3hET2xoc2R6RnRsM1Q4RTVEVXlWQTh2emxxbDRTQnZhUTJudWJKK1JGdFVid1hWK2Q0b0NPQm9QSWs4TVBtK1JSNGtWRVRhaVRoclNmZFlOZHJNSEpoT241RUNaUjVnN21XeUUwbERzVlJkV0JKbUxQdEp5ZytSWTQ0TW1oNjVnR1NOdTI1OFN3SURBUUFC&query=0&msgVer=3&timestamp=1755683594891&signature=dpyKdP/Sq4xbTd/iKit/0NvQPYmac59b7/GC+n2viGzQ/bvQQPEtimZLZR2uLJ/oM7/2UHvZTSJyvVBFh5I+/5SP5dy2rczOpk1j/gx51FgG9fd18JbBtsc47/E80LPxQ92yYOoyCPzvGnx0qchqpQd2EckBVIpDfWetX4YMx3s=
2025-08-20 17:53:14.982  2795-2795  WebSocket               com.dspread.mdm.service              I  🔧 未检测到系统代理设置
2025-08-20 17:53:15.106  2795-2980  WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误
2025-08-20 17:53:15.123  2795-2980  WebSocket               com.dspread.mdm.service              I  🔧 onError: 不触发重连，等待 onDisconnected 处理
2025-08-20 17:53:15.136  2795-2980  WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误
2025-08-20 17:53:15.151  2795-2980  WebSocket               com.dspread.mdm.service              I  🔧 reconnect() 被调用，调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onConnectError:672
2025-08-20 17:53:15.159  2795-2980  WebSocket               com.dspread.mdm.service              I  🔧 开始重连 WebSocket... (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onConnectError:672)
2025-08-20 17:53:15.179  2795-2980  WebSocket               com.dspread.mdm.service              I  🔧 使用Provisioning配置的重连策略: delayPolicy=1, delaySwitch=1, delayTime=60
2025-08-20 17:53:15.189  2795-2980  WebSocket               com.dspread.mdm.service              I  🔧 当前断开记录数: 0, 延迟开关: 1
2025-08-20 17:53:15.199  2795-2980  WebSocket               com.dspread.mdm.service              I  🔧 重连策略计算完成: 第6次重连，间隔12000ms (12秒)
2025-08-20 17:53:15.209  2795-2980  WebSocket               com.dspread.mdm.service              I  🔧 开始第6次重连，间隔12000ms (12秒) (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onConnectError:672)
2025-08-20 17:53:15.322  2795-2795  Platform                com.dspread.mdm.service              D  🔧 [WiFiManagerApi] WiFiManagerApi 单例实例已创建
2025-08-20 17:53:15.457  2795-2795  Platform                com.dspread.mdm.service              D  🔧 NetworkApi 单例实例已创建
2025-08-20 17:53:15.477  2795-2795  Receiver                com.dspread.mdm.service              D  🔧 网络状态变化: 连接=true, 类型=WIFI
2025-08-20 17:53:15.508  2795-2795  Receiver                com.dspread.mdm.service              D  🔧 网络恢复，尝试重新连接WebSocket
2025-08-20 17:53:15.540  2795-2795  Receiver                com.dspread.mdm.service              D  🔧 ServiceManagementEventHandler 处理网络变化事件
2025-08-20 17:53:15.559  2795-2795  Common                  com.dspread.mdm.service              D  🔧 ServiceStartupManager 服务正在运行，无需启动: NETWORK_CHANGE
2025-08-20 17:53:15.572  2795-2795  Receiver                com.dspread.mdm.service              D  🔧 ServiceManagementEventHandler 网络变化后服务检查完成
2025-08-20 17:53:15.579  2795-2795  Provisioning            com.dspread.mdm.service              D  🔧 ProvisioningEventHandler 网络状态变化
2025-08-20 17:53:15.587  2795-2795  Provisioning            com.dspread.mdm.service              D  🔧 ProvisioningEventHandler 忽略初始启动时的网络连接事件
2025-08-20 17:53:27.259  2795-2795  WebSocket               com.dspread.mdm.service              I  🔧 重连 WebSocket: wss://api.dspreadserv.net/status/websocket/register?serialNo=MDEzNTQwOTAyMDI1MDMwNTAzOTk=&publicKey=TUlHZk1BMEdDU3FHU0liM0RRRUJBUVVBQTRHTkFEQ0JpUUtCZ1FDKytib1FDMkFNelBBdU0wcDkweG0valpCSStFSHZjTC9oY3hET2xoc2R6RnRsM1Q4RTVEVXlWQTh2emxxbDRTQnZhUTJudWJKK1JGdFVid1hWK2Q0b0NPQm9QSWs4TVBtK1JSNGtWRVRhaVRoclNmZFlOZHJNSEpoT241RUNaUjVnN21XeUUwbERzVlJkV0JKbUxQdEp5ZytSWTQ0TW1oNjVnR1NOdTI1OFN3SURBUUFC&query=0&msgVer=3&timestamp=1755683607232&signature=dJbUCzv8jL2ndV5xx1lr5I7jQaVAR5wYzNmQjmFjRUlmAuvRZahQTVZv6nhO3B3XrXYUVs04/T+wGLWKCJKCiAlNed0his7hH8ARtFGGGmz2wQYakelhVfIdDiJPcpePTelEPX8y2Ac36XEMw37vb87vRPGJok52HpPTe/kckmM=
2025-08-20 17:53:27.273  2795-2795  WebSocket               com.dspread.mdm.service              I  🔧 未检测到系统代理设置
2025-08-20 17:53:27.425  2795-3058  TrafficStats            com.dspread.mdm.service              D  tagSocket(92) with statsTag=0xffffffff, statsUid=-1
2025-08-20 17:53:27.674  2795-3059  TrafficStats            com.dspread.mdm.service              D  tagSocket(95) with statsTag=0xffffffff, statsUid=-1
2025-08-20 17:53:29.142  2795-3060  Common                  com.dspread.mdm.service              I  ✅ WebSocket 物理连接成功，等待服务器 hellMsg
2025-08-20 17:53:29.151  2795-3060  WebSocket               com.dspread.mdm.service              I  🔧 ===== 心跳发送器实际配置 =====
2025-08-20 17:53:29.162  2795-3060  WebSocket               com.dspread.mdm.service              I  🔧 实际 PING 发送间隔: 30000ms (30秒)
2025-08-20 17:53:29.171  2795-3060  WebSocket               com.dspread.mdm.service              I  🔧 PING 定时器名称: PingSender
2025-08-20 17:53:29.179  2795-3060  WebSocket               com.dspread.mdm.service              I  🔧 实际 PONG 发送间隔: 0ms (0秒)
2025-08-20 17:53:29.187  2795-3060  WebSocket               com.dspread.mdm.service              I  🔧 PONG 定时器名称: PongSender
2025-08-20 17:53:29.194  2795-3060  WebSocket               com.dspread.mdm.service              I  🔧 ===============================
2025-08-20 17:53:29.200  2795-3060  WebSocket               com.dspread.mdm.service              I  🔧 启动PING/PONG检测，阈值: 连续3个PING无PONG响应
2025-08-20 17:53:29.403  2795-3060  Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"hellMsg":"hello 01354090202503050399","tranCode":"S0000","version":"1","rebootTime":"02:47:07","serialNo":"01354090202503050399","deviceStatus":"6"}
2025-08-20 17:53:29.411  2795-3060  Common                  com.dspread.mdm.service              I  ✅ 收到服务器 hellMsg，WebSocket 业务连接成功！
2025-08-20 17:53:29.418  2795-3060  Common                  com.dspread.mdm.service              I  ✅ WebSocket 业务连接成功！
2025-08-20 17:53:29.425  2795-3060  WebSocket               com.dspread.mdm.service              I  🔧 业务连接成功，重连计数已重置为 0
2025-08-20 17:53:29.432  2795-3060  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接成功，检查自身更新任务状态
2025-08-20 17:53:29.440  2795-3060  Receiver                com.dspread.mdm.service              D  🔧 发送广播: com.dspread.mdm.service.CHECK_SELF_UPDATE_STATUS
2025-08-20 17:53:29.446  2795-3060  WebSocket               com.dspread.mdm.service              I  🔧 开始发送缓存消息，数量: 1
2025-08-20 17:53:29.456  2795-3060  WebSocket               com.dspread.mdm.service              I  🔧 C0902 主动式上送: cached_message_resend (主动: 1)
2025-08-20 17:53:29.490  2795-3060  Common                  com.dspread.mdm.service              I  ✅ 缓存消息发送成功: C0902
2025-08-20 17:53:29.999  2795-3060  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接成功
2025-08-20 17:53:30.006  2795-3060  WebSocket               com.dspread.mdm.service              I  🔧 设备状态: 6
2025-08-20 17:53:30.013  2795-3060  WebSocket               com.dspread.mdm.service              I  🔧 检查首次连接标志: isFirstTimeWebSocketConnected=true
2025-08-20 17:53:30.019  2795-3060  WebSocket               com.dspread.mdm.service              I  🔧 首次WebSocket连接成功，发送初始化信息并执行初始化检查
2025-08-20 17:53:30.025  2795-3060  WebSocket               com.dspread.mdm.service              I  🔧 开始发送初始化信息（带流量控制）
2025-08-20 17:53:30.031  2795-3060  WebSocket               com.dspread.mdm.service              I  🔧 C0901 被动式上送: first_connection (被动: 2)
2025-08-20 17:53:30.047  2795-3060  Platform                com.dspread.mdm.service              D  🔧 重新收集应用信息
2025-08-20 17:53:30.154  2795-3060  AppManager              com.dspread.mdm.service              I  ℹ️ 应用信息获取完成: 总数131(系统129/用户2) 返回2个
2025-08-20 17:53:30.173  2795-3060  Platform                com.dspread.mdm.service              D  🔧 应用信息: 2 个应用
2025-08-20 17:53:30.209  2795-3060  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0901","serialNo":"01354090202503050399","request_time":"1755683610179","request_id":"1755683610179C0901","version":"1","data":{"apkInfo":[{"packName":"com.dspread.mdm.service","apkName":"service","versionCode":22,"versionName":"1.1.03.20250820.DSPREAD.MDM.SERVICE","updateDate":"2025-08-20 17:49:20"},{"packName":"mark.via","apkName":"Via","versionCode":20250117,"versionName":"6.2.0","updateDate":"2025-08-18 17:50:20"}],"sytemInfo":{"androidVersion":"14","buildNumber":"BASE_D30M-MU_GEN_AP_V1.0.8.1_202507032000_ota_test","aspl":"2025-03-05"},"serviceInfo":[]},"myVersionName":"1.1.03.20250820.DSPREAD.MDM.SERVICE","terminalDate":"20250820175330"}
2025-08-20 17:53:30.215  2795-3060  WebSocket               com.dspread.mdm.service              I  🔧 C0901 应用信息上送成功 (1)
2025-08-20 17:53:30.533  2795-2795  Receiver                com.dspread.mdm.service              D  🔧 网络恢复，重新上传网络状态信息（防抖通过）
2025-08-20 17:53:30.553  2795-2795  WebSocket               com.dspread.mdm.service              I  🔧 C0904 被动式上送拒绝: NETWORK_RECOVERY (拒绝: 1)
2025-08-20 17:53:30.561  2795-2795  WebSocket               com.dspread.mdm.service              I  🔧 C0904 网络状态上送被流量控制阻止: 被动事件 'NETWORK_RECOVERY' 在平衡模式 - 重要变化下未启用
2025-08-20 17:53:31.223  2795-3060  WebSocket               com.dspread.mdm.service              I  🔧 C0902 被动式上送: first_connection (被动: 3)
2025-08-20 17:53:31.272  2795-3060  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0902","serialNo":"01354090202503050399","request_time":"1755683611247","request_id":"1755683611247C0902","version":"1","data":{"batteryLife":95,"batteryHealth":2,"temprature":"28.3","isCharging":"1","isLowBattery":"0","powerOff":"0","outOfBattery":"0"},"myVersionName":"1.1.03.20250820.DSPREAD.MDM.SERVICE","terminalDate":"20250820175331"}
2025-08-20 17:53:31.283  2795-3060  WebSocket               com.dspread.mdm.service              I  🔧 C0902 电池状态上送成功 (1, trigger=first_connection)
2025-08-20 17:53:31.297  2795-2842  System                  com.dspread.mdm.service              W  A resource failed to call close. 
2025-08-20 17:53:32.296  2795-3060  WebSocket               com.dspread.mdm.service              I  🔧 C0903 被动式上送: first_connection (被动: 4)
2025-08-20 17:53:32.471  2795-3060  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0903","serialNo":"01354090202503050399","request_time":"1755683612431","request_id":"1755683612431C0903","version":"1","data":{"hardwareInfo":[{"name":"CPU Usage","desc":"cpu:0.50"},{"name":"storage_usage_status","desc":" 4.56GB \/ 32.00GB ","state":"1"},{"name":"total_memory","desc":" 0.74GB \/ 1.86GB ","state":"1"},{"name":"androidVersion","desc":"14","state":"1"},{"name":"network_status","desc":"wifi","state":"1"},{"name":"OSVer","desc":"14","state":"1"}],"locationInfo":{"gps":{"longitude":"-999.0","latitude":"-999.0","satelliteCount":0,"satelliteCountInUsed":0,"satelliteAverSNR":"0.00","distance":"0.0","lockStatus":"1","lockMeter":"200","proId":"","proName":""},"baseSite":{"MCC":"","MNC":"","CID":"-1","LAC":"-1","DBM":"-999","IMSI":"","ICCID":""},"wifi":{"SSID":"fubox_2.4G","IP":"**************","MAC":"BE:87:C2:42:DF:75","SSTH":"-32","BSSID":"7c:10:c9:d6:9f:10"},"address":"无法获取"}},"myVersionName":"1.1.03.20250820.DSPREAD.MDM.SERVICE","terminalDate":"20250820175332"}
2025-08-20 17:53:32.478  2795-3060  WebSocket               com.dspread.mdm.service              I  🔧 C0903 数据信息上送成功 (1)
2025-08-20 17:53:33.485  2795-3060  WebSocket               com.dspread.mdm.service              I  🔧 C0904 被动式上送: first_connection (被动: 5)
2025-08-20 17:53:33.602  2795-3060  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0904","serialNo":"01354090202503050399","request_time":"1755683613559","request_id":"1755683613559C0904","version":"1","data":{"wifiOption":[{"SSID":"fubox_5G","SSTH":"-44"},{"SSID":"2306","SSTH":"-72"},{"SSID":"2205","SSTH":"-46"},{"SSID":"2205_5G","SSTH":"-59"},{"SSID":"fubox_2.4G","SSTH":"-33"},{"SSID":"DIRECT-CC-HP Laser 1188w","SSTH":"-71"},{"SSID":"2206","SSTH":"-22"},{"SSID":"2103","SSTH":"-75"},{"SSID":"2206-5G","SSTH":"-33"},{"SSID":"DIRECT-C6-Mi All-in-One Inkjet","SSTH":"-45"},{"SSID":"jibao","SSTH":"-85"},{"SSID":"2207-5G","SSTH":"-80"},{"SSID":"ChinaNet-ce2Z","SSTH":"-79"},{"SSID":"2103_5G","SSTH":"-84"},{"SSID":"@Ruijie-1816","SSTH":"-54"}],"baseSite":{"MCC":"","MNC":"","CID":"-1","LAC":"-1","DBM":"-999","IMSI":"","ICCID":""},"wifi":{"SSID":"fubox_2.4G","IP":"**************","MAC":"BE:87:C2:42:DF:75","SSTH":"-32","BSSID":"7c:10:c9:d6:9f:10"}},"myVersionName":"1.1.03.20250820.DSPREAD.MDM.SERVICE","terminalDate":"20250820175333"}
2025-08-20 17:53:33.609  2795-3060  WebSocket               com.dspread.mdm.service              I  🔧 C0904 网络状态上送成功 (1)
2025-08-20 17:53:34.616  2795-3060  WebSocket               com.dspread.mdm.service              I  🔧 C0906 被动式上送: first_connection (被动: 6)
2025-08-20 17:53:34.649  2795-3060  Platform                com.dspread.mdm.service              D  🔧 SpVersionApi 解析SP版本: V1.0.5
2025-08-20 17:53:34.657  2795-3060  Platform                com.dspread.mdm.service              D  🔧 DspreadService 首次获取SP版本成功: V1.0.5 (原始: *SP_VERSION:V1.0.5#*COMMIT:2d7cc90#*DATETIME:20250...)
2025-08-20 17:53:34.665  2795-3060  Platform                com.dspread.mdm.service              D  🔧 SpVersionApi 通过DspreadService获取SP版本: V1.0.5
2025-08-20 17:53:34.699  2795-3060  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0906","serialNo":"01354090202503050399","request_time":"1755683614680","request_id":"1755683614680C0906","version":"1","data":{"firmWareInfo":{"spfw":"V1.0.5"},"imei_1":"864177060143618","imei_2":"","wifi_mac":"be:87:c2:42:df:75","bt_mac":"","bsn":""},"myVersionName":"1.1.03.20250820.DSPREAD.MDM.SERVICE","terminalDate":"20250820175334"}
2025-08-20 17:53:34.705  2795-3060  WebSocket               com.dspread.mdm.service              I  🔧 C0906 设备信息上送成功 (1)
2025-08-20 17:53:34.712  2795-3060  WebSocket               com.dspread.mdm.service              I  🔧 C0109 被动式上送: first_connection (被动: 7)
2025-08-20 17:53:34.728  2795-3060  Platform                com.dspread.mdm.service              D  🔧 重新收集应用信息（强制刷新）
2025-08-20 17:53:34.815  2795-3060  AppManager              com.dspread.mdm.service              I  ℹ️ 应用信息获取完成: 总数131(系统129/用户2) 返回2个
2025-08-20 17:53:34.829  2795-3060  Platform                com.dspread.mdm.service              D  🔧 应用信息: 2 个应用
2025-08-20 17:53:34.979  2795-3060  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0109","serialNo":"01354090202503050399","request_time":"1755683614915","request_id":"1755683614915C0109","version":"1","data":{"apkInfo":[{"packName":"com.dspread.mdm.service","apkName":"service","versionCode":22,"versionName":"1.1.03.20250820.DSPREAD.MDM.SERVICE","updateDate":"2025-08-20 17:49:20"},{"packName":"mark.via","apkName":"Via","versionCode":20250117,"versionName":"6.2.0","updateDate":"2025-08-18 17:50:20"}],"locationInfo":{"gps":{"longitude":"-999.0","latitude":"-999.0","satelliteCount":0,"satelliteCountInUsed":0,"satelliteAverSNR":"0.00","distance":"0.0","lockStatus":"1","lockMeter":"200","proId":"","proName":""},"baseSite":{"MCC":"","MNC":"","CID":"-1","LAC":"-1","DBM":"-999","IMSI":"","ICCID":""},"wifi":{"SSID":"fubox_2.4G","IP":"**************","MAC":"BE:87:C2:42:DF:75","SSTH":"-32","BSSID":"7c:10:c9:d6:9f:10"},"address":"无法获取"},"hardwareInfo":[{"name":"CPU Usage","desc":"cpu:0.50"},{"name":"storage_usage_status","desc":" 4.56GB \/ 32.00GB ","state":"1"},{"name":"total_memory","desc":" 0.73GB \/ 1.86GB ","state":"1"},{"name":"androidVersion","desc":"14","state":"1"},{"name":"network_status","desc":"wifi","state":"1"},{"name":"OSVer","desc":"14","state":"1"}],"wifiOption":[{"SSID":"fubox_5G","SSTH":"-44"},{"SSID":"2306","SSTH":"-72"},{"SSID":"2205","SSTH":"-46"},{"SSID":"2205_5G","SSTH":"-59"},{"SSID":"fubox_2.4G","SSTH":"-33"},{"SSID":"DIRECT-CC-HP Laser 1188w","SSTH":"-71"},{"SSID":"2206","SSTH":"-22"},{"SSID":"2103","SSTH":"-75"},{"SSID":"2206-5G","SSTH":"-33"},{"SSID":"DIRECT-C6-Mi All-in-One Inkjet","SSTH":"-45"},{"SSID":"jibao","SSTH":"-85"},{"SSID":"2207-5G","SSTH":"-80"},{"SSID":"ChinaNet-ce2Z","SSTH":"-79"},{"SSID":"2103_5G","SSTH":"-84"},{"SSID":"@Ruijie-1816","SSTH":"-54"}],"sytemInfo":{"androidVersion":"14","buildNumber":"BASE_D30M-MU_GEN_AP_V1.0.8.1_202507032000_ota_test","aspl":"2025-03-05"}},"myVersionName":"1.1.03.20250820.DSPREAD.MDM.SERVICE","terminalDate":"20250820175334"}
2025-08-20 17:53:34.986  2795-3060  WebSocket               com.dspread.mdm.service              I  🔧 C0109 终端信息上传完成 (1, trigger=first_connection)
2025-08-20 17:53:34.992  2795-3060  Common                  com.dspread.mdm.service              I  ✅ 初始化信息发送完成
2025-08-20 17:53:34.999  2795-3060  WebSocket               com.dspread.mdm.service              I  🔧 检查服务更新结果
2025-08-20 17:53:35.005  2795-3060  Task                    com.dspread.mdm.service              W  ⚠️ 任务列表为空，跳过服务更新结果检查
2025-08-20 17:53:35.011  2795-3060  WebSocket               com.dspread.mdm.service              I  🔧 检查OS升级状态...
2025-08-20 17:53:35.020  2795-3060  Common                  com.dspread.mdm.service              D  🔧 OsUpdateStatusChecker WebSocket连接成功，检查OS升级状态
2025-08-20 17:53:35.026  2795-3060  Common                  com.dspread.mdm.service              D  🔧 OsUpdateStatusChecker 没有待检查的OS升级任务
2025-08-20 17:53:35.032  2795-3060  WebSocket               com.dspread.mdm.service              I  🔧 OS升级状态检查已触发
2025-08-20 17:53:35.038  2795-3060  WebSocket               com.dspread.mdm.service              I  🔧 首次连接初始化检查完成
2025-08-20 17:53:35.365  2795-3060  Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"request_time":"1755683615602","org_request_time":"1755683614680","org_request_id":"1755683614680C0906","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755683615602S0000","serialNo":"01354090202503050399"}
2025-08-20 17:53:35.375  2795-3060  WebSocket               com.dspread.mdm.service              I  🔧 收到响应消息: transCode=S0000, orgRequestId=1755683614680C0906, state=0, remark=
2025-08-20 17:53:35.381  2795-3060  WebSocket               com.dspread.mdm.service              I  🔧 处理设备信息响应: state=0
2025-08-20 17:53:35.388  2795-3060  WebSocket               com.dspread.mdm.service              I  🔧 设备信息上传成功
2025-08-20 17:53:35.510  2795-3060  Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"request_time":"1755683615948","org_request_time":"1755683614915","org_request_id":"1755683614915C0109","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755683615948S0000","serialNo":"01354090202503050399"}
2025-08-20 17:53:35.520  2795-3060  WebSocket               com.dspread.mdm.service              I  🔧 收到响应消息: transCode=S0000, orgRequestId=1755683614915C0109, state=0, remark=
2025-08-20 17:53:35.526  2795-3060  WebSocket               com.dspread.mdm.service              I  🔧 终端信息上传成功
