---------------------------- PROCESS ENDED (4108) for package com.dspread.mdm.service ----------------------------
2025-08-20 18:26:50.992   646-1923  BufferQueueDebug        surfaceflinger                       E  [ActivityRecord{b5cea58 u0 com.dspread.mdm.service/.ui.activity.TestActivity#360](this:0xa58d1c40,id:-1,api:0,p:-1,c:-1) id info cannot be read from 'ActivityRecord{b5cea58 u0 com.dspread.mdm.service/.ui.activity.TestActivity#360'
2025-08-20 18:26:51.016   646-1923  BufferQueueDebug        surfaceflinger                       E  [ec2f422 Splash Screen com.dspread.mdm.service#361](this:0xa5836c40,id:-1,api:0,p:-1,c:-1) id info cannot be read from 'ec2f422 Splash Screen com.dspread.mdm.service#361'
2025-08-20 18:26:51.026   646-1322  BufferQueueDebug        surfaceflinger                       E  [Splash Screen com.dspread.mdm.service#362](this:0xa5832c40,id:-1,api:0,p:-1,c:-1) id info cannot be read from 'Splash Screen com.dspread.mdm.service#362'
2025-08-20 18:26:51.070   646-939   BufferQueueDebug        surfaceflinger                       E  [cd6e33b ActivityRecordInputSink com.dspread.mdm.service/.ui.activity.TestActivity#366](this:0xa57fec40,id:-1,api:0,p:-1,c:-1) id info cannot be read from 'cd6e33b ActivityRecordInputSink com.dspread.mdm.service/.ui.activity.TestActivity#366'
---------------------------- PROCESS STARTED (4396) for package com.dspread.mdm.service ----------------------------
2025-08-20 18:26:51.214  4396-4396  ziparchive              com.dspread.mdm.service              W  Unable to open '/data/app/~~ULPBmDaJu2Mi8GgugZvNDg==/com.dspread.mdm.service-85uSUexrzCbaBI1Iez9VlA==/base.dm': No such file or directory
2025-08-20 18:26:51.636  4396-4396  nativeloader            com.dspread.mdm.service              D  Configuring clns-4 for other apk /data/app/~~ULPBmDaJu2Mi8GgugZvNDg==/com.dspread.mdm.service-85uSUexrzCbaBI1Iez9VlA==/base.apk. target_sdk_version=34, uses_libraries=, library_path=/data/app/~~ULPBmDaJu2Mi8GgugZvNDg==/com.dspread.mdm.service-85uSUexrzCbaBI1Iez9VlA==/lib/arm, permitted_path=/data:/mnt/expand:/data/user/0/com.dspread.mdm.service
2025-08-20 18:26:51.662  4396-4396  GraphicsEnvironment     com.dspread.mdm.service              V  Currently set values for:
2025-08-20 18:26:51.663  4396-4396  GraphicsEnvironment     com.dspread.mdm.service              V    angle_gl_driver_selection_pkgs=[]
2025-08-20 18:26:51.663  4396-4396  GraphicsEnvironment     com.dspread.mdm.service              V    angle_gl_driver_selection_values=[]
2025-08-20 18:26:51.664  4396-4396  GraphicsEnvironment     com.dspread.mdm.service              V  ANGLE GameManagerService for com.dspread.mdm.service: false
2025-08-20 18:26:51.664  4396-4396  GraphicsEnvironment     com.dspread.mdm.service              V  com.dspread.mdm.service is not listed in per-application setting
2025-08-20 18:26:51.665  4396-4396  GraphicsEnvironment     com.dspread.mdm.service              V  Neither updatable production driver nor prerelease driver is supported.
2025-08-20 18:26:51.692  4396-4396  Compatibil...geReporter com.dspread.mdm.service              D  Compat change id reported: 183155436; UID 1000; state: ENABLED
2025-08-20 18:26:51.739  4396-4396  MbrainDebugManagerImpl  com.dspread.mdm.service              D  getService failed
2025-08-20 18:26:51.797  4396-4396  Common                  com.dspread.mdm.service              D  🔧 日志配置：生产环境模式
2025-08-20 18:26:51.808  4396-4396  Common                  com.dspread.mdm.service              D  🔧 [DebugConfig] 调试模式已禁用
2025-08-20 18:26:51.814  4396-4396  Common                  com.dspread.mdm.service              D  🔧 SmartMdmServiceApp: 统一配置管理器初始化完成
2025-08-20 18:26:51.820  4396-4396  Common                  com.dspread.mdm.service              D  🔧 SmartMdmServiceApp: 系统目录初始化完成 - /data/pos/config/
2025-08-20 18:26:51.869  4396-4396  Receiver                com.dspread.mdm.service              D  🔧 ServiceGuardEventHandler 启动服务守护定时器: 120s
2025-08-20 18:26:51.877  4396-4396  Compatibil...geReporter com.dspread.mdm.service              D  Compat change id reported: 160794467; UID 1000; state: ENABLED
2025-08-20 18:26:51.882  4396-4396  Common                  com.dspread.mdm.service              I  ✅ ⏰ 设置服务守护定时器成功，下次执行: 120秒后 (2分钟)
2025-08-20 18:26:51.888  4396-4396  Common                  com.dspread.mdm.service              D  🔧 SmartMdmServiceApp: 服务守护定时器启动成功
2025-08-20 18:26:51.898  4396-4396  Common                  com.dspread.mdm.service              D  🔧 ServiceStartupManager 收到服务启动请求: APPLICATION_STARTUP, 强制启动: false
2025-08-20 18:26:51.909  4396-4396  Common                  com.dspread.mdm.service              D  🔧 ServiceStartupManager 开始执行服务启动: APPLICATION_STARTUP
2025-08-20 18:26:51.915  4396-4396  Common                  com.dspread.mdm.service              D  🔧 ServiceManager: 准备启动后台服务
2025-08-20 18:26:51.922  4396-4396  ContextImpl             com.dspread.mdm.service              W  Calling a method in the system process without a qualified user: android.app.ContextImpl.startService:1899 android.content.ContextWrapper.startService:825 com.dspread.mdm.service.platform.manager.ServiceManager.startBackgroundService:20 com.dspread.mdm.service.platform.manager.ServiceStartupManager.performServiceStart:121 com.dspread.mdm.service.platform.manager.ServiceStartupManager.startService:66 
2025-08-20 18:26:51.933  4396-4396  Common                  com.dspread.mdm.service              D  🔧 ServiceManager: 后台服务启动成功，ComponentName: ComponentInfo{com.dspread.mdm.service/com.dspread.mdm.service.services.SmartMdmBackgroundService}
2025-08-20 18:26:52.943  4396-4396  Common                  com.dspread.mdm.service              D  🔧 ServiceStartupManager 服务启动验证成功: APPLICATION_STARTUP
2025-08-20 18:26:52.949  4396-4396  Common                  com.dspread.mdm.service              D  🔧 ServiceStartupManager 服务启动成功: APPLICATION_STARTUP
2025-08-20 18:26:52.955  4396-4396  Common                  com.dspread.mdm.service              D  🔧 SmartMdmServiceApp: 主服务启动请求已提交
2025-08-20 18:26:52.961  4396-4396  Common                  com.dspread.mdm.service              D  🔧 SmartMdmServiceApp: Application启动完成，包名: com.dspread.mdm.service
2025-08-20 18:26:52.975  4396-4396  M-ProMotion             com.dspread.mdm.service              I  M-ProMotion is disabled
2025-08-20 18:26:53.043  4396-4396  Compatibil...geReporter com.dspread.mdm.service              D  Compat change id reported: 210923482; UID 1000; state: ENABLED
2025-08-20 18:26:53.077  4396-4396  Compatibil...geReporter com.dspread.mdm.service              D  Compat change id reported: 171228096; UID 1000; state: ENABLED
2025-08-20 18:26:53.134  4396-4396  getAnimDeal             com.dspread.mdm.service              I  mode:3 packageName:com.dspread.mdm.service result: false
2025-08-20 18:26:53.142  4396-4422  PowerHalWrapper         com.dspread.mdm.service              I  PowerHalWrapper.getInstance 
2025-08-20 18:26:53.142  4396-4396  SurfaceFactory          com.dspread.mdm.service              I  [static] sSurfaceFactory = com.mediatek.view.impl.SurfaceFactoryImpl@728e83d
2025-08-20 18:26:53.148  4396-4396  Compatibil...geReporter com.dspread.mdm.service              D  Compat change id reported: 237531167; UID 1000; state: DISABLED
2025-08-20 18:26:53.153  4396-4396  OpenGLRenderer          com.dspread.mdm.service              W  Unknown dataspace 0
2025-08-20 18:26:53.158  4396-4396  VRI[TestActivity]       com.dspread.mdm.service              D  hardware acceleration = true, forceHwAccelerated = false
2025-08-20 18:26:53.163   646-938   BufferQueueDebug        surfaceflinger                       E  [fae84ef com.dspread.mdm.service/com.dspread.mdm.service.ui.activity.TestActivity#368](this:0xa58d6c40,id:-1,api:0,p:-1,c:-1) id info cannot be read from 'fae84ef com.dspread.mdm.service/com.dspread.mdm.service.ui.activity.TestActivity#368'
2025-08-20 18:26:53.172  4396-4396  InputTransport          com.dspread.mdm.service              D  Create ARC handle: 0xb1b84680
2025-08-20 18:26:53.172  4396-4396  InputEventReceiver      com.dspread.mdm.service              D  Input log is disabled in InputEventReceiver.
2025-08-20 18:26:53.172  4396-4396  InputTransport          com.dspread.mdm.service              D  Input log is disabled in InputChannel.
2025-08-20 18:26:53.176  4396-4396  Choreographer           com.dspread.mdm.service              I  Skipped 84 frames!  The application may be doing too much work on its main thread.
2025-08-20 18:26:53.221   646-938   BufferQueueDebug        surfaceflinger                       E  [com.dspread.mdm.service/com.dspread.mdm.service.ui.activity.TestActivity#369](this:0xa58d4c40,id:-1,api:0,p:-1,c:-1) id info cannot be read from 'com.dspread.mdm.service/com.dspread.mdm.service.ui.activity.TestActivity#369'
2025-08-20 18:26:53.235  4396-4396  BufferQueueConsumer     com.dspread.mdm.service              D  [](id:112c00000000,api:0,p:-1,c:4396) connect: controlledByApp=false
2025-08-20 18:26:53.270  4396-4423  OpenGLRenderer          com.dspread.mdm.service              E  Unable to match the desired swap behavior.
2025-08-20 18:26:53.279  4396-4427  ion                     com.dspread.mdm.service              E  ioctl c0044901 failed with code -1: Invalid argument
2025-08-20 18:26:53.387  4396-4423  BLASTBufferQueue        com.dspread.mdm.service              D  [VRI[TestActivity]#0](f:0,a:1) acquireNextBufferLocked size=480x854 mFrameNumber=1 applyTransaction=true mTimestamp=2082584806508(auto) mPendingTransactions.size=0 graphicBufferId=18880676233221 transform=3
2025-08-20 18:26:53.393   646-938   BufferQueueDebug        surfaceflinger                       E  [Surface(name=fae84ef com.dspread.mdm.service/com.dspread.mdm.service.ui.activity.TestActivity)/@0x5335d3d - animation-leash of starting_reveal#372](this:0xa582cc40,id:-1,api:0,p:-1,c:-1) id info cannot be read from 'Surface(name=fae84ef com.dspread.mdm.service/com.dspread.mdm.service.ui.activity.TestActivity)/@0x5335d3d - animation-leash of starting_reveal#372'
2025-08-20 18:26:53.403  4396-4396  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 服务启动
2025-08-20 18:26:53.410  4396-4411  OpenGLRenderer          com.dspread.mdm.service              I  Davey! duration=1627ms; Flags=1, FrameTimelineVsyncId=83606, IntendedVsync=2080957008828, Vsync=2082359659980, InputEventId=0, HandleInputStart=2082376081046, AnimationStart=2082376104661, PerformTraversalsStart=2082376747431, DrawStart=2082469322508, FrameDeadline=2080982508828, FrameInterval=2082374781661, FrameStartTime=16698228, SyncQueued=2082483768431, SyncStart=2082486262200, IssueDrawCommandsStart=2082486685585, SwapBuffers=2082583476123, FrameCompleted=2082586568046, DequeueBufferDuration=0, QueueBufferDuration=1150539, GpuCompleted=2082586568046, SwapBuffersCompleted=2082586347738, DisplayPresentTime=8320773541883110929, CommandSubmissionCompleted=2082583476123, 
2025-08-20 18:26:53.436  4396-4396  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 开始服务初始化
2025-08-20 18:26:53.443  4396-4396  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] Android 14+: 初始化DSPREAD服务
2025-08-20 18:26:53.451  4396-4396  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 服务创建完成
2025-08-20 18:26:53.451  4396-4433  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 后台线程：初始化DSPREAD服务
2025-08-20 18:26:53.463  4396-4433  Platform                com.dspread.mdm.service              D  🔧 DspreadService 开始初始化DSPREAD服务
2025-08-20 18:26:53.466  4396-4396  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] onStartCommand调用
2025-08-20 18:26:53.473  4396-4433  ContextImpl             com.dspread.mdm.service              W  Calling a method in the system process without a qualified user: android.app.ContextImpl.bindService:1988 android.content.ContextWrapper.bindService:861 com.dspread.mdm.service.services.DspreadService.bindSysService:111 com.dspread.mdm.service.services.DspreadService.initialize:63 com.dspread.mdm.service.services.SmartMdmBackgroundService.initializeDspreadServiceAsync$lambda$1:227 
2025-08-20 18:26:53.479  4396-4396  Platform                com.dspread.mdm.service              D  🔧 WakeLockManager 获取智能WakeLock成功，持续时间: 60000ms
2025-08-20 18:26:53.488  4396-4433  Platform                com.dspread.mdm.service              D  🔧 DspreadService 系统服务绑定请求已发送
2025-08-20 18:26:53.494  4396-4396  Platform                com.dspread.mdm.service              D  🔧 WakeLockManager 设置WakeLock续期定时器: 60000ms
2025-08-20 18:26:53.537  4396-4396  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 初始化基础组件
2025-08-20 18:26:53.547  4396-4396  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 开始初始化...
2025-08-20 18:26:53.564  4396-4396  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 包更新广播接收器注册完成
2025-08-20 18:26:53.572  4396-4396  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 统一广播接收器注册完成
2025-08-20 18:26:53.578  4396-4396  Common                  com.dspread.mdm.service              I  ✅ BroadcastManager 初始化完成
2025-08-20 18:26:53.640  4396-4434  Platform                com.dspread.mdm.service              D  🔧 DeviceInfoApi 单例实例已创建
2025-08-20 18:26:53.646  4396-4396  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 服务初始化完成
2025-08-20 18:26:53.664  4396-4396  Platform                com.dspread.mdm.service              D  🔧 DspreadService 系统服务连接成功: ComponentInfo{com.dspread.sdkservice/com.dspread.sdkservice.SDKSysService}
2025-08-20 18:26:53.671  4396-4396  Platform                com.dspread.mdm.service              D  🔧 DspreadService 系统服务绑定完成
2025-08-20 18:26:53.680  4396-4433  ContextImpl             com.dspread.mdm.service              W  Calling a method in the system process without a qualified user: android.app.ContextImpl.bindService:1988 android.content.ContextWrapper.bindService:861 com.dspread.mdm.service.services.DspreadService.bindDevService:166 com.dspread.mdm.service.services.DspreadService.initialize:66 com.dspread.mdm.service.services.SmartMdmBackgroundService.initializeDspreadServiceAsync$lambda$1:227 
2025-08-20 18:26:53.685  4396-4433  Platform                com.dspread.mdm.service              D  🔧 DspreadService 设备服务绑定请求已发送
2025-08-20 18:26:53.689  4396-4396  Receiver                com.dspread.mdm.service              D  🔧 电池状态变化: 电量=100%, 温度=28°C, 充电=true
2025-08-20 18:26:53.725  4396-4396  Provisioning            com.dspread.mdm.service              I  ℹ️ ProvisioningManager初始化完成
2025-08-20 18:26:53.742  4396-4396  Provisioning            com.dspread.mdm.service              D  🔧 状态标志加载成功: /data/pos/config/provisioning_flags.json
2025-08-20 18:26:53.765  4396-4396  WebSocket               com.dspread.mdm.service              I  🔧 C0902 被动式上送: battery_level_change_5 (被动: 1)
2025-08-20 18:26:53.785  4396-4396  Platform                com.dspread.mdm.service              E  ❌ DspreadService 设备服务不可用，无法获取序列号
2025-08-20 18:26:53.805   646-1322  BufferQueueDebug        surfaceflinger                       E  [Surface(name=ec2f422 Splash Screen com.dspread.mdm.service)/@0x57126cf - animation-leash of window_animation#374](this:0xa56f0c40,id:-1,api:0,p:-1,c:-1) id info cannot be read from 'Surface(name=ec2f422 Splash Screen com.dspread.mdm.service)/@0x57126cf - animation-leash of window_animation#374'
2025-08-20 18:26:53.812  4396-4396  WebSocket               com.dspread.mdm.service              I  🔧 消息已缓存: C0902 (缓存数量: 1)
2025-08-20 18:26:53.819  4396-4396  WebSocket               com.dspread.mdm.service              I  🔧 C0902 电池状态上送成功 (1, trigger=battery_level_change_5)
2025-08-20 18:26:53.829  4396-4396  Platform                com.dspread.mdm.service              D  🔧 NetworkApi 单例实例已创建
2025-08-20 18:26:53.838  4396-4396  Receiver                com.dspread.mdm.service              D  🔧 网络状态变化: 连接=false, 类型=NONE
2025-08-20 18:26:53.845  4396-4396  Receiver                com.dspread.mdm.service              D  🔧 网络不可用，WebSocket连接可能受影响
2025-08-20 18:26:53.851  4396-4396  Receiver                com.dspread.mdm.service              D  🔧 ServiceManagementEventHandler 处理网络变化事件
2025-08-20 18:26:53.861  4396-4396  Common                  com.dspread.mdm.service              D  🔧 ServiceStartupManager 服务正在运行，无需启动: NETWORK_CHANGE
2025-08-20 18:26:53.867  4396-4396  Receiver                com.dspread.mdm.service              D  🔧 ServiceManagementEventHandler 网络变化后服务检查完成
2025-08-20 18:26:53.874  4396-4396  Provisioning            com.dspread.mdm.service              D  🔧 ProvisioningEventHandler 网络状态变化
2025-08-20 18:26:53.880  4396-4396  Provisioning            com.dspread.mdm.service              D  🔧 ProvisioningEventHandler 忽略初始启动时的网络连接事件
2025-08-20 18:26:53.896  4396-4396  Platform                com.dspread.mdm.service              D  🔧 DspreadService 设备服务连接成功: ComponentInfo{com.dspread.sdkservice/com.dspread.sdkservice.SDKDevService}
2025-08-20 18:26:53.904  4396-4396  Platform                com.dspread.mdm.service              D  🔧 DspreadService 设备服务绑定完成
2025-08-20 18:26:54.000  4396-4396  WebSocket               com.dspread.mdm.service              I  🔧 C0905 主动式上送: manual_trigger (主动: 1)
2025-08-20 18:26:54.028  4396-4396  WebSocket               com.dspread.mdm.service              I  🔧 首次上送，上送当日数据
2025-08-20 18:26:54.035  4396-4396  WebSocket               com.dspread.mdm.service              I  🔧 没有流量数据需要上送
2025-08-20 18:26:54.040  4396-4396  Common                  com.dspread.mdm.service              D  🔧 刷新时自动上送流量统计
2025-08-20 18:26:54.090  4396-4434  Platform                com.dspread.mdm.service              D  🔧 SpVersionApi 解析SP版本: V1.0.5
2025-08-20 18:26:54.115  4396-4434  Platform                com.dspread.mdm.service              D  🔧 DspreadService 首次获取SP版本成功: V1.0.5 (原始: *SP_VERSION:V1.0.5#*COMMIT:2d7cc90#*DATETIME:20250...)
2025-08-20 18:26:54.184  4396-4396  Common                  com.dspread.mdm.service              D  🔧 TestActivity UI已更新: SP=V1.0.5, SN=01354090202503050399
2025-08-20 18:26:54.912  4396-4433  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 等待DSPREAD服务就绪 (1/10): true
2025-08-20 18:26:54.917  4396-4433  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] DSPREAD服务就绪，启动Provisioning
2025-08-20 18:26:54.923  4396-4433  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 验证DSPREAD服务功能
2025-08-20 18:26:54.929  4396-4433  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 设备序列号验证: 成功(01354090202503050399)
2025-08-20 18:26:54.935  4396-4433  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 服务状态 - 系统服务:✅ 设备服务:✅
2025-08-20 18:26:54.941  4396-4444  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 启动ProvisioningService
2025-08-20 18:26:54.950  4396-4444  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] ProvisioningService启动完成
2025-08-20 18:26:54.950  4396-4434  Provisioning            com.dspread.mdm.service              I  ℹ️ 开始执行Provisioning配置，触发类型: FIRST_BOOT
2025-08-20 18:26:54.957  4396-4434  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] Provisioning进度: 正在下载配置文件...
2025-08-20 18:26:54.963  4396-4434  Provisioning            com.dspread.mdm.service              I  ℹ️ 开始请求远程配置...
2025-08-20 18:26:54.970  4396-4434  Provisioning            com.dspread.mdm.service              I  ℹ️ 请求配置: https://config.dspreadserv.net/status/config?SN=MDEzNTQwOTAyMDI1MDMwNTAzOTk=&timestamp=1755685614969
2025-08-20 18:26:54.997  4396-4434  Provisioning            com.dspread.mdm.service              E  ❌ 配置API请求失败
2025-08-20 18:26:55.011  4396-4434  Provisioning            com.dspread.mdm.service              W  ⚠️ 远程配置请求失败: Unable to resolve host "config.dspreadserv.net": No address associated with hostname
2025-08-20 18:26:55.026  4396-4434  Provisioning            com.dspread.mdm.service              I  ℹ️ 📂 使用本地保存的配置 - CID: 1001
2025-08-20 18:26:55.034  4396-4434  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] Provisioning进度: 正在下载 Logo...
2025-08-20 18:26:55.111  4396-4434  Provisioning            com.dspread.mdm.service              I  ℹ️ 文件已存在且MD5匹配，跳过下载: Logo
2025-08-20 18:26:55.117  4396-4434  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] Provisioning进度: 正在下载 BootAnimation...
2025-08-20 18:26:55.191  4396-4434  Provisioning            com.dspread.mdm.service              I  ℹ️ 文件已存在且MD5匹配，跳过下载: BootAnimation
2025-08-20 18:26:55.198  4396-4434  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] Provisioning进度: 正在应用配置...
2025-08-20 18:26:55.203  4396-4434  Provisioning            com.dspread.mdm.service              I  ℹ️ 配置应用完成
2025-08-20 18:26:55.209  4396-4434  Provisioning            com.dspread.mdm.service              W  ⚠️ 远程配置失败，首次配置状态保持未完成，下次启动将重试
2025-08-20 18:26:55.218  4396-4434  Provisioning            com.dspread.mdm.service              D  🔧 状态标志保存成功: /data/pos/config/provisioning_flags.json
2025-08-20 18:26:55.223  4396-4434  Provisioning            com.dspread.mdm.service              I  ℹ️ ProvisioningManager: 初始启动标记完成，网络重连重试功能已激活
2025-08-20 18:26:55.229  4396-4434  Provisioning            com.dspread.mdm.service              I  ℹ️ Provisioning配置执行完成
2025-08-20 18:26:55.234  4396-4434  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] Provisioning完成，启动核心组件
2025-08-20 18:26:55.254  4396-4396  WebSocket               com.dspread.mdm.service              I  🔧 开始初始化 WebSocket 组件...
2025-08-20 18:26:55.260  4396-4396  WebSocket               com.dspread.mdm.service              I  🔧 初始化 WebSocket 密钥管理器
2025-08-20 18:26:55.266  4396-4396  WebSocket               com.dspread.mdm.service              I  🔧 生成客户端公私钥对
2025-08-20 18:26:55.393  4396-4396  Common                  com.dspread.mdm.service              I  ✅ WebSocket 密钥管理器初始化完成
2025-08-20 18:26:55.403  4396-4396  WebSocket               com.dspread.mdm.service              I  🔧 开始初始化 WsMessageCenter...
2025-08-20 18:26:55.408  4396-4396  WebSocket               com.dspread.mdm.service              I  🔧 WsMessageCenter 首次初始化
2025-08-20 18:26:55.428  4396-4396  WebSocket               com.dspread.mdm.service              I  🔧 WsTaskManager 初始化完成
2025-08-20 18:26:55.530  4396-4396  WebSocket               com.dspread.mdm.service              I  🔧 WsMessageCenter 初始化完成
2025-08-20 18:26:55.536  4396-4396  WebSocket               com.dspread.mdm.service              I  🔧 开始清理终态任务，当前任务数量: 0
2025-08-20 18:26:55.541  4396-4396  WebSocket               com.dspread.mdm.service              I  🔧 没有需要清理的终态任务
2025-08-20 18:26:55.546  4396-4396  WebSocket               com.dspread.mdm.service              I  🔧 开始重置任务状态，当前任务个数: 0
2025-08-20 18:26:55.553  4396-4396  WebSocket               com.dspread.mdm.service              I  🔧 没有需要重置的任务
2025-08-20 18:26:55.559  4396-4396  Common                  com.dspread.mdm.service              I  ✅ 任务状态恢复完成
2025-08-20 18:26:55.576  4396-4396  WebSocket               com.dspread.mdm.service              I  🔧 使用Provisioning配置的WebSocket URL: wss://api.dspreadserv.net/status/websocket/register
2025-08-20 18:26:55.582  4396-4396  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接管理器初始化完成
2025-08-20 18:26:55.588  4396-4396  Common                  com.dspread.mdm.service              I  ✅ WebSocket 管理器初始化完成
2025-08-20 18:26:55.595  4396-4396  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 管理器初始化完成
2025-08-20 18:26:55.601  4396-4396  Common                  com.dspread.mdm.service              I  ✅ WebSocket 组件初始化完成
2025-08-20 18:26:55.606  4396-4396  WebSocket               com.dspread.mdm.service              I  🔧 启动 WebSocket 连接...
2025-08-20 18:26:55.612  4396-4396  WebSocket               com.dspread.mdm.service              I  🔧 开始连接 WebSocket...
2025-08-20 18:26:55.632  4396-4396  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接到: wss://api.dspreadserv.net/status/websocket/register?serialNo=MDEzNTQwOTAyMDI1MDMwNTAzOTk=&publicKey=TUlHZk1BMEdDU3FHU0liM0RRRUJBUVVBQTRHTkFEQ0JpUUtCZ1FENjVsY3lJc2p6b0swWFpMZ2RUQmpOaCtKVDhpdGNlUHp1eVVMUGVyS281VzRxWjZRUy9QZS9zWU1PdG04RzhoZk5pSzZEN0xKQnZ2V243RW1nVTdVM2x3YjR4VXI3SS84V1hFZEFBWW1WNVlTdzdOenBQYkFaTUxSNVFmazBiK2lvcDZxaTlkeFpTazFsOGhlL0E3ZEV2MC92ZnNmRTU4a2k5UzRLVVRMVUhRSURBUUFC&query=1&msgVer=3&timestamp=1755685615617&signature=nf36dSb/bWj2nSeMmdAPmQz7r9/GoEEm6NA72cWrbrEoDM4gZyY70vTyipuUaeApYPVbsxR40ggZX7E0yCpQdsP/b3VgTBZNJusPpuQt4oXs5/NpTUVxi+gM20sGevbVfLBPsVIevIPf/Qo7lm/7LCR4DFpa9fAenr+BHUOxD4w=
2025-08-20 18:26:55.641  4396-4396  WebSocket               com.dspread.mdm.service              I  🔧 未检测到系统代理设置
2025-08-20 18:26:55.687  4396-4396  WebSocket               com.dspread.mdm.service              I  🔧 ===== WebSocket 库默认配置 =====
2025-08-20 18:26:55.692  4396-4396  WebSocket               com.dspread.mdm.service              I  🔧 默认 PING 间隔: 30000ms (30秒)
2025-08-20 18:26:55.698  4396-4396  WebSocket               com.dspread.mdm.service              I  🔧 默认 PONG 间隔: 0ms (0秒)
2025-08-20 18:26:55.703  4396-4396  WebSocket               com.dspread.mdm.service              I  🔧 帧队列大小: 5
2025-08-20 18:26:55.709  4396-4396  WebSocket               com.dspread.mdm.service              I  🔧 连接超时: 15000ms (15秒)
2025-08-20 18:26:55.715  4396-4396  WebSocket               com.dspread.mdm.service              I  🔧 允许缺失关闭帧: false
2025-08-20 18:26:55.720  4396-4396  WebSocket               com.dspread.mdm.service              I  🔧 ================================
2025-08-20 18:26:55.726  4396-4396  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接请求已发送
2025-08-20 18:26:55.732  4396-4396  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接启动完成
2025-08-20 18:26:55.732  4396-4451  WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误
2025-08-20 18:26:55.738  4396-4396  Common                  com.dspread.mdm.service              D  🔧 [ModuleManagerRegistry] 模块管理器注册中心初始化完成
2025-08-20 18:26:55.739  4396-4451  WebSocket               com.dspread.mdm.service              I  🔧 onError: 不触发重连，等待 onDisconnected 处理
2025-08-20 18:26:55.744  4396-4451  WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误
2025-08-20 18:26:55.745  4396-4396  Provisioning            com.dspread.mdm.service              D  🔧 ProvisioningEventHandler 启动Provisioning定时器: 43200s (12小时)
2025-08-20 18:26:55.746  4396-4434  Common                  com.dspread.mdm.service              D  🔧 [ModuleManagerRegistry] 启动所有模块
2025-08-20 18:26:55.752  4396-4451  WebSocket               com.dspread.mdm.service              I  🔧 reconnect() 被调用，调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onConnectError:672
2025-08-20 18:26:55.755  4396-4396  Common                  com.dspread.mdm.service              I  ✅ ⏰ 设置初始化定时器成功，下次执行: 43200秒后 (12小时)
2025-08-20 18:26:55.758  4396-4451  WebSocket               com.dspread.mdm.service              I  🔧 开始重连 WebSocket... (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onConnectError:672)
2025-08-20 18:26:55.759  4396-4434  Common                  com.dspread.mdm.service              D  🔧 Module LogStream Manager status changed to: INITIALIZED
2025-08-20 18:26:55.766  4396-4434  Common                  com.dspread.mdm.service              D  🔧 Module LogStream Manager status changed to: STARTING
2025-08-20 18:26:55.773  4396-4451  WebSocket               com.dspread.mdm.service              I  🔧 使用Provisioning配置的重连策略: delayPolicy=1, delaySwitch=1, delayTime=60
2025-08-20 18:26:55.774  4396-4396  Common                  com.dspread.mdm.service              I  ✅ ⏰ 设置心跳定时器成功，下次执行: 300秒后 (5分钟)
2025-08-20 18:26:55.779  4396-4451  WebSocket               com.dspread.mdm.service              I  🔧 当前断开记录数: 0, 延迟开关: 1
2025-08-20 18:26:55.784  4396-4396  Common                  com.dspread.mdm.service              I  ✅ ⏰ 设置任务执行定时器成功，下次执行: 60秒后 (1分钟)
2025-08-20 18:26:55.785  4396-4451  WebSocket               com.dspread.mdm.service              I  🔧 重连策略计算完成: 第1次重连，间隔3000ms (3秒)
2025-08-20 18:26:55.791  4396-4451  WebSocket               com.dspread.mdm.service              I  🔧 开始第1次重连，间隔3000ms (3秒) (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onConnectError:672)
2025-08-20 18:26:55.803  4396-4396  Common                  com.dspread.mdm.service              I  ✅ ⏰ 设置终端信息定时器成功，下次执行: 900秒后 (15分钟)
2025-08-20 18:26:55.828  4396-4396  Common                  com.dspread.mdm.service              D  🔧 TimerConfig ========== 当前定时器配置 (生产模式) ==========
2025-08-20 18:26:55.834  4396-4396  Common                  com.dspread.mdm.service              D  🔧 TimerConfig ①心跳定时器: 300秒
2025-08-20 18:26:55.840  4396-4396  Common                  com.dspread.mdm.service              D  🔧 TimerConfig ②终端信息上传定时器: 900秒
2025-08-20 18:26:55.846  4396-4396  Common                  com.dspread.mdm.service              D  🔧 TimerConfig ③任务执行定时器: 60秒
2025-08-20 18:26:55.852  4396-4396  Common                  com.dspread.mdm.service              D  🔧 TimerConfig ④服务守护定时器: 120秒
2025-08-20 18:26:55.858  4396-4434  Common                  com.dspread.mdm.service              D  🔧 Module LogStream Manager status changed to: RUNNING
2025-08-20 18:26:55.858  4396-4396  Common                  com.dspread.mdm.service              D  🔧 TimerConfig ⑤初始化定时器: 43200秒
2025-08-20 18:26:55.864  4396-4396  Common                  com.dspread.mdm.service              D  🔧 TimerConfig ===============================================
2025-08-20 18:26:55.867  4396-4434  Common                  com.dspread.mdm.service              D  🔧 [ModuleManagerRegistry] LogStreamManager启动成功
2025-08-20 18:26:55.870  4396-4396  Common                  com.dspread.mdm.service              I  ✅ [SmartMdmBackgroundService] 所有定时器启动完成
2025-08-20 18:26:55.876  4396-4396  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 核心组件初始化完成
2025-08-20 18:26:55.892  4396-4434  Compatibil...geReporter com.dspread.mdm.service              D  Compat change id reported: 263076149; UID 1000; state: ENABLED
2025-08-20 18:26:55.902  4396-4434  Common                  com.dspread.mdm.service              D  🔧 Module Geofence Manager status changed to: INITIALIZED
2025-08-20 18:26:55.909  4396-4434  Common                  com.dspread.mdm.service              D  🔧 Module Geofence Manager status changed to: STARTING
2025-08-20 18:26:55.933  4396-4434  Platform                com.dspread.mdm.service              D  🔧 用户交互监控已启动（简化版本）
2025-08-20 18:26:55.939  4396-4434  Platform                com.dspread.mdm.service              D  🔧 使用模拟用户交互检测
2025-08-20 18:26:55.945  4396-4434  Platform                com.dspread.mdm.service              D  🔧 用户交互监控已启动
2025-08-20 18:26:55.951  4396-4434  Service                 com.dspread.mdm.service              D  🔧 [GeofenceManager]Constants.geofenceStatus = 1
2025-08-20 18:26:55.958  4396-4434  Service                 com.dspread.mdm.service              D  🔧 [GeofenceManager]-- Constants.storeId =  -- Constants.storeSsid =  -- Constants.storeIp = 
2025-08-20 18:26:55.966  4396-4434  Common                  com.dspread.mdm.service              D  🔧 Module Geofence Manager status changed to: RUNNING
2025-08-20 18:26:55.973  4396-4434  Common                  com.dspread.mdm.service              D  🔧 [ModuleManagerRegistry] GeofenceManager启动成功
2025-08-20 18:26:55.991  4396-4434  RuleBase                com.dspread.mdm.service              D  🔧 RuleBaseManager 开始初始化RuleBaseManager...
2025-08-20 18:26:56.002  4396-4434  RuleBase                com.dspread.mdm.service              D  🔧 RuleBaseStorage RuleBase存储管理器初始化完成
2025-08-20 18:26:56.009  4396-4434  RuleBase                com.dspread.mdm.service              D  🔧 RuleStateMonitor 状态监控器初始化完成
2025-08-20 18:26:56.015  4396-4434  RuleBase                com.dspread.mdm.service              D  🔧 RuleBaseManager RuleBaseManager初始化完成
2025-08-20 18:26:56.022  4396-4434  Common                  com.dspread.mdm.service              D  🔧 [ModuleManagerRegistry] RuleBaseManager启动成功
2025-08-20 18:26:56.028  4396-4434  Common                  com.dspread.mdm.service              D  🔧 [ModuleManagerRegistry] 所有模块启动完成
2025-08-20 18:26:58.683  4396-4463  ProfileInstaller        com.dspread.mdm.service              D  Installing profile for com.dspread.mdm.service
2025-08-20 18:26:58.814  4396-4396  WebSocket               com.dspread.mdm.service              I  🔧 重连 WebSocket: wss://api.dspreadserv.net/status/websocket/register?serialNo=MDEzNTQwOTAyMDI1MDMwNTAzOTk=&publicKey=TUlHZk1BMEdDU3FHU0liM0RRRUJBUVVBQTRHTkFEQ0JpUUtCZ1FENjVsY3lJc2p6b0swWFpMZ2RUQmpOaCtKVDhpdGNlUHp1eVVMUGVyS281VzRxWjZRUy9QZS9zWU1PdG04RzhoZk5pSzZEN0xKQnZ2V243RW1nVTdVM2x3YjR4VXI3SS84V1hFZEFBWW1WNVlTdzdOenBQYkFaTUxSNVFmazBiK2lvcDZxaTlkeFpTazFsOGhlL0E3ZEV2MC92ZnNmRTU4a2k5UzRLVVRMVUhRSURBUUFC&query=0&msgVer=3&timestamp=1755685618796&signature=OC+SdNF//6EWjo+Ah4W0FjJKRAtBlz8Zs4x2701TILSv+6mV4HEil35yfrRf5y/q1uM8x3gGcQipErr0XDa4J3PXnrF1j+ssXFYXdcFcLhHpQmV4RSs1gCWFxka7LtTB6KKffKjM3TCKSZ8/cBX2MRLAWscviTw08MoEB3Qu7Vs=
2025-08-20 18:26:58.822  4396-4396  WebSocket               com.dspread.mdm.service              I  🔧 未检测到系统代理设置
2025-08-20 18:26:58.865  4396-4464  WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误
2025-08-20 18:26:58.872  4396-4464  WebSocket               com.dspread.mdm.service              I  🔧 onError: 不触发重连，等待 onDisconnected 处理
2025-08-20 18:26:58.878  4396-4464  WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误
2025-08-20 18:26:58.885  4396-4464  WebSocket               com.dspread.mdm.service              I  🔧 reconnect() 被调用，调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onConnectError:672
2025-08-20 18:26:58.891  4396-4464  WebSocket               com.dspread.mdm.service              I  🔧 开始重连 WebSocket... (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onConnectError:672)
2025-08-20 18:26:58.906  4396-4464  WebSocket               com.dspread.mdm.service              I  🔧 使用Provisioning配置的重连策略: delayPolicy=1, delaySwitch=1, delayTime=60
2025-08-20 18:26:58.911  4396-4464  WebSocket               com.dspread.mdm.service              I  🔧 当前断开记录数: 0, 延迟开关: 1
2025-08-20 18:26:58.917  4396-4464  WebSocket               com.dspread.mdm.service              I  🔧 重连策略计算完成: 第2次重连，间隔3000ms (3秒)
2025-08-20 18:26:58.923  4396-4464  WebSocket               com.dspread.mdm.service              I  🔧 开始第2次重连，间隔3000ms (3秒) (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onConnectError:672)
2025-08-20 18:27:01.958  4396-4396  WebSocket               com.dspread.mdm.service              I  🔧 重连 WebSocket: wss://api.dspreadserv.net/status/websocket/register?serialNo=MDEzNTQwOTAyMDI1MDMwNTAzOTk=&publicKey=TUlHZk1BMEdDU3FHU0liM0RRRUJBUVVBQTRHTkFEQ0JpUUtCZ1FENjVsY3lJc2p6b0swWFpMZ2RUQmpOaCtKVDhpdGNlUHp1eVVMUGVyS281VzRxWjZRUy9QZS9zWU1PdG04RzhoZk5pSzZEN0xKQnZ2V243RW1nVTdVM2x3YjR4VXI3SS84V1hFZEFBWW1WNVlTdzdOenBQYkFaTUxSNVFmazBiK2lvcDZxaTlkeFpTazFsOGhlL0E3ZEV2MC92ZnNmRTU4a2k5UzRLVVRMVUhRSURBUUFC&query=0&msgVer=3&timestamp=1755685621930&signature=rgsDufBrYRPNlXT/Bm47YR/8j/9RbkcFtBxF96ZgKTGE0NLqbkklMifXf+w5o2AOG9iq7P7Slz/Tgs3VbFF8W9LDHvqRCOAvyeHYfJGEw0hJw3z7xTMWgVS7il0FSWpolubb4oI73kBmGTpb183I9lL6e3ahLG3N2X17dAZ0LPU=
2025-08-20 18:27:01.971  4396-4396  WebSocket               com.dspread.mdm.service              I  🔧 未检测到系统代理设置
2025-08-20 18:27:02.030  4396-4466  WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误
2025-08-20 18:27:02.038  4396-4466  WebSocket               com.dspread.mdm.service              I  🔧 onError: 不触发重连，等待 onDisconnected 处理
2025-08-20 18:27:02.044  4396-4466  WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误
2025-08-20 18:27:02.051  4396-4466  WebSocket               com.dspread.mdm.service              I  🔧 reconnect() 被调用，调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onConnectError:672
2025-08-20 18:27:02.057  4396-4466  WebSocket               com.dspread.mdm.service              I  🔧 开始重连 WebSocket... (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onConnectError:672)
2025-08-20 18:27:02.072  4396-4466  WebSocket               com.dspread.mdm.service              I  🔧 使用Provisioning配置的重连策略: delayPolicy=1, delaySwitch=1, delayTime=60
2025-08-20 18:27:02.078  4396-4466  WebSocket               com.dspread.mdm.service              I  🔧 当前断开记录数: 0, 延迟开关: 1
2025-08-20 18:27:02.084  4396-4466  WebSocket               com.dspread.mdm.service              I  🔧 重连策略计算完成: 第3次重连，间隔3000ms (3秒)
2025-08-20 18:27:02.090  4396-4466  WebSocket               com.dspread.mdm.service              I  🔧 开始第3次重连，间隔3000ms (3秒) (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onConnectError:672)
2025-08-20 18:27:04.079  4396-4396  ScrollIdentify          com.dspread.mdm.service              I  on fling
2025-08-20 18:27:05.111  4396-4396  WebSocket               com.dspread.mdm.service              I  🔧 重连 WebSocket: wss://api.dspreadserv.net/status/websocket/register?serialNo=MDEzNTQwOTAyMDI1MDMwNTAzOTk=&publicKey=TUlHZk1BMEdDU3FHU0liM0RRRUJBUVVBQTRHTkFEQ0JpUUtCZ1FENjVsY3lJc2p6b0swWFpMZ2RUQmpOaCtKVDhpdGNlUHp1eVVMUGVyS281VzRxWjZRUy9QZS9zWU1PdG04RzhoZk5pSzZEN0xKQnZ2V243RW1nVTdVM2x3YjR4VXI3SS84V1hFZEFBWW1WNVlTdzdOenBQYkFaTUxSNVFmazBiK2lvcDZxaTlkeFpTazFsOGhlL0E3ZEV2MC92ZnNmRTU4a2k5UzRLVVRMVUhRSURBUUFC&query=0&msgVer=3&timestamp=1755685625097&signature=Fk2+PBUkh2CfaTxe5CAjU04jce+gNFAeQWUZ2OvLBJ4QDdiv+L2VVQa9lAf+IYWTDjYzm6aeOnr7xf4JaEKEQ7Wqde5VLmDTx11rBiSokz4Jiw5lVFERlekJ0Sj0vM1X7quw7DuNRl8UEH54R+YDJzaCCPTurCt3/uAKb5Xu1iI=
2025-08-20 18:27:05.118  4396-4396  WebSocket               com.dspread.mdm.service              I  🔧 未检测到系统代理设置
2025-08-20 18:27:05.156  4396-4472  WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误
2025-08-20 18:27:05.165  4396-4472  WebSocket               com.dspread.mdm.service              I  🔧 onError: 不触发重连，等待 onDisconnected 处理
2025-08-20 18:27:05.173  4396-4472  WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误
2025-08-20 18:27:05.182  4396-4472  WebSocket               com.dspread.mdm.service              I  🔧 reconnect() 被调用，调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onConnectError:672
2025-08-20 18:27:05.188  4396-4472  WebSocket               com.dspread.mdm.service              I  🔧 开始重连 WebSocket... (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onConnectError:672)
2025-08-20 18:27:05.205  4396-4472  WebSocket               com.dspread.mdm.service              I  🔧 使用Provisioning配置的重连策略: delayPolicy=1, delaySwitch=1, delayTime=60
2025-08-20 18:27:05.212  4396-4472  WebSocket               com.dspread.mdm.service              I  🔧 当前断开记录数: 0, 延迟开关: 1
2025-08-20 18:27:05.219  4396-4472  WebSocket               com.dspread.mdm.service              I  🔧 重连策略计算完成: 第4次重连，间隔6000ms (6秒)
2025-08-20 18:27:05.226  4396-4472  WebSocket               com.dspread.mdm.service              I  🔧 开始第4次重连，间隔6000ms (6秒) (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onConnectError:672)
2025-08-20 18:27:09.010  4396-4396  Platform                com.dspread.mdm.service              D  🔧 [WiFiManagerApi] WiFiManagerApi 单例实例已创建
2025-08-20 18:27:09.133  4396-4396  Receiver                com.dspread.mdm.service              D  🔧 网络状态变化: 连接=true, 类型=WIFI
2025-08-20 18:27:09.141  4396-4396  Receiver                com.dspread.mdm.service              D  🔧 网络恢复，尝试重新连接WebSocket
2025-08-20 18:27:09.150  4396-4396  Receiver                com.dspread.mdm.service              D  🔧 ServiceManagementEventHandler 处理网络变化事件
2025-08-20 18:27:09.165  4396-4396  Common                  com.dspread.mdm.service              D  🔧 ServiceStartupManager 服务正在运行，无需启动: NETWORK_CHANGE
2025-08-20 18:27:09.174  4396-4396  Receiver                com.dspread.mdm.service              D  🔧 ServiceManagementEventHandler 网络变化后服务检查完成
2025-08-20 18:27:09.181  4396-4396  Provisioning            com.dspread.mdm.service              D  🔧 ProvisioningEventHandler 网络状态变化
2025-08-20 18:27:09.192  4396-4396  Provisioning            com.dspread.mdm.service              I  ℹ️ ProvisioningEventHandler 检测到网络重连，但Provisioning已完成，按正常定时器间隔检查更新
2025-08-20 18:27:11.249  4396-4396  WebSocket               com.dspread.mdm.service              I  🔧 重连 WebSocket: wss://api.dspreadserv.net/status/websocket/register?serialNo=MDEzNTQwOTAyMDI1MDMwNTAzOTk=&publicKey=TUlHZk1BMEdDU3FHU0liM0RRRUJBUVVBQTRHTkFEQ0JpUUtCZ1FENjVsY3lJc2p6b0swWFpMZ2RUQmpOaCtKVDhpdGNlUHp1eVVMUGVyS281VzRxWjZRUy9QZS9zWU1PdG04RzhoZk5pSzZEN0xKQnZ2V243RW1nVTdVM2x3YjR4VXI3SS84V1hFZEFBWW1WNVlTdzdOenBQYkFaTUxSNVFmazBiK2lvcDZxaTlkeFpTazFsOGhlL0E3ZEV2MC92ZnNmRTU4a2k5UzRLVVRMVUhRSURBUUFC&query=0&msgVer=3&timestamp=1755685631235&signature=RitR8rjpNQxry0ivWbCBxJClEeNm6E6az4m2xK4jNKifRI7IVTjHW4ooTtoAzsYp7e2Vhou+Ft/fgcnlyTtbyTUJ25rMDTGkgEKS4ig8jqO/+zmyfjX1/18tqr9gYxjrcyRZmsfXUjWjLy9nI5jaqds7c4HlrmtVsQVg3T9hmnA=
2025-08-20 18:27:11.256  4396-4396  WebSocket               com.dspread.mdm.service              I  🔧 未检测到系统代理设置
2025-08-20 18:27:11.302  4396-4510  TrafficStats            com.dspread.mdm.service              D  tagSocket(163) with statsTag=0xffffffff, statsUid=-1
2025-08-20 18:27:11.552  4396-4511  TrafficStats            com.dspread.mdm.service              D  tagSocket(175) with statsTag=0xffffffff, statsUid=-1
2025-08-20 18:27:12.908  4396-4519  Common                  com.dspread.mdm.service              I  ✅ WebSocket 物理连接成功，等待服务器 hellMsg
2025-08-20 18:27:12.918  4396-4519  WebSocket               com.dspread.mdm.service              I  🔧 ===== 心跳发送器实际配置 =====
2025-08-20 18:27:12.926  4396-4519  WebSocket               com.dspread.mdm.service              I  🔧 实际 PING 发送间隔: 30000ms (30秒)
2025-08-20 18:27:12.937  4396-4519  WebSocket               com.dspread.mdm.service              I  🔧 PING 定时器名称: PingSender
2025-08-20 18:27:12.953  4396-4519  WebSocket               com.dspread.mdm.service              I  🔧 实际 PONG 发送间隔: 0ms (0秒)
2025-08-20 18:27:12.960  4396-4519  WebSocket               com.dspread.mdm.service              I  🔧 PONG 定时器名称: PongSender
2025-08-20 18:27:12.967  4396-4519  WebSocket               com.dspread.mdm.service              I  🔧 ===============================
2025-08-20 18:27:12.974  4396-4519  WebSocket               com.dspread.mdm.service              I  🔧 启动PING/PONG检测，阈值: 连续3个PING无PONG响应
2025-08-20 18:27:12.996  4396-4518  Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"hellMsg":"hello 01354090202503050399","tranCode":"S0000","version":"1","rebootTime":"02:36:42","serialNo":"01354090202503050399","deviceStatus":"6"}
2025-08-20 18:27:13.003  4396-4518  Common                  com.dspread.mdm.service              I  ✅ 收到服务器 hellMsg，WebSocket 业务连接成功！
2025-08-20 18:27:13.010  4396-4518  Common                  com.dspread.mdm.service              I  ✅ WebSocket 业务连接成功！
2025-08-20 18:27:13.016  4396-4518  WebSocket               com.dspread.mdm.service              I  🔧 业务连接成功，重连计数已重置为 0
2025-08-20 18:27:13.022  4396-4518  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接成功，检查自身更新任务状态
2025-08-20 18:27:13.032  4396-4518  Receiver                com.dspread.mdm.service              D  🔧 发送广播: com.dspread.mdm.service.CHECK_SELF_UPDATE_STATUS
2025-08-20 18:27:13.038  4396-4518  WebSocket               com.dspread.mdm.service              I  🔧 开始发送缓存消息，数量: 1
2025-08-20 18:27:13.049  4396-4518  WebSocket               com.dspread.mdm.service              I  🔧 C0902 主动式上送: cached_message_resend (主动: 2)
2025-08-20 18:27:13.087  4396-4518  Common                  com.dspread.mdm.service              I  ✅ 缓存消息发送成功: C0902
2025-08-20 18:27:13.594  4396-4518  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接成功
2025-08-20 18:27:13.601  4396-4518  WebSocket               com.dspread.mdm.service              I  🔧 设备状态: 6
2025-08-20 18:27:13.608  4396-4518  WebSocket               com.dspread.mdm.service              I  🔧 检查首次连接标志: isFirstTimeWebSocketConnected=true
2025-08-20 18:27:13.616  4396-4518  WebSocket               com.dspread.mdm.service              I  🔧 首次WebSocket连接成功，发送初始化信息并执行初始化检查
2025-08-20 18:27:13.623  4396-4518  WebSocket               com.dspread.mdm.service              I  🔧 开始发送初始化信息（带流量控制）
2025-08-20 18:27:13.630  4396-4518  WebSocket               com.dspread.mdm.service              I  🔧 C0901 被动式上送: first_connection (被动: 2)
2025-08-20 18:27:13.647  4396-4518  Platform                com.dspread.mdm.service              D  🔧 重新收集应用信息
2025-08-20 18:27:13.748  4396-4518  AppManager              com.dspread.mdm.service              I  ℹ️ 应用信息获取完成: 总数131(系统129/用户2) 返回2个
2025-08-20 18:27:13.769  4396-4518  Platform                com.dspread.mdm.service              D  🔧 应用信息: 2 个应用
2025-08-20 18:27:13.801  4396-4518  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0901","serialNo":"01354090202503050399","request_time":"1755685633776","request_id":"1755685633776C0901","version":"1","data":{"apkInfo":[{"packName":"com.dspread.mdm.service","apkName":"service","versionCode":22,"versionName":"1.1.03.20250820.DSPREAD.MDM.SERVICE","updateDate":"2025-08-20 18:26:49"},{"packName":"mark.via","apkName":"Via","versionCode":20250117,"versionName":"6.2.0","updateDate":"2025-08-18 17:50:20"}],"sytemInfo":{"androidVersion":"14","buildNumber":"BASE_D30M-MU_GEN_AP_V1.0.8.1_202507032000_ota_test","aspl":"2025-03-05"},"serviceInfo":[]},"myVersionName":"1.1.03.20250820.DSPREAD.MDM.SERVICE","terminalDate":"20250820182713"}
2025-08-20 18:27:13.807  4396-4518  WebSocket               com.dspread.mdm.service              I  🔧 C0901 应用信息上送成功 (1)
2025-08-20 18:27:14.816  4396-4518  WebSocket               com.dspread.mdm.service              I  🔧 C0902 被动式上送: first_connection (被动: 3)
2025-08-20 18:27:14.851  4396-4518  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0902","serialNo":"01354090202503050399","request_time":"1755685634835","request_id":"1755685634835C0902","version":"1","data":{"batteryLife":100,"batteryHealth":2,"temprature":"28.6","isCharging":"1","isLowBattery":"0","powerOff":"0","outOfBattery":"0"},"myVersionName":"1.1.03.20250820.DSPREAD.MDM.SERVICE","terminalDate":"20250820182714"}
2025-08-20 18:27:14.858  4396-4518  WebSocket               com.dspread.mdm.service              I  🔧 C0902 电池状态上送成功 (1, trigger=first_connection)
2025-08-20 18:27:15.866  4396-4518  WebSocket               com.dspread.mdm.service              I  🔧 C0903 被动式上送: first_connection (被动: 4)
2025-08-20 18:27:16.015  4396-4518  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0903","serialNo":"01354090202503050399","request_time":"1755685635979","request_id":"1755685635979C0903","version":"1","data":{"hardwareInfo":[{"name":"CPU Usage","desc":"cpu:0.50"},{"name":"storage_usage_status","desc":" 4.58GB \/ 32.00GB ","state":"1"},{"name":"total_memory","desc":" 0.69GB \/ 1.86GB ","state":"1"},{"name":"androidVersion","desc":"14","state":"1"},{"name":"network_status","desc":"wifi","state":"1"},{"name":"OSVer","desc":"14","state":"1"}],"locationInfo":{"gps":{"longitude":"-999.0","latitude":"-999.0","satelliteCount":0,"satelliteCountInUsed":0,"satelliteAverSNR":"0.00","distance":"0.0","lockStatus":"1","lockMeter":"200","proId":"","proName":""},"baseSite":{"MCC":"","MNC":"","CID":"-1","LAC":"-1","DBM":"-999","IMSI":"","ICCID":""},"wifi":{"SSID":"fubox_2.4G","IP":"**************","MAC":"BE:87:C2:42:DF:75","SSTH":"-33","BSSID":"7c:10:c9:d6:9f:10"},"address":"无法获取"}},"myVersionName":"1.1.03.20250820.DSPREAD.MDM.SERVICE","terminalDate":"20250820182715"}
2025-08-20 18:27:16.022  4396-4518  WebSocket               com.dspread.mdm.service              I  🔧 C0903 数据信息上送成功 (1)
2025-08-20 18:27:17.028  4396-4518  WebSocket               com.dspread.mdm.service              I  🔧 C0904 被动式上送: first_connection (被动: 5)
2025-08-20 18:27:17.136  4396-4518  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0904","serialNo":"01354090202503050399","request_time":"1755685637100","request_id":"1755685637100C0904","version":"1","data":{"wifiOption":[{"SSID":"2306","SSTH":"-75"},{"SSID":"2205","SSTH":"-50"},{"SSID":"2205_5G","SSTH":"-59"},{"SSID":"dingjie888","SSTH":"-84"},{"SSID":"DIRECT-7D-HP Laser 136w","SSTH":"-57"},{"SSID":"fubox_2.4G","SSTH":"-33"},{"SSID":"2206","SSTH":"-33"},{"SSID":"2103","SSTH":"-63"},{"SSID":"2206-5G","SSTH":"-39"},{"SSID":"CMCC-EtAF","SSTH":"-90"},{"SSID":"DIRECT-C6-Mi All-in-One Inkjet","SSTH":"-42"},{"SSID":"kimberly","SSTH":"-79"},{"SSID":"2207-5G","SSTH":"-77"},{"SSID":"2207","SSTH":"-60"},{"SSID":"2103_5G","SSTH":"-82"},{"SSID":"@Ruijie-1816_5G","SSTH":"-67"},{"SSID":"FJQS","SSTH":"-80"},{"SSID":"@Ruijie-1816","SSTH":"-52"}],"baseSite":{"MCC":"","MNC":"","CID":"-1","LAC":"-1","DBM":"-999","IMSI":"","ICCID":""},"wifi":{"SSID":"fubox_2.4G","IP":"**************","MAC":"BE:87:C2:42:DF:75","SSTH":"-33","BSSID":"7c:10:c9:d6:9f:10"}},"myVersionName":"1.1.03.20250820.DSPREAD.MDM.SERVICE","terminalDate":"20250820182717"}
2025-08-20 18:27:17.142  4396-4518  WebSocket               com.dspread.mdm.service              I  🔧 C0904 网络状态上送成功 (1)
2025-08-20 18:27:18.149  4396-4518  WebSocket               com.dspread.mdm.service              I  🔧 C0906 被动式上送: first_connection (被动: 6)
2025-08-20 18:27:18.165  4396-4518  Platform                com.dspread.mdm.service              D  🔧 DspreadService 使用缓存的SP版本: V1.0.5
2025-08-20 18:27:18.172  4396-4518  Platform                com.dspread.mdm.service              D  🔧 SpVersionApi 通过DspreadService获取SP版本: V1.0.5
2025-08-20 18:27:18.200  4396-4518  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0906","serialNo":"01354090202503050399","request_time":"1755685638183","request_id":"1755685638183C0906","version":"1","data":{"firmWareInfo":{"spfw":"V1.0.5"},"imei_1":"864177060143618","imei_2":"","wifi_mac":"be:87:c2:42:df:75","bt_mac":"","bsn":""},"myVersionName":"1.1.03.20250820.DSPREAD.MDM.SERVICE","terminalDate":"20250820182718"}
2025-08-20 18:27:18.207  4396-4518  WebSocket               com.dspread.mdm.service              I  🔧 C0906 设备信息上送成功 (1)
2025-08-20 18:27:18.214  4396-4518  WebSocket               com.dspread.mdm.service              I  🔧 C0109 被动式上送: first_connection (被动: 7)
2025-08-20 18:27:18.230  4396-4518  Platform                com.dspread.mdm.service              D  🔧 重新收集应用信息（强制刷新）
2025-08-20 18:27:18.320  4396-4407  System                  com.dspread.mdm.service              W  A resource failed to call close. 
2025-08-20 18:27:18.321  4396-4518  AppManager              com.dspread.mdm.service              I  ℹ️ 应用信息获取完成: 总数131(系统129/用户2) 返回2个
2025-08-20 18:27:18.336  4396-4518  Platform                com.dspread.mdm.service              D  🔧 应用信息: 2 个应用
2025-08-20 18:27:18.497  4396-4518  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0109","serialNo":"01354090202503050399","request_time":"1755685638429","request_id":"1755685638429C0109","version":"1","data":{"apkInfo":[{"packName":"com.dspread.mdm.service","apkName":"service","versionCode":22,"versionName":"1.1.03.20250820.DSPREAD.MDM.SERVICE","updateDate":"2025-08-20 18:26:49"},{"packName":"mark.via","apkName":"Via","versionCode":20250117,"versionName":"6.2.0","updateDate":"2025-08-18 17:50:20"}],"locationInfo":{"gps":{"longitude":"-999.0","latitude":"-999.0","satelliteCount":0,"satelliteCountInUsed":0,"satelliteAverSNR":"0.00","distance":"0.0","lockStatus":"1","lockMeter":"200","proId":"","proName":""},"baseSite":{"MCC":"","MNC":"","CID":"-1","LAC":"-1","DBM":"-999","IMSI":"","ICCID":""},"wifi":{"SSID":"fubox_2.4G","IP":"**************","MAC":"BE:87:C2:42:DF:75","SSTH":"-33","BSSID":"7c:10:c9:d6:9f:10"},"address":"无法获取"},"hardwareInfo":[{"name":"CPU Usage","desc":"cpu:0.50"},{"name":"storage_usage_status","desc":" 4.58GB \/ 32.00GB ","state":"1"},{"name":"total_memory","desc":" 0.69GB \/ 1.86GB ","state":"1"},{"name":"androidVersion","desc":"14","state":"1"},{"name":"network_status","desc":"wifi","state":"1"},{"name":"OSVer","desc":"14","state":"1"}],"wifiOption":[{"SSID":"2306","SSTH":"-75"},{"SSID":"2205","SSTH":"-50"},{"SSID":"2205_5G","SSTH":"-59"},{"SSID":"dingjie888","SSTH":"-84"},{"SSID":"DIRECT-7D-HP Laser 136w","SSTH":"-57"},{"SSID":"fubox_2.4G","SSTH":"-33"},{"SSID":"2206","SSTH":"-33"},{"SSID":"2103","SSTH":"-63"},{"SSID":"2206-5G","SSTH":"-39"},{"SSID":"CMCC-EtAF","SSTH":"-90"},{"SSID":"DIRECT-C6-Mi All-in-One Inkjet","SSTH":"-42"},{"SSID":"kimberly","SSTH":"-79"},{"SSID":"2207-5G","SSTH":"-77"},{"SSID":"2207","SSTH":"-60"},{"SSID":"2103_5G","SSTH":"-82"},{"SSID":"@Ruijie-1816_5G","SSTH":"-67"},{"SSID":"FJQS","SSTH":"-80"},{"SSID":"@Ruijie-1816","SSTH":"-52"}],"sytemInfo":{"androidVersion":"14","buildNumber":"BASE_D30M-MU_GEN_AP_V1.0.8.1_202507032000_ota_test","aspl":"2025-03-05"}},"myVersionName":"1.1.03.20250820.DSPREAD.MDM.SERVICE","terminalDate":"20250820182718"}
2025-08-20 18:27:18.503  4396-4518  WebSocket               com.dspread.mdm.service              I  🔧 C0109 终端信息上传完成 (1, trigger=first_connection)
2025-08-20 18:27:18.509  4396-4518  Common                  com.dspread.mdm.service              I  ✅ 初始化信息发送完成
2025-08-20 18:27:18.516  4396-4518  WebSocket               com.dspread.mdm.service              I  🔧 检查服务更新结果
2025-08-20 18:27:18.522  4396-4518  Task                    com.dspread.mdm.service              W  ⚠️ 任务列表为空，跳过服务更新结果检查
2025-08-20 18:27:18.528  4396-4518  WebSocket               com.dspread.mdm.service              I  🔧 检查OS升级状态...
2025-08-20 18:27:18.536  4396-4518  Common                  com.dspread.mdm.service              D  🔧 OsUpdateStatusChecker WebSocket连接成功，检查OS升级状态
2025-08-20 18:27:18.542  4396-4518  Common                  com.dspread.mdm.service              D  🔧 OsUpdateStatusChecker 没有待检查的OS升级任务
2025-08-20 18:27:18.548  4396-4518  WebSocket               com.dspread.mdm.service              I  🔧 OS升级状态检查已触发
2025-08-20 18:27:18.555  4396-4518  WebSocket               com.dspread.mdm.service              I  🔧 首次连接初始化检查完成
2025-08-20 18:27:18.651  4396-4518  Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"request_time":"1755685639105","org_request_time":"1755685638183","org_request_id":"1755685638183C0906","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755685639105S0000","serialNo":"01354090202503050399"}
2025-08-20 18:27:18.661  4396-4518  WebSocket               com.dspread.mdm.service              I  🔧 收到响应消息: transCode=S0000, orgRequestId=1755685638183C0906, state=0, remark=
2025-08-20 18:27:18.668  4396-4518  WebSocket               com.dspread.mdm.service              I  🔧 处理设备信息响应: state=0
2025-08-20 18:27:18.674  4396-4518  WebSocket               com.dspread.mdm.service              I  🔧 设备信息上传成功
2025-08-20 18:27:19.146  4396-4518  Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"request_time":"1755685639448","org_request_time":"1755685638429","org_request_id":"1755685638429C0109","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755685639448S0000","serialNo":"01354090202503050399"}
2025-08-20 18:27:19.155  4396-4518  WebSocket               com.dspread.mdm.service              I  🔧 收到响应消息: transCode=S0000, orgRequestId=1755685638429C0109, state=0, remark=
2025-08-20 18:27:19.162  4396-4518  WebSocket               com.dspread.mdm.service              I  🔧 终端信息上传成功
2025-08-20 18:27:24.153  4396-4396  Receiver                com.dspread.mdm.service              D  🔧 网络恢复，重新上传网络状态信息（防抖通过）
2025-08-20 18:27:24.181  4396-4396  WebSocket               com.dspread.mdm.service              I  🔧 C0904 被动式上送拒绝: NETWORK_RECOVERY (拒绝: 1)
2025-08-20 18:27:24.193  4396-4396  WebSocket               com.dspread.mdm.service              I  🔧 C0904 网络状态上送被流量控制阻止: 被动事件 'NETWORK_RECOVERY' 在平衡模式 - 重要变化下未启用
