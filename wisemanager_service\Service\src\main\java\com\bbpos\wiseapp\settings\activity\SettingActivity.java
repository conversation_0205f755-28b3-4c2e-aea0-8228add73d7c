package com.bbpos.wiseapp.settings.activity;

import android.Manifest;
import android.app.Activity;
import android.app.AlarmManager;
import android.app.AlertDialog;
import android.app.PendingIntent;
import android.app.Service;
import android.content.ComponentName;
import android.content.DialogInterface;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.os.SystemClock;
import android.provider.Settings;
import androidx.annotation.NonNull;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;
import android.util.Log;
import android.view.View;
import android.view.WindowManager;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.SeekBar;
import android.widget.TextView;
import android.widget.Toast;

import com.bbpos.wiseapp.logger.BBLog;
import com.bbpos.wiseapp.security.SecurityOperate;
import com.bbpos.wiseapp.service.BuildConfig;
import com.bbpos.wiseapp.service.R;
import com.bbpos.wiseapp.service.receiver.LauncherDeamonReceiver;
import com.bbpos.wiseapp.settings.utils.HelperUtil;
import com.bbpos.wiseapp.system.api.DeviceInfoApi;
import com.bbpos.wiseapp.tms.adapter.SystemManagerAdapter;
import com.bbpos.wiseapp.utils.FileUtils;
import com.bbpos.wiseapp.websocket.WebSocketReceiver;

import java.io.File;

import static com.bbpos.wiseapp.service.receiver.LauncherDeamonReceiver.ACTION;

public class SettingActivity extends Activity implements View.OnClickListener{
    final private static int REQ_PERM_WRITE_STORAGE = 0;

    private ImageView iv_close;
    private SeekBar seekBar;
    private TextView curLanguage;
    private TextView tv_version;
    private LinearLayout ll_language;
    private LinearLayout ll_wifi;
    private LinearLayout ll_bluetooth;
    private long curTime;
    private int clickCounts;

    private AlarmManager alarmManager;
    private PendingIntent pendingIntent;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_setting);
        initView();

        applyForPermission();
//        setAlertManager();

        if(!com.bbpos.wiseapp.tms.utils.Constants.isActionServiceExist()) {
            Toast.makeText(this,getResources().getText(R.string.strServiceUninstall), Toast.LENGTH_LONG).show();
        } else {
            SystemManagerAdapter.bindServiceInit(getApplicationContext(), new SystemManagerAdapter.BindServiceSuccess() {

                @Override
                public void onBindSuccess() {
                    // TODO Auto-generated method stub

                }
            });
        }

//        Intent initTmsService = new Intent(this, InitializeService.class);
//        startService(initTmsService);
    }


    @Deprecated
    private void setAlertManager() {
        Intent i = new Intent(this, LauncherDeamonReceiver.class);
        i.setAction(ACTION);
        pendingIntent = PendingIntent.getBroadcast(this, 0, i,PendingIntent.FLAG_UPDATE_CURRENT);

        alarmManager =(AlarmManager)getSystemService(Service.ALARM_SERVICE);
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            alarmManager.setExactAndAllowWhileIdle(AlarmManager.ELAPSED_REALTIME_WAKEUP, SystemClock.elapsedRealtime()+ LauncherDeamonReceiver.TIME_INTERVAL, pendingIntent);
        } else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
            alarmManager.setExact(AlarmManager.ELAPSED_REALTIME_WAKEUP, SystemClock.elapsedRealtime()+ LauncherDeamonReceiver.TIME_INTERVAL, pendingIntent);
        } else {
            alarmManager.setRepeating(AlarmManager.ELAPSED_REALTIME_WAKEUP, SystemClock.elapsedRealtime(), LauncherDeamonReceiver.TIME_INTERVAL, pendingIntent);
        }
    }

    private void applyForPermission() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            //如果当前平台版本大于23平台
            if (ContextCompat.checkSelfPermission(this, Manifest.permission.WRITE_EXTERNAL_STORAGE) != PackageManager.PERMISSION_GRANTED
                    || ContextCompat.checkSelfPermission(this, Manifest.permission.WRITE_SETTINGS) != PackageManager.PERMISSION_GRANTED) {
                if (ActivityCompat.shouldShowRequestPermissionRationale(this, Manifest.permission.WRITE_EXTERNAL_STORAGE)) {
                    Toast.makeText(SettingActivity.this, "请至权限中心打开本应用的相机访问权限", Toast.LENGTH_LONG).show();
                }
                // 申请权限
                ActivityCompat.requestPermissions(SettingActivity.this, new String[]{Manifest.permission.WRITE_EXTERNAL_STORAGE, Manifest.permission.WRITE_SETTINGS}, REQ_PERM_WRITE_STORAGE);
            } else {
                registerSeekBar();
            }
        } else {
            registerSeekBar();
        }
    }

    private String getVersion() {
        String versionName = BuildConfig.VERSION_NAME;
        if (versionName.length() > 26) {
            String[] strlist = versionName.split("\\.");
            if (strlist.length > 3) {
                return "v" + strlist[0] + "." + strlist[1] + "." + strlist[2];
            }
        }

        return "v0.0.0";
    }

    private void initView() {
        TextView title = findViewById(R.id.toolbar_title_tv);
        title.setText(getString(R.string.settings));
        title.setOnClickListener(this);
        title.setOnLongClickListener(new View.OnLongClickListener() {
            @Override
            public boolean onLongClick(View v) {
                BBLog.i(BBLog.TAG, "SettingActivity, set Login success flag...");
                WebSocketReceiver.setLoginSuccessFlag();
                return false;
                /*
                AlertDialog.Builder builder = new AlertDialog.Builder(SettingActivity.this);
                builder.setTitle("请输入");
                final EditText et = new EditText(SettingActivity.this);
                et.setHint("请输入密码");
                et.setSingleLine(true);
                builder.setView(et);
                builder.setNegativeButton("取消",null);
                builder.setPositiveButton("确定", new DialogInterface.OnClickListener() {
                    @Override
                    public void onClick(DialogInterface dialog, int which) {
                        String password = et.getText().toString();
                        if (password.equals(SecurityOperate.getInstance().getSettingPassword(SettingActivity.this))) {
                            WebSocketReceiver.setLoginSuccessFlag();
                        }else{
                            Toast.makeText(SettingActivity.this, "密码错误", Toast.LENGTH_SHORT).show();
                        }
                    }
                });
                AlertDialog alertDialog = builder.create();
                alertDialog.show();
                return false;
                */
            }
        });

        iv_close = findViewById(R.id.toolbar_left_btn);
        iv_close.setOnClickListener(this);

        tv_version = findViewById(R.id.tv_version);
        tv_version.setText(getVersion());
        tv_version.setOnClickListener(this);

        //亮度设置
        seekBar = findViewById(R.id.sb_brightness);
        seekBar.setProgress(getScreenBrightness());

        //语言设置
        ll_language = findViewById(R.id.ll_language);
        ll_language.setOnClickListener(this);
        curLanguage = findViewById(R.id.tv_cur_language);

        String lang = HelperUtil.getCountryLanguage(SettingActivity.this);
        if ("en-US".equals(lang)) {
            curLanguage.setText(getString(R.string.english));
        } else if ("zh-CN".equals(lang)) {
            curLanguage.setText(getString(R.string.chinese_simplified));
        } else if ("zh-HK".equals(lang)) {
            curLanguage.setText(getString(R.string.chinese_traditional));
        }

        //網絡設置
        ll_wifi = findViewById(R.id.ll_wifi);
        ll_wifi.setOnClickListener(this);
        ll_bluetooth = findViewById(R.id.ll_bluetooth);
        ll_bluetooth.setOnClickListener(this);
    }

    private void registerSeekBar() {
        seekBar.setOnSeekBarChangeListener(new SeekBar.OnSeekBarChangeListener() {
            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                setScreenBrightness(seekBar.getProgress());
            }

            @Override
            public void onStartTrackingTouch(SeekBar seekBar) {

            }

            @Override
            public void onStopTrackingTouch(SeekBar seekBar) {

            }
        });
    }

    private int getScreenBrightness(){
        int screenBrightness = 255;
        try{
            screenBrightness = Settings.System.getInt(getContentResolver(), Settings.System.SCREEN_BRIGHTNESS);
        }
        catch (Exception localException){

        }
        return screenBrightness;
    }

    private void setScreenBrightness(int progress) {
        WindowManager.LayoutParams localLayoutParams = getWindow().getAttributes();
        float f = progress / 255.0F;
        localLayoutParams.screenBrightness = f;
        getWindow().setAttributes(localLayoutParams);
        //修改系统的亮度值,以至于退出应用程序亮度保持
        //改变系统的亮度值
        //这里需要权限android.permission.WRITE_SETTINGS
        //设置为手动调节模式
        Settings.System.putInt(getContentResolver(), Settings.System.SCREEN_BRIGHTNESS_MODE, Settings.System.SCREEN_BRIGHTNESS_MODE_MANUAL);
        //保存到系统中
        Uri uri = android.provider.Settings.System.getUriFor(Settings.System.SCREEN_BRIGHTNESS);
        android.provider.Settings.System.putInt(getContentResolver(), Settings.System.SCREEN_BRIGHTNESS, progress);
        getContentResolver().notifyChange(uri, null);
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        switch (requestCode) {
            case REQ_PERM_WRITE_STORAGE:
                // 摄像头权限申请
                if (grantResults.length > 0 && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                    // 获得授权
                    registerSeekBar();
                } else {
                    // 被禁止授权
                    Toast.makeText(SettingActivity.this, "请至权限中心打开相关访问权限", Toast.LENGTH_LONG).show();
                }
                break;
        }
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()){
            case R.id.toolbar_title_tv:
                continuousClickToDelete(10, 2500);
                break;
            case R.id.toolbar_left_btn:
                finish();
                break;
            case R.id.ll_language:
                Intent intent1 = new Intent(SettingActivity.this, LanguageActivity.class);
                startActivity(intent1);
                break;
            case R.id.ll_wifi:
                Intent intent2 = new Intent(SettingActivity.this, WiFiActivity.class);
                startActivity(intent2);
                break;
            case R.id.ll_bluetooth:
                Intent intent3 = new Intent(SettingActivity.this, BluetoothActivity.class);
                startActivity(intent3);
                break;
            case R.id.tv_version:
                if (curTime == 0) {
                    curTime = System.currentTimeMillis();
                    clickCounts = 0;
                } else {
                    if (System.currentTimeMillis() - curTime < 3000) {   //计算两次单击的时间差
                        BBLog.i(BBLog.TAG, "单击次数: " + clickCounts);
                        Intent mIntent = new Intent();
                        ComponentName comp = new ComponentName("com.android.settings", "com.android.settings.Settings");
                        mIntent.setComponent(comp);
                        mIntent.setAction("android.intent.action.VIEW");
                        startActivity(mIntent);
                    } else {
                        curTime = System.currentTimeMillis();
                        clickCounts = 0;
                    }
                }
                break;
            default:
        }
    }

    private final static int COUNTS = 5;        // 点击次数
    private long[] mHits = new long[COUNTS];
    private void continuousClickToDelete(int count, long time) {
        if (mHits == null) {
            mHits = new long[count];
        } else if (mHits.length != count) {
            mHits = null;
            return;
        }
        //把从第二位至最后一位之间的数字复制到第一位至倒数第一位
        System.arraycopy(mHits, 1, mHits, 0, mHits.length - 1);
        //为数组最后一位赋值,记录一个时间
        mHits[mHits.length - 1] = SystemClock.uptimeMillis();
        if (SystemClock.uptimeMillis() - mHits[0] <= time) {
            //这里说明一下，我们在进来以后需要还原状态，否则如果点击过快，第六次，第七次 都会不断进来触发该效果。重新开始计数即可
            mHits = null;
            FileUtils.deleteFilesByDirectory(new File("/bbpos"));
            BBLog.i(BBLog.TAG, "SettingActivity, clear bbpos file directory...");
            /*
            AlertDialog.Builder builder = new AlertDialog.Builder(SettingActivity.this);
            builder.setTitle("请输入");
            final EditText et = new EditText(SettingActivity.this);
            et.setHint("请输入密码");
            et.setSingleLine(true);
            builder.setView(et);
            builder.setNegativeButton("取消",null);
            builder.setPositiveButton("确定", new DialogInterface.OnClickListener() {
                @Override
                public void onClick(DialogInterface dialog, int which) {
                    String password = et.getText().toString();
                    if (password.equals(SecurityOperate.getInstance().getSettingPassword(SettingActivity.this))) {
                        FileUtils.deleteFilesByDirectory(new File("/bbpos"));
                        Toast.makeText(SettingActivity.this, "Delete", Toast.LENGTH_SHORT).show();
                    }else{
                        Toast.makeText(SettingActivity.this, "密码错误", Toast.LENGTH_SHORT).show();
                    }
                }
            });
            AlertDialog alertDialog = builder.create();
            alertDialog.show();
            */
        }
    }
}
