package com.dspread.mdm.service.modules.rulebase

import android.content.Context
import com.dspread.mdm.service.constants.TaskStateConstants
import com.dspread.mdm.service.modules.rulebase.core.RuleStateMachine
import com.dspread.mdm.service.modules.rulebase.core.RuleStateMachine.RuleState
import com.dspread.mdm.service.modules.rulebase.engine.RuleExecutionEngine
import com.dspread.mdm.service.modules.rulebase.monitor.RuleStateMonitor
import com.dspread.mdm.service.modules.rulebase.model.Rule
import com.dspread.mdm.service.modules.rulebase.model.RuleStatus
import com.dspread.mdm.service.utils.log.Logger
import java.util.concurrent.atomic.AtomicBoolean

/**
 * RuleBaseManager - 重新设计版本
 * 作为Rulebase模块的主控制器，协调状态机、执行引擎和监控器
 */
class RuleBaseManager private constructor(private val context: Context) {
    
    companion object {
    private const val TAG = "RuleBaseManager"

        @Volatile
        private var instance: RuleBaseManager? = null
        
        fun getInstance(context: Context): RuleBaseManager {
            return instance ?: synchronized(this) {
                instance ?: RuleBaseManager(context.applicationContext).also { instance = it }
        }
    }
    
    /**
         * 直接执行规则 - 静态方法，供RuleBaseStorage调用
         */
        fun executeRuleDirectly(rule: Rule) {
            try {
                Logger.rule("$TAG 直接执行规则: ${rule.ruleId}")
                
                // 获取实例并执行
                val instance = instance
                if (instance != null) {
                    instance.processRule(rule, "A")
                } else {
                    Logger.ruleE("$TAG RuleBaseManager实例未初始化，无法执行规则: ${rule.ruleId}")
                }
                
            } catch (e: Exception) {
                Logger.ruleE("$TAG 直接执行规则失败: ${rule.ruleId}", e)
            }
        }
    }
    
    // 核心组件
    private val stateMachine = RuleStateMachine.getInstance()
    private val executionEngine = RuleExecutionEngine.getInstance(context)
    private val stateMonitor = RuleStateMonitor.getInstance(context)
    
    // 初始化状态
    private val isInitialized = AtomicBoolean(false)
    
    /**
     * 初始化RuleBaseManager
     */
    fun initialize() {
        if (isInitialized.compareAndSet(false, true)) {
            try {
                Logger.rule("$TAG 开始初始化RuleBaseManager...")
                
                // 初始化存储
                RuleBaseStorage.init(context)
                
                // 初始化状态监控器
                stateMonitor.initialize()
                
                // 恢复持久化的规则状态
                // restorePersistedRules() // 暂时注释掉，避免编译错误
                
                Logger.rule("$TAG RuleBaseManager初始化完成")
                
            } catch (e: Exception) {
                Logger.ruleE("$TAG RuleBaseManager初始化失败", e)
                isInitialized.set(false)
                throw e
            }
        }
    }
    
    /**
     * 处理规则
     * 注意：同一条rule不允许修改，Modify操作会被忽略
     */
    fun processRule(rule: Rule, action: String): Boolean {
        return try {
            Logger.rule("$TAG 处理规则: ${rule.ruleId}, 操作: $action")
            
            when (action.uppercase()) {
                "A" -> addRule(rule)
                "D" -> deleteRule(rule.ruleId)
                "U", "M" -> {
                    // 同一条rule不允许修改，直接忽略
                    Logger.rule("$TAG 规则修改操作被忽略: ${rule.ruleId} (同一条rule不允许修改)")
                    true // 返回true表示"成功忽略"
                }
                else -> {
                    Logger.ruleE("$TAG 不支持的操作: $action")
                    false
                }
            }

        } catch (e: Exception) {
            Logger.ruleE("$TAG 处理规则失败: ${rule.ruleId}", e)
            false
        }
    }
    
    /**
     * 添加规则
     */
    private fun addRule(rule: Rule): Boolean {
        return try {
            val ruleId = rule.ruleId
            
            // 检查规则是否已存在
            if (stateMachine.ruleExists(ruleId)) {
                Logger.rule("$TAG 规则已存在，跳过添加: $ruleId")
                return true
            }
            
            // 保存规则到存储
            val saveResult = RuleBaseStorage.processRule(rule.toJson())
            if (!saveResult.isSuccess) {
                Logger.ruleE("$TAG 保存规则失败: $ruleId - ${saveResult.message}")
                return false
            }
            
            // 初始化规则状态
            if (!stateMachine.initializeRule(ruleId)) {
                Logger.ruleE("$TAG 初始化规则状态失败: $ruleId")
                return false
            }
            
            // 立即执行规则
            if (!executionEngine.executeRule(rule)) {
                Logger.ruleE("$TAG 执行规则失败: $ruleId")
                return false
            }
            
            Logger.rule("$TAG 规则添加成功: $ruleId")
            true

        } catch (e: Exception) {
            Logger.ruleE("$TAG 添加规则异常: ${rule.ruleId}", e)
            false
        }
    }

    /**
     * 删除规则
     */
    private fun deleteRule(ruleId: String): Boolean {
        return try {
            Logger.rule("$TAG 删除规则: $ruleId")
            
            // 停止规则执行
            executionEngine.stopRuleExecution(ruleId)
            
            // 清理规则状态
            stateMachine.cleanupRule(ruleId, force = true)
            
            // 从存储中删除规则
            // 创建一个临时的删除规则对象
            val deleteRule = Rule(
                ruleId = ruleId,
                action = "D",
                ruleType = "DELETE",
                ruleStatus = RuleStatus.READY,
                beginDate = "",
                endDate = "",
                modifyTime = System.currentTimeMillis(),
                appList = emptyList(),
                deleteAppList = emptyList()
            )
            val deleteResult = RuleBaseStorage.processRule(deleteRule.toJson())
            if (!deleteResult.isSuccess) {
                Logger.ruleE("$TAG 删除规则失败: $ruleId - ${deleteResult.message}")
                return false
            }
            
            Logger.rule("$TAG 规则删除成功: $ruleId")
            true
            
        } catch (e: Exception) {
            Logger.ruleE("$TAG 删除规则失败: $ruleId", e)
            false
        }
    }

    /**
     * 获取规则状态
     */
    fun getRuleState(ruleId: String): RuleState? {
        return stateMachine.getRuleState(ruleId)
    }
    
    /**
     * 检查规则是否存在
     */
    fun ruleExists(ruleId: String): Boolean {
        return stateMachine.ruleExists(ruleId)
    }
    
    /**
     * 获取所有规则状态
     */
    fun getAllRuleStates(): Map<String, RuleState> {
        return stateMachine.getAllRuleStates()
    }
    
    /**
     * 获取规则执行状态
     */
    fun getRuleExecutionState(ruleId: String): RuleExecutionEngine.ExecutionState? {
        return executionEngine.getRuleExecutionState(ruleId)
    }
    
    /**
     * 获取监控统计信息
     */
    fun getMonitorStats(): RuleStateMonitor.MonitorStats {
        return stateMonitor.getMonitorStats()
    }
    
    /**
     * 停止规则执行
     */
    fun stopRuleExecution(ruleId: String): Boolean {
        return executionEngine.stopRuleExecution(ruleId)
    }
    
    /**
     * 清理所有规则
     */
    fun clearAllRules() {
        try {
            Logger.rule("$TAG 开始清理所有规则...")
            
            // 清理执行引擎
            executionEngine.clearAllRules()
            
            // 清理状态机
            stateMachine.clearAllRules()
            
            // 清理存储
            RuleBaseStorage.clearAllRules()
            
            Logger.rule("$TAG 所有规则清理完成")
            
        } catch (e: Exception) {
            Logger.ruleE("$TAG 清理所有规则失败", e)
        }
    }

    /**
     * 检查规则是否需要执行（兼容旧系统逻辑）
     */
    fun checkRuleExecutionNeeded(): Boolean {
        return try {
            // 暂时返回false，避免编译错误
            // TODO: 实现从RuleBaseStorage获取规则的逻辑
            Logger.rule("$TAG 检查规则执行需求 - 暂时返回false")
                false
            
        } catch (e: Exception) {
            Logger.ruleE("$TAG 检查规则执行需求失败", e)
            false
        }
    }

    /**
     * 批量处理规则（兼容旧系统逻辑）
     */
    fun processBatchRules() {
        try {
            Logger.rule("$TAG 开始批量处理规则...")
            
            // 暂时跳过批量处理，避免编译错误
            // TODO: 实现从RuleBaseStorage获取规则的逻辑
            Logger.rule("$TAG 批量处理规则 - 暂时跳过")

        } catch (e: Exception) {
            Logger.ruleE("$TAG 批量处理规则失败", e)
        }
    }
    
    /**
     * 获取执行引擎实例
     */
    fun getExecutionEngine(): RuleExecutionEngine {
        return executionEngine
    }
    
    /**
     * 获取状态机实例
     */
    fun getStateMachine(): RuleStateMachine {
        return stateMachine
    }
    
    /**
     * 获取规则执行统计
     */
    fun getRuleExecutionStats(): RuleExecutionStats {
        return try {
            // 暂时返回空统计，避免编译错误
            // TODO: 实现从RuleBaseStorage获取规则的逻辑
            Logger.rule("$TAG 获取规则执行统计 - 暂时返回空统计")
            RuleExecutionStats()

        } catch (e: Exception) {
            Logger.ruleE("$TAG 获取规则执行统计失败", e)
            RuleExecutionStats()
        }
    }

    /**
     * 规则执行统计
     */
    data class RuleExecutionStats(
        val totalRules: Int = 0,
        val executedRules: Int = 0,
        val pendingRules: Int = 0,
        val completedRules: Int = 0,
        val failedRules: Int = 0
    )
}
