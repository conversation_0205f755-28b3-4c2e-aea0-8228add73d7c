package com.dspread.mdm.service.broadcast.core

import android.content.Context
import android.content.Intent

/**
 * 广播事件处理器接口
 * 定义统一的事件处理接口
 */
interface BroadcastEventHandler {
    
    /**
     * 获取处理器名称
     */
    fun getHandlerName(): String
    
    /**
     * 获取支持的广播Action列表
     */
    fun getSupportedActions(): List<String>
    
    /**
     * 处理广播事件
     * @param context 上下文
     * @param intent 广播Intent
     * @return 是否处理成功
     */
    fun handleBroadcast(context: Context, intent: Intent): Boolean
    
    /**
     * 初始化处理器
     */
    fun initialize(context: Context) {}
    
    /**
     * 清理资源
     */
    fun cleanup() {}
}

/**
 * 网络事件处理器接口
 */
interface NetworkEventHandler : BroadcastEventHandler {
    
    /**
     * 处理网络连接变化
     */
    fun onNetworkChanged(context: Context, isConnected: Boolean, networkType: String)
    
    /**
     * 处理WiFi状态变化
     */
    fun onWifiStateChanged(context: Context, wifiState: Int, ssid: String?)
}

/**
 * 电池事件处理器接口
 */
interface BatteryEventHandler : BroadcastEventHandler {
    
    /**
     * 处理电池状态变化
     */
    fun onBatteryChanged(context: Context, level: Int, temperature: Int, voltage: Int, status: Int)
    
    /**
     * 处理电池电量低
     */
    fun onBatteryLow(context: Context)
    
    /**
     * 处理电源连接/断开
     */
    fun onPowerConnectionChanged(context: Context, isConnected: Boolean)
}

/**
 * 系统事件处理器接口
 */
interface SystemEventHandler : BroadcastEventHandler {
    
    /**
     * 处理开机完成
     */
    fun onBootCompleted(context: Context)

    /**
     * 处理系统关机/重启
     */
    fun onSystemShutdown(context: Context, isReboot: Boolean)
}

/**
 * WebSocket事件处理器接口
 */
interface WebSocketEventHandler : BroadcastEventHandler {
    
    /**
     * 处理WebSocket连接请求
     */
    fun onWebSocketConnect(context: Context)
    
    /**
     * 处理WebSocket断开请求
     */
    fun onWebSocketDisconnect(context: Context)
    
    /**
     * 处理WebSocket重连请求
     */
    fun onWebSocketReconnect(context: Context)
    
    /**
     * 处理心跳发送请求
     */
    fun onSendHeartbeat(context: Context)
}

/**
 * 定时器事件处理器接口
 */
interface HeartbeatEventHandler : BroadcastEventHandler {
    
    /**
     * 处理轮询定时器启动
     */
    fun onPollTimerStart(context: Context)
    
    /**
     * 处理其他定时器事件
     */
    fun onTimerEvent(context: Context, timerType: String, data: String?)
}
