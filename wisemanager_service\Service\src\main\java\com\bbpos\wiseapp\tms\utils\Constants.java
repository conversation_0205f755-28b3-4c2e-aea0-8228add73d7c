package com.bbpos.wiseapp.tms.utils;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.content.pm.ResolveInfo;
import android.os.Environment;
import android.text.TextUtils;
import android.util.Log;

import com.bbpos.wiseapp.logger.BBLog;
import com.bbpos.wiseapp.service.common.UsualData;
import com.bbpos.wiseapp.settings.utils.WirelessUtil;
import com.bbpos.wiseapp.tms.location.GpsLocationManager;
import com.bbpos.wiseapp.utils.SharedPreferencesUtils;
import com.bbpos.wiseapp.param.manager.ParamManager;
import com.bbpos.wiseapp.tms.adapter.SystemManagerAdapter;
import com.bbpos.wiseapp.tms.adapter.SystemManagerAdapter.BindServiceSuccess;
import com.bbpos.wiseapp.tms.listener.device.MidwareInterface;

import org.json.JSONArray;

import java.io.File;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class Constants {
	static {
		if (isActionServiceExist()) {
			SystemManagerAdapter.bindService(ContextUtil.getInstance(), new BindServiceSuccess() {
				@Override
				public void onBindSuccess() {
					// TODO Auto-generated method stub
					MidwareInterface.init(ContextUtil.getInstance());
				}
			});
		}
		paramInit();
	}
	public static boolean DEBUG = false;
	/**厂商标志*/
	public static final String WISE_APP_NAME = "com.bbpos.wiseapp";
	public static final String BBPOS_MANU = "BBPOS";
	public static final String INSTALLER_BBPOS = "hscloud.appmarket";
	public static final String SERVICE_SYSTEM_MANAGER= "com.bbpos.wiseapp.SDK_MANAGE";
	/**上次网络类型 wifi/mobile*/
	public static String LAST_NET_TYPE = "";
	/**上次网络类型  网络制式*/
	public static String LAST_SUB_NET_TYPE = "";
	public static final String NET_TYPE_WIFI = "WIFI";
	public static final String NET_TYPE_MOBILE = "MOBILE";
	/**数据收集服务定时间隔 单位:秒*/
	public static final int DATA_COLLECT_INVERTAL = 60*5;
	/**初始化服务定时间隔 单位:秒*/
	public static int PROVISION_INVERTAL = 12*60*60;
	/**流量上送最多间隔天数*/
	public static final int TRAFFIC_UPLOAD_DAY = 7;
	/**任务完成列表项存储的最大值*/
	public static final int COMPLETED_TASK_LIST_MAX = 50;
	public static final String STORAGE_OVER_FLOW = "STORAGE_OVER_FLOW";
	public static String HTTP_TRANS_URL;
	public static String HTTP_SERVER_URL;
	public static String HTTP_HEAVY_SERVER_URL;
	public static String HTTP_TRANS_URL(String type) {
//		return "http://192.168.66.191:8099/WAIS/posTran/tran";
		String url = null;
		String serverUrl = SharedPreferencesUtils.getSharePreferencesValue(UsualData.SHARED_PREFERENCES_SERVER_URL, "");
//		BBLog.i(BBLog.TAG, "serverUrl = " + serverUrl);
		if (!TextUtils.isEmpty(serverUrl)) {
			url = serverUrl;
		} else {
			url = HTTP_SERVER_URL;
		}

		HTTP_TRANS_URL = url + File.separator + new ParamManager().getStrParam(ContextUtil.getInstance(), ParameterName.HTTP_METHOD_TRANS_KEY, "tran");
		return HTTP_TRANS_URL;
	}
	public static String HTTP_UPLOAD_URL;
	/**设置连接超时*/
	public static final int ConnectionTimeout = 60000;
	/**设置请求超时*/
	public static final int SoTimeout = 30000;
	/**接口版本*/
	public static final String SERVER_INTERFACE_VERSION = "1";
	public static final String TAG = "WiseApp2.0";
	public static final String dateFormat = "yyyy-MM-dd HH:mm:ss";
	public static final String dateFormatForUrl = "yyyy-MM-dd-HH-mm-ss";
	public static final String dateFormatForTaskResultUpload = "yyyy/MM/dd HH:mm:ss.SSS";
	@SuppressLint("SdCardPath")
	public static final String SD_PATH = "/sdcard/";
	/**是否完成初次轮询*/
	public static boolean IS_FIRST_POLL_COMPLETED = false;
	/**是否完成终端信息上送*/
	public static boolean IS_FIRST_TER_INFO_UPLOAD_COMPLETED = false;
	/**是否完成WebSocket连接上终端信息上送*/
	public static boolean IS_FIRST_C090X_UPLOAD_COMPLETED = false;
	public static boolean IS_FIRST_TIME_WEBSOCKET_CONNECTED = true;
	public static boolean IS_C0904_UPLOAD_NEEDED = false;

	/****provisioning process***/
	public static boolean IS_PROVISIONING_FROM_SCAN_QRCODE = false;//是否进行扫码操作，优先级 IS_PROVISIONING_FROM_SCAN_QRCODE>IS_FIRST_PROVISIONING_COMPLETED
	public static boolean IS_FIRST_PROVISIONING_COMPLETED = false;//终端开机后第一次provisioning是否完成
	public static boolean IS_LOGO_DOWNLOAD_SUCCESS = false;
	public static boolean IS_WALLPAPER_DOWNLOAD_SUCCESS = false;
	public static boolean IS_CUSTOMER_APP_DOWNLOAD_SUCCESS = false;
	public static boolean IS_CUSTOMER_BOOT_LOGO_DOWNLOAD_SUCCESS = false;
	public static boolean IS_CUSTOMER_BOOT_ANIM_DOWNLOAD_SUCCESS = false;
	public static boolean IS_CUSTOMER_CA_CERT_DOWNLOAD_SUCCESS = false;

	public static boolean IS_NEED_REBOOT = false;

	public static boolean IS_UNBOX_RESETTING = false;
	public static String UPLOAD_MODE = "1";

	public static boolean IF_BATLOW_AFTER_DIALOG = false;
	public static boolean IF_DOWNLOAD_WIFI_ONLY_AFTER_DIALOG = false;

	public static boolean IS_BATTERY_CHARGING = false;
	public static boolean IS_BATTERY_LOW = false;
	public static boolean IS_BATTERY_LOW_FOR_OSUPDATE = false;
	public static String UNBOX_STATUS = "";
	public static boolean IS_OUT_OF_BATTERY = false;
	public static boolean IS_IN_USE = false;
	public static int BAT_TEMP = 0;
	public static int BAT_LEVEL = 0;
	public static int BAT_HEALTH = 0;
	public static int BAT_LEVEL_OFFSET = 2;
	public static int BAT_LOW_FOR_OSUPDATE = 20;
	public static int BAT_LOW_FOR_OSUPDATE_CHARGING = 15;
	public static int BAT_LOW_FOR_SYYTEM = 15;
	public static int WIFI_SIGNAL_LEVEL = -1;
	public static int WIFI_SIGNAL_VALUE = -1000;
	public static String WIFI_SSID = "unKnow";
	public static int MOBILE_TYPE = WirelessUtil.NETWORK_UNKNOW;
	public static int MOBILE_SIGNAL_LEVEL = -1;
	public static long WISEAPP_LOG_FILE_MAX_SIZE = 3 * 1024 * 1024 * 1024L;//wiseapp log文件夹存储空间上限
	public static int rule_query_count = 4;
	public static int wifi_query_count = 4;

	public static boolean EGINX_PROXY_ENABLE = false;
	public static String EGINX_PROXY_IP = "";

	public static String S3_RESOURCE_URL = "https://api.wisemanager.de/v1/mdm/resources/regfileurl?";
	/**轮询间隔时间  单位：秒*/
	public static long POLL_INTERVAL;
	public static long POLL_INTERVAL() {
		String heartbeatTime = SharedPreferencesUtils.getSharePreferencesValue(UsualData.SHARED_PREFERENCES_HEARTBEAT_TIME, "0");
		if (!TextUtils.isEmpty(heartbeatTime) && Integer.parseInt(heartbeatTime) != 0) {
			POLL_INTERVAL = Integer.parseInt(heartbeatTime);
		}
		BBLog.i(BBLog.TAG, "POLL_INTERVAL = " + POLL_INTERVAL);
		return POLL_INTERVAL;
	}
	/**终端信息上送间隔时间  单位：秒*/
	public static long TER_INFO_UPLOAD_INTERVAL;
	public static long TER_INFO_UPLOAD_INTERVAL() {
		String terminalInfoTime = SharedPreferencesUtils.getSharePreferencesValue(UsualData.SHARED_PREFERENCES_TERMINAL_INFO_TIME, "0");
		if (!TextUtils.isEmpty(terminalInfoTime) && Integer.parseInt(terminalInfoTime) != 0) {
			TER_INFO_UPLOAD_INTERVAL = Integer.parseInt(terminalInfoTime);
		}
		BBLog.i(BBLog.TAG, "TER_INFO_UPLOAD_INTERVAL = " + TER_INFO_UPLOAD_INTERVAL);
		return TER_INFO_UPLOAD_INTERVAL;
	}
	public static long WISE_LOG_UPLOAD_INTERVAL;
	public static long WISE_LOG_UPLOAD_INTERVAL() {
		String wiseLogUploadTime = SharedPreferencesUtils.getSharePreferencesValue(UsualData.SHARED_PREFERENCES_WISELOG_TIME, "0");
		if (!TextUtils.isEmpty(wiseLogUploadTime) && Integer.parseInt(wiseLogUploadTime) != 0) {
			WISE_LOG_UPLOAD_INTERVAL = Integer.parseInt(wiseLogUploadTime);
		}
		BBLog.i(BBLog.TAG, "WISE_LOG_UPLOAD_INTERVAL = " + WISE_LOG_UPLOAD_INTERVAL);
		return WISE_LOG_UPLOAD_INTERVAL;
	}
	public static long PROVISIONING_INTERVAL() {
		String provisioningTime = SharedPreferencesUtils.getSharePreferencesValue(UsualData.SHARED_PREFERENCES_ACCESS_POLLING_TIME, "12");
		if (!TextUtils.isEmpty(provisioningTime) && Integer.parseInt(provisioningTime) != 0) {
			PROVISION_INVERTAL = Integer.parseInt(provisioningTime) * 60 * 60;
		}
		BBLog.i(BBLog.TAG, "PROVISIONING_INTERVAL = " + PROVISION_INVERTAL);
		return PROVISION_INVERTAL;
	}
	/**任务删除标志*/
	public static final String TASK_DELETE_FLG = "01";
    public static String HTTP_HEAD_KEY_VERSION = "tmsVersion";
    public static String HTTP_HEAD_VALUE_VERSION = "HS1.5.base_00";
//	@SuppressLint("SdCardPath")
	public static String USAGE_LOG_FILE_PATH = Environment.getExternalStorageDirectory().getPath()+ "/Android/Data/system/usages.log";
	public static String APK_FILE_FATHER_PATH = Environment.getExternalStorageDirectory().getPath()+ "/Share"+File.separator+ ContextUtil.getInstance().getPackageName()+File.separator;
	public static String APK_FILE_PATH = Environment.getExternalStorageDirectory().getPath()+"/Share"+File.separator+ ContextUtil.getInstance().getPackageName()+File.separator+"apk"+File.separator;
	public static String APK_FILE_PATH_UNBOX = Environment.getExternalStorageDirectory().getPath()+"/Share"+File.separator+ ContextUtil.getInstance().getPackageName()+File.separator+"unbox"+File.separator;
	public static String CERT_FILE_PATH = "mdm.cer";
	public static String LOGIN_SUCCESSFUL = "login_successful";
	public static String SET_LAUNCHER = "set_launcher";
	public static String APK_ORIGINAL_SUFFIX = "_ORG";
	public static String MARKET_APK_FILE_PATH = Environment.getExternalStorageDirectory().getPath()+ "/Share"+File.separator+ ContextUtil.getInstance().getPackageName()+File.separator+"market_apk"+File.separator;
	public static String OTA_FILE_PATH = Environment.getExternalStorageDirectory().getPath()+"/Share"+File.separator+ ContextUtil.getInstance().getPackageName()+File.separator+"ota"+File.separator;
	public static String OTA_FLAG_PATH = Environment.getExternalStorageDirectory()+"/Share"+File.separator+ "com.bbpos.tmt.gds"+File.separator+"ota_flag.con";
	public static String OTA_UPGRADE_STATUS_PATH = Environment.getExternalStorageDirectory()+"/Share"+File.separator+"com.bbpos.tmt.gds"+File.separator+"ota_upgrade_status.con";

	public static String OTA_FILE_SDCardPATH = SD_PATH+"Share"+File.separator+ ContextUtil.getInstance().getPackageName()+File.separator+"ota"+File.separator;
	public static String OTA_UPDATE_FILE_NAME = "update.zip";
	public static final int FILE_DOWNLOAD_RETRY = 5;
	public static long STORAGE_REMAIN_MIN;
	/**--------------参数更新--------------**/
	public static final Map<String,String> parsingParamFile = new HashMap<String,String>();
	public static final String PARAM_FILE_PATH = "filePath";
	public static final String PARAM_FILE_MD5 = "md5";
	public static final String PARAM_UPDT_TIME = "updtTime";
	public static final String PARSED_STAT_SUCCESS = "01";
	public static final String PARSED_STAT_FAILE = "02";
	public static final String PARSED_STAT_NOTPARSE = "00";
	public static final String OFF_LINE_PARAM_FILE_DIR = "/data/xml";
	public static final String PUBLIC_KEY= "PUBLIC_KEY";
	public static final String TMS_PKG_NAME = ContextUtil.getInstance().getPackageName();
	public static boolean BT_ENABLE = false;//allow to use
	public static boolean BT_STATUS = false;//BT switch default status  always close
	public static String BT_MAC = "";
	public static String IMEI_NO = "";

	public final static int NOTIFICATION_ID_ICON = 0x10000;

	public static boolean B_UNBOX_RUNNING = false;
	public static boolean B_UNBOX_RUNNING_LOCK = false;
	public static boolean B_UNBOX_RESET_FROM_GEO = false;
	public static boolean M_GEOFENCE_SET_SERVICE_LAUNCHER = true;//是否使用“设置service为主Launcher的锁屏方案
	public static int M_GEOFENCE_STATUS = GpsLocationManager.IN_ZONE;
	public static String STORE_ID = "";
	public static String STORE_SSID = "";
	public static String STORE_IP = "";
	public static String LAST_CLIENT_APK_MD5 = "";

	public static String m_gms_applist = null;
	public static boolean m_need_gms_download = false;

	public static final int M_WEBSOCKET_MSGVER = 3;

	public static String U_MY_VERSION = "";

	@SuppressLint("InlinedApi")
	public static void paramInit(){
		Context context = ContextUtil.getInstance();
		long default_poll_interval = 300; //5分钟
		String default_http_heavy_server_url = "https://api.wisemanager.com/WAIS/posTran";
		String default_http_server_url = "https://bbpos.wisemanager.com/TIS/posTran";
		String default_http_server_port = "8088";
		String default_http_trans = "tran";
		String default_http_upload = "upload";
		long default_ter_info_upload_interval = 900; //15分钟
		long default_wise_log_upload_interval = 600; //10分钟
		long default_min_storage_remain = 100;
		ParamManager pm = new ParamManager();
		updateParamIfNoExist(pm, context, ParameterName.POLL_INTERVAL_KEY, default_poll_interval+"");
		updateParamIfNoExist(pm, context, ParameterName.HTTP_URL_KEY, default_http_server_url+"");
		updateParamIfNoExist(pm, context, ParameterName.HTTP_HEAVY_URL_KEY, default_http_heavy_server_url+"");
		updateParamIfNoExist(pm, context, ParameterName.HTTP_PORT_KEY, default_http_server_port+"");
		updateParamIfNoExist(pm, context, ParameterName.TER_INFO_UPLOAD_INTERVAL_KEY, default_ter_info_upload_interval+"");
		POLL_INTERVAL = pm.getLongParam(context,ParameterName.POLL_INTERVAL_KEY, default_poll_interval);//default
		String httpServerUrl = pm.getStrParam(context, ParameterName.HTTP_URL_KEY, Helpers.getSPParam(context, ParameterName.HTTP_URL, default_http_server_url));//defalut http url TODO
		HTTP_SERVER_URL = httpServerUrl;
		String httpHeavyServerUrl = pm.getStrParam(context, ParameterName.HTTP_HEAVY_URL_KEY, Helpers.getSPParam(context, ParameterName.HTTP_HEAVY_URL, default_http_heavy_server_url));//defalut http url TODO
		HTTP_HEAVY_SERVER_URL = httpHeavyServerUrl;
		HTTP_TRANS_URL = httpServerUrl+File.separator + pm.getStrParam(context, ParameterName.HTTP_METHOD_TRANS_KEY, default_http_trans);
		HTTP_UPLOAD_URL = httpServerUrl+File.separator + pm.getStrParam(context, ParameterName.HTTP_METHOD_UPLOAD_KEY, default_http_upload);
		//更新url
//		DbConstants.updateServerUrlData(context, HTTP_TRANS_URL);
		TER_INFO_UPLOAD_INTERVAL  = pm.getLongParam(context, ParameterName.TER_INFO_UPLOAD_INTERVAL_KEY, default_ter_info_upload_interval);//default
		WISE_LOG_UPLOAD_INTERVAL  = default_wise_log_upload_interval;//default
		STORAGE_REMAIN_MIN = pm.getLongParam(context, ParameterName.STORAGE_REMAIN_MIN_KEY, default_min_storage_remain);
		BBLog.i(BBLog.TAG, "POLL_INTERVAL="+POLL_INTERVAL+" STORAGE_REMAIN_MIN=" + STORAGE_REMAIN_MIN + " TER_INFO_UPLOAD_INTERVAL= "+TER_INFO_UPLOAD_INTERVAL);
	}

	private static void updateParamIfNoExist(ParamManager pm, Context context, String paramKey, String paramValue){
		if(!pm.hasParam(context, paramKey)){
			BBLog.v(BBLog.TAG, "init param "+paramKey+":"+paramValue);
			pm.insertParams(context, paramKey, paramValue);
		}
	}

	/***
	 * 根据action名称获取包名
	 * @return
	 */
	public static boolean isActionServiceExist() {
		Intent intent = new Intent();
		intent.setAction(Constants.SERVICE_SYSTEM_MANAGER);
		PackageManager pm = ContextUtil.getInstance().getPackageManager();
		List<ResolveInfo> resolveInfo = pm.queryIntentServices(intent, 0);
		if (resolveInfo == null || resolveInfo.size() < 1) {
			return false;
		} else {
			return true;
		}
	}

	/**0101、0109 定时器是否正常工作标志**/
	public static final String FLAG_LAST_POLL_SUCCESS = "last_A0101_success";
	public static final String FLAG_LAST_TER_INFO_UPLOAD_ALIVE = "last_A0109_success";
	/**wisecube firmware 、 SP Firmware 升級標誌**/
	public static final int TYPE_WISECUBE_FW_UPGRADE = 1;//7MD
	public static final int TYPE_SP_FW_UPGRADE = 2;//7MD
	public static final int TYPE_TMT_UPGRADE = 3;//P1000/P500 tmt
	public static final int TYPE_WISEPOS_TMT_UPGRADE = 4;//wisepos4G  ota
	public static final int TYPE_KEY_UPGRADE = 5;//P1000/P500 key

	public static final String FLAG_WISECUBE_FW_UPGRADE_EXIST = "wc_ota_exist";//7MD WC OTA
	public static final String FLAG_SP_UPGRADE_EXIST = "sp_ota_exist";//7MD SP OTA
	public static final String FLAG_TMT_UPGRADE_EXIST = "tmt_ota_exist";//P1000/P500 TMT OTA
	public static final String FLAG_KEY_UPGRADE_EXIST = "key_ota_exist";//P1000/P500 KEY OTA

	public static boolean isOTARunning = false;//当前是否正在执行ota、sp更新操作

	public static String allowUploadPaymentDataPkgs = "";//允许上送payment 交易信息的包名
	public static final String PRE_PROVISION = "Provision:";//provision下发的payment list packageName 缓存标志
	public static final String PRE_LOCAL = "Local:";//默认配置的payment list packageName 缓存标志

	public static String start_up_model = "";//service启动时终端所处环境标识
	public static enum E_MSR_TYPE {
		MAXIM_MSR,
		MEGAHUNT_MSR,
		UNKNOWN,
	}
	public static E_MSR_TYPE MSR_TYPE = E_MSR_TYPE.UNKNOWN;
}
