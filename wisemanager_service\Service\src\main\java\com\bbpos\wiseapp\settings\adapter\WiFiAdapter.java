package com.bbpos.wiseapp.settings.adapter;

import android.content.Context;
import android.net.wifi.ScanResult;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseAdapter;
import android.widget.ImageView;
import android.widget.TextView;

import com.bbpos.wiseapp.service.R;
import com.bbpos.wiseapp.settings.utils.HelperUtil;
import com.bbpos.wiseapp.settings.utils.WirelessUtil;

import java.lang.reflect.Constructor;
import java.lang.reflect.InvocationTargetException;
import java.util.List;

/**
 * Created by ThinkPad on 2018/1/17.
 */

public class WiFiAdapter extends BaseAdapter {
    private List<ScanResult> list_item = null;
    private int selectItem;
    private Context context;

    public WiFiAdapter(Context context, List<ScanResult> list) {
        super();
        this.context = context;
        setData(list);
    }

    public void setData(List<ScanResult> list) {
        list_item = list;
        if (list_item != null) {
            try {
                ScanResult sr1 = null;
                Constructor<ScanResult> ctor = null;
                ctor =ScanResult.class.getDeclaredConstructor(ScanResult.class);
                ctor.setAccessible(true);
                ScanResult sr = ctor.newInstance(sr1);
                sr.SSID = "none";
                sr.capabilities = "WPA2-PSK";
                list.add(list.size(), sr);
            } catch (NoSuchMethodException e) {
                e.printStackTrace();
            } catch (IllegalAccessException e) {
                e.printStackTrace();
            } catch (InstantiationException e) {
                e.printStackTrace();
            } catch (InvocationTargetException e) {
                e.printStackTrace();
            }
        }
    }

    @Override
    public int getCount() {
        return list_item!=null ? this.list_item.size() : 0;
    }

    @Override
    public Object getItem(int position) {
        return list_item!=null ? list_item.get(position) : null;
    }

    @Override
    public long getItemId(int position) {
        return position;
    }

    @Override
    public View getView(int position, View convertView, ViewGroup parent) {
        if (list_item == null) {
            return null;
        }

        ViewHolder viewHolder = null;
        if (convertView == null) {
            convertView = LayoutInflater.from(context).inflate(R.layout.item_wifi, parent, false);
            viewHolder = new ViewHolder();
            viewHolder.mTVName = (TextView) convertView.findViewById(R.id.tv_name);
            viewHolder.mIVSignal = (ImageView) convertView.findViewById(R.id.iv_signal);
            viewHolder.mTVStutas = (TextView) convertView.findViewById(R.id.tv_status);
            viewHolder.mIVCheck = (ImageView) convertView.findViewById(R.id.iv_check);
            convertView.setTag(viewHolder);
        } else {
            viewHolder = (ViewHolder) convertView.getTag();
        }

        if (position == list_item.size()-1) {
            viewHolder.mIVSignal.setImageResource(R.drawable.add_selector);
            viewHolder.mTVName.setText(context.getString(R.string.add_wifi));
            viewHolder.mTVStutas.setVisibility(View.GONE);
            viewHolder.mIVCheck.setVisibility(View.GONE);
        } else {
            viewHolder.mTVName.setText(list_item.get(position).SSID);
            //信号强度
            if (list_item.get(position).capabilities.contains("WEP")
                    || list_item.get(position).capabilities.contains("PSK")
                    || list_item.get(position).capabilities.contains("EAP")) {
                viewHolder.mIVSignal.setImageResource(R.drawable.wifilock_level_list);
                int level = WirelessUtil.getWifiLevel(list_item.get(position).level, WirelessUtil.WIFILOCK_SIGNAL_LEVEL_MAX);
                viewHolder.mIVSignal.setImageLevel(level);
            } else {
                viewHolder.mIVSignal.setImageResource(R.drawable.wifi_level_list);
                int level = WirelessUtil.getWifiLevel(list_item.get(position).level, WirelessUtil.WIFI_SIGNAL_LEVEL_MAX);
                viewHolder.mIVSignal.setImageLevel(level);
            }
            //是否连接
            if (WirelessUtil.getCurConnectSSID(context).equals(list_item.get(position).SSID)) {
                viewHolder.mTVStutas.setVisibility(View.VISIBLE);
                viewHolder.mTVStutas.setText(context.getString(R.string.connected));
                viewHolder.mIVCheck.setVisibility(View.VISIBLE);
            } else {
                viewHolder.mTVStutas.setVisibility(View.GONE);
                viewHolder.mIVCheck.setVisibility(View.INVISIBLE);
            }
        }
        return convertView;
    }

    private static class ViewHolder {
        public TextView mTVName;
        public TextView mTVStutas;
        public ImageView mIVSignal;
        public ImageView mIVCheck;
    }
}
