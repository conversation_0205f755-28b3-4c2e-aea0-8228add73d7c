package com.dspread.mdm.service.utils.path

import android.content.Context
import com.dspread.mdm.service.constants.Constants
import com.dspread.mdm.service.utils.log.Logger
import java.io.File

/**
 * 配置路径管理器
 * 负责管理配置文件的存储路径，优先使用系统目录，失败时回退到外部存储
 */
object ConfigPathManager {
    
    private var actualConfigPath: String? = null
    private var isInitialized = false
    
    /**
     * 初始化配置路径
     * 尝试创建主目录，失败时使用回退目录
     */
    fun initialize(context: Context): String {
        if (isInitialized && actualConfigPath != null) {
            return actualConfigPath!!
        }
        
        // 1. 尝试使用主目录 /data/pos/config/
        val primaryDir = File(Constants.ModuleConstants.PRIMARY_CONFIG_PATH)
        if (tryCreateDirectory(primaryDir, "主目录")) {
            actualConfigPath = Constants.ModuleConstants.PRIMARY_CONFIG_PATH
            Logger.provI("使用主配置目录: ${actualConfigPath}")
            isInitialized = true
            return actualConfigPath!!
        }
        
        // 2. 回退到外部存储目录 /sdcard/Android/data/com.dspread.mdm.service/files/config/
        val fallbackDir = File(Constants.ModuleConstants.FALLBACK_CONFIG_PATH)
        if (tryCreateDirectory(fallbackDir, "回退目录")) {
            actualConfigPath = Constants.ModuleConstants.FALLBACK_CONFIG_PATH
            Logger.provW("主目录不可用，使用回退配置目录: ${actualConfigPath}")
            isInitialized = true
            return actualConfigPath!!
        }
        
        // 3. 最后的回退：使用应用私有目录
        val appPrivateDir = File(context.filesDir, "config")
        if (tryCreateDirectory(appPrivateDir, "应用私有目录")) {
            actualConfigPath = appPrivateDir.absolutePath + "/"
            Logger.provE("系统目录和外部存储都不可用，使用应用私有目录: ${actualConfigPath}")
            isInitialized = true
            return actualConfigPath!!
        }
        
        throw RuntimeException("无法创建任何配置目录")
    }
    
    /**
     * 获取当前实际使用的配置路径
     */
    fun getConfigPath(context: Context): String {
        if (!isInitialized || actualConfigPath == null) {
            return initialize(context)
        }
        return actualConfigPath!!
    }
    
    /**
     * 获取配置文件的完整路径
     */
    fun getConfigFilePath(context: Context, fileName: String): String {
        return getConfigPath(context) + fileName
    }
    
    /**
     * 检查当前使用的是否为主目录
     */
    fun isUsingPrimaryPath(): Boolean {
        return actualConfigPath == Constants.ModuleConstants.PRIMARY_CONFIG_PATH
    }
    
    /**
     * 检查当前使用的是否为回退目录
     */
    fun isUsingFallbackPath(): Boolean {
        return actualConfigPath == Constants.ModuleConstants.FALLBACK_CONFIG_PATH
    }
    
    /**
     * 获取路径类型描述
     */
    fun getPathTypeDescription(): String {
        return when {
            isUsingPrimaryPath() -> "系统目录"
            isUsingFallbackPath() -> "外部存储目录"
            else -> "应用私有目录"
        }
    }
    
    /**
     * 尝试创建目录
     */
    private fun tryCreateDirectory(dir: File, description: String): Boolean {
        return try {
            if (dir.exists()) {
                Logger.prov("$description 已存在: ${dir.absolutePath}")
                return true
            }
            
            val created = dir.mkdirs()
            if (created) {
                Logger.provI("$description 创建成功: ${dir.absolutePath}")
                
                // 验证目录权限
                if (dir.canRead() && dir.canWrite()) {
                    Logger.prov("$description 权限验证通过")
                    return true
                } else {
                    Logger.provW("$description 权限不足")
                    return false
                }
            } else {
                Logger.provW("$description 创建失败: ${dir.absolutePath}")
                return false
            }
        } catch (e: Exception) {
            Logger.provE("$description 创建异常: ${e.message}")
            false
        }
    }
    
    /**
     * 重置初始化状态（用于测试）
     */
    fun reset() {
        actualConfigPath = null
        isInitialized = false
    }
    
    /**
     * 获取诊断信息
     */
    fun getDiagnosticInfo(context: Context): String {
        val currentPath = if (isInitialized) actualConfigPath ?: "未初始化" else "未初始化"
        val pathType = if (isInitialized) getPathTypeDescription() else "未知"
        
        return """
            配置路径诊断:
            - 当前路径: $currentPath
            - 路径类型: $pathType
            - 初始化状态: $isInitialized
            - 主目录可用: ${File(Constants.ModuleConstants.PRIMARY_CONFIG_PATH).let { it.exists() || it.mkdirs() }}
            - 回退目录可用: ${File(Constants.ModuleConstants.FALLBACK_CONFIG_PATH).let { it.exists() || it.mkdirs() }}
        """.trimIndent()
    }
}
