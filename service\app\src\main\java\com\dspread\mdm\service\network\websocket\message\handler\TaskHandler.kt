package com.dspread.mdm.service.network.websocket.message.handler

import android.content.Context
import com.dspread.mdm.service.constants.TaskStateConstants
import com.dspread.mdm.service.platform.api.app.AppManagerApi
import com.dspread.mdm.service.platform.api.storage.StorageApi

import com.dspread.mdm.service.network.https.HttpDownloader
import com.dspread.mdm.service.network.websocket.message.WsMessageSender
import com.dspread.mdm.service.network.websocket.task.WsTaskManager
import com.dspread.mdm.service.platform.collector.DeviceDataCollector
import com.dspread.mdm.service.utils.log.Logger
import org.json.JSONArray
import org.json.JSONObject
import java.io.File
import java.io.FileInputStream
import java.security.MessageDigest
import android.os.Build
import kotlinx.coroutines.*
import com.dspread.mdm.service.ui.dialog.OsUpgradeDialog
import com.dspread.mdm.service.platform.api.system.SystemUpdateApi
import com.dspread.mdm.service.platform.api.model.SystemOperationResult
import com.dspread.mdm.service.modules.osupdate.OsUpdateStatusChecker
import com.dspread.mdm.service.platform.manager.WakeLockManager
import com.dspread.mdm.service.services.DspreadService
import com.dspread.sdkdevservice.aidl.deviceService.SDKDeviceService
import com.dspread.sdkdevservice.aidl.deviceInfo.SDKInstallListener

/**
 * 任务处理器
 * 处理 ST* 任务相关消息
 */
class TaskHandler(context: Context) : BaseMessageHandler(context) {

    private val deviceDataCollector by lazy { DeviceDataCollector(context) }

    companion object {
        private const val TAG = "TaskHandler"
    }

    override fun handleMessage(message: String) {
        val jsonObject = parseMessage(message) ?: return
        val messageInfo = getMessageInfo(jsonObject)
        val data = getDataFromMessage(jsonObject) ?: return
        
        Logger.task("处理任务消息: ${messageInfo.tranCode}")
        
        try {
            if (data.has("taskList")) {
                val taskList = data.getJSONArray("taskList")

                // 先更新任务列表到管理器
                val silentInstall = jsonObject.optString("silent_install", "")
                WsTaskManager.updateWSTaskList(messageInfo.requestId, messageInfo.requestTime, taskList, silentInstall)

                // 然后处理任务列表
                processTaskList(taskList, messageInfo)
            } else {
                Logger.wsmE("任务消息缺少 taskList 字段")
            }
        } catch (e: Exception) {
            Logger.wsmE("处理任务消息失败", e)
        }
    }

    /**
     * 处理任务列表
     */
    private fun processTaskList(taskList: JSONArray, messageInfo: BaseMessageHandler.MessageInfo) {
        Logger.task("开始处理任务列表，共 ${taskList.length()} 个任务")
        
        for (i in 0 until taskList.length()) {
            try {
                val task = taskList.getJSONObject(i)
                processTask(task, messageInfo)
            } catch (e: Exception) {
                Logger.wsmE("处理第 $i 个任务失败", e)
            }
        }
    }

    /**
     * 处理单个任务
     */
    private fun processTask(task: JSONObject, messageInfo: BaseMessageHandler.MessageInfo) {
        val taskId = task.optString("taskId")
        val taskType = task.optString("taskType")
        val appId = task.optString("appId")
        val packageName = task.optString("packName")
        val apkName = task.optString("apkName")
        val versionName = task.optString("versionName")
        val versionCode = task.optString("versionCode")
        val downloadUrl = task.optString("downloadUrl")
        val installBy = task.optString("installBy", "0") // 0: 任何网络, 1: 仅WiFi

        Logger.task("处理任务: taskId=$taskId, type=$taskType, package=$packageName, apk=$apkName")
        
        when (taskType) {
            TaskStateConstants.TASK_INSTALL -> {
                Logger.task("处理安装/更新任务: $taskId")
                handleInstallTask(task, messageInfo)
            }
            TaskStateConstants.TASK_UNINSTALL -> {
                Logger.task("处理卸载任务: $taskId")
                handleUninstallTask(task, messageInfo)
            }
            TaskStateConstants.TASK_UPDATE_PARAM -> {
                Logger.task("处理参数更新任务: $taskId")
                handleUpdateParamTask(task, messageInfo)
            }
            TaskStateConstants.TASK_OS_UPDATE -> {
                Logger.task("处理OS更新任务: $taskId")
                handleOSUpdateTask(task, messageInfo)
            }
            TaskStateConstants.TASK_SP_DOWNLOAD -> {
                Logger.task("处理SP下载任务: $taskId")
                handleSpDownloadTask(task, messageInfo)
            }
            else -> {
                Logger.wsmE("未知的任务类型: $taskType")
                uploadTaskResult(task, TaskStateConstants.RESULT_FAILED, "未知的任务类型", messageInfo)
            }
        }
    }

    /**
     * 处理安装任务
     */
    private fun handleInstallTask(task: JSONObject, messageInfo: BaseMessageHandler.MessageInfo) {
        val taskId = task.optString("taskId")
        val packageName = task.optString("packName")  // 注意：使用packName
        val apkName = task.optString("apkName")
        val versionName = task.optString("versionName")
        val versionCode = task.optString("versionCode")
        val downloadUrl = task.optString("url")  // 修正：使用url字段
        val appId = task.optString("appId")
        val allowDowngrade = task.optBoolean("allowDowngrade", false)
        val forceInstall = task.optBoolean("forceInstall", false)

        Logger.task("执行安装任务: $apkName ($packageName)")
        Logger.task("版本信息: $versionName($versionCode)")
        Logger.task("允许降级: $allowDowngrade, 强制安装: $forceInstall")

        try {
            // 第一步：应用安装状态检查
            val installStatus = checkAppInstallStatus(packageName, versionName, versionCode)
            Logger.task("应用安装状态检查结果: $installStatus")

            when (installStatus) {
                1 -> {
                    // 已安装且版本匹配，直接返回成功
                    Logger.task("应用已安装且版本匹配，直接返回成功")
                    uploadTaskResult(task, TaskStateConstants.INSTALL_SUCCESS, null, messageInfo)
                    return
                }
                2 -> {
                    // 已安装但版本更高，检查是否允许降级
                    if (!allowDowngrade) {
                        Logger.task("应用版本更高且不允许降级")
                        uploadTaskResult(task, TaskStateConstants.UPDATE_DOWNGRADE_FORBIDDEN, "不允许降级安装", messageInfo)
                        return
                    }
                    Logger.task("应用版本更高但允许降级，继续安装流程")
                }
                0 -> {
                    // 未安装或版本较低，继续安装流程
                    Logger.task("应用未安装或版本较低，继续安装流程")
                }
            }

            // 第二步：开始下载和安装流程
            downloadAndInstallApp(task, messageInfo, forceInstall)

        } catch (e: Exception) {
            Logger.taskE("安装任务执行失败", e)
            uploadTaskResult(task, TaskStateConstants.INSTALL_FAILED, e.message, messageInfo)
        }
    }

    /**
     * 检查应用安装状态
     * @return 0=未安装或版本较低, 1=已安装且版本匹配, 2=已安装但版本更高
     */
    private fun checkAppInstallStatus(packageName: String, versionName: String, versionCode: String): Int {
        return try {
            Logger.task("判断 packageName=$packageName versionCode=$versionCode versionName=$versionName 是否安装")

            val packageManager = context.packageManager
            val packageInfo = try {
                packageManager.getPackageInfo(packageName, 0)
            } catch (e: Exception) {
                null
            }

            if (packageInfo != null) {
                val installedVersionCode = packageInfo.versionCode
                val installedVersionName = packageInfo.versionName ?: ""
                val targetVersionCode = versionCode.toIntOrNull() ?: 0

                Logger.task("已安装版本: versionCode=$installedVersionCode, versionName=$installedVersionName")
                Logger.task("目标版本: versionCode=$targetVersionCode, versionName=$versionName")

                when {
                    // 版本完全匹配（包名、版本号、版本名都相同）
                    packageName == packageInfo.packageName &&
                    targetVersionCode == installedVersionCode &&
                    versionName == installedVersionName -> {
                        Logger.task("$packageName 已安装并且版本一致")
                        1
                    }
                    // 已安装更高版本
                    packageName == packageInfo.packageName &&
                    targetVersionCode < installedVersionCode -> {
                        Logger.task("$packageName 已安装高版本")
                        2
                    }
                    // 其他情况（版本较低或版本名不匹配）
                    else -> {
                        Logger.task("$packageName 需要安装/更新（当前版本较低或版本名不匹配）")
                        0
                    }
                }
            } else {
                Logger.task("$packageName 未安装")
                0
            }
        } catch (e: Exception) {
            Logger.taskE("检查应用安装状态失败: $packageName", e)
            0 // 出错时认为未安装
        }
    }

    /**
     * 处理卸载任务
     */
    /**
     * 处理卸载任务
     */
    private fun handleUninstallTask(task: JSONObject, messageInfo: BaseMessageHandler.MessageInfo) {
        val taskId = task.optString("taskId")
        val packageName = task.optString("packName")
        val apkName = task.optString("apkName")

        try {
            if (!AppManagerApi(context).isApplicationInstalled(packageName)) {
                // 应用不存在，视为卸载成功
                uploadTaskResult(task, TaskStateConstants.UNINSTALL_SUCCESS, null, messageInfo)
                return
            }

            // TaskHandler需要立即响应，所以仍使用回调版本
            // 但可以简化回调逻辑，主要依赖系统广播作为备用
            AppManagerApi(context).uninstallAppSilently(packageName) { _, returnCode, errorMsg ->
                val result = if (returnCode == 1) TaskStateConstants.UNINSTALL_SUCCESS else TaskStateConstants.UNINSTALL_FAILED
                val finalErrorMsg = if (returnCode == 1) null else (errorMsg ?: "卸载失败")

                // 卸载成功后通知应用状态变化
                if (returnCode == 1) {
                    Logger.task("卸载成功，通知应用状态变化")
                    deviceDataCollector.onAppStateChanged("UNINSTALL", packageName)
                }

                uploadTaskResult(task, result, finalErrorMsg, messageInfo)
            }

        } catch (e: Exception) {
            uploadTaskResult(task, TaskStateConstants.UNINSTALL_FAILED, e.message, messageInfo)
        }
    }

    /**
     * 处理更新任务
     */
    private fun handleUpdateTask(task: JSONObject, messageInfo: BaseMessageHandler.MessageInfo) {
        val packageName = task.optString("packageName")
        val downloadUrl = task.optString("downloadUrl")
        
        Logger.task("执行更新任务: $packageName")
        
        try {
            // 检查应用是否已安装
            if (!isAppInstalled(packageName)) {
                Logger.task("应用未安装，转为安装任务: $packageName")
                handleInstallTask(task, messageInfo)
                return
            }
            
            // 检查版本是否需要更新
            if (!needUpdate(task)) {
                Logger.task("应用版本已是最新: $packageName")
                uploadTaskResult(task, TaskStateConstants.RESULT_SUCCESS, null, messageInfo)
                return
            }

            // 开始下载和更新
            downloadAndInstallApp(task, messageInfo)

        } catch (e: Exception) {
            Logger.taskE("更新任务执行失败", e)
            uploadTaskResult(task, TaskStateConstants.RESULT_FAILED, e.message, messageInfo)
        }
    }

    private fun handleEnableTask(task: JSONObject, messageInfo: BaseMessageHandler.MessageInfo) {
        uploadTaskResult(task, TaskStateConstants.RESULT_FAILED, "启用功能暂未实现", messageInfo)
    }

    private fun handleDisableTask(task: JSONObject, messageInfo: BaseMessageHandler.MessageInfo) {
        uploadTaskResult(task, TaskStateConstants.RESULT_FAILED, "禁用功能暂未实现", messageInfo)
    }

    /**
     * 下载并安装应用
     */
    /**
     * 下载和安装应用
     */
    private fun downloadAndInstallApp(task: JSONObject, messageInfo: BaseMessageHandler.MessageInfo, forceInstall: Boolean = false) {
        val taskId = task.optString("taskId")
        val packageName = task.optString("packName")
        val apkName = task.optString("apkName")
        val versionName = task.optString("versionName")
        val versionCode = task.optString("versionCode")
        val downloadUrl = task.optString("url")  // 使用正确的字段名
        val apkMd5 = task.optString("apkMd5")
        val apkSize = task.optLong("apkSize")

        Logger.task("开始下载和安装应用: $apkName")
        Logger.task("下载URL: $downloadUrl")
        Logger.task("包名: $packageName, 版本: $versionName($versionCode)")

        try {
            // 第一步：检查应用安装状态
            val installStatus = checkAppInstallStatus(packageName, versionName, versionCode)
            Logger.task("检查应用安装状态: $packageName = $installStatus")

            // 只有在已安装高版本且不允许降级时才跳过安装
            if (installStatus == 2) {
                val allowDowngrade = task.optBoolean("allowDowngrade", false)
                if (!allowDowngrade) {
                    Logger.task("已安装高版本且不允许降级，返回降级禁止状态")
                    uploadTaskResult(task, TaskStateConstants.UPDATE_DOWNGRADE_FORBIDDEN, "不允许降级安装", messageInfo)
                    return
                } else {
                    Logger.task("已安装高版本但允许降级，继续安装流程")
                }
            } else {
                // installStatus == 0 (未安装或版本较低) 或 installStatus == 1 (版本匹配)
                // 都继续安装流程
                Logger.task("继续安装流程: status=$installStatus")
            }

            // 检查下载URL
            if (downloadUrl.isBlank()) {
                Logger.task("缺少下载URL，安装失败")
                uploadTaskResult(task, TaskStateConstants.INSTALL_FAILED, "缺少下载URL", messageInfo)
                return
            }

            // 第一步：上报下载中状态
            uploadTaskResult(task, TaskStateConstants.DOWNLOAD_ING, null, messageInfo)

            // 第二步：开始下载APK文件
            downloadApkFile(task, messageInfo, downloadUrl, apkMd5, apkSize, forceInstall)

        } catch (e: Exception) {
            Logger.taskE("下载安装过程失败", e)
            uploadTaskResult(task, TaskStateConstants.INSTALL_FAILED, e.message, messageInfo)
        }
    }

    /**
     * 下载APK文件
     */
    private fun downloadApkFile(
        task: JSONObject,
        messageInfo: BaseMessageHandler.MessageInfo,
        downloadUrl: String,
        expectedMd5: String,
        expectedSize: Long,
        forceInstall: Boolean
    ) {
        val taskId = task.optString("taskId")
        val packageName = task.optString("packName")
        val apkName = task.optString("apkName")

        // 创建下载目录和文件路径
        // 使用外部存储应用专用目录：/sdcard/Android/data/com.dspread.mdm.service/files/apk/

        // 直接在这里清理文件名，确保生效
        val cleanTaskId = taskId.replace("[^a-zA-Z0-9._-]".toRegex(), "_")
        val storageApi = StorageApi(context)
        val downloadDir = storageApi.getApkDownloadDir()
        val apkFile = File(downloadDir, "$cleanTaskId.apk")

        Logger.task("原始taskId: $taskId")
        Logger.task("清理后taskId: $cleanTaskId")
        Logger.task("清理后APK路径: ${apkFile.absolutePath}")
        Logger.task("APK文件名: ${apkFile.name}")

        val wakeLockTag = "download_$taskId"

        // 异步下载
        Thread {
            try {
                // 获取WakeLock防止下载过程中CPU休眠
                WakeLockManager.acquireWakeLock(context, wakeLockTag, 10 * 60 * 1000L) // 10分钟超时

                val success = HttpDownloader.fileDownloadByUrlWithRetry(
                    downloadUrl,
                    apkFile.absolutePath,
                    expectedSize,
                    expectedMd5,
                    object : HttpDownloader.FileDownloadCallBack {
                        override fun requestSuccess() {
                            Logger.task("APK下载成功: ${apkFile.absolutePath}")

                            // 第三步：上报下载成功状态
                            uploadTaskResult(task, TaskStateConstants.DOWNLOAD_SUCCESS, null, messageInfo)

                            // 第四步：设备状态判断
                            val shouldInstallNow = shouldInstallImmediately(forceInstall)
                            if (shouldInstallNow) {
                                // 立即安装
                                Logger.task("设备空闲或强制安装，立即开始安装")
                                installDownloadedApk(task, messageInfo, apkFile.absolutePath)
                            } else {
                                // 等待安装
                                Logger.task("设备忙碌，等待用户确认安装")
                                uploadTaskResult(task, TaskStateConstants.INSTALL_WAITING, null, messageInfo)
                                // TODO: 发送广播通知用户有应用等待安装
                                // Helpers.sendBroad(context, BroadcastActions.APP_PLUS_DOWNLOAD_COMPLETED)
                            }
                        }

                        override fun requestFail(errorCode: Int, errorStr: String) {
                            Logger.taskE("APK下载失败: $errorStr (错误码: $errorCode)")

                            // 上报下载失败状态
                            uploadTaskResult(task, TaskStateConstants.DOWNLOAD_FAILED, errorStr, messageInfo)
                        }

                        override fun onDownloading(curFileSize: Long, fileSize: Long) {
                            val percentage = if (fileSize > 0) {
                                ((curFileSize * 100) / fileSize).toInt()
                            } else 0

                            // 只记录关键进度点，减少日志量
                            if (percentage % 10 == 0 || percentage == 100) {
                                Logger.task("应用下载进度: $percentage%")
                            }
                        }
                    },
                    context
                )

                if (!success) {
                    Logger.taskE("下载失败，未调用回调")
                }

            } catch (e: Exception) {
                Logger.taskE("下载过程异常", e)
                uploadTaskResult(task, TaskStateConstants.DOWNLOAD_FAILED, e.message, messageInfo)

            } finally {
                // 释放WakeLock
                WakeLockManager.releaseWakeLock(wakeLockTag)
            }
        }.start()
    }

    /**
     * 安装已下载的APK
     */
    private fun installDownloadedApk(
        task: JSONObject,
        messageInfo: BaseMessageHandler.MessageInfo,
        apkPath: String
    ) {
        val taskId = task.optString("taskId")
        val packageName = task.optString("packName")
        val apkName = task.optString("apkName")

        val wakeLockTag = "install_$taskId"
        try {
            // 获取WakeLock防止安装过程中CPU休眠
            WakeLockManager.acquireWakeLock(context, wakeLockTag, 5 * 60 * 1000L) // 5分钟超时

            // 第五步：安装前再次检查应用状态
            val versionName = task.optString("versionName")
            val versionCode = task.optString("versionCode")
            val installStatus = checkAppInstallStatus(packageName, versionName, versionCode)
            Logger.task("检查应用安装状态: $packageName = $installStatus")

            // 只有在已安装高版本且不允许降级时才跳过安装
            if (installStatus == 2) {
                val allowDowngrade = task.optBoolean("allowDowngrade", false)
                if (!allowDowngrade) {
                    Logger.task("已安装高版本且不允许降级，跳过安装")
                    uploadTaskResult(task, TaskStateConstants.UPDATE_DOWNGRADE_FORBIDDEN, "不允许降级安装", messageInfo)

                    // 删除APK文件
                    try {
                        if (File(apkPath).delete()) {
                            Logger.task("已删除APK文件: $apkPath")
                        }
                    } catch (e: Exception) {
                        Logger.taskE("删除APK文件失败: $apkPath", e)
                    }

                    // 释放WakeLock
                    WakeLockManager.releaseWakeLock(wakeLockTag)
                    return
                } else {
                    Logger.task("已安装高版本但允许降级，继续安装")
                }
            } else {
                // installStatus == 0 (未安装或版本较低) 或 installStatus == 1 (版本匹配)
                // 都继续安装流程
                Logger.task("继续安装流程: status=$installStatus")
            }

            // 第六步：上报安装中状态
            uploadTaskResult(task, TaskStateConstants.INSTALL_ING, null, messageInfo)

            // 第七步：直接安装APK文件（无需验签）
            Logger.task("开始安装APK: $apkPath")
            AppManagerApi(context).installApkSilently(apkPath) { pkg, returnCode, errorMsg ->
                Logger.task("安装回调: pkg=$pkg, returnCode=$returnCode, error=$errorMsg")

                try {
                    // 第八步：处理安装结果
                    if (returnCode == 1) {
                        // 安装成功
                        Logger.task("安装成功: $packageName")

                        // 删除APK文件
                        try {
                            File(apkPath).delete()
                            Logger.task("已删除APK文件: $apkPath")
                        } catch (e: Exception) {
                            Logger.task("删除APK文件失败: $apkPath - ${e.message}")
                        }

                        // 通知应用状态变化
                        deviceDataCollector.onAppStateChanged("INSTALL", packageName)

                        // 上报安装成功
                        uploadTaskResult(task, TaskStateConstants.INSTALL_SUCCESS, null, messageInfo)

                    } else {
                        // 安装失败
                        Logger.taskE("安装失败: $packageName, error=$errorMsg")

                        // 删除APK文件
                        try {
                            File(apkPath).delete()
                        } catch (e: Exception) {
                            Logger.task("删除APK文件失败: $apkPath - ${e.message}")
                        }

                        // 上报安装失败
                        uploadTaskResult(task, TaskStateConstants.INSTALL_FAILED, errorMsg ?: "安装失败", messageInfo)
                    }
                } finally {
                    // 安装完成后释放WakeLock
                    WakeLockManager.releaseWakeLock(wakeLockTag)
                }
            }

        } catch (e: Exception) {
            Logger.taskE("安装过程失败", e)
            uploadTaskResult(task, TaskStateConstants.INSTALL_FAILED, e.message, messageInfo)
            // 异常时也要释放WakeLock
            WakeLockManager.releaseWakeLock(wakeLockTag)
        }
    }

    /**
     * 上传任务结果
     */
    private fun uploadTaskResult(
        task: JSONObject,
        result: String,
        errorMsg: String?,
        messageInfo: BaseMessageHandler.MessageInfo
    ) {
        val taskId = task.optString("taskId")
        val appId = task.optString("appId")

        // 安全地更新任务状态，避免状态串改
        // 使用WsTaskManager的专用方法来更新状态，确保线程安全和数据一致性
        WsTaskManager.updateWSTaskState(taskId, result)

        // 如果有错误信息，单独更新
        if (errorMsg != null) {
            WsTaskManager.updateTaskErrorMessage(taskId, errorMsg)
        }

        // 更新最后更新时间（用于超时检测）
        WsTaskManager.updateTaskLastUpdateTime(taskId)

        WsMessageSender.uploadTaskResult(
            taskId = taskId,
            taskResult = result,
            errorMsg = errorMsg,
            appId = appId,
            orgRequestId = messageInfo.requestId,
            orgRequestTime = messageInfo.requestTime
        )
    }

    /**
     * 检查应用是否已安装
     */
    private fun isAppInstalled(packageName: String): Boolean {
        return try {
            val appManagerApi = AppManagerApi(context)
            val isInstalled = appManagerApi.isApplicationInstalled(packageName)
            Logger.task("检查应用安装状态: $packageName = $isInstalled")
            isInstalled
        } catch (e: Exception) {
            Logger.taskE("检查应用安装状态失败: $packageName", e)
            false
        }
    }

    /**
     * 判断是否应该立即安装
     */
    private fun shouldInstallImmediately(forceInstall: Boolean): Boolean {
        return try {
            // 强制安装直接返回true
            if (forceInstall) {
                Logger.task("强制安装模式，立即安装")
                return true
            }

            // 检查设备是否空闲
            val isDeviceIdle = isDeviceIdle()
            Logger.task("设备空闲状态: $isDeviceIdle")

            // 设备空闲时立即安装
            isDeviceIdle

        } catch (e: Exception) {
            Logger.taskE("判断设备状态失败", e)
            // 出错时默认立即安装
            true
        }
    }

    /**
     * 检查设备是否空闲
     */
    private fun isDeviceIdle(): Boolean {
        return try {
            // 简化版本：暂时总是返回true，表示立即安装
            // 在实际部署时，可以集成用户交互监控逻辑
            // 5分钟无用户交互即认为设备空闲
            true
        } catch (e: Exception) {
            Logger.taskE("检查设备空闲状态失败", e)
            true
        }
    }

    /**
     * 检查是否需要更新
     */
    private fun needUpdate(task: JSONObject): Boolean {
        // 这里需要实现版本比较逻辑
        return true
    }



    /**
     * 处理参数更新任务
     */
    private fun handleUpdateParamTask(task: JSONObject, messageInfo: BaseMessageHandler.MessageInfo) {
        val taskId = task.optString("taskId")
        Logger.task("执行参数更新任务: $taskId")

        try {
            // TODO: 实现参数更新逻辑
            Logger.task("参数更新任务暂未实现: $taskId")
            uploadTaskResult(task, TaskStateConstants.RESULT_SUCCESS, "参数更新任务暂未实现", messageInfo)

        } catch (e: Exception) {
            Logger.taskE("参数更新任务执行失败", e)
            uploadTaskResult(task, TaskStateConstants.RESULT_FAILED, e.message, messageInfo)
        }
    }

    /**
     * 处理OS更新任务
     */
    private fun handleOSUpdateTask(task: JSONObject, messageInfo: BaseMessageHandler.MessageInfo) {
        val taskId = task.optString("taskId")
        val displayVer = task.optString("displayVer", "")
        val url = task.optString("url", "")
        val fileName = task.optString("fileName", "")
        val fileMd5 = task.optString("fileMd5", "")
        val fileSize = task.optString("fileSize", "0").toLongOrNull() ?: 0L

        Logger.task("执行OS更新任务: $taskId")
        Logger.task("目标版本: $displayVer, 文件: $fileName")

        try {
            // 检查当前版本是否已经是目标版本
            val currentVersion = getCurrentSystemVersion()
            if (currentVersion == displayVer) {
                Logger.task("当前版本已是目标版本，直接返回成功")
                uploadTaskResult(task, TaskStateConstants.UPDATE_SUCCESS, "版本已是最新", messageInfo)
                return
            }

            // Android 14+特殊检查：防止安装过旧的升级包和状态冲突
            if (Build.VERSION.SDK_INT >= 34) {
                Logger.task("Android 14+设备，执行升级前检查...")

                // 检查系统更新状态
                val systemUpdateApi = SystemUpdateApi(context)
                Logger.task("SystemUpdateApi已初始化")

                Logger.task("注意：Android 14+对升级包时间戳有严格要求")
                Logger.task("如果升级失败，可能需要更新的升级包")
            }

            // 启动OS升级流程
            startOSUpgradeProcess(task, messageInfo)

        } catch (e: Exception) {
            Logger.taskE("OS更新任务执行失败", e)
            uploadTaskResult(task, TaskStateConstants.UPDATE_FAILED, e.message, messageInfo)
        }
    }

    /**
     * 启动OS升级流程
     * 下载 -> 验证 -> 弹框确认 -> 升级
     */
    private fun startOSUpgradeProcess(task: JSONObject, messageInfo: BaseMessageHandler.MessageInfo) {
        val taskId = task.optString("taskId")
        val url = task.optString("url", "")
        val fileName = task.optString("fileName", "")
        val fileMd5 = task.optString("fileMd5", "")
        val fileSize = task.optString("fileSize", "0").toLongOrNull() ?: 0L

        Logger.task("开始OS升级流程: $taskId")

        // 更新任务状态为下载中
        uploadTaskResult(task, TaskStateConstants.DOWNLOAD_ING, "开始下载升级包", messageInfo)

        // 启动异步下载和升级流程
        GlobalScope.launch(Dispatchers.IO) {
            try {
                // 1. 下载升级包
                val downloadPath = downloadOSUpdateFile(url, fileName, fileSize, fileMd5, taskId, messageInfo)
                if (downloadPath == null) {
                    uploadTaskResult(task, TaskStateConstants.DOWNLOAD_FAILED, "下载失败", messageInfo)
                    return@launch
                }

                // 2. 验证升级包
                if (!verifyOSUpdateFile(downloadPath, fileMd5)) {
                    uploadTaskResult(task, TaskStateConstants.DOWNLOAD_FAILED, "文件验证失败", messageInfo)
                    return@launch
                }

                uploadTaskResult(task, TaskStateConstants.DOWNLOAD_SUCCESS, "下载完成", messageInfo)

                // 3. 版本兼容性检查
                val versionCheckResult = checkOSUpdateVersionCompatibility(downloadPath, task)
                if (!versionCheckResult) {
                    uploadTaskResult(task, TaskStateConstants.UPDATE_FAILED, "版本检查失败", messageInfo)
                    return@launch
                }

                // 4. 显示升级确认弹框（5分钟倒计时）
                showOSUpgradeDialog(task, downloadPath, messageInfo)

            } catch (e: Exception) {
                Logger.taskE("OS升级流程失败", e)
                uploadTaskResult(task, TaskStateConstants.UPDATE_FAILED, e.message, messageInfo)
            }
        }
    }

    /**
     * 获取当前系统版本
     */
    private fun getCurrentSystemVersion(): String {
        return Build.DISPLAY ?: ""
    }

    /**
     * 下载OS升级文件
     */
    private suspend fun downloadOSUpdateFile(
        url: String,
        fileName: String,
        fileSize: Long,
        fileMd5: String,
        taskId: String,
        messageInfo: BaseMessageHandler.MessageInfo
    ): String? {
        return try {
            val downloadDir = File("/sdcard/Android/data/${context.packageName}/files/updates")
            if (!downloadDir.exists()) {
                downloadDir.mkdirs()
            }

            val downloadPath = File(downloadDir, fileName).absolutePath
            Logger.task("开始下载OS升级文件: $url -> $downloadPath")

            // 使用HttpDownloader下载文件
            var downloadSuccess = false
            var lastReportedProgress = -1  // 上次上报的进度，避免重复上报

            val callback = object : HttpDownloader.FileDownloadCallBack {
                override fun requestSuccess() {
                    downloadSuccess = true
                    Logger.task("OS升级文件下载成功: $downloadPath")
                }

                override fun requestFail(errorCode: Int, errorStr: String) {
                    Logger.taskE("OS升级文件下载失败: $errorCode - $errorStr")
                }

                override fun onDownloading(curFileSize: Long, fileSize: Long) {
                    val progress = if (fileSize > 0) (curFileSize * 100 / fileSize).toInt() else 0

                    // 只记录进度到本地日志，不上报WebSocket
                    if (progress != lastReportedProgress && (progress % 10 == 0 || progress == 100)) {
                        lastReportedProgress = progress
                        Logger.task("OS下载进度: $progress%")
                    }
                }
            }

            val success = HttpDownloader.fileDownloadByUrlWithRetry(url, downloadPath, fileSize, fileMd5, callback, context)

            if (success && downloadSuccess) {
                Logger.task("OS升级文件下载完成: $downloadPath")
                downloadPath
            } else {
                Logger.taskE("OS升级文件下载失败")
                null
            }

        } catch (e: Exception) {
            Logger.taskE("下载OS升级文件异常", e)
            null
        }
    }

    /**
     * 检查OS升级版本兼容性
     */
    private fun checkOSUpdateVersionCompatibility(updateFilePath: String, task: JSONObject): Boolean {
        return try {
            val updateFile = File(updateFilePath)
            val systemUpdateApi = SystemUpdateApi(context)

            // 使用SystemUpdateApi进行版本检查
            val versionCheckResult = systemUpdateApi.checkVersionCompatibility(updateFile)

            val taskId = task.optString("taskId")
            val targetVersion = task.optString("displayVer", "")

            Logger.task("版本兼容性检查: taskId=$taskId")
            Logger.task("当前版本: ${versionCheckResult.currentVersion}")
            Logger.task("升级包版本: ${versionCheckResult.updateVersion}")
            Logger.task("目标版本: $targetVersion")
            Logger.task("检查状态: ${versionCheckResult.status}")

            when (versionCheckResult.status) {
                SystemUpdateApi.VersionCheckStatus.SAME_VERSION -> {
                    Logger.task("警告：升级包版本与当前系统版本相同，继续升级（可能是强制重装）")
                    true // TaskHandler中通常允许相同版本升级
                }
                SystemUpdateApi.VersionCheckStatus.OLDER_VERSION -> {
                    Logger.task("警告：升级包版本低于当前系统版本，这是降级操作")
                    // 在生产环境中可能需要更严格的检查
                    true // 暂时允许降级，可根据需求调整
                }
                SystemUpdateApi.VersionCheckStatus.NEWER_VERSION -> {
                    Logger.task("升级包版本高于当前系统版本，正常升级")
                    true
                }
                SystemUpdateApi.VersionCheckStatus.UNKNOWN -> {
                    Logger.task("无法确定版本关系，继续升级")
                    true
                }
            }

        } catch (e: Exception) {
            Logger.taskE("版本兼容性检查失败", e)
            true // 检查失败时允许继续升级，避免阻塞正常流程
        }
    }

    /**
     * 验证OS升级文件
     */
    private fun verifyOSUpdateFile(filePath: String, expectedMd5: String): Boolean {
        if (expectedMd5.isEmpty()) {
            Logger.task("未提供MD5校验值，跳过验证")
            return true
        }

        return try {
            val file = File(filePath)
            if (!file.exists()) {
                Logger.taskE("升级文件不存在: $filePath")
                return false
            }

            val md5 = calculateFileMD5(file)
            val isValid = md5.equals(expectedMd5, ignoreCase = true)

            Logger.task("文件MD5验证: 期望=$expectedMd5, 实际=$md5, 结果=${if (isValid) "通过" else "失败"}")
            isValid

        } catch (e: Exception) {
            Logger.taskE("验证OS升级文件失败", e)
            false
        }
    }

    /**
     * 计算文件MD5
     */
    private fun calculateFileMD5(file: File): String {
        val md = MessageDigest.getInstance("MD5")
        FileInputStream(file).use { fis ->
            val buffer = ByteArray(8192)
            var length: Int
            while (fis.read(buffer).also { length = it } != -1) {
                md.update(buffer, 0, length)
            }
        }

        val digest = md.digest()
        return digest.joinToString("") { "%02x".format(it) }
    }

    /**
     * 显示OS升级确认弹框
     * 根据Android版本调整用户体验
     */
    private fun showOSUpgradeDialog(
        task: JSONObject,
        updateFilePath: String,
        messageInfo: BaseMessageHandler.MessageInfo
    ) {
        val taskId = task.optString("taskId")

        // 根据Android版本确定升级特点
        val (title, message, buttonText) = when {
            Build.VERSION.SDK_INT >= 34 -> {
                // Android 14+: 支持的UpdateEngine A/B分区升级
                // 需要2-3分钟后台处理（验证数据完整性 + 准备分区切换 + 最终系统配置）
                Triple(
                    "系统升级提示",
                    "检测到新的系统版本，升级包已准备就绪。\n\n" +
                    "升级将在后台进行约3-5分钟，完成后自动重启。\n\n" +
                    "系统将在5分钟后自动开始升级。",
                    "开始升级"
                )
            }
            else -> {
                // Android < 14: 使用传统Recovery升级方式
                // 重启后在Recovery模式中完成升级，用户看不到进度
                Triple(
                    "系统升级提示",
                    "检测到新的系统版本，升级包已准备就绪。\n\n" +
                    "设备将重启进入升级模式，升级完成后自动重启。\n\n" +
                    "系统将在5分钟后自动开始升级。",
                    "开始升级"
                )
            }
        }

        // 在主线程显示弹框
        GlobalScope.launch(Dispatchers.Main) {
            try {
                OsUpgradeDialog.showOSUpgradeDialog(
                    context = context,
                    title = title,
                    message = message,
                    onDelayClick = {
                        // 用户选择延迟，5分钟后自动升级
                        Logger.task("用户选择延迟升级，5分钟后自动执行")
                        GlobalScope.launch {
                            delay(300000) // 5分钟 = 300秒
                            executeOSUpgrade(task, updateFilePath, messageInfo)
                        }
                    },
                    onInstallClick = {
                        // 用户选择立即升级
                        Logger.task("用户选择立即升级")
                        GlobalScope.launch {
                            executeOSUpgrade(task, updateFilePath, messageInfo)
                        }
                    }
                )
            } catch (e: Exception) {
                Logger.taskE("显示升级弹框失败", e)
                // 弹框失败时直接执行升级
                GlobalScope.launch {
                    executeOSUpgrade(task, updateFilePath, messageInfo)
                }
            }
        }
    }

    /**
     * 执行OS升级
     */
    private suspend fun executeOSUpgrade(
        task: JSONObject,
        updateFilePath: String,
        messageInfo: BaseMessageHandler.MessageInfo
    ) {
        val taskId = task.optString("taskId")
        val targetVersion = task.optString("displayVer", "")

        try {
            Logger.task("开始执行OS升级: $taskId, 目标版本: $targetVersion")

            // 保存升级任务信息，用于重启后状态检查
            OsUpdateStatusChecker.saveOsUpdateTaskInfo(context, taskId, targetVersion)

            // 保存升级前的系统版本
            OsUpdateStatusChecker.savePreUpgradeVersion(context)

            uploadTaskResult(task, "C02", "开始升级", messageInfo)

            // 使用SystemUpdateApi执行升级
            val systemUpdateApi = SystemUpdateApi(context)

            val updateListener = object : SystemUpdateApi.UpdateStatusListener {
                override fun onStatusUpdate(status: String, progress: Int) {
                    Logger.task("升级进度: $status ($progress%)")

                    // 上报进度到平台
                    val progressPercent = progress.coerceIn(0, 100)
                    uploadTaskResult(task, "C02", "$status ($progressPercent%)", messageInfo)
                }

                override fun onUpdateCompleted(success: Boolean, message: String) {
                    if (success) {
                        val successMsg = when {
                            Build.VERSION.SDK_INT >= 34 -> {
                                // Android 14+: UpdateEngine A/B分区升级完成
                                "UpdateEngine A/B分区升级完成，即将重启"
                            }
                            else -> {
                                // Android < 14: Recovery升级包准备完成
                                "升级包已准备，即将重启进入Recovery升级模式"
                            }
                        }
                        Logger.task("OS升级完成: $taskId")
                        uploadTaskResult(task, TaskStateConstants.UPDATE_SUCCESS, successMsg, messageInfo)
                    } else {
                        Logger.taskE("OS升级失败: $taskId")
                        uploadTaskResult(task, TaskStateConstants.UPDATE_FAILED, "升级失败", messageInfo)
                    }
                }
            }

            // 执行升级 - 使用新的多版本兼容策略
            val result = systemUpdateApi.installOtaUpdate(updateFilePath, taskId, updateListener)

            if (!result.isSuccess) {
                val errorMsg = when (result) {
                    is SystemOperationResult.Failure -> result.error
                    else -> "升级启动失败"
                }
                Logger.taskE("启动OS升级失败: $errorMsg")
                uploadTaskResult(task, TaskStateConstants.UPDATE_FAILED, errorMsg, messageInfo)
            }

        } catch (e: Exception) {
            Logger.taskE("执行OS升级异常", e)
            uploadTaskResult(task, TaskStateConstants.UPDATE_FAILED, e.message, messageInfo)
        }
    }

    /**
     * 从messageInfo创建task对象（用于进度上报）
     */
    private fun createTaskFromInfo(taskId: String, messageInfo: BaseMessageHandler.MessageInfo): JSONObject {
        return JSONObject().apply {
            put("taskId", taskId)
        }
    }

    /**
     * 处理SP升级任务（包含下载和升级）
     */
    private fun handleSpDownloadTask(task: JSONObject, messageInfo: BaseMessageHandler.MessageInfo) {
        val taskId = task.optString("taskId")
        val fileName = task.optString("fileName")
        val downloadUrl = task.optString("url")
        val fileSize = task.optString("fileSize", "0").toLongOrNull() ?: 0L
        val fileMd5 = task.optString("fileMd5")

        Logger.task("执行SP升级任务: $fileName")
        Logger.task("下载URL: $downloadUrl")
        Logger.task("文件大小: $fileSize, MD5: $fileMd5")

        uploadTaskResult(task, TaskStateConstants.DOWNLOAD_ING, "开始下载SP文件", messageInfo)

        GlobalScope.launch(Dispatchers.IO) {
            try {
                // 1. 下载SP文件
                val downloadPath = downloadSpFile(downloadUrl, fileName, fileSize, fileMd5, taskId, messageInfo)
                if (downloadPath == null) {
                    uploadTaskResult(task, TaskStateConstants.DOWNLOAD_FAILED, "SP文件下载失败", messageInfo)
                    return@launch
                }

                // 2. 下载成功，上报下载完成状态
                uploadTaskResult(task, TaskStateConstants.DOWNLOAD_SUCCESS, "SP文件下载完成", messageInfo)

                // 3. 执行SP升级
                executeSpUpgrade(task, downloadPath, messageInfo)

            } catch (e: Exception) {
                Logger.taskE("SP升级流程失败", e)
                uploadTaskResult(task, TaskStateConstants.UPDATE_FAILED, "SP升级任务异常: ${e.message}", messageInfo)
            }
        }
    }

    /**
     * 下载SP文件
     */
    private suspend fun downloadSpFile(
        url: String,
        fileName: String,
        fileSize: Long,
        fileMd5: String,
        taskId: String,
        messageInfo: BaseMessageHandler.MessageInfo
    ): String? {
        return try {
            // 使用平台推送的原始文件名，保持原始名称和后缀
            val downloadDir = File("/sdcard/download")
            if (!downloadDir.exists()) {
                downloadDir.mkdirs()
            }

            // 如果平台没有提供文件名，则使用默认名称
            val actualFileName = if (fileName.isNotEmpty()) fileName else "sp_update.zip"
            val downloadPath = File(downloadDir, actualFileName).absolutePath
            Logger.task("开始下载SP文件: $url -> $downloadPath")

            var downloadSuccess = false
            var lastReportedProgress = -1
            var retryCount = 0

            val callback = object : HttpDownloader.FileDownloadCallBack {
                override fun requestSuccess() {
                    downloadSuccess = true
                    Logger.task("SP文件下载成功: $downloadPath")
                }

                override fun requestFail(errorCode: Int, errorStr: String) {
                    retryCount++
                    Logger.taskE("SP文件下载失败: $errorCode - $errorStr (第${retryCount}次)")

                    // 前两次失败要上报，第三次失败不在这里处理
                    if (retryCount <= 2) {
                        val task = createTaskFromInfo(taskId, messageInfo)
                        uploadTaskResult(task, TaskStateConstants.DOWNLOAD_FAILED, "下载重试中 (第${retryCount}次失败)", messageInfo)
                    }
                }

                override fun onDownloading(curFileSize: Long, fileSize: Long) {
                    val progress = if (fileSize > 0) (curFileSize * 100 / fileSize).toInt() else 0

                    if (progress != lastReportedProgress && (progress % 10 == 0 || progress == 100)) {
                        lastReportedProgress = progress
                        Logger.task("SP下载进度: $progress%")
                    }
                }
            }

            val success = HttpDownloader.fileDownloadByUrlWithRetry(url, downloadPath, fileSize, fileMd5, callback, context)

            if (success && downloadSuccess) {
                Logger.task("SP文件下载完成: $downloadPath")
                downloadPath
            } else {
                Logger.taskE("SP文件下载最终失败")
                null
            }

        } catch (e: Exception) {
            Logger.taskE("下载SP文件异常", e)
            null
        }
    }

    /**
     * 执行SP升级
     */
    private suspend fun executeSpUpgrade(
        task: JSONObject,
        spFilePath: String,
        messageInfo: BaseMessageHandler.MessageInfo
    ) {
        val taskId = task.optString("taskId")

        try {
            Logger.task("开始执行SP升级: $taskId, 文件路径: $spFilePath")

            // 上报升级开始状态
            uploadTaskResult(task, TaskStateConstants.UPDATE_ING, "开始SP升级", messageInfo)

            // 获取设备管理器并执行SP升级
            val deviceManager = getDeviceManager()
            if (deviceManager == null) {
                Logger.taskE("deviceManager为null，无法执行SP升级")
                uploadTaskResult(task, TaskStateConstants.UPDATE_FAILED, "设备管理器不可用", messageInfo)
                return
            }

            // 创建升级回调
            val spUpgradeCallback = createSpUpgradeCallback(task, messageInfo)

            // 调用DSPREAD服务执行SP升级
            deviceManager.installSp(spFilePath, 1, spUpgradeCallback)

        } catch (e: Exception) {
            Logger.taskE("执行SP升级失败", e)
            uploadTaskResult(task, TaskStateConstants.UPDATE_FAILED, "SP升级执行异常: ${e.message}", messageInfo)
        }
    }

    /**
     * 获取设备管理器
     */
    private fun getDeviceManager(): SDKDeviceService? {
        return try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE) {
                // Android 14及以上，使用DspreadService
                DspreadService.getDeviceService()
            } else {
                // Android 14以下，暂不支持SP升级
                Logger.taskE("Android 14以下设备暂不支持SP升级")
                null
            }
        } catch (e: Exception) {
            Logger.taskE("获取设备管理器失败", e)
            null
        }
    }

    /**
     * 创建SP升级回调
     */
    private fun createSpUpgradeCallback(
        task: JSONObject,
        messageInfo: BaseMessageHandler.MessageInfo
    ): SDKInstallListener.Stub {
        return object : SDKInstallListener.Stub() {
            override fun onProgress(current: Long, total: Long, percent: Int) {
                Logger.task("SP升级进度: $percent% ($current/$total)")
                // 可以选择性上报进度
            }

            override fun onError(code: Int, msg: String?) {
                Logger.taskE("SP升级失败: code=$code, msg=$msg")
                val errorMessage = "SP升级失败: $msg (错误码: $code)"
                uploadTaskResult(task, TaskStateConstants.UPDATE_FAILED, errorMessage, messageInfo)
            }

            override fun onSuccess() {
                Logger.task("SP升级成功")
                uploadTaskResult(task, TaskStateConstants.UPDATE_SUCCESS, "SP升级成功", messageInfo)
            }
        }
    }
}