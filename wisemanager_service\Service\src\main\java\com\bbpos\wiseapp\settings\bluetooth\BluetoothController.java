package com.bbpos.wiseapp.settings.bluetooth;

import android.bluetooth.BluetoothAdapter;
import android.bluetooth.BluetoothDevice;
import android.bluetooth.BluetoothSocket;
import android.content.Context;
import android.content.Intent;
import android.util.Log;

import com.bbpos.wiseapp.logger.BBLog;
import com.bbpos.wiseapp.service.common.RequestPermission;
import com.bbpos.wiseapp.settings.utils.PhoneDevicesInfo;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * Created by <PERSON> on 2018/12/29.
 */

public class BluetoothController {
    private static final UUID BT_UUID_SECURE = UUID.fromString("00001101-0000-1000-8000-00805F9B34FB");
    private static BluetoothDevice remoteDevice;

    private BluetoothAdapter mAdapter = null;
    private  ConnectThread mConnectThread = null;

    private String address = null;

    public static final int STATE_NONE = 0;
    public static final int STATE_CONNECTED = 1;
    public static final int STATE_DISCONNECTED = 2;
    public static final int STATE_ERROR = 3;

    public static final String ACTION_BLUETOOTH_CONNECTED = "com.bbpos.bluetooth.connect";
    public static final String ACTION_BLUETOOTH_DISCONNECTED = "com.bbpos.bluetooth.disconnect";
    public static final String ACTION_BLUETOOTH_CONNECT_FAIL = "com.bbpos.bluetooth.connect_fail";

    private int mState = STATE_NONE;

//    private DataCallback callback;
    private int cmd = -1;
    private boolean isBreak = false;
    int len = 0;
    int inCount = 0;
    byte[] data = null;
    private String name;
    private Context mContext;
    private boolean userCancel = false;
    private int id = -1;
    private boolean isRegister = false;

    public BluetoothController(Context context, BluetoothAdapter adapter){
        mAdapter = adapter;
        mContext = context;
    }

    /**
     * 设置蓝牙绑定地址
     * @param boundAddress
     */
    public void setAddress(String boundAddress){
        this.address = boundAddress;
    }


    public String getAddress(){
        return this.address;
    }

    public String getName(){
        return this.name;
    }

    public void setId(int id){
        BBLog.d(BBLog.TAG, "bt id :" + id);
        this.id = id;
    }

    /**
     * 设置蓝牙状态
     * @param state
     */
    private synchronized void setState(int state){
        mState = state;
    }

    /**
     * 获取当前蓝牙状态
     * @return
     */
    public int getState(){
        return mState;
    }

    /**
     * 连接蓝牙设备
     */
    public synchronized void connect(String address){
        BBLog.d(BBLog.TAG, "call connect() ... ");
        setAddress(address);
        if(mConnectThread == null && mAdapter.isEnabled()){
            BBLog.d(BBLog.TAG, "start connect Thread . ");
            mConnectThread = new ConnectThread();
            mConnectThread.start();
        }
    }


    /**
     * 断开蓝牙设备
     */
    public synchronized void disconnect(){
        BBLog.d(BBLog.TAG, "call disconnect() ... ");
        if(mConnectThread != null && mAdapter.isEnabled()){
            BBLog.d(BBLog.TAG, "start disconnect. ");
            mConnectThread.cancel();
            mConnectThread = null;
            setState(STATE_DISCONNECTED);
            mContext.sendBroadcast(new Intent(ACTION_BLUETOOTH_DISCONNECTED), RequestPermission.REQUEST_PERMISSION_MY_BROADCAST);
        }
    }


    /**
     * 由外部输入，蓝牙接收线程不再作处理
     * @param isCancel
     */
    public void setCancel(boolean isCancel){
        userCancel = isCancel;
    }


    /**
     * 发送数据
     * @param cmd
     * @param out
     * @param len
     */
    public synchronized void send(int cmd,byte[] out,int len){
        ConnectThread thread;
        synchronized (this) {
            if (mState != STATE_CONNECTED){
                BBLog.e(BBLog.TAG, "Error! Bluetooth device is not connected !");
                Map<String,Object> resetMap = new HashMap<String,Object>();
//                XnUtils.putByte(resetMap, XnSDKManager.KEY_RESULT, (byte)XnSDKManager.RESULT_BLUETOOTH_CONN_FAIL);
//                callback.onDataAvaiable(resetMap);
                return;
            }
            thread = this.mConnectThread;
            this.cmd =cmd;
            BBLog.i(BBLog.TAG, "send data1 ******** !");
            thread.write(out, len);
        }
    }

    /**
     * 设置回调
     * @param callback
     */
//    public void setDatacallback(DataCallback callback){
//        this.callback = callback;
//    }

    /**
     * 蓝牙连接线程类
     * <AUTHOR>
     *
     */
    private class ConnectThread extends Thread {
        private BluetoothDevice mCurrentDevice;
        private BluetoothSocket mSocket;
        private InputStream mmInStream;
        private OutputStream mmOutStream;

        private int loop ;
        private final int NONE = 0;
        private final int START = 1;
        private final int END = 2;
        private final UUID CUSTOM_UUID = UUID.fromString("00001101-0000-1000-8000-00805F9B34FB");
        public ConnectThread(){
            mSocket = null;
            loop = NONE;
        }

        @Override
        public void run() {
            loop = START;
            while(loop == START){
                if(mAdapter.isEnabled()){
                    //判断蓝牙设备地址
                    if( null == address ){
                        BBLog.e(BBLog.TAG, "Bluetooth address is null .");
                        isBreak = true;
                        break;
                    }

                    mCurrentDevice = mAdapter.getRemoteDevice(address);
                    name = mCurrentDevice.getName();

                    //无法获取到当前蓝牙设备
                    if((mCurrentDevice == null) || (mCurrentDevice.getName() == null)){
                        BBLog.e(BBLog.TAG, "Can not obtain Current BluetoothDevice ");
                        isBreak = true;
                        break;
                    }

                    //获取蓝牙Socket
                    // Get a BluetoothSocket for a connection with the given BluetoothDevice
                    try {
                        if (PhoneDevicesInfo.shouldUseFixChannel()) {
                            Method m;
                            try {
                                m = mCurrentDevice.getClass().getMethod("createInsecureRfcommSocket", new Class[] { int.class });
                                mSocket = (BluetoothSocket) m.invoke(mCurrentDevice, 6);
                            } catch (Exception e1) {
                                e1.printStackTrace();
                            }
                        }
                        //issc
                        else  if (PhoneDevicesInfo.shouldUseSecure()) {
                            mSocket = mCurrentDevice.createRfcommSocketToServiceRecord(CUSTOM_UUID);
                        }
                        else {
                            mSocket = mCurrentDevice.createInsecureRfcommSocketToServiceRecord(CUSTOM_UUID);
                        }
		    	    	/*issc2.1*/
		    	    	/*Method m = mmDevice.getClass().getMethod("createInsecureRfcommSocket",new Class[] { int.class });
		    	    	tmp = (BluetoothSocket)m.invoke(mmDevice,6);*/
                        BBLog.i(BBLog.TAG, "[SocketThread] Constructure: Get a BluetoothSocket for a connection, create Rfcomm");
                    } catch (Exception e) {
                        BBLog.e(BBLog.TAG, "create() failed", e);
                    }

                    //蓝牙连接
                    try{
                        mSocket.connect();
                        BBLog.d(BBLog.TAG, "connected " + this.mCurrentDevice.getName());
                    }catch(Exception e){
                        BBLog.e(BBLog.TAG, "Bluetooth connected " + mCurrentDevice.getName() + " Failed.");
                        mContext.sendBroadcast(new Intent(ACTION_BLUETOOTH_CONNECT_FAIL), RequestPermission.REQUEST_PERMISSION_MY_BROADCAST);
                        isBreak = true;
                        closeSocket();
                        break;
                    }

                    try{
                        mmInStream = mSocket.getInputStream();
                        mmOutStream = mSocket.getOutputStream();
                    }catch(Exception e){
                        BBLog.e(BBLog.TAG, "mmInStream or mmOutStreamt create failed. ");
                        setState(STATE_DISCONNECTED);
                        isBreak = true;
                        closeSocket();
                        break;
                    }

                    setState(STATE_CONNECTED);
                    mContext.sendBroadcast(new Intent(ACTION_BLUETOOTH_CONNECTED), RequestPermission.REQUEST_PERMISSION_MY_BROADCAST);

                    ReceiveData();

                    //读取函数异常退出
                    if(loop == START && isBreak){
                        BBLog.e(BBLog.TAG," error to exit read function ...");
                        closeSocket();
                        setState(STATE_DISCONNECTED);
                        mContext.sendBroadcast(new Intent(ACTION_BLUETOOTH_DISCONNECTED), RequestPermission.REQUEST_PERMISSION_MY_BROADCAST);
                        break;
                    }

                    //读取函数正常退出
                    if (loop == END){
                        BBLog.d(BBLog.TAG,"normal to exit read function ...");
                        break;
                    }
                }
            }
            BBLog.d(BBLog.TAG, "Waiting for connect thread exit.");
            //read失败退出
            if(loop == START && isBreak){
                BBLog.d(BBLog.TAG," break operation ...");
                isBreak = false;
                mConnectThread = null;
                BBLog.d(BBLog.TAG, " connect thread exit success ...");
            }
            this.loop = NONE;
        }

        /**
         * 数据接收处理
         */
        public void ReceiveData(){
            BBLog.d(BBLog.TAG, " waiting for data.");
            int bytes = 0;
            byte[] buffer = new byte[1024];
            boolean isStx = false;
            boolean isNumCacl = false;

            while(loop == START){
                try {
                    if(mmInStream.available() > 0){
                        if(!isStx){
                            bytes = mmInStream.read(buffer,0,2);
                            if(buffer[0] == 0x58 && buffer[1] == 0x4E){
                                isStx = true;
                                inCount += 2;
                            }
                            continue;
                        }else{
                            if(!isNumCacl){
                                bytes = mmInStream.read(buffer,0,2);
                                String d1 = String.format("%02X", (int)(buffer[0]  & 0xff));
                                String d2 = String.format("%02X", (int)(buffer[1]  & 0xff));
                                len = Integer.parseInt(d1) * 100  + Integer.parseInt(d2) + 2 + 2 + 1 + 1;
                                data = new byte[len];
                                data[0] = 0x58;
                                data[1] = 0x4E;
                                data[2] = buffer[0];
                                data[3] = buffer[1];
                                isNumCacl = true;
                                inCount += 2;
                                continue;
                            }else{
                                bytes = mmInStream.read(buffer,0,len-4);
                                for(int i=0;i<bytes;i++){
                                    data[inCount++] = buffer[i];
                                }

                                if(inCount == len){
//                                    if(XnSetting.ID == 0x7F){
//                                        XnSetting.ID = 0x00;
//                                    }
//                                    if(XnSetting.ID == (byte)(data[7]-1)) {
//                                        if(data[5]==0x1D && data[6]==0x08){
//                                            //RESET
//                                        }else{
//                                            try{
//                                                MessageFactory.parseMessage(cmd,data, len, callback);
//                                            }catch(Exception e){
//                                                e.printStackTrace();
//                                                Map<String,Object> resetMap = new HashMap<String,Object>();
//                                                XnUtils.putByte(resetMap, XnSDKManager.KEY_RESULT, (byte)XnSDKManager.RESULT_PARSE_DATA);
//                                                callback.onDataAvaiable(resetMap);
//                                            }
//                                        }
//                                    }
                                    inCount = 0;
                                    data = null;
                                    len = 0;
                                    isStx = false;
                                    isNumCacl = false;
                                }
                            }
                        }
                    }
                } catch (Exception e){
                    BBLog.v(BBLog.TAG, "bluetooth socket read fail.");
                    closeSocket();
                    isBreak = true;
                }


                if (loop == END){
                    BBLog.d(BBLog.TAG,"normal cancel read data ...");
                    break;
                }

                try {
                    Thread.sleep(100);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            }
        }


        /**
         * 关闭socket
         */
        private void closeSocket(){
            if(mSocket != null){
                try {
                    mSocket.close();
                    BBLog.d(BBLog.TAG, "Close Bluetooth Socket");
                    mSocket = null;
                }
                catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }


        /**
         * 线程退出操作
         */
        public void cancel(){
            loop = END;
            do{
                try{
                    Thread.sleep(100);
                }catch(InterruptedException e){
                    e.printStackTrace();
                }
            }while(loop != NONE);
            closeSocket();
            BBLog.d(BBLog.TAG, "ConnectThread cancel success");
        }

        /**
         * 通过蓝牙串口发送数据
         * @param bytes
         */
        public void write(byte[] bytes,int len) {
            if (mSocket == null) {
                BBLog.e(BBLog.TAG, "Bluetooth not connected ");
                return;
            }
            try {
                this.mmOutStream.write(bytes,0,len);
            } catch (Exception e) {
                BBLog.e(BBLog.TAG, "Bluetooth socket write failed.");
                e.printStackTrace();
            }
        }
    }

    /**
     * 与设备配对 参考源码：platform/packages/apps/Settings.git
     * /Settings/src/com/android/settings/bluetooth/CachedBluetoothDevice.java
     */
    private boolean createBond(Class btClass, BluetoothDevice btDevice) throws Exception {
        Method createBondMethod = btClass.getMethod("createBond");
        Boolean returnValue = (Boolean) createBondMethod.invoke(btDevice);
        return returnValue.booleanValue();
    }

    /**
     * 与设备解除配对 参考源码：platform/packages/apps/Settings.git
     * /Settings/src/com/android/settings/bluetooth/CachedBluetoothDevice.java
     */
    static public boolean removeBond(Class btClass, BluetoothDevice btDevice) throws Exception {
        Method removeBondMethod = btClass.getMethod("removeBond");
        Boolean returnValue = (Boolean) removeBondMethod.invoke(btDevice);
        return returnValue.booleanValue();
    }

    private boolean setPin(Class btClass, BluetoothDevice btDevice, String str) throws Exception {
        try {
            Method removeBondMethod = btClass.getDeclaredMethod("setPin",
                    new Class[]
                            {byte[].class});
            Boolean returnValue = (Boolean) removeBondMethod.invoke(btDevice,
                    new Object[]
                            {str.getBytes()});
            BBLog.e("returnValue", "" + returnValue);
        }
        catch (SecurityException e) {
            // throw new RuntimeException(e.getMessage());
            e.printStackTrace();
        }
        catch (IllegalArgumentException e) {
            // throw new RuntimeException(e.getMessage());
            e.printStackTrace();
        }
        catch (Exception e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }
        return true;
    }

    // 取消用户输入
    public boolean cancelPairingUserInput(Class btClass, BluetoothDevice device) throws Exception {
        Method createBondMethod = btClass.getMethod("cancelPairingUserInput");
        // cancelBondProcess()
        Boolean returnValue = (Boolean) createBondMethod.invoke(device);
        return returnValue.booleanValue();
    }

    // 取消配对
    public boolean cancelBondProcess(Class btClass, BluetoothDevice device) throws Exception {
        Method createBondMethod = btClass.getMethod("cancelBondProcess");
        Boolean returnValue = (Boolean) createBondMethod.invoke(device);
        return returnValue.booleanValue();
    }

    /**
     *
     * @param clsShow
     */
    public void printAllInform(Class clsShow) {
        try
        {
            // 取得所有方法
            Method[] hideMethod = clsShow.getMethods();
            int i = 0;
            for (; i < hideMethod.length; i++)
            {
                BBLog.e("method name", hideMethod[i].getName() + ";and the i is:"
                        + i);
            }
            // 取得所有常量
            Field[] allFields = clsShow.getFields();
            for (i = 0; i < allFields.length; i++)
            {
                BBLog.e("Field name", allFields[i].getName());
            }
        }
        catch (SecurityException e)
        {
            // throw new RuntimeException(e.getMessage());
            e.printStackTrace();
        }
        catch (IllegalArgumentException e)
        {
            // throw new RuntimeException(e.getMessage());
            e.printStackTrace();
        }
        catch (Exception e)
        {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }
    }

    public boolean pair(String strAddr) {
        boolean result = false;
        BluetoothAdapter bluetoothAdapter = BluetoothAdapter.getDefaultAdapter();

        bluetoothAdapter.cancelDiscovery();

        if (!bluetoothAdapter.isEnabled()) {
            bluetoothAdapter.enable();
        }

        if (!BluetoothAdapter.checkBluetoothAddress(strAddr)) { // 检查蓝牙地址是否有效
            BBLog.d("mylog", "devAdd un effient!");
        }

        BluetoothDevice device = bluetoothAdapter.getRemoteDevice(strAddr);

        if (device.getBondState() != BluetoothDevice.BOND_BONDED) {
            try {
                BBLog.d("mylog", "NOT BOND_BONDED");
//                setPin(device.getClass(), device, strPsw); // 手机和蓝牙采集器配对
                createBond(device.getClass(), device);
                remoteDevice = device; // 配对完毕就把这个设备对象传给全局的remoteDevice
                result = true;
            } catch (Exception e) {
                // TODO Auto-generated catch block

                BBLog.d("mylog", "setPiN failed!");
                e.printStackTrace();
            } //

        } else {
            BBLog.d("mylog", "HAS BOND_BONDED");
            try {
                createBond(device.getClass(), device);
//                setPin(device.getClass(), device, strPsw); // 手机和蓝牙采集器配对
                createBond(device.getClass(), device);
                remoteDevice = device; // 如果绑定成功，就直接把这个设备对象传给全局的remoteDevice
                result = true;
            } catch (Exception e) {
                // TODO Auto-generated catch block
                BBLog.d("mylog", "setPiN failed!");
                e.printStackTrace();
            }
        }
        return result;
    }
}
