package com.dspread.mdm.service.modules.wifi

import android.content.Context
import com.dspread.mdm.service.modules.BaseModuleHandler
import com.dspread.mdm.service.utils.log.Logger
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.launch
import org.json.JSONObject
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Locale

/**
 * WiFi Profile WebSocket消息处理器
 * 处理ST006类型的WiFi配置消息
 */
class WifiProfileHandler(
    private val context: Context
) : BaseModuleHandler() {

    companion object {
        private const val TAG = "[WifiProfileHandler]"
    }

    private val wifiProfileManager by lazy { WifiProfileManager(context) }

    // WiFi处理专用的协程作用域
    private val wifiScope = CoroutineScope(Dispatchers.IO + SupervisorJob())

    override fun getModuleName(): String = "WiFi Profile"

    override suspend fun handleMessage(message: String): Result<Unit> {
        return try {
            // 检查模块是否启用
            checkModuleEnabled().getOrThrow()

            Logger.wifi("$TAG 开始处理WiFi Profile消息")

            val jsonObject = parseJsonMessage(message)

            // 发送C0000响应确认
            sendAcknowledgment(jsonObject)

            // 解析WiFi配置列表
            val wifiProfiles = parseWifiProfiles(jsonObject)

            if (wifiProfiles.isEmpty()) {
                Logger.wifiW("$TAG WiFi配置列表为空")
                return Result.success(Unit)
            }

            Logger.wifi("$TAG 解析到 ${wifiProfiles.size} 个WiFi配置")

            // 异步处理WiFi配置（避免阻塞WebSocket消息处理）
            wifiScope.launch {
                val result = wifiProfileManager.processWifiProfiles(wifiProfiles)
                if (result.isFailure) {
                    Logger.wifiE("$TAG WiFi配置处理失败", result.exceptionOrNull())
                } else {
                    Logger.wifi("$TAG WiFi配置处理完成")
                }
            }

            Result.success(Unit)

        } catch (e: Exception) {
            Logger.wifiE("$TAG WiFi Profile消息处理失败", e)
            Result.failure(e)
        }
    }

    /**
     * 解析WiFi配置列表
     */
    private fun parseWifiProfiles(jsonObject: JSONObject): List<WifiProfile> {
        val profiles = mutableListOf<WifiProfile>()

        try {
            val data = jsonObject.optJSONObject("data") ?: return profiles
            val wifiList = data.optJSONArray("wifiList") ?: return profiles

            for (i in 0 until wifiList.length()) {
                val wifiObj = wifiList.getJSONObject(i)
                val profile = parseWifiProfile(wifiObj)
                if (profile != null) {
                    profiles.add(profile)
                }
            }

        } catch (e: Exception) {
            Logger.wifiE("$TAG 解析WiFi配置列表失败", e)
        }

        return profiles
    }

    /**
     * 解析单个WiFi配置
     */
    private fun parseWifiProfile(wifiObj: JSONObject): WifiProfile? {
        return try {
            WifiProfile(
                ssid = wifiObj.optString("ssid", ""),
                password = wifiObj.optString("password", ""),
                securityType = wifiObj.optString("securityType", "OPEN"),
                order = wifiObj.optInt("order", 999),
                startTime = wifiObj.optString("startTime", ""),
                endTime = wifiObj.optString("endTime", ""),
                connectHidden = wifiObj.optString("connectHidden", "0") == "1",
                proxyType = wifiObj.optString("proxyType", "NONE"),
                proxyHost = wifiObj.optString("proxyIp", ""), // 使用proxyIp字段
                proxyPort = wifiObj.optInt("proxyPort", 0),
                proxyUser = wifiObj.optString("proxyUser", ""),
                proxyPassword = wifiObj.optString("proxyPwd", ""),
                wifiLostTime = wifiObj.optLong("wifi_lost_time", 0L),

                // 字段解析
                isDefault = wifiObj.optString("isDefault", "0") == "1",
                beginDate = wifiObj.optString("beginDate", ""),
                endDate = wifiObj.optString("endDate", ""),
                byPassProxyFor = wifiObj.optString("byPassProxyFor", ""),
                pacUrl = wifiObj.optString("pacUrl", ""),
                wifiFlagCache = wifiObj.optString("wifi_flag_cache", "0") == "1"
            )
        } catch (e: Exception) {
            Logger.wifiE("$TAG 解析WiFi配置失败: ${wifiObj}", e)
            null
        }
    }
}

/**
 * WiFi配置数据类
 */
data class WifiProfile(
    val ssid: String,                    // WiFi网络名称
    val password: String,                // WiFi密码
    val securityType: String,            // 安全类型 (OPEN, WEP, WPA_PSK, WPA2_PSK)
    val order: Int,                      // 优先级 (数字越小优先级越高)
    val startTime: String,               // 开始时间 (HH:mm格式)
    val endTime: String,                 // 结束时间 (HH:mm格式)
    val connectHidden: Boolean,          // 是否连接隐藏网络
    val proxyType: String,               // 代理类型 (NONE, MANUAL, PAC)
    val proxyHost: String,               // 代理主机 (proxyIp)
    val proxyPort: Int,                  // 代理端口
    val proxyUser: String,               // 代理用户名
    val proxyPassword: String,           // 代理密码
    val wifiLostTime: Long,              // 连接失败时间戳（用于24小时延迟重试）

    val isDefault: Boolean = false,      // 是否为默认WiFi
    val beginDate: String = "",          // 开始日期 (完整日期时间格式)
    val endDate: String = "",            // 结束日期 (完整日期时间格式)
    val byPassProxyFor: String = "",     // 代理排除列表 (JSON数组字符串)
    val pacUrl: String = "",             // PAC代理URL
    val wifiFlagCache: Boolean = false   // 是否为缓存WiFi标识
) {

    companion object {
        private const val TAG = "[WifiProfile]"
    }

    /**
     * 检查WiFi配置是否有效
     * 增强密码验证和代理配置验证
     */
    fun isValid(): Boolean {
        if (ssid.isEmpty()) {
            return false
        }

        // 验证安全类型和密码的匹配性
        if (!validateSecurityAndPassword()) {
            return false
        }

        // 验证代理配置
        if (!isValidProxyConfig()) {
            Logger.wifiE("$TAG 无效的代理配置: $ssid, ${getProxyDescription()}")
            return false
        }

        return true
    }

    /**
     * 验证安全类型和密码的匹配性
     */
    private fun validateSecurityAndPassword(): Boolean {
        val securityType = WifiSecurityType.fromString(this.securityType)

        return when (securityType) {
            WifiSecurityType.NONE, WifiSecurityType.OPEN -> {
                // NONE或OPEN类型不进行密码校验
                true
            }

            WifiSecurityType.WEP -> {
                if (password.isEmpty()) {
                    Logger.wifiE("$TAG WEP网络密码为空: $ssid")
                    return false
                }

                val length = password.length
                // WEP-40, WEP-104, and 256-bit WEP (WEP-232?)
                if ((length == 10 || length == 26 || length == 32) && password.matches(Regex("^[0-9A-Fa-f]*$"))) {
                    // 十六进制WEP密钥
                    true
                } else {
                    // ASCII格式WEP密钥
                    true
                }
            }

            WifiSecurityType.WPA, WifiSecurityType.WPA_PSK, WifiSecurityType.WPA2_PSK -> {
                if (password.isEmpty()) {
                    Logger.wifiE("$TAG WPA/WPA2网络密码为空: $ssid")
                    return false
                }

                // 检查是否为64位十六进制PSK
                if (password.matches(Regex("^[0-9A-Fa-f]{64}$"))) {
                    // 64位十六进制PSK
                    true
                } else {
                    // 普通密码
                    true
                }
            }

            WifiSecurityType.NOT_ALLOW -> {
                // 不允许连接的网络类型
                Logger.wifiE("$TAG 网络类型不允许连接: $ssid")
                false
            }
        }
    }

    /**
     * 检查当前时间是否在配置的时间窗口内
     * 支持完整的日期时间范围和简单时间范围
     */
    fun isInTimeWindow(): Boolean {
        // 优先检查完整日期时间范围
        if (beginDate.isNotEmpty() && endDate.isNotEmpty()) {
            return isInDateTimeRange()
        }

        // 回退到简单时间范围检查
        if (startTime.isEmpty() || endTime.isEmpty()) {
            return true // 没有时间限制
        }

        return try {
            val currentTime = getCurrentTimeString()
            isTimeInRange(currentTime, startTime, endTime)
        } catch (e: Exception) {
            Logger.wifiE("检查时间窗口失败", e)
            true // 出错时默认允许
        }
    }

    /**
     * 检查是否在完整日期时间范围内
     * 支持数字时间戳和字符串日期格式
     */
    fun isInDateTimeRange(): Boolean {
        if (beginDate.isEmpty() && endDate.isEmpty()) {
            return true
        }

        val currentTime = System.currentTimeMillis()

        try {
            val beginTime = parseDateTimeString(beginDate)
            val endTime = parseDateTimeString(endDate)

            return when {
                beginTime != null && endTime != null -> {
                    currentTime in beginTime..endTime
                }
                beginTime != null -> currentTime >= beginTime
                endTime != null -> currentTime <= endTime
                else -> true
            }
        } catch (e: Exception) {
            Logger.wifiE("$TAG 解析日期时间失败: beginDate=$beginDate, endDate=$endDate", e)
            return true // 解析失败时默认允许
        }
    }

    /**
     * 解析日期时间字符串
     * 支持多种格式：数字时间戳、ISO日期格式等
     */
    private fun parseDateTimeString(dateTimeStr: String): Long? {
        if (dateTimeStr.isEmpty()) return null

        return try {
            // 尝试解析为数字时间戳
            if (dateTimeStr.matches(Regex("^\\d+$"))) {
                dateTimeStr.toLong()
            } else {
                // 尝试解析为日期字符串格式
                parseDateString(dateTimeStr)
            }
        } catch (e: Exception) {
            Logger.wifiE("$TAG 解析日期时间字符串失败: $dateTimeStr", e)
            null
        }
    }

    /**
     * 解析日期字符串为时间戳
     * 支持多种日期格式
     */
    private fun parseDateString(dateStr: String): Long? {
        val dateFormats = listOf(
            "yyyy-MM-dd HH:mm:ss",
            "yyyy-MM-dd HH:mm",
            "yyyy-MM-dd",
            "yyyy/MM/dd HH:mm:ss",
            "yyyy/MM/dd HH:mm",
            "yyyy/MM/dd"
        )

        for (format in dateFormats) {
            try {
                val sdf = SimpleDateFormat(format, Locale.getDefault())
                return sdf.parse(dateStr)?.time
            } catch (e: Exception) {
                // 继续尝试下一个格式
            }
        }

        return null
    }

    /**
     * 检查是否需要延迟执行（24小时延迟重试机制）
     */
    fun shouldDelayExecution(): Boolean {
        if (wifiLostTime == 0L) {
            return false
        }

        val currentTime = System.currentTimeMillis()
        val delayTime = 24 * 60 * 60 * 1000L // 24小时

        return (currentTime - wifiLostTime) < delayTime
    }

    /**
     * 获取当前时间字符串 (HH:mm格式)
     */
    private fun getCurrentTimeString(): String {
        val calendar = Calendar.getInstance()
        val hour = calendar.get(Calendar.HOUR_OF_DAY)
        val minute = calendar.get(Calendar.MINUTE)
        return String.format("%02d:%02d", hour, minute)
    }

    /**
     * 检查时间是否在指定范围内
     * 支持跨天时间段（如23:00-02:00）
     */
    private fun isTimeInRange(current: String, start: String, end: String): Boolean {
        val currentMinutes = timeToMinutes(current)
        val startMinutes = timeToMinutes(start)
        val endMinutes = timeToMinutes(end)

        return if (startMinutes <= endMinutes) {
            // 同一天内的时间段
            currentMinutes in startMinutes..endMinutes
        } else {
            // 跨天的时间段
            currentMinutes >= startMinutes || currentMinutes <= endMinutes
        }
    }

    /**
     * 将时间字符串转换为分钟数
     */
    private fun timeToMinutes(time: String): Int {
        val parts = time.split(":")
        return parts[0].toInt() * 60 + parts[1].toInt()
    }

    /**
     * 创建带有失败时间戳的副本
     */
    fun withFailureTime(failureTime: Long): WifiProfile {
        return copy(wifiLostTime = failureTime)
    }

    /**
     * 清除失败时间戳
     */
    fun clearFailureTime(): WifiProfile {
        return copy(wifiLostTime = 0L)
    }

    /**
     * 检查WiFi配置是否已过期
     */
    fun isExpired(): Boolean {
        if (endDate.isEmpty()) {
            return false
        }

        val currentTime = System.currentTimeMillis()
        val endTime = parseDateTimeString(endDate)

        return endTime != null && currentTime > endTime
    }

    /**
     * 检查WiFi配置是否还未生效
     */
    fun isNotYetActive(): Boolean {
        if (beginDate.isEmpty()) {
            return false
        }

        val currentTime = System.currentTimeMillis()
        val beginTime = parseDateTimeString(beginDate)

        return beginTime != null && currentTime < beginTime
    }

    /**
     * 验证代理配置是否有效
     */
    fun isValidProxyConfig(): Boolean {
        val proxyType = WifiProxyType.fromString(this.proxyType)
        return WifiProxyType.isValidProxyConfig(proxyType, proxyHost, proxyPort, pacUrl)
    }

    /**
     * 获取代理配置描述
     */
    fun getProxyDescription(): String {
        val proxyType = WifiProxyType.fromString(this.proxyType)
        return WifiProxyType.getProxyDescription(proxyType, proxyHost, proxyPort, pacUrl)
    }

    /**
     * 检查是否需要代理配置
     */
    fun needsProxyConfig(): Boolean {
        val proxyType = WifiProxyType.fromString(this.proxyType)
        return proxyType != WifiProxyType.NONE
    }

    /**
     * 检查是否为隐藏网络
     */
    fun isHiddenWifi(): Boolean {
        return connectHidden
    }

    /**
     * 获取WiFi类型描述
     */
    fun getWifiTypeDescription(): String {
        val typeList = mutableListOf<String>()

        if (isDefault) {
            typeList.add("Default")
        }

        if (isHiddenWifi()) {
            typeList.add("Hidden")
        }

        if (wifiFlagCache) {
            typeList.add("Cache")
        }

        return if (typeList.isNotEmpty()) {
            "[${typeList.joinToString(", ")}] $ssid"
        } else {
            ssid
        }
    }

    /**
     * 检查隐藏网络的特殊处理需求
     */
    fun needsHiddenNetworkHandling(): Boolean {
        return isHiddenWifi() && ssid.isNotEmpty()
    }

    /**
     * 检查WiFi是否处于故障状态（24小时延迟重试）
     * 与wifi_lost_time逻辑保持一致
     */
    fun isInFailureState(): Boolean {
        if (wifiLostTime == 0L) {
            return false
        }

        val currentTime = System.currentTimeMillis()
        val timeSinceFailure = currentTime - wifiLostTime
        val delayPeriod = 24 * 60 * 60 * 1000L // 24小时

        val isInDelay = timeSinceFailure < delayPeriod
        if (isInDelay) {
            val remainingHours = (delayPeriod - timeSinceFailure) / (60 * 60 * 1000L)
            Logger.wifi("$TAG WiFi处于故障延迟状态: $ssid, 剩余: ${remainingHours}小时")
        }

        return isInDelay
    }

    /**
     * 标记WiFi连接失败
     */
    fun markAsFailure(): WifiProfile {
        val currentTime = System.currentTimeMillis()
        Logger.wifi("$TAG 标记WiFi连接失败: $ssid, 时间: $currentTime")
        return this.copy(wifiLostTime = currentTime)
    }

    /**
     * 清除WiFi故障状态
     */
    fun clearFailureState(): WifiProfile {
        if (wifiLostTime != 0L) {
            Logger.wifi("$TAG 清除WiFi故障状态: $ssid")
        }
        return this.copy(wifiLostTime = 0L)
    }

    /**
     * 获取故障状态描述
     */
    fun getFailureStateDescription(): String {
        if (wifiLostTime == 0L) {
            return "正常"
        }

        val currentTime = System.currentTimeMillis()
        val timeSinceFailure = currentTime - wifiLostTime
        val delayPeriod = 24 * 60 * 60 * 1000L // 24小时

        return if (timeSinceFailure < delayPeriod) {
            val remainingHours = (delayPeriod - timeSinceFailure) / (60 * 60 * 1000L)
            "故障延迟中 (剩余${remainingHours}小时)"
        } else {
            "故障延迟已过期，可重试"
        }
    }

    /**
     * 检查是否可以重试连接
     */
    fun canRetryConnection(): Boolean {
        return !isInFailureState()
    }
}

/**
 * WiFi安全类型枚举
 */
enum class WifiSecurityType(val value: String, val displayName: String) {
    NONE("None", "None"),                    // 开放网络
    OPEN("OPEN", "None"),                    // 兼容旧格式
    WEP("WEP", "WEP"),                      // WEP加密
    WPA("WPA", "WPA"),                      // WPA加密
    WPA_PSK("WPA_PSK", "WPA"),              // 兼容旧格式
    WPA2_PSK("WPA/WPA2 PSK", "WPA/WPA2 PSK"), // WPA2加密
    NOT_ALLOW("NOT_ALLOW", "NOT_ALLOW");     // 不允许连接

    companion object {
        fun fromString(value: String): WifiSecurityType {
            return values().find { it.value == value } ?: NONE
        }

        /**
         * 检查是否为十六进制密码
         * 只有恰好64位的十六进制字符串才被认为是十六进制PSK
         * password.matches("[0-9A-Fa-f]{64}")
         */
        fun isHexPassword(password: String): Boolean {
            return password.matches(Regex("^[0-9A-Fa-f]{64}$"))
        }

        /**
         * 验证WEP密钥长度
         * 支持10,26,32位十六进制或任意长度ASCII
         */
        fun isValidWepKey(password: String): Boolean {
            if (password.isEmpty()) {
                return false
            }

            val length = password.length
            // 十六进制格式：10, 26, 32位
            if ((length == 10 || length == 26 || length == 32) && password.matches(Regex("^[0-9A-Fa-f]*$"))) {
                return true
            }
            // ASCII格式：任意长度
            return true
        }
    }
}



/**
 * WiFi代理类型枚举
 */
enum class WifiProxyType(val value: String) {
    NONE("None"),           // 无代理
    MANUAL("Manual"),       // 手动代理
    PAC("PAC");            // PAC代理

    companion object {
        fun fromString(value: String): WifiProxyType {
            return values().find { it.value == value } ?: NONE
        }

        /**
         * 验证代理配置是否有效
         */
        fun isValidProxyConfig(proxyType: WifiProxyType, proxyHost: String, proxyPort: Int, pacUrl: String): Boolean {
            return when (proxyType) {
                NONE -> true
                MANUAL -> proxyHost.isNotEmpty() && proxyPort > 0 && proxyPort <= 65535
                PAC -> pacUrl.isNotEmpty() && (pacUrl.startsWith("http://") || pacUrl.startsWith("https://"))
            }
        }

        /**
         * 获取代理配置描述
         */
        fun getProxyDescription(proxyType: WifiProxyType, proxyHost: String, proxyPort: Int, pacUrl: String): String {
            return when (proxyType) {
                NONE -> "无代理"
                MANUAL -> "手动代理: $proxyHost:$proxyPort"
                PAC -> "PAC代理: $pacUrl"
            }
        }
    }
}

/**
 * WiFi扫描信息数据类
 */
data class WifiScanInfo(
    val ssid: String,                    // WiFi网络名称
    val bssid: String,                   // MAC地址
    val level: Int,                      // 信号强度 (dBm)
    val frequency: Int,                  // 频率 (MHz)
    val capabilities: String,            // 安全能力
    val signalStrength: String           // 信号强度描述
) {
    /**
     * 检查是否为5GHz频段
     */
    fun is5GHz(): Boolean {
        return frequency > 5000
    }

    /**
     * 检查是否为2.4GHz频段
     */
    fun is2_4GHz(): Boolean {
        return frequency in 2400..2500
    }

    /**
     * 获取WiFi安全类型
     */
    fun getSecurityType(): String {
        return when {
            capabilities.contains("WPA2") -> "WPA2"
            capabilities.contains("WPA") -> "WPA"
            capabilities.contains("WEP") -> "WEP"
            else -> "OPEN"
        }
    }

    /**
     * 检查信号是否足够强
     */
    fun isSignalStrong(): Boolean {
        return level > -70 // 大于-70dBm认为信号较强
    }
}
