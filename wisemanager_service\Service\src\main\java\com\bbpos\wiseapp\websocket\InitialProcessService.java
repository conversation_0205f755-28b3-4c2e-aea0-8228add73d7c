package com.bbpos.wiseapp.websocket;

import android.content.Intent;
import android.os.Environment;
import android.os.Handler;
import android.os.Looper;
import android.preference.PreferenceManager;
import android.text.TextUtils;

import com.bbpos.wiseapp.logger.BBLog;
import com.bbpos.wiseapp.service.WakeLockService;
import com.bbpos.wiseapp.service.common.RequestPermission;
import com.bbpos.wiseapp.service.common.UsualData;
import com.bbpos.wiseapp.tms.adapter.SystemManagerAdapter;
import com.bbpos.wiseapp.tms.model.AppInfo;
import com.bbpos.wiseapp.tms.network.HttpUtils;
import com.bbpos.wiseapp.tms.utils.BroadcastActions;
import com.bbpos.wiseapp.tms.utils.Constants;
import com.bbpos.wiseapp.tms.utils.ContextUtil;
import com.bbpos.wiseapp.tms.utils.DateTimeUtils;
import com.bbpos.wiseapp.tms.utils.FileUtils;
import com.bbpos.wiseapp.tms.utils.Helpers;
import com.bbpos.wiseapp.tms.utils.ParameterName;
import com.bbpos.wiseapp.tms.utils.RuleStatus;
import com.bbpos.wiseapp.tms.utils.RulebasedAppListManager;
import com.bbpos.wiseapp.tms.utils.SPKeys;
import com.bbpos.wiseapp.tms.utils.TaskState;
import com.bbpos.wiseapp.utils.SharedPreferencesUtils;
import com.bbpos.wiseapp.websocket.handler.GeoProfileHandler;
import com.bbpos.wiseapp.websocket.handler.RulebasedListHandler;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.File;
import java.io.IOException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.HashMap;

public class InitialProcessService extends WakeLockService {
    public static final String TAG = "UnboxInit";
    private Handler mHandler = new Handler(Looper.getMainLooper());
    private boolean waiting;
    private int total = 0;
    private int count = 0;
    private JSONArray ruleListExec = new JSONArray();
    private AppInfo serviceInfo = null;
    private HashMap<String, String> appsToDownload = new HashMap<String, String>();      //<packName, appJson>;
    private HashMap<String, String> appsToDelete = new HashMap<String, String>();      //<packName, appJson>;
    private HashMap<String, String> ruleForApplist = new HashMap<String, String>();    //<ruleID, resultArray>
    public InitialProcessService() {
        super("InitialProcessService");
    }

    @Override
    protected void onHandleIntent(final Intent intent) {
        BBLog.w(TAG, "InitialProcessService BEGIN");
        String response = intent.getStringExtra("response");
        try {
            JSONArray geoList = new JSONArray();
            JSONArray ruleList = new JSONArray();
            JSONArray appList = new JSONArray();
            JSONObject responseJson = new JSONObject(response);
            JSONObject responseData = Helpers.getDataFromResponse(responseJson);
            BBLog.w(TAG, responseData.toString());
            if (responseData == null) {
                return;
            }
            BBLog.e(TAG, "RulebasedHandler = " + responseData.toString());
            if (responseData.has(ParameterName.geoList)) {
                geoList = responseData.getJSONArray(ParameterName.geoList);
                long lastModify = 0L;
                for (int i=0; i<geoList.length(); i++) {
                    JSONObject profileJsonObj = geoList.getJSONObject(i);
                    if (!(profileJsonObj.has(ParameterName.isDefault) && "1".equals(profileJsonObj.getString(ParameterName.isDefault)))) {
                        String beginDateStr = profileJsonObj.getString(ParameterName.beginDate);
                        String endDateStr = profileJsonObj.getString(ParameterName.endDate);
                        String modifyDateStr = profileJsonObj.getString(ParameterName.modifyDate);
                        long nowTime = System.currentTimeMillis();
                        long begTime;
                        long endTime;
                        long modifyTime;
                        if (DateTimeUtils.isNumeric(beginDateStr) && DateTimeUtils.isNumeric(endDateStr)) {
                            begTime = new Long(beginDateStr).longValue();
                            endTime = new Long(endDateStr).longValue();
                            modifyTime = new Long(modifyDateStr).longValue();
                        } else {
                            SimpleDateFormat sdf = new SimpleDateFormat(Constants.dateFormat);
                            begTime = sdf.parse(beginDateStr).getTime();
                            endTime = sdf.parse(endDateStr).getTime();
                            modifyTime = sdf.parse(modifyDateStr).getTime();
                        }

                        if (begTime < nowTime && endTime > nowTime) {
                            if (modifyTime > lastModify) {
                                SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_GPS_GEOFENCE_CUR_PROFILE,
                                        profileJsonObj.toString());
                                lastModify = modifyTime;
                            }
                        } else if (begTime > nowTime) {
                            GeoProfileHandler.updateGeoProfileList(profileJsonObj);
                        }
                    } else {
                        GeoProfileHandler.updateGeoProfileList(profileJsonObj);
                    }
                }
            }
            if (responseData.has(ParameterName.ruleList)) {
                ruleList = responseData.getJSONArray(ParameterName.ruleList);
                if (ruleList.length() > 0) {
                    PreferenceManager.getDefaultSharedPreferences(ContextUtil.getInstance()).edit().putString(SPKeys.WEBSOCKET_RULEBASED_LIST, "").commit();
//                    String localRuleListStr = PreferenceManager.getDefaultSharedPreferences(ContextUtil.getInstance()).getString(SPKeys.WEBSOCKET_RULEBASED_LIST, "");
//                    JSONArray localRuleList = new JSONArray(localRuleListStr);
//                    for (int i=0; i<localRuleList.length(); i++) {
//                        ruleList.put(localRuleList.get(i));
//                    }
                }
                Long modifyTimeLast = Long.MIN_VALUE;
                for (int i=0; i<ruleList.length(); i++) {
                    JSONObject rulebased = ruleList.getJSONObject(i);
                    String action = rulebased.getString(ParameterName.action);
                    BBLog.i(TAG, "action = " + action);
                    if (RulebasedListHandler.Add.equals(action) || RulebasedListHandler.Modify.equals(action)) {
                        String ruleId = rulebased.getString(ParameterName.ruleId);
                        RulebasedListHandler.addWSRulebasedJsonObjById(ruleId, rulebased);
                    } else if (RulebasedListHandler.Delete.equals(action)) {
                        String ruleId = rulebased.getString(ParameterName.ruleId);
                        RulebasedListHandler.deleteWSRulebasedJsonObjById(ruleId, rulebased);
                    }
                    long modifyTime = Long.MIN_VALUE;
                    long beginTime = Long.MIN_VALUE;
                    long endTime = Long.MIN_VALUE;
                    if(RulebasedListHandler.Add.equals(rulebased.getString(ParameterName.action))
                    || RulebasedListHandler.Modify.equals(rulebased.getString(ParameterName.action))) {
                        String modifyDateStr = rulebased.getString(ParameterName.modifyDate);
                        String beginDateStr = rulebased.getString(ParameterName.beginDate);
                        String endDateStr = rulebased.getString(ParameterName.endDate);
                        long nowTime = System.currentTimeMillis();
                        try {
                            if (DateTimeUtils.isNumeric(beginDateStr) && DateTimeUtils.isNumeric(endDateStr) && DateTimeUtils.isNumeric(modifyDateStr)) {
                                modifyTime = new Long(modifyDateStr).longValue();
                                beginTime = new Long(beginDateStr).longValue();
                                endTime = new Long(endDateStr).longValue();
                            } else {
                                SimpleDateFormat sdf = new SimpleDateFormat(Constants.dateFormat);
                                modifyTime = sdf.parse(modifyDateStr).getTime();
                                beginTime = sdf.parse(beginDateStr).getTime();
                                endTime = sdf.parse(endDateStr).getTime();
                            }

                            if (beginTime < nowTime && nowTime <endTime) {
                                //到时执行
                                String ruleId = rulebased.getString(ParameterName.ruleId);
                                ruleListExec.put(rulebased);
                                JSONArray appListTmp = rulebased.getJSONArray(ParameterName.appList);
                                try {
                                    ruleForApplist.put(ruleId, createResultAppList(appListTmp));
                                } catch (Exception e) {
                                    e.printStackTrace();
                                }
                                for (int j=0; j<appListTmp.length(); j++) {
                                    JSONObject appJson = appListTmp.getJSONObject(j);
                                    String packName = appJson.getString(ParameterName.packName);
                                    if (appsToDownload.containsKey(packName)) {
                                        JSONObject appJsonLast = new JSONObject(appsToDownload.get(packName));
                                        SimpleDateFormat sdf = new SimpleDateFormat(Constants.dateFormat);
                                        try {
                                            modifyTimeLast = sdf.parse(appJsonLast.optString(ParameterName.modifyDate)).getTime();
                                        } catch (ParseException e) {
                                            e.printStackTrace();
                                            modifyTimeLast = 0L;
                                        }
                                        if (modifyTime > modifyTimeLast) {
                                            appJson.put(ParameterName.ruleId, ruleId);
                                            appJson.put(ParameterName.modifyDate, modifyDateStr);
                                            appsToDownload.put(packName, appJson.toString());
                                        }
                                    } else {
                                        appJson.put(ParameterName.ruleId, ruleId);
                                        appJson.put(ParameterName.modifyDate, modifyDateStr);
                                        appsToDownload.put(packName, appJson.toString());
                                    }
                                }

                                JSONArray appDeleteListTmp = rulebased.getJSONArray(ParameterName.deleteAppList);
                                for (int j=0; j<appDeleteListTmp.length(); j++) {
                                    JSONObject appDeleteJson = appDeleteListTmp.getJSONObject(j);
                                    String versionName = appDeleteJson.getString(ParameterName.versionName);
                                    String versionCode = appDeleteJson.getString(ParameterName.versionCode);
                                    String packName = appDeleteJson.getString(ParameterName.packName);
//                                    if (appsToDelete.containsKey(packName+"-"+versionCode)) {
                                    if (appsToDelete.containsKey(packName)) {
//                                        JSONObject appDeleteJsonLast = new JSONObject(appsToDelete.get(packName+"-"+versionCode));
                                        JSONObject appDeleteJsonLast = new JSONObject(appsToDelete.get(packName));
                                        String versionNameTmp = appDeleteJsonLast.getString(ParameterName.versionName);
                                        String versionCodeTmp = appDeleteJsonLast.getString(ParameterName.versionCode);
                                        SimpleDateFormat sdf = new SimpleDateFormat(Constants.dateFormat);
                                        try {
                                            modifyTimeLast = sdf.parse(appDeleteJsonLast.optString(ParameterName.modifyDate)).getTime();
                                        } catch (ParseException e) {
                                            e.printStackTrace();
                                            modifyTimeLast = 0L;
                                        }
                                        if (modifyTime > modifyTimeLast) {
                                            appDeleteJson.put(ParameterName.ruleId, ruleId);
                                            appDeleteJson.put(ParameterName.modifyDate, modifyDateStr);
//                                            appsToDelete.put(packName+"-"+versionCodeTmp, appDeleteJson.toString());
                                            appsToDelete.put(packName, appDeleteJson.toString());
                                        }
                                    } else {
                                        appDeleteJson.put(ParameterName.ruleId, ruleId);
                                        appDeleteJson.put(ParameterName.modifyDate, modifyDateStr);
//                                        appsToDelete.put(packName+"-"+versionCode, appDeleteJson.toString());
                                        appsToDelete.put(packName, appDeleteJson.toString());
                                    }
                                }
                            }
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }
                }
            }

            BBLog.i(TAG, "To-remove unbox app：" + appsToDelete.toString());
            BBLog.i(TAG, "To-install unbox app：" + appsToDownload.toString());
            if (appsToDownload.containsKey(UsualData.LAUNCHER_711_PACKAGE_NAME)) {
                String launcherApp = appsToDownload.get(UsualData.LAUNCHER_711_PACKAGE_NAME);
                appsToDownload.remove(UsualData.LAUNCHER_711_PACKAGE_NAME);
                for (String object : appsToDownload.values()) {
                    JSONObject taskJsonDownload = new JSONObject(object);
                    String packName = taskJsonDownload.getString(ParameterName.packName);
                    String versionName = taskJsonDownload.getString(ParameterName.versionName);
                    String versionCode = taskJsonDownload.getString(ParameterName.versionCode);
//                    if (appsToDelete.containsKey(packName+"-"+versionCode)) {
                    if (appsToDelete.containsKey(packName)) {
//                        JSONObject taskJsonDelete = new JSONObject(appsToDelete.get(packName+"-"+versionCode));
                        JSONObject taskJsonDelete = new JSONObject(appsToDelete.get(packName));
                        if (taskJsonDownload.has(ParameterName.modifyDate) && taskJsonDelete.has(ParameterName.modifyDate)) {
                            String modifyDateStr = taskJsonDownload.getString(ParameterName.modifyDate);
                            String modifyDateStrDelete = taskJsonDelete.getString(ParameterName.modifyDate);
                            long modifyTime = 0L;
                            long modifyTimeDelete = 0L;
                            SimpleDateFormat sdf = new SimpleDateFormat(Constants.dateFormat);
                            if (DateTimeUtils.isNumeric(modifyDateStr) && DateTimeUtils.isNumeric(modifyDateStrDelete)) {
                                modifyTime = new Long(modifyDateStr).longValue();
                                modifyTimeDelete = new Long(modifyDateStrDelete).longValue();
                            } else {
                                modifyTime = sdf.parse(modifyDateStr).getTime();
                                modifyTimeDelete = sdf.parse(modifyDateStrDelete).getTime();
                            }
                            if (modifyTime > modifyTimeDelete) {
                                //如果要求刪除的 modify 更新，那麽直接不安裝；
                                appList.put(taskJsonDownload);
                            }
                        } else {
                            appList.put(taskJsonDownload);
                        }
                    } else {
                        appList.put(taskJsonDownload);
                    }
                }
                appList.put(new JSONObject(launcherApp));
                appsToDownload.put(UsualData.LAUNCHER_711_PACKAGE_NAME, launcherApp);
            } else {
                for (String object : appsToDownload.values()) {
                    JSONObject taskJsonDownload = new JSONObject(object);
                    String packName = taskJsonDownload.getString(ParameterName.packName);
                    String versionName = taskJsonDownload.getString(ParameterName.versionName);
                    String versionCode = taskJsonDownload.getString(ParameterName.versionCode);
//                    if (appsToDelete.containsKey(packName+"-"+versionCode)) {
                    if (appsToDelete.containsKey(packName)) {
//                        JSONObject taskJsonDelete = new JSONObject(appsToDelete.get(packName+"-"+versionCode));
                        JSONObject taskJsonDelete = new JSONObject(appsToDelete.get(packName));
                        if (taskJsonDownload.has(ParameterName.modifyDate) && taskJsonDelete.has(ParameterName.modifyDate)) {
                            String modifyDateStr = taskJsonDownload.getString(ParameterName.modifyDate);
                            String modifyDateStrDelete = taskJsonDelete.getString(ParameterName.modifyDate);
                            long modifyTime = 0L;
                            long modifyTimeDelete = 0L;
                            SimpleDateFormat sdf = new SimpleDateFormat(Constants.dateFormat);
                            if (DateTimeUtils.isNumeric(modifyDateStr) && DateTimeUtils.isNumeric(modifyDateStrDelete)) {
                                modifyTime = new Long(modifyDateStr).longValue();
                                modifyTimeDelete = new Long(modifyDateStrDelete).longValue();
                            } else {
                                modifyTime = sdf.parse(modifyDateStr).getTime();
                                modifyTimeDelete = sdf.parse(modifyDateStrDelete).getTime();
                            }
                            if (modifyTime > modifyTimeDelete) {
                                //如果要求刪除的 modify 更新，那麽直接不安裝；
                                appList.put(taskJsonDownload);
                            }
                        } else {
                            appList.put(taskJsonDownload);
                        }
                    } else {
                        appList.put(taskJsonDownload);
                    }
                }
            }

            BBLog.i(TAG, "START to-download unbox app count：" + appList.length());
            BBLog.i(TAG, "START to-download unbox app list：" + appList);
            if (appList.length() > 0) {
                count = 0;
                total = appList.length();
                //通知Launcher apps个数
                Intent intent_1 = new Intent(BroadcastActions.BEGIN_DOWNLOAD);
                BBLog.w(TAG, "sendBroadcast: BEGIN_DOWNLOAD");
                intent_1.putExtra("app_count", ""+appList.length());
                sendBroadcast(intent_1, RequestPermission.REQUEST_PERMISSION_INTERNET);

                //开始逐个下载
                waiting = false;
                for (int index=1; index<=appList.length(); index++) {
                    while (waiting) {
                        try {
                            Thread.sleep(100);
                        } catch (InterruptedException e) {
                            e.printStackTrace();
                        }
                    }
                    count ++;
                    waiting = true;
                    JSONObject taskJson = appList.getJSONObject(index-1);

                    final String packName = taskJson.getString(ParameterName.packName);
                    final String versionCode = taskJson.getString(ParameterName.versionCode);
                    final String versionName = taskJson.getString(ParameterName.versionName);
                    final String apkName = taskJson.getString(ParameterName.apkName);
                    final String appId = taskJson.getString(ParameterName.appId);
                    final String apkMd5 = taskJson.getString(ParameterName.apkMd5);
                    final long totalfileSize = Long.parseLong(taskJson.getString(ParameterName.apkSize));
                    String fileUrl = taskJson.getString(ParameterName.url);
                    final String filePath = Constants.APK_FILE_PATH_UNBOX + apkName;
                    final String fileKey = taskJson.optString(ParameterName.fileKey);
/*
                    if (appsToDelete.containsKey(packName+"-"+versionCode)) {
                        JSONObject taskJsonDelete = new JSONObject(appsToDelete.get(packName+"-"+versionCode));
                        String versionNameDelete = taskJsonDelete.getString(ParameterName.versionName);
                        String versionCodeDelete = taskJsonDelete.getString(ParameterName.versionCode);
                        if (taskJson.has(ParameterName.modifyDate) && taskJsonDelete.has(ParameterName.modifyDate)) {
                            String modifyDateStr = taskJson.getString(ParameterName.modifyDate);
                            String modifyDateStrDelete = taskJsonDelete.getString(ParameterName.modifyDate);
                            long modifyTime = 0L;
                            long modifyTimeDelete = 0L;
                            SimpleDateFormat sdf = new SimpleDateFormat(Constants.dateFormat);
                            if (DateTimeUtils.isNumeric(modifyDateStr) && DateTimeUtils.isNumeric(modifyDateStrDelete)) {
                                modifyTime = new Long(modifyDateStr).longValue();
                                modifyTimeDelete = new Long(modifyDateStrDelete).longValue();
                            } else {
                                modifyTime = sdf.parse(modifyDateStr).getTime();
                                modifyTimeDelete = sdf.parse(modifyDateStrDelete).getTime();
                            }
                            if (modifyTime < modifyTimeDelete) {
                                //如果要求刪除的 modify 更新，那麽直接不安裝；

                            } else {
                                //如果要求安裝的更新，則進行安裝
                            }
                        }
                    }
*/
                    int ret = RulebasedAppListManager.isApkInstalled(taskJson);
                    if (ret==1 || ret==2 || UsualData.SERVICE_PACKAGE_NAME.equals(packName)) {
                        Intent intent_0 = new Intent(BroadcastActions.APP_DOWNLOADED);
                        intent_0.putExtra("app_name", apkName);
                        intent_0.putExtra("package_name", packName);
                        BBLog.w(TAG, "sendBroadcast: APP_DOWNLOADED");
                        sendBroadcast(intent_0, RequestPermission.REQUEST_PERMISSION_INTERNET);

                        checkFinished();

                        waiting = false;
                        continue;
                    }

                    if (!TextUtils.isEmpty(fileKey)) {
                        String response_str = "";
                        for (int i=0; i<3; i++) {
                            response_str = HttpUtils.postGetFileUrl(Constants.S3_RESOURCE_URL, HttpUtils.getPostTokenRequestContent(fileKey));
                            if (!TextUtils.isEmpty(response)) {
                                break;
                            }
                        }
                        if (TextUtils.isEmpty(response_str)) {
                            updateAndUploadRuleStatus(taskJson, TaskState.DOWNLOAD_FAILED, "APK file downloaded failed");
                            return;
                        } else {
                            fileUrl = response_str;
                        }
                    } else {
                        BBLog.e(BBLog.TAG, "No fileKey, normal downloading");
                    }

                    updateAndUploadRuleStatus(taskJson, TaskState.DOWNLOAD_ING, "");
                    HttpUtils.fileDownloadByUrlWithRetry(fileUrl, filePath, totalfileSize, apkMd5, new HttpUtils.FileDownloadCallBack() {
                        @Override
                        public void requestSuccess(JSONObject responseJson) throws Exception {
                            // TODO Auto-generated method stub
                            //上送安装中报文
                            String newApkPath = null;
                            // 目前仅适用增量包处理安装
                            newApkPath = Constants.APK_FILE_PATH_UNBOX + apkName;
                            if (UsualData.SERVICE_PACKAGE_NAME.equals(packName)) {
                                BBLog.i(BBLog.TAG, "檢測到rule中的service app, 先不安裝");
                                if (serviceInfo == null) {
                                    serviceInfo = new AppInfo();
                                    serviceInfo.setApk_name(apkName);
                                    serviceInfo.setPackage_name(packName);
                                    serviceInfo.setVersion_code(versionCode);
                                    serviceInfo.setVersion_name(versionName);
                                    serviceInfo.setInstall_path(newApkPath);
                                } else {
                                    BBLog.i(BBLog.TAG, "檢測到rule中的更高版本service app, 替換，先不安裝");
                                    if (Integer.valueOf(versionCode) > Integer.valueOf(serviceInfo.getVersion_code())) {
                                        serviceInfo.setApk_name(apkName);
                                        serviceInfo.setPackage_name(packName);
                                        serviceInfo.setInstall_path(newApkPath);
                                    }
                                }
                                waiting = false;
                            } else {
                                updateAndUploadRuleStatus(taskJson, TaskState.INSTALL_ING, "");
                                installApk(taskJson, apkName, packName, newApkPath);
                            }
                        }

                        @Override
                        public void requestFail(int errorCode, String errorStr) {
                            // TODO Auto-generated method stub
                            BBLog.e(TAG, "error in download apk file" + errorStr);
                            updateAndUploadRuleStatus(taskJson, TaskState.DOWNLOAD_FAILED, "APK file downloaded failed");

                            //通知Launcher下载失败
                            Intent intent = new Intent(BroadcastActions.APP_DOWNLOAD_FAIL);
                            intent.putExtra("app_name", apkName);
                            intent.putExtra("package_name", packName);
                            intent.putExtra("error_code", "-1");
                            BBLog.w(TAG, "sendBroadcast: APP_DOWNLOAD_FAIL");
                            sendBroadcast(intent, RequestPermission.REQUEST_PERMISSION_INTERNET);

                            checkFinished();
                            waiting = false;
                        }

                        @Override
                        public void onDownloading(long curFileSize, long fileSize) {
                            //下载进度反馈 TODO
                            BBLog.w(BBLog.TAG, "onDownloading " + (int) ((curFileSize * 100) / fileSize) + "%");
                        }
                    });

                    try {
                        Thread.sleep(500);
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                    }
                }
            } else {
                //通知Launcher apps个数
                Intent intent_1 = new Intent(BroadcastActions.BEGIN_DOWNLOAD);
                BBLog.w(TAG, "sendBroadcast: BEGIN_DOWNLOAD");
                intent_1.putExtra("app_count", "0");
                sendBroadcast(intent_1, RequestPermission.REQUEST_PERMISSION_INTERNET);

                checkFinished();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void updateRuleAppStatus(String ruleId, JSONObject taskJson, String result, String errMsg) {
        try {
            String packageName = taskJson.getString(ParameterName.packName);
            JSONArray resultApkListArray = new JSONArray(ruleForApplist.get(ruleId));
            BBLog.w(TAG, "UNBOX -- updateRuleAppStatus： " + packageName + "   resultApkListArray.length() = " + resultApkListArray.length());
            for (int index=0; index<resultApkListArray.length(); index++) {
                JSONObject appTask = resultApkListArray.getJSONObject(index);
                if (appTask!=null && packageName.equals(appTask.getString(ParameterName.packName))) {
                    appTask.put(ParameterName.result, result);
                    if (!TextUtils.isEmpty(errMsg)) {
                        appTask.put(ParameterName.errorMsg, errMsg);
                    }
                    break;
                }
            }
            ruleForApplist.put(ruleId, resultApkListArray.toString());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void updateAndUploadRuleStatus(JSONObject taskJson, String result, String errMsg) {
        try {
            String ruleId = taskJson.getString(ParameterName.ruleId);
            String packageName = taskJson.getString(ParameterName.packName);
            JSONArray resultApkListArray = new JSONArray(ruleForApplist.get(ruleId));
            BBLog.w(TAG, "UNBOX -- updateAndUploadRuleStatus： " + packageName + "   resultApkListArray.length() = " + resultApkListArray.length());
            for (int index=0; index<resultApkListArray.length(); index++) {
                JSONObject appTask = resultApkListArray.getJSONObject(index);
                if (appTask!=null && packageName.equals(appTask.getString(ParameterName.packName))) {
                    appTask.put(ParameterName.result, result);
                    if (!TextUtils.isEmpty(errMsg)) {
                        appTask.put(ParameterName.errorMsg, errMsg);
                    }
                    break;
                }
            }
            ruleForApplist.put(ruleId, resultApkListArray.toString());
            WebSocketSender.C0107_uploadRulebasedResult(ruleId, TaskState.RULEBASED_EXECUTING, resultApkListArray);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void checkFinished() {
        BBLog.w(TAG, "count=" + count + "  total=" + total);
        if (count == total) {
            if (serviceInfo != null) {
                BBLog.i(BBLog.TAG, "安裝rule中的service app");
                installApk(null, serviceInfo.getApk_name(), serviceInfo.getPackage_name(), serviceInfo.getInstall_path());
                serviceInfo = null;
                return;
            }

            WebSocketSender.C0201_DeviceStatusUpload("5", Constants.IS_IN_USE);
            SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_UNBOX_STATUS, "5");

            BBLog.i(TAG, "ruleListExec.length() = " + ruleListExec.length());
            for (int i=0; i<ruleListExec.length(); i++) {
                try {
                    boolean isRuleCompleted = true;
                    JSONObject rulebased = ruleListExec.getJSONObject(i);
                    String ruleId = rulebased.getString(ParameterName.ruleId);
                    if (rulebased.has(ParameterName.serviceList) && rulebased.getJSONArray(ParameterName.serviceList).length()>0) {
                        continue;
                    }
                    if (rulebased.has(ParameterName.appList)) {
                        JSONArray appListTmp = rulebased.getJSONArray(ParameterName.appList);
                        String modifyDateStr = rulebased.getString(ParameterName.modifyDate);
                        for (int j = 0; j < appListTmp.length(); j++) {
                            JSONObject appJson = appListTmp.getJSONObject(j);
                            String packName = appJson.getString(ParameterName.packName);
                            String versionName = appJson.getString(ParameterName.versionName);
                            String versionCode = appJson.getString(ParameterName.versionCode);
//                            if (appsToDelete.containsKey(packName+"-"+versionCode)) {
                            if (appsToDelete.containsKey(packName)) {
//                                JSONObject appDeleteJson = new JSONObject(appsToDelete.get(packName+"-"+versionCode));
                                JSONObject appDeleteJson = new JSONObject(appsToDelete.get(packName));
                                String versionNameTmp = appDeleteJson.getString(ParameterName.versionName);
                                String versionCodeTmp = appDeleteJson.getString(ParameterName.versionCode);
                                SimpleDateFormat sdf = new SimpleDateFormat(Constants.dateFormat);
                                long modifyTime = 0L;
                                long modifyTimeDelete = 0L;
                                try {
                                    modifyTime = sdf.parse(modifyDateStr).getTime();
                                    modifyTimeDelete = sdf.parse(appDeleteJson.optString(ParameterName.modifyDate)).getTime();
                                } catch (ParseException e) {
                                    e.printStackTrace();
                                    modifyTime = 0L;
                                    modifyTimeDelete = 0L;
                                }
                                if (modifyTime < modifyTimeDelete) {
                                    BBLog.w(TAG, appJson.optString(ParameterName.apkName) + "As a to-delete app in latest rule，ignore it");
                                    updateRuleAppStatus(ruleId, appJson, TaskState.INSTALL_SUCCESS, "");
                                    continue;
                                } else {
                                    isRuleCompleted = false;
                                }
                            } else if (appsToDownload.containsKey(packName)) {
                                JSONObject appJsonToDownload = new JSONObject(appsToDownload.get(packName));
                                if (!versionName.equals(appJsonToDownload.getString(ParameterName.versionName))
                                        || !versionCode.equals(appJsonToDownload.getString(ParameterName.versionCode))) {
                                    BBLog.w(TAG, appJsonToDownload.optString(ParameterName.apkName) + " Exist other version in other rule，target as to-install" + appJsonToDownload.getString(ParameterName.versionName) + "-" + appJsonToDownload.getString(ParameterName.versionCode) + " version，ignore it");
                                    updateRuleAppStatus(ruleId, appJson, TaskState.INSTALL_OVERRIDE, "");
                                    continue;
                                } else {
                                    if (!appJsonToDownload.optString(ParameterName.ruleId).equals(rulebased.getString(ParameterName.ruleId))) {
                                        BBLog.w(TAG, appJsonToDownload.optString(ParameterName.apkName) + " Exist other version in other rule，target as to-install" + appJsonToDownload.getString(ParameterName.versionName) + "-" + appJsonToDownload.getString(ParameterName.versionCode) + " version，ignore it");
                                        updateRuleAppStatus(ruleId, appJson, TaskState.INSTALL_OVERRIDE, "");
                                        continue;
                                    } else {
                                        //应该是没下载、安装成功的情况
                                        if (RulebasedAppListManager.isApkInstalled(appJson) == 0) {
                                            isRuleCompleted = false;
                                        }
                                    }
                                }
                            } else {
                                if (isRuleCompleted) {
                                    if (RulebasedAppListManager.isApkInstalled(appJson) == 0) {
                                        isRuleCompleted = false;
                                    }
                                }
                            }
                        }
                        if (isRuleCompleted) {
                            RulebasedListHandler.updateRuleState(rulebased.getString(ParameterName.ruleId), RuleStatus.IMPLEMENTED);
                            WebSocketSender.C0107_uploadRulebasedResult(rulebased.getString(ParameterName.ruleId), TaskState.RULEBASED_SUCCESS, new JSONArray(ruleForApplist.get(rulebased.getString(ParameterName.ruleId))));
                            RulebasedListHandler.updateAppList(rulebased.getString(ParameterName.ruleId), rulebased.getJSONArray(ParameterName.appList));
                        } else {
                            RulebasedListHandler.updateRuleState(rulebased.getString(ParameterName.ruleId), RuleStatus.READY);
                            WebSocketSender.C0107_uploadRulebasedResult(rulebased.getString(ParameterName.ruleId), TaskState.RULEBASED_EXECUTING, new JSONArray(ruleForApplist.get(rulebased.getString(ParameterName.ruleId))));
                            RulebasedListHandler.updateAppList(rulebased.getString(ParameterName.ruleId), rulebased.getJSONArray(ParameterName.appList));
                        }
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }

            Intent intent_2 = new Intent(BroadcastActions.DOWNLOAD_FINISHED);
            BBLog.w(TAG, "sendBroadcast: DOWNLOAD_FINISHED");
            sendBroadcast(intent_2, RequestPermission.REQUEST_PERMISSION_INTERNET);

//            Constants.B_UNBOX_RUNNING = false;
        }
    }

    @SuppressWarnings("deprecation")
    private void installApk(JSONObject taskJson, final String apkName, final String packName, final String apkPath) {
        SystemManagerAdapter.installApk(getApplicationContext(), apkPath, new SystemManagerAdapter.ApkInstallCompleted() {
            @Override
            public void onInstallFinished(String pkgName, int returnCode) {
                // TODO Auto-generated method stub
                final File apkFile = new File(apkPath + ".apk");
                final File apkFileOrg = new File(apkPath + Constants.APK_ORIGINAL_SUFFIX + ".apk");
                try {
                    if (!apkFile.getCanonicalPath().startsWith(Environment.getExternalStorageDirectory().getCanonicalPath()) &&
                            !apkFileOrg.getCanonicalPath().startsWith(Environment.getExternalStorageDirectory().getCanonicalPath())) {
                        BBLog.e("InitialProcessService", "Path Traversal");
                    }
                } catch (IOException e) {
                    e.printStackTrace();
                }
                //安装成功后上送安装结果
                if (returnCode == 1) {
                    if (apkFile.exists()) {// 安装成功后删除apk文件
                        apkFile.delete();
                    } else if (apkFileOrg.exists()) {// 安装成功后删除apk文件
                        apkFileOrg.delete();
                    }

                    if (taskJson != null) {
                        updateAndUploadRuleStatus(taskJson, TaskState.INSTALL_SUCCESS, "");
                    }

                    Intent intent = new Intent(BroadcastActions.APP_DOWNLOADED);
                    intent.putExtra("app_name", apkName);
                    intent.putExtra("package_name", packName);
                    BBLog.w(TAG, "sendBroadcast: APP_DOWNLOADED");
                    sendBroadcast(intent, RequestPermission.REQUEST_PERMISSION_INTERNET);

                    //如果安装了711-launcher，设置为主Launcher
                    if (UsualData.LAUNCHER_711_PACKAGE_NAME.equals(packName)) {
                        //设置为Launcher
                        try {
                            Thread.sleep(1000);
                        } catch (InterruptedException e) {
                            e.printStackTrace();
                        }

                        WebSocketSender.C0201_DeviceStatusUpload("5", Constants.IS_IN_USE);
                        SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_UNBOX_STATUS, "5");
                        ContextUtil.setLauncherApp(getApplicationContext(), packName);
//                        hasLauncherInstalled = true;
                    }

                    checkFinished();
                } else {
                    if (apkFile.exists()) {// 安装失败后删除apk文件
                        apkFile.delete();
                    } else if (apkFileOrg.exists()) {// 安装成功后删除apk文件
                        apkFileOrg.delete();
                    }

                    if (taskJson != null) {
                        updateAndUploadRuleStatus(taskJson, TaskState.INSTALL_FAILED, "APK file installed failed: " + returnCode);
                    }

                    Intent intent = new Intent(BroadcastActions.APP_DOWNLOAD_FAIL);
                    intent.putExtra("app_name", apkName);
                    intent.putExtra("package_name", packName);
                    intent.putExtra("error_code", "99");
                    BBLog.w(TAG, "sendBroadcast: APP_DOWNLOAD_FAIL");
                    sendBroadcast(intent, RequestPermission.REQUEST_PERMISSION_INTERNET);

                    checkFinished();
                }

                waiting = false;
            }
        });
    }

    private String createResultAppList(JSONArray jsonArray) {
        BBLog.d(TAG, "createResultAppList: ");
        try {
            JSONArray appListArray = new JSONArray("[]");
            for (int index = 0; index < jsonArray.length(); index++) {
                JSONObject taskJson = jsonArray.getJSONObject(index);

                BBLog.d(TAG, "createResultAppList: " + taskJson.getString(ParameterName.packName));
                JSONObject appTask = new JSONObject();
                appTask.put(ParameterName.packName, taskJson.getString(ParameterName.packName));
                appTask.put(ParameterName.apkName, taskJson.getString(ParameterName.apkName));
                appTask.put(ParameterName.versionCode, taskJson.getString(ParameterName.versionCode));
                appTask.put(ParameterName.versionName, taskJson.getString(ParameterName.versionName));
                if (taskJson.has(ParameterName.appIconUrlEx)) {
                    appTask.put(ParameterName.appIconUrlEx, taskJson.getString(ParameterName.appIconUrlEx));
                }
                appTask.put(ParameterName.result, TaskState.EXECUTE_WATING);
                appListArray.put(appTask);
            }
            return appListArray.toString();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "";
    }

    private boolean isDownloadAppInDeleteList(JSONObject taskJsonDownload) {
        try {
            String packName = taskJsonDownload.getString(ParameterName.versionName);
            String versionName = taskJsonDownload.getString(ParameterName.versionName);
            String versionCode = taskJsonDownload.getString(ParameterName.versionCode);
            if (appsToDelete.containsKey(packName+"-"+versionCode)) {
                JSONObject taskJsonDelete = new JSONObject(appsToDelete.get(packName+"-"+versionCode));
                if (taskJsonDownload.has(ParameterName.modifyDate) && taskJsonDelete.has(ParameterName.modifyDate)) {
                    String modifyDateStr = taskJsonDownload.getString(ParameterName.modifyDate);
                    String modifyDateStrDelete = taskJsonDelete.getString(ParameterName.modifyDate);
                    long modifyTime = 0L;
                    long modifyTimeDelete = 0L;
                    SimpleDateFormat sdf = new SimpleDateFormat(Constants.dateFormat);
                    if (DateTimeUtils.isNumeric(modifyDateStr) && DateTimeUtils.isNumeric(modifyDateStrDelete)) {
                        modifyTime = new Long(modifyDateStr).longValue();
                        modifyTimeDelete = new Long(modifyDateStrDelete).longValue();
                    } else {
                        modifyTime = sdf.parse(modifyDateStr).getTime();
                        modifyTimeDelete = sdf.parse(modifyDateStrDelete).getTime();
                    }
                    if (modifyTime < modifyTimeDelete) {
                        //如果要求刪除的 modify 更新，那麽直接不安裝；
                        return true;
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }
}
