package com.bbpos.wiseapp.service;

import java.math.BigInteger;
import java.security.MessageDigest;
import java.util.List;
import android.app.Service;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.os.Bundle;
import android.os.RemoteException;

import com.bbpos.wiseapp.logger.BBLog;
import com.bbpos.wiseapp.service.common.DeviceInfoConstants;
import com.bbpos.wiseapp.system.api.DeviceInfo;
import com.bbpos.wiseapp.utils.SharedPreferencesUtils;

import com.bbpos.wiseapp.sdk.device.DeviceStatus;
import com.bbpos.wiseapp.sdk.device.IDeviceManager;

/**
 * 设备信息广告接口
 * <AUTHOR> */
public class DeviceManagerSerice extends IDeviceManager.Stub {
	/**与后后通信校验码*/
	private static final String key = "12345678";
	
	private Service service;
	public DeviceManagerSerice(Service service) {
		this.service = service;
		SharedPreferencesUtils.init(service);
	}

	@Override
	public Bundle getDeviceInfo() throws RemoteException {
		BBLog.v(CloudService.V2_TAG, "+++getDeviceInfo begin.");
		Bundle bundle = new Bundle();
		DeviceInfo deviceInfo = new DeviceInfo(service.getApplicationContext());

		bundle.putString(DeviceInfoConstants.VENDOR, deviceInfo.getManufacturer());
		bundle.putString(DeviceInfoConstants.MODEL, deviceInfo.getOSModel());
		bundle.putString(DeviceInfoConstants.SN, deviceInfo.getSn());
		BBLog.v(CloudService.V2_TAG, "sn:" + deviceInfo.getSn());
		bundle.putString(DeviceInfoConstants.VERIFY_CODE, getMD5String());
//		bundle.putString(DeviceInfoConstants.DOWNLOAD_PATH, tms.getDownloadPath());
//		bundle.putLong(DeviceInfoConstants.DOWNLOAD_FREE_SPACE, tms.getDownloadFreeSpace());
//		bundle.putString(DeviceInfoConstants.CUR_PROGS, tms.getHttpTermInfo());
//		bundle.putString(DeviceInfoConstants.PLAT_FORM, tms.getSystemInfo().getPlatform());
		//TODO os版本
		bundle.putString(DeviceInfoConstants.OS_VERSION, deviceInfo.getOsVersion());

		PackageManager manager = service.getApplication().getPackageManager();
		try {
			PackageInfo info = manager.getPackageInfo(service.getPackageName(), 0);
			bundle.putString(DeviceInfoConstants.SERVICE_VERSION, info.versionName+"-"+info.versionCode);
		} catch (Exception e) {
			e.printStackTrace();
		}

//		String serverUrl = null;
//		ParamFile pf = new ParamFile(paramModuleName,
//				paramFileName);
//		String tmsServerUrl = "00000001";
//		if (pf.getString(tmsServerUrl) != null && !"".equals(pf.getString(tmsServerUrl))){
//			serverUrl = pf.getString(tmsServerUrl);
//			bundle.putString(DeviceInfoConstants.SERVER_URL, serverUrl);
//		}

//		forceStopProgress(service.getApplication(), "com.bbpos.wiseapp.tms");

		return bundle;
	}

	/** 获取认证码的MD5值*/
	private String getMD5String() {
		DeviceInfo deviceInfo = new DeviceInfo(service.getApplicationContext());
		String src = "serialNo=" + deviceInfo.getSn() + "&modelNo="
				+ deviceInfo.getOSModel() +"&facNo="
				+ "&key=" + key;
		String value = null;
		MessageDigest md5;
		try {
			md5 = MessageDigest.getInstance("MD5");
			byte[] inputByteArray = src.getBytes();
			md5.update(inputByteArray);
			// 转换并返回结果，也是字节数组，包含16个元素
			BigInteger bi = new BigInteger(1, md5.digest());
			int md5Length = 32;
			value = bi.toString(16);//16进制
			if(value.length() < md5Length){
				for (int i = value.length(); i < md5Length; i++) {
					value = "0"+value;
				}
			}
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
			BBLog.e(CloudService.V2_TAG, "error in getMD5String");
		}

		return value;
	}

	@Override
	public List<DeviceStatus> getAllDeviceStatus() throws RemoteException {
		// TODO Auto-generated method stub
//		return DeviceStatusInfo.getAllStatisticsInfo();
		return null;
	}

	@Override
	public DeviceStatus getDeviceStatus(int type) throws RemoteException {
		// TODO Auto-generated method stub
//		return DeviceStatusInfo.getStatisticsInfoType(type);
		return null;
	}
}
