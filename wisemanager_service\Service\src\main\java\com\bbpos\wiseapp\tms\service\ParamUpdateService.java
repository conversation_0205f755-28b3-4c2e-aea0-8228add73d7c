package com.bbpos.wiseapp.tms.service;

import android.content.ContentResolver;
import android.content.ContentValues;
import android.content.Intent;
import android.net.Uri;
import android.os.Environment;

import com.bbpos.wiseapp.logger.BBLog;
import com.bbpos.wiseapp.service.WakeLockService;
import com.bbpos.wiseapp.tms.network.HttpUtils;
import com.bbpos.wiseapp.tms.utils.CallType;
import com.bbpos.wiseapp.tms.utils.Constants;
import com.bbpos.wiseapp.tms.utils.ContextUtil;
import com.bbpos.wiseapp.tms.utils.FileUtils;
import com.bbpos.wiseapp.tms.utils.ParameterFactory;
import com.bbpos.wiseapp.tms.utils.ParameterName;

import org.json.JSONObject;

import java.io.File;
import java.io.IOException;

public class ParamUpdateService extends WakeLockService {
	public static String FILE_PATH=Environment.getExternalStorageDirectory().getPath()+"/Share"+File.separator+"para"+File.separator;

	public ParamUpdateService() {
		super("ParamUpdateService");
	}

	@Override
	protected void onHandleIntent(Intent intent) {
		final String taskId = intent.getExtras().getString(ParameterName.taskId);
		String pkgName = intent.getExtras().getString(ParameterName.pkgName);
		final String fileName = FILE_PATH+pkgName;//+".xml";
		BBLog.e("ParamUpdateService", "fileName:"+fileName);
		File xmlFile = new File(fileName+".xml");
		File zipFile = new File(fileName+".zip");
		try {
			if (!xmlFile.getCanonicalPath().startsWith(Environment.getExternalStorageDirectory().getCanonicalPath()) &&
					!zipFile.getCanonicalPath().startsWith(Environment.getExternalStorageDirectory().getCanonicalPath())) {
				BBLog.e("ParamUpdateService", "Path Traversal");
			}
		} catch (IOException e) {
			e.printStackTrace();
		}
		if (xmlFile.exists()) {
			BBLog.e("ParamUpdateService", "xml file delete");
			xmlFile.delete();
		}
		if (zipFile.exists()) {
			BBLog.e("ParamUpdateService", "zip file delete");
			zipFile.delete();
		}
		HttpUtils.requestForFileWithRetry(CallType.PARAM_UPDATE_TASK, ParameterFactory.createParUpdateParam(taskId), fileName, new HttpUtils.RequestCallBack() {
			@Override
			public void requestSuccess(JSONObject responseJson) throws Exception {
				BBLog.e("requestForFileWithRetry", "responseJson:"+responseJson.toString());
				JSONObject downloadFileInfo = responseJson.getJSONObject("downloadFileInfo");
//				responseJson:{"downloadFileName":"\/storage\/emulated\/0\/Share\/para\/smartpos.tms2",
//					"downloadFileInfo":{"appParamType":2,"fileName":"4bb34a076956bc63e05014ac6e093e9e.xml",
//					"md5":"9f35ab7c5f7664f38f4414eee2304204","size":23}}
//				if (downloadFileInfo.has("appParamType")) {
//					String appParamType  = downloadFileInfo.getString("appParamType");
//					//2为xml格式
//					if (appParamType.equals("2")) {
						String newfilepath = renameEnd(fileName,"xml");
						if(installXmlPara(newfilepath)) {
							BBLog.e("ParamUpdateService", "fileName is exist:"+new File(newfilepath).exists());
//							Intent i = new Intent(BroadcastActions.PARAM_UPDATE_COMPLETE_BC);
//							sendBroadcast(i, RequestPermission.REQUEST_PERMISSION_MY_BROADCAST);
						} else {
						}
/*
					} else {
							String newfilepath = renameEnd(fileName,"zip");
							FileUtils.copyFile(new File(newfilepath), new File("/sdcard/cloudserviceverify/os/fififiif.zip"));
							File bigZipFile = new File(newfilepath);
							InputStream inputStream = new FileInputStream(bigZipFile);
							// 从文件头部读取确认包
							// 头部为4个字节的确认包长度
							byte[] lengthBytes = new byte[4];
							int confirmPackageLength = 0;
							String confirmPkgStr = null;
							if (inputStream.read(lengthBytes) != -1) {
								confirmPackageLength = (lengthBytes[3]&0x000000ff)<<24 | (lengthBytes[2]&0x000000ff)<<16
										| (lengthBytes[1]&0x000000ff)<<8 | (lengthBytes[0]&0x000000ff);
							}
							// 读取确认包
							byte[] confirmPacketBytes = new byte[confirmPackageLength];
							if (inputStream.read(confirmPacketBytes) != -1) {
								confirmPkgStr = new String(confirmPacketBytes);
								BBLog.w("confirmPkgStr", "|confirmPkgStr:"+confirmPkgStr);
								//确认包保存在本地 更新自己或更新中断电时重新安装
							}
							ConfirmPacketUtil confirmPacketUtil = new ConfirmPacketUtil(confirmPkgStr);
							saveFileFromBigZip( FILE_PATH , inputStream, confirmPacketUtil);
							SystemManagerAdapter.updateSystem(ParamUpdateService.this, newfilepath, 0);
							Helpers.updateTaskStateAndUpload(getApplicationContext(), taskId, TaskState.UPDATE_SUCCESS);
	//					Intent i = new Intent(BroadcastActions.PARAM_UPDATE_COMPLETE_BC);
	//					sendBroadcast(i, RequestPermission.REQUEST_PERMISSION_MY_BROADCAST);
					}
				} else {
					Helpers.updateTaskStateAndUpload(getApplicationContext(), taskId, TaskState.UPDATE_FAILED);
				}
*/
			}
			
			@Override
			public void requestFail(int errorCode, String errorStr) {
				BBLog.e(BBLog.TAG, "param file download failed."+errorStr);
				if (Constants.STORAGE_OVER_FLOW.equals(errorStr)) {
				} else {
				}
			}
		});
	}

	private boolean installXmlPara(String xmlFilePath) {
		try{
			Uri uri = Uri.parse("content://com.bbpos.wiseapp.service.contentprovider.ParamPathContentProvider/tblParamPath");
			ContentResolver resolve = ContextUtil.getInstance().getContentResolver();
			ContentValues values = new ContentValues();
			String fileMd5 = FileUtils.getMd5ByFile(new File(xmlFilePath));
			values.put("param_file_path", xmlFilePath);
			values.put("param_file_md5", fileMd5);
			resolve.insert(uri, values);
			return true;
		} catch(Exception e) {
			e.printStackTrace();
			BBLog.e(BBLog.TAG, "installXmlPara error");
		}
		return false;
	}
	
	/**apk文件后缀*/
	public static String renameEnd(String filenamepath,String fileend) {

		String newFilePath = filenamepath+"."+fileend;
		File oldFile = new File(filenamepath);
		File newFile = new File(newFilePath);
		try {
			if (!oldFile.getCanonicalPath().startsWith(Environment.getExternalStorageDirectory().getCanonicalPath()) &&
					!newFile.getCanonicalPath().startsWith(Environment.getExternalStorageDirectory().getCanonicalPath())) {
				BBLog.e("ParamUpdateService", "Path Traversal");
			}
		} catch (IOException e) {
			e.printStackTrace();
		}
		if (oldFile.exists() && !newFile.exists()) {
			oldFile.renameTo(new File(newFilePath));
		}
		return newFilePath;
	}
}
