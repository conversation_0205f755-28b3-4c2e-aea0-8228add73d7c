package com.dspread.mdm.service

import android.app.Application
import com.dspread.mdm.service.broadcast.handlers.service.ServiceGuardEventHandler
import com.dspread.mdm.service.constants.Constants
import com.dspread.mdm.service.platform.manager.ServiceStartupManager
import com.dspread.mdm.service.utils.log.Logger
import com.dspread.mdm.service.utils.log.LogLevel
import com.dspread.mdm.service.config.DebugConfig
import com.dspread.mdm.service.platform.monitor.NetworkTrafficMonitor
import java.io.File

class SmartMdmServiceApp : Application() {

    override fun onCreate() {
        super.onCreate()

        instance = this

        // 首先初始化 Logger，确保文件日志功能正常
        Logger.init(this, LogLevel.DEBUG, console = true, file = true)

        // 初始化统一配置管理器
        initializeDebugConfig()

        // 预创建系统目录
        initializeSystemDirectories()

        // 初始化网络流量监控器
        NetworkTrafficMonitor.init(this)

        // 启动服务守护定时器
        startServiceGuardTimer()

        // 启动主服务
        startMainService()

        Logger.com("SmartMdmServiceApp: Application启动完成，包名: $packageName")
    }

    override fun onTerminate() {
        super.onTerminate()

        // 停止服务守护定时器
        stopServiceGuardTimer()
        
        // 清理网络流量监控器资源
        NetworkTrafficMonitor.cleanup()
    }

    /**
     * 预创建系统目录
     * 在应用启动时尝试创建必要的系统目录
     */
    private fun initializeSystemDirectories() {
        try {
            Logger.com("SmartMdmServiceApp: 开始初始化系统目录...")

            // 创建配置目录
            val configDir = File(Constants.ModuleConstants.CONFIG_STORAGE_PATH)
            if (!configDir.exists()) {
                val configResult = configDir.mkdirs()
                Logger.com("SmartMdmServiceApp: 配置目录创建 - 路径: ${configDir.absolutePath}, 结果: $configResult")
            } else {
                Logger.com("SmartMdmServiceApp: 配置目录已存在 - ${configDir.absolutePath}")
            }

            // 创建Logo目录
            val logoDir = File(Constants.ModuleConstants.SYSTEM_LOGO_PATH).parentFile
            if (logoDir != null && !logoDir.exists()) {
                val logoResult = logoDir.mkdirs()
                Logger.com("SmartMdmServiceApp: Logo目录创建 - 路径: ${logoDir.absolutePath}, 结果: $logoResult")
            } else if (logoDir != null) {
                Logger.com("SmartMdmServiceApp: Logo目录已存在 - ${logoDir.absolutePath}")
            }

            // 创建开机动画目录
            val bootAnimDir = File(Constants.ModuleConstants.BOOT_ANIMATION_PATH).parentFile
            if (bootAnimDir != null && !bootAnimDir.exists()) {
                val bootAnimResult = bootAnimDir.mkdirs()
                Logger.com("SmartMdmServiceApp: 开机动画目录创建 - 路径: ${bootAnimDir.absolutePath}, 结果: $bootAnimResult")
            } else if (bootAnimDir != null) {
                Logger.com("SmartMdmServiceApp: 开机动画目录已存在 - ${bootAnimDir.absolutePath}")
            }

            Logger.com("SmartMdmServiceApp: 系统目录初始化完成")

        } catch (e: Exception) {
            Logger.comE("SmartMdmServiceApp: 初始化系统目录失败", e)
        }
    }

    /**
     * 启动服务守护定时器
     */
    private fun startServiceGuardTimer() {
        try {
            ServiceGuardEventHandler().startServiceGuardTimer(this)
            Logger.com("SmartMdmServiceApp: 服务守护定时器启动成功")
        } catch (e: Exception) {
            Logger.comE("SmartMdmServiceApp: 启动服务守护定时器失败", e)
        }
    }

    /**
     * 启动主服务（使用统一管理器）
     */
    private fun startMainService() {
        try {
            ServiceStartupManager.startService(
                this,
                ServiceStartupManager.StartupReason.APPLICATION_STARTUP
            )
            Logger.com("SmartMdmServiceApp: 主服务启动请求已提交")
        } catch (e: Exception) {
            Logger.comE("SmartMdmServiceApp: 启动主服务失败", e)
        }
    }

    /**
     * 停止服务守护定时器
     */
    private fun stopServiceGuardTimer() {
        try {
            ServiceGuardEventHandler().stopServiceGuardTimer(this)
            Logger.com("SmartMdmServiceApp: 服务守护定时器已停止")
        } catch (e: Exception) {
            Logger.comE("SmartMdmServiceApp: 停止服务守护定时器失败", e)
        }
    }

    /**
     * 初始化统一配置管理器
     */
    private fun initializeDebugConfig() {
        try {
            // 初始化统一配置管理器
            DebugConfig.init(this)

            // 设置为调试模式（可根据需要修改）
            DebugConfig.setDebugMode(true)  // 调试模式：详细日志和调试路径
//             DebugConfig.setDebugMode(false)  // 生产模式：最小日志和系统路径

            Logger.com("SmartMdmServiceApp: 统一配置管理器初始化完成")
        } catch (e: Exception) {
            Logger.comE("SmartMdmServiceApp: 统一配置管理器初始化失败", e)
        }
    }


    companion object {
        lateinit var instance: SmartMdmServiceApp
    }

}