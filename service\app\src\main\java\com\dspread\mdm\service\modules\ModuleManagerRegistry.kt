package com.dspread.mdm.service.modules

import android.annotation.SuppressLint
import android.content.Context
import com.dspread.mdm.service.constants.Constants
import com.dspread.mdm.service.modules.geofence.GeofenceManager
import com.dspread.mdm.service.modules.geofence.location.GpsLocationManager
import com.dspread.mdm.service.modules.logstream.LogStreamManager
import com.dspread.mdm.service.utils.log.Logger
import com.dspread.mdm.service.utils.storage.PreferencesManager
import kotlinx.coroutines.runBlocking
import org.json.JSONObject

/**
 * 模块管理器注册中心
 * 负责管理所有模块实例的生命周期
 */
@SuppressLint("StaticFieldLeak")
object ModuleManagerRegistry {
    
    private const val TAG = "[ModuleManagerRegistry]"
    
    private lateinit var appContext: Context

    /**
     * 初始化注册中心
     */
    fun initialize(context: Context) {
        appContext = context.applicationContext
        Logger.com("$TAG 模块管理器注册中心初始化完成")
    }

    /**
     * 获取LogStreamManager实例
     */
    fun getLogStreamManager(): LogStreamManager? {
        return try {
            if (!::appContext.isInitialized) return null
            LogStreamManager(appContext)
        } catch (e: Exception) {
            Logger.comE("$TAG 获取LogStreamManager实例失败", e)
            null
        }
    }

    /**
     * 启动所有模块
     */
    suspend fun startAllModules() {
        Logger.com("$TAG 启动所有模块")

        // 1. LogStreamManager - 日志存储基础设施
        try {
            val logStreamManager = getLogStreamManager()
            logStreamManager?.let {
                val initResult = it.initialize()
                if (initResult.isSuccess) {
                    val startResult = it.start()
                    if (startResult.isFailure) {
                        throw startResult.exceptionOrNull() ?: RuntimeException("LogStreamManager启动失败")
                    }
                    Logger.com("$TAG LogStreamManager启动成功")
                } else {
                    throw initResult.exceptionOrNull() ?: RuntimeException("LogStreamManager初始化失败")
                }
            }
        } catch (e: Exception) {
            Logger.comE("$TAG LogStreamManager启动失败", e)
        }

        // 2. GeofenceManager - 地理围栏功能
        try {
            if (::appContext.isInitialized) {
                // 初始化地理围栏Profile
                initializeGeofenceProfile()

                val success = GeofenceManager.initialize(appContext)
                if (success) {
                    Logger.com("$TAG GeofenceManager启动成功")
                } else {
                    throw RuntimeException("GeofenceManager初始化失败")
                }
            }
        } catch (e: Exception) {
            Logger.comE("$TAG GeofenceManager启动失败", e)
        }

        // 3. RuleBaseManager - 规则引擎
        try {
            if (::appContext.isInitialized) {
                val ruleBaseManager = com.dspread.mdm.service.modules.rulebase.RuleBaseManager.getInstance(appContext)
                ruleBaseManager.initialize()
                Logger.com("$TAG RuleBaseManager启动成功")
            }
        } catch (e: Exception) {
            Logger.comE("$TAG RuleBaseManager启动失败", e)
        }

        // TODO: 启动其他模块（WiFi Profile, APN等）

        Logger.com("$TAG 所有模块启动完成")
    }

    /**
     * 初始化地理围栏Profile配置
     */
    private fun initializeGeofenceProfile() {
        try {
            Logger.com("$TAG 开始初始化地理围栏Profile...")

            val geofencePrefs = appContext.getSharedPreferences("geofence_state", Context.MODE_PRIVATE)

            // 初始化地理围栏状态
            Constants.geofenceStatus = geofencePrefs.getInt("geofence_status", GpsLocationManager.IN_ZONE)
            Constants.storeId = geofencePrefs.getString("store_id", "") ?: ""
            Constants.storeSsid = geofencePrefs.getString("store_ssid", "") ?: ""
            Constants.storeIp = geofencePrefs.getString("store_ip", "") ?: ""

            Logger.com("$TAG 地理围栏状态: ${Constants.geofenceStatus}")
            Logger.com("$TAG 商店信息: ID=${Constants.storeId}, SSID=${Constants.storeSsid}, IP=${Constants.storeIp}")

            // 初始化有效距离
            try {
                val validDistanceStr = PreferencesManager.getString("valid_distance", "500")
                if (validDistanceStr.all { it.isDigit() }) {
                    GpsLocationManager.validErrorDistance = validDistanceStr.toFloatOrNull() ?: 500f
                } else {
                    GpsLocationManager.validErrorDistance = 500f
                }
                Logger.com("$TAG 有效距离设置: ${GpsLocationManager.validErrorDistance}米")
            } catch (e: Exception) {
                GpsLocationManager.validErrorDistance = 500f
                Logger.comE("$TAG 设置有效距离失败，使用默认值500米", e)
            }

            // 初始化地理围栏Profile
            try {
                val profile = geofencePrefs.getString("geofence_current_profile", "") ?: ""
                if (profile.isNotEmpty()) {
                    val profileJson = JSONObject(profile)
                    GpsLocationManager.initGeoProfile(appContext, profileJson)
                    Logger.com("$TAG 地理围栏Profile初始化成功")
                } else {
                    GpsLocationManager.initGeoProfile(appContext, null)
                    Logger.com("$TAG 使用默认地理围栏Profile")
                }
            } catch (e: Exception) {
                Logger.comE("$TAG 地理围栏Profile初始化失败", e)
            }

            Logger.com("$TAG 地理围栏Profile初始化完成")

        } catch (e: Exception) {
            Logger.comE("$TAG 初始化地理围栏Profile失败", e)
        }
    }
    
    /**
     * 清理所有模块实例
     */
    fun cleanup() {
        Logger.com("$TAG 清理模块实例")

        // 停止LogStreamManager
        try {
            val logStreamManager = getLogStreamManager()
            logStreamManager?.let {
                // 使用runBlocking在普通函数中调用suspend函数
                runBlocking {
                    val stopResult = it.stop()
                    if (stopResult.isSuccess) {
                        Logger.com("$TAG LogStreamManager停止成功")
                    } else {
                        Logger.comE("$TAG LogStreamManager停止失败", stopResult.exceptionOrNull())
                    }
                }
            }
        } catch (e: Exception) {
            Logger.comE("$TAG LogStreamManager停止失败", e)
        }

        // GeofenceManager和RuleBaseManager目前没有stop方法，无需处理

        Logger.com("$TAG 模块实例清理完成")
    }
}
