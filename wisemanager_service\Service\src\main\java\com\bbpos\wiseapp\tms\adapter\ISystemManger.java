package com.bbpos.wiseapp.tms.adapter;

import android.content.Context;
import android.os.IBinder;

import com.bbpos.wiseapp.tms.adapter.SystemManagerAdapter.ApkInstallCompleted;
import com.bbpos.wiseapp.tms.adapter.SystemManagerAdapter.ApkUnInstallCompleted;
import com.bbpos.wiseapp.tms.adapter.SystemManagerAdapter.BackupCompleted;
import com.bbpos.wiseapp.tms.adapter.SystemManagerAdapter.RecoveryCompleted;

import java.util.List;

import com.bbpos.wiseapp.sdk.app.UsageStats;
import com.bbpos.wiseapp.sdk.device.DeviceStatus;

public interface ISystemManger {
	/***
	 * 绑定服务后，固化服务句柄
	 * @param binder
	 */
	public void initCloudManager(IBinder binder);

	/***
	 * 判断服务是否绑定  
	 */
	public boolean isServiceBinded();

	/***
	 * 服务解绑回调
	 */
	public void unBindCloudManager();

	public void reboot(Context context);
	
	public void initTerParam(Context context);

	public void installApk(Context context, String appPath, ApkInstallCompleted observer);

	public void updateSystem(Context context, String osFilePath, int updateType);

	public void unInstallApk(Context context, String apkPkgs, ApkUnInstallCompleted observer);

	public void backupByPkgName(List<String> pkgList, BackupCompleted listener);
	
	public void restore(String path, RecoveryCompleted listener);
	
	public List<UsageStats> getUsageStats(String yyyyMMdd);
	
	public DeviceStatus getDeviceStatus(int type);
    //获取所有可获取设备状态信息
	public List<DeviceStatus> getAllDeviceStatus();
	
	public class ApkIndex {
		private int i;

		public ApkIndex(int i) {
			this.i = i;
		}

		public int getIndex() {
			return this.i;
		}

		public void setIndex(int i) {
			this.i = i;
		}

		public ApkIndex add() {
			this.i++;
			return this;
		}
	}

}
