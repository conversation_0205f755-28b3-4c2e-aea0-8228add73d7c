<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:gravity="center"
    android:id="@+id/root"
    android:background="@color/provisionin_color">

    <LinearLayout
        android:id="@+id/ll_progress"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:gravity="center"
        android:orientation="vertical"
        android:visibility="visible">
        <ImageView
            android:id="@+id/iv_progress"
            android:layout_width="80dp"
            android:layout_height="80dp"
            android:padding="10dp" />
        <TextView
            android:id="@+id/tv_count"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:padding="10dp"
            android:gravity="center"
            android:text="Provisioning"
            android:textColor="@color/white"
            android:textSize="25sp"/>
        <LinearLayout
            android:id="@+id/ll_progressbar"
            android:layout_width="250dp"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:visibility="visible">
            <ProgressBar
                android:id="@+id/pb_progress"
                android:layout_width="match_parent"
                android:layout_height="7dp"
                style="@android:style/Widget.ProgressBar.Horizontal"
                android:max="100"
                android:progress="0" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="26dp"
                android:gravity="right"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/tv_prog"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="@color/white" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="/"
                    android:textColor="@color/white" />

                <TextView
                    android:id="@+id/tv_max"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="@color/white" />
            </LinearLayout>

        </LinearLayout>
        <TextView
            android:id="@+id/tv_err"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            tools:text="error msg"
            android:textSize="15sp"
            android:gravity="center"
            android:textColor="@color/white"
            android:visibility="visible"/>
        <TextView
            android:id="@+id/tv_exit_tip"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            tools:text="upgrade will exit after 10 sec "
            android:textSize="15sp"
            android:gravity="center"
            android:textColor="@color/white"
            android:visibility="visible"/>
    </LinearLayout>

</LinearLayout>