package com.dspread.mdm.service

import android.app.Application
import com.dspread.mdm.service.broadcast.core.BroadcastManager
import com.dspread.mdm.service.broadcast.handlers.service.ServiceGuardEventHandler
import com.dspread.mdm.service.broadcast.handlers.service.ServiceManagementEventHandlerImpl
import com.dspread.mdm.service.broadcast.handlers.system.*
import com.dspread.mdm.service.broadcast.handlers.websocket.*
import com.dspread.mdm.service.config.*
import com.dspread.mdm.service.constants.Constants
import com.dspread.mdm.service.platform.manager.ServiceStartupManager
import com.dspread.mdm.service.platform.monitor.NetworkTrafficMonitor
import com.dspread.mdm.service.utils.log.Logger
import com.dspread.mdm.service.utils.log.LogLevel
import java.io.File

/**
 * SmartMDM服务应用程序主类
 * 负责应用程序的全局初始化和生命周期管理
 *
 * 主要功能：
 * - 系统配置初始化
 * - 广播管理器初始化
 * - 定时器系统启动
 * - 主服务启动
 */
class SmartMdmServiceApp : Application() {

    companion object {
        private const val TAG = "SmartMdmServiceApp"
        lateinit var instance: SmartMdmServiceApp
            private set
    }

    // ==================== 生命周期方法 ====================

    override fun onCreate() {
        super.onCreate()

        instance = this

        try {
            Logger.com("$TAG: 开始应用程序初始化...")

            // 1. 初始化系统配置
            initializeSystemConfiguration()

            // 2. 初始化广播管理器
            initializeBroadcastSystem()

            // 3. 启动定时器系统
            initializeTimerSystem()

            // 4. 启动主服务
            initializeMainService()

            Logger.com("$TAG: 应用程序初始化完成，包名: $packageName")

        } catch (e: Exception) {
            Logger.comE("$TAG: 应用程序初始化失败", e)
        }
    }

    override fun onTerminate() {
        super.onTerminate()

        try {
            Logger.com("$TAG: 开始应用程序终止清理...")

            // 1. 停止定时器系统
            cleanupTimerSystem()

            // 2. 清理广播系统
            cleanupBroadcastSystem()

            // 3. 清理网络监控器
            cleanupNetworkMonitor()

            Logger.com("$TAG: 应用程序终止清理完成")

        } catch (e: Exception) {
            Logger.comE("$TAG: 应用程序终止清理失败", e)
        }
    }

    // ==================== 系统配置初始化 ====================

    /**
     * 初始化系统配置
     * 包括日志系统、调试配置、系统目录等
     */
    private fun initializeSystemConfiguration() {
        try {
            Logger.com("$TAG: 开始初始化系统配置...")

            // 1. 初始化日志系统
            initializeLoggingSystem()

            // 2. 初始化调试配置
            initializeDebugConfiguration()

            // 3. 初始化系统目录
            initializeSystemDirectories()

            Logger.com("$TAG: 系统配置初始化完成")

        } catch (e: Exception) {
            Logger.comE("$TAG: 系统配置初始化失败", e)
        }
    }

    /**
     * 初始化日志系统
     */
    private fun initializeLoggingSystem() {
        try {
            Logger.init(this, LogLevel.DEBUG, console = true, file = true)
            LogStreamConfig.init(this)
            Logger.com("$TAG: 日志系统初始化完成")
        } catch (e: Exception) {
            Logger.comE("$TAG: 日志系统初始化失败", e)
        }
    }

    /**
     * 初始化调试配置
     */
    private fun initializeDebugConfiguration() {
        try {
            DebugConfig.init(this)
            DebugConfig.setDebugMode(true)  // 调试模式：详细日志和调试路径
//             DebugConfig.setDebugMode(false)  // 生产模式：最小日志和系统路径
            Logger.com("$TAG: 调试配置初始化完成")
        } catch (e: Exception) {
            Logger.comE("$TAG: 调试配置初始化失败", e)
        }
    }

    /**
     * 初始化系统目录
     * 按优先级选择可用目录并创建配置目录
     */
    private fun initializeSystemDirectories() {
        try {
            val selectedPath = selectOptimalDirectory()
            val configDir = File(selectedPath, "config")

            if (!configDir.exists()) {
                configDir.mkdirs()
            }

            Constants.ModuleConstants.CONFIG_STORAGE_PATH = configDir.absolutePath + "/"
            Logger.com("$TAG: 系统目录初始化完成 - ${Constants.ModuleConstants.CONFIG_STORAGE_PATH}")

        } catch (e: Exception) {
            Logger.comE("$TAG: 系统目录初始化失败", e)
        }
    }

    /**
     * 选择最优的存储目录
     * 调试模式使用外部存储，生产模式按优先级选择
     */
    private fun selectOptimalDirectory(): String {
        return if (DebugConfig.isDebugMode()) {
            Constants.ModuleConstants.TERTIARY_PATH
        } else {
            selectProductionDirectory()
        }
    }

    /**
     * 生产模式按优先级选择目录
     */
    private fun selectProductionDirectory(): String {
        val directories = listOf(
            Constants.ModuleConstants.PRIMARY_PATH,
            Constants.ModuleConstants.SECONDARY_PATH,
            Constants.ModuleConstants.TERTIARY_PATH
        )

        return directories.firstOrNull { isDirectoryUsable(it) }
            ?: Constants.ModuleConstants.TERTIARY_PATH
    }

    /**
     * 检查目录是否可用
     */
    private fun isDirectoryUsable(path: String): Boolean {
        return try {
            val dir = File(path)
            if (!dir.exists()) {
                dir.mkdirs()
            }
            dir.exists() && dir.canRead() && dir.canWrite()
        } catch (e: Exception) {
            false
        }
    }

    // ==================== 广播系统初始化 ====================

    /**
     * 初始化广播系统
     * 包括广播管理器初始化和事件处理器注册
     */
    private fun initializeBroadcastSystem() {
        try {
            Logger.com("$TAG: 开始初始化广播系统...")

            BroadcastManager.initialize(this)
            registerEventHandlers()

            Logger.com("$TAG: 广播系统初始化完成")

        } catch (e: Exception) {
            Logger.comE("$TAG: 广播系统初始化失败", e)
        }
    }

    /**
     * 注册所有事件处理器
     */
    private fun registerEventHandlers() {
        val handlers = listOf(
            NetworkEventHandlerImpl(),
            BatteryEventHandlerImpl(),
            SystemEventHandlerImpl(),
            ScreenEventHandlerImpl(),
            HeartbeatEventHandlerImpl(),
            TaskExecuteEventHandlerImpl(),
            TerminalInfoEventHandlerImpl(),
            PackageUpdateEventHandlerImpl(),
            ServiceGuardEventHandler(),
            ServiceManagementEventHandlerImpl(),
            WakeLockEventHandlerImpl(),
            ProvisioningEventHandler()
        )

        handlers.forEach { handler ->
            BroadcastManager.registerEventHandler(handler)
        }

        Logger.com("$TAG: 已注册 ${handlers.size} 个事件处理器")
    }

    // ==================== 定时器系统初始化 ====================

    /**
     * 初始化定时器系统
     * 按优先级启动所有定时器
     */
    private fun initializeTimerSystem() {
        try {
            Logger.com("$TAG: 开始初始化定时器系统...")

            val timerConfigs = listOf(
                TimerConfigure("服务守护", 1) { ServiceGuardEventHandler().startServiceGuardTimer(this) },
                TimerConfigure("初始化", 2) { ProvisioningEventHandler().startProvisioningTimer(this) },
                TimerConfigure("心跳", 3) { HeartbeatEventHandlerImpl().scheduleNextHeartbeat(this) },
                TimerConfigure("任务执行", 4) { TaskExecuteEventHandlerImpl().scheduleNextTaskExecution(this) },
                TimerConfigure("终端信息", 5) { TerminalInfoEventHandlerImpl().scheduleNextUpload(this) }
            )

            timerConfigs.forEach { config ->
                try {
                    config.startAction()
                    Logger.com("$TAG: ${config.name}定时器启动成功")
                } catch (e: Exception) {
                    Logger.comE("$TAG: ${config.name}定时器启动失败", e)
                }
            }

            TimerConfig.printCurrentConfig(this)
            Logger.com("$TAG: 定时器系统初始化完成")

        } catch (e: Exception) {
            Logger.comE("$TAG: 定时器系统初始化失败", e)
        }
    }

    /**
     * 初始化主服务
     * 使用统一的服务启动管理器
     */
    private fun initializeMainService() {
        try {
            ServiceStartupManager.startService(
                this,
                ServiceStartupManager.StartupReason.APPLICATION_STARTUP
            )
            Logger.com("$TAG: 主服务启动请求已提交")
        } catch (e: Exception) {
            Logger.comE("$TAG: 主服务启动失败", e)
        }
    }

    // ==================== 清理方法 ====================

    /**
     * 清理定时器系统
     */
    private fun cleanupTimerSystem() {
        try {
            ServiceGuardEventHandler().stopServiceGuardTimer(this)
            Logger.com("$TAG: 定时器系统清理完成")
        } catch (e: Exception) {
            Logger.comE("$TAG: 定时器系统清理失败", e)
        }
    }

    /**
     * 清理广播系统
     */
    private fun cleanupBroadcastSystem() {
        try {
            BroadcastManager.cleanup(this)
            Logger.com("$TAG: 广播系统清理完成")
        } catch (e: Exception) {
            Logger.comE("$TAG: 广播系统清理失败", e)
        }
    }

    /**
     * 清理网络监控器
     */
    private fun cleanupNetworkMonitor() {
        try {
            NetworkTrafficMonitor.cleanup()
            Logger.com("$TAG: 网络监控器清理完成")
        } catch (e: Exception) {
            Logger.comE("$TAG: 网络监控器清理失败", e)
        }
    }

    // ==================== 内部数据类 ====================

    /**
     * 定时器配置数据类
     */
    private data class TimerConfigure(
        val name: String,
        val priority: Int,
        val startAction: () -> Unit
    )

}