package com.bbpos.wiseapp.websocket;

import android.content.Context;
import android.content.Intent;
import android.net.wifi.ScanResult;
import android.net.wifi.WifiConfiguration;
import android.net.wifi.WifiInfo;
import android.text.TextUtils;

import androidx.annotation.Nullable;

import com.bbpos.wiseapp.logger.BBLog;
import com.bbpos.wiseapp.network.wificonfig.ProfileEnum.WifiProxyTypeEnum;
import com.bbpos.wiseapp.network.wificonfig.ProfileEnum.WifiSecurityTypeEnum;
import com.bbpos.wiseapp.network.wificonfig.WiFiProfileManager;
import com.bbpos.wiseapp.network.wificonfig.listener.OnWifiConnectListener;
import com.bbpos.wiseapp.service.WakeLockService;
import com.bbpos.wiseapp.settings.utils.WirelessUtil;
import com.bbpos.wiseapp.tms.utils.AESUtil;
import com.bbpos.wiseapp.tms.utils.Constants;
import com.bbpos.wiseapp.tms.utils.ContextUtil;
import com.bbpos.wiseapp.tms.utils.DateTimeUtils;
import com.bbpos.wiseapp.tms.utils.ParameterName;
import com.bbpos.wiseapp.tms.utils.SPKeys;
import com.bbpos.wiseapp.tms.utils.WebSocketServiceListManager;
import com.bbpos.wiseapp.utils.SharedPreferencesUtils;
import com.bbpos.wiseapp.websocket.nv.WsManager;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.neovisionaries.ws.client.WebsocketStatus;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * wifi profile 處理
 */
public class WifiProfileProcessService extends WakeLockService {
    public static final String TAG = BBLog.TAG;
    private static final long WAIT_TIME = 15 * 1000;
    private static final long WIFI_DELAY_EXECUTE_TIME = 24 * 60 * 60 * 1000;//延迟执行时间（24 hour）
    public static AtomicBoolean isRunning = new AtomicBoolean(false);
    private static OnWifiConnectListener wifiConnectListener;
    public  static JSONArray currentCacheWifiProfile;
    public static Context mContext = ContextUtil.getInstance();
    
    public static boolean isConnected;
    public static String ssid = "";
    private int networkId = -1;

     static {
         wifiConnectListener = new OnWifiConnectListener() {
             @Override
             public void onWiFiConnectLog(String log) {

             }

             @Override
             public void onWiFiConnectSuccess(String SSID) {
                 BBLog.e(BBLog.TAG,"onWiFiConnectSuccess : wifi [" +SSID+ "] CONNECTED");
                 isConnected = true;
                 ssid = SSID;
             }

             @Override
             public void onWiFiConnectFailure(String SSID) {
                 BBLog.e(TAG, "onWiFiConnectFailure: wifi [" +SSID+ "] DISCONNECTED");
                 isConnected = false;
                 ssid = SSID;
             }
         };
     }

    public WifiProfileProcessService() {
        super("WifiProfileProcessService");
    }

    @Override
    protected void onHandleIntent(@Nullable Intent intent) {
        BBLog.w(TAG, "WifiProfileProcessService BEGIN");
//        BBLog.d(TAG, "WifiProfileProcessService [onHandleIntent]: wifiList = "+ currentCacheWifiProfile);
        if (currentCacheWifiProfile == null) {
            BBLog.e(TAG, "onHandleIntent: wifiProfileJson is null" );
            return;
        }
        isRunning.compareAndSet(false,true);
        mContext = ContextUtil.getInstance();
        processLogicByWifiProfile(currentCacheWifiProfile);
        isRunning.compareAndSet(true,false);
        WiFiProfileManager.getInstance(mContext).setOnWifiConnectListener(null);
    }

    public void processLogicByWifiProfile(JSONArray wifiList) {
//        BBLog.d(TAG, "WifiProfileProcessService [processLogicByWifiProfile]: wifiList = "+ wifiList);
        //2019年8月15日 同Joe讨论过。如果下发数据为空时，本地不做任何操作（禁断开wifi）。尽可能保证终端在线并可控状态
        if (wifiList == null || wifiList.length() == 0) return;
        //保存数据到内存中，用于同SharedPreferences中缓存的数据做比较
        currentCacheWifiProfile = wifiList;
//        // 保持所有list中wifi
//        saveAllWifiProfileToWifiConfig();
        //获取符合当前时间的 wifi profile
        JSONArray newWiFiList = getWifiProfileBetweenCurrentDate(wifiList);
        //根据order进行优先级排序
        newWiFiList = sortWifiProfileByOrder(newWiFiList);
//        BBLog.i(TAG, "WifiProfileProcessService [processLogicByWifiProfile]: 按优先级排序后 newWiFiList= " + newWiFiList);
        // 执行切换wifi操作
        switchWifiProfileByOrder(newWiFiList);
    }

    /**
     * 剔除不合法的wifi（终端已经连接过的、但不在wifi profile list中的wifi）
     */
    private void delNonListWifiFromWifiConfig() {
        BBLog.d(TAG, "WifiProfileProcessService [delNonListWifiFromWifiConfig] ");
        //获取本地已经连接过的wifi信息
        List<WifiConfiguration> existingConfigs = WiFiProfileManager.getInstance(ContextUtil.getInstance()).getConfiguredNetworks();
        //当前正在连接的wifi ssid 信息
        WifiInfo wifiInfo = WiFiProfileManager.getInstance(ContextUtil.getInstance()).getConnectionInfo();
        try {
            String curSSID = "";
            if (wifiInfo!=null) {
                //wifiInfo.getSSID(): hidden wifi 取值可能为空
                curSSID = wifiInfo.getSSID()!=null ? wifiInfo.getSSID().replace("\"",""): "";
            }

            List<WifiConfiguration> exitsConfigs = new ArrayList<>();
            if (null != existingConfigs) {
                //获取存在 wifi profile list中的 wificonfig，包括当前已连接的wifi
                for (WifiConfiguration config : existingConfigs) {
                    for (int i = 0;i<currentCacheWifiProfile.length();i++) {
                        if (config.SSID.replace("\"","").equals(currentCacheWifiProfile.getJSONObject(i).getString(ParameterName.ssid))) {//存在 profile list中的wifi
                            if (!exitsConfigs.contains(config))
                                exitsConfigs.add(config);
                        }else if (curSSID.equals(config.SSID.replace("\"",""))) {//当前正在连接的wifi
                            if (!exitsConfigs.contains(config))
                                exitsConfigs.add(config);
                        }
                    }
                }
                // 剔除掉不在profile list中的config
                if (exitsConfigs!=null && exitsConfigs.size()>0) {
                    existingConfigs.removeAll(exitsConfigs);
                }
                //清除
                for (WifiConfiguration config : existingConfigs) {
                    BBLog.e(TAG, "delNonListWifiFromWifiConfig: remove wifi config : " + config.SSID.replace("\"","") );
                    WiFiProfileManager.getInstance(ContextUtil.getInstance()).removedWifiConfig(config);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 配置wifi信息，执行wifi切换操作
     *
     * @param wifiList
     */
    private void switchWifiProfileByOrder(JSONArray wifiList) {
        WiFiProfileManager.getInstance(mContext).setOnWifiConnectListener(wifiConnectListener);
        try {
            if (wifiList == null || wifiList.length() == 0) {//如果集合为空，则尝试获取default wifi
                networkId = -1;
                JSONObject defaultWifiProfile = getDefaultWifi(currentCacheWifiProfile);
//                BBLog.i(TAG, "WifiProfileProcessService [switchWifiProfileByOrder]: size = 0， defaultWifiProfile = " + defaultWifiProfile);
                if (defaultWifiProfile!=null) {
                    if (defaultWifiProfile.has(ParameterName.wifi_lost_time)) {
                        BBLog.e(TAG, "WifiProfileProcessService [switchWifiProfileByOrder]: default wifi[" + defaultWifiProfile.getString(ParameterName.ssid) + "] delayed for 24H to exec");
                        return;
                    }
                    if (isCurrentWifiConnected(defaultWifiProfile)) {
//                        String cacheJson = SharedPreferencesUtils.getSharePreferencesValue(SPKeys.LAST_CONNECTED_WIFL_PROFILE,"");
                        String cacheJson = readConnectedWifiProfile();
                        if (!TextUtils.isEmpty(cacheJson)) {
                            JSONObject temp = new JSONObject(cacheJson);
                            if ((temp != null) && isTheSameWifiProfile(defaultWifiProfile, temp)) {
                                BBLog.e(TAG, "WifiProfileProcessService [switchWifiProfileByOrder]: default wifi[" + defaultWifiProfile.getString(ParameterName.ssid) + "]connected，profile no change");
                                if (WebsocketStatus.CONNECT_SUCCESS == WsManager.getInstance().getStatus() || WirelessUtil.ping()) {
                                    removeCacheWifiFromList();
                                    return;
                                } else {
                                    delayWifiConnect(defaultWifiProfile, ssid);
                                    isConnected = false;
                                    networkId = -1;
                                }
                            } else {
                                printCacheWplData();
                                BBLog.e(TAG, "WifiProfileProcessService [switchWifiProfileByOrder]:  wifi[" + defaultWifiProfile.getString(ParameterName.ssid) + "]connected，profile change，need to reconnected");
                            }
                        }
                    }
//                    if (!checkIsWifiInRange(defaultWifiProfile)) return;
                    if (checkIsWifiInRange(defaultWifiProfile) || isHiddentWifi(defaultWifiProfile)) {
                        networkId = handlerWifiConnectBySecurityType(networkId, defaultWifiProfile);
                        //检查连接状态
                        checkConnectStatus(defaultWifiProfile);
                    }
                }else {
                    BBLog.e(TAG, "WifiProfileProcessService default wifi not exist");
                }
            }else if (wifiList.length() == 1) {//只有一个wifi profile
                networkId = -1;
//                BBLog.i(TAG, "WifiProfileProcessService [switchWifiProfileByOrder]: size = 1， wifiProfile = " + wifiList.getJSONObject(0));
                if (isCurrentWifiConnected(wifiList.getJSONObject(0))) {
//                    String cacheJson = SharedPreferencesUtils.getSharePreferencesValue(SPKeys.LAST_CONNECTED_WIFL_PROFILE,"");
					String cacheJson = readConnectedWifiProfile();
                    if (!TextUtils.isEmpty(cacheJson)) {
                        JSONObject temp = new JSONObject(cacheJson);
                        if ((temp != null) && isTheSameWifiProfile(wifiList.getJSONObject(0),temp)) {
                            BBLog.e(TAG, "WifiProfileProcessService [switchWifiProfileByOrder]:  wifi[" + wifiList.getJSONObject(0).getString(ParameterName.ssid) + "]connected，profile no change");
                            if (WebsocketStatus.CONNECT_SUCCESS == WsManager.getInstance().getStatus() || WirelessUtil.ping()) {
                                removeCacheWifiFromList();
                                return;
                            }else {
                                delayWifiConnect(wifiList.getJSONObject(0),ssid);
                                isConnected = false;
                            }
                        }else {
                            printCacheWplData();
                            BBLog.e(TAG, "WifiProfileProcessService [switchWifiProfileByOrder]:  wifi[" + wifiList.getJSONObject(0).getString(ParameterName.ssid) + "]connected，profile change，need to reconnected");
                        }
                    }
                }
//                if (!checkIsWifiInRange(wifiList.getJSONObject(0))) return;
                if ((checkIsWifiInRange(wifiList.getJSONObject(0)) &&  !wifiList.getJSONObject(0).has(ParameterName.wifi_lost_time))  || isHiddentWifi(wifiList.getJSONObject(0))) {
                    BBLog.e(TAG, "WifiProfileProcessService [switchWifiProfileByOrder]: try to connect wifi [" + wifiList.getJSONObject(0).getString(ParameterName.ssid) + "]");
                    networkId = handlerWifiConnectBySecurityType(networkId, wifiList.getJSONObject(0));
                    //检查连接状态
//                checkConnectStatus(wifiList.getJSONObject(0));
                    Thread.sleep(WAIT_TIME);
                    if (isConnected) {
                        if (networkId != -1) {
                            if (checkWifiStatus(wifiList.getJSONObject(0))) {
                                removeCacheWifiFromList();
                                BBLog.e(TAG, "WifiProfileProcessService [switchWifiProfileByOrder]: wifi [" + wifiList.getJSONObject(0).getString(ParameterName.ssid) + "] connected, Ping succ");
                            } else {
                                BBLog.e(TAG, "switchWifiProfileByOrder: wifi [" + wifiList.getJSONObject(0).getString(ParameterName.ssid) + "] connected, Ping failed");
                                delayWifiConnect(wifiList.getJSONObject(0), wifiList.getJSONObject(0).getString(ParameterName.ssid));
                                isConnected = false;
                            }
                        } else {
                            BBLog.e(TAG, "WifiProfileProcessService [switchWifiProfileByOrder]: wifi [" + wifiList.getJSONObject(0).getString(ParameterName.ssid) + "]connected but Ping failed，try next wifi");
                            delayWifiConnect(wifiList.getJSONObject(0), wifiList.getJSONObject(0).getString(ParameterName.ssid));
                            isConnected = false;
                            networkId = -1;
                        }
                    } else {
                        BBLog.e(TAG, "WifiProfileProcessService [switchWifiProfileByOrder]: wifi [" + wifiList.getJSONObject(0).getString(ParameterName.ssid) + "] connect failed，try next wifi");
                        delayWifiConnect(wifiList.getJSONObject(0), wifiList.getJSONObject(0).getString(ParameterName.ssid));
                        networkId = -1;
                    }
                }else {
                    networkId = -1;
                }
            } else { //有多个wifi profile
                BBLog.i(TAG, "WifiProfileProcessService [switchWifiProfileByOrder]: size > 1");
                try {
                    for (int i = 0; i < wifiList.length(); i++) {
                        networkId = -1;
                        JSONObject wifiProfile = wifiList.getJSONObject(i);

                        if (isCurrentWifiConnected(wifiProfile)) {
//                            String cacheJson = SharedPreferencesUtils.getSharePreferencesValue(SPKeys.LAST_CONNECTED_WIFL_PROFILE,"");
							String cacheJson = readConnectedWifiProfile();
                            if (!TextUtils.isEmpty(cacheJson)) {
                                JSONObject temp = new JSONObject(cacheJson);
                                if ((temp != null) && isTheSameWifiProfile(wifiProfile,temp)) {
                                    BBLog.e(TAG, "WifiProfileProcessService [switchWifiProfileByOrder]:  wifi[" + wifiProfile.getString(ParameterName.ssid) + "]connected，profile no change");
                                    if (WebsocketStatus.CONNECT_SUCCESS == WsManager.getInstance().getStatus() || WirelessUtil.ping()) {
                                        removeCacheWifiFromList();
                                        return;
                                    }else {
                                        delayWifiConnect(wifiProfile,wifiProfile.getString(ParameterName.ssid));
                                        isConnected = false;
                                    }
                                }else{
                                    printCacheWplData();
                                    BBLog.i(TAG, "WifiProfileProcessService [switchWifiProfileByOrder]: wifi[" + wifiProfile.getString(ParameterName.ssid) + "]connected，profile change，need to reconnected");
                                }
                            }
//                            BBLog.i(TAG, "WifiProfileProcessService [switchWifiProfileByOrder]: wifi[" + wifiProfile.getString(ParameterName.ssid) + "]已连接，profile 信息已变更，，需要重新配置、连接");
                        }

                        if (!isHiddentWifi(wifiProfile) && !checkIsWifiInRange(wifiProfile)) continue;
                        if (wifiProfile.has(ParameterName.wifi_lost_time)) continue;

                        BBLog.i(TAG, "WifiProfileProcessService [switchWifiProfileByOrder]: try to connect wifi["+wifiProfile.getString(ParameterName.ssid)+"]");
                        networkId = handlerWifiConnectBySecurityType(networkId, wifiProfile);
                        Thread.sleep(WAIT_TIME);
                        //等待结果
                        if (isConnected) {
                            if (networkId != -1) {
                                if (checkWifiStatus(wifiProfile)) {
                                    removeCacheWifiFromList();
                                    BBLog.e(TAG, "WifiProfileProcessService [switchWifiProfileByOrder]: wifi ["+wifiProfile.getString(ParameterName.ssid)+"] connected, Ping succ");
                                }else {
                                    BBLog.e(TAG, "switchWifiProfileByOrder: wifi ["+wifiProfile.getString(ParameterName.ssid)+"] connected, Ping failed" );
                                    delayWifiConnect(wifiProfile,wifiProfile.getString(ParameterName.ssid));
                                    isConnected = false;
                                    continue;
                                }
                                break;
                            }else {
                                BBLog.e(TAG, "WifiProfileProcessService [switchWifiProfileByOrder]: wifi ["+wifiProfile.getString(ParameterName.ssid)+"]connected but Ping failed，try next wifi");
                                delayWifiConnect(wifiProfile,wifiProfile.getString(ParameterName.ssid));
                                isConnected = false;
                                networkId = -1;
                                continue;
                            }
                        }else {
                            BBLog.e(TAG, "WifiProfileProcessService [switchWifiProfileByOrder]: wifi ["+wifiProfile.getString(ParameterName.ssid)+"] connect failed，try next wifi");
                            delayWifiConnect(wifiProfile,wifiProfile.getString(ParameterName.ssid));
                            networkId = -1;
                            continue;
                        }
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }

            Thread.sleep(WAIT_TIME);
            //如果所有wifi都没有连接成功，则尝试使用默认wifi进行连接
            if (networkId == -1 || !isConnected) {
                JSONObject defaultWifiProfile = getDefaultWifi(currentCacheWifiProfile);
                // FIXME: 2019/11/12 default wifi 如果被延期了，不执行
                if (defaultWifiProfile!=null && !defaultWifiProfile.has(ParameterName.wifi_lost_time)) {
                    BBLog.i(TAG, "WifiProfileProcessService [switchWifiProfileByOrder]: try to connect default wifi [" + defaultWifiProfile.getString(ParameterName.ssid) + "]");
                    if (isCurrentWifiConnected(defaultWifiProfile)) {
//                        String cacheJson = SharedPreferencesUtils.getSharePreferencesValue(SPKeys.LAST_CONNECTED_WIFL_PROFILE,"");
						String cacheJson = readConnectedWifiProfile();
//                        BBLog.e(TAG, "switchWifiProfileByOrder: cache = "+cacheJson );
                        if (!TextUtils.isEmpty(cacheJson)) {
                            JSONObject temp = new JSONObject(cacheJson);
                            if ((temp != null) && isTheSameWifiProfile(defaultWifiProfile, temp)) {
                                BBLog.e(TAG, "WifiProfileProcessService [switchWifiProfileByOrder]:  wifi[" + defaultWifiProfile.getString(ParameterName.ssid) + "]connected，profile no change");
                                if (WebsocketStatus.CONNECT_SUCCESS == WsManager.getInstance().getStatus() || WirelessUtil.ping()) {
                                    removeCacheWifiFromList();
                                    return;
                                }
                            } else {
                                printCacheWplData();
                                BBLog.e(TAG, "WifiProfileProcessService [switchWifiProfileByOrder]:  wifi[" + defaultWifiProfile.getString(ParameterName.ssid) + "]connected，profile change, need to reconnect");
                            }
                        }
//                        BBLog.e(TAG, "WifiProfileProcessService [switchWifiProfileByOrder]:  wifi[" + defaultWifiProfile.getString(ParameterName.ssid) + "]已连接，profile 信息已变更，需要重新配置、连接");
                    }
                    if (checkIsWifiInRange(defaultWifiProfile) || isHiddentWifi(defaultWifiProfile)) {
                        networkId = handlerWifiConnectBySecurityType(networkId, defaultWifiProfile);
                        Thread.sleep(WAIT_TIME);
                        //等待wifi 认证结果
                        if (isConnected && (WebsocketStatus.CONNECT_SUCCESS == WsManager.getInstance().getStatus() || WirelessUtil.ping())) {
//                            checkWifiStatus(defaultWifiProfile);
                            BBLog.e(TAG, "WifiProfileProcessService [checkWifiStatus]: wifi ["+defaultWifiProfile.getString(ParameterName.ssid)+"] connected, ping success,");
                            saveConnectedWifiProfile(defaultWifiProfile);
                            // 从终端剔除掉不在wifi profile list 中的wifi
                            delNonListWifiFromWifiConfig();
                            removeCacheWifiFromList();
                        }else {
                            BBLog.e(TAG, "WifiProfileProcessService [switchWifiProfileByOrder]: wifi ["+defaultWifiProfile.getString(ParameterName.ssid)+"] ping failed or connect failed，try to search cache wifi");
                            reconnectByLastWifi(defaultWifiProfile);
                        }
                    }else {
                        reconnectByLastWifi(defaultWifiProfile);
                    }
                }else {
                    BBLog.e(TAG, "WifiProfileProcessService default wifi no exist or delayed");
                    reconnectByLastWifi(null);
                }
            }

            //清空数据
            resetData();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 判断是否是隐藏wifi
     * @param wifiProfile
     * @return
     */
    private static  boolean isHiddentWifi(JSONObject wifiProfile) {
        if(wifiProfile == null) return false;
        BBLog.e(TAG, "WifiProfileProcessService isHiddentWifi =  "+(wifiProfile.has(ParameterName.connectHidden) && "1".equals(wifiProfile.optString(ParameterName.connectHidden))));
        return wifiProfile.has(ParameterName.connectHidden) && "1".equals(wifiProfile.optString(ParameterName.connectHidden));
    }

    /**
     * 根据 加密类型 选择对应的加密方式进行连接
     *
     * @param networkId
     * @param wifiProfile
     * @return
     * @throws JSONException
     */
    public static int handlerWifiConnectBySecurityType(int networkId, JSONObject wifiProfile) throws JSONException {
        BBLog.i(TAG, "WifiProfileProcessService [handlerWifiConnectBySecurityType]: networkId = " + networkId);// + ", wifiProfile = " + wifiProfile);
        if (wifiProfile == null) return -1;
        //先清除掉当前连接的wifi信息，7md上会出现连接新wifi失败时，自动恢复连接到上一次wifi
        if (WiFiProfileManager.getInstance(mContext).isWifiConnected()) {
            WiFiProfileManager.getInstance(mContext).disconnectCurrentWifi();
        }
        //根据 serurityType创建wifi 并保存
        networkId = saveBysecurityType(networkId, wifiProfile);
//        boolean result= WiFiProfileManager.getInstance(mContext).refreshWifiConfig(WiFiProfileManager.getInstance(mContext).getConfigFromConfiguredNetworksBySsid(wifiProfile.getString(ParameterName.ssid)));
        WiFiProfileManager.getInstance(mContext).enableNetwork(networkId);
        return networkId;
    }

    private static int saveBysecurityType(int networkId, JSONObject wifiProfile) throws JSONException {
        if (WifiSecurityTypeEnum.NONE.getValue().equals(wifiProfile.getString(ParameterName.securityType))) {
            networkId = WiFiProfileManager.getInstance(mContext).setOpenNetwork(wifiProfile.getString(ParameterName.ssid), "1".equals(wifiProfile.getString(ParameterName.connectHidden)));
            if (networkId != -1) {
                handlerProxyByProxyType(wifiProfile, networkId);
            }
        } else if (WifiSecurityTypeEnum.WEP.getValue().equals(wifiProfile.getString(ParameterName.securityType))) {
            networkId = WiFiProfileManager.getInstance(mContext).setWEPNetwork(wifiProfile.getString(ParameterName.ssid), wifiProfile.getString(ParameterName.pwd), "1".equals(wifiProfile.getString(ParameterName.connectHidden)));
            if (networkId != -1) {
                handlerProxyByProxyType(wifiProfile, networkId);
            }
        } else if (WifiSecurityTypeEnum.WPA.getValue().equals(wifiProfile.getString(ParameterName.securityType)) || WifiSecurityTypeEnum.WPA2_PSK.getValue().equals(wifiProfile.getString(ParameterName.securityType))) {
            networkId = WiFiProfileManager.getInstance(mContext).setWPA2Network(wifiProfile.getString(ParameterName.ssid), wifiProfile.getString(ParameterName.pwd), "1".equals(wifiProfile.getString(ParameterName.connectHidden)));
            if (networkId != -1) {
                handlerProxyByProxyType(wifiProfile, networkId);
            }
        }
        return networkId;
    }

    /**
     * 根据 代理类型 设置对应代理方式
     *
     * @param wifiProfile
     * @param networkId
     * @throws JSONException
     */
    private static void handlerProxyByProxyType(JSONObject wifiProfile, int networkId) throws JSONException {
        BBLog.d(TAG, "WifiProfileProcessService [handlerProxyByProxyType]: networkId = " + networkId);// + ", wifiProfile = " + wifiProfile);
        if (networkId == -1) return;
        if (wifiProfile == null) return;
        WifiConfiguration proxyConfig = WiFiProfileManager.getInstance(mContext).getConfigFromConfiguredNetworksBySsid(wifiProfile.getString(ParameterName.ssid));
        if (wifiProfile.has(ParameterName.proxyType) && WifiProxyTypeEnum.NONE.getValue().equals(wifiProfile.getString(ParameterName.proxyType))) {
//            BBLog.d(TAG, "WifiProfileProcessService [handlerProxyByProxyType]: proxy NONE 需要清除当前wifi proxy信息");
//            if (WifiProxyUtil.isWifiProxy(mContext)) {
                BBLog.d(TAG, "WifiProfileProcessService [handlerProxyByProxyType]: proxy NONE clear current wifi proxy");
                WiFiProfileManager.getInstance(mContext).setWifiProxyByHostAndPort(proxyConfig,null, 0, null,true);
                WiFiProfileManager.getInstance(mContext).updateNetwork(proxyConfig);
//            }
        } else if (wifiProfile.has(ParameterName.proxyType) && WifiProxyTypeEnum.MANUAL.getValue().equals(wifiProfile.getString(ParameterName.proxyType))) {
            if (proxyConfig != null) {
                String host = wifiProfile.getString(ParameterName.proxyIp);
                String port = wifiProfile.getString(ParameterName.proxyPort);
                String byPass = wifiProfile.getString(ParameterName.byPassProxyFor);
                BBLog.d(TAG, "WifiProfileProcessService [handlerProxyByProxyType]: proxy MAUNAL: hostIp: " + host + ", port: " + port + ", byPass: " + byPass);
                if (!TextUtils.isEmpty(host) && !TextUtils.isEmpty(port)) {
                    BBLog.d(TAG, "WifiProfileProcessService [handlerProxyByProxyType]: MAUNAL set proxy");
                    WiFiProfileManager.getInstance(mContext).setWifiProxyByHostAndPort(proxyConfig,
                            host,
                            Integer.valueOf(port),
                            TextUtils.isEmpty(byPass) ? null : new Gson().fromJson(wifiProfile.getString(ParameterName.byPassProxyFor), new TypeToken<List<String>>() {
                            }.getType()),false);
                    WiFiProfileManager.getInstance(mContext).updateNetwork(proxyConfig);
                }
            }
        } else if (wifiProfile.has(ParameterName.proxyType) && WifiProxyTypeEnum.PAC.getValue().equals(wifiProfile.getString(ParameterName.proxyType))) {
            if (proxyConfig != null) {
                String pacUrl = wifiProfile.getString(ParameterName.pacUrl);
                BBLog.d(TAG, "WifiProfileProcessService [handlerProxyByProxyType]: proxy pacUrl: " + pacUrl);
                if (!TextUtils.isEmpty(pacUrl)) {
                    BBLog.d(TAG, "WifiProfileProcessService [handlerProxyByProxyType]: PAC set proxy");
                    WiFiProfileManager.getInstance(mContext).setWifiProxyByPAC(proxyConfig, pacUrl);
                    WiFiProfileManager.getInstance(mContext).updateNetwork(proxyConfig);
                }
            }
        }
    }

    /**
     * 获取默认wifi
     *
     * @param wifiList
     * @return
     */
    public  JSONObject getDefaultWifi(JSONArray wifiList) {
//        BBLog.i(TAG, "WifiProfileProcessService [getDefaultWifi]: wifiList=" + wifiList);
        if (wifiList == null || wifiList.length() == 0) return null;
        JSONObject defaultWifi = null;
        try {
            for (int i = 0; i < wifiList.length(); i++) {
                JSONObject wifiProfile = wifiList.getJSONObject(i);
                if (wifiProfile != null && "1".equals(wifiProfile.getString(ParameterName.isDefault))) {
                    defaultWifi = wifiProfile;
                    break;
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return defaultWifi;
    }

    /**
     * 获取在当前时间内有效的wifi profile列表
     *
     * @param wifiList
     * @return
     */
    public  JSONArray getWifiProfileBetweenCurrentDate(JSONArray wifiList) {
        if (wifiList == null || wifiList.length() == 0) return null;
        JSONArray needExecuteTask = new JSONArray();
        JSONArray expireList = new JSONArray();//已到期的profile
        long nowTime = System.currentTimeMillis();
        SimpleDateFormat sdf = new SimpleDateFormat(Constants.dateFormat);
        BBLog.e(TAG, "WifiProfileProcessService: system time: "+ sdf.format(new Date()));
        long begTime;
        long endTime;
        try {
            for (int i = 0; i < wifiList.length(); i++) {
                JSONObject wifiProfileJsonObj = (JSONObject) wifiList.get(i);
                String beginDateStr = wifiProfileJsonObj.getString(ParameterName.beginDate);
                String endDateStr = wifiProfileJsonObj.getString(ParameterName.endDate);

                if (DateTimeUtils.isNumeric(beginDateStr) && DateTimeUtils.isNumeric(endDateStr)) {
                    begTime = new Long(beginDateStr).longValue();
                    endTime = new Long(endDateStr).longValue();
                } else {
                    begTime = sdf.parse(beginDateStr).getTime();
                    endTime = sdf.parse(endDateStr).getTime();
                }

                if (begTime > nowTime) {
                    //未到时 略过
                } else if (begTime < nowTime && nowTime < endTime) {
                    //判断该wifi非default wifi 并且上次有连接异常情况（连接不上/ping不通，wifi_lost_time 属性存在），则延迟24小时/重启后再进入execute队列
//                    if ("0".equals(wifiProfileJsonObj.getString(ParameterName.isDefault)) && wifiProfileJsonObj.has(ParameterName.wifi_lost_time)) {
                    if (wifiProfileJsonObj.has(ParameterName.wifi_lost_time)) { // FIXME: 2019/11/12 default 连接不上时，也加入到delay队列
                        if ((System.currentTimeMillis() - wifiProfileJsonObj.getLong(ParameterName.wifi_lost_time)) > WIFI_DELAY_EXECUTE_TIME ) {
                            wifiProfileJsonObj.remove(ParameterName.wifi_lost_time);
                            needExecuteTask.put(wifiProfileJsonObj);
                            BBLog.i(TAG, "WifiProfileProcessService wifi ssid = [" + wifiProfileJsonObj.getString(ParameterName.ssid) + "]re-put in QUEUE to exec。");
                        }else {
                            BBLog.i(TAG, "WifiProfileProcessService wifi ssid = [" + wifiProfileJsonObj.getString(ParameterName.ssid) + "]delayed for 24h to exec。");
                        }
                    }else {
                        BBLog.i(TAG, "WifiProfileProcessService wifi profile matched current time, to exec，ssid = [" + wifiProfileJsonObj.getString(ParameterName.ssid) + "]");
                        needExecuteTask.put(wifiProfileJsonObj);
                    }
                } else if (endTime < nowTime) {
                    BBLog.i(TAG, "WifiProfileProcessService wifi profile-["+wifiProfileJsonObj.getString(ParameterName.ssid)+"] expired");
                    expireList.put(wifiProfileJsonObj);
                }
            }
            //update: 根据Joe需求，已过期的wifi，需要从 list中移除，
            removeExpireWifi(expireList);

        } catch (Exception e) {
            e.printStackTrace();
        }
        return needExecuteTask;
    }

    /**
     * 对 profile 进行排序
     * order值越小，优先级越高
     * @param wifiList
     * @return
     */
    public  JSONArray sortWifiProfileByOrder(JSONArray wifiList) {
        if (wifiList == null || wifiList.length() == 0) return wifiList;
//        if (wifiList.length() == 1) return wifiList;
        //排序后的新数组
        JSONArray sortedJsonArray = new JSONArray();
        //待排序数组
        List<JSONObject> jsonValues = new ArrayList<JSONObject>();
        try {
            for (int i = 0; i < wifiList.length(); i++) {
                //保存default wifi
                if ("1".equals(wifiList.getJSONObject(i).getString(ParameterName.isDefault))) {
                    continue;
                }
                jsonValues.add(wifiList.getJSONObject(i));
            }
            if (jsonValues.size()>1) {
                Collections.sort(jsonValues, new Comparator<JSONObject>() {
                    @Override
                    public int compare(JSONObject a, JSONObject b) {
                        int orderA = 0;
                        int orderB = 0;
                        try {
                            orderA = a.getInt(ParameterName.order);
                            orderB = b.getInt(ParameterName.order);
                        } catch (JSONException e) {
                            e.printStackTrace();
                        }
                        return orderA - orderB;
                    }
                });
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        for (int i = 0; i < jsonValues.size(); i++) {
            sortedJsonArray.put(jsonValues.get(i));
        }
        return sortedJsonArray;
    }

    /**
     * 移除过期的wifi
     * @param expireList
     */
    private void removeExpireWifi( JSONArray expireList) {
        if (expireList == null || expireList.length() == 0) return;
        if (currentCacheWifiProfile == null || currentCacheWifiProfile.length() == 0 )  return;
        int size = currentCacheWifiProfile.length();
//        BBLog.e(TAG, "WifiProfileProcessService removeExpireWifi: expireList = " + expireList );
//        BBLog.e(TAG, "WifiProfileProcessService removeExpireWifi: orglist before = " + currentCacheWifiProfile );
        try {
            for (int i=0;i<expireList.length();i++) {
                for (int j=size -1;j>=0;j--) {
                    if (expireList.getJSONObject(i).toString().equals(currentCacheWifiProfile.getJSONObject(j).toString())) {
                        BBLog.e(TAG, "WifiProfileProcessService removeExpireWifi ssid : "+expireList.getJSONObject(i).getString(ParameterName.ssid) );
                        currentCacheWifiProfile.remove(j);
                       continue;
                    }
                }
            }
//            BBLog.e(TAG, "WifiProfileProcessService removeExpireWifi: orglist after = " + currentCacheWifiProfile );
            WebSocketServiceListManager.updateWifiProfileList(currentCacheWifiProfile);
            delNonListWifiFromWifiConfig();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private  void checkConnectStatus(JSONObject jsonObject) {
//        BBLog.e(TAG, "WifiProfileProcessService checkConnectStatus: wifi = "+ jsonObject );
        try {
            Thread.sleep(WAIT_TIME);
            if (isConnected && (WebsocketStatus.CONNECT_SUCCESS == WsManager.getInstance().getStatus() || WirelessUtil.ping())) {
//                checkWifiStatus(jsonObject);
                BBLog.e(TAG, "WifiProfileProcessService [checkWifiStatus]: wifi ["+jsonObject.getString(ParameterName.ssid)+"] connected ping success,");
                saveConnectedWifiProfile(jsonObject);
                // 从终端剔除掉不在wifi profile list 中的wifi
                delNonListWifiFromWifiConfig();
                removeCacheWifiFromList();
            }else {
                //如果待执行的wifi 出现连接不上/或连接上但ping不通情况，则将给该wifi打上delay connect 的标志，待24小时/重启后才允许进入execute 队列
                delayWifiConnect(jsonObject,"<unknown ssid>");
                isConnected = false;
                networkId = -1;
                // FIXME: 2019/11/7 当 default wifi连接不上时，使用上一次连接的wifi进行补救，避免终端进入离线状态
//                reconnectByLastWifi(jsonObject);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 当 default wifi连接不上时，使用上一次连接的wifi进行补救，避免终端进入离线状态
     * @param jsonObject
     * @throws JSONException
     */
    private void reconnectByLastWifi(JSONObject jsonObject) {
        BBLog.e(TAG, "WifiProfileProcessService reconnectByLastWifi: " );
        try {
//            if(jsonObject == null) {
//                BBLog.e(TAG, "WifiProfileProcessService reconnectByLastWifi wifi 不存在 " );
//                return;
//            }
//            if ("1".equals(jsonObject.optString(ParameterName.isDefault))) {
//                String cacheJson = SharedPreferencesUtils.getSharePreferencesValue(SPKeys.LAST_CONNECTED_WIFL_PROFILE, "");
			String cacheJson = readConnectedWifiProfile();
            printCacheWplData();
            if (!TextUtils.isEmpty(cacheJson)) {
                    JSONObject obj = new JSONObject(cacheJson);
                    if (obj == null) return;
                    if (isCurrentWifiConnected(obj))return;
                    BBLog.e(TAG, "WifiProfileProcessService reconnectByLastWifi: has cache wifi，try to use it" );
                    if (obj.has(ParameterName.wifi_lost_time))
                        obj.remove(ParameterName.wifi_lost_time);//移除delay标识
                    //非default wifi ，endTime 增加5 min的时延
    //                if (!"1".equals(obj.getString(ParameterName.isDefault))) {
                        DateFormat df = new SimpleDateFormat(Constants.dateFormat);
                        long time = new Date().getTime() + WIFI_DELAY_EXECUTE_TIME;
                        obj.put(ParameterName.endDate, df.format(new Date(time)));
                        obj.put(ParameterName.wifi_flag_cache,"1");//打上标志
    //                }

                    if (currentCacheWifiProfile.length() == 1) {
                        if ("1".equals(obj.getString(ParameterName.isDefault))) {//上次保存的wifi 即是default wifi.
                            currentCacheWifiProfile.remove(0);
                            currentCacheWifiProfile.put(obj);
                        }else {
                            currentCacheWifiProfile.put(obj);
                         }
                    }else {
                        int size = currentCacheWifiProfile.length();
                        for (int i = size-1; i>0; i--) {
                            //先移除列表中存在的cache wifi
                            if (/*obj.getString(ParameterName.ssid).equals(currentCacheWifiProfile.getJSONObject(i).getString(ParameterName.ssid))
                        && */currentCacheWifiProfile.getJSONObject(i).has(ParameterName.wifi_flag_cache)
                                    && "1".equals(currentCacheWifiProfile.getJSONObject(i).getString(ParameterName.wifi_flag_cache))) {
                                currentCacheWifiProfile.remove(i);
                                break;
                            }
                        }
                        currentCacheWifiProfile.put(obj);
                    }
                    //连接wifi
                    handlerWifiConnectBySecurityType(-1, obj);
                }else {
                    BBLog.e(TAG, "WifiProfileProcessService reconnectByLastWifi: has no cache wifi" );
                }
//            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 移除 执行列表中的cache wifi
     * @throws JSONException
     */
    private void removeCacheWifiFromList() {
        try {
            int size = currentCacheWifiProfile.length();
            String curSSID =  WiFiProfileManager.getInstance(mContext).getConnectionInfo().getSSID().replace("\"","");
            for (int i = size-1; i>0; i--) {
                if (!curSSID.equals(currentCacheWifiProfile.getJSONObject(i).optString(ParameterName.SSID))
                        && currentCacheWifiProfile.getJSONObject(i).has(ParameterName.wifi_flag_cache)
                        && "1".equals(currentCacheWifiProfile.getJSONObject(i).getString(ParameterName.wifi_flag_cache))) {
                    currentCacheWifiProfile.remove(i);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 对指定wifi 打上delay 标志
     * @param jsonObject
     * @throws JSONException
     */
    private void delayWifiConnect(JSONObject jsonObject,String ssidName) throws JSONException {
        BBLog.e(TAG, "WifiProfileProcessService delayWifiConnect: ssidName= "+ ssidName + ",ssid = "+ssid);
        try {
            if (jsonObject.has(ParameterName.ssid) && !TextUtils.isEmpty(ssid) && ssidName.equals(ssid.replace("\"","")) || "<unknown ssid>".equals(ssid.replace("\"",""))) {
                for (int i=0;i<currentCacheWifiProfile.length();i++) {
                    //default wifi 不执行delay处理
                    // FIXME: 2019/11/12 取消default wifi  进入delay队列
    //                if ("1".equals(currentCacheWifiProfile.getJSONObject(i).getString(ParameterName.isDefault))) continue;

                    if (currentCacheWifiProfile.getJSONObject(i).getString(ParameterName.ssid).equals(jsonObject.getString(ParameterName.ssid))) {
                        currentCacheWifiProfile.getJSONObject(i).put(ParameterName.wifi_lost_time, System.currentTimeMillis());
                        break;
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
//        BBLog.e(TAG, "WifiProfileProcessService delayWifiConnect: = "+currentCacheWifiProfile  );
    }


    private  boolean checkWifiStatus(JSONObject wifiProfile) throws JSONException {
        if (wifiProfile == null)return false;
        if (networkId != -1 && (WebsocketStatus.CONNECT_SUCCESS == WsManager.getInstance().getStatus() || WirelessUtil.ping())) {
            BBLog.e(TAG, "WifiProfileProcessService [checkWifiStatus]: wifi ["+wifiProfile.getString(ParameterName.ssid)+"] connected ping success,");
            saveConnectedWifiProfile(wifiProfile);
            // 从终端剔除掉不在wifi profile list 中的wifi
            delNonListWifiFromWifiConfig();
            return true;
        }/*else {
            //当前尝试连接的为default wifi，且未连接上，则尝试连接上一次的wifi
//            if ("1".equals(wifiProfile.getString(ParameterName.isDefault)))
                reconnectByLastWifi(wifiProfile);
        }*/
        return false;
    }

    /**
     * 保存wifi到 wifiConfigtion中
     *  Update: 2019/12/17 优化ssid保存逻辑
     */
    public static void saveAllWifiProfileToWifiConfig(JSONArray wifiList) {
        try {
            BBLog.e(TAG, "WifiProfileProcessService [saveAllWifiProfileToWifiConfig]: wifi begin ----->  ");
            //获取本地已经连接过的wifi信息
            List<WifiConfiguration> existingConfigs = WiFiProfileManager.getInstance(ContextUtil.getInstance()).getConfiguredNetworks();
            if (null!=existingConfigs) {
                for (int i = existingConfigs.size()-1; i>0; i--) {
                    if (!existingConfigs.get(i).SSID.equals(WiFiProfileManager.getInstance(mContext).getConnectionInfo().getSSID().replace("\"",""))) {
                        existingConfigs.remove(i);
                    }
                }
                if (wifiList!=null && wifiList.length()>0) {
                    for (int i = 0; i < wifiList.length(); i++) {
                        saveBysecurityType(-1, wifiList.getJSONObject(i));
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        BBLog.e(TAG, "WifiProfileProcessService [saveAllWifiProfileToWifiConfig]: wifi end <----- ");
    }


    private  void saveConnectedWifiProfile(JSONObject wifiProfile) {
		try {
			if (wifiProfile != null) {
/*
				byte[] encode = DESUtils.encryptCbc(wifiProfile.toString().getBytes(),
                        SecurityOperate.getInstance().getDeviceDK(ContextUtil.getInstance()).getBytes(),
                        SecurityOperate.getInstance().getDeviceDIV(ContextUtil.getInstance()).getBytes(),
						DESUtils.Padding.PKCS5_PADDING);
				String wplData = StringUtils.Hex2String(encode);
*/
                String wplData = AESUtil.encryptToHex(wifiProfile.toString());
				BBLog.e(BBLog.TAG, "WifiProfileProcessService [saveConnectedWPLData]: wplData = " + wplData);
				if (!TextUtils.isEmpty(wplData)) {
					//移除旧数据
					SharedPreferencesUtils.clearByKey(SPKeys.LAST_CONNECTED_WIFL_PROFILE);
					SharedPreferencesUtils.setSharePreferencesValue(SPKeys.LAST_CONNECTED_ENCRYPT_WIFL_PROFILE,wplData);
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
    }

    private String readConnectedWifiProfile() {
        try {
            String cacheWplData = SharedPreferencesUtils.getSharePreferencesValue(
                    SPKeys.LAST_CONNECTED_ENCRYPT_WIFL_PROFILE, "");
            if (!TextUtils.isEmpty(cacheWplData)) {
                String original = null;
                // step 1: 先用 aes 尝试解码
                try {
                    original = AESUtil.decryptHexToString(cacheWplData);
                    if (!TextUtils.isEmpty(original)) {
                        return original;
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
/*
                // step 2: aes 解码失败，再用des解码
                try {
                    byte[] decode = DESUtils.decryptCbc(StringUtils.String2Hex(cacheWplData),
                            SecurityOperate.getInstance().getDeviceDK(ContextUtil.getInstance()).getBytes(),
                            SecurityOperate.getInstance().getDeviceDIV(ContextUtil.getInstance()).getBytes(),
                            DESUtils.Padding.PKCS5_PADDING);
                    original = new String(decode);
                } catch (Exception e) {
                    e.printStackTrace();
                }

                //step 3: 若des解码成功，将数据重新aes加密并保存
                try {
                    String wplData = AESUtil.encryptToHex(original);
                    //step 4: 保存数据
                    if (!TextUtils.isEmpty(wplData)) {
                        SharedPreferencesUtils.clearByKey(SPKeys.LAST_CONNECTED_WIFL_PROFILE);
                        SharedPreferencesUtils.setSharePreferencesValue(SPKeys.LAST_CONNECTED_ENCRYPT_WIFL_PROFILE, wplData);
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
                return original;
*/
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 检查 wifi 是否在 热点范围内
     * @param wifiProfile
     * @return
     */
    public static boolean checkIsWifiInRange(JSONObject wifiProfile) {
        if (wifiProfile == null) return false;
        try {
            List<String> rangeWifis = getWifiInRange();
            BBLog.i(TAG, "WifiProfileProcessService [checkIsWifiInRange]: scan wifi ap，scanResults = "+rangeWifis);
            if (rangeWifis!=null && rangeWifis.contains(wifiProfile.getString(ParameterName.ssid)))
                return true;
                BBLog.i(TAG, "WifiProfileProcessService [checkIsWifiInRange]: scanned as no wifi ["+wifiProfile.getString(ParameterName.ssid)+"]");
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

    /**
     * 获取周边wifi
     * @return
     */
    public  static List<String> getWifiInRange() {
        List<ScanResult> scanResults = WiFiProfileManager.getInstance(mContext).getScanResults();
        if (scanResults == null || scanResults.size() == 0) {
            return null;
        }
        List<String> ssids = new ArrayList<>();
        for (ScanResult scanResult : scanResults) {
            if (!TextUtils.isEmpty(scanResult.SSID) && !ssids.contains(scanResult.SSID)) {
                ssids.add(scanResult.SSID);
            }
        }
        return ssids;
    }

    private  boolean isCurrentWifiConnected(JSONObject wifiProfile) {
        if (wifiProfile == null) return false;
        try {
            BBLog.e(TAG, "WifiProfileProcessService [isCurrentWifiConnected]: current connected wifi ssid = "+ WiFiProfileManager.getInstance(mContext).getConnectionInfo().getSSID() + ", target wifi ssid = "+ wifiProfile.getString(ParameterName.ssid));
            if (/*!"1".equals(wifiProfile.getString(ParameterName.isDefault))
                    &&*/ !TextUtils.isEmpty(WiFiProfileManager.getInstance(mContext).getConnectionInfo().getSSID())
                    && WiFiProfileManager.getInstance(mContext).getConnectionInfo().getSSID().replace("\"","").equals(wifiProfile.getString(ParameterName.ssid))
                    && WiFiProfileManager.getInstance(mContext).isWifiConnected()) {
                BBLog.i(TAG, "WifiProfileProcessService [isCurrentWifiConnected]: target wifi["+wifiProfile.getString(ParameterName.ssid)+"] connected");
                return true;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

    /**
     * 比较wifi profile 是否一致
     * @param cur 当前profile
     * @param tar 目标profile
     * @return
     */
    private  boolean isTheSameWifiProfile(JSONObject cur, JSONObject tar) {
        if (tar == null) return false;
//        BBLog.e(TAG, "isTheSameWifiProfile: cur= "+cur.toString() );
//        BBLog.e(TAG, "isTheSameWifiProfile: tar= "+tar.toString() );
        return cur.toString().equals(tar.toString());
    }
    
    public  void resetData() {
        //清空数据
        isConnected = false;
        ssid = "";
//        wifiConnectListener = null;
        //成功连上wifi后，取消相关监听
//        WiFiProfileManager.getInstance(mContext).setOnWifiConnectListener(null);
    }

    private void printCacheWplData() {
        String cacheWplData = SharedPreferencesUtils.getSharePreferencesValue(SPKeys.LAST_CONNECTED_ENCRYPT_WIFL_PROFILE, "");
        BBLog.e(BBLog.TAG, "WifiProfileProcessService [readConnectedWplData] cacheWplData :  " + cacheWplData);
    }
}
