package com.bbpos.wiseapp.tms.utils;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.SharedPreferences;
import android.os.Build;
import android.preference.PreferenceManager;
import android.text.TextUtils;

import com.bbpos.wiseapp.logger.BBLog;
import com.bbpos.wiseapp.service.common.UsualData;
import com.bbpos.wiseapp.utils.SharedPreferencesUtils;
import com.bbpos.wiseapp.websocket.WebSocketSender;
import com.bbpos.wiseapp.websocket.handler.RulebasedHandler;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Set;

import static com.bbpos.wiseapp.utils.FileUtils.getWifiConfigFilePath;
import static com.bbpos.wiseapp.utils.FileUtils.readFile;
import static com.bbpos.wiseapp.utils.FileUtils.writeWifiConfigToFile;

@SuppressLint({ "UseSparseArrays", "UseValueOf", "SimpleDateFormat", "NewApi"})
/**
 * 本地任务列表管理
 * 固化任务数据至shareprefrence中
 * */
public class WebSocketServiceListManager {
	/**任务执行时间map,用于发出timeup广播*/
	public static final SharedPreferences sp = PreferenceManager.getDefaultSharedPreferences(ContextUtil.getInstance());

	/**与本地任务列表进行对比、更新*/
	public static void updateServiceList(String requestId, String requestTime, JSONArray serviceList, String beginDate, String endDate, String status) {
		try {
			if (serviceList==null || serviceList.length()==0) {
				BBLog.e(BBLog.TAG, "serviceList is empty.updateServiceList failed");
				return;
			}

			String localServiceAppListStr = sp.getString(SPKeys.WEBSOCKET_SERVICE_LIST, "");
			if (!Helpers.isStrNoEmpty(localServiceAppListStr)) {
				for (int i = 0; i < serviceList.length(); i++) {
					JSONObject taskJsonObj = (JSONObject) serviceList.get(i);
					if ("06".equals(taskJsonObj.getString(ParameterName.taskType))) {
						continue;
					} else if ("05".equals(taskJsonObj.optString(ParameterName.taskType))
							&& "C03".equals(taskJsonObj.optString(ParameterName.command))
							&& "1970-01-01 00:00:00".equals(beginDate)
							&& "9999-12-31 23:59:59".equals(endDate)) {
						BBLog.w(BBLog.TAG, "遇到LogStreaming的Schedule 起始結束時間不明確: " + taskJsonObj.toString());
						long nowtime = System.currentTimeMillis();
						taskJsonObj.put(ParameterName.request_id, requestId);
                        taskJsonObj.put(ParameterName.request_time, requestTime);
						taskJsonObj.put(ParameterName.beginDate, Helpers.getTransDateStr(nowtime));
						taskJsonObj.put(ParameterName.endDate, Helpers.getTransDateStr(nowtime + 3*24*60*60*1000));
						taskJsonObj.put(ParameterName.stateDesc, status);
						BBLog.w(BBLog.TAG, "修改起始結束時間: " + taskJsonObj);
					} else {
					    taskJsonObj.put(ParameterName.request_id, requestId);
                        taskJsonObj.put(ParameterName.request_time, requestTime);
						taskJsonObj.put(ParameterName.beginDate, beginDate);
						taskJsonObj.put(ParameterName.endDate, endDate);
						taskJsonObj.put(ParameterName.stateDesc, status);
					}
				}
				BBLog.w(BBLog.TAG, "updateServiceList: " + serviceList.toString());
				sp.edit().putString(SPKeys.WEBSOCKET_SERVICE_LIST, serviceList.toString()).commit();
			} else {
				JSONObject jsonC05ServiceApp = null;
				HashMap<String, JSONObject> taskJsonMap = new HashMap<String, JSONObject>();
				for (int i = 0; i < serviceList.length(); i++) {
					JSONObject taskJsonObj = (JSONObject) serviceList.get(i);
					if ("05".equals(taskJsonObj.optString(ParameterName.taskType))
					 && "C03".equals(taskJsonObj.optString(ParameterName.command))
					 && "1970-01-01 00:00:00".equals(beginDate)
					 && "9999-12-31 23:59:59".equals(endDate)) {
						BBLog.w(BBLog.TAG, "遇到LogStreaming的Schedule 起始結束時間不明確: " + serviceList.toString());
						long nowtime = System.currentTimeMillis();
                        taskJsonObj.put(ParameterName.request_id, requestId);
                        taskJsonObj.put(ParameterName.request_time, requestTime);
						taskJsonObj.put(ParameterName.beginDate, Helpers.getTransDateStr(nowtime));
						taskJsonObj.put(ParameterName.endDate, Helpers.getTransDateStr(nowtime + 3*24*60*60*1000));
						taskJsonObj.put(ParameterName.stateDesc, status);
						BBLog.w(BBLog.TAG, "修改起始結束時間: " + taskJsonObj);
					} else {
                        taskJsonObj.put(ParameterName.request_id, requestId);
                        taskJsonObj.put(ParameterName.request_time, requestTime);
						taskJsonObj.put(ParameterName.beginDate, beginDate);
						taskJsonObj.put(ParameterName.endDate, endDate);
						taskJsonObj.put(ParameterName.stateDesc, status);
						if ("C05".equals(taskJsonObj.optString(ParameterName.command))) {
							jsonC05ServiceApp = taskJsonObj;
						}
					}
					taskJsonMap.put(taskJsonObj.getString(ParameterName.taskId), taskJsonObj);
				}
				JSONArray localServiceList = new JSONArray(localServiceAppListStr);
				List<Integer> toDeleteIndexs = new ArrayList<Integer>();
				for (int i = 0; i < localServiceList.length(); i++) {
					JSONObject serviceJsonObj = (JSONObject) localServiceList.get(i);
					BBLog.w(BBLog.TAG, "serviceJsonObj = " + serviceJsonObj);
					String taskId = serviceJsonObj.optString(ParameterName.taskId);
					if (taskId!=null && taskJsonMap.get(taskId) != null) {
						JSONObject serviceJson = taskJsonMap.get(taskId);
						updateServiceAppJsonObj(serviceJsonObj, serviceJson);
						taskJsonMap.remove(taskId);
					} else {
						//判断本地是否已经有存在未执行成功的C05，如果有，且再接收到新的，则覆盖原有的，保留一条
						String command = serviceJsonObj.getString(ParameterName.command);
						if (jsonC05ServiceApp != null && "C05".equals(command) && !RuleStatus.IMPLEMENTED.equals(serviceJsonObj.getString(ParameterName.stateDesc))) {
							updateServiceAppJsonObj(serviceJsonObj, jsonC05ServiceApp);
							taskJsonMap.remove(jsonC05ServiceApp.getString(ParameterName.taskId));
							BBLog.w(BBLog.TAG, "覆盖之前的C05 service, 结果localServiceList=" + localServiceList.toString());
						}
					}
				}

				//加入新任务
				Set<String> newTaskIdSet = taskJsonMap.keySet();
				Iterator<String> newTaskIdIt = newTaskIdSet.iterator();
				while (newTaskIdIt.hasNext()) {
					String taskIdTmp = newTaskIdIt.next();
					JSONObject taskJsonObj = taskJsonMap.get(taskIdTmp);
					localServiceList.put(taskJsonObj);
				}
				//更新至sp
//				BBLog.w(BBLog.TAG, "updateServiceList UPDATE: " + localServiceList.toString());
				sp.edit().putString(SPKeys.WEBSOCKET_SERVICE_LIST, localServiceList.toString()).commit();
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	private static void updateServiceAppJsonObj(JSONObject serviceJson,JSONObject serviceJsonForReplace) {
		try {
			serviceJson.put(ParameterName.beginDate, serviceJsonForReplace.optString(ParameterName.beginDate));
			serviceJson.put(ParameterName.endDate, serviceJsonForReplace.optString(ParameterName.endDate));
			serviceJson.put(ParameterName.taskType, serviceJsonForReplace.optString(ParameterName.taskType));
			serviceJson.put(ParameterName.serviceName, serviceJsonForReplace.optString(ParameterName.serviceName));
			serviceJson.put(ParameterName.command, serviceJsonForReplace.optString(ParameterName.command));
			serviceJson.put(ParameterName.url, serviceJsonForReplace.optString(ParameterName.url));
			serviceJson.put(ParameterName.taskId, serviceJsonForReplace.optString(ParameterName.taskId));
			serviceJson.put(ParameterName.stateDesc, serviceJsonForReplace.optString(ParameterName.stateDesc));
			if (serviceJson.has(ParameterName.param) && serviceJsonForReplace.has(ParameterName.param)) {
				serviceJson.put(ParameterName.param, serviceJsonForReplace.optString(ParameterName.param));
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	public static void removeWSServiceJsonImplementC05() {
		try {
			String localServiceListStr = sp.getString(SPKeys.WEBSOCKET_SERVICE_LIST, "");
			if (!Helpers.isStrNoEmpty(localServiceListStr))
				return;
			BBLog.i(Constants.TAG, "removeWSServiceJsonImplementC05");
			JSONArray localServiceList = new JSONArray(localServiceListStr);
			List<Integer> toDeleteIndexs = new ArrayList<Integer>();
			for (int i = 0; i < localServiceList.length(); i++) {
				JSONObject localServiceJsonObj = (JSONObject) localServiceList.get(i);
				String command = localServiceJsonObj.getString(ParameterName.command);
				if ("C05".equals(command) && RuleStatus.IMPLEMENTED.equals(localServiceJsonObj.getString(ParameterName.stateDesc))) {
					toDeleteIndexs.add(i);
				}
			}
			//删除标志为delete的任务
			if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
				for (int i = toDeleteIndexs.size() - 1; i >= 0; i--) {
					localServiceList.remove(toDeleteIndexs.get(i));
				}
			} else {
				for (int i = toDeleteIndexs.size() - 1; i >= 0; i--) {
					localServiceList = Helpers.remove(localServiceList, toDeleteIndexs.get(i));
				}
			}
			sp.edit().putString(SPKeys.WEBSOCKET_SERVICE_LIST, localServiceList.toString()).commit();
			return;
		} catch (JSONException e) {
			e.printStackTrace();
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	public static void removeWSServiceJsonObjByTaskId(String taskId) {
		try {
			String localServiceListStr = sp.getString(SPKeys.WEBSOCKET_SERVICE_LIST, "");
			if (!Helpers.isStrNoEmpty(localServiceListStr))
				return;
			BBLog.i(Constants.TAG, "removeWSServiceJsonObjById: " + taskId);
			JSONArray localServiceList = new JSONArray(localServiceListStr);
			for (int i = 0; i < localServiceList.length(); i++) {
				JSONObject localServiceJsonObj = (JSONObject) localServiceList.get(i);
				String taskIdTmp = localServiceJsonObj.getString(ParameterName.taskId);
				if (taskId.equals(taskIdTmp)) {
					if(Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
						localServiceList.remove(i);
					} else {
						localServiceList = Helpers.remove(localServiceList, i);
					}
					sp.edit().putString(SPKeys.WEBSOCKET_SERVICE_LIST, localServiceList.toString()).commit();
					return;
				}
			}
		} catch (JSONException e) {
			e.printStackTrace();
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	/**根据任务号返回任务json*/
	public static JSONObject getWSServiceAppJsonObjById(String taskId) {
		try {
			String localServiceListStr = sp.getString(SPKeys.WEBSOCKET_SERVICE_LIST, "");
			if (!Helpers.isStrNoEmpty(localServiceListStr))
				return null;
			JSONArray localServiceList = new JSONArray(localServiceListStr);
			for (int i = 0; i < localServiceList.length(); i++) {
				JSONObject localServiceJsonObj = (JSONObject) localServiceList.get(i);
				String taskIdTmp = localServiceJsonObj.getString(ParameterName.taskId);
				if (taskId.equals(taskIdTmp)) {
					return localServiceJsonObj;
				}
			}
		} catch (JSONException e) {
			e.printStackTrace();
		} catch (Exception e) {
			e.printStackTrace();
		}
		return null;
	}

	public static JSONObject getWSLogServiceSchedule() {
		try {
			String localServiceListStr = sp.getString(SPKeys.WEBSOCKET_SERVICE_LIST, "");
			BBLog.e(BBLog.TAG, "终端保存的ServiceApp：" + localServiceListStr);
			if (!Helpers.isStrNoEmpty(localServiceListStr))
				return null;
			JSONArray localServiceList = new JSONArray(localServiceListStr);
			for (int i = 0; i < localServiceList.length(); i++) {
				JSONObject localServiceJsonObj = (JSONObject) localServiceList.get(i);
				if ("C03".equals(localServiceJsonObj.getString(ParameterName.command))) {
					return localServiceJsonObj;
				}
			}
		} catch (JSONException e) {
			e.printStackTrace();
		} catch (Exception e) {
			e.printStackTrace();
		}
		return null;
	}

	/**
	 * 获取C0901中准备上送的service app的内容
	 * @return
	 */
	public static JSONArray uploadServiceAppList() {
		try {
			String localServiceListStr = sp.getString(SPKeys.WEBSOCKET_SERVICE_LIST, "");
			if (!Helpers.isStrNoEmpty(localServiceListStr))
				return null;
			JSONArray uploadServiceList = new JSONArray();
			JSONArray localServiceList = new JSONArray(localServiceListStr);
			HashMap<String, JSONObject> taskJsonMap = new HashMap<String, JSONObject>();
			for (int i = 0; i < localServiceList.length(); i++) {
				JSONObject localServiceJsonObj = (JSONObject) localServiceList.get(i);
				if ("05".equals(localServiceJsonObj.getString(ParameterName.taskType))
				 && RuleStatus.IMPLEMENTED.equals(localServiceJsonObj.optString(ParameterName.stateDesc))) {
					if (!taskJsonMap.containsKey(localServiceJsonObj.getString(ParameterName.serviceId))) {
						if ("C06".equals(localServiceJsonObj.getString(ParameterName.command))) {
							//如果是C06的话，那么以taskID作为key，可以放多个C06 Service
							taskJsonMap.put(localServiceJsonObj.getString(ParameterName.taskId), localServiceJsonObj);
						} else if ("C03".equals(localServiceJsonObj.getString(ParameterName.command))) {
							String service_app_str = SharedPreferencesUtils.getSharePreferencesValue(UsualData.LOG_STREAM_SERVICE_APP_EXECUTING, "");
							try {
								if (!TextUtils.isEmpty(service_app_str)
										&& localServiceJsonObj.getString(ParameterName.beginDate).equals(new JSONObject(service_app_str).getString(ParameterName.beginDate))) {
									taskJsonMap.put(localServiceJsonObj.getString(ParameterName.serviceId), localServiceJsonObj);
								}
							} catch (Exception e) {
								e.printStackTrace();
							}
						} else {
							taskJsonMap.put(localServiceJsonObj.getString(ParameterName.serviceId), localServiceJsonObj);
						}
					} else {
						if ("C06".equals(localServiceJsonObj.getString(ParameterName.command))) {
							taskJsonMap.put(localServiceJsonObj.getString(ParameterName.taskId), localServiceJsonObj);
						}
					}
				}
			}

			Set<String> newTaskIdSet = taskJsonMap.keySet();
			Iterator<String> newTaskIdIt = newTaskIdSet.iterator();
			while (newTaskIdIt.hasNext()) {
				String taskIdTmp = newTaskIdIt.next();
				JSONObject taskJsonObj = taskJsonMap.get(taskIdTmp);
				uploadServiceList.put(taskJsonObj);
			}
			BBLog.e(BBLog.TAG, "uploadServiceAppList: " + uploadServiceList);
			return uploadServiceList;
		}catch (Exception e) {
			e.printStackTrace();
		}
		return null;
	}

	public static void updateWSServiceAppState(String serviceId, String serviceState) {
		try {
			String localServiceListStr = sp.getString(SPKeys.WEBSOCKET_SERVICE_LIST, "");
			if (!Helpers.isStrNoEmpty(localServiceListStr))
				return;
			JSONArray localServiceList = new JSONArray(localServiceListStr);
			for (int i = 0; i < localServiceList.length(); i++) {
				JSONObject localServiceJsonObj = (JSONObject) localServiceList.get(i);
				String serviceTaskIdTmp = localServiceJsonObj.getString(ParameterName.taskId);
				if (serviceId.equals(serviceTaskIdTmp)) {
					BBLog.e(Constants.TAG, "updateWSTaskState " + serviceState);
					localServiceJsonObj.put(ParameterName.stateDesc, serviceState);
					sp.edit().putString(SPKeys.WEBSOCKET_SERVICE_LIST, localServiceList.toString()).commit();
					break;
				}
			}
		} catch (JSONException e) {
			e.printStackTrace();
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	public static void checkServiceAppPlusTask(Context context) {
		try {
			String localServiceListStr = sp.getString(SPKeys.WEBSOCKET_SERVICE_LIST, "");
			if (!Helpers.isStrNoEmpty(localServiceListStr))
				return;
			JSONArray localServiceList = new JSONArray(localServiceListStr);
			List<Integer> toDeleteIndexs = new ArrayList<Integer>();
			//处理06要求卸载的service app的情况，记录被要求删除的service id，然后将list中相同service id且状态为已完成的service app删除
			List<String> toDeleteServiceID = new ArrayList<>();

			boolean bNeedToUpload = false;
			BBLog.e(BBLog.TAG, "当前存在 Service App: " + localServiceList.length() + "条");
			for (int i = 0; i < localServiceList.length(); i++) {
				JSONObject localServiceJsonObj = (JSONObject) localServiceList.get(i);
//				BBLog.e(BBLog.TAG, "存在 Service App: " + localServiceJsonObj);
				if ("06".equals(localServiceJsonObj.getString(ParameterName.taskType))) {
					BBLog.e(BBLog.TAG, "此 Service App 执行卸载动作");
					RulebasedHandler.executeServiceReset(localServiceJsonObj.getString(ParameterName.command));
					toDeleteIndexs.add(i);
					if (!toDeleteServiceID.contains(localServiceJsonObj.getString(ParameterName.serviceId))) {
						toDeleteServiceID.add(localServiceJsonObj.getString(ParameterName.serviceId));
					}
					bNeedToUpload = true;
				} else if (RuleStatus.READY.equals(localServiceJsonObj.getString(ParameterName.stateDesc))
				|| RuleStatus.IMPLEMENTED.equals(localServiceJsonObj.getString(ParameterName.stateDesc))
				|| RuleStatus.READY_TO_FALLBACK.equals(localServiceJsonObj.getString(ParameterName.stateDesc))) {
					String beginDateStr = localServiceJsonObj.getString(ParameterName.beginDate);
					String endDateStr = localServiceJsonObj.getString(ParameterName.endDate);
					long nowTime = System.currentTimeMillis();
					try {
						long beginTime;
						long endTime;
						if (DateTimeUtils.isNumeric(beginDateStr) && DateTimeUtils.isNumeric(endDateStr)) {
							beginTime = new Long(beginDateStr).longValue();
							endTime = new Long(endDateStr).longValue();
						} else {
							SimpleDateFormat sdf = new SimpleDateFormat(Constants.dateFormat);
							beginTime = sdf.parse(beginDateStr).getTime();
							endTime = sdf.parse(endDateStr).getTime();
						}

						if (RuleStatus.READY.equals(localServiceJsonObj.getString(ParameterName.stateDesc))) {
							if (beginTime < nowTime && nowTime < endTime) {
								//在周期内，启动日志；
								BBLog.e(BBLog.TAG, "到期执行 Service App: " + localServiceJsonObj);
								RulebasedHandler.executeService(localServiceJsonObj);
								if ("C05".equals(localServiceJsonObj.getString(ParameterName.command))
								 || "C06".equals(localServiceJsonObj.getString(ParameterName.command))
								 || "C07".equals(localServiceJsonObj.getString(ParameterName.command))) {
									bNeedToUpload = false;
									localServiceJsonObj.put(ParameterName.stateDesc, RuleStatus.EXECUTING);
								} else {
									localServiceJsonObj.put(ParameterName.stateDesc, RuleStatus.IMPLEMENTED);
									WebSocketSender.C0108_uploadWSServiceAppResult(localServiceJsonObj, TaskState.INSTALL_SUCCESS, "");
									bNeedToUpload = true;
								}
								//针对logStream的enddate9999时，有效期是3天的情况，如果是过期的，直接删除，避免C0901上送
								if ("C03".equals(localServiceJsonObj.getString(ParameterName.command)) && "05".equals(localServiceJsonObj.getString(ParameterName.taskType)) && "9999-12-31 23:59:59".equals(endDateStr)) {
									if (nowTime >= (beginTime+24*3*60*60*1000)) {
										toDeleteIndexs.add(i);
									}
								}
							} else if (nowTime > endTime) {
								toDeleteIndexs.add(i);
							}
						} else if (RuleStatus.IMPLEMENTED.equals(localServiceJsonObj.getString(ParameterName.stateDesc))) {
							if ((endTime < nowTime) || (!"C01".equals(localServiceJsonObj.getString(ParameterName.command))
									&& !"C03".equals(localServiceJsonObj.getString(ParameterName.command))
									&& !"C05".equals(localServiceJsonObj.getString(ParameterName.command))
									&& !"C06".equals(localServiceJsonObj.getString(ParameterName.command))
									&& !"C07".equals(localServiceJsonObj.getString(ParameterName.command)))) {
								BBLog.e(BBLog.TAG, "过期删除 Service App: " + localServiceJsonObj);
								RulebasedHandler.executeServiceFallback(localServiceJsonObj);
								localServiceJsonObj.put(ParameterName.stateDesc, RuleStatus.COMPLETED);
								if (endTime < nowTime) {
									WebSocketSender.C0108_uploadWSServiceAppResult(localServiceJsonObj, TaskState.UNINSTALL_EXPIRE, "");
								}
								toDeleteIndexs.add(i);
								bNeedToUpload = true;
							}
						} else if (RuleStatus.READY_TO_FALLBACK.equals(localServiceJsonObj.getString(ParameterName.stateDesc))) {
							BBLog.e(BBLog.TAG, "过期回退 Service App: " + localServiceJsonObj);
							RulebasedHandler.executeServiceFallback(localServiceJsonObj);
							localServiceJsonObj.put(ParameterName.stateDesc, RuleStatus.COMPLETED);
							toDeleteIndexs.add(i);
							bNeedToUpload = true;
						}
					} catch (Exception e) {
						e.printStackTrace();
					}
				}
			}

			//删除标志为delete的任务
			if (Build.VERSION.SDK_INT>=Build.VERSION_CODES.KITKAT) {
				for (int i = toDeleteIndexs.size()-1; i >= 0; i--) {
					localServiceList.remove(toDeleteIndexs.get(i));
				}
			} else{
				for (int i = toDeleteIndexs.size()-1; i >= 0; i--) {
					localServiceList = Helpers.remove(localServiceList, toDeleteIndexs.get(i));
				}
			}

			for (int idx=0; idx<toDeleteServiceID.size(); idx++) {
				toDeleteIndexs.clear();
				String serviceID = toDeleteServiceID.get(idx);
				for (int i = 0; i < localServiceList.length(); i++) {
					JSONObject localServiceJsonObj = (JSONObject) localServiceList.get(i);
					if ("05".equals(localServiceJsonObj.getString(ParameterName.taskType))
					 && RuleStatus.IMPLEMENTED.equals(localServiceJsonObj.getString(ParameterName.stateDesc))
					 && serviceID.equals(localServiceJsonObj.getString(ParameterName.serviceId))) {
						toDeleteIndexs.add(i);
						if ("C03".equals(localServiceJsonObj.getString(ParameterName.command))) {
							removeExecutingLogStreangServiceAppByServiceId(serviceID);
						}
					}
				}
				//删除标志为delete的任务
				if (Build.VERSION.SDK_INT>=Build.VERSION_CODES.KITKAT) {
					for (int i = toDeleteIndexs.size()-1; i >= 0; i--) {
						localServiceList.remove(toDeleteIndexs.get(i));
					}
				} else{
					for (int i = toDeleteIndexs.size()-1; i >= 0; i--) {
						localServiceList = Helpers.remove(localServiceList, toDeleteIndexs.get(i));
					}
				}
			}

			sp.edit().putString(SPKeys.WEBSOCKET_SERVICE_LIST, localServiceList.toString()).commit();
			if (bNeedToUpload) {
				WebSocketSender.C0901_AppInfoUpload();
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	/**与本地缓存的wifi profile 进行对比、更新*/
	public static void updateWifiProfileList(JSONArray wifiProflieList) {
		try {
			if (wifiProflieList == null) {
				BBLog.e(BBLog.TAG, "wifiProfileList is empty. updateWifiProfileList failed");
				return;
			}
/*
			byte[] encode = DESUtils.encryptCbc(wifiProflieList.toString().getBytes(),
					SecurityOperate.getInstance().getDeviceDK(ContextUtil.getInstance()).getBytes(),
					SecurityOperate.getInstance().getDeviceDIV(ContextUtil.getInstance()).getBytes(),
					DESUtils.Padding.PKCS5_PADDING);
			String wplData = StringUtils.Hex2String(encode);
*/
			String wplData = AESUtil.encryptToHex(wifiProflieList.toString());
			BBLog.e(BBLog.TAG, "updateWPList: wplData = "+ wplData );

			if (!TextUtils.isEmpty(getWifiConfigFilePath())) {
				if (!TextUtils.isEmpty(wplData)) {
					boolean result = writeWifiConfigToFile(wplData);
					BBLog.d(BBLog.TAG, "writeWifiConfigToFile result : " + result );
				}
			}else {
				//更新至sp
				sp.edit().putString(SPKeys.WEBSOCKET_WIFL_PROFILE_LIST,  wplData).commit();
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	/**
	 * 获取缓存中wifi profile 信息
	 * @return
	 */
	public static JSONArray getWifiProfileListFromLocal() {
		String wifiProfileData = "";
		if (!TextUtils.isEmpty(getWifiConfigFilePath())) {
			wifiProfileData = readFile(getWifiConfigFilePath());
		}else {
			wifiProfileData = sp.getString(SPKeys.WEBSOCKET_WIFL_PROFILE_LIST, "");
		}
		Constants.wifi_query_count++;
		if (Constants.wifi_query_count%5==0) {
			BBLog.e(BBLog.TAG, "getWplDataFromLocal: " + wifiProfileData);
			Constants.wifi_query_count = 0;
		}

		if (!TextUtils.isEmpty(wifiProfileData)) {
			String original = null;

			// step 1: 先用 aes 尝试解码
			try {
				original = AESUtil.decryptHexToString(wifiProfileData);
				if (!TextUtils.isEmpty(original)) {
					return new JSONArray(original);
				}
			} catch (Exception e) {
				e.printStackTrace();
			}
/*
			// step 2: aes 解码失败，再用des解码
			try {
				byte[] decode = DESUtils.decryptCbc(StringUtils.String2Hex(wifiProfileData),
						SecurityOperate.getInstance().getDeviceDK(ContextUtil.getInstance()).getBytes(),
						SecurityOperate.getInstance().getDeviceDIV(ContextUtil.getInstance()).getBytes(),
						DESUtils.Padding.PKCS5_PADDING);
				original = new String(decode);
			} catch (Exception e) {
				e.printStackTrace();
			}

			//step 3: 若des解码成功，将数据重新aes加密并保存
			try {
				String wplData = AESUtil.encryptToHex(original);
				if (!TextUtils.isEmpty(getWifiConfigFilePath())) {
					if (!TextUtils.isEmpty(wplData)) {
						writeWifiConfigToFile(wplData);
					}
				} else {
					sp.edit().putString(SPKeys.WEBSOCKET_WIFL_PROFILE_LIST, wplData).commit();
				}
			} catch (Exception e) {
				e.printStackTrace();
			}

			try {
				if (!TextUtils.isEmpty(original)) {
					return new JSONArray(original);
				}
			} catch (Exception e) {
				e.printStackTrace();
			}
*/
		}
		return null;
	}

	@Deprecated
	public static void updateProvisionDefaultWifiProfile(JSONArray wifiProflieList) {
		try {
			if (wifiProflieList == null) {
				BBLog.e(BBLog.TAG, "wifiProfileList is empty. updateWifiProfileList failed");
				return;
			}
			sp.edit().putString(UsualData.SHARED_PREFERENCES_PROVISION_DEFAULT_WIFI, wifiProflieList.toString()).commit();
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	/**
	 * 获取缓存中provision wifi profile 信息
	 * @return
	 */
	@Deprecated
	public static JSONArray getProvisionDefaultWifiProfileListFromLocal() {
		String wifiProfileJson = sp.getString(UsualData.SHARED_PREFERENCES_PROVISION_DEFAULT_WIFI,"");
		if (!TextUtils.isEmpty(wifiProfileJson)) {
			try {
				return new JSONArray(wifiProfileJson);
			} catch (Exception e) {
				e.printStackTrace();
				return null;
			}
		}
		return null;
	}

	public static void removeExecutingLogStreangServiceAppByServiceId(String serviceId) {
		try {
			String service_app_str = SharedPreferencesUtils.getSharePreferencesValue(UsualData.LOG_STREAM_SERVICE_APP_EXECUTING, "");
			if (!Helpers.isStrNoEmpty(service_app_str))
				return;
			BBLog.i(Constants.TAG, "removeExecutingLogStreangServiceAppByServiceId: " + serviceId);
			BBLog.i(Constants.TAG, "removeExecutingLogStreangServiceAppByServiceId: " + service_app_str);
			JSONObject localServiceApp = new JSONObject(service_app_str);
			String serviceIdTmp = localServiceApp.getString(ParameterName.serviceId);
			if (serviceId.equals(serviceIdTmp)) {
				SharedPreferencesUtils.setSharePreferencesValue(UsualData.LOG_STREAM_SERVICE_APP_EXECUTING, "");
				return;
			}
		} catch (JSONException e) {
			e.printStackTrace();
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
}
