package com.bbpos.wiseapp.system.api;

import android.content.Context;
import android.content.Intent;
import android.os.Build;

public class ServiceApi {
    private Context mContext = null;
    private static ServiceApi mServiceApi = null;

    public void init(Context context) {
        if (mContext == null) {
            mContext = context;
        }
    }

    private Context getContext() {
        return mContext;
    }

    public static ServiceApi getIntance() {
        if (mServiceApi == null) {
            mServiceApi = new ServiceApi();
        }

        return mServiceApi;
    }

    public void startService(Intent intent) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
//            getContext().startForegroundService(intent);
            getContext().startService(intent);
        } else {
            getContext().startService(intent);
        }
    }
}
