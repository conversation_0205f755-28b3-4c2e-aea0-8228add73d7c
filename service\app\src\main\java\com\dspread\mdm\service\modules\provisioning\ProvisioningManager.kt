package com.dspread.mdm.service.modules.provisioning

import android.content.Context
import android.util.Base64
import com.dspread.mdm.service.config.ProvisionConfig
import com.dspread.mdm.service.modules.provisioning.model.*
import com.dspread.mdm.service.modules.provisioning.model.ProvisioningTrigger as ProvisioningTriggerType
import com.dspread.mdm.service.network.https.HttpDownloader
import com.dspread.mdm.service.platform.api.device.DeviceInfoApi
import com.dspread.mdm.service.platform.monitor.NetworkTrafficMonitor
import com.dspread.mdm.service.utils.log.Logger
import kotlinx.coroutines.*
import org.json.JSONObject
import java.io.File
import java.net.HttpURLConnection
import java.net.URL

/**
 * Provisioning管理器
 * 负责设备配置的下载、解析和应用
 */
class ProvisioningManager private constructor(private val context: Context) {

    init {
        Logger.provI("ProvisioningManager初始化完成")
    }

    companion object {
        @Volatile
        private var INSTANCE: ProvisioningManager? = null

        fun getInstance(context: Context): ProvisioningManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: ProvisioningManager(context.applicationContext).also { INSTANCE = it }
            }
        }
    }
    
    private var currentJob: Job? = null

    // 状态标志
    private var flags: ProvisioningFlags = loadFlags()

    // 执行状态锁，防止并发执行
    @Volatile
    private var isExecuting = false

    // 初始启动状态管理
    @Volatile
    private var isInitialStartup = true
    
    // 回调接口
    interface ProvisioningCallback {
        fun onProgress(result: ProvisioningResult)
        fun onCompleted(result: ProvisioningResult)
        fun onError(result: ProvisioningResult)
    }
    
    /**
     * 执行Provisioning配置
     */
    fun executeProvisioning(
        configUrl: String,
        trigger: ProvisioningTriggerType = ProvisioningTriggerType.TIMER,
        callback: ProvisioningCallback? = null
    ) {
        // 取消之前的任务
        currentJob?.cancel()
        
        currentJob = CoroutineScope(Dispatchers.IO).launch {
            try {
                Logger.provI("开始执行Provisioning配置，触发类型: $trigger")
                
                // 1. 下载配置文件
                callback?.onProgress(ProvisioningResult(
                    ProvisioningStatus.DOWNLOADING_CONFIG,
                    "正在下载配置文件...",
                    completedSteps = 0,
                    totalSteps = 4
                ))
                
                // 1. 尝试请求远程配置
                var isRemoteConfigSuccess = false
                val config = try {
                    Logger.provI("开始请求远程配置...")
                    val apiResponse = requestConfig(configUrl)

                    Logger.provI("远程配置请求成功")

                    // 解析配置
                    val parsedConfig = ProvisioningConfig.fromJson(apiResponse) ?: throw Exception("远程配置解析失败")
                    Logger.provI("远程配置解析成功 - CID: ${parsedConfig.cid}")

                    // 只有解析成功后才保存API响应到本地
                    saveApiResponse(apiResponse)

                    isRemoteConfigSuccess = true  // 标记远程配置成功
                    parsedConfig

                } catch (e: Exception) {
                    Logger.provW("远程配置请求失败: ${e.message}")

                    // 2. 尝试使用本地保存的配置
                    val localConfig = loadLocalConfig()
                    if (localConfig != null) {
                        Logger.provI("📂 使用本地保存的配置 - CID: ${localConfig.cid}")
                        localConfig
                    } else {
                        // 3. 使用默认配置
                        Logger.provI("使用默认配置")
                        val defaultConfig = loadDefaultConfig() ?: throw Exception("默认配置加载失败")
                        defaultConfig
                    }
                }
                
//                // 2. 下载系统资源
//                val downloadTasks = createDownloadTasks(config.system)
//                var completedTasks = 1 // 配置下载已完成
//
//                for (task in downloadTasks) {
//                    if (!isActive) break // 检查协程是否被取消
//
//                    callback?.onProgress(ProvisioningResult(
//                        getDownloadStatus(task.name),
//                        "正在下载 ${task.name}...",
//                        completedSteps = completedTasks,
//                        totalSteps = 4
//                    ))
//
//                    executeDownloadTask(task)
//                    completedTasks++
//                }
                
                // 4. 应用配置
                callback?.onProgress(ProvisioningResult(
                    ProvisioningStatus.APPLYING_CONFIG,
                    "正在应用配置...",
                    completedSteps = 3,
                    totalSteps = 4
                ))
                
                applyConfiguration(config)

                // 5. 根据远程配置是否成功决定是否标记完成
                if (isRemoteConfigSuccess) {
                    // 远程配置成功，标记首次配置完成
                    flags.markCompleted(config.cid)
                    Logger.provI("远程配置成功，标记首次配置完成")
                } else {
                    // 远程配置失败，只更新时间和CID，但不标记首次配置完成
                    flags.lastProvisioningTime = System.currentTimeMillis()
                    flags.lastConfigCid = config.cid
                    Logger.provW("远程配置失败，首次配置状态保持未完成，下次启动将重试")
                }
                saveFlags()
                // 注意：API响应已经在saveApiResponse()中保存了，不需要重复保存
                
                // 标记初始启动完成（无论成功失败，都表示首次执行已完成）
                if (trigger == ProvisioningTriggerType.FIRST_BOOT) {
                    markInitialStartupCompleted()
                }

                Logger.provI("Provisioning配置执行完成")
                callback?.onCompleted(ProvisioningResult(
                    ProvisioningStatus.COMPLETED,
                    "配置执行完成",
                    completedSteps = 4,
                    totalSteps = 4
                ))



            } catch (e: Exception) {
                Logger.provE("Provisioning执行失败", e)

                // 标记初始启动完成（即使失败，也表示首次执行已完成）
                if (trigger == ProvisioningTriggerType.FIRST_BOOT) {
                    markInitialStartupCompleted()
                }

                callback?.onError(ProvisioningResult(
                    ProvisioningStatus.FAILED,
                    "配置执行失败: ${e.message}",
                    errorCode = "PROVISIONING_ERROR"
                ))

                // 即使失败也要启动业务流程
                // 确保默认配置文件存在，如果不存在则创建
                try {
                    if (!hasValidConfig()) {
                        Logger.provI("创建紧急默认配置")
                        initializeDefaultConfig()
                    }
                    Logger.provI("Provisioning完成: 即使配置失败也启动业务流程")
                } catch (fallbackException: Exception) {
                    Logger.provE("紧急配置创建失败", fallbackException)
                    // 即使紧急配置失败，也要尝试启动业务流程
                    // 让系统使用硬编码的最小配置运行
                }
            }
        }
    }
    
    /**
     * 请求配置API
     * 支持新的API格式: {configUrl}?SN=base64(serialNumber)&timestamp=currentTime
     * 默认配置URL定义在 ProvisionConfig.DEFAULT_CONFIG_URL
     */
    private suspend fun requestConfig(configUrl: String): JSONObject = withContext(Dispatchers.IO) {
        try {
            // 构建完整的请求URL
            val fullUrl = buildConfigUrl(configUrl)

            val url = URL(fullUrl)
            val connection = url.openConnection() as HttpURLConnection

            connection.apply {
                requestMethod = "GET"
                connectTimeout = 10000 // 10秒连接超时
                readTimeout = 15000    // 15秒读取超时
                setRequestProperty("Accept", "application/json")
                setRequestProperty("User-Agent", "SmartMDM/1.0")
            }

            val responseCode = connection.responseCode

            if (responseCode == HttpURLConnection.HTTP_OK) {
                val responseContent = connection.inputStream.bufferedReader().use { it.readText() }

                // 统计HTTP下载流量
                NetworkTrafficMonitor.recordHttpDownload(responseContent.toByteArray(Charsets.UTF_8).size.toLong())

                // 解析API响应格式
                val apiResponse = JSONObject(responseContent)

                // 检查API响应状态
                val stateCode = apiResponse.optString("stateCode", "")
                if (stateCode != "0") {
                    val description = apiResponse.optString("description", "未知错误")
                    Logger.prov("API响应内容: ${responseContent.take(200)}...")
                    throw Exception("API返回错误: $stateCode - $description")
                }

                Logger.provI("远程配置获取成功")
                // 返回完整的API响应（包含外层结构）
                apiResponse

            } else {
                val errorContent = connection.errorStream?.bufferedReader()?.use { it.readText() } ?: "无错误信息"
                throw Exception("HTTP请求失败: $responseCode - $errorContent")
            }

        } catch (e: Exception) {
            Logger.provE("配置API请求失败", e)
            throw e
        }
    }

    /**
     * 构建配置请求URL
     * 格式: {baseUrl}?SN=base64(serialNumber)&timestamp=currentTime
     * 默认baseUrl定义在 ProvisionConfig.DEFAULT_CONFIG_URL
     */
    private fun buildConfigUrl(baseUrl: String): String {
        return try {
            // 获取设备序列号并构建请求URL
            val deviceInfoApi = DeviceInfoApi(context)
            val serialNumber = deviceInfoApi.getSerialNumber()
            val encodedSN = Base64.encodeToString(serialNumber.toByteArray(Charsets.UTF_8), Base64.NO_WRAP)
            val timestamp = System.currentTimeMillis()
            val fullUrl = "$baseUrl?SN=$encodedSN&timestamp=$timestamp"

            Logger.provI("请求配置: $fullUrl")
            fullUrl
        } catch (e: Exception) {
            Logger.provE("构建配置URL失败，使用基础URL", e)
            baseUrl
        }
    }
    



    
    /**
     * 创建下载任务列表
     */
    private fun createDownloadTasks(systemConfig: SystemConfig): List<DownloadTask> {
        val tasks = mutableListOf<DownloadTask>()
        
        // Logo下载任务
        systemConfig.logo?.let { url ->
            tasks.add(DownloadTask(
                name = "Logo",
                url = url,
                targetPath = ProvisionConfig.getLogoFilePath(context),
                md5 = systemConfig.logoMd5
            ))
        }

        // 开机动画下载任务
        systemConfig.bootAnimation?.let { url ->
            tasks.add(DownloadTask(
                name = "BootAnimation",
                url = url,
                targetPath = ProvisionConfig.getBootAnimationFilePath(context),
                md5 = systemConfig.bootAnimationMd5
            ))
        }
        
        return tasks
    }
    
    /**
     * 执行下载任务
     */
    private suspend fun executeDownloadTask(task: DownloadTask) {
        withContext(Dispatchers.IO) {
            try {
                val targetFile = File(task.targetPath)

                // 检查文件是否已存在且MD5匹配
                if (targetFile.exists() && task.md5 != null) {
                    val existingMd5 = calculateFileMd5(targetFile)
                    if (existingMd5 == task.md5) {
                        Logger.provI("文件已存在且MD5匹配，跳过下载: ${task.name}")
                        task.status = DownloadStatus.COMPLETED
                        return@withContext
                    } else {
                        Logger.provI("文件存在但MD5不匹配，重新下载: ${task.name}")
                        Logger.prov("   期望MD5: ${task.md5}")
                        Logger.prov("   实际MD5: $existingMd5")
                    }
                } else if (targetFile.exists()) {
                    Logger.provI("文件存在但无MD5校验，重新下载: ${task.name}")
                } else {
                    Logger.provI("文件不存在，开始下载: ${task.name}")
                }

                task.status = DownloadStatus.DOWNLOADING
                val parentDir = targetFile.parentFile

                Logger.provI("下载: ${task.url} -> ${task.targetPath}")

                // 确保目录存在
                if (parentDir != null && !parentDir.exists()) {
                    val mkdirsResult = parentDir.mkdirs()
                    if (!mkdirsResult) {
                        Logger.provE("创建目录失败: ${parentDir.absolutePath}")
                    }
                }

                val success = HttpDownloader.fileDownloadByUrlWithRetry(
                    task.url,
                    targetFile.absolutePath,
                    0L, // 文件大小未知
                    task.md5 ?: "",
                    object : HttpDownloader.FileDownloadCallBack {
                        override fun requestSuccess() {
                            // 下载成功
                        }
                        override fun requestFail(errorCode: Int, errorStr: String) {
                            throw Exception("${task.name} 下载失败: $errorStr")
                        }
                        override fun onDownloading(curFileSize: Long, fileSize: Long) {
                            // 下载进度
                        }
                    }
                )
                
                if (success) {
                    task.status = DownloadStatus.COMPLETED
                    Logger.provI("${task.name} 下载完成: ${task.targetPath}")
                } else {
                    task.status = DownloadStatus.FAILED
                    task.errorMessage = "下载失败"
                    throw Exception("${task.name} 下载失败")
                }
                
            } catch (e: Exception) {
                task.status = DownloadStatus.FAILED
                task.errorMessage = e.message
                throw e
            }
        }
    }
    
    /**
     * 应用配置
     */
    private fun applyConfiguration(config: ProvisioningConfig) {
        // TODO: 应用各种配置
        // 1. 更新WebSocket配置
        // 2. 更新GPS配置
        // 3. 更新省电模式配置
        // 4. 更新时区配置
        
        Logger.provI("配置应用完成")
    }
    
    /**
     * 获取下载状态对应的Provisioning状态
     */
    private fun getDownloadStatus(taskName: String): ProvisioningStatus {
        return when (taskName) {
            "Logo" -> ProvisioningStatus.DOWNLOADING_LOGO
            "BootAnimation" -> ProvisioningStatus.DOWNLOADING_BOOT_ANIMATION
            else -> ProvisioningStatus.IN_PROGRESS
        }
    }
    
    /**
     * 获取当前状态标志
     */
    fun getFlags(): ProvisioningFlags = flags.copy()
    
    /**
     * 保存状态标志到配置目录
     */
    private fun saveFlags() {
        try {
            val flagsFile = File(ProvisionConfig.getProvisioningFlagsFilePath())

            val flagsJson = JSONObject().apply {
                put("isFirstProvisioningCompleted", flags.isFirstProvisioningCompleted)
                put("isLogoDownloadSuccess", flags.isLogoDownloadSuccess)
                put("isBootAnimationDownloadSuccess", flags.isBootAnimationDownloadSuccess)
                put("lastProvisioningTime", flags.lastProvisioningTime)
                put("lastConfigCid", flags.lastConfigCid)
            }

            flagsFile.writeText(flagsJson.toString())
            Logger.prov("状态标志保存成功: ${flagsFile.absolutePath}")

        } catch (e: Exception) {
            Logger.provE("保存状态标志失败", e)
        }
    }

    /**
     * 从配置目录加载状态标志
     */
    private fun loadFlags(): ProvisioningFlags {
        return try {
            val flagsFile = File(ProvisionConfig.getProvisioningFlagsFilePath())

            if (!flagsFile.exists()) {
                Logger.prov("状态标志文件不存在，使用默认值")
                return ProvisioningFlags()
            }

            val flagsJson = JSONObject(flagsFile.readText())
            ProvisioningFlags(
                isFirstProvisioningCompleted = flagsJson.optBoolean("isFirstProvisioningCompleted", false),
                isLogoDownloadSuccess = flagsJson.optBoolean("isLogoDownloadSuccess", false),
                isBootAnimationDownloadSuccess = flagsJson.optBoolean("isBootAnimationDownloadSuccess", false),
                lastProvisioningTime = flagsJson.optLong("lastProvisioningTime", 0L),
                lastConfigCid = flagsJson.optString("lastConfigCid", "")
            ).also {
                Logger.prov("状态标志加载成功: ${flagsFile.absolutePath}")
            }

        } catch (e: Exception) {
            Logger.provE("加载状态标志失败，使用默认值", e)
            ProvisioningFlags()
        }
    }


    
    /**
     * 获取当前保存的配置
     * 优先级：本地配置 > 默认配置
     */
    fun getCurrentConfig(): ProvisioningConfig? {
        // 1. 尝试加载本地配置
        val localConfig = loadLocalConfig()
        if (localConfig != null) {
            return localConfig
        }

        // 2. 加载默认配置
        val defaultConfig = loadDefaultConfig()
        if (defaultConfig != null) {
            return defaultConfig
        }

        return null
    }

    /**
     * 检查是否已完成首次配置
     */
    fun isFirstProvisioningCompleted(): Boolean {
        return flags.isFirstProvisioningCompleted
    }

    /**
     * 检查是否为初始启动状态
     */
    fun isInitialStartup(): Boolean {
        return isInitialStartup
    }

    /**
     * 标记初始启动完成（统一管理）
     * 在首次Provisioning执行后调用，无论成功失败
     */
    fun markInitialStartupCompleted() {
        if (isInitialStartup) {
            isInitialStartup = false
            Logger.provI("ProvisioningManager: 初始启动标记完成，网络重连重试功能已激活")
        }
    }

    /**
     * 初始化默认配置
     * 使用代码中的DefaultProvisioningConfig保存到配置目录
     */
    fun initializeDefaultConfig(): Boolean {
        return try {
            val configFile = File(ProvisionConfig.getProvisioningConfigFilePath())

            // 如果配置文件已存在，不覆盖
            if (configFile.exists()) {
                Logger.prov("配置文件已存在，跳过默认配置初始化")
                return true
            }

            // 获取默认配置
            val defaultConfig = ProvisionConfig.getDefaultConfig()

            // 构建API响应格式的JSON（按照标准结构）
            val apiResponse = JSONObject().apply {
                put("data", JSONObject().apply {
                    put("cid", defaultConfig.cid)
                    put("requestTime", defaultConfig.requestTime)
                    put("client", "default")

                    // 系统配置（直接在data下）
                    put("system", JSONObject().apply {
                        defaultConfig.system.bootAnimation?.let { put("bootAnimation", it) }
                        defaultConfig.system.bootAnimationMd5?.let { put("bootAnimationMd5", it) }
                        defaultConfig.system.logo?.let { put("logo", it) }
                        defaultConfig.system.logoMd5?.let { put("logoMd5", it) }
                        put("timezone", defaultConfig.system.timezone)

                        put("gps", JSONObject().apply {
                            put("minUpdateTime", defaultConfig.system.gpsConfig.minUpdateTime)
                            put("scheduleTime", defaultConfig.system.gpsConfig.scheduleTime)
                            put("maxLocateTime", defaultConfig.system.gpsConfig.maxLocateTime)
                            put("minDistance", defaultConfig.system.gpsConfig.minDistance)
                            put("valid_distance", defaultConfig.system.gpsConfig.validDistance)
                            put("care", defaultConfig.system.gpsConfig.care)
                        })

                        put("powerSaveMode", JSONObject().apply {
                            put("enable", defaultConfig.system.powerSaveMode.enable)
                            put("screenTimeout", defaultConfig.system.powerSaveMode.screenTimeout)
                        })
                    })

                    // 自定义配置
                    put("customization", JSONObject().apply {
                        // 轮询配置
                        put("polling", JSONObject().apply {
                            put("heartbeatTime", defaultConfig.polling.heartbeatTime)
                            put("terminalInfoTime", defaultConfig.polling.terminalInfoTime)
                            put("statusApiUrl", defaultConfig.polling.statusApiUrl)
                            put("remoteUrl", defaultConfig.polling.remoteUrl)
                            put("uploadMode", defaultConfig.polling.uploadMode)
                            put("wssreconn", JSONObject().apply {
                                put("delayPolicy", defaultConfig.polling.wssReconnConfig.delayPolicy)
                                put("delaySwitch", defaultConfig.polling.wssReconnConfig.delaySwitch)
                                put("delayTime", defaultConfig.polling.wssReconnConfig.delayTime)
                            })
                        })
                    })
                })
                put("stateCode", "0")
                put("description", "SUCCESS")
                put("version", "v1.0.1.20250801")
                put("function", "getProvisioningConfig")
                put("mode", "dev")
            }

            // 确保父目录存在
            val parentDir = configFile.parentFile
            if (parentDir != null && !parentDir.exists()) {
                val mkdirsResult = parentDir.mkdirs()
                if (!mkdirsResult) {
                    Logger.provE("创建配置目录失败: ${parentDir.absolutePath}")
                    return false
                }
                Logger.provI("创建配置目录成功: ${parentDir.absolutePath}")
            }

            // 保存完整的API响应格式到配置文件
            configFile.writeText(apiResponse.toString())

            Logger.provI("默认配置初始化成功: ${configFile.absolutePath}")
            Logger.provI("默认配置CID: ${defaultConfig.cid}")

            true

        } catch (e: Exception) {
            Logger.provE("初始化默认配置失败", e)
            false
        }
    }

    /**
     * 保存API响应到本地
     */
    private fun saveApiResponse(apiResponse: JSONObject) {
        try {
            val configFile = File(ProvisionConfig.getProvisioningConfigFilePath())

            // 确保父目录存在
            val parentDir = configFile.parentFile
            if (parentDir != null && !parentDir.exists()) {
                val mkdirsResult = parentDir.mkdirs()
                if (!mkdirsResult) {
                    Logger.provE("创建配置目录失败: ${parentDir.absolutePath}")
                    return
                }
                Logger.provI("创建配置目录成功: ${parentDir.absolutePath}")
            }

            // 保存完整的API响应
            configFile.writeText(apiResponse.toString())

            Logger.provI("API响应已保存到本地: ${configFile.absolutePath}, 具体内容为:\n${apiResponse}")

        } catch (e: Exception) {
            Logger.provE("保存API响应失败", e)
        }
    }

    /**
     * 加载本地保存的配置
     */
    private fun loadLocalConfig(): ProvisioningConfig? {
        return try {
            val configFile = File(ProvisionConfig.getProvisioningConfigFilePath())

            if (!configFile.exists()) {
                return null
            }

            val content = configFile.readText()
            val apiResponse = JSONObject(content)
            if (apiResponse.has("data") && apiResponse.has("stateCode")) {
                ProvisioningConfig.fromJson(apiResponse)
            } else {
                Logger.provW("本地配置格式不正确，期望完整的API响应格式")
                null
            }

        } catch (e: Exception) {
            Logger.provE("加载本地配置失败", e)
            null
        }
    }

    /**
     * 加载默认配置
     * 使用代码中的DefaultProvisioningConfig替代assets/default_config.json
     */
    private fun loadDefaultConfig(): ProvisioningConfig? {
        return try {
            ProvisionConfig.getDefaultConfig()
        } catch (e: Exception) {
            Logger.provE("加载默认配置失败", e)
            null
        }
    }

    /**
     * 取消当前任务
     */
    fun cancel() {
        currentJob?.cancel()
        currentJob = null
    }

    /**
     * 打印API响应内容
     */
    private fun printApiResponse(apiResponse: JSONObject) {
        try {
            Logger.provI("   API响应详情:")
            Logger.provI("   状态码: ${apiResponse.optString("stateCode")}")
            Logger.provI("   描述: ${apiResponse.optString("description")}")
            Logger.provI("   版本: ${apiResponse.optString("version")}")
            Logger.provI("   功能: ${apiResponse.optString("function")}")
            Logger.provI("   模式: ${apiResponse.optString("mode")}")

            val dataJson = apiResponse.optJSONObject("data")
            if (dataJson != null) {
                Logger.provI("   CID: ${dataJson.optString("cid")}")
                Logger.provI("   请求时间: ${dataJson.optString("requestTime")}")
            }
        } catch (e: Exception) {
            Logger.provE("打印API响应失败", e)
        }
    }

    /**
     * 打印Provisioning配置详情
     */
    private fun printProvisioningConfig(config: ProvisioningConfig, source: String) {
        try {
            Logger.provI("   $source 详情:")
            Logger.provI("   CID: ${config.cid}")
            Logger.provI("   请求时间: ${config.requestTime}")

            Logger.provI("WebSocket配置:")
            Logger.provI("   通讯URL: ${config.polling.statusApiUrl}")
            Logger.provI("   远程URL: ${config.polling.remoteUrl}")
            Logger.provI("   心跳时间: ${config.polling.heartbeatTime}秒")
            Logger.provI("   终端信息时间: ${config.polling.terminalInfoTime}秒")
            Logger.provI("   上传模式: ${config.polling.uploadMode}")

            Logger.provI("重连配置:")
            Logger.provI("   延迟策略: ${config.polling.wssReconnConfig.delayPolicy}")
            Logger.provI("   延迟开关: ${config.polling.wssReconnConfig.delaySwitch}")
            Logger.provI("   延迟时间: ${config.polling.wssReconnConfig.delayTime}")

            Logger.provI("🎨 系统配置:")
            Logger.provI("   Logo: ${config.system.logo ?: "未设置"}")
            Logger.provI("   Logo MD5: ${config.system.logoMd5 ?: "未设置"}")
            Logger.provI("   开机动画: ${config.system.bootAnimation ?: "未设置"}")
            Logger.provI("   开机动画 MD5: ${config.system.bootAnimationMd5 ?: "未设置"}")
            Logger.provI("   时区: ${config.system.timezone}")

            Logger.provI("📍 GPS配置:")
            Logger.provI("   最小更新时间: ${config.system.gpsConfig.minUpdateTime}秒")
            Logger.provI("   调度时间: ${config.system.gpsConfig.scheduleTime}秒")
            Logger.provI("   最大定位时间: ${config.system.gpsConfig.maxLocateTime}秒")
            Logger.provI("   最小距离: ${config.system.gpsConfig.minDistance}米")
            Logger.provI("   有效距离: ${config.system.gpsConfig.validDistance}米")
            Logger.provI("   GPS关注: ${config.system.gpsConfig.care}")

            Logger.provI("🔋 省电模式:")
            Logger.provI("   启用: ${config.system.powerSaveMode.enable}")
            Logger.provI("   屏幕超时: ${config.system.powerSaveMode.screenTimeout}")

        } catch (e: Exception) {
            Logger.provE("打印配置详情失败", e)
        }
    }

    /**
     * 打印当前Provisioning状态
     */
    private fun printProvisioningStatus() {
        try {
            Logger.provI("当前Provisioning状态:")
            Logger.provI("   首次配置完成: ${flags.isFirstProvisioningCompleted}")
            Logger.provI("   Logo下载成功: ${flags.isLogoDownloadSuccess}")
            Logger.provI("   开机动画下载成功: ${flags.isBootAnimationDownloadSuccess}")
            Logger.provI("   最后配置时间: ${if (flags.lastProvisioningTime > 0) java.util.Date(flags.lastProvisioningTime) else "未配置"}")
            Logger.provI("   最后配置CID: ${flags.lastConfigCid.ifEmpty { "无" }}")

            // 计算距离上次配置的时间
            if (flags.lastProvisioningTime > 0) {
                val timeDiff = System.currentTimeMillis() - flags.lastProvisioningTime
                val hours = timeDiff / (1000 * 60 * 60)
                val minutes = (timeDiff % (1000 * 60 * 60)) / (1000 * 60)
                Logger.provI("   距离上次配置: ${hours}小时${minutes}分钟")
            }

        } catch (e: Exception) {
            Logger.provE("打印Provisioning状态失败", e)
        }
    }

    /**
     * 检查是否有有效的配置文件
     */
    private fun hasValidConfig(): Boolean {
        return try {
            val configFile = File(ProvisionConfig.getProvisioningConfigFilePath())
            if (!configFile.exists()) {
                return false
            }

            val content = configFile.readText()
            val jsonObject = JSONObject(content)

            // 检查基本结构
            jsonObject.has("data") &&
            jsonObject.getJSONObject("data").has("customization") &&
            jsonObject.getJSONObject("data").has("system")
        } catch (e: Exception) {
            Logger.prov("配置文件检查失败: ${e.message}")
            false
        }
    }



    /**
     * 计算文件MD5值
     */
    private fun calculateFileMd5(file: File): String? {
        return try {
            val digest = java.security.MessageDigest.getInstance("MD5")
            file.inputStream().use { inputStream ->
                val buffer = ByteArray(8192)
                var bytesRead: Int
                while (inputStream.read(buffer).also { bytesRead = it } != -1) {
                    digest.update(buffer, 0, bytesRead)
                }
            }
            digest.digest().joinToString("") { "%02x".format(it) }
        } catch (e: Exception) {
            Logger.provE("计算文件MD5失败: ${file.absolutePath}", e)
            null
        }
    }
}
