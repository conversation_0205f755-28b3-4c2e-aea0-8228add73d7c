<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:background="@drawable/float_window_background"
    android:elevation="8dp"
    android:gravity="center"
    android:orientation="horizontal"
    android:padding="12dp">

    <ImageView
        android:id="@+id/iv_float_icon"
        android:layout_width="20dp"
        android:layout_height="20dp"
        android:src="@drawable/ic_reboot_warning"
        app:tint="#FF0000" />

    <TextView
        android:id="@+id/tv_reboot_message"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="8dp"
        android:gravity="center"
        android:text="系统将在 60 秒后重启"
        android:textColor="#ffffff"
        android:textSize="14sp"
        android:textStyle="bold" />

</LinearLayout>
