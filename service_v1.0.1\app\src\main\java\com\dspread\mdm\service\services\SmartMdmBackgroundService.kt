package com.dspread.mdm.service.services

import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.Service
import android.content.Context
import android.content.Intent
import android.os.Handler
import android.os.Looper
import android.os.Build
import android.os.IBinder
import androidx.core.app.NotificationCompat
import com.dspread.mdm.service.R
import com.dspread.mdm.service.utils.log.Logger
import com.dspread.mdm.service.network.websocket.WebSocketCenter
import com.dspread.mdm.service.broadcast.handlers.system.NetworkEventHandlerImpl
import com.dspread.mdm.service.broadcast.handlers.system.BatteryEventHandlerImpl
import com.dspread.mdm.service.broadcast.handlers.system.SystemEventHandlerImpl
import com.dspread.mdm.service.broadcast.handlers.system.ScreenEventHandlerImpl
import com.dspread.mdm.service.broadcast.handlers.system.WakeLockEventHandlerImpl
import com.dspread.mdm.service.broadcast.handlers.system.ProvisioningEventHandler
import com.dspread.mdm.service.config.TimerConfig
import com.dspread.mdm.service.broadcast.handlers.websocket.TerminalInfoEventHandlerImpl
import com.dspread.mdm.service.broadcast.handlers.service.ServiceGuardEventHandler
import com.dspread.mdm.service.broadcast.handlers.service.ServiceManagementEventHandlerImpl
import com.dspread.mdm.service.broadcast.handlers.system.PackageUpdateEventHandlerImpl
import com.dspread.mdm.service.broadcast.handlers.websocket.TaskExecuteEventHandlerImpl
import com.dspread.mdm.service.modules.ModuleManagerRegistry
import com.dspread.mdm.service.modules.provisioning.ProvisioningManager
import com.dspread.mdm.service.modules.provisioning.model.ProvisioningTrigger as ProvisioningTriggerType
import com.dspread.mdm.service.modules.provisioning.model.ProvisioningResult
import com.dspread.mdm.service.modules.provisioning.model.ProvisioningStatus
import java.util.concurrent.Executors
import java.util.concurrent.ScheduledExecutorService
import java.util.concurrent.TimeUnit
import com.dspread.mdm.service.constants.Constants
import com.dspread.mdm.service.modules.geofence.location.GpsLocationManager
import com.dspread.mdm.service.platform.manager.WakeLockManager
import com.dspread.mdm.service.broadcast.core.BroadcastManager
import com.dspread.mdm.service.broadcast.handlers.websocket.HeartbeatEventHandlerImpl
import com.dspread.mdm.service.utils.storage.PreferencesManager
import kotlinx.coroutines.*
import org.json.JSONObject

/**
 * Smart MDM 后台服务
 */
class SmartMdmBackgroundService : Service() {
    companion object {
        private const val TAG = "[SmartMdmBackgroundService] "
    }

    private val backgroundExecutor: ScheduledExecutorService = Executors.newScheduledThreadPool(2)
    private val mainHandler = Handler(Looper.getMainLooper())

    @Volatile
    private var isCoreComponentsInitialized = false

    override fun onCreate() {
        super.onCreate()
        Logger.serve(TAG + "服务启动")

        // 创建通知渠道并启动前台服务
        createNotificationChannel()
        startForeground(Constants.ServiceConstants.NOTIFICATION_ID, createNotification())

        // 初始化服务组件
        initializeService()

        Logger.serve(TAG + "服务创建完成")
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        Logger.serve(TAG + "onStartCommand调用")

        // 获取智能WakeLock，确保服务稳定运行
        WakeLockManager.acquireSmartWakeLock(this, Constants.ServiceConstants.WAKELOCK_INITIAL_TIMEOUT)

        // 返回START_STICKY确保服务被系统杀死后会重启
        return START_STICKY
    }

    override fun onTaskRemoved(rootIntent: Intent?) {
        super.onTaskRemoved(rootIntent)
        val restartServiceIntent = Intent(applicationContext, this::class.java)
        startService(restartServiceIntent)
    }

    override fun onBind(intent: Intent?): IBinder? = null

    override fun onDestroy() {
        super.onDestroy()
        Logger.serve(TAG + "服务销毁开始")

        // 释放WakeLock
        WakeLockManager.releasePersistentWakeLock(this)

        // 释放DSPREAD服务连接
        try {
            DspreadService.release(this)
            Logger.serve(TAG + "DSPREAD服务连接已释放")
        } catch (e: Exception) {
            Logger.serveE(TAG + "释放DSPREAD服务失败", e)
        }

        // 关闭线程池
        try {
            backgroundExecutor.shutdown()
            if (!backgroundExecutor.awaitTermination(5, TimeUnit.SECONDS)) {
                backgroundExecutor.shutdownNow()
            }
            Logger.serve(TAG + "线程池已关闭")
        } catch (e: Exception) {
            Logger.serveE(TAG + "关闭线程池失败", e)
            backgroundExecutor.shutdownNow()
        }

        cleanupResources()
        Logger.serve(TAG + "服务销毁完成")
    }

    /**
     * 初始化服务
     * Android 14+: 先初始化DSPREAD服务，等待完成后启动Provisioning
     * Android < 14: 直接启动Provisioning服务
     */
    private fun initializeService() {
        try {
            Logger.serve(TAG + "开始服务初始化")

            // 第一阶段：启动关键服务
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE) {
                Logger.serve(TAG + "Android 14+: 初始化DSPREAD服务")
                initializeDspreadServiceAsync()
            } else {
                Logger.serve(TAG + "Android < 14: 启动Provisioning服务")
                startProvisioningServiceAsync()
            }

            // 第二阶段：启动基础组件
            mainHandler.post {
                try {
                    Logger.serve(TAG + "初始化基础组件")
                    initializeBasicComponents()
                    Logger.serve(TAG + "服务初始化完成")
                } catch (e: Exception) {
                    Logger.serveE(TAG + "基础组件初始化失败", e)
                }
            }
        } catch (e: Exception) {
            Logger.serveE(TAG + "服务初始化失败", e)
        }
    }

    /**
     * 启动Provisioning服务
     * 异步处理，不阻塞主线程
     */
    private fun startProvisioningService() {
        try {
            Logger.serve(TAG + "启动ProvisioningService")

            val provisioningManager = ProvisioningManager.getInstance(this)
            provisioningManager.executeProvisioning(
                configUrl = Constants.ServiceConstants.DEFAULT_PROVISIONING_CONFIG_URL,
                trigger = ProvisioningTriggerType.FIRST_BOOT,
                callback = object : ProvisioningManager.ProvisioningCallback {
                    override fun onProgress(result: ProvisioningResult) {
                        Logger.serve(TAG + "Provisioning进度: ${result.message}")
                    }

                    override fun onCompleted(result: ProvisioningResult) {
                        Logger.serve(TAG + "Provisioning完成，启动核心组件")
                        mainHandler.post {
                            try {
                                if (result.status == ProvisioningStatus.COMPLETED) {
                                    initializeCoreComponents()
                                } else {
                                    Logger.serveW(TAG + "Provisioning状态异常: ${result.status}")
                                }
                            } catch (e: Exception) {
                                Logger.serveE(TAG + "核心组件启动失败", e)
                            }
                        }
                    }

                    override fun onError(result: ProvisioningResult) {
                        Logger.serveE(TAG + "Provisioning失败: ${result.message}，使用降级配置")
                        mainHandler.post {
                            try {
                                initializeCoreComponents()
                            } catch (e: Exception) {
                                Logger.serveE(TAG + "核心组件启动失败", e)
                            }
                        }
                    }
                }
            )

            // 注意：Provisioning定时器现在由ProvisioningEventHandler管理

            Logger.serve(TAG + "ProvisioningService启动完成")

        } catch (e: Exception) {
            Logger.serveE(TAG + "ProvisioningService启动失败", e)
        }
    }

    private fun initializeBasicComponents() {
        try {
            initializeBroadcastManager()
            initializeGeofenceProfile()
        } catch (e: Exception) {
            Logger.serveE(TAG + "基础组件初始化失败", e)
        }
    }

    /**
     * 异步初始化DSPREAD服务（Android 14+）
     * 使用线程池执行，避免阻塞主线程
     */
    private fun initializeDspreadServiceAsync() {
        backgroundExecutor.execute {
            try {
                Logger.serve(TAG + "后台线程：初始化DSPREAD服务")
                DspreadService.initialize(this@SmartMdmBackgroundService)

                waitForDspreadServiceReady()
            } catch (e: Exception) {
                Logger.serveE(TAG + "DSPREAD服务初始化失败", e)
                startProvisioningServiceAsync()
            }
        }
    }

    private fun startProvisioningServiceAsync() {
        backgroundExecutor.execute {
            try {
                startProvisioningService()
            } catch (e: Exception) {
                Logger.serveE(TAG + "ProvisioningService启动失败", e)
            }
        }
    }

    /**
     * 等待DSPREAD服务绑定完成
     * 最多等待10秒，超时后使用备用方案
     */
    private fun waitForDspreadServiceReady() {
        var attempts = 0
        val maxAttempts = 10

        val checkTask = object : Runnable {
            override fun run() {
                attempts++
                val devServiceReady = DspreadService.isDeviceServiceAvailable()

                Logger.serve(TAG + "等待DSPREAD服务就绪 ($attempts/$maxAttempts): $devServiceReady")

                if (devServiceReady) {
                    Logger.serve(TAG + "DSPREAD服务就绪，启动Provisioning")
                    testDspreadServiceAfterInit()
                    startProvisioningServiceAsync()
                } else if (attempts >= maxAttempts) {
                    Logger.serve(TAG + "DSPREAD服务等待超时，使用备用方案")
                    startProvisioningServiceAsync()
                } else {
                    backgroundExecutor.schedule(this, 1, TimeUnit.SECONDS)
                }
            }
        }
        backgroundExecutor.schedule(checkTask, 1, TimeUnit.SECONDS)
    }

    /**
     * 验证DSPREAD服务功能
     * 测试获取设备序列号等关键功能
     */
    private fun testDspreadServiceAfterInit() {
        try {
            Logger.serve(TAG + "验证DSPREAD服务功能")

            // 测试获取设备序列号（Provisioning需要用到）
            val sn = DspreadService.getDeviceSerialNumber()
            Logger.serve(TAG + "设备序列号验证: ${if (sn.isNotEmpty()) "成功($sn)" else "失败"}")

            // 检查服务状态
            val sysServiceAvailable = DspreadService.isSysServiceAvailable()
            val devServiceAvailable = DspreadService.isDeviceServiceAvailable()
            Logger.serve(TAG + "服务状态 - 系统服务:${if (sysServiceAvailable) "✅" else "❌"} 设备服务:${if (devServiceAvailable) "✅" else "❌"}")

        } catch (e: Exception) {
            Logger.serveE(TAG + "DSPREAD服务验证失败", e)
        }
    }

    /**
     * 初始化核心组件（在Provisioning完成后调用）
     * 包含WebSocket、模块管理器等需要配置的组件
     */
    private fun initializeCoreComponents() {
        if (isCoreComponentsInitialized) {
            Logger.serve(TAG + "核心组件已初始化，跳过重复调用")
            return
        }

        try {
            Logger.serve(TAG + "开始初始化核心组件")

            // 1. 使用Provisioning配置初始化WebSocket
            initializeWebSocketWithConfig()

            // 2. 初始化模块管理器
            initializeModuleRegistry()

            // 3. 启动Provisioning定时器
            startProvisioningTimer()

            // 4. 打印当前定时器配置
            printTimerConfig()

            isCoreComponentsInitialized = true
            Logger.serve(TAG + "核心组件初始化完成")

        } catch (e: Exception) {
            Logger.serveE(TAG + "核心组件初始化失败", e)
        }
    }

    private fun initializeWebSocketWithConfig() {
        try {
            val provisioningManager = ProvisioningManager.getInstance(this)
            val config = provisioningManager.getCurrentConfig()

            if (config != null) {
                val sharedPreferences = getSharedPreferences("smart_mdm_config", Context.MODE_PRIVATE)
                sharedPreferences.edit()
                    .putString("websocket_url", config.polling.statusApiUrl)
                    .putString("remote_websocket_url", config.polling.remoteUrl)
                    .apply()
            }

            initializeWebSocket()

        } catch (e: Exception) {
            Logger.serveE(TAG + "WebSocket初始化失败", e)
        }
    }

    private fun initializeWebSocket() {
        try {
            WebSocketCenter.init(this)
            WebSocketCenter.connect()
        } catch (e: Exception) {
            Logger.serveE(TAG + "WebSocket初始化失败", e)
        }
    }

    private fun initializeBroadcastManager() {
        try {
            BroadcastManager.initialize(this)
            val networkHandler = NetworkEventHandlerImpl()
            BroadcastManager.registerEventHandler(networkHandler)
            val batteryHandler = BatteryEventHandlerImpl()
            BroadcastManager.registerEventHandler(batteryHandler)
            val systemHandler = SystemEventHandlerImpl()
            BroadcastManager.registerEventHandler(systemHandler)
            val screenHandler = ScreenEventHandlerImpl()
            BroadcastManager.registerEventHandler(screenHandler)
            val heartbeatHandler = HeartbeatEventHandlerImpl()
            BroadcastManager.registerEventHandler(heartbeatHandler)
            val packageUpdateHandler = PackageUpdateEventHandlerImpl()
            BroadcastManager.registerEventHandler(packageUpdateHandler)
            val taskExecuteHandler = TaskExecuteEventHandlerImpl()
            BroadcastManager.registerEventHandler(taskExecuteHandler)
            val terminalInfoHandler = TerminalInfoEventHandlerImpl()
            BroadcastManager.registerEventHandler(terminalInfoHandler)
            val serviceGuardHandler = ServiceGuardEventHandler()
            BroadcastManager.registerEventHandler(serviceGuardHandler)
            val serviceManagementHandler = ServiceManagementEventHandlerImpl()
            BroadcastManager.registerEventHandler(serviceManagementHandler)
            val wakeLockHandler = WakeLockEventHandlerImpl()
            BroadcastManager.registerEventHandler(wakeLockHandler)
            val provisioningHandler = ProvisioningEventHandler()
            BroadcastManager.registerEventHandler(provisioningHandler)
        } catch (e: Exception) {
            Logger.serveE(TAG + "广播管理器初始化失败", e)
        }
    }

    /**
     * 启动Provisioning定时器
     */
    private fun startProvisioningTimer() {
        try {
            val provisioningHandler = ProvisioningEventHandler()
            provisioningHandler.startProvisioningTimer(this)
            Logger.serve(TAG + "Provisioning定时器启动成功")
        } catch (e: Exception) {
            Logger.serveE(TAG + "启动Provisioning定时器失败", e)
        }
    }

    /**
     * 打印当前定时器配置
     */
    private fun printTimerConfig() {
        try {
            TimerConfig.printCurrentConfig(this)
        } catch (e: Exception) {
            Logger.serveE(TAG + "打印定时器配置失败", e)
        }
    }

    private fun cleanupResources() {
        try {
            BroadcastManager.cleanup(this)
            ModuleManagerRegistry.cleanup()
        } catch (e: Exception) {
            Logger.serveE(TAG + "资源清理失败", e)
        }
    }

    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                Constants.ServiceConstants.CHANNEL_ID,
                Constants.ServiceConstants.CHANNEL_NAME,
                NotificationManager.IMPORTANCE_LOW
            ).apply {
                description = Constants.ServiceConstants.CHANNEL_DESCRIPTION
                setShowBadge(false)
            }
            val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            notificationManager.createNotificationChannel(channel)
        }
    }

    private fun createNotification(): Notification {
        return NotificationCompat.Builder(this, Constants.ServiceConstants.CHANNEL_ID)
            .setContentTitle(Constants.ServiceConstants.NOTIFICATION_TITLE)
            .setContentText(Constants.ServiceConstants.NOTIFICATION_TEXT)
            .setSmallIcon(R.drawable.ic_launcher_foreground)
            .setAutoCancel(false)
            .setOngoing(true)
            .build()
    }

    private fun initializeModuleRegistry() {
        try {
            ModuleManagerRegistry.initialize(this)
            
            // 初始化RuleBaseManager
            try {
                val ruleBaseManager = com.dspread.mdm.service.modules.rulebase.RuleBaseManager.getInstance(this)
                ruleBaseManager.initialize()
                Logger.serve(TAG + "RuleBaseManager初始化成功")
            } catch (e: Exception) {
                Logger.serveE(TAG + "RuleBaseManager初始化失败", e)
            }
            
            GlobalScope.launch {
                try {
                    ModuleManagerRegistry.startAllModules()
                } catch (e: Exception) {
                    Logger.serveE(TAG + "启动模块失败", e)
                }
            }
        } catch (e: Exception) {
            Logger.serveE(TAG + "模块管理器初始化失败", e)
        }
    }

    private fun initializeGeofenceProfile() {
        val geofencePrefs = getSharedPreferences("geofence_state", MODE_PRIVATE)
        Constants.geofenceStatus = geofencePrefs.getInt("geofence_status", GpsLocationManager.IN_ZONE)
        Constants.storeId = geofencePrefs.getString("store_id", "") ?: ""
        Constants.storeSsid = geofencePrefs.getString("store_ssid", "") ?: ""
        Constants.storeIp = geofencePrefs.getString("store_ip", "") ?: ""

        try {
            val validDistanceStr = PreferencesManager.getString("valid_distance", "500")
            if (validDistanceStr.all { it.isDigit() }) {
                GpsLocationManager.validErrorDistance = validDistanceStr.toFloatOrNull() ?: 500f
            } else {
                GpsLocationManager.validErrorDistance = 500f
            }
        } catch (e: Exception) {
            GpsLocationManager.validErrorDistance = 500f
        }

        try {
            val profile = geofencePrefs.getString("geofence_current_profile", "") ?: ""
            if (profile.isNotEmpty()) {
                val profileJson = JSONObject(profile)
                GpsLocationManager.initGeoProfile(this, profileJson)
            } else {
                GpsLocationManager.initGeoProfile(this, null)
            }
        } catch (e: Exception) {
            Logger.serveE(TAG + "地理围栏Profile初始化失败", e)
        }
    }
}
