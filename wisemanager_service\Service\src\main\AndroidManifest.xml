<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.bbpos.wiseapp.service"
    android:sharedUserId="android.uid.system">

    <uses-permission android:name="android.permission.CAMERA"/>
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" /> <!-- 用于提高GPS定位速度 -->
    <uses-permission android:name="android.permission.ACCESS_LOCATION_EXTRA_COMMANDS" />
    <uses-permission android:name="android.permission.ACCESS_NOTIFICATION_POLICY" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" /> <!-- 用于访问wifi网络信息，wifi信息会用于进行网络定位 -->
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.BIND_CARRIER_SERVICE" />
    <uses-permission android:name="android.permission.BLUETOOTH" />
    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />
    <uses-permission android:name="android.permission.BLUETOOTH_PRIVILEGED" />
    <uses-permission android:name="android.permission.BROADCAST_STICKY" />
    <uses-permission android:name="android.permission.BODY_SENSORS" />
    <uses-permission android:name="android.permission.CALL_PHONE" />
    <uses-permission android:name="android.permission.CHANGE_COMPONENT_ENABLED_STATE" />
    <uses-permission android:name="android.permission.CHANGE_NETWORK_STATE" />
    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />
    <uses-permission android:name="android.permission.CHANGE_WIFI_MULTICAST_STATE" />
    <uses-permission android:name="android.permission.DELETE_PACKAGES" />
    <uses-permission android:name="android.permission.DOWNLOAD_WITHOUT_NOTIFICATION" />
    <uses-permission android:name="android.permission.EXPAND_STATUS_BAR" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.FORCE_STOP_PACKAGES" />
    <uses-permission android:name="android.permission.GET_ACCOUNTS" />
    <uses-permission android:name="android.permission.GET_PACKAGE_SIZE" />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.INSTALL_PACKAGES" />
    <uses-permission android:name="android.permission.INSTALL_SHORTCUT" />
    <uses-permission android:name="android.permission.INTERACT_ACROSS_USERS_FULL" />
    <uses-permission android:name="android.permission.KILL_BACKGROUND_PROCESSES" />
    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
    <uses-permission android:name="android.permission.MODIFY_PHONE_STATE" />
    <uses-permission android:name="android.permission.MOUNT_UNMOUNT_FILESYSTEMS"/>
    <uses-permission android:name="android.permission.NFC" />
    <uses-permission android:name="android.permission.PROCESS_OUTGOING_CALLS" />
    <uses-permission android:name="android.permission.READ_CALENDAR" />
    <uses-permission android:name="android.permission.READ_CALL_LOG" />
    <uses-permission android:name="android.permission.READ_CONTACTS" />
    <uses-permission android:name="android.permission.READ_LOGS" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
    <uses-permission android:name="android.permission.READ_SMS" />
    <uses-permission android:name="android.permission.READ_SYNC_SETTINGS" />
    <uses-permission android:name="android.permission.READ_SYNC_STATS" />
    <uses-permission android:name="android.permission.RECEIVE_MMS" />
    <uses-permission android:name="android.permission.RECEIVE_SMS" />
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
    <uses-permission android:name="android.permission.RECOVERY" />
    <uses-permission android:name="android.permission.RECORD_AUDIO" />
    <uses-permission android:name="android.permission.REORDER_TASKS" />
    <uses-permission android:name="android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS" />
    <uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES" />
    <uses-permission android:name="android.permission.SEND_SMS" />
    <uses-permission android:name="android.permission.SET_TIME" />
    <uses-permission android:name="android.permission.SET_TIME_ZONE" />
    <uses-permission android:name="android.permission.SET_WALLPAPER" />
    <uses-permission android:name="android.permission.SET_WALLPAPER_HINTS" />
    <uses-permission android:name="android.permission.SHUTDOWN" />
    <uses-permission android:name="android.permission.STATUS_BAR"/>
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
    <uses-permission android:name="android.permission.UNINSTALL_SHORTCUT" />
    <uses-permission android:name="android.permission.VIBRATE" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.WRITE_MEDIA_STORAGE" />
    <uses-permission android:name="android.permission.WRITE_SETTINGS" />
    <uses-permission android:name="android.permission.WRITE_SECURE_SETTINGS" />
    <uses-permission android:name="android.permission.WRITE_APN_SETTINGS" />
    <uses-permission android:name="android.permission.BBPOS" />

    <!--三方广播-->
    <uses-permission android:name="com.bbpos.wiseapp.permissions.MY_BROADCAST" />
    <permission
        android:name="com.bbpos.wiseapp.permissions.MY_BROADCAST"
        android:protectionLevel="signature" >
    </permission>

    <application
        android:name="com.bbpos.wiseapp.tms.utils.ContextUtil"
        android:allowBackup="false"
        android:configChanges="locale|layoutDirection"
        android:hardwareAccelerated="true"
        android:icon="@drawable/ic_logo"
        android:label="@string/app_name"
        android:largeHeap="true"
        android:networkSecurityConfig="@xml/network_security_config"
        android:supportsRtl="true"
        android:theme="@style/AppTheme">

<!--        <activity
            android:name="com.bbpos.TestActivity"
            android:icon="@drawable/ic_launcher_setting"
            android:label="@string/settings"
            android:roundIcon="@drawable/ic_launcher_setting">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>-->


        <activity
            android:name="com.bbpos.wiseapp.settings.activity.SettingActivity"
            android:label="@string/settings"
            android:icon="@drawable/ic_launcher_setting"
            android:roundIcon="@drawable/ic_launcher_setting"
            android:excludeFromRecents="true">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <activity android:name="com.mining.app.zxing.activity.MipcaActivityCapture"
            android:label="@string/wise_scan"
            android:icon="@drawable/ic_launcher_wisescan"
            android:roundIcon="@drawable/ic_launcher_wisescan"
            android:screenOrientation="portrait"
            android:excludeFromRecents="true">
            <intent-filter>
                <action android:name="android.intent.action.MAIN"/>
                <category android:name="android.intent.category.LAUNCHER"/>
            </intent-filter>
        </activity>
        <activity
            android:name="com.bbpos.wiseapp.settings.activity.LanguageActivity"
            android:configChanges="locale|layoutDirection" />
        <activity
            android:name="com.bbpos.wiseapp.settings.activity.WiFiActivity"
            android:enabled="true"
            android:exported="false" />
        <activity
            android:name="com.bbpos.wiseapp.settings.activity.WiFiDialogActivity"
            android:exported="true"
            android:theme="@android:style/Theme.Translucent" />
        <activity android:name="com.bbpos.wiseapp.settings.activity.BluetoothActivity" />

        <activity android:name="com.bbpos.wiseapp.settings.activity.ProvisionUIActivity"
            android:enabled="true"
            android:exported="false" />

        <activity android:name="com.bbpos.wiseapp.activity.CustomConfigActivity"
            android:enabled="true"
            android:exported="false" />

        <activity android:name="com.bbpos.wiseapp.activity.LockScreenActivity"
            android:theme="@style/no_statusbar_activity"
            android:enabled="true"
            android:exported="true"
            android:screenOrientation="portrait"
            android:excludeFromRecents="true"
            android:launchMode="singleTask">
            <intent-filter android:priority="-100">
                <action android:name="android.intent.action.MAIN"/>
                <category android:name="android.intent.category.HOME"/>
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>

        <receiver android:name="com.bbpos.wiseapp.provisioning.ProvisionTimer"
            android:exported="false">
            <intent-filter>
                <action android:name="com.bbpos.wiseapp.tms.PROVISION_TIMER_START_BC" />
                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
                <action android:name="com.bbpos.wiseapp.wisescan.SCAN_CODE" />
            </intent-filter>
        </receiver>
        <receiver
            android:name="com.bbpos.wiseapp.tms.timer.PollTimer"
            android:exported="false">
            <intent-filter>
                <action android:name="com.bbpos.wiseapp.tms.POLL_TIMER_START_BC" />
                <action android:name="com.bbpos.wiseapp.tms.BLE_SCAN_TIMER_START_BC" />
                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
            </intent-filter>
        </receiver>
        <receiver
            android:name="com.bbpos.wiseapp.tms.timer.WiseLogUploadTimer"
            android:exported="false">
            <intent-filter>
                <action android:name="com.bbpos.wiseapp.tms.WISE_LOG_UPLOAD_BC" />
            </intent-filter>
        </receiver>
        <receiver
            android:name="com.bbpos.wiseapp.tms.timer.HardwareDetectTimer"
            android:exported="false">
            <intent-filter>
                <action android:name="com.bbpos.wiseapp.tms.HARDWARE_DETECT_UPLOAD_BC" />
            </intent-filter>
        </receiver>
        <receiver android:name="com.bbpos.wiseapp.tms.timer.DataCollectTimer">
            <intent-filter>
                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
            </intent-filter>
        </receiver>
        <receiver android:name="com.bbpos.wiseapp.tms.receiver.TmsReceiver">
            <intent-filter>
                <action android:name="com.bbpos.wiseapp.tms.GPS_SCHEDULE_TIME" />
            </intent-filter>
        </receiver>
        <receiver
            android:name="com.bbpos.wiseapp.tms.timer.TerminalInfoUploadTimer"
            android:exported="false">
            <intent-filter>
                <action android:name="com.bbpos.wiseapp.tms.TER_INFO_UPLOAD_BC" />
                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
            </intent-filter>
        </receiver>
        <receiver android:name=".receiver.CloudReceiver">
            <intent-filter>
                <action android:name="android.intent.action.ACTION_SHUTDOWN" />
                <action android:name="android.intent.action.BOOT_COMPLETED" />
                <action android:name="android.net.wifi.WIFI_STATE_CHANGED" />
                <action android:name="com.bbpos.wiseapp.service.ACTION_EXPIRE_REBOOT" />
            </intent-filter>
        </receiver>

        <receiver android:name=".receiver.OTAUpdateReceiver"
            android:exported="true"
            android:enabled="true">
            <intent-filter>
                <action android:name="android.intent.action.BOOT_COMPLETED" />
                <action android:name="com.bbpos.wiseapp.wisepos.atcion.OTA.COMPLETED" />
                <action android:name="com.bbpos.wiseapp.atcion.OTA.COMPLETED" />
                <action android:name="com.bbpos.wiseapp.atcion.SP.COMPLETED" />
                <action android:name="com.bbpos.wiseapp.atcion.TMT.COMPLETED" />
                <action android:name="com.bbpos.wiseapp.atcion.KEYUPDATE.COMPLETED" />
                <action android:name="com.bbpos.wiseapp.launcher.ACTION_WISEPOS_OTA_UPDATE" />
                <action android:name="com.bbpos.wiseapp.launcher.ACTION_OTA_UPDATE" />
                <action android:name="com.bbpos.wiseapp.launcher.ACTION_SP_UPDATE" />
                <action android:name="com.bbpos.wiseapp.launcher.ACTION_TMT_UPDATE" />
                <action android:name="com.bbpos.wiseapp.launcher.ACTION_KEY_UPDATE" />

                <!--<action android:name="com.bbpos.wiseapp.service.ACTION_TODO_OTAUPGRADE" />-->
            </intent-filter>
        </receiver>
        <receiver
            android:name=".receiver.FallReciver">
            <intent-filter >
                <action android:name="kphone.intent.action.FALL_FREE"/>
            </intent-filter>
        </receiver>

        <!--<receiver android:name="com.bbpos.wiseapp.tms.receiver.SystemReceiver"-->
            <!--android:exported="true"-->
            <!--android:enabled="true">-->
            <!--<intent-filter>-->
                <!--<action android:name="prepare_to_factory_reset" />-->
            <!--</intent-filter>-->
        <!--</receiver>-->

        <service
            android:name="com.bbpos.wiseapp.websocket.InitialProcessService"
            android:enabled="true"
            android:exported="false" />
        <service
            android:name="com.bbpos.wiseapp.websocket.RulebasedService"
            android:enabled="true"
            android:exported="false" />
        <service
            android:name="com.bbpos.wiseapp.tms.service.WSTaskApkUpdateService"
            android:enabled="true"
            android:exported="false" />
        <service
            android:name="com.bbpos.wiseapp.tms.service.WSTaskOsUpdateService"
            android:enabled="true"
            android:exported="false" />
        <service
            android:name="com.bbpos.wiseapp.tms.service.WSTaskResetUnboxService"
            android:enabled="true"
            android:exported="false" />
        <service
            android:name="com.bbpos.wiseapp.tms.service.LockDeskService"
            android:enabled="true"
            android:exported="false" />
        <service
            android:name="com.bbpos.wiseapp.tms.service.WSTaskApkUninstallService"
            android:enabled="true"
            android:exported="false" />
        <service
            android:name="com.bbpos.wiseapp.tms.service.WSTaskUpdateParamService"
            android:enabled="true"
            android:exported="false" />
        <service
            android:name="com.bbpos.wiseapp.tms.service.WSTaskApkExpireUninstallService"
            android:enabled="true"
            android:exported="false" />
        <service
            android:name="com.bbpos.wiseapp.tms.service.ParamUpdateService"
            android:enabled="true"
            android:exported="false" />
        <service
            android:name="com.bbpos.wiseapp.tms.service.TerminalInfoUploadSerivce"
            android:enabled="true"
            android:exported="false" />
        <service
            android:name="com.bbpos.wiseapp.param.manager.ParamParseService"
            android:enabled="true"
            android:exported="false" />
        <service
            android:name="com.bbpos.wiseapp.provisioning.ProvisionService"
            android:enabled="true"
            android:exported="false"/>
        <service
            android:name="com.bbpos.wiseapp.tms.service.WiseLogUploadSerivce"
            android:enabled="true"
            android:exported="false"/>
        <service
            android:name="com.bbpos.wiseapp.provisioning.ProvisionDownloadService"
            android:enabled="true"
            android:exported="false"/>
        <service
            android:name="com.bbpos.wiseapp.tms.service.InitializeService"
            android:enabled="true"
            android:exported="false" />
        <service
            android:name="com.bbpos.wiseapp.tms.traffic.DataCollectService"
            android:enabled="true"
            android:exported="false" />
        <service
            android:name="com.bbpos.wiseapp.tms.traffic.DataProvideService"
            android:enabled="true"
            android:exported="false" />
        <service
            android:name=".CloudService"
            android:enabled="true">
            <intent-filter>
                <action android:name="com.bbpos.wiseapp.SDK_MANAGE" />

                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </service>
        <service
            android:name=".appdata.DataCollectService"
            android:enabled="true" />
        <service
            android:name="com.bbpos.wiseapp.websocket.OTANotifyService"
            android:enabled="true"
            android:exported="false" />
	    <service
            android:name="com.bbpos.wiseapp.websocket.WifiProfileProcessService"
            android:enabled="true"
            android:exported="false" />
        <service android:name="com.bbpos.wiseapp.logstream.LogService"
            android:enabled="true"
            android:exported="false" />
        <service android:name="com.bbpos.wiseapp.remoteviewer.ViewService"
            android:enabled="true"
            android:exported="false" />
        <service
            android:name="com.bbpos.wiseapp.tms.service.DownloadService"
            android:enabled="true"
            android:exported="false" />
        <service
            android:name="com.bbpos.wiseapp.tms.service.HardwareDetectUploadSerivce"
            android:enabled="true"
            android:exported="false" />
        <!--
        <service
            android:name="com.bbpos.wiseapp.service.LocalService"
            android:enabled="true"
            android:exported="false" />
        <service
            android:name="com.bbpos.wiseapp.service.RemoteGuardService"
            android:enabled="true"
            android:exported="false"/>
        -->
        <service
            android:name=".ListeningService"
            android:permission="android.permission.BIND_ACCESSIBILITY_SERVICE">
            <intent-filter>
                <action android:name="android.accessibilityservice.AccessibilityService" />
            </intent-filter>
            <meta-data
                android:name="android.accessibilityservice"
                android:resource="@xml/accessibilityservice"/>
        </service>

        <service android:name="com.bbpos.wiseapp.websocket.DeviceBidUploadService"
            android:enabled="true"
            android:exported="false"/>

        <provider
            android:name=".contentprovider.CommonDataProvider"
            android:authorities="com.bbpos.wiseapp.service.contentprovider.CommonDataProvider"
            android:exported="true" />
        <provider
            android:name=".contentprovider.ParamContentProvider"
            android:authorities="com.bbpos.wiseapp.service.inapp.provider"
            android:exported="true" />
        <!--<provider-->
            <!--android:name=".contentprovider.ParamPathContentProvider"-->
            <!--android:authorities="com.bbpos.wiseapp.service.contentprovider.ParamPathContentProvider"-->
            <!--android:exported="true" /> &lt;!&ndash; FileProvider配置访问路径，适配7.0及其以上 &ndash;&gt;-->
        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="com.bbpos.wiseapp.service.fileprovider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_paths" />
        </provider> <!-- WEBSOCKET -->

        <service android:name="com.bbpos.wiseapp.websocket.WebSocketService"
            android:enabled="true"
            android:exported="false"/>

        <receiver android:name="com.bbpos.wiseapp.websocket.WebSocketNetworkChangedReceiver">
            <intent-filter>
                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
            </intent-filter>
        </receiver>

<!--
        <receiver android:name="com.bbpos.wiseapp.service.receiver.SecurityTamperReceiver"
            android:exported="true"
            android:enabled="true">
            <intent-filter>
                <action android:name="com.bbpos.bbdevice.ERROR_NOTIFICATION"/>
                <data android:scheme="package" />
            </intent-filter>
        </receiver>
-->

        <receiver
            android:name="com.bbpos.wiseapp.websocket.WebSocketReceiver"
            android:exported="true">
            <intent-filter>
                <action android:name="com.bbpos.wiseapp.service.WSTASK_UPDATED_BC" />
                <action android:name="com.bbpos.wiseapp.service.RULEBASED_EXEC_BC" />
                <action android:name="com.bbpos.wiseapp.service.WSTASK_EXEC_BC" />
                <action android:name="com.bbpos.intent.WIFI_FOUND" />
                <action android:name="com.bbpos.intent.LOGIN_SUCCESS" />
                <action android:name="com.bbpos.intent.LOGIN_FAILED" />
                <action android:name="com.bbpos.intent.INIT_READY" />
                <action android:name="com.bbpos.intent.STORE_SELECTED" />
                <action android:name="com.bbpos.intent.STORE_FOUND" />
                <action android:name="com.bbpos.intent.RESTART_DEVICE" />
                <action android:name="com.bbpos.intent.UNBIND_COMPLETE" />
                <action android:name="com.bbpos.intent.DELETE_TEST_WIFI" />
                <action android:name="com.bbpos.intent.SET_DEFAULT_LAUNCHER" />
                <action android:name="com.bbpos.wiseapp.service.RULEBASED_DOWNLOAD_COMPLETED" />
                <action android:name="com.bbpos.wiseapp.service.RULEBASED_START_INSTALL" />
                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
                <action android:name="com.bbpos.wiseapp.service.APP_PLUS_DOWNLOAD_COMPLETED" />
            </intent-filter>
        </receiver>

        <receiver android:name=".receiver.WifiControlReceiver"
            android:exported="true">
            <intent-filter >
                <action android:name="com.bbpos.intent.CLOSE_WIFI"/>
                <action android:name="com.bbpos.intent.OPEN_WIFI"/>
                <action android:name="com.bbpos.intent.GET_WIFI_STATE"/>
                <action android:name="android.net.wifi.WIFI_STATE_CHANGED"/>
            </intent-filter>
        </receiver>
    </application>

</manifest>
