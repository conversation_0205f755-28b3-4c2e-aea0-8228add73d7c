<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/bg_shape">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="17dp"
        android:gravity="center"
        android:background="@color/white">
        <ImageView
            android:id="@+id/iv_image"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:scaleType="center"
            android:background="@drawable/geo_warning"
            android:layout_gravity="center"
            android:contentDescription="TODO" />
    </LinearLayout>
    <LinearLayout
        android:layout_height="wrap_content"
        android:layout_width="match_parent"
        android:gravity="center"
        android:orientation="vertical"
        android:background="@drawable/shape_corner_down">
        <TextView
            android:id="@+id/tv_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="16dp"
            android:layout_marginRight="16dp"
            android:text="OUT OF GEOFENCE"
            android:gravity="center"
            android:textStyle="bold"
            android:textSize="30sp"
            android:textColor="#3a3c46"/>
        <TextView
            android:id="@+id/tv_content"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="15dp"
            android:layout_marginRight="15dp"
            android:gravity="center"
            android:text="Please return to the store, otherwise the device will be locked."
            android:textSize="13sp"
            android:textColor="#3a3c46"/>
        <Button
            android:id="@+id/btn_ok"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:padding="@dimen/dp_5"
            android:layout_marginTop="18dp"
            android:layout_marginLeft="16dp"
            android:layout_marginRight="16dp"
            android:layout_marginBottom="@dimen/dp_20"
            android:gravity="center"
            android:text="OK"
            android:textAllCaps="false"
            android:textSize="25sp"
            android:textColor="@color/white"
            android:background="@drawable/btn_bord_green" />
    </LinearLayout>
</LinearLayout>