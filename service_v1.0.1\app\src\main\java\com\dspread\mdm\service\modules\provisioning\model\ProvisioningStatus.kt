package com.dspread.mdm.service.modules.provisioning.model

/**
 * Provisioning状态枚举
 */
enum class ProvisioningStatus {
    /** 未开始 */
    NOT_STARTED,
    
    /** 执行中 */
    IN_PROGRESS,
    
    /** 下载配置中 */
    DOWNLOADING_CONFIG,
    
    /** 下载Logo中 */
    DOWNLOADING_LOGO,
    
    /** 下载开机动画中 */
    DOWNLOADING_BOOT_ANIMATION,
    
    /** 应用配置中 */
    APPLYING_CONFIG,
    
    /** 完成 */
    COMPLETED,
    
    /** 失败 */
    FAILED
}

/**
 * Provisioning结果
 */
data class ProvisioningResult(
    val status: ProvisioningStatus,
    val message: String,
    val errorCode: String? = null,
    val completedSteps: Int = 0,
    val totalSteps: Int = 0
) {
    val isSuccess: Bo<PERSON>an
        get() = status == ProvisioningStatus.COMPLETED
        
    val isInProgress: <PERSON><PERSON><PERSON>
        get() = status == ProvisioningStatus.IN_PROGRESS ||
                status == ProvisioningStatus.DOWNLOADING_CONFIG ||
                status == ProvisioningStatus.DOWNLOADING_LOGO ||
                status == ProvisioningStatus.DOWNLOADING_BOOT_ANIMATION ||
                status == ProvisioningStatus.APPLYING_CONFIG
                
    val progress: Float
        get() = if (totalSteps > 0) completedSteps.toFloat() / totalSteps else 0f
}

/**
 * 下载任务状态
 */
data class DownloadTask(
    val name: String,
    val url: String,
    val targetPath: String,
    val md5: String? = null,
    var status: DownloadStatus = DownloadStatus.PENDING,
    var errorMessage: String? = null
)

/**
 * 下载状态
 */
enum class DownloadStatus {
    /** 等待中 */
    PENDING,
    
    /** 下载中 */
    DOWNLOADING,
    
    /** 下载完成 */
    COMPLETED,
    
    /** 下载失败 */
    FAILED,
    
    /** 已跳过 */
    SKIPPED
}

/**
 * Provisioning触发类型
 */
enum class ProvisioningTrigger {
    /** 定时器触发 */
    TIMER,

    /** 网络连接触发 */
    NETWORK_CONNECTED,

    /** 手动触发 */
    MANUAL,

    /** 首次启动触发 */
    FIRST_BOOT
}

/**
 * Provisioning配置标志
 */
data class ProvisioningFlags(
    var isFirstProvisioningCompleted: Boolean = false,
    var isLogoDownloadSuccess: Boolean = false,
    var isBootAnimationDownloadSuccess: Boolean = false,
    var lastProvisioningTime: Long = 0L,
    var lastConfigCid: String = ""
) {

    /**
     * 检查是否需要执行Provisioning
     */
    fun needsProvisioning(currentCid: String? = null): Boolean {
        // 如果首次配置未完成，需要执行
        if (!isFirstProvisioningCompleted) {
            return true
        }

        // 如果CID发生变化，需要重新配置
        if (currentCid != null && currentCid != lastConfigCid) {
            return true
        }

        return false
    }

    /**
     * 标记配置完成
     */
    fun markCompleted(cid: String) {
        isFirstProvisioningCompleted = true
        isLogoDownloadSuccess = true
        isBootAnimationDownloadSuccess = true
        lastProvisioningTime = System.currentTimeMillis()
        lastConfigCid = cid
    }

    /**
     * 重置所有标志（用于重新配置）
     */
    fun reset() {
        isFirstProvisioningCompleted = false
        isLogoDownloadSuccess = false
        isBootAnimationDownloadSuccess = false
        lastProvisioningTime = 0L
        lastConfigCid = ""
    }
}
