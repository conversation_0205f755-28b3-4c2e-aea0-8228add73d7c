package com.bbpos.wiseapp.network.wificonfig;

import android.annotation.TargetApi;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.net.ProxyInfo;
import android.net.Uri;
import android.net.wifi.SupplicantState;
import android.net.wifi.WifiConfiguration;
import android.net.wifi.WifiInfo;
import android.net.wifi.WifiManager;
import android.os.Build;
import androidx.annotation.NonNull;

import com.bbpos.wiseapp.logger.BBLog;
import com.bbpos.wiseapp.network.wificonfig.listener.OnWifiConnectListener;
import com.bbpos.wiseapp.service.common.RequestPermission;
import com.bbpos.wiseapp.tms.utils.ContextUtil;
import com.bbpos.wiseapp.websocket.WifiProfileProcessService;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.List;


/**
 * wifi 管理类
 */
public class WiFiProfileManager extends BaseWiFiManager {

    private static final String TAG = WifiProfileProcessService.TAG;//BBLog.TAG;
    private static WiFiProfileManager mWiFiManager;
    private static NetworkBroadcastReceiver receiver;

    private WiFiProfileManager(Context context) {
        super(context);
        BBLog.d(TAG, "WiFiProfileManager: ");
        initNetWorkReceiver(context);
    }

    private void initNetWorkReceiver(Context context) {
        BBLog.d(TAG, "initNetWorkReceiver: ");
        receiver = new NetworkBroadcastReceiver();
        IntentFilter filter = new IntentFilter();
        filter.addAction("android.net.wifi.SCAN_RESULTS");
        filter.addAction("android.net.wifi.WIFI_STATE_CHANGED");
        filter.addAction("android.net.wifi.STATE_CHANGE");
        filter.addAction("android.net.wifi.supplicant.STATE_CHANGE");
        context.registerReceiver(receiver,filter, RequestPermission.REQUEST_PERMISSION_MY_BROADCAST, null);
    }

    public static WiFiProfileManager getInstance(Context context) {
        if (null == mWiFiManager) {
            synchronized (WiFiProfileManager.class) {
                if (null == mWiFiManager) {
                    mWiFiManager = new WiFiProfileManager(context);
                }
            }
        }
        return mWiFiManager;
    }

    /**
     * 打开Wifi
     */
    public void openWiFi() {
        if (!isWifiEnabled() && null != mWifiManager) {
            mWifiManager.setWifiEnabled(true);
        }
    }

    /**
     * 关闭Wifi
     */
    public void closeWiFi() {
        if (isWifiEnabled() && null != mWifiManager) {
            mWifiManager.setWifiEnabled(false);
        }
    }

    /**
     * 连接到开放网络
     *
     * @param ssid 热点名
     * @return 配置是否成功
     */
    public boolean connectOpenNetwork(@NonNull String ssid) {
        // 获取networkId
        int networkId = setOpenNetwork(ssid);
        if (-1 != networkId) {
            // 保存配置
            boolean isSave = saveConfiguration();
            // 连接网络
            boolean isEnable = enableNetwork(networkId);

            return isSave && isEnable;
        }
        return false;
    }

    /**
     * 连接到WEP网络
     *
     * @param ssid     热点名
     * @param password 密码
     * @return 配置是否成功
     */
    public boolean connectWEPNetwork(@NonNull String ssid, @NonNull String password) {
        // 获取networkId
        int networkId = setWEPNetwork(ssid, password);
        if (-1 != networkId) {
            // 保存配置
            boolean isSave = saveConfiguration();
            // 连接网络
            boolean isEnable = enableNetwork(networkId);

            return isSave && isEnable;
        }
        return false;
    }

    /**
     * 连接到WPA2网络
     *
     * @param ssid     热点名
     * @param password 密码
     * @return 配置是否成功
     */
    public boolean connectWPA2Network(@NonNull String ssid, @NonNull String password) {
        // 获取networkId
        int networkId = setWPA2Network(ssid, password);
        if (-1 != networkId) {
            // 保存配置
            boolean isSave = saveConfiguration();
            // 连接网络
            boolean isEnable = enableNetwork(networkId);

            return isSave && isEnable;
        }
        return false;
    }

       @TargetApi(value = 21)
       public WifiConfiguration setWifiProxyByHostAndPort( WifiConfiguration config,String host, int port, List<String> exclList,boolean isClearProxy){
           try{
               //linkProperties is no longer in WifiConfiguration
               Class proxyInfoClass = Class.forName("android.net.ProxyInfo");
               Class[] setHttpProxyParams = new Class[1];
               setHttpProxyParams[0] = proxyInfoClass;
               Class wifiConfigClass = Class.forName("android.net.wifi.WifiConfiguration");
               Method setHttpProxy = wifiConfigClass.getDeclaredMethod("setHttpProxy", setHttpProxyParams);
               setHttpProxy.setAccessible(true);

               //Method 1 to get the ENUM ProxySettings in IpConfiguration
               Class ipConfigClass = Class.forName("android.net.IpConfiguration");
               Field f = ipConfigClass.getField("proxySettings");
               Class proxySettingsClass = f.getType();

               //Method 2 to get the ENUM ProxySettings in IpConfiguration
               //Note the $ between the class and ENUM
               //Class proxySettingsClass = Class.forName("android.net.IpConfiguration$ProxySettings");
               Class[] setProxySettingsParams = new Class[1];
               setProxySettingsParams[0] = proxySettingsClass;
               Method setProxySettings = wifiConfigClass.getDeclaredMethod("setProxySettings", setProxySettingsParams);
               setProxySettings.setAccessible(true);

               ProxyInfo pi = null;
               if (!isClearProxy) {
                   if (exclList == null || exclList.size() == 0) {
                       if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                           pi = ProxyInfo.buildDirectProxy(host, port);
                       }
                   } else {
                       if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                           pi = ProxyInfo.buildDirectProxy(host, port, exclList);
                       }
                   }
               }else {
                   pi = ProxyInfo.buildDirectProxy(null, 0);
               }

               //pass the new object to setHttpProxy
               Object[] params_SetHttpProxy = new Object[1];
               params_SetHttpProxy[0] = pi;
               setHttpProxy.invoke(config, params_SetHttpProxy);

               //pass the enum to setProxySettings
               Object[] params_setProxySettings = new Object[1];
               params_setProxySettings[0] = Enum.valueOf((Class<Enum>) proxySettingsClass, isClearProxy ? "NONE" : "STATIC");
               setProxySettings.invoke(config, params_setProxySettings);

               BBLog.d(TAG, "setHttpProxyByHostAndPort: proxy succ" );
           }catch(Exception e){
               e.printStackTrace();
               BBLog.e(TAG, "setHttpProxyByHostAndPort: proxy failed" );
           }
           return config;
       }

    @TargetApi(value = 21)
    public WifiConfiguration setWifiProxyByPAC(WifiConfiguration config,String pac){
        BBLog.d(TAG, "setHttpProxyByPAC: pac = "+pac);
        try{
            //linkProperties is no longer in WifiConfiguration
            Class proxyInfoClass = Class.forName("android.net.ProxyInfo");
            Class[] setHttpProxyParams = new Class[1];
            setHttpProxyParams[0] = proxyInfoClass;
            Class wifiConfigClass = Class.forName("android.net.wifi.WifiConfiguration");
            Method setHttpProxy = wifiConfigClass.getDeclaredMethod("setHttpProxy", setHttpProxyParams);
            setHttpProxy.setAccessible(true);

            //Method 1 to get the ENUM ProxySettings in IpConfiguration
            Class ipConfigClass = Class.forName("android.net.IpConfiguration");
            Field f = ipConfigClass.getField("proxySettings");
            Class proxySettingsClass = f.getType();

            //Method 2 to get the ENUM ProxySettings in IpConfiguration
            //Note the $ between the class and ENUM
            //Class proxySettingsClass = Class.forName("android.net.IpConfiguration$ProxySettings");
            Class[] setProxySettingsParams = new Class[1];
            setProxySettingsParams[0] = proxySettingsClass;
            Method setProxySettings = wifiConfigClass.getDeclaredMethod("setProxySettings", setProxySettingsParams);
            setProxySettings.setAccessible(true);

            //Android 5 supports a PAC file
            //ENUM value is "PAC"
            ProxyInfo proxyPac = ProxyInfo.buildPacProxy(Uri.parse(pac));

            //pass the new object to setHttpProxy
            Object[] params_SetHttpProxy = new Object[1];
            params_SetHttpProxy[0] = proxyPac;
            setHttpProxy.invoke(config, params_SetHttpProxy);

            //pass the enum to setProxySettings
            Object[] params_setProxySettings = new Object[1];
            params_setProxySettings[0] = Enum.valueOf((Class<Enum>) proxySettingsClass, "STATIC");
            setProxySettings.invoke(config, params_setProxySettings);

            BBLog.d(TAG, "setHttpProxyByPAC: proxy succ config= "+config );
        }catch(Exception e){
            e.printStackTrace();
            BBLog.d(TAG, "setHttpProxyByPAC: proxy failed config= "+config );
        }
        return config;
    }

    /* *******************************************************************************************/


    /**
     * 广播接收者
     */
    public static class NetworkBroadcastReceiver extends BroadcastReceiver {
        private WifiInfo connectFailureInfo;
        @Override
        public void onReceive(Context context, Intent intent) {
            WifiManager wifiManager = (WifiManager) context.getApplicationContext().getSystemService(Context.WIFI_SERVICE);
            switch (intent.getAction()) {
                case WifiManager.NETWORK_STATE_CHANGED_ACTION: // WIFI连接状态发生改变
                    WifiInfo wifiInfo = intent.getParcelableExtra(WifiManager.EXTRA_WIFI_INFO);
                    if (null != wifiInfo && wifiInfo.getSupplicantState() == SupplicantState.COMPLETED) {
                        String ssid = wifiInfo.getSSID().replace("\"","");
                        BBLog.i(TAG, "onReceive: network connected ssid = " + ssid);
                    }
                    break;
                case WifiManager.SUPPLICANT_STATE_CHANGED_ACTION: // WIFI连接请求状态发生改变
                    // 获取连接状态
                    SupplicantState supplicantState = intent.getParcelableExtra(WifiManager.EXTRA_NEW_STATE);
                    if (null != supplicantState && null != mOnWifiConnectListener) {
                        mOnWifiConnectListener.onWiFiConnectLog(supplicantState.toString());
                    }
                    switch (supplicantState) {
                        case INTERFACE_DISABLED: // 接口禁用
                            BBLog.i(TAG, "onReceive: INTERFACE_DISABLED");
                            break;
                        case DISCONNECTED:// 断开连接
                            BBLog.e(TAG, "onReceive: DISCONNECTED");
                            connectFailureInfo = wifiManager.getConnectionInfo();
                            BBLog.i(TAG, "onReceive: DISCONNECTED  connectFailureInfo = " + connectFailureInfo);
                            if (null != connectFailureInfo && null != mOnWifiConnectListener) {
                                mOnWifiConnectListener.onWiFiConnectFailure(connectFailureInfo.getSSID().replace("\"",""));
                                // 断开连接
                                int networkId = connectFailureInfo.getNetworkId();
                                boolean isDisable = wifiManager.disableNetwork(networkId);
                                boolean isDisconnect = wifiManager.disconnect();
                                BBLog.i(TAG, "onReceive: DISCONNECTED  =  " + (isDisable && isDisconnect));
                            }
                            break;
                        case INACTIVE: // 不活跃的
                            connectFailureInfo = wifiManager.getConnectionInfo();
                            BBLog.e(TAG, "onReceive: INACTIVE connectFailureInfo = " + connectFailureInfo);
                            if (null != connectFailureInfo && null != mOnWifiConnectListener) {
                                mOnWifiConnectListener.onWiFiConnectFailure(connectFailureInfo.getSSID().replace("\"",""));
                                // 断开连接
                                int networkId = connectFailureInfo.getNetworkId();
                                boolean isDisable = wifiManager.disableNetwork(networkId);
                                boolean isDisconnect = wifiManager.disconnect();
                                BBLog.i(TAG, "onReceive: INACTIVE  =  " + (isDisable && isDisconnect));
                            }
                            break;
                        case SCANNING: // 正在扫描
                            BBLog.i(TAG, "onReceive: SCANNING");
                            break;
                        case AUTHENTICATING: // 正在验证
                            BBLog.i(TAG, "onReceive: AUTHENTICATING");
                            break;
                        case ASSOCIATING: // 正在关联
                            BBLog.i(TAG, "onReceive: ASSOCIATING");
                            break;
                        case ASSOCIATED: // 已经关联
                            BBLog.i(TAG, "onReceive: ASSOCIATED");
                            break;
                        case FOUR_WAY_HANDSHAKE:
                            BBLog.i(TAG, "onReceive: FOUR_WAY_HANDSHAKE");
                            break;
                        case GROUP_HANDSHAKE:
                            BBLog.i(TAG, "onReceive: GROUP_HANDSHAKE");
                            break;
                        case COMPLETED: // 完成
                            BBLog.e(TAG, "onReceive: WIFI_CONNECT_SUCCESS");
                            WifiInfo connectSuccessInfo = wifiManager.getConnectionInfo();
                            if (null != connectSuccessInfo && null != mOnWifiConnectListener) {
                                mOnWifiConnectListener.onWiFiConnectSuccess(connectSuccessInfo.getSSID().replace("\"",""));
                            }
                            break;
                        case DORMANT:
                            BBLog.i(TAG, "onReceive: DORMANT");
                            break;
                        case UNINITIALIZED: // 未初始化
                            BBLog.i(TAG, "onReceive: UNINITIALIZED");
                            break;
                        case INVALID: // 无效的
                            BBLog.i(TAG, "onReceive: INVALID");
                            break;
                        default:
                            break;
                    }
                    break;
                default:
                    break;
            }
        }
    }

    private static OnWifiConnectListener mOnWifiConnectListener;

    public void setOnWifiConnectListener(OnWifiConnectListener listener) {
        mOnWifiConnectListener = listener;
    }

    public void removeOnWifiConnectListener() {
        mOnWifiConnectListener = null;
    }

    /**
     * 剔除某个wifi
     */
    public static void delWifiBySSID(String ssid) {
        BBLog.d(TAG, "WiFiProfileManager delWifiBySSID");
        //获取本地已经连接过的wifi信息
        List<WifiConfiguration> existingConfigs = WiFiProfileManager.getInstance(ContextUtil.getInstance()).getConfiguredNetworks();
        //当前正在连接的wifi ssid 信息
        WifiInfo wifiInfo = WiFiProfileManager.getInstance(ContextUtil.getInstance()).getConnectionInfo();
        try {
            String curSSID = "";
            if (wifiInfo!=null){
                curSSID = wifiInfo.getSSID()!=null ? wifiInfo.getSSID().replace("\"",""): "";
                if (curSSID.equals(ssid)) {
                    BBLog.w(TAG, "Find a connected \"test\" WIFI AP");
                    WiFiProfileManager.getInstance(ContextUtil.getInstance()).disconnectCurrentWifi();
                    WiFiProfileManager.getInstance(ContextUtil.getInstance()).deleteConfig(wifiInfo.getNetworkId());
                    return;
                }
            }

            if (null != existingConfigs) {
                //获取存在 wifi profile list中的 wificonfig，包括当前已连接的wifi
                for (WifiConfiguration config : existingConfigs) {
                    if (config.SSID.replace("\"","").equals(ssid)) {//存在 profile list中的wifi
                        BBLog.w(TAG, "Find a saved \"test\" WIFI AP");
                        WiFiProfileManager.getInstance(ContextUtil.getInstance()).removedWifiConfig(config);
                        return;
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
