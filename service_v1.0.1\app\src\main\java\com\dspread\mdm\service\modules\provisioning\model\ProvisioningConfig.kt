package com.dspread.mdm.service.modules.provisioning.model

import com.dspread.mdm.service.utils.log.Logger
import org.json.JSONObject

/**
 * Provisioning配置数据模型
 * 对应平台返回的配置JSON结构
 */
data class ProvisioningConfig(
    val cid: String,
    val polling: PollingConfig,
    val system: SystemConfig,
    val requestTime: String
) {
    
    companion object {
        /**
         * 从JSON解析配置
         */
        fun fromJson(jsonObject: JSONObject): ProvisioningConfig? {
            return try {
                // 按照新的API返回格式解析
                val data = jsonObject.getJSONObject("data")

                // 获取基本信息
                val cid = data.getString("cid")
                val requestTime = data.getString("requestTime")

                // 解析customization配置
                val customization = data.getJSONObject("customization")

                // 解析customization.polling配置
                val pollingConfig = PollingConfig.fromJson(customization.getJSONObject("polling"))

                // 解析customization.system配置（新格式：system在customization下）
                val systemConfig = SystemConfig.fromJson(customization.getJSONObject("system"))

                ProvisioningConfig(cid, pollingConfig, systemConfig, requestTime)

            } catch (e: Exception) {
                // 只在真正出错时输出日志
                Logger.comE("Provisioning配置解析失败: ${e.message}")
                null
            }
        }
    }
}

/**
 * 轮询配置
 */
data class PollingConfig(
    val heartbeatTime: String,
    val terminalInfoTime: String,
    val statusApiUrl: String,
    val remoteUrl: String,
    val uploadMode: String,
    val wssReconnConfig: WssReconnConfig
) {
    companion object {
        fun fromJson(jsonObject: JSONObject): PollingConfig {
            val wssreconn = jsonObject.getJSONObject("wssreconn")
            return PollingConfig(
                heartbeatTime = jsonObject.getString("heartbeatTime"),
                terminalInfoTime = jsonObject.getString("terminalInfoTime"),
                statusApiUrl = jsonObject.getString("statusApiUrl"),
                remoteUrl = jsonObject.getString("remoteUrl"),
                uploadMode = jsonObject.getString("uploadMode"),
                wssReconnConfig = WssReconnConfig.fromJson(wssreconn)
            )
        }
    }
}

/**
 * WebSocket重连配置
 */
data class WssReconnConfig(
    val delayPolicy: String,
    val delaySwitch: String,
    val delayTime: String
) {
    companion object {
        fun fromJson(jsonObject: JSONObject): WssReconnConfig {
            return WssReconnConfig(
                delayPolicy = jsonObject.getString("delayPolicy"),
                delaySwitch = jsonObject.getString("delaySwitch"),
                delayTime = jsonObject.getString("delayTime")
            )
        }
    }
}

/**
 * 系统配置
 */
data class SystemConfig(
    val bootAnimation: String?,
    val bootAnimationMd5: String?,
    val logo: String?,
    val logoMd5: String?,
    val gpsConfig: GpsConfig,
    val powerSaveMode: PowerSaveMode,
    val timezone: String
) {
    companion object {
        fun fromJson(jsonObject: JSONObject): SystemConfig {
            return SystemConfig(
                bootAnimation = jsonObject.optString("bootAnimation").takeIf { it.isNotEmpty() },
                bootAnimationMd5 = jsonObject.optString("bootAnimationMd5").takeIf { it.isNotEmpty() },
                logo = jsonObject.optString("logo").takeIf { it.isNotEmpty() },
                logoMd5 = jsonObject.optString("logoMd5").takeIf { it.isNotEmpty() },
                gpsConfig = if (jsonObject.has("gps")) {
                    GpsConfig.fromJson(jsonObject.getJSONObject("gps"))
                } else {
                    // 平台返回的JSON中没有gps配置，使用默认值
                    GpsConfig("60", "300", "30", "10", "50", "true")
                },
                powerSaveMode = if (jsonObject.has("powerSaveMode")) {
                    PowerSaveMode.fromJson(jsonObject.getJSONObject("powerSaveMode"))
                } else {
                    PowerSaveMode("false", "30000")
                },
                timezone = jsonObject.optString("timezone", "Asia/Shanghai")
            )
        }
    }
}

/**
 * GPS配置
 */
data class GpsConfig(
    val minUpdateTime: String,
    val scheduleTime: String,
    val maxLocateTime: String,
    val minDistance: String,
    val validDistance: String,
    val care: String
) {
    companion object {
        fun fromJson(jsonObject: JSONObject): GpsConfig {
            return GpsConfig(
                minUpdateTime = jsonObject.getString("minUpdateTime"),
                scheduleTime = jsonObject.getString("scheduleTime"),
                maxLocateTime = jsonObject.getString("maxLocateTime"),
                minDistance = jsonObject.getString("minDistance"),
                validDistance = jsonObject.getString("valid_distance"),
                care = jsonObject.getString("care")
            )
        }
    }
}

/**
 * 省电模式配置
 */
data class PowerSaveMode(
    val enable: String,
    val screenTimeout: String
) {
    companion object {
        fun fromJson(jsonObject: JSONObject): PowerSaveMode {
            return PowerSaveMode(
                enable = jsonObject.getString("enable"),
                screenTimeout = jsonObject.getString("screenTimeout")
            )
        }
    }
}

/**
 * 默认Provisioning配置
 * 替代原有的assets/default_config.json文件
 */
object DefaultProvisioningConfig {

    /**
     * 获取默认的ProvisioningConfig
     */
    fun getDefaultConfig(): ProvisioningConfig {
        return ProvisioningConfig(
            cid = "10001",
            polling = getDefaultPollingConfig(),
            system = getDefaultSystemConfig(),
            requestTime = System.currentTimeMillis().toString()
        )
    }

    /**
     * 默认轮询配置
     */
    private fun getDefaultPollingConfig(): PollingConfig {
        return PollingConfig(
            heartbeatTime = "300",              // 5分钟心跳
            terminalInfoTime = "900",           // 15分钟终端信息上传
            statusApiUrl = "ws://35.75.3.206:8080/status/websocket/register",
            remoteUrl = "ws://35.75.3.206:8080/remoteWSS/websockify",
            uploadMode = "1",                   // 上传模式
            wssReconnConfig = getDefaultWssReconnConfig()
        )
    }

    /**
     * 默认WebSocket重连配置
     */
    private fun getDefaultWssReconnConfig(): WssReconnConfig {
        return WssReconnConfig(
            delayPolicy = "30",                 // 延迟策略30秒
            delaySwitch = "1",                  // 启用延迟重连
            delayTime = "900"                   // 延迟时间15分钟
        )
    }

    /**
     * 默认系统配置
     */
    private fun getDefaultSystemConfig(): SystemConfig {
        return SystemConfig(
            bootAnimation = "https://siot-bucket01.s3.ap-northeast-1.amazonaws.com/test/bootanimation.zip",
            bootAnimationMd5 = "ba1ee533924eae5c408465e7cddcbda4",
            logo = "https://siot-bucket01.s3.ap-northeast-1.amazonaws.com/test/logo.bin",
            logoMd5 = "ba1ee533924eae5c408465e7cddcbda4",
            gpsConfig = getDefaultGpsConfig(),
            powerSaveMode = getDefaultPowerSaveMode(),
            timezone = "Asia/Hong_Kong"         // 默认时区
        )
    }

    /**
     * 默认GPS配置
     */
    private fun getDefaultGpsConfig(): GpsConfig {
        return GpsConfig(
            minUpdateTime = "30",               // 最小更新时间30秒
            scheduleTime = "60",                // 调度时间60秒
            maxLocateTime = "0",                // 最大定位时间（0表示无限制）
            minDistance = "10",                 // 最小距离10米
            validDistance = "500",              // 有效距离500米
            care = "1"                          // 启用GPS关注
        )
    }

    /**
     * 默认省电模式配置
     */
    private fun getDefaultPowerSaveMode(): PowerSaveMode {
        return PowerSaveMode(
            enable = "1",                       // 启用省电模式
            screenTimeout = "0"                 // 屏幕超时（0表示不超时）
        )
    }
}
