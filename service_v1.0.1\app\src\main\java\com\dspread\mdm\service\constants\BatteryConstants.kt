package com.dspread.mdm.service.constants

/**
 * 电池相关常量
 *  Constants.java 中的电池相关常量
 * 用于全局管理电池状态信息
 */
object BatteryConstants {
    
    // 电池充电状态
    @JvmStatic
    var IS_BATTERY_CHARGING = false
    
    // 电池低电量状态
    @JvmStatic
    var IS_BATTERY_LOW = false
    
    // 电池低电量状态（用于系统更新）
    @JvmStatic
    var IS_BATTERY_LOW_FOR_OSUPDATE = false
    
    // 拆箱状态
    @JvmStatic
    var UNBOX_STATUS = ""
    
    // 电池耗尽状态
    @JvmStatic
    var IS_OUT_OF_BATTERY = false
    
    // 设备使用中状态
    @JvmStatic
    var IS_IN_USE = false
    
    // 电池温度（单位：摄氏度 * 10）
    @JvmStatic
    var BAT_TEMP = 0
    
    // 电池电量百分比
    @JvmStatic
    var BAT_LEVEL = 0
    
    // 电池健康状态
    @JvmStatic
    var BAT_HEALTH = 0
    
    // 电池电量偏移量
    @JvmStatic
    var BAT_LEVEL_OFFSET = 2
    
    // 系统更新所需的最低电量
    @JvmStatic
    var BAT_LOW_FOR_OSUPDATE = 20
    
    // 充电时系统更新所需的最低电量
    @JvmStatic
    var BAT_LOW_FOR_OSUPDATE_CHARGING = 15
    
    // 系统运行所需的最低电量
    @JvmStatic
    var BAT_LOW_FOR_SYYTEM = 15
    
    /**
     * 检查电池是否低电量
     */
    @JvmStatic
    fun isBatteryLow(): Boolean {
        return BAT_LEVEL <= BAT_LOW_FOR_SYYTEM
    }
    
    /**
     * 检查电池是否适合系统更新
     */
    @JvmStatic
    fun isBatteryOkForOSUpdate(): Boolean {
        return if (IS_BATTERY_CHARGING) {
            BAT_LEVEL >= BAT_LOW_FOR_OSUPDATE_CHARGING
        } else {
            BAT_LEVEL >= BAT_LOW_FOR_OSUPDATE
        }
    }
    
    /**
     * 检查电池是否耗尽
     */
    @JvmStatic
    fun isBatteryOutOfPower(): Boolean {
        return BAT_LEVEL <= 5
    }
    
    /**
     * 获取电池温度（摄氏度）
     */
    @JvmStatic
    fun getBatteryTemperatureCelsius(): Float {
        return BAT_TEMP / 10.0f
    }
    
    /**
     * 更新电池状态
     */
    @JvmStatic
    fun updateBatteryStatus(
        level: Int,
        temperature: Int,
        health: Int,
        isCharging: Boolean
    ) {
        BAT_LEVEL = level
        BAT_TEMP = temperature
        BAT_HEALTH = health
        IS_BATTERY_CHARGING = isCharging
        IS_BATTERY_LOW = isBatteryLow()
        IS_BATTERY_LOW_FOR_OSUPDATE = !isBatteryOkForOSUpdate()
        IS_OUT_OF_BATTERY = isBatteryOutOfPower()
    }
    
    /**
     * 重置电池状态
     */
    @JvmStatic
    fun resetBatteryStatus() {
        IS_BATTERY_CHARGING = false
        IS_BATTERY_LOW = false
        IS_BATTERY_LOW_FOR_OSUPDATE = false
        UNBOX_STATUS = ""
        IS_OUT_OF_BATTERY = false
        IS_IN_USE = false
        BAT_TEMP = 0
        BAT_LEVEL = 0
        BAT_HEALTH = 0
    }
    
    /**
     * 获取电池状态描述
     */
    @JvmStatic
    fun getBatteryStatusDescription(): String {
        return buildString {
            append("电池状态: ")
            append("电量=$BAT_LEVEL%, ")
            append("温度=${getBatteryTemperatureCelsius()}°C, ")
            append("健康=$BAT_HEALTH, ")
            append("充电=${if (IS_BATTERY_CHARGING) "是" else "否"}, ")
            append("低电量=${if (IS_BATTERY_LOW) "是" else "否"}, ")
            append("耗尽=${if (IS_OUT_OF_BATTERY) "是" else "否"}")
        }
    }
}
