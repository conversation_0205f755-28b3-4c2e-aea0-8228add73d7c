package com.bbpos.wiseapp.settings.activity;

import android.app.Activity;
import android.bluetooth.BluetoothAdapter;
import android.bluetooth.BluetoothDevice;
import android.bluetooth.BluetoothGatt;
import android.bluetooth.BluetoothGattCallback;
import android.bluetooth.BluetoothProfile;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.res.Configuration;
import android.net.NetworkInfo;
import android.os.Bundle;
import android.util.Log;
import android.view.View;
import android.view.View.OnClickListener;
import android.widget.AdapterView;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ListView;
import android.widget.TextView;

import com.bbpos.wiseapp.logger.BBLog;
import com.bbpos.wiseapp.service.R;
import com.bbpos.wiseapp.service.common.RequestPermission;
import com.bbpos.wiseapp.settings.adapter.BTAdapter;
import com.bbpos.wiseapp.settings.bluetooth.BluetoothController;
import com.bbpos.wiseapp.settings.widget.ToggleSwitch;

import java.util.ArrayList;
import java.util.List;

//登录界面
public class BluetoothActivity extends Activity implements OnClickListener{
	private List<BluetoothDevice> listItemBonded = new ArrayList<>();
	private List<BluetoothDevice> listItemAvailable = new ArrayList<>();

	private LinearLayout ll_bluetooth;
	private LinearLayout ll_listbonded;
	private ListView lv_btbonded;
	private ListView lv_btAvailable;
	private ImageView iv_close;
	private ToggleSwitch toggleSwitch;
	private TextView tv_status;
	private TextView tv_prompt;
	private TextView tv_prompt_available;
	private TextView tv_visible_prompt;
	private BTAdapter listbtbondedAdapter;
	private BTAdapter listbtAvailableAdapter;
	private IntentFilter mIntentFilter;
	private BluetoothEnabler mBluetoothEnabler;
	private BluetoothAdapter mBluetoothAdapter = BluetoothAdapter.getDefaultAdapter();;
	private BluetoothController mBluetoothController;
	private BluetoothGatt mBluetoothGatt;
	private BluetoothDevice mCurPairDevice;
	private LinearLayout mCurLLView;
	private int btstate;
	private boolean isBtConnecting = false;

	private final BroadcastReceiver mReceiver = new BroadcastReceiver() {
		@Override
		public void onReceive(Context context, Intent intent) {
			String action = intent.getAction();
			BBLog.i(BBLog.TAG, "BluetoothActivity onReceive: " + action);
			if (action.equals(BluetoothAdapter.ACTION_DISCOVERY_FINISHED)){

			} else if (action.equals(BluetoothAdapter.ACTION_DISCOVERY_FINISHED)){

			} else if (action.equals(BluetoothDevice.ACTION_FOUND)) {
				// 从intent中获取设备
				BluetoothDevice device = intent.getParcelableExtra(BluetoothDevice.EXTRA_DEVICE);
				// 没否配对
				if (device.getBondState() != BluetoothDevice.BOND_BONDED) {
					if (!listItemAvailable.contains(device)) {
						listItemAvailable.add(device);
					}
				}
				updateBluetoothList();
			} else if (action.equals(BluetoothAdapter.ACTION_STATE_CHANGED)){
				int blueState = intent.getIntExtra(BluetoothAdapter.EXTRA_STATE, 0);
				mBluetoothEnabler.handleBluetoothStateChanged(blueState);
				onBluetoothStateChanged(blueState);
			} else if (action.equals(BluetoothDevice.ACTION_PAIRING_REQUEST)) {
				BBLog.i(BBLog.TAG, "BluetoothDevice.ACTION_PAIRING_REQUEST");
			} else if (action.equals(BluetoothDevice.ACTION_BOND_STATE_CHANGED)) {
				BluetoothDevice remoteDevice = intent.getParcelableExtra(BluetoothDevice.EXTRA_DEVICE);
				int bondState = remoteDevice.getBondState();
				onBluetoothBondStateChanged(bondState);
			} else if (action.equals(BluetoothDevice.ACTION_ACL_DISCONNECTED)) {
				updateBluetoothList();
			}
		}
	};

	private void onBluetoothBondStateChanged(int bondState) {

		switch (bondState) {
			case BluetoothDevice.BOND_BONDING:
				if (mCurLLView != null) {
					TextView tvName = (TextView) mCurLLView.findViewById(R.id.tv_name);
					TextView tvStatus = (TextView) mCurLLView.findViewById(R.id.tv_status);
					tvName.setEnabled(false);
					tvStatus.setText(getString(R.string.bluetooth_pairing));
					tvStatus.setVisibility(View.VISIBLE);
					tvStatus.setEnabled(false);
				}
				break;
			case BluetoothDevice.BOND_BONDED:
				mCurLLView = null;
				listItemAvailable.remove(mCurPairDevice);
				updateBluetoothList();
				break;
			case BluetoothDevice.BOND_NONE:
				mCurLLView = null;
				updateBluetoothList();
				break;
		}
	}

	@Override
	public void onCreate(Bundle savedInstanseBundle){
		super.onCreate(savedInstanseBundle);
		setContentView(R.layout.activity_bluetooth);

		initView();

		mBluetoothEnabler = new BluetoothEnabler(BluetoothActivity.this, toggleSwitch, tv_status);
		mBluetoothController = new BluetoothController(BluetoothActivity.this, mBluetoothAdapter);
		mIntentFilter = new IntentFilter(BluetoothAdapter.ACTION_DISCOVERY_STARTED);
		mIntentFilter.addAction(BluetoothAdapter.ACTION_DISCOVERY_FINISHED);
		mIntentFilter.addAction(BluetoothAdapter.ACTION_STATE_CHANGED);
		mIntentFilter.addAction(BluetoothDevice.ACTION_FOUND);
		mIntentFilter.addAction(BluetoothDevice.ACTION_ACL_DISCONNECTED);
//		mIntentFilter.addAction(BluetoothLeService.ACTION_GATT_CONNECTED);
//		mIntentFilter.addAction(BluetoothLeService.ACTION_GATT_DISCONNECTED);
//		mIntentFilter.addAction(BluetoothLeService.ACTION_GATT_SERVICES_DISCOVERED);
//		mIntentFilter.addAction(BluetoothLeService.ACTION_DATA_AVAILABLE);
		mIntentFilter.addAction(BluetoothDevice.ACTION_UUID);
		mIntentFilter.addAction(BluetoothDevice.ACTION_PAIRING_REQUEST);
		mIntentFilter.addAction(BluetoothDevice.ACTION_BOND_STATE_CHANGED);
	}

	/**
	 * 搜索蓝牙的方法
	 */
	private void startScanBluetooth() {
		// 判断是否在搜索,如果在搜索，就取消搜索
		if (mBluetoothAdapter.isDiscovering()) {
			mBluetoothAdapter.cancelDiscovery();
		}
		// 开始搜索
		mBluetoothAdapter.startDiscovery();
	}

	private void showBluetoothStateDesc(String desc) {
		if (View.VISIBLE == ll_bluetooth.getVisibility()) {
			ll_bluetooth.setVisibility(View.INVISIBLE);
		}
		tv_prompt.setVisibility(View.VISIBLE);
		tv_prompt.setText(desc);
	}

	private void updateBluetoothList() {
		if (View.INVISIBLE == ll_bluetooth.getVisibility()) {
			ll_bluetooth.setVisibility(View.VISIBLE);
		}
		if (View.VISIBLE == tv_prompt.getVisibility()) {
			tv_prompt.setVisibility(View.INVISIBLE);
		}

		listItemBonded.clear();
		Object[] pairedObjects = BluetoothAdapter.getDefaultAdapter().getBondedDevices().toArray();
		for (int i = 0; i < pairedObjects.length; ++i) {
			listItemBonded.add((BluetoothDevice) pairedObjects[i]);
		}

		if (listItemBonded.size() == 0) {
			if (View.VISIBLE == ll_listbonded.getVisibility()) {
				ll_listbonded.setVisibility(View.GONE);
			}
		} else {
			if (View.GONE == ll_listbonded.getVisibility() || View.INVISIBLE == ll_listbonded.getVisibility()) {
				ll_listbonded.setVisibility(View.VISIBLE);
			}
		}
		listbtbondedAdapter.notifyDataSetChanged();

		listbtAvailableAdapter.notifyDataSetChanged();
	}

	private void onBluetoothStateChanged(int wifiState) {
		switch (wifiState) {
			case BluetoothAdapter.STATE_ON:
				showBluetoothStateDesc(getString(R.string.bluetooth_searching_for_devices));
				startScanBluetooth();
				break;
			case BluetoothAdapter.STATE_TURNING_ON:
				showBluetoothStateDesc(getString(R.string.bluetooth_starting));
				break;
			case BluetoothAdapter.STATE_TURNING_OFF:
				showBluetoothStateDesc(getString(R.string.wifi_stopping));
				break;
			case BluetoothAdapter.STATE_OFF:
				showBluetoothStateDesc(getString(R.string.bluetooth_empty_list_bluetooth_off));
				break;
		}
	}

	private void setConnectDetail(String desc) {
//		if (tv_connect_detail != null) {
//			tv_connect_detail.setVisibility(View.VISIBLE);
//			tv_connect_detail.setText(desc);
//		}
	}

	private void onNetWorkStateChanged(NetworkInfo.DetailedState state) {
		if (state == NetworkInfo.DetailedState.SCANNING) {
			BBLog.i(BBLog.TAG, "onReceive onNetWorkStateChanged: +正在扫描");
			setConnectDetail("正在扫描");
		} else if (state == NetworkInfo.DetailedState.CONNECTING) {
			BBLog.i(BBLog.TAG, "onReceive onNetWorkStateChanged: +正在连接");
			setConnectDetail("正在连接");
		} else if (state == NetworkInfo.DetailedState.OBTAINING_IPADDR) {
			BBLog.i(BBLog.TAG, "onReceive onNetWorkStateChanged: +获取IP地址");
			setConnectDetail("获取IP地址");
		} else if (state == NetworkInfo.DetailedState.CONNECTED) {
			BBLog.i(BBLog.TAG, "onReceive onNetWorkStateChanged: +建立连接");
			setConnectDetail(getString(R.string.connected));
		} else if (state == NetworkInfo.DetailedState.DISCONNECTING) {
			BBLog.i(BBLog.TAG, "onReceive onNetWorkStateChanged: +正在断开连接");
			setConnectDetail("正在断开连接");
		} else if (state == NetworkInfo.DetailedState.DISCONNECTED) {
			BBLog.i(BBLog.TAG, "onReceive onNetWorkStateChanged: +已断开连接");
			setConnectDetail("已断开连接");
		} else if (state == NetworkInfo.DetailedState.FAILED) {
			BBLog.i(BBLog.TAG, "onReceive onNetWorkStateChanged: +连接失败");
			setConnectDetail("连接失败");
		}
	}

	@Override
	protected void onResume() {
		super.onResume();
		mBluetoothEnabler.resume(BluetoothActivity.this);
		registerReceiver(mReceiver, mIntentFilter,  RequestPermission.REQUEST_PERMISSION_MY_BROADCAST, null);
	}

	@Override
	protected void onPause() {
		super.onPause();
		mBluetoothEnabler.pause();
		unregisterReceiver(mReceiver);
	}

	@Override
	public void onConfigurationChanged(Configuration newConfig) {
		super.onConfigurationChanged(newConfig);
		finish();
	}

	private void initView() {
		TextView title = findViewById(R.id.toolbar_title_tv);
		title.setText(getString(R.string.bluetooth));

		iv_close = findViewById(R.id.toolbar_left_btn);
		iv_close.setOnClickListener(this);

		lv_btbonded = findViewById(R.id.list_btbonded);
		lv_btAvailable = findViewById(R.id.list_btavailable);

		ll_bluetooth = findViewById(R.id.ll_bluetooth);
		ll_listbonded = findViewById(R.id.ll_listbonded);
		tv_prompt = findViewById(R.id.tv_prompt);
		tv_prompt_available = findViewById(R.id.tv_prompt_available);

		tv_visible_prompt = findViewById(R.id.tv_visible_prompt);
		tv_visible_prompt.setText(getString(R.string.bluetooth_is_visible_message, mBluetoothAdapter.getName()));

		toggleSwitch = findViewById(R.id.tb_switch);
		tv_status = findViewById(R.id.tv_status);

		lv_btbonded = (ListView) findViewById(R.id.list_btbonded);
		listbtbondedAdapter = new BTAdapter(BluetoothActivity.this, listItemBonded);
		lv_btbonded.setAdapter(listbtbondedAdapter);
		lv_btbonded.setOnItemClickListener(new AdapterView.OnItemClickListener() {
			@Override
			public void onItemClick(AdapterView<?> parent, View view, int position, long id) {

			}
		});

		lv_btAvailable = (ListView) findViewById(R.id.list_btavailable);
		listbtAvailableAdapter = new BTAdapter(BluetoothActivity.this, listItemAvailable);
		lv_btAvailable.setAdapter(listbtAvailableAdapter);
		lv_btAvailable.setOnItemClickListener(new AdapterView.OnItemClickListener() {
			@Override
			public void onItemClick(AdapterView<?> parent, View view, int position, long id) {
				mCurLLView = (LinearLayout) view;
				mCurPairDevice = listItemAvailable.get(position);
				mBluetoothController.pair(listItemAvailable.get(position).getAddress());
			}
		});
		if (mBluetoothAdapter.isEnabled()) {
			toggleSwitch.setChecked(true);
			tv_status.setText(getString(R.string.on));
			ll_bluetooth.setVisibility(View.VISIBLE);
			tv_prompt.setVisibility(View.INVISIBLE);

			Object[] pairedObjects = BluetoothAdapter.getDefaultAdapter().getBondedDevices().toArray();
			for (int i = 0; i < pairedObjects.length; ++i) {
				listItemBonded.add((BluetoothDevice) pairedObjects[i]);
			}

			startScanBluetooth();
		} else {
			toggleSwitch.setChecked(false);
			tv_status.setText(getString(R.string.off));
			ll_bluetooth.setVisibility(View.INVISIBLE);
			tv_prompt.setVisibility(View.VISIBLE);
			tv_prompt.setText(getString(R.string.bluetooth_empty_list_bluetooth_off));
		}
	}

	@Override
	public void onClick(View v) {
		switch (v.getId()) {
			case R.id.toolbar_left_btn:
				finish();
				break;
		}
	}

	private final BluetoothGattCallback mGattCallback = new BluetoothGattCallback() {
		@Override
		public void onConnectionStateChange(BluetoothGatt gatt, int status, int newState) {
			super.onConnectionStateChange(gatt, status, newState);
			String intentAction;
			BBLog.i(BBLog.TAG, "oldStatus=" + status + " NewStates=" + newState);
			if(status == BluetoothGatt.GATT_SUCCESS)
			{
				if (newState == BluetoothProfile.STATE_CONNECTED) {
					BBLog.i(BBLog.TAG, "Connected to GATT server.");
					// Attempts to discover services after successful connection.
					BBLog.i(BBLog.TAG, "Attempting to start service discovery:" + mBluetoothGatt.discoverServices());
				} else if (newState == BluetoothProfile.STATE_DISCONNECTED) {
					if (mBluetoothGatt != null) {
						mBluetoothGatt.close();
						mBluetoothGatt = null;
					}
					BBLog.i(BBLog.TAG, "Disconnected from GATT server.");
				}
			}
		}
	};
}
