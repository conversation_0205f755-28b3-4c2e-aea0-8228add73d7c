package com.bbpos.wiseapp.utils;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.UnsupportedEncodingException;

public class ByteUtils {
	/** 返回字符串的ASCII字节数组 */
    public static final byte[] getBytes(String s) {
        try {
            return s.getBytes("US-ASCII");
        } catch (UnsupportedEncodingException e) {
            // 正常不会出现异常
            e.printStackTrace();
            return new byte[0];
        }
    }
    
    /** 低位在前，byte数组转int*/
    public static int lowToInt(byte[] data){
    	 int s = 0; 
    	  s = (data[3]&0xFF)<<24 | (data[2]&0xFF)<<16 | (data[1]&0xFF)<<8 | (data[0]&0xFF); 
    	  return s;
    }

    public static String getString(InputStream inputStream) {
        InputStreamReader inputStreamReader = null;
        try {
            inputStreamReader = new InputStreamReader(inputStream, "UTF-8");
        } catch (UnsupportedEncodingException e1) {
            e1.printStackTrace();
        }
        BufferedReader reader = new BufferedReader(inputStreamReader);
        StringBuffer sb = new StringBuffer("");
        String line;
        try {
            while ((line = reader.readLine()) != null) {
                sb.append(line);
                sb.append("\n");
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return sb.toString();
    }

    public static boolean isNumeric(String str) {
        for (int i = str.length(); --i >= 0; ) {
            if (!Character.isDigit(str.charAt(i))) {
                return false;
            }
        }

        return true;
    }

    public static String hexString2AsciiString(String hexString) {
        if (hexString == null)
            return "";
        if (hexString.contains(" ")) {
            hexString = hexString.replaceAll(" ", "");
        }
        if (hexString.length() % 2 != 0) {
            return "";
        }
        StringBuilder output = new StringBuilder();
        for (int i = 0; i < hexString.length(); i += 2) {
            String str = hexString.substring(i, i + 2);
            output.append((char) Integer.parseInt(str, 16));
        }
        return output.toString();
    }

    public static int hexStringToInt(String hex) {
        return Integer.parseInt(hex, 16);
    }
}
