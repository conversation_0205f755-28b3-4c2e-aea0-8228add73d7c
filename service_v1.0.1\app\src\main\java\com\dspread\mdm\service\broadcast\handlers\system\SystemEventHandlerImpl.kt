package com.dspread.mdm.service.broadcast.handlers.system

import android.content.Context
import android.content.Intent
import android.os.Handler
import android.os.Looper
import com.dspread.mdm.service.platform.manager.ServiceStartupManager
import com.dspread.mdm.service.network.websocket.WebSocketCenter
import com.dspread.mdm.service.network.websocket.message.WsMessageSender
import com.dspread.mdm.service.broadcast.core.BroadcastActions
import com.dspread.mdm.service.broadcast.core.SystemEventHandler
import com.dspread.mdm.service.broadcast.handlers.service.ServiceGuardEventHandler
import com.dspread.mdm.service.utils.log.Logger


/**
 * 系统事件处理器实现
 * 统一处理所有系统相关的广播事件
 */
class SystemEventHandlerImpl : SystemEventHandler {
    
    private val TAG = "SystemEventHandler"
    
    override fun getHandlerName(): String = TAG
    
    override fun getSupportedActions(): List<String> {
        return listOf(
            BroadcastActions.ACTION_BOOT_COMPLETED,
            BroadcastActions.ACTION_QUICKBOOT_POWERON,
            Intent.ACTION_LOCKED_BOOT_COMPLETED,
            Intent.ACTION_SHUTDOWN,
            Intent.ACTION_REBOOT
        )
    }
    
    override fun handleBroadcast(context: Context, intent: Intent): Boolean {
        val action = intent.action ?: return false
        
        return try {
            when (action) {
                BroadcastActions.ACTION_BOOT_COMPLETED,
                BroadcastActions.ACTION_QUICKBOOT_POWERON,
                Intent.ACTION_LOCKED_BOOT_COMPLETED -> {
                    onBootCompleted(context)
                    true
                }
                Intent.ACTION_SHUTDOWN -> {
                    onSystemShutdown(context, false)
                    true
                }
                Intent.ACTION_REBOOT -> {
                    onSystemShutdown(context, true)
                    true
                }
                else -> false
            }
        } catch (e: Exception) {
            Logger.receiverE("$TAG 处理广播失败: $action", e)
            false
        }
    }
    
    override fun onBootCompleted(context: Context) {
        Logger.receiver("SystemEventHandlerImpl: 处理开机完成事件")

        // 使用统一服务管理器启动后台服务
        ServiceStartupManager.startService(
            context,
            ServiceStartupManager.StartupReason.BOOT_COMPLETED
        )

        // 延迟启动WebSocket服务（等待网络就绪）
        Handler(Looper.getMainLooper()).postDelayed({
            try {
                WebSocketCenter.init(context)
//                WebSocketCenter.startWebSocket()
                Logger.success("开机后WebSocket服务启动完成")
            } catch (e: Exception) {
                Logger.receiverE("开机后启动WebSocket服务失败", e)
            }
        }, 5000) // 延迟5秒

        // 启动服务守护定时器
        try {
            ServiceGuardEventHandler().startServiceGuardTimer(context)
            Logger.receiver("SystemEventHandlerImpl: 服务守护定时器启动成功")
        } catch (e: Exception) {
            Logger.receiverE("SystemEventHandlerImpl: 启动服务守护定时器失败", e)
        }
    }
    

    
    override fun onSystemShutdown(context: Context, isReboot: Boolean) {
        if (isReboot) {
            Logger.receiver("系统即将重启")
            // 系统重启事件 - 使用正确的事件类型编码
            WsMessageSender.uploadDeviceEvent("1", "系统即将重启")
        } else {
            Logger.receiver("系统即将关机")
            // 系统关机事件 - 使用正确的事件类型编码
            WsMessageSender.uploadDeviceEvent("1", "系统即将关机")
            // 关机前上报最后的电池状态
            WsMessageSender.uploadBatteryStatus("system_shutdown")
        }
    }
    
}
