package com.dspread.mdm.service.modules

import android.content.Context
import com.dspread.mdm.service.modules.geofence.GeofenceManager
import com.dspread.mdm.service.modules.logstream.LogStreamManager
import com.dspread.mdm.service.utils.log.Logger
import kotlinx.coroutines.*
import java.util.concurrent.ConcurrentHashMap

/**
 * 模块管理器注册中心
 * 负责管理所有模块实例的生命周期
 */
object ModuleManagerRegistry {
    
    private const val TAG = "[ModuleManagerRegistry]"
    
    // 模块实例存储
    private val moduleInstances = ConcurrentHashMap<String, Any>()

    // 模块初始化状态
    private val moduleInitialized = ConcurrentHashMap<String, Boolean>()

    // 上下文引用
    private var applicationContext: Context? = null

    // 注册中心专用协程作用域
    private val registryScope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    
    /**
     * 初始化注册中心
     */
    fun initialize(context: Context) {
        applicationContext = context.applicationContext
        Logger.com("$TAG 模块管理器注册中心初始化完成")
    }
    
    /**
     * 获取LogStreamManager实例
     */
    fun getLogStreamManager(): LogStreamManager? {
        return try {
            val key = "LogStreamManager"

            // 如果实例不存在，创建新实例
            if (!moduleInstances.containsKey(key)) {
                val context = applicationContext ?: return null
                val instance = LogStreamManager(context)

                moduleInstances[key] = instance
                moduleInitialized[key] = false

                Logger.com("$TAG LogStreamManager实例创建完成")
            }

            moduleInstances[key] as? LogStreamManager

        } catch (e: Exception) {
            Logger.comE("$TAG 获取LogStreamManager实例失败", e)
            null
        }
    }

    /**
     * 初始化并启动LogStreamManager
     */
    suspend fun initializeLogStreamManager(): Boolean {
        return try {
            val key = "LogStreamManager"
            val instance = getLogStreamManager() ?: return false

            if (!moduleInitialized[key]!!) {
                // 先初始化
                val initResult = instance.initialize()
                if (initResult.isSuccess) {
                    Logger.com("$TAG LogStreamManager初始化成功")
                    
                    // 再启动
                    val startResult = instance.start()
                    if (startResult.isSuccess) {
                        moduleInitialized[key] = true
                        Logger.com("$TAG LogStreamManager启动成功")
                        true
                    } else {
                        Logger.comE("$TAG LogStreamManager启动失败", startResult.exceptionOrNull())
                        false
                    }
                } else {
                    Logger.comE("$TAG LogStreamManager初始化失败", initResult.exceptionOrNull())
                    false
                }
            } else {
                Logger.com("$TAG LogStreamManager已经初始化并启动")
                true
            }
        } catch (e: Exception) {
            Logger.comE("$TAG 初始化LogStreamManager异常", e)
            false
        }
    }

    /**
     * 初始化并启动GeofenceManager
     */
    suspend fun initializeGeofenceManager(): Boolean {
        return try {
            val key = "GeofenceManager"

            if (!moduleInitialized.getOrDefault(key, false)) {
                Logger.com("$TAG 开始初始化地理围栏管理器")

                val context = applicationContext ?: return false
                val success = GeofenceManager.initialize(context)

                if (success) {
                    moduleInitialized[key] = true
                    Logger.com("$TAG 地理围栏管理器初始化成功")
                    true
                } else {
                    Logger.comE("$TAG 地理围栏管理器初始化失败")
                    false
                }
            } else {
                Logger.com("$TAG 地理围栏管理器已经初始化")
                true
            }
        } catch (e: Exception) {
            Logger.comE("$TAG 初始化地理围栏管理器异常", e)
            false
        }
    }

    /**
     * 检查模块是否已初始化
     */
    fun isModuleInitialized(moduleName: String): Boolean {
        return moduleInitialized[moduleName] ?: false
    }
    
    /**
     * 启动所有模块
     */
    suspend fun startAllModules() {
        try {
            Logger.com("$TAG 启动所有模块")

            // 恢复LogStreamManager，启用日志存储功能
            val logStreamInitialized = initializeLogStreamManager()
            if (logStreamInitialized) {
                Logger.com("$TAG LogStreamManager启动成功")
            } else {
                Logger.comE("$TAG LogStreamManager启动失败")
            }

            // 启动地理围栏模块
            val geofenceInitialized = initializeGeofenceManager()
            if (geofenceInitialized) {
                Logger.com("$TAG GeofenceManager启动成功")
            } else {
                Logger.comE("$TAG GeofenceManager启动失败")
            }

            // TODO: 启动其他模块（WiFi Profile, APN等）

            Logger.com("$TAG 所有模块启动完成")

        } catch (e: Exception) {
            Logger.comE("$TAG 启动模块失败", e)
        }
    }
    
    /**
     * 停止所有模块
     */
    suspend fun stopAllModules() {
        try {
            Logger.com("$TAG 停止所有模块")

            // 恢复LogStreamManager停止逻辑
            val logStreamManager = moduleInstances["LogStreamManager"] as? LogStreamManager
            if (logStreamManager != null) {
                val stopResult = logStreamManager.stop()
                if (stopResult.isSuccess) {
                    moduleInitialized["LogStreamManager"] = false
                    Logger.com("$TAG LogStreamManager停止成功")
                } else {
                    Logger.comE("$TAG LogStreamManager停止失败", stopResult.exceptionOrNull())
                }
            } else {
                Logger.com("$TAG LogStreamManager未运行，跳过停止")
            }

            // TODO: 停止其他模块

            Logger.com("$TAG 所有模块停止完成")

        } catch (e: Exception) {
            Logger.comE("$TAG 停止模块失败", e)
        }
    }
    
    /**
     * 清理所有模块实例
     */
    fun cleanup() {
        try {
            Logger.com("$TAG 清理模块实例")
            
            registryScope.launch {
                stopAllModules()
            }
            
            moduleInstances.clear()
            moduleInitialized.clear()
            applicationContext = null
            
            Logger.com("$TAG 模块实例清理完成")
            
        } catch (e: Exception) {
            Logger.comE("$TAG 清理模块实例失败", e)
        }
    }
    
    /**
     * 获取所有模块状态
     */
    fun getAllModuleStatus(): Map<String, Boolean> {
        return moduleInitialized.toMap()
    }
    
    /**
     * 重新初始化指定模块
     */
    suspend fun reinitializeModule(moduleName: String): Boolean {
        return try {
            Logger.com("$TAG 重新初始化模块: $moduleName")
            
            when (moduleName) {
                "LogStreamManager" -> {
                    // 移除旧实例
                    moduleInstances.remove(moduleName)
                    moduleInitialized.remove(moduleName)
                    
                    // 创建新实例
                    val newInstance = getLogStreamManager()
                    newInstance != null
                }
                else -> {
                    Logger.comW("$TAG 未知的模块名称: $moduleName")
                    false
                }
            }
            
        } catch (e: Exception) {
            Logger.comE("$TAG 重新初始化模块失败: $moduleName", e)
            false
        }
    }
}
