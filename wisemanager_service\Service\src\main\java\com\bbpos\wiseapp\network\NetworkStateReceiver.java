package com.bbpos.wiseapp.network;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import android.os.Build;

import com.bbpos.wiseapp.service.common.UsualData;
import com.bbpos.wiseapp.websocket.handler.ApnHandler;

public class NetworkStateReceiver extends BroadcastReceiver {
    public static int mNetworkState = 0;

    public void onReceive(Context context, Intent intent) {
        if (context == null) {
            throw new UnsupportedOperationException("context is null, check your code first please!");
        }

        ConnectivityManager connectivityManager = (ConnectivityManager) context.getSystemService(Context.CONNECTIVITY_SERVICE);
        if (connectivityManager != null) {
            NetworkInfo activeNetworkInfo = connectivityManager.getActiveNetworkInfo();
            if (activeNetworkInfo != null && activeNetworkInfo.isConnected()) {
                if (activeNetworkInfo.getType() == (ConnectivityManager.TYPE_WIFI)) {
                    mNetworkState = UsualData.NETWORK_STATUS_WIFI;
                } else if (activeNetworkInfo.getType() == (ConnectivityManager.TYPE_MOBILE)) {
                    mNetworkState = UsualData.NETWORK_STATUS_MOBILE;
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                        ApnHandler.checkIfApnAvailable();
                    }
                } else if (activeNetworkInfo.getType() == (ConnectivityManager.TYPE_ETHERNET)) {
                    mNetworkState = UsualData.NETWORK_STATUS_ETHERNET;
                }
            } else {
                mNetworkState = UsualData.NETWORK_STATUS_INAVAILABLE;
            }
        }

        if (NetworkStateListerner.getINetworkStateListener() != null) {
            NetworkStateListerner.getINetworkStateListener().getNetworkState(mNetworkState);
        }
    }
}
