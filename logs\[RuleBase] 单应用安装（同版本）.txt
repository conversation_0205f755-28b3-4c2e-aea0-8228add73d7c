2025-08-14 17:22:52.029 17358-17358 Common                  com.dspread.mdm.service              I  ✅ ⏰ 设置任务执行定时器成功，下次执行: 60秒后 (1分钟)
2025-08-14 17:22:58.862 17358-17430 Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"msg_state":"P","request_time":"1755163377733","data":{"ruleList":[{"deleteAppList":[],"modifyDate":"2025-08-14 09:22:57","endDate":"9999-12-31 23:59:59","restartAfter":"0","installBy":"0","beginDate":"2024-08-14 09:22:57","appList":[{"apkMd5":"54cc3a371939423d4288d63e1ac88019","apkName":"cl960","appId":"f32ac615171249418e3fc4b5bc2f4773","apkSize":"6155416","appIconUrl":"https://siot-bucket01.s3.ap-northeast-1.amazonaws.com/icon/c940c121d9c04565a85e36fb5665fac3ic_launcher.png","packName":"com.chileaf.cl960.sample","versionName":"1.1.0","versionCode":"1","url":"https://siot-bucket01.s3.ap-northeast-1.amazonaws.com/app/42e16b87eaa849bcaaf0f5fadcac6aab.apk"}],"serviceList":[],"action":"A","ruleName":"d60_test_26","ruleId":"4b5f2b2a4edd45d883d20ffc6bd27725","createDate":"2025-08-14 09:22:57","orgRuleId":""}]},"tranCode":"ST005","request_id":"1755163377733ST005","version":"1","serialNo":"01354090202503050399"}
2025-08-14 17:22:58.873 17358-17430 WebSocket               com.dspread.mdm.service              I  🔧 准备发送响应确认 - requestId: 1755163377733ST005, needResponse: true
2025-08-14 17:22:58.898 17358-17430 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0000","serialNo":"01354090202503050399","request_time":"1755163378883","request_id":"1755163378883C0000","version":"1","org_request_id":"1755163377733ST005","org_request_time":"1755163377733","response_state":"0","myVersionName":"1.0.08.20250814.DSPREAD.MDM.SERVICE","terminalDate":"20250814172258"}
2025-08-14 17:22:58.921 17358-17430 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0000","serialNo":"01354090202503050399","request_time":"1755163378907","request_id":"1755163378907C0000","version":"1","org_request_id":"1755163377733ST005","org_request_time":"1755163377733","response_state":"0","myVersionName":"1.0.08.20250814.DSPREAD.MDM.SERVICE","terminalDate":"20250814172258"}
2025-08-14 17:22:58.927 17358-17430 WebSocket               com.dspread.mdm.service              I  🔧 已发送C0000响应确认: requestId=1755163377733ST005
2025-08-14 17:22:58.939 17358-17430 RuleBase                com.dspread.mdm.service              D  🔧 RuleHandler 处理规则消息: ST005
2025-08-14 17:22:58.947 17358-17430 RuleBase                com.dspread.mdm.service              D  🔧 RuleHandler 处理规则列表，数量: 1
2025-08-14 17:22:58.954 17358-17430 RuleBase                com.dspread.mdm.service              D  🔧 RuleHandler 处理规则: ruleId=4b5f2b2a4edd45d883d20ffc6bd27725, action=A
2025-08-14 17:22:58.966 17358-17430 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析规则JSON: ruleId=4b5f2b2a4edd45d883d20ffc6bd27725
2025-08-14 17:22:58.977 17358-17430 RuleBase                com.dspread.mdm.service              D  🔧 Rule appList数组: [{"apkMd5":"54cc3a371939423d4288d63e1ac88019","apkName":"cl960","appId":"f32ac615171249418e3fc4b5bc2f4773","apkSize":"6155416","appIconUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/icon\/c940c121d9c04565a85e36fb5665fac3ic_launcher.png","packName":"com.chileaf.cl960.sample","versionName":"1.1.0","versionCode":"1","url":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/42e16b87eaa849bcaaf0f5fadcac6aab.apk"}]
2025-08-14 17:22:58.983 17358-17430 RuleBase                com.dspread.mdm.service              D  🔧 Rule deleteAppList数组: []
2025-08-14 17:22:58.991 17358-17430 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=com.chileaf.cl960.sample, apkName=cl960
2025-08-14 17:22:58.998 17358-17430 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后appList数量: 1
2025-08-14 17:22:59.004 17358-17430 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后deleteAppList数量: 0
2025-08-14 17:22:59.012 17358-17430 RuleBase                com.dspread.mdm.service              D  🔧 RuleBaseStorage 处理规则: 4b5f2b2a4edd45d883d20ffc6bd27725, 操作: A
2025-08-14 17:22:59.018 17358-17430 RuleBase                com.dspread.mdm.service              D  🔧 Rule 验证规则: ruleId=4b5f2b2a4edd45d883d20ffc6bd27725, ruleType=app_management, action=A
2025-08-14 17:22:59.025 17358-17430 RuleBase                com.dspread.mdm.service              D  🔧 Rule appList数量: 1, deleteAppList数量: 0
2025-08-14 17:22:59.048 17358-17430 RuleBase                com.dspread.mdm.service              D  🔧 Rule 验证应用管理规则: action=A
2025-08-14 17:22:59.055 17358-17430 RuleBase                com.dspread.mdm.service              D  🔧 Rule 验证应用列表: appList=1, deleteAppList=0
2025-08-14 17:22:59.062 17358-17430 RuleBase                com.dspread.mdm.service              D  🔧 Rule 应用列表验证通过
2025-08-14 17:22:59.077 17358-17430 RuleBase                com.dspread.mdm.service              D  🔧 RuleBaseStorage 保存 1 个规则到存储
2025-08-14 17:22:59.084 17358-17430 RuleBase                com.dspread.mdm.service              D  🔧 RuleBaseStorage Rule 4b5f2b2a4edd45d883d20ffc6bd27725 添加成功
2025-08-14 17:22:59.090 17358-17430 RuleBase                com.dspread.mdm.service              D  🔧 RuleBaseStorage 触发规则执行: 4b5f2b2a4edd45d883d20ffc6bd27725
2025-08-14 17:22:59.098 17358-17389 RuleBase                com.dspread.mdm.service              D  🔧 RuleBaseStorage 设备空闲，立即执行规则: 4b5f2b2a4edd45d883d20ffc6bd27725
2025-08-14 17:22:59.098 17358-17430 RuleBase                com.dspread.mdm.service              D  🔧 RuleHandler 规则处理成功: 4b5f2b2a4edd45d883d20ffc6bd27725
2025-08-14 17:22:59.105 17358-17389 RuleBase                com.dspread.mdm.service              D  🔧 RuleBaseManager 直接执行规则: 4b5f2b2a4edd45d883d20ffc6bd27725
2025-08-14 17:22:59.105 17358-17430 RuleBase                com.dspread.mdm.service              D  🔧 RuleHandler 规则列表处理完成: 处理完成 - 成功: 1, 失败: 0, 忽略: 0
2025-08-14 17:22:59.111 17358-17430 RuleBase                com.dspread.mdm.service              D  🔧 RuleHandler 上报规则处理结果: 0 - 处理完成 - 成功: 1, 失败: 0, 忽略: 0
2025-08-14 17:22:59.112 17358-17389 RuleBase                com.dspread.mdm.service              D  🔧 RuleBaseManager 处理规则: 4b5f2b2a4edd45d883d20ffc6bd27725, 操作: A
2025-08-14 17:22:59.118 17358-17430 WebSocket               com.dspread.mdm.service              I  🔧 C0107 主动式上送: rulebase_update (主动: 5)
2025-08-14 17:22:59.119 17358-17389 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析规则JSON: ruleId=4b5f2b2a4edd45d883d20ffc6bd27725
2025-08-14 17:22:59.128 17358-17389 RuleBase                com.dspread.mdm.service              D  🔧 Rule appList数组: [{"packName":"com.chileaf.cl960.sample","apkName":"cl960","versionName":"1.1.0","versionCode":"1","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/42e16b87eaa849bcaaf0f5fadcac6aab.apk","apkMd5":"54cc3a371939423d4288d63e1ac88019","apkSize":6155416,"installBy":"0","isSystemApp":false,"priority":0}]
2025-08-14 17:22:59.135 17358-17389 RuleBase                com.dspread.mdm.service              D  🔧 Rule deleteAppList数组: []
2025-08-14 17:22:59.139 17358-17430 WebSocket               com.dspread.mdm.service              I  🔧 添加规则结果: ruleId=batch_rules
2025-08-14 17:22:59.141 17358-17389 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=com.chileaf.cl960.sample, apkName=cl960
2025-08-14 17:22:59.147 17358-17389 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后appList数量: 1
2025-08-14 17:22:59.154 17358-17389 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后deleteAppList数量: 0
2025-08-14 17:22:59.155 17358-17430 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0107","serialNo":"01354090202503050399","request_time":"1755163379133","request_id":"1755163379133C0107","version":"1","data":{"ruleId":"batch_rules","taskResult":"0"},"myVersionName":"1.0.08.20250814.DSPREAD.MDM.SERVICE","terminalDate":"20250814172259","org_request_id":"1755163377733ST005","org_request_time":"1755163377733"}
2025-08-14 17:22:59.160 17358-17389 RuleBase                com.dspread.mdm.service              D  🔧 RuleBaseStorage 处理规则: 4b5f2b2a4edd45d883d20ffc6bd27725, 操作: A
2025-08-14 17:22:59.162 17358-17430 WebSocket               com.dspread.mdm.service              I  🔧 规则结果实时上送: ruleId=batch_rules, result=0 (1)
2025-08-14 17:22:59.167 17358-17389 RuleBase                com.dspread.mdm.service              D  🔧 Rule 验证规则: ruleId=4b5f2b2a4edd45d883d20ffc6bd27725, ruleType=app_management, action=A
2025-08-14 17:22:59.173 17358-17389 RuleBase                com.dspread.mdm.service              D  🔧 Rule appList数量: 1, deleteAppList数量: 0
2025-08-14 17:22:59.196 17358-17389 RuleBase                com.dspread.mdm.service              D  🔧 Rule 验证应用管理规则: action=A
2025-08-14 17:22:59.202 17358-17389 RuleBase                com.dspread.mdm.service              D  🔧 Rule 验证应用列表: appList=1, deleteAppList=0
2025-08-14 17:22:59.209 17358-17389 RuleBase                com.dspread.mdm.service              D  🔧 Rule 应用列表验证通过
2025-08-14 17:22:59.219 17358-17389 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析规则JSON: ruleId=4b5f2b2a4edd45d883d20ffc6bd27725
2025-08-14 17:22:59.229 17358-17389 RuleBase                com.dspread.mdm.service              D  🔧 Rule appList数组: [{"packName":"com.chileaf.cl960.sample","apkName":"cl960","versionName":"1.1.0","versionCode":"1","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/42e16b87eaa849bcaaf0f5fadcac6aab.apk","apkMd5":"54cc3a371939423d4288d63e1ac88019","apkSize":6155416,"installBy":"0","isSystemApp":false,"priority":0}]
2025-08-14 17:22:59.236 17358-17389 RuleBase                com.dspread.mdm.service              D  🔧 Rule deleteAppList数组: []
2025-08-14 17:22:59.242 17358-17389 RuleBase                com.dspread.mdm.service              D  🔧 RuleApp解析: packName=com.chileaf.cl960.sample, apkName=cl960
2025-08-14 17:22:59.249 17358-17389 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后appList数量: 1
2025-08-14 17:22:59.255 17358-17389 RuleBase                com.dspread.mdm.service              D  🔧 Rule 解析后deleteAppList数量: 0
2025-08-14 17:22:59.262 17358-17389 RuleBase                com.dspread.mdm.service              D  🔧 RuleBaseStorage Rule 4b5f2b2a4edd45d883d20ffc6bd27725 已存在，忽略Add操作
2025-08-14 17:22:59.270 17358-17389 RuleBase                com.dspread.mdm.service              D  🔧 RuleStateMachine 规则初始化成功: 4b5f2b2a4edd45d883d20ffc6bd27725 -> RuleState(code=todo, description=等待执行)
2025-08-14 17:22:59.276 17358-17389 RuleBase                com.dspread.mdm.service              D  🔧 RuleStateMachine 规则已存在: 4b5f2b2a4edd45d883d20ffc6bd27725, 当前状态: RuleState(code=todo, description=等待执行)
2025-08-14 17:22:59.284 17358-17389 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 创建应用状态: packName=com.chileaf.cl960.sample, apkName=cl960
2025-08-14 17:22:59.291 17358-17389 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 初始化规则应用状态: 4b5f2b2a4edd45d883d20ffc6bd27725, 应用数量: 1
2025-08-14 17:22:59.298 17358-17389 RuleBase                com.dspread.mdm.service              D  🔧 RuleStateMonitor 收到状态变化: 4b5f2b2a4edd45d883d20ffc6bd27725, todo -> R01
2025-08-14 17:22:59.305 17358-17389 RuleBase                com.dspread.mdm.service              D  🔧 RuleStateMonitor 安排延迟上报: 4b5f2b2a4edd45d883d20ffc6bd27725, R01
2025-08-14 17:22:59.312 17358-17389 RuleBase                com.dspread.mdm.service              D  🔧 RuleStateMachine 状态转换成功: 4b5f2b2a4edd45d883d20ffc6bd27725, todo -> R01
2025-08-14 17:22:59.318 17358-17389 RuleBase                com.dspread.mdm.service              D  🔧 RuleStateMonitor 收到状态变化: 4b5f2b2a4edd45d883d20ffc6bd27725, R01 -> R02
2025-08-14 17:22:59.325 17358-17389 RuleBase                com.dspread.mdm.service              D  🔧 RuleStateMonitor 立即上报状态: 4b5f2b2a4edd45d883d20ffc6bd27725, R02
2025-08-14 17:22:59.332 17358-17389 WebSocket               com.dspread.mdm.service              I  🔧 C0107 主动式上送: rulebase_update (主动: 6)
2025-08-14 17:22:59.352 17358-17389 WebSocket               com.dspread.mdm.service              I  🔧 添加规则结果: ruleId=4b5f2b2a4edd45d883d20ffc6bd27725
2025-08-14 17:22:59.367 17358-17389 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0107","serialNo":"01354090202503050399","request_time":"1755163379347","request_id":"1755163379347C0107","version":"1","data":{"ruleId":"4b5f2b2a4edd45d883d20ffc6bd27725","taskResult":"R02"},"myVersionName":"1.0.08.20250814.DSPREAD.MDM.SERVICE","terminalDate":"20250814172259"}
2025-08-14 17:22:59.373 17358-17389 WebSocket               com.dspread.mdm.service              I  🔧 规则结果实时上送: ruleId=4b5f2b2a4edd45d883d20ffc6bd27725, result=R02 (1)
2025-08-14 17:22:59.380 17358-17389 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 上报规则状态: 4b5f2b2a4edd45d883d20ffc6bd27725 -> R02
2025-08-14 17:22:59.386 17358-17389 RuleBase                com.dspread.mdm.service              D  🔧 RuleStateMachine 状态转换成功: 4b5f2b2a4edd45d883d20ffc6bd27725, R01 -> R02
2025-08-14 17:22:59.394 17358-17389 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 开始执行规则: 4b5f2b2a4edd45d883d20ffc6bd27725
2025-08-14 17:22:59.400 17358-17389 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 执行安装应用，数量: 1
2025-08-14 17:22:59.406 17358-17389 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 开始安装应用，数量: 1
2025-08-14 17:22:59.413 17358-17389 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 安装应用: com.chileaf.cl960.sample, URL: https://siot-bucket01.s3.ap-northeast-1.amazonaws.com/app/42e16b87eaa849bcaaf0f5fadcac6aab.apk
2025-08-14 17:22:59.420 17358-17389 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 检查应用安装状态: com.chileaf.cl960.sample
2025-08-14 17:22:59.426 17358-17389 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 应用是否已安装: true
2025-08-14 17:22:59.437 17358-17389 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 已安装应用信息: AppInfo(packageName=com.chileaf.cl960.sample, appName=cl960, versionName=1.1.0, versionCode=1, installPath=/data/app/~~fVLkmyz7g2Ye6n2LHwHsaQ==/com.chileaf.cl960.sample-QWVY2u71T8Md1oQ3FGP1Dg==/base.apk, isSystemApp=false, isEnabled=true, firstInstallTime=1755162372574, lastUpdateTime=1755162372574)
2025-08-14 17:22:59.444 17358-17389 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 版本比较: 已安装=1.1.0(1) vs 规则=1.1.0(1)
2025-08-14 17:22:59.450 17358-17389 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 版本匹配结果: true
2025-08-14 17:22:59.457 17358-17389 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 应用已安装且版本一致: com.chileaf.cl960.sample
2025-08-14 17:22:59.463 17358-17389 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 更新应用状态: 4b5f2b2a4edd45d883d20ffc6bd27725, com.chileaf.cl960.sample -> B03
2025-08-14 17:22:59.470 17358-17389 WebSocket               com.dspread.mdm.service              I  🔧 C0107 主动式上送: rulebase_update (主动: 7)
2025-08-14 17:22:59.490 17358-17389 WebSocket               com.dspread.mdm.service              I  🔧 添加规则结果: ruleId=4b5f2b2a4edd45d883d20ffc6bd27725
2025-08-14 17:22:59.514 17358-17430 Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"request_time":"1755163378282","org_request_time":"1755163379133","org_request_id":"1755163379133C0107","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755163378282S0000","serialNo":"01354090202503050399"}
2025-08-14 17:22:59.514 17358-17389 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0107","serialNo":"01354090202503050399","request_time":"1755163379485","request_id":"1755163379485C0107","version":"1","data":{"ruleId":"4b5f2b2a4edd45d883d20ffc6bd27725","taskResult":"R02","successApkList":[{"packName":"com.chileaf.cl960.sample","apkName":"cl960","versionName":"1.1.0","versionCode":"1","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/42e16b87eaa849bcaaf0f5fadcac6aab.apk","apkMd5":"54cc3a371939423d4288d63e1ac88019","apkSize":6155416,"installBy":"0","isSystemApp":false,"priority":0,"result":"B03","taskType":"01","msg":"INSTALLATION SUCCESS"}]},"myVersionName":"1.0.08.20250814.DSPREAD.MDM.SERVICE","terminalDate":"20250814172259"}
2025-08-14 17:22:59.521 17358-17389 WebSocket               com.dspread.mdm.service              I  🔧 规则结果实时上送: ruleId=4b5f2b2a4edd45d883d20ffc6bd27725, result=R02 (1)
2025-08-14 17:22:59.524 17358-17430 WebSocket               com.dspread.mdm.service              I  🔧 收到响应消息: transCode=S0000, orgRequestId=1755163379133C0107, state=0, remark=
2025-08-14 17:22:59.528 17358-17389 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 上报规则执行中状态: 4b5f2b2a4edd45d883d20ffc6bd27725 (失败:0, 成功:1)
2025-08-14 17:22:59.531 17358-17430 WebSocket               com.dspread.mdm.service              I  🔧 处理规则结果响应: state=0
2025-08-14 17:22:59.535 17358-17389 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 开始检查规则完成状态: 4b5f2b2a4edd45d883d20ffc6bd27725, 总应用数: 1
2025-08-14 17:22:59.537 17358-17430 WebSocket               com.dspread.mdm.service              I  🔧 规则结果上传成功
2025-08-14 17:22:59.541 17358-17389 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 检查应用状态: com.chileaf.cl960.sample -> B03
2025-08-14 17:22:59.548 17358-17389 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 发现成功状态: com.chileaf.cl960.sample -> B03
2025-08-14 17:22:59.554 17358-17389 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 规则完成检查结果: 4b5f2b2a4edd45d883d20ffc6bd27725, 全部完成: true, 完成数: 1/1, 有失败: false
2025-08-14 17:22:59.561 17358-17389 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 规则执行完成，当前状态: R02, 目标状态: R03
2025-08-14 17:22:59.568 17358-17389 RuleBase                com.dspread.mdm.service              D  🔧 RuleStateMonitor 收到状态变化: 4b5f2b2a4edd45d883d20ffc6bd27725, R02 -> R03
2025-08-14 17:22:59.574 17358-17389 RuleBase                com.dspread.mdm.service              D  🔧 RuleStateMonitor 立即上报状态: 4b5f2b2a4edd45d883d20ffc6bd27725, R03
2025-08-14 17:22:59.581 17358-17389 WebSocket               com.dspread.mdm.service              I  🔧 C0107 主动式上送: rulebase_update (主动: 8)
2025-08-14 17:22:59.601 17358-17389 WebSocket               com.dspread.mdm.service              I  🔧 添加规则结果: ruleId=4b5f2b2a4edd45d883d20ffc6bd27725
2025-08-14 17:22:59.617 17358-17389 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0107","serialNo":"01354090202503050399","request_time":"1755163379596","request_id":"1755163379596C0107","version":"1","data":{"ruleId":"4b5f2b2a4edd45d883d20ffc6bd27725","taskResult":"R03"},"myVersionName":"1.0.08.20250814.DSPREAD.MDM.SERVICE","terminalDate":"20250814172259"}
2025-08-14 17:22:59.623 17358-17389 WebSocket               com.dspread.mdm.service              I  🔧 规则结果实时上送: ruleId=4b5f2b2a4edd45d883d20ffc6bd27725, result=R03 (1)
2025-08-14 17:22:59.630 17358-17389 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 上报规则状态: 4b5f2b2a4edd45d883d20ffc6bd27725 -> R03
2025-08-14 17:22:59.636 17358-17430 Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"request_time":"1755163378492","org_request_time":"1755163379347","org_request_id":"1755163379347C0107","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755163378492S0000","serialNo":"01354090202503050399"}
2025-08-14 17:22:59.637 17358-17389 RuleBase                com.dspread.mdm.service              D  🔧 RuleStateMachine 状态转换成功: 4b5f2b2a4edd45d883d20ffc6bd27725, R02 -> R03
2025-08-14 17:22:59.643 17358-17389 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 状态机状态转换成功: R02 -> R03
2025-08-14 17:22:59.645 17358-17430 WebSocket               com.dspread.mdm.service              I  🔧 收到响应消息: transCode=S0000, orgRequestId=1755163379347C0107, state=0, remark=
2025-08-14 17:22:59.650 17358-17389 WebSocket               com.dspread.mdm.service              I  🔧 C0107 主动式上送: rulebase_update (主动: 9)
2025-08-14 17:22:59.652 17358-17430 WebSocket               com.dspread.mdm.service              I  🔧 处理规则结果响应: state=0
2025-08-14 17:22:59.658 17358-17430 WebSocket               com.dspread.mdm.service              I  🔧 规则结果上传成功
2025-08-14 17:22:59.670 17358-17389 WebSocket               com.dspread.mdm.service              I  🔧 添加规则结果: ruleId=4b5f2b2a4edd45d883d20ffc6bd27725
2025-08-14 17:22:59.692 17358-17430 Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"request_time":"1755163378634","org_request_time":"1755163379485","org_request_id":"1755163379485C0107","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755163378634S0000","serialNo":"01354090202503050399"}
2025-08-14 17:22:59.694 17358-17389 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0107","serialNo":"01354090202503050399","request_time":"1755163379665","request_id":"1755163379665C0107","version":"1","data":{"ruleId":"4b5f2b2a4edd45d883d20ffc6bd27725","taskResult":"R03","successApkList":[{"packName":"com.chileaf.cl960.sample","apkName":"cl960","versionName":"1.1.0","versionCode":"1","apkUrl":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/app\/42e16b87eaa849bcaaf0f5fadcac6aab.apk","apkMd5":"54cc3a371939423d4288d63e1ac88019","apkSize":6155416,"installBy":"0","isSystemApp":false,"priority":0,"result":"B03","taskType":"01","msg":"INSTALLATION SUCCESS"}]},"myVersionName":"1.0.08.20250814.DSPREAD.MDM.SERVICE","terminalDate":"20250814172259"}
2025-08-14 17:22:59.701 17358-17389 WebSocket               com.dspread.mdm.service              I  🔧 规则结果实时上送: ruleId=4b5f2b2a4edd45d883d20ffc6bd27725, result=R03 (1)
2025-08-14 17:22:59.702 17358-17430 WebSocket               com.dspread.mdm.service              I  🔧 收到响应消息: transCode=S0000, orgRequestId=1755163379485C0107, state=0, remark=
2025-08-14 17:22:59.708 17358-17389 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 规则执行完成: 4b5f2b2a4edd45d883d20ffc6bd27725, 最终状态: R03
2025-08-14 17:22:59.709 17358-17430 WebSocket               com.dspread.mdm.service              I  🔧 处理规则结果响应: state=0
2025-08-14 17:22:59.715 17358-17389 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 规则执行启动成功: 4b5f2b2a4edd45d883d20ffc6bd27725
2025-08-14 17:22:59.715 17358-17430 WebSocket               com.dspread.mdm.service              I  🔧 规则结果上传成功
2025-08-14 17:22:59.721 17358-17389 RuleBase                com.dspread.mdm.service              D  🔧 RuleBaseManager 规则添加成功: 4b5f2b2a4edd45d883d20ffc6bd27725
2025-08-14 17:22:59.749 17358-17430 Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"request_time":"1755163378740","org_request_time":"1755163379596","org_request_id":"1755163379596C0107","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755163378740S0000","serialNo":"01354090202503050399"}
2025-08-14 17:22:59.759 17358-17430 WebSocket               com.dspread.mdm.service              I  🔧 收到响应消息: transCode=S0000, orgRequestId=1755163379596C0107, state=0, remark=
2025-08-14 17:22:59.765 17358-17430 WebSocket               com.dspread.mdm.service              I  🔧 处理规则结果响应: state=0
2025-08-14 17:22:59.772 17358-17430 WebSocket               com.dspread.mdm.service              I  🔧 规则结果上传成功
2025-08-14 17:22:59.854 17358-17430 Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"request_time":"1755163378833","org_request_time":"1755163379665","org_request_id":"1755163379665C0107","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755163378833S0000","serialNo":"01354090202503050399"}
2025-08-14 17:22:59.863 17358-17430 WebSocket               com.dspread.mdm.service              I  🔧 收到响应消息: transCode=S0000, orgRequestId=1755163379665C0107, state=0, remark=
2025-08-14 17:22:59.870 17358-17430 WebSocket               com.dspread.mdm.service              I  🔧 处理规则结果响应: state=0
2025-08-14 17:22:59.876 17358-17430 WebSocket               com.dspread.mdm.service              I  🔧 规则结果上传成功
