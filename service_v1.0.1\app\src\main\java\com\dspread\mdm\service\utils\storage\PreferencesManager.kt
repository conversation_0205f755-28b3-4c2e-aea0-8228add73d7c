package com.dspread.mdm.service.utils.storage

import android.content.Context
import android.content.SharedPreferences
import com.dspread.mdm.service.SmartMdmServiceApp

/**
 * SharedPreferences 管理器
 */
object PreferencesManager {
    
    private const val PREF_NAME = "smart_mdm_service_prefs"
    
    private val sharedPreferences: SharedPreferences by lazy {
        SmartMdmServiceApp.instance.getSharedPreferences(PREF_NAME, Context.MODE_PRIVATE)
    }
    
    /**
     * 存储字符串值
     */
    fun putString(key: String, value: String) {
        sharedPreferences.edit().putString(key, value).apply()
    }
    
    /**
     * 获取字符串值
     */
    fun getString(key: String, defaultValue: String = ""): String {
        return sharedPreferences.getString(key, defaultValue) ?: defaultValue
    }
    
    /**
     * 存储布尔值
     */
    fun putBoolean(key: String, value: Boolean) {
        sharedPreferences.edit().putBoolean(key, value).apply()
    }
    
    /**
     * 获取布尔值
     */
    fun getBoolean(key: String, defaultValue: Boolean = false): Boolean {
        return sharedPreferences.getBoolean(key, defaultValue)
    }
    
    /**
     * 存储整数值
     */
    fun putInt(key: String, value: Int) {
        sharedPreferences.edit().putInt(key, value).apply()
    }
    
    /**
     * 获取整数值
     */
    fun getInt(key: String, defaultValue: Int = 0): Int {
        return sharedPreferences.getInt(key, defaultValue)
    }
    
    /**
     * 存储长整数值
     */
    fun putLong(key: String, value: Long) {
        sharedPreferences.edit().putLong(key, value).apply()
    }
    
    /**
     * 获取长整数值
     */
    fun getLong(key: String, defaultValue: Long = 0L): Long {
        return sharedPreferences.getLong(key, defaultValue)
    }
    
    /**
     * 存储浮点数值
     */
    fun putFloat(key: String, value: Float) {
        sharedPreferences.edit().putFloat(key, value).apply()
    }
    
    /**
     * 获取浮点数值
     */
    fun getFloat(key: String, defaultValue: Float = 0f): Float {
        return sharedPreferences.getFloat(key, defaultValue)
    }
    
    /**
     * 删除指定键的值
     */
    fun remove(key: String) {
        sharedPreferences.edit().remove(key).apply()
    }
    
    /**
     * 清空所有数据
     */
    fun clear() {
        sharedPreferences.edit().clear().apply()
    }
    
    /**
     * 检查是否包含指定键
     */
    fun contains(key: String): Boolean {
        return sharedPreferences.contains(key)
    }
}
