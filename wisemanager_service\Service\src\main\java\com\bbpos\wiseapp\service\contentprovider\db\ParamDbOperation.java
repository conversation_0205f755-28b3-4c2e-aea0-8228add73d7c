package com.bbpos.wiseapp.service.contentprovider.db;

import android.content.ContentValues;
import android.content.Context;
import android.database.Cursor;
import android.database.SQLException;
import android.database.sqlite.SQLiteDatabase;
import android.util.Log;

import com.bbpos.wiseapp.logger.BBLog;
import com.bbpos.wiseapp.param.manager.ParamModel;
import com.bbpos.wiseapp.param.manager.ParamPathModel;
import com.bbpos.wiseapp.tms.utils.Constants;
import com.bbpos.wiseapp.tms.utils.Helpers;

import java.util.ArrayList;
import java.util.List;

public class ParamDbOperation {
//	private static SQLiteDatabase mSQLiteDatabase = null;
	private static ParamDbHelper mInstanceDb=null;
	
	public static ParamDbHelper open(Context mContext) throws SQLException {
		if(mInstanceDb == null){
			mInstanceDb = new ParamDbHelper(mContext.getApplicationContext());
		}
		return mInstanceDb;
	}	
	
	/**
	 * 插入参数数据  如果param_key已存在就更新
	 * @param param_key
	 * @param param_value
	 * @return
	 */
	public static boolean updateParams(Context mContext,String pkgName,List<ParamModel> paramList) {
		open(mContext);
		boolean isSuccessed = true;
		String list_key = "";
		for (int i = 0; i < paramList.size(); i++) {
			BBLog.v(BBLog.TAG,paramList.get(i).key+"-"+paramList.get(i).value);
			paramList.get(i).pkgName = pkgName;
			if(isParamExist(pkgName,paramList.get(i).key))
			{
				isSuccessed = updateParam(paramList.get(i));
			}else{
				isSuccessed = insertParam(paramList.get(i));
			}
			
			if (i == (paramList.size()-1)) {
				list_key += "'" + paramList.get(i).key + "'";
			} else {
				list_key += "'" + paramList.get(i).key + "',";
			}
		}
		deleteParamsNotNeed(pkgName, list_key);
		return isSuccessed;
	}
	
	private static boolean updateParam(ParamModel paramModel){
		boolean ret = false;
		SQLiteDatabase mSQLiteDatabase = mInstanceDb.getWritableDatabase();
		BBLog.v(BBLog.TAG, "paramModel:"+paramModel);
		ContentValues initialValues = new ContentValues();
		initialValues.put("idx", paramModel.idx);
		initialValues.put("pkg_name", paramModel.pkgName);
		initialValues.put("param_key", paramModel.key);
		initialValues.put("param_value", paramModel.value);
		initialValues.put("data_src", paramModel.dataSrc);
		if ("D".equals(paramModel.dataSrc)) {
			ret = mSQLiteDatabase.update(ParamDbHelper.TBL_PARAM_NAME, initialValues, "param_key" + "= '"
					+ paramModel.key + "' and pkg_name = '" + paramModel.pkgName + "'", null) > 0;
		} else {
			ret = mSQLiteDatabase.update(ParamDbHelper.TBL_PARAM_NAME, initialValues, "param_key" + "= '"
					+ paramModel.key + "' and pkg_name = '" + paramModel.pkgName + "' and data_src != 'D'", null) > 0;
		}
		
		mSQLiteDatabase.close();
		return ret;
	}
	
	private static boolean insertParam(ParamModel paramModel){
		SQLiteDatabase mSQLiteDatabase = mInstanceDb.getWritableDatabase();
		BBLog.v(BBLog.TAG, "paramModel:"+paramModel);
		ContentValues initialValues = new ContentValues();
		initialValues.put("idx", paramModel.idx);
		initialValues.put("pkg_name", paramModel.pkgName);
		initialValues.put("param_key", paramModel.key);
		initialValues.put("param_value", paramModel.value);
		initialValues.put("data_src", paramModel.dataSrc);
		boolean ret = mSQLiteDatabase.insert(ParamDbHelper.TBL_PARAM_NAME, "_id", initialValues) > 0;
		mSQLiteDatabase.close();
		return ret;
	}

	private static int deleteParamsNotNeed(String pkgName, String list_key){
		SQLiteDatabase mSQLiteDatabase = mInstanceDb.getWritableDatabase();
		BBLog.v(BBLog.TAG, "deleteAppParams pkgName:"+pkgName+" ,list_key:" + list_key);
		int ret = mSQLiteDatabase.delete(ParamDbHelper.TBL_PARAM_NAME, "pkg_name=? and param_key not in(" + list_key + ")", new String[]{pkgName});
		mSQLiteDatabase.close();
		return ret;
	}
	
	/**
	 * 判断 type是否已存在
	 * @param param_key
	 * @return
	 * @throws SQLException
	 */
	private static boolean isParamExist(String pkgname,String param_key) throws SQLException {
		SQLiteDatabase mSQLiteDatabase = mInstanceDb.getReadableDatabase();
		boolean result=false;
		Cursor mCursor = mSQLiteDatabase.query(true, ParamDbHelper.TBL_PARAM_NAME,
				new String[] {"param_value"}, "param_key = '"+param_key
				+"' and pkg_name = '"+pkgname+"'", null, null, null, null, null);
		if (mCursor != null && mCursor.getCount() > 0) {
			result=true;
		}
		mCursor.close();
		mSQLiteDatabase.close();
		return  result;
	}

	/**
	 * 判断 是否存在參數
	 * @param pkgname
	 * @return
	 * @throws SQLException
	 */
	public static List<ParamModel> getAppParams(String pkgname) throws SQLException {
		List<ParamModel> paramList = new ArrayList<ParamModel>();
		SQLiteDatabase mSQLiteDatabase = mInstanceDb.getReadableDatabase();
		Cursor mCursor = mSQLiteDatabase.query(true, ParamDbHelper.TBL_PARAM_NAME,
				new String[] {"idx", "pkg_name", "param_key", "param_value", "data_src"}, "pkg_name = '"+pkgname+"'", null, null, null, null, null);
		if (mCursor != null && mCursor.getCount() > 0) {
			mCursor.moveToFirst();
			while (!mCursor.isAfterLast()) {
				String idx = mCursor.getString(mCursor.getColumnIndex(ParamModel.IDX_COLOUMN));
				String pkg_name = mCursor.getString(mCursor.getColumnIndex(ParamModel.PKG_NAME_COLOUMN));
				String param_key = mCursor.getString(mCursor.getColumnIndex(ParamModel.KEY_COLOUMN));
				String param_value  = mCursor.getString(mCursor.getColumnIndex(ParamModel.VALUE_COLOUMN));
				String data_src  = mCursor.getString(mCursor.getColumnIndex(ParamModel.DATASRC_COLOUMN));
				paramList.add(new ParamModel(idx, pkg_name, param_key, param_value, data_src));
				mCursor.moveToNext();
			}
		}
		mCursor.close();
		mSQLiteDatabase.close();
		return  paramList;
	}

	public static boolean updateParamFile(Context mContext, ParamPathModel paramPath) {
		if (!isParamFileParsed(mContext, paramPath.fileMd5)) {
			return insertParamFile(mContext, paramPath);
		}
		open(mContext);
		SQLiteDatabase mSQLiteDatabase = mInstanceDb.getWritableDatabase();
		ContentValues initialValues = new ContentValues();
		initialValues.put("param_file_path", paramPath.filePath);
		initialValues.put("param_file_md5", paramPath.fileMd5);
		initialValues.put("param_file_last_modify_time", paramPath.lastModifyTime);
		initialValues.put("param_file_parse_stat", paramPath.parseStat);
		boolean result = mSQLiteDatabase.update(ParamDbHelper.TBL_PARAM_PATH_NAME, initialValues,
				"param_file_md5" + "= '" + paramPath.fileMd5 + "'", null) > 0;

				mInstanceDb.close();
				return result;
	}
	
	private static boolean insertParamFile(Context mContext, ParamPathModel paramPath){
		open(mContext);
		SQLiteDatabase mSQLiteDatabase = mInstanceDb.getWritableDatabase();
		ContentValues initialValues = new ContentValues();
		initialValues.put("param_file_path", paramPath.filePath);
		initialValues.put("param_file_md5", paramPath.fileMd5);
		initialValues.put("param_file_last_modify_time", paramPath.lastModifyTime);
		initialValues.put("param_file_parse_stat", Constants.PARSED_STAT_NOTPARSE);
		boolean result = mSQLiteDatabase.insert(ParamDbHelper.TBL_PARAM_PATH_NAME, "_id", initialValues) > 0;
		return result;
	}
	
	public static boolean insertParamFile(Context mContext, ContentValues contentValues){
		open(mContext);
		SQLiteDatabase mSQLiteDatabase = mInstanceDb.getWritableDatabase();
		contentValues.put("param_file_parse_stat", Constants.PARSED_STAT_NOTPARSE);
		boolean result =mSQLiteDatabase.insert(ParamDbHelper.TBL_PARAM_PATH_NAME, "_id", contentValues) > 0;
		mSQLiteDatabase.close();
		return result;
	}
	
	/**参数文件是否已被解析过*/
	public static boolean isParamFileParsed(Context mContext, String fileMd5){
		open(mContext);
		boolean result=false;
		SQLiteDatabase mSQLiteDatabase = mInstanceDb.getReadableDatabase();
		Cursor mCursor = mSQLiteDatabase.query(true, ParamDbHelper.TBL_PARAM_PATH_NAME,
				new String[] {"param_file_md5"}, "param_file_md5 = '"+fileMd5
				+"'", null, null, null, null, null);			
		if (mCursor != null && mCursor.getCount() > 0) {
			result=true;
		}
		mCursor.close();
		mSQLiteDatabase.close();
		return  result;
	}

	/**
	 * 判断参数文件是否已被解析过
	 * @return
	 * @throws SQLException
	 */
	public static boolean isParamFileParsed(Context mContext, String filePath,String fileMd5,long lastModifyTime) throws SQLException {
		open(mContext);
		boolean result=false;
		SQLiteDatabase mSQLiteDatabase = mInstanceDb.getReadableDatabase();
		Cursor mCursor = mSQLiteDatabase.query(true, ParamDbHelper.TBL_PARAM_PATH_NAME,new String[] {"param_file_path"},
				"param_file_path = '"+filePath+"'"
				+ " and param_file_md5 = '"+fileMd5+"'"
				+ " and param_file_last_modify_time ="+lastModifyTime, null, null, null, null, null);			
		if (mCursor != null && mCursor.getCount() > 0) {
			result=true;
		}
		mCursor.close();
		mSQLiteDatabase.close();
		return  result;
	}
}
