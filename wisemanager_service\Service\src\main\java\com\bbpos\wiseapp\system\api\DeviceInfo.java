package com.bbpos.wiseapp.system.api;

import android.content.Context;
import android.os.Build;
import android.text.TextUtils;

import java.lang.reflect.Method;

public class DeviceInfo {

    // 获取硬件序列号
    public DeviceInfo(Context ctx) {

    }

    public String getSn() {
        String serial = null;

        try {
            Class<?> c =Class.forName("android.os.SystemProperties");
            Method get =c.getMethod("get", String.class);
            serial = (String)get.invoke(c, "ro.serialno");
        } catch (Exception e) {
            e.printStackTrace();
        }

        if (TextUtils.isEmpty(serial)) {
            serial = Build.SERIAL;
        }

        return serial;
    }

    // 获取系统硬件制造商
    public String getManufacturer() {
        return Build.MANUFACTURER;
    }

    // 获取系统硬件版本
    public String getHardwareVer() {
        return Build.HARDWARE;
    }

    // 获取系统型号
    public String getOSModel() {
        return Build.MODEL;
    }

    // 获取系统版本
    public String getOsVersion() {
        return Build.VERSION.RELEASE;
    }

    // 获取sdk版本
    public String getSdkVersion() {
        return Build.VERSION.SDK;
    }
}