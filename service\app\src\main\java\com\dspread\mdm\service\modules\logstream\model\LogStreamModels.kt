package com.dspread.mdm.service.modules.logstream.model

import org.json.JSONArray
import org.json.JSONObject
import java.io.File
import java.text.SimpleDateFormat
import java.util.*

/**
 * 日志流配置数据类
 */
data class LogStreamConfig(
    val enabled: Boolean,                    // 是否启用日志流
    val uploadUrl: String,                   // 上传URL
    val uploadInterval: Long,                // 上传间隔（毫秒）
    val maxFileSize: Long,                   // 最大文件大小（字节）
    val maxLogAge: Long,                     // 日志最大保存时间（毫秒）
    val compressionEnabled: Boolean,         // 是否启用压缩
    val encryptionEnabled: Boolean,          // 是否启用加密
    val logSources: List<LogSource>,         // 日志源配置
    val uploadHeaders: Map<String, String>,  // 上传请求头
    val retryCount: Int = 3,                 // 重试次数
    val chunkSize: Long = 1024 * 1024,       // 分片大小（1MB）
    val realTimeEnabled: Boolean = false,    // 是否启用实时流
    val filterRules: List<LogFilterRule> = emptyList() // 过滤规则
) {
    
    /**
     * 检查配置是否有效
     */
    fun isValid(): Boolean {
        return enabled && 
               uploadUrl.isNotEmpty() && 
               uploadInterval > 0 && 
               maxFileSize > 0 && 
               logSources.isNotEmpty()
    }
    
    /**
     * 转换为JSON对象
     */
    fun toJson(): JSONObject {
        return JSONObject().apply {
            put("enabled", enabled)
            put("uploadUrl", uploadUrl)
            put("uploadInterval", uploadInterval)
            put("maxFileSize", maxFileSize)
            put("maxLogAge", maxLogAge)
            put("compressionEnabled", compressionEnabled)
            put("encryptionEnabled", encryptionEnabled)
            put("retryCount", retryCount)
            put("chunkSize", chunkSize)
            put("realTimeEnabled", realTimeEnabled)
            
            val sourcesArray = JSONArray()
            logSources.forEach { source ->
                sourcesArray.put(source.toJson())
            }
            put("logSources", sourcesArray)
            
            val headersObj = JSONObject()
            uploadHeaders.forEach { (key, value) ->
                headersObj.put(key, value)
            }
            put("uploadHeaders", headersObj)
        }
    }
    
    companion object {
        /**
         * 从JSON对象创建配置
         */
        fun fromJson(json: JSONObject): LogStreamConfig? {
            return try {
                val sourcesArray = json.optJSONArray("logSources") ?: JSONArray()
                val sources = mutableListOf<LogSource>()
                
                for (i in 0 until sourcesArray.length()) {
                    val sourceObj = sourcesArray.getJSONObject(i)
                    LogSource.fromJson(sourceObj)?.let { sources.add(it) }
                }
                
                val headersObj = json.optJSONObject("uploadHeaders") ?: JSONObject()
                val headers = mutableMapOf<String, String>()
                headersObj.keys().forEach { key ->
                    headers[key] = headersObj.getString(key)
                }
                
                LogStreamConfig(
                    enabled = json.optBoolean("enabled", false),
                    uploadUrl = json.optString("uploadUrl", ""),
                    uploadInterval = json.optLong("uploadInterval", 300000), // 5分钟
                    maxFileSize = json.optLong("maxFileSize", 10 * 1024 * 1024), // 10MB
                    maxLogAge = json.optLong("maxLogAge", 7 * 24 * 60 * 60 * 1000), // 7天
                    compressionEnabled = json.optBoolean("compressionEnabled", true),
                    encryptionEnabled = json.optBoolean("encryptionEnabled", false),
                    logSources = sources,
                    uploadHeaders = headers,
                    retryCount = json.optInt("retryCount", 3),
                    chunkSize = json.optLong("chunkSize", 1024 * 1024),
                    realTimeEnabled = json.optBoolean("realTimeEnabled", false)
                )
            } catch (e: Exception) {
                null
            }
        }
    }
}

/**
 * 日志源配置数据类
 */
data class LogSource(
    val type: LogSourceType,                 // 日志源类型
    val path: String,                        // 日志路径或命令
    val enabled: Boolean,                    // 是否启用
    val priority: Int,                       // 优先级
    val maxLines: Int = 1000,                // 最大行数
    val includePattern: String = "",         // 包含模式
    val excludePattern: String = "",         // 排除模式
    val encoding: String = "UTF-8"           // 编码格式
) {
    
    /**
     * 转换为JSON对象
     */
    fun toJson(): JSONObject {
        return JSONObject().apply {
            put("type", type.name)
            put("path", path)
            put("enabled", enabled)
            put("priority", priority)
            put("maxLines", maxLines)
            put("includePattern", includePattern)
            put("excludePattern", excludePattern)
            put("encoding", encoding)
        }
    }
    
    companion object {
        /**
         * 从JSON对象创建日志源
         */
        fun fromJson(json: JSONObject): LogSource? {
            return try {
                LogSource(
                    type = LogSourceType.valueOf(json.getString("type")),
                    path = json.getString("path"),
                    enabled = json.optBoolean("enabled", true),
                    priority = json.optInt("priority", 1),
                    maxLines = json.optInt("maxLines", 1000),
                    includePattern = json.optString("includePattern", ""),
                    excludePattern = json.optString("excludePattern", ""),
                    encoding = json.optString("encoding", "UTF-8")
                )
            } catch (e: Exception) {
                null
            }
        }
    }
}

/**
 * 日志源类型枚举
 */
enum class LogSourceType(val description: String, val command: String) {
    LOGCAT("系统日志", "logcat"),
    KERNEL("内核日志", "dmesg"),
    SYSTEM("系统事件", "getprop"),
    APPLICATION("应用日志", ""),
    CRASH("崩溃日志", ""),
    ANR("ANR日志", ""),
    NETWORK("网络日志", "netstat"),
    BATTERY("电池日志", "dumpsys battery"),
    MEMORY("内存日志", "dumpsys meminfo"),
    CPU("CPU日志", "top"),
    STORAGE("存储日志", "df"),
    CUSTOM("自定义", "");
    
    /**
     * 获取日志收集命令
     */
    fun getCollectionCommand(maxLines: Int = 1000): String {
        return when (this) {
            LOGCAT -> "logcat -d -t $maxLines"
            KERNEL -> "dmesg | tail -n $maxLines"
            SYSTEM -> "getprop"
            NETWORK -> "netstat -an"
            BATTERY -> "dumpsys battery"
            MEMORY -> "dumpsys meminfo"
            CPU -> "top -n 1"
            STORAGE -> "df -h"
            APPLICATION, CRASH, ANR -> ""
            CUSTOM -> command
        }
    }
}

/**
 * 日志过滤规则数据类
 */
data class LogFilterRule(
    val pattern: String,                     // 过滤模式
    val type: FilterType,                    // 过滤类型
    val action: FilterAction,                // 过滤动作
    val enabled: Boolean = true              // 是否启用
)

/**
 * 过滤类型枚举
 */
enum class FilterType {
    REGEX,          // 正则表达式
    CONTAINS,       // 包含
    STARTS_WITH,    // 开始于
    ENDS_WITH,      // 结束于
    EQUALS          // 等于
}

/**
 * 过滤动作枚举
 */
enum class FilterAction {
    INCLUDE,        // 包含
    EXCLUDE,        // 排除
    HIGHLIGHT,      // 高亮
    MASK            // 掩码
}

/**
 * 日志条目数据类
 */
data class LogEntry(
    val timestamp: Long,                     // 时间戳
    val level: LogLevel,                     // 日志级别
    val tag: String,                         // 标签
    val message: String,                     // 消息内容
    val source: LogSourceType,               // 日志源
    val pid: Int = 0,                        // 进程ID
    val tid: Int = 0,                        // 线程ID
    val packageName: String = "",            // 包名
    val metadata: Map<String, String> = emptyMap() // 元数据
) {
    
    /**
     * 转换为JSON对象
     */
    fun toJson(): JSONObject {
        return JSONObject().apply {
            put("timestamp", timestamp)
            put("level", level.name)
            put("tag", tag)
            put("message", message)
            put("source", source.name)
            put("pid", pid)
            put("tid", tid)
            put("packageName", packageName)
            
            val metaObj = JSONObject()
            metadata.forEach { (key, value) ->
                metaObj.put(key, value)
            }
            put("metadata", metaObj)
        }
    }
    
    /**
     * 格式化为字符串
     */
    fun format(): String {
        val dateFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS", Locale.getDefault())
        val timeStr = dateFormat.format(Date(timestamp))
        return "$timeStr ${level.symbol} $tag: $message"
    }
    
    companion object {
        /**
         * 从logcat行解析日志条目
         */
        fun fromLogcatLine(line: String): LogEntry? {
            return try {
                // 解析logcat格式: 时间戳 PID TID 级别 标签: 消息
                val parts = line.split(" ", limit = 6)
                if (parts.size < 6) return null
                
                val timestamp = System.currentTimeMillis() // 简化处理
                val pid = parts[1].toIntOrNull() ?: 0
                val tid = parts[2].toIntOrNull() ?: 0
                val level = LogLevel.fromChar(parts[3].firstOrNull() ?: 'I')
                val tagAndMessage = parts[5].split(": ", limit = 2)
                val tag = tagAndMessage.getOrNull(0) ?: ""
                val message = tagAndMessage.getOrNull(1) ?: ""
                
                LogEntry(
                    timestamp = timestamp,
                    level = level,
                    tag = tag,
                    message = message,
                    source = LogSourceType.LOGCAT,
                    pid = pid,
                    tid = tid
                )
            } catch (e: Exception) {
                null
            }
        }
    }
}

/**
 * 日志级别枚举
 */
enum class LogLevel(val value: Int, val symbol: String) {
    VERBOSE(2, "V"),
    DEBUG(3, "D"),
    INFO(4, "I"),
    WARN(5, "W"),
    ERROR(6, "E"),
    ASSERT(7, "A");
    
    companion object {
        fun fromChar(char: Char): LogLevel {
            return when (char.uppercaseChar()) {
                'V' -> VERBOSE
                'D' -> DEBUG
                'I' -> INFO
                'W' -> WARN
                'E' -> ERROR
                'A' -> ASSERT
                else -> INFO
            }
        }
    }
}

/**
 * 上传任务数据类
 */
data class UploadTask(
    val id: String,                          // 任务ID
    val filePath: String,                    // 文件路径
    val uploadUrl: String,                   // 上传URL
    val status: UploadStatus,                // 上传状态
    val createdTime: Long,                   // 创建时间
    val startTime: Long = 0,                 // 开始时间
    val completedTime: Long = 0,             // 完成时间
    val fileSize: Long,                      // 文件大小
    val uploadedSize: Long = 0,              // 已上传大小
    val chunkSize: Long,                     // 分片大小
    val currentChunk: Int = 0,               // 当前分片
    val totalChunks: Int,                    // 总分片数
    val retryCount: Int = 0,                 // 重试次数
    val maxRetries: Int = 3,                 // 最大重试次数
    val errorMessage: String = "",           // 错误消息
    val headers: Map<String, String> = emptyMap(), // 请求头
    val metadata: Map<String, String> = emptyMap() // 元数据
) {
    
    /**
     * 计算上传进度
     */
    fun getProgress(): Float {
        return if (fileSize > 0) {
            (uploadedSize.toFloat() / fileSize.toFloat()) * 100f
        } else {
            0f
        }
    }
    
    /**
     * 检查是否可以重试
     */
    fun canRetry(): Boolean {
        return retryCount < maxRetries && 
               (status == UploadStatus.FAILED || status == UploadStatus.PAUSED)
    }
    
    /**
     * 检查是否已完成
     */
    fun isCompleted(): Boolean {
        return status == UploadStatus.COMPLETED
    }
    
    /**
     * 检查是否失败
     */
    fun isFailed(): Boolean {
        return status == UploadStatus.FAILED && !canRetry()
    }
}

/**
 * 上传状态枚举
 */
enum class UploadStatus(val description: String) {
    PENDING("等待中"),
    PREPARING("准备中"),
    UPLOADING("上传中"),
    PAUSED("已暂停"),
    COMPLETED("已完成"),
    FAILED("失败"),
    CANCELLED("已取消");
}

/**
 * 日志文件信息数据类
 */
data class LogFileInfo(
    val path: String,                        // 文件路径
    val name: String,                        // 文件名
    val size: Long,                          // 文件大小
    val lastModified: Long,                  // 最后修改时间
    val compressed: Boolean = false,         // 是否已压缩
    val encrypted: Boolean = false,          // 是否已加密
    val checksum: String = "",               // 校验和
    val source: LogSourceType,               // 日志源
    val lineCount: Int = 0                   // 行数
) {
    
    /**
     * 获取文件对象
     */
    fun getFile(): File = File(path)
    
    /**
     * 检查文件是否存在
     */
    fun exists(): Boolean = getFile().exists()
    
    /**
     * 检查文件是否可读
     */
    fun canRead(): Boolean = getFile().canRead()
    
    /**
     * 获取格式化的文件大小
     */
    fun getFormattedSize(): String {
        return when {
            size < 1024 -> "${size}B"
            size < 1024 * 1024 -> "${size / 1024}KB"
            size < 1024 * 1024 * 1024 -> "${size / (1024 * 1024)}MB"
            else -> "${size / (1024 * 1024 * 1024)}GB"
        }
    }
}

/**
 * 日志流统计信息数据类
 */
data class LogStreamStatistics(
    var totalLogEntries: Long = 0,           // 总日志条目数
    var totalUploadTasks: Long = 0,          // 总上传任务数
    var completedUploads: Long = 0,          // 完成的上传数
    var failedUploads: Long = 0,             // 失败的上传数
    var totalUploadedBytes: Long = 0,        // 总上传字节数
    var totalCompressionRatio: Float = 0f,   // 总压缩比
    var averageUploadSpeed: Long = 0,        // 平均上传速度（字节/秒）
    var lastUploadTime: Long = 0,            // 最后上传时间
    var lastCollectionTime: Long = 0,        // 最后收集时间
    var activeUploadTasks: Int = 0,          // 活跃上传任务数
    var errorCount: Long = 0                 // 错误计数
) {
    
    /**
     * 记录日志条目
     */
    fun recordLogEntry() {
        totalLogEntries++
        lastCollectionTime = System.currentTimeMillis()
    }
    
    /**
     * 记录上传任务
     */
    fun recordUploadTask() {
        totalUploadTasks++
    }
    
    /**
     * 记录上传完成
     */
    fun recordUploadCompleted(bytes: Long) {
        completedUploads++
        totalUploadedBytes += bytes
        lastUploadTime = System.currentTimeMillis()
    }
    
    /**
     * 记录上传失败
     */
    fun recordUploadFailed() {
        failedUploads++
        errorCount++
    }
    
    /**
     * 计算成功率
     */
    fun getSuccessRate(): Float {
        return if (totalUploadTasks > 0) {
            (completedUploads.toFloat() / totalUploadTasks.toFloat()) * 100f
        } else {
            0f
        }
    }
    
    /**
     * 重置统计信息
     */
    fun reset() {
        totalLogEntries = 0
        totalUploadTasks = 0
        completedUploads = 0
        failedUploads = 0
        totalUploadedBytes = 0
        totalCompressionRatio = 0f
        averageUploadSpeed = 0
        lastUploadTime = 0
        lastCollectionTime = 0
        activeUploadTasks = 0
        errorCount = 0
    }
}
