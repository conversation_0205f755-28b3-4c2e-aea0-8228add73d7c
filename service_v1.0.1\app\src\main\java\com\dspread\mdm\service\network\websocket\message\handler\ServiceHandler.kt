package com.dspread.mdm.service.network.websocket.message.handler

import android.content.Context
import com.dspread.mdm.service.network.websocket.message.WsMessageSender
import com.dspread.mdm.service.utils.log.Logger
import org.json.JSONArray
import org.json.JSONObject

/**
 * 服务处理器
 * 处理 ST003 服务相关消息
 */
class ServiceHandler(context: Context) : BaseMessageHandler(context) {

    companion object {
        private const val TAG = "ServiceHandler"
        
        // 服务操作类型
        private const val SERVICE_START = "1"     // 启动服务
        private const val SERVICE_STOP = "2"      // 停止服务
        private const val SERVICE_RESTART = "3"   // 重启服务
        
        // 结果常量
        private const val RESULT_SUCCESS = "0"
        private const val RESULT_FAILED = "1"
    }

    override fun handleMessage(message: String) {
        val jsonObject = parseMessage(message) ?: return
        val messageInfo = getMessageInfo(jsonObject)
        val data = getDataFromMessage(jsonObject) ?: return
        
        Logger.wsm("处理服务消息: ${messageInfo.tranCode}")
        
        try {
            if (data.has("serviceList")) {
                val serviceList = data.getJSONArray("serviceList")
                processServiceList(serviceList, messageInfo)
            } else {
                Logger.wsmE("服务消息缺少 serviceList 字段")
            }
        } catch (e: Exception) {
            Logger.wsmE("处理服务消息失败", e)
        }
    }

    /**
     * 处理服务列表
     */
    private fun processServiceList(serviceList: JSONArray, messageInfo: BaseMessageHandler.MessageInfo) {
        Logger.wsm("开始处理服务列表，共 ${serviceList.length()} 个服务")
        
        for (i in 0 until serviceList.length()) {
            try {
                val service = serviceList.getJSONObject(i)
                processService(service, messageInfo)
            } catch (e: Exception) {
                Logger.wsmE("处理第 $i 个服务失败", e)
            }
        }
    }

    /**
     * 处理单个服务
     */
    private fun processService(service: JSONObject, messageInfo: BaseMessageHandler.MessageInfo) {
        val serviceId = service.optString("serviceId")
        val serviceName = service.optString("serviceName")
        val serviceType = service.optString("serviceType")
        val operation = service.optString("operation")
        
        Logger.wsm("处理服务: serviceId=$serviceId, name=$serviceName, operation=$operation")
        
        when (operation) {
            SERVICE_START -> {
                handleStartService(service, messageInfo)
            }
            SERVICE_STOP -> {
                handleStopService(service, messageInfo)
            }
            SERVICE_RESTART -> {
                handleRestartService(service, messageInfo)
            }
            else -> {
                Logger.wsmE("未知的服务操作: $operation")
                uploadServiceResult(service, RESULT_FAILED, "未知的服务操作", messageInfo)
            }
        }
    }

    /**
     * 处理启动服务
     */
    private fun handleStartService(service: JSONObject, messageInfo: BaseMessageHandler.MessageInfo) {
        val serviceName = service.optString("serviceName")
        
        Logger.wsm("启动服务: $serviceName")
        
        try {
            val success = startService(serviceName)
            val result = if (success) RESULT_SUCCESS else RESULT_FAILED
            val errorMsg = if (success) null else "启动服务失败"
            
            uploadServiceResult(service, result, errorMsg, messageInfo)
            
        } catch (e: Exception) {
            Logger.wsmE("启动服务失败", e)
            uploadServiceResult(service, RESULT_FAILED, e.message, messageInfo)
        }
    }

    /**
     * 处理停止服务
     */
    private fun handleStopService(service: JSONObject, messageInfo: BaseMessageHandler.MessageInfo) {
        val serviceName = service.optString("serviceName")
        
        Logger.wsm("停止服务: $serviceName")
        
        try {
            val success = stopService(serviceName)
            val result = if (success) RESULT_SUCCESS else RESULT_FAILED
            val errorMsg = if (success) null else "停止服务失败"
            
            uploadServiceResult(service, result, errorMsg, messageInfo)
            
        } catch (e: Exception) {
            Logger.wsmE("停止服务失败", e)
            uploadServiceResult(service, RESULT_FAILED, e.message, messageInfo)
        }
    }

    /**
     * 处理重启服务
     */
    private fun handleRestartService(service: JSONObject, messageInfo: BaseMessageHandler.MessageInfo) {
        val serviceName = service.optString("serviceName")
        
        Logger.wsm("重启服务: $serviceName")
        
        try {
            val success = restartService(serviceName)
            val result = if (success) RESULT_SUCCESS else RESULT_FAILED
            val errorMsg = if (success) null else "重启服务失败"
            
            uploadServiceResult(service, result, errorMsg, messageInfo)
            
        } catch (e: Exception) {
            Logger.wsmE("重启服务失败", e)
            uploadServiceResult(service, RESULT_FAILED, e.message, messageInfo)
        }
    }

    /**
     * 上传服务操作结果
     */
    private fun uploadServiceResult(
        service: JSONObject,
        result: String,
        errorMsg: String?,
        messageInfo: BaseMessageHandler.MessageInfo
    ) {
        val serviceId = service.optString("serviceId")
        
        // 这里可以使用类似 uploadTaskResult 的方法
        // 或者创建专门的服务结果上传方法
        Logger.wsm("上传服务操作结果: serviceId=$serviceId, result=$result")
    }

    /**
     * 启动服务
     */
    private fun startService(serviceName: String): Boolean {
        // 这里需要实现实际的服务启动逻辑
        // 可能需要使用 SystemManager 或其他系统接口
        Logger.wsm("执行启动服务: $serviceName")
        return true // 模拟成功
    }

    /**
     * 停止服务
     */
    private fun stopService(serviceName: String): Boolean {
        // 这里需要实现实际的服务停止逻辑
        Logger.wsm("执行停止服务: $serviceName")
        return true // 模拟成功
    }

    /**
     * 重启服务
     */
    private fun restartService(serviceName: String): Boolean {
        // 这里需要实现实际的服务重启逻辑
        Logger.wsm("执行重启服务: $serviceName")
        return stopService(serviceName) && startService(serviceName)
    }
}
