package com.dspread.mdm.service.modules.logstream

import android.content.Context
import com.dspread.mdm.service.config.LogStreamConfig as LogStreamConfigManager
import com.dspread.mdm.service.modules.BaseModuleManager
import com.dspread.mdm.service.modules.logstream.model.LogEntry
import com.dspread.mdm.service.modules.logstream.model.LogFileInfo
import com.dspread.mdm.service.modules.logstream.model.LogSource
import com.dspread.mdm.service.modules.logstream.model.LogSourceType
import com.dspread.mdm.service.modules.logstream.model.LogStreamConfig
import com.dspread.mdm.service.modules.logstream.model.LogStreamStatistics
import com.dspread.mdm.service.modules.logstream.model.UploadTask
import com.dspread.mdm.service.utils.log.Logger
import kotlinx.coroutines.*
import kotlinx.coroutines.sync.withLock
import java.io.File
import java.util.*
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.atomic.AtomicBoolean
import java.util.concurrent.atomic.AtomicReference

/**
 * 日志流管理器
 * 负责日志收集、处理、上传的整体协调和管理
 */
class LogStreamManager(
    private val context: Context
) : BaseModuleManager() {
    
    companion object {
        private const val TAG = "[LogStreamManager]"
        private const val DEFAULT_UPLOAD_INTERVAL = 300000L // 5分钟
        private const val DEFAULT_CLEANUP_INTERVAL = 3600000L // 1小时
    }
    
    // 核心组件
    private val logCollector = LogCollector(context)
    private val logProcessor = LogProcessor(context)
    private val logUploader = LogUploader(context)
    private val recentLogHandler = RecentLogHandler(context)
    private val logStorageManager = LogStorageManager(
        logDirectory = logCollector.getAllLogFiles().firstOrNull()?.parentFile ?: context.filesDir,
        maxStorageSize = 500 * 1024 * 1024L // 500MB
    )
    
    // 状态管理
    private val currentConfig = AtomicReference<LogStreamConfig?>()
    private val isStreaming = AtomicBoolean(false)
    private val isPaused = AtomicBoolean(false)
    private val statistics = LogStreamStatistics()

    // 并发控制
    private val compressionMutex = kotlinx.coroutines.sync.Mutex()
    private val processingFiles = Collections.synchronizedSet(mutableSetOf<String>())
    
    // 单一协程作用域 + 明确Job管理
    // 使用SupervisorJob确保子协程失败不影响其他协程
    private val managerScope = CoroutineScope(Dispatchers.IO + SupervisorJob())

    // 明确的Job引用，便于精确控制各个功能模块
    private val uploadTasks = ConcurrentHashMap<String, UploadTask>()
    private var collectionJob: Job? = null
    private var uploadJob: Job? = null
    private var cleanupJob: Job? = null
    private var compressionJob: Job? = null
    
    override fun getModuleName(): String = "LogStream Manager"
    
    override suspend fun onInitialize(): Result<Unit> {
        return try {
            Logger.logStream("$TAG 初始化日志流管理器")

            // 初始化日志队列
            initLogQueue()

            // 初始化各个组件
            // logCollector已经在构造函数中初始化
            Logger.logStream("$TAG 日志流管理器初始化完成")
            Result.success(Unit)
        } catch (e: Exception) {
            Logger.logStreamE("$TAG 日志流管理器初始化失败", e)
            Result.failure(e)
        }
    }
    
    override suspend fun onStart(): Result<Unit> {
        return try {
            Logger.logStream("$TAG 启动日志流服务")

            // 初始化LogStream配置
            LogStreamConfigManager.init(context)
            Logger.logStream("$TAG ${LogStreamConfigManager.getConfigDescription()}")

            // 无论是否有配置，都要启动基础的日志收集
            startBasicLogCollection()

            val config = currentConfig.get()
            if (config != null && config.enabled) {
                startStreaming()
            } else {
                Logger.logStream("$TAG 日志流服务未配置或未启用，但基础日志收集已启动")
            }

            Logger.logStream("$TAG 日志流服务启动完成")
            Result.success(Unit)
        } catch (e: Exception) {
            Logger.logStreamE("$TAG 日志流服务启动失败", e)
            Result.failure(e)
        }
    }
    
    override suspend fun onStop(): Result<Unit> {
        return try {
            Logger.logStream("$TAG 停止日志流服务")

            stopStreaming()

            // 取消所有任务
            collectionJob?.cancel()
            uploadJob?.cancel()
            cleanupJob?.cancel()
            compressionJob?.cancel()

            // 取消协程作用域（模块完全停止时）
            managerScope.cancel()

            // 保存日志队列
            saveLogQueue()

            Logger.logStream("$TAG 日志流服务停止完成")
            Result.success(Unit)
        } catch (e: Exception) {
            Logger.logStreamE("$TAG 日志流服务停止失败", e)
            Result.failure(e)
        }
    }
    
    /**
     * 更新配置
     */
    suspend fun updateConfig(config: LogStreamConfig): Result<Unit> {
        return try {
            Logger.logStream("$TAG 更新日志流配置")
            
            if (!config.isValid()) {
                return Result.failure(IllegalArgumentException("Invalid log stream config"))
            }
            
            val oldConfig = currentConfig.getAndSet(config)
            
            // 如果配置发生变化，重新启动流服务
            if (oldConfig != config) {
                Logger.logStream("$TAG 日志流配置已更改，重新启动服务")
                
                if (isStreaming.get()) {
                    stopStreaming()
                    if (config.enabled) {
                        startStreaming()
                    }
                }
            }
            
            Logger.logStream("$TAG 日志流配置更新完成")
            Result.success(Unit)
        } catch (e: Exception) {
            Logger.logStreamE("$TAG 日志流配置更新失败", e)
            Result.failure(e)
        }
    }
    
    /**
     * 启动日志流
     */
    suspend fun startStreaming(): Result<Unit> {
        return try {
            // 如果没有配置，创建默认配置
            var config = currentConfig.get()
            if (config == null) {
                config = createDefaultConfig()
                currentConfig.set(config)
                Logger.logStream("$TAG 使用默认配置启动日志流")
            }

            if (isStreaming.compareAndSet(false, true)) {
                Logger.logStream("$TAG 启动日志流")

                // 启动日志收集任务
                startCollectionJob(config)

                // 启动上传任务
                startUploadJob(config)

                // 启动清理任务
                startCleanupJob()

                Logger.logStream("$TAG 日志流启动完成")
            }

            Result.success(Unit)
        } catch (e: Exception) {
            Logger.logStreamE("$TAG 启动日志流失败", e)
            Result.failure(e)
        }
    }
    
    /**
     * 停止日志流
     * 优化：立即响应停止命令，确保任何时候都以收到指令为主
     */
    suspend fun stopStreaming(): Result<Unit> {
        return try {
            Logger.logStream("$TAG 收到停止日志流请求，当前状态: isStreaming=${isStreaming.get()}")

            if (isStreaming.compareAndSet(true, false)) {
                Logger.logStream("$TAG 立即停止日志流")

                // 立即取消所有任务，使用CancellationException
                try {
                    collectionJob?.cancel(CancellationException("日志流服务停止"))
                    Logger.logStream("$TAG 收集任务已取消")
                } catch (e: Exception) {
                    Logger.logStreamE("$TAG 取消收集任务失败", e)
                }

                try {
                    uploadJob?.cancel(CancellationException("日志流服务停止"))
                    Logger.logStream("$TAG 上传任务已取消")
                } catch (e: Exception) {
                    Logger.logStreamE("$TAG 取消上传任务失败", e)
                }

                try {
                    cleanupJob?.cancel(CancellationException("日志流服务停止"))
                    Logger.logStream("$TAG 清理任务已取消")
                } catch (e: Exception) {
                    Logger.logStreamE("$TAG 取消清理任务失败", e)
                }

                // 立即停止日志收集器
                try {
                    logCollector.stop()
                    Logger.logStream("$TAG 日志收集器已停止")
                } catch (e: Exception) {
                    Logger.logStreamE("$TAG 停止日志收集器失败", e)
                }

                // 立即取消所有上传任务
                try {
                    uploadTasks.values.forEach { task ->
                        try {
                            logUploader.cancelUpload(task.id)
                        } catch (e: Exception) {
                            Logger.logStreamE("$TAG 取消上传任务失败: ${task.id}", e)
                        }
                    }
                    uploadTasks.clear()
                    Logger.logStream("$TAG 所有上传任务已清理")
                } catch (e: Exception) {
                    Logger.logStreamE("$TAG 清理上传任务失败", e)
                }

                Logger.logStream("$TAG 日志流停止完成")
            } else {
                Logger.logStream("$TAG 日志流已经停止，无需重复操作")
            }

            Result.success(Unit)
        } catch (e: Exception) {
            Logger.logStreamE("$TAG 停止日志流失败", e)
            Result.failure(e)
        }
    }
    
    /**
     * 暂停日志流
     */
    suspend fun pauseStreaming(): Result<Unit> {
        return try {
            if (isStreaming.get() && isPaused.compareAndSet(false, true)) {
                Logger.logStream("$TAG 暂停日志流")
                
                uploadTasks.values.forEach { task ->
                    logUploader.pauseUpload(task.id)
                }
                
                Logger.logStream("$TAG 日志流暂停完成")
            }
            Result.success(Unit)
        } catch (e: Exception) {
            Logger.logStreamE("$TAG 暂停日志流失败", e)
            Result.failure(e)
        }
    }
    
    /**
     * 恢复日志流
     */
    suspend fun resumeStreaming(): Result<Unit> {
        return try {
            if (isStreaming.get() && isPaused.compareAndSet(true, false)) {
                Logger.logStream("$TAG 恢复日志流")
                
                uploadTasks.values.forEach { task ->
                    // TODO: 实现恢复上传
                    // logUploader.resumeUpload(task.id)
                }
                
                Logger.logStream("$TAG 日志流恢复完成")
            }
            Result.success(Unit)
        } catch (e: Exception) {
            Logger.logStreamE("$TAG 恢复日志流失败", e)
            Result.failure(e)
        }
    }
    
    /**
     * 收集日志
     */
    suspend fun collectLogs(): Result<List<LogFileInfo>> {
        return try {
            val config = currentConfig.get() ?: return Result.failure(IllegalStateException("No config set"))
            
            Logger.logStream("$TAG 开始收集日志，来源: ${config.logSources}")
            
            val files = logCollector.getAllLogFiles()

            // TODO: 实现统计更新
            // statistics.incrementCollectedFiles(files.size)
            Logger.logStream("$TAG 日志收集完成，文件数: ${files.size}")
            return Result.success(files.map {
                LogFileInfo(
                    name = it.name,
                    path = it.absolutePath,
                    size = it.length(),
                    lastModified = it.lastModified(),
                    source = LogSourceType.SYSTEM
                )
            })
        } catch (e: Exception) {
            Logger.logStreamE("$TAG 收集日志失败", e)
            Result.failure(e)
        }
    }
    
    /**
     * 上传日志
     */
    suspend fun uploadLogs(filePaths: List<String>, uploadUrl: String): Result<Unit> {
        return try {
            val config = currentConfig.get() ?: return Result.failure(IllegalStateException("No config set"))
            
            Logger.logStream("$TAG 开始上传日志")
            
            val files = logCollector.getAllLogFiles()
            
            Logger.logStream("$TAG 日志上传完成")
            Result.success(Unit)
        } catch (e: Exception) {
            Logger.logStreamE("$TAG 上传日志失败", e)
            Result.failure(e)
        }
    }
    
    /**
     * 清理日志
     */
    suspend fun clearLogs(): Result<Unit> {
        return try {
            val config = currentConfig.get() ?: return Result.failure(IllegalStateException("No config set"))
            
            Logger.logStream("$TAG 开始清理日志")
            
            val files = logCollector.getAllLogFiles()
            
            Logger.logStream("$TAG 日志清理完成")
            Result.success(Unit)
        } catch (e: Exception) {
            Logger.logStreamE("$TAG 清理日志失败", e)
            Result.failure(e)
        }
    }
    
    /**
     * 重置状态
     */
    suspend fun resetStatus(): Result<Unit> {
        return try {
            Logger.logStream("$TAG 重置日志流状态")

            managerScope.launch {
                stopStreaming()
            }

            Logger.logStream("$TAG 日志流状态重置完成")
            Result.success(Unit)
        } catch (e: Exception) {
            Logger.logStreamE("$TAG 重置日志流状态失败", e)
            Result.failure(e)
        }
    }

    // 启动任务
    private fun startCollectionJob(config: LogStreamConfig) {
        collectionJob?.cancel()

        collectionJob = managerScope.launch {
            while (isStreaming.get() && !isPaused.get()) {
                try {
                    Logger.logStream("$TAG 执行日志收集任务")
                    collectLogs()
                } catch (e: Exception) {
                    Logger.logStreamE("$TAG 日志收集任务异常", e)
                }

                delay(DEFAULT_UPLOAD_INTERVAL)
            }
        }
    }

    private fun startUploadJob(config: LogStreamConfig) {
        uploadJob?.cancel()

        uploadJob = managerScope.launch {
            while (isStreaming.get() && !isPaused.get()) {
                try {
                    Logger.logStream("$TAG 执行日志上传任务")

                    // 处理上传队列
                    processUploadQueue()

                } catch (e: Exception) {
                    Logger.logStreamE("$TAG 日志上传任务异常", e)
                }

                delay(config.uploadInterval ?: DEFAULT_UPLOAD_INTERVAL)
            }
        }
    }

    private fun startCleanupJob() {
        cleanupJob?.cancel()

        cleanupJob = managerScope.launch {
            while (isStreaming.get()) {
                try {
                    Logger.logStream("$TAG 执行日志清理任务")

                    // 清理过期日志
                    cleanupStorageSpace()

                } catch (e: Exception) {
                    Logger.logStreamE("$TAG 日志清理任务异常", e)
                }

                delay(DEFAULT_CLEANUP_INTERVAL)
            }
        }
    }

    // 处理上传队列
    private suspend fun processUploadQueue() {
        try {
            Logger.logStream("$TAG 处理上传队列")

            // 1. 处理日志队列中的文件（压缩并移动到上传队列）
            processLogQueueForUpload()

            // 2. 处理压缩队列中的文件（上传到S3）
            processGzQueueForUpload()

        } catch (e: Exception) {
            Logger.logStreamE("$TAG 处理上传队列失败", e)
        }
    }

    /**
     * 处理日志队列，压缩文件并移动到上传队列
     */
    private suspend fun processLogQueueForUpload() {
        try {
            val logQueue = LogQueue.getLogQueue()
            Logger.logStream("$TAG 日志队列长度: ${logQueue.size}")

            while (logQueue.isNotEmpty()) {
                val logFilePath = logQueue.peek() as? String ?: break
                val logFile = File(logFilePath)

                Logger.logStream("$TAG 处理日志文件: ${logFile.name}, 大小: ${logFile.length()}")

                if (logFile.exists() && logFilePath.endsWith(".log")) {
                    // 检查文件大小，如果达到阈值则压缩
                    // 使用配置的文件大小作为压缩阈值
                    val compressionThreshold = LogStreamConfigManager.getLogFileSize()
                    if (logFile.length() >= compressionThreshold) {
                        Logger.logStream("$TAG 日志文件达到压缩阈值: ${logFile.name}, 大小: ${formatSize(logFile.length())}")

                        // 压缩文件
                        val compressedFile = compressLogFile(logFile)
                        if (compressedFile != null) {
                            // 添加到压缩队列
                            LogQueue.getGzQueue().offer(compressedFile.absolutePath)
                            LogQueue.saveGzQueueToFile()

                            // 从日志队列中移除
                            logQueue.poll()
                            LogQueue.saveLogQueueToFile()

                            // 删除原始文件
                            logFile.delete()

                            Logger.logStream("$TAG 日志文件压缩完成: ${compressedFile.name}")
                        } else {
                            Logger.logStreamE("$TAG 日志文件压缩失败: ${logFile.name}")
                            break
                        }
                    } else {
                        // 文件还没达到压缩阈值，跳出循环
                        break
                    }
                } else {
                    Logger.logStreamW("$TAG 无效的日志文件: $logFilePath")
                    logQueue.poll() // 移除无效文件
                    LogQueue.saveLogQueueToFile()
                }
            }

        } catch (e: Exception) {
            Logger.logStreamE("$TAG 处理日志队列失败", e)
        }
    }

    /**
     * 处理压缩队列，上传文件到S3
     */
    private suspend fun processGzQueueForUpload() {
        try {
            val gzQueue = LogQueue.getGzQueue()
            Logger.logStream("$TAG 压缩队列长度: ${gzQueue.size}")

            while (gzQueue.isNotEmpty()) {
                val gzFilePath = gzQueue.peek() as? String ?: break
                val gzFile = File(gzFilePath)

                Logger.logStream("$TAG 上传压缩文件: ${gzFile.name}, 大小: ${gzFile.length()}")

                if (gzFile.exists() && gzFilePath.endsWith(".gz")) {
                    // 上传到S3
                    val uploadResult = logUploader.uploadFileToS3(gzFilePath)

                    if (uploadResult.isSuccess) {
                        Logger.logStream("$TAG 压缩文件上传成功: ${gzFile.name}")

                        // 从压缩队列中移除
                        gzQueue.poll()
                        LogQueue.saveGzQueueToFile()

                        // 删除本地文件
                        gzFile.delete()

                        // 更新统计
                        // statistics.incrementUploadedFiles(1)

                    } else {
                        Logger.logStreamE("$TAG 压缩文件上传失败: ${gzFile.name}", uploadResult.exceptionOrNull())
                        // 上传失败，暂时跳出循环，下次再试
                        break
                    }
                } else {
                    Logger.logStreamW("$TAG 无效的压缩文件: $gzFilePath")
                    gzQueue.poll() // 移除无效文件
                    LogQueue.saveGzQueueToFile()
                }
            }

        } catch (e: Exception) {
            Logger.logStreamE("$TAG 处理压缩队列失败", e)
        }
    }

    /**
     * 压缩日志文件
     */
    private suspend fun compressLogFile(logFile: File): File? = withContext(Dispatchers.IO) {
        return@withContext try {
            val timestamp = java.text.SimpleDateFormat("yyyyMMdd_HHmmss", java.util.Locale.getDefault()).format(java.util.Date())
            val gzFileName = logFile.nameWithoutExtension + "--" + timestamp + ".gz"

            // 创建上传目录
            val uploadDir = File(logFile.parent, "upload")
            if (!uploadDir.exists()) {
                uploadDir.mkdirs()
            }

            val gzFile = File(uploadDir, gzFileName)

            Logger.logStream("$TAG 开始压缩文件: ${logFile.name} -> ${gzFile.name}")

            // 使用LogProcessor进行压缩
            val compressResult = logProcessor.compressFile(logFile, CompressionType.GZIP)

            if (compressResult.isSuccess) {
                val compressedInfo = compressResult.getOrNull()
                if (compressedInfo != null) {
                    val actualGzFile = File(compressedInfo.path)
                    // 如果压缩后的文件路径不是我们期望的，移动到正确位置
                    if (actualGzFile.absolutePath != gzFile.absolutePath) {
                        actualGzFile.renameTo(gzFile)
                    }
                    Logger.logStream("$TAG 文件压缩成功: ${gzFile.name}")
                    gzFile
                } else {
                    Logger.logStreamE("$TAG 文件压缩结果为空: ${logFile.name}")
                    null
                }
            } else {
                Logger.logStreamE("$TAG 文件压缩失败: ${logFile.name}", compressResult.exceptionOrNull())
                null
            }

        } catch (e: Exception) {
            Logger.logStreamE("$TAG 压缩文件异常: ${logFile.name}", e)
            null
        }
    }

    // Getter方法
    fun getCurrentConfig(): LogStreamConfig? = currentConfig.get()
    fun getStatistics(): LogStreamStatistics = statistics

    fun getUploadTasks(): List<UploadTask> = uploadTasks.values.toList()
    fun isStreamingActive(): Boolean = isStreaming.get()
    fun isPaused(): Boolean = isPaused.get()

    // 上传任务管理方法
    suspend fun startUploadTask(filePath: String, uploadUrl: String, headers: Map<String, String>): Result<String> {
        val result = logUploader.uploadFile(filePath, uploadUrl, headers)
        return if (result.isSuccess) {
            val uploadResponse = result.getOrNull()!!
            Result.success(uploadResponse.toString()) // 转换为String
        } else {
            Result.failure(result.exceptionOrNull() ?: Exception("Upload failed"))
        }
    }

    fun pauseUploadTask(taskId: String): Result<Unit> {
        // TODO: 实现暂停上传
        return Result.success(Unit)
    }

    fun resumeUploadTask(taskId: String): Result<Unit> {
        // TODO: 实现恢复上传
        return Result.success(Unit)
    }

    fun cancelUploadTask(taskId: String): Result<Unit> {
        return logUploader.cancelUpload(taskId)
    }

    // 查询方法
    suspend fun getRecentLogs(count: Int, source: String): List<LogEntry> {
        // TODO: 实现获取最近日志
        return emptyList()
    }

    /**
     * 初始化日志队列
     */
    private fun initLogQueue() {
        try {
            Logger.logStream("$TAG 初始化日志队列")

            // 从文件加载队列
            val logDirectory = logCollector.getAllLogFiles().firstOrNull()?.parentFile ?: context.filesDir
            val logQueueFile = File(logDirectory, "log_queue.txt")
            val gzQueueFile = File(logDirectory, "gz_queue.txt")

            LogQueue.loadLogQueueFromFile(logQueueFile)
            LogQueue.loadGzQueueFromFile(gzQueueFile)

            Logger.logStream("$TAG 日志队列初始化完成")
        } catch (e: Exception) {
            Logger.logStreamE("$TAG 初始化日志队列失败", e)
        }
    }

    /**
     * 保存日志队列
     */
    private fun saveLogQueue() {
        try {
            Logger.logStream("$TAG 保存日志队列")

            val logDirectory = logCollector.getAllLogFiles().firstOrNull()?.parentFile ?: context.filesDir
            val logQueueFile = File(logDirectory, "log_queue.txt")
            val gzQueueFile = File(logDirectory, "gz_queue.txt")

            LogQueue.saveLogQueueToFile(logQueueFile)
            LogQueue.saveGzQueueToFile(gzQueueFile)

            Logger.logStream("$TAG 日志队列保存完成")
        } catch (e: Exception) {
            Logger.logStreamE("$TAG 保存日志队列失败", e)
        }
    }

    /**
     * 创建默认配置
     */
    private fun createDefaultConfig(): LogStreamConfig {
        return LogStreamConfig(
            enabled = true,
            uploadUrl = "", // 默认为空，等待WebSocket配置
            uploadInterval = DEFAULT_UPLOAD_INTERVAL,
            maxFileSize = 10 * 1024 * 1024L, // 10MB
            maxLogAge = 7 * 24 * 60 * 60 * 1000L, // 7天
            compressionEnabled = true,
            encryptionEnabled = false,
            logSources = listOf(
                LogSource(
                    type = LogSourceType.LOGCAT,
                    path = "logcat",
                    enabled = true,
                    priority = 1
                ),
                LogSource(
                    type = LogSourceType.SYSTEM,
                    path = "system",
                    enabled = true,
                    priority = 2
                )
            ),
            uploadHeaders = emptyMap(),
            retryCount = 3,
            chunkSize = 1024 * 1024L, // 1MB
            realTimeEnabled = false
        )
    }

    /**
     * 启动基础日志收集（无论是否有配置都要运行）
     */
    private suspend fun startBasicLogCollection() {
        try {
            Logger.logStream("$TAG 启动基础日志收集")

            // 设置文件轮转监听器
            logCollector.setRotationListener(object : LogCollector.OnFileRotationListener {
                override fun onFileRotated() {
                    triggerCompressionCheck()
                }
            })

            // 启动日志收集器
            logCollector.start()

            // 启动定期检查任务，处理日志文件大小和队列
            startPeriodicTasks()

            Logger.logStream("$TAG 基础日志收集启动完成")

        } catch (e: Exception) {
            Logger.logStreamE("$TAG 启动基础日志收集失败", e)
        }
    }

    /**
     * 启动定期任务
     */
    private suspend fun startPeriodicTasks() {
        try {
            Logger.logStream("$TAG 启动定期任务")

            // 启动文件管理任务（调试模式30秒检查一次，生产模式5分钟检查一次）
            val checkInterval = if (LogStreamConfigManager.isEnabled()) 30000L else 300000L
            managerScope.launch {
                var lastCompressionCheck = 0L
                var lastRecentLogProcess = 0L

                while (isStreaming.get() || logCollector.isCollecting()) {
                    try {
                        val currentTime = System.currentTimeMillis()

                        // 处理日志队列（压缩大文件）- 限制频率
                        if (currentTime - lastCompressionCheck >= checkInterval) {
                            processLogQueueForCompression()
                            lastCompressionCheck = currentTime
                        }

                        // 检查存储空间并清理旧文件
                        logCollector.checkStorageAndCleanup()

                        // 等待指定间隔
                        kotlinx.coroutines.delay(checkInterval)

                    } catch (e: Exception) {
                        Logger.logStreamE("$TAG 定期任务执行异常", e)
                        kotlinx.coroutines.delay(checkInterval) // 出错也要等待，避免死循环
                    }
                }
            }

        } catch (e: Exception) {
            Logger.logStreamE("$TAG 启动定期任务失败", e)
        }
    }

    /**
     * 立即触发压缩检查（用于文件轮转时，避免重复触发）
     */
    fun triggerCompressionCheck() {
        // 只有在没有正在进行压缩时才触发
        if (compressionMutex.isLocked) {
            Logger.logStream("$TAG 压缩正在进行中，跳过触发")
            return
        }

        // 使用CPU调度器处理压缩任务（CPU密集型）
        compressionJob = managerScope.launch(Dispatchers.Default) {
            processLogQueueForCompression()
        }
    }

    /**
     * 处理日志队列进行压缩
     */
    private suspend fun processLogQueueForCompression() {
        // 使用互斥锁防止并发压缩
        compressionMutex.withLock {
            try {
                val logQueue = LogQueue.getLogQueue()
                Logger.logStream("$TAG 检查日志队列进行压缩，队列长度: ${logQueue.size}")

                // 遍历队列中的文件，检查是否需要压缩
                val iterator = logQueue.iterator()
                while (iterator.hasNext()) {
                    val logFilePath = iterator.next()
                    val logFile = File(logFilePath)

                    // 检查文件是否正在被处理
                    if (processingFiles.contains(logFilePath)) {
                        Logger.logStream("$TAG 文件正在被处理，跳过: ${logFile.name}")
                        continue
                    }

                    // 检查文件是否存在且不是当前正在写入的文件
                    if (logFile.exists() && logFile != logCollector.getCurrentLogFile()) {
                        val fileSize = logFile.length()
                        val compressionThreshold = LogStreamConfigManager.getLogFileSize()

                        Logger.logStream("$TAG 检查日志文件: ${logFile.name}, 大小: ${formatSize(fileSize)}")

                        // 如果文件达到压缩阈值，进行压缩
                        if (fileSize >= compressionThreshold) {
                            Logger.logStream("$TAG 日志文件达到压缩阈值: ${logFile.name}")

                            // 标记文件为正在处理
                            processingFiles.add(logFilePath)

                            try {
                                // 压缩文件
                                val compressedFile = withContext(Dispatchers.IO) {
                                    compressLogFileWithTimeRange(logFile)
                                }
                                if (compressedFile != null) {
                                    // 添加到压缩队列
                                    LogQueue.getGzQueue().offer(compressedFile.absolutePath)
                                    LogQueue.saveGzQueueToFile()

                                    // 从日志队列中移除
                                    iterator.remove()
                                    LogQueue.saveLogQueueToFile()

                                    // 删除原始文件
                                    logFile.delete()

                                    Logger.logStream("$TAG 日志文件压缩完成: ${compressedFile.name}")
                                } else {
                                    Logger.logStreamE("$TAG 日志文件压缩失败: ${logFile.name}")
                                }
                            } finally {
                                // 移除处理标记
                                processingFiles.remove(logFilePath)
                            }
                        }
                    } else if (!logFile.exists()) {
                        // 文件不存在，从队列中移除
                        Logger.logStream("$TAG 文件不存在，从队列中移除: ${logFile.name}")
                        iterator.remove()
                        LogQueue.saveLogQueueToFile()
                    }
                }

            } catch (e: Exception) {
                Logger.logStreamE("$TAG 处理日志队列压缩失败", e)
            }
        }
    }

    /**
     * 压缩日志文件（使用时间范围命名）
     */
    private suspend fun compressLogFileWithTimeRange(logFile: File): File? = withContext(Dispatchers.IO) {
        return@withContext try {
            // 检查文件是否存在
            if (!logFile.exists()) {
                Logger.logStreamW("$TAG 文件不存在，跳过压缩: ${logFile.absolutePath}")
                return@withContext null
            }

            // 检查文件大小
            if (logFile.length() == 0L) {
                Logger.logStreamW("$TAG 文件为空，跳过压缩: ${logFile.name}")
                return@withContext null
            }

            // 解析文件的时间范围
            val timeRange = parseLogFileTimeRange(logFile)
            val gzFileName = "${timeRange.startTime}-${timeRange.endTime}.gz"

            // 创建上传目录
            val uploadDir = File(logFile.parent, "upload")
            if (!uploadDir.exists()) {
                uploadDir.mkdirs()
            }

            val gzFile = File(uploadDir, gzFileName)

            Logger.logStream("$TAG 开始压缩文件: ${logFile.name} -> ${gzFile.name}")
            Logger.logStream("$TAG 文件大小: ${logFile.length()} bytes")

            // 再次检查文件是否存在（防止在处理过程中被删除）
            if (!logFile.exists()) {
                Logger.logStreamW("$TAG 文件在压缩前被删除: ${logFile.absolutePath}")
                return@withContext null
            }

            // 使用LogProcessor进行压缩，直接输出到upload目录，使用时间范围文件名
            val compressResult = logProcessor.compressFile(logFile, uploadDir, gzFileName,
                CompressionType.GZIP
            )

            if (compressResult.isSuccess) {
                val compressedInfo = compressResult.getOrNull()
                if (compressedInfo != null) {
                    val finalFile = File(compressedInfo.path)
                    Logger.logStream("$TAG 压缩文件生成: ${finalFile.absolutePath}")

                    val originalSize = logFile.length()
                    val compressedSize = finalFile.length()
                    val compressionRatio = if (originalSize > 0) {
                        (compressedSize.toDouble() / originalSize) * 100
                    } else {
                        100.0
                    }
                    Logger.logStream("$TAG 文件压缩成功: ${finalFile.name}")
                    Logger.logStream("$TAG 压缩统计 - 原始: ${formatSize(originalSize)}, 压缩后: ${formatSize(compressedSize)}, 压缩比: ${String.format("%.2f", compressionRatio)}%")
                    finalFile
                } else {
                    Logger.logStreamE("$TAG 文件压缩结果为空: ${logFile.name}")
                    null
                }
            } else {
                Logger.logStreamE("$TAG 文件压缩失败: ${logFile.name}", compressResult.exceptionOrNull())
                null
            }

        } catch (e: Exception) {
            Logger.logStreamE("$TAG 压缩文件异常: ${logFile.name}", e)
            null
        }
    }

    /**
     * 移动压缩文件到目标位置
     */
    private fun moveCompressedFileToTarget(sourceFile: File, targetFile: File): File? {
        return try {
            Logger.logStream("$TAG 移动压缩文件: ${sourceFile.absolutePath} -> ${targetFile.absolutePath}")

            // 检查源文件是否存在
            if (!sourceFile.exists()) {
                Logger.logStreamE("$TAG 源文件不存在: ${sourceFile.absolutePath}")
                return null
            }

            // 确保目标目录存在
            targetFile.parentFile?.let { parentDir ->
                if (!parentDir.exists()) {
                    Logger.logStream("$TAG 创建目标目录: ${parentDir.absolutePath}")
                    parentDir.mkdirs()
                }
            }

            // 如果目标文件已存在，先删除
            if (targetFile.exists()) {
                Logger.logStreamW("$TAG 目标文件已存在，删除: ${targetFile.name}")
                targetFile.delete()
            }

            // 尝试重命名（移动）
            if (sourceFile.renameTo(targetFile)) {
                Logger.logStream("$TAG 文件移动成功: ${targetFile.name}")
                return targetFile
            } else {
                Logger.logStreamW("$TAG 重命名失败，尝试复制+删除")
                return copyAndDeleteFile(sourceFile, targetFile)
            }

        } catch (e: Exception) {
            Logger.logStreamE("$TAG 移动压缩文件异常", e)
            null
        }
    }

    /**
     * 重命名压缩文件（带重试机制）
     */
    private fun renameCompressedFileWithRetry(sourceFile: File, targetFile: File, maxRetries: Int = 3): File? {
        repeat(maxRetries) { attempt ->
            try {
                // 如果目标文件已存在，先删除
                if (targetFile.exists()) {
                    Logger.logStreamW("$TAG 目标文件已存在，删除: ${targetFile.name}")
                    targetFile.delete()
                }

                // 确保目标目录存在
                targetFile.parentFile?.let { parentDir ->
                    if (!parentDir.exists()) {
                        parentDir.mkdirs()
                    }
                }

                // 尝试重命名
                if (sourceFile.renameTo(targetFile)) {
                    Logger.logStream("$TAG 文件重命名成功: ${sourceFile.name} -> ${targetFile.name}")
                    return targetFile
                } else {
                    Logger.logStreamW("$TAG 重命名失败 (尝试 ${attempt + 1}/$maxRetries): ${sourceFile.name} -> ${targetFile.name}")

                    // 如果重命名失败，尝试复制+删除
                    if (attempt == maxRetries - 1) {
                        return copyAndDeleteFile(sourceFile, targetFile)
                    }
                }

                // 短暂等待后重试
                Thread.sleep(100)

            } catch (e: Exception) {
                Logger.logStreamE("$TAG 重命名文件异常 (尝试 ${attempt + 1}/$maxRetries)", e)
                if (attempt == maxRetries - 1) {
                    return copyAndDeleteFile(sourceFile, targetFile)
                }
            }
        }
        return null
    }

    /**
     * 复制文件并删除原文件（作为重命名的备选方案）
     */
    private fun copyAndDeleteFile(sourceFile: File, targetFile: File): File? {
        return try {
            Logger.logStreamW("$TAG 使用复制+删除方式处理文件: ${sourceFile.name} -> ${targetFile.name}")

            sourceFile.copyTo(targetFile, overwrite = true)
            sourceFile.delete()

            if (targetFile.exists()) {
                Logger.logStream("$TAG 文件复制成功: ${targetFile.name}")
                targetFile
            } else {
                Logger.logStreamE("$TAG 文件复制失败: ${targetFile.name}")
                null
            }
        } catch (e: Exception) {
            Logger.logStreamE("$TAG 文件复制异常", e)
            null
        }
    }

    /**
     * 解析日志文件的时间范围
     */
    private fun parseLogFileTimeRange(logFile: File): TimeRange {
        return try {
            // 从文件名解析开始时间
            val fileName = logFile.nameWithoutExtension
            val startTime = if (fileName.contains("logcat_")) {
                fileName.substring(fileName.indexOf("logcat_") + 7)
            } else {
                // 读取文件内容获取真实的开始时间
                parseActualStartTimeFromFile(logFile)
            }

            // 读取文件内容获取真实的结束时间
            val endTime = parseActualEndTimeFromFile(logFile)

            TimeRange(startTime, endTime)

        } catch (e: Exception) {
            Logger.logStreamE("$TAG 解析文件时间范围失败: ${logFile.name}", e)
            val currentTime = java.text.SimpleDateFormat("yyyyMMdd_HHmmss", java.util.Locale.getDefault()).format(java.util.Date())
            TimeRange(currentTime, currentTime)
        }
    }

    /**
     * 从日志文件内容解析实际开始时间
     */
    private fun parseActualStartTimeFromFile(logFile: File): String {
        return try {
            logFile.useLines { lines ->
                for (line in lines) {
                    val timeMatch = extractTimeFromLogLine(line)
                    if (timeMatch != null) {
                        return@useLines timeMatch
                    }
                }
                // 如果没有找到时间，使用文件修改时间
                java.text.SimpleDateFormat("yyyyMMdd_HHmmss", java.util.Locale.getDefault())
                    .format(java.util.Date(logFile.lastModified()))
            }
        } catch (e: Exception) {
            Logger.logStreamE("$TAG 解析开始时间失败: ${logFile.name}", e)
            java.text.SimpleDateFormat("yyyyMMdd_HHmmss", java.util.Locale.getDefault())
                .format(java.util.Date(logFile.lastModified()))
        }
    }

    /**
     * 从日志文件内容解析实际结束时间
     */
    private fun parseActualEndTimeFromFile(logFile: File): String {
        return try {
            var lastTime: String? = null
            logFile.useLines { lines ->
                for (line in lines) {
                    val timeMatch = extractTimeFromLogLine(line)
                    if (timeMatch != null) {
                        lastTime = timeMatch
                    }
                }
            }
            lastTime ?: java.text.SimpleDateFormat("yyyyMMdd_HHmmss", java.util.Locale.getDefault())
                .format(java.util.Date(logFile.lastModified()))
        } catch (e: Exception) {
            Logger.logStreamE("$TAG 解析结束时间失败: ${logFile.name}", e)
            java.text.SimpleDateFormat("yyyyMMdd_HHmmss", java.util.Locale.getDefault())
                .format(java.util.Date(logFile.lastModified()))
        }
    }

    /**
     * 从日志行中提取时间戳并转换为文件名格式
     */
    private fun extractTimeFromLogLine(line: String): String? {
        return try {
            // 匹配格式: 07-17 15:39:27.637
            val timeRegex = Regex("""(\d{2})-(\d{2}) (\d{2}):(\d{2}):(\d{2})\.(\d{3})""")
            val matchResult = timeRegex.find(line)

            if (matchResult != null) {
                val (month, day, hour, minute, second, _) = matchResult.destructured
                val currentYear = java.util.Calendar.getInstance().get(java.util.Calendar.YEAR)
                return "${currentYear}${month}${day}_${hour}${minute}${second}"
            }
            null
        } catch (e: Exception) {
            null
        }
    }

    /**
     * 格式化文件大小显示
     */
    private fun formatSize(bytes: Long): String {
        return when {
            bytes >= 1024 * 1024 * 1024 -> "${bytes / (1024 * 1024 * 1024)}GB"
            bytes >= 1024 * 1024 -> "${bytes / (1024 * 1024)}MB"
            bytes >= 1024 -> "${bytes / 1024}KB"
            else -> "${bytes}B"
        }
    }

    /**
     * 清理存储空间
     */
    private suspend fun cleanupStorageSpace() = withContext(Dispatchers.IO) {
        try {
            Logger.logStream("$TAG 开始存储空间清理检查")

            // 检查压缩文件总大小
            val uploadDir = File(logCollector.getLogDir(), "upload")
            if (uploadDir.exists()) {
                val compressedFiles: Array<File> = uploadDir.listFiles { file ->
                    file.isFile && file.name.endsWith(".gz")
                } ?: emptyArray()

                val sortedFiles = compressedFiles.sortedBy { it.lastModified() }
                val totalCompressedSize = sortedFiles.sumOf { it.length() }
                val compressedLimit = LogStreamConfigManager.getCompressedStorageLimit()

                Logger.logStream("$TAG 压缩文件总大小: ${formatSize(totalCompressedSize)}, 限制: ${formatSize(compressedLimit)}")

                if (totalCompressedSize > compressedLimit) {
                    val sizeToClean = totalCompressedSize - compressedLimit
                    Logger.logStream("$TAG 压缩文件超出限制，需要清理: ${formatSize(sizeToClean)}")

                    var cleanedSize = 0L
                    for (file in sortedFiles) {
                        if (cleanedSize >= sizeToClean) break

                        Logger.logStream("$TAG 删除旧压缩文件: ${file.name}")
                        cleanedSize += file.length()

                        // 从压缩队列中移除
                        LogQueue.getGzQueue().remove(file.absolutePath)
                        file.delete()
                    }

                    LogQueue.saveGzQueueToFile()
                    Logger.logStream("$TAG 压缩文件清理完成，释放空间: ${formatSize(cleanedSize)}")
                }
            }

            // 检查原始日志文件总大小
            val logDir = logCollector.getLogDir()
            val rawFiles: Array<File> = logDir.listFiles { file ->
                file.isFile && file.name.endsWith(".log")
            } ?: emptyArray()

            val sortedRawFiles = rawFiles.sortedBy { it.lastModified() }
            val totalRawSize = sortedRawFiles.sumOf { it.length() }
            val rawLimit = LogStreamConfigManager.getRawStorageLimit()

            Logger.logStream("$TAG 原始日志总大小: ${formatSize(totalRawSize)}, 限制: ${formatSize(rawLimit)}")

            if (totalRawSize > rawLimit) {
                val sizeToClean = totalRawSize - rawLimit
                Logger.logStream("$TAG 原始日志超出限制，需要清理: ${formatSize(sizeToClean)}")

                var cleanedSize = 0L
                for (file in sortedRawFiles) {
                    if (cleanedSize >= sizeToClean) break

                    // 不删除当前正在写入的文件
                    if (file == logCollector.getCurrentLogFile()) continue

                    Logger.logStream("$TAG 删除旧原始日志: ${file.name}")
                    cleanedSize += file.length()

                    // 从日志队列中移除
                    LogQueue.removeLogFile(file.absolutePath)
                    file.delete()
                }

                LogQueue.saveLogQueueToFile()
                Logger.logStream("$TAG 原始日志清理完成，释放空间: ${formatSize(cleanedSize)}")
            }

        } catch (e: Exception) {
            Logger.logStreamE("$TAG 存储空间清理失败", e)
        }
    }



    /**
     * 时间范围数据类
     */
    private data class TimeRange(
        val startTime: String,
        val endTime: String
    )
}
