--------- beginning of system
--------- beginning of main
2025-08-19 13:09:10.652 10805-11910 Common                  com.dspread.mdm.service              D  🔧 OsUpdateStatusChecker WebSocket连接成功，检查OS升级状态
2025-08-19 13:09:10.653 10805-11910 Common                  com.dspread.mdm.service              D  🔧 OsUpdateStatusChecker 没有待检查的OS升级任务
2025-08-19 13:09:10.654 10805-11910 WebSocket               com.dspread.mdm.service              I  🔧 OS升级状态检查已触发
2025-08-19 13:09:40.168 10805-10805 Receiver                com.dspread.mdm.service              D  🔧 电池状态更新: 电池状态: 电量=75%, 温度=29.5°C, 健康=2, 充电=是, 低电量=否, 耗尽=否
2025-08-19 13:10:10.305 10805-11911 WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 1
2025-08-19 13:10:10.318 10805-11910 WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误 (Ask Gemini)
                                                                                                    com.neovisionaries.ws.client.NoMoreFrameException: No more WebSocket frame from the server.
                                                                                                    	at com.neovisionaries.ws.client.WebSocketInputStream.readFrame(WebSocketInputStream.java:54)
                                                                                                    	at com.neovisionaries.ws.client.ReadingThread.readFrame(ReadingThread.java:338)
                                                                                                    	at com.neovisionaries.ws.client.ReadingThread.main(ReadingThread.java:99)
                                                                                                    	at com.neovisionaries.ws.client.ReadingThread.runMain(ReadingThread.java:64)
                                                                                                    	at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45)
2025-08-19 13:10:10.319 10805-11910 WebSocket               com.dspread.mdm.service              I  🔧 onError: 不触发重连，等待 onDisconnected 处理
2025-08-19 13:10:10.323 10805-11911 WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误 (Ask Gemini)
                                                                                                    com.neovisionaries.ws.client.WebSocketException: Flushing frames to the server failed: Socket closed
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.doFlush(WritingThread.java:434)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.sendFrames(WritingThread.java:384)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.main(WritingThread.java:108)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.runMain(WritingThread.java:55)
                                                                                                    	at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45)
                                                                                                    Caused by: java.net.SocketException: Socket closed
                                                                                                    	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.writeInternal(ConscryptEngineSocket.java:681)
                                                                                                    	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.write(ConscryptEngineSocket.java:652)
                                                                                                    	at java.io.BufferedOutputStream.flushBuffer(BufferedOutputStream.java:82)
                                                                                                    	at java.io.BufferedOutputStream.flush(BufferedOutputStream.java:140)
                                                                                                    	at java.io.BufferedOutputStream.flush(BufferedOutputStream.java:141)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.flush(WritingThread.java:271)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.doFlush(WritingThread.java:422)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.sendFrames(WritingThread.java:384) 
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.main(WritingThread.java:108) 
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.runMain(WritingThread.java:55) 
                                                                                                    	at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45) 
2025-08-19 13:10:10.325 10805-11911 WebSocket               com.dspread.mdm.service              I  🔧 onError: 不触发重连，等待 onDisconnected 处理
2025-08-19 13:10:10.327 10805-11911 WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误 (Ask Gemini)
                                                                                                    com.neovisionaries.ws.client.WebSocketException: Flushing frames to the server failed: Socket closed
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.doFlush(WritingThread.java:434)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.sendFrames(WritingThread.java:384)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.main(WritingThread.java:120)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.runMain(WritingThread.java:55)
                                                                                                    	at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45)
                                                                                                    Caused by: java.net.SocketException: Socket closed
                                                                                                    	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.writeInternal(ConscryptEngineSocket.java:681)
                                                                                                    	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.write(ConscryptEngineSocket.java:652)
                                                                                                    	at java.io.BufferedOutputStream.flushBuffer(BufferedOutputStream.java:82)
                                                                                                    	at java.io.BufferedOutputStream.flush(BufferedOutputStream.java:140)
                                                                                                    	at java.io.BufferedOutputStream.flush(BufferedOutputStream.java:141)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.flush(WritingThread.java:271)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.doFlush(WritingThread.java:422)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.sendFrames(WritingThread.java:384) 
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.main(WritingThread.java:120) 
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.runMain(WritingThread.java:55) 
                                                                                                    	at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45) 
2025-08-19 13:10:10.329 10805-11911 WebSocket               com.dspread.mdm.service              I  🔧 onError: 不触发重连，等待 onDisconnected 处理
2025-08-19 13:10:10.332 10805-11911 WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接断开 - 客户端主动断开
2025-08-19 13:10:10.333 10805-11911 WebSocket               com.dspread.mdm.service              I  🔧    服务器关闭帧: code=-1, reason=无原因
2025-08-19 13:10:10.334 10805-11911 WebSocket               com.dspread.mdm.service              I  🔧    客户端关闭帧: code=1002, reason=No more WebSocket frame from the server.
2025-08-19 13:10:10.335 10805-11911 WebSocket               com.dspread.mdm.service              I  🔧    断开发起方: 客户端
2025-08-19 13:10:10.337 10805-11911 WebSocket               com.dspread.mdm.service              I  🔧 reconnect() 被调用，调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onDisconnected:724
2025-08-19 13:10:10.338 10805-11911 WebSocket               com.dspread.mdm.service              I  🔧 开始重连 WebSocket... (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onDisconnected:724)
2025-08-19 13:10:10.342 10805-11911 WebSocket               com.dspread.mdm.service              I  🔧 使用Provisioning配置的重连策略: delayPolicy=1, delaySwitch=1, delayTime=60
2025-08-19 13:10:10.344 10805-11911 WebSocket               com.dspread.mdm.service              I  🔧 当前断开记录数: 10, 延迟开关: 1
2025-08-19 13:10:10.347 10805-11911 WebSocket               com.dspread.mdm.service              I  🔧 断开记录 最早一次断开: 2025-08-19 12:59:22
2025-08-19 13:10:10.350 10805-11911 WebSocket               com.dspread.mdm.service              I  🔧 断开记录 最后一次断开: 2025-08-19 13:10:10
2025-08-19 13:10:10.351 10805-11911 WebSocket               com.dspread.mdm.service              I  🔧 时间差: 10分钟 (配置阈值: 1分钟)
2025-08-19 13:10:10.352 10805-11911 WebSocket               com.dspread.mdm.service              I  🔧 断开间隔正常，使用标准重连策略
2025-08-19 13:10:10.353 10805-11911 WebSocket               com.dspread.mdm.service              I  🔧 重连策略计算完成: 第1次重连，间隔3000ms (3秒)
2025-08-19 13:10:10.354 10805-11911 WebSocket               com.dspread.mdm.service              I  🔧 开始第1次重连，间隔3000ms (3秒) (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onDisconnected:724)
2025-08-19 13:10:13.373 10805-10805 WebSocket               com.dspread.mdm.service              I  🔧 重连 WebSocket: wss://api.dspreadserv.net/status/websocket/register?serialNo=MDE2MTAwNDAyMDIzMDcwNjAyMjc=&publicKey=TUlHZk1BMEdDU3FHU0liM0RRRUJBUVVBQTRHTkFEQ0JpUUtCZ1FEQXEvOE5rMFJ1dEVpUW9yTTdXYTg4SkNEbTViQUJ3VlMyZFFvRWV2NEZDdGVLL3loZnVFSUUxZ2lBdVFsa05kZkhVOEJVUU1FWEoxZEZoVGl4RUhQUDRIQXRMbUU0aHFpMjdDL0NYZnBER3hIMVY5QmpVTytXVVArd2k2c2VDU21uYzBqRE5odlZuQjl1QjFrUlBkdm4wTVVXa2Jad3JUOFdaU0xJMHlJYzl3SURBUUFC&query=0&msgVer=3&timestamp=1755580213362&signature=tUTT4djWGZwMioHyWVoHPoLxUj0YDEP1Vu+b2FdCRCaFh8qrUduE2h/kNVg5W6nuVi75AFK5y2wNUmdVINlsnrRTIOm2aXBj2iMZV43g3fJ8MM2sfZtEoIZ5yYADTzwbzZujA8MSv/At8sARmLlBdnWWvsRreb/UoQqLNi6VcNQ=
2025-08-19 13:10:13.375 10805-10805 WebSocket               com.dspread.mdm.service              I  🔧 未检测到系统代理设置
2025-08-19 13:10:13.586 10805-11915 System.out              com.dspread.mdm.service              I  [socket]:check permission begin!
2025-08-19 13:10:13.837 10805-11916 System.out              com.dspread.mdm.service              I  [socket]:check permission begin!
2025-08-19 13:10:15.634 10805-11917 Common                  com.dspread.mdm.service              I  ✅ WebSocket 物理连接成功，等待服务器 hellMsg
2025-08-19 13:10:15.635 10805-11917 WebSocket               com.dspread.mdm.service              I  🔧 ===== 心跳发送器实际配置 =====
2025-08-19 13:10:15.637 10805-11917 WebSocket               com.dspread.mdm.service              I  🔧 实际 PING 发送间隔: 60000ms (60秒)
2025-08-19 13:10:15.638 10805-11917 WebSocket               com.dspread.mdm.service              I  🔧 PING 定时器名称: PingSender
2025-08-19 13:10:15.639 10805-11917 WebSocket               com.dspread.mdm.service              I  🔧 实际 PONG 发送间隔: 0ms (0秒)
2025-08-19 13:10:15.640 10805-11917 WebSocket               com.dspread.mdm.service              I  🔧 PONG 定时器名称: PongSender
2025-08-19 13:10:15.641 10805-11917 WebSocket               com.dspread.mdm.service              I  🔧 ===============================
2025-08-19 13:10:15.654 10805-11917 Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"hellMsg":"hello 01610040202307060227","tranCode":"S0000","version":"1","rebootTime":"02:42:28","serialNo":"01610040202307060227","deviceStatus":"6"}
2025-08-19 13:10:15.655 10805-11917 Common                  com.dspread.mdm.service              I  ✅ 收到服务器 hellMsg，WebSocket 业务连接成功！
2025-08-19 13:10:15.656 10805-11917 Common                  com.dspread.mdm.service              I  ✅ WebSocket 业务连接成功！
2025-08-19 13:10:15.657 10805-11917 WebSocket               com.dspread.mdm.service              I  🔧 业务连接成功，重连计数已重置为 0
2025-08-19 13:10:15.660 10805-11917 Common                  com.dspread.mdm.service              I  ✅ ⏰ 设置任务执行定时器成功，下次执行: 60秒后 (1分钟)
2025-08-19 13:10:15.660 10805-11917 WebSocket               com.dspread.mdm.service              I  🔧 已启动任务执行定时器
2025-08-19 13:10:15.663 10805-11917 Common                  com.dspread.mdm.service              I  ✅ ⏰ 设置心跳定时器成功，下次执行: 60秒后 (1分钟)
2025-08-19 13:10:15.664 10805-11917 WebSocket               com.dspread.mdm.service              I  🔧 已启动心跳定时器
2025-08-19 13:10:15.667 10805-11917 Common                  com.dspread.mdm.service              I  ✅ ⏰ 设置终端信息定时器成功，下次执行: 120秒后 (2分钟)
2025-08-19 13:10:15.668 10805-11917 WebSocket               com.dspread.mdm.service              I  🔧 已启动终端信息上传定时器
2025-08-19 13:10:15.668 10805-11917 WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接成功，检查自身更新任务状态
2025-08-19 13:10:15.670 10805-11917 Receiver                com.dspread.mdm.service              D  🔧 发送广播: com.dspread.mdm.service.CHECK_SELF_UPDATE_STATUS
2025-08-19 13:10:15.671 10805-11917 WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接成功
2025-08-19 13:10:15.672 10805-11917 WebSocket               com.dspread.mdm.service              I  🔧 设备状态: 6
2025-08-19 13:10:15.673 10805-11917 WebSocket               com.dspread.mdm.service              I  🔧 检查首次连接标志: isFirstTimeWebSocketConnected=false
2025-08-19 13:10:15.673 10805-11917 WebSocket               com.dspread.mdm.service              I  🔧 非首次连接，跳过初始化信息发送和初始化检查
2025-08-19 13:10:15.674 10805-11917 WebSocket               com.dspread.mdm.service              I  🔧 执行必要的状态同步检查
2025-08-19 13:10:15.675 10805-11917 WebSocket               com.dspread.mdm.service              I  🔧 检查服务更新结果
2025-08-19 13:10:15.675 10805-11917 Task                    com.dspread.mdm.service              W  ⚠️ 任务列表为空，跳过服务更新结果检查
2025-08-19 13:10:15.676 10805-11917 WebSocket               com.dspread.mdm.service              I  🔧 检查OS升级状态...
2025-08-19 13:10:15.677 10805-11917 Common                  com.dspread.mdm.service              D  🔧 OsUpdateStatusChecker WebSocket连接成功，检查OS升级状态
2025-08-19 13:10:15.678 10805-11917 Common                  com.dspread.mdm.service              D  🔧 OsUpdateStatusChecker 没有待检查的OS升级任务
2025-08-19 13:10:15.678 10805-11917 WebSocket               com.dspread.mdm.service              I  🔧 OS升级状态检查已触发
2025-08-19 13:10:21.543 10805-10805 Receiver                com.dspread.mdm.service              D  🔧 电池状态更新: 电池状态: 电量=75%, 温度=29.5°C, 健康=2, 充电=是, 低电量=否, 耗尽=否
2025-08-19 13:11:15.535 10805-11917 WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误 (Ask Gemini)
                                                                                                    com.neovisionaries.ws.client.NoMoreFrameException: No more WebSocket frame from the server.
                                                                                                    	at com.neovisionaries.ws.client.WebSocketInputStream.readFrame(WebSocketInputStream.java:54)
                                                                                                    	at com.neovisionaries.ws.client.ReadingThread.readFrame(ReadingThread.java:338)
                                                                                                    	at com.neovisionaries.ws.client.ReadingThread.main(ReadingThread.java:99)
                                                                                                    	at com.neovisionaries.ws.client.ReadingThread.runMain(ReadingThread.java:64)
                                                                                                    	at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45)
2025-08-19 13:11:15.536 10805-11917 WebSocket               com.dspread.mdm.service              I  🔧 onError: 不触发重连，等待 onDisconnected 处理
2025-08-19 13:11:15.541 10805-11918 WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误 (Ask Gemini)
                                                                                                    com.neovisionaries.ws.client.WebSocketException: Flushing frames to the server failed: Socket closed
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.doFlush(WritingThread.java:434)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.sendFrames(WritingThread.java:384)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.main(WritingThread.java:108)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.runMain(WritingThread.java:55)
                                                                                                    	at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45)
                                                                                                    Caused by: java.net.SocketException: Socket closed
                                                                                                    	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.writeInternal(ConscryptEngineSocket.java:681)
                                                                                                    	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.write(ConscryptEngineSocket.java:652)
                                                                                                    	at java.io.BufferedOutputStream.flushBuffer(BufferedOutputStream.java:82)
                                                                                                    	at java.io.BufferedOutputStream.flush(BufferedOutputStream.java:140)
                                                                                                    	at java.io.BufferedOutputStream.flush(BufferedOutputStream.java:141)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.flush(WritingThread.java:271)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.doFlush(WritingThread.java:422)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.sendFrames(WritingThread.java:384) 
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.main(WritingThread.java:108) 
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.runMain(WritingThread.java:55) 
                                                                                                    	at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45) 
2025-08-19 13:11:15.543 10805-11918 WebSocket               com.dspread.mdm.service              I  🔧 onError: 不触发重连，等待 onDisconnected 处理
2025-08-19 13:11:15.545 10805-11918 WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误 (Ask Gemini)
                                                                                                    com.neovisionaries.ws.client.WebSocketException: Flushing frames to the server failed: Socket closed
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.doFlush(WritingThread.java:434)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.sendFrames(WritingThread.java:384)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.main(WritingThread.java:120)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.runMain(WritingThread.java:55)
                                                                                                    	at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45)
                                                                                                    Caused by: java.net.SocketException: Socket closed
                                                                                                    	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.writeInternal(ConscryptEngineSocket.java:681)
                                                                                                    	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.write(ConscryptEngineSocket.java:652)
                                                                                                    	at java.io.BufferedOutputStream.flushBuffer(BufferedOutputStream.java:82)
                                                                                                    	at java.io.BufferedOutputStream.flush(BufferedOutputStream.java:140)
                                                                                                    	at java.io.BufferedOutputStream.flush(BufferedOutputStream.java:141)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.flush(WritingThread.java:271)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.doFlush(WritingThread.java:422)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.sendFrames(WritingThread.java:384) 
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.main(WritingThread.java:120) 
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.runMain(WritingThread.java:55) 
                                                                                                    	at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45) 
2025-08-19 13:11:15.546 10805-11918 WebSocket               com.dspread.mdm.service              I  🔧 onError: 不触发重连，等待 onDisconnected 处理
2025-08-19 13:11:15.549 10805-11918 WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接断开 - 客户端主动断开
2025-08-19 13:11:15.551 10805-11918 WebSocket               com.dspread.mdm.service              I  🔧    服务器关闭帧: code=-1, reason=无原因
2025-08-19 13:11:15.552 10805-11918 WebSocket               com.dspread.mdm.service              I  🔧    客户端关闭帧: code=1002, reason=No more WebSocket frame from the server.
2025-08-19 13:11:15.553 10805-11918 WebSocket               com.dspread.mdm.service              I  🔧    断开发起方: 客户端
2025-08-19 13:11:15.554 10805-11918 WebSocket               com.dspread.mdm.service              I  🔧 reconnect() 被调用，调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onDisconnected:724
2025-08-19 13:11:15.556 10805-11918 WebSocket               com.dspread.mdm.service              I  🔧 开始重连 WebSocket... (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onDisconnected:724)
2025-08-19 13:11:15.560 10805-11918 WebSocket               com.dspread.mdm.service              I  🔧 使用Provisioning配置的重连策略: delayPolicy=1, delaySwitch=1, delayTime=60
2025-08-19 13:11:15.561 10805-11918 WebSocket               com.dspread.mdm.service              I  🔧 当前断开记录数: 10, 延迟开关: 1
2025-08-19 13:11:15.565 10805-11918 WebSocket               com.dspread.mdm.service              I  🔧 断开记录 最早一次断开: 2025-08-19 13:00:28
2025-08-19 13:11:15.567 10805-11918 WebSocket               com.dspread.mdm.service              I  🔧 断开记录 最后一次断开: 2025-08-19 13:11:15
2025-08-19 13:11:15.569 10805-11918 WebSocket               com.dspread.mdm.service              I  🔧 时间差: 10分钟 (配置阈值: 1分钟)
2025-08-19 13:11:15.570 10805-11918 WebSocket               com.dspread.mdm.service              I  🔧 断开间隔正常，使用标准重连策略
2025-08-19 13:11:15.571 10805-11918 WebSocket               com.dspread.mdm.service              I  🔧 重连策略计算完成: 第1次重连，间隔3000ms (3秒)
2025-08-19 13:11:15.572 10805-11918 WebSocket               com.dspread.mdm.service              I  🔧 开始第1次重连，间隔3000ms (3秒) (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onDisconnected:724)
2025-08-19 13:11:18.592 10805-10805 WebSocket               com.dspread.mdm.service              I  🔧 重连 WebSocket: wss://api.dspreadserv.net/status/websocket/register?serialNo=MDE2MTAwNDAyMDIzMDcwNjAyMjc=&publicKey=TUlHZk1BMEdDU3FHU0liM0RRRUJBUVVBQTRHTkFEQ0JpUUtCZ1FEQXEvOE5rMFJ1dEVpUW9yTTdXYTg4SkNEbTViQUJ3VlMyZFFvRWV2NEZDdGVLL3loZnVFSUUxZ2lBdVFsa05kZkhVOEJVUU1FWEoxZEZoVGl4RUhQUDRIQXRMbUU0aHFpMjdDL0NYZnBER3hIMVY5QmpVTytXVVArd2k2c2VDU21uYzBqRE5odlZuQjl1QjFrUlBkdm4wTVVXa2Jad3JUOFdaU0xJMHlJYzl3SURBUUFC&query=0&msgVer=3&timestamp=1755580278580&signature=HZnBqWYnCO8RmK1acZXqlRjBIjqhf5OKGAEKMjUjflqOWHORI+SrO7H95fVMPvmyS3WmPJuvWtKdrsHOM8uJuDDjhFZPSUZTW4lIGJJ5bcl+ZzCK6A8GlIcRmHwlIkjbmsHe00hYuDr1yduovivQosRgS2D5bqHoL8SUx8lfIew=
2025-08-19 13:11:18.594 10805-10805 WebSocket               com.dspread.mdm.service              I  🔧 未检测到系统代理设置
2025-08-19 13:11:19.020 10805-11922 System.out              com.dspread.mdm.service              I  [socket]:check permission begin!
2025-08-19 13:11:19.271 10805-11923 System.out              com.dspread.mdm.service              I  [socket]:check permission begin!
2025-08-19 13:11:21.068 10805-11924 Common                  com.dspread.mdm.service              I  ✅ WebSocket 物理连接成功，等待服务器 hellMsg
2025-08-19 13:11:21.070 10805-11924 WebSocket               com.dspread.mdm.service              I  🔧 ===== 心跳发送器实际配置 =====
2025-08-19 13:11:21.071 10805-11924 WebSocket               com.dspread.mdm.service              I  🔧 实际 PING 发送间隔: 60000ms (60秒)
2025-08-19 13:11:21.072 10805-11924 WebSocket               com.dspread.mdm.service              I  🔧 PING 定时器名称: PingSender
2025-08-19 13:11:21.073 10805-11924 WebSocket               com.dspread.mdm.service              I  🔧 实际 PONG 发送间隔: 0ms (0秒)
2025-08-19 13:11:21.075 10805-11924 WebSocket               com.dspread.mdm.service              I  🔧 PONG 定时器名称: PongSender
2025-08-19 13:11:21.076 10805-11924 WebSocket               com.dspread.mdm.service              I  🔧 ===============================
2025-08-19 13:11:21.088 10805-11924 Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"hellMsg":"hello 01610040202307060227","tranCode":"S0000","version":"1","rebootTime":"01:01:12","serialNo":"01610040202307060227","deviceStatus":"6"}
2025-08-19 13:11:21.089 10805-11924 Common                  com.dspread.mdm.service              I  ✅ 收到服务器 hellMsg，WebSocket 业务连接成功！
2025-08-19 13:11:21.091 10805-11924 Common                  com.dspread.mdm.service              I  ✅ WebSocket 业务连接成功！
2025-08-19 13:11:21.092 10805-11924 WebSocket               com.dspread.mdm.service              I  🔧 业务连接成功，重连计数已重置为 0
2025-08-19 13:11:21.094 10805-11924 Common                  com.dspread.mdm.service              I  ✅ ⏰ 设置任务执行定时器成功，下次执行: 60秒后 (1分钟)
2025-08-19 13:11:21.095 10805-11924 WebSocket               com.dspread.mdm.service              I  🔧 已启动任务执行定时器
2025-08-19 13:11:21.098 10805-11924 Common                  com.dspread.mdm.service              I  ✅ ⏰ 设置心跳定时器成功，下次执行: 60秒后 (1分钟)
2025-08-19 13:11:21.099 10805-11924 WebSocket               com.dspread.mdm.service              I  🔧 已启动心跳定时器
2025-08-19 13:11:21.102 10805-11924 Common                  com.dspread.mdm.service              I  ✅ ⏰ 设置终端信息定时器成功，下次执行: 120秒后 (2分钟)
2025-08-19 13:11:21.103 10805-11924 WebSocket               com.dspread.mdm.service              I  🔧 已启动终端信息上传定时器
2025-08-19 13:11:21.103 10805-11924 WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接成功，检查自身更新任务状态
2025-08-19 13:11:21.105 10805-11924 Receiver                com.dspread.mdm.service              D  🔧 发送广播: com.dspread.mdm.service.CHECK_SELF_UPDATE_STATUS
2025-08-19 13:11:21.106 10805-11924 WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接成功
2025-08-19 13:11:21.107 10805-11924 WebSocket               com.dspread.mdm.service              I  🔧 设备状态: 6
2025-08-19 13:11:21.108 10805-11924 WebSocket               com.dspread.mdm.service              I  🔧 检查首次连接标志: isFirstTimeWebSocketConnected=false
2025-08-19 13:11:21.109 10805-11924 WebSocket               com.dspread.mdm.service              I  🔧 非首次连接，跳过初始化信息发送和初始化检查
2025-08-19 13:11:21.109 10805-11924 WebSocket               com.dspread.mdm.service              I  🔧 执行必要的状态同步检查
2025-08-19 13:11:21.110 10805-11924 WebSocket               com.dspread.mdm.service              I  🔧 检查服务更新结果
2025-08-19 13:11:21.110 10805-11924 Task                    com.dspread.mdm.service              W  ⚠️ 任务列表为空，跳过服务更新结果检查
2025-08-19 13:11:21.111 10805-11924 WebSocket               com.dspread.mdm.service              I  🔧 检查OS升级状态...
2025-08-19 13:11:21.112 10805-11924 Common                  com.dspread.mdm.service              D  🔧 OsUpdateStatusChecker WebSocket连接成功，检查OS升级状态
2025-08-19 13:11:21.113 10805-11924 Common                  com.dspread.mdm.service              D  🔧 OsUpdateStatusChecker 没有待检查的OS升级任务
2025-08-19 13:11:21.113 10805-11924 WebSocket               com.dspread.mdm.service              I  🔧 OS升级状态检查已触发
2025-08-19 13:11:31.384 10805-10805 Receiver                com.dspread.mdm.service              D  🔧 电源已断开
2025-08-19 13:11:31.385 10805-10805 WebSocket               com.dspread.mdm.service              I  🔧 C0902 主动式上送: charging_state_change (主动: 3)
2025-08-19 13:11:31.405 10805-10805 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0902","serialNo":"01610040202307060227","request_time":"1755580291393","request_id":"1755580291393C0902","version":"1","data":{"batteryLife":75,"batteryHealth":2,"temprature":"29.5","isCharging":"1","isLowBattery":"0","powerOff":"0","outOfBattery":"0"},"myVersionName":"1.1.01.20250819.DSPREAD.MDM.SERVICE","terminalDate":"20250819131131"}
2025-08-19 13:11:31.407 10805-10805 WebSocket               com.dspread.mdm.service              I  🔧 C0902 电池状态上送成功 (1, trigger=charging_state_change)
2025-08-19 13:11:31.408 10805-10805 Receiver                com.dspread.mdm.service              D  🔧 电池状态更新: 电池状态: 电量=75%, 温度=29.7°C, 健康=2, 充电=否, 低电量=否, 耗尽=否
2025-08-19 13:11:31.560 10805-10805 PhoneWindow             com.dspread.mdm.service              V  DecorView setVisiblity: visibility = 0, Parent = android.view.ViewRootImpl@b3dd2e5, this = DecorView@1b2c1ba[TestActivity]
2025-08-19 13:11:31.562 10805-10805 PhoneWindow             com.dspread.mdm.service              V  DecorView setVisiblity: visibility = 0, Parent = android.view.ViewRootImpl@b3dd2e5, this = DecorView@1b2c1ba[TestActivity]
2025-08-19 13:11:31.660 10805-10805 Receiver                com.dspread.mdm.service              D  🔧 电池状态更新: 电池状态: 电量=75%, 温度=29.8°C, 健康=2, 充电=否, 低电量=否, 耗尽=否
2025-08-19 13:11:31.661 10805-10805 Receiver                com.dspread.mdm.service              D  🔧 ServiceManagementEventHandler 处理用户解锁事件
2025-08-19 13:11:31.664 10805-10805 Common                  com.dspread.mdm.service              D  🔧 ServiceStartupManager 服务正在运行，无需启动: USER_PRESENT
2025-08-19 13:11:31.690 10805-10805 Receiver                com.dspread.mdm.service              D  🔧 ServiceManagementEventHandler 用户解锁后服务检查完成
2025-08-19 13:11:31.748 10805-10805 Receiver                com.dspread.mdm.service              D  🔧 屏幕点亮
2025-08-19 13:11:31.749 10805-10805 WebSocket               com.dspread.mdm.service              W  ⚠️ 未知触发条件: screen_on，默认按被动式处理
2025-08-19 13:11:31.751 10805-10805 WebSocket               com.dspread.mdm.service              I  🔧 C0201 被动式上送拒绝: screen_on (拒绝: 3)
2025-08-19 13:11:31.751 10805-10805 WebSocket               com.dspread.mdm.service              I  🔧 C0201 设备状态上送被流量控制阻止: 被动事件 'screen_on' 在平衡模式 - 重要变化下未启用
2025-08-19 13:11:31.752 10805-10805 Receiver                com.dspread.mdm.service              D  🔧 屏幕点亮处理完成
2025-08-19 13:11:33.278 10805-10805 Receiver                com.dspread.mdm.service              D  🔧 电源已连接
2025-08-19 13:11:33.279 10805-10805 WebSocket               com.dspread.mdm.service              I  🔧 C0902 主动式上送: charging_state_change (主动: 4)
2025-08-19 13:11:33.296 10805-10805 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0902","serialNo":"01610040202307060227","request_time":"1755580293284","request_id":"1755580293284C0902","version":"1","data":{"batteryLife":75,"batteryHealth":2,"temprature":"29.6","isCharging":"1","isLowBattery":"0","powerOff":"0","outOfBattery":"0"},"myVersionName":"1.1.01.20250819.DSPREAD.MDM.SERVICE","terminalDate":"20250819131133"}
2025-08-19 13:11:33.297 10805-10805 WebSocket               com.dspread.mdm.service              I  🔧 C0902 电池状态上送成功 (1, trigger=charging_state_change)
2025-08-19 13:11:33.302 10805-10805 Receiver                com.dspread.mdm.service              D  🔧 电池状态更新: 电池状态: 电量=75%, 温度=29.6°C, 健康=2, 充电=是, 低电量=否, 耗尽=否
2025-08-19 13:11:33.365 10805-10805 Receiver                com.dspread.mdm.service              D  🔧 电池状态更新: 电池状态: 电量=75%, 温度=29.7°C, 健康=2, 充电=是, 低电量=否, 耗尽=否
2025-08-19 13:12:21.070 10805-11925 WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 1
2025-08-19 13:12:21.481 10805-11924 WebSocket               com.dspread.mdm.service              I  🔧 收到服务器 PONG 帧: 1
2025-08-19 13:12:21.555 10805-10805 Receiver                com.dspread.mdm.service              D  🔧 电池状态更新: 电池状态: 电量=75%, 温度=29.6°C, 健康=2, 充电=是, 低电量=否, 耗尽=否
2025-08-19 13:12:33.382 10805-10805 Receiver                com.dspread.mdm.service              D  🔧 电池状态更新: 电池状态: 电量=75%, 温度=29.6°C, 健康=2, 充电=是, 低电量=否, 耗尽=否
2025-08-19 13:12:33.391 10805-10805 PhoneWindow             com.dspread.mdm.service              V  DecorView setVisiblity: visibility = 4, Parent = android.view.ViewRootImpl@b3dd2e5, this = DecorView@1b2c1ba[TestActivity]
2025-08-19 13:12:33.642 10805-10805 Receiver                com.dspread.mdm.service              D  🔧 屏幕关闭
2025-08-19 13:12:33.643 10805-10805 WebSocket               com.dspread.mdm.service              W  ⚠️ 未知触发条件: screen_off，默认按被动式处理
2025-08-19 13:12:33.645 10805-10805 WebSocket               com.dspread.mdm.service              I  🔧 C0201 被动式上送拒绝: screen_off (拒绝: 4)
2025-08-19 13:12:33.646 10805-10805 WebSocket               com.dspread.mdm.service              I  🔧 C0201 设备状态上送被流量控制阻止: 被动事件 'screen_off' 在平衡模式 - 重要变化下未启用
2025-08-19 13:12:33.646 10805-10805 Receiver                com.dspread.mdm.service              D  🔧 屏幕关闭处理完成
2025-08-19 13:13:21.072 10805-11925 WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 2
2025-08-19 13:13:21.487 10805-11924 WebSocket               com.dspread.mdm.service              I  🔧 收到服务器 PONG 帧: 2
2025-08-19 13:13:21.563 10805-10805 Receiver                com.dspread.mdm.service              D  🔧 电池状态更新: 电池状态: 电量=75%, 温度=29.5°C, 健康=2, 充电=是, 低电量=否, 耗尽=否
2025-08-19 13:14:01.237 10805-11924 Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"msg_state":"P","request_time":"1755580441011","data":{"param":"","c_type":"CALLC0109"},"expire_time":1755584041,"tranCode":"SC004","request_id":"1755580441011SC004","version":"1","serialNo":"01610040202307060227"}
2025-08-19 13:14:01.239 10805-11924 WebSocket               com.dspread.mdm.service              I  🔧 准备发送响应确认 - requestId: 1755580441011SC004, needResponse: true
2025-08-19 13:14:01.251 10805-11924 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0000","serialNo":"01610040202307060227","request_time":"1755580441244","request_id":"1755580441244C0000","version":"1","org_request_id":"1755580441011SC004","org_request_time":"1755580441011","response_state":"0","myVersionName":"1.1.01.20250819.DSPREAD.MDM.SERVICE","terminalDate":"20250819131401"}
2025-08-19 13:14:01.262 10805-11924 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0000","serialNo":"01610040202307060227","request_time":"1755580441255","request_id":"1755580441255C0000","version":"1","org_request_id":"1755580441011SC004","org_request_time":"1755580441011","response_state":"0","myVersionName":"1.1.01.20250819.DSPREAD.MDM.SERVICE","terminalDate":"20250819131401"}
2025-08-19 13:14:01.263 10805-11924 WebSocket               com.dspread.mdm.service              I  🔧 已发送C0000响应确认: requestId=1755580441011SC004
2025-08-19 13:14:01.265 10805-11924 WebSocket               com.dspread.mdm.service              I  🔧 处理命令: SC004
2025-08-19 13:14:01.266 10805-11924 WebSocket               com.dspread.mdm.service              I  🔧 收到SC004命令，类型: CALLC0109
2025-08-19 13:14:01.267 10805-11924 WebSocket               com.dspread.mdm.service              I  🔧 服务器请求终端信息上传
2025-08-19 13:14:01.268 10805-11924 WebSocket               com.dspread.mdm.service              I  🔧 C0109 主动式上送: terminal_info_timer (主动: 5)
2025-08-19 13:14:01.273 10805-11924 Platform                com.dspread.mdm.service              D  🔧 重新收集应用信息（强制刷新）
2025-08-19 13:14:01.341 10805-11924 AppManager              com.dspread.mdm.service              I  ℹ️ 应用信息获取完成: 总数122(系统119/用户3) 返回3个
2025-08-19 13:14:01.346 10805-11924 Platform                com.dspread.mdm.service              D  🔧 应用信息: 3 个应用
2025-08-19 13:14:01.352 10805-10828 ead.mdm.servic          com.dspread.mdm.service              I  Waiting for a blocking GC ProfileSaver
2025-08-19 13:14:01.360 10805-10821 System                  com.dspread.mdm.service              W  A resource failed to call close. 
2025-08-19 13:14:01.363 10805-10821 chatty                  com.dspread.mdm.service              I  uid=1000(system) FinalizerDaemon identical 1 line
2025-08-19 13:14:01.366 10805-10821 System                  com.dspread.mdm.service              W  A resource failed to call close. 
2025-08-19 13:14:01.433 10805-11924 Platform                com.dspread.mdm.service              D  🔧 内存总容量为： 1.86GB 
2025-08-19 13:14:01.434 10805-11924 Platform                com.dspread.mdm.service              D  🔧 内存使用量为： 0.55GB 
2025-08-19 13:14:01.457 10805-11924 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0109","serialNo":"01610040202307060227","request_time":"1755580441445","request_id":"1755580441445C0109","version":"1","data":{"apkInfo":[{"packName":"de.blinkt.openvpn","apkName":"OpenVPN for Android","versionCode":65,"versionName":"0.5.36a","updateDate":"2025-08-18 16:11:59"},{"packName":"com.dspread.mdm.service","apkName":"service","versionCode":20,"versionName":"1.1.01.20250819.DSPREAD.MDM.SERVICE","updateDate":"2025-08-19 12:10:19"},{"packName":"mark.via","apkName":"Via","versionCode":20250117,"versionName":"6.2.0","updateDate":"2025-08-18 17:50:24"}],"locationInfo":{"gps":{"longitude":"-999.0","latitude":"-999.0","satelliteCount":0,"satelliteCountInUsed":0,"satelliteAverSNR":"0.00","distance":"0.0","lockStatus":"1","lockMeter":"200","proId":"","proName":""},"baseSite":{"MCC":"","MNC":"","CID":"-1","LAC":"-1","DBM":"-999","IMSI":"","ICCID":""},"wifi":{"SSID":"fubox_2.4G","IP":"**************","MAC":"52:C3:63:13:8C:EE","SSTH":"-27","BSSID":"7c:10:c9:d6:9f:10"},"address":"无法获取"},"hardwareInfo":[{"name":"CPU Usage","desc":"cpu:0.50"},{"name":"storage_usage_status","desc":" 5.39GB \/ 32.00GB ","state":"1"},{"name":"total_memory","desc":" 0.55GB \/ 1.86GB ","state":"1"},{"name":"androidVersion","desc":"11","state":"1"},{"name":"network_status","desc":"wifi","state":"1"},{"name":"OSVer","desc":"11","state":"1"}],"wifiOption":[{"SSID":"fubox_2.4G","SSTH":"-26"},{"SSID":"2206-5G","SSTH":"-31"},{"SSID":"2206","SSTH":"-39"},{"SSID":"@Ruijie-1816","SSTH":"-52"},{"SSID":"2205_5G","SSTH":"-54"},{"SSID":"2205","SSTH":"-56"},{"SSID":"DIRECT-C6-Mi All-in-One Inkjet","SSTH":"-57"},{"SSID":"2207","SSTH":"-66"},{"SSID":"DIRECT-CC-HP Laser 1188w","SSTH":"-69"},{"SSID":"2207-5G","SSTH":"-82"},{"SSID":"vivo Y51s","SSTH":"-83"}],"sytemInfo":{"androidVersion":"11","buildNumber":"D60_Baseline_debug_20250708_ota_test","aspl":"2021-12-05"}},"myVersionName":"1.1.01.20250819.DSPREAD.MDM.SERVICE","terminalDate":"20250819131401"}
2025-08-19 13:14:01.459 10805-11924 WebSocket               com.dspread.mdm.service              I  🔧 C0109 终端信息上传完成 (1, trigger=terminal_info_timer)
2025-08-19 13:14:01.460 10805-11924 WebSocket               com.dspread.mdm.service              I  🔧 同时上传C0901应用信息（含服务信息）
2025-08-19 13:14:01.461 10805-11924 WebSocket               com.dspread.mdm.service              I  🔧 检查默认开启的服务
2025-08-19 13:14:01.461 10805-11924 WebSocket               com.dspread.mdm.service              I  🔧 本地无LogStream服务信息
2025-08-19 13:14:01.462 10805-11924 WebSocket               com.dspread.mdm.service              I  🔧 LogStream服务未启用，跳过保存
2025-08-19 13:14:01.463 10805-11924 WebSocket               com.dspread.mdm.service              I  🔧 默认服务检查完成
2025-08-19 13:14:01.464 10805-11924 WebSocket               com.dspread.mdm.service              I  🔧 C0901 主动式上送: command_trigger (主动: 6)
2025-08-19 13:14:01.467 10805-11924 Platform                com.dspread.mdm.service              D  🔧 使用缓存的应用信息
2025-08-19 13:14:01.473 10805-11924 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0901","serialNo":"01610040202307060227","request_time":"1755580441467","request_id":"1755580441467C0901","version":"1","data":{"apkInfo":[{"packName":"de.blinkt.openvpn","apkName":"OpenVPN for Android","versionCode":65,"versionName":"0.5.36a","updateDate":"2025-08-18 16:11:59"},{"packName":"com.dspread.mdm.service","apkName":"service","versionCode":20,"versionName":"1.1.01.20250819.DSPREAD.MDM.SERVICE","updateDate":"2025-08-19 12:10:19"},{"packName":"mark.via","apkName":"Via","versionCode":20250117,"versionName":"6.2.0","updateDate":"2025-08-18 17:50:24"}],"sytemInfo":{"androidVersion":"11","buildNumber":"D60_Baseline_debug_20250708_ota_test","aspl":"2021-12-05"},"serviceInfo":[]},"myVersionName":"1.1.01.20250819.DSPREAD.MDM.SERVICE","terminalDate":"20250819131401"}
2025-08-19 13:14:01.474 10805-11924 WebSocket               com.dspread.mdm.service              I  🔧 C0901 应用信息上送成功 (1)
2025-08-19 13:14:02.341 10805-11924 Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"request_time":"1755580442043","org_request_time":"1755580441445","org_request_id":"1755580441445C0109","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755580442043S0000","serialNo":"01610040202307060227"}
2025-08-19 13:14:02.343 10805-11924 WebSocket               com.dspread.mdm.service              I  🔧 收到响应消息: transCode=S0000, orgRequestId=1755580441445C0109, state=0, remark=
2025-08-19 13:14:02.344 10805-11924 WebSocket               com.dspread.mdm.service              I  🔧 终端信息上传成功
2025-08-19 13:14:02.346 10805-11924 WebSocket               com.dspread.mdm.service              I  🔧 终端信息上传成功标志已设置
2025-08-19 13:14:05.618 10805-10805 PhoneWindow             com.dspread.mdm.service              V  DecorView setVisiblity: visibility = 0, Parent = android.view.ViewRootImpl@b3dd2e5, this = DecorView@1b2c1ba[TestActivity]
2025-08-19 13:14:05.623 10805-10805 PhoneWindow             com.dspread.mdm.service              V  DecorView setVisiblity: visibility = 0, Parent = android.view.ViewRootImpl@b3dd2e5, this = DecorView@1b2c1ba[TestActivity]
2025-08-19 13:14:05.698 10805-10805 Receiver                com.dspread.mdm.service              D  🔧 ServiceManagementEventHandler 处理用户解锁事件
2025-08-19 13:14:05.705 10805-10805 Common                  com.dspread.mdm.service              D  🔧 ServiceStartupManager 服务正在运行，无需启动: USER_PRESENT
2025-08-19 13:14:05.705 10805-10805 Receiver                com.dspread.mdm.service              D  🔧 ServiceManagementEventHandler 用户解锁后服务检查完成
2025-08-19 13:14:05.793 10805-10805 Receiver                com.dspread.mdm.service              D  🔧 屏幕点亮
2025-08-19 13:14:05.794 10805-10805 WebSocket               com.dspread.mdm.service              W  ⚠️ 未知触发条件: screen_on，默认按被动式处理
2025-08-19 13:14:05.796 10805-10805 WebSocket               com.dspread.mdm.service              I  🔧 C0201 被动式上送拒绝: screen_on (拒绝: 5)
2025-08-19 13:14:05.796 10805-10805 WebSocket               com.dspread.mdm.service              I  🔧 C0201 设备状态上送被流量控制阻止: 被动事件 'screen_on' 在平衡模式 - 重要变化下未启用
2025-08-19 13:14:05.797 10805-10805 Receiver                com.dspread.mdm.service              D  🔧 屏幕点亮处理完成
2025-08-19 13:14:21.072 10805-11925 WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 3
2025-08-19 13:14:21.412 10805-10805 PhoneWindow             com.dspread.mdm.service              V  DecorView setVisiblity: visibility = 4, Parent = android.view.ViewRootImpl@b3dd2e5, this = DecorView@1b2c1ba[TestActivity]
2025-08-19 13:14:21.420 10805-10805 View                    com.dspread.mdm.service              D  [Warning] assignParent to null: this = DecorView@1b2c1ba[TestActivity]
2025-08-19 13:14:21.426 10805-10805 InputTransport          com.dspread.mdm.service              I  Destroy ARC handle: 0xa2b9d210
2025-08-19 13:14:21.492 10805-11924 WebSocket               com.dspread.mdm.service              I  🔧 收到服务器 PONG 帧: 3
2025-08-19 13:14:21.568 10805-10805 Receiver                com.dspread.mdm.service              D  🔧 电池状态更新: 电池状态: 电量=75%, 温度=29.6°C, 健康=2, 充电=是, 低电量=否, 耗尽=否
2025-08-19 13:14:34.833 10805-11924 Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"msg_state":"P","request_time":"1755580474546","data":{"param":{"beginDate":"2024-08-19 05:14:34","taskType":"05","period":"1","endDate":"9999-12-31 23:59:59","serviceId":"856c2e6d2c7c4f60bc0fadcf9b9ad650","serviceName":"Log Streaming","command":"C03","url":"https://smartms.s3.sa-east-1.amazonaws.com/icon/5e80041b23fb441695e88253d4542c5b.png","taskId":"1755580474546"},"c_type":"CALL_LOG_STREAM"},"expire_time":1756185274,"tranCode":"SC004","request_id":"1755580474546SC004","version":"1","serialNo":"01610040202307060227"}
2025-08-19 13:14:34.835 10805-11924 WebSocket               com.dspread.mdm.service              I  🔧 准备发送响应确认 - requestId: 1755580474546SC004, needResponse: true
2025-08-19 13:14:34.843 10805-11924 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0000","serialNo":"01610040202307060227","request_time":"1755580474837","request_id":"1755580474837C0000","version":"1","org_request_id":"1755580474546SC004","org_request_time":"1755580474546","response_state":"0","myVersionName":"1.1.01.20250819.DSPREAD.MDM.SERVICE","terminalDate":"20250819131434"}
2025-08-19 13:14:34.851 10805-11924 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0000","serialNo":"01610040202307060227","request_time":"1755580474846","request_id":"1755580474846C0000","version":"1","org_request_id":"1755580474546SC004","org_request_time":"1755580474546","response_state":"0","myVersionName":"1.1.01.20250819.DSPREAD.MDM.SERVICE","terminalDate":"20250819131434"}
2025-08-19 13:14:34.852 10805-11924 WebSocket               com.dspread.mdm.service              I  🔧 已发送C0000响应确认: requestId=1755580474546SC004
2025-08-19 13:14:34.854 10805-11924 WebSocket               com.dspread.mdm.service              I  🔧 处理命令: SC004
2025-08-19 13:14:34.854 10805-11924 WebSocket               com.dspread.mdm.service              I  🔧 收到SC004命令，类型: CALL_LOG_STREAM
2025-08-19 13:14:34.855 10805-11924 WebSocket               com.dspread.mdm.service              I  🔧 服务器请求启动日志流
2025-08-19 13:14:35.010 10805-10866 System.out              com.dspread.mdm.service              I  [okhttp]:check permission begin!
2025-08-19 13:14:35.011 10805-10866 System.out              com.dspread.mdm.service              I  [okhttp]:not MMS!
2025-08-19 13:14:35.011 10805-10866 System.out              com.dspread.mdm.service              I  [okhttp]:not Email!
2025-08-19 13:14:35.111 10805-10866 System.out              com.dspread.mdm.service              I  [socket]:check permission begin!
2025-08-19 13:14:35.932 10805-10866 System.out              com.dspread.mdm.service              I  [OkHttp] sendRequest<<
2025-08-19 13:14:37.064 10805-10834 WebSocket               com.dspread.mdm.service              I  🔧 发送C0901应用信息响应（包含服务信息）
2025-08-19 13:14:37.066 10805-10834 WebSocket               com.dspread.mdm.service              I  🔧 服务信息已添加到管理器: 856c2e6d2c7c4f60bc0fadcf9b9ad650
2025-08-19 13:14:37.067 10805-10834 Platform                com.dspread.mdm.service              D  🔧 使用缓存的应用信息
2025-08-19 13:14:37.068 10805-10834 WebSocket               com.dspread.mdm.service              I  🔧 服务信息已添加到C0901响应中
2025-08-19 13:14:37.082 10805-10834 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0901","serialNo":"01610040202307060227","request_time":"1755580477069","request_id":"1755580477069C0901","version":"1","data":{"apkInfo":[{"packName":"de.blinkt.openvpn","apkName":"OpenVPN for Android","versionCode":65,"versionName":"0.5.36a","updateDate":"2025-08-18 16:11:59"},{"packName":"com.dspread.mdm.service","apkName":"service","versionCode":20,"versionName":"1.1.01.20250819.DSPREAD.MDM.SERVICE","updateDate":"2025-08-19 12:10:19"},{"packName":"mark.via","apkName":"Via","versionCode":20250117,"versionName":"6.2.0","updateDate":"2025-08-18 17:50:24"}],"sytemInfo":{"androidVersion":"11","buildNumber":"D60_Baseline_debug_20250708_ota_test","aspl":"2021-12-05"},"serviceInfo":[{"taskId":"1755580474546","serviceId":"856c2e6d2c7c4f60bc0fadcf9b9ad650","serviceName":"Log Stream","taskType":"05","command":"C03","stateDesc":"IMPLEMENTED","beginDate":"2025-08-19 13:14:37","endDate":"9999-12-31 23:59:59","period":"1","request_id":"","request_time":""}]},"myVersionName":"1.1.01.20250819.DSPREAD.MDM.SERVICE","terminalDate":"20250819131437"}
2025-08-19 13:14:37.083 10805-10834 WebSocket               com.dspread.mdm.service              I  🔧 C0901 应用信息响应（含服务信息）发送成功
2025-08-19 13:14:49.874 10805-11924 Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"msg_state":"P","request_time":"1755580489388","data":{"param":"","c_type":"CLOSE_REMOTE_CONTROL"},"expire_time":1756185289,"tranCode":"SC004","request_id":"1755580489388SC004","version":"1","serialNo":"01610040202307060227"}
2025-08-19 13:14:49.875 10805-11924 WebSocket               com.dspread.mdm.service              I  🔧 准备发送响应确认 - requestId: 1755580489388SC004, needResponse: true
2025-08-19 13:14:49.883 10805-11924 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0000","serialNo":"01610040202307060227","request_time":"1755580489878","request_id":"1755580489878C0000","version":"1","org_request_id":"1755580489388SC004","org_request_time":"1755580489388","response_state":"0","myVersionName":"1.1.01.20250819.DSPREAD.MDM.SERVICE","terminalDate":"20250819131449"}
2025-08-19 13:14:49.892 10805-11924 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0000","serialNo":"01610040202307060227","request_time":"1755580489887","request_id":"1755580489887C0000","version":"1","org_request_id":"1755580489388SC004","org_request_time":"1755580489388","response_state":"0","myVersionName":"1.1.01.20250819.DSPREAD.MDM.SERVICE","terminalDate":"20250819131449"}
2025-08-19 13:14:49.893 10805-11924 WebSocket               com.dspread.mdm.service              I  🔧 已发送C0000响应确认: requestId=1755580489388SC004
2025-08-19 13:14:49.895 10805-11924 WebSocket               com.dspread.mdm.service              I  🔧 处理命令: SC004
2025-08-19 13:14:49.896 10805-11924 WebSocket               com.dspread.mdm.service              I  🔧 收到SC004命令，类型: CLOSE_REMOTE_CONTROL
2025-08-19 13:14:49.897 10805-11924 WebSocket               com.dspread.mdm.service              I  🔧 收到SC004命令，类型: CLOSE_REMOTE_CONTROL
2025-08-19 13:14:49.898 10805-11924 WebSocket               com.dspread.mdm.service              I  🔧 服务器请求关闭远程控制
2025-08-19 13:14:49.900 10805-10805 WebSocket               com.dspread.mdm.service              W  ⚠️ Remote View服务未运行，无需停止
2025-08-19 13:14:49.914 10805-11924 Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"msg_state":"P","request_time":"1755580489410","data":{"param":"","c_type":"CALLC0109"},"expire_time":1755584089,"tranCode":"SC004","request_id":"1755580489410SC004","version":"1","serialNo":"01610040202307060227"}
2025-08-19 13:14:49.917 10805-11924 WebSocket               com.dspread.mdm.service              I  🔧 准备发送响应确认 - requestId: 1755580489410SC004, needResponse: true
2025-08-19 13:14:49.926 10805-11924 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0000","serialNo":"01610040202307060227","request_time":"1755580489920","request_id":"1755580489920C0000","version":"1","org_request_id":"1755580489410SC004","org_request_time":"1755580489410","response_state":"0","myVersionName":"1.1.01.20250819.DSPREAD.MDM.SERVICE","terminalDate":"20250819131449"}
2025-08-19 13:14:49.934 10805-11924 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0000","serialNo":"01610040202307060227","request_time":"1755580489930","request_id":"1755580489930C0000","version":"1","org_request_id":"1755580489410SC004","org_request_time":"1755580489410","response_state":"0","myVersionName":"1.1.01.20250819.DSPREAD.MDM.SERVICE","terminalDate":"20250819131449"}
2025-08-19 13:14:49.936 10805-11924 WebSocket               com.dspread.mdm.service              I  🔧 已发送C0000响应确认: requestId=1755580489410SC004
2025-08-19 13:14:49.937 10805-11924 WebSocket               com.dspread.mdm.service              I  🔧 处理命令: SC004
2025-08-19 13:14:49.938 10805-11924 WebSocket               com.dspread.mdm.service              I  🔧 收到SC004命令，类型: CALLC0109
2025-08-19 13:14:49.939 10805-11924 WebSocket               com.dspread.mdm.service              I  🔧 服务器请求终端信息上传
2025-08-19 13:14:49.940 10805-11924 WebSocket               com.dspread.mdm.service              I  🔧 C0109 主动式上送: terminal_info_timer (主动: 7)
2025-08-19 13:14:49.944 10805-11924 Platform                com.dspread.mdm.service              D  🔧 重新收集应用信息（强制刷新）
2025-08-19 13:14:49.988 10805-11924 AppManager              com.dspread.mdm.service              I  ℹ️ 应用信息获取完成: 总数122(系统119/用户3) 返回3个
2025-08-19 13:14:49.991 10805-11924 Platform                com.dspread.mdm.service              D  🔧 应用信息: 3 个应用
2025-08-19 13:14:50.046 10805-11924 Platform                com.dspread.mdm.service              D  🔧 内存总容量为： 1.86GB 
2025-08-19 13:14:50.047 10805-11924 Platform                com.dspread.mdm.service              D  🔧 内存使用量为： 0.56GB 
2025-08-19 13:14:50.069 10805-11924 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0109","serialNo":"01610040202307060227","request_time":"1755580490057","request_id":"1755580490057C0109","version":"1","data":{"apkInfo":[{"packName":"de.blinkt.openvpn","apkName":"OpenVPN for Android","versionCode":65,"versionName":"0.5.36a","updateDate":"2025-08-18 16:11:59"},{"packName":"com.dspread.mdm.service","apkName":"service","versionCode":20,"versionName":"1.1.01.20250819.DSPREAD.MDM.SERVICE","updateDate":"2025-08-19 12:10:19"},{"packName":"mark.via","apkName":"Via","versionCode":20250117,"versionName":"6.2.0","updateDate":"2025-08-18 17:50:24"}],"locationInfo":{"gps":{"longitude":"-999.0","latitude":"-999.0","satelliteCount":0,"satelliteCountInUsed":0,"satelliteAverSNR":"0.00","distance":"0.0","lockStatus":"1","lockMeter":"200","proId":"","proName":""},"baseSite":{"MCC":"","MNC":"","CID":"-1","LAC":"-1","DBM":"-999","IMSI":"","ICCID":""},"wifi":{"SSID":"fubox_2.4G","IP":"**************","MAC":"52:C3:63:13:8C:EE","SSTH":"-27","BSSID":"7c:10:c9:d6:9f:10"},"address":"无法获取"},"hardwareInfo":[{"name":"CPU Usage","desc":"cpu:0.50"},{"name":"storage_usage_status","desc":" 5.39GB \/ 32.00GB ","state":"1"},{"name":"total_memory","desc":" 0.56GB \/ 1.86GB ","state":"1"},{"name":"androidVersion","desc":"11","state":"1"},{"name":"network_status","desc":"wifi","state":"1"},{"name":"OSVer","desc":"11","state":"1"}],"wifiOption":[{"SSID":"fubox_2.4G","SSTH":"-26"},{"SSID":"2206-5G","SSTH":"-31"},{"SSID":"2206","SSTH":"-39"},{"SSID":"@Ruijie-1816","SSTH":"-52"},{"SSID":"2205_5G","SSTH":"-54"},{"SSID":"2205","SSTH":"-56"},{"SSID":"DIRECT-C6-Mi All-in-One Inkjet","SSTH":"-57"},{"SSID":"2207","SSTH":"-66"},{"SSID":"DIRECT-CC-HP Laser 1188w","SSTH":"-69"},{"SSID":"2207-5G","SSTH":"-82"},{"SSID":"vivo Y51s","SSTH":"-83"}],"sytemInfo":{"androidVersion":"11","buildNumber":"D60_Baseline_debug_20250708_ota_test","aspl":"2021-12-05"}},"myVersionName":"1.1.01.20250819.DSPREAD.MDM.SERVICE","terminalDate":"20250819131450"}
2025-08-19 13:14:50.070 10805-11924 WebSocket               com.dspread.mdm.service              I  🔧 C0109 终端信息上传完成 (1, trigger=terminal_info_timer)
2025-08-19 13:14:50.070 10805-11924 WebSocket               com.dspread.mdm.service              I  🔧 同时上传C0901应用信息（含服务信息）
2025-08-19 13:14:50.071 10805-11924 WebSocket               com.dspread.mdm.service              I  🔧 检查默认开启的服务
2025-08-19 13:14:50.072 10805-11924 WebSocket               com.dspread.mdm.service              I  🔧 本地无LogStream服务信息
2025-08-19 13:14:50.073 10805-11924 WebSocket               com.dspread.mdm.service              I  🔧 LogStream服务未启用，跳过保存
2025-08-19 13:14:50.073 10805-11924 WebSocket               com.dspread.mdm.service              I  🔧 默认服务检查完成
2025-08-19 13:14:50.075 10805-11924 WebSocket               com.dspread.mdm.service              I  🔧 C0901 主动式上送: command_trigger (主动: 8)
2025-08-19 13:14:50.080 10805-11924 Platform                com.dspread.mdm.service              D  🔧 使用缓存的应用信息
2025-08-19 13:14:50.090 10805-11924 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0901","serialNo":"01610040202307060227","request_time":"1755580490080","request_id":"1755580490080C0901","version":"1","data":{"apkInfo":[{"packName":"de.blinkt.openvpn","apkName":"OpenVPN for Android","versionCode":65,"versionName":"0.5.36a","updateDate":"2025-08-18 16:11:59"},{"packName":"com.dspread.mdm.service","apkName":"service","versionCode":20,"versionName":"1.1.01.20250819.DSPREAD.MDM.SERVICE","updateDate":"2025-08-19 12:10:19"},{"packName":"mark.via","apkName":"Via","versionCode":20250117,"versionName":"6.2.0","updateDate":"2025-08-18 17:50:24"}],"sytemInfo":{"androidVersion":"11","buildNumber":"D60_Baseline_debug_20250708_ota_test","aspl":"2021-12-05"},"serviceInfo":[{"taskId":"1755580474546","serviceId":"856c2e6d2c7c4f60bc0fadcf9b9ad650","serviceName":"Log Stream","taskType":"05","command":"C03","stateDesc":"IMPLEMENTED","beginDate":"2025-08-19 13:14:37","endDate":"9999-12-31 23:59:59","period":"1","request_id":"","request_time":""}]},"myVersionName":"1.1.01.20250819.DSPREAD.MDM.SERVICE","terminalDate":"20250819131450"}
2025-08-19 13:14:50.092 10805-11924 WebSocket               com.dspread.mdm.service              I  🔧 C0901 应用信息上送成功 (1)
2025-08-19 13:14:50.480 10805-11924 Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"request_time":"1755580490291","org_request_time":"1755580490057","org_request_id":"1755580490057C0109","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755580490291S0000","serialNo":"01610040202307060227"}
2025-08-19 13:14:50.482 10805-11924 WebSocket               com.dspread.mdm.service              I  🔧 收到响应消息: transCode=S0000, orgRequestId=1755580490057C0109, state=0, remark=
2025-08-19 13:14:50.483 10805-11924 WebSocket               com.dspread.mdm.service              I  🔧 终端信息上传成功
2025-08-19 13:14:50.485 10805-11924 WebSocket               com.dspread.mdm.service              I  🔧 终端信息上传成功标志已设置
2025-08-19 13:14:50.797 10805-11924 Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"msg_state":"P","request_time":"1755580490425","data":{"param":"","c_type":"CALL_REMOTE_CONTROL"},"expire_time":1756185290,"tranCode":"SC004","request_id":"1755580490425SC004","version":"1","serialNo":"01610040202307060227"}
2025-08-19 13:14:50.799 10805-11924 WebSocket               com.dspread.mdm.service              I  🔧 准备发送响应确认 - requestId: 1755580490425SC004, needResponse: true
2025-08-19 13:14:50.807 10805-11924 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0000","serialNo":"01610040202307060227","request_time":"1755580490803","request_id":"1755580490803C0000","version":"1","org_request_id":"1755580490425SC004","org_request_time":"1755580490425","response_state":"0","myVersionName":"1.1.01.20250819.DSPREAD.MDM.SERVICE","terminalDate":"20250819131450"}
2025-08-19 13:14:50.815 10805-11924 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0000","serialNo":"01610040202307060227","request_time":"1755580490812","request_id":"1755580490812C0000","version":"1","org_request_id":"1755580490425SC004","org_request_time":"1755580490425","response_state":"0","myVersionName":"1.1.01.20250819.DSPREAD.MDM.SERVICE","terminalDate":"20250819131450"}
2025-08-19 13:14:50.817 10805-11924 WebSocket               com.dspread.mdm.service              I  🔧 已发送C0000响应确认: requestId=1755580490425SC004
2025-08-19 13:14:50.818 10805-11924 WebSocket               com.dspread.mdm.service              I  🔧 处理命令: SC004
2025-08-19 13:14:50.820 10805-11924 WebSocket               com.dspread.mdm.service              I  🔧 收到SC004命令，类型: CALL_REMOTE_CONTROL
2025-08-19 13:14:50.821 10805-11924 WebSocket               com.dspread.mdm.service              I  🔧 服务器请求启动远程控制
2025-08-19 13:14:50.822 10805-11924 WebSocket               com.dspread.mdm.service              I  🔧 启动Remote View服务，bucket路径: 
2025-08-19 13:14:50.825 10805-10834 WebSocket               com.dspread.mdm.service              I  🔧 直接启动Remote View服务
2025-08-19 13:14:50.871 10805-10834 Common                  com.dspread.mdm.service              D  🔧 Module RemoteView status changed to: INITIALIZED
2025-08-19 13:14:50.882 10805-10834 Common                  com.dspread.mdm.service              D  🔧 Module RemoteView status changed to: STARTING
2025-08-19 13:14:50.935 10805-10834 ead.mdm.servic          com.dspread.mdm.service              I  Waiting for a blocking GC ClassLinker
2025-08-19 13:14:50.947 10805-10834 ead.mdm.servic          com.dspread.mdm.service              I  WaitForGcToComplete blocked ClassLinker on ProfileSaver for 12.056ms
2025-08-19 13:14:51.225 10805-11998 System.out              com.dspread.mdm.service              I  [socket]:check permission begin!
2025-08-19 13:14:53.106 10805-10866 Common                  com.dspread.mdm.service              D  🔧 Module RemoteView status changed to: RUNNING
2025-08-19 13:14:53.119 10805-10866 ContextImpl             com.dspread.mdm.service              W  Calling a method in the system process without a qualified user: android.app.ContextImpl.startActivity:993 android.content.ContextWrapper.startActivity:403 com.dspread.mdm.service.modules.remoteview.RequestMediaProjectionActivity$Companion.requestMediaProjection:43 com.dspread.mdm.service.modules.remoteview.MediaProjectionScreenCapture.tryRequestMediaProjectionPermission:142 com.dspread.mdm.service.modules.remoteview.MediaProjectionScreenCapture.createMediaProjection:93 
2025-08-19 13:14:53.121 10805-10866 ContextImpl             com.dspread.mdm.service              W  Calling a method in the system process without a qualified user: android.app.ContextImpl.startActivity:1005 android.app.ContextImpl.startActivity:994 android.content.ContextWrapper.startActivity:403 com.dspread.mdm.service.modules.remoteview.RequestMediaProjectionActivity$Companion.requestMediaProjection:43 com.dspread.mdm.service.modules.remoteview.MediaProjectionScreenCapture.tryRequestMediaProjectionPermission:142 
2025-08-19 13:14:53.206 10805-10805 PhoneWindow             com.dspread.mdm.service              V  DecorView setVisiblity: visibility = 4, Parent = null, this = DecorView@642d3b0[]
2025-08-19 13:14:53.212 10805-10805 ViewRootIm...nActivity] com.dspread.mdm.service              D  hardware acceleration = true , fakeHwAccelerated = false, sRendererDisabled = false, forceHwAccelerated = false, sSystemRendererDisabled = false
2025-08-19 13:14:53.218 10805-10805 InputTransport          com.dspread.mdm.service              I  Create ARC handle: 0x90b81140
2025-08-19 13:14:53.219 10805-10805 PhoneWindow             com.dspread.mdm.service              V  DecorView setVisiblity: visibility = 0, Parent = android.view.ViewRootImpl@4a9134f, this = DecorView@642d3b0[RequestMediaProjectionActivity]
2025-08-19 13:14:53.288 10805-10805 ContextImpl             com.dspread.mdm.service              W  Calling a method in the system process without a qualified user: android.app.ContextImpl.startForegroundService:1675 android.content.ContextWrapper.startForegroundService:725 com.dspread.mdm.service.services.MediaProjectionService$Companion.startService:58 com.dspread.mdm.service.modules.remoteview.MediaProjectionScreenCapture$tryRequestMediaProjectionPermission$1.invoke:148 com.dspread.mdm.service.modules.remoteview.MediaProjectionScreenCapture$tryRequestMediaProjectionPermission$1.invoke:142 
2025-08-19 13:14:53.327 10805-10805 setContentIntent        com.dspread.mdm.service              D  packageName = com.dspread.mdm.service
2025-08-19 13:14:53.335   977-1006  NotificationService     system_server                        E  enqueueNotificationInternal pkg: com.dspread.mdm.service
2025-08-19 13:14:53.396 10805-10805 PhoneWindow             com.dspread.mdm.service              V  DecorView setVisiblity: visibility = 4, Parent = android.view.ViewRootImpl@4a9134f, this = DecorView@642d3b0[RequestMediaProjectionActivity]
2025-08-19 13:14:53.402 10805-10805 View                    com.dspread.mdm.service              D  [Warning] assignParent to null: this = DecorView@642d3b0[RequestMediaProjectionActivity]
2025-08-19 13:14:53.407 10805-10805 InputTransport          com.dspread.mdm.service              I  Destroy ARC handle: 0x90b81140
2025-08-19 13:14:53.435 10805-10866 BufferQueueConsumer     com.dspread.mdm.service              I  [](id:2a3500000000,api:0,p:-1,c:10805) connect(): controlledByApp=true
2025-08-19 13:14:53.526 10805-11931 BufferQueueProducer     com.dspread.mdm.service              I  [ImageReader-720x1600f1m2-10805-0](id:2a3500000000,api:1,p:407,c:10805) connect(): api=1 producerControlledByApp=false
2025-08-19 13:14:53.725 10805-10866 ion                     com.dspread.mdm.service              E  ioctl c0044901 failed with code -1: Invalid argument
2025-08-19 13:14:53.725 10805-10866 skia                    com.dspread.mdm.service              D  SkJpegCodec::onGetPixels +
2025-08-19 13:14:53.739 10805-10866 skia                    com.dspread.mdm.service              D  SkJpegCodec::onGetPixels -
2025-08-19 13:14:53.883 10805-10866 skia                    com.dspread.mdm.service              D  SkJpegCodec::onGetPixels +
2025-08-19 13:14:53.897 10805-10866 skia                    com.dspread.mdm.service              D  SkJpegCodec::onGetPixels -
2025-08-19 13:14:54.038 10805-10866 skia                    com.dspread.mdm.service              D  SkJpegCodec::onGetPixels +
2025-08-19 13:14:54.052 10805-10866 skia                    com.dspread.mdm.service              D  SkJpegCodec::onGetPixels -
2025-08-19 13:14:54.193 10805-10866 skia                    com.dspread.mdm.service              D  SkJpegCodec::onGetPixels +
2025-08-19 13:14:54.207 10805-10866 skia                    com.dspread.mdm.service              D  SkJpegCodec::onGetPixels -
2025-08-19 13:14:54.350 10805-10866 skia                    com.dspread.mdm.service              D  SkJpegCodec::onGetPixels +
2025-08-19 13:14:54.364 10805-10866 skia                    com.dspread.mdm.service              D  SkJpegCodec::onGetPixels -
2025-08-19 13:14:54.505 10805-10866 skia                    com.dspread.mdm.service              D  SkJpegCodec::onGetPixels +
2025-08-19 13:14:54.519 10805-10866 skia                    com.dspread.mdm.service              D  SkJpegCodec::onGetPixels -
2025-08-19 13:14:54.661 10805-10866 skia                    com.dspread.mdm.service              D  SkJpegCodec::onGetPixels +
2025-08-19 13:14:54.676 10805-10866 skia                    com.dspread.mdm.service              D  SkJpegCodec::onGetPixels -
2025-08-19 13:14:54.819 10805-10866 skia                    com.dspread.mdm.service              D  SkJpegCodec::onGetPixels +
2025-08-19 13:14:54.833 10805-10866 skia                    com.dspread.mdm.service              D  SkJpegCodec::onGetPixels -
2025-08-19 13:14:54.976 10805-10866 skia                    com.dspread.mdm.service              D  SkJpegCodec::onGetPixels +
2025-08-19 13:14:54.990 10805-10866 skia                    com.dspread.mdm.service              D  SkJpegCodec::onGetPixels -
2025-08-19 13:14:55.135 10805-10866 skia                    com.dspread.mdm.service              D  SkJpegCodec::onGetPixels +
2025-08-19 13:14:55.149 10805-10866 skia                    com.dspread.mdm.service              D  SkJpegCodec::onGetPixels -
2025-08-19 13:14:55.291 10805-10866 skia                    com.dspread.mdm.service              D  SkJpegCodec::onGetPixels +
2025-08-19 13:14:55.305 10805-10866 skia                    com.dspread.mdm.service              D  SkJpegCodec::onGetPixels -
2025-08-19 13:14:55.448 10805-10866 skia                    com.dspread.mdm.service              D  SkJpegCodec::onGetPixels +
2025-08-19 13:14:55.462 10805-10866 skia                    com.dspread.mdm.service              D  SkJpegCodec::onGetPixels -
2025-08-19 13:14:55.623 10805-10866 skia                    com.dspread.mdm.service              D  SkJpegCodec::onGetPixels +
2025-08-19 13:14:55.638 10805-10866 skia                    com.dspread.mdm.service              D  SkJpegCodec::onGetPixels -
2025-08-19 13:14:55.788 10805-10866 skia                    com.dspread.mdm.service              D  SkJpegCodec::onGetPixels +
2025-08-19 13:14:55.792 10805-11924 Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"msg_state":"P","request_time":"1755580495555","data":{"param":"","c_type":"CLOSE_REMOTE_CONTROL"},"expire_time":1756185295,"tranCode":"SC004","request_id":"1755580495555SC004","version":"1","serialNo":"01610040202307060227"}
2025-08-19 13:14:55.793 10805-11924 WebSocket               com.dspread.mdm.service              I  🔧 准备发送响应确认 - requestId: 1755580495555SC004, needResponse: true
2025-08-19 13:14:55.801 10805-11924 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0000","serialNo":"01610040202307060227","request_time":"1755580495796","request_id":"1755580495796C0000","version":"1","org_request_id":"1755580495555SC004","org_request_time":"1755580495555","response_state":"0","myVersionName":"1.1.01.20250819.DSPREAD.MDM.SERVICE","terminalDate":"20250819131455"}
2025-08-19 13:14:55.803 10805-10866 skia                    com.dspread.mdm.service              D  SkJpegCodec::onGetPixels -
2025-08-19 13:14:55.806 10805-11924 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0000","serialNo":"01610040202307060227","request_time":"1755580495803","request_id":"1755580495803C0000","version":"1","org_request_id":"1755580495555SC004","org_request_time":"1755580495555","response_state":"0","myVersionName":"1.1.01.20250819.DSPREAD.MDM.SERVICE","terminalDate":"20250819131455"}
2025-08-19 13:14:55.807 10805-11924 WebSocket               com.dspread.mdm.service              I  🔧 已发送C0000响应确认: requestId=1755580495555SC004
2025-08-19 13:14:55.808 10805-11924 WebSocket               com.dspread.mdm.service              I  🔧 处理命令: SC004
2025-08-19 13:14:55.809 10805-11924 WebSocket               com.dspread.mdm.service              I  🔧 收到SC004命令，类型: CLOSE_REMOTE_CONTROL
2025-08-19 13:14:55.810 10805-11924 WebSocket               com.dspread.mdm.service              I  🔧 收到SC004命令，类型: CLOSE_REMOTE_CONTROL
2025-08-19 13:14:55.810 10805-11924 WebSocket               com.dspread.mdm.service              I  🔧 服务器请求关闭远程控制
2025-08-19 13:14:55.811 10805-10805 WebSocket               com.dspread.mdm.service              I  🔧 找到运行中的Remote View管理器，立即停止
2025-08-19 13:14:55.812 10805-10805 Common                  com.dspread.mdm.service              D  🔧 Module RemoteView status changed to: STOPPING
2025-08-19 13:14:55.873 10805-10805 BufferQueueConsumer     com.dspread.mdm.service              I  [ImageReader-720x1600f1m2-10805-0](id:2a3500000000,api:1,p:407,c:10805) disconnect()
2025-08-19 13:14:55.883 10805-10864 BufferQueueProducer     com.dspread.mdm.service              I  [ImageReader-720x1600f1m2-10805-0](id:2a3500000000,api:1,p:407,c:10805) disconnect(): api=1
2025-08-19 13:14:55.967 10805-10805 ContextImpl             com.dspread.mdm.service              W  Calling a method in the system process without a qualified user: android.app.ContextImpl.stopService:1681 android.content.ContextWrapper.stopService:730 com.dspread.mdm.service.services.MediaProjectionService$Companion.stopService:75 com.dspread.mdm.service.modules.remoteview.RemoteViewManager.stopMediaProjectionService:563 com.dspread.mdm.service.modules.remoteview.RemoteViewManager.onStop-IoAF18A:180 
2025-08-19 13:14:56.187 10805-10805 Common                  com.dspread.mdm.service              D  🔧 Module RemoteView status changed to: STOPPED
2025-08-19 13:14:56.190 10805-10805 ContextImpl             com.dspread.mdm.service              W  Calling a method in the system process without a qualified user: android.app.ContextImpl.stopService:1681 android.content.ContextWrapper.stopService:730 com.dspread.mdm.service.services.MediaProjectionService$Companion.stopService:75 com.dspread.mdm.service.modules.remoteview.RemoteViewManager.stopMediaProjectionService:563 com.dspread.mdm.service.modules.remoteview.RemoteViewManager.release:583 
2025-08-19 13:14:56.195 10805-10805 WebSocket               com.dspread.mdm.service              I  🔧 Remote View服务停止成功
2025-08-19 13:14:57.959 10805-11924 Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"msg_state":"P","request_time":"1755580497653","data":{"param":"","c_type":"CLOSE_REMOTE_CONTROL"},"expire_time":1756185297,"tranCode":"SC004","request_id":"1755580497653SC004","version":"1","serialNo":"01610040202307060227"}
2025-08-19 13:14:57.961 10805-11924 WebSocket               com.dspread.mdm.service              I  🔧 准备发送响应确认 - requestId: 1755580497653SC004, needResponse: true
2025-08-19 13:14:57.968 10805-11924 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0000","serialNo":"01610040202307060227","request_time":"1755580497964","request_id":"1755580497964C0000","version":"1","org_request_id":"1755580497653SC004","org_request_time":"1755580497653","response_state":"0","myVersionName":"1.1.01.20250819.DSPREAD.MDM.SERVICE","terminalDate":"20250819131457"}
2025-08-19 13:14:57.978 10805-11924 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0000","serialNo":"01610040202307060227","request_time":"1755580497973","request_id":"1755580497973C0000","version":"1","org_request_id":"1755580497653SC004","org_request_time":"1755580497653","response_state":"0","myVersionName":"1.1.01.20250819.DSPREAD.MDM.SERVICE","terminalDate":"20250819131457"}
2025-08-19 13:14:57.979 10805-11924 WebSocket               com.dspread.mdm.service              I  🔧 已发送C0000响应确认: requestId=1755580497653SC004
2025-08-19 13:14:57.980 10805-11924 WebSocket               com.dspread.mdm.service              I  🔧 处理命令: SC004
2025-08-19 13:14:57.981 10805-11924 WebSocket               com.dspread.mdm.service              I  🔧 收到SC004命令，类型: CLOSE_REMOTE_CONTROL
2025-08-19 13:14:57.982 10805-11924 WebSocket               com.dspread.mdm.service              I  🔧 收到SC004命令，类型: CLOSE_REMOTE_CONTROL
2025-08-19 13:14:57.983 10805-11924 WebSocket               com.dspread.mdm.service              I  🔧 服务器请求关闭远程控制
2025-08-19 13:14:57.985 10805-10805 WebSocket               com.dspread.mdm.service              W  ⚠️ Remote View服务未运行，无需停止
2025-08-19 13:15:17.933 10805-11924 Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"msg_state":"P","request_time":"1755580517739","data":{"param":"","c_type":"CALLC0109"},"expire_time":1755584117,"tranCode":"SC004","request_id":"1755580517739SC004","version":"1","serialNo":"01610040202307060227"}
2025-08-19 13:15:17.935 10805-11924 WebSocket               com.dspread.mdm.service              I  🔧 准备发送响应确认 - requestId: 1755580517739SC004, needResponse: true
2025-08-19 13:15:17.945 10805-11924 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0000","serialNo":"01610040202307060227","request_time":"1755580517939","request_id":"1755580517939C0000","version":"1","org_request_id":"1755580517739SC004","org_request_time":"1755580517739","response_state":"0","myVersionName":"1.1.01.20250819.DSPREAD.MDM.SERVICE","terminalDate":"20250819131517"}
2025-08-19 13:15:17.956 10805-11924 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0000","serialNo":"01610040202307060227","request_time":"1755580517949","request_id":"1755580517949C0000","version":"1","org_request_id":"1755580517739SC004","org_request_time":"1755580517739","response_state":"0","myVersionName":"1.1.01.20250819.DSPREAD.MDM.SERVICE","terminalDate":"20250819131517"}
2025-08-19 13:15:17.957 10805-11924 WebSocket               com.dspread.mdm.service              I  🔧 已发送C0000响应确认: requestId=1755580517739SC004
2025-08-19 13:15:17.959 10805-11924 WebSocket               com.dspread.mdm.service              I  🔧 处理命令: SC004
2025-08-19 13:15:17.960 10805-11924 WebSocket               com.dspread.mdm.service              I  🔧 收到SC004命令，类型: CALLC0109
2025-08-19 13:15:17.962 10805-11924 WebSocket               com.dspread.mdm.service              I  🔧 服务器请求终端信息上传
2025-08-19 13:15:17.963 10805-11924 WebSocket               com.dspread.mdm.service              I  🔧 C0109 主动式上送: terminal_info_timer (主动: 9)
2025-08-19 13:15:17.968 10805-11924 Platform                com.dspread.mdm.service              D  🔧 重新收集应用信息（强制刷新）
2025-08-19 13:15:18.027 10805-11924 AppManager              com.dspread.mdm.service              I  ℹ️ 应用信息获取完成: 总数122(系统119/用户3) 返回3个
2025-08-19 13:15:18.031 10805-11924 Platform                com.dspread.mdm.service              D  🔧 应用信息: 3 个应用
2025-08-19 13:15:18.099 10805-11924 Platform                com.dspread.mdm.service              D  🔧 内存总容量为： 1.86GB 
2025-08-19 13:15:18.100 10805-11924 Platform                com.dspread.mdm.service              D  🔧 内存使用量为： 0.57GB 
2025-08-19 13:15:18.116 10805-11924 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0109","serialNo":"01610040202307060227","request_time":"1755580518109","request_id":"1755580518109C0109","version":"1","data":{"apkInfo":[{"packName":"de.blinkt.openvpn","apkName":"OpenVPN for Android","versionCode":65,"versionName":"0.5.36a","updateDate":"2025-08-18 16:11:59"},{"packName":"com.dspread.mdm.service","apkName":"service","versionCode":20,"versionName":"1.1.01.20250819.DSPREAD.MDM.SERVICE","updateDate":"2025-08-19 12:10:19"},{"packName":"mark.via","apkName":"Via","versionCode":20250117,"versionName":"6.2.0","updateDate":"2025-08-18 17:50:24"}],"locationInfo":{"gps":{"longitude":"-999.0","latitude":"-999.0","satelliteCount":0,"satelliteCountInUsed":0,"satelliteAverSNR":"0.00","distance":"0.0","lockStatus":"1","lockMeter":"200","proId":"","proName":""},"baseSite":{"MCC":"","MNC":"","CID":"-1","LAC":"-1","DBM":"-999","IMSI":"","ICCID":""},"wifi":{"SSID":"fubox_2.4G","IP":"**************","MAC":"52:C3:63:13:8C:EE","SSTH":"-28","BSSID":"7c:10:c9:d6:9f:10"},"address":"无法获取"},"hardwareInfo":[{"name":"CPU Usage","desc":"cpu:0.50"},{"name":"storage_usage_status","desc":" 5.39GB \/ 32.00GB ","state":"1"},{"name":"total_memory","desc":" 0.57GB \/ 1.86GB ","state":"1"},{"name":"androidVersion","desc":"11","state":"1"},{"name":"network_status","desc":"wifi","state":"1"},{"name":"OSVer","desc":"11","state":"1"}],"wifiOption":[{"SSID":"fubox_2.4G","SSTH":"-26"},{"SSID":"2206-5G","SSTH":"-31"},{"SSID":"2206","SSTH":"-39"},{"SSID":"@Ruijie-1816","SSTH":"-52"},{"SSID":"2205_5G","SSTH":"-54"},{"SSID":"2205","SSTH":"-56"},{"SSID":"DIRECT-C6-Mi All-in-One Inkjet","SSTH":"-57"},{"SSID":"2207","SSTH":"-66"},{"SSID":"DIRECT-CC-HP Laser 1188w","SSTH":"-69"},{"SSID":"2207-5G","SSTH":"-82"},{"SSID":"vivo Y51s","SSTH":"-83"}],"sytemInfo":{"androidVersion":"11","buildNumber":"D60_Baseline_debug_20250708_ota_test","aspl":"2021-12-05"}},"myVersionName":"1.1.01.20250819.DSPREAD.MDM.SERVICE","terminalDate":"20250819131518"}
2025-08-19 13:15:18.117 10805-11924 WebSocket               com.dspread.mdm.service              I  🔧 C0109 终端信息上传完成 (1, trigger=terminal_info_timer)
2025-08-19 13:15:18.118 10805-11924 WebSocket               com.dspread.mdm.service              I  🔧 同时上传C0901应用信息（含服务信息）
2025-08-19 13:15:18.119 10805-11924 WebSocket               com.dspread.mdm.service              I  🔧 检查默认开启的服务
2025-08-19 13:15:18.119 10805-11924 WebSocket               com.dspread.mdm.service              I  🔧 本地无LogStream服务信息
2025-08-19 13:15:18.120 10805-11924 WebSocket               com.dspread.mdm.service              I  🔧 LogStream服务未启用，跳过保存
2025-08-19 13:15:18.120 10805-11924 WebSocket               com.dspread.mdm.service              I  🔧 默认服务检查完成
2025-08-19 13:15:18.121 10805-11924 WebSocket               com.dspread.mdm.service              I  🔧 C0901 主动式上送: command_trigger (主动: 10)
2025-08-19 13:15:18.124 10805-11924 Platform                com.dspread.mdm.service              D  🔧 使用缓存的应用信息
2025-08-19 13:15:18.132 10805-11924 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0901","serialNo":"01610040202307060227","request_time":"1755580518124","request_id":"1755580518124C0901","version":"1","data":{"apkInfo":[{"packName":"de.blinkt.openvpn","apkName":"OpenVPN for Android","versionCode":65,"versionName":"0.5.36a","updateDate":"2025-08-18 16:11:59"},{"packName":"com.dspread.mdm.service","apkName":"service","versionCode":20,"versionName":"1.1.01.20250819.DSPREAD.MDM.SERVICE","updateDate":"2025-08-19 12:10:19"},{"packName":"mark.via","apkName":"Via","versionCode":20250117,"versionName":"6.2.0","updateDate":"2025-08-18 17:50:24"}],"sytemInfo":{"androidVersion":"11","buildNumber":"D60_Baseline_debug_20250708_ota_test","aspl":"2021-12-05"},"serviceInfo":[{"taskId":"1755580474546","serviceId":"856c2e6d2c7c4f60bc0fadcf9b9ad650","serviceName":"Log Stream","taskType":"05","command":"C03","stateDesc":"IMPLEMENTED","beginDate":"2025-08-19 13:14:37","endDate":"9999-12-31 23:59:59","period":"1","request_id":"","request_time":""}]},"myVersionName":"1.1.01.20250819.DSPREAD.MDM.SERVICE","terminalDate":"20250819131518"}
2025-08-19 13:15:18.133 10805-11924 WebSocket               com.dspread.mdm.service              I  🔧 C0901 应用信息上送成功 (1)
2025-08-19 13:15:18.510 10805-11924 Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"request_time":"1755580518331","org_request_time":"1755580518109","org_request_id":"1755580518109C0109","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755580518331S0000","serialNo":"01610040202307060227"}
2025-08-19 13:15:18.511 10805-11924 WebSocket               com.dspread.mdm.service              I  🔧 收到响应消息: transCode=S0000, orgRequestId=1755580518109C0109, state=0, remark=
2025-08-19 13:15:18.512 10805-11924 WebSocket               com.dspread.mdm.service              I  🔧 终端信息上传成功
2025-08-19 13:15:18.513 10805-11924 WebSocket               com.dspread.mdm.service              I  🔧 终端信息上传成功标志已设置
2025-08-19 13:15:21.073 10805-11925 WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 4
2025-08-19 13:15:21.499 10805-11924 WebSocket               com.dspread.mdm.service              I  🔧 收到服务器 PONG 帧: 4
2025-08-19 13:15:21.573 10805-10805 Receiver                com.dspread.mdm.service              D  🔧 电池状态更新: 电池状态: 电量=75%, 温度=29.6°C, 健康=2, 充电=是, 低电量=否, 耗尽=否
2025-08-19 13:15:23.402 10805-10805 Receiver                com.dspread.mdm.service              D  🔧 屏幕关闭
2025-08-19 13:15:23.404 10805-10805 WebSocket               com.dspread.mdm.service              W  ⚠️ 未知触发条件: screen_off，默认按被动式处理
2025-08-19 13:15:23.408 10805-10805 WebSocket               com.dspread.mdm.service              I  🔧 C0201 被动式上送拒绝: screen_off (拒绝: 6)
2025-08-19 13:15:23.410 10805-10805 WebSocket               com.dspread.mdm.service              I  🔧 C0201 设备状态上送被流量控制阻止: 被动事件 'screen_off' 在平衡模式 - 重要变化下未启用
2025-08-19 13:15:23.411 10805-10805 Receiver                com.dspread.mdm.service              D  🔧 屏幕关闭处理完成
2025-08-19 13:15:24.901 10805-11924 Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"msg_state":"P","request_time":"1755580524415","data":{"taskList":[{"beginDate":"2024-08-19 05:15:24","taskType":"02","apkName":"OpenVPN for Android","endDate":"9999-12-31 23:59:59","appId":"1755580524415","packName":"de.blinkt.openvpn","versionName":"0.5.36a","taskId":"1755580524415","versionCode":"65"}]},"tranCode":"ST001","request_id":"1755580524415ST001","version":"1","serialNo":"01610040202307060227"}
2025-08-19 13:15:24.903 10805-11924 WebSocket               com.dspread.mdm.service              I  🔧 准备发送响应确认 - requestId: 1755580524415ST001, needResponse: true
2025-08-19 13:15:24.913 10805-11924 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0000","serialNo":"01610040202307060227","request_time":"1755580524907","request_id":"1755580524907C0000","version":"1","org_request_id":"1755580524415ST001","org_request_time":"1755580524415","response_state":"0","myVersionName":"1.1.01.20250819.DSPREAD.MDM.SERVICE","terminalDate":"20250819131524"}
2025-08-19 13:15:24.922 10805-11924 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0000","serialNo":"01610040202307060227","request_time":"1755580524917","request_id":"1755580524917C0000","version":"1","org_request_id":"1755580524415ST001","org_request_time":"1755580524415","response_state":"0","myVersionName":"1.1.01.20250819.DSPREAD.MDM.SERVICE","terminalDate":"20250819131524"}
2025-08-19 13:15:24.923 10805-11924 WebSocket               com.dspread.mdm.service              I  🔧 已发送C0000响应确认: requestId=1755580524415ST001
2025-08-19 13:15:24.925 10805-11924 Task                    com.dspread.mdm.service              D  🔧 处理任务消息: ST001
2025-08-19 13:15:24.927 10805-11924 Task                    com.dspread.mdm.service              I  ℹ️ 更新任务列表: requestId=1755580524415ST001, 任务数量=1
2025-08-19 13:15:24.928 10805-11924 Task                    com.dspread.mdm.service              D  🔧 任务插入到末尾: taskId=1755580524415
2025-08-19 13:15:24.930 10805-11924 Task                    com.dspread.mdm.service              D  🔧 添加/更新任务: taskId=1755580524415
2025-08-19 13:15:24.933 10805-11924 Task                    com.dspread.mdm.service              D  🔧 保存任务列表到存储: 1 个
2025-08-19 13:15:24.935 10805-11924 Task                    com.dspread.mdm.service              D  🔧 开始处理任务列表，共 1 个任务
2025-08-19 13:15:24.936 10805-11924 Task                    com.dspread.mdm.service              D  🔧 处理任务: taskId=1755580524415, type=02, package=de.blinkt.openvpn, apk=OpenVPN for Android
2025-08-19 13:15:24.937 10805-11924 Task                    com.dspread.mdm.service              D  🔧 处理卸载任务: 1755580524415
2025-08-19 13:15:24.939 10805-11924 AppManager              com.dspread.mdm.service              I  ℹ️ Uninstalling app via PackageInstaller: de.blinkt.openvpn
2025-08-19 13:15:24.940 10805-11924 AppManager              com.dspread.mdm.service              I  ℹ️ Deleting package via PackageInstaller: de.blinkt.openvpn
2025-08-19 13:15:24.946 10805-11924 AppManager              com.dspread.mdm.service              I  ℹ️ PackageInstaller uninstall initiated
2025-08-19 13:15:25.296 10805-11924 Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"msg_state":"P","request_time":"1755580524929","data":{"param":"","c_type":"CALLC0109"},"expire_time":1755584124,"tranCode":"SC004","request_id":"1755580524929SC004","version":"1","serialNo":"01610040202307060227"}
2025-08-19 13:15:25.297 10805-11924 WebSocket               com.dspread.mdm.service              I  🔧 准备发送响应确认 - requestId: 1755580524929SC004, needResponse: true
2025-08-19 13:15:25.298 10805-11931 BpBinder                com.dspread.mdm.service              I  onLastStrongRef automatically unlinking death recipients: <uncached descriptor>
2025-08-19 13:15:25.305 10805-11924 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0000","serialNo":"01610040202307060227","request_time":"1755580525300","request_id":"1755580525300C0000","version":"1","org_request_id":"1755580524929SC004","org_request_time":"1755580524929","response_state":"0","myVersionName":"1.1.01.20250819.DSPREAD.MDM.SERVICE","terminalDate":"20250819131525"}
2025-08-19 13:15:25.323 10805-11924 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0000","serialNo":"01610040202307060227","request_time":"1755580525319","request_id":"1755580525319C0000","version":"1","org_request_id":"1755580524929SC004","org_request_time":"1755580524929","response_state":"0","myVersionName":"1.1.01.20250819.DSPREAD.MDM.SERVICE","terminalDate":"20250819131525"}
2025-08-19 13:15:25.324 10805-11924 WebSocket               com.dspread.mdm.service              I  🔧 已发送C0000响应确认: requestId=1755580524929SC004
2025-08-19 13:15:25.325 10805-11924 WebSocket               com.dspread.mdm.service              I  🔧 处理命令: SC004
2025-08-19 13:15:25.326 10805-11924 WebSocket               com.dspread.mdm.service              I  🔧 收到SC004命令，类型: CALLC0109
2025-08-19 13:15:25.327 10805-11924 WebSocket               com.dspread.mdm.service              I  🔧 服务器请求终端信息上传
2025-08-19 13:15:25.331 10805-11924 WebSocket               com.dspread.mdm.service              I  🔧 C0109 主动式上送: terminal_info_timer (主动: 11)
2025-08-19 13:15:25.334 10805-11924 Platform                com.dspread.mdm.service              D  🔧 重新收集应用信息（强制刷新）
2025-08-19 13:15:25.391 10805-10805 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 收到广播: android.intent.action.PACKAGE_REMOVED
2025-08-19 13:15:25.392 10805-10805 Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 收到包更新广播: android.intent.action.PACKAGE_REMOVED, 包名: de.blinkt.openvpn
2025-08-19 13:15:25.392 10805-10805 Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 应用卸载: de.blinkt.openvpn
2025-08-19 13:15:25.393 10805-10805 Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 检查WebSocket状态: packageName=de.blinkt.openvpn, action=UNINSTALL
2025-08-19 13:15:25.396 10805-10805 Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 通知rulebase处理应用卸载: de.blinkt.openvpn
2025-08-19 13:15:25.398 10805-10805 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 收到系统广播，应用已卸载: de.blinkt.openvpn
2025-08-19 13:15:25.400 10805-10805 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 未找到匹配的卸载回调: de.blinkt.openvpn
2025-08-19 13:15:25.401 10805-10805 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 未找到匹配的规则应用: de.blinkt.openvpn
2025-08-19 13:15:25.402 10805-10805 Platform                com.dspread.mdm.service              D  🔧 重新收集应用信息（强制刷新）
2025-08-19 13:15:25.408 10805-11924 AppManager              com.dspread.mdm.service              I  ℹ️ 应用信息获取完成: 总数121(系统119/用户2) 返回2个
2025-08-19 13:15:25.418 10805-10819 ead.mdm.servic          com.dspread.mdm.service              I  Background concurrent copying GC freed 28796(2019KB) AllocSpace objects, 24(776KB) LOS objects, 49% free, 3521KB/7043KB, paused 85us total 105.405ms
2025-08-19 13:15:25.421 10805-11924 Platform                com.dspread.mdm.service              D  🔧 应用信息: 2 个应用
2025-08-19 13:15:25.498 10805-10805 AppManager              com.dspread.mdm.service              I  ℹ️ 应用信息获取完成: 总数121(系统119/用户2) 返回2个
2025-08-19 13:15:25.517 10805-10805 Platform                com.dspread.mdm.service              D  🔧 应用信息: 2 个应用
2025-08-19 13:15:25.521 10805-10805 Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 应用信息缓存刷新完成
2025-08-19 13:15:25.534 10805-10805 Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 发送应用刷新广播
2025-08-19 13:15:25.581 10805-11924 Platform                com.dspread.mdm.service              D  🔧 内存总容量为： 1.86GB 
2025-08-19 13:15:25.581 10805-11924 Platform                com.dspread.mdm.service              D  🔧 内存使用量为： 0.58GB 
2025-08-19 13:15:25.598 10805-11924 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0109","serialNo":"01610040202307060227","request_time":"1755580525589","request_id":"1755580525589C0109","version":"1","data":{"apkInfo":[{"packName":"com.dspread.mdm.service","apkName":"service","versionCode":20,"versionName":"1.1.01.20250819.DSPREAD.MDM.SERVICE","updateDate":"2025-08-19 12:10:19"},{"packName":"mark.via","apkName":"Via","versionCode":20250117,"versionName":"6.2.0","updateDate":"2025-08-18 17:50:24"}],"locationInfo":{"gps":{"longitude":"-999.0","latitude":"-999.0","satelliteCount":0,"satelliteCountInUsed":0,"satelliteAverSNR":"0.00","distance":"0.0","lockStatus":"1","lockMeter":"200","proId":"","proName":""},"baseSite":{"MCC":"","MNC":"","CID":"-1","LAC":"-1","DBM":"-999","IMSI":"","ICCID":""},"wifi":{"SSID":"fubox_2.4G","IP":"**************","MAC":"52:C3:63:13:8C:EE","SSTH":"-27","BSSID":"7c:10:c9:d6:9f:10"},"address":"无法获取"},"hardwareInfo":[{"name":"CPU Usage","desc":"cpu:0.50"},{"name":"storage_usage_status","desc":" 5.39GB \/ 32.00GB ","state":"1"},{"name":"total_memory","desc":" 0.58GB \/ 1.86GB ","state":"1"},{"name":"androidVersion","desc":"11","state":"1"},{"name":"network_status","desc":"wifi","state":"1"},{"name":"OSVer","desc":"11","state":"1"}],"wifiOption":[{"SSID":"fubox_2.4G","SSTH":"-26"},{"SSID":"2206-5G","SSTH":"-31"},{"SSID":"2206","SSTH":"-39"},{"SSID":"@Ruijie-1816","SSTH":"-52"},{"SSID":"2205_5G","SSTH":"-54"},{"SSID":"2205","SSTH":"-56"},{"SSID":"DIRECT-C6-Mi All-in-One Inkjet","SSTH":"-57"},{"SSID":"2207","SSTH":"-66"},{"SSID":"DIRECT-CC-HP Laser 1188w","SSTH":"-69"},{"SSID":"2207-5G","SSTH":"-82"},{"SSID":"vivo Y51s","SSTH":"-83"}],"sytemInfo":{"androidVersion":"11","buildNumber":"D60_Baseline_debug_20250708_ota_test","aspl":"2021-12-05"}},"myVersionName":"1.1.01.20250819.DSPREAD.MDM.SERVICE","terminalDate":"20250819131525"}
2025-08-19 13:15:25.598 10805-11924 WebSocket               com.dspread.mdm.service              I  🔧 C0109 终端信息上传完成 (1, trigger=terminal_info_timer)
2025-08-19 13:15:25.599 10805-11924 WebSocket               com.dspread.mdm.service              I  🔧 同时上传C0901应用信息（含服务信息）
2025-08-19 13:15:25.600 10805-11924 WebSocket               com.dspread.mdm.service              I  🔧 检查默认开启的服务
2025-08-19 13:15:25.600 10805-11924 WebSocket               com.dspread.mdm.service              I  🔧 本地无LogStream服务信息
2025-08-19 13:15:25.601 10805-11924 WebSocket               com.dspread.mdm.service              I  🔧 LogStream服务未启用，跳过保存
2025-08-19 13:15:25.602 10805-11924 WebSocket               com.dspread.mdm.service              I  🔧 默认服务检查完成
2025-08-19 13:15:25.602 10805-11924 WebSocket               com.dspread.mdm.service              I  🔧 C0901 主动式上送: command_trigger (主动: 12)
2025-08-19 13:15:25.605 10805-11924 Platform                com.dspread.mdm.service              D  🔧 使用缓存的应用信息
2025-08-19 13:15:25.612 10805-11924 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0901","serialNo":"01610040202307060227","request_time":"1755580525605","request_id":"1755580525605C0901","version":"1","data":{"apkInfo":[{"packName":"com.dspread.mdm.service","apkName":"service","versionCode":20,"versionName":"1.1.01.20250819.DSPREAD.MDM.SERVICE","updateDate":"2025-08-19 12:10:19"},{"packName":"mark.via","apkName":"Via","versionCode":20250117,"versionName":"6.2.0","updateDate":"2025-08-18 17:50:24"}],"sytemInfo":{"androidVersion":"11","buildNumber":"D60_Baseline_debug_20250708_ota_test","aspl":"2021-12-05"},"serviceInfo":[{"taskId":"1755580474546","serviceId":"856c2e6d2c7c4f60bc0fadcf9b9ad650","serviceName":"Log Stream","taskType":"05","command":"C03","stateDesc":"IMPLEMENTED","beginDate":"2025-08-19 13:14:37","endDate":"9999-12-31 23:59:59","period":"1","request_id":"","request_time":""}]},"myVersionName":"1.1.01.20250819.DSPREAD.MDM.SERVICE","terminalDate":"20250819131525"}
2025-08-19 13:15:25.613 10805-11924 WebSocket               com.dspread.mdm.service              I  🔧 C0901 应用信息上送成功 (1)
2025-08-19 13:15:26.426 10805-11924 Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"request_time":"1755580525818","org_request_time":"1755580525589","org_request_id":"1755580525589C0109","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755580525818S0000","serialNo":"01610040202307060227"}
2025-08-19 13:15:26.428 10805-11924 WebSocket               com.dspread.mdm.service              I  🔧 收到响应消息: transCode=S0000, orgRequestId=1755580525589C0109, state=0, remark=
2025-08-19 13:15:26.429 10805-11924 WebSocket               com.dspread.mdm.service              I  🔧 终端信息上传成功
2025-08-19 13:15:26.430 10805-11924 WebSocket               com.dspread.mdm.service              I  🔧 终端信息上传成功标志已设置
2025-08-19 13:15:29.952 10805-12015 AppManager              com.dspread.mdm.service              I  ℹ️ Package uninstalled (backup check): de.blinkt.openvpn
2025-08-19 13:15:29.953 10805-12015 Task                    com.dspread.mdm.service              D  🔧 卸载成功，通知应用状态变化
2025-08-19 13:15:29.954 10805-12015 Platform                com.dspread.mdm.service              D  🔧 应用状态变化: UNINSTALL - de.blinkt.openvpn
2025-08-19 13:15:29.956 10805-12015 Platform                com.dspread.mdm.service              D  🔧 重新收集应用信息（强制刷新）
2025-08-19 13:15:30.015 10805-12015 AppManager              com.dspread.mdm.service              I  ℹ️ 应用信息获取完成: 总数121(系统119/用户2) 返回2个
2025-08-19 13:15:30.022 10805-12015 Platform                com.dspread.mdm.service              D  🔧 应用信息: 2 个应用
2025-08-19 13:15:30.024 10805-12015 Task                    com.dspread.mdm.service              D  🔧 更新任务状态: taskId=1755580524415, state=D02
2025-08-19 13:15:30.026 10805-12015 Task                    com.dspread.mdm.service              D  🔧 保存任务列表到存储: 1 个
2025-08-19 13:15:30.028 10805-12015 Task                    com.dspread.mdm.service              D  🔧 保存任务列表到存储: 1 个
2025-08-19 13:15:30.029 10805-12015 WebSocket               com.dspread.mdm.service              I  🔧 ST001 主动式上送: task_failed (主动: 13)
2025-08-19 13:15:30.041 10805-12015 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0108","serialNo":"01610040202307060227","request_time":"1755580530033","request_id":"1755580530033C0108","version":"1","data":{"taskId":"1755580524415","taskResult":"D02","appId":"1755580524415"},"myVersionName":"1.1.01.20250819.DSPREAD.MDM.SERVICE","terminalDate":"20250819131530","org_request_id":"1755580524415ST001","org_request_time":"1755580524415"}
2025-08-19 13:15:30.042 10805-12015 WebSocket               com.dspread.mdm.service              I  🔧 任务结果实时上送: taskId=1755580524415, result=D02 (1)
2025-08-19 13:15:30.422 10805-11924 Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"request_time":"1755580530245","org_request_time":"1755580530033","org_request_id":"1755580530033C0108","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755580530245S0000","serialNo":"01610040202307060227"}
2025-08-19 13:15:30.424 10805-11924 WebSocket               com.dspread.mdm.service              I  🔧 收到响应消息: transCode=S0000, orgRequestId=1755580530033C0108, state=0, remark=
2025-08-19 13:15:30.425 10805-11924 WebSocket               com.dspread.mdm.service              I  🔧 处理任务结果响应: state=0
2025-08-19 13:15:30.426 10805-11924 WebSocket               com.dspread.mdm.service              I  🔧 任务结果上传成功
2025-08-19 13:15:33.286 10805-10805 Receiver                com.dspread.mdm.service              D  🔧 电池状态更新: 电池状态: 电量=76%, 温度=29.5°C, 健康=2, 充电=是, 低电量=否, 耗尽=否
2025-08-19 13:15:37.187 10805-11924 Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"msg_state":"P","request_time":"1755580536662","data":{"taskList":[{"beginDate":"2024-08-19 05:15:36","taskType":"02","apkName":"Via","endDate":"9999-12-31 23:59:59","appId":"1755580536662","packName":"mark.via","versionName":"6.2.0","taskId":"1755580536662","versionCode":"20250117"}]},"tranCode":"ST001","request_id":"1755580536662ST001","version":"1","serialNo":"01610040202307060227"}
2025-08-19 13:15:37.189 10805-11924 WebSocket               com.dspread.mdm.service              I  🔧 准备发送响应确认 - requestId: 1755580536662ST001, needResponse: true
2025-08-19 13:15:37.200 10805-11924 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0000","serialNo":"01610040202307060227","request_time":"1755580537193","request_id":"1755580537193C0000","version":"1","org_request_id":"1755580536662ST001","org_request_time":"1755580536662","response_state":"0","myVersionName":"1.1.01.20250819.DSPREAD.MDM.SERVICE","terminalDate":"20250819131537"}
2025-08-19 13:15:37.210 10805-11924 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0000","serialNo":"01610040202307060227","request_time":"1755580537204","request_id":"1755580537204C0000","version":"1","org_request_id":"1755580536662ST001","org_request_time":"1755580536662","response_state":"0","myVersionName":"1.1.01.20250819.DSPREAD.MDM.SERVICE","terminalDate":"20250819131537"}
2025-08-19 13:15:37.212 10805-11924 WebSocket               com.dspread.mdm.service              I  🔧 已发送C0000响应确认: requestId=1755580536662ST001
2025-08-19 13:15:37.214 10805-11924 Task                    com.dspread.mdm.service              D  🔧 处理任务消息: ST001
2025-08-19 13:15:37.215 10805-11924 Task                    com.dspread.mdm.service              I  ℹ️ 更新任务列表: requestId=1755580536662ST001, 任务数量=1
2025-08-19 13:15:37.217 10805-11924 Task                    com.dspread.mdm.service              D  🔧 任务插入到末尾: taskId=1755580536662
2025-08-19 13:15:37.218 10805-11924 Task                    com.dspread.mdm.service              D  🔧 添加/更新任务: taskId=1755580536662
2025-08-19 13:15:37.220 10805-11924 Task                    com.dspread.mdm.service              D  🔧 发现已完成任务: taskId=1755580524415, result=D02
2025-08-19 13:15:37.221 10805-11924 Task                    com.dspread.mdm.service              D  🔧 上送任务结果: taskId=1755580524415, result=D02
2025-08-19 13:15:37.223 10805-11924 Task                    com.dspread.mdm.service              D  🔧 清理已完成任务: taskId=1755580524415, result=D02
2025-08-19 13:15:37.224 10805-11924 Task                    com.dspread.mdm.service              D  🔧 清理了 1 个已完成任务
2025-08-19 13:15:37.226 10805-11924 Task                    com.dspread.mdm.service              D  🔧 保存任务列表到存储: 1 个
2025-08-19 13:15:37.227 10805-11924 Task                    com.dspread.mdm.service              D  🔧 开始处理任务列表，共 1 个任务
2025-08-19 13:15:37.228 10805-11924 Task                    com.dspread.mdm.service              D  🔧 处理任务: taskId=1755580536662, type=02, package=mark.via, apk=Via
2025-08-19 13:15:37.229 10805-11924 Task                    com.dspread.mdm.service              D  🔧 处理卸载任务: 1755580536662
2025-08-19 13:15:37.233 10805-11924 AppManager              com.dspread.mdm.service              I  ℹ️ Uninstalling app via PackageInstaller: mark.via
2025-08-19 13:15:37.234 10805-11924 AppManager              com.dspread.mdm.service              I  ℹ️ Deleting package via PackageInstaller: mark.via
2025-08-19 13:15:37.238 10805-11924 AppManager              com.dspread.mdm.service              I  ℹ️ PackageInstaller uninstall initiated
2025-08-19 13:15:37.584 10805-11924 Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"msg_state":"P","request_time":"1755580537148","data":{"param":"","c_type":"CALLC0109"},"expire_time":1755584137,"tranCode":"SC004","request_id":"1755580537148SC004","version":"1","serialNo":"01610040202307060227"}
2025-08-19 13:15:37.585 10805-11924 WebSocket               com.dspread.mdm.service              I  🔧 准备发送响应确认 - requestId: 1755580537148SC004, needResponse: true
2025-08-19 13:15:37.598 10805-11924 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0000","serialNo":"01610040202307060227","request_time":"1755580537593","request_id":"1755580537593C0000","version":"1","org_request_id":"1755580537148SC004","org_request_time":"1755580537148","response_state":"0","myVersionName":"1.1.01.20250819.DSPREAD.MDM.SERVICE","terminalDate":"20250819131537"}
2025-08-19 13:15:37.606 10805-11924 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0000","serialNo":"01610040202307060227","request_time":"1755580537600","request_id":"1755580537600C0000","version":"1","org_request_id":"1755580537148SC004","org_request_time":"1755580537148","response_state":"0","myVersionName":"1.1.01.20250819.DSPREAD.MDM.SERVICE","terminalDate":"20250819131537"}
2025-08-19 13:15:37.606 10805-11924 WebSocket               com.dspread.mdm.service              I  🔧 已发送C0000响应确认: requestId=1755580537148SC004
2025-08-19 13:15:37.608 10805-11924 WebSocket               com.dspread.mdm.service              I  🔧 处理命令: SC004
2025-08-19 13:15:37.608 10805-11924 WebSocket               com.dspread.mdm.service              I  🔧 收到SC004命令，类型: CALLC0109
2025-08-19 13:15:37.609 10805-11924 WebSocket               com.dspread.mdm.service              I  🔧 服务器请求终端信息上传
2025-08-19 13:15:37.610 10805-11924 WebSocket               com.dspread.mdm.service              I  🔧 C0109 主动式上送: terminal_info_timer (主动: 14)
2025-08-19 13:15:37.613 10805-11924 Platform                com.dspread.mdm.service              D  🔧 重新收集应用信息（强制刷新）
2025-08-19 13:15:37.653 10805-11924 AppManager              com.dspread.mdm.service              I  ℹ️ 应用信息获取完成: 总数120(系统119/用户1) 返回1个
2025-08-19 13:15:37.658 10805-11924 Platform                com.dspread.mdm.service              D  🔧 应用信息: 1 个应用
2025-08-19 13:15:37.697 10805-10805 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 收到广播: android.intent.action.PACKAGE_REMOVED
2025-08-19 13:15:37.698 10805-10805 Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 收到包更新广播: android.intent.action.PACKAGE_REMOVED, 包名: mark.via
2025-08-19 13:15:37.698 10805-10805 Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 应用卸载: mark.via
2025-08-19 13:15:37.699 10805-10805 Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 检查WebSocket状态: packageName=mark.via, action=UNINSTALL
2025-08-19 13:15:37.700 10805-10805 Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 通知rulebase处理应用卸载: mark.via
2025-08-19 13:15:37.700 10805-10805 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 收到系统广播，应用已卸载: mark.via
2025-08-19 13:15:37.701 10805-10805 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 未找到匹配的卸载回调: mark.via
2025-08-19 13:15:37.702 10805-10805 RuleBase                com.dspread.mdm.service              D  🔧 RuleExecutionEngine 未找到匹配的规则应用: mark.via
2025-08-19 13:15:37.703 10805-10805 Platform                com.dspread.mdm.service              D  🔧 重新收集应用信息（强制刷新）
2025-08-19 13:15:37.721 10805-10819 ead.mdm.servic          com.dspread.mdm.service              I  Background concurrent copying GC freed 28624(1828KB) AllocSpace objects, 12(368KB) LOS objects, 49% free, 3607KB/7214KB, paused 85us total 106.385ms
2025-08-19 13:15:37.739 10805-10805 AppManager              com.dspread.mdm.service              I  ℹ️ 应用信息获取完成: 总数120(系统119/用户1) 返回1个
2025-08-19 13:15:37.742 10805-10805 Platform                com.dspread.mdm.service              D  🔧 应用信息: 1 个应用
2025-08-19 13:15:37.743 10805-10805 Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 应用信息缓存刷新完成
2025-08-19 13:15:37.757 10805-10805 Receiver                com.dspread.mdm.service              D  🔧 PackageUpdateEventHandler 发送应用刷新广播
2025-08-19 13:15:37.848 10805-11924 Platform                com.dspread.mdm.service              D  🔧 内存总容量为： 1.86GB 
2025-08-19 13:15:37.849 10805-11924 Platform                com.dspread.mdm.service              D  🔧 内存使用量为： 0.58GB 
2025-08-19 13:15:37.865 10805-11924 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0109","serialNo":"01610040202307060227","request_time":"1755580537858","request_id":"1755580537858C0109","version":"1","data":{"apkInfo":[{"packName":"com.dspread.mdm.service","apkName":"service","versionCode":20,"versionName":"1.1.01.20250819.DSPREAD.MDM.SERVICE","updateDate":"2025-08-19 12:10:19"}],"locationInfo":{"gps":{"longitude":"-999.0","latitude":"-999.0","satelliteCount":0,"satelliteCountInUsed":0,"satelliteAverSNR":"0.00","distance":"0.0","lockStatus":"1","lockMeter":"200","proId":"","proName":""},"baseSite":{"MCC":"","MNC":"","CID":"-1","LAC":"-1","DBM":"-999","IMSI":"","ICCID":""},"wifi":{"SSID":"fubox_2.4G","IP":"**************","MAC":"52:C3:63:13:8C:EE","SSTH":"-27","BSSID":"7c:10:c9:d6:9f:10"},"address":"无法获取"},"hardwareInfo":[{"name":"CPU Usage","desc":"cpu:0.50"},{"name":"storage_usage_status","desc":" 5.38GB \/ 32.00GB ","state":"1"},{"name":"total_memory","desc":" 0.58GB \/ 1.86GB ","state":"1"},{"name":"androidVersion","desc":"11","state":"1"},{"name":"network_status","desc":"wifi","state":"1"},{"name":"OSVer","desc":"11","state":"1"}],"wifiOption":[{"SSID":"fubox_2.4G","SSTH":"-26"},{"SSID":"2206-5G","SSTH":"-31"},{"SSID":"2206","SSTH":"-39"},{"SSID":"@Ruijie-1816","SSTH":"-52"},{"SSID":"2205_5G","SSTH":"-54"},{"SSID":"2205","SSTH":"-56"},{"SSID":"DIRECT-C6-Mi All-in-One Inkjet","SSTH":"-57"},{"SSID":"2207","SSTH":"-66"},{"SSID":"DIRECT-CC-HP Laser 1188w","SSTH":"-69"},{"SSID":"2207-5G","SSTH":"-82"},{"SSID":"vivo Y51s","SSTH":"-83"}],"sytemInfo":{"androidVersion":"11","buildNumber":"D60_Baseline_debug_20250708_ota_test","aspl":"2021-12-05"}},"myVersionName":"1.1.01.20250819.DSPREAD.MDM.SERVICE","terminalDate":"20250819131537"}
2025-08-19 13:15:37.865 10805-11924 WebSocket               com.dspread.mdm.service              I  🔧 C0109 终端信息上传完成 (1, trigger=terminal_info_timer)
2025-08-19 13:15:37.866 10805-11924 WebSocket               com.dspread.mdm.service              I  🔧 同时上传C0901应用信息（含服务信息）
2025-08-19 13:15:37.867 10805-11924 WebSocket               com.dspread.mdm.service              I  🔧 检查默认开启的服务
2025-08-19 13:15:37.867 10805-11924 WebSocket               com.dspread.mdm.service              I  🔧 本地无LogStream服务信息
2025-08-19 13:15:37.868 10805-11924 WebSocket               com.dspread.mdm.service              I  🔧 LogStream服务未启用，跳过保存
2025-08-19 13:15:37.869 10805-11924 WebSocket               com.dspread.mdm.service              I  🔧 默认服务检查完成
2025-08-19 13:15:37.869 10805-11924 WebSocket               com.dspread.mdm.service              I  🔧 C0901 主动式上送: command_trigger (主动: 15)
2025-08-19 13:15:37.872 10805-11924 Platform                com.dspread.mdm.service              D  🔧 使用缓存的应用信息
2025-08-19 13:15:37.878 10805-11924 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0901","serialNo":"01610040202307060227","request_time":"1755580537873","request_id":"1755580537873C0901","version":"1","data":{"apkInfo":[{"packName":"com.dspread.mdm.service","apkName":"service","versionCode":20,"versionName":"1.1.01.20250819.DSPREAD.MDM.SERVICE","updateDate":"2025-08-19 12:10:19"}],"sytemInfo":{"androidVersion":"11","buildNumber":"D60_Baseline_debug_20250708_ota_test","aspl":"2021-12-05"},"serviceInfo":[{"taskId":"1755580474546","serviceId":"856c2e6d2c7c4f60bc0fadcf9b9ad650","serviceName":"Log Stream","taskType":"05","command":"C03","stateDesc":"IMPLEMENTED","beginDate":"2025-08-19 13:14:37","endDate":"9999-12-31 23:59:59","period":"1","request_id":"","request_time":""}]},"myVersionName":"1.1.01.20250819.DSPREAD.MDM.SERVICE","terminalDate":"20250819131537"}
2025-08-19 13:15:37.879 10805-11924 WebSocket               com.dspread.mdm.service              I  🔧 C0901 应用信息上送成功 (1)
2025-08-19 13:15:38.276 10805-11924 Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"request_time":"1755580538085","org_request_time":"1755580537858","org_request_id":"1755580537858C0109","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755580538085S0000","serialNo":"01610040202307060227"}
2025-08-19 13:15:38.278 10805-11924 WebSocket               com.dspread.mdm.service              I  🔧 收到响应消息: transCode=S0000, orgRequestId=1755580537858C0109, state=0, remark=
2025-08-19 13:15:38.279 10805-11924 WebSocket               com.dspread.mdm.service              I  🔧 终端信息上传成功
2025-08-19 13:15:38.281 10805-11924 WebSocket               com.dspread.mdm.service              I  🔧 终端信息上传成功标志已设置
2025-08-19 13:15:42.243 10805-12022 AppManager              com.dspread.mdm.service              I  ℹ️ Package uninstalled (backup check): mark.via
2025-08-19 13:15:42.244 10805-12022 Task                    com.dspread.mdm.service              D  🔧 卸载成功，通知应用状态变化
2025-08-19 13:15:42.245 10805-12022 Platform                com.dspread.mdm.service              D  🔧 应用状态变化: UNINSTALL - mark.via
2025-08-19 13:15:42.247 10805-12022 Platform                com.dspread.mdm.service              D  🔧 重新收集应用信息（强制刷新）
2025-08-19 13:15:42.302 10805-12022 AppManager              com.dspread.mdm.service              I  ℹ️ 应用信息获取完成: 总数120(系统119/用户1) 返回1个
2025-08-19 13:15:42.306 10805-12022 Platform                com.dspread.mdm.service              D  🔧 应用信息: 1 个应用
2025-08-19 13:15:42.308 10805-12022 Task                    com.dspread.mdm.service              D  🔧 更新任务状态: taskId=1755580536662, state=D02
2025-08-19 13:15:42.310 10805-12022 Task                    com.dspread.mdm.service              D  🔧 保存任务列表到存储: 1 个
2025-08-19 13:15:42.312 10805-12022 Task                    com.dspread.mdm.service              D  🔧 保存任务列表到存储: 1 个
2025-08-19 13:15:42.314 10805-12022 WebSocket               com.dspread.mdm.service              I  🔧 ST001 主动式上送: task_failed (主动: 16)
2025-08-19 13:15:42.325 10805-12022 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0108","serialNo":"01610040202307060227","request_time":"1755580542317","request_id":"1755580542317C0108","version":"1","data":{"taskId":"1755580536662","taskResult":"D02","appId":"1755580536662"},"myVersionName":"1.1.01.20250819.DSPREAD.MDM.SERVICE","terminalDate":"20250819131542","org_request_id":"1755580536662ST001","org_request_time":"1755580536662"}
2025-08-19 13:15:42.327 10805-12022 WebSocket               com.dspread.mdm.service              I  🔧 任务结果实时上送: taskId=1755580536662, result=D02 (1)
2025-08-19 13:15:43.016 10805-11924 Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"request_time":"1755580542529","org_request_time":"1755580542317","org_request_id":"1755580542317C0108","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755580542529S0000","serialNo":"01610040202307060227"}
2025-08-19 13:15:43.017 10805-11924 WebSocket               com.dspread.mdm.service              I  🔧 收到响应消息: transCode=S0000, orgRequestId=1755580542317C0108, state=0, remark=
2025-08-19 13:15:43.019 10805-11924 WebSocket               com.dspread.mdm.service              I  🔧 处理任务结果响应: state=0
2025-08-19 13:15:43.020 10805-11924 WebSocket               com.dspread.mdm.service              I  🔧 任务结果上传成功
2025-08-19 13:15:54.179 10805-11924 Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"msg_state":"P","request_time":"1755580553752","data":{"param":"","c_type":"CALLC0109"},"expire_time":1755584153,"tranCode":"SC004","request_id":"1755580553752SC004","version":"1","serialNo":"01610040202307060227"}
2025-08-19 13:15:54.181 10805-11924 WebSocket               com.dspread.mdm.service              I  🔧 准备发送响应确认 - requestId: 1755580553752SC004, needResponse: true
2025-08-19 13:15:54.192 10805-11924 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0000","serialNo":"01610040202307060227","request_time":"1755580554185","request_id":"1755580554185C0000","version":"1","org_request_id":"1755580553752SC004","org_request_time":"1755580553752","response_state":"0","myVersionName":"1.1.01.20250819.DSPREAD.MDM.SERVICE","terminalDate":"20250819131554"}
2025-08-19 13:15:54.201 10805-11924 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0000","serialNo":"01610040202307060227","request_time":"1755580554196","request_id":"1755580554196C0000","version":"1","org_request_id":"1755580553752SC004","org_request_time":"1755580553752","response_state":"0","myVersionName":"1.1.01.20250819.DSPREAD.MDM.SERVICE","terminalDate":"20250819131554"}
2025-08-19 13:15:54.203 10805-11924 WebSocket               com.dspread.mdm.service              I  🔧 已发送C0000响应确认: requestId=1755580553752SC004
2025-08-19 13:15:54.205 10805-11924 WebSocket               com.dspread.mdm.service              I  🔧 处理命令: SC004
2025-08-19 13:15:54.206 10805-11924 WebSocket               com.dspread.mdm.service              I  🔧 收到SC004命令，类型: CALLC0109
2025-08-19 13:15:54.207 10805-11924 WebSocket               com.dspread.mdm.service              I  🔧 服务器请求终端信息上传
2025-08-19 13:15:54.208 10805-11924 WebSocket               com.dspread.mdm.service              I  🔧 C0109 主动式上送: terminal_info_timer (主动: 17)
2025-08-19 13:15:54.213 10805-11924 Platform                com.dspread.mdm.service              D  🔧 重新收集应用信息（强制刷新）
2025-08-19 13:15:54.258 10805-11924 AppManager              com.dspread.mdm.service              I  ℹ️ 应用信息获取完成: 总数120(系统119/用户1) 返回1个
2025-08-19 13:15:54.262 10805-11924 Platform                com.dspread.mdm.service              D  🔧 应用信息: 1 个应用
2025-08-19 13:15:54.330 10805-11924 Platform                com.dspread.mdm.service              D  🔧 内存总容量为： 1.86GB 
2025-08-19 13:15:54.331 10805-11924 Platform                com.dspread.mdm.service              D  🔧 内存使用量为： 0.58GB 
2025-08-19 13:15:54.351 10805-11924 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0109","serialNo":"01610040202307060227","request_time":"1755580554342","request_id":"1755580554342C0109","version":"1","data":{"apkInfo":[{"packName":"com.dspread.mdm.service","apkName":"service","versionCode":20,"versionName":"1.1.01.20250819.DSPREAD.MDM.SERVICE","updateDate":"2025-08-19 12:10:19"}],"locationInfo":{"gps":{"longitude":"-999.0","latitude":"-999.0","satelliteCount":0,"satelliteCountInUsed":0,"satelliteAverSNR":"0.00","distance":"0.0","lockStatus":"1","lockMeter":"200","proId":"","proName":""},"baseSite":{"MCC":"","MNC":"","CID":"-1","LAC":"-1","DBM":"-999","IMSI":"","ICCID":""},"wifi":{"SSID":"fubox_2.4G","IP":"**************","MAC":"52:C3:63:13:8C:EE","SSTH":"-27","BSSID":"7c:10:c9:d6:9f:10"},"address":"无法获取"},"hardwareInfo":[{"name":"CPU Usage","desc":"cpu:0.50"},{"name":"storage_usage_status","desc":" 5.38GB \/ 32.00GB ","state":"1"},{"name":"total_memory","desc":" 0.58GB \/ 1.86GB ","state":"1"},{"name":"androidVersion","desc":"11","state":"1"},{"name":"network_status","desc":"wifi","state":"1"},{"name":"OSVer","desc":"11","state":"1"}],"wifiOption":[{"SSID":"fubox_2.4G","SSTH":"-26"},{"SSID":"2206-5G","SSTH":"-31"},{"SSID":"2206","SSTH":"-39"},{"SSID":"@Ruijie-1816","SSTH":"-52"},{"SSID":"2205_5G","SSTH":"-54"},{"SSID":"2205","SSTH":"-56"},{"SSID":"DIRECT-C6-Mi All-in-One Inkjet","SSTH":"-57"},{"SSID":"2207","SSTH":"-66"},{"SSID":"DIRECT-CC-HP Laser 1188w","SSTH":"-69"},{"SSID":"2207-5G","SSTH":"-82"},{"SSID":"vivo Y51s","SSTH":"-83"}],"sytemInfo":{"androidVersion":"11","buildNumber":"D60_Baseline_debug_20250708_ota_test","aspl":"2021-12-05"}},"myVersionName":"1.1.01.20250819.DSPREAD.MDM.SERVICE","terminalDate":"20250819131554"}
2025-08-19 13:15:54.352 10805-11924 WebSocket               com.dspread.mdm.service              I  🔧 C0109 终端信息上传完成 (1, trigger=terminal_info_timer)
2025-08-19 13:15:54.353 10805-11924 WebSocket               com.dspread.mdm.service              I  🔧 同时上传C0901应用信息（含服务信息）
2025-08-19 13:15:54.354 10805-11924 WebSocket               com.dspread.mdm.service              I  🔧 检查默认开启的服务
2025-08-19 13:15:54.354 10805-11924 WebSocket               com.dspread.mdm.service              I  🔧 本地无LogStream服务信息
2025-08-19 13:15:54.355 10805-11924 WebSocket               com.dspread.mdm.service              I  🔧 LogStream服务未启用，跳过保存
2025-08-19 13:15:54.356 10805-11924 WebSocket               com.dspread.mdm.service              I  🔧 默认服务检查完成
2025-08-19 13:15:54.357 10805-11924 WebSocket               com.dspread.mdm.service              I  🔧 C0901 主动式上送: command_trigger (主动: 18)
2025-08-19 13:15:54.359 10805-11924 Platform                com.dspread.mdm.service              D  🔧 使用缓存的应用信息
2025-08-19 13:15:54.365 10805-11924 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0901","serialNo":"01610040202307060227","request_time":"1755580554360","request_id":"1755580554360C0901","version":"1","data":{"apkInfo":[{"packName":"com.dspread.mdm.service","apkName":"service","versionCode":20,"versionName":"1.1.01.20250819.DSPREAD.MDM.SERVICE","updateDate":"2025-08-19 12:10:19"}],"sytemInfo":{"androidVersion":"11","buildNumber":"D60_Baseline_debug_20250708_ota_test","aspl":"2021-12-05"},"serviceInfo":[{"taskId":"1755580474546","serviceId":"856c2e6d2c7c4f60bc0fadcf9b9ad650","serviceName":"Log Stream","taskType":"05","command":"C03","stateDesc":"IMPLEMENTED","beginDate":"2025-08-19 13:14:37","endDate":"9999-12-31 23:59:59","period":"1","request_id":"","request_time":""}]},"myVersionName":"1.1.01.20250819.DSPREAD.MDM.SERVICE","terminalDate":"20250819131554"}
2025-08-19 13:15:54.366 10805-11924 WebSocket               com.dspread.mdm.service              I  🔧 C0901 应用信息上送成功 (1)
2025-08-19 13:15:55.307 10805-11924 Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"request_time":"1755580554876","org_request_time":"1755580554342","org_request_id":"1755580554342C0109","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755580554876S0000","serialNo":"01610040202307060227"}
2025-08-19 13:15:55.309 10805-11924 WebSocket               com.dspread.mdm.service              I  🔧 收到响应消息: transCode=S0000, orgRequestId=1755580554342C0109, state=0, remark=
2025-08-19 13:15:55.310 10805-11924 WebSocket               com.dspread.mdm.service              I  🔧 终端信息上传成功
2025-08-19 13:15:55.312 10805-11924 WebSocket               com.dspread.mdm.service              I  🔧 终端信息上传成功标志已设置
2025-08-19 13:16:00.016 10805-11924 Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"msg_state":"P","request_time":"1755580559574","data":{"param":"","c_type":"CALL_LOG_STREAM"},"expire_time":1756185359,"tranCode":"SC004","request_id":"1755580559574SC004","version":"1","serialNo":"01610040202307060227"}
2025-08-19 13:16:00.017 10805-11924 WebSocket               com.dspread.mdm.service              I  🔧 准备发送响应确认 - requestId: 1755580559574SC004, needResponse: true
2025-08-19 13:16:00.027 10805-11924 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0000","serialNo":"01610040202307060227","request_time":"1755580560021","request_id":"1755580560021C0000","version":"1","org_request_id":"1755580559574SC004","org_request_time":"1755580559574","response_state":"0","myVersionName":"1.1.01.20250819.DSPREAD.MDM.SERVICE","terminalDate":"20250819131600"}
2025-08-19 13:16:00.035 10805-11924 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0000","serialNo":"01610040202307060227","request_time":"1755580560031","request_id":"1755580560031C0000","version":"1","org_request_id":"1755580559574SC004","org_request_time":"1755580559574","response_state":"0","myVersionName":"1.1.01.20250819.DSPREAD.MDM.SERVICE","terminalDate":"20250819131600"}
2025-08-19 13:16:00.036 10805-11924 WebSocket               com.dspread.mdm.service              I  🔧 已发送C0000响应确认: requestId=1755580559574SC004
2025-08-19 13:16:00.038 10805-11924 WebSocket               com.dspread.mdm.service              I  🔧 处理命令: SC004
2025-08-19 13:16:00.039 10805-11924 WebSocket               com.dspread.mdm.service              I  🔧 收到SC004命令，类型: CALL_LOG_STREAM
2025-08-19 13:16:00.041 10805-11924 WebSocket               com.dspread.mdm.service              I  🔧 服务器请求启动日志流
2025-08-19 13:16:21.074 10805-11925 WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 5
2025-08-19 13:16:21.609 10805-11924 WebSocket               com.dspread.mdm.service              I  🔧 收到服务器 PONG 帧: 5
2025-08-19 13:16:33.285 10805-10805 Receiver                com.dspread.mdm.service              D  🔧 电池状态更新: 电池状态: 电量=76%, 温度=29.5°C, 健康=2, 充电=是, 低电量=否, 耗尽=否
2025-08-19 13:17:21.075 10805-11925 WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 6
2025-08-19 13:17:21.720 10805-11924 WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误 (Ask Gemini)
                                                                                                    com.neovisionaries.ws.client.NoMoreFrameException: No more WebSocket frame from the server.
                                                                                                    	at com.neovisionaries.ws.client.WebSocketInputStream.readFrame(WebSocketInputStream.java:54)
                                                                                                    	at com.neovisionaries.ws.client.ReadingThread.readFrame(ReadingThread.java:338)
                                                                                                    	at com.neovisionaries.ws.client.ReadingThread.main(ReadingThread.java:99)
                                                                                                    	at com.neovisionaries.ws.client.ReadingThread.runMain(ReadingThread.java:64)
                                                                                                    	at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45)
2025-08-19 13:17:21.722 10805-11924 WebSocket               com.dspread.mdm.service              I  🔧 onError: 不触发重连，等待 onDisconnected 处理
2025-08-19 13:17:21.726 10805-11925 WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误 (Ask Gemini)
                                                                                                    com.neovisionaries.ws.client.WebSocketException: Flushing frames to the server failed: Socket closed
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.doFlush(WritingThread.java:434)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.sendFrames(WritingThread.java:384)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.main(WritingThread.java:108)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.runMain(WritingThread.java:55)
                                                                                                    	at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45)
                                                                                                    Caused by: java.net.SocketException: Socket closed
                                                                                                    	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.writeInternal(ConscryptEngineSocket.java:681)
                                                                                                    	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.write(ConscryptEngineSocket.java:652)
                                                                                                    	at java.io.BufferedOutputStream.flushBuffer(BufferedOutputStream.java:82)
                                                                                                    	at java.io.BufferedOutputStream.flush(BufferedOutputStream.java:140)
                                                                                                    	at java.io.BufferedOutputStream.flush(BufferedOutputStream.java:141)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.flush(WritingThread.java:271)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.doFlush(WritingThread.java:422)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.sendFrames(WritingThread.java:384) 
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.main(WritingThread.java:108) 
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.runMain(WritingThread.java:55) 
                                                                                                    	at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45) 
2025-08-19 13:17:21.728 10805-11925 WebSocket               com.dspread.mdm.service              I  🔧 onError: 不触发重连，等待 onDisconnected 处理
2025-08-19 13:17:21.731 10805-11925 WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误 (Ask Gemini)
                                                                                                    com.neovisionaries.ws.client.WebSocketException: Flushing frames to the server failed: Socket closed
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.doFlush(WritingThread.java:434)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.sendFrames(WritingThread.java:384)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.main(WritingThread.java:120)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.runMain(WritingThread.java:55)
                                                                                                    	at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45)
                                                                                                    Caused by: java.net.SocketException: Socket closed
                                                                                                    	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.writeInternal(ConscryptEngineSocket.java:681)
                                                                                                    	at com.android.org.conscrypt.ConscryptEngineSocket$SSLOutputStream.write(ConscryptEngineSocket.java:652)
                                                                                                    	at java.io.BufferedOutputStream.flushBuffer(BufferedOutputStream.java:82)
                                                                                                    	at java.io.BufferedOutputStream.flush(BufferedOutputStream.java:140)
                                                                                                    	at java.io.BufferedOutputStream.flush(BufferedOutputStream.java:141)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.flush(WritingThread.java:271)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.doFlush(WritingThread.java:422)
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.sendFrames(WritingThread.java:384) 
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.main(WritingThread.java:120) 
                                                                                                    	at com.neovisionaries.ws.client.WritingThread.runMain(WritingThread.java:55) 
                                                                                                    	at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45) 
2025-08-19 13:17:21.732 10805-11925 WebSocket               com.dspread.mdm.service              I  🔧 onError: 不触发重连，等待 onDisconnected 处理
2025-08-19 13:17:21.736 10805-11925 WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接断开 - 客户端主动断开
2025-08-19 13:17:21.737 10805-11925 WebSocket               com.dspread.mdm.service              I  🔧    服务器关闭帧: code=-1, reason=无原因
2025-08-19 13:17:21.738 10805-11925 WebSocket               com.dspread.mdm.service              I  🔧    客户端关闭帧: code=1002, reason=No more WebSocket frame from the server.
2025-08-19 13:17:21.739 10805-11925 WebSocket               com.dspread.mdm.service              I  🔧    断开发起方: 客户端
2025-08-19 13:17:21.741 10805-11925 WebSocket               com.dspread.mdm.service              I  🔧 reconnect() 被调用，调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onDisconnected:724
2025-08-19 13:17:21.742 10805-11925 WebSocket               com.dspread.mdm.service              I  🔧 开始重连 WebSocket... (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onDisconnected:724)
2025-08-19 13:17:21.746 10805-11925 WebSocket               com.dspread.mdm.service              I  🔧 使用Provisioning配置的重连策略: delayPolicy=1, delaySwitch=1, delayTime=60
2025-08-19 13:17:21.748 10805-11925 WebSocket               com.dspread.mdm.service              I  🔧 当前断开记录数: 10, 延迟开关: 1
2025-08-19 13:17:21.751 10805-11925 WebSocket               com.dspread.mdm.service              I  🔧 断开记录 最早一次断开: 2025-08-19 13:01:33
2025-08-19 13:17:21.754 10805-11925 WebSocket               com.dspread.mdm.service              I  🔧 断开记录 最后一次断开: 2025-08-19 13:17:21
2025-08-19 13:17:21.755 10805-11925 WebSocket               com.dspread.mdm.service              I  🔧 时间差: 15分钟 (配置阈值: 1分钟)
2025-08-19 13:17:21.756 10805-11925 WebSocket               com.dspread.mdm.service              I  🔧 断开间隔正常，使用标准重连策略
2025-08-19 13:17:21.757 10805-11925 WebSocket               com.dspread.mdm.service              I  🔧 重连策略计算完成: 第1次重连，间隔3000ms (3秒)
2025-08-19 13:17:21.759 10805-11925 WebSocket               com.dspread.mdm.service              I  🔧 开始第1次重连，间隔3000ms (3秒) (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onDisconnected:724)
2025-08-19 13:17:24.777 10805-10805 WebSocket               com.dspread.mdm.service              I  🔧 重连 WebSocket: wss://api.dspreadserv.net/status/websocket/register?serialNo=MDE2MTAwNDAyMDIzMDcwNjAyMjc=&publicKey=TUlHZk1BMEdDU3FHU0liM0RRRUJBUVVBQTRHTkFEQ0JpUUtCZ1FEQXEvOE5rMFJ1dEVpUW9yTTdXYTg4SkNEbTViQUJ3VlMyZFFvRWV2NEZDdGVLL3loZnVFSUUxZ2lBdVFsa05kZkhVOEJVUU1FWEoxZEZoVGl4RUhQUDRIQXRMbUU0aHFpMjdDL0NYZnBER3hIMVY5QmpVTytXVVArd2k2c2VDU21uYzBqRE5odlZuQjl1QjFrUlBkdm4wTVVXa2Jad3JUOFdaU0xJMHlJYzl3SURBUUFC&query=0&msgVer=3&timestamp=1755580644766&signature=uoqIIeRIq4z8BVgMyGx+WwKPR2xyt9U3xqAmj1iVDZfrL4ZTpg7SjzL71PqYy/KGHSxVyiygTonbHasWIhAVADASA3tWbGfUAlopAxq4+SZYW9oRkg2RDmC6YKejJ+fBtbz2Z3yMY3P771gww7gyGPGvR3FG1fFYaINiOpzR3v0=
2025-08-19 13:17:24.779 10805-10805 WebSocket               com.dspread.mdm.service              I  🔧 未检测到系统代理设置
2025-08-19 13:17:24.800 10805-12034 System.out              com.dspread.mdm.service              I  [socket]:check permission begin!
2025-08-19 13:17:25.051 10805-12035 System.out              com.dspread.mdm.service              I  [socket]:check permission begin!
2025-08-19 13:17:26.570 10805-12036 Common                  com.dspread.mdm.service              I  ✅ WebSocket 物理连接成功，等待服务器 hellMsg
2025-08-19 13:17:26.571 10805-12036 WebSocket               com.dspread.mdm.service              I  🔧 ===== 心跳发送器实际配置 =====
2025-08-19 13:17:26.572 10805-12036 WebSocket               com.dspread.mdm.service              I  🔧 实际 PING 发送间隔: 60000ms (60秒)
2025-08-19 13:17:26.573 10805-12036 WebSocket               com.dspread.mdm.service              I  🔧 PING 定时器名称: PingSender
2025-08-19 13:17:26.574 10805-12036 WebSocket               com.dspread.mdm.service              I  🔧 实际 PONG 发送间隔: 0ms (0秒)
2025-08-19 13:17:26.574 10805-12036 WebSocket               com.dspread.mdm.service              I  🔧 PONG 定时器名称: PongSender
2025-08-19 13:17:26.575 10805-12036 WebSocket               com.dspread.mdm.service              I  🔧 ===============================
2025-08-19 13:17:26.610 10805-12036 Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"msg_state":"P","request_time":"1755580536662","data":{"taskList":[{"beginDate":"2024-08-19 05:15:36","taskType":"02","apkName":"Via","endDate":"9999-12-31 23:59:59","appId":"1755580536662","packName":"mark.via","versionName":"6.2.0","taskId":"1755580536662","versionCode":"20250117"}]},"tranCode":"ST001","request_id":"1755580536662ST001","version":"1","serialNo":"01610040202307060227"}
2025-08-19 13:17:26.612 10805-12036 WebSocket               com.dspread.mdm.service              I  🔧 准备发送响应确认 - requestId: 1755580536662ST001, needResponse: true
2025-08-19 13:17:26.617 10805-12036 WebSocket               com.dspread.mdm.service              I  🔧 非重要消息，不缓存: C0000
2025-08-19 13:17:26.620 10805-12036 WebSocket               com.dspread.mdm.service              I  🔧 非重要消息，不缓存: C0000
2025-08-19 13:17:26.621 10805-12036 WebSocket               com.dspread.mdm.service              I  🔧 已发送C0000响应确认: requestId=1755580536662ST001
2025-08-19 13:17:26.622 10805-12036 Task                    com.dspread.mdm.service              D  🔧 处理任务消息: ST001
2025-08-19 13:17:26.623 10805-12036 Task                    com.dspread.mdm.service              I  ℹ️ 更新任务列表: requestId=1755580536662ST001, 任务数量=1
2025-08-19 13:17:26.624 10805-12036 Task                    com.dspread.mdm.service              D  🔧 添加/更新任务: taskId=1755580536662
2025-08-19 13:17:26.626 10805-12036 Task                    com.dspread.mdm.service              D  🔧 保存任务列表到存储: 1 个
2025-08-19 13:17:26.627 10805-12036 Task                    com.dspread.mdm.service              D  🔧 开始处理任务列表，共 1 个任务
2025-08-19 13:17:26.628 10805-12036 Task                    com.dspread.mdm.service              D  🔧 处理任务: taskId=1755580536662, type=02, package=mark.via, apk=Via
2025-08-19 13:17:26.628 10805-12036 Task                    com.dspread.mdm.service              D  🔧 处理卸载任务: 1755580536662
2025-08-19 13:17:26.631 10805-12036 Task                    com.dspread.mdm.service              D  🔧 更新任务状态: taskId=1755580536662, state=D02
2025-08-19 13:17:26.633 10805-12036 Task                    com.dspread.mdm.service              D  🔧 保存任务列表到存储: 1 个
2025-08-19 13:17:26.634 10805-12036 Task                    com.dspread.mdm.service              D  🔧 保存任务列表到存储: 1 个
2025-08-19 13:17:26.635 10805-12036 WebSocket               com.dspread.mdm.service              I  🔧 ST001 主动式上送: task_failed (主动: 19)
2025-08-19 13:17:26.643 10805-12036 WebSocket               com.dspread.mdm.service              I  🔧 消息已缓存: C0108 (缓存数量: 1)
2025-08-19 13:17:26.644 10805-12036 WebSocket               com.dspread.mdm.service              I  🔧 任务结果实时上送: taskId=1755580536662, result=D02 (1)
2025-08-19 13:17:26.657 10805-12036 Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"msg_state":"P","request_time":"1755580524415","data":{"taskList":[{"beginDate":"2024-08-19 05:15:24","taskType":"02","apkName":"OpenVPN for Android","endDate":"9999-12-31 23:59:59","appId":"1755580524415","packName":"de.blinkt.openvpn","versionName":"0.5.36a","taskId":"1755580524415","versionCode":"65"}]},"tranCode":"ST001","request_id":"1755580524415ST001","version":"1","serialNo":"01610040202307060227"}
2025-08-19 13:17:26.659 10805-12036 WebSocket               com.dspread.mdm.service              I  🔧 准备发送响应确认 - requestId: 1755580524415ST001, needResponse: true
2025-08-19 13:17:26.665 10805-12036 WebSocket               com.dspread.mdm.service              I  🔧 非重要消息，不缓存: C0000
2025-08-19 13:17:26.669 10805-12036 WebSocket               com.dspread.mdm.service              I  🔧 非重要消息，不缓存: C0000
2025-08-19 13:17:26.669 10805-12036 WebSocket               com.dspread.mdm.service              I  🔧 已发送C0000响应确认: requestId=1755580524415ST001
2025-08-19 13:17:26.670 10805-10821 System                  com.dspread.mdm.service              W  A resource failed to call close. 
2025-08-19 13:17:26.670 10805-12036 Task                    com.dspread.mdm.service              D  🔧 处理任务消息: ST001
2025-08-19 13:17:26.672 10805-12036 Task                    com.dspread.mdm.service              I  ℹ️ 更新任务列表: requestId=1755580524415ST001, 任务数量=1
2025-08-19 13:17:26.673 10805-12036 Task                    com.dspread.mdm.service              D  🔧 任务按时间插入到位置: 0, taskId=1755580524415
2025-08-19 13:17:26.674 10805-12036 Task                    com.dspread.mdm.service              D  🔧 添加/更新任务: taskId=1755580524415
2025-08-19 13:17:26.675 10805-12036 Task                    com.dspread.mdm.service              D  🔧 发现已完成任务: taskId=1755580536662, result=D02
2025-08-19 13:17:26.675 10805-12036 Task                    com.dspread.mdm.service              D  🔧 上送任务结果: taskId=1755580536662, result=D02
2025-08-19 13:17:26.676 10805-12036 Task                    com.dspread.mdm.service              D  🔧 清理已完成任务: taskId=1755580536662, result=D02
2025-08-19 13:17:26.677 10805-12036 Task                    com.dspread.mdm.service              D  🔧 清理了 1 个已完成任务
2025-08-19 13:17:26.678 10805-12036 Task                    com.dspread.mdm.service              D  🔧 保存任务列表到存储: 1 个
2025-08-19 13:17:26.679 10805-12036 Task                    com.dspread.mdm.service              D  🔧 开始处理任务列表，共 1 个任务
2025-08-19 13:17:26.679 10805-12036 Task                    com.dspread.mdm.service              D  🔧 处理任务: taskId=1755580524415, type=02, package=de.blinkt.openvpn, apk=OpenVPN for Android
2025-08-19 13:17:26.680 10805-12036 Task                    com.dspread.mdm.service              D  🔧 处理卸载任务: 1755580524415
2025-08-19 13:17:26.682 10805-12036 Task                    com.dspread.mdm.service              D  🔧 更新任务状态: taskId=1755580524415, state=D02
2025-08-19 13:17:26.683 10805-12036 Task                    com.dspread.mdm.service              D  🔧 保存任务列表到存储: 1 个
2025-08-19 13:17:26.684 10805-12036 Task                    com.dspread.mdm.service              D  🔧 保存任务列表到存储: 1 个
2025-08-19 13:17:26.685 10805-12036 WebSocket               com.dspread.mdm.service              I  🔧 ST001 主动式上送: task_failed (主动: 20)
2025-08-19 13:17:26.690 10805-12036 WebSocket               com.dspread.mdm.service              I  🔧 消息已缓存: C0108 (缓存数量: 2)
2025-08-19 13:17:26.691 10805-12036 WebSocket               com.dspread.mdm.service              I  🔧 任务结果实时上送: taskId=1755580524415, result=D02 (1)
2025-08-19 13:17:27.052 10805-12036 Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"hellMsg":"hello 01610040202307060227","tranCode":"S0000","version":"1","rebootTime":"03:23:55","serialNo":"01610040202307060227","deviceStatus":"6"}
2025-08-19 13:17:27.054 10805-12036 Common                  com.dspread.mdm.service              I  ✅ 收到服务器 hellMsg，WebSocket 业务连接成功！
2025-08-19 13:17:27.055 10805-12036 Common                  com.dspread.mdm.service              I  ✅ WebSocket 业务连接成功！
2025-08-19 13:17:27.056 10805-12036 WebSocket               com.dspread.mdm.service              I  🔧 业务连接成功，重连计数已重置为 0
2025-08-19 13:17:27.063 10805-12036 Common                  com.dspread.mdm.service              I  ✅ ⏰ 设置任务执行定时器成功，下次执行: 60秒后 (1分钟)
2025-08-19 13:17:27.064 10805-12036 WebSocket               com.dspread.mdm.service              I  🔧 已启动任务执行定时器
2025-08-19 13:17:27.067 10805-12036 Common                  com.dspread.mdm.service              I  ✅ ⏰ 设置心跳定时器成功，下次执行: 60秒后 (1分钟)
2025-08-19 13:17:27.068 10805-12036 WebSocket               com.dspread.mdm.service              I  🔧 已启动心跳定时器
2025-08-19 13:17:27.071 10805-12036 Common                  com.dspread.mdm.service              I  ✅ ⏰ 设置终端信息定时器成功，下次执行: 120秒后 (2分钟)
2025-08-19 13:17:27.072 10805-12036 WebSocket               com.dspread.mdm.service              I  🔧 已启动终端信息上传定时器
2025-08-19 13:17:27.073 10805-12036 WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接成功，检查自身更新任务状态
2025-08-19 13:17:27.076 10805-12036 Receiver                com.dspread.mdm.service              D  🔧 发送广播: com.dspread.mdm.service.CHECK_SELF_UPDATE_STATUS
2025-08-19 13:17:27.077 10805-12036 WebSocket               com.dspread.mdm.service              I  🔧 开始发送缓存消息，数量: 2
2025-08-19 13:17:27.078 10805-12036 WebSocket               com.dspread.mdm.service              I  🔧 C0108 主动式上送: cached_message_resend (主动: 21)
2025-08-19 13:17:27.083 10805-12036 Common                  com.dspread.mdm.service              I  ✅ 缓存消息发送成功: C0108
2025-08-19 13:17:27.585 10805-12036 WebSocket               com.dspread.mdm.service              I  🔧 C0108 主动式上送: cached_message_resend (主动: 22)
2025-08-19 13:17:27.591 10805-12036 Common                  com.dspread.mdm.service              I  ✅ 缓存消息发送成功: C0108
2025-08-19 13:17:28.093 10805-12036 WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接成功
2025-08-19 13:17:28.095 10805-12036 WebSocket               com.dspread.mdm.service              I  🔧 设备状态: 6
2025-08-19 13:17:28.096 10805-12036 WebSocket               com.dspread.mdm.service              I  🔧 检查首次连接标志: isFirstTimeWebSocketConnected=false
2025-08-19 13:17:28.097 10805-12036 WebSocket               com.dspread.mdm.service              I  🔧 非首次连接，跳过初始化信息发送和初始化检查
2025-08-19 13:17:28.098 10805-12036 WebSocket               com.dspread.mdm.service              I  🔧 执行必要的状态同步检查
2025-08-19 13:17:28.099 10805-12036 WebSocket               com.dspread.mdm.service              I  🔧 检查服务更新结果
2025-08-19 13:17:28.101 10805-12036 Task                    com.dspread.mdm.service              I  ℹ️ 本地任务列表长度: 1
2025-08-19 13:17:28.103 10805-12036 Task                    com.dspread.mdm.service              D  🔧 本地任务列表内容: [{"beginDate":"2024-08-19 05:15:24","taskType":"02","apkName":"OpenVPN for Android","endDate":"9999-12-31 23:59:59","appId":"1755580524415","packName":"de.blinkt.openvpn","versionName":"0.5.36a","taskId":"1755580524415","versionCode":"65","request_id":"1755580524415ST001","request_time":"1755580524415","silent_install":"","taskResult":"D02","lastUpdateTime":1755580646684}]
2025-08-19 13:17:28.105 10805-12036 Task                    com.dspread.mdm.service              I  ℹ️ 当前应用版本: versionCode=20, versionName=1.1.01.20250819.DSPREAD.MDM.SERVICE
2025-08-19 13:17:28.106 10805-12036 Task                    com.dspread.mdm.service              I  ℹ️ 任务列表数量: 1
2025-08-19 13:17:28.107 10805-12036 Task                    com.dspread.mdm.service              D  🔧 检查任务[0]: taskId=1755580524415, taskType=02, pkgName=de.blinkt.openvpn, versionCode=65, versionName=0.5.36a, taskResult=D02
2025-08-19 13:17:28.141 10805-12036 WebSocket               com.dspread.mdm.service              I  🔧 检查OS升级状态...
2025-08-19 13:17:28.143 10805-12036 Common                  com.dspread.mdm.service              D  🔧 OsUpdateStatusChecker WebSocket连接成功，检查OS升级状态
2025-08-19 13:17:28.144 10805-12036 Common                  com.dspread.mdm.service              D  🔧 OsUpdateStatusChecker 没有待检查的OS升级任务
2025-08-19 13:17:28.145 10805-12036 WebSocket               com.dspread.mdm.service              I  🔧 OS升级状态检查已触发
2025-08-19 13:17:28.158 10805-12036 Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"request_time":"1755580647287","org_request_time":"1755580646638","org_request_id":"1755580646638C0108","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755580647287S0000","serialNo":"01610040202307060227"}
2025-08-19 13:17:28.160 10805-12036 WebSocket               com.dspread.mdm.service              I  🔧 收到响应消息: transCode=S0000, orgRequestId=1755580646638C0108, state=0, remark=
2025-08-19 13:17:28.161 10805-12036 WebSocket               com.dspread.mdm.service              I  🔧 处理任务结果响应: state=0
2025-08-19 13:17:28.162 10805-12036 WebSocket               com.dspread.mdm.service              I  🔧 任务结果上传成功
2025-08-19 13:17:28.386 10805-12036 Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"request_time":"1755580647962","org_request_time":"1755580646687","org_request_id":"1755580646687C0108","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755580647962S0000","serialNo":"01610040202307060227"}
2025-08-19 13:17:28.387 10805-12036 WebSocket               com.dspread.mdm.service              I  🔧 收到响应消息: transCode=S0000, orgRequestId=1755580646687C0108, state=0, remark=
2025-08-19 13:17:28.388 10805-12036 WebSocket               com.dspread.mdm.service              I  🔧 处理任务结果响应: state=0
2025-08-19 13:17:28.390 10805-12036 WebSocket               com.dspread.mdm.service              I  🔧 任务结果上传成功
2025-08-19 13:17:41.305 10805-12036 Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"msg_state":"P","request_time":"1755580660804","data":{"taskList":[{"apkMd5":"7fa4d472f03df6a4e8b78d182ac221ad","apkName":"service","endDate":"9999-12-31 23:59:59","installBy":"0","versionName":"1.1.02.20250819.DSPREAD.MDM.SERVICE","versionCode":"21","url":"https://smartms.s3.sa-east-1.amazonaws.com/app/ff6dd8d6f56c466e82c2e07cef78be15.apk","beginDate":"2024-08-19 05:17:40","taskType":"01","appId":"a71a933343a548fd81d2d830b95c930b","apkSize":"7838603","appIconUrl":"https://smartms.s3.sa-east-1.amazonaws.com/icon/8697584c22e642dbbeeaeada4c6a0f62ic_launcher.webp","packName":"com.dspread.mdm.service","taskId":"d0b86561002748a6ae2a8122bc9867d4&a71a933343a548fd81d2d830b95c930b"}]},"tranCode":"ST001","request_id":"1755580660804ST001","version":"1","serialNo":"01610040202307060227"}
2025-08-19 13:17:41.307 10805-12036 WebSocket               com.dspread.mdm.service              I  🔧 准备发送响应确认 - requestId: 1755580660804ST001, needResponse: true
2025-08-19 13:17:41.316 10805-12036 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0000","serialNo":"01610040202307060227","request_time":"1755580661311","request_id":"1755580661311C0000","version":"1","org_request_id":"1755580660804ST001","org_request_time":"1755580660804","response_state":"0","myVersionName":"1.1.01.20250819.DSPREAD.MDM.SERVICE","terminalDate":"20250819131741"}
2025-08-19 13:17:41.327 10805-12036 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0000","serialNo":"01610040202307060227","request_time":"1755580661322","request_id":"1755580661322C0000","version":"1","org_request_id":"1755580660804ST001","org_request_time":"1755580660804","response_state":"0","myVersionName":"1.1.01.20250819.DSPREAD.MDM.SERVICE","terminalDate":"20250819131741"}
2025-08-19 13:17:41.328 10805-12036 WebSocket               com.dspread.mdm.service              I  🔧 已发送C0000响应确认: requestId=1755580660804ST001
2025-08-19 13:17:41.330 10805-12036 Task                    com.dspread.mdm.service              D  🔧 处理任务消息: ST001
2025-08-19 13:17:41.331 10805-12036 Task                    com.dspread.mdm.service              I  ℹ️ 更新任务列表: requestId=1755580660804ST001, 任务数量=1
2025-08-19 13:17:41.333 10805-12036 Task                    com.dspread.mdm.service              D  🔧 任务插入到末尾: taskId=d0b86561002748a6ae2a8122bc9867d4&a71a933343a548fd81d2d830b95c930b
2025-08-19 13:17:41.335 10805-12036 Task                    com.dspread.mdm.service              D  🔧 添加/更新任务: taskId=d0b86561002748a6ae2a8122bc9867d4&a71a933343a548fd81d2d830b95c930b
2025-08-19 13:17:41.336 10805-12036 Task                    com.dspread.mdm.service              D  🔧 发现已完成任务: taskId=1755580524415, result=D02
2025-08-19 13:17:41.337 10805-12036 Task                    com.dspread.mdm.service              D  🔧 上送任务结果: taskId=1755580524415, result=D02
2025-08-19 13:17:41.338 10805-12036 Task                    com.dspread.mdm.service              D  🔧 清理已完成任务: taskId=1755580524415, result=D02
2025-08-19 13:17:41.339 10805-12036 Task                    com.dspread.mdm.service              D  🔧 清理了 1 个已完成任务
2025-08-19 13:17:41.341 10805-12036 Task                    com.dspread.mdm.service              D  🔧 保存任务列表到存储: 1 个
2025-08-19 13:17:41.343 10805-12036 Task                    com.dspread.mdm.service              D  🔧 开始处理任务列表，共 1 个任务
2025-08-19 13:17:41.344 10805-12036 Task                    com.dspread.mdm.service              D  🔧 处理任务: taskId=d0b86561002748a6ae2a8122bc9867d4&a71a933343a548fd81d2d830b95c930b, type=01, package=com.dspread.mdm.service, apk=service
2025-08-19 13:17:41.345 10805-12036 Task                    com.dspread.mdm.service              D  🔧 处理安装/更新任务: d0b86561002748a6ae2a8122bc9867d4&a71a933343a548fd81d2d830b95c930b
2025-08-19 13:17:41.346 10805-12036 Task                    com.dspread.mdm.service              D  🔧 执行安装任务: service (com.dspread.mdm.service)
2025-08-19 13:17:41.347 10805-12036 Task                    com.dspread.mdm.service              D  🔧 版本信息: 1.1.02.20250819.DSPREAD.MDM.SERVICE(21)
2025-08-19 13:17:41.348 10805-12036 Task                    com.dspread.mdm.service              D  🔧 允许降级: false, 强制安装: false
2025-08-19 13:17:41.349 10805-12036 Task                    com.dspread.mdm.service              D  🔧 判断 packageName=com.dspread.mdm.service versionCode=21 versionName=1.1.02.20250819.DSPREAD.MDM.SERVICE 是否安装
2025-08-19 13:17:41.351 10805-12036 Task                    com.dspread.mdm.service              D  🔧 已安装版本: versionCode=20, versionName=1.1.01.20250819.DSPREAD.MDM.SERVICE
2025-08-19 13:17:41.352 10805-12036 Task                    com.dspread.mdm.service              D  🔧 目标版本: versionCode=21, versionName=1.1.02.20250819.DSPREAD.MDM.SERVICE
2025-08-19 13:17:41.353 10805-12036 Task                    com.dspread.mdm.service              D  🔧 com.dspread.mdm.service 需要安装/更新（当前版本较低或版本名不匹配）
2025-08-19 13:17:41.354 10805-12036 Task                    com.dspread.mdm.service              D  🔧 应用安装状态检查结果: 0
2025-08-19 13:17:41.355 10805-12036 Task                    com.dspread.mdm.service              D  🔧 应用未安装或版本较低，继续安装流程
2025-08-19 13:17:41.356 10805-12036 Task                    com.dspread.mdm.service              D  🔧 开始下载和安装应用: service
2025-08-19 13:17:41.357 10805-12036 Task                    com.dspread.mdm.service              D  🔧 下载URL: https://smartms.s3.sa-east-1.amazonaws.com/app/ff6dd8d6f56c466e82c2e07cef78be15.apk
2025-08-19 13:17:41.359 10805-12036 Task                    com.dspread.mdm.service              D  🔧 包名: com.dspread.mdm.service, 版本: 1.1.02.20250819.DSPREAD.MDM.SERVICE(21)
2025-08-19 13:17:41.360 10805-12036 Task                    com.dspread.mdm.service              D  🔧 判断 packageName=com.dspread.mdm.service versionCode=21 versionName=1.1.02.20250819.DSPREAD.MDM.SERVICE 是否安装
2025-08-19 13:17:41.361 10805-12036 Task                    com.dspread.mdm.service              D  🔧 已安装版本: versionCode=20, versionName=1.1.01.20250819.DSPREAD.MDM.SERVICE
2025-08-19 13:17:41.362 10805-12036 Task                    com.dspread.mdm.service              D  🔧 目标版本: versionCode=21, versionName=1.1.02.20250819.DSPREAD.MDM.SERVICE
2025-08-19 13:17:41.363 10805-12036 Task                    com.dspread.mdm.service              D  🔧 com.dspread.mdm.service 需要安装/更新（当前版本较低或版本名不匹配）
2025-08-19 13:17:41.364 10805-12036 Task                    com.dspread.mdm.service              D  🔧 检查应用安装状态: com.dspread.mdm.service = 0
2025-08-19 13:17:41.365 10805-12036 Task                    com.dspread.mdm.service              D  🔧 继续安装流程: status=0
2025-08-19 13:17:41.367 10805-12036 Task                    com.dspread.mdm.service              D  🔧 更新任务状态: taskId=d0b86561002748a6ae2a8122bc9867d4&a71a933343a548fd81d2d830b95c930b, state=A01
2025-08-19 13:17:41.368 10805-12036 Task                    com.dspread.mdm.service              D  🔧 保存任务列表到存储: 1 个
2025-08-19 13:17:41.370 10805-12036 Task                    com.dspread.mdm.service              D  🔧 保存任务列表到存储: 1 个
2025-08-19 13:17:41.371 10805-12036 WebSocket               com.dspread.mdm.service              I  🔧 ST001 主动式上送: task_failed (主动: 23)
2025-08-19 13:17:41.379 10805-12036 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0108","serialNo":"01610040202307060227","request_time":"1755580661374","request_id":"1755580661374C0108","version":"1","data":{"taskId":"d0b86561002748a6ae2a8122bc9867d4&a71a933343a548fd81d2d830b95c930b","taskResult":"A01","appId":"a71a933343a548fd81d2d830b95c930b"},"myVersionName":"1.1.01.20250819.DSPREAD.MDM.SERVICE","terminalDate":"20250819131741","org_request_id":"1755580660804ST001","org_request_time":"1755580660804"}
2025-08-19 13:17:41.380 10805-12036 WebSocket               com.dspread.mdm.service              I  🔧 任务结果实时上送: taskId=d0b86561002748a6ae2a8122bc9867d4&a71a933343a548fd81d2d830b95c930b, result=A01 (1)
2025-08-19 13:17:41.387 10805-12036 Task                    com.dspread.mdm.service              D  🔧 原始taskId: d0b86561002748a6ae2a8122bc9867d4&a71a933343a548fd81d2d830b95c930b
2025-08-19 13:17:41.388 10805-12036 Task                    com.dspread.mdm.service              D  🔧 清理后taskId: d0b86561002748a6ae2a8122bc9867d4_a71a933343a548fd81d2d830b95c930b
2025-08-19 13:17:41.388 10805-12036 Task                    com.dspread.mdm.service              D  🔧 清理后APK路径: /storage/emulated/0/Android/data/com.dspread.mdm.service/files/apk/d0b86561002748a6ae2a8122bc9867d4_a71a933343a548fd81d2d830b95c930b.apk
2025-08-19 13:17:41.389 10805-12036 Task                    com.dspread.mdm.service              D  🔧 APK文件名: d0b86561002748a6ae2a8122bc9867d4_a71a933343a548fd81d2d830b95c930b.apk
2025-08-19 13:17:41.391 10805-12041 Platform                com.dspread.mdm.service              D  🔧 WakeLockManager WakeLock不存在或未持有: download_d0b86561002748a6ae2a8122bc9867d4&a71a933343a548fd81d2d830b95c930b
2025-08-19 13:17:41.396 10805-12041 Platform                com.dspread.mdm.service              D  🔧 WakeLockManager 获取WakeLock成功: download_d0b86561002748a6ae2a8122bc9867d4&a71a933343a548fd81d2d830b95c930b (超时: 600000ms)
2025-08-19 13:17:41.397 10805-12041 HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 尝试下载，第1次
2025-08-19 13:17:41.398 10805-12041 HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 开始下载
2025-08-19 13:17:41.399 10805-12041 HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader URL: https://smartms.s3.sa-east-1.amazonaws.com/app/ff6dd8d6f56c466e82c2e07cef78be15.apk
2025-08-19 13:17:41.400 10805-12041 HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 路径: /storage/emulated/0/Android/data/com.dspread.mdm.service/files/apk/d0b86561002748a6ae2a8122bc9867d4_a71a933343a548fd81d2d830b95c930b.apk
2025-08-19 13:17:41.402 10805-12041 HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 本地文件不存在，创建文件
2025-08-19 13:17:41.404 10805-12041 HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader allFileLength=7838603, 需要从服务器获取文件大小
2025-08-19 13:17:41.408 10805-12041 System.out              com.dspread.mdm.service              I  [okhttp]:check permission begin!
2025-08-19 13:17:41.408 10805-12041 System.out              com.dspread.mdm.service              I  [okhttp]:not MMS!
2025-08-19 13:17:41.408 10805-12041 System.out              com.dspread.mdm.service              I  [okhttp]:not Email!
2025-08-19 13:17:41.691 10805-12041 System.out              com.dspread.mdm.service              I  [socket]:check permission begin!
2025-08-19 13:17:42.208 10805-12036 Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"request_time":"1755580661897","org_request_time":"1755580661374","org_request_id":"1755580661374C0108","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755580661897S0000","serialNo":"01610040202307060227"}
2025-08-19 13:17:42.210 10805-12036 WebSocket               com.dspread.mdm.service              I  🔧 收到响应消息: transCode=S0000, orgRequestId=1755580661374C0108, state=0, remark=
2025-08-19 13:17:42.211 10805-12036 WebSocket               com.dspread.mdm.service              I  🔧 处理任务结果响应: state=0
2025-08-19 13:17:42.212 10805-12036 WebSocket               com.dspread.mdm.service              I  🔧 任务结果上传成功
2025-08-19 13:17:42.554 10805-12041 System.out              com.dspread.mdm.service              I  [OkHttp] sendRequest>>
2025-08-19 13:17:42.555 10805-12041 System.out              com.dspread.mdm.service              I  [OkHttp] sendRequest<<
2025-08-19 13:17:43.533 10805-12041 HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader HTTP响应码: 200
2025-08-19 13:17:43.535 10805-12041 HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 服务器返回Content-Length: 7838603
2025-08-19 13:17:43.536 10805-12041 HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 完整下载模式
2025-08-19 13:17:43.537 10805-12041 HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 使用调用方提供的文件大小: 7838603
2025-08-19 13:17:43.539 10805-12041 HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader localFile = true /storage/emulated/0/Android/data/com.dspread.mdm.service/files/apk/d0b86561002748a6ae2a8122bc9867d4_a71a933343a548fd81d2d830b95c930b.apk, 追加模式: false
2025-08-19 13:17:43.945 10805-12041 Task                    com.dspread.mdm.service              D  🔧 应用下载进度: 0%
2025-08-19 13:17:50.018 10805-12041 Task                    com.dspread.mdm.service              D  🔧 应用下载进度: 10%
2025-08-19 13:17:53.292 10805-12041 Task                    com.dspread.mdm.service              D  🔧 应用下载进度: 20%
2025-08-19 13:17:56.598 10805-12041 Task                    com.dspread.mdm.service              D  🔧 应用下载进度: 30%
2025-08-19 13:18:00.061 10805-12041 Task                    com.dspread.mdm.service              D  🔧 应用下载进度: 40%
2025-08-19 13:18:03.725 10805-12041 Task                    com.dspread.mdm.service              D  🔧 应用下载进度: 50%
2025-08-19 13:18:06.618 10805-12041 Task                    com.dspread.mdm.service              D  🔧 应用下载进度: 60%
2025-08-19 13:18:09.887 10805-12041 Task                    com.dspread.mdm.service              D  🔧 应用下载进度: 70%
2025-08-19 13:18:13.539 10805-12041 Task                    com.dspread.mdm.service              D  🔧 应用下载进度: 80%
2025-08-19 13:18:16.838 10805-12041 Task                    com.dspread.mdm.service              D  🔧 应用下载进度: 90%
2025-08-19 13:18:20.129 10805-12041 Task                    com.dspread.mdm.service              D  🔧 应用下载进度: 100%
2025-08-19 13:18:20.131 10805-12041 HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 计算MD5: d0b86561002748a6ae2a8122bc9867d4_a71a933343a548fd81d2d830b95c930b.apk
2025-08-19 13:18:20.233 10805-12041 HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 文件: 7838603 MD5: 7fa4d472f03df6a4e8b78d182ac221ad
2025-08-19 13:18:20.234 10805-12041 HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader MD5校验正确
2025-08-19 13:18:20.234 10805-12041 HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 清理连接资源
2025-08-19 13:18:20.235 10805-12041 Task                    com.dspread.mdm.service              D  🔧 APK下载成功: /storage/emulated/0/Android/data/com.dspread.mdm.service/files/apk/d0b86561002748a6ae2a8122bc9867d4_a71a933343a548fd81d2d830b95c930b.apk
2025-08-19 13:18:20.236 10805-12041 Task                    com.dspread.mdm.service              D  🔧 更新任务状态: taskId=d0b86561002748a6ae2a8122bc9867d4&a71a933343a548fd81d2d830b95c930b, state=A03
2025-08-19 13:18:20.238 10805-12041 Task                    com.dspread.mdm.service              D  🔧 保存任务列表到存储: 1 个
2025-08-19 13:18:20.239 10805-12041 Task                    com.dspread.mdm.service              D  🔧 保存任务列表到存储: 1 个
2025-08-19 13:18:20.240 10805-12041 WebSocket               com.dspread.mdm.service              I  🔧 ST001 主动式上送: task_failed (主动: 24)
2025-08-19 13:18:20.248 10805-12041 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0108","serialNo":"01610040202307060227","request_time":"1755580700243","request_id":"1755580700243C0108","version":"1","data":{"taskId":"d0b86561002748a6ae2a8122bc9867d4&a71a933343a548fd81d2d830b95c930b","taskResult":"A03","appId":"a71a933343a548fd81d2d830b95c930b"},"myVersionName":"1.1.01.20250819.DSPREAD.MDM.SERVICE","terminalDate":"20250819131820","org_request_id":"1755580660804ST001","org_request_time":"1755580660804"}
2025-08-19 13:18:20.249 10805-12041 WebSocket               com.dspread.mdm.service              I  🔧 任务结果实时上送: taskId=d0b86561002748a6ae2a8122bc9867d4&a71a933343a548fd81d2d830b95c930b, result=A03 (1)
2025-08-19 13:18:20.250 10805-12041 Task                    com.dspread.mdm.service              D  🔧 设备空闲状态: true
2025-08-19 13:18:20.250 10805-12041 Task                    com.dspread.mdm.service              D  🔧 设备空闲或强制安装，立即开始安装
2025-08-19 13:18:20.251 10805-12041 Platform                com.dspread.mdm.service              D  🔧 WakeLockManager WakeLock不存在或未持有: install_d0b86561002748a6ae2a8122bc9867d4&a71a933343a548fd81d2d830b95c930b
2025-08-19 13:18:20.254 10805-12041 Platform                com.dspread.mdm.service              D  🔧 WakeLockManager 获取WakeLock成功: install_d0b86561002748a6ae2a8122bc9867d4&a71a933343a548fd81d2d830b95c930b (超时: 300000ms)
2025-08-19 13:18:20.254 10805-12041 Task                    com.dspread.mdm.service              D  🔧 判断 packageName=com.dspread.mdm.service versionCode=21 versionName=1.1.02.20250819.DSPREAD.MDM.SERVICE 是否安装
2025-08-19 13:18:20.255 10805-12041 Task                    com.dspread.mdm.service              D  🔧 已安装版本: versionCode=20, versionName=1.1.01.20250819.DSPREAD.MDM.SERVICE
2025-08-19 13:18:20.256 10805-12041 Task                    com.dspread.mdm.service              D  🔧 目标版本: versionCode=21, versionName=1.1.02.20250819.DSPREAD.MDM.SERVICE
2025-08-19 13:18:20.257 10805-12041 Task                    com.dspread.mdm.service              D  🔧 com.dspread.mdm.service 需要安装/更新（当前版本较低或版本名不匹配）
2025-08-19 13:18:20.257 10805-12041 Task                    com.dspread.mdm.service              D  🔧 检查应用安装状态: com.dspread.mdm.service = 0
2025-08-19 13:18:20.258 10805-12041 Task                    com.dspread.mdm.service              D  🔧 继续安装流程: status=0
2025-08-19 13:18:20.259 10805-12041 Task                    com.dspread.mdm.service              D  🔧 更新任务状态: taskId=d0b86561002748a6ae2a8122bc9867d4&a71a933343a548fd81d2d830b95c930b, state=B02
2025-08-19 13:18:20.260 10805-12041 Task                    com.dspread.mdm.service              D  🔧 保存任务列表到存储: 1 个
2025-08-19 13:18:20.262 10805-12041 Task                    com.dspread.mdm.service              D  🔧 保存任务列表到存储: 1 个
2025-08-19 13:18:20.263 10805-12041 WebSocket               com.dspread.mdm.service              I  🔧 ST001 主动式上送: task_failed (主动: 25)
2025-08-19 13:18:20.269 10805-12041 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0108","serialNo":"01610040202307060227","request_time":"1755580700265","request_id":"1755580700265C0108","version":"1","data":{"taskId":"d0b86561002748a6ae2a8122bc9867d4&a71a933343a548fd81d2d830b95c930b","taskResult":"B02","appId":"a71a933343a548fd81d2d830b95c930b"},"myVersionName":"1.1.01.20250819.DSPREAD.MDM.SERVICE","terminalDate":"20250819131820","org_request_id":"1755580660804ST001","org_request_time":"1755580660804"}
2025-08-19 13:18:20.270 10805-12041 WebSocket               com.dspread.mdm.service              I  🔧 任务结果实时上送: taskId=d0b86561002748a6ae2a8122bc9867d4&a71a933343a548fd81d2d830b95c930b, result=B02 (1)
2025-08-19 13:18:20.271 10805-12041 Task                    com.dspread.mdm.service              D  🔧 开始安装APK: /storage/emulated/0/Android/data/com.dspread.mdm.service/files/apk/d0b86561002748a6ae2a8122bc9867d4_a71a933343a548fd81d2d830b95c930b.apk
2025-08-19 13:18:20.272 10805-12041 AppManager              com.dspread.mdm.service              I  ℹ️ 创建SessionCallback
2025-08-19 13:18:20.273 10805-12041 AppManager              com.dspread.mdm.service              I  ℹ️ 注册SessionCallback
2025-08-19 13:18:20.326 10805-12041 AppManager              com.dspread.mdm.service              I  ℹ️ 准备安装: com.dspread.mdm.service
2025-08-19 13:18:20.370 10805-12041 AppManager              com.dspread.mdm.service              I  ℹ️ APK验证通过: com.dspread.mdm.service v1.1.02.20250819.DSPREAD.MDM.SERVICE(21) 7654KB
2025-08-19 13:18:20.374 10805-12041 AppManager              com.dspread.mdm.service              I  ℹ️ 创建Session: sessionId=1672002828
2025-08-19 13:18:20.640 10805-12041 AppManager              com.dspread.mdm.service              I  ℹ️ PackageInstaller installation committed: sessionId=1672002828
2025-08-19 13:18:20.642 10805-12041 Platform                com.dspread.mdm.service              D  🔧 WakeLockManager 释放WakeLock成功: download_d0b86561002748a6ae2a8122bc9867d4&a71a933343a548fd81d2d830b95c930b
2025-08-19 13:18:20.670 10805-10805 AppManager              com.dspread.mdm.service              I  ℹ️ 安装进度: 90%
2025-08-19 13:18:20.733   977-1006  ActivityManager         system_server                        I  Force stopping com.dspread.mdm.service appid=1000 user=-1: installPackageLI
2025-08-19 13:18:20.733   977-1006  ActivityManager         system_server                        I  Killing 10805:com.dspread.mdm.service/1000 (adj 200): stop com.dspread.mdm.service due to installPackageLI
2025-08-19 13:18:20.736   977-1030  PackageManager          system_server                        I  Update package com.dspread.mdm.service code path from /data/app/~~9XnMBI_ibMnvvJkYasl3sQ==/com.dspread.mdm.service-fAfCdlnXgWcUtux6ziDkHA== to /data/app/~~iNA98PmJluCbOB6q7ZCWJQ==/com.dspread.mdm.service-MSzvu8A-jCbT6HMELpaopA==; Retain data and using new
2025-08-19 13:18:20.737   977-1030  PackageManager          system_server                        I  Update package com.dspread.mdm.service resource path from /data/app/~~9XnMBI_ibMnvvJkYasl3sQ==/com.dspread.mdm.service-fAfCdlnXgWcUtux6ziDkHA== to /data/app/~~iNA98PmJluCbOB6q7ZCWJQ==/com.dspread.mdm.service-MSzvu8A-jCbT6HMELpaopA==; Retain data and using new
2025-08-19 13:18:20.738   398-452   libPowerHal             ven...hardware.mtkpower@1.0-service  I  [perfNotifyAppState] pack:com.dspread.mdm.service, pid:10805, STATE_DEAD
2025-08-19 13:18:20.765   977-1682  VirtualDisplayAdapter   system_server                        I  Virtual display device released because application token died: com.dspread.mdm.service
2025-08-19 13:18:20.767   977-995   DisplayManagerService   system_server                        I  Display device removed: DisplayDeviceInfo{"ScreenCapture": uniqueId="virtual:com.dspread.mdm.service,1000,ScreenCapture,0", 720 x 1600, modeId 2, defaultModeId 2, supportedModes [{id=2, width=720, height=1600, fps=60.0}], colorMode 0, supportedColorModes [0], HdrCapabilities null, allmSupported false, gameContentTypeSupported false, density 320, 320.0 x 320.0 dpi, appVsyncOff 0, presDeadline 16666666, touch NONE, rotation 0, type VIRTUAL, deviceProductInfo null, state ON, owner com.dspread.mdm.service (uid 1000), FLAG_PRIVATE, FLAG_PRESENTATION}
2025-08-19 13:18:20.907   977-1030  ActivityManager         system_server                        I  Force stopping com.dspread.mdm.service appid=1000 user=0: pkg removed
2025-08-19 13:18:20.914  1707-1707  MediaProvider           com.android.providers.media.module   I  Invalidating LocalCallingIdentity cache for package com.dspread.mdm.service. Reason: package android.intent.action.PACKAGE_REMOVED
2025-08-19 13:18:20.919   977-1006  BroadcastQueue          system_server                        W  Background execution not allowed: receiving Intent { act=android.intent.action.PACKAGE_REMOVED dat=package:com.dspread.mdm.service flg=0x4000010 (has extras) } to com.android.gallery3d/.app.PackagesMonitor
2025-08-19 13:18:20.928   977-1006  BroadcastQueue          system_server                        W  Background execution not allowed: receiving Intent { act=android.intent.action.PACKAGE_REMOVED dat=package:com.dspread.mdm.service flg=0x4000010 (has extras) } to com.android.musicfx/.Compatibility$Receiver
2025-08-19 13:18:20.936  1707-1707  MediaProvider           com.android.providers.media.module   I  Invalidating LocalCallingIdentity cache for package com.dspread.mdm.service. Reason: package android.intent.action.PACKAGE_ADDED
2025-08-19 13:18:20.945   977-1006  BroadcastQueue          system_server                        W  Background execution not allowed: receiving Intent { act=android.intent.action.PACKAGE_ADDED dat=package:com.dspread.mdm.service flg=0x4000010 (has extras) } to com.android.packageinstaller/.PackageInstalledReceiver
2025-08-19 13:18:20.946   977-1006  BroadcastQueue          system_server                        W  Background execution not allowed: receiving Intent { act=android.intent.action.PACKAGE_ADDED dat=package:com.dspread.mdm.service flg=0x4000010 (has extras) } to com.android.gallery3d/.app.PackagesMonitor
2025-08-19 13:18:20.948   977-1006  BroadcastQueue          system_server                        W  Background execution not allowed: receiving Intent { act=android.intent.action.PACKAGE_ADDED dat=package:com.dspread.mdm.service flg=0x4000010 (has extras) } to com.android.musicfx/.Compatibility$Receiver
2025-08-19 13:18:20.989   977-1006  BroadcastQueue          system_server                        W  Background execution not allowed: receiving Intent { act=android.intent.action.PACKAGE_REPLACED dat=package:com.dspread.mdm.service flg=0x4000010 (has extras) } to com.android.musicfx/.Compatibility$Receiver
2025-08-19 13:18:20.993  1251-1251  ImsResolver             com.android.phone                    D  maybeAddedImsService, packageName: com.dspread.mdm.service
2025-08-19 13:18:21.026  1482-1510  Launcher.IconCache      com.android.launcher3                D  isNeedHideAppIconsPkg   :  8com.dspread.mdm.service
2025-08-19 13:18:21.027  1482-1510  Launcher.IconCache      com.android.launcher3                D  isNeedHideAppIconsPkg   :  8com.dspread.mdm.service
2025-08-19 13:18:42.579   977-1007  ActivityManager         system_server                        I  Start proc 12049:com.dspread.mdm.service/1000 for broadcast {com.dspread.mdm.service/com.dspread.mdm.service.broadcast.receivers.GeofenceReceiver}
2025-08-19 13:18:42.703 12049-12049 ead.mdm.service         com.dspread.mdm.service              W  type=1400 audit(0.0:532): avc: denied { write } for name="com.dspread.mdm.service-MSzvu8A-jCbT6HMELpaopA==" dev="dm-6" ino=10068 scontext=u:r:system_app:s0 tcontext=u:object_r:apk_data_file:s0 tclass=dir permissive=0
2025-08-19 13:18:43.154 12049-12049 NetworkSecurityConfig   com.dspread.mdm.service              D  Using Network Security Config from resource network_security_config debugBuild: true
2025-08-19 13:18:43.158 12049-12049 NetworkSecurityConfig   com.dspread.mdm.service              D  Using Network Security Config from resource network_security_config debugBuild: true
2025-08-19 13:18:43.269 12049-12049 Common                  com.dspread.mdm.service              D  🔧 日志配置：测试环境模式
2025-08-19 13:18:43.274 12049-12049 Common                  com.dspread.mdm.service              D  🔧 [DebugConfig] 调试模式已启用
2025-08-19 13:18:43.276 12049-12049 Common                  com.dspread.mdm.service              D  🔧 SmartMdmServiceApp: 统一配置管理器初始化完成
2025-08-19 13:18:43.278 12049-12049 Common                  com.dspread.mdm.service              D  🔧 SmartMdmServiceApp: 开始初始化系统目录...
2025-08-19 13:18:43.280 12049-12049 Common                  com.dspread.mdm.service              D  🔧 SmartMdmServiceApp: 配置目录已存在 - /data/pos/config
2025-08-19 13:18:43.282 12049-12049 Common                  com.dspread.mdm.service              D  🔧 SmartMdmServiceApp: Logo目录已存在 - /data/data/com.dspread.mdm.service/files/media/logo
2025-08-19 13:18:43.285 12049-12049 Common                  com.dspread.mdm.service              D  🔧 SmartMdmServiceApp: 开机动画目录已存在 - /data/data/com.dspread.mdm.service/files/media/anim
2025-08-19 13:18:43.287 12049-12049 Common                  com.dspread.mdm.service              D  🔧 SmartMdmServiceApp: 系统目录初始化完成
2025-08-19 13:18:43.293 12049-12049 Platform                com.dspread.mdm.service              D  🔧 NetworkTrafficMonitor 初始化网络流量监控器
2025-08-19 13:18:43.302 12049-12049 Platform                com.dspread.mdm.service              D  🔧 NetworkTrafficMonitor 加载已保存的流量数据: 0B
2025-08-19 13:18:43.361 12049-12049 Platform                com.dspread.mdm.service              D  🔧 NetworkTrafficMonitor 每日上送任务已启动
2025-08-19 13:18:43.364 12049-12049 Platform                com.dspread.mdm.service              D  🔧 NetworkTrafficMonitor 网络流量监控器初始化完成
2025-08-19 13:18:43.371 12049-12049 Receiver                com.dspread.mdm.service              D  🔧 ServiceGuardEventHandler 启动服务守护定时器: 120s
2025-08-19 13:18:43.379 12049-12049 Common                  com.dspread.mdm.service              I  ✅ ⏰ 设置服务守护定时器成功，下次执行: 120秒后 (2分钟)
2025-08-19 13:18:43.381 12049-12049 Common                  com.dspread.mdm.service              D  🔧 SmartMdmServiceApp: 服务守护定时器启动成功
2025-08-19 13:18:43.385 12049-12049 Common                  com.dspread.mdm.service              D  🔧 ServiceStartupManager 收到服务启动请求: APPLICATION_STARTUP, 强制启动: false
2025-08-19 13:18:43.391 12049-12049 Common                  com.dspread.mdm.service              D  🔧 ServiceStartupManager 开始执行服务启动: APPLICATION_STARTUP
2025-08-19 13:18:43.393 12049-12049 Common                  com.dspread.mdm.service              D  🔧 ServiceManager: 准备启动后台服务
2025-08-19 13:18:43.396 12049-12049 ContextImpl             com.dspread.mdm.service              W  Calling a method in the system process without a qualified user: android.app.ContextImpl.startService:1669 android.content.ContextWrapper.startService:720 com.dspread.mdm.service.platform.manager.ServiceManager.startBackgroundService:20 com.dspread.mdm.service.platform.manager.ServiceStartupManager.performServiceStart:121 com.dspread.mdm.service.platform.manager.ServiceStartupManager.startService:66 
2025-08-19 13:18:43.400 12049-12049 Common                  com.dspread.mdm.service              D  🔧 ServiceManager: 后台服务启动成功，ComponentName: ComponentInfo{com.dspread.mdm.service/com.dspread.mdm.service.services.SmartMdmBackgroundService}
2025-08-19 13:18:44.446 12049-12049 Common                  com.dspread.mdm.service              D  🔧 ServiceStartupManager 服务启动验证成功: APPLICATION_STARTUP
2025-08-19 13:18:44.450 12049-12049 Common                  com.dspread.mdm.service              D  🔧 ServiceStartupManager 服务启动成功: APPLICATION_STARTUP
2025-08-19 13:18:44.453 12049-12049 Common                  com.dspread.mdm.service              D  🔧 SmartMdmServiceApp: 主服务启动请求已提交
2025-08-19 13:18:44.455 12049-12049 Common                  com.dspread.mdm.service              D  🔧 SmartMdmServiceApp: Application启动完成，包名: com.dspread.mdm.service
2025-08-19 13:18:44.472   977-12044 BroadcastQueue          system_server                        I  Delay finish: com.dspread.mdm.service/.broadcast.receivers.GeofenceReceiver
2025-08-19 13:18:44.472 12049-12049 Choreographer           com.dspread.mdm.service              I  Skipped 76 frames!  The application may be doing too much work on its main thread.
2025-08-19 13:18:44.480 12049-12049 Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 服务启动
2025-08-19 13:18:44.490 12049-12049 setContentIntent        com.dspread.mdm.service              D  packageName = com.dspread.mdm.service
2025-08-19 13:18:44.496   977-1006  NotificationService     system_server                        E  enqueueNotificationInternal pkg: com.dspread.mdm.service
2025-08-19 13:18:44.497 12049-12049 Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 开始服务初始化
2025-08-19 13:18:44.499 12049-12049 Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] Android < 14: 启动Provisioning服务
2025-08-19 13:18:44.503 12049-12049 Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 服务创建完成
2025-08-19 13:18:44.504 12049-12082 Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 启动ProvisioningService
2025-08-19 13:18:44.507 12049-12049 Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] onStartCommand调用
2025-08-19 13:18:44.514 12049-12049 Platform                com.dspread.mdm.service              D  🔧 WakeLockManager 获取智能WakeLock成功，持续时间: 60000ms
2025-08-19 13:18:44.515 12049-12082 Provisioning            com.dspread.mdm.service              D  🔧 主目录 已存在: /data/pos/config
2025-08-19 13:18:44.517 12049-12082 Provisioning            com.dspread.mdm.service              I  ℹ️ 使用主配置目录: /data/pos/config/
2025-08-19 13:18:44.520 12049-12082 Provisioning            com.dspread.mdm.service              I  ℹ️ 配置路径初始化完成: 系统目录
2025-08-19 13:18:44.520 12049-12049 Platform                com.dspread.mdm.service              D  🔧 WakeLockManager 设置WakeLock续期定时器: 60000ms
2025-08-19 13:18:44.525 12049-12049 Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 初始化基础组件
2025-08-19 13:18:44.528 12049-12049 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 开始初始化...
2025-08-19 13:18:44.530 12049-12082 Provisioning            com.dspread.mdm.service              D  🔧 状态标志加载成功: /data/pos/config/provisioning_flags.json
2025-08-19 13:18:44.536 12049-12082 Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] ProvisioningService启动完成
2025-08-19 13:18:44.536 12049-12079 Provisioning            com.dspread.mdm.service              I  ℹ️ 开始执行Provisioning配置，触发类型: FIRST_BOOT
2025-08-19 13:18:44.536 12049-12049 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 包更新广播接收器注册完成
2025-08-19 13:18:44.538 12049-12049 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 统一广播接收器注册完成
2025-08-19 13:18:44.539 12049-12079 Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] Provisioning进度: 正在下载配置文件...
2025-08-19 13:18:44.540 12049-12049 Common                  com.dspread.mdm.service              I  ✅ BroadcastManager 初始化完成
2025-08-19 13:18:44.541 12049-12079 Provisioning            com.dspread.mdm.service              I  ℹ️ 开始请求远程配置...
2025-08-19 13:18:44.544 12049-12049 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: NetworkEventHandler -> android.net.conn.CONNECTIVITY_CHANGE
2025-08-19 13:18:44.547 12049-12049 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: NetworkEventHandler -> android.net.wifi.STATE_CHANGE
2025-08-19 13:18:44.551 12049-12049 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: BatteryEventHandler -> android.intent.action.BATTERY_CHANGED
2025-08-19 13:18:44.554 12049-12049 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: BatteryEventHandler -> android.intent.action.BATTERY_LOW
2025-08-19 13:18:44.555 12049-12079 Provisioning            com.dspread.mdm.service              I  ℹ️ 请求配置: https://config.dspreadserv.net/status/config?SN=MDE2MTAwNDAyMDIzMDcwNjAyMjc=&timestamp=1755580724555
2025-08-19 13:18:44.556 12049-12049 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: BatteryEventHandler -> android.intent.action.BATTERY_OKAY
2025-08-19 13:18:44.557 12049-12049 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: BatteryEventHandler -> android.intent.action.ACTION_POWER_CONNECTED
2025-08-19 13:18:44.561 12049-12049 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: BatteryEventHandler -> android.intent.action.ACTION_POWER_DISCONNECTED
2025-08-19 13:18:44.565 12049-12049 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: SystemEventHandler -> android.intent.action.BOOT_COMPLETED
2025-08-19 13:18:44.567 12049-12079 System.out              com.dspread.mdm.service              I  [okhttp]:check permission begin!
2025-08-19 13:18:44.567 12049-12049 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: SystemEventHandler -> android.intent.action.QUICKBOOT_POWERON
2025-08-19 13:18:44.567 12049-12079 System                  com.dspread.mdm.service              W  ClassLoader referenced unknown path: system/framework/mediatek-cta.jar
2025-08-19 13:18:44.569 12049-12079 System.out              com.dspread.mdm.service              I  [okhttp] e:java.lang.ClassNotFoundException: com.mediatek.cta.CtaUtils
2025-08-19 13:18:44.570 12049-12049 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: SystemEventHandler -> android.intent.action.LOCKED_BOOT_COMPLETED
2025-08-19 13:18:44.572 12049-12049 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: SystemEventHandler -> android.intent.action.ACTION_SHUTDOWN
2025-08-19 13:18:44.575 12049-12049 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: SystemEventHandler -> android.intent.action.REBOOT
2025-08-19 13:18:44.578 12049-12049 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: ScreenEventHandler -> android.intent.action.SCREEN_ON
2025-08-19 13:18:44.581 12049-12049 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: ScreenEventHandler -> android.intent.action.SCREEN_OFF
2025-08-19 13:18:44.585 12049-12049 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: HeartbeatEventHandler -> com.dspread.mdm.service.POLL_TIMER_START
2025-08-19 13:18:44.592 12049-12049 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: PackageUpdateEventHandler -> android.intent.action.PACKAGE_ADDED
2025-08-19 13:18:44.594 12049-12049 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: PackageUpdateEventHandler -> android.intent.action.PACKAGE_REMOVED
2025-08-19 13:18:44.596 12049-12049 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: PackageUpdateEventHandler -> android.intent.action.PACKAGE_REPLACED
2025-08-19 13:18:44.598 12049-12049 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: PackageUpdateEventHandler -> com.dspread.mdm.service.CHECK_SELF_UPDATE_STATUS
2025-08-19 13:18:44.604 12049-12049 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: TaskExecuteEventHandler -> com.dspread.mdm.service.WSTASK_EXEC_BC
2025-08-19 13:18:44.607 12049-12049 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: TerminalInfoEventHandler -> com.dspread.mdm.service.TER_INFO_UPLOAD_BC
2025-08-19 13:18:44.609 12049-12049 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: ServiceGuardEventHandler -> com.dspread.mdm.service.SERVICE_GUARD_TIMER
2025-08-19 13:18:44.612 12049-12049 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: ServiceManagementEventHandler -> android.intent.action.USER_PRESENT
2025-08-19 13:18:44.614 12049-12049 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: ServiceManagementEventHandler -> android.intent.action.TIME_TICK
2025-08-19 13:18:44.616 12049-12049 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: ServiceManagementEventHandler -> com.dspread.mdm.service.SERVICE_RESTART
2025-08-19 13:18:44.618 12049-12049 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: ServiceManagementEventHandler -> android.net.conn.CONNECTIVITY_CHANGE
2025-08-19 13:18:44.621 12049-12049 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: WakeLockEventHandler -> com.dspread.mdm.service.WAKELOCK_RENEWAL
2025-08-19 13:18:44.624 12049-12049 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: WakeLockEventHandler -> com.dspread.mdm.service.WAKELOCK_ACQUIRE
2025-08-19 13:18:44.626 12049-12049 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: WakeLockEventHandler -> com.dspread.mdm.service.WAKELOCK_RELEASE
2025-08-19 13:18:44.631 12049-12049 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: ProvisioningEventHandler -> com.dspread.mdm.service.PROVISIONING_TIMER
2025-08-19 13:18:44.634 12049-12049 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: ProvisioningEventHandler -> android.net.conn.CONNECTIVITY_CHANGE
2025-08-19 13:18:44.636 12049-12049 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: ProvisioningEventHandler -> android.intent.action.BOOT_COMPLETED
2025-08-19 13:18:44.658 12049-12049 Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 服务初始化完成
2025-08-19 13:18:44.663 12049-12049 Platform                com.dspread.mdm.service              D  🔧 NetworkApi 单例实例已创建
2025-08-19 13:18:44.667 12049-12049 TetheringManager        com.dspread.mdm.service              I  registerTetheringEventCallback:com.dspread.mdm.service
2025-08-19 13:18:44.678 12049-12049 Receiver                com.dspread.mdm.service              D  🔧 网络状态变化: 连接=true, 类型=WIFI
2025-08-19 13:18:44.687 12049-12049 Receiver                com.dspread.mdm.service              D  🔧 网络恢复，尝试重新连接WebSocket
2025-08-19 13:18:44.689 12049-12049 Receiver                com.dspread.mdm.service              D  🔧 ServiceManagementEventHandler 处理网络变化事件
2025-08-19 13:18:44.694 12049-12049 Common                  com.dspread.mdm.service              D  🔧 ServiceStartupManager 服务正在运行，无需启动: NETWORK_CHANGE
2025-08-19 13:18:44.697 12049-12049 Receiver                com.dspread.mdm.service              D  🔧 ServiceManagementEventHandler 网络变化后服务检查完成
2025-08-19 13:18:44.700 12049-12049 Provisioning            com.dspread.mdm.service              D  🔧 ProvisioningEventHandler 网络状态变化
2025-08-19 13:18:44.702 12049-12049 Provisioning            com.dspread.mdm.service              D  🔧 ProvisioningEventHandler 忽略初始启动时的网络连接事件
2025-08-19 13:18:44.705 12049-12049 Receiver                com.dspread.mdm.service              D  🔧 电池状态更新: 电池状态: 电量=76%, 温度=29.5°C, 健康=2, 充电=是, 低电量=否, 耗尽=否
2025-08-19 13:18:44.707 12049-12049 Receiver                com.dspread.mdm.service              D  🔧 电池状态变化: 电量=76%, 温度=29°C, 充电=true
2025-08-19 13:18:44.815 12049-12049 WebSocket               com.dspread.mdm.service              I  🔧 C0902 被动式上送: battery_level_change_5 (被动: 1)
2025-08-19 13:18:44.829 12049-12049 Platform                com.dspread.mdm.service              D  🔧 DeviceInfoApi 单例实例已创建
2025-08-19 13:18:44.843 12049-12049 WebSocket               com.dspread.mdm.service              I  🔧 消息已缓存: C0902 (缓存数量: 1)
2025-08-19 13:18:44.844 12049-12049 WebSocket               com.dspread.mdm.service              I  🔧 C0902 电池状态上送成功 (1, trigger=battery_level_change_5)
2025-08-19 13:18:44.975 12049-12079 System.out              com.dspread.mdm.service              I  [socket]:check permission begin!
2025-08-19 13:18:44.976 12049-12079 System                  com.dspread.mdm.service              W  ClassLoader referenced unknown path: system/framework/mediatek-cta.jar
2025-08-19 13:18:44.978 12049-12079 System.out              com.dspread.mdm.service              I  [socket] e:java.lang.ClassNotFoundException: com.mediatek.cta.CtaUtils
2025-08-19 13:18:46.122 12049-12079 System.out              com.dspread.mdm.service              I  [OkHttp] sendRequest>>
2025-08-19 13:18:46.122 12049-12079 System.out              com.dspread.mdm.service              I  [OkHttp] sendRequest<<
2025-08-19 13:18:47.126 12049-12079 Provisioning            com.dspread.mdm.service              I  ℹ️ 远程配置获取成功
2025-08-19 13:18:47.129 12049-12079 Provisioning            com.dspread.mdm.service              I  ℹ️ 远程配置请求成功
2025-08-19 13:18:47.132 12049-12079 Provisioning            com.dspread.mdm.service              I  ℹ️ 远程配置解析成功 - CID: 1001
2025-08-19 13:18:47.140 12049-12079 Provisioning            com.dspread.mdm.service              I  ℹ️ API响应已保存到本地: /data/pos/config/provisioning_config.json, 具体内容为:
                                                                                                    {"mode":"dev","data":{"requestTime":"1755580726674","customization":{"system":{"timezone":"Asia\/Hong_Kong","logo":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/test\/logo.bin","logoMd5":"ba1ee533924eae5c408465e7cddcbda4","bootAnimationMd5":"ba1ee533924eae5c408465e7cddcbda4","gps":{"minUpdateTime":"30","scheduleTime":"60","maxLocateTime":"0","minDistance":"10","valid_distance":"500","care":"1"},"powerSaveMode":{"enable":"1","screenTimeout":"0"},"bootAnimation":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/test\/bootanimation.zip"},"polling":{"heartbeatTime":"300","terminalInfoTime":"900","statusApiUrl":"wss:\/\/api.dspreadserv.net\/status\/websocket\/register","remoteUrl":"wss:\/\/remote.dspreadserv.net\/remoteWSS\/websockify","uploadMode":"1","wssreconn":{"delayPolicy":"1","delaySwitch":"1","delayTime":"60"}}},"client":"default","cid":"1001"},"function":"getProvisioningConfig","description":"SUCCESS","stateCode":"0","version":"v1.0.1.20250801"}
2025-08-19 13:18:47.144 12049-12079 Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] Provisioning进度: 正在下载 Logo...
2025-08-19 13:18:47.214 12049-12079 Provisioning            com.dspread.mdm.service              I  ℹ️ 文件已存在且MD5匹配，跳过下载: Logo
2025-08-19 13:18:47.217 12049-12079 Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] Provisioning进度: 正在下载 BootAnimation...
2025-08-19 13:18:47.275 12049-12079 Provisioning            com.dspread.mdm.service              I  ℹ️ 文件已存在且MD5匹配，跳过下载: BootAnimation
2025-08-19 13:18:47.277 12049-12079 Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] Provisioning进度: 正在应用配置...
2025-08-19 13:18:47.279 12049-12079 Provisioning            com.dspread.mdm.service              I  ℹ️ 配置应用完成
2025-08-19 13:18:47.280 12049-12079 Provisioning            com.dspread.mdm.service              I  ℹ️ 远程配置成功，标记首次配置完成
2025-08-19 13:18:47.283 12049-12079 Provisioning            com.dspread.mdm.service              D  🔧 状态标志保存成功: /data/pos/config/provisioning_flags.json
2025-08-19 13:18:47.285 12049-12079 Provisioning            com.dspread.mdm.service              I  ℹ️ Provisioning配置执行完成
2025-08-19 13:18:47.287 12049-12079 Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] Provisioning完成，启动核心组件
2025-08-19 13:18:47.289 12049-12049 Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 开始初始化核心组件
2025-08-19 13:18:47.299 12049-12049 WebSocket               com.dspread.mdm.service              I  🔧 开始初始化 WebSocket 组件...
2025-08-19 13:18:47.301 12049-12049 WebSocket               com.dspread.mdm.service              I  🔧 初始化 WebSocket 密钥管理器
2025-08-19 13:18:47.303 12049-12049 WebSocket               com.dspread.mdm.service              I  🔧 生成客户端公私钥对
2025-08-19 13:18:47.448 12049-12049 Common                  com.dspread.mdm.service              I  ✅ WebSocket 密钥管理器初始化完成
2025-08-19 13:18:47.456 12049-12049 WebSocket               com.dspread.mdm.service              I  🔧 开始初始化 WsMessageCenter...
2025-08-19 13:18:47.458 12049-12049 WebSocket               com.dspread.mdm.service              I  🔧 WsMessageCenter 首次初始化
2025-08-19 13:18:47.478 12049-12049 Task                    com.dspread.mdm.service              D  🔧 从存储加载任务: 1 个
2025-08-19 13:18:47.481 12049-12049 WebSocket               com.dspread.mdm.service              I  🔧 WsTaskManager 初始化完成
2025-08-19 13:18:47.580 12049-12049 WebSocket               com.dspread.mdm.service              I  🔧 WsMessageCenter 初始化完成
2025-08-19 13:18:47.582 12049-12049 WebSocket               com.dspread.mdm.service              I  🔧 开始清理终态任务，当前任务数量: 1
2025-08-19 13:18:47.584 12049-12049 WebSocket               com.dspread.mdm.service              I  🔧 🔍 检查任务: taskId=d0b86561002748a6ae2a8122bc9867d4&a71a933343a548fd81d2d830b95c930b, taskResult=B02, isTerminal=false
2025-08-19 13:18:47.586 12049-12049 WebSocket               com.dspread.mdm.service              I  🔧 ✅ 保留非终态任务: taskId=d0b86561002748a6ae2a8122bc9867d4&a71a933343a548fd81d2d830b95c930b, taskResult=B02
2025-08-19 13:18:47.589 12049-12049 WebSocket               com.dspread.mdm.service              I  🔧 没有需要清理的终态任务
2025-08-19 13:18:47.591 12049-12049 WebSocket               com.dspread.mdm.service              I  🔧 开始重置任务状态，当前任务个数: 1
2025-08-19 13:18:47.593 12049-12049 WebSocket               com.dspread.mdm.service              I  🔧 检查任务: taskId=d0b86561002748a6ae2a8122bc9867d4&a71a933343a548fd81d2d830b95c930b, taskResult=B02, taskType=01
2025-08-19 13:18:47.596 12049-12049 WebSocket               com.dspread.mdm.service              I  🔧 跳过自身更新任务: d0b86561002748a6ae2a8122bc9867d4&a71a933343a548fd81d2d830b95c930b (INSTALL_ING)
2025-08-19 13:18:47.598 12049-12049 WebSocket               com.dspread.mdm.service              I  🔧 没有需要重置的任务
2025-08-19 13:18:47.599 12049-12049 Common                  com.dspread.mdm.service              I  ✅ 任务状态恢复完成
2025-08-19 13:18:47.606 12049-12049 WebSocket               com.dspread.mdm.service              I  🔧 使用Provisioning配置的WebSocket URL: wss://api.dspreadserv.net/status/websocket/register
2025-08-19 13:18:47.608 12049-12049 WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接管理器初始化完成
2025-08-19 13:18:47.610 12049-12049 Common                  com.dspread.mdm.service              I  ✅ WebSocket 管理器初始化完成
2025-08-19 13:18:47.612 12049-12049 WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 管理器初始化完成
2025-08-19 13:18:47.616 12049-12049 Common                  com.dspread.mdm.service              I  ✅ WebSocket 组件初始化完成
2025-08-19 13:18:47.618 12049-12049 WebSocket               com.dspread.mdm.service              I  🔧 启动 WebSocket 连接...
2025-08-19 13:18:47.621 12049-12049 WebSocket               com.dspread.mdm.service              I  🔧 开始连接 WebSocket...
2025-08-19 13:18:47.633 12049-12049 WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接到: wss://api.dspreadserv.net/status/websocket/register?serialNo=MDE2MTAwNDAyMDIzMDcwNjAyMjc=&publicKey=TUlHZk1BMEdDU3FHU0liM0RRRUJBUVVBQTRHTkFEQ0JpUUtCZ1FEQzdvNDFKcXdPaFBXRnNjblk2SlNOcnFVV3JVT1Y3eGlxVVVPN0hzYVNxV2VaWWN4UDcwVHFwZEhOZnc3QlRRUVdWRUFRTWIydUhONVZxejRyaWdONlVjbVlmZ214aFJPdVlSSld3bjhYWkkvdFEvejJqUTd0WVRLSGg5ZFp6RVlSbWFxSzQ3Z2VpOXpOQ1RscVV2eU9hWkpFb0RaeFU3ZVk0QngzSEpGSHp3SURBUUFC&query=1&msgVer=3&timestamp=1755580727625&signature=YJ91zV5m1U5UOkVUQjzTI/QqfxiPPExCPblo91fCh+YSN4L69E0xSyZwS8GyUkDbqC5LaL7luGkkyaAU+/wKmZtt+WrGGRifFOZG5Fzmo5tiGQqR9kQEastDl16GNwYFfx0LiRl4UEDnzM9K/h32b5VWIdxYS+aOmdHbVPh2ODA=
2025-08-19 13:18:47.639 12049-12049 WebSocket               com.dspread.mdm.service              I  🔧 未检测到系统代理设置
2025-08-19 13:18:47.659 12049-12049 WebSocket               com.dspread.mdm.service              I  🔧 ===== WebSocket 库默认配置 =====
2025-08-19 13:18:47.662 12049-12049 WebSocket               com.dspread.mdm.service              I  🔧 默认 PING 间隔: 60000ms (60秒)
2025-08-19 13:18:47.664 12049-12049 WebSocket               com.dspread.mdm.service              I  🔧 默认 PONG 间隔: 0ms (0秒)
2025-08-19 13:18:47.666 12049-12049 WebSocket               com.dspread.mdm.service              I  🔧 帧队列大小: 5
2025-08-19 13:18:47.668 12049-12049 WebSocket               com.dspread.mdm.service              I  🔧 连接超时: 15000ms (15秒)
2025-08-19 13:18:47.671 12049-12049 WebSocket               com.dspread.mdm.service              I  🔧 允许缺失关闭帧: false
2025-08-19 13:18:47.672 12049-12049 WebSocket               com.dspread.mdm.service              I  🔧 ================================
2025-08-19 13:18:47.676 12049-12049 WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接请求已发送
2025-08-19 13:18:47.679 12049-12049 WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接启动完成
2025-08-19 13:18:47.684 12049-12049 Common                  com.dspread.mdm.service              D  🔧 [ModuleManagerRegistry] 模块管理器注册中心初始化完成
2025-08-19 13:18:47.692 12049-12095 System.out              com.dspread.mdm.service              I  [socket]:check permission begin!
2025-08-19 13:18:47.700 12049-12049 RuleBase                com.dspread.mdm.service              D  🔧 RuleBaseManager 开始初始化RuleBaseManager...
2025-08-19 13:18:47.707 12049-12049 RuleBase                com.dspread.mdm.service              D  🔧 RuleBaseStorage RuleBase存储管理器初始化完成
2025-08-19 13:18:47.709 12049-12049 RuleBase                com.dspread.mdm.service              D  🔧 RuleStateMonitor 状态监控器初始化完成
2025-08-19 13:18:47.710 12049-12049 RuleBase                com.dspread.mdm.service              D  🔧 RuleBaseManager RuleBaseManager初始化完成
2025-08-19 13:18:47.712 12049-12049 Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] RuleBaseManager初始化成功
2025-08-19 13:18:47.715 12049-12049 Provisioning            com.dspread.mdm.service              D  🔧 ProvisioningEventHandler 启动Provisioning定时器: 300s (0小时)
2025-08-19 13:18:47.715 12049-12079 Common                  com.dspread.mdm.service              D  🔧 [ModuleManagerRegistry] 启动所有模块
2025-08-19 13:18:47.719 12049-12049 Common                  com.dspread.mdm.service              I  ✅ ⏰ 设置初始化定时器成功，下次执行: 300秒后 (0小时)
2025-08-19 13:18:47.721 12049-12049 Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] Provisioning定时器启动成功
2025-08-19 13:18:47.723 12049-12049 Common                  com.dspread.mdm.service              D  🔧 TimerConfig ========== 当前定时器配置 (调试模式) ==========
2025-08-19 13:18:47.725 12049-12079 Common                  com.dspread.mdm.service              D  🔧 [ModuleManagerRegistry] LogStreamManager实例创建完成
2025-08-19 13:18:47.726 12049-12049 Common                  com.dspread.mdm.service              D  🔧 TimerConfig ①心跳定时器: 60秒
2025-08-19 13:18:47.727 12049-12079 Common                  com.dspread.mdm.service              D  🔧 Module LogStream Manager status changed to: INITIALIZED
2025-08-19 13:18:47.727 12049-12049 Common                  com.dspread.mdm.service              D  🔧 TimerConfig ②终端信息上传: 120秒
2025-08-19 13:18:47.729 12049-12049 Common                  com.dspread.mdm.service              D  🔧 TimerConfig ③任务执行: 60秒
2025-08-19 13:18:47.731 12049-12049 Common                  com.dspread.mdm.service              D  🔧 TimerConfig ④服务守护: 120秒
2025-08-19 13:18:47.732 12049-12079 Common                  com.dspread.mdm.service              D  🔧 [ModuleManagerRegistry] LogStreamManager初始化成功
2025-08-19 13:18:47.733 12049-12049 Common                  com.dspread.mdm.service              D  🔧 TimerConfig ⑤Provisioning: 300秒
2025-08-19 13:18:47.734 12049-12049 Common                  com.dspread.mdm.service              D  🔧 TimerConfig ===============================================
2025-08-19 13:18:47.734 12049-12079 Common                  com.dspread.mdm.service              D  🔧 Module LogStream Manager status changed to: STARTING
2025-08-19 13:18:47.736 12049-12049 Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 核心组件初始化完成
2025-08-19 13:18:47.818 12049-12079 Common                  com.dspread.mdm.service              D  🔧 Module LogStream Manager status changed to: RUNNING
2025-08-19 13:18:47.820 12049-12079 Common                  com.dspread.mdm.service              D  🔧 [ModuleManagerRegistry] LogStreamManager启动成功
2025-08-19 13:18:47.822 12049-12079 Common                  com.dspread.mdm.service              D  🔧 [ModuleManagerRegistry] LogStreamManager启动成功
2025-08-19 13:18:47.823 12049-12079 Common                  com.dspread.mdm.service              D  🔧 [ModuleManagerRegistry] 开始初始化地理围栏管理器
2025-08-19 13:18:47.850 12049-12079 Common                  com.dspread.mdm.service              D  🔧 Module Geofence Manager status changed to: INITIALIZED
2025-08-19 13:18:47.855 12049-12079 Common                  com.dspread.mdm.service              D  🔧 Module Geofence Manager status changed to: STARTING
2025-08-19 13:18:47.866 12049-12079 Platform                com.dspread.mdm.service              D  🔧 用户交互监控已启动（简化版本）
2025-08-19 13:18:47.868 12049-12079 Platform                com.dspread.mdm.service              D  🔧 使用模拟用户交互检测
2025-08-19 13:18:47.870 12049-12079 Platform                com.dspread.mdm.service              D  🔧 用户交互监控已启动
2025-08-19 13:18:47.872 12049-12049 Platform                com.dspread.mdm.service              D  🔧 [WiFiManagerApi] WiFiManagerApi 单例实例已创建
2025-08-19 13:18:47.872 12049-12079 Service                 com.dspread.mdm.service              D  🔧 [GeofenceManager]Constants.geofenceStatus = 1
2025-08-19 13:18:47.874 12049-12079 Service                 com.dspread.mdm.service              D  🔧 [GeofenceManager]-- Constants.storeId =  -- Constants.storeSsid =  -- Constants.storeIp = 
2025-08-19 13:18:47.876 12049-12079 Common                  com.dspread.mdm.service              D  🔧 Module Geofence Manager status changed to: RUNNING
2025-08-19 13:18:47.878 12049-12079 Common                  com.dspread.mdm.service              D  🔧 [ModuleManagerRegistry] 地理围栏管理器初始化成功
2025-08-19 13:18:47.880 12049-12079 Common                  com.dspread.mdm.service              D  🔧 [ModuleManagerRegistry] GeofenceManager启动成功
2025-08-19 13:18:47.881 12049-12079 Common                  com.dspread.mdm.service              D  🔧 [ModuleManagerRegistry] 所有模块启动完成
2025-08-19 13:18:47.943 12049-12096 System.out              com.dspread.mdm.service              I  [socket]:check permission begin!
2025-08-19 13:18:48.416 12049-12079 Platform                com.dspread.mdm.service              D  🔧 NetworkTrafficMonitor 等待下次上送时间: 2025-08-20 00:00:00
2025-08-19 13:18:49.835 12049-12106 Common                  com.dspread.mdm.service              I  ✅ WebSocket 物理连接成功，等待服务器 hellMsg
2025-08-19 13:18:49.838 12049-12106 WebSocket               com.dspread.mdm.service              I  🔧 ===== 心跳发送器实际配置 =====
2025-08-19 13:18:49.841 12049-12106 WebSocket               com.dspread.mdm.service              I  🔧 实际 PING 发送间隔: 60000ms (60秒)
2025-08-19 13:18:49.844 12049-12106 WebSocket               com.dspread.mdm.service              I  🔧 PING 定时器名称: PingSender
2025-08-19 13:18:49.846 12049-12106 WebSocket               com.dspread.mdm.service              I  🔧 实际 PONG 发送间隔: 0ms (0秒)
2025-08-19 13:18:49.849 12049-12106 WebSocket               com.dspread.mdm.service              I  🔧 PONG 定时器名称: PongSender
2025-08-19 13:18:49.851 12049-12106 WebSocket               com.dspread.mdm.service              I  🔧 ===============================
2025-08-19 13:18:49.878 12049-12106 Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"msg_state":"P","request_time":"1755580536662","data":{"taskList":[{"beginDate":"2024-08-19 05:15:36","taskType":"02","apkName":"Via","endDate":"9999-12-31 23:59:59","appId":"1755580536662","packName":"mark.via","versionName":"6.2.0","taskId":"1755580536662","versionCode":"20250117"}]},"tranCode":"ST001","request_id":"1755580536662ST001","version":"1","serialNo":"01610040202307060227"}
2025-08-19 13:18:49.880 12049-12106 WebSocket               com.dspread.mdm.service              I  🔧 准备发送响应确认 - requestId: 1755580536662ST001, needResponse: true
2025-08-19 13:18:49.886 12049-12106 WebSocket               com.dspread.mdm.service              I  🔧 非重要消息，不缓存: C0000
2025-08-19 13:18:49.893 12049-12106 WebSocket               com.dspread.mdm.service              I  🔧 非重要消息，不缓存: C0000
2025-08-19 13:18:49.894 12049-12106 WebSocket               com.dspread.mdm.service              I  🔧 已发送C0000响应确认: requestId=1755580536662ST001
2025-08-19 13:18:49.897 12049-12106 Task                    com.dspread.mdm.service              D  🔧 处理任务消息: ST001
2025-08-19 13:18:49.899 12049-12106 Task                    com.dspread.mdm.service              I  ℹ️ 更新任务列表: requestId=1755580536662ST001, 任务数量=1
2025-08-19 13:18:49.901 12049-12106 Task                    com.dspread.mdm.service              D  🔧 任务按时间插入到位置: 0, taskId=1755580536662
2025-08-19 13:18:49.903 12049-12106 Task                    com.dspread.mdm.service              D  🔧 添加/更新任务: taskId=1755580536662
2025-08-19 13:18:49.907 12049-12106 Task                    com.dspread.mdm.service              D  🔧 保存任务列表到存储: 2 个
2025-08-19 13:18:49.908 12049-12106 Task                    com.dspread.mdm.service              D  🔧 开始处理任务列表，共 1 个任务
2025-08-19 13:18:49.910 12049-12106 Task                    com.dspread.mdm.service              D  🔧 处理任务: taskId=1755580536662, type=02, package=mark.via, apk=Via
2025-08-19 13:18:49.912 12049-12106 Task                    com.dspread.mdm.service              D  🔧 处理卸载任务: 1755580536662
2025-08-19 13:18:49.921 12049-12106 Task                    com.dspread.mdm.service              D  🔧 更新任务状态: taskId=1755580536662, state=D02
2025-08-19 13:18:49.924 12049-12106 Task                    com.dspread.mdm.service              D  🔧 保存任务列表到存储: 2 个
2025-08-19 13:18:49.927 12049-12106 Task                    com.dspread.mdm.service              D  🔧 保存任务列表到存储: 2 个
2025-08-19 13:18:49.929 12049-12106 WebSocket               com.dspread.mdm.service              I  🔧 ST001 主动式上送: task_failed (主动: 1)
2025-08-19 13:18:49.938 12049-12106 WebSocket               com.dspread.mdm.service              I  🔧 消息已缓存: C0108 (缓存数量: 2)
2025-08-19 13:18:49.940 12049-12106 WebSocket               com.dspread.mdm.service              I  🔧 任务结果实时上送: taskId=1755580536662, result=D02 (1)
2025-08-19 13:18:49.955 12049-12106 Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"msg_state":"P","request_time":"1755580524415","data":{"taskList":[{"beginDate":"2024-08-19 05:15:24","taskType":"02","apkName":"OpenVPN for Android","endDate":"9999-12-31 23:59:59","appId":"1755580524415","packName":"de.blinkt.openvpn","versionName":"0.5.36a","taskId":"1755580524415","versionCode":"65"}]},"tranCode":"ST001","request_id":"1755580524415ST001","version":"1","serialNo":"01610040202307060227"}
2025-08-19 13:18:49.957 12049-12106 WebSocket               com.dspread.mdm.service              I  🔧 准备发送响应确认 - requestId: 1755580524415ST001, needResponse: true
2025-08-19 13:18:49.961 12049-12106 WebSocket               com.dspread.mdm.service              I  🔧 非重要消息，不缓存: C0000
2025-08-19 13:18:49.967 12049-12106 WebSocket               com.dspread.mdm.service              I  🔧 非重要消息，不缓存: C0000
2025-08-19 13:18:49.969 12049-12106 WebSocket               com.dspread.mdm.service              I  🔧 已发送C0000响应确认: requestId=1755580524415ST001
2025-08-19 13:18:49.971 12049-12106 Task                    com.dspread.mdm.service              D  🔧 处理任务消息: ST001
2025-08-19 13:18:49.973 12049-12106 Task                    com.dspread.mdm.service              I  ℹ️ 更新任务列表: requestId=1755580524415ST001, 任务数量=1
2025-08-19 13:18:49.975 12049-12106 Task                    com.dspread.mdm.service              D  🔧 任务按时间插入到位置: 0, taskId=1755580524415
2025-08-19 13:18:49.977 12049-12106 Task                    com.dspread.mdm.service              D  🔧 添加/更新任务: taskId=1755580524415
2025-08-19 13:18:49.978 12049-12106 Task                    com.dspread.mdm.service              D  🔧 发现已完成任务: taskId=1755580536662, result=D02
2025-08-19 13:18:49.980 12049-12106 Task                    com.dspread.mdm.service              D  🔧 上送任务结果: taskId=1755580536662, result=D02
2025-08-19 13:18:49.982 12049-12106 Task                    com.dspread.mdm.service              D  🔧 清理已完成任务: taskId=1755580536662, result=D02
2025-08-19 13:18:49.983 12049-12106 Task                    com.dspread.mdm.service              D  🔧 清理了 1 个已完成任务
2025-08-19 13:18:49.986 12049-12106 Task                    com.dspread.mdm.service              D  🔧 保存任务列表到存储: 2 个
2025-08-19 13:18:49.988 12049-12106 Task                    com.dspread.mdm.service              D  🔧 开始处理任务列表，共 1 个任务
2025-08-19 13:18:49.989 12049-12106 Task                    com.dspread.mdm.service              D  🔧 处理任务: taskId=1755580524415, type=02, package=de.blinkt.openvpn, apk=OpenVPN for Android
2025-08-19 13:18:49.991 12049-12106 Task                    com.dspread.mdm.service              D  🔧 处理卸载任务: 1755580524415
2025-08-19 13:18:49.994 12049-12106 Task                    com.dspread.mdm.service              D  🔧 更新任务状态: taskId=1755580524415, state=D02
2025-08-19 13:18:49.997 12049-12106 Task                    com.dspread.mdm.service              D  🔧 保存任务列表到存储: 2 个
2025-08-19 13:18:50.001 12049-12106 Task                    com.dspread.mdm.service              D  🔧 保存任务列表到存储: 2 个
2025-08-19 13:18:50.004 12049-12106 WebSocket               com.dspread.mdm.service              I  🔧 ST001 主动式上送: task_failed (主动: 2)
2025-08-19 13:18:50.017 12049-12106 WebSocket               com.dspread.mdm.service              I  🔧 消息已缓存: C0108 (缓存数量: 3)
2025-08-19 13:18:50.019 12049-12106 WebSocket               com.dspread.mdm.service              I  🔧 任务结果实时上送: taskId=1755580524415, result=D02 (1)
2025-08-19 13:18:50.030 12049-12106 Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"hellMsg":"hello 01610040202307060227","tranCode":"S0000","version":"1","rebootTime":"01:12:49","serialNo":"01610040202307060227","deviceStatus":"6"}
2025-08-19 13:18:50.032 12049-12106 Common                  com.dspread.mdm.service              I  ✅ 收到服务器 hellMsg，WebSocket 业务连接成功！
2025-08-19 13:18:50.034 12049-12106 Common                  com.dspread.mdm.service              I  ✅ WebSocket 业务连接成功！
2025-08-19 13:18:50.036 12049-12106 WebSocket               com.dspread.mdm.service              I  🔧 业务连接成功，重连计数已重置为 0
2025-08-19 13:18:50.039 12049-12106 Common                  com.dspread.mdm.service              I  ✅ ⏰ 设置任务执行定时器成功，下次执行: 60秒后 (1分钟)
2025-08-19 13:18:50.041 12049-12106 WebSocket               com.dspread.mdm.service              I  🔧 已启动任务执行定时器
2025-08-19 13:18:50.045 12049-12106 Common                  com.dspread.mdm.service              I  ✅ ⏰ 设置心跳定时器成功，下次执行: 60秒后 (1分钟)
2025-08-19 13:18:50.046 12049-12106 WebSocket               com.dspread.mdm.service              I  🔧 已启动心跳定时器
2025-08-19 13:18:50.050 12049-12106 Common                  com.dspread.mdm.service              I  ✅ ⏰ 设置终端信息定时器成功，下次执行: 120秒后 (2分钟)
2025-08-19 13:18:50.052 12049-12106 WebSocket               com.dspread.mdm.service              I  🔧 已启动终端信息上传定时器
2025-08-19 13:18:50.053 12049-12106 WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接成功，检查自身更新任务状态
2025-08-19 13:18:50.056 12049-12106 Receiver                com.dspread.mdm.service              D  🔧 发送广播: com.dspread.mdm.service.CHECK_SELF_UPDATE_STATUS
2025-08-19 13:18:50.058 12049-12106 WebSocket               com.dspread.mdm.service              I  🔧 开始发送缓存消息，数量: 3
2025-08-19 13:18:50.062 12049-12106 WebSocket               com.dspread.mdm.service              I  🔧 C0902 主动式上送: cached_message_resend (主动: 3)
2025-08-19 13:18:50.068 12049-12106 Common                  com.dspread.mdm.service              I  ✅ 缓存消息发送成功: C0902
2025-08-19 13:18:50.392 12049-12109 ProfileInstaller        com.dspread.mdm.service              D  Installing profile for com.dspread.mdm.service
2025-08-19 13:18:50.571 12049-12106 WebSocket               com.dspread.mdm.service              I  🔧 C0108 主动式上送: cached_message_resend (主动: 4)
2025-08-19 13:18:50.579 12049-12106 Common                  com.dspread.mdm.service              I  ✅ 缓存消息发送成功: C0108
2025-08-19 13:18:51.083 12049-12106 WebSocket               com.dspread.mdm.service              I  🔧 C0108 主动式上送: cached_message_resend (主动: 5)
2025-08-19 13:18:51.090 12049-12106 Common                  com.dspread.mdm.service              I  ✅ 缓存消息发送成功: C0108
2025-08-19 13:18:51.594 12049-12106 WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接成功
2025-08-19 13:18:51.596 12049-12106 WebSocket               com.dspread.mdm.service              I  🔧 设备状态: 6
2025-08-19 13:18:51.599 12049-12106 WebSocket               com.dspread.mdm.service              I  🔧 检查首次连接标志: isFirstTimeWebSocketConnected=true
2025-08-19 13:18:51.601 12049-12106 WebSocket               com.dspread.mdm.service              I  🔧 首次WebSocket连接成功，发送初始化信息并执行初始化检查
2025-08-19 13:18:51.604 12049-12106 WebSocket               com.dspread.mdm.service              I  🔧 开始发送初始化信息（带流量控制）
2025-08-19 13:18:51.607 12049-12106 WebSocket               com.dspread.mdm.service              I  🔧 C0901 被动式上送: first_connection (被动: 2)
2025-08-19 13:18:51.613 12049-12106 Platform                com.dspread.mdm.service              D  🔧 重新收集应用信息
2025-08-19 13:18:51.682 12049-12106 AppManager              com.dspread.mdm.service              I  ℹ️ 应用信息获取完成: 总数120(系统119/用户1) 返回1个
2025-08-19 13:18:51.686 12049-12106 Platform                com.dspread.mdm.service              D  🔧 应用信息: 1 个应用
2025-08-19 13:18:51.695 12049-12106 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0901","serialNo":"01610040202307060227","request_time":"1755580731689","request_id":"1755580731689C0901","version":"1","data":{"apkInfo":[{"packName":"com.dspread.mdm.service","apkName":"service","versionCode":21,"versionName":"1.1.02.20250819.DSPREAD.MDM.SERVICE","updateDate":"2025-08-19 13:18:20"}],"sytemInfo":{"androidVersion":"11","buildNumber":"D60_Baseline_debug_20250708_ota_test","aspl":"2021-12-05"},"serviceInfo":[]},"myVersionName":"1.1.02.20250819.DSPREAD.MDM.SERVICE","terminalDate":"20250819131851"}
2025-08-19 13:18:51.697 12049-12106 WebSocket               com.dspread.mdm.service              I  🔧 C0901 应用信息上送成功 (1)
2025-08-19 13:18:52.699 12049-12106 WebSocket               com.dspread.mdm.service              I  🔧 C0902 被动式上送: first_connection (被动: 3)
2025-08-19 13:18:52.717 12049-12106 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0902","serialNo":"01610040202307060227","request_time":"1755580732708","request_id":"1755580732708C0902","version":"1","data":{"batteryLife":76,"batteryHealth":2,"temprature":"29.5","isCharging":"1","isLowBattery":"0","powerOff":"0","outOfBattery":"0"},"myVersionName":"1.1.02.20250819.DSPREAD.MDM.SERVICE","terminalDate":"20250819131852"}
2025-08-19 13:18:52.720 12049-12106 WebSocket               com.dspread.mdm.service              I  🔧 C0902 电池状态上送成功 (1, trigger=first_connection)
2025-08-19 13:18:53.725 12049-12106 WebSocket               com.dspread.mdm.service              I  🔧 C0903 被动式上送: first_connection (被动: 4)
2025-08-19 13:18:53.765 12049-12106 Platform                com.dspread.mdm.service              D  🔧 内存总容量为： 1.86GB 
2025-08-19 13:18:53.768 12049-12106 Platform                com.dspread.mdm.service              D  🔧 内存使用量为： 0.57GB 
2025-08-19 13:18:53.880 12049-12106 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0903","serialNo":"01610040202307060227","request_time":"1755580733871","request_id":"1755580733871C0903","version":"1","data":{"hardwareInfo":[{"name":"CPU Usage","desc":"cpu:0.50"},{"name":"storage_usage_status","desc":" 5.39GB \/ 32.00GB ","state":"1"},{"name":"total_memory","desc":" 0.57GB \/ 1.86GB ","state":"1"},{"name":"androidVersion","desc":"11","state":"1"},{"name":"network_status","desc":"wifi","state":"1"},{"name":"OSVer","desc":"11","state":"1"}],"locationInfo":{"gps":{"longitude":"-999.0","latitude":"-999.0","satelliteCount":0,"satelliteCountInUsed":0,"satelliteAverSNR":"0.00","distance":"0.0","lockStatus":"1","lockMeter":"200","proId":"","proName":""},"baseSite":{"MCC":"","MNC":"","CID":"-1","LAC":"-1","DBM":"-999","IMSI":"","ICCID":""},"wifi":{"SSID":"fubox_2.4G","IP":"**************","MAC":"52:C3:63:13:8C:EE","SSTH":"-27","BSSID":"7c:10:c9:d6:9f:10"},"address":"无法获取"}},"myVersionName":"1.1.02.20250819.DSPREAD.MDM.SERVICE","terminalDate":"20250819131853"}
2025-08-19 13:18:53.882 12049-12106 WebSocket               com.dspread.mdm.service              I  🔧 C0903 数据信息上送成功 (1)
2025-08-19 13:18:54.884 12049-12106 WebSocket               com.dspread.mdm.service              I  🔧 C0904 被动式上送: first_connection (被动: 5)
2025-08-19 13:18:54.986 12049-12106 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0904","serialNo":"01610040202307060227","request_time":"1755580734975","request_id":"1755580734975C0904","version":"1","data":{"wifiOption":[{"SSID":"fubox_2.4G","SSTH":"-26"},{"SSID":"2206-5G","SSTH":"-31"},{"SSID":"2206","SSTH":"-39"},{"SSID":"@Ruijie-1816","SSTH":"-52"},{"SSID":"2205_5G","SSTH":"-54"},{"SSID":"2205","SSTH":"-56"},{"SSID":"DIRECT-C6-Mi All-in-One Inkjet","SSTH":"-57"},{"SSID":"2207","SSTH":"-66"},{"SSID":"DIRECT-CC-HP Laser 1188w","SSTH":"-69"},{"SSID":"2207-5G","SSTH":"-82"},{"SSID":"vivo Y51s","SSTH":"-83"}],"baseSite":{"MCC":"","MNC":"","CID":"-1","LAC":"-1","DBM":"-999","IMSI":"","ICCID":""},"wifi":{"SSID":"fubox_2.4G","IP":"**************","MAC":"52:C3:63:13:8C:EE","SSTH":"-27","BSSID":"7c:10:c9:d6:9f:10"}},"myVersionName":"1.1.02.20250819.DSPREAD.MDM.SERVICE","terminalDate":"20250819131854"}
2025-08-19 13:18:54.988 12049-12106 WebSocket               com.dspread.mdm.service              I  🔧 C0904 网络状态上送成功 (1)
2025-08-19 13:18:55.992 12049-12106 WebSocket               com.dspread.mdm.service              I  🔧 C0906 被动式上送: first_connection (被动: 6)
2025-08-19 13:18:56.000 12049-12106 Platform                com.dspread.mdm.service              W  ⚠️ SpVersionApi SP版本信息格式异常，未找到SP_VERSION标记
2025-08-19 13:18:56.003 12049-12106 Platform                com.dspread.mdm.service              D  🔧 SpVersionApi 从Settings获取SP版本: STD 582.1.238.8
2025-08-19 13:18:56.020 12049-12106 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0906","serialNo":"01610040202307060227","request_time":"1755580736011","request_id":"1755580736011C0906","version":"1","data":{"firmWareInfo":{"spfw":"STD 582.1.238.8"},"imei_1":"014250620000709","imei_2":"","wifi_mac":"52:c3:63:13:8c:ee","bt_mac":"","bsn":""},"myVersionName":"1.1.02.20250819.DSPREAD.MDM.SERVICE","terminalDate":"20250819131856"}
2025-08-19 13:18:56.023 12049-12106 WebSocket               com.dspread.mdm.service              I  🔧 C0906 设备信息上送成功 (1)
2025-08-19 13:18:56.027 12049-12106 WebSocket               com.dspread.mdm.service              I  🔧 C0109 被动式上送: first_connection (被动: 7)
2025-08-19 13:18:56.034 12049-12106 Platform                com.dspread.mdm.service              D  🔧 重新收集应用信息（强制刷新）
2025-08-19 13:18:56.095 12049-12106 AppManager              com.dspread.mdm.service              I  ℹ️ 应用信息获取完成: 总数120(系统119/用户1) 返回1个
2025-08-19 13:18:56.098 12049-12106 Platform                com.dspread.mdm.service              D  🔧 应用信息: 1 个应用
2025-08-19 13:18:56.170 12049-12106 Platform                com.dspread.mdm.service              D  🔧 内存总容量为： 1.86GB 
2025-08-19 13:18:56.172 12049-12106 Platform                com.dspread.mdm.service              D  🔧 内存使用量为： 0.57GB 
2025-08-19 13:18:56.200 12049-12106 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0109","serialNo":"01610040202307060227","request_time":"1755580736186","request_id":"1755580736186C0109","version":"1","data":{"apkInfo":[{"packName":"com.dspread.mdm.service","apkName":"service","versionCode":21,"versionName":"1.1.02.20250819.DSPREAD.MDM.SERVICE","updateDate":"2025-08-19 13:18:20"}],"locationInfo":{"gps":{"longitude":"-999.0","latitude":"-999.0","satelliteCount":0,"satelliteCountInUsed":0,"satelliteAverSNR":"0.00","distance":"0.0","lockStatus":"1","lockMeter":"200","proId":"","proName":""},"baseSite":{"MCC":"","MNC":"","CID":"-1","LAC":"-1","DBM":"-999","IMSI":"","ICCID":""},"wifi":{"SSID":"fubox_2.4G","IP":"**************","MAC":"52:C3:63:13:8C:EE","SSTH":"-27","BSSID":"7c:10:c9:d6:9f:10"},"address":"无法获取"},"hardwareInfo":[{"name":"CPU Usage","desc":"cpu:0.50"},{"name":"storage_usage_status","desc":" 5.39GB \/ 32.00GB ","state":"1"},{"name":"total_memory","desc":" 0.57GB \/ 1.86GB ","state":"1"},{"name":"androidVersion","desc":"11","state":"1"},{"name":"network_status","desc":"wifi","state":"1"},{"name":"OSVer","desc":"11","state":"1"}],"wifiOption":[{"SSID":"fubox_2.4G","SSTH":"-26"},{"SSID":"2206-5G","SSTH":"-31"},{"SSID":"2206","SSTH":"-39"},{"SSID":"@Ruijie-1816","SSTH":"-52"},{"SSID":"2205_5G","SSTH":"-54"},{"SSID":"2205","SSTH":"-56"},{"SSID":"DIRECT-C6-Mi All-in-One Inkjet","SSTH":"-57"},{"SSID":"2207","SSTH":"-66"},{"SSID":"DIRECT-CC-HP Laser 1188w","SSTH":"-69"},{"SSID":"2207-5G","SSTH":"-82"},{"SSID":"vivo Y51s","SSTH":"-83"}],"sytemInfo":{"androidVersion":"11","buildNumber":"D60_Baseline_debug_20250708_ota_test","aspl":"2021-12-05"}},"myVersionName":"1.1.02.20250819.DSPREAD.MDM.SERVICE","terminalDate":"20250819131856"}
2025-08-19 13:18:56.202 12049-12106 WebSocket               com.dspread.mdm.service              I  🔧 C0109 终端信息上传完成 (1, trigger=first_connection)
2025-08-19 13:18:56.204 12049-12106 Common                  com.dspread.mdm.service              I  ✅ 初始化信息发送完成
2025-08-19 13:18:56.206 12049-12106 WebSocket               com.dspread.mdm.service              I  🔧 检查服务更新结果
2025-08-19 13:18:56.209 12049-12106 Task                    com.dspread.mdm.service              I  ℹ️ 本地任务列表长度: 2
2025-08-19 13:18:56.211 12049-12106 Task                    com.dspread.mdm.service              D  🔧 本地任务列表内容: [{"beginDate":"2024-08-19 05:15:24","taskType":"02","apkName":"OpenVPN for Android","endDate":"9999-12-31 23:59:59","appId":"1755580524415","packName":"de.blinkt.openvpn","versionName":"0.5.36a","taskId":"1755580524415","versionCode":"65","request_id":"1755580524415ST001","request_time":"1755580524415","silent_install":"","taskResult":"D02","lastUpdateTime":1755580729998},{"apkMd5":"7fa4d472f03df6a4e8b78d182ac221ad","apkName":"service","endDate":"9999-12-31 23:59:59","installBy":"0","versionName":"1.1.02.20250819.DSPREAD.MDM.SERVICE","versionCode":"21","url":"https:\/\/smartms.s3.sa-east-1.amazonaws.com\/app\/ff6dd8d6f56c466e82c2e07cef78be15.apk","beginDate":"2024-08-19 05:17:40","taskType":"01","appId":"a71a933343a548fd81d2d830b95c930b","apkSize":"7838603","appIconUrl":"https:\/\/smartms.s3.sa-east-1.amazonaws.com\/icon\/8697584c22e642dbbeeaeada4c6a0f62ic_launcher.webp","packName":"com.dspread.mdm.service","taskId":"d0b86561002748a6ae2a8122bc9867d4&a71a933343a548fd81d2d830b95c930b","request_id":"1755580660804ST001","request_time":"1755580660804","silent_install":"","taskResult":"B02","lastUpdateTime":1755580700261}]
2025-08-19 13:18:56.213 12049-12106 Task                    com.dspread.mdm.service              I  ℹ️ 当前应用版本: versionCode=21, versionName=1.1.02.20250819.DSPREAD.MDM.SERVICE
2025-08-19 13:18:56.215 12049-12106 Task                    com.dspread.mdm.service              I  ℹ️ 任务列表数量: 2
2025-08-19 13:18:56.217 12049-12106 Task                    com.dspread.mdm.service              D  🔧 检查任务[0]: taskId=1755580524415, taskType=02, pkgName=de.blinkt.openvpn, versionCode=65, versionName=0.5.36a, taskResult=D02
2025-08-19 13:18:56.219 12049-12106 Task                    com.dspread.mdm.service              D  🔧 检查任务[1]: taskId=d0b86561002748a6ae2a8122bc9867d4&a71a933343a548fd81d2d830b95c930b, taskType=01, pkgName=com.dspread.mdm.service, versionCode=21, versionName=1.1.02.20250819.DSPREAD.MDM.SERVICE, taskResult=B02
2025-08-19 13:18:56.221 12049-12106 Task                    com.dspread.mdm.service              I  ℹ️ 找到自身服务更新任务: d0b86561002748a6ae2a8122bc9867d4&a71a933343a548fd81d2d830b95c930b
2025-08-19 13:18:56.228 12049-12106 WebSocket               com.dspread.mdm.service              I  🔧 时间检查: nowTime=1755580736223, beginTime=1701961745000, taskResult=B02
2025-08-19 13:18:56.230 12049-12106 WebSocket               com.dspread.mdm.service              I  🔧 服务更新已完成，上报安装成功
2025-08-19 13:18:56.233 12049-12106 Task                    com.dspread.mdm.service              D  🔧 更新任务状态: taskId=d0b86561002748a6ae2a8122bc9867d4&a71a933343a548fd81d2d830b95c930b, state=B03
2025-08-19 13:18:56.236 12049-12106 Task                    com.dspread.mdm.service              D  🔧 保存任务列表到存储: 2 个
2025-08-19 13:18:56.239 12049-12106 WebSocket               com.dspread.mdm.service              I  🔧 ST001 主动式上送: task_failed (主动: 6)
2025-08-19 13:18:56.250 12049-12106 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0108","serialNo":"01610040202307060227","request_time":"1755580736244","request_id":"1755580736244C0108","version":"1","data":{"taskId":"d0b86561002748a6ae2a8122bc9867d4&a71a933343a548fd81d2d830b95c930b","taskResult":"B03"},"myVersionName":"1.1.02.20250819.DSPREAD.MDM.SERVICE","terminalDate":"20250819131856"}
2025-08-19 13:18:56.252 12049-12106 WebSocket               com.dspread.mdm.service              I  🔧 任务结果实时上送: taskId=d0b86561002748a6ae2a8122bc9867d4&a71a933343a548fd81d2d830b95c930b, result=B03 (1)
2025-08-19 13:18:56.253 12049-12106 Common                  com.dspread.mdm.service              I  ✅ 服务更新结果上报完成: d0b86561002748a6ae2a8122bc9867d4&a71a933343a548fd81d2d830b95c930b -> B03
2025-08-19 13:18:56.256 12049-12106 WebSocket               com.dspread.mdm.service              I  🔧 检查OS升级状态...
2025-08-19 13:18:56.262 12049-12106 Common                  com.dspread.mdm.service              D  🔧 OsUpdateStatusChecker WebSocket连接成功，检查OS升级状态
2025-08-19 13:18:56.264 12049-12106 Common                  com.dspread.mdm.service              D  🔧 OsUpdateStatusChecker 没有待检查的OS升级任务
2025-08-19 13:18:56.266 12049-12106 WebSocket               com.dspread.mdm.service              I  🔧 OS升级状态检查已触发
2025-08-19 13:18:56.269 12049-12106 WebSocket               com.dspread.mdm.service              I  🔧 首次连接初始化检查完成
2025-08-19 13:18:56.284 12049-12106 Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"request_time":"1755580730906","org_request_time":"1755580729933","org_request_id":"1755580729933C0108","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755580730906S0000","serialNo":"01610040202307060227"}
2025-08-19 13:18:56.287 12049-12106 WebSocket               com.dspread.mdm.service              I  🔧 收到响应消息: transCode=S0000, orgRequestId=1755580729933C0108, state=0, remark=
2025-08-19 13:18:56.291 12049-12106 WebSocket               com.dspread.mdm.service              I  🔧 处理任务结果响应: state=0
2025-08-19 13:18:56.294 12049-12106 WebSocket               com.dspread.mdm.service              I  🔧 任务结果上传成功
2025-08-19 13:18:56.300 12049-12065 System                  com.dspread.mdm.service              W  A resource failed to call close. 
2025-08-19 13:18:56.306 12049-12106 Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"request_time":"1755580731519","org_request_time":"1755580730009","org_request_id":"1755580730009C0108","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755580731519S0000","serialNo":"01610040202307060227"}
2025-08-19 13:18:56.309 12049-12106 WebSocket               com.dspread.mdm.service              I  🔧 收到响应消息: transCode=S0000, orgRequestId=1755580730009C0108, state=0, remark=
2025-08-19 13:18:56.311 12049-12106 WebSocket               com.dspread.mdm.service              I  🔧 处理任务结果响应: state=0
2025-08-19 13:18:56.312 12049-12106 WebSocket               com.dspread.mdm.service              I  🔧 任务结果上传成功
2025-08-19 13:18:56.437 12049-12106 Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"request_time":"1755580736226","org_request_time":"1755580736011","org_request_id":"1755580736011C0906","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755580736226S0000","serialNo":"01610040202307060227"}
2025-08-19 13:18:56.440 12049-12106 WebSocket               com.dspread.mdm.service              I  🔧 收到响应消息: transCode=S0000, orgRequestId=1755580736011C0906, state=0, remark=
2025-08-19 13:18:56.442 12049-12106 WebSocket               com.dspread.mdm.service              I  🔧 处理设备信息响应: state=0
2025-08-19 13:18:56.444 12049-12106 WebSocket               com.dspread.mdm.service              I  🔧 设备信息上传成功
2025-08-19 13:18:57.064 12049-12106 Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"request_time":"1755580736571","org_request_time":"1755580736186","org_request_id":"1755580736186C0109","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755580736571S0000","serialNo":"01610040202307060227"}
2025-08-19 13:18:57.066 12049-12106 WebSocket               com.dspread.mdm.service              I  🔧 收到响应消息: transCode=S0000, orgRequestId=1755580736186C0109, state=0, remark=
2025-08-19 13:18:57.069 12049-12106 WebSocket               com.dspread.mdm.service              I  🔧 终端信息上传成功
2025-08-19 13:18:57.071 12049-12106 WebSocket               com.dspread.mdm.service              I  🔧 终端信息上传成功标志已设置
2025-08-19 13:18:57.082 12049-12106 Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"request_time":"1755580736573","org_request_time":"1755580736244","org_request_id":"1755580736244C0108","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755580736573S0000","serialNo":"01610040202307060227"}
2025-08-19 13:18:57.084 12049-12106 WebSocket               com.dspread.mdm.service              I  🔧 收到响应消息: transCode=S0000, orgRequestId=1755580736244C0108, state=0, remark=
2025-08-19 13:18:57.086 12049-12106 WebSocket               com.dspread.mdm.service              I  🔧 处理任务结果响应: state=0
2025-08-19 13:18:57.087 12049-12106 WebSocket               com.dspread.mdm.service              I  🔧 任务结果上传成功
2025-08-19 13:18:59.699 12049-12049 Receiver                com.dspread.mdm.service              D  🔧 网络恢复，重新上传网络状态信息（防抖通过）
2025-08-19 13:18:59.706 12049-12049 WebSocket               com.dspread.mdm.service              I  🔧 C0904 被动式上送拒绝: NETWORK_RECOVERY (拒绝: 1)
2025-08-19 13:18:59.710 12049-12049 WebSocket               com.dspread.mdm.service              I  🔧 C0904 网络状态上送被流量控制阻止: 被动事件 'NETWORK_RECOVERY' 在平衡模式 - 重要变化下未启用
2025-08-19 13:19:16.623 12049-12106 Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"msg_state":"P","request_time":"1755580756359","data":{"param":"","c_type":"CALLC0109"},"expire_time":1755584356,"tranCode":"SC004","request_id":"1755580756359SC004","version":"1","serialNo":"01610040202307060227"}
2025-08-19 13:19:16.625 12049-12106 WebSocket               com.dspread.mdm.service              I  🔧 准备发送响应确认 - requestId: 1755580756359SC004, needResponse: true
2025-08-19 13:19:16.634 12049-12106 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0000","serialNo":"01610040202307060227","request_time":"1755580756628","request_id":"1755580756628C0000","version":"1","org_request_id":"1755580756359SC004","org_request_time":"1755580756359","response_state":"0","myVersionName":"1.1.02.20250819.DSPREAD.MDM.SERVICE","terminalDate":"20250819131916"}
2025-08-19 13:19:16.644 12049-12106 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0000","serialNo":"01610040202307060227","request_time":"1755580756638","request_id":"1755580756638C0000","version":"1","org_request_id":"1755580756359SC004","org_request_time":"1755580756359","response_state":"0","myVersionName":"1.1.02.20250819.DSPREAD.MDM.SERVICE","terminalDate":"20250819131916"}
2025-08-19 13:19:16.646 12049-12106 WebSocket               com.dspread.mdm.service              I  🔧 已发送C0000响应确认: requestId=1755580756359SC004
2025-08-19 13:19:16.649 12049-12106 WebSocket               com.dspread.mdm.service              I  🔧 处理命令: SC004
2025-08-19 13:19:16.651 12049-12106 WebSocket               com.dspread.mdm.service              I  🔧 收到SC004命令，类型: CALLC0109
2025-08-19 13:19:16.656 12049-12106 WebSocket               com.dspread.mdm.service              I  🔧 服务器请求终端信息上传
2025-08-19 13:19:16.659 12049-12106 WebSocket               com.dspread.mdm.service              I  🔧 C0109 主动式上送: terminal_info_timer (主动: 7)
2025-08-19 13:19:16.665 12049-12106 Platform                com.dspread.mdm.service              D  🔧 重新收集应用信息（强制刷新）
2025-08-19 13:19:16.724 12049-12106 AppManager              com.dspread.mdm.service              I  ℹ️ 应用信息获取完成: 总数120(系统119/用户1) 返回1个
2025-08-19 13:19:16.729 12049-12106 Platform                com.dspread.mdm.service              D  🔧 应用信息: 1 个应用
2025-08-19 13:19:16.800 12049-12106 Platform                com.dspread.mdm.service              D  🔧 内存总容量为： 1.86GB 
2025-08-19 13:19:16.802 12049-12106 Platform                com.dspread.mdm.service              D  🔧 内存使用量为： 0.57GB 
2025-08-19 13:19:16.827 12049-12106 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0109","serialNo":"01610040202307060227","request_time":"1755580756812","request_id":"1755580756812C0109","version":"1","data":{"apkInfo":[{"packName":"com.dspread.mdm.service","apkName":"service","versionCode":21,"versionName":"1.1.02.20250819.DSPREAD.MDM.SERVICE","updateDate":"2025-08-19 13:18:20"}],"locationInfo":{"gps":{"longitude":"-999.0","latitude":"-999.0","satelliteCount":0,"satelliteCountInUsed":0,"satelliteAverSNR":"0.00","distance":"0.0","lockStatus":"1","lockMeter":"200","proId":"","proName":""},"baseSite":{"MCC":"","MNC":"","CID":"-1","LAC":"-1","DBM":"-999","IMSI":"","ICCID":""},"wifi":{"SSID":"fubox_2.4G","IP":"**************","MAC":"52:C3:63:13:8C:EE","SSTH":"-27","BSSID":"7c:10:c9:d6:9f:10"},"address":"无法获取"},"hardwareInfo":[{"name":"CPU Usage","desc":"cpu:0.50"},{"name":"storage_usage_status","desc":" 5.39GB \/ 32.00GB ","state":"1"},{"name":"total_memory","desc":" 0.57GB \/ 1.86GB ","state":"1"},{"name":"androidVersion","desc":"11","state":"1"},{"name":"network_status","desc":"wifi","state":"1"},{"name":"OSVer","desc":"11","state":"1"}],"wifiOption":[{"SSID":"fubox_2.4G","SSTH":"-26"},{"SSID":"2206-5G","SSTH":"-31"},{"SSID":"2206","SSTH":"-39"},{"SSID":"@Ruijie-1816","SSTH":"-52"},{"SSID":"2205_5G","SSTH":"-54"},{"SSID":"2205","SSTH":"-56"},{"SSID":"DIRECT-C6-Mi All-in-One Inkjet","SSTH":"-57"},{"SSID":"2207","SSTH":"-66"},{"SSID":"DIRECT-CC-HP Laser 1188w","SSTH":"-69"},{"SSID":"2207-5G","SSTH":"-82"},{"SSID":"vivo Y51s","SSTH":"-83"}],"sytemInfo":{"androidVersion":"11","buildNumber":"D60_Baseline_debug_20250708_ota_test","aspl":"2021-12-05"}},"myVersionName":"1.1.02.20250819.DSPREAD.MDM.SERVICE","terminalDate":"20250819131916"}
2025-08-19 13:19:16.829 12049-12106 WebSocket               com.dspread.mdm.service              I  🔧 C0109 终端信息上传完成 (1, trigger=terminal_info_timer)
2025-08-19 13:19:16.831 12049-12106 WebSocket               com.dspread.mdm.service              I  🔧 同时上传C0901应用信息（含服务信息）
2025-08-19 13:19:16.833 12049-12106 WebSocket               com.dspread.mdm.service              I  🔧 检查默认开启的服务
2025-08-19 13:19:16.835 12049-12106 WebSocket               com.dspread.mdm.service              I  🔧 本地无LogStream服务信息
2025-08-19 13:19:16.837 12049-12106 WebSocket               com.dspread.mdm.service              I  🔧 LogStream服务未启用，跳过保存
2025-08-19 13:19:16.839 12049-12106 WebSocket               com.dspread.mdm.service              I  🔧 默认服务检查完成
2025-08-19 13:19:16.841 12049-12106 WebSocket               com.dspread.mdm.service              I  🔧 C0901 主动式上送: command_trigger (主动: 8)
2025-08-19 13:19:16.845 12049-12106 Platform                com.dspread.mdm.service              D  🔧 使用缓存的应用信息
2025-08-19 13:19:16.854 12049-12106 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0901","serialNo":"01610040202307060227","request_time":"1755580756847","request_id":"1755580756847C0901","version":"1","data":{"apkInfo":[{"packName":"com.dspread.mdm.service","apkName":"service","versionCode":21,"versionName":"1.1.02.20250819.DSPREAD.MDM.SERVICE","updateDate":"2025-08-19 13:18:20"}],"sytemInfo":{"androidVersion":"11","buildNumber":"D60_Baseline_debug_20250708_ota_test","aspl":"2021-12-05"},"serviceInfo":[]},"myVersionName":"1.1.02.20250819.DSPREAD.MDM.SERVICE","terminalDate":"20250819131916"}
2025-08-19 13:19:16.856 12049-12106 WebSocket               com.dspread.mdm.service              I  🔧 C0901 应用信息上送成功 (1)
2025-08-19 13:19:17.649 12049-12106 Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"request_time":"1755580757222","org_request_time":"1755580756812","org_request_id":"1755580756812C0109","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755580757222S0000","serialNo":"01610040202307060227"}
2025-08-19 13:19:17.652 12049-12106 WebSocket               com.dspread.mdm.service              I  🔧 收到响应消息: transCode=S0000, orgRequestId=1755580756812C0109, state=0, remark=
2025-08-19 13:19:17.654 12049-12106 WebSocket               com.dspread.mdm.service              I  🔧 终端信息上传成功
2025-08-19 13:19:17.656 12049-12106 WebSocket               com.dspread.mdm.service              I  🔧 终端信息上传成功标志已设置
2025-08-19 13:19:21.608 12049-12049 Receiver                com.dspread.mdm.service              D  🔧 电池状态更新: 电池状态: 电量=76%, 温度=29.5°C, 健康=2, 充电=是, 低电量=否, 耗尽=否
2025-08-19 13:19:49.839 12049-12107 WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 1
2025-08-19 13:19:50.507 12049-12106 WebSocket               com.dspread.mdm.service              I  🔧 收到服务器 PONG 帧: 1