<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="56dp"
    android:orientation="vertical">
    <RelativeLayout
        android:id="@+id/toolbar_content_rlyt"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginTop="25dp"
        android:paddingLeft="15dp"
        android:paddingRight="15dp">
        <LinearLayout
            android:id="@+id/toolbar_left_btn"
            android:layout_width="@dimen/dp_50"
            android:layout_height="@dimen/dp_50"
            android:layout_alignParentLeft="true"
            android:layout_centerVertical="true"
            android:gravity="center">
            <ImageView
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:background="@drawable/close_selector"/>
        </LinearLayout>
        <TextView
            android:id="@+id/toolbar_title_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:layout_centerInParent="true"
            android:text="@string/app_name"
            android:textSize="16sp"
            android:textColor="@color/white"/>
        <ImageView
            android:id="@+id/toolbar_right_btn"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_alignParentRight="true"
            android:gravity="center"
            android:layout_centerVertical="true"
            android:visibility="gone"
            android:background="#00000000"
            android:textSize="15sp"
            android:textColor="#fff"/>
        <TextView
            android:id="@+id/toolbar_right_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:gravity="center"
            android:layout_centerVertical="true"
            android:textColor="#fff"
            android:textSize="15sp"
            android:text="更多"
            android:visibility="gone"/>
    </RelativeLayout>
</LinearLayout>
