package com.bbpos.wiseapp.tms.service;

import android.content.Intent;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.util.Log;

import com.bbpos.wiseapp.logger.BBLog;
import com.bbpos.wiseapp.service.WakeLockService;
import com.bbpos.wiseapp.service.common.UsualData;
import com.bbpos.wiseapp.tms.adapter.SystemManagerAdapter;
import com.bbpos.wiseapp.tms.utils.Helpers;
import com.bbpos.wiseapp.tms.utils.ParameterName;

import java.util.Iterator;
import java.util.List;

public class RulebasedApkUninstallService extends WakeLockService {

	public RulebasedApkUninstallService() {
		super("RulebasedApkUninstallService");
	}

	@Override
	public void onDestroy() {
		super.onDestroy();
		Helpers.sendBroad(RulebasedApkUninstallService.this, UsualData.RULEBASED_EXEC_BC);
	}

	@Override
	protected void onHandleIntent(Intent intent) {
		String pkgName = intent.getExtras().getString(ParameterName.pkgName);
		uninstall(pkgName);
	}

	private void uninstall(final String pkgName) {
		if (!isAppInstalled(pkgName)){
			BBLog.i(BBLog.TAG, pkgName+" do not exist.");
			return;
		}
		
		SystemManagerAdapter.unInstallApk(getApplicationContext(), pkgName, new SystemManagerAdapter.ApkUnInstallCompleted() {
			@Override
			public void onDeleteFinished(int returnCode) {
				BBLog.i(BBLog.TAG, "packageDeleted " + pkgName + " returnCode " + returnCode);
			}
		});
	}

    /**
     * 判断应用是否安装 
     * @param pkgName 包名
     * */
    @SuppressWarnings("rawtypes")
	private boolean isAppInstalled(String pkgName){
    	PackageManager pm = this.getPackageManager();
		List installedPackages = pm.getInstalledPackages(0);
		Iterator localIterator = installedPackages.iterator();
		while (localIterator.hasNext()) {
			PackageInfo packageInfo = (PackageInfo) localIterator.next();
			if (packageInfo.applicationInfo.packageName.equals(pkgName)) {
				return true;
			}
		}
		return false;
    }
    
}
