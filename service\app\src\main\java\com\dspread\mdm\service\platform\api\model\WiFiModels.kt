package com.dspread.mdm.service.platform.api.model

/**
 * WiFi连接信息数据类
 */
data class WiFiConnectionInfo(
    val ssid: String?,
    val networkId: Int,
    val rssi: Int,
    val linkSpeed: Int,
    val frequency: Int
) {
    /**
     * 获取信号强度等级 (0-4)
     */
    fun getSignalLevel(): Int {
        return when {
            rssi >= -50 -> 4  // 优秀
            rssi >= -60 -> 3  // 良好
            rssi >= -70 -> 2  // 一般
            rssi >= -80 -> 1  // 较差
            else -> 0         // 很差
        }
    }
    
    /**
     * 获取信号强度描述
     */
    fun getSignalDescription(): String {
        return when (getSignalLevel()) {
            4 -> "优秀"
            3 -> "良好"
            2 -> "一般"
            1 -> "较差"
            else -> "很差"
        }
    }
    
    /**
     * 检查是否为5GHz频段
     */
    fun is5GHz(): Boolean {
        return frequency > 5000
    }
    
    /**
     * 检查是否为2.4GHz频段
     */
    fun is2_4GHz(): Boolean {
        return frequency in 2400..2500
    }
}

/**
 * WiFi扫描结果数据类
 */
data class WiFiScanResult(
    val ssid: String,
    val bssid: String,
    val level: Int,
    val frequency: Int,
    val capabilities: String
) {
    /**
     * 获取安全类型
     */
    fun getSecurityType(): String {
        return when {
            capabilities.contains("WPA3") -> "WPA3"
            capabilities.contains("WPA2") -> "WPA2"
            capabilities.contains("WPA") -> "WPA"
            capabilities.contains("WEP") -> "WEP"
            else -> "OPEN"
        }
    }
    
    /**
     * 检查信号是否足够强
     */
    fun isSignalStrong(): Boolean {
        return level > -70 // 大于-70dBm认为信号较强
    }
    
    /**
     * 获取信号强度等级 (0-4)
     */
    fun getSignalLevel(): Int {
        return when {
            level >= -50 -> 4  // 优秀
            level >= -60 -> 3  // 良好
            level >= -70 -> 2  // 一般
            level >= -80 -> 1  // 较差
            else -> 0          // 很差
        }
    }
    
    /**
     * 获取信号强度描述
     */
    fun getSignalDescription(): String {
        return when (getSignalLevel()) {
            4 -> "优秀"
            3 -> "良好"
            2 -> "一般"
            1 -> "较差"
            else -> "很差"
        }
    }
    
    /**
     * 检查是否为5GHz频段
     */
    fun is5GHz(): Boolean {
        return frequency > 5000
    }
    
    /**
     * 检查是否为2.4GHz频段
     */
    fun is2_4GHz(): Boolean {
        return frequency in 2400..2500
    }
    
    /**
     * 检查是否为隐藏网络
     */
    fun isHidden(): Boolean {
        return ssid.isEmpty() || ssid.isBlank()
    }
    
    /**
     * 检查是否支持WPS
     */
    fun supportsWPS(): Boolean {
        return capabilities.contains("WPS")
    }
}

/**
 * WiFi网络状态枚举
 */
enum class WiFiNetworkState {
    DISABLED,           // 禁用
    DISCONNECTED,       // 断开连接
    CONNECTING,         // 连接中
    CONNECTED,          // 已连接
    SUSPENDED,          // 暂停
    DISCONNECTING,      // 断开连接中
    FAILED,             // 连接失败
    BLOCKED,            // 被阻止
    TEMPORARILY_DISABLED // 临时禁用
}

/**
 * WiFi操作结果
 */
sealed class WiFiOperationResult {
    object Success : WiFiOperationResult()
    data class Failure(val error: String, val exception: Throwable? = null) : WiFiOperationResult()
    data class PartialSuccess(val successCount: Int, val totalCount: Int, val errors: List<String>) : WiFiOperationResult()
}

/**
 * WiFi配置验证结果
 */
data class WiFiConfigValidationResult(
    val isValid: Boolean,
    val errors: List<String> = emptyList(),
    val warnings: List<String> = emptyList()
) {
    fun hasErrors(): Boolean = errors.isNotEmpty()
    fun hasWarnings(): Boolean = warnings.isNotEmpty()
    
    fun getErrorMessage(): String = errors.joinToString("; ")
    fun getWarningMessage(): String = warnings.joinToString("; ")
}
