package com.bbpos.wiseapp.tms.utils;

import android.util.Log;

import java.security.InvalidAlgorithmParameterException;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;

import android.util.Base64;

import javax.crypto.BadPaddingException;
import javax.crypto.Cipher;
import javax.crypto.IllegalBlockSizeException;
import javax.crypto.NoSuchPaddingException;
import javax.crypto.SecretKey;
import javax.crypto.spec.GCMParameterSpec;
import javax.crypto.spec.SecretKeySpec;

/**
 * AES/CBC/NoPadding
 * 针对7-11 wifi pwd 的解密工具
 * <p>
 * 算法参数：
 * Algorithm: AES/CBC/NoPadding
 * IV: 0000000000000000
 * Encryption/Decryption Key: A72BB1524BE493C3C495D12ABA2C667D9B02270E8755FE6668C529C55A0CB186
 * Remarks: The decrypted result shall be in Hexadecimal format, please convert it to ASCII to get the actual WiFi password
 * <p>
 * example：
 * try {
 * final byte[] content = AESUtil.decodeHex("37F438E40CD005638629B7E39BB56C81".toCharArray());
 * final byte[] key = AESUtil.decodeHex("A72BB1524BE493C3C495D12ABA2C667D9B02270E8755FE6668C529C55A0CB186".toCharArray());
 * //                    final byte[] iv = AESUtil.decodeHex("00000000000000000000000000000000".toCharArray());
 * final byte[] iv = AESUtil.decodeHex(LiAesTool.create256BitsIV("0000000000000000"));
 * System.out.println(AESUtil.printData(iv, iv.length));
 * final byte[] decrypt = AESUtil.decrypt(content, key, iv);
 * System.out.println(AESUtil.printData(decrypt, decrypt.length));//获得wifi pwd值 ：myPassword
 * } catch (Exception e) {
 * e.printStackTrace();
 * }
 */
 //原解密方式，暂作废

public class AESUtil {

    // AES-GCM parameters
    public static final int AES_KEY_SIZE = 128; // in bits
    public static final int GCM_NONCE_LENGTH = 12; // in bytes
    public static final int GCM_TAG_LENGTH = 16; // in bytes

    /**
     * Helper converting hex string to string e.g. "7a4b7a78476d58513766333730626464" to "zKzxGmXQ7f370bdd"
     *
     * @param[hex] hex string like "7a4b7a78476d58513766333730626464"
     * @return[string] string from input hex string.
     */
    public static String convertHexToString(String hex) {
        StringBuilder sb = new StringBuilder();
        StringBuilder temp = new StringBuilder();

        // 49204c6f7665204a617661 split into two characters 49, 20, 4c...
        for (int i = 0; i < hex.length() - 1; i += 2) {
            String output = hex.substring(i, (i + 2));
            int decimal = Integer.parseInt(output, 16);
            sb.append((char) decimal);
            temp.append(decimal);
        }
        return sb.toString();
    }

    /**
     * Generate SecretKey instance for using during encryption or decryption.
     *
     * @param[password] The 16 bytes encryption key in hex string format e.g. "7a4b7a78476d58513766333730626464"
     * @return[SecretKey] SecretKey instance.
     */
    public static SecretKey generateSecretKey(String password) {
        return new SecretKeySpec(password.getBytes(), "AES");
    }

    /**
     * Generate hex string from bytes array.
     *
     * @param[bytes] Data in bytes array.
     * @return[hexString] Hex string of input data.
     */
    public static String bytesToHex(byte[] bytes) {
        char[] hexArray = "0123456789ABCDEF".toCharArray();
        char[] hexChars = new char[bytes.length * 2];
        for (int j = 0; j < bytes.length; j++) {
            int v = bytes[j] & 0xFF;
            hexChars[j * 2] = hexArray[v >>> 4];
            hexChars[j * 2 + 1] = hexArray[v & 0x0F];
        }
        return new String(hexChars);
    }

    /**
     * Encrypt byte plain text and return byte cipher.
     *
     * @param[hexKeyString] The 16 bytes encryption key in hex string format e.g. "7a4b7a78476d58513766333730626464"
     * @param[hexIvString] The 12 bytes IV in hex string format e.g. "36623865" + "3466363851555034"
     * @return[bytePlain] Cipher text in byte array format.
     */
    public static byte[] encrypt(byte[] bytePlain, String hexKeyString, String hexIvString) {
        byte[] cipherText = null;

        try {
            byte[] input = bytePlain;

            String keyString = convertHexToString(hexKeyString);
            String ivString = convertHexToString(hexIvString);
            SecretKey key;

            key = generateSecretKey(keyString);

            System.out.println("[+] Key: " + keyString);
            System.out.println("[+] IV: " + ivString);

            Cipher cipher = Cipher.getInstance("AES/GCM/NoPadding");
            byte[] nonce = new byte[GCM_NONCE_LENGTH];

            nonce = ivString.getBytes();
            GCMParameterSpec spec = new GCMParameterSpec(GCM_TAG_LENGTH * 8, nonce);
            cipher.init(Cipher.ENCRYPT_MODE, key, spec);

            // This is an optional, so just hard coded it for now.
            byte[] aad = "ABCDEFGHIJKL".getBytes();
            cipher.updateAAD(aad);

            cipherText = cipher.doFinal(input);
        } catch (NoSuchAlgorithmException | NoSuchPaddingException | InvalidKeyException
                | InvalidAlgorithmParameterException | IllegalBlockSizeException | BadPaddingException e) {

            e.printStackTrace();
        }
        return cipherText;
    }

    /**
     * Decrypt byte cipher and return byte plain text.
     *
     * @param[hexKeyString] The 16 bytes encryption key in hex string format e.g. "7a4b7a78476d58513766333730626464"
     * @param[hexIvString] The 12 bytes IV in hex string format e.g. "36623865" + "3466363851555034"
     * @return[bytePlain] Plain text data in byte array format.
     */
    public static byte[] decrypt(byte[] byteCipher, String hexKeyString, String hexIvString) {
        byte[] bytePlain = null;

        try {
            byte[] input = byteCipher;

            String keyString = convertHexToString(hexKeyString);
            String ivString = convertHexToString(hexIvString);
            SecretKey key;

            key = generateSecretKey(keyString);

//            System.out.println("[+] Key: " + keyString);
//            System.out.println("[+] IV: " + ivString);

            Cipher cipher = Cipher.getInstance("AES/GCM/NoPadding");
            byte[] nonce = new byte[GCM_NONCE_LENGTH];

            nonce = ivString.getBytes();
            GCMParameterSpec spec = new GCMParameterSpec(GCM_TAG_LENGTH * 8, nonce);
            cipher.init(Cipher.DECRYPT_MODE, key, spec);

            // This is an optional, so just hard coded it for now.
            byte[] aad = "ABCDEFGHIJKL".getBytes();
            cipher.updateAAD(aad);

            bytePlain = cipher.doFinal(input);
        } catch (NoSuchAlgorithmException | NoSuchPaddingException | InvalidKeyException
                | InvalidAlgorithmParameterException | IllegalBlockSizeException | BadPaddingException e) {

            e.printStackTrace();
        }

        return bytePlain;
    }

    private static String keyHexString = "7a4b7a78476d58513766333730626464";
    private static String ivHexString = "366238653466363851555034";

    public static String encryptToHex(String strData) {
        String base64Cipher = "";
        try {
            byte[] byteCipher = encrypt(strData.getBytes(), keyHexString, ivHexString);
            base64Cipher = Base64.encodeToString(byteCipher, Base64.DEFAULT);
            Log.d("KAPI-DEBUG","[+] Encoded cipher: " + base64Cipher);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return base64Cipher;
    }

    public static String decryptHexToString(String hexData) {
        String decryptedText = "";
        try {
            byte[] byteCipher = Base64.decode(hexData,Base64.DEFAULT);
            byte[] bytePlain = decrypt(byteCipher, keyHexString, ivHexString);
            decryptedText = convertHexToString(bytesToHex(bytePlain));
//            Log.d("KAPI-DEBUG","[+] Decoded plain: " + decryptedText);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return decryptedText;
    }

    public static void test() {
//        Log.d("KAPI-DEBUG","##### Encrypting #####");
//        String text = "this is the test text";
//        String keyHexString = "7a4b7a78476d58513766333730626464";
//
//        // IV is 12-byte random number and considered as a nonce in AES/GCM.
//        // This nonce must be unique so that for each encryption with the same key the nonce should not be repeat
//        // In RFC5288 the 3rd section (https://tools.ietf.org/html/rfc5288#section-3) inform about handling IV
//        // IV is composed of 4 bytes salt and 8 bytes nonce_explicit
//        // The first 4 bytes of IV should be chosen during handshake and keep it secretly on both sides.
//        // The second 8 bytes of IV can be sent in clear-text along with the encrypted data
//        // Each value of the nonce_explicit MUST be distinct for each distinct
//        // invocation of the GCM encrypt function for any fixed key.  Failure to
//        // meet this uniqueness requirement can significantly degrade security.
//        // The nonce_explicit MAY be the 64-bit sequence number.
//        String ivSaltHex = "36623865";
//        String nonceHex = "3466363851555034";
//        String ivHexString = ivSaltHex + nonceHex;
//
//        byte[] byteCipher = encrypt(text.getBytes(), keyHexString, ivHexString);
//        byte[] tag = Arrays.copyOfRange(byteCipher, byteCipher.length - ((GCM_TAG_LENGTH * 8) / Byte.SIZE),
//                byteCipher.length);
//
//        // This line utilizes Base64 of java.util.Base64
//        // String base64Cipher = Base64.getEncoder().encodeToString(byteCipher);
//
//        // For Android app use the following line
//        String base64Cipher = Base64.encodeToString(byteCipher, Base64.DEFAULT);
//
//
//        String nonce = convertHexToString(ivHexString);
//
//        Log.d("KAPI-DEBUG","[+] Plain: " + text);
//        Log.d("KAPI-DEBUG","[+] Cipher(hex): " + bytesToHex(byteCipher));
//        Log.d("KAPI-DEBUG","[+] Tag(hex): " + bytesToHex(tag));
//        Log.d("KAPI-DEBUG","[+] Encoded cipher: " + base64Cipher);
//        Log.d("KAPI-DEBUG","[+] Nonce: " + nonce);
//
//        Log.d("KAPI-DEBUG","\n##### Decrypting #####");
//
//        byte[] tagFromCipher = Arrays.copyOfRange(byteCipher, byteCipher.length - ((GCM_TAG_LENGTH * 8) / Byte.SIZE),
//                byteCipher.length);
//
//        // ivSalt is known implicitly and chosen during handshake. Nonce is carried explicitly for each packets.
//        byte[] bytePlain = decrypt(byteCipher, keyHexString, (ivSaltHex + nonceHex));
//        String decryptedText = convertHexToString(bytesToHex(bytePlain));
//
//        Log.d("KAPI-DEBUG","[+] Plain(hex): " + bytesToHex(bytePlain));
//        Log.d("KAPI-DEBUG","[+] Tag(hex): " + bytesToHex(tagFromCipher));
//        Log.d("KAPI-DEBUG","[+] Decoded plain: " + decryptedText);
//        Log.d("KAPI-DEBUG","[+] Nonce: " + nonce);
//
//        Log.d("KAPI-DEBUG",""+text.equals(decryptedText));


        Log.d("KAPI-DEBUG","\n##### ---------------- #####");
        encryptToHex("");
        decryptHexToString("");
        Log.d("KAPI-DEBUG","\n##### ---------------- #####");
    }


//    private static final String ALGORITHM = "AES/CBC/NoPadding";
//    private static final char[] DIGITS_LOWER = {'0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'a', 'b', 'c', 'd', 'e', 'f'};
//    private static final char[] DIGITS_UPPER = {'0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'A', 'B', 'C', 'D', 'E', 'F'};
//    public static final String AESKey = "A72BB1524BE493C3C495D12ABA2C667D9B02270E8755FE6668C529C55A0CB186";
//    public static final String IV = "0000000000000000";

//    public static final byte[] encrypt(final byte[] srcData, final byte[] key, final byte[] iv) throws Exception {
//        final SecretKeySpec keySpec = new SecretKeySpec(key, "AES");
//        final Cipher cipher = Cipher.getInstance(ALGORITHM);
//        cipher.init(Cipher.ENCRYPT_MODE, keySpec, new IvParameterSpec(iv));
//        return cipher.doFinal(srcData);
//    }
//
//    public static final byte[] decrypt(final byte[] encData, final byte[] key, final byte[] iv) throws Exception {
//        final SecretKeySpec keySpec = new SecretKeySpec(key, "AES");
//        final Cipher cipher = Cipher.getInstance(ALGORITHM);
//        cipher.init(Cipher.DECRYPT_MODE, keySpec, new IvParameterSpec(iv));
//        return cipher.doFinal(encData);
//    }
//
//    public static final char[] encodeHex(final byte[] data) {
//        return encodeHex(data, true);
//    }
//
//    public static final char[] encodeHex(final byte[] data, final boolean toLowerCase) {
//        return encodeHex(data, toLowerCase ? DIGITS_LOWER : DIGITS_UPPER);
//    }
//
//    protected static final char[] encodeHex(final byte[] data, final char[] toDigits) {
//        final int l = data.length;
//        final char[] out = new char[l << 1];
//        for (int i = 0, j = 0; i < l; i++) {
//            out[j++] = toDigits[(0xF0 & data[i]) >>> 4];
//            out[j++] = toDigits[0x0F & data[i]];
//        }
//        return out;
//    }
//
//    public static final String encodeHexStr(final byte[] data) {
//        return encodeHexStr(data, true);
//    }
//
//    public static final String encodeHexStr(final byte[] data, final boolean toLowerCase) {
//        return encodeHexStr(data, toLowerCase ? DIGITS_LOWER : DIGITS_UPPER);
//    }
//
//    public static final String encodeHexStr(final byte[] data, final char[] toDigits) {
//        return new String(encodeHex(data, toDigits));
//    }
//
//    public static final byte[] decodeHex(final char[] data) {
//        final int len = data.length;
//        if ((len & 0x01) != 0) {
//            throw new RuntimeException("Odd number of characters.");
//        }
//        final byte[] out = new byte[len >> 1];
//        for (int i = 0, j = 0; j < len; i++) {
//            int f = toDigit(data[j], j) << 4;
//            j++;
//            f = f | toDigit(data[j], j);
//            j++;
//            out[i] = (byte) (f & 0xFF);
//        }
//        return out;
//    }
//
//    private static final int toDigit(final char ch, final int index) {
//        final int digit = Character.digit(ch, 16);
//        if (digit == -1) {
//            throw new RuntimeException("Illegal hexadecimal character " + ch + " at index " + index);
//        }
//        return digit;
//    }
//
//    public static final String printData(final byte[] data, final int length) {
//        final StringBuffer stringbuffer = new StringBuffer();
//        int j = 0;
//        for (int k = 0; k < length; k++) {
//            if (j % 16 == 0) {
//                stringbuffer.append((new StringBuilder()).append(fillHex(k, 4)).append(": ").toString());
//            }
//            stringbuffer.append((new StringBuilder()).append(fillHex(data[k] & 0xff, 2)).append(" ").toString());
//            if (++j != 16) {
//                continue;
//            }
//            stringbuffer.append("   ");
//            int i1 = k - 15;
//            for (int l1 = 0; l1 < 16; l1++) {
//                final byte byte0 = data[i1++];
//                if (byte0 > 31 && byte0 < 128) {
//                    stringbuffer.append((char) byte0);
//                } else {
//                    stringbuffer.append('.');
//                }
//            }
//            stringbuffer.append("\n");
//            j = 0;
//        }
//        int l = data.length % 16;
//        if (l > 0) {
//            for (int j1 = 0; j1 < 17 - l; j1++) {
//                stringbuffer.append("   ");
//            }
//            int k1 = data.length - l;
//            for (int i2 = 0; i2 < l; i2++) {
//                final byte byte1 = data[k1++];
//                if (byte1 > 31 && byte1 < 128) {
//                    stringbuffer.append((char) byte1);
//                } else {
//                    stringbuffer.append('.');
//                }
//            }
//            stringbuffer.append("\n");
//        }
//        return stringbuffer.toString();
//    }
//
//    public static final String getOrgData(final byte[] data, final int length) {
//
//        final StringBuffer stringbuffer = new StringBuffer();
//        int j = 0;
//        for (int k = 0; k < length; k++) {
//            if (++j != 16) {
//                continue;
//            }
//            int i1 = k - 15;
//            for (int l1 = 0; l1 < 16; l1++) {
//                final byte byte0 = data[i1++];
//                if (byte0 > 31 && byte0 < 128) {
//                    stringbuffer.append((char) byte0);
//                }
//            }
//            j = 0;
//        }
//        return stringbuffer.toString();
//    }
//
//    private static final String fillHex(int i, int j) {
//        String s = Integer.toHexString(i);
//        for (int k = s.length(); k < j; k++) {
//            s = (new StringBuilder()).append("0").append(s).toString();
//        }
//        return s;
//    }
//
//
//    /**
//     * 创建256位的偏移量，iv的长度小于32时后面补0，大于32，截取前32个字符;
//     *
//     * @param iv
//     * @return
//     */
//    public static char[] create256BitsIV(String iv) {
//        if (iv == null) {
//            iv = null;
//        }
//        char[] data = null;
//        StringBuffer buffer = new StringBuffer(32);
//        buffer.append(iv);
//        while (buffer.length() < 32) {
//            buffer.append("0");
//        }
//        if (buffer.length() > 32) {
//            buffer.setLength(32);
//        }
//        data = buffer.toString().toCharArray();
//        return data;
//    }
}
