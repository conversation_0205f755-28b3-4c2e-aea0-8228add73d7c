<resources xmlns:xliff="urn:oasis:names:tc:xliff:document:1.2">

    <string name="app_name">WiseAppService</string>
    <string name="settings">Settings</string>
    <string name="display">Display</string>
    <string name="network">Wireless &amp; Network</string>
    <string name="brightnesslevel">Brightness level</string>
    <string name="language">Language</string>
    <string name="wifi">Wi-Fi</string>
    <string name="bluetooth">Bluetooth</string>

    <string name="on">On</string>
    <string name="off">Off</string>
    <string name="wifi_error">wifi error</string>
    <string name="wifi_in_airplane_mode">"In aeroplane mode"</string>
    <string name="wifi_empty_list_wifi_off">"To see available networks, turn Wi‑Fi on."</string>
    <string name="wifi_empty_list_wifi_on">"Searching for Wi‑Fi networks…"</string>
    <string name="wifi_starting">"Turning Wi‑Fi on…"</string>
    <string name="wifi_stopping">"Turning off Wi‑Fi…"</string>
    <string name="add_wifi">"Add another network"</string>
    <string name="select_wifi">"Select Wi-Fi network"</string>
    <string name="skip">"SKIP"</string>
    <string name="wifi_band_24ghz">"2.4 GHz"</string>
    <string name="wifi_band_5ghz">"5 GHz"</string>

    <string name="wifi_ssid">Network name</string>
    <!-- Hint for a text field to enter the SSID of a hidden wifi network. [CHAR LIMIT=35] -->
    <string name="wifi_ssid_hint">Enter the SSID</string>
    <!-- Label for the security of the connection -->
    <string name="wifi_security">Security</string>
    <!-- Label for the signal strength of the connection -->
    <string name="wifi_signal">Signal strength</string>
    <!-- Label for the status of the connection -->
    <string name="wifi_status">Status</string>
    <!-- Label for the link speed of the connection -->
    <string name="wifi_speed">Link speed</string>
    <!-- Label for the frequency band of the connection -->
    <string name="wifi_frequency">Frequency</string>
    <!-- Label for the IP address of the connection -->
    <string name="wifi_ip_address">IP address</string>
    <!-- Hint text for the IP address -->
    <string name="wifi_ip_address_hint" translatable="false">*************</string>
    <string name="wifi_display_status_connecting">"Connecting"</string>
    <string name="wifi_display_status_connected">"Connected"</string>
    <string name="wifi_display_status_in_use">"In use"</string>
    <string name="wifi_display_status_not_available">"Unavailable"</string>
    <!-- Button label to connect to a Wi-Fi network -->
    <string name="wifi_connect">Connect</string>
    <!-- Failured notification for connect -->
    <string name="wifi_failed_connect_message">Failed to connect to network</string>
    <!-- Button label to delete a Wi-Fi network -->
    <string name="wifi_forget">Forget</string>
    <!-- Failured notification for forget -->
    <string name="wifi_failed_forget_message">Failed to forget network</string>
    <!-- Button label to save a Wi-Fi network configuration -->
    <string name="wifi_save">Save</string>
    <!-- Failured notification for save -->
    <string name="wifi_failed_save_message">Failed to save network</string>
    <!-- Button label to dismiss the dialog -->
    <string name="wifi_cancel">Cancel</string>
    <!-- Button for skipping a step after having been warned of a potential concern [CHAR LIMIT=30] -->
    <string name="wifi_skip_anyway">Skip anyway</string>
    <!-- Button for going to the previous screen or step [CHAR LIMIT=20] -->
    <string name="wifi_dont_skip">Don\'t skip</string>

    <string name="connected">Connected</string>
    <string name="show_pwd">Show password</string>
    <string name="advanced_option">Advanced options</string>
    <string name="apply">Apply</string>
    <string name="cancel">Cancel</string>
    <string name="reset">Reset</string>
    <string name="connect">Connect</string>
    <string name="pwd">Password</string>
    <string name="wifi_security_wapi_psk">WAPI PSK</string>
    <string name="wifi_security_wapi_certificate">WAPI Certificate</string>
    <string name="scanning">Scanning</string>
    <string name="connecting">Connecting</string>
    <string name="obtaining">Obtaining IP address</string>
    <string name="disconnecting">Disconnecting</string>
    <string name="disconnected">Disconnected</string>
    <string name="connect_failed">Connect failed</string>

    <string name="english">English</string>
    <string name="chinese_simplified">中文(简体)</string>
    <string name="chinese_traditional">中文(繁體)</string>

    <string name="bluetooth_error">"bluetooth error"</string>
    <string name="bluetooth_starting">"Turning bluetooth on…"</string>
    <string name="bluetooth_stopping">"Turning off bluetooth…"</string>
    <string name="bluetooth_preference_scan_title">"Scan for devices"</string>
    <string name="bluetooth_search_for_devices">"Refresh"</string>
    <string name="bluetooth_searching_for_devices">"Searching…"</string>
    <string name="bluetooth_preference_device_settings">"Device settings"</string>
    <string name="bluetooth_preference_paired_dialog_title">"Paired device"</string>
    <string name="bluetooth_preference_paired_dialog_name_label">"Name"</string>
    <string name="bluetooth_preference_paired_dialog_internet_option">"Internet connection"</string>
    <string name="bluetooth_preference_paired_dialog_keyboard_option">"Keyboard"</string>
    <string name="bluetooth_preference_paired_dialog_contacts_option">"Contacts and call history"</string>
    <string name="bluetooth_pairing_dialog_title">"Pair with this device?"</string>
    <string name="bluetooth_pairing_dialog_sharing_phonebook_title">"Share phone book?"</string>
    <string name="bluetooth_preference_paired_devices">"Paired devices"</string>
    <string name="bluetooth_preference_found_devices">"Available devices"</string>
    <string name="bluetooth_preference_no_found_devices">"No devices available"</string>
    <string name="bluetooth_device_context_connect">"Connect"</string>
    <string name="bluetooth_device_context_disconnect">"Disconnect"</string>
    <string name="bluetooth_device_context_pair_connect">"Pair &amp; connect"</string>
    <string name="bluetooth_device_context_unpair">"Unpair"</string>
    <string name="bluetooth_device_context_disconnect_unpair">"Disconnect &amp; unpair"</string>
    <string name="bluetooth_device_context_connect_advanced">"Options…"</string>
    <string name="bluetooth_menu_advanced">"Advanced"</string>
    <string name="bluetooth_advanced_titlebar">"Advanced Bluetooth"</string>
    <string name="bluetooth_empty_list_bluetooth_off">"When Bluetooth is turned on, your device can communicate with other nearby Bluetooth devices."</string>
    <string name="bluetooth_is_visible_message"><xliff:g id="device_name">%1$s</xliff:g> is visible to nearby devices while Bluetooth Settings is open.</string>

    <string name="bluetooth_pairing">"Pairing…"</string>
    <string name="bluetooth_unbond">"forgot"</string>
    <string name="bluetooth_done">"done"</string>

    <string name="activity_login_hint">PASSWORD</string>
    <string name="activity_login_error">Password error</string>
    <string name="update_tip_dialog_title">Update tips</string>
    <string name="update_tip_dialog_message">Terminal has an update task, can be updated immediately, or delayed update for</string>
    <string name="update_tip_dialog_btn_update_imtl">Right now</string>
    <string name="update_tip_dialog_btn_after_update_pre">Delayed(</string>
    <string name="update_tip_dialog_btn_after_update_suf">)</string>
    <string name="update_tip_dialog_after_some_time_update">\r\n(right now)</string>
    <string name="confirm">confirm</string>
    <string name="update_tip_min_after">minutes later</string>
    <string name="tip">Tip</string>
    <string name="empty_app_data">Whether to empty the current application data?</string>
    <string name="empty">Empty</string>
    <string name="timeout_and_no_response">timeout and no response</string>
    <string name="install_failed">install failed</string>

    <string name="install_tip_dialog_title">Install tips</string>
    <string name="install_tip_dialog_message">OS patch is available to download and install.</string>
    <string name="install_tip_dialog_message_low_power">Terminal has an install task, power is too low, please charge </string>
    <string name="install_apk_tip_dialog_message_low_power">Terminal has an apps install task, power is too low, please charge</string>
    <string name="install_apk_tip_dialog_message">%1$s has an upgrade, if install immediately，or delayed install?</string>
    <string name="install_tip_dialog_btn_install_imtl">Right now</string>
    <string name="install_tip_dialog_btn_after_install_pre">Delayed</string>

    <string name="update_shade_updating">Being updated……</string>
    <string name="update_shade_update_tip">Software Update</string>
    <string name="update_shade_result_tip">Update results</string>
    <string name="update_shade_result_success">Update success</string>
    <string name="update_shade_result_download_faile">Download failed</string>
    <string name="update_shade_result_update_faile">Update failed</string>
    <string name="update_shade_result_file_init_error">File not generated</string>

    <string name="strPic">LOGO pictures</string>
    <string name="strTextBottom"> POS is locked，Please contact customer manager.</string>
    <string name="strServiceUninstall">Cloude Service is Lack</string>
    <string name="manu">manu</string>
    <string name="serial">serial</string>
    <string name="model">model</string>
    <string name="servicever">serviceVer</string>
    <string name="tmsver">TMSVer</string>
    <string name="deviceinfo">Device Info</string>
    <string name="urlinfo">Url Info</string>
    <string name="save">Save</string>
    <string name="mshowurl">Show URL</string>
    <string name="mshowdeviceinfo">Show Device Info</string>
    <string name="mstartpoll">Start Poll</string>
    <string name="muploadTermInfo">Upload Terminal Info</string>
    <string name="mLocalUpdate">Local Update</string>
    <string name="mbackupfile">Backup File</string>
    <string name="task_end">task end</string>

    <string name="input_name">Please input user name</string>
    <string name="input_passwd">Please input user pwd</string>
    <string name="login">Login</string>

    <string name="usage">1. Copy the generated OTA upgrade file to the U-disk root directory;\n2. Click "Search" button to find upgrade file;\n3. Click "Update" button to update.</string>
    <string name="check">Search</string>
    <string name="update">Update</string>
    <string name="click_check">Please click "Search" button to find upgrade file!</string>

    <string name="str_find">Find&#160;</string>
    <string name="str_num_files">&#160;file(s)</string>

    <string name="str_begin_copy">Copying file……</string>
    <string name="str_begin_install">Updating system……</string>
    <string name="str_begin_verify">Verifying upgrade file……</string>
    <string name="str_install_err">Update failed!No permission?</string>
    <string name="str_verify_err">Verify upgrade file failed!</string>
    <string name="str_verify_file_err">Read upgrade file failed!</string>
    <string name="str_verify_ok">Verify upgrade file success!</string>

    <string name="str_verify">Verify upgrade file</string>
    <string name="str_verifying">Verifying upgrade file,please wait……\n</string>

    <string name="str_copy2sd">Copy upgrade file to internal storage</string>
    <string name="str_copying">Copying upgrade file,please wait……\n</string>

    <string name="str_copy_ok">Copy upgrade file success!</string>
    <string name="str_copy_fail1">Copy failed,files are different size!</string>
    <string name="str_copy_fail2">Copy failed,file does not exist!</string>

    <string name="app_download">Apps install download</string>
    <string name="ota_download">OTA upgrade download</string>
    <string name="provisioning_download">Provisioning download</string>
    <string name="downloading">Downloading</string>
    <string name="download_complete">Download complete</string>
    <string name="download_failed">Download failed</string>
    <string name="ota_install_failed">OTA upgrade package installation: install failure</string>
    <string name="ota_check_failed_1">OTA update package check: file read failure</string>
    <string name="ota_check_failed_2">OTA upgrade package check: file error or non compliant specification</string>
    <string name="no_list_to_upload">No list to upload!</string>
    <string name="obtain_bs_failed">Failure to obtain base station information</string>
    <string name="server_response_error">Server response error</string>
    <string name="app_download_failed">Application package download failure</string>
    <string name="lock_screen">Lock screen</string>
    <string name="unlock_screen">Unlock screen</string>
    <string name="msg_no_update">Already be the latest version</string>
    <string name="ota_download_completed">OTA upgrade package download completed</string>
    <string name="ota_download_failed">OTA upgrade package download failed</string>
    <string name="bt_turn_on">Bluetooth turned on</string>
    <string name="bt_turn_off">Bluetooth turned off</string>
    <string name="wifi_turn_on">WIFI turned on</string>
    <string name="wifi_turn_off">WIFI turned off</string>
    <string name="download_upgrade_package">Download upgrade package: </string>
    <string name="check_upgrade_package">Check upgrade package: </string>
    <string name="install_upgrade_package">Install upgrade package</string>
    <string name="install_upgrade_package_tip">sec before installing upgrade package</string>
    <string name="upgrade_now">Upgrade now</string>
    <string name="upgrade_after_5min">Upgrade after 5 min</string>
    <string name="prompt_for_ota">A OTA upgrade requiring device to restart</string>
    <string name="reboot_now">Reboot now</string>
    <string name="reboot_after_5min">Reboot after 5min</string>
    <string name="expire_to_reboot">Terminal will automatically reboot at the end of the countdown.</string>

    <string name="storage_usage_status">storage_usage_status</string>
    <string name="total_memory">total_memory</string>
    <string name="network_status">network_status</string>
    <string name="cpu_usage">CPU Usage</string>

    <string name="wise_scan">WiseScan</string>
    <string name="prompt">prompt</string>
    <string name="prompt_sys_maintenance">System maintenance</string>
    <string name="prompt_osupdate_reboot">OS upgrade package is installed and needs to be restarted</string>
    <string name="low_battery">Low battery</string>
    <string name="scan_text">Put the QR code in the box</string>
    <string name="prompt_install">Confirm installation</string>
    <string name="prompt_install_content1">Are you sure you want to install version</string>
    <string name="install">Install</string>
    <string name="qrcode_invalid">QRCode Invalid</string>

    <string name="prompt_sysInfo_verfiy">System Info Verfiy Result</string>
    <string name="qrcode_ok">OK</string>
    <string name="system_info_verify_info">System info verify fail. Please CHECK above info !</string>
    <string name="system_info_verify_tip1">Please Check Below Info !</string>
    <string name="system_info_verify_tip">Please compare with fuselage label info !</string>
    <string name="system_info_verify_fail_back">System info verify fail.Please check !</string>

    <string name="provisioning_fail">Provisioning fail,please try again</string>
    <string name="provisioning_fail_tips">Provisioning fail,Please reboot and retry</string>
    <string name="provisioning_fail_retry">Provisioning fail,retry %d </string>
    <string name="provisioning_success">Provisioning success</string>
    <string name="provisioning_config_fail">Fail to get config </string>

    <string name="later">LATER</string>
    <string name="install_now">INSTALL NOW</string>

    <string name="location_prompt">Getting location information, please go to the open space outside.</string>
    <string name="unbox_finish">Initialization completed,terminal needs to reboot now.</string>
    <string name="unbox_reset">Factory reset</string>
    <string name="accessibility_service_description">accessibility_service_description</string>
    <string name="unbox_tip_dialog_message_reset">Reset the device to its default setting</string>

    <string name="tmt_update_failed">TMT upgrade failed, try again</string>
    <string name="tmt_update_completed">TMT upgrade completed, will reboot now</string>

    <string name="find_my_device">Find my device</string>
    <string name="device_alert">Device Alert</string>

    <!-- GEO -->
    <string name="device_locked">DEVICE LOCKED</string>
    <string name="out_of_geofence">OUT OF GEOFENCE</string>
    <string name="data_wiped">data wiped</string>
    <string name="wiping_data">wiping data</string>
    <string name="mins_till_wiping_data"> mins till wiping data</string>
    <string name="error_input">Error Input</string>
    <string name="incorrect_password">Incorrect Password</string>
    <string name="otp_expire">OTP expire.Please generate a new one.</string>
    <string name="no_verify_pwd">Please obtain your OTP first</string>
    <string name="goto_unlock_device">Unlock Device</string>
    <string name="unlock_device">Unlock Device</string>
    <string name="password">Password</string>
    <string name="nonetwork">No network</string>
    
    <string name="download_using_wifi_only">Download using Wi-Fi only</string>
    <string name="download_prompt">Please connect to Wi-Fi.</string>

</resources>
