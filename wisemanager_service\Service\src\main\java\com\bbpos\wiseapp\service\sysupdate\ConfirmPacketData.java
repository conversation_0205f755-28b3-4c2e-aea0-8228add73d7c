package com.bbpos.wiseapp.service.sysupdate;

import java.util.ArrayList;
import java.util.HashMap;

/** 标准确认包结构
 * 确认包可能是厂商zip包、APK或两者组合，是一个描述信息，纯文本字串
 * 单个格式(ota、apk、pkg):MD5+FileName+Version+Type+Operate+Update+FileLen，可能还有其他字段
 * 多个的格式由多个叠加组成一个字串,各项之间由回车换行
 * 例子：MD5:xxx
 *     FileName:xxx
 *     Version:xxx
 *     Type:APK/PATCH/OS/CRT
 *     Operate:0x00-不清除数据 0x01-清除数据
 *     Update:0x00-定时更新，0x01-删除， 0x02-立即更新
 * */
public class ConfirmPacketData{
	private static String MD5Key = "MD5";
	private static String filenameKey = "FileName";
	private static String versionKey = "Version";
	private static String typeKey = "Type";
	private static String operateKey = "Operate";
	private static String updateKey = "Update";
	private static String fileLenKey = "FileLen";
	
	private HashMap<String, String> itemData = new HashMap<String, String>();
	
	/** 获取MD5*/
	public String getMD5(){
		return itemData.get(MD5Key);
	}
	
	/** 获取文件名*/
	public String getFileName(){
		return itemData.get(filenameKey);
	}
	
	/** 获取版本*/
	public String getVersion(){
		return itemData.get(versionKey);
	}
	
	/** 获取Type:APK\PATCH\OS*/
	public String getType(){
		return itemData.get(typeKey);
	}
	
	/** 获取大小*/
	public String getOperate(){
		return itemData.get(operateKey);
	}
	
	/** 获取更新操作：
	 * 	   ;0x00:定时更新,
	 *     ;0x01:删除
	 *     ;0x02:立即更新
	*/
	public String getUpdate(){
		return itemData.get(updateKey);
	}
	
	/** 获取大小*/
	public String getFileLen(){
		return itemData.get(fileLenKey);
	}
	
	/** 返回整个数据项*/
	public HashMap<String, String> getData(){
		return itemData;
	}
	
	/** 分离各项数据*/
    private static String[] spitData(String data, String split){
    	return data.split(split);
    }
    
    /** 解析确认包*/
    public static ArrayList<ConfirmPacketData> parse(String confirmPacket){
    	ArrayList<ConfirmPacketData> confirmPacketData;
    	/** 每个MD5开头的为新的数据项*/
    	String[] allDataArray = spitData(confirmPacket, "MD5:");
    	/** 由于使用"MD5:"来分离，所以第一项为空，要去掉*/
    	String[] realDataArray = new String[allDataArray.length - 1];
    	System.arraycopy(allDataArray, 1, realDataArray, 0, allDataArray.length - 1);
		/** 初始化数组*/
		confirmPacketData = new ArrayList<ConfirmPacketData>();
		
		/** 每个数据项数据*/
    	String[] itemDataArray;
    	for(int i = 0; i < realDataArray.length; i++){
    		ConfirmPacketData temp = new ConfirmPacketData();
    		/** 每个数据字段通过\n隔开*/
    		itemDataArray = spitData(realDataArray[i], "\n");
    		/** 由于使用"MD5:"来分离，所以package直接取值*/
    		temp.getData().put(MD5Key, itemDataArray[0]);
    		for(int j = 1; j < itemDataArray.length; j++){
    			/** 每个数据项名称和数据项内容用冒号隔开*/
    			String[] itemdata = spitData(itemDataArray[j], ":");
    			if(itemdata.length == 2)
    				temp.getData().put(itemdata[0], itemdata[1]);
    		}
    		confirmPacketData.add(temp);
    	}
    	return confirmPacketData;
    }
}
