package com.dspread.mdm.service.platform.monitor

import java.io.FilterInputStream
import java.io.FilterOutputStream
import java.io.InputStream
import java.io.OutputStream
import java.net.HttpURLConnection
import java.net.URLConnection

/**
 * 网络流量拦截器
 * 用于自动统计HTTP/HTTPS请求的流量
 */
object NetworkTrafficInterceptor {
    
    private const val TAG = "NetworkTrafficInterceptor"
    
    /**
     * 包装HttpURLConnection以自动统计流量
     */
    fun wrapConnection(connection: URLConnection): URLConnection {
        return if (connection is HttpURLConnection) {
            TrafficMonitoringConnection(connection)
        } else {
            connection
        }
    }
    
    /**
     * 包装InputStream以统计下载流量
     */
    fun wrapInputStream(inputStream: InputStream): InputStream {
        return TrafficMonitoringInputStream(inputStream)
    }
    
    /**
     * 包装OutputStream以统计上传流量
     */
    fun wrapOutputStream(outputStream: OutputStream): OutputStream {
        return TrafficMonitoringOutputStream(outputStream)
    }
    
    /**
     * 流量监控的HttpURLConnection包装器
     */
    private class TrafficMonitoringConnection(
        private val delegate: HttpURLConnection
    ) : HttpURLConnection(delegate.url) {
        
        override fun getInputStream(): InputStream {
            return TrafficMonitoringInputStream(delegate.inputStream)
        }
        
        override fun getOutputStream(): OutputStream {
            return TrafficMonitoringOutputStream(delegate.outputStream)
        }
        
        override fun getErrorStream(): InputStream? {
            return delegate.errorStream?.let { TrafficMonitoringInputStream(it) }
        }
        
        // 委托所有其他方法到原始连接
        override fun disconnect() = delegate.disconnect()
        override fun usingProxy(): Boolean = delegate.usingProxy()
        override fun connect() = delegate.connect()
        override fun getResponseCode(): Int = delegate.responseCode
        override fun getResponseMessage(): String? = delegate.responseMessage
        override fun getHeaderField(name: String?): String? = delegate.getHeaderField(name)
        override fun getHeaderField(n: Int): String? = delegate.getHeaderField(n)
        override fun getHeaderFieldKey(n: Int): String? = delegate.getHeaderFieldKey(n)
        override fun getHeaderFields(): MutableMap<String, MutableList<String>>? = delegate.headerFields
        override fun getContentLength(): Int = delegate.contentLength
        override fun getContentType(): String? = delegate.contentType
        override fun getDate(): Long = delegate.date
        override fun getExpiration(): Long = delegate.expiration
        override fun getLastModified(): Long = delegate.lastModified
        
        override fun setRequestMethod(method: String?) {
            delegate.requestMethod = method
        }
        
        override fun getRequestMethod(): String = delegate.requestMethod
        
        override fun setInstanceFollowRedirects(followRedirects: Boolean) {
            delegate.instanceFollowRedirects = followRedirects
        }
        
        override fun getInstanceFollowRedirects(): Boolean = delegate.instanceFollowRedirects
        
        override fun setRequestProperty(key: String?, value: String?) {
            delegate.setRequestProperty(key, value)
        }
        
        override fun addRequestProperty(key: String?, value: String?) {
            delegate.addRequestProperty(key, value)
        }
        
        override fun getRequestProperty(key: String?): String? = delegate.getRequestProperty(key)
        
        override fun getRequestProperties(): MutableMap<String, MutableList<String>>? = delegate.requestProperties
        
        override fun setConnectTimeout(timeout: Int) {
            delegate.connectTimeout = timeout
        }
        
        override fun getConnectTimeout(): Int = delegate.connectTimeout
        
        override fun setReadTimeout(timeout: Int) {
            delegate.readTimeout = timeout
        }
        
        override fun getReadTimeout(): Int = delegate.readTimeout
        
        override fun setDoInput(doinput: Boolean) {
            delegate.doInput = doinput
        }
        
        override fun getDoInput(): Boolean = delegate.doInput
        
        override fun setDoOutput(dooutput: Boolean) {
            delegate.doOutput = dooutput
        }
        
        override fun getDoOutput(): Boolean = delegate.doOutput
        
        override fun setUseCaches(usecaches: Boolean) {
            delegate.useCaches = usecaches
        }
        
        override fun getUseCaches(): Boolean = delegate.useCaches
    }
    
    /**
     * 流量监控的InputStream包装器
     */
    private class TrafficMonitoringInputStream(
        private val delegate: InputStream
    ) : FilterInputStream(delegate) {
        
        override fun read(): Int {
            val result = super.read()
            if (result != -1) {
                NetworkTrafficMonitor.recordHttpDownload(1)
            }
            return result
        }
        
        override fun read(b: ByteArray): Int {
            val bytesRead = super.read(b)
            if (bytesRead > 0) {
                NetworkTrafficMonitor.recordHttpDownload(bytesRead.toLong())
            }
            return bytesRead
        }
        
        override fun read(b: ByteArray, off: Int, len: Int): Int {
            val bytesRead = super.read(b, off, len)
            if (bytesRead > 0) {
                NetworkTrafficMonitor.recordHttpDownload(bytesRead.toLong())
            }
            return bytesRead
        }
    }
    
    /**
     * 流量监控的OutputStream包装器
     */
    private class TrafficMonitoringOutputStream(
        private val delegate: OutputStream
    ) : FilterOutputStream(delegate) {
        
        override fun write(b: Int) {
            super.write(b)
            NetworkTrafficMonitor.recordHttpUpload(1)
        }
        
        override fun write(b: ByteArray) {
            super.write(b)
            NetworkTrafficMonitor.recordHttpUpload(b.size.toLong())
        }
        
        override fun write(b: ByteArray, off: Int, len: Int) {
            super.write(b, off, len)
            NetworkTrafficMonitor.recordHttpUpload(len.toLong())
        }
    }
}
