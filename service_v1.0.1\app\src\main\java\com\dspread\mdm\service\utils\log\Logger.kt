package com.dspread.mdm.service.utils.log

import android.content.Context
import com.dspread.mdm.service.config.LogConfig

/**
 * 统一日志管理器
 * 基于新的Logger架构，支持多种输出目标
 */
object Logger {

    // 模块键常量
    private const val MODULE_COMMON = "common"
    private const val MODULE_HTTPS = "https"
    private const val MODULE_WEBSOCKET = "websocket"
    private const val MODULE_PLATFORM = "platform"
    private const val MODULE_SERVICE = "service"
    private const val MODULE_PROVISIONING = "provisioning"
    private const val MODULE_TASK = "task"
    private const val MODULE_RULE_BASE = "ruleBase"
    private const val MODULE_APP_MGR = "appMgr"
    private const val MODULE_LOG_STREAM = "logStream"
    private const val MODULE_REMOTE_VIEW = "remoteView"
    private const val MODULE_WIFI_PROFILE = "wifiProfile"
    private const val MODULE_APN_PROFILE = "apnProfile"
    private const val MODULE_GEOFENCE = "geofence"
    private const val MODULE_RECEIVER = "receiver"

    // 模块标签常量
    private const val TAG_COMMON = "Common"
    private const val TAG_HTTPS = "HTTPS"
    private const val TAG_WEBSOCKET = "WebSocket"
    private const val TAG_PLATFORM = "Platform"
    private const val TAG_SERVICE = "Service"
    private const val TAG_PROVISIONING = "Provisioning"
    private const val TAG_TASK = "Task"
    private const val TAG_RULE_BASE = "RuleBase"
    private const val TAG_APP_MGR = "AppManager"
    private const val TAG_LOG_STREAM = "LogStream"
    private const val TAG_REMOTE_VIEW = "RemoteView"
    private const val TAG_WIFI_PROFILE = "WifiProfile"
    private const val TAG_APN_PROFILE = "ApnProfile"
    private const val TAG_GEOFENCE = "GeoFence"
    private const val TAG_RECEIVER = "Receiver"
    private const val TAG_DEFAULT = "MDM"

    // 日志图标常量
    private const val ICON_DEBUG = "🔧"
    private const val ICON_ERROR = "❌"
    private const val ICON_INFO = "ℹ️"
    private const val ICON_WARN = "⚠️"
    private const val ICON_SUCCESS = "✅"
    private const val ICON_SECURITY = "🔐"
    private const val ICON_EMPTY = ""

    // 模块标签映射
    private fun getModuleTag(moduleKey: String): String = when (moduleKey) {
        MODULE_COMMON -> TAG_COMMON
        MODULE_HTTPS -> TAG_HTTPS
        MODULE_WEBSOCKET -> TAG_WEBSOCKET
        MODULE_PLATFORM -> TAG_PLATFORM
        MODULE_SERVICE -> TAG_SERVICE
        MODULE_PROVISIONING -> TAG_PROVISIONING
        MODULE_TASK -> TAG_TASK
        MODULE_RULE_BASE -> TAG_RULE_BASE
        MODULE_APP_MGR -> TAG_APP_MGR
        MODULE_LOG_STREAM -> TAG_LOG_STREAM
        MODULE_REMOTE_VIEW -> TAG_REMOTE_VIEW
        MODULE_WIFI_PROFILE -> TAG_WIFI_PROFILE
        MODULE_APN_PROFILE -> TAG_APN_PROFILE
        MODULE_GEOFENCE -> TAG_GEOFENCE
        MODULE_RECEIVER -> TAG_RECEIVER
        else -> TAG_DEFAULT
    }

    // 是否已初始化
    private var isInitialized = false

    /**
     * 初始化Logger
     */
    fun init(context: Context, level: Int = LogLevel.DEBUG, console: Boolean = true, file: Boolean = true) {
        LoggerConfig.init(context, level, console, file)
        isInitialized = true
    }

    /**
     * 内部日志写入方法
     */
    private fun writeLog(level: Int, tag: String, message: String, throwable: Throwable? = null) {
        // 检查日志级别
        if (level < LoggerConfig.getLogLevel()) {
            return
        }

        // 如果未初始化，使用默认的Android Log
        if (!isInitialized) {
            when (level) {
                LogLevel.VERBOSE -> android.util.Log.v(tag, message, throwable)
                LogLevel.DEBUG -> android.util.Log.d(tag, message, throwable)
                LogLevel.INFO -> android.util.Log.i(tag, message, throwable)
                LogLevel.WARN -> android.util.Log.w(tag, message, throwable)
                LogLevel.ERROR -> android.util.Log.e(tag, message, throwable)
            }
            return
        }

        // 使用配置的写入器
        LoggerConfig.getLogWriters().forEach { writer ->
            try {
                writer.writeLog(level, tag, message, throwable)
            } catch (e: Exception) {
                // 避免日志写入异常影响主流程
                android.util.Log.e("Logger", "日志写入失败", e)
            }
        }
    }

    // 简化的日志方法
    private fun logIfEnabled(moduleKey: String, level: Int, icon: String, message: String, throwable: Throwable? = null) {
        if (!LogConfig.globalEnabled) return

        val enabled = when (moduleKey) {
            MODULE_COMMON -> LogConfig.commonEnabled
            MODULE_HTTPS -> LogConfig.httpsEnabled
            MODULE_WEBSOCKET -> LogConfig.websocketEnabled
            MODULE_PLATFORM -> LogConfig.platformEnabled
            MODULE_SERVICE -> LogConfig.serviceEnabled
            MODULE_PROVISIONING -> LogConfig.provisioningEnabled
            MODULE_TASK -> LogConfig.taskEnabled
            MODULE_RULE_BASE -> LogConfig.ruleBaseEnabled
            MODULE_APP_MGR -> LogConfig.appMgrEnabled
            MODULE_LOG_STREAM -> LogConfig.logStreamEnabled
            MODULE_REMOTE_VIEW -> LogConfig.remoteViewEnabled
            MODULE_WIFI_PROFILE -> LogConfig.wifiProfileEnabled
            MODULE_APN_PROFILE -> LogConfig.apnProfileEnabled
            MODULE_GEOFENCE -> LogConfig.geofenceEnabled
            MODULE_RECEIVER -> LogConfig.receiverEnabled
            else -> true
        }

        if (enabled) {
            val tag = getModuleTag(moduleKey)
            writeLog(level, tag, "$icon $message", throwable)
        }
    }

    // 通用日志
    fun com(message: String) = logIfEnabled(MODULE_COMMON, LogLevel.DEBUG, ICON_DEBUG, message)
    fun comE(message: String, throwable: Throwable? = null) = logIfEnabled(MODULE_COMMON, LogLevel.ERROR, ICON_ERROR, message, throwable)
    fun comI(message: String) = logIfEnabled(MODULE_COMMON, LogLevel.INFO, ICON_INFO, message)
    fun comW(message: String) = logIfEnabled(MODULE_COMMON, LogLevel.WARN, ICON_WARN, message)

    fun success(message: String) = logIfEnabled(MODULE_COMMON, LogLevel.INFO, ICON_SUCCESS, message)
    fun security(message: String) = logIfEnabled(MODULE_COMMON, LogLevel.WARN, ICON_SECURITY, message)

    // HTTP/HTTPS 日志
    fun https(message: String) = logIfEnabled(MODULE_HTTPS, LogLevel.DEBUG, ICON_DEBUG, message)
    fun httpsE(message: String, throwable: Throwable? = null) = logIfEnabled(MODULE_HTTPS, LogLevel.ERROR, ICON_ERROR, message, throwable)
    fun httpsI(message: String) = logIfEnabled(MODULE_HTTPS, LogLevel.INFO, ICON_INFO, message)
    fun httpsW(message: String) = logIfEnabled(MODULE_HTTPS, LogLevel.WARN, ICON_WARN, message)

    // WebSocket 日志
    fun wsm(message: String) = logIfEnabled(MODULE_WEBSOCKET, LogLevel.INFO, ICON_DEBUG, message)
    fun wsmE(message: String, throwable: Throwable? = null) = logIfEnabled(MODULE_WEBSOCKET, LogLevel.ERROR, ICON_ERROR, message, throwable)
    fun wsmI(message: String) = logIfEnabled(MODULE_WEBSOCKET, LogLevel.INFO, ICON_INFO, message)
    fun wsmW(message: String) = logIfEnabled(MODULE_WEBSOCKET, LogLevel.WARN, ICON_WARN, message)

    // 系统 API 日志
    fun platform(message: String) = logIfEnabled(MODULE_PLATFORM, LogLevel.DEBUG, ICON_DEBUG, message)
    fun platformE(message: String, throwable: Throwable? = null) = logIfEnabled(MODULE_PLATFORM, LogLevel.ERROR, ICON_ERROR, message, throwable)
    fun platformI(message: String) = logIfEnabled(MODULE_PLATFORM, LogLevel.INFO, ICON_INFO, message)
    fun platformW(message: String) = logIfEnabled(MODULE_PLATFORM, LogLevel.WARN, ICON_WARN, message)

    // 服务日志
    fun serve(message: String) = logIfEnabled(MODULE_SERVICE, LogLevel.DEBUG, ICON_DEBUG, message)
    fun serveE(message: String, throwable: Throwable? = null) = logIfEnabled(MODULE_SERVICE, LogLevel.ERROR, ICON_ERROR, message, throwable)
    fun serveI(message: String) = logIfEnabled(MODULE_SERVICE, LogLevel.INFO, ICON_INFO, message)
    fun serveW(message: String) = logIfEnabled(MODULE_SERVICE, LogLevel.WARN, ICON_WARN, message)

    // Provisioning 日志
    fun prov(message: String) = logIfEnabled(MODULE_PROVISIONING, LogLevel.DEBUG, ICON_DEBUG, message)
    fun provE(message: String, throwable: Throwable? = null) = logIfEnabled(MODULE_PROVISIONING, LogLevel.ERROR, ICON_ERROR, message, throwable)
    fun provI(message: String) = logIfEnabled(MODULE_PROVISIONING, LogLevel.INFO, ICON_INFO, message)
    fun provW(message: String) = logIfEnabled(MODULE_PROVISIONING, LogLevel.WARN, ICON_WARN, message)

    // Task 日志
    fun task(message: String) = logIfEnabled(MODULE_TASK, LogLevel.DEBUG, ICON_DEBUG, message)
    fun taskE(message: String, throwable: Throwable? = null) = logIfEnabled(MODULE_TASK, LogLevel.ERROR, ICON_ERROR, message, throwable)
    fun taskI(message: String) = logIfEnabled(MODULE_TASK, LogLevel.INFO, ICON_INFO, message)
    fun taskW(message: String) = logIfEnabled(MODULE_TASK, LogLevel.WARN, ICON_WARN, message)

    // RuleBase 日志
    fun rule(message: String) = logIfEnabled(MODULE_RULE_BASE, LogLevel.DEBUG, ICON_DEBUG, message)
    fun ruleE(message: String, throwable: Throwable? = null) = logIfEnabled(MODULE_RULE_BASE, LogLevel.ERROR, ICON_ERROR, message, throwable)
    fun ruleI(message: String) = logIfEnabled(MODULE_RULE_BASE, LogLevel.INFO, ICON_INFO, message)
    fun ruleW(message: String) = logIfEnabled(MODULE_RULE_BASE, LogLevel.WARN, ICON_WARN, message)

    // App Manager 日志
    fun appMgr(message: String) = logIfEnabled(MODULE_APP_MGR, LogLevel.DEBUG, ICON_DEBUG, message)
    fun appMgrE(message: String, throwable: Throwable? = null) = logIfEnabled(MODULE_APP_MGR, LogLevel.ERROR, ICON_ERROR, message, throwable)
    fun appMgrI(message: String) = logIfEnabled(MODULE_APP_MGR, LogLevel.INFO, ICON_INFO, message)
    fun appMgrW(message: String) = logIfEnabled(MODULE_APP_MGR, LogLevel.WARN, ICON_WARN, message)

    // Remote View 日志
    fun remote(message: String) = logIfEnabled(MODULE_REMOTE_VIEW, LogLevel.DEBUG, ICON_DEBUG, message)
    fun remoteE(message: String, throwable: Throwable? = null) = logIfEnabled(MODULE_REMOTE_VIEW, LogLevel.ERROR, ICON_ERROR, message, throwable)
    fun remoteI(message: String) = logIfEnabled(MODULE_REMOTE_VIEW, LogLevel.INFO, ICON_INFO, message)
    fun remoteW(message: String) = logIfEnabled(MODULE_REMOTE_VIEW, LogLevel.WARN, ICON_WARN, message)

    // LogStream日志
    fun logStream(message: String) = logIfEnabled(MODULE_LOG_STREAM, LogLevel.INFO, ICON_DEBUG, message)
    fun logStreamE(message: String, throwable: Throwable? = null) = logIfEnabled(MODULE_LOG_STREAM, LogLevel.ERROR, ICON_ERROR, message, throwable)
    fun logStreamI(message: String) = logIfEnabled(MODULE_LOG_STREAM, LogLevel.INFO, ICON_INFO, message)
    fun logStreamW(message: String) = logIfEnabled(MODULE_LOG_STREAM, LogLevel.WARN, ICON_WARN, message)

    // WiFi日志
    fun wifi(message: String) = logIfEnabled(MODULE_WIFI_PROFILE, LogLevel.DEBUG, ICON_DEBUG, message)
    fun wifiE(message: String, throwable: Throwable? = null) = logIfEnabled(MODULE_WIFI_PROFILE, LogLevel.ERROR, ICON_ERROR, message, throwable)
    fun wifiI(message: String) = logIfEnabled(MODULE_WIFI_PROFILE, LogLevel.INFO, ICON_EMPTY, message)
    fun wifiW(message: String) = logIfEnabled(MODULE_WIFI_PROFILE, LogLevel.WARN, ICON_EMPTY, message)

    // APN日志
    fun apn(message: String) = logIfEnabled(MODULE_APN_PROFILE, LogLevel.DEBUG, ICON_DEBUG, message)
    fun apnE(message: String, throwable: Throwable? = null) = logIfEnabled(MODULE_APN_PROFILE, LogLevel.ERROR, ICON_ERROR, message, throwable)
    fun apnI(message: String) = logIfEnabled(MODULE_APN_PROFILE, LogLevel.INFO, ICON_EMPTY, message)
    fun apnW(message: String) = logIfEnabled(MODULE_APN_PROFILE, LogLevel.WARN, ICON_EMPTY, message)

    // 地理围栏（GPS、基站、WiFi定位相关）
    fun geo(message: String) = logIfEnabled(MODULE_GEOFENCE, LogLevel.DEBUG, ICON_DEBUG, message)
    fun geoE(message: String, throwable: Throwable? = null) = logIfEnabled(MODULE_GEOFENCE, LogLevel.ERROR, ICON_ERROR, message, throwable)
    fun geoI(message: String) = logIfEnabled(MODULE_GEOFENCE, LogLevel.INFO, ICON_INFO, message)
    fun geoW(message: String) = logIfEnabled(MODULE_GEOFENCE, LogLevel.WARN, ICON_WARN, message)

    // Receiver日志（广播接收器相关）
    fun receiver(message: String) = logIfEnabled(MODULE_RECEIVER, LogLevel.DEBUG, ICON_DEBUG, message)
    fun receiverE(message: String, throwable: Throwable? = null) = logIfEnabled(MODULE_RECEIVER, LogLevel.ERROR, ICON_ERROR, message, throwable)
    fun receiverI(message: String) = logIfEnabled(MODULE_RECEIVER, LogLevel.INFO, ICON_INFO, message)
    fun receiverW(message: String) = logIfEnabled(MODULE_RECEIVER, LogLevel.WARN, ICON_WARN, message)

    // 工具方法
    fun flush() {
        if (isInitialized) {
            LoggerConfig.flushLogs()
        }
    }

    fun cleanup() {
        if (isInitialized) {
            LoggerConfig.cleanupOldLogFiles()
        }
    }

    fun setLogLevel(level: Int) {
        if (isInitialized) {
            LoggerConfig.setLogLevel(level)
        }
    }

    fun setConsoleOutput(enabled: Boolean) {
        if (isInitialized) {
            LoggerConfig.setConsoleOutput(enabled)
        }
    }

    fun setFileOutput(enabled: Boolean) {
        if (isInitialized) {
            LoggerConfig.setFileOutput(enabled)
        }
    }

    fun getLogDirectory(): java.io.File? {
        return if (isInitialized) {
            LoggerConfig.getLogDirectory()
        } else {
            null
        }
    }
}