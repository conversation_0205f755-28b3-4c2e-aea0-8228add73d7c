package com.dspread.mdm.service.platform.api.system

import android.content.Context
import android.os.PowerManager
import com.dspread.mdm.service.utils.log.Logger
import com.dspread.mdm.service.platform.api.model.SystemOperationResult
import com.dspread.mdm.service.platform.api.system.ShellApi
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.withContext
import java.lang.reflect.Method

/**
 * 系统控制API
 * 提供系统重启、关机、恢复出厂设置等功能
 *
 * 使用单例模式，避免重复实例化和资源浪费
 */
class SystemControlApi(private val context: Context) {

    companion object {
        private const val TAG = "SystemControlApi"

        @Volatile
        private var INSTANCE: SystemControlApi? = null

        /**
         * 获取单例实例
         */
        private fun getInstance(context: Context): SystemControlApi {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: SystemControlApi(context.applicationContext).also {
                    INSTANCE = it
                    Logger.platform("SystemControlApi 单例实例已创建")
                }
            }
        }

        // ==================== 静态方法接口 ====================

        /**
         * 系统重启（异步包装）
         */
        fun reboot(context: Context, reason: String? = null): SystemOperationResult {
            return try {
                kotlinx.coroutines.runBlocking {
                    getInstance(context).reboot(reason)
                }
            } catch (e: Exception) {
                SystemOperationResult.failure("系统重启失败: ${e.message}")
            }
        }

        /**
         * 恢复出厂设置（异步包装）
         */
        fun wipeData(context: Context): SystemOperationResult {
            return try {
                kotlinx.coroutines.runBlocking {
                    getInstance(context).wipeData()
                }
            } catch (e: Exception) {
                SystemOperationResult.failure("恢复出厂设置失败: ${e.message}")
            }
        }

        /**
         * 锁屏（异步包装）
         */
        fun lockScreen(context: Context): SystemOperationResult {
            return try {
                kotlinx.coroutines.runBlocking {
                    getInstance(context).lockScreen()
                }
            } catch (e: Exception) {
                SystemOperationResult.failure("锁屏失败: ${e.message}")
            }
        }

        /**
         * 执行shell命令（异步包装）
         */
        fun executeShellCommand(context: Context, command: String): SystemOperationResult {
            return try {
                kotlinx.coroutines.runBlocking {
                    getInstance(context).executeShellCommand(command, false)
                }
            } catch (e: Exception) {
                SystemOperationResult.failure("执行shell命令失败: ${e.message}")
            }
        }

        /**
         * 检查是否有root权限
         */
        fun hasRootPermission(context: Context): Boolean {
            return try {
                getInstance(context).hasSystemPermission()
            } catch (e: Exception) {
                false
            }
        }

        // 其他方法暂时保持实例调用方式，避免复杂的suspend函数包装
    }

    private val powerManager by lazy {
        context.getSystemService(Context.POWER_SERVICE) as PowerManager
    }
    
    /**
     * 系统重启
     */
    fun reboot(reason: String? = null): SystemOperationResult {
        return try {
            Logger.platformI("Attempting to reboot system, reason: $reason")
            
            // 方法1：使用PowerManager（推荐）
            if (tryPowerManagerReboot(reason)) {
                Logger.platformI("System reboot initiated successfully via PowerManager")
                return SystemOperationResult.success("Reboot initiated via PowerManager")
            }
            
            // 方法2：使用反射调用隐藏API
            if (tryReflectionReboot(reason)) {
                Logger.platformI("System reboot initiated successfully via reflection")
                return SystemOperationResult.success("Reboot initiated via reflection")
            }
            
            // 方法3：使用Shell命令作为备选
            val rebootCommand = if (reason?.isNotEmpty() == true) "reboot $reason" else "reboot"
            val shellResult = runBlocking { ShellApi.executeRootCommand(rebootCommand) }
            if (shellResult.isSuccess) {
                Logger.platformI("System reboot initiated successfully via shell")
                SystemOperationResult.success("Reboot initiated via shell")
            } else {
                Logger.platformE("Failed to reboot via shell: ${shellResult.exceptionOrNull()?.message}")
                SystemOperationResult.failure("Shell reboot failed: ${shellResult.exceptionOrNull()?.message}")
            }
        } catch (e: Exception) {
            Logger.platformE("Failed to reboot system", e)
            SystemOperationResult.failure("Reboot failed: ${e.message}")
        }
    }
    
    /**
     * 系统关机
     */
    fun shutdown(): SystemOperationResult {
        return try {
            Logger.platformI("Attempting to shutdown system")
            
            // 方法1：使用PowerManager
            if (tryPowerManagerShutdown()) {
                Logger.platformI("System shutdown initiated successfully via PowerManager")
                return SystemOperationResult.success("Shutdown initiated via PowerManager")
            }
            
            // 方法2：使用Shell命令
            val shellResult = runBlocking { ShellApi.executeRootCommand("shutdown -h now") }
            if (shellResult.isSuccess) {
                Logger.platformI("System shutdown initiated successfully via shell")
                SystemOperationResult.success("Shutdown initiated via shell")
            } else {
                Logger.platformE("Failed to shutdown via shell: ${shellResult.exceptionOrNull()?.message}")
                SystemOperationResult.failure("Shell shutdown failed: ${shellResult.exceptionOrNull()?.message}")
            }
        } catch (e: Exception) {
            Logger.platformE("Failed to shutdown system", e)
            SystemOperationResult.failure("Shutdown failed: ${e.message}")
        }
    }
    
    /**
     * 恢复出厂设置
     */
    fun factoryReset(): SystemOperationResult {
        return try {
            Logger.platformW("Attempting factory reset - this will erase all data!")
            
            // 使用Shell命令执行恢复出厂设置
            val shellResult = runBlocking { ShellApi.executeRootCommand("recovery --wipe_data") }

            if (shellResult.isSuccess) {
                Logger.platformI("Factory reset initiated successfully")
                SystemOperationResult.success("Factory reset initiated")
            } else {
                Logger.platformE("Failed to initiate factory reset: ${shellResult.exceptionOrNull()?.message}")
                SystemOperationResult.failure("Factory reset failed: ${shellResult.exceptionOrNull()?.message}")
            }
        } catch (e: Exception) {
            Logger.platformE("Failed to perform factory reset", e)
            SystemOperationResult.failure("Factory reset failed: ${e.message}")
        }
    }
    
    /**
     * 软重启（重启系统UI）
     */
    fun softReboot(): SystemOperationResult {
        return try {
            Logger.platformI("Attempting soft reboot (restart system UI)")
            
            val shellResult = runBlocking { ShellApi.executeRootCommand("killall system_server") }

            if (shellResult.isSuccess) {
                Logger.platformI("Soft reboot initiated successfully")
                SystemOperationResult.success("Soft reboot initiated")
            } else {
                Logger.platformE("Failed to perform soft reboot: ${shellResult.exceptionOrNull()?.message}")
                SystemOperationResult.failure("Soft reboot failed: ${shellResult.exceptionOrNull()?.message}")
            }
        } catch (e: Exception) {
            Logger.platformE("Failed to perform soft reboot", e)
            SystemOperationResult.failure("Soft reboot failed: ${e.message}")
        }
    }
    
    /**
     * 重启到Recovery模式
     */
    fun rebootToRecovery(): SystemOperationResult {
        return try {
            Logger.platformI("Attempting to reboot to recovery mode")
            
            val shellResult = runBlocking { ShellApi.executeRootCommand("reboot recovery") }

            if (shellResult.isSuccess) {
                Logger.platformI("Reboot to recovery initiated successfully")
                SystemOperationResult.success("Reboot to recovery initiated")
            } else {
                Logger.platformE("Failed to reboot to recovery: ${shellResult.exceptionOrNull()?.message}")
                SystemOperationResult.failure("Reboot to recovery failed: ${shellResult.exceptionOrNull()?.message}")
            }
        } catch (e: Exception) {
            Logger.platformE("Failed to reboot to recovery", e)
            SystemOperationResult.failure("Reboot to recovery failed: ${e.message}")
        }
    }
    
    /**
     * 重启到Bootloader模式
     */
    fun rebootToBootloader(): SystemOperationResult {
        return try {
            Logger.platformI("Attempting to reboot to bootloader mode")
            
            val shellResult = runBlocking { ShellApi.executeRootCommand("reboot bootloader") }

            if (shellResult.isSuccess) {
                Logger.platformI("Reboot to bootloader initiated successfully")
                SystemOperationResult.success("Reboot to bootloader initiated")
            } else {
                Logger.platformE("Failed to reboot to bootloader: ${shellResult.exceptionOrNull()?.message}")
                SystemOperationResult.failure("Reboot to bootloader failed: ${shellResult.exceptionOrNull()?.message}")
            }
        } catch (e: Exception) {
            Logger.platformE("Failed to reboot to bootloader", e)
            SystemOperationResult.failure("Reboot to bootloader failed: ${e.message}")
        }
    }
    
    /**
     * 检查API可用性
     */
    fun isAvailable(): Boolean {
        return try {
            // 检查是否有PowerManager访问权限
            powerManager != null
        } catch (e: Exception) {
            Logger.platformW("SystemControlApi not available: ${e.message}")
            false
        }
    }
    
    // 私有方法：尝试使用PowerManager重启
    private fun tryPowerManagerReboot(reason: String?): Boolean {
        return try {
            powerManager.reboot(reason)
            true
        } catch (e: Exception) {
            Logger.platformW("PowerManager reboot failed: ${e.message}")
            false
        }
    }
    
    // 私有方法：尝试使用反射重启
    private fun tryReflectionReboot(reason: String?): Boolean {
        return try {
            val method: Method = powerManager.javaClass.getMethod("reboot", String::class.java)
            method.invoke(powerManager, reason)
            true
        } catch (e: Exception) {
            Logger.platformW("Reflection reboot failed: ${e.message}")
            false
        }
    }
    
    // 私有方法：尝试使用PowerManager关机
    private fun tryPowerManagerShutdown(): Boolean {
        return try {
            // 尝试使用隐藏的shutdown方法
            val method: Method = powerManager.javaClass.getMethod("shutdown", Boolean::class.java, String::class.java, Boolean::class.java)
            method.invoke(powerManager, false, null, false)
            true
        } catch (e: Exception) {
            Logger.platformW("PowerManager shutdown failed: ${e.message}")
            false
        }
    }

    /**
     * 锁屏操作
     */
    suspend fun lockScreen(): SystemOperationResult {
        return withContext(Dispatchers.IO) {
            try {
                Logger.platformI("执行锁屏操作")
                val result = ShellApi.executeRootCommand("input keyevent 26")
                if (result.isSuccess) {
                    Logger.platformI("锁屏操作成功")
                    SystemOperationResult.success("Screen locked successfully")
                } else {
                    Logger.platformE("锁屏操作失败: ${result.exceptionOrNull()?.message}")
                    SystemOperationResult.failure("Lock screen failed: ${result.exceptionOrNull()?.message}")
                }
            } catch (e: Exception) {
                Logger.platformE("锁屏操作异常", e)
                SystemOperationResult.failure("Lock screen exception: ${e.message}")
            }
        }
    }

    /**
     * 数据擦除操作
     */
    suspend fun wipeData(): SystemOperationResult {
        return withContext(Dispatchers.IO) {
            try {
                Logger.platformW("执行数据擦除操作 - 危险操作！")
                val result = ShellApi.executeRootCommand("recovery --wipe_data")
                if (result.isSuccess) {
                    Logger.platformW("数据擦除操作已执行")
                    SystemOperationResult.success("Data wipe initiated")
                } else {
                    Logger.platformE("数据擦除操作失败: ${result.exceptionOrNull()?.message}")
                    SystemOperationResult.failure("Wipe data failed: ${result.exceptionOrNull()?.message}")
                }
            } catch (e: Exception) {
                Logger.platformE("数据擦除操作异常", e)
                SystemOperationResult.failure("Wipe data exception: ${e.message}")
            }
        }
    }

    /**
     * 执行Shell命令
     */
    suspend fun executeShellCommand(command: String, requireRoot: Boolean = false): SystemOperationResult {
        return withContext(Dispatchers.IO) {
            try {
                Logger.platformI("执行Shell命令: $command (root: $requireRoot)")
                val result = if (requireRoot) {
                    ShellApi.executeRootCommand(command)
                } else {
                    ShellApi.executeCommand(command)
                }

                if (result.isSuccess) {
                    val output = result.getOrNull() ?: ""
                    Logger.platformI("Shell命令执行成功: $output")
                    SystemOperationResult.success("Command executed successfully: $output")
                } else {
                    val error = result.exceptionOrNull()?.message ?: "Unknown error"
                    Logger.platformE("Shell命令执行失败: $error")
                    SystemOperationResult.failure("Command failed: $error")
                }
            } catch (e: Exception) {
                Logger.platformE("Shell命令执行异常", e)
                SystemOperationResult.failure("Command exception: ${e.message}")
            }
        }
    }

    /**
     * 检查是否有系统权限
     */
    fun hasSystemPermission(): Boolean {
        return try {
            // 简单检查：检查是否能访问系统级别的文件
            val systemFile = java.io.File("/system/build.prop")
            systemFile.exists() && systemFile.canRead()
        } catch (e: Exception) {
            Logger.platformE("检查系统权限异常", e)
            false
        }
    }
}
