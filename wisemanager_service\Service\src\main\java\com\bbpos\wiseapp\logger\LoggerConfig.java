package com.bbpos.wiseapp.logger;

import com.bbpos.wiseapp.utils.FileUtils;
import com.orhanobut.logger.AndroidLogAdapter;
import com.orhanobut.logger.FormatStrategy;
import com.orhanobut.logger.Logger;
import com.orhanobut.logger.PrettyFormatStrategy;

import java.io.File;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.List;

public class LoggerConfig {
    public static final String logFolder = FileUtils.getWiseAppConfigPath() + File.separatorChar + "log";
    private static final int MAX_LOG_FILES_COUNT = 10;  //最大允许存在的日志个数，超出删除

    private static MyDiskLogAdapter logAdapter;
    /**
     * 初始化Logger参数
     */
    public static void initLogger(boolean isConsoleOutput, boolean isWriteFile){
        Logger.clearLogAdapters();

        FormatStrategy myStrategy = PrettyFormatStrategy.newBuilder()
                .showThreadInfo(false)  // (Optional) Whether to show thread info or not. Default true
                .methodCount(1)         // (Optional) How many method line to show. Default 2
                .methodOffset(1)        // (Optional) Skips some method invokes in stack trace. Default 5
//              .logStrategy(customLog) // (Optional) Changes the log strategy to print out. Default LogCat
                //.tag("My custom tag")   // (Optional) Custom tag for each log. Default PRETTY_LOGGER
                .simpleMode(true)
                .build();
        if(isConsoleOutput) {
            Logger.addLogAdapter(new AndroidLogAdapter(myStrategy));  //用于打印输出到console
        }
        if(isWriteFile) {
            logAdapter = new MyDiskLogAdapter();
            Logger.addLogAdapter(logAdapter);   //用于输出到文件
        }
    }

    /**
     * 删除旧log
     */
    public synchronized static void deleteOldLogFiles(){

        //保证目录一定要存在如果不存在新建
        File folder = new File(logFolder);
        try {
            if (!folder.exists()) {
                //TODO: What if logFolder is not created, what happens then?
                folder.mkdirs();
            }
        }catch (Exception e){
            e.printStackTrace();
            return;
        }

        List<File> list = new ArrayList<>();
        for(File file:folder.listFiles()){
            list.add(file);
        }
        Collections.sort(list, new FileComparatorDesc());   //降序

        int fileCount = list.size();
        Logger.v("LoggerConfig", "fileCount=" + fileCount);

        if(fileCount > MAX_LOG_FILES_COUNT){
            for(int i=MAX_LOG_FILES_COUNT; i<fileCount; i++){
                //删除对应的文件
                File file = list.get(i);
                try {
                    Logger.i("deleting file:" + file.getName());
                    file.delete();
                }catch (Exception e){
                    e.printStackTrace();
                }
            }
        }

    }

    static class FileComparatorDesc implements Comparator<File> {
        public int compare(File file1, File file2) {
            String createInfo1 = FileUtil.getFileNameNoEx(file1.getName());
            String createInfo2 = FileUtil.getFileNameNoEx(file2.getName());

            try {
                Date create1 = MyDiskLogStrategy.sdftshort.parse(createInfo1);
                Date create2 = MyDiskLogStrategy.sdftshort.parse(createInfo2);
                if(create1.before(create2)){//按降序排列
                    return 1;
                }else{
                    return -1;
                }
            } catch (ParseException e) {
                return 0;
            }
        }
    }

    public static void flushLoggerMemoryLogToFile(){
        if(logAdapter!=null){
            logAdapter.sendFlushMsg();
        }
    }
}
