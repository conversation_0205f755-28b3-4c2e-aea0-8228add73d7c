package com.bbpos.wiseapp.tms.timer;

import android.app.AlarmManager;
import android.app.PendingIntent;
import android.content.BroadcastReceiver;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.net.ConnectivityManager;
import android.os.Build;
import android.util.Log;

import com.bbpos.wiseapp.logger.BBLog;
import com.bbpos.wiseapp.system.api.ServiceApi;
import com.bbpos.wiseapp.utils.SharedPreferencesUtils;
import com.bbpos.wiseapp.tms.service.TerminalInfoUploadSerivce;
import com.bbpos.wiseapp.tms.utils.BroadcastActions;
import com.bbpos.wiseapp.tms.utils.Constants;
import com.bbpos.wiseapp.tms.utils.Helpers;
import com.bbpos.wiseapp.websocket.WebSocketCenter;

public class TerminalInfoUploadTimer extends BroadcastReceiver{
	@Override
	public void onReceive(Context context, Intent intent) {
		// TODO Auto-generated method stub
		String action = intent.getAction();
		BBLog.v(BBLog.TAG, "TerminalInfoUploadTimer receive broadcast  "+ action);
		if(BroadcastActions.BOOT_COMPLETED_BC.equals(action)){
			if( !Constants.IS_FIRST_TER_INFO_UPLOAD_COMPLETED && Helpers.isOnline(context)) {
				startTerminalInfoUploadTimer(context);
			}
		}else if(BroadcastActions.TER_INFO_UPLOAD_BC.equals(action)){
			startTerminalInfoUploadTimer(context);
			SharedPreferencesUtils.setSharePreferencesValue(Constants.FLAG_LAST_TER_INFO_UPLOAD_ALIVE,"false");
			BBLog.d(BBLog.TAG, "onReceive: 收到0109上报广播，开启上报任务");
		}else if(ConnectivityManager.CONNECTIVITY_ACTION.equals(action)){
//			//不再需要聯網觸發上送C0109，全部依賴到WebSocket連接成功時；
//			if(Helpers.isOnline(context)){
//				startTerminalInfoUploadTimer(context);
//			}
		}
	}

	/**启动终端信息定时上送功能*/
	private void startTerminalInfoUploadTimer(Context context)
	{
		if (WebSocketCenter.isWebSocketConnected) {
			Intent intentTerInfoUpload = new Intent(context, TerminalInfoUploadSerivce.class);
			ServiceApi.getIntance().startService(intentTerInfoUpload);
		}

		Intent intentTmp = new Intent(BroadcastActions.TER_INFO_UPLOAD_BC);
		if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
			intentTmp.setComponent(new ComponentName(context.getPackageName(), TerminalInfoUploadTimer.class.getName()));
		}
		PendingIntent pi = PendingIntent.getBroadcast(context, 0, intentTmp, PendingIntent.FLAG_UPDATE_CURRENT);
		AlarmManager am = (AlarmManager) context.getSystemService(Context.ALARM_SERVICE);
		am.cancel(pi);
		long timeOnMillis = System.currentTimeMillis() + (Constants.TER_INFO_UPLOAD_INTERVAL()*1000);

		if(Build.VERSION.SDK_INT < Build.VERSION_CODES.KITKAT){
			am.set(AlarmManager.RTC_WAKEUP, timeOnMillis, pi);
		}else{
			am.setExact(AlarmManager.RTC_WAKEUP, timeOnMillis, pi);
		}

	}
}
