package com.bbpos.wiseapp.tms.service;

import android.content.Intent;
import android.text.TextUtils;

import com.bbpos.wiseapp.logger.BBLog;
import com.bbpos.wiseapp.service.ListeningService;
import com.bbpos.wiseapp.service.WakeLockService;
import com.bbpos.wiseapp.service.common.UsualData;
import com.bbpos.wiseapp.system.api.DeviceInfoApi;
import com.bbpos.wiseapp.tms.model.TaskInfo;
import com.bbpos.wiseapp.tms.network.HttpUtils;
import com.bbpos.wiseapp.tms.utils.BroadcastActions;
import com.bbpos.wiseapp.tms.utils.Constants;
import com.bbpos.wiseapp.tms.utils.Helpers;
import com.bbpos.wiseapp.tms.utils.ParameterName;
import com.bbpos.wiseapp.tms.utils.RulebasedAppListManager;
import com.bbpos.wiseapp.tms.utils.TaskState;
import com.bbpos.wiseapp.tms.widget.MAppPlusDialog;

import org.json.JSONException;
import org.json.JSONObject;

public class WSTaskApkUpdateService extends WakeLockService {
	private static final String TAG = "AppPlusUpdate";
	private int ret = 0;
	public WSTaskApkUpdateService() {
		super("WSTaskApkUpdateService");
	}
    @Override
    public void onDestroy() {
        super.onDestroy();
		BBLog.w(BBLog.TAG, "WSTaskApkUpdateService 結束 發送 WSTASK_EXEC_BC");
		Helpers.sendBroad(WSTaskApkUpdateService.this, UsualData.WSTASK_EXEC_BC);
    }
	@Override
	protected void onHandleIntent(Intent intent) {
		BBLog.e(TAG, "WSTaskApkUpdateService START");
		final String taskJson = intent.getExtras().getString(ParameterName.taskJson);
		try {
			if (!TextUtils.isEmpty(taskJson)) {
				JSONObject taskJsonObj = new JSONObject(taskJson);
				ret = RulebasedAppListManager.isApkInstalled(taskJsonObj);
				if (ret == 1 || (ret==2 && UsualData.LAUNCHER_711_PACKAGE_NAME.equals(taskJsonObj.getString(ParameterName.packName)))) {
					Helpers.updateWSTaskStateAndUpload(WSTaskApkUpdateService.this, intent.getExtras().getString(ParameterName.taskId), TaskState.INSTALL_SUCCESS, null);
					return;
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		}

		final String packName = intent.getExtras().getString(ParameterName.packName);
		final String versionName = intent.getExtras().getString(ParameterName.versionName);
		final String versionCode = intent.getExtras().getString(ParameterName.versionCode);
		final String taskId = intent.getExtras().getString(ParameterName.taskId);
		final String apkName = intent.getExtras().getString(ParameterName.apkName);
		final String appId = intent.getExtras().getString(ParameterName.appId);
		final String apkMd5 = intent.getExtras().getString(ParameterName.apkMd5);
		final long totalfileSize = Long.parseLong(intent.getExtras().getString(ParameterName.apkSize));
		String fileUrl = intent.getExtras().getString(ParameterName.url);
		final String filePath = Constants.APK_FILE_PATH + taskId;
		final String iconUrl = intent.getExtras().getString(ParameterName.appIconUrlEx);
		final String fileKey = intent.getExtras().getString(ParameterName.fileKey);
		boolean allowDowngrade = intent.getExtras().getBoolean(ParameterName.allowDowngrade);
		boolean forceInstall = intent.getExtras().getBoolean(ParameterName.forceInstall);

		int ret = RulebasedAppListManager.isApkInstalled(packName, versionName, versionCode);
		if (ret == 2 && !allowDowngrade) {
			Helpers.updateWSTaskStateAndUpload(WSTaskApkUpdateService.this, taskId, TaskState.UPDATE_DOWNGRADE_FORBIDDEN, null);
			return;
		}

		/*
		//上送下载中报文
		if (!TextUtils.isEmpty(fileKey)) {
			String response = "";
			for (int i=0; i<3; i++) {
				response = HttpUtils.postGetFileUrl(Constants.S3_RESOURCE_URL, HttpUtils.getPostTokenRequestContent(fileKey));
				if (!TextUtils.isEmpty(response)) {
					break;
				}
			}
			if (TextUtils.isEmpty(response)) {
				Helpers.updateWSTaskStateAndUpload(WSTaskApkUpdateService.this, taskId, TaskState.DOWNLOAD_FAILED, null);
				return;
			} else {
				fileUrl = response;
			}
		}
		 */
		Helpers.updateWSTaskStateAndUpload(WSTaskApkUpdateService.this, taskId, TaskState.DOWNLOAD_ING, null);
		HttpUtils.fileDownloadByUrlWithRetry(fileUrl, filePath, totalfileSize, apkMd5, new HttpUtils.FileDownloadCallBack() {
			@Override
			public void requestSuccess(JSONObject responseJson) throws Exception {
				// TODO Auto-generated method stub
				Helpers.updateWSTaskStateAndUpload(WSTaskApkUpdateService.this, taskId, TaskState.DOWNLOAD_SUCCESS, null);
				//判斷終端是否空閑5分鐘
				if ((ListeningService.isDeviceIdle(true) && DeviceInfoApi.getIntance().isWisePosPro())
				 || !DeviceInfoApi.getIntance().isWisePosPro() || forceInstall) {
					String newApkPath = null;
					// 目前仅适用增量包处理安装
					newApkPath = Constants.APK_FILE_PATH + taskId;

					TaskInfo taskInfo = new TaskInfo();
					taskInfo.setTask_id(taskId);
					taskInfo.setApk_name(apkName);
					taskInfo.setPackage_name(packName);
					taskInfo.setVersion_name(versionName);
					taskInfo.setVersion_code(versionCode);
					taskInfo.setIcon_url(iconUrl);
					taskInfo.setInstall_path(newApkPath);
					MAppPlusDialog.addTask(taskInfo);

					if (!MAppPlusDialog.bAppPlusInstalling) {
						MAppPlusDialog.installAppPlusApk(WSTaskApkUpdateService.this);
					}
				} else {
					String newApkPath = null;
					// 目前仅适用增量包处理安装
					newApkPath = Constants.APK_FILE_PATH + taskId;

					TaskInfo taskInfo = new TaskInfo();
					taskInfo.setTask_id(taskId);
					taskInfo.setApk_name(apkName);
					taskInfo.setPackage_name(packName);
					taskInfo.setVersion_name(versionName);
					taskInfo.setVersion_code(versionCode);
					taskInfo.setIcon_url(iconUrl);
					taskInfo.setInstall_path(newApkPath);
					MAppPlusDialog.addTask(taskInfo);

					Helpers.updateWSTaskStateAndUpload(WSTaskApkUpdateService.this, taskId, TaskState.INSTALL_WAITING, null);
					Helpers.sendBroad(WSTaskApkUpdateService.this, BroadcastActions.APP_PLUS_DOWNLOAD_COMPLETED);
				}
			}

			@Override
			public void requestFail(int errorCode, String errorStr) {
				// TODO Auto-generated method stub
				BBLog.e(TAG, "error in download apk file" + errorStr);
				Helpers.updateWSTaskStateAndUpload(getApplicationContext(), taskId, TaskState.DOWNLOAD_FAILED, null);
			}

			@Override
			public void onDownloading(long curFileSize, long fileSize) {
				//下载进度反馈 TODO
				BBLog.w(BBLog.TAG, "onDownloading " + (int) ((curFileSize * 100) / fileSize) + "%");
			}
		});
	}
}
