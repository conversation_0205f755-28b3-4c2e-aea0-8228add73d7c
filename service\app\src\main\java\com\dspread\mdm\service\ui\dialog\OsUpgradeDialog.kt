package com.dspread.mdm.service.ui.dialog

import android.app.Dialog
import android.content.Context
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.os.Message
import android.view.LayoutInflater
import android.view.View
import android.view.WindowManager
import android.widget.TextView
import com.dspread.mdm.service.R
import com.dspread.mdm.service.utils.log.Logger
import java.util.*

/**
 * OS升级弹框
 * 
 * 功能：
 * - 显示OS升级提示
 * - 5分钟倒计时自动升级
 * - 用户可选择延迟或立即升级
 */
class OsUpgradeDialog private constructor(
    context: Context,
    private val title: String,
    private val message: String,
    private val onDelayClick: () -> Unit,
    private val onInstallClick: () -> Unit
) : Dialog(context, android.R.style.Theme_DeviceDefault_Dialog) {

    companion object {
        private const val TAG = "OsUpgradeDialog"
        private var currentDialog: OsUpgradeDialog? = null
        
        /**
         * 显示OS升级弹框
         */
        fun showOSUpgradeDialog(
            context: Context,
            title: String,
            message: String,
            onDelayClick: () -> Unit,
            onInstallClick: () -> Unit
        ): OsUpgradeDialog {
            Logger.platformI("$TAG 显示OS升级弹框")
            
            // 关闭之前的弹框
            currentDialog?.dismiss()
            currentDialog = null
            
            // 创建新弹框
            val dialog = OsUpgradeDialog(context, title, message, onDelayClick, onInstallClick)
            currentDialog = dialog
            
            // 设置弹框属性
            dialog.setCanceledOnTouchOutside(false)
            dialog.setCancelable(false)
            
            // 设置全屏显示
            dialog.window?.let { window ->
                val layoutParams = window.attributes
                layoutParams.width = WindowManager.LayoutParams.MATCH_PARENT
                layoutParams.height = WindowManager.LayoutParams.MATCH_PARENT
                layoutParams.type = WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY
                window.attributes = layoutParams
            }
            
            dialog.show()
            return dialog
        }
    }

    private lateinit var titleView: TextView
    private lateinit var messageView: TextView
    private lateinit var timeoutView: TextView
    private lateinit var delayButton: TextView
    private lateinit var installButton: TextView
    
    private var timer: Timer? = null
    private var countdownSeconds = 300 // 5分钟 = 300秒
    
    private val handler = object : Handler(Looper.getMainLooper()) {
        override fun handleMessage(msg: Message) {
            super.handleMessage(msg)
            
            when {
                msg.what >= 0 -> {
                    // 更新倒计时显示
                    val seconds = msg.what
                    val minutes = seconds / 60
                    val remainingSeconds = seconds % 60
                    
                    val timeText = if (minutes > 0) {
                        String.format("%02d:%02d", minutes, remainingSeconds)
                    } else {
                        String.format("%02d", remainingSeconds)
                    }
                    
                    timeoutView.text = timeText
                }
                msg.what < 0 -> {
                    // 倒计时结束，自动点击安装
                    Logger.platformI("$TAG 倒计时结束，自动开始升级")
                    installButton.performClick()
                    timer?.cancel()
                }
            }
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        initViews()
        startCountdown()
    }

    private fun initViews() {
        val view = LayoutInflater.from(context).inflate(R.layout.dialog_os_upgrade, null)
        setContentView(view)
        
        titleView = view.findViewById(R.id.tv_title)
        messageView = view.findViewById(R.id.tv_message)
        timeoutView = view.findViewById(R.id.tv_timeout)
        delayButton = view.findViewById(R.id.btn_delay)
        installButton = view.findViewById(R.id.btn_install)
        
        // 设置内容
        titleView.text = title
        messageView.text = message
        
        // 设置按钮点击事件
        delayButton.setOnClickListener {
            Logger.platformI("$TAG 用户选择延迟升级")
            timer?.cancel()
            dismiss()
            onDelayClick()
        }
        
        installButton.setOnClickListener {
            Logger.platformI("$TAG 用户选择立即升级")
            timer?.cancel()
            dismiss()
            onInstallClick()
        }
    }

    private fun startCountdown() {
        timer?.cancel()
        timer = Timer()
        
        val timerTask = object : TimerTask() {
            override fun run() {
                val message = Message()
                message.what = countdownSeconds
                handler.sendMessage(message)
                countdownSeconds--
            }
        }
        
        // 每秒更新一次，立即开始
        timer?.schedule(timerTask, 0, 1000)
    }

    override fun dismiss() {
        timer?.cancel()
        timer = null
        currentDialog = null
        super.dismiss()
    }

    override fun onDetachedFromWindow() {
        timer?.cancel()
        timer = null
        super.onDetachedFromWindow()
    }
}
