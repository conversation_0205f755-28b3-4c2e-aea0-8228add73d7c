package com.bbpos.wiseapp.tms.service;

import android.content.Intent;
import android.os.Environment;
import android.text.TextUtils;

import com.bbpos.wiseapp.logger.BBLog;
import com.bbpos.wiseapp.logger.FileUtil;
import com.bbpos.wiseapp.service.ListeningService;
import com.bbpos.wiseapp.service.WakeLockService;
import com.bbpos.wiseapp.service.common.UsualData;
import com.bbpos.wiseapp.system.api.DeviceInfoApi;
import com.bbpos.wiseapp.tms.model.TaskInfo;
import com.bbpos.wiseapp.tms.network.HttpUtils;
import com.bbpos.wiseapp.tms.utils.BroadcastActions;
import com.bbpos.wiseapp.tms.utils.Constants;
import com.bbpos.wiseapp.tms.utils.FileUtils;
import com.bbpos.wiseapp.tms.utils.Helpers;
import com.bbpos.wiseapp.tms.utils.ParameterName;
import com.bbpos.wiseapp.tms.utils.RulebasedAppListManager;
import com.bbpos.wiseapp.tms.utils.TaskState;
import com.bbpos.wiseapp.tms.widget.MAppPlusDialog;

import org.json.JSONObject;

import java.io.File;
import java.io.IOException;

public class WSTaskUpdateParamService extends WakeLockService {
	private static final String TAG = "UpdateParam";
	private int ret = 0;
	public WSTaskUpdateParamService() {
		super("WSTaskUpdateParamService");
	}
    @Override
    public void onDestroy() {
        super.onDestroy();
		BBLog.w(BBLog.TAG, "WSTaskUpdateParamService 結束 發送 WSTASK_EXEC_BC");
		Helpers.sendBroad(WSTaskUpdateParamService.this, UsualData.WSTASK_EXEC_BC);
    }
	@Override
	protected void onHandleIntent(Intent intent) {
		BBLog.e(TAG, "WSTaskUpdateParamService START");

		final String packName = intent.getExtras().getString(ParameterName.packName);
		final String versionName = intent.getExtras().getString(ParameterName.versionName);
		final String versionCode = intent.getExtras().getString(ParameterName.versionCode);
		final String taskId = intent.getExtras().getString(ParameterName.taskId);
		final String apkName = intent.getExtras().getString(ParameterName.apkName);
		final String appId = intent.getExtras().getString(ParameterName.appId);
		final long totalfileSize = Long.parseLong(intent.getExtras().getString(ParameterName.fileSizeEx));
		final String fileUrl = intent.getExtras().getString(ParameterName.url);
		final String fileName = intent.getExtras().getString(ParameterName.fileName);
		final String fileMd5 = intent.getExtras().getString(ParameterName.fileMd5Ex);
		final String filePath = Environment.getExternalStorageDirectory().getPath()+"/Share"+ File.separator + fileName;
		//如果已经有文件判断是否存在
		File tmpFile = new File(filePath);
		try {
			if (!tmpFile.getCanonicalPath().startsWith(Environment.getExternalStorageDirectory().getCanonicalPath())) {
				BBLog.e("WSTaskUpdateParamService", "Path Traversal");
			}
		} catch (IOException e) {
			e.printStackTrace();
		}
		if (tmpFile.exists()) {
			String localFileMd5 = FileUtils.getMd5ByFile(tmpFile);
			if (fileMd5.equalsIgnoreCase(localFileMd5)) {
				Helpers.updateWSTaskStateAndUpload(WSTaskUpdateParamService.this, taskId, TaskState.UPDATE_SUCCESS, null);
				return;
			} else {
				tmpFile.delete();
			}
		}
		//上送下载中报文
		Helpers.updateWSTaskStateAndUpload(WSTaskUpdateParamService.this, taskId, TaskState.DOWNLOAD_ING, null);
		HttpUtils.fileDownloadByUrlWithRetry(fileUrl, filePath, totalfileSize, fileMd5, new HttpUtils.FileDownloadCallBack() {
			@Override
			public void requestSuccess(JSONObject responseJson) throws Exception {
				// TODO Auto-generated method stub
				Helpers.updateWSTaskStateAndUpload(WSTaskUpdateParamService.this, taskId, TaskState.UPDATE_SUCCESS, null);
			}

			@Override
			public void requestFail(int errorCode, String errorStr) {
				// TODO Auto-generated method stub
				BBLog.e(TAG, "error in download apk file" + errorStr);
				Helpers.updateWSTaskStateAndUpload(getApplicationContext(), taskId, TaskState.DOWNLOAD_FAILED, null);
			}

			@Override
			public void onDownloading(long curFileSize, long fileSize) {
				//下载进度反馈 TODO
				BBLog.w(BBLog.TAG, "onDownloading " + (int) ((curFileSize * 100) / fileSize) + "%");
			}
		});
	}
}
