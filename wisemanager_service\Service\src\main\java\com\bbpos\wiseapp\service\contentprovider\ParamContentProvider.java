package com.bbpos.wiseapp.service.contentprovider;

import android.content.ContentProvider;
import android.content.ContentValues;
import android.database.Cursor;
import android.database.sqlite.SQLiteDatabase;
import android.net.Uri;
import android.os.Binder;

import com.bbpos.wiseapp.logger.BBLog;
import com.bbpos.wiseapp.service.contentprovider.db.ParamDbHelper;
import com.bbpos.wiseapp.service.contentprovider.db.ParamDbOperation;

import java.util.regex.Pattern;

public class ParamContentProvider extends ContentProvider  {
	private ParamDbHelper db;

	@Override
	public int delete(Uri arg0, String arg1, String[] arg2) {
		return 0;
	}

	@Override
	public String getType(Uri arg0) {
		return null;
	}

	@Override
	public Uri insert(Uri arg0, ContentValues arg1) {
		BBLog.e(BBLog.TAG, "ParamContentProvider update-----");
		String callingPackageName = getContext().getPackageManager().getNameForUid(Binder.getCallingUid());
		BBLog.d(BBLog.TAG, "calling Package Name::" + callingPackageName);
		SQLiteDatabase sqdb = db.getWritableDatabase();
		Cursor mCursor = sqdb.query(true, ParamDbHelper.TBL_PARAM_NAME,
				new String[] {"param_value"}, "param_key = '"+arg1.get("param_key")
						+"' and pkg_name = '"+callingPackageName+"'", null, null, null, null, null);
		if (mCursor == null || mCursor.getCount() == 0) {
			if (arg1.containsKey("pkg_name")) {
				if (arg1.get("pkg_name") != callingPackageName) {
					return null;
				}
			} else {
				arg1.put("pkg_name", callingPackageName);
			}
			sqdb.insert(ParamDbHelper.TBL_PARAM_NAME, null, arg1);
		}
		return arg0;
	}

	@Override
	public boolean onCreate() {
		BBLog.i(BBLog.TAG, "ParamContentProvider onCreate");
		db = ParamDbOperation.open(getContext());
		return false;
	}

	@Override
	public Cursor query(Uri arg0, String[] arg1, String arg2, String[] arg3, String arg4) {
		BBLog.e(BBLog.TAG, "ParamContentProvider query-----");
		String callingPackageName = getContext().getPackageManager().getNameForUid(Binder.getCallingUid());
		BBLog.d(BBLog.TAG, "calling Package Name::" + callingPackageName);
		SQLiteDatabase sqdb = db.getWritableDatabase();
		Cursor cursor = null;
		if (arg2 != null) {
			arg2 += " AND pkg_name=" + "'"+callingPackageName+"'";
		} else {
			arg2 = "pkg_name=" + "'"+callingPackageName+"'";
		}
		cursor = sqdb.query(ParamDbHelper.TBL_PARAM_NAME, arg1, arg2, arg3, null, null, arg4);
		return cursor;
	}

	@Override
	public int update(Uri arg0, ContentValues arg1, String arg2, String[] arg3) {
		BBLog.e(BBLog.TAG, "ParamContentProvider update-----");
		String callingPackageName = getContext().getPackageManager().getNameForUid(Binder.getCallingUid());
		BBLog.d(BBLog.TAG, "calling Package Name::" + callingPackageName);
		SQLiteDatabase sqdb = db.getWritableDatabase();
		if (arg2 != null) {
			arg2 += " AND pkg_name=" + "'"+callingPackageName+"'";
		} else {
			arg2 = "pkg_name=" + "'"+callingPackageName+"'";
		}

		if (!Pattern.matches("^(.+)\\sand\\s(.+)|(.+)\\sor(.+)\\s$", arg2)) {
			return 0;
		}
		return sqdb.update(ParamDbHelper.TBL_PARAM_NAME, arg1, arg2, arg3);
	}
}
