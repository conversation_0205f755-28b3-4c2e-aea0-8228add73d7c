package com.bbpos.wiseapp.service;

import android.app.IntentService;
import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.content.SharedPreferences;
import android.graphics.Color;
import android.os.Build;
import android.os.PowerManager;
import android.os.PowerManager.WakeLock;

import java.net.InetSocketAddress;

/** WakeLock服务公共类，处理WakeLock */
public abstract class WakeLockService extends IntentService {

    protected String mName;
    protected WakeLock mWakeLock;
    protected SharedPreferences mPrefs;
    protected InetSocketAddress mTmsAddr;

    public WakeLockService(String name) {
        super(name);
        this.mName = name;
    }

    @Override
    public void onCreate() {
        super.onCreate();
        super.setIntentRedelivery(true);
        // 获取WakeLock
        this.getWakeLock();

        /*
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            String CHANNEL_ONE_ID = "com.bbpos.wisemanager";
            String CHANNEL_ONE_NAME = "Channel One";
            NotificationChannel notificationChannel = null;
            notificationChannel = new NotificationChannel(CHANNEL_ONE_ID,
                    CHANNEL_ONE_NAME, NotificationManager.IMPORTANCE_HIGH);
            notificationChannel.enableLights(true);
            notificationChannel.setLightColor(Color.RED);
            notificationChannel.setShowBadge(true);
            notificationChannel.setLockscreenVisibility(Notification.VISIBILITY_PUBLIC);
            NotificationManager manager = (NotificationManager) getSystemService(NOTIFICATION_SERVICE);
            manager.createNotificationChannel(notificationChannel);

            Notification notification = new Notification.Builder(this)
                    .setChannelId(CHANNEL_ONE_ID)
                    .setWhen(System.currentTimeMillis())//设置创建时间
                    .setTicker("Nature")
//                .setSmallIcon(R.mipmap.ic_launcher)
//                .setContentTitle("xxxx")
//                .setContentText(musicList.size() > 0 && musicList != null ? musicList.get(currentMusic).radio_en_desc:"xxxxx")
                    .build();
//                .getNotification();
            notification.flags |= Notification.FLAG_NO_CLEAR;
            startForeground(1, notification);
        }
        */
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        this.reaseWakeLock();
    }

    private void getWakeLock() {
        PowerManager pm = (PowerManager) getSystemService(POWER_SERVICE);
        this.mWakeLock = pm.newWakeLock(PowerManager.PARTIAL_WAKE_LOCK, mName);
        this.mWakeLock.acquire();
    }

    private void reaseWakeLock() {
        if (mWakeLock != null) {
        	 this.mWakeLock.release();
        }
    }
}
