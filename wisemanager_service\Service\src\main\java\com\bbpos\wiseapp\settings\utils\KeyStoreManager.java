package com.bbpos.wiseapp.settings.utils;

import android.content.Context;
import android.os.Build;
import androidx.annotation.RequiresApi;
import android.text.TextUtils;
import android.util.Base64;

import java.security.KeyPair;
import java.security.interfaces.RSAPublicKey;

/**
 * keystore 操作管理类
 *
 *  eg:
 *   //初始化
 *   KeyStoreManager manager = new KeyStoreManager(this,"wiseapp");
 *
 *   //加密
 *   manager.encryptData(encryptionString)
 *
 *   //解密
 *   tvDecode.setText(manager.decryptData(decodeString));
 */
@RequiresApi(api = Build.VERSION_CODES.JELLY_BEAN_MR2)
public class KeyStoreManager {
    private  Context mContext;
    private RSAPublicKey publicKey;
    private String alais;

    public KeyStoreManager(Context context, String alias){
        this.mContext = context;
        this.alais = alias;
        checkAlais();
        initRSAKeyPairByAlaias();
    }

    private void checkAlais() {
        if (TextUtils.isEmpty(alais)) {
            this.alais = KeyStoreRSAUtil.SAMPLE_ALIAS;
        }
        KeyStoreRSAUtil.setAlias(alais);
    }

    /**
     * 根据alais 初始化公钥
     */
    private void initRSAKeyPairByAlaias(){
        try {//在项目中放在application或启动页中
            if (!KeyStoreRSAUtil.isHaveKeyStore()) {
                KeyPair keyPair = KeyStoreRSAUtil.generateRSAKeyPair(mContext);
                // 公钥
                publicKey = (RSAPublicKey) keyPair.getPublic();
            }else {
                publicKey = getPublicKey();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    /**
     * 根据key别名获取公钥
     *
     * @return
     */
    private RSAPublicKey getPublicKey(){
        try {
            publicKey = (RSAPublicKey) KeyStoreRSAUtil.getLocalPublicKey();
            if (publicKey == null) {
                KeyPair keyPair = KeyStoreRSAUtil.generateRSAKeyPair(mContext);
                // 公钥
                publicKey = (RSAPublicKey) keyPair.getPublic();
            }
            return publicKey;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 使用公钥进行内容加密
     * @param data
     * @return
     */
    public String encryptData(String data){
        if (TextUtils.isEmpty(data)) return null;
        try {
            byte[] encryptBytes = KeyStoreRSAUtil.encryptByPublicKeyForSpilt(data.getBytes(),
                   publicKey.getEncoded());
            return (new String(Base64.encode(encryptBytes,Base64.DEFAULT),"utf-8"));
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 使用私钥进行解密
     * @param data
     * @return
     */
    public String decryptData(String data){
        if (TextUtils.isEmpty(data)) return null;
        try {
            byte[] decryptBytes = KeyStoreRSAUtil.decryptByPrivateKeyForSpilt(
                    Base64.decode(data,Base64.DEFAULT));
           return (new String(decryptBytes,"utf-8"));
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }
}
