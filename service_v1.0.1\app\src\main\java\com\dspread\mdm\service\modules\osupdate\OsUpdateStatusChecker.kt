package com.dspread.mdm.service.modules.osupdate

import android.content.Context
import android.content.SharedPreferences
import android.os.Build
import android.preference.PreferenceManager
import com.dspread.mdm.service.constants.TaskStateConstants
import com.dspread.mdm.service.network.websocket.message.WsMessageSender
import com.dspread.mdm.service.network.websocket.task.WsTaskManager
import com.dspread.mdm.service.utils.log.Logger
import kotlinx.coroutines.*

/**
 * OS升级状态检查器
 * 
 * 功能：
 * - 系统启动时检查是否有待验证的OS升级任务
 * - 比较升级前后的版本号判断升级是否成功
 * - 上报升级结果到服务器
 * - 清理升级任务标记
 */
object OsUpdateStatusChecker {
    
    private const val TAG = "OsUpdateStatusChecker"
    
    // SharedPreferences键名
    private const val KEY_OS_UPDATE_TASK_ID = "os_update_task_id"
    private const val KEY_OS_UPDATE_TARGET_VERSION = "os_update_target_version"
    private const val KEY_OS_UPDATE_IN_PROGRESS = "os_update_in_progress"
    
    /**
     * WebSocket连接成功后检查OS升级状态
     * 确保WebSocket连接可用时才检查和上报升级结果
     */
    fun checkOsUpdateStatusOnBoot(context: Context) {
        Logger.com("$TAG WebSocket连接成功，检查OS升级状态")
        
        try {
            val sp = PreferenceManager.getDefaultSharedPreferences(context)
            val taskId = sp.getString(KEY_OS_UPDATE_TASK_ID, "")
            val targetVersion = sp.getString(KEY_OS_UPDATE_TARGET_VERSION, "")
            val isUpgradeInProgress = sp.getBoolean(KEY_OS_UPDATE_IN_PROGRESS, false)
            
            if (taskId.isNullOrEmpty() || targetVersion.isNullOrEmpty() || !isUpgradeInProgress) {
                Logger.com("$TAG 没有待检查的OS升级任务")
                return
            }
            
            Logger.com("$TAG 发现待检查的OS升级任务: $taskId, 目标版本: $targetVersion")
            
            // 延迟5秒执行检查，确保WebSocket连接稳定
            GlobalScope.launch(Dispatchers.IO) {
                delay(5000)
                performOsUpdateStatusCheck(context, taskId!!, targetVersion!!)
            }
            
        } catch (e: Exception) {
            Logger.comE("$TAG 检查OS升级状态失败", e)
        }
    }
    
    /**
     * 保存OS升级任务信息（升级开始前调用）
     */
    fun saveOsUpdateTaskInfo(context: Context, taskId: String, targetVersion: String) {
        try {
            val sp = PreferenceManager.getDefaultSharedPreferences(context)
            val editor = sp.edit()
            editor.putString(KEY_OS_UPDATE_TASK_ID, taskId)
            editor.putString(KEY_OS_UPDATE_TARGET_VERSION, targetVersion)
            editor.putBoolean(KEY_OS_UPDATE_IN_PROGRESS, true)
            editor.apply()
            
            Logger.com("$TAG 保存OS升级任务信息: $taskId, 目标版本: $targetVersion")
            
        } catch (e: Exception) {
            Logger.comE("$TAG 保存OS升级任务信息失败", e)
        }
    }
    
    /**
     * 执行OS升级状态检查
     */
    private suspend fun performOsUpdateStatusCheck(context: Context, taskId: String, targetVersion: String) {
        try {
            Logger.com("$TAG 开始检查OS升级结果")
            
            // 获取当前系统版本
            val currentVersion = getCurrentSystemVersion()
            Logger.com("$TAG 版本比较: 当前版本=$currentVersion, 目标版本=$targetVersion")
            
            // 判断升级是否成功
            val isUpgradeSuccess = isVersionMatched(context, currentVersion, targetVersion)
            
            if (isUpgradeSuccess) {
                Logger.com("$TAG OS升级成功")
                reportOsUpdateResult(context, taskId, TaskStateConstants.UPDATE_SUCCESS, "OS升级成功")
            } else {
                Logger.comE("$TAG OS升级失败，版本不匹配")
                reportOsUpdateResult(context, taskId, TaskStateConstants.UPDATE_FAILED, "OS升级失败，版本验证不通过")
            }
            
            // 清理升级任务标记和升级前版本记录
            clearOsUpdateTaskInfo(context)
            clearPreUpgradeVersion(context)
            
        } catch (e: Exception) {
            Logger.comE("$TAG 执行OS升级状态检查失败", e)
            
            // 出错时也要清理标记，避免重复检查
            clearOsUpdateTaskInfo(context)
            clearPreUpgradeVersion(context)
        }
    }
    
    /**
     * 获取当前系统版本
     */
    private fun getCurrentSystemVersion(): String {
        return try {
            // 根据Android版本使用不同的版本获取方式
            when {
                Build.VERSION.SDK_INT >= 34 -> {
                    // Android 14+: 可能需要特殊处理A/B分区版本
                    Build.DISPLAY ?: Build.VERSION.INCREMENTAL ?: ""
                }
                else -> {
                    // Android < 14: 使用传统方式
                    Build.DISPLAY ?: Build.VERSION.INCREMENTAL ?: ""
                }
            }
        } catch (e: Exception) {
            Logger.comE("$TAG 获取当前系统版本失败", e)
            ""
        }
    }
    
    /**
     * 判断版本是否匹配（升级是否成功）
     * 新逻辑：比较升级前后版本是否不同，如果不同则认为升级成功
     */
    private fun isVersionMatched(context: Context, currentVersion: String, targetVersion: String): Boolean {
        return try {
            if (currentVersion.isEmpty()) {
                Logger.comW("$TAG 当前版本信息为空，无法比较")
                return false
            }

            // 获取升级前的版本（从SharedPreferences中读取）
            val preUpgradeVersion = getPreUpgradeVersion(context)

            Logger.com("$TAG 版本比较:")
            Logger.com("$TAG   升级前版本: '$preUpgradeVersion'")
            Logger.com("$TAG   当前版本: '$currentVersion'")
            Logger.com("$TAG   目标版本: '$targetVersion'")

            // 新逻辑：如果当前版本与升级前版本不同，则认为升级成功
            val isUpgradeSuccess = if (preUpgradeVersion.isNotEmpty()) {
                !currentVersion.equals(preUpgradeVersion, ignoreCase = true)
            } else {
                // 如果没有升级前版本记录，降级使用目标版本比较
                // 但是使用包含关系而不是严格相等
                currentVersion.contains(targetVersion, ignoreCase = true) ||
                targetVersion.contains(currentVersion, ignoreCase = true)
            }

            Logger.com("$TAG 升级成功判定: $isUpgradeSuccess")

            isUpgradeSuccess

        } catch (e: Exception) {
            Logger.comE("$TAG 版本比较失败", e)
            false
        }
    }
    
    /**
     * 上报OS升级结果
     */
    private suspend fun reportOsUpdateResult(context: Context, taskId: String, taskResult: String, message: String) {
        try {
            Logger.com("$TAG 上报OS升级结果: $taskId -> $taskResult")
            
            // 1. 更新本地任务状态
            WsTaskManager.updateWSTaskState(taskId, taskResult)
            
            // 2. 上报任务结果到服务器
            WsMessageSender.uploadTaskResult(taskId, taskResult, message)
            
            Logger.com("$TAG OS升级结果上报完成: $taskId -> $taskResult")
            
        } catch (e: Exception) {
            Logger.comE("$TAG 上报OS升级结果失败: $taskId", e)
        }
    }
    
    /**
     * 清理OS升级任务信息
     */
    private fun clearOsUpdateTaskInfo(context: Context) {
        try {
            val sp = PreferenceManager.getDefaultSharedPreferences(context)
            val editor = sp.edit()
            editor.remove(KEY_OS_UPDATE_TASK_ID)
            editor.remove(KEY_OS_UPDATE_TARGET_VERSION)
            editor.remove(KEY_OS_UPDATE_IN_PROGRESS)
            editor.apply()

            Logger.com("$TAG 清理OS升级任务信息完成")

            // OS升级完成后清理升级包文件
            clearOsUpdatePackageFiles(context)

        } catch (e: Exception) {
            Logger.comE("$TAG 清理OS升级任务信息失败", e)
        }
    }

    /**
     * 清理OS升级包文件
     * 升级完成后删除下载的升级包，为下次升级腾出空间
     */
    private fun clearOsUpdatePackageFiles(context: Context) {
        try {
            Logger.com("$TAG 开始清理OS升级包文件...")

            // 清理OS升级包存储路径（基于实际代码中的路径）
            val updatePaths = listOf(
                "/sdcard/Android/data/${context.packageName}/files/updates",  // 主要下载目录（TaskHandler中定义）
                "/cache",                                                      // 系统缓存目录（RecoverySystem使用）
                "/data/ota_package"                                            // 系统OTA包目录（部分设备使用）
            )

            var totalClearedSize = 0L
            var clearedFileCount = 0

            updatePaths.forEach { path ->
                val updateDir = java.io.File(path)
                if (updateDir.exists() && updateDir.isDirectory) {
                    updateDir.listFiles()?.forEach { file ->
                        if (file.isFile && isOsUpdateFile(file)) {
                            val fileSize = file.length()
                            if (file.delete()) {
                                totalClearedSize += fileSize
                                clearedFileCount++
                                Logger.com("$TAG 删除升级包文件: ${file.absolutePath} (${fileSize / 1024 / 1024}MB)")
                            }
                        }
                    }
                }
            }

            if (clearedFileCount > 0) {
                Logger.com("$TAG OS升级包清理完成: 删除 $clearedFileCount 个文件，释放 ${totalClearedSize / 1024 / 1024}MB 空间")
            } else {
                Logger.com("$TAG 没有找到需要清理的OS升级包文件")
            }

        } catch (e: Exception) {
            Logger.comE("$TAG 清理OS升级包文件失败", e)
        }
    }

    /**
     * 判断是否为OS升级文件
     */
    private fun isOsUpdateFile(file: java.io.File): Boolean {
        val fileName = file.name.lowercase()
        return when {
            // 标准OTA升级包
            fileName.endsWith(".zip") && (fileName.contains("update") || fileName.contains("ota")) -> true
            // 系统镜像文件
            fileName.endsWith(".img") -> true
            // 二进制升级包
            fileName.endsWith(".bin") && fileName.contains("update") -> true
            // 特定的升级包文件名
            fileName == "update.zip" -> true
            fileName == "ota.zip" -> true
            // 其他可能的升级包格式
            fileName.endsWith(".zip") && file.length() > 50 * 1024 * 1024 -> true  // 大于50MB的ZIP文件
            else -> false
        }
    }

    /**
     * 保存升级前的系统版本
     */
    fun savePreUpgradeVersion(context: Context) {
        try {
            val currentVersion = getCurrentSystemVersion()
            val prefs = context.getSharedPreferences("os_update_status", Context.MODE_PRIVATE)
            prefs.edit().putString("pre_upgrade_version", currentVersion).apply()
            Logger.com("$TAG 保存升级前版本: $currentVersion")
        } catch (e: Exception) {
            Logger.comE("$TAG 保存升级前版本失败", e)
        }
    }

    /**
     * 获取升级前的系统版本
     */
    private fun getPreUpgradeVersion(context: Context): String {
        return try {
            val prefs = context.getSharedPreferences("os_update_status", Context.MODE_PRIVATE)
            prefs.getString("pre_upgrade_version", "") ?: ""
        } catch (e: Exception) {
            Logger.comE("$TAG 获取升级前版本失败", e)
            ""
        }
    }

    /**
     * 清理升级前版本记录
     */
    private fun clearPreUpgradeVersion(context: Context) {
        try {
            val prefs = context.getSharedPreferences("os_update_status", Context.MODE_PRIVATE)
            prefs.edit().remove("pre_upgrade_version").apply()
            Logger.com("$TAG 清理升级前版本记录")
        } catch (e: Exception) {
            Logger.comE("$TAG 清理升级前版本记录失败", e)
        }
    }

    /**
     * 手动触发OS升级状态检查（用于测试）
     */
    fun manualCheckOsUpdateStatus(context: Context) {
        Logger.com("$TAG 手动触发OS升级状态检查")
        checkOsUpdateStatusOnBoot(context)
    }
}
