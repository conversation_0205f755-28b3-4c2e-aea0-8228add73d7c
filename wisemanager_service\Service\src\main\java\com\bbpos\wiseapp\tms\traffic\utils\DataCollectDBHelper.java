package com.bbpos.wiseapp.tms.traffic.utils;

import android.content.ContentValues;
import android.content.Context;
import android.database.Cursor;
import android.database.sqlite.SQLiteDatabase;
import android.database.sqlite.SQLiteOpenHelper;
import android.util.Log;

import com.bbpos.wiseapp.logger.BBLog;
import com.bbpos.wiseapp.tms.traffic.model.TrafficData;
import com.bbpos.wiseapp.tms.traffic.model.UsageData;

import java.util.ArrayList;
import java.util.List;

public class DataCollectDBHelper extends SQLiteOpenHelper{
	private static final String TAG = DataCollectDBHelper.class.getName();
	private final static String DATABASE_NAME = "data_collect.db";
	private final static int DATABASE_VERSION = 1;
	private final static String USAGE_TABLE_NAME = "usage";
	private final static String TRAFFIC_TABLE_NAME = "traffic";
	private final static String TRAFFIC_TMP_TABLE_NAME = "traffic_tmp";

	public DataCollectDBHelper(Context context) {
		super(context, DATABASE_NAME, null, DATABASE_VERSION);
	}

	@Override
	public void onCreate(SQLiteDatabase db) {
		BBLog.v(BBLog.TAG, "create table if not exists");
		String sql = "CREATE TABLE IF NOT EXISTS " + TRAFFIC_TABLE_NAME + " (" 
				+ TrafficData.DATA_DATE + " TEXT, "
				+ TrafficData.APP_NAME + " TEXT, "
				+ TrafficData.PKG_NAME + " TEXT, "
				+ TrafficData.VERSION_CODE+ " INT, "
				+ TrafficData.VERSION_NAME + " TEXT, "
				+ TrafficData.NET_TYPE + " TEXT, "
				+ TrafficData.RX_BYTES + " INT, "
				+ TrafficData.TX_BYTES + " INT "+")";
		db.execSQL(sql);
		
		sql = "CREATE TABLE IF NOT EXISTS " + TRAFFIC_TMP_TABLE_NAME + " (" 
				+ TrafficData.PKG_NAME + " TEXT, "
				+ TrafficData.VERSION_CODE+ " INT, "
				+ TrafficData.RX_BYTES + " INT, "
				+ TrafficData.TX_BYTES + " INT"+")";
		db.execSQL(sql);
		
		sql = "CREATE TABLE IF NOT EXISTS " + USAGE_TABLE_NAME + " (" 
				+ UsageData.DATA_DATE + " TEXT, "
				+ UsageData.PKG_NAME+ " TEXT, "
				+ UsageData.VERSION_CODE+ " INT, "
				+ UsageData.VERSION_NAME + " TEXT, "
				+ UsageData.LAUNCK_COUNT + " INT, "
				+ UsageData.TOTAL_TIME_IN_FOREGROUND + " INT"+")";
		db.execSQL(sql);
	}

	@Override
	public void onUpgrade(SQLiteDatabase db, int oldVersion, int newVersion) {
		String sql = "DROP TABLE IF EXISTS " + TRAFFIC_TABLE_NAME;
		db.execSQL(sql);
		sql = "DROP TABLE IF EXISTS " + TRAFFIC_TMP_TABLE_NAME;
		db.execSQL(sql);
		sql = "DROP TABLE IF EXISTS " + USAGE_TABLE_NAME;
		db.execSQL(sql);
		onCreate(db);
	}

	/**
	 * 累加流量数据至该应用的原数据下，若原数据不存在，则新增
	 * @param date
	 * @param netType 数据网络类型
	 * @param packageName 
	 * @param appName
	 * @param versionCode
	 * @param versionName
	 * @param rxBytes 下载流量
	 * @param txBytes 上传流量
	 */
	public void appendTrafficData(String date, String netType, String packageName, String appName,
			int versionCode, String versionName, long rxBytes, long txBytes) {
		BBLog.v(BBLog.TAG, "appendTrafficData start.pkgName:"+packageName);
		SQLiteDatabase db = this.getReadableDatabase();
		String selectionStr = TrafficData.DATA_DATE + "=? AND " + TrafficData.NET_TYPE + "=? AND "
							+ TrafficData.PKG_NAME + "=? AND " + TrafficData.VERSION_CODE + "=? ";
		String selectionArgStr = date+","+netType+","+packageName+","+versionCode;
		String[] selectionArgs = selectionArgStr.split(",");
		Cursor cursor = db.query(TRAFFIC_TABLE_NAME, null, selectionStr, selectionArgs, null, null, null);		
		
		if(cursor != null && cursor.getCount() >0){ 
			if(!isCursorSingle(cursor)){
				BBLog.e(BBLog.TAG, "traffic data must be single");
				db.delete(TRAFFIC_TABLE_NAME, selectionStr, selectionArgs);
				//删除错误数据 新增
				addTrafficData(new TrafficData(date, packageName, appName, versionCode, versionName, netType, rxBytes, txBytes));
				cursor.close();
				return;
			}
			//原数据存在 进行累加
			TrafficData dataModel = null;
			while (cursor.moveToNext()) {
				dataModel = new TrafficData(cursor, TrafficData.DATA_TYPE_TRAFFIC);
			}
			dataModel.rxBytes += rxBytes;
			dataModel.txBytes += txBytes;
			
			updateTrafficData(dataModel);
			
		}else{
			//原数据不存在 直接新增
			addTrafficData(new TrafficData(date, packageName, appName, versionCode, versionName, netType, rxBytes, txBytes));
		}
		cursor.close();
		db.close();
	}

	/**
	 * 记录当前流量为上次流量数据，以便下次统计时间段内流量
	 * @param nowDate
	 * @param packageName
	 * @param versionCode
	 * @param rxBytes 下载流量
	 * @param txBytes 上传流量
	 * @return 
	 */
	public long recordLastTimeTrafficData(String packageName,int versionCode,
			long rxBytes, long txBytes) {
		//先删除旧的同条记录 
		deleteLastTimeTrafficData(packageName,versionCode);
		
		SQLiteDatabase db = this.getWritableDatabase();
		ContentValues cv = new ContentValues();
		cv.put(TrafficData.PKG_NAME, packageName);
		cv.put(TrafficData.VERSION_CODE, versionCode);
		cv.put(TrafficData.RX_BYTES, rxBytes);
		cv.put(TrafficData.TX_BYTES, txBytes);
		long flag = db.insert(TRAFFIC_TMP_TABLE_NAME, null, cv);
		db.close();
		return flag;
	}

	/**
	 * 更新使用状态 若未存在 则新增
	 * @param date 
	 * @param pkgName
	 * @param launchCount 使用次数
	 * @param totalTimeInForeground 使用时长 单位毫秒
	 */
	public void updateUsageStat(String date, String pkgName,int versionCode,String versionName,
			int launchCount, long totalTimeInForeground) {
		SQLiteDatabase db = this.getReadableDatabase();
		String selectionStr = UsageData.DATA_DATE + "=? AND " + UsageData.PKG_NAME + "=? AND " + UsageData.VERSION_CODE + "=?";
		String selectionArgStr = date+","+pkgName+","+versionCode;
		BBLog.v(BBLog.TAG, "updateUsageStat selectionArgStr:"+selectionArgStr);
		String[] selectionArgs = selectionArgStr.split(",");
		Cursor cursor = db.query(USAGE_TABLE_NAME, null, selectionStr, selectionArgs, null, null, null);		
		
		if(cursor !=null && cursor.getCount() >0){ 
			if(!isCursorSingle(cursor)){
				BBLog.e(BBLog.TAG, "updateUsageStat() -usage data must be single");
				db.delete(USAGE_TABLE_NAME, selectionStr, selectionArgs);
				//删除错误数据 新增
				addUsageData(new UsageData(date, pkgName, versionCode,versionName,launchCount,totalTimeInForeground));
				cursor.close();
				db.close();
				return;
			}
			//原数据存在 进行更新
			UsageData dataModel = null;
			while (cursor.moveToNext()) {
				dataModel = new UsageData(cursor);
			}
			
			ContentValues cv = new ContentValues();
			cv.put(UsageData.LAUNCK_COUNT, dataModel.launchCount);
			cv.put(UsageData.TOTAL_TIME_IN_FOREGROUND, dataModel.totalTimeInForeground);
			db.update(USAGE_TABLE_NAME, cv, selectionStr, selectionArgs);
		}else{
			//原数据不存在 直接新增
			addUsageData(new UsageData(date, pkgName, versionCode,versionName,launchCount,totalTimeInForeground));
		}
		cursor.close();
		db.close();
	}

	/**新增使用状态统计数据*/
	public long addUsageData(UsageData dataModel) {
		if (dataModel == null) {
			return -1;
		}
		SQLiteDatabase db = this.getWritableDatabase();
		
		ContentValues cv = new ContentValues();
		cv.put(UsageData.DATA_DATE, dataModel.dataDate);
		cv.put(UsageData.PKG_NAME, dataModel.pkgName);
		cv.put(UsageData.VERSION_CODE, dataModel.versionCode);
		cv.put(UsageData.VERSION_NAME, dataModel.versionName);
		cv.put(UsageData.LAUNCK_COUNT, dataModel.launchCount);
		cv.put(UsageData.TOTAL_TIME_IN_FOREGROUND, dataModel.totalTimeInForeground);
		long flag = db.insert(USAGE_TABLE_NAME, null, cv);
		db.close();
		return flag;
	}

	/**
	 * 删除上次流量数据
	 * @param msg
	 * @return
	 */
	public int deleteLastTimeTrafficData() {
		SQLiteDatabase db = this.getWritableDatabase();
		int flag = db.delete(TRAFFIC_TMP_TABLE_NAME, null, null);
		db.close();
		return flag;
	}
	
	/**
	 * 删除上次流量数据
	 * @param msg
	 * @return
	 */
	public int deleteLastTimeTrafficData(String pkgName,int versionCode) {
		SQLiteDatabase db = this.getWritableDatabase();
		String selectionStr = TrafficData.PKG_NAME + "=? AND " + TrafficData.VERSION_CODE + "=?";
		String selectionArgStr = pkgName+","+versionCode;
		int flag =  db.delete(TRAFFIC_TMP_TABLE_NAME, selectionStr, selectionArgStr.split(","));
		db.close();
		return flag;
	}
	
	/**
	 * 获取上次统计流量数据
	 * @return last_time
	 */
	public TrafficData getLasTimeTraffic(String pkgName, int versionCode) {
		SQLiteDatabase db = this.getWritableDatabase();
		String selectionStr = TrafficData.PKG_NAME + "=? AND " + TrafficData.VERSION_CODE + "=?";
		String selectionArgStr = pkgName+","+versionCode;
		TrafficData dataModel = null;
		Cursor cursor = null;
		try {
			cursor = db.query(TRAFFIC_TMP_TABLE_NAME, null, selectionStr, selectionArgStr.split(","), null, null, null);
			if (cursor != null && cursor.getCount() >0) {
				if(!isCursorSingle(cursor)){
					BBLog.e(BBLog.TAG, "last traffic data must be single");
					cursor.close();
					return null;
				}
				while (cursor.moveToNext()) {
					dataModel = new TrafficData(cursor, TrafficData.DATA_TYPE_LAST_TRAFFIC);
				}
			}
		} catch (Exception e) {
			// TODO: handle exception
		}finally{
			if(cursor !=null)
				cursor.close();
			db.close();
		}

		return dataModel;
	}
	
	public boolean isCursorSingle(Cursor cursor){
		if(cursor == null){
			return false;
		}
		if(cursor.getCount() == 1)
			return true;
		return false;
	}
	
	/**更新流量数据*/
	private int updateTrafficData(TrafficData dataModel) {
		if (dataModel == null) {
			return -1;
		}
		SQLiteDatabase db = this.getWritableDatabase();
		String selectionStr = TrafficData.DATA_DATE + "=? AND " + TrafficData.NET_TYPE + "=? AND "
				+ TrafficData.PKG_NAME + "=? AND " + TrafficData.VERSION_CODE + "=? ";
		String selectionArgStr = dataModel.dataDate+","+dataModel.netType+","+dataModel.pkgName+","+dataModel.versionCode;
		String[] selectionArgs = selectionArgStr.split(",");
		
		ContentValues cv = new ContentValues();
		cv.put(TrafficData.TX_BYTES, dataModel.txBytes);
		cv.put(TrafficData.RX_BYTES, dataModel.rxBytes);
		int flag  =  db.update(TRAFFIC_TABLE_NAME, cv, selectionStr, selectionArgs);
		db.close();
		return flag;
	}
	
	/**新增流量数据*/
	private long addTrafficData(TrafficData dataModel) {
		if (dataModel == null) {
			return -1;
		}
		SQLiteDatabase db = this.getWritableDatabase();
		
		ContentValues cv = new ContentValues();
		cv.put(TrafficData.DATA_DATE, dataModel.dataDate);
		cv.put(TrafficData.PKG_NAME, dataModel.pkgName);
		cv.put(TrafficData.VERSION_CODE, dataModel.versionCode);
		cv.put(TrafficData.NET_TYPE, dataModel.netType);
		
		cv.put(TrafficData.APP_NAME, dataModel.appName);
		cv.put(TrafficData.VERSION_NAME, dataModel.versionName);
		cv.put(TrafficData.TX_BYTES, dataModel.txBytes);
		cv.put(TrafficData.RX_BYTES, dataModel.rxBytes);
		long flag = db.insert(TRAFFIC_TABLE_NAME, null, cv);
		db.close();
		return flag;
	}
	
	public List<UsageData> getUsageDataList(String dateStr){
		BBLog.v(BBLog.TAG, "getUsageDataList...");
		SQLiteDatabase db = this.getReadableDatabase();
		List<UsageData> usageList = new ArrayList<UsageData>();
		String selectionStr = UsageData.DATA_DATE + "="+dateStr;
		
		Cursor cursor = db.query(USAGE_TABLE_NAME, null, selectionStr, null, null, null, null);
		if(cursor != null && cursor.getCount()>0)
			while(cursor.moveToNext()){
				usageList.add(new UsageData(cursor));
			}
		cursor.close();
		db.close();
		return usageList;
	}
	
	public List<TrafficData> getTrafficDataList(String dateStr){
//		BBLog.v(BBLog.TAG, "getTrafficDataList...");
		SQLiteDatabase db = this.getReadableDatabase();
		List<TrafficData> trafficList = new ArrayList<TrafficData>();
		String selectionStr = TrafficData.DATA_DATE + "="+dateStr;
		Cursor cursor = null;
		try {
			cursor = db.query(TRAFFIC_TABLE_NAME, null, selectionStr, null, null, null, null);
			if(cursor != null && cursor.getCount()>0)
				while(cursor.moveToNext()){
					trafficList.add(new TrafficData(cursor, TrafficData.DATA_TYPE_TRAFFIC));
				}
		} catch (Exception e) {
			// TODO: handle exception
		}finally{
			if(cursor != null){
				cursor.close();
			}
			db.close();
		}	
		return trafficList;
	}
	
	public List<TrafficData> getTrafficDataList(String dateStr, String pkgName){
//		BBLog.v(BBLog.TAG, "getTrafficData...");
		SQLiteDatabase db = this.getReadableDatabase();
		List<TrafficData> trafficList = new ArrayList<TrafficData>();
		String selectionStr = TrafficData.DATA_DATE + "=\""+dateStr+"\" and "+ TrafficData.PKG_NAME+"=\""+pkgName+"\"";
		Cursor cursor = null;
		try {
			cursor = db.query(TRAFFIC_TABLE_NAME, null, selectionStr, null, null, null, null);
			if(cursor !=null && cursor.getCount()>0)
				while(cursor.moveToNext()){
					trafficList.add(new TrafficData(cursor, TrafficData.DATA_TYPE_TRAFFIC));
				}
		} catch (Exception e) {
			// TODO: handle exception
		}finally{
			if(cursor != null){
				cursor.close();
			}		
			db.close();
		}		
		return trafficList;
	}
}