package com.bbpos.wiseapp.websocket.handler;

import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.text.TextUtils;
import android.util.Log;

import com.bbpos.wiseapp.logger.BBLog;
import com.bbpos.wiseapp.param.manager.ParamModel;
import com.bbpos.wiseapp.service.common.RequestPermission;
import com.bbpos.wiseapp.service.contentprovider.db.ParamDbOperation;
import com.bbpos.wiseapp.tms.utils.ContextUtil;
import com.bbpos.wiseapp.tms.utils.Helpers;
import com.bbpos.wiseapp.tms.utils.ParameterName;
import com.bbpos.wiseapp.websocket.WebSocketManager;
import com.bbpos.wiseapp.websocket.WebSocketSender;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

public class InAppVarHandler {
    private WebSocketManager webSocketManager;
    private Context mContext;

    public InAppVarHandler(Context context, WebSocketManager manager) {
        mContext = context;
        webSocketManager = manager;
    }

    public void handleMsg(String response) {
        String tranCode = "";
        JSONObject jsonObject = null;
        try {
            //判断是否有任务列表
            JSONArray inAppVal = null;
            JSONObject responseJson = new JSONObject(response);
            JSONObject responseData = Helpers.getDataFromResponse(responseJson);
            if (responseData == null) {
                return;
            }
            String packName = responseData.optString(ParameterName.packName);
            if (TextUtils.isEmpty(packName)) {
                return;
            }
            String apkName = responseData.optString(ParameterName.apkName);
            String versionCode = responseData.optString(ParameterName.versionCode);
            String versionName = responseData.optString(ParameterName.versionName);
            if (responseData.has(ParameterName.inAppVal)) {
                List<ParamModel> params = new ArrayList<ParamModel>();
                inAppVal = responseData.getJSONArray(ParameterName.inAppVal);
                for (int i=0; i<inAppVal.length(); i++) {
                    JSONObject inAppValJson = inAppVal.getJSONObject(i);
                    String idx = inAppValJson.optString(ParameterName.idx);
                    String inKey = inAppValJson.optString(ParameterName.inKey);
                    String inValue = inAppValJson.optString(ParameterName.inValue);
                    String dataSrc = inAppValJson.optString(ParameterName.dataSrc);
                    ParamModel paramModel = new ParamModel(idx, packName, inKey, inValue, dataSrc);
                    params.add(paramModel);
                }
                if (ParamDbOperation.updateParams(ContextUtil.getInstance(), packName, params)) {
                    sendBroadcastToNotify(packName);
                    WebSocketSender.C0901_AppInfoUpload();
                }
            }
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    //broadcast message to notify another application
    public static void sendBroadcastToNotify(String packName) {
        if (isAppInstalled(ContextUtil.getInstance(), packName)) {
            Intent intent = new Intent("com.bbpos.wiseapp.service.IN_APP_DATA_CHANGED");
            intent.setPackage(packName);
            ContextUtil.getInstance().sendBroadcast(intent, RequestPermission.REQUEST_PERMISSION_MY_BROADCAST);
            BBLog.i(BBLog.TAG, "發送廣播通知 " + packName + " 參數變化");
        }
    }

    public static boolean isAppInstalled(Context context, String pkgName){
        PackageManager pm = context.getPackageManager();
        List installedPackages = pm.getInstalledPackages(0);
        Iterator localIterator = installedPackages.iterator();
        while (localIterator.hasNext()) {
            PackageInfo packageInfo = (PackageInfo) localIterator.next();
            if (packageInfo.applicationInfo.packageName.equals(pkgName)) {
                return true;
            }
        }
        return false;
    }
}
