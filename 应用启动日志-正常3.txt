2025-08-20 16:20:28.822   648-948   BufferQueueDebug        surfaceflinger                       E  [ActivityRecord{87cfa37 u0 com.dspread.mdm.service/.ui.activity.TestActivity#128](this:0xa9b76c40,id:-1,api:0,p:-1,c:-1) id info cannot be read from 'ActivityRecord{87cfa37 u0 com.dspread.mdm.service/.ui.activity.TestActivity#128'
2025-08-20 16:20:29.474  3705-3705  Compatibil...geReporter com.dspread.mdm.service              D  Compat change id reported: 183155436; UID 1000; state: ENABLED
2025-08-20 16:20:29.515  3705-3705  MbrainDebugManagerImpl  com.dspread.mdm.service              D  getService failed
2025-08-20 16:20:29.585  3705-3705  Common                  com.dspread.mdm.service              D  🔧 日志配置：生产环境模式
2025-08-20 16:20:29.597  3705-3705  Common                  com.dspread.mdm.service              D  🔧 [DebugConfig] 调试模式已禁用
2025-08-20 16:20:29.603  3705-3705  Common                  com.dspread.mdm.service              D  🔧 SmartMdmServiceApp: 统一配置管理器初始化完成
2025-08-20 16:20:29.608  3705-3705  Common                  com.dspread.mdm.service              D  🔧 SmartMdmServiceApp: 开始初始化系统目录...
2025-08-20 16:20:29.614  3705-3705  Common                  com.dspread.mdm.service              D  🔧 SmartMdmServiceApp: 配置目录已存在 - /data/pos/config
2025-08-20 16:20:29.623  3705-3705  Common                  com.dspread.mdm.service              D  🔧 SmartMdmServiceApp: Logo目录创建 - 路径: /data/data/com.dspread.mdm.service/files/media/logo, 结果: true
2025-08-20 16:20:29.630  3705-3705  Common                  com.dspread.mdm.service              D  🔧 SmartMdmServiceApp: 开机动画目录创建 - 路径: /data/data/com.dspread.mdm.service/files/media/anim, 结果: true
2025-08-20 16:20:29.635  3705-3705  Common                  com.dspread.mdm.service              D  🔧 SmartMdmServiceApp: 系统目录初始化完成
2025-08-20 16:20:29.683  3705-3705  Receiver                com.dspread.mdm.service              D  🔧 ServiceGuardEventHandler 启动服务守护定时器: 120s
2025-08-20 16:20:29.691  3705-3705  Compatibil...geReporter com.dspread.mdm.service              D  Compat change id reported: 160794467; UID 1000; state: ENABLED
2025-08-20 16:20:29.697  3705-3705  Common                  com.dspread.mdm.service              I  ✅ ⏰ 设置服务守护定时器成功，下次执行: 120秒后 (2分钟)
2025-08-20 16:20:29.703  3705-3705  Common                  com.dspread.mdm.service              D  🔧 SmartMdmServiceApp: 服务守护定时器启动成功
2025-08-20 16:20:29.713  3705-3705  Common                  com.dspread.mdm.service              D  🔧 ServiceStartupManager 收到服务启动请求: APPLICATION_STARTUP, 强制启动: false
2025-08-20 16:20:29.724  3705-3705  Common                  com.dspread.mdm.service              D  🔧 ServiceStartupManager 开始执行服务启动: APPLICATION_STARTUP
2025-08-20 16:20:29.730  3705-3705  Common                  com.dspread.mdm.service              D  🔧 ServiceManager: 准备启动后台服务
2025-08-20 16:20:29.736  3705-3705  ContextImpl             com.dspread.mdm.service              W  Calling a method in the system process without a qualified user: android.app.ContextImpl.startService:1899 android.content.ContextWrapper.startService:825 com.dspread.mdm.service.platform.manager.ServiceManager.startBackgroundService:20 com.dspread.mdm.service.platform.manager.ServiceStartupManager.performServiceStart:121 com.dspread.mdm.service.platform.manager.ServiceStartupManager.startService:66 
2025-08-20 16:20:29.741  3705-3705  Common                  com.dspread.mdm.service              D  🔧 ServiceManager: 后台服务启动成功，ComponentName: ComponentInfo{com.dspread.mdm.service/com.dspread.mdm.service.services.SmartMdmBackgroundService}
2025-08-20 16:20:30.751  3705-3705  Common                  com.dspread.mdm.service              D  🔧 ServiceStartupManager 服务启动验证成功: APPLICATION_STARTUP
2025-08-20 16:20:30.757  3705-3705  Common                  com.dspread.mdm.service              D  🔧 ServiceStartupManager 服务启动成功: APPLICATION_STARTUP
2025-08-20 16:20:30.763  3705-3705  Common                  com.dspread.mdm.service              D  🔧 SmartMdmServiceApp: 主服务启动请求已提交
2025-08-20 16:20:30.769  3705-3705  Common                  com.dspread.mdm.service              D  🔧 SmartMdmServiceApp: Application启动完成，包名: com.dspread.mdm.service
2025-08-20 16:20:30.782  3705-3705  M-ProMotion             com.dspread.mdm.service              I  M-ProMotion is disabled
2025-08-20 16:20:30.851  3705-3705  Compatibil...geReporter com.dspread.mdm.service              D  Compat change id reported: 210923482; UID 1000; state: ENABLED
2025-08-20 16:20:30.886  3705-3705  Compatibil...geReporter com.dspread.mdm.service              D  Compat change id reported: 171228096; UID 1000; state: ENABLED
2025-08-20 16:20:30.944  3705-3705  getAnimDeal             com.dspread.mdm.service              I  mode:3 packageName:com.dspread.mdm.service result: false
2025-08-20 16:20:30.952  3705-3736  PowerHalWrapper         com.dspread.mdm.service              I  PowerHalWrapper.getInstance 
2025-08-20 16:20:30.952  3705-3705  SurfaceFactory          com.dspread.mdm.service              I  [static] sSurfaceFactory = com.mediatek.view.impl.SurfaceFactoryImpl@8d07d32
2025-08-20 16:20:30.958  3705-3705  Compatibil...geReporter com.dspread.mdm.service              D  Compat change id reported: 237531167; UID 1000; state: DISABLED
2025-08-20 16:20:30.963  3705-3705  OpenGLRenderer          com.dspread.mdm.service              W  Unknown dataspace 0
2025-08-20 16:20:30.968  3705-3705  VRI[TestActivity]       com.dspread.mdm.service              D  hardware acceleration = true, forceHwAccelerated = false
2025-08-20 16:20:30.973   648-2054  BufferQueueDebug        surfaceflinger                       E  [3079c04 com.dspread.mdm.service/com.dspread.mdm.service.ui.activity.TestActivity#129](this:0xa9b4ac40,id:-1,api:0,p:-1,c:-1) id info cannot be read from '3079c04 com.dspread.mdm.service/com.dspread.mdm.service.ui.activity.TestActivity#129'
2025-08-20 16:20:30.981  3705-3705  InputTransport          com.dspread.mdm.service              D  Create ARC handle: 0xaa5ba750
2025-08-20 16:20:30.981  3705-3705  InputEventReceiver      com.dspread.mdm.service              D  Input log is disabled in InputEventReceiver.
2025-08-20 16:20:30.981  3705-3705  InputTransport          com.dspread.mdm.service              D  Input log is disabled in InputChannel.
2025-08-20 16:20:30.988  3705-3705  Choreographer           com.dspread.mdm.service              I  Skipped 87 frames!  The application may be doing too much work on its main thread.
2025-08-20 16:20:31.049   648-2054  BufferQueueDebug        surfaceflinger                       E  [com.dspread.mdm.service/com.dspread.mdm.service.ui.activity.TestActivity#130](this:0xa9b48c40,id:-1,api:0,p:-1,c:-1) id info cannot be read from 'com.dspread.mdm.service/com.dspread.mdm.service.ui.activity.TestActivity#130'
2025-08-20 16:20:31.061  3705-3705  BufferQueueConsumer     com.dspread.mdm.service              D  [](id:e7900000000,api:0,p:-1,c:3705) connect: controlledByApp=false
2025-08-20 16:20:31.095  3705-3737  OpenGLRenderer          com.dspread.mdm.service              E  Unable to match the desired swap behavior.
2025-08-20 16:20:31.104  3705-3741  ion                     com.dspread.mdm.service              E  ioctl c0044901 failed with code -1: Invalid argument
2025-08-20 16:20:31.211  3705-3737  BLASTBufferQueue        com.dspread.mdm.service              D  [VRI[TestActivity]#0](f:0,a:1) acquireNextBufferLocked size=480x854 mFrameNumber=1 applyTransaction=true mTimestamp=3669654242754(auto) mPendingTransactions.size=0 graphicBufferId=15912853831685 transform=3
2025-08-20 16:20:31.216  3705-3724  OpenGLRenderer          com.dspread.mdm.service              I  Davey! duration=1673ms; Flags=1, FrameTimelineVsyncId=23370, IntendedVsync=3667979793831, Vsync=3669429793860, InputEventId=0, HandleInputStart=3669433773216, AnimationStart=3669433797831, PerformTraversalsStart=3669434379600, DrawStart=3669539922754, FrameDeadline=3667995793831, FrameInterval=3669432411908, FrameStartTime=16666667, SyncQueued=3669553536908, SyncStart=3669556064831, IssueDrawCommandsStart=3669556502985, SwapBuffers=3669652953447, FrameCompleted=3669655804831, DequeueBufferDuration=0, QueueBufferDuration=1140231, GpuCompleted=3669655297831, SwapBuffersCompleted=3669655804831, DisplayPresentTime=0, CommandSubmissionCompleted=3669652953447, 
2025-08-20 16:20:31.218  3705-3737  GED                     com.dspread.mdm.service              I  ged_boost_gpu_freq, level 100, eOrigin 2, final_idx 2, oppidx_max 2, oppidx_min 0
2025-08-20 16:20:31.228  3705-3705  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 服务启动
2025-08-20 16:20:31.258  3705-3705  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 开始服务初始化
2025-08-20 16:20:31.264  3705-3705  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] Android 14+: 初始化DSPREAD服务
2025-08-20 16:20:31.271  3705-3705  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 服务创建完成
2025-08-20 16:20:31.271  3705-3747  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 后台线程：初始化DSPREAD服务
2025-08-20 16:20:31.278  3705-3705  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] onStartCommand调用
2025-08-20 16:20:31.280  3705-3747  Platform                com.dspread.mdm.service              D  🔧 DspreadService 开始初始化DSPREAD服务
2025-08-20 16:20:31.286  3705-3747  ContextImpl             com.dspread.mdm.service              W  Calling a method in the system process without a qualified user: android.app.ContextImpl.bindService:1988 android.content.ContextWrapper.bindService:861 com.dspread.mdm.service.services.DspreadService.bindSysService:111 com.dspread.mdm.service.services.DspreadService.initialize:63 com.dspread.mdm.service.services.SmartMdmBackgroundService.initializeDspreadServiceAsync$lambda$1:227 
2025-08-20 16:20:31.290  3705-3705  Platform                com.dspread.mdm.service              D  🔧 WakeLockManager 获取智能WakeLock成功，持续时间: 60000ms
2025-08-20 16:20:31.291  3705-3747  Platform                com.dspread.mdm.service              D  🔧 DspreadService 系统服务绑定请求已发送
2025-08-20 16:20:31.300  3705-3705  Platform                com.dspread.mdm.service              D  🔧 WakeLockManager 设置WakeLock续期定时器: 60000ms
2025-08-20 16:20:31.333  3705-3737  GED                     com.dspread.mdm.service              I  ged_boost_gpu_freq, level 100, eOrigin 2, final_idx 2, oppidx_max 2, oppidx_min 0
2025-08-20 16:20:31.339  3705-3705  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 初始化基础组件
2025-08-20 16:20:31.347  3705-3705  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 开始初始化...
2025-08-20 16:20:31.361  3705-3705  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 包更新广播接收器注册完成
2025-08-20 16:20:31.367  3705-3705  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 统一广播接收器注册完成
2025-08-20 16:20:31.373  3705-3705  Common                  com.dspread.mdm.service              I  ✅ BroadcastManager 初始化完成
2025-08-20 16:20:31.426  3705-3748  Platform                com.dspread.mdm.service              D  🔧 DeviceInfoApi 单例实例已创建
2025-08-20 16:20:31.433  3705-3705  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 服务初始化完成
2025-08-20 16:20:31.440  3705-3705  Platform                com.dspread.mdm.service              D  🔧 DspreadService 系统服务连接成功: ComponentInfo{com.dspread.sdkservice/com.dspread.sdkservice.SDKSysService}
2025-08-20 16:20:31.447  3705-3705  Platform                com.dspread.mdm.service              D  🔧 DspreadService 系统服务绑定完成
2025-08-20 16:20:31.455  3705-3747  ContextImpl             com.dspread.mdm.service              W  Calling a method in the system process without a qualified user: android.app.ContextImpl.bindService:1988 android.content.ContextWrapper.bindService:861 com.dspread.mdm.service.services.DspreadService.bindDevService:166 com.dspread.mdm.service.services.DspreadService.initialize:66 com.dspread.mdm.service.services.SmartMdmBackgroundService.initializeDspreadServiceAsync$lambda$1:227 
2025-08-20 16:20:31.457  3705-3737  BLASTBufferQueue        com.dspread.mdm.service              D  [VRI[TestActivity]#0](f:0,a:1) destructor()
2025-08-20 16:20:31.458  3705-3737  BufferQueueConsumer     com.dspread.mdm.service              D  [VRI[TestActivity]#0(BLAST Consumer)0](id:e7900000000,api:0,p:-1,c:3705) disconnect
2025-08-20 16:20:31.458  3705-3737  GED                     com.dspread.mdm.service              I  ged_boost_gpu_freq, level 100, eOrigin 2, final_idx 2, oppidx_max 2, oppidx_min 0
2025-08-20 16:20:31.459  3705-3747  Platform                com.dspread.mdm.service              D  🔧 DspreadService 设备服务绑定请求已发送
2025-08-20 16:20:31.471  3705-3705  Receiver                com.dspread.mdm.service              D  🔧 电池状态变化: 电量=82%, 温度=28°C, 充电=true
2025-08-20 16:20:31.504  3705-3705  Provisioning            com.dspread.mdm.service              D  🔧 主目录 已存在: /data/pos/config
2025-08-20 16:20:31.510  3705-3705  Provisioning            com.dspread.mdm.service              I  ℹ️ 使用主配置目录: /data/pos/config/
2025-08-20 16:20:31.516  3705-3705  Provisioning            com.dspread.mdm.service              I  ℹ️ 配置路径初始化完成: 系统目录
2025-08-20 16:20:31.532  3705-3705  Provisioning            com.dspread.mdm.service              D  🔧 状态标志加载成功: /data/pos/config/provisioning_flags.json
2025-08-20 16:20:31.553  3705-3705  WebSocket               com.dspread.mdm.service              I  🔧 C0902 被动式上送: battery_level_change_5 (被动: 1)
2025-08-20 16:20:31.570  3705-3705  Platform                com.dspread.mdm.service              E  ❌ DspreadService 设备服务不可用，无法获取序列号
2025-08-20 16:20:31.594  3705-3705  WebSocket               com.dspread.mdm.service              I  🔧 消息已缓存: C0902 (缓存数量: 1)
2025-08-20 16:20:31.601  3705-3705  WebSocket               com.dspread.mdm.service              I  🔧 C0902 电池状态上送成功 (1, trigger=battery_level_change_5)
2025-08-20 16:20:31.609  3705-3705  Platform                com.dspread.mdm.service              D  🔧 NetworkApi 单例实例已创建
2025-08-20 16:20:31.621  3705-3705  Receiver                com.dspread.mdm.service              D  🔧 网络状态变化: 连接=true, 类型=WIFI
2025-08-20 16:20:31.629  3705-3705  Receiver                com.dspread.mdm.service              D  🔧 网络恢复，尝试重新连接WebSocket
2025-08-20 16:20:31.635  3705-3705  Receiver                com.dspread.mdm.service              D  🔧 ServiceManagementEventHandler 处理网络变化事件
2025-08-20 16:20:31.645  3705-3705  Common                  com.dspread.mdm.service              D  🔧 ServiceStartupManager 服务正在运行，无需启动: NETWORK_CHANGE
2025-08-20 16:20:31.651  3705-3705  Receiver                com.dspread.mdm.service              D  🔧 ServiceManagementEventHandler 网络变化后服务检查完成
2025-08-20 16:20:31.658  3705-3705  Provisioning            com.dspread.mdm.service              D  🔧 ProvisioningEventHandler 网络状态变化
2025-08-20 16:20:31.663  3705-3705  Provisioning            com.dspread.mdm.service              D  🔧 ProvisioningEventHandler 忽略初始启动时的网络连接事件
2025-08-20 16:20:31.671  3705-3705  Platform                com.dspread.mdm.service              D  🔧 DspreadService 设备服务连接成功: ComponentInfo{com.dspread.sdkservice/com.dspread.sdkservice.SDKDevService}
2025-08-20 16:20:31.679  3705-3705  Platform                com.dspread.mdm.service              D  🔧 DspreadService 设备服务绑定完成
2025-08-20 16:20:31.811  3705-3705  WebSocket               com.dspread.mdm.service              I  🔧 C0905 主动式上送: manual_trigger (主动: 1)
2025-08-20 16:20:31.835  3705-3705  WebSocket               com.dspread.mdm.service              I  🔧 首次上送，上送当日数据
2025-08-20 16:20:31.842  3705-3705  WebSocket               com.dspread.mdm.service              I  🔧 没有流量数据需要上送
2025-08-20 16:20:31.847  3705-3705  Common                  com.dspread.mdm.service              D  🔧 刷新时自动上送流量统计
2025-08-20 16:20:31.860  3705-3737  GED                     com.dspread.mdm.service              I  ged_boost_gpu_freq, level 100, eOrigin 2, final_idx 2, oppidx_max 2, oppidx_min 0
2025-08-20 16:20:31.878  3705-3748  Platform                com.dspread.mdm.service              D  🔧 SpVersionApi 解析SP版本: V1.0.5
2025-08-20 16:20:31.894  3705-3748  Platform                com.dspread.mdm.service              D  🔧 DspreadService 首次获取SP版本成功: V1.0.5 (原始: *SP_VERSION:V1.0.5#*COMMIT:2d7cc90#*DATETIME:20250...)
2025-08-20 16:20:31.960  3705-3705  Common                  com.dspread.mdm.service              D  🔧 TestActivity UI已更新: SP=V1.0.5, SN=01354090202503050399
2025-08-20 16:20:32.685  3705-3747  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 等待DSPREAD服务就绪 (1/10): true
2025-08-20 16:20:32.691  3705-3747  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] DSPREAD服务就绪，启动Provisioning
2025-08-20 16:20:32.696  3705-3747  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 验证DSPREAD服务功能
2025-08-20 16:20:32.701  3705-3747  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 设备序列号验证: 成功(01354090202503050399)
2025-08-20 16:20:32.707  3705-3747  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 服务状态 - 系统服务:✅ 设备服务:✅
2025-08-20 16:20:32.713  3705-3756  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 启动ProvisioningService
2025-08-20 16:20:32.721  3705-3756  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] ProvisioningService启动完成
2025-08-20 16:20:32.721  3705-3748  Provisioning            com.dspread.mdm.service              I  ℹ️ 开始执行Provisioning配置，触发类型: FIRST_BOOT
2025-08-20 16:20:32.728  3705-3748  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] Provisioning进度: 正在下载配置文件...
2025-08-20 16:20:32.733  3705-3748  Provisioning            com.dspread.mdm.service              I  ℹ️ 开始请求远程配置...
2025-08-20 16:20:32.740  3705-3748  Provisioning            com.dspread.mdm.service              I  ℹ️ 请求配置: https://config.dspreadserv.net/status/config?SN=MDEzNTQwOTAyMDI1MDMwNTAzOTk=&timestamp=1755678032739
2025-08-20 16:20:32.982  3705-3748  TrafficStats            com.dspread.mdm.service              D  tagSocket(109) with statsTag=0xffffffff, statsUid=-1
2025-08-20 16:20:35.344  3705-3748  Provisioning            com.dspread.mdm.service              I  ℹ️ 远程配置获取成功
2025-08-20 16:20:35.350  3705-3748  Provisioning            com.dspread.mdm.service              I  ℹ️ 远程配置请求成功
2025-08-20 16:20:35.356  3705-3748  Provisioning            com.dspread.mdm.service              I  ℹ️ 远程配置解析成功 - CID: 1001
2025-08-20 16:20:35.386  3705-3748  Provisioning            com.dspread.mdm.service              I  ℹ️ API响应已保存到本地: /data/pos/config/provisioning_config.json, 具体内容为:
                                                                                                    {"mode":"dev","data":{"requestTime":"1755678034908","customization":{"system":{"timezone":"Asia\/Hong_Kong","logo":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/test\/logo.bin","logoMd5":"ba1ee533924eae5c408465e7cddcbda4","bootAnimationMd5":"ba1ee533924eae5c408465e7cddcbda4","gps":{"minUpdateTime":"30","scheduleTime":"60","maxLocateTime":"0","minDistance":"10","valid_distance":"500","care":"1"},"powerSaveMode":{"enable":"1","screenTimeout":"0"},"bootAnimation":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/test\/bootanimation.zip"},"polling":{"heartbeatTime":"300","terminalInfoTime":"900","statusApiUrl":"wss:\/\/api.dspreadserv.net\/status\/websocket\/register","remoteUrl":"wss:\/\/remote.dspreadserv.net\/remoteWSS\/websockify","uploadMode":"1","wssreconn":{"pingInterval":"30","delayPolicy":"1","delaySwitch":"1","delayTime":"60"}}},"client":"default","cid":"1001"},"function":"getProvisioningConfig","description":"SUCCESS","stateCode":"0","version":"v1.0.1.20250801"}
2025-08-20 16:20:35.394  3705-3748  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] Provisioning进度: 正在下载 Logo...
2025-08-20 16:20:35.403  3705-3748  Provisioning            com.dspread.mdm.service              I  ℹ️ 文件不存在，开始下载: Logo
2025-08-20 16:20:35.409  3705-3748  Provisioning            com.dspread.mdm.service              I  ℹ️ 下载: https://siot-bucket01.s3.ap-northeast-1.amazonaws.com/test/logo.bin -> /data/data/com.dspread.mdm.service/files/media/logo/logo.bin
2025-08-20 16:20:35.421  3705-3748  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 尝试下载，第1次
2025-08-20 16:20:35.427  3705-3748  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 开始下载
2025-08-20 16:20:35.432  3705-3748  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader URL: https://siot-bucket01.s3.ap-northeast-1.amazonaws.com/test/logo.bin
2025-08-20 16:20:35.438  3705-3748  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 路径: /data/data/com.dspread.mdm.service/files/media/logo/logo.bin
2025-08-20 16:20:35.444  3705-3748  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 本地文件不存在，创建文件
2025-08-20 16:20:35.450  3705-3748  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader allFileLength=0, 需要从服务器获取文件大小
2025-08-20 16:20:35.645  3705-3748  TrafficStats            com.dspread.mdm.service              D  tagSocket(112) with statsTag=0xffffffff, statsUid=-1
2025-08-20 16:20:36.224  3705-3765  ProfileInstaller        com.dspread.mdm.service              D  Installing profile for com.dspread.mdm.service
2025-08-20 16:20:36.773  3705-3748  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader HTTP响应码: 200
2025-08-20 16:20:36.780  3705-3748  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 服务器返回Content-Length: 4771451
2025-08-20 16:20:36.785  3705-3748  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 完整下载模式
2025-08-20 16:20:36.790  3705-3748  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 完整下载，使用服务器返回大小: 4771451
2025-08-20 16:20:36.796  3705-3748  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader localFile = true /data/data/com.dspread.mdm.service/files/media/logo/logo.bin, 追加模式: false
2025-08-20 16:20:43.036  3705-3748  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 计算MD5: logo.bin
2025-08-20 16:20:43.132  3705-3748  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 文件: 4771451 MD5: ba1ee533924eae5c408465e7cddcbda4
2025-08-20 16:20:43.137  3705-3748  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader MD5校验正确
2025-08-20 16:20:43.142  3705-3748  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 清理连接资源
2025-08-20 16:20:43.148  3705-3748  Provisioning            com.dspread.mdm.service              I  ℹ️ Logo 下载完成: /data/data/com.dspread.mdm.service/files/media/logo/logo.bin
2025-08-20 16:20:43.154  3705-3748  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] Provisioning进度: 正在下载 BootAnimation...
2025-08-20 16:20:43.161  3705-3748  Provisioning            com.dspread.mdm.service              I  ℹ️ 文件不存在，开始下载: BootAnimation
2025-08-20 16:20:43.167  3705-3748  Provisioning            com.dspread.mdm.service              I  ℹ️ 下载: https://siot-bucket01.s3.ap-northeast-1.amazonaws.com/test/bootanimation.zip -> /data/data/com.dspread.mdm.service/files/media/anim/bootanimation.zip
2025-08-20 16:20:43.173  3705-3748  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 尝试下载，第1次
2025-08-20 16:20:43.178  3705-3748  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 开始下载
2025-08-20 16:20:43.184  3705-3748  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader URL: https://siot-bucket01.s3.ap-northeast-1.amazonaws.com/test/bootanimation.zip
2025-08-20 16:20:43.189  3705-3748  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 路径: /data/data/com.dspread.mdm.service/files/media/anim/bootanimation.zip
2025-08-20 16:20:43.195  3705-3748  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 本地文件不存在，创建文件
2025-08-20 16:20:43.202  3705-3748  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader allFileLength=0, 需要从服务器获取文件大小
2025-08-20 16:20:43.431  3705-3748  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader HTTP响应码: 200
2025-08-20 16:20:43.438  3705-3748  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 服务器返回Content-Length: 4771451
2025-08-20 16:20:43.443  3705-3748  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 完整下载模式
2025-08-20 16:20:43.449  3705-3748  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 完整下载，使用服务器返回大小: 4771451
2025-08-20 16:20:43.455  3705-3748  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader localFile = true /data/data/com.dspread.mdm.service/files/media/anim/bootanimation.zip, 追加模式: false
2025-08-20 16:20:48.933  3705-3748  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 计算MD5: bootanimation.zip
2025-08-20 16:20:49.001  3705-3748  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 文件: 4771451 MD5: ba1ee533924eae5c408465e7cddcbda4
2025-08-20 16:20:49.006  3705-3748  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader MD5校验正确
2025-08-20 16:20:49.012  3705-3748  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 清理连接资源
2025-08-20 16:20:49.017  3705-3748  Provisioning            com.dspread.mdm.service              I  ℹ️ BootAnimation 下载完成: /data/data/com.dspread.mdm.service/files/media/anim/bootanimation.zip
2025-08-20 16:20:49.023  3705-3748  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] Provisioning进度: 正在应用配置...
2025-08-20 16:20:49.029  3705-3748  Provisioning            com.dspread.mdm.service              I  ℹ️ 配置应用完成
2025-08-20 16:20:49.034  3705-3748  Provisioning            com.dspread.mdm.service              I  ℹ️ 远程配置成功，标记首次配置完成
2025-08-20 16:20:49.042  3705-3748  Provisioning            com.dspread.mdm.service              D  🔧 状态标志保存成功: /data/pos/config/provisioning_flags.json
2025-08-20 16:20:49.048  3705-3748  Provisioning            com.dspread.mdm.service              I  ℹ️ Provisioning配置执行完成
2025-08-20 16:20:49.053  3705-3748  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] Provisioning完成，启动核心组件
2025-08-20 16:20:49.072  3705-3705  WebSocket               com.dspread.mdm.service              I  🔧 开始初始化 WebSocket 组件...
2025-08-20 16:20:49.078  3705-3705  WebSocket               com.dspread.mdm.service              I  🔧 初始化 WebSocket 密钥管理器
2025-08-20 16:20:49.085  3705-3705  WebSocket               com.dspread.mdm.service              I  🔧 生成客户端公私钥对
2025-08-20 16:20:49.234  3705-3705  Common                  com.dspread.mdm.service              I  ✅ WebSocket 密钥管理器初始化完成
2025-08-20 16:20:49.243  3705-3705  WebSocket               com.dspread.mdm.service              I  🔧 开始初始化 WsMessageCenter...
2025-08-20 16:20:49.249  3705-3705  WebSocket               com.dspread.mdm.service              I  🔧 WsMessageCenter 首次初始化
2025-08-20 16:20:49.269  3705-3705  WebSocket               com.dspread.mdm.service              I  🔧 WsTaskManager 初始化完成
2025-08-20 16:20:49.375  3705-3705  WebSocket               com.dspread.mdm.service              I  🔧 WsMessageCenter 初始化完成
2025-08-20 16:20:49.381  3705-3705  WebSocket               com.dspread.mdm.service              I  🔧 开始清理终态任务，当前任务数量: 0
2025-08-20 16:20:49.387  3705-3705  WebSocket               com.dspread.mdm.service              I  🔧 没有需要清理的终态任务
2025-08-20 16:20:49.392  3705-3705  WebSocket               com.dspread.mdm.service              I  🔧 开始重置任务状态，当前任务个数: 0
2025-08-20 16:20:49.398  3705-3705  WebSocket               com.dspread.mdm.service              I  🔧 没有需要重置的任务
2025-08-20 16:20:49.403  3705-3705  Common                  com.dspread.mdm.service              I  ✅ 任务状态恢复完成
2025-08-20 16:20:49.420  3705-3705  WebSocket               com.dspread.mdm.service              I  🔧 使用Provisioning配置的WebSocket URL: wss://api.dspreadserv.net/status/websocket/register
2025-08-20 16:20:49.426  3705-3705  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接管理器初始化完成
2025-08-20 16:20:49.431  3705-3705  Common                  com.dspread.mdm.service              I  ✅ WebSocket 管理器初始化完成
2025-08-20 16:20:49.437  3705-3705  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 管理器初始化完成
2025-08-20 16:20:49.443  3705-3705  Common                  com.dspread.mdm.service              I  ✅ WebSocket 组件初始化完成
2025-08-20 16:20:49.449  3705-3705  WebSocket               com.dspread.mdm.service              I  🔧 启动 WebSocket 连接...
2025-08-20 16:20:49.454  3705-3705  WebSocket               com.dspread.mdm.service              I  🔧 开始连接 WebSocket...
2025-08-20 16:20:49.475  3705-3705  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接到: wss://api.dspreadserv.net/status/websocket/register?serialNo=MDEzNTQwOTAyMDI1MDMwNTAzOTk=&publicKey=TUlHZk1BMEdDU3FHU0liM0RRRUJBUVVBQTRHTkFEQ0JpUUtCZ1FETU8ydXlvRnIvekpxTmlHMng4bWxHd29HcTkrQ29xY0lTNjBXMkp4UHd3Q2h1Rm9RbEhzUVYwL3pWRURJbjRRYlBDTGlKZi9JL1E0RDhIc3BsQXFuSzFKMytzVWxJNkpWQVY1NUpYYnlpSitndlMwWVByN1ZkTEhPMEZqa1VUbnJOR0R6aTJ1djRTMWN3R0lFTmQ0MTFRbTR5Y1QyUXVGUEJMWjU1aUdOREF3SURBUUFC&query=1&msgVer=3&timestamp=1755678049459&signature=AP7sGDdlzlAFyPrTlhUn1V3nQSe6FVX6BGOCAY5GQH2TL93OXZG2OltyYRMQJ0AsgJng8ui23rZJhyxRVkqebKIfeHKI03KbJjHJwXLqx8OEaJlGm775Tia8L6hrd6wyMwQFDva6fqG6LUZXiqKQcSTDkftHZyuBCuQvogqen64=
2025-08-20 16:20:49.483  3705-3705  WebSocket               com.dspread.mdm.service              I  🔧 未检测到系统代理设置
2025-08-20 16:20:49.524  3705-3705  WebSocket               com.dspread.mdm.service              I  🔧 ===== WebSocket 库默认配置 =====
2025-08-20 16:20:49.530  3705-3705  WebSocket               com.dspread.mdm.service              I  🔧 默认 PING 间隔: 30000ms (30秒)
2025-08-20 16:20:49.536  3705-3705  WebSocket               com.dspread.mdm.service              I  🔧 默认 PONG 间隔: 0ms (0秒)
2025-08-20 16:20:49.541  3705-3705  WebSocket               com.dspread.mdm.service              I  🔧 帧队列大小: 5
2025-08-20 16:20:49.547  3705-3705  WebSocket               com.dspread.mdm.service              I  🔧 连接超时: 15000ms (15秒)
2025-08-20 16:20:49.553  3705-3705  WebSocket               com.dspread.mdm.service              I  🔧 允许缺失关闭帧: false
2025-08-20 16:20:49.558  3705-3705  WebSocket               com.dspread.mdm.service              I  🔧 ================================
2025-08-20 16:20:49.565  3705-3705  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接请求已发送
2025-08-20 16:20:49.571  3705-3705  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接启动完成
2025-08-20 16:20:49.578  3705-3705  Common                  com.dspread.mdm.service              D  🔧 [ModuleManagerRegistry] 模块管理器注册中心初始化完成
2025-08-20 16:20:49.585  3705-3705  Provisioning            com.dspread.mdm.service              D  🔧 ProvisioningEventHandler 启动Provisioning定时器: 43200s (12小时)
2025-08-20 16:20:49.585  3705-3748  Common                  com.dspread.mdm.service              D  🔧 [ModuleManagerRegistry] 启动所有模块
2025-08-20 16:20:49.595  3705-3705  Common                  com.dspread.mdm.service              I  ✅ ⏰ 设置初始化定时器成功，下次执行: 43200秒后 (12小时)
2025-08-20 16:20:49.599  3705-3748  Common                  com.dspread.mdm.service              D  🔧 Module LogStream Manager status changed to: INITIALIZED
2025-08-20 16:20:49.608  3705-3748  Common                  com.dspread.mdm.service              D  🔧 Module LogStream Manager status changed to: STARTING
2025-08-20 16:20:49.611  3705-3705  Common                  com.dspread.mdm.service              D  🔧 TimerConfig 使用Provisioning配置的心跳间隔: 300秒
2025-08-20 16:20:49.622  3705-3705  Common                  com.dspread.mdm.service              I  ✅ ⏰ 设置心跳定时器成功，下次执行: 300秒后 (5分钟)
2025-08-20 16:20:49.633  3705-3705  Common                  com.dspread.mdm.service              I  ✅ ⏰ 设置任务执行定时器成功，下次执行: 60秒后 (1分钟)
2025-08-20 16:20:49.649  3705-3705  Common                  com.dspread.mdm.service              D  🔧 TimerConfig 使用Provisioning配置的终端信息上传间隔: 900秒
2025-08-20 16:20:49.659  3705-3705  Common                  com.dspread.mdm.service              I  ✅ ⏰ 设置终端信息定时器成功，下次执行: 900秒后 (15分钟)
2025-08-20 16:20:49.666  3705-3705  Common                  com.dspread.mdm.service              D  🔧 TimerConfig ========== 当前定时器配置 (生产模式) ==========
2025-08-20 16:20:49.682  3705-3705  Common                  com.dspread.mdm.service              D  🔧 TimerConfig 使用Provisioning配置的心跳间隔: 300秒
2025-08-20 16:20:49.688  3705-3705  Common                  com.dspread.mdm.service              D  🔧 TimerConfig ①心跳定时器: 300秒
2025-08-20 16:20:49.703  3705-3748  Common                  com.dspread.mdm.service              D  🔧 Module LogStream Manager status changed to: RUNNING
2025-08-20 16:20:49.704  3705-3705  Common                  com.dspread.mdm.service              D  🔧 TimerConfig 使用Provisioning配置的终端信息上传间隔: 900秒
2025-08-20 16:20:49.710  3705-3748  Common                  com.dspread.mdm.service              D  🔧 [ModuleManagerRegistry] LogStreamManager启动成功
2025-08-20 16:20:49.710  3705-3705  Common                  com.dspread.mdm.service              D  🔧 TimerConfig ②终端信息上传: 900秒
2025-08-20 16:20:49.716  3705-3705  Common                  com.dspread.mdm.service              D  🔧 TimerConfig ③任务执行: 60秒
2025-08-20 16:20:49.723  3705-3705  Common                  com.dspread.mdm.service              D  🔧 TimerConfig ④服务守护: 120秒
2025-08-20 16:20:49.729  3705-3705  Common                  com.dspread.mdm.service              D  🔧 TimerConfig ⑤Provisioning: 43200秒
2025-08-20 16:20:49.735  3705-3705  Common                  com.dspread.mdm.service              D  🔧 TimerConfig ===============================================
2025-08-20 16:20:49.737  3705-3748  Compatibil...geReporter com.dspread.mdm.service              D  Compat change id reported: 263076149; UID 1000; state: ENABLED
2025-08-20 16:20:49.741  3705-3705  Common                  com.dspread.mdm.service              I  ✅ [SmartMdmBackgroundService] 所有定时器启动完成
2025-08-20 16:20:49.747  3705-3705  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 核心组件初始化完成
2025-08-20 16:20:49.750  3705-3748  Common                  com.dspread.mdm.service              D  🔧 Module Geofence Manager status changed to: INITIALIZED
2025-08-20 16:20:49.757  3705-3748  Common                  com.dspread.mdm.service              D  🔧 Module Geofence Manager status changed to: STARTING
2025-08-20 16:20:49.785  3705-3748  Platform                com.dspread.mdm.service              D  🔧 用户交互监控已启动（简化版本）
2025-08-20 16:20:49.785  3705-3705  Platform                com.dspread.mdm.service              D  🔧 [WiFiManagerApi] WiFiManagerApi 单例实例已创建
2025-08-20 16:20:49.791  3705-3748  Platform                com.dspread.mdm.service              D  🔧 使用模拟用户交互检测
2025-08-20 16:20:49.797  3705-3748  Platform                com.dspread.mdm.service              D  🔧 用户交互监控已启动
2025-08-20 16:20:49.803  3705-3748  Service                 com.dspread.mdm.service              D  🔧 [GeofenceManager]Constants.geofenceStatus = 1
2025-08-20 16:20:49.810  3705-3748  Service                 com.dspread.mdm.service              D  🔧 [GeofenceManager]-- Constants.storeId =  -- Constants.storeSsid =  -- Constants.storeIp = 
2025-08-20 16:20:49.817  3705-3748  Common                  com.dspread.mdm.service              D  🔧 Module Geofence Manager status changed to: RUNNING
2025-08-20 16:20:49.823  3705-3748  Common                  com.dspread.mdm.service              D  🔧 [ModuleManagerRegistry] GeofenceManager启动成功
2025-08-20 16:20:49.841  3705-3748  RuleBase                com.dspread.mdm.service              D  🔧 RuleBaseManager 开始初始化RuleBaseManager...
2025-08-20 16:20:49.852  3705-3748  RuleBase                com.dspread.mdm.service              D  🔧 RuleBaseStorage RuleBase存储管理器初始化完成
2025-08-20 16:20:49.857  3705-3748  RuleBase                com.dspread.mdm.service              D  🔧 RuleStateMonitor 状态监控器初始化完成
2025-08-20 16:20:49.863  3705-3748  RuleBase                com.dspread.mdm.service              D  🔧 RuleBaseManager RuleBaseManager初始化完成
2025-08-20 16:20:49.869  3705-3748  Common                  com.dspread.mdm.service              D  🔧 [ModuleManagerRegistry] RuleBaseManager启动成功
2025-08-20 16:20:49.875  3705-3748  Common                  com.dspread.mdm.service              D  🔧 [ModuleManagerRegistry] 所有模块启动完成
2025-08-20 16:20:49.880  3705-3783  TrafficStats            com.dspread.mdm.service              D  tagSocket(119) with statsTag=0xffffffff, statsUid=-1
2025-08-20 16:20:50.131  3705-3784  TrafficStats            com.dspread.mdm.service              D  tagSocket(124) with statsTag=0xffffffff, statsUid=-1
2025-08-20 16:20:52.034  3705-3785  Common                  com.dspread.mdm.service              I  ✅ WebSocket 物理连接成功，等待服务器 hellMsg
2025-08-20 16:20:52.044  3705-3785  WebSocket               com.dspread.mdm.service              I  🔧 ===== 心跳发送器实际配置 =====
2025-08-20 16:20:52.052  3705-3785  WebSocket               com.dspread.mdm.service              I  🔧 实际 PING 发送间隔: 30000ms (30秒)
2025-08-20 16:20:52.058  3705-3785  WebSocket               com.dspread.mdm.service              I  🔧 PING 定时器名称: PingSender
2025-08-20 16:20:52.064  3705-3785  WebSocket               com.dspread.mdm.service              I  🔧 实际 PONG 发送间隔: 0ms (0秒)
2025-08-20 16:20:52.070  3705-3785  WebSocket               com.dspread.mdm.service              I  🔧 PONG 定时器名称: PongSender
2025-08-20 16:20:52.076  3705-3785  WebSocket               com.dspread.mdm.service              I  🔧 ===============================
2025-08-20 16:20:52.082  3705-3785  WebSocket               com.dspread.mdm.service              I  🔧 启动PING/PONG检测，阈值: 连续3个PING无PONG响应
2025-08-20 16:20:52.122  3705-3785  Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"hellMsg":"hello 01354090202503050399","tranCode":"S0000","version":"1","rebootTime":"03:26:59","serialNo":"01354090202503050399","deviceStatus":"6"}
2025-08-20 16:20:52.129  3705-3785  Common                  com.dspread.mdm.service              I  ✅ 收到服务器 hellMsg，WebSocket 业务连接成功！
2025-08-20 16:20:52.134  3705-3785  Common                  com.dspread.mdm.service              I  ✅ WebSocket 业务连接成功！
2025-08-20 16:20:52.140  3705-3785  WebSocket               com.dspread.mdm.service              I  🔧 业务连接成功，重连计数已重置为 0
2025-08-20 16:20:52.146  3705-3785  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接成功，检查自身更新任务状态
2025-08-20 16:20:52.155  3705-3785  Receiver                com.dspread.mdm.service              D  🔧 发送广播: com.dspread.mdm.service.CHECK_SELF_UPDATE_STATUS
2025-08-20 16:20:52.161  3705-3785  WebSocket               com.dspread.mdm.service              I  🔧 开始发送缓存消息，数量: 1
2025-08-20 16:20:52.170  3705-3785  WebSocket               com.dspread.mdm.service              I  🔧 C0902 主动式上送: cached_message_resend (主动: 2)
2025-08-20 16:20:52.206  3705-3785  Common                  com.dspread.mdm.service              I  ✅ 缓存消息发送成功: C0902
2025-08-20 16:20:52.713  3705-3785  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接成功
2025-08-20 16:20:52.719  3705-3785  WebSocket               com.dspread.mdm.service              I  🔧 设备状态: 6
2025-08-20 16:20:52.725  3705-3785  WebSocket               com.dspread.mdm.service              I  🔧 检查首次连接标志: isFirstTimeWebSocketConnected=true
2025-08-20 16:20:52.731  3705-3785  WebSocket               com.dspread.mdm.service              I  🔧 首次WebSocket连接成功，发送初始化信息并执行初始化检查
2025-08-20 16:20:52.737  3705-3785  WebSocket               com.dspread.mdm.service              I  🔧 开始发送初始化信息（带流量控制）
2025-08-20 16:20:52.743  3705-3785  WebSocket               com.dspread.mdm.service              I  🔧 C0901 被动式上送: first_connection (被动: 2)
2025-08-20 16:20:52.758  3705-3785  Platform                com.dspread.mdm.service              D  🔧 重新收集应用信息
2025-08-20 16:20:52.860  3705-3785  AppManager              com.dspread.mdm.service              I  ℹ️ 应用信息获取完成: 总数131(系统129/用户2) 返回2个
2025-08-20 16:20:52.879  3705-3785  Platform                com.dspread.mdm.service              D  🔧 应用信息: 2 个应用
2025-08-20 16:20:52.910  3705-3785  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0901","serialNo":"01354090202503050399","request_time":"1755678052885","request_id":"1755678052885C0901","version":"1","data":{"apkInfo":[{"packName":"com.dspread.mdm.service","apkName":"service","versionCode":22,"versionName":"1.1.03.20250820.DSPREAD.MDM.SERVICE","updateDate":"2025-08-20 16:20:27"},{"packName":"mark.via","apkName":"Via","versionCode":20250117,"versionName":"6.2.0","updateDate":"2025-08-18 17:50:20"}],"sytemInfo":{"androidVersion":"14","buildNumber":"BASE_D30M-MU_GEN_AP_V1.0.8.1_202507032000_ota_test","aspl":"2025-03-05"},"serviceInfo":[]},"myVersionName":"1.1.03.20250820.DSPREAD.MDM.SERVICE","terminalDate":"20250820162052"}
2025-08-20 16:20:52.916  3705-3785  WebSocket               com.dspread.mdm.service              I  🔧 C0901 应用信息上送成功 (1)
2025-08-20 16:20:53.924  3705-3785  WebSocket               com.dspread.mdm.service              I  🔧 C0902 被动式上送: first_connection (被动: 3)
2025-08-20 16:20:53.959  3705-3785  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0902","serialNo":"01354090202503050399","request_time":"1755678053942","request_id":"1755678053942C0902","version":"1","data":{"batteryLife":82,"batteryHealth":2,"temprature":"28.0","isCharging":"1","isLowBattery":"0","powerOff":"0","outOfBattery":"0"},"myVersionName":"1.1.03.20250820.DSPREAD.MDM.SERVICE","terminalDate":"20250820162053"}
2025-08-20 16:20:53.965  3705-3785  WebSocket               com.dspread.mdm.service              I  🔧 C0902 电池状态上送成功 (1, trigger=first_connection)
2025-08-20 16:20:54.972  3705-3785  WebSocket               com.dspread.mdm.service              I  🔧 C0903 被动式上送: first_connection (被动: 4)
2025-08-20 16:20:55.082  3705-3716  System                  com.dspread.mdm.service              W  A resource failed to call close. 
2025-08-20 16:20:55.143  3705-3785  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0903","serialNo":"01354090202503050399","request_time":"1755678055106","request_id":"1755678055106C0903","version":"1","data":{"hardwareInfo":[{"name":"CPU Usage","desc":"cpu:0.50"},{"name":"storage_usage_status","desc":" 4.56GB \/ 32.00GB ","state":"1"},{"name":"total_memory","desc":" 0.66GB \/ 1.86GB ","state":"1"},{"name":"androidVersion","desc":"14","state":"1"},{"name":"network_status","desc":"wifi","state":"1"},{"name":"OSVer","desc":"14","state":"1"}],"locationInfo":{"gps":{"longitude":"-999.0","latitude":"-999.0","satelliteCount":0,"satelliteCountInUsed":0,"satelliteAverSNR":"0.00","distance":"0.0","lockStatus":"1","lockMeter":"200","proId":"","proName":""},"baseSite":{"MCC":"","MNC":"","CID":"-1","LAC":"-1","DBM":"-999","IMSI":"","ICCID":""},"wifi":{"SSID":"fubox_2.4G","IP":"**************","MAC":"BE:87:C2:42:DF:75","SSTH":"-34","BSSID":"7c:10:c9:d6:9f:10"},"address":"无法获取"}},"myVersionName":"1.1.03.20250820.DSPREAD.MDM.SERVICE","terminalDate":"20250820162055"}
2025-08-20 16:20:55.149  3705-3785  WebSocket               com.dspread.mdm.service              I  🔧 C0903 数据信息上送成功 (1)
2025-08-20 16:20:56.156  3705-3785  WebSocket               com.dspread.mdm.service              I  🔧 C0904 被动式上送: first_connection (被动: 5)
2025-08-20 16:20:56.260  3705-3785  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0904","serialNo":"01354090202503050399","request_time":"1755678056225","request_id":"1755678056225C0904","version":"1","data":{"wifiOption":[{"SSID":"2306","SSTH":"-72"},{"SSID":"2205","SSTH":"-50"},{"SSID":"2205_5G","SSTH":"-67"},{"SSID":"DIRECT-7D-HP Laser 136w","SSTH":"-77"},{"SSID":"fubox_2.4G","SSTH":"-32"},{"SSID":"2106","SSTH":"-66"},{"SSID":"DIRECT-CC-HP Laser 1188w","SSTH":"-85"},{"SSID":"2206","SSTH":"-30"},{"SSID":"2206-5G","SSTH":"-37"},{"SSID":"DIRECT-C6-Mi All-in-One Inkjet","SSTH":"-50"},{"SSID":"2207-5G","SSTH":"-78"},{"SSID":"2103_5G","SSTH":"-82"},{"SSID":"@Ruijie-1816_5G","SSTH":"-69"},{"SSID":"@Ruijie-1816","SSTH":"-50"},{"SSID":"jibao2","SSTH":"-72"}],"baseSite":{"MCC":"","MNC":"","CID":"-1","LAC":"-1","DBM":"-999","IMSI":"","ICCID":""},"wifi":{"SSID":"fubox_2.4G","IP":"**************","MAC":"BE:87:C2:42:DF:75","SSTH":"-34","BSSID":"7c:10:c9:d6:9f:10"}},"myVersionName":"1.1.03.20250820.DSPREAD.MDM.SERVICE","terminalDate":"20250820162056"}
2025-08-20 16:20:56.266  3705-3785  WebSocket               com.dspread.mdm.service              I  🔧 C0904 网络状态上送成功 (1)
2025-08-20 16:20:57.273  3705-3785  WebSocket               com.dspread.mdm.service              I  🔧 C0906 被动式上送: first_connection (被动: 6)
2025-08-20 16:20:57.288  3705-3785  Platform                com.dspread.mdm.service              D  🔧 DspreadService 使用缓存的SP版本: V1.0.5
2025-08-20 16:20:57.294  3705-3785  Platform                com.dspread.mdm.service              D  🔧 SpVersionApi 通过DspreadService获取SP版本: V1.0.5
2025-08-20 16:20:57.325  3705-3785  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0906","serialNo":"01354090202503050399","request_time":"1755678057307","request_id":"1755678057307C0906","version":"1","data":{"firmWareInfo":{"spfw":"V1.0.5"},"imei_1":"864177060143618","imei_2":"","wifi_mac":"be:87:c2:42:df:75","bt_mac":"","bsn":""},"myVersionName":"1.1.03.20250820.DSPREAD.MDM.SERVICE","terminalDate":"20250820162057"}
2025-08-20 16:20:57.332  3705-3785  WebSocket               com.dspread.mdm.service              I  🔧 C0906 设备信息上送成功 (1)
2025-08-20 16:20:57.339  3705-3785  WebSocket               com.dspread.mdm.service              I  🔧 C0109 被动式上送: first_connection (被动: 7)
2025-08-20 16:20:57.356  3705-3785  Platform                com.dspread.mdm.service              D  🔧 重新收集应用信息（强制刷新）
2025-08-20 16:20:57.445  3705-3785  AppManager              com.dspread.mdm.service              I  ℹ️ 应用信息获取完成: 总数131(系统129/用户2) 返回2个
2025-08-20 16:20:57.460  3705-3785  Platform                com.dspread.mdm.service              D  🔧 应用信息: 2 个应用
2025-08-20 16:20:57.623  3705-3785  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0109","serialNo":"01354090202503050399","request_time":"1755678057558","request_id":"1755678057558C0109","version":"1","data":{"apkInfo":[{"packName":"com.dspread.mdm.service","apkName":"service","versionCode":22,"versionName":"1.1.03.20250820.DSPREAD.MDM.SERVICE","updateDate":"2025-08-20 16:20:27"},{"packName":"mark.via","apkName":"Via","versionCode":20250117,"versionName":"6.2.0","updateDate":"2025-08-18 17:50:20"}],"locationInfo":{"gps":{"longitude":"-999.0","latitude":"-999.0","satelliteCount":0,"satelliteCountInUsed":0,"satelliteAverSNR":"0.00","distance":"0.0","lockStatus":"1","lockMeter":"200","proId":"","proName":""},"baseSite":{"MCC":"","MNC":"","CID":"-1","LAC":"-1","DBM":"-999","IMSI":"","ICCID":""},"wifi":{"SSID":"fubox_2.4G","IP":"**************","MAC":"BE:87:C2:42:DF:75","SSTH":"-34","BSSID":"7c:10:c9:d6:9f:10"},"address":"无法获取"},"hardwareInfo":[{"name":"CPU Usage","desc":"cpu:0.50"},{"name":"storage_usage_status","desc":" 4.56GB \/ 32.00GB ","state":"1"},{"name":"total_memory","desc":" 0.66GB \/ 1.86GB ","state":"1"},{"name":"androidVersion","desc":"14","state":"1"},{"name":"network_status","desc":"wifi","state":"1"},{"name":"OSVer","desc":"14","state":"1"}],"wifiOption":[{"SSID":"2306","SSTH":"-72"},{"SSID":"2205","SSTH":"-50"},{"SSID":"2205_5G","SSTH":"-67"},{"SSID":"DIRECT-7D-HP Laser 136w","SSTH":"-77"},{"SSID":"fubox_2.4G","SSTH":"-32"},{"SSID":"2106","SSTH":"-66"},{"SSID":"DIRECT-CC-HP Laser 1188w","SSTH":"-85"},{"SSID":"2206","SSTH":"-30"},{"SSID":"2206-5G","SSTH":"-37"},{"SSID":"DIRECT-C6-Mi All-in-One Inkjet","SSTH":"-50"},{"SSID":"2207-5G","SSTH":"-78"},{"SSID":"2103_5G","SSTH":"-82"},{"SSID":"@Ruijie-1816_5G","SSTH":"-69"},{"SSID":"@Ruijie-1816","SSTH":"-50"},{"SSID":"jibao2","SSTH":"-72"}],"sytemInfo":{"androidVersion":"14","buildNumber":"BASE_D30M-MU_GEN_AP_V1.0.8.1_202507032000_ota_test","aspl":"2025-03-05"}},"myVersionName":"1.1.03.20250820.DSPREAD.MDM.SERVICE","terminalDate":"20250820162057"}
2025-08-20 16:20:57.630  3705-3785  WebSocket               com.dspread.mdm.service              I  🔧 C0109 终端信息上传完成 (1, trigger=first_connection)
2025-08-20 16:20:57.636  3705-3785  Common                  com.dspread.mdm.service              I  ✅ 初始化信息发送完成
2025-08-20 16:20:57.642  3705-3785  WebSocket               com.dspread.mdm.service              I  🔧 检查服务更新结果
2025-08-20 16:20:57.648  3705-3785  Task                    com.dspread.mdm.service              W  ⚠️ 任务列表为空，跳过服务更新结果检查
2025-08-20 16:20:57.654  3705-3785  WebSocket               com.dspread.mdm.service              I  🔧 检查OS升级状态...
2025-08-20 16:20:57.662  3705-3785  Common                  com.dspread.mdm.service              D  🔧 OsUpdateStatusChecker WebSocket连接成功，检查OS升级状态
2025-08-20 16:20:57.669  3705-3785  Common                  com.dspread.mdm.service              D  🔧 OsUpdateStatusChecker 没有待检查的OS升级任务
2025-08-20 16:20:57.675  3705-3785  WebSocket               com.dspread.mdm.service              I  🔧 OS升级状态检查已触发
2025-08-20 16:20:57.681  3705-3785  WebSocket               com.dspread.mdm.service              I  🔧 首次连接初始化检查完成
2025-08-20 16:20:57.994  3705-3785  Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"request_time":"1755678057501","org_request_time":"1755678057307","org_request_id":"1755678057307C0906","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755678057501S0000","serialNo":"01354090202503050399"}
2025-08-20 16:20:58.004  3705-3785  WebSocket               com.dspread.mdm.service              I  🔧 收到响应消息: transCode=S0000, orgRequestId=1755678057307C0906, state=0, remark=
2025-08-20 16:20:58.010  3705-3785  WebSocket               com.dspread.mdm.service              I  🔧 处理设备信息响应: state=0
2025-08-20 16:20:58.016  3705-3785  WebSocket               com.dspread.mdm.service              I  🔧 设备信息上传成功
2025-08-20 16:20:58.629  3705-3785  Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"request_time":"1755678058140","org_request_time":"1755678057558","org_request_id":"1755678057558C0109","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755678058140S0000","serialNo":"01354090202503050399"}
2025-08-20 16:20:58.638  3705-3785  WebSocket               com.dspread.mdm.service              I  🔧 收到响应消息: transCode=S0000, orgRequestId=1755678057558C0109, state=0, remark=
2025-08-20 16:20:58.644  3705-3785  WebSocket               com.dspread.mdm.service              I  🔧 终端信息上传成功
2025-08-20 16:21:22.038  3705-3786  WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 1 (第1个，待响应: 1)
2025-08-20 16:21:22.645  3705-3785  WebSocket               com.dspread.mdm.service              I  🔧 收到服务器 PONG 帧: 1 (待响应PING: 0)
2025-08-20 16:21:52.039  3705-3786  WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 2 (第2个，待响应: 1)
2025-08-20 16:21:52.546  3705-3785  WebSocket               com.dspread.mdm.service              I  🔧 收到服务器 PONG 帧: 2 (待响应PING: 0)
2025-08-20 16:22:22.039  3705-3786  WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 3 (第3个，待响应: 1)
2025-08-20 16:22:22.754  3705-3785  WebSocket               com.dspread.mdm.service              I  🔧 收到服务器 PONG 帧: 3 (待响应PING: 0)
2025-08-20 16:22:52.039  3705-3786  WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 4 (第4个，待响应: 1)
2025-08-20 16:22:52.553  3705-3785  WebSocket               com.dspread.mdm.service              I  🔧 收到服务器 PONG 帧: 4 (待响应PING: 0)
2025-08-20 16:23:22.040  3705-3786  WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 5 (第5个，待响应: 1)
2025-08-20 16:23:22.659  3705-3785  WebSocket               com.dspread.mdm.service              I  🔧 收到服务器 PONG 帧: 5 (待响应PING: 0)
2025-08-20 16:23:52.041  3705-3786  WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 6 (第6个，待响应: 1)
2025-08-20 16:23:52.458  3705-3785  WebSocket               com.dspread.mdm.service              I  🔧 收到服务器 PONG 帧: 6 (待响应PING: 0)
2025-08-20 16:24:22.042  3705-3786  WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 7 (第7个，待响应: 1)
2025-08-20 16:24:22.564  3705-3785  WebSocket               com.dspread.mdm.service              I  🔧 收到服务器 PONG 帧: 7 (待响应PING: 0)
2025-08-20 16:24:52.043  3705-3786  WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 8 (第8个，待响应: 1)
2025-08-20 16:24:52.670  3705-3785  WebSocket               com.dspread.mdm.service              I  🔧 收到服务器 PONG 帧: 8 (待响应PING: 0)
