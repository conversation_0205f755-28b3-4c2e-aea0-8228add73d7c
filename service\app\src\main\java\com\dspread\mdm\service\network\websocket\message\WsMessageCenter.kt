package com.dspread.mdm.service.network.websocket.message

import android.annotation.SuppressLint
import android.content.Context
import com.dspread.mdm.service.network.websocket.message.handler.*
import com.dspread.mdm.service.network.websocket.constant.WsTransactionCodes
import com.dspread.mdm.service.network.websocket.task.WsTaskManager
import com.dspread.mdm.service.utils.log.Logger
import com.dspread.mdm.service.network.websocket.message.WsMessageSender
import com.dspread.mdm.service.modules.osupdate.OsUpdateStatusChecker
import com.dspread.mdm.service.broadcast.core.BroadcastActions
import com.dspread.mdm.service.SmartMdmServiceApp
import com.dspread.mdm.service.constants.Constants.WsMessageTypes
import com.dspread.mdm.service.modules.wifi.WifiProfileHandler
import com.dspread.mdm.service.modules.geofence.GeofenceHandler
import com.dspread.mdm.service.modules.logstream.LogStreamHandler
import com.dspread.mdm.service.modules.apn.ApnHandler
import org.json.JSONObject
import java.util.concurrent.ConcurrentHashMap
import kotlinx.coroutines.launch
import android.content.SharedPreferences
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.cancel

/**
 * WebSocket 消息处理中心
 * 负责消息的分发和处理
 */
@SuppressLint("StaticFieldLeak")
object WsMessageCenter {

    private const val TAG = "WsMessageCenter"
    
    private var context: Context? = null
    private var isWebSocketConnected = false

    // 消息处理专用协程作用域
    private val messageScope = CoroutineScope(Dispatchers.IO + SupervisorJob())

    // 首次连接标志
    private var isFirstTimeWebSocketConnected = true
    private var sharedPreferences: SharedPreferences? = null

    // 防重复初始化标志
    private var isInitialized = false
    
    // 各种消息处理器
    private var commandHandler: CommandHandler? = null
    private var taskHandler: TaskHandler? = null
    private var serviceHandler: ServiceHandler? = null
    private var ruleHandler: RuleHandler? = null
    private var responseHandler: ResponseHandler? = null

    // 🆕 四大模块处理器
    private var wifiProfileHandler: WifiProfileHandler? = null
    private var geofenceHandler: GeofenceHandler? = null
    private var logStreamHandler: LogStreamHandler? = null
    private var apnHandler: ApnHandler? = null



    // 响应管理功能（合并自 WsResponseManager）
    private val registerRequests = ConcurrentHashMap<String, String>()

    /**
     * 初始化消息中心
     */
    fun init(context: Context) {
        Logger.wsm("开始初始化 WsMessageCenter...")

        if (isInitialized) {
            Logger.wsm("WsMessageCenter 已初始化，重新读取首次连接标志")
        } else {
            Logger.wsm("WsMessageCenter 首次初始化")
        }

        // 使用 ApplicationContext 避免内存泄漏
        this.context = context.applicationContext

        // 初始化SharedPreferences
        this.sharedPreferences = context.applicationContext.getSharedPreferences("ws_message_center", Context.MODE_PRIVATE)

        // 只在首次初始化时创建处理器，避免重复创建
        if (!isInitialized) {
            // 初始化任务管理器
            WsTaskManager.init(context.applicationContext)

            // 初始化各种处理器
            commandHandler = CommandHandler(context.applicationContext)
            taskHandler = TaskHandler(context.applicationContext)
            serviceHandler = ServiceHandler(context.applicationContext)
            ruleHandler = RuleHandler(context.applicationContext)
            responseHandler = ResponseHandler(context.applicationContext)

            // 🆕 初始化四大模块处理器
            wifiProfileHandler = WifiProfileHandler(context.applicationContext)
            geofenceHandler = GeofenceHandler(context.applicationContext)
            logStreamHandler = LogStreamHandler(context.applicationContext)
            apnHandler = ApnHandler(context.applicationContext)

            isInitialized = true
            Logger.wsm("WsMessageCenter 初始化完成")
        } else {
            Logger.wsm("WsMessageCenter 首次连接标志已更新")
        }
    }

    /**
     * 重置首次连接标志（用于测试或重新安装后）
     */
    fun resetFirstTimeConnectionFlag() {
        isFirstTimeWebSocketConnected = true
        sharedPreferences?.edit()?.putBoolean("is_first_time_connected", true)?.apply()
        Logger.wsm("首次连接标志已重置为 true")
    }

    /**
     * 处理接收到的消息
     */
    @SuppressLint("StaticFieldLeak")
    fun handleMessage(message: String) {
        try {
//            Logger.wsm("收到 WebSocket 消息: $message")
            
            val jsonObject = JSONObject(message)
            if (!jsonObject.has("tranCode")) {
                Logger.wsmE("消息缺少 tranCode 字段")
                return
            }
            
            val tranCode = jsonObject.getString("tranCode")
//            Logger.wsm("处理消息类型: $tranCode")
            
            // 分析是否需要执行命令（基于网络状态等条件）
            val shouldExecute = analyzeCommandExecution(tranCode, jsonObject)
            
            // 对于非响应消息且需要执行的命令，发送确认响应
            if (!tranCode.startsWith("S0000") && shouldExecute && !isHeartbeatCall(tranCode, jsonObject)) {
                sendAcknowledgment(jsonObject)
            }
            
            // 处理连接成功消息
            if (jsonObject.has("hellMsg")) {
                handleConnectionSuccess(jsonObject)
                return
            }
            
            // 根据 tranCode 分发到对应的处理器
            when {
                tranCode.startsWith("SC") -> {
                    // 🆕 优先检查四大模块消息（修正后的消息类型）
                    when (tranCode) {
                        WsMessageTypes.WIFI_PROFILE_CONFIG -> {
                            // WiFi Profile配置消息 (SC007)
                            if (shouldExecute) {
                                Logger.wsm("处理WiFi Profile配置消息 (SC007)")
                                messageScope.launch {
                                    try {
                                        wifiProfileHandler?.handleMessage(message)
                                    } catch (e: Exception) {
                                        Logger.wsmE("WiFi Profile处理失败", e)
                                    }
                                }
                            }
                        }
                        WsMessageTypes.LOG_STREAM_CONTROL -> {
                            // 日志流控制消息 (SC009)
                            if (shouldExecute) {
                                Logger.wsm("处理日志流控制消息 (SC009)")
                                messageScope.launch {
                                    try {
                                        logStreamHandler?.handleMessage(message)
                                    } catch (e: Exception) {
                                        Logger.wsmE("日志流处理失败", e)
                                    }
                                }
                            }
                        }
                        WsMessageTypes.APN_CONFIG -> {
                            // APN配置管理消息 (SC010)
                            if (shouldExecute) {
                                Logger.wsm("处理APN配置管理消息 (SC010)")
                                messageScope.launch {
                                    try {
                                        apnHandler?.handleMessage(message)
                                    } catch (e: Exception) {
                                        Logger.wsmE("APN管理处理失败", e)
                                    }
                                }
                            }
                        }
                        else -> {
                            // 其他SC命令由CommandHandler处理
                            if (shouldExecute) {
                                commandHandler?.handleMessage(message)
                            }
                        }
                    }
                }
                tranCode.startsWith("ST") -> {
                    // 🆕 ST系列消息处理（包括地理围栏ST007）
                    when (tranCode) {
                        WsMessageTypes.GEOFENCE_CONFIG -> {
                            // 地理围栏配置消息 (ST007)
                            if (shouldExecute) {
                                Logger.wsm("处理地理围栏配置消息 (ST007)")
                                messageScope.launch {
                                    try {
                                        geofenceHandler?.handleMessage(message)
                                    } catch (e: Exception) {
                                        Logger.wsmE("地理围栏处理失败", e)
                                    }
                                }
                            }
                        }
                        else -> {
                            // 其他ST命令的现有处理逻辑
                            when {
                                tranCode.startsWith("ST001") -> {
                                    // 任务处理
                                    if (shouldExecute) {
                                        taskHandler?.handleMessage(message)
                                    }
                                }
                                tranCode.startsWith("ST002") -> {
                                    // 规则处理（ST002）
                                    if (shouldExecute) {
                                        ruleHandler?.handleMessage(message)
                                    }
                                }
                                tranCode.startsWith("ST003") -> {
                                    // 服务处理
                                    if (shouldExecute) {
                                        serviceHandler?.handleMessage(message)
                                    }
                                }
                                tranCode.startsWith("ST005") -> {
                                    // 规则处理（ST005）
                                    if (shouldExecute) {
                                        ruleHandler?.handleMessage(message)
                                    }
                                }
                                tranCode.startsWith("ST006") -> {
                                    // 服务处理
                                    if (shouldExecute) {
                                        messageScope.launch {
                                            try {
                                                wifiProfileHandler?.handleMessage(message)
                                            } catch (e: Exception) {
                                                Logger.wsmE("Wifi-Profile 处理失败", e)
                                            }
                                        }
                                    }
                                }
                                tranCode.startsWith("ST009") -> {
                                    // 规则处理（ST005）
                                    if (shouldExecute) {
                                        messageScope.launch {
                                            try {
                                                apnHandler?.handleMessage(message)
                                            } catch (e: Exception) {
                                                Logger.wsmE("Apn处理失败", e)
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
                tranCode.startsWith("S0000") -> {
                    // 响应处理
                    responseHandler?.handleMessage(message)
                }
                tranCode.startsWith("SP001") -> {
                    // 支付相关处理
                    handlePaymentMessage(jsonObject)
                }
                else -> {
                    Logger.wsmE("未知的消息类型: $tranCode")
                }
            }
            
        } catch (e: Exception) {
            Logger.wsmE("处理消息失败", e)
            // 上报WebSocket消息处理异常
            reportWebSocketException("MESSAGE_PROCESSING", e, message)
        }
    }

    /**
     * 处理连接成功
     */
    private fun handleConnectionSuccess(jsonObject: JSONObject) {
        Logger.wsm("WebSocket 连接成功")
        isWebSocketConnected = true

        try {
            // 处理设备状态
            if (jsonObject.has("deviceStatus")) {
                val deviceStatus = jsonObject.getString("deviceStatus")
                Logger.wsm("设备状态: $deviceStatus")
                // 保存设备状态到 SharedPreferences
                // SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_UNBOX_STATUS, deviceStatus)
            }

            // 🔥 首次连接时的特殊处理
            Logger.wsm("检查首次连接标志: isFirstTimeWebSocketConnected=$isFirstTimeWebSocketConnected")

            if (isFirstTimeWebSocketConnected) {
                Logger.wsm("首次WebSocket连接成功，发送初始化信息并执行初始化检查")

                // 只在首次连接时发送初始化信息，避免频繁重复上送
                sendInitialConnectionInfo()

                // 设置首次连接标志为false，并记录连接时间
                isFirstTimeWebSocketConnected = false
                sharedPreferences?.edit()
                    ?.putBoolean("is_first_time_connected", false)
                    ?.putLong("last_connection_time", System.currentTimeMillis())
                    ?.apply()

                // 检查服务更新结果
                WsTaskManager.checkServiceUpdateResult()

                // 检查OS升级状态
                checkOSUpdateResult()

                Logger.wsm("首次连接初始化检查完成")
            } else {
                Logger.wsm("非首次连接，跳过初始化信息发送和初始化检查")

                // 非首次连接时，只执行必要的状态检查，不发送C09系列消息
                Logger.wsm("执行必要的状态同步检查")
                WsTaskManager.checkServiceUpdateResult()
                checkOSUpdateResult()
            }

        } catch (e: Exception) {
            Logger.wsmE("处理连接成功失败", e)
        }
    }

    /**
     * 检查OS升级结果
     * 在WebSocket连接成功后检查是否有待上报的OS升级结果
     */
    private fun checkOSUpdateResult() {
        try {
            Logger.wsm("检查OS升级状态...")

            // 获取应用上下文
            val context = SmartMdmServiceApp.instance

            // 调用OS升级状态检查器
            OsUpdateStatusChecker.checkOsUpdateStatusOnBoot(context)

            Logger.wsm("OS升级状态检查已触发")

        } catch (e: Exception) {
            Logger.wsmE("检查OS升级状态失败", e)
        }
    }

    /**
     * 发送连接成功后的初始化信息
     * 委托给 WsMessageSender 处理具体的协议逻辑
     */
    private fun sendInitialConnectionInfo() {
        // 消息中心只负责调用，具体协议逻辑由 WsMessageSender 管理
        WsMessageSender.sendInitialConnectionInfo()
    }

    /**
     * 分析是否应该执行命令
     */
    private fun analyzeCommandExecution(tranCode: String, jsonObject: JSONObject): Boolean {
        // 默认执行所有命令
        var shouldExecute = true
        
        try {
            // 针对特定命令检查网络条件
            if (tranCode in listOf("ST001", "ST003", "ST005")) {
                // 检查是否有 installBy 字段且值为 "1"（仅WiFi安装）
                if (jsonObject.has("data")) {
                    val dataObject = jsonObject.getJSONObject("data")
                    val listParameterName = when (tranCode) {
                        "ST001" -> "taskList"
                        "ST003" -> "serviceList"
                        "ST005" -> "ruleList"
                        else -> null
                    }
                    
                    listParameterName?.let { paramName ->
                        if (dataObject.has(paramName)) {
                            val listArray = dataObject.getJSONArray(paramName)
                            for (i in 0 until listArray.length()) {
                                val item = listArray.getJSONObject(i)
                                if (item.has("installBy") && item.getString("installBy") == "1") {
                                    // 检查当前网络是否为移动网络
                                    if (isMobileNetwork()) {
                                        shouldExecute = false
                                        Logger.wsmW("检测到仅WiFi安装要求，但当前为移动网络，跳过执行: $tranCode")
                                        break
                                    }
                                }
                            }
                        }
                    }
                }
            }
        } catch (e: Exception) {
            Logger.wsmE("分析命令执行条件失败", e)
        }
        
        return shouldExecute
    }

    /**
     * 检查是否为心跳调用
     */
    private fun isHeartbeatCall(tranCode: String, jsonObject: JSONObject): Boolean {
        return try {
            tranCode == WsTransactionCodes.CMD_HEARTBEAT &&
            jsonObject.has("data") && 
            jsonObject.getJSONObject("data").optString("c_type") == "CALLHB"
        } catch (e: Exception) {
            false
        }
    }

    /**
     * 发送确认响应（C0000）
     */
    private fun sendAcknowledgment(jsonObject: JSONObject) {
        try {
            val requestId = jsonObject.optString("request_id")
            val requestTime = jsonObject.optString("request_time")

            if (requestId.isNotEmpty() && requestTime.isNotEmpty()) {
                WsMessageSender.sendWebSocketResponse(requestId, requestTime, "0", null)
                Logger.wsm("已发送C0000响应确认: requestId=$requestId")
            } else {
                Logger.wsmW("消息缺少request_id或request_time，无法发送响应确认")
            }
        } catch (e: Exception) {
            Logger.wsmE("发送确认响应失败", e)
        }
    }

    /**
     * 处理支付相关消息
     */
    private fun handlePaymentMessage(jsonObject: JSONObject) {
        try {
            val data = jsonObject.optJSONObject("data")
            if (data != null) {
                val packageName = data.optString("packageName")
                val paymentDataMD5 = data.optString("paymentDataMD5")
                Logger.wsm("处理支付消息: package=$packageName, md5=$paymentDataMD5")
                // 这里可以删除相关的支付记录
            }
        } catch (e: Exception) {
            Logger.wsmE("处理支付消息失败", e)
        }
    }

    /**
     * 处理连接断开
     */
    fun handleDisconnection() {
        Logger.wsm("WebSocket 连接断开!")
        isWebSocketConnected = false
        // 可以在这里处理断开连接的逻辑
    }

    /**
     * 检查是否为移动网络
     */
    private fun isMobileNetwork(): Boolean {
        // 这里需要实现网络类型检查
        // 可以使用 ConnectivityManager 来检查网络类型
        return false // 暂时返回 false
    }

    /**
     * 获取连接状态
     */
    fun isConnected(): Boolean = isWebSocketConnected

    /**
     * 释放资源
     */
    fun release() {
        context = null
        commandHandler = null
        taskHandler = null
        serviceHandler = null
        ruleHandler = null
        responseHandler = null
        registerRequests.clear()
        messageScope.cancel()
        isWebSocketConnected = false
        Logger.wsm("WsMessageCenter 资源已释放")
    }

    // ==================== 响应管理功能（合并自 WsResponseManager） ====================

    /**
     * 注册请求（registerAction）
     */
    fun registerAction(tranCode: String, requestId: String) {
        registerRequests[tranCode] = requestId
        Logger.wsm("注册请求: tranCode=$tranCode, requestId=$requestId")
    }

    /**
     * 获取注册的请求ID
     */
    fun getRegisteredRequestId(tranCode: String): String? {
        return registerRequests[tranCode]
    }

    /**
     * 移除注册的请求
     */
    fun removeRegisteredAction(tranCode: String): String? {
        val requestId = registerRequests.remove(tranCode)
        if (requestId != null) {
            Logger.wsm("移除注册请求: tranCode=$tranCode, requestId=$requestId")
        }
        return requestId
    }

    /**
     * 清空所有注册的请求
     */
    fun clearAllRegistrations() {
        val count = registerRequests.size
        registerRequests.clear()
        Logger.wsm("清空所有注册请求，共 $count 个")
    }

    /**
     * 获取所有注册的请求
     */
    fun getAllRegistrations(): Map<String, String> {
        return HashMap(registerRequests)
    }

    /**
     * 检查是否有注册的请求
     */
    fun hasRegisteredAction(tranCode: String): Boolean {
        return registerRequests.containsKey(tranCode)
    }

    /**
     * 处理响应消息（dispatch 方法）
     */
    fun dispatch(response: String) {
        try {
            Logger.wsm("处理响应消息: $response")

            // 这里可以添加具体的响应处理逻辑
            // 比如解析响应，匹配请求，执行回调等

        } catch (e: Exception) {
            Logger.wsmE("处理响应消息失败", e)
        }
    }

    /**
     * 上报WebSocket异常
     */
    private fun reportWebSocketException(type: String, exception: Exception, context: String = "") {
        try {
            Logger.wsmE("WebSocket异常上报: $type", exception)

            // 构建异常报告
            val exceptionReport = mapOf(
                "type" to type,
                "exception" to exception.javaClass.simpleName,
                "message" to (exception.message ?: ""),
                "context" to context,
                "timestamp" to System.currentTimeMillis(),
                "stackTrace" to exception.stackTrace.take(5).joinToString("\n") { it.toString() }
            )

            // 可以通过WebSocket发送异常报告给服务器
            // 或者存储到本地日志中供后续分析
            Logger.wsmI("异常报告: $exceptionReport")

        } catch (e: Exception) {
            Logger.wsmE("上报WebSocket异常失败", e)
        }
    }
}
