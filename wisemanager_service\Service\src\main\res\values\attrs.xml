<?xml version="1.0" encoding="utf-8"?>
<resources>
    <declare-styleable name="PasswordEditText">
        <!-- 密码的个数 -->
        <attr name="passwordNumber" format="integer"/>
        <!-- 密码圆点的半径 -->
        <attr name="passwordRadius" format="dimension" />
        <!-- 密码圆点的颜色 -->
        <attr name="passwordColor" format="color" />
        <!-- 分割线的颜色 -->
        <attr name="divisionLineColor" format="color" />
        <!-- 分割线的大小 -->
        <attr name="divisionLineSize" format="color" />
        <!-- 背景边框的颜色 -->
        <attr name="bgColor" format="color" />
        <!-- 背景边框的大小 -->
        <attr name="bgSize" format="dimension" />
        <!-- 背景边框的圆角大小 -->
        <attr name="bgCorner" format="dimension"/>
        <!-- 背景边框的圆角大小 -->
        <attr name="showPassword" format="boolean"/>
    </declare-styleable>

</resources>
