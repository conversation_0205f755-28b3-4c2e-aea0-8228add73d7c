package com.bbpos.wiseapp.tms.location;

import static android.content.Context.WIFI_SERVICE;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.IntentFilter;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import android.net.wifi.ScanResult;
import android.net.wifi.WifiInfo;
import android.net.wifi.WifiManager;
import android.os.Build;

import com.bbpos.wiseapp.logger.BBLog;
import com.bbpos.wiseapp.service.common.RequestPermission;
import com.bbpos.wiseapp.settings.utils.WirelessUtil;

import org.json.JSONArray;
import org.json.JSONObject;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class WifiLocationManager {

	private Context mContext;
	private WifiManager wifiManager;
	private static WifiLocationManager wifiLocationManager = null;

	public static WifiLocationManager getInstance(Context context) {
		if (wifiLocationManager == null) {
			wifiLocationManager = new WifiLocationManager(context);
		}

		return wifiLocationManager;
	}

	public WifiLocationManager(Context context) {
		mContext = context;
		wifiManager = (WifiManager) mContext.getSystemService(WIFI_SERVICE);
	}

	synchronized public void scanWifi() {
		wifiManager.startScan();
	}
	
	public void getLocation(BroadcastReceiver receiver) {
		mContext.registerReceiver(receiver, new IntentFilter(WifiManager.SCAN_RESULTS_AVAILABLE_ACTION), RequestPermission.REQUEST_PERMISSION_MY_BROADCAST, null);
		wifiManager.startScan();
	}
	
	public List<ScanResult> getWifiList() {
		return wifiManager.getScanResults();
	}
/*
	public String getLocationWifi() {

		// 采用Android默认的HttpClient
		HttpClient client = new DefaultHttpClient();
		// 采用POST方法
		HttpPost post = new HttpPost(LocationUtil.LOCATION_URL);
		try {
			// 构造POST的JSON数据
			JSONObject holder = new JSONObject();
			holder.put("version", "1.1.0");
			holder.put("host", LocationUtil.LOCATION_HOST);
			holder.put("address_language", "zh_CN");
			holder.put("request_address", true);

			JSONArray towerarray = new JSONArray();
			List<ScanResult> wifiList = getWifiList();
			for (int i = 0; i < wifiList.size(); i++) {
				JSONObject tower = new JSONObject();
				tower.put("mac_address", wifiList.get(i).BSSID);
				tower.put("ssid", wifiList.get(i).SSID);
				tower.put("signal_strength", wifiList.get(i).level);
				towerarray.put(tower);
			}

			holder.put("wifi_towers", towerarray);
			LocationUtil.logd("holder.put: " + holder.toString());

			StringEntity query = new StringEntity(holder.toString());
			post.setEntity(query);

			// 发出POST数据并获取返回数据
			HttpResponse response = client.execute(post);
			HttpEntity entity = response.getEntity();
			BufferedReader buffReader = new BufferedReader(new InputStreamReader(entity.getContent()));
			StringBuffer strBuff = new StringBuffer();
			String result = null;
			while ((result = buffReader.readLine()) != null) {
				strBuff.append(result);
			}
			LocationUtil.logd("result: " + strBuff.toString());

			// 解析返回的JSON数据获得经纬度
			JSONObject json = new JSONObject(strBuff.toString());
			JSONObject subjosn = new JSONObject(json.getString("location"));

			String latitude = subjosn.getString("latitude");
			String longitude = subjosn.getString("longitude");

			return LocationUtil.getLocation(latitude, longitude);

		} catch(ClientProtocolException e) {
			LocationUtil.loge("ClientProtocolException : " + e.getMessage());
		}catch(IOException e) {
			LocationUtil.loge("IOException : " + e.getMessage());
		} catch (Exception e) {
			LocationUtil.loge("Exception : " + e.getMessage());
		} finally{
			post.abort();
			client = null;
		}
		return null;
    }
*/

	public JSONArray getLocationJsonWifi() {
		/** 构造POST的JSON数据 */
		JSONArray towerarray = new JSONArray();
		try {
			List<ScanResult> wifiList = getWifiList();

			if(wifiList==null || wifiList.size()==0) {
				return null;
			}
			BBLog.e(BBLog.TAG, "getLocationJsonWifi wifiList = " + wifiList.size());
			//对SSID做去重处理，保留信号值较大的
			Map<String,Integer> dataMap = new HashMap<>();
			for (final ScanResult result : wifiList) {
				if (!result.SSID.isEmpty()) {
					if (dataMap.containsKey(result.SSID)) {
						if (result.level > dataMap.get(result.SSID)) {
							dataMap.put(result.SSID,result.level);
						}
					} else {
						dataMap.put(result.SSID,result.level);
					}
				}
			}
			//赋值
			for (final String ssid : dataMap.keySet()) {
				JSONObject tower = new JSONObject();
				tower.put("SSID", ""+ssid);
				tower.put("SSTH", ""+dataMap.get(ssid));
				towerarray.put(tower);
			}

		} catch (Exception e) {
			BBLog.e(BBLog.TAG, "get wifi info error"+e.getMessage());
			e.printStackTrace();
		}
		return towerarray;
	}

	public JSONObject getLocationJsonWifiFirst() {
		/** 构造POST的JSON数据 */
		JSONObject tower = new JSONObject();
		try {
			List<ScanResult> wifiList = getWifiList();
			if(wifiList==null || wifiList.size()==0) {
				return null;
			} else {
			    WifiInfo info =getCurrentWIFI(mContext);
			    if (info != null) {
                    if (Build.VERSION.SDK_INT < Build.VERSION_CODES.KITKAT) {
                        tower.put("SSID", "" + info.getSSID());
                    } else {
                        tower.put("SSID", "" + info.getSSID().replace("\"", ""));
                    }
                    int ip = info.getIpAddress();
					tower.put("IP", "" + WirelessUtil.int2ip(ip));
                    tower.put("MAC", "" + info.getMacAddress());
                    tower.put("SSTH", "" + info.getRssi());
					tower.put("FREQ",getWifiFrequency(info.getFrequency()));
					tower.put("BSSID", "" + WirelessUtil.getRouterMac(WirelessUtil.int2Gateway(ip)));
                } else {
			    	return null;
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		}

		return tower;
	}

	public WifiInfo getCurrentWIFI(Context context) {
//		if (Build.VERSION.SDK_INT <= Build.VERSION_CODES.O||Build.VERSION.SDK_INT==28) {
		ConnectivityManager cm = (ConnectivityManager) context.getSystemService(Context.CONNECTIVITY_SERVICE);
		WifiManager mWifiManager = (WifiManager) context.getApplicationContext().getSystemService(Context.WIFI_SERVICE);
		NetworkInfo wifiNetworkInfo = cm.getNetworkInfo(ConnectivityManager.TYPE_WIFI);
		if (wifiNetworkInfo.isConnected()) {
			return mWifiManager.getConnectionInfo();
		} else {
			return null;
		}
//		}
	}

	public String getWIFISSID(Context context) {
		String ssid="unknown id";
		BBLog.d("TAG", "getWIFISSID: API: "+ Build.VERSION.SDK_INT);
		if (Build.VERSION.SDK_INT <= Build.VERSION_CODES.O||Build.VERSION.SDK_INT>=28) {

			WifiManager mWifiManager = (WifiManager) context.getApplicationContext().getSystemService(Context.WIFI_SERVICE);

			assert mWifiManager != null;
			WifiInfo info = mWifiManager.getConnectionInfo();

			if (Build.VERSION.SDK_INT < Build.VERSION_CODES.KITKAT) {
				BBLog.d("TAG", "getWIFISSID: getExtraInfo11: "+info.getSSID());
				return info.getSSID();
			} else {
				BBLog.d("TAG", "getWIFISSID: getExtraInfo222: "+info.getSSID());
				return info.getSSID().replace("\"", "");
			}
		} else if (Build.VERSION.SDK_INT==Build.VERSION_CODES.O_MR1) {

			ConnectivityManager connManager = (ConnectivityManager) context.getApplicationContext().getSystemService(Context.CONNECTIVITY_SERVICE);
			assert connManager != null;
			NetworkInfo networkInfo = connManager.getActiveNetworkInfo();
			if (networkInfo.isConnected()) {
				if (networkInfo.getExtraInfo()!=null) {
					BBLog.d("TAG", "getWIFISSID: getExtraInfo: "+networkInfo.getExtraInfo());
					return networkInfo.getExtraInfo().replace("\"","");
				}
			}
		}
		return ssid;
	}

	public String getWifiFrequency(int frequency) {
		if (frequency > 2400 && frequency < 2500) {
			return "2.4GHz";
		} else if (frequency > 4900 && frequency < 5900) {
			return "5GHz";
		}
		return "2.4GHz";
	}
}








