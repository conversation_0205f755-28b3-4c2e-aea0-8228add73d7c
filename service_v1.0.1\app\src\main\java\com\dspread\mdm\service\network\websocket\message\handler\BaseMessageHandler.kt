package com.dspread.mdm.service.network.websocket.message.handler

import android.content.Context
import com.dspread.mdm.service.network.websocket.message.WsMessageSender
import com.dspread.mdm.service.utils.log.Logger
import org.json.JSONObject

/**
 * 消息处理器基类
 */
abstract class BaseMessageHandler(protected val context: Context) {

    /**
     * 处理消息的抽象方法
     */
    abstract fun handleMessage(message: String)

    /**
     * 解析 JSON 消息
     */
    protected fun parseMessage(message: String): JSONObject? {
        return try {
            JSONObject(message)
        } catch (e: Exception) {
            Logger.wsmE("解析消息失败: $message", e)
            null
        }
    }

    /**
     * 获取消息中的 data 字段
     */
    protected fun getDataFromMessage(jsonObject: JSONObject): JSONObject? {
        return try {
            if (jsonObject.has("data")) {
                jsonObject.getJSONObject("data")
            } else {
                null
            }
        } catch (e: Exception) {
            Logger.wsmE("获取 data 字段失败", e)
            null
        }
    }

    /**
     * 获取消息的基本信息
     */
    protected fun getMessageInfo(jsonObject: JSONObject): MessageInfo {
        return MessageInfo(
            tranCode = jsonObject.optString("tranCode"),
            requestId = jsonObject.optString("request_id"),
            requestTime = jsonObject.optString("request_time"),
            serialNo = jsonObject.optString("serialNo"),
            version = jsonObject.optString("version")
        )
    }

    /**
     * 发送响应消息
     */
    protected fun sendResponse(originalMessage: JSONObject, responseState: String, responseRemark: String) {
        try {
            val messageInfo = getMessageInfo(originalMessage)
            WsMessageSender.sendResponse(
                tranCode = messageInfo.tranCode,
                requestId = messageInfo.requestId,
                requestTime = messageInfo.requestTime,
                responseState = responseState,
                responseRemark = responseRemark
            )
        } catch (e: Exception) {
            Logger.wsmE("发送响应失败", e)
        }
    }

    /**
     * 消息基本信息数据类
     */
    data class MessageInfo(
        val tranCode: String,
        val requestId: String,
        val requestTime: String,
        val serialNo: String,
        val version: String
    )
}
