package com.bbpos.wiseapp.service.bbdevice;

import android.bluetooth.BluetoothDevice;

import com.bbpos.bbdevice.BBDeviceController;
import com.bbpos.bbdevice.CAPK;

import java.util.ArrayList;
import java.util.Hashtable;
import java.util.List;

public class DefaultBBDeviceControllerListener implements BBDeviceController.BBDeviceControllerListener {
    @Override
    public void onWaitingForCard(BBDeviceController.CheckCardMode checkCardMode) {

    }

    @Override
    public void onWaitingReprintOrPrintNext() {

    }

    @Override
    public void onBTReturnScanResults(List<BluetoothDevice> list) {

    }

    @Override
    public void onBTScanTimeout() {

    }

    @Override
    public void onBTScanStopped() {

    }

    @Override
    public void onBTConnected(BluetoothDevice bluetoothDevice) {

    }

    @Override
    public void onBTDisconnected() {

    }

    @Override
    public void onBTRequestPairing() {

    }

    @Override
    public void onUsbConnected() {

    }

    @Override
    public void onUsbDisconnected() {

    }

    @Override
    public void onSerialConnected() {

    }

    @Override
    public void onSerialDisconnected() {

    }

    @Override
    public void onReturnCheckCardResult(BBDeviceController.CheckCardResult checkCardResult, Hashtable<String, String> hashtable) {

    }

    @Override
    public void onReturnCancelCheckCardResult(boolean b) {

    }

    @Override
    public void onReturnDeviceInfo(Hashtable<String, String> hashtable) {

    }

    @Override
    public void onReturnTransactionResult(BBDeviceController.TransactionResult transactionResult) {

    }

    @Override
    public void onReturnBatchData(String s) {

    }

    @Override
    public void onReturnReversalData(String s) {

    }

    @Override
    public void onReturnAmountConfirmResult(boolean b) {

    }

    @Override
    public void onReturnPinEntryResult(BBDeviceController.PinEntryResult pinEntryResult, Hashtable<String, String> hashtable) {

    }

    @Override
    public void onReturnPrintResult(BBDeviceController.PrintResult printResult) {

    }

    @Override
    public void onReturnAccountSelectionResult(BBDeviceController.AccountSelectionResult accountSelectionResult, int i) {

    }

    @Override
    public void onReturnAmount(BBDeviceController.AmountInputResult amountInputResult, Hashtable<String, String> hashtable) {

    }

    @Override
    public void onReturnUpdateAIDResult(Hashtable<String, Object> hashtable) {

    }

    @Override
    public void onReturnUpdateTerminalSettingResult(BBDeviceController.TerminalSettingStatus terminalSettingStatus) {

    }

    @Override
    public void onReturnUpdateTerminalSettingsResult(Hashtable<String, BBDeviceController.TerminalSettingStatus> hashtable) {

    }

    @Override
    public void onReturnUpdateDisplayStringResult(boolean b, String s) {

    }

    @Override
    public void onReturnReadDisplayStringResult(boolean b, Hashtable<String, String> hashtable) {

    }

    @Override
    public void onReturnReadDisplaySettingsResult(boolean b, Hashtable<String, Object> hashtable) {

    }

    @Override
    public void onReturnReadAIDResult(Hashtable<String, Object> hashtable) {

    }

    @Override
    public void onReturnReadTerminalSettingResult(Hashtable<String, Object> hashtable) {

    }

    @Override
    public void onReturnEnableAccountSelectionResult(boolean b) {

    }

    @Override
    public void onReturnEnableInputAmountResult(boolean b) {

    }

    @Override
    public void onReturnEnableBluetoothResult(boolean b) {

    }

    @Override
    public void onReturnCAPKList(List<CAPK> list) {

    }

    @Override
    public void onReturnCAPKDetail(CAPK capk) {

    }

    @Override
    public void onReturnCAPKLocation(String s) {

    }

    @Override
    public void onReturnUpdateCAPKResult(boolean b) {

    }

    @Override
    public void onReturnRemoveCAPKResult(boolean b) {

    }

    @Override
    public void onReturnEmvReportList(Hashtable<String, String> hashtable) {

    }

    @Override
    public void onReturnEmvReport(String s) {

    }

    @Override
    public void onReturnDisableAccountSelectionResult(boolean b) {

    }

    @Override
    public void onReturnDisableInputAmountResult(boolean b) {

    }

    @Override
    public void onReturnDisableBluetoothResult(boolean b) {

    }

    @Override
    public void onReturnPhoneNumber(BBDeviceController.PhoneEntryResult phoneEntryResult, String s) {

    }

    @Override
    public void onReturnEmvCardDataResult(boolean b, String s) {

    }

    @Override
    public void onReturnEmvCardNumber(boolean b, String s) {

    }

    @Override
    public void onReturnEncryptPinResult(boolean b, Hashtable<String, String> hashtable) {

    }

    @Override
    public void onReturnEncryptDataResult(boolean b, Hashtable<String, String> hashtable) {

    }

    @Override
    public void onReturnInjectSessionKeyResult(boolean b, Hashtable<String, String> hashtable) {

    }

    @Override
    public void onReturnPowerOnIccResult(boolean b, String s, String s1, int i) {

    }

    @Override
    public void onReturnPowerOffIccResult(boolean b) {

    }

    @Override
    public void onReturnApduResult(boolean b, Hashtable<String, Object> hashtable) {

    }

    @Override
    public void onRequestSelectApplication(ArrayList<String> arrayList) {

    }

    @Override
    public void onRequestSelectAccountType() {

    }

    @Override
    public void onRequestSetAmount() {

    }

    @Override
    public void onRequestOtherAmount(BBDeviceController.AmountInputType amountInputType) {

    }

    @Override
    public void onRequestPinEntry(BBDeviceController.PinEntrySource pinEntrySource) {

    }

    @Override
    public void onRequestManualPanEntry(BBDeviceController.ManualPanEntryType manualPanEntryType) {

    }

    @Override
    public void onReturnSetPinPadButtonsResult(boolean b) {

    }

	@Override
	public void onReturnSetPinPadOrientationResult(boolean b) {

	}

    @Override
    public void onReturnAccessiblePINPadTouchEvent(BBDeviceController.PinPadTouchEvent pinPadTouchEvent) {

    }

    @Override
    public void onReturnUpdateDisplaySettingsProgress(double v) {

    }

    @Override
    public void onReturnUpdateDisplaySettingsResult(boolean b, String s) {

    }

    @Override
    public void onRequestOnlineProcess(String s) {

    }

    @Override
    public void onRequestTerminalTime() {

    }

    @Override
    public void onRequestDisplayText(BBDeviceController.DisplayText displayText, String s) {

    }

    @Override
    public void onRequestDisplayAsterisk(String s, int i) {

    }

    @Override
    public void onRequestDisplayLEDIndicator(BBDeviceController.ContactlessStatus contactlessStatus) {

    }

    @Override
    public void onRequestProduceAudioTone(BBDeviceController.ContactlessStatusTone contactlessStatusTone) {

    }

    @Override
    public void onRequestClearDisplay() {

    }

    @Override
    public void onRequestFinalConfirm() {

    }

    @Override
    public void onRequestAmountConfirm(Hashtable<String, String> hashtable) {

    }

    @Override
    public void onRequestPrintData(int i, boolean b) {

    }

    @Override
    public void onPrintDataCancelled() {

    }

    @Override
    public void onPrintDataEnd() {

    }

    @Override
    public void onBatteryLow(BBDeviceController.BatteryStatus batteryStatus) {

    }

    @Override
    public void onError(BBDeviceController.Error error, String s) {

    }

    @Override
    public void onSessionInitialized() {

    }

    @Override
    public void onSessionError(BBDeviceController.SessionError sessionError, String s) {

    }

    @Override
    public void onReturnDebugLog(Hashtable<String, Object> hashtable) {

    }

    @Override
    public void onDeviceHere(boolean b) {

    }

    @Override
    public void onPowerDown() {

    }

    @Override
    public void onPowerButtonPressed() {

    }

    @Override
    public void onPowerConnected(BBDeviceController.PowerSource powerSource, BBDeviceController.BatteryStatus batteryStatus) {

    }

    @Override
    public void onPowerDisconnected(BBDeviceController.PowerSource powerSource) {

    }

    @Override
    public void onDeviceReset(boolean b, BBDeviceController.DeviceResetReason deviceResetReason) {

    }

    @Override
    public void onDeviceResetAlert(int i) {

    }

    @Override
    public void onEnterStandbyMode() {

    }

    @Override
    public void onReturnWatchdogTimerReset() {

    }

    @Override
    public void onReturnNfcDataExchangeResult(boolean b, Hashtable<String, String> hashtable) {

    }

    @Override
    public void onReturnNfcDetectCardResult(BBDeviceController.NfcDetectCardResult nfcDetectCardResult, Hashtable<String, Object> hashtable) {

    }

    @Override
    public void onReturnControlLEDResult(boolean b, String s) {

    }

    @Override
    public void onReturnVasResult(BBDeviceController.VASResult vasResult, Hashtable<String, Object> hashtable) {

    }

    @Override
    public void onRequestStartEmv() {

    }

    @Override
    public void onDeviceDisplayingPrompt() {

    }

    @Override
    public void onRequestKeypadResponse() {

    }

    @Override
    public void onReturnDisplayPromptResult(BBDeviceController.DisplayPromptResult displayPromptResult) {

    }

    @Override
    public void onReturnFunctionKey(BBDeviceController.FunctionKey functionKey) {

    }

    @Override
    public void onHardwareFunctionalTestResult(int i, int i1, String s) {

    }

    @Override
    public void onBarcodeReaderConnected() {

    }

    @Override
    public void onBarcodeReaderDisconnected() {

    }

    @Override
    public void onReturnBarcode(String s) {

    }

    @Override
    public void onRequestVirtuCryptPEDIResponse(boolean b, Hashtable<String, String> hashtable) {

    }

    @Override
    public void onReturnVirtuCryptPEDICommandResult(boolean b, Hashtable<String, String> hashtable) {

    }

    @Override
    public void onRequestVirtuCryptPEDKResponse(boolean b, Hashtable<String, String> hashtable) {

    }

    @Override
    public void onReturnVirtuCryptPEDKCommandResult(boolean b, Hashtable<String, String> hashtable) {

    }
}
