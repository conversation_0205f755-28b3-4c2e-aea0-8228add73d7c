package com.bbpos.wiseapp.tms.location;

import android.Manifest;
import android.app.AlarmManager;
import android.app.PendingIntent;
import android.bluetooth.BluetoothAdapter;
import android.bluetooth.le.BluetoothLeScanner;
import android.bluetooth.le.ScanCallback;
import android.bluetooth.le.ScanFilter;
import android.bluetooth.le.ScanRecord;
import android.bluetooth.le.ScanResult;
import android.bluetooth.le.ScanSettings;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import android.content.pm.PackageManager;
import android.location.Criteria;
import android.location.GnssStatus;
import android.location.GpsSatellite;
import android.location.GpsStatus;
import android.location.Location;
import android.location.LocationListener;
import android.location.LocationManager;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.preference.PreferenceManager;
import android.text.TextUtils;
import android.util.SparseArray;
import android.widget.Toast;

import androidx.core.app.ActivityCompat;

import com.bbpos.wiseapp.activity.LockScreenActivity;
import com.bbpos.wiseapp.logger.BBLog;
import com.bbpos.wiseapp.service.common.RequestPermission;
import com.bbpos.wiseapp.service.common.UsualData;
import com.bbpos.wiseapp.service.receiver.CloudReceiver;
import com.bbpos.wiseapp.settings.utils.WirelessUtil;
import com.bbpos.wiseapp.system.api.DeviceInfoApi;
import com.bbpos.wiseapp.system.api.SystemApi;
import com.bbpos.wiseapp.tms.model.AppInfo;
import com.bbpos.wiseapp.tms.receiver.TmsReceiver;
import com.bbpos.wiseapp.tms.timer.PollTimer;
import com.bbpos.wiseapp.tms.utils.BroadcastActions;
import com.bbpos.wiseapp.tms.utils.Constants;
import com.bbpos.wiseapp.tms.utils.ContextUtil;
import com.bbpos.wiseapp.tms.utils.DateTimeUtils;
import com.bbpos.wiseapp.tms.utils.Helpers;
import com.bbpos.wiseapp.tms.utils.ParameterName;
import com.bbpos.wiseapp.tms.utils.SPKeys;
import com.bbpos.wiseapp.utils.SharedPreferencesUtils;
import com.bbpos.wiseapp.websocket.Constant;
import com.bbpos.wiseapp.websocket.WebSocketCenter;
import com.bbpos.wiseapp.websocket.WebSocketReceiver;
import com.bbpos.wiseapp.websocket.WebSocketSender;
import com.bbpos.wiseapp.websocket.handler.GeoProfileHandler;
import com.bbpos.wiseapp.websocket.handler.WifiProfileHandler;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Locale;

public class GpsLocationManager {
	private static final String TAG = "GPSLocation";
	private static final double DEFAULTVALUE = -999.0;
	private static double latitude = -999.0;
	private static double longitude = -999.0;
	private static boolean isGpsValid = false;
	private static LocationListener mListener = null;
	private static LocationManager locationManager = null;

	//********provisioning配置参数******************
	public static boolean isCareGPS = true;//是否关注定位信息
	private static long updatesTimeCalc;// 实际位置更新之间的最小时间间隔
	private static float updatesDistanceCalc; //实际配置位置更新之间的最小距离
	private static long updatesTime;// provisioning配置位置更新之间的最小时间间隔
	private static float updatesDistance;// provisioning配置位置更新之间的最小距离
	private static long scheduleTime;// gps 定时触发间隔时间，仅在 gps_care为0时生效
	private static long maxLocateTimeout;// gps定位超时时间,仅在GPS_care为0时生效
	public static float validErrorDistance = 500;// geofence 允许GPS定位的误差距离；
	private static final long TIMEOUT = 5 * 60;//定位超时时间，当isCareGPS为true时，被启用
	private static final long DEFAULT_SCHEDULE_TIME = 6 * 60 * 60;//当 isCareGPS 为false时，在每次定位结束后需要开启闹钟，设置下一次获取gps的时间，单位:秒
	public static final float DEFAULT_DISTANCE = 10L;//默认更新距离
	public static final long DEFAULT_TIME = 60;//默认刷新位置信息的时间。单位：s

	public static final int IN_ZONE = 1;
	public static final int OUT_OF_ZONE = 2;
	public static final int LOCK_SCREEN = 3;
	public static final int WIPE_DATA = 4;
	public static final int ROAMING = 5;

	public static final int WARNING_STATUS_IN_FENCE = 1;
	public static final int WARNING_STATUS_OUT_OF_FENCE = 2;
	public static final int WARNING_STATUS_OUT_OF_FENCE_AT_REBOOT = 3;

	public static boolean B_GEO_OUT_OF_FENCE = true;
	public static boolean GEO_STATUS = false;
	public static boolean GEO_LOCK_STATUS = true;
	public static int GEO_LOCK_METER = 200;
	public static int GEO_LOCK_MINS = 5;
	public static boolean GEO_WIPE_STATUS = false;
	public static int GEO_WIPE_MINS = 5;

	public static boolean GEO_IS_NEAR_BEACON = false;
	public static int SCAN_MODE = 1;	//蓝牙常开模式

	public static double last_distance = 0L;
    public static boolean first_location = true;
	public static int satellite_count = 0;
	public static int satellite_count_in_used = 0;
	public static float satellite_average_snr = 0f;

	public static boolean isLocate = true;

	public static boolean isGpsValid() {
		return isGpsValid;
	}

	public static String getLatitudeStr() {
		return String.valueOf(latitude);
	}

	public static String getLongitudeStr() {
		return String.valueOf(longitude);
	}

	/**return null if no gps*/
	public static JSONObject getGpsLocation() {
		JSONObject tower = new JSONObject();
		try {
			if (!Helpers.isOnline(ContextUtil.getInstance())) {
				BBLog.i(BBLog.TAG, "GpsLocationManager, network unavailable");
				tower.put(ParameterName.longitude, "" + -999);
				tower.put(ParameterName.latitude, "" + -999);
				return tower;
			}

			/*
			// 无效的gps信息不上送
			if (isGpsValid == false) {
				tower.put("longitude", "" + -999);
				tower.put("latitude", "" + -999);
				return tower;
			}
			// gps非法信息值不上送（经度有效取值范围为-180~180，纬度有效取值范围为-90~90）
			if((latitude == -999) || (longitude == -999))
			{
				tower.put("longitude", "" + -999);
				tower.put("latitude", "" + -999);
				return tower;
		    }

			BBLog.i(BBLog.TAG, "getGPSLocation");
			*/

			boolean isGetLocationInfo = isCareGPS || (!isCareGPS && isLocate);
			if (isGetLocationInfo) {
				boolean isNetworkProviderEnabled = getLocationManager().isProviderEnabled(LocationManager.NETWORK_PROVIDER);
				boolean isGpsProviderEnabled = getLocationManager().isProviderEnabled(LocationManager.GPS_PROVIDER);
				BBLog.i(BBLog.TAG, "GpsLocationManager, getGpsLocation: isNetworkProviderEnabled = " + isNetworkProviderEnabled + ", isGpsProviderEnabled = " + isGpsProviderEnabled);
				if (isNetworkProviderEnabled) {
//					BBLog.i(BBLog.TAG, "GpsLocationManager, getGpsLocation: 支持 NETWORK_PROVIDER");
					if (!getLocation(LocationManager.NETWORK_PROVIDER)) {
						if (isGpsProviderEnabled) {
							getLocation(LocationManager.GPS_PROVIDER);
						}
					}
				} else {
//					BBLog.i(BBLog.TAG, "GpsLocationManager, getGpsLocation: 不支持 NETWORK_PROVIDER");
					if (isGpsProviderEnabled) {
						getLocation(LocationManager.GPS_PROVIDER);
					}
				}
			}

			tower.put(ParameterName.longitude, "" + longitude);
			tower.put(ParameterName.latitude, "" + latitude);
			String latitudeStr = SharedPreferencesUtils.getSharePreferencesValue(UsualData.SHARED_PREFERENCES_GPS_STORE_LATITUDE, "");
			String longitudeStr = SharedPreferencesUtils.getSharePreferencesValue(UsualData.SHARED_PREFERENCES_GPS_STORE_LONGITUDE, "");
			if (!TextUtils.isEmpty(latitudeStr) && !TextUtils.isEmpty(longitudeStr)) {
				float[] result = new float[1];
				Location.distanceBetween(Double.valueOf(latitudeStr), Double.valueOf(longitudeStr), latitude, longitude, result);
				tower.put(ParameterName.distance, "" + result[0]);
			}
			tower.put(ParameterName.lockStatus, Constants.M_GEOFENCE_STATUS);

			String curProfile = SharedPreferencesUtils.getSharePreferencesValue(UsualData.SHARED_PREFERENCES_GPS_GEOFENCE_CUR_PROFILE, "");
			if (!TextUtils.isEmpty(curProfile)) {
				JSONObject profile = new JSONObject(curProfile);
				tower.put(ParameterName.proId, profile.getString(ParameterName.proId));
				tower.put(ParameterName.proName, profile.getString(ParameterName.proName));
			}

			tower.put("satelliteCount", satellite_count);
			tower.put("satelliteCountInUsed", satellite_count_in_used);
			tower.put("satelliteAverSNR", String.format(Locale.US, "%.2f", satellite_average_snr));
			tower.put(ParameterName.lockMeter, GpsLocationManager.GEO_LOCK_METER);
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		return tower;
	}

	public static LocationManager getLocationManager() {
		if (locationManager == null) {
			synchronized (GpsLocationManager.class) {
				locationManager = (LocationManager) ContextUtil.getInstance().getSystemService(Context.LOCATION_SERVICE);
			}
		}

		return locationManager;
	}

	/**
	 * GPS获取定位方式
	 */
	public static Location getGPSLocation(Context context) {
		Location location = null;
		LocationManager manager = getLocationManager();
		//高版本的权限检查
		if (ActivityCompat.checkSelfPermission(context, Manifest.permission.ACCESS_FINE_LOCATION) != PackageManager.PERMISSION_GRANTED) {
			return null;
		}
		if (manager.isProviderEnabled(LocationManager.GPS_PROVIDER)) {//是否支持GPS定位
			//获取最后的GPS定位信息，如果是第一次打开，一般会拿不到定位信息，一般可以请求监听，在有效的时间范围可以获取定位信息
			location = manager.getLastKnownLocation(LocationManager.GPS_PROVIDER);
		}

		return location;
	}

	/**
	 * network获取定位方式
	 */
	public static Location getNetWorkLocation(Context context) {
		Location location = null;
		LocationManager manager = getLocationManager();
		//高版本的权限检查
		if (ActivityCompat.checkSelfPermission(context, Manifest.permission.ACCESS_COARSE_LOCATION) != PackageManager.PERMISSION_GRANTED) {
			return null;
		}
		if (manager.isProviderEnabled(LocationManager.NETWORK_PROVIDER)) {//是否支持Network定位
			//获取最后的network定位信息
			BBLog.i(BBLog.TAG, "LocationManager.NETWORK_PROVIDER");
			location = manager.getLastKnownLocation(LocationManager.NETWORK_PROVIDER);
		}
		return location;
	}

	private static boolean getLocation(String provider) {
		if (getLocationManager().isProviderEnabled(provider)) {
			if (ActivityCompat.checkSelfPermission(ContextUtil.getInstance(), Manifest.permission.ACCESS_FINE_LOCATION) != PackageManager.PERMISSION_GRANTED && ActivityCompat.checkSelfPermission(ContextUtil.getInstance(), Manifest.permission.ACCESS_COARSE_LOCATION) != PackageManager.PERMISSION_GRANTED) {
				// TODO: Consider calling
				//    ActivityCompat#requestPermissions
				// here to request the missing permissions, and then overriding
				//   public void onRequestPermissionsResult(int requestCode, String[] permissions,
				//                                          int[] grantResults)
				// to handle the case where the user grants the permission. See the documentation
				// for ActivityCompat#requestPermissions for more details.
				return false;
			}
			Location location = getLocationManager().getLastKnownLocation(provider);
			if (location != null) {
				BBLog.d(BBLog.TAG, "GpsLocationManager, getLocation: provider=" + provider + "#########getLastKnownLocation#########latitude:" + location.getLatitude() + "   longitude:" + location.getLongitude());
				latitude = location.getLatitude();
				longitude = location.getLongitude();
				return true;
			} else {
				BBLog.d(BBLog.TAG, "GpsLocationManager, getLocation: ################################" + provider + " getLastKnownLocation is null");
			}
		} else {
			BBLog.d(BBLog.TAG, "GpsLocationManager, getLocation: provider=" + provider + " is disabled");
		}

		return false;
	}

	/**
	 * 获取最好的定位方式
	 */
	public static Location getBestLocation(Context context, Criteria criteria) {
		Location location;
		LocationManager manager = getLocationManager();
		if (criteria == null) {
			criteria = new Criteria();
		}
		String provider = manager.getBestProvider(criteria, true);
		if (TextUtils.isEmpty(provider)) {
			//如果找不到最适合的定位，使用network定位
			location = getNetWorkLocation(context);
		} else {
			//高版本的权限检查
			if (ActivityCompat.checkSelfPermission(context, Manifest.permission.ACCESS_COARSE_LOCATION) != PackageManager.PERMISSION_GRANTED
					&& ActivityCompat.checkSelfPermission(context, Manifest.permission.ACCESS_FINE_LOCATION) != PackageManager.PERMISSION_GRANTED) {
				return null;
			}
			//获取最适合的定位方式的最后的定位权限
			location = manager.getLastKnownLocation(provider);
		}
		return location;
	}


	public static void registLocationChangeListener() {
		if (!DeviceInfoApi.getIntance().isWisePosPro() && !Helpers.isOnline(ContextUtil.getInstance())){
			BBLog.i(TAG, "GpsLocationManager, netword unavailable");
			return;
		}
		BBLog.e(TAG, "GpsLocationManager, registLocationChangeListener");
		isCareGPS = "1".equals(SharedPreferencesUtils.getSharePreferencesValue(UsualData.SHARED_PREFERENCES_ISCAREGPS,"0"));
		updatesDistance = Float.valueOf(SharedPreferencesUtils.getSharePreferencesValue(UsualData.SHARED_PREFERENCES_GPS_UPDATE_DISTANCE ,DEFAULT_DISTANCE+""));
		updatesTime = Long.valueOf(SharedPreferencesUtils.getSharePreferencesValue(UsualData.SHARED_PREFERENCES_GPS_UPDATE_TIME ,+DEFAULT_TIME+""));
		scheduleTime = Long.valueOf(SharedPreferencesUtils.getSharePreferencesValue(UsualData.SHARED_PREFERENCES_GPS_UPDATE_SCHEDULETIME ,+DEFAULT_SCHEDULE_TIME+""));
		maxLocateTimeout = Long.valueOf(SharedPreferencesUtils.getSharePreferencesValue(UsualData.SHARED_PREFERENCES_GPS_UPDATE_MAXLOCATETIME ,+TIMEOUT+""));
		BBLog.e(TAG, "GpsLocationManager, registLocationChangeListener: isCareGPS = " + isCareGPS +", updatesDistance = "+
				updatesDistance + " ,updatesTime = "+ updatesTime + " , scheduleTime = "+ scheduleTime + ", maxLocateTimeout = "+ maxLocateTimeout);
		isLocate = true;
		BBLog.d(TAG, "GpsLocationManager, registLocationChangeListener: isLocate = " + isLocate);
		if (!isCareGPS){//终端不care gps信息，则默认5分钟超时，超时或获取到位置信息后立即释放资源
			new Handler(Looper.getMainLooper()).postDelayed(new Runnable() {
				@Override
				public void run() {
					unregistLocationChangeListener();
				}
			},maxLocateTimeout * 1000);
		}

		if (mListener == null) {
			mListener = new LocationListener() {
				@Override
				public void onLocationChanged(Location location) {
					if (location != null) {
//						Toast.makeText(ContextUtil.getInstance().getApplicationContext(),
//								"onLocationChanged latitude=" + location.getLatitude() + "  longitude=" + location.getLongitude(), Toast.LENGTH_LONG).show();
						BBLog.i(BBLog.TAG, "GpsLocationManager, registLocationChangeListener: onLocationChanged latitude=" + location.getLatitude() + "  longitude=" + location.getLongitude());
						BBLog.i(BBLog.TAG, "GpsLocationManager, registLocationChangeListener: CONFIG updateTime=" + updatesTimeCalc + "  updateDistance=" + updatesDistanceCalc);
						BBLog.i(BBLog.TAG, "GpsLocationManager, location.getAccuracy = " + location.getAccuracy());
						latitude = location.getLatitude();
						longitude = location.getLongitude();
						isGpsValid = true;

						if (GEO_STATUS) {
							boolean check_geofence = true;
							if (Constants.M_GEOFENCE_SET_SERVICE_LAUNCHER) {
								if (DeviceInfoApi.getIntance().isWisePosPro()
										&& (!WebSocketReceiver.getLoginSuccessFlag() || Constants.B_UNBOX_RUNNING) && !"6".equals(SharedPreferencesUtils.getSharePreferencesValue(UsualData.SHARED_UNBOX_STATUS, ""))) {
									BBLog.i(BBLog.TAG, "Device not login, not actived，ignore");
									showGPSToastMsg("Device is not activated");
									check_geofence = false;
								}
							} else {
								if (DeviceInfoApi.getIntance().isWisePosPro()
										&& (!WebSocketReceiver.getLoginSuccessFlag() || (Constants.B_UNBOX_RUNNING && Constants.B_UNBOX_RUNNING_LOCK)) && !"6".equals(SharedPreferencesUtils.getSharePreferencesValue(UsualData.SHARED_UNBOX_STATUS, ""))) {
									BBLog.i(BBLog.TAG, "Device not login, not actived，ignore");
									showGPSToastMsg("Device is not activated");
									check_geofence = false;
								}
							}

							if (check_geofence) {
								try {
									String latitudeStr = SharedPreferencesUtils.getSharePreferencesValue(UsualData.SHARED_PREFERENCES_GPS_STORE_LATITUDE, "");
									String longitudeStr = SharedPreferencesUtils.getSharePreferencesValue(UsualData.SHARED_PREFERENCES_GPS_STORE_LONGITUDE, "");
									if (!TextUtils.isEmpty(latitudeStr) && !TextUtils.isEmpty(longitudeStr)) {
										float[] result = new float[1];
										BBLog.e(BBLog.TAG, "**** distanceBetween: CENTERN longitude=" + longitudeStr + " latitude=" + latitudeStr);
										BBLog.e(BBLog.TAG, "**** distanceBetween: CURRENT longitude=" + longitude + " latitude=" + latitude);
										Location.distanceBetween(Double.valueOf(latitudeStr), Double.valueOf(longitudeStr), latitude, longitude, result);

										BBLog.e(BBLog.TAG, "**** distanceBetween: " + result[0] + "  Radius = " + GEO_LOCK_METER);
										showGPSToastMsg("**** distanceBetween: CENTERN longitude=" + longitudeStr + " latitude=" + latitudeStr +
												"\n**** distanceBetween: CURRENT longitude=" + longitude + " latitude=" + latitude +
												"\n**** distanceBetween: " + result[0] + "  Radius = " + GEO_LOCK_METER  + "  LastDistance = " + last_distance
												+ "  ErrorDistance = " + validErrorDistance);
										double distacne = result[0];
										if (!first_location && Math.abs(distacne-last_distance)>validErrorDistance) {
											BBLog.e(BBLog.TAG, "compare with last distance(" + last_distance + ") ，if longer than " + validErrorDistance + "，ignore");
											last_distance = distacne;
											return;
										} else {
											last_distance = distacne;
										}
										if (first_location) {
											first_location = false;
											BBLog.e(BBLog.TAG, "ignore the first location");
											return;	//針對在reboot 後, 在未連上wifi 時候之前, 多台終端出現out of zone 的情況，ignore the first Location
										}
										if (distacne > GEO_LOCK_METER) {
											BBLog.e(BBLog.TAG, "Constants.M_GEOFENCE_STATUS = " + Constants.M_GEOFENCE_STATUS);
											if (GpsLocationManager.IN_ZONE==Constants.M_GEOFENCE_STATUS) {
												if (!conditionEnterGeoFence()) {
													showOutOfGeofenceWarning(WARNING_STATUS_IN_FENCE);
													setCurrentGPSStatus(GpsLocationManager.OUT_OF_ZONE);
													registLocationChangeListenerEx();
													B_GEO_OUT_OF_FENCE = true;
												}
											} else if (GpsLocationManager.OUT_OF_ZONE == Constants.M_GEOFENCE_STATUS) {
												if (conditionEnterGeoFence()) {
													//查看終端的IP address, 如果不變和unbox 時候IP 的一樣, 就算他是in fence
													closeOutOfGeofenceWarning();
													setCurrentGPSStatus(GpsLocationManager.IN_ZONE);
												} else {
													showOutOfGeofenceWarning(WARNING_STATUS_OUT_OF_FENCE);
												}
											} else if (Constants.M_GEOFENCE_STATUS == GpsLocationManager.LOCK_SCREEN || Constants.M_GEOFENCE_STATUS == GpsLocationManager.WIPE_DATA) {
												if (conditionEnterGeoFence()) {
													Intent intent = new Intent(UsualData.ACTION_ENTER_GEOFENCE);
													ContextUtil.getInstance().sendBroadcast(intent, RequestPermission.REQUEST_PERMISSION_MY_BROADCAST);
												} else {
													Intent intent = new Intent(UsualData.ACTION_OUT_OF_GEOFENCE);
													ContextUtil.getInstance().sendBroadcast(intent, RequestPermission.REQUEST_PERMISSION_MY_BROADCAST);
												}
											}
										} else {
											B_GEO_OUT_OF_FENCE = false;
											if (GpsLocationManager.OUT_OF_ZONE == Constants.M_GEOFENCE_STATUS) {
												setCurrentGPSStatus(GpsLocationManager.IN_ZONE);
												registLocationChangeListener();
												closeOutOfGeofenceWarning();
											} else if (GpsLocationManager.LOCK_SCREEN==Constants.M_GEOFENCE_STATUS || GpsLocationManager.WIPE_DATA==Constants.M_GEOFENCE_STATUS) {
												BBLog.e(BBLog.TAG, "Constants.STORE_SSID=" + Constants.STORE_SSID + "  Constants.STORE_IP=" + Constants.STORE_IP);
												BBLog.e(BBLog.TAG, "CurConnectSSID=" + WirelessUtil.getCurConnectSSID(ContextUtil.getInstance()) + "  CurConnectIP=" + WirelessUtil.getWifiIP(ContextUtil.getInstance()));
												if (WebSocketCenter.isWebSocketConnected) {
													Intent intent = new Intent(UsualData.ACTION_ENTER_GEOFENCE);
													ContextUtil.getInstance().sendBroadcast(intent, RequestPermission.REQUEST_PERMISSION_MY_BROADCAST);
												} else {
													if (!WebSocketCenter.isWebSocketConnected) {
														showGPSToastMsg("Enter Fence! Devide is offline,ignore");
														BBLog.e(BBLog.TAG, "Enter Fence! Devide is offline,ignore");
													}
												}
											}
										}
									} else {
										showGPSToastMsg("GEO Fence is not ready");
									}
								} catch (Exception e) {
									e.printStackTrace();
								}
							}
						}

						new Handler(Looper.getMainLooper()).postDelayed(new Runnable() {
							@Override
							public void run() {
								BBLog.e(BBLog.TAG, "onLocation SatelliteStatusChanged COUNT = " + satellite_count
										+ " COUNT_IN_USED = " + satellite_count_in_used
										+ " AVER_SNR = " + satellite_average_snr);
								WebSocketSender.C0903_DataInfoUploadForGeo(System.currentTimeMillis());
							}
						}, 500);

						//对不关注gps的终端，当获取到位置信息时，需要进行解绑
						if (!isCareGPS && latitude != DEFAULTVALUE && longitude != DEFAULTVALUE) {
							unregistLocationChangeListener();
						}
					} else {
						isGpsValid = false;
					}
				}

				@Override
				public void onProviderDisabled(String arg0) {
					isGpsValid = false;
				}

				@Override
				public void onProviderEnabled(String arg0) {

				}

				@Override
				public void onStatusChanged(String arg0, int arg1, Bundle arg2) {

				}

			};

			//获取经纬度间隔 30分钟 或移动50米
			BBLog.w(TAG, "GpsLocationManager, registLocationChangeListener: start GPS locate request");
			if (ActivityCompat.checkSelfPermission(ContextUtil.getInstance(), Manifest.permission.ACCESS_FINE_LOCATION) != PackageManager.PERMISSION_GRANTED && ActivityCompat.checkSelfPermission(ContextUtil.getInstance(), Manifest.permission.ACCESS_COARSE_LOCATION) != PackageManager.PERMISSION_GRANTED) {
				// TODO: Consider calling
				//    ActivityCompat#requestPermissions
				// here to request the missing permissions, and then overriding
				//   public void onRequestPermissionsResult(int requestCode, String[] permissions,
				//                                          int[] grantResults)
				// to handle the case where the user grants the permission. See the documentation
				// for ActivityCompat#requestPermissions for more details.
				return;
			}

			long updatesTimsTemp = updatesTime * 5;
			float updatesDistanceTemp = updatesDistance;
			if (Constants.M_GEOFENCE_STATUS >= GpsLocationManager.OUT_OF_ZONE && Constants.M_GEOFENCE_STATUS <= GpsLocationManager.WIPE_DATA) {
				updatesTimsTemp = updatesTime;
				updatesDistanceTemp = 0;
			}
			BBLog.e(TAG, "GpsLocationManager, registLocationChangeListener updatesTime = " + updatesTimsTemp + "SECS， updatesDistance=" + updatesDistanceTemp + "METERS");
			updatesTimeCalc = updatesTimsTemp;
			updatesDistanceCalc = updatesDistanceTemp;
			getLocationManager().requestLocationUpdates(LocationManager.GPS_PROVIDER,
					updatesTimsTemp * 1000,
					updatesDistanceTemp,
					mListener, Looper.getMainLooper());
			if (getLocationManager().isProviderEnabled(LocationManager.NETWORK_PROVIDER)) {
				BBLog.w(TAG, "GpsLocationManager, registLocationChangeListener: support NETWORK PROVIDER");
				getLocationManager().requestLocationUpdates(LocationManager.NETWORK_PROVIDER,
						updatesTimsTemp * 1000,
						updatesDistanceTemp ,
						mListener, Looper.getMainLooper());
			}
		} else {
			//获取经纬度间隔 30分钟 或移动50米
			BBLog.w(TAG, "GpsLocationManager, registLocationChangeListener: start GPS locate request");
			if (ActivityCompat.checkSelfPermission(ContextUtil.getInstance(), Manifest.permission.ACCESS_FINE_LOCATION) != PackageManager.PERMISSION_GRANTED && ActivityCompat.checkSelfPermission(ContextUtil.getInstance(), Manifest.permission.ACCESS_COARSE_LOCATION) != PackageManager.PERMISSION_GRANTED) {
				// TODO: Consider calling
				//    ActivityCompat#requestPermissions
				// here to request the missing permissions, and then overriding
				//   public void onRequestPermissionsResult(int requestCode, String[] permissions,
				//                                          int[] grantResults)
				// to handle the case where the user grants the permission. See the documentation
				// for ActivityCompat#requestPermissions for more details.
				return;
			}
			long updatesTimsTemp = updatesTime * 5;
			float updatesDistanceTemp = updatesDistance;
			if (Constants.M_GEOFENCE_STATUS >= GpsLocationManager.OUT_OF_ZONE && Constants.M_GEOFENCE_STATUS <= GpsLocationManager.WIPE_DATA) {
				updatesTimsTemp = updatesTime;
				updatesDistanceTemp = 0;
			}
			BBLog.e(TAG, "GpsLocationManager, registLocationChangeListener updatesTime = " + updatesTimsTemp + "SECS， updatesDistance=" + updatesDistanceTemp + "METERS");
			updatesTimeCalc = updatesTimsTemp;
			updatesDistanceCalc = updatesDistanceTemp;
			if (getLocationManager().isProviderEnabled(LocationManager.GPS_PROVIDER)) {
				getLocationManager().requestLocationUpdates(LocationManager.GPS_PROVIDER,
						updatesTimsTemp * 1000,
						updatesDistanceTemp,
						mListener);
			}
			if (getLocationManager().isProviderEnabled(LocationManager.NETWORK_PROVIDER)) {
				getLocationManager().requestLocationUpdates(LocationManager.NETWORK_PROVIDER,
						updatesTimsTemp * 1000,
						updatesDistanceTemp,
						mListener);
			}
		}

		if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
			getLocationManager().registerGnssStatusCallback(mGnssStatusCB);
		}
	}

	private GpsStatus.Listener gpsStatusListener = new GpsStatus.Listener() {
		@Override
		public void onGpsStatusChanged(int event) {
			switch (event) {
				//卫星状态改变
				case GpsStatus.GPS_EVENT_SATELLITE_STATUS:
					//获取当前状态
					GpsStatus gpsStatus = getLocationManager().getGpsStatus(null);
					//获取卫星颗数的默认最大值
					int maxSatellites = gpsStatus.getMaxSatellites();
					//获取所有的卫星
					Iterator<GpsSatellite> iters = gpsStatus.getSatellites().iterator();
					//卫星颗数统计
					int count = 0;
					StringBuilder sb = new StringBuilder();
					while (iters.hasNext() && count <= maxSatellites) {
						count++;
						GpsSatellite s = iters.next();
						//卫星的信噪比
						float snr = s.getSnr();
						s.usedInFix();
						sb.append("NO.").append(count).append("：").append(snr).append("\n");
					}
					BBLog.e(TAG, sb.toString());
					break;
				default:
					break;
			}
		}
	};

	private static GnssStatus.Callback mGnssStatusCB= new GnssStatus.Callback() {
		@Override
		public void onStarted() {
			super.onStarted();
		}

		@Override
		public void onSatelliteStatusChanged(GnssStatus status) {
			super.onSatelliteStatusChanged(status);
			if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
				satellite_count = status.getSatelliteCount();
				int countInUsed = 0;
				float totalSNR = 0f;
				for (int i=0; i<satellite_count; i++) {
					if (status.usedInFix(i)) {
						countInUsed++;
						totalSNR += status.getCn0DbHz(i);
					}
				}
				satellite_count_in_used = countInUsed;
				satellite_average_snr = totalSNR/countInUsed;
			}
		}
	};

	private static void registLocationChangeListenerEx() {
		if (!DeviceInfoApi.getIntance().isWisePosPro() && !Helpers.isOnline(ContextUtil.getInstance())) {
			BBLog.i(TAG, "GpsLocationManager, netword unavailable");
			return;
		}
		BBLog.e(TAG, "GpsLocationManager, registLocationChangeListenerEx");
		isCareGPS = "1".equals(SharedPreferencesUtils.getSharePreferencesValue(UsualData.SHARED_PREFERENCES_ISCAREGPS, "0"));
		updatesDistance = Float.valueOf(SharedPreferencesUtils.getSharePreferencesValue(UsualData.SHARED_PREFERENCES_GPS_UPDATE_DISTANCE, DEFAULT_DISTANCE + ""));
		updatesTime = Long.valueOf(SharedPreferencesUtils.getSharePreferencesValue(UsualData.SHARED_PREFERENCES_GPS_UPDATE_TIME, +DEFAULT_TIME + ""));
		scheduleTime = Long.valueOf(SharedPreferencesUtils.getSharePreferencesValue(UsualData.SHARED_PREFERENCES_GPS_UPDATE_SCHEDULETIME, +DEFAULT_SCHEDULE_TIME + ""));
		maxLocateTimeout = Long.valueOf(SharedPreferencesUtils.getSharePreferencesValue(UsualData.SHARED_PREFERENCES_GPS_UPDATE_MAXLOCATETIME, +TIMEOUT + ""));
		BBLog.e(TAG, "GpsLocationManager, registLocationChangeListenerEx: isCareGPS = " + isCareGPS + ", updatesDistance = " +
				updatesDistance + " ,updatesTime = " + updatesTime + " , scheduleTime = " + scheduleTime + ", maxLocateTimeout = " + maxLocateTimeout);
		isLocate = true;
		BBLog.d(TAG, "GpsLocationManager, registLocationChangeListenerEx: isLocate = " + isLocate);
		if (!isCareGPS) {//终端不care gps信息，则默认5分钟超时，超时或获取到位置信息后立即释放资源
			new Handler(Looper.getMainLooper()).postDelayed(new Runnable() {
				@Override
				public void run() {
					unregistLocationChangeListener();
				}
			}, maxLocateTimeout * 1000);
		}

		if (mListener != null) {
			//获取经纬度间隔 30分钟 或移动50米
			BBLog.w(TAG, "GpsLocationManager, registLocationChangeListenerEx: start GPS locate request");
			if (ActivityCompat.checkSelfPermission(ContextUtil.getInstance(), Manifest.permission.ACCESS_FINE_LOCATION) != PackageManager.PERMISSION_GRANTED && ActivityCompat.checkSelfPermission(ContextUtil.getInstance(), Manifest.permission.ACCESS_COARSE_LOCATION) != PackageManager.PERMISSION_GRANTED) {
				// TODO: Consider calling
				//    ActivityCompat#requestPermissions
				// here to request the missing permissions, and then overriding
				//   public void onRequestPermissionsResult(int requestCode, String[] permissions,
				//                                          int[] grantResults)
				// to handle the case where the user grants the permission. See the documentation
				// for ActivityCompat#requestPermissions for more details.
				return;
			}
			BBLog.e(TAG, "GpsLocationManager, registLocationChangeListenerEx updatesTime = " + updatesTime + "SECS， updatesDistance=" + 0 + "METERS");
			updatesTimeCalc = updatesTime;
			updatesDistanceCalc = 0;
			if (getLocationManager().isProviderEnabled(LocationManager.GPS_PROVIDER)) {
				getLocationManager().requestLocationUpdates(LocationManager.GPS_PROVIDER,
						updatesTime * 1000,
						0,
						mListener);
			}
			if (getLocationManager().isProviderEnabled(LocationManager.NETWORK_PROVIDER)) {
				getLocationManager().requestLocationUpdates(LocationManager.NETWORK_PROVIDER,
						updatesTime * 1000,
						0,
						mListener);
			}
		}

		if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
			getLocationManager().registerGnssStatusCallback(mGnssStatusCB);
		}
	}

	public static void unregistLocationChangeListener() {
		BBLog.i(TAG, "GpsLocationManager, unregistLocationChangeListener");
		if (mListener != null) {
			getLocationManager().removeUpdates(mListener);
			mListener = null;
		}
		if (locationManager != null) {
			locationManager = null;
		}
		//释放资源后需要设置下次执行时间
		if (!isCareGPS){
			isLocate = false;
			setScheduleGPS();
		}
	}

	public static LocationListener getGpsListener() {
		if (mListener != null) {
			return mListener;
		}
		return null;
	}

	public static void setScheduleGPS(){
		BBLog.i(TAG, "GpsLocationManager, setScheduleGPS");

		Intent intentTmp = new Intent(BroadcastActions.GPS_SCHEDULE_TIME);
		if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
			intentTmp.setComponent(new ComponentName(ContextUtil.getInstance().getPackageName(), TmsReceiver.class.getName()));
		}
		PendingIntent pi = PendingIntent.getBroadcast(ContextUtil.getInstance(), 0, intentTmp, PendingIntent.FLAG_UPDATE_CURRENT);
		AlarmManager am = (AlarmManager) ContextUtil.getInstance().getSystemService(Context.ALARM_SERVICE);
		am.cancel(pi);

		long timeOnMillis = System.currentTimeMillis() + scheduleTime * 1000;
		if (Build.VERSION.SDK_INT < Build.VERSION_CODES.KITKAT) {
			am.set(AlarmManager.RTC_WAKEUP, timeOnMillis, pi);
		} else {
			am.setExact(AlarmManager.RTC_WAKEUP, timeOnMillis, pi);
		}
	}

	/*
	public static void setGpsSuccess() {
		latitude = 22.368819999999996;
		longitude = 114.11176500000002;
		isGpsValid = true;
	}
	*/

	public static void setCurrentGPSStatus(int status) {
		BBLog.e(TAG, "setCurrentGPSStatus = " + status);
		Constants.M_GEOFENCE_STATUS = status;
		SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_GPS_STATUS, Constants.M_GEOFENCE_STATUS+"");
	}

	public static final double DEGREES_TO_RADIANS = Math.PI / 180.0;
	public static final double RADIANS_TO_DEGREES = 180.0 / Math.PI;
	//地球半径
	public static final double EARTH_MEAN_RADIUS_KM = 6.371229*1e6;
	//地球直径
	private static final double EARTH_MEAN_DIAMETER = EARTH_MEAN_RADIUS_KM * 2;

	/***
	 * 距离半径计算方式
	 * @param latCenterRad  中心点经纬度
	 * @param lonCenterRad
	 * @param latVals  目标经纬度
	 * @param lonVals
	 * @return 两坐标的距离 单位千米
	 */
	public static double getDistance(double latCenterRad, double lonCenterRad, double latVals, double lonVals) {
		//计算经纬度
		double latRad = latVals * DEGREES_TO_RADIANS;
		double lonRad = lonVals * DEGREES_TO_RADIANS;

		//计算经纬度的差
		double diffX = latCenterRad * DEGREES_TO_RADIANS - latRad;
		double diffY = lonCenterRad * DEGREES_TO_RADIANS - lonRad;
		//计算正弦和余弦
		double hsinX = Math.sin(diffX * 0.5);
		double hsinY = Math.sin(diffY * 0.5);
		double latCenterRad_cos = Math.cos(latCenterRad * DEGREES_TO_RADIANS);
		double h = hsinX * hsinX + (latCenterRad_cos * Math.cos(latRad) * hsinY * hsinY);

		return (EARTH_MEAN_DIAMETER * Math.atan2(Math.sqrt(h), Math.sqrt(1 - h)));
	}

	public static void showOutOfGeofenceWarning(int status) {
		Intent intent = new Intent();
		intent.setAction(BroadcastActions.ACTION_GEOFENCING_DETECTED_WARNING);
		intent.putExtra("warning_status", status);
		intent.setComponent(new ComponentName(ContextUtil.getInstance().getPackageName(), CloudReceiver.class.getName()));
		ContextUtil.getInstance().sendBroadcast(intent, RequestPermission.REQUEST_PERMISSION_MY_BROADCAST);
	}

	public static void closeOutOfGeofenceWarning() {
		Intent intent = new Intent();
		intent.setAction(BroadcastActions.ACTION_GEOFENCING_DETECTED_WARNING_CLOSE);
		intent.setComponent(new ComponentName(ContextUtil.getInstance().getPackageName(), CloudReceiver.class.getName()));
		ContextUtil.getInstance().sendBroadcast(intent, RequestPermission.REQUEST_PERMISSION_MY_BROADCAST);
	}

	public static void showOutOfGeofenceLockScreen() {
		BBLog.e(BBLog.TAG, "Broadcast to start LockScreen Activity");
		Intent intent = new Intent();
		intent.setAction(BroadcastActions.ACTION_GEOFENCING_DETECTED_LOCK);
		intent.setComponent(new ComponentName(ContextUtil.getInstance().getPackageName(), CloudReceiver.class.getName()));
		ContextUtil.getInstance().sendBroadcast(intent, RequestPermission.REQUEST_PERMISSION_MY_BROADCAST);
	}

	public static void closeOutOfGeofenceLockScreen() {
		Intent intent = new Intent();
		intent.setAction(UsualData.ACTION_CLOSE_LOCKSCREEN);
		ContextUtil.getInstance().sendBroadcast(intent, RequestPermission.REQUEST_PERMISSION_MY_BROADCAST);
	}

	public static void gotoLockDeviceScreen() {
		BBLog.e(TAG, "gotoLockDeviceScreen GEO_LOCK_STATUS = " + GEO_LOCK_STATUS);
		if (GEO_LOCK_STATUS) {
			Intent it = new Intent(ContextUtil.getInstance(), LockScreenActivity.class);
			it.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
			ContextUtil.getInstance().startActivity(it);
		}
	}

	public static void gotoLockDeviceScreenInput() {
		BBLog.e(TAG, "gotoLockDeviceScreen GEO_LOCK_STATUS = " + GEO_LOCK_STATUS);
		if (GEO_LOCK_STATUS) {
			Intent it = new Intent(ContextUtil.getInstance(), LockScreenActivity.class);
			it.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
			it.putExtra("mode", "1");
			ContextUtil.getInstance().startActivity(it);
		}
	}

	public static void executeGeoProfile(JSONObject geoInfo) {
		try {
			BBLog.e(TAG, "executeGeoProfile = " + geoInfo);
			if (geoInfo == null) {
				GEO_STATUS = false;
				SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_GPS_GEOFENCE_CUR_PROFILE, "");
				return;
			}
			GEO_STATUS = true;

			String curProfile = SharedPreferencesUtils.getSharePreferencesValue(UsualData.SHARED_PREFERENCES_GPS_GEOFENCE_CUR_PROFILE, "");
			if (!TextUtils.isEmpty(curProfile)) {
				String curProfileID = new JSONObject(curProfile).getString(ParameterName.proId);
				boolean disableTag = "1".equals(geoInfo.optString(ParameterName.disableTag)) ? true : false;
				if (disableTag) {
					if (curProfileID.equals(geoInfo.getString(ParameterName.proId))) {
						executeGeoProfile(GeoProfileHandler.getDefaultGeoProfile());
						return;
					} else {
						//如果disable的不是当前执行的，则直接放弃
						BBLog.e(TAG, "disable ID is not current executing ID，ignore " + geoInfo);
						return;
					}
				}
			}

			boolean roamingTag = "1".equals(geoInfo.optString(ParameterName.roamingTag)) ? true : false;
			if (roamingTag) {
				GEO_STATUS = false;
				if (Constants.M_GEOFENCE_STATUS>=LOCK_SCREEN || Constants.M_GEOFENCE_STATUS>=WIPE_DATA) {
					closeOutOfGeofenceLockScreen();
				} else if (Constants.M_GEOFENCE_STATUS == OUT_OF_ZONE) {
					closeOutOfGeofenceWarning();
					closeOutOfGeofenceLockScreen();
				}
				setCurrentGPSStatus(ROAMING);
			} else {
				if (Constants.M_GEOFENCE_STATUS == ROAMING) {
					setCurrentGPSStatus(IN_ZONE);
				}
			}

			String lockMeters = geoInfo.optString(ParameterName.lockMeter);
			if (!TextUtils.isEmpty(lockMeters)) {
				GpsLocationManager.GEO_LOCK_METER = Integer.valueOf(lockMeters);
			}
			String lockMins = geoInfo.optString(ParameterName.lockMin);
			if (!TextUtils.isEmpty(lockMins)) {
				if (GpsLocationManager.GEO_LOCK_MINS != Integer.valueOf(lockMins)) {
					GpsLocationManager.GEO_LOCK_MINS = Integer.valueOf(lockMins);
					if (GpsLocationManager.OUT_OF_ZONE == Constants.M_GEOFENCE_STATUS) {
						Intent intent = new Intent();
						intent.setAction(BroadcastActions.ACTION_GEOFENCING_RESET_LOCKSCREEN_TIMER);
						intent.setComponent(new ComponentName(ContextUtil.getInstance().getPackageName(), CloudReceiver.class.getName()));
						ContextUtil.getInstance().sendBroadcast(intent, RequestPermission.REQUEST_PERMISSION_MY_BROADCAST);
					}
				}
			}

			boolean wipeData = "1".equals(geoInfo.optString(ParameterName.wipeStatus)) ? true : false;
			String wipeMins = geoInfo.optString(ParameterName.wipeMin);
			int nWipeMins = -1;
			if (!TextUtils.isEmpty(wipeMins)) {
				nWipeMins = Integer.valueOf(wipeMins);
			}
			if (Constants.M_GEOFENCE_STATUS==LOCK_SCREEN) {
				if (GpsLocationManager.GEO_WIPE_STATUS!=wipeData || (nWipeMins>0 && GpsLocationManager.GEO_WIPE_MINS!=nWipeMins)) {
					Intent intent = new Intent(UsualData.ACTION_WIPE_PARAM_CHANGE);
					intent.putExtra("GEO_WIPE_STATUS", wipeData);
					intent.putExtra("GEO_WIPE_MINS", nWipeMins);
					ContextUtil.getInstance().sendBroadcast(intent, RequestPermission.REQUEST_PERMISSION_MY_BROADCAST);

					SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_GPS_GEOFENCE_LOCK_METERS,
							geoInfo.optString(ParameterName.lockMeter));
					SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_GPS_GEOFENCE_LOCK_MINS,
							geoInfo.optString(ParameterName.lockMin));
				}
			} else {
				GpsLocationManager.GEO_WIPE_STATUS = wipeData;
				GpsLocationManager.GEO_WIPE_MINS = nWipeMins;

				SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_GPS_GEOFENCE_LOCK_METERS,
						geoInfo.optString(ParameterName.lockMeter));
				SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_GPS_GEOFENCE_LOCK_MINS,
						geoInfo.optString(ParameterName.lockMin));
				SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_GPS_GEOFENCE_WIPE_STATUS,
						geoInfo.optString(ParameterName.wipeStatus));
				SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_GPS_GEOFENCE_WIPE_MINS,
						geoInfo.optString(ParameterName.wipeMin));
			}

			SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_GPS_GEOFENCE_CUR_PROFILE, geoInfo.toString());
			WebSocketSender.C0903_DataInfoUploadForGeo(System.currentTimeMillis());
			BBLog.e(TAG, "GpsLocationManager.GEO_STATUS = " + GpsLocationManager.GEO_STATUS);
		} catch (JSONException e) {
			e.printStackTrace();
		}
	}

	public static void executeGeoProfileForInit(JSONObject geoInfo) {
		BBLog.e(TAG, "executeGeoProfileForInit = " + geoInfo);
		if (geoInfo == null) {
			GEO_STATUS = false;
			SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_GPS_GEOFENCE_CUR_PROFILE, "");
			return;
		}
		GEO_STATUS = true;

		//executeGeoProfileForInit 执行的就是current，如果是disable，直接执行default
		boolean disableTag = "1".equals(geoInfo.optString(ParameterName.disableTag)) ? true : false;
		if (disableTag) {
			executeGeoProfile(GeoProfileHandler.getDefaultGeoProfile());
			return;
		}

		boolean roamingTag = "1".equals(geoInfo.optString(ParameterName.roamingTag)) ? true : false;
		if (roamingTag) {
			GEO_STATUS = false;
			if (Constants.M_GEOFENCE_STATUS>=LOCK_SCREEN) {
				closeOutOfGeofenceLockScreen();
			}
			setCurrentGPSStatus(ROAMING);
		} else {
			if (Constants.M_GEOFENCE_STATUS == ROAMING) {
				setCurrentGPSStatus(IN_ZONE);
			}
		}

		String lockMeters = geoInfo.optString(ParameterName.lockMeter);
		if (!TextUtils.isEmpty(lockMeters)) {
			GpsLocationManager.GEO_LOCK_METER = Integer.valueOf(lockMeters);
		}
		String lockMins = geoInfo.optString(ParameterName.lockMin);
		if (!TextUtils.isEmpty(lockMins)) {
			GpsLocationManager.GEO_LOCK_MINS = Integer.valueOf(lockMins);
		}

		boolean wipeData = "1".equals(geoInfo.optString(ParameterName.wipeStatus)) ? true : false;
		String wipeMins = geoInfo.optString(ParameterName.wipeMin);
		int nWipeMins = -1;
		if (!TextUtils.isEmpty(wipeMins)) {
			nWipeMins = Integer.valueOf(wipeMins);
		}

		GpsLocationManager.GEO_WIPE_STATUS = wipeData;
		GpsLocationManager.GEO_WIPE_MINS = nWipeMins;

		SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_GPS_GEOFENCE_LOCK_METERS,
				geoInfo.optString(ParameterName.lockMeter));
		SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_GPS_GEOFENCE_LOCK_MINS,
				geoInfo.optString(ParameterName.lockMin));
		SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_GPS_GEOFENCE_WIPE_STATUS,
				geoInfo.optString(ParameterName.wipeStatus));
		SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_GPS_GEOFENCE_WIPE_MINS,
				geoInfo.optString(ParameterName.wipeMin));
	}

	public static void initGeoProfile(JSONObject geoInfo) {
		BBLog.e(TAG, "initGeoProfile = " + geoInfo);
		if (geoInfo == null) {
			GEO_STATUS = false;
			SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_GPS_GEOFENCE_CUR_PROFILE, "");
			return;
		}
		try {
			String beginDateStr = geoInfo.getString(ParameterName.beginDate);
			String endDateStr = geoInfo.getString(ParameterName.endDate);
			long nowTime = System.currentTimeMillis();
			long begTime;
			long endTime;
			if (DateTimeUtils.isNumeric(beginDateStr) && DateTimeUtils.isNumeric(endDateStr)) {
				begTime = new Long(beginDateStr).longValue();
				endTime = new Long(endDateStr).longValue();
			} else {
				SimpleDateFormat sdf = new SimpleDateFormat(Constants.dateFormat);
				begTime = sdf.parse(beginDateStr).getTime();
				endTime = sdf.parse(endDateStr).getTime();
			}

			if (begTime < nowTime && endTime > nowTime) {
				GpsLocationManager.executeGeoProfileForInit(geoInfo);
				return;
			} else if (nowTime > endTime) {
				SharedPreferences sp = PreferenceManager.getDefaultSharedPreferences(ContextUtil.getInstance());
				String localGeoProfileListStr = sp.getString(SPKeys.WEBSOCKET_GEOFENCE_PROFILE_LIST, "");
				//如果是當前執行的profile，則直接執行default
				BBLog.d(Constants.TAG, "LOCAL: localGeoProfileListStr  -->"+localGeoProfileListStr);
				if (!Helpers.isStrNoEmpty(localGeoProfileListStr)) {
					BBLog.d(Constants.TAG, "LOCAL isn't emtpy，loop to delete");
					JSONArray localProfileList = new JSONArray(localGeoProfileListStr);
					for (int i = 0; i < localProfileList.length(); i++) {
						JSONObject profileJsonObj = (JSONObject) localProfileList.get(i);
						if ("1".equals(profileJsonObj.getString(ParameterName.isDefault))) {
							GpsLocationManager.executeGeoProfileForInit(profileJsonObj);
							return;
						}
					}
					//如果找不到default，则没有可执行的
					GpsLocationManager.executeGeoProfileForInit(null);
				}
			}
		} catch (JSONException e) {
			e.printStackTrace();
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	public static void shutDownGeoFenceDetect() {
		GEO_STATUS = false;
		closeOutOfGeofenceWarning();
		closeOutOfGeofenceLockScreen();
		setCurrentGPSStatus(IN_ZONE);
	}

	private static void showGPSToastMsg(String msg) {
		if (ContextUtil.isApkInDebug()) {
			new Handler(Looper.getMainLooper()).post(new Runnable() {
				@Override
				public void run() {
					Toast.makeText(ContextUtil.getInstance().getApplicationContext(), msg, Toast.LENGTH_LONG).show();
				}
			});
		}
	}

	public static boolean conditionEnterGeoFence() {

		if(isWifiEnterGeofence())
			return true;

		if (GEO_IS_NEAR_BEACON) {
			BBLog.d(Constants.TAG, "conditionEnterGeoFence Beacon detected");
			return true;
		}

		return false;
	}

	public static boolean isWifiEnterGeofence() {
		String curIP = WirelessUtil.getWifiIP(ContextUtil.getInstance());
		String curSSID = WirelessUtil.getCurConnectSSID(ContextUtil.getInstance());
		BBLog.d(TAG, "conditionEnterGeoFence curIP=" + curIP + " curSSID=" + curSSID);
		BBLog.d(TAG, "conditionEnterGeoFence Constants.STORE_IP=" + Constants.STORE_IP);
		if (!TextUtils.isEmpty(Constants.STORE_IP)) {
			if (!TextUtils.isEmpty(curIP) && Helpers.isMachedIP(curIP, Constants.STORE_IP)) {
				BBLog.d(TAG, "conditionEnterGeoFence IP matched");
				return true;
			}
		} else {
			if (!TextUtils.isEmpty(curSSID) && WifiProfileHandler.checkIsWifiProfileSSID(curSSID)) {
				BBLog.d(TAG, "conditionEnterGeoFence SSID matched");
				return true;
			}
		}

		return false;
	}

	////////////////////////////////////////
	private static BluetoothLeScanner mBLEScanner;
	private static int resultCount = 0;
	private static List<String> list_uuid = new ArrayList<>();
	private static ScanCallback mScanCB = new ScanCallback() {
		@Override
		public void onScanResult(int callbackType, ScanResult result) {
			super.onScanResult(callbackType, result);
			if(result != null) {
				final ScanRecord scanRecord = result.getScanRecord();

				if(scanRecord != null) {
					resultCount ++;
					if (list_uuid.contains(result.getDevice().getAddress())) {
						return;
					} else {
						list_uuid.add(result.getDevice().getAddress());
					}
					final SparseArray<byte[]> manufacturerData = scanRecord.getManufacturerSpecificData();
					if (manufacturerData != null && manufacturerData.size() > 0) {
						for (int i = 0; i < manufacturerData.size(); i++) {
							byte[] bytesData = manufacturerData.valueAt(i);
							String data = new String(bytesData, StandardCharsets.UTF_8);
							int rssiValue = result.getRssi();
							if (data.contains(Constants.STORE_ID)) {
								BBLog.e(BBLog.TAG, " *****         Rssi = " + rssiValue);
								if ((rssiValue <= 0) && (rssiValue >= -85)) {
//									BBLog.e(BBLog.TAG, " *****   DeviceName = " + scanRecord.getDeviceName());
//									BBLog.e(BBLog.TAG, " *****      Address = " + result.getDevice().getAddress());
//									BBLog.e(BBLog.TAG, " *****   scanRecord = " + scanRecord.toString());
									String string = new String(manufacturerData.valueAt(i), StandardCharsets.UTF_8);
									if (string.contains("Store:") || string.contains("store:")) {
										BBLog.e(BBLog.TAG + "_BLE", i + " ***** manufacturer = " + string);
									} else {
										BBLog.e(BBLog.TAG, i + " ***** manufacturer = " + string);
									}
									if (Constants.M_GEOFENCE_STATUS == GpsLocationManager.OUT_OF_ZONE) {
										BBLog.w(BBLog.TAG, "OUT_OF_ZONE, BEACON detected，back to store，OUT_OF_FENCE dismiss");
										GpsLocationManager.closeOutOfGeofenceWarning();
										GpsLocationManager.setCurrentGPSStatus(GpsLocationManager.IN_ZONE);
										WebSocketSender.C0903_DataInfoUploadForGeo(System.currentTimeMillis());
									} else if (Constants.M_GEOFENCE_STATUS==GpsLocationManager.LOCK_SCREEN || Constants.M_GEOFENCE_STATUS==GpsLocationManager.WIPE_DATA) {
										BBLog.w(BBLog.TAG, "LOCK_SCREEN, BEACON detected，back to store，OTP to unlock");
										Intent intent_geo = new Intent(UsualData.ACTION_ENTER_GEOFENCE);
										ContextUtil.getInstance().sendBroadcast(intent_geo, RequestPermission.REQUEST_PERMISSION_MY_BROADCAST);
									}
									GEO_IS_NEAR_BEACON = true;
								}
							}
						}
					}
				}
			}
		}

		@Override
		public void onBatchScanResults(List<ScanResult> results) {
			super.onBatchScanResults(results);
			BBLog.e(BBLog.TAG, "onBatchScanResults = " + results.size());
		}

		@Override
		public void onScanFailed(int errorCode) {
			super.onScanFailed(errorCode);
//			new Handler(Looper.getMainLooper()).postDelayed(new Runnable() {
//				@Override
//				public void run() {
//					if (mBLEScanner != null) {
//						mBLEScanner.startScan(mScanCB);
//					}
//				}
//			}, 10000);
		}
	};

	public static void registerBLEScanner() {
		if (!isBTBeaconScanNeed()) {
			return;
		}

		BluetoothAdapter mBluetoothAdapter = BluetoothAdapter.getDefaultAdapter();
		BBLog.e(BBLog.TAG, "***** registerBLEScanner ***** mBluetoothAdapter.isEnabled() = " + mBluetoothAdapter.isEnabled());
		if (mBluetoothAdapter.isEnabled()) {
			mBLEScanner = mBluetoothAdapter.getBluetoothLeScanner();
		}
	}

	public static void unregisterBLEScanner() {
		if (!isBTBeaconScanNeed()) {
			return;
		}

		BBLog.e(BBLog.TAG, "***** unregisterBLEScanner *****");
		mBLEScanner = null;
	}

	public static void startBLEScan() {
		if (!isBTBeaconScanNeed()) {
			return;
		}

		BBLog.e(BBLog.TAG + "_BLE", "***** startBLEScan ***** " + "  Constants.STORE_ID = " + Constants.STORE_ID);
//		GEO_IS_NEAR_BEACON = false;
		if (BluetoothAdapter.getDefaultAdapter().isEnabled() && mBLEScanner!=null && !TextUtils.isEmpty(Constants.STORE_ID)) {
			ScanSettings.Builder builder = new ScanSettings.Builder();
			builder.setScanMode(ScanSettings.SCAN_MODE_LOW_POWER);
			if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
				builder.setCallbackType(ScanSettings.CALLBACK_TYPE_ALL_MATCHES);
			}
			ScanSettings scanSettings = builder.build();

			ScanFilter.Builder builder2 = new ScanFilter.Builder();
			builder2.setManufacturerData(0xEFFE, ("Store:"+Constants.STORE_ID).getBytes());
//			builder2.setDeviceName(null);
			ScanFilter scanFilter = builder2.build();
			List<ScanFilter> scanFilterList = new ArrayList<>();
			scanFilterList.add(scanFilter);
			resultCount = 0;
			list_uuid.clear();
			mBLEScanner.startScan(scanFilterList, scanSettings, mScanCB);
			new Handler(Looper.getMainLooper()).postDelayed(new Runnable() {
				@Override
				public void run() {
					BBLog.e(BBLog.TAG, "***** stopBLEScan ***** resultCount = " + resultCount);
					if (resultCount == 0) {
						GEO_IS_NEAR_BEACON = false;
					}
					if (BluetoothAdapter.getDefaultAdapter().isEnabled() && mBLEScanner!=null) {
						mBLEScanner.stopScan(mScanCB);
						if (SCAN_MODE == 1) {
							BluetoothAdapter.getDefaultAdapter().disable();
						}
					}
				}
			}, 10000);
		} else {
			GEO_IS_NEAR_BEACON = false;
		}
	}

	/**取消轮询定时广播*/
	public static void stopBLEScanTimer(Context context){
		if (!isBTBeaconScanNeed()) {
			return;
		}

		BBLog.i(BBLog.TAG, "stopBLEScanTimer ");
		Intent intentTmp = new Intent(BroadcastActions.BLE_SCAN_TIMER_START_BC);
		if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
			intentTmp.setComponent(new ComponentName(context.getPackageName(), PollTimer.class.getName()));
		}
		PendingIntent pi = PendingIntent.getBroadcast(context, 0, intentTmp, PendingIntent.FLAG_UPDATE_CURRENT);
		AlarmManager am = (AlarmManager) context.getSystemService(Context.ALARM_SERVICE);
		am.cancel(pi);
	}

	/**启动轮询定时广播*/
	public static void startBLEScanTimer(Context context){
		if (!isBTBeaconScanNeed()) {
			return;
		}

		//if (GpsLocationManager.IN_ZONE==Constants.M_GEOFENCE_STATUS
		// || GeoProfileHandler.getCurExeProfileId()==null) {
		if(isWifiEnterGeofence()){
			BBLog.i(BBLog.TAG, "wifi is inzone in, reset GEO_IS_NEAR_BEACON" );
			GEO_IS_NEAR_BEACON = false;
			if (BluetoothAdapter.getDefaultAdapter().isEnabled()) {
				BluetoothAdapter.getDefaultAdapter().disable();
			}
		} else {
			if (SCAN_MODE == 1) {
				if (BluetoothAdapter.getDefaultAdapter().isEnabled()) {
					startBLEScan();
				} else {
					BluetoothAdapter.getDefaultAdapter().enable();
				}
			} else {
				startBLEScan();
			}
		}

		BBLog.i(BBLog.TAG, "startBLEScanTimer in " + (GEO_LOCK_MINS*60*1000) / 4);
		Intent intentTmp = new Intent(BroadcastActions.BLE_SCAN_TIMER_START_BC);
		if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
			intentTmp.setComponent(new ComponentName(context.getPackageName(), PollTimer.class.getName()));
		}
		PendingIntent pi = PendingIntent.getBroadcast(context, 0, intentTmp, PendingIntent.FLAG_UPDATE_CURRENT);
		AlarmManager am = (AlarmManager) context.getSystemService(Context.ALARM_SERVICE);
		am.cancel(pi);
		long timeOnMillis = System.currentTimeMillis() + ((GEO_LOCK_MINS*60*1000)/4);

		if (Build.VERSION.SDK_INT < Build.VERSION_CODES.KITKAT) {
			am.set(AlarmManager.RTC_WAKEUP, timeOnMillis, pi);
		} else {
			am.setExact(AlarmManager.RTC_WAKEUP, timeOnMillis, pi);
		}
	}

	public static boolean isBTBeaconScanNeed() {
		if (DeviceInfoApi.getIntance().isWisePosPro()) {
			return true;
		}
		return false;
	}
}