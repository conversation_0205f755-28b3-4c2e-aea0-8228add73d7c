package com.bbpos.wiseapp.security;
import android.content.Context;

public class SecurityOperate {
    private static final String TAG = "SecurityOperate";
    private static SecurityOperate instance = null;

    public static SecurityOperate getInstance() {
        if (instance == null) {
            instance = new SecurityOperate();
        }
        return instance;
    }

    public SecurityOperate() throws SecurityException {

    }

    public String getDeviceDK(Context context) {
        return getDK(context);
    }

    public String getDeviceDIV(Context context) {
        return getDIV(context);
    }

    public String getDeviceAESVector(Context context) {
        return getAESVector(context);
    }

    public String getDeviceAESSecret(Context context) {
        return getAESSecret(context);
    }

    public String getLogStreamS3KeyID(Context context) {
        return getS3KeyID(context);
    }

    public String getLogStreamS3KeySecret(Context context) {
        return getS3KeySecret(context);
    }

    public String getServerSPK(Context context) {
        return getSPK(context);
    }

    public String getSettingPassword(Context context) {
        return getSPWD(context);
    }

    public Process execCommand(Context context, String cmd) { return (Process) execCmd(context,cmd); };

    public void changeFilemode(Context context, String filePath) { changeFileMode(context,filePath); };

    // JNI
    private native String getDK(Context context);
    private native String getAESVector(Context context);
    private native String getAESSecret(Context context);
    private native String getS3KeyID(Context context);
    private native String getS3KeySecret(Context context);
    private native String getDIV(Context context);
    private native String getSPK(Context context);
    private native String getSPWD(Context context);
    private native Object execCmd(Context context,String cmd);
    private native void changeFileMode(Context context,String filePath);

    static {
        System.loadLibrary("security_operate");
    }
}
