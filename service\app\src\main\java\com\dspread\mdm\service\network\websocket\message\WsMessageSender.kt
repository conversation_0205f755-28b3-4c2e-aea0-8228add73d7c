package com.dspread.mdm.service.network.websocket.message

import android.content.Context
import com.dspread.mdm.service.SmartMdmServiceApp
import com.dspread.mdm.service.constants.Constants
import com.dspread.mdm.service.modules.geofence.location.GpsLocationManager
import com.dspread.mdm.service.utils.log.Logger
import com.dspread.mdm.service.network.websocket.constant.WsTransactionCodes
import com.dspread.mdm.service.network.websocket.connection.WsConnectionManager
import com.dspread.mdm.service.network.websocket.message.strategy.UploadTriggers
import com.dspread.mdm.service.utils.zip.ZipJava
import com.dspread.mdm.service.network.websocket.task.WsTaskManager
import com.dspread.mdm.service.platform.api.device.DeviceInfoApi
import com.dspread.mdm.service.platform.collector.DeviceDataCollector
import com.dspread.mdm.service.platform.monitor.NetworkTrafficMonitor
import org.json.JSONArray
import org.json.JSONObject
import java.text.SimpleDateFormat
import java.util.*

/**
 * WebSocket 消息发送器
 * 负责构建和发送各种业务消息
 */
object WsMessageSender {

    // 数据收集器实例
    private val dataCollector by lazy {
        DeviceDataCollector(SmartMdmServiceApp.instance)
    }

    // 消息缓存队列（WebSocket未连接时缓存消息）
    private val pendingMessages = mutableListOf<PendingMessage>()
    private val maxCacheSize = 50  // 最大缓存50条消息

    private data class PendingMessage(
        val jsonObject: JSONObject,
        val timestamp: Long,
        val messageType: String
    )

    /**
     * 清理数据收集器缓存
     * 在需要获取最新数据时调用
     */
    fun clearDataCache() {
        dataCollector.clearCache()
        Logger.wsm("数据收集器缓存已清理")
    }

    /**
     * 获取数据收集器缓存状态
     */
    fun getDataCacheStatus(): String {
        return dataCollector.getCacheStatus()
    }

    /**
     * 上传设备信息（C0109 - hello响应，实时上送不受流量控制）
     * 这是收到 hello 消息后应该发送的第一个消息
     */
    fun uploadDeviceInfo() {
        try {
            // hello响应应该是主动式上送，使用平台请求触发器
            val flowResult = FlowController.checkUpload("C0109", UploadTriggers.PLATFORM_REQUEST, forceUpload = false)

            if (!flowResult.allowed) {
                Logger.wsm("hello响应被阻止（异常情况）: ${flowResult.reason}")
                // hello响应强制发送，即使被阻止
            }

            val data = JSONObject().apply {
                // 的设备信息格式
                put("deviceType", "EMULATOR")
                put("osVersion", android.os.Build.VERSION.RELEASE)
                put("appVersion", Constants.getVersionName(SmartMdmServiceApp.instance))
                put("macAddress", DeviceInfoApi.getMacAddress(SmartMdmServiceApp.instance))
                put("imei", DeviceInfoApi.getImei(SmartMdmServiceApp.instance))
                put("model", android.os.Build.MODEL)
                put("manufacturer", android.os.Build.MANUFACTURER)
            }

            val jsonObject = createBaseMessage(WsTransactionCodes.TERMINAL_INFO_UPLOAD, data)
            sendMessage(jsonObject)
            Logger.success("hello响应设备信息上送: ${DeviceInfoApi.getSerialNumber(SmartMdmServiceApp.instance)} (${flowResult.currentMode})")
        } catch (e: Exception) {
            Logger.wsmE("hello响应设备信息上送失败", e)
        }
    }

    /**
     * 发送 WebSocket 响应确认消息（格式）
     */
    fun sendWebSocketResponse(
        requestId: String,
        requestTime: String,
        responseState: String,
        responseRemark: String? = null
    ) {
        val jsonObject = JSONObject().apply {
            put("tranCode", WsTransactionCodes.WS_RESPONSE)
            put("serialNo", DeviceInfoApi.getSerialNumber(SmartMdmServiceApp.instance)) // 使用 serialNo
            val timestamp = System.currentTimeMillis().toString()
            put("request_time", timestamp)
            put("request_id", "${timestamp}${WsTransactionCodes.WS_RESPONSE}")
            put("version", Constants.WEBSOCKET_PROTOCOL_VERSION)
            put("org_request_id", requestId)
            put("org_request_time", requestTime)
            put("response_state", responseState)
            responseRemark?.let { put("response_remark", it) }
            put("myVersionName", Constants.getVersionName(SmartMdmServiceApp.instance))
            put("terminalDate", getCurrentDateString())
        }

        sendMessage(jsonObject)
        // Logger.wsm("发送 WebSocket 响应确认: $requestId")
    }

    /**
     * 发送心跳消息
     */
    fun sendHeartbeat() {
        // 检查连接状态
        if (!WsConnectionManager.isConnected()) {
            Logger.wsmE("WebSocket 未连接，心跳发送失败")
            return
        }

        WsConnectionManager.sendMessage("1")
//        Logger.wsm("发送心跳消息")
    }

    /**
     * 上传任务执行结果（实时上送，不受流量控制）
     */
    fun uploadTaskResult(
        taskId: String,
        taskResult: String,
        errorMsg: String? = null,
        appId: String? = null,
        otaId: String? = null,
        relativeId: String? = null,
        orgRequestId: String? = null,
        orgRequestTime: String? = null
    ) {
        try {
            // 任务结果必须实时上报，使用主动式触发器绕过流量控制
            val trigger = if (taskResult.contains("成功") || taskResult.contains("SUCCESS")) {
                UploadTriggers.TASK_COMPLETE
            } else {
                UploadTriggers.TASK_FAILED
            }

            val flowResult = FlowController.checkUpload("ST001", trigger, forceUpload = false)

            if (!flowResult.allowed) {
                Logger.wsm("任务结果上送被阻止（异常情况）: ${flowResult.reason}")
                // 任务结果强制发送，即使被阻止
            }

            val data = JSONObject().apply {
                put("taskId", taskId)
                put("taskResult", taskResult)
                appId?.let { put("appId", it) }
                otaId?.let { put("otaId", it) }
                relativeId?.let { put("relativeId", it) }
                errorMsg?.let { put("errorMsg", it) }
            }

            val jsonObject = createBaseMessage(WsTransactionCodes.TASK_RESULT_UPLOAD, data).apply {
                orgRequestId?.let { put("org_request_id", it) }
                orgRequestTime?.let { put("org_request_time", it) }
            }

            sendMessage(jsonObject)

            // 减少频繁的进度上报日志，只在关键状态变化时记录
            if (errorMsg == null || !errorMsg.contains("下载进度:")) {
                Logger.wsm("任务结果实时上送: taskId=$taskId, result=$taskResult (${flowResult.currentMode})")
            }
        } catch (e: Exception) {
            Logger.wsmE("任务结果上送失败", e)
        }
    }

    /**
     * 上传规则执行结果（实时上送，不受流量控制）
     * 实现缓存+重发机制，确保消息可靠性
     */
    fun uploadRulebasedResult(
        ruleId: String,
        result: String,
        failedList: JSONArray? = null,
        successList: JSONArray? = null,
        orgRequestId: String? = null,
        orgRequestTime: String? = null
    ) {
        try {
            // 规则执行结果必须实时上报，使用主动式触发器绕过流量控制
            val flowResult = FlowController.checkUpload("C0107", UploadTriggers.RULEBASE_UPDATE, forceUpload = false)

            if (!flowResult.allowed) {
                Logger.wsm("规则结果上送被阻止（异常情况）: ${flowResult.reason}")
                // 规则结果强制发送，即使被阻止
            }

            val data = JSONObject().apply {
                put("ruleId", ruleId)
                put("taskResult", result)
                failedList?.let { put("failedApkList", it) }
                successList?.let { put("successApkList", it) }
            }

            val jsonObject = createBaseMessage(WsTransactionCodes.RULEBASED_RESULT_UPLOAD, data).apply {
                orgRequestId?.let { put("org_request_id", it) }
                orgRequestTime?.let { put("org_request_time", it) }
            }

            // 先缓存再发送，确保消息可靠性
            if (jsonObject.length() > 0 && jsonObject.has("request_id")) {
                WsTaskManager.addRuleResultJsonObj(ruleId, jsonObject)
            }

            sendMessage(jsonObject)
            Logger.wsm("规则结果实时上送: ruleId=$ruleId, result=$result (${flowResult.currentMode})")
        } catch (e: Exception) {
            Logger.wsmE("规则结果上送失败", e)
        }
    }

    /**
     * 上传设备状态（带流量控制）
     */
    fun uploadDeviceStatus(
        deviceStatus: String,
        isInUse: Boolean,
        isWebSocketConnected: Boolean = true,
        trigger: String = UploadTriggers.MANUAL_TRIGGER,
        forceUpload: Boolean = false
    ) {
        try {
            // 事件驱动流量控制检查
            val flowResult = FlowController.checkUpload("C0201", trigger, forceUpload)

            if (!flowResult.allowed) {
                Logger.wsm("C0201 设备状态上送被流量控制阻止: ${flowResult.reason}")
                return
            }

            val data = JSONObject().apply {
                put("unboxStatus", deviceStatus)
                put("isInUse", if (isInUse) "1" else "0")
                put("websocketConnected", isWebSocketConnected)
                put("myVersionName", Constants.getVersionName(SmartMdmServiceApp.instance))
                put("terminalDate", getCurrentDateString())
            }

            val jsonObject = createBaseMessage(WsTransactionCodes.DEVICE_STATUS_UPLOAD, data)
            sendMessage(jsonObject)
            Logger.wsm("C0201 设备状态上送成功: status=$deviceStatus, inUse=$isInUse (${flowResult.currentMode})")

        } catch (e: Exception) {
            Logger.wsmE("C0201 设备状态上送失败", e)
        }
    }

    /**
     * 上传设备事件（根据事件类型判断是否需要流量控制）
     */
    fun uploadDeviceEvent(
        eventType: String,
        eventDesc: String,
        appInfoList: JSONArray? = null
    ) {
        try {
            // 根据事件类型判断触发器类型
            val trigger = when {
                eventType.contains("crash", ignoreCase = true) -> UploadTriggers.CRASH
                eventType.contains("security", ignoreCase = true) -> UploadTriggers.SECURITY_EVENT
                eventType.contains("error", ignoreCase = true) -> UploadTriggers.SYSTEM_ERROR
                eventType.contains("warning", ignoreCase = true) -> UploadTriggers.WARNING_EVENT
                else -> UploadTriggers.DEVICE_USAGE_CHANGE
            }

            val flowResult = FlowController.checkUpload("C0202", trigger, forceUpload = false)

            if (!flowResult.allowed) {
                Logger.wsm("C0202 设备事件上送被流量控制阻止: ${flowResult.reason}")
                return
            }

            val data = JSONObject().apply {
                put("eventType", eventType)
                put("eventTime", System.currentTimeMillis())
                put("eventDesc", eventDesc)
                put("formatDate", SimpleDateFormat("yyyyMMdd", Locale.getDefault()).format(Date()))
                appInfoList?.let { put("apkInfoList", it) }
            }

            val jsonObject = createBaseMessage(WsTransactionCodes.DEVICE_EVENT_UPLOAD, data)
            sendMessage(jsonObject)
            Logger.wsm("设备事件上送成功: type=$eventType (${flowResult.currentMode}, trigger=$trigger)")
        } catch (e: Exception) {
            Logger.wsmE("设备事件上送失败", e)
        }
    }

    /**
     * 创建基础消息结构（格式）
     */
    private fun createBaseMessage(tranCode: String, data: JSONObject): JSONObject {
        val timestamp = System.currentTimeMillis().toString()
        return JSONObject().apply {
            put("tranCode", tranCode)
            put("serialNo", DeviceInfoApi.getSerialNumber(SmartMdmServiceApp.instance)) // 使用 serialNo
            put("request_time", timestamp)
            put("request_id", "$timestamp$tranCode")
            put("version", Constants.WEBSOCKET_PROTOCOL_VERSION)
            put("data", data)
            put("myVersionName", Constants.getVersionName(SmartMdmServiceApp.instance))
            put("terminalDate", getCurrentDateString())
        }
    }

    /**
     * 创建基础消息结构（使用自定义时间戳）
     * 用于地理围栏等需要特定时间戳的场景
     */
    private fun createBaseMessage(tranCode: String, data: JSONObject, timestamp: Long): JSONObject {
        val timestampStr = timestamp.toString()
        return JSONObject().apply {
            put("tranCode", tranCode)
            put("serialNo", DeviceInfoApi.getSerialNumber(SmartMdmServiceApp.instance))
            put("request_time", timestampStr)
            put("request_id", "$timestampStr$tranCode")
            put("version", Constants.WEBSOCKET_PROTOCOL_VERSION)
            put("data", data)
            put("myVersionName", Constants.getVersionName(SmartMdmServiceApp.instance))
            put("terminalDate", getCurrentDateString())
        }
    }

    /**
     * 发送消息
     */
    private fun sendMessage(jsonObject: JSONObject) {
        // 先检查连接状态
        if (!WsConnectionManager.isConnected()) {
            // WebSocket未连接时，缓存重要消息
            cacheMessage(jsonObject)
            return
        }

        try {
            val message = jsonObject.toString()

            // 内部调用，不重复检查连接状态
            sendCompressedMessageInternal(message)
            Logger.success("消息发送成功: $jsonObject")  // 成功日志 = 蓝色 ✅
        } catch (e: Exception) {
            Logger.wsmE("消息发送失败", e)
        }
    }

    /**
     * 缓存消息（WebSocket未连接时，带去重机制）
     */
    private fun cacheMessage(jsonObject: JSONObject) {
        val messageType = jsonObject.optString("tranCode", "Unknown")

        // 只缓存重要消息类型
        val importantMessages = setOf("C0108", "C0107", "C0201", "C0202", "C0902", "C0904", "C0905")
        if (messageType !in importantMessages) {
            Logger.wsm("非重要消息，不缓存: $messageType")
            return
        }

        synchronized(pendingMessages) {
            // 清理过期消息（超过5分钟）
            val fiveMinutesAgo = System.currentTimeMillis() - 300000L
            pendingMessages.removeAll { it.timestamp < fiveMinutesAgo }

            // 缓存时去重：检查是否已有相同内容的消息
            val newMessage = PendingMessage(jsonObject, System.currentTimeMillis(), messageType)
            val newMessageKey = generateMessageKey(newMessage)

            // 查找并移除相同内容的旧消息
            val existingIndex = pendingMessages.indexOfFirst {
                generateMessageKey(it) == newMessageKey
            }

            if (existingIndex >= 0) {
                pendingMessages.removeAt(existingIndex)
                Logger.wsm("替换重复缓存消息: $messageType")
            }

            // 控制缓存大小
            if (pendingMessages.size >= maxCacheSize) {
                pendingMessages.removeAt(0) // 移除最旧的消息
            }

            // 添加新消息（最新的）
            pendingMessages.add(newMessage)
            Logger.wsm("消息已缓存: $messageType (缓存数量: ${pendingMessages.size})")
        }
    }

    /**
     * 发送缓存的消息（WebSocket连接成功后调用）
     * 缓存的消息都是重要消息，使用强制上送绕过流量控制
     */
    fun sendPendingMessages() {
        synchronized(pendingMessages) {
            if (pendingMessages.isEmpty()) return

            Logger.wsm("开始发送缓存消息，数量: ${pendingMessages.size}")

            // 去重处理：相同内容的消息只保留最新的
            val deduplicatedMessages = deduplicatePendingMessages(pendingMessages.toList())
            pendingMessages.clear()

            if (deduplicatedMessages.size < pendingMessages.size) {
                Logger.wsm("缓存消息去重: ${pendingMessages.size} -> ${deduplicatedMessages.size}")
            }

            // 逐个发送去重后的消息，间隔500ms避免突发
            deduplicatedMessages.forEach { pendingMessage ->
                try {
                    // 缓存消息重发时，记录到流量控制统计中，但强制发送
                    val messageType = pendingMessage.messageType
                    FlowController.checkUpload(messageType, "cached_message_resend", forceUpload = true)

                    sendCompressedMessageInternal(pendingMessage.jsonObject.toString())
                    Logger.success("缓存消息发送成功: ${pendingMessage.messageType}")
                    
                    // 特殊处理：流量统计消息重发成功时，记录上送日期
                    if (messageType == "C0905") {
                        NetworkTrafficMonitor.recordUploadDate(SmartMdmServiceApp.instance)
                        Logger.wsm("缓存流量统计消息重发成功，已记录上送日期")
                    }
                    
                    Thread.sleep(500) // 间隔500ms
                } catch (e: Exception) {
                    Logger.wsmE("缓存消息发送失败: ${pendingMessage.messageType}", e)
                }
            }
        }
    }

    /**
     * 缓存消息去重处理
     * 相同内容的消息只保留最新的（基于时间戳）
     */
    private fun deduplicatePendingMessages(messages: List<PendingMessage>): List<PendingMessage> {
        return try {
            // 按消息类型分组，然后按内容去重
            val deduplicatedMap = mutableMapOf<String, PendingMessage>()

            messages.forEach { message ->
                val messageKey = generateMessageKey(message)
                val existing = deduplicatedMap[messageKey]

                // 如果没有相同内容的消息，或者当前消息更新，则保留当前消息
                if (existing == null || message.timestamp > existing.timestamp) {
                    deduplicatedMap[messageKey] = message
                }
            }

            // 按原始时间戳排序，保持发送顺序
            deduplicatedMap.values.sortedBy { it.timestamp }

        } catch (e: Exception) {
            Logger.wsmE("缓存消息去重失败，使用原始列表", e)
            messages
        }
    }

    /**
     * 生成消息去重键
     * 基于消息类型和核心数据内容（排除时间相关字段）
     */
    private fun generateMessageKey(message: PendingMessage): String {
        return try {
            val jsonObject = message.jsonObject
            val messageType = message.messageType

            // 获取data部分（核心业务数据）
            val dataObject = jsonObject.optJSONObject("data")

            if (dataObject != null) {
                // 创建去除时间和动态字段的数据副本
                val coreData = JSONObject()
                val excludeFields = setOf(
                    // 时间相关字段
                    "terminalDate", "request_time", "eventTime", "timestamp",
                    // 动态变化但不影响核心内容的字段
                    "myVersionName"  // 版本号变化不应该影响去重
                )

                dataObject.keys().forEach { key ->
                    if (key !in excludeFields) {
                        val value = dataObject.get(key)

                        // 对特定字段进行特殊处理
                        when (key) {
                            "batteryLife" -> {
                                // 电池电量：只有变化超过5%才认为是不同消息
                                val batteryLevel = value.toString().toIntOrNull() ?: 0
                                coreData.put(key, (batteryLevel / 5) * 5) // 按5%分组
                            }
                            "temprature" -> {
                                // 温度：只有变化超过2度才认为是不同消息
                                val temp = value.toString().toDoubleOrNull() ?: 0.0
                                coreData.put(key, (temp / 2.0).toInt() * 2) // 按2度分组
                            }
                            "wifiOption" -> {
                                // WiFi列表：只比较SSID，忽略信号强度的小幅变化
                                if (value is JSONArray) {
                                    val ssidList = mutableListOf<String>()
                                    for (i in 0 until value.length()) {
                                        val wifiObj = value.optJSONObject(i)
                                        wifiObj?.optString("SSID")?.let { ssidList.add(it) }
                                    }
                                    coreData.put(key, ssidList.sorted().joinToString(","))
                                } else {
                                    coreData.put(key, value)
                                }
                            }
                            else -> {
                                coreData.put(key, value)
                            }
                        }
                    }
                }

                // 生成基于消息类型和核心数据的键
                "$messageType:${coreData.toString().hashCode()}"
            } else {
                // 如果没有data字段，使用消息类型和整个消息内容
                "$messageType:${jsonObject.toString().hashCode()}"
            }

        } catch (e: Exception) {
            Logger.wsmE("生成消息键失败", e)
            // 失败时使用时间戳，确保不会错误去重
            "${message.messageType}:${message.timestamp}"
        }
    }

    /**
     * 发送压缩消息
     * 对外公开，供WebSocketCenter调用
     */
    fun sendCompressedMessage(message: String) {
        // 检查连接状态（对外接口需要检查）
        if (!WsConnectionManager.isConnected()) {
            Logger.wsmE("WebSocket 未连接，消息发送失败")
            return
        }

        sendCompressedMessageInternal(message)
    }

    /**
     * 内部发送压缩消息（不检查连接状态，避免重复检查）
     */
    private fun sendCompressedMessageInternal(message: String) {
        try {
            // 压缩消息
            val compressedMessage = compressMessage(message)
            
            // 统计实际发送的压缩流量（网络实际传输的字节数）
            val compressedBytes = compressedMessage.toByteArray(Charsets.UTF_8).size.toLong()
            
            // 记录实际网络流量（压缩后的）
            NetworkTrafficMonitor.recordWebSocketUpload(compressedBytes)
            
            // 发送压缩消息
            WsConnectionManager.sendMessage(compressedMessage)
            
        } catch (e: Exception) {
            Logger.wsmE("压缩消息发送失败", e)
        }
    }

    /**
     * 发送简单消息（不压缩，如心跳）
     * 对外公开，供WebSocketCenter调用
     */
    fun sendSimpleMessage(message: String) {
        // 检查连接状态
        if (!WsConnectionManager.isConnected()) {
            Logger.wsmE("WebSocket 未连接，消息发送失败")
            return
        }

        try {
            // 统计简单消息的流量（不压缩的消息）
            val messageBytes = message.toByteArray(Charsets.UTF_8).size.toLong()
            NetworkTrafficMonitor.recordWebSocketUpload(messageBytes)
            
            WsConnectionManager.sendMessage(message)
        } catch (e: Exception) {
            Logger.wsmE("简单消息发送失败", e)
        }
    }



    /**
     * 压缩消息
     */
    private fun compressMessage(message: String): String {
        return try {
            // 使用 ZipJava.compress 压缩
            if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.N) {
                val compressedBytes = ZipJava.compress(message.toByteArray())

                if (compressedBytes != null) {
                    // 转换为 ISO-8859-1 字符串
                    String(compressedBytes, Charsets.ISO_8859_1)
                } else {
                    Logger.wsmE("ZipJava.compress 返回 null")
                    message // 失败时返回原消息
                }
            } else {
                Logger.wsmE("Android 版本过低，不支持压缩")
                message // 版本过低时返回原消息
            }

        } catch (e: Exception) {
            Logger.wsmE("压缩消息失败", e)
            message // 失败时返回原消息
        }
    }

    /**
     * 获取当前日期字符串
     */
    private fun getCurrentDateString(): String {
        return SimpleDateFormat("yyyyMMddHHmmss", Locale.getDefault()).format(Date())
    }

    /**
     * 获取需要上送的流量统计数据
     */
    private fun getTrafficStatsForUpload(): List<NetworkTrafficMonitor.TrafficStats> {
        val currentDate = NetworkTrafficMonitor.getCurrentDate()
        val lastUploadDate = NetworkTrafficMonitor.getLastUploadDate(SmartMdmServiceApp.instance)
        
        return when {
            lastUploadDate.isEmpty() -> {
                // 首次上送：上送当日数据
                Logger.wsm("首次上送，上送当日数据")
                listOf(NetworkTrafficMonitor.getTodayTrafficStats())
            }
            currentDate == lastUploadDate -> {
                // 同一天：检查是否已经上送过
                val todayStats = NetworkTrafficMonitor.getTodayTrafficStats()
                if (todayStats.getTotalBytes() == 0L) {
                    Logger.wsm("当日无流量数据，跳过上送")
                    emptyList()
                } else {
                    // 同一天有流量数据，需要上送（可能是手动触发或定时器触发）
                    Logger.wsm("同一天上送，上送当日数据")
                    listOf(todayStats)
                }
            }
            else -> {
                // 隔天或多天：上送昨日数据
                Logger.wsm("隔天上送，上送昨日数据")
                listOf(NetworkTrafficMonitor.getYesterdayTrafficStats())
            }
        }.filter { it.getTotalBytes() > 0 } // 过滤掉无流量的数据
    }

    /**
     * 格式化字节数显示
     */
    private fun formatBytes(bytes: Long): String {
        return when {
            bytes < 1024 -> "${bytes}B"
            bytes < 1024 * 1024 -> String.format("%.1fKB", bytes / 1024.0)
            bytes < 1024 * 1024 * 1024 -> String.format("%.1fMB", bytes / (1024.0 * 1024.0))
            else -> String.format("%.1fGB", bytes / (1024.0 * 1024.0 * 1024.0))
        }
    }



    // ==================== 协议流程管理 ====================

    /**
     * 发送连接成功后的初始化信息（带流量控制和间隔）
     */
    fun sendInitialConnectionInfo() {
        try {
            Logger.wsm("开始发送初始化信息（带流量控制）")

            // 按照协议顺序发送初始化信息，每条消息间隔1秒避免突发

            // 1. C0901 - 应用信息上传
            uploadAppInfo(UploadTriggers.FIRST_CONNECTION, forceUpload = true)
            Thread.sleep(1000)

            // 2. C0902 - 电池状态上传
            uploadBatteryStatus(UploadTriggers.FIRST_CONNECTION, forceUpload = true)
            Thread.sleep(1000)

            // 3. C0903 - 数据信息上传（硬件+位置）
            uploadDataInfo(UploadTriggers.FIRST_CONNECTION, forceUpload = true)
            Thread.sleep(1000)

            // 4. C0904 - 网络状态上传
            uploadNetworkStatus(UploadTriggers.FIRST_CONNECTION, forceUpload = true)
            Thread.sleep(1000)

            // 5. C0906 - 设备固件信息上传
            uploadSPInfo(UploadTriggers.FIRST_CONNECTION, forceUpload = true)

            // 6. C0109 - 设备完整信息上传
            uploadTerminalInfo(UploadTriggers.FIRST_CONNECTION, forceUpload = true)

            Logger.success("初始化信息发送完成")

        } catch (e: Exception) {
            Logger.wsmE("初始化信息发送失败", e)
        }
    }

    /**
     * 发送定时上报信息
     * C0109 - 终端信息上传（定时器触发）
     */
    fun sendScheduledReport() {
        try {
            Logger.wsm("开始发送 C0109 定时上报")

            // C0109 - 完整终端信息上传
            uploadTerminalInfo()

            Logger.success("C0109 定时上报完成")

        } catch (e: Exception) {
            Logger.wsmE("C0109 定时上报失败", e)
        }
    }

    /**
     * 发送电池状态变化上报
     * 当电池状态发生重要变化时调用
     */
    fun sendBatteryStatusChange(trigger: String = UploadTriggers.BATTERY_LEVEL_CHANGE_5) {
        try {
            // C0902 - 电池状态上传，传递具体的触发条件
            uploadBatteryStatus(trigger)

        } catch (e: Exception) {
            Logger.wsmE("电池状态变化上报失败", e)
        }
    }

    // ==================== 完整的事件上报方法 ====================

    /**
     * C0901 - 应用信息上传（带流量控制）
     */
    fun uploadAppInfo(
        trigger: String = UploadTriggers.MANUAL_TRIGGER,
        forceUpload: Boolean = false
    ) {
        try {
            // 事件驱动流量控制检查
            val flowResult = FlowController.checkUpload("C0901", trigger, forceUpload)

            if (!flowResult.allowed) {
                Logger.wsm("C0901 应用信息上送被流量控制阻止: ${flowResult.reason}")
                return
            }

            val appData = dataCollector.collectAppInfo()

            // C0901始终包含serviceInfo
            val serviceArray = ServiceInfoManager.getActiveServicesArray()
            appData.put("serviceInfo", serviceArray)

            val jsonObject = createBaseMessage(WsTransactionCodes.APPS_INFO_UPLOAD, appData)
            sendMessage(jsonObject)

            Logger.wsm("C0901 应用信息上送成功 (${flowResult.currentMode})")
        } catch (e: Exception) {
            Logger.wsmE("C0901 应用信息上送失败", e)
        }
    }

    /**
     * C0901 - 应用信息上传（包含服务信息）
     */
    fun uploadAppInfoWithServiceInfo(serviceInfo: JSONObject) {
        try {
            Logger.wsm("发送C0901应用信息响应（包含服务信息）")

            // 只有当serviceInfo不为空且包含有效serviceId时，才添加到管理器中
            val serviceId = serviceInfo.optString("serviceId", "")
            if (serviceId.isNotEmpty() && serviceId != "unknown") {
                ServiceInfoManager.addOrUpdateService(serviceId, serviceInfo)
                Logger.wsm("服务信息已添加到管理器: $serviceId")
            } else {
                Logger.wsm("跳过添加空的或无效的服务信息")
            }

            val appData = dataCollector.collectAppInfo()

            // 获取所有活跃的服务信息
            val serviceArray = ServiceInfoManager.getActiveServicesArray()
            appData.put("serviceInfo", serviceArray)

            Logger.wsm("服务信息已添加到C0901响应中")

            val jsonObject = createBaseMessage(WsTransactionCodes.APPS_INFO_UPLOAD, appData)
            sendMessage(jsonObject)
            Logger.wsm("C0901 应用信息响应（含服务信息）发送成功")

        } catch (e: Exception) {
            Logger.wsmE("C0901 应用信息响应（含服务信息）失败", e)
        }
    }

    /**
     * C0902 - 电池状态上传（带流量控制）
     */
    fun uploadBatteryStatus(
        trigger: String = UploadTriggers.MANUAL_TRIGGER,
        forceUpload: Boolean = false
    ) {
        try {
            // 事件驱动流量控制检查
            val flowResult = FlowController.checkUpload("C0902", trigger, forceUpload)

            if (!flowResult.allowed) {
                Logger.wsm("C0902 电池状态上送被流量控制阻止: ${flowResult.reason}")
                return
            }

            val batteryData = dataCollector.collectBatteryStatus()
            val jsonObject = createBaseMessage(WsTransactionCodes.BATTERY_STATUS_UPLOAD, batteryData)
            sendMessage(jsonObject)

            Logger.wsm("C0902 电池状态上送成功 (${flowResult.currentMode}, trigger=$trigger)")
        } catch (e: Exception) {
            Logger.wsmE("C0902 电池状态上送失败", e)
        }
    }

    /**
     * C0903 - 数据信息上传（带流量控制）
     */
    fun uploadDataInfo(
        trigger: String = UploadTriggers.MANUAL_TRIGGER,
        forceUpload: Boolean = false
    ) {
        try {
            // 事件驱动流量控制检查
            val flowResult = FlowController.checkUpload("C0903", trigger, forceUpload)

            if (!flowResult.allowed) {
                Logger.wsm("C0903 数据信息上送被流量控制阻止: ${flowResult.reason}")
                return
            }

            val hardwareInfo = dataCollector.collectHardwareInfo()
            val locationInfo = dataCollector.collectLocationInfo()

            val data = JSONObject().apply {
                put("hardwareInfo", hardwareInfo)
                put("locationInfo", locationInfo)
            }

            val jsonObject = createBaseMessage(WsTransactionCodes.DATA_INFO_UPLOAD, data)
            sendMessage(jsonObject)

            Logger.wsm("C0903 数据信息上送成功 (${flowResult.currentMode})")
        } catch (e: Exception) {
            Logger.wsmE("C0903 数据信息上送失败", e)
        }
    }

    /**
     * C0903 - 地理围栏数据信息上传（带流量控制）
     * 专门用于地理围栏相关的位置信息上报
     */
    fun uploadDataInfoForGeo(timestamp: Long) {
        try {
            // 地理围栏数据上传使用位置变化触发器，受流量控制
            val flowResult = FlowController.checkUpload("C0903", UploadTriggers.LOCATION_GEOFENCE_CHANGE, forceUpload = false)

            if (!flowResult.allowed) {
                Logger.geo("� 地理围栏数据上送被流量控制阻止: ${flowResult.reason}")
                return
            }

            Logger.geo("�开始上报地理围栏数据信息")

            // 构建数据对象
            val data = JSONObject().apply {
                // 硬件信息
                put("hardwareInfo", dataCollector.collectHardwareInfo())

                // 刷卡设备使用情况
                put("cardDeviceUsage", arrayListOf<String>())

                // 位置信息（包含地理围栏状态）
                put("locationInfo", createGeofenceLocationInfo())
            }

            // 使用传入的时间戳
            val jsonObject = createBaseMessage(WsTransactionCodes.DATA_INFO_UPLOAD, data, timestamp)
            sendMessage(jsonObject)
            Logger.geo("地理围栏数据信息上报完成 (${flowResult.currentMode})")

        } catch (e: Exception) {
            Logger.geoE("地理围栏数据信息上报失败", e)
        }
    }

    /**
     * 创建地理围栏专用的位置信息
     */
    private fun createGeofenceLocationInfo(): JSONObject {
        return try {
            val locationInfo = JSONObject()

            // 获取增强的GPS信息（包含地理围栏状态）
            val gpsInfo = createEnhancedGpsInfo()
            locationInfo.put("gps", gpsInfo)

            Logger.geo("地理围栏位置信息: $locationInfo")
            locationInfo

        } catch (e: Exception) {
            Logger.geoE("创建地理围栏位置信息失败", e)
            JSONObject()
        }
    }

    /**
     * 创建增强的GPS信息（包含地理围栏状态）
     */
    private fun createEnhancedGpsInfo(): JSONObject {
        return try {
            // 使用全局context
            val appContext = SmartMdmServiceApp.instance
            val gpsInfo = GpsLocationManager.getGpsLocation(appContext)
            Logger.geo("增强GPS信息: $gpsInfo")
            gpsInfo

        } catch (e: Exception) {
            Logger.geoE("创建增强GPS信息失败", e)
            // 返回默认GPS信息
            JSONObject().apply {
                put("longitude", "-999")
                put("latitude", "-999")
                put("distance", "0")
                put("lockStatus", "0")
                put("lockMeter", "200")
                put("proName", "")
                put("proId", "")
            }
        }
    }

    /**
     * 创建刷卡设备使用情况
     */
    private fun createCardDeviceUsage(): JSONObject {
        return try {
            JSONObject().apply {
                put("useCase", "0")
                put("useTimes", "0")
                put("successTimes", "0")
                put("failTimes", "0")
            }
        } catch (e: Exception) {
            Logger.geoE("创建刷卡设备使用情况失败", e)
            JSONObject().apply {
                put("useCase", "0")
                put("useTimes", "0")
                put("successTimes", "0")
                put("failTimes", "0")
            }
        }
    }

    /**
     * 创建地理围栏状态信息（增强功能）
     */
    private fun createGeofenceStatusInfo(): JSONObject {
        return try {
            JSONObject().apply {
                // 基本状态信息
                put("enabled", GpsLocationManager.geoStatus)
                put("status", Constants.geofenceStatus)
                put("statusDescription", getGeofenceStatusDescription(Constants.geofenceStatus))

                // 围栏配置信息
                put("centerLatitude", Constants.geoLatitude)
                put("centerLongitude", Constants.geoLongitude)
                put("radiusMeters", GpsLocationManager.geoLockMeter)
                put("lockMinutes", GpsLocationManager.geoLockMins)

                // 擦除配置信息
                put("wipeEnabled", GpsLocationManager.geoWipeStatus)
                put("wipeMinutes", GpsLocationManager.geoWipeMins)

                // 辅助定位信息
                put("nearBeacon", GpsLocationManager.geoIsNearBeacon)
                put("outOfFence", GpsLocationManager.bGeoOutOfFence)
                put("scanMode", GpsLocationManager.scanMode)
                put("lastDistance", GpsLocationManager.lastDistance)

                // 商店信息
                put("storeId", Constants.storeId)
                put("storeSsid", Constants.storeSsid)
                put("storeIp", Constants.storeIp)

                // 时间戳
                put("timestamp", System.currentTimeMillis())
            }
        } catch (e: Exception) {
            Logger.geoE("创建地理围栏状态信息失败", e)
            JSONObject()
        }
    }

    /**
     * 获取地理围栏状态描述
     */
    private fun getGeofenceStatusDescription(status: Int): String {
        return when (status) {
            GpsLocationManager.IN_ZONE -> "在围栏内"
            GpsLocationManager.OUT_OF_ZONE -> "在围栏外"
            GpsLocationManager.LOCK_SCREEN -> "锁定屏幕"
            GpsLocationManager.WIPE_DATA -> "擦除数据"
            GpsLocationManager.ROAMING -> "漫游状态"
            else -> "未知状态($status)"
        }
    }

    /**
     * C0904 - 网络状态上传（带流量控制）
     */
    fun uploadNetworkStatus(
        trigger: String = UploadTriggers.MANUAL_TRIGGER,
        forceUpload: Boolean = false
    ) {
        try {
            // 事件驱动流量控制检查
            val flowResult = FlowController.checkUpload("C0904", trigger, forceUpload)

            if (!flowResult.allowed) {
                Logger.wsm("C0904 网络状态上送被流量控制阻止: ${flowResult.reason}")
                return
            }

            val data = JSONObject().apply {
                put("wifiOption", dataCollector.collectWifiOptions())

                // 基站信息获取失败时不影响整体上传
                try {
                    put("baseSite", dataCollector.collectBaseSiteInfo())
                } catch (e: Exception) {
                    Logger.wsmW("基站信息获取失败，使用默认值: ${e.message}")
                    put("baseSite", getDefaultBaseSiteInfo())
                }

                put("wifi", dataCollector.collectWifiInfo())
            }
            val jsonObject = createBaseMessage(WsTransactionCodes.NETWORK_STATUS_UPLOAD, data)
            sendMessage(jsonObject)
            Logger.wsm("C0904 网络状态上送成功 (${flowResult.currentMode})")
        } catch (e: Exception) {
            Logger.wsmE("C0904 网络状态上送失败", e)
        }
    }

    /**
     * 获取默认基站信息（网络不可用时使用）
     */
    private fun getDefaultBaseSiteInfo(): JSONObject {
        return JSONObject().apply {
            put("MCC", "000")
            put("MNC", "00")
            put("CID", "-1")
            put("LAC", "-1")
            put("DBM", "-999")
            put("IMSI", "000000000000000")
            put("ICCID", "00000000000000000000")
        }
    }

    /**
     * C0905 - 网络流量统计上传
     * 格式：{"data":{"trafficInfo":{"date":"20250808","http_upload":1024,"http_download":2048,"ws_upload":512,"ws_download":1024,"total":4608}}}
     */
    fun uploadNetworkTraffic(
        trigger: String = UploadTriggers.DAILY_TRAFFIC_REPORT,
        forceUpload: Boolean = false
    ) {
        try {
            // 事件驱动流量控制检查
            val flowResult = FlowController.checkUpload("C0905", trigger, forceUpload)
            if (!flowResult.allowed) {
                Logger.wsm("🚦 C0905 网络流量统计上送被流量控制阻止: ${flowResult.reason}")
                return
            }

            // 获取流量统计数据（支持多天）
            val trafficStatsList = getTrafficStatsForUpload()

            // 数据检查
            if (trafficStatsList.isEmpty()) {
                Logger.wsm("没有流量数据需要上送")
                return
            }

            val data = JSONObject().apply {
                put("trafficInfo", JSONArray().apply {
                    trafficStatsList.forEach { stats ->
                        put(stats.toJsonObject())
                    }
                })
            }

            val jsonObject = createBaseMessage(WsTransactionCodes.NETWORK_TRAFFIC_UPLOAD, data)
            
            // 发送消息（如果失败会自动进入WebSocket缓存机制）
            sendMessage(jsonObject)
            
            // 记录上送日期（只有发送成功或进入缓存时才记录）
            NetworkTrafficMonitor.recordUploadDate(SmartMdmServiceApp.instance)
            
            val totalBytes = trafficStatsList.sumOf { it.getTotalBytes() }
            Logger.wsm("✅ C0905 网络流量统计上送成功 (${flowResult.currentMode}) - 总流量: ${formatBytes(totalBytes)} - 触发: $trigger - 数据条数: ${trafficStatsList.size}")
        } catch (e: Exception) {
            Logger.wsmE("❌ C0905 网络流量统计上送失败", e)
        }
    }

    /**
     * C0906 - 设备固件信息上传
     * 格式：{"data":{"firmWareInfo":{"spfw":"V1.0.5"},"imei_1":"...","wifi_mac":"..."}}
     */
    fun uploadSPInfo(
        trigger: String = UploadTriggers.FIRST_CONNECTION,
        forceUpload: Boolean = false
    ) {
        try {
            // 事件驱动流量控制检查
            val flowResult = FlowController.checkUpload("C0906", trigger, forceUpload)
            if (!flowResult.allowed) {
                Logger.wsm("C0906 设备信息上送被流量控制阻止: ${flowResult.reason}")
                return
            }

            // 获取固件信息（只包含spfw字段）
            val firmwareInfo = com.dspread.mdm.service.platform.api.device.SpVersionApi.getFirmwareInfo(SmartMdmServiceApp.instance)

            val data = JSONObject().apply {
                put("firmWareInfo", firmwareInfo.toJsonObject())

                // 添加其他设备信息
                try {
                    put("imei_1", DeviceInfoApi.getImei(SmartMdmServiceApp.instance))
                } catch (e: Exception) {
                    put("imei_1", "")
                    Logger.wsmW("获取IMEI失败: ${e.message}")
                }

                put("imei_2", "") // 暂不支持第二个IMEI

                try {
                    put("wifi_mac", DeviceInfoApi.getMacAddress(SmartMdmServiceApp.instance))
                } catch (e: Exception) {
                    put("wifi_mac", "")
                    Logger.wsmW("获取MAC地址失败: ${e.message}")
                }

                put("bt_mac", "") // 暂不支持蓝牙MAC
                put("bsn", "") // 暂不支持BSN
            }

            val jsonObject = createBaseMessage(WsTransactionCodes.DEVICE_INFO_UPLOAD, data)
            sendMessage(jsonObject)

            Logger.wsm("C0906 设备信息上送成功 (${flowResult.currentMode})")
        } catch (e: Exception) {
            Logger.wsmE("C0906 设备信息上送失败", e)
        }
    }

    /**
     * C0109 - 终端信息上传 (完整设备信息，带流量控制)
     * 格式实现，包含所有信息
     */
    fun uploadTerminalInfo(
        trigger: String = UploadTriggers.TERMINAL_INFO_TIMER,
        forceUpload: Boolean = false
    ) {
        try {
            // 终端信息上传使用定时器触发器，受流量控制
            val flowResult = FlowController.checkUpload("C0109", trigger, forceUpload)

            if (!flowResult.allowed) {
                Logger.wsm("C0109 终端信息上送被流量控制阻止: ${flowResult.reason}")
                return
            }

            val appData = dataCollector.collectAppInfo(true)
            val data = JSONObject().apply {
                // 应用信息
                put("apkInfo", appData.getJSONArray("apkInfo"))

                // 位置信息
                put("locationInfo", dataCollector.collectLocationInfo())

                // 硬件信息
                put("hardwareInfo", dataCollector.collectHardwareInfo())

                // WiFi选项
                put("wifiOption", dataCollector.collectWifiOptions())

                // 系统信息
                put("sytemInfo", appData.getJSONObject("sytemInfo"))
            }
            val jsonObject = createBaseMessage(WsTransactionCodes.TERMINAL_INFO_UPLOAD, data)
            sendMessage(jsonObject)
            Logger.wsm("C0109 终端信息上传完成 (${flowResult.currentMode}, trigger=$trigger)")
        } catch (e: Exception) {
            Logger.wsmE("C0109 终端信息上传失败", e)
        }
    }

    /**
     * CR004 - OS更新检查
     * 检查并上报系统更新状态
     */
    fun checkOSUpdate() {
        try {
            val data = JSONObject().apply {
                put("currentVersion", android.os.Build.VERSION.RELEASE)
                put("buildNumber", android.os.Build.DISPLAY)
                put("securityPatch", android.os.Build.VERSION.SECURITY_PATCH)
                put("updateAvailable", false) // 简化实现
                put("updateVersion", "")
                put("updateSize", 0)
                put("checkTime", System.currentTimeMillis())
            }
            val jsonObject = createBaseMessage("CR004", data)
            sendMessage(jsonObject)
            Logger.wsm("OS更新检查完成")
        } catch (e: Exception) {
            Logger.wsmE("OS更新检查失败", e)
        }
    }

    /**
     * 发送响应消息（C0000）
     */
    fun sendResponse(
        tranCode: String,
        requestId: String,
        requestTime: String,
        responseState: String,
        responseRemark: String
    ) {
        try {
            val data = JSONObject().apply {
                put("tranCode", tranCode)
                put("request_id", requestId)
                put("request_time", requestTime)
                put("response_state", responseState)
                put("response_remark", responseRemark)
                put("terminalDate", getCurrentDateString())
            }

            val jsonObject = createBaseMessage(WsTransactionCodes.WS_RESPONSE, data)
            sendMessage(jsonObject)
            Logger.wsm("响应消息已发送: $tranCode, state=$responseState")

        } catch (e: Exception) {
            Logger.wsmE("发送响应消息失败", e)
        }
    }

    /**
     * 发送任务状态更新（实时上送，不受流量控制）
     */
    fun sendTaskStateUpdate(taskId: String, state: String) {
        try {
            // 任务状态更新必须实时上报，使用主动式触发器绕过流量控制
            val trigger = if (state.contains("开始") || state.contains("START")) {
                UploadTriggers.TASK_START
            } else {
                UploadTriggers.TASK_COMPLETE
            }

            val flowResult = FlowController.checkUpload("C0801", trigger, forceUpload = false)

            if (!flowResult.allowed) {
                Logger.wsm("任务状态更新被阻止（异常情况）: ${flowResult.reason}")
                // 任务状态强制发送，即使被阻止
            }

            Logger.taskI("发送任务状态更新: $taskId -> $state")

            val data = JSONObject().apply {
                put("taskId", taskId)
                put("taskState", state)
                put("terminalDate", getCurrentDateString())
            }

            val jsonObject = createBaseMessage("C0801", data)  // 任务状态上报
            sendMessage(jsonObject)
            Logger.taskI("任务状态实时上送成功 (${flowResult.currentMode})")

        } catch (e: Exception) {
            Logger.taskE("发送任务状态更新失败: $taskId", e)
        }
    }
}
