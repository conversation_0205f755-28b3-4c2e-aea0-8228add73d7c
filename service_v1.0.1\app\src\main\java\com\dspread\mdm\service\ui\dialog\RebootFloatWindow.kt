package com.dspread.mdm.service.ui.dialog

import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import android.graphics.PixelFormat
import android.os.Build
import android.os.CountDownTimer
import android.os.Handler
import android.os.Looper
import android.view.Gravity
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.view.WindowManager
import android.widget.ImageView
import android.widget.TextView
import androidx.annotation.RequiresApi
import com.dspread.mdm.service.R
import com.dspread.mdm.service.utils.log.Logger
import java.util.Timer
import java.util.TimerTask

/**
 * 重启悬浮窗
 *
 * 功能：
 * - 显示重启倒计时悬浮窗
 * - 支持拖拽移动
 * - 自动倒计时并执行重启
 * - 支持锁定倒计时显示
 */
class RebootFloatWindow(
    private val context: Context,
    private val windowType: WindowType = WindowType.REBOOT
) {

    enum class WindowType {
        REBOOT,     // 重启倒计时
        LOCK,       // 锁定倒计时
        WIPE_DATA   // 数据擦除倒计时
    }
    
    companion object {
        private const val TAG = "RebootFloatWindow"
    }
    
    private var windowManager: WindowManager? = null
    private var layoutParams: WindowManager.LayoutParams? = null
    private var floatView: View? = null
    private var messageTextView: TextView? = null
    
    private var countdownTimer: Timer? = null
    private var countdownTask: TimerTask? = null
    private var remainingSeconds = 60 // 默认60秒
    
    private val handler = Handler(Looper.getMainLooper())
    
    // 触摸相关变量
    private var touchStartX = 0f
    private var touchStartY = 0f
    private var startX = 0
    private var startY = 0
    
    /**
     * 创建WindowManager
     */
    fun createWindowManager() {
        try {
            // 获取系统窗体管理器
            windowManager = context.getSystemService(Context.WINDOW_SERVICE) as WindowManager
            
            // 创建布局参数
            layoutParams = WindowManager.LayoutParams().apply {
                // 设置窗体显示类型
                type = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                    WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY
                } else {
                    WindowManager.LayoutParams.TYPE_PHONE
                }
                
                // 设置窗体焦点及触摸
                flags = WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE or 
                       WindowManager.LayoutParams.FLAG_FULLSCREEN
                
                // 设置显示模式
                format = PixelFormat.RGBA_8888
                
                // 设置对齐方式
                gravity = Gravity.TOP or Gravity.CENTER_HORIZONTAL
                
                // 设置初始位置
                x = 0
                y = 100 // 距离顶部100像素
                
                // 设置窗体大小
                width = WindowManager.LayoutParams.WRAP_CONTENT
                height = WindowManager.LayoutParams.WRAP_CONTENT
            }
            
            Logger.geo("$TAG WindowManager创建成功")
            
        } catch (e: Exception) {
            Logger.geoE("$TAG 创建WindowManager失败", e)
        }
    }
    
    /**
     * 创建悬浮窗布局
     */
    @SuppressLint("ClickableViewAccessibility")
    fun createDesktopLayout() {
        try {
            if (floatView == null) {
                val layoutInflater = LayoutInflater.from(context)
                floatView = layoutInflater.inflate(R.layout.view_reboot_float_window, null)

                messageTextView = floatView?.findViewById(R.id.tv_reboot_message)

                // 根据窗口类型设置不同的图标颜色
                val iconView = floatView?.findViewById<ImageView>(R.id.iv_float_icon)
                when (windowType) {
                    WindowType.REBOOT -> {
                        iconView?.setColorFilter(context.resources.getColor(android.R.color.holo_orange_dark))
                    }
                    WindowType.LOCK -> {
                        iconView?.setColorFilter(context.resources.getColor(android.R.color.holo_red_dark))
                    }
                    WindowType.WIPE_DATA -> {
                        iconView?.setColorFilter(context.getColor(android.R.color.holo_red_light))
                    }
                }

                updateMessage()
                
                // 设置触摸监听器，支持拖拽
                floatView?.setOnTouchListener { view, event ->
                    when (event.action) {
                        MotionEvent.ACTION_DOWN -> {
                            touchStartX = event.rawX
                            touchStartY = event.rawY
                            layoutParams?.let { params ->
                                startX = params.x
                                startY = params.y
                            }
                        }
                        
                        MotionEvent.ACTION_MOVE -> {
                            layoutParams?.let { params ->
                                val deltaX = (event.rawX - touchStartX).toInt()
                                val deltaY = (event.rawY - touchStartY).toInt()
                                
                                params.x = startX + deltaX
                                params.y = startY + deltaY
                                
                                // 更新悬浮窗位置
                                windowManager?.updateViewLayout(floatView, params)
                            }
                        }
                        
                        MotionEvent.ACTION_UP -> {
                            // 可以在这里添加点击事件处理
                        }
                    }
                    true
                }
                
                Logger.geo("$TAG 悬浮窗布局创建成功")
            }
            
        } catch (e: Exception) {
            Logger.geoE("$TAG 创建悬浮窗布局失败", e)
        }
    }
    
    /**
     * 显示悬浮窗

     */
    fun showDesk(timeoutSeconds: Int) {
        try {
            remainingSeconds = timeoutSeconds
            
            floatView?.let { view ->
                layoutParams?.let { params ->
                    if (view.parent == null) {
                        windowManager?.addView(view, params)
                        Logger.geo("$TAG 悬浮窗显示成功，倒计时: ${timeoutSeconds}秒")
                    }
                }
            }
            
            // 开始倒计时
            startCountdown()
            
        } catch (e: Exception) {
            Logger.geoE("$TAG 显示悬浮窗失败", e)
        }
    }
    
    /**
     * 开始倒计时
     */
    private fun startCountdown() {
        stopCountdown()
        
        countdownTimer = Timer()
        countdownTask = object : TimerTask() {
            override fun run() {
                remainingSeconds--
                
                handler.post {
                    if (remainingSeconds > 0) {
                        updateMessage()
                    } else {
                        // 倒计时结束，根据类型执行不同操作
                        when (windowType) {
                            WindowType.REBOOT -> {
                                Logger.geo("$TAG 悬浮窗倒计时结束，执行重启")
                                closeDesk()
                                executeReboot()
                            }
                            WindowType.LOCK -> {
                                Logger.geo("$TAG 悬浮窗倒计时结束，执行锁定")
                                closeDesk()
                                executeLock()
                            }
                            WindowType.WIPE_DATA -> {
                                Logger.geo("$TAG 悬浮窗倒计时结束，执行数据擦除")
                                closeDesk()
                                executeWipeData()
                            }
                        }
                    }
                }
            }
        }
        
        countdownTimer?.schedule(countdownTask, 1000, 1000)
    }
    
    /**
     * 停止倒计时
     */
    private fun stopCountdown() {
        countdownTask?.cancel()
        countdownTask = null
        countdownTimer?.cancel()
        countdownTimer = null
    }
    
    /**
     * 更新消息显示
     */
    private fun updateMessage() {
        val message = when (windowType) {
            WindowType.REBOOT -> "系统将在 $remainingSeconds 秒后重启"
            WindowType.LOCK -> "设备将在 $remainingSeconds 秒后锁定"
            WindowType.WIPE_DATA -> {
                if (remainingSeconds >= 60) {
                    val minutes = remainingSeconds / 60
                    val seconds = remainingSeconds % 60
                    if (seconds > 0) {
                        "数据将在 ${minutes}分${seconds}秒 后擦除"
                    } else {
                        "数据将在 ${minutes}分钟 后擦除"
                    }
                } else {
                    "数据将在 $remainingSeconds 秒后擦除"
                }
            }
        }
        messageTextView?.text = message
    }
    
    /**
     * 关闭悬浮窗
     */
    fun closeDesk() {
        try {
            stopCountdown()
            
            floatView?.let { view ->
                if (view.parent != null) {
                    windowManager?.removeView(view)
                    Logger.geo("$TAG 悬浮窗已关闭")
                }
            }
            
            floatView = null
            
        } catch (e: Exception) {
            Logger.geoE("$TAG 关闭悬浮窗失败", e)
        }
    }
    
    /**
     * 执行重启
     * 这里应该调用实际的重启方法
     */
    private fun executeReboot() {
        try {
            Logger.geo("$TAG 执行系统重启")
            // 这里应该调用实际的重启方法
            // 例如: SystemManagerAdapter.reboot(context)
            Runtime.getRuntime().exec("su -c reboot")

        } catch (e: Exception) {
            Logger.geoE("$TAG 执行重启失败", e)
        }
    }

    /**
     * 执行锁定
     * 启动锁屏界面
     */
    private fun executeLock() {
        try {
            Logger.geo("$TAG 执行设备锁定")

            // 启动锁屏Activity
            val intent = Intent().apply {
                setClassName(context.packageName, "com.dspread.mdm.service.ui.activity.LockScreenActivity")
                addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP)
                addFlags(Intent.FLAG_ACTIVITY_SINGLE_TOP)
            }
            context.startActivity(intent)

        } catch (e: Exception) {
            Logger.geoE("$TAG 执行锁定失败", e)
        }
    }

    /**
     * 执行数据擦除
     * 发送数据擦除广播
     */
    private fun executeWipeData() {
        try {
            Logger.geo("$TAG 执行数据擦除")

            // 发送数据擦除广播
            val intent = Intent("com.dspread.mdm.service.action.UNBIND_LAUNCHER")
            context.sendBroadcast(intent, "com.dspread.mdm.service.permission.DSPREAD")

            Logger.geo("$TAG 数据擦除广播已发送")

        } catch (e: Exception) {
            Logger.geoE("$TAG 执行数据擦除失败", e)
        }
    }
}
