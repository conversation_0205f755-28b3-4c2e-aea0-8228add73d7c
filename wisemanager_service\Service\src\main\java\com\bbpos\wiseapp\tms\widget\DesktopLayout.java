package com.bbpos.wiseapp.tms.widget;

import android.content.Context;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.LinearLayout;

import com.bbpos.wiseapp.service.R;

public class DesktopLayout extends LinearLayout {
	Context mContext;

	public DesktopLayout(Context context) {
		super(context);
		setOrientation(LinearLayout.VERTICAL);// 水平排列
		mContext = context;
		//设置宽高
		this.setLayoutParams( new LayoutParams(LayoutParams.MATCH_PARENT,
				LayoutParams.MATCH_PARENT));
		View view = LayoutInflater.from(context).inflate(
                R.layout.desklayout, null); 
		this.setBackgroundResource(R.color.lockbg);
		this.setGravity(Gravity.CENTER);
		this.setPadding(40, 0, 40, 0);
		this.addView(view);
	}
}
