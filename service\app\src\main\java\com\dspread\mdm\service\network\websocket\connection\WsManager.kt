package com.dspread.mdm.service.network.websocket.connection

import android.content.Context
import com.dspread.mdm.service.utils.log.Logger
import com.dspread.mdm.service.network.websocket.constant.WebSocketStatus


/**
 * WebSocket 管理器（对外接口）
 * 内部委托给 WsConnectionManager 实现
 * 
 * 主要功能：
 * - WebSocket 连接管理
 * - 消息发送
 * - 连接状态监听
 * - 重连机制
 * 
 * <AUTHOR> Mdm Team
 * @since 1.0.0
 */
object WsManager {

    /**
     * 构建器类
     */
    class Builder {
        internal var context: Context? = null
        internal var serviceUrl: String? = null
        internal var serviceUrlForReconn: String? = null
        internal var proxy: String? = null
        internal var connectTimeout: Int = 10000
        fun context(context: Context) = apply { this.context = context }
        fun serviceUrl(url: String) = apply { this.serviceUrl = url }
        fun serviceUrlForReconn(url: String) = apply { this.serviceUrlForReconn = url }
        fun proxy(proxy: String?) = apply { this.proxy = proxy }
        fun connectTimeout(timeout: Int) = apply { this.connectTimeout = timeout }

        fun build(): WsManager {
            checkParams()

            WsConnectionManager.init(
                context = context!!,
                url = serviceUrl!!
            )

            return WsManager
        }

        private fun checkParams() {
            requireNotNull(context) { "Context 不能为空" }
            requireNotNull(serviceUrl) { "Service URL 不能为空" }
        }
    }

    /**
     * 初始化 WebSocket 管理器
     */
    fun init(builder: Builder) {
        builder.build()
        Logger.success("WebSocket 管理器初始化完成")
    }

    /**
     * 连接 WebSocket
     */
    fun connect() {
        WsConnectionManager.connect()
    }

    /**
     * 重连 WebSocket
     */
    fun reconnect() {
        WsConnectionManager.reconnect()
    }

    /**
     * 发送消息
     */
    fun sendText(message: String) {
        WsConnectionManager.sendMessage(message)
    }

    /**
     * 断开连接
     */
    fun disconnect() {
        WsConnectionManager.disconnect()
    }

    /**
     * 获取连接状态
     */
    fun getStatus(): WebSocketStatus {
        return WsConnectionManager.getStatus()
    }

    /**
     * 是否已连接
     */
    fun isConnected(): Boolean {
        return WsConnectionManager.isConnected()
    }

    /**
     * 设置业务连接状态（收到 hellMsg 时调用）
     */
    fun setWebSocketConnected(connected: Boolean) {
        WsConnectionManager.setWebSocketConnected(connected)
    }

    /**
     * 释放资源
     */
    fun release() {
        WsConnectionManager.release()
        Logger.wsm("WebSocket 管理器已释放")
    }

    /**
     * 获取实例
     */
    @JvmStatic
    fun getInstance(): WsManager {
        return this
    }
}
