package com.dspread.mdm.service.modules.geofence.location

import android.content.BroadcastReceiver
import android.content.Context
import android.content.IntentFilter
import android.net.ConnectivityManager
import android.net.wifi.ScanResult
import android.net.wifi.WifiInfo
import android.net.wifi.WifiManager
import android.os.Build
import com.dspread.mdm.service.utils.log.Logger
import org.json.JSONArray
import org.json.JSONObject

/**
 * WiFi位置管理器
 * 用于获取WiFi信息和扫描结果
 */
class WifiLocationManager private constructor(private val context: Context) {
    
    private val wifiManager: WifiManager by lazy {
        context.applicationContext.getSystemService(Context.WIFI_SERVICE) as WifiManager
    }
    
    companion object {
        @Volatile
        private var INSTANCE: WifiLocationManager? = null
        
        fun getInstance(context: Context): WifiLocationManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: WifiLocationManager(context.applicationContext).also { INSTANCE = it }
            }
        }
    }
    
    /**
     * 开始WiFi扫描
     */
    @Synchronized
    fun scanWifi() {
        try {
            wifiManager.startScan()
        } catch (e: Exception) {
            Logger.geoE("WiFi扫描失败", e)
        }
    }
    
    /**
     * 注册WiFi扫描结果接收器
     */
    fun getLocation(receiver: BroadcastReceiver) {
        try {
            context.registerReceiver(
                receiver, 
                IntentFilter(WifiManager.SCAN_RESULTS_AVAILABLE_ACTION)
            )
            wifiManager.startScan()
        } catch (e: Exception) {
            Logger.geoE("注册WiFi扫描接收器失败", e)
        }
    }
    
    /**
     * 获取WiFi扫描结果列表
     */
    fun getWifiList(): List<ScanResult> {
        return try {
            wifiManager.scanResults ?: emptyList()
        } catch (e: Exception) {
            Logger.geoE("获取WiFi扫描结果失败", e)
            emptyList()
        }
    }
    
    /**
     * 获取WiFi扫描结果的JSON数组（去重处理）
     */
    fun getLocationJsonWifi(): JSONArray? {
        return try {
            val wifiList = getWifiList()
            
            if (wifiList.isEmpty()) {
                return null
            }
            
            Logger.geo("WiFi扫描结果数量: ${wifiList.size}")
            
            // 对SSID做去重处理，保留信号值较大的
            val dataMap = mutableMapOf<String, Int>()
            for (result in wifiList) {
                if (result.SSID.isNotEmpty()) {
                    val existingLevel = dataMap[result.SSID]
                    if (existingLevel == null || result.level > existingLevel) {
                        dataMap[result.SSID] = result.level
                    }
                }
            }
            
            // 构建JSON数组
            val towerArray = JSONArray()
            for ((ssid, level) in dataMap) {
                val tower = JSONObject().apply {
                    put("SSID", ssid)
                    put("SSTH", level.toString())
                }
                towerArray.put(tower)
            }
            
            towerArray
            
        } catch (e: Exception) {
            Logger.geoE("获取WiFi JSON信息失败", e)
            null
        }
    }
    
    /**
     * 获取当前连接的WiFi信息JSON
     */
    fun getLocationJsonWifiFirst(): JSONObject? {
        return try {
            val wifiList = getWifiList()
            if (wifiList.isEmpty()) {
                return null
            }
            
            val wifiInfo = getCurrentWifi(context) ?: return null
            
            JSONObject().apply {
                // 处理SSID
                val ssid = if (Build.VERSION.SDK_INT < Build.VERSION_CODES.KITKAT) {
                    wifiInfo.ssid
                } else {
                    wifiInfo.ssid?.replace("\"", "")
                }
                put("SSID", ssid ?: "")
                
                // IP地址
                val ip = wifiInfo.ipAddress
                put("IP", intToIp(ip))
                
                // MAC地址
                put("MAC", wifiInfo.macAddress ?: "")
                
                // 信号强度
                put("SSTH", wifiInfo.rssi.toString())
                
                // 频率
                put("FREQ", getWifiFrequency(wifiInfo.frequency))
                
                // BSSID
                put("BSSID", wifiInfo.bssid ?: "")
            }
            
        } catch (e: Exception) {
            Logger.geoE("获取当前WiFi JSON信息失败", e)
            null
        }
    }
    
    /**
     * 获取当前连接的WiFi信息
     */
    fun getCurrentWifi(context: Context): WifiInfo? {
        return try {
            val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
            val wifiManager = context.applicationContext.getSystemService(Context.WIFI_SERVICE) as WifiManager
            
            @Suppress("DEPRECATION")
            val wifiNetworkInfo = connectivityManager.getNetworkInfo(ConnectivityManager.TYPE_WIFI)
            
            if (wifiNetworkInfo?.isConnected == true) {
                wifiManager.connectionInfo
            } else {
                null
            }
        } catch (e: Exception) {
            Logger.geoE("获取当前WiFi信息失败", e)
            null
        }
    }
    
    /**
     * 获取WiFi SSID
     */
    fun getWifiSSID(context: Context): String {
        return try {
            val apiLevel = Build.VERSION.SDK_INT
            Logger.geo("获取WiFi SSID, API级别: $apiLevel")
            
            when {
                apiLevel <= Build.VERSION_CODES.O || apiLevel >= 28 -> {
                    val wifiManager = context.applicationContext.getSystemService(Context.WIFI_SERVICE) as WifiManager
                    val wifiInfo = wifiManager.connectionInfo
                    
                    if (apiLevel < Build.VERSION_CODES.KITKAT) {
                        wifiInfo.ssid ?: "unknown id"
                    } else {
                        wifiInfo.ssid?.replace("\"", "") ?: "unknown id"
                    }
                }
                
                apiLevel == Build.VERSION_CODES.O_MR1 -> {
                    val connectivityManager = context.applicationContext.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
                    @Suppress("DEPRECATION")
                    val networkInfo = connectivityManager.activeNetworkInfo
                    
                    if (networkInfo?.isConnected == true && networkInfo.extraInfo != null) {
                        networkInfo.extraInfo.replace("\"", "")
                    } else {
                        "unknown id"
                    }
                }
                
                else -> "unknown id"
            }
        } catch (e: Exception) {
            Logger.geoE("获取WiFi SSID失败", e)
            "unknown id"
        }
    }
    
    /**
     * 获取WiFi频率描述
     */
    fun getWifiFrequency(frequency: Int): String {
        return when {
            frequency in 2400..2500 -> "2.4GHz"
            frequency in 4900..5900 -> "5GHz"
            else -> "2.4GHz"
        }
    }
    
    /**
     * 将整数IP地址转换为字符串格式
     */
    private fun intToIp(ip: Int): String {
        return try {
            "${ip and 0xFF}.${(ip shr 8) and 0xFF}.${(ip shr 16) and 0xFF}.${(ip shr 24) and 0xFF}"
        } catch (e: Exception) {
            "0.0.0.0"
        }
    }
}
