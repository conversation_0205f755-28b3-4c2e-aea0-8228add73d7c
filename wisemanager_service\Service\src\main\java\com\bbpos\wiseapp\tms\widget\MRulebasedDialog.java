package com.bbpos.wiseapp.tms.widget;

import android.app.Dialog;
import android.content.Context;
import android.os.Bundle;
import android.os.Handler;
import android.os.Message;
import android.text.TextUtils;
import android.util.DisplayMetrics;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.view.WindowManager;
import android.widget.BaseAdapter;
import android.widget.ImageView;
import android.widget.ListView;
import android.widget.TextView;

import com.bbpos.wiseapp.logger.BBLog;
import com.bbpos.wiseapp.service.R;
import com.bbpos.wiseapp.tms.adapter.SystemManagerAdapter;
import com.bbpos.wiseapp.tms.model.AppInfo;
import com.bbpos.wiseapp.tms.utils.Constants;
import com.bbpos.wiseapp.tms.utils.ContextUtil;
import com.bbpos.wiseapp.tms.utils.Helpers;
import com.bbpos.wiseapp.tms.utils.RuleStatus;
import com.bbpos.wiseapp.tms.utils.RulebasedAppListManager;
import com.bbpos.wiseapp.tms.utils.TaskState;
import com.bbpos.wiseapp.websocket.RulebasedService;
import com.bbpos.wiseapp.websocket.WebSocketSender;
import com.bbpos.wiseapp.websocket.handler.RulebasedListHandler;
import com.bumptech.glide.Glide;

import java.io.File;
import java.util.ArrayList;
import java.util.List;
import java.util.Timer;
import java.util.TimerTask;

public class MRulebasedDialog extends Dialog{
	private Context context;
	public static String mRuleId = "";
	private static String mTaskExeTime = "";
	private String mTxtTitle = "";
	private String mTextSubtitle = "";
	private TextView mTitle;
	private TextView mTimeout;
	private ListView mListView;
	private TextView mBtnDelay;
	private TextView mBtnInstall;
	private View.OnClickListener delayListener;
	private View.OnClickListener installListener;
	public static List<AppInfo> mRulebasedlist = new ArrayList<>();
	public static List<AppInfo> mRulebasedDeletelist = new ArrayList<>();
	public static MRulebasedDialog mDialog;
	public static boolean bRuleInstalling;
	public static boolean bWaitingForInstallRule;
	private AppListAdapter mAdapter;
	private static int totalInstallCount = 0;
	private boolean isCreated = false;
	private Timer mTimer = null;
	private Handler mHandler = new Handler() {
		@Override
		public void handleMessage(Message msg) {
			super.handleMessage(msg);
			if (msg.what >= 0) {
				String str_hour = "";
				String str_min = "";
				String str_sec = "";
				int hour = msg.what/3600;
				int min = (msg.what-hour*3600)/60;
				int sec = msg.what%60;
				if (hour>0 && hour<10) {
					str_hour = "0"+hour+"";
				} else if (hour>=10) {
					str_hour = hour+"";
				}
				if (min>0 && min<10) {
					str_min = "0"+min+"";
				} else if (min>=10) {
					str_min = min+"";
				}
				if (sec>=0 && sec<10) {
					str_sec = "0"+sec+"";
				} else if (sec>=10) {
					str_sec = sec+"";
				}
				mTimeout.setText((hour>0?str_hour+":":"") + (min>0?str_min+":":"") +str_sec);
			} else {
				dismiss();
				try {
					installRulebasedApk();
				} catch (InterruptedException e) {
					e.printStackTrace();
				} catch (Exception e) {
					e.printStackTrace();
				}
				mTimer.cancel();
			}
		}
	};


	public static MRulebasedDialog showRulebasedAppList(Context context, String ruleId, final View.OnClickListener onDelayClickListener, final View.OnClickListener onInstallClickListener){
		BBLog.w(BBLog.TAG, "showRulebasedAppList SIZE = " + mRulebasedlist.size());
		mRuleId = ruleId;
		if (mRulebasedlist.size() == 0) {
			try {
				installRulebasedApk();
			} catch (InterruptedException e) {
				e.printStackTrace();
			} catch (Exception e) {
				e.printStackTrace();
			}
			return null;
		}
		if (mDialog != null) {
			mDialog.dismiss();
			mDialog = null;
		}
		mDialog = new MRulebasedDialog(context) ;
		mDialog.setCanceledOnTouchOutside(false);
		mDialog.setDelayButton(new View.OnClickListener() {
			@Override
			public void onClick(View v) {
				mDialog.dismiss();
				if (mDialog.mTimer != null) {
					mDialog.mTimer.cancel();
				}
				onDelayClickListener.onClick(v);
			}
		});
		mDialog.setInstallButton(new View.OnClickListener() {
			@Override
			public void onClick(View v) {
				mDialog.dismiss();
				if (mDialog.mTimer != null) {
					mDialog.mTimer.cancel();
				}
				onInstallClickListener.onClick(v);
			}
		});

		Window dialogWindow = mDialog.getWindow();
		WindowManager.LayoutParams lp = dialogWindow.getAttributes();
		/*
		 * lp.x与lp.y表示相对于原始位置的偏移.
		 * 当参数值包含Gravity.LEFT时,对话框出现在左边,所以lp.x就表示相对左边的偏移,负值忽略.
		 * 当参数值包含Gravity.RIGHT时,对话框出现在右边,所以lp.x就表示相对右边的偏移,负值忽略.
		 * 当参数值包含Gravity.TOP时,对话框出现在上边,所以lp.y就表示相对上边的偏移,负值忽略.
		 * 当参数值包含Gravity.BOTTOM时,对话框出现在下边,所以lp.y就表示相对下边的偏移,负值忽略.
		 * 当参数值包含Gravity.CENTER_HORIZONTAL时
		 * ,对话框水平居中,所以lp.x就表示在水平居中的位置移动lp.x像素,正值向右移动,负值向左移动.
		 * 当参数值包含Gravity.CENTER_VERTICAL时
		 * ,对话框垂直居中,所以lp.y就表示在垂直居中的位置移动lp.y像素,正值向右移动,负值向左移动.
		 * gravity的默认值为Gravity.CENTER,即Gravity.CENTER_HORIZONTAL |
		 * Gravity.CENTER_VERTICAL.
		 *
		 * 本来setGravity的参数值为Gravity.LEFT | Gravity.TOP时对话框应出现在程序的左上角,但在
		 * 我手机上测试时发现距左边与上边都有一小段距离,而且垂直坐标把程序标题栏也计算在内了,
		 * Gravity.LEFT, Gravity.TOP, Gravity.BOTTOM与Gravity.RIGHT都是如此,据边界有一小段距离
		 */

		WindowManager m = (WindowManager)context.getSystemService(Context.WINDOW_SERVICE);
		DisplayMetrics dm = new DisplayMetrics();; // 获取屏幕宽、高用
		m.getDefaultDisplay().getMetrics(dm);
        WindowManager.LayoutParams p = dialogWindow.getAttributes(); // 获取对话框当前的参数值

		lp.width = (int) (dm.widthPixels); // 宽度
		lp.height = (int) (dm.heightPixels); // 宽度

		dialogWindow.setAttributes(lp);
		dialogWindow.setType(WindowManager.LayoutParams.TYPE_SYSTEM_ALERT);//这句话是关键
		mDialog.show();
		return mDialog;
	}

	TimerTask timerTask = new TimerTask() {
		int second = 300;
		@Override
		public void run() {
			Message msg = new Message();
			msg.what = second;
			mHandler.sendMessage(msg);
			second--;
		}
	};

	@Override
	protected void onCreate(Bundle savedInstanceState) {
		super.onCreate(savedInstanceState);
		init();

		if (mTimer == null) {
			mTimer = new Timer();
			mTimer.schedule(timerTask, 0, 1000);
		}
	}

	public MRulebasedDialog(Context context){
		super(context, R.style.dialog_style);
		this.context = context;
	}

	public MRulebasedDialog(Context context,List<AppInfo> list) {
		super(context);
		this.context = context;
		this.mRulebasedlist.clear();
		this.mRulebasedlist.addAll(list);
		this.mRulebasedDeletelist.clear();
	}

	private void init(){
		 LayoutInflater inflater = LayoutInflater.from(context);
		 View view = inflater.inflate(R.layout.dialog_rulebased,null);
		 setContentView(view);
		 isCreated = true;
		 findControls(view);

		mAdapter = new AppListAdapter(context, mRulebasedlist);
		mListView.setAdapter(mAdapter);
	}

	private void findControls(View view){
		mTitle = (TextView) view.findViewById(R.id.tv_title);
		mTimeout = (TextView)view.findViewById(R.id.tv_timeout);
		mListView = (ListView) view.findViewById(R.id.list_app);
		mBtnDelay = (TextView)view.findViewById(R.id.tv_delay);
		mBtnDelay.setOnClickListener(delayListener);
		mBtnInstall = (TextView)view.findViewById(R.id.tv_install);
		mBtnInstall.setOnClickListener(installListener);
	}

	/** 
	* @Description  设置确定键内容（右键）
	* @param listener
	* @return void    
	*/
	public void setDelayButton(final View.OnClickListener listener){
		delayListener = listener;
		if(isCreated){
			mBtnDelay.setOnClickListener(delayListener);
		}
	}

	/** 
	* @Description  设置取消键内容（左键）
	* @param listener
	* @return void    
	*/
	public void setInstallButton(final View.OnClickListener listener){
		installListener = listener;
		if(isCreated){
			mBtnInstall.setOnClickListener(installListener);
		}
	}

	class AppListAdapter extends BaseAdapter {
		private List<AppInfo> list_item = null;
		private int selectItem;
		private Context context;

		public AppListAdapter(Context context, List<AppInfo> list) {
			super();
			this.context = context;
			list_item = list;
		}

		@Override
		public int getCount() {
			return list_item != null ? this.list_item.size() : 0;
		}

		@Override
		public Object getItem(int position) {
			return list_item != null ? list_item.get(position) : null;
		}

		@Override
		public long getItemId(int position) {
			return position;
		}

		@Override
		public View getView(int position, View convertView, ViewGroup parent) {
			if (list_item == null) {
				return null;
			}

			ViewHolder viewHolder = null;
			if (convertView == null) {
				convertView = LayoutInflater.from(context).inflate(R.layout.item_app, parent, false);
				viewHolder = new ViewHolder();
				viewHolder.mApkName = (TextView) convertView.findViewById(R.id.tv_apkname);
				viewHolder.mVersionName = (TextView) convertView.findViewById(R.id.tv_versionname);
				viewHolder.mVersionCode = (TextView) convertView.findViewById(R.id.tv_versioncode);
				viewHolder.mApkIcon = (ImageView) convertView.findViewById(R.id.iv_icon);
				convertView.setTag(viewHolder);
			} else {
				viewHolder = (ViewHolder) convertView.getTag();
			}

			viewHolder.mApkName.setText(list_item.get(position).getApk_name());
			viewHolder.mVersionName.setText(list_item.get(position).getVersion_name());
			viewHolder.mVersionCode.setText(list_item.get(position).getVersion_code());
			if (!TextUtils.isEmpty(list_item.get(position).getIcon_url())) {
				viewHolder.mApkIcon.setBackground(null);
				Glide.with(context).load(list_item.get(position).getIcon_url()).into(viewHolder.mApkIcon);
			}

			return convertView;
		}

		private class ViewHolder {
			public TextView mApkName;
			public TextView mVersionName;
			public TextView mVersionCode;
			public ImageView mApkIcon;
		}
	}

	private static AppInfo m_appInfo;
	private static int m_totalInstallCount = 0;
	private static SystemManagerAdapter.ApkInstallCompleted m_obsever = new SystemManagerAdapter.ApkInstallCompleted() {
		@Override
		public void onInstallFinished(String pkgName, int returnCode) {
			try {
				// TODO Auto-generated method stub
				final File apkFile = new File(m_appInfo.getInstall_path() + ".apk");
				final File apkFileOrg = new File(m_appInfo.getInstall_path() + Constants.APK_ORIGINAL_SUFFIX + ".apk");

				//安装成功后上送安装结果
				if (returnCode == 1) {
					if (apkFile.exists()) {// 安装成功后删除apk文件
						apkFile.delete();
					} else if (apkFileOrg.exists()) {// 安装成功后删除apk文件
						apkFileOrg.delete();
					}

					RulebasedService.updateResultAppStatus(m_appInfo.getPackage_name(), TaskState.INSTALL_SUCCESS, null);
				} else {
					if (apkFile.exists()) {// 安装失败后删除apk文件
						apkFile.delete();
					} else if (apkFileOrg.exists()) {// 安装成功后删除apk文件
						apkFileOrg.delete();
					}

					RulebasedService.anyFailed = true;
					RulebasedService.updateResultAppStatus(m_appInfo.getPackage_name(), TaskState.INSTALL_FAILED, null);
					RulebasedListHandler.updateRuleAppListState(mRuleId, m_appInfo.getPackage_name(), m_appInfo.getVersion_name(), m_appInfo.getVersion_code(), TaskState.INSTALL_FAILED);
				}

				m_totalInstallCount--;
				BBLog.e(BBLog.TAG, m_appInfo.getApk_name() + "安装完成 剩餘" + m_totalInstallCount + "個app");
				mRulebasedlist.remove(m_appInfo);
			} catch (Exception e) {
				e.printStackTrace();
			}

			if (mRulebasedlist.size() > 0) {
				m_appInfo = mRulebasedlist.get(0);
				int ret = RulebasedAppListManager.isApkInstalled(m_appInfo.getPackage_name(), m_appInfo.getVersion_name(), m_appInfo.getVersion_code());
				if (ret == 2) {
					SystemManagerAdapter.unInstallApk(ContextUtil.getInstance(), m_appInfo.getPackage_name(), new SystemManagerAdapter.ApkUnInstallCompleted() {
						@Override
						public void onDeleteFinished(int returnCode) {
							BBLog.i(Constants.TAG, "packageDeleted " + m_appInfo.getPackage_name() + " returnCode " + returnCode);
							if (returnCode == 1) {
								installRuleApk(m_appInfo);
							}
						}
					});
				} else {
					installRuleApk(m_appInfo);
				}
			} else {
				bRuleInstalling = false;

				RulebasedService.onRuleExecuteCompleted(mRuleId);

				if (MAppPlusDialog.bWaitingForInstallAppPlus) {
					MAppPlusDialog.bWaitingForInstallAppPlus = false;
					try {
						MAppPlusDialog.installAppPlusApk(ContextUtil.getInstance());
					} catch (InterruptedException e) {
						e.printStackTrace();
					}
				}
			}
		}
	};

	public static void installRulebasedApk() throws InterruptedException {
		if (MAppPlusDialog.bAppPlusInstalling) {
			BBLog.e(BBLog.TAG, "--- App+正在安装，等待安装Rule ---");
			bWaitingForInstallRule = true;
			return;
		}
		BBLog.e(BBLog.TAG, "--- 开始安装Rule --- " + mRulebasedlist.size());
		bRuleInstalling = true;
		if (mRulebasedlist.size() == 0) {
			bRuleInstalling = false;

			RulebasedService.onRuleExecuteCompleted(mRuleId);

			return;
		}

		try {
			m_totalInstallCount = mRulebasedlist.size();
			m_appInfo = mRulebasedlist.get(0);
			int ret = RulebasedAppListManager.isApkInstalled(m_appInfo.getPackage_name(), m_appInfo.getVersion_name(), m_appInfo.getVersion_code());
			if (ret == 2) {
				SystemManagerAdapter.unInstallApk(ContextUtil.getInstance(), m_appInfo.getPackage_name(), new SystemManagerAdapter.ApkUnInstallCompleted() {
					@Override
					public void onDeleteFinished(int returnCode) {
						BBLog.i(Constants.TAG, "packageDeleted " + m_appInfo.getPackage_name() + " returnCode " + returnCode);
						if (returnCode == 1) {
							installRuleApk(m_appInfo);
						}
					}
				});
			} else {
				installRuleApk(m_appInfo);
			}
		} catch (Exception e) {
			e.printStackTrace();
			bRuleInstalling = false;
		}
	}

	private static void installRuleApk(final AppInfo appInfo) {
		SystemManagerAdapter.installApk(ContextUtil.getInstance(), appInfo.getInstall_path(), m_obsever);
	}
}
