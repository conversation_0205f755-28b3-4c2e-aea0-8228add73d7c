package com.dspread.mdm.service.platform.api.upgrade

import android.content.Context
import android.os.Build
import com.dspread.mdm.service.utils.log.Logger
import com.dspread.mdm.service.platform.api.model.SystemOperationResult
import java.io.File

/**
 * 升级策略工厂
 * 根据Android版本和设备特性自动选择合适的升级策略
 */
class UpgradeStrategyFactory(private val context: Context) {

    companion object {
        private const val TAG = "UpgradeStrategyFactory"
    }

    private val updateEngineStrategy = UpdateEngineUpgradeStrategy(context)
    private val recoverySystemStrategy = RecoverySystemUpgradeStrategy(context)

    /**
     * 升级策略类型
     */
    enum class StrategyType {
        UPDATE_ENGINE,      // UpdateEngine策略 (Android 14+ 或支持A/B分区)
        RECOVERY_SYSTEM     // RecoverySystem策略 (传统升级方式)
    }

    /**
     * 升级策略信息
     */
    data class StrategyInfo(
        val type: StrategyType,
        val name: String,
        val description: String,
        val supported: <PERSON>olean,
        val reason: String
    )

    /**
     * 获取推荐的升级策略
     */
    fun getRecommendedStrategy(updateFile: File): StrategyInfo {
        return try {
            Logger.platformI("$TAG 分析升级策略...")
            Logger.platformI("$TAG Android版本: ${Build.VERSION.SDK_INT} (${Build.VERSION.RELEASE})")
            Logger.platformI("$TAG 更新文件: ${updateFile.name}")

            // 检查文件格式
            val isPayloadFormat = checkIfPayloadFormat(updateFile)
            val isZipFormat = checkIfZipFormat(updateFile)
            
            Logger.platformI("$TAG 文件格式 - Payload: $isPayloadFormat, ZIP: $isZipFormat")

            when {
                Build.VERSION.SDK_INT >= 34 -> {
                    // Android 14+ 强制使用UpdateEngine
                    if (isPayloadFormat) {
                        StrategyInfo(
                            type = StrategyType.UPDATE_ENGINE,
                            name = "UpdateEngine (Android 14+)",
                            description = "Android 14+ 使用UpdateEngine进行A/B分区升级",
                            supported = true,
                            reason = "Android 14+ 强制要求使用UpdateEngine"
                        )
                    } else {
                        StrategyInfo(
                            type = StrategyType.UPDATE_ENGINE,
                            name = "UpdateEngine (Android 14+)",
                            description = "Android 14+ 使用UpdateEngine进行A/B分区升级",
                            supported = false,
                            reason = "Android 14+ 不支持ZIP格式OTA包，需要Payload格式"
                        )
                    }
                }
                
                updateEngineStrategy.isSupported() && isPayloadFormat -> {
                    // Android 7-13 且支持A/B分区，使用UpdateEngine
                    StrategyInfo(
                        type = StrategyType.UPDATE_ENGINE,
                        name = "UpdateEngine (A/B分区)",
                        description = "支持A/B分区的设备使用UpdateEngine升级",
                        supported = true,
                        reason = "设备支持A/B分区且OTA包为Payload格式"
                    )
                }
                
                recoverySystemStrategy.isSupported() && isZipFormat -> {
                    // 传统升级方式
                    StrategyInfo(
                        type = StrategyType.RECOVERY_SYSTEM,
                        name = "RecoverySystem (传统)",
                        description = "传统设备使用RecoverySystem升级",
                        supported = true,
                        reason = "设备不支持A/B分区或OTA包为ZIP格式"
                    )
                }
                
                else -> {
                    // 无合适策略
                    val reason = when {
                        !isPayloadFormat && !isZipFormat -> "无法识别OTA包格式"
                        isPayloadFormat && !updateEngineStrategy.isSupported() -> "设备不支持UpdateEngine但OTA包为Payload格式"
                        isZipFormat && !recoverySystemStrategy.isSupported() -> "设备不支持RecoverySystem但OTA包为ZIP格式"
                        else -> "未知原因"
                    }
                    
                    StrategyInfo(
                        type = StrategyType.RECOVERY_SYSTEM, // 默认fallback
                        name = "无合适策略",
                        description = "无法找到合适的升级策略",
                        supported = false,
                        reason = reason
                    )
                }
            }

        } catch (e: Exception) {
            Logger.platformE("$TAG 分析升级策略失败", e)
            StrategyInfo(
                type = StrategyType.RECOVERY_SYSTEM,
                name = "错误",
                description = "分析升级策略时发生错误",
                supported = false,
                reason = "分析异常: ${e.message}"
            )
        }
    }

    /**
     * 执行升级
     */
    fun performUpgrade(
        updateFile: File,
        taskId: String? = null,
        listener: UpdateEngineUpgradeStrategy.UpdateStatusListener? = null
    ): SystemOperationResult {
        return try {
            val strategyInfo = getRecommendedStrategy(updateFile)
            
            Logger.platformI("$TAG 选择升级策略: ${strategyInfo.name}")
            Logger.platformI("$TAG 策略描述: ${strategyInfo.description}")
            Logger.platformI("$TAG 支持状态: ${strategyInfo.supported}")
            Logger.platformI("$TAG 选择原因: ${strategyInfo.reason}")

            if (!strategyInfo.supported) {
                return SystemOperationResult.failure("升级策略不支持: ${strategyInfo.reason}")
            }

            when (strategyInfo.type) {
                StrategyType.UPDATE_ENGINE -> {
                    Logger.platformI("$TAG 使用UpdateEngine策略执行升级")
                    updateEngineStrategy.performUpgrade(updateFile, taskId, listener)
                }
                
                StrategyType.RECOVERY_SYSTEM -> {
                    Logger.platformI("$TAG 使用RecoverySystem策略执行升级")
                    recoverySystemStrategy.performUpgrade(updateFile, taskId)
                }
            }

        } catch (e: Exception) {
            Logger.platformE("$TAG 执行升级失败", e)
            SystemOperationResult.failure("升级执行异常: ${e.message}")
        }
    }

    /**
     * 获取所有策略的支持状态
     */
    fun getAllStrategiesStatus(): Map<String, Any> {
        return try {
            mapOf(
                "android_version" to Build.VERSION.SDK_INT,
                "android_release" to Build.VERSION.RELEASE,
                "update_engine_supported" to updateEngineStrategy.isSupported(),
                "recovery_system_supported" to recoverySystemStrategy.isSupported(),
                "ab_partition" to getSystemProperty("ro.build.ab_update"),
                "current_slot" to getSystemProperty("ro.boot.slot_suffix"),
                "device_model" to Build.MODEL,
                "device_manufacturer" to Build.MANUFACTURER
            )
        } catch (e: Exception) {
            mapOf("error" to "获取策略状态失败: ${e.message}")
        }
    }

    /**
     * 检查是否为Payload格式
     */
    private fun checkIfPayloadFormat(file: File): Boolean {
        return try {
            val buffer = ByteArray(4)
            file.inputStream().use { input ->
                input.read(buffer)
            }

            val magic = String(buffer, Charsets.US_ASCII)
            val isDirectPayload = magic == "CrAU"
            
            // 检查ZIP是否包含payload.bin
            val containsPayload = if (!isDirectPayload && magic == "PK\u0003\u0004") {
                checkZipContainsPayload(file)
            } else {
                false
            }

            isDirectPayload || containsPayload

        } catch (e: Exception) {
            Logger.platformW("$TAG 检查Payload格式失败: ${e.message}")
            false
        }
    }

    /**
     * 检查是否为ZIP格式
     */
    private fun checkIfZipFormat(file: File): Boolean {
        return try {
            val buffer = ByteArray(4)
            file.inputStream().use { input ->
                input.read(buffer)
            }

            val magic = String(buffer, Charsets.US_ASCII)
            magic == "PK\u0003\u0004"

        } catch (e: Exception) {
            Logger.platformW("$TAG 检查ZIP格式失败: ${e.message}")
            false
        }
    }

    /**
     * 检查ZIP是否包含payload.bin
     */
    private fun checkZipContainsPayload(zipFile: File): Boolean {
        return try {
            java.util.zip.ZipFile(zipFile).use { zip ->
                val hasPayload = zip.getEntry("payload.bin") != null
                val hasProperties = zip.getEntry("payload_properties.txt") != null
                hasPayload && hasProperties
            }
        } catch (e: Exception) {
            false
        }
    }

    /**
     * 获取系统属性
     */
    private fun getSystemProperty(key: String): String {
        return try {
            val process = Runtime.getRuntime().exec("getprop $key")
            process.inputStream.bufferedReader().readText().trim()
        } catch (e: Exception) {
            ""
        }
    }

    /**
     * 清理所有策略的资源
     */
    fun cleanup() {
        try {
            updateEngineStrategy.release()
            recoverySystemStrategy.cleanupUpdateFiles()
            Logger.platformI("$TAG 升级策略资源清理完成")
        } catch (e: Exception) {
            Logger.platformW("$TAG 清理资源失败: ${e.message}")
        }
    }
}
