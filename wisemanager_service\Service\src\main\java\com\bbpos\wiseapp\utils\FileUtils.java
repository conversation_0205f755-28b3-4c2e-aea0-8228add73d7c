package com.bbpos.wiseapp.utils;

import android.content.pm.PackageInfo;
import android.os.Environment;
import android.text.TextUtils;
import android.util.Log;

import com.bbpos.wiseapp.logger.BBLog;
import com.bbpos.wiseapp.service.common.UsualData;
import com.bbpos.wiseapp.tms.utils.Constants;
import com.bbpos.wiseapp.tms.utils.ContextUtil;
import com.bbpos.wiseapp.tms.utils.Helpers;
import com.bbpos.wiseapp.tms.utils.IOUtils;

import org.json.JSONObject;

import java.io.BufferedReader;
import java.io.DataOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.FileReader;
import java.io.IOException;
import java.io.PrintWriter;
import java.text.SimpleDateFormat;

public class FileUtils {
    private final static String TAG = BBLog.TAG;//"FileUtils";

    public final static String FILE_SEPARATOR = "/";
    public final static String FILE_FORMAT = ".txt";
    public final static String CONFIG_CATALOG = "wiseapp";
    public final static String CONFIG_NAME = "config";
    public final static String WIFI_CONFIG_NAME = "wpl_config";
    public final static String ALLOW_PKGS_UPLOAD_CONFIG_NAME = "allow_pkgs_upload_config";
    public final static String FILE_LOG = "logo.png";
    public final static String FILE_WALLPAPER = "wallpaper.png";
    public final static String FILE_CUSTOMER_CERT = "cert.con";

    public static SimpleDateFormat mSimpleDateFormat = new SimpleDateFormat("yyyy-MM-dd hh:mm:ss SSS");

    public static final String FILE_NAME = "FallTest_Log.txt";
    public static final String FALL_COUNT = "tst_fall_count";

    public static String getWiseAppConfigPath() {
        File sdCardPath = null;
        String wiseAppConfigPath = null;
        //判断sd卡是否存在
        boolean sdCardExist = Environment.getExternalStorageState().equals(Environment.MEDIA_MOUNTED);
        if(sdCardExist) {
            sdCardPath = Environment.getExternalStorageDirectory();//获取跟目录
        }

        if (!TextUtils.isEmpty(sdCardPath.toString())) {
            wiseAppConfigPath = sdCardPath.toString() + FILE_SEPARATOR + CONFIG_CATALOG;
        }

        return wiseAppConfigPath;
    }

    public static String getConfigFilePath() {
        String configFilePath = "";
//        if (new File("/bbpos").exists()) {
//            configFilePath = "/bbpos" + FileUtils.FILE_SEPARATOR + FileUtils.CONFIG_NAME + FileUtils.FILE_FORMAT;
//        }

        if (TextUtils.isEmpty(configFilePath)) {
            String configPath = getWiseAppConfigPath();
            if (TextUtils.isEmpty(configPath)) {
                BBLog.e(BBLog.TAG, "config file path is invalid...");
                return configFilePath;
            }
            File configPathFile = new File(configPath);
            if (!configPathFile.exists()) {
                configPathFile.mkdir();
            }
            configFilePath = configPath + FILE_SEPARATOR + CONFIG_NAME + FILE_FORMAT;
        }

        return configFilePath;
    }

	public static String getWifiConfigFilePath() {
		String wifiConfigFilePath = "";
		try {
			if (new File("/bbpos").exists()) {
				wifiConfigFilePath = "/bbpos" + FileUtils.FILE_SEPARATOR + FileUtils.WIFI_CONFIG_NAME + FileUtils.FILE_FORMAT;

				File WifConfigPathFile = new File(wifiConfigFilePath);
				if (!WifConfigPathFile.exists()) {
					boolean result = WifConfigPathFile.createNewFile();
					BBLog.e(TAG, "getWifiConfigFilePath: result = "+ result );
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		BBLog.e(TAG, "getWifiConfigFilePath: "+ wifiConfigFilePath );
		return wifiConfigFilePath;
	}

	public static boolean writeWifiConfigToFile(String result) {
    	if (TextUtils.isEmpty(result)) {
    	    return false;
        }
		String configFilePath = getWifiConfigFilePath();
		BBLog.e(BBLog.TAG, "WPL config file path is " + configFilePath);
		File file = new File(configFilePath);
        DataOutputStream out = null;
        try {
			if (file.exists() && file.isFile()){
				file.delete();
			}
			if (!file.isFile()) {
				file.createNewFile();
			}

			out = new DataOutputStream(new FileOutputStream(file));
			out.writeBytes(result);
			out.close();
			return true;
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
            IOUtils.flushCloseOutputStream(out);
        }
		return false;
	}

    public static String getAllowPaymentPkgsConfigPath() {
        String allowPaymentPkgsConfigPath = "";
        try {
            if (new File("/bbpos").exists()) {
                allowPaymentPkgsConfigPath = "/bbpos" + FileUtils.FILE_SEPARATOR + FileUtils.ALLOW_PKGS_UPLOAD_CONFIG_NAME + FileUtils.FILE_FORMAT;

                File configPathFile = new File(allowPaymentPkgsConfigPath);
                if (!configPathFile.exists()) {
                    boolean result = configPathFile.createNewFile();
                    BBLog.e(TAG, "getAllowPaymentPkgsConfigPath: result = "+ result );
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        BBLog.e(TAG, "getAllowPaymentPkgsConfigPath: "+ allowPaymentPkgsConfigPath );
        return allowPaymentPkgsConfigPath;
    }

    public static void saveAllowPaymentPks(String pkgs) {
        String path = getAllowPaymentPkgsConfigPath();
        if (TextUtils.isEmpty(path)) {
            SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_ALLOW_PAYMENT_DATA,pkgs);
        } else {
            File file = null;
            FileOutputStream fos = null;
            try {
                file = new File("/bbpos" + FILE_SEPARATOR + FileUtils.ALLOW_PKGS_UPLOAD_CONFIG_NAME + FileUtils.FILE_FORMAT);
                if(file.exists()){
                    file.delete();
                }
                file.createNewFile();
                fos = new FileOutputStream(file);
                PrintWriter pw = new PrintWriter(fos,true);
                pw.println(pkgs);
                pw.close();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    public static String getAllowPaymentPks() {
        String path = getAllowPaymentPkgsConfigPath();
        if (TextUtils.isEmpty(path)) {
            return SharedPreferencesUtils.getSharePreferencesValue(UsualData.SHARED_PREFERENCES_ALLOW_PAYMENT_DATA,"");
        }else {
           return readFile("/bbpos" + FILE_SEPARATOR + FileUtils.ALLOW_PKGS_UPLOAD_CONFIG_NAME + FileUtils.FILE_FORMAT);
        }
    }

    public static void writeFile(String result) {
        String configFilePath = getConfigFilePath();

        BBLog.e(BBLog.TAG, "config file path is " + configFilePath);
        File file = new File(configFilePath);
        DataOutputStream out = null;
        try {
            if (!file.isFile()) {
                file.createNewFile();
            }

            out = new DataOutputStream(new FileOutputStream(file));
            out.writeBytes(result);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            IOUtils.flushCloseOutputStream(out);
        }
    }

    public static boolean writeFile(String path, String fileName, String str, boolean append) {
        File file = null;
        FileOutputStream fos = null;
        try {
            file = new File(path + FILE_SEPARATOR + fileName);
            if(file.exists()){
                if (append) {
                    fos = new FileOutputStream(file, append);//这里构造方法多了一个参数true,表示在文件末尾追加写入
                } else {
                    file.delete();
                    file.createNewFile();
                    fos = new FileOutputStream(file);
                }
            } else {
                file.createNewFile();
                fos = new FileOutputStream(file);
            }

            PrintWriter pw = new PrintWriter(fos,true);
            pw.println(str);
            pw.close();

            return true;
        } catch (Exception e) {
            e.printStackTrace();
        }

        return false;
    }

    // 读文件，返回字符串
    public static String readFile(String path) {
        File file = new File(path);
        if (!file.exists()) {
            return null;
        }

        BufferedReader reader = null;
        String laststr = "";
        try {
            reader = new BufferedReader(new FileReader(file));
            String tempString = null;
            // 一次读入一行，直到读入null为文件结束
            while ((tempString = reader.readLine()) != null) {
                laststr = laststr + tempString;
            }
            reader.close();
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (reader != null) {
                try {
                    reader.close();
                } catch (IOException e1) {
                }
            }
        }
        Log.e(TAG, "readFile: pkg = "+laststr );
        return laststr;
    }

    /**
     * 删除文件夹
     * @param folderPath 文件夹完整绝对路径 ,"Z:/xuyun/save"
     */
    public static void delFolder(String folderPath) {
        try {
            delAllFile(folderPath); //删除完里面所有内容
            String filePath = folderPath;
            filePath = filePath.toString();
            java.io.File myFilePath = new java.io.File(filePath);
            myFilePath.delete(); //删除空文件夹
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 删除指定文件夹下所有文件
     * @param path 文件夹完整绝对路径 ,"Z:/xuyun/save"
     */
    public static boolean delAllFile(String path) {
        boolean flag = false;
        File file = new File(path);
        if (!file.exists()) {
            return flag;
        }
        if (!file.isDirectory()) {
            return flag;
        }
        String[] tempList = file.list();
        File temp = null;
        for (int i = 0; i < tempList.length; i++) {
            if (path.endsWith(File.separator)) {
                temp = new File(path + tempList[i]);
            } else {
                temp = new File(path + File.separator + tempList[i]);
            }
            if (temp.isFile()) {
                temp.delete();
            }
            if (temp.isDirectory()) {
                delAllFile(path + "/" + tempList[i]);//先删除文件夹里面的文件
                delFolder(path + "/" + tempList[i]);//再删除空文件夹
                flag = true;
            }
        }
        return flag;
    }

    public static void deleteFilesByDirectory(File directory) {
        if (directory != null && directory.exists() && directory.isDirectory()) {
            for (File item : directory.listFiles()) {
                item.delete();
            }
        }
    }

    public static long getTotalSizeOfFilesInDir(final File file) {
        if (file.isFile()) {
            return file.length();
        }

        final File[] children = file.listFiles();
        long total = 0;
        if (children != null) {
            for (final File child : children) {
                total += getTotalSizeOfFilesInDir(child);
            }
        }
        return total;
    }

    public static void saveOSUpgradeStatus(JSONObject jsonObject) {
        if (jsonObject == null) return;
        try {
            Log.e(BBLog.TAG, "saveOSUpgradeStatus:  jsonObject= " + jsonObject.toString() );
            //WM 1.7.9以上版本支持重启后显示升级结果弹窗提示
            PackageInfo packageInfo = ContextUtil.getInstance().getApplicationContext()
                    .getPackageManager()
                    .getPackageInfo(ContextUtil.getInstance().getPackageName(), 0);
            int versionCode = packageInfo.versionCode;

            if (versionCode < 129) return;
            File file = new File(Constants.OTA_UPGRADE_STATUS_PATH);
            if (file.exists()){
                file.delete();
            } else{
                if (!file.getParentFile().exists()) {
                    file.getParentFile().mkdirs();
                }
            }
            file.createNewFile();
            FileOutputStream outputStream = new FileOutputStream(file);
            outputStream.write(jsonObject.toString().getBytes());
            outputStream.flush();
            outputStream.close();
        } catch (Exception e) {
            e.printStackTrace();
            Log.e(BBLog.TAG, "saveOSUpgradeStatus: errror = " + e.getMessage() );
        }
    }
}
