package com.bbpos.wiseapp.tms.utils;

import android.annotation.SuppressLint;
import android.annotation.TargetApi;
import android.app.IntentService;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import android.content.pm.PackageManager;
import android.content.pm.ResolveInfo;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import android.os.Build;
import android.os.Handler;
import android.os.Looper;
import android.preference.PreferenceManager;
import android.text.TextUtils;
import android.widget.Toast;

import com.bbpos.wiseapp.logger.BBLog;
import com.bbpos.wiseapp.param.manager.ParamManager;
import com.bbpos.wiseapp.param.manager.ParamModel;
import com.bbpos.wiseapp.service.common.RequestPermission;
import com.bbpos.wiseapp.service.common.UsualData;
import com.bbpos.wiseapp.service.contentprovider.db.ParamDbOperation;
import com.bbpos.wiseapp.service.receiver.CloudReceiver;
import com.bbpos.wiseapp.service.receiver.OTAUpdateReceiver;
import com.bbpos.wiseapp.tms.receiver.TmsReceiver;
import com.bbpos.wiseapp.tms.timer.PollTimer;
import com.bbpos.wiseapp.tms.timer.TerminalInfoUploadTimer;
import com.bbpos.wiseapp.tms.timer.WiseLogUploadTimer;
import com.bbpos.wiseapp.utils.ActivityUtils;
import com.bbpos.wiseapp.utils.SharedPreferencesUtils;
import com.bbpos.wiseapp.websocket.WebSocketReceiver;
import com.bbpos.wiseapp.websocket.WebSocketSender;

import org.json.JSONArray;
import org.json.JSONObject;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigInteger;
import java.security.MessageDigest;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

@TargetApi(Build.VERSION_CODES.HONEYCOMB_MR1)
@SuppressLint({ "SimpleDateFormat", "ShowToast", "UseValueOf", "NewApi" })
public class Helpers {
	public static final SharedPreferences sp = PreferenceManager.getDefaultSharedPreferences(ContextUtil.getInstance());

	public static boolean isStrEmpty(String str) {
		if(str!=null && !"".equals(str))
			return false;
		return true;
	}

	public static boolean isXmlParaHadParsed(Context mContext,String filePath) {
		File xmlParaFile = new File(filePath);
		String fileMd5 = FileUtils.getMd5ByFile(xmlParaFile);
		long lastModifyTime = xmlParaFile.lastModified();
		return ParamDbOperation.isParamFileParsed(mContext, filePath, fileMd5, lastModifyTime);
	}

	/***
	 * 解析并更新par文件
	 * @param paramList
	 */
	public static void updateParParam(List<ParamModel> paramList) {
		String paramFile = null;
		String paramModule = null;

		for (ParamModel paraModel : paramList) {
			if(paraModel.key.equals(ParamModel.ParamFile)) {
				paramFile = paraModel.value;
			}else if(paraModel.key.equals(ParamModel.ParamModule)) {
				paramModule = paraModel.value;
			}

			if(paramFile!=null && paramModule!=null)
				break;
		}
		updateParParam(paramList,paramModule,paramFile);
	}

	/***
	 * 解析并更新par文件
	 * @param paramList
	 */
	public static void updateParParam(List<ParamModel> paramList,String paramModule,String paramFile) {
		ParamManager pm = new ParamManager();
		for (ParamModel param : paramList) {
			if(!param.key.equals(ParamModel.PackageName)
			&& !param.key.equals(ParamModel.ParamFile)
			&& !param.key.equals(ParamModel.ParamModule)) {
				pm.updateParam(ContextUtil.getInstance().getApplicationContext(), param.key, param.value);
			}
		}
	}

	/** 是否有网络 */
	public static boolean isOnline(Context context) {
		ConnectivityManager cm = (ConnectivityManager) context.getSystemService(Context.CONNECTIVITY_SERVICE);
		NetworkInfo netInfo = cm.getActiveNetworkInfo();
		if (netInfo != null && netInfo.isConnected()) {
			return true;
		}
		return false;
	}

	/**判断返回码是否为01*/
	public static boolean isReturnSuccess(JSONObject json) {
		try {
			if(json == null) {
				BBLog.e(BBLog.TAG, "response is null!");
				return false;
			}
			if (json.has(ParameterName.returnCode)) {
				if (ServerReturnCode.SUCCESS.equals(json
						.get(ParameterName.returnCode))) {
					return true;
				} else if(ServerReturnCode.NO_TER_IN_SERVER.equals(json
						.get(ParameterName.returnCode))) {
					BBLog.e(BBLog.TAG, "returnCode is not "+ ServerReturnCode.SUCCESS+",returnCode:"+json.get(ParameterName.returnCode));
					BBLog.e(BBLog.TAG, "ter info not resigst in server.");
					return false;
				} else {
					BBLog.e(BBLog.TAG, "returnCode is not "+ ServerReturnCode.SUCCESS+",returnCode:"+json.get(ParameterName.returnCode));
					return false;
				}
			} else {
				BBLog.e(BBLog.TAG, "error on get returnCode:no returnCode");
				return false;
			}
		} catch (Exception e) {
			e.printStackTrace();
			BBLog.e(BBLog.TAG, "error on get returnCode:no returnCode",e);
			return false;
		}
	}

	/**从返回的json中获取data*/
	public static JSONObject getDataFromResponse(JSONObject json) {
		try {
			if(json == null) {
				BBLog.e(BBLog.TAG, "response is null!");
				return null;
			}
			if (json.has(ParameterName.DATA)) {
				if(json.getString(ParameterName.DATA)!=null&&!json.getString(ParameterName.DATA).equals("null")) {
					return json.getJSONObject(ParameterName.DATA);
				} else {
					return null;
				}
			} else {
				BBLog.e(BBLog.TAG, "error on getDataFromResponse");
				return null;
			}
		} catch (Exception e) {
			e.printStackTrace();
			BBLog.e(BBLog.TAG, "error on getDataFromResponse",e);
			return null;
		}
	}

	/**启动任务结果上送服务*/
	public static void updateWSTaskStateAndUpload(Context context,String taskId,String taskState,String errMsg) {
		WebSocketTaskListManager.updateWSTaskState(taskId, taskState);
		WebSocketSender.C0108_uploadWSTaskResult(taskId, errMsg);
	}

	public static String getSPParam(Context context, String key,
			String defaultValue) {
		SharedPreferences sp = PreferenceManager
				.getDefaultSharedPreferences(context);
		return sp.getString(key, defaultValue);
	}

	public static int parseInt(String str) {
		try {
			return Integer.parseInt(str);
		} catch(Exception e) {
			e.printStackTrace();
			return 0;
		}
	}

	/**获取传输格式日期字符串*/
	public static String getTransDateStr(Long time) {
		SimpleDateFormat sdfName = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		return sdfName.format(new Date(time));
	}

	/**获取传输格式日期字符串*/
	public static String getTransDateStr(Date date) {
		SimpleDateFormat sdfName = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		return sdfName.format(date);
	}

	/**获取传输格式日期字符串*/
	public static Date getTransDate(String dateStr) {
		if (TextUtils.isEmpty(dateStr)) return null;
		SimpleDateFormat sdfName = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		try {
			return sdfName.parse(dateStr);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return null;
	}

	public static void makeToast(Context contex,String text) {
		Toast.makeText(contex, text, Toast.LENGTH_LONG).show();
	}

	public static void showToastByRunnable(final IntentService context, final String text) {
		Handler handler = new Handler(Looper.getMainLooper());
		handler.post(new Runnable() {
			@Override
			public void run() {
				Toast.makeText(context, text, Toast.LENGTH_LONG).show();
			}
		});
	}

	public static void sleep(long time) {
		try {
			Thread.sleep(time);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	public static String getStrMD5(String tmp) {
		MessageDigest messageDigest;
		try {
			messageDigest = MessageDigest.getInstance("MD5");
			messageDigest.update(tmp.getBytes());
			BigInteger bi = new BigInteger(1, messageDigest.digest());
			int md5Length = 32;
			String md5 = bi.toString(16);//16进制
			if (md5.length() < md5Length) {
				for (int i = md5.length(); i < md5Length; i++) {
					md5 = "0" + md5;
				}
			}
			return md5;
		} catch (Exception e) {
			e.printStackTrace();
			return null;
		}
	}

	/** 
	 * make true current connect service is wifi 
	 * @param mContext 
	 * @return 
	 */  
	public static boolean isWifi(Context mContext) {  
		ConnectivityManager connectivityManager = (ConnectivityManager) mContext  
				.getSystemService(Context.CONNECTIVITY_SERVICE);  
		NetworkInfo activeNetInfo = connectivityManager.getActiveNetworkInfo();  
		if (activeNetInfo != null  
				&& activeNetInfo.getType() == ConnectivityManager.TYPE_WIFI) {  
			BBLog.v(BBLog.TAG, "has wifi");
			return true;  
		}  
		return false;  
	} 

	/**
	 * make true current connect service is mobile
	 * @param mContext
	 * @return
	 */
	public static boolean isMobile(Context mContext) {
		ConnectivityManager connectivityManager = (ConnectivityManager) mContext
				.getSystemService(Context.CONNECTIVITY_SERVICE);
		NetworkInfo activeNetInfo = connectivityManager.getActiveNetworkInfo();
		if (activeNetInfo != null
				&& activeNetInfo.getType() == ConnectivityManager.TYPE_MOBILE) {
			BBLog.v(BBLog.TAG, "has mobile, extra: " + activeNetInfo.getExtraInfo());
			return true;
		}
		return false;
	}

	public static void sendBroad(Context mContext, String action) {
		BBLog.v(BBLog.TAG, "sendBroad:" + action);
		String permission = RequestPermission.REQUEST_PERMISSION_MY_BROADCAST;
		Intent it = new Intent(action);
		if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
			if (action.equals(BroadcastActions.POLL_TIMER_START_BC)) {
				it.setComponent(new ComponentName(mContext.getPackageName(), PollTimer.class.getName()));
			} else if (action.equals(BroadcastActions.BLE_SCAN_TIMER_START_BC)) {
				it.setComponent(new ComponentName(mContext.getPackageName(), PollTimer.class.getName()));
			} else if (action.equals(BroadcastActions.TER_INFO_UPLOAD_BC)) {
				it.setComponent(new ComponentName(mContext.getPackageName(), TerminalInfoUploadTimer.class.getName()));
			} else if (action.equals(BroadcastActions.WISE_LOG_UPLOAD_BC)) {
				it.setComponent(new ComponentName(mContext.getPackageName(), WiseLogUploadTimer.class.getName()));
			} else if (action.equals(BroadcastActions.PARAM_UPDATE_COMPLETE_BC)) {
				it.setComponent(new ComponentName(mContext.getPackageName(), TmsReceiver.class.getName()));
			} else if (action.equals(UsualData.RULEBASED_EXEC_BC)) {
				it.setComponent(new ComponentName(mContext.getPackageName(), WebSocketReceiver.class.getName()));
			} else if (action.equals(UsualData.WSTASK_UPDATED_BC)) {
				it.setComponent(new ComponentName(mContext.getPackageName(), WebSocketReceiver.class.getName()));
			} else if (action.equals(UsualData.WSTASK_EXEC_BC)) {
				it.setComponent(new ComponentName(mContext.getPackageName(), WebSocketReceiver.class.getName()));
			} else if (action.equals(BroadcastActions.RULEBASED_DOWNLOAD_COMPLETED)) {
				it.setComponent(new ComponentName(mContext.getPackageName(), WebSocketReceiver.class.getName()));
			} else if (action.equals(BroadcastActions.APP_PLUS_DOWNLOAD_COMPLETED)) {
				it.setComponent(new ComponentName(mContext.getPackageName(), WebSocketReceiver.class.getName()));
			} else if (action.equals(UsualData.ACTION_TODO_OTA_UPDATE)) {
				permission = RequestPermission.REQUEST_PERMISSION_INTERNET;
				it.setComponent(new ComponentName(mContext.getPackageName(), OTAUpdateReceiver.class.getName()));
			} else if (action.equals(BroadcastActions.ACTION_EXPIRE_REBOOT)) {
				it.setComponent(new ComponentName(mContext.getPackageName(), CloudReceiver.class.getName()));
			}
		}
		//未开启过的应用也可以收到广播
		it.setFlags(Intent.FLAG_INCLUDE_STOPPED_PACKAGES);
		mContext.sendBroadcast(it, permission);
	}

	public static void sendUpdateTMTBroadByAction(Context mContext, String msg, String action) {
		BBLog.v(BBLog.TAG, "sendUpdateBroadByAction:" + action);
		Intent it = new Intent(action);
		//未开启过的应用也可以收到广播
		it.putExtra(ParameterName.TMT_URL, SharedPreferencesUtils.getSharePreferencesValue(UsualData.SHARED_PREFERENCES_TMT_SERVER_URL, UsualData.WISEPOS_PULS_TMT_URL_DEFAULT));
		it.putExtra(ParameterName.packName, msg);
		it.setComponent(new ComponentName(mContext.getPackageName(), OTAUpdateReceiver.class.getName()));
		it.setFlags(Intent.FLAG_INCLUDE_STOPPED_PACKAGES);
		mContext.sendBroadcast(it, RequestPermission.REQUEST_PERMISSION_INTERNET);
	}

	public static void sendUpdateBroadByAction(Context mContext, String msg, String action) {
		BBLog.v(BBLog.TAG, "sendUpdateBroadByAction:" + action);
		Intent it = new Intent(action);
		//未开启过的应用也可以收到广播
		it.putExtra(ParameterName.packName, msg);
		it.setComponent(new ComponentName(mContext.getPackageName(), OTAUpdateReceiver.class.getName()));
		it.setFlags(Intent.FLAG_INCLUDE_STOPPED_PACKAGES);
		mContext.sendBroadcast(it, RequestPermission.REQUEST_PERMISSION_INTERNET);
	}

	public static void sendToastMsgBroad(Context mContext, String msg) {
		BBLog.v(BBLog.TAG, "sendBroad:" + UsualData.ACTION_TOAST_MSG);
		Intent it = new Intent(UsualData.ACTION_TOAST_MSG);
		//未开启过的应用也可以收到广播
		it.putExtra("TOAST_MSG", msg);
		it.setFlags(Intent.FLAG_INCLUDE_STOPPED_PACKAGES);
		mContext.sendBroadcast(it, RequestPermission.REQUEST_PERMISSION_MY_BROADCAST);
	}

	public static void returnHome(Context mContext) {
		BBLog.v(BBLog.TAG, "restartService: call...");
		if (ActivityUtils.isApplictionInBackground(mContext, UsualData.LAUNCHER_PACKAGE_NAME)) {
			BBLog.v(BBLog.TAG, "restartService: isApplictionInBackground is false, start");
			Intent intent = new Intent(Intent.ACTION_MAIN);
			intent.addCategory(Intent.CATEGORY_HOME);
			intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_RESET_TASK_IF_NEEDED |
					Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_INCLUDE_STOPPED_PACKAGES);
			mContext.startActivity(intent);
		}
	}

	public static boolean isStrNoEmpty(String str) {
		if(str!=null && !str.equals(""))
			return true;
		else
			return false;
	}

	/**
	 * 以素材值更新被替换的json
	 * @param taskJson 被替换的json
	 * @param taskJsonForReplace 替换素材*/
	public static void updateWSTaskJsonObj(JSONObject taskJson, JSONObject taskJsonForReplace) {
		try {
			taskJson.put(ParameterName.taskResult, TaskState.TODO);
//			taskJson.put(ParameterName.taskTimestamp, taskJsonForReplace.getString(ParameterName.taskTimestamp));
			taskJson.put(ParameterName.beginDate, taskJsonForReplace.getString(ParameterName.beginDate));
			taskJson.put(ParameterName.endDate, taskJsonForReplace.getString(ParameterName.endDate));
			if(taskJsonForReplace.has(ParameterName.delete))//非必要字段
				taskJson.put(ParameterName.delete, taskJsonForReplace.getString(ParameterName.delete));
			if(taskJsonForReplace.has(ParameterName.relyTaskId))//非必要字段
				taskJson.put(ParameterName.relyTaskId, taskJsonForReplace.getString(ParameterName.relyTaskId));
			taskJson.put(ParameterName.pkgName, taskJsonForReplace.getString(ParameterName.pkgName));
			if(taskJsonForReplace.has(ParameterName.diffPack))//非必要字段
				taskJson.put(ParameterName.diffPack, taskJsonForReplace.getString(ParameterName.diffPack));
			taskJson.put(ParameterName.appId, taskJsonForReplace.getString(ParameterName.appId));
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	/**JsonArray 删除方法，兼容旧版本api， api版本19以上直接用原生的删除方法*/
	public static JSONArray remove(JSONArray jsonArray,int index) { 
		JSONArray mJsonArray  = new JSONArray();

		if(index<0 || index>jsonArray.length())
			return mJsonArray;
		for( int i=0;i<index;i++) {
			try {
				mJsonArray.put(jsonArray.getJSONObject(i));
			} catch (Exception e) {
				e.printStackTrace();
			}
		}   
		for( int i=index+1;i< jsonArray.length();i++) {
			try {
				mJsonArray.put(jsonArray.getJSONObject(i));
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
		return mJsonArray;
	}
	
	/***
	 * 根据action名称获取包名
	 * @param actionName
	 * @return
	 */
	public static String getPkgNameByServiceName(String actionName) {
		Intent intent = new Intent();
		intent.setAction(actionName);
		PackageManager pm = ContextUtil.getInstance().getPackageManager();
		List<ResolveInfo> resolveInfo = pm.queryIntentServices(intent, 0);
		if(resolveInfo == null || resolveInfo.size() < 1)
			return null;

		return resolveInfo.get(0).serviceInfo.packageName;
	}
	
	/**apk文件后缀*/
	public static String addApkSuffix(String apkPath) {
		if(apkPath.endsWith(".apk"))
			return apkPath;
		String newApkPath = apkPath+".apk";
		File apkFile = new File(apkPath);
		File newApkFile = new File(newApkPath);
		if (newApkFile.exists()) {
			newApkFile.delete();
		}
		if(apkFile.exists() && !newApkFile.exists())
			apkFile.renameTo(new File(newApkPath));
		
		return newApkPath;
	}
	
	public static boolean isMachedIP(String remoteIp,String storeIp) {
		if(TextUtils.isEmpty(storeIp)) {
			return false;
		}
		if(TextUtils.isEmpty(remoteIp)) {
			return false;
		}
		String remoteSubNet = "";
		String storeSubNet = "";
		try {
			remoteIp = remoteIp.trim();
			storeIp = storeIp.trim();
			remoteSubNet = remoteIp.substring(0, remoteIp.lastIndexOf("."));
			storeSubNet = storeIp.substring(0, storeIp.lastIndexOf("."));
			if(remoteSubNet.equals(storeSubNet)) {
				return true;
			}
		} catch(Exception e) {
			System.out.println(e.getMessage());
			return false;
		}
		return false;
	}

	/** 清除下载缓冲区*/
	public static void downloadPathClear(String path) {
		File downDir = new File(path);
		if(!downDir.isDirectory()) {
			return;
		}

		/** 列出所有文件，并删除*/
		File[] files = downDir.listFiles();
		if(files == null) {
			return;
		}
		for(File f : files) {
			if(f.exists()) {
				f.delete();
			}
		}
	}

	/**
	 * 将文件生成位图
	 *
	 * @param path
	 * @return
	 * @throws IOException
	 */
	public static Bitmap getImageDrawable(String path) throws IOException {
		//打开文件
		File file = new File(path);
		if (!file.exists()) {
			return null;
		}

		ByteArrayOutputStream outStream = new ByteArrayOutputStream();
		byte[] bt = new byte[2 * 1024];

		//得到文件的输入流
		InputStream in = new FileInputStream(file);

		//将文件读出到输出流中
		int readLength = in.read(bt);
		while (readLength != -1) {
			outStream.write(bt, 0, readLength);
			readLength = in.read(bt);
		}

		//转换成byte 后 再格式化成位图
		byte[] data = outStream.toByteArray();
		Bitmap bitmap = BitmapFactory.decodeByteArray(data, 0, data.length);// 生成位图

		return bitmap;
	}

	public static String formatToString(String[] data) {
		if (data == null || data.length == 0) return "";
		int iMax = data.length - 1;
		StringBuilder b = new StringBuilder();
		for (int i = 0; ; i++) {
			b.append(String.valueOf(data[i]));
			if (i == iMax)
				return b.toString();
			b.append(",");
		}
	}
}
