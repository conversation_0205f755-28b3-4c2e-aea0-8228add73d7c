package com.dspread.mdm.service.modules.remoteview

import android.content.Context
import com.dspread.mdm.service.modules.BaseModuleHandler
import com.dspread.mdm.service.modules.remoteview.model.RemoteViewConfig
import com.dspread.mdm.service.modules.remoteview.model.RemoteViewEvent
import com.dspread.mdm.service.modules.remoteview.model.RemoteViewPerformanceStats
import com.dspread.mdm.service.modules.remoteview.model.RemoteViewStatus
import com.dspread.mdm.service.network.websocket.message.WsMessageSender
import com.dspread.mdm.service.utils.log.Logger
import com.dspread.mdm.service.network.websocket.WebSocketCenter
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.json.JSONObject

/**
 * Remote View 处理器
 * 处理WebSocket命令，控制Remote View功能的启停
 */
class RemoteViewHandler(
    private val context: Context
) : BaseModuleHandler() {

    companion object {
        // WebSocket命令类型
        private const val CMD_CALL_REMOTE_CONTROL = "CALL_REMOTE_CONTROL"
        private const val CMD_CLOSE_REMOTE_CONTROL = "CLOSE_REMOTE_CONTROL"
        private const val CMD_REMOTE_VIEW_CONFIG = "REMOTE_VIEW_CONFIG"
    }

    private var remoteViewManager: RemoteViewManager? = null
    private val handlerScope = CoroutineScope(Dispatchers.Main + SupervisorJob())

    /**
     * 设置当前管理器实例（供CommandHandler使用）
     */
    fun setCurrentManager(manager: RemoteViewManager?) {
        remoteViewManager = manager
    }

    /**
     * 获取当前管理器实例（供CommandHandler使用）
     */
    fun getCurrentManager(): RemoteViewManager? {
        return remoteViewManager
    }

    override fun getModuleName(): String = "RemoteViewHandler"

    override suspend fun handleMessage(message: String): Result<Unit> {
        return try {
            val result = checkModuleEnabled()
            if (result.isFailure) return result

            val jsonObject = parseJsonMessage(message)
            val commandType = extractCommandType(jsonObject)
            
            Logger.remote("处理Remote View命令: $commandType")
            
            when (commandType) {
                CMD_CALL_REMOTE_CONTROL -> handleStartRemoteView(jsonObject)
                CMD_CLOSE_REMOTE_CONTROL -> handleStopRemoteView(jsonObject)
                CMD_REMOTE_VIEW_CONFIG -> handleConfigUpdate(jsonObject)
                else -> {
                    Logger.remoteW("未知的Remote View命令: $commandType")
                    return Result.failure(IllegalArgumentException("未知命令: $commandType"))
                }
            }
            
            // 发送确认响应
            sendAcknowledgment(jsonObject)
            
            Result.success(Unit)
        } catch (e: Exception) {
            Logger.remoteE("处理Remote View消息失败", e)
            Result.failure(e)
        }
    }

    /**
     * 处理启动Remote View命令
     */
    private suspend fun handleStartRemoteView(jsonObject: JSONObject) {
        try {
            Logger.remote("收到启动Remote View命令")
            
            // 解析参数
            val data = jsonObject.optJSONObject("data")
            val bucketPath = data?.optString("param") ?: data?.optString("bucket_path")
            val websocketUrl = data?.optString("websocket_url")
            val version = data?.optString("version")
            val quality = data?.optInt("quality", 60)
            val interval = data?.optLong("interval", 50L)
            
            // 获取WebSocket URL（优先使用消息中的URL，否则使用配置的URL）
            val finalWebSocketUrl = if (!websocketUrl.isNullOrEmpty()) {
                websocketUrl
            } else {
                RemoteViewWebSocketManager.getRemoteViewWebSocketUrl(context)
            }

            // 创建配置
            var config = RemoteViewConfig(websocketUrl = finalWebSocketUrl)

            // 更新配置参数
            if (!websocketUrl.isNullOrEmpty()) {
                config = config.copy(websocketUrl = websocketUrl)
            }
            if (!bucketPath.isNullOrEmpty()) {
                config = config.copy(bucketPath = bucketPath)
            }
            if (!version.isNullOrEmpty()) {
                // 版本参数已废弃，现在统一使用MediaProjection方式
                Logger.remoteW("版本参数已废弃: $version，统一使用MediaProjection方式")
            }
            if (quality != null && quality > 0) {
                config = config.copy(compressionQuality = quality)
            }
            if (interval != null && interval > 0) {
                config = config.copy(captureInterval = interval)
            }
            
            Logger.remote("Remote View配置: $config")
            
            // 检查当前状态，避免重复启动
            val currentManager = remoteViewManager
            if (currentManager != null) {
                val currentStatus = currentManager.remoteViewStatus.value
                if (currentStatus == RemoteViewStatus.RUNNING || currentStatus == RemoteViewStatus.STARTING) {
                    Logger.remoteW("Remote View服务已在运行中，跳过重复启动")
                    sendRemoteViewStatusResponse(jsonObject, "running", "Remote View已在运行")
                    return
                }

                // 如果状态不是STOPPED，先停止再重新启动
                if (currentStatus != RemoteViewStatus.STOPPED) {
                    Logger.remote("Remote View状态异常($currentStatus)，先停止再启动")
                    currentManager.stop()
                    delay(200) // 等待停止完成
                    currentManager.release()
                    remoteViewManager = null
                }
            }

            // 创建新的管理器
            if (remoteViewManager == null) {
                remoteViewManager = RemoteViewManager(context, config).apply {
                    // 设置事件监听
                    onEvent = { event ->
                        handleRemoteViewEvent(event)
                    }
                }

                // 初始化管理器
                val initResult = remoteViewManager!!.initialize()
                if (initResult.isFailure) {
                    Logger.remoteE("Remote View管理器初始化失败")
                    sendRemoteViewStatusResponse(jsonObject, "error", "初始化失败")
                    return
                }
            }
            
            // 启动Remote View服务
            val startResult = remoteViewManager!!.start()
            if (startResult.isSuccess) {
                Logger.remote("Remote View服务启动成功")
                sendRemoteViewStatusResponse(jsonObject, "started", "Remote View已启动")
            } else {
                Logger.remoteE("Remote View服务启动失败")
                sendRemoteViewStatusResponse(jsonObject, "error", "启动失败")
            }
            
        } catch (e: Exception) {
            Logger.remoteE("处理启动Remote View命令异常", e)
            sendRemoteViewStatusResponse(jsonObject, "error", "启动异常: ${e.message}")
        }
    }

    /**
     * 处理停止Remote View命令
     */
    private suspend fun handleStopRemoteView(jsonObject: JSONObject) {
        try {
            Logger.remote("收到停止Remote View命令")
            
            if (remoteViewManager == null) {
                Logger.remoteW("Remote View管理器不存在，无需停止")
                sendRemoteViewStatusResponse(jsonObject, "stopped", "Remote View未运行")
                return
            }
            
            // 确保停止和释放的正确顺序
            val manager = remoteViewManager!!
            val stopResult = manager.stop()

            if (stopResult.isSuccess) {
                Logger.remote("Remote View服务停止成功")

                // 等待一小段时间确保停止完成，然后释放资源
                delay(100)
                manager.release()
                remoteViewManager = null

                sendRemoteViewStatusResponse(jsonObject, "stopped", "Remote View已停止")
            } else {
                Logger.remoteE("Remote View服务停止失败")

                // 即使停止失败也要尝试释放资源
                try {
                    manager.release()
                    remoteViewManager = null
                } catch (e: Exception) {
                    Logger.remoteE("强制释放Remote View资源失败", e)
                }

                sendRemoteViewStatusResponse(jsonObject, "error", "停止失败")
            }
            
        } catch (e: Exception) {
            Logger.remoteE("处理停止Remote View命令异常", e)
            sendRemoteViewStatusResponse(jsonObject, "error", "停止异常: ${e.message}")
        }
    }

    /**
     * 处理配置更新命令
     */
    private suspend fun handleConfigUpdate(jsonObject: JSONObject) {
        try {
            Logger.remote("收到Remote View配置更新命令")
            
            val data = jsonObject.optJSONObject("data")
            if (data == null) {
                Logger.remoteW("配置更新命令缺少data字段")
                return
            }
            
            // 解析新配置
            val newConfig = RemoteViewConfig.fromJson(data)
            
            if (remoteViewManager != null) {
                remoteViewManager!!.updateConfig(newConfig)
                Logger.remote("Remote View配置已更新")
                sendRemoteViewStatusResponse(jsonObject, "config_updated", "配置已更新")
            } else {
                Logger.remoteW("Remote View管理器不存在，无法更新配置")
                sendRemoteViewStatusResponse(jsonObject, "error", "服务未运行")
            }
            
        } catch (e: Exception) {
            Logger.remoteE("处理配置更新命令异常", e)
            sendRemoteViewStatusResponse(jsonObject, "error", "配置更新异常: ${e.message}")
        }
    }

    /**
     * 处理Remote View事件
     */
    private fun handleRemoteViewEvent(event: RemoteViewEvent) {
        handlerScope.launch {
            when (event) {
                is RemoteViewEvent.Started -> {
                    Logger.remote("Remote View已启动")
                }
                is RemoteViewEvent.Stopped -> {
                    Logger.remote("Remote View已停止")
                }
                is RemoteViewEvent.Error -> {
                    Logger.remoteE("Remote View错误: ${event.message}", event.throwable)
                }
                is RemoteViewEvent.WebSocketConnected -> {
                    Logger.remote("WebSocket已连接: ${event.url}")
                }
                is RemoteViewEvent.WebSocketDisconnected -> {
                    Logger.remoteW("WebSocket已断开: ${event.reason}")
                }
                is RemoteViewEvent.FrameCaptured -> {
                    // 定期输出性能统计（每100帧输出一次）
                    if (event.stats.totalFrames % 100 == 0L) {
                        Logger.remote(
                            "Remote View性能统计 - " +
                            "总帧数: ${event.stats.totalFrames}, " +
                            "成功率: ${String.format("%.2f", event.stats.successRate * 100)}%, " +
                            "平均截屏时间: ${event.stats.averageCaptureTime}ms, " +
                            "平均数据大小: ${event.stats.averageDataSize} bytes"
                        )
                    }
                }
                else -> {
                    // 其他事件的处理
                }
            }
        }
    }

    /**
     * 发送Remote View状态响应
     */
    private fun sendRemoteViewStatusResponse(
        originalMessage: JSONObject,
        status: String,
        message: String
    ) {
        try {
            val requestId = originalMessage.optString("request_id")
            val requestTime = originalMessage.optString("request_time")
            
            if (requestId.isNotEmpty() && requestTime.isNotEmpty()) {
                val responseData = JSONObject().apply {
                    put("status", status)
                    put("message", message)
                    put("timestamp", System.currentTimeMillis())
                }
                
                // 通过WsMessageSender发送响应
                WsMessageSender.sendWebSocketResponse(requestId, requestTime, "0", responseData.toString())
                Logger.remote("已发送Remote View状态响应: $status")
            }
        } catch (e: Exception) {
            Logger.remoteE("发送Remote View状态响应失败", e)
        }
    }

    /**
     * 提取命令类型
     */
    private fun extractCommandType(jsonObject: JSONObject): String {
        // 尝试从不同字段提取命令类型
        return jsonObject.optString("tran_code")
            ?: jsonObject.optString("type")
            ?: jsonObject.optJSONObject("data")?.optString("type")
            ?: jsonObject.optJSONObject("data")?.optString("c_type") // 添加c_type字段支持
            ?: ""
    }

    /**
     * 获取当前Remote View状态
     */
    fun getCurrentStatus(): RemoteViewStatus? {
        return remoteViewManager?.remoteViewStatus?.value
    }

    /**
     * 获取性能统计
     */
    fun getPerformanceStats(): RemoteViewPerformanceStats? {
        return remoteViewManager?.performanceStats?.value
    }

    /**
     * 释放资源
     */
    fun release() {
        Logger.remote("释放Remote View处理器资源")
        remoteViewManager?.release()
        remoteViewManager = null
    }
}
