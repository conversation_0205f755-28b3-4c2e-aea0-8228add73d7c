package com.bbpos.wiseapp.settings.adapter;

import android.content.Context;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseAdapter;
import android.widget.ImageView;
import android.widget.TextView;

import com.bbpos.wiseapp.logger.BBLog;
import com.bbpos.wiseapp.service.R;
import com.bbpos.wiseapp.settings.utils.HelperUtil;

import java.util.List;

/**
 * Created by ThinkPad on 2018/1/17.
 */

public class LanguageAdapter extends BaseAdapter {
    private List<String> list_item = null;
    private int selectItem;
    private Context context;

    public LanguageAdapter(Context context, List<String> list) {
        super();
        this.context = context;
        this.list_item = list;
    }


    @Override
    public int getCount() {
        return list_item!=null ? this.list_item.size() : 0;
    }

    @Override
    public Object getItem(int position) {
        return list_item!=null ? list_item.get(position) : null;
    }

    @Override
    public long getItemId(int position) {
        return position;
    }

    @Override
    public View getView(int position, View convertView, ViewGroup parent) {
        if (list_item == null) {
            return null;
        }

        ViewHolder viewHolder = null;
        if (convertView == null) {
            convertView = LayoutInflater.from(context).inflate(R.layout.item_language, parent, false);
            viewHolder = new ViewHolder();
            viewHolder.mTextView = (TextView) convertView.findViewById(R.id.tv_item);
            viewHolder.mImageView = (ImageView) convertView.findViewById(R.id.iv_check);
            convertView.setTag(viewHolder);
        } else {
            viewHolder = (ViewHolder) convertView.getTag();
        }

        viewHolder.mTextView.setText(list_item.get(position));
        String lang = HelperUtil.getCountryLanguage(context);
        BBLog.i(BBLog.TAG, "当前语言：" + lang);
        if ("en-US".equals(lang) || "-".equals(lang)) {
            if (context.getString(R.string.english).equals(list_item.get(position))) {
                viewHolder.mImageView.setVisibility(View.VISIBLE);
            } else {
                viewHolder.mImageView.setVisibility(View.INVISIBLE);
            }
        } else if ("zh-CN".equals(lang)) {
            if (context.getString(R.string.chinese_simplified).equals(list_item.get(position))) {
                viewHolder.mImageView.setVisibility(View.VISIBLE);
            } else {
                viewHolder.mImageView.setVisibility(View.INVISIBLE);
            }
        } else if ("zh-HK".equals(lang) || "zh-TW".equals(lang)) {
            if (context.getString(R.string.chinese_traditional).equals(list_item.get(position))) {
                viewHolder.mImageView.setVisibility(View.VISIBLE);
            } else {
                viewHolder.mImageView.setVisibility(View.INVISIBLE);
            }
        }
        return convertView;
    }

    private static class ViewHolder {
        public TextView mTextView;
        public ImageView mImageView;
    }
}
