package com.bbpos.wiseapp.settings.activity;

import android.app.Activity;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.res.Configuration;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import android.net.wifi.ScanResult;
import android.net.wifi.WifiConfiguration;
import android.net.wifi.WifiManager;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;
import android.view.View.OnClickListener;
import android.widget.AdapterView;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ListView;
import android.widget.TextView;

import com.bbpos.wiseapp.logger.BBLog;
import com.bbpos.wiseapp.service.R;
import com.bbpos.wiseapp.service.common.RequestPermission;
import com.bbpos.wiseapp.settings.adapter.WiFiAdapter;
import com.bbpos.wiseapp.settings.dialog.WifiConnectedDialog;
import com.bbpos.wiseapp.settings.dialog.WifiDialog;
import com.bbpos.wiseapp.settings.utils.HelperUtil;
import com.bbpos.wiseapp.settings.utils.WirelessUtil;
import com.bbpos.wiseapp.settings.widget.ToggleSwitch;
import com.bbpos.wiseapp.settings.wifi.WifiController;

import java.util.ArrayList;
import java.util.List;

//登录界面
public class WiFiActivity extends Activity implements OnClickListener{
	private List<ScanResult> listItem;

	private LinearLayout ll_listwifi;
	private ListView listView;
	private ImageView iv_close;
	private ToggleSwitch toggleSwitch;
	private TextView tv_status;
	private TextView tv_prompt;
	private TextView tv_connect_detail;
	private WiFiAdapter listAdapter;
	private WifiEnabler wifiEnabler;
	private WifiManager mWifiManager;
	private WifiController mWifiController;
	private ConnectivityManager mConnectivityManager;
	private IntentFilter mIntentFilter;
	private WifiDialog dialog;
	private WifiConnectedDialog connectedDialog;
	private int wifistate;
	private boolean isWifConnecting = false;

	private final BroadcastReceiver mReceiver = new BroadcastReceiver() {
		@Override
		public void onReceive(Context context, Intent intent) {
			String action = intent.getAction();
			if (action.equals(WifiManager.WIFI_STATE_CHANGED_ACTION)) {
				wifistate = intent.getIntExtra(WifiManager.EXTRA_WIFI_STATE, WifiManager.WIFI_STATE_UNKNOWN);
				onAccessPointsChanged(wifistate);
				wifiEnabler.handleWifiStateChanged(wifistate);
			} else if (action.equals(WifiManager.RSSI_CHANGED_ACTION)) {
				updataWifiState();
			} else if (action.equals(WifiManager.SCAN_RESULTS_AVAILABLE_ACTION)) {
				updataWifiState();
			} else if (intent.getAction().equals(WifiManager.NETWORK_STATE_CHANGED_ACTION)) {//wifi连接网络状态变化
				updataWifiState();
				NetworkInfo.DetailedState state = ((NetworkInfo) intent.getParcelableExtra(WifiManager.EXTRA_NETWORK_INFO)).getDetailedState();
				onNetWorkStateChanged(state);
			}
		}
	};

	public void updataWifiState() {
		if (wifistate==WifiManager.WIFI_STATE_DISABLED) {
			BBLog.i(BBLog.TAG, "updataWifiState return");
			return;
		}
		listItem = getScanResultNotNull();
//		listItem = mWifiManager.getScanResults();
//		String ssid = WirelessUtil.getCurConnectSSID(WiFiActivity.this);
//		for(int i = 0 ; i < listItem.size() ; i++) {
//			if (ssid.equals(listItem.get(i).SSID)) {
//				if (i == 0) {
//					break;
//				}
//				ScanResult scanResult = listItem.get(i);
//				listItem.remove(i);
//				listItem.add(0, scanResult);
//				break;
//			}
//		}
		if (View.INVISIBLE == ll_listwifi.getVisibility()) {
			ll_listwifi.setVisibility(View.VISIBLE);
		}
		listAdapter.setData(listItem);
		listAdapter.notifyDataSetChanged();
	}

	@Override
	public void onCreate(Bundle savedInstanseBundle){
		super.onCreate(savedInstanseBundle);
		setContentView(R.layout.activity_wifi);

		mConnectivityManager = (ConnectivityManager) getSystemService(CONNECTIVITY_SERVICE);
		mWifiManager = (WifiManager) getApplicationContext().getSystemService(WIFI_SERVICE);

		initData();
		initView();

		mIntentFilter = new IntentFilter(WifiManager.WIFI_STATE_CHANGED_ACTION);
		// The order matters! We really should not depend on this. :(
		mIntentFilter.addAction(WifiManager.SUPPLICANT_STATE_CHANGED_ACTION);
		mIntentFilter.addAction(WifiManager.NETWORK_STATE_CHANGED_ACTION);
		mIntentFilter.addAction(WifiManager.RSSI_CHANGED_ACTION);
		mIntentFilter.addAction(WifiManager.SCAN_RESULTS_AVAILABLE_ACTION);

		wifiEnabler = new WifiEnabler(WiFiActivity.this, toggleSwitch, tv_status);
		mWifiController = new WifiController(mConnectivityManager, mWifiManager);
	}

	private void initData() {
//		listItem = mWifiManager.getScanResults();
		listItem = getScanResultNotNull();
		String ssid = WirelessUtil.getCurConnectSSID(WiFiActivity.this);
		for(int i = 0 ; i < listItem.size() ; i++) {
			if (ssid.equals(listItem.get(i).SSID)) {
				ScanResult scanResult = listItem.get(i);
				listItem.remove(i);
				listItem.add(0, scanResult);
				break;
			}
		}
	}

	/**
	 * 过滤ssid为空的wifi
	 * @return
	 */
	private List<ScanResult> getScanResultNotNull(){
		if (listItem !=null)
			listItem.clear();
		else
			listItem = new ArrayList<>();
		List<ScanResult> results = mWifiManager.getScanResults();
		for (ScanResult  result: results){
			if (!TextUtils.isEmpty(result.SSID)){
				listItem.add(result);
			}
		}
		return listItem;
	}

	private void showWifiStateDesc(String desc) {
		if (View.VISIBLE == ll_listwifi.getVisibility()) {
			ll_listwifi.setVisibility(View.INVISIBLE);
		}
		tv_prompt.setVisibility(View.VISIBLE);
		tv_prompt.setText(desc);
	}

	private void onAccessPointsChanged(int wifiState) {
		switch (wifiState) {
			case WifiManager.WIFI_STATE_ENABLED:
				showWifiStateDesc(getString(R.string.wifi_empty_list_wifi_on));
				updataWifiState();
				break;
			case WifiManager.WIFI_STATE_ENABLING:
				showWifiStateDesc(getString(R.string.wifi_starting));
				break;
			case WifiManager.WIFI_STATE_DISABLING:
				showWifiStateDesc(getString(R.string.wifi_stopping));
				break;
			case WifiManager.WIFI_STATE_DISABLED:
				if (tv_connect_detail!=null && View.VISIBLE==tv_connect_detail.getVisibility()) {
					tv_connect_detail.setVisibility(View.GONE);
					tv_connect_detail = null;
				}
				showWifiStateDesc(getString(R.string.wifi_empty_list_wifi_off));
				break;
		}
	}

	private void setConnectDetail(String desc) {
		if (tv_connect_detail != null) {
			tv_connect_detail.setVisibility(View.VISIBLE);
			tv_connect_detail.setText(desc);
		}
	}

	private void onNetWorkStateChanged(NetworkInfo.DetailedState state) {
		if (state == NetworkInfo.DetailedState.SCANNING) {
			BBLog.i(BBLog.TAG, "onReceive onNetWorkStateChanged: +正在扫描");
			setConnectDetail("正在扫描");
		} else if (state == NetworkInfo.DetailedState.CONNECTING) {
			isWifConnecting = true;
			BBLog.i(BBLog.TAG, "onReceive onNetWorkStateChanged: +正在连接");
			setConnectDetail("正在连接");
		} else if (state == NetworkInfo.DetailedState.OBTAINING_IPADDR) {
			BBLog.i(BBLog.TAG, "onReceive onNetWorkStateChanged: +获取IP地址");
			setConnectDetail("获取IP地址");
		} else if (state == NetworkInfo.DetailedState.CONNECTED) {
			isWifConnecting = false;
			BBLog.i(BBLog.TAG, "onReceive onNetWorkStateChanged: +建立连接");
			setConnectDetail(getString(R.string.connected));
			updataWifiState();
		} else if (state == NetworkInfo.DetailedState.DISCONNECTING) {
			BBLog.i(BBLog.TAG, "onReceive onNetWorkStateChanged: +正在断开连接");
			setConnectDetail("正在断开连接");
		} else if (state == NetworkInfo.DetailedState.DISCONNECTED) {
			BBLog.i(BBLog.TAG, "onReceive onNetWorkStateChanged: +已断开连接");
			setConnectDetail("已断开连接");
		} else if (state == NetworkInfo.DetailedState.FAILED) {
			isWifConnecting = false;
			BBLog.i(BBLog.TAG, "onReceive onNetWorkStateChanged: +连接失败");
			setConnectDetail("连接失败");
		}
	}

	@Override
	protected void onResume() {
		super.onResume();
		wifiEnabler.resume(this);
		if (!mWifiManager.isWifiEnabled()) {
			mWifiManager.setWifiEnabled(true);
			mWifiManager.startScan();
		}

		registerReceiver(mReceiver, mIntentFilter,  RequestPermission.REQUEST_PERMISSION_MY_BROADCAST, null);
	}

	@Override
	protected void onPause() {
		super.onPause();
		wifiEnabler.pause();
		unregisterReceiver(mReceiver);
	}

	@Override
	public void onConfigurationChanged(Configuration newConfig) {
		super.onConfigurationChanged(newConfig);
		finish();
	}

	private void initView() {
		TextView title = findViewById(R.id.toolbar_title_tv);
		title.setText(getString(R.string.wifi));

		iv_close = findViewById(R.id.toolbar_left_btn);
		iv_close.setOnClickListener(this);

		ll_listwifi = findViewById(R.id.ll_listwifi);
		listView = findViewById(R.id.list_wifi);

		toggleSwitch = findViewById(R.id.tb_switch);
		tv_status = findViewById(R.id.tv_status);
		tv_prompt = findViewById(R.id.tv_prompt);
		if (HelperUtil.isWifiEnable(WiFiActivity.this)) {
			toggleSwitch.setChecked(true);
			tv_status.setText(getString(R.string.on));
			ll_listwifi.setVisibility(View.VISIBLE);
			tv_prompt.setVisibility(View.INVISIBLE);
		} else {
			toggleSwitch.setChecked(false);
			tv_status.setText(getString(R.string.off));
			ll_listwifi.setVisibility(View.INVISIBLE);
			tv_prompt.setVisibility(View.VISIBLE);
			tv_prompt.setText(getString(R.string.wifi_empty_list_wifi_off));
		}

		//初始化列表
		listView = (ListView) findViewById(R.id.list_wifi);

		listAdapter = new WiFiAdapter(WiFiActivity.this, listItem);
		listView.setAdapter(listAdapter);
		listView.setOnItemClickListener(new AdapterView.OnItemClickListener() {
			@Override
			public void onItemClick(AdapterView<?> parent, View view, final int position, long id) {
				if (HelperUtil.isFastDoubleClick()) { //判断是否是快速点击
					return;
				}
				if (position == listItem.size()-1) {
					//添加网络
					promptForWifiConnect(null);
				} else {
					if (view != null) {
						tv_connect_detail = view.findViewById(R.id.tv_status);
					}
					promptForWifiConnect(listItem.get(position));
				}
			}
		});
	}

	@Override
	public void onClick(View v) {
		switch (v.getId()) {
			case R.id.toolbar_left_btn:
				finish();
				break;
		}
	}

	private void promptForWifiConnect(ScanResult scanResult){
		if (scanResult == null) {
			if (dialog != null) {
				dialog.dismiss();
				dialog = null;
			}
			dialog = new WifiDialog(WiFiActivity.this, wifiDialogListener, scanResult, 0);
			dialog.show();
		} else {
			if (mWifiController.isGivenWifiConnect(scanResult.SSID)) {
				if (connectedDialog != null) {
					connectedDialog.dismiss();
					connectedDialog = null;
				}
				connectedDialog = new WifiConnectedDialog(WiFiActivity.this, mWifiManager, mWifiController, wifiDialogListener, scanResult);
				connectedDialog.show();
			} else {
				if (dialog != null) {
					dialog.dismiss();
					dialog = null;
				}
				dialog = new WifiDialog(WiFiActivity.this, wifiDialogListener, scanResult, 1);
				dialog.show();
			}
		}
	}

	private WifiDialog.WifiDialogListener wifiDialogListener = new WifiDialog.WifiDialogListener() {
		@Override
		public void onForget() {
			List<WifiConfiguration> list = mWifiManager.getConfiguredNetworks();
			for( WifiConfiguration i : list ) {
				mWifiManager.removeNetwork(i.networkId);
				mWifiManager.saveConfiguration();
			}
		}

		@Override
		public void onSubmit(ScanResult scanResult, String password) {
			WifiConfiguration config = mWifiController.creatWifiConfiguration(scanResult, password);
			mWifiController.connect(config);
		}
	};
}
