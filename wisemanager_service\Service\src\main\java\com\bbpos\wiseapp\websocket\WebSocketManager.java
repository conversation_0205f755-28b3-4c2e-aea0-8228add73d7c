package com.bbpos.wiseapp.websocket;

import android.content.Context;
import android.util.Log;

import com.bbpos.BaseWebSocketHandlerCenter;
import com.bbpos.wiseapp.logger.BBLog;

public class WebSocketManager extends BaseWebSocketHandlerCenter {

    private static BaseWebSocketHandlerCenter mHandlerCenter;
    private static WebSocketManager mWebSocketManager;
//    private static onMessageRecvListener mListener;

//    public static boolean m_need_rsa = true;
//    public static String m_public_key = "";
//    public static String m_private_key = "";
//    public static String m_server_public_key = "";
//    public static String url = "";

//    public interface onMessageRecvListener {
//        public void onMessageRecv(String respone);
//        public void onDisconnected();
//    }

    public static WebSocketManager getInstance(Context context, onMessageRecvListener listener,BaseWebSocketHandlerCenter handlerCenter) {
        if (handlerCenter == null)
            throw new IllegalArgumentException("handlerCenter can't be null");
        mHandlerCenter = handlerCenter;
        return getInstance(context, listener);
    }

    public static WebSocketManager getInstance(Context context, onMessageRecvListener listener) {
        if (mWebSocketManager == null) {
            mWebSocketManager = new WebSocketManager(context);
            mListener = listener;
        }
        return mWebSocketManager;
    }

    public WebSocketManager(Context context) {
        super(context);
    }

    @Override
    public void onMessageResponse(Response message) {
        BBLog.d(WebSocketCenter.TAG, "onMessageResponse: messsage: "+message);
        mHandlerCenter.onMessageResponse(message);
//        String respone = "";
//        try {
//            if (m_need_rsa) {
//                respone = new String(RSAUtils.decryptByPrivateKey(Base64Utils.decode(message.getResponseText()), m_private_key));
//            } else {
//                respone = message.getResponseText();
//            }
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//        BBLog.e(Constants.TAG, "onMessageResponse: " + respone);
//
//        if (mListener!=null) {
//            mListener.onMessageRecv(respone);
//        }
    }

    @Override
    public void sendText(String text) {
        mHandlerCenter.sendText(text);
//        BBLog.e(BBLog.TAG, "WebSocket: 数据发送：" + text);
    }

    @Override
    public void disconnect() {
        mHandlerCenter.disconnect();
    }

    @Override
    public void onSendMessageError(ErrorResponse error) {
        super.onSendMessageError(error);

    }

    @Override
    public void onDisconnected() {
        super.onDisconnected();
//        mListener.onDisconnected();
    }
}
