package com.bbpos.wiseapp.utils;

import android.graphics.Bitmap;
import android.os.Build;
import android.text.TextUtils;
import android.util.Base64;
import android.util.Log;

import com.bbpos.wiseapp.logger.BBLog;
import com.bbpos.wiseapp.security.SecurityOperate;
import com.bbpos.wiseapp.settings.utils.WirelessUtil;
import com.bbpos.wiseapp.system.api.DeviceInfoApi;
import com.bbpos.wiseapp.tms.utils.Constants;
import com.bbpos.wiseapp.tms.utils.ContextUtil;
import com.bbpos.wiseapp.tms.utils.Helpers;
import com.bbpos.wiseapp.tms.utils.IOUtils;
import com.bbpos.wiseapp.websocket.nv.WmSSlContent;

import java.io.BufferedInputStream;
import java.io.BufferedOutputStream;
import java.io.ByteArrayOutputStream;
import java.io.DataOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.io.UnsupportedEncodingException;
import java.net.HttpURLConnection;
import java.net.MalformedURLException;
import java.net.URL;
import java.net.URLConnection;
import java.security.InvalidKeyException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.security.cert.CertPath;
import java.security.cert.CertPathValidatorException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;
import java.util.TimeZone;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import javax.net.ssl.HostnameVerifier;
import javax.net.ssl.HttpsURLConnection;
import javax.net.ssl.SSLContext;
import javax.net.ssl.SSLSession;

public class HttpUtils {
	private final static String TAG = "HttpUtils";

	public interface FileDownloadCallBack extends com.bbpos.wiseapp.tms.network.HttpUtils.RequestCallBack {
		public void onDownloading(long curFileSize, long fileSize);
	}

	public static String connectPost(String str_url, String content) {
		String result = "";
		String errorMsg;
		DataOutputStream bOutputStream = null;
		InputStream inputStream = null;
		HttpsURLConnection urlConnection = null;
		try {
			BBLog.v(BBLog.TAG, "url = "+ str_url);
			URL url = new URL(str_url);
			urlConnection = (HttpsURLConnection) url.openConnection();

			SSLContext sc = WmSSlContent.getSSLContext();
			urlConnection.setSSLSocketFactory(sc.getSocketFactory());
			urlConnection.setHostnameVerifier(new HostnameVerifier() {
				@Override
				public boolean verify(String hostname, SSLSession session) {
					HostnameVerifier hv = HttpsURLConnection.getDefaultHostnameVerifier();
					BBLog.e(TAG, "connectPost, httpconnect verify = " + hv.verify(hostname,session));
					return hv.verify(hostname,session);
				}
			});
//			BBLog.i(BBLog.TAG, "url = "+url.toString());
			urlConnection.setDoInput(true);
			urlConnection.setDoOutput(true);
			urlConnection.setConnectTimeout(Constants.ConnectionTimeout);
			urlConnection.setReadTimeout(Constants.SoTimeout);
			urlConnection.setRequestMethod("POST");
//			urlConnection.setRequestProperty("Connection", "Keep-Alive");
//			urlConnection.setRequestProperty("Charset", "UTF-8");
//			urlConnection.setRequestProperty("User-Agent", "directclient");
			urlConnection.setRequestProperty("Content-Type","application/json; charset=utf-8");
			bOutputStream = new DataOutputStream(urlConnection.getOutputStream());
			BBLog.i(BBLog.TAG, "http post content="+content);
			bOutputStream.write(content.getBytes("UTF-8"));
			bOutputStream.flush();
//			BBLog.v(BBLog.TAG, "http response,ResponseMessage=" + urlConnection.getResponseCode() + " ResponseMessage=" + urlConnection.getResponseMessage());

			inputStream = urlConnection.getInputStream();
			StringBuffer responseSb = new StringBuffer();
			// 读取服务器数据
			if (inputStream != null) {
				ByteArrayOutputStream baOutputStream = new ByteArrayOutputStream();
				int len = -1;
				byte[] buf = new byte[1024];
				while ((len = inputStream.read(buf)) != -1) {
					baOutputStream.write(buf, 0, len);
				}
				baOutputStream.flush();
				responseSb.append(baOutputStream.toString());
			}

			// 解析服务器返回数据
			result = responseSb.toString();
			BBLog.i(BBLog.TAG, "http post result=" + result);
		} catch (IOException e) {
			e.printStackTrace();
			errorMsg = e.getMessage();
			Helpers.sendToastMsgBroad(ContextUtil.getInstance(), errorMsg);
			BBLog.i(BBLog.TAG, "http response,IOException=="+errorMsg);
		} catch (Exception e) {
			e.printStackTrace();
			errorMsg = e.getMessage();
			Helpers.sendToastMsgBroad(ContextUtil.getInstance(), errorMsg);
			BBLog.i(BBLog.TAG, "http response,Exception=="+errorMsg);

            if (e instanceof CertPathValidatorException){
                CertPathValidatorException exception = (CertPathValidatorException) e;
                CertPath certPath = exception.getCertPath();
                BBLog.e(TAG, "SSLContent certification path : "+certPath );
            }
		} finally {
			if(urlConnection != null) {
				urlConnection.disconnect();
			}
			IOUtils.flushCloseOutputStream(bOutputStream);
			IOUtils.closeInputStream(inputStream);
		}
		
		return result;
	}

	public static boolean url2file(String strurl, String fileName, String md5, FileDownloadCallBack callBack) {
		BBLog.i(BBLog.TAG, "url2file 开始下载");
		boolean isDownloadSucc = false;
		InputStream inputStream = null;
		BufferedInputStream bin = null;
		FileOutputStream fileOutputStream = null;
		long curFileLength = 0;
		int fileLength = 0;
		File curFile = null;

		if (TextUtils.isEmpty(strurl)) {
			return false;
		}

		String strurl_proxy = strurl;
		if (Constants.EGINX_PROXY_ENABLE && !TextUtils.isEmpty(Constants.EGINX_PROXY_IP)
				&& WirelessUtil.ping_ip(Constants.EGINX_PROXY_IP)
				&& WirelessUtil.checkURL("http://"+Constants.EGINX_PROXY_IP+":8080"+"/")) {
			try {
				if (strurl_proxy.contains("wisemanager.de")) {
					strurl_proxy = strurl_proxy.replace("https://" + new URL(strurl_proxy).getHost(), "http://" + Constants.EGINX_PROXY_IP + ":8080/bbpos4");
				} else if (strurl_proxy.contains("wisemanager.com")) {
					strurl_proxy = strurl_proxy.replace("https://" + new URL(strurl_proxy).getHost(), "http://" + Constants.EGINX_PROXY_IP + ":8080/bbpos3");
				} else {
					strurl_proxy = strurl_proxy.replace("https://" + new URL(strurl_proxy).getHost(), "http://" + Constants.EGINX_PROXY_IP + ":8080");
				}
			} catch (MalformedURLException e) {
				e.printStackTrace();
			}
			BBLog.e(BBLog.TAG, "Proxy sourcePath = " + strurl_proxy);
		} else {
			BBLog.e(BBLog.TAG, "Proxy Server not found!!!");
		}

		if (strurl_proxy.contains("https")) {
			HttpsURLConnection urlConnection = null;
			try {
				curFile = new File(fileName);
				if (curFile.exists() && !TextUtils.isEmpty(md5)) {
					if (md5.equals(com.bbpos.wiseapp.tms.utils.FileUtils.getMd5ByFile(curFile))) {
						BBLog.w(BBLog.TAG, "MD5匹配，文件已存在");
						return true;
					} else {
						curFile.delete();
						curFile.createNewFile();
					}
				}
				if (!curFile.getParentFile().exists()) {
					curFile.getParentFile().mkdirs();
					curFile.createNewFile();
				}

				curFileLength = curFile.length();

				URL url = new URL(strurl_proxy);
				urlConnection = (HttpsURLConnection) url.openConnection();

				SSLContext sc = WmSSlContent.getSSLContext();
				urlConnection.setSSLSocketFactory(sc.getSocketFactory());
				urlConnection.setHostnameVerifier(new HostnameVerifier() {
					@Override
					public boolean verify(String hostname, SSLSession session) {
						HostnameVerifier hv = HttpsURLConnection.getDefaultHostnameVerifier();
						BBLog.e(TAG, "url2file, httpconnect verify = " + hv.verify(hostname, session));
						return hv.verify(hostname, session);
					}
				});

				urlConnection.setConnectTimeout(Constants.ConnectionTimeout);
				urlConnection.setReadTimeout(Constants.SoTimeout);
				urlConnection.setRequestProperty("Range", String.format("bytes=%d-", curFileLength));
				urlConnection.connect();
				// 文件大小
				fileLength = urlConnection.getContentLength();
				if (fileLength <= 0) {
					return false;
				}
				BBLog.i(BBLog.TAG, "url2file fileLength= " + fileLength);

				bin = new BufferedInputStream(urlConnection.getInputStream());
				byte[] bytes = new byte[4 * 1024];
				int numReadByte = 0;
				// 读取服务器数据 并写入到文件中
				if (DeviceInfoApi.getIntance().isWisePos4G() && !Build.DISPLAY.contains("WiseManager")) {
					fileOutputStream = new FileOutputStream(curFile, false);
				} else {
					fileOutputStream = new FileOutputStream(curFile, true);
				}
				long readcount = curFileLength;
				while ((numReadByte = bin.read(bytes)) != -1) {
					readcount += numReadByte;
//                BBLog.i(BBLog.TAG, "url2file readcount = " + readcount);
					fileOutputStream.write(bytes, 0, numReadByte);
					callBack.onDownloading(readcount, fileLength);
				}
				bin.close();
			} catch (Exception e) {
				isDownloadSucc = false;
				e.printStackTrace();

				if (e instanceof CertPathValidatorException) {
					CertPathValidatorException exception = (CertPathValidatorException) e;
					CertPath certPath = exception.getCertPath();
					BBLog.e(TAG, "SSLContent certification path : " + certPath);
				}
			} finally {
				if (urlConnection != null)
					urlConnection.disconnect();
				IOUtils.flushCloseOutputStream(fileOutputStream);
				IOUtils.closeInputStream(inputStream);
			}
		} else {
			HttpURLConnection urlConnection = null;
			try {
				curFile = new File(fileName);
				if (curFile.exists() && !TextUtils.isEmpty(md5)) {
					if (md5.equals(com.bbpos.wiseapp.tms.utils.FileUtils.getMd5ByFile(curFile))) {
						BBLog.w(BBLog.TAG, "MD5匹配，文件已存在");
						return true;
					} else {
						curFile.delete();
						curFile.createNewFile();
					}
				}
				if (!curFile.getParentFile().exists()) {
					curFile.getParentFile().mkdirs();
					curFile.createNewFile();
				}

				curFileLength = curFile.length();

				URL url = new URL(strurl_proxy);
				urlConnection = (HttpURLConnection) url.openConnection();
				urlConnection.setConnectTimeout(Constants.ConnectionTimeout);
				urlConnection.setReadTimeout(Constants.SoTimeout);
				urlConnection.setRequestProperty("Range", String.format("bytes=%d-", curFileLength));
				urlConnection.connect();
				// 文件大小
				fileLength = urlConnection.getContentLength();
				if (fileLength <= 0) {
					return false;
				}
				BBLog.i(BBLog.TAG, "url2file fileLength= " + fileLength);

				bin = new BufferedInputStream(urlConnection.getInputStream());
				byte[] bytes = new byte[4 * 1024];
				int numReadByte = 0;
				// 读取服务器数据 并写入到文件中
				if (DeviceInfoApi.getIntance().isWisePos4G() && !Build.DISPLAY.contains("WiseManager")) {
					fileOutputStream = new FileOutputStream(curFile, false);
				} else {
					fileOutputStream = new FileOutputStream(curFile, true);
				}
				long readcount = curFileLength;
				while ((numReadByte = bin.read(bytes)) != -1) {
					readcount += numReadByte;
//                BBLog.i(BBLog.TAG, "url2file readcount = " + readcount);
					fileOutputStream.write(bytes, 0, numReadByte);
					callBack.onDownloading(readcount, fileLength);
				}
				bin.close();
			}catch (Exception e) {
				isDownloadSucc = false;
				e.printStackTrace();

				if (e instanceof CertPathValidatorException){
					CertPathValidatorException exception = (CertPathValidatorException) e;
					CertPath certPath = exception.getCertPath();
					BBLog.e(TAG, "SSLContent certification path : "+certPath );
				}
			} finally {
				if(urlConnection != null)
					urlConnection.disconnect();
				IOUtils.flushCloseOutputStream(fileOutputStream);
				IOUtils.closeInputStream(inputStream);
			}
		}
		isDownloadSucc = com.bbpos.wiseapp.tms.utils.FileUtils.isFileDownloadSuccessed(fileName, curFileLength+fileLength, md5);
		if (!isDownloadSucc) {
			curFile.delete();
		}
		return isDownloadSucc;
	}

	public static boolean url2bitmap(String url, String md5, String type, FileDownloadCallBack callBack) {
		BBLog.i(BBLog.TAG, "下载图片 url2bitmap = " + url);
		if (TextUtils.isEmpty(url)) {
			return false;
		}

		File appDir = new File(FileUtils.getWiseAppConfigPath());
		if (!appDir.exists()) appDir.mkdir();
		String fileName = "nothing.png";
		String fileNameTemp = "nothing.png";
		if (type.equals("Logo")) {
			fileNameTemp = "Logo_temp.png";
			fileName = "Logo.png";
		} else if (type.equals("wallpaper")) {
			fileNameTemp = "wallpaper_temp.png";
			fileName = "wallpaper.png";
		} else {
			fileNameTemp = "unknown_temp.png";
			fileName = "unknown.png";
		}
		File fileTemp = new File(appDir, fileNameTemp);
		File file = new File(appDir, fileName);

		if(file.exists() && !TextUtils.isEmpty(md5)){
			if (md5.equals(com.bbpos.wiseapp.tms.utils.FileUtils.getMd5ByFile(file))) {
				BBLog.w(BBLog.TAG, "MD5匹配，图片已存在");
				return true;
			}
		}

		Bitmap bm = null;
		try {
			URL iconUrl = new URL(url);
			HttpsURLConnection urlConnection = null;
			urlConnection = (HttpsURLConnection) iconUrl.openConnection();

			SSLContext sc = WmSSlContent.getSSLContext();
			urlConnection.setSSLSocketFactory(sc.getSocketFactory());
			urlConnection.setHostnameVerifier(new HostnameVerifier() {
				@Override
				public boolean verify(String hostname, SSLSession session) {
					HostnameVerifier hv = HttpsURLConnection.getDefaultHostnameVerifier();
					BBLog.e(TAG, "url2bitmap, httpconnect verify = " + hv.verify(hostname,session));
					return hv.verify(hostname,session);
				}
			});

			urlConnection.setConnectTimeout(Constants.ConnectionTimeout);
			urlConnection.setReadTimeout(Constants.SoTimeout);
			int length = urlConnection.getContentLength();
			urlConnection.connect();
			//获得图像的字符流
			InputStream is = urlConnection.getInputStream();
			BufferedInputStream bis = new BufferedInputStream(is, length);
			BufferedOutputStream bos=new BufferedOutputStream(new FileOutputStream(fileTemp));
			int len=0;
			int readcount = 0;
			byte [] by=new byte[8*1024];
			while ((len=bis.read(by))!=-1) {
				readcount += len;
				bos.write(by, 0, len);
				callBack.onDownloading(readcount, length);
			}
			bos.flush();
			bos.close();
			bis.close();
			is.close();

			com.bbpos.wiseapp.tms.utils.FileUtils.copyFile(fileTemp, file);
		} catch (Exception e) {
			e.printStackTrace();
			return false;
		}
		return true;
	}

	public static final String S3_LOG_STREAMING_URL = "https://download.wisemanager.com/wisemanager-log";
	public static final String S3_LOG_STREAMING_BUCKET_NAME = "wisemanager-log";

	public static final String S3_VIEW_STREAMING_URL = "https://download.wisemanager.com/wisemanager-view";
	public static final String S3_VIEW_STREAMING_BUCKET_NAME = "wisemanager-view";

//	public static final String S3_LOG_STREAMING_KEY_ID = "********************";
//	public static final String S3_LOG_STREAMING_KEY_SECRET = "T8671AvCpNNBfJmgsqX6LPlRTMQ4T+REQEaJxyvC";

	public static final String S3_LOG_STREAMING_CONTENT_TYPE = "application/octet-stream";
	public static final String S3_LOG_STREAMING_DATE_FORMAT = "EEE, dd MMM yyyy HH:mm:ss Z";
	public static final String S3_LOG_STREAMING_DEFAULT_TIMEZONE = "UTC";

	public static final int S3_LOG_STREAMING_BUFFER_SIZE = 8192;
	/**
	 * url上传文件至S3
	 *
	 * @param strUrl 	上传url 			（默认：https://download.wisemanager.com/wisemanager-log）
	 * @param filePath 	S3上存放文件的路径	（例如：uat/20191002）
	 * @param file 		本地存放的文件		（例如：/sdcard/xxx.xxx）
	 */
	public static boolean urlUploadFile(final String strUrl, final String strBucket, final String filePath, final File file) {
		boolean ret = false;
		try {
			String fileName = filePath + "/" + file.getName();
			BBLog.i(BBLog.TAG, "urlUploadFile, fileName: " + fileName);

			SimpleDateFormat simpleDateFormat = new SimpleDateFormat(S3_LOG_STREAMING_DATE_FORMAT, Locale.US);
			simpleDateFormat.setTimeZone(TimeZone.getTimeZone(S3_LOG_STREAMING_DEFAULT_TIMEZONE));
			String date = simpleDateFormat.format(new Date());
			BBLog.i(BBLog.TAG, "urlUploadFile, date: " + date);

			String MD5 = getMd5Base64(file);
			BBLog.i(BBLog.TAG, "urlUploadFile, md5: " + MD5);

			String sig = getSigHmacBase64(MD5, S3_LOG_STREAMING_CONTENT_TYPE, date, strBucket, fileName,
					SecurityOperate.getInstance().getLogStreamS3KeySecret(ContextUtil.getInstance()));
			BBLog.i(BBLog.TAG, "urlUploadFile, sig: " + sig);

			URL url = new URL(strUrl + "/" +  fileName);
			HttpsURLConnection urlConnection = null;
			//添加 请求内容
			urlConnection = (HttpsURLConnection) url.openConnection();

			SSLContext sc = WmSSlContent.getSSLContext();
			urlConnection.setSSLSocketFactory(sc.getSocketFactory());
			urlConnection.setHostnameVerifier(new HostnameVerifier() {
				@Override
				public boolean verify(String hostname, SSLSession session) {
					HostnameVerifier hv = HttpsURLConnection.getDefaultHostnameVerifier();
					BBLog.e(TAG, "url2bitmap, httpconnect verify = " + hv.verify(hostname,session));
					return hv.verify(hostname,session);
				}
			});
			//设置http连接属性
			urlConnection.setDoOutput(true);// http正文内，因此需要设为true, 默认情况下是false;
			urlConnection.setDoInput(true);// 设置是否从httpUrlConnection读入，默认情况下是true;
			urlConnection.setRequestMethod("PUT"); // 可以根据需要 提交 GET、POST、DELETE、PUT等http提供的功能
			//urlConnection.setUseCaches(false);//设置缓存，注意设置请求方法为post不能用缓存
			// urlConnection.setInstanceFollowRedirects(true);

			urlConnection.setRequestProperty("Host", "download.wisemanager.com");  //设置请 求的服务器网址，域名，例如***.**.***.***
			urlConnection.setRequestProperty("Cache-Control", "no-cache");
			urlConnection.setRequestProperty("Accept", "*/*");  //设置编码语言
			urlConnection.setRequestProperty("Accept-Encoding", "gzip, deflate");  //设置连接的状态
			urlConnection.setRequestProperty("Connection", "keep-alive");  //设置连接的状态
			urlConnection.setRequestProperty("Content-Type", S3_LOG_STREAMING_CONTENT_TYPE);//设定 请求格式 json，也可以设定xml格式的

			urlConnection.setRequestProperty("Authorization", "AWS " + SecurityOperate.getInstance().getLogStreamS3KeyID(ContextUtil.getInstance()) + ":" + sig);
			urlConnection.setRequestProperty("Content-MD5", MD5);
//			urlConnection.setRequestProperty("Content-Length", length);  //设置连接的状态
			urlConnection.setRequestProperty("Date", date); //设置编码语言

			urlConnection.setReadTimeout(10000);//设置读取超时时间
			urlConnection.setConnectTimeout(10000);//设置连接超时时间
			urlConnection.connect();

			OutputStream out = urlConnection.getOutputStream();

			FileInputStream inputStream2 = new FileInputStream(file);
			byte[] buffer = new byte[S3_LOG_STREAMING_BUFFER_SIZE];
			int bytesRead = -1;

			while ((bytesRead = inputStream2.read(buffer)) != -1) {
				out.write(buffer, 0, bytesRead);
			}
			out.close();
			inputStream2.close();

			int responseCode = urlConnection.getResponseCode();
			StringBuffer sbuffer = null;
			if(HttpURLConnection.HTTP_OK == responseCode) {
				BBLog.i(BBLog.TAG,"请求成功: responseCode=" + urlConnection.getResponseCode() +
						", responseMessage=" + urlConnection.getResponseMessage());
				ret = true;
			} else {
				BBLog.i(BBLog.TAG,"请求失败: responseCode=" + urlConnection.getResponseCode() +
						", responseMessage=" + urlConnection.getResponseMessage());// + ", errorStream=" + inputStream2String(urlConnection.getErrorStream()));
				ret = false;
			}
			urlConnection.disconnect();
		} catch (UnsupportedEncodingException e) {
			e.printStackTrace();
			return false;
		} catch (NoSuchAlgorithmException e) {
			e.printStackTrace();
			return false;
		} catch (InvalidKeyException e) {
			e.printStackTrace();
			return false;
		} catch (MalformedURLException e) {
			e.printStackTrace();
			return false;
		} catch (Exception e) {
			e.printStackTrace();
			return false;
		}
		return ret;
	}

	public static boolean urlUploadPicture(final String strUrl, final String strBucket, final String filePath) {
		boolean ret = false;
		long screencapTime = 0;
		long checkTime = 0;
		long uploadTime = 0;
		try {
			Long cur = System.currentTimeMillis();
			byte[] scByteArray = new byte[1024 * 1024];
			try {
				scByteArray = getScreencapBytes();
			} catch (Exception e) {
				e.printStackTrace();
			}
			screencapTime = System.currentTimeMillis() - cur;

			cur = System.currentTimeMillis();
//			BBLog.i(BBLog.TAG, "urlUploadFile, filePath: " + filePath);

			SimpleDateFormat simpleDateFormat = new SimpleDateFormat(S3_LOG_STREAMING_DATE_FORMAT);
			simpleDateFormat.setTimeZone(TimeZone.getTimeZone(S3_LOG_STREAMING_DEFAULT_TIMEZONE));
			String date = simpleDateFormat.format(new Date());
//			BBLog.i(BBLog.TAG, "urlUploadFile, date: " + date);

			String MD5 = getMd5Base64ByteArray(scByteArray);
//			BBLog.i(BBLog.TAG, "urlUploadFile, md5: " + MD5);

			String sig = getSigHmacBase64(MD5, S3_LOG_STREAMING_CONTENT_TYPE, date, strBucket, filePath,
					SecurityOperate.getInstance().getLogStreamS3KeySecret(ContextUtil.getInstance()));
//			BBLog.i(BBLog.TAG, "urlUploadFile, sig: " + sig);
			checkTime = System.currentTimeMillis() - cur;

			cur = System.currentTimeMillis();
			URL url = new URL(strUrl + "/" +  filePath);
			HttpsURLConnection urlConnection = null;
			//添加 请求内容
			urlConnection= (HttpsURLConnection) url.openConnection();

			SSLContext sc = WmSSlContent.getSSLContext();
			urlConnection.setSSLSocketFactory(sc.getSocketFactory());
			urlConnection.setHostnameVerifier(new HostnameVerifier() {
				@Override
				public boolean verify(String hostname, SSLSession session) {
					HostnameVerifier hv = HttpsURLConnection.getDefaultHostnameVerifier();
					BBLog.e(TAG, "url2bitmap, httpconnect verify = " + hv.verify(hostname,session));
					return hv.verify(hostname,session);
				}
			});
			//设置http连接属性
			urlConnection.setDoOutput(true);// http正文内，因此需要设为true, 默认情况下是false;
			urlConnection.setDoInput(true);// 设置是否从httpUrlConnection读入，默认情况下是true;
			urlConnection.setRequestMethod("PUT"); // 可以根据需要 提交 GET、POST、DELETE、PUT等http提供的功能
			//urlConnection.setUseCaches(false);//设置缓存，注意设置请求方法为post不能用缓存
			// urlConnection.setInstanceFollowRedirects(true);

			urlConnection.setRequestProperty("Host", "download.wisemanager.com");  //设置请 求的服务器网址，域名，例如***.**.***.***
			urlConnection.setRequestProperty("Cache-Control", "no-cache");
			urlConnection.setRequestProperty("Accept", "*/*");  //设置编码语言
			urlConnection.setRequestProperty("Accept-Encoding", "gzip, deflate");  //设置连接的状态
			urlConnection.setRequestProperty("Connection", "keep-alive");  //设置连接的状态
			urlConnection.setRequestProperty("Content-Type", S3_LOG_STREAMING_CONTENT_TYPE);//设定 请求格式 json，也可以设定xml格式的

			urlConnection.setRequestProperty("Authorization", "AWS " + SecurityOperate.getInstance().getLogStreamS3KeyID(ContextUtil.getInstance()) + ":" + sig);
			urlConnection.setRequestProperty("Content-MD5", MD5);
//			urlConnection.setRequestProperty("Content-Length", length);  //设置连接的状态
			urlConnection.setRequestProperty("Date", date); //设置编码语言

			urlConnection.setReadTimeout(10000);//设置读取超时时间
			urlConnection.setConnectTimeout(10000);//设置连接超时时间
			OutputStream out = urlConnection.getOutputStream();
			out.write(scByteArray);
			out.close();

			int responseCode = urlConnection.getResponseCode();
			uploadTime = System.currentTimeMillis() - cur;
			if(HttpURLConnection.HTTP_OK == responseCode) {
				BBLog.i(BBLog.TAG,"RemoteView 请求成功" +
						", 響應碼值：" + urlConnection.getResponseCode() +
						", 響應信息：" + urlConnection.getResponseMessage() +
						", 圖片大小：" + scByteArray.length + " b" +
						", 截屏耗時：" + screencapTime + " ms" +
						", 校驗耗時：" + checkTime + " ms" +
						", 上傳耗時：" + uploadTime + " ms");
				ret = true;
			} else {
				BBLog.i(BBLog.TAG,"RemoteView 请求失败" +
						", 響應碼值：" + urlConnection.getResponseCode() +
						", 響應信息：" + urlConnection.getResponseMessage() +
						", 錯誤信息：" + inputStream2String(urlConnection.getErrorStream()) +
						", 圖片大小：" + scByteArray.length + " b" +
						", 截屏耗時：" + screencapTime + " ms" +
						", 校驗耗時：" + checkTime + " ms" +
						", 上傳耗時：" + uploadTime + " ms");
				ret = false;
			}
			urlConnection.disconnect();
		} catch (UnsupportedEncodingException e) {
			e.printStackTrace();
			return false;
		} catch (NoSuchAlgorithmException e) {
			e.printStackTrace();
			return false;
		} catch (InvalidKeyException e) {
			e.printStackTrace();
			return false;
		} catch (MalformedURLException e) {
			e.printStackTrace();
			return false;
		} catch (Exception e) {
			e.printStackTrace();
			return false;
		}

		return ret;
	}

	public static byte[] getScreencapBytes() throws Exception {
		StringBuilder buffer = new StringBuilder(1024 * 1024);
		byte[] tempBuffer = new byte[1024 * 1024];
		try {
			//不加-p會是原始像素
//			Process exec = Runtime.getRuntime().exec("screencap -p");
			Process exec = SecurityOperate.getInstance().execCommand(ContextUtil.getInstance(),"screencap -p");
			try {
				final InputStream inputStream = exec.getInputStream();
				BufferedInputStream bufferedInputStream = new BufferedInputStream(inputStream);
				//清空缓存内容
				buffer.setLength(0);
				int count;
//				long start = System.currentTimeMillis();
				while ((count = bufferedInputStream.read(tempBuffer)) > 0) {
					buffer.append(new String(tempBuffer, 0, count, "ISO-8859-1"));
				}
//				BBLog.v(BBLog.TAG, "获取截屏耗时:" + (System.currentTimeMillis() - start) + "ms png图片大小: " + buffer.length());
				bufferedInputStream.close();
				int retCode = exec.waitFor();
				exec.destroy();
//				BBLog.e(BBLog.TAG, "retCode: " + retCode);
			} catch (InterruptedException e) {
				e.printStackTrace();
			}
		} catch (Exception e) {
			e.printStackTrace();
		}

		try {
			tempBuffer = buffer.toString().getBytes("ISO-8859-1");
		} catch (Exception e) {
			e.printStackTrace();
		}

		return tempBuffer;
	}

	private static String getMd5Base64ByteArray(byte[] data) throws IOException, NoSuchAlgorithmException {
		MessageDigest md5Digest = MessageDigest.getInstance("MD5");
		md5Digest.update(data);
		byte messageDigest[] = md5Digest.digest();
		return Base64.encodeToString(messageDigest, Base64.DEFAULT);
	}

	private static String getMd5Base64(File file) throws IOException, NoSuchAlgorithmException {
		MessageDigest md5Digest = MessageDigest.getInstance("MD5");
		md5Digest.update(File2Bytes(file));
		byte messageDigest[] = md5Digest.digest();
		return Base64.encodeToString(messageDigest, Base64.DEFAULT);
	}

	private static String getSigHmacBase64(String md5, String content_type, String date, String bucket_name, String fileName, String key_secret) throws UnsupportedEncodingException, NoSuchAlgorithmException, InvalidKeyException {
		String secret = "PUT\n" + md5 + content_type + "\n" + date + "\n/" + bucket_name + "/" + fileName;
		return sha1(secret, key_secret);
	}

	private static String sha1(String str, String keyString) throws UnsupportedEncodingException, NoSuchAlgorithmException, InvalidKeyException {
		SecretKeySpec key = new SecretKeySpec((keyString).getBytes("UTF-8"), "HmacSHA1");
		Mac mac = Mac.getInstance("HmacSHA1");
		mac.init(key);
		byte[] bytes = mac.doFinal(str.getBytes("UTF-8"));
		return Base64.encodeToString(bytes, Base64.DEFAULT);
	}

	public static byte[] File2Bytes(File file) throws IOException {
		byte[] b = new byte[S3_LOG_STREAMING_BUFFER_SIZE];
		FileInputStream fileInputStream = new FileInputStream(file);
		ByteArrayOutputStream outputStream = new ByteArrayOutputStream(S3_LOG_STREAMING_BUFFER_SIZE);
		for (int length; (length = fileInputStream.read(b)) != -1;) {
			outputStream.write(b, 0, length);
		}
		fileInputStream.close();
		outputStream.close();
		return outputStream.toByteArray();
	}

	public static String inputStream2String (InputStream in) throws IOException {
		if (in == null) {
			return null;
		}

		StringBuffer out = new StringBuffer();
		byte[] b = new byte[S3_LOG_STREAMING_BUFFER_SIZE];
		for (int n; (n = in.read(b)) != -1;) {
			out.append(new String(b, 0, n));
		}

		return out.toString();
	}
}
