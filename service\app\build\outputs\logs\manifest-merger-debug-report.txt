-- Merging decision tree log ---
manifest
ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:2:1-314:12
INJECTED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:2:1-314:12
INJECTED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:2:1-314:12
INJECTED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:2:1-314:12
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-4\bf3ce957942acc18ffc5566e978b28d1\transformed\material-1.12.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\cb0c4ee9fb1b389850a128017f9fd3b5\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\ff51d80052c885105c08f5f52e375989\transformed\constraintlayout-2.0.1\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\82b09ff52065ac1ba4a0ffc39771c3aa\transformed\appcompat-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\ab6cf38908650051fe2f1a6d80a7e542\transformed\viewpager2-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.fragment:fragment:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-4\c3da6a89f54cf5487d08ff7a898f4db6\transformed\fragment-1.5.4\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.activity:activity:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\df68dff3ffddcdd3a0ab23b595cdcb60\transformed\activity-1.8.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core-ktx:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\eab7f745b2414a6d8e03970585b7bdab\transformed\core-ktx-1.13.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\5ef8727fe47792f238431de99dcedbb5\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\cbaa961bfd044a072a7d4e5814fece02\transformed\emoji2-1.3.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\02768d0950aed636a3a4ae68ec291012\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\486e573e82c2bf861ffbe09f61e94d1c\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\3b5aec1aa42fa8f5f36f7b1e7cf7876a\transformed\transition-1.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\8cdeef4ccc02f9a2d86ede3fbb9506a9\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\d4ed086affecfcc4ca7b896f26a26da4\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\31ccf84cb53e6cf4d52578cd2ec6b72a\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\475522cfe3f26e9754bcdbf5c44d7534\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\216a56dc0936e6f90bfe5691acfbae19\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\d572909e4152f3bba6c4f8c8d563ebfb\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\c3541e9e54d10db1cdf05c56bb1f41f6\transformed\recyclerview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\5badd919721e740d68b2bd278a8b940e\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\77d65a215b30b35b66736631fc83d763\transformed\core-1.13.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\2f9ca5d82b4779d8b851207a1483b22c\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\94e6bde2c3b7c57bbb4f658ac05944ca\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\2324ba83f0e58d631ef534998e5b824f\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\22617dcd7d0bfafe651a5718d9c5e1c6\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\178acdbae628dedc05771fd6cf4a0425\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\217c51b6d36c700efbd89e91e99150db\transformed\lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\07ba09faee8a1a019c72ed051b349696\transformed\savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\446099b80423dad66db848285f7a7ab3\transformed\annotation-experimental-1.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\5afe5e34af731426be04e9e3f843f09b\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\3a86084b347b0870efb55c81983d610b\transformed\profileinstaller-1.3.1\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\1bdef8dfb21e378bbe694c1c4ac8e326\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\8fe79bde9f42a4d35d2077a9626667a0\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\f08e1d989efc6fa4cc808c7f1539c3e8\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\c55fff13a9172b184c60d8e76a9555c7\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\1b09faa2cd4d96eefdad8eeefda262ac\transformed\tracing-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\60ef85186be49f1483fdc1c51d8d3679\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\2027979e422b8c88bd168f10b8461f09\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\b33092e6817857c04fe046950386fd0e\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\92864dba3933d295c562aaa4744246d8\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
	package
		INJECTED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml
	android:sharedUserId
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:4:5-46
	android:versionName
		INJECTED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.MOUNT_UNMOUNT_FILESYSTEMS
ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:7:5-84
	android:name
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:7:22-81
uses-permission#android.permission.MANAGE_EXTERNAL_STORAGE
ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:8:5-82
	android:name
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:8:22-79
uses-permission#android.permission.SCHEDULE_EXACT_ALARM
ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:9:5-79
	android:name
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:9:22-76
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:10:5-81
	android:name
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:10:22-78
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:11:5-80
	android:name
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:11:22-77
uses-permission#android.permission.WRITE_MEDIA_STORAGE
ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:12:5-78
	android:name
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:12:22-75
uses-permission#android.permission.MANAGE_DOCUMENTS
ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:13:5-75
	android:name
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:13:22-72
uses-permission#android.permission.INTERNET
ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:16:5-67
	android:name
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:16:22-64
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:17:5-79
	android:name
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:17:22-76
uses-permission#android.permission.ACCESS_WIFI_STATE
ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:18:5-76
	android:name
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:18:22-73
uses-permission#android.permission.CHANGE_WIFI_STATE
ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:19:5-76
	android:name
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:19:22-73
uses-permission#android.permission.CHANGE_NETWORK_STATE
ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:20:5-79
	android:name
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:20:22-76
uses-permission#android.permission.CHANGE_WIFI_MULTICAST_STATE
ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:21:5-86
	android:name
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:21:22-83
uses-permission#android.permission.WAKE_LOCK
ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:25:5-68
	android:name
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:25:22-65
uses-permission#android.permission.RECEIVE_BOOT_COMPLETED
ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:27:5-81
	android:name
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:27:22-78
uses-permission#android.permission.FOREGROUND_SERVICE
ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:29:5-77
	android:name
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:29:22-74
uses-permission#android.permission.GET_TASKS
ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:31:5-68
	android:name
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:31:22-65
uses-permission#android.permission.DOWNLOAD_WITHOUT_NOTIFICATION
ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:32:5-88
	android:name
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:32:22-85
uses-permission#android.permission.RECOVERY
ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:35:5-67
	android:name
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:35:22-64
uses-permission#android.permission.ACCESS_CACHE_FILESYSTEM
ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:36:5-82
	android:name
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:36:22-79
uses-permission#android.permission.REBOOT
ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:37:5-65
	android:name
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:37:22-62
uses-permission#android.permission.UPDATE_DEVICE_STATS
ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:38:5-78
	android:name
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:38:22-75
uses-permission#android.permission.WRITE_SECURE_SETTINGS
ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:39:5-80
	android:name
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:39:22-77
uses-permission#android.permission.BIND_UPDATE_ENGINE
ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:42:5-77
	android:name
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:42:22-74
uses-permission#android.permission.ACCESS_UPDATE_ENGINE
ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:43:5-79
	android:name
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:43:22-76
uses-permission#android.permission.UPDATE_ENGINE
ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:44:5-72
	android:name
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:44:22-69
uses-permission#android.permission.MANAGE_ROLLBACKS
ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:47:5-75
	android:name
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:47:22-72
uses-permission#android.permission.TEST_MANAGE_ROLLBACKS
ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:48:5-80
	android:name
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:48:22-77
uses-permission#android.permission.RECOVERY_REFRESH
ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:49:5-75
	android:name
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:49:22-72
uses-permission#android.permission.BLUETOOTH
ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:52:5-68
	android:name
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:52:22-65
uses-permission#android.permission.BLUETOOTH_ADMIN
ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:53:5-74
	android:name
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:53:22-71
uses-permission#android.permission.BLUETOOTH_CONNECT
ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:54:5-76
	android:name
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:54:22-73
uses-permission#android.permission.BLUETOOTH_SCAN
ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:55:5-73
	android:name
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:55:22-70
uses-permission#android.permission.BLUETOOTH_ADVERTISE
ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:56:5-78
	android:name
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:56:22-75
uses-permission#android.permission.BLUETOOTH_PRIVILEGED
ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:57:5-79
	android:name
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:57:22-76
uses-permission#android.permission.NFC
ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:58:5-62
	android:name
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:58:22-59
uses-permission#android.permission.ACCESS_COARSE_LOCATION
ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:61:5-81
	android:name
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:61:22-78
uses-permission#android.permission.ACCESS_FINE_LOCATION
ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:62:5-79
	android:name
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:62:22-76
uses-permission#android.permission.ACCESS_LOCATION_EXTRA_COMMANDS
ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:63:5-89
	android:name
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:63:22-86
uses-permission#android.permission.ACCESS_CELL_LOCATION
ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:64:5-79
	android:name
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:64:22-76
uses-permission#android.permission.READ_CELL_BROADCASTS
ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:65:5-79
	android:name
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:65:22-76
uses-permission#android.permission.ACTIVITY_RECOGNITION
ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:66:5-79
	android:name
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:66:22-76
uses-permission#android.permission.BODY_SENSORS
ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:67:5-71
	android:name
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:67:22-68
uses-permission#android.permission.READ_PHONE_STATE
ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:70:5-75
	android:name
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:70:22-72
uses-permission#android.permission.READ_PRIVILEGED_PHONE_STATE
ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:71:5-86
	android:name
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:71:22-83
uses-permission#android.permission.CALL_PHONE
ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:72:5-69
	android:name
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:72:22-66
uses-permission#android.permission.ANSWER_PHONE_CALLS
ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:73:5-77
	android:name
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:73:22-74
uses-permission#android.permission.PROCESS_OUTGOING_CALLS
ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:74:5-81
	android:name
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:74:22-78
uses-permission#android.permission.USE_SIP
ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:75:5-66
	android:name
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:75:22-63
uses-permission#android.permission.MODIFY_PHONE_STATE
ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:76:5-77
	android:name
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:76:22-74
uses-permission#android.permission.SHUTDOWN
ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:79:5-67
	android:name
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:79:22-64
uses-permission#android.permission.DEVICE_POWER
ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:80:5-71
	android:name
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:80:22-68
uses-permission#android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS
ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:81:5-95
	android:name
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:81:22-92
uses-permission#android.permission.BATTERY_STATS
ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:82:5-72
	android:name
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:82:22-69
uses-permission#android.permission.SET_TIME
ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:83:5-67
	android:name
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:83:22-64
uses-permission#android.permission.SET_TIME_ZONE
ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:84:5-72
	android:name
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:84:22-69
uses-permission#android.permission.WRITE_SETTINGS
ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:85:5-73
	android:name
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:85:22-70
uses-permission#android.permission.CHANGE_COMPONENT_ENABLED_STATE
ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:86:5-89
	android:name
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:86:22-86
uses-permission#android.permission.KILL_BACKGROUND_PROCESSES
ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:87:5-84
	android:name
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:87:22-81
uses-permission#android.permission.GET_PACKAGE_SIZE
ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:88:5-75
	android:name
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:88:22-72
uses-permission#android.permission.INTERACT_ACROSS_USERS
ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:89:5-80
	android:name
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:89:22-77
uses-permission#android.permission.INTERACT_ACROSS_USERS_FULL
ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:90:5-85
	android:name
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:90:22-82
uses-permission#android.permission.BROADCAST_STICKY
ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:91:5-75
	android:name
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:91:22-72
uses-permission#android.permission.REORDER_TASKS
ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:92:5-72
	android:name
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:92:22-69
uses-permission#android.permission.SYSTEM_ALERT_WINDOW
ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:93:5-78
	android:name
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:93:22-75
uses-permission#android.permission.EXPAND_STATUS_BAR
ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:94:5-76
	android:name
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:94:22-73
uses-permission#android.permission.STATUS_BAR
ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:95:5-69
	android:name
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:95:22-66
uses-permission#android.permission.STATUS_BAR_SERVICE
ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:96:5-77
	android:name
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:96:22-74
uses-permission#android.permission.MANAGE_USERS
ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:97:5-71
	android:name
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:97:22-68
uses-permission#android.permission.PACKAGE_USAGE_STATS
ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:98:5-78
	android:name
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:98:22-75
uses-permission#android.permission.DUMP
ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:99:5-63
	android:name
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:99:22-60
uses-permission#android.permission.READ_LOGS
ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:100:5-68
	android:name
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:100:22-65
uses-permission#android.permission.SET_DEBUG_APP
ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:101:5-72
	android:name
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:101:22-69
uses-permission#android.permission.PERSISTENT_ACTIVITY
ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:102:5-78
	android:name
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:102:22-75
uses-permission#android.permission.CLEAR_APP_USER_DATA
ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:103:5-78
	android:name
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:103:22-75
uses-permission#android.permission.MANAGE_APP_OPS_MODES
ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:104:5-79
	android:name
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:104:22-76
uses-permission#android.permission.BIND_CARRIER_SERVICE
ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:105:5-79
	android:name
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:105:22-76
uses-permission#android.permission.WRITE_APN_SETTINGS
ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:106:5-77
	android:name
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:106:22-74
uses-permission#android.permission.INSTALL_PACKAGES
ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:109:5-75
	android:name
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:109:22-72
uses-permission#android.permission.DELETE_PACKAGES
ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:110:5-74
	android:name
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:110:22-71
uses-permission#android.permission.REQUEST_INSTALL_PACKAGES
ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:111:5-83
	android:name
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:111:22-80
uses-permission#android.permission.FORCE_STOP_PACKAGES
ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:112:5-78
	android:name
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:112:22-75
uses-permission#android.permission.REQUEST_DELETE_PACKAGES
ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:113:5-82
	android:name
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:113:22-79
uses-permission#android.permission.QUERY_ALL_PACKAGES
ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:114:5-77
	android:name
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:114:22-74
uses-permission#android.permission.POST_NOTIFICATIONS
ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:117:5-77
	android:name
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:117:22-74
uses-permission#android.permission.ACCESS_NOTIFICATION_POLICY
ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:118:5-85
	android:name
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:118:22-82
uses-permission#android.permission.RECORD_AUDIO
ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:121:5-71
	android:name
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:121:22-68
uses-permission#android.permission.CAMERA
ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:122:5-65
	android:name
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:122:22-62
uses-permission#android.permission.MODIFY_AUDIO_SETTINGS
ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:123:5-80
	android:name
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:123:22-77
uses-permission#android.permission.VIBRATE
ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:124:5-66
	android:name
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:124:22-63
uses-permission#android.permission.CAPTURE_AUDIO_OUTPUT
ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:127:5-79
	android:name
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:127:22-76
uses-permission#android.permission.CAPTURE_VIDEO_OUTPUT
ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:128:5-79
	android:name
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:128:22-76
uses-permission#android.permission.CAPTURE_SECURE_VIDEO_OUTPUT
ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:129:5-86
	android:name
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:129:22-83
uses-permission#android.permission.FOREGROUND_SERVICE_MEDIA_PROJECTION
ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:132:5-94
	android:name
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:132:22-91
uses-permission#android.permission.BIND_ACCESSIBILITY_SERVICE
ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:135:5-85
	android:name
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:135:22-82
uses-permission#android.permission.GET_ACCOUNTS
ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:138:5-71
	android:name
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:138:22-68
uses-permission#android.permission.READ_CALENDAR
ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:139:5-72
	android:name
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:139:22-69
uses-permission#android.permission.READ_CALL_LOG
ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:140:5-72
	android:name
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:140:22-69
uses-permission#android.permission.READ_CONTACTS
ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:141:5-72
	android:name
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:141:22-69
uses-permission#android.permission.READ_SMS
ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:142:5-67
	android:name
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:142:22-64
uses-permission#android.permission.READ_SYNC_SETTINGS
ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:143:5-77
	android:name
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:143:22-74
uses-permission#android.permission.READ_SYNC_STATS
ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:144:5-74
	android:name
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:144:22-71
uses-permission#android.permission.RECEIVE_MMS
ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:145:5-70
	android:name
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:145:22-67
uses-permission#android.permission.RECEIVE_SMS
ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:146:5-70
	android:name
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:146:22-67
uses-permission#android.permission.RECEIVE_WAP_PUSH
ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:147:5-75
	android:name
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:147:22-72
uses-permission#android.permission.SEND_SMS
ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:148:5-67
	android:name
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:148:22-64
uses-permission#android.permission.UNINSTALL_SHORTCUT
ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:151:5-77
	android:name
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:151:22-74
uses-permission#android.permission.INSTALL_SHORTCUT
ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:152:5-75
	android:name
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:152:22-72
uses-permission#android.permission.SET_WALLPAPER
ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:153:5-72
	android:name
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:153:22-69
uses-permission#android.permission.SET_WALLPAPER_HINTS
ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:154:5-78
	android:name
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:154:22-75
uses-permission#android.permission.DSPREAD
ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:157:5-66
	android:name
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:157:22-63
uses-permission#com.dspread.mdm.service.permissions.MY_BROADCAST
ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:158:5-88
	android:name
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:158:22-85
uses-permission#android.permission.BROADCAST_CLOSE_SYSTEM_DIALOGS
ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:161:5-89
	android:name
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:161:22-86
uses-permission#android.permission.BROADCAST_PACKAGE_ADDED
ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:162:5-82
	android:name
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:162:22-79
uses-permission#android.permission.BROADCAST_PACKAGE_CHANGED
ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:163:5-84
	android:name
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:163:22-81
uses-permission#android.permission.BROADCAST_PACKAGE_INSTALL
ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:164:5-84
	android:name
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:164:22-81
uses-permission#android.permission.BROADCAST_PACKAGE_REPLACED
ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:165:5-85
	android:name
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:165:22-82
uses-permission#android.permission.BROADCAST_PACKAGE_REMOVED
ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:166:5-84
	android:name
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:166:22-81
uses-permission#android.permission.BROADCAST_PACKAGE_RESTARTED
ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:167:5-86
	android:name
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:167:22-83
uses-permission#android.permission.BROADCAST_PACKAGE_FIRST_LAUNCH
ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:168:5-89
	android:name
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:168:22-86
uses-permission#android.permission.BROADCAST_PACKAGE_NEEDS_VERIFICATION
ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:169:5-95
	android:name
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:169:22-92
uses-permission#android.permission.BROADCAST_PACKAGE_VERIFIED
ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:170:5-85
	android:name
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:170:22-82
permission#com.dspread.mdm.service.permissions.MY_BROADCAST
ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:172:5-174:47
	android:protectionLevel
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:174:9-44
	android:name
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:173:9-72
application
ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:177:5-312:19
INJECTED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:177:5-312:19
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-4\bf3ce957942acc18ffc5566e978b28d1\transformed\material-1.12.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-4\bf3ce957942acc18ffc5566e978b28d1\transformed\material-1.12.0\AndroidManifest.xml:22:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\ff51d80052c885105c08f5f52e375989\transformed\constraintlayout-2.0.1\AndroidManifest.xml:9:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\ff51d80052c885105c08f5f52e375989\transformed\constraintlayout-2.0.1\AndroidManifest.xml:9:5-20
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\cbaa961bfd044a072a7d4e5814fece02\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\cbaa961bfd044a072a7d4e5814fece02\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\77d65a215b30b35b66736631fc83d763\transformed\core-1.13.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\77d65a215b30b35b66736631fc83d763\transformed\core-1.13.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\2324ba83f0e58d631ef534998e5b824f\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\2324ba83f0e58d631ef534998e5b824f\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\3a86084b347b0870efb55c81983d610b\transformed\profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\3a86084b347b0870efb55c81983d610b\transformed\profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\f08e1d989efc6fa4cc808c7f1539c3e8\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\f08e1d989efc6fa4cc808c7f1539c3e8\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\c55fff13a9172b184c60d8e76a9555c7\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\c55fff13a9172b184c60d8e76a9555c7\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
	android:extractNativeLibs
		INJECTED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml
	android:requestLegacyExternalStorage
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:187:9-52
	android:roundIcon
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:184:9-54
	android:icon
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:182:9-43
	android:networkSecurityConfig
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:189:9-69
	android:appComponentFactory
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\77d65a215b30b35b66736631fc83d763\transformed\core-1.13.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:185:9-35
	android:label
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:183:9-41
	android:fullBackupContent
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:181:9-54
	tools:targetApi
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:190:9-29
	android:allowBackup
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:179:9-35
	android:theme
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:186:9-45
	android:dataExtractionRules
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:180:9-65
	android:usesCleartextTraffic
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:188:9-44
	android:name
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:178:9-43
activity#com.dspread.mdm.service.ui.activity.TestActivity
ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:195:9-203:20
	android:label
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:198:13-40
	android:exported
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:197:13-36
	android:name
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:196:13-53
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:199:13-202:29
action#android.intent.action.MAIN
ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:200:17-69
	android:name
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:200:25-66
category#android.intent.category.LAUNCHER
ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:201:17-77
	android:name
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:201:27-74
activity#com.dspread.mdm.service.ui.activity.OsUpdateTestActivity
ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:206:9-210:52
	android:label
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:209:13-35
	android:exported
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:208:13-37
	android:theme
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:210:13-49
	android:name
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:207:13-61
activity#com.dspread.mdm.service.modules.remoteview.RequestMediaProjectionActivity
ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:213:9-218:49
	android:excludeFromRecents
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:218:13-46
	android:launchMode
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:217:13-43
	android:exported
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:215:13-37
	android:theme
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:216:13-72
	android:name
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:214:13-78
activity#com.dspread.mdm.service.ui.activity.LockScreenActivity
ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:221:9-234:20
	android:screenOrientation
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:226:13-49
	android:enabled
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:224:13-35
	android:excludeFromRecents
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:227:13-46
	android:launchMode
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:228:13-44
	android:exported
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:225:13-36
	android:theme
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:223:13-57
	android:name
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:222:13-59
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.DEFAULT+category:name:android.intent.category.HOME
ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:229:13-233:29
	android:priority
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:229:28-51
category#android.intent.category.HOME
ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:231:17-73
	android:name
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:231:27-70
category#android.intent.category.DEFAULT
ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:232:17-76
	android:name
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:232:27-73
service#com.dspread.mdm.service.services.SmartMdmBackgroundService
ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:240:9-249:19
	android:enabled
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:242:13-35
	android:exported
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:243:13-36
	android:foregroundServiceType
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:244:13-53
	android:name
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:241:13-63
intent-filter#action:name:com.dspread.mdm.service.START_SERVICE+category:name:android.intent.category.DEFAULT
ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:245:13-248:29
action#com.dspread.mdm.service.START_SERVICE
ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:246:17-80
	android:name
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:246:25-77
service#com.dspread.mdm.service.services.ProvisioningService
ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:252:9-255:40
	android:enabled
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:254:13-35
	android:exported
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:255:13-37
	android:name
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:253:13-57
service#com.dspread.mdm.service.services.MediaProjectionService
ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:258:9-262:63
	android:enabled
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:260:13-35
	android:exported
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:261:13-37
	android:foregroundServiceType
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:262:13-60
	android:name
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:259:13-60
service#com.dspread.mdm.service.services.ServiceKeepAliveService
ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:265:9-268:40
	android:enabled
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:267:13-35
	android:exported
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:268:13-37
	android:name
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:266:13-61
service#com.dspread.mdm.service.services.AppInstallService
ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:271:9-274:40
	android:enabled
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:273:13-35
	android:exported
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:274:13-37
	android:name
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:272:13-55
service#com.dspread.mdm.service.services.AppUninstallService
ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:277:9-280:40
	android:enabled
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:279:13-35
	android:exported
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:280:13-37
	android:name
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:278:13-57
service#com.dspread.mdm.service.platform.monitor.UserInteractionAccessibilityService
ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:283:9-293:19
	android:exported
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:286:13-37
	android:permission
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:285:13-79
	android:name
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:284:13-81
intent-filter#action:name:android.accessibilityservice.AccessibilityService
ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:287:13-289:29
action#android.accessibilityservice.AccessibilityService
ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:288:17-92
	android:name
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:288:25-89
meta-data#android.accessibilityservice
ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:290:13-292:72
	android:resource
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:292:17-69
	android:name
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:291:17-60
receiver#com.dspread.mdm.service.broadcast.receivers.GeofenceReceiver
ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:298:9-310:20
	android:enabled
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:300:13-35
	android:exported
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:301:13-37
	android:name
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:299:13-65
intent-filter#action:name:android.intent.action.BOOT_COMPLETED+action:name:android.intent.action.SCREEN_OFF+action:name:android.intent.action.SCREEN_ON+action:name:android.intent.action.USER_PRESENT+action:name:android.net.wifi.NETWORK_STATE_CHANGED+action:name:android.net.wifi.RSSI_CHANGED
ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:302:13-309:29
action#android.intent.action.SCREEN_ON
ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:303:17-74
	android:name
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:303:25-71
action#android.intent.action.SCREEN_OFF
ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:304:17-75
	android:name
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:304:25-72
action#android.intent.action.USER_PRESENT
ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:305:17-77
	android:name
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:305:25-74
action#android.intent.action.BOOT_COMPLETED
ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:306:17-79
	android:name
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:306:25-76
action#android.net.wifi.RSSI_CHANGED
ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:307:17-72
	android:name
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:307:25-69
action#android.net.wifi.NETWORK_STATE_CHANGED
ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:308:17-81
	android:name
		ADDED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml:308:25-78
uses-sdk
INJECTED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml
INJECTED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-4\bf3ce957942acc18ffc5566e978b28d1\transformed\material-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-4\bf3ce957942acc18ffc5566e978b28d1\transformed\material-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\cb0c4ee9fb1b389850a128017f9fd3b5\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\cb0c4ee9fb1b389850a128017f9fd3b5\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\ff51d80052c885105c08f5f52e375989\transformed\constraintlayout-2.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\ff51d80052c885105c08f5f52e375989\transformed\constraintlayout-2.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\82b09ff52065ac1ba4a0ffc39771c3aa\transformed\appcompat-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\82b09ff52065ac1ba4a0ffc39771c3aa\transformed\appcompat-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\ab6cf38908650051fe2f1a6d80a7e542\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\ab6cf38908650051fe2f1a6d80a7e542\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-4\c3da6a89f54cf5487d08ff7a898f4db6\transformed\fragment-1.5.4\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-4\c3da6a89f54cf5487d08ff7a898f4db6\transformed\fragment-1.5.4\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.activity:activity:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\df68dff3ffddcdd3a0ab23b595cdcb60\transformed\activity-1.8.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\df68dff3ffddcdd3a0ab23b595cdcb60\transformed\activity-1.8.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-ktx:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\eab7f745b2414a6d8e03970585b7bdab\transformed\core-ktx-1.13.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\eab7f745b2414a6d8e03970585b7bdab\transformed\core-ktx-1.13.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\5ef8727fe47792f238431de99dcedbb5\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\5ef8727fe47792f238431de99dcedbb5\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\cbaa961bfd044a072a7d4e5814fece02\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\cbaa961bfd044a072a7d4e5814fece02\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\02768d0950aed636a3a4ae68ec291012\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\02768d0950aed636a3a4ae68ec291012\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\486e573e82c2bf861ffbe09f61e94d1c\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\486e573e82c2bf861ffbe09f61e94d1c\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\3b5aec1aa42fa8f5f36f7b1e7cf7876a\transformed\transition-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\3b5aec1aa42fa8f5f36f7b1e7cf7876a\transformed\transition-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\8cdeef4ccc02f9a2d86ede3fbb9506a9\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\8cdeef4ccc02f9a2d86ede3fbb9506a9\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\d4ed086affecfcc4ca7b896f26a26da4\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\d4ed086affecfcc4ca7b896f26a26da4\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\31ccf84cb53e6cf4d52578cd2ec6b72a\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\31ccf84cb53e6cf4d52578cd2ec6b72a\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\475522cfe3f26e9754bcdbf5c44d7534\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\475522cfe3f26e9754bcdbf5c44d7534\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\216a56dc0936e6f90bfe5691acfbae19\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\216a56dc0936e6f90bfe5691acfbae19\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\d572909e4152f3bba6c4f8c8d563ebfb\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\d572909e4152f3bba6c4f8c8d563ebfb\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\c3541e9e54d10db1cdf05c56bb1f41f6\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\c3541e9e54d10db1cdf05c56bb1f41f6\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\5badd919721e740d68b2bd278a8b940e\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\5badd919721e740d68b2bd278a8b940e\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\77d65a215b30b35b66736631fc83d763\transformed\core-1.13.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\77d65a215b30b35b66736631fc83d763\transformed\core-1.13.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\2f9ca5d82b4779d8b851207a1483b22c\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\2f9ca5d82b4779d8b851207a1483b22c\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\94e6bde2c3b7c57bbb4f658ac05944ca\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\94e6bde2c3b7c57bbb4f658ac05944ca\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\2324ba83f0e58d631ef534998e5b824f\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\2324ba83f0e58d631ef534998e5b824f\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\22617dcd7d0bfafe651a5718d9c5e1c6\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\22617dcd7d0bfafe651a5718d9c5e1c6\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\178acdbae628dedc05771fd6cf4a0425\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\178acdbae628dedc05771fd6cf4a0425\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\217c51b6d36c700efbd89e91e99150db\transformed\lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\217c51b6d36c700efbd89e91e99150db\transformed\lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\07ba09faee8a1a019c72ed051b349696\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\07ba09faee8a1a019c72ed051b349696\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\446099b80423dad66db848285f7a7ab3\transformed\annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\446099b80423dad66db848285f7a7ab3\transformed\annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\5afe5e34af731426be04e9e3f843f09b\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\5afe5e34af731426be04e9e3f843f09b\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\3a86084b347b0870efb55c81983d610b\transformed\profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\3a86084b347b0870efb55c81983d610b\transformed\profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\1bdef8dfb21e378bbe694c1c4ac8e326\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\1bdef8dfb21e378bbe694c1c4ac8e326\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\8fe79bde9f42a4d35d2077a9626667a0\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\8fe79bde9f42a4d35d2077a9626667a0\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\f08e1d989efc6fa4cc808c7f1539c3e8\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\f08e1d989efc6fa4cc808c7f1539c3e8\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\c55fff13a9172b184c60d8e76a9555c7\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\c55fff13a9172b184c60d8e76a9555c7\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\1b09faa2cd4d96eefdad8eeefda262ac\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\1b09faa2cd4d96eefdad8eeefda262ac\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\60ef85186be49f1483fdc1c51d8d3679\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\60ef85186be49f1483fdc1c51d8d3679\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\2027979e422b8c88bd168f10b8461f09\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\2027979e422b8c88bd168f10b8461f09\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\b33092e6817857c04fe046950386fd0e\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\b33092e6817857c04fe046950386fd0e\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\92864dba3933d295c562aaa4744246d8\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\92864dba3933d295c562aaa4744246d8\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
	android:targetSdkVersion
		INJECTED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from E:\SourceCode\Dspread\service\app\src\main\AndroidManifest.xml
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\cbaa961bfd044a072a7d4e5814fece02\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\2324ba83f0e58d631ef534998e5b824f\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\2324ba83f0e58d631ef534998e5b824f\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\3a86084b347b0870efb55c81983d610b\transformed\profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\3a86084b347b0870efb55c81983d610b\transformed\profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\c55fff13a9172b184c60d8e76a9555c7\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\c55fff13a9172b184c60d8e76a9555c7\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\cbaa961bfd044a072a7d4e5814fece02\transformed\emoji2-1.3.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\cbaa961bfd044a072a7d4e5814fece02\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\cbaa961bfd044a072a7d4e5814fece02\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\cbaa961bfd044a072a7d4e5814fece02\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\cbaa961bfd044a072a7d4e5814fece02\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\cbaa961bfd044a072a7d4e5814fece02\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\cbaa961bfd044a072a7d4e5814fece02\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\77d65a215b30b35b66736631fc83d763\transformed\core-1.13.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\77d65a215b30b35b66736631fc83d763\transformed\core-1.13.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\77d65a215b30b35b66736631fc83d763\transformed\core-1.13.0\AndroidManifest.xml:23:9-81
permission#com.dspread.mdm.service.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\77d65a215b30b35b66736631fc83d763\transformed\core-1.13.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\77d65a215b30b35b66736631fc83d763\transformed\core-1.13.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\77d65a215b30b35b66736631fc83d763\transformed\core-1.13.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\77d65a215b30b35b66736631fc83d763\transformed\core-1.13.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\77d65a215b30b35b66736631fc83d763\transformed\core-1.13.0\AndroidManifest.xml:26:22-94
uses-permission#com.dspread.mdm.service.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\77d65a215b30b35b66736631fc83d763\transformed\core-1.13.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\77d65a215b30b35b66736631fc83d763\transformed\core-1.13.0\AndroidManifest.xml:26:22-94
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\2324ba83f0e58d631ef534998e5b824f\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\2324ba83f0e58d631ef534998e5b824f\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\2324ba83f0e58d631ef534998e5b824f\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\3a86084b347b0870efb55c81983d610b\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\3a86084b347b0870efb55c81983d610b\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\3a86084b347b0870efb55c81983d610b\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\3a86084b347b0870efb55c81983d610b\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\3a86084b347b0870efb55c81983d610b\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\3a86084b347b0870efb55c81983d610b\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\3a86084b347b0870efb55c81983d610b\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\3a86084b347b0870efb55c81983d610b\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\3a86084b347b0870efb55c81983d610b\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\3a86084b347b0870efb55c81983d610b\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\3a86084b347b0870efb55c81983d610b\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\3a86084b347b0870efb55c81983d610b\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\3a86084b347b0870efb55c81983d610b\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\3a86084b347b0870efb55c81983d610b\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\3a86084b347b0870efb55c81983d610b\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\3a86084b347b0870efb55c81983d610b\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\3a86084b347b0870efb55c81983d610b\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\3a86084b347b0870efb55c81983d610b\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\3a86084b347b0870efb55c81983d610b\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\3a86084b347b0870efb55c81983d610b\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\3a86084b347b0870efb55c81983d610b\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
