package com.dspread.mdm.service.platform.manager

import android.content.Context
import com.dspread.mdm.service.utils.log.Logger
import java.util.concurrent.atomic.AtomicBoolean

/**
 * 统一服务启动管理器
 * 解决多处启动服务导致的管理混乱问题
 * 
 * 设计原则：
 * 1. 单一入口：所有服务启动请求都通过这里
 * 2. 状态管理：统一的服务状态检查和管理
 * 3. 防重复启动：避免多个地方同时启动服务
 * 4. 清晰职责：每个组件职责明确
 */
object ServiceStartupManager {
    
    private const val TAG = "ServiceStartupManager"
    
    // 服务启动状态标记
    private val isStarting = AtomicBoolean(false)
    private val isInitialized = AtomicBoolean(false)
    
    // 启动原因枚举
    enum class StartupReason {
        APPLICATION_STARTUP,    // 应用启动
        BOOT_COMPLETED,        // 开机完成
        USER_PRESENT,          // 用户解锁
        SCREEN_ON,             // 屏幕点亮
        NETWORK_CHANGE,        // 网络变化
        TIME_TICK,             // 定时检查
        PACKAGE_UPDATE,        // 包更新
        WAKELOCK_RENEWAL,      // WakeLock续期
        MANUAL_START,          // 手动启动
        SERVICE_RESTART        // 服务重启
    }
    
    /**
     * 统一服务启动入口
     * @param context 上下文
     * @param reason 启动原因
     * @param forceStart 是否强制启动（忽略状态检查）
     */
    @Synchronized
    fun startService(context: Context, reason: StartupReason, forceStart: Boolean = false): Boolean {
        return try {
            Logger.com("$TAG 收到服务启动请求: $reason, 强制启动: $forceStart")
            
            // 检查是否正在启动中
            if (isStarting.get() && !forceStart) {
                Logger.com("$TAG 服务正在启动中，跳过重复请求: $reason")
                return true
            }
            
            // 检查服务是否已经运行
            if (!forceStart && ServiceManager.isBackgroundServiceRunning(context)) {
                Logger.com("$TAG 服务已在运行，无需启动: $reason")
                return true
            }
            
            // 设置启动状态
            isStarting.set(true)
            
            // 执行启动逻辑
            val success = performServiceStart(context, reason)
            
            if (success) {
                isInitialized.set(true)
                Logger.com("$TAG 服务启动成功: $reason")
            } else {
                Logger.comE("$TAG 服务启动失败: $reason")
            }
            
            success
            
        } catch (e: Exception) {
            Logger.comE("$TAG 服务启动异常: $reason", e)
            false
        } finally {
            isStarting.set(false)
        }
    }
    
    /**
     * 检查并启动服务（如果需要）
     * @param context 上下文
     * @param reason 检查原因
     * @param silent 是否静默检查（不输出日志）
     */
    fun checkAndStartService(context: Context, reason: StartupReason, silent: Boolean = false): Boolean {
        return try {
            if (!ServiceManager.isBackgroundServiceRunning(context)) {
                if (!silent) {
                    Logger.com("$TAG 检测到服务未运行，启动服务: $reason")
                }
                startService(context, reason)
            } else {
                if (!silent) {
                    Logger.com("$TAG 服务正在运行，无需启动: $reason")
                }
                true
            }
        } catch (e: Exception) {
            Logger.comE("$TAG 检查并启动服务失败: $reason", e)
            false
        }
    }
    
    /**
     * 执行实际的服务启动
     */
    private fun performServiceStart(context: Context, reason: StartupReason): Boolean {
        return try {
            Logger.com("$TAG 开始执行服务启动: $reason")

            // 根据启动原因执行不同的启动策略
            val result = when (reason) {
                StartupReason.APPLICATION_STARTUP -> {
                    // 应用启动：完整初始化
                    ServiceManager.startBackgroundService(context)
                }

                StartupReason.BOOT_COMPLETED -> {
                    // 开机启动：完整初始化
                    ServiceManager.startBackgroundService(context)
                }

                StartupReason.PACKAGE_UPDATE, StartupReason.SERVICE_RESTART -> {
                    // 更新重启：使用重启方法
                    ServiceManager.restartBackgroundService(context)
                }

                else -> {
                    // 其他情况：普通启动
                    ServiceManager.startBackgroundService(context)
                }
            }

            // 等待一小段时间，然后验证服务是否真的启动了
            Thread.sleep(1000)
            val isRunning = ServiceManager.isBackgroundServiceRunning(context)

            if (isRunning) {
                Logger.com("$TAG 服务启动验证成功: $reason")
            } else {
                Logger.comE("$TAG 服务启动验证失败，服务未运行: $reason")
                return false
            }

            result

        } catch (e: Exception) {
            Logger.comE("$TAG 执行服务启动失败: $reason", e)
            false
        }
    }
    
    /**
     * 获取服务状态
     */
    fun getServiceStatus(context: Context): ServiceStatus {
        return ServiceStatus(
            isRunning = ServiceManager.isBackgroundServiceRunning(context),
            isStarting = isStarting.get(),
            isInitialized = isInitialized.get()
        )
    }
    
    /**
     * 服务状态数据类
     */
    data class ServiceStatus(
        val isRunning: Boolean,
        val isStarting: Boolean,
        val isInitialized: Boolean
    )
}
