package com.bbpos.wiseapp.tms.receiver;

import android.bluetooth.BluetoothAdapter;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.net.Uri;
import android.os.Handler;
import android.os.Message;
import android.text.TextUtils;
import android.util.Log;

import com.bbpos.wiseapp.logger.BBLog;
import com.bbpos.wiseapp.logger.MyDiskLogStrategy;
import com.bbpos.wiseapp.logstream.LogService;
import com.bbpos.wiseapp.provisioning.ProvisionDownloadService;
import com.bbpos.wiseapp.service.common.RequestPermission;
import com.bbpos.wiseapp.service.common.UsualData;
import com.bbpos.wiseapp.settings.utils.WirelessUtil;
import com.bbpos.wiseapp.tms.location.GpsLocationManager;
import com.bbpos.wiseapp.tms.utils.BroadcastActions;
import com.bbpos.wiseapp.tms.utils.Constants;
import com.bbpos.wiseapp.tms.utils.ContextUtil;
import com.bbpos.wiseapp.tms.utils.ParameterName;
import com.bbpos.wiseapp.tms.utils.WebSocketServiceListManager;
import com.bbpos.wiseapp.tms.utils.WebSocketTaskListManager;
import com.bbpos.wiseapp.utils.FileUtils;
import com.bbpos.wiseapp.utils.SharedPreferencesUtils;
import com.bbpos.wiseapp.websocket.WebSocketCenter;
import com.bbpos.wiseapp.websocket.WebSocketSender;
import com.bbpos.wiseapp.websocket.handler.GeoProfileHandler;
import com.bbpos.wiseapp.websocket.handler.RulebasedListHandler;
import com.bbpos.wiseapp.websocket.handler.WifiProfileHandler;

public class SystemReceiver extends BroadcastReceiver {
	private Context mContext;
	private static boolean reboot_expire = false;

	Handler myHandler = new Handler() {
		@Override
		public void handleMessage(Message msg) {
			// TODO Auto-generated method stub
			super.handleMessage(msg);
			switch (msg.what) {
				case 0:
					sendFactoryResetBroadcast();
					break;
				case 1:
					if (TextUtils.isEmpty(Constants.BT_MAC)) {
						Constants.BT_MAC = WirelessUtil.getBluetoothMacAddress();
						BBLog.e(BBLog.TAG, "BT Mac = " + Constants.BT_MAC);
						if (!TextUtils.isEmpty(Constants.BT_MAC)) {
							SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_BT_MAC, Constants.BT_MAC);
						}
						if (!Constants.BT_STATUS) {
							if (BluetoothAdapter.getDefaultAdapter() != null) {
								BluetoothAdapter.getDefaultAdapter().disable();
							}
						}
					}

					//刪除到期的APP+
					BBLog.e(BBLog.TAG, "检查到期的App+");
					WebSocketTaskListManager.checkExpireAppPlusTask(mContext);
					BBLog.e(BBLog.TAG, "检查到期的App+ End");

					if (Constants.IS_FIRST_TIME_WEBSOCKET_CONNECTED == false) {
						BBLog.e(BBLog.TAG, "检查Service App+可执行情况");
						WebSocketServiceListManager.checkServiceAppPlusTask(mContext);
						BBLog.e(BBLog.TAG, "检查Service App+可执行情况 End");

						//执行Rulebased
						BBLog.e(BBLog.TAG, "检查Rulebased可执行情况");
						RulebasedListHandler.executeRulebasedList(mContext, myHandler);
						BBLog.e(BBLog.TAG, "检查Rulebased可执行情况 End");

						//下載GMS套件apk
						BBLog.e(BBLog.TAG, "检查GMS套件APK下載");
						if (!ProvisionDownloadService.isBusyNow && ProvisionDownloadService.isGMSAppNeedDownload()) {
							Intent intentservice = new Intent(mContext, ProvisionDownloadService.class);
							mContext.startService(intentservice);
						}
						BBLog.e(BBLog.TAG, "检查GMS套件APK下載 End");
					}
					
					//检查wifi profile状态
					BBLog.e(BBLog.TAG, "检查wifi profile 状态");
					WifiProfileHandler.scheduleWifProfileTask(mContext);
					BBLog.e(BBLog.TAG, "检查wifi profile 状态 end");
					
					//检查APP+ RULE執行結果上送
					BBLog.e(BBLog.TAG, "检查APP+ RULE執行結果上送 ");
					WebSocketTaskListManager.uploadWSTaskResult();
					RulebasedListHandler.uploadRuleResult();
					BBLog.e(BBLog.TAG, "检查APP+ RULE執行結果上送 End");

					//检查GeoFence到期執行情况
					BBLog.e(BBLog.TAG, "检查GeoFence到期執行情况 GEO_STATUS=" + GpsLocationManager.GEO_STATUS + "  M_GEOFENCE_STATUS=" + Constants.M_GEOFENCE_STATUS);
					GeoProfileHandler.executeOnDueGeoProfile();
					BBLog.e(BBLog.TAG, "检查GeoFence到期執行情况 End");

                    //緩存日志寫入文件；
					BBLog.flushMemoryLogToFileAsync();
//					MyDiskLogStrategy.uploadLoggerFile();

					break;
			}
		}
	};

	@Override
	public void onReceive(Context arg0, Intent arg1) {
		// TODO Auto-generated method stub
		BBLog.e(BBLog.TAG, "SystemReceiver onReceive " + arg1.getAction());
		if (arg1.getAction().equals("prepare_to_factory_reset")) {
			mContext = arg0;
			WebSocketSender.C0202_DeviceEventUpload("5", "", null);
			myHandler.sendEmptyMessageDelayed(0, 3000);
		} else if (arg1.getAction().equals(Intent.ACTION_SHUTDOWN)) {
			mContext = arg0;
			WebSocketSender.C0902_BatteryStatusUpload(Constants.BAT_LEVEL, Constants.BAT_HEALTH, Constants.BAT_TEMP, Constants.IS_BATTERY_CHARGING, Constants.IS_BATTERY_LOW, true, Constants.IS_OUT_OF_BATTERY);
		} else if (arg1.getAction().equals(Intent.ACTION_TIME_TICK)) {
			mContext = arg0;
			myHandler.sendEmptyMessage(1);
		} else if (BluetoothAdapter.ACTION_STATE_CHANGED.equals(arg1.getAction())) {
			final int state = arg1.getIntExtra(BluetoothAdapter.EXTRA_STATE, BluetoothAdapter.ERROR);
			switch (state) {
				case BluetoothAdapter.STATE_OFF:
					GpsLocationManager.unregisterBLEScanner();
					break;
				case BluetoothAdapter.STATE_ON:
					if (TextUtils.isEmpty(Constants.BT_MAC)) {
						Constants.BT_MAC = WirelessUtil.getBluetoothMacAddress();
						BBLog.e(BBLog.TAG, "BT Mac = " + Constants.BT_MAC);
						SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_BT_MAC, Constants.BT_MAC);
						//获取mac的情况，要判断是否主动关闭
						if (!Constants.BT_STATUS) {
							BluetoothAdapter adapter = BluetoothAdapter.getDefaultAdapter();
							adapter.disable();
						}
					} else if (!Constants.BT_ENABLE
						&& SharedPreferencesUtils.getSharePreferencesValue(UsualData.SHARED_PREFERENCES_ALLOW_BT_LISTENING, "1").equals("1")) {
						BluetoothAdapter adapter = BluetoothAdapter.getDefaultAdapter();
						adapter.disable();
					}
					if (Constants.BT_ENABLE) {
						GpsLocationManager.registerBLEScanner();
						if (GpsLocationManager.SCAN_MODE == 1) {
							GpsLocationManager.startBLEScan();
						}
					}
					break;
			}
		} else if (UsualData.ACTION_DELETE_APK.equals(arg1.getAction())) {
			String packName = arg1.getStringExtra(ParameterName.packName);
			Uri uri = Uri.fromParts("package", packName, null);
			Intent intent = new Intent(Intent.ACTION_DELETE, uri);
			intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
			arg0.startActivity(intent);
		} else if (UsualData.ACTION_LAUNCH_COMPLETE.equals(arg1.getAction())) {
			if (Constants.IS_FIRST_PROVISIONING_COMPLETED) {
				Intent it = new Intent(UsualData.ACTION_LAUNCHER_REFRESH);
				String configFilePath = FileUtils.getConfigFilePath();
            	String config = FileUtils.readFile(configFilePath);
            	it.putExtra("config", config);
				ContextUtil.getInstance().sendBroadcast(it);
			}
		} else if (BroadcastActions.LOGCAT_STREAM_TIME.equals(arg1.getAction())) {
//			ContextUtil.stopLogService();
			LogService.stopLogServiceUpload();
		}
	}

	private void sendFactoryResetBroadcast() {
		Intent intent = new Intent();
		intent.setAction("mdm_finish_job_and_begin_factory_reset");
		mContext.sendBroadcast(intent, RequestPermission.REQUEST_PERMISSION_MY_BROADCAST);
	}
}
