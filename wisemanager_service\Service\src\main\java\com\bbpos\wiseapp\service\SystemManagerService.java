package com.bbpos.wiseapp.service;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.Calendar;
import java.util.List;

import android.app.NotificationManager;
import android.app.Service;
import android.bluetooth.BluetoothAdapter;
import android.content.Context;
import android.content.Intent;
import android.content.res.Configuration;
import android.net.ConnectivityManager;
import android.net.Uri;
import android.net.wifi.WifiManager;
import android.os.Binder;
import android.os.Build;
import android.os.RemoteException;
import android.provider.Settings;
import android.telephony.TelephonyManager;
import android.text.TextUtils;
import android.util.Log;

import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import com.bbpos.wiseapp.logger.BBLog;
import com.bbpos.wiseapp.service.common.RequestPermission;
import com.bbpos.wiseapp.service.common.UsualData;
import com.bbpos.wiseapp.system.api.SysIntermediateApi;
import com.bbpos.wiseapp.system.api.SystemApi;
import com.bbpos.wiseapp.tms.utils.ContextUtil;
import com.bbpos.wiseapp.utils.ByteUtils;
import com.bbpos.wiseapp.utils.SharedPreferencesUtils;

import com.bbpos.wiseapp.sdk.system.IBackupObserver;
import com.bbpos.wiseapp.sdk.system.IRestoreObserver;
import com.bbpos.wiseapp.sdk.system.ISystemManager;

/**
 * 系统管理接口
 * <AUTHOR> */
public class SystemManagerService extends ISystemManager.Stub{
	private Service service;

	public SystemManagerService(Service service){
		this.service = service;
	}
	
	@Override
	public void backupByPackage(List<String> pkgList, final IBackupObserver listener) throws RemoteException {
//		try {
//			final BackupAPI backupApi = BackupAPIFactory.create(service);
//			JSONObject backupJson = new JSONObject();
//			JSONArray pkgNameArray = new JSONArray();
//			String callingPkgName = getMyCallingPackage();
//			for (int i = 0; i < pkgList.size(); i++) {
//				String pkgName = pkgList.get(i);
//				if(!pkgName.equals(callingPkgName))
//					pkgNameArray.put(pkgList.get(i));
//			}
//
//			backupJson.put(BackupApi.PARAM_PACKAGES, pkgNameArray);
//			backupJson.put(BackupApi.PARAM_PAR, Constants.IS_PARFILE_BAKCUP);
//			// 传入文件存放路径
//			backupJson.put(BackupApi.PARAM_DATA_FILE_PATH, Constants.SD_PATH);
//			backupJson.put(BackupApi.PARAM_DATA_ACCOUNT, true);
//
//			String backupArgs = backupJson.toString();
//
//			backupApi.backup(backupArgs, new OnBackupListener() {
//
//				@Override
//				public void onResult(int code, String fileName) {
//					try {
//						listener.onBackupFinished(code,"msg", fileName);
//						backupApi.finish();
//					} catch (RemoteException e) {
//						e.printStackTrace();
//					}
//				}
//			});
//		} catch (JSONException e) {
//			e.printStackTrace();
//		} catch (Exception e) {
//			e.printStackTrace();
//		}
	}

	@Override
	public void restore(String path, final IRestoreObserver listener) throws RemoteException {
	    if (!TextUtils.isEmpty(path)) {
            if ("reboot".equals(path)) {
                SystemApi.shutdown(service);
            }
        }
	}

	@Override
	public void setSleepTime(int i) throws RemoteException {
		Settings.System.putInt(service.getContentResolver(),android.provider.Settings.System.SCREEN_OFF_TIMEOUT, i*1000);
	}

	@Override
	public void setScreenBrightness(int i) throws RemoteException {
		Settings.System.putInt(service.getContentResolver(), Settings.System.SCREEN_BRIGHTNESS_MODE, Settings.System.SCREEN_BRIGHTNESS_MODE_MANUAL);
		//保存到系统中
		Uri uri = android.provider.Settings.System.getUriFor(Settings.System.SCREEN_BRIGHTNESS);
		android.provider.Settings.System.putInt(service.getContentResolver(), Settings.System.SCREEN_BRIGHTNESS, i);
		service.getContentResolver().notifyChange(uri, null);
	}

	@Override
	public void setAutoTime(boolean b) throws RemoteException {
		if (b) {
			String timeSettings = android.provider.Settings.Global.getString(service.getContentResolver(), android.provider.Settings.Global.AUTO_TIME);
			if (timeSettings.contentEquals("0")) {
				android.provider.Settings.Global.putString(service.getContentResolver(), android.provider.Settings.Global.AUTO_TIME, "1");
			}
		} else {
			String timeSettings = android.provider.Settings.Global.getString(service.getContentResolver(), android.provider.Settings.Global.AUTO_TIME);
			if (!timeSettings.contentEquals("0")) {
				android.provider.Settings.Global.putString(service.getContentResolver(), android.provider.Settings.Global.AUTO_TIME, "0");
			}
		}
	}

	@Override
	public void setAutoTimezone(boolean b) throws RemoteException {
		android.provider.Settings.Global.putInt(service.getContentResolver(),
				android.provider.Settings.Global.AUTO_TIME_ZONE, b ? 1 : 0);
	}

	@Override
	public void setSystemFontSize(float v) throws RemoteException {
		Method method;
		try {
			Configuration mconfig = new Configuration();
			Class<?> activityManagerNative = Class.forName("android.app.ActivityManagerNative");
			try {
				Object am = activityManagerNative.getMethod("getDefault").invoke(activityManagerNative);
				method = am.getClass().getMethod("updatePersistentConfiguration", android.content.res.Configuration.class);
				mconfig.fontScale = v;
				method.invoke(am, mconfig);
				BBLog.e(CloudService.V2_TAG, "setFontSize  success ");
			} catch (IllegalArgumentException e) {
				e.printStackTrace();
			} catch (SecurityException e) {
				e.printStackTrace();
			} catch (IllegalAccessException e) {
				e.printStackTrace();
			} catch (InvocationTargetException e) {
				e.printStackTrace();
			} catch (NoSuchMethodException e) {
				e.printStackTrace();
			}
		} catch (ClassNotFoundException e) {
			e.printStackTrace();
		}
	}

	@Override
	public void setDoNotDisturb(boolean b) throws RemoteException {
		if (b) {
			NotificationManager mNotificationManager = (NotificationManager) service.getSystemService(Context.NOTIFICATION_SERVICE);
			if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
				mNotificationManager.setInterruptionFilter(NotificationManager.INTERRUPTION_FILTER_NONE);
			}
		} else {
			NotificationManager mNotificationManager = (NotificationManager) service.getSystemService(Context.NOTIFICATION_SERVICE);
			if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
				mNotificationManager.setInterruptionFilter(NotificationManager.INTERRUPTION_FILTER_ALL);
			}
		}
	}

	@Override
	public void setRoamingDataEnable(boolean b) throws RemoteException {
		if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
			TelephonyManager telephonyService = (TelephonyManager) service.getSystemService(Context.TELEPHONY_SERVICE);
			try {
				Method setDataEnabled = telephonyService.getClass().getDeclaredMethod("setDataEnabled",boolean.class);
				if (null != setDataEnabled) {
					setDataEnabled.invoke(telephonyService, b);
				}
			} catch (Exception e) {
				e.printStackTrace();
			}
		}else {
			Class[] setArgArray = new Class[] {boolean.class};
			try {
				ConnectivityManager conManager = (ConnectivityManager) service.getSystemService(Context.CONNECTIVITY_SERVICE);
				Method mSetMethod = conManager.getClass().getMethod("setMobileDataEnabled", setArgArray);
				if (mSetMethod !=null)
					mSetMethod.invoke(conManager,b);
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
	}

	@Override
	public void setWiFiEnable(boolean b) throws RemoteException {
		try {
			//打開wifi
			WifiManager mWifiManager = (WifiManager) service.getApplicationContext().getSystemService(Context.WIFI_SERVICE);
			mWifiManager.setWifiEnabled(b);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	@Override
	public void setBluetoothEnable(boolean b) throws RemoteException {
		try {
			if (b) {
				BluetoothAdapter mBluetoothAdapter = BluetoothAdapter.getDefaultAdapter();
				mBluetoothAdapter.enable();
			} else {
				BluetoothAdapter mBluetoothAdapter = BluetoothAdapter.getDefaultAdapter();
				mBluetoothAdapter.disable();
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	@Override
	public void setAirplaneModeEnable(boolean b) throws RemoteException {
		if (Build.VERSION.SDK_INT <= Build.VERSION_CODES.JELLY_BEAN) {
			Settings.System.putInt(service.getContentResolver(), Settings.System.AIRPLANE_MODE_ON, b ? 1 : 0);
		} else {
			Settings.Global.putInt(service.getContentResolver(), Settings.Global.AIRPLANE_MODE_ON, b ? 1 : 0);
		}
		Intent intent = new Intent(Intent.ACTION_AIRPLANE_MODE_CHANGED);
		intent.putExtra("state", b);
		service.sendBroadcast(intent, RequestPermission.REQUEST_PERMISSION_MY_BROADCAST);
	}

	@Override
	public void setStatusBarEnabled(boolean b) throws RemoteException {
		if (b) {
			Intent intent = new Intent("bbpos_show_status_bar");
			service.sendBroadcast(intent, RequestPermission.REQUEST_PERMISSION_MY_BROADCAST);
		} else {
			Intent intent = new Intent("bbpos_hide_status_bar");
			service.sendBroadcast(intent, RequestPermission.REQUEST_PERMISSION_MY_BROADCAST);
		}
	}

	@Override
	public void setNavigationBarEnabled(boolean b) throws RemoteException {
		if (b) {
			Intent intent = new Intent("bbpos_show_navigation_bar");
			service.sendBroadcast(intent, RequestPermission.REQUEST_PERMISSION_MY_BROADCAST);
		} else {
			Intent intent = new Intent("bbpos_hide_navigation_bar");
			service.sendBroadcast(intent, RequestPermission.REQUEST_PERMISSION_MY_BROADCAST);
		}
	}

	@Override
	public void setADBEnabled(boolean b) throws RemoteException {
		if (b) {
			String timeSettings = android.provider.Settings.Global.getString(service.getContentResolver(), android.provider.Settings.Global.ADB_ENABLED );
			if (timeSettings.contentEquals("0")) {
				android.provider.Settings.Global.putString(service.getContentResolver(), android.provider.Settings.Global.ADB_ENABLED, "1");
			}
		} else {
			String timeSettings = android.provider.Settings.Global.getString(service.getContentResolver(), android.provider.Settings.Global.ADB_ENABLED);
			if (!timeSettings.contentEquals("0")) {
				android.provider.Settings.Global.putString(service.getContentResolver(), android.provider.Settings.Global.ADB_ENABLED, "0");
			}
		}
	}

	@Override
	public void setDeveloperSettingModeEnable(boolean b) throws RemoteException {
		Settings.Global.putInt(service.getContentResolver(), Settings.Global.DEVELOPMENT_SETTINGS_ENABLED, b ? 1 : 0);
		ContextUtil.getInstance().sendBroadcast(
				new Intent("com.android.settingslib.development.DevelopmentSettingsEnabler.SETTINGS_CHANGED"),
				RequestPermission.REQUEST_PERMISSION_INTERNET);
	}

	@Override
	public void setSystemProperty(String s, String s1) throws RemoteException {
		SysIntermediateApi.getIntance().setProp(s, s1);
	}

	@Override
	public void getSystemProperty(String s) throws RemoteException {
		SysIntermediateApi.getIntance().getProp(s);
	}

	@Override
	public void reboot() {
		SystemApi.reboot(service);
	}

	@Override
	public void updateSystem(String path, int type) throws RemoteException {
		if (type == 0) {
			SystemApi.updateSystem(service, path);
		} else if (type == 1) {
			SystemApi.replaceLogo(null);
		} else if (type == 2) {
			SystemApi.replaceBootAnimation(null);
		} else if (type == 3) {
			BBLog.v(CloudService.V2_TAG, "setConfigInfoKeyValue()");
			CloudService.setConfigInfoKeyValue();
		} else if (type == 4) {
			BBLog.v(CloudService.V2_TAG, "setTimezone and setTime");
			setTimezone();
			setTime();
			Settings.System.putString(service.getContentResolver(), android.provider.Settings.System.TIME_12_24, "24");
		} else if (type == 5) {
            BBLog.v(CloudService.V2_TAG, "setScreenOffTime, default 5 min");
            SystemApi.setScreenOffTime(service, 300);
        }
	}
	
	public String getMyCallingPackage(){
		return SystemApi.getPackName(service, Binder.getCallingPid());
	}

	public void setTimezone() {
//		android.provider.Settings.Global.putInt(context.getContentResolver(),
//				android.provider.Settings.Global.AUTO_TIME_ZONE, 0); //0为关闭自动设置时区
		String timezone = SharedPreferencesUtils.getSharePreferencesValue(UsualData.SHARED_PREFERENCES_TIMEZONE, "");
		if (!TextUtils.isEmpty(timezone)) {
			BBLog.v(CloudService.V2_TAG, "Find config file, timezone(update): " + timezone);
		} else {
			BBLog.v(CloudService.V2_TAG, "Can't find config file, timezone(default): Asia/Hong_Kong");
		}
		SystemApi.setTimeZone(service, timezone);
	}

	public void setTime() {
		String versionName = BuildConfig.VERSION_NAME;
		BBLog.v(BBLog.TAG, "versionName = " + versionName);
		int year = 2019;
		int month = 8;
		int dayOfMonth = 9;
		if (!TextUtils.isEmpty(versionName)) {
			if (!TextUtils.isEmpty(versionName)) {
				String[] versionFilterStr = versionName.split("\\.");
//				for (int i = 0; i < versionFilterStr.length; i ++) {
//					BBLog.v(BBLog.TAG, "versionFilterStr[" + i + "] = " + versionFilterStr[i]);
//				}
				if (versionFilterStr.length > 4) {
					String versionInfo = "V" + versionFilterStr[0] + "." + versionFilterStr[1] + "." + versionFilterStr[2];
					BBLog.v(BBLog.TAG, "versionInfo: " + versionInfo);
					String dateInfo = versionFilterStr[3];
					BBLog.v(BBLog.TAG, "dateInfo: " + versionInfo);
//					BBLog.v(BBLog.TAG, "isNumeric(dateInfo): " + ByteUtils.isNumeric(dateInfo));
					if (dateInfo.length() == 8 && ByteUtils.isNumeric(dateInfo)) {
						year = Integer.parseInt(dateInfo.substring(0, 4));
						month = Integer.parseInt(dateInfo.substring(4, 6));
						dayOfMonth = Integer.parseInt(dateInfo.substring(6, 8));
//						BBLog.v(BBLog.TAG, "year: " + dateInfo.substring(0, 4));
//						BBLog.v(BBLog.TAG, "date: " + dateInfo.substring(4, 6));
//						BBLog.v(BBLog.TAG, "day: " + dateInfo.substring(6, 8));
					}
				}
			}
		}

		Calendar c = Calendar.getInstance();
		c.set(Calendar.YEAR, year);
		c.set(Calendar.MONTH, month);
		c.set(Calendar.DAY_OF_MONTH, dayOfMonth);
		c.set(Calendar.HOUR_OF_DAY, 0);
		c.set(Calendar.MINUTE, 0);
		c.set(Calendar.SECOND, 0);
		c.set(Calendar.MILLISECOND, 0);

		String timeValid = SharedPreferencesUtils.getSharePreferencesValue(UsualData.SHARED_PREFERENCES_REQUEST_TIME, "");
		if (!TextUtils.isEmpty(timeValid)) {
			BBLog.v(CloudService.V2_TAG, "Find config file, time(update): " + timeValid);
		} else {
			timeValid = String.valueOf(c.getTimeInMillis());
			BBLog.v(CloudService.V2_TAG, "Can't find config file, time(default):" +  timeValid);
		}
		long time = Long.parseLong(timeValid);
		long curTime = System.currentTimeMillis();
		if (time-curTime>60000 || curTime-time>60000) {
			SystemApi.setTime(service, timeValid);
		}
	}
}
