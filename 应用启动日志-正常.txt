---------------------------- PROCESS ENDED (10365) for package com.dspread.mdm.service ----------------------------
2025-08-19 17:00:52.271   958-1061  InputDispatcher         system_server                        E  channel 'ca12329 com.dspread.mdm.service/com.dspread.mdm.service.ui.activity.TestActivity (server)' ~ Channel is unrecoverably broken and will be disposed!
2025-08-19 17:00:53.004   958-958   LoadedApk               system_server                        E  Unable to instantiate appComponentFactory (Ask Gemini)
                                                                                                    java.lang.ClassNotFoundException: Didn't find class "androidx.core.app.CoreComponentFactory" on path: DexPathList[[],nativeLibraryDirectories=[/data/app/com.dspread.mdm.service-mNZ55fIQTA9jC-Z1sbYtlA==/lib/arm, /data/app/com.dspread.mdm.service-mNZ55fIQTA9jC-Z1sbYtlA==/base.apk!/lib/armeabi-v7a, /system/lib, /product/lib]]
                                                                                                         at dalvik.system.BaseDexClassLoader.findClass(BaseDexClassLoader.java:196)
                                                                                                         at java.lang.ClassLoader.loadClass(ClassLoader.java:379)
                                                                                                         at java.lang.ClassLoader.loadClass(ClassLoader.java:312)
                                                                                                         at android.app.LoadedApk.createAppFactory(LoadedApk.java:256)
                                                                                                         at android.app.LoadedApk.updateApplicationInfo(LoadedApk.java:370)
                                                                                                         at android.app.ActivityThread.handleDispatchPackageBroadcast(ActivityThread.java:5979)
                                                                                                         at android.app.ActivityThread$H.handleMessage(ActivityThread.java:1966)
                                                                                                         at android.os.Handler.dispatchMessage(Handler.java:107)
                                                                                                         at android.os.Looper.loop(Looper.java:214)
                                                                                                         at com.android.server.SystemServer.run(SystemServer.java:576)
                                                                                                         at com.android.server.SystemServer.main(SystemServer.java:370)
                                                                                                         at java.lang.reflect.Method.invoke(Native Method)
                                                                                                         at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:492)
                                                                                                         at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:958)
---------------------------- PROCESS STARTED (10499) for package com.dspread.mdm.service ----------------------------
2025-08-19 17:00:53.913 10499-10499 ead.mdm.service         com.dspread.mdm.service              W  type=1400 audit(0.0:248): avc: denied { write } for name="com.dspread.mdm.service-R412g0Tbn6zQpOHk3dObmg==" dev="mmcblk0p36" ino=29212 scontext=u:r:system_app:s0 tcontext=u:object_r:apk_data_file:s0 tclass=dir permissive=0
2025-08-19 17:00:54.968 10499-10499 Common                  com.dspread.mdm.service              D  🔧 日志配置：测试环境模式
2025-08-19 17:00:54.972 10499-10499 Common                  com.dspread.mdm.service              D  🔧 [DebugConfig] 调试模式已启用
2025-08-19 17:00:54.973 10499-10499 Common                  com.dspread.mdm.service              D  🔧 SmartMdmServiceApp: 统一配置管理器初始化完成
2025-08-19 17:00:54.974 10499-10499 Common                  com.dspread.mdm.service              D  🔧 SmartMdmServiceApp: 开始初始化系统目录...
2025-08-19 17:00:54.973 10499-10499 ead.mdm.service         com.dspread.mdm.service              W  type=1400 audit(0.0:249): avc: denied { create } for name="pos" scontext=u:r:system_app:s0 tcontext=u:object_r:system_data_file:s0 tclass=dir permissive=0
2025-08-19 17:00:54.976 10499-10499 Common                  com.dspread.mdm.service              D  🔧 SmartMdmServiceApp: 配置目录创建 - 路径: /data/pos/config, 结果: false
2025-08-19 17:00:54.977 10499-10499 Common                  com.dspread.mdm.service              D  🔧 SmartMdmServiceApp: Logo目录已存在 - /data/data/com.dspread.mdm.service/files/media/logo
2025-08-19 17:00:54.978 10499-10499 Common                  com.dspread.mdm.service              D  🔧 SmartMdmServiceApp: 开机动画目录已存在 - /data/data/com.dspread.mdm.service/files/media/anim
2025-08-19 17:00:54.979 10499-10499 Common                  com.dspread.mdm.service              D  🔧 SmartMdmServiceApp: 系统目录初始化完成
2025-08-19 17:00:54.985 10499-10499 Platform                com.dspread.mdm.service              D  🔧 NetworkTrafficMonitor 初始化网络流量监控器
2025-08-19 17:00:54.992 10499-10499 Platform                com.dspread.mdm.service              D  🔧 NetworkTrafficMonitor 加载已保存的流量数据: 2.0MB
2025-08-19 17:00:55.054 10499-10499 Platform                com.dspread.mdm.service              D  🔧 NetworkTrafficMonitor 每日上送任务已启动
2025-08-19 17:00:55.055 10499-10499 Platform                com.dspread.mdm.service              D  🔧 NetworkTrafficMonitor 网络流量监控器初始化完成
2025-08-19 17:00:55.059 10499-10499 Receiver                com.dspread.mdm.service              D  🔧 ServiceGuardEventHandler 启动服务守护定时器: 120s
2025-08-19 17:00:55.065 10499-10499 Common                  com.dspread.mdm.service              I  ✅ ⏰ 设置服务守护定时器成功，下次执行: 120秒后 (2分钟)
2025-08-19 17:00:55.066 10499-10499 Common                  com.dspread.mdm.service              D  🔧 SmartMdmServiceApp: 服务守护定时器启动成功
2025-08-19 17:00:55.069 10499-10499 Common                  com.dspread.mdm.service              D  🔧 ServiceStartupManager 收到服务启动请求: APPLICATION_STARTUP, 强制启动: false
2025-08-19 17:00:55.073 10499-10499 Common                  com.dspread.mdm.service              D  🔧 ServiceStartupManager 开始执行服务启动: APPLICATION_STARTUP
2025-08-19 17:00:55.075 10499-10499 Common                  com.dspread.mdm.service              D  🔧 ServiceManager: 准备启动后台服务
2025-08-19 17:00:55.076 10499-10499 ContextImpl             com.dspread.mdm.service              W  Calling a method in the system process without a qualified user: android.app.ContextImpl.startService:1571 android.content.ContextWrapper.startService:669 com.dspread.mdm.service.platform.manager.ServiceManager.startBackgroundService:20 com.dspread.mdm.service.platform.manager.ServiceStartupManager.performServiceStart:121 com.dspread.mdm.service.platform.manager.ServiceStartupManager.startService:66 
2025-08-19 17:00:55.079 10499-10499 Common                  com.dspread.mdm.service              D  🔧 ServiceManager: 后台服务启动成功，ComponentName: ComponentInfo{com.dspread.mdm.service/com.dspread.mdm.service.services.SmartMdmBackgroundService}
2025-08-19 17:00:56.083 10499-10499 Common                  com.dspread.mdm.service              D  🔧 ServiceStartupManager 服务启动验证成功: APPLICATION_STARTUP
2025-08-19 17:00:56.084 10499-10499 Common                  com.dspread.mdm.service              D  🔧 ServiceStartupManager 服务启动成功: APPLICATION_STARTUP
2025-08-19 17:00:56.085 10499-10499 Common                  com.dspread.mdm.service              D  🔧 SmartMdmServiceApp: 主服务启动请求已提交
2025-08-19 17:00:56.085 10499-10499 Common                  com.dspread.mdm.service              D  🔧 SmartMdmServiceApp: Application启动完成，包名: com.dspread.mdm.service
2025-08-19 17:00:56.206 10499-10499 SurfaceFactory          com.dspread.mdm.service              I  [static] sSurfaceFactory = com.mediatek.view.impl.SurfaceFactoryImpl@edbbe93
2025-08-19 17:00:56.217 10499-10499 ViewRootIm...tActivity] com.dspread.mdm.service              D  hardware acceleration = true , fakeHwAccelerated = false, sRendererDisabled = false, forceHwAccelerated = false, sSystemRendererDisabled = false
2025-08-19 17:00:56.224 10499-10499 InputTransport          com.dspread.mdm.service              I  Create ARC handle: 0x94706510
2025-08-19 17:00:56.225 10499-10499 PhoneWindow             com.dspread.mdm.service              V  DecorView setVisiblity: visibility = 0, Parent = android.view.ViewRootImpl@7f9b0ef, this = DecorView@11357fc[TestActivity]
2025-08-19 17:00:56.226 10499-10499 Choreographer           com.dspread.mdm.service              I  Skipped 79 frames!  The application may be doing too much work on its main thread.
2025-08-19 17:00:56.275 10499-10540 GPUD                    com.dspread.mdm.service              I  @gpudInitialize: successfully initialized with GL, dbg=0 mmdump_dbg=0 mmpath_dbg=0
2025-08-19 17:00:56.281 10499-10540 GED                     com.dspread.mdm.service              E  Failed to get GED Log Buf, err(0)
2025-08-19 17:00:56.302 10499-10540 Surface                 com.dspread.mdm.service              D  Surface::connect(this=0x80fba000,api=1)
2025-08-19 17:00:56.304 10499-10540 Surface                 com.dspread.mdm.service              D  Surface::setBufferCount(this=0x80fba000,bufferCount=3)
2025-08-19 17:00:56.304 10499-10540 Surface                 com.dspread.mdm.service              D  Surface::allocateBuffers(this=0x80fba000)
2025-08-19 17:00:56.315 10499-10540 Gralloc3                com.dspread.mdm.service              W  mapper 3.x is not supported
2025-08-19 17:00:56.318 10499-10540 ion                     com.dspread.mdm.service              E  ioctl c0044901 failed with code -1: Invalid argument
2025-08-19 17:00:56.452 10499-10540 OpenGLRenderer          com.dspread.mdm.service              I  Davey! duration=1539ms; Flags=1, IntendedVsync=28551495854525, Vsync=28552812521218, OldestInputEvent=9223372036854775807, NewestInputEvent=0, HandleInputStart=28552817220343, AnimationStart=28552817308959, PerformTraversalsStart=28552817734959, DrawStart=28552895096351, SyncQueued=28552902747121, SyncStart=28552909189968, IssueDrawCommandsStart=28552909302276, SwapBuffers=28553040608212, FrameCompleted=28553042122827, DequeueBufferDuration=0, QueueBufferDuration=860000, 
2025-08-19 17:00:56.465 10499-10499 Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 服务启动
2025-08-19 17:00:56.482   958-981   NotificationService     system_server                        E  enqueueNotificationInternal pkg: com.dspread.mdm.service
2025-08-19 17:00:56.482 10499-10499 Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 开始服务初始化
2025-08-19 17:00:56.483 10499-10499 Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] Android < 14: 启动Provisioning服务
2025-08-19 17:00:56.485 10499-10499 Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 服务创建完成
2025-08-19 17:00:56.486 10499-10547 Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 启动ProvisioningService
2025-08-19 17:00:56.487 10499-10499 Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] onStartCommand调用
2025-08-19 17:00:56.493 10499-10499 Platform                com.dspread.mdm.service              D  🔧 WakeLockManager 获取智能WakeLock成功，持续时间: 60000ms
2025-08-19 17:00:56.497 10499-10499 Platform                com.dspread.mdm.service              D  🔧 WakeLockManager 设置WakeLock续期定时器: 60000ms
2025-08-19 17:00:56.496 10499-10499 pool-1-thread-1         com.dspread.mdm.service              W  type=1400 audit(0.0:251): avc: denied { create } for name="pos" scontext=u:r:system_app:s0 tcontext=u:object_r:system_data_file:s0 tclass=dir permissive=0
2025-08-19 17:00:56.498 10499-10547 Provisioning            com.dspread.mdm.service              W  ⚠️ 主目录 创建失败: /data/pos/config
2025-08-19 17:00:56.500 10499-10547 Provisioning            com.dspread.mdm.service              D  🔧 回退目录 已存在: /sdcard/Android/data/com.dspread.mdm.service/files/config
2025-08-19 17:00:56.501 10499-10547 Provisioning            com.dspread.mdm.service              W  ⚠️ 主目录不可用，使用回退配置目录: /sdcard/Android/data/com.dspread.mdm.service/files/config/
2025-08-19 17:00:56.502 10499-10547 Provisioning            com.dspread.mdm.service              I  ℹ️ 配置路径初始化完成: 外部存储目录
2025-08-19 17:00:56.512 10499-10547 Provisioning            com.dspread.mdm.service              D  🔧 状态标志加载成功: /sdcard/Android/data/com.dspread.mdm.service/files/config/provisioning_flags.json
2025-08-19 17:00:56.514 10499-10499 Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 初始化基础组件
2025-08-19 17:00:56.516 10499-10499 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 开始初始化...
2025-08-19 17:00:56.516 10499-10547 Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] ProvisioningService启动完成
2025-08-19 17:00:56.521 10499-10538 Provisioning            com.dspread.mdm.service              I  ℹ️ 开始执行Provisioning配置，触发类型: FIRST_BOOT
2025-08-19 17:00:56.525 10499-10538 Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] Provisioning进度: 正在下载配置文件...
2025-08-19 17:00:56.527 10499-10538 Provisioning            com.dspread.mdm.service              I  ℹ️ 开始请求远程配置...
2025-08-19 17:00:56.527 10499-10499 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 包更新广播接收器注册完成
2025-08-19 17:00:56.528 10499-10499 Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 统一广播接收器注册完成
2025-08-19 17:00:56.529 10499-10499 Common                  com.dspread.mdm.service              I  ✅ BroadcastManager 初始化完成
2025-08-19 17:00:56.546 10499-10538 Provisioning            com.dspread.mdm.service              I  ℹ️ 请求配置: https://config.dspreadserv.net/status/config?SN=MDEyNDMwMzAyMDI0MTIwOTAxMDE=&timestamp=1755594056546
2025-08-19 17:00:56.565 10499-10538 NetworkSecurityConfig   com.dspread.mdm.service              D  Using Network Security Config from resource network_security_config debugBuild: true
2025-08-19 17:00:56.574 10499-10538 System.out              com.dspread.mdm.service              I  [okhttp]:check permission begin!
2025-08-19 17:00:56.574 10499-10538 System                  com.dspread.mdm.service              W  ClassLoader referenced unknown path: system/framework/mediatek-cta.jar
2025-08-19 17:00:56.577 10499-10538 System.out              com.dspread.mdm.service              I  [okhttp] e:java.lang.ClassNotFoundException: com.mediatek.cta.CtaUtils
2025-08-19 17:00:56.586 10499-10538 System.out              com.dspread.mdm.service              I  [socket]:check permission begin!
2025-08-19 17:00:56.587 10499-10538 System                  com.dspread.mdm.service              W  ClassLoader referenced unknown path: system/framework/mediatek-cta.jar
2025-08-19 17:00:56.589 10499-10538 System.out              com.dspread.mdm.service              I  [socket] e:java.lang.ClassNotFoundException: com.mediatek.cta.CtaUtils
2025-08-19 17:00:56.595 10499-10499 Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 服务初始化完成
2025-08-19 17:00:56.602 10499-10499 Platform                com.dspread.mdm.service              D  🔧 NetworkApi 单例实例已创建
2025-08-19 17:00:56.606 10499-10499 Receiver                com.dspread.mdm.service              D  🔧 网络状态变化: 连接=true, 类型=WIFI
2025-08-19 17:00:56.615 10499-10499 Receiver                com.dspread.mdm.service              D  🔧 网络恢复，尝试重新连接WebSocket
2025-08-19 17:00:56.616 10499-10499 Receiver                com.dspread.mdm.service              D  🔧 ServiceManagementEventHandler 处理网络变化事件
2025-08-19 17:00:56.617 10499-10536 Platform                com.dspread.mdm.service              D  🔧 DeviceInfoApi 单例实例已创建
2025-08-19 17:00:56.619 10499-10499 Common                  com.dspread.mdm.service              D  🔧 ServiceStartupManager 服务正在运行，无需启动: NETWORK_CHANGE
2025-08-19 17:00:56.620 10499-10499 Receiver                com.dspread.mdm.service              D  🔧 ServiceManagementEventHandler 网络变化后服务检查完成
2025-08-19 17:00:56.622 10499-10499 Provisioning            com.dspread.mdm.service              D  🔧 ProvisioningEventHandler 网络状态变化
2025-08-19 17:00:56.623 10499-10499 Provisioning            com.dspread.mdm.service              D  🔧 ProvisioningEventHandler 忽略初始启动时的网络连接事件
2025-08-19 17:00:56.626 10499-10499 Receiver                com.dspread.mdm.service              D  🔧 电池状态更新: 电池状态: 电量=37%, 温度=32.1°C, 健康=2, 充电=是, 低电量=否, 耗尽=否
2025-08-19 17:00:56.627 10499-10499 Receiver                com.dspread.mdm.service              D  🔧 电池状态变化: 电量=37%, 温度=32°C, 充电=true
2025-08-19 17:00:56.643 10499-10536 Platform                com.dspread.mdm.service              W  ⚠️ SpVersionApi SP版本信息格式异常，未找到SP_VERSION标记
2025-08-19 17:00:56.656 10499-10536 Platform                com.dspread.mdm.service              D  🔧 SpVersionApi 从Settings获取SP版本: 588.30.241.59
2025-08-19 17:00:56.660 10499-10499 WebSocket               com.dspread.mdm.service              I  🔧 C0902 被动式上送: battery_level_change_5 (被动: 1)
2025-08-19 17:00:56.681 10499-10499 WebSocket               com.dspread.mdm.service              I  🔧 消息已缓存: C0902 (缓存数量: 1)
2025-08-19 17:00:56.682 10499-10499 WebSocket               com.dspread.mdm.service              I  🔧 C0902 电池状态上送成功 (1, trigger=battery_level_change_5)
2025-08-19 17:00:56.729 10499-10499 WebSocket               com.dspread.mdm.service              I  🔧 C0905 主动式上送: manual_trigger (主动: 1)
2025-08-19 17:00:56.734 10499-10499 WebSocket               com.dspread.mdm.service              I  🔧 隔天上送，上送昨日数据
2025-08-19 17:00:56.736 10499-10499 WebSocket               com.dspread.mdm.service              I  🔧 没有流量数据需要上送
2025-08-19 17:00:56.737 10499-10499 Common                  com.dspread.mdm.service              D  🔧 刷新时自动上送流量统计
2025-08-19 17:00:57.484 10499-10538 System.out              com.dspread.mdm.service              I  [OkHttp] sendRequest>>
2025-08-19 17:00:57.485 10499-10538 System.out              com.dspread.mdm.service              I  [OkHttp] sendRequest<<
2025-08-19 17:00:58.256 10499-10538 Provisioning            com.dspread.mdm.service              I  ℹ️ 远程配置获取成功
2025-08-19 17:00:58.257 10499-10538 Provisioning            com.dspread.mdm.service              I  ℹ️ 远程配置请求成功
2025-08-19 17:00:58.259 10499-10538 Provisioning            com.dspread.mdm.service              I  ℹ️ 远程配置解析成功 - CID: 10012001
2025-08-19 17:00:58.264 10499-10538 Provisioning            com.dspread.mdm.service              I  ℹ️ API响应已保存到本地: /sdcard/Android/data/com.dspread.mdm.service/files/config/provisioning_config.json, 具体内容为:
                                                                                                    {"mode":"dev","data":{"requestTime":"1755594058024","customization":{"system":{"timezone":"Asia\/Hong_Kong","logo":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/test\/logo.bin","logoMd5":"ba1ee533924eae5c408465e7cddcbda4","bootAnimationMd5":"ba1ee533924eae5c408465e7cddcbda4","gps":{"minUpdateTime":"30","scheduleTime":"60","maxLocateTime":"0","minDistance":"10","valid_distance":"500","care":"1"},"powerSaveMode":{"enable":"1","screenTimeout":"0"},"bootAnimation":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/test\/bootanimation.zip"},"polling":{"heartbeatTime":"300","terminalInfoTime":"900","statusApiUrl":"wss:\/\/api.dspreadserv.net\/status\/websocket\/register","remoteUrl":"wss:\/\/remote.dspreadserv.net\/remoteWSS\/websockify","uploadMode":"1","wssreconn":{"delayPolicy":"1","delaySwitch":"1","delayTime":"60"}}},"client":"default","cid":"10012001"},"function":"getProvisioningConfig","description":"SUCCESS","stateCode":"0","version":"v1.0.1.20250801"}
2025-08-19 17:00:58.266 10499-10538 Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] Provisioning进度: 正在下载 Logo...
2025-08-19 17:00:58.322 10499-10538 Provisioning            com.dspread.mdm.service              I  ℹ️ 文件已存在且MD5匹配，跳过下载: Logo
2025-08-19 17:00:58.323 10499-10538 Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] Provisioning进度: 正在下载 BootAnimation...
2025-08-19 17:00:58.376 10499-10538 Provisioning            com.dspread.mdm.service              I  ℹ️ 文件已存在且MD5匹配，跳过下载: BootAnimation
2025-08-19 17:00:58.377 10499-10538 Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] Provisioning进度: 正在应用配置...
2025-08-19 17:00:58.378 10499-10538 Provisioning            com.dspread.mdm.service              I  ℹ️ 配置应用完成
2025-08-19 17:00:58.378 10499-10538 Provisioning            com.dspread.mdm.service              I  ℹ️ 远程配置成功，标记首次配置完成
2025-08-19 17:00:58.380 10499-10538 Provisioning            com.dspread.mdm.service              D  🔧 状态标志保存成功: /sdcard/Android/data/com.dspread.mdm.service/files/config/provisioning_flags.json
2025-08-19 17:00:58.381 10499-10538 Provisioning            com.dspread.mdm.service              I  ℹ️ Provisioning配置执行完成
2025-08-19 17:00:58.382 10499-10538 Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] Provisioning完成，启动核心组件
2025-08-19 17:00:58.391 10499-10499 WebSocket               com.dspread.mdm.service              I  🔧 开始初始化 WebSocket 组件...
2025-08-19 17:00:58.392 10499-10499 WebSocket               com.dspread.mdm.service              I  🔧 初始化 WebSocket 密钥管理器
2025-08-19 17:00:58.393 10499-10499 WebSocket               com.dspread.mdm.service              I  🔧 生成客户端公私钥对
2025-08-19 17:00:58.543 10499-10499 Common                  com.dspread.mdm.service              I  ✅ WebSocket 密钥管理器初始化完成
2025-08-19 17:00:58.550 10499-10499 WebSocket               com.dspread.mdm.service              I  🔧 开始初始化 WsMessageCenter...
2025-08-19 17:00:58.551 10499-10499 WebSocket               com.dspread.mdm.service              I  🔧 WsMessageCenter 首次初始化
2025-08-19 17:00:58.567 10499-10499 Task                    com.dspread.mdm.service              D  🔧 从存储加载任务: 0 个
2025-08-19 17:00:58.569 10499-10499 WebSocket               com.dspread.mdm.service              I  🔧 WsTaskManager 初始化完成
2025-08-19 17:00:58.674 10499-10499 WebSocket               com.dspread.mdm.service              I  🔧 WsMessageCenter 初始化完成
2025-08-19 17:00:58.675 10499-10499 WebSocket               com.dspread.mdm.service              I  🔧 开始清理终态任务，当前任务数量: 0
2025-08-19 17:00:58.676 10499-10499 WebSocket               com.dspread.mdm.service              I  🔧 没有需要清理的终态任务
2025-08-19 17:00:58.677 10499-10499 WebSocket               com.dspread.mdm.service              I  🔧 开始重置任务状态，当前任务个数: 0
2025-08-19 17:00:58.678 10499-10499 WebSocket               com.dspread.mdm.service              I  🔧 没有需要重置的任务
2025-08-19 17:00:58.679 10499-10499 Common                  com.dspread.mdm.service              I  ✅ 任务状态恢复完成
2025-08-19 17:00:58.685 10499-10499 WebSocket               com.dspread.mdm.service              I  🔧 使用Provisioning配置的WebSocket URL: wss://api.dspreadserv.net/status/websocket/register
2025-08-19 17:00:58.687 10499-10499 WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接管理器初始化完成
2025-08-19 17:00:58.688 10499-10499 Common                  com.dspread.mdm.service              I  ✅ WebSocket 管理器初始化完成
2025-08-19 17:00:58.688 10499-10499 WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 管理器初始化完成
2025-08-19 17:00:58.690 10499-10499 Common                  com.dspread.mdm.service              I  ✅ WebSocket 组件初始化完成
2025-08-19 17:00:58.691 10499-10499 WebSocket               com.dspread.mdm.service              I  🔧 启动 WebSocket 连接...
2025-08-19 17:00:58.692 10499-10499 WebSocket               com.dspread.mdm.service              I  🔧 开始连接 WebSocket...
2025-08-19 17:00:58.704 10499-10499 WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接到: wss://api.dspreadserv.net/status/websocket/register?serialNo=MDEyNDMwMzAyMDI0MTIwOTAxMDE=&publicKey=TUlHZk1BMEdDU3FHU0liM0RRRUJBUVVBQTRHTkFEQ0JpUUtCZ1FDdyszWmNXVldwVDk5eWdKL1p0U09KLzVsamlHWENKcmR4SVlUVXpMd3RyZzhiZ2psNVZXTVFVWXFianM4M2NXYjVOQi9rSWhsVVQvNVBYcmVxcnA4dE9JVFhqYU5GTDVlQlRyU3BiRWFxbk4zMVJpaHMxd2NSRHl2cGNXcXhmaW1XbWFuT29vams3R3NCOHZiNkJBeFhTVzg3S0pwWWRkV3cxMDF6d09CYlZ3SURBUUFC&query=1&msgVer=3&timestamp=1755594058694&signature=GbGJBJI0UjLa0WwzqTKHD5DIB/AXydqlhwnpsxuG6iI6MQ3ya1mvMnyrHMMtsrZQ1rParPPxlfmUK8FHSiXN8FUf+VfeEHxRvbx+DFDYC770r0WxJJ/Xeau1/05D7am3gwc6A/016+iuDxk//XebaiEgSeF3g12QFjgjUNfB8ek=
2025-08-19 17:00:58.708 10499-10499 WebSocket               com.dspread.mdm.service              I  🔧 未检测到系统代理设置
2025-08-19 17:00:58.728 10499-10499 WebSocket               com.dspread.mdm.service              I  🔧 ===== WebSocket 库默认配置 =====
2025-08-19 17:00:58.730 10499-10499 WebSocket               com.dspread.mdm.service              I  🔧 默认 PING 间隔: 30000ms (30秒)
2025-08-19 17:00:58.730 10499-10499 WebSocket               com.dspread.mdm.service              I  🔧 默认 PONG 间隔: 0ms (0秒)
2025-08-19 17:00:58.731 10499-10499 WebSocket               com.dspread.mdm.service              I  🔧 帧队列大小: 5
2025-08-19 17:00:58.732 10499-10499 WebSocket               com.dspread.mdm.service              I  🔧 连接超时: 15000ms (15秒)
2025-08-19 17:00:58.733 10499-10499 WebSocket               com.dspread.mdm.service              I  🔧 允许缺失关闭帧: false
2025-08-19 17:00:58.734 10499-10499 WebSocket               com.dspread.mdm.service              I  🔧 ================================
2025-08-19 17:00:58.736 10499-10499 WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接请求已发送
2025-08-19 17:00:58.737 10499-10499 WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接启动完成
2025-08-19 17:00:58.741 10499-10499 Common                  com.dspread.mdm.service              D  🔧 [ModuleManagerRegistry] 模块管理器注册中心初始化完成
2025-08-19 17:00:58.754 10499-10499 RuleBase                com.dspread.mdm.service              D  🔧 RuleBaseManager 开始初始化RuleBaseManager...
2025-08-19 17:00:58.760 10499-10499 RuleBase                com.dspread.mdm.service              D  🔧 RuleBaseStorage RuleBase存储管理器初始化完成
2025-08-19 17:00:58.761 10499-10499 RuleBase                com.dspread.mdm.service              D  🔧 RuleStateMonitor 状态监控器初始化完成
2025-08-19 17:00:58.762 10499-10499 RuleBase                com.dspread.mdm.service              D  🔧 RuleBaseManager RuleBaseManager初始化完成
2025-08-19 17:00:58.762 10499-10499 Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] RuleBaseManager初始化成功
2025-08-19 17:00:58.764 10499-10499 Provisioning            com.dspread.mdm.service              D  🔧 ProvisioningEventHandler 启动Provisioning定时器: 300s (0小时)
2025-08-19 17:00:58.765 10499-10538 Common                  com.dspread.mdm.service              D  🔧 [ModuleManagerRegistry] 启动所有模块
2025-08-19 17:00:58.769 10499-10499 Common                  com.dspread.mdm.service              I  ✅ ⏰ 设置初始化定时器成功，下次执行: 300秒后 (0小时)
2025-08-19 17:00:58.773 10499-10499 Common                  com.dspread.mdm.service              I  ✅ ⏰ 设置心跳定时器成功，下次执行: 60秒后 (1分钟)
2025-08-19 17:00:58.774 10499-10538 Common                  com.dspread.mdm.service              D  🔧 [ModuleManagerRegistry] LogStreamManager实例创建完成
2025-08-19 17:00:58.775 10499-10538 Common                  com.dspread.mdm.service              D  🔧 Module LogStream Manager status changed to: INITIALIZED
2025-08-19 17:00:58.778 10499-10499 Common                  com.dspread.mdm.service              I  ✅ ⏰ 设置任务执行定时器成功，下次执行: 60秒后 (1分钟)
2025-08-19 17:00:58.782 10499-10499 Common                  com.dspread.mdm.service              I  ✅ ⏰ 设置终端信息定时器成功，下次执行: 120秒后 (2分钟)
2025-08-19 17:00:58.782 10499-10538 Common                  com.dspread.mdm.service              D  🔧 [ModuleManagerRegistry] LogStreamManager初始化成功
2025-08-19 17:00:58.783 10499-10499 Common                  com.dspread.mdm.service              D  🔧 TimerConfig ========== 当前定时器配置 (调试模式) ==========
2025-08-19 17:00:58.784 10499-10538 Common                  com.dspread.mdm.service              D  🔧 Module LogStream Manager status changed to: STARTING
2025-08-19 17:00:58.784 10499-10499 Common                  com.dspread.mdm.service              D  🔧 TimerConfig ①心跳定时器: 60秒
2025-08-19 17:00:58.785 10499-10499 Common                  com.dspread.mdm.service              D  🔧 TimerConfig ②终端信息上传: 120秒
2025-08-19 17:00:58.786 10499-10499 Common                  com.dspread.mdm.service              D  🔧 TimerConfig ③任务执行: 60秒
2025-08-19 17:00:58.787 10499-10499 Common                  com.dspread.mdm.service              D  🔧 TimerConfig ④服务守护: 120秒
2025-08-19 17:00:58.788 10499-10499 Common                  com.dspread.mdm.service              D  🔧 TimerConfig ⑤Provisioning: 300秒
2025-08-19 17:00:58.788 10499-10499 Common                  com.dspread.mdm.service              D  🔧 TimerConfig ===============================================
2025-08-19 17:00:58.789 10499-10499 Common                  com.dspread.mdm.service              I  ✅ [SmartMdmBackgroundService] 所有定时器启动完成
2025-08-19 17:00:58.790 10499-10499 Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 核心组件初始化完成
2025-08-19 17:00:58.835 10499-10567 System.out              com.dspread.mdm.service              I  [socket]:check permission begin!
2025-08-19 17:00:58.852 10499-10538 Common                  com.dspread.mdm.service              D  🔧 Module LogStream Manager status changed to: RUNNING
2025-08-19 17:00:58.854 10499-10538 Common                  com.dspread.mdm.service              D  🔧 [ModuleManagerRegistry] LogStreamManager启动成功
2025-08-19 17:00:58.854 10499-10538 Common                  com.dspread.mdm.service              D  🔧 [ModuleManagerRegistry] LogStreamManager启动成功
2025-08-19 17:00:58.855 10499-10538 Common                  com.dspread.mdm.service              D  🔧 [ModuleManagerRegistry] 开始初始化地理围栏管理器
2025-08-19 17:00:58.878 10499-10538 Common                  com.dspread.mdm.service              D  🔧 Module Geofence Manager status changed to: INITIALIZED
2025-08-19 17:00:58.892 10499-10538 Common                  com.dspread.mdm.service              D  🔧 Module Geofence Manager status changed to: STARTING
2025-08-19 17:00:58.911 10499-10538 Platform                com.dspread.mdm.service              D  🔧 用户交互监控已启动（简化版本）
2025-08-19 17:00:58.911 10499-10499 Platform                com.dspread.mdm.service              D  🔧 [WiFiManagerApi] WiFiManagerApi 单例实例已创建
2025-08-19 17:00:58.911 10499-10538 Platform                com.dspread.mdm.service              D  🔧 使用模拟用户交互检测
2025-08-19 17:00:58.912 10499-10538 Platform                com.dspread.mdm.service              D  🔧 用户交互监控已启动
2025-08-19 17:00:58.913 10499-10538 Service                 com.dspread.mdm.service              D  🔧 [GeofenceManager]Constants.geofenceStatus = 1
2025-08-19 17:00:58.915 10499-10538 Service                 com.dspread.mdm.service              D  🔧 [GeofenceManager]-- Constants.storeId =  -- Constants.storeSsid =  -- Constants.storeIp = 
2025-08-19 17:00:58.916 10499-10538 Common                  com.dspread.mdm.service              D  🔧 Module Geofence Manager status changed to: RUNNING
2025-08-19 17:00:58.918 10499-10538 Common                  com.dspread.mdm.service              D  🔧 [ModuleManagerRegistry] 地理围栏管理器初始化成功
2025-08-19 17:00:58.918 10499-10538 Common                  com.dspread.mdm.service              D  🔧 [ModuleManagerRegistry] GeofenceManager启动成功
2025-08-19 17:00:58.919 10499-10538 Common                  com.dspread.mdm.service              D  🔧 [ModuleManagerRegistry] 所有模块启动完成
2025-08-19 17:00:59.085 10499-10568 System.out              com.dspread.mdm.service              I  [socket]:check permission begin!
2025-08-19 17:01:00.070 10499-10538 Platform                com.dspread.mdm.service              D  🔧 NetworkTrafficMonitor 等待下次上送时间: 2025-08-20 00:00:00
2025-08-19 17:01:00.410 10499-10573 Common                  com.dspread.mdm.service              I  ✅ WebSocket 物理连接成功，等待服务器 hellMsg
2025-08-19 17:01:00.411 10499-10573 WebSocket               com.dspread.mdm.service              I  🔧 ===== 心跳发送器实际配置 =====
2025-08-19 17:01:00.412 10499-10573 WebSocket               com.dspread.mdm.service              I  🔧 实际 PING 发送间隔: 30000ms (30秒)
2025-08-19 17:01:00.413 10499-10573 WebSocket               com.dspread.mdm.service              I  🔧 PING 定时器名称: PingSender
2025-08-19 17:01:00.413 10499-10573 WebSocket               com.dspread.mdm.service              I  🔧 实际 PONG 发送间隔: 0ms (0秒)
2025-08-19 17:01:00.414 10499-10573 WebSocket               com.dspread.mdm.service              I  🔧 PONG 定时器名称: PongSender
2025-08-19 17:01:00.415 10499-10573 WebSocket               com.dspread.mdm.service              I  🔧 ===============================
2025-08-19 17:01:00.433 10499-10573 Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"hellMsg":"hello 01243030202412090101","tranCode":"S0000","version":"1","rebootTime":"01:44:10","serialNo":"01243030202412090101","deviceStatus":"6"}
2025-08-19 17:01:00.434 10499-10573 Common                  com.dspread.mdm.service              I  ✅ 收到服务器 hellMsg，WebSocket 业务连接成功！
2025-08-19 17:01:00.435 10499-10573 Common                  com.dspread.mdm.service              I  ✅ WebSocket 业务连接成功！
2025-08-19 17:01:00.436 10499-10573 WebSocket               com.dspread.mdm.service              I  🔧 业务连接成功，重连计数已重置为 0
2025-08-19 17:01:00.437 10499-10573 WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接成功，检查自身更新任务状态
2025-08-19 17:01:00.439 10499-10573 Receiver                com.dspread.mdm.service              D  🔧 发送广播: com.dspread.mdm.service.CHECK_SELF_UPDATE_STATUS
2025-08-19 17:01:00.440 10499-10573 WebSocket               com.dspread.mdm.service              I  🔧 开始发送缓存消息，数量: 1
2025-08-19 17:01:00.442 10499-10573 WebSocket               com.dspread.mdm.service              I  🔧 C0902 主动式上送: cached_message_resend (主动: 2)
2025-08-19 17:01:00.448 10499-10573 Common                  com.dspread.mdm.service              I  ✅ 缓存消息发送成功: C0902
2025-08-19 17:01:00.949 10499-10573 WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接成功
2025-08-19 17:01:00.950 10499-10573 WebSocket               com.dspread.mdm.service              I  🔧 设备状态: 6
2025-08-19 17:01:00.951 10499-10573 WebSocket               com.dspread.mdm.service              I  🔧 检查首次连接标志: isFirstTimeWebSocketConnected=true
2025-08-19 17:01:00.952 10499-10573 WebSocket               com.dspread.mdm.service              I  🔧 首次WebSocket连接成功，发送初始化信息并执行初始化检查
2025-08-19 17:01:00.952 10499-10573 WebSocket               com.dspread.mdm.service              I  🔧 开始发送初始化信息（带流量控制）
2025-08-19 17:01:00.953 10499-10573 WebSocket               com.dspread.mdm.service              I  🔧 C0901 被动式上送: first_connection (被动: 2)
2025-08-19 17:01:00.957 10499-10573 Platform                com.dspread.mdm.service              D  🔧 重新收集应用信息
2025-08-19 17:01:01.017 10499-10573 AppManager              com.dspread.mdm.service              I  ℹ️ 应用信息获取完成: 总数100(系统95/用户5) 返回5个
2025-08-19 17:01:01.026 10499-10573 Platform                com.dspread.mdm.service              D  🔧 应用信息: 5 个应用
2025-08-19 17:01:01.035 10499-10573 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0901","serialNo":"01243030202412090101","request_time":"1755594061027","request_id":"1755594061027C0901","version":"1","data":{"apkInfo":[{"packName":"de.blinkt.openvpn","apkName":"OpenVPN for Android","versionCode":65,"versionName":"0.5.36a","updateDate":"2025-08-18 16:23:34"},{"packName":"com.android.dreams.basic","apkName":"Basic Daydreams","versionCode":29,"versionName":"10","updateDate":"2009-01-01 08:00:00"},{"packName":"com.dspread.mdm.service","apkName":"service","versionCode":21,"versionName":"1.1.02.20250819.DSPREAD.MDM.SERVICE","updateDate":"2025-08-19 17:00:52"},{"packName":"mark.via","apkName":"Via","versionCode":20250117,"versionName":"6.2.0","updateDate":"2025-08-18 17:50:26"},{"packName":"com.dspread.new","apkName":"Smart-pos-demo","versionCode":3687,"versionName":"3.6.87","updateDate":"2009-01-01 08:00:00"}],"sytemInfo":{"androidVersion":"10","buildNumber":"D20Pro_Baseline_debug_20250708_ota_test","aspl":"2021-07-05"},"serviceInfo":[]},"myVersionName":"1.1.02.20250819.DSPREAD.MDM.SERVICE","terminalDate":"20250819170101"}
2025-08-19 17:01:01.036 10499-10573 WebSocket               com.dspread.mdm.service              I  🔧 C0901 应用信息上送成功 (1)
2025-08-19 17:01:01.952 10499-10576 ProfileInstaller        com.dspread.mdm.service              D  Installing profile for com.dspread.mdm.service
2025-08-19 17:01:02.037 10499-10573 WebSocket               com.dspread.mdm.service              I  🔧 C0902 被动式上送: first_connection (被动: 3)
2025-08-19 17:01:02.048 10499-10573 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0902","serialNo":"01243030202412090101","request_time":"1755594062041","request_id":"1755594062041C0902","version":"1","data":{"batteryLife":37,"batteryHealth":2,"temprature":"32.1","isCharging":"1","isLowBattery":"0","powerOff":"0","outOfBattery":"0"},"myVersionName":"1.1.02.20250819.DSPREAD.MDM.SERVICE","terminalDate":"20250819170102"}
2025-08-19 17:01:02.048 10499-10573 WebSocket               com.dspread.mdm.service              I  🔧 C0902 电池状态上送成功 (1, trigger=first_connection)
2025-08-19 17:01:03.050 10499-10573 WebSocket               com.dspread.mdm.service              I  🔧 C0903 被动式上送: first_connection (被动: 4)
2025-08-19 17:01:03.077 10499-10573 Platform                com.dspread.mdm.service              D  🔧 内存总容量为： 1.87GB 
2025-08-19 17:01:03.078 10499-10573 Platform                com.dspread.mdm.service              D  🔧 内存使用量为： 0.42GB 
2025-08-19 17:01:03.145 10499-10573 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0903","serialNo":"01243030202412090101","request_time":"1755594063135","request_id":"1755594063135C0903","version":"1","data":{"hardwareInfo":[{"name":"CPU Usage","desc":"cpu:0.50"},{"name":"storage_usage_status","desc":" 4.81GB \/ 8.00GB ","state":"1"},{"name":"total_memory","desc":" 0.42GB \/ 1.87GB ","state":"1"},{"name":"androidVersion","desc":"10","state":"1"},{"name":"network_status","desc":"wifi","state":"1"},{"name":"OSVer","desc":"10","state":"1"}],"locationInfo":{"gps":{"longitude":"-999.0","latitude":"-999.0","satelliteCount":0,"satelliteCountInUsed":0,"satelliteAverSNR":"0.00","distance":"0.0","lockStatus":"1","lockMeter":"200","proId":"","proName":""},"baseSite":{"MCC":"","MNC":"","CID":"-1","LAC":"-1","DBM":"-999","IMSI":"","ICCID":""},"wifi":{"SSID":"fubox_2.4G","IP":"*************","MAC":"D6:C4:A7:CA:EB:20","SSTH":"-32","BSSID":"7c:10:c9:d6:9f:10"},"address":"无法获取"}},"myVersionName":"1.1.02.20250819.DSPREAD.MDM.SERVICE","terminalDate":"20250819170103"}
2025-08-19 17:01:03.146 10499-10573 WebSocket               com.dspread.mdm.service              I  🔧 C0903 数据信息上送成功 (1)
2025-08-19 17:01:04.148 10499-10573 WebSocket               com.dspread.mdm.service              I  🔧 C0904 被动式上送: first_connection (被动: 5)
2025-08-19 17:01:04.207 10499-10514 System                  com.dspread.mdm.service              W  A resource failed to call close. 
2025-08-19 17:01:04.226 10499-10573 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0904","serialNo":"01243030202412090101","request_time":"1755594064217","request_id":"1755594064217C0904","version":"1","data":{"wifiOption":[{"SSID":"2206","SSTH":"-29"},{"SSID":"fubox_2.4G","SSTH":"-32"},{"SSID":"2206-5G","SSTH":"-41"},{"SSID":"@Ruijie-1816","SSTH":"-51"},{"SSID":"DIRECT-C6-Mi All-in-One Inkjet","SSTH":"-58"},{"SSID":"2207","SSTH":"-68"},{"SSID":"@Ruijie-1816_5G","SSTH":"-71"},{"SSID":"DIRECT-CC-HP Laser 1188w","SSTH":"-75"},{"SSID":"2306","SSTH":"-75"},{"SSID":"dingjie888","SSTH":"-78"},{"SSID":"CMCC-Cc5b","SSTH":"-78"},{"SSID":"2205_5G","SSTH":"-85"}],"baseSite":{"MCC":"","MNC":"","CID":"-1","LAC":"-1","DBM":"-999","IMSI":"","ICCID":""},"wifi":{"SSID":"fubox_2.4G","IP":"*************","MAC":"D6:C4:A7:CA:EB:20","SSTH":"-32","BSSID":"7c:10:c9:d6:9f:10"}},"myVersionName":"1.1.02.20250819.DSPREAD.MDM.SERVICE","terminalDate":"20250819170104"}
2025-08-19 17:01:04.227 10499-10573 WebSocket               com.dspread.mdm.service              I  🔧 C0904 网络状态上送成功 (1)
2025-08-19 17:01:05.229 10499-10573 WebSocket               com.dspread.mdm.service              I  🔧 C0906 被动式上送: first_connection (被动: 6)
2025-08-19 17:01:05.234 10499-10573 Platform                com.dspread.mdm.service              W  ⚠️ SpVersionApi SP版本信息格式异常，未找到SP_VERSION标记
2025-08-19 17:01:05.235 10499-10573 Platform                com.dspread.mdm.service              D  🔧 SpVersionApi 从Settings获取SP版本: 588.30.241.59
2025-08-19 17:01:05.250 10499-10573 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0906","serialNo":"01243030202412090101","request_time":"1755594065241","request_id":"1755594065241C0906","version":"1","data":{"firmWareInfo":{"spfw":"588.30.241.59"},"imei_1":"869096056734583","imei_2":"","wifi_mac":"d6:c4:a7:ca:eb:20","bt_mac":"","bsn":""},"myVersionName":"1.1.02.20250819.DSPREAD.MDM.SERVICE","terminalDate":"20250819170105"}
2025-08-19 17:01:05.251 10499-10573 WebSocket               com.dspread.mdm.service              I  🔧 C0906 设备信息上送成功 (1)
2025-08-19 17:01:05.253 10499-10573 WebSocket               com.dspread.mdm.service              I  🔧 C0109 被动式上送: first_connection (被动: 7)
2025-08-19 17:01:05.256 10499-10573 Platform                com.dspread.mdm.service              D  🔧 重新收集应用信息（强制刷新）
2025-08-19 17:01:05.286 10499-10573 AppManager              com.dspread.mdm.service              I  ℹ️ 应用信息获取完成: 总数100(系统95/用户5) 返回5个
2025-08-19 17:01:05.294 10499-10573 Platform                com.dspread.mdm.service              D  🔧 应用信息: 5 个应用
2025-08-19 17:01:05.349 10499-10573 Platform                com.dspread.mdm.service              D  🔧 内存总容量为： 1.87GB 
2025-08-19 17:01:05.349 10499-10573 Platform                com.dspread.mdm.service              D  🔧 内存使用量为： 0.42GB 
2025-08-19 17:01:05.372 10499-10573 Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0109","serialNo":"01243030202412090101","request_time":"1755594065361","request_id":"1755594065361C0109","version":"1","data":{"apkInfo":[{"packName":"de.blinkt.openvpn","apkName":"OpenVPN for Android","versionCode":65,"versionName":"0.5.36a","updateDate":"2025-08-18 16:23:34"},{"packName":"com.android.dreams.basic","apkName":"Basic Daydreams","versionCode":29,"versionName":"10","updateDate":"2009-01-01 08:00:00"},{"packName":"com.dspread.mdm.service","apkName":"service","versionCode":21,"versionName":"1.1.02.20250819.DSPREAD.MDM.SERVICE","updateDate":"2025-08-19 17:00:52"},{"packName":"mark.via","apkName":"Via","versionCode":20250117,"versionName":"6.2.0","updateDate":"2025-08-18 17:50:26"},{"packName":"com.dspread.new","apkName":"Smart-pos-demo","versionCode":3687,"versionName":"3.6.87","updateDate":"2009-01-01 08:00:00"}],"locationInfo":{"gps":{"longitude":"-999.0","latitude":"-999.0","satelliteCount":0,"satelliteCountInUsed":0,"satelliteAverSNR":"0.00","distance":"0.0","lockStatus":"1","lockMeter":"200","proId":"","proName":""},"baseSite":{"MCC":"","MNC":"","CID":"-1","LAC":"-1","DBM":"-999","IMSI":"","ICCID":""},"wifi":{"SSID":"fubox_2.4G","IP":"*************","MAC":"D6:C4:A7:CA:EB:20","SSTH":"-32","BSSID":"7c:10:c9:d6:9f:10"},"address":"无法获取"},"hardwareInfo":[{"name":"CPU Usage","desc":"cpu:0.50"},{"name":"storage_usage_status","desc":" 4.81GB \/ 8.00GB ","state":"1"},{"name":"total_memory","desc":" 0.42GB \/ 1.87GB ","state":"1"},{"name":"androidVersion","desc":"10","state":"1"},{"name":"network_status","desc":"wifi","state":"1"},{"name":"OSVer","desc":"10","state":"1"}],"wifiOption":[{"SSID":"2206","SSTH":"-29"},{"SSID":"fubox_2.4G","SSTH":"-32"},{"SSID":"2206-5G","SSTH":"-41"},{"SSID":"@Ruijie-1816","SSTH":"-51"},{"SSID":"DIRECT-C6-Mi All-in-One Inkjet","SSTH":"-58"},{"SSID":"2207","SSTH":"-68"},{"SSID":"@Ruijie-1816_5G","SSTH":"-71"},{"SSID":"DIRECT-CC-HP Laser 1188w","SSTH":"-75"},{"SSID":"2306","SSTH":"-75"},{"SSID":"dingjie888","SSTH":"-78"},{"SSID":"CMCC-Cc5b","SSTH":"-78"},{"SSID":"2205_5G","SSTH":"-85"}],"sytemInfo":{"androidVersion":"10","buildNumber":"D20Pro_Baseline_debug_20250708_ota_test","aspl":"2021-07-05"}},"myVersionName":"1.1.02.20250819.DSPREAD.MDM.SERVICE","terminalDate":"20250819170105"}
2025-08-19 17:01:05.373 10499-10573 WebSocket               com.dspread.mdm.service              I  🔧 C0109 终端信息上传完成 (1, trigger=first_connection)
2025-08-19 17:01:05.374 10499-10573 Common                  com.dspread.mdm.service              I  ✅ 初始化信息发送完成
2025-08-19 17:01:05.375 10499-10573 WebSocket               com.dspread.mdm.service              I  🔧 检查服务更新结果
2025-08-19 17:01:05.376 10499-10573 Task                    com.dspread.mdm.service              I  ℹ️ 本地任务列表长度: 0
2025-08-19 17:01:05.376 10499-10573 Task                    com.dspread.mdm.service              D  🔧 本地任务列表内容: []
2025-08-19 17:01:05.379 10499-10573 Task                    com.dspread.mdm.service              I  ℹ️ 当前应用版本: versionCode=21, versionName=1.1.02.20250819.DSPREAD.MDM.SERVICE
2025-08-19 17:01:05.379 10499-10573 Task                    com.dspread.mdm.service              I  ℹ️ 任务列表数量: 0
2025-08-19 17:01:05.380 10499-10573 WebSocket               com.dspread.mdm.service              I  🔧 检查OS升级状态...
2025-08-19 17:01:05.384 10499-10573 Common                  com.dspread.mdm.service              D  🔧 OsUpdateStatusChecker WebSocket连接成功，检查OS升级状态
2025-08-19 17:01:05.384 10499-10573 Common                  com.dspread.mdm.service              D  🔧 OsUpdateStatusChecker 没有待检查的OS升级任务
2025-08-19 17:01:05.385 10499-10573 WebSocket               com.dspread.mdm.service              I  🔧 OS升级状态检查已触发
2025-08-19 17:01:05.386 10499-10573 WebSocket               com.dspread.mdm.service              I  🔧 首次连接初始化检查完成
2025-08-19 17:01:05.951 10499-10573 Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"request_time":"1755594065439","org_request_time":"1755594065241","org_request_id":"1755594065241C0906","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755594065439S0000","serialNo":"01243030202412090101"}
2025-08-19 17:01:05.954 10499-10573 WebSocket               com.dspread.mdm.service              I  🔧 收到响应消息: transCode=S0000, orgRequestId=1755594065241C0906, state=0, remark=
2025-08-19 17:01:05.955 10499-10573 WebSocket               com.dspread.mdm.service              I  🔧 处理设备信息响应: state=0
2025-08-19 17:01:05.956 10499-10573 WebSocket               com.dspread.mdm.service              I  🔧 设备信息上传成功
2025-08-19 17:01:05.987 10499-10573 Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"request_time":"1755594065811","org_request_time":"1755594065361","org_request_id":"1755594065361C0109","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755594065811S0000","serialNo":"01243030202412090101"}
2025-08-19 17:01:05.989 10499-10573 WebSocket               com.dspread.mdm.service              I  🔧 收到响应消息: transCode=S0000, orgRequestId=1755594065361C0109, state=0, remark=
2025-08-19 17:01:05.989 10499-10573 WebSocket               com.dspread.mdm.service              I  🔧 终端信息上传成功
2025-08-19 17:01:05.991 10499-10573 WebSocket               com.dspread.mdm.service              I  🔧 终端信息上传成功标志已设置
2025-08-19 17:01:11.624 10499-10499 Receiver                com.dspread.mdm.service              D  🔧 网络恢复，重新上传网络状态信息（防抖通过）
2025-08-19 17:01:11.630 10499-10499 WebSocket               com.dspread.mdm.service              I  🔧 C0904 被动式上送拒绝: NETWORK_RECOVERY (拒绝: 1)
2025-08-19 17:01:11.632 10499-10499 WebSocket               com.dspread.mdm.service              I  🔧 C0904 网络状态上送被流量控制阻止: 被动事件 'NETWORK_RECOVERY' 在平衡模式 - 重要变化下未启用
2025-08-19 17:01:12.860 10499-10499 Receiver                com.dspread.mdm.service              D  🔧 电池状态更新: 电池状态: 电量=37%, 温度=32.2°C, 健康=2, 充电=是, 低电量=否, 耗尽=否
2025-08-19 17:01:12.881 10499-10499 Receiver                com.dspread.mdm.service              D  🔧 电池状态更新: 电池状态: 电量=37%, 温度=32.2°C, 健康=2, 充电=是, 低电量=否, 耗尽=否
2025-08-19 17:01:12.906 10499-10499 Receiver                com.dspread.mdm.service              D  🔧 电池状态更新: 电池状态: 电量=37%, 温度=31.9°C, 健康=2, 充电=是, 低电量=否, 耗尽=否
2025-08-19 17:01:17.999 10499-10499 Receiver                com.dspread.mdm.service              D  🔧 电池状态更新: 电池状态: 电量=38%, 温度=32.2°C, 健康=2, 充电=是, 低电量=否, 耗尽=否
2025-08-19 17:01:30.412 10499-10574 WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 1
2025-08-19 17:01:30.817 10499-10573 WebSocket               com.dspread.mdm.service              I  🔧 收到服务器 PONG 帧: 1
2025-08-19 17:02:00.413 10499-10574 WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 2
2025-08-19 17:02:00.821 10499-10573 WebSocket               com.dspread.mdm.service              I  🔧 收到服务器 PONG 帧: 2
