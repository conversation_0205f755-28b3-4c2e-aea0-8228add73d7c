package com.bbpos.wiseapp.tms.service;

import android.app.ProgressDialog;
import android.content.Intent;
import android.content.pm.ApplicationInfo;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.os.Handler;
import android.os.Looper;
import android.os.Message;
import android.view.WindowManager;

import com.bbpos.wiseapp.logger.BBLog;
import com.bbpos.wiseapp.service.WakeLockService;
import com.bbpos.wiseapp.service.common.RequestPermission;
import com.bbpos.wiseapp.service.common.UsualData;
import com.bbpos.wiseapp.tms.adapter.SystemManagerAdapter;
import com.bbpos.wiseapp.tms.utils.Constants;
import com.bbpos.wiseapp.tms.utils.ContextUtil;
import com.bbpos.wiseapp.tms.utils.ParameterName;
import com.bbpos.wiseapp.utils.SharedPreferencesUtils;
import com.bbpos.wiseapp.websocket.WebSocketSender;

import java.util.Iterator;
import java.util.List;

public class WSTaskResetUnboxService extends WakeLockService {
	private static final String TAG = "UnboxReset";
	private boolean waiting;
	private String org_request_id;
	private String org_request_time;
	private ProgressDialog mProgressDialog;
	private Handler mHandler = new Handler(Looper.getMainLooper()) {
		@Override
		public void handleMessage(Message msg) {
			BBLog.e(BBLog.TAG, "WSTaskResetUnboxService mHandler " + msg.what);
			if (msg.what == 1001) {
				try {
					if (mProgressDialog == null) {
						mProgressDialog = new ProgressDialog(ContextUtil.getInstance());
						mProgressDialog.setTitle("Device Unbinding");
						mProgressDialog.setMessage("Removing applications...");
						mProgressDialog.setCancelable(false);
						mProgressDialog.setCanceledOnTouchOutside(false);
						mProgressDialog.getWindow().setType((WindowManager.LayoutParams.TYPE_SYSTEM_ALERT));
					}
					if (!mProgressDialog.isShowing()) {
						mProgressDialog.show();
					}
				} catch (Exception e) {
					e.printStackTrace();
				}
			} else if (msg.what == 1002) {
				try {
					mProgressDialog.dismiss();
				} catch (Exception e) {
					e.printStackTrace();
				}
			}
		}
	};
	public WSTaskResetUnboxService() {
		super("WSTaskResetUnboxService");
	}

	@Override
	public void onDestroy() {
		super.onDestroy();
	}
	@Override
	protected void onHandleIntent(Intent intent) {
		BBLog.e(TAG, "WSTaskResetUnboxService start");

		mHandler.sendEmptyMessage(1001);
		if (intent.hasExtra(ParameterName.request_id) && intent.hasExtra(ParameterName.request_time)) {
			org_request_id = intent.getStringExtra(ParameterName.request_id);
			org_request_time = intent.getStringExtra(ParameterName.request_time);
		}

		PackageManager packageManager = getPackageManager();
		List<PackageInfo> packageInfos = packageManager.getInstalledPackages(0);

		waiting = false;
		boolean has711Launcher = false;
		for(int i =0;i < packageInfos.size();i++) {
//			while (waiting) {
//				try {
//					Thread.sleep(100);
//				} catch (InterruptedException e) {
//					e.printStackTrace();
//				}
//			}
			PackageInfo packageInfo = packageInfos.get(i);
			//过滤掉系统app
			if ((ApplicationInfo.FLAG_SYSTEM & packageInfo.applicationInfo.flags) != 0
					|| UsualData.SERVICE_PACKAGE_NAME.equals(packageInfo.packageName)
					|| UsualData.LAUNCHER_PACKAGE_NAME.equals(packageInfo.packageName)
					|| UsualData.LOADER_711_PACKAGE_NAME.equals(packageInfo.packageName)
					|| UsualData.LAUNCHER_711_PACKAGE_NAME.equals(packageInfo.packageName)) {
				if (UsualData.LAUNCHER_711_PACKAGE_NAME.equals(packageInfo.packageName)) {
					has711Launcher = true;
				}
				continue;
			}

			BBLog.e(TAG, "WSTaskResetUnboxService uninstall " + packageInfo.packageName);
			waiting = true;
			uninstall(packageInfo.packageName);
			try {
				Thread.sleep(1000);
			} catch (InterruptedException e) {
				e.printStackTrace();
			}
		}

		if (has711Launcher) {
			uninstall(UsualData.LAUNCHER_711_PACKAGE_NAME);
		}

		SharedPreferencesUtils.clearWebSocketTask();

//		LauncherDeamonReceiver.restartLoaderProcess();

		if (!Constants.B_UNBOX_RESET_FROM_GEO) {
			ContextUtil.setLauncherApp(ContextUtil.getInstance(), UsualData.LOADER_711_PACKAGE_NAME);
			WebSocketSender.CC004_unboxResetDone(org_request_id, org_request_time, "0", "");
			SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_UNBOX_STATUS, "0");
			//清除STORE INFO
			SharedPreferencesUtils.clearByKey(UsualData.SHARED_PREFERENCES_GPS_STORE_LATITUDE);
			SharedPreferencesUtils.clearByKey(UsualData.SHARED_PREFERENCES_GPS_STORE_LONGITUDE);
			SharedPreferencesUtils.clearByKey(UsualData.SHARED_PREFERENCES_STORE_ID);
			SharedPreferencesUtils.clearByKey(UsualData.SHARED_PREFERENCES_STORE_SSID);
			SharedPreferencesUtils.clearByKey(UsualData.SHARED_PREFERENCES_STORE_IP);

			Constants.IS_UNBOX_RESETTING = true;
			SystemManagerAdapter.reboot(ContextUtil.getInstance());
		} else {
			if (Constants.M_GEOFENCE_SET_SERVICE_LAUNCHER) {
				ContextUtil.setLauncherApp(ContextUtil.getInstance(), UsualData.SERVICE_PACKAGE_NAME);
			}
			Intent it = new Intent();
			it.setAction(UsualData.ACTION_DATA_WIPED);
			ContextUtil.getInstance().sendBroadcast(it, RequestPermission.REQUEST_PERMISSION_MY_BROADCAST);
		}
		mHandler.sendEmptyMessage(1002);
	}

	private void uninstall(final String pkgName) {
		if (!isAppInstalled(pkgName)){
			waiting = false;
			BBLog.i(TAG, pkgName+" do not exist.");
			return;
		}
		
		SystemManagerAdapter.unInstallApk(getApplicationContext(), pkgName, new SystemManagerAdapter.ApkUnInstallCompleted() {
			@Override
			public void onDeleteFinished(int returnCode) {
				BBLog.i(TAG, "packageDeleted " + pkgName + " returnCode " + returnCode);
	            if (returnCode == 1) {

				} else {

				}
				waiting = false;
			}
		});
	}

    /**
     * 判断应用是否安装 
     * @param pkgName 包名
     * */
    @SuppressWarnings("rawtypes")
	private boolean isAppInstalled(String pkgName){
    	PackageManager pm = this.getPackageManager();
		List installedPackages = pm.getInstalledPackages(0);
		Iterator localIterator = installedPackages.iterator();
		while (localIterator.hasNext()) {
			PackageInfo packageInfo = (PackageInfo) localIterator.next();
			if (packageInfo.applicationInfo.packageName.equals(pkgName)) {
				return true;
			}
		}
		return false;
    }
}
