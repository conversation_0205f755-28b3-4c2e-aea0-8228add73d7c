#include <jni.h>
#include <string>
#include <unistd.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <fcntl.h>

#include "android/log.h"
static const char *TAG="logo_block_device";
#define LOGI(fmt, args...) __android_log_print(ANDROID_LOG_INFO,  TAG, fmt, ##args)
#define LOGD(fmt, args...) __android_log_print(ANDROID_LOG_DEBUG, TAG, fmt, ##args)
#define LOGE(fmt, args...) __android_log_print(ANDROID_LOG_ERROR, TAG, fmt, ##args)

extern "C" JNIEXPORT jobject JNICALL
Java_com_bbpos_bootupcustom_LogoBlockDevice_open(JNIEnv* env, jobject thiz, jstring file)
{
    int fd;
    jobject mFileDescriptor;
    const char *pfile;
    pfile = env->GetStringUTFChars(file, 0);
    /* Opening device */
    {
        fd = open(pfile, O_RDWR);
        LOGD("open() fd = %d", fd);
        if (fd == -1)
        {
            /* Throw an exception */
            LOGE("Cannot open device");
            /* TODO: throw an exception */
            return NULL;
        }
    }

    /* Create a corresponding file descriptor */
    {
        jclass cFileDescriptor = env->FindClass("java/io/FileDescriptor");
        jmethodID iFileDescriptor = env->GetMethodID(cFileDescriptor, "<init>", "()V");
        jfieldID descriptorID = env->GetFieldID(cFileDescriptor, "descriptor", "I");
        mFileDescriptor = env->NewObject(cFileDescriptor, iFileDescriptor);
        env->SetIntField(mFileDescriptor, descriptorID, (jint)fd);
    }
    env->ReleaseStringUTFChars(file, pfile);
    return mFileDescriptor;
}

JNIEXPORT void JNICALL Java_com_bbpos_bootupcustom_LogoBlockDevice_close(JNIEnv *env, jobject thiz)
{
    jclass LogoBlockDeviceClass = env->GetObjectClass(thiz);
    jclass FileDescriptorClass = env->FindClass("java/io/FileDescriptor");

    jfieldID mFdID = env->GetFieldID(LogoBlockDeviceClass, "mFd", "Ljava/io/FileDescriptor;");
    jfieldID descriptorID = env->GetFieldID(FileDescriptorClass, "descriptor", "I");

    jobject mFd = env->GetObjectField(thiz, mFdID);
    jint descriptor = env->GetIntField(mFd, descriptorID);

    LOGD("close(fd = %d)", descriptor);
    close(descriptor);
}