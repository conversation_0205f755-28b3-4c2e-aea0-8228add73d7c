package com.dspread.mdm.service.modules.wifi

import android.annotation.SuppressLint
import android.content.Context
import android.net.Uri
import android.net.wifi.WifiConfiguration
import android.net.wifi.WifiManager
import android.os.Build
import com.dspread.mdm.service.constants.Constants.ModuleConstants
import com.dspread.mdm.service.platform.api.network.WiFiManagerApi
import com.dspread.mdm.service.utils.log.Logger
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import kotlinx.coroutines.delay
import java.io.File

/**
 * WiFi配置助手类
 * 负责WiFi配置的创建、管理和缓存
 */
class WifiConfigurationHelper(
    private val context: Context,
    private val wifiApi: WiFiManagerApi = WiFiManagerApi(context)
) {

    companion object {
        private const val TAG = "[WifiConfigurationHelper]"
    }

    private val wifiManager = context.applicationContext.getSystemService(Context.WIFI_SERVICE) as WifiManager
    // StorageUtils是object，不需要实例化
    private val gson = Gson()

    /**
     * 初始化配置助手
     */
    fun initialize() {
        Logger.wifi("$TAG WiFi配置助手初始化")

        // 确保WiFi已启用
        if (!wifiManager.isWifiEnabled) {
            Logger.wifi("$TAG WiFi未启用，尝试启用WiFi")
            wifiManager.isWifiEnabled = true
        }
    }

    /**
     * 创建WiFi配置
     */
    fun createWifiConfiguration(profile: WifiProfile): WifiConfiguration? {
        return try {
            val config = WifiConfiguration()

            // 基本配置
            config.SSID = "\"${profile.ssid}\""

            // 配置安全类型
            configureWifiSecurity(config, profile)

            // 配置代理（如果有）
            configureWifiProxy(config, profile)

            // 隐藏网络配置
            if (profile.connectHidden) {
                config.hiddenSSID = true
                Logger.wifi("$TAG 配置隐藏网络: ${profile.ssid}")
            }

            Logger.wifi("$TAG 创建WiFi配置成功: ${profile.ssid}")
            config

        } catch (e: Exception) {
            Logger.wifiE("$TAG 创建WiFi配置失败: ${profile.ssid}", e)
            null
        }
    }

    /**
     * 配置WiFi安全类型
     * 增强支持十六进制密码和WEP密钥验证
     */
    private fun configureWifiSecurity(config: WifiConfiguration, profile: WifiProfile) {
        val securityType = WifiSecurityType.fromString(profile.securityType)

        when (securityType) {
            WifiSecurityType.NONE, WifiSecurityType.OPEN -> {
                // 开放网络，无需密码
                config.allowedKeyManagement.set(WifiConfiguration.KeyMgmt.NONE)
                Logger.wifi("$TAG 配置开放网络: ${profile.ssid}")
            }

            WifiSecurityType.WEP -> {
                // WEP加密 - 增强密钥验证
                if (profile.password.isEmpty()) {
                    Logger.wifiE("$TAG WEP网络密码为空: ${profile.ssid}")
                    throw IllegalArgumentException("WEP网络需要密码")
                }

                // 验证WEP密钥格式
                if (!WifiSecurityType.isValidWepKey(profile.password)) {
                    Logger.wifiE("$TAG 无效的WEP密钥: ${profile.ssid}, 密钥长度: ${profile.password.length}")
                    throw IllegalArgumentException("无效的WEP密钥格式")
                }

                config.allowedKeyManagement.set(WifiConfiguration.KeyMgmt.NONE)
                config.allowedAuthAlgorithms.set(WifiConfiguration.AuthAlgorithm.OPEN)
                config.allowedAuthAlgorithms.set(WifiConfiguration.AuthAlgorithm.SHARED)
                config.allowedGroupCiphers.set(WifiConfiguration.GroupCipher.WEP40)
                config.allowedGroupCiphers.set(WifiConfiguration.GroupCipher.WEP104)

                // 根据密钥格式设置WEP密钥
                val length = profile.password.length
                val wepKey = if ((length == 10 || length == 26 || length == 32) &&
                                profile.password.matches(Regex("^[0-9A-Fa-f]*$"))) {
                    // 十六进制密钥，不加引号
                    profile.password
                } else {
                    // ASCII密钥，加引号
                    "\"${profile.password}\""
                }

                config.wepKeys[0] = wepKey
                config.wepTxKeyIndex = 0
                val keyType = if ((length == 10 || length == 26 || length == 32) &&
                                 profile.password.matches(Regex("^[0-9A-Fa-f]*$"))) "HEX" else "ASCII"
                Logger.wifi("$TAG 配置WEP网络: ${profile.ssid}, 密钥类型: $keyType")
            }

            WifiSecurityType.WPA, WifiSecurityType.WPA_PSK -> {
                // WPA-PSK加密
                if (profile.password.isEmpty()) {
                    Logger.wifiE("$TAG WPA网络密码为空: ${profile.ssid}")
                    throw IllegalArgumentException("WPA网络需要密码")
                }

                config.allowedKeyManagement.set(WifiConfiguration.KeyMgmt.WPA_PSK)
                config.allowedProtocols.set(WifiConfiguration.Protocol.WPA)
                config.allowedPairwiseCiphers.set(WifiConfiguration.PairwiseCipher.TKIP)
                config.allowedGroupCiphers.set(WifiConfiguration.GroupCipher.TKIP)

                // 支持十六进制密码
                config.preSharedKey = if (WifiSecurityType.isHexPassword(profile.password) && profile.password.length == 64) {
                    // 64位十六进制PSK，不加引号
                    profile.password
                } else {
                    // 普通密码，加引号
                    "\"${profile.password}\""
                }

                Logger.wifi("$TAG 配置WPA网络: ${profile.ssid}")
            }

            WifiSecurityType.WPA2_PSK -> {
                // WPA2-PSK加密
                if (profile.password.isEmpty()) {
                    Logger.wifiE("$TAG WPA2网络密码为空: ${profile.ssid}")
                    throw IllegalArgumentException("WPA2网络需要密码")
                }

                config.allowedKeyManagement.set(WifiConfiguration.KeyMgmt.WPA_PSK)
                config.allowedProtocols.set(WifiConfiguration.Protocol.RSN)
                config.allowedProtocols.set(WifiConfiguration.Protocol.WPA)
                config.allowedGroupCiphers.set(WifiConfiguration.GroupCipher.TKIP)
                config.allowedGroupCiphers.set(WifiConfiguration.GroupCipher.CCMP)
                config.allowedPairwiseCiphers.set(WifiConfiguration.PairwiseCipher.TKIP)
                config.allowedPairwiseCiphers.set(WifiConfiguration.PairwiseCipher.CCMP)

                // 支持十六进制密码
                config.preSharedKey = if (WifiSecurityType.isHexPassword(profile.password) && profile.password.length == 64) {
                    // 64位十六进制PSK，不加引号
                    profile.password
                } else {
                    // 普通密码，加引号
                    "\"${profile.password}\""
                }

                Logger.wifi("$TAG 配置WPA2网络: ${profile.ssid}")
            }

            WifiSecurityType.NOT_ALLOW -> {
                // 不允许连接的网络类型
                Logger.wifiE("$TAG 网络类型不允许连接: ${profile.ssid}")
                throw IllegalArgumentException("网络类型不允许连接: ${profile.securityType}")
            }
        }
    }

    /**
     * 配置WiFi代理
     * 支持NONE、MANUAL、PAC三种代理类型
     */
    private fun configureWifiProxy(config: WifiConfiguration, profile: WifiProfile) {
        try {
            when (WifiProxyType.fromString(profile.proxyType)) {
                WifiProxyType.NONE -> {
                    // 清除代理设置
                    Logger.wifi("$TAG 清除代理配置: ${profile.ssid}")
                    clearProxySettings(config)
                }

                WifiProxyType.MANUAL -> {
                    // 手动代理配置
                    if (profile.proxyHost.isEmpty() || profile.proxyPort == 0) {
                        Logger.wifiW("$TAG 手动代理配置不完整: ${profile.ssid}")
                        return
                    }

                    Logger.wifi("$TAG 配置手动代理: ${profile.proxyHost}:${profile.proxyPort}")
                    configureManualProxy(config, profile)
                }

                WifiProxyType.PAC -> {
                    // PAC代理配置
                    if (profile.pacUrl.isEmpty()) {
                        Logger.wifiW("$TAG PAC代理URL为空: ${profile.ssid}")
                        return
                    }

                    Logger.wifi("$TAG 配置PAC代理: ${profile.pacUrl}")
                    configurePacProxy(config, profile)
                }

                else -> {
                    // 默认清除代理设置
                    Logger.wifi("$TAG 默认清除代理配置: ${profile.ssid}")
                    clearProxySettings(config)
                }
            }
        } catch (e: Exception) {
            Logger.wifiE("$TAG 配置WiFi代理失败", e)
        }
    }

    /**
     * 清除代理设置
     */
    @SuppressLint("PrivateApi")
    private fun clearProxySettings(config: WifiConfiguration) {
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                clearProxySettingsForLollipop(config)
            } else {
                clearProxySettingsForKitKat(config)
            }
        } catch (e: Exception) {
            Logger.wifiE("$TAG 清除代理设置失败", e)
        }
    }

    /**
     * 清除代理设置 (Android 5.0+)
     */
    @SuppressLint("PrivateApi")
    private fun clearProxySettingsForLollipop(config: WifiConfiguration) {
        try {
            val wifiConfigClass = Class.forName("android.net.wifi.WifiConfiguration")
            val proxyInfoClass = Class.forName("android.net.ProxyInfo")

            // 获取setHttpProxy方法
            val setHttpProxy = wifiConfigClass.getDeclaredMethod("setHttpProxy", proxyInfoClass)
            setHttpProxy.isAccessible = true

            // 获取setProxySettings方法
            val ipConfigClass = Class.forName("android.net.IpConfiguration")
            val proxySettingsField = ipConfigClass.getField("proxySettings")
            val proxySettingsClass = proxySettingsField.type
            val setProxySettings = wifiConfigClass.getDeclaredMethod("setProxySettings", proxySettingsClass)
            setProxySettings.isAccessible = true

            // 设置为无代理
            setHttpProxy.invoke(config, null)
            val noneProxySetting = java.lang.Enum.valueOf(proxySettingsClass as Class<out Enum<*>>, "NONE")
            setProxySettings.invoke(config, noneProxySetting)

            Logger.wifi("$TAG 代理设置已清除(Lollipop+)")

        } catch (e: Exception) {
            Logger.wifiE("$TAG Lollipop+清除代理设置失败", e)
        }
    }

    /**
     * 清除代理设置 (Android 4.4)
     */
    @SuppressLint("PrivateApi")
    private fun clearProxySettingsForKitKat(config: WifiConfiguration) {
        try {
            val proxySettings = Class.forName("android.net.wifi.WifiConfiguration\$ProxySettings")
            val proxySettingsNone = proxySettings.getField("NONE").get(null)

            // 设置代理配置
            val proxySettingsField = config.javaClass.getField("proxySettings")
            val httpProxyField = config.javaClass.getField("httpProxy")

            proxySettingsField.set(config, proxySettingsNone)
            httpProxyField.set(config, null)

            Logger.wifi("$TAG 代理设置已清除(KitKat)")

        } catch (e: Exception) {
            Logger.wifiE("$TAG KitKat清除代理设置失败", e)
        }
    }

    /**
     * 配置手动代理
     * 使用更健壮的反射方式，兼容不同Android版本
     */
    private fun configureManualProxy(config: WifiConfiguration, profile: WifiProfile) {
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                configureManualProxyForLollipop(config, profile)
            } else {
                configureManualProxyForKitKat(config, profile)
            }
        } catch (e: Exception) {
            Logger.wifiE("$TAG 手动代理配置失败，使用简化配置", e)
            // 降级处理，记录配置信息
            Logger.wifi("$TAG 代理信息: ${profile.proxyHost}:${profile.proxyPort}, 排除: ${profile.byPassProxyFor}")
        }
    }

    /**
     * Android 5.0+ 手动代理配置
     */
    private fun configureManualProxyForLollipop(config: WifiConfiguration, profile: WifiProfile) {
        try {
            // 获取ProxyInfo类和方法
            val proxyInfoClass = Class.forName("android.net.ProxyInfo")
            val wifiConfigClass = Class.forName("android.net.wifi.WifiConfiguration")

            // 获取setHttpProxy方法
            val setHttpProxy = wifiConfigClass.getDeclaredMethod("setHttpProxy", proxyInfoClass)
            setHttpProxy.isAccessible = true

            // 获取setProxySettings方法
            val ipConfigClass = Class.forName("android.net.IpConfiguration")
            val proxySettingsField = ipConfigClass.getField("proxySettings")
            val proxySettingsClass = proxySettingsField.type
            val setProxySettings = wifiConfigClass.getDeclaredMethod("setProxySettings", proxySettingsClass)
            setProxySettings.isAccessible = true

            // 解析代理排除列表
            val exclusionList = parseProxyExclusionList(profile.byPassProxyFor)

            // 创建ProxyInfo对象
            val proxyInfo = if (exclusionList.isEmpty()) {
                proxyInfoClass.getMethod("buildDirectProxy", String::class.java, Int::class.java)
                    .invoke(null, profile.proxyHost, profile.proxyPort)
            } else {
                proxyInfoClass.getMethod("buildDirectProxy", String::class.java, Int::class.java, List::class.java)
                    .invoke(null, profile.proxyHost, profile.proxyPort, exclusionList)
            }

            // 设置代理
            setHttpProxy.invoke(config, proxyInfo)

            // 设置代理类型为STATIC
            val staticProxySetting = java.lang.Enum.valueOf(proxySettingsClass as Class<out Enum<*>>, "STATIC")
            setProxySettings.invoke(config, staticProxySetting)

            Logger.wifi("$TAG 手动代理配置成功(Lollipop+): ${profile.proxyHost}:${profile.proxyPort}")

        } catch (e: Exception) {
            Logger.wifiE("$TAG Lollipop+手动代理配置失败", e)
            throw e
        }
    }

    /**
     * Android 4.4 手动代理配置
     */
    @SuppressLint("PrivateApi")
    private fun configureManualProxyForKitKat(config: WifiConfiguration, profile: WifiProfile) {
        try {
            val proxySettings = Class.forName("android.net.wifi.WifiConfiguration\$ProxySettings")
            val proxySettingsManual = proxySettings.getField("STATIC").get(null)

            val proxyInfo = Class.forName("android.net.ProxyInfo")
            val buildDirectProxy = proxyInfo.getMethod("buildDirectProxy", String::class.java, Int::class.java, List::class.java)

            // 解析代理排除列表
            val exclusionList = parseProxyExclusionList(profile.byPassProxyFor)
            val proxy = buildDirectProxy.invoke(null, profile.proxyHost, profile.proxyPort, exclusionList)

            // 设置代理配置
            val proxySettingsField = config.javaClass.getField("proxySettings")
            val httpProxyField = config.javaClass.getField("httpProxy")

            proxySettingsField.set(config, proxySettingsManual)
            httpProxyField.set(config, proxy)

            Logger.wifi("$TAG 手动代理配置成功(KitKat): ${profile.proxyHost}:${profile.proxyPort}")

        } catch (e: Exception) {
            Logger.wifiE("$TAG KitKat手动代理配置失败", e)
            throw e
        }
    }

    /**
     * 配置PAC代理
     */
    private fun configurePacProxy(config: WifiConfiguration, profile: WifiProfile) {
        try {
            configurePacProxyForLollipop(config, profile)
        } catch (e: Exception) {
            Logger.wifiE("$TAG PAC代理配置失败", e)
        }
    }

    /**
     * Android 5.0+ PAC代理配置
     */
    private fun configurePacProxyForLollipop(config: WifiConfiguration, profile: WifiProfile) {
        try {
            // 获取ProxyInfo类和方法
            val proxyInfoClass = Class.forName("android.net.ProxyInfo")
            val wifiConfigClass = Class.forName("android.net.wifi.WifiConfiguration")

            // 获取setHttpProxy方法
            val setHttpProxy = wifiConfigClass.getDeclaredMethod("setHttpProxy", proxyInfoClass)
            setHttpProxy.isAccessible = true

            // 获取setProxySettings方法
            val ipConfigClass = Class.forName("android.net.IpConfiguration")
            val proxySettingsField = ipConfigClass.getField("proxySettings")
            val proxySettingsClass = proxySettingsField.type
            val setProxySettings = wifiConfigClass.getDeclaredMethod("setProxySettings", proxySettingsClass)
            setProxySettings.isAccessible = true

            // 创建PAC代理
            val buildPacProxy = proxyInfoClass.getMethod("buildPacProxy", Uri::class.java)
            val pacUri = Uri.parse(profile.pacUrl)
            val proxyInfo = buildPacProxy.invoke(null, pacUri)

            // 设置代理
            setHttpProxy.invoke(config, proxyInfo)

            // 设置代理类型为PAC
            val pacProxySetting = java.lang.Enum.valueOf(proxySettingsClass as Class<out Enum<*>>, "PAC")
            setProxySettings.invoke(config, pacProxySetting)

            Logger.wifi("$TAG PAC代理配置成功(Lollipop+): ${profile.pacUrl}")

        } catch (e: Exception) {
            Logger.wifiE("$TAG Lollipop+ PAC代理配置失败", e)
            throw e
        }
    }

    /**
     * Android 4.4 PAC代理配置
     */
    @SuppressLint("PrivateApi")
    private fun configurePacProxyForKitKat(config: WifiConfiguration, profile: WifiProfile) {
        try {
            val proxySettings = Class.forName("android.net.wifi.WifiConfiguration\$ProxySettings")
            val proxySettingsPac = proxySettings.getField("PAC").get(null)

            val proxyInfo = Class.forName("android.net.ProxyInfo")
            val buildPacProxy = proxyInfo.getMethod("buildPacProxy", Uri::class.java)

            val pacUri = Uri.parse(profile.pacUrl)
            val proxy = buildPacProxy.invoke(null, pacUri)

            // 设置代理配置
            val proxySettingsField = config.javaClass.getField("proxySettings")
            val httpProxyField = config.javaClass.getField("httpProxy")

            proxySettingsField.set(config, proxySettingsPac)
            httpProxyField.set(config, proxy)

            Logger.wifi("$TAG PAC代理配置成功(KitKat): ${profile.pacUrl}")

        } catch (e: Exception) {
            Logger.wifiE("$TAG KitKat PAC代理配置失败", e)
            throw e
        }
    }

    /**
     * 解析代理排除列表
     */
    private fun parseProxyExclusionList(byPassProxyFor: String): List<String> {
        if (byPassProxyFor.isEmpty()) {
            return emptyList()
        }

        return try {
            // 尝试解析JSON数组格式
            if (byPassProxyFor.startsWith("[") && byPassProxyFor.endsWith("]")) {
                val gson = Gson()
                val type = object : TypeToken<List<String>>() {}.type
                gson.fromJson(byPassProxyFor, type) ?: emptyList()
            } else {
                // 逗号分隔格式
                byPassProxyFor.split(",").map { it.trim() }.filter { it.isNotEmpty() }
            }
        } catch (e: Exception) {
            Logger.wifiE("$TAG 解析代理排除列表失败: $byPassProxyFor", e)
            emptyList()
        }
    }

    /**
     * 验证代理配置是否有效
     */
    fun validateProxyConfiguration(profile: WifiProfile): Boolean {
        val proxyType = WifiProxyType.fromString(profile.proxyType)

        return when (proxyType) {
            WifiProxyType.NONE -> {
                Logger.wifi("$TAG 无代理配置: ${profile.ssid}")
                true
            }

            WifiProxyType.MANUAL -> {
                if (profile.proxyHost.isEmpty() || profile.proxyPort <= 0 || profile.proxyPort > 65535) {
                    Logger.wifiE("$TAG 手动代理配置无效: ${profile.ssid}, ${profile.proxyHost}:${profile.proxyPort}")
                    return false
                }

                // 验证代理排除列表格式
                try {
                    parseProxyExclusionList(profile.byPassProxyFor)
                    Logger.wifi("$TAG 手动代理配置有效: ${profile.ssid}, ${profile.proxyHost}:${profile.proxyPort}")
                    true
                } catch (e: Exception) {
                    Logger.wifiE("$TAG 代理排除列表格式错误: ${profile.ssid}", e)
                    false
                }
            }

            WifiProxyType.PAC -> {
                if (profile.pacUrl.isEmpty()) {
                    Logger.wifiE("$TAG PAC代理URL为空: ${profile.ssid}")
                    return false
                }

                if (!profile.pacUrl.startsWith("http://") && !profile.pacUrl.startsWith("https://")) {
                    Logger.wifiE("$TAG PAC代理URL格式错误: ${profile.ssid}, ${profile.pacUrl}")
                    return false
                }

                Logger.wifi("$TAG PAC代理配置有效: ${profile.ssid}, ${profile.pacUrl}")
                true
            }
        }
    }

    /**
     * 添加或更新WiFi配置到系统
     * 优化：添加配置差异检测和智能重连逻辑
     */
    suspend fun addWifiConfiguration(config: WifiConfiguration, profile: WifiProfile? = null): Int {
        val ssid = config.SSID?.replace("\"", "") ?: ""

        // 1. 检查是否为当前连接的WiFi且配置发生变化
        val shouldForceReconnect = checkAndHandleConfigurationChange(ssid, profile)

        // 检查是否已存在相同SSID的配置
        val existingNetwork = wifiApi.findNetworkBySSID(ssid).getOrNull()

        val networkId = if (existingNetwork != null) {
            // 更新现有配置
            Logger.wifi("$TAG WiFi配置已存在，更新配置: $ssid, networkId: ${existingNetwork.networkId}")
            if (profile != null) {
                Logger.wifi("$TAG 更新内容: ${getProfileChangeDescription(profile)}")
            }

            // 2. 如果需要强制重连，先断开当前连接
            if (shouldForceReconnect) {
                Logger.wifiI("$TAG 检测到配置变化，主动断开当前连接: $ssid")
                disconnectCurrentWifi()
            }

            config.networkId = existingNetwork.networkId
            wifiApi.updateNetwork(config).getOrElse { -1 }
        } else {
            // 添加新配置
            wifiApi.addNetwork(config).getOrElse { -1 }
        }

        if (networkId != -1) {
            // 启用网络配置
            wifiApi.enableNetwork(networkId, false)

            // 保存配置并检查结果，失败时重试
            val saveResult = saveConfigurationWithRetry(ssid)
            if (!saveResult) {
                Logger.wifiW("$TAG 系统WiFi配置保存失败（已重试），但继续处理: $ssid")
            }

            // 3. 保存新的配置到缓存（用于下次比较）
            if (profile != null) {
                saveCurrentWifiProfileCache(profile)
            }
        }

        return networkId
    }



    /**
     * 移除WiFi配置
     */
    suspend fun removeWifiConfiguration(ssid: String): Boolean {
        Logger.wifi("$TAG 开始移除WiFi配置: $ssid")

        val targetConfig = wifiApi.findNetworkBySSID(ssid).getOrNull()

        return if (targetConfig != null) {
            val removed = wifiApi.removeNetwork(targetConfig.networkId).getOrElse { false }
            if (removed) {
                wifiApi.saveConfiguration()
                Logger.wifi("$TAG WiFi配置移除成功: $ssid")
            }
            removed
        } else {
            Logger.wifiW("$TAG 未找到WiFi配置: $ssid")
            false
        }
    }

    /**
     * 连接到指定的WiFi网络
     */
    suspend fun connectToNetwork(networkId: Int): Boolean {
        val enableResult = wifiApi.enableNetwork(networkId, true).getOrElse { false }
        if (enableResult) {
            Logger.wifi("$TAG 开始连接WiFi网络: networkId=$networkId")
            wifiApi.reconnect()
        }
        return enableResult
    }

    /**
     * 移除不在配置列表中的WiFi
     */
    @SuppressLint("MissingPermission")
    fun removeNonListWifi(allowedSsids: List<String>) {
        try {
            val configuredNetworks = wifiManager.configuredNetworks ?: return
            val allowedSsidSet = allowedSsids.map { "\"$it\"" }.toSet()

            var removedCount = 0

            for (config in configuredNetworks) {
                if (config.SSID !in allowedSsidSet) {
                    val removed = wifiManager.removeNetwork(config.networkId)
                    if (removed) {
                        removedCount++
                        Logger.wifi("$TAG 移除非列表WiFi: ${config.SSID}")
                    }
                }
            }

            if (removedCount > 0) {
                wifiManager.saveConfiguration()
                Logger.wifi("$TAG 移除非列表WiFi完成，共移除: $removedCount 个")
            }

        } catch (e: Exception) {
            Logger.wifiE("$TAG 移除非列表WiFi失败", e)
        }
    }

    /**
     * 获取当前连接的WiFi信息
     */
    suspend fun getCurrentWifiInfo(): String? {
        return wifiApi.getCurrentWifiInfo().getOrNull()?.ssid
    }

    /**
     * 检查WiFi是否已配置
     */
    @SuppressLint("MissingPermission")
    fun isWifiConfigured(ssid: String): Boolean {
        return try {
            val configuredNetworks = wifiManager.configuredNetworks ?: return false
            val targetSsid = "\"$ssid\""

            configuredNetworks.any { it.SSID == targetSsid }
        } catch (e: Exception) {
            Logger.wifiE("$TAG 检查WiFi配置失败: $ssid", e)
            false
        }
    }

    /**
     * 保存WiFi配置到缓存文件
     */
    fun saveCachedProfiles(profiles: List<WifiProfile>) {
        try {
            val cacheFile = getCacheFile()
            val json = gson.toJson(profiles)

            cacheFile.writeText(json)
            Logger.wifi("$TAG WiFi配置缓存保存成功: ${profiles.size} 个")

        } catch (e: Exception) {
            Logger.wifiE("$TAG WiFi配置缓存保存失败", e)
        }
    }

    /**
     * 从缓存文件加载WiFi配置
     */
    fun loadCachedProfiles(): List<WifiProfile> {
        return try {
            val cacheFile = getCacheFile()

            if (!cacheFile.exists()) {
                Logger.wifi("$TAG WiFi配置缓存文件不存在")
                return emptyList()
            }

            val json = cacheFile.readText()
            val type = object : TypeToken<List<WifiProfile>>() {}.type
            val profiles = gson.fromJson<List<WifiProfile>>(json, type) ?: emptyList()

            Logger.wifi("$TAG WiFi配置缓存加载成功: ${profiles.size} 个")
            profiles

        } catch (e: Exception) {
            Logger.wifiE("$TAG WiFi配置缓存加载失败", e)
            emptyList()
        }
    }

    /**
     * 清除WiFi配置缓存
     */
    fun clearCachedProfiles() {
        try {
            val cacheFile = getCacheFile()
            if (cacheFile.exists()) {
                cacheFile.delete()
                Logger.wifi("$TAG WiFi配置缓存清除成功")
            }
        } catch (e: Exception) {
            Logger.wifiE("$TAG WiFi配置缓存清除失败", e)
        }
    }

    /**
     * 检查并处理配置变化
     * 返回是否需要强制重连
     */
    private fun checkAndHandleConfigurationChange(ssid: String, newProfile: WifiProfile?): Boolean {
        if (newProfile == null) return false

        try {
            // 1. 检查是否为当前连接的WiFi
            val currentSsid = getCurrentConnectedSSID()
            if (currentSsid != ssid) {
                Logger.wifi("$TAG 目标SSID与当前连接不同: 当前=$currentSsid, 目标=$ssid")
                return false
            }

            // 2. 获取缓存的配置
            val cachedProfile = getCurrentWifiProfileCache()
            if (cachedProfile == null) {
                Logger.wifi("$TAG 没有找到缓存的WiFi配置，将进行首次连接: $ssid")
                return false
            }

            // 3. 比较配置是否发生变化
            val hasChanged = hasProfileChanged(cachedProfile, newProfile)
            if (hasChanged) {
                Logger.wifiI("$TAG 检测到WiFi配置发生变化: $ssid")
                Logger.wifiI("$TAG 变化详情: ${getProfileDifference(cachedProfile, newProfile)}")
                return true
            } else {
                Logger.wifi("$TAG WiFi配置无变化，跳过重连: $ssid")
                return false
            }

        } catch (e: Exception) {
            Logger.wifiE("$TAG 检查配置变化失败: $ssid", e)
            return false
        }
    }

    /**
     * 比较两个WiFi配置是否发生变化
     */
    private fun hasProfileChanged(oldProfile: WifiProfile, newProfile: WifiProfile): Boolean {
        return oldProfile.password != newProfile.password ||
               oldProfile.securityType != newProfile.securityType ||
               oldProfile.proxyType != newProfile.proxyType ||
               oldProfile.proxyHost != newProfile.proxyHost ||
               oldProfile.proxyPort != newProfile.proxyPort ||
               oldProfile.proxyUser != newProfile.proxyUser ||
               oldProfile.proxyPassword != newProfile.proxyPassword ||
               oldProfile.connectHidden != newProfile.connectHidden ||
               oldProfile.pacUrl != newProfile.pacUrl ||
               oldProfile.byPassProxyFor != newProfile.byPassProxyFor
    }

    /**
     * 获取配置变化的详细描述
     */
    private fun getProfileDifference(oldProfile: WifiProfile, newProfile: WifiProfile): String {
        val changes = mutableListOf<String>()

        if (oldProfile.password != newProfile.password) {
            changes.add("密码变化")
        }
        if (oldProfile.securityType != newProfile.securityType) {
            changes.add("安全类型: ${oldProfile.securityType} -> ${newProfile.securityType}")
        }
        if (oldProfile.proxyType != newProfile.proxyType) {
            changes.add("代理类型: ${oldProfile.proxyType} -> ${newProfile.proxyType}")
        }
        if (oldProfile.proxyHost != newProfile.proxyHost) {
            changes.add("代理主机: ${oldProfile.proxyHost} -> ${newProfile.proxyHost}")
        }
        if (oldProfile.proxyPort != newProfile.proxyPort) {
            changes.add("代理端口: ${oldProfile.proxyPort} -> ${newProfile.proxyPort}")
        }
        if (oldProfile.connectHidden != newProfile.connectHidden) {
            changes.add("隐藏网络: ${oldProfile.connectHidden} -> ${newProfile.connectHidden}")
        }

        return if (changes.isNotEmpty()) {
            changes.joinToString(", ")
        } else {
            "无明显变化"
        }
    }

    /**
     * 获取配置变化的简要描述
     */
    private fun getProfileChangeDescription(profile: WifiProfile): String {
        val descriptions = mutableListOf<String>()
        descriptions.add("安全类型=${profile.securityType}")
        descriptions.add("代理类型=${profile.getProxyDescription()}")
        if (profile.connectHidden) {
            descriptions.add("隐藏网络")
        }
        return descriptions.joinToString(", ")
    }

    /**
     * 获取缓存文件
     */
    private fun getCacheFile(): File {
        val cacheDir = File(context.cacheDir, "wifi_profiles")
        if (!cacheDir.exists()) {
            cacheDir.mkdirs()
        }
        return File(cacheDir, ModuleConstants.WIFI_PROFILE_CACHE_FILE)
    }

    /**
     * 获取当前连接WiFi配置缓存文件路径
     */
    private fun getCurrentWifiCacheFile(): File {
        val cacheDir = File(context.cacheDir, "wifi_profiles")
        if (!cacheDir.exists()) {
            cacheDir.mkdirs()
        }
        return File(cacheDir, "current_wifi_profile.json")
    }

    /**
     * 保存当前WiFi配置到缓存
     */
    fun saveCurrentWifiProfileCache(profile: WifiProfile) {
        try {
            val cacheFile = getCurrentWifiCacheFile()
            val json = gson.toJson(profile)
            cacheFile.writeText(json)
            Logger.wifi("$TAG 当前WiFi配置缓存保存成功: ${profile.ssid}")
        } catch (e: Exception) {
            Logger.wifiE("$TAG 当前WiFi配置缓存保存失败: ${profile.ssid}", e)
        }
    }

    /**
     * 获取当前WiFi配置缓存
     */
    private fun getCurrentWifiProfileCache(): WifiProfile? {
        return try {
            val cacheFile = getCurrentWifiCacheFile()
            if (!cacheFile.exists()) {
                return null
            }

            val json = cacheFile.readText()
            val profile = gson.fromJson(json, WifiProfile::class.java)
            Logger.wifi("$TAG 当前WiFi配置缓存加载成功: ${profile.ssid}")
            profile
        } catch (e: Exception) {
            Logger.wifiE("$TAG 当前WiFi配置缓存加载失败", e)
            null
        }
    }

    /**
     * 获取当前连接的WiFi SSID
     */
    private fun getCurrentConnectedSSID(): String? {
        return try {
            val wifiInfo = wifiManager.connectionInfo
            val ssid = wifiInfo?.ssid?.replace("\"", "")
            if (ssid == "<unknown ssid>" || ssid.isNullOrEmpty()) {
                null
            } else {
                ssid
            }
        } catch (e: Exception) {
            Logger.wifiE("$TAG 获取当前连接SSID失败", e)
            null
        }
    }

    /**
     * 断开当前WiFi连接
     */
    private suspend fun disconnectCurrentWifi() {
        try {
            val wifiInfo = wifiManager.connectionInfo
            if (wifiInfo != null && wifiInfo.networkId != -1) {
                Logger.wifiI("$TAG 断开当前WiFi连接: networkId=${wifiInfo.networkId}")
                wifiApi.disableNetwork(wifiInfo.networkId)
                wifiApi.disconnect()

                // 等待断开完成
                delay(2000)
                Logger.wifiI("$TAG WiFi连接已断开")
            }
        } catch (e: Exception) {
            Logger.wifiE("$TAG 断开WiFi连接失败", e)
        }
    }

    /**
     * 带重试的配置保存
     */
    private suspend fun saveConfigurationWithRetry(ssid: String, maxRetries: Int = 2): Boolean {
        repeat(maxRetries) { attempt ->
            val result = wifiApi.saveConfiguration().getOrElse { false }
            if (result) {
                if (attempt > 0) {
                    Logger.wifi("$TAG 系统WiFi配置保存成功（第${attempt + 1}次尝试）: $ssid")
                }
                return true
            } else {
                if (attempt < maxRetries - 1) {
                    Logger.wifiW("$TAG 系统WiFi配置保存失败，准备重试: $ssid (第${attempt + 1}次)")
                    delay(1000) // 等待1秒后重试
                }
            }
        }
        return false
    }

    /**
     * 获取WiFi信号强度
     */
    fun getWifiSignalStrength(): Int {
        return try {
            val wifiInfo = wifiManager.connectionInfo
            WifiManager.calculateSignalLevel(wifiInfo.rssi, 5)
        } catch (e: Exception) {
            Logger.wifiE("$TAG 获取WiFi信号强度失败", e)
            0
        }
    }

    /**
     * 检查WiFi是否已连接
     */
    suspend fun isWifiConnected(): Boolean {
        return wifiApi.isWifiConnected().getOrElse { false }
    }

    /**
     * 检查WiFi是否在信号范围内
     * 扫描周边WiFi热点，检查指定SSID是否可达
     */
    @SuppressLint("MissingPermission")
    fun checkIsWifiInRange(profile: WifiProfile): Boolean {
        return try {
            Logger.wifi("$TAG 开始检查WiFi信号范围: ${profile.ssid}")

            // 如果是隐藏网络，默认认为在范围内（无法通过扫描检测）
            if (profile.connectHidden) {
                Logger.wifi("$TAG 隐藏网络默认在范围内: ${profile.ssid}")
                return true
            }

            // 启动WiFi扫描（带超时控制）
            val scanResult = startWifiScanWithTimeout()
            if (!scanResult) {
                Logger.wifiW("$TAG WiFi扫描启动失败，默认认为在范围内: ${profile.ssid}")
                return true // 扫描失败时默认允许连接
            }

            // 等待扫描完成
            Thread.sleep(3000) // 等待3秒让扫描完成

            // 获取扫描结果
            val scanResults = wifiManager.scanResults
            if (scanResults.isNullOrEmpty()) {
                Logger.wifiW("$TAG WiFi扫描结果为空，默认认为在范围内: ${profile.ssid}")
                return true
            }

            // 检查目标SSID是否在扫描结果中
            val targetSsid = profile.ssid
            val foundWifi = scanResults.find { scanResult ->
                val scannedSsid = scanResult.SSID
                scannedSsid == targetSsid
            }

            if (foundWifi != null) {
                val signalLevel = foundWifi.level
                val signalStrength = calculateSignalStrength(signalLevel)
                Logger.wifi("$TAG WiFi在信号范围内: ${profile.ssid}, 信号强度: $signalLevel dBm ($signalStrength)")

                // 检查信号强度是否足够（例如 > -85 dBm）
                val isSignalStrong = signalLevel > -85
                if (!isSignalStrong) {
                    Logger.wifiW("$TAG WiFi信号过弱: ${profile.ssid}, 信号强度: $signalLevel dBm")
                }

                return isSignalStrong
            } else {
                Logger.wifi("$TAG WiFi不在信号范围内: ${profile.ssid}")
                return false
            }

        } catch (e: Exception) {
            Logger.wifiE("$TAG 检查WiFi信号范围异常: ${profile.ssid}", e)
            return true // 异常时默认允许连接
        }
    }

    /**
     * 带超时控制的WiFi扫描
     */
    private fun startWifiScanWithTimeout(): Boolean {
        return try {
            if (!wifiManager.isWifiEnabled) {
                Logger.wifiW("$TAG WiFi未启用，无法扫描")
                return false
            }

            val scanStarted = wifiManager.startScan()
            Logger.wifi("$TAG WiFi扫描启动: $scanStarted")

            if (!scanStarted) {
                Logger.wifiW("$TAG WiFi扫描启动失败")
                return false
            }

            scanStarted

        } catch (e: Exception) {
            Logger.wifiE("$TAG WiFi扫描启动异常", e)
            false
        }
    }

    /**
     * 启动WiFi扫描
     */
    private fun startWifiScan(): Boolean {
        return try {
            if (!wifiManager.isWifiEnabled) {
                Logger.wifiW("$TAG WiFi未启用，无法扫描")
                return false
            }

            val scanStarted = wifiManager.startScan()
            Logger.wifi("$TAG WiFi扫描启动: $scanStarted")
            scanStarted

        } catch (e: Exception) {
            Logger.wifiE("$TAG WiFi扫描启动失败", e)
            false
        }
    }

    /**
     * 计算信号强度描述
     */
    private fun calculateSignalStrength(level: Int): String {
        return when {
            level > -50 -> "优秀"
            level > -60 -> "良好"
            level > -70 -> "一般"
            level > -80 -> "较弱"
            else -> "很弱"
        }
    }

    /**
     * 获取周边WiFi列表
     * 返回所有可检测到的WiFi SSID列表
     */
    @SuppressLint("MissingPermission")
    fun getWifiInRange(): List<String> {
        return try {
            Logger.wifi("$TAG 获取周边WiFi列表")

            // 启动扫描
            if (!startWifiScan()) {
                Logger.wifiW("$TAG WiFi扫描启动失败")
                return emptyList()
            }

            // 等待扫描完成
            Thread.sleep(3000)

            // 获取扫描结果
            val scanResults = wifiManager.scanResults
            if (scanResults.isNullOrEmpty()) {
                Logger.wifiW("$TAG WiFi扫描结果为空")
                return emptyList()
            }

            // 提取SSID列表，过滤空SSID和重复项
            val wifiList = scanResults
                .mapNotNull { it.SSID }
                .filter { it.isNotEmpty() }
                .distinct()
                .sorted()

            Logger.wifi("$TAG 发现 ${wifiList.size} 个WiFi热点: ${wifiList.joinToString(", ")}")
            wifiList

        } catch (e: Exception) {
            Logger.wifiE("$TAG 获取周边WiFi列表失败", e)
            emptyList()
        }
    }

    /**
     * 验证隐藏网络配置
     */
    fun validateHiddenNetworkConfiguration(profile: WifiProfile): Boolean {
        if (!profile.connectHidden) {
            return true // 非隐藏网络，无需特殊验证
        }

        Logger.wifi("$TAG 验证隐藏网络配置: ${profile.ssid}")

        // 隐藏网络必须有SSID
        if (profile.ssid.isEmpty()) {
            Logger.wifiE("$TAG 隐藏网络SSID不能为空")
            return false
        }

        // 隐藏网络通常需要密码（除非是开放网络）
        val securityType = WifiSecurityType.fromString(profile.securityType)
        if (securityType != WifiSecurityType.NONE && securityType != WifiSecurityType.OPEN) {
            if (profile.password.isEmpty()) {
                Logger.wifiE("$TAG 隐藏网络需要密码: ${profile.ssid}")
                return false
            }
        }

        Logger.wifi("$TAG 隐藏网络配置验证通过: ${profile.ssid}")
        return true
    }

    /**
     * 检查是否为隐藏网络
     */
    fun isHiddenWifi(profile: WifiProfile): Boolean {
        val isHidden = profile.connectHidden
        Logger.wifi("$TAG 检查隐藏网络: ${profile.ssid} -> $isHidden")
        return isHidden
    }

    /**
     * 为隐藏网络创建特殊的WiFi配置
     */
    fun createHiddenWifiConfiguration(profile: WifiProfile): WifiConfiguration? {
        if (!profile.connectHidden) {
            return createWifiConfiguration(profile)
        }

        Logger.wifi("$TAG 创建隐藏网络配置: ${profile.ssid}")

        return try {
            val config = WifiConfiguration()

            // 基本配置
            config.SSID = "\"${profile.ssid}\""
            config.hiddenSSID = true // 标记为隐藏网络

            // 配置安全类型
            configureWifiSecurity(config, profile)

            // 配置代理（如果有）
            configureWifiProxy(config, profile)

            Logger.wifi("$TAG 隐藏网络配置创建成功: ${profile.ssid}")
            config

        } catch (e: Exception) {
            Logger.wifiE("$TAG 隐藏网络配置创建失败: ${profile.ssid}", e)
            null
        }
    }

    /**
     * 连接隐藏网络的特殊处理
     */
    suspend fun connectToHiddenWifi(profile: WifiProfile): Int {
        if (!profile.connectHidden) {
            Logger.wifiE("$TAG 非隐藏网络，使用普通连接方法: ${profile.ssid}")
            return -1
        }

        Logger.wifi("$TAG 开始连接隐藏网络: ${profile.ssid}")

        try {
            // 1. 验证隐藏网络配置
            if (!validateHiddenNetworkConfiguration(profile)) {
                Logger.wifiE("$TAG 隐藏网络配置验证失败: ${profile.ssid}")
                return -1
            }

            // 2. 创建隐藏网络配置
            val config = createHiddenWifiConfiguration(profile)
            if (config == null) {
                Logger.wifiE("$TAG 隐藏网络配置创建失败: ${profile.ssid}")
                return -1
            }

            // 3. 添加到系统
            val networkId = addWifiConfiguration(config, profile)
            if (networkId == -1) {
                Logger.wifiE("$TAG 隐藏网络添加到系统失败: ${profile.ssid}")
                return -1
            }

            Logger.wifi("$TAG 隐藏网络连接配置完成: ${profile.ssid}, networkId: $networkId")
            return networkId

        } catch (e: Exception) {
            Logger.wifiE("$TAG 隐藏网络连接异常: ${profile.ssid}", e)
            return -1
        }
    }
}
