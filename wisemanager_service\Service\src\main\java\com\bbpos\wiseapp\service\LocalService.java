package com.bbpos.wiseapp.service;

import android.app.Service;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.ServiceConnection;
import android.os.IBinder;
import android.os.RemoteException;
import android.util.Log;

import com.bbpos.wiseapp.logger.BBLog;
import com.bbpos.wiseapp.settings.utils.ServiceAliveUtils;
import com.bbpos.wiseapp.system.api.ServiceApi;

/**
 * 本地进程，
 */
@Deprecated
public class LocalService extends Service {
    private LocalServiceBinder binder;

    private ServiceConnection connection = new ServiceConnection() {
        @Override
        public void onServiceConnected(ComponentName name, IBinder service) {
            BBLog.d(BBLog.TAG,"LocalService  onServiceConnected ->");
           boolean isServiceRunning = ServiceAliveUtils.isServiceAlice();
           if (!isServiceRunning){
               BBLog.d(BBLog.TAG, "LocalService  onServiceConnected -> 检测到service未存活，运行守护进程service  " );
               Intent i = new Intent(LocalService.this,RemoteGuardService.class);
               startService(i);
                //重启service模块
               restartService();
           }
            KeepAliveConnection iMyAidlInterface = KeepAliveConnection.Stub.asInterface(service);
            try {
                BBLog.d(BBLog.TAG, "LocalService  onServiceConnected -> connected with " + iMyAidlInterface.getServiceName());
            } catch (RemoteException e) {
                e.printStackTrace();
            }
        }

        @Override
        public void onServiceDisconnected(ComponentName name) {
            BBLog.d(BBLog.TAG,"LocalService  onServiceDisconnected ->链接断开，重新启动 RemoteGuardService");
            startService(new Intent(LocalService.this,RemoteGuardService.class));
            bindService(new Intent(LocalService.this,RemoteGuardService.class),connection, Context.BIND_IMPORTANT);
        }
    };

    public LocalService() {
    }

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        BBLog.d(BBLog.TAG,"LocalService  onStartCommand ->LocalService 启动");
        ServiceApi.getIntance().startService(new Intent(LocalService.this,RemoteGuardService.class));
        bindService(new Intent(this,RemoteGuardService.class),connection, Context.BIND_IMPORTANT);
        return START_STICKY;
    }

    @Override
    public IBinder onBind(Intent intent) {
        binder = new LocalServiceBinder();
        return binder;
    }


    public void restartService(){
        Intent intentLauncher = getPackageManager().getLaunchIntentForPackage("com.bbpos.wiseapp.service");
        startActivity(intentLauncher);
        BBLog.d(BBLog.TAG, "LocalService  onServiceConnected -> restartService: 重新运行service ");
    }

    class LocalServiceBinder extends KeepAliveConnection.Stub{

        @Override
        public String getServiceName() throws RemoteException {
            return LocalService.class.getSimpleName();
        }
    }
}
