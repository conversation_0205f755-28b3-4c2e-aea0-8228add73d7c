package com.bbpos.wiseapp.provisioning;

import android.app.AlarmManager;
import android.app.PendingIntent;
import android.content.BroadcastReceiver;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.net.ConnectivityManager;
import android.os.Build;
import android.text.TextUtils;

import com.bbpos.wiseapp.logger.BBLog;
import com.bbpos.wiseapp.service.common.UsualData;
import com.bbpos.wiseapp.settings.activity.ProvisionUIActivity;
import com.bbpos.wiseapp.settings.utils.WirelessUtil;
import com.bbpos.wiseapp.system.api.ServiceApi;
import com.bbpos.wiseapp.tms.utils.AESUtil;
import com.bbpos.wiseapp.tms.utils.BroadcastActions;
import com.bbpos.wiseapp.tms.utils.Constants;
import com.bbpos.wiseapp.tms.utils.ContextUtil;
import com.bbpos.wiseapp.tms.utils.Helpers;
import com.bbpos.wiseapp.utils.SharedPreferencesUtils;

import org.json.JSONObject;

public class ProvisionTimer extends BroadcastReceiver {
    @Override
    public void onReceive(Context context, Intent intent) {
        String action = intent.getAction();
        BBLog.v(ProvisionService.TAG, "ProvisionTimer Receiver Broadcast " + action);
        if (BroadcastActions.PROVISION_TIMER_START_BC.equals(action)){
            startProvisionTimer(context, "0");
//            SharedPreferencesUtils.setSharePreferencesValue(Constants.FLAG_LAST_POLL_SUCCESS,"false");
        } else if (ConnectivityManager.CONNECTIVITY_ACTION.equals(action)){
            if (com.bbpos.wiseapp.tms.utils.Constants.IS_PROVISIONING_FROM_SCAN_QRCODE && Helpers.isOnline(context)){
                com.bbpos.wiseapp.tms.utils.Constants.IS_PROVISIONING_FROM_SCAN_QRCODE = false;
                startProvisionTimer(context, "1");
                return;
            }
            if (!com.bbpos.wiseapp.tms.utils.Constants.IS_FIRST_PROVISIONING_COMPLETED && Helpers.isOnline(context)) {
                startProvisionTimer(context, "0");
//                com.bbpos.wiseapp.tms.utils.Constants.IS_FIRST_PROVISIONING_COMPLETED = true;
            }
        } else if (BroadcastActions.SCAN_CODE.equals(action)) {
            com.bbpos.wiseapp.tms.utils.Constants.IS_PROVISIONING_FROM_SCAN_QRCODE = true;
            String result = intent.getStringExtra("result");
            BBLog.i(ProvisionService.TAG, "SCAN_CODE, call, content = " + result);
            JSONObject jsonObject = null;
            try {
                jsonObject = new JSONObject(result);
                if (jsonObject != null && jsonObject.has("CID")) {
                    final String cid = jsonObject.getString("CID");
                    final String soid = jsonObject.optString("SOID");
                    String loginToken = "";
                    if (jsonObject.has("loginToken")) {
                       loginToken = jsonObject.getString("loginToken");
                    }
                    SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_SCAN_CODE_CONTENT, intent.getStringExtra("result"));
                    SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_CLIENT_ID, cid);
                    SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_SOID, soid);

                    SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_LOGINTOKEN, loginToken);
//                    SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_OTA_TODO_FLAG, true + "");
//                    SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_INIT_CONFIG_FLAG, 2 + "");

//                    if (cid.contains("SZZZ")) {
//                        CustomConfigActivity.createLauncherShortcut(ContextUtil.getInstance());
//                    }
                }
                if (jsonObject != null && jsonObject.has("mode")) {
                    final String mode = jsonObject.getString("mode");
                    SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_MODE, mode);
//                    SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_OTA_TODO_FLAG, true + "");
//                    SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_INIT_CONFIG_FLAG, 2 + "");
                }
                //保存WIFI
                if (jsonObject != null && jsonObject.has("wifiID") && jsonObject.has("wifiPW")) {
                    final String ssid = jsonObject.getString("wifiID");
                    final String pwd = jsonObject.getString("wifiPW");
                    BBLog.i(ProvisionService.TAG, "wifiSSID = "+ssid+", wifiPWD = "+pwd);
                    BBLog.i(ProvisionService.TAG, "decryptWifiID = "+ssid+", wifiPWD = "+getWifiPwd(pwd));
                    if ((TextUtils.isEmpty(ssid) && TextUtils.isEmpty(pwd))
                      || "null".equals(ssid)
                      || "null".equals(pwd)) {
                        startProvisionTimer(context, "1");
                    } else {
                        WirelessUtil.connectWifi(context, ssid, getWifiPwd(pwd), WirelessUtil.SECURITY_WPA2_PSK);
                    }
                } else {
                    if (Helpers.isOnline(context)) {
                        startProvisionTimer(context, "1");
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    /**启动轮询定时广播*/
    public static void startProvisionTimer(Context context, String type){
//        Helpers.startProvisionService(context);
        if (false) {// !new File(FileUtils.getConfigFilePath()).exists() ) {
            Intent intent = new Intent(ContextUtil.getInstance(), ProvisionUIActivity.class);
            intent.putExtra("type", type);
            intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            context.startActivity(intent);
        }else {
            Intent provisionService = new Intent(context, ProvisionService.class);
            provisionService.putExtra("type", type);
            ServiceApi.getIntance().startService(provisionService);
        }

        BBLog.i(ProvisionService.TAG, "startProvisionTimer in " + Constants.PROVISIONING_INTERVAL() * 1000);
        Intent intentTmp = new Intent(BroadcastActions.PROVISION_TIMER_START_BC);
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            intentTmp.setComponent(new ComponentName(context.getPackageName(), ProvisionTimer.class.getName()));
        }
        PendingIntent pi = PendingIntent.getBroadcast(context, 0, intentTmp, PendingIntent.FLAG_UPDATE_CURRENT);
        AlarmManager am = (AlarmManager) context.getSystemService(Context.ALARM_SERVICE);
        am.cancel(pi);
        long timeOnMillis = System.currentTimeMillis() + (Constants.PROVISIONING_INTERVAL() * 1000);

        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.KITKAT) {
            am.set(AlarmManager.RTC_WAKEUP, timeOnMillis, pi);
        } else {
            am.setExact(AlarmManager.RTC_WAKEUP, timeOnMillis, pi);
        }
    }

    /**启动轮询定时广播*/
    public static void resetProvisionTimer(Context context){
        BBLog.i(ProvisionService.TAG, "startProvisionTimer in " + Constants.PROVISIONING_INTERVAL() * 1000);
        Intent intentTmp = new Intent(BroadcastActions.PROVISION_TIMER_START_BC);
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            intentTmp.setComponent(new ComponentName(context.getPackageName(), ProvisionTimer.class.getName()));
        }
        PendingIntent pi = PendingIntent.getBroadcast(context, 0, intentTmp, PendingIntent.FLAG_UPDATE_CURRENT);
        AlarmManager am = (AlarmManager) context.getSystemService(Context.ALARM_SERVICE);
        am.cancel(pi);
        long timeOnMillis = System.currentTimeMillis() + (Constants.PROVISIONING_INTERVAL() * 1000);

        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.KITKAT) {
            am.set(AlarmManager.RTC_WAKEUP, timeOnMillis, pi);
        } else {
            am.setExact(AlarmManager.RTC_WAKEUP, timeOnMillis, pi);
        }
    }

    private static String getWifiPwd(String orgPwd) {
        String wifiPwd = "";
        try {
//            final byte[] content = decodeHex(orgPwd.toCharArray());
//            final byte[] key = decodeHex(AESKey.toCharArray());
//            final byte[] iv = decodeHex(create256BitsIV(IV));
//            final byte[] decrypt = decrypt(content, key, iv);
//            wifiPwd = getOrgData(decrypt, decrypt.length);
            wifiPwd = AESUtil.decryptHexToString(orgPwd);
            BBLog.e(ProvisionService.TAG, "getWifiPwd: " );
        } catch (Exception e) {
            e.printStackTrace();
            wifiPwd = "";
        }
        return wifiPwd;
    }
}
