package com.bbpos.wiseapp.network;

import android.content.Context;
import android.net.ConnectivityManager;
import android.net.LinkProperties;
import android.net.Network;
import android.net.NetworkCapabilities;
import android.net.NetworkInfo;
import android.os.Build;

import com.bbpos.wiseapp.logger.BBLog;
import com.bbpos.wiseapp.service.common.UsualData;
import com.bbpos.wiseapp.tms.utils.Constants;
import com.bbpos.wiseapp.tms.utils.ContextUtil;
import com.bbpos.wiseapp.websocket.handler.ApnHandler;

public class NetworkStateCallbackImpl extends ConnectivityManager.NetworkCallback {
    private final static String TAG = "NetworkStateCallback";

    @Override
    public void onAvailable(Network network) {
        super.onAvailable(network);
        BBLog.i(TAG, "onAvailable...");
        if (NetworkStateListerner.getINetworkStateListener() != null) {
            NetworkStateListerner.getINetworkStateListener().getNetworkState(getNetType(ContextUtil.getInstance().getBaseContext()));
        }
        Constants.IS_C0904_UPLOAD_NEEDED = true;
    }

    @Override
    public void onLosing(Network network, int maxMsToLive) {
        super.onLosing(network, maxMsToLive);
        BBLog.i(TAG, "onLosing...");
    }

    @Override
    public void onLost(Network network) {
        super.onLost(network);
        BBLog.i(TAG, "onLost...");
        if (NetworkStateListerner.getINetworkStateListener() != null) {
//            NetworkStateListerner.getINetworkStateListener().getNetworkState(UsualData.NETWORK_STATUS_INAVAILABLE);
            NetworkStateListerner.getINetworkStateListener().getNetworkState(getNetType(ContextUtil.getInstance().getBaseContext()));
        }

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            ApnHandler.checkIfApnAvailable();
        }
        Constants.IS_C0904_UPLOAD_NEEDED = true;
    }

    @Override
    public void onCapabilitiesChanged(Network network, NetworkCapabilities networkCapabilities) {
        super.onCapabilitiesChanged(network, networkCapabilities);
        BBLog.i(TAG, "onCapabilitiesChanged...");
    }

    @Override
    public void onLinkPropertiesChanged(Network network, LinkProperties linkProperties) {
        super.onLinkPropertiesChanged(network, linkProperties);
        BBLog.i(TAG, "onLinkPropertiesChanged...");
    }

    private int getNetType(Context context){
        ConnectivityManager connMgr = (ConnectivityManager)context.getSystemService(Context.CONNECTIVITY_SERVICE);
        //获取当前激活的网络连接状态
        NetworkInfo networkInfo = connMgr.getActiveNetworkInfo();
        BBLog.w(TAG, "getNetType networkInfo=" + networkInfo);
        if(networkInfo == null || NetworkInfo.State.DISCONNECTED.equals(networkInfo.getState())){
            return UsualData.NETWORK_STATUS_INAVAILABLE;
        }

        int nType = networkInfo.getType();
        if(nType == ConnectivityManager.TYPE_MOBILE){
            return UsualData.NETWORK_STATUS_MOBILE;
        }else if(nType == ConnectivityManager.TYPE_WIFI){
            return UsualData.NETWORK_STATUS_WIFI;
        }else if(nType == ConnectivityManager.TYPE_ETHERNET){
            return UsualData.NETWORK_STATUS_ETHERNET;
        }
        return UsualData.NETWORK_STATUS_INAVAILABLE;
    }
}
