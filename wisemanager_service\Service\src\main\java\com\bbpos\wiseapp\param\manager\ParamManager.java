package com.bbpos.wiseapp.param.manager;

import android.annotation.SuppressLint;
import android.content.ContentResolver;
import android.content.ContentValues;
import android.content.Context;
import android.database.Cursor;
import android.net.Uri;
import android.util.Log;

import com.bbpos.wiseapp.logger.BBLog;
import com.bbpos.wiseapp.tms.utils.ContextUtil;

import java.util.ArrayList;
import java.util.List;

public class ParamManager implements IParamManager {
    private static final String contentProviderUrl = "content://com.bbpos.wiseapp.service.contentprovider.CommonDataProvider";
    private static final String paramContentProviderUrl = "content://com.bbpos.wiseapp.service.contentprovider.ParamContentProvider";
    public static final String RESOURCE_PATH_KEY = "RESOURCE_PATH_KEY";
    public static final String RESOURCE_MD5_KEY = "RESOURCE_MD5_KEY";

    public ParamManager() {
    }

    public Uri insertParams(Context context, String param_key, String param_value) {
        Uri uri = Uri.parse(contentProviderUrl);
        ContentResolver resolve = context.getContentResolver();
        ContentValues initialValues = new ContentValues();
        initialValues.put("param_key", param_key);
        initialValues.put("param_value", param_value);
        return resolve.insert(uri, initialValues);
    }

    public List<ParamModel> getParams(Context context) {
        ArrayList paramList = new ArrayList();
        Uri uri = Uri.parse(contentProviderUrl);
        ContentResolver resolve = context.getContentResolver();
        Cursor cursor = resolve.query(uri, new String[]{"param_key", "param_value"}, (String)null, (String[])null, (String)null);
        if(cursor != null && cursor.getCount() > 0) {
            cursor.moveToFirst();

            while(!cursor.isAfterLast()) {
                String key = cursor.getString(cursor.getColumnIndex("param_key"));
                String value = cursor.getString(cursor.getColumnIndex("param_value"));
                paramList.add(new ParamModel("", ContextUtil.getInstance().getPackageName(), key, value, ""));
                cursor.moveToNext();
            }
        }

        if(cursor != null) {
            cursor.close();
            cursor = null;
        }

        return paramList;
    }

    public String getStrParam(Context context, String key, String defaultStr) {
        String paramValue = defaultStr;
        Uri uri = Uri.parse(contentProviderUrl);
        ContentResolver resolve = context.getContentResolver();
        Cursor cursor = resolve.query(uri, new String[]{"param_key", "param_value"}, "param_key=\'" + key + "\'", (String[])null, (String)null);
        if(cursor != null && cursor.getCount() > 0) {
            cursor.moveToFirst();

            while(!cursor.isAfterLast()) {
                paramValue = cursor.getString(cursor.getColumnIndex("param_value"));
                cursor.moveToNext();
            }
        }

        if(cursor != null) {
            cursor.close();
            cursor = null;
        }

        return paramValue;
    }

    public void updateParam(Context context, String key, String value) {
        Uri uri = Uri.parse(contentProviderUrl);
        ContentResolver resolve = context.getContentResolver();
        ContentValues initialValues = new ContentValues();
        initialValues.put("param_key", key);
        initialValues.put("param_value", value);

        try {
            resolve.update(uri, initialValues, "param_key" + "= '"
                    + key +"'", (String[])null);
        } catch (Exception var8) {
            BBLog.e("updateParam", "e:" + var8);
        }

    }

    public void updateParam(Context context, ParamModel para) {
        this.updateParam(context, para.getKey(), para.getValue());
    }

    public void updateParamInPar(Context context, String moudleName, String fileName, String key, String value) {
        Uri uri = Uri.parse(contentProviderUrl);
        ContentResolver resolve = context.getContentResolver();
        ContentValues initialValues = new ContentValues();
        initialValues.put("param_key", key);
        initialValues.put("param_value", value);
        initialValues.put("moudleName", moudleName);
        initialValues.put("fileName", fileName);
        resolve.update(uri, initialValues, (String)null, (String[])null);
    }

    public void updateParamInPar(Context context, String moudleName, String fileName, ParamModel para) {
        this.updateParamInPar(context, moudleName, fileName, para.getKey(), para.getValue());
    }

    @SuppressLint({"UseValueOf"})
    public int getIntParam(Context context, String key, int defalueInt) {
        String strParam = this.getStrParam(context, key, (String)null);

        try {
            return strParam == null?defalueInt:(new Integer(strParam)).intValue();
        } catch (Exception var6) {
            var6.printStackTrace();
            return defalueInt;
        }
    }

    public boolean getBooleanParam(Context context, String key, boolean defaultBoolean) {
        String strParam = this.getStrParam(context, key, (String)null);
        return strParam == null?defaultBoolean:("0".equals(strParam)?false:("1".equals(strParam)?true:defaultBoolean));
    }

    public void updateParam(Context context, String key, Boolean value) {
        this.updateParam(context, key, value.booleanValue()?"1":"0");
    }

    public void updateParamInPar(Context context, String moudleName, String fileName, String key, Boolean value) {
        this.updateParamInPar(context, moudleName, fileName, key, value.booleanValue()?"1":"0");
    }

    public boolean hasParam(Context context, String key) {
        Uri uri = Uri.parse(contentProviderUrl);
        ContentResolver resolve = context.getContentResolver();
        Cursor cursor = resolve.query(uri, new String[]{"param_key", "param_value"}, "param_key=\'" + key + "\'", (String[])null, (String)null);
        boolean result;
        if(cursor != null && cursor.getCount() > 0) {
            result = true;
        } else {
            result = false;
        }

        if(cursor != null) {
            cursor.close();
            cursor = null;
        }

        return result;
    }

    @SuppressLint({"UseValueOf"})
    public long getLongParam(Context context, String key, long defaultLong) {
        String strParam = this.getStrParam(context, key, (String)null);

        try {
            return strParam == null?defaultLong:(new Long(strParam)).longValue();
        } catch (Exception var7) {
            var7.printStackTrace();
            return defaultLong;
        }
    }
}
