package com.bbpos.wiseapp.tms.utils;

public class CallType {
	/**
	*    [轮询 A0101](#轮询-A0101)
    *    [任务列表确认 A0102](#任务列表确认-A0102)
    *    [应用更新任务 A0103](#应用更新任务-A0103)
    *    [参数更新任务 A0104](#参数更新任务-A0104)
    *    [任务结果上送 0108](#任务结果上送-0108)
    *    [终端信息上送 0109](#终端信息上送-0109)
    *    [应用还原下载 0110](#应用还原下载-0110)
    *    [用户接口获取 0111](#用户接口获取-0111)
    *    [OTA更新任务  0112](#OTA更新任务-0112)
    *    [终端日志列表上送 0113](#终端日志列表上送-0113)
    *    [终端执行结果反馈(下载/上传) 0114](#终端执行结果反馈(下载/上传)-0114)
    *    [备份文件下载 0115](#备份文件下载-0115)
    *    [终端日志文件上送 0116](#终端日志上送-0116)
    *    [终端备份文件上送 0117](#终端备份文件上送-0117)
    *    [终端换机确认 0118](#终端换机确认-0118)
	 */
	public static final String POLL = "A0101";//轮询
	public static final String TASK_CONFIRM = "A0102";//任务列表确认
//	public static final String TASK_CHECK = "0103";//任务执行验证
	public static final String APK_UPDATE_TASK = "A0103";//应用更新任务;
	public static final String PARAM_UPDATE_TASK="A0104";//参数更新任务

//	public static final String RECOVERY_TASK="0107";//还原任务
	public static final String TASK_RESULT_UPLOAD="A0108";//任务结果上送
	public static final String LOGIN="A0111";//商户登录操作
	public static final String TER_INFO_UPLOAD="A0109";//终端信息上送
	public static final String OOTA_UPDATE = "A0112";
	public static final String OOTA_CHECK_UPDATE = "A0121";
	public static final String UPLOAD_LOGLIST = "A0113";
	public static final String APK_RECOVERY = "A0110";//应用还原接口\
	public static final String APK_UPDATE_COMPEL = "A0110";//应用还原接口
}
