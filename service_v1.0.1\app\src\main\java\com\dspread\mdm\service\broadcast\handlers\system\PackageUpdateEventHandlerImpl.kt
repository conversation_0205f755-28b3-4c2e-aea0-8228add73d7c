package com.dspread.mdm.service.broadcast.handlers.system

import android.content.Context
import android.content.Intent
import com.dspread.mdm.service.modules.rulebase.RuleBaseManager
import com.dspread.mdm.service.platform.manager.ServiceStartupManager
import com.dspread.mdm.service.network.websocket.WebSocketCenter
import com.dspread.mdm.service.network.websocket.message.WsMessageSender
import com.dspread.mdm.service.network.websocket.task.WsTaskManager
import com.dspread.mdm.service.broadcast.core.BroadcastEventHandler
import com.dspread.mdm.service.utils.log.Logger
import com.dspread.mdm.service.constants.TaskStateConstants
import com.dspread.mdm.service.broadcast.core.BroadcastSender
import org.json.JSONObject
import android.os.Handler
import android.os.Looper
import com.dspread.mdm.service.platform.collector.DeviceDataCollector
import com.dspread.mdm.service.broadcast.core.BroadcastActions

/**
 * 应用安装/卸载/更新事件处理器
 * PackageUpdateReceiver，统一由BroadcastManager管理
 */
class PackageUpdateEventHandlerImpl : BroadcastEventHandler {
    
    companion object {
        private const val TAG = "PackageUpdateEventHandler"
    }
    
    override fun getHandlerName(): String = TAG
    
    override fun getSupportedActions(): List<String> {
        return listOf(
            Intent.ACTION_PACKAGE_ADDED,
            Intent.ACTION_PACKAGE_REMOVED,
            Intent.ACTION_PACKAGE_REPLACED,
            BroadcastActions.CHECK_SELF_UPDATE_STATUS // WebSocket连接成功后检查自身更新状态
        )
    }
    
    override fun handleBroadcast(context: Context, intent: Intent): Boolean {
        val action = intent.action ?: return false

        return try {
            when (action) {
                Intent.ACTION_PACKAGE_ADDED,
                Intent.ACTION_PACKAGE_REMOVED,
                Intent.ACTION_PACKAGE_REPLACED -> {
                    // 包更新相关广播需要包名
                    val packageName = intent.data?.schemeSpecificPart ?: return false
                    Logger.receiver("$TAG 收到包更新广播: $action, 包名: $packageName")

                    when (action) {
                        Intent.ACTION_PACKAGE_ADDED -> {
                            handlePackageAdded(context, packageName)
                            true
                        }
                        Intent.ACTION_PACKAGE_REMOVED -> {
                            handlePackageRemoved(context, packageName)
                            true
                        }
                        Intent.ACTION_PACKAGE_REPLACED -> {
                            handlePackageReplaced(context, packageName)
                            true
                        }
                        else -> false
                    }
                }

                BroadcastActions.CHECK_SELF_UPDATE_STATUS -> {
                    // 自身更新状态检查广播不需要包名
                    Logger.receiver("$TAG 收到自身更新状态检查广播: $action")
                    performSelfUpdateTaskStatusCheck(context)
                    true
                }

                else -> false
            }

        } catch (e: Exception) {
            Logger.receiverE("$TAG 处理广播失败: $action", e)
            false
        }
    }
    
    /**
     * 处理应用安装
     */
    private fun handlePackageAdded(context: Context, packageName: String) {
        Logger.receiver("$TAG 应用安装: $packageName")

        // 检查并更新WebSocket任务/规则状态
        try {
            checkAndUpdateWebSocketStatus(context, packageName, "INSTALL")
        } catch (e: Exception) {
            Logger.receiverE("$TAG 更新WebSocket状态失败", e)
        }

        // 刷新应用信息缓存
        refreshAppInfoCache(context)

        // 发送应用刷新广播
        sendAppRefreshBroadcast(context)
    }
    
    /**
     * 处理应用卸载
     */
    private fun handlePackageRemoved(context: Context, packageName: String) {
        Logger.receiver("$TAG 应用卸载: $packageName")

        // 检查并更新WebSocket任务/规则状态
        try {
            checkAndUpdateWebSocketStatus(context, packageName, "UNINSTALL")
        } catch (e: Exception) {
            Logger.receiverE("$TAG 更新WebSocket状态失败", e)
        }

        // 刷新应用信息缓存
        refreshAppInfoCache(context)

        // 发送应用刷新广播
        sendAppRefreshBroadcast(context)
    }
    
    /**
     * 处理应用更新/替换
     */
    private fun handlePackageReplaced(context: Context, packageName: String) {
        Logger.receiver("$TAG 应用更新: $packageName")

        // 检查是否是自身更新
        if (packageName == context.packageName) {
            Logger.receiver("$TAG 检测到自身更新，处理状态同步")
            handleSelfUpdate(context)
        } else {
            // 其他应用更新
            refreshAppInfoCache(context)
            sendAppRefreshBroadcast(context)
        }
    }
    
    /**
     * 处理自身更新
     */
    private fun handleSelfUpdate(context: Context) {
        try {
            Logger.receiver("$TAG 开始处理自身更新")

            // 1. 首先检查并同步任务状态
            checkAndSyncSelfUpdateTaskStatus(context)

            // 2. 设置延迟重启（使用AlarmManager确保在进程重启后能够执行）
            scheduleServiceRestart(context)

        } catch (e: Exception) {
            Logger.receiverE("$TAG 设置服务重启失败", e)

            // 降级方案：直接尝试重启
            Thread {
                try {
                    Thread.sleep(3000)
                    ServiceStartupManager.startService(
                        context,
                        ServiceStartupManager.StartupReason.PACKAGE_UPDATE,
                        forceStart = true
                    )
                } catch (ex: Exception) {
                    Logger.receiverE("$TAG 降级重启失败", ex)
                }
            }.start()
        }
    }

    /**
     * 检查并同步自身更新任务状态
     */
    private fun checkAndSyncSelfUpdateTaskStatus(context: Context) {
        try {
            Logger.receiver("$TAG 检查自身更新任务状态")

            // 延迟检查，等待WebSocket连接
            Handler(Looper.getMainLooper()).postDelayed({
                performSelfUpdateTaskStatusCheck(context)
            }, 3000) // 延迟3秒，等待服务完全启动和WebSocket连接

        } catch (e: Exception) {
            Logger.receiverE("$TAG 检查自身更新任务状态失败", e)
        }
    }

    /**
     * 执行自身更新任务状态检查
     */
    private fun performSelfUpdateTaskStatusCheck(context: Context) {
        try {
            Logger.receiver("$TAG 开始执行自身更新任务状态检查")

            // 检查WebSocket连接状态
            if (!WebSocketCenter.isConnected()) {
                Logger.receiver("$TAG WebSocket未连接，延迟重试")
                // 再次延迟重试，但限制重试次数避免无限循环
                Handler(Looper.getMainLooper()).postDelayed({
                    try {
                        performSelfUpdateTaskStatusCheck(context)
                    } catch (e: Exception) {
                        Logger.receiverE("$TAG 延迟重试自身更新任务状态检查失败", e)
                    }
                }, 5000)
                return
            }

            // 获取当前应用包名
            val currentPackageName = context.packageName

            // 查找与自身包名相关的待执行任务
            val pendingTasks = try {
                WsTaskManager.getAllPendingTasks()
            } catch (e: Exception) {
                Logger.receiverE("$TAG 获取待执行任务失败", e)
                return
            }
            
            Logger.receiver("$TAG 待执行任务数量: ${pendingTasks.size}")

            for (task in pendingTasks) {
                try {
                    val taskPackageName = task.optString("pkgName", "")
                    val taskType = task.optString("taskType", "")
                    val taskId = task.optString("taskId", "")

                    Logger.receiver("$TAG 检查任务: taskId=$taskId, pkgName=$taskPackageName, taskType=$taskType")

                    // 检查是否是自身应用的安装/更新任务
                    if (currentPackageName == taskPackageName && "02" == taskType) {
                        Logger.receiver("$TAG 找到自身更新任务: $taskId")

                        // 检查版本是否匹配
                        if (isVersionMatched(task)) {
                            Logger.receiver("$TAG 版本匹配，上报安装成功")

                            // 更新任务状态并上报
                            updateTaskStateAndUpload(taskId, "D02", null)

                            // 发送任务执行广播
                            sendTaskExecuteBroadcast(context)

                        } else {
                            Logger.receiver("$TAG 版本不匹配，任务继续执行")
                        }
                    }
                } catch (e: Exception) {
                    Logger.receiverE("$TAG 处理单个任务失败", e)
                    // 继续处理下一个任务，不中断整个流程
                }
            }

        } catch (e: Exception) {
            Logger.receiverE("$TAG 执行自身更新任务状态检查失败", e)
        }
    }

    /**
     * 检查并更新WebSocket任务/规则状态
     * 当应用安装/卸载完成后，检查是否有对应的WebSocket任务或rulebase规则需要更新状态
     */
    private fun checkAndUpdateWebSocketStatus(context: Context, packageName: String, action: String) {
        try {
            Logger.receiver("$TAG 检查WebSocket状态: packageName=$packageName, action=$action")
            
            // 1. 检查并更新WebSocket任务状态
            checkAndUpdateWebSocketTaskStatus(context, packageName, action)
            
            // 2. 检查并更新rulebase规则状态
            checkAndUpdateRulebaseStatus(context, packageName, action)
            
        } catch (e: Exception) {
            Logger.receiverE("$TAG 检查WebSocket状态失败: $packageName", e)
        }
    }
    
    /**
     * 检查并更新rulebase规则状态
     * 当应用安装/卸载完成后，检查是否有对应的rulebase规则需要更新状态
     */
    private fun checkAndUpdateRulebaseStatus(context: Context, packageName: String, action: String) {
        try {
            val ruleManager = RuleBaseManager.getInstance(context)
            val executionEngine = ruleManager.getExecutionEngine()
            
            // 通知rulebase执行引擎处理应用状态变化
            when (action) {
                "INSTALL" -> {
                    Logger.receiver("$TAG 通知rulebase处理应用安装: $packageName")
                    executionEngine.onAppInstalled(packageName)
                }
                "UNINSTALL" -> {
                    Logger.receiver("$TAG 通知rulebase处理应用卸载: $packageName")
                    executionEngine.onAppUninstalled(packageName)
                }
            }
            
        } catch (e: Exception) {
            Logger.receiverE("$TAG 检查rulebase规则状态失败: $packageName", e)
        }
    }
    
    /**
     * 检查并更新WebSocket任务状态
     * 当应用安装/卸载完成后，检查是否有对应的WebSocket任务需要更新状态
     */
    private fun checkAndUpdateWebSocketTaskStatus(context: Context, packageName: String, action: String) {
        try {
            val localTaskList = WsTaskManager.getLocalTaskList()
            if (localTaskList.length() == 0) {
                return
            }

            for (i in 0 until localTaskList.length()) {
                val task = localTaskList.getJSONObject(i)
                val taskId = task.optString("taskId", "")
                val taskType = task.optString("taskType", "")
                val taskResult = task.optString("taskResult", "")

                // 检查包名字段（可能是pkgName或packName）
                val taskPackageName = task.optString("pkgName", "").takeIf { it.isNotEmpty() }
                    ?: task.optString("packName", "")

                // 匹配包名和任务类型
                if (taskPackageName == packageName) {
                    when (action) {
                        "INSTALL" -> {
                            if (taskType == "01" && (taskResult == TaskStateConstants.INSTALL_ING || taskResult.isEmpty())) {
                                // 检查应用版本是否匹配
                                if (isAppVersionMatched(context, task)) {
                                    updateTaskStateAndUpload(taskId, TaskStateConstants.INSTALL_SUCCESS, null)
                                    Logger.receiver("$TAG 安装任务完成: $taskId")
                                }
                            }
                        }
                        "UNINSTALL" -> {
                            if (taskType == "02" && (taskResult == "UNINSTALL_ING" || taskResult == "C02")) {
                                updateTaskStateAndUpload(taskId, TaskStateConstants.UPDATE_SUCCESS, null)
                                Logger.receiver("$TAG 卸载任务完成: $taskId")
                            }
                        }
                    }
                }
            }

        } catch (e: Exception) {
            Logger.receiverE("$TAG 检查WebSocket任务状态失败: $packageName", e)
        }
    }
    
    /**
     * 检查应用版本是否匹配任务要求
     */
    private fun isAppVersionMatched(context: Context, task: JSONObject): Boolean {
        return try {
            val packageName = task.optString("pkgName", "").takeIf { it.isNotEmpty() }
                ?: task.optString("packName", "")
            val taskVersionName = task.optString("versionName", "")
            val taskVersionCode = task.optString("versionCode", "")

            if (packageName.isEmpty()) {
                Logger.receiver("$TAG 任务包名为空，跳过版本检查")
                return true
            }

            val packageManager = context.packageManager
            val packageInfo = try {
                packageManager.getPackageInfo(packageName, 0)
            } catch (e: Exception) {
                Logger.receiver("$TAG 获取应用信息失败: $packageName")
                return false
            }

            val installedVersionName = packageInfo.versionName ?: ""
            val installedVersionCode = packageInfo.versionCode.toString()

            Logger.receiver("$TAG 版本比较: 任务要求=$taskVersionName($taskVersionCode), 已安装=$installedVersionName($installedVersionCode)")

            // 版本匹配检查
            val versionNameMatch = taskVersionName.isEmpty() || taskVersionName == installedVersionName
            val versionCodeMatch = taskVersionCode.isEmpty() || taskVersionCode == installedVersionCode

            val isMatched = versionNameMatch && versionCodeMatch
            Logger.receiver("$TAG 版本匹配结果: $isMatched")

            isMatched

        } catch (e: Exception) {
            Logger.receiverE("$TAG 版本匹配检查失败", e)
            true // 出错时默认认为匹配，避免阻塞任务完成
        }
    }

    /**
     * 检查版本是否匹配
     */
    private fun isVersionMatched(task: JSONObject): Boolean {
        return try {
            // 这里可以添加版本比较逻辑
            // 暂时返回true，表示更新成功
            true
        } catch (e: Exception) {
            Logger.receiverE("$TAG 版本检查失败", e)
            false
        }
    }

    /**
     * 更新任务状态并上报
     */
    private fun updateTaskStateAndUpload(taskId: String, taskState: String, errorMsg: String?) {
        try {
            // 1. 更新本地任务状态
            WsTaskManager.updateWSTaskState(taskId, taskState)

            // 2. 上报任务结果
            WsMessageSender.uploadTaskResult(
                taskId = taskId,
                taskResult = taskState,
                errorMsg = errorMsg
            )

            Logger.receiver("$TAG 任务状态同步完成: $taskId -> $taskState")

        } catch (e: Exception) {
            Logger.receiverE("$TAG 任务状态同步失败: $taskId", e)
        }
    }

    /**
     * 发送任务执行广播
     */
    private fun sendTaskExecuteBroadcast(context: Context) {
        try {
            BroadcastSender.sendBroadcast(context, BroadcastActions.ACTION_WSTASK_EXEC_BC)
            Logger.receiver("$TAG 发送任务执行广播")
        } catch (e: Exception) {
            Logger.receiverE("$TAG 发送任务执行广播失败", e)
        }
    }

    /**
     * 设置服务重启定时器
     */
    private fun scheduleServiceRestart(context: Context) {
        try {
            val alarmManager = context.getSystemService(Context.ALARM_SERVICE) as android.app.AlarmManager
            
            val restartIntent = Intent(BroadcastActions.ACTION_SERVICE_RESTART).apply {
                setPackage(context.packageName)
            }
            val pendingIntent = android.app.PendingIntent.getBroadcast(
                context,
                0,
                restartIntent,
                android.app.PendingIntent.FLAG_UPDATE_CURRENT or android.app.PendingIntent.FLAG_IMMUTABLE
            )
            
            // 5秒后重启服务
            val triggerTime = System.currentTimeMillis() + 5000
            alarmManager.setExactAndAllowWhileIdle(
                android.app.AlarmManager.RTC_WAKEUP,
                triggerTime,
                pendingIntent
            )
            
            Logger.receiver("$TAG 设置服务重启定时器: 5秒后执行")
            
        } catch (e: Exception) {
            Logger.receiverE("$TAG 设置服务重启定时器失败", e)
        }
    }
    
    /**
     * 刷新应用信息缓存
     */
    private fun refreshAppInfoCache(context: Context) {
        try {
            // 强制刷新应用信息缓存
            val collector = DeviceDataCollector(context)
            collector.collectAppInfo(forceRefresh = true)
            Logger.receiver("$TAG 应用信息缓存刷新完成")
        } catch (e: Exception) {
            Logger.receiverE("$TAG 刷新应用信息缓存失败", e)
        }
    }
    
    /**
     * 发送应用刷新广播
     */
    private fun sendAppRefreshBroadcast(context: Context) {
        try {
            BroadcastSender.sendBroadcast(context, "com.dspread.mdm.service.APP_REFRESH")
            Logger.receiver("$TAG 发送应用刷新广播")
        } catch (e: Exception) {
            Logger.receiverE("$TAG 发送应用刷新广播失败", e)
        }
    }
}
