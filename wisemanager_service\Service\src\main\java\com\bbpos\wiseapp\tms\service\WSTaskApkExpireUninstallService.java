package com.bbpos.wiseapp.tms.service;

import android.content.Intent;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.util.Log;

import com.bbpos.wiseapp.logger.BBLog;
import com.bbpos.wiseapp.service.WakeLockService;
import com.bbpos.wiseapp.service.common.UsualData;
import com.bbpos.wiseapp.tms.adapter.SystemManagerAdapter;
import com.bbpos.wiseapp.tms.utils.Constants;
import com.bbpos.wiseapp.tms.utils.Helpers;
import com.bbpos.wiseapp.tms.utils.ParameterName;
import com.bbpos.wiseapp.tms.utils.SPKeys;
import com.bbpos.wiseapp.tms.utils.TaskState;
import com.bbpos.wiseapp.tms.utils.WebSocketTaskListManager;
import com.bbpos.wiseapp.websocket.WebSocketSender;

import java.util.Iterator;
import java.util.List;

public class WSTaskApkExpireUninstallService extends WakeLockService {
	private static final String TAG = "AppPlusExpire";
	public WSTaskApkExpireUninstallService() {
		super("WSTaskApkExpireUninstallService");
	}

	@Override
	public void onDestroy() {
		super.onDestroy();
	}
	@Override
	protected void onHandleIntent(Intent intent) {
		String pkgName = intent.getExtras().getString(ParameterName.pkgName);
		final String taskId = intent.getExtras().getString(ParameterName.taskId);
		uninstall(taskId,pkgName);
	}

	private void uninstall(final String taskId, final String pkgName) {
		if (!isAppInstalled(pkgName)){
			BBLog.i(Constants.TAG, pkgName+" do not exist.");
			return;
		}
		
		SystemManagerAdapter.unInstallApk(getApplicationContext(), pkgName, new SystemManagerAdapter.ApkUnInstallCompleted() {
			@Override
			public void onDeleteFinished(int returnCode) {
				BBLog.i(TAG, "packageDeleted " + pkgName + " returnCode " + returnCode);
	            if (returnCode == 1) {
					BBLog.i(TAG, "delete expired APP+ app " + pkgName + " returnCode " + returnCode);
					Helpers.updateWSTaskStateAndUpload(WSTaskApkExpireUninstallService.this, taskId, TaskState.UNINSTALL_EXPIRE, "");
					WebSocketTaskListManager.removeWSTaskJsonObjById(SPKeys.WEBSOCKET_TASK_LIST, taskId);
				}
			}
		});
	}

    /**
     * 判断应用是否安装 
     * @param pkgName 包名
     * */
    @SuppressWarnings("rawtypes")
	private boolean isAppInstalled(String pkgName){
    	PackageManager pm = this.getPackageManager();
		List installedPackages = pm.getInstalledPackages(0);
		Iterator localIterator = installedPackages.iterator();
		while (localIterator.hasNext()) {
			PackageInfo packageInfo = (PackageInfo) localIterator.next();
			if (packageInfo.applicationInfo.packageName.equals(pkgName)) {
				return true;
			}
		}
		return false;
    }
    
}
