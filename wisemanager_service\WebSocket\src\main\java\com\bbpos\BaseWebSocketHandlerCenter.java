package com.bbpos;

import android.content.Context;
import android.util.Log;

import com.bbpos.wiseapp.EncrypUtil;
import com.bbpos.wiseapp.websocket.ErrorResponse;
import com.bbpos.wiseapp.websocket.IWebSocketPage;
import com.bbpos.wiseapp.websocket.Response;

public class BaseWebSocketHandlerCenter implements IWebSocketPage {
    public static final String TAG = "WiseApp2.0";
    protected Context context;

    public static boolean m_need_rsa = true;
    public static String m_public_key = "";
    public static String m_private_key = "";
    public static String m_server_public_key = "";
    public static String url = "";
//    public static String url = "wss://wiseapp-us.dev.wisemanager.com/status/websocket/register";


    protected static onMessageRecvListener mListener;

    public interface onMessageRecvListener {
        public void onMessageRecv(String respone);
        public void onDisconnected();
    }

    public BaseWebSocketHandlerCenter(Context context){
        this.context = context;
    }

    @Override
    public void onServiceBindSuccess() {

    }

    @Override
    public void sendText(String text) {
//        Log.e(TAG, "WebSocket: 数据发送：" + text);
//        try {
//            if (m_need_rsa) {
//                sendText(EncrypUtil.encode(EncrypUtil.encryptByPublicKey(text.getBytes(), m_server_public_key)));
//            } else {
//                sendText(text);
//            }
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
    }

    @Override
    public void reconnect() {

    }

    @Override
    public void disconnect() {

    }

    @Override
    public void onConnected() {

    }

    @Override
    public void onConnectError(Throwable cause) {

    }

    @Override
    public void onDisconnected() {
        if (mListener!=null)
            mListener.onDisconnected();
    }

    @Override
    public void onMessageResponse(Response message) {
//        String respone = "";
//        try {
//            if (m_need_rsa) {
//                respone = new String(EncrypUtil.decryptByPrivateKey(EncrypUtil.decode(message.getResponseText()), m_private_key));
//            } else {
//                respone = message.getResponseText();
//            }
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//        Log.e(TAG, "onMessageResponse: " + respone);
//
//        if (mListener!=null) {
//            mListener.onMessageRecv(respone);
//        }
    }

    @Override
    public void onSendMessageError(ErrorResponse error) {

    }
}
