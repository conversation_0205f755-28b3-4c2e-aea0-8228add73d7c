package com.dspread.mdm.service.network.websocket.message.strategy

import android.content.Context
import com.dspread.mdm.service.modules.provisioning.ProvisioningManager
import com.dspread.mdm.service.utils.log.Logger

/**
 * 数据收集策略管理器
 * 协调数据收集和上送策略，确保在不同uploadMode下采用合适的数据收集频率
 */
object DataCollectionStrategy {
    
    /**
     * 数据收集等级
     */
    enum class CollectionLevel(val mode: String, val description: String) {
        REALTIME("2", "实时收集"),
        BALANCED("1", "平衡收集"),
        POWER_SAVING("0", "省电收集")
    }
    
    /**
     * 数据类型的收集策略配置
     */
    data class CollectionConfig(
        val realtimeInterval: Long,      // 实时模式收集间隔
        val balancedInterval: Long,      // 平衡模式收集间隔  
        val powerSavingInterval: Long,   // 省电模式收集间隔
        val cacheValidDuration: Long,    // 缓存有效期
        val forceRefreshTriggers: List<String>, // 强制刷新的触发条件
        val description: String
    )
    
    /**
     * 各种数据类型的收集策略配置
     */
    private val collectionConfigMap = mapOf(
        "battery" to CollectionConfig(
            realtimeInterval = 10 * 1000L,      // 10秒
            balancedInterval = 30 * 1000L,      // 30秒
            powerSavingInterval = 2 * 60 * 1000L, // 2分钟
            cacheValidDuration = 30 * 1000L,    // 30秒缓存
            forceRefreshTriggers = listOf(
                UploadTriggers.BATTERY_LOW_CRITICAL,
                UploadTriggers.CHARGING_STATE_CHANGE,
                UploadTriggers.TEMPERATURE_ABNORMAL
            ),
            description = "电池状态收集"
        ),
        
        "app_info" to CollectionConfig(
            realtimeInterval = 30 * 1000L,      // 30秒
            balancedInterval = 5 * 60 * 1000L,  // 5分钟
            powerSavingInterval = 15 * 60 * 1000L, // 15分钟
            cacheValidDuration = 5 * 60 * 1000L, // 5分钟缓存
            forceRefreshTriggers = listOf(
                UploadTriggers.APP_INSTALL,
                UploadTriggers.APP_UNINSTALL,
                UploadTriggers.SERVICE_CHANGE
            ),
            description = "应用信息收集"
        ),
        
        "hardware_info" to CollectionConfig(
            realtimeInterval = 60 * 1000L,      // 1分钟
            balancedInterval = 10 * 60 * 1000L, // 10分钟
            powerSavingInterval = 30 * 60 * 1000L, // 30分钟
            cacheValidDuration = 10 * 60 * 1000L, // 10分钟缓存
            forceRefreshTriggers = listOf(
                UploadTriggers.HARDWARE_CHANGE
            ),
            description = "硬件信息收集"
        ),
        
        "location_info" to CollectionConfig(
            realtimeInterval = 30 * 1000L,      // 30秒
            balancedInterval = 2 * 60 * 1000L,  // 2分钟
            powerSavingInterval = 10 * 60 * 1000L, // 10分钟
            cacheValidDuration = 2 * 60 * 1000L, // 2分钟缓存
            forceRefreshTriggers = listOf(
                UploadTriggers.LOCATION_GEOFENCE_CHANGE,
                UploadTriggers.LOCATION_CHANGE
            ),
            description = "位置信息收集"
        ),
        
        "network_info" to CollectionConfig(
            realtimeInterval = 15 * 1000L,      // 15秒
            balancedInterval = 60 * 1000L,      // 1分钟
            powerSavingInterval = 5 * 60 * 1000L, // 5分钟
            cacheValidDuration = 60 * 1000L,    // 1分钟缓存
            forceRefreshTriggers = listOf(
                UploadTriggers.NETWORK_CONNECT,
                UploadTriggers.NETWORK_DISCONNECT,
                UploadTriggers.WIFI_SWITCH
            ),
            description = "网络信息收集"
        )
    )
    
    /**
     * 数据收集决策结果
     */
    data class CollectionDecision(
        val shouldCollect: Boolean,           // 是否应该收集
        val useCache: Boolean,               // 是否使用缓存
        val forceRefresh: Boolean,           // 是否强制刷新
        val level: CollectionLevel,          // 收集等级
        val reason: String,                  // 决策原因
        val nextCollectionTime: Long = 0L    // 下次收集时间
    )
    
    /**
     * 获取当前的数据收集等级
     */
    fun getCurrentCollectionLevel(context: Context): CollectionLevel {
        return try {
            val provisioningManager = ProvisioningManager.getInstance(context)
            val config = provisioningManager.getCurrentConfig()
            val uploadMode = config?.polling?.uploadMode ?: "1"
            
            when (uploadMode) {
                "0" -> CollectionLevel.POWER_SAVING
                "1" -> CollectionLevel.BALANCED
                "2" -> CollectionLevel.REALTIME
                else -> CollectionLevel.BALANCED
            }
        } catch (e: Exception) {
            Logger.wsmE("获取数据收集等级失败，使用默认平衡模式", e)
            CollectionLevel.BALANCED
        }
    }
    
    /**
     * 判断是否应该收集数据
     * 
     * @param context 上下文
     * @param dataType 数据类型（如"battery", "app_info"）
     * @param trigger 触发条件
     * @param lastCollectionTime 上次收集时间
     * @param hasCachedData 是否有缓存数据
     * @param cacheAge 缓存年龄（毫秒）
     * @return 收集决策结果
     */
    fun shouldCollectData(
        context: Context,
        dataType: String,
        trigger: String,
        lastCollectionTime: Long = 0L,
        hasCachedData: Boolean = false,
        cacheAge: Long = 0L
    ): CollectionDecision {
        
        val currentLevel = getCurrentCollectionLevel(context)
        val config = collectionConfigMap[dataType]
        val currentTime = System.currentTimeMillis()
        
        if (config == null) {
            Logger.wsm("未知数据类型: $dataType，默认收集")
            return CollectionDecision(true, false, false, currentLevel, "未知数据类型")
        }
        
        Logger.wsm("$dataType 收集判断: mode=${currentLevel.mode}, trigger=$trigger")
        
        // 检查是否需要强制刷新
        if (trigger in config.forceRefreshTriggers) {
            Logger.wsm("🔥 $dataType 强制刷新: $trigger")
            return CollectionDecision(true, false, true, currentLevel, "强制刷新: $trigger")
        }
        
        // 检查缓存是否有效
        if (hasCachedData && cacheAge < config.cacheValidDuration) {
            Logger.wsm("$dataType 使用有效缓存 (年龄: ${cacheAge}ms)")
            return CollectionDecision(false, true, false, currentLevel, "使用有效缓存")
        }
        
        // 根据当前等级确定收集间隔
        val collectionInterval = when (currentLevel) {
            CollectionLevel.REALTIME -> config.realtimeInterval
            CollectionLevel.BALANCED -> config.balancedInterval
            CollectionLevel.POWER_SAVING -> config.powerSavingInterval
        }
        
        // 检查是否到了收集时间
        if (currentTime - lastCollectionTime >= collectionInterval) {
            Logger.wsm("$dataType ${currentLevel.description}收集时间到")
            return CollectionDecision(
                shouldCollect = true,
                useCache = false,
                forceRefresh = false,
                level = currentLevel,
                reason = "${currentLevel.description}定时收集",
                nextCollectionTime = currentTime + collectionInterval
            )
        }
        
        // 不满足收集条件，使用缓存或跳过
        if (hasCachedData) {
            Logger.wsm("$dataType 使用过期缓存 (年龄: ${cacheAge}ms)")
            return CollectionDecision(false, true, false, currentLevel, "使用过期缓存")
        } else {
            Logger.wsm("$dataType 无缓存且未到收集时间")
            return CollectionDecision(true, false, false, currentLevel, "无缓存强制收集")
        }
    }
    
    /**
     * 获取数据类型的配置信息
     */
    fun getCollectionConfig(dataType: String): CollectionConfig? {
        return collectionConfigMap[dataType]
    }
    
    /**
     * 获取所有支持的数据类型
     */
    fun getSupportedDataTypes(): Set<String> {
        return collectionConfigMap.keys
    }
    
    /**
     * 获取收集策略统计信息
     */
    fun getCollectionStats(): String {
        return buildString {
            appendLine("数据收集策略统计:")
            collectionConfigMap.forEach { (type, config) ->
                appendLine("  $type - ${config.description}")
                appendLine("    实时间隔: ${config.realtimeInterval / 1000}秒")
                appendLine("    平衡间隔: ${config.balancedInterval / 1000}秒")
                appendLine("    省电间隔: ${config.powerSavingInterval / 1000}秒")
                appendLine("    缓存有效期: ${config.cacheValidDuration / 1000}秒")
            }
        }
    }
}
