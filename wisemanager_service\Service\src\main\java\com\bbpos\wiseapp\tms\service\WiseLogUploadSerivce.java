package com.bbpos.wiseapp.tms.service;

import android.annotation.SuppressLint;
import android.content.Intent;
import android.os.Build;

import com.bbpos.wiseapp.logger.BBLog;
import com.bbpos.wiseapp.logger.LoggerConfig;
import com.bbpos.wiseapp.logger.MyCsvFormatStrategy;
import com.bbpos.wiseapp.logger.MyDiskLogStrategy;
import com.bbpos.wiseapp.service.WakeLockService;
import com.bbpos.wiseapp.service.common.UsualData;
import com.bbpos.wiseapp.system.api.DeviceInfoApi;
import com.bbpos.wiseapp.system.api.SystemApi;
import com.bbpos.wiseapp.tms.listener.device.HardwareInfo;
import com.bbpos.wiseapp.tms.utils.Constants;
import com.bbpos.wiseapp.tms.utils.ContextUtil;
import com.bbpos.wiseapp.utils.FileUtils;
import com.bbpos.wiseapp.utils.HttpUtils;
import com.bbpos.wiseapp.utils.SharedPreferencesUtils;
import com.bbpos.wiseapp.utils.SystemUtils;
import com.bbpos.wiseapp.websocket.WebSocketSender;
import com.bbpos.wiseapp.zip.GzipJava;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Locale;

@SuppressLint("SimpleDateFormat")
public class WiseLogUploadSerivce extends WakeLockService {
	public WiseLogUploadSerivce() {
		super("WiseLogUploadSerivce");
	}

	@Override
	public void onDestroy() {
		super.onDestroy();
	}

	@Override
	protected void onHandleIntent(Intent intent) {
        BBLog.e(BBLog.TAG, "开始检查上送: ");
        try {
            File folder = new File(LoggerConfig.logFolder + File.separator);
            if (!folder.exists()) {
                return;
            }

            int length = folder.listFiles().length;
            BBLog.e(BBLog.TAG, "Logger 开始检查上送，当前Log条数：" + length);
            if (length == 0) {
                return;
            }

            SimpleDateFormat sdft_short = new SimpleDateFormat("yyyyMMdd_HHmmss");//日志名称格式
            List<File> list = new ArrayList<>();
            for(File file:folder.listFiles()){
                try {
//          BBLog.e(BBLog.TAG, "uploadLoggerFile 找到文件: " + MyDiskLogStrategy.WriteHandler.getFileNameWithoutExtension(file.getName()));
                    sdft_short.setLenient(false);
                    sdft_short.parse(MyDiskLogStrategy.WriteHandler.getFileNameWithoutExtension(file.getName()));   //如果时间格式正确就是日志文件
                    if (file.getName().endsWith(".log")) {
                        list.add(file);
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                    BBLog.e(BBLog.TAG, MyDiskLogStrategy.WriteHandler.getFileNameWithoutExtension(file.getName()) + "解析错误");
                    file.delete();
                }
            }
            Collections.sort(list, new Comparator<File>() {
                @Override
                public int compare(File file1, File file2) {
                    String createInfo1 = MyDiskLogStrategy.WriteHandler.getFileNameWithoutExtension(file1.getName());
                    String createInfo2 = MyDiskLogStrategy.WriteHandler.getFileNameWithoutExtension(file2.getName());
                    try {
                        Date create1 = sdft_short.parse(createInfo1);
                        Date create2 = sdft_short.parse(createInfo2);
                        if(create1.before(create2)){//按升序排列
                            return -1;
                        }else{
                            return 1;
                        }
                    } catch (ParseException e) {
                        return 0;
                    }
                }

                @Override
                public boolean equals(Object obj) {
                    return false;
                }
            });

            File fileUpload = list.get(0);
            //只有一个文件的时候，是当前在写入的文件，此时必须满足500KB以上大小，才能上传
            if (list.size() == 1) {
                if (fileUpload.length() < MyCsvFormatStrategy.Builder.MAX_BYTES) {
                    return;
                }
            } else {
                //如果多条，如果有存在文件时间晚于当前的时间的，先上送最晚的
                String createInfo = MyDiskLogStrategy.WriteHandler.getFileNameWithoutExtension(list.get(list.size() - 1).getName());
                try {
                    Date createDate = sdft_short.parse(createInfo);
                    if (createDate.after(new Date())) {
                        fileUpload = list.get(list.size() - 1);
                    }
                } catch (ParseException e) {
                    e.printStackTrace();
                }
            }
            BBLog.e(BBLog.TAG, "uploadLoggerFile 找到最早的要上送的文件: " + fileUpload.getName());
            String filePath = fileUpload.getAbsolutePath();
            if (fileUpload.exists()) {
                File finalFileUpload = fileUpload;
                new Thread(new Runnable() {
                    @Override
                    public void run() {
                        try {
                            Long dirSize = FileUtils.getTotalSizeOfFilesInDir(folder);
                            BBLog.e(BBLog.TAG, "Logger  當前Log文件夾总大小： "+ dirSize );
                            if (dirSize >= Constants.WISEAPP_LOG_FILE_MAX_SIZE){
                                BBLog.i(BBLog.TAG, "Logger  當前Log文件夾大小超出3G,清空log文件夹 ");
                                SystemApi.execCommand("rm -rf /sdcard/wiseapp/log/");
                                return;
                            }

                            String gzName = filePath.substring(0, filePath.lastIndexOf(".")) + ".gz";
                            File fileZip = new File(gzName);
                            BBLog.i(BBLog.TAG, "WiseApp 日志壓縮成文件 " + gzName);
                            GzipJava.compressGZIP(finalFileUpload, fileZip);

                            for (int i=0; i<1; i++) {
                                String mode = SharedPreferencesUtils.getSharePreferencesValue(UsualData.SHARED_PREFERENCES_MODE_IN_CONFIG, "uat");
                                String sn = DeviceInfoApi.getIntance().getSerialNumber();
                                if (MyDiskLogStrategy.b_enable_upload_log && HttpUtils.urlUploadFile(HttpUtils.S3_LOG_STREAMING_URL, HttpUtils.S3_LOG_STREAMING_BUCKET_NAME, "wiseapp/" + mode + "/" + sn, fileZip)) {
                                    if (fileZip != null) {
                                        fileZip.delete();
                                    }
                                    if (finalFileUpload != null) {
                                        finalFileUpload.delete();
                                    }
                                    BBLog.w(BBLog.TAG, "Logger " + finalFileUpload.getAbsolutePath() + " 上送成功");
                                    break;
                                } else {
                                    BBLog.w(BBLog.TAG, "Logger " + finalFileUpload.getAbsolutePath() + " 上送失败");
                                    if (dirSize > MyCsvFormatStrategy.Builder.MAX_BYTES * 256 ){
                                        BBLog.e(BBLog.TAG, "Logger " + folder.getAbsolutePath() + " 占用空间: "+dirSize+ " ,大于128M，需清理最早日志文件 ");
                                        if (fileZip != null) {
                                            fileZip.delete();
                                            BBLog.w(BBLog.TAG, "Logger delete file ---> " + fileZip.getAbsolutePath());
                                        }
                                        if (finalFileUpload != null) {
                                            finalFileUpload.delete();
                                            BBLog.w(BBLog.TAG, "Logger delete file ---> " + finalFileUpload.getAbsolutePath());
                                        }
                                    }
                                }
                            }

                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }
                }).start();
            } else {
                BBLog.w(BBLog.TAG, "Logger " + fileUpload.getAbsolutePath() + "文件不存在");
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
	}
}
