package com.dspread.mdm.service.modules.apn

import android.annotation.SuppressLint
import android.content.ContentResolver
import android.content.ContentValues
import android.content.Context
import android.database.Cursor
import android.net.Uri
import android.os.Build
import android.provider.Telephony
import com.dspread.mdm.service.modules.apn.model.ApnConfig
import com.dspread.mdm.service.modules.apn.model.ApnOperationResult
import com.dspread.mdm.service.modules.apn.model.ApnValidationResult
import com.dspread.mdm.service.utils.log.Logger
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.net.HttpURLConnection
import java.net.URL

/**
 * APN配置管理器
 * 负责APN配置的写入、读取、验证和管理
 */
class ApnConfigManager(
    private val context: Context
) {
    
    companion object {
        private const val TAG = "[ApnConfigManager]"
        private const val APN_URI = "content://telephony/carriers"
        private const val PREFERRED_APN_URI = "content://telephony/carriers/preferapn"
        private const val VALIDATION_TIMEOUT = 30000 // 30秒验证超时
        private const val TEST_URL_DEFAULT = "http://www.google.com/generate_204"



        // APN数据库查询投影数组
        private val PROJECTION = arrayOf(
            Telephony.Carriers._ID,                 // 0 唯一識別符
            Telephony.Carriers.NAME,                // 1 運營商的名稱
            Telephony.Carriers.APN,                 // 2 訪問點名稱
            Telephony.Carriers.PROXY,               // 3 代理地址
            Telephony.Carriers.PORT,                // 4 端口號
            Telephony.Carriers.MMSPROXY,            // 5 MMS代理地址
            Telephony.Carriers.MMSPORT,             // 6 MMS端口號
            Telephony.Carriers.SERVER,              // 7 服務器地址
            Telephony.Carriers.USER,                // 8 用戶名
            Telephony.Carriers.PASSWORD,            // 9 密碼
            Telephony.Carriers.MMSC,                // 10 MMS服務中心地址
            Telephony.Carriers.MCC,                 // 11 移動國家代碼
            Telephony.Carriers.MNC,                 // 12 移動網路代碼
            Telephony.Carriers.NUMERIC,             // 13 運營商的數字代碼 MCC+MNC
            Telephony.Carriers.AUTH_TYPE,           // 14 身份驗證類型
            Telephony.Carriers.TYPE,                // 15 連接類型
            Telephony.Carriers.PROTOCOL,            // 16 連接協議
            Telephony.Carriers.ROAMING_PROTOCOL,    // 17 漫遊時的連接協議
            Telephony.Carriers.CURRENT,             // 18 是否為當前選中的設置
            Telephony.Carriers.CARRIER_ENABLED,     // 19 是否啟用此運營商設置
            Telephony.Carriers.BEARER,              // 20 網絡連接類型
            Telephony.Carriers.MVNO_TYPE,           // 21 虛擬網絡運營商類型
            Telephony.Carriers.MVNO_MATCH_DATA,     // 22 虛擬網絡運營商匹配數據
            "bearer_bitmask"                        // 23 网络连接类型掩码
        )

        // 数据库字段索引常量
        private const val ID_INDEX = 0
        private const val NAME_INDEX = 1
        private const val APN_INDEX = 2
        private const val PROXY_INDEX = 3
        private const val PROXY_PORT_INDEX = 4
        private const val MMS_PROXY_INDEX = 5
        private const val MMS_PORT_INDEX = 6
        private const val SERVER_INDEX = 7
        private const val USER_INDEX = 8
        private const val PASSWORD_INDEX = 9
        private const val MMSC_INDEX = 10
        private const val MCC_INDEX = 11
        private const val MNC_INDEX = 12
        private const val NUMERIC_INDEX = 13
        private const val AUTH_TYPE_INDEX = 14
        private const val TYPE_INDEX = 15
        private const val PROTOCOL_INDEX = 16
        private const val ROAMING_PROTOCOL_INDEX = 17
        private const val CURRENT_INDEX = 18
        private const val CARRIER_ENABLED_INDEX = 19
        private const val BEARER_INDEX = 20
        private const val MVNO_TYPE_INDEX = 21
        private const val MVNO_MATCH_DATA_INDEX = 22
        private const val BEARER_BITMASK_INDEX = 23
    }
    
    private val contentResolver: ContentResolver = context.contentResolver
    
    /**
     * 添加APN配置
     */
    suspend fun addApnConfig(config: ApnConfig): Result<ApnOperationResult> {
        return withContext(Dispatchers.IO) {
            try {
                Logger.apnI("$TAG 添加APN配置: ${config.name}")

                if (!config.isValid()) {
                    return@withContext Result.failure(IllegalArgumentException("Invalid APN config"))
                }

                val values = createContentValues(config)
                val uri = Uri.parse(APN_URI)

                // 系统应用直接使用标准方式插入
                val insertedUri = contentResolver.insert(uri, values)
                
                if (insertedUri != null) {
                    val apnId = insertedUri.lastPathSegment?.toLongOrNull() ?: -1
                    
                    Logger.apnI("$TAG APN配置添加成功: ID=$apnId")
                    
                    val result = ApnOperationResult(
                        success = true,
                        apnId = apnId,
                        message = "APN配置添加成功",
                        affectedRows = 1
                    )
                    
                    Result.success(result)
                } else {
                    Logger.apnE("$TAG APN配置添加失败")
                    
                    val result = ApnOperationResult(
                        success = false,
                        message = "APN配置添加失败",
                        errorCode = -1
                    )
                    
                    Result.failure(Exception("Failed to insert APN config"))
                }
                
            } catch (e: Exception) {
                Logger.apnE("$TAG 添加APN配置异常", e)
                
                val result = ApnOperationResult(
                    success = false,
                    message = "APN配置添加异常: ${e.message}",
                    errorCode = -2
                )
                
                Result.failure(e)
            }
        }
    }



    /**
     * 更新APN配置
     */
    suspend fun updateApnConfig(apnId: Long, config: ApnConfig): Result<ApnOperationResult> {
        return withContext(Dispatchers.IO) {
            try {
                Logger.apnI("$TAG 更新APN配置: ID=$apnId, Name=${config.name}")
                
                if (!config.isValid()) {
                    return@withContext Result.failure(IllegalArgumentException("Invalid APN config"))
                }
                
                val values = createContentValues(config)
                val uri = Uri.parse("$APN_URI/$apnId")
                
                val updatedRows = contentResolver.update(uri, values, null, null)
                
                if (updatedRows > 0) {
                    Logger.apnI("$TAG APN配置更新成功: ID=$apnId, 影响行数=$updatedRows")
                    
                    val result = ApnOperationResult(
                        success = true,
                        apnId = apnId,
                        message = "APN配置更新成功",
                        affectedRows = updatedRows
                    )
                    
                    Result.success(result)
                } else {
                    Logger.apnW("$TAG APN配置更新失败: ID=$apnId")
                    
                    val result = ApnOperationResult(
                        success = false,
                        apnId = apnId,
                        message = "APN配置更新失败，可能不存在",
                        errorCode = -3
                    )
                    
                    Result.failure(Exception("Failed to update APN config"))
                }
                
            } catch (e: Exception) {
                Logger.apnE("$TAG 更新APN配置异常", e)
                
                val result = ApnOperationResult(
                    success = false,
                    apnId = apnId,
                    message = "APN配置更新异常: ${e.message}",
                    errorCode = -4
                )
                
                Result.failure(e)
            }
        }
    }
    
    /**
     * 删除APN配置
     */
    suspend fun deleteApnConfig(apnId: Long): Result<ApnOperationResult> {
        return withContext(Dispatchers.IO) {
            try {
                Logger.apnI("$TAG 删除APN配置: ID=$apnId")
                
                val uri = Uri.parse("$APN_URI/$apnId")
                val deletedRows = contentResolver.delete(uri, null, null)
                
                if (deletedRows > 0) {
                    Logger.apnI("$TAG APN配置删除成功: ID=$apnId, 影响行数=$deletedRows")
                    
                    val result = ApnOperationResult(
                        success = true,
                        apnId = apnId,
                        message = "APN配置删除成功",
                        affectedRows = deletedRows
                    )
                    
                    Result.success(result)
                } else {
                    Logger.apnW("$TAG APN配置删除失败: ID=$apnId")
                    Result.failure(Exception("Failed to delete APN config"))
                }
                
            } catch (e: Exception) {
                Logger.apnE("$TAG 删除APN配置异常", e)
                Result.failure(e)
            }
        }
    }
    
    /**
     * 获取所有APN配置
     */
    suspend fun getAllApnConfigs(): Result<List<ApnConfig>> {
        return withContext(Dispatchers.IO) {
            try {
                Logger.apnI("$TAG 获取所有APN配置")
                
                val apnConfigs = mutableListOf<ApnConfig>()
                val uri = Uri.parse(APN_URI)
                
                val cursor = contentResolver.query(uri, PROJECTION, null, null, null)
                
                cursor?.use { c ->
                    while (c.moveToNext()) {
                        try {
                            val config = parseApnConfigFromCursor(c)
                            if (config != null) {
                                apnConfigs.add(config)
                            }
                        } catch (e: Exception) {
                            Logger.apnE("$TAG 解析APN配置失败", e)
                        }
                    }
                }
                
                Logger.apnI("$TAG 获取到 ${apnConfigs.size} 个APN配置")
                Result.success(apnConfigs)
                
            } catch (e: Exception) {
                Logger.apnE("$TAG 获取APN配置异常", e)
                Result.failure(e)
            }
        }
    }
    
    /**
     * 获取当前默认APN配置
     */
    suspend fun getCurrentApnConfigs(): Result<List<ApnConfig>> {
        return withContext(Dispatchers.IO) {
            try {
                Logger.apnI("$TAG 获取当前默认APN配置")
                
                val apnConfigs = mutableListOf<ApnConfig>()
                val uri = Uri.parse(PREFERRED_APN_URI)
                
                val cursor = contentResolver.query(uri, PROJECTION, null, null, null)
                
                cursor?.use { c ->
                    while (c.moveToNext()) {
                        try {
                            val config = parseApnConfigFromCursor(c)
                            if (config != null) {
                                apnConfigs.add(config)
                            }
                        } catch (e: Exception) {
                            Logger.apnE("$TAG 解析当前APN配置失败", e)
                        }
                    }
                }
                
                Logger.apnI("$TAG 获取到 ${apnConfigs.size} 个当前APN配置")
                Result.success(apnConfigs)
                
            } catch (e: Exception) {
                Logger.apnE("$TAG 获取当前APN配置异常", e)
                Result.failure(e)
            }
        }
    }
    
    /**
     * 设置默认APN
     */
    suspend fun setDefaultApn(apnId: Long, simSlot: Int): Result<ApnOperationResult> {
        return withContext(Dispatchers.IO) {
            try {
                Logger.apnI("$TAG 设置默认APN: ID=$apnId, SIM槽位=$simSlot")
                
                val values = ContentValues().apply {
                    put("apn_id", apnId)
                }
                
                val uri = Uri.parse(PREFERRED_APN_URI)
                
                // 先清除现有的默认APN
                contentResolver.delete(uri, null, null)
                
                // 设置新的默认APN
                val insertedUri = contentResolver.insert(uri, values)
                
                if (insertedUri != null) {
                    Logger.apnI("$TAG 默认APN设置成功: ID=$apnId")
                    
                    val result = ApnOperationResult(
                        success = true,
                        apnId = apnId,
                        message = "默认APN设置成功",
                        affectedRows = 1
                    )
                    
                    Result.success(result)
                } else {
                    Logger.apnE("$TAG 默认APN设置失败: ID=$apnId")
                    
                    val result = ApnOperationResult(
                        success = false,
                        apnId = apnId,
                        message = "默认APN设置失败",
                        errorCode = -7
                    )
                    
                    Result.failure(Exception("Failed to set default APN"))
                }
                
            } catch (e: Exception) {
                Logger.apnE("$TAG 设置默认APN异常", e)
                
                val result = ApnOperationResult(
                    success = false,
                    apnId = apnId,
                    message = "设置默认APN异常: ${e.message}",
                    errorCode = -8
                )
                
                Result.failure(e)
            }
        }
    }
    
    /**
     * 验证APN连接
     */
    suspend fun validateApnConnection(config: ApnConfig, testUrl: String = TEST_URL_DEFAULT, timeout: Int = VALIDATION_TIMEOUT): Result<ApnValidationResult> {
        return withContext(Dispatchers.IO) {
            try {
                Logger.apnI("$TAG 验证APN连接: ${config.name}")
                
                val startTime = System.currentTimeMillis()
                
                // 创建HTTP连接测试
                val url = URL(testUrl)
                val connection = url.openConnection() as HttpURLConnection
                
                connection.apply {
                    connectTimeout = timeout
                    readTimeout = timeout
                    requestMethod = "GET"
                    instanceFollowRedirects = false
                    useCaches = false
                }
                
                val responseCode = connection.responseCode
                val responseTime = System.currentTimeMillis() - startTime
                
                connection.disconnect()
                
                val isValid = responseCode in 200..299 || responseCode == 204
                val canConnect = responseCode != -1
                
                val result = ApnValidationResult(
                    isValid = isValid,
                    canConnect = canConnect,
                    responseTime = responseTime,
                    testUrl = testUrl,
                    errorMessage = if (!isValid) "HTTP $responseCode" else ""
                )
                
                Logger.apnI("$TAG APN验证完成: ${config.name}, 有效=$isValid, 响应时间=${responseTime}ms")
                Result.success(result)
                
            } catch (e: Exception) {
                Logger.apnE("$TAG APN验证失败: ${config.name}", e)
                
                val result = ApnValidationResult(
                    isValid = false,
                    canConnect = false,
                    errorMessage = e.message ?: "Unknown error",
                    testUrl = testUrl
                )
                
                Result.success(result) // 返回验证结果而不是异常
            }
        }
    }
    
    /**
     * 重置APN配置
     */
    suspend fun resetApnConfigs(simSlot: Int = -1): Result<ApnOperationResult> {
        return withContext(Dispatchers.IO) {
            try {
                Logger.apnI("$TAG 重置APN配置: SIM槽位=$simSlot")
                
                val uri = Uri.parse(APN_URI)
                val selection = if (simSlot >= 0) "sim_id = ?" else null
                val selectionArgs = if (simSlot >= 0) arrayOf(simSlot.toString()) else null
                
                val deletedRows = contentResolver.delete(uri, selection, selectionArgs)
                
                Logger.apnI("$TAG APN配置重置完成: 删除了 $deletedRows 个配置")
                
                val result = ApnOperationResult(
                    success = true,
                    message = "APN配置重置成功",
                    affectedRows = deletedRows
                )
                
                Result.success(result)
                
            } catch (e: Exception) {
                Logger.apnE("$TAG 重置APN配置异常", e)
                
                val result = ApnOperationResult(
                    success = false,
                    message = "重置APN配置异常: ${e.message}",
                    errorCode = -9
                )
                
                Result.failure(e)
            }
        }
    }
    
    /**
     * 创建ContentValues
     */
    fun createContentValues(config: ApnConfig): ContentValues {
        return ContentValues().apply {
            put(Telephony.Carriers.NAME, config.name)
            put(Telephony.Carriers.APN, config.apn)
            put(Telephony.Carriers.PROXY, checkNotSet(config.proxy))
            put(Telephony.Carriers.PORT, checkNotSet(config.port))
            put(Telephony.Carriers.USER, checkNotSet(config.user))
            put(Telephony.Carriers.PASSWORD, checkNotSet(config.password))
            put(Telephony.Carriers.SERVER, checkNotSet(config.server))
            put(Telephony.Carriers.MMSC, checkNotSet(config.mmsc))
            put(Telephony.Carriers.MMSPROXY, checkNotSet(config.mmsProxy))
            put(Telephony.Carriers.MMSPORT, checkNotSet(config.mmsPort))
            put(Telephony.Carriers.MCC, config.mcc)
            put(Telephony.Carriers.MNC, config.mnc)
            put(Telephony.Carriers.NUMERIC, config.mcc + config.mnc)
            put(Telephony.Carriers.AUTH_TYPE, config.authType)
            put(Telephony.Carriers.TYPE, checkNotSet(config.type))
            put(Telephony.Carriers.PROTOCOL, checkNotSet(config.protocol))
            put(Telephony.Carriers.ROAMING_PROTOCOL, checkNotSet(config.roamingProtocol))
            put(Telephony.Carriers.CARRIER_ENABLED, if (config.carrierEnabled) 1 else 0)

            // Only add bearer and bearer_bitmask if they are not empty
            // Bearer must be an integer, convert string to int safely
            if (config.bearer.isNotEmpty()) {
                try {
                    val bearerInt = config.bearer.toInt()
                    if (bearerInt > 0) {  // Only add if greater than 0
                        put(Telephony.Carriers.BEARER, bearerInt)
                    }
                } catch (e: NumberFormatException) {
                    Logger.apnW("$TAG Invalid bearer value: ${config.bearer}")
                }
            }

            // BearerBitmask must also be an integer
            if (config.bearerBitmask.isNotEmpty()) {
                try {
                    val bearerBitmaskInt = config.bearerBitmask.toInt()
                    if (bearerBitmaskInt > 0) {  // Only add if greater than 0
                        put("bearer_bitmask", bearerBitmaskInt)
                    }
                } catch (e: NumberFormatException) {
                    Logger.apnW("$TAG Invalid bearerBitmask value: ${config.bearerBitmask}")
                }
            }

            // MVNO_TYPE should be an integer, convert string to int safely
            if (config.mvnoType.isNotEmpty()) {
                put(Telephony.Carriers.MVNO_TYPE, getMvnoTypeInt(config.mvnoType))
            }
            put(Telephony.Carriers.MVNO_MATCH_DATA, checkNotSet(config.mvnoMatchData))

            // 处理CURRENT字段
            val numeric = config.mcc + config.mnc
            Logger.apnI("$TAG numeric = $numeric")

            if (numeric.length > 4) {
                // Country code (MCC) is first 3 chars
                val curMcc = numeric.substring(0, 3)
                // Network code (MNC) is remaining chars
                val curMnc = numeric.substring(3)
                Logger.apnI("$TAG curMcc = $curMcc, curMnc = $curMnc")

                if (curMnc == config.mnc && curMcc == config.mcc) {
                    // 将CURRENT字段设置为1表示将此APN设置设置为当前活动的APN
                    // 当条件匹配时，这个APN设置将被标记为当前活动的APN，系统将尝试使用该APN进行数据连接
                    put(Telephony.Carriers.CURRENT, 1)
                    Logger.apnI("$TAG 设置APN为当前活动APN")
                }
            }

            // 双卡支持 - 暂时注释掉，可能导致NullPointerException
            // if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP_MR1) {
            //     put("sim_id", config.simSlot)
            // }

            // Mark as edited
            put("edited", 1)
        }
    }

    /**
     * Check if value is null and return empty string if so
     */
    private fun checkNotSet(value: String?): String {
        return value ?: ""
    }

    /**
     * Convert MVNO type string to integer
     * MVNO types: 0=none, 1=SPN, 2=IMSI, 3=GID, 4=ICCID
     */
    private fun getMvnoTypeInt(mvnoType: String): Int {
        return when (mvnoType.lowercase()) {
            "spn" -> 1
            "imsi" -> 2
            "gid" -> 3
            "iccid" -> 4
            else -> mvnoType.toIntOrNull() ?: 0
        }
    }
    
    /**
     * 从Cursor解析APN配置
     */
    @SuppressLint("Range")
    fun parseApnConfigFromCursor(cursor: Cursor): ApnConfig? {
        return try {
            ApnConfig(
                id = cursor.getLong(ID_INDEX),
                name = cursor.getString(NAME_INDEX) ?: "",
                apn = cursor.getString(APN_INDEX) ?: "",
                proxy = cursor.getString(PROXY_INDEX) ?: "",
                port = cursor.getString(PROXY_PORT_INDEX) ?: "",
                user = cursor.getString(USER_INDEX) ?: "",
                password = cursor.getString(PASSWORD_INDEX) ?: "",
                server = cursor.getString(SERVER_INDEX) ?: "",
                mmsc = cursor.getString(MMSC_INDEX) ?: "",
                mmsProxy = cursor.getString(MMS_PROXY_INDEX) ?: "",
                mmsPort = cursor.getString(MMS_PORT_INDEX) ?: "",
                mcc = cursor.getString(MCC_INDEX) ?: "",
                mnc = cursor.getString(MNC_INDEX) ?: "",
                numeric = cursor.getString(NUMERIC_INDEX) ?: "",
                authType = cursor.getInt(AUTH_TYPE_INDEX),
                type = cursor.getString(TYPE_INDEX) ?: "default,supl",
                protocol = cursor.getString(PROTOCOL_INDEX) ?: "IP",
                roamingProtocol = cursor.getString(ROAMING_PROTOCOL_INDEX) ?: "IP",
                carrierEnabled = cursor.getInt(CARRIER_ENABLED_INDEX) == 1,
                bearer = cursor.getString(BEARER_INDEX) ?: "",
                bearerBitmask = cursor.getString(BEARER_BITMASK_INDEX) ?: "",
                mvnoType = cursor.getString(MVNO_TYPE_INDEX) ?: "",
                mvnoMatchData = cursor.getString(MVNO_MATCH_DATA_INDEX) ?: "",
                current = cursor.getString(CURRENT_INDEX) ?: "0",
                simSlot = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP_MR1) {
                    // 注意：sim_id不在PROJECTION中，需要特殊处理或使用默认值
                    try {
                        val simIdColumnIndex = cursor.getColumnIndex("sim_id")
                        if (simIdColumnIndex >= 0) {
                            cursor.getInt(simIdColumnIndex)
                        } else {
                            0 // sim_id列不存在，使用默认值
                        }
                    } catch (e: Exception) {
                        0 // 默认值
                    }
                } else {
                    0
                }
            )
        } catch (e: Exception) {
            Logger.apnE("$TAG 解析APN配置失败", e)
            null
        }
    }
    
    /**
     * 检查APN配置管理器是否可用
     */
    fun isAvailable(): Boolean {
        return try {
            contentResolver != null
        } catch (e: Exception) {
            Logger.apnE("$TAG APN配置管理器不可用", e)
            false
        }
    }


    /**
     * 根据APN名称和numeric查找APN
     */
    suspend fun findApnByApnNameAndNumeric(apnName: String, numeric: String): ApnConfig? {
        return withContext(Dispatchers.IO) {
            try {
                if ((apnName.isBlank()) && (numeric.isBlank())) {
                    Logger.apn("$TAG findApnByApnNameAndNumeric skip check, apn and numeric must not be null")
                    return@withContext null
                }

                Logger.apnI("$TAG 查找APN: apnName=$apnName, numeric=$numeric")

                val uri = Uri.parse(APN_URI)
                val selection = "apn=\"" + apnName + "\"" + " AND numeric=\"" + numeric + "\"" +
                        " AND NOT (type='ia' AND (apn=\"\" OR apn IS NULL)) AND user_visible!=0" +
                        " AND NOT (type='emergency')" +
                        " AND NOT (type='ims')"

                val cursor = contentResolver.query(uri, PROJECTION, selection, null, Telephony.Carriers.DEFAULT_SORT_ORDER)

                cursor?.use { c ->
                    if (c.moveToFirst()) {
                        return@withContext parseApnConfigFromCursor(c)
                    }
                }

                null

            } catch (e: Exception) {
                Logger.apnE("$TAG 查找APN异常", e)
                null
            }
        }
    }

    /**
     * 根据运营商和APN名称移除APN
     */
    suspend fun removeApnByOperatorAndApnName(apnName: String, numeric: String): Result<Boolean> {
        return withContext(Dispatchers.IO) {
            try {
                Logger.apnI("$TAG 根据运营商和APN名称移除APN: apnName=$apnName, numeric=$numeric")

                val uri = Uri.parse(APN_URI)
                val selection = "${Telephony.Carriers.APN} = ? AND ${Telephony.Carriers.NUMERIC} = ?"
                val selectionArgs = arrayOf(apnName, numeric)

                val deletedRows = contentResolver.delete(uri, selection, selectionArgs)

                Logger.apnI("$TAG 移除APN完成: 删除了 $deletedRows 个配置")
                Result.success(deletedRows > 0)

            } catch (e: Exception) {
                Logger.apnE("$TAG 根据运营商和APN名称移除APN异常", e)
                Result.failure(e)
            }
        }
    }
}
