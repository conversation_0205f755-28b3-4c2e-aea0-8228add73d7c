package com.dspread.mdm.service.platform.api.model

/**
 * 统一的Shell命令执行结果
 */
data class ShellResult(
    val exitCode: Int,
    val output: String,
    val error: String,
    val executionTime: Long = 0L,
    val command: String = "",
    val timestamp: Long = System.currentTimeMillis()
) {
    val isSuccess: Boolean get() = exitCode == 0
    val hasOutput: <PERSON>olean get() = output.isNotEmpty()
    val hasError: <PERSON>olean get() = error.isNotEmpty()

    /**
     * 获取清理后的输出（去除空行和空格）
     */
    val cleanOutput: String
        get() = output.trim().lines().filter { it.isNotBlank() }.joinToString("\n")

    /**
     * 检查输出是否包含指定文本
     */
    fun containsOutput(text: String, ignoreCase: Boolean = true): Boolean {
        return output.contains(text, ignoreCase)
    }

    /**
     * 获取输出的行数组
     */
    fun getOutputLines(): List<String> {
        return output.lines().filter { it.isNotBlank() }
    }

    override fun toString(): String {
        return "ShellResult(exitCode=$exitCode, hasOutput=$hasOutput, hasError=$hasError, time=${executionTime}ms)"
    }

    companion object {
        fun failure(error: String, command: String = ""): ShellResult {
            return ShellResult(
                exitCode = -1,
                output = "",
                error = error,
                command = command
            )
        }

        fun success(output: String, command: String = "", executionTime: Long = 0L): ShellResult {
            return ShellResult(
                exitCode = 0,
                output = output,
                error = "",
                command = command,
                executionTime = executionTime
            )
        }
    }
}

/**
 * 系统操作结果
 */
sealed class SystemOperationResult {
    data class Success(val message: String) : SystemOperationResult()
    data class Failure(val error: String, val exception: Throwable? = null) : SystemOperationResult()
    
    val isSuccess: Boolean get() = this is Success
    val isFailure: Boolean get() = this is Failure
    
    companion object {
        fun success(message: String) = Success(message)
        fun failure(error: String, exception: Throwable? = null) = Failure(error, exception)
    }
}

/**
 * 应用安装结果
 */
data class AppInstallResult(
    val packageName: String,
    val returnCode: Int,
    val message: String
) {
    val isSuccess: Boolean get() = returnCode == 1 // Android PackageManager success code
}

/**
 * 应用卸载结果
 */
data class AppUninstallResult(
    val packageName: String,
    val returnCode: Int,
    val message: String
) {
    val isSuccess: Boolean get() = returnCode == 1
}

/**
 * 设备信息
 */
data class DeviceInfo(
    val serialNumber: String,
    val imei: String,
    val model: String,
    val manufacturer: String,
    val androidVersion: String,
    val buildVersion: String,
    val macAddress: String,
    val cellInfo: CellInfo?
)

/**
 * 基站信息
 */
data class CellInfo(
    val type: String, // GSM, CDMA, LTE等
    val lac: Int,     // Location Area Code
    val cid: Int,     // Cell ID
    val mcc: String = "", // Mobile Country Code
    val mnc: String = "", // Mobile Network Code
    val signalStrength: Int = 0
)

/**
 * 网络配置信息
 */
data class NetworkConfig(
    val ssid: String,
    val password: String,
    val securityType: SecurityType,
    val isHidden: Boolean = false,
    val proxyConfig: ProxyConfig? = null
)

/**
 * WiFi安全类型
 */
enum class SecurityType {
    NONE,
    WEP,
    WPA,
    WPA2,
    WPA3
}

/**
 * 代理配置
 */
data class ProxyConfig(
    val host: String,
    val port: Int,
    val username: String? = null,
    val password: String? = null,
    val excludeList: List<String> = emptyList()
)

/**
 * 系统设置项
 */
data class SystemSetting(
    val key: String,
    val value: String,
    val type: SettingType
)

/**
 * 设置类型
 */
enum class SettingType {
    SYSTEM,    // Settings.System
    SECURE,    // Settings.Secure  
    GLOBAL     // Settings.Global
}

/**
 * 应用信息
 */
data class AppInfo(
    val packageName: String,
    val appName: String,
    val versionName: String,
    val versionCode: Long,
    val installPath: String,
    val isSystemApp: Boolean,
    val isEnabled: Boolean,
    val firstInstallTime: Long,
    val lastUpdateTime: Long
)

/**
 * 系统状态
 */
data class SystemStatus(
    val cpuUsage: Float,
    val memoryUsage: MemoryUsage,
    val storageUsage: StorageUsage,
    val batteryLevel: Int,
    val batteryTemperature: Float,
    val networkStatus: NetworkStatus
)

/**
 * 内存使用情况
 */
data class MemoryUsage(
    val totalMemory: Long,
    val availableMemory: Long,
    val usedMemory: Long
) {
    val usagePercentage: Float get() = (usedMemory.toFloat() / totalMemory) * 100
}

/**
 * 存储使用情况
 */
data class StorageUsage(
    val totalSpace: Long,
    val freeSpace: Long,
    val usedSpace: Long
) {
    val usagePercentage: Float get() = (usedSpace.toFloat() / totalSpace) * 100
}

/**
 * 网络状态
 */
data class NetworkStatus(
    val isWifiConnected: Boolean,
    val isMobileConnected: Boolean,
    val wifiSsid: String?,
    val wifiSignalStrength: Int,
    val mobileSignalStrength: Int,
    val networkType: String
)

/**
 * 任务执行状态
 */
enum class TaskStatus {
    PENDING,    // 等待执行
    RUNNING,    // 正在执行
    SUCCESS,    // 执行成功
    FAILED,     // 执行失败
    CANCELLED   // 已取消
}

/**
 * 系统任务
 */
data class SystemTask(
    val id: String,
    val type: TaskType,
    val parameters: Map<String, Any>,
    val status: TaskStatus,
    val createdTime: Long,
    val startTime: Long? = null,
    val endTime: Long? = null,
    val result: String? = null,
    val error: String? = null
)

/**
 * 任务类型
 */
enum class TaskType {
    INSTALL_APP,
    UNINSTALL_APP,
    REBOOT_SYSTEM,
    SHUTDOWN_SYSTEM,
    EXECUTE_COMMAND,
    UPDATE_SETTINGS,
    BACKUP_DATA,
    RESTORE_DATA,
    NETWORK_CONFIG,
    SYSTEM_UPDATE
}

/**
 * 系统更新信息
 */
data class UpdateInfo(
    val currentVersion: String,
    val buildNumber: String,
    val securityPatch: String,
    val buildFingerprint: String,
    val buildId: String,
    val buildType: String,
    val buildTags: String,
    val sdkVersion: Int,
    val incrementalVersion: String,
    val availableUpdate: UpdatePackage? = null
)

/**
 * 更新包信息
 */
data class UpdatePackage(
    val version: String,
    val buildNumber: String,
    val downloadUrl: String,
    val fileSize: Long,
    val checksum: String,
    val releaseNotes: String,
    val isSecurityUpdate: Boolean,
    val isMajorUpdate: Boolean
)

/**
 * 统一的截屏结果
 */
data class ScreenCaptureResult(
    val success: Boolean,
    val data: ByteArray? = null,
    val filePath: String? = null,
    val bitmap: android.graphics.Bitmap? = null,
    val captureTime: Long = 0L,
    val compressTime: Long = 0L,
    val dataSize: Int = 0,
    val error: String? = null,
    val method: String? = null,
    val format: String? = null,
    val quality: Int = 0,
    val screenInfo: ScreenCaptureInfo? = null
) {
    val totalTime: Long get() = captureTime + compressTime
    val hasData: Boolean get() = data != null || filePath != null || bitmap != null
    val isFailure: Boolean get() = !success || error != null

    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false
        other as ScreenCaptureResult
        return success == other.success &&
                data?.contentEquals(other.data) == true &&
                filePath == other.filePath &&
                error == other.error
    }

    override fun hashCode(): Int {
        var result = success.hashCode()
        result = 31 * result + (data?.contentHashCode() ?: 0)
        result = 31 * result + (filePath?.hashCode() ?: 0)
        result = 31 * result + (error?.hashCode() ?: 0)
        return result
    }

    companion object {
        fun success(
            data: ByteArray? = null,
            filePath: String? = null,
            bitmap: android.graphics.Bitmap? = null,
            captureTime: Long = 0L,
            method: String? = null
        ): ScreenCaptureResult {
            return ScreenCaptureResult(
                success = true,
                data = data,
                filePath = filePath,
                bitmap = bitmap,
                captureTime = captureTime,
                dataSize = data?.size ?: 0,
                method = method
            )
        }

        fun failure(error: String, method: String? = null): ScreenCaptureResult {
            return ScreenCaptureResult(
                success = false,
                error = error,
                method = method
            )
        }
    }
}

/**
 * 截屏信息
 */
data class ScreenCaptureInfo(
    val width: Int,
    val height: Int,
    val density: Float,
    val orientation: Int,
    val pixelFormat: String? = null
)
