package com.dspread.mdm.service.network.websocket.message.handler

import android.content.Context
import com.dspread.mdm.service.modules.rulebase.RuleBaseStorage
import com.dspread.mdm.service.network.websocket.message.WsMessageSender
import com.dspread.mdm.service.utils.log.Logger
import org.json.JSONArray
import org.json.JSONObject

/**
 * 规则处理器
 * 处理 ST002/ST005 规则相关消息
 * 集成新的RuleBase模块，实现简化的规则处理逻辑
 */
class RuleHandler(context: Context) : BaseMessageHandler(context) {

    companion object {
        private const val TAG = "RuleHandler"

        // 结果常量
        private const val RESULT_SUCCESS = "0"
        private const val RESULT_FAILED = "1"
        private const val RESULT_IGNORED = "2"
    }

    override fun handleMessage(message: String) {
        val jsonObject = parseMessage(message) ?: return
        val messageInfo = getMessageInfo(jsonObject)
        val data = getDataFromMessage(jsonObject) ?: return

        Logger.rule("$TAG 处理规则消息: ${messageInfo.tranCode}")

        try {
            if (data.has("ruleList")) {
                val ruleList = data.getJSONArray("ruleList")
                processRuleList(ruleList, messageInfo)
            } else {
                Logger.ruleE("$TAG 规则消息缺少 ruleList 字段")
                uploadRuleResult(messageInfo, RESULT_FAILED, "消息格式错误：缺少ruleList字段")
            }
        } catch (e: Exception) {
            Logger.ruleE("$TAG 处理规则消息失败", e)
            uploadRuleResult(messageInfo, RESULT_FAILED, "处理规则消息异常: ${e.message}")
        }
    }

    /**
     * 处理规则列表
     * 使用新的RuleBase模块处理规则
     */
    private fun processRuleList(ruleList: JSONArray, messageInfo: BaseMessageHandler.MessageInfo) {
        Logger.rule("$TAG 处理规则列表，数量: ${ruleList.length()}")

        var successCount = 0
        var failedCount = 0
        var ignoredCount = 0
        val processResults = mutableListOf<String>()

        for (i in 0 until ruleList.length()) {
            try {
                val rule = ruleList.getJSONObject(i)
                val ruleId = rule.optString("ruleId", "unknown")
                val action = rule.optString("action", "unknown")

                Logger.rule("$TAG 处理规则: ruleId=$ruleId, action=$action")

                // 使用新的RuleBaseStorage处理规则
                val result = RuleBaseStorage.processRule(rule)

                when {
                    result.isSuccess && result is com.dspread.mdm.service.modules.rulebase.RuleProcessResult.Success -> {
                        successCount++
                        processResults.add("$ruleId: ${result.message}")
                        Logger.rule("$TAG 规则处理成功: $ruleId")
                    }
                    result.isSuccess && result is com.dspread.mdm.service.modules.rulebase.RuleProcessResult.Ignored -> {
                        ignoredCount++
                        processResults.add("$ruleId: ${result.message}")
                        Logger.rule("$TAG 规则被忽略: $ruleId - ${result.message}")
                    }
                    else -> {
                        failedCount++
                        processResults.add("$ruleId: ${result.message}")
                        Logger.ruleE("$TAG 规则处理失败: $ruleId - ${result.message}")
                    }
                }

            } catch (e: Exception) {
                Logger.ruleE("$TAG 处理单个规则异常", e)
                failedCount++
                processResults.add("规则${i}: 处理异常 - ${e.message}")
            }
        }

        // 上报处理结果
        val resultMessage = "处理完成 - 成功: $successCount, 失败: $failedCount, 忽略: $ignoredCount"
        val overallResult = when {
            failedCount == 0 && successCount > 0 -> RESULT_SUCCESS
            failedCount == 0 && ignoredCount > 0 -> RESULT_IGNORED
            successCount == 0 && ignoredCount == 0 -> RESULT_FAILED
            else -> RESULT_SUCCESS // 部分成功也算成功
        }

        Logger.rule("$TAG 规则列表处理完成: $resultMessage")
        uploadRuleResult(messageInfo, overallResult, resultMessage)
    }

    /**
     * 上报规则处理结果
     */
    private fun uploadRuleResult(messageInfo: BaseMessageHandler.MessageInfo, result: String, message: String) {
        try {
            Logger.rule("$TAG 上报规则处理结果: $result - $message")

            // 使用现有的uploadRulebasedResult方法
            WsMessageSender.uploadRulebasedResult(
                ruleId = "batch_rules", // 批量处理的规则ID
                result = result,
                failedList = null,
                orgRequestId = messageInfo.requestId,
                orgRequestTime = messageInfo.requestTime
            )

        } catch (e: Exception) {
            Logger.ruleE("$TAG 上报规则结果失败", e)
        }
    }
}


