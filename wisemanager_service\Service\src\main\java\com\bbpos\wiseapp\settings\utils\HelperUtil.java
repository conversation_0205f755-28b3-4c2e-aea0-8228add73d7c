package com.bbpos.wiseapp.settings.utils;

import android.app.backup.BackupManager;
import android.content.Context;
import android.content.res.Configuration;
import android.net.wifi.WifiManager;
import android.os.Build;
import android.os.LocaleList;
import android.util.Log;

import com.bbpos.wiseapp.service.R;

import java.lang.reflect.Method;
import java.util.Locale;

import static android.content.ContentValues.TAG;

/**
 * Created by <PERSON> on 2018/12/27.
 */

public class HelperUtil {
    public static String getCountryLanguage(Context context) {
        Locale locale;
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            locale = context.getResources().getConfiguration().getLocales().get(0);
        } else {
            locale = context.getResources().getConfiguration().locale;
        }
        return locale.getLanguage() + "-" + locale.getCountry();
    }

    public static void changeSystemLanguage(Locale locale) {
        if (locale != null) {
            try {
                Class classActivityManagerNative = Class.forName("android.app.ActivityManagerNative");
                Method getDefault = classActivityManagerNative.getDeclaredMethod("getDefault");
                Object objIActivityManager = getDefault.invoke(classActivityManagerNative);
                Class classIActivityManager = Class.forName("android.app.IActivityManager");
                Method getConfiguration = classIActivityManager.getDeclaredMethod("getConfiguration");
                Configuration config = (Configuration) getConfiguration.invoke(objIActivityManager);
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                    config.setLocales(new LocaleList(locale));
                    Class[] clzParams = {Configuration.class};
                    Method updateConfiguration = classIActivityManager.getDeclaredMethod("updatePersistentConfiguration", clzParams);
                    updateConfiguration.invoke(objIActivityManager, config);
                } else {
                    config.setLocale(locale);
                    Class clzConfig = Class.forName("android.content.res.Configuration");
                    java.lang.reflect.Field userSetLocale = clzConfig.getField("userSetLocale");
                    userSetLocale.set(config, true);
                    Class[] clzParams = {Configuration.class};
                    Method updateConfiguration = classIActivityManager.getDeclaredMethod("updateConfiguration", clzParams);
                    updateConfiguration.setAccessible(true);
                    updateConfiguration.invoke(objIActivityManager, config);
                    BackupManager.dataChanged("com.android.providers.settings");
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    /** 判断是否是快速点击 */
    private static long lastClickTime;
    public static boolean isFastDoubleClick() {
        long time = System.currentTimeMillis();
        long timeD = time - lastClickTime;
        if (0 < timeD && timeD < 1000) {
            return true;
        }
        lastClickTime = time;
        return false;
    }

    public static boolean isWifiEnable(Context context) {
        WifiManager wifiManager = null;
        if(wifiManager==null) {
            wifiManager = (WifiManager) context.getApplicationContext().getSystemService(Context.WIFI_SERVICE);
        }
        return wifiManager==null?false:wifiManager.isWifiEnabled();
    }
}
