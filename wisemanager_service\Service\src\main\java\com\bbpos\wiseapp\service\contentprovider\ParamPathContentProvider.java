package com.bbpos.wiseapp.service.contentprovider;

import android.app.ActivityManager;
import android.content.ContentProvider;
import android.content.ContentValues;
import android.content.Context;
import android.content.Intent;
import android.database.Cursor;
import android.net.Uri;
import android.os.Binder;
import android.util.Log;

import com.bbpos.wiseapp.logger.BBLog;
import com.bbpos.wiseapp.param.manager.ParamParseService;
import com.bbpos.wiseapp.service.contentprovider.db.ParamDbOperation;
import com.bbpos.wiseapp.system.api.ServiceApi;
import com.bbpos.wiseapp.tms.utils.Constants;

import java.util.Iterator;
import java.util.List;

public class ParamPathContentProvider extends ContentProvider{
	private static final String TAG = "ParamPathCntProvider";

	@Override
	public boolean onCreate() {
		return false;
	}

	@Override
	public Cursor query(Uri uri, String[] projection, String selection, String[] selectionArgs, String sortOrder) {
		return null;
	}

	@Override
	public String getType(Uri uri) {
		return null;
	}

	@Override
	public Uri insert(Uri uri, ContentValues values) {
		String pkgName = getMyCallingPackage();
		if(!Constants.TMS_PKG_NAME.equals(pkgName))
			return null;
		BBLog.v(BBLog.TAG, pkgName+"insert :"+values.toString());
		boolean isInserted = ParamDbOperation.insertParamFile(getContext(), values);
		if(isInserted){
			Intent service = new Intent(getContext(), ParamParseService.class);
			service.putExtra(Constants.PARAM_FILE_PATH, values.getAsString("param_file_path"));
			service.putExtra(Constants.PARAM_FILE_MD5, values.getAsString("param_file_md5"));
			ServiceApi.getIntance().startService(service);
		}
		return null;
	}

	@Override
	public int delete(Uri uri, String selection, String[] selectionArgs) {
		// TODO Auto-generated method stub
		return 0;
	}

	@Override
	public int update(Uri uri, ContentValues values, String selection, String[] selectionArgs) {
		// TODO Auto-generated method stub
		return 0;
	}
	
	public String getMyCallingPackage(){
		return getPackName(Binder.getCallingPid());
	}
	
	@SuppressWarnings("rawtypes")
	private String getPackName(int pID) {
		String processName = null;
		ActivityManager am = (ActivityManager) getContext().getSystemService(Context.ACTIVITY_SERVICE);
		List l = am.getRunningAppProcesses();
		Iterator i = l.iterator();
		while (i.hasNext()) {
			ActivityManager.RunningAppProcessInfo info = (ActivityManager.RunningAppProcessInfo) (i.next());
//			BBLog.v(Constans.TAG, info.processName);
			try {
				if (info.pid == pID) {
					processName = info.processName;
				}
			} catch (Exception e) {
			}
		}
		return processName;
	}
}
