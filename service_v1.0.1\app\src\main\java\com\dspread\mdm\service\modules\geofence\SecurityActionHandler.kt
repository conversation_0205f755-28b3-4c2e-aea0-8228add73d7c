package com.dspread.mdm.service.modules.geofence

import android.app.admin.DevicePolicyManager
import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.os.RecoverySystem
import android.widget.Toast
import com.dspread.mdm.service.platform.api.system.SystemControlApi
import com.dspread.mdm.service.utils.log.Logger

import com.dspread.mdm.service.broadcast.core.BroadcastSender
import kotlinx.coroutines.delay
import kotlinx.coroutines.withContext
import kotlinx.coroutines.Dispatchers

/**
 * 安全措施处理器
 * 负责执行地理围栏相关的安全措施
 */
class SecurityActionHandler(
    private val context: Context
) {
    
    companion object {
        private const val TAG = "[SecurityActionHandler]"
    }

    // 设备管理员相关
    private val devicePolicyManager = context.getSystemService(Context.DEVICE_POLICY_SERVICE) as DevicePolicyManager
    private var adminComponent: ComponentName? = null
    
    /**
     * 初始化安全措施处理器
     */
    fun initialize(): Boolean {
        return try {
            Logger.geo("$TAG 初始化安全措施处理器")
            
            // 检查设备管理员权限
            checkDeviceAdminPermission()
            
            Logger.geo("$TAG 安全措施处理器初始化完成")
            true
        } catch (e: Exception) {
            Logger.geoE("$TAG 安全措施处理器初始化失败", e)
            false
        }
    }
    
    /**
     * 显示警告消息
     */
    suspend fun showWarning(message: String): Result<Unit> {
        return try {
            Logger.geo("$TAG 显示警告: $message")
            
            withContext(Dispatchers.Main) {
                // 显示Toast警告
                Toast.makeText(context, message, Toast.LENGTH_LONG).show()
                
                // 可以在这里添加更复杂的警告UI，比如全屏对话框
                showFullScreenWarning(message)
            }
            
            Result.success(Unit)
        } catch (e: Exception) {
            Logger.geoE("$TAG 显示警告失败", e)
            Result.failure(e)
        }
    }
    
    /**
     * 锁定屏幕
     */
    suspend fun lockScreen(): Result<Unit> {
        return try {
            Logger.geo("$TAG 执行锁屏操作")
            
            // 方法1：使用设备管理员API
            val adminResult = lockScreenWithDeviceAdmin()
            if (adminResult.isSuccess) {
                Logger.geo("$TAG 通过设备管理员API锁屏成功")
                return adminResult
            }
            
            // 方法2：使用系统控制API
            val systemResult = SystemControlApi.lockScreen(context)
            if (systemResult.isSuccess) {
                Logger.geo("$TAG 通过系统控制API锁屏成功")
                return Result.success(Unit)
            }
            
            // 方法3：使用Shell命令
            val shellResult = lockScreenWithShell()
            if (shellResult.isSuccess) {
                Logger.geo("$TAG 通过Shell命令锁屏成功")
                return shellResult
            }
            
            Logger.geoE("$TAG 所有锁屏方法都失败")
            Result.failure(Exception("All lock screen methods failed"))
            
        } catch (e: Exception) {
            Logger.geoE("$TAG 锁屏操作异常", e)
            Result.failure(e)
        }
    }
    
    /**
     * 数据擦除
     */
    suspend fun wipeData(): Result<Unit> {
       return try {
            Logger.geo("$TAG 执行恢复出厂设置操作")

           // 显示最后警告
           showWarning("警告：即将执行恢复出厂设置操作！")
           delay(5000) // 等待5秒

           // 方法1：使用RecoverySystem API（系统权限应用专用）
           val recoveryResult = factoryResetWithRecoverySystem()
           if (recoveryResult.isSuccess) {
               Logger.geo("$TAG 通过RecoverySystem API恢复出厂设置成功")
               return recoveryResult
           }

           // 方法2：使用设备管理员API
           val adminResult = wipeDataWithDeviceAdmin()
           if (adminResult.isSuccess) {
               Logger.geo("$TAG 通过设备管理员API恢复出厂设置成功")
               return adminResult
           }

           // 方法3：使用系统控制API
           val systemResult = SystemControlApi.wipeData(context)
           if (systemResult.isSuccess) {
               Logger.geo("$TAG 通过系统控制API恢复出厂设置成功")
               return Result.success(Unit)
           }

           // 方法4：使用Shell命令
           val shellResult = factoryResetWithShell()
           if (shellResult.isSuccess) {
               Logger.geo("$TAG 通过Shell命令恢复出厂设置成功")
               return shellResult
           }

           Logger.geoE("$TAG 所有恢复出厂设置方法都失败")
           Result.failure(Exception("All factory reset methods failed"))

       } catch (e: Exception) {
           Logger.geoE("$TAG 恢复出厂设置操作异常", e)
           Result.failure(e)
       }
    }
    
    /**
     * 重启设备
     */
    suspend fun reboot(): Result<Unit> {
        return try {
            Logger.geo("$TAG 执行重启操作")
            
            // 显示重启警告
            showWarning("设备将在5秒后重启")
            delay(5000)
            
            // 使用系统控制API重启
            val result = SystemControlApi.reboot(context, "geofence_security_action")

            if (result.isSuccess) {
                Logger.geo("$TAG 重启操作执行成功")
                Result.success(Unit)
            } else {
                Logger.geoE("$TAG 重启操作执行失败")
                Result.failure(Exception("Reboot failed"))
            }
            
        } catch (e: Exception) {
            Logger.geoE("$TAG 重启操作异常", e)
            Result.failure(e)
        }
    }
    
    /**
     * 禁用特定功能
     */
    suspend fun disableFeatures(features: List<String>): Result<Unit> {
        return try {
            Logger.geo("$TAG 禁用功能: $features")
            
            for (feature in features) {
                when (feature.lowercase()) {
                    "wifi" -> disableWifi()
                    "bluetooth" -> disableBluetooth()
                    "camera" -> disableCamera()
                    "usb" -> disableUsb()
                    "location" -> disableLocation()
                    else -> Logger.geoW("$TAG 未知功能: $feature")
                }
            }
            
            Result.success(Unit)
        } catch (e: Exception) {
            Logger.geoE("$TAG 禁用功能失败", e)
            Result.failure(e)
        }
    }
    
    /**
     * 使用设备管理员API锁屏
     */
    private fun lockScreenWithDeviceAdmin(): Result<Unit> {
        return try {
            if (adminComponent != null && devicePolicyManager.isAdminActive(adminComponent!!)) {
                devicePolicyManager.lockNow()
                Logger.geo("$TAG 设备管理员锁屏成功")
                Result.success(Unit)
            } else {
                Logger.geoW("$TAG 设备管理员权限不可用")
                Result.failure(Exception("Device admin not available"))
            }
        } catch (e: Exception) {
            Logger.geoE("$TAG 设备管理员锁屏失败", e)
            Result.failure(e)
        }
    }
    
    /**
     * 使用Shell命令锁屏
     */
    private suspend fun lockScreenWithShell(): Result<Unit> {
        return try {
            val commands = listOf(
                "input keyevent 26", // 电源键
                "am start -a android.intent.action.SCREEN_OFF",
                "service call power 1 i32 0 i32 0 i32 1"
            )
            
            for (command in commands) {
                val result = SystemControlApi.executeShellCommand(context, command)
                if (result.isSuccess) {
                    Logger.geo("$TAG Shell命令锁屏成功: $command")
                    return Result.success(Unit)
                }
            }
            
            Result.failure(Exception("All shell lock commands failed"))
        } catch (e: Exception) {
            Logger.geoE("$TAG Shell命令锁屏失败", e)
            Result.failure(e)
        }
    }
    
    /**
     * 使用设备管理员API数据擦除
     */
    private fun wipeDataWithDeviceAdmin(): Result<Unit> {
        return try {
            if (adminComponent != null && devicePolicyManager.isAdminActive(adminComponent!!)) {
                // 擦除所有数据，包括外部存储
                devicePolicyManager.wipeData(
                    DevicePolicyManager.WIPE_EXTERNAL_STORAGE or 
                    DevicePolicyManager.WIPE_RESET_PROTECTION_DATA
                )
                Logger.geo("$TAG 设备管理员数据擦除成功")
                Result.success(Unit)
            } else {
                Logger.geoW("$TAG 设备管理员权限不可用")
                Result.failure(Exception("Device admin not available"))
            }
        } catch (e: Exception) {
            Logger.geoE("$TAG 设备管理员数据擦除失败", e)
            Result.failure(e)
        }
    }
    
    /**
     * 使用RecoverySystem API恢复出厂设置（系统权限应用专用）
     */
    private suspend fun factoryResetWithRecoverySystem(): Result<Unit> {
        return withContext(Dispatchers.IO) {
            try {
                Logger.geo("$TAG 使用RecoverySystem API执行恢复出厂设置")

                // 系统权限应用可以直接调用RecoverySystem.rebootWipeUserData
                RecoverySystem.rebootWipeUserData(context)

                Logger.geo("$TAG RecoverySystem API恢复出厂设置命令已执行")
                Result.success(Unit)

            } catch (e: SecurityException) {
                Logger.geoE("$TAG RecoverySystem API权限不足", e)
                Result.failure(e)
            } catch (e: Exception) {
                Logger.geoE("$TAG RecoverySystem API恢复出厂设置失败", e)
                Result.failure(e)
            }
        }
    }

    /**
     * 使用Shell命令恢复出厂设置
     */
    private suspend fun factoryResetWithShell(): Result<Unit> {
        return try {
            val commands = listOf(
                // 方法1：发送恢复出厂设置广播
                "am broadcast -a android.intent.action.MASTER_CLEAR",
                // 方法2：直接调用recovery模式
                "recovery --wipe_data",
                // 方法3：重启到recovery模式
                "reboot recovery",
                // 方法4：使用settings命令
                "settings put global device_provisioned 0 && reboot"
            )

            for (command in commands) {
                val result = SystemControlApi.executeShellCommand(context, command)
                if (result.isSuccess) {
                    Logger.geo("$TAG Shell命令恢复出厂设置成功: $command")
                    return Result.success(Unit)
                }
            }

            Result.failure(Exception("All shell factory reset commands failed"))
        } catch (e: Exception) {
            Logger.geoE("$TAG Shell命令恢复出厂设置失败", e)
            Result.failure(e)
        }
    }

    /**
     * 使用Shell命令数据擦除（保留原方法作为备用）
     */
    private suspend fun wipeDataWithShell(): Result<Unit> {
        return try {
            val commands = listOf(
                "am broadcast -a android.intent.action.MASTER_CLEAR",
                "recovery --wipe_data",
                "reboot recovery"
            )
            
            for (command in commands) {
                val result = SystemControlApi.executeShellCommand(context, command)
                if (result.isSuccess) {
                    Logger.geo("$TAG Shell命令数据擦除成功: $command")
                    return Result.success(Unit)
                }
            }
            
            Result.failure(Exception("All shell wipe commands failed"))
        } catch (e: Exception) {
            Logger.geoE("$TAG Shell命令数据擦除失败", e)
            Result.failure(e)
        }
    }
    
    /**
     * 显示全屏警告
     */
    private fun showFullScreenWarning(message: String) {
        try {
            // 发送广播显示警告
            BroadcastSender.sendBroadcast(
                context,
                "com.dspread.mdm.GEOFENCE_WARNING",
                Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TOP,
                "message" to message,
                "timestamp" to System.currentTimeMillis()
            )

            Logger.geo("$TAG 全屏警告已发送")
        } catch (e: Exception) {
            Logger.geoE("$TAG 显示全屏警告失败", e)
        }
    }
    
    /**
     * 检查设备管理员权限
     */
    private fun checkDeviceAdminPermission() {
        try {
            // 这里需要根据实际的设备管理员组件名称进行配置
            // adminComponent = ComponentName(context, DeviceAdminReceiver::class.java)
            
            // 检查是否已激活设备管理员
            // val isActive = adminComponent?.let { devicePolicyManager.isAdminActive(it) } ?: false
            // Logger.geo("$TAG 设备管理员状态: $isActive")
            
            Logger.geo("$TAG 设备管理员权限检查完成")
        } catch (e: Exception) {
            Logger.geoE("$TAG 设备管理员权限检查失败", e)
        }
    }
    
    /**
     * 禁用WiFi
     */
    private suspend fun disableWifi(): Result<Unit> {
        val result = SystemControlApi.executeShellCommand(context, "svc wifi disable")
        return if (result.isSuccess) Result.success(Unit) else Result.failure(Exception("WiFi disable failed"))
    }

    /**
     * 禁用蓝牙
     */
    private suspend fun disableBluetooth(): Result<Unit> {
        val result = SystemControlApi.executeShellCommand(context, "svc bluetooth disable")
        return if (result.isSuccess) Result.success(Unit) else Result.failure(Exception("Bluetooth disable failed"))
    }
    
    /**
     * 禁用摄像头
     */
    private suspend fun disableCamera(): Result<Unit> {
        return try {
            if (adminComponent != null && devicePolicyManager.isAdminActive(adminComponent!!)) {
                devicePolicyManager.setCameraDisabled(adminComponent!!, true)
                Result.success(Unit)
            } else {
                val result = SystemControlApi.executeShellCommand(context, "setprop camera.disable_zsl_mode 1")
                if (result.isSuccess) Result.success(Unit) else Result.failure(Exception("Camera disable failed"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * 禁用USB
     */
    private suspend fun disableUsb(): Result<Unit> {
        val result = SystemControlApi.executeShellCommand(context, "setprop sys.usb.config none")
        return if (result.isSuccess) Result.success(Unit) else Result.failure(Exception("USB disable failed"))
    }

    /**
     * 禁用位置服务
     */
    private suspend fun disableLocation(): Result<Unit> {
        val result = SystemControlApi.executeShellCommand(context, "settings put secure location_providers_allowed ''")
        return if (result.isSuccess) Result.success(Unit) else Result.failure(Exception("Location disable failed"))
    }
    
    /**
     * 检查是否有设备管理员权限
     */
    fun hasDeviceAdminPermission(): Boolean {
        return adminComponent?.let { devicePolicyManager.isAdminActive(it) } == true
    }
    
    /**
     * 检查是否有系统权限
     */
    fun hasSystemPermission(): Boolean {
        return SystemControlApi.hasRootPermission(context)
    }
}
