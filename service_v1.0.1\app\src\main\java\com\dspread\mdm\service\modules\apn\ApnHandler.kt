package com.dspread.mdm.service.modules.apn

import android.content.Context
import android.content.SharedPreferences
import android.os.Build
import android.telephony.SubscriptionInfo
import androidx.annotation.RequiresApi
import com.dspread.mdm.service.modules.BaseModuleHandler
import com.dspread.mdm.service.modules.apn.model.ApnConfig
import com.dspread.mdm.service.network.websocket.message.WsMessageSender
import com.dspread.mdm.service.utils.log.Logger
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.launch
import org.json.JSONArray
import org.json.JSONObject

/**
 * APN管理WebSocket消息处理器
 * 处理ST009类型的APN配置管理消息
 */
@RequiresApi(api = Build.VERSION_CODES.N)
class ApnHandler(
    private val context: Context
) : BaseModuleHandler() {
    
    companion object {
        private const val TAG = "[ApnHandler]"
        private const val APN_LIST_CACHE_KEY = "apn_list_cache"
    }
    
    // 简化版本：ApnManager本身可以安全创建，NetworkStatusMonitor已经处理了线程安全问题
    private val apnManager by lazy { ApnManager(context) }
    
    override fun getModuleName(): String = "APN"

    // APN处理专用的协程作用域
    private val apnScope = CoroutineScope(Dispatchers.IO + SupervisorJob())

    // SharedPreferences for APN cache 
    private val sharedPreferences: SharedPreferences by lazy {
        context.getSharedPreferences("apn_cache", Context.MODE_PRIVATE)
    }

    override suspend fun handleMessage(message: String): Result<Unit> {
        return try {
            // 检查模块是否启用
            checkModuleEnabled().getOrThrow()
            
            Logger.apnI("$TAG 开始处理APN配置管理消息")
            
            val jsonObject = parseJsonMessage(message)
            
            // 发送C0000响应确认
            sendAcknowledgment(jsonObject)

            handleMsg(message)

            Result.success(Unit)
            
        } catch (e: Exception) {
            Logger.apnE("$TAG APN消息处理失败", e)
            Result.failure(e)
        }
    }


    /**
     * 完整的APN配置处理流程
     */
    fun handleMsg(response: String) {
        try {
            Logger.apnI("$TAG ApnHandler [handleMsg]: 开始处理APN配置")

            val responseJson = JSONObject(response)
            val responseData = getDataFromResponse(responseJson)
            if (responseData == null) {
                Logger.apnW("$TAG responseData为空")
                return
            }

            if (responseData.has("apnList")) {
                val apnList = responseData.getJSONArray("apnList")

                if (apnList == null || apnList.length() == 0) {
                    Logger.apnW("$TAG 下发APN数据为空，移除所有已保存的APN配置项")
                    // 获取缓存的APN数据并清理
                    val cacheApnData = getCachedApnData()
                    if (cacheApnData.isNotEmpty()) {
                        removeCustomerAddApnIfNeed(cacheApnData)
                        clearApnCache()
                    }
                    return
                }

                Logger.apnI("$TAG ========== APN配置处理开始 ==========")
                Logger.apnI("$TAG 下发APN配置总数: ${apnList.length()}")

                // 解析APN数据列表
                val apnDataList = parseDataToApnList(apnList)
                if (apnDataList.isNotEmpty()) {
                    Logger.apnI("$TAG 解析到 ${apnDataList.size} 个有效APN配置")

                    // 打印所有下发的APN配置详情
                    apnDataList.forEachIndexed { index, apn ->
                        Logger.apnI("$TAG   📌 APN[${index + 1}]: ${apn.name} (${apn.apn}) - 优先级:${apn.priority} - 运营商:${apn.numeric}")
                    }

                    // 移除所有已保存的APN配置项
                    Logger.apnI("$TAG 🗑️ 清理旧APN配置...")
                    removeCustomerAddApn()

                    // 缓存APN配置列表
                    Logger.apn("$TAG 💾 保存APN列表到缓存")
                    saveApnListToCache(apnList.toString())

                    // 根据numeric进行分组，符合当前运营商组的首个节点为优先级最高的apn
                    val numericMap = groupApnsByNumeric(apnDataList)
                    Logger.apn("$TAG APN分组完成，共 ${numericMap.size} 个运营商分组")

                    // 处理当前运营商的APN配置
                    processCurrentCarrierApn(numericMap)
                }
            }

        } catch (e: Exception) {
            Logger.apnE("$TAG handleMsg处理失败", e)
        }
    }

    /**
     * 从响应中获取data对象
     */
    private fun getDataFromResponse(responseJson: JSONObject): JSONObject? {
        return try {
            responseJson.optJSONObject("data")
        } catch (e: Exception) {
            Logger.apnE("$TAG 获取响应数据失败", e)
            null
        }
    }

    /**
     * 获取缓存的APN数据
     */
    private fun getCachedApnData(): String {
        return try {
            sharedPreferences.getString(APN_LIST_CACHE_KEY, "") ?: ""
        } catch (e: Exception) {
            Logger.apnE("$TAG 获取缓存APN数据失败", e)
            ""
        }
    }

    /**
     * 保存APN列表到缓存
     */
    private fun saveApnListToCache(apnListJson: String) {
        try {
            Logger.apn("$TAG 保存APN列表到缓存")
            sharedPreferences.edit()
                .putString(APN_LIST_CACHE_KEY, apnListJson)
                .apply()
        } catch (e: Exception) {
            Logger.apnE("$TAG 保存APN列表缓存失败", e)
        }
    }

    /**
     * 清除APN缓存
     */
    private fun clearApnCache() {
        try {
            Logger.apn("$TAG 清除APN缓存")
            sharedPreferences.edit()
                .remove(APN_LIST_CACHE_KEY)
                .apply()
        } catch (e: Exception) {
            Logger.apnE("$TAG 清除APN缓存失败", e)
        }
    }

    /**
     * 移除客户添加的APN（如果需要）
     */
    private fun removeCustomerAddApnIfNeed(apnList: String) {
        if (apnList.isEmpty()) return

        try {
            val cacheApnData = JSONArray(apnList)
            if (cacheApnData.length() > 0) {
                for (i in 0 until cacheApnData.length()) {
                    val apnData = cacheApnData.optJSONObject(i)
                    if (apnData != null) {
                        val mcc = apnData.optString("mcc")
                        val mnc = apnData.optString("mnc")
                        val apnName = apnData.optString("apn")
                        val numeric = "$mcc$mnc"

                        // 移除指定的APN配置
                        apnScope.launch {
                            apnManager.removeApnByOperatorAndApnName(apnName, numeric)
                        }
                    }
                }

                // 恢复默认网络和APN
                apnScope.launch {
                    apnManager.restoreNormalNetwork()
                    apnManager.restoreDefaultApn()

                    // 上送网络状态
                    Logger.apnI("$TAG APN配置发生变化，上送网络状态")
                    sendNetworkStatusUpload()
                }
            }
        } catch (e: Exception) {
            Logger.apnE("$TAG 移除客户APN失败", e)
        }
    }

    /**
     * 移除客户添加的APN
     */
    private fun removeCustomerAddApn() {
        val cacheData = getCachedApnData()
        if (cacheData.isNotEmpty()) {
            removeCustomerAddApnIfNeed(cacheData)
        }
    }

    /**
     * 解析数据到APN列表
     */
    private fun parseDataToApnList(jsonArray: JSONArray): List<ApnConfig> {
        val apnList = mutableListOf<ApnConfig>()
        if (jsonArray.length() == 0) return apnList

        try {
            for (i in 0 until jsonArray.length()) {
                val apnEntry = jsonArray.getJSONObject(i)
                val apnConfig = ApnConfig(
                    name = apnEntry.optString("name", ""),
                    apn = apnEntry.optString("apn", ""),
                    proxy = apnEntry.optString("proxy", ""),
                    port = apnEntry.optString("port", ""),
                    user = apnEntry.optString("user", ""),
                    password = apnEntry.optString("password", ""),
                    server = apnEntry.optString("server", ""),
                    mmsc = apnEntry.optString("mmsc", ""),
                    mmsProxy = apnEntry.optString("mmsProxy", ""),
                    mmsPort = apnEntry.optString("mmsPort", ""),
                    mcc = apnEntry.optString("mcc", ""),
                    mnc = apnEntry.optString("mnc", ""),
                    numeric = "${apnEntry.optString("mcc")}${apnEntry.optString("mnc")}",
                    authType = apnEntry.optInt("authType", 0),
                    type = apnEntry.optString("apnType", "default"),
                    protocol = apnEntry.optString("protocol", "IP"),
                    roamingProtocol = apnEntry.optString("roamingProtocol", "IP"),
                    carrierEnabled = apnEntry.optBoolean("carrierEnabled", true),
                    bearer = run {
                        // 处理bearer和bearer_bitmask值
                        val bearerBitmask = apnManager.getBearerBitmaskValue(apnEntry.optString("bearer"))
                        val bearerValue = apnManager.getBearerValue(bearerBitmask)
                        bearerValue.toString()
                    },
                    bearerBitmask = run {
                        val bearerBitmask = apnManager.getBearerBitmaskValue(apnEntry.optString("bearer"))
                        bearerBitmask.toString()
                    },
                    mvnoType = apnEntry.optString("mvnoType", ""),
                    mvnoMatchData = apnEntry.optString("mvnoMatchData", ""),
                    priority = apnEntry.optInt("order", 1),
                    simSlot = apnEntry.optInt("subId", 0),
                    isDefault = apnEntry.optString("current", "0") == "1",
                    isActive = apnEntry.optBoolean("carrierEnabled", true)
                )
                apnList.add(apnConfig)
            }
        } catch (e: Exception) {
            Logger.apnE("$TAG 解析APN数据失败", e)
        }

        return apnList
    }

    /**
     * 根据numeric分组APN并按优先级排序
     */
    private fun groupApnsByNumeric(apnList: List<ApnConfig>): Map<String, List<ApnConfig>> {
        return try {
            // 先按numeric分组
            val numericMap = apnList.groupBy { it.numeric }.toMutableMap()

            // 对每个分组内的APN按优先级排序（优先级数值越小，优先级越高）
            numericMap.forEach { (numeric, apns) ->
                numericMap[numeric] = apns.sortedBy { it.priority }
            }

            Logger.apn("$TAG APN分组完成，共 ${numericMap.size} 个运营商分组")
            numericMap
        } catch (e: Exception) {
            Logger.apnE("$TAG 分组APN失败", e)
            emptyMap()
        }
    }

    /**
     * 处理当前运营商的APN配置
     */
    private fun processCurrentCarrierApn(numericMap: Map<String, List<ApnConfig>>) {
        apnScope.launch {
            try {
                // 获取活跃的SIM卡信息
                val subscriptionInfoList = apnManager.getActiveSubscriptionInfoList()

                if (!subscriptionInfoList.isNullOrEmpty()) {
                    var applyApnResult = false
                    var firstIndexApn: ApnConfig? = null
                    var currentSubInfo: SubscriptionInfo? = null

                    // 遍历每个SIM卡
                    for (info in subscriptionInfoList) {
                        val numeric = apnManager.getCurrentOperatorNumeric()
                        Logger.apnI("$TAG 处理SIM卡运营商numeric: $numeric")

                        if (numericMap.containsKey(numeric)) {
                            val apns = numericMap[numeric]
                            if (!apns.isNullOrEmpty()) {
                                Logger.apnI("$TAG 当前运营商匹配到 ${apns.size} 个APN配置")

                                for (i in apns.indices) {
                                    if (i == 0) {
                                        // 添加优先级最高的apn并应用
                                        firstIndexApn = apns[0]
                                        Logger.apnI("$TAG ⭐ 首选APN (优先级${firstIndexApn!!.priority}): ${firstIndexApn!!.name} (${firstIndexApn!!.apn})")
                                        Logger.apnI("$TAG 开始应用首选APN...")
                                        currentSubInfo = info
                                        applyApnResult = applyFirstApnFromMDM(firstIndexApn!!, currentSubInfo)
                                        if (applyApnResult) {
                                            Logger.apnI("$TAG 首选APN应用成功")
                                        } else {
                                            Logger.apnE("$TAG 首选APN应用失败")
                                        }
                                    } else {
                                        // 其余apn只保存不做进一步处理
                                        val otherApn = apns[i]
                                        Logger.apnI("$TAG 💾 保存非首选APN (优先级${otherApn.priority}): ${otherApn.name} (${otherApn.apn})")
                                        val saveResult = apnManager.checkAndSaveApn(otherApn)
                                        if (saveResult.isSuccess) {
                                            Logger.apnI("$TAG   非首选APN保存成功")
                                        } else {
                                            Logger.apnE("$TAG   非首选APN保存失败")
                                        }
                                    }
                                }
                            }
                        } else {
                            Logger.apnW("$TAG 当前运营商($numeric)无匹配的APN配置")
                        }
                    }

                    // 保存剩余数据（其他运营商的APN配置）
                    Logger.apnW("$TAG 处理其他运营商的APN配置...")
                    val currentNumeric = apnManager.getCurrentOperatorNumeric()
                    val otherCarrierMap = numericMap.filterKeys { it != currentNumeric }
                    if (otherCarrierMap.isNotEmpty()) {
                        Logger.apnW("$TAG 发现 ${otherCarrierMap.size} 个其他运营商的APN配置")
                        otherCarrierMap.forEach { (numeric, apns) ->
                            Logger.apnW("$TAG   运营商 $numeric: ${apns.size} 个APN")
                        }
                        apnManager.checkAndSaveAllApn(otherCarrierMap)
                        Logger.apnW("$TAG 其他运营商APN配置保存完成")
                    } else {
                        Logger.apnW("$TAG 无其他运营商的APN配置需要保存")
                    }

                    Logger.apnI("$TAG ========== APN配置处理结果 ==========")
                    if (applyApnResult && firstIndexApn != null) {
                        Logger.apnI("$TAG 🎉 首选APN配置成功: ${firstIndexApn.name} (${firstIndexApn.apn})")
                        Logger.apnI("$TAG 开始应用网络配置...")
                        apnManager.applyApnNetwork()
                        kotlinx.coroutines.delay(5000) // Thread.sleep(5000) 的协程版本
                        Logger.apnI("$TAG APN网络配置应用完成")
                    } else {
                        Logger.apnE("$TAG APN配置处理失败，无法应用网络配置")

                        if (apnManager.isMobileDataActive()) {
                            Logger.apnI("$TAG 当前为移动数据网络，检测网络状态...")
                            firstIndexApn?.let { apn ->
                                checkNetworkAfterApnSwitch(apn)
                            } ?: run {
                                Logger.apnE("$TAG 首选APN为空，无法检测网络状态")
                            }
                        } else {
                            Logger.apnI("$TAG 当前为WIFI环境，仅添加APN...")
                            // 在WiFi环境下重新应用APN
                            firstIndexApn?.let { apn ->
                                applyApnResult = applyFirstApnFromMDM(apn, currentSubInfo)
                                Logger.apnI("$TAG applyApn Result = $applyApnResult")
                                // 上送状态
                                sendNetworkStatusUpload()
                            } ?: run {
                                Logger.apnE("$TAG 首选APN为空，无法重新应用APN")
                            }
                        }
                    }
                } else {
                    Logger.apnI("$TAG 终端未插入SIM卡，仅保存APN")
                    apnManager.checkAndSaveAllApn(numericMap)
                }

            } catch (e: Exception) {
                Logger.apnE("$TAG 处理当前运营商APN失败", e)
            }
        }
    }

    /**
     * 应用首选APN
     */
    private suspend fun applyFirstApnFromMDM(apnConfig: ApnConfig, subInfo: SubscriptionInfo? = null): Boolean {
        return try {
            Logger.apnI("$TAG 开始应用首选APN: ${apnConfig.name} (${apnConfig.apn})")

            // 1. 先查找是否存在相同的APN
            Logger.apn("$TAG   检查是否存在同名APN...")
            val findApn = apnManager.findApnByApnNameAndNumeric(apnConfig.apn, apnConfig.numeric)

            if (findApn != null) {
                // 2. 如果存在，先删除旧的
                Logger.apnI("$TAG   🗑️ 发现同名APN(ID:${findApn.id})，先删除...")
                val removeResult = apnManager.removeApnByOperatorAndId(findApn.id.toString())
                if (removeResult.isSuccess) {
                    Logger.apnI("$TAG   旧APN删除成功")
                } else {
                    Logger.apnE("$TAG   旧APN删除失败")
                }
            } else {
                Logger.apn("$TAG   未发现同名APN，直接添加")
            }

            // 3. 添加新的APN
            Logger.apnI("$TAG   ➕ 添加新APN配置...")
            val addResult = apnManager.addApnWithForce(apnConfig, true)
            val apnId = addResult.getOrNull() ?: -1
            if (apnId != -1) {
                Logger.apnI("$TAG   APN添加成功，ID: $apnId")
            } else {
                Logger.apnE("$TAG   APN添加失败")
                return false
            }

            // 4. 应用APN到指定SIM卡
            Logger.apnI("$TAG   应用APN到SIM卡...")
            var applyApnResult = false
            if (apnId != -1 && subInfo != null) {
                try {
                    // API版本适配：getSubscriptionId()需要API 22
                    val subscriptionId = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP_MR1) {
                        subInfo.subscriptionId
                    } else {
                        // API 21以下使用默认subscription ID
                        apnManager.getDefaultSubscriptionId()
                    }

                    if (subscriptionId != -1) {
                        Logger.apn("$TAG   目标SIM卡ID: $subscriptionId")
                        val applyResult = apnManager.applyApnBySubId(subscriptionId, apnId)
                        applyApnResult = applyResult.getOrNull() == true
                        if (applyApnResult) {
                            Logger.apnI("$TAG   APN应用到SIM卡成功")
                        } else {
                            Logger.apnE("$TAG   APN应用到SIM卡失败")
                        }
                    } else {
                        Logger.apnE("$TAG   无效的SIM卡ID")
                    }
                    Logger.apnI("$TAG 应用APN到SIM卡结果: $applyApnResult, subscriptionId=$subscriptionId")
                } catch (e: Exception) {
                    Logger.apnE("$TAG 应用APN到SIM卡失败", e)
                    applyApnResult = false
                }
            }

            applyApnResult

        } catch (e: Exception) {
            Logger.apnE("$TAG 应用首选APN失败", e)
            false
        }
    }

    /**
     * APN切换后检查网络状态
     */
    private fun checkNetworkAfterApnSwitch(apnConfig: ApnConfig) {
        apnScope.launch {
            try {
                // 应用APN网络配置
                apnManager.applyApnNetwork()

                // 等待网络切换完成
                kotlinx.coroutines.delay(5000)

                // 检查当前网络类型
                if (apnManager.isMobileDataActive()) {
                    Logger.apnI("$TAG 当前为移动数据网络，检测网络状态...")

                    // 执行网络连通性测试
                    val pingSuccess = apnManager.testNetworkConnectivity("*******", 5000)

                    if (pingSuccess) {
                        Logger.apnI("$TAG APN切换成功并且ping成功")
                        sendNetworkStatusUpload()
                    } else {
                        Logger.apnW("$TAG APN切换成功但ping失败，准备恢复到默认APN")

                        // 恢复默认APN
                        apnManager.restoreDefaultApn(apnManager.getDefaultSubscriptionId())
                        // 解除网络绑定
                        apnManager.restoreNormalNetwork()

                        // 重新添加所有APN
                        val parsedCacheMap = parseCacheDataToMap()
                        if (parsedCacheMap != null) {
                            apnManager.checkAndSaveAllApn(parsedCacheMap)
                        }

                        // 上送状态
                        Logger.apnI("$TAG ApnHandler 上送APN状态")
                        sendNetworkStatusUpload()
                    }
                } else {
                    Logger.apnI("$TAG 当前为WIFI环境，仅添加APN")
                  
                }

            } catch (e: Exception) {
                Logger.apnE("$TAG 网络状态检查失败", e)
            }
        }
    }

    /**
     * 解析缓存数据到Map
     */
    private fun parseCacheDataToMap(): Map<String, List<ApnConfig>>? {
        return try {
            val cacheData = getApnDataFromCache()
            if (cacheData != null) {
                val apnDatas = parseDataToApnList(cacheData)
                if (apnDatas.isNotEmpty()) {
                    // 根据numeric进行分组，符合当前运营商组的首个节点为优先级最高的apn，需要设置为首选
                    return groupApnsByNumeric(apnDatas)
                }
            }
            null
        } catch (e: Exception) {
            Logger.apnE("$TAG 解析缓存数据失败", e)
            null
        }
    }

    /**
     * 从缓存获取APN数据
     */
    private fun getApnDataFromCache(): JSONArray? {
        return try {
            val cachedData = getCachedApnData()
            if (cachedData.isNotEmpty()) {
                JSONArray(cachedData)
            } else {
                null
            }
        } catch (e: Exception) {
            Logger.apnE("$TAG 从缓存获取APN数据失败", e)
            null
        }
    }

    /**
     * 发送网络状态上报
     * C0904 - 网络状态上传
     */
    private fun sendNetworkStatusUpload() {
        try {
            Logger.apn("$TAG 发送C0904网络状态上报")

            // 调用WsMessageSender的uploadNetworkStatus方法
            // 该方法会收集WiFi选项、基站信息、WiFi信息并上报
            WsMessageSender.uploadNetworkStatus()

            Logger.apnI("$TAG C0904网络状态上报完成")
        } catch (e: Exception) {
            Logger.apnE("$TAG C0904网络状态上报失败", e)
        }
    }

}
