package com.bbpos.wiseapp.utils;

import android.content.IntentFilter;
import android.os.Environment;
import android.text.TextUtils;
import android.util.Log;

import com.bbpos.wiseapp.logger.BBLog;
import com.bbpos.wiseapp.system.api.Helper;
import com.bbpos.wiseapp.tms.utils.Constants;
import com.bbpos.wiseapp.tms.utils.ContextUtil;
import com.bbpos.wiseapp.tms.utils.Helpers;
import com.bbpos.wiseapp.tms.utils.IOUtils;
import com.bbpos.wiseapp.utils.CertificateUtils;
import com.bbpos.wiseapp.websocket.Constant;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.RandomAccessFile;
import java.nio.channels.FileChannel;
import java.util.Arrays;

public class CertificateTest {
    private static final String password = "a543210";
    private static final String alias = "wisemanager";

//    static void testFileSign() throws Exception {
//        String filePath = "D:/test/zhifubao10159.apk";
//        String sign = CertificateUtils.signFileToBase64(filePath, keyStorePath, alias, password);
//        System.err.println("生成签名：\r\n" + sign);
//        boolean result = CertificateUtils.validateFileSign(filePath, sign, certificatePath);
//        System.err.println("校验结果�?" + result);
//    }
//
//    //生成文件摘要 、对摘要进行签名、将签名加到文件末尾
//    static void testSignFile() throws Exception {
//    	String filePath = "D:/test/wangyizf.apk";
//    	String digest=CertificateUtils.getDigestOfFileBySHA1(filePath);
//    	System.err.println("生成摘要：\r\n" + digest);
//
//    	String sign = CertificateUtils.signToBase64(digest.getBytes(), keyStorePath, alias, password);
//    	System.err.println("生成摘要签名：\r\n" + sign);
//    	try {
//            FileOutputStream fos = new FileOutputStream (new File(filePath),true );
//            fos.write(sign.getBytes()) ;
//            fos.close ();
//        } catch (IOException e) {
//            e.printStackTrace();
//        }
//    }
    
    //验证签名
    public static boolean verfySignFile(String filePath) throws Exception {
	    //计算原文件的摘要
        boolean ret = false;
        try {
            File file = new File(filePath);
            String fileOriginalApkName = file.getName()+Constants.APK_ORIGINAL_SUFFIX;
            String fileOriginalApkPath = file.getParent() + File.separator + fileOriginalApkName;//签名后的文件
            File outfile = new File(fileOriginalApkPath); //源文件
            if (!outfile.getParentFile().exists()) {
                outfile.getParentFile().mkdirs();
                outfile.createNewFile();
            }
            try {
                if (!file.getCanonicalPath().startsWith(Environment.getExternalStorageDirectory().getCanonicalPath()) &&
                        !outfile.getCanonicalPath().startsWith(Environment.getExternalStorageDirectory().getCanonicalPath())) {
                    BBLog.e("CertificateTest", "Path Traversal");
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
            FileInputStream inFile = new FileInputStream(file);
            FileOutputStream outFile = new FileOutputStream(outfile);

            FileChannel inputChannel = null;
            FileChannel outputChannel = null;
            //还原文件（去掉文件末尾的签名内容）
            try {
                inputChannel = inFile.getChannel();
                outputChannel = outFile.getChannel();
                outputChannel.transferFrom(inputChannel, 0, inputChannel.size()-172);
                inputChannel.close();
                outputChannel.close();
            } catch (Exception e) {
                e.printStackTrace();
            } finally {
                inputChannel.close();
                outputChannel.close();
                IOUtils.closeInputStream(inFile);
                IOUtils.flushCloseOutputStream(outFile);
            }

            String digest=CertificateUtils.getDigestOfFileBySHA1(fileOriginalApkPath);
            byte[] buffer = new byte[172];
            //获取文件签名
            RandomAccessFile raf = null;
            try {
                raf = new RandomAccessFile(file, "r");
                raf.seek(raf.length() - 172);
                raf.read(buffer);
            }catch (IOException e){
                e.printStackTrace();
            }finally {
                if (raf != null) {
                    raf.close();
                    raf = null;
                }
            }
            String sign= new String(buffer);
            //验证文件签名
            String cerFilePath = "";
            if (new File("/bbpos").exists()) {
                cerFilePath = "/bbpos" + File.separator + Constants.CERT_FILE_PATH;
            }

            if (TextUtils.isEmpty(cerFilePath)) {
                cerFilePath = Environment.getExternalStorageDirectory().getPath()+"/Share"+File.separator+ContextUtil.getInstance().getPackageName()+File.separator+"mdm.cer";
            }
            ret=CertificateUtils.verifySign(digest.getBytes(), sign, cerFilePath);
            BBLog.e(BBLog.TAG, "验证摘要签名：" + ret);

            if (ret) {
                file.delete();
            } else {
                file.delete();
                outfile.delete();
            }
        } catch (Exception e) {
            e.printStackTrace();
            return ret;
        }

        return ret;
    }
}