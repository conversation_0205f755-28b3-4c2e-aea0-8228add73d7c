package com.dspread.mdm.service.modules.geofence

import kotlin.math.*

/**
 * 地理围栏计算器
 * 提供地理围栏相关的计算功能
 */
object GeofenceCalculator {
    
    /**
     * 计算两个地理坐标之间的距离（米）
     * 使用Haversine公式
     */
    fun calculateDistance(lat1: Double, lon1: Double, lat2: Double, lon2: Double): Double {
        val earthRadius = 6371000.0 // 地球半径（米）
        
        val dLat = Math.toRadians(lat2 - lat1)
        val dLon = Math.toRadians(lon2 - lon1)
        
        val a = sin(dLat / 2) * sin(dLat / 2) +
                cos(Math.toRadians(lat1)) * cos(Math.toRadians(lat2)) *
                sin(dLon / 2) * sin(dLon / 2)
        
        val c = 2 * atan2(sqrt(a), sqrt(1 - a))
        
        return earthRadius * c
    }
    
    /**
     * 检查点是否在圆形围栏内
     */
    fun isPointInCircle(
        pointLat: Double, 
        pointLon: Double, 
        centerLat: Double, 
        centerLon: Double, 
        radiusMeters: Double
    ): Boolean {
        val distance = calculateDistance(pointLat, pointLon, centerLat, centerLon)
        return distance <= radiusMeters
    }
    
    /**
     * 检查点是否在矩形围栏内
     */
    fun isPointInRectangle(
        pointLat: Double,
        pointLon: Double,
        northLat: Double,
        southLat: Double,
        eastLon: Double,
        westLon: Double
    ): Boolean {
        return pointLat >= southLat && pointLat <= northLat &&
               pointLon >= westLon && pointLon <= eastLon
    }
    
    /**
     * 检查点是否在多边形围栏内
     * 使用射线投射算法
     */
    fun isPointInPolygon(pointLat: Double, pointLon: Double, polygonPoints: List<Pair<Double, Double>>): Boolean {
        if (polygonPoints.size < 3) return false
        
        var inside = false
        var j = polygonPoints.size - 1
        
        for (i in polygonPoints.indices) {
            val xi = polygonPoints[i].first
            val yi = polygonPoints[i].second
            val xj = polygonPoints[j].first
            val yj = polygonPoints[j].second
            
            if (((yi > pointLon) != (yj > pointLon)) &&
                (pointLat < (xj - xi) * (pointLon - yi) / (yj - yi) + xi)) {
                inside = !inside
            }
            j = i
        }
        
        return inside
    }
    
    /**
     * 计算点到圆形围栏边界的最短距离
     */
    fun distanceToCircleBoundary(
        pointLat: Double,
        pointLon: Double,
        centerLat: Double,
        centerLon: Double,
        radiusMeters: Double
    ): Double {
        val distanceToCenter = calculateDistance(pointLat, pointLon, centerLat, centerLon)
        return abs(distanceToCenter - radiusMeters)
    }
    
    /**
     * 计算围栏的中心点
     */
    fun calculatePolygonCenter(polygonPoints: List<Pair<Double, Double>>): Pair<Double, Double> {
        if (polygonPoints.isEmpty()) return Pair(0.0, 0.0)
        
        var sumLat = 0.0
        var sumLon = 0.0
        
        for (point in polygonPoints) {
            sumLat += point.first
            sumLon += point.second
        }
        
        return Pair(sumLat / polygonPoints.size, sumLon / polygonPoints.size)
    }
    
    /**
     * 计算围栏的边界框
     */
    fun calculateBoundingBox(polygonPoints: List<Pair<Double, Double>>): Map<String, Double> {
        if (polygonPoints.isEmpty()) {
            return mapOf(
                "north" to 0.0,
                "south" to 0.0,
                "east" to 0.0,
                "west" to 0.0
            )
        }
        
        var north = polygonPoints[0].first
        var south = polygonPoints[0].first
        var east = polygonPoints[0].second
        var west = polygonPoints[0].second
        
        for (point in polygonPoints) {
            north = maxOf(north, point.first)
            south = minOf(south, point.first)
            east = maxOf(east, point.second)
            west = minOf(west, point.second)
        }
        
        return mapOf(
            "north" to north,
            "south" to south,
            "east" to east,
            "west" to west
        )
    }
    
    /**
     * 验证坐标是否有效
     */
    fun isValidCoordinate(lat: Double, lon: Double): Boolean {
        return lat >= -90.0 && lat <= 90.0 && lon >= -180.0 && lon <= 180.0
    }
    
    /**
     * 计算两点之间的方位角（度）
     */
    fun calculateBearing(lat1: Double, lon1: Double, lat2: Double, lon2: Double): Double {
        val dLon = Math.toRadians(lon2 - lon1)
        val lat1Rad = Math.toRadians(lat1)
        val lat2Rad = Math.toRadians(lat2)
        
        val y = sin(dLon) * cos(lat2Rad)
        val x = cos(lat1Rad) * sin(lat2Rad) - sin(lat1Rad) * cos(lat2Rad) * cos(dLon)
        
        val bearingRad = atan2(y, x)
        return (Math.toDegrees(bearingRad) + 360) % 360
    }
    
    /**
     * 根据起点、方位角和距离计算终点坐标
     */
    fun calculateDestination(lat: Double, lon: Double, bearingDegrees: Double, distanceMeters: Double): Pair<Double, Double> {
        val earthRadius = 6371000.0
        val bearingRad = Math.toRadians(bearingDegrees)
        val latRad = Math.toRadians(lat)
        val lonRad = Math.toRadians(lon)
        
        val destLatRad = asin(sin(latRad) * cos(distanceMeters / earthRadius) +
                             cos(latRad) * sin(distanceMeters / earthRadius) * cos(bearingRad))
        
        val destLonRad = lonRad + atan2(sin(bearingRad) * sin(distanceMeters / earthRadius) * cos(latRad),
                                       cos(distanceMeters / earthRadius) - sin(latRad) * sin(destLatRad))
        
        return Pair(Math.toDegrees(destLatRad), Math.toDegrees(destLonRad))
    }
    
    /**
     * 格式化距离显示
     */
    fun formatDistance(distanceMeters: Double): String {
        return when {
            distanceMeters < 1000 -> "${distanceMeters.toInt()}米"
            distanceMeters < 10000 -> String.format("%.1f公里", distanceMeters / 1000)
            else -> "${(distanceMeters / 1000).toInt()}公里"
        }
    }
    
    /**
     * 地理围栏类型枚举
     */
    enum class GeofenceType {
        CIRCLE,     // 圆形
        RECTANGLE,  // 矩形
        POLYGON     // 多边形
    }
    
    /**
     * 地理围栏数据类
     */
    data class GeofenceInfo(
        val id: String,
        val name: String,
        val type: GeofenceType,
        val centerLat: Double,
        val centerLon: Double,
        val radiusMeters: Double = 0.0,
        val polygonPoints: List<Pair<Double, Double>> = emptyList(),
        val isActive: Boolean = true
    )
    
    /**
     * 检查点是否在指定地理围栏内
     */
    fun isPointInGeofence(pointLat: Double, pointLon: Double, geofence: GeofenceInfo): Boolean {
        if (!geofence.isActive) return false
        if (!isValidCoordinate(pointLat, pointLon)) return false
        
        return when (geofence.type) {
            GeofenceType.CIRCLE -> isPointInCircle(
                pointLat, pointLon,
                geofence.centerLat, geofence.centerLon,
                geofence.radiusMeters
            )
            GeofenceType.POLYGON -> isPointInPolygon(pointLat, pointLon, geofence.polygonPoints)
            GeofenceType.RECTANGLE -> {
                val bbox = calculateBoundingBox(geofence.polygonPoints)
                isPointInRectangle(
                    pointLat, pointLon,
                    bbox["north"] ?: 0.0, bbox["south"] ?: 0.0,
                    bbox["east"] ?: 0.0, bbox["west"] ?: 0.0
                )
            }
        }
    }
}
