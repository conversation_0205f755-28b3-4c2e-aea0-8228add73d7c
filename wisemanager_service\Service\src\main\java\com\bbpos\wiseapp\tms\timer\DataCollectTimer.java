package com.bbpos.wiseapp.tms.timer;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.net.ConnectivityManager;

import com.bbpos.wiseapp.logger.BBLog;
import com.bbpos.wiseapp.tms.traffic.utils.DataCollectDBHelper;
import com.bbpos.wiseapp.tms.traffic.utils.TrafficHelpers;
import com.bbpos.wiseapp.tms.utils.BroadcastActions;
import com.bbpos.wiseapp.tms.utils.Constants;
import com.bbpos.wiseapp.tms.utils.Helpers;

public class DataCollectTimer extends BroadcastReceiver{
	private static final String TAG = "DataCollectReceiver";
	
	@Override
	public void onReceive(final Context context, Intent intent) {
		String action = intent.getAction();
		BBLog.v(BBLog.TAG, "onReceive() -> " + action);
		if (ConnectivityManager.CONNECTIVITY_ACTION.equals(action)) {
			//网络连上了
			if (Helpers.isOnline(context))
				TrafficHelpers.startDataCollectService(context);
        }
	}
}
