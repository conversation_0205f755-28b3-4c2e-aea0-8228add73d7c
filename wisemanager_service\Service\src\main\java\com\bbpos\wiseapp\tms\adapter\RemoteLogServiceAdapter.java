package com.bbpos.wiseapp.tms.adapter;

import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.ServiceConnection;
import android.os.IBinder;
import android.os.RemoteException;
import android.util.Log;

import com.bbpos.wiseapp.logger.BBLog;
import com.bbpos.wiseapp.tms.utils.ContextUtil;
import com.bbpos.wiseapp.tms.utils.Constants;
import com.bbpos.wiseapp.tms.utils.Helpers;

public class RemoteLogServiceAdapter {
	private static String REMOTE_LOG_SERVICE_MANAGER_ACTION = "com_android_remoteLogService";
	private static RemoteLogServiceManager logManager;

	public static void bindService(final BindServiceSuccess bindListener) {
		try {
			if (logManager == null) {
				ServiceConnection mConnection = new ServiceConnection() {
					public void onServiceConnected(ComponentName className, IBinder service) {
						logManager = RemoteLogServiceManager.Stub.asInterface(service);
						bindListener.onBindSuccess();
					}

					public void onServiceDisconnected(ComponentName className) {
						logManager = null;
					}
				};
				String action = REMOTE_LOG_SERVICE_MANAGER_ACTION;
				String pkgName = Helpers.getPkgNameByServiceName(action);

				Intent serviceIntent = new Intent(action);
				serviceIntent.setPackage(pkgName);
				ContextUtil.getInstance().bindService(serviceIntent, mConnection, Context.BIND_AUTO_CREATE);
			} else {
				bindListener.onBindSuccess();
			}
		} catch (Exception e) {
			BBLog.e(BBLog.TAG, "RemoteLogServiceAdapter bindService failed."+e.getMessage());
		}
	}

	public static void setLogEnable1(final boolean enable) {
		bindService(new BindServiceSuccess() {
			@Override
			public void onBindSuccess() {
				try {
					BBLog.v(BBLog.TAG, "setLogEnable "+enable);
					logManager.setLogEnable(enable);
				} catch (RemoteException e) {
					BBLog.e(BBLog.TAG, "setLogEnable error."+e.getMessage());
				} catch (Exception e) {
					e.printStackTrace();
				}
			}
		});
	}

	public static void bakupNow() {
		bindService(new BindServiceSuccess() {
			@Override
			public void onBindSuccess() {
				try {
					BBLog.v(BBLog.TAG, "bakupNow.");
					logManager.bakupNow();
				} catch (RemoteException e) {
					BBLog.e(BBLog.TAG, "bakupNow error."+e.getMessage());
				} catch (Exception e) {
					e.printStackTrace();
				}
			}
		});
	}
	
	public static String getLogDir() {
		try {
			return logManager.getLogDir();
		} catch (RemoteException e) {
			BBLog.e(BBLog.TAG, e.getMessage(), e);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return null;
	}

	public interface BindServiceSuccess {
		public void onBindSuccess();
	}
}
