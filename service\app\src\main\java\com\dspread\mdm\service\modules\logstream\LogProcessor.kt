package com.dspread.mdm.service.modules.logstream

import android.content.Context
import com.dspread.mdm.service.modules.logstream.model.LogFileInfo
import com.dspread.mdm.service.modules.logstream.model.LogSourceType
import com.dspread.mdm.service.utils.log.Logger
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.*
import java.security.MessageDigest
import java.util.zip.GZIPOutputStream
import java.util.zip.ZipEntry
import java.util.zip.ZipOutputStream
import javax.crypto.Cipher
import javax.crypto.KeyGenerator
import javax.crypto.SecretKey
import javax.crypto.spec.SecretKeySpec

/**
 * 日志处理器
 * 负责日志文件的压缩、分片和加密处理
 */
class LogProcessor(
    private val context: Context
) {
    
    companion object {
        private const val TAG = "[LogProcessor]"
        private const val CHUNK_BUFFER_SIZE = 8192 // 8KB缓冲区
        private const val DEFAULT_CHUNK_SIZE = 1024 * 1024 // 1MB分片大小
        private const val ENCRYPTION_ALGORITHM = "AES"
        private const val ENCRYPTION_TRANSFORMATION = "AES/ECB/PKCS5Padding"
    }
    
    private val processDirectory by lazy {
        File(context.cacheDir, "processed_logs").apply {
            if (!exists()) mkdirs()
        }
    }
    
    /**
     * 压缩日志文件
     */
    suspend fun compressFile(
        inputFile: File,
        compressionType: CompressionType = CompressionType.GZIP
    ): Result<LogFileInfo> {
        return compressFile(inputFile, null, compressionType)
    }

    /**
     * 压缩日志文件到指定目录
     */
    suspend fun compressFile(
        inputFile: File,
        outputDir: File?,
        compressionType: CompressionType = CompressionType.GZIP
    ): Result<LogFileInfo> {
        return compressFile(inputFile, outputDir, null, compressionType)
    }

    /**
     * 压缩日志文件到指定目录和文件名
     */
    suspend fun compressFile(
        inputFile: File,
        outputDir: File?,
        outputFileName: String?,
        compressionType: CompressionType = CompressionType.GZIP
    ): Result<LogFileInfo> {
        return withContext(Dispatchers.IO) {
            try {
                Logger.logStream("$TAG 开始压缩文件: ${inputFile.name}")

                // 检查输入文件是否存在
                if (!inputFile.exists()) {
                    Logger.logStreamE("$TAG 输入文件不存在: ${inputFile.absolutePath}")
                    return@withContext Result.failure(java.io.FileNotFoundException("输入文件不存在: ${inputFile.absolutePath}"))
                }

                // 检查文件大小
                if (inputFile.length() == 0L) {
                    Logger.logStreamW("$TAG 输入文件为空: ${inputFile.name}")
                    return@withContext Result.failure(IllegalArgumentException("输入文件为空: ${inputFile.name}"))
                }

                // 确定输出目录和文件
                val targetDir = outputDir ?: processDirectory
                val fileName = outputFileName ?: "${inputFile.nameWithoutExtension}.${compressionType.extension}"
                val outputFile = File(targetDir, fileName)

                // 确保输出目录存在
                if (!targetDir.exists()) {
                    targetDir.mkdirs()
                }

                when (compressionType) {
                    CompressionType.GZIP -> compressWithGzip(inputFile, outputFile)
                    CompressionType.ZIP -> compressWithZip(inputFile, outputFile)
                }
                
                val originalSize = inputFile.length()
                val compressedSize = outputFile.length()
                val compressionRatio = if (originalSize > 0) {
                    (compressedSize.toFloat() / originalSize.toFloat()) * 100f
                } else {
                    100f
                }
                
                Logger.logStream("$TAG 压缩完成: ${inputFile.name} -> ${outputFile.name}")
                Logger.logStream("$TAG 压缩比: ${String.format("%.2f", compressionRatio)}% (${originalSize}B -> ${compressedSize}B)")
                
                val fileInfo = LogFileInfo(
                    path = outputFile.absolutePath,
                    name = outputFile.name,
                    size = compressedSize,
                    lastModified = outputFile.lastModified(),
                    compressed = true,
                    checksum = calculateChecksum(outputFile),
                    source = LogSourceType.CUSTOM
                )
                
                Result.success(fileInfo)
                
            } catch (e: Exception) {
                Logger.logStreamE("$TAG 压缩文件失败: ${inputFile.name}", e)
                Result.failure(e)
            }
        }
    }
    
    /**
     * 使用GZIP压缩
     */
    private fun compressWithGzip(inputFile: File, outputFile: File) {
        FileInputStream(inputFile).use { fis ->
            FileOutputStream(outputFile).use { fos ->
                GZIPOutputStream(fos).use { gzos ->
                    val buffer = ByteArray(CHUNK_BUFFER_SIZE)
                    var bytesRead: Int
                    
                    while (fis.read(buffer).also { bytesRead = it } != -1) {
                        gzos.write(buffer, 0, bytesRead)
                    }
                }
            }
        }
    }
    
    /**
     * 使用ZIP压缩
     */
    private fun compressWithZip(inputFile: File, outputFile: File) {
        FileInputStream(inputFile).use { fis ->
            FileOutputStream(outputFile).use { fos ->
                ZipOutputStream(fos).use { zos ->
                    val entry = ZipEntry(inputFile.name)
                    zos.putNextEntry(entry)
                    
                    val buffer = ByteArray(CHUNK_BUFFER_SIZE)
                    var bytesRead: Int
                    
                    while (fis.read(buffer).also { bytesRead = it } != -1) {
                        zos.write(buffer, 0, bytesRead)
                    }
                    
                    zos.closeEntry()
                }
            }
        }
    }
    
    /**
     * 加密文件
     */
    suspend fun encryptFile(
        inputFile: File,
        secretKey: SecretKey? = null
    ): Result<Pair<LogFileInfo, SecretKey>> {
        return withContext(Dispatchers.IO) {
            try {
                Logger.logStream("$TAG 开始加密文件: ${inputFile.name}")
                
                val key = secretKey ?: generateSecretKey()
                val cipher = Cipher.getInstance(ENCRYPTION_TRANSFORMATION)
                cipher.init(Cipher.ENCRYPT_MODE, key)
                
                val outputFile = File(processDirectory, "${inputFile.nameWithoutExtension}.enc")
                
                FileInputStream(inputFile).use { fis ->
                    FileOutputStream(outputFile).use { fos ->
                        val buffer = ByteArray(CHUNK_BUFFER_SIZE)
                        var bytesRead: Int
                        
                        while (fis.read(buffer).also { bytesRead = it } != -1) {
                            val encryptedBytes = if (bytesRead == CHUNK_BUFFER_SIZE) {
                                cipher.update(buffer)
                            } else {
                                cipher.update(buffer, 0, bytesRead)
                            }
                            
                            encryptedBytes?.let { fos.write(it) }
                        }
                        
                        // 写入最终块
                        val finalBytes = cipher.doFinal()
                        fos.write(finalBytes)
                    }
                }
                
                val fileInfo = LogFileInfo(
                    path = outputFile.absolutePath,
                    name = outputFile.name,
                    size = outputFile.length(),
                    lastModified = outputFile.lastModified(),
                    encrypted = true,
                    checksum = calculateChecksum(outputFile),
                    source = LogSourceType.CUSTOM
                )
                
                Logger.logStream("$TAG 加密完成: ${inputFile.name} -> ${outputFile.name}")
                Result.success(Pair(fileInfo, key))
                
            } catch (e: Exception) {
                Logger.logStreamE("$TAG 加密文件失败: ${inputFile.name}", e)
                Result.failure(e)
            }
        }
    }
    
    /**
     * 分片文件
     */
    suspend fun splitFile(
        inputFile: File,
        chunkSize: Long = DEFAULT_CHUNK_SIZE.toLong()
    ): Result<List<FileChunk>> {
        return withContext(Dispatchers.IO) {
            try {
                Logger.logStream("$TAG 开始分片文件: ${inputFile.name}, 分片大小: ${chunkSize}B")
                
                val chunks = mutableListOf<FileChunk>()
                val totalSize = inputFile.length()
                val totalChunks = ((totalSize + chunkSize - 1) / chunkSize).toInt()
                
                FileInputStream(inputFile).use { fis ->
                    var chunkIndex = 0
                    var bytesRead = 0L
                    
                    while (bytesRead < totalSize) {
                        val currentChunkSize = minOf(chunkSize, totalSize - bytesRead)
                        val chunkFile = File(processDirectory, "${inputFile.nameWithoutExtension}.chunk.$chunkIndex")
                        
                        FileOutputStream(chunkFile).use { fos ->
                            val buffer = ByteArray(CHUNK_BUFFER_SIZE)
                            var remainingBytes = currentChunkSize
                            
                            while (remainingBytes > 0) {
                                val toRead = minOf(buffer.size.toLong(), remainingBytes).toInt()
                                val read = fis.read(buffer, 0, toRead)
                                
                                if (read == -1) break
                                
                                fos.write(buffer, 0, read)
                                remainingBytes -= read
                                bytesRead += read
                            }
                        }
                        
                        val chunk = FileChunk(
                            index = chunkIndex,
                            filePath = chunkFile.absolutePath,
                            size = chunkFile.length(),
                            checksum = calculateChecksum(chunkFile),
                            startOffset = chunkIndex * chunkSize,
                            endOffset = bytesRead - 1
                        )
                        
                        chunks.add(chunk)
                        chunkIndex++
                        
                        Logger.logStream("$TAG 创建分片 $chunkIndex/$totalChunks: ${chunkFile.name}")
                    }
                }
                
                Logger.logStream("$TAG 分片完成: ${inputFile.name}, 共 ${chunks.size} 个分片")
                Result.success(chunks)
                
            } catch (e: Exception) {
                Logger.logStreamE("$TAG 分片文件失败: ${inputFile.name}", e)
                Result.failure(e)
            }
        }
    }
    
    /**
     * 合并分片文件
     */
    suspend fun mergeChunks(
        chunks: List<FileChunk>,
        outputFile: File
    ): Result<LogFileInfo> {
        return withContext(Dispatchers.IO) {
            try {
                Logger.logStream("$TAG 开始合并分片: ${chunks.size} 个分片 -> ${outputFile.name}")
                
                // 按索引排序分片
                val sortedChunks = chunks.sortedBy { it.index }
                
                FileOutputStream(outputFile).use { fos ->
                    sortedChunks.forEach { chunk ->
                        val chunkFile = File(chunk.filePath)
                        if (chunkFile.exists()) {
                            FileInputStream(chunkFile).use { fis ->
                                val buffer = ByteArray(CHUNK_BUFFER_SIZE)
                                var bytesRead: Int
                                
                                while (fis.read(buffer).also { bytesRead = it } != -1) {
                                    fos.write(buffer, 0, bytesRead)
                                }
                            }
                            Logger.logStream("$TAG 合并分片: ${chunk.index}")
                        } else {
                            throw FileNotFoundException("分片文件不存在: ${chunk.filePath}")
                        }
                    }
                }
                
                val fileInfo = LogFileInfo(
                    path = outputFile.absolutePath,
                    name = outputFile.name,
                    size = outputFile.length(),
                    lastModified = outputFile.lastModified(),
                    checksum = calculateChecksum(outputFile),
                    source = LogSourceType.CUSTOM
                )
                
                Logger.logStream("$TAG 合并完成: ${outputFile.name}")
                Result.success(fileInfo)
                
            } catch (e: Exception) {
                Logger.logStreamE("$TAG 合并分片失败", e)
                Result.failure(e)
            }
        }
    }
    
    /**
     * 验证文件完整性
     */
    suspend fun verifyFileIntegrity(
        file: File,
        expectedChecksum: String
    ): Result<Boolean> {
        return withContext(Dispatchers.IO) {
            try {
                val actualChecksum = calculateChecksum(file)
                val isValid = actualChecksum.equals(expectedChecksum, ignoreCase = true)
                
                if (isValid) {
                    Logger.logStream("$TAG 文件完整性验证通过: ${file.name}")
                } else {
                    Logger.logStreamW("$TAG 文件完整性验证失败: ${file.name}")
                    Logger.logStreamW("$TAG 期望: $expectedChecksum, 实际: $actualChecksum")
                }
                
                Result.success(isValid)
            } catch (e: Exception) {
                Logger.logStreamE("$TAG 文件完整性验证异常: ${file.name}", e)
                Result.failure(e)
            }
        }
    }
    
    /**
     * 计算文件校验和
     */
    private fun calculateChecksum(file: File): String {
        return try {
            val digest = MessageDigest.getInstance("SHA-256")
            
            FileInputStream(file).use { fis ->
                val buffer = ByteArray(CHUNK_BUFFER_SIZE)
                var bytesRead: Int
                
                while (fis.read(buffer).also { bytesRead = it } != -1) {
                    digest.update(buffer, 0, bytesRead)
                }
            }
            
            digest.digest().joinToString("") { "%02x".format(it) }
        } catch (e: Exception) {
            Logger.logStreamE("$TAG 计算校验和失败: ${file.name}", e)
            ""
        }
    }
    
    /**
     * 生成密钥
     */
    private fun generateSecretKey(): SecretKey {
        val keyGenerator = KeyGenerator.getInstance(ENCRYPTION_ALGORITHM)
        keyGenerator.init(256) // 256位密钥
        return keyGenerator.generateKey()
    }
    
    /**
     * 从字节数组创建密钥
     */
    fun createSecretKey(keyBytes: ByteArray): SecretKey {
        return SecretKeySpec(keyBytes, ENCRYPTION_ALGORITHM)
    }
    
    /**
     * 清理处理目录
     */
    suspend fun cleanupProcessDirectory(): Result<Int> {
        return withContext(Dispatchers.IO) {
            try {
                var deletedCount = 0
                
                processDirectory.listFiles()?.forEach { file ->
                    if (file.delete()) {
                        deletedCount++
                    }
                }
                
                Logger.logStream("$TAG 清理处理目录完成，删除了 $deletedCount 个文件")
                Result.success(deletedCount)
            } catch (e: Exception) {
                Logger.logStreamE("$TAG 清理处理目录失败", e)
                Result.failure(e)
            }
        }
    }
    
    /**
     * 获取处理目录大小
     */
    fun getProcessDirectorySize(): Long {
        return try {
            processDirectory.walkTopDown()
                .filter { it.isFile }
                .map { it.length() }
                .sum()
        } catch (e: Exception) {
            Logger.logStreamE("$TAG 计算处理目录大小失败", e)
            0L
        }
    }
    
    /**
     * 批量处理文件
     */
    suspend fun batchProcess(
        files: List<File>,
        config: ProcessConfig
    ): Result<List<ProcessResult>> {
        return try {
            Logger.logStream("$TAG 开始批量处理 ${files.size} 个文件")
            
            val results = mutableListOf<ProcessResult>()
            
            files.forEach { file ->
                try {
                    var currentFile = file
                    var processedInfo: LogFileInfo? = null
                    var secretKey: SecretKey? = null
                    var chunks: List<FileChunk>? = null
                    
                    // 压缩
                    if (config.compressionEnabled) {
                        val compressResult = compressFile(currentFile, config.compressionType)
                        if (compressResult.isSuccess) {
                            processedInfo = compressResult.getOrNull()
                            currentFile = File(processedInfo!!.path)
                        }
                    }
                    
                    // 加密
                    if (config.encryptionEnabled) {
                        val encryptResult = encryptFile(currentFile, config.secretKey)
                        if (encryptResult.isSuccess) {
                            val (fileInfo, key) = encryptResult.getOrNull()!!
                            processedInfo = fileInfo
                            secretKey = key
                            currentFile = File(fileInfo.path)
                        }
                    }
                    
                    // 分片
                    if (config.chunkingEnabled) {
                        val splitResult = splitFile(currentFile, config.chunkSize)
                        if (splitResult.isSuccess) {
                            chunks = splitResult.getOrNull()
                        }
                    }
                    
                    results.add(
                        ProcessResult(
                            originalFile = file,
                            processedFile = processedInfo,
                            secretKey = secretKey,
                            chunks = chunks,
                            success = true,
                            error = null
                        )
                    )
                    
                } catch (e: Exception) {
                    Logger.logStreamE("$TAG 处理文件失败: ${file.name}", e)
                    results.add(
                        ProcessResult(
                            originalFile = file,
                            processedFile = null,
                            secretKey = null,
                            chunks = null,
                            success = false,
                            error = e.message
                        )
                    )
                }
            }
            
            Logger.logStream("$TAG 批量处理完成，成功: ${results.count { it.success }}, 失败: ${results.count { !it.success }}")
            Result.success(results)
            
        } catch (e: Exception) {
            Logger.logStreamE("$TAG 批量处理异常", e)
            Result.failure(e)
        }
    }
}

/**
 * 压缩类型枚举
 */
enum class CompressionType(val extension: String) {
    GZIP("gz"),
    ZIP("zip")
}

/**
 * 文件分片数据类
 */
data class FileChunk(
    val index: Int,                          // 分片索引
    val filePath: String,                    // 分片文件路径
    val size: Long,                          // 分片大小
    val checksum: String,                    // 分片校验和
    val startOffset: Long,                   // 起始偏移
    val endOffset: Long                      // 结束偏移
)

/**
 * 处理配置数据类
 */
data class ProcessConfig(
    val compressionEnabled: Boolean = true,
    val compressionType: CompressionType = CompressionType.GZIP,
    val encryptionEnabled: Boolean = false,
    val secretKey: SecretKey? = null,
    val chunkingEnabled: Boolean = true,
    val chunkSize: Long = 1024 * 1024L // 1MB
)

/**
 * 处理结果数据类
 */
data class ProcessResult(
    val originalFile: File,
    val processedFile: LogFileInfo?,
    val secretKey: SecretKey?,
    val chunks: List<FileChunk>?,
    val success: Boolean,
    val error: String?
)
