package com.bbpos.wiseapp.tms.service;

import android.annotation.SuppressLint;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.os.BatteryManager;
import android.os.Build;
import android.os.Handler;
import android.os.Looper;

import com.bbpos.wiseapp.logger.BBLog;
import com.bbpos.wiseapp.service.WakeLockService;
import com.bbpos.wiseapp.system.api.DeviceInfoApi;
import com.bbpos.wiseapp.tms.listener.device.HardwareInfo;
import com.bbpos.wiseapp.tms.network.HttpUtils;
import com.bbpos.wiseapp.tms.utils.Constants;
import com.bbpos.wiseapp.tms.utils.ContextUtil;
import com.bbpos.wiseapp.tms.utils.IOUtils;
import com.bbpos.wiseapp.tms.utils.ParameterFactory;
import com.bbpos.wiseapp.tms.utils.ParameterName;
import com.bbpos.wiseapp.tms.utils.WSCallType;
import com.bbpos.wiseapp.utils.SystemUtils;
import com.bbpos.wiseapp.websocket.WebSocketCenter;
import com.bbpos.wiseapp.websocket.WebSocketSender;

import org.json.JSONObject;

import java.io.BufferedReader;
import java.io.FileReader;
import java.util.List;
import java.util.Locale;

import com.bbpos.wiseapp.sdk.app.UsageStats;

@SuppressLint("SimpleDateFormat")
public class HardwareDetectUploadSerivce extends WakeLockService {
    private static int cpu_over_count = 0;
    private static int mem_over_count = 0;
    private static int rom_over_count = 0;
	public HardwareDetectUploadSerivce() {
		super("HardwareDetectUploadSerivce");
	}

	@Override
	public void onDestroy() {
		super.onDestroy();
	}

	@Override
	protected void onHandleIntent(Intent intent) {
		try {
		    boolean sendC0903 = false;

            String usedRomMemory = "";
            String totalRomMemory = "";
			String rate = SystemUtils.getCPURateDesc_All();
			if (Float.valueOf(rate) > 90) {
                cpu_over_count ++;
                BBLog.d(BBLog.TAG,"cpu_over_count = " + cpu_over_count + "，监测到CPU使用率超过阈值：" + rate + "%");
                if (cpu_over_count==1 || cpu_over_count >= 10) {
                    if (cpu_over_count>=10) {
                        cpu_over_count = 0;
                    } else {
                        sendC0903 = true;
                    }
                }
			} else {
			    if (cpu_over_count > 0) {
                    BBLog.d(BBLog.TAG,"cpu_over_count = " + cpu_over_count + "，监测到CPU使用率降回阈值：" + rate + "%");
                    sendC0903 = true;
                }
                cpu_over_count = 0;
			}

            HardwareInfo.queryWithStorageManager(ContextUtil.getInstance());
            if (Build.VERSION.SDK_INT < Build.VERSION_CODES.M) {//小于6.0
                usedRomMemory = HardwareInfo.getUnit(HardwareInfo.usedBytes, 1024);
                totalRomMemory = HardwareInfo.getUnit(HardwareInfo.totalBytes, 1024);
            } else {
                usedRomMemory = HardwareInfo.getUnit(HardwareInfo.usedBytes, 1000);
                totalRomMemory = HardwareInfo.getUnit(HardwareInfo.totalBytes, 1000);
            }
			int rom_rate = (int)(HardwareInfo.usedBytes*100/HardwareInfo.totalBytes);
            if (rom_rate > 90) {
                rom_over_count ++;
                BBLog.d(BBLog.TAG,"rom_over_count = " + rom_over_count + "，监测到容量使用率超过阈值：" + rom_rate + "%");
                if (rom_over_count==1 || rom_over_count>=10) {
                    if (rom_over_count>=10) {
                        rom_over_count = 0;
                    } else {
                        sendC0903 = true;
                    }
                }
            } else {
                if (rom_over_count > 0) {
                    BBLog.d(BBLog.TAG,"rom_over_count = " + rom_over_count + "，监测到容量使用率降回阈值：" + rom_rate + "%");
                    sendC0903 = true;
                }
                rom_over_count = 0;
            }

            String str1 = "/proc/meminfo";
            String str2;
            String[] arrayOfString;
            long totalMemory = 0;
            long freeMemory = 0;
            long availableMemory = 0;
            long buffers = 0;
            long cached = 0;

            BufferedReader localBufferedReader = null;
            try {
                FileReader localFileReader = new FileReader(str1);
                localBufferedReader = new BufferedReader(localFileReader, 8192);
                /**读取meminfo第一行，当前系统总内存大小 */
                str2 = localBufferedReader.readLine();
                arrayOfString = str2.split("\\s+");
                totalMemory = Integer.valueOf(arrayOfString[1]).intValue();
                str2 = localBufferedReader.readLine();
                arrayOfString = str2.split("\\s+");
                freeMemory = Integer.valueOf(arrayOfString[1]).intValue();
                str2 = localBufferedReader.readLine();
                arrayOfString = str2.split("\\s+");
                availableMemory = Integer.valueOf(arrayOfString[1]).intValue();
                str2 = localBufferedReader.readLine();
                arrayOfString = str2.split("\\s+");
                buffers = Integer.valueOf(arrayOfString[1]).intValue();
                str2 = localBufferedReader.readLine();
                arrayOfString = str2.split("\\s+");
                cached = Integer.valueOf(arrayOfString[1]).intValue();
            } catch (Exception e) {
                e.printStackTrace();
            } finally {
                try {
                    if (localBufferedReader != null) {
                        localBufferedReader.close();
                        localBufferedReader = null;
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
            /**将获取当前系统总内存规格化为MB*/
            String totalMemStr = String.format(Locale.US, " %.2f%s ", totalMemory / (1024*1024.00), "GB");
            String usedMemStr = String.format(Locale.US, " %.2f%s ", (totalMemory-freeMemory-buffers-cached) / (1024*1024.00), "GB");
            BBLog.d(BBLog.TAG,"内存总容量为：" + totalMemStr);
            BBLog.d(BBLog.TAG,"内存使用量为：" + usedMemStr);
            int mem_rate = (int)((totalMemory-freeMemory-buffers-cached)*100/totalMemory);
            if (mem_rate > 80) {
                mem_over_count ++;
                BBLog.d(BBLog.TAG,"mem_over_count = " + mem_over_count + "，监测到内存使用率超过阈值：" + mem_rate + "%");
                if (mem_over_count==1 || mem_over_count>=10) {
                    if (mem_over_count>=10) {
                        mem_over_count = 0;
                    } else {
                        sendC0903 = true;
                    }
                }
            } else {
                if (mem_over_count > 0) {
                    BBLog.d(BBLog.TAG,"mem_over_count = " + mem_over_count + "，监测到内存使用率降回阈值：" + mem_rate + "%");
                    sendC0903 = true;
                }
                mem_over_count = 0;
            }

            if (sendC0903) {
                BBLog.e(BBLog.TAG, "监测到CPU/内存/存储 使用量持续超阈值，上送C0903");
                WebSocketSender.C0903_DataInfoUpload(rate, usedRomMemory+"/"+totalRomMemory, usedMemStr+"/"+totalMemStr);
            }

		} catch (Exception e) {
			e.printStackTrace();
		}
	}
}
