package com.dspread.mdm.service.modules.wifi

import com.dspread.mdm.service.utils.log.Logger
import kotlinx.coroutines.delay
import java.util.concurrent.TimeoutException

/**
 * WiFi模块错误处理器
 * 提供统一的异常处理、错误码定义和错误恢复策略
 */
object WifiErrorHandler {
    
    /**
     * WiFi错误码枚举
     */
    enum class WifiErrorCode(val code: String, val message: String) {
        // 连接相关错误
        CONNECTION_FAILED("WIFI_001", "WiFi连接失败"),
        CONNECTION_TIMEOUT("WIFI_002", "WiFi连接超时"),
        AUTHENTICATION_FAILED("WIFI_003", "WiFi认证失败"),
        NETWORK_UNREACHABLE("WIFI_004", "网络不可达"),
        
        // 配置相关错误
        INVALID_CONFIGURATION("WIFI_005", "WiFi配置无效"),
        INVALID_PASSWORD("WIFI_006", "WiFi密码无效"),
        INVALID_SECURITY_TYPE("WIFI_007", "WiFi安全类型无效"),
        INVALID_PROXY_CONFIG("WIFI_008", "代理配置无效"),
        
        // 扫描相关错误
        SCAN_FAILED("WIFI_009", "WiFi扫描失败"),
        WIFI_NOT_IN_RANGE("WIFI_010", "WiFi不在信号范围内"),
        SCAN_TIMEOUT("WIFI_011", "WiFi扫描超时"),
        
        // 系统相关错误
        WIFI_DISABLED("WIFI_012", "WiFi功能未启用"),
        PERMISSION_DENIED("WIFI_013", "WiFi权限被拒绝"),
        SYSTEM_ERROR("WIFI_014", "系统错误"),
        
        // 隐藏网络相关错误
        HIDDEN_NETWORK_CONFIG_ERROR("WIFI_015", "隐藏网络配置错误"),
        HIDDEN_NETWORK_NOT_FOUND("WIFI_016", "隐藏网络未找到"),
        
        // 故障恢复相关错误
        RECOVERY_FAILED("WIFI_017", "故障恢复失败"),
        NO_AVAILABLE_PROFILES("WIFI_018", "没有可用的WiFi配置"),
        CACHE_WIFI_INVALID("WIFI_019", "缓存WiFi配置无效"),
        
        // 通用错误
        UNKNOWN_ERROR("WIFI_999", "未知错误")
    }
    
    /**
     * WiFi异常类
     */
    class WifiException(
        val errorCode: WifiErrorCode,
        val ssid: String? = null,
        val details: String? = null,
        cause: Throwable? = null
    ) : Exception("${errorCode.message}${if (ssid != null) ": $ssid" else ""}${if (details != null) " - $details" else ""}", cause) {
        
        fun getErrorInfo(): Map<String, Any?> {
            return mapOf(
                "errorCode" to errorCode.code,
                "errorMessage" to errorCode.message,
                "ssid" to ssid,
                "details" to details,
                "timestamp" to System.currentTimeMillis()
            )
        }
    }
    
    /**
     * 错误恢复策略
     */
    enum class RecoveryStrategy {
        RETRY,          // 重试
        SKIP,           // 跳过
        FALLBACK,       // 降级处理
        ABORT           // 中止操作
    }
    
    /**
     * 错误恢复配置
     */
    data class RecoveryConfig(
        val strategy: RecoveryStrategy,
        val maxRetries: Int = 3,
        val retryDelay: Long = 1000L,
        val fallbackAction: (() -> Unit)? = null
    )
    
    /**
     * 错误码到恢复策略的映射
     */
    private val errorRecoveryMap = mapOf(
        WifiErrorCode.CONNECTION_TIMEOUT to RecoveryConfig(RecoveryStrategy.RETRY, 2, 2000L),
        WifiErrorCode.SCAN_TIMEOUT to RecoveryConfig(RecoveryStrategy.RETRY, 1, 1000L),
        WifiErrorCode.WIFI_NOT_IN_RANGE to RecoveryConfig(RecoveryStrategy.SKIP),
        WifiErrorCode.AUTHENTICATION_FAILED to RecoveryConfig(RecoveryStrategy.SKIP),
        WifiErrorCode.INVALID_PASSWORD to RecoveryConfig(RecoveryStrategy.SKIP),
        WifiErrorCode.WIFI_DISABLED to RecoveryConfig(RecoveryStrategy.FALLBACK),
        WifiErrorCode.PERMISSION_DENIED to RecoveryConfig(RecoveryStrategy.ABORT),
        WifiErrorCode.SYSTEM_ERROR to RecoveryConfig(RecoveryStrategy.RETRY, 1, 5000L)
    )
    
    /**
     * 处理WiFi异常
     */
    fun handleException(
        exception: Throwable,
        ssid: String? = null,
        operation: String = "WiFi操作"
    ): WifiException {
        val wifiException = when (exception) {
            is WifiException -> exception
            is SecurityException -> WifiException(
                WifiErrorCode.PERMISSION_DENIED,
                ssid,
                "权限不足: ${exception.message}",
                exception
            )
            is TimeoutException -> WifiException(
                WifiErrorCode.CONNECTION_TIMEOUT,
                ssid,
                "操作超时: ${exception.message}",
                exception
            )
            is IllegalArgumentException -> WifiException(
                WifiErrorCode.INVALID_CONFIGURATION,
                ssid,
                "参数无效: ${exception.message}",
                exception
            )
            else -> WifiException(
                WifiErrorCode.UNKNOWN_ERROR,
                ssid,
                "未知异常: ${exception.message}",
                exception
            )
        }
        
        // 记录错误日志
        Logger.wifiE("$operation 发生异常: ${wifiException.errorCode.code} - ${wifiException.message}", wifiException)
        
        return wifiException
    }
    
    /**
     * 获取错误恢复策略
     */
    fun getRecoveryStrategy(errorCode: WifiErrorCode): RecoveryConfig {
        return errorRecoveryMap[errorCode] ?: RecoveryConfig(RecoveryStrategy.ABORT)
    }
    
    /**
     * 执行错误恢复
     */
    suspend fun executeRecovery(
        exception: WifiException,
        retryAction: suspend () -> Unit
    ): Boolean {
        val recoveryConfig = getRecoveryStrategy(exception.errorCode)
        
        Logger.wifiW("执行错误恢复: ${exception.errorCode.code}, 策略: ${recoveryConfig.strategy}")
        
        return when (recoveryConfig.strategy) {
            RecoveryStrategy.RETRY -> {
                executeRetry(exception, retryAction, recoveryConfig)
            }
            RecoveryStrategy.SKIP -> {
                Logger.wifiW("跳过错误: ${exception.errorCode.code}")
                false
            }
            RecoveryStrategy.FALLBACK -> {
                Logger.wifiW("执行降级处理: ${exception.errorCode.code}")
                recoveryConfig.fallbackAction?.invoke()
                false
            }
            RecoveryStrategy.ABORT -> {
                Logger.wifiE("中止操作: ${exception.errorCode.code}")
                false
            }
        }
    }
    
    /**
     * 执行重试逻辑
     */
    private suspend fun executeRetry(
        exception: WifiException,
        retryAction: suspend () -> Unit,
        config: RecoveryConfig
    ): Boolean {
        repeat(config.maxRetries) { attempt ->
            try {
                Logger.wifiI("重试操作: ${exception.errorCode.code}, 第 ${attempt + 1}/${config.maxRetries} 次")
                
                if (attempt > 0) {
                    delay(config.retryDelay)
                }
                
                retryAction()
                Logger.wifiI("重试成功: ${exception.errorCode.code}")
                return true
                
            } catch (e: Exception) {
                Logger.wifiW("重试失败: ${exception.errorCode.code}, 第 ${attempt + 1} 次, 错误: ${e.message}")
                if (attempt == config.maxRetries - 1) {
                    Logger.wifiE("重试次数已用完: ${exception.errorCode.code}")
                }
            }
        }
        return false
    }
    
    /**
     * 创建特定类型的WiFi异常
     */
    fun createConnectionException(ssid: String, details: String? = null, cause: Throwable? = null) =
        WifiException(WifiErrorCode.CONNECTION_FAILED, ssid, details, cause)
    
    fun createTimeoutException(ssid: String, details: String? = null, cause: Throwable? = null) =
        WifiException(WifiErrorCode.CONNECTION_TIMEOUT, ssid, details, cause)
    
    fun createAuthenticationException(ssid: String, details: String? = null, cause: Throwable? = null) =
        WifiException(WifiErrorCode.AUTHENTICATION_FAILED, ssid, details, cause)
    
    fun createConfigurationException(ssid: String, details: String? = null, cause: Throwable? = null) =
        WifiException(WifiErrorCode.INVALID_CONFIGURATION, ssid, details, cause)
    
    fun createScanException(details: String? = null, cause: Throwable? = null) =
        WifiException(WifiErrorCode.SCAN_FAILED, null, details, cause)
    
    fun createPermissionException(details: String? = null, cause: Throwable? = null) =
        WifiException(WifiErrorCode.PERMISSION_DENIED, null, details, cause)
}
