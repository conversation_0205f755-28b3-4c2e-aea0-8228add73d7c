package com.bbpos.wiseapp.tms.receiver;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.os.BatteryManager;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;

import com.bbpos.wiseapp.logger.BBLog;
import com.bbpos.wiseapp.service.common.RequestPermission;
import com.bbpos.wiseapp.tms.utils.BroadcastActions;
import com.bbpos.wiseapp.tms.utils.Constants;
import com.bbpos.wiseapp.websocket.WebSocketSender;

public class BatteryStatusReceiver extends BroadcastReceiver{
	private static final String TAG = "BATTERY";
	private Handler mHandler = new Handler(Looper.getMainLooper());
	@Override
	public void onReceive(final Context context, Intent intent) {
//		BBLog.v(TAG, "BatteryStatusReceiver receive "+ intent.getAction());
		if (intent.getAction().equals(Intent.ACTION_POWER_CONNECTED)) {
//			Constants.IS_BATTERY_CHARGING = true;
//			WebSocketSender.C0201_DeviceStatusUpload(Constants.IS_BATTERY_CHARGING,  Constants.IS_BATTERY_LOW);
		} else if (Intent.ACTION_POWER_DISCONNECTED.equals(intent.getAction())) {
//			Constants.IS_BATTERY_CHARGING = false;
//			WebSocketSender.C0201_DeviceStatusUpload(Constants.IS_BATTERY_CHARGING,  Constants.IS_BATTERY_LOW);
		} else if (Intent.ACTION_BATTERY_CHANGED.equals(intent.getAction())) {
			int temperature = intent.getIntExtra(BatteryManager.EXTRA_TEMPERATURE,0);
			int intLevel = intent.getIntExtra(BatteryManager.EXTRA_LEVEL, 0);
			int intHealth = intent.getIntExtra(BatteryManager.EXTRA_HEALTH,0);
			int intScale = intent.getIntExtra(BatteryManager.EXTRA_SCALE, 100);
			int status = intent.getIntExtra(BatteryManager.EXTRA_STATUS, -1);
			boolean is_battery_low = intLevel <= Constants.BAT_LOW_FOR_SYYTEM;
			boolean is_out_of_battery = intLevel <= 1;
			Constants.BAT_HEALTH = intHealth;
			boolean isCharging = (status==BatteryManager.BATTERY_STATUS_CHARGING || status==BatteryManager.BATTERY_STATUS_FULL);
			int chargePlug = intent.getIntExtra(BatteryManager.EXTRA_PLUGGED, -1);
			boolean usbCharge = (chargePlug == BatteryManager.BATTERY_PLUGGED_USB);
			boolean acCharge = (chargePlug == BatteryManager.BATTERY_PLUGGED_AC);

			if ((isCharging&&intLevel<Constants.BAT_LOW_FOR_OSUPDATE_CHARGING)
			 || (!isCharging&&intLevel<Constants.BAT_LOW_FOR_OSUPDATE)) {
				Constants.IS_BATTERY_LOW_FOR_OSUPDATE = true;
			} else {
				Constants.IS_BATTERY_LOW_FOR_OSUPDATE = false;
			}

//			BBLog.i(BBLog.TAG, "level=" + intLevel + ", temperature=" + temperature + ", isLowBattery=" + Constants.IS_BATTERY_LOW + ", isCharging=" + isCharging + ", usbCharge=" + usbCharge + ", acCharge=" + acCharge);
//			BBLog.i(BBLog.TAG, "IS_BATTERY_CHARGING=" + Constants.IS_BATTERY_CHARGING  + ", IS_BATTERY_LOW=" + Constants.IS_BATTERY_LOW + ", BAT_LEVEL=" + Constants.BAT_LEVEL + ", BAT_HEALTH=" + Constants.BAT_HEALTH + ", BAT_TEMP=" + Constants.BAT_TEMP  + ", IS_OUT_OF_BATTERY=" + Constants.IS_OUT_OF_BATTERY);
			if (Constants.IS_BATTERY_CHARGING != isCharging
			 || Constants.IS_BATTERY_LOW != is_battery_low
			 || Constants.IS_OUT_OF_BATTERY != is_out_of_battery
			 || (Constants.BAT_LEVEL != intLevel)
			 || ((Constants.BAT_TEMP/10)!=(temperature/10))) {//((Constants.BAT_LEVEL -intLevel)==Constants.BAT_LEVEL_OFFSET ||(Constants.BAT_LEVEL -intLevel)==(0-Constants.BAT_LEVEL_OFFSET)))) {
				Constants.BAT_LEVEL = intLevel;
				Constants.BAT_TEMP = temperature;
//				BBLog.i(BBLog.TAG, "電池狀態發生變化");
				if (!"0".equals(Constants.UPLOAD_MODE) || Constants.IS_BATTERY_CHARGING != isCharging || Constants.IS_BATTERY_LOW != is_battery_low || Constants.IS_OUT_OF_BATTERY != is_out_of_battery) {
					WebSocketSender.C0902_BatteryStatusUpload(Constants.BAT_LEVEL, Constants.BAT_HEALTH, Constants.BAT_TEMP, isCharging, is_battery_low, false, is_out_of_battery);
				}
				Constants.IS_BATTERY_CHARGING = isCharging;
				Constants.IS_BATTERY_LOW = is_battery_low;
				if (Constants.IS_BATTERY_CHARGING==true || Constants.IS_BATTERY_LOW==false) {
					Constants.IF_BATLOW_AFTER_DIALOG = false;
				}
				Constants.IS_OUT_OF_BATTERY = is_out_of_battery;
			}

			if (temperature<=0 || temperature>=500) {
				//通知7-11 launcher
				Intent it = new Intent(BroadcastActions.TEMP_WARNING);
				context.sendBroadcast(it, RequestPermission.REQUEST_PERMISSION_MY_BROADCAST);
			}
		} else if (Intent.ACTION_BATTERY_OKAY.equals(intent.getAction())) {
			if (android.os.Build.VERSION.SDK_INT < android.os.Build.VERSION_CODES.P) {
				if (Constants.IS_BATTERY_LOW) {
					Constants.IS_BATTERY_LOW = false;
//					WebSocketSender.C0902_BatteryStatusUpload(Constants.BAT_LEVEL, Constants.BAT_HEALTH, Constants.BAT_TEMP, Constants.IS_BATTERY_CHARGING, Constants.IS_BATTERY_LOW, false, Constants.IS_OUT_OF_BATTERY);
				}
			}
		} else if (Intent.ACTION_BATTERY_LOW.equals(intent.getAction())) {
			if (android.os.Build.VERSION.SDK_INT < android.os.Build.VERSION_CODES.P) {
				if (!Constants.IS_BATTERY_LOW) {
					Constants.IS_BATTERY_LOW = true;
//					WebSocketSender.C0902_BatteryStatusUpload(Constants.BAT_LEVEL, Constants.BAT_HEALTH, Constants.BAT_TEMP, Constants.IS_BATTERY_CHARGING, Constants.IS_BATTERY_LOW, false, Constants.IS_OUT_OF_BATTERY);
				}
			}
		}
	}
}
