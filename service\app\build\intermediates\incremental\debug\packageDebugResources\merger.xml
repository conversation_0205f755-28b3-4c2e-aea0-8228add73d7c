<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\SourceCode\Dspread\service\app\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\SourceCode\Dspread\service\app\src\main\res"><file name="dialog_enter" path="E:\SourceCode\Dspread\service\app\src\main\res\anim\dialog_enter.xml" qualifiers="" type="anim"/><file name="dialog_exit" path="E:\SourceCode\Dspread\service\app\src\main\res\anim\dialog_exit.xml" qualifiers="" type="anim"/><file name="bg_countdown_warning" path="E:\SourceCode\Dspread\service\app\src\main\res\drawable\bg_countdown_warning.xml" qualifiers="" type="drawable"/><file name="bg_shape" path="E:\SourceCode\Dspread\service\app\src\main\res\drawable\bg_shape.xml" qualifiers="" type="drawable"/><file name="btn_bord_gray" path="E:\SourceCode\Dspread\service\app\src\main\res\drawable\btn_bord_gray.xml" qualifiers="" type="drawable"/><file name="btn_bord_green" path="E:\SourceCode\Dspread\service\app\src\main\res\drawable\btn_bord_green.xml" qualifiers="" type="drawable"/><file name="button_background" path="E:\SourceCode\Dspread\service\app\src\main\res\drawable\button_background.xml" qualifiers="" type="drawable"/><file name="button_background_red" path="E:\SourceCode\Dspread\service\app\src\main\res\drawable\button_background_red.xml" qualifiers="" type="drawable"/><file name="button_delay_selector" path="E:\SourceCode\Dspread\service\app\src\main\res\drawable\button_delay_selector.xml" qualifiers="" type="drawable"/><file name="button_primary" path="E:\SourceCode\Dspread\service\app\src\main\res\drawable\button_primary.xml" qualifiers="" type="drawable"/><file name="button_reboot_selector" path="E:\SourceCode\Dspread\service\app\src\main\res\drawable\button_reboot_selector.xml" qualifiers="" type="drawable"/><file name="button_secondary" path="E:\SourceCode\Dspread\service\app\src\main\res\drawable\button_secondary.xml" qualifiers="" type="drawable"/><file name="countdown_background" path="E:\SourceCode\Dspread\service\app\src\main\res\drawable\countdown_background.xml" qualifiers="" type="drawable"/><file name="credit_card" path="E:\SourceCode\Dspread\service\app\src\main\res\drawable\credit_card.xml" qualifiers="" type="drawable"/><file name="dialog_background" path="E:\SourceCode\Dspread\service\app\src\main\res\drawable\dialog_background.xml" qualifiers="" type="drawable"/><file name="dialog_content_background" path="E:\SourceCode\Dspread\service\app\src\main\res\drawable\dialog_content_background.xml" qualifiers="" type="drawable"/><file name="dialog_reboot_background" path="E:\SourceCode\Dspread\service\app\src\main\res\drawable\dialog_reboot_background.xml" qualifiers="" type="drawable"/><file name="dialog_reboot_header_bg" path="E:\SourceCode\Dspread\service\app\src\main\res\drawable\dialog_reboot_header_bg.xml" qualifiers="" type="drawable"/><file name="edit_text_background" path="E:\SourceCode\Dspread\service\app\src\main\res\drawable\edit_text_background.xml" qualifiers="" type="drawable"/><file name="float_window_background" path="E:\SourceCode\Dspread\service\app\src\main\res\drawable\float_window_background.xml" qualifiers="" type="drawable"/><file name="geo_warning" path="E:\SourceCode\Dspread\service\app\src\main\res\drawable\geo_warning.xml" qualifiers="" type="drawable"/><file name="ic_launcher_background" path="E:\SourceCode\Dspread\service\app\src\main\res\drawable\ic_launcher_background.xml" qualifiers="" type="drawable"/><file name="ic_launcher_foreground" path="E:\SourceCode\Dspread\service\app\src\main\res\drawable\ic_launcher_foreground.xml" qualifiers="" type="drawable"/><file name="ic_lock" path="E:\SourceCode\Dspread\service\app\src\main\res\drawable\ic_lock.xml" qualifiers="" type="drawable"/><file name="ic_notification" path="E:\SourceCode\Dspread\service\app\src\main\res\drawable\ic_notification.png" qualifiers="" type="drawable"/><file name="ic_reboot_warning" path="E:\SourceCode\Dspread\service\app\src\main\res\drawable\ic_reboot_warning.xml" qualifiers="" type="drawable"/><file name="ic_timer" path="E:\SourceCode\Dspread\service\app\src\main\res\drawable\ic_timer.xml" qualifiers="" type="drawable"/><file name="ic_warning_geofence" path="E:\SourceCode\Dspread\service\app\src\main\res\drawable\ic_warning_geofence.xml" qualifiers="" type="drawable"/><file name="shape_corner_down" path="E:\SourceCode\Dspread\service\app\src\main\res\drawable\shape_corner_down.xml" qualifiers="" type="drawable"/><file name="activity_lock_screen" path="E:\SourceCode\Dspread\service\app\src\main\res\layout\activity_lock_screen.xml" qualifiers="" type="layout"/><file name="activity_remote_view_test" path="E:\SourceCode\Dspread\service\app\src\main\res\layout\activity_remote_view_test.xml" qualifiers="" type="layout"/><file name="dialog_os_upgrade" path="E:\SourceCode\Dspread\service\app\src\main\res\layout\dialog_os_upgrade.xml" qualifiers="" type="layout"/><file name="dialog_reboot_warning" path="E:\SourceCode\Dspread\service\app\src\main\res\layout\dialog_reboot_warning.xml" qualifiers="" type="layout"/><file name="view_reboot_float_window" path="E:\SourceCode\Dspread\service\app\src\main\res\layout\view_reboot_float_window.xml" qualifiers="" type="layout"/><file name="ic_launcher" path="E:\SourceCode\Dspread\service\app\src\main\res\mipmap-anydpi-v26\ic_launcher.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher_round" path="E:\SourceCode\Dspread\service\app\src\main\res\mipmap-anydpi-v26\ic_launcher_round.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher" path="E:\SourceCode\Dspread\service\app\src\main\res\mipmap-hdpi\ic_launcher.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="E:\SourceCode\Dspread\service\app\src\main\res\mipmap-hdpi\ic_launcher_round.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher" path="E:\SourceCode\Dspread\service\app\src\main\res\mipmap-mdpi\ic_launcher.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="E:\SourceCode\Dspread\service\app\src\main\res\mipmap-mdpi\ic_launcher_round.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher" path="E:\SourceCode\Dspread\service\app\src\main\res\mipmap-xhdpi\ic_launcher.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="E:\SourceCode\Dspread\service\app\src\main\res\mipmap-xhdpi\ic_launcher_round.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher" path="E:\SourceCode\Dspread\service\app\src\main\res\mipmap-xxhdpi\ic_launcher.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="E:\SourceCode\Dspread\service\app\src\main\res\mipmap-xxhdpi\ic_launcher_round.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher" path="E:\SourceCode\Dspread\service\app\src\main\res\mipmap-xxxhdpi\ic_launcher.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="E:\SourceCode\Dspread\service\app\src\main\res\mipmap-xxxhdpi\ic_launcher_round.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file path="E:\SourceCode\Dspread\service\app\src\main\res\values\attrs.xml" qualifiers=""><declare-styleable name="PasswordEditText">
        
        <attr format="integer" name="passwordNumber"/>
        
        <attr format="dimension" name="passwordRadius"/>
        
        <attr format="color" name="passwordColor"/>
        
        <attr format="color" name="divisionLineColor"/>
        
        <attr format="dimension" name="divisionLineSize"/>
        
        <attr format="color" name="bgColor"/>
        
        <attr format="dimension" name="bgSize"/>
        
        <attr format="dimension" name="bgCorner"/>
        
        <attr format="boolean" name="showPassword"/>
    </declare-styleable></file><file path="E:\SourceCode\Dspread\service\app\src\main\res\values\colors.xml" qualifiers=""><color name="purple_200">#FFBB86FC</color><color name="purple_500">#FF6200EE</color><color name="purple_700">#FF3700B3</color><color name="teal_200">#FF03DAC5</color><color name="teal_700">#FF018786</color><color name="black">#FF000000</color><color name="white">#FFFFFFFF</color><color name="red">#FFFF0000</color><color name="gray">#FF808080</color><color name="slate_grey">#FF708090</color></file><file path="E:\SourceCode\Dspread\service\app\src\main\res\values\dimens.xml" qualifiers=""><dimen name="dp_5">5dp</dimen><dimen name="dp_15">15dp</dimen><dimen name="dp_20">20dp</dimen><dimen name="dp_40">40dp</dimen></file><file path="E:\SourceCode\Dspread\service\app\src\main\res\values\strings.xml" qualifiers=""><string name="app_name">service</string><string name="accessibility_service_description">用于监控设备使用状态，检测用户交互事件以判断设备是否在使用中</string></file><file path="E:\SourceCode\Dspread\service\app\src\main\res\values\styles.xml" qualifiers=""><style name="no_statusbar_activity">
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowFullscreen">true</item>
        <item name="android:windowNoTitle">true</item>
    </style><style name="GeofenceWarningDialogStyle" parent="android:Theme.Dialog">
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:backgroundDimEnabled">true</item>
        <item name="android:backgroundDimAmount">0.6</item>
        <item name="android:windowAnimationStyle">@style/GeofenceDialogAnimation</item>
    </style><style name="GeofenceDialogAnimation">
        <item name="android:windowEnterAnimation">@anim/dialog_enter</item>
        <item name="android:windowExitAnimation">@anim/dialog_exit</item>
    </style></file><file path="E:\SourceCode\Dspread\service\app\src\main\res\values\themes.xml" qualifiers=""><style name="Theme.MyTheme" parent="Theme.MaterialComponents.DayNight.DarkActionBar">
        
        <item name="colorPrimary">@color/purple_500</item>
        <item name="colorPrimaryVariant">@color/purple_700</item>
        <item name="colorOnPrimary">@color/white</item>
        
        <item name="colorSecondary">@color/teal_200</item>
        <item name="colorSecondaryVariant">@color/teal_700</item>
        <item name="colorOnSecondary">@color/black</item>
        
        <item name="android:statusBarColor">?attr/colorPrimaryVariant</item>
        
    </style></file><file path="E:\SourceCode\Dspread\service\app\src\main\res\values-night\themes.xml" qualifiers="night-v8"><style name="Theme.MyTheme" parent="Theme.MaterialComponents.DayNight.DarkActionBar">
        
        <item name="colorPrimary">@color/purple_200</item>
        <item name="colorPrimaryVariant">@color/purple_700</item>
        <item name="colorOnPrimary">@color/black</item>
        
        <item name="colorSecondary">@color/teal_200</item>
        <item name="colorSecondaryVariant">@color/teal_200</item>
        <item name="colorOnSecondary">@color/black</item>
        
        <item name="android:statusBarColor">?attr/colorPrimaryVariant</item>
        
    </style></file><file name="accessibility_service_config" path="E:\SourceCode\Dspread\service\app\src\main\res\xml\accessibility_service_config.xml" qualifiers="" type="xml"/><file name="backup_rules" path="E:\SourceCode\Dspread\service\app\src\main\res\xml\backup_rules.xml" qualifiers="" type="xml"/><file name="data_extraction_rules" path="E:\SourceCode\Dspread\service\app\src\main\res\xml\data_extraction_rules.xml" qualifiers="" type="xml"/><file name="network_security_config" path="E:\SourceCode\Dspread\service\app\src\main\res\xml\network_security_config.xml" qualifiers="" type="xml"/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\SourceCode\Dspread\service\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\SourceCode\Dspread\service\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\SourceCode\Dspread\service\app\build\generated\res\resValues\debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\SourceCode\Dspread\service\app\build\generated\res\resValues\debug"/></dataSet><mergedItems><configuration qualifiers=""><declare-styleable name="PasswordEditText">
        
        <attr format="integer" name="passwordNumber"/>
        
        <attr format="dimension" name="passwordRadius"/>
        
        <attr format="color" name="passwordColor"/>
        
        <attr format="color" name="divisionLineColor"/>
        
        <attr format="dimension" name="divisionLineSize"/>
        
        <attr format="color" name="bgColor"/>
        
        <attr format="dimension" name="bgSize"/>
        
        <attr format="dimension" name="bgCorner"/>
        
        <attr format="boolean" name="showPassword"/>
    </declare-styleable></configuration></mergedItems></merger>