<?xml version="1.0" encoding="utf-8"?>
<network-security-config>
    <domain-config cleartextTrafficPermitted="true">
        <!-- 允许Remote View服务器的明文通信 -->
        <domain includeSubdomains="false">***********</domain>
        <domain includeSubdomains="false">config.dspreadserv.net</domain>
        <!-- 允许本地测试 -->
        <domain includeSubdomains="false">localhost</domain>
        <domain includeSubdomains="false">127.0.0.1</domain>
        <domain includeSubdomains="false">********</domain>
    </domain-config>
    
    <!-- 对于其他域名，仍然要求HTTPS -->
    <base-config cleartextTrafficPermitted="false">
        <trust-anchors>
            <certificates src="system"/>
        </trust-anchors>
    </base-config>
</network-security-config>
