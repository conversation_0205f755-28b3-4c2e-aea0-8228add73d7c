package com.dspread.mdm.service.network.websocket.connection

import android.content.Context
import android.text.TextUtils
import com.dspread.mdm.service.utils.crypto.RSAUtils
import com.dspread.mdm.service.utils.log.Logger

/**
 * WebSocket 密钥管理器
 *  WebSocketManager 密钥管理逻辑
 */
object WsKeyManager {

    private const val TAG = "WsKeyManager"
    
    // 密钥存储
    private var clientPublicKey: String? = null
    private var clientPrivateKey: String? = null
    private var serverPublicKey: String? = null

    /**
     * 初始化密钥管理器
     */
    fun initialize(context: Context) {
        try {
            Logger.wsm("初始化 WebSocket 密钥管理器")

            // 生成客户端公私钥对
            generateClientKeyPair()

            Logger.success("WebSocket 密钥管理器初始化完成")

        } catch (e: Exception) {
            Logger.wsmE("WebSocket 密钥管理器初始化失败", e)
        }
    }

    /**
     * 生成客户端密钥对
     */
    private fun generateClientKeyPair() {
        try {
            Logger.wsm("生成客户端公私钥对")
            
            val keyMap = RSAUtils.genKeyPair()
            clientPublicKey = RSAUtils.getPublicKey(keyMap)
            clientPrivateKey = RSAUtils.getPrivateKey(keyMap)
            
        } catch (e: Exception) {
            Logger.wsmE("生成密钥对失败", e)
        }
    }



    /**
     * 设置服务器公钥
     */
    fun setServerPublicKey(publicKey: String) {
        serverPublicKey = publicKey
        Logger.wsm("设置服务器公钥")
    }

    /**
     * 获取客户端公钥
     */
    fun getClientPublicKeyString(): String {
        return clientPublicKey ?: "dummy_client_public_key"
    }

    /**
     * 获取客户端私钥
     */
    fun getClientPrivateKeyString(): String {
        return clientPrivateKey ?: "dummy_client_private_key"
    }

    /**
     * 获取服务器公钥
     */
    fun getServerPublicKeyString(): String {
        return serverPublicKey ?: "dummy_server_public_key"
    }

    /**
     * 使用私钥签名数据
     */
    fun signData(data: String): String {
        return try {
            if (clientPrivateKey != null) {
                RSAUtils.sign(data.toByteArray(), clientPrivateKey!!)
            } else {
                // 暂时返回简单的哈希值
                data.hashCode().toString()
            }
        } catch (e: Exception) {
            Logger.wsmE("数据签名失败", e)
            data.hashCode().toString()
        }
    }

    /**
     * 验证数据签名
     */
    fun verifyData(data: ByteArray, signature: String): Boolean {
        return try {
            if (serverPublicKey != null) {
                RSAUtils.verify(data, serverPublicKey!!, signature)
            } else {
                Logger.wsmE("服务器公钥未设置，跳过验签")
                true // 暂时跳过验签
            }
        } catch (e: Exception) {
            Logger.wsmE("数据验签失败", e)
            false
        }
    }

    /**
     * 使用服务器公钥加密数据
     */
    fun encryptWithServerPublicKey(data: ByteArray): ByteArray {
        return try {
            if (serverPublicKey != null) {
                RSAUtils.encryptByPublicKey(data, serverPublicKey!!)
            } else {
                Logger.wsmE("服务器公钥未设置，返回原数据")
                data
            }
        } catch (e: Exception) {
            Logger.wsmE("公钥加密失败", e)
            data
        }
    }

    /**
     * 使用客户端私钥解密数据
     */
    fun decryptWithClientPrivateKey(encryptedData: ByteArray): ByteArray {
        return try {
            if (clientPrivateKey != null) {
                RSAUtils.decryptByPrivateKey(encryptedData, clientPrivateKey!!)
            } else {
                Logger.wsmE("客户端私钥未设置，返回原数据")
                encryptedData
            }
        } catch (e: Exception) {
            Logger.wsmE("私钥解密失败", e)
            encryptedData
        }
    }

    /**
     * 检查密钥是否已初始化
     */
    fun isInitialized(): Boolean {
        return !TextUtils.isEmpty(clientPublicKey) && !TextUtils.isEmpty(clientPrivateKey)
    }

    /**
     * 清理密钥
     */
    fun clear() {
        clientPublicKey = null
        clientPrivateKey = null
        serverPublicKey = null
        Logger.wsm("密钥管理器已清理")
    }
}
