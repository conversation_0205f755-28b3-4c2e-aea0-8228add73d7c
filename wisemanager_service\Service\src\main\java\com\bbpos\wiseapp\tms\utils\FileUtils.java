package com.bbpos.wiseapp.tms.utils;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.FileReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.math.BigInteger;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.attribute.BasicFileAttributeView;
import java.security.MessageDigest;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.StringTokenizer;
import java.util.zip.CRC32;
import java.util.zip.CheckedInputStream;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;
import java.util.zip.ZipOutputStream;

import android.os.Environment;
import android.text.TextUtils;
import android.util.Log;

import com.bbpos.wiseapp.logger.BBLog;
import com.bbpos.wiseapp.security.SecurityOperate;

public class FileUtils {
	private static final String TAG = FileUtils.class.getName();
	private static final int BUFFER_SIZE = 3*1024;

	private final static String FILE_SEPARATOR = "/";
	private final static String FILE_FORMAT = ".txt";
	private final static String CONFIG_CATALOG = "wiseapp";
	private final static String CONFIG_NAME = "config";
	
	/**获取文件CRC32码-8位16进制*/
	public static String getCRC32(String fileUri) {  
        CRC32 crc32 = new CRC32();  
        FileInputStream fileinputstream = null;  
        CheckedInputStream checkedinputstream = null;  
        String crc = null;  
        try {  
            fileinputstream = new FileInputStream(new File(fileUri));  
            checkedinputstream = new CheckedInputStream(fileinputstream, crc32);
            byte[] tmp = new byte[1024*4];
            while (checkedinputstream.read(tmp) != -1) {  
            }  
            crc = Long.toHexString(crc32.getValue());  
        } catch (Exception e) {
            e.printStackTrace();  
        } finally {
        	IOUtils.closeInputStream(fileinputstream);
        	IOUtils.closeInputStream(checkedinputstream);
        }  
        return crc;  
    }  
	
	/**获取文件MD5码*/
	public static String getMd5ByFile(File file) {
		String value = null;
		FileInputStream in = null;
		BBLog.i(BBLog.TAG, "getMd5ByFile:"+ file.getName());
		try {
//			in = new FileInputStream(file);
//			MappedByteBuffer byteBuffer = in.getChannel().map(
//					FileChannel.MapMode.READ_ONLY, 0, file.length());
//			MessageDigest md5 = MessageDigest.getInstance("MD5");
//			md5.update(byteBuffer);
//			BigInteger bi = new BigInteger(1, md5.digest());
//			int md5Length = 32;
//			value = bi.toString(16);//16进制
//			if(value.length() < md5Length){
//				for (int i = value.length(); i < md5Length; i++) {
//					value = "0"+value;
//				}
//			}
			try {
				if (!file.getCanonicalPath().startsWith(Environment.getExternalStorageDirectory().getCanonicalPath())) {
					BBLog.e("FileUtils", "Path Traversal");
				}
			} catch (IOException e) {
				e.printStackTrace();
			}
			in = new FileInputStream(file);
			MessageDigest md5 = MessageDigest.getInstance("MD5");
			byte[] buffer = new byte[1024*8];
			int length;
			while ((length = in.read(buffer)) != -1) {
				md5.update(buffer, 0, length);
			}
			BigInteger bi = new BigInteger(1, md5.digest());
			int md5Length = 32;
			value = bi.toString(16);//16进制
			if(value.length() < md5Length){
				for (int i = value.length(); i < md5Length; i++) {
					value = "0"+value;
				}
			}
		} catch (FileNotFoundException e) {
			e.printStackTrace();
			BBLog.e(BBLog.TAG, "error in getMd5ByFile", e);
		} catch (Exception e) {
			e.printStackTrace();
			BBLog.e(BBLog.TAG, "error in getMd5ByFile", e);
		} finally {
			IOUtils.closeInputStream(in);
		}
		BBLog.i(BBLog.TAG, "file:"+ file.length()+" MD5:"+value);
		return value;
	}

	public static boolean isFileMd5Correct(String filePath,String fileMd5){
		String countFileMd5 = getMd5ByFile(new File(filePath));
		if(fileMd5.equalsIgnoreCase(countFileMd5))
			return true;
		return false;
	}
	
	/**
	 * 压缩文件对象
	 * 
	 * @param zipFilePath
	 *            压缩文件路径
	 * @param inputFiles
	 *            文件对象
	 * @return
	 */
	public static void zipFiles(String zipFilePath, File[] inputFiles) {
		ZipOutputStream out = null;
		try {
			out = new ZipOutputStream(new FileOutputStream(
					zipFilePath));
			for (File file : inputFiles) {
				if(file==null || !file.exists() || file.length() == 0)
					continue;
				zipOuntputStream(out, file, "");
			}
		} catch (Exception e) {
			BBLog.e(BBLog.TAG, "error in zipFiles", e);
		} finally {
			try {
				if (out != null) {
					out.flush();
					out.close();
				}
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
	}
	/**
	 * 压缩文件对象
	 * 
	 * @param zipFilePath
	 *            压缩文件路径
	 * @param inputFiles
	 *            文件对象
	 * @return
	 */
	public static void zipFiles(String zipFilePath, ArrayList<File> inputFiles) {
		ZipOutputStream out = null;
		try {
			out = new ZipOutputStream(new FileOutputStream(
					zipFilePath));
			for (File file : inputFiles) {
				if(file==null || !file.exists() || file.length() == 0)
					continue;
				zipOuntputStream(out, file, "");
			}

		} catch (Exception e) {
			BBLog.e(BBLog.TAG, "error in zipFiles", e);
		} finally {
			try {
				if (out != null) {
					out.flush();
					out.close();
				}
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
	}

	/**
	 * 
	 * @param outputStream
	 *            压缩输出流对象
	 * @param file
	 * @param parePath
	 *            压缩文件中目录，相对路径
	 * @throws Exception
	 */
	private static void zipOuntputStream(ZipOutputStream outputStream,
			File file, String parePath) throws Exception {
		if (file.isDirectory()) {
			File[] fileList = file.listFiles();
			outputStream.putNextEntry(new ZipEntry(file.getName() + File.separator));
			for (int i = 0; i < fileList.length; i++) {
				zipOuntputStream(outputStream, fileList[i], file.getName());
			}
		} else {
			outputStream.putNextEntry(new ZipEntry(parePath + File.separator + file.getName()));
			FileInputStream inputStream = new FileInputStream(file);
			// 普通压缩文件
			byte[] buffer = new byte[BUFFER_SIZE];
			int readSize = -1;
			while ((readSize = inputStream.read(buffer))!= -1) {
				outputStream.write(buffer,0,readSize);
			}
			inputStream.close();
		}
		file.delete();
	}

	@SuppressWarnings("rawtypes")
	public static ArrayList unZipFile(String zipFileName, String outputDirectory){
		try {
			ZipInputStream inputStream = new ZipInputStream(new FileInputStream(
					zipFileName));
			return unzip(inputStream, outputDirectory);
		} catch (Exception e) {
			BBLog.e(BBLog.TAG, "error in unZipFile. zipfile:"+zipFileName, e);
			e.printStackTrace();
		}
		return new ArrayList();
	}

	@SuppressWarnings("rawtypes")
	private static ArrayList unzip(ZipInputStream inputStream, String outputDirectory)
			throws Exception {
	 	ArrayList<String> fileNameList = new ArrayList<String>();
		ZipEntry zipEntry = null;
		FileOutputStream outputStream = null;
		while ((zipEntry = inputStream.getNextEntry()) != null) {
			if (zipEntry.isDirectory()) {
				String name = zipEntry.getName();
				name = name.substring(0, name.length() - 1);
				//加入+ File.separator + "tmp"，确保目录被正确创建  shisen.zhang @20160523
				File file = new File(outputDirectory + File.separator + name + File.separator + "tmp");
				try {
					if (!file.getCanonicalPath().startsWith(Environment.getExternalStorageDirectory().getCanonicalPath())) {
						BBLog.e("FileUtils-unzip", "Path Traversal");
					}
				} catch (IOException e) {
					e.printStackTrace();
				}
				file.mkdir();
			} else {
				String fileName = outputDirectory + File.separator + zipEntry.getName();
				File file = new File(fileName);
				try {
					if (!file.getCanonicalPath().startsWith(Environment.getExternalStorageDirectory().getCanonicalPath())) {
						BBLog.e("FileUtils-unzip", "Path Traversal");
					}
				} catch (IOException e) {
					e.printStackTrace();
				}
				fileNameList.add(fileName);
				file.createNewFile();
				outputStream = new FileOutputStream(file);
				// 普通解压缩文件
				byte[] buffer = new byte[BUFFER_SIZE];
				int readSize = -1;
				while ((readSize = inputStream.read(buffer))!= -1) {
					outputStream.write(buffer,0,readSize);
				}
				outputStream.flush();
				outputStream.close();
			}
		}
		inputStream.close();
		return fileNameList;
	}
	public static void deleteFile(String filePath) {
		boolean result = false;
		File file = new File(filePath);
		if(file.exists()) {
			result = file.delete();
		}
		BBLog.e(BBLog.TAG, "deleteFile:  result " + result +", filePath = " + filePath );
	}
	
	// copy a file from srcFile to destFile, return true if succeed, return  
    // false if fail  
    public static boolean copyFile(File srcFile, File destFile) {  
        boolean result = false;
		InputStream in = null;
        try {  
            in = new FileInputStream(srcFile);
			result = copyToFile(in, destFile);
        } catch (Exception e) {
            result = false;  
        } finally {
			try {
				if (in != null) {
					in.close();
				}
			} catch (Exception e) {
			}
		}
        return result;  
    }  
  
    /**  
     * Copy data from a source stream to destFile.  
     * Return true if succeed, return false if failed.  
     */  
    public static boolean copyToFile(InputStream inputStream, File destFile) {  
        try {  
            if (destFile.exists()) {  
                destFile.delete();  
            }  
            FileOutputStream out = new FileOutputStream(destFile);  
            try {  
                byte[] buffer = new byte[4096];  
                int bytesRead;  
                while ((bytesRead = inputStream.read(buffer)) >= 0) {  
                    out.write(buffer, 0, bytesRead);  
                }  
            } finally {  
                try {
                	if (out != null) {
						out.flush();
						out.getFD().sync();
						out.close();
					}
                } catch (Exception e) {
                }  
            }
            return true;  
        } catch (Exception e) {
            return false;  
        }  
    }

	public static boolean isFileDownloadSuccessed(String fileName, long serverFileSize, String serverFileMd5) {
		File file = new File(fileName);
		if(!file.exists()){
			BBLog.e(BBLog.TAG, "file:"+fileName+" donot exists");
			return false;
		}
		if(file.length() != serverFileSize){
			BBLog.e(BBLog.TAG, "filesize check failed.file:"+fileName+" length:"+file.length()+" serverFileSize:"+serverFileSize);
			return false;
		}
		String curFileMd5 = getMd5ByFile(file);
		if(!curFileMd5.equalsIgnoreCase(serverFileMd5)){
			BBLog.e(BBLog.TAG, "md5 check failed.file:"+fileName+" fileMd5:"+curFileMd5+" serverFileMd5:"+serverFileMd5);
			return false;
		}
		
		return true;
	}

	public static String getWiseAppConfigPath() {
		File sdCardPath = null;
		String wiseAppConfigPath = null;
		//判断sd卡是否存在
		boolean sdCardExist = Environment.getExternalStorageState().equals(android.os.Environment.MEDIA_MOUNTED);
		if(sdCardExist) {
			sdCardPath = Environment.getExternalStorageDirectory();//获取跟目录
		}

		if (!TextUtils.isEmpty(sdCardPath.toString())) {
			wiseAppConfigPath = sdCardPath.toString() + FILE_SEPARATOR +
					CONFIG_CATALOG + FILE_SEPARATOR + CONFIG_NAME + FILE_FORMAT;
		}

		return wiseAppConfigPath;
	}

	// 读文件，返回字符串
	public static String readFile(String path) {
		File file = new File(path);
		if (!file.exists()) {
			return null;
		}

		BufferedReader reader = null;
		String laststr = "";
		try {
			reader = new BufferedReader(new FileReader(file));
			String tempString = null;
			// 一次读入一行，直到读入null为文件结束
			while ((tempString = reader.readLine()) != null) {
				laststr = laststr + tempString;
			}
			reader.close();
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			if (reader != null) {
				try {
					reader.close();
				} catch (Exception e1) {
				}
			}
		}

		return laststr;
	}

	/**
	 * 判断文件是否存在是否超过x天
	 * @param date
	 * @return
	 */
	public static boolean justTime(long date, int days){
		BBLog.d(TAG, "justTime: "+date);
		if(date == 0L) return false;

		Date date1 = new Date(date);
		Date date2 = new Date();

		long diff = date2.getTime() -date1.getTime();
		double result = diff * 1.0 / (1000 * 60 * 60 * 24);
		return result >= days;//判断时间是否超过3天
	}

	/**
	 * 获取文件创建时间
	 * @param file
	 * @return
	 */
	public static long getFileCreateDate(File file) {
		if (file == null) return 0L;
		try {
//			Process ls_proc = Runtime.getRuntime().exec(
//					"cmd.exe /c dir " + file.getAbsolutePath() + " /tc");
			Process ls_proc =  SecurityOperate.getInstance().execCommand(ContextUtil.getInstance(),
					"cmd.exe /c dir " + file.getAbsolutePath() + " /tc");

			BufferedReader br = new BufferedReader(new InputStreamReader(ls_proc.getInputStream()));
			for (int i = 0; i < 5; i++) {
				br.readLine();
			}
			String stuff = br.readLine();
			StringTokenizer st = new StringTokenizer(stuff);
			String dateC = st.nextToken();
			String time = st.nextToken();
			String datetime = dateC.concat(time);
			BBLog.d(TAG, "getFileCreateDate: "+datetime);
			br.close();
			return new Date(datetime).getTime();
		} catch (Exception e) {
			return 0L;
		}
	}
}
