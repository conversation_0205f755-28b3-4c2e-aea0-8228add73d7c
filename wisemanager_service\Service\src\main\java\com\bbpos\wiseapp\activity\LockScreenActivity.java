package com.bbpos.wiseapp.activity;

import android.app.Activity;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.net.ConnectivityManager;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.Message;
import android.os.PowerManager;
import android.text.TextUtils;
import android.util.Log;
import android.util.TypedValue;
import android.view.View;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.TextView;
import android.widget.Toast;

import com.bbpos.wiseapp.logger.BBLog;
import com.bbpos.wiseapp.service.R;
import com.bbpos.wiseapp.service.common.RequestPermission;
import com.bbpos.wiseapp.service.common.UsualData;
import com.bbpos.wiseapp.system.api.DeviceInfoApi;
import com.bbpos.wiseapp.system.api.SysIntermediateApi;
import com.bbpos.wiseapp.tms.location.GpsLocationManager;
import com.bbpos.wiseapp.tms.utils.BroadcastActions;
import com.bbpos.wiseapp.tms.utils.Constants;
import com.bbpos.wiseapp.tms.utils.ContextUtil;
import com.bbpos.wiseapp.tms.utils.ParameterName;
import com.bbpos.wiseapp.utils.SharedPreferencesUtils;
import com.bbpos.wiseapp.websocket.Constant;
import com.bbpos.wiseapp.websocket.WebSocketSender;

import java.util.Timer;
import java.util.TimerTask;

public class LockScreenActivity extends Activity implements PasswordEditText.PasswordFullListener{
    private static final String TAG = "LockScreen";
    private int m_count = 0;
    private int m_wipedata_count = GpsLocationManager.GEO_WIPE_MINS;
    private static String unlock_pwd = "";
    private static long expire_time = 0L;
    private String mode = "0";  //mode代表创建LockScreen时是否显示解锁密码输入框模式UI
    private boolean finish_from_external = false;
    private boolean finish_without_callback = false;
    private boolean isScreenOff = false;
    private boolean hasResume = false;  //表明LockScreen UI是否已经恢复最前面显示

    private Timer timer = null;
    private TimerTask mTimerTask = null;
    private boolean b_data_wiped = false;

    private LinearLayout ll_lockdevice;
    private LinearLayout ll_lockdevice_input;
    private Button btnSubmit;
    private TextView tv_no_pwd;
    private TextView tv_tip;
    private PasswordEditText passwordEditText;

    private int otp_counter = 5;
    private TextView tv_warning;
    private static long lastOTPGotTime = 0L;

    private IntentFilter mIntentFilter;
    private final BroadcastReceiver mReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            String action = intent.getAction();
            BBLog.e(TAG, "LockScreenActivity BroadcastReceiver : " + action);
            if (action.equals(UsualData.ACTION_CLOSE_LOCKSCREEN)) {
                BBLog.e(TAG, "ACTION_CLOSE_LOCKSCREEN b_data_wiped =" + b_data_wiped);
                BBLog.e(TAG, "Constants.M_GEOFENCE_STATUS = " + Constants.M_GEOFENCE_STATUS);
                if (Constants.M_GEOFENCE_STATUS == GpsLocationManager.WIPE_DATA) {
                    if (DeviceInfoApi.getIntance().isWisePosPro()) {
                        ContextUtil.setLauncherApp(ContextUtil.getInstance(), UsualData.LOADER_711_PACKAGE_NAME);
                    }
                } else {
                    if (b_data_wiped) {
                        if (DeviceInfoApi.getIntance().isWisePosPro()) {
                            ContextUtil.setLauncherApp(ContextUtil.getInstance(), UsualData.LOADER_711_PACKAGE_NAME);
                        }
                    }
                }
                Message message = new Message();
                message.what = 0;
                mHandler.sendMessageDelayed(message, 200);
            } else if (action.equals(UsualData.ACTION_DATA_WIPED)) {
                b_data_wiped = true;
                showPwdErrorWarning(false);
                showStopWipingUI();
                otp_counter = 5;
                SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_GPS_GEOFENCE_OTP_COUNTER, otp_counter);
                BBLog.e(TAG, "ACTION_DATA_WIPED b_data_wiped =" + b_data_wiped);
                GpsLocationManager.setCurrentGPSStatus(GpsLocationManager.WIPE_DATA);
                WebSocketSender.C0903_DataInfoUploadForGeo(System.currentTimeMillis());
            } else if (action.equals(UsualData.ACTION_ENTER_GEOFENCE)) {
                if (Constants.M_GEOFENCE_STATUS == GpsLocationManager.LOCK_SCREEN && GpsLocationManager.GEO_WIPE_STATUS) {
                    if (timer != null) {
                        stopTimeSchedule();
                    }
                }
                showLockDeviceInputDialog();
            } else if (action.equals(UsualData.ACTION_OUT_OF_GEOFENCE)) {
                if (Constants.M_GEOFENCE_STATUS == GpsLocationManager.LOCK_SCREEN && GpsLocationManager.GEO_WIPE_STATUS) {
                    if (timer == null) {
                        startTimeSchedule();
                    }
                }
                showLockDeviceDialog();
            } else if (action.equals(UsualData.ACTION_GET_ONETIME_PWD)) {
                if (intent.hasExtra(ParameterName.password) && intent.hasExtra(ParameterName.expiration_time)) {
                    lastOTPGotTime = System.currentTimeMillis();
                    unlock_pwd = intent.getStringExtra(ParameterName.password);
                    expire_time = intent.getLongExtra(ParameterName.expiration_time, 0L);
                    hideNoPwdWarning();
                    mHandler.post(new Runnable() {
                        @Override
                        public void run() {
                            Toast.makeText(LockScreenActivity.this, "A new OTP is created, please check with help desk", Toast.LENGTH_LONG).show();
                        }
                    });
                }
            } else if (action.equals(UsualData.ACTION_WIPE_PARAM_CHANGE)) {
                boolean wipe_status = intent.getBooleanExtra("GEO_WIPE_STATUS", false);
                int wipe_mins = intent.getIntExtra("GEO_WIPE_MINS", GpsLocationManager.GEO_WIPE_MINS);
                BBLog.e(TAG, "update param：wipe_status=" + wipe_status + "  wipe_mins=" + wipe_mins);
                BBLog.e(TAG, "original param：wipe_status=" + GpsLocationManager.GEO_WIPE_STATUS + "  wipe_mins=" + GpsLocationManager.GEO_WIPE_MINS);
                if (GpsLocationManager.GEO_WIPE_STATUS && !wipe_status) {
                    if (Constants.M_GEOFENCE_STATUS == GpsLocationManager.LOCK_SCREEN && GpsLocationManager.GEO_WIPE_STATUS) {
                        stopTimeSchedule();
                        GpsLocationManager.GEO_WIPE_STATUS = wipe_status;
                        GpsLocationManager.GEO_WIPE_MINS = wipe_mins;
                        SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_GPS_GEOFENCE_WIPE_STATUS,
                                GpsLocationManager.GEO_WIPE_STATUS + "");
                        SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_GPS_GEOFENCE_WIPE_MINS,
                                GpsLocationManager.GEO_WIPE_MINS + "");
                    }
                } else if (!GpsLocationManager.GEO_WIPE_STATUS && wipe_status) {
                    if (Constants.M_GEOFENCE_STATUS == GpsLocationManager.LOCK_SCREEN && !GpsLocationManager.GEO_WIPE_STATUS) {
                        GpsLocationManager.GEO_WIPE_STATUS = wipe_status;
                        GpsLocationManager.GEO_WIPE_MINS = wipe_mins;
                        SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_GPS_GEOFENCE_WIPE_STATUS,
                                GpsLocationManager.GEO_WIPE_STATUS + "");
                        SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_GPS_GEOFENCE_WIPE_MINS,
                                GpsLocationManager.GEO_WIPE_MINS + "");
                        m_wipedata_count = GpsLocationManager.GEO_WIPE_MINS;
                        startTimeSchedule();
                    }
                } else if (GpsLocationManager.GEO_WIPE_STATUS && wipe_status && GpsLocationManager.GEO_WIPE_MINS != wipe_mins) {
                    if (Constants.M_GEOFENCE_STATUS == GpsLocationManager.LOCK_SCREEN && GpsLocationManager.GEO_WIPE_STATUS) {
                        GpsLocationManager.GEO_WIPE_MINS = wipe_mins;
                        SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_GPS_GEOFENCE_WIPE_MINS,
                                GpsLocationManager.GEO_WIPE_MINS + "");
                        m_wipedata_count = GpsLocationManager.GEO_WIPE_MINS;
                        startTimeSchedule();
                    }
                }
            } else if (action.equals(UsualData.ACTION_WEBSOCKET_CONNECTION)) {
                if (intent.hasExtra("connection")) {
                    boolean websocket_connected = intent.getBooleanExtra("connection", false);
                    if (websocket_connected) {
                    } else {
                    }
                }
            } else if (action.equals(ConnectivityManager.CONNECTIVITY_ACTION)) {
                BBLog.e(TAG, "LockScreenActivity receives CONNECTIVITY_ACTION");
            } else if (action.equals(Intent.ACTION_SCREEN_ON)) {
                isScreenOff = false;

                if(!hasResume){
                    //屏幕点亮时，如果已经是最前端显示，则不再重复启动LockScreen；
                    //否则LockScreen已被覆盖，则启动（解决711Launcher的deskTop锁屏时，息屏亮屏后回到login UI，lockScreen被覆盖的问题）
                    //做个延时，避免快速点击的时候，被自己onPause的代码执行到onDestroy杀死；
                    mHandler.postDelayed(new Runnable() {
                        @Override
                        public void run() {
                            BBLog.e(TAG, "recreate from screen on ");
                            finish_without_callback = true;
                            finish_from_external = false;
                            finish();

                            if (ll_lockdevice_input.getVisibility() == View.GONE) {
                                GpsLocationManager.gotoLockDeviceScreen();
                            } else {
                                GpsLocationManager.gotoLockDeviceScreenInput();
                            }
                        }
                    }, 1000);
                }
            } else if (action.equals(Intent.ACTION_SCREEN_OFF)) {
                isScreenOff = true;
            }
        }
    };

    private Handler mHandler = new Handler() {
        @Override
        public void handleMessage(Message msg) {
            super.handleMessage(msg);
            if (msg.what == 0) {
                finish_from_external = true;
                finish();
            }
        }
    };

    private void registerBroadcastReceiver() {
        if (mIntentFilter == null) {
            mIntentFilter = new IntentFilter();
            mIntentFilter.addAction(UsualData.ACTION_CLOSE_LOCKSCREEN);
            mIntentFilter.addAction(UsualData.ACTION_DATA_WIPED);
            mIntentFilter.addAction(UsualData.ACTION_ENTER_GEOFENCE);
            mIntentFilter.addAction(UsualData.ACTION_OUT_OF_GEOFENCE);
            mIntentFilter.addAction(UsualData.ACTION_GET_ONETIME_PWD);
            mIntentFilter.addAction(UsualData.ACTION_WIPE_PARAM_CHANGE);
            mIntentFilter.addAction(UsualData.ACTION_WEBSOCKET_CONNECTION);
            mIntentFilter.addAction(Intent.ACTION_SCREEN_ON);
            mIntentFilter.addAction(Intent.ACTION_SCREEN_OFF);
            if (Build.VERSION.SDK_INT < Build.VERSION_CODES.N) {
                mIntentFilter.addAction(ConnectivityManager.CONNECTIVITY_ACTION);
            }
        }

        registerReceiver(mReceiver, mIntentFilter,  RequestPermission.REQUEST_PERMISSION_MY_BROADCAST, null);
    }

    private void unregisterBroadcastReceiver() {
        unregisterReceiver(mReceiver);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        BBLog.e(TAG, "LockScreenActivity onCreate");

        //mode代表创建LockScreen时是否显示解锁密码输入框模式UI
        if (getIntent().hasExtra("mode")) {
            mode = getIntent().getStringExtra("mode");
        }

        setContentView(R.layout.activity_lockscreen);

        if (!GpsLocationManager.GEO_STATUS) {
            BBLog.e(TAG, "**** GEOFENCE is disable，finish ****");
            GpsLocationManager.setCurrentGPSStatus(GpsLocationManager.IN_ZONE);
            finish_from_external = true;
            finish();
        }

        if (Constants.M_GEOFENCE_STATUS <= GpsLocationManager.LOCK_SCREEN) {
            if (Constants.M_GEOFENCE_STATUS < GpsLocationManager.LOCK_SCREEN) {
                GpsLocationManager.setCurrentGPSStatus(GpsLocationManager.LOCK_SCREEN);
                m_wipedata_count = GpsLocationManager.GEO_WIPE_MINS;
                BBLog.e(TAG, "to wipe data：" + m_wipedata_count + "mins");
                SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_GPS_GEOFENCE_LOCK_COUNTDOWN_MINS, m_wipedata_count+"");
                WebSocketSender.C0903_DataInfoUploadForGeo(System.currentTimeMillis());
            } else if (Constants.M_GEOFENCE_STATUS == GpsLocationManager.LOCK_SCREEN) {
                m_wipedata_count = Integer.valueOf(SharedPreferencesUtils.getSharePreferencesValue(UsualData.SHARED_PREFERENCES_GPS_GEOFENCE_LOCK_COUNTDOWN_MINS, "" + GpsLocationManager.GEO_WIPE_MINS));
                BBLog.e(TAG, "to wipe data：" + m_wipedata_count + "mins");
            }
            if (GpsLocationManager.GEO_WIPE_STATUS) {
                BBLog.e(TAG, "start timer：" + m_wipedata_count + " mins");
                startTimeSchedule();
            } else {
            }
        } else if (Constants.M_GEOFENCE_STATUS == GpsLocationManager.WIPE_DATA) {
            b_data_wiped = true;
            BBLog.e(TAG, "onCreate b_data_wiped =" + b_data_wiped);
        }

        otp_counter = SharedPreferencesUtils.getSharePreferencesValue(UsualData.SHARED_PREFERENCES_GPS_GEOFENCE_OTP_COUNTER, 5);
        initView();
        registerBroadcastReceiver();
    }

    @Override
    protected void onResume() {
        super.onResume();
        BBLog.e(TAG, "LockScreenActivity onResume "+ Constants.M_GEOFENCE_STATUS );
        hasResume = true;
        SysIntermediateApi.getIntance().updateSystemProperty("true");
    }

    @Override
    protected void onPause() {
        super.onPause();
		BBLog.e(TAG, "LockScreenActivity onPause Constants.M_GEOFENCE_STATUS "+ Constants.M_GEOFENCE_STATUS);
        BBLog.e(TAG, "LockScreenActivity onPause finish_from_external=" + finish_from_external);
        hasResume = false;

        if (!finish_from_external) {
            //以下这段onPause代码作用于LockScreen被其他UI覆盖退到后台时，能再次startActivity回到前台；
            //但是lockScreen一直在前台时的息屏动作，也会触发onPause，此时应做排除；
            //用isScreenOff变量来判断是否是息屏的情况（但是ACTION_SCREEN_OFF的广播比较onPause慢，所以用isScreenOn来判断实际情况）
            //为避免此种case会误执行restartActivity，那么此时靠hasResume变量，在onResume中提前设置，来表明lockScreen是否已经在亮屏时是在前台；
            //isScreenOff 和 hasResume 和 isScreenOn()共同作用，来区分lockScreen一直在前台时手动息屏亮屏时，能够不执行restartActivity的代码；
            mHandler.postDelayed(new Runnable() {
                @Override
                public void run() {
                    BBLog.e(TAG, "LockScreenActivity "+ isScreenOff  + " " + hasResume );
                    if (!isScreenOff && !hasResume && isScreenOn()) {
                        BBLog.e(TAG, "recreate from pause");
                        finish_without_callback = true;
                        finish_from_external = false;
                        finish();

                        if (ll_lockdevice_input.getVisibility() == View.GONE) {
                            GpsLocationManager.gotoLockDeviceScreen();
                        } else {
                            GpsLocationManager.gotoLockDeviceScreenInput();
                        }
                    }
                }
            }, 1000);
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        BBLog.e(TAG, "LockScreenActivity onDestroy");
        if (!finish_without_callback) {
            if (Constants.M_GEOFENCE_SET_SERVICE_LAUNCHER) {
                if (Constants.M_GEOFENCE_STATUS == GpsLocationManager.WIPE_DATA) {
                    if (DeviceInfoApi.getIntance().isWisePosPro()) {
                        ContextUtil.setLauncherApp(ContextUtil.getInstance(), UsualData.LOADER_711_PACKAGE_NAME);
                    }
                }
            } else {
                if (Constants.B_UNBOX_RUNNING) {
                    Constants.B_UNBOX_RUNNING_LOCK = true;
                    GpsLocationManager.shutDownGeoFenceDetect();
                }
            }
            SysIntermediateApi.getIntance().updateSystemProperty("false");
        }

        unregisterBroadcastReceiver();
        if (timer != null) {
            timer.cancel();
            timer = null;
        }

        if (!finish_without_callback) {
            if (Constants.M_GEOFENCE_STATUS != GpsLocationManager.ROAMING) {
                GpsLocationManager.setCurrentGPSStatus(GpsLocationManager.IN_ZONE);
                GpsLocationManager.registLocationChangeListener();
            } else {
                if (b_data_wiped) {
                    if (DeviceInfoApi.getIntance().isWisePosPro()) {
                        ContextUtil.setLauncherApp(ContextUtil.getInstance(), UsualData.LOADER_711_PACKAGE_NAME);
//                        LauncherDeamonReceiver.restartLoaderProcess();
                    }
                }
            }
            WebSocketSender.C0903_DataInfoUploadForGeo(System.currentTimeMillis());
        }
    }

    private void initView() {
        ((TextView)findViewById(R.id.tv_title_1)).setTypeface(ContextUtil.tf_trasandina_w03_bold);
        ((TextView)findViewById(R.id.tv_content_1)).setTypeface(ContextUtil.tf_trasandina_w03_bold);
        ((TextView)findViewById(R.id.tv_title_2)).setTypeface(ContextUtil.tf_trasandina_w03_bold);
        ((TextView)findViewById(R.id.tv_sn)).setText("Device ID: " + DeviceInfoApi.getIntance().getSerialNumber());
        tv_warning = findViewById(R.id.tv_warning);
        tv_no_pwd = findViewById(R.id.tv_no_pwd);
        tv_tip = findViewById(R.id.tv_tip);

        ll_lockdevice = findViewById(R.id.ll_lockdevice);
        ll_lockdevice.setVisibility(View.VISIBLE);
        ll_lockdevice_input = findViewById(R.id.ll_lockdevice_input);
        ll_lockdevice_input.setVisibility(View.GONE);

        passwordEditText = findViewById(R.id.pet_pwd);
        passwordEditText.setOnPasswordFullListener(this);

        showPwdErrorWarning(true);

        btnSubmit = findViewById(R.id.btn_submit);
        if (TextUtils.isEmpty(unlock_pwd)) {
            showNoPwdWarning();
        }
        btnSubmit.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                BBLog.e(TAG, "btnSubmit " + new StringBuffer(passwordEditText.getText().toString()).replace(0, 4, "****"));
                if (TextUtils.isEmpty(unlock_pwd)) {
                    BBLog.e(TAG, "" + passwordEditText.getText().toString());
                    Toast.makeText(LockScreenActivity.this, getString(R.string.no_verify_pwd), Toast.LENGTH_SHORT).show();
                    errorAndShakeClear();
                    return;
                }
                if (TextUtils.isEmpty(passwordEditText.getText().toString())) {
                    Toast.makeText(LockScreenActivity.this, getString(R.string.error_input), Toast.LENGTH_SHORT).show();
                    errorAndShakeClear();
                    return;
                }
                if (!TextUtils.isEmpty(unlock_pwd) && !passwordEditText.getText().toString().equals(unlock_pwd)) {
                    Toast.makeText(LockScreenActivity.this, getString(R.string.incorrect_password), Toast.LENGTH_SHORT).show();
                    checkOTPCounter();
                    errorAndShakeClear();
                } else {
                    BBLog.e(TAG, "System.currentTimeMillis()="+System.currentTimeMillis()+"  expire_time="+expire_time);
                    if (expire_time>0 && System.currentTimeMillis()>expire_time) {
                        Toast.makeText(LockScreenActivity.this, getString(R.string.otp_expire), Toast.LENGTH_SHORT).show();
                        checkOTPCounter();
                        errorAndShakeClear();
                    } else{
                        otp_counter = 5;
                        unlock_pwd = "";
                        expire_time = 0L;
                        SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_GPS_GEOFENCE_OTP_COUNTER, otp_counter);
                        BBLog.e(TAG, "Constants.M_GEOFENCE_STATUS = " + Constants.M_GEOFENCE_STATUS);
                        if (Constants.M_GEOFENCE_STATUS == GpsLocationManager.WIPE_DATA) {
                            if (DeviceInfoApi.getIntance().isWisePosPro()) {
                                ContextUtil.setLauncherApp(ContextUtil.getInstance(), UsualData.LOADER_711_PACKAGE_NAME);
                            }
                        } else {
                            if (b_data_wiped) {
                                if (DeviceInfoApi.getIntance().isWisePosPro()) {
                                    ContextUtil.setLauncherApp(ContextUtil.getInstance(), UsualData.LOADER_711_PACKAGE_NAME);
                                }
                            }
                        }
                        Message message = new Message();
                        message.what = 0;
                        mHandler.sendMessageDelayed(message, 200);
                    }
                }
            }
        });
        btnSubmit.setClickable(false);

        if ("1".equals(mode)) {
            showLockDeviceInputDialog();
        } else {
            showLockDeviceDialog();
        }
    }

    class MyTimerTask extends TimerTask {
        @Override
        public void run() {
            // TODO Auto-generated method stub
            m_wipedata_count--;
            BBLog.e(TAG, "countdown: " + m_wipedata_count + " mins");
            SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_GPS_GEOFENCE_LOCK_COUNTDOWN_MINS, m_wipedata_count+"");
            if (m_wipedata_count == 0) {
                Constants.B_UNBOX_RESET_FROM_GEO = true;
                GpsLocationManager.setCurrentGPSStatus(GpsLocationManager.WIPE_DATA);
                Intent intent = new Intent(BroadcastActions.UNBIND_LAUNCHER);
                BBLog.w(TAG, "sendBroadcast: UNBIND_LAUNCHER");
                sendBroadcast(intent, RequestPermission.REQUEST_PERMISSION_BBPOS);
                timer.cancel();
            }
        }
    }

    private void startTimeSchedule() {
        BBLog.e(TAG, "start timer: " + m_wipedata_count + " mins");
        if (mTimerTask == null) {
            mTimerTask = new MyTimerTask();
        } else {
            mTimerTask.cancel();
            mTimerTask = null;
            mTimerTask = new MyTimerTask();
        }

        if (timer == null) {
            timer = new Timer();
        } else {
            timer.cancel();
            timer.purge();
            timer = null;
            timer = new Timer();
        }

        timer.schedule(mTimerTask, 60 * 1000, 60 * 1000);
    }

    private void stopTimeSchedule() {
        BBLog.e(TAG, "LockScreen timer cancelled");
        if (mTimerTask != null) {
            mTimerTask.cancel();
            mTimerTask = null;
        }

        if (timer != null) {
            timer.cancel();
            timer.purge();
            timer = null;
        }
    }

    private void showLockDeviceDialog() {
        BBLog.e(TAG, "LockScreenActivity showLockDeviceDialog");
        if (ll_lockdevice.getVisibility() == View.GONE) {
            ll_lockdevice = findViewById(R.id.ll_lockdevice);
            ll_lockdevice.setVisibility(View.VISIBLE);
            ll_lockdevice_input = findViewById(R.id.ll_lockdevice_input);
            ll_lockdevice_input.setVisibility(View.GONE);
        }
    }

    private void showLockDeviceInputDialog() {
        BBLog.e(TAG, "LockScreenActivity showLockDeviceInputDialog");
        if (ll_lockdevice_input.getVisibility() == View.GONE) {
            ll_lockdevice = findViewById(R.id.ll_lockdevice);
            ll_lockdevice.setVisibility(View.GONE);
            ll_lockdevice_input = findViewById(R.id.ll_lockdevice_input);
            ll_lockdevice_input.setVisibility(View.VISIBLE);
            passwordEditText.setText("");
        }
    }

    @Override
    public void passwordFull(String password, boolean bfull) {
        if (bfull) {
//            if (!TextUtils.isEmpty(unlock_pwd) && expire_time!=0) {
                btnSubmit.setTextSize(TypedValue.COMPLEX_UNIT_SP, 25);
                btnSubmit.setText("SUBMIT");
                btnSubmit.setBackground(getDrawable(R.drawable.btn_bord_green));
                btnSubmit.setClickable(true);
                btnSubmit.setTextColor(getResources().getColor(R.color.white));
//            }
        } else {
            btnSubmit.setBackground(getDrawable(R.drawable.btn_bord_gray));
            btnSubmit.setClickable(false);
            btnSubmit.setTextColor(getResources().getColor(R.color.slate_grey));
        }
    }

    private boolean isScreenOn() {
        PowerManager pm = (PowerManager) getSystemService(Context.POWER_SERVICE);
        //如果不是在Activity里面需要得到当时的上下文句柄 用context.getSystemService...
        BBLog.e(TAG, "isScreenOn() = " + pm.isInteractive());
        return pm.isInteractive();
    }

    private void showStartWipingUI() {
        passwordEditText.setEnabled(false);
        tv_warning.setText("Please wait .... Wiping Device\n");
        tv_warning.setVisibility(View.VISIBLE);
    }

    private void showStopWipingUI() {
        passwordEditText.setEnabled(true);
        tv_warning.setText("Please wait .... Wiping Device\n");
        tv_warning.setVisibility(View.GONE);
    }

    private void showPwdErrorWarning(boolean b) {
        if (b && !b_data_wiped) {
            tv_warning.setText("WARNING: All data on the device will be wiped out after " + otp_counter + " incorrect attempts.");
            tv_warning.setVisibility(View.VISIBLE);
        } else {
            tv_warning.setText("WARNING: All data on the device will be wiped out after " + otp_counter + " incorrect attempts.");
            tv_warning.setVisibility(View.GONE);
        }
    }

    private void checkOTPCounter() {
        if (GpsLocationManager.LOCK_SCREEN == Constants.M_GEOFENCE_STATUS) {
            if (otp_counter > 0) {
                otp_counter--;
                SharedPreferencesUtils.setSharePreferencesValue(UsualData.SHARED_PREFERENCES_GPS_GEOFENCE_OTP_COUNTER, otp_counter);
                if (otp_counter <= 0) {
                    showPwdErrorWarning(false);
                    showStartWipingUI();
                    Constants.B_UNBOX_RESET_FROM_GEO = true;
                    GpsLocationManager.setCurrentGPSStatus(GpsLocationManager.WIPE_DATA);
                    Intent intent = new Intent(BroadcastActions.UNBIND_LAUNCHER);
                    BBLog.w(TAG, "sendBroadcast: UNBIND_LAUNCHER");
                    sendBroadcast(intent, RequestPermission.REQUEST_PERMISSION_BBPOS);
                    if (timer != null) {
                        timer.cancel();
                    }
                } else {
                    showPwdErrorWarning(true);
                    Toast.makeText(LockScreenActivity.this, " All data will be wiped out after " + otp_counter + " times Error Input!", Toast.LENGTH_LONG).show();
                }
            }
        } else {
            showPwdErrorWarning(false);
        }
    }

    private void errorAndShakeClear() {
        Animation shake = AnimationUtils.loadAnimation(this, R.anim.shake);//加载动画资源文件
        passwordEditText.startAnimation(shake); //给组件播放动画效果
        passwordEditText.setText("");
    }

    private void showNoPwdWarning() {
        tv_no_pwd.setVisibility(View.VISIBLE);
        btnSubmit.setVisibility(View.INVISIBLE);
        tv_warning.setVisibility(View.GONE);
        passwordEditText.setVisibility(View.INVISIBLE);
        tv_tip.setVisibility(View.INVISIBLE);
    }

    private void hideNoPwdWarning() {
        tv_no_pwd.setVisibility(View.INVISIBLE);
        btnSubmit.setVisibility(View.VISIBLE);
        if (Constants.M_GEOFENCE_STATUS!=GpsLocationManager.WIPE_DATA) {
            tv_warning.setVisibility(View.VISIBLE);
        }
        passwordEditText.setVisibility(View.VISIBLE);
        tv_tip.setVisibility(View.VISIBLE);
    }
}
