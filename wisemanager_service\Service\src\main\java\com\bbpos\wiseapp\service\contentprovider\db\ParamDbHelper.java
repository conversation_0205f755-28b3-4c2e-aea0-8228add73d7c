package com.bbpos.wiseapp.service.contentprovider.db;

import android.content.Context;
import android.database.sqlite.SQLiteDatabase;
import android.database.sqlite.SQLiteOpenHelper;
import android.util.Log;

import com.bbpos.wiseapp.logger.BBLog;
import com.bbpos.wiseapp.tms.utils.Constants;

public class ParamDbHelper extends SQLiteOpenHelper {
	private final static String DB_NAME = "db_param.db";
	private final static int VERSION = 1;
	
	public final static String TBL_PARAM_NAME = "tblParam";
	public final static String TBL_PARAM_PATH_NAME = "tblParamPath";
	
	private final static String creatParaTblSql ="create table "+TBL_PARAM_NAME+
			"(_id INTEGER PRIMARY KEY," +
			"idx varchar(8)," +
			"pkg_name varchar(128)," +  
			"param_key varchar(32)," +  
			"param_value varchar(128),"+
			"data_src varchar(8))" ;


	private final static String creatParaPathTblSql ="create table "+TBL_PARAM_PATH_NAME+
			"(_id INTEGER PRIMARY KEY," +
			"param_file_path varchar(128)," +  
			"param_file_md5 varchar(32)," +  
			"param_file_last_modify_time INTEGER," +  
			"param_file_parse_stat varchar(2))" ;

	public ParamDbHelper(Context context) {
		super(context, DB_NAME, null, VERSION);
	}

	@Override
	public void onCreate(SQLiteDatabase db) {
		BBLog.v(BBLog.TAG, "create tbl.");
		db.execSQL(creatParaTblSql);
		db.execSQL(creatParaPathTblSql);
	}

	@Override
	public void onUpgrade(SQLiteDatabase db, int oldVersion, int newVersion) {

	}
	
	@Override
	public void close(){
		BBLog.e(BBLog.TAG, "ParamDbHelper is closing");
		super.close();
	}

}
