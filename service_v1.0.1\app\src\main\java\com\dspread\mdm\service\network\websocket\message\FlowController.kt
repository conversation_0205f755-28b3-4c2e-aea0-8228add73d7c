package com.dspread.mdm.service.network.websocket.message

import com.dspread.mdm.service.SmartMdmServiceApp
import com.dspread.mdm.service.modules.provisioning.ProvisioningManager
import com.dspread.mdm.service.utils.log.Logger
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.atomic.AtomicLong

/**
 * WebSocket消息流量控制器
 * 
 * 核心原则：
 * 1. 只控制被动式上送：设备状态变化触发的上送
 * 2. 不影响主动式上送：平台指令要求的上送必须立即响应
 * 3. 事件变更触发：只有状态真正变化才上报
 * 4. 模式可配：根据uploadMode控制上报策略
 * 
 * 设计理念：
 * - 被动上送：事件驱动，需要流量控制
 * - 主动上送：指令驱动，立即响应
 * - 10万台设备 × 合理频率 = 可控流量
 */
object FlowController {
    
    private const val TAG = "FlowController"
    
    // ==================== 事件驱动配置 ====================
    
    /**
     * 上送模式配置（基于事件变更）
     */
    data class ModeConfig(
        val enabledEvents: Set<String>,     // 该模式下启用的事件类型
        val antiSpamInterval: Long,         // 防刷间隔（同类型消息）
        val burstProtection: Int,           // 突发保护（每分钟最大消息数）
        val description: String
    )
    
    /**
     * 各模式的事件控制配置
     */
    private val modeConfigs = mapOf(
        "0" to ModeConfig(  // 省电模式 - 只上报关键事件
            enabledEvents = setOf(
                // 电池关键事件
                "battery_low_critical", "charging_state_change", "temperature_abnormal",
                // 网络关键事件  
                "network_connect", "network_disconnect",
                // 应用关键事件
                "app_install", "app_uninstall", "service_change",
                // 系统关键事件
                "crash", "security_event", "first_connection"
            ),
            antiSpamInterval = 30000L,      // 30秒防刷
            burstProtection = 10,           // 每分钟最多10条
            description = "省电模式 - 仅关键事件"
        ),
        
        "1" to ModeConfig(  // 平衡模式 - 上报重要变化
            enabledEvents = setOf(
                // 电池事件
                "battery_low_critical", "charging_state_change", "temperature_abnormal",
                "battery_level_change_5", "temperature_change_5", "battery_status_change", "system_shutdown",
                // 网络事件
                "network_connect", "network_disconnect", "wifi_switch", "signal_strength_change",
                // 应用事件
                "app_install", "app_uninstall", "app_update", "service_change",
                // 位置事件
                "location_geofence_change", "location_change",
                // 系统事件
                "crash", "security_event", "first_connection", "manual_trigger", "scheduled_report"
            ),
            antiSpamInterval = 10000L,      // 10秒防刷
            burstProtection = 20,           // 每分钟最多20条
            description = "平衡模式 - 重要变化"
        ),
        
        "2" to ModeConfig(  // 实时模式 - 上报所有变化
            enabledEvents = setOf(
                // 所有事件都启用
                "battery_low_critical", "charging_state_change", "temperature_abnormal",
                "battery_level_change_5", "temperature_change_5", "battery_status_change", "system_shutdown",
                "network_connect", "network_disconnect", "wifi_switch", "signal_strength_change", "ip_change",
                "app_install", "app_uninstall", "app_update", "service_change", "permission_change",
                "location_geofence_change", "location_change", "hardware_change",
                "crash", "security_event", "system_error", "warning_event",
                "first_connection", "manual_trigger", "significant_change", "scheduled_report",
                "device_usage_change", "user_interaction_change"
            ),
            antiSpamInterval = 3000L,       // 3秒防刷
            burstProtection = 50,           // 每分钟最多50条
            description = "实时模式 - 所有变化"
        )
    )
    
    // ==================== 状态管理 ====================
    private val lastUploadTime = AtomicLong(0L)
    private val uploadHistory = ConcurrentHashMap<Long, Int>() // 时间窗口 -> 消息数量
    private val totalUploadCount = AtomicLong(0L)
    private val rejectedCount = AtomicLong(0L)
    
    // 主动/被动上送统计
    private val activeUploadCount = AtomicLong(0L)    // 主动式上送计数
    private val passiveUploadCount = AtomicLong(0L)   // 被动式上送计数
    
    // 消息类型的上次上送时间
    private val messageLastUploadTime = ConcurrentHashMap<String, Long>()
    
    // WebSocket重连保护：记录首次连接消息的发送时间
    private val firstConnectionMessageTime = ConcurrentHashMap<String, Long>()
    
    // 首次连接消息的最小间隔（防止频繁重连导致重复发送）
    private val FIRST_CONNECTION_MIN_INTERVAL = mapOf(
        "C0901" to 300000L,   // 应用信息：5分钟（减少频率）
        "C0902" to 120000L,   // 电池状态：2分钟（减少频率）
        "C0903" to 600000L,   // 数据信息：10分钟（减少频率）
        "C0904" to 300000L,   // 网络状态：5分钟（减少频率）
        "C0109" to 900000L    // 终端信息：15分钟（减少频率）
    )
    
    // 首次连接相关的触发条件
    private val firstConnectionTriggers = setOf(
        "first_connection",        // 首次连接
        "websocket_reconnect"      // WebSocket重连
    )
    
    // ==================== 主动/被动上送区分 ====================
    
    /**
     * 主动式上送触发条件（业务关联，立即响应）
     */
    private val activeTriggers = setOf(
        // 平台指令触发
        "platform_request",        // 平台请求
        "command_trigger",         // 指令触发
        "query_request",           // 查询请求
        "api_call",               // API调用
        "remote_command",         // 远程指令
        
        // 业务流程触发
        "app_install",            // 应用安装（业务需要）
        "app_uninstall",          // 应用卸载（业务需要）
        "app_update",             // 应用更新（业务需要）
        "rulebase_update",        // 规则引擎更新
        "rulebase_execute",       // 规则引擎执行
        "task_start",             // 任务开始
        "task_complete",          // 任务完成
        "task_failed",            // 任务失败
        "task_result_upload",     // 任务结果上送
        
        // 管理触发
        "admin_trigger",          // 管理员触发
        "manual_trigger",         // 手动触发

        // 缓存消息重发（重要消息，强制发送）
        "cached_message_resend",  // 缓存消息重发

        // 网络恢复（重要事件，但需要限制频率）
        // 注意：NETWORK_RECOVERY 虽然重要，但移回被动触发以避免过于频繁
        
        // 定时控制的主动上送
        "heartbeat_timer",        // 心跳定时器触发（heartbeatTime控制）
        "terminal_info_timer",    // 终端信息定时器触发（terminalInfoTime控制）
        "daily_traffic_report",   // 每日流量统计上报（定时任务）

        // 关键硬件事件（绕过流量控制，立即上送）
        "charging_state_change",  // 拔插电源
        "battery_low_critical",   // 电量严重不足
        "temperature_abnormal",   // 温度异常
        "system_shutdown",        // 系统关机
        "network_connect",        // 网络连接
        "network_disconnect"      // 网络断开
    )
    
    /**
     * 被动式上送触发条件（状态变化，需要流量控制）
     */
    private val passiveTriggers = setOf(
        // 电池状态变化（C0902）- 非关键事件
        "battery_level_change_5",   // 电量变化5%
        "temperature_change_5",     // 温度变化5度
        "battery_status_change",    // 其他电池状态变化

        // 网络状态变化（C0904）- 非关键事件
        "wifi_switch",             // WiFi切换
        "signal_strength_change",   // 信号强度变化
        "ip_change",               // IP地址变化
        "NETWORK_RECOVERY",        // 网络恢复
        
        // 位置信息变化（C0903）
        "location_geofence_change", // 地理围栏变化
        "location_change",          // 位置变化
        "hardware_change",          // 硬件状态变化
        
        // 设备使用状态变化（C0201）
        "device_usage_change",      // 设备使用状态变化
        "user_interaction_change",  // 用户交互状态变化
        
        // 崩溃和异常（C0202）
        "crash",                   // 系统崩溃
        "security_event",          // 安全事件
        "system_error",            // 系统错误
        "warning_event",           // 警告事件
        
        // 其他状态变化
        "service_change",          // 服务状态变化
        "permission_change",       // 权限变化
        "scheduled_report"         // 定时上报（其他类型的定时上报）
    )
    
    /**
     * 流量控制结果
     */
    data class FlowResult(
        val allowed: Boolean,           // 是否允许上送
        val reason: String,             // 决策原因
        val suggestedDelay: Long = 0L,  // 建议延迟时间
        val currentMode: String = "",   // 当前模式
        val stats: String = ""          // 统计信息
    )
    
    /**
     * 智能流量控制检查（区分主动/被动上送）
     * 
     * @param messageType 消息类型（如C0901, C0902等）
     * @param trigger 触发事件（如battery_level_change_5或platform_request）
     * @param forceUpload 是否强制上送（绕过所有限制）
     * @return 流量控制结果
     */
    fun checkUpload(
        messageType: String,
        trigger: String,
        forceUpload: Boolean = false
    ): FlowResult {

        if (forceUpload) {
            recordUpload(messageType, trigger, true)
            return FlowResult(true, "强制上送", currentMode = getCurrentMode())
        }

        // C0000响应确认消息直接通过，不受流量控制
        if (messageType == "C0000") {
            recordUpload(messageType, trigger, true)
            return FlowResult(
                true,
                "C0000响应确认消息，业务需要立即发送",
                currentMode = getCurrentMode(),
                stats = "响应确认消息: $messageType"
            )
        }

        // 主动式上送直接通过，不受流量控制
        if (trigger in activeTriggers) {
            recordUpload(messageType, trigger, true)
            return FlowResult(
                true,
                "主动式上送，立即响应",
                currentMode = getCurrentMode(),
                stats = "业务流程触发: $trigger"
            )
        }
        
        // 首次连接消息特殊处理（有重连保护但不受uploadMode限制）
        if (trigger in firstConnectionTriggers) {
            val currentTime = System.currentTimeMillis()
            val currentMode = getCurrentMode()
            val minInterval = FIRST_CONNECTION_MIN_INTERVAL[messageType] ?: 30000L
            val lastFirstConnectionTime = firstConnectionMessageTime[messageType] ?: 0L
            val firstConnectionInterval = currentTime - lastFirstConnectionTime
            
            if (firstConnectionInterval < minInterval) {
                val delay = minInterval - firstConnectionInterval
                recordUpload(messageType, trigger, false)
                return FlowResult(
                    false,
                    "$messageType 首次连接保护，防止频繁重连，需等待${delay}ms",
                    delay,
                    currentMode,
                    "重连间隔: ${firstConnectionInterval}ms < ${minInterval}ms"
                )
            }
            
            // 记录首次连接时间并允许上送
            firstConnectionMessageTime[messageType] = currentTime
            recordUpload(messageType, trigger, true)
            return FlowResult(
                true,
                "首次连接上送通过",
                0L,
                currentMode,
                "首次连接消息: $trigger"
            )
        }
        
        // 被动式上送需要流量控制
        if (trigger !in passiveTriggers) {
            Logger.wsmW("未知触发条件: $trigger，默认按被动式处理")
        }
        
        val currentTime = System.currentTimeMillis()
        val currentMode = getCurrentMode()
        val config = modeConfigs[currentMode] ?: modeConfigs["1"]!!
        
        try {
            // 1. 检查该事件是否在当前模式下启用
            if (trigger !in config.enabledEvents) {
                recordUpload(messageType, trigger, false)
                return FlowResult(
                    false,
                    "被动事件 '$trigger' 在${config.description}下未启用",
                    currentMode = currentMode
                )
            }
            
            // 2. 检查防刷间隔（同类型消息）
            val lastMessageTime = messageLastUploadTime[messageType] ?: 0L
            val messageInterval = currentTime - lastMessageTime
            
            if (messageInterval < config.antiSpamInterval) {
                val delay = config.antiSpamInterval - messageInterval
                recordUpload(messageType, trigger, false)
                return FlowResult(
                    false,
                    "$messageType 防刷保护，需等待${delay}ms",
                    delay,
                    currentMode,
                    "间隔: ${messageInterval}ms < ${config.antiSpamInterval}ms"
                )
            }
            
            // 3. 检查突发保护（每分钟消息数限制）
            val oneMinuteAgo = currentTime - 60000L
            val messagesInLastMinute = uploadHistory.entries
                .filter { it.key >= oneMinuteAgo }
                .sumOf { it.value }
                
            if (messagesInLastMinute >= config.burstProtection) {
                recordUpload(messageType, trigger, false)
                return FlowResult(
                    false,
                    "突发保护：1分钟内已发送${messagesInLastMinute}条消息，达到上限${config.burstProtection}",
                    suggestedDelay = 60000L,
                    currentMode = currentMode,
                    stats = "1分钟内消息数: $messagesInLastMinute >= ${config.burstProtection}"
                )
            }
            
            // 4. 通过所有检查，允许被动上送
            recordUpload(messageType, trigger, true)
            return FlowResult(
                true,
                "被动式上送通过",
                0L,
                currentMode,
                "1分钟内消息数: $messagesInLastMinute/${config.burstProtection}, 事件: $trigger"
            )
            
        } catch (e: Exception) {
            Logger.wsmE("流量控制检查异常", e)
            recordUpload(messageType, trigger, false)
            return FlowResult(false, "流量控制异常: ${e.message}", currentMode = currentMode)
        }
    }

    /**
     * 记录上送统计（区分主动/被动）
     */
    private fun recordUpload(messageType: String, trigger: String, allowed: Boolean) {
        val currentTime = System.currentTimeMillis()
        val isActive = trigger in activeTriggers

        if (allowed) {
            lastUploadTime.set(currentTime)
            messageLastUploadTime[messageType] = currentTime
            totalUploadCount.incrementAndGet()

            // 区分主动/被动统计
            if (isActive) {
                activeUploadCount.incrementAndGet()
                Logger.wsm("$messageType 主动式上送: $trigger (主动: ${activeUploadCount.get()})")
            } else {
                passiveUploadCount.incrementAndGet()
                Logger.wsm("$messageType 被动式上送: $trigger (被动: ${passiveUploadCount.get()})")
            }

            // 记录到历史窗口（按分钟分组）
            val windowKey = currentTime / 60000 * 60000 // 按分钟分组
            uploadHistory[windowKey] = (uploadHistory[windowKey] ?: 0) + 1

        } else {
            rejectedCount.incrementAndGet()
            Logger.wsm("$messageType 被动式上送拒绝: $trigger (拒绝: ${rejectedCount.get()})")
        }

        // 清理过期历史记录
        cleanupHistory()
    }

    /**
     * 获取当前uploadMode
     */
    private fun getCurrentMode(): String {
        return try {
            val context = SmartMdmServiceApp.instance
            val provisioningManager = ProvisioningManager.getInstance(context)
            val config = provisioningManager.getCurrentConfig()
            config?.polling?.uploadMode ?: "1"
        } catch (e: Exception) {
            Logger.wsmE("获取uploadMode失败，使用默认值", e)
            "1"
        }
    }

    /**
     * 清理过期的历史记录
     */
    private fun cleanupHistory() {
        val currentTime = System.currentTimeMillis()
        val expireTime = currentTime - 120000L // 保留2分钟历史

        uploadHistory.entries.removeAll { it.key < expireTime }
    }

    /**
     * 获取流量控制状态
     */
    fun getStatus(): String {
        val currentTime = System.currentTimeMillis()
        val currentMode = getCurrentMode()
        val config = modeConfigs[currentMode] ?: modeConfigs["1"]!!

        val oneMinuteAgo = currentTime - 60000L
        val recentMessages = uploadHistory.entries
            .filter { it.key >= oneMinuteAgo }
            .sumOf { it.value }

        return buildString {
            appendLine("智能流量控制器状态:")
            appendLine("  当前模式: $currentMode (${config.description})")
            appendLine("  配置: 防刷间隔=${config.antiSpamInterval}ms, 突发保护=${config.burstProtection}条/分钟")
            appendLine("  📈 总上送: ${totalUploadCount.get()}, 拒绝: ${rejectedCount.get()}")
            appendLine("  主动式上送: ${activeUploadCount.get()} (平台指令)")
            appendLine("  被动式上送: ${passiveUploadCount.get()} (事件驱动)")
            appendLine("  ⏰ 1分钟内消息: $recentMessages/${config.burstProtection}")

            appendLine("  被动式启用事件:")
            config.enabledEvents.chunked(3).forEach { chunk ->
                appendLine("    ${chunk.joinToString(", ")}")
            }

            appendLine("  主动式触发条件:")
            activeTriggers.chunked(3).forEach { chunk ->
                appendLine("    ${chunk.joinToString(", ")}")
            }

            appendLine("  消息类型上次上送时间:")
            messageLastUploadTime.forEach { (type, time) ->
                val ago = currentTime - time
                appendLine("    $type: ${ago}ms前")
            }
        }
    }

    /**
     * 重置流量控制状态（调试用）
     */
    fun reset() {
        uploadHistory.clear()
        messageLastUploadTime.clear()
        firstConnectionMessageTime.clear()
        rejectedCount.set(0L)
        Logger.wsm("流量控制状态已重置")
    }

    /**
     * 重置首次连接保护（WebSocket重连时调用）
     */
    fun resetFirstConnectionProtection() {
        firstConnectionMessageTime.clear()
        Logger.wsm("首次连接保护已重置，允许重新发送C09系列消息")
    }

    /**
     * 检查是否为主动式上送
     */
    fun isActiveUpload(trigger: String): Boolean {
        return trigger in activeTriggers
    }

    /**
     * 检查是否为被动式上送
     */
    fun isPassiveUpload(trigger: String): Boolean {
        return trigger in passiveTriggers
    }

    /**
     * 检查特定事件是否启用（仅对被动式上送有效）
     */
    fun isEventEnabled(trigger: String): Boolean {
        // 主动式上送总是启用
        if (trigger in activeTriggers) {
            return true
        }

        // 被动式上送检查配置
        val currentMode = getCurrentMode()
        val config = modeConfigs[currentMode] ?: modeConfigs["1"]!!
        return trigger in config.enabledEvents
    }

    /**
     * 获取当前模式下启用的事件列表
     */
    fun getEnabledEvents(): Set<String> {
        val currentMode = getCurrentMode()
        val config = modeConfigs[currentMode] ?: modeConfigs["1"]!!
        return config.enabledEvents
    }

    /**
     * 获取流量统计
     */
    fun getTrafficStats(): String {
        val currentTime = System.currentTimeMillis()
        val oneMinuteAgo = currentTime - 60000L
        val fiveMinutesAgo = currentTime - 300000L

        val lastMinute = uploadHistory.entries.filter { it.key >= oneMinuteAgo }.sumOf { it.value }
        val lastFiveMinutes = uploadHistory.entries.filter { it.key >= fiveMinutesAgo }.sumOf { it.value }

        return buildString {
            appendLine("流量统计:")
            appendLine("  最近1分钟: $lastMinute 条消息")
            appendLine("  最近5分钟: $lastFiveMinutes 条消息")
            appendLine("  总计上送: ${totalUploadCount.get()} 条")
            appendLine("  总计拒绝: ${rejectedCount.get()} 条")
            appendLine("  成功率: ${if (totalUploadCount.get() > 0) String.format("%.1f", totalUploadCount.get() * 100.0 / (totalUploadCount.get() + rejectedCount.get())) else "0.0"}%")
        }
    }
}
