2025-08-18 18:18:05.897  6903-6930  LogStream               com.dspread.mdm.service              I  🔧 [LogStreamManager] 检查日志队列进行压缩，队列长度: 0
2025-08-18 18:18:05.905  6903-6930  LogStream               com.dspread.mdm.service              I  🔧 [LogCollector] 检查存储空间并清理旧文件
2025-08-18 18:18:05.913  6903-6930  LogStream               com.dspread.mdm.service              I  🔧 [LogCollector] 当前压缩文件总大小: 0B / 250MB
2025-08-18 18:18:05.922  6903-6930  LogStream               com.dspread.mdm.service              I  🔧 [LogCollector] 当前原始日志总大小: 44KB / 250MB
2025-08-18 18:18:06.004  6903-6976  Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"request_time":"1755512284797","org_request_time":"1755512285278","org_request_id":"1755512285278C0109","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755512284797S0000","serialNo":"01354090202503050399"}
2025-08-18 18:18:06.015  6903-6976  WebSocket               com.dspread.mdm.service              I  🔧 收到响应消息: transCode=S0000, orgRequestId=1755512285278C0109, state=0, remark=
2025-08-18 18:18:06.022  6903-6976  WebSocket               com.dspread.mdm.service              I  🔧 终端信息上传成功
2025-08-18 18:18:06.030  6903-6976  WebSocket               com.dspread.mdm.service              I  🔧 终端信息上传成功标志已设置
2025-08-18 18:18:11.553  6903-6976  Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"msg_state":"P","request_time":"1755512290518","data":{"param":{"beginDate":"2024-08-18 10:18:10","taskType":"05","period":"1","endDate":"9999-12-31 23:59:59","serviceId":"856c2e6d2c7c4f60bc0fadcf9b9ad650","serviceName":"Log Streaming","command":"C03","url":"https://smartms.s3.sa-east-1.amazonaws.com/icon/5e80041b23fb441695e88253d4542c5b.png","taskId":"1755512290518"},"c_type":"CALL_LOG_STREAM"},"expire_time":1756117090,"tranCode":"SC004","request_id":"1755512290518SC004","version":"1","serialNo":"01354090202503050399"}
2025-08-18 18:18:11.563  6903-6976  WebSocket               com.dspread.mdm.service              I  🔧 准备发送响应确认 - requestId: 1755512290518SC004, needResponse: true
2025-08-18 18:18:11.586  6903-6976  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0000","serialNo":"01354090202503050399","request_time":"1755512291570","request_id":"1755512291570C0000","version":"1","org_request_id":"1755512290518SC004","org_request_time":"1755512290518","response_state":"0","myVersionName":"1.1.01.20250818.DSPREAD.MDM.SERVICE","terminalDate":"20250818181811"}
2025-08-18 18:18:11.610  6903-6976  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0000","serialNo":"01354090202503050399","request_time":"1755512291595","request_id":"1755512291595C0000","version":"1","org_request_id":"1755512290518SC004","org_request_time":"1755512290518","response_state":"0","myVersionName":"1.1.01.20250818.DSPREAD.MDM.SERVICE","terminalDate":"20250818181811"}
2025-08-18 18:18:11.618  6903-6976  WebSocket               com.dspread.mdm.service              I  🔧 已发送C0000响应确认: requestId=1755512290518SC004
2025-08-18 18:18:11.627  6903-6976  WebSocket               com.dspread.mdm.service              I  🔧 处理命令: SC004
2025-08-18 18:18:11.634  6903-6976  WebSocket               com.dspread.mdm.service              I  🔧 收到SC004命令，类型: CALL_LOG_STREAM
2025-08-18 18:18:11.642  6903-6976  WebSocket               com.dspread.mdm.service              I  🔧 服务器请求启动日志流
2025-08-18 18:18:11.649  6903-6976  LogStream               com.dspread.mdm.service              I  🔧 [LogStreamWebSocketHandler] 处理CALL_LOG_STREAM请求
2025-08-18 18:18:11.660  6903-6976  LogStream               com.dspread.mdm.service              I  🔧 [LogStreamWebSocketHandler] 日志流参数: {"beginDate":"2024-08-18 10:18:10","taskType":"05","period":"1","endDate":"9999-12-31 23:59:59","serviceId":"856c2e6d2c7c4f60bc0fadcf9b9ad650","serviceName":"Log Streaming","command":"C03","url":"https:\/\/smartms.s3.sa-east-1.amazonaws.com\/icon\/5e80041b23fb441695e88253d4542c5b.png","taskId":"1755512290518"}
2025-08-18 18:18:11.669  6903-6976  LogStream               com.dspread.mdm.service              I  🔧 [LogStreamWebSocketHandler] 解析参数 - taskType: 05, serviceId: 856c2e6d2c7c4f60bc0fadcf9b9ad650
2025-08-18 18:18:11.676  6903-6976  LogStream               com.dspread.mdm.service              I  🔧 [LogStreamWebSocketHandler] 时间范围 - 开始: 2024-08-18 10:18:10, 结束: 9999-12-31 23:59:59
2025-08-18 18:18:11.684  6903-6976  LogStream               com.dspread.mdm.service              I  🔧 [LogStreamWebSocketHandler] 上传URL: https://smartms.s3.sa-east-1.amazonaws.com/icon/5e80041b23fb441695e88253d4542c5b.png
2025-08-18 18:18:11.693  6903-6930  LogStream               com.dspread.mdm.service              I  🔧 [LogStreamWebSocketHandler] ========== 开始处理日志流上传 ==========
2025-08-18 18:18:11.700  6903-6930  LogStream               com.dspread.mdm.service              I  🔧 [LogStreamWebSocketHandler] 原始参数 - beginDate: 2024-08-18 10:18:10, endDate: 9999-12-31 23:59:59, period: 1
2025-08-18 18:18:11.712  6903-6930  LogStream               com.dspread.mdm.service              I  🔧 [LogStreamWebSocketHandler] 处理时间范围 - 当前时间: 2025-08-18 18:18:11
2025-08-18 18:18:11.720  6903-6930  LogStream               com.dspread.mdm.service              I  🔧 [LogStreamWebSocketHandler] 检测到不明确时间范围，使用period计算实际时间
2025-08-18 18:18:11.727  6903-6930  LogStream               com.dspread.mdm.service              I  🔧 [LogStreamWebSocketHandler] Period解析结果: 1小时
2025-08-18 18:18:11.735  6903-6930  LogStream               com.dspread.mdm.service              I  🔧 [LogStreamWebSocketHandler] 计算后时间范围 - 开始: 2025-08-18 18:18:11, 结束: 2025-08-18 19:18:11
2025-08-18 18:18:11.743  6903-6930  LogStream               com.dspread.mdm.service              I  🔧 [LogStreamWebSocketHandler] 处理后时间范围 - 开始: 2025-08-18 18:18:11, 结束: 2025-08-18 19:18:11
2025-08-18 18:18:11.761  6903-6930  LogStream               com.dspread.mdm.service              I  🔧 [LogStreamWebSocketHandler] 时间判断 - 当前: 1755512291751, 开始: 1755512291000, 结束: 1755515891000
2025-08-18 18:18:11.768  6903-6930  LogStream               com.dspread.mdm.service              I  🔧 [LogStreamWebSocketHandler] 时间范围检查结果: true
2025-08-18 18:18:11.776  6903-6930  LogStream               com.dspread.mdm.service              I  🔧 [LogStreamWebSocketHandler] 时间范围判断结果 - 是否需要上传: true
2025-08-18 18:18:11.783  6903-6930  LogStream               com.dspread.mdm.service              I  🔧 [LogStreamWebSocketHandler] 步骤1: 上传Recent日志
2025-08-18 18:18:11.790  6903-6930  LogStream               com.dspread.mdm.service              I  🔧 [LogStreamWebSocketHandler] 开始上传Recent日志
2025-08-18 18:18:11.810  6903-6930  LogStream               com.dspread.mdm.service              I  🔧 [LogCollector] 找到最新日志文件: mdm_log_20250818_181735.log
2025-08-18 18:18:11.818  6903-6930  LogStream               com.dspread.mdm.service              I  🔧 [LogStreamWebSocketHandler] 当前日志文件: mdm_log_20250818_181735.log, 大小: 52125
2025-08-18 18:18:11.827  6903-6930  LogStream               com.dspread.mdm.service              I  🔧 [LogStreamWebSocketHandler] 生成Recent日志 - 原文件大小: 52260B, Recent限制: 262144B
2025-08-18 18:18:11.835  6903-6930  LogStream               com.dspread.mdm.service              I  🔧 [LogStreamWebSocketHandler] Recent文件路径: /storage/emulated/0/Android/data/com.dspread.mdm.service/files/logs/upload/recent.log
2025-08-18 18:18:11.850  6903-6930  LogStream               com.dspread.mdm.service              I  🔧 [LogStreamWebSocketHandler] 文件≤256KB，直接复制全部内容: 52260B
2025-08-18 18:18:11.859  6903-6930  LogStream               com.dspread.mdm.service              I  🔧 [LogStreamWebSocketHandler] Recent日志文件生成成功: recent.log, 大小: 52578B
2025-08-18 18:18:11.867  6903-6930  LogStream               com.dspread.mdm.service              I  🔧 [LogStreamWebSocketHandler] Recent日志文件生成成功: recent.log, 大小: 52578
2025-08-18 18:18:11.875  6903-6930  LogStream               com.dspread.mdm.service              I  🔧 [LogStreamWebSocketHandler] 开始压缩Recent日志: recent.log -> recent.gz
2025-08-18 18:18:11.883  6903-6930  LogStream               com.dspread.mdm.service              I  🔧 [LogStreamWebSocketHandler] 压缩目标路径: /storage/emulated/0/Android/data/com.dspread.mdm.service/files/logs/upload/recent.gz
2025-08-18 18:18:11.893  6903-6930  LogStream               com.dspread.mdm.service              I  🔧 [LogProcessor] 开始压缩文件: recent.log
2025-08-18 18:18:11.911  6903-6930  LogStream               com.dspread.mdm.service              I  🔧 [LogProcessor] 压缩完成: recent.log -> recent.gz
2025-08-18 18:18:11.919  6903-6930  LogStream               com.dspread.mdm.service              I  🔧 [LogProcessor] 压缩比: 19.14% (52578B -> 10064B)
2025-08-18 18:18:11.947  6903-6973  LogStream               com.dspread.mdm.service              I  🔧 [LogStreamWebSocketHandler] Recent日志压缩成功: /storage/emulated/0/Android/data/com.dspread.mdm.service/files/logs/upload/recent.gz
2025-08-18 18:18:11.955  6903-6973  LogStream               com.dspread.mdm.service              I  🔧 [LogStreamWebSocketHandler] Recent日志压缩完成: recent.gz, 大小: 10064
2025-08-18 18:18:11.963  6903-6973  LogStream               com.dspread.mdm.service              I  🔧 [LogStreamWebSocketHandler] 开始上传Recent文件到S3
2025-08-18 18:18:11.972  6903-6973  LogStream               com.dspread.mdm.service              I  🔧 [S3LogUploader] 开始上传Recent文件: recent.gz
2025-08-18 18:18:11.980  6903-6973  LogStream               com.dspread.mdm.service              I  🔧 [S3LogUploader] Recent文件S3路径: dev/01354090202503050399/recent.gz
2025-08-18 18:18:12.050  6903-6973  LogStream               com.dspread.mdm.service              I  🔧 [S3LogUploader] V4签名生成完成
2025-08-18 18:18:12.058  6903-6973  LogStream               com.dspread.mdm.service              I  🔧 [S3LogUploader] 规范请求: PUT
                                                                                                    /log/dev/01354090202503050399/recent.gz
                                                                                                    
                                                                                                    host:smartms-applog.s3.sa-east-1.amazonaws.com
                                                                                                    x-amz-content-sha256:e23f5234624d64730de4afa596cf91d5dc2053cceb0327315ed640a0563b80a0
                                                                                                    x-amz-date:20250818T101811Z
                                                                                                    
                                                                                                    host;x-amz-content-sha256;x-amz-date
                                                                                                    e23f5234624d64730de4afa596cf91d5dc2053cceb0327315ed640a0563b80a0
2025-08-18 18:18:12.065  6903-6973  LogStream               com.dspread.mdm.service              I  🔧 [S3LogUploader] 待签名字符串: AWS4-HMAC-SHA256
                                                                                                    20250818T101811Z
                                                                                                    20250818/sa-east-1/s3/aws4_request
                                                                                                    23867632fdc66dcbaddf68a4e31d089eee04a8958a89f166339504f8aeab1e18
2025-08-18 18:18:12.073  6903-6973  LogStream               com.dspread.mdm.service              I  🔧 [S3LogUploader] S3 V4签名生成完成
2025-08-18 18:18:12.082  6903-6973  LogStream               com.dspread.mdm.service              I  🔧 [S3LogUploader] V4上传到URL: https://smartms-applog.s3.sa-east-1.amazonaws.com/log/dev/01354090202503050399/recent.gz
2025-08-18 18:18:12.532  6903-6973  TrafficStats            com.dspread.mdm.service              D  tagSocket(119) with statsTag=0xffffffff, statsUid=-1
2025-08-18 18:18:14.784  6903-6973  LogStream               com.dspread.mdm.service              I  🔧 [S3LogUploader] V4上传响应码: 200
2025-08-18 18:18:14.790  6903-6973  LogStream               com.dspread.mdm.service              I  🔧 [S3LogUploader] V4上传成功: recent.gz
2025-08-18 18:18:14.796  6903-6973  LogStream               com.dspread.mdm.service              I  🔧 [S3LogUploader] Recent文件上传成功
2025-08-18 18:18:14.803  6903-6932  LogStream               com.dspread.mdm.service              I  🔧 [LogStreamWebSocketHandler] Recent文件S3上传成功
2025-08-18 18:18:14.813  6903-6932  LogStream               com.dspread.mdm.service              I  🔧 [LogStreamWebSocketHandler] Recent日志上传成功
2025-08-18 18:18:14.823  6903-6932  LogStream               com.dspread.mdm.service              I  🔧 [LogStreamWebSocketHandler] 步骤2: 启动压缩包日志上传
2025-08-18 18:18:14.835  6903-6932  LogStream               com.dspread.mdm.service              I  🔧 [LogStreamWebSocketHandler] 开始启动压缩包日志上传
2025-08-18 18:18:14.847  6903-6932  LogStream               com.dspread.mdm.service              I  🔧 [LogStreamWebSocketHandler] 开始处理压缩日志上传
2025-08-18 18:18:14.848  6903-6973  LogStream               com.dspread.mdm.service              I  🔧 [LogStreamManager] 检查日志队列进行压缩，队列长度: 0
2025-08-18 18:18:14.869  6903-6932  LogStream               com.dspread.mdm.service              I  🔧 [LogStreamWebSocketHandler] 找到 0 个压缩文件待上传
2025-08-18 18:18:14.877  6903-6932  LogStream               com.dspread.mdm.service              I  🔧 [LogStreamWebSocketHandler] 压缩日志上传处理完成
2025-08-18 18:18:14.885  6903-6932  LogStream               com.dspread.mdm.service              I  🔧 [LogStreamWebSocketHandler] 压缩包日志上传启动完成
2025-08-18 18:18:14.891  6903-6932  LogStream               com.dspread.mdm.service              I  🔧 [LogStreamWebSocketHandler] 步骤3: 发送C0901应用信息响应
2025-08-18 18:18:14.896  6903-6932  LogStream               com.dspread.mdm.service              I  🔧 [LogStreamWebSocketHandler] 开始发送C0901日志流响应
2025-08-18 18:18:14.907  6903-6932  LogStream               com.dspread.mdm.service              I  🔧 [LogStreamWebSocketHandler] 日志流服务信息构建完成
2025-08-18 18:18:14.916  6903-6932  LogStream               com.dspread.mdm.service              I  🔧 [LogStreamWebSocketHandler] 构建服务信息完成: {"taskId":"1755512290518","serviceId":"856c2e6d2c7c4f60bc0fadcf9b9ad650","serviceName":"Log Stream","taskType":"05","command":"C03","stateDesc":"IMPLEMENTED","beginDate":"2025-08-18 18:18:14","endDate":"9999-12-31 23:59:59","period":"1","request_id":"","request_time":""}
2025-08-18 18:18:14.922  6903-6932  WebSocket               com.dspread.mdm.service              I  🔧 发送C0901应用信息响应（包含服务信息）
2025-08-18 18:18:14.928  6903-6932  WebSocket               com.dspread.mdm.service              I  🔧 服务信息已添加到管理器: 856c2e6d2c7c4f60bc0fadcf9b9ad650
2025-08-18 18:18:14.934  6903-6932  Platform                com.dspread.mdm.service              D  🔧 使用缓存的应用信息
2025-08-18 18:18:14.940  6903-6932  WebSocket               com.dspread.mdm.service              I  🔧 服务信息已添加到C0901响应中
2025-08-18 18:18:14.976  6903-6932  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0901","serialNo":"01354090202503050399","request_time":"1755512294946","request_id":"1755512294946C0901","version":"1","data":{"apkInfo":[{"packName":"com.dspread.mdm.service","apkName":"service","versionCode":20,"versionName":"1.1.01.20250818.DSPREAD.MDM.SERVICE","updateDate":"2025-08-18 18:17:11"},{"packName":"mark.via","apkName":"Via","versionCode":20250117,"versionName":"6.2.0","updateDate":"2025-08-18 17:50:20"}],"sytemInfo":{"androidVersion":"14","buildNumber":"BASE_D30M-MU_GEN_AP_V1.0.8.1_202507032000_ota_test","aspl":"2025-03-05"},"serviceInfo":[{"taskId":"1755512290518","serviceId":"856c2e6d2c7c4f60bc0fadcf9b9ad650","serviceName":"Log Stream","taskType":"05","command":"C03","stateDesc":"IMPLEMENTED","beginDate":"2025-08-18 18:18:14","endDate":"9999-12-31 23:59:59","period":"1","request_id":"","request_time":""}]},"myVersionName":"1.1.01.20250818.DSPREAD.MDM.SERVICE","terminalDate":"20250818181814"}
2025-08-18 18:18:14.982  6903-6932  WebSocket               com.dspread.mdm.service              I  🔧 C0901 应用信息响应（含服务信息）发送成功
2025-08-18 18:18:14.988  6903-6932  LogStream               com.dspread.mdm.service              I  🔧 [LogStreamWebSocketHandler] C0901日志流响应发送完成
2025-08-18 18:18:14.994  6903-6932  LogStream               com.dspread.mdm.service              I  🔧 [LogStreamWebSocketHandler] ========== 日志流上传处理完成 ==========