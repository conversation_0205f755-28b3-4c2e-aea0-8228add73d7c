package com.dspread.mdm.service.network.websocket.message.processor

import android.content.Context
import com.dspread.mdm.service.utils.log.Logger
import com.dspread.mdm.service.network.websocket.connection.WsConnectionManager
import com.dspread.mdm.service.network.websocket.message.WsMessageSender
import com.dspread.mdm.service.network.websocket.connection.WsKeyManager
import com.dspread.mdm.service.network.websocket.connection.WsManager
import com.dspread.mdm.service.platform.monitor.NetworkTrafficMonitor
import com.dspread.mdm.service.utils.zip.ZipJava
import com.dspread.mdm.service.utils.crypto.EncrypUtil
import org.json.JSONObject
import java.util.zip.Inflater
import java.util.zip.ZipInputStream
import java.io.ByteArrayOutputStream
import java.io.ByteArrayInputStream

/**
 * WebSocket 消息预处理器
 * 专门负责消息的压缩、解压缩、加密、解密等预处理
 */
class WsMessageProcessor(private val context: Context) {

    private var messageListener: ((String) -> Unit)? = null

    /**
     * 设置消息监听器
     */
    fun setMessageListener(listener: (String) -> Unit) {
        this.messageListener = listener
    }

    /**
     * 处理接收到的消息响应
     */
    fun onMessageResponse(message: String) {
        try {
            // 统计WebSocket下载流量（接收到的原始压缩数据）
            val receivedBytes = message.toByteArray(Charsets.UTF_8).size.toLong()
            NetworkTrafficMonitor.recordWebSocketDownload(receivedBytes)

            // 调试：记录收到的原始消息类型
//            Logger.wsm("收到消息类型检查 - 长度: ${message.length}, 前50字符: ${message.take(50)}")

            // 不要在原始消息中检查hello，因为hellMsg可能在加密的JSON中
            // 直接解密和解压缩消息
            val decryptedMessage = decryptAndDecompressMessage(message)

            // 处理业务消息并发送响应确认
            handleBusinessMessage(decryptedMessage)

            // 通知监听器
            messageListener?.invoke(decryptedMessage)

        } catch (e: Exception) {
            Logger.wsmE("处理消息响应失败", e)
        }
    }

    /**
     * 处理业务消息并发送响应确认
     */
    private fun handleBusinessMessage(message: String) {
        try {
            val jsonObject = JSONObject(message)

            if (jsonObject.has("tranCode")) {
                val tranCode = jsonObject.getString("tranCode")

                // 调试：记录收到的消息类型
//                Logger.wsm("收到业务消息 - tranCode: $tranCode")

                // 对于大部分消息都需要发送 C0000 响应确认
                // 除了 S0000 系列和特定的 SC004 心跳消息
                val needResponse = !tranCode.startsWith("S0000") &&
                    !(tranCode.startsWith("SC004") &&
                      jsonObject.optJSONObject("data")?.optString("c_type") == "CALLHB")

                if (needResponse) {
                    val requestId = jsonObject.optString("request_id", "")
                    val requestTime = jsonObject.optString("request_time", "")

                    // 记录响应发送尝试
                    Logger.wsm("准备发送响应确认 - requestId: $requestId, needResponse: $needResponse")

                    if (requestId.isNotEmpty() && requestTime.isNotEmpty()) {
                        // 发送响应确认
                        WsMessageSender.sendWebSocketResponse(requestId, requestTime, "0", null)
                    }
                }

                // 处理 hello 消息的特殊逻辑
                if (jsonObject.has("hellMsg")) {
                    Logger.success("收到服务器 hellMsg，WebSocket 业务连接成功！")
                    // 设置业务连接状态为成功
                    WsManager.setWebSocketConnected(true)
                } else {
                    // 调试：记录解密后的消息内容
//                    Logger.wsm("解密后消息内容: ${message.take(200)}")
                }
            }

        } catch (e: Exception) {
            Logger.wsmE("处理业务消息失败", e)
        }
    }

    /**
     * 处理 Hello 消息
     */
    private fun handleHelloMessage(message: String) {
        try {
            Logger.success("收到服务器 Hello 消息")

            // 逻辑，收到 hello 后应该发送 C0109 设备信息
            WsMessageSender.uploadDeviceInfo()

        } catch (e: Exception) {
            Logger.wsmE("处理 Hello 消息失败", e)
        }
    }

    /**
     * 发送文本消息
     */
    fun sendText(text: String, needCompress: Boolean = true) {
        try {
            val finalMessage = if (needCompress) {
                // 压缩消息
                compressAndEncodeMessage(text)
            } else {
                // 直接发送（如心跳消息）
                text
            }

            // 实际发送
            WsConnectionManager.sendMessage(finalMessage)

            // 输出发送成功的消息内容
            Logger.success("发送消息成功: $text")
            
        } catch (e: Exception) {
            Logger.wsmE("发送消息失败", e)
        }
    }

    /**
     * 解密和解压缩消息
     */
    private fun decryptAndDecompressMessage(encryptedMessage: String): String {
        return try {
            // 检查是否有签名分隔符
            if (encryptedMessage.contains("&signature=")) {
                val parts = encryptedMessage.split("&signature=")

                if (parts.size >= 2) {
                    val messageData = parts[0]
                    val signature = parts[1]

                    // 验证签名（使用真正的 RSA 验签）
                    val dataBytes = messageData.toByteArray(Charsets.ISO_8859_1)
                    if (WsKeyManager.verifyData(dataBytes, signature)) {
                        // 解压缩并解密消息数据
                        decompressAndDecryptMessage(messageData)
                    } else {
                        // 验签失败时仍然尝试解压缩和解密（兼容性考虑）
                        decompressAndDecryptMessage(messageData)
                    }
                } else {
                    encryptedMessage
                }
            } else {
                // 没有签名，逻辑判断是否需要解密
                // 实际逻辑：始终执行解压缩和解密
                decompressAndDecryptMessage(encryptedMessage)
            }

        } catch (e: Exception) {
            Logger.wsmE("解密解压消息失败", e)
            encryptedMessage // 失败时返回原消息
        }
    }

    /**
     * 判断是否需要 RSA 解密
     */
    private fun needRsaDecryption(): Boolean {
        // 所以当前服务器配置不需要 RSA 解密，只需要 ZIP 解压缩
        return false
    }

    /**
     * 解压缩并解密消息（完整流程）
     */
    private fun decompressAndDecryptMessage(compressedData: String): String {
        return try {
            // 第一步：解压缩（ ZipJava）
            val decompressedData = decompressWithZipJava(compressedData)

            // 第二步：RSA 解密（ EncrypUtil）
            val decryptedMessage = decryptWithEncrypUtil(decompressedData)

            // 输出接收到的消息内容
            Logger.success("接收消息内容: $decryptedMessage")

            decryptedMessage

        } catch (e: Exception) {
            Logger.wsmE("解压缩和解密失败", e)
            compressedData // 失败时返回原数据
        }
    }

    /**
     * ZipJava 解压缩消息
     */
    private fun decompressWithZipJava(compressedData: String): ByteArray {
        // 从 ISO-8859-1 字符串转换为字节数组
        val compressedBytes = compressedData.toByteArray(Charsets.ISO_8859_1)

        //  ZipJava.decompress
        return ZipJava.decompress(compressedBytes) ?: byteArrayOf()
    }

    /**
     * EncrypUtil 解密消息
     */
    private fun decryptWithEncrypUtil(encryptedData: ByteArray): String {


        // 获取客户端私钥
        val privateKey = WsKeyManager.getClientPrivateKeyString()

        //  EncrypUtil.decryptByPrivateKey
        val decryptedBytes = EncrypUtil.decryptByPrivateKey(encryptedData, privateKey)

        // 转换为 UTF-8 字符串
        return String(decryptedBytes, Charsets.UTF_8)
    }

    /**
     * RSA 解密消息
     */
    private fun decryptMessage(encryptedData: String): String {
        return try {
            // 将字符串转换为字节数组
            val encryptedBytes = encryptedData.toByteArray(Charsets.ISO_8859_1)

            // 使用客户端私钥解密
            val decryptedBytes = WsKeyManager.decryptWithClientPrivateKey(encryptedBytes)

            // 转换为 UTF-8 字符串
            String(decryptedBytes, Charsets.UTF_8)

        } catch (e: Exception) {
            Logger.wsmE("RSA 解密失败", e)
            encryptedData // 失败时返回原数据
        }
    }

    /**
     * 解压缩消息
     */
    private fun decompressMessage(compressedData: String): String {
        return try {
            // 从 ISO-8859-1 字符串转换为字节数组
            val compressedBytes = compressedData.toByteArray(Charsets.ISO_8859_1)

            // 检查是否为 ZIP 格式（PK 开头）
            if (compressedBytes.size >= 2 && compressedBytes[0] == 0x50.toByte() && compressedBytes[1] == 0x4B.toByte()) {
                decompressZipData(compressedBytes)
            } else {
                decompressInflaterData(compressedBytes)
            }

        } catch (e: Exception) {
            Logger.wsmE("解压缩失败", e)
            compressedData // 失败时返回压缩数据
        }
    }

    /**
     * 使用 ZipInputStream 解压缩
     */
    private fun decompressZipData(compressedBytes: ByteArray): String {
        var zipInputStream: ZipInputStream? = null
        var byteArrayInputStream: ByteArrayInputStream? = null
        var outputStream: ByteArrayOutputStream? = null

        return try {
            outputStream = ByteArrayOutputStream()
            byteArrayInputStream = ByteArrayInputStream(compressedBytes)
            zipInputStream = ZipInputStream(byteArrayInputStream)

            // 获取第一个条目
            zipInputStream.nextEntry

            val buffer = ByteArray(1024)
            var offset: Int
            while (zipInputStream.read(buffer).also { offset = it } != -1) {
                outputStream.write(buffer, 0, offset)
            }

            val decompressedBytes = outputStream.toByteArray()
            val decompressedMessage = String(decompressedBytes, Charsets.UTF_8)

            decompressedMessage

        } finally {
            zipInputStream?.close()
            byteArrayInputStream?.close()
            outputStream?.close()
        }
    }

    /**
     * 使用 Inflater 解压缩（ZLIB 格式）
     */
    private fun decompressInflaterData(compressedBytes: ByteArray): String {
        val inflater = Inflater()
        return try {
            inflater.setInput(compressedBytes)

            val buffer = ByteArray(1024)
            val outputStream = ByteArrayOutputStream()

            while (!inflater.finished()) {
                val count = inflater.inflate(buffer)
                outputStream.write(buffer, 0, count)
            }

            outputStream.toString("UTF-8")

        } finally {
            inflater.end()
        }
    }

    /**
     * 压缩并编码消息
     */
    private fun compressAndEncodeMessage(message: String): String {
        return try {
            Logger.wsm("压缩消息，原始长度: ${message.length}")

            // 使用 Deflater 压缩
            val deflater = java.util.zip.Deflater()
            deflater.setInput(message.toByteArray(Charsets.UTF_8))
            deflater.finish()

            val buffer = ByteArray(1024)
            val outputStream = ByteArrayOutputStream()

            while (!deflater.finished()) {
                val count = deflater.deflate(buffer)
                outputStream.write(buffer, 0, count)
            }

            deflater.end()
            val compressedBytes = outputStream.toByteArray()

            // 转换为 ISO-8859-1 字符串
            val compressedMessage = String(compressedBytes, Charsets.ISO_8859_1)

            Logger.wsm("压缩完成，压缩后长度: ${compressedMessage.length}")
            compressedMessage

        } catch (e: Exception) {
            Logger.wsmE("压缩消息失败", e)
            message // 失败时返回原消息
        }
    }

    /**
     * 释放资源
     */
    fun release() {
        messageListener = null
        Logger.wsm("WsMessageHandler 资源已释放")
    }
}
