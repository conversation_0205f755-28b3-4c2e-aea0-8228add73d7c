package com.bbpos.wiseapp.logger;

import com.bbpos.wiseapp.tms.utils.IOUtils;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.FileWriter;
import java.io.IOException;
import java.io.ObjectInputStream;
import java.io.ObjectOutputStream;
import java.util.LinkedList;

public class LLQueue<E> {
    private LinkedList<E> list = new LinkedList<>();
    String flag = "sync";

    public void saveToFile(String path) throws IOException {
        if (list.size() == 0) {
            File file = new File(path);
            FileWriter fileWriter = null;
            try {
                if(!file.exists()) {
                    file.createNewFile();
                }
                fileWriter =new FileWriter(file);
                fileWriter.write("");
            } catch (Exception e) {
                e.printStackTrace();
            } finally {
                try {
                    if (fileWriter != null) {
                        fileWriter.flush();
                        fileWriter.close();
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
            return;
        }

        FileOutputStream fileOut = null;
        ObjectOutputStream oos = null;
        try {
            File file = new File(path);
            if (!file.exists()) {
                file.createNewFile();
            }
            fileOut = new FileOutputStream(path);
            oos = new ObjectOutputStream(fileOut);
            oos.writeObject(list);
        } catch (Exception e){
            e.printStackTrace();
        } finally {
            IOUtils.flushCloseOutputStream(fileOut);
            IOUtils.flushCloseOutputStream(oos);
		}
    }

    public void readFromFile(String path) throws IOException {
        File file = new File(path);
        if (!file.exists() || file.length()==0) {
            return;
        }

        FileInputStream fileIn = null;
        ObjectInputStream ois = null;
        try {
            //将对象从stu.txt文件中读出。
            fileIn=new FileInputStream(path);
            ois=new ObjectInputStream(fileIn);
            list = (LinkedList<E>)ois.readObject();
        } catch (FileNotFoundException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        } catch (ClassNotFoundException e) {
            e.printStackTrace();
        } catch (Exception e){
            e.printStackTrace();
        } finally {
            IOUtils.closeInputStream(fileIn);
            IOUtils.closeInputStream(ois);
        }
    }

    /**
     *清空队列
     */
    public void clear() {
        list.clear();
    }

    /**
     * 判断是否为空
     * @return
     */
    public boolean isQueueEmpty() {
        return list.isEmpty();
    }

    /**
     * 进队
     * @param e
     */
    public void enQueue(E e) {
        synchronized (flag) {
            list.addLast(e);
        }
    }

    /**
     * 出队
     * @return
     */
    public E deQueue() {
        synchronized (flag) {
            if (!list.isEmpty()) {
                return list.removeFirst();
            } else {
                return null;
            }
        }
    }

    /**
     * 出队
     * @return
     */
    public boolean removeOne(E e) {
        synchronized (flag) {
            if (!list.isEmpty()) {
                return list.remove(e);
            } else {
                return false;
            }
        }
    }

    /**
     * 获取队列长度
     * @return
     */
    public int queueLength() {
        return list.size();
    }

    /**
     * 查看队首元素
     * @return
     */
    public Object getQueuePeek() {
        return list.getFirst();
    }

    /**
     * 查看队首元素
     * @return
     */
    public Object getQueueLast() {
        return list.getLast();
    }

    public boolean isInQueue(E e) {
        return list.contains(e);
    }
}
