2025-08-21 21:09:50.745   947-5778  AppOps                  system_server                        E  checkOperation (Ask Gemini)
                                                                                                    java.lang.SecurityException: Specified package com.dspread.mdm.service under uid 1000 but it is really -1
                                                                                                         at com.android.server.appop.AppOpsService.verifyAndGetBypass(AppOpsService.java:3966)
                                                                                                         at com.android.server.appop.AppOpsService.checkOperationUnchecked(AppOpsService.java:2941)
                                                                                                         at com.android.server.appop.AppOpsService.checkOperationImpl(AppOpsService.java:2924)
                                                                                                         at com.android.server.appop.AppOpsService.checkOperationInternal(AppOpsService.java:2911)
                                                                                                         at com.android.server.appop.AppOpsService.checkOperation(AppOpsService.java:2902)
                                                                                                         at com.android.server.appop.AppOpsService.checkAudioOperationImpl(AppOpsService.java:2988)
                                                                                                         at com.android.server.appop.AppOpsService.checkAudioOperation(AppOpsService.java:2976)
                                                                                                         at com.android.internal.app.IAppOpsService$Stub.onTransact(IAppOpsService.java:538)
                                                                                                         at android.os.Binder.execTransactInternal(Binder.java:1159)
                                                                                                         at android.os.Binder.execTransact(Binder.java:1123)
2025-08-21 21:09:50.750   947-3995  AppOps                  system_server                        E  checkOperation (Ask Gemini)
                                                                                                    java.lang.SecurityException: Specified package com.dspread.mdm.service under uid 1000 but it is really -1
                                                                                                         at com.android.server.appop.AppOpsService.verifyAndGetBypass(AppOpsService.java:3966)
                                                                                                         at com.android.server.appop.AppOpsService.checkOperationUnchecked(AppOpsService.java:2941)
                                                                                                         at com.android.server.appop.AppOpsService.checkOperationImpl(AppOpsService.java:2924)
                                                                                                         at com.android.server.appop.AppOpsService.checkOperationInternal(AppOpsService.java:2911)
                                                                                                         at com.android.server.appop.AppOpsService.checkOperation(AppOpsService.java:2902)
                                                                                                         at com.android.server.appop.AppOpsService.checkAudioOperationImpl(AppOpsService.java:2988)
                                                                                                         at com.android.server.appop.AppOpsService.checkAudioOperation(AppOpsService.java:2976)
                                                                                                         at com.android.internal.app.IAppOpsService$Stub.onTransact(IAppOpsService.java:538)
                                                                                                         at android.os.Binder.execTransactInternal(Binder.java:1159)
                                                                                                         at android.os.Binder.execTransact(Binder.java:1123)
2025-08-21 21:09:50.753   947-5143  AppOps                  system_server                        E  checkOperation (Ask Gemini)
                                                                                                    java.lang.SecurityException: Specified package com.dspread.mdm.service under uid 1000 but it is really -1
                                                                                                         at com.android.server.appop.AppOpsService.verifyAndGetBypass(AppOpsService.java:3966)
                                                                                                         at com.android.server.appop.AppOpsService.checkOperationUnchecked(AppOpsService.java:2941)
                                                                                                         at com.android.server.appop.AppOpsService.checkOperationImpl(AppOpsService.java:2924)
                                                                                                         at com.android.server.appop.AppOpsService.checkOperationInternal(AppOpsService.java:2911)
                                                                                                         at com.android.server.appop.AppOpsService.checkOperation(AppOpsService.java:2902)
                                                                                                         at com.android.server.appop.AppOpsService.checkAudioOperationImpl(AppOpsService.java:2988)
                                                                                                         at com.android.server.appop.AppOpsService.checkAudioOperation(AppOpsService.java:2976)
                                                                                                         at com.android.internal.app.IAppOpsService$Stub.onTransact(IAppOpsService.java:538)
                                                                                                         at android.os.Binder.execTransactInternal(Binder.java:1159)
                                                                                                         at android.os.Binder.execTransact(Binder.java:1123)
2025-08-21 21:09:50.758   947-3995  AppOps                  system_server                        E  checkOperation (Ask Gemini)
                                                                                                    java.lang.SecurityException: Specified package com.dspread.mdm.service under uid 1000 but it is really -1
                                                                                                         at com.android.server.appop.AppOpsService.verifyAndGetBypass(AppOpsService.java:3966)
                                                                                                         at com.android.server.appop.AppOpsService.checkOperationUnchecked(AppOpsService.java:2941)
                                                                                                         at com.android.server.appop.AppOpsService.checkOperationImpl(AppOpsService.java:2924)
                                                                                                         at com.android.server.appop.AppOpsService.checkOperationInternal(AppOpsService.java:2911)
                                                                                                         at com.android.server.appop.AppOpsService.checkOperation(AppOpsService.java:2902)
                                                                                                         at com.android.server.appop.AppOpsService.checkAudioOperationImpl(AppOpsService.java:2988)
                                                                                                         at com.android.server.appop.AppOpsService.checkAudioOperation(AppOpsService.java:2976)
                                                                                                         at com.android.internal.app.IAppOpsService$Stub.onTransact(IAppOpsService.java:538)
                                                                                                         at android.os.Binder.execTransactInternal(Binder.java:1159)
                                                                                                         at android.os.Binder.execTransact(Binder.java:1123)
2025-08-21 21:09:50.761   947-5936  AppOps                  system_server                        E  checkOperation (Ask Gemini)
                                                                                                    java.lang.SecurityException: Specified package com.dspread.mdm.service under uid 1000 but it is really -1
                                                                                                         at com.android.server.appop.AppOpsService.verifyAndGetBypass(AppOpsService.java:3966)
                                                                                                         at com.android.server.appop.AppOpsService.checkOperationUnchecked(AppOpsService.java:2941)
                                                                                                         at com.android.server.appop.AppOpsService.checkOperationImpl(AppOpsService.java:2924)
                                                                                                         at com.android.server.appop.AppOpsService.checkOperationInternal(AppOpsService.java:2911)
                                                                                                         at com.android.server.appop.AppOpsService.checkOperation(AppOpsService.java:2902)
                                                                                                         at com.android.server.appop.AppOpsService.checkAudioOperationImpl(AppOpsService.java:2988)
                                                                                                         at com.android.server.appop.AppOpsService.checkAudioOperation(AppOpsService.java:2976)
                                                                                                         at com.android.internal.app.IAppOpsService$Stub.onTransact(IAppOpsService.java:538)
                                                                                                         at android.os.Binder.execTransactInternal(Binder.java:1159)
                                                                                                         at android.os.Binder.execTransact(Binder.java:1123)
2025-08-21 21:09:50.769   947-5936  AppOps                  system_server                        E  checkOperation (Ask Gemini)
                                                                                                    java.lang.SecurityException: Specified package com.dspread.mdm.service under uid 1000 but it is really -1
                                                                                                         at com.android.server.appop.AppOpsService.verifyAndGetBypass(AppOpsService.java:3966)
                                                                                                         at com.android.server.appop.AppOpsService.checkOperationUnchecked(AppOpsService.java:2941)
                                                                                                         at com.android.server.appop.AppOpsService.checkOperationImpl(AppOpsService.java:2924)
                                                                                                         at com.android.server.appop.AppOpsService.checkOperationInternal(AppOpsService.java:2911)
                                                                                                         at com.android.server.appop.AppOpsService.checkOperation(AppOpsService.java:2902)
                                                                                                         at com.android.server.appop.AppOpsService.checkAudioOperationImpl(AppOpsService.java:2988)
                                                                                                         at com.android.server.appop.AppOpsService.checkAudioOperation(AppOpsService.java:2976)
                                                                                                         at com.android.internal.app.IAppOpsService$Stub.onTransact(IAppOpsService.java:538)
                                                                                                         at android.os.Binder.execTransactInternal(Binder.java:1159)
                                                                                                         at android.os.Binder.execTransact(Binder.java:1123)
2025-08-21 21:09:50.773   947-4292  AppOps                  system_server                        E  checkOperation (Ask Gemini)
                                                                                                    java.lang.SecurityException: Specified package com.dspread.mdm.service under uid 1000 but it is really -1
                                                                                                         at com.android.server.appop.AppOpsService.verifyAndGetBypass(AppOpsService.java:3966)
                                                                                                         at com.android.server.appop.AppOpsService.checkOperationUnchecked(AppOpsService.java:2941)
                                                                                                         at com.android.server.appop.AppOpsService.checkOperationImpl(AppOpsService.java:2924)
                                                                                                         at com.android.server.appop.AppOpsService.checkOperationInternal(AppOpsService.java:2911)
                                                                                                         at com.android.server.appop.AppOpsService.checkOperation(AppOpsService.java:2902)
                                                                                                         at com.android.server.appop.AppOpsService.checkAudioOperationImpl(AppOpsService.java:2988)
                                                                                                         at com.android.server.appop.AppOpsService.checkAudioOperation(AppOpsService.java:2976)
                                                                                                         at com.android.internal.app.IAppOpsService$Stub.onTransact(IAppOpsService.java:538)
                                                                                                         at android.os.Binder.execTransactInternal(Binder.java:1159)
                                                                                                         at android.os.Binder.execTransact(Binder.java:1123)
2025-08-21 21:09:50.795   947-4292  AppOps                  system_server                        E  checkOperation (Ask Gemini)
                                                                                                    java.lang.SecurityException: Specified package com.dspread.mdm.service under uid 1000 but it is really -1
                                                                                                         at com.android.server.appop.AppOpsService.verifyAndGetBypass(AppOpsService.java:3966)
                                                                                                         at com.android.server.appop.AppOpsService.checkOperationUnchecked(AppOpsService.java:2941)
                                                                                                         at com.android.server.appop.AppOpsService.checkOperationImpl(AppOpsService.java:2924)
                                                                                                         at com.android.server.appop.AppOpsService.checkOperationInternal(AppOpsService.java:2911)
                                                                                                         at com.android.server.appop.AppOpsService.checkOperation(AppOpsService.java:2902)
                                                                                                         at com.android.server.appop.AppOpsService.checkAudioOperationImpl(AppOpsService.java:2988)
                                                                                                         at com.android.server.appop.AppOpsService.checkAudioOperation(AppOpsService.java:2976)
                                                                                                         at com.android.internal.app.IAppOpsService$Stub.onTransact(IAppOpsService.java:538)
                                                                                                         at android.os.Binder.execTransactInternal(Binder.java:1159)
                                                                                                         at android.os.Binder.execTransact(Binder.java:1123)
2025-08-21 21:09:50.799   947-3995  AppOps                  system_server                        E  checkOperation (Ask Gemini)
                                                                                                    java.lang.SecurityException: Specified package com.dspread.mdm.service under uid 1000 but it is really -1
                                                                                                         at com.android.server.appop.AppOpsService.verifyAndGetBypass(AppOpsService.java:3966)
                                                                                                         at com.android.server.appop.AppOpsService.checkOperationUnchecked(AppOpsService.java:2941)
                                                                                                         at com.android.server.appop.AppOpsService.checkOperationImpl(AppOpsService.java:2924)
                                                                                                         at com.android.server.appop.AppOpsService.checkOperationInternal(AppOpsService.java:2911)
                                                                                                         at com.android.server.appop.AppOpsService.checkOperation(AppOpsService.java:2902)
                                                                                                         at com.android.server.appop.AppOpsService.checkAudioOperationImpl(AppOpsService.java:2988)
                                                                                                         at com.android.server.appop.AppOpsService.checkAudioOperation(AppOpsService.java:2976)
                                                                                                         at com.android.internal.app.IAppOpsService$Stub.onTransact(IAppOpsService.java:538)
                                                                                                         at android.os.Binder.execTransactInternal(Binder.java:1159)
                                                                                                         at android.os.Binder.execTransact(Binder.java:1123)
2025-08-21 21:09:50.803   947-3995  AppOps                  system_server                        E  checkOperation (Ask Gemini)
                                                                                                    java.lang.SecurityException: Specified package com.dspread.mdm.service under uid 1000 but it is really -1
                                                                                                         at com.android.server.appop.AppOpsService.verifyAndGetBypass(AppOpsService.java:3966)
                                                                                                         at com.android.server.appop.AppOpsService.checkOperationUnchecked(AppOpsService.java:2941)
                                                                                                         at com.android.server.appop.AppOpsService.checkOperationImpl(AppOpsService.java:2924)
                                                                                                         at com.android.server.appop.AppOpsService.checkOperationInternal(AppOpsService.java:2911)
                                                                                                         at com.android.server.appop.AppOpsService.checkOperation(AppOpsService.java:2902)
                                                                                                         at com.android.server.appop.AppOpsService.checkAudioOperationImpl(AppOpsService.java:2988)
                                                                                                         at com.android.server.appop.AppOpsService.checkAudioOperation(AppOpsService.java:2976)
                                                                                                         at com.android.internal.app.IAppOpsService$Stub.onTransact(IAppOpsService.java:538)
                                                                                                         at android.os.Binder.execTransactInternal(Binder.java:1159)
                                                                                                         at android.os.Binder.execTransact(Binder.java:1123)
2025-08-21 21:09:50.806   947-4292  AppOps                  system_server                        E  checkOperation (Ask Gemini)
                                                                                                    java.lang.SecurityException: Specified package com.dspread.mdm.service under uid 1000 but it is really -1
                                                                                                         at com.android.server.appop.AppOpsService.verifyAndGetBypass(AppOpsService.java:3966)
                                                                                                         at com.android.server.appop.AppOpsService.checkOperationUnchecked(AppOpsService.java:2941)
                                                                                                         at com.android.server.appop.AppOpsService.checkOperationImpl(AppOpsService.java:2924)
                                                                                                         at com.android.server.appop.AppOpsService.checkOperationInternal(AppOpsService.java:2911)
                                                                                                         at com.android.server.appop.AppOpsService.checkOperation(AppOpsService.java:2902)
                                                                                                         at com.android.server.appop.AppOpsService.checkAudioOperationImpl(AppOpsService.java:2988)
                                                                                                         at com.android.server.appop.AppOpsService.checkAudioOperation(AppOpsService.java:2976)
                                                                                                         at com.android.internal.app.IAppOpsService$Stub.onTransact(IAppOpsService.java:538)
                                                                                                         at android.os.Binder.execTransactInternal(Binder.java:1159)
                                                                                                         at android.os.Binder.execTransact(Binder.java:1123)
2025-08-21 21:09:50.810   947-2851  AppOps                  system_server                        E  checkOperation (Ask Gemini)
                                                                                                    java.lang.SecurityException: Specified package com.dspread.mdm.service under uid 1000 but it is really -1
                                                                                                         at com.android.server.appop.AppOpsService.verifyAndGetBypass(AppOpsService.java:3966)
                                                                                                         at com.android.server.appop.AppOpsService.checkOperationUnchecked(AppOpsService.java:2941)
                                                                                                         at com.android.server.appop.AppOpsService.checkOperationImpl(AppOpsService.java:2924)
                                                                                                         at com.android.server.appop.AppOpsService.checkOperationInternal(AppOpsService.java:2911)
                                                                                                         at com.android.server.appop.AppOpsService.checkOperation(AppOpsService.java:2902)
                                                                                                         at com.android.server.appop.AppOpsService.checkAudioOperationImpl(AppOpsService.java:2988)
                                                                                                         at com.android.server.appop.AppOpsService.checkAudioOperation(AppOpsService.java:2976)
                                                                                                         at com.android.internal.app.IAppOpsService$Stub.onTransact(IAppOpsService.java:538)
                                                                                                         at android.os.Binder.execTransactInternal(Binder.java:1159)
                                                                                                         at android.os.Binder.execTransact(Binder.java:1123)
2025-08-21 21:09:50.813   947-2851  AppOps                  system_server                        E  checkOperation (Ask Gemini)
                                                                                                    java.lang.SecurityException: Specified package com.dspread.mdm.service under uid 1000 but it is really -1
                                                                                                         at com.android.server.appop.AppOpsService.verifyAndGetBypass(AppOpsService.java:3966)
                                                                                                         at com.android.server.appop.AppOpsService.checkOperationUnchecked(AppOpsService.java:2941)
                                                                                                         at com.android.server.appop.AppOpsService.checkOperationImpl(AppOpsService.java:2924)
                                                                                                         at com.android.server.appop.AppOpsService.checkOperationInternal(AppOpsService.java:2911)
                                                                                                         at com.android.server.appop.AppOpsService.checkOperation(AppOpsService.java:2902)
                                                                                                         at com.android.server.appop.AppOpsService.checkAudioOperationImpl(AppOpsService.java:2988)
                                                                                                         at com.android.server.appop.AppOpsService.checkAudioOperation(AppOpsService.java:2976)
                                                                                                         at com.android.internal.app.IAppOpsService$Stub.onTransact(IAppOpsService.java:538)
                                                                                                         at android.os.Binder.execTransactInternal(Binder.java:1159)
                                                                                                         at android.os.Binder.execTransact(Binder.java:1123)
2025-08-21 21:09:50.816   947-5778  AppOps                  system_server                        E  checkOperation (Ask Gemini)
                                                                                                    java.lang.SecurityException: Specified package com.dspread.mdm.service under uid 1000 but it is really -1
                                                                                                         at com.android.server.appop.AppOpsService.verifyAndGetBypass(AppOpsService.java:3966)
                                                                                                         at com.android.server.appop.AppOpsService.checkOperationUnchecked(AppOpsService.java:2941)
                                                                                                         at com.android.server.appop.AppOpsService.checkOperationImpl(AppOpsService.java:2924)
                                                                                                         at com.android.server.appop.AppOpsService.checkOperationInternal(AppOpsService.java:2911)
                                                                                                         at com.android.server.appop.AppOpsService.checkOperation(AppOpsService.java:2902)
                                                                                                         at com.android.server.appop.AppOpsService.checkAudioOperationImpl(AppOpsService.java:2988)
                                                                                                         at com.android.server.appop.AppOpsService.checkAudioOperation(AppOpsService.java:2976)
                                                                                                         at com.android.internal.app.IAppOpsService$Stub.onTransact(IAppOpsService.java:538)
                                                                                                         at android.os.Binder.execTransactInternal(Binder.java:1159)
                                                                                                         at android.os.Binder.execTransact(Binder.java:1123)
2025-08-21 21:09:50.820   947-5143  AppOps                  system_server                        E  checkOperation (Ask Gemini)
                                                                                                    java.lang.SecurityException: Specified package com.dspread.mdm.service under uid 1000 but it is really -1
                                                                                                         at com.android.server.appop.AppOpsService.verifyAndGetBypass(AppOpsService.java:3966)
                                                                                                         at com.android.server.appop.AppOpsService.checkOperationUnchecked(AppOpsService.java:2941)
                                                                                                         at com.android.server.appop.AppOpsService.checkOperationImpl(AppOpsService.java:2924)
                                                                                                         at com.android.server.appop.AppOpsService.checkOperationInternal(AppOpsService.java:2911)
                                                                                                         at com.android.server.appop.AppOpsService.checkOperation(AppOpsService.java:2902)
                                                                                                         at com.android.server.appop.AppOpsService.checkAudioOperationImpl(AppOpsService.java:2988)
                                                                                                         at com.android.server.appop.AppOpsService.checkAudioOperation(AppOpsService.java:2976)
                                                                                                         at com.android.internal.app.IAppOpsService$Stub.onTransact(IAppOpsService.java:538)
                                                                                                         at android.os.Binder.execTransactInternal(Binder.java:1159)
                                                                                                         at android.os.Binder.execTransact(Binder.java:1123)
2025-08-21 21:09:50.823   947-5778  AppOps                  system_server                        E  checkOperation (Ask Gemini)
                                                                                                    java.lang.SecurityException: Specified package com.dspread.mdm.service under uid 1000 but it is really -1
                                                                                                         at com.android.server.appop.AppOpsService.verifyAndGetBypass(AppOpsService.java:3966)
                                                                                                         at com.android.server.appop.AppOpsService.checkOperationUnchecked(AppOpsService.java:2941)
                                                                                                         at com.android.server.appop.AppOpsService.checkOperationImpl(AppOpsService.java:2924)
                                                                                                         at com.android.server.appop.AppOpsService.checkOperationInternal(AppOpsService.java:2911)
                                                                                                         at com.android.server.appop.AppOpsService.checkOperation(AppOpsService.java:2902)
                                                                                                         at com.android.server.appop.AppOpsService.checkAudioOperationImpl(AppOpsService.java:2988)
                                                                                                         at com.android.server.appop.AppOpsService.checkAudioOperation(AppOpsService.java:2976)
                                                                                                         at com.android.internal.app.IAppOpsService$Stub.onTransact(IAppOpsService.java:538)
                                                                                                         at android.os.Binder.execTransactInternal(Binder.java:1159)
                                                                                                         at android.os.Binder.execTransact(Binder.java:1123)
2025-08-21 21:09:56.167   947-998   PackageManager          system_server                        E  Adding duplicate shared id: 1000 name=com.dspread.mdm.service
2025-08-21 21:09:57.638  5998-5998  NetworkSecurityConfig   com.dspread.mdm.service              D  Using Network Security Config from resource network_security_config debugBuild: true
2025-08-21 21:09:57.642  5998-5998  NetworkSecurityConfig   com.dspread.mdm.service              D  Using Network Security Config from resource network_security_config debugBuild: true
2025-08-21 21:09:57.732  5998-5998  Common                  com.dspread.mdm.service              D  🔧 日志配置：测试环境模式
2025-08-21 21:09:57.737  5998-5998  Common                  com.dspread.mdm.service              D  🔧 [DebugConfig] 调试模式已启用
2025-08-21 21:09:57.740  5998-5998  Common                  com.dspread.mdm.service              D  🔧 SmartMdmServiceApp: 统一配置管理器初始化完成
2025-08-21 21:09:57.744  5998-5998  Common                  com.dspread.mdm.service              D  🔧 SmartMdmServiceApp: 系统目录初始化完成 - /sdcard/Android/data/com.dspread.mdm.service/files/config/
2025-08-21 21:09:57.785  5998-5998  Receiver                com.dspread.mdm.service              D  🔧 ServiceGuardEventHandler 启动服务守护定时器: 120s
2025-08-21 21:09:57.791  5998-5998  Common                  com.dspread.mdm.service              I  ✅ ⏰ 设置服务守护定时器成功，下次执行: 120秒后 (2分钟)
2025-08-21 21:09:57.793  5998-5998  Common                  com.dspread.mdm.service              D  🔧 SmartMdmServiceApp: 服务守护定时器启动成功
2025-08-21 21:09:57.799  5998-5998  Common                  com.dspread.mdm.service              D  🔧 ServiceStartupManager 收到服务启动请求: APPLICATION_STARTUP, 强制启动: false
2025-08-21 21:09:57.805  5998-5998  Common                  com.dspread.mdm.service              D  🔧 ServiceStartupManager 开始执行服务启动: APPLICATION_STARTUP
2025-08-21 21:09:57.808  5998-5998  Common                  com.dspread.mdm.service              D  🔧 ServiceManager: 准备启动后台服务
2025-08-21 21:09:57.811  5998-5998  ContextImpl             com.dspread.mdm.service              W  Calling a method in the system process without a qualified user: android.app.ContextImpl.startService:1669 android.content.ContextWrapper.startService:720 com.dspread.mdm.service.platform.manager.ServiceManager.startBackgroundService:20 com.dspread.mdm.service.platform.manager.ServiceStartupManager.performServiceStart:121 com.dspread.mdm.service.platform.manager.ServiceStartupManager.startService:66 
2025-08-21 21:09:57.815  5998-5998  Common                  com.dspread.mdm.service              D  🔧 ServiceManager: 后台服务启动成功，ComponentName: ComponentInfo{com.dspread.mdm.service/com.dspread.mdm.service.services.SmartMdmBackgroundService}
2025-08-21 21:09:58.857  5998-5998  Common                  com.dspread.mdm.service              D  🔧 ServiceStartupManager 服务启动验证成功: APPLICATION_STARTUP
2025-08-21 21:09:58.860  5998-5998  Common                  com.dspread.mdm.service              D  🔧 ServiceStartupManager 服务启动成功: APPLICATION_STARTUP
2025-08-21 21:09:58.862  5998-5998  Common                  com.dspread.mdm.service              D  🔧 SmartMdmServiceApp: 主服务启动请求已提交
2025-08-21 21:09:58.864  5998-5998  Common                  com.dspread.mdm.service              D  🔧 SmartMdmServiceApp: Application启动完成，包名: com.dspread.mdm.service
2025-08-21 21:09:58.997  5998-5998  SurfaceFactory          com.dspread.mdm.service              I  [static] sSurfaceFactory = com.mediatek.view.impl.SurfaceFactoryImpl@b98077b
2025-08-21 21:09:59.007  5998-5998  ViewRootIm...tActivity] com.dspread.mdm.service              D  hardware acceleration = true , fakeHwAccelerated = false, sRendererDisabled = false, forceHwAccelerated = false, sSystemRendererDisabled = false
2025-08-21 21:09:59.015  5998-5998  InputTransport          com.dspread.mdm.service              I  Create ARC handle: 0x9e17e380
2025-08-21 21:09:59.016  5998-5998  PhoneWindow             com.dspread.mdm.service              V  DecorView setVisiblity: visibility = 0, Parent = android.view.ViewRootImpl@e15a057, this = DecorView@59f744[TestActivity]
2025-08-21 21:09:59.024  5998-5998  PhoneWindow             com.dspread.mdm.service              V  DecorView setVisiblity: visibility = 4, Parent = android.view.ViewRootImpl@e15a057, this = DecorView@59f744[TestActivity]
2025-08-21 21:09:59.026  5998-5998  Choreographer           com.dspread.mdm.service              I  Skipped 79 frames!  The application may be doing too much work on its main thread.
2025-08-21 21:09:59.060  5998-5998  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 服务启动
2025-08-21 21:09:59.073  5998-5998  setContentIntent        com.dspread.mdm.service              D  packageName = com.dspread.mdm.service
2025-08-21 21:09:59.079   947-975   NotificationService     system_server                        E  enqueueNotificationInternal pkg: com.dspread.mdm.service
2025-08-21 21:09:59.079  5998-5998  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 开始服务初始化
2025-08-21 21:09:59.082  5998-5998  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] Android < 14: 启动Provisioning服务
2025-08-21 21:09:59.086  5998-6031  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 启动ProvisioningService
2025-08-21 21:09:59.086  5998-5998  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 服务创建完成
2025-08-21 21:09:59.090  5998-5998  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] onStartCommand调用
2025-08-21 21:09:59.096  5998-6031  Provisioning            com.dspread.mdm.service              I  ℹ️ ProvisioningManager初始化完成
2025-08-21 21:09:59.099  5998-5998  Platform                com.dspread.mdm.service              D  🔧 WakeLockManager 获取智能WakeLock成功，持续时间: 60000ms
2025-08-21 21:09:59.099  5998-6031  Provisioning            com.dspread.mdm.service              D  🔧 状态标志文件不存在，使用默认值
2025-08-21 21:09:59.104  5998-5998  Platform                com.dspread.mdm.service              D  🔧 WakeLockManager 设置WakeLock续期定时器: 60000ms
2025-08-21 21:09:59.115  5998-6031  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] ProvisioningService启动完成
2025-08-21 21:09:59.116  5998-6032  Provisioning            com.dspread.mdm.service              I  ℹ️ 开始执行Provisioning配置，触发类型: FIRST_BOOT
2025-08-21 21:09:59.118  5998-6032  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] Provisioning进度: 正在下载配置文件...
2025-08-21 21:09:59.120  5998-5998  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 初始化基础组件
2025-08-21 21:09:59.120  5998-6032  Provisioning            com.dspread.mdm.service              I  ℹ️ 开始请求远程配置...
2025-08-21 21:09:59.133  5998-6032  Provisioning            com.dspread.mdm.service              I  ℹ️ 请求配置: https://config.dspreadserv.net/status/config?SN=MDE2MTAwNDAyMDIzMDcwNjAyMjc=&timestamp=1755781799133
2025-08-21 21:09:59.144  5998-6032  System.out              com.dspread.mdm.service              I  [okhttp]:check permission begin!
2025-08-21 21:09:59.144  5998-6032  System                  com.dspread.mdm.service              W  ClassLoader referenced unknown path: system/framework/mediatek-cta.jar
2025-08-21 21:09:59.146  5998-6032  System.out              com.dspread.mdm.service              I  [okhttp] e:java.lang.ClassNotFoundException: com.mediatek.cta.CtaUtils
2025-08-21 21:09:59.161  5998-5998  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 服务初始化完成
2025-08-21 21:09:59.217  5998-6033  Platform                com.dspread.mdm.service              D  🔧 DeviceInfoApi 单例实例已创建
2025-08-21 21:09:59.237  5998-6033  Platform                com.dspread.mdm.service              W  ⚠️ SpVersionApi SP版本信息格式异常，未找到SP_VERSION标记
2025-08-21 21:09:59.250  5998-6033  Platform                com.dspread.mdm.service              D  🔧 SpVersionApi 从Settings获取SP版本: STD 582.1.238.8
2025-08-21 21:09:59.285  5998-5998  WebSocket               com.dspread.mdm.service              I  🔧 C0905 主动式上送: manual_trigger (主动: 1)
2025-08-21 21:09:59.298  5998-5998  WebSocket               com.dspread.mdm.service              I  🔧 首次上送，上送当日数据
2025-08-21 21:09:59.301  5998-5998  WebSocket               com.dspread.mdm.service              I  🔧 没有流量数据需要上送
2025-08-21 21:09:59.303  5998-5998  Common                  com.dspread.mdm.service              D  🔧 刷新时自动上送流量统计
2025-08-21 21:09:59.513  5998-6032  System.out              com.dspread.mdm.service              I  [socket]:check permission begin!
2025-08-21 21:09:59.514  5998-6032  System                  com.dspread.mdm.service              W  ClassLoader referenced unknown path: system/framework/mediatek-cta.jar
2025-08-21 21:09:59.515  5998-6032  System.out              com.dspread.mdm.service              I  [socket] e:java.lang.ClassNotFoundException: com.mediatek.cta.CtaUtils
2025-08-21 21:10:00.616  5998-6032  System.out              com.dspread.mdm.service              I  [OkHttp] sendRequest>>
2025-08-21 21:10:00.617  5998-6032  System.out              com.dspread.mdm.service              I  [OkHttp] sendRequest<<
2025-08-21 21:10:01.872  5998-6032  Provisioning            com.dspread.mdm.service              I  ℹ️ 远程配置获取成功
2025-08-21 21:10:01.875  5998-6032  Provisioning            com.dspread.mdm.service              I  ℹ️ 远程配置请求成功
2025-08-21 21:10:01.877  5998-6032  Provisioning            com.dspread.mdm.service              I  ℹ️ 远程配置解析成功 - CID: 1001
2025-08-21 21:10:01.880  5998-6032  Provisioning            com.dspread.mdm.service              I  ℹ️ 创建配置目录成功: /sdcard/Android/data/com.dspread.mdm.service/files/config
2025-08-21 21:10:01.890  5998-6032  Provisioning            com.dspread.mdm.service              I  ℹ️ API响应已保存到本地: /sdcard/Android/data/com.dspread.mdm.service/files/config/provisioning_config.json, 具体内容为:
                                                                                                    {"mode":"dev","data":{"requestTime":"1755781802735","customization":{"system":{"timezone":"Asia\/Hong_Kong","logo":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/test\/logo.bin","logoMd5":"ba1ee533924eae5c408465e7cddcbda4","bootAnimationMd5":"ba1ee533924eae5c408465e7cddcbda4","gps":{"minUpdateTime":"30","scheduleTime":"60","maxLocateTime":"0","minDistance":"10","valid_distance":"500","care":"1"},"powerSaveMode":{"enable":"1","screenTimeout":"0"},"bootAnimation":"https:\/\/siot-bucket01.s3.ap-northeast-1.amazonaws.com\/test\/bootanimation.zip"},"polling":{"heartbeatTime":"300","terminalInfoTime":"900","statusApiUrl":"wss:\/\/api.dspreadserv.net\/status\/websocket\/register","remoteUrl":"wss:\/\/remote.dspreadserv.net\/remoteWSS\/websockify","uploadMode":"1","wssreconn":{"pingInterval":"30","delayPolicy":"1","delaySwitch":"1","delayTime":"60"}}},"client":"default","cid":"1001"},"function":"getProvisioningConfig","description":"SUCCESS","stateCode":"0","version":"v1.0.1.20250801"}
2025-08-21 21:10:01.893  5998-6032  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] Provisioning进度: 正在下载 Logo...
2025-08-21 21:10:01.897  5998-6032  Provisioning            com.dspread.mdm.service              I  ℹ️ 文件不存在，开始下载: Logo
2025-08-21 21:10:01.899  5998-6032  Provisioning            com.dspread.mdm.service              I  ℹ️ 下载: https://siot-bucket01.s3.ap-northeast-1.amazonaws.com/test/logo.bin -> /sdcard/Android/data/com.dspread.mdm.service/files/config/logo.bin
2025-08-21 21:10:01.907  5998-6032  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 尝试下载，第1次
2025-08-21 21:10:01.910  5998-6032  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 开始下载
2025-08-21 21:10:01.912  5998-6032  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader URL: https://siot-bucket01.s3.ap-northeast-1.amazonaws.com/test/logo.bin
2025-08-21 21:10:01.914  5998-6032  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 路径: /sdcard/Android/data/com.dspread.mdm.service/files/config/logo.bin
2025-08-21 21:10:01.917  5998-6032  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 本地文件不存在，创建文件
2025-08-21 21:10:01.920  5998-6032  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader allFileLength=0, 需要从服务器获取文件大小
2025-08-21 21:10:01.924  5998-6032  System.out              com.dspread.mdm.service              I  [okhttp]:check permission begin!
2025-08-21 21:10:01.925  5998-6032  System.out              com.dspread.mdm.service              I  [okhttp]:not MMS!
2025-08-21 21:10:01.925  5998-6032  System.out              com.dspread.mdm.service              I  [okhttp]:not Email!
2025-08-21 21:10:02.278  5998-6032  System.out              com.dspread.mdm.service              I  [socket]:check permission begin!
2025-08-21 21:10:02.756  5998-6032  System.out              com.dspread.mdm.service              I  [OkHttp] sendRequest>>
2025-08-21 21:10:02.757  5998-6032  System.out              com.dspread.mdm.service              I  [OkHttp] sendRequest<<
2025-08-21 21:10:03.404  5998-6032  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader HTTP响应码: 200
2025-08-21 21:10:03.407  5998-6032  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 服务器返回Content-Length: 4771451
2025-08-21 21:10:03.409  5998-6032  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 完整下载模式
2025-08-21 21:10:03.411  5998-6032  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 完整下载，使用服务器返回大小: 4771451
2025-08-21 21:10:03.413  5998-6032  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader localFile = true /sdcard/Android/data/com.dspread.mdm.service/files/config/logo.bin, 追加模式: false
2025-08-21 21:10:04.516  5998-6049  ProfileInstaller        com.dspread.mdm.service              D  Installing profile for com.dspread.mdm.service
2025-08-21 21:10:44.593  5998-6032  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 计算MD5: logo.bin
2025-08-21 21:10:44.686  5998-6032  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 文件: 4771451 MD5: ba1ee533924eae5c408465e7cddcbda4
2025-08-21 21:10:44.688  5998-6032  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader MD5校验正确
2025-08-21 21:10:44.690  5998-6032  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 清理连接资源
2025-08-21 21:10:44.693  5998-6032  Provisioning            com.dspread.mdm.service              I  ℹ️ Logo 下载完成: /sdcard/Android/data/com.dspread.mdm.service/files/config/logo.bin
2025-08-21 21:10:44.695  5998-6032  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] Provisioning进度: 正在下载 BootAnimation...
2025-08-21 21:10:44.698  5998-6032  Provisioning            com.dspread.mdm.service              I  ℹ️ 文件不存在，开始下载: BootAnimation
2025-08-21 21:10:44.701  5998-6032  Provisioning            com.dspread.mdm.service              I  ℹ️ 下载: https://siot-bucket01.s3.ap-northeast-1.amazonaws.com/test/bootanimation.zip -> /sdcard/Android/data/com.dspread.mdm.service/files/config/bootanimation.zip
2025-08-21 21:10:44.703  5998-6032  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 尝试下载，第1次
2025-08-21 21:10:44.706  5998-6032  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 开始下载
2025-08-21 21:10:44.708  5998-6032  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader URL: https://siot-bucket01.s3.ap-northeast-1.amazonaws.com/test/bootanimation.zip
2025-08-21 21:10:44.710  5998-6032  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 路径: /sdcard/Android/data/com.dspread.mdm.service/files/config/bootanimation.zip
2025-08-21 21:10:44.712  5998-6032  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 本地文件不存在，创建文件
2025-08-21 21:10:44.715  5998-6032  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader allFileLength=0, 需要从服务器获取文件大小
2025-08-21 21:10:44.719  5998-6032  System.out              com.dspread.mdm.service              I  [okhttp]:check permission begin!
2025-08-21 21:10:44.719  5998-6032  System.out              com.dspread.mdm.service              I  [okhttp]:not MMS!
2025-08-21 21:10:44.720  5998-6032  System.out              com.dspread.mdm.service              I  [okhttp]:not Email!
2025-08-21 21:10:44.720  5998-6032  System.out              com.dspread.mdm.service              I  [OkHttp] sendRequest>>
2025-08-21 21:10:44.721  5998-6032  System.out              com.dspread.mdm.service              I  [OkHttp] sendRequest<<
2025-08-21 21:10:45.082  5998-6032  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader HTTP响应码: 200
2025-08-21 21:10:45.085  5998-6032  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 服务器返回Content-Length: 4771451
2025-08-21 21:10:45.088  5998-6032  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 完整下载模式
2025-08-21 21:10:45.090  5998-6032  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 完整下载，使用服务器返回大小: 4771451
2025-08-21 21:10:45.093  5998-6032  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader localFile = true /sdcard/Android/data/com.dspread.mdm.service/files/config/bootanimation.zip, 追加模式: false
2025-08-21 21:11:36.305  5998-6032  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 计算MD5: bootanimation.zip
2025-08-21 21:11:36.401  5998-6032  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 文件: 4771451 MD5: ba1ee533924eae5c408465e7cddcbda4
2025-08-21 21:11:36.403  5998-6032  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader MD5校验正确
2025-08-21 21:11:36.405  5998-6032  HTTPS                   com.dspread.mdm.service              D  🔧 HttpDownloader 清理连接资源
2025-08-21 21:11:36.408  5998-6032  Provisioning            com.dspread.mdm.service              I  ℹ️ BootAnimation 下载完成: /sdcard/Android/data/com.dspread.mdm.service/files/config/bootanimation.zip
2025-08-21 21:11:36.410  5998-6032  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] Provisioning进度: 正在应用配置...
2025-08-21 21:11:36.412  5998-6032  Provisioning            com.dspread.mdm.service              I  ℹ️ 配置应用完成
2025-08-21 21:11:36.415  5998-6032  Provisioning            com.dspread.mdm.service              I  ℹ️ 远程配置成功，标记首次配置完成
2025-08-21 21:11:36.419  5998-6032  Provisioning            com.dspread.mdm.service              D  🔧 状态标志保存成功: /sdcard/Android/data/com.dspread.mdm.service/files/config/provisioning_flags.json
2025-08-21 21:11:36.421  5998-6032  Provisioning            com.dspread.mdm.service              I  ℹ️ ProvisioningManager: 初始启动标记完成，网络重连重试功能已激活
2025-08-21 21:11:36.423  5998-6032  Provisioning            com.dspread.mdm.service              I  ℹ️ Provisioning配置执行完成
2025-08-21 21:11:36.425  5998-6032  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] Provisioning完成，启动核心组件
2025-08-21 21:11:36.440  5998-5998  WebSocket               com.dspread.mdm.service              I  🔧 开始初始化 WebSocket 组件...
2025-08-21 21:11:36.446  5998-5998  WebSocket               com.dspread.mdm.service              I  🔧 初始化 WebSocket 密钥管理器
2025-08-21 21:11:36.450  5998-5998  WebSocket               com.dspread.mdm.service              I  🔧 生成客户端公私钥对
2025-08-21 21:11:36.528  5998-5998  Common                  com.dspread.mdm.service              I  ✅ WebSocket 密钥管理器初始化完成
2025-08-21 21:11:36.537  5998-5998  WebSocket               com.dspread.mdm.service              I  🔧 开始初始化 WsMessageCenter...
2025-08-21 21:11:36.539  5998-5998  WebSocket               com.dspread.mdm.service              I  🔧 WsMessageCenter 首次初始化
2025-08-21 21:11:36.555  5998-5998  WebSocket               com.dspread.mdm.service              I  🔧 WsTaskManager 初始化完成
2025-08-21 21:11:36.657  5998-5998  WebSocket               com.dspread.mdm.service              I  🔧 WsMessageCenter 初始化完成
2025-08-21 21:11:36.659  5998-5998  WebSocket               com.dspread.mdm.service              I  🔧 开始清理终态任务，当前任务数量: 0
2025-08-21 21:11:36.661  5998-5998  WebSocket               com.dspread.mdm.service              I  🔧 没有需要清理的终态任务
2025-08-21 21:11:36.664  5998-5998  WebSocket               com.dspread.mdm.service              I  🔧 开始重置任务状态，当前任务个数: 0
2025-08-21 21:11:36.666  5998-5998  WebSocket               com.dspread.mdm.service              I  🔧 没有需要重置的任务
2025-08-21 21:11:36.668  5998-5998  Common                  com.dspread.mdm.service              I  ✅ 任务状态恢复完成
2025-08-21 21:11:36.675  5998-5998  WebSocket               com.dspread.mdm.service              I  🔧 使用Provisioning配置的WebSocket URL: wss://api.dspreadserv.net/status/websocket/register
2025-08-21 21:11:36.686  5998-5998  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接管理器初始化完成
2025-08-21 21:11:36.688  5998-5998  Common                  com.dspread.mdm.service              I  ✅ WebSocket 管理器初始化完成
2025-08-21 21:11:36.690  5998-5998  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 管理器初始化完成
2025-08-21 21:11:36.694  5998-5998  Common                  com.dspread.mdm.service              I  ✅ WebSocket 组件初始化完成
2025-08-21 21:11:36.696  5998-5998  WebSocket               com.dspread.mdm.service              I  🔧 启动 WebSocket 连接...
2025-08-21 21:11:36.698  5998-5998  WebSocket               com.dspread.mdm.service              I  🔧 开始连接 WebSocket...
2025-08-21 21:11:36.712  5998-5998  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接到: wss://api.dspreadserv.net/status/websocket/register?serialNo=MDE2MTAwNDAyMDIzMDcwNjAyMjc=&publicKey=TUlHZk1BMEdDU3FHU0liM0RRRUJBUVVBQTRHTkFEQ0JpUUtCZ1FDdHJqTmMvNnNhU2ZRVnZGUUhuaUtZelg4MVM0RWVoeTNsY1ZscU05MmxWazJkZGdZR0dZdnI4OFBSZ0wrZVNnYU9HdXhpNWNqRUEyTUo2ZHB2L09IaDBrVThUYkl4dm9Vem9CSFJjbUNsS2c5RlZRQ1M1QU1EYll2WUNaVm1iYVFIU2NxdjQxREtQOGF6aHlBTTVSTHMwZ042VEMwT2xWY2VUTTJjd05PSXN3SURBUUFC&query=1&msgVer=3&timestamp=1755781896702&signature=P7/k/vTqtNsQwUlGgnYk9+0Iovx1hrIvJV3wtVOnwyKmz/sMPDxHdgDyg0DSrUGxcMgziUsZ0KO+9+a7lXphm+o/BkNZsSI/XXqNak7K+Sq984q+UBvOagMLGpAxxITIYZAImINdeMIAi9D/NMan32hCNkrMv1XwxxP+mOi748E=
2025-08-21 21:11:36.717  5998-5998  WebSocket               com.dspread.mdm.service              I  🔧 未检测到系统代理设置
2025-08-21 21:11:36.742  5998-5998  WebSocket               com.dspread.mdm.service              I  🔧 ===== WebSocket 库默认配置 =====
2025-08-21 21:11:36.744  5998-5998  WebSocket               com.dspread.mdm.service              I  🔧 默认 PING 间隔: 30000ms (30秒)
2025-08-21 21:11:36.746  5998-5998  WebSocket               com.dspread.mdm.service              I  🔧 默认 PONG 间隔: 0ms (0秒)
2025-08-21 21:11:36.748  5998-5998  WebSocket               com.dspread.mdm.service              I  🔧 帧队列大小: 5
2025-08-21 21:11:36.750  5998-5998  WebSocket               com.dspread.mdm.service              I  🔧 连接超时: 15000ms (15秒)
2025-08-21 21:11:36.753  5998-5998  WebSocket               com.dspread.mdm.service              I  🔧 允许缺失关闭帧: false
2025-08-21 21:11:36.754  5998-5998  WebSocket               com.dspread.mdm.service              I  🔧 ================================
2025-08-21 21:11:36.758  5998-5998  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接请求已发送
2025-08-21 21:11:36.760  5998-5998  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接启动完成
2025-08-21 21:11:36.762  5998-5998  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 开始初始化广播管理器...
2025-08-21 21:11:36.767  5998-5998  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 开始初始化...
2025-08-21 21:11:36.774  5998-5998  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 包更新广播接收器注册完成
2025-08-21 21:11:36.776  5998-5998  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 统一广播接收器注册完成
2025-08-21 21:11:36.778  5998-5998  Common                  com.dspread.mdm.service              I  ✅ BroadcastManager 初始化完成
2025-08-21 21:11:36.781  5998-5998  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: NetworkEventHandler -> android.net.conn.CONNECTIVITY_CHANGE
2025-08-21 21:11:36.784  5998-5998  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: NetworkEventHandler -> android.net.wifi.STATE_CHANGE
2025-08-21 21:11:36.787  5998-5998  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: BatteryEventHandler -> android.intent.action.BATTERY_CHANGED
2025-08-21 21:11:36.789  5998-5998  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: BatteryEventHandler -> android.intent.action.BATTERY_LOW
2025-08-21 21:11:36.791  5998-5998  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: BatteryEventHandler -> android.intent.action.BATTERY_OKAY
2025-08-21 21:11:36.793  5998-5998  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: BatteryEventHandler -> android.intent.action.ACTION_POWER_CONNECTED
2025-08-21 21:11:36.795  5998-5998  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: BatteryEventHandler -> android.intent.action.ACTION_POWER_DISCONNECTED
2025-08-21 21:11:36.798  5998-5998  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: SystemEventHandler -> android.intent.action.BOOT_COMPLETED
2025-08-21 21:11:36.801  5998-5998  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: SystemEventHandler -> android.intent.action.QUICKBOOT_POWERON
2025-08-21 21:11:36.803  5998-5998  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: SystemEventHandler -> android.intent.action.LOCKED_BOOT_COMPLETED
2025-08-21 21:11:36.805  5998-5998  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: SystemEventHandler -> android.intent.action.ACTION_SHUTDOWN
2025-08-21 21:11:36.807  5998-5998  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: SystemEventHandler -> android.intent.action.REBOOT
2025-08-21 21:11:36.810  5998-5998  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: ScreenEventHandler -> android.intent.action.SCREEN_ON
2025-08-21 21:11:36.812  5998-5998  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: ScreenEventHandler -> android.intent.action.SCREEN_OFF
2025-08-21 21:11:36.817  5998-5998  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: HeartbeatEventHandler -> com.dspread.mdm.service.POLL_TIMER_START
2025-08-21 21:11:36.822  5998-5998  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: PackageUpdateEventHandler -> android.intent.action.PACKAGE_ADDED
2025-08-21 21:11:36.824  5998-5998  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: PackageUpdateEventHandler -> android.intent.action.PACKAGE_REMOVED
2025-08-21 21:11:36.826  5998-5998  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: PackageUpdateEventHandler -> android.intent.action.PACKAGE_REPLACED
2025-08-21 21:11:36.828  5998-5998  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: PackageUpdateEventHandler -> com.dspread.mdm.service.CHECK_SELF_UPDATE_STATUS
2025-08-21 21:11:36.832  5998-5998  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: TaskExecuteEventHandler -> com.dspread.mdm.service.WSTASK_EXEC_BC
2025-08-21 21:11:36.835  5998-5998  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: TerminalInfoEventHandler -> com.dspread.mdm.service.TER_INFO_UPLOAD_BC
2025-08-21 21:11:36.837  5998-5998  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: ServiceGuardEventHandler -> com.dspread.mdm.service.SERVICE_GUARD_TIMER
2025-08-21 21:11:36.840  5998-5998  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: ServiceManagementEventHandler -> android.intent.action.USER_PRESENT
2025-08-21 21:11:36.843  5998-5998  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: ServiceManagementEventHandler -> android.intent.action.TIME_TICK
2025-08-21 21:11:36.845  5998-5998  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: ServiceManagementEventHandler -> com.dspread.mdm.service.SERVICE_RESTART
2025-08-21 21:11:36.847  5998-5998  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: ServiceManagementEventHandler -> android.net.conn.CONNECTIVITY_CHANGE
2025-08-21 21:11:36.849  5998-5998  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: WakeLockEventHandler -> com.dspread.mdm.service.WAKELOCK_RENEWAL
2025-08-21 21:11:36.851  5998-5998  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: WakeLockEventHandler -> com.dspread.mdm.service.WAKELOCK_ACQUIRE
2025-08-21 21:11:36.854  5998-5998  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: WakeLockEventHandler -> com.dspread.mdm.service.WAKELOCK_RELEASE
2025-08-21 21:11:36.859  5998-5998  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: ProvisioningEventHandler -> com.dspread.mdm.service.PROVISIONING_TIMER
2025-08-21 21:11:36.861  5998-5998  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: ProvisioningEventHandler -> android.net.conn.CONNECTIVITY_CHANGE
2025-08-21 21:11:36.863  5998-5998  Receiver                com.dspread.mdm.service              D  🔧 BroadcastManager 注册事件处理器: ProvisioningEventHandler -> android.intent.action.BOOT_COMPLETED
2025-08-21 21:11:36.866  5998-5998  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 广播管理器初始化完成
2025-08-21 21:11:36.868  5998-5998  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 开始启动所有定时器...
2025-08-21 21:11:36.871  5998-5998  Provisioning            com.dspread.mdm.service              D  🔧 ProvisioningEventHandler 启动Provisioning定时器: 300s (0小时)
2025-08-21 21:11:36.877  5998-5998  Common                  com.dspread.mdm.service              I  ✅ ⏰ 设置初始化定时器成功，下次执行: 300秒后 (0小时)
2025-08-21 21:11:36.879  5998-5998  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] Provisioning定时器启动成功
2025-08-21 21:11:36.884  5998-5998  Common                  com.dspread.mdm.service              I  ✅ ⏰ 设置心跳定时器成功，下次执行: 60秒后 (1分钟)
2025-08-21 21:11:36.886  5998-5998  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 心跳定时器启动成功
2025-08-21 21:11:36.891  5998-5998  Common                  com.dspread.mdm.service              I  ✅ ⏰ 设置任务执行定时器成功，下次执行: 60秒后 (1分钟)
2025-08-21 21:11:36.893  5998-5998  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 任务执行定时器启动成功
2025-08-21 21:11:36.898  5998-5998  Common                  com.dspread.mdm.service              I  ✅ ⏰ 设置终端信息定时器成功，下次执行: 120秒后 (2分钟)
2025-08-21 21:11:36.900  5998-5998  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 终端信息上传定时器启动成功
2025-08-21 21:11:36.901  5998-5998  Common                  com.dspread.mdm.service              D  🔧 TimerConfig ========== 当前定时器配置 (调试模式) ==========
2025-08-21 21:11:36.903  5998-5998  Common                  com.dspread.mdm.service              D  🔧 TimerConfig ①心跳定时器: 60秒
2025-08-21 21:11:36.905  5998-5998  Common                  com.dspread.mdm.service              D  🔧 TimerConfig ②终端信息上传定时器: 120秒
2025-08-21 21:11:36.907  5998-5998  Common                  com.dspread.mdm.service              D  🔧 TimerConfig ③任务执行定时器: 60秒
2025-08-21 21:11:36.909  5998-5998  Common                  com.dspread.mdm.service              D  🔧 TimerConfig ④服务守护定时器: 120秒
2025-08-21 21:11:36.910  5998-5998  Common                  com.dspread.mdm.service              D  🔧 TimerConfig ⑤初始化定时器: 300秒
2025-08-21 21:11:36.912  5998-5998  Common                  com.dspread.mdm.service              D  🔧 TimerConfig ===============================================
2025-08-21 21:11:36.914  5998-5998  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 所有定时器启动完成
2025-08-21 21:11:36.918  5998-5998  Common                  com.dspread.mdm.service              D  🔧 [ModuleManagerRegistry] 模块管理器注册中心初始化完成
2025-08-21 21:11:36.921  5998-5998  Service                 com.dspread.mdm.service              D  🔧 [SmartMdmBackgroundService] 核心组件初始化完成
2025-08-21 21:11:36.922  5998-6032  Common                  com.dspread.mdm.service              D  🔧 [ModuleManagerRegistry] 启动所有模块
2025-08-21 21:11:36.927  5998-5998  Platform                com.dspread.mdm.service              D  🔧 NetworkApi 单例实例已创建
2025-08-21 21:11:36.931  5998-5998  TetheringManager        com.dspread.mdm.service              I  registerTetheringEventCallback:com.dspread.mdm.service
2025-08-21 21:11:36.934  5998-6032  Common                  com.dspread.mdm.service              D  🔧 Module LogStream Manager status changed to: INITIALIZED
2025-08-21 21:11:36.939  5998-6032  Common                  com.dspread.mdm.service              D  🔧 Module LogStream Manager status changed to: STARTING
2025-08-21 21:11:36.942  5998-5998  Receiver                com.dspread.mdm.service              D  🔧 网络状态变化: 连接=true, 类型=WIFI
2025-08-21 21:11:36.944  5998-5998  Receiver                com.dspread.mdm.service              D  🔧 网络恢复，尝试重新连接WebSocket
2025-08-21 21:11:36.946  5998-5998  Receiver                com.dspread.mdm.service              D  🔧 ServiceManagementEventHandler 处理网络变化事件
2025-08-21 21:11:36.951  5998-5998  Common                  com.dspread.mdm.service              D  🔧 ServiceStartupManager 服务正在运行，无需启动: NETWORK_CHANGE
2025-08-21 21:11:36.954  5998-5998  Receiver                com.dspread.mdm.service              D  🔧 ServiceManagementEventHandler 网络变化后服务检查完成
2025-08-21 21:11:36.955  5998-5998  Provisioning            com.dspread.mdm.service              D  🔧 ProvisioningEventHandler 网络状态变化
2025-08-21 21:11:36.962  5998-5998  Provisioning            com.dspread.mdm.service              I  ℹ️ ProvisioningEventHandler 检测到网络重连，但Provisioning已完成，按正常定时器间隔检查更新
2025-08-21 21:11:36.966  5998-5998  Receiver                com.dspread.mdm.service              D  🔧 电池状态变化: 电量=100%, 温度=26°C, 充电=true
2025-08-21 21:11:36.971  5998-5998  WebSocket               com.dspread.mdm.service              I  🔧 C0902 被动式上送: battery_level_change_5 (被动: 1)
2025-08-21 21:11:36.991  5998-5998  WebSocket               com.dspread.mdm.service              I  🔧 消息已缓存: C0902 (缓存数量: 1)
2025-08-21 21:11:36.993  5998-5998  WebSocket               com.dspread.mdm.service              I  🔧 C0902 电池状态上送成功 (1, trigger=battery_level_change_5)
2025-08-21 21:11:37.015  5998-6032  Common                  com.dspread.mdm.service              D  🔧 Module LogStream Manager status changed to: RUNNING
2025-08-21 21:11:37.017  5998-6032  Common                  com.dspread.mdm.service              D  🔧 [ModuleManagerRegistry] LogStreamManager启动成功
2025-08-21 21:11:37.039  5998-6032  Common                  com.dspread.mdm.service              D  🔧 Module Geofence Manager status changed to: INITIALIZED
2025-08-21 21:11:37.044  5998-6032  Common                  com.dspread.mdm.service              D  🔧 Module Geofence Manager status changed to: STARTING
2025-08-21 21:11:37.065  5998-6032  Platform                com.dspread.mdm.service              D  🔧 用户交互监控已启动（简化版本）
2025-08-21 21:11:37.065  5998-5998  Platform                com.dspread.mdm.service              D  🔧 [WiFiManagerApi] WiFiManagerApi 单例实例已创建
2025-08-21 21:11:37.067  5998-6032  Platform                com.dspread.mdm.service              D  🔧 使用模拟用户交互检测
2025-08-21 21:11:37.068  5998-6032  Platform                com.dspread.mdm.service              D  🔧 用户交互监控已启动
2025-08-21 21:11:37.070  5998-6032  Service                 com.dspread.mdm.service              D  🔧 [GeofenceManager]Constants.geofenceStatus = 1
2025-08-21 21:11:37.072  5998-6032  Service                 com.dspread.mdm.service              D  🔧 [GeofenceManager]-- Constants.storeId =  -- Constants.storeSsid =  -- Constants.storeIp = 
2025-08-21 21:11:37.074  5998-6032  Common                  com.dspread.mdm.service              D  🔧 Module Geofence Manager status changed to: RUNNING
2025-08-21 21:11:37.077  5998-6032  Common                  com.dspread.mdm.service              D  🔧 [ModuleManagerRegistry] GeofenceManager启动成功
2025-08-21 21:11:37.089  5998-6032  RuleBase                com.dspread.mdm.service              D  🔧 RuleBaseManager 开始初始化RuleBaseManager...
2025-08-21 21:11:37.095  5998-6032  RuleBase                com.dspread.mdm.service              D  🔧 RuleBaseStorage RuleBase存储管理器初始化完成
2025-08-21 21:11:37.097  5998-6032  RuleBase                com.dspread.mdm.service              D  🔧 RuleStateMonitor 状态监控器初始化完成
2025-08-21 21:11:37.099  5998-6032  RuleBase                com.dspread.mdm.service              D  🔧 RuleBaseManager RuleBaseManager初始化完成
2025-08-21 21:11:37.100  5998-6032  Common                  com.dspread.mdm.service              D  🔧 [ModuleManagerRegistry] RuleBaseManager启动成功
2025-08-21 21:11:37.102  5998-6032  Common                  com.dspread.mdm.service              D  🔧 [ModuleManagerRegistry] 所有模块启动完成
2025-08-21 21:11:37.102  5998-6067  System.out              com.dspread.mdm.service              I  [socket]:check permission begin!
2025-08-21 21:11:37.352  5998-6068  System.out              com.dspread.mdm.service              I  [socket]:check permission begin!
2025-08-21 21:11:39.073  5998-6070  Common                  com.dspread.mdm.service              I  ✅ WebSocket 物理连接成功，等待服务器 hellMsg
2025-08-21 21:11:39.077  5998-6070  WebSocket               com.dspread.mdm.service              I  🔧 ===== 心跳发送器实际配置 =====
2025-08-21 21:11:39.080  5998-6070  WebSocket               com.dspread.mdm.service              I  🔧 实际 PING 发送间隔: 30000ms (30秒)
2025-08-21 21:11:39.083  5998-6070  WebSocket               com.dspread.mdm.service              I  🔧 PING 定时器名称: PingSender
2025-08-21 21:11:39.086  5998-6070  WebSocket               com.dspread.mdm.service              I  🔧 实际 PONG 发送间隔: 0ms (0秒)
2025-08-21 21:11:39.089  5998-6070  WebSocket               com.dspread.mdm.service              I  🔧 PONG 定时器名称: PongSender
2025-08-21 21:11:39.092  5998-6070  WebSocket               com.dspread.mdm.service              I  🔧 ===============================
2025-08-21 21:11:39.095  5998-6070  WebSocket               com.dspread.mdm.service              I  🔧 启动PING/PONG检测，阈值: 连续3个PING无PONG响应
2025-08-21 21:11:39.790  5998-6069  Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"hellMsg":"hello 01610040202307060227","tranCode":"S0000","version":"1","rebootTime":"03:44:43","serialNo":"01610040202307060227","deviceStatus":"6"}
2025-08-21 21:11:39.793  5998-6069  Common                  com.dspread.mdm.service              I  ✅ 收到服务器 hellMsg，WebSocket 业务连接成功！
2025-08-21 21:11:39.796  5998-6069  Common                  com.dspread.mdm.service              I  ✅ WebSocket 业务连接成功！
2025-08-21 21:11:39.798  5998-6069  WebSocket               com.dspread.mdm.service              I  🔧 业务连接成功，重连计数已重置为 0
2025-08-21 21:11:39.804  5998-6069  Platform                com.dspread.mdm.service              D  🔧 WakeLockManager 获取WiFi锁成功，防止WiFi休眠
2025-08-21 21:11:39.807  5998-6069  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接成功，检查自身更新任务状态
2025-08-21 21:11:39.812  5998-6069  Receiver                com.dspread.mdm.service              D  🔧 发送广播: com.dspread.mdm.service.CHECK_SELF_UPDATE_STATUS
2025-08-21 21:11:39.814  5998-6069  WebSocket               com.dspread.mdm.service              I  🔧 开始发送缓存消息，数量: 1
2025-08-21 21:11:39.820  5998-6069  WebSocket               com.dspread.mdm.service              I  🔧 C0902 主动式上送: cached_message_resend (主动: 2)
2025-08-21 21:11:39.832  5998-6069  Common                  com.dspread.mdm.service              I  ✅ 缓存消息发送成功: C0902
2025-08-21 21:11:40.336  5998-6069  WebSocket               com.dspread.mdm.service              I  🔧 WebSocket 连接成功
2025-08-21 21:11:40.339  5998-6069  WebSocket               com.dspread.mdm.service              I  🔧 设备状态: 6
2025-08-21 21:11:40.342  5998-6069  WebSocket               com.dspread.mdm.service              I  🔧 检查首次连接标志: isFirstTimeWebSocketConnected=true
2025-08-21 21:11:40.344  5998-6069  WebSocket               com.dspread.mdm.service              I  🔧 首次WebSocket连接成功，发送初始化信息并执行初始化检查
2025-08-21 21:11:40.347  5998-6069  WebSocket               com.dspread.mdm.service              I  🔧 开始发送初始化信息（带流量控制）
2025-08-21 21:11:40.349  5998-6069  WebSocket               com.dspread.mdm.service              I  🔧 C0901 被动式上送: first_connection (被动: 2)
2025-08-21 21:11:40.356  5998-6069  Platform                com.dspread.mdm.service              D  🔧 重新收集应用信息
2025-08-21 21:11:40.442  5998-6069  AppManager              com.dspread.mdm.service              I  ℹ️ 应用信息获取完成: 总数120(系统119/用户1) 返回1个
2025-08-21 21:11:40.447  5998-6069  Platform                com.dspread.mdm.service              D  🔧 应用信息: 1 个应用
2025-08-21 21:11:40.455  5998-6069  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0901","serialNo":"01610040202307060227","request_time":"1755781900449","request_id":"1755781900449C0901","version":"1","data":{"apkInfo":[{"packName":"com.dspread.mdm.service","apkName":"service","versionCode":22,"versionName":"1.1.03.20250821.DSPREAD.MDM.SERVICE","updateDate":"2025-08-21 21:09:56"}],"sytemInfo":{"androidVersion":"11","buildNumber":"D60_Baseline_debug_20250708_ota_test","aspl":"2021-12-05"},"serviceInfo":[]},"myVersionName":"1.1.03.20250821.DSPREAD.MDM.SERVICE","terminalDate":"20250821211140"}
2025-08-21 21:11:40.457  5998-6069  WebSocket               com.dspread.mdm.service              I  🔧 C0901 应用信息上送成功 (1)
2025-08-21 21:11:41.459  5998-6069  WebSocket               com.dspread.mdm.service              I  🔧 C0902 被动式上送: first_connection (被动: 3)
2025-08-21 21:11:41.475  5998-6069  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0902","serialNo":"01610040202307060227","request_time":"1755781901467","request_id":"1755781901467C0902","version":"1","data":{"batteryLife":100,"batteryHealth":2,"temprature":"26.8","isCharging":"1","isLowBattery":"0","powerOff":"0","outOfBattery":"0"},"myVersionName":"1.1.03.20250821.DSPREAD.MDM.SERVICE","terminalDate":"20250821211141"}
2025-08-21 21:11:41.477  5998-6069  WebSocket               com.dspread.mdm.service              I  🔧 C0902 电池状态上送成功 (1, trigger=first_connection)
2025-08-21 21:11:42.481  5998-6069  WebSocket               com.dspread.mdm.service              I  🔧 C0903 被动式上送: first_connection (被动: 4)
2025-08-21 21:11:42.626  5998-6069  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0903","serialNo":"01610040202307060227","request_time":"1755781902617","request_id":"1755781902617C0903","version":"1","data":{"hardwareInfo":[{"name":"CPU Usage","desc":"cpu:0.50"},{"name":"storage_usage_status","desc":" 5.47GB \/ 32.00GB ","state":"1"},{"name":"total_memory","desc":" 0.55GB \/ 1.86GB ","state":"1"},{"name":"androidVersion","desc":"11","state":"1"},{"name":"network_status","desc":"wifi","state":"1"},{"name":"OSVer","desc":"11","state":"1"}],"locationInfo":{"gps":{"longitude":"-999.0","latitude":"-999.0","satelliteCount":0,"satelliteCountInUsed":0,"satelliteAverSNR":"0.00","distance":"0.0","lockStatus":"1","lockMeter":"200","proId":"","proName":""},"baseSite":{"MCC":"","MNC":"","CID":"-1","LAC":"-1","DBM":"-999","IMSI":"","ICCID":""},"wifi":{"SSID":"fubox_2.4G","IP":"**************","MAC":"52:C3:63:13:8C:EE","SSTH":"-25","BSSID":"7c:10:c9:d6:9f:10"},"address":"无法获取"}},"myVersionName":"1.1.03.20250821.DSPREAD.MDM.SERVICE","terminalDate":"20250821211142"}
2025-08-21 21:11:42.628  5998-6069  WebSocket               com.dspread.mdm.service              I  🔧 C0903 数据信息上送成功 (1)
2025-08-21 21:11:43.631  5998-6069  WebSocket               com.dspread.mdm.service              I  🔧 C0904 被动式上送: first_connection (被动: 5)
2025-08-21 21:11:43.733  5998-6069  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0904","serialNo":"01610040202307060227","request_time":"1755781903722","request_id":"1755781903722C0904","version":"1","data":{"wifiOption":[{"SSID":"fubox_2.4G","SSTH":"-28"},{"SSID":"2206-5G","SSTH":"-32"},{"SSID":"fubox_5G","SSTH":"-40"},{"SSID":"2206","SSTH":"-41"},{"SSID":"2205_5G","SSTH":"-51"},{"SSID":"2205","SSTH":"-53"},{"SSID":"@Ruijie-1816","SSTH":"-56"},{"SSID":"2207","SSTH":"-67"},{"SSID":"DIRECT-C6-Mi All-in-One Inkjet","SSTH":"-70"},{"SSID":"DIRECT-CC-HP Laser 1188w","SSTH":"-71"},{"SSID":"2306","SSTH":"-72"},{"SSID":"2106","SSTH":"-72"},{"SSID":"2103","SSTH":"-77"},{"SSID":"2207-5G","SSTH":"-80"},{"SSID":"诺富特酒店2208","SSTH":"-81"},{"SSID":"dingjie888","SSTH":"-82"},{"SSID":"FJQS","SSTH":"-83"},{"SSID":"ChinaNet-SnSC","SSTH":"-85"},{"SSID":"2405","SSTH":"-86"},{"SSID":"2103_5G","SSTH":"-86"},{"SSID":"1621","SSTH":"-87"},{"SSID":"HUAWEI_B311_福","SSTH":"-92"},{"SSID":"2106-5G","SSTH":"-94"}],"baseSite":{"MCC":"","MNC":"","CID":"-1","LAC":"-1","DBM":"-999","IMSI":"","ICCID":""},"wifi":{"SSID":"fubox_2.4G","IP":"**************","MAC":"52:C3:63:13:8C:EE","SSTH":"-25","BSSID":"7c:10:c9:d6:9f:10"}},"myVersionName":"1.1.03.20250821.DSPREAD.MDM.SERVICE","terminalDate":"20250821211143"}
2025-08-21 21:11:43.735  5998-6069  WebSocket               com.dspread.mdm.service              I  🔧 C0904 网络状态上送成功 (1)
2025-08-21 21:11:44.738  5998-6069  WebSocket               com.dspread.mdm.service              I  🔧 C0906 被动式上送: first_connection (被动: 6)
2025-08-21 21:11:44.745  5998-6069  Platform                com.dspread.mdm.service              W  ⚠️ SpVersionApi SP版本信息格式异常，未找到SP_VERSION标记
2025-08-21 21:11:44.749  5998-6069  Platform                com.dspread.mdm.service              D  🔧 SpVersionApi 从Settings获取SP版本: STD 582.1.238.8
2025-08-21 21:11:44.763  5998-6069  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0906","serialNo":"01610040202307060227","request_time":"1755781904755","request_id":"1755781904755C0906","version":"1","data":{"firmWareInfo":{"spfw":"STD 582.1.238.8"},"imei_1":"014250620000709","imei_2":"","wifi_mac":"52:c3:63:13:8c:ee","bt_mac":"","bsn":""},"myVersionName":"1.1.03.20250821.DSPREAD.MDM.SERVICE","terminalDate":"20250821211144"}
2025-08-21 21:11:44.766  5998-6069  WebSocket               com.dspread.mdm.service              I  🔧 C0906 设备信息上送成功 (1)
2025-08-21 21:11:44.768  5998-6069  WebSocket               com.dspread.mdm.service              I  🔧 C0109 被动式上送: first_connection (被动: 7)
2025-08-21 21:11:44.775  5998-6069  Platform                com.dspread.mdm.service              D  🔧 重新收集应用信息（强制刷新）
2025-08-21 21:11:44.834  5998-6069  AppManager              com.dspread.mdm.service              I  ℹ️ 应用信息获取完成: 总数120(系统119/用户1) 返回1个
2025-08-21 21:11:44.837  5998-6069  Platform                com.dspread.mdm.service              D  🔧 应用信息: 1 个应用
2025-08-21 21:11:44.949  5998-6014  System                  com.dspread.mdm.service              W  A resource failed to call close. 
2025-08-21 21:11:44.968  5998-6069  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0109","serialNo":"01610040202307060227","request_time":"1755781904952","request_id":"1755781904952C0109","version":"1","data":{"apkInfo":[{"packName":"com.dspread.mdm.service","apkName":"service","versionCode":22,"versionName":"1.1.03.20250821.DSPREAD.MDM.SERVICE","updateDate":"2025-08-21 21:09:56"}],"locationInfo":{"gps":{"longitude":"-999.0","latitude":"-999.0","satelliteCount":0,"satelliteCountInUsed":0,"satelliteAverSNR":"0.00","distance":"0.0","lockStatus":"1","lockMeter":"200","proId":"","proName":""},"baseSite":{"MCC":"","MNC":"","CID":"-1","LAC":"-1","DBM":"-999","IMSI":"","ICCID":""},"wifi":{"SSID":"fubox_2.4G","IP":"**************","MAC":"52:C3:63:13:8C:EE","SSTH":"-25","BSSID":"7c:10:c9:d6:9f:10"},"address":"无法获取"},"hardwareInfo":[{"name":"CPU Usage","desc":"cpu:0.50"},{"name":"storage_usage_status","desc":" 5.47GB \/ 32.00GB ","state":"1"},{"name":"total_memory","desc":" 0.55GB \/ 1.86GB ","state":"1"},{"name":"androidVersion","desc":"11","state":"1"},{"name":"network_status","desc":"wifi","state":"1"},{"name":"OSVer","desc":"11","state":"1"}],"wifiOption":[{"SSID":"fubox_2.4G","SSTH":"-28"},{"SSID":"2206-5G","SSTH":"-32"},{"SSID":"fubox_5G","SSTH":"-40"},{"SSID":"2206","SSTH":"-41"},{"SSID":"2205_5G","SSTH":"-51"},{"SSID":"2205","SSTH":"-53"},{"SSID":"@Ruijie-1816","SSTH":"-56"},{"SSID":"2207","SSTH":"-67"},{"SSID":"DIRECT-C6-Mi All-in-One Inkjet","SSTH":"-70"},{"SSID":"DIRECT-CC-HP Laser 1188w","SSTH":"-71"},{"SSID":"2306","SSTH":"-72"},{"SSID":"2106","SSTH":"-72"},{"SSID":"2103","SSTH":"-77"},{"SSID":"2207-5G","SSTH":"-80"},{"SSID":"诺富特酒店2208","SSTH":"-81"},{"SSID":"dingjie888","SSTH":"-82"},{"SSID":"FJQS","SSTH":"-83"},{"SSID":"ChinaNet-SnSC","SSTH":"-85"},{"SSID":"2405","SSTH":"-86"},{"SSID":"2103_5G","SSTH":"-86"},{"SSID":"1621","SSTH":"-87"},{"SSID":"HUAWEI_B311_福","SSTH":"-92"},{"SSID":"2106-5G","SSTH":"-94"}],"sytemInfo":{"androidVersion":"11","buildNumber":"D60_Baseline_debug_20250708_ota_test","aspl":"2021-12-05"}},"myVersionName":"1.1.03.20250821.DSPREAD.MDM.SERVICE","terminalDate":"20250821211144"}
2025-08-21 21:11:44.970  5998-6069  WebSocket               com.dspread.mdm.service              I  🔧 C0109 终端信息上传完成 (1, trigger=first_connection)
2025-08-21 21:11:44.972  5998-6069  Common                  com.dspread.mdm.service              I  ✅ 初始化信息发送完成
2025-08-21 21:11:44.974  5998-6069  WebSocket               com.dspread.mdm.service              I  🔧 检查服务更新结果
2025-08-21 21:11:44.976  5998-6069  Task                    com.dspread.mdm.service              W  ⚠️ 任务列表为空，跳过服务更新结果检查
2025-08-21 21:11:44.978  5998-6069  WebSocket               com.dspread.mdm.service              I  🔧 检查OS升级状态...
2025-08-21 21:11:44.983  5998-6069  Common                  com.dspread.mdm.service              D  🔧 OsUpdateStatusChecker WebSocket连接成功，检查OS升级状态
2025-08-21 21:11:44.985  5998-6069  Common                  com.dspread.mdm.service              D  🔧 OsUpdateStatusChecker 没有待检查的OS升级任务
2025-08-21 21:11:44.987  5998-6069  WebSocket               com.dspread.mdm.service              I  🔧 OS升级状态检查已触发
2025-08-21 21:11:44.989  5998-6069  WebSocket               com.dspread.mdm.service              I  🔧 首次连接初始化检查完成
2025-08-21 21:11:45.008  5998-6069  Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"msg_state":"P","request_time":"1755580536662","data":{"taskList":[{"beginDate":"2024-08-19 05:15:36","taskType":"02","apkName":"Via","endDate":"9999-12-31 23:59:59","appId":"1755580536662","packName":"mark.via","versionName":"6.2.0","taskId":"1755580536662","versionCode":"20250117"}]},"tranCode":"ST001","request_id":"1755580536662ST001","version":"1","serialNo":"01610040202307060227"}
2025-08-21 21:11:45.010  5998-6069  WebSocket               com.dspread.mdm.service              I  🔧 准备发送响应确认 - requestId: 1755580536662ST001, needResponse: true
2025-08-21 21:11:45.016  5998-6069  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0000","serialNo":"01610040202307060227","request_time":"1755781905013","request_id":"1755781905013C0000","version":"1","org_request_id":"1755580536662ST001","org_request_time":"1755580536662","response_state":"0","myVersionName":"1.1.03.20250821.DSPREAD.MDM.SERVICE","terminalDate":"20250821211145"}
2025-08-21 21:11:45.024  5998-6069  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0000","serialNo":"01610040202307060227","request_time":"1755781905020","request_id":"1755781905020C0000","version":"1","org_request_id":"1755580536662ST001","org_request_time":"1755580536662","response_state":"0","myVersionName":"1.1.03.20250821.DSPREAD.MDM.SERVICE","terminalDate":"20250821211145"}
2025-08-21 21:11:45.026  5998-6069  WebSocket               com.dspread.mdm.service              I  🔧 已发送C0000响应确认: requestId=1755580536662ST001
2025-08-21 21:11:45.028  5998-6069  Task                    com.dspread.mdm.service              D  🔧 处理任务消息: ST001
2025-08-21 21:11:45.030  5998-6069  Task                    com.dspread.mdm.service              I  ℹ️ 更新任务列表: requestId=1755580536662ST001, 任务数量=1
2025-08-21 21:11:45.032  5998-6069  Task                    com.dspread.mdm.service              D  🔧 任务插入到末尾: taskId=1755580536662
2025-08-21 21:11:45.033  5998-6069  Task                    com.dspread.mdm.service              D  🔧 添加/更新任务: taskId=1755580536662
2025-08-21 21:11:45.036  5998-6069  Task                    com.dspread.mdm.service              D  🔧 保存任务列表到存储: 1 个
2025-08-21 21:11:45.038  5998-6069  Task                    com.dspread.mdm.service              D  🔧 开始处理任务列表，共 1 个任务
2025-08-21 21:11:45.040  5998-6069  Task                    com.dspread.mdm.service              D  🔧 处理任务: taskId=1755580536662, type=02, package=mark.via, apk=Via
2025-08-21 21:11:45.042  5998-6069  Task                    com.dspread.mdm.service              D  🔧 处理卸载任务: 1755580536662
2025-08-21 21:11:45.044  5998-6069  Task                    com.dspread.mdm.service              D  🔧 更新任务状态: taskId=1755580536662, state=D02
2025-08-21 21:11:45.047  5998-6069  Task                    com.dspread.mdm.service              D  🔧 保存任务列表到存储: 1 个
2025-08-21 21:11:45.049  5998-6069  Task                    com.dspread.mdm.service              D  🔧 保存任务列表到存储: 1 个
2025-08-21 21:11:45.051  5998-6069  WebSocket               com.dspread.mdm.service              I  🔧 ST001 主动式上送: task_failed (主动: 3)
2025-08-21 21:11:45.060  5998-6069  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0108","serialNo":"01610040202307060227","request_time":"1755781905055","request_id":"1755781905055C0108","version":"1","data":{"taskId":"1755580536662","taskResult":"D02","appId":"1755580536662"},"myVersionName":"1.1.03.20250821.DSPREAD.MDM.SERVICE","terminalDate":"20250821211145","org_request_id":"1755580536662ST001","org_request_time":"1755580536662"}
2025-08-21 21:11:45.062  5998-6069  WebSocket               com.dspread.mdm.service              I  🔧 任务结果实时上送: taskId=1755580536662, result=D02 (1)
2025-08-21 21:11:45.075  5998-6069  Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"msg_state":"P","request_time":"1755580524415","data":{"taskList":[{"beginDate":"2024-08-19 05:15:24","taskType":"02","apkName":"OpenVPN for Android","endDate":"9999-12-31 23:59:59","appId":"1755580524415","packName":"de.blinkt.openvpn","versionName":"0.5.36a","taskId":"1755580524415","versionCode":"65"}]},"tranCode":"ST001","request_id":"1755580524415ST001","version":"1","serialNo":"01610040202307060227"}
2025-08-21 21:11:45.077  5998-6069  WebSocket               com.dspread.mdm.service              I  🔧 准备发送响应确认 - requestId: 1755580524415ST001, needResponse: true
2025-08-21 21:11:45.084  5998-6069  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0000","serialNo":"01610040202307060227","request_time":"1755781905080","request_id":"1755781905080C0000","version":"1","org_request_id":"1755580524415ST001","org_request_time":"1755580524415","response_state":"0","myVersionName":"1.1.03.20250821.DSPREAD.MDM.SERVICE","terminalDate":"20250821211145"}
2025-08-21 21:11:45.092  5998-6069  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0000","serialNo":"01610040202307060227","request_time":"1755781905088","request_id":"1755781905088C0000","version":"1","org_request_id":"1755580524415ST001","org_request_time":"1755580524415","response_state":"0","myVersionName":"1.1.03.20250821.DSPREAD.MDM.SERVICE","terminalDate":"20250821211145"}
2025-08-21 21:11:45.094  5998-6069  WebSocket               com.dspread.mdm.service              I  🔧 已发送C0000响应确认: requestId=1755580524415ST001
2025-08-21 21:11:45.096  5998-6069  Task                    com.dspread.mdm.service              D  🔧 处理任务消息: ST001
2025-08-21 21:11:45.097  5998-6069  Task                    com.dspread.mdm.service              I  ℹ️ 更新任务列表: requestId=1755580524415ST001, 任务数量=1
2025-08-21 21:11:45.099  5998-6069  Task                    com.dspread.mdm.service              D  🔧 任务按时间插入到位置: 0, taskId=1755580524415
2025-08-21 21:11:45.101  5998-6069  Task                    com.dspread.mdm.service              D  🔧 添加/更新任务: taskId=1755580524415
2025-08-21 21:11:45.103  5998-6069  Task                    com.dspread.mdm.service              D  🔧 发现已完成任务: taskId=1755580536662, result=D02
2025-08-21 21:11:45.104  5998-6069  Task                    com.dspread.mdm.service              D  🔧 上送任务结果: taskId=1755580536662, result=D02
2025-08-21 21:11:45.106  5998-6069  Task                    com.dspread.mdm.service              D  🔧 清理已完成任务: taskId=1755580536662, result=D02
2025-08-21 21:11:45.107  5998-6069  Task                    com.dspread.mdm.service              D  🔧 清理了 1 个已完成任务
2025-08-21 21:11:45.110  5998-6069  Task                    com.dspread.mdm.service              D  🔧 保存任务列表到存储: 1 个
2025-08-21 21:11:45.111  5998-6069  Task                    com.dspread.mdm.service              D  🔧 开始处理任务列表，共 1 个任务
2025-08-21 21:11:45.113  5998-6069  Task                    com.dspread.mdm.service              D  🔧 处理任务: taskId=1755580524415, type=02, package=de.blinkt.openvpn, apk=OpenVPN for Android
2025-08-21 21:11:45.114  5998-6069  Task                    com.dspread.mdm.service              D  🔧 处理卸载任务: 1755580524415
2025-08-21 21:11:45.117  5998-6069  Task                    com.dspread.mdm.service              D  🔧 更新任务状态: taskId=1755580524415, state=D02
2025-08-21 21:11:45.119  5998-6069  Task                    com.dspread.mdm.service              D  🔧 保存任务列表到存储: 1 个
2025-08-21 21:11:45.122  5998-6069  Task                    com.dspread.mdm.service              D  🔧 保存任务列表到存储: 1 个
2025-08-21 21:11:45.123  5998-6069  WebSocket               com.dspread.mdm.service              I  🔧 ST001 主动式上送: task_failed (主动: 4)
2025-08-21 21:11:45.133  5998-6069  Common                  com.dspread.mdm.service              I  ✅ 消息发送成功: {"tranCode":"C0108","serialNo":"01610040202307060227","request_time":"1755781905127","request_id":"1755781905127C0108","version":"1","data":{"taskId":"1755580524415","taskResult":"D02","appId":"1755580524415"},"myVersionName":"1.1.03.20250821.DSPREAD.MDM.SERVICE","terminalDate":"20250821211145","org_request_id":"1755580524415ST001","org_request_time":"1755580524415"}
2025-08-21 21:11:45.135  5998-6069  WebSocket               com.dspread.mdm.service              I  🔧 任务结果实时上送: taskId=1755580524415, result=D02 (1)
2025-08-21 21:11:45.404  5998-6069  Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"request_time":"1755781906230","org_request_time":"1755781904755","org_request_id":"1755781904755C0906","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755781906230S0000","serialNo":"01610040202307060227"}
2025-08-21 21:11:45.406  5998-6069  WebSocket               com.dspread.mdm.service              I  🔧 收到响应消息: transCode=S0000, orgRequestId=1755781904755C0906, state=0, remark=
2025-08-21 21:11:45.408  5998-6069  WebSocket               com.dspread.mdm.service              I  🔧 处理设备信息响应: state=0
2025-08-21 21:11:45.410  5998-6069  WebSocket               com.dspread.mdm.service              I  🔧 设备信息上传成功
2025-08-21 21:11:45.460  5998-6069  Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"request_time":"1755781906527","org_request_time":"1755781904952","org_request_id":"1755781904952C0109","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755781906527S0000","serialNo":"01610040202307060227"}
2025-08-21 21:11:45.462  5998-6069  WebSocket               com.dspread.mdm.service              I  🔧 收到响应消息: transCode=S0000, orgRequestId=1755781904952C0109, state=0, remark=
2025-08-21 21:11:45.465  5998-6069  WebSocket               com.dspread.mdm.service              I  🔧 终端信息上传成功
2025-08-21 21:11:46.121  5998-6069  Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"request_time":"1755781906856","org_request_time":"1755781905055","org_request_id":"1755781905055C0108","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755781906856S0000","serialNo":"01610040202307060227"}
2025-08-21 21:11:46.124  5998-6069  WebSocket               com.dspread.mdm.service              I  🔧 收到响应消息: transCode=S0000, orgRequestId=1755781905055C0108, state=0, remark=
2025-08-21 21:11:46.126  5998-6069  WebSocket               com.dspread.mdm.service              I  🔧 处理任务结果响应: state=0
2025-08-21 21:11:46.128  5998-6069  WebSocket               com.dspread.mdm.service              I  🔧 任务结果上传成功
2025-08-21 21:11:46.140  5998-6069  Common                  com.dspread.mdm.service              I  ✅ 接收消息内容: {"request_time":"1755781906886","org_request_time":"1755781905127","org_request_id":"1755781905127C0108","response_state":"0","tranCode":"S0000","version":"1","request_id":"1755781906886S0000","serialNo":"01610040202307060227"}
2025-08-21 21:11:46.143  5998-6069  WebSocket               com.dspread.mdm.service              I  🔧 收到响应消息: transCode=S0000, orgRequestId=1755781905127C0108, state=0, remark=
2025-08-21 21:11:46.145  5998-6069  WebSocket               com.dspread.mdm.service              I  🔧 处理任务结果响应: state=0
2025-08-21 21:11:46.146  5998-6069  WebSocket               com.dspread.mdm.service              I  🔧 任务结果上传成功
2025-08-21 21:11:51.959  5998-5998  Receiver                com.dspread.mdm.service              D  🔧 网络恢复，重新上传网络状态信息（防抖通过）
2025-08-21 21:11:51.968  5998-5998  WebSocket               com.dspread.mdm.service              I  🔧 C0904 被动式上送拒绝: NETWORK_RECOVERY (拒绝: 1)
2025-08-21 21:11:51.973  5998-5998  WebSocket               com.dspread.mdm.service              I  🔧 C0904 网络状态上送被流量控制阻止: 被动事件 'NETWORK_RECOVERY' 在平衡模式 - 重要变化下未启用
2025-08-21 21:12:09.077  5998-6070  WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 1 (第1个，待响应: 1)
2025-08-21 21:12:09.562  5998-6069  WebSocket               com.dspread.mdm.service              I  🔧 收到服务器 PONG 帧: 1 (待响应PING: 0)
2025-08-21 21:12:39.076  5998-6070  WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 2 (第2个，待响应: 1)
2025-08-21 21:12:39.462  5998-6069  WebSocket               com.dspread.mdm.service              I  🔧 收到服务器 PONG 帧: 2 (待响应PING: 0)
2025-08-21 21:13:09.077  5998-6070  WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 3 (第3个，待响应: 1)
2025-08-21 21:13:09.568  5998-6069  WebSocket               com.dspread.mdm.service              I  🔧 收到服务器 PONG 帧: 3 (待响应PING: 0)
2025-08-21 21:13:39.078  5998-6070  WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 4 (第4个，待响应: 1)
2025-08-21 21:13:39.469  5998-6069  WebSocket               com.dspread.mdm.service              I  🔧 收到服务器 PONG 帧: 4 (待响应PING: 0)
2025-08-21 21:14:09.079  5998-6070  WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 5 (第5个，待响应: 1)
2025-08-21 21:14:09.677  5998-6069  WebSocket               com.dspread.mdm.service              I  🔧 收到服务器 PONG 帧: 5 (待响应PING: 0)
2025-08-21 21:14:39.080  5998-6070  WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 6 (第6个，待响应: 1)
2025-08-21 21:14:39.578  5998-6069  WebSocket               com.dspread.mdm.service              I  🔧 收到服务器 PONG 帧: 6 (待响应PING: 0)
2025-08-21 21:15:09.081  5998-6070  WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 7 (第7个，待响应: 1)
2025-08-21 21:15:09.581  5998-6069  WebSocket               com.dspread.mdm.service              I  🔧 收到服务器 PONG 帧: 7 (待响应PING: 0)
2025-08-21 21:15:39.082  5998-6070  WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 8 (第8个，待响应: 1)
2025-08-21 21:15:39.585  5998-6069  WebSocket               com.dspread.mdm.service              I  🔧 收到服务器 PONG 帧: 8 (待响应PING: 0)
2025-08-21 21:16:09.082  5998-6070  WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 9 (第9个，待响应: 1)
2025-08-21 21:16:09.486  5998-6069  WebSocket               com.dspread.mdm.service              I  🔧 收到服务器 PONG 帧: 9 (待响应PING: 0)
2025-08-21 21:16:39.083  5998-6070  WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 10 (第10个，待响应: 1)
2025-08-21 21:16:39.694  5998-6069  WebSocket               com.dspread.mdm.service              I  🔧 收到服务器 PONG 帧: 10 (待响应PING: 0)
2025-08-21 21:17:09.085  5998-6070  WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 11 (第11个，待响应: 1)
2025-08-21 21:17:09.492  5998-6069  WebSocket               com.dspread.mdm.service              I  🔧 收到服务器 PONG 帧: 11 (待响应PING: 0)
2025-08-21 21:17:39.085  5998-6070  WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 12 (第12个，待响应: 1)
2025-08-21 21:17:39.700  5998-6069  WebSocket               com.dspread.mdm.service              I  🔧 收到服务器 PONG 帧: 12 (待响应PING: 0)
2025-08-21 21:18:09.087  5998-6070  WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 13 (第13个，待响应: 1)
2025-08-21 21:18:09.499  5998-6069  WebSocket               com.dspread.mdm.service              I  🔧 收到服务器 PONG 帧: 13 (待响应PING: 0)
2025-08-21 21:18:39.088  5998-6070  WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 14 (第14个，待响应: 1)
2025-08-21 21:18:39.505  5998-6069  WebSocket               com.dspread.mdm.service              I  🔧 收到服务器 PONG 帧: 14 (待响应PING: 0)
2025-08-21 21:19:09.088  5998-6070  WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 15 (第15个，待响应: 1)
2025-08-21 21:19:09.711  5998-6069  WebSocket               com.dspread.mdm.service              I  🔧 收到服务器 PONG 帧: 15 (待响应PING: 0)
2025-08-21 21:19:39.090  5998-6070  WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 16 (第16个，待响应: 1)
2025-08-21 21:19:39.509  5998-6069  WebSocket               com.dspread.mdm.service              I  🔧 收到服务器 PONG 帧: 16 (待响应PING: 0)
2025-08-21 21:20:09.091  5998-6070  WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 17 (第17个，待响应: 1)
2025-08-21 21:20:09.718  5998-6069  WebSocket               com.dspread.mdm.service              I  🔧 收到服务器 PONG 帧: 17 (待响应PING: 0)
2025-08-21 21:20:39.092  5998-6070  WebSocket               com.dspread.mdm.service              I  🔧 发送客户端 PING 帧: 18 (第18个，待响应: 1)
2025-08-21 21:20:39.619  5998-6069  WebSocket               com.dspread.mdm.service              I  🔧 收到服务器 PONG 帧: 18 (待响应PING: 0)
