# 📚 任务状态机 API 参考

## 核心类和方法

### TaskStateConstants
状态常量定义类，包含所有任务状态的定义和判断方法。

#### 状态常量
```kotlin
object TaskStateConstants {
    // 基础状态
    const val TODO = "todo"
    const val WAITING = "wating"
    const val EXECUTE_WAITING = "W01"
    
    // 下载状态
    const val DOWNLOAD_ING = "A01"
    const val DOWNLOAD_SUCCESS = "A03"
    const val DOWNLOAD_FAILED = "A04"
    
    // 安装状态
    const val INSTALL_WAITING = "B01"
    const val INSTALL_ING = "B02"
    const val INSTALL_SUCCESS = "B03"
    const val INSTALL_FAILED = "B04"
    const val INSTALL_OVERRIDE = "B05"
    
    // 更新状态
    const val UPDATE_ING = "C02"
    const val UPDATE_SUCCESS = "C03"
    const val UPDATE_FAILED = "C04"
    const val UPDATE_DOWNGRADE_FORBIDDEN = "C05"
    
    // 卸载状态
    const val UNINSTALL_ING = "D01"
    const val UNINSTALL_SUCCESS = "D02"
    const val UNINSTALL_FAILED = "D03"
    const val UNINSTALL_EXPIRE = "D04"
    
    // 通用状态
    const val SUCCESSED = "F01"
    const val FAILED = "E05"
    const val TASK_CANCEL = "E01"
    const val RELY_FAILED = "E02"
    const val LOW_BAT = "E03"
    const val SERVER_FAILED = "E04"
    
    // 规则状态
    const val RULEBASED_STARTING = "R01"
    const val RULEBASED_EXECUTING = "R02"
    const val RULEBASED_SUCCESS = "R03"
    const val RULEBASED_EXPIRED = "R04"
    
    // 新格式状态
    const val RESULT_SUCCESS = "0"
    const val RESULT_FAILED = "1"
    const val RESULT_PARTIAL = "2"
}
```

#### 状态判断方法
```kotlin
/**
 * 判断是否为完成状态（成功或失败）
 * @param state 状态码
 * @return true表示已完成，false表示未完成
 */
fun isCompletedState(state: String): Boolean

/**
 * 判断是否为成功状态
 * @param state 状态码
 * @return true表示成功，false表示非成功
 */
fun isSuccessState(state: String): Boolean

/**
 * 判断是否为失败状态
 * @param state 状态码
 * @return true表示失败，false表示非失败
 */
fun isFailureState(state: String): Boolean

/**
 * 判断是否为进行中状态
 * @param state 状态码
 * @return true表示进行中，false表示非进行中
 */
fun isInProgressState(state: String): Boolean
```

### WsTaskManager
任务管理器，负责任务的状态管理、存储和生命周期控制。

#### 核心方法
```kotlin
/**
 * 更新任务状态（线程安全）
 * @param taskId 任务ID
 * @param taskState 新状态
 */
@Synchronized
fun updateWSTaskState(taskId: String, taskState: String)

/**
 * 更新任务错误信息
 * @param taskId 任务ID
 * @param errorMessage 错误信息
 */
@Synchronized
fun updateTaskErrorMessage(taskId: String, errorMessage: String)

/**
 * 更新任务最后更新时间
 * @param taskId 任务ID
 */
@Synchronized
fun updateTaskLastUpdateTime(taskId: String)

/**
 * 获取本地任务列表
 * @return JSONArray 任务列表
 */
fun getLocalTaskList(): JSONArray

/**
 * 获取下一个待执行任务
 * @return JSONObject? 任务对象，null表示无待执行任务
 */
fun getNextPendingTask(): JSONObject?

/**
 * 重置正在执行的任务状态为待执行
 */
fun resetDoingWSTaskState()

/**
 * 检查任务超时
 */
fun checkTaskTimeout()

/**
 * 保存任务列表到存储
 * @param taskList 任务列表
 */
fun saveTaskListToStorage(taskList: JSONArray)
```

### TaskHandler
任务处理器，负责具体任务类型的执行逻辑。

#### 主要方法
```kotlin
/**
 * 处理安装任务
 * @param task 任务对象
 * @param messageInfo 消息信息
 */
private fun handleInstallTask(task: JSONObject, messageInfo: MessageInfo)

/**
 * 处理卸载任务
 * @param task 任务对象
 * @param messageInfo 消息信息
 */
private fun handleUninstallTask(task: JSONObject, messageInfo: MessageInfo)

/**
 * 处理更新任务
 * @param task 任务对象
 * @param messageInfo 消息信息
 */
private fun handleUpdateTask(task: JSONObject, messageInfo: MessageInfo)

/**
 * 上传任务结果
 * @param task 任务对象
 * @param result 结果状态
 * @param errorMsg 错误信息
 * @param messageInfo 消息信息
 */
private fun uploadTaskResult(
    task: JSONObject,
    result: String,
    errorMsg: String?,
    messageInfo: MessageInfo
)
```

### WsMessageSender
消息发送器，负责任务状态的上报和消息缓存。

#### 关键方法
```kotlin
/**
 * 上传任务结果
 * @param taskId 任务ID
 * @param taskResult 任务结果
 * @param errorMsg 错误信息
 * @param appId 应用ID
 * @param orgRequestId 原始请求ID
 * @param orgRequestTime 原始请求时间
 */
fun uploadTaskResult(
    taskId: String,
    taskResult: String,
    errorMsg: String? = null,
    appId: String = "",
    orgRequestId: String = "",
    orgRequestTime: String = ""
)

/**
 * 发送任务状态更新
 * @param taskId 任务ID
 * @param state 状态
 */
fun sendTaskStateUpdate(taskId: String, state: String)
```

## 使用示例

### 基本状态更新
```kotlin
// 更新任务状态为下载中
WsTaskManager.updateWSTaskState("task123", TaskStateConstants.DOWNLOAD_ING)

// 更新任务状态为安装成功
WsTaskManager.updateWSTaskState("task123", TaskStateConstants.INSTALL_SUCCESS)

// 更新错误信息
WsTaskManager.updateTaskErrorMessage("task123", "网络连接失败")
```

### 状态判断
```kotlin
val taskResult = task.optString("taskResult")

// 检查任务是否完成
if (TaskStateConstants.isCompletedState(taskResult)) {
    Logger.com("任务已完成")
    
    // 进一步判断成功或失败
    if (TaskStateConstants.isSuccessState(taskResult)) {
        Logger.com("任务成功")
    } else if (TaskStateConstants.isFailureState(taskResult)) {
        Logger.com("任务失败")
    }
}

// 检查任务是否正在执行
if (TaskStateConstants.isInProgressState(taskResult)) {
    Logger.com("任务执行中")
}
```

### 任务结果上报
```kotlin
// 上报任务成功
WsMessageSender.uploadTaskResult(
    taskId = "task123",
    taskResult = TaskStateConstants.INSTALL_SUCCESS,
    appId = "com.example.app"
)

// 上报任务失败（带错误信息）
WsMessageSender.uploadTaskResult(
    taskId = "task123",
    taskResult = TaskStateConstants.INSTALL_FAILED,
    errorMsg = "安装包损坏",
    appId = "com.example.app"
)
```

### 任务超时处理
```kotlin
// 手动触发超时检查
WsTaskManager.checkTaskTimeout()

// 在定时器中使用
Timer().scheduleAtFixedRate(object : TimerTask() {
    override fun run() {
        WsTaskManager.checkTaskTimeout()
    }
}, 0, 10 * 60 * 1000L) // 每10分钟检查一次
```

### 服务重启后的状态重置
```kotlin
// 服务启动时调用
WsTaskManager.resetDoingWSTaskState()
```

## 注意事项

### 线程安全
- 所有状态更新方法都是线程安全的，使用了`@Synchronized`注解
- 可以在多线程环境中安全调用

### 状态一致性
- 状态更新会同时更新内存缓存和持久化存储
- 确保数据的一致性

### 错误处理
- 所有方法都包含异常处理，不会因为异常导致程序崩溃
- 错误信息会记录到日志中

### 性能考虑
- 状态更新操作会涉及I/O，建议在后台线程中调用
- 避免频繁的状态更新，可以考虑批量操作

## 调试和监控

### 日志输出
```kotlin
// 启用调试日志
Logger.setDebugMode(true)

// 查看任务状态变化
Logger.task("任务状态更新: $taskId -> $newState")
```

### 状态导出
```kotlin
// 导出所有任务状态（用于调试）
val taskList = WsTaskManager.getLocalTaskList()
Logger.com(taskList.toString(2)) // 格式化输出
```

### 统计信息
```kotlin
// 获取任务统计信息
fun getTaskStatistics(): Map<String, Any> {
    val taskList = WsTaskManager.getLocalTaskList()
    val totalTasks = taskList.length()
    var completedTasks = 0
    var failedTasks = 0
    
    for (i in 0 until taskList.length()) {
        val task = taskList.getJSONObject(i)
        val state = task.optString("taskResult")
        
        if (TaskStateConstants.isCompletedState(state)) {
            completedTasks++
            if (TaskStateConstants.isFailureState(state)) {
                failedTasks++
            }
        }
    }
    
    return mapOf(
        "totalTasks" to totalTasks,
        "completedTasks" to completedTasks,
        "failedTasks" to failedTasks,
        "successTasks" to (completedTasks - failedTasks),
        "pendingTasks" to (totalTasks - completedTasks)
    )
}
```

---

*API参考文档 - 基于service项目任务状态机实现*
*版本: 1.0*
*最后更新: 2025-08-07*
