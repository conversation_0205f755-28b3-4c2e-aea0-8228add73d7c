2025-08-18 13:11:07.984  4136-4136  WebSocket               com.dspread.mdm.service              I  🔧 重连 WebSocket: ws://***********:8080/status/websocket/register?serialNo=MDEzNTQwOTAyMDI1MDMwNTAzOTk=&publicKey=TUlHZk1BMEdDU3FHU0liM0RRRUJBUVVBQTRHTkFEQ0JpUUtCZ1FETHNEZVQyMEU0NXdBeDN1QkpkRG5wR0kxUlFVcytOa1NVNHpDSmdVaU45Yy9vdURXMlR6ZVZHVXNhaFhyeVdBVU9MUmd2UjArQld2eDJ3K2VsdmJXbEIrdXN0ZnJyU0JMY3gyZ1VrdkdIRjRhNU5sNnprcVJjZEc5bnZuRUZTclArcVR0a0J3bnhQb2FnMldydldIS1Z1eTE0bjVGVkZWWGU4czBjRmdoeTFRSURBUUFC&query=0&msgVer=3&timestamp=1755493867954&signature=VWgD7rt9Dd/ApH5xpvXBM2+XPVF9hM6CmEaMIwBeRjLvgv1c4bABTveo/VcwCUBsvLK97PcTgFK1itdmxKbSWFm+FZYxeV7QS+Pgfb6A5NljXjqA31SuzGkp/dtsHDHr36xiEZQZ6m8tMeEpgrf495oVQNXDVyOeVYUeAo5AJss=
2025-08-18 13:11:07.998  4136-4136  WebSocket               com.dspread.mdm.service              I  🔧 未检测到系统代理设置
2025-08-18 13:11:08.040  4136-4307  TrafficStats            com.dspread.mdm.service              D  tagSocket(127) with statsTag=0xffffffff, statsUid=-1
2025-08-18 13:11:08.059  4136-4306  WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误 (Ask Gemini)
                                                                                                    com.neovisionaries.ws.client.WebSocketException: Failed to connect to '***********:8080': failed to connect to /*********** (port 8080) from /:: (port 0) after 15000ms: connect failed: ENETUNREACH (Network is unreachable)
                                                                                                    	at com.neovisionaries.ws.client.SocketConnector.connectSocket(SocketConnector.java:126)
                                                                                                    	at com.neovisionaries.ws.client.SocketConnector.doConnect(SocketConnector.java:238)
                                                                                                    	at com.neovisionaries.ws.client.SocketConnector.connect(SocketConnector.java:189)
                                                                                                    	at com.neovisionaries.ws.client.WebSocket.connect(WebSocket.java:2351)
                                                                                                    	at com.neovisionaries.ws.client.ConnectThread.runMain(ConnectThread.java:32)
                                                                                                    	at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45)
                                                                                                    Caused by: java.net.ConnectException: failed to connect to /*********** (port 8080) from /:: (port 0) after 15000ms: connect failed: ENETUNREACH (Network is unreachable)
                                                                                                    	at libcore.io.IoBridge.connect(IoBridge.java:187)
                                                                                                    	at java.net.PlainSocketImpl.socketConnect(PlainSocketImpl.java:142)
                                                                                                    	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:390)
                                                                                                    	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:230)
                                                                                                    	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:212)
                                                                                                    	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:436)
                                                                                                    	at java.net.Socket.connect(Socket.java:646)
                                                                                                    	at com.neovisionaries.ws.client.SocketInitiator$SocketRacer.run(SocketInitiator.java:126)
                                                                                                    Caused by: android.system.ErrnoException: connect failed: ENETUNREACH (Network is unreachable)
                                                                                                    	at libcore.io.Linux.connect(Native Method)
                                                                                                    	at libcore.io.ForwardingOs.connect(ForwardingOs.java:201)
                                                                                                    	at libcore.io.BlockGuardOs.connect(BlockGuardOs.java:158)
                                                                                                    	at libcore.io.ForwardingOs.connect(ForwardingOs.java:201)
                                                                                                    	at libcore.io.IoBridge.connectErrno(IoBridge.java:218)
                                                                                                    	at libcore.io.IoBridge.connect(IoBridge.java:179)
                                                                                                    	at java.net.PlainSocketImpl.socketConnect(PlainSocketImpl.java:142) 
                                                                                                    	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:390) 
                                                                                                    	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:230) 
                                                                                                    	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:212) 
                                                                                                    	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:436) 
                                                                                                    	at java.net.Socket.connect(Socket.java:646) 
                                                                                                    	at com.neovisionaries.ws.client.SocketInitiator$SocketRacer.run(SocketInitiator.java:126) 
2025-08-18 13:11:08.069  4136-4306  WebSocket               com.dspread.mdm.service              I  🔧 onError: 不触发重连，等待 onDisconnected 处理
2025-08-18 13:11:08.085  4136-4306  WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误 (Ask Gemini)
                                                                                                    com.neovisionaries.ws.client.WebSocketException: Failed to connect to '***********:8080': failed to connect to /*********** (port 8080) from /:: (port 0) after 15000ms: connect failed: ENETUNREACH (Network is unreachable)
                                                                                                    	at com.neovisionaries.ws.client.SocketConnector.connectSocket(SocketConnector.java:126)
                                                                                                    	at com.neovisionaries.ws.client.SocketConnector.doConnect(SocketConnector.java:238)
                                                                                                    	at com.neovisionaries.ws.client.SocketConnector.connect(SocketConnector.java:189)
                                                                                                    	at com.neovisionaries.ws.client.WebSocket.connect(WebSocket.java:2351)
                                                                                                    	at com.neovisionaries.ws.client.ConnectThread.runMain(ConnectThread.java:32)
                                                                                                    	at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45)
                                                                                                    Caused by: java.net.ConnectException: failed to connect to /*********** (port 8080) from /:: (port 0) after 15000ms: connect failed: ENETUNREACH (Network is unreachable)
                                                                                                    	at libcore.io.IoBridge.connect(IoBridge.java:187)
                                                                                                    	at java.net.PlainSocketImpl.socketConnect(PlainSocketImpl.java:142)
                                                                                                    	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:390)
                                                                                                    	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:230)
                                                                                                    	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:212)
                                                                                                    	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:436)
                                                                                                    	at java.net.Socket.connect(Socket.java:646)
                                                                                                    	at com.neovisionaries.ws.client.SocketInitiator$SocketRacer.run(SocketInitiator.java:126)
                                                                                                    Caused by: android.system.ErrnoException: connect failed: ENETUNREACH (Network is unreachable)
                                                                                                    	at libcore.io.Linux.connect(Native Method)
                                                                                                    	at libcore.io.ForwardingOs.connect(ForwardingOs.java:201)
                                                                                                    	at libcore.io.BlockGuardOs.connect(BlockGuardOs.java:158)
                                                                                                    	at libcore.io.ForwardingOs.connect(ForwardingOs.java:201)
                                                                                                    	at libcore.io.IoBridge.connectErrno(IoBridge.java:218)
                                                                                                    	at libcore.io.IoBridge.connect(IoBridge.java:179)
                                                                                                    	at java.net.PlainSocketImpl.socketConnect(PlainSocketImpl.java:142) 
                                                                                                    	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:390) 
                                                                                                    	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:230) 
                                                                                                    	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:212) 
                                                                                                    	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:436) 
                                                                                                    	at java.net.Socket.connect(Socket.java:646) 
                                                                                                    	at com.neovisionaries.ws.client.SocketInitiator$SocketRacer.run(SocketInitiator.java:126) 
2025-08-18 13:11:08.094  4136-4306  WebSocket               com.dspread.mdm.service              I  🔧 reconnect() 被调用，调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onConnectError:675
2025-08-18 13:11:08.101  4136-4306  WebSocket               com.dspread.mdm.service              I  🔧 开始重连 WebSocket... (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onConnectError:675)
2025-08-18 13:11:08.107  4136-4306  WebSocket               com.dspread.mdm.service              I  🔧 使用Provisioning配置的重连策略: delayPolicy=30, delaySwitch=1, delayTime=900
2025-08-18 13:11:08.114  4136-4306  WebSocket               com.dspread.mdm.service              I  🔧 当前断开记录数: 0, 延迟开关: 1
2025-08-18 13:11:08.120  4136-4306  WebSocket               com.dspread.mdm.service              I  🔧 重连策略计算完成: 第6次重连，间隔12000ms (12秒)
2025-08-18 13:11:08.127  4136-4306  WebSocket               com.dspread.mdm.service              I  🔧 开始第6次重连，间隔12000ms (12秒) (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onConnectError:675)
2025-08-18 13:11:08.941  4136-4136  Common                  com.dspread.mdm.service              D  🔧 OsUpdateTest: === 执行OTA升级 ===
2025-08-18 13:11:08.956  4136-4136  Common                  com.dspread.mdm.service              D  🔧 OsUpdateTest: 开始执行OTA升级...
2025-08-18 13:11:08.981  4136-4136  Common                  com.dspread.mdm.service              D  🔧 OsUpdateTest: 注意：这将开始系统升级过程
2025-08-18 13:11:09.027  4136-4136  Common                  com.dspread.mdm.service              D  🔧 OsUpdateTest: 使用用户选择的升级包: /data/user/0/com.dspread.mdm.service/cache/BASE_D30M-MU_GEN_AP_V1.0.8.1_202507032000_ota_test.zip
2025-08-18 13:11:09.034  4136-4136  Common                  com.dspread.mdm.service              D  🔧 OsUpdateTest: 找到升级文件: /data/user/0/com.dspread.mdm.service/cache/BASE_D30M-MU_GEN_AP_V1.0.8.1_202507032000_ota_test.zip
2025-08-18 13:11:09.042  4136-4136  Common                  com.dspread.mdm.service              D  🔧 OsUpdateTest: 文件名: BASE_D30M-MU_GEN_AP_V1.0.8.1_202507032000_ota_test.zip
2025-08-18 13:11:09.050  4136-4136  Common                  com.dspread.mdm.service              D  🔧 OsUpdateTest: 文件大小: 2MB
2025-08-18 13:11:09.058  4136-4136  Common                  com.dspread.mdm.service              D  🔧 OsUpdateTest: 升级策略: A/B分区无缝升级 (UpdateEngine)
2025-08-18 13:11:09.067  4136-4136  Common                  com.dspread.mdm.service              D  🔧 OsUpdateTest: 升级进度: 准备阶段: 检查系统版本 (5%)
2025-08-18 13:11:09.074  4136-4136  Common                  com.dspread.mdm.service              D  🔧 OsUpdateTest: 当前系统版本: BASE_D30M-MU_GEN_AP_V1.0.8_202507031912
2025-08-18 13:11:09.081  4136-4136  Common                  com.dspread.mdm.service              D  🔧 OsUpdateTest: Android 14+设备，执行升级前检查...
2025-08-18 13:11:09.089  4136-4136  Common                  com.dspread.mdm.service              D  🔧 OsUpdateTest: 注意：Android 14+对升级包时间戳有严格要求
2025-08-18 13:11:09.097  4136-4136  Common                  com.dspread.mdm.service              D  🔧 OsUpdateTest: 升级进度: 准备阶段: Android 14+升级检查 (8%)
2025-08-18 13:11:09.107  4136-4136  Common                  com.dspread.mdm.service              D  🔧 OsUpdateTest: 升级进度: 准备阶段: 验证升级包 (10%)
2025-08-18 13:11:09.116  4136-4136  Common                  com.dspread.mdm.service              D  🔧 OsUpdateTest: 升级包验证通过: BASE_D30M-MU_GEN_AP_V1.0.8.1_202507032000_ota_test.zip, 大小: 2MB
2025-08-18 13:11:09.124  4136-4136  Common                  com.dspread.mdm.service              D  🔧 OsUpdateTest: 升级进度: 准备阶段: 升级包验证通过 (15%)
2025-08-18 13:11:09.131  4136-4136  Common                  com.dspread.mdm.service              D  🔧 OsUpdateTest: 升级进度: 准备阶段: 检查版本兼容性 (18%)
2025-08-18 13:11:09.142  4136-4136  Platform                com.dspread.mdm.service              I  ℹ️ SystemUpdateApi 开始分析升级包: BASE_D30M-MU_GEN_AP_V1.0.8.1_202507032000_ota_test.zip
2025-08-18 13:11:09.151  4136-4136  Platform                com.dspread.mdm.service              I  ℹ️ SystemUpdateApi 检测到A/B升级包格式
2025-08-18 13:11:09.165  4136-4136  Platform                com.dspread.mdm.service              I  ℹ️ SystemUpdateApi 从build信息提取版本: 1.0.8.1_202507032000
2025-08-18 13:11:09.174  4136-4136  Platform                com.dspread.mdm.service              I  ℹ️ SystemUpdateApi A14+格式提取版本: 1.0.8.1_202507032000
2025-08-18 13:11:09.182  4136-4136  Platform                com.dspread.mdm.service              I  ℹ️ SystemUpdateApi 版本比较: 当前=BASE_D30M-MU_GEN_AP_V1.0.8_202507031912, 升级包=1.0.8.1_202507032000
2025-08-18 13:11:09.189  4136-4136  Platform                com.dspread.mdm.service              I  ℹ️ SystemUpdateApi 详细版本比较: 'BASE_D30M-MU_GEN_AP_V1.0.8_202507031912' vs '1.0.8.1_202507032000'
2025-08-18 13:11:09.196  4136-4136  Platform                com.dspread.mdm.service              I  ℹ️ SystemUpdateApi 清理版本字符串: 'BASE_D30M-MU_GEN_AP_V1.0.8_202507031912'
2025-08-18 13:11:09.205  4136-4136  Platform                com.dspread.mdm.service              I  ℹ️ SystemUpdateApi 提取完整版本: '1.0.8_202507031912'
2025-08-18 13:11:09.212  4136-4136  Platform                com.dspread.mdm.service              I  ℹ️ SystemUpdateApi 清理版本字符串: '1.0.8.1_202507032000'
2025-08-18 13:11:09.221  4136-4136  Platform                com.dspread.mdm.service              I  ℹ️ SystemUpdateApi 提取完整版本: '1.0.8.1_202507032000'
2025-08-18 13:11:09.230  4136-4136  Platform                com.dspread.mdm.service              I  ℹ️ SystemUpdateApi 清理后版本: '1.0.8_202507031912' vs '1.0.8.1_202507032000'
2025-08-18 13:11:09.242  4136-4136  Platform                com.dspread.mdm.service              I  ℹ️ SystemUpdateApi 核心版本: '1.0.8' vs '1.0.8.1'
2025-08-18 13:11:09.255  4136-4136  Platform                com.dspread.mdm.service              I  ℹ️ SystemUpdateApi 版本数字: [1, 0, 8, 0] vs [1, 0, 8, 1, 0]
2025-08-18 13:11:09.262  4136-4136  Platform                com.dspread.mdm.service              I  ℹ️ SystemUpdateApi 版本1更旧: 0 < 1
2025-08-18 13:11:09.272  4136-4136  Common                  com.dspread.mdm.service              D  🔧 OsUpdateTest: 版本信息显示: 正常升级
2025-08-18 13:11:09.280  4136-4136  Common                  com.dspread.mdm.service              D  🔧 OsUpdateTest: 升级包版本高于当前系统版本，正常升级
2025-08-18 13:11:09.288  4136-4136  Common                  com.dspread.mdm.service              D  🔧 OsUpdateTest: 升级进度: 准备阶段: 版本检查完成 (20%)
2025-08-18 13:11:09.298  4136-4136  Common                  com.dspread.mdm.service              D  🔧 OsUpdateTest: 升级进度: 初始化: 启动升级引擎 (25%)
2025-08-18 13:11:09.306  4136-4136  Common                  com.dspread.mdm.service              D  🔧 OsUpdateTest: 开始执行OS升级，taskId: test_upgrade_1755493869294
2025-08-18 13:11:09.313  4136-4136  Platform                com.dspread.mdm.service              I  ℹ️ SystemUpdateApi 开始安装OTA更新: /data/user/0/com.dspread.mdm.service/cache/BASE_D30M-MU_GEN_AP_V1.0.8.1_202507032000_ota_test.zip
2025-08-18 13:11:09.320  4136-4136  Platform                com.dspread.mdm.service              I  ℹ️ SystemUpdateApi 执行升级前系统检查
2025-08-18 13:11:09.328  4136-4136  Platform                com.dspread.mdm.service              I  ℹ️ SystemUpdateApi Android 14+设备，检查并重置UpdateEngine状态
2025-08-18 13:11:09.335  4136-4136  Platform                com.dspread.mdm.service              I  ℹ️ SystemUpdateApi 尝试重置UpdateEngine状态
2025-08-18 13:11:09.344  4136-4136  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineManager UpdateEngine初始化成功 (Android 34)
2025-08-18 13:11:09.351  4136-4136  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineManager 尝试重置UpdateEngine状态
2025-08-18 13:11:09.371  4136-4136  Platform                com.dspread.mdm.service              W  ⚠️ UpdateEngineManager cancel方法调用失败: null
2025-08-18 13:11:10.477  4136-4136  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineManager 通过resetStatus方法重置成功
2025-08-18 13:11:10.492  4136-4136  Platform                com.dspread.mdm.service              I  ℹ️ SystemUpdateApi UpdateEngine状态重置成功
2025-08-18 13:11:11.523  4136-4136  Platform                com.dspread.mdm.service              I  ℹ️ SystemUpdateApi 升级前检查通过
2025-08-18 13:11:11.537  4136-4136  Platform                com.dspread.mdm.service              I  ℹ️ UpgradeStrategyFactory 分析升级策略...
2025-08-18 13:11:11.552  4136-4136  Platform                com.dspread.mdm.service              I  ℹ️ UpgradeStrategyFactory Android版本: 34 (14)
2025-08-18 13:11:11.567  4136-4136  Platform                com.dspread.mdm.service              I  ℹ️ UpgradeStrategyFactory 更新文件: BASE_D30M-MU_GEN_AP_V1.0.8.1_202507032000_ota_test.zip
2025-08-18 13:11:11.591  4136-4136  Platform                com.dspread.mdm.service              I  ℹ️ UpgradeStrategyFactory 文件格式 - Payload: true, ZIP: true
2025-08-18 13:11:11.607  4136-4136  Platform                com.dspread.mdm.service              I  ℹ️ UpgradeStrategyFactory 选择升级策略: UpdateEngine (Android 14+)
2025-08-18 13:11:11.619  4136-4136  Platform                com.dspread.mdm.service              I  ℹ️ UpgradeStrategyFactory 策略描述: Android 14+ 使用UpdateEngine进行A/B分区升级
2025-08-18 13:11:11.634  4136-4136  Platform                com.dspread.mdm.service              I  ℹ️ UpgradeStrategyFactory 支持状态: true
2025-08-18 13:11:11.647  4136-4136  Platform                com.dspread.mdm.service              I  ℹ️ UpgradeStrategyFactory 选择原因: Android 14+ 强制要求使用UpdateEngine
2025-08-18 13:11:11.661  4136-4136  Platform                com.dspread.mdm.service              I  ℹ️ UpgradeStrategyFactory 使用UpdateEngine策略执行升级
2025-08-18 13:11:11.677  4136-4136  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineUpgradeStrategy 开始UpdateEngine升级: /data/user/0/com.dspread.mdm.service/cache/BASE_D30M-MU_GEN_AP_V1.0.8.1_202507032000_ota_test.zip
2025-08-18 13:11:11.692  4136-4136  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineUpgradeStrategy Android版本: 34 (14)
2025-08-18 13:11:11.715  4136-4136  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineUpgradeStrategy OTA包格式检查: Payload格式
2025-08-18 13:11:11.730  4136-4136  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineUpgradeStrategy 检测到ZIP格式，提取payload.bin
2025-08-18 13:11:11.857  4136-4136  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineUpgradeStrategy 成功提取payload.bin: /data/user/0/com.dspread.mdm.service/cache/ota_extract/payload.bin
2025-08-18 13:11:11.875  4136-4136  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineUpgradeStrategy 成功提取payload_properties.txt: /data/user/0/com.dspread.mdm.service/cache/ota_extract/payload_properties.txt
2025-08-18 13:11:12.051  4136-4136  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineUpgradeStrategy 文件复制成功: /data/ota_package/payload.bin
2025-08-18 13:11:12.059  4136-4136  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineManager 开始应用UpdateEngine payload
2025-08-18 13:11:12.066  4136-4136  Platform                com.dspread.mdm.service              W  ⚠️ UpdateEngineManager UpdateEngineCallback回调异常，启动增强监控
2025-08-18 13:11:12.073  4136-4136  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineManager 启动UpdateEngine增强监控
2025-08-18 13:11:12.081  4136-4136  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineManager 启动系统属性监控
2025-08-18 13:11:12.089  4136-4136  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineManager 启动文件系统监控
2025-08-18 13:11:12.098  4136-4317  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineManager 检测到文件系统活动: /data/ota_package
2025-08-18 13:11:12.098  4136-4136  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineManager 开始读取payload参数，文件: /data/ota_package/payload.bin
2025-08-18 13:11:12.105  4136-4136  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineManager 查找payload_properties.txt:
2025-08-18 13:11:12.100  4136-4136  Thread-15               com.dspread.mdm.service              W  type=1400 audit(0.0:186): avc:  denied  { getattr } for  path="/data/cache/recovery" dev="mmcblk0p47" ino=193 scontext=u:r:system_app:s0 tcontext=u:object_r:cache_recovery_file:s0 tclass=dir permissive=0
2025-08-18 13:11:12.100  4136-4136  Thread-15               com.dspread.mdm.service              W  type=1400 audit(0.0:187): avc:  denied  { getattr } for  path="/dev/block/platform/bootdevice/by-name" dev="tmpfs" ino=7267 scontext=u:r:system_app:s0 tcontext=u:object_r:block_device:s0 tclass=dir permissive=0
2025-08-18 13:11:12.113  4136-4136  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineManager   payload文件: /data/ota_package/payload.bin
2025-08-18 13:11:12.121  4136-4136  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineManager   查找目录: /data/ota_package
2025-08-18 13:11:12.128  4136-4136  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineManager   properties文件: /data/ota_package/payload_properties.txt
2025-08-18 13:11:12.135  4136-4136  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineManager   文件存在: false
2025-08-18 13:11:12.143  4136-4136  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineManager 尝试缓存目录:
2025-08-18 13:11:12.151  4136-4136  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineManager   缓存目录: /data/user/0/com.dspread.mdm.service/cache/ota_extract
2025-08-18 13:11:12.158  4136-4136  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineManager   缓存properties文件: /data/user/0/com.dspread.mdm.service/cache/ota_extract/payload_properties.txt
2025-08-18 13:11:12.166  4136-4136  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineManager   缓存文件存在: true
2025-08-18 13:11:12.173  4136-4136  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineManager 找到payload_properties.txt: /data/user/0/com.dspread.mdm.service/cache/ota_extract/payload_properties.txt
2025-08-18 13:11:12.183  4136-4136  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineManager payload_properties.txt内容:
2025-08-18 13:11:12.197  4136-4136  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineManager   FILE_HASH=x/53I9LRkm74Z6EgFM4uEbarni/VIyXAskIT6jq6ReI=
2025-08-18 13:11:12.204  4136-4136  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineManager   FILE_SIZE=2812788
2025-08-18 13:11:12.212  4136-4136  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineManager   METADATA_HASH=PfJ/2VVj97IDNB3F0lTB3qqAjJHzWVRB19Eb9jMR/xQ=
2025-08-18 13:11:12.220  4136-4136  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineManager   METADATA_SIZE=71613
2025-08-18 13:11:12.235  4136-4136  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineManager 解析得到的参数:
2025-08-18 13:11:12.344  4136-4136  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineManager   FILE_HASH=x/53I9LRkm74Z6EgFM4uEbarni/VIyXAskIT6jq6ReI=
2025-08-18 13:11:12.350  4136-4136  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineManager   FILE_SIZE=2812788
2025-08-18 13:11:12.356  4136-4136  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineManager   METADATA_HASH=PfJ/2VVj97IDNB3F0lTB3qqAjJHzWVRB19Eb9jMR/xQ=
2025-08-18 13:11:12.362  4136-4136  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineManager   METADATA_SIZE=71613
2025-08-18 13:11:12.367  4136-4136  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineManager   SWITCH_SLOT_ON_REBOOT=1
2025-08-18 13:11:12.373  4136-4136  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineManager 从payload_properties.txt读取参数完成
2025-08-18 13:11:12.379  4136-4136  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineManager 最终headerKeyValuePairs:
2025-08-18 13:11:12.385  4136-4136  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineManager   [0] FILE_HASH=x/53I9LRkm74Z6EgFM4uEbarni/VIyXAskIT6jq6ReI=
2025-08-18 13:11:12.391  4136-4136  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineManager   [1] FILE_SIZE=2812788
2025-08-18 13:11:12.397  4136-4136  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineManager   [2] METADATA_HASH=PfJ/2VVj97IDNB3F0lTB3qqAjJHzWVRB19Eb9jMR/xQ=
2025-08-18 13:11:12.403  4136-4136  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineManager   [3] METADATA_SIZE=71613
2025-08-18 13:11:12.409  4136-4136  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineManager   [4] SWITCH_SLOT_ON_REBOOT=1
2025-08-18 13:11:12.416  4136-4136  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineManager UpdateEngine参数:
2025-08-18 13:11:12.421  4136-4136  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineManager   URL: file:///data/ota_package/payload.bin
2025-08-18 13:11:12.428  4136-4136  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineManager   Size: 2812788 bytes
2025-08-18 13:11:12.435  4136-4136  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineManager   Headers: FILE_HASH=x/53I9LRkm74Z6EgFM4uEbarni/VIyXAskIT6jq6ReI=, FILE_SIZE=2812788, METADATA_HASH=PfJ/2VVj97IDNB3F0lTB3qqAjJHzWVRB19Eb9jMR/xQ=, METADATA_SIZE=71613, SWITCH_SLOT_ON_REBOOT=1
2025-08-18 13:11:12.537  4136-4136  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineManager UpdateEngine payload应用成功
2025-08-18 13:11:12.542  4136-4136  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineUpgradeStrategy UpdateEngine升级已启动
2025-08-18 13:11:12.548  4136-4136  Platform                com.dspread.mdm.service              I  ℹ️ SystemUpdateApi 升级执行结果: 成功
2025-08-18 13:11:12.554  4136-4136  Common                  com.dspread.mdm.service              D  🔧 OsUpdateTest: 升级启动成功，taskId: test_upgrade_1755493869294
2025-08-18 13:11:12.560  4136-4136  Common                  com.dspread.mdm.service              D  🔧 OsUpdateTest: 升级进度: 初始化: 升级引擎已启动 (30%)
2025-08-18 13:11:12.579  4136-4136  Choreographer           com.dspread.mdm.service              I  Skipped 218 frames!  The application may be doing too much work on its main thread.
2025-08-18 13:11:12.726  4136-4179  OpenGLRenderer          com.dspread.mdm.service              I  Davey! duration=3783ms; Flags=0, FrameTimelineVsyncId=31269, IntendedVsync=223324357475, Vsync=226957690881, InputEventId=0, HandleInputStart=226964881242, AnimationStart=226964899319, PerformTraversalsStart=226966077242, DrawStart=226992157858, FrameDeadline=227056775198, FrameInterval=226963777781, FrameStartTime=16697850, SyncQueued=227012185781, SyncStart=227012257473, IssueDrawCommandsStart=227012663627, SwapBuffers=227105453165, FrameCompleted=227107767088, DequeueBufferDuration=52615, QueueBufferDuration=557615, GpuCompleted=227107767088, SwapBuffersCompleted=227106941242, DisplayPresentTime=-2, CommandSubmissionCompleted=227105453165, 
2025-08-18 13:11:14.102  4136-4318  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineManager 启动改进的日志监控
2025-08-18 13:11:14.124  4136-4334  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineManager 日志监控开始时间: 08-18 13:11:14.111
2025-08-18 13:11:14.137  4136-4334  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineManager 开始监控UpdateEngine日志
2025-08-18 13:11:14.174  4136-4334  Platform                com.dspread.mdm.service              D  🔧 UpdateEngineManager 解析时间戳: 08-18 13:11:09.360 -> 1755493869360
2025-08-18 13:11:14.181  4136-4334  Platform                com.dspread.mdm.service              D  🔧 UpdateEngineManager 忽略历史错误日志: 08-18 13:11:09.360 E/update_engine(  950): [ERROR:update_attempter_android.cc(-1)] unknown(...): Domain=update_engine, Code=generic_error, Message=No ongoing update to cancel.
2025-08-18 13:11:14.199  4136-4334  Platform                com.dspread.mdm.service              D  🔧 UpdateEngineManager 解析时间戳: 08-18 13:11:09.365 -> 1755493869365
2025-08-18 13:11:14.206  4136-4334  Platform                com.dspread.mdm.service              D  🔧 UpdateEngineManager 忽略历史错误日志: 08-18 13:11:09.365 E/update_engine(  950): [ERROR:update_attempter_android.cc(94)] Replying with failure: pc:0x1437231: No ongoing update to cancel.
2025-08-18 13:11:14.230  4136-4334  Platform                com.dspread.mdm.service              D  🔧 UpdateEngineManager 解析时间戳: 08-18 13:11:10.466 -> 1755493870466
2025-08-18 13:11:14.236  4136-4334  Platform                com.dspread.mdm.service              D  🔧 UpdateEngineManager 忽略历史错误日志: 08-18 13:11:10.466 E/update_engine(  950): [ERROR:snapshot.cpp(2929)] Read state file failed: No such file or directory
2025-08-18 13:11:14.256  4136-4334  Platform                com.dspread.mdm.service              D  🔧 UpdateEngineManager 解析时间戳: 08-18 13:11:12.483 -> 1755493872483
2025-08-18 13:11:14.261  4136-4334  Platform                com.dspread.mdm.service              D  🔧 UpdateEngineManager 忽略历史错误日志: 08-18 13:11:12.483 E/update_engine(  950): [ERROR:snapshot.cpp(2929)] Read state file failed: No such file or directory
2025-08-18 13:11:14.302  4136-4334  Platform                com.dspread.mdm.service              D  🔧 UpdateEngineManager 解析时间戳: 08-18 13:11:12.636 -> 1755493872636
2025-08-18 13:11:14.307  4136-4334  Platform                com.dspread.mdm.service              D  🔧 UpdateEngineManager 忽略历史错误日志: 08-18 13:11:12.636 E/update_engine(  950): [ERROR:snapshot.cpp(2929)] Read state file failed: No such file or directory
2025-08-18 13:11:14.321  4136-4334  Platform                com.dspread.mdm.service              D  🔧 UpdateEngineManager 解析时间戳: 08-18 13:11:12.643 -> 1755493872643
2025-08-18 13:11:14.327  4136-4334  Platform                com.dspread.mdm.service              D  🔧 UpdateEngineManager 忽略历史错误日志: 08-18 13:11:12.643 E/update_engine(  950): [ERROR:snapshot.cpp(2929)] Read state file failed: No such file or directory
2025-08-18 13:11:14.342  4136-4334  Platform                com.dspread.mdm.service              D  🔧 UpdateEngineManager 解析时间戳: 08-18 13:11:12.674 -> 1755493872674
2025-08-18 13:11:14.348  4136-4334  Platform                com.dspread.mdm.service              D  🔧 UpdateEngineManager 忽略历史错误日志: 08-18 13:11:12.674 E/update_engine(  950): [ERROR:snapshot.cpp(2929)] Read state file failed: No such file or directory
2025-08-18 13:11:14.366  4136-4334  Platform                com.dspread.mdm.service              D  🔧 UpdateEngineManager 解析时间戳: 08-18 13:11:12.720 -> 1755493872720
2025-08-18 13:11:14.371  4136-4334  Platform                com.dspread.mdm.service              D  🔧 UpdateEngineManager 忽略历史错误日志: 08-18 13:11:12.720 E/update_engine(  950): [ERROR:snapshot.cpp(2929)] Read state file failed: No such file or directory
2025-08-18 13:11:14.433  4136-4334  Platform                com.dspread.mdm.service              D  🔧 UpdateEngineManager 解析时间戳: 08-18 13:11:12.973 -> 1755493872973
2025-08-18 13:11:14.439  4136-4334  Platform                com.dspread.mdm.service              D  🔧 UpdateEngineManager 忽略历史错误日志: 08-18 13:11:12.973 E/update_engine(  950): [ERROR:snapshot.cpp(2929)] Read state file failed: No such file or directory
2025-08-18 13:11:14.455  4136-4334  Platform                com.dspread.mdm.service              D  🔧 UpdateEngineManager 解析时间戳: 08-18 13:11:13.988 -> 1755493873988
2025-08-18 13:11:14.461  4136-4334  Platform                com.dspread.mdm.service              D  🔧 UpdateEngineManager 忽略历史错误日志: 08-18 13:11:13.988 E/update_engine(  950): [ERROR:snapshot.cpp(2929)] Read state file failed: No such file or directory
2025-08-18 13:11:15.628  4136-4334  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineManager 从日志解析进度: 10%
2025-08-18 13:11:15.641  4136-4136  Common                  com.dspread.mdm.service              D  🔧 OsUpdateTest: 升级进度: 下载阶段: DOWNLOADING (10%)
2025-08-18 13:11:15.642  4136-4334  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineManager 从日志解析进度: 20%
2025-08-18 13:11:15.647  4136-4136  Common                  com.dspread.mdm.service              D  🔧 OsUpdateTest: 升级进度更新: 下载阶段 - DOWNLOADING (10%)
2025-08-18 13:11:15.652  4136-4334  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineManager 从日志解析进度: 30%
2025-08-18 13:11:15.655  4136-4136  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineUpgradeStrategy UpdateEngine状态: DOWNLOADING (3), 进度: 10%
2025-08-18 13:11:15.665  4136-4334  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineManager 从日志解析进度: 40%
2025-08-18 13:11:15.676  4136-4136  Common                  com.dspread.mdm.service              D  🔧 OsUpdateTest: 升级进度: 校验阶段: VERIFYING (20%)
2025-08-18 13:11:15.683  4136-4136  Common                  com.dspread.mdm.service              D  🔧 OsUpdateTest: 升级进度更新: 校验阶段 - VERIFYING (20%)
2025-08-18 13:11:15.688  4136-4136  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineUpgradeStrategy UpdateEngine状态: VERIFYING (4), 进度: 20%
2025-08-18 13:11:15.697  4136-4136  Common                  com.dspread.mdm.service              D  🔧 OsUpdateTest: 升级进度: 校验阶段: VERIFYING (30%)
2025-08-18 13:11:15.703  4136-4136  Common                  com.dspread.mdm.service              D  🔧 OsUpdateTest: 升级进度更新: 校验阶段 - VERIFYING (30%)
2025-08-18 13:11:15.708  4136-4136  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineUpgradeStrategy UpdateEngine状态: VERIFYING (4), 进度: 30%
2025-08-18 13:11:15.722  4136-4136  Common                  com.dspread.mdm.service              D  🔧 OsUpdateTest: 升级进度: 校验阶段: VERIFYING (40%)
2025-08-18 13:11:15.728  4136-4136  Common                  com.dspread.mdm.service              D  🔧 OsUpdateTest: 升级进度更新: 校验阶段 - VERIFYING (40%)
2025-08-18 13:11:15.734  4136-4136  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineUpgradeStrategy UpdateEngine状态: VERIFYING (4), 进度: 40%
2025-08-18 13:11:17.100  4136-4136  Thread-15               com.dspread.mdm.service              W  type=1400 audit(0.0:188): avc:  denied  { getattr } for  path="/data/cache/recovery" dev="mmcblk0p47" ino=193 scontext=u:r:system_app:s0 tcontext=u:object_r:cache_recovery_file:s0 tclass=dir permissive=0
2025-08-18 13:11:17.110  4136-4317  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineManager 检测到文件系统活动: /data/ota_package
2025-08-18 13:11:17.100  4136-4136  Thread-15               com.dspread.mdm.service              W  type=1400 audit(0.0:189): avc:  denied  { getattr } for  path="/dev/block/platform/bootdevice/by-name" dev="tmpfs" ino=7267 scontext=u:r:system_app:s0 tcontext=u:object_r:block_device:s0 tclass=dir permissive=0
2025-08-18 13:11:19.760  4136-4334  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineManager 从日志解析进度: 50%
2025-08-18 13:11:19.774  4136-4136  Common                  com.dspread.mdm.service              D  🔧 OsUpdateTest: 升级进度: 校验阶段: VERIFYING (50%)
2025-08-18 13:11:19.780  4136-4136  Common                  com.dspread.mdm.service              D  🔧 OsUpdateTest: 升级进度更新: 校验阶段 - VERIFYING (50%)
2025-08-18 13:11:19.787  4136-4136  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineUpgradeStrategy UpdateEngine状态: VERIFYING (4), 进度: 50%
2025-08-18 13:11:20.153  4136-4136  WebSocket               com.dspread.mdm.service              I  🔧 重连 WebSocket: ws://***********:8080/status/websocket/register?serialNo=MDEzNTQwOTAyMDI1MDMwNTAzOTk=&publicKey=TUlHZk1BMEdDU3FHU0liM0RRRUJBUVVBQTRHTkFEQ0JpUUtCZ1FETHNEZVQyMEU0NXdBeDN1QkpkRG5wR0kxUlFVcytOa1NVNHpDSmdVaU45Yy9vdURXMlR6ZVZHVXNhaFhyeVdBVU9MUmd2UjArQld2eDJ3K2VsdmJXbEIrdXN0ZnJyU0JMY3gyZ1VrdkdIRjRhNU5sNnprcVJjZEc5bnZuRUZTclArcVR0a0J3bnhQb2FnMldydldIS1Z1eTE0bjVGVkZWWGU4czBjRmdoeTFRSURBUUFC&query=0&msgVer=3&timestamp=1755493880139&signature=KRx5RqLywg6o03jg153lAg7AQgfkv1I9fzGSXPAlZoxPW4vi1KOaByPBJGsPHvHDv5rbGLA3v2FqoOvSB4O1dDQHbaSMT2vMX6svzurb0duKz3HkAaw9gRkkt7iTTyJoxWCgC7gD4mjdjt7p0VqJO1h23Fo4jrZqnUy3NfMCpVE=
2025-08-18 13:11:20.160  4136-4136  WebSocket               com.dspread.mdm.service              I  🔧 未检测到系统代理设置
2025-08-18 13:11:20.183  4136-4381  TrafficStats            com.dspread.mdm.service              D  tagSocket(114) with statsTag=0xffffffff, statsUid=-1
2025-08-18 13:11:20.198  4136-4380  WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误 (Ask Gemini)
                                                                                                    com.neovisionaries.ws.client.WebSocketException: Failed to connect to '***********:8080': failed to connect to /*********** (port 8080) from /:: (port 0) after 15000ms: connect failed: ENETUNREACH (Network is unreachable)
                                                                                                    	at com.neovisionaries.ws.client.SocketConnector.connectSocket(SocketConnector.java:126)
                                                                                                    	at com.neovisionaries.ws.client.SocketConnector.doConnect(SocketConnector.java:238)
                                                                                                    	at com.neovisionaries.ws.client.SocketConnector.connect(SocketConnector.java:189)
                                                                                                    	at com.neovisionaries.ws.client.WebSocket.connect(WebSocket.java:2351)
                                                                                                    	at com.neovisionaries.ws.client.ConnectThread.runMain(ConnectThread.java:32)
                                                                                                    	at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45)
                                                                                                    Caused by: java.net.ConnectException: failed to connect to /*********** (port 8080) from /:: (port 0) after 15000ms: connect failed: ENETUNREACH (Network is unreachable)
                                                                                                    	at libcore.io.IoBridge.connect(IoBridge.java:187)
                                                                                                    	at java.net.PlainSocketImpl.socketConnect(PlainSocketImpl.java:142)
                                                                                                    	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:390)
                                                                                                    	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:230)
                                                                                                    	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:212)
                                                                                                    	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:436)
                                                                                                    	at java.net.Socket.connect(Socket.java:646)
                                                                                                    	at com.neovisionaries.ws.client.SocketInitiator$SocketRacer.run(SocketInitiator.java:126)
                                                                                                    Caused by: android.system.ErrnoException: connect failed: ENETUNREACH (Network is unreachable)
                                                                                                    	at libcore.io.Linux.connect(Native Method)
                                                                                                    	at libcore.io.ForwardingOs.connect(ForwardingOs.java:201)
                                                                                                    	at libcore.io.BlockGuardOs.connect(BlockGuardOs.java:158)
                                                                                                    	at libcore.io.ForwardingOs.connect(ForwardingOs.java:201)
                                                                                                    	at libcore.io.IoBridge.connectErrno(IoBridge.java:218)
                                                                                                    	at libcore.io.IoBridge.connect(IoBridge.java:179)
                                                                                                    	at java.net.PlainSocketImpl.socketConnect(PlainSocketImpl.java:142) 
                                                                                                    	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:390) 
                                                                                                    	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:230) 
                                                                                                    	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:212) 
                                                                                                    	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:436) 
                                                                                                    	at java.net.Socket.connect(Socket.java:646) 
                                                                                                    	at com.neovisionaries.ws.client.SocketInitiator$SocketRacer.run(SocketInitiator.java:126) 
2025-08-18 13:11:20.205  4136-4380  WebSocket               com.dspread.mdm.service              I  🔧 onError: 不触发重连，等待 onDisconnected 处理
2025-08-18 13:11:20.219  4136-4380  WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误 (Ask Gemini)
                                                                                                    com.neovisionaries.ws.client.WebSocketException: Failed to connect to '***********:8080': failed to connect to /*********** (port 8080) from /:: (port 0) after 15000ms: connect failed: ENETUNREACH (Network is unreachable)
                                                                                                    	at com.neovisionaries.ws.client.SocketConnector.connectSocket(SocketConnector.java:126)
                                                                                                    	at com.neovisionaries.ws.client.SocketConnector.doConnect(SocketConnector.java:238)
                                                                                                    	at com.neovisionaries.ws.client.SocketConnector.connect(SocketConnector.java:189)
                                                                                                    	at com.neovisionaries.ws.client.WebSocket.connect(WebSocket.java:2351)
                                                                                                    	at com.neovisionaries.ws.client.ConnectThread.runMain(ConnectThread.java:32)
                                                                                                    	at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45)
                                                                                                    Caused by: java.net.ConnectException: failed to connect to /*********** (port 8080) from /:: (port 0) after 15000ms: connect failed: ENETUNREACH (Network is unreachable)
                                                                                                    	at libcore.io.IoBridge.connect(IoBridge.java:187)
                                                                                                    	at java.net.PlainSocketImpl.socketConnect(PlainSocketImpl.java:142)
                                                                                                    	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:390)
                                                                                                    	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:230)
                                                                                                    	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:212)
                                                                                                    	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:436)
                                                                                                    	at java.net.Socket.connect(Socket.java:646)
                                                                                                    	at com.neovisionaries.ws.client.SocketInitiator$SocketRacer.run(SocketInitiator.java:126)
                                                                                                    Caused by: android.system.ErrnoException: connect failed: ENETUNREACH (Network is unreachable)
                                                                                                    	at libcore.io.Linux.connect(Native Method)
                                                                                                    	at libcore.io.ForwardingOs.connect(ForwardingOs.java:201)
                                                                                                    	at libcore.io.BlockGuardOs.connect(BlockGuardOs.java:158)
                                                                                                    	at libcore.io.ForwardingOs.connect(ForwardingOs.java:201)
                                                                                                    	at libcore.io.IoBridge.connectErrno(IoBridge.java:218)
                                                                                                    	at libcore.io.IoBridge.connect(IoBridge.java:179)
                                                                                                    	at java.net.PlainSocketImpl.socketConnect(PlainSocketImpl.java:142) 
                                                                                                    	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:390) 
                                                                                                    	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:230) 
                                                                                                    	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:212) 
                                                                                                    	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:436) 
                                                                                                    	at java.net.Socket.connect(Socket.java:646) 
                                                                                                    	at com.neovisionaries.ws.client.SocketInitiator$SocketRacer.run(SocketInitiator.java:126) 
2025-08-18 13:11:20.226  4136-4380  WebSocket               com.dspread.mdm.service              I  🔧 reconnect() 被调用，调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onConnectError:675
2025-08-18 13:11:20.232  4136-4380  WebSocket               com.dspread.mdm.service              I  🔧 开始重连 WebSocket... (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onConnectError:675)
2025-08-18 13:11:20.238  4136-4380  WebSocket               com.dspread.mdm.service              I  🔧 使用Provisioning配置的重连策略: delayPolicy=30, delaySwitch=1, delayTime=900
2025-08-18 13:11:20.244  4136-4380  WebSocket               com.dspread.mdm.service              I  🔧 当前断开记录数: 0, 延迟开关: 1
2025-08-18 13:11:20.250  4136-4380  WebSocket               com.dspread.mdm.service              I  🔧 重连策略计算完成: 第7次重连，间隔15000ms (15秒)
2025-08-18 13:11:20.256  4136-4380  WebSocket               com.dspread.mdm.service              I  🔧 开始第7次重连，间隔15000ms (15秒) (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onConnectError:675)
2025-08-18 13:11:21.192  4136-4334  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineManager 从日志解析进度: 60%
2025-08-18 13:11:21.201  4136-4136  Common                  com.dspread.mdm.service              D  🔧 OsUpdateTest: 升级进度: 校验阶段: VERIFYING (60%)
2025-08-18 13:11:21.206  4136-4136  Common                  com.dspread.mdm.service              D  🔧 OsUpdateTest: 升级进度更新: 校验阶段 - VERIFYING (60%)
2025-08-18 13:11:21.212  4136-4136  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineUpgradeStrategy UpdateEngine状态: VERIFYING (4), 进度: 60%
2025-08-18 13:11:22.110  4136-4136  Thread-15               com.dspread.mdm.service              W  type=1400 audit(0.0:190): avc:  denied  { getattr } for  path="/data/cache/recovery" dev="mmcblk0p47" ino=193 scontext=u:r:system_app:s0 tcontext=u:object_r:cache_recovery_file:s0 tclass=dir permissive=0
2025-08-18 13:11:22.116  4136-4317  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineManager 检测到文件系统活动: /data/ota_package
2025-08-18 13:11:22.110  4136-4136  Thread-15               com.dspread.mdm.service              W  type=1400 audit(0.0:191): avc:  denied  { getattr } for  path="/dev/block/platform/bootdevice/by-name" dev="tmpfs" ino=7267 scontext=u:r:system_app:s0 tcontext=u:object_r:block_device:s0 tclass=dir permissive=0
2025-08-18 13:11:24.597  4136-4334  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineManager 从日志解析进度: 70%
2025-08-18 13:11:24.607  4136-4136  Common                  com.dspread.mdm.service              D  🔧 OsUpdateTest: 升级进度: 校验阶段: VERIFYING (70%)
2025-08-18 13:11:24.613  4136-4136  Common                  com.dspread.mdm.service              D  🔧 OsUpdateTest: 升级进度更新: 校验阶段 - VERIFYING (70%)
2025-08-18 13:11:24.618  4136-4136  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineUpgradeStrategy UpdateEngine状态: VERIFYING (4), 进度: 70%
2025-08-18 13:11:27.123  4136-4317  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineManager 检测到文件系统活动: /data/ota_package
2025-08-18 13:11:27.120  4136-4136  Thread-15               com.dspread.mdm.service              W  type=1400 audit(0.0:192): avc:  denied  { getattr } for  path="/data/cache/recovery" dev="mmcblk0p47" ino=193 scontext=u:r:system_app:s0 tcontext=u:object_r:cache_recovery_file:s0 tclass=dir permissive=0
2025-08-18 13:11:27.120  4136-4136  Thread-15               com.dspread.mdm.service              W  type=1400 audit(0.0:193): avc:  denied  { getattr } for  path="/dev/block/platform/bootdevice/by-name" dev="tmpfs" ino=7267 scontext=u:r:system_app:s0 tcontext=u:object_r:block_device:s0 tclass=dir permissive=0
2025-08-18 13:11:27.727  4136-4136  Receiver                com.dspread.mdm.service              D  🔧 电池状态更新: 电池状态: 电量=73%, 温度=28.5°C, 健康=2, 充电=是, 低电量=否, 耗尽=否
2025-08-18 13:11:28.747  4136-4334  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineManager 从日志解析进度: 80%
2025-08-18 13:11:28.760  4136-4136  Common                  com.dspread.mdm.service              D  🔧 OsUpdateTest: 升级进度: 完成阶段: FINALIZING (80%)
2025-08-18 13:11:28.766  4136-4136  Common                  com.dspread.mdm.service              D  🔧 OsUpdateTest: 升级进度更新: 完成阶段 - FINALIZING (80%)
2025-08-18 13:11:28.771  4136-4136  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineUpgradeStrategy UpdateEngine状态: FINALIZING (5), 进度: 80%
2025-08-18 13:11:32.131  4136-4317  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineManager 检测到文件系统活动: /data/ota_package
2025-08-18 13:11:32.130  4136-4136  Thread-15               com.dspread.mdm.service              W  type=1400 audit(0.0:194): avc:  denied  { getattr } for  path="/data/cache/recovery" dev="mmcblk0p47" ino=193 scontext=u:r:system_app:s0 tcontext=u:object_r:cache_recovery_file:s0 tclass=dir permissive=0
2025-08-18 13:11:32.130  4136-4136  Thread-15               com.dspread.mdm.service              W  type=1400 audit(0.0:195): avc:  denied  { getattr } for  path="/dev/block/platform/bootdevice/by-name" dev="tmpfs" ino=7267 scontext=u:r:system_app:s0 tcontext=u:object_r:block_device:s0 tclass=dir permissive=0
2025-08-18 13:11:32.834  4136-4334  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineManager 从日志解析进度: 90%
2025-08-18 13:11:32.845  4136-4136  Common                  com.dspread.mdm.service              D  🔧 OsUpdateTest: 升级进度: 完成阶段: FINALIZING (90%)
2025-08-18 13:11:32.851  4136-4136  Common                  com.dspread.mdm.service              D  🔧 OsUpdateTest: 升级进度更新: 完成阶段 - FINALIZING (90%)
2025-08-18 13:11:32.857  4136-4136  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineUpgradeStrategy UpdateEngine状态: FINALIZING (5), 进度: 90%
2025-08-18 13:11:35.277  4136-4136  WebSocket               com.dspread.mdm.service              I  🔧 重连 WebSocket: ws://***********:8080/status/websocket/register?serialNo=MDEzNTQwOTAyMDI1MDMwNTAzOTk=&publicKey=TUlHZk1BMEdDU3FHU0liM0RRRUJBUVVBQTRHTkFEQ0JpUUtCZ1FETHNEZVQyMEU0NXdBeDN1QkpkRG5wR0kxUlFVcytOa1NVNHpDSmdVaU45Yy9vdURXMlR6ZVZHVXNhaFhyeVdBVU9MUmd2UjArQld2eDJ3K2VsdmJXbEIrdXN0ZnJyU0JMY3gyZ1VrdkdIRjRhNU5sNnprcVJjZEc5bnZuRUZTclArcVR0a0J3bnhQb2FnMldydldIS1Z1eTE0bjVGVkZWWGU4czBjRmdoeTFRSURBUUFC&query=0&msgVer=3&timestamp=1755493895264&signature=KNq/SZ6cdwp0jj6+oXcMJbYtVvVebuFuechhBXNpZGtVWmtlt6hvP7nosbVveCWIW1+rs+Q/BtUHwa/o2x0Z/pNIn5nvoyNNkNvgeco0DQ0LNbBqvCj785JDIWluokuqJrtY3KRetrhqPHjXre3+hCXfWZGDhZ1XiG+fbGXgOTA=
2025-08-18 13:11:35.283  4136-4136  WebSocket               com.dspread.mdm.service              I  🔧 未检测到系统代理设置
2025-08-18 13:11:35.303  4136-4426  TrafficStats            com.dspread.mdm.service              D  tagSocket(114) with statsTag=0xffffffff, statsUid=-1
2025-08-18 13:11:35.317  4136-4425  WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误 (Ask Gemini)
                                                                                                    com.neovisionaries.ws.client.WebSocketException: Failed to connect to '***********:8080': failed to connect to /*********** (port 8080) from /:: (port 0) after 15000ms: connect failed: ENETUNREACH (Network is unreachable)
                                                                                                    	at com.neovisionaries.ws.client.SocketConnector.connectSocket(SocketConnector.java:126)
                                                                                                    	at com.neovisionaries.ws.client.SocketConnector.doConnect(SocketConnector.java:238)
                                                                                                    	at com.neovisionaries.ws.client.SocketConnector.connect(SocketConnector.java:189)
                                                                                                    	at com.neovisionaries.ws.client.WebSocket.connect(WebSocket.java:2351)
                                                                                                    	at com.neovisionaries.ws.client.ConnectThread.runMain(ConnectThread.java:32)
                                                                                                    	at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45)
                                                                                                    Caused by: java.net.ConnectException: failed to connect to /*********** (port 8080) from /:: (port 0) after 15000ms: connect failed: ENETUNREACH (Network is unreachable)
                                                                                                    	at libcore.io.IoBridge.connect(IoBridge.java:187)
                                                                                                    	at java.net.PlainSocketImpl.socketConnect(PlainSocketImpl.java:142)
                                                                                                    	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:390)
                                                                                                    	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:230)
                                                                                                    	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:212)
                                                                                                    	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:436)
                                                                                                    	at java.net.Socket.connect(Socket.java:646)
                                                                                                    	at com.neovisionaries.ws.client.SocketInitiator$SocketRacer.run(SocketInitiator.java:126)
                                                                                                    Caused by: android.system.ErrnoException: connect failed: ENETUNREACH (Network is unreachable)
                                                                                                    	at libcore.io.Linux.connect(Native Method)
                                                                                                    	at libcore.io.ForwardingOs.connect(ForwardingOs.java:201)
                                                                                                    	at libcore.io.BlockGuardOs.connect(BlockGuardOs.java:158)
                                                                                                    	at libcore.io.ForwardingOs.connect(ForwardingOs.java:201)
                                                                                                    	at libcore.io.IoBridge.connectErrno(IoBridge.java:218)
                                                                                                    	at libcore.io.IoBridge.connect(IoBridge.java:179)
                                                                                                    	at java.net.PlainSocketImpl.socketConnect(PlainSocketImpl.java:142) 
                                                                                                    	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:390) 
                                                                                                    	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:230) 
                                                                                                    	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:212) 
                                                                                                    	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:436) 
                                                                                                    	at java.net.Socket.connect(Socket.java:646) 
                                                                                                    	at com.neovisionaries.ws.client.SocketInitiator$SocketRacer.run(SocketInitiator.java:126) 
2025-08-18 13:11:35.324  4136-4425  WebSocket               com.dspread.mdm.service              I  🔧 onError: 不触发重连，等待 onDisconnected 处理
2025-08-18 13:11:35.338  4136-4425  WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误 (Ask Gemini)
                                                                                                    com.neovisionaries.ws.client.WebSocketException: Failed to connect to '***********:8080': failed to connect to /*********** (port 8080) from /:: (port 0) after 15000ms: connect failed: ENETUNREACH (Network is unreachable)
                                                                                                    	at com.neovisionaries.ws.client.SocketConnector.connectSocket(SocketConnector.java:126)
                                                                                                    	at com.neovisionaries.ws.client.SocketConnector.doConnect(SocketConnector.java:238)
                                                                                                    	at com.neovisionaries.ws.client.SocketConnector.connect(SocketConnector.java:189)
                                                                                                    	at com.neovisionaries.ws.client.WebSocket.connect(WebSocket.java:2351)
                                                                                                    	at com.neovisionaries.ws.client.ConnectThread.runMain(ConnectThread.java:32)
                                                                                                    	at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45)
                                                                                                    Caused by: java.net.ConnectException: failed to connect to /*********** (port 8080) from /:: (port 0) after 15000ms: connect failed: ENETUNREACH (Network is unreachable)
                                                                                                    	at libcore.io.IoBridge.connect(IoBridge.java:187)
                                                                                                    	at java.net.PlainSocketImpl.socketConnect(PlainSocketImpl.java:142)
                                                                                                    	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:390)
                                                                                                    	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:230)
                                                                                                    	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:212)
                                                                                                    	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:436)
                                                                                                    	at java.net.Socket.connect(Socket.java:646)
                                                                                                    	at com.neovisionaries.ws.client.SocketInitiator$SocketRacer.run(SocketInitiator.java:126)
                                                                                                    Caused by: android.system.ErrnoException: connect failed: ENETUNREACH (Network is unreachable)
                                                                                                    	at libcore.io.Linux.connect(Native Method)
                                                                                                    	at libcore.io.ForwardingOs.connect(ForwardingOs.java:201)
                                                                                                    	at libcore.io.BlockGuardOs.connect(BlockGuardOs.java:158)
                                                                                                    	at libcore.io.ForwardingOs.connect(ForwardingOs.java:201)
                                                                                                    	at libcore.io.IoBridge.connectErrno(IoBridge.java:218)
                                                                                                    	at libcore.io.IoBridge.connect(IoBridge.java:179)
                                                                                                    	at java.net.PlainSocketImpl.socketConnect(PlainSocketImpl.java:142) 
                                                                                                    	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:390) 
                                                                                                    	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:230) 
                                                                                                    	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:212) 
                                                                                                    	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:436) 
                                                                                                    	at java.net.Socket.connect(Socket.java:646) 
                                                                                                    	at com.neovisionaries.ws.client.SocketInitiator$SocketRacer.run(SocketInitiator.java:126) 
2025-08-18 13:11:35.346  4136-4425  WebSocket               com.dspread.mdm.service              I  🔧 reconnect() 被调用，调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onConnectError:675
2025-08-18 13:11:35.353  4136-4425  WebSocket               com.dspread.mdm.service              I  🔧 开始重连 WebSocket... (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onConnectError:675)
2025-08-18 13:11:35.360  4136-4425  WebSocket               com.dspread.mdm.service              I  🔧 使用Provisioning配置的重连策略: delayPolicy=30, delaySwitch=1, delayTime=900
2025-08-18 13:11:35.366  4136-4425  WebSocket               com.dspread.mdm.service              I  🔧 当前断开记录数: 0, 延迟开关: 1
2025-08-18 13:11:35.373  4136-4425  WebSocket               com.dspread.mdm.service              I  🔧 重连策略计算完成: 第8次重连，间隔15000ms (15秒)
2025-08-18 13:11:35.379  4136-4425  WebSocket               com.dspread.mdm.service              I  🔧 开始第8次重连，间隔15000ms (15秒) (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onConnectError:675)
2025-08-18 13:11:36.909  4136-4334  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineManager 从日志解析进度: 100%
2025-08-18 13:11:36.947  4136-4136  Common                  com.dspread.mdm.service              D  🔧 OsUpdateTest: 升级进度: 后处理阶段: A/B分区切换准备中，请耐心等待3-5分钟 (100%)
2025-08-18 13:11:36.953  4136-4136  Common                  com.dspread.mdm.service              D  🔧 OsUpdateTest: 升级进度更新: 后处理阶段 - A/B分区切换准备中，请耐心等待3-5分钟 (100%)
2025-08-18 13:11:36.960  4136-4136  Common                  com.dspread.mdm.service              D  🔧 OsUpdateTest: 注意：FINALIZING 100%后需要等待A/B分区切换完成
2025-08-18 13:11:36.966  4136-4136  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineUpgradeStrategy UpdateEngine状态: FINALIZING (5), 进度: 100%
2025-08-18 13:11:37.138  4136-4317  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineManager 检测到文件系统活动: /data/ota_package
2025-08-18 13:11:37.140  4136-4136  Thread-15               com.dspread.mdm.service              W  type=1400 audit(0.0:196): avc:  denied  { getattr } for  path="/data/cache/recovery" dev="mmcblk0p47" ino=193 scontext=u:r:system_app:s0 tcontext=u:object_r:cache_recovery_file:s0 tclass=dir permissive=0
2025-08-18 13:11:37.140  4136-4136  Thread-15               com.dspread.mdm.service              W  type=1400 audit(0.0:197): avc:  denied  { getattr } for  path="/dev/block/platform/bootdevice/by-name" dev="tmpfs" ino=7267 scontext=u:r:system_app:s0 tcontext=u:object_r:block_device:s0 tclass=dir permissive=0
2025-08-18 13:11:42.152  4136-4317  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineManager 检测到文件系统活动: /data/ota_package
2025-08-18 13:11:42.150  4136-4136  Thread-15               com.dspread.mdm.service              W  type=1400 audit(0.0:198): avc:  denied  { getattr } for  path="/data/cache/recovery" dev="mmcblk0p47" ino=193 scontext=u:r:system_app:s0 tcontext=u:object_r:cache_recovery_file:s0 tclass=dir permissive=0
2025-08-18 13:11:42.150  4136-4136  Thread-15               com.dspread.mdm.service              W  type=1400 audit(0.0:199): avc:  denied  { getattr } for  path="/dev/block/platform/bootdevice/by-name" dev="tmpfs" ino=7267 scontext=u:r:system_app:s0 tcontext=u:object_r:block_device:s0 tclass=dir permissive=0
2025-08-18 13:11:47.160  4136-4317  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineManager 检测到文件系统活动: /data/ota_package
2025-08-18 13:11:47.160  4136-4136  Thread-15               com.dspread.mdm.service              W  type=1400 audit(0.0:200): avc:  denied  { getattr } for  path="/data/cache/recovery" dev="mmcblk0p47" ino=193 scontext=u:r:system_app:s0 tcontext=u:object_r:cache_recovery_file:s0 tclass=dir permissive=0
2025-08-18 13:11:47.160  4136-4136  Thread-15               com.dspread.mdm.service              W  type=1400 audit(0.0:201): avc:  denied  { getattr } for  path="/dev/block/platform/bootdevice/by-name" dev="tmpfs" ino=7267 scontext=u:r:system_app:s0 tcontext=u:object_r:block_device:s0 tclass=dir permissive=0
2025-08-18 13:11:50.412  4136-4136  WebSocket               com.dspread.mdm.service              I  🔧 重连 WebSocket: ws://***********:8080/status/websocket/register?serialNo=MDEzNTQwOTAyMDI1MDMwNTAzOTk=&publicKey=TUlHZk1BMEdDU3FHU0liM0RRRUJBUVVBQTRHTkFEQ0JpUUtCZ1FETHNEZVQyMEU0NXdBeDN1QkpkRG5wR0kxUlFVcytOa1NVNHpDSmdVaU45Yy9vdURXMlR6ZVZHVXNhaFhyeVdBVU9MUmd2UjArQld2eDJ3K2VsdmJXbEIrdXN0ZnJyU0JMY3gyZ1VrdkdIRjRhNU5sNnprcVJjZEc5bnZuRUZTclArcVR0a0J3bnhQb2FnMldydldIS1Z1eTE0bjVGVkZWWGU4czBjRmdoeTFRSURBUUFC&query=0&msgVer=3&timestamp=1755493910398&signature=YNUv1RMdfmSFaaTAxnIvg74S1guNocba03w4tjU6Evv/h6IEWYHfasmPmq5VUeCOcYhfU3tnIPqBQOD+vLyJPSQCZz2Tc5eWaCM2GcYOp2hrzc1IkvHwYzXKK5RXl2jhpOWEBQUFGeDFzUOUTH5STsT2XuLpz6crnWMHLWxxhoI=
2025-08-18 13:11:50.419  4136-4136  WebSocket               com.dspread.mdm.service              I  🔧 未检测到系统代理设置
2025-08-18 13:11:50.439  4136-4479  TrafficStats            com.dspread.mdm.service              D  tagSocket(114) with statsTag=0xffffffff, statsUid=-1
2025-08-18 13:11:50.455  4136-4478  WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误 (Ask Gemini)
                                                                                                    com.neovisionaries.ws.client.WebSocketException: Failed to connect to '***********:8080': failed to connect to /*********** (port 8080) from /:: (port 0) after 15000ms: connect failed: ENETUNREACH (Network is unreachable)
                                                                                                    	at com.neovisionaries.ws.client.SocketConnector.connectSocket(SocketConnector.java:126)
                                                                                                    	at com.neovisionaries.ws.client.SocketConnector.doConnect(SocketConnector.java:238)
                                                                                                    	at com.neovisionaries.ws.client.SocketConnector.connect(SocketConnector.java:189)
                                                                                                    	at com.neovisionaries.ws.client.WebSocket.connect(WebSocket.java:2351)
                                                                                                    	at com.neovisionaries.ws.client.ConnectThread.runMain(ConnectThread.java:32)
                                                                                                    	at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45)
                                                                                                    Caused by: java.net.ConnectException: failed to connect to /*********** (port 8080) from /:: (port 0) after 15000ms: connect failed: ENETUNREACH (Network is unreachable)
                                                                                                    	at libcore.io.IoBridge.connect(IoBridge.java:187)
                                                                                                    	at java.net.PlainSocketImpl.socketConnect(PlainSocketImpl.java:142)
                                                                                                    	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:390)
                                                                                                    	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:230)
                                                                                                    	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:212)
                                                                                                    	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:436)
                                                                                                    	at java.net.Socket.connect(Socket.java:646)
                                                                                                    	at com.neovisionaries.ws.client.SocketInitiator$SocketRacer.run(SocketInitiator.java:126)
                                                                                                    Caused by: android.system.ErrnoException: connect failed: ENETUNREACH (Network is unreachable)
                                                                                                    	at libcore.io.Linux.connect(Native Method)
                                                                                                    	at libcore.io.ForwardingOs.connect(ForwardingOs.java:201)
                                                                                                    	at libcore.io.BlockGuardOs.connect(BlockGuardOs.java:158)
                                                                                                    	at libcore.io.ForwardingOs.connect(ForwardingOs.java:201)
                                                                                                    	at libcore.io.IoBridge.connectErrno(IoBridge.java:218)
                                                                                                    	at libcore.io.IoBridge.connect(IoBridge.java:179)
                                                                                                    	at java.net.PlainSocketImpl.socketConnect(PlainSocketImpl.java:142) 
                                                                                                    	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:390) 
                                                                                                    	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:230) 
                                                                                                    	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:212) 
                                                                                                    	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:436) 
                                                                                                    	at java.net.Socket.connect(Socket.java:646) 
                                                                                                    	at com.neovisionaries.ws.client.SocketInitiator$SocketRacer.run(SocketInitiator.java:126) 
2025-08-18 13:11:50.463  4136-4478  WebSocket               com.dspread.mdm.service              I  🔧 onError: 不触发重连，等待 onDisconnected 处理
2025-08-18 13:11:50.477  4136-4478  WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误 (Ask Gemini)
                                                                                                    com.neovisionaries.ws.client.WebSocketException: Failed to connect to '***********:8080': failed to connect to /*********** (port 8080) from /:: (port 0) after 15000ms: connect failed: ENETUNREACH (Network is unreachable)
                                                                                                    	at com.neovisionaries.ws.client.SocketConnector.connectSocket(SocketConnector.java:126)
                                                                                                    	at com.neovisionaries.ws.client.SocketConnector.doConnect(SocketConnector.java:238)
                                                                                                    	at com.neovisionaries.ws.client.SocketConnector.connect(SocketConnector.java:189)
                                                                                                    	at com.neovisionaries.ws.client.WebSocket.connect(WebSocket.java:2351)
                                                                                                    	at com.neovisionaries.ws.client.ConnectThread.runMain(ConnectThread.java:32)
                                                                                                    	at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45)
                                                                                                    Caused by: java.net.ConnectException: failed to connect to /*********** (port 8080) from /:: (port 0) after 15000ms: connect failed: ENETUNREACH (Network is unreachable)
                                                                                                    	at libcore.io.IoBridge.connect(IoBridge.java:187)
                                                                                                    	at java.net.PlainSocketImpl.socketConnect(PlainSocketImpl.java:142)
                                                                                                    	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:390)
                                                                                                    	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:230)
                                                                                                    	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:212)
                                                                                                    	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:436)
                                                                                                    	at java.net.Socket.connect(Socket.java:646)
                                                                                                    	at com.neovisionaries.ws.client.SocketInitiator$SocketRacer.run(SocketInitiator.java:126)
                                                                                                    Caused by: android.system.ErrnoException: connect failed: ENETUNREACH (Network is unreachable)
                                                                                                    	at libcore.io.Linux.connect(Native Method)
                                                                                                    	at libcore.io.ForwardingOs.connect(ForwardingOs.java:201)
                                                                                                    	at libcore.io.BlockGuardOs.connect(BlockGuardOs.java:158)
                                                                                                    	at libcore.io.ForwardingOs.connect(ForwardingOs.java:201)
                                                                                                    	at libcore.io.IoBridge.connectErrno(IoBridge.java:218)
                                                                                                    	at libcore.io.IoBridge.connect(IoBridge.java:179)
                                                                                                    	at java.net.PlainSocketImpl.socketConnect(PlainSocketImpl.java:142) 
                                                                                                    	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:390) 
                                                                                                    	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:230) 
                                                                                                    	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:212) 
                                                                                                    	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:436) 
                                                                                                    	at java.net.Socket.connect(Socket.java:646) 
                                                                                                    	at com.neovisionaries.ws.client.SocketInitiator$SocketRacer.run(SocketInitiator.java:126) 
2025-08-18 13:11:50.485  4136-4478  WebSocket               com.dspread.mdm.service              I  🔧 reconnect() 被调用，调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onConnectError:675
2025-08-18 13:11:50.491  4136-4478  WebSocket               com.dspread.mdm.service              I  🔧 开始重连 WebSocket... (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onConnectError:675)
2025-08-18 13:11:50.498  4136-4478  WebSocket               com.dspread.mdm.service              I  🔧 使用Provisioning配置的重连策略: delayPolicy=30, delaySwitch=1, delayTime=900
2025-08-18 13:11:50.504  4136-4478  WebSocket               com.dspread.mdm.service              I  🔧 当前断开记录数: 0, 延迟开关: 1
2025-08-18 13:11:50.511  4136-4478  WebSocket               com.dspread.mdm.service              I  🔧 重连策略计算完成: 第9次重连，间隔15000ms (15秒)
2025-08-18 13:11:50.517  4136-4478  WebSocket               com.dspread.mdm.service              I  🔧 开始第9次重连，间隔15000ms (15秒) (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onConnectError:675)
2025-08-18 13:11:52.160  4136-4136  Thread-15               com.dspread.mdm.service              W  type=1400 audit(0.0:202): avc:  denied  { getattr } for  path="/data/cache/recovery" dev="mmcblk0p47" ino=193 scontext=u:r:system_app:s0 tcontext=u:object_r:cache_recovery_file:s0 tclass=dir permissive=0
2025-08-18 13:11:52.168  4136-4317  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineManager 检测到文件系统活动: /data/ota_package
2025-08-18 13:11:52.160  4136-4136  Thread-15               com.dspread.mdm.service              W  type=1400 audit(0.0:203): avc:  denied  { getattr } for  path="/dev/block/platform/bootdevice/by-name" dev="tmpfs" ino=7267 scontext=u:r:system_app:s0 tcontext=u:object_r:block_device:s0 tclass=dir permissive=0
2025-08-18 13:11:57.182  4136-4317  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineManager 检测到文件系统活动: /data/ota_package
2025-08-18 13:11:57.180  4136-4136  Thread-15               com.dspread.mdm.service              W  type=1400 audit(0.0:204): avc:  denied  { getattr } for  path="/data/cache/recovery" dev="mmcblk0p47" ino=193 scontext=u:r:system_app:s0 tcontext=u:object_r:cache_recovery_file:s0 tclass=dir permissive=0
2025-08-18 13:11:57.180  4136-4136  Thread-15               com.dspread.mdm.service              W  type=1400 audit(0.0:205): avc:  denied  { getattr } for  path="/dev/block/platform/bootdevice/by-name" dev="tmpfs" ino=7267 scontext=u:r:system_app:s0 tcontext=u:object_r:block_device:s0 tclass=dir permissive=0
2025-08-18 13:12:02.191  4136-4317  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineManager 检测到文件系统活动: /data/ota_package
2025-08-18 13:12:02.190  4136-4136  Thread-15               com.dspread.mdm.service              W  type=1400 audit(0.0:206): avc:  denied  { getattr } for  path="/data/cache/recovery" dev="mmcblk0p47" ino=193 scontext=u:r:system_app:s0 tcontext=u:object_r:cache_recovery_file:s0 tclass=dir permissive=0
2025-08-18 13:12:02.190  4136-4136  Thread-15               com.dspread.mdm.service              W  type=1400 audit(0.0:207): avc:  denied  { getattr } for  path="/dev/block/platform/bootdevice/by-name" dev="tmpfs" ino=7267 scontext=u:r:system_app:s0 tcontext=u:object_r:block_device:s0 tclass=dir permissive=0
2025-08-18 13:12:03.725  4136-4136  ScrollIdentify          com.dspread.mdm.service              I  on fling
2025-08-18 13:12:05.536  4136-4136  WebSocket               com.dspread.mdm.service              I  🔧 重连 WebSocket: ws://***********:8080/status/websocket/register?serialNo=MDEzNTQwOTAyMDI1MDMwNTAzOTk=&publicKey=TUlHZk1BMEdDU3FHU0liM0RRRUJBUVVBQTRHTkFEQ0JpUUtCZ1FETHNEZVQyMEU0NXdBeDN1QkpkRG5wR0kxUlFVcytOa1NVNHpDSmdVaU45Yy9vdURXMlR6ZVZHVXNhaFhyeVdBVU9MUmd2UjArQld2eDJ3K2VsdmJXbEIrdXN0ZnJyU0JMY3gyZ1VrdkdIRjRhNU5sNnprcVJjZEc5bnZuRUZTclArcVR0a0J3bnhQb2FnMldydldIS1Z1eTE0bjVGVkZWWGU4czBjRmdoeTFRSURBUUFC&query=0&msgVer=3&timestamp=1755493925523&signature=ocs/phnU1Gnh7PtgDIu2FMtkCsI9ewEriPigZ7px1naRuO5wg7BLcITiZBu+XzQAjfsorhImxwHHhfDHEOH5ZGv9KwkCFLJvFIpebetLVDlYOXH/dZF4R24Zo6gj5aG/tcwebkGgWRkQoKeBXk0SKT+BNMdFBZl1BEghv7EUKZA=
2025-08-18 13:12:05.542  4136-4136  WebSocket               com.dspread.mdm.service              I  🔧 未检测到系统代理设置
2025-08-18 13:12:05.562  4136-4524  TrafficStats            com.dspread.mdm.service              D  tagSocket(116) with statsTag=0xffffffff, statsUid=-1
2025-08-18 13:12:05.575  4136-4523  WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误 (Ask Gemini)
                                                                                                    com.neovisionaries.ws.client.WebSocketException: Failed to connect to '***********:8080': failed to connect to /*********** (port 8080) from /:: (port 0) after 15000ms: connect failed: ENETUNREACH (Network is unreachable)
                                                                                                    	at com.neovisionaries.ws.client.SocketConnector.connectSocket(SocketConnector.java:126)
                                                                                                    	at com.neovisionaries.ws.client.SocketConnector.doConnect(SocketConnector.java:238)
                                                                                                    	at com.neovisionaries.ws.client.SocketConnector.connect(SocketConnector.java:189)
                                                                                                    	at com.neovisionaries.ws.client.WebSocket.connect(WebSocket.java:2351)
                                                                                                    	at com.neovisionaries.ws.client.ConnectThread.runMain(ConnectThread.java:32)
                                                                                                    	at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45)
                                                                                                    Caused by: java.net.ConnectException: failed to connect to /*********** (port 8080) from /:: (port 0) after 15000ms: connect failed: ENETUNREACH (Network is unreachable)
                                                                                                    	at libcore.io.IoBridge.connect(IoBridge.java:187)
                                                                                                    	at java.net.PlainSocketImpl.socketConnect(PlainSocketImpl.java:142)
                                                                                                    	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:390)
                                                                                                    	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:230)
                                                                                                    	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:212)
                                                                                                    	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:436)
                                                                                                    	at java.net.Socket.connect(Socket.java:646)
                                                                                                    	at com.neovisionaries.ws.client.SocketInitiator$SocketRacer.run(SocketInitiator.java:126)
                                                                                                    Caused by: android.system.ErrnoException: connect failed: ENETUNREACH (Network is unreachable)
                                                                                                    	at libcore.io.Linux.connect(Native Method)
                                                                                                    	at libcore.io.ForwardingOs.connect(ForwardingOs.java:201)
                                                                                                    	at libcore.io.BlockGuardOs.connect(BlockGuardOs.java:158)
                                                                                                    	at libcore.io.ForwardingOs.connect(ForwardingOs.java:201)
                                                                                                    	at libcore.io.IoBridge.connectErrno(IoBridge.java:218)
                                                                                                    	at libcore.io.IoBridge.connect(IoBridge.java:179)
                                                                                                    	at java.net.PlainSocketImpl.socketConnect(PlainSocketImpl.java:142) 
                                                                                                    	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:390) 
                                                                                                    	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:230) 
                                                                                                    	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:212) 
                                                                                                    	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:436) 
                                                                                                    	at java.net.Socket.connect(Socket.java:646) 
                                                                                                    	at com.neovisionaries.ws.client.SocketInitiator$SocketRacer.run(SocketInitiator.java:126) 
2025-08-18 13:12:05.581  4136-4523  WebSocket               com.dspread.mdm.service              I  🔧 onError: 不触发重连，等待 onDisconnected 处理
2025-08-18 13:12:05.594  4136-4523  WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误 (Ask Gemini)
                                                                                                    com.neovisionaries.ws.client.WebSocketException: Failed to connect to '***********:8080': failed to connect to /*********** (port 8080) from /:: (port 0) after 15000ms: connect failed: ENETUNREACH (Network is unreachable)
                                                                                                    	at com.neovisionaries.ws.client.SocketConnector.connectSocket(SocketConnector.java:126)
                                                                                                    	at com.neovisionaries.ws.client.SocketConnector.doConnect(SocketConnector.java:238)
                                                                                                    	at com.neovisionaries.ws.client.SocketConnector.connect(SocketConnector.java:189)
                                                                                                    	at com.neovisionaries.ws.client.WebSocket.connect(WebSocket.java:2351)
                                                                                                    	at com.neovisionaries.ws.client.ConnectThread.runMain(ConnectThread.java:32)
                                                                                                    	at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45)
                                                                                                    Caused by: java.net.ConnectException: failed to connect to /*********** (port 8080) from /:: (port 0) after 15000ms: connect failed: ENETUNREACH (Network is unreachable)
                                                                                                    	at libcore.io.IoBridge.connect(IoBridge.java:187)
                                                                                                    	at java.net.PlainSocketImpl.socketConnect(PlainSocketImpl.java:142)
                                                                                                    	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:390)
                                                                                                    	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:230)
                                                                                                    	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:212)
                                                                                                    	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:436)
                                                                                                    	at java.net.Socket.connect(Socket.java:646)
                                                                                                    	at com.neovisionaries.ws.client.SocketInitiator$SocketRacer.run(SocketInitiator.java:126)
                                                                                                    Caused by: android.system.ErrnoException: connect failed: ENETUNREACH (Network is unreachable)
                                                                                                    	at libcore.io.Linux.connect(Native Method)
                                                                                                    	at libcore.io.ForwardingOs.connect(ForwardingOs.java:201)
                                                                                                    	at libcore.io.BlockGuardOs.connect(BlockGuardOs.java:158)
                                                                                                    	at libcore.io.ForwardingOs.connect(ForwardingOs.java:201)
                                                                                                    	at libcore.io.IoBridge.connectErrno(IoBridge.java:218)
                                                                                                    	at libcore.io.IoBridge.connect(IoBridge.java:179)
                                                                                                    	at java.net.PlainSocketImpl.socketConnect(PlainSocketImpl.java:142) 
                                                                                                    	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:390) 
                                                                                                    	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:230) 
                                                                                                    	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:212) 
                                                                                                    	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:436) 
                                                                                                    	at java.net.Socket.connect(Socket.java:646) 
                                                                                                    	at com.neovisionaries.ws.client.SocketInitiator$SocketRacer.run(SocketInitiator.java:126) 
2025-08-18 13:12:05.602  4136-4523  WebSocket               com.dspread.mdm.service              I  🔧 reconnect() 被调用，调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onConnectError:675
2025-08-18 13:12:05.608  4136-4523  WebSocket               com.dspread.mdm.service              I  🔧 开始重连 WebSocket... (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onConnectError:675)
2025-08-18 13:12:05.617  4136-4523  WebSocket               com.dspread.mdm.service              I  🔧 使用Provisioning配置的重连策略: delayPolicy=30, delaySwitch=1, delayTime=900
2025-08-18 13:12:05.624  4136-4523  WebSocket               com.dspread.mdm.service              I  🔧 当前断开记录数: 0, 延迟开关: 1
2025-08-18 13:12:05.630  4136-4523  WebSocket               com.dspread.mdm.service              I  🔧 重连策略计算完成: 第10次重连，间隔15000ms (15秒)
2025-08-18 13:12:05.635  4136-4523  WebSocket               com.dspread.mdm.service              I  🔧 开始第10次重连，间隔15000ms (15秒) (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onConnectError:675)
2025-08-18 13:12:07.215  4136-4317  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineManager 检测到文件系统活动: /data/ota_package
2025-08-18 13:12:07.250  4136-4136  Thread-15               com.dspread.mdm.service              W  type=1400 audit(0.0:208): avc:  denied  { getattr } for  path="/data/cache/recovery" dev="mmcblk0p47" ino=193 scontext=u:r:system_app:s0 tcontext=u:object_r:cache_recovery_file:s0 tclass=dir permissive=0
2025-08-18 13:12:07.250  4136-4136  Thread-15               com.dspread.mdm.service              W  type=1400 audit(0.0:209): avc:  denied  { getattr } for  path="/dev/block/platform/bootdevice/by-name" dev="tmpfs" ino=7267 scontext=u:r:system_app:s0 tcontext=u:object_r:block_device:s0 tclass=dir permissive=0
2025-08-18 13:12:12.250  4136-4136  Thread-15               com.dspread.mdm.service              W  type=1400 audit(0.0:210): avc:  denied  { getattr } for  path="/data/cache/recovery" dev="mmcblk0p47" ino=193 scontext=u:r:system_app:s0 tcontext=u:object_r:cache_recovery_file:s0 tclass=dir permissive=0
2025-08-18 13:12:12.250  4136-4136  Thread-15               com.dspread.mdm.service              W  type=1400 audit(0.0:211): avc:  denied  { getattr } for  path="/dev/block/platform/bootdevice/by-name" dev="tmpfs" ino=7267 scontext=u:r:system_app:s0 tcontext=u:object_r:block_device:s0 tclass=dir permissive=0
---------------------------- PROCESS STARTED (4555) for package com.dspread.mdm.service ----------------------------
2025-08-18 13:12:17.250  4136-4136  Thread-15               com.dspread.mdm.service              W  type=1400 audit(0.0:212): avc:  denied  { getattr } for  path="/data/cache/recovery" dev="mmcblk0p47" ino=193 scontext=u:r:system_app:s0 tcontext=u:object_r:cache_recovery_file:s0 tclass=dir permissive=0
2025-08-18 13:12:17.250  4136-4136  Thread-15               com.dspread.mdm.service              W  type=1400 audit(0.0:213): avc:  denied  { getattr } for  path="/dev/block/platform/bootdevice/by-name" dev="tmpfs" ino=7267 scontext=u:r:system_app:s0 tcontext=u:object_r:block_device:s0 tclass=dir permissive=0
---------------------------- PROCESS ENDED (4555) for package com.dspread.mdm.service ----------------------------
2025-08-18 13:12:20.677  4136-4136  WebSocket               com.dspread.mdm.service              I  🔧 重连 WebSocket: ws://***********:8080/status/websocket/register?serialNo=MDEzNTQwOTAyMDI1MDMwNTAzOTk=&publicKey=TUlHZk1BMEdDU3FHU0liM0RRRUJBUVVBQTRHTkFEQ0JpUUtCZ1FETHNEZVQyMEU0NXdBeDN1QkpkRG5wR0kxUlFVcytOa1NVNHpDSmdVaU45Yy9vdURXMlR6ZVZHVXNhaFhyeVdBVU9MUmd2UjArQld2eDJ3K2VsdmJXbEIrdXN0ZnJyU0JMY3gyZ1VrdkdIRjRhNU5sNnprcVJjZEc5bnZuRUZTclArcVR0a0J3bnhQb2FnMldydldIS1Z1eTE0bjVGVkZWWGU4czBjRmdoeTFRSURBUUFC&query=0&msgVer=3&timestamp=1755493940664&signature=v+imGoz8ZUZ925rP4tMsZpvuR0Bmw9nZteVq4r9vMKti7ehVHz+4jxIemO23BribgBNGmlIXOoR9xoiBIuGVvOSPuJUqI8MvYxq6ZygZdoeBfAd8YSyI13ti6wkW3MMJ//+SgKwoimReyaTz7uY06hjZ1t9OXPhn3sse82E81pw=
2025-08-18 13:12:20.683  4136-4136  WebSocket               com.dspread.mdm.service              I  🔧 未检测到系统代理设置
2025-08-18 13:12:20.705  4136-4569  TrafficStats            com.dspread.mdm.service              D  tagSocket(116) with statsTag=0xffffffff, statsUid=-1
2025-08-18 13:12:20.721  4136-4568  WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误 (Ask Gemini)
                                                                                                    com.neovisionaries.ws.client.WebSocketException: Failed to connect to '***********:8080': failed to connect to /*********** (port 8080) from /:: (port 0) after 15000ms: connect failed: ENETUNREACH (Network is unreachable)
                                                                                                    	at com.neovisionaries.ws.client.SocketConnector.connectSocket(SocketConnector.java:126)
                                                                                                    	at com.neovisionaries.ws.client.SocketConnector.doConnect(SocketConnector.java:238)
                                                                                                    	at com.neovisionaries.ws.client.SocketConnector.connect(SocketConnector.java:189)
                                                                                                    	at com.neovisionaries.ws.client.WebSocket.connect(WebSocket.java:2351)
                                                                                                    	at com.neovisionaries.ws.client.ConnectThread.runMain(ConnectThread.java:32)
                                                                                                    	at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45)
                                                                                                    Caused by: java.net.ConnectException: failed to connect to /*********** (port 8080) from /:: (port 0) after 15000ms: connect failed: ENETUNREACH (Network is unreachable)
                                                                                                    	at libcore.io.IoBridge.connect(IoBridge.java:187)
                                                                                                    	at java.net.PlainSocketImpl.socketConnect(PlainSocketImpl.java:142)
                                                                                                    	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:390)
                                                                                                    	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:230)
                                                                                                    	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:212)
                                                                                                    	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:436)
                                                                                                    	at java.net.Socket.connect(Socket.java:646)
                                                                                                    	at com.neovisionaries.ws.client.SocketInitiator$SocketRacer.run(SocketInitiator.java:126)
                                                                                                    Caused by: android.system.ErrnoException: connect failed: ENETUNREACH (Network is unreachable)
                                                                                                    	at libcore.io.Linux.connect(Native Method)
                                                                                                    	at libcore.io.ForwardingOs.connect(ForwardingOs.java:201)
                                                                                                    	at libcore.io.BlockGuardOs.connect(BlockGuardOs.java:158)
                                                                                                    	at libcore.io.ForwardingOs.connect(ForwardingOs.java:201)
                                                                                                    	at libcore.io.IoBridge.connectErrno(IoBridge.java:218)
                                                                                                    	at libcore.io.IoBridge.connect(IoBridge.java:179)
                                                                                                    	at java.net.PlainSocketImpl.socketConnect(PlainSocketImpl.java:142) 
                                                                                                    	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:390) 
                                                                                                    	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:230) 
                                                                                                    	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:212) 
                                                                                                    	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:436) 
                                                                                                    	at java.net.Socket.connect(Socket.java:646) 
                                                                                                    	at com.neovisionaries.ws.client.SocketInitiator$SocketRacer.run(SocketInitiator.java:126) 
2025-08-18 13:12:20.728  4136-4568  WebSocket               com.dspread.mdm.service              I  🔧 onError: 不触发重连，等待 onDisconnected 处理
2025-08-18 13:12:20.742  4136-4568  WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误 (Ask Gemini)
                                                                                                    com.neovisionaries.ws.client.WebSocketException: Failed to connect to '***********:8080': failed to connect to /*********** (port 8080) from /:: (port 0) after 15000ms: connect failed: ENETUNREACH (Network is unreachable)
                                                                                                    	at com.neovisionaries.ws.client.SocketConnector.connectSocket(SocketConnector.java:126)
                                                                                                    	at com.neovisionaries.ws.client.SocketConnector.doConnect(SocketConnector.java:238)
                                                                                                    	at com.neovisionaries.ws.client.SocketConnector.connect(SocketConnector.java:189)
                                                                                                    	at com.neovisionaries.ws.client.WebSocket.connect(WebSocket.java:2351)
                                                                                                    	at com.neovisionaries.ws.client.ConnectThread.runMain(ConnectThread.java:32)
                                                                                                    	at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45)
                                                                                                    Caused by: java.net.ConnectException: failed to connect to /*********** (port 8080) from /:: (port 0) after 15000ms: connect failed: ENETUNREACH (Network is unreachable)
                                                                                                    	at libcore.io.IoBridge.connect(IoBridge.java:187)
                                                                                                    	at java.net.PlainSocketImpl.socketConnect(PlainSocketImpl.java:142)
                                                                                                    	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:390)
                                                                                                    	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:230)
                                                                                                    	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:212)
                                                                                                    	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:436)
                                                                                                    	at java.net.Socket.connect(Socket.java:646)
                                                                                                    	at com.neovisionaries.ws.client.SocketInitiator$SocketRacer.run(SocketInitiator.java:126)
                                                                                                    Caused by: android.system.ErrnoException: connect failed: ENETUNREACH (Network is unreachable)
                                                                                                    	at libcore.io.Linux.connect(Native Method)
                                                                                                    	at libcore.io.ForwardingOs.connect(ForwardingOs.java:201)
                                                                                                    	at libcore.io.BlockGuardOs.connect(BlockGuardOs.java:158)
                                                                                                    	at libcore.io.ForwardingOs.connect(ForwardingOs.java:201)
                                                                                                    	at libcore.io.IoBridge.connectErrno(IoBridge.java:218)
                                                                                                    	at libcore.io.IoBridge.connect(IoBridge.java:179)
                                                                                                    	at java.net.PlainSocketImpl.socketConnect(PlainSocketImpl.java:142) 
                                                                                                    	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:390) 
                                                                                                    	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:230) 
                                                                                                    	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:212) 
                                                                                                    	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:436) 
                                                                                                    	at java.net.Socket.connect(Socket.java:646) 
                                                                                                    	at com.neovisionaries.ws.client.SocketInitiator$SocketRacer.run(SocketInitiator.java:126) 
2025-08-18 13:12:20.750  4136-4568  WebSocket               com.dspread.mdm.service              I  🔧 reconnect() 被调用，调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onConnectError:675
2025-08-18 13:12:20.756  4136-4568  WebSocket               com.dspread.mdm.service              I  🔧 开始重连 WebSocket... (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onConnectError:675)
2025-08-18 13:12:20.766  4136-4568  WebSocket               com.dspread.mdm.service              I  🔧 使用Provisioning配置的重连策略: delayPolicy=30, delaySwitch=1, delayTime=900
2025-08-18 13:12:20.772  4136-4568  WebSocket               com.dspread.mdm.service              I  🔧 当前断开记录数: 0, 延迟开关: 1
2025-08-18 13:12:20.778  4136-4568  WebSocket               com.dspread.mdm.service              I  🔧 重连策略计算完成: 第11次重连，间隔15000ms (15秒)
2025-08-18 13:12:20.783  4136-4568  WebSocket               com.dspread.mdm.service              I  🔧 开始第11次重连，间隔15000ms (15秒) (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onConnectError:675)
2025-08-18 13:12:22.260  4136-4136  Thread-15               com.dspread.mdm.service              W  type=1400 audit(0.0:214): avc:  denied  { getattr } for  path="/data/cache/recovery" dev="mmcblk0p47" ino=193 scontext=u:r:system_app:s0 tcontext=u:object_r:cache_recovery_file:s0 tclass=dir permissive=0
2025-08-18 13:12:22.260  4136-4136  Thread-15               com.dspread.mdm.service              W  type=1400 audit(0.0:215): avc:  denied  { getattr } for  path="/dev/block/platform/bootdevice/by-name" dev="tmpfs" ino=7267 scontext=u:r:system_app:s0 tcontext=u:object_r:block_device:s0 tclass=dir permissive=0
2025-08-18 13:12:27.260  4136-4136  Thread-15               com.dspread.mdm.service              W  type=1400 audit(0.0:216): avc:  denied  { getattr } for  path="/data/cache/recovery" dev="mmcblk0p47" ino=193 scontext=u:r:system_app:s0 tcontext=u:object_r:cache_recovery_file:s0 tclass=dir permissive=0
2025-08-18 13:12:27.260  4136-4136  Thread-15               com.dspread.mdm.service              W  type=1400 audit(0.0:217): avc:  denied  { getattr } for  path="/dev/block/platform/bootdevice/by-name" dev="tmpfs" ino=7267 scontext=u:r:system_app:s0 tcontext=u:object_r:block_device:s0 tclass=dir permissive=0
2025-08-18 13:12:27.719  4136-4136  Receiver                com.dspread.mdm.service              D  🔧 电池状态更新: 电池状态: 电量=73%, 温度=28.5°C, 健康=2, 充电=是, 低电量=否, 耗尽=否
2025-08-18 13:12:32.260  4136-4136  Thread-15               com.dspread.mdm.service              W  type=1400 audit(0.0:218): avc:  denied  { getattr } for  path="/data/cache/recovery" dev="mmcblk0p47" ino=193 scontext=u:r:system_app:s0 tcontext=u:object_r:cache_recovery_file:s0 tclass=dir permissive=0
2025-08-18 13:12:32.260  4136-4136  Thread-15               com.dspread.mdm.service              W  type=1400 audit(0.0:219): avc:  denied  { getattr } for  path="/dev/block/platform/bootdevice/by-name" dev="tmpfs" ino=7267 scontext=u:r:system_app:s0 tcontext=u:object_r:block_device:s0 tclass=dir permissive=0
2025-08-18 13:12:35.811  4136-4136  WebSocket               com.dspread.mdm.service              I  🔧 重连 WebSocket: ws://***********:8080/status/websocket/register?serialNo=MDEzNTQwOTAyMDI1MDMwNTAzOTk=&publicKey=TUlHZk1BMEdDU3FHU0liM0RRRUJBUVVBQTRHTkFEQ0JpUUtCZ1FETHNEZVQyMEU0NXdBeDN1QkpkRG5wR0kxUlFVcytOa1NVNHpDSmdVaU45Yy9vdURXMlR6ZVZHVXNhaFhyeVdBVU9MUmd2UjArQld2eDJ3K2VsdmJXbEIrdXN0ZnJyU0JMY3gyZ1VrdkdIRjRhNU5sNnprcVJjZEc5bnZuRUZTclArcVR0a0J3bnhQb2FnMldydldIS1Z1eTE0bjVGVkZWWGU4czBjRmdoeTFRSURBUUFC&query=0&msgVer=3&timestamp=1755493955798&signature=Ic3mWoGMeDIrH/Smt6SFaAQBnQXToDb5hMqaUj2dHW1nVmXL4c1uAYx6Z2kzohPWFfpiY9nBUJbH71Od+YOHGBU20lwGDylrOLKA0ZjfgXnjld2zFr2Tv90ZykNSVYKWSTFnT7B82QFV8EWUgqtTM626mqGv30AA3lUvdOt4P5U=
2025-08-18 13:12:35.818  4136-4136  WebSocket               com.dspread.mdm.service              I  🔧 未检测到系统代理设置
2025-08-18 13:12:35.839  4136-4617  TrafficStats            com.dspread.mdm.service              D  tagSocket(116) with statsTag=0xffffffff, statsUid=-1
2025-08-18 13:12:35.853  4136-4616  WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误 (Ask Gemini)
                                                                                                    com.neovisionaries.ws.client.WebSocketException: Failed to connect to '***********:8080': failed to connect to /*********** (port 8080) from /:: (port 0) after 15000ms: connect failed: ENETUNREACH (Network is unreachable)
                                                                                                    	at com.neovisionaries.ws.client.SocketConnector.connectSocket(SocketConnector.java:126)
                                                                                                    	at com.neovisionaries.ws.client.SocketConnector.doConnect(SocketConnector.java:238)
                                                                                                    	at com.neovisionaries.ws.client.SocketConnector.connect(SocketConnector.java:189)
                                                                                                    	at com.neovisionaries.ws.client.WebSocket.connect(WebSocket.java:2351)
                                                                                                    	at com.neovisionaries.ws.client.ConnectThread.runMain(ConnectThread.java:32)
                                                                                                    	at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45)
                                                                                                    Caused by: java.net.ConnectException: failed to connect to /*********** (port 8080) from /:: (port 0) after 15000ms: connect failed: ENETUNREACH (Network is unreachable)
                                                                                                    	at libcore.io.IoBridge.connect(IoBridge.java:187)
                                                                                                    	at java.net.PlainSocketImpl.socketConnect(PlainSocketImpl.java:142)
                                                                                                    	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:390)
                                                                                                    	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:230)
                                                                                                    	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:212)
                                                                                                    	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:436)
                                                                                                    	at java.net.Socket.connect(Socket.java:646)
                                                                                                    	at com.neovisionaries.ws.client.SocketInitiator$SocketRacer.run(SocketInitiator.java:126)
                                                                                                    Caused by: android.system.ErrnoException: connect failed: ENETUNREACH (Network is unreachable)
                                                                                                    	at libcore.io.Linux.connect(Native Method)
                                                                                                    	at libcore.io.ForwardingOs.connect(ForwardingOs.java:201)
                                                                                                    	at libcore.io.BlockGuardOs.connect(BlockGuardOs.java:158)
                                                                                                    	at libcore.io.ForwardingOs.connect(ForwardingOs.java:201)
                                                                                                    	at libcore.io.IoBridge.connectErrno(IoBridge.java:218)
                                                                                                    	at libcore.io.IoBridge.connect(IoBridge.java:179)
                                                                                                    	at java.net.PlainSocketImpl.socketConnect(PlainSocketImpl.java:142) 
                                                                                                    	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:390) 
                                                                                                    	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:230) 
                                                                                                    	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:212) 
                                                                                                    	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:436) 
                                                                                                    	at java.net.Socket.connect(Socket.java:646) 
                                                                                                    	at com.neovisionaries.ws.client.SocketInitiator$SocketRacer.run(SocketInitiator.java:126) 
2025-08-18 13:12:35.860  4136-4616  WebSocket               com.dspread.mdm.service              I  🔧 onError: 不触发重连，等待 onDisconnected 处理
2025-08-18 13:12:35.873  4136-4616  WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误 (Ask Gemini)
                                                                                                    com.neovisionaries.ws.client.WebSocketException: Failed to connect to '***********:8080': failed to connect to /*********** (port 8080) from /:: (port 0) after 15000ms: connect failed: ENETUNREACH (Network is unreachable)
                                                                                                    	at com.neovisionaries.ws.client.SocketConnector.connectSocket(SocketConnector.java:126)
                                                                                                    	at com.neovisionaries.ws.client.SocketConnector.doConnect(SocketConnector.java:238)
                                                                                                    	at com.neovisionaries.ws.client.SocketConnector.connect(SocketConnector.java:189)
                                                                                                    	at com.neovisionaries.ws.client.WebSocket.connect(WebSocket.java:2351)
                                                                                                    	at com.neovisionaries.ws.client.ConnectThread.runMain(ConnectThread.java:32)
                                                                                                    	at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45)
                                                                                                    Caused by: java.net.ConnectException: failed to connect to /*********** (port 8080) from /:: (port 0) after 15000ms: connect failed: ENETUNREACH (Network is unreachable)
                                                                                                    	at libcore.io.IoBridge.connect(IoBridge.java:187)
                                                                                                    	at java.net.PlainSocketImpl.socketConnect(PlainSocketImpl.java:142)
                                                                                                    	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:390)
                                                                                                    	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:230)
                                                                                                    	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:212)
                                                                                                    	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:436)
                                                                                                    	at java.net.Socket.connect(Socket.java:646)
                                                                                                    	at com.neovisionaries.ws.client.SocketInitiator$SocketRacer.run(SocketInitiator.java:126)
                                                                                                    Caused by: android.system.ErrnoException: connect failed: ENETUNREACH (Network is unreachable)
                                                                                                    	at libcore.io.Linux.connect(Native Method)
                                                                                                    	at libcore.io.ForwardingOs.connect(ForwardingOs.java:201)
                                                                                                    	at libcore.io.BlockGuardOs.connect(BlockGuardOs.java:158)
                                                                                                    	at libcore.io.ForwardingOs.connect(ForwardingOs.java:201)
                                                                                                    	at libcore.io.IoBridge.connectErrno(IoBridge.java:218)
                                                                                                    	at libcore.io.IoBridge.connect(IoBridge.java:179)
                                                                                                    	at java.net.PlainSocketImpl.socketConnect(PlainSocketImpl.java:142) 
                                                                                                    	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:390) 
                                                                                                    	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:230) 
                                                                                                    	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:212) 
                                                                                                    	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:436) 
                                                                                                    	at java.net.Socket.connect(Socket.java:646) 
                                                                                                    	at com.neovisionaries.ws.client.SocketInitiator$SocketRacer.run(SocketInitiator.java:126) 
2025-08-18 13:12:35.881  4136-4616  WebSocket               com.dspread.mdm.service              I  🔧 reconnect() 被调用，调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onConnectError:675
2025-08-18 13:12:35.888  4136-4616  WebSocket               com.dspread.mdm.service              I  🔧 开始重连 WebSocket... (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onConnectError:675)
2025-08-18 13:12:35.898  4136-4616  WebSocket               com.dspread.mdm.service              I  🔧 使用Provisioning配置的重连策略: delayPolicy=30, delaySwitch=1, delayTime=900
2025-08-18 13:12:35.904  4136-4616  WebSocket               com.dspread.mdm.service              I  🔧 当前断开记录数: 0, 延迟开关: 1
2025-08-18 13:12:35.910  4136-4616  WebSocket               com.dspread.mdm.service              I  🔧 重连策略计算完成: 第12次重连，间隔15000ms (15秒)
2025-08-18 13:12:35.916  4136-4616  WebSocket               com.dspread.mdm.service              I  🔧 开始第12次重连，间隔15000ms (15秒) (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onConnectError:675)
2025-08-18 13:12:37.270  4136-4136  Thread-15               com.dspread.mdm.service              W  type=1400 audit(0.0:220): avc:  denied  { getattr } for  path="/data/cache/recovery" dev="mmcblk0p47" ino=193 scontext=u:r:system_app:s0 tcontext=u:object_r:cache_recovery_file:s0 tclass=dir permissive=0
2025-08-18 13:12:37.270  4136-4136  Thread-15               com.dspread.mdm.service              W  type=1400 audit(0.0:221): avc:  denied  { getattr } for  path="/dev/block/platform/bootdevice/by-name" dev="tmpfs" ino=7267 scontext=u:r:system_app:s0 tcontext=u:object_r:block_device:s0 tclass=dir permissive=0
2025-08-18 13:12:39.026  4136-4204  Common                  com.dspread.mdm.service              D  🔧 ServiceGuardEventHandler 开始执行保活动作
2025-08-18 13:12:39.038  4136-4204  Common                  com.dspread.mdm.service              I  ✅ ⏰ 设置服务守护定时器成功，下次执行: 120秒后 (2分钟)
2025-08-18 13:12:39.045  4136-4204  ContextImpl             com.dspread.mdm.service              W  Calling a method in the system process without a qualified user: android.app.ContextImpl.startService:1899 android.content.ContextWrapper.startService:825 com.dspread.mdm.service.broadcast.handlers.service.ServiceGuardEventHandler.startKeepAliveService:211 com.dspread.mdm.service.broadcast.handlers.service.ServiceGuardEventHandler.performKeepAliveActions:193 com.dspread.mdm.service.broadcast.handlers.service.ServiceGuardEventHandler.access$performKeepAliveActions:24 
2025-08-18 13:12:39.050  4136-4204  Common                  com.dspread.mdm.service              D  🔧 ServiceGuardEventHandler 启动保活服务
2025-08-18 13:12:39.055  4136-4136  Service                 com.dspread.mdm.service              D  🔧 ServiceKeepAliveService 保活服务创建
2025-08-18 13:12:39.063  4136-4136  Service                 com.dspread.mdm.service              D  🔧 ServiceKeepAliveService 保活服务启动
2025-08-18 13:12:39.064  4136-4204  Common                  com.dspread.mdm.service              D  🔧 ServiceGuardEventHandler 保活动作执行完成
2025-08-18 13:12:39.069  4136-4136  Platform                com.dspread.mdm.service              D  🔧 WakeLockManager WakeLock不存在或未持有: ServiceKeepAlive
2025-08-18 13:12:39.079  4136-4136  Platform                com.dspread.mdm.service              D  🔧 WakeLockManager 获取WakeLock成功: ServiceKeepAlive (超时: 30000ms)
2025-08-18 13:12:42.067  4136-4204  Common                  com.dspread.mdm.service              D  🔧 ServiceGuardEventHandler WakeLock已释放
2025-08-18 13:12:42.270  4136-4136  Thread-15               com.dspread.mdm.service              W  type=1400 audit(0.0:222): avc:  denied  { getattr } for  path="/data/cache/recovery" dev="mmcblk0p47" ino=193 scontext=u:r:system_app:s0 tcontext=u:object_r:cache_recovery_file:s0 tclass=dir permissive=0
2025-08-18 13:12:42.270  4136-4136  Thread-15               com.dspread.mdm.service              W  type=1400 audit(0.0:223): avc:  denied  { getattr } for  path="/dev/block/platform/bootdevice/by-name" dev="tmpfs" ino=7267 scontext=u:r:system_app:s0 tcontext=u:object_r:block_device:s0 tclass=dir permissive=0
2025-08-18 13:12:47.270  4136-4136  Thread-15               com.dspread.mdm.service              W  type=1400 audit(0.0:224): avc:  denied  { getattr } for  path="/data/cache/recovery" dev="mmcblk0p47" ino=193 scontext=u:r:system_app:s0 tcontext=u:object_r:cache_recovery_file:s0 tclass=dir permissive=0
2025-08-18 13:12:47.280  4136-4136  Thread-15               com.dspread.mdm.service              W  type=1400 audit(0.0:225): avc:  denied  { getattr } for  path="/dev/block/platform/bootdevice/by-name" dev="tmpfs" ino=7267 scontext=u:r:system_app:s0 tcontext=u:object_r:block_device:s0 tclass=dir permissive=0
2025-08-18 13:12:50.954  4136-4136  WebSocket               com.dspread.mdm.service              I  🔧 重连 WebSocket: ws://***********:8080/status/websocket/register?serialNo=MDEzNTQwOTAyMDI1MDMwNTAzOTk=&publicKey=TUlHZk1BMEdDU3FHU0liM0RRRUJBUVVBQTRHTkFEQ0JpUUtCZ1FETHNEZVQyMEU0NXdBeDN1QkpkRG5wR0kxUlFVcytOa1NVNHpDSmdVaU45Yy9vdURXMlR6ZVZHVXNhaFhyeVdBVU9MUmd2UjArQld2eDJ3K2VsdmJXbEIrdXN0ZnJyU0JMY3gyZ1VrdkdIRjRhNU5sNnprcVJjZEc5bnZuRUZTclArcVR0a0J3bnhQb2FnMldydldIS1Z1eTE0bjVGVkZWWGU4czBjRmdoeTFRSURBUUFC&query=0&msgVer=3&timestamp=1755493970935&signature=E6+LwUVU29x76PhWVBwieuRCKoN0Z+RsuSjLIN7ERAr0wVElcq6L6y8VgzSrGphnrNzeOFQ6aqqP/mfUmLdoXzAF1tq6/I5bYSLVqv77Xy3FYPb8RelLPAsXRlFz6pb63Mgx4eIipMGM9vFvdfD8D/4nWgJUjfYrA9FbPfcp63U=
2025-08-18 13:12:50.961  4136-4136  WebSocket               com.dspread.mdm.service              I  🔧 未检测到系统代理设置
2025-08-18 13:12:50.983  4136-4665  TrafficStats            com.dspread.mdm.service              D  tagSocket(116) with statsTag=0xffffffff, statsUid=-1
2025-08-18 13:12:50.998  4136-4664  WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误 (Ask Gemini)
                                                                                                    com.neovisionaries.ws.client.WebSocketException: Failed to connect to '***********:8080': failed to connect to /*********** (port 8080) from /:: (port 0) after 15000ms: connect failed: ENETUNREACH (Network is unreachable)
                                                                                                    	at com.neovisionaries.ws.client.SocketConnector.connectSocket(SocketConnector.java:126)
                                                                                                    	at com.neovisionaries.ws.client.SocketConnector.doConnect(SocketConnector.java:238)
                                                                                                    	at com.neovisionaries.ws.client.SocketConnector.connect(SocketConnector.java:189)
                                                                                                    	at com.neovisionaries.ws.client.WebSocket.connect(WebSocket.java:2351)
                                                                                                    	at com.neovisionaries.ws.client.ConnectThread.runMain(ConnectThread.java:32)
                                                                                                    	at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45)
                                                                                                    Caused by: java.net.ConnectException: failed to connect to /*********** (port 8080) from /:: (port 0) after 15000ms: connect failed: ENETUNREACH (Network is unreachable)
                                                                                                    	at libcore.io.IoBridge.connect(IoBridge.java:187)
                                                                                                    	at java.net.PlainSocketImpl.socketConnect(PlainSocketImpl.java:142)
                                                                                                    	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:390)
                                                                                                    	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:230)
                                                                                                    	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:212)
                                                                                                    	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:436)
                                                                                                    	at java.net.Socket.connect(Socket.java:646)
                                                                                                    	at com.neovisionaries.ws.client.SocketInitiator$SocketRacer.run(SocketInitiator.java:126)
                                                                                                    Caused by: android.system.ErrnoException: connect failed: ENETUNREACH (Network is unreachable)
                                                                                                    	at libcore.io.Linux.connect(Native Method)
                                                                                                    	at libcore.io.ForwardingOs.connect(ForwardingOs.java:201)
                                                                                                    	at libcore.io.BlockGuardOs.connect(BlockGuardOs.java:158)
                                                                                                    	at libcore.io.ForwardingOs.connect(ForwardingOs.java:201)
                                                                                                    	at libcore.io.IoBridge.connectErrno(IoBridge.java:218)
                                                                                                    	at libcore.io.IoBridge.connect(IoBridge.java:179)
                                                                                                    	at java.net.PlainSocketImpl.socketConnect(PlainSocketImpl.java:142) 
                                                                                                    	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:390) 
                                                                                                    	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:230) 
                                                                                                    	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:212) 
                                                                                                    	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:436) 
                                                                                                    	at java.net.Socket.connect(Socket.java:646) 
                                                                                                    	at com.neovisionaries.ws.client.SocketInitiator$SocketRacer.run(SocketInitiator.java:126) 
2025-08-18 13:12:51.006  4136-4664  WebSocket               com.dspread.mdm.service              I  🔧 onError: 不触发重连，等待 onDisconnected 处理
2025-08-18 13:12:51.021  4136-4664  WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误 (Ask Gemini)
                                                                                                    com.neovisionaries.ws.client.WebSocketException: Failed to connect to '***********:8080': failed to connect to /*********** (port 8080) from /:: (port 0) after 15000ms: connect failed: ENETUNREACH (Network is unreachable)
                                                                                                    	at com.neovisionaries.ws.client.SocketConnector.connectSocket(SocketConnector.java:126)
                                                                                                    	at com.neovisionaries.ws.client.SocketConnector.doConnect(SocketConnector.java:238)
                                                                                                    	at com.neovisionaries.ws.client.SocketConnector.connect(SocketConnector.java:189)
                                                                                                    	at com.neovisionaries.ws.client.WebSocket.connect(WebSocket.java:2351)
                                                                                                    	at com.neovisionaries.ws.client.ConnectThread.runMain(ConnectThread.java:32)
                                                                                                    	at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45)
                                                                                                    Caused by: java.net.ConnectException: failed to connect to /*********** (port 8080) from /:: (port 0) after 15000ms: connect failed: ENETUNREACH (Network is unreachable)
                                                                                                    	at libcore.io.IoBridge.connect(IoBridge.java:187)
                                                                                                    	at java.net.PlainSocketImpl.socketConnect(PlainSocketImpl.java:142)
                                                                                                    	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:390)
                                                                                                    	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:230)
                                                                                                    	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:212)
                                                                                                    	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:436)
                                                                                                    	at java.net.Socket.connect(Socket.java:646)
                                                                                                    	at com.neovisionaries.ws.client.SocketInitiator$SocketRacer.run(SocketInitiator.java:126)
                                                                                                    Caused by: android.system.ErrnoException: connect failed: ENETUNREACH (Network is unreachable)
                                                                                                    	at libcore.io.Linux.connect(Native Method)
                                                                                                    	at libcore.io.ForwardingOs.connect(ForwardingOs.java:201)
                                                                                                    	at libcore.io.BlockGuardOs.connect(BlockGuardOs.java:158)
                                                                                                    	at libcore.io.ForwardingOs.connect(ForwardingOs.java:201)
                                                                                                    	at libcore.io.IoBridge.connectErrno(IoBridge.java:218)
                                                                                                    	at libcore.io.IoBridge.connect(IoBridge.java:179)
                                                                                                    	at java.net.PlainSocketImpl.socketConnect(PlainSocketImpl.java:142) 
                                                                                                    	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:390) 
                                                                                                    	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:230) 
                                                                                                    	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:212) 
                                                                                                    	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:436) 
                                                                                                    	at java.net.Socket.connect(Socket.java:646) 
                                                                                                    	at com.neovisionaries.ws.client.SocketInitiator$SocketRacer.run(SocketInitiator.java:126) 
2025-08-18 13:12:51.029  4136-4664  WebSocket               com.dspread.mdm.service              I  🔧 reconnect() 被调用，调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onConnectError:675
2025-08-18 13:12:51.035  4136-4664  WebSocket               com.dspread.mdm.service              I  🔧 开始重连 WebSocket... (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onConnectError:675)
2025-08-18 13:12:51.046  4136-4664  WebSocket               com.dspread.mdm.service              I  🔧 使用Provisioning配置的重连策略: delayPolicy=30, delaySwitch=1, delayTime=900
2025-08-18 13:12:51.053  4136-4664  WebSocket               com.dspread.mdm.service              I  🔧 当前断开记录数: 0, 延迟开关: 1
2025-08-18 13:12:51.060  4136-4664  WebSocket               com.dspread.mdm.service              I  🔧 重连策略计算完成: 第13次重连，间隔15000ms (15秒)
2025-08-18 13:12:51.066  4136-4664  WebSocket               com.dspread.mdm.service              I  🔧 开始第13次重连，间隔15000ms (15秒) (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onConnectError:675)
2025-08-18 13:12:52.280  4136-4136  Thread-15               com.dspread.mdm.service              W  type=1400 audit(0.0:226): avc:  denied  { getattr } for  path="/data/cache/recovery" dev="mmcblk0p47" ino=193 scontext=u:r:system_app:s0 tcontext=u:object_r:cache_recovery_file:s0 tclass=dir permissive=0
2025-08-18 13:12:52.280  4136-4136  Thread-15               com.dspread.mdm.service              W  type=1400 audit(0.0:227): avc:  denied  { getattr } for  path="/dev/block/platform/bootdevice/by-name" dev="tmpfs" ino=7267 scontext=u:r:system_app:s0 tcontext=u:object_r:block_device:s0 tclass=dir permissive=0
2025-08-18 13:12:57.290  4136-4136  Thread-15               com.dspread.mdm.service              W  type=1400 audit(0.0:228): avc:  denied  { getattr } for  path="/data/cache/recovery" dev="mmcblk0p47" ino=193 scontext=u:r:system_app:s0 tcontext=u:object_r:cache_recovery_file:s0 tclass=dir permissive=0
2025-08-18 13:12:57.290  4136-4136  Thread-15               com.dspread.mdm.service              W  type=1400 audit(0.0:229): avc:  denied  { getattr } for  path="/dev/block/platform/bootdevice/by-name" dev="tmpfs" ino=7267 scontext=u:r:system_app:s0 tcontext=u:object_r:block_device:s0 tclass=dir permissive=0
2025-08-18 13:13:02.290  4136-4136  Thread-15               com.dspread.mdm.service              W  type=1400 audit(0.0:230): avc:  denied  { getattr } for  path="/data/cache/recovery" dev="mmcblk0p47" ino=193 scontext=u:r:system_app:s0 tcontext=u:object_r:cache_recovery_file:s0 tclass=dir permissive=0
2025-08-18 13:13:02.290  4136-4136  Thread-15               com.dspread.mdm.service              W  type=1400 audit(0.0:231): avc:  denied  { getattr } for  path="/dev/block/platform/bootdevice/by-name" dev="tmpfs" ino=7267 scontext=u:r:system_app:s0 tcontext=u:object_r:block_device:s0 tclass=dir permissive=0
2025-08-18 13:13:06.089  4136-4136  WebSocket               com.dspread.mdm.service              I  🔧 重连 WebSocket: ws://***********:8080/status/websocket/register?serialNo=MDEzNTQwOTAyMDI1MDMwNTAzOTk=&publicKey=TUlHZk1BMEdDU3FHU0liM0RRRUJBUVVBQTRHTkFEQ0JpUUtCZ1FETHNEZVQyMEU0NXdBeDN1QkpkRG5wR0kxUlFVcytOa1NVNHpDSmdVaU45Yy9vdURXMlR6ZVZHVXNhaFhyeVdBVU9MUmd2UjArQld2eDJ3K2VsdmJXbEIrdXN0ZnJyU0JMY3gyZ1VrdkdIRjRhNU5sNnprcVJjZEc5bnZuRUZTclArcVR0a0J3bnhQb2FnMldydldIS1Z1eTE0bjVGVkZWWGU4czBjRmdoeTFRSURBUUFC&query=0&msgVer=3&timestamp=1755493986075&signature=qBH8hBP4ExEFPARTy0boFjOslueTqrXOnorhkECJSJkdXAFlt4/9kfpKanJoaOuYFEuQN0JgUl/rVFT+VoKRSmGQ9edIj7gFA6H5B8O0VcUDdZIv2tlqlbudQRCUQ+EdU2/rcGk35hQY9MyRbodoKLJbv/lo4oQhcXH7WMZvZpk=
2025-08-18 13:13:06.095  4136-4136  WebSocket               com.dspread.mdm.service              I  🔧 未检测到系统代理设置
2025-08-18 13:13:06.114  4136-4713  TrafficStats            com.dspread.mdm.service              D  tagSocket(116) with statsTag=0xffffffff, statsUid=-1
2025-08-18 13:13:06.127  4136-4712  WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误 (Ask Gemini)
                                                                                                    com.neovisionaries.ws.client.WebSocketException: Failed to connect to '***********:8080': failed to connect to /*********** (port 8080) from /:: (port 0) after 15000ms: connect failed: ENETUNREACH (Network is unreachable)
                                                                                                    	at com.neovisionaries.ws.client.SocketConnector.connectSocket(SocketConnector.java:126)
                                                                                                    	at com.neovisionaries.ws.client.SocketConnector.doConnect(SocketConnector.java:238)
                                                                                                    	at com.neovisionaries.ws.client.SocketConnector.connect(SocketConnector.java:189)
                                                                                                    	at com.neovisionaries.ws.client.WebSocket.connect(WebSocket.java:2351)
                                                                                                    	at com.neovisionaries.ws.client.ConnectThread.runMain(ConnectThread.java:32)
                                                                                                    	at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45)
                                                                                                    Caused by: java.net.ConnectException: failed to connect to /*********** (port 8080) from /:: (port 0) after 15000ms: connect failed: ENETUNREACH (Network is unreachable)
                                                                                                    	at libcore.io.IoBridge.connect(IoBridge.java:187)
                                                                                                    	at java.net.PlainSocketImpl.socketConnect(PlainSocketImpl.java:142)
                                                                                                    	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:390)
                                                                                                    	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:230)
                                                                                                    	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:212)
                                                                                                    	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:436)
                                                                                                    	at java.net.Socket.connect(Socket.java:646)
                                                                                                    	at com.neovisionaries.ws.client.SocketInitiator$SocketRacer.run(SocketInitiator.java:126)
                                                                                                    Caused by: android.system.ErrnoException: connect failed: ENETUNREACH (Network is unreachable)
                                                                                                    	at libcore.io.Linux.connect(Native Method)
                                                                                                    	at libcore.io.ForwardingOs.connect(ForwardingOs.java:201)
                                                                                                    	at libcore.io.BlockGuardOs.connect(BlockGuardOs.java:158)
                                                                                                    	at libcore.io.ForwardingOs.connect(ForwardingOs.java:201)
                                                                                                    	at libcore.io.IoBridge.connectErrno(IoBridge.java:218)
                                                                                                    	at libcore.io.IoBridge.connect(IoBridge.java:179)
                                                                                                    	at java.net.PlainSocketImpl.socketConnect(PlainSocketImpl.java:142) 
                                                                                                    	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:390) 
                                                                                                    	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:230) 
                                                                                                    	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:212) 
                                                                                                    	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:436) 
                                                                                                    	at java.net.Socket.connect(Socket.java:646) 
                                                                                                    	at com.neovisionaries.ws.client.SocketInitiator$SocketRacer.run(SocketInitiator.java:126) 
2025-08-18 13:13:06.136  4136-4712  WebSocket               com.dspread.mdm.service              I  🔧 onError: 不触发重连，等待 onDisconnected 处理
2025-08-18 13:13:06.150  4136-4712  WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误 (Ask Gemini)
                                                                                                    com.neovisionaries.ws.client.WebSocketException: Failed to connect to '***********:8080': failed to connect to /*********** (port 8080) from /:: (port 0) after 15000ms: connect failed: ENETUNREACH (Network is unreachable)
                                                                                                    	at com.neovisionaries.ws.client.SocketConnector.connectSocket(SocketConnector.java:126)
                                                                                                    	at com.neovisionaries.ws.client.SocketConnector.doConnect(SocketConnector.java:238)
                                                                                                    	at com.neovisionaries.ws.client.SocketConnector.connect(SocketConnector.java:189)
                                                                                                    	at com.neovisionaries.ws.client.WebSocket.connect(WebSocket.java:2351)
                                                                                                    	at com.neovisionaries.ws.client.ConnectThread.runMain(ConnectThread.java:32)
                                                                                                    	at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45)
                                                                                                    Caused by: java.net.ConnectException: failed to connect to /*********** (port 8080) from /:: (port 0) after 15000ms: connect failed: ENETUNREACH (Network is unreachable)
                                                                                                    	at libcore.io.IoBridge.connect(IoBridge.java:187)
                                                                                                    	at java.net.PlainSocketImpl.socketConnect(PlainSocketImpl.java:142)
                                                                                                    	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:390)
                                                                                                    	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:230)
                                                                                                    	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:212)
                                                                                                    	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:436)
                                                                                                    	at java.net.Socket.connect(Socket.java:646)
                                                                                                    	at com.neovisionaries.ws.client.SocketInitiator$SocketRacer.run(SocketInitiator.java:126)
                                                                                                    Caused by: android.system.ErrnoException: connect failed: ENETUNREACH (Network is unreachable)
                                                                                                    	at libcore.io.Linux.connect(Native Method)
                                                                                                    	at libcore.io.ForwardingOs.connect(ForwardingOs.java:201)
                                                                                                    	at libcore.io.BlockGuardOs.connect(BlockGuardOs.java:158)
                                                                                                    	at libcore.io.ForwardingOs.connect(ForwardingOs.java:201)
                                                                                                    	at libcore.io.IoBridge.connectErrno(IoBridge.java:218)
                                                                                                    	at libcore.io.IoBridge.connect(IoBridge.java:179)
                                                                                                    	at java.net.PlainSocketImpl.socketConnect(PlainSocketImpl.java:142) 
                                                                                                    	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:390) 
                                                                                                    	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:230) 
                                                                                                    	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:212) 
                                                                                                    	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:436) 
                                                                                                    	at java.net.Socket.connect(Socket.java:646) 
                                                                                                    	at com.neovisionaries.ws.client.SocketInitiator$SocketRacer.run(SocketInitiator.java:126) 
2025-08-18 13:13:06.158  4136-4712  WebSocket               com.dspread.mdm.service              I  🔧 reconnect() 被调用，调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onConnectError:675
2025-08-18 13:13:06.164  4136-4712  WebSocket               com.dspread.mdm.service              I  🔧 开始重连 WebSocket... (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onConnectError:675)
2025-08-18 13:13:06.172  4136-4712  WebSocket               com.dspread.mdm.service              I  🔧 使用Provisioning配置的重连策略: delayPolicy=30, delaySwitch=1, delayTime=900
2025-08-18 13:13:06.178  4136-4712  WebSocket               com.dspread.mdm.service              I  🔧 当前断开记录数: 0, 延迟开关: 1
2025-08-18 13:13:06.184  4136-4712  WebSocket               com.dspread.mdm.service              I  🔧 重连策略计算完成: 第14次重连，间隔15000ms (15秒)
2025-08-18 13:13:06.190  4136-4712  WebSocket               com.dspread.mdm.service              I  🔧 开始第14次重连，间隔15000ms (15秒) (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onConnectError:675)
2025-08-18 13:13:07.300  4136-4136  Thread-15               com.dspread.mdm.service              W  type=1400 audit(0.0:232): avc:  denied  { getattr } for  path="/data/cache/recovery" dev="mmcblk0p47" ino=193 scontext=u:r:system_app:s0 tcontext=u:object_r:cache_recovery_file:s0 tclass=dir permissive=0
2025-08-18 13:13:07.300  4136-4136  Thread-15               com.dspread.mdm.service              W  type=1400 audit(0.0:233): avc:  denied  { getattr } for  path="/dev/block/platform/bootdevice/by-name" dev="tmpfs" ino=7267 scontext=u:r:system_app:s0 tcontext=u:object_r:block_device:s0 tclass=dir permissive=0
2025-08-18 13:13:09.087  4136-4136  Platform                com.dspread.mdm.service              D  🔧 WakeLockManager WakeLock不存在或未持有: ServiceKeepAlive
2025-08-18 13:13:09.093  4136-4136  Service                 com.dspread.mdm.service              D  🔧 ServiceKeepAliveService 保活任务完成，释放WakeLock
2025-08-18 13:13:12.300  4136-4136  Thread-15               com.dspread.mdm.service              W  type=1400 audit(0.0:234): avc:  denied  { getattr } for  path="/data/cache/recovery" dev="mmcblk0p47" ino=193 scontext=u:r:system_app:s0 tcontext=u:object_r:cache_recovery_file:s0 tclass=dir permissive=0
2025-08-18 13:13:12.300  4136-4136  Thread-15               com.dspread.mdm.service              W  type=1400 audit(0.0:235): avc:  denied  { getattr } for  path="/dev/block/platform/bootdevice/by-name" dev="tmpfs" ino=7267 scontext=u:r:system_app:s0 tcontext=u:object_r:block_device:s0 tclass=dir permissive=0
2025-08-18 13:13:17.300  4136-4136  Thread-15               com.dspread.mdm.service              W  type=1400 audit(0.0:236): avc:  denied  { getattr } for  path="/data/cache/recovery" dev="mmcblk0p47" ino=193 scontext=u:r:system_app:s0 tcontext=u:object_r:cache_recovery_file:s0 tclass=dir permissive=0
2025-08-18 13:13:17.300  4136-4136  Thread-15               com.dspread.mdm.service              W  type=1400 audit(0.0:237): avc:  denied  { getattr } for  path="/dev/block/platform/bootdevice/by-name" dev="tmpfs" ino=7267 scontext=u:r:system_app:s0 tcontext=u:object_r:block_device:s0 tclass=dir permissive=0
2025-08-18 13:13:21.225  4136-4136  WebSocket               com.dspread.mdm.service              I  🔧 重连 WebSocket: ws://***********:8080/status/websocket/register?serialNo=MDEzNTQwOTAyMDI1MDMwNTAzOTk=&publicKey=TUlHZk1BMEdDU3FHU0liM0RRRUJBUVVBQTRHTkFEQ0JpUUtCZ1FETHNEZVQyMEU0NXdBeDN1QkpkRG5wR0kxUlFVcytOa1NVNHpDSmdVaU45Yy9vdURXMlR6ZVZHVXNhaFhyeVdBVU9MUmd2UjArQld2eDJ3K2VsdmJXbEIrdXN0ZnJyU0JMY3gyZ1VrdkdIRjRhNU5sNnprcVJjZEc5bnZuRUZTclArcVR0a0J3bnhQb2FnMldydldIS1Z1eTE0bjVGVkZWWGU4czBjRmdoeTFRSURBUUFC&query=0&msgVer=3&timestamp=1755494001212&signature=CAFPNgI/WPXqDhECvGZbzriJZ4yHhFBjqdw+8EuTNC6Akx3VKT2TSPO09WHyh5iD5xnWjZdWg2StQ42ltrxlym9XGsQp7F0Nbxqu0HBfFO3hlHN5KcJF3DhBqdwU1/+X1yDcqEC9BRVFiNN+geXLl+RExapxXOZyLguCAnbpglA=
2025-08-18 13:13:21.231  4136-4136  WebSocket               com.dspread.mdm.service              I  🔧 未检测到系统代理设置
2025-08-18 13:13:21.250  4136-4758  TrafficStats            com.dspread.mdm.service              D  tagSocket(116) with statsTag=0xffffffff, statsUid=-1
2025-08-18 13:13:21.264  4136-4757  WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误 (Ask Gemini)
                                                                                                    com.neovisionaries.ws.client.WebSocketException: Failed to connect to '***********:8080': failed to connect to /*********** (port 8080) from /:: (port 0) after 15000ms: connect failed: ENETUNREACH (Network is unreachable)
                                                                                                    	at com.neovisionaries.ws.client.SocketConnector.connectSocket(SocketConnector.java:126)
                                                                                                    	at com.neovisionaries.ws.client.SocketConnector.doConnect(SocketConnector.java:238)
                                                                                                    	at com.neovisionaries.ws.client.SocketConnector.connect(SocketConnector.java:189)
                                                                                                    	at com.neovisionaries.ws.client.WebSocket.connect(WebSocket.java:2351)
                                                                                                    	at com.neovisionaries.ws.client.ConnectThread.runMain(ConnectThread.java:32)
                                                                                                    	at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45)
                                                                                                    Caused by: java.net.ConnectException: failed to connect to /*********** (port 8080) from /:: (port 0) after 15000ms: connect failed: ENETUNREACH (Network is unreachable)
                                                                                                    	at libcore.io.IoBridge.connect(IoBridge.java:187)
                                                                                                    	at java.net.PlainSocketImpl.socketConnect(PlainSocketImpl.java:142)
                                                                                                    	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:390)
                                                                                                    	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:230)
                                                                                                    	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:212)
                                                                                                    	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:436)
                                                                                                    	at java.net.Socket.connect(Socket.java:646)
                                                                                                    	at com.neovisionaries.ws.client.SocketInitiator$SocketRacer.run(SocketInitiator.java:126)
                                                                                                    Caused by: android.system.ErrnoException: connect failed: ENETUNREACH (Network is unreachable)
                                                                                                    	at libcore.io.Linux.connect(Native Method)
                                                                                                    	at libcore.io.ForwardingOs.connect(ForwardingOs.java:201)
                                                                                                    	at libcore.io.BlockGuardOs.connect(BlockGuardOs.java:158)
                                                                                                    	at libcore.io.ForwardingOs.connect(ForwardingOs.java:201)
                                                                                                    	at libcore.io.IoBridge.connectErrno(IoBridge.java:218)
                                                                                                    	at libcore.io.IoBridge.connect(IoBridge.java:179)
                                                                                                    	at java.net.PlainSocketImpl.socketConnect(PlainSocketImpl.java:142) 
                                                                                                    	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:390) 
                                                                                                    	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:230) 
                                                                                                    	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:212) 
                                                                                                    	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:436) 
                                                                                                    	at java.net.Socket.connect(Socket.java:646) 
                                                                                                    	at com.neovisionaries.ws.client.SocketInitiator$SocketRacer.run(SocketInitiator.java:126) 
2025-08-18 13:13:21.273  4136-4757  WebSocket               com.dspread.mdm.service              I  🔧 onError: 不触发重连，等待 onDisconnected 处理
2025-08-18 13:13:21.286  4136-4757  WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误 (Ask Gemini)
                                                                                                    com.neovisionaries.ws.client.WebSocketException: Failed to connect to '***********:8080': failed to connect to /*********** (port 8080) from /:: (port 0) after 15000ms: connect failed: ENETUNREACH (Network is unreachable)
                                                                                                    	at com.neovisionaries.ws.client.SocketConnector.connectSocket(SocketConnector.java:126)
                                                                                                    	at com.neovisionaries.ws.client.SocketConnector.doConnect(SocketConnector.java:238)
                                                                                                    	at com.neovisionaries.ws.client.SocketConnector.connect(SocketConnector.java:189)
                                                                                                    	at com.neovisionaries.ws.client.WebSocket.connect(WebSocket.java:2351)
                                                                                                    	at com.neovisionaries.ws.client.ConnectThread.runMain(ConnectThread.java:32)
                                                                                                    	at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45)
                                                                                                    Caused by: java.net.ConnectException: failed to connect to /*********** (port 8080) from /:: (port 0) after 15000ms: connect failed: ENETUNREACH (Network is unreachable)
                                                                                                    	at libcore.io.IoBridge.connect(IoBridge.java:187)
                                                                                                    	at java.net.PlainSocketImpl.socketConnect(PlainSocketImpl.java:142)
                                                                                                    	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:390)
                                                                                                    	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:230)
                                                                                                    	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:212)
                                                                                                    	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:436)
                                                                                                    	at java.net.Socket.connect(Socket.java:646)
                                                                                                    	at com.neovisionaries.ws.client.SocketInitiator$SocketRacer.run(SocketInitiator.java:126)
                                                                                                    Caused by: android.system.ErrnoException: connect failed: ENETUNREACH (Network is unreachable)
                                                                                                    	at libcore.io.Linux.connect(Native Method)
                                                                                                    	at libcore.io.ForwardingOs.connect(ForwardingOs.java:201)
                                                                                                    	at libcore.io.BlockGuardOs.connect(BlockGuardOs.java:158)
                                                                                                    	at libcore.io.ForwardingOs.connect(ForwardingOs.java:201)
                                                                                                    	at libcore.io.IoBridge.connectErrno(IoBridge.java:218)
                                                                                                    	at libcore.io.IoBridge.connect(IoBridge.java:179)
                                                                                                    	at java.net.PlainSocketImpl.socketConnect(PlainSocketImpl.java:142) 
                                                                                                    	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:390) 
                                                                                                    	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:230) 
                                                                                                    	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:212) 
                                                                                                    	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:436) 
                                                                                                    	at java.net.Socket.connect(Socket.java:646) 
                                                                                                    	at com.neovisionaries.ws.client.SocketInitiator$SocketRacer.run(SocketInitiator.java:126) 
2025-08-18 13:13:21.294  4136-4757  WebSocket               com.dspread.mdm.service              I  🔧 reconnect() 被调用，调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onConnectError:675
2025-08-18 13:13:21.300  4136-4757  WebSocket               com.dspread.mdm.service              I  🔧 开始重连 WebSocket... (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onConnectError:675)
2025-08-18 13:13:21.307  4136-4757  WebSocket               com.dspread.mdm.service              I  🔧 使用Provisioning配置的重连策略: delayPolicy=30, delaySwitch=1, delayTime=900
2025-08-18 13:13:21.313  4136-4757  WebSocket               com.dspread.mdm.service              I  🔧 当前断开记录数: 0, 延迟开关: 1
2025-08-18 13:13:21.319  4136-4757  WebSocket               com.dspread.mdm.service              I  🔧 重连策略计算完成: 第15次重连，间隔15000ms (15秒)
2025-08-18 13:13:21.325  4136-4757  WebSocket               com.dspread.mdm.service              I  🔧 开始第15次重连，间隔15000ms (15秒) (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onConnectError:675)
2025-08-18 13:13:22.300  4136-4136  Thread-15               com.dspread.mdm.service              W  type=1400 audit(0.0:238): avc:  denied  { getattr } for  path="/data/cache/recovery" dev="mmcblk0p47" ino=193 scontext=u:r:system_app:s0 tcontext=u:object_r:cache_recovery_file:s0 tclass=dir permissive=0
2025-08-18 13:13:22.300  4136-4136  Thread-15               com.dspread.mdm.service              W  type=1400 audit(0.0:239): avc:  denied  { getattr } for  path="/dev/block/platform/bootdevice/by-name" dev="tmpfs" ino=7267 scontext=u:r:system_app:s0 tcontext=u:object_r:block_device:s0 tclass=dir permissive=0
2025-08-18 13:13:27.310  4136-4136  Thread-15               com.dspread.mdm.service              W  type=1400 audit(0.0:240): avc:  denied  { getattr } for  path="/data/cache/recovery" dev="mmcblk0p47" ino=193 scontext=u:r:system_app:s0 tcontext=u:object_r:cache_recovery_file:s0 tclass=dir permissive=0
2025-08-18 13:13:27.310  4136-4136  Thread-15               com.dspread.mdm.service              W  type=1400 audit(0.0:241): avc:  denied  { getattr } for  path="/dev/block/platform/bootdevice/by-name" dev="tmpfs" ino=7267 scontext=u:r:system_app:s0 tcontext=u:object_r:block_device:s0 tclass=dir permissive=0
2025-08-18 13:13:27.714  4136-4136  Receiver                com.dspread.mdm.service              D  🔧 电池状态更新: 电池状态: 电量=73%, 温度=28.6°C, 健康=2, 充电=是, 低电量=否, 耗尽=否
2025-08-18 13:13:32.310  4136-4136  Thread-15               com.dspread.mdm.service              W  type=1400 audit(0.0:242): avc:  denied  { getattr } for  path="/data/cache/recovery" dev="mmcblk0p47" ino=193 scontext=u:r:system_app:s0 tcontext=u:object_r:cache_recovery_file:s0 tclass=dir permissive=0
2025-08-18 13:13:32.310  4136-4136  Thread-15               com.dspread.mdm.service              W  type=1400 audit(0.0:243): avc:  denied  { getattr } for  path="/dev/block/platform/bootdevice/by-name" dev="tmpfs" ino=7267 scontext=u:r:system_app:s0 tcontext=u:object_r:block_device:s0 tclass=dir permissive=0
2025-08-18 13:13:36.348  4136-4136  WebSocket               com.dspread.mdm.service              I  🔧 重连 WebSocket: ws://***********:8080/status/websocket/register?serialNo=MDEzNTQwOTAyMDI1MDMwNTAzOTk=&publicKey=TUlHZk1BMEdDU3FHU0liM0RRRUJBUVVBQTRHTkFEQ0JpUUtCZ1FETHNEZVQyMEU0NXdBeDN1QkpkRG5wR0kxUlFVcytOa1NVNHpDSmdVaU45Yy9vdURXMlR6ZVZHVXNhaFhyeVdBVU9MUmd2UjArQld2eDJ3K2VsdmJXbEIrdXN0ZnJyU0JMY3gyZ1VrdkdIRjRhNU5sNnprcVJjZEc5bnZuRUZTclArcVR0a0J3bnhQb2FnMldydldIS1Z1eTE0bjVGVkZWWGU4czBjRmdoeTFRSURBUUFC&query=0&msgVer=3&timestamp=1755494016335&signature=BIOm0THxfb7cYYJpJ118tiSlhNj11ivZ4Z9SgoUB+11DFm6ySYYdgYp46x/fAAhEEkr8/0UVmlpOyqC4PxkPrD8orTp7h97OvGx0mAzU3mTORmpKtS6jfctJ+svQ4gM4JglayDwAASq+tyEC2FXyeqgrUfFVkDfK+6Jro1kK3kQ=
2025-08-18 13:13:36.354  4136-4136  WebSocket               com.dspread.mdm.service              I  🔧 未检测到系统代理设置
2025-08-18 13:13:36.374  4136-4802  TrafficStats            com.dspread.mdm.service              D  tagSocket(116) with statsTag=0xffffffff, statsUid=-1
2025-08-18 13:13:36.387  4136-4801  WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误 (Ask Gemini)
                                                                                                    com.neovisionaries.ws.client.WebSocketException: Failed to connect to '***********:8080': failed to connect to /*********** (port 8080) from /:: (port 0) after 15000ms: connect failed: ENETUNREACH (Network is unreachable)
                                                                                                    	at com.neovisionaries.ws.client.SocketConnector.connectSocket(SocketConnector.java:126)
                                                                                                    	at com.neovisionaries.ws.client.SocketConnector.doConnect(SocketConnector.java:238)
                                                                                                    	at com.neovisionaries.ws.client.SocketConnector.connect(SocketConnector.java:189)
                                                                                                    	at com.neovisionaries.ws.client.WebSocket.connect(WebSocket.java:2351)
                                                                                                    	at com.neovisionaries.ws.client.ConnectThread.runMain(ConnectThread.java:32)
                                                                                                    	at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45)
                                                                                                    Caused by: java.net.ConnectException: failed to connect to /*********** (port 8080) from /:: (port 0) after 15000ms: connect failed: ENETUNREACH (Network is unreachable)
                                                                                                    	at libcore.io.IoBridge.connect(IoBridge.java:187)
                                                                                                    	at java.net.PlainSocketImpl.socketConnect(PlainSocketImpl.java:142)
                                                                                                    	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:390)
                                                                                                    	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:230)
                                                                                                    	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:212)
                                                                                                    	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:436)
                                                                                                    	at java.net.Socket.connect(Socket.java:646)
                                                                                                    	at com.neovisionaries.ws.client.SocketInitiator$SocketRacer.run(SocketInitiator.java:126)
                                                                                                    Caused by: android.system.ErrnoException: connect failed: ENETUNREACH (Network is unreachable)
                                                                                                    	at libcore.io.Linux.connect(Native Method)
                                                                                                    	at libcore.io.ForwardingOs.connect(ForwardingOs.java:201)
                                                                                                    	at libcore.io.BlockGuardOs.connect(BlockGuardOs.java:158)
                                                                                                    	at libcore.io.ForwardingOs.connect(ForwardingOs.java:201)
                                                                                                    	at libcore.io.IoBridge.connectErrno(IoBridge.java:218)
                                                                                                    	at libcore.io.IoBridge.connect(IoBridge.java:179)
                                                                                                    	at java.net.PlainSocketImpl.socketConnect(PlainSocketImpl.java:142) 
                                                                                                    	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:390) 
                                                                                                    	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:230) 
                                                                                                    	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:212) 
                                                                                                    	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:436) 
                                                                                                    	at java.net.Socket.connect(Socket.java:646) 
                                                                                                    	at com.neovisionaries.ws.client.SocketInitiator$SocketRacer.run(SocketInitiator.java:126) 
2025-08-18 13:13:36.394  4136-4801  WebSocket               com.dspread.mdm.service              I  🔧 onError: 不触发重连，等待 onDisconnected 处理
2025-08-18 13:13:36.408  4136-4801  WebSocket               com.dspread.mdm.service              E  ❌ WebSocket 连接错误 (Ask Gemini)
                                                                                                    com.neovisionaries.ws.client.WebSocketException: Failed to connect to '***********:8080': failed to connect to /*********** (port 8080) from /:: (port 0) after 15000ms: connect failed: ENETUNREACH (Network is unreachable)
                                                                                                    	at com.neovisionaries.ws.client.SocketConnector.connectSocket(SocketConnector.java:126)
                                                                                                    	at com.neovisionaries.ws.client.SocketConnector.doConnect(SocketConnector.java:238)
                                                                                                    	at com.neovisionaries.ws.client.SocketConnector.connect(SocketConnector.java:189)
                                                                                                    	at com.neovisionaries.ws.client.WebSocket.connect(WebSocket.java:2351)
                                                                                                    	at com.neovisionaries.ws.client.ConnectThread.runMain(ConnectThread.java:32)
                                                                                                    	at com.neovisionaries.ws.client.WebSocketThread.run(WebSocketThread.java:45)
                                                                                                    Caused by: java.net.ConnectException: failed to connect to /*********** (port 8080) from /:: (port 0) after 15000ms: connect failed: ENETUNREACH (Network is unreachable)
                                                                                                    	at libcore.io.IoBridge.connect(IoBridge.java:187)
                                                                                                    	at java.net.PlainSocketImpl.socketConnect(PlainSocketImpl.java:142)
                                                                                                    	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:390)
                                                                                                    	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:230)
                                                                                                    	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:212)
                                                                                                    	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:436)
                                                                                                    	at java.net.Socket.connect(Socket.java:646)
                                                                                                    	at com.neovisionaries.ws.client.SocketInitiator$SocketRacer.run(SocketInitiator.java:126)
                                                                                                    Caused by: android.system.ErrnoException: connect failed: ENETUNREACH (Network is unreachable)
                                                                                                    	at libcore.io.Linux.connect(Native Method)
                                                                                                    	at libcore.io.ForwardingOs.connect(ForwardingOs.java:201)
                                                                                                    	at libcore.io.BlockGuardOs.connect(BlockGuardOs.java:158)
                                                                                                    	at libcore.io.ForwardingOs.connect(ForwardingOs.java:201)
                                                                                                    	at libcore.io.IoBridge.connectErrno(IoBridge.java:218)
                                                                                                    	at libcore.io.IoBridge.connect(IoBridge.java:179)
                                                                                                    	at java.net.PlainSocketImpl.socketConnect(PlainSocketImpl.java:142) 
                                                                                                    	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:390) 
                                                                                                    	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:230) 
                                                                                                    	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:212) 
                                                                                                    	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:436) 
                                                                                                    	at java.net.Socket.connect(Socket.java:646) 
                                                                                                    	at com.neovisionaries.ws.client.SocketInitiator$SocketRacer.run(SocketInitiator.java:126) 
2025-08-18 13:13:36.416  4136-4801  WebSocket               com.dspread.mdm.service              I  🔧 reconnect() 被调用，调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onConnectError:675
2025-08-18 13:13:36.422  4136-4801  WebSocket               com.dspread.mdm.service              I  🔧 开始重连 WebSocket... (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onConnectError:675)
2025-08-18 13:13:36.432  4136-4801  WebSocket               com.dspread.mdm.service              I  🔧 使用Provisioning配置的重连策略: delayPolicy=30, delaySwitch=1, delayTime=900
2025-08-18 13:13:36.439  4136-4801  WebSocket               com.dspread.mdm.service              I  🔧 当前断开记录数: 0, 延迟开关: 1
2025-08-18 13:13:36.445  4136-4801  WebSocket               com.dspread.mdm.service              I  🔧 重连策略计算完成: 第16次重连，间隔15000ms (15秒)
2025-08-18 13:13:36.452  4136-4801  WebSocket               com.dspread.mdm.service              I  🔧 开始第16次重连，间隔15000ms (15秒) (调用者: com.dspread.mdm.service.network.websocket.connection.WsConnectionManager$createWebSocketListener$1.onConnectError:675)
2025-08-18 13:13:37.310  4136-4136  Thread-15               com.dspread.mdm.service              W  type=1400 audit(0.0:244): avc:  denied  { getattr } for  path="/data/cache/recovery" dev="mmcblk0p47" ino=193 scontext=u:r:system_app:s0 tcontext=u:object_r:cache_recovery_file:s0 tclass=dir permissive=0
2025-08-18 13:13:37.310  4136-4136  Thread-15               com.dspread.mdm.service              W  type=1400 audit(0.0:245): avc:  denied  { getattr } for  path="/dev/block/platform/bootdevice/by-name" dev="tmpfs" ino=7267 scontext=u:r:system_app:s0 tcontext=u:object_r:block_device:s0 tclass=dir permissive=0
---------------------------- PROCESS STARTED (4821) for package com.dspread.mdm.service ----------------------------
---------------------------- PROCESS ENDED (4821) for package com.dspread.mdm.service ----------------------------
2025-08-18 13:13:42.871  4136-4334  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineManager 检测到UpdateEngine完成
2025-08-18 13:13:42.889  4136-4334  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineManager UpdateEngine日志监控结束
2025-08-18 13:13:42.917  4136-4136  Common                  com.dspread.mdm.service              D  🔧 OsUpdateTest: 升级进度: 完成: 升级完成 (100%)
2025-08-18 13:13:42.930  4136-4136  Common                  com.dspread.mdm.service              D  🔧 OsUpdateTest: 升级完成: UpdateEngine升级完成，设备将重启
2025-08-18 13:13:43.128  4136-4136  VRI[OsUpda...tActivity] com.dspread.mdm.service              D  hardware acceleration = true, forceHwAccelerated = false
2025-08-18 13:13:43.133   783-916   BufferQueueDebug        surfaceflinger                       E  [de7cad4 com.dspread.mdm.service/com.dspread.mdm.service.ui.activity.OsUpdateTestActivity#343](this:0xa67bbc40,id:-1,api:0,p:-1,c:-1) id info cannot be read from 'de7cad4 com.dspread.mdm.service/com.dspread.mdm.service.ui.activity.OsUpdateTestActivity#343'
2025-08-18 13:13:43.149  4136-4136  InputTransport          com.dspread.mdm.service              D  Create ARC handle: 0xabb3f920
2025-08-18 13:13:43.149  4136-4136  InputEventReceiver      com.dspread.mdm.service              D  Input log is disabled in InputEventReceiver.
2025-08-18 13:13:43.150  4136-4136  InputTransport          com.dspread.mdm.service              D  Input log is disabled in InputChannel.
2025-08-18 13:13:43.152  4136-4136  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineUpgradeStrategy UpdateEngine升级完成: 成功
2025-08-18 13:13:43.158  4136-4136  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineUpgradeStrategy 安排5秒后自动重启
2025-08-18 13:13:43.263   783-916   BufferQueueDebug        surfaceflinger                       E  [com.dspread.mdm.service/com.dspread.mdm.service.ui.activity.OsUpdateTestActivity#346](this:0xa6768c40,id:-1,api:0,p:-1,c:-1) id info cannot be read from 'com.dspread.mdm.service/com.dspread.mdm.service.ui.activity.OsUpdateTestActivity#346'
2025-08-18 13:13:43.275  4136-4136  BufferQueueConsumer     com.dspread.mdm.service              D  [](id:102800000004,api:0,p:-1,c:4136) connect: controlledByApp=false
2025-08-18 13:13:43.282  4136-4169  OpenGLRenderer          com.dspread.mdm.service              E  Unable to match the desired swap behavior.
2025-08-18 13:13:43.371  4136-4169  BLASTBufferQueue        com.dspread.mdm.service              D  [VRI[OsUpdateTestActivity]#4](f:0,a:1) acquireNextBufferLocked size=542x391 mFrameNumber=1 applyTransaction=true mTimestamp=377755447636(auto) mPendingTransactions.size=0 graphicBufferId=17763984736301 transform=3
2025-08-18 13:13:43.377   783-916   BufferQueueDebug        surfaceflinger                       E  [Surface(name=de7cad4 com.dspread.mdm.service/com.dspread.mdm.service.ui.activity.OsUpdateTestActivity)/@0x30830b1 - animation-leash of window_animation#347](this:0xa674cc40,id:-1,api:0,p:-1,c:-1) id info cannot be read from 'Surface(name=de7cad4 com.dspread.mdm.service/com.dspread.mdm.service.ui.activity.OsUpdateTestActivity)/@0x30830b1 - animation-leash of window_animation#347'
2025-08-18 13:13:47.310  4136-4136  Thread-15               com.dspread.mdm.service              W  type=1400 audit(0.0:257): avc:  denied  { getattr } for  path="/data/cache/recovery" dev="mmcblk0p47" ino=193 scontext=u:r:system_app:s0 tcontext=u:object_r:cache_recovery_file:s0 tclass=dir permissive=0
2025-08-18 13:13:47.310  4136-4136  Thread-15               com.dspread.mdm.service              W  type=1400 audit(0.0:258): avc:  denied  { getattr } for  path="/dev/block/platform/bootdevice/by-name" dev="tmpfs" ino=7267 scontext=u:r:system_app:s0 tcontext=u:object_r:block_device:s0 tclass=dir permissive=0
2025-08-18 13:13:48.167  4136-4830  Platform                com.dspread.mdm.service              I  ℹ️ UpdateEngineUpgradeStrategy 开始执行重启...
2025-08-18 13:13:48.182  4136-4830  Platform                com.dspread.mdm.service              I  ℹ️ Attempting to reboot system, reason: UpdateEngine升级完成
2025-08-18 13:13:48.258  4136-4136  Receiver                com.dspread.mdm.service              D  🔧 系统即将关机
2025-08-18 13:13:48.266  4136-4136  WebSocket               com.dspread.mdm.service              I  🔧 C0202 被动式上送拒绝: device_usage_change (拒绝: 1)
2025-08-18 13:13:48.274  4136-4136  WebSocket               com.dspread.mdm.service              I  🔧 C0202 设备事件上送被流量控制阻止: 被动事件 'device_usage_change' 在平衡模式 - 重要变化下未启用
2025-08-18 13:13:48.282  4136-4136  WebSocket               com.dspread.mdm.service              I  🔧 C0902 主动式上送: system_shutdown (主动: 2)
2025-08-18 13:13:48.313  4136-4136  WebSocket               com.dspread.mdm.service              I  🔧 替换重复缓存消息: C0902
2025-08-18 13:13:48.322  4136-4136  WebSocket               com.dspread.mdm.service              I  🔧 消息已缓存: C0902 (缓存数量: 1)
2025-08-18 13:13:48.329  4136-4136  WebSocket               com.dspread.mdm.service              I  🔧 C0902 电池状态上送成功 (1, trigger=system_shutdown)
