# 任务状态机完整文档

## 概述

本文档详细描述了MDM服务中的任务状态机设计、状态定义、流转逻辑和异常处理机制。

## 📋 状态定义

### 基础状态
| 状态码 | 常量名 | 描述 | 类型 |
|--------|--------|------|------|
| `todo` | `TODO` | 待执行 | 初始状态 |
| `wating` | `WAITING` | 等待中 | 中间状态 |
| `W01` | `EXECUTE_WAITING` | 执行等待 | 中间状态 |

### 下载相关状态
| 状态码 | 常量名 | 描述 | 类型 |
|--------|--------|------|------|
| `A01` | `DOWNLOAD_ING` | 下载中 | 进行中 |
| `A03` | `DOWNLOAD_SUCCESS` | 下载成功 | 成功状态 |
| `A04` | `DOWNLOAD_FAILED` | 下载失败 | 失败状态 |

### 安装相关状态
| 状态码 | 常量名 | 描述 | 类型 |
|--------|--------|------|------|
| `B01` | `INSTALL_WAITING` | 安装等待 | 中间状态 |
| `B02` | `INSTALL_ING` | 安装中 | 进行中 |
| `B03` | `INSTALL_SUCCESS` | 安装成功 | 成功状态 |
| `B04` | `INSTALL_FAILED` | 安装失败 | 失败状态 |
| `B05` | `INSTALL_OVERRIDE` | 安装覆盖 | 特殊状态 |

### 更新相关状态
| 状态码 | 常量名 | 描述 | 类型 |
|--------|--------|------|------|
| `C02` | `UPDATE_ING` | 更新中 | 进行中 |
| `C03` | `UPDATE_SUCCESS` | 更新成功 | 成功状态 |
| `C04` | `UPDATE_FAILED` | 更新失败 | 失败状态 |
| `C05` | `UPDATE_DOWNGRADE_FORBIDDEN` | 更新降级禁止 | 失败状态 |

### 卸载相关状态
| 状态码 | 常量名 | 描述 | 类型 |
|--------|--------|------|------|
| `D01` | `UNINSTALL_ING` | 卸载中 | 进行中 |
| `D02` | `UNINSTALL_SUCCESS` | 卸载成功 | 成功状态 |
| `D03` | `UNINSTALL_FAILED` | 卸载失败 | 失败状态 |
| `D04` | `UNINSTALL_EXPIRE` | 卸载过期 | 失败状态 |

### 通用状态
| 状态码 | 常量名 | 描述 | 类型 |
|--------|--------|------|------|
| `F01` | `SUCCESSED` | 成功 | 成功状态 |
| `E01` | `TASK_CANCEL` | 任务取消 | 失败状态 |
| `E02` | `RELY_FAILED` | 依赖失败 | 失败状态 |
| `E03` | `LOW_BAT` | 低电量 | 失败状态 |
| `E04` | `SERVER_FAILED` | 服务器失败 | 失败状态 |
| `E05` | `FAILED` | 失败 | 失败状态 |

### 规则相关状态
| 状态码 | 常量名 | 描述 | 类型 |
|--------|--------|------|------|
| `R01` | `RULEBASED_STARTING` | 规则启动中 | 进行中 |
| `R02` | `RULEBASED_EXECUTING` | 规则执行中 | 进行中 |
| `R03` | `RULEBASED_SUCCESS` | 规则成功 | 成功状态 |
| `R04` | `RULEBASED_EXPIRED` | 规则过期 | 失败状态 |

### 新格式兼容状态
| 状态码 | 常量名 | 描述 | 类型 |
|--------|--------|------|------|
| `0` | `RESULT_SUCCESS` | 成功（新格式） | 成功状态 |
| `1` | `RESULT_FAILED` | 失败（新格式） | 失败状态 |
| `2` | `RESULT_PARTIAL` | 部分成功（新格式） | 特殊状态 |

## 状态流转图

### 安装任务流程
```
TODO → DOWNLOAD_ING → DOWNLOAD_SUCCESS → INSTALL_WAITING → INSTALL_ING → INSTALL_SUCCESS ✅
  ↓         ↓              ↓                    ↓              ↓
FAILED ← DOWNLOAD_FAILED ← INSTALL_FAILED ← INSTALL_FAILED ← INSTALL_FAILED ❌
```

### 卸载任务流程
```
TODO → UNINSTALL_ING → UNINSTALL_SUCCESS ✅
  ↓           ↓
FAILED ← UNINSTALL_FAILED ❌
```

### 更新任务流程
```
TODO → DOWNLOAD_ING → DOWNLOAD_SUCCESS → UPDATE_ING → UPDATE_SUCCESS ✅
  ↓         ↓              ↓               ↓
FAILED ← DOWNLOAD_FAILED ← UPDATE_FAILED ← UPDATE_FAILED ❌
  ↓
UPDATE_DOWNGRADE_FORBIDDEN (不允许降级)
```

## 状态判断方法

### 完成状态判断
```kotlin
fun isCompletedState(state: String): Boolean {
    return when (state) {
        INSTALL_SUCCESS, INSTALL_FAILED,
        UNINSTALL_SUCCESS, UNINSTALL_FAILED,
        UPDATE_SUCCESS, UPDATE_FAILED, UPDATE_DOWNGRADE_FORBIDDEN,
        DOWNLOAD_SUCCESS, DOWNLOAD_FAILED,
        RULEBASED_SUCCESS, RULEBASED_EXPIRED,
        SUCCESSED, FAILED,
        RESULT_SUCCESS, RESULT_FAILED -> true
        else -> false
    }
}
```

### 成功状态判断
```kotlin
fun isSuccessState(state: String): Boolean {
    return when (state) {
        INSTALL_SUCCESS, UNINSTALL_SUCCESS, UPDATE_SUCCESS,
        DOWNLOAD_SUCCESS, RULEBASED_SUCCESS,
        SUCCESSED, RESULT_SUCCESS -> true
        else -> false
    }
}
```

### 失败状态判断
```kotlin
fun isFailureState(state: String): Boolean {
    return when (state) {
        INSTALL_FAILED, UNINSTALL_FAILED, UPDATE_FAILED, UPDATE_DOWNGRADE_FORBIDDEN,
        DOWNLOAD_FAILED, RULEBASED_EXPIRED,
        FAILED, RESULT_FAILED,
        TASK_CANCEL, RELY_FAILED, LOW_BAT, SERVER_FAILED -> true
        else -> false
    }
}
```

### 进行中状态判断
```kotlin
fun isInProgressState(state: String): Boolean {
    return when (state) {
        DOWNLOAD_ING, INSTALL_ING, UPDATE_ING, UNINSTALL_ING,
        RULEBASED_STARTING, RULEBASED_EXECUTING,
        EXECUTE_WAITING, INSTALL_WAITING -> true
        else -> false
    }
}
```

## 状态管理机制

### 状态更新
- **线程安全**: 使用`@Synchronized`保护状态更新
- **精确匹配**: 通过taskId精确定位要更新的任务
- **双重存储**: 同时更新内存缓存和持久化存储

### 状态重置
- **重启时触发**: 服务重启时重置未完成的任务
- **特殊处理**: 自身更新任务在INSTALL_ING状态时不重置
- **智能判断**: 只重置执行中状态，跳过已完成状态

### 超时检测
- **检测范围**: 所有进行中状态（DOWNLOAD_ING, INSTALL_ING, UPDATE_ING, UNINSTALL_ING）
- **超时时间**: 20分钟无响应自动标记为失败
- **智能映射**: 根据当前状态自动选择对应的失败状态

## 异常处理

### 网络异常
```
任务执行中 → 网络断开 → 状态上报缓存 → 网络恢复 → 自动重发状态
```

### 超时异常
```
DOWNLOAD_ING → (20分钟无响应) → DOWNLOAD_FAILED
INSTALL_ING → (20分钟无响应) → INSTALL_FAILED
UPDATE_ING → (20分钟无响应) → UPDATE_FAILED
UNINSTALL_ING → (20分钟无响应) → UNINSTALL_FAILED
```

### 降级安装异常
```
检查应用版本 → 发现已安装更高版本 → 检查allowDowngrade → 
如果不允许降级 → UPDATE_DOWNGRADE_FORBIDDEN
如果允许降级 → 继续安装流程
```

## 🛡️ 保护机制

### 1. 线程安全保护
- 所有状态更新方法使用`@Synchronized`
- 防止并发修改导致的数据不一致

### 2. 状态串改防护
- 通过taskId精确匹配，避免误更新其他任务
- 分离的错误信息更新方法

### 3. 缓存机制
- 重要消息自动缓存（C0108任务状态上报）
- 网络恢复后自动重发缓存的状态

### 4. 超时保护
- 20分钟超时检测，防止任务永远卡住
- 定时检查机制，每10分钟检查一次

## 📝 使用示例

### 更新任务状态
```kotlin
// 安全的状态更新
WsTaskManager.updateWSTaskState(taskId, TaskStateConstants.DOWNLOAD_ING)

// 更新错误信息
WsTaskManager.updateTaskErrorMessage(taskId, "下载失败")

// 更新最后更新时间
WsTaskManager.updateTaskLastUpdateTime(taskId)
```

### 状态判断
```kotlin
// 检查任务是否完成
if (TaskStateConstants.isCompletedState(taskResult)) {
    // 任务已完成（成功或失败）
}

// 检查任务是否成功
if (TaskStateConstants.isSuccessState(taskResult)) {
    // 任务成功
}

// 检查任务是否失败
if (TaskStateConstants.isFailureState(taskResult)) {
    // 任务失败
}
```

## 最佳实践

1. **统一使用常量**: 始终使用`TaskStateConstants`中定义的常量
2. **及时状态上报**: 状态变化时立即上报到服务器
3. **错误信息记录**: 失败时记录详细的错误信息
4. **超时时间设置**: 合理设置任务超时时间
5. **异常处理**: 完善的异常捕获和处理机制

## 详细流程说明

### 安装任务详细流程

#### 1. 任务接收阶段
```
C0101消息 → TaskHandler.handleInstallTask() → 状态: TODO
```

#### 2. 预检查阶段
```kotlin
// 检查应用安装状态
val installStatus = checkAppInstallStatus(packageName, versionName, versionCode)
when (installStatus) {
    1 -> INSTALL_SUCCESS (已安装且版本匹配)
    2 -> 检查allowDowngrade参数
         false → UPDATE_DOWNGRADE_FORBIDDEN
         true → 继续流程
    0 → 继续流程 (未安装或版本较低)
}
```

#### 3. 下载阶段
```
开始下载 → DOWNLOAD_ING → 下载成功 → DOWNLOAD_SUCCESS
                      ↓
                  下载失败 → DOWNLOAD_FAILED
```

#### 4. 安装阶段
```
设备状态检查 → 立即安装/等待安装
立即安装: DOWNLOAD_SUCCESS → INSTALL_ING → INSTALL_SUCCESS/INSTALL_FAILED
等待安装: DOWNLOAD_SUCCESS → INSTALL_WAITING → (用户确认) → INSTALL_ING → INSTALL_SUCCESS/INSTALL_FAILED
```

### 卸载任务详细流程

#### 1. 任务接收
```
C0102消息 → TaskHandler.handleUninstallTask() → 状态: TODO
```

#### 2. 预检查
```kotlin
if (!AppManagerApi.isApplicationInstalled(packageName)) {
    // 应用不存在，视为卸载成功
    return UNINSTALL_SUCCESS
}
```

#### 3. 卸载执行
```
开始卸载 → UNINSTALL_ING → 卸载成功 → UNINSTALL_SUCCESS
                        ↓
                    卸载失败 → UNINSTALL_FAILED
```

### 更新任务详细流程

#### 1. 任务接收
```
C0103消息 → TaskHandler.handleUpdateTask() → 状态: TODO
```

#### 2. 版本检查
```kotlin
if (!needUpdate(task)) {
    // 版本已是最新
    return RESULT_SUCCESS
}
```

#### 3. 更新执行
```
开始下载 → DOWNLOAD_ING → DOWNLOAD_SUCCESS → UPDATE_ING → UPDATE_SUCCESS
                      ↓                                ↓
                  DOWNLOAD_FAILED                  UPDATE_FAILED
```

## 🚨 异常场景处理

### 网络断开场景
```mermaid
graph TD
    A[任务执行中] --> B[网络断开]
    B --> C[状态上报失败]
    C --> D[消息自动缓存]
    D --> E[网络恢复]
    E --> F[自动重发缓存消息]
    F --> G[状态同步完成]
```

### 超时检测场景
```mermaid
graph TD
    A[任务开始执行] --> B[记录lastUpdateTime]
    B --> C[定时检查 - 每10分钟]
    C --> D{超过20分钟?}
    D -->|否| C
    D -->|是| E[标记为对应失败状态]
    E --> F[上报失败状态]
    F --> G[任务结束]
```

### 服务重启场景
```mermaid
graph TD
    A[服务重启] --> B[调用resetDoingWSTaskState]
    B --> C[遍历所有任务]
    C --> D{任务已完成?}
    D -->|是| E[跳过]
    D -->|否| F{特殊任务?}
    F -->|自身更新INSTALL_ING| E
    F -->|OS更新UPDATE_ING| E
    F -->|否| G[重置为TODO]
    E --> H[继续下一个]
    G --> H
    H --> I{还有任务?}
    I -->|是| C
    I -->|否| J[重置完成]
```

## 状态统计和监控

### 状态分布统计
```kotlin
// 获取各状态的任务数量
fun getTaskStatusStatistics(): Map<String, Int> {
    val stats = mutableMapOf<String, Int>()
    val taskList = getLocalTaskList()

    for (i in 0 until taskList.length()) {
        val task = taskList.getJSONObject(i)
        val status = task.optString("taskResult", "unknown")
        stats[status] = stats.getOrDefault(status, 0) + 1
    }

    return stats
}
```

### 任务执行时长统计
```kotlin
// 计算任务执行时长
fun getTaskExecutionTime(taskId: String): Long {
    val task = getTaskById(taskId)
    val startTime = task?.optLong("startTime", 0L) ?: 0L
    val endTime = task?.optLong("lastUpdateTime", 0L) ?: 0L
    return if (endTime > startTime) endTime - startTime else 0L
}
```

## 故障排查指南

### 常见问题诊断

#### 1. 任务卡在DOWNLOAD_ING状态
**可能原因**:
- 网络连接问题
- 下载服务器响应慢
- 文件过大导致下载超时

**排查步骤**:
1. 检查网络连接状态
2. 查看下载进度日志
3. 检查是否触发超时机制（20分钟）
4. 验证下载URL是否有效

#### 2. 任务卡在INSTALL_ING状态
**可能原因**:
- 安装权限不足
- 存储空间不足
- APK文件损坏
- 系统安装服务异常

**排查步骤**:
1. 检查系统安装权限
2. 验证存储空间
3. 校验APK文件MD5
4. 查看系统安装日志

#### 3. 状态上报失败
**可能原因**:
- WebSocket连接断开
- 服务器响应异常
- 消息格式错误

**排查步骤**:
1. 检查WebSocket连接状态
2. 查看消息缓存机制是否工作
3. 验证消息格式是否正确
4. 检查服务器响应日志

### 调试工具

#### 1. 状态机日志
```kotlin
// 启用详细日志
Logger.setDebugMode(true)

// 查看状态变化日志
Logger.task("任务状态变化: $taskId -> $newState")
```

#### 2. 状态导出
```kotlin
// 导出当前所有任务状态
fun exportTaskStates(): String {
    val taskList = getLocalTaskList()
    return taskList.toString(2) // 格式化JSON
}
```

## 📈 性能优化建议

### 1. 状态更新优化
- 批量状态更新，减少I/O操作
- 使用内存缓存加速状态查询
- 异步状态持久化，避免阻塞主线程

### 2. 超时检测优化
- 合理设置检测间隔（当前10分钟）
- 只检测必要的状态，减少CPU消耗
- 使用增量检测，避免全量扫描

### 3. 缓存机制优化
- 限制缓存大小，防止内存溢出
- 定期清理过期缓存
- 压缩缓存数据，减少存储空间

---

*本文档基于service项目的TaskStateConstants.kt和相关状态管理代码生成*
*最后更新时间: 2025-08-07*
