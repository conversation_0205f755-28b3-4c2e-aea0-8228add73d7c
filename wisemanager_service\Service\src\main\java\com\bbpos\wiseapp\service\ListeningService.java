package com.bbpos.wiseapp.service;

import android.accessibilityservice.AccessibilityService;
import android.accessibilityservice.AccessibilityServiceInfo;
import android.content.ComponentName;
import android.content.Context;
import android.os.Build;
import android.provider.Settings;
import android.text.TextUtils;
import android.util.Log;
import android.view.accessibility.AccessibilityEvent;

import com.bbpos.wiseapp.logger.BBLog;
import com.bbpos.wiseapp.tms.utils.Constants;
import com.bbpos.wiseapp.websocket.WebSocketSender;

import java.util.HashSet;
import java.util.Set;
import java.util.Timer;
import java.util.TimerTask;

public class ListeningService extends AccessibilityService {
    private static final String TAG = "WindowChange";
    private static final int idle_interval = 300000;
    private static long lastTime = System.currentTimeMillis()-idle_interval;
    Timer mTimer = null;

    @Override
    protected void onServiceConnected() {
        super.onServiceConnected();
        BBLog.e(BBLog.TAG, "ListeningService onServiceConnected");
        AccessibilityServiceInfo config = new AccessibilityServiceInfo();
        //配置监听的事件类型为界面变化|点击事件
        config.eventTypes = AccessibilityEvent.TYPES_ALL_MASK;
        config.feedbackType = AccessibilityServiceInfo.FEEDBACK_GENERIC;
        if (Build.VERSION.SDK_INT >= 16) {
            config.flags = AccessibilityServiceInfo.FLAG_INCLUDE_NOT_IMPORTANT_VIEWS;
        }
        setServiceInfo(config);

        mTimer = new Timer();
        mTimer.schedule(new TimerTask() {
            @Override
            public void run() {
                if (isDeviceIdle(false)) {
                    if (Constants.IS_IN_USE) {
                        Constants.IS_IN_USE = false;
                        WebSocketSender.C0201_DeviceStatusUpload("", Constants.IS_IN_USE);
                    }
                }
            }
        }, 0, 1000);
    }

    @Override
    public void onAccessibilityEvent(AccessibilityEvent event) {
        if (event.getEventType() != AccessibilityEvent.TYPE_NOTIFICATION_STATE_CHANGED
         && event.getEventType() != AccessibilityEvent.TYPE_WINDOW_STATE_CHANGED
         && event.getEventType() != AccessibilityEvent.TYPE_WINDOW_CONTENT_CHANGED
//         && event.getEventType() != AccessibilityEvent.TYPE_VIEW_FOCUSED
//         && event.getEventType() != AccessibilityEvent.TYPE_VIEW_TEXT_SELECTION_CHANGED
        ) {
            BBLog.e(BBLog.TAG, "onAccessibilityEvent == " + event.getEventType());
            lastTime = System.currentTimeMillis();
            if (!Constants.IS_IN_USE) {
                Constants.IS_IN_USE = true;
                WebSocketSender.C0201_DeviceStatusUpload("", Constants.IS_IN_USE);
            }
        }
    }

    @Override
    public void onInterrupt() {

    }

    /**
     * 检测辅助功能是否开启
     *
     * @param mContext
     * @return boolean
     */
    public static boolean isAccessibilitySettingsOn(Context mContext, String serviceName, String serviceSimpleName) {
        int accessibilityEnabled = 0;
        // 对应的服务
        final String service = mContext.getPackageName() + "/" + serviceName;
        final String serviceSimple = mContext.getPackageName() + "/." + serviceSimpleName;
        //BBLog.i(TAG, "service:" + service);
        try {
            accessibilityEnabled = Settings.Secure.getInt(mContext.getApplicationContext().getContentResolver(), android.provider.Settings.Secure.ACCESSIBILITY_ENABLED);
            BBLog.v(TAG, "accessibilityEnabled = " + accessibilityEnabled);
        } catch (Exception e) {
            BBLog.e(TAG, "Error finding setting, default accessibility to not found: " + e.getMessage());
        }
        TextUtils.SimpleStringSplitter mStringColonSplitter = new TextUtils.SimpleStringSplitter(':');

        if (accessibilityEnabled == 1) {
            BBLog.v(TAG, "***ACCESSIBILITY IS ENABLED*** -----------------");
            String settingValue = Settings.Secure.getString(mContext.getApplicationContext().getContentResolver(), Settings.Secure.ENABLED_ACCESSIBILITY_SERVICES);
            if (settingValue != null) {
                mStringColonSplitter.setString(settingValue);
                while (mStringColonSplitter.hasNext()) {
                    String accessibilityService = mStringColonSplitter.next();
                    BBLog.v(TAG, "-------------- > accessibilityService :: " + accessibilityService + " " + service);
                    BBLog.v(TAG, "-------------- > accessibilityService :: " + accessibilityService + " " + serviceSimple);
                    if (accessibilityService.equalsIgnoreCase(service) || accessibilityService.equalsIgnoreCase(serviceSimple)) {
                        BBLog.v(TAG, "We've found the correct setting - accessibility is switched on!");
                        return true;
                    }
                }
            }
        } else {
            BBLog.v(TAG, "***ACCESSIBILITY IS DISABLED***");
        }
        return false;
    }

    public static boolean isDeviceIdle(boolean debug) {
        boolean idle = false;
        long nowTime = System.currentTimeMillis();
        if (debug) {
            BBLog.e(BBLog.TAG, "isDeviceIdle nowTime=" + nowTime + "   lastTime=" + lastTime + "   offset=" + (nowTime - lastTime));
        }
        if (nowTime-lastTime >= idle_interval) {
            idle = true;
        }
        return idle;
    }

    ////////////// Jim added for default enabled service in Accessibility
    public static final char ENABLED_ACCESSIBILITY_SERVICES_SEPARATOR = ':';
    final static TextUtils.SimpleStringSplitter sStringColonSplitter =
            new TextUtils.SimpleStringSplitter(ENABLED_ACCESSIBILITY_SERVICES_SEPARATOR);

    public static void updateAccessibility(Context context) {
        // Parse the enabled services.
        Set<ComponentName> enabledServices = getEnabledServicesFromSettings(context);
        if(null == enabledServices) {
            return;
        }
        // Determine enabled services and accessibility state.
        BBLog.e(BBLog.TAG, "updateAccessibility = " + context.getPackageName()+"/"+ListeningService.class.getName());
        ComponentName toggledService = ComponentName.unflattenFromString(context.getPackageName()+"/"+ListeningService.class.getName());
        final boolean accessibilityEnabled;
        // Enabling at least one service enables accessibility.
        accessibilityEnabled = true;
        enabledServices.add(toggledService);
        // Update the enabled services setting.
        StringBuilder enabledServicesBuilder = new StringBuilder();
        // Keep the enabled services even if they are not installed since we
        // have no way to know whether the application restore process has
        // completed. In general the system should be responsible for the
        // clean up not settings.
        for (ComponentName enabledService : enabledServices) {
            enabledServicesBuilder.append(enabledService.flattenToString());
            enabledServicesBuilder.append(ENABLED_ACCESSIBILITY_SERVICES_SEPARATOR);
        }
        final int enabledServicesBuilderLength = enabledServicesBuilder.length();
        if (enabledServicesBuilderLength > 0) {
            enabledServicesBuilder.deleteCharAt(enabledServicesBuilderLength - 1);
        }
        Settings.Secure.putString(context.getContentResolver(),
                Settings.Secure.ENABLED_ACCESSIBILITY_SERVICES,
                enabledServicesBuilder.toString());

        // Update accessibility enabled.
        Settings.Secure.putInt(context.getContentResolver(),
                Settings.Secure.ACCESSIBILITY_ENABLED, accessibilityEnabled ? 1 : 0);
    }


    public static Set<ComponentName> getEnabledServicesFromSettings(Context context) {
        String enabledServicesSetting = Settings.Secure.getString(context.getContentResolver(),
                Settings.Secure.ENABLED_ACCESSIBILITY_SERVICES);
        if (enabledServicesSetting == null) {
            enabledServicesSetting = "";
        }
        Set<ComponentName> enabledServices = new HashSet<ComponentName>();
        TextUtils.SimpleStringSplitter colonSplitter = sStringColonSplitter;
        colonSplitter.setString(enabledServicesSetting);
        while (colonSplitter.hasNext()) {
            String componentNameString = colonSplitter.next();
            ComponentName enabledService = ComponentName.unflattenFromString(
                    componentNameString);
            if (enabledService != null) {
                if(enabledService.flattenToString().equals(context.getPackageName()+"/"+ListeningService.class.getName())) {
                    return null;
                }
                enabledServices.add(enabledService);
            }
        }
        return enabledServices;
    }
}