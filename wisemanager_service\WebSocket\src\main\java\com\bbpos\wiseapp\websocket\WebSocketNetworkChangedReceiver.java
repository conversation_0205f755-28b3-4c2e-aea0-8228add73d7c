package com.bbpos.wiseapp.websocket;

import android.Manifest;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import androidx.annotation.RequiresPermission;
import android.util.Log;

/**
 * 监听网络变化广播，网络变化时自动重连
 */
public class WebSocketNetworkChangedReceiver extends BroadcastReceiver {
    private static WebsocketNetworkChangeListenner mListenner;
    public static boolean isNetworkConnected;

    public WebSocketNetworkChangedReceiver() {
    }

    @RequiresPermission(Manifest.permission.ACCESS_NETWORK_STATE)
    @Override
    public void onReceive(Context context, Intent intent) {
        if (ConnectivityManager.CONNECTIVITY_ACTION.equals(intent.getAction())) {
            ConnectivityManager manager = (ConnectivityManager) context
                    .getSystemService(Context.CONNECTIVITY_SERVICE);
            if (manager == null) return;
            NetworkInfo activeNetwork = manager.getActiveNetworkInfo();
            if (activeNetwork != null) {
                if (activeNetwork.isConnected()) {
                    isNetworkConnected = true;
                    if (activeNetwork.getType() == ConnectivityManager.TYPE_WIFI) {
                        Log.i(Constant.TAG, "网络连接发生变化，当前WiFi连接可用，正在尝试重连。");
                    } else if (activeNetwork.getType() == ConnectivityManager.TYPE_MOBILE) {
                        Log.i(Constant.TAG, "网络连接发生变化，当前移动连接可用，正在尝试重连。");
                    }

//                    if (WebSocketSetting.isReconnectWithNetworkChanged()) {
//                        if (mListenner != null) {
//                            mListenner.onNetworkConnected();
//                        }
//                    }
                } else {
                    isNetworkConnected = false;
                    Log.i(Constant.TAG, "当前没有可用网络");
                }
            }
        }
    }

    public interface WebsocketNetworkChangeListenner{
        void onNetworkConnected();
    }

    public static void setWebsocketNetworkChangeListenner(WebsocketNetworkChangeListenner listenner){
        mListenner = listenner;
    }

    public static WebsocketNetworkChangeListenner getmListenner() {
        return mListenner;
    }
}
