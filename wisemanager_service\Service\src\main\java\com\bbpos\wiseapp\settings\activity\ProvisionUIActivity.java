package com.bbpos.wiseapp.settings.activity;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.os.Handler;
import androidx.annotation.Nullable;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.TextView;
import android.widget.Toast;

import com.bbpos.wiseapp.logger.BBLog;
import com.bbpos.wiseapp.provisioning.ProgressUpdateListener;
import com.bbpos.wiseapp.provisioning.ProvisionService;
import com.bbpos.wiseapp.service.R;
import com.bbpos.wiseapp.system.api.ServiceApi;
import com.bumptech.glide.Glide;
import com.bumptech.glide.load.engine.DiskCacheStrategy;
import com.bumptech.glide.request.RequestOptions;

public class ProvisionUIActivity extends Activity implements ProgressUpdateListener {
    private LinearLayout ll_progress;
    private LinearLayout ll_progressbar;
    private TextView tv_count;
    private TextView tv_err;
    private TextView tv_exit_tip;
    private ImageView iv_progress;
    private ProgressBar pb_progress;
    private TextView mTvProg = null;
    private TextView mTvMax = null;

    private Context mContext;
    private String type;
    private Handler handler ;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.provisioning);
        handler = new Handler(getMainLooper());
        initComponent();
        mContext = getApplicationContext();
        type = getIntent().getStringExtra("type");
        BBLog.w(BBLog.TAG, "[ProvisionUIActivity] ======onCreate====== " + type);

        ProvisionService.setUpdateListener(this);
        Intent provisionService = new Intent(mContext, ProvisionService.class);
        provisionService.putExtra("type", type);
        ServiceApi.getIntance().startService(provisionService);

    }

    private void initComponent() {
        ll_progress = findViewById(R.id.ll_progress);
        ll_progress.setVisibility(View.VISIBLE);
        tv_count = findViewById(R.id.tv_count);
        tv_err = findViewById(R.id.tv_err);
        tv_exit_tip = findViewById(R.id.tv_exit_tip);
        iv_progress = ll_progress.findViewById(R.id.iv_progress);
        pb_progress = findViewById(R.id.pb_progress);
        ll_progressbar = findViewById(R.id.ll_progressbar);
        ll_progressbar.setVisibility(View.INVISIBLE);
        mTvProg = findViewById(R.id.tv_prog);
        mTvMax = findViewById(R.id.tv_max);

        RequestOptions options = new RequestOptions().diskCacheStrategy(DiskCacheStrategy.RESOURCE);
        Glide.with(ProvisionUIActivity.this).load(R.drawable.down_load).apply(options).into(iv_progress);
    }

    @Override
    public void onProgressUpdate(int cur, int max, String err, String errTip) {
        handler.postDelayed(new Runnable() {
            @Override
            public void run() {
                if (ll_progressbar.getVisibility() != View.VISIBLE)
                    ll_progressbar.setVisibility(View.VISIBLE);
                mTvProg.setText(cur + "");
                mTvMax.setText(max + "");
                pb_progress.setMax(max);
                pb_progress.setProgress(cur);
            }
        }, 500);
    }

    @Override
    public void onUpdateFail(String msg) {
        handler.postDelayed(new Runnable() {
            @Override
            public void run() {
                Toast.makeText(mContext, TextUtils.isEmpty(msg) ? getString(R.string.provisioning_fail_retry) : msg, Toast.LENGTH_LONG).show();
                tv_err.setText(TextUtils.isEmpty(msg) ? getString(R.string.provisioning_fail_retry) : msg);
                finish();
            }
        }, 500);
    }

    @Override
    public void onUpdateSuccess(String msg) {

    }

    @Override
    public void onUpdateCompleted(boolean success,String msg) {
        handler.postDelayed(new Runnable() {
            @Override
            public void run() {
                if (!success) {
                    Toast.makeText(mContext, TextUtils.isEmpty(msg) ? getString(R.string.provisioning_fail_retry) : msg, Toast.LENGTH_LONG).show();
                    tv_err.setText(TextUtils.isEmpty(msg) ? getString(R.string.provisioning_fail_retry) : msg);
                }else{
//                    Toast.makeText(mContext, TextUtils.isEmpty(msg) ? getString(R.string.provisioning_success) : msg, Toast.LENGTH_LONG).show();
//                    tv_err.setText(TextUtils.isEmpty(msg) ? getString(R.string.provisioning_success) : msg);
                }
                finish();
            }
        }, 5000);
    }

}


