package com.bbpos.wiseapp.utils;

import android.app.Dialog;
import android.bluetooth.BluetoothAdapter;
import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.Color;
import android.text.TextUtils;
import android.util.DisplayMetrics;
import android.view.View;
import android.view.View.OnClickListener;
import android.view.WindowManager;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.IdRes;

import com.bbpos.wiseapp.logger.BBLog;
import com.bbpos.wiseapp.service.R;
import com.bbpos.wiseapp.service.common.UsualData;
import com.bbpos.wiseapp.settings.utils.WirelessUtil;
import com.bbpos.wiseapp.system.api.DeviceInfoApi;
import com.bbpos.wiseapp.tms.utils.Constants;
import com.bbpos.wiseapp.tms.utils.ContextUtil;
import com.bbpos.wiseapp.tms.utils.ParameterName;
import com.google.zxing.BarcodeFormat;
import com.google.zxing.EncodeHintType;
import com.google.zxing.MultiFormatWriter;
import com.google.zxing.common.BitMatrix;
import com.google.zxing.qrcode.decoder.ErrorCorrectionLevel;

import org.json.JSONObject;

import java.util.EnumMap;
import java.util.Map;

public class QRCodeDialogUtils {
    private static Dialog dialog;
    /**
     * 初步本.
     *   由BT_MAC、bid、uid、S/N、WiFi mac、IMEI2 、MODEL等信息，组合生成一组二维码
     * @return
     */
    public static Dialog createQRCodeDialog() {
        if (TextUtils.isEmpty(Constants.BT_MAC)) {
            Constants.BT_MAC = WirelessUtil.getBluetoothMacAddress();
            if (!Constants.BT_STATUS) {
                BluetoothAdapter.getDefaultAdapter().disable();
            }
        }
        String bid = SharedPreferencesUtils.getSharePreferencesValue(UsualData.SHARED_PREFERENCES_DEVICE_FULL_INFO, "");
        String uid = "NULL";
        if (!TextUtils.isEmpty(bid)) {
            BBLog.e(BBLog.TAG, "WebSocketCenter: cache bid = " + bid);
            try {
                JSONObject infoData = new JSONObject(bid);
                if (infoData.has(ParameterName.bid)) {
                    bid = infoData.getString(ParameterName.bid);
                } else {
                    bid = "NULL";
                }

                if (infoData.has(ParameterName.uid)) {
                    uid = infoData.getString(ParameterName.uid);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        } else {
            bid = "NULL";
        }

        String QRcontent = "S/N:" + DeviceInfoApi.getIntance().getSerialNumber()
                + ",WIFI:" + WirelessUtil.getMac(ContextUtil.getInstance())
                + ",BT:" + Constants.BT_MAC
                + ",IMEI2:" + WirelessUtil.getIMEI(ContextUtil.getInstance());
        if (DeviceInfoApi.getIntance().isWisePosPro()) {
            QRcontent += ",MODEL:" + SystemUtils.get7MDModel(ContextUtil.getInstance());
        }
        QRcontent += ",bID:" + bid;
        QRcontent += ",uID:" + uid;
        BBLog.w(BBLog.TAG, "QRcontent = " + QRcontent);
        WindowManager windowManager = (WindowManager) ContextUtil.getInstance().getSystemService(Context.WINDOW_SERVICE);
        Dialog dialog = new Dialog(ContextUtil.getInstance(), R.style.dialog_style);
        dialog.setContentView(R.layout.qr_code);
        dialog.setTitle("");

        ImageView imgView = (ImageView) dialog.findViewById(R.id.qrCodeImg);
        Bitmap bitmap = createBitmapFromString(QRcontent);
        // 設定為 QR code 影像
        if (bitmap != null) {
            imgView.setImageBitmap(bitmap);
        }
        return  dialog;
    }

    public static Dialog showDialogByStep(StepEnum stepEnum) {
        Dialog dialog = getQRCodeDialog();
        String defaultValue = "Unknown";
        switch (stepEnum) {
            case LABEL_PRINTING_SN:
                ((TextView)getView(R.id.label)).setText("Label Printing");
                getView(R.id.full_info).setVisibility(View.GONE);
                getView(R.id.back).setVisibility(View.INVISIBLE);
                getView(R.id.divider).setVisibility(View.INVISIBLE);
                getView(R.id.qrCodeImg).setVisibility(View.VISIBLE);
                getView(R.id.sn).setVisibility(View.VISIBLE);

                String sn = (TextUtils.isEmpty(DeviceInfoApi.getIntance().getSerialNumber()) ? defaultValue : DeviceInfoApi.getIntance().getSerialNumber());
                ((TextView)getView(R.id.sn)).setText("SN: " + sn);
                Bitmap snBitmap = createBitmapFromString(sn);
                if (snBitmap != null) {
                    ((ImageView)getView(R.id.qrCodeImg)).setImageBitmap(snBitmap);
                }

                ((TextView)getView(R.id.next)).setText("Next");
                getView(R.id.next).setOnClickListener(new OnClickListener() {
                    @Override
                    public void onClick(final View v) {
                        showDialogByStep(StepEnum.LABEL_PRINTING_IMEI1);
                    }
                });
                break;
            case LABEL_PRINTING_IMEI1:
                ((TextView)getView(R.id.label)).setText("Label Printing");
                getView(R.id.full_info).setVisibility(View.GONE);
                getView(R.id.back).setVisibility(View.VISIBLE);
                getView(R.id.divider).setVisibility(View.VISIBLE);
                getView(R.id.qrCodeImg).setVisibility(View.VISIBLE);
                getView(R.id.sn).setVisibility(View.VISIBLE);

                String imei1 = (TextUtils.isEmpty(DeviceInfoApi.getIntance().getIMEI(ContextUtil.getInstance(),0)) ? defaultValue : DeviceInfoApi.getIntance().getIMEI(ContextUtil.getInstance(),0));
                ((TextView)getView(R.id.sn)).setText("IMEI 1: " + imei1);
                Bitmap imei1Bitmap = createBitmapFromString(imei1);
                if (imei1Bitmap != null) {
                    ((ImageView)getView(R.id.qrCodeImg)).setImageBitmap(imei1Bitmap);
                }

                getView(R.id.back).setOnClickListener(new OnClickListener() {
                    @Override
                    public void onClick(final View v) {
                        showDialogByStep(StepEnum.LABEL_PRINTING_SN);
                    }
                });

                getView(R.id.next).setOnClickListener(new OnClickListener() {
                    @Override
                    public void onClick(final View v) {
                        showDialogByStep(StepEnum.LABEL_PRINTING_IMEI2);
                    }
                });

                break;
            case LABEL_PRINTING_IMEI2:
                ((TextView)getView(R.id.label)).setText("Label Printing");
                getView(R.id.full_info).setVisibility(View.GONE);
                getView(R.id.back).setVisibility(View.VISIBLE);
                getView(R.id.divider).setVisibility(View.VISIBLE);
                getView(R.id.qrCodeImg).setVisibility(View.VISIBLE);
                getView(R.id.sn).setVisibility(View.VISIBLE);

                String imei2 = (TextUtils.isEmpty(DeviceInfoApi.getIntance().getIMEI(ContextUtil.getInstance(),1)) ? defaultValue : DeviceInfoApi.getIntance().getIMEI(ContextUtil.getInstance(),1));
                ((TextView)getView(R.id.sn)).setText("IMEI 2: " + imei2);

                Bitmap imei2Bitmap = createBitmapFromString(imei2);
                if (imei2Bitmap != null) {
                    ((ImageView)getView(R.id.qrCodeImg)).setImageBitmap(imei2Bitmap);
                }

                getView(R.id.back).setOnClickListener(new OnClickListener() {
                    @Override
                    public void onClick(final View v) {
                        showDialogByStep(StepEnum.LABEL_PRINTING_IMEI1);
                    }
                });

                getView(R.id.next).setOnClickListener(new OnClickListener() {
                    @Override
                    public void onClick(final View v) {
                        showDialogByStep(StepEnum.LABEL_PRINTING_FULL);
                    }
                });
                break;
            case LABEL_PRINTING_FULL:
                ((TextView)getView(R.id.label)).setText("Label Printing");
                getView(R.id.full_info).setVisibility(View.VISIBLE);
                getView(R.id.qrCodeImg).setVisibility(View.GONE);
                getView(R.id.back).setVisibility(View.VISIBLE);
                getView(R.id.divider).setVisibility(View.VISIBLE);
                getView(R.id.sn).setVisibility(View.GONE);

                ((TextView)getView(R.id.full_info_sn)).setText("SN: " + ((TextUtils.isEmpty(DeviceInfoApi.getIntance().getSerialNumber()) ? defaultValue : DeviceInfoApi.getIntance().getSerialNumber())));
                ((TextView)getView(R.id.full_info_imie1)).setText("IMEI 1: " + (TextUtils.isEmpty(DeviceInfoApi.getIntance().getIMEI(ContextUtil.getInstance(),0)) ? defaultValue : DeviceInfoApi.getIntance().getIMEI(ContextUtil.getInstance(),0)));
                ((TextView)getView(R.id.full_info_imei2)).setText("IMEI 2: " + (TextUtils.isEmpty(DeviceInfoApi.getIntance().getIMEI(ContextUtil.getInstance(),1)) ? defaultValue : DeviceInfoApi.getIntance().getIMEI(ContextUtil.getInstance(),1)));

                getView(R.id.back).setOnClickListener(new OnClickListener() {
                    @Override
                    public void onClick(final View v) {
                        showDialogByStep(StepEnum.LABEL_PRINTING_IMEI2);
                    }
                });

                getView(R.id.next).setOnClickListener(new OnClickListener() {
                    @Override
                    public void onClick(final View v) {
                        dialog.dismiss();
                    }
                });
                break;
            case LASER_MARKING_QRCODE:
                ((TextView)getView(R.id.label)).setText("Laser Marking");
                getView(R.id.full_info).setVisibility(View.GONE);
                getView(R.id.qrCodeImg).setVisibility(View.VISIBLE);
                getView(R.id.back).setVisibility(View.INVISIBLE);
                getView(R.id.divider).setVisibility(View.INVISIBLE);
                getView(R.id.sn).setVisibility(View.VISIBLE);

                ((TextView)getView(R.id.sn)).setText("SN: " + ((TextUtils.isEmpty(DeviceInfoApi.getIntance().getSerialNumber()) ? defaultValue : DeviceInfoApi.getIntance().getSerialNumber())));
                ((TextView)getView(R.id.next)).setText("Next");

                Bitmap markBitmap = createBitmapFromString(((TextUtils.isEmpty(DeviceInfoApi.getIntance().getSerialNumber()) ? defaultValue : DeviceInfoApi.getIntance().getSerialNumber())));
                if (markBitmap != null) {
                    ((ImageView)getView(R.id.qrCodeImg)).setImageBitmap(markBitmap);
                }

                getView(R.id.next).setOnClickListener(new OnClickListener() {
                    @Override
                    public void onClick(final View v) {
                        showDialogByStep(StepEnum.LASER_MARKING);
                    }
                });
                break;
            case LASER_MARKING:
                ((TextView)getView(R.id.label)).setText("Laser Marking");
                getView(R.id.full_info).setVisibility(View.GONE);
                getView(R.id.qrCodeImg).setVisibility(View.GONE);
                getView(R.id.back).setVisibility(View.INVISIBLE);
                getView(R.id.divider).setVisibility(View.INVISIBLE);
                getView(R.id.sn).setVisibility(View.VISIBLE);

                ((TextView)getView(R.id.sn)).setText("SN: " + ((TextUtils.isEmpty(DeviceInfoApi.getIntance().getSerialNumber()) ? defaultValue : DeviceInfoApi.getIntance().getSerialNumber())));
                ((TextView)getView(R.id.next)).setText("Confirm");

                getView(R.id.next).setOnClickListener(new OnClickListener() {
                    @Override
                    public void onClick(final View v) {
                        dialog.dismiss();
                    }
                });
                break;
        }
        return dialog;
    }

    private static View getView(@IdRes int id) {
        return dialog.findViewById(id);
    }

    private static Dialog getQRCodeDialog() {
        if (dialog == null) {
            dialog = new Dialog(ContextUtil.getInstance(), R.style.dialog_style);
            dialog.setContentView(R.layout.qr_code_label_printing_for_sn);
            dialog.setTitle("Label Printing");
        }
        return dialog;
    }

    private static DisplayMetrics getDisplayMetrics() {
        WindowManager windowManager = (WindowManager) ContextUtil.getInstance().getSystemService(Context.WINDOW_SERVICE);
        DisplayMetrics displaymetrics = new DisplayMetrics();
        windowManager.getDefaultDisplay().getMetrics(displaymetrics);
        return displaymetrics;
    }

    /**
     * 根据指定内容生成二维码
     * @param qrContent
     * @return
     */
    private static Bitmap createBitmapFromString(String qrContent){
        if (TextUtils.isEmpty(qrContent)) {
            return null;
        }
        Bitmap bitmap = null;
        DisplayMetrics displaymetrics = getDisplayMetrics();
        int QRCodeWidth = displaymetrics.widthPixels / 5 * 3;
        int QRCodeHeight = displaymetrics.widthPixels / 5 * 3;
        Map<EncodeHintType, Object> hints = new EnumMap<EncodeHintType, Object>(EncodeHintType.class);
        hints.put(EncodeHintType.CHARACTER_SET, "UTF-8");

        MultiFormatWriter writer = new MultiFormatWriter();
        try {
            // 容錯率姑且可以將它想像成解析度，分為 4 級：L(7%)，M(15%)，Q(25%)，H(30%)
            // 設定 QR code 容錯率為 H
            hints.put(EncodeHintType.ERROR_CORRECTION, ErrorCorrectionLevel.H);
            hints.put(EncodeHintType.MARGIN, 0);
            // 建立 QR code 的資料矩陣
            BitMatrix result = writer.encode(qrContent, BarcodeFormat.QR_CODE, QRCodeWidth, QRCodeHeight, hints);
            // ZXing 還可以生成其他形式條碼，如：BarcodeFormat.CODE_39、BarcodeFormat.CODE_93、BarcodeFormat.CODE_128、BarcodeFormat.EAN_8、BarcodeFormat.EAN_13...

            //建立點陣圖
            bitmap = Bitmap.createBitmap(QRCodeWidth, QRCodeHeight, Bitmap.Config.ARGB_8888);
            // 將 QR code 資料矩陣繪製到點陣圖上
            for (int y = 0; y < QRCodeHeight; y++) {
                for (int x = 0; x < QRCodeWidth; x++) {
                    bitmap.setPixel(x, y, result.get(x, y) ? Color.BLACK : Color.WHITE);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
        return bitmap;
    }


    public enum StepEnum {
        NONE(0),
        LABEL_PRINTING_SN(1),
        LABEL_PRINTING_IMEI1(2),
        LABEL_PRINTING_IMEI2(3),
        LABEL_PRINTING_FULL(4),
        LASER_MARKING_QRCODE(5),
        LASER_MARKING(6);

        private int mValue;
        StepEnum(int value ) {
            this.mValue = value;
        }

        public int value() {
            return this.mValue;
        }

        public static StepEnum valueOf(int value) {
            switch (value) {
                case 1:
                    return LABEL_PRINTING_SN;
                case 2:
                    return LABEL_PRINTING_IMEI1;
                case 3:
                    return LABEL_PRINTING_IMEI2;
                case 4:
                    return LABEL_PRINTING_FULL;
                case 5:
                    return LASER_MARKING;
                default:
                    return NONE;
            }
        }
    }
}
